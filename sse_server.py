from flask import Flask, Response, render_template, request
import json
import time

app = Flask(__name__)

def parse_msg_file():
    """解析msg_new.txt文件，返回事件列表"""
    import os
    events = []

    # 打印当前工作目录和文件是否存在
    print(f"Current working directory: {os.getcwd()}")
    print(f"File exists: {os.path.exists('msg_new.txt')}")

    try:
        with open('msg_new.txt', 'r') as f:
            content = f.read()

        # 跳过第一行注释
        if content.startswith('// loop'):
            content = content[content.find('{'): ]

        # 分割成单独的JSON对象
        json_objects = []
        depth = 0
        start = 0

        for i, char in enumerate(content):
            if char == '{':
                if depth == 0:
                    start = i
                depth += 1
            elif char == '}':
                depth -= 1
                if depth == 0:
                    json_str = content[start:i+1].strip()
                    if json_str:
                        json_objects.append(json_str)

        # 解析每个JSON对象
        for json_str in json_objects:
            try:
                event = json.loads(json_str)
                events.append(event)
                print(f"Parsed event: {event['event']}")
            except json.JSONDecodeError as e:
                print(f"Error parsing JSON: {e}\n{json_str}")

    except Exception as e:
        print(f"Error reading file: {e}")

    print(f"Total events parsed: {len(events)}")
    return events

def process_events(events):
    """处理事件，确保每个plan都有不同的loop_id"""
    processed_events = []
    current_loop_id = 1
    plan_count = 0

    for event in events:
        # 如果是plan事件，增加loop_id
        if event['event'] == 'plan':
            plan_count += 1
            current_loop_id = plan_count

        # 更新事件的loop_id
        if 'loop_id' in event:
            event['loop_id'] = current_loop_id

        processed_events.append(event)

    return processed_events

@app.route('/')
def index():
    """返回一个简单的HTML页面，用于测试SSE"""
    return render_template('index.html')

@app.route('/run-browser-task', methods=['POST'])
def stream():
    """SSE流，返回msg_new.txt中的数据"""
    # 获取POST请求的数据（如果有的话）
    request_data = request.json if request.is_json else {}
    print(f"Received POST request with data: {request_data}")

    def generate():
        events = parse_msg_file()

        if not events:
            print("No events found in the file!")
            yield "data: {\"error\": \"No events found in the file\"}\n\n"
            return

        # 处理事件，确保每个plan都有不同的loop_id
        processed_events = process_events(events)
        print(f"Processed {len(processed_events)} events with unique loop_ids for plans")

        for event in processed_events:
            # 将事件转换为SSE格式
            event_data = json.dumps(event)
            print(f"Sending event: {event['event']} with loop_id: {event.get('loop_id', 'N/A')}")
            yield f"data: {event_data}\n\n"
            time.sleep(0.5)  # 添加延迟，模拟真实场景

        # 发送结束标记
        print("Sending [DONE] event")
        yield "data: [DONE]\n\n"

    return Response(generate(), mimetype='text/event-stream')

# 添加一个路由来显示文件内容
@app.route('/file')
def show_file():
    import os
    try:
        with open('msg_new.txt', 'r') as f:
            content = f.read()
        return Response(content, mimetype='text/plain')
    except Exception as e:
        return f"Error reading file: {e}"

if __name__ == '__main__':
    print("Starting server...")
    print("Visit http://localhost:5000/ for the web interface")
    print("Use POST request to http://localhost:5000/stream for the SSE stream")
    print("Visit http://localhost:5000/file to view the raw file content")
    app.run(debug=True, host='0.0.0.0', port=5000)
