CREATE DATABASE IF NOT EXISTS `secwalk`;

USE `secwalk`;

-- secwalk.agents definition

CREATE TABLE IF NOT EXISTS `agents` (
  `id` varchar(255) COLLATE utf8mb4_bin NOT NULL,
  `type` bigint NOT NULL DEFAULT '0',
  `version` varchar(255) COLLATE utf8mb4_bin NOT NULL,
  `eng_version` varchar(255) COLLATE utf8mb4_bin NOT NULL,
  `config` longtext COLLATE utf8mb4_bin NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

-- secwalk.toolkits definition

CREATE TABLE IF NOT EXISTS `toolkits` (
  `id` varchar(255) COLLATE utf8mb4_bin NOT NULL,
  `type` varchar(255) COLLATE utf8mb4_bin NOT NULL DEFAULT 'custom',
  `tools` json NOT NULL,
  `config` longtext COLLATE utf8mb4_bin NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

-- secwalk.signs definition

CREATE TABLE IF NOT EXISTS `signs` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_bin NOT NULL,
  `secret` varchar(255) COLLATE utf8mb4_bin NOT NULL,
  `apikey` varchar(255) COLLATE utf8mb4_bin NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`),
  UNIQUE KEY `secret` (`secret`),
  UNIQUE KEY `apikey` (`apikey`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;