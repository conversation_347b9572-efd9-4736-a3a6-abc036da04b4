INSERT INTO `secwalk`.`toolkits` (`id`, `type`, `tools`, `config`) VALUES ('75a3e7eb-2133-992c-9a03-1a2d48d8e284', 'system', '[\"4432fa25-a348-a709-ebec-1822e0666b76\", \"76c6f57a-dd1d-9b17-dc26-6b69559e1882\", \"5525f862-0d49-4e65-989b-1e157f18889d\", \"09d00dd1-902c-40ea-94ee-f61189d616dd\"]', '{\"id\":\"75a3e7eb-2133-992c-9a03-1a2d48d8e284\",\"name\":\"文档生成工具集\",\"description\":\"文档格式转换生成。\",\"type\":\"system\",\"tools\":[{\"id\":\"4432fa25-a348-a709-ebec-1822e0666b76\",\"name\":\"文档生成\",\"description\":\"支持：markdown转docx、html、pdf\",\"input_parameters\":[{\"name\":\"Content-Type\",\"type\":\"string\",\"required\":true,\"configed\":true,\"default_value\":\"application/x-www-form-urlencoded\",\"location\":\"header\"},{\"name\":\"token\",\"type\":\"string\",\"required\":true,\"configed\":true,\"default_value\":\"b0b1b70d79d64dcaab401e7a1b4431ee\",\"location\":\"header\"},{\"name\":\"text\",\"description\":\"markdown文本\",\"type\":\"string\",\"required\":true,\"location\":\"query\"},{\"name\":\"target\",\"description\":\"生成文档的格式 docx/html/pdf\",\"type\":\"string\",\"required\":true,\"location\":\"query\"}],\"input_example\":\"{\\\"text\\\":\\\"# 标题1 ## 标题2 今天天气真好  这是代码 ``` print(\\\\\\\"hello world\\\\\\\") ```  表格数据如下：  | 232  | 432  | 535  | | ---- | ---- | ---- | | 5353 | 535  | 5353 | | 535  | 3535 | 53   | | 535  | 53   | 5353 |\\\",\\\"target\\\":\\\"docx\\\"}\",\"output_parameters\":[{\"name\":\"file\",\"type\":\"file\"}],\"method\":\"POST\",\"url\":\"http://hn-aio-plugin/api/v1/open/ti/conversion/markdown\",\"status\":1,\"display\":\"file\"},{\"id\":\"76c6f57a-dd1d-9b17-dc26-6b69559e1882\",\"name\":\"表格生成\",\"description\":\"支持：csv、json格式的文本转xlsx文件\",\"input_parameters\":[{\"name\":\"Content-Type\",\"type\":\"string\",\"required\":true,\"configed\":true,\"default_value\":\"application/x-www-form-urlencoded\",\"location\":\"header\"},{\"name\":\"token\",\"type\":\"string\",\"required\":true,\"configed\":true,\"default_value\":\"b0b1b70d79d64dcaab401e7a1b4431ee\",\"location\":\"header\"},{\"name\":\"text\",\"description\":\"csv/json格式的文本\",\"type\":\"string\",\"required\":true,\"location\":\"query\"}],\"input_example\":\"{\\\"text\\\":\\\"AccessKey ID,AccessKey Secret\\\"}\",\"output_parameters\":[{\"name\":\"file\",\"type\":\"file\"}],\"method\":\"POST\",\"url\":\"http://hn-aio-plugin\",\"path\":\"/api/v1/open/ti/conversion/excel\",\"status\":1,\"display\":\"file\"},{\"id\":\"5525f862-0d49-4e65-989b-1e157f18889d\",\"name\":\"文档生成-超长内容\",\"description\":\"文档生成-超长内容\",\"input_parameters\":[{\"name\":\"Content-type\",\"type\":\"string\",\"required\":true,\"configed\":true,\"default_value\":\"application/json\",\"location\":\"header\"},{\"name\":\"text\",\"description\":\"markdown文本\",\"type\":\"string\",\"required\":true,\"location\":\"body\"},{\"name\":\"target\",\"description\":\"生成文档的格式 docx/html/pdf\",\"type\":\"string\",\"required\":true,\"location\":\"body\"}],\"input_example\":\"{\\\"text\\\":\\\"# 标题\\\",\\\"target\\\":\\\"docx\\\"}\",\"output_parameters\":[{\"name\":\"file\",\"type\":\"file\"}],\"method\":\"POST\",\"url\":\"http://hn-aio-plugin/api/v1/open/ti/conversion/markdown\",\"status\":1,\"display\":\"file\"},{\"id\":\"09d00dd1-902c-40ea-94ee-f61189d616dd\",\"name\":\"表格生成-超长内容\",\"description\":\"表格生成-超长内容\",\"input_parameters\":[{\"name\":\"Content-type\",\"type\":\"string\",\"required\":true,\"configed\":true,\"default_value\":\"application/json\",\"location\":\"header\"},{\"name\":\"text\",\"type\":\"string\",\"required\":true,\"location\":\"body\"}],\"input_example\":\"{\\\"text\\\":\\\"aaa,bbb,ccc\\\"}\",\"output_parameters\":[{\"name\":\"file\",\"type\":\"file\"}],\"method\":\"POST\",\"url\":\"http://hn-aio-plugin/api/v1/open/ti/conversion/excel\",\"status\":1,\"display\":\"file\"}]}') ON DUPLICATE KEY UPDATE`type` = VALUES(`type`), `tools` = VALUES(`tools`), `config` = VALUES(`config`);