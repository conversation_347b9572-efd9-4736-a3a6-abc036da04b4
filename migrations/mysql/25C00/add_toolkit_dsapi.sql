INSERT INTO `secwalk`.`toolkits` (`id`, `type`, `tools`, `config`) VALUES ('be9fa4a1-4332-adc6-9956-ab24547341d0', 'system', '[\"6116e2d1-2489-ec8e-e32d-34da2edf7aa4\", \"9981e18a-14d5-0a11-47ad-3bdb4d320ee2\"]', '{\"id\":\"be9fa4a1-4332-adc6-9956-ab24547341d0\",\"name\":\"DeepSeek-API\",\"description\":\"DeepSeek-API\",\"type\":\"system\",\"url\":\"https://api.deepseek.com\",\"tools\":[{\"id\":\"6116e2d1-2489-ec8e-e32d-34da2edf7aa4\",\"name\":\"DeepSeek-R1\",\"description\":\"调用DeepSeek-R1\",\"input_parameters\":[{\"name\":\"Content-Type\",\"type\":\"string\",\"required\":true,\"configed\":true,\"default_value\":\"application/json\",\"location\":\"header\"},{\"name\":\"Authorization\",\"description\":\"API Key\",\"type\":\"string\",\"required\":true,\"location\":\"header\"},{\"name\":\"model\",\"description\":\"选择模型\",\"type\":\"string\",\"hidden\":true,\"default_value\":\"deepseek-reasoner\",\"location\":\"body\"},{\"name\":\"messages\",\"description\":\"消息列表\",\"type\":\"array\",\"required\":true,\"location\":\"body\"},{\"name\":\"stream\",\"description\":\"是否流式\",\"type\":\"boolean\",\"hidden\":true,\"default_value\":false,\"location\":\"body\"}],\"input_example\":\"{\\\"Authorization\\\":\\\"Bearer sk-464baf00672440dd9bd9e23c420bc2d5\\\",\\\"model\\\":\\\"deepseek-reasoner\\\",\\\"messages\\\":[{\\\"role\\\":\\\"system\\\",\\\"content\\\":\\\"You are a helpful assistant.\\\"},{\\\"role\\\":\\\"user\\\",\\\"content\\\":\\\"Hello!\\\"}],\\\"stream\\\":false}\",\"output_parameters\":[{\"name\":\"choices\",\"type\":\"array\",\"schema\":[{\"type\":\"object\",\"schema\":[{\"name\":\"index\",\"type\":\"integer\",\"required\":true},{\"name\":\"message\",\"type\":\"object\",\"schema\":[{\"name\":\"role\",\"type\":\"string\",\"required\":true},{\"name\":\"content\",\"type\":\"string\",\"required\":true},{\"name\":\"reasoning_content\",\"type\":\"string\",\"required\":true}],\"required\":true}],\"required\":true}],\"required\":true}],\"output_example\":\"{\\\"choices\\\":[{\\\"index\\\":0,\\\"message\\\":{\\\"role\\\":\\\"assistant\\\",\\\"content\\\":\\\"Hello! How can I assist you today? Whether you have a question, need help with something, or just want to chat, I\'m here to help! 😊\\\",\\\"reasoning_content\\\":\\\"Okay, the user started with \\\\\\\"Hello!\\\\\\\" so they\'re probably just testing or starting a conversation. I should respond warmly and let them know I\'m here to help. Maybe ask how I can assist them today. Keep it friendly and open-ended to encourage them to ask whatever they need.\\\"},\\\"finish_reason\\\":\\\"stop\\\"}]}\",\"method\":\"POST\",\"path\":\"/chat/completions\",\"status\":1},{\"id\":\"9981e18a-14d5-0a11-47ad-3bdb4d320ee2\",\"name\":\"DeepSeek-V3\",\"description\":\"调用DeepSeek-V3\",\"input_parameters\":[{\"name\":\"Content-Type\",\"type\":\"string\",\"required\":true,\"configed\":true,\"default_value\":\"application/json\",\"location\":\"header\"},{\"name\":\"Authorization\",\"description\":\"API Key\",\"type\":\"string\",\"required\":true,\"location\":\"header\"},{\"name\":\"model\",\"description\":\"选择模型\",\"type\":\"string\",\"required\":true,\"hidden\":true,\"default_value\":\"deepseek-chat\",\"location\":\"body\"},{\"name\":\"messages\",\"description\":\"消息列表\",\"type\":\"array\",\"required\":true,\"location\":\"body\"},{\"name\":\"stream\",\"description\":\"是否流式\",\"type\":\"boolean\",\"required\":true,\"hidden\":true,\"default_value\":false,\"location\":\"body\"}],\"input_example\":\"{\\\"Authorization\\\":\\\"Bearer sk-464baf00672440dd9bd9e23c420bc2d5\\\",\\\"model\\\":\\\"deepseek-chat\\\",\\\"messages\\\":[{\\\"role\\\":\\\"system\\\",\\\"content\\\":\\\"You are a helpful assistant.\\\"},{\\\"role\\\":\\\"user\\\",\\\"content\\\":\\\"Hello!\\\"}],\\\"stream\\\":false}\",\"output_parameters\":[{\"name\":\"choices\",\"type\":\"array\",\"schema\":[{\"type\":\"object\",\"schema\":[{\"name\":\"index\",\"type\":\"integer\",\"required\":true},{\"name\":\"message\",\"type\":\"object\",\"schema\":[{\"name\":\"role\",\"type\":\"string\",\"required\":true},{\"name\":\"content\",\"type\":\"string\",\"required\":true}],\"required\":true}],\"required\":true}],\"required\":true}],\"output_example\":\"{\\\"choices\\\":[{\\\"index\\\":0,\\\"message\\\":{\\\"role\\\":\\\"assistant\\\",\\\"content\\\":\\\"Hello! How can I assist you today? 😊\\\"},\\\"finish_reason\\\":\\\"stop\\\"}]}\",\"method\":\"POST\",\"path\":\"/chat/completions\",\"status\":1}]}');