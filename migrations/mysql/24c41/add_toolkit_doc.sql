INSERT INTO `secwalk`.`toolkits` (`id`, `type`, `tools`, `config`) VALUES ('b719ff1f-2b4e-de95-b015-dc5cd077501f', 'system', '[\"8342e6cb-9371-75fb-6394-728b37cf498b\", \"018826c1-610a-3545-79de-3fe32b82a95b\"]', '{\"id\":\"b719ff1f-2b4e-de95-b015-dc5cd077501f\",\"name\":\"文档解析工具集\",\"description\":\"用于提取各种文档的内容。\",\"type\":\"custom\",\"url\":\"http://server-openapi:9996\",\"tools\":[{\"id\":\"8342e6cb-9371-75fb-6394-728b37cf498b\",\"name\":\"文档解析任务创建\",\"description\":\"创建一个文档解析任务，提交需要文档解析的文件。\",\"input_parameters\":[{\"name\":\"Content-Type\",\"type\":\"string\",\"required\":true,\"configed\":true,\"default_value\":\"multipart/form-data\",\"location\":\"header\"},{\"name\":\"hn-token\",\"type\":\"string\",\"required\":true,\"configed\":true,\"default_value\":\"9e0cbb2262705bc2965926ae6f57699b\",\"location\":\"header\"},{\"name\":\"file\",\"description\":\"文件\",\"type\":\"file\",\"required\":true,\"location\":\"body\"}],\"input_example\":\"{\\\"file\\\":\\\"file:知识检索与创作-开放服务接口.pdf\\u003c44c7f834-4412-45cf-8085-a2ab5af143ea\\u003e\\\"}\",\"output_parameters\":[{\"name\":\"msg\",\"type\":\"string\",\"required\":true},{\"name\":\"code\",\"type\":\"integer\",\"required\":true},{\"name\":\"flag\",\"type\":\"integer\",\"required\":true},{\"name\":\"data\",\"type\":\"object\",\"schema\":[{\"name\":\"taskId\",\"type\":\"string\",\"required\":true}],\"required\":true}],\"output_example\":\"{\\\"msg\\\":\\\"成功\\\",\\\"code\\\":null,\\\"flag\\\":null,\\\"data\\\":{\\\"taskId\\\":\\\"5650fd31d8094818bcc753242e3ac895\\\"}}\",\"method\":\"POST\",\"path\":\"/inner/open/api/v2/document/plugin/analysis/submit\",\"status\":1},{\"id\":\"018826c1-610a-3545-79de-3fe32b82a95b\",\"name\":\"文档解析内容获取\",\"description\":\"获取文档解析的文本内容。\",\"input_parameters\":[{\"name\":\"Content-Type\",\"type\":\"string\",\"required\":true,\"configed\":true,\"default_value\":\"application/json\",\"location\":\"header\"},{\"name\":\"hn-token\",\"type\":\"string\",\"required\":true,\"configed\":true,\"default_value\":\"9e0cbb2262705bc2965926ae6f57699b\",\"location\":\"header\"},{\"name\":\"taskId\",\"description\":\"任务ID\",\"type\":\"string\",\"required\":true,\"location\":\"body\"}],\"input_example\":\"{\\\"taskId\\\":\\\"4c0292afeec847caba1e78636e16ecda\\\"}\",\"output_parameters\":[{\"name\":\"data\",\"type\":\"object\",\"schema\":[{\"name\":\"msg\",\"description\":\"信息描述\",\"type\":\"string\",\"required\":true},{\"name\":\"code\",\"description\":\"-1 失败，0 解析中， 1 成功\",\"type\":\"integer\",\"required\":true},{\"name\":\"data\",\"description\":\"解析结果\",\"type\":\"string\",\"required\":true}],\"required\":true}],\"output_example\":\"{\\\"data\\\":{\\\"msg\\\":\\\"analysis success！\\\",\\\"code\\\":1,\\\"data\\\":\\\"##表格[\\\\\\\"1.mac(!\\\\\\\\\\\\\\\")app\\\\\\\"]\\\\n !\\\\\\\" \\\\n##表格[\\\\\\\"2.windows\\\\\\\"]\\\\n3./\\\\n##表格[\\\\\\\"“\\\\\\\"]\\\\n##表格[\\\\\\\"”\\\\\\\"]\\\\n##表格[\\\\\\\"24\\\\\\\"]\\\\n\\\\n\\\"}}\",\"method\":\"POST\",\"path\":\"/inner/open/api/v2/document/plugin/analysis/status\",\"status\":1}]}');