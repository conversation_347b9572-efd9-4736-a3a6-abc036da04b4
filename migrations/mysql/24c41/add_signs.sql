-- secwalk.signs definition

CREATE TABLE IF NOT EXISTS `signs` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_bin NOT NULL,
  `secret` varchar(255) COLLATE utf8mb4_bin NOT NULL,
  `apikey` varchar(255) COLLATE utf8mb4_bin NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`),
  UNIQUE KEY `secret` (`secret`),
  UNIQUE KEY `apikey` (`apikey`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

INSERT INTO `secwalk`.`signs` (name, secret, apikey) VALUES('coder', 'secark-2bvyffqo85xbeasazc17btdvqae7ifqe', 'apikey-rghs5piuwkdqouuu8bv5c54azrgotbil');