INSERT INTO `secwalk`.`toolkits` (`id`, `type`, `tools`, `config`) VALUES ('a946e035-c201-334d-8f46-f761616ff1c6', 'system', '[\"33f73e36-6bf6-c151-05e9-8acb080aec5f\", \"7ac15ff1-885d-31f1-2069-593c81652473\", \"bf193751-949b-0e6b-83b3-c202f1d57442\", \"ac24dd58-cb10-9c3e-3734-e751f840b24b\"]', '{\"id\":\"a946e035-c201-334d-8f46-f761616ff1c6\",\"name\":\"图表生成工具集\",\"description\":\"支持生成雷达图、饼状图、散点图、折线图、柱状图等图表。\",\"type\":\"system\",\"tools\":[{\"id\":\"33f73e36-6bf6-c151-05e9-8acb080aec5f\",\"name\":\"雷达图生成\",\"description\":\"基于数据生成雷达图。\",\"input_parameters\":[{\"name\":\"Content-Type\",\"type\":\"string\",\"required\":true,\"configed\":true,\"default_value\":\"application/json\",\"location\":\"header\"},{\"name\":\"token\",\"type\":\"string\",\"required\":true,\"configed\":true,\"default_value\":\"b0b1b70d79d64dcaab401e7a1b4431ee\",\"location\":\"header\"},{\"name\":\"indicators\",\"description\":\"雷达图指示器\",\"type\":\"array\",\"schema\":[{\"type\":\"object\",\"schema\":[{\"name\":\"name\",\"description\":\"指示器分支名称,示例值(分支1)\",\"type\":\"string\",\"required\":true},{\"name\":\"maxValue\",\"description\":\"指示器的最大值，可选，建议设置,示例值(100)\",\"type\":\"integer\",\"required\":false},{\"name\":\"minValue\",\"description\":\"指示器的最小值，可选，默认为 0。,示例值(50)\",\"type\":\"integer\",\"required\":false}],\"required\":true}],\"required\":true,\"location\":\"body\"},{\"name\":\"data\",\"description\":\"数据项数组\",\"type\":\"array\",\"schema\":[{\"type\":\"object\",\"schema\":[{\"name\":\"seriesName\",\"description\":\"标签项,示例值(数据1)\",\"type\":\"string\",\"required\":false},{\"name\":\"data\",\"description\":\"数据项，一维数组，数组元素为数值，数组长度需要与指示器数量一致。,示例值([ 60, 80, 76, 53 ])\",\"type\":\"array\",\"schema\":[{\"type\":\"integer\",\"required\":true}],\"required\":true},{\"name\":\"color\",\"description\":\"线条颜色,示例值(#ff0000)\",\"type\":\"string\",\"required\":false}],\"required\":true}],\"required\":true,\"location\":\"body\"},{\"name\":\"title\",\"description\":\"图表标题,示例值(测试雷达图)\",\"type\":\"string\",\"required\":false,\"location\":\"body\"}],\"input_example\":\"{\\\"indicators\\\":[{\\\"name\\\":\\\"分支1\\\",\\\"maxValue\\\":100,\\\"minValue\\\":50},{\\\"name\\\":\\\"分支2\\\",\\\"maxValue\\\":100,\\\"minValue\\\":50},{\\\"name\\\":\\\"分支3\\\",\\\"maxValue\\\":100,\\\"minValue\\\":50},{\\\"name\\\":\\\"分支4\\\",\\\"maxValue\\\":100,\\\"minValue\\\":50}],\\\"data\\\":[{\\\"seriesName\\\":\\\"数据1\\\",\\\"data\\\":[60,80,76,53],\\\"color\\\":\\\"#9AA2F8\\\"},{\\\"seriesName\\\":\\\"数据2\\\",\\\"data\\\":[81,72,68,64],\\\"color\\\":\\\"#000000\\\"}]}\",\"output_parameters\":[{\"name\":\"file\",\"description\":\"生成的图表文件\",\"type\":\"file\"}],\"method\":\"POST\",\"url\":\"http://aio-plugin\",\"path\":\"/api/v1/open/ti/chart/radar\",\"status\":1,\"display\":\"image\"},{\"id\":\"7ac15ff1-885d-31f1-2069-593c81652473\",\"name\":\"饼状图生成\",\"description\":\"基于数据生成饼状图。\",\"input_parameters\":[{\"name\":\"Content-Type\",\"type\":\"string\",\"required\":true,\"configed\":true,\"default_value\":\"application/json\",\"location\":\"header\"},{\"name\":\"token\",\"type\":\"string\",\"required\":true,\"configed\":true,\"default_value\":\"b0b1b70d79d64dcaab401e7a1b4431ee\",\"location\":\"header\"},{\"name\":\"title\",\"description\":\"饼图标题,示例值(饼图标题)\",\"type\":\"string\",\"required\":false,\"location\":\"body\"},{\"name\":\"absolute\",\"description\":\"是否展示绝对值:是否在饼图上展示绝对值,默认展示比例,示例值(true)\",\"type\":\"boolean\",\"required\":false,\"location\":\"body\"},{\"name\":\"composition\",\"description\":\"数值项:一维数组，数组元素为数值，用于饼图计算每一项比例的数值列表，每项是个数值,示例值([ 52, 48 ])\",\"type\":\"array\",\"schema\":[{\"type\":\"integer\",\"required\":true}],\"required\":true,\"location\":\"body\"},{\"name\":\"colors\",\"description\":\"颜色项:一维数组，用于设置饼图每一项的颜色列表, 支持RGB 16进制颜色值, 如: #ff0000,示例值([ \\\"#ff0000\\\", \\\"#00ff00\\\" ])\",\"type\":\"array\",\"schema\":[{\"type\":\"string\",\"required\":true}],\"required\":false,\"location\":\"body\"},{\"name\":\"labels\",\"description\":\"标签项:一维数组，用于饼图每一项展示的标签名称列表,示例值([ \\\"男\\\", \\\"女\\\" ])\",\"type\":\"array\",\"schema\":[{\"type\":\"string\",\"required\":true}],\"required\":true,\"location\":\"body\"}],\"input_example\":\"{\\\"title\\\":\\\"饼图标题\\\",\\\"absolute\\\":false,\\\"composition\\\":[52,48],\\\"colors\\\":[\\\"#ff0000\\\",\\\"#00ff00\\\"],\\\"labels\\\":[\\\"男\\\",\\\"女\\\"]}\",\"output_parameters\":[{\"name\":\"file\",\"description\":\"生成的图表文件\",\"type\":\"file\"}],\"method\":\"POST\",\"url\":\"http://aio-plugin\",\"path\":\"/api/v1/open/ti/chart/pie\",\"status\":1,\"display\":\"image\"},{\"id\":\"bf193751-949b-0e6b-83b3-c202f1d57442\",\"name\":\"折线图生成\",\"description\":\"基于数据生成折线图。\",\"input_parameters\":[{\"name\":\"Content-Type\",\"type\":\"string\",\"required\":true,\"configed\":true,\"default_value\":\"application/json\",\"location\":\"header\"},{\"name\":\"token\",\"type\":\"string\",\"required\":true,\"configed\":true,\"default_value\":\"b0b1b70d79d64dcaab401e7a1b4431ee\",\"location\":\"header\"},{\"name\":\"title\",\"description\":\"图表标题,示例值(测试图表)\",\"type\":\"string\",\"required\":false,\"location\":\"body\"},{\"name\":\"labelX\",\"description\":\"横坐标名称,示例值(横坐标)\",\"type\":\"string\",\"required\":false,\"location\":\"body\"},{\"name\":\"labelY\",\"description\":\"纵坐标名称,示例值(纵坐标)\",\"type\":\"string\",\"required\":false,\"location\":\"body\"},{\"name\":\"smooth\",\"description\":\"是否平滑曲线,示例值(true)\",\"type\":\"boolean\",\"required\":false,\"location\":\"body\"},{\"name\":\"x\",\"description\":\"x坐标，一维数组，用于显示在横坐标上的坐标刻度,示例值([ \\\"2021-01-01\\\", \\\"2021-01-02\\\", \\\"2021-01-03\\\" ])\",\"type\":\"array\",\"schema\":[{\"type\":\"string\",\"required\":true}],\"required\":true,\"location\":\"body\"},{\"name\":\"y\",\"description\":\"y坐标数据，二维数组，数组元素为数值，数组每一行对应一条折线，数组的列数必须与x长度一致,示例值([ [ 1, 2, 3 ], [ 4, 5, 6 ] ])\",\"type\":\"array\",\"schema\":[{\"type\":\"array\",\"schema\":[{\"type\":\"integer\",\"required\":true}],\"required\":true}],\"required\":true,\"location\":\"body\"},{\"name\":\"labels\",\"description\":\"标签项，每个标签对应一条折线,示例值([ \\\"标签1\\\", \\\"标签2\\\" ])\",\"type\":\"array\",\"schema\":[{\"type\":\"string\",\"required\":true}],\"required\":false,\"location\":\"body\"}],\"input_example\":\"{\\\"title\\\":\\\"test\\\",\\\"labelX\\\":\\\"横坐标\\\",\\\"labelY\\\":\\\"纵坐标\\\",\\\"smooth\\\":false,\\\"x\\\":[\\\"a\\\",\\\"b\\\",\\\"c\\\",\\\"h\\\",\\\"e\\\"],\\\"y\\\":[[12,3,7,6,3]],\\\"labels\\\":[\\\"默认\\\"]}\",\"output_parameters\":[{\"name\":\"file\",\"description\":\"生成的图表文件\",\"type\":\"file\"}],\"method\":\"POST\",\"url\":\"http://aio-plugin\",\"path\":\"/api/v1/open/ti/chart/line\",\"status\":1,\"display\":\"image\"},{\"id\":\"ac24dd58-cb10-9c3e-3734-e751f840b24b\",\"name\":\"柱状图生成\",\"description\":\"基于数据生成柱状图。\",\"input_parameters\":[{\"name\":\"Content-Type\",\"type\":\"string\",\"required\":true,\"configed\":true,\"default_value\":\"application/json\",\"location\":\"header\"},{\"name\":\"token\",\"type\":\"string\",\"required\":true,\"configed\":true,\"default_value\":\"b0b1b70d79d64dcaab401e7a1b4431ee\",\"location\":\"header\"},{\"name\":\"title\",\"description\":\"图表标题,示例值(测试图表)\",\"type\":\"string\",\"required\":false,\"location\":\"body\"},{\"name\":\"labelX\",\"description\":\"横坐标名称,示例值(横坐标)\",\"type\":\"string\",\"required\":false,\"location\":\"body\"},{\"name\":\"labelY\",\"description\":\"纵坐标名称,示例值(纵坐标)\",\"type\":\"string\",\"required\":false,\"location\":\"body\"},{\"name\":\"x\",\"description\":\"x坐标，一维数组，用于显示在横坐标上的坐标刻度,示例值([ \\\"2021-01-01\\\", \\\"2021-01-02\\\", \\\"2021-01-03\\\" ])\",\"type\":\"array\",\"schema\":[{\"type\":\"string\",\"required\":true}],\"required\":true,\"location\":\"body\"},{\"name\":\"y\",\"description\":\"y坐标数据，二维数组，数组元素为数值，每行为每个标签项对应的y坐标数据，即每一行对应一个labels中的一项,示例值([ [ 1, 2, 3 ], [ 4, 5, 6 ] ])\",\"type\":\"array\",\"schema\":[{\"type\":\"array\",\"schema\":[{\"type\":\"integer\",\"required\":true}],\"required\":true}],\"required\":true,\"location\":\"body\"},{\"name\":\"labels\",\"description\":\"标签项，一维数组，对于每一个标签项都会生成柱状图,示例值([ \\\"标签1\\\", \\\"标签2\\\" ])\",\"type\":\"array\",\"schema\":[{\"type\":\"string\",\"required\":true}],\"required\":false,\"location\":\"body\"}],\"input_example\":\"{\\\"title\\\":\\\"test\\\",\\\"labelX\\\":\\\"横坐标\\\",\\\"labelY\\\":\\\"纵坐标\\\",\\\"x\\\":[\\\"2021-01-01\\\",\\\"2021-01-02\\\",\\\"2021-01-03\\\"],\\\"y\\\":[[1,2,3],[4,5,6]],\\\"labels\\\":[\\\"标签1\\\",\\\"标签2\\\"]}\",\"output_parameters\":[{\"name\":\"file\",\"description\":\"生成的图表文件\",\"type\":\"file\"}],\"method\":\"POST\",\"url\":\"http://aio-plugin\",\"path\":\"/api/v1/open/ti/chart/bar\",\"status\":1,\"display\":\"image\"}]}');