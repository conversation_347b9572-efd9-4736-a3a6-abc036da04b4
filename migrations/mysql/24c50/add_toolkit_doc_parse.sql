UPDATE `secwalk`.`toolkits` SET `type` = 'system', `tools` = '[\"8342e6cb-9371-75fb-6394-728b37cf498b\", \"018826c1-610a-3545-79de-3fe32b82a95b\"]', `config` = '{\"id\":\"b719ff1f-2b4e-de95-b015-dc5cd077501f\",\"name\":\"文档解析工具集\",\"description\":\"用于提取各种文档的内容。\",\"type\":\"system\",\"url\":\"http://server-openapi:9996\",\"tools\":[{\"id\":\"8342e6cb-9371-75fb-6394-728b37cf498b\",\"name\":\"文档解析任务创建\",\"description\":\"创建一个文档解析任务，提交需要文档解析的文件。\",\"input_parameters\":[{\"name\":\"Content-Type\",\"type\":\"string\",\"required\":true,\"configed\":true,\"default_value\":\"multipart/form-data\",\"location\":\"header\"},{\"name\":\"hn-token\",\"type\":\"string\",\"required\":true,\"configed\":true,\"default_value\":\"9e0cbb2262705bc2965926ae6f57699b\",\"location\":\"header\"},{\"name\":\"file\",\"description\":\"指定需要解析的文件\",\"type\":\"file\",\"required\":true,\"location\":\"body\"},{\"name\":\"imageocr\",\"description\":\"是否需要理解图片内容 false/true\",\"type\":\"boolean\",\"required\":false,\"default_value\":false,\"location\":\"body\"}],\"input_example\":\"{\\\"file\\\":\\\"file:test.docx<41945a0e-f08e-4a10-a9dd-65a4e22e753f>\\\",\\\"imageocr\\\":\\\"True\\\"}\",\"output_parameters\":[{\"name\":\"msg\",\"type\":\"string\",\"required\":true},{\"name\":\"code\",\"description\":\"响应码 0-成功，非0-失败\",\"type\":\"integer\",\"required\":true},{\"name\":\"data\",\"type\":\"object\",\"schema\":[{\"name\":\"taskId\",\"description\":\"解析任务对应的任务ID，通过结果获取接口可获取解析内容。\",\"type\":\"string\",\"required\":true}],\"required\":true}],\"output_example\":\"{\\\"code\\\":0,\\\"msg\\\":\\\"成功\\\",\\\"data\\\":{\\\"taskId\\\":\\\"167f06b7becd4a7ab1c8c9c6b7f43015\\\"}}\",\"method\":\"POST\",\"path\":\"/inner/open/api/v2/document/plugin/analysis/submit\",\"status\":1},{\"id\":\"018826c1-610a-3545-79de-3fe32b82a95b\",\"name\":\"文档解析内容获取\",\"description\":\"获取文档解析的文本内容。\",\"input_parameters\":[{\"name\":\"Content-Type\",\"type\":\"string\",\"required\":true,\"configed\":true,\"default_value\":\"application/json\",\"location\":\"header\"},{\"name\":\"hn-token\",\"type\":\"string\",\"required\":true,\"configed\":true,\"default_value\":\"9e0cbb2262705bc2965926ae6f57699b\",\"location\":\"header\"},{\"name\":\"taskId\",\"description\":\"任务ID\",\"type\":\"string\",\"required\":true,\"location\":\"body\"}],\"input_example\":\"{\\\"taskId\\\":\\\"5904c77c7d274c64956e65b2abb95510\\\"}\",\"output_parameters\":[{\"name\":\"data\",\"type\":\"object\",\"schema\":[{\"name\":\"msg\",\"description\":\"信息描述\",\"type\":\"string\",\"required\":true},{\"name\":\"code\",\"description\":\"-1 失败，0 解析中， 1 成功\",\"type\":\"integer\",\"required\":true},{\"name\":\"data\",\"description\":\"解析结果\",\"type\":\"string\",\"required\":true}],\"required\":true}],\"output_example\":\"{\\\"code\\\":0,\\\"msg\\\":\\\"成功\\\",\\\"data\\\":{\\\"msg\\\":\\\"analysis success！\\\",\\\"code\\\":1,\\\"data\\\":\\\"今天天气怎么样\\\\n今天天气很好\\\\n今天天气很好\\\\n\\\\n\\\"}}\",\"method\":\"POST\",\"path\":\"/inner/open/api/v2/document/plugin/analysis/status\",\"status\":1}]}' WHERE `id` = 'b719ff1f-2b4e-de95-b015-dc5cd077501f';