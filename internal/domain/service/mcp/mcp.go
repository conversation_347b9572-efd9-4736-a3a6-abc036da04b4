package mcp

import (
	"context"
	"fmt"
	"secwalk/internal/domain/core/endpoint/hostmcp"
	"secwalk/internal/domain/core/schema"
	"secwalk/internal/domain/repo"
)

type Service struct {
	mcpRepo *repo.McpRepo
}

func NewService(mcpRepo *repo.McpRepo) *Service {
	return &Service{mcpRepo: mcpRepo}
}

func (s *Service) GetMCPServerInfo(ctx context.Context, msc schema.ServersFile,
	opts ...schema.CallOption) ([]*hostmcp.ServerInfo, error) {

	res := make([]*hostmcp.ServerInfo, 0)

	for k, v := range msc.McpServers {
		cli, err := hostmcp.NewMCPClient(ctx, k, v, opts...)
		if err != nil {
			return nil, err
		}

		info, err := cli.QueryServer(ctx)
		if err != nil {
			return nil, err
		}

		info.Toolkit.ID = v.ID
		info.Toolkit.Name = k
		res = append(res, info)
	}

	return res, nil
}

func (s *Service) UpsertMCPServer(ctx context.Context, msc schema.ServersFile) error {
	if err := s.mcpRepo.Upsert(ctx, msc); err != nil {
		return fmt.Errorf("%w: %v", schema.ErrMCPServerConfig, err)
	}

	return nil
}

func (s *Service) DeleteMCPServer(ctx context.Context, id string) error {
	if err := s.mcpRepo.Delete(ctx, id); err != nil {
		return fmt.Errorf("%w: %v", schema.ErrParameter, id)
	}

	return nil
}
