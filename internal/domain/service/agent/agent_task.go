package agent

import (
	"context"
	"errors"
	"fmt"
	"secwalk/internal/domain/core/callback"
	"secwalk/internal/domain/core/schema"
	"secwalk/pkg/logger"
	"sync"

	"github.com/sirupsen/logrus"
)

var (
	ErrTaskSidExist    = errors.New("task sid exist")
	ErrTaskNotFound    = errors.New("task not found")
	ErrTaskInvalidType = errors.New("task type invalid")
)

const (
	StatusRunning  = 1
	StatusFinished = 2
	StatusCanceled = 3
	StatusFailured = 4
)

type Task struct {
	mux    *sync.Mutex        `json:"-"`
	cancel context.CancelFunc `json:"-"`

	SID      string                     `json:"sid,omitempty"`
	QID      string                     `json:"qid,omitempty"`
	Messages []*callback.PreviewMessage `json:"messages,omitempty"`
	Status   int                        `json:"status,omitempty"`
	Errors   string                     `json:"errors,omitempty"`
}

func (s *Service) TaskSubmit(ctx context.Context, inputs map[string]any,
	opts ...Option) (map[string]string, error) {
	opt := &Options{}
	for _, o := range opts {
		o(opt)
	}

	id := opt.sid + opt.qid
	mux := sync.Mutex{}
	pms := map[string]*callback.PreviewMessage{}
	opt.fnStreamPreview = func(msg *callback.PreviewMessage) error {
		mux.Lock()
		defer mux.Unlock()

		if msg.Content == callback.EOFMARK {
			last, ok := pms[msg.MessageID]
			if ok {
				delete(pms, msg.MessageID)
				s.setTaskMessage(id, last)
			}
		} else {
			last, ok := pms[msg.MessageID]
			if ok {
				last.Content += msg.Content
				for k, v := range msg.Results {
					last.Results[k] = v
				}
			} else {
				last = &callback.PreviewMessage{
					Mode:      callback.ModePreview,
					Type:      msg.Type,
					NodeID:    msg.NodeID,
					From:      msg.From,
					Name:      msg.Name,
					Timestamp: msg.Timestamp,
					MessageID: msg.MessageID,
					Content:   msg.Content,
					Results:   msg.Results,
				}
				if len(last.Results) == 0 {
					last.Results = make(map[string]any)
				}
			}
			pms[msg.MessageID] = last
		}

		return nil
	}

	agent, session, err := s.createAgent(ctx, inputs, opt)
	if err != nil {
		return nil, err
	}

	_, err = s.checkInputs(ctx, inputs, opt)
	if err != nil {
		return nil, err
	}

	logrus.WithField(logger.KeyCategory, logger.CategoryService).
		WithField(logger.KeyEXT, opt.ext).
		Info("create agent task")
	runCtx, cancel := context.WithCancel(context.Background())
	s.tasks.Store(id, &Task{
		mux:    &sync.Mutex{},
		cancel: cancel,

		SID:      opt.sid,
		QID:      opt.qid,
		Messages: make([]*callback.PreviewMessage, 0),
		Status:   StatusRunning,
	})

	go func() {
		defer cancel()

		_, err := s.runAgent(runCtx, agent, session, inputs, opt)
		if err != nil {
			s.setTaskStatus(id, StatusFailured, err.Error())
		} else {
			s.setTaskStatus(id, StatusFinished, "")
		}

		for _, last := range pms {
			s.setTaskMessage(id, last)
		}
	}()

	return map[string]string{"aid": opt.aid, "sid": opt.sid, "qid": opt.qid}, nil
}

func (s *Service) TaskCancel(id string) error {
	return s.setTaskStatus(id, StatusCanceled, "")
}

func (s *Service) TaskMessages(id string) (*Task, error) {
	val, ok := s.tasks.Load(id)
	if !ok {
		return nil, schema.ErrTaskID
	}

	task, ok := val.(*Task)
	if !ok {
		return nil, fmt.Errorf("%w: %v", schema.ErrTaskID, ErrTaskInvalidType)
	}

	task.mux.Lock()
	defer task.mux.Unlock()

	res := &Task{
		SID:      task.SID,
		QID:      task.QID,
		Messages: task.Messages,
		Status:   task.Status,
		Errors:   task.Errors,
	}

	if task.Status >= StatusFinished {
		s.tasks.Delete(id)
	} else {
		task.Messages = make([]*callback.PreviewMessage, 0)
		s.tasks.Store(id, task)
	}

	return res, nil
}

func (s *Service) setTaskMessage(id string, msg *callback.PreviewMessage) error {
	if len(msg.Content) > 0 || len(msg.Results) > 0 {
		val, ok := s.tasks.Load(id)
		if !ok {
			return schema.ErrTaskID
		}

		task, ok := val.(*Task)
		if !ok {
			return fmt.Errorf("%w: %v", schema.ErrTaskID, ErrTaskInvalidType)
		}

		task.mux.Lock()
		task.Messages = append(task.Messages, msg)
		s.tasks.Store(id, task)
		task.mux.Unlock()
	}

	return nil
}

func (s *Service) setTaskStatus(id string, status int, errors string) error {
	val, ok := s.tasks.Load(id)
	if !ok {
		return schema.ErrTaskID
	}

	task, ok := val.(*Task)
	if !ok {
		return fmt.Errorf("%w: %v", schema.ErrTaskID, ErrTaskInvalidType)
	}

	task.mux.Lock()
	if task.Status < StatusFinished && status == StatusCanceled {
		task.cancel()
		task.Status = StatusCanceled
	} else {
		task.Status = status
		task.Errors = errors
	}
	task.mux.Unlock()

	return nil
}
