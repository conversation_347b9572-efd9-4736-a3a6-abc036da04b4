package agent

import (
	"context"
	"fmt"
	"secwalk/internal/config"
	"secwalk/internal/domain/core/schema"
	"secwalk/internal/domain/core/xguard"
	"secwalk/internal/domain/repo"
	"secwalk/pkg/logger"
	"sync"

	"github.com/sirupsen/logrus"

	"github.com/google/uuid"
)

type Service struct {
	xguard      *xguard.XGuard
	agentRepo   *repo.AgentRepo
	toolkitRepo *repo.ToolkitRepo
	sessionRepo *repo.SessionRepo
	mcpRepo     *repo.McpRepo
	termRepo    *repo.TermRepo

	tasks *sync.Map
}

func NewService(xguard *xguard.XGuard, agentRepo *repo.AgentRepo,
	toolkitRepo *repo.ToolkitRepo, sessionRepo *repo.SessionRepo,
	mcpRepo *repo.McpRepo, termRepo *repo.TermRepo) *Service {
	return &Service{
		xguard:      xguard,
		agentRepo:   agentRepo,
		toolkitRepo: toolkitRepo,
		sessionRepo: sessionRepo,
		mcpRepo:     mcpRepo,
		termRepo:    termRepo,
		tasks:       new(sync.Map),
	}
}

func (s *Service) QueryAgents(ctx context.Context, ids []string) ([]*schema.AgentConfig, error) {
	if len(ids) == 0 {
		return s.agentRepo.QueryAndDecryptAll(ctx)
	}

	agents := make([]*schema.AgentConfig, 0)
	for _, id := range ids {
		agent, err := s.agentRepo.QueryAndDecrypt(ctx, id)
		if err != nil {
			logrus.WithField(logger.KeyCategory, logger.CategoryCore).
				Errorf("[%s] query agent error： %v", id, err)
			continue
		}

		if err := schema.VerifyAgent(agent, config.GetVersion()); err != nil {
			logrus.WithField(logger.KeyCategory, logger.CategoryCore).
				Errorf("[%s] query agent error： %v", id, err)
			continue
		}

		agents = append(agents, agent)
	}
	return agents, nil
}

func (s *Service) QueryIDs(ctx context.Context) ([]string, error) {
	return s.agentRepo.QueryIDs(ctx)
}

func (s *Service) ExportAgent(ctx context.Context, ids []string) ([]repo.AgentConfig, error) {
	agents := make([]repo.AgentConfig, 0)

	for _, id := range ids {
		agent, err := s.agentRepo.Export(ctx, id)
		if err != nil {
			return nil, fmt.Errorf("%w: %v, %v", schema.ErrParameter, err, ids)
		}
		agents = append(agents, *agent)
	}

	return agents, nil
}

func (s *Service) ImportAgent(ctx context.Context, agents []repo.AgentConfig) error {
	for _, v := range agents {
		if err := s.agentRepo.Import(ctx, v); err != nil {
			return err
		}
	}

	return nil
}

func (s *Service) CreateAgent(ctx context.Context, ac *schema.AgentConfig) error {
	if len(ac.ID) == 0 {
		ac.ID = uuid.NewString()
	}

	if err := schema.VerifyAgent(ac, config.GetVersion()); err != nil {
		return fmt.Errorf("%w: %v", schema.ErrConfig, err)
	}

	return s.agentRepo.Create(ctx, ac)
}

func (s *Service) UpdateAgent(ctx context.Context, ac *schema.AgentConfig) error {
	if len(ac.ID) == 0 {
		ac.ID = uuid.NewString()
	}

	if err := schema.VerifyAgent(ac, config.GetVersion()); err != nil {
		return fmt.Errorf("%w: %v", schema.ErrConfig, err)
	}

	return s.agentRepo.Update(ctx, ac)
}

func (s *Service) UpdateAgentExtension(ctx context.Context, bs *schema.AgentExtension) error {
	return s.agentRepo.UpdateExtension(ctx, bs)
}

func (s *Service) UpsertAgent(ctx context.Context, ac *schema.AgentConfig) error {
	if len(ac.ID) == 0 {
		ac.ID = uuid.NewString()
	}

	if err := schema.VerifyAgent(ac, config.GetVersion()); err != nil {
		return fmt.Errorf("%w: %v", schema.ErrConfig, err)
	}

	if err := s.agentRepo.Create(ctx, ac); err != nil {
		return s.agentRepo.Update(ctx, ac)
	}
	return nil
}

func (s *Service) DeleteAgent(ctx context.Context, id string) error {
	return s.agentRepo.Delete(ctx, id)
}

func (s *Service) VerifyAgent(_ context.Context, ac *schema.AgentConfig) error {
	if err := schema.VerifyAgent(ac, config.GetVersion()); err != nil {
		return fmt.Errorf("%w: %v", schema.ErrConfig, err)
	}
	return nil
}

func (s *Service) ExecuteAgent(ctx context.Context, inputs map[string]any, opts ...Option) (*schema.AgentChatResult, error) {
	opt := &Options{}
	for _, o := range opts {
		o(opt)
	}

	agent, session, err := s.createAgent(ctx, inputs, opt)
	if err != nil {
		return nil, err
	}

	res, err := s.checkInputs(ctx, inputs, opt)
	if err != nil {
		return nil, err
	}
	if res != nil {
		return res, nil
	}

	return s.runAgent(ctx, agent, session, inputs, opt)
}
