package agent

import (
	"secwalk/internal/domain/core/agent"
	"secwalk/internal/domain/core/callback"
	"secwalk/internal/domain/core/memory"
	"secwalk/internal/domain/core/message"
	"secwalk/internal/domain/core/schema"
	"secwalk/internal/domain/core/schema/ext"
	"secwalk/internal/domain/repo"
	"secwalk/pkg/random"
	"secwalk/pkg/selector"

	"github.com/google/uuid"
)

type Option func(*Options)

type Options struct {
	ext             ext.ExtType
	newSid          bool
	sid             string
	qid             string
	aid             string
	cfg             *schema.AgentConfig
	variables       map[string]any
	fnStreamPreview callback.FnStreamPreview
	fnStreamVerbose callback.FnStreamVerbose
	fnQuestion      callback.FnQuestion
}

func WithEXT(str string) Option {
	return func(o *Options) {
		o.ext = ext.New(str)
	}
}

func WithSID(sid string) Option {
	return func(o *Options) {
		if len(sid) == 0 {
			sid = random.RandomID()
			o.newSid = true
		} else {
			o.sid = sid
			o.newSid = false
		}
	}
}

func WithQID(qid string) Option {
	return func(o *Options) {
		if len(qid) == 0 {
			qid = random.RandomID()
		}
		o.qid = qid
	}
}

func WithAID(aid string) Option {
	return func(o *Options) { o.aid = aid }
}

func WithConfig(cfg *schema.AgentConfig) Option {
	return func(o *Options) {
		o.cfg = cfg
		if o.cfg != nil && len(o.cfg.ID) == 0 {
			o.cfg.ID = uuid.NewString()
		}
	}
}

func WithVariables(variables map[string]any) Option {
	return func(o *Options) { o.variables = variables }
}

func WithStreamPreview(fn callback.FnStreamPreview) Option {
	return func(o *Options) { o.fnStreamPreview = fn }
}

func WithStreamVerbose(fn callback.FnStreamVerbose) Option {
	return func(o *Options) { o.fnStreamVerbose = fn }
}

func WithFnQuestion(fn callback.FnQuestion) Option {
	return func(o *Options) { o.fnQuestion = fn }
}

func getAgentOptions(opts *Options, msg []message.Message,
	gateway *selector.Instance, termRepo *repo.TermRepo) []agent.Option {
	agentOpts := []agent.Option{
		agent.WithGateway(gateway),
		agent.WithMasterEntry(),
		agent.WithAgentConfig(opts.cfg),
		agent.WithTermRepo(termRepo),
	}
	if opts.cfg.AgentBase.SessionRound == 0 || len(msg) <= opts.cfg.AgentBase.SessionRound*2 {
		agentOpts = append(agentOpts, agent.WithMemory(memory.NewChatMemory(
			memory.WithChatHistory(
				memory.NewChatMessageHistory(
					memory.WithPreviousMessages(msg),
				),
			),
		)))
	} else {
		index := len(msg) - opts.cfg.AgentBase.SessionRound*2
		agentOpts = append(agentOpts, agent.WithMemory(memory.NewChatMemory(
			memory.WithChatHistory(
				memory.NewChatMessageHistory(
					memory.WithPreviousMessages(msg[index:]),
				),
			),
		)))
	}
	if len(opts.variables) > 0 {
		agentOpts = append(agentOpts, agent.WithVariables(opts.variables))
	}
	if opts.fnStreamPreview != nil {
		agentOpts = append(agentOpts, agent.WithStreamPreview(opts.fnStreamPreview))
	}
	if opts.fnStreamVerbose != nil {
		agentOpts = append(agentOpts, agent.WithStreamVerbose(opts.fnStreamVerbose))
	}
	if opts.fnQuestion != nil {
		agentOpts = append(agentOpts, agent.WithFnQuestion(opts.fnQuestion))
	}

	return agentOpts
}
