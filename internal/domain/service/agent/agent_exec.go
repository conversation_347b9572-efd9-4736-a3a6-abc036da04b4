package agent

import (
	"context"
	"fmt"
	"secwalk/internal/config"
	"secwalk/internal/domain/core/agent"
	"secwalk/internal/domain/core/callback"
	"secwalk/internal/domain/core/message"
	"secwalk/internal/domain/core/schema"
	"secwalk/internal/domain/core/schema/ext"
	"secwalk/pkg/logger"
	"secwalk/pkg/random"
	"secwalk/pkg/selector"
	"secwalk/pkg/util"
	"time"

	"github.com/sirupsen/logrus"
)

func (s *Service) createAgent(ctx context.Context, inputs map[string]any,
	opt *Options) (*agent.Agent, *message.Session, error) {
	var err error

	// 拼接扩展信息
	opt.ext.SetValue(ext.EXTSID, opt.sid)
	opt.ext.SetValue(ext.EXTQID, opt.qid)

	// 查询智能体配置
	if opt.cfg == nil {
		logrus.WithField(logger.KeyCategory, logger.CategoryService).
			WithField(logger.KeyEXT, opt.ext).
			Infof("query agent config: %s", opt.aid)
		opt.cfg, err = s.agentRepo.QueryAndDecrypt(ctx, opt.aid)
		if err != nil {
			return nil, nil, err
		}
	}
	if err := schema.VerifyAgent(opt.cfg, config.GetVersion()); err != nil {
		return nil, nil, fmt.Errorf("%w: %v", schema.ErrConfig, err)
	}

	opt.ext.SetValue(ext.EXTAID, opt.cfg.ID)
	opt.ext.SetValue(ext.EXTUID, opt.cfg.UID)
	opt.ext.SetValue(ext.EXTORG, opt.cfg.ORG)

	// 获取智能体的历史会话
	session := &message.Session{
		ID:       opt.sid,
		Messages: make([]message.Message, 0),
	}
	if !opt.newSid {
		logrus.WithField(logger.KeyCategory, logger.CategoryService).
			WithField(logger.KeyEXT, opt.ext).
			Infof("query session: %s", opt.sid)
		session, err = s.sessionRepo.Query(ctx, opt.sid, false)
		if err != nil {
			return nil, nil, fmt.Errorf("%w: %v", schema.ErrMiddleware, err)
		}
	}

	// 初始化模型客户端
	logrus.WithField(logger.KeyCategory, logger.CategoryService).
		WithField(logger.KeyEXT, opt.ext).
		Info("selector gateway address")
	gateway, err := selector.G().GatewayInstance()
	if err != nil {
		return nil, session, fmt.Errorf("%w: %v", schema.ErrGateway, err)
	}

	// 用户未传入历史记录则使用系列内部会话记录
	var messages []message.Message
	his, ok := inputs[schema.ReservedHistory]
	if !ok {
		logrus.WithField(logger.KeyCategory, logger.CategoryService).
			WithField(logger.KeyEXT, opt.ext).
			Info("use core history")
		messages = session.Messages
	} else {
		logrus.WithField(logger.KeyCategory, logger.CategoryService).
			WithField(logger.KeyEXT, opt.ext).
			Info("use user history")
		messages = message.ToMessages(his)
	}

	if !config.GetFeature().UseXGuard {
		opt.ext.SetValue(ext.EXTXORGRiskLevel, schema.ORGRiskLevelLow)
	}

	// 创建智能体实例
	logrus.WithField(logger.KeyCategory, logger.CategoryService).
		WithField(logger.KeyEXT, opt.ext).
		Info("create agent instance")
	agent, err := agent.NewAgent(
		s.agentRepo,
		s.toolkitRepo,
		s.mcpRepo,
		opt.ext,
		getAgentOptions(opt, messages, gateway, s.termRepo)...,
	)
	if err != nil {
		return nil, session, err
	}

	return agent, session, nil
}

func (s *Service) checkInputs(ctx context.Context, inputs map[string]any,
	opt *Options) (*schema.AgentChatResult, error) {
	// 风险检测
	xorgRiskLevel := opt.ext.GetValue(ext.EXTXORGRiskLevel)
	if xorgRiskLevel == schema.ORGRiskLevelMedium || xorgRiskLevel == schema.ORGRiskLevelHigh {
		inputsStr := util.ToJsonString(inputs["input"])

		logrus.WithField(logger.KeyCategory, logger.CategoryService).
			WithField(logger.KeyEXT, opt.ext).
			Infof("xagent checking: %s", inputsStr)

		trait, answer, ok := s.xguard.Match(ctx, inputsStr)
		if trait != nil {
			logrus.WithField(logger.KeyCategory, logger.CategoryService).
				WithField(logger.KeyEXT, opt.ext).
				Warnf("checked involves words: %s", trait.Text)
		}
		if ok {
			if len(answer) > 0 {
				if opt.fnStreamPreview != nil {
					callback.PreviewFackStream(opt.fnStreamPreview,
						&callback.PreviewMessage{
							Type:      callback.TypeInline,
							From:      callback.FromExecuteResult,
							Name:      opt.cfg.Name,
							Timestamp: time.Now().UnixMilli(),
							MessageID: random.UniqueID(),
							Content:   answer,
						})
				}
				return &schema.AgentChatResult{
					Session: &message.Session{
						ID: opt.sid,
						Messages: []message.Message{
							{Role: message.ChatMessageRoleUser, Content: inputs[schema.DefaultInputKey].(string)},
							{Role: message.ChatMessageRoleAssistant, Content: answer},
						},
					},
				}, nil
			}
			return nil, fmt.Errorf("%w: %v",
				schema.ErrInputs, schema.ErrInvolvesSensitiveWord)
		}
	}

	return nil, nil
}

func (s *Service) runAgent(ctx context.Context, agent *agent.Agent, session *message.Session,
	inputs map[string]any, opt *Options) (*schema.AgentChatResult, error) {

	var opts []schema.CallOption
	opts = append(opts,
		schema.WithEXT(opt.ext),
	)

	// 执行工作流
	logrus.WithField(logger.KeyCategory, logger.CategoryService).
		WithField(logger.KeyEXT, opt.ext).
		Infof("execute agent, inputs: %s", util.ToJsonString(inputs))
	res, err := agent.Call(ctx, inputs, opts...)
	if err != nil {
		return nil, err
	}

	// 更新会话记录
	session.Messages = append(session.Messages, res.Session.Messages...)
	if opt.newSid {
		err = s.sessionRepo.Create(ctx, session)
	} else {
		err = s.sessionRepo.Update(ctx, session)
	}
	if err != nil {
		return nil, fmt.Errorf("%w: %v", schema.ErrMiddleware, err)
	}

	logrus.WithField(logger.KeyCategory, logger.CategoryService).
		WithField(logger.KeyEXT, opt.ext).
		Infof("execute agent, result: %s", res)
	return res, nil
}
