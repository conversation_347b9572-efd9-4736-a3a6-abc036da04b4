package xguard

import (
	"context"
	"secwalk/internal/domain/core/xguard"
	"secwalk/internal/domain/repo"
	"secwalk/internal/domain/repo/ent"
)

type Service struct {
	xguard     *xguard.XGuard
	xguardRepo *repo.XGuardRepo
}

func NewService(xguard *xguard.XGuard, repo *repo.XGuardRepo) *Service {
	return &Service{xguard: xguard, xguardRepo: repo}
}

func (s *Service) TraitQuery(ctx context.Context) ([]*ent.Trait, error) {
	return s.xguardRepo.QueryTrait(ctx)
}

func (s *Service) TraitUpsert(ctx context.Context, traits []*ent.Trait) error {
	if err := s.xguard.Reload(traits); err != nil {
		return err
	}

	return s.xguardRepo.UpsertTrait(ctx, traits)
}

func (s *Service) TraitInsert(ctx context.Context, traits []*ent.Trait) error {
	if err := s.xguardRepo.UpsertTrait(ctx, traits); err != nil {
		return err
	}

	curTraits, err := s.xguardRepo.QueryTrait(ctx)
	if err != nil {
		return err
	}

	return s.xguard.Reload(curTraits)
}

func (s *Service) TraitDelete(ctx context.Context, traits []string) error {
	if err := s.xguardRepo.DeleteTrait(ctx, traits); err != nil {
		return err
	}

	curTraits, err := s.xguardRepo.QueryTrait(ctx)
	if err != nil {
		return err
	}

	return s.xguard.Reload(curTraits)
}
