package session

import (
	"context"
	"secwalk/internal/domain/core/message"
	"secwalk/internal/domain/repo"
)

type Service struct {
	repo *repo.SessionRepo
}

func NewService(repo *repo.SessionRepo) *Service {
	return &Service{repo: repo}
}

func (s *Service) QuerySession(ctx context.Context, id string) (*message.Session, error) {
	return s.repo.Query(ctx, id, true)
}

func (s *Service) InsertSession(ctx context.Context, id string, role message.Role, content string) error {
	session, err := s.repo.Query(ctx, id, true)
	if err != nil {
		return err
	}

	if session.Messages == nil {
		session.Messages = []message.Message{}
	}
	session.Messages = append(session.Messages, message.Message{
		Role:    role,
		Content: content,
	})

	return s.repo.Update(ctx, session)
}
