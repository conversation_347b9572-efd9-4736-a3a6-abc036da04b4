package toolkit

import (
	"context"
	"fmt"
	"secwalk/internal/domain/core/schema"
	"secwalk/internal/domain/core/toolkit/python"
	"secwalk/internal/domain/core/toolkit/restful"
	"secwalk/internal/domain/repo"

	"github.com/google/uuid"
)

type Service struct {
	toolkitRepo *repo.ToolkitRepo
}

func NewService(toolkitRepo *repo.ToolkitRepo) *Service {
	return &Service{
		toolkitRepo: toolkitRepo,
	}
}

// 获取多个工具集
func (s *Service) QueryToolkits(ctx context.Context, ids []string) ([]schema.Toolkit, error) {
	if len(ids) == 0 {
		return s.toolkitRepo.QueryAllToolkit(ctx)
	}

	toolkits := make([]schema.Toolkit, 0)
	for _, id := range ids {
		toolkit, err := s.toolkitRepo.QueryToolkit(ctx, id)
		if err != nil {
			return nil, err
		}
		toolkits = append(toolkits, *toolkit)
	}
	return toolkits, nil
}

// 获取系统工具集
func (s *Service) QuerySystemToolkits(ctx context.Context) ([]schema.Toolkit, error) {
	return s.toolkitRepo.FindAllDefaultToolkit(ctx)
}

// 创建工具集
func (s *Service) CreateToolkit(ctx context.Context, toolkit *schema.Toolkit) error {
	if toolkit.Type == schema.ToolkitTypeSystem {
		return nil
	}

	if len(toolkit.ID) == 0 {
		toolkit.ID = uuid.NewString()
	}

	if err := schema.VerifyToolkit(toolkit); err != nil {
		return fmt.Errorf("%w: %v", schema.ErrConfig, err)
	}

	return s.toolkitRepo.CreateToolkit(ctx, toolkit)
}

// 更新工具集
func (s *Service) UpdateToolkit(ctx context.Context, toolkit *schema.Toolkit) error {
	if toolkit.Type == schema.ToolkitTypeSystem {
		return nil
	}

	if err := schema.VerifyToolkit(toolkit); err != nil {
		return fmt.Errorf("%w: %v", schema.ErrConfig, err)
	}

	return s.toolkitRepo.UpdateToolkit(ctx, toolkit)
}

// 创建并更新工具集
func (s *Service) UpsertToolkit(ctx context.Context, toolkit *schema.Toolkit) error {
	if toolkit.Type == schema.ToolkitTypeSystem {
		return nil
	}

	if len(toolkit.ID) == 0 {
		toolkit.ID = uuid.NewString()
	}
	for i, v := range toolkit.Tools {
		if len(v.ID) == 0 {
			toolkit.Tools[i].ID = uuid.NewString()
		}
	}

	if err := schema.VerifyToolkit(toolkit); err != nil {
		return fmt.Errorf("%w: %v", schema.ErrConfig, err)
	}

	return s.toolkitRepo.UpsertToolkit(ctx, toolkit)
}

// 删除工具集
func (s *Service) DeleteToolkit(ctx context.Context, toolkitID string) error {
	return s.toolkitRepo.DeleteToolkit(ctx, toolkitID)
}

// 调试能力
func (s *Service) DebugAbility(ctx context.Context, toolkit *schema.Toolkit,
	inputs map[string]any, opts ...schema.CallOption) (string, error) {
	if inputs == nil {
		inputs = make(map[string]any)
	}

	if len(toolkit.Tools) == 0 {
		return "", fmt.Errorf("%w: miss ability config", schema.ErrConfig)
	}

	if err := schema.VerifyToolkit(toolkit); err != nil {
		return "", fmt.Errorf("%w: %v", schema.ErrConfig, err)
	}

	if len(toolkit.Tools[0].Code) == 0 {
		if len(toolkit.Tools[0].Url) == 0 {
			toolkit.Tools[0].Url = toolkit.Url
		}
		toolkit.Tools[0].Creator = restful.New
	} else {
		toolkit.Tools[0].Creator = python.New
	}

	for _, tkp := range toolkit.Parameters {
		for _, tp := range toolkit.Tools[0].InputParameters {
			if tp.Name == tkp.Name {
				continue
			}
		}
		toolkit.Tools[0].InputParameters = append(toolkit.Tools[0].InputParameters, tkp)
	}

	// 创建工具实例
	ability, err := toolkit.Tools[0].Creator(
		schema.WithToolConfig(&toolkit.Tools[0]),
	)

	if err != nil {
		return "", err
	}

	return schema.ToolCall(ctx, ability, inputs, opts...)
}

// 调用能力
func (s *Service) ExecuteAbility(ctx context.Context, toolID string,
	inputs map[string]any, opts ...schema.CallOption) (string, error) {
	// 获取工具实例
	ability, err := s.toolkitRepo.FetchAbility(ctx, toolID, []schema.Parameter{})
	if err != nil {
		return "", err
	}

	return schema.ToolCall(ctx, ability, inputs, opts...)
}
