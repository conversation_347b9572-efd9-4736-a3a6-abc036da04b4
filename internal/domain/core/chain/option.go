package chain

import (
	"secwalk/internal/domain/core/memory"
	"secwalk/internal/domain/core/prompt"
	"secwalk/internal/domain/core/schema"
)

type Option func(*Options)

type Options struct {
	systemPrompt       string
	userPrompt         string
	systemPromptFormat prompt.MessageFormatter
	userPromptFormat   prompt.MessageFormatter
	prompt             prompt.FormatPrompter
	memory             memory.Memory
	tools              []schema.Tool
	agents             []schema.Agent
	db                 *schema.DB
	kb                 *schema.KB
	maxIterations      int
	parallel           bool
	once               bool
	noRepeat           bool
	noAction           bool
	cotVersion         string
}

func newDefaultChainOption() *Options {
	return &Options{
		memory:        memory.NewChatMemory(),
		maxIterations: schema.DefaultMaxReflections,
	}
}

// 语言链中可选系统提示词
func WithSystemPrompt(tmpl string) Option {
	return func(o *Options) {
		o.systemPrompt = tmpl
	}
}

// 语言链中可选用户提示词
func WithUserPrompt(tmpl string) Option {
	return func(o *Options) {
		o.userPrompt = tmpl
	}
}

// 语言链中可选系统提示词模板
func WithSystemPromptTmpl(fmt prompt.MessageFormatter) Option {
	return func(o *Options) {
		o.systemPromptFormat = fmt
	}
}

// 语言链中可选用户提示词模板
func WithUserPromptTmpl(fmt prompt.MessageFormatter) Option {
	return func(o *Options) {
		o.userPromptFormat = fmt
	}
}

// 语言链中可选提示词模板
func WithPrompt(prompt prompt.FormatPrompter) Option {
	return func(o *Options) {
		o.prompt = prompt
	}
}

// 语言链中可选记忆
func WithMemory(memory memory.Memory) Option {
	return func(o *Options) {
		o.memory = memory
	}
}

// 语言链中可选工具
func WithTools(tools []schema.Tool) Option {
	return func(o *Options) {
		o.tools = tools
	}
}

func WithTool(tool schema.Tool) Option {
	return func(o *Options) {
		if o.tools == nil {
			o.tools = make([]schema.Tool, 0)
		}

		o.tools = append(o.tools, tool)
	}
}

// 语言链中可选智能体
func WithAgents(agents []schema.Agent) Option {
	return func(o *Options) {
		o.agents = agents
	}
}

// 语言链中可选数据库
func WithDB(db *schema.DB) Option {
	return func(o *Options) {
		o.db = db
	}
}

// 语言链中可选知识库
func WithKB(kb *schema.KB) Option {
	return func(o *Options) {
		o.kb = kb
	}
}

// 思维链最大迭代次数
func WithMaxIterations(iterations int) Option {
	return func(o *Options) {
		o.maxIterations = iterations
	}
}

// 是否并发调用工具
func WithParallel() Option {
	return func(o *Options) {
		o.parallel = true
	}
}

// 仅调用一次工具
func WithOnce() Option {
	return func(o *Options) {
		o.once = true
	}
}

// 每种工具只调用一次
func WithNoRepeat() Option {
	return func(o *Options) {
		o.noRepeat = true
	}
}

// 仅生成参数不调用工具
func WithNoAction() Option {
	return func(o *Options) {
		o.noAction = true
	}
}

// 思维链版本
func WithCotVersion(version string) Option {
	return func(o *Options) {
		o.cotVersion = version
	}
}
