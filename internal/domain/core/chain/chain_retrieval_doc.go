package chain

import (
	"context"
	"fmt"
	"secwalk/internal/domain/core/callback"
	"secwalk/internal/domain/core/endpoint"
	"secwalk/internal/domain/core/llm"
	"secwalk/internal/domain/core/memory"
	"secwalk/internal/domain/core/prompt"
	"secwalk/internal/domain/core/schema"
	"secwalk/internal/domain/core/template"
	"secwalk/pkg/logger"
	"secwalk/pkg/random"
	"secwalk/pkg/util"
	"strings"
	"time"

	"github.com/sirupsen/logrus"
)

const VerboseNameRetrieval = "知识召回"

const (
	_defaultDocSystemPrompt = `你是问答任务的助手。 使用以下检索到的上下文来回答问题。`
	_defaultDocUserElement  = `# 注意事项:
1. 片段中以下述格式描述图片标识：image:$filename<$uuid>，其中$filename为图片文件名，$uuid为图片存储的唯一ID，当你需要使用该图片时，需要原样返回该图片标识字符串。
   例如：片段中包含图片image:record.png<b236320e-aa5e-44bb-ad89-c0289366f874>，当需要向用户展示该图片时，你需要返回image:record.png<b236320e-aa5e-44bb-ad89-c0289366f874>。
2. 不允许伪造图片标识，即不允许使用不存在的图片标识。

# 相关片段:
{{.element}}

# 步骤分析:
1. 理解用户问题
2. 根据相关片段，回答用户的问题。
`
	_defaultDocUserQuestion = `# 用户问题:
{{.input}} 

# 回答内容：
`
)

type DocChain struct {
	systemPrompt string
	userPrompt   string
	kb           *schema.KB
	docchain     Chain
	noAction     bool
}

func NewDocChain(llm llm.Model, opts ...Option) (Chain, error) {
	opt := newDefaultChainOption()
	for _, option := range opts {
		option(opt)
	}

	systemTmpl := _defaultDocSystemPrompt
	userTmpl := fmt.Sprintf("%s\n%s", _defaultDocUserElement, _defaultDocUserQuestion)

	if len(opt.systemPrompt) > 0 {
		systemTmpl = opt.systemPrompt
	}
	if len(opt.userPrompt) > 0 {
		userTmpl = fmt.Sprintf("%s\n%s", _defaultDocUserElement, opt.userPrompt)
	}

	docchain, err := NewLLMChain(
		llm,
		WithSystemPromptTmpl(prompt.NewSystemMessagePromptTemplate(systemTmpl)),
		WithUserPromptTmpl(prompt.NewUserMessagePromptTemplate(userTmpl)),
	)
	if err != nil {
		return nil, err
	}

	return &DocChain{
		systemPrompt: opt.systemPrompt,
		userPrompt:   opt.userPrompt,
		kb:           opt.kb,
		docchain:     docchain,
		noAction:     opt.noAction,
	}, nil
}

func (c *DocChain) Call(ctx context.Context, inputs map[string]any, opts ...schema.CallOption) (string, error) {
	opt := schema.NewCallOptions()
	for _, o := range opts {
		o(opt)
	}

	input, ok := inputs[schema.DefaultInputKey].(string)
	if !ok {
		return "", ErrInputType
	}

	var err error
	var chunks string
	var cid = random.UniqueID()

	if opt.FnStreamVerbose != nil {
		opt.FnStreamVerbose(&callback.VerboseMessage{
			Type:      callback.TypeRetrievalDoc,
			Name:      VerboseNameRetrieval,
			NodeID:    opt.MessageNodeID,
			PID:       opt.PID,
			CID:       cid,
			Stage:     callback.StageRun,
			State:     callback.CallStateSuccess,
			Timestamp: time.Now().UnixMilli(),
			Inputs:    c.kb,
		})

		defer func() {
			if err != nil {
				opt.FnStreamVerbose(&callback.VerboseMessage{
					Type:      callback.TypeRetrievalDoc,
					Name:      VerboseNameRetrieval,
					NodeID:    opt.MessageNodeID,
					PID:       opt.PID,
					CID:       cid,
					Stage:     callback.StageEnd,
					State:     callback.CallStateFailure,
					Error:     err.Error(),
					Timestamp: time.Now().UnixMilli(),
				})
			} else {
				opt.FnStreamVerbose(&callback.VerboseMessage{
					Type:      callback.TypeRetrievalDoc,
					Name:      VerboseNameRetrieval,
					NodeID:    opt.MessageNodeID,
					PID:       opt.PID,
					CID:       cid,
					Stage:     callback.StageEnd,
					State:     callback.CallStateSuccess,
					Timestamp: time.Now().UnixMilli(),
					Output:    chunks,
				})
			}
		}()
	}

	chunks, err = c.retrieve(input, opt)
	if err != nil {
		return "", err
	}

	if c.noAction {
		return chunks, nil
	}

	// 输出召回数据
	if opt.FnStreamPreview != nil {
		opt.FnStreamPreview(&callback.PreviewMessage{
			Type:      callback.TypeInline,
			From:      callback.FromRetrievalDocContext,
			NodeID:    opt.MessageNodeID,
			Name:      opt.Name,
			Timestamp: time.Now().UnixMilli(),
			MessageID: random.UniqueID(),
			Content:   chunks,
		})

		if opt.MasterEntry {
			messageID := random.UniqueID()
			opts = append(opts, schema.WithFnStreaming(func(ctx context.Context, chunk []byte) error {
				from := callback.FromRetrievalDocAnswer
				if opt.OutputFinal {
					from = callback.FromExecuteResult
				}

				opt.FnStreamPreview(&callback.PreviewMessage{
					Type:      callback.TypeInline,
					From:      from,
					NodeID:    opt.MessageNodeID,
					Name:      opt.Name,
					Timestamp: time.Now().UnixMilli(),
					MessageID: messageID,
					Content:   string(chunk),
				})
				return nil
			}))
		}
	}

	var sysRender, userRender string
	if len(c.systemPrompt) > 0 {
		sysRender, _ = template.RenderTemplate(c.systemPrompt, inputs)
	}
	if len(c.userPrompt) > 0 {
		userRender, _ = template.RenderTemplate(c.userPrompt, inputs)
	}

	logrus.WithField(logger.KeyCategory, logger.CategoryCore).
		WithField(logger.KeyEXT, opt.EXT).
		Info("doc-chain: use llm summary answer")
	inputs["element"] = chunks
	answer, err := c.docchain.Call(
		ctx,
		inputs,
		append(opts,
			schema.WithSystemPromptRender(sysRender),
			schema.WithUserPromptRender(userRender))...,
	)
	if err != nil {
		return "", err
	}

	return answer, nil
}

func (c *DocChain) GetMemory() memory.Memory {
	return c.docchain.GetMemory()
}

func (c *DocChain) retrieve(query string, opt *schema.CallOptions) (string, error) {
	ret := &strings.Builder{}

	repos := make([]string, 0)
	files := make([]string, 0)
	for _, repo := range c.kb.Repos {
		// 从能力中台API查询知识库的文件信息
		logrus.WithField(logger.KeyCategory, logger.CategoryCore).
			WithField(logger.KeyEXT, opt.EXT).
			Infof("doc-chain: search knowledge: %s", repo.ID)
		doc, err := endpoint.KnowledgeSearch(repo.ID)
		if err != nil {
			return "", err
		}

		repos = append(repos, doc.ID)
		for i := range doc.Files {
			files = append(files, doc.Files[i].ID)
		}
	}

	// 输出Action信息
	if opt.FnStreamPreview != nil {
		callback.PreviewFackStream(opt.FnStreamPreview, &callback.PreviewMessage{
			Type:      callback.TypeInline,
			From:      callback.FromRetrievalDocAction,
			NodeID:    opt.MessageNodeID,
			Name:      opt.Name,
			Timestamp: time.Now().UnixMilli(),
			MessageID: random.UniqueID(),
			Content: fmt.Sprintf("召回文档 - 文档ID: %s, 文件ID: %s",
				util.ToJsonString(repos), util.ToJsonString(files)),
		})
	}

	// 调用RAG的召回接口（文件ID列表），获取到相关段落
	logrus.WithField(logger.KeyCategory, logger.CategoryCore).
		WithField(logger.KeyEXT, opt.EXT).
		Infof("doc-chain: retrieve knowledge: %s", util.ToJsonString(files))
	chunks, err := endpoint.RAGRetrieve(
		endpoint.RAGRetrieveParam{
			Query:  query,
			Size:   c.kb.Count,
			Labels: files,
		},
		endpoint.WithNodeID(opt.NodeID),
		endpoint.WithEXT(opt.EXT),
	)
	if err != nil {
		return "", err
	}

	// 拼提示词（相关段落），给出答案
	for i, chunk := range chunks.MixRetrieveResults {
		ret.WriteString(fmt.Sprintf("片段%d:\n%s\n",
			i, chunk.ChunkContent))
	}

	return ret.String(), nil
}
