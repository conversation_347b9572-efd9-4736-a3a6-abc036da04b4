package chain

import (
	"context"
	"secwalk/internal/domain/core/llm/dbapp"
	"secwalk/internal/domain/core/memory"
	"secwalk/internal/domain/core/schema"
	"testing"
	"time"

	nested "github.com/antonfisher/nested-logrus-formatter"
	"github.com/sirupsen/logrus"
	"github.com/stretchr/testify/require"
)

func TestFunctionCall(t *testing.T) {
	logrus.SetFormatter(&nested.Formatter{TimestampFormat: time.RFC3339, HideKeys: false})
	logrus.SetLevel(logrus.DebugLevel)

	model, err := dbapp.NewChatLLM(dbapp.WithAddress("***********:8995"))
	require.NoError(t, err)

	tls := []schema.Tool{}

	chain, err := NewFunctionCallChain(model,
		WithMemory(memory.NewChatMemory()),
		WithTools(tls))
	require.NoError(t, err)

	_, err = Run(context.Background(), chain, "我收到了一条告警信息，里面标注出了关联的域名martin27.xyz和IP104.21.73.89，我需要处理这个告警吗")
	require.NoError(t, err)
}

func TestFunctionCallParallel(t *testing.T) {
	logrus.SetFormatter(&nested.Formatter{TimestampFormat: time.RFC3339, HideKeys: false})
	logrus.SetLevel(logrus.DebugLevel)

	model, err := dbapp.NewChatLLM(dbapp.WithAddress("***********:8995"))
	require.NoError(t, err)

	tls := []schema.Tool{}

	chain, err := NewFunctionCallChain(model,
		WithMemory(memory.NewChatMemory()),
		WithParallel(),
		WithTools(tls))
	require.NoError(t, err)

	_, err = Run(context.Background(), chain, "我收到了一条告警信息，里面标注出了关联的域名martin27.xyz和IP104.21.73.89，我需要处理这个告警吗")
	require.NoError(t, err)
}

func TestFnCallRegexp(t *testing.T) {
	type testCase struct {
		text string
		want []string
	}

	testCases := []testCase{
		{
			text: `✿FUNCTION✿: 域名情报查询
✿ARGS✿: {"domain": "martin27.xyz"}`,
			want: []string{
				"✿FUNCTION✿: 域名情报查询\n✿ARGS✿: {\"domain\": \"martin27.xyz\"}",
				"域名情报查询",
				"{\"domain\": \"martin27.xyz\"}",
			},
		},
		{
			text: `assistant] ✿FUNCTION✿: 域名情报查询
✿ARGS✿: {"domain": "martin27.xyz"}

Detaching and terminating target process`,
			want: []string{
				"✿FUNCTION✿: 域名情报查询\n✿ARGS✿: {\"domain\": \"martin27.xyz\"}",
				"域名情报查询",
				"{\"domain\": \"martin27.xyz\"}",
			},
		},
	}

	for _, tc := range testCases {
		tc := tc
		matches := _FnCallRegex.FindStringSubmatch(tc.text)
		require.EqualValues(t, tc.want, matches)
	}
}
