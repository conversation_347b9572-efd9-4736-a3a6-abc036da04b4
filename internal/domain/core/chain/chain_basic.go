package chain

import (
	"context"
	"fmt"
	"time"

	"secwalk/internal/domain/core/callback"
	"secwalk/internal/domain/core/llm"
	"secwalk/internal/domain/core/memory"
	"secwalk/internal/domain/core/message"
	"secwalk/internal/domain/core/prompt"
	"secwalk/internal/domain/core/schema"
	"secwalk/pkg/random"
)

const _defaultLLMChainTemplate = `{{.input}}`

var _ Chain = &LLMChain{}

type LLMChain struct {
	llm                llm.Model
	systemPromptFormat prompt.MessageFormatter
	userPromptFormat   prompt.MessageFormatter
	prompt             prompt.FormatPrompter
	memory             memory.Memory
}

func NewLLMChain(llm llm.Model, opts ...Option) (Chain, error) {
	opt := newDefaultChainOption()
	for _, o := range opts {
		o(opt)
	}

	c := &LLMChain{
		llm:                llm,
		systemPromptFormat: opt.systemPromptFormat,
		userPromptFormat:   opt.userPromptFormat,
		prompt:             opt.prompt,
		memory:             opt.memory,
	}

	// 设置系统提示词渲染模板
	if opt.systemPromptFormat == nil && len(opt.systemPrompt) > 0 {
		c.systemPromptFormat = prompt.NewSystemMessagePromptTemplate(opt.systemPrompt)
	}

	// 设置用户提示词渲染模板
	if opt.userPromptFormat == nil && len(opt.userPrompt) > 0 {
		c.userPromptFormat = prompt.NewUserMessagePromptTemplate(opt.userPrompt)
	}

	if opt.userPromptFormat == nil && len(opt.userPrompt) == 0 {
		c.userPromptFormat = prompt.NewUserMessagePromptTemplate(_defaultLLMChainTemplate)
	}

	return c, nil
}

func (c LLMChain) Call(ctx context.Context, inputs map[string]any, opts ...schema.CallOption) (string, error) {
	opt := schema.NewCallOptions()
	for _, o := range opts {
		o(opt)
	}

	var pvs message.PromptValue
	var res *llm.Generation
	var err error
	var cid = random.UniqueID()

	// 如果设置了提示词模板，直接使用
	if c.prompt != nil {
		pvs, err = c.prompt.FormatPrompt(inputs)
		if err != nil {
			return "", err
		}
	} else {
		msgs := make([]message.Message, 0)
		// 拼系统提示词
		if c.systemPromptFormat != nil {
			msg, err := c.systemPromptFormat.FormatMessages(inputs)
			if err != nil {
				return "", err
			}
			msgs = append(msgs, msg...)

			if !opt.SetSystemPromptRender {
				for _, msg := range msgs {
					opt.SystemPromptRender = msg.GetContent()
				}
			}
		}

		// 拼接历史记录
		if opt.UseMemory {
			msg, err := c.memory.LoadMemoryMessages(ctx)
			if err != nil {
				return "", err
			}
			msgs = append(msgs, msg...)
		}

		// 拼用户提示词
		if c.userPromptFormat != nil {
			msg, err := c.userPromptFormat.FormatMessages(inputs)
			if err != nil {
				return "", err
			}
			msgs = append(msgs, msg...)

			if !opt.SetUserPromptRender {
				for _, msg := range msgs {
					opt.UserPromptRender = msg.GetContent()
				}
			}
		}

		pvs = prompt.ChatPromptValue(msgs)
	}

	if opt.FnStreamVerbose != nil {
		opt.FnStreamVerbose(&callback.VerboseMessage{
			Type:               callback.TypeLLM,
			NodeID:             opt.MessageNodeID,
			PID:                opt.PID,
			CID:                cid,
			Stage:              callback.StageRun,
			State:              callback.CallStateSuccess,
			Timestamp:          time.Now().UnixMilli(),
			SystemPromptRender: opt.SystemPromptRender,
			UserPromptRender:   opt.UserPromptRender,
			Inputs:             opt.Locals,
		})

		defer func() {
			if err != nil {
				opt.FnStreamVerbose(&callback.VerboseMessage{
					Type:               callback.TypeLLM,
					NodeID:             opt.MessageNodeID,
					PID:                opt.PID,
					CID:                cid,
					Stage:              callback.StageEnd,
					State:              callback.CallStateFailure,
					Error:              err.Error(),
					UserPromptRender:   opt.UserPromptRender,
					SystemPromptRender: opt.SystemPromptRender,
					Timestamp:          time.Now().UnixMilli(),
				})
			} else {
				opt.FnStreamVerbose(&callback.VerboseMessage{
					Type:               callback.TypeLLM,
					NodeID:             opt.MessageNodeID,
					PID:                opt.PID,
					CID:                cid,
					Stage:              callback.StageEnd,
					State:              callback.CallStateSuccess,
					Timestamp:          time.Now().UnixMilli(),
					SystemPromptRender: opt.SystemPromptRender,
					UserPromptRender:   opt.UserPromptRender,
					FinishReason:       string(res.FinishReason),
					Output:             res,
				})
			}
		}()
	}

	// 模型请求实时返回
	if opt.MasterEntry && opt.FnStreamPreview != nil && opt.FnStreaming == nil {
		messageID := random.UniqueID()
		messageID_R1 := random.UniqueID()
		opts = append(opts, schema.WithFnStreaming(func(ctx context.Context, chunk []byte) error {
			from := callback.FromBasicLLM
			if opt.OutputFinal {
				from = callback.FromExecuteResult
			}
			opt.FnStreamPreview(&callback.PreviewMessage{
				Type:      callback.TypeInline,
				From:      from,
				NodeID:    opt.MessageNodeID,
				Name:      opt.Name,
				Timestamp: time.Now().UnixMilli(),
				MessageID: messageID,
				Content:   string(chunk),
			})
			return nil
		}),
			schema.WithFnReasoning(func(ctx context.Context, chunk []byte) error {
				from := callback.FromBasicLLM
				if opt.OutputFinal {
					from = callback.FromExecuteResult
				}

				if ctx.Value(callback.LLMThinkingTypeReasonStart) != nil {
					chunk = append([]byte(callback.ThinkLabelStart), chunk...)
				}

				if ctx.Value(callback.LLMThinkingTypeReasonEnd) != nil {
					chunk = append(chunk, []byte(callback.ThinklabelEnd)...)
				}

				opt.FnStreamPreview(&callback.PreviewMessage{
					Type:      callback.TypeInline,
					From:      from,
					NodeID:    opt.MessageNodeID,
					Name:      opt.Name,
					Timestamp: time.Now().UnixMilli(),
					MessageID: messageID_R1,
					Content:   string(chunk),
				})
				return nil
			}),
		)
	}

	// 调用模型获取结果
	res, err = llm.Call(ctx, c.llm, pvs, opts...)
	if err != nil {
		return "", fmt.Errorf("%w: %v", schema.ErrLLMCall, err)
	}

	if opt.CbSetVariable != nil && opt.LogProbs > 0 {
		opt.CbSetVariable(opt.NodeID, map[string]any{"logprobs": res.LogProbs})
	}

	if opt.CbSetVariable != nil {
		opt.CbSetVariable(opt.NodeID, map[string]any{"reasoning_content": res.ReasoningContent})
	}

	return res.Message.GetContent(), nil
}

func (c LLMChain) GetMemory() memory.Memory {
	return c.memory
}
