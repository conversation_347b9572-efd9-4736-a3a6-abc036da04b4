package chain

import (
	"context"
	"strings"

	"secwalk/internal/domain/core/memory"
	"secwalk/internal/domain/core/schema"
)

type Chain interface {
	Call(ctx context.Context, inputs map[string]any, opts ...schema.CallOption) (string, error)
	GetMemory() memory.Memory
}

type Step struct {
	Task   string // 任务
	Output string // 完整模型输出
	Answer string // 最终答案
	*Action
}

func (s *Step) Trim() {
	s.Answer = strings.TrimSpace(s.Answer)
	s.Action.Trim()
}

type Action struct {
	Thought     string // 思考
	Action      string // 动作（调用工具或智能体）
	ActionInput string // 参数
	Observation string // 观察
}

func (a *Action) Trim() {
	a.Thought = strings.TrimSpace(a.Thought)
	a.Action = strings.TrimSpace(a.Action)
	a.ActionInput = strings.TrimSpace(a.ActionInput)
	a.Observation = strings.TrimSpace(a.Observation)
}

type Finish struct {
	ReturnValues string
	Log          string
}

// 语言链调用入口：标准方式
func Call(ctx context.Context, c Chain, inputValues map[string]any, opts ...schema.CallOption) (string, error) {
	opt := schema.NewCallOptions()
	for _, o := range opts {
		o(opt)
	}

	// 合并用户输入与记忆输入
	allValues := make(map[string]any, 0)
	for key, value := range inputValues {
		allValues[key] = value
	}

	newValues, err := c.GetMemory().LoadMemoryVariables(ctx)
	if err != nil {
		return "", err
	}

	for key, value := range newValues {
		allValues[key] = value
	}

	if opt.LogMemory {
		if err := c.GetMemory().SaveInput(ctx, inputValues); err != nil {
			return "", err
		}
	}

	output, err := c.Call(ctx, allValues, opts...)
	if err != nil {
		return "", err
	}

	if opt.LogMemory {
		if err = c.GetMemory().SaveOutput(ctx, output); err != nil {
			return "", err
		}
	}

	return output, nil
}

// 语言链调用入口：只接受一个字符串吗，输出一个字符串
func Run(ctx context.Context, c Chain, input any, opts ...schema.CallOption) (string, error) {
	return Call(
		ctx,
		c,
		map[string]any{schema.DefaultInputKey: input},
		opts...,
	)
}
