package chain

import (
	"fmt"
	"secwalk/internal/domain/core/schema"
)

const (
	ReactKeyAction      = "Action:"
	ReactKeyActionInput = "Action Input:"
	ReactKeyFinalAnswer = "Final Answer:"

	ReactKeyThought = "Thought:"
)

var (
	_ReactPromptZH = fmt.Sprintf(`您可以使用工具获取新信息，请尽可能使用以下工具回答问题:

[工具及说明]
{{.tool_descs}}

[请遵照以下格式输出]
Question: 你必须要回答的输入问题
Thought: 你应该始终思考需要怎么做才能更好的解答问题
Action: 你选择使用的工具，工具必须是下列选项之一[{{.tool_names}}]
Action Input: 调用工具的输入参数, 参数必须是一个Json字符串, 如果不需要参数输入，请输出{}
Observation: 调用工具的结果。
... (Thought/Action/Action Input/Observation 这样的思考步骤可以重复N次)
Thought: 我现在知道最终答案了。
Final Answer: 最终答案是对原始输入问题的回答。


[现在开始]
Question: {{.input}}
Thought: {{.%s}}`, schema.ReservedScratchpad)

	_ToolDescTemplateZH = "%s: %s 输入参数：%s 此工具的输入应为JSON对象。"
)

var (
	_ReactPromptEN = fmt.Sprintf(`Answer the following questions as best you can. You have access to the following tools:

{{.tool_descs}}

Use the following format:

Question: the input question you must answer
Thought: you should always think about what to do
Action: the action to take, should be one of [{{.tool_names}}]
Action Input: the input to the action
Observation: the result of the action
... (this Thought/Action/Action Input/Observation can be repeated zero or more times)
Thought: I now know the final answer
Final Answer: the final answer to the original input question

Begin!

Question: {{.input}}
Thought: {{.%s}}`, schema.ReservedScratchpad)

	_ToolDescTemplateEN = "%s: %s Parameters: %s Format the arguments as a JSON object."
)
