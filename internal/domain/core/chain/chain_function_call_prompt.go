package chain

import (
	"fmt"
	"regexp"
	"secwalk/internal/domain/core/schema"
)

const (
	_FnName   = `✿FUNCTION✿`
	_FnArgs   = `✿ARGS✿`
	_FnResult = `✿RESULT✿`
	_FnReturn = `✿RETURN✿`
)

var _FnStopWords = []string{_FnResult, _FnReturn}
var _FnCallRegex = regexp.MustCompile(`(?s)✿FUNCTION✿:\s*(.*?)\n✿ARGS✿:\s*({(?:[^{}]|{[^{}]*})*})`)

var (
	_FnCallTemplateInfoZH = `# 工具

## 你拥有如下工具：

{{.tool_descs}}`

	_FnCallTemplateFmtZH = fmt.Sprintf(`## 你可以在回复中插入零次、一次或多次以下命令以调用工具：

%s: 工具名称，必须是[{{.tool_names}}]之一。
%s: 工具输入
%s: 工具结果
%s: 根据工具结果进行回复`,
		_FnName, _FnArgs,
		_FnResult, _FnReturn)

	_FnCallTemplateFmtParaZH = fmt.Sprintf(`## 你可以在回复中插入以下命令以并行调用N个工具：

%s: 工具1的名称，必须是[{{.tool_names}}]之一
%s: 工具1的输入
%s: 工具2的名称
%s: 工具2的输入
...
%s: 工具N的名称
%s: 工具N的输入
%s: 工具1的结果
%s: 工具2的结果
...
%s: 工具N的结果
%s: 根据工具结果进行回复`,
		_FnName, _FnArgs,
		_FnName, _FnArgs,
		_FnName, _FnArgs,
		_FnResult, _FnResult,
		_FnResult, _FnReturn)

	// {human name} {model name} {description} {parameters}
	_FunctionDescTemplateZH = "### %s\n\n%s: %s 输入参数：%s 此工具的输入应为JSON对象。"
)

var (
	_FnCallTemplateInfoEN = `# Tools

## You have access to the following tools:

{{.tool_descs}}`

	_FnCallTemplateFmtEN = fmt.Sprintf(`## When you need to call a tool, please insert the following command in your reply, which can be called zero or multiple times according to your needs:

%s: The tool to use, should be one of [{{.tool_names}}]
%s: The input of the tool
%s: Tool results
%s: Reply based on tool results`,
		_FnName, _FnArgs,
		_FnResult, _FnReturn)

	_FnCallTemplateFmtParaEN = fmt.Sprintf(`## Insert the following command in your reply when you need to call N tools in parallel:

%s: The name of tool 1, should be one of [{{.tool_names}}]
%s: The input of tool 1
%s: The name of tool 2
%s: The input of tool 2
...
%s: The name of tool N
%s: The input of tool N
%s: The result of tool 1
%s: The result of tool 2
...
%s: The result of tool N
%s: Reply based on tool results`,
		_FnName, _FnArgs,
		_FnName, _FnArgs,
		_FnName, _FnArgs,
		_FnResult, _FnResult,
		_FnResult, _FnReturn)

	// {human name} {model name} {description} {parameters}
	_FunctionDescTemplateEN = "### %s\n\n%s: %s Parameters: %s Format the arguments as a JSON object."
)

var (
	_FnCallDefaultUser       = "{{.input}}"
	_FnCallDefaultScratchPad = fmt.Sprintf("{{.%s}}", schema.ReservedScratchpad)
)
