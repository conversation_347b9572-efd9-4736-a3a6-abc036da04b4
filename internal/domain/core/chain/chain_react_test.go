package chain

import (
	"context"
	"secwalk/internal/domain/core/llm/dbapp"
	"secwalk/internal/domain/core/memory"
	"secwalk/internal/domain/core/schema"
	"strings"
	"testing"
	"time"

	nested "github.com/antonfisher/nested-logrus-formatter"
	"github.com/sirupsen/logrus"
	"github.com/stretchr/testify/require"
)

func TestReActThought(t *testing.T) {
	logrus.SetFormatter(&nested.Formatter{TimestampFormat: time.RFC3339, HideKeys: false})
	logrus.SetLevel(logrus.DebugLevel)

	model, err := dbapp.NewChatLLM(dbapp.WithAddress("***********:8995"))
	require.NoError(t, err)

	chain, err := NewReActChain(model,
		WithMemory(memory.NewChatMemory()),
		WithTools([]schema.Tool{}))
	require.NoError(t, err)

	_, err = Run(context.Background(), chain, "Hi! my name is <PERSON> and the year I was born is 1987")
	require.NoError(t, err)

	out, err := Run(context.Background(), chain, "What is the year I was born times 34")
	require.NoError(t, err)
	require.True(t, strings.Contains(out, "67558"))
}
