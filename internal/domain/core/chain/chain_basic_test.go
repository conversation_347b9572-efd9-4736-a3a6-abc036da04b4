package chain

import (
	"context"
	"fmt"
	"strings"
	"testing"

	"secwalk/internal/domain/core/llm/dbapp"
	"secwalk/internal/domain/core/memory"
	"secwalk/internal/domain/core/prompt"
	"secwalk/internal/domain/core/schema"

	"github.com/stretchr/testify/require"
)

func TestLLMChain(t *testing.T) {
	t.Parallel()

	model, err := dbapp.NewChatLLM(dbapp.WithAddress("***********:8995"))
	require.NoError(t, err)

	chain, err := NewLLMChain(model, WithMemory(memory.NewChatMemory()))
	require.NoError(t, err)

	res, err := Run(context.Background(), chain, "Hi! I'm Jim",
		schema.WithMaxTokens(1024),
	)
	require.NoError(t, err)
	fmt.Println(res)

	res, err = Run(context.Background(), chain, "What is my name?",
		schema.WithFnStreaming(func(ctx context.Context, chunk []byte) error {
			fmt.Println("chunk:", string(chunk))
			return nil
		}),
	)
	require.NoError(t, err)
	require.True(t, strings.Contains(res, "Jim"), `result does not contain the keyword 'Jim'`)
}

func TestLLMChainStream(t *testing.T) {
	t.Parallel()

	model, err := dbapp.NewChatLLM(dbapp.WithAddress("***********:8995"))
	require.NoError(t, err)

	chain, err := NewLLMChain(model)
	require.NoError(t, err)

	_, err = Run(context.Background(), chain, "Hi! I'm Jim",
		schema.WithMaxTokens(1024),
		schema.WithFnStreaming(func(ctx context.Context, chunk []byte) error {
			fmt.Println("chunk:", string(chunk))
			return nil
		}),
	)
	require.NoError(t, err)

	res, err := Run(context.Background(), chain, "What is my name?",
		schema.WithFnStreaming(func(ctx context.Context, chunk []byte) error {
			fmt.Println("chunk:", string(chunk))
			return nil
		}),
	)
	require.NoError(t, err)
	require.True(t, strings.Contains(res, "Jim"), `result does not contain the keyword 'Jim'`)
}

func TestLLMChainWithPromptTemplate(t *testing.T) {
	t.Parallel()

	model, err := dbapp.NewChatLLM(dbapp.WithAddress("***********:8995"))
	require.NoError(t, err)

	prompt := prompt.NewPromptTemplate(
		"What is the capital of {{.country}}", prompt.WithInputKeys("country"))

	chain, err := NewLLMChain(model, WithPrompt(prompt))
	require.NoError(t, err)

	res, err := Call(context.Background(), chain,
		map[string]any{"country": "France"})
	require.NoError(t, err)
	require.True(t, strings.Contains(res, "Paris"))
}

func TestLLMChainWithChatPromptTemplate(t *testing.T) {
	t.Parallel()

	model, err := dbapp.NewChatLLM(dbapp.WithAddress("***********:8995"))
	require.NoError(t, err)

	prompt := prompt.NewChatPromptTemplate(
		[]prompt.MessageFormatter{
			prompt.NewSystemMessagePromptTemplate("you are AI assistant"),
			prompt.NewUserMessagePromptTemplate("What is the capital of {{.country}}", prompt.WithInputKeys("country")),
		},
	)

	chain, err := NewLLMChain(model, WithPrompt(prompt))
	require.NoError(t, err)

	res, err := Call(context.Background(), chain,
		map[string]any{"country": "France"})
	require.NoError(t, err)
	require.True(t, strings.Contains(res, "Paris"))
}

func TestLLMChainPromptTemplate(t *testing.T) {
	t.Parallel()

	model, err := dbapp.NewChatLLM(dbapp.WithAddress("***********:8995"))
	require.NoError(t, err)

	chain, err := NewLLMChain(model, WithUserPrompt("You are helpful assistant.\n\n[history]:\n{{.history}}\n\n[question]:\n{{.input}}"))
	require.NoError(t, err)

	_, err = Run(context.Background(), chain, "Hi! I'm Jim",
		schema.WithMaxTokens(1024),
	)
	require.NoError(t, err)

	res, err := Run(context.Background(), chain, "What is my name?")
	require.NoError(t, err)
	require.True(t, strings.Contains(res, "Jim"), `result does not contain the keyword 'Jim'`)
}
