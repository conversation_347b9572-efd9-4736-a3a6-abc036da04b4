package chain

import (
	"context"
	"secwalk/internal/domain/core/llm/dbapp"
	"secwalk/internal/domain/core/schema"
	"secwalk/internal/domain/core/sqloader"
	"testing"
	"time"

	nested "github.com/antonfisher/nested-logrus-formatter"
	"github.com/sirupsen/logrus"
	"github.com/stretchr/testify/require"
)

func TestMysqlChain(t *testing.T) {
	logrus.SetFormatter(&nested.Formatter{TimestampFormat: time.RFC3339, HideKeys: false})
	logrus.SetLevel(logrus.DebugLevel)

	model, err := dbapp.NewChatLLM(dbapp.WithAddress("***********:8995"))
	require.NoError(t, err)

	chain, err := NewSQLChain(model, WithDB(&schema.DB{
		Type: sqloader.DialectMysql,
		DSN:  "root:J7aXgk2BJUj=@tcp(*************)/whales",
		Tables: []sqloader.Table{
			{
				Name: "machines",
				Fields: []sqloader.Field{
					{
						Name: "id",
					},
					{
						Name:    "lable",
						Comment: "机器标签名称",
					},
				},
			},
			{
				Name: "missions",
			},
		},
	}))
	require.NoError(t, err)

	_, err = Call(
		context.Background(),
		chain,
		map[string]any{"input": "有多少台机器"},
	)
	require.NoError(t, err)
}

func TestSqlite3Chain(t *testing.T) {
	logrus.SetFormatter(&nested.Formatter{TimestampFormat: time.RFC3339, HideKeys: false})
	logrus.SetLevel(logrus.DebugLevel)

	model, err := dbapp.NewChatLLM(dbapp.WithAddress("***********:8995"))
	require.NoError(t, err)

	chain, err := NewSQLChain(model, WithDB(&schema.DB{
		Type: sqloader.DialectSqlite3,
		DSN:  "C:/Users/<USER>/Desktop/SQLITEDB/SQLITEDB.db",
		Tables: []sqloader.Table{
			{Name: "spiders"},
			{
				Name: "decryptions",
				Fields: []sqloader.Field{
					{
						Name:    "family",
						Comment: "家族",
					},
					{
						Name:    "decrypted_lnk",
						Comment: "解密链接",
					},
				},
			},
		},
	}))
	require.NoError(t, err)

	_, err = Call(
		context.Background(),
		chain,
		map[string]any{"input": "Wannacry能够解密吗"},
	)
	require.NoError(t, err)
}

func TestDoris(t *testing.T) {
	logrus.SetFormatter(&nested.Formatter{TimestampFormat: time.RFC3339, HideKeys: false})
	logrus.SetLevel(logrus.DebugLevel)

	model, err := dbapp.NewChatLLM(dbapp.WithAddress("************:8995"))
	require.NoError(t, err)

	chain, err := NewSQLChain(model, WithOnce(), WithDB(&schema.DB{
		Type: sqloader.DialectMysql,
		DSN:  "root:@tcp(*********:9030)/ailpha",
		Tables: []sqloader.Table{
			{
				Name: "ailpha_securityalarm",
				Fields: []sqloader.Field{
					{Name: "eventId", Comment: "事件ID"},
					{Name: "srcAddress", Comment: "来源IP"},
					{Name: "destAddress", Comment: "目的IP"},
					{Name: "alarmName", Comment: "告警名称"},
					{Name: "threatSeverity", Comment: "安全告警威胁等级:低危(Low)/中危(Medium)/高危(High)"},
					{Name: "startTime", Comment: "起始时间"},
					{Name: "alarmResults", Comment: "告警结果"},
					{Name: "appProtocol", Comment: "应用协议"},
					{Name: "subCategory", Comment: "告警子类型"},
					{Name: "payload", Comment: "攻击报文"},
					{Name: "attacker", Comment: "攻击者"},
					{Name: "victim", Comment: "受害者"},
					{Name: "srcPort", Comment: "来源端口"},
					{Name: "destPort", Comment: "目的端口"},
					{Name: "describe", Comment: "告警描述"},
					{Name: "direction", Comment: "通过来源IP、目的IP及内部IP配置判断数据流方向，取值：00、01、10、11，分别对应（内访问内、内访问外、外访问内、外访问外）"},
				},
			},
			// {
			// 	Name: "ailpha_securitylog_log",
			// 	Fields: []sqloader.Field{
			// 		{Name: "eventId", Comment: "事件ID"},
			// 		{Name: "srcAddress", Comment: "来源IP"},
			// 		{Name: "destAddress", Comment: "目的IP"},
			// 		{Name: "name", Comment: "事件名称"},
			// 		{Name: "severity", Comment: "安全日志威胁等级"},
			// 		{Name: "startTime", Comment: "起始时间"},
			// 		{Name: "catBehavior", Comment: "事件行为分类"},
			// 		{Name: "appProtocol", Comment: "应用协议"},
			// 		{Name: "payload", Comment: "攻击报文"},
			// 		{Name: "attackerAddress", Comment: "攻击者IP"},
			// 		{Name: "victimAddress", Comment: "受害者IP"},
			// 		{Name: "srcPort", Comment: "来源端口"},
			// 		{Name: "destPort", Comment: "目的端口"},
			// 		{Name: "message", Comment: "事件消息"},
			// 		{Name: "direction", Comment: "数据流方向"},
			// 	},
			// },
		},
	}))
	require.NoError(t, err)

	_, err = Call(
		context.Background(),
		chain,
		map[string]any{"input": "最近外部有哪些威胁"},
	)
	require.NoError(t, err)
}
