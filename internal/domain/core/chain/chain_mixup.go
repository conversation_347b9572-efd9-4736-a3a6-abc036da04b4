package chain

import (
	"context"
	"fmt"
	"secwalk/internal/domain/core/endpoint"
	"secwalk/internal/domain/core/llm"
	"secwalk/internal/domain/core/schema"
)

func NewMixupChain(llm llm.Model, opts ...Option) (Chain, error) {
	opt := newDefaultChainOption()
	for _, option := range opts {
		option(opt)
	}

	if opt.db != nil {
		var desc = "该工具可以检索数据库，包含数据表: "
		for _, tb := range opt.db.Tables {
			desc += fmt.Sprintf("%s；", tb.Description)
		}

		sqlchain, err := NewSQLChain(llm, opts...)
		if err != nil {
			return nil, err
		}

		sqltool := ToolChain{
			cfg: schema.ToolConfig{
				Name:        "数据检索工具",
				Description: desc,
				InputParameters: []schema.Parameter{
					{
						Name:        "input",
						Description: "用户的问题",
						Type:        schema.TypeString,
						Configed:    false,
						Required:    true,
					},
				},
			},
			chain: sqlchain,
		}

		opts = append(opts, WithTool(&sqltool))
	}

	if opt.kb != nil {
		var desc = "该工具可以检索以下知识库: "
		for _, kb := range opt.kb.Repos {
			if len(kb.Description) > 0 {
				desc += fmt.Sprintf("%s；", kb.Description)
			} else {
				doc, err := endpoint.KnowledgeSearch(kb.ID)
				if err != nil {
					return nil, err
				}
				desc += fmt.Sprintf("%s %s；", doc.Name, doc.Description)
			}
		}

		docchain, err := NewDocChain(llm, append(opts, WithSystemPrompt(""), WithUserPrompt(""))...)
		if err != nil {
			return nil, err
		}

		doctool := ToolChain{
			cfg: schema.ToolConfig{
				Name:        "知识检索工具",
				Description: desc,
				InputParameters: []schema.Parameter{
					{
						Name:        "input",
						Description: "用户的问题",
						Type:        schema.TypeString,
						Configed:    false,
						Required:    true,
					},
				},
			},
			chain: docchain,
		}

		opts = append(opts, WithTool(&doctool))
	}

	if len(opt.tools) > 0 || len(opt.agents) > 0 || opt.db != nil || opt.kb != nil {
		if opt.cotVersion == schema.COTv2 {
			return NewFunctionCallChain(llm, opts...)
		}
		return NewReActChain(llm, opts...)
	}

	return NewLLMChain(llm, opts...)
}

type ToolChain struct {
	cfg   schema.ToolConfig
	chain Chain
}

func (t *ToolChain) Config() schema.ToolConfig {
	return t.cfg
}

func (t *ToolChain) Call(ctx context.Context, inputs map[string]any,
	opts ...schema.CallOption) (string, error) {
	return t.chain.Call(ctx, inputs, opts...)
}
