package chain

import (
	"context"
	"encoding/json"
	"fmt"
	"regexp"
	"strings"
	"time"

	"secwalk/internal/domain/core/callback"
	"secwalk/internal/domain/core/llm"
	"secwalk/internal/domain/core/memory"
	"secwalk/internal/domain/core/message"
	"secwalk/internal/domain/core/prompt"
	"secwalk/internal/domain/core/schema"
	"secwalk/internal/domain/core/template"
	"secwalk/pkg/logger"
	"secwalk/pkg/random"
	"secwalk/pkg/util"

	"github.com/sirupsen/logrus"
)

var _ Chain = &ReActChain{}

type ReActChain struct {
	llm           llm.Model
	systemPrompt  string
	userPrompt    string
	tools         []schema.Tool
	agents        []schema.Agent
	memory        memory.Memory
	maxIterations int
	once          bool
	noRepeat      bool
	noAction      bool
	hasZH         bool
}

func NewReActChain(llm llm.Model, opts ...Option) (Chain, error) {
	opt := newDefaultChainOption()
	for _, option := range opts {
		option(opt)
	}

	return &ReActChain{
		llm:           llm,
		systemPrompt:  opt.systemPrompt,
		userPrompt:    opt.userPrompt,
		tools:         opt.tools,
		agents:        opt.agents,
		memory:        opt.memory,
		maxIterations: opt.maxIterations,
		once:          opt.once,
		noRepeat:      opt.noRepeat,
		noAction:      opt.noAction,
	}, nil
}

func (c *ReActChain) Call(ctx context.Context, inputs map[string]any, opts ...schema.CallOption) (string, error) {
	var err error
	var steps = make([]Step, 0)
	var finish string

	// 检查输出是否带有中文
	input, ok := inputs[schema.DefaultInputKey].(string)
	if !ok {
		input = util.ToPureJsonString(inputs[schema.DefaultInputKey])
	}
	c.hasZH = util.HasZH(input)

	// 根据设置的最大迭代次数，反复调用模型进行思考与工具调用，直到产生答案
	for i := 0; i < c.maxIterations; i++ {
		finish, err = c.doIteration(ctx, &steps, inputs, opts...)
		if len(finish) > 0 || err != nil {
			return finish, err
		}
	}

	return "", ErrNotFinished
}

// 使用MRKL模板思维模板，让模型思考得到解决问题的动作或最终答案
func (c *ReActChain) doIteration(ctx context.Context, steps *[]Step, inputs map[string]any, opts ...schema.CallOption) (string, error) {
	opt := schema.NewCallOptions()
	for _, o := range opts {
		o(opt)
	}

	// 实时创建链，为了刷新可用工具
	createOpts := make([]Option, 0)
	createOpts = append(createOpts, WithMemory(c.memory))

	var sysRender, userRender string

	// 设置系统提示词部分
	if len(c.systemPrompt) > 0 {
		createOpts = append(createOpts, WithSystemPromptTmpl(prompt.NewSystemMessagePromptTemplate(c.systemPrompt)))
		sysRender, _ = template.RenderTemplate(c.systemPrompt, inputs)
	}

	// 设置用户提示词部分
	var defaultUserPrompt string
	if c.hasZH {
		defaultUserPrompt = _ReactPromptZH
	} else {
		defaultUserPrompt = _ReactPromptEN
	}
	userPrompt := defaultUserPrompt
	if len(c.userPrompt) > 0 {
		userPrompt = fmt.Sprintf("%s\n\n%s", c.userPrompt, defaultUserPrompt)
		userRender, _ = template.RenderTemplate(c.userPrompt, inputs)
	}
	createOpts = append(createOpts, WithUserPromptTmpl(prompt.NewUserMessagePromptTemplate(
		userPrompt,
		prompt.WithInputKeys(schema.DefaultInputKey, schema.ReservedScratchpad),
		prompt.WithPartialValue("tool_names", c.getToolNames()),
		prompt.WithPartialValue("tool_descs", c.getToolDescs()),
	)))

	chain, err := NewLLMChain(c.llm, createOpts...)
	if err != nil {
		return "", err
	}

	// 合并思考步骤
	inputs[schema.ReservedScratchpad] = c.constructScratchPad(*steps)
	output := ""
	keyword := ""
	prefixs := ""
	subkey := false
	step := &Step{Action: &Action{}}
	messageID := random.UniqueID()

	// 调用模型链
	output, err = chain.Call(
		ctx,
		inputs,
		append(opts,
			schema.WithStopWords([]string{"\nObservation:", "\n\tObservation:"}),
			schema.WithFnStreamPreview(nil),
			schema.WithSystemPromptRender(sysRender),
			schema.WithUserPromptRender(userRender),
			schema.WithFnStreaming(func(ctx context.Context, chunk []byte) error {
				if string(chunk) == callback.EOFMARK {
					return nil
				}

				if opt.TokenChecker != nil {
					word, sub, ok := opt.TokenChecker.Check(string(chunk))
					if sub {
						subkey = true
					} else {
						subkey = false
					}
					if ok {
						keyword = word
					}
				}

				prefixs += string(chunk)

				if subkey {
					return nil
				}

				switch keyword {
				case ReactKeyAction:
					index := strings.Index(prefixs, ReactKeyAction)
					if index > -1 {
						step.Action.Thought = strings.TrimPrefix(prefixs[:index], ReactKeyThought)
						// 输出思考信息
						logrus.WithField(logger.KeyCategory, logger.CategoryCore).
							WithField(logger.KeyEXT, opt.EXT).
							Infof("react think: %s", step.Thought)

						if opt.FnStreamPreview != nil {
							callback.PreviewFackStream(opt.FnStreamPreview, &callback.PreviewMessage{
								Type:      callback.TypeInline,
								From:      callback.FromReactThink,
								NodeID:    opt.MessageNodeID,
								Name:      opt.Name,
								Timestamp: time.Now().UnixMilli(),
								MessageID: random.UniqueID(),
								Content:   step.Thought,
							})
						}

						step.Action.Action = prefixs[index+len(ReactKeyAction):]
					} else {
						step.Action.Action += prefixs
					}

					prefixs = ""
				case ReactKeyActionInput:
					index := strings.Index(prefixs, ReactKeyActionInput)
					if index > -1 {
						step.Action.Action += prefixs[:index]
						step.Action.ActionInput = prefixs[index+len(ReactKeyActionInput):]
					} else {
						step.Action.ActionInput += prefixs
					}

					prefixs = ""
				case ReactKeyFinalAnswer:
					index := strings.Index(prefixs, ReactKeyFinalAnswer)
					if index > -1 {
						step.Action.Thought = prefixs[:index]
						// 输出思考信息
						logrus.WithField(logger.KeyCategory, logger.CategoryCore).
							WithField(logger.KeyEXT, opt.EXT).
							Infof("react think: %s", step.Thought)

						if opt.FnStreamPreview != nil {
							callback.PreviewFackStream(opt.FnStreamPreview, &callback.PreviewMessage{
								Type:      callback.TypeInline,
								From:      callback.FromReactThink,
								NodeID:    opt.MessageNodeID,
								Name:      opt.Name,
								Timestamp: time.Now().UnixMilli(),
								MessageID: random.UniqueID(),
								Content:   step.Thought,
							})
						}

						prefixs = prefixs[index+len(ReactKeyFinalAnswer):]
						step.Answer = prefixs
					} else {
						step.Answer += prefixs
					}

					// 真流式返回结果
					if opt.MasterEntry && opt.FnStreamPreview != nil {
						from := callback.FromReactAnswer
						if opt.OutputFinal {
							from = callback.FromExecuteResult
						}
						opt.FnStreamPreview(&callback.PreviewMessage{
							Type:      callback.TypeInline,
							From:      from,
							NodeID:    opt.MessageNodeID,
							Name:      opt.Name,
							Timestamp: time.Now().UnixMilli(),
							MessageID: messageID,
							Content:   prefixs,
						})
					}

					prefixs = ""
				}

				return nil
			}),
		)...,
	)
	if err != nil {
		return "", err
	}

	// 没有思考和动作，作为最终结果
	if len(prefixs) > 0 && len(step.Answer) == 0 {
		step.Answer = prefixs
		step.Output = prefixs
		if opt.FnStreamPreview != nil {
			from := callback.FromReactAnswer
			if opt.OutputFinal {
				from = callback.FromExecuteResult
			}

			callback.PreviewFackStream(opt.FnStreamPreview, &callback.PreviewMessage{
				Type:      callback.TypeInline,
				From:      from,
				NodeID:    opt.MessageNodeID,
				Name:      opt.Name,
				Timestamp: time.Now().UnixMilli(),
				MessageID: random.UniqueID(),
				Content:   prefixs,
			})
		}
	} else {
		step.Output = output
		step.Trim()
	}

	if opt.LogMemory {
		c.memory.SaveMessage(ctx, message.NewAssistantChatMessage(
			fmt.Sprintf("Thought: %s", step.Thought)))
	}

	// 存在结果则思考结束返回
	if len(step.Answer) > 0 {
		if opt.MasterEntry {
			logrus.WithField(logger.KeyCategory, logger.CategoryCore).
				WithField(logger.KeyEXT, opt.EXT).
				Infof("react answer: %s", step.Answer)

			if opt.LogMemory {
				c.memory.SaveMessage(ctx, message.NewAssistantChatMessage(
					fmt.Sprintf("Final Answer: %s", step.Answer)))
			}
		}

		return step.Answer, nil
	}

	actionStr := fmt.Sprintf("%s - %s", step.Action.Action, step.ActionInput)
	if c.noAction {
		if opt.MasterEntry {
			logrus.WithField(logger.KeyCategory, logger.CategoryCore).
				WithField(logger.KeyEXT, opt.EXT).
				Infof("react answer: %s", actionStr)

			if opt.LogMemory {
				c.memory.SaveMessage(ctx, message.NewAssistantChatMessage(
					fmt.Sprintf("Final Answer: %s", actionStr)))
			}

			if opt.FnStreamPreview != nil {
				from := callback.FromReactAnswer
				if opt.OutputFinal {
					from = callback.FromExecuteResult
				}

				callback.PreviewFackStream(opt.FnStreamPreview, &callback.PreviewMessage{
					Type:      callback.TypeInline,
					From:      from,
					NodeID:    opt.MessageNodeID,
					Name:      opt.Name,
					Timestamp: time.Now().UnixMilli(),
					MessageID: random.UniqueID(),
					Content:   actionStr,
				})
			}
		}

		return actionStr, nil
	}

	// 输出动作调用
	logrus.WithField(logger.KeyCategory, logger.CategoryCore).
		WithField(logger.KeyEXT, opt.EXT).
		Infof("react action: %s", actionStr)

	if opt.LogMemory {
		c.memory.SaveMessage(ctx, message.NewAssistantChatMessage(
			fmt.Sprintf("Action Input: %s - %s", step.Action.Action, step.Action.ActionInput)))
	}

	if opt.FnStreamPreview != nil {
		callback.PreviewFackStream(opt.FnStreamPreview, &callback.PreviewMessage{
			Type:      callback.TypeInline,
			From:      callback.FromReactAction,
			NodeID:    opt.MessageNodeID,
			Name:      opt.Name,
			Timestamp: time.Now().UnixMilli(),
			MessageID: random.UniqueID(),
			Content:   actionStr,
		})
	}

	// 调用动作
	step.Observation = c.doAction(ctx, step.Action.Action, step.ActionInput, inputs, append(opts, schema.WithFinalOut(false))...)

	// 输出动作结果
	logrus.WithField(logger.KeyCategory, logger.CategoryCore).
		WithField(logger.KeyEXT, opt.EXT).
		Infof("react action observe: %s", step.Observation)

	if opt.LogMemory {
		c.memory.SaveMessage(ctx, message.NewAssistantChatMessage(
			fmt.Sprintf("Action Output: %s", step.Observation),
		))
	}

	if opt.FnStreamPreview != nil {
		opt.FnStreamPreview(&callback.PreviewMessage{
			Type:      callback.TypeInline,
			From:      callback.FromReactObserve,
			NodeID:    opt.MessageNodeID,
			Name:      opt.Name,
			Timestamp: time.Now().UnixMilli(),
			MessageID: random.UniqueID(),
			Content:   step.Observation,
		})
	}

	// 工具模型链直接返回
	if c.once {
		return step.Observation, nil
	}

	*steps = append(*steps, *step)
	return "", nil
}

func (c *ReActChain) GetMemory() memory.Memory {
	return c.memory
}

func (c *ReActChain) getToolNames() string {
	list := make([]string, 0)
	for _, v := range c.tools {
		list = append(list, v.Config().Name)
	}
	for _, v := range c.agents {
		list = append(list, v.Config().Name)
	}
	return strings.Join(list, ",")
}

func (c *ReActChain) getToolDescs() string {
	type Param struct {
		Name        string `json:"name"`
		Type        string `json:"type"`
		Description string `json:"description"`
		Required    bool   `json:"required"`
	}

	var format string
	if c.hasZH {
		format = _ToolDescTemplateZH
	} else {
		format = _ToolDescTemplateEN
	}

	list := make([]string, 0)
	for _, v := range c.tools {
		params := make([]Param, 0)
		for _, vv := range v.Config().InputParameters {
			if vv.Configed {
				continue
			}
			params = append(params, Param{
				Name:        vv.Name,
				Type:        vv.Type,
				Description: vv.Description,
				Required:    vv.Required,
			})
		}
		list = append(list, fmt.Sprintf(format, v.Config().Name,
			v.Config().Description, util.ToPureJsonString(params),
		))
	}
	for _, v := range c.agents {
		params := make([]Param, 0)
		for _, vv := range v.Config().InputParameters {
			if vv.Configed {
				continue
			}
			params = append(params, Param{
				Name:        vv.Name,
				Type:        vv.Type,
				Description: vv.Description,
				Required:    vv.Required,
			})
		}
		list = append(list, fmt.Sprintf(format, v.Config().Name,
			v.Config().Description, util.ToPureJsonString(params),
		))
	}
	return strings.Join(list, "\n")
}

// 拼接中间思考步骤
func (c *ReActChain) constructScratchPad(steps []Step) string {
	var scratchPad string
	if len(steps) > 0 {
		for _, step := range steps {
			scratchPad += step.Output

			// 限制10k长度
			if len(step.Observation) > 30000 {
				scratchPad += "\nObservation: " + step.Observation[:30000]
			} else {
				scratchPad += "\nObservation: " + step.Observation
			}
		}
		scratchPad += "\nThought:"
	}

	return scratchPad
}

// 动作调用
func (c *ReActChain) doAction(ctx context.Context, action, actionInput string, inputs map[string]any, opts ...schema.CallOption) string {
	opt := schema.NewCallOptions()
	for _, o := range opts {
		o(opt)
	}

	r := regexp.MustCompile(`(?s){.*}`)
	matches := r.FindStringSubmatch(actionInput)
	if len(matches) > 0 {
		actionInput = matches[0]
	}

	var params = make(map[string]any)
	// 解析工具参数
	if err := json.Unmarshal([]byte(actionInput), &params); err != nil {
		return "tool params is not a Json map, try regenerate"
	}
	for k, v := range params {
		inputs[k] = v
	}

	// 获取工具实例
	tool, ok := schema.NameToTool(c.tools)[strings.ToUpper(action)]
	if ok {
		// 调用工具
		ob, err := schema.ToolCall(ctx, tool, inputs, append(opts, schema.WithLocals(params))...)
		// 调用过的工具直接移除，避免重复调用
		if c.noRepeat {
			for i, v := range c.tools {
				if tool.Config().ID == v.Config().ID {
					c.tools = append(c.tools[:i], c.tools[i+1:]...)
					break
				}
			}
		}

		if err != nil {
			return fmt.Sprintf("tool call error: %s", err.Error())
		} else {
			return ob
		}
	}

	agent, ok := schema.NameToAgent(c.agents)[strings.ToUpper(action)]
	if ok {
		res, err := agent.Call(ctx, params, opts...)

		// 调用过的智能体直接移除，避免重复调用
		if c.noRepeat {
			for i, v := range c.agents {
				if agent.Config().ID == v.Config().ID {
					c.agents = append(c.agents[:i], c.agents[i+1:]...)
					break
				}
			}
		}

		if err != nil {
			return fmt.Sprintf("assistant call error: %s", err.Error())
		} else {
			if opt.CbSetVariable != nil {
				opt.CbSetVariable(opt.NodeID, res.Results)
			}
			return res.Session.Messages[len(res.Session.Messages)-1].Content
		}
	}

	return fmt.Sprintf("%s is not a valid tool or assistant, try another one", action)
}
