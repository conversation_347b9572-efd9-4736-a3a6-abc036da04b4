package chain

import (
	"context"
	"secwalk/internal/domain/core/callback"
	"secwalk/internal/domain/core/llm"
	"secwalk/internal/domain/core/memory"
	"secwalk/internal/domain/core/prompt"
	"secwalk/internal/domain/core/schema"
	"secwalk/internal/domain/core/sqloader/database"
	"secwalk/pkg/logger"
	"secwalk/pkg/random"
	"time"

	"github.com/sirupsen/logrus"
)

const _defaultSQLTemplate2 = `### Task
Generate a {{.dialect}} query to answer [QUESTION]{{.input}}[/QUESTION]

### Instructions
-  Never query for all the columns from a specific table, only ask for a the few relevant columns given the question.
-  Unless the user specifies in his question a specific number of examples he wishes to obtain, always limit your query to at most {{.top_k}} results.
-  You need to wrap it with backticks to avoid conflicts with the MySQL keyword.
-  You must use this format generate answer: [SQL]answer[/SQL]

### Database Schema
The query will run on a database with the following schema:
{{.table_info}}

### Answer
Given the database schema, here is the SQL query that answers [QUESTION]{{.input}}[/QUESTION]
[SQL]`

const _defaultSQLCompile = `### Task
Answer questions based on the data retrieved from the database and your own knowledge

### Answer
[QUESTION]{{.input}}[/QUESTION]
[SQL]{{.sqlQuery}}[/SQL]
[SQL RESULT]{{.sqlResult}}[/SQL RESULT]
[FINAL ANSWER]
`

type SQLChain struct {
	sqlchain Chain
	reschain Chain
	sqloader *database.Loader
	noAction bool
}

func NewSQLChain(llm llm.Model, opts ...Option) (Chain, error) {
	opt := newDefaultChainOption()
	for _, option := range opts {
		option(opt)
	}

	sqloader, err := database.NewLoader(opt.db.Type, opt.db.DSN, opt.db.Tables)
	if err != nil {
		return nil, err
	}

	sqlchain, err := NewLLMChain(
		llm,
		WithPrompt(
			prompt.NewPromptTemplate(
				_defaultSQLTemplate2,
				prompt.WithInputKeys("dialect", "top_k", "table_info", "input"),
			),
		),
	)
	if err != nil {
		return nil, err
	}

	reschain, err := NewLLMChain(
		llm,
		WithPrompt(
			prompt.NewPromptTemplate(
				_defaultSQLCompile,
				prompt.WithInputKeys("sqlQuery", "sqlResult", "input"),
			),
		),
	)
	if err != nil {
		return nil, err
	}

	return &SQLChain{
		sqlchain: sqlchain,
		reschain: reschain,
		sqloader: sqloader,
		noAction: opt.noAction,
	}, nil
}

const (
	sqlStop   = "[/SQL]"
	finalStop = "[/FINAL ANSWER]"
)

func (c *SQLChain) Call(ctx context.Context, inputs map[string]any, opts ...schema.CallOption) (string, error) {
	opt := schema.NewCallOptions()
	for _, o := range opts {
		o(opt)
	}

	tableInfos, err := c.sqloader.TableInfos(ctx)
	if err != nil {
		return "", err
	}

	inputs["top_k"] = c.sqloader.TopK()
	inputs["dialect"] = c.sqloader.Dialect()
	inputs["table_info"] = tableInfos

	// 生成SQL语句
	logrus.WithField(logger.KeyCategory, logger.CategoryCore).
		WithField(logger.KeyEXT, opt.EXT).
		Info("sql-chain: sql generate")

	callOpts := append(opts, schema.WithStopWords([]string{sqlStop}))
	if opt.FnStreamPreview != nil {
		messageID := random.UniqueID()
		callOpts = append(callOpts,
			schema.WithFnStreaming(func(ctx context.Context, chunk []byte) error {
				opt.FnStreamPreview(&callback.PreviewMessage{
					Type:      callback.TypeInline,
					From:      callback.FromRetrievalSqlGenerate,
					NodeID:    opt.MessageNodeID,
					Name:      opt.Name,
					Timestamp: time.Now().UnixMilli(),
					MessageID: messageID,
					Content:   string(chunk),
				})
				return nil
			}))
	}
	sqlQuery, err := c.sqlchain.Call(ctx, inputs, callOpts...)
	if err != nil {
		return "", err
	}

	if c.noAction {
		return sqlQuery, nil
	}

	logrus.WithField(logger.KeyCategory, logger.CategoryCore).
		WithField(logger.KeyEXT, opt.EXT).
		Infof("sql-chain: sql query: %s", sqlQuery)
	sqlResult, err := c.sqloader.Query(ctx, sqlQuery)
	if err != nil {
		return "", err
	}

	callOpts = append(opts, schema.WithStopWords([]string{finalStop}))

	// 输出SQL数据
	if opt.FnStreamPreview != nil {
		opt.FnStreamPreview(&callback.PreviewMessage{
			Type:      callback.TypeInline,
			From:      callback.FromRetrievalSqlContenxt,
			NodeID:    opt.MessageNodeID,
			Name:      opt.Name,
			Timestamp: time.Now().UnixMilli(),
			MessageID: random.UniqueID(),
			Content:   sqlResult,
		})

		if opt.MasterEntry {
			messageID := random.UniqueID()
			callOpts = append(callOpts,
				schema.WithFnStreaming(func(ctx context.Context, chunk []byte) error {
					from := callback.FromRetrievalSqlAnswer
					if opt.OutputFinal {
						from = callback.FromExecuteResult
					}

					opt.FnStreamPreview(&callback.PreviewMessage{
						Type:      callback.TypeInline,
						From:      from,
						NodeID:    opt.MessageNodeID,
						Name:      opt.Name,
						Timestamp: time.Now().UnixMilli(),
						MessageID: messageID,
						Content:   string(chunk),
					})
					return nil
				}))
		}
	}

	// 生成答案
	inputs["sqlQuery"] = sqlQuery
	inputs["sqlResult"] = sqlResult

	logrus.WithField(logger.KeyCategory, logger.CategoryCore).
		WithField(logger.KeyEXT, opt.EXT).
		Info("sql-chain: use llm summary answer")
	output, err := c.reschain.Call(
		ctx,
		inputs,
		callOpts...,
	)
	if err != nil {
		return "", err
	}

	return output, nil
}

func (c *SQLChain) GetMemory() memory.Memory {
	return c.sqlchain.GetMemory()
}
