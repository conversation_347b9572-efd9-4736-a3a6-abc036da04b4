package chain

import (
	"context"
	"encoding/json"
	"fmt"
	"secwalk/internal/domain/core/callback"
	"secwalk/internal/domain/core/llm"
	"secwalk/internal/domain/core/memory"
	"secwalk/internal/domain/core/message"
	"secwalk/internal/domain/core/prompt"
	"secwalk/internal/domain/core/schema"
	"secwalk/internal/domain/core/template"
	"secwalk/pkg/logger"
	"secwalk/pkg/random"
	"secwalk/pkg/util"
	"strings"
	"time"

	"github.com/sirupsen/logrus"
)

type FunctionCallChain struct {
	llm           llm.Model
	tools         []schema.Tool
	agents        []schema.Agent
	memory        memory.Memory
	systemPrompt  string
	userPrompt    string
	maxIterations int
	parallel      bool
	hasZH         bool
	noRepeat      bool
	noAction      bool
}

func NewFunctionCallChain(llm llm.Model, opts ...Option) (Chain, error) {
	opt := newDefaultChainOption()
	for _, option := range opts {
		option(opt)
	}

	return &FunctionCallChain{
		llm:           llm,
		tools:         opt.tools,
		agents:        opt.agents,
		memory:        opt.memory,
		systemPrompt:  opt.systemPrompt,
		userPrompt:    opt.userPrompt,
		maxIterations: opt.maxIterations,
		parallel:      opt.parallel,
		noRepeat:      opt.noRepeat,
		noAction:      opt.noAction,
	}, nil
}

func (c *FunctionCallChain) Call(ctx context.Context, inputs map[string]any, opts ...schema.CallOption) (string, error) {
	opt := schema.NewCallOptions()
	for _, o := range opts {
		o(opt)
	}
	var sysRender, userRender string

	// 检查输出是否带有中文
	input, ok := inputs[schema.DefaultInputKey].(string)
	if !ok {
		input = util.ToPureJsonString(inputs[schema.DefaultInputKey])
	}
	c.hasZH = util.HasZH(input)

	actions := make([]*Action, 0)
	for i := 0; i < c.maxIterations; i++ {
		// 创建基础链
		createOpts := make([]Option, 0)
		createOpts = append(createOpts, WithMemory(c.memory))

		// 获取工具配置
		tool_names := c.getFuncNames()
		tool_descs := c.getFuncDescs()

		// 获取框架提示词
		system_prompt := ""
		if c.hasZH {
			if c.parallel {
				system_prompt = _FnCallTemplateInfoZH + "\n\n" + _FnCallTemplateFmtParaZH
			} else {
				system_prompt = _FnCallTemplateInfoZH + "\n\n" + _FnCallTemplateFmtZH
			}
		} else {
			if c.parallel {
				system_prompt = _FnCallTemplateInfoEN + "\n\n" + _FnCallTemplateFmtParaEN
			} else {
				system_prompt = _FnCallTemplateInfoEN + "\n\n" + _FnCallTemplateFmtEN
			}
		}

		// 设置系统提示词
		if len(c.systemPrompt) > 0 {
			system_prompt = c.systemPrompt + "\n\n" + system_prompt
			sysRender, _ = template.RenderTemplate(c.systemPrompt, inputs)
		}
		createOpts = append(createOpts, WithSystemPromptTmpl(prompt.NewSystemMessagePromptTemplate(
			system_prompt,
			prompt.WithPartialValue("tool_names", tool_names),
			prompt.WithPartialValue("tool_descs", tool_descs),
		)))

		// 设置用户提示词
		user_prompt := ""
		if len(c.userPrompt) > 0 {
			user_prompt = c.userPrompt + "\n" + _FnCallDefaultScratchPad
			userRender, _ = template.RenderTemplate(c.userPrompt, inputs)
		} else {
			user_prompt = _FnCallDefaultUser + "\n" + _FnCallDefaultScratchPad
		}
		createOpts = append(createOpts, WithUserPromptTmpl(prompt.NewUserMessagePromptTemplate(
			user_prompt,
			prompt.WithInputKeys(schema.DefaultInputKey, schema.ReservedScratchpad),
		)))

		chain, err := NewLLMChain(c.llm, createOpts...)
		if err != nil {
			return "", err
		}

		inputs[schema.ReservedScratchpad] = c.getScratchpad(actions)

		// 调用模型链
		output, err := chain.Call(ctx, inputs, append(opts,
			schema.WithStopWords(_FnStopWords),
			schema.WithFnStreamPreview(nil),
			schema.WithSystemPromptRender(sysRender),
			schema.WithUserPromptRender(userRender),
		)...)
		if err != nil {
			return "", err
		}

		acts := c.parseOutput(output)
		if len(acts) == 1 && len(acts[0].Thought) > 0 {
			if opt.MasterEntry {
				logrus.WithField(logger.KeyCategory, logger.CategoryCore).
					WithField(logger.KeyEXT, opt.EXT).
					Infof("funcall answer: %s", acts[0].Thought)

				if opt.LogMemory {
					c.memory.SaveMessage(ctx, message.NewAssistantChatMessage(
						fmt.Sprintf("Return: %s", acts[0].Thought)))
				}

				if opt.FnStreamPreview != nil {
					from := callback.FromReactAnswer
					if opt.OutputFinal {
						from = callback.FromExecuteResult
					}

					callback.PreviewFackStream(opt.FnStreamPreview, &callback.PreviewMessage{
						Type:      callback.TypeInline,
						From:      from,
						NodeID:    opt.MessageNodeID,
						Name:      opt.Name,
						Timestamp: time.Now().UnixMilli(),
						MessageID: random.UniqueID(),
						Content:   acts[0].Thought,
					})
				}
			}
			return acts[0].Thought, nil
		}

		for k := range acts {
			actionStr := fmt.Sprintf("%s - %s", acts[k].Action, acts[k].ActionInput)
			// 输出动作调用
			logrus.WithField(logger.KeyCategory, logger.CategoryCore).
				WithField(logger.KeyEXT, opt.EXT).
				Infof("react action: %s", actionStr)

			if opt.LogMemory {
				c.memory.SaveMessage(ctx, message.NewAssistantChatMessage(
					fmt.Sprintf("Function Input: %s - %s", acts[k].Action, acts[k].ActionInput)))
			}

			if opt.FnStreamPreview != nil {
				callback.PreviewFackStream(opt.FnStreamPreview, &callback.PreviewMessage{
					Type:      callback.TypeInline,
					From:      callback.FromReactAction,
					NodeID:    opt.MessageNodeID,
					Name:      opt.Name,
					Timestamp: time.Now().UnixMilli(),
					MessageID: random.UniqueID(),
					Content:   actionStr,
				})
			}

			if c.noAction {
				if opt.MasterEntry {
					logrus.WithField(logger.KeyCategory, logger.CategoryCore).
						WithField(logger.KeyEXT, opt.EXT).
						Infof("react answer: %s", actionStr)

					if opt.LogMemory {
						c.memory.SaveMessage(ctx, message.NewAssistantChatMessage(
							fmt.Sprintf("Final Answer: %s", actionStr)))
					}

					if opt.FnStreamPreview != nil {
						from := callback.FromReactAnswer
						if opt.OutputFinal {
							from = callback.FromExecuteResult
						}

						callback.PreviewFackStream(opt.FnStreamPreview, &callback.PreviewMessage{
							Type:      callback.TypeInline,
							From:      from,
							NodeID:    opt.MessageNodeID,
							Name:      opt.Name,
							Timestamp: time.Now().UnixMilli(),
							MessageID: random.UniqueID(),
							Content:   actionStr,
						})
					}

					continue
				}
			}

			ob := c.doFunction(ctx, acts[k], inputs, append(opts, schema.WithFinalOut(false))...)

			// 输出函数执行结果
			logrus.WithField(logger.KeyCategory, logger.CategoryCore).
				WithField(logger.KeyEXT, opt.EXT).
				Infof("react action observe: %s", ob)
			if opt.LogMemory {
				c.memory.SaveMessage(ctx, message.NewAssistantChatMessage(
					fmt.Sprintf("Function Return: %s", ob),
				))
			}

			if opt.FnStreamPreview != nil {
				opt.FnStreamPreview(&callback.PreviewMessage{
					Type:      callback.TypeInline,
					From:      callback.FromReactObserve,
					NodeID:    opt.MessageNodeID,
					Name:      opt.Name,
					Timestamp: time.Now().UnixMilli(),
					MessageID: random.UniqueID(),
					Content:   ob,
				})
			}
			acts[k].Observation = ob
		}

		actions = append(actions, acts...)
	}

	return "", nil
}

func (c *FunctionCallChain) GetMemory() memory.Memory {
	return c.memory
}

func (c *FunctionCallChain) getFuncNames() string {
	list := make([]string, 0)
	for _, v := range c.tools {
		list = append(list, v.Config().Name)
	}
	for _, v := range c.agents {
		list = append(list, v.Config().Name)
	}
	return strings.Join(list, ",")
}

func (c *FunctionCallChain) getFuncDescs() string {
	type Param struct {
		Name        string `json:"name"`
		Type        string `json:"type"`
		Description string `json:"description"`
		Required    bool   `json:"required"`
	}

	var format string
	if c.hasZH {
		format = _FunctionDescTemplateZH
	} else {
		format = _FunctionDescTemplateEN
	}

	list := make([]string, 0)
	for _, v := range c.tools {
		params := make([]Param, 0)
		for _, vv := range v.Config().InputParameters {
			if vv.Configed {
				continue
			}
			params = append(params, Param{
				Name:        vv.Name,
				Type:        vv.Type,
				Description: vv.Description,
				Required:    vv.Required,
			})
		}
		list = append(list, fmt.Sprintf(format,
			v.Config().Name, v.Config().Name,
			v.Config().Description, util.ToPureJsonString(params),
		))
	}
	for _, v := range c.agents {
		params := make([]Param, 0)
		for _, vv := range v.Config().InputParameters {
			if vv.Configed {
				continue
			}
			params = append(params, Param{
				Name:        vv.Name,
				Type:        vv.Type,
				Description: vv.Description,
				Required:    vv.Required,
			})
		}
		list = append(list, fmt.Sprintf(format,
			v.Config().Name, v.Config().Name,
			v.Config().Description, util.ToPureJsonString(params),
		))
	}
	return strings.Join(list, "\n\n")
}

func (c *FunctionCallChain) getScratchpad(actions []*Action) string {
	if len(actions) == 0 {
		return ""
	}

	var scratchpad string
	if c.parallel {
		preifx := ""
		suffix := ""
		for _, v := range actions {
			preifx += fmt.Sprintf("%s: %s\n%s: %s\n", _FnName, v.Action, _FnArgs, v.ActionInput)
			suffix += fmt.Sprintf("%s: %s\n", _FnResult, v.Observation)
		}
		scratchpad = preifx + suffix + _FnReturn
	} else {
		for _, v := range actions {
			scratchpad += fmt.Sprintf("%s: %s\n%s: %s\n%s: %s\n%s\n",
				_FnName, v.Action, _FnArgs, v.ActionInput, _FnResult, v.Observation, _FnReturn)
		}
	}

	return scratchpad
}

func (c *FunctionCallChain) parseOutput(output string) []*Action {
	if strings.Contains(output, _FnReturn) {
		splits := strings.Split(output, _FnReturn)
		return []*Action{{Thought: strings.TrimSpace(splits[len(splits)-1])}}
	}

	matches := _FnCallRegex.FindAllStringSubmatch(output, -1)
	if len(matches) == 0 {
		return []*Action{
			{
				Thought: strings.ReplaceAll(output, "✿", ""),
			},
		}
	}

	actions := make([]*Action, 0, len(matches))
	for _, match := range matches {
		actions = append(actions,
			&Action{
				Action:      strings.TrimSpace(match[1]),
				ActionInput: strings.TrimSpace(match[2]),
			},
		)
	}

	return actions
}

func (c *FunctionCallChain) doFunction(ctx context.Context, action *Action, inputs map[string]any, opts ...schema.CallOption) string {
	opt := schema.NewCallOptions()
	for _, o := range opts {
		o(opt)
	}

	// 参数反序列化
	var params = make(map[string]any)
	if err := json.Unmarshal([]byte(action.ActionInput), &params); err != nil {
		return "tool params is not a Json map, try regenerate"
	}
	for k, v := range params {
		inputs[k] = v
	}

	// 调用工具
	tool, ok := schema.NameToTool(c.tools)[strings.ToUpper(action.Action)]
	// 调用过的工具直接移除，避免重复调用
	if c.noRepeat {
		for i, v := range c.tools {
			if tool.Config().ID == v.Config().ID {
				c.tools = append(c.tools[:i], c.tools[i+1:]...)
				break
			}
		}
	}

	if ok {
		ob, err := schema.ToolCall(ctx, tool, inputs, append(opts, schema.WithLocals(params))...)
		if err != nil {
			return fmt.Sprintf("function call error: %s", err.Error())
		}
		return ob
	}

	// 调用智能体
	agent, ok := schema.NameToAgent(c.agents)[strings.ToUpper(action.Action)]
	if ok {
		res, err := agent.Call(ctx, params, opts...)
		if err != nil {
			return fmt.Sprintf("function call error: %s", err.Error())
		}
		if opt.CbSetVariable != nil {
			opt.CbSetVariable(opt.NodeID, res.Results)
		}
		return res.Session.Messages[len(res.Session.Messages)-1].Content
	}

	return fmt.Sprintf("%s is not a valid function, try another one", action)
}
