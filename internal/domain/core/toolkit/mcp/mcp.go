package mcp

import (
	"context"
	"encoding/json"
	"secwalk/internal/domain/core/endpoint/hostmcp"
	"secwalk/internal/domain/core/schema"
	"secwalk/pkg/logger"

	sdk "github.com/mark3labs/mcp-go/mcp"
	"github.com/sirupsen/logrus"
)

type McpTool struct {
	cfg schema.ToolConfig
	cli *hostmcp.Client
}

func New(ctx context.Context, cli *hostmcp.Client,
	opts ...schema.ToolOption) (schema.Tool, error) {
	tool := McpTool{cli: cli}

	for _, opt := range opts {
		opt(&tool.cfg)
	}

	return &tool, nil
}

func (c *McpTool) Config() schema.ToolConfig {
	return c.cfg
}

func (c *McpTool) Call(ctx context.Context, input map[string]any,
	opts ...schema.CallOption) (string, error) {
	opt := schema.NewCallOptions()
	for _, o := range opts {
		o(opt)
	}

	logrus.WithField(logger.KeyCategory, logger.CategoryCore).
		WithField(logger.KeyEXT, opt.EXT).
		Infof("[%s] mcp call address: %s", opt.NodeID, c.cli.ProxyHost())

	res, err := c.cli.CallTool(ctx, c.cfg.Name, input)
	if err != nil {
		return "", err
	}

	out := make(map[string]any)
	for _, v := range res.Content {
		switch r := v.(type) {
		case sdk.TextContent:
			out[r.Type] = r.Text
		case sdk.ImageContent:
			out[r.Type] = map[string]any{
				"mime_type": r.MIMEType,
				"data":      r.Data,
			}
		case sdk.EmbeddedResource:
			switch rr := r.Resource.(type) {
			case sdk.TextResourceContents:
				out[r.Type] = map[string]any{
					"url":       rr.URI,
					"mime_type": rr.MIMEType,
					"text":      rr.Text,
				}
			case sdk.BlobResourceContents:
				out[r.Type] = map[string]any{
					"url":       rr.URI,
					"mime_type": rr.MIMEType,
					"blob":      rr.Blob,
				}
			}
		}
	}

	data, err := json.Marshal(out)
	return string(data), err
}
