package restful

import (
	"context"
	"encoding/json"
	"fmt"
	"secwalk/internal/domain/core/schema"
	"testing"

	"github.com/stretchr/testify/require"
)

func TestParamters(t *testing.T) {
	tool, err := New(
		schema.WithToolConfig(
			&schema.ToolConfig{
				ID:          "123",
				Name:        "aaa",
				Description: "vvv",
				InputParameters: []schema.Parameter{
					{Location: schema.LocationBody, Name: "1", DefaultValue: "a"},
					{Location: schema.LocationPath, Name: "2", DefaultValue: "b"},
					{Location: schema.LocationQuery, Name: "3", DefaultValue: "c"},
					{Location: schema.LocationHeader, Name: "4", DefaultValue: "e"},
				},
				Method: "POST",
				Url:    "http://127.0.0.1:8900/v1",
			},
		),
	)
	require.NoError(t, err)

	tool.Call(context.Background(), map[string]any{})
}

func TestWeather(t *testing.T) {
	tool, err := New(
		schema.WithToolConfig(
			&schema.ToolConfig{
				ID:          "4dbff23b-6cec-4234-b441-9e44e65c1de2",
				Name:        "weather",
				Description: "天气查询是一个简单的HTTP接口，根据用户输入的adcode，查询目标区域当前/未来的天气情况，数据来源是中国气象局。",
				InputParameters: []schema.Parameter{
					{
						Name:         "key",
						Description:  "请求服务权限标识",
						Type:         schema.TypeString,
						DefaultValue: "cfec07722b4a2d374b2ca3f83885c297",
						Required:     true,
						Configed:     true,
						Location:     schema.LocationQuery,
					},
					{
						Name:         "city",
						Description:  "城市编码",
						Type:         schema.TypeString,
						DefaultValue: "330100",
						Required:     true,
						Location:     schema.LocationQuery,
					},
				},
				Method: "GET",
				Url:    "https://restapi.amap.com/v3/weather/weatherInfo",
			},
		),
	)
	require.NoError(t, err)

	out, err := tool.Call(context.Background(), map[string]any{})
	require.NoError(t, err)

	fmt.Println(out)
}

func TestGenerateTool(t *testing.T) {
	type Request struct {
		Config schema.ToolConfig `json:"config,omitempty"`
		Inputs map[string]any    `json:"inputs,omitempty"`
	}

	req := Request{
		Config: schema.ToolConfig{
			ID:          "4dbff23b-6cec-4234-b441-9e44e65c1de2",
			Name:        "weather",
			Description: "天气查询是一个简单的HTTP接口，根据用户输入的adcode，查询目标区域当前/未来的天气情况，数据来源是中国气象局。",
			InputParameters: []schema.Parameter{
				{
					Name:        "key",
					Description: "请求服务权限标识",
					Type:        schema.TypeString,
					Required:    true,
					Location:    schema.LocationQuery,
				},
				{
					Name:        "city",
					Description: "城市编码",
					Type:        schema.TypeString,
					Required:    true,
					Location:    schema.LocationQuery,
				},
			},
			Method: "GET",
			Url:    "https://restapi.amap.com/v3/weather/weatherInfo",
		},
		Inputs: map[string]any{
			"key":  "cfec07722b4a2d374b2ca3f83885c297",
			"city": "330100",
		},
	}

	data, err := json.Marshal(req)
	require.NoError(t, err)

	fmt.Println(string(data))
}

func TestTi(t *testing.T) {
	tool, err := New(
		schema.WithToolConfig(
			&schema.ToolConfig{
				ID:          "4dbff23b-6cec-4234-b441-9e44e65c1de2",
				Name:        "weather",
				Description: "天气查询是一个简单的HTTP接口，根据用户输入的adcode，查询目标区域当前/未来的天气情况，数据来源是中国气象局。",
				InputParameters: []schema.Parameter{
					{
						Name:         "Sdc-Token",
						Description:  "请求服务权限标识",
						Type:         schema.TypeString,
						DefaultValue: "5041bc1f50f1fcb1e250fe34dd51d586f366df82",
						Required:     true,
						Configed:     true,
						Location:     schema.LocationHeader,
					},
					{
						Name:         "keys",
						Description:  "城市编码",
						Type:         schema.TypeString,
						DefaultValue: "iuqerfsodp9ifjaposdfjhgosurijfaewrwergwea.com",
						Required:     true,
						Location:     schema.LocationBody,
					},
				},
				Method: "POST",
				Url:    "https://daas.dbappsecurity.com.cn/ti/query_domain_ip_intelligence",
			},
		),
	)
	require.NoError(t, err)

	out, err := tool.Call(context.Background(), map[string]any{})
	require.NoError(t, err)

	fmt.Println(out)
}
