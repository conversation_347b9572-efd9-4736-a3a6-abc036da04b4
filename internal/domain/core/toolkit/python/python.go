package python

import (
	"context"
	"errors"
	"fmt"
	"secwalk/internal/domain/core/endpoint/codeide"
	"secwalk/internal/domain/core/schema"
	"secwalk/pkg/util"
	"sync"
)

var (
	mux        sync.Mutex
	max_result = 0
)

func SetMaxResult(max int) {
	mux.Lock()
	defer mux.Unlock()
	max_result = max * 1024 * 1024
}

func NewToolkit() schema.Toolkit {
	return schema.Toolkit{
		ID:          "11d08b7a-c324-4e56-9ce8-7b5037a75795",
		Name:        "代码执行器",
		Description: "如果你想通过运行一段代码，可以使用这个工具包。",
		Type:        schema.ToolkitTypeSystem,
		Tools: []schema.ToolConfig{
			{
				ID:          "5c1e41d0-91d4-4082-88cd-35a30a33e01f",
				Name:        "代码执行器v1",
				Description: "如果你想通过运行一段代码，可以使用这个工具。",
				InputParameters: []schema.Parameter{
					{
						Name:        "code",
						Description: "python代码",
						Type:        schema.TypeString,
						Required:    true,
					},
				},
				Creator: New,
			},
			{
				ID:          "df907c68-3d21-11f0-9dce-000c29676a38",
				Name:        "代码执行器v2",
				Description: "代码执行器v2版本是一款升级版代码执行工具。",
				InputParameters: []schema.Parameter{
					{
						Name:         "lang",
						Description:  "语言, 支持python、javascript",
						Type:         schema.TypeString,
						Required:     false,
						DefaultValue: "python",
					},
					{
						Name:        "code",
						Description: "代码",
						Type:        schema.TypeString,
						Required:    true,
					},
				},
				Creator: NewCodeIDE,
			},
		},
	}
}

type Python struct {
	cfg schema.ToolConfig
}

func New(opts ...schema.ToolOption) (schema.Tool, error) {
	var cfg schema.ToolConfig
	for _, opt := range opts {
		opt(&cfg)
	}
	return &Python{cfg: cfg}, nil
}

// 工具配置
func (c *Python) Config() schema.ToolConfig {
	return c.cfg
}

func (c *Python) Call(ctx context.Context, inputs map[string]any,
	opts ...schema.CallOption) (string, error) {
	opt := schema.NewCallOptions()
	for _, o := range opts {
		o(opt)
	}

	body := &codeide.InterpreterRequest{
		Inputs: inputs,
		Lang:   codeide.LangPython,
	}

	if len(c.cfg.Code) > 0 {
		body.Code = c.cfg.Code
	} else if code, err := schema.GetValueFromName("code",
		c.cfg.InputParameters, inputs); err == nil {
		body.Code = code.String()
	} else {
		return "", errors.New("you must input python code")
	}

	res, err := codeide.CodeInterpreter(ctx, body,
		schema.WithNodeID(opt.NodeID),
		schema.WithEXT(opt.EXT),
	)
	if err != nil {
		return "", err
	}

	if max_result > 0 && len(res.Output) > max_result {
		return "", fmt.Errorf("coder result is too large, should less than %d", max_result)
	}

	return res.Output, nil
}

type CodeIDE struct {
	cfg schema.ToolConfig
}

func NewCodeIDE(opts ...schema.ToolOption) (schema.Tool, error) {
	var cfg schema.ToolConfig
	for _, opt := range opts {
		opt(&cfg)
	}
	return &CodeIDE{cfg: cfg}, nil
}

// 工具配置
func (c *CodeIDE) Config() schema.ToolConfig {
	return c.cfg
}

func (c *CodeIDE) Call(ctx context.Context, inputs map[string]any,
	opts ...schema.CallOption) (string, error) {
	opt := schema.NewCallOptions()
	for _, o := range opts {
		o(opt)
	}

	body := &codeide.InterpreterRequest{
		Inputs: inputs,
	}

	if len(c.cfg.Code) > 0 {
		body.Code = c.cfg.Code
	} else if code, err := schema.GetValueFromName("code",
		c.cfg.InputParameters, inputs); err == nil {
		body.Code = code.String()
	} else {
		return "", errors.New("you must input python code")
	}

	lang, err := schema.GetValueFromName("lang",
		c.cfg.InputParameters, inputs)
	if err == nil {
		body.Lang = lang.String()
	}

	res, err := codeide.CodeInterpreterV2(ctx, body,
		schema.WithNodeID(opt.NodeID),
		schema.WithEXT(opt.EXT),
	)
	if err != nil {
		return "", err
	}

	if max_result > 0 && len(util.ToJsonString(res.Result)) > max_result {
		return "", fmt.Errorf("coder result is too large, should less than %d", max_result)
	}

	return util.ToPureJsonString(res), nil
}
