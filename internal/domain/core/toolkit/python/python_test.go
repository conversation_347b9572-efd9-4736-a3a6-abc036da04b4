package python

import (
	"context"
	"secwalk/internal/domain/core/schema"
	"testing"

	"github.com/stretchr/testify/require"
)

func TestPythonCall(t *testing.T) {
	tool, err := NewCodeIDE(
		schema.WithToolConfig(
			&schema.ToolConfig{
				ID:          "111",
				Name:        "Python",
				Description: "代码执行",
				Code:        `import subprocess;subprocess.run(["which","python3"])`,
			},
		),
	)
	require.NoError(t, err)

	ret, err := tool.Call(context.Background(), map[string]any{
		"secvirt": "***************:8994",
	})
	require.NoError(t, err)

	t.Log(ret)
}
