package parser

import (
	"bytes"
	"context"
	"path/filepath"
	"secwalk/internal/domain/core/toolkit/secdocx/format"
	"strconv"
	"strings"

	"github.com/unidoc/unioffice/v2/spreadsheet"
)

// parseExcel 解析Excel文档并返回一个文档对象
func parseExcel(ctx context.Context, file *format.File) (*format.Document, error) {
	// 检查上下文是否已经结束
	select {
	case <-ctx.Done():
		return nil, ctx.Err()
	default:
		// 继续处理
	}

	// 获取文件数据
	reader := bytes.NewReader(file.GetReader().Bytes())

	// 创建Excel文档
	xls, err := spreadsheet.Read(reader, int64(len(file.GetReader().Bytes())))
	if err != nil {
		return nil, err
	}

	// 创建文档
	docf := format.NewDocument(filepath.Base(file.Target), format.TypeXLS)

	// 解析文档属性
	for k, v := range parseExcelProperties(xls) {
		docf.Properties[k] = v
	}

	// 处理工作表
	for sheetIndex, sheet := range xls.Sheets() {
		// 定期检查上下文
		select {
		case <-ctx.Done():
			return nil, ctx.Err()
		default:
			// 继续处理
		}

		// 每个工作表被视为单独的页面
		pageNum := sheetIndex + 1

		// 将工作表名称添加为标题
		sheetNameBlock := format.NewTextBlock(sheet.Name(), 1, "")
		docf.AddBlockToPage(pageNum, sheetNameBlock)

		// 创建数据结构
		var rows, cols int
		data := make(map[int]map[int]string)

		// 初始化行列数
		rows = 0
		cols = 0

		// 处理行和单元格
		rowCount := 0
		for _, row := range sheet.Rows() {
			// 定期检查上下文（每20行）
			rowCount++
			if rowCount%20 == 0 {
				select {
				case <-ctx.Done():
					return nil, ctx.Err()
				default:
					// 继续处理
				}
			}

			rowIndex := int(row.RowNumber())

			for _, cell := range row.Cells() {
				// 从引用中获取列索引
				colIndex := getColumnIndex(cell.Reference())

				// 将单元格值转换为字符串
				cellValue := getCellValue(cell)
				if cellValue != "" {
					// 初始化行数据
					if _, ok := data[rowIndex]; !ok {
						data[rowIndex] = make(map[int]string)
					}

					// 添加单元格数据
					data[rowIndex][colIndex] = cellValue

					// 更新行列数
					if rowIndex+1 > rows {
						rows = rowIndex + 1
					}
					if colIndex+1 > cols {
						cols = colIndex + 1
					}
				}
			}
		}

		// 如果表格有数据，将其添加到文档中
		if rows > 0 && cols > 0 {
			// 创建最终数据数组
			tableData := make([][]string, rows)
			for i := range tableData {
				tableData[i] = make([]string, cols)
			}

			// 填充数据
			for r, rowData := range data {
				for c, cellValue := range rowData {
					tableData[r][c] = cellValue
				}
			}

			// 创建表格块
			tableBlock := format.NewTableBlock(rows, cols, tableData)
			docf.AddBlockToPage(pageNum, tableBlock)
		}
	}

	return docf, nil
}

// parseExcelProperties 从 Excel 文档中提取属性
func parseExcelProperties(xls *spreadsheet.Workbook) map[string]any {
	properties := make(map[string]any)

	// 添加基本属性
	properties["sheetCount"] = len(xls.Sheets())

	// 尝试获取工作表名称
	sheetNames := []string{}
	for _, sheet := range xls.Sheets() {
		sheetNames = append(sheetNames, sheet.Name())
	}
	if len(sheetNames) > 0 {
		properties["sheetNames"] = strings.Join(sheetNames, ", ")
	}

	return properties
}

// getColumnIndex 从单元格引用中提取列索引（例如，"A1" -> 0, "B1" -> 1）
func getColumnIndex(ref string) int {
	// 提取列字母（第一个数字之前的所有内容）
	colLetters := ""
	for _, c := range ref {
		if c >= '0' && c <= '9' {
			break
		}
		colLetters += string(c)
	}

	// 将列字母转换为索引（A=0, B=1, ..., Z=25, AA=26, ...）
	index := 0
	for _, c := range colLetters {
		index = index*26 + int(c-'A')
	}

	return index
}

// getCellValue 将单元格的值转换为字符串
func getCellValue(cell spreadsheet.Cell) string {
	// 直接获取字符串值
	value := cell.GetString()

	// 如果是公式，尝试获取计算值
	if strings.HasPrefix(value, "=") {
		// 对于公式，我们只能返回公式文本
		return value
	}

	// 如果看起来像数字，尝试获取数值
	if value == "" {
		if v, err := cell.GetRawValue(); err == nil && v != "" {
			// 尝试转换为浮点数并格式化
			if f, err := strconv.ParseFloat(v, 64); err == nil {
				return strconv.FormatFloat(f, 'f', -1, 64)
			}
			return v
		}
	}

	return value
}
