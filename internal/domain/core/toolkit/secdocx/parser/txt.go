package parser

import (
	"bufio"
	"bytes"
	"context"
	"path/filepath"
	"secwalk/internal/domain/core/toolkit/secdocx/format"
	"strings"
)

// parseTXT 解析TXT文本文件并返回一个文档对象
func parseTXT(ctx context.Context, file *format.File) (*format.Document, error) {
	// 检查上下文是否已经结束
	select {
	case <-ctx.Done():
		return nil, ctx.Err()
	default:
		// 继续处理
	}

	// 获取文件数据
	fileData := file.GetReader().Bytes()

	// 创建文档
	docf := format.NewDocument(filepath.Base(file.Target), format.TypeTXT)

	// 解析文档属性
	for k, v := range parseTXTProperties(fileData) {
		docf.Properties[k] = v
	}

	// 处理文本内容
	pageNum := 1 // 从第1页开始
	lineCount := 0
	paragraphCount := 0
	currentParagraph := ""

	// 使用Scanner逐行读取文本
	scanner := bufio.NewScanner(bytes.NewReader(fileData))
	for scanner.Scan() {
		// 定期检查上下文（每100行）
		lineCount++
		if lineCount%100 == 0 {
			select {
			case <-ctx.Done():
				return nil, ctx.Err()
			default:
				// 继续处理
			}
		}

		line := scanner.Text()
		trimmedLine := strings.TrimSpace(line)

		// 如果是空行，表示段落结束
		if trimmedLine == "" {
			// 如果当前段落不为空，添加到文档中
			if currentParagraph != "" {
				// 检查是否需要开始新页（每50个段落任意分页）
				paragraphCount++
				if paragraphCount > 50 {
					pageNum++
					paragraphCount = 1
				}

				// 创建文本块
				textBlock := format.NewTextBlock(currentParagraph, 0, "")

				// 将块添加到文档中
				docf.AddBlockToPage(pageNum, textBlock)

				// 重置当前段落
				currentParagraph = ""
			}
		} else {
			// 如果当前段落为空，直接赋值；否则添加空格和当前行
			if currentParagraph == "" {
				currentParagraph = trimmedLine
			} else {
				currentParagraph += " " + trimmedLine
			}
		}
	}

	// 处理最后一个段落
	if currentParagraph != "" {
		// 创建文本块
		textBlock := format.NewTextBlock(currentParagraph, 0, "")

		// 将块添加到文档中
		docf.AddBlockToPage(pageNum, textBlock)
	}

	// 检查Scanner是否有错误
	if err := scanner.Err(); err != nil {
		return nil, err
	}

	return docf, nil
}

// parseTXTProperties 从TXT文件中提取属性
func parseTXTProperties(data []byte) map[string]any {
	properties := make(map[string]any)

	// 计算行数
	lineCount := 0
	scanner := bufio.NewScanner(bytes.NewReader(data))
	for scanner.Scan() {
		lineCount++
	}
	properties["lineCount"] = lineCount

	// 计算字符数
	properties["charCount"] = len(data)

	// 计算单词数（简单实现，仅按空格分割）
	wordCount := 0
	scanner = bufio.NewScanner(bytes.NewReader(data))
	scanner.Split(bufio.ScanWords)
	for scanner.Scan() {
		wordCount++
	}
	properties["wordCount"] = wordCount

	return properties
}
