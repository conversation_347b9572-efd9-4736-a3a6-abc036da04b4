package parser

import (
	"secwalk/internal/domain/core/schema/ext"
	"secwalk/internal/domain/core/toolkit/secdocx/format"
	"secwalk/internal/domain/core/xfile"
)

// Option 是一个配置解析器的函数
type Option func(*Parser)

// WithFileType 设置解析器的文件类型
func WithFileType(fileType string, fileName string) Option {
	return func(p *Parser) {

		switch fileType {
		case "doc", "docx":
			p.fileType = format.TypeDOC
			return
		case "xls", "xlsx":
			p.fileType = format.TypeXLS
			return
		case "ppt", "pptx":
			p.fileType = format.TypePPT
			return
		}

		if fileType == "" {
			xf, err := xfile.Parse(fileName)
			if err != nil {
				p.fileType = format.TypeTXT
				return
			}
			switch xf.Extension {
			case "pdf":
				fileType = format.TypePDF
			case "doc", "docx":
				fileType = format.TypeDOC
			case "xls", "xlsx":
				fileType = format.TypeXLS
			case "ppt", "pptx":
				fileType = format.TypePPT
			case "json":
				fileType = format.TypeJson
			case "txt":
				fileType = format.TypeTXT
			}
		}

		p.fileType = fileType
	}
}

// WithSplitMode 设置解析器的分割模式
func WithSplitMode(splitMode string) Option {
	return func(p *Parser) {
		p.splitMode = splitMode
	}
}

func WithEXT(ext ext.ExtType) Option {
	return func(o *Parser) { o.ext = ext }
}
