package parser

import (
	"bytes"
	"context"
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"path/filepath"
	"regexp"
	"secwalk/internal/domain/core/schema/ext"
	"secwalk/internal/domain/core/toolkit/secdocx/format"
	"strings"

	"github.com/unidoc/unipdf/v3/extractor"
	"github.com/unidoc/unipdf/v3/model"
)

// parsePDF 解析PDF文件并返回一个文档对象
func parsePDF(ctx context.Context, file *format.File) (*format.Document, error) {
	// 从上下文中获取ext信息，用于图片存储
	extType := ext.New("")

	// 检查上下文是否已经结束
	select {
	case <-ctx.Done():
		return nil, ctx.Err()
	default:
	}

	// 获取文件数据
	fileData := file.GetReader().Bytes()

	// 创建PDF读取器
	reader := bytes.NewReader(fileData)
	pdf, err := model.NewPdfReader(reader)
	if err != nil {
		return nil, err
	}

	// 创建文档
	docf := format.NewDocument(filepath.Base(file.Target), format.TypePDF)

	// 解析文档属性
	for k, v := range parsePDFProperties(pdf) {
		docf.Properties[k] = v
	}

	// 获取页数
	numPages, err := pdf.GetNumPages()
	if err != nil {
		return nil, err
	}

	// 处理每一页
	for i := 1; i <= numPages; i++ {
		// 在处理每页之前检查上下文是否已经结束
		select {
		case <-ctx.Done():
			return nil, ctx.Err()
		default:
			// 继续处理
		}

		page, err := pdf.GetPage(i)
		if err != nil {
			return nil, err
		}

		// 提取页面内容（包括文本和图片）
		err = extractPDFPageContent(ctx, docf, page, i, extType)
		if err != nil {
			return nil, err
		}
	}

	return docf, nil
}

// parsePDFProperties 从 PDF 文档中提取属性
func parsePDFProperties(pdf *model.PdfReader) map[string]any {
	properties := make(map[string]any)

	// 获取页数
	if numPages, err := pdf.GetNumPages(); err == nil {
		properties["pageCount"] = numPages
	}

	return properties
}

// extractPDFPageContent 从 PDF 页面中提取内容（文本和图片）
func extractPDFPageContent(ctx context.Context, docf *format.Document, page *model.PdfPage, pageNum int, extType ext.ExtType) error {
	// 检查上下文是否已经结束
	select {
	case <-ctx.Done():
		return ctx.Err()
	default:
		// 继续处理
	}

	// 创建提取器
	ex, err := extractor.New(page)
	if err != nil {
		return err
	}

	// 提取文本
	text, err := ex.ExtractText()
	if err != nil {
		return err
	}

	// 在可能耗时的操作后再次检查上下文
	select {
	case <-ctx.Done():
		return ctx.Err()
	default:

	}

	// 如果提取的文本为空，尝试使用页面文本对象
	if strings.TrimSpace(text) == "" {
		pageText, _, _, err := ex.ExtractPageText()
		if err == nil && pageText != nil {
			text = pageText.Text()
		}
	}

	// 如果文本仍然为空，跳过这一页
	if strings.TrimSpace(text) == "" {
		return nil
	}

	// 将提取的文本解析为块
	blocks := parsePDFText(text)

	// 如果没有有效的块，跳过
	if len(blocks) == 0 {
		return nil
	}

	// 将块添加到文档中
	for _, block := range blocks {
		docf.AddBlockToPage(pageNum, block)
	}

	// 提取页面中的图片
	if err := extractPDFPageImages(ctx, docf, page, pageNum, extType); err != nil {
		return err
	}

	return nil
}

// parsePDFText 将PDF文本解析为块
func parsePDFText(text string) []format.Block {
	var blocks []format.Block

	// 将文本分割为段落
	paragraphs := strings.Split(text, "\n\n")
	for _, paragraph := range paragraphs {
		paragraph = strings.TrimSpace(paragraph)
		if paragraph == "" {
			continue
		}

		// 检查段落是否为标题
		headingLevel, headingNumber := detectHeading(paragraph)
		finalText := paragraph

		// 如果检测到标题编号，将编号拼接到标题前面
		if headingLevel > 0 && headingNumber != "" {
			// 检查文本是否已经包含编号，如果没有则添加
			if !strings.HasPrefix(paragraph, headingNumber) {
				finalText = headingNumber + " " + paragraph
			}
		}

		// 创建文本块
		textBlock := format.NewTextBlock(finalText, headingLevel, headingNumber)

		blocks = append(blocks, textBlock)
	}

	return blocks
}

// detectHeading 尝试检测文本是否为标题，并返回其级别和编号
func detectHeading(text string) (int, string) {
	// 检查数字标题模式，如 "1. 标题", "1.1 标题", "1.1.1 标题" 等
	numHeadingRegex := regexp.MustCompile(`^(\d+(\.\d+)*\.?)\s+(.+)$`)
	if matches := numHeadingRegex.FindStringSubmatch(text); len(matches) > 0 {
		// 根据点的数量确定标题级别
		level := 1
		numberPart := matches[1]
		// 移除末尾的点来计算级别
		cleanNumber := strings.TrimSuffix(numberPart, ".")
		if strings.Contains(cleanNumber, ".") {
			level = len(strings.Split(cleanNumber, "."))
		}
		return level, numberPart
	}

	// 检查第几章/节模式，如 "第一章 标题", "第二节 标题" 等
	chapterRegex := regexp.MustCompile(`^\s*第([一二三四五六七八九十百千万]+|一十|二十|\d+)\s*([章节条])\s*[:：]?\s*(.+)$`)
	if matches := chapterRegex.FindStringSubmatch(text); len(matches) > 0 {
		// 根据章节类型确定级别
		level := 1
		if matches[2] == "节" {
			level = 2
		} else if matches[2] == "条" {
			level = 3
		}
		return level, matches[1] + matches[2]
	}

	// 检查中文数字标题模式，如 "一、标题", "（一）标题" 等
	chineseHeadingRegex := regexp.MustCompile(`^\s*[（(]?([一二三四五六七八九十]+)[)）]?[、.．]?\s*(.+)$`)
	if matches := chineseHeadingRegex.FindStringSubmatch(text); len(matches) > 0 {
		return 1, matches[1]
	}

	// 检查罗马数字标题模式，如 "I. 标题", "II. 标题" 等
	romanHeadingRegex := regexp.MustCompile(`^\s*([IVXivx]+)[.\s]\s*(.+)$`)
	if matches := romanHeadingRegex.FindStringSubmatch(text); len(matches) > 0 {
		return 1, matches[1]
	}

	// 检查字母标题模式，如 "A. 标题", "B. 标题" 等
	letterHeadingRegex := regexp.MustCompile(`^\s*([A-Za-z])[.\s]\s*(.+)$`)
	if matches := letterHeadingRegex.FindStringSubmatch(text); len(matches) > 0 {
		return 1, matches[1]
	}

	// 检查特殊标题模式，如文本较短且以冒号结尾
	if len(text) < 50 && (strings.HasSuffix(text, ":") || strings.HasSuffix(text, "：")) {
		return 1, ""
	}

	// 检查文本特征，如全部大写或特殊关键词
	if len(text) < 100 {
		// 全部大写的英文标题
		if text == strings.ToUpper(text) && regexp.MustCompile(`[A-Z]`).MatchString(text) {
			return 1, ""
		}

		// 包含特定关键词的标题
		headingKeywords := []string{"摘要", "摘要", "关键词", "引言", "结论", "背景", "方法", "结果", "讨论", "参考文献", "附录"}
		for _, keyword := range headingKeywords {
			if strings.Contains(text, keyword) && len(text) < 20 {
				return 1, ""
			}
		}
	}

	return 0, "" // 不是标题
}

// extractPDFPageImages 从 PDF 页面中提取图片
func extractPDFPageImages(ctx context.Context, docf *format.Document, page *model.PdfPage, pageNum int, extType ext.ExtType) error {
	// 检查上下文
	select {
	case <-ctx.Done():
		return ctx.Err()
	default:
	}

	// 创建图片提取器
	imageExtractor, err := extractor.New(page)
	if err != nil {
		return err
	}

	// 提取页面中的所有图片
	images, err := imageExtractor.ExtractPageImages(nil)
	if err != nil {
		// 如果提取图片失败，不返回错误，只是跳过
		return nil
	}

	// 处理每个图片
	for _, img := range images.Images {
		// 检查上下文
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
		}

		// 获取图片数据
		var imgBytes []byte
		imgData, err := img.Image.ToGoImage()
		if err != nil {
			// 图片转换失败，使用最小图片数据
			imgBytes = createMinimalPNGData()
		} else {
			// 将图片转换为字节数组
			imgBytes, err = imageToBytes(imgData)
			if err != nil || len(imgBytes) == 0 {
				// 转换失败或空数据，使用最小图片数据
				imgBytes = createMinimalPNGData()
			}
		}

		// 存储图片到MinIO
		xf := storeImageToMinio(ctx, imgBytes, "pdf")

		// 添加到文档（如果xf为NULL则不添加）
		addImageToDocument(docf, xf, pageNum)
	}

	return nil
}

// generateRandomImageNameForPDF 为PDF生成随机图片名称
func generateRandomImageNameForPDF() string {
	// 生成8字节的随机数据
	bytes := make([]byte, 8)
	_, err := rand.Read(bytes)
	if err != nil {
		// 如果随机数生成失败，使用备用方案
		return fmt.Sprintf("pdf_img_%d", len(bytes)*1000+int(bytes[0]))
	}

	// 将字节转换为十六进制字符串，加上pdf前缀以区分
	return fmt.Sprintf("pdf_%s", hex.EncodeToString(bytes))
}

// imageToBytes 将Go图片转换为字节数组
func imageToBytes(img interface{}) ([]byte, error) {
	// 这里需要实现图片到字节数组的转换
	// 由于unipdf的图片处理比较复杂，这里先返回空数组
	// 在实际实现中，需要根据unipdf的API进行具体实现
	// 为了测试，这里返回空数组，这样会触发使用createMinimalPNGDataForPDF()
	return []byte{}, nil
}
