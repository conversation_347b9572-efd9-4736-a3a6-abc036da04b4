package parser

import (
	"context"
	"fmt"
	"os"
	"secwalk/internal/domain/core/endpoint"
	"secwalk/internal/domain/core/schema/ext"
	"secwalk/internal/domain/core/toolkit/secdocx/format"
)

// Parser 表示文档解析器
type Parser struct {
	fileName  string      // 文件路径
	fileType  string      // 文件类型
	splitMode string      // 分割模式
	data      []byte      // 文件数据，如果提供则直接使用，否则从文件路径读取
	ext       ext.ExtType // 组织ID，用于从minIO获取文件
}

// New 创建一个新的文档解析器
func New(fileName string, opts ...Option) (*Parser, error) {
	p := &Parser{
		fileName:  fileName,
		splitMode: format.SplitModePage, // 默认为页面视图
	}

	for _, opt := range opts {
		opt(p)
	}

	return p, nil
}

// Parse 解析文档并返回结果为JSON字符串
func (p *Parser) Parse(ctx context.Context) (string, error) {

	resultCh := make(chan struct {
		result string
		err    error
	})

	go func() {
		// 获取文件数据
		var data []byte
		var err error

		// 如果提供了数据，直接使用
		if len(p.data) > 0 {
			data = p.data
		} else if p.fileName != "" {
			// 如果提供了组织ID，尝试从 minIO 上获取文件
			data, err = endpoint.GetFile(ctx, p.fileName, p.ext)
			if err != nil {
				// 如果从 minIO 获取失败，尝试从本地文件系统读取
				data, err = os.ReadFile(p.fileName)
				if err != nil {
					resultCh <- struct {
						result string
						err    error
					}{"", fmt.Errorf("file reading failed: %w", err)}
					return
				}
			}
		}

		// 创建文件对象
		file := format.NewFile(data, p.fileName)

		// 根据文件类型解析文档
		var docf *format.Document

		switch p.fileType {
		case format.TypePDF:
			docf, err = parsePDF(ctx, file)
		case format.TypeDOC, format.TypeXLS, format.TypePPT:
			docf, err = parseOffice(ctx, file, p.fileType)
		case format.TypeTXT, format.TypeJson:
			docf, err = parseTXT(ctx, file)
		default:
			resultCh <- struct {
				result string
				err    error
			}{"", fmt.Errorf("file type not supported: %s", p.fileType)}
			return
		}

		if err != nil {
			resultCh <- struct {
				result string
				err    error
			}{"", err}
			return
		}

		// 设置视图类型
		if p.splitMode == "toc" || p.splitMode == "tocView" {
			// 如果是目录视图，需要构建目录
			docf.BuildTocViewFromPageView()
			// 设置视图类型为目录视图
			docf.SetViewType(format.SpiltModeToc)
			// 清空页面数据，只保留目录数据
			docf.Pages = nil
		} else {
			// 默认使用页面视图
			docf.SetViewType(format.SplitModePage)
			// 清空目录数据，只保留页面数据
			docf.Headers = nil
		}

		// 转换为JSON
		result, err := docf.ToJSON()
		if err != nil {
			resultCh <- struct {
				result string
				err    error
			}{"", err}
			return
		}

		resultCh <- struct {
			result string
			err    error
		}{result, nil}
	}()

	// 等待结果或超时
	select {
	case res := <-resultCh:
		return res.result, res.err
	case <-ctx.Done():
		return "", fmt.Errorf("parser time out: %w", ctx.Err())
	}
}

// parseOffice 解析办公文档（Word、Excel、PowerPoint）
func parseOffice(ctx context.Context, file *format.File, fileType string) (*format.Document, error) {
	switch fileType {
	case format.TypeDOC:
		return parseWord(ctx, file)
	case format.TypeXLS:
		return parseExcel(ctx, file)
	case format.TypePPT:
		return parsePowerPoint(ctx, file)
	}

	return nil, fmt.Errorf("file type not supported: %s", fileType)
}

// extraFiles 将额外文件添加到文档中
func extraFiles(ctx context.Context, docf *format.Document, files []interface{}, data []byte) error {
	for _, v := range files {
		if extraFile, ok := v.(struct{ ZipPath string }); ok {
			docf.AddAttachment(extraFile.ZipPath, "file", extraFile.ZipPath, "application/octet-stream", "")
		}
	}
	return nil
}
