package parser

import (
	"bytes"
	"context"
	"path/filepath"
	"secwalk/internal/domain/core/toolkit/secdocx/format"
	"strconv"
	"strings"

	"github.com/unidoc/unioffice/v2/presentation"
)

// parsePowerPoint 解析PowerPoint文档并返回一个文档对象
func parsePowerPoint(ctx context.Context, file *format.File) (*format.Document, error) {
	// 检查上下文是否已经结束
	select {
	case <-ctx.Done():
		return nil, ctx.Err()
	default:

	}

	// 获取文件数据
	reader := bytes.NewReader(file.GetReader().Bytes())

	// 创建PowerPoint文档
	ppt, err := presentation.Read(reader, int64(len(file.GetReader().Bytes())))
	if err != nil {
		return nil, err
	}
	defer ppt.Close()

	// 创建文档
	docf := format.NewDocument(filepath.Base(file.Target), format.TypePPT)

	// 解析文档属性
	for k, v := range parsePowerPointProperties(ppt) {
		docf.Properties[k] = v
	}

	// 处理幻灯片
	for i, slide := range ppt.Slides() {
		// 定期检查上下文（每5张幻灯片）
		if i%5 == 0 {
			select {
			case <-ctx.Done():
				return nil, ctx.Err()
			default:
				// 继续处理
			}
		}

		// 每张幻灯片被视为单独的页面
		pageNum := i + 1

		// 将幻灯片编号添加为标题
		slideHeading := format.NewTextBlock("幻灯片 "+strconv.Itoa(pageNum), 1, "")
		docf.AddBlockToPage(pageNum, slideHeading)

		// 提取幻灯片文本
		slideText := extractSlideText(slide)
		if slideText := strings.TrimSpace(slideText); slideText != "" {
			textBlock := format.NewTextBlock(slideText, 0, "")
			docf.AddBlockToPage(pageNum, textBlock)
		}
	}

	return docf, nil
}

// extractSlideText 从幻灯片中提取文本
func extractSlideText(slide presentation.Slide) string {
	// 从幻灯片中获取所有文本
	var allText strings.Builder

	// 尝试使用内置方法提取文本
	if text := slide.ExtractText(); text != nil {
		allText.WriteString(text.Text())
	}

	return allText.String()
}

// parsePowerPointProperties 从 PowerPoint 文档中提取属性
func parsePowerPointProperties(ppt *presentation.Presentation) map[string]any {
	properties := make(map[string]any)

	// 添加基本属性
	properties["slideCount"] = len(ppt.Slides())

	return properties
}
