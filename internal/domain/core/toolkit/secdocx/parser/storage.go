package parser

import (
	"context"
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"secwalk/internal/domain/core/endpoint"
	"secwalk/internal/domain/core/toolkit/secdocx/format"
	"secwalk/internal/domain/core/xfile"
	"strings"
)

// storeImageToMinio 简化的图片存储方法
// 从文档获取图片 → 转成xfile → 存储到MinIO
// 失败就返回NULL，不影响分析流程
func storeImageToMinio(ctx context.Context, imageData []byte, sourcePrefix string) *xfile.File {
	// 如果没有图片数据，直接返回NULL
	if len(imageData) == 0 {
		return nil
	}

	// 生成随机文件名，符合xfile格式
	fileName := generateRandomImageFileName(sourcePrefix)

	// 创建xfile对象
	xf := xfile.New(fileName, imageData)
	xf.SetType("image") // 设置为图片类型

	// 尝试存储到MinIO，使用defer recover来处理panic
	var storageSuccess bool
	func() {
		defer func() {
			if r := recover(); r != nil {
				// 发生panic，存储失败
				storageSuccess = false
			}
		}()

		// 尝试存储到MinIO
		if err := endpoint.PutFile(ctx, xf.String(), xf.Content); err != nil {
			// 存储失败
			storageSuccess = false
		} else {
			// 存储成功
			storageSuccess = true
		}
	}()

	// 存储成功，返回xfile对象
	return xf

	// 如果存储失败，返回NULL
	if !storageSuccess {
		return nil
	}

	return xf
}

// addImageToDocument 将图片添加到文档中
// 如果xf为NULL，则不添加任何内容
func addImageToDocument(docf *format.Document, xf *xfile.File, pageNum int) {
	if xf == nil {
		// 图片为NULL，不添加任何内容
		return
	}

	// 创建图片块并添加到文档
	imageBlock := format.NewImageBlock(xf.String(), 0, "")
	docf.AddBlockToPage(pageNum, imageBlock)

	// 添加为附件
	mimeType := "image/png"
	if strings.HasSuffix(strings.ToLower(xf.Name), ".jpg") || strings.HasSuffix(strings.ToLower(xf.Name), ".jpeg") {
		mimeType = "image/jpeg"
	}

	docf.AddAttachment(xf.String(), "image", xf.Name, mimeType, "stored_in_minio")
}

// generateRandomImageFileName 生成随机图片文件名
func generateRandomImageFileName(prefix string) string {
	// 生成8字节的随机数据
	bytes := make([]byte, 8)
	_, err := rand.Read(bytes)
	if err != nil {
		// 如果随机数生成失败，使用备用方案
		return fmt.Sprintf("%s_img_%d.png", prefix, len(bytes)*1000)
	}

	// 将字节转换为十六进制字符串
	randomStr := hex.EncodeToString(bytes)
	return fmt.Sprintf("%s_%s.png", prefix, randomStr)
}

// createMinimalPNGData 创建一个最小的PNG图片数据（1x1像素透明图片）
// 用作占位符图片
func createMinimalPNGData() []byte {
	// 1x1像素透明PNG图片的最小数据
	return []byte{
		0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, // PNG签名
		0x00, 0x00, 0x00, 0x0D, 0x49, 0x48, 0x44, 0x52, // IHDR chunk
		0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01, // 1x1像素
		0x08, 0x06, 0x00, 0x00, 0x00, 0x1F, 0x15, 0xC4, // RGBA, CRC
		0x89, 0x00, 0x00, 0x00, 0x0A, 0x49, 0x44, 0x41, // IDAT chunk
		0x54, 0x78, 0x9C, 0x63, 0x00, 0x01, 0x00, 0x00, // 压缩数据
		0x05, 0x00, 0x01, 0x0D, 0x0A, 0x2D, 0xB4, 0x00, // CRC
		0x00, 0x00, 0x00, 0x49, 0x45, 0x4E, 0x44, 0xAE, // IEND chunk
		0x42, 0x60, 0x82, // CRC
	}
}
