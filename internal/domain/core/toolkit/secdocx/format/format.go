package format

import (
	"bytes"
	"encoding/json"
	"fmt"
	"path/filepath"
	"sort"
	"strings"
)

// 文件类型常量
const (
	TypePDF  = "pdf"
	TypeDOC  = "doc"
	TypeXLS  = "xls"
	TypePPT  = "ppt"
	TypeTXT  = "txt"
	TypeJson = "json"
)

// 视图类型常量
const (
	SplitModePage = "page" // 页面视图
	SpiltModeToc  = "toc"  // 目录视图
)

// Document 简化的文档结构
type Document struct {
	Name        string                 `json:"name"`              // 文档名称
	Type        string                 `json:"type"`              // 文档类型（doc, pdf, xls等）
	Properties  map[string]interface{} `json:"properties"`        // 文档属性
	ViewType    string                 `json:"viewType"`          // 视图类型：page或toc
	Pages       []Page                 `json:"pages,omitempty"`   // 页面视图
	Headers     []Header               `json:"headers,omitempty"` // 目录视图
	Attachments []Attachment           `json:"attachments"`       // 附件
}

// Page 页面结构
type Page struct {
	Number int     `json:"number"` // 页码
	Blocks []Block `json:"blocks"` // 页面内容块
}

// Block 内容块结构
type Block struct {
	Type         string     `json:"type"`                // 块类型：text, table, image等
	Content      string     `json:"content,omitempty"`   // 文本内容
	HeadingLevel int        `json:"-"`                   // 标题级别，0表示正文
	HeadingID    string     `json:"headingID,omitempty"` // 标题ID
	Rows         int        `json:"rows,omitempty"`      // 表格行数
	Cols         int        `json:"cols,omitempty"`      // 表格列数
	Data         [][]string `json:"data,omitempty"`      // 表格数据
}

// Header 标题结构
type Header struct {
	ID       string   `json:"-"`                  // 标题ID
	Level    int      `json:"-"`                  // 标题级别
	Title    string   `json:"title"`              // 标题文本
	Content  []Block  `json:"content,omitempty"`  // 该标题下的内容
	Children []Header `json:"children,omitempty"` // 子标题
}

// Attachment 附件
type Attachment struct {
	ID       string `json:"id"`       // 附件ID
	Type     string `json:"type"`     // 附件类型（image, file等）
	Name     string `json:"name"`     // 附件名称
	MimeType string `json:"mimeType"` // MIME类型
	Data     string `json:"data"`     // Base64编码的数据或引用路径
}

// NewDocument 创建新的文档
func NewDocument(name, docType string) *Document {
	return &Document{
		Name:        name,
		Type:        docType,
		Properties:  make(map[string]interface{}),
		ViewType:    SplitModePage, // 默认使用页面视图
		Pages:       make([]Page, 0),
		Headers:     make([]Header, 0),
		Attachments: make([]Attachment, 0),
	}
}

// AddPage 添加页面
func (d *Document) AddPage(pageNumber int) *Page {
	// 检查页面是否已存在
	for i, page := range d.Pages {
		if page.Number == pageNumber {
			return &d.Pages[i]
		}
	}

	// 创建新页面
	newPage := Page{
		Number: pageNumber,
		Blocks: make([]Block, 0),
	}
	d.Pages = append(d.Pages, newPage)
	return &d.Pages[len(d.Pages)-1]
}

// AddBlockToPage 向页面添加内容块
func (d *Document) AddBlockToPage(pageNumber int, block Block) error {
	// 查找页面
	var targetPage *Page
	for i, page := range d.Pages {
		if page.Number == pageNumber {
			targetPage = &d.Pages[i]
			break
		}
	}

	// 如果页面不存在，创建新页面
	if targetPage == nil {
		targetPage = d.AddPage(pageNumber)
	}

	// 添加内容块
	targetPage.Blocks = append(targetPage.Blocks, block)
	return nil
}

// NewTextBlock 创建新的文本块
func NewTextBlock(content string, headingLevel int, headingID string) Block {
	return Block{
		Type:         "text",
		Content:      content,
		HeadingLevel: headingLevel,
		HeadingID:    headingID,
	}
}

// NewTableBlock 创建新的表格块
func NewTableBlock(rows, cols int, data [][]string) Block {
	return Block{
		Type:         "table",
		HeadingLevel: 0,
		Rows:         rows,
		Cols:         cols,
		Data:         data,
	}
}

// NewImageBlock 创建新的图片块
func NewImageBlock(content string, headingLevel int, headingID string) Block {
	return Block{
		Type:         "image",
		Content:      content,
		HeadingLevel: headingLevel,
		HeadingID:    headingID,
	}
}

// AddAttachment 添加附件
func (d *Document) AddAttachment(id, attachType, name, mimeType, data string) {
	d.Attachments = append(d.Attachments, Attachment{
		ID:       id,
		Type:     attachType,
		Name:     name,
		MimeType: mimeType,
		Data:     data,
	})
}

// SetViewType 设置视图类型
func (d *Document) SetViewType(viewType string) {
	d.ViewType = viewType
}

// GetViewType 获取视图类型
func (d *Document) GetViewType() string {
	return d.ViewType
}

// HeadingInfo 标题信息
type HeadingInfo struct {
	Level       int
	Title       string
	ID          string
	Page        int
	Index       int // 在原始列表中的索引
	Content     []Block
	ParentIndex int // 父标题的索引
}

// BuildTocViewFromPageView 从页面视图构建目录层级视图
func (d *Document) BuildTocViewFromPageView() {
	// 先对页面进行排序
	d.sortPages()

	// 清空现有的目录层级视图
	d.Headers = make([]Header, 0)

	// 收集所有标题块
	headings := []HeadingInfo{}

	// 当前处理的标题
	var currentHeading *HeadingInfo

	// 遍历所有页面和内容块
	for _, page := range d.Pages {
		for _, block := range page.Blocks {
			// 如果是标题
			if block.HeadingLevel > 0 {
				// 生成标题ID
				headingID := block.HeadingID
				if headingID == "" {
					// 生成格式为 h{level}.{index} 的ID
					headingID = fmt.Sprintf("h%d.%d", block.HeadingLevel, len(headings)+1)
				}

				// 创建新的标题信息
				currentHeading = &HeadingInfo{
					Level:       block.HeadingLevel,
					Title:       block.Content,
					ID:          headingID,
					Page:        page.Number,
					Index:       len(headings),
					Content:     []Block{},
					ParentIndex: -1, // 初始化为-1，表示没有父标题
				}

				// 添加到标题列表
				headings = append(headings, *currentHeading)
			} else if len(headings) > 0 {
				// 如果不是标题，但有已存在的标题，将内容添加到最后一个标题下
				// 但要避免重复添加表格数据
				if block.Type != "table" || !ContainsTableData(headings[len(headings)-1].Content, block) {
					headings[len(headings)-1].Content = append(headings[len(headings)-1].Content, block)
				}
			} else {
				// 如果没有当前标题上下文，创建一个默认的标题
				defaultHeaderID := fmt.Sprintf("h0.%d", len(headings)+1)

				// 创建内容列表，避免重复表格
				var content []Block
				if block.Type != "table" {
					content = []Block{block}
				} else {
					// 对于表格，检查是否已经存在
					content = []Block{block}
				}

				currentHeading = &HeadingInfo{
					Level:       0,
					Title:       "Uncategorized Content",
					ID:          defaultHeaderID,
					Page:        page.Number,
					Index:       len(headings),
					Content:     content,
					ParentIndex: -1,
				}

				// 添加到标题列表
				headings = append(headings, *currentHeading)
			}
		}
	}

	// 根据文档大纲结构确定标题的父子关系
	for i := 0; i < len(headings); i++ {
		// 对于每个标题，找到它的父标题
		if headings[i].Level <= 1 {
			// 第一级标题没有父标题
			continue
		}

		// 向前查找最近的上一级标题作为父标题
		// 例如，对于级别为3的标题，应该找到最近的级别为2的标题
		parentLevel := headings[i].Level - 1
		for j := i - 1; j >= 0; j-- {
			if headings[j].Level == parentLevel {
				headings[i].ParentIndex = j
				break
			}
		}

		// 如果没有找到直接父级，则尝试找更高层级的标题
		if headings[i].ParentIndex == -1 {
			for j := i - 1; j >= 0; j-- {
				if headings[j].Level < headings[i].Level {
					headings[i].ParentIndex = j
					break
				}
			}
		}
	}

	// 构建标题树
	// 首先找出所有的一级标题
	for i := 0; i < len(headings); i++ {
		if headings[i].Level <= 1 {
			// 创建一级标题
			header := Header{
				ID:       headings[i].ID,
				Level:    headings[i].Level,
				Title:    headings[i].Title,
				Content:  headings[i].Content,
				Children: []Header{},
			}

			// 递归添加子标题
			addChildHeaders(&header, headings, i)

			// 添加到文档的标题列表
			d.Headers = append(d.Headers, header)
		}
	}

	// 设置视图类型为目录视图
	d.ViewType = SpiltModeToc
}

// ContainsTableData 检查内容列表中是否已经包含相同的表格数据
func ContainsTableData(contents []Block, tableBlock Block) bool {
	if tableBlock.Type != "table" {
		return false
	}

	// 检查是否已经存在相同的表格
	for _, content := range contents {
		if content.Type == "table" &&
			content.Rows == tableBlock.Rows &&
			content.Cols == tableBlock.Cols {
			// 比较表格数据是否相同
			if len(content.Data) == len(tableBlock.Data) {
				same := true
				for i, row := range content.Data {
					if len(row) != len(tableBlock.Data[i]) {
						same = false
						break
					}
					for j, cell := range row {
						if cell != tableBlock.Data[i][j] {
							same = false
							break
						}
					}
					if !same {
						break
					}
				}
				if same {
					return true
				}
			}
		}
	}
	return false
}

// addChildHeaders 递归添加子标题
func addChildHeaders(parent *Header, headings []HeadingInfo, parentIndex int) {
	// 找出所有父标题索引为 parentIndex 的标题
	for i := 0; i < len(headings); i++ {
		if headings[i].ParentIndex == parentIndex {
			// 创建子标题
			child := Header{
				ID:       headings[i].ID,
				Level:    headings[i].Level,
				Title:    headings[i].Title,
				Content:  headings[i].Content,
				Children: []Header{},
			}

			// 递归添加子标题的子标题
			addChildHeaders(&child, headings, i)

			// 添加到父标题的子标题列表
			parent.Children = append(parent.Children, child)
		}
	}
}

// FindOrCreateHeaderItem 查找或创建标题项
func (d *Document) FindOrCreateHeaderItem(headingID string, level int, title string) *Header {
	// 解析标题ID，确定层级关系
	// 例如：h1.2.3 表示第1个一级标题下的第2个二级标题下的第3个三级标题

	// 防止无限递归
	if level <= 0 {
		// 如果层级小于等于0，创建一个默认的顶级标题
		level = 1
		if headingID == "" {
			headingID = fmt.Sprintf("h%d.%d", level, len(d.Headers)+1)
		}
	}

	// 如果是顶级标题
	if level == 1 {
		// 查找顶级标题
		for i, item := range d.Headers {
			if item.ID == headingID {
				// 如果标题存在但标题为空，更新标题
				if item.Title == "" && title != "" {
					d.Headers[i].Title = title
				}
				return &d.Headers[i]
			}
		}

		// 创建新的顶级标题
		newItem := Header{
			ID:       headingID,
			Level:    level,
			Title:    title,
			Content:  make([]Block, 0),
			Children: make([]Header, 0),
		}
		d.Headers = append(d.Headers, newItem)
		return &d.Headers[len(d.Headers)-1]
	}

	// 如果是子标题，需要找到父标题
	parentID := getParentID(headingID)
	parentLevel := level - 1

	// 防止父ID与当前ID相同导致的无限递归
	if parentID == headingID {
		// 如果父ID与当前ID相同，则将其作为顶级标题处理
		return d.FindOrCreateHeaderItem(headingID, 1, title)
	}

	// 查找父标题
	parent := d.FindOrCreateHeaderItem(parentID, parentLevel, "") // 父标题的title暂时为空，后续会填充

	// 在父标题的子标题中查找
	for i, child := range parent.Children {
		if child.ID == headingID {
			// 如果标题存在但标题为空，更新标题
			if child.Title == "" && title != "" {
				parent.Children[i].Title = title
			}
			return &parent.Children[i]
		}
	}

	// 创建新的子标题
	newItem := Header{
		ID:       headingID,
		Level:    level,
		Title:    title,
		Content:  make([]Block, 0),
		Children: make([]Header, 0),
	}
	parent.Children = append(parent.Children, newItem)
	return &parent.Children[len(parent.Children)-1]
}

// AddBlockToHeader 向标题添加内容块
func (d *Document) AddBlockToHeader(headingID string, block Block) error {
	// 查找标题项
	var targetHeader *Header
	var findHeader func(items []Header) *Header

	findHeader = func(items []Header) *Header {
		for i, item := range items {
			if item.ID == headingID {
				return &items[i]
			}
			if len(item.Children) > 0 {
				if found := findHeader(item.Children); found != nil {
					return found
				}
			}
		}
		return nil
	}

	targetHeader = findHeader(d.Headers)
	if targetHeader == nil {
		return fmt.Errorf("header with ID %s not found", headingID)
	}

	// 添加内容块
	targetHeader.Content = append(targetHeader.Content, block)
	return nil
}

// getParentID 从标题ID获取父标题ID
// 例如：h1.2.3 -> h1.2
func getParentID(id string) string {
	lastDot := -1
	for i := len(id) - 1; i >= 0; i-- {
		if id[i] == '.' {
			lastDot = i
			break
		}
	}
	if lastDot == -1 {
		return id // 没有父ID，返回原ID
	}
	return id[:lastDot]
}

// ToJSON 将文档转换为JSON字符串
func (d *Document) ToJSON() (string, error) {
	// 先对页面进行排序
	d.sortPages()

	// 使用标准JSON编码器
	data, err := json.MarshalIndent(d, "", "  ")
	if err != nil {
		return "", err
	}
	return string(data), nil
}

// sortPages 对页面按页码排序
func (d *Document) sortPages() {
	// 使用内置的sort包对页面进行排序
	sort.Slice(d.Pages, func(i, j int) bool {
		return d.Pages[i].Number < d.Pages[j].Number
	})
}

// DocumentFromJSON 从JSON字符串创建Document
func DocumentFromJSON(jsonStr string) (*Document, error) {
	var doc Document
	err := json.Unmarshal([]byte(jsonStr), &doc)
	if err != nil {
		return nil, fmt.Errorf("解析JSON失败: %v", err)
	}
	return &doc, nil
}

// NewFile 创建新的文件对象
func NewFile(data []byte, target string) *File {
	return &File{
		Target: target,
		reader: &bytes.Buffer{},
		data:   data,
	}
}

// File 文件对象
type File struct {
	Target string
	reader *bytes.Buffer
	data   []byte
}

// GetReader 获取文件读取器
func (f *File) GetReader() *bytes.Buffer {
	if f.reader.Len() == 0 && len(f.data) > 0 {
		f.reader.Write(f.data)
	}
	return f.reader
}

// GetName 获取文件名
func (f *File) GetName() string {
	return filepath.Base(f.Target)
}

// Printf 输出文档内容为文本格式
func (d *Document) Printf() string {
	// 先对页面进行排序
	d.sortPages()

	var result string
	result += fmt.Sprintf("文档名称: %s\n", d.Name)
	result += fmt.Sprintf("文档类型: %s\n", d.Type)
	result += fmt.Sprintf("视图类型: %s\n\n", d.ViewType)

	if d.ViewType == SplitModePage {
		// 按页输出
		for _, page := range d.Pages {
			result += fmt.Sprintf("===== 第 %d 页 =====\n", page.Number)
			for _, block := range page.Blocks {
				switch block.Type {
				case "text":
					result += block.Content + "\n"
				case "table":
					// 输出表格，带表格格式使用(|)
					for _, row := range block.Data {
						line := ""
						for _, cell := range row {
							line += fmt.Sprintf("|%s", cell)
						}
						result += fmt.Sprintf("%s|\n", line)
					}
				case "image":
					result += "[图片: " + block.Content + "]\n"
				}
			}
			result += "\n"
		}
	} else if d.ViewType == SpiltModeToc {
		// 按目录层级输出
		var printHeader func(header Header, level int) string
		printHeader = func(header Header, level int) string {
			indent := strings.Repeat("  ", level-1)
			str := fmt.Sprintf("%s%s\n", indent, header.Title)

			// 输出内容
			for _, content := range header.Content {
				switch content.Type {
				case "text":
					str += fmt.Sprintf("%s  %s\n", indent, content.Content)
				case "table":
					// 输出表格，带表格格式使用(|)
					for _, row := range content.Data {
						line := ""
						for _, cell := range row {
							line += fmt.Sprintf("|%s", cell)
						}
						str += fmt.Sprintf("%s  %s|\n", indent, line)
					}
				case "image":
					str += fmt.Sprintf("%s  [图片: %s]\n", indent, content.Content)
				}
			}

			// 递归输出子标题
			for _, child := range header.Children {
				str += printHeader(child, level+1)
			}
			return str
		}

		for _, header := range d.Headers {
			result += printHeader(header, 1)
		}
	}

	return result
}
