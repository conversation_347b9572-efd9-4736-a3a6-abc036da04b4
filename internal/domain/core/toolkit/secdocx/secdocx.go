package secdocx

import (
	"context"
	"fmt"
	"secwalk/internal/domain/core/schema"
	"secwalk/internal/domain/core/toolkit/secdocx/format"
	"secwalk/internal/domain/core/toolkit/secdocx/parser"
)

const (
	ParamFileType  = "filetype"
	ParamSplitMode = "split_mode"
	ParamFileName  = "filename"
)

// NewToolkit creates a new document parsing toolkit
func NewToolkit() schema.Toolkit {
	return schema.Toolkit{
		ID:          "f8d92e7a-b3c5-4a1d-9e87-6f4a2b5c8d3e",
		Name:        "文档解析器",
		Description: "解析各种文档格式（PDF、Word、Excel、PowerPoint）并提取其内容。",
		Type:        schema.ToolkitTypeSystem,
		Tools: []schema.ToolConfig{
			{
				ID:          "a7c3e9d1-5b2f-4c8d-9a6e-3f1d2b5c8a7e",
				Name:        "文档解析工具",
				Description: "解析文档并提取文本、表格内容。支持PDF、DOCX、XLSX、JSON、TXT和PPTX格式。(暂不支持较早版本的Office文档解析，如xls,ppt,doc)",
				InputParameters: []schema.Parameter{
					{
						Name:        ParamFileName,
						Description: "文档文件",
						Type:        schema.TypeFile,
						Required:    true,
					},
					{
						Name:        ParamFileType,
						Description: "文档类型（pdf、docx、xlsx、pptx、json、txt），如果不提供将根据文件扩展名推断",
						Type:        schema.TypeString,
						Required:    false,
					},
					{
						Name:         ParamSplitMode,
						Description:  "输出类型（page：按页输出，toc：按标题层级输出），默认为page",
						Type:         schema.TypeString,
						Required:     false,
						DefaultValue: format.SplitModePage,
					},
				},
				Creator: New,
			},
		},
	}
}

type Secdocx struct {
	cfg schema.ToolConfig
}

// New creates a new document parsing tool
func New(opts ...schema.ToolOption) (schema.Tool, error) {
	var cfg schema.ToolConfig
	for _, opt := range opts {
		opt(&cfg)
	}
	return &Secdocx{cfg: cfg}, nil
}

// Config returns the tool configuration
func (d *Secdocx) Config() schema.ToolConfig {
	return d.cfg
}

// Call executes the document parsing
func (d *Secdocx) Call(ctx context.Context, inputs map[string]any,
	opts ...schema.CallOption) (string, error) {
	opt := schema.NewCallOptions()
	for _, o := range opts {
		o(opt)
	}

	values := make(map[string]string)
	for _, v := range d.cfg.InputParameters {
		values[v.Name] = ""
		value, err := v.GetValue(inputs)
		if err != nil {
			if !v.Required {
				continue
			}
			return "", fmt.Errorf("secdcox missing required parameter: %s", v.Name)
		}
		values[v.Name] = value.String()
	}

	// 创建解析器选项
	options := []parser.Option{
		parser.WithFileType(values[ParamFileType], values[ParamFileName]),
		parser.WithSplitMode(values[ParamSplitMode]),
		parser.WithEXT(opt.EXT),
	}

	// 创建解析器
	p, err := parser.New(values[ParamFileName], options...)
	if err != nil {
		return "", fmt.Errorf("create doc parser fail: %w", err)
	}

	// 解析文档
	output, err := p.Parse(ctx)
	if err != nil {
		return "", err
	}

	return output, nil
}
