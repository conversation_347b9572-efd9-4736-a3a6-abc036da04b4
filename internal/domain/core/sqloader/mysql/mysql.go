package mysql

import (
	"context"
	"database/sql"
	"fmt"
	"secwalk/internal/domain/core/sqloader"
	"strings"

	_ "github.com/go-sql-driver/mysql"
)

type Client struct {
	*sql.DB
}

func New(dsn string) (sqloader.SQLoader, error) {
	db, err := sql.Open(sqloader.DialectMysql, dsn)
	if err != nil {
		return nil, err
	}
	db.SetMaxOpenConns(32)

	return &Client{DB: db}, nil
}

func (c *Client) Dialect() string {
	return sqloader.DialectMysql
}

func (c *Client) Query(ctx context.Context, query string, args ...any) ([]string, [][]string, error) {
	rows, err := c.QueryContext(ctx, query, args...)
	if err != nil {
		return nil, nil, err
	}
	defer rows.Close()

	cols, err := rows.Columns()
	if err != nil {
		return nil, nil, err
	}

	results := make([][]string, 0)
	for rows.Next() {
		rowStrs := make([]sql.NullString, len(cols))
		rowPtrs := make([]any, 0)
		for i := range rowStrs {
			rowPtrs = append(rowPtrs, &rowStrs[i])
		}

		if err := rows.Scan(rowPtrs...); err != nil {
			return nil, nil, err
		}

		row := make([]string, 0)
		for _, v := range rowStrs {
			row = append(row, v.String)
		}
		results = append(results, row)
	}

	return cols, results, nil
}

func (c *Client) TableNames(ctx context.Context) ([]string, error) {
	_, result, err := c.Query(ctx, "SHOW TABLES")
	if err != nil {
		return nil, err
	}

	tables := make([]string, 0)
	for _, row := range result {
		tables = append(tables, row[0])
	}
	return tables, nil
}

func (c *Client) TableInfo(ctx context.Context, tb sqloader.Table) (string, []string, error) {
	var sqlang string

	params := "TABLE_NAME,COLUMN_NAME,DATA_TYPE,IS_NULLABLE,COLUMN_DEFAULT,COLUMN_COMMENT"
	fields := []string{}
	for _, f := range tb.Fields {
		fields = append(fields, fmt.Sprintf("'%s'", f.Name))
	}

	if len(fields) > 0 {
		sqlang = fmt.Sprintf(
			"SELECT %s FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='%s' AND COLUMN_NAME IN (%s)",
			params, tb.Name, strings.Join(fields, ","))

	} else {
		sqlang = fmt.Sprintf(
			"SELECT %s FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='%s'", params, tb.Name)
	}

	_, result, err := c.Query(ctx, sqlang)
	if err != nil {
		return "", nil, err
	}

	if len(result) == 0 {
		return "", nil, sqloader.ErrTableNotFound
	}

	if len(result[0]) < 6 {
		return "", nil, sqloader.ErrInvalidResult
	}

	fields = []string{}
	res := fmt.Sprintf("TABLE NAME: %s\nTABLE DESCRIPTION: %s\nTABLE INFORMATION SCHEMA: \n|%-15s|%-15s|%-15s|%-15s|%-15s|\n",
		tb.Name, tb.Description, "COLUMN_NAME", "DATA_TYPE", "IS_NULLABLE", "COLUMN_DEFAULT", "COLUMN_COMMENT")
	for _, col := range result {
		fields = append(fields, fmt.Sprintf("`%s`", col[1]))
		comment := col[5]
		fd, err := tb.GetField(col[1])
		if err == nil && fd != nil && len(fd.Comment) > 0 {
			comment = fd.Comment
		}
		res += fmt.Sprintf("|%-15s|%-15s|%-15s|%-15s|%-15s|\n", col[1], col[2], col[3], col[4], comment)
	}

	return res, fields, nil
}

func (c *Client) Close() error {
	return c.DB.Close()
}
