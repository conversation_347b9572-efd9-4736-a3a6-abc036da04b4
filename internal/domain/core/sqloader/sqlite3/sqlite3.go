package sqlite3

import (
	"context"
	"database/sql"
	"fmt"
	"secwalk/internal/domain/core/sqloader"

	_ "github.com/mattn/go-sqlite3"
)

type Client struct {
	*sql.DB
}

func New(dsn string) (sqloader.SQLoader, error) {
	db, err := sql.Open(sqloader.DialectSqlite3, dsn)
	if err != nil {
		return nil, err
	}
	db.SetMaxOpenConns(1)

	return &Client{DB: db}, nil
}

func (c *Client) Dialect() string {
	return sqloader.DialectSqlite3
}

func (c *Client) Query(ctx context.Context, query string, args ...any) ([]string, [][]string, error) {
	rows, err := c.QueryContext(ctx, query, args...)
	if err != nil {
		return nil, nil, err
	}
	defer rows.Close()

	cols, err := rows.Columns()
	if err != nil {
		return nil, nil, err
	}

	results := make([][]string, 0)
	for rows.Next() {
		rowStrs := make([]sql.NullString, len(cols))
		rowPtrs := make([]any, 0)
		for i := range rowStrs {
			rowPtrs = append(rowPtrs, &rowStrs[i])
		}

		if err := rows.Scan(rowPtrs...); err != nil {
			return nil, nil, err
		}

		row := make([]string, 0)
		for _, v := range rowStrs {
			row = append(row, v.String)
		}
		results = append(results, row)
	}

	return cols, results, nil
}

func (c *Client) TableNames(ctx context.Context) ([]string, error) {
	_, result, err := c.Query(ctx, "SELECT name FROM sqlite_master WHERE type='table';")
	if err != nil {
		return nil, err
	}

	tables := make([]string, 0)
	for _, row := range result {
		tables = append(tables, row[0])
	}
	return tables, nil
}

func (c *Client) TableInfo(ctx context.Context, tb sqloader.Table) (string, []string, error) {
	_, result, err := c.Query(ctx, fmt.Sprintf("PRAGMA table_info(%s);", tb.Name))
	if err != nil {
		return "", nil, err
	}

	if len(result) == 0 {
		return "", nil, sqloader.ErrTableNotFound
	}

	fields := []string{}
	res := fmt.Sprintf("TABLE NAME: %s\nTABLE DESCRIPTION: %s\nTABLE INFORMATION SCHEMA: \n|%-15s|%-15s|%-15s|%-15s|%-15s|\n",
		tb.Name, tb.Description, "COLUMN_NAME", "DATA_TYPE", "NOTNULL", "COLUMN_DEFAULT", "COLUMN_COMMENT")
	for _, col := range result {
		if len(col) < 6 {
			return "", nil, sqloader.ErrInvalidResult
		}

		fd, err := tb.GetField(col[1])
		if len(tb.Fields) == 0 || err == nil {
			fields = append(fields, col[1])
			comment := ""
			if fd != nil {
				comment = fd.Comment
			}

			res += fmt.Sprintf("|%-15s|%-15s|%-15s|%-15s|%-15s|\n", col[1], col[2], col[3], col[4], comment)
		}
	}

	return res, fields, nil
}

func (c *Client) Close() error {
	return c.DB.Close()
}
