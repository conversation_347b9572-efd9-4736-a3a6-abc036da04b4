package sqloader

import (
	"context"
	"fmt"
)

const (
	DialectMysql   = "mysql"
	DialectSqlite3 = "sqlite3"
)

var (
	ErrUnknownDialect = fmt.<PERSON><PERSON><PERSON>("unknown dialect")
	ErrTableNotFound  = fmt.<PERSON><PERSON><PERSON>("table not found")
	ErrInvalidResult  = fmt.<PERSON><PERSON>rf("invalid result")
)

type SQLoader interface {
	Dialect() string
	Query(context.Context, string, ...any) (cols []string, results [][]string, err error)
	TableNames(context.Context) ([]string, error)
	TableInfo(context.Context, Table) (string, []string, error)
	Close() error
}

type Table struct {
	Name        string  `json:"name,omitempty"`
	Description string  `json:"description"`
	Fields      []Field `json:"fields,omitempty"`
}

type Field struct {
	Name    string `json:"name,omitempty"`
	Comment string `json:"comment,omitempty"`
}

func (tb *Table) GetField(n string) (*Field, error) {
	for i, f := range tb.Fields {
		if f.Name == n {
			return &tb.Fields[i], nil
		}
	}
	return nil, fmt.<PERSON><PERSON><PERSON>("not found filed: %s", n)
}
