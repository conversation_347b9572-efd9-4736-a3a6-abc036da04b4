package database

import (
	"context"
	"fmt"
	"secwalk/internal/domain/core/sqloader"
	"secwalk/internal/domain/core/sqloader/mysql"
	"secwalk/internal/domain/core/sqloader/sqlite3"
	"strings"
)

const sampleNumber = 3

type Loader struct {
	client sqloader.SQLoader
	tables []sqloader.Table
	fields []string
	topk   int
}

func NewLoader(dialect, dsn string, tables []sqloader.Table) (*Loader, error) {
	var client sqloader.SQLoader
	var err error

	switch dialect {
	case sqloader.DialectMysql:
		client, err = mysql.New(dsn)
	case sqloader.DialectSqlite3:
		client, err = sqlite3.New(dsn)
	default:
		return nil, sqloader.ErrUnknownDialect
	}
	if err != nil {
		return nil, err
	}

	return &Loader{
		client: client,
		tables: tables,
		topk:   sampleNumber,
	}, nil
}

func (l *Loader) Dialect() string {
	return l.client.Dialect()
}

func (l *Loader) TopK() int {
	return l.topk
}

func (l *Loader) Query(ctx context.Context, query string) (string, error) {
	// 不允许使用SELECT *
	if strings.HasPrefix(strings.ToLower(query), "select *") && len(l.fields) > 0 {
		query = fmt.Sprintf("SELECT %s%s", strings.Join(l.fields, ","), query[8:])
	}

	cols, results, err := l.client.Query(ctx, query)
	if err != nil {
		return "", err
	}

	str := strings.Join(cols, "\t") + "\n"
	if len(results) == 0 {
		return "no data was queried", nil
	}
	for _, row := range results {
		str += strings.Join(row, "\t") + "\n"
	}
	return str, nil
}

func (l *Loader) TableInfos(ctx context.Context) (string, error) {
	var res string

	for _, tb := range l.tables {
		info, fields, err := l.client.TableInfo(ctx, tb)
		if err != nil {
			return res, err
		}
		l.fields = fields

		// sqlfields := ""
		// if len(fields) > 0 {
		// 	sqlfields = strings.Join(fields, ",")
		// } else {
		// 	sqlfields = "*"
		// }

		// cols, err := l.Query(ctx, fmt.Sprintf("SELECT %s FROM %s LIMIT %d", sqlfields, tb.Name, l.topk))
		// if err != nil {
		// 	return "", err
		// }

		res += info
		// res += fmt.Sprintf("/*\n%d rows from %s table:\n%s*/ \n\n", l.topk, tb.Name, cols)
	}

	return res, nil
}
