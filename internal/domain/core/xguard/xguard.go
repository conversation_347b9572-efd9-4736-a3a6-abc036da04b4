package xguard

import (
	"context"
	"fmt"
	"secwalk/internal/config"
	"secwalk/internal/domain/core/agent"
	"secwalk/internal/domain/core/memory"
	"secwalk/internal/domain/core/schema"
	"secwalk/internal/domain/core/schema/ext"
	"secwalk/internal/domain/repo"
	"secwalk/internal/domain/repo/ent"
	"secwalk/pkg/ac"
	"secwalk/pkg/logger"
	"secwalk/pkg/selector"
	"secwalk/pkg/token"
	"strings"
	"sync"

	"github.com/sirupsen/logrus"
)

const (
	_XGuardAgentID  = "7d93486b-f4fe-4b74-b708-ab43dc285ae1"
	_FriendlyFormat = "抱歉哦~小恒无法给出针对这个问题的解答，此问题可能涉及【%s】相关敏感信息或潜在的违规内容，感谢理解。"
)

type XGuard struct {
	// 敏感词
	mx sync.RWMutex
	ac *ac.AC
	tk *token.Tiktoken
	ts map[string]*ent.Trait

	// 智能体
	agent *agent.Agent

	// 提示词泄露
}

func New(ctx context.Context, agentRepo *repo.AgentRepo,
	xguardRepo *repo.XGuardRepo) (*XGuard, error) {
	logrus.WithField(logger.KeyCategory, logger.CategorySystem).
		Infof("init trait xguard")
	ts, err := xguardRepo.QueryTrait(ctx)
	if err != nil {
		return nil, err
	}

	tk, err := token.NewTokenCounter()
	if err != nil {
		return nil, err
	}

	xg := &XGuard{
		mx: sync.RWMutex{},
		tk: tk,
	}

	at, err := agentRepo.QueryAndDecrypt(ctx, _XGuardAgentID)
	if err == nil {
		if err := schema.VerifyAgent(at, config.GetVersion()); err != nil {
			return nil, err
		}

		agent, err := agent.NewAgent(
			nil,
			nil,
			nil,
			ext.New(""),
			agent.WithAgentConfig(at),
			agent.WithMemory(memory.NewChatMemory()),
		)
		if err != nil {
			return nil, err
		}

		xg.agent = agent
	}

	return xg, xg.Reload(ts)
}

func (x *XGuard) Reload(traits []*ent.Trait) error {
	x.mx.Lock()
	defer x.mx.Unlock()

	var err error

	x.ts = make(map[string]*ent.Trait, len(traits))
	words := make([]string, 0, len(traits))
	for i, t := range traits {
		words = append(words, t.Text)
		x.ts[t.Text] = traits[i]
	}

	x.ac, err = ac.New(words)
	if err != nil {
		return err
	}

	return nil
}

func (x *XGuard) Match(ctx context.Context, text string) (*ent.Trait, string, bool) {
	x.mx.RLock()
	defer x.mx.RUnlock()

	if !config.GetFeature().UseXGuard {
		return nil, "", false
	}

	trait, ok := x.matchTokens(text)
	if !ok || trait.Level == schema.TraitLevelPass {
		return nil, "", false
	}

	if x.agent != nil && trait.Level == schema.TraitLevelWarn {
		answer, ok := x.matchAgent(ctx, text)
		return trait, answer, ok
	}

	return trait, fmt.Sprintf(_FriendlyFormat, trait.Type), true
}

// 流式分词检测
func (x *XGuard) matchTokens(text string) (*ent.Trait, bool) {
	tokens := x.tk.CutTokens(text)
	stream := x.newStream()

	for _, token := range tokens {
		word, ok := stream.check(token)
		if !ok {
			continue
		}
		trait, ok := x.ts[word]
		if !ok {
			continue
		}

		return trait, true
	}

	return nil, false
}

func (x *XGuard) newStream() *Stream {
	return &Stream{
		stream: x.ac.MultiPatternHitSearchStream(),
		prefix: "",
		xguard: x,
	}
}

type Stream struct {
	stream ac.SearchStream
	prefix string
	xguard *XGuard
}

func (p *Stream) check(token string) (string, bool) {
	hit, sub, ok := p.stream([]rune(token))
	if ok {
		keyword := ""
		if len(hit.Value) > 0 {
			keyword = string(hit.Value)
			if ok := p.doubleCheck(token, keyword); ok {
				return keyword, true
			}
		} else {
			p.prefix += token
			keyword = string([]rune(p.prefix)[hit.Begin : hit.End+1])
			if ok := p.doubleCheck(p.prefix, keyword); ok {
				return keyword, true
			}
		}
	}
	if sub {
		p.prefix += token
	} else {
		p.prefix = ""
	}
	return "", false
}

func (p *Stream) doubleCheck(content, keyword string) bool {
	if strings.TrimSpace(content) == strings.TrimSpace(keyword) {
		return true
	}

	// 判断边界
	var j int = 0
	keywords := []rune(keyword)
	words := p.xguard.tk.CutTokens(content)
CHECK:
	for _, word := range words {
		ws := []rune(word)
		if len(ws) == 0 {
			continue
		}

		for i := range ws {
			if ws[i] == keywords[j] {
				if j == 0 && i != 0 { // 匹配的是非首字母
					break CHECK
				}
				if len(keywords) == j+1 { // 匹配结束
					if len(ws) == i+1 { // word为尾字母
						return true
					}
					break CHECK
				}
				j++
			} else if j != 0 {
				break CHECK
			}
		}
	}

	for _, word := range words {
		_, ok := p.xguard.ac.MultiPatternHitSearch([]rune(word))
		if ok {
			return true
		}
	}

	return false
}

// 安检智能体检测
func (x *XGuard) matchAgent(ctx context.Context, text string) (string, bool) {
	gateway, err := selector.G().GatewayInstance()
	if err != nil {
		return "", false
	}

	res, err := x.agent.Call(
		ctx,
		map[string]any{"input": text},
		schema.WithAddress(gateway),
	)
	if err != nil {
		return "", false
	}

	var sensitive bool
	var friendres string
	var ok1, ok2 bool

	sensitive, ok1 = res.Results["sensitive"].(bool)
	friendres, ok2 = res.Results["friendly_response"].(string)
	if !ok1 || !ok2 {
		return "", false
	}

	return friendres, sensitive
}
