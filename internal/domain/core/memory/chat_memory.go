package memory

import (
	"context"
	"encoding/json"

	"secwalk/internal/domain/core/message"
	"secwalk/internal/domain/core/schema"
)

var _ Memory = &ChatMemory{}

// 基础对话记忆
type ChatMemory struct {
	ChatHistory message.ChatMessageHistory

	InputKey        string
	OutputKey       string
	MemoryKey       string
	UserPrefix      string
	AssistantPrefix string
}

func NewChatMemory(options ...ChatMemoryOption) *ChatMemory {
	memory := &ChatMemory{
		ChatHistory:     NewChatMessageHistory(),
		InputKey:        schema.DefaultInputKey,
		OutputKey:       "output",
		UserPrefix:      "user",
		AssistantPrefix: "assistant",
		MemoryKey:       schema.ReservedHistory,
	}
	for _, opt := range options {
		opt(memory)
	}

	return memory
}

func (m *ChatMemory) GetMemoryKey(context.Context) string {
	return m.MemoryKey
}

func (m *ChatMemory) MemoryVariables(context.Context) []string {
	return []string{m.MemoryKey}
}

// 按照MemoryKey值作为key，返回历史消息
func (m *ChatMemory) LoadMemoryVariables(ctx context.Context) (map[string]string, error) {
	messages, err := m.ChatHistory.Messages(ctx)
	if err != nil {
		return nil, err
	}

	return map[string]string{
		m.MemoryKey: message.GetBufferString(messages,
			message.WithUserPrefix(m.UserPrefix),
			message.WithAssistantPrefix(m.AssistantPrefix)),
	}, nil
}

func (m *ChatMemory) LoadMemoryMessages(ctx context.Context) ([]message.Message, error) {
	return m.ChatHistory.Messages(ctx)
}

func (m *ChatMemory) SaveMessage(ctx context.Context, message message.Message) error {
	return m.ChatHistory.AddMessage(ctx, message)
}

func (m *ChatMemory) SaveInput(ctx context.Context, inputs map[string]any) error {
	userInputValue, err := GetInputValue(inputs, m.InputKey)
	if err != nil {
		return err
	}
	return m.ChatHistory.AddUserMessage(ctx, userInputValue)
}

func (m *ChatMemory) SaveOutput(ctx context.Context, output string) error {
	return m.ChatHistory.AddAssistantMessage(ctx, output)
}

func (m *ChatMemory) Clear(ctx context.Context) error {
	return m.ChatHistory.Clear(ctx)
}

func GetInputValue(inputValues map[string]any, inputKey string) (string, error) {
	if len(inputKey) > 0 {
		inputValue, ok := inputValues[inputKey]
		if ok {
			return getInputValueReturnToString(inputValue)
		}
	}

	if len(inputValues) == 1 {
		for _, inputValue := range inputValues {
			if _, ok := inputValue.(string); ok {
				return getInputValueReturnToString(inputValue)
			}
		}
	}

	return getInputValueReturnToString(inputValues)
}

func getInputValueReturnToString(inputValue interface{}) (string, error) {
	switch value := inputValue.(type) {
	case string:
		return value, nil
	default:
		data, err := json.Marshal(value)
		return string(data), err
	}
}
