package memory

import (
	"context"
	"secwalk/internal/domain/core/message"
)

// Memory is the interface for memory in chains.
type Memory interface {
	Get<PERSON><PERSON>oryKey(ctx context.Context) string
	MemoryVariables(ctx context.Context) []string
	// Load
	LoadMemoryVariables(ctx context.Context) (map[string]string, error)
	LoadMemoryMessages(ctx context.Context) ([]message.Message, error)
	// Save
	SaveMessage(ctx context.Context, message message.Message) error
	SaveInput(ctx context.Context, inputs map[string]any) error
	SaveOutput(ctx context.Context, output string) error
	// Clear
	Clear(ctx context.Context) error
}
