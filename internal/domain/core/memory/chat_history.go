package memory

import (
	"context"

	"secwalk/internal/domain/core/message"
)

var _ message.ChatMessageHistory = &ChatMessageHistory{}

// 聊天历史可选参数
type ChatMessageHistoryOption func(m *ChatMessageHistory)

func WithPreviousMessages(previousMessages []message.Message) ChatMessageHistoryOption {
	return func(m *ChatMessageHistory) {
		m.messages = append(m.messages, previousMessages...)
	}
}

// 聊天历史记录
type ChatMessageHistory struct {
	messages []message.Message
}

func NewChatMessageHistory(opts ...ChatMessageHistoryOption) *ChatMessageHistory {
	h := &ChatMessageHistory{
		messages: make([]message.Message, 0),
	}
	for _, o := range opts {
		o(h)
	}

	return h
}

// 添加历史消息
func (h *ChatMessageHistory) AddMessage(_ context.Context, message message.Message) error {
	h.messages = append(h.messages, message)
	return nil
}

// 添加用户消息
func (h *ChatMessageHistory) AddUserMessage(_ context.Context, text string) error {
	h.messages = append(h.messages, message.NewUserChatMessage(text))
	return nil
}

// 添加助手消息
func (h *ChatMessageHistory) AddAssistantMessage(_ context.Context, text string) error {
	h.messages = append(h.messages, message.NewAssistantChatMessage(text))
	return nil
}

// 清理所有记录
func (h *ChatMessageHistory) Clear(_ context.Context) error {
	h.messages = make([]message.Message, 0)
	return nil
}

// 获取记录列表
func (h *ChatMessageHistory) Messages(_ context.Context) ([]message.Message, error) {
	return h.messages, nil
}

// 覆盖所有记录
func (h *ChatMessageHistory) SetMessages(_ context.Context, messages []message.Message) error {
	h.messages = messages
	return nil
}
