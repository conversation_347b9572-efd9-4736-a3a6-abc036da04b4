package memory

import (
	"context"
	"testing"

	"secwalk/internal/domain/core/message"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestChatMessageHistory(t *testing.T) {
	t.<PERSON>()

	h := NewChatMessageHistory()
	require.NoError(t, h.AddAssistantMessage(context.Background(), "foo"))
	require.NoError(t, h.AddUserMessage(context.Background(), "bar"))

	messages, err := h.Messages(context.Background())
	require.NoError(t, err)

	assert.Equal(t, []message.Message{
		message.NewAssistantChatMessage("foo"),
		message.NewUserChatMessage("bar"),
	}, messages)

	h = NewChatMessageHistory(
		WithPreviousMessages([]message.Message{
			message.NewAssistantChatMessage("foo"),
			message.NewSystemChatMessage("bar"),
		}),
	)
	require.NoError(t, h.AddUserMessage(context.Background(), "zoo"))

	messages, err = h.Messages(context.Background())
	require.NoError(t, err)

	assert.Equal(t, []message.Message{
		message.NewAssistantChatMessage("foo"),
		message.NewSystemChatMessage("bar"),
		message.NewUserChatMessage("zoo"),
	}, messages)
}
