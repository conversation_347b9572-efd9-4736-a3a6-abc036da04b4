package memory

import "secwalk/internal/domain/core/message"

type ChatMemoryOption func(m *ChatMemory)

func WithChatHistory(history message.ChatMessageHistory) ChatMemoryOption {
	return func(m *ChatMemory) {
		m.ChatHistory = history
	}
}

func WithInputKey(inputKey string) ChatMemoryOption {
	return func(m *ChatMemory) {
		m.InputKey = inputKey
	}
}

func WithOutputKey(outputKey string) ChatMemoryOption {
	return func(m *ChatMemory) {
		m.OutputKey = outputKey
	}
}

func WithMemoryKey(memoryKey string) ChatMemoryOption {
	return func(m *ChatMemory) {
		m.MemoryKey = memoryKey
	}
}

func WithHumanPrefix(humanPrefix string) ChatMemoryOption {
	return func(m *ChatMemory) {
		m.UserPrefix = humanPrefix
	}
}

func WithAssistantPrefix(aiPrefix string) ChatMemoryOption {
	return func(m *ChatMemory) {
		m.AssistantPrefix = aiPrefix
	}
}
