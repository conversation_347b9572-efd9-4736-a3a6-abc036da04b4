package schema

import (
	"secwalk/pkg/ac"
	"secwalk/pkg/token"
	"strings"
)

type TokenChecker struct {
	ac     *ac.AC
	tk     *token.Tiktoken
	stream ac.SearchStream
	prefix string
}

func NewTokenChecker(keywords ...string) (token<PERSON><PERSON>cker *TokenChecker) {
	ac, err := ac.New(keywords)
	if err != nil {
		return
	}

	tk, err := token.NewTokenCounter()
	if err != nil {
		return
	}

	return &TokenChecker{
		ac:     ac,
		tk:     tk,
		stream: ac.MultiPatternHitSearchStream(),
		prefix: "",
	}
}

func (p *TokenChecker) Check(token string) (string, bool, bool) {
	hit, sub, ok := p.stream([]rune(token))
	if ok {
		keyword := ""
		if len(hit.Value) > 0 {
			keyword = string(hit.Value)
			if ok := p.doubleCheck(token, keyword); ok {
				return keyword, false, true
			}
		} else {
			p.prefix += token
			keyword = string([]rune(p.prefix)[hit.Begin : hit.End+1])
			if ok := p.doubleCheck(p.prefix, keyword); ok {
				return keyword, false, true
			}
		}
	}
	if sub {
		p.prefix += token
		return "", sub, false
	} else {
		p.prefix = ""
		return "", sub, false
	}
}

func (p *TokenChecker) doubleCheck(content, keyword string) bool {
	if strings.TrimSpace(content) == strings.TrimSpace(keyword) {
		return true
	}

	// 判断边界
	var j int = 0
	keywords := []rune(keyword)
	words := p.tk.CutTokens(content)
CHECK:
	for _, word := range words {
		ws := []rune(word)
		if len(ws) == 0 {
			continue
		}

		for i := range ws {
			if ws[i] == keywords[j] {
				if j == 0 && i != 0 { // 匹配的是非首字母
					break CHECK
				}
				if len(keywords) == j+1 { // 匹配结束
					if len(ws) == i+1 { // word为尾字母
						return true
					}
					break CHECK
				}
				j++
			} else if j != 0 {
				break CHECK
			}
		}
	}

	for _, word := range words {
		_, ok := p.ac.MultiPatternHitSearch([]rune(word))
		if ok {
			return true
		}
	}

	return false
}
