package schema

import "errors"

var (
	ErrAgentID              = errors.New("the length of the agent ID must be between 4 and 128 characters")
	ErrAgentName            = errors.New("the length of the agent name must be between 2 and 128 characters")
	ErrAgentType            = errors.New("the agent type is invalid")
	ErrAgentVersion         = errors.New("the agent version number format is incorrect")
	ErrAgentDescription     = errors.New("the length of the agent description must be between 8 and 1024 characters")
	ErrAgentPrologue        = errors.New("the length of the agent prologue must be less than 1024 characters")
	ErrAgentCotVersion      = errors.New("the agent CoT version is invalid")
	ErrAgentPrompt          = errors.New("the length of the agent output expression must be less than 32k characters")
	ErrAgentEntryUnset      = errors.New("the agent entry point not set")
	ErrAgentEntryNotExist   = errors.New("the agent entry point not exist")
	ErrAgentNodeEmpty       = errors.New("the agent node is empty")
	ErrAgentNodeDuplicate   = errors.New("duplicate nodes exist in the agent")
	ErrAgentNodeNotExist    = errors.New("the workflow of the agent contains connections to non-existent nodes")
	ErrAgentEdgeSrcUnset    = errors.New("the agent edge src not set")
	ErrAgentEdgeSrcNotExist = errors.New("the agent edge src not exist")
	ErrAgentEdgeDstUnset    = errors.New("the agent edge dst not set")
	ErrAgentEdgeDstNotExist = errors.New("the agent edge dst not exist")
	ErrAgentEdgeDuplicate   = errors.New("duplicate edge exist in the agent")
	ErrAgentEdgeConflict    = errors.New("conflict edge exist in the agent")

	ErrNodeID                 = errors.New("the length of the node ID must be between 4 and 128 characters")
	ErrNodeName               = errors.New("the length of the node name must be between 2 and 128 characters")
	ErrNodeType               = errors.New("the node type is invalid")
	ErrNodeDescription        = errors.New("the length of the node description must be less than 1024 characters")
	ErrNodePrompt             = errors.New("the length of the node prompt must be less than 32k characters")
	ErrNodeToolOne            = errors.New("the node at least one tool must be configured")
	ErrNodeAgentOne           = errors.New("the node at least one agent must be configured")
	ErrNodeDBNotSet           = errors.New("the node database not configured")
	ErrNodeKBNotSet           = errors.New("the node knowledge base not configured")
	ErrNodeQuestionNameRepeat = errors.New("the node question name repeat")
	ErrNodeIterationValue     = errors.New("the node iteration value can not be empty")

	ErrComponentID = errors.New("the length of the component ID must be between 4 and 128 characters")

	ErrModelType           = errors.New("the model type is invalid")
	ErrModelGuidedDecoding = errors.New("the model only one guided decoding can be configured")

	ErrParameterName          = errors.New("the length of the parameter name must be between 1 and 128 characters")
	ErrParameterDescription   = errors.New("the length of parameter description must be between 2 and 1024 characters")
	ErrParameterType          = errors.New("the parameter type is invalid")
	ErrExprValueType          = errors.New("the expr type is invalid")
	ErrExprValueExpr          = errors.New("the expr expression is invalid")
	ErrParameterLocation      = errors.New("the parameter location is invalid")
	ErrParameterString        = errors.New("the value for the parameter must be of string type")
	ErrParameterInteger       = errors.New("the value for the parameter must be of integer type")
	ErrParameterBoolean       = errors.New("the value for the parameter must be of boolean type")
	ErrParameterfloat         = errors.New("the value for the parameter must be of float type")
	ErrParameterArray         = errors.New("the value for the parameter must be of array type")
	ErrParameterObject        = errors.New("the value for the parameter must be of object type")
	ErrParameterFiles         = errors.New("the value for the parameter must be of files type")
	ErrParameterValueNotfound = errors.New("the parameter value not found")

	ErrDBType             = errors.New("unsupported database type, currently supported types are MySQL and SQLite3")
	ErrDBDSN              = errors.New("the length of the database dsn must be less than 512")
	ErrDBTable            = errors.New("the database table not configured")
	ErrDBTableName        = errors.New("the length of the database table name must be between 2 and 128")
	ErrDBTableDescription = errors.New("the length of the database table description must be between 8 and 1024")

	ErrKBID          = errors.New("the length of the knowledge base ID must be between 2 and 128 characters")
	ErrKBDescription = errors.New("the length of the knowledge base description must be between 8 and 1024 characters")

	ErrPluginID           = errors.New("the length of the plugin ID must be between 4 and 128")
	ErrPluginName         = errors.New("the length of the plugin name must be between 2 and 128 characters")
	ErrPluginDescription  = errors.New("the length of the plugin description must be between 8 and 1024 characters")
	ErrPluginUrl          = errors.New("the length of the plugin url must be less than 1024")
	ErrAbilityID          = errors.New("the length of the ability ID must be between 4 and 128")
	ErrAbilityName        = errors.New("the length of the ability name must be between 2 and 128 characters")
	ErrAbilityDescription = errors.New("the length of the ability description must be between 8 and 1024 characters")
	ErrAbilityMethod      = errors.New("unsupported ability method, current supported GET and POST")
	ErrAbilityUrl         = errors.New("the length ability of the url must be less than 1024")
	ErrAbilityPath        = errors.New("the length ability of the path must be less than 1024")
	ErrAbilityLocalhost   = errors.New("the ability can not use localhost")

	ErrInvolvesSensitiveWord = errors.New("involves sensitive words")

	// 错误码分类
	ErrConfig               = errors.New("config error")
	ErrInputs               = errors.New("inputs error")
	ErrMiddleware           = errors.New("middleware error")
	ErrGateway              = errors.New("gateway error")
	ErrLicense              = errors.New("license error")
	ErrLicenseSecret        = errors.New("license secret invalid")
	ErrTaskID               = errors.New("task not found")
	ErrParameter            = errors.New("parameter error")
	ErrAbilityExecute       = errors.New("the ability execute failed")
	ErrAbilityExecuteResult = errors.New("the ability execute result failed")
	ErrLLMCall              = errors.New("llm call fail")

	// MCP
	ErrMCPServerConfig  = errors.New("mcp server config error")
	ErrMCPProxy         = errors.New("mcp proxy error")
	ErrMCPServerConnect = errors.New("connect mcp server error")
	ErrMCPServerRequest = errors.New("request mcp server error")
)
