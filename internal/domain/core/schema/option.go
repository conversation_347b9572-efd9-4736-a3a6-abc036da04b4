package schema

import (
	"secwalk/internal/domain/core/callback"
	"secwalk/internal/domain/core/schema/ext"
	"secwalk/pkg/selector"
)

type CallOption func(*CallOptions)

type CallOptions struct {
	// Dbapp
	Model             string             `json:"model,omitempty"`
	LoraType          string             `json:"lora_type,omitempty"`
	MaxTokens         int                `json:"max_tokens"`
	Temperature       float32            `json:"temperature"`
	TopK              int                `json:"top_k"`
	TopP              float32            `json:"top_p"`
	RepetitionPenalty float32            `json:"repetition_penalty"`
	StopWord          []string           `json:"stop_word,omitempty"`
	GuidedChoice      []string           `json:"guided_choice,omitempty"`
	GuidedJson        string             `json:"guided_json,omitempty"`
	GuidedRegex       string             `json:"guided_regex,omitempty"`
	LogProbs          int                `json:"logprobs,omitempty"`
	Seed              int                `json:"seed,omitempty"`
	Reasoning         bool               `json:"reasoning,omitempty"`
	Gateway           *selector.Instance `json:"gateway,omitempty"`

	// Openai
	PresencePenalty  float32 `json:"-"`
	FrequencyPenalty float32 `json:"-"`
	N                int     `json:"-"`

	// Chain
	UseMemory bool `json:"-"`
	LogMemory bool `json:"-"`

	// Produce
	Name                  string                      `json:"-"`
	EXT                   ext.ExtType                 `json:"-"`
	PID                   string                      `json:"-"`
	CID                   string                      `json:"-"`
	NodeID                string                      `json:"-"`
	MessageNodeID         string                      `json:"-"`
	Locals                map[string]any              `json:"-"`
	SystemPromptRender    string                      `json:"-"`
	SetSystemPromptRender bool                        `json:"-"`
	UserPromptRender      string                      `json:"-"`
	SetUserPromptRender   bool                        `json:"-"`
	MasterEntry           bool                        `json:"-"`
	OutputFinal           bool                        `json:"-"`
	CbSetVariable         callback.CbSetVariable      `json:"-"`
	FnStreaming           callback.FnStreaming        `json:"-"`
	FnReasoning           callback.FnStreaming        `json:"-"`
	FnStreamVerbose       callback.FnStreamVerbose    `json:"-"`
	FnStreamPreview       callback.FnStreamPreview    `json:"-"`
	FnStreamTokenUsage    callback.FnStreamTokenUsage `json:"-"`

	// action
	FnPromptGuard *LeakGuard    `json:"-"`
	TokenChecker  *TokenChecker `json:"-"`
}

func NewCallOptions() *CallOptions {
	return &CallOptions{
		EXT: ext.New(""),
	}
}

func WithModel(m string) CallOption {
	return func(o *CallOptions) {
		o.Model = m
	}
}

func WithLoraType(lt string) CallOption {
	return func(o *CallOptions) {
		o.LoraType = lt
	}
}

func WithMaxTokens(max_tokens int) CallOption {
	return func(cco *CallOptions) {
		cco.MaxTokens = max_tokens
	}
}

func WithTemperature(temperature float32) CallOption {
	return func(o *CallOptions) {
		o.Temperature = temperature
	}
}

func WithTopK(topK int) CallOption {
	return func(o *CallOptions) {
		o.TopK = topK
	}
}

func WithTopP(topP float32) CallOption {
	return func(o *CallOptions) {
		o.TopP = topP
	}
}

func WithRepetitionPenalty(repetitionPenalty float32) CallOption {
	return func(o *CallOptions) {
		o.RepetitionPenalty = repetitionPenalty
	}
}

func WithStopWords(stopWords []string) CallOption {
	return func(o *CallOptions) {
		o.StopWord = stopWords
	}
}

// openai
func WithPresencePenalty(presencePenalty float32) CallOption {
	return func(o *CallOptions) {
		o.PresencePenalty = presencePenalty
	}
}

func WithFrequencyPenalty(frequencyPenalty float32) CallOption {
	return func(o *CallOptions) {
		o.FrequencyPenalty = frequencyPenalty
	}
}

func WithN(n int) CallOption {
	return func(o *CallOptions) {
		o.N = n
	}
}

func WithGuidedChoices(choices []string) CallOption {
	return func(co *CallOptions) {
		co.GuidedChoice = choices
	}
}

func WithGuidedJson(jsonStr string) CallOption {
	return func(co *CallOptions) {
		co.GuidedJson = jsonStr
	}
}

func WithGuidedRegex(regexStr string) CallOption {
	return func(co *CallOptions) {
		co.GuidedRegex = regexStr
	}
}

func WithLogProbs(logProbs int) CallOption {
	return func(co *CallOptions) {
		co.LogProbs = logProbs
	}
}

func WithSeed(seed int) CallOption {
	return func(co *CallOptions) {
		co.Seed = seed
	}
}

func WithReasoning() CallOption {
	return func(co *CallOptions) {
		co.Reasoning = true
	}
}

func WithAddress(gateway *selector.Instance) CallOption {
	return func(o *CallOptions) {
		o.Gateway = gateway
	}
}

func WithUseMemory() CallOption {
	return func(o *CallOptions) {
		o.UseMemory = true
	}
}

func WithLogMemory() CallOption {
	return func(o *CallOptions) {
		o.LogMemory = true
	}
}

func WithName(n string) CallOption {
	return func(o *CallOptions) {
		o.Name = n
	}
}

func WithEXTString(e string) CallOption {
	return func(o *CallOptions) {
		o.EXT = ext.New(e)
	}
}

func WithEXT(ext ext.ExtType) CallOption {
	return func(co *CallOptions) {
		co.EXT = ext
	}
}

func WithXORG(xorg string) CallOption {
	return func(a *CallOptions) {
		if len(xorg) > 0 {
			a.EXT.SetValue(ext.EXTXORG, xorg)
		}
	}
}

func WithXUID(xuid string) CallOption {
	return func(a *CallOptions) {
		if len(xuid) > 0 {
			a.EXT.SetValue(ext.EXTXUID, xuid)
		}
	}
}

func WithPID(pid string) CallOption {
	return func(o *CallOptions) {
		o.PID = pid
	}
}

func WithCID(cid string) CallOption {
	return func(o *CallOptions) {
		o.CID = cid
	}
}

func WithMessageNodeID(messageNodeID string) CallOption {
	return func(co *CallOptions) {
		co.MessageNodeID = messageNodeID
	}
}

func WithNodeID(nodeId string) CallOption {
	return func(co *CallOptions) {
		co.NodeID = nodeId
	}
}

func WithLocals(locals map[string]any) CallOption {
	return func(co *CallOptions) {
		co.Locals = locals
	}
}

func WithSystemPromptRender(prompt string) CallOption {
	return func(co *CallOptions) {
		co.SystemPromptRender = prompt
		co.SetSystemPromptRender = true
	}
}

func WithUserPromptRender(prompt string) CallOption {
	return func(co *CallOptions) {
		co.UserPromptRender = prompt
		co.SetUserPromptRender = true
	}
}

func WithMasterEntry() CallOption {
	return func(co *CallOptions) {
		co.MasterEntry = true
	}
}

func WithFinalOut(b bool) CallOption {
	return func(co *CallOptions) {
		co.OutputFinal = b
	}
}

func WithFnPromptGuard(FnPromptGuard *LeakGuard) CallOption {
	return func(co *CallOptions) {
		co.FnPromptGuard = FnPromptGuard
	}
}

func WithCbSetVariable(cb callback.CbSetVariable) CallOption {
	return func(co *CallOptions) {
		co.CbSetVariable = cb
	}
}

func WithFnStreaming(fn callback.FnStreaming) CallOption {
	return func(o *CallOptions) {
		o.FnStreaming = fn
	}
}

func WithFnReasoning(fn callback.FnStreaming) CallOption {
	return func(o *CallOptions) {
		o.FnReasoning = fn
	}
}

func WithFnStreamVerbose(fn callback.FnStreamVerbose) CallOption {
	return func(o *CallOptions) {
		o.FnStreamVerbose = fn
	}
}

func WithFnStreamPreview(fn callback.FnStreamPreview) CallOption {
	return func(o *CallOptions) {
		o.FnStreamPreview = fn
	}
}

func WithFnStreamTokenUsage(fn callback.FnStreamTokenUsage) CallOption {
	return func(o *CallOptions) {
		o.FnStreamTokenUsage = fn
	}
}

func WithTokenChecker(checker *TokenChecker) CallOption {
	return func(o *CallOptions) {
		o.TokenChecker = checker
	}
}
