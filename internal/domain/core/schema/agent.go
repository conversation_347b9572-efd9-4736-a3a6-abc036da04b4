package schema

import (
	"context"
	"encoding/json"
	"fmt"
	"secwalk/internal/domain/core/message"
	"strings"
	"unicode/utf8"

	"github.com/Masterminds/semver/v3"
	"github.com/sasha<PERSON>nov/go-openai"
)

type Agent interface {
	Config() *AgentConfig
	Call(ctx context.Context, inputs map[string]any, opts ...CallOption) (*AgentChatResult, error)
}

// 智能体执行结果
type AgentChatResult struct {
	Session    *message.Session `json:"session"`
	Results    map[string]any   `json:"results"`
	TokenUsage *openai.Usage    `json:"token_usage"`
}

func (a *AgentChatResult) String() string {
	data, _ := json.Marshal(a)
	return string(data)
}

const (
	TypeUser = iota // 用户智能体
	TypePerm        // 认证智能体
)

const (
	COTv1 = "v1"
	COTv2 = "v2"
)

const (
	ReservedScratchpad = "agent_scratchpad"
	ReservedHistory    = "history"
	ReservedQuestion   = "_question"
	ReservedAnswer     = "_answer"
	ReservedOptions    = "_options"
)

// 智能体配置
type AgentConfig struct {
	// 基本信息
	AgentBase
	// 工作流信息
	AgentWorkflow
}

type AgentBase struct {
	ID              string       `json:"id"`                         // 智能体ID
	Name            string       `json:"name"`                       // 智能体名称
	Type            int          `json:"type"`                       // 智能体类型
	Version         string       `json:"version"`                    // 智能体版本号
	Description     string       `json:"description"`                // 智能体描述
	ORG             string       `json:"org,omitempty"`              // 创建者ID所属组织ID
	UID             string       `json:"uid,omitempty"`              // 创建者ID
	Prologue        string       `json:"prologue"`                   // 智能体开场白
	Model           *ModelConfig `json:"model,omitempty"`            // 智能体模型配置
	SessionRound    int          `json:"session_round,omitempty"`    // 使用历史记录的轮数
	CotVersion      string       `json:"cot_version,omitempty"`      // 思维链版本号
	EngVersion      string       `json:"eng_version,omitempty"`      // 引擎版本号
	InputParameters []Parameter  `json:"input_parameters,omitempty"` // 智能体输入参数
	Interaction                  // 智能体交互配置
	PublicEnvs      []Parameter  `json:"public_envs,omitempty"` // 可配置的环境变量参数
}

type AgentWorkflow struct {
	PrivateEnvs          []Parameter    `json:"private_envs,omitempty"`           // 智能体环境私有变量
	Environments         map[string]any `json:"environments,omitempty"`           // 智能体环境变量（废弃）
	SystemPromptTemplate string         `json:"system_prompt_template,omitempty"` // 智能体系统提示词
	UserPromptTemplate   string         `json:"user_prompt_template,omitempty"`   // 智能体用户提示词
	Entry                string         `json:"entry,omitempty"`                  // 智能体工作流入口
	Nodes                []*Node        `json:"nodes,omitempty"`                  // 智能体工作流节点
	Edges                []*Edge        `json:"edges,omitempty"`                  // 智能体工作流边
}

func VerifyAgent(cfg *AgentConfig, engVersion string) (err error) {
	// 校验ID
	if len(cfg.ID) < 4 || len(cfg.ID) > 128 {
		return ErrAgentID
	}

	// 校验名称
	if utf8.RuneCountInString(cfg.Name) < 2 || utf8.RuneCountInString(cfg.Name) > 128 {
		return ErrAgentName
	}

	// 校验类型
	if cfg.Type != TypeUser && cfg.Type != TypePerm {
		return ErrAgentType
	}

	// 校验版本号
	if len(cfg.Version) > 0 {
		_, err := semver.NewVersion(cfg.Version)
		if err != nil {
			return ErrAgentVersion
		}
	}

	// 校验描述
	if utf8.RuneCountInString(cfg.Description) < 8 || utf8.RuneCountInString(cfg.Description) > 1024 {
		return ErrAgentDescription
	}

	// 校验开场白
	if utf8.RuneCountInString(cfg.Prologue) > 1024 {
		return ErrAgentPrologue
	}

	// 校验思维链版本
	if len(cfg.CotVersion) == 0 {
		cfg.CotVersion = COTv1
	}
	if cfg.CotVersion != COTv1 && cfg.CotVersion != COTv2 {
		return ErrAgentCotVersion
	}

	// 赋予框架版本
	cfg.EngVersion = engVersion

	// 环境变量参数转换（C41）
	if len(cfg.Environments) > 0 && len(cfg.PrivateEnvs) == 0 {
		cfg.PrivateEnvs = make([]Parameter, 0)
	}
	for k, v := range cfg.Environments {
		cfg.PrivateEnvs = append(cfg.PrivateEnvs, Parameter{
			Name:         k,
			DefaultValue: v,
			Hidden:       true,
		})
	}
	cfg.Environments = nil

	// 校验参数
	for _, v := range cfg.InputParameters {
		if err := verifyParameter(v, true); err != nil {
			return err
		}
	}

	for _, v := range cfg.OutputParameters {
		if err := verifyParameter(v, true); err != nil {
			return err
		}
	}

	// 取值配置
	if err := verifyExprValue(cfg.OutputExpression); err != nil {
		return err
	}

	// 校验模型配置
	cfg.Model, err = VerifyModel(cfg.Model)
	if err != nil {
		return err
	}

	// 提示词长度校验
	if utf8.RuneCountInString(cfg.SystemPromptTemplate) > 32000 ||
		utf8.RuneCountInString(cfg.UserPromptTemplate) > 32000 {
		return ErrAgentPrompt
	}

	if len(cfg.Entry) == 0 {
		return ErrAgentEntryUnset
	}

	if len(cfg.Nodes) == 0 {
		return ErrAgentNodeEmpty
	}

	// 检查节点配置
	nodes := make(map[string]bool, 0)
	for i := range cfg.Nodes {
		if err := verifyNode(cfg.Nodes[i]); err != nil {
			return err
		}
		nodes[cfg.Nodes[i].ID] = true
	}

	// 节点ID是否重复
	if len(nodes) < len(cfg.Nodes) {
		return ErrAgentNodeDuplicate
	}

	// 入口节点ID是否存在
	if _, ok := nodes[cfg.Entry]; !ok {
		return ErrAgentEntryNotExist
	}

	// 检查边配置
	// 重复边
	dupEdges := make(map[string]bool, 0)
	// 冲突边
	conflictEdges := make(map[string]bool, 0)
	for _, edge := range cfg.Edges {
		if len(edge.SrcNode) == 0 {
			return ErrAgentEdgeSrcUnset
		}
		if _, ok := nodes[edge.SrcNode]; !ok {
			return ErrAgentEdgeSrcNotExist
		}

		if len(edge.DstNode) == 0 {
			return ErrAgentEdgeDstUnset
		}
		if _, ok := nodes[edge.DstNode]; !ok {
			return ErrAgentEdgeDstNotExist
		}

		if len(edge.Condition) == 0 && !edge.Parallel {
			_, ok := conflictEdges[edge.SrcNode]
			if ok {
				return ErrAgentEdgeConflict
			}
			conflictEdges[edge.SrcNode] = true
		}

		dupEdges[edge.SrcNode+edge.DstNode] = true
	}

	// 边是否重复
	if len(dupEdges) < len(cfg.Edges) {
		return ErrAgentEdgeDuplicate
	}

	return nil
}

const (
	NodeTypeActTool        = "act_tool"        // 工具调用
	NodeTypeActAgent       = "act_agent"       // 智能体调用
	NodeTypeActLogic       = "act_logic"       // 逻辑判断
	NodeTypeActInterpreter = "act_interpreter" // 解释执行
	NodeTypeActInteraction = "act_interaction" // 用户交互

	NodeTypeLLMMixup        = "llm_mixup"
	NodeTypeLLMBasic        = "llm_basic"
	NodeTypeLLMTools        = "llm_tools"
	NodeTypeLLMReact        = "llm_react"
	NodeTypeLLMFunctionCall = "llm_function_call"
	NodeTypeLLMRetrievalSql = "llm_retrieval_sql"
	NodeTypeLLMGeneraterSql = "llm_generater_sql"
	NodeTypeLLMRetrievalDoc = "llm_retrieval_doc"
)

func IsNodeTypeAct(t string) bool {
	switch t {
	case NodeTypeActTool, NodeTypeActAgent, NodeTypeActLogic,
		NodeTypeActInteraction, NodeTypeActInterpreter:
		return true
	default:
		return false
	}
}

func IsNodeTypeLLM(t string) bool {
	switch t {
	case NodeTypeLLMMixup, NodeTypeLLMBasic, NodeTypeLLMTools,
		NodeTypeLLMReact, NodeTypeLLMFunctionCall, NodeTypeLLMRetrievalSql,
		NodeTypeLLMGeneraterSql, NodeTypeLLMRetrievalDoc:
		return true
	default:
		return false
	}
}

const (
	DefaultResultKey      = "result_key"
	DefaultMaxReflections = 5
	DefaultMaxIterations  = 10
	DefaultTimeout        = 300
	DefaultMaxWorking     = 30
	DefaultMaxRetries     = 3
	DefaultInputKey       = "input"
)

const (
	OnErrorReturn = 0 + iota
	OnErrorBypass
	OnErrorRetry
)

type Node struct {
	ID               string      `json:"id"`                          // 节点ID
	Name             string      `json:"name"`                        // 节点名称
	Description      string      `json:"description,omitempty"`       // 节点描述，可选字段
	Type             string      `json:"type"`                        // 节点类型
	InputParameters  []Parameter `json:"input_parameters,omitempty"`  // 节点输入参数
	OutputParameters []Parameter `json:"output_parameters,omitempty"` // 节点输出参数

	SystemPromptTemplate string         `json:"system_prompt_template,omitempty"` // 系统提示词
	UserPromptTemplate   string         `json:"user_prompt_template,omitempty"`   // 提示词模板
	Model                *ModelConfig   `json:"model,omitempty"`                  // 模型名称
	Tools                []Component    `json:"tools,omitempty"`                  // 工具集
	Agents               []Component    `json:"agents,omitempty"`                 // 智能体集
	Mcps                 []MCPComponent `json:"mcps,omitempty"`                   // MCP服务配置
	DB                   *DB            `json:"db,omitempty"`                     // 检索数据库
	KB                   *KB            `json:"kb,omitempty"`                     // 检索知识库
	KBs                  []KBRepo       `json:"kbs,omitempty"`                    // 检索知识库（旧版兼容）
	Interpreter          *Interpreter   `json:"interpreter,omitempty"`            // 解释执行配置
	Interactions         []Interaction  `json:"interactions,omitempty"`           // 交互列表
	Questions            []Question     `json:"questions,omitempty"`              // 智能体向用户提问
	ResultKey            string         `json:"result_key"`                       // 解析输出KEY
	ResultParsers        []ParseConfig  `json:"result_parsers,omitempty"`         // 解析器

	NoRepeat       bool `json:"no_repeat,omitempty"`       // 不重复选用插件
	NoAction       bool `json:"no_action,omitempty"`       // 不执行动作
	UseMemory      bool `json:"use_memory"`                // 使用历史记录
	LogMemory      bool `json:"log_memory"`                // 记录到历史记录
	MaxReflections int  `json:"max_reflections"`           // 链最大思考次数
	ParallelAction bool `json:"parallel_action,omitempty"` // 并行动作

	Iteration       bool         `json:"iteration,omitempty"`        // 是否迭代处理
	IterationTimes  int          `json:"iteration_times,omitempty"`  // 迭代处理次数
	IterationValues []RangeValue `json:"iteration_values,omitempty"` // 迭代处理的值

	Pending    int `json:"pending,omitempty"` // 节点完成后等待时间
	Timeout    int `json:"timeout"`           // 超时时间
	MaxWorking int `json:"max_working"`       // 最大运行次数
	MaxRetries int `json:"max_retries"`       // 最大重试次数
	OnError    int `json:"on_error"`          // 错误执行逻辑
}

type Interaction struct {
	From             string      `json:"from,omitempty"`              // 来源名称
	Stream           bool        `json:"stream,omitempty"`            // 是否流式处理
	OutputParameters []Parameter `json:"output_parameters,omitempty"` // 智能体输出参数
	OutputExpression *ExprValue  `json:"output_expression,omitempty"` // 智能体用户交互输出
}

const (
	QuestionTypeInput       = "type_input"
	QuestionTypeSelect      = "type_select"
	QuestionTypeMultiSelect = "type_multi_select"
	QuestionTypeFile        = "type_file"
	QuestionTypeFiles       = "type_files"
)

type Question struct {
	Type    string      `json:"type"`
	Name    string      `json:"name"`
	Message ExprValue   `json:"message"`
	Options []Parameter `json:"options"`
	Timeout int         `json:"timeout"`
}

type Interpreter struct {
	Lang string `json:"lang,omitempty"` // 语言类型
	Code string `json:"code,omitempty"` // 代码内容
}

type RangeValue struct {
	ListName string     `json:"list_name,omitempty"`
	ListExpr *ExprValue `json:"list_expr,omitempty"`
	DestName string     `json:"dest_name,omitempty"`
}

func verifyNode(n *Node) error {
	// 校验节点ID
	if len(n.ID) < 4 || len(n.ID) > 128 {
		return ErrNodeID
	}

	// 校验节点名称
	if utf8.RuneCountInString(n.Name) < 2 || utf8.RuneCountInString(n.Name) > 128 {
		return ErrNodeName
	}

	// 节点类型校验
	if !IsNodeTypeAct(n.Type) && !IsNodeTypeLLM(n.Type) {
		return ErrNodeType
	}

	// 校验节点描述
	if utf8.RuneCountInString(n.Description) > 1024 {
		return ErrNodeDescription
	}

	// 校验提示词
	if utf8.RuneCountInString(n.SystemPromptTemplate) > 32000 ||
		utf8.RuneCountInString(n.UserPromptTemplate) > 32000 {
		return ErrNodePrompt
	}

	// 校验参数
	for _, v := range n.InputParameters {
		if err := verifyParameter(v, true); err != nil {
			return err
		}
	}

	for _, v := range n.OutputParameters {
		if err := verifyParameter(v, true); err != nil {
			return err
		}
	}

	// 模型配置校验
	_, err := VerifyModel(n.Model)
	if err != nil {
		return err
	}

	if n.Type == NodeTypeActTool && len(n.Tools) != 1 && len(n.Mcps) != 1 {
		return ErrNodeToolOne
	}
	if n.Type == NodeTypeActAgent && len(n.Agents) != 1 {
		return ErrNodeAgentOne
	}

	for _, t := range append(n.Tools, n.Agents...) {
		if len(t.ID) < 4 || len(t.ID) > 128 {
			return ErrComponentID
		}
	}

	// 校验数据库检索链是否配置正确数据库
	if n.Type == NodeTypeLLMRetrievalSql {
		if n.DB == nil {
			return ErrNodeDBNotSet
		} else {
			if err := n.DB.verify(); err != nil {
				return err
			}
		}
	}

	// 校验知识库检索链是否配置正确知识库
	if n.Type == NodeTypeLLMRetrievalDoc && (n.KB == nil && len(n.KBs) == 0) {
		return ErrNodeKBNotSet
	}

	if len(n.KBs) > 0 {
		n.KB = &KB{
			Repos: n.KBs,
			Count: 6,
		}
		n.KBs = nil
	}
	if n.KB != nil && n.KB.Count < 1 {
		n.KB.Count = 6
	}

	// 交互配置校验
	names := make(map[string]struct{})
	for i := range n.Questions {
		if n.Questions[i].Timeout <= 0 {
			n.Questions[i].Timeout = 30
		}

		_, ok := names[n.Questions[i].Name]
		if ok {
			return ErrNodeQuestionNameRepeat
		}
		names[n.Questions[i].Name] = struct{}{}
	}

	// 校验结果存储键
	if utf8.RuneCountInString(n.ResultKey) < 2 || utf8.RuneCountInString(n.ResultKey) > 64 {
		n.ResultKey = DefaultResultKey
	}

	// 校验结果解析器
	trans := make([]Parameter, 0)
	for _, rp := range n.ResultParsers {
		param := Parameter{
			Name: rp.Key,
			Value: &ExprValue{
				Expr: rp.Expression,
			},
		}

		switch rp.Type {
		case ParserRegex:
			param.Value.Type = ParamValueTypeRegexp
		case ParserJsonpath:
			param.Value.Type = ParamValueTypeJsonPath
		case ParserDict:
			param.Value.Type = ParamValueTypeRegexpDict
		}

		if rp.TextExpr != nil {
			param.Value.Text = rp.TextExpr
		} else {
			if len(rp.TextKey) > 0 {
				param.Value.Text = &ExprValue{
					Type: ParamValueTypeReference,
					Expr: fmt.Sprintf("$.%s.%s", n.ID, rp.TextKey),
				}
			} else {
				param.Value.Text = &ExprValue{
					Type: ParamValueTypeReference,
					Expr: fmt.Sprintf("$.%s.%s", n.ID, n.ResultKey),
				}
			}
		}

		trans = append(trans, param)
	}
	n.ResultParsers = nil
	n.OutputParameters = append(trans, n.OutputParameters...)

	//校验插件/智能体参数
	n.Tools = conversion(n.Tools)
	n.Agents = conversion(n.Agents)

	if n.Timeout < 10 || n.Timeout > 3000 {
		n.Timeout = DefaultTimeout
	}

	if n.Iteration && (n.IterationTimes < 0 || n.IterationTimes > 32) {
		n.IterationTimes = DefaultMaxIterations
	}
	if n.Iteration && len(n.IterationValues) <= 0 {
		return ErrNodeIterationValue
	}
	for _, v := range n.IterationValues {
		if err := verifyExprValue(v.ListExpr); err != nil {
			return err
		}
	}

	if n.MaxWorking <= 0 || n.MaxRetries > 10000 {
		n.MaxWorking = DefaultMaxWorking
	}

	if n.MaxRetries <= 0 || n.MaxRetries > 32 {
		n.MaxRetries = DefaultMaxRetries
	}

	if n.MaxReflections <= 0 || n.MaxReflections > 32 {
		n.MaxReflections = DefaultMaxReflections
	}

	return nil
}

type Component struct {
	ID      string `json:"id"`
	Options any    `json:"options,omitempty"`
}

type MCPComponent struct {
	ID    string `json:"id"` // mcp服务ID
	Tools []struct {
		Name    string `json:"name,omitempty"`
		Options any    `json:"options,omitempty"`
	} `json:"tools,omitempty"`
}

type Edge struct {
	SrcNode   string `json:"src_node"`
	DstNode   string `json:"dst_node"`
	Condition string `json:"condition,omitempty"`
	Parallel  bool   `json:"parallel,omitempty"`
}

// Agent Produce
func NameToAgent(agents []Agent) map[string]Agent {
	if len(agents) == 0 {
		return nil
	}

	nameToAgents := make(map[string]Agent, len(agents))
	for _, a := range agents {
		nameToAgents[strings.ToUpper(a.Config().Name)] = a
	}

	return nameToAgents
}

type AgentExtension struct {
	ID         string      `json:"id"`                    // 智能体ID
	PublicEnvs []Parameter `json:"public_envs,omitempty"` // 可配置的环境变量参数
}

// Extract 提取参数
func (c *Component) Extract() ([]Parameter, error) {
	var op []Parameter
	data, err := json.Marshal(c.Options)
	if err != nil {
		return nil, err
	}
	err = json.Unmarshal(data, &op)
	if err != nil {
		return nil, err
	}
	return op, nil
}
