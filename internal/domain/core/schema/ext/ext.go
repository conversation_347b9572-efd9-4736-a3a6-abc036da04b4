package ext

import (
	"strings"
	"sync"
)

const (
	EXTTID           = "tid"
	EXTSID           = "sid"
	EXTQID           = "qid"
	EXTORG           = "org"
	EXTUID           = "uid"
	EXTAID           = "aid"
	EXTXUID          = "x-uid"
	EXTXORG          = "x-org"
	EXTXORGRiskLevel = "x-org-risklevel"
	EXTORDER         = "order"
	EXTPrinciple     = "principle"
)

type ExtType struct {
	mux *sync.RWMutex
	kvs map[string]string
}

func New(ext string) ExtType {
	e := ExtType{
		mux: &sync.RWMutex{},
		kvs: map[string]string{},
	}
	for _, v := range strings.Split(ext, ";") {
		kv := strings.Split(v, ":")
		if len(kv) != 2 {
			continue
		}
		e.kvs[kv[0]] = kv[1]
	}
	return e
}

func (e *ExtType) ToString() string {
	e.mux.RLock()
	defer e.mux.RUnlock()

	ext := ""
	for k, v := range e.kvs {
		ext += k + ":" + v + ";"
	}
	return ext
}

func (e *ExtType) GetValue(key string) string {
	e.mux.RLock()
	defer e.mux.RUnlock()

	return e.kvs[key]
}

func (e *ExtType) SetValue(key, value string) {
	e.mux.Lock()
	defer e.mux.Unlock()

	e.kvs[key] = value
}
