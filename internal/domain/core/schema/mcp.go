package schema

import "time"

type ServersFile struct {
	McpServers map[string]ServerEntry `json:"mcpServers,omitempty"`
}

type ServerEntry struct {
	ID   string `json:"id,omitempty"`
	Type string `json:"type,omitempty"`

	// STDIO
	Command string            `json:"command,omitempty"`
	Args    []string          `json:"args,omitempty"`
	Env     map[string]string `json:"env,omitempty"`

	// HTTP
	URL     string            `json:"url,omitempty"`
	Headers map[string]string `json:"headers,omitempty"`
	Timeout time.Duration     `json:"timeout,omitempty"`
}
