package schema

const (
	ORGRiskLevelHigh   = "1" // 输入：敏感词+风控智能体；输出：检查标志位+相似度
	ORGRiskLevelMedium = "2" // 输入：敏感词+风控智能体；输出：检查标志位
	ORGRiskLevelLow    = "3"
)

const (
	TraitLevelPass = 0
	TraitLevelWarn = 1
	TraitLevelStop = 2
)

const (
	TraitTypePolitical       = "Political"       // 政治敏感
	TraitTypePornographic    = "Pornographic"    // 色情内容
	TraitTypeViolence        = "Violence"        // 暴力内容
	TraitTypeHate            = "Hate"            // 仇恨言论
	TraitTypeFraud           = "Fraud"           // 欺诈与诈骗
	TraitTypeProhibited      = "Prohibited"      // 违禁品
	TraitTypeMalware         = "Malware"         // 恶意软件及网络安全
	TraitTypeMisinformation  = "Misinformation"  // 谣言与虚假信息
	TraitTypeInfringement    = "Infringement"    // 侵权与违法内容
	TraitTypeExtremism       = "Extremism"       // 宗教极端
	TraitTypeMinors          = "Minors"          // 未成年人不宜
	TraitTypePropaganda      = "Propaganda"      // 政治宣传
	TraitTypeGambling        = "Gambling"        // 赌博
	TraitTypePharmaceuticals = "Pharmaceuticals" // 违规药品及保健品
	TraitTypeInducement      = "Inducement"      // 诈骗诱导
	TraitTypeCounterfeit     = "Counterfeit"     // 伪劣产品
	TraitTypeAnti            = "AntiSocial"      // 反社会行为
	TraitTypeHarmful         = "Harmful"         // 其它不良内容
)

var TraitTypeMap = map[string]string{
	TraitTypePolitical:       "政治敏感",
	TraitTypePornographic:    "色情内容",
	TraitTypeViolence:        "暴力内容",
	TraitTypeHate:            "仇恨言论",
	TraitTypeFraud:           "欺诈与诈骗",
	TraitTypeProhibited:      "违禁品",
	TraitTypeMalware:         "恶意软件及网络安全",
	TraitTypeMisinformation:  "谣言与虚假信息",
	TraitTypeInfringement:    "侵权与违法内容",
	TraitTypeExtremism:       "宗教极端",
	TraitTypeMinors:          "未成年人不宜",
	TraitTypePropaganda:      "政治宣传",
	TraitTypeGambling:        "赌博",
	TraitTypePharmaceuticals: "违规药品及保健品",
	TraitTypeInducement:      "诈骗诱导",
	TraitTypeCounterfeit:     "伪劣产品",
	TraitTypeAnti:            "反社会行为",
	TraitTypeHarmful:         "其它不良内容",
}
