package schema

import (
	"errors"
)

var (
	ErrParserKVError = errors.New("parser key/expression config error")
	ErrParserType    = errors.New("parser type not support")
)

const (
	ParserRegex    = "parser_regex"
	ParserJsonpath = "parser_jsonpath"
	ParserDict     = "parser_dict"
)

type ParseConfig struct {
	Type       string     `json:"type"`
	Key        string     `json:"key"`
	Expression string     `json:"expression"`
	TextKey    string     `json:"text_key,omitempty"`
	TextExpr   *ExprValue `json:"text_expr,omitempty"`
}
