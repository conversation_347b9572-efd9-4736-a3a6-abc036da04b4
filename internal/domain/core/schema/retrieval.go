package schema

import (
	"secwalk/internal/domain/core/sqloader"
	"unicode/utf8"
)

type DB struct {
	Type   string           `json:"type,omitempty"`
	DSN    string           `json:"dsn,omitempty"`
	Tables []sqloader.Table `json:"tables,omitempty"`
}

func (d *DB) verify() error {
	if d.Type != sqloader.DialectMysql && d.Type != sqloader.DialectSqlite3 {
		return ErrDBType
	}

	if utf8.RuneCountInString(d.DSN) > 512 {
		return ErrDBDSN
	}

	if len(d.Tables) == 0 {
		return ErrDBTable
	}

	for _, t := range d.Tables {
		if utf8.RuneCountInString(t.Name) < 2 || utf8.RuneCountInString(t.Name) > 128 {
			return ErrDBTableName
		}

		if utf8.RuneCountInString(t.Description) < 8 || utf8.RuneCountInString(t.Description) > 1024 {
			return ErrDBTableDescription
		}
	}

	return nil
}

type KB struct {
	Repos []KBRepo `json:"repos,omitempty"` // 知识库列表
	Count int      `json:"count,omitempty"` // chunk数量
}

type KBRepo struct {
	ID          string   `json:"id,omitempty"`          // 唯一标识符，用于区分不同的知识库
	Description string   `json:"description,omitempty"` // 知识库的描述信息，用于说明知识库的内容和用途等
	Files       []KBFile `json:"files,omitempty"`       // 知识库文件列表，用于存储知识库相关的文件信息
}

type KBFile struct {
	ID          string `json:"id,omitempty"`          // 唯一标识符，用于区分不同的知识库文件
	Description string `json:"description,omitempty"` // 知识库文件的描述信息，用于说明文件的内容和用途等
}
