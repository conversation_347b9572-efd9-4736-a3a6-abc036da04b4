package schema

import (
	"fmt"
)

const (
	TypeChat = "chat"
)

func SupportModelType(t string) error {
	switch t {
	case TypeChat:
		return nil
	default:
		return fmt.Errorf("unsupport model type, must one of [%s]", TypeChat)
	}
}

const (
	ReasoningDefault = 0
	ReasoningON      = 1
	ReasoningOFF     = -1
)

var HengNaoR1 = "HengNao-R1"

type ModelConfig struct {
	Type              string     `json:"type"`
	Model             string     `json:"model,omitempty"`
	LoraType          string     `json:"lora_type,omitempty"`
	MaxTokens         int        `json:"max_tokens,omitempty"`
	Temperature       float32    `json:"temperature"`
	TopK              int        `json:"top_k,omitempty"`
	TopP              float32    `json:"top_p,omitempty"`
	RepetitionPenalty float32    `json:"repetition_penalty,omitempty"`
	Stop              []string   `json:"stop,omitempty"`
	GuidedChoiceExpr  *ExprValue `json:"guided_choice_expr,omitempty"`
	GuidedJsonExpr    *ExprValue `json:"guided_json_expr,omitempty"`
	GuidedRegexExpr   *ExprValue `json:"guided_regex_expr,omitempty"`
	LogProbs          int        `json:"logprobs,omitempty"`
	Seed              int        `json:"seed,omitempty"`
	Reasoning         int        `json:"reasoning,omitempty"` // -1关闭，0默认，1开启
}

func VerifyModel(model *ModelConfig) (*ModelConfig, error) {
	if model == nil {
		model = &ModelConfig{
			Type: TypeChat,
		}
	}

	if len(model.Type) == 0 {
		model.Type = TypeChat
	}
	if err := SupportModelType(model.Type); err != nil {
		return nil, ErrModelType
	}

	if model.MaxTokens > 32000 {
		model.MaxTokens = 0
	}

	if model.Temperature <= 0 || model.Temperature > 2 {
		model.Temperature = 0.0
	}

	if model.TopK == 0 {
		model.TopK = -1
	}

	if model.TopP == 0 || model.TopP > 1 {
		model.TopP = 1.0
	}

	if err := verifyExprValue(model.GuidedChoiceExpr); err != nil {
		return model, err
	}
	if err := verifyExprValue(model.GuidedJsonExpr); err != nil {
		return model, err
	}
	if err := verifyExprValue(model.GuidedRegexExpr); err != nil {
		return model, err
	}

	i := 0
	if model.GuidedChoiceExpr != nil {
		i++
	}
	if model.GuidedJsonExpr != nil {
		i++
	}
	if model.GuidedRegexExpr != nil {
		i++
	}
	if i > 1 {
		return model, ErrModelGuidedDecoding
	}

	return model, nil
}
