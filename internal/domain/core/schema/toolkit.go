package schema

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"math"
	"net"
	"net/http"
	"net/url"
	"secwalk/internal/domain/core/callback"
	"secwalk/internal/domain/core/endpoint"
	"secwalk/internal/domain/core/schema/ext"
	"secwalk/internal/domain/core/xfile"
	"secwalk/pkg/logger"
	"secwalk/pkg/random"
	"secwalk/pkg/util"
	"slices"
	"strings"
	"time"
	"unicode/utf8"

	"github.com/jinzhu/copier"
	"github.com/sirupsen/logrus"
)

const (
	ToolkitTypeSystem = "system"
	ToolkitTypeCustom = "custom"
)

const (
	DisplayImage = "image"
	DisplayText  = "file"
)

// 工具集
type Toolkit struct {
	ID          string       `json:"id"`
	Name        string       `json:"name"`
	Description string       `json:"description"`
	Type        string       `json:"type"`
	Url         string       `json:"url,omitempty"`
	Parameters  []Parameter  `json:"parameters,omitempty"`
	Tools       []ToolConfig `json:"tools,omitempty"`
}

func VerifyToolkit(cfg *Toolkit) error {
	if len(cfg.ID) < 4 || len(cfg.ID) > 128 {
		return ErrPluginID
	}

	if utf8.RuneCountInString(cfg.Name) < 2 || utf8.RuneCountInString(cfg.Name) > 128 {
		return ErrPluginName
	}

	if utf8.RuneCountInString(cfg.Description) < 8 || utf8.RuneCountInString(cfg.Description) > 1024 {
		return ErrPluginDescription
	}

	if utf8.RuneCountInString(cfg.Url) > 1024 {
		return ErrPluginUrl
	}

	if err := verifyUrl(cfg.Url); err != nil {
		return err
	}

	for _, v := range cfg.Parameters {
		if err := verifyParameter(v, true); err != nil {
			return err
		}
	}

	for _, v := range cfg.Tools {
		if err := VerifyTool(&v); err != nil {
			return err
		}
	}

	return nil
}

// 工具
type Tool interface {
	Config() ToolConfig
	Call(ctx context.Context, inputs map[string]any, opts ...CallOption) (string, error)
}

// 工具实例化创建器
type ToolCreator func(...ToolOption) (Tool, error)

// 工具配置
type ToolConfig struct {
	ID                string      `json:"id"`
	Name              string      `json:"name"`
	Description       string      `json:"description"`
	InputParameters   []Parameter `json:"input_parameters,omitempty"`
	InputExample      string      `json:"input_example,omitempty"`
	OutputParameters  []Parameter `json:"output_parameters,omitempty"`
	OutputExample     string      `json:"output_example,omitempty"`
	LLMFlag           bool        `json:"llm_flag,omitempty"`
	Method            string      `json:"method,omitempty"`
	Url               string      `json:"url,omitempty"`
	Path              string      `json:"path,omitempty"`
	Code              string      `json:"code,omitempty"`
	Stream            bool        `json:"stream,omitempty"`
	StreamContentExpr *ExprValue  `json:"stream_content_expr,omitempty"`
	Status            int         `json:"status"`
	CreateAt          int64       `json:"create_at,omitempty"`
	UpdateAt          int64       `json:"update_at,omitempty"`
	Display           string      `json:"display,omitempty"` //展示类型 图片/文本
	Creator           ToolCreator `json:"-"`
}

func VerifyTool(v *ToolConfig) error {
	if len(v.ID) < 4 || len(v.ID) > 128 {
		return ErrAbilityID
	}

	if utf8.RuneCountInString(v.Name) < 2 || utf8.RuneCountInString(v.Name) > 128 {
		return ErrAbilityName
	}

	if utf8.RuneCountInString(v.Description) < 8 || utf8.RuneCountInString(v.Description) > 1024 {
		return ErrAbilityDescription
	}

	for _, vv := range v.InputParameters {
		if err := verifyParameter(vv, true); err != nil {
			return err
		}
	}

	for _, vv := range v.OutputParameters {
		if err := verifyParameter(vv, true); err != nil {
			return err
		}
	}

	if len(v.Method) > 0 && v.Method != http.MethodGet && v.Method != http.MethodPost {
		return ErrAbilityMethod
	}

	if utf8.RuneCountInString(v.Url) > 1024 {
		return ErrAbilityUrl
	}

	if err := verifyUrl(v.Url); err != nil {
		return err
	}

	if utf8.RuneCountInString(v.Path) > 1024 {
		return ErrAbilityPath
	}

	return nil
}

func verifyUrl(urlStr string) error {
	url, err := url.Parse(urlStr)
	if err != nil {
		return err
	}

	host, _, err := net.SplitHostPort(url.Host)
	if err != nil {
		host = url.Host
	}

	if slices.Contains(
		[]string{"localhost", "127.0.0.1", "0.0.0.0"},
		strings.ToLower(host)) {
		return ErrAbilityLocalhost
	}

	return nil
}

// 实例化创建可选配置
type ToolOption func(*ToolConfig)

// 工具配置
func WithToolConfig(cfg *ToolConfig) ToolOption {
	return func(co *ToolConfig) {
		copier.CopyWithOption(co, cfg, copier.Option{DeepCopy: true})
	}
}

// 智能体定义时可有用户定义的参数
func WithPartOption(opts any) ToolOption {
	return func(co *ToolConfig) {
		var ps []Parameter
		data, err := json.Marshal(opts)
		if err != nil {
			return
		}
		err = json.Unmarshal(data, &ps)
		if err != nil {
			return
		}
	LOOP:
		for _, v := range ps {
			for i, arg := range co.InputParameters {
				if arg.Name == v.Name {
					co.InputParameters[i].Value = v.Value
					continue LOOP
				}
			}
		}
	}
}

func ToolCall(ctx context.Context, tool Tool, inputs map[string]any, opts ...CallOption) (string, error) {
	opt := NewCallOptions()
	for _, o := range opts {
		o(opt)
	}

	var out string
	var err error
	var cid = random.UniqueID()

	//加载工具参数
	tps := make(map[string]any)
	for _, v := range tool.Config().InputParameters {
		vv, err := v.GetValue(inputs)
		if err != nil {
			if !v.Required {
				continue
			}
			return "", err
		}
		tps[v.Name] = vv.V()
		inputs[v.Name] = vv.V()
	}

	if opt.FnStreamVerbose != nil {
		opt.FnStreamVerbose(&callback.VerboseMessage{
			Type:      callback.TypeTool,
			Name:      tool.Config().Name,
			NodeID:    opt.MessageNodeID,
			PID:       opt.PID,
			CID:       cid,
			Stage:     callback.StageRun,
			State:     callback.CallStateSuccess,
			Timestamp: time.Now().UnixMilli(),
			Inputs:    tps,
		})

		defer func() {
			if err != nil {
				opt.FnStreamVerbose(&callback.VerboseMessage{
					Type:      callback.TypeTool,
					Name:      tool.Config().Name,
					NodeID:    opt.MessageNodeID,
					PID:       opt.PID,
					CID:       cid,
					Stage:     callback.StageEnd,
					State:     callback.CallStateFailure,
					Error:     err.Error(),
					Timestamp: time.Now().UnixMilli(),
				})
			} else {
				opt.FnStreamVerbose(&callback.VerboseMessage{
					Type:      callback.TypeTool,
					Name:      tool.Config().Name,
					NodeID:    opt.MessageNodeID,
					PID:       opt.PID,
					CID:       cid,
					Stage:     callback.StageEnd,
					State:     callback.CallStateSuccess,
					Timestamp: time.Now().UnixMilli(),
					Output:    out,
				})
			}
		}()
	}

	logrus.WithField(logger.KeyCategory, logger.CategoryCore).
		WithField(logger.KeyEXT, opt.EXT).
		Infof("[%s] use ability: %s %s", opt.NodeID, tool.Config().ID, tool.Config().Name)

	out, err = tool.Call(ctx, inputs, append(opts, WithPID(cid))...)
	if err != nil {
		logrus.WithField(logger.KeyCategory, logger.CategoryCore).
			WithField(logger.KeyEXT, opt.EXT).
			Errorf("[%s] use ability error: %s", opt.NodeID, err.Error())
		if util.ContainIP(err.Error()) || util.ContainHTTP(err.Error()) {
			return out, fmt.Errorf("%w: connect fail", ErrAbilityExecute)
		}
		return out, fmt.Errorf("%w: %v", ErrAbilityExecute, err)
	}

	outs := make(map[string]any)

	if !json.Valid([]byte(out)) {
		goto END
	}

	if err := json.Unmarshal([]byte(out), &outs); err != nil {
		goto END
	}

	for _, v := range tool.Config().OutputParameters {
		saves := map[string]any{}
		val, err := v.GetValue(outs)
		if err == nil {
			saves[v.Name] = val.V()
		}

		if v.Type == TypeFile {
			// 内置插件返回文件类型
			if xf, err := xfile.Load([]byte(out)); err == nil {
				if tool.Config().Display == DisplayImage {
					xf.SetType(DisplayImage)
				}
				if err := endpoint.PutFile(ctx, xf.String(), xf.Content); err != nil {
					logrus.WithField(logger.KeyCategory, logger.CategoryCore).
						WithField(logger.KeyEXT, opt.EXT).
						Errorf("[%s] ability file output, put file error: %s",
							opt.NodeID, err.Error())
					return "", fmt.Errorf("%w: %s put file error", ErrAbilityExecute, err)
				}
				xf.SetContent(nil)
				saves[tool.Config().OutputParameters[0].Name] = xf.String()
				saves[tool.Config().OutputParameters[0].Name+xfile.FileAttributeSuffix] = xf.ToMap()
				out = util.ToPureJsonString(map[string]any{
					tool.Config().OutputParameters[0].Name: xf.String(),
				})
				if opt.CbSetVariable != nil {
					opt.CbSetVariable(opt.NodeID, saves)
				}
				continue
			}

			//代码插件返回文件类型
			if xf, err := xfile.Parse(val.String()); err == nil {
				sf, err := endpoint.StatFile(ctx, xf.Tag,
					opt.EXT.GetValue(ext.EXTXORG))
				if err != nil {
					logrus.WithField(logger.KeyCategory, logger.CategoryCore).
						WithField(logger.KeyEXT, opt.EXT).
						Warnf("[%s] tool param file object not found : %s", opt.NodeID, val.V())
					return "", fmt.Errorf("%w: %s put file error", ErrAbilityExecute, err)
				}
				xf.Size = math.Round(float64(sf)/1024*100) / 100
				saves[v.Name] = val.V()
				saves[v.Name+xfile.FileAttributeSuffix] = xf.ToMap()
				out = util.ToPureJsonString(map[string]any{
					tool.Config().OutputParameters[0].Name: xf.String(),
				})
				if opt.CbSetVariable != nil {
					opt.CbSetVariable(opt.NodeID, saves)
				}
				continue
			}

			//MCP协议插件返回文件类型
			mcpContent := make(map[string]any)
			err := json.Unmarshal([]byte(out), &mcpContent)
			if err == nil {
				var fileContent []byte
				xf := xfile.New("", fileContent)
				if _, hasURL := mcpContent["url"].(string); hasURL {
					if text, hasText := mcpContent["text"].(string); hasText {
						fileContent = []byte(text)
					}
					if blobBase64, hasBlob := mcpContent["blob"].(string); hasBlob {
						var err error
						fileContent, err = base64.StdEncoding.DecodeString(blobBase64)
						if err != nil {
							logrus.WithField(logger.KeyCategory, logger.CategoryCore).
								WithField(logger.KeyEXT, opt.EXT).
								Errorf("[%s] Failed to decode base64 blob: %s",
									opt.NodeID, err.Error())
							return "", fmt.Errorf("%w: %s put file error", ErrAbilityExecute, err)
						}
					}
					xf.Name = util.MD5(fileContent)
				}

				if mt, hasMime := mcpContent["mime_type"].(string); hasMime {
					if strings.HasPrefix(mt, "image") {
						imgData := mcpContent["data"].(string)
						fileContent, err = base64.StdEncoding.DecodeString(imgData)
						if err != nil {
							logrus.WithField(logger.KeyCategory, logger.CategoryCore).
								WithField(logger.KeyEXT, opt.EXT).
								Errorf("[%s] Failed to decode base64 blob: %s",
									opt.NodeID, err.Error())
							return "", fmt.Errorf("%w: %s put file error", ErrAbilityExecute, err)
						}
						xf.SetType(DisplayImage)
						xf.Name = util.MD5(fileContent) + ".png"
					}
				}

				xf.SetContent(fileContent)
				if err := endpoint.PutFile(ctx, xf.String(), xf.Content); err != nil {
					logrus.WithField(logger.KeyCategory, logger.CategoryCore).
						WithField(logger.KeyEXT, opt.EXT).
						Errorf("[%s] ability file output, put file error: %s",
							opt.NodeID, err.Error())
					return "", fmt.Errorf("%w: %s put file error", ErrAbilityExecute, err)
				}
				xf.SetContent(nil)
				saves[tool.Config().OutputParameters[0].Name] = xf.String()
				saves[tool.Config().OutputParameters[0].Name+xfile.FileAttributeSuffix] = xf.ToMap()
				out = util.ToPureJsonString(map[string]any{
					tool.Config().OutputParameters[0].Name: xf.String(),
				})
				if opt.CbSetVariable != nil {
					opt.CbSetVariable(opt.NodeID, saves)
				}
				continue
			}

			logrus.WithField(logger.KeyCategory, logger.CategoryCore).
				WithField(logger.KeyEXT, opt.EXT).
				Infof("[%s] ability file output, put file success: %s",
					opt.NodeID, v.Name)
		}

		if opt.CbSetVariable != nil {
			opt.CbSetVariable(opt.NodeID, saves)
		}
		if len(saves) <= 0 {
			logrus.WithField(logger.KeyCategory, logger.CategoryCore).
				WithField(logger.KeyEXT, opt.EXT).
				Warnf("[%s] miss found tool param: %s", opt.NodeID, v.Name)
		}
	}

END:
	logrus.WithField(logger.KeyCategory, logger.CategoryCore).
		WithField(logger.KeyEXT, opt.EXT).
		Infof("[%s] use ability success: %s %s",
			opt.NodeID, tool.Config().ID, tool.Config().Name)
	return out, nil
}

func NameToTool(tools []Tool) map[string]Tool {
	if len(tools) == 0 {
		return nil
	}

	nameToTool := make(map[string]Tool, len(tools))
	for _, tool := range tools {
		nameToTool[strings.ToUpper(tool.Config().Name)] = tool
	}

	return nameToTool
}
