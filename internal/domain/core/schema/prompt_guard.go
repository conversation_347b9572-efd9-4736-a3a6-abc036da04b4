package schema

import (
	"fmt"
	"github.com/clipperhouse/uax29/iterators/transformer"
	"github.com/clipperhouse/uax29/sentences"
	"github.com/clipperhouse/uax29/words"
	"github.com/gosbd/gosbd"
	"regexp"
	"unicode"
	"unicode/utf8"
	"unsafe"
)

var _FriendlyFormat = "抱歉哦~小恒无法给出针对这个问题的解答，此问题可能涉及隐私泄漏，感谢理解。"

type LeakGuard struct {
	dict   map[string]int // 由需过滤的单词组成的字典
	w2s    [][]int        // 单词所属句子的索引
	s2w    [][]int        // 句子包含单词的索引
	buff   string         // 缓存
	level  string         // 级别
	stream []string       // 原始流失数据
}

const (
	strSplitTag   = rune('\u22B9') // 字符串标记⋉
	skipShortWord = 4              // 最小英文单词长度
	w2sThreshold  = 0.8            // 待过滤的句子中，命中单词占比阈值
	s2wThreshold  = 0.8            // 过滤器的句子中，命中单词占比阈值
	s2sThreshold  = 0.8            // 两个句子相似度阈值
)

var (
	hanRange = unicode.Scripts["Han"] // unicode汉字范围，不含生僻字
	// ASCII字符属性表，数字为1，大写字母为2，小写字母为4
	charProp = [128]byte{
		/*00-0F*/ 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
		/*10-1F*/ 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
		/*20-2F*/ 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
		/*30-3F*/ 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0,
		/*40-4F*/ 0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2,
		/*50-5F*/ 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 0, 0, 0, 0, 0,
		/*60-6F*/ 0, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4,
		/*70-7F*/ 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 0, 0, 0, 0, 0,
	}
)

// 单词和汉字的过滤器
var letterFilter = func(token []byte) bool {
	var r rune
	var w, c int
	for pos := 0; pos < len(token); pos += w {
		r, w = utf8.DecodeRune(token[pos:])
		if r <= unicode.MaxASCII { // 忽略ASCII中的非数字和字母
			if charProp[r] > 0 {
				c++
				continue
			}
		} else if unicode.Is(hanRange, r) { // 忽略非汉字和汉字中的常用字
			switch r {
			case '的', '一', '是', '也', '了', '这', '以',
				'在', '之', '个', '就', '而', '为', '有':
				return false
			}
			c = skipShortWord
			continue
		}
		return false
	}
	// 忽略太短的英文单词
	return c >= skipShortWord
}

func NewLeakGuard(level string) *LeakGuard {
	return &LeakGuard{
		dict:  make(map[string]int),
		level: level,
	}
}

// 将分割标签之间的文本（utf8字符串）添加到过滤器中
func (lg *LeakGuard) AddTextWithinTag(text string) {
	//用正则匹配标签
	re := regexp.MustCompile(fmt.Sprintf("\\x{%X}(.*?)\\x{%X}", strSplitTag, strSplitTag))
	matches := re.FindStringSubmatch(text)
	if len(matches) >= 1 {
		lg.addText(matches[1])
	}
}

// 将文本（utf8字符串）添加到过滤器中
func (lg *LeakGuard) addText(text string) {
	lg.addBytes(stringToBytes(text))
}

// 将文本（utf8字节串）添加到过滤器中
func (lg *LeakGuard) addBytes(text []byte) {
	// 新建句子和单词的分割器
	ss := sentences.NewSegmenter(text)
	ws := words.NewSegmenter(nil)
	// 设置单词过滤器，将英文单词转小写，忽略不需要的字符
	ws.Transform(transformer.Lower)
	ws.Filter(letterFilter)

	// 枚举句子
	for ss.Next() {
		// 获得句子的序号
		si := len(lg.s2w)
		// 枚举单词
		ws.SetText(ss.Bytes())
		for ws.Next() {
			// 将单词加入过滤器的字典中
			key := bytesToString(ws.Bytes())
			wi, ok := lg.dict[key]
			if ok {
				// 单词已经存在，把句子序号加入单词倒排索引
				lg.w2s[wi] = append(lg.w2s[wi], si)
			} else {
				// 单词不存在，新增单词序号和单词倒排索引
				wi = len(lg.w2s)
				lg.dict[key] = wi
				lg.w2s = append(lg.w2s, []int{si})
			}

			// 如果句子倒排索引不存在则新建
			if si == len(lg.s2w) {
				lg.s2w = append(lg.s2w, nil)
			}
			// 将单词序号加入句子倒排索引
			lg.s2w[si] = append(lg.s2w[si], wi)
		}
	}
}

// 检查文本（utf8字节串）中的句子与过滤器中的是否相似
func (lg *LeakGuard) check(text []byte) bool {
	// 新建句子和单词的分割器
	ss := sentences.NewSegmenter(text)
	ws := words.NewSegmenter(nil)
	// 设置单词过滤器，将英文单词转小写，忽略不需要的字符
	ws.Transform(transformer.Lower)
	ws.Filter(letterFilter)

	snlen := len(lg.s2w)
	wimax := len(lg.w2s)
	sns := make([]int, snlen)
	wis := make([]int, 0, 32)

	// 枚举句子
	for ss.Next() {
		wis = wis[0:0]
		wn := 0
		// 枚举单词
		ws.SetText(ss.Bytes())
		for ws.Next() {
			// 记录每个单词的序号，不在字典中的单词赋予新序号
			key := bytesToString(ws.Bytes())
			wi, ok := lg.dict[key]
			if ok {
				wn++
			} else {
				wi = wimax
			}
			wis = append(wis, wi)
		}

		// 判断在字典中的单词数占待过滤句子总单词数的比例是否达到阈值
		if float32(wn)/float32(len(wis)) < w2sThreshold {
			continue
		}

		// 统计过滤器中每个句子被命中的单词数
		for _, wi := range wis {
			if wi < wimax {
				for _, si := range lg.w2s[wi] {
					if si < snlen {
						sns[si]++
					}
				}
			}
		}

		// 枚举过滤器中的每个句子及其命中单词数
		for si, sn := range sns {
			if sn > 0 {
				// 判断过滤器中句子命中的单词数占该句子总单词数的比例是否达到阈值
				if float32(sn)/float32(len(lg.s2w[si])) >= s2wThreshold {
					// 判断待过滤句子和过滤器中句子的相似度是否达到阈值
					if calcSimilarity(wis, lg.s2w[si]) >= s2sThreshold {
						return true
					}
				}
				sns[si] = 0
			}
		}
	}
	return false
}

// 计算两个单词序列的相似度
// 算法是Damerau–Levenshtein编辑距离的改造，从比较字符改成比较索引。
// 算法来源https://github.com/hbollon/go-edlib/blob/master/levenshtein.go
func calcSimilarity(wis1, wis2 []int) float32 {
	wis1Len := len(wis1)
	wis2Len := len(wis2)
	if wis1Len == 0 {
		return 0
	} else if wis2Len == 0 {
		return 0
	} else if indexEqual(wis1, wis2) {
		return 1
	} else if wis1Len < wis2Len {
		return calcSimilarity(wis2, wis1)
	}

	row := minInt(wis1Len+1, 3)
	matrix := make([][]int, row)
	for i := 0; i < row; i++ {
		matrix[i] = make([]int, wis2Len+1)
		matrix[i][0] = i
	}

	for j := 0; j <= wis2Len; j++ {
		matrix[0][j] = j
	}

	var count int
	for i := 1; i <= wis1Len; i++ {
		matrix[i%3][0] = i
		for j := 1; j <= wis2Len; j++ {
			if wis1[i-1] == wis2[j-1] {
				count = 0
			} else {
				count = 1
			}

			matrix[i%3][j] = minInt(minInt(matrix[(i-1)%3][j]+1, matrix[i%3][j-1]+1),
				matrix[(i-1)%3][j-1]+count)
			if i > 1 && j > 1 && wis1[i-1] == wis2[j-2] && wis1[i-2] == wis2[j-1] {
				matrix[i%3][j] = minInt(matrix[i%3][j], matrix[(i-2)%3][j-2]+1)
			}
		}
	}

	distance := matrix[wis1Len%3][wis2Len]
	return float32(wis1Len-distance) / float32(wis1Len)
}

func minInt(a int, b int) int {
	if b < a {
		return b
	}
	return a
}

func indexEqual(a, b []int) bool {
	if len(a) != len(b) {
		return false
	}
	for i, v := range a {
		if v != b[i] {
			return false
		}
	}
	return true
}

func stringToBytes(s string) []byte {
	return unsafe.Slice(unsafe.StringData(s), len(s))
}

func bytesToString(b []byte) string {
	return unsafe.String(unsafe.SliceData(b), len(b))
}

func (lg *LeakGuard) Match(text string) ([]string, bool) {

	var answer []string
	lg.buff += text
	lg.stream = append(lg.stream, text)
	if lg.level == ORGRiskLevelMedium {
		for _, char := range text {
			if char == strSplitTag {
				answer = []string{_FriendlyFormat}
				return answer, true
			}
		}
		lg.buff = ""
		lg.stream = make([]string, 0)
		answer = append(answer, text)
		return answer, false
	} else if lg.level == ORGRiskLevelHigh {
		frase := gosbd.NewSegmenter("zh").Segment(lg.buff)
		if len(frase) >= 2 {
			answer = lg.stream
			if lg.matchTokens(frase) {
				answer = []string{_FriendlyFormat}
				return answer, true
			}
			//保留最后一个文本
			lg.stream = lg.stream[len(lg.stream)-1:]
			answer = answer[:len(answer)-1]
			return answer, false
		}
		return answer, false
	}

	answer = append(answer, text)
	lg.buff = ""
	lg.stream = make([]string, 0)
	return answer, false
}

func (lg *LeakGuard) matchTokens(token []string) bool {
	s := ""
	for _, v := range token {
		if lg.check(stringToBytes(v)) {
			return true
		}
		s = v
	}

	//过滤已经检测的文本
	lg.buff = s
	return false
}

func (lg *LeakGuard) Check() ([]string, bool) {
	if lg.check(stringToBytes(lg.buff)) {
		return []string{_FriendlyFormat}, true
	}
	return lg.stream, false
}

func (lg *LeakGuard) Clear() {
	lg.buff = ""
	lg.stream = make([]string, 0)
}
