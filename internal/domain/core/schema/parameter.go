package schema

import (
	"context"
	"encoding/json"
	"fmt"
	"math"
	"secwalk/internal/domain/core/endpoint"
	"secwalk/internal/domain/core/parser"
	"secwalk/internal/domain/core/template"
	"secwalk/internal/domain/core/xfile"
	"secwalk/pkg/util"
	"strconv"
	"unicode/utf8"

	"github.com/oliveagle/jsonpath"
)

const (
	TypeString  = "string"
	TypeInteger = "integer"
	TypeBoolean = "boolean"
	TypeFloat   = "float"
	TypeArray   = "array"
	TypeObject  = "object"
	TypeFile    = "file"
	TypeFiles   = "files"

	// FOR MCP
	TypeNumber = "number" // integer+float
	TypeNull   = "null"
)

func supportParameterType(s string) bool {
	switch s {
	case TypeString, TypeInteger, TypeBoolean,
		TypeFloat, TypeArray, TypeObject, TypeFile,
		TypeFiles, TypeNumber, TypeNull:
		return true
	default:
		return false
	}
}

const (
	LocationBody   = "body"
	LocationPath   = "path"
	LocationQuery  = "query"
	LocationHeader = "header"
)

func supportParameterLocation(s string) bool {
	switch s {
	case LocationBody, LocationPath,
		LocationQuery, LocationHeader, "":
		return true
	default:
		return false
	}
}

const (
	ParamValueTypeLiteral    = "literal"
	ParamValueTypeAssociate  = "associate"
	ParamValueTypeReference  = "reference"
	ParamValueTypeRendering  = "rendering"
	ParamValueTypeRegexp     = "regexp"
	ParamValueTypeRegexpDict = "regexp_dict"
	ParamValueTypeJsonPath   = "jsonpath"
)

type Parameter struct {
	Name         string      `json:"name,omitempty"`          // 参数名称
	Description  string      `json:"description,omitempty"`   // 参数描述
	Type         string      `json:"type,omitempty"`          // 参数类型
	Schema       []Parameter `json:"schema,omitempty"`        // 参数视图
	Required     bool        `json:"required,omitempty"`      // 是否必须
	Configed     bool        `json:"configed,omitempty"`      // 用户配置
	Hidden       bool        `json:"hidden,omitempty"`        // 是否隐藏
	LongTerm     bool        `json:"long_term,omitempty"`     // 是否长期记忆
	Value        *ExprValue  `json:"value,omitempty"`         // 参数取值方式
	DefaultValue any         `json:"default_value,omitempty"` // 参数默认值
	ExampleValue any         `json:"example_value,omitempty"` // 参数示例值
	Location     string      `json:"location,omitempty"`      // 参数位置，仅HTTP请求参数使用
}

func verifyParameter(p Parameter, fullcheck bool) error {
	// 校验参数名称
	if fullcheck {
		if utf8.RuneCountInString(p.Name) < 1 || utf8.RuneCountInString(p.Name) > 128 {
			return fmt.Errorf("%w: %s", ErrParameterName, p.Name)
		}
	}

	// 校验参数描述
	if len(p.Description) > 0 {
		if utf8.RuneCountInString(p.Description) < 2 || utf8.RuneCountInString(p.Description) > 1024 {
			return fmt.Errorf("%w: %s", ErrParameterDescription, p.Name)
		}
	}

	// 校验参数类型
	if len(p.Type) > 0 {
		if !supportParameterType(p.Type) {
			return fmt.Errorf("%w: %s", ErrParameterType, p.Name)
		}
	}

	// 取值配置
	if err := verifyExprValue(p.Value); err != nil {
		return fmt.Errorf("%w: %s", err, p.Name)
	}

	// 参数位置校验
	if len(p.Location) > 0 {
		if !supportParameterLocation(p.Location) {
			return fmt.Errorf("%w: %s", ErrParameterLocation, p.Name)
		}
	}

	if p.DefaultValue != nil {
		switch p.Type {
		case TypeString, TypeFile:
			if _, ok := p.DefaultValue.(string); !ok {
				return fmt.Errorf("%w: %s", ErrParameterString, p.Name)
			}
		case TypeInteger:
			_, ok1 := p.DefaultValue.(int)
			_, ok2 := p.DefaultValue.(float64)
			if !ok1 && !ok2 {
				return fmt.Errorf("%w: %s", ErrParameterInteger, p.Name)
			}
		case TypeBoolean:
			if _, ok := p.DefaultValue.(bool); !ok {
				return fmt.Errorf("%w: %s", ErrParameterBoolean, p.Name)
			}
		case TypeFloat:
			if _, ok := p.DefaultValue.(float64); !ok {
				return fmt.Errorf("%w: %s", ErrParameterfloat, p.Name)
			}
		case TypeArray:
			if _, ok := p.DefaultValue.([]any); !ok {
				return fmt.Errorf("%w: %s", ErrParameterArray, p.Name)
			}
		case TypeObject:
			if _, ok := p.DefaultValue.(map[string]any); !ok {
				return fmt.Errorf("%w: %s", ErrParameterObject, p.Name)
			}
		case TypeFiles:
			if _, ok := p.DefaultValue.([]string); !ok {
				return fmt.Errorf("%w: %s", ErrParameterObject, p.Name)
			}
		}
	}

	for _, v := range p.Schema {
		if p.Type == TypeArray {
			if err := verifyParameter(v, false); err != nil {
				return err
			}
		} else {
			if err := verifyParameter(v, true); err != nil {
				return err
			}
		}
	}

	return nil
}

func GetValueFromName(name string, params []Parameter,
	inputs map[string]any) (*Value, error) {
	for _, param := range params {
		if param.Name == name {
			return param.GetValue(inputs)
		}
	}
	return nil, fmt.Errorf("value not found: %s", name)
}

func (p Parameter) VerifyValueType(v any) error {
	switch p.Type {
	case TypeString:
		if _, ok := v.(string); !ok {
			return fmt.Errorf("%w: %s", ErrParameterString, p.Name)
		}
	case TypeInteger:
		_, ok1 := v.(int)
		_, ok2 := v.(float64)
		if !ok1 && !ok2 {
			return fmt.Errorf("%w: %s", ErrParameterInteger, p.Name)
		}
	case TypeBoolean:
		if _, ok := v.(bool); !ok {
			return fmt.Errorf("%w: %s", ErrParameterBoolean, p.Name)
		}
	case TypeFloat:
		if _, ok := v.(float64); !ok {
			return fmt.Errorf("%w: %s", ErrParameterfloat, p.Name)
		}
	case TypeArray:
		if _, ok := v.([]any); !ok {
			return fmt.Errorf("%w: %s", ErrParameterArray, p.Name)
		}
	case TypeObject:
		if _, ok := v.(map[string]any); !ok {
			return fmt.Errorf("%w: %s", ErrParameterObject, p.Name)
		}
	}
	return nil
}

func (p Parameter) InitValue() any {
	switch p.Type {
	case TypeInteger:
		return 0
	case TypeBoolean:
		return false
	case TypeFloat, TypeNumber:
		return 0.0
	case TypeArray:
		return []any{}
	case TypeObject:
		return map[string]any{}
	case TypeFiles:
		return []string{}
	case TypeNull:
		return nil
	default:
		return ""
	}
}

func (p Parameter) GetValue(kvs map[string]any) (*Value, error) {
	var ret *Value
	var err error

	if p.Value != nil {
		ret, err = p.Value.GetValue(kvs, p.Type)
		if err != nil {
			return nil, err
		}
	} else {
		val, ok := kvs[p.Name]
		if ok {
			ret = &Value{val}
		} else {
			if p.DefaultValue != nil {
				ret = &Value{p.DefaultValue}
			}
		}
	}

	if ret == nil {
		return nil, fmt.Errorf("%w: %s", ErrParameterValueNotfound, p.Name)
	}

	return ret, nil
}

type ExprValue struct {
	Type string     `json:"type,omitempty"`
	Expr string     `json:"expr,omitempty"`
	Text *ExprValue `json:"text,omitempty"`
}

func verifyExprValue(ev *ExprValue) error {
	if ev == nil {
		return nil
	}

	if ev.Type != ParamValueTypeLiteral &&
		ev.Type != ParamValueTypeAssociate &&
		ev.Type != ParamValueTypeReference &&
		ev.Type != ParamValueTypeRendering &&
		ev.Type != ParamValueTypeRegexp &&
		ev.Type != ParamValueTypeRegexpDict &&
		ev.Type != ParamValueTypeJsonPath {
		return ErrExprValueType
	}

	if ev.Type == ParamValueTypeRegexp || ev.Type == ParamValueTypeJsonPath {
		if ev.Text == nil {
			return ErrExprValueExpr
		}
	}

	//检查表达式是否正确
	if ev.Expr == "" {
		return ErrExprValueExpr
	}

	return nil
}

func (e *ExprValue) GetValue(kvs map[string]any, type1 ...string) (*Value, error) {
	var text string
	if e.Text != nil {
		v, err := e.Text.GetValue(kvs)
		if err != nil {
			return nil, err
		}
		text = v.String()
	}

	switch e.Type {
	case ParamValueTypeLiteral, ParamValueTypeAssociate:
		value := &Value{e.Expr}
		if len(type1) > 0 {
			value.SetTrans(e.Expr, type1[0])
		}
		return value, nil
	case ParamValueTypeReference:
		val, err := jsonpath.JsonPathLookup(kvs, e.Expr)
		if err != nil {
			return nil, fmt.Errorf("%w: %v", ErrConfig, err)
		}
		return &Value{val}, nil
	case ParamValueTypeRendering:
		val, err := template.RenderTemplate(e.Expr, kvs)
		if err != nil {
			return nil, fmt.Errorf("%w: %v", ErrConfig, err)
		}
		value := &Value{val}
		if len(type1) > 0 {
			value.SetTrans(val, type1[0])
		}
		return value, nil
	case ParamValueTypeRegexp:
		var result any
		var err error
		func() {
			defer func() {
				if r := recover(); r != nil {
					result = nil
					err = fmt.Errorf("regexp config error: %v", r)
				}
			}()

			val, parseErr := parser.NewRegexParser(e.Expr).Parse(text)
			if parseErr != nil {
				result = nil
				err = parseErr
				return
			}
			result = val
		}()
		if err != nil {
			return nil, err
		}
		return &Value{result}, nil
	case ParamValueTypeRegexpDict:
		val, err := parser.NewRegexDict(e.Expr).Parse(text)
		if err != nil {
			return nil, err
		}
		return &Value{val}, nil
	case ParamValueTypeJsonPath:
		val, err := parser.NewJsonpathParser(e.Expr).Parse(text)
		if err != nil {
			return nil, err
		}
		return &Value{val}, nil
	default:
		return nil, fmt.Errorf("%w: value not found", ErrConfig)
	}
}

type Value struct {
	any
}

func (v *Value) V() any {
	return v.any
}

func (v *Value) Set(val any) {
	v.any = val
}

func (v *Value) SetTrans(val, type1 string) {
	switch type1 {
	case TypeString, TypeFile:
		v.any = val
	case TypeInteger:
		fval, err := strconv.ParseFloat(val, 64)
		if err != nil {
			v.any = val
		} else {
			v.any = int(fval)
		}
	case TypeBoolean:
		bval, err := strconv.ParseBool(val)
		if err != nil {
			v.any = val
		} else {
			v.any = bval
		}
	case TypeFloat, TypeNumber:
		fval, err := strconv.ParseFloat(val, 64)
		if err != nil {
			v.any = val
		} else {
			v.any = fval
		}
	case TypeArray:
		aval := make([]any, 0)
		if err := json.Unmarshal([]byte(val), &aval); err != nil {
			v.any = val
		} else {
			v.any = aval
		}
	case TypeObject:
		oval := make(map[string]any, 0)
		if err := json.Unmarshal([]byte(val), &oval); err != nil {
			v.any = val
		} else {
			v.any = oval
		}
	case TypeFiles:
		v.any = []string{val}
	}
}

func (v *Value) String() string {
	switch v.any.(type) {
	case string:
		return v.any.(string)
	case int:
		return strconv.Itoa(v.any.(int))
	case bool:
		return strconv.FormatBool(v.any.(bool))
	case float32:
		return strconv.FormatFloat(v.any.(float64), 'f', -1, 32)
	case float64:
		return strconv.FormatFloat(v.any.(float64), 'f', -1, 64)
	default:
		return util.ToPureJsonString(v.any)
	}
}

func (v *Value) Int() int {
	switch v.any.(type) {
	case string:
		i, _ := strconv.Atoi(v.any.(string))
		return i
	case int:
		return v.any.(int)
	case float32:
		return int(v.any.(float32))
	case float64:
		return int(v.any.(float64))
	default:
		return 0
	}
}

func (v *Value) Files() []string {
	switch vs := v.any.(type) {
	case map[string]any, *xfile.File:
		data, err := json.Marshal(vs)
		if err != nil {
			return nil
		}

		f, err := xfile.Load(data)
		if err != nil {
			return nil
		}
		return []string{f.String()}
	case []any:
		files := make([]string, 0, len(vs))
		for _, vv := range vs {
			data, err := json.Marshal(vv)
			if err != nil {
				continue
			}

			f, err := xfile.Load(data)
			if err != nil {
				continue
			}

			files = append(files, f.String())
		}
		return files
	case []string:
		return vs
	case string:
		return []string{vs}
	default:
		return nil
	}
}

func (v *Value) IsString() bool {
	_, ok := v.any.(string)
	return ok
}

func (v *Value) IsFile() bool {
	_, ok := v.any.(*xfile.File)
	return ok
}

func (v *Value) IsStringList() ([]string, bool) {
	vals, ok := v.any.([]any)
	if !ok {
		return nil, false
	}

	list := make([]string, len(vals))
	for i, v := range vals {
		str, ok := v.(string)
		if !ok {
			return nil, false
		}
		list[i] = str
	}

	return list, true
}

func (v *Value) IsObject() (map[string]any, bool) {
	m, ok := v.any.(map[string]any)
	return m, ok
}

func (p Parameter) InitFile(ctx context.Context, kvs map[string]any, org string) (any, error) {

	ret := &Value{}
	val, ok := kvs[p.Name]
	if ok {
		ret = &Value{val}
	} else {
		if p.DefaultValue != nil {
			ret = &Value{p.DefaultValue}
		}
	}
	switch p.Type {
	case TypeFile:
		str, ok := ret.V().(string)
		if !ok {
			return ret, nil
		}

		f, err := xfile.Parse(str)
		if err != nil {
			return nil, err
		}

		len, err := endpoint.StatFile(ctx, str, org)
		if err != nil {
			return nil, err
		}
		f.Size = math.Round(float64(len)/1024*100) / 100
		ret = &Value{f.ToMap()}
	case TypeFiles:
		fs, ok := ret.V().([]any)
		if !ok {
			return nil, fmt.Errorf("%w: %s", ErrParameterFiles, p.Name)
		}

		vs := make([]any, 0)
		for _, v := range fs {
			str, ok := v.(string)
			if !ok {
				return ret, nil
			}

			f, err := xfile.Parse(str)
			if err != nil {
				return nil, err
			}

			len, err := endpoint.StatFile(ctx, str, org)
			if err != nil {
				return nil, err
			}
			f.Size = math.Round(float64(len)/1024*100) / 100

			vs = append(vs, f.ToMap())
		}
		ret = &Value{vs}
	}

	return ret.V(), nil
}

// options 兼容旧版本
func conversion(cs []Component) []Component {
	ncs := make([]Component, 0)
	for _, c := range cs {
		ps := make([]any, 0)
		switch c.Options.(type) {
		case map[string]any:
			for k, p := range c.Options.(map[string]any) {
				ps = append(ps, Parameter{
					Name: k,
					Type: TypeString,
					Value: &ExprValue{
						Type: ParamValueTypeLiteral,
						Expr: fmt.Sprintf("%v", p),
					},
				})
			}
		case []any:
			ps = c.Options.([]any)
		}

		ncs = append(ncs, Component{
			ID:      c.ID,
			Options: ps,
		})
	}
	return ncs
}
