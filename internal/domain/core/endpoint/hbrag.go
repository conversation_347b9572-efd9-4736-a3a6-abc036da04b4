package endpoint

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"secwalk/internal/domain/core/schema/ext"
	"secwalk/pkg/logger"
	"secwalk/pkg/selector"
	"secwalk/pkg/util"

	"github.com/sirupsen/logrus"
)

var (
	ErrRetrieveDoc        = errors.New("retrieve document failed")
	ErrRetrieveDocTimeout = errors.New("retrieve document failed, timeout")
)

type RAGFileChunk struct {
	MixRetrieveResults []struct {
		FileID          string  `json:"fileId"`
		SourceID        string  `json:"sourceId"`
		PageNumber      []int   `json:"pageNumber"`
		Score           float64 `json:"score"`
		PreviousContent string  `json:"previousContent"`
		ChunkContent    string  `json:"chunkContent"`
		NextContent     string  `json:"nextContent"`
		RetrieveMode    string  `json:"retrieveMode"`
	} `json:"mixRetrieveResults"`
}

type RAGRetrieveParam struct {
	Query       string   `json:"query"`
	Size        int      `json:"size"`
	Labels      []string `json:"labels,omitempty"`
	Owner       string   `json:"owner,omitempty"`
	Permissions []string `json:"permissions,omitempty"`
	UID         string   `json:"uid,omitempty"`
	QID         string   `json:"qid,omitempty"`
}

func RAGRetrieve(param RAGRetrieveParam, opts ...Option) (*RAGFileChunk, error) {
	opt := newOptions()
	for _, o := range opts {
		o(opt)
	}

	ins, err := selector.G().HBRAGInstance()
	if err != nil {
		return nil, err
	}

	data, err := json.Marshal(param)
	if err != nil {
		return nil, err
	}

	logrus.WithField(logger.KeyCategory, logger.CategoryCore).
		WithField(logger.KeyEXT, opt.ext).
		Infof("rag retrieve request: %s %s", ins.Host, string(data))

	ret := &RAGFileChunk{}
	req, err := http.NewRequest(
		http.MethodPost,
		fmt.Sprintf("http://%s/v2/rag/retrieve", ins.Host),
		bytes.NewReader(data),
	)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", "application/json")
	req.Header.Set("X-TID", opt.ext.GetValue(ext.EXTTID))
	req.Header.Set("X-UID", opt.ext.GetValue(ext.EXTXUID)) //调用者用户ID
	req.Header.Set("X-ORG", opt.ext.GetValue(ext.EXTXORG)) // 调用者租户ID
	req.Header.Set("ORDER", opt.ext.GetValue(ext.EXTORDER))
	req.Header.Set("EXT", opt.ext.ToString())

	for k, v := range ins.Metas {
		req.Header.Set(k, v)
	}

	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		goto END
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		msg, _ := io.ReadAll(resp.Body)
		err = fmt.Errorf("rag retrieve response error: %d %s", resp.StatusCode, msg)
		goto END
	}

	if err = json.NewDecoder(resp.Body).Decode(&ret); err != nil {
		err = fmt.Errorf("rag response decode error: %s", err.Error())
		goto END
	}

	logrus.WithField(logger.KeyCategory, logger.CategoryCore).
		WithField(logger.KeyEXT, opt.ext).
		Debugf("rag retrieve all chunk: %s", util.ToJsonString(ret))

	logrus.WithField(logger.KeyCategory, logger.CategoryCore).
		WithField(logger.KeyEXT, opt.ext).
		Infof("rag retrieve response success: %d", resp.StatusCode)

END:
	if err != nil {
		if errors.Is(err, context.DeadlineExceeded) {
			return nil, ErrRetrieveDocTimeout
		} else if util.ContainIP(err.Error()) || util.ContainHTTP(err.Error()) {
			return nil, ErrRetrieveDoc
		}
		return nil, err
	}
	return ret, err
}
