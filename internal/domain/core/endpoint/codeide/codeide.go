package codeide

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"secwalk/internal/domain/core/schema"
	"secwalk/internal/domain/core/schema/ext"
	"secwalk/pkg/logger"
	"secwalk/pkg/selector"
	"secwalk/pkg/util"

	"github.com/sirupsen/logrus"
)

const (
	LangPython     = "python"
	LangJavaScript = "javascript"
)

var (
	ErrCodeExecute        = errors.New("code execute failed")
	ErrCodeExecuteTimeout = errors.New("code execute failed, timeout")
	ErrCodeExecuteDecode  = errors.New("code execute failed, response body decode error")
)

type InterpreterRequest struct {
	Lang   string         `json:"lang,omitempty"`
	Code   string         `json:"code,omitempty"`
	Inputs map[string]any `json:"inputs,omitempty"`
}

type InterpreterResponse struct {
	Code    int               `json:"code"`
	Message string            `json:"message,omitempty"`
	Data    InterpreterOutput `json:"data,omitempty"`
}

type InterpreterOutput struct {
	Output  string `json:"output"`
	Console string `json:"console"`
}

func CodeInterpreter(ctx context.Context, body *InterpreterRequest,
	opts ...schema.CallOption) (*InterpreterOutput, error) {
	opt := schema.NewCallOptions()
	for _, o := range opts {
		o(opt)
	}

	address, err := selector.G().SecvirtAddress()
	if err != nil {
		return nil, err
	}

	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}

	logrus.WithField(logger.KeyCategory, logger.CategoryCore).
		WithField(logger.KeyEXT, opt.EXT).
		Infof("[%s] code interpreter start", opt.NodeID)

	req, err := http.NewRequestWithContext(
		ctx,
		http.MethodPost,
		fmt.Sprintf("http://%s/secvirt/v1/code/exec", address),
		bytes.NewReader(buf),
	)
	if err != nil {
		return nil, err
	}

	req.Header.Set("X-TID", opt.EXT.GetValue(ext.EXTTID))
	req.Header.Set("EXT", opt.EXT.ToString())

	var ret = InterpreterResponse{}

	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		goto END
	}
	defer resp.Body.Close()

	if err = json.NewDecoder(resp.Body).Decode(&ret); err != nil {
		logrus.WithField(logger.KeyCategory, logger.CategoryCore).
			WithField(logger.KeyEXT, opt.EXT).
			Errorf("[%s] code interpreter error: %s", opt.NodeID, resp.Status)
		err = ErrCodeExecuteDecode
		goto END
	}

	if resp.StatusCode != http.StatusOK || (ret.Code != 0 && ret.Code != 200) {
		logrus.WithField(logger.KeyCategory, logger.CategoryCore).
			WithField(logger.KeyEXT, opt.EXT).
			Errorf("[%s] code interpreter error: %s", opt.NodeID, ret.Message)
		err = errors.New(ret.Message)
		goto END
	}

END:
	if err != nil {
		if errors.Is(err, context.DeadlineExceeded) {
			err = ErrCodeExecuteTimeout
		} else if util.ContainIP(err.Error()) || util.ContainHTTP(err.Error()) {
			err = ErrCodeExecute
		}
		return nil, err
	}

	return &ret.Data, nil
}

type InterpreterResponseV2 struct {
	Code    int                 `json:"code"`
	Message string              `json:"message,omitempty"`
	Data    InterpreterOutputV2 `json:"data,omitempty"`
}

type InterpreterOutputV2 struct {
	Result  any   `json:"result"`
	Errors  []any `json:"errors"`
	Stdouts []any `json:"stdouts"`
	Stderrs []any `json:"stderrs"`
}

func CodeInterpreterV2(ctx context.Context, body *InterpreterRequest,
	opts ...schema.CallOption) (*InterpreterOutputV2, error) {
	opt := schema.NewCallOptions()
	for _, o := range opts {
		o(opt)
	}

	address, err := selector.G().SecvirtAddress()
	if err != nil {
		return nil, err
	}

	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}

	logrus.WithField(logger.KeyCategory, logger.CategoryCore).
		WithField(logger.KeyEXT, opt.EXT).
		Infof("[%s] code interpreter v2 start", opt.NodeID)

	req, err := http.NewRequestWithContext(
		ctx,
		http.MethodPost,
		fmt.Sprintf("http://%s/secvirt/v2/code/execute", address),
		bytes.NewReader(buf),
	)
	if err != nil {
		return nil, err
	}

	req.Header.Set("X-TID", opt.EXT.GetValue(ext.EXTTID))
	req.Header.Set("EXT", opt.EXT.ToString())

	var ret = InterpreterResponseV2{}

	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		goto END
	}
	defer resp.Body.Close()

	if err = json.NewDecoder(resp.Body).Decode(&ret); err != nil {
		logrus.WithField(logger.KeyCategory, logger.CategoryCore).
			WithField(logger.KeyEXT, opt.EXT).
			Errorf("[%s] code interpreter v2 error: %s", opt.NodeID, resp.Status)
		err = ErrCodeExecuteDecode
		goto END
	}

	if resp.StatusCode != http.StatusOK || (ret.Code != 0 && ret.Code != 200) {
		logrus.WithField(logger.KeyCategory, logger.CategoryCore).
			WithField(logger.KeyEXT, opt.EXT).
			Errorf("[%s] code interpreter v2 error: %s", opt.NodeID, ret.Message)
		err = errors.New(ret.Message)
		goto END
	}

END:
	if err != nil {
		if errors.Is(err, context.DeadlineExceeded) {
			err = ErrCodeExecuteTimeout
		} else if util.ContainIP(err.Error()) || util.ContainHTTP(err.Error()) {
			err = ErrCodeExecute
		}
		return nil, err
	}

	return &ret.Data, nil
}
