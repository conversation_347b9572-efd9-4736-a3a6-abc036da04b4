package hostmcp

import (
	"context"
	"fmt"
	"net/http"
	"secwalk/internal/domain/core/schema"
	"secwalk/internal/domain/core/schema/ext"
	"secwalk/pkg/selector"
	"secwalk/pkg/util"

	"github.com/go-resty/resty/v2"
	"github.com/mark3labs/mcp-go/client"
	"github.com/mark3labs/mcp-go/mcp"
)

type Client struct {
	endpoint   *MCPEndpoint
	client     *client.Client
	initResult *mcp.InitializeResult
}

func NewMCPClient(ctx context.Context, name string, entry schema.ServerEntry,
	opts ...schema.CallOption) (*Client, error) {
	res, err := GetMcpProxy(ctx, schema.ServersFile{
		McpServers: map[string]schema.ServerEntry{name: entry},
	}, opts...)
	if err != nil {
		return nil, err
	}

	cli, err := client.NewStreamableHttpClient(res.Host + res.Path)
	if err != nil {
		return nil, fmt.Errorf("%w: %v", schema.ErrMCPServerConnect, err)
	}

	if err := cli.Start(ctx); err != nil {
		return nil, fmt.Errorf("%w: %v", schema.ErrMCPServerConnect, err)
	}

	initRequest := mcp.InitializeRequest{}
	initRequest.Params.ProtocolVersion = mcp.LATEST_PROTOCOL_VERSION
	initRequest.Params.Capabilities = mcp.ClientCapabilities{}
	initRequest.Params.ClientInfo = mcp.Implementation{
		Name:    "secwalk",
		Version: "v1.0.0",
	}

	initResult, err := cli.Initialize(ctx, initRequest)
	if err != nil {
		return nil, fmt.Errorf("%w: %v", schema.ErrMCPServerConnect, err)
	}

	return &Client{
		endpoint:   res,
		client:     cli,
		initResult: initResult,
	}, nil
}

func (c *Client) ProxyHost() string {
	return c.endpoint.Host + c.endpoint.Path
}

type ServerInfo struct {
	Toolkit *schema.Toolkit `json:"toolkit,omitempty"`
}

func (c *Client) QueryServer(ctx context.Context) (*ServerInfo, error) {

	// 工具信息
	tk, err := c.QueryTools(ctx)
	if err != nil {
		return nil, err
	}

	// 提示词信息

	// 资源信息

	return &ServerInfo{
		Toolkit: tk,
	}, nil
}

func (c *Client) QueryTools(ctx context.Context) (*schema.Toolkit, error) {
	res := &schema.Toolkit{
		Name:        c.initResult.ServerInfo.Name,
		Description: c.initResult.Instructions,
		Type:        schema.ToolkitTypeCustom,
		Tools:       make([]schema.ToolConfig, 0),
	}

	for _, v := range c.endpoint.Tools {
		tool := schema.ToolConfig{}
		tool.Name = v.Name
		tool.Description = v.Description
		tool.InputParameters = make([]schema.Parameter, 0)
		tool.Status = 1

		for argName, argInfo := range v.InputSchema.Properties {
			required := false
			if util.ValueInList[string](argName, v.InputSchema.Required) {
				required = true
			}

			if info, ok := argInfo.(map[string]any); ok {
				parm := schema.Parameter{Name: argName, Required: required}

				desc, ok1 := info["description"].(string)
				if ok1 {
					parm.Description = desc
				}
				typp, ok2 := info["type"].(string)
				if ok2 {
					parm.Type = typp
				}

				tool.InputParameters = append(tool.InputParameters, parm)
			}
		}

		tool.OutputParameters = append(tool.OutputParameters, []schema.Parameter{
			{
				Name: "text",
				Type: schema.TypeString,
			},
			{
				Name: "image",
				Type: schema.TypeObject,
			},
			{
				Name: "resource",
				Type: schema.TypeObject,
			},
		}...)

		res.Tools = append(res.Tools, tool)
	}

	return res, nil
}

func (c *Client) CallTool(ctx context.Context, name string,
	inputs map[string]any) (*mcp.CallToolResult, error) {
	callRequest := mcp.CallToolRequest{}
	callRequest.Params.Name = name
	callRequest.Params.Arguments = inputs

	res, err := c.client.CallTool(ctx, callRequest)
	if err != nil {
		return nil, fmt.Errorf("%w: %v", schema.ErrMCPServerRequest, err)
	}
	return res, nil
}

type MCPProxyResult struct {
	Code    int         `json:"code"`
	Message string      `json:"message,omitempty"`
	Data    MCPEndpoint `json:"data,omitempty"`
}

type MCPEndpoint struct {
	Name  string     `json:"name,omitempty"`
	Host  string     `json:"host,omitempty"`
	Path  string     `json:"path,omitempty"`
	Tools []mcp.Tool `json:"tools"`
}

func GetMcpProxy(ctx context.Context, cfg schema.ServersFile, opts ...schema.CallOption) (*MCPEndpoint, error) {
	opt := schema.NewCallOptions()
	for _, o := range opts {
		o(opt)
	}

	address, err := selector.G().SecvirtAddress()
	if err != nil {
		return nil, err
	}

	var res MCPProxyResult

	resp, err := resty.New().R().
		SetContext(ctx).
		SetHeaders(map[string]string{
			"X-TID": opt.EXT.GetValue(ext.EXTTID),
			"EXT":   opt.EXT.ToString(),
		}).
		SetBody(map[string]any{
			"config": cfg,
		}).
		SetResult(&res).
		Post(fmt.Sprintf("http://%s/secvirt/v2/mcp/proxy", address))
	if err != nil {
		return nil, err
	}

	if resp.StatusCode() != http.StatusOK {
		return nil, schema.ErrMCPProxy
	}

	if res.Code != http.StatusOK {
		return nil, fmt.Errorf("%w: %v", schema.ErrMCPProxy, res.Message)
	}

	return &res.Data, nil
}
