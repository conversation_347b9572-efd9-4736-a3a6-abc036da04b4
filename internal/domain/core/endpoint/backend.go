package endpoint

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"secwalk/pkg/selector"
	"secwalk/pkg/util"
	"strings"
)

var (
	ErrLicenseIncorrect = errors.New("incorrect license")
	ErrKnSearch         = errors.New("knowledge search failed")
	ErrKnSearchTimeout  = errors.New("knowledge search failed, timeout")
)

type License struct {
	Model Model `json:"model"`
	Agent Agent `json:"agent"`
}

type Model struct {
	Key string `json:"key"`
}

type Agent struct {
	Licenses []AgentLicense `json:"authorizations"`
}

type AgentLicense struct {
	ID   string `json:"id"`
	Name string `json:"name"`
	Key  string `json:"key"`
}

func GetLicense() (*License, error) {
	type response struct {
		Code int      `json:"code"`
		Msg  string   `json:"msg"`
		Data *License `json:"data"`
	}

	ins, err := selector.G().BackendInstance()
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", fmt.Sprintf("http://%s/api/licence/getKey", ins.Host), nil)
	if err != nil {
		return nil, err
	}

	for k, v := range ins.Metas {
		req.Header.Set(k, v)
	}

	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	ret := &response{}
	if err := json.NewDecoder(resp.Body).Decode(ret); err != nil {
		return nil, err
	}

	if ret.Code != 0 {
		return nil, ErrLicenseIncorrect
	}

	return ret.Data, nil
}

type Knowledge struct {
	ID          string          `json:"id,omitempty"`          // 知识库ID
	Name        string          `json:"name,omitempty"`        // 名称
	Description string          `json:"description,omitempty"` // 描述
	TenantID    string          `json:"tenantId,omitempty"`    // 租户ID
	Files       []KnowledgeFile `json:"files,omitempty"`       // 文件
}

type KnowledgeFile struct {
	ID               string `json:"id"`               // 文件ID
	FileName         string `json:"fileName"`         // 文件名
	FileExtName      string `json:"fileExtName"`      // 扩展名
	OriginalFileName string `json:"originalFileName"` // 原始文件名
	FileSize         string `json:"fileSize"`         // 文件大小
}

func KnowledgeSearch(id string) (*Knowledge, error) {
	type response struct {
		Code int       `json:"code,omitempty"` // 状态码
		Msg  string    `json:"msg,omitempty"`  // 消息
		Data Knowledge `json:"data,omitempty"` // 分页数据
	}

	ins, err := selector.G().BackendInstance()
	if err != nil {
		return nil, err
	}

	ret := &response{}
	req, err := http.NewRequest(
		http.MethodPost,
		fmt.Sprintf("http://%s/api/knowledge/search", ins.Host),
		strings.NewReader(fmt.Sprintf("{\"id\":%s}", id)),
	)
	if err != nil {
		return nil, err
	}

	for k, v := range ins.Metas {
		req.Header.Set(k, v)
	}
	req.Header.Add("Content-Type", "application/json")

	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		goto END
	}
	defer resp.Body.Close()

	if err = json.NewDecoder(resp.Body).Decode(ret); err != nil {
		return nil, err
	}

	if resp.StatusCode != http.StatusOK || ret.Code != 0 {
		err = errors.New(ret.Msg)
		goto END
	}

END:
	if err != nil {
		if errors.Is(err, context.DeadlineExceeded) {
			return nil, ErrKnSearchTimeout
		} else if util.ContainIP(err.Error()) || util.ContainHTTP(err.Error()) {
			return nil, ErrKnSearch
		}
		return nil, err
	}

	return &ret.Data, nil
}

var (
	ErrAccessTheFile  = errors.New("access the files failed")
	ErrOpenApiTimeout = errors.New("openAPI failed, timeout")
)

func FileAuthentication(fileID string) ([]string, error) {
	type response struct {
		Code int      `json:"code,omitempty"` // 状态码
		Msg  string   `json:"msg,omitempty"`  // 消息
		Data []string `json:"data,omitempty"` // 租户id
	}

	var data []string

	ins, err := selector.G().OpenApiInstance()
	if err != nil {
		return data, err
	}

	ret := &response{}
	req, err := http.NewRequest(
		http.MethodPost,
		fmt.Sprintf("http://%s/api/file/getTenant", ins.Host),
		strings.NewReader(fmt.Sprintf("{\"fileId\":\"%s\"}", fileID)),
	)
	if err != nil {
		return data, err
	}

	for k, v := range ins.Metas {
		req.Header.Set(k, v)
	}
	req.Header.Add("Content-Type", "application/json")

	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		goto END
	}
	defer resp.Body.Close()

	if err = json.NewDecoder(resp.Body).Decode(ret); err != nil {
		return data, err
	}

	if resp.StatusCode == http.StatusOK || resp.StatusCode == http.StatusConflict {
		data = ret.Data
		return data, nil
	}

	goto END

END:
	if err != nil {
		if errors.Is(err, context.DeadlineExceeded) {
			return data, ErrOpenApiTimeout
		} else if util.ContainIP(err.Error()) || util.ContainHTTP(err.Error()) {
			return data, ErrAccessTheFile
		}
		return data, err
	}

	return data, errors.New(ret.Msg)
}
