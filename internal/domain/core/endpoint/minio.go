package endpoint

import (
	"bytes"
	"context"
	"errors"
	"secwalk/internal/domain/core/schema/ext"
	"secwalk/pkg/db/minio"
	"secwalk/pkg/logger"

	"github.com/sirupsen/logrus"
)

const _FileBucket = "annex-file"

var (
	ErrNoAccess = errors.New("no access the file")
)

func GetFile(ctx context.Context, id string, ext_ ext.ExtType) ([]byte, error) {
	logrus.WithField(logger.KeyCategory, logger.CategoryCore).
		WithField(logger.KeyEXT, ext_).
		Debugf("get file meta: %s", id)
	orgIds, err := FileAuthentication(id)
	if err != nil {
		return nil, err
	}

	if orgIds == nil {
		return minio.G().Query(ctx, _FileBucket, id)
	}

	xorg := ext_.GetValue(ext.EXTXORG)
	for _, orgId := range orgIds {
		if xorg != orgId && orgId != "" {
			continue
		}
		logrus.WithField(logger.KeyCategory, logger.CategoryCore).
			WithField(logger.KeyEXT, ext_).
			Debugf("get file data: %s", id)
		return minio.G().Query(ctx, _FileBucket, id)
	}

	logrus.WithField(logger.KeyCategory, logger.CategoryCore).
		WithField(logger.KeyEXT, ext_).
		Warnf("no access the file: %s, x-org: %s", id, xorg)
	return nil, ErrNoAccess
}

func PutFile(ctx context.Context, id string, data []byte) error {
	logrus.WithField(logger.KeyCategory, logger.CategoryCore).
		Debugf("put file data: %s", id)
	return minio.G().Create(ctx, _FileBucket, id, bytes.NewReader(data))
}

func StatFile(ctx context.Context, id, org string) (int64, error) {
	logrus.WithField(logger.KeyCategory, logger.CategoryCore).
		Debugf("get file meta: %s", id)
	orgIds, err := FileAuthentication(id)
	if err != nil {
		return 0, err
	}

	if orgIds == nil {
		return minio.G().QueryStat(ctx, _FileBucket, id)
	}

	for _, orgId := range orgIds {
		if org != orgId && orgId != "" {
			continue
		}

		return minio.G().QueryStat(ctx, _FileBucket, id)
	}

	logrus.WithField(logger.KeyCategory, logger.CategoryCore).
		Warnf("no access the file: %s, x-org: %s", id, org)
	return 0, ErrNoAccess

}
