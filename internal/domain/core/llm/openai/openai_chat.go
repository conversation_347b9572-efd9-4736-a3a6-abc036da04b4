package openai

import (
	"context"
	"errors"
	"io"
	"os"

	"secwalk/internal/domain/core/llm"
	"secwalk/internal/domain/core/message"
	"secwalk/internal/domain/core/schema"

	"github.com/sasha<PERSON>nov/go-openai"
)

// 聊天对话模型
type ChatLLM struct {
	client *openai.Client
	opt    *options
}

var _ llm.Model = &ChatLLM{}

func NewChat(opts ...Option) (*ChatLLM, error) {
	opt := &options{
		token: os.Getenv(EnvVarNameToken),
		model: openai.GPT3Dot5Turbo,
	}

	for _, o := range opts {
		o(opt)
	}

	cfg := openai.DefaultConfig(opt.token)

	return &ChatLLM{
		client: openai.NewClientWithConfig(cfg),
		opt:    opt,
	}, nil
}

func (o *ChatLLM) Call(ctx context.Context, messages []message.Message,
	opts ...schema.CallOption) (*llm.Generation, error) {
	r, err := o.Generate(ctx, [][]message.Message{messages}, opts...)
	if err != nil {
		return nil, err
	}
	if len(r) == 0 {
		return nil, llm.ErrModelResponse
	}
	return &r[0], nil
}

func (o *ChatLLM) Generate(ctx context.Context, messages [][]message.Message,
	opts ...schema.CallOption) (llm.Generations, error) {
	opt := schema.NewCallOptions()
	opt.Model = o.opt.model
	for _, o := range opts {
		o(opt)
	}

	gens := make(llm.Generations, 0, len(messages))
	for _, messageSet := range messages {
		if opt.FnStreaming != nil {
			var text string
			var gen llm.Generation

			stream, err := o.client.CreateChatCompletionStream(ctx, openai.ChatCompletionRequest{
				Model:            opt.Model,
				Messages:         toChatMessages(messageSet),
				MaxTokens:        opt.MaxTokens,
				Temperature:      opt.Temperature,
				TopP:             opt.TopP,
				N:                opt.N,
				Stop:             opt.StopWord,
				FrequencyPenalty: opt.FrequencyPenalty,
				PresencePenalty:  opt.PresencePenalty,
				Seed:             &opt.Seed,
			})
			if err != nil {
				return nil, err
			}
			defer stream.Close()

			for {
				resp, err := stream.Recv()
				if errors.Is(err, io.EOF) {
					break
				}
				if err != nil {
					return nil, err
				}

				if len(resp.Choices[0].Delta.Content) > 0 {
					opt.FnStreaming(ctx, []byte(resp.Choices[0].Delta.Content))
					gen.ID = resp.ID
					gen.Model = resp.Model
					text += resp.Choices[0].Delta.Content
				}
			}
			if len(text) == 0 {
				return nil, llm.ErrModelResponse
			}

			gen.Message = message.NewAssistantChatMessage(text)
			gens = append(gens, gen)
		} else {
			result, err := o.client.CreateChatCompletion(ctx, openai.ChatCompletionRequest{
				Model:            opt.Model,
				Messages:         toChatMessages(messageSet),
				MaxTokens:        opt.MaxTokens,
				Temperature:      opt.Temperature,
				TopP:             opt.TopP,
				N:                opt.N,
				Stop:             opt.StopWord,
				FrequencyPenalty: opt.FrequencyPenalty,
				PresencePenalty:  opt.PresencePenalty,
			})
			if err != nil {
				return nil, err
			}
			if len(result.Choices) == 0 {
				return nil, llm.ErrModelResponse
			}

			gens = append(gens, llm.Generation{
				ID:      result.ID,
				Model:   result.Model,
				Message: message.NewAssistantChatMessage(result.Choices[0].Message.Content),
				TokenUsage: openai.Usage{
					PromptTokens:            result.Usage.PromptTokens,
					CompletionTokens:        result.Usage.CompletionTokens,
					TotalTokens:             result.Usage.TotalTokens,
					PromptCacheHitTokens:    result.Usage.PromptCacheHitTokens,
					PromptCacheMissTokens:   result.Usage.PromptCacheMissTokens,
					PromptTokensDetails:     result.Usage.PromptTokensDetails,
					CompletionTokensDetails: result.Usage.CompletionTokensDetails,
				},
				FinishReason: result.Choices[0].FinishReason,
			})
		}
	}

	return gens, nil
}

func toChatMessages(messages []message.Message) []openai.ChatCompletionMessage {
	msgs := make([]openai.ChatCompletionMessage, 0)
	for _, m := range messages {
		var role string
		switch m.GetRole() {
		case message.ChatMessageRoleUser:
			role = "user"
		case message.ChatMessageRoleAssistant:
			role = "assistant"
		default:
			role = string(m.GetRole())
		}

		msgs = append(msgs, openai.ChatCompletionMessage{
			Role:    role,
			Content: m.GetContent(),
		})
	}
	return msgs
}
