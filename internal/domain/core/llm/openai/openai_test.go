package openai

import (
	"context"
	"fmt"
	"os"
	"secwalk/internal/domain/core/message"
	"secwalk/internal/domain/core/schema"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestChat(t *testing.T) {
	os.Setenv("http_proxy", "127.0.0.1:7890")
	os.Setenv("https_proxy", "127.0.0.1:7890")
	os.Setenv("OPENAI_API_KEY", "")

	cli, err := NewChat()
	require.NoError(t, err)

	res, err := cli.Call(context.Background(),
		[]message.Message{
			message.NewUserChatMessage("你好吗"),
			message.NewUserChatMessage("你是谁"),
			message.NewUserChatMessage("你从哪里来"),
			message.NewUserChatMessage("你几岁了"),
		},
		schema.WithFnStreaming(func(ctx context.Context, chunk []byte) error {
			fmt.Println(string(chunk))
			return nil
		}),
		schema.WithStopWords([]string{"年龄"}),
	)
	assert.NoError(t, err)
	assert.Greater(t, len(res.Message.GetContent()), 0)
}
