package llm

import (
	"context"
	"errors"
	"fmt"

	"secwalk/internal/domain/core/message"
	"secwalk/internal/domain/core/schema"
	"secwalk/pkg/logger"
	"secwalk/pkg/util"

	"github.com/sashabaranov/go-openai"
	"github.com/sirupsen/logrus"
)

var (
	ErrModelAddress         = errors.New("llm service address empty")
	ErrModelResponse        = errors.New("llm response empty")
	ErrModelCall            = errors.New("llm call failed")
	ErrModelResponseTooMuch = errors.New("llm response too much")
	ErrModelLeakOfPrompt    = errors.New("llm leak of prompt")
)

// 聊天模型接口
type Model interface {
	Call(ctx context.Context, messages []message.Message, opts ...schema.CallOption) (*Generation, error)
	Generate(ctx context.Context, messages [][]message.Message, opts ...schema.CallOption) (Generations, error)
}

// 单次结果
type Generation struct {
	ID               string              `json:"id,omitempty"`
	Model            string              `json:"model,omitempty"`
	Message          message.Message     `json:"message,omitempty"`
	LogProbs         openai.LogProbs     `json:"logprobs,omitempty"`
	TokenUsage       openai.Usage        `json:"token_usage,omitempty"`
	FinishReason     openai.FinishReason `json:"finish_reason,omitempty"`
	ReasoningContent string              `json:"reasoning_content,omitempty"`
}

// Batch结果
type Generations []Generation

func Call(ctx context.Context, l Model, promptValue message.PromptValue, opts ...schema.CallOption) (*Generation, error) {
	opt := schema.NewCallOptions()
	for _, o := range opts {
		o(opt)
	}

	logrus.WithField(logger.KeyCategory, logger.CategoryCore).
		WithField(logger.KeyEXT, opt.EXT).
		Infof("[%s] chat llm request: %s", opt.NodeID, util.ToJsonString(opt))

	gen, err := l.Call(ctx, promptValue.Messages(), opts...)
	if err != nil {
		logrus.WithField(logger.KeyCategory, logger.CategoryCore).
			WithField(logger.KeyEXT, opt.EXT).
			Errorf("[%s] chat llm error: %s", opt.NodeID, err)
		if util.ContainIP(err.Error()) || util.ContainHTTP(err.Error()) {
			return nil, ErrModelCall
		}
		return nil, err
	}

	logrus.WithField(logger.KeyCategory, logger.CategoryCore).
		WithField(logger.KeyEXT, opt.EXT).
		Infof("[%s] chat llm response: id:%s, token_usage:%d %d %d, reason:%s", opt.NodeID,
			gen.ID, gen.TokenUsage.PromptTokens, gen.TokenUsage.CompletionTokens,
			gen.TokenUsage.TotalTokens, gen.FinishReason)

	if opt.FnStreamTokenUsage != nil {
		opt.FnStreamTokenUsage(&gen.TokenUsage)
	}
	return gen, err
}

func Generate(ctx context.Context, l Model, promptValues []message.PromptValue, opts ...schema.CallOption) (Generations, error) {
	opt := schema.NewCallOptions()
	for _, o := range opts {
		o(opt)
	}

	prints := ""
	messages := make([][]message.Message, 0, len(promptValues))
	for _, promptValue := range promptValues {
		for _, msg := range promptValue.Messages() {
			prints += fmt.Sprintf("\n[%s] %s", msg.GetRole(), msg.GetContent())
		}
		messages = append(messages, promptValue.Messages())
	}

	logrus.WithField(logger.KeyCategory, logger.CategoryCore).
		WithField(logger.KeyEXT, opt.EXT).
		Infof("[%s] chat llm request: %s", opt.NodeID, util.ToJsonString(opt))

	gens, err := l.Generate(ctx, messages, opts...)
	if err != nil {
		logrus.WithField(logger.KeyCategory, logger.CategoryCore).
			WithField(logger.KeyEXT, opt.EXT).
			Errorf("[%s] chat llm error: %s", opt.NodeID, err.Error())
		if util.ContainIP(err.Error()) || util.ContainHTTP(err.Error()) {
			return nil, ErrModelCall
		}
		return nil, err
	}

	prints = ""
	for _, gen := range gens {
		prints += fmt.Sprintf("\n[%s] %s", gen.Message.GetRole(), gen.Message.GetContent())
	}

	logrus.WithField(logger.KeyCategory, logger.CategoryCore).
		WithField(logger.KeyEXT, opt.EXT).
		Infof("[%s] chat llm response success", opt.NodeID)

	return gens, err
}
