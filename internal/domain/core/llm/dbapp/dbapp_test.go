package dbapp

import (
	"context"
	"fmt"
	"testing"
	"time"

	"secwalk/internal/domain/core/message"
	"secwalk/internal/domain/core/schema"

	nested "github.com/antonfisher/nested-logrus-formatter"
	"github.com/sirupsen/logrus"
	"github.com/stretchr/testify/assert"
)

func TestChatLLM(t *testing.T) {
	logrus.SetFormatter(&nested.Formatter{TimestampFormat: time.RFC3339, HideKeys: false})
	logrus.SetLevel(logrus.DebugLevel)

	cli, err := NewChatLLM(WithAddress("***********:8995"))
	assert.NoError(t, err)

	out, err := cli.Call(context.Background(), []message.Message{
		message.NewUserChatMessage("你好吗"),
		message.NewUserChatMessage("你是谁"),
		message.NewUserChatMessage("你从哪里来"),
		message.NewUserChatMessage("你几岁了"),
	},
		schema.WithFnStreaming(func(ctx context.Context, chunk []byte) error {
			fmt.Println(string(chunk))
			return nil
		}),
		schema.WithStopWords([]string{"年龄"}),
		schema.WithLogProbs(2),
	)
	assert.NoError(t, err)
	assert.Greater(t, len(out.Message.GetContent()), 0)
}
