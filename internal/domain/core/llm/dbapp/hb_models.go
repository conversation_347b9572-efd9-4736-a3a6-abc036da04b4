package dbapp

import (
	"context"
	"encoding/json"
	"net/http"
	"net/url"
	"strconv"
)

const (
	URLSuffixModels = "/secwall/v1/models"

	ChatService      = "hb-serve-chat"
	LoraService      = "hb-serve-lora"
	DefaultMaxTokens = 5000
)

type ModelsResponse struct {
	Data []struct {
		Service string            `json:"service"`
		ID      string            `json:"id"`
		Host    string            `json:"host"`
		Port    int               `json:"port"`
		Metas   map[string]string `json:"metas"`
	} `json:"data"`
}

func (c *Client) GetMaxChatTokens(ctx context.Context) (int, error) {
	req, err := http.NewRequestWithContext(ctx, http.MethodGet,
		(&url.URL{Scheme: "http", Host: c.address, Path: URLSuffixModels}).String(), nil)
	if err != nil {
		return 0, err
	}

	req.Header.Set("Authorization", "Bearer 7178648308129206272")
	res, err := c.cli.Do(req)
	if err != nil {
		return 0, err
	}
	defer res.Body.Close()
	defer c.tr.CloseIdleConnections()

	resp := &ModelsResponse{}
	if err := json.NewDecoder(res.Body).Decode(resp); err != nil {
		return 0, err
	}

	max := DefaultMaxTokens
	for _, v := range resp.Data {
		if v.Service != ChatService {
			continue
		}

		str := v.Metas["max_model_len"]
		cur, err := strconv.Atoi(str)
		if err != nil {
			continue
		}

		if cur > max {
			max = cur
		}
	}

	return max, nil
}

func (c *Client) GetMaxLoraTokens(ctx context.Context) (int, error) {
	req, err := http.NewRequestWithContext(ctx, http.MethodGet,
		(&url.URL{Scheme: "http", Host: c.address, Path: URLSuffixModels}).String(), nil)
	if err != nil {
		return 0, err
	}

	req.Header.Set("Authorization", "Bearer 7178648308129206272")
	res, err := c.cli.Do(req)
	if err != nil {
		return 0, err
	}
	defer res.Body.Close()
	defer c.tr.CloseIdleConnections()

	resp := &ModelsResponse{}
	if err := json.NewDecoder(res.Body).Decode(resp); err != nil {
		return 0, err
	}

	max := DefaultMaxTokens
	for _, v := range resp.Data {
		if v.Service != LoraService {
			continue
		}

		str := v.Metas["max_model_len"]
		cur, err := strconv.Atoi(str)
		if err != nil {
			continue
		}

		if cur > max {
			max = cur
		}
	}

	return max, nil
}
