package dbapp

import (
	"context"
	"errors"
	"fmt"
	"io"
	"net/http"
	"secwalk/pkg/random"
	"time"

	"secwalk/internal/domain/core/callback"
	"secwalk/internal/domain/core/llm"
	"secwalk/internal/domain/core/message"
	"secwalk/internal/domain/core/schema"
	"secwalk/internal/domain/core/schema/ext"

	"github.com/sashabaranov/go-openai"
)

// 聊天对话模型
var _ llm.Model = &ChatLLM{}

type ChatLLM struct {
	maxTokens int
	opt       *Options
}

func NewChatLLM(opts ...Option) (*ChatLLM, error) {
	opt := &Options{}
	for _, o := range opts {
		o(opt)
	}

	if len(opt.address) == 0 {
		return nil, llm.ErrModelAddress
	}

	maxTokens, err := NewClient(opt.address).GetMaxChatTokens(context.Background())
	if err != nil {
		maxTokens = DefaultMaxTokens
	}

	return &ChatLLM{
		maxTokens: maxTokens,
		opt:       opt,
	}, nil
}

func (l *ChatLLM) Call(ctx context.Context, messages []message.Message,
	opts ...schema.CallOption) (*llm.Generation, error) {
	gens, err := l.Generate(ctx, [][]message.Message{messages}, opts...)
	if err != nil {
		return nil, err
	}
	if len(gens) == 0 {
		return nil, llm.ErrModelResponse
	}

	return &gens[0], nil
}

func (l *ChatLLM) Generate(ctx context.Context, messages [][]message.Message,
	opts ...schema.CallOption) (llm.Generations, error) {
	opt := schema.NewCallOptions()
	opt.Model = l.opt.model
	for _, o := range opts {
		o(opt)
	}

	config := openai.DefaultConfig(l.opt.apikey)
	config.BaseURL = fmt.Sprintf("http://%s/v1", l.opt.address)
	config.HTTPHeaderSets = http.Header{
		"X-TID": []string{opt.EXT.GetValue(ext.EXTTID)},
		"EXT":   []string{opt.EXT.ToString()},
	}
	client := openai.NewClientWithConfig(config)

	var thinkType = callback.LLMThinkingTypeReasonAnswer
	gens := make(llm.Generations, 0, len(messages))
	for _, msgs := range messages {

		if opt.FnPromptGuard != nil {
			defer opt.FnPromptGuard.Clear()

			opt.FnPromptGuard.AddTextWithinTag(msgs[0].GetContent())
		}

		if opt.FnStreaming != nil {
			var text string
			var gen = llm.Generation{
				LogProbs: openai.LogProbs{
					Content: make([]openai.LogProb, 0),
				},
			}

			stream, err := client.CreateChatCompletionStream(ctx, openai.ChatCompletionRequest{
				Model:             opt.Model,
				Messages:          toChatMessages(msgs),
				MaxTokens:         opt.MaxTokens,
				Temperature:       opt.Temperature,
				TopK:              opt.TopK,
				TopP:              opt.TopP,
				Stop:              opt.StopWord,
				RepetitionPenalty: opt.RepetitionPenalty,
				GuidedChoice:      opt.GuidedChoice,
				GuidedJson:        opt.GuidedJson,
				GuidedRegex:       opt.GuidedRegex,
				LogProbs:          opt.LogProbs > 0,
				TopLogProbs:       opt.LogProbs,
				Seed:              &opt.Seed,
				Reasoning:         opt.Reasoning,
			})
			if err != nil {
				return nil, err
			}
			defer stream.Close()

			count := 0
			for {
				resp, err := stream.Recv()
				if errors.Is(err, io.EOF) {
					if opt.FnPromptGuard != nil {
						answer, check := opt.FnPromptGuard.Check()
						if len(answer) > 0 {
							for _, a := range answer {
								rand := random.RandomInt(5, 30)
								time.Sleep(time.Duration(rand) * time.Millisecond)
								opt.FnStreaming(ctx, []byte(a))
							}
						}

						if check {
							return nil, llm.ErrModelLeakOfPrompt
						}
					}
					break
				}
				if err != nil {
					return nil, err
				}

				count++
				if count > l.maxTokens {
					return nil, llm.ErrModelResponseTooMuch
				}

				if len(resp.Choices[0].Delta.ReasoningContent) > 0 {
					content := resp.Choices[0].Delta.ReasoningContent
					gen.ReasoningContent += content
					if opt.FnReasoning != nil {
						if thinkType == callback.LLMThinkingTypeReasonAnswer {
							thinkType = callback.LLMThinkingTypeReasonStart
							c := context.WithValue(ctx, callback.LLMThinkingTypeReasonStart, content)
							opt.FnReasoning(c, []byte(content))
							continue
						}
						opt.FnReasoning(ctx, []byte(content))
					}
				} else {
					if thinkType == callback.LLMThinkingTypeReasonStart && len(resp.Choices[0].Delta.Content) > 0 {
						thinkType = callback.LLMThinkingTypeReasonEnd
						c := context.WithValue(ctx, callback.LLMThinkingTypeReasonEnd, "")
						opt.FnReasoning(c, []byte(""))
					}
					if thinkType == callback.LLMThinkingTypeReasonEnd {
						thinkType = callback.LLMThinkingTypeReasonAnswer
					}
					text += resp.Choices[0].Delta.Content
					gen.ID = resp.ID
					gen.Model = resp.Model
					if resp.Usage != nil {
						gen.TokenUsage = *resp.Usage
					}
					gen.FinishReason = resp.Choices[0].FinishReason

					if len(resp.Choices[0].Delta.Content) == 0 {
						continue
					}

					if opt.FnPromptGuard != nil {
						answer, check := opt.FnPromptGuard.Match(resp.Choices[0].Delta.Content)
						if len(answer) > 0 {
							for _, a := range answer {
								rand := random.RandomInt(5, 30)
								time.Sleep(time.Duration(rand) * time.Millisecond)
								opt.FnStreaming(ctx, []byte(a))
							}
						}
						if check {
							return nil, llm.ErrModelLeakOfPrompt
						}
					} else {
						opt.FnStreaming(ctx, []byte(resp.Choices[0].Delta.Content))
					}

					if opt.LogProbs > 0 {
						for _, v := range resp.Choices[0].Logprobs.Content {
							logs := openai.LogProb{
								Token:   v.Token,
								LogProb: v.Logprob,
							}

							for _, vv := range v.Bytes {
								logs.Bytes = append(logs.Bytes, byte(vv))
							}

							for _, vv := range v.TopLogprobs {
								tlp := openai.TopLogProbs{
									Token:   vv.Token,
									LogProb: vv.Logprob,
								}

								for _, vvv := range vv.Bytes {
									tlp.Bytes = append(tlp.Bytes, byte(vvv))
								}
								logs.TopLogProbs = append(logs.TopLogProbs, tlp)
							}
							gen.LogProbs.Content = append(gen.LogProbs.Content, logs)
						}
					}
				}
			}

			if len(text) == 0 {
				return nil, llm.ErrModelResponse
			}

			// 结束标识
			opt.FnStreaming(ctx, []byte(callback.EOFMARK))

			gen.Message = message.NewAssistantChatMessage(text)
			gens = append(gens, gen)
		} else {
			result, err := client.CreateChatCompletion(ctx, openai.ChatCompletionRequest{
				Model:             opt.Model,
				Messages:          toChatMessages(msgs),
				MaxTokens:         opt.MaxTokens,
				Temperature:       opt.Temperature,
				TopK:              opt.TopK,
				TopP:              opt.TopP,
				Stop:              opt.StopWord,
				RepetitionPenalty: opt.RepetitionPenalty,
				GuidedChoice:      opt.GuidedChoice,
				GuidedJson:        opt.GuidedJson,
				GuidedRegex:       opt.GuidedRegex,
				LogProbs:          opt.LogProbs > 0,
				TopLogProbs:       opt.LogProbs,
				Seed:              &opt.Seed,
				Reasoning:         opt.Reasoning,
			})
			if err != nil {
				return nil, err
			}
			if len(result.Choices) == 0 {
				return nil, llm.ErrModelResponse
			}

			if opt.FnPromptGuard != nil {
				_, ok := opt.FnPromptGuard.Match(result.Choices[0].Message.Content)
				if ok {
					return nil, llm.ErrModelLeakOfPrompt
				}
			}

			gen := llm.Generation{
				ID:               result.ID,
				Model:            result.Model,
				Message:          message.NewAssistantChatMessage(result.Choices[0].Message.Content),
				TokenUsage:       result.Usage,
				FinishReason:     result.Choices[0].FinishReason,
				ReasoningContent: result.Choices[0].Message.ReasoningContent,
			}
			if result.Choices[0].LogProbs != nil {
				gen.LogProbs = *result.Choices[0].LogProbs
			}

			gens = append(gens, gen)
		}
	}

	return gens, nil
}

func toChatMessages(messages []message.Message) []openai.ChatCompletionMessage {
	msgs := make([]openai.ChatCompletionMessage, 0)
	for _, m := range messages {
		var role string
		switch m.GetRole() {
		case message.ChatMessageRoleUser:
			role = "user"
		case message.ChatMessageRoleAssistant:
			role = "assistant"
		default:
			role = string(m.GetRole())
		}

		msgs = append(msgs, openai.ChatCompletionMessage{
			Role:    role,
			Content: m.GetContent(),
		})
	}
	return msgs
}
