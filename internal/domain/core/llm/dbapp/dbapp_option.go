package dbapp

type Option func(*Options)

type Options struct {
	// API address
	address string
	// 模型名称
	model  string
	apikey string
}

func WithModel(model string) Option {
	return func(o *Options) {
		o.model = model
	}
}

func WithAddress(address string) Option {
	return func(o *Options) {
		o.address = address
	}
}

func WithAPIKey(apikey string) Option {
	return func(o *Options) {
		o.apikey = apikey
	}
}
