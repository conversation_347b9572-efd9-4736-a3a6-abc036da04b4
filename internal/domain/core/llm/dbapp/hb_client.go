package dbapp

import (
	"crypto/tls"
	"net/http"
	"time"
)

type Client struct {
	address string
	cli     *http.Client
	tr      *http.Transport
}

func NewClient(baseurl string) *Client {
	tr := &http.Transport{
		TLSClientConfig:     &tls.Config{InsecureSkipVerify: true},
		MaxIdleConns:        100,
		MaxIdleConnsPerHost: 50,
		IdleConnTimeout:     90 * time.Second,
	}
	cli := &http.Client{Transport: tr}

	return &Client{
		address: baseurl,
		cli:     cli,
		tr:      tr,
	}
}
