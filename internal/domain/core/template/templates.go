package template

import (
	"errors"
	"fmt"
	"strings"
	"text/template"

	"github.com/Masterminds/sprig/v3"
	"golang.org/x/exp/maps"
)

// ErrInvalidTemplateFormat is the error when the template format is invalid and
// not supported.
var ErrInvalidTemplateFormat = errors.New("invalid template format")

// TemplateFormat is the format of the template.
type TemplateFormat string

const (
	// TemplateFormatGoTemplate is the format for go-template.
	TemplateFormatGoTemplate TemplateFormat = "go-template"
)

// interpolator is the function that interpolates the given template with the given values.
type interpolator func(template string, values map[string]any) (string, error)

// defaultFormatterMapping is the default mapping of TemplateFormat to interpolator.
var defaultformatterMapping = map[TemplateFormat]interpolator{
	TemplateFormatGoTemplate: interpolateGoTemplate,
}

// interpolateGoTemplate interpolates the given template with the given values by using
// text/template.
func interpolateGoTemplate(tmpl string, values map[string]any) (string, error) {
	parsedTmpl, err := template.New("template").
		Option("missingkey=error").
		Funcs(sprig.TxtFuncMap()).
		Parse(tmpl)
	if err != nil {
		return "", err
	}

	builder := new(strings.Builder)
	if err = parsedTmpl.Execute(builder, values); err != nil {
		return "", err
	}
	return builder.String(), nil
}

func newInvalidTemplateError(gotTemplateFormat TemplateFormat) error {
	return fmt.Errorf("%w, got: %s, should be one of %s",
		ErrInvalidTemplateFormat,
		gotTemplateFormat,
		maps.Keys(defaultformatterMapping),
	)
}

// CheckValidTemplate checks if the template is valid through checking whether the given
// TemplateFormat is available and whether the template can be rendered.
func CheckValidTemplate(template string, inputVariables []string) error {
	_, ok := defaultformatterMapping[TemplateFormatGoTemplate]
	if !ok {
		return newInvalidTemplateError(TemplateFormatGoTemplate)
	}

	dummyInputs := make(map[string]any, len(inputVariables))
	for _, v := range inputVariables {
		dummyInputs[v] = "foo"
	}

	_, err := RenderTemplate(template, dummyInputs)
	return err
}

// RenderTemplate renders the template with the given values.
func RenderTemplate(tmpl string, values map[string]any) (string, error) {
	formatter, ok := defaultformatterMapping[TemplateFormatGoTemplate]
	if !ok {
		return "", newInvalidTemplateError(TemplateFormatGoTemplate)
	}
	return formatter(tmpl, values)
}
