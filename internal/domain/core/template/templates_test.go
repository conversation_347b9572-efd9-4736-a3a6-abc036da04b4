package template

import (
	"encoding/json"
	"fmt"
	"os"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestInterpolateGoTemplate(t *testing.T) {
	t.<PERSON>()

	type testCase struct {
		name           string
		template       string
		templateValues map[string]any
		expected       string
		errValue       string
	}

	testCases := []testCase{
		{
			name:           "Single",
			template:       "Hello {{ .key }}",
			templateValues: map[string]any{"key": "world"},
			expected:       "Hello world",
		},
		{
			name:           "Multiple",
			template:       "Hello {{ .key1 }} and {{ .key2 }}",
			templateValues: map[string]any{"key1": "world", "key2": "universe"},
			expected:       "Hello world and universe",
		},
		{
			name:           "Nested",
			template:       "Hello {{ .key1.key2 }}",
			templateValues: map[string]any{"key1": map[string]any{"key2": "world"}},
			expected:       "Hello world",
		},
	}

	for _, tc := range testCases {
		tc := tc
		t.Run(tc.name, func(t *testing.T) {
			t.<PERSON>()

			actual, err := interpolateGoTemplate(tc.template, tc.templateValues)
			require.NoError(t, err)
			assert.Equal(t, tc.expected, actual)
		})
	}

	errTestCases := []testCase{
		{
			name:     "ParseErrored",
			template: "Hello {{{ .key1 }}",
			expected: "",
			errValue: "template: template:1: unexpected \"{\" in command",
		},
		{
			name:     "ExecuteErrored",
			template: "Hello {{ .key1 .key2 }}",
			expected: "",
			errValue: "template: template:1:9: executing \"template\" at <.key1>: key1 is not a method but has arguments",
		},
	}

	for _, tc := range errTestCases {
		tc := tc
		t.Run(tc.name, func(t *testing.T) {
			t.Parallel()

			_, err := interpolateGoTemplate(tc.template, map[string]any{})
			require.Error(t, err)
			require.EqualError(t, err, tc.errValue)
		})
	}
}

func TestCheckValidTemplate(t *testing.T) {
	t.Parallel()

	t.Run("NoTemplateAvailable", func(t *testing.T) {
		t.Parallel()

		err := CheckValidTemplate("Hello, {test}", []string{"test"})
		require.Error(t, err)
		require.ErrorIs(t, err, ErrInvalidTemplateFormat)
		require.EqualError(t, err, "invalid template format, got: unknown, should be one of [go-template]")
	})

	t.Run("TemplateErrored", func(t *testing.T) {
		t.Parallel()

		err := CheckValidTemplate("Hello, {{{ test }}", []string{"test"})
		require.Error(t, err)
		require.EqualError(t, err, "template: template:1: unexpected \"{\" in command")
	})

	t.Run("TemplateValid", func(t *testing.T) {
		t.Parallel()

		err := CheckValidTemplate("Hello, {{ .test }}", []string{"test"})
		require.NoError(t, err)
	})
}

func TestRenderTemplate(t *testing.T) {
	t.Parallel()

	t.Run("TemplateAvailable", func(t *testing.T) {
		t.Parallel()

		actual, err := RenderTemplate(
			"Hello {{ .key }}",
			map[string]any{
				"key": "world",
			},
		)
		require.NoError(t, err)
		assert.Equal(t, "Hello world", actual)
	})

	t.Run("TemplateNotAvailable", func(t *testing.T) {
		t.Parallel()

		_, err := RenderTemplate(
			"Hello {key}",
			map[string]any{
				"key": "world",
			},
		)
		require.Error(t, err)
		require.ErrorIs(t, err, ErrInvalidTemplateFormat)
	})
}

// {{- range .frame.classify -}}
// {{- if eq .curr $.l1 -}}
// {{- range .next -}}
// {{- if eq .curr $.l2 -}}
// {{- range .next -}}
// {{- if .explain}}
// {{$.l1}}-{{$.l2}}-{{.curr}}：{{.explain}}{{.fields}}
// {{- else}}
// {{$.l1}}-{{$.l2}}-{{.curr}}：
// {{- range .next}}如{{.fields}}
// {{- end -}}
// {{- end -}}
// {{- end -}}
// {{- end -}}
// {{- end -}}
// {{- end -}}
// {{- end -}}

func TestRenderMultiMap(t *testing.T) {

	data, err := os.ReadFile("../../testdata/dataclass/input.json")
	if err != nil {
		return
	}

	var inputs map[string]any
	if err := json.Unmarshal(data, &inputs); err != nil {
		return
	}

	inputs["mean"] = ""
	// inputs["l1"] = "业务数据"
	// inputs["l2"] = "交易信息"
	// inputs["l3"] = "通用交易信息"

	res, err := RenderTemplate(
		`
[数据类别及说明（冒号：前面为数据类别）]：
{{- range .frame.classify -}}
{{- if .explain}}
{{.curr}}：{{.explain}}{{.fields}}
{{- else}}
{{.curr}}：主要包括
{{- range .next -}}
{{.curr}}、
{{- end -}}
如
{{- range .next -}}
{{- range .next -}}
{{.curr}}、
{{- range .next -}}
{{.curr}}、
{{- end -}}
{{- end -}}
{{- end -}}
{{- end -}}
{{- end -}}
`,
		inputs,
	)
	require.NoError(t, err)

	fmt.Println(res)
}
