package parser

import (
	"github.com/dlclark/regexp2"
)

var _ Parser = &RegexParser{}

type RegexParser struct {
	keys []string
	exp  *regexp2.Regexp
}

func NewRegexParser(expression string) *RegexParser {
	exp := regexp2.MustCompile(expression, regexp2.RE2)
	return &RegexParser{
		keys: append([]string{ParserRootKey}, exp.GetGroupNames()[1:]...),
		exp:  exp,
	}
}

func (p *RegexParser) Parse(text string) (any, error) {
	matches, err := p.exp.FindStringMatch(text)
	if err != nil {
		return nil, err
	}
	if matches == nil {
		return nil, ErrNoMatchFound
	}

	if len(p.keys) == 1 {
		return matches.String(), nil
	} else {
		results := map[string]any{}

		for i := 0; i < len(p.keys); i++ {
			results[p.keys[i]] = matches.Groups()[i].String()
		}

		return results, nil
	}

}
