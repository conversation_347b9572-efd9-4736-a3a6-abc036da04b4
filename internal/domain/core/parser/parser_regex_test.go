package parser

import (
	"reflect"
	"testing"
)

func TestRegexParser(t *testing.T) {
	t.<PERSON>()

	testCases := []struct {
		input      string
		expression string
		expected   any
	}{
		{
			input:      "2024#0724",
			expression: `(?P<year>\d{4})#(?P<date>\d{4})`,
			expected:   map[string]any{ParserRootKey: "2024#0724", "year": "2024", "date": "0724"},
		},
		{
			input:      "2024#0724",
			expression: `^(.*?)(?=#)`,
			expected:   map[string]any{ParserRootKey: "2024", "1": "2024"},
		},
		{
			input:      "```json\n{\n\t\"url\": \"https://google.com\" \n}\n```",
			expression: `https?://[^\"]+`,
			expected:   "https://google.com",
		},
		{
			input:      `业务数据-交易信息-金额信息`,
			expression: `^[^-]+-(?P<l2>[^-]+)`,
			expected:   map[string]any{ParserRootKey: "业务数据-交易信息", "l2": "交易信息"},
		},
		{
			input:      `业务数据-交易信息-金额信息`,
			expression: `^[^-]+-[^-]+-(?P<l3>[^-]+)`,
			expected:   map[string]any{ParserRootKey: "业务数据-交易信息-金额信息", "l3": "金额信息"},
		},
	}

	for _, tc := range testCases {
		actual, err := NewRegexParser(tc.expression).Parse(tc.input)
		if err != nil {
			t.Errorf("Unexpected error: %v", err)
		}

		if !reflect.DeepEqual(actual, tc.expected) {
			t.Errorf("Expected %v, got %v", tc.expected, actual)
		}
	}
}
