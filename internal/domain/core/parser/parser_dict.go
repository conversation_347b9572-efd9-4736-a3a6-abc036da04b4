package parser

import (
	"fmt"
	"regexp"
)

const (
	regexDictPattern = `(?:%s):\s?(?P<value>(?:[^'\n']*)\.?)`
)

var _ Parser = &RegexDict{}

type RegexDict struct {
	format string
}

func NewRegexDict(expr string) *RegexDict {
	return &RegexDict{format: expr}
}

func (p *RegexDict) Parse(text string) (any, error) {
	expression := regexp.MustCompile(fmt.Sprintf(regexDictPattern, p.format))
	matches := expression.FindStringSubmatch(text)

	if len(matches) < 2 {
		return nil, fmt.Errorf("no match found")
	}

	return matches[1], nil
}
