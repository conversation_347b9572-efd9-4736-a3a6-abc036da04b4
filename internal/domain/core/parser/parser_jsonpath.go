package parser

import (
	"encoding/json"

	"github.com/oliveagle/jsonpath"
)

var _ Parser = &JsonPathParser{}

type JsonPathParser struct {
	exp string
}

func NewJsonpathParser(exp string) *JsonPathParser {
	return &JsonPathParser{exp: exp}
}

func (p *JsonPathParser) Parse(text string) (any, error) {
	var data interface{}
	if err := json.Unmarshal([]byte(text), &data); err != nil {
		return nil, err
	}

	res, err := jsonpath.JsonPathLookup(data, p.exp)
	if err != nil {
		return nil, err
	}

	return res, nil
}
