package parser

import (
	"reflect"
	"testing"
)

func TestJsonPath<PERSON><PERSON><PERSON>(t *testing.T) {
	t.<PERSON>()

	testCases := []struct {
		text       string
		key        string
		expression string
		expected   map[string]any
	}{
		{
			text: `
			{
				"store": {
					"book": [
						{
							"category": "reference",
							"author": "<PERSON>",
							"title": "Sayings of the Century",
							"price": 8.95
						},
						{
							"category": "fiction",
							"author": "<PERSON>",
							"title": "Sword of Honour",
							"price": 12.99
						},
						{
							"category": "fiction",
							"author": "<PERSON>",
							"title": "Moby Dick",
							"isbn": "0-553-21311-3",
							"price": 8.99
						},
						{
							"category": "fiction",
							"author": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>",
							"title": "The Lord of the Rings",
							"isbn": "0-395-19395-8",
							"price": 22.99
						}
					],
					"bicycle": {
						"color": "red",
						"price": 19.95
					}
				},
				"expensive": 10
			}
			`,
			key:        "output",
			expression: "$",
			expected:   map[string]any{"output": float64(10)},
		},
		{
			text: `{
    "status": "1",
    "count": "1",
    "info": "OK",
    "infocode": "10000",
    "lives": [
        {
            "province": "浙江",
            "city": "杭州市",
            "adcode": "330100",
            "weather": "小雨",
            "temperature": "9",
            "winddirection": "东南",
            "windpower": "≤3",
            "humidity": "76",
            "reporttime": "2024-03-18 10:02:18",
            "temperature_float": "9.0",
            "humidity_float": "76.0"
        }
    ]
}`,
			key:        "province",
			expression: "$.lives[0].province",
			expected:   map[string]any{"province": "浙江"},
		},
		{
			text: `{
				"industry": "金融",
				"classify": [
					{
						"curr": "用户数据",
						"next": [
							{
								"curr": "客户个人信息",
								"next": [
									{
										"curr": "个人自然信息",
										"next": [
											{
												"curr": "个人基本概况信息",
												"next": [],
												"fields": "姓名、出生日期、性别、民族、国籍、国籍编码、居住地址、婚姻状况、家庭关系等。",
												"explain": "指个人基本情况数据"
											},
											{
												"curr": "个人身份信息",
												"next": [],
												"fields": "各类证件类型、身份证号、军官证号、护照号、驾驶证编号、工作证、出入证、社保卡、居住证、港澳通行证、台胞证等。",
												"explain": "指能识别个人身份的信息"
											},
											{
												"curr": "个人财产信息",
												"next": [],
												"fields": "个人收入状况、拥有的不动产状况、拥有的车辆状况、纳税额、公积金缴存金额、个人社保与医保存缴金额等。",
												"explain": "指个人的财产数据"
											},
											{
												"curr": "个人联系信息",
												"next": [],
												"fields": "手机、固定电话、邮箱地址、微信号等。",
												"explain": "指个人各类通信联系方式数据"
											},
											{
												"curr": "个人健康生理信息",
												"next": [],
												"fields": "病症、住院志、医嘱单、检验报告、手术及麻醉记录、护理记录、用药记录、药物食物过敏信息、生育信息、以往病史、诊治情况、家族病史、现病史、传染病史等，以及与个人身体健康状况相关信息，如体重、身高、肺活量、吸烟史等。",
												"explain": "指个人生病医治过程中产生的相关记录数据"
											},
											{
												"curr": "个人地理位置信息",
												"next": [],
												"fields": "所在国家、城市、区域、街道等维度下的常在位置和当前位置等。",
												"explain": "指描述个人地理位置的数据"
											},
											{
												"curr": "个人就学信息",
												"next": [],
												"fields": "入学日期、毕业日期、就学的学校名称、就学的学校院系、学历、学位及学科信息等。",
												"explain": "指个人受教育情况的记录数据"
											},
											{
												"curr": "个人职业信息",
												"next": [],
												"fields": "工作单位、工作开始日期、工作结束日期、职位、工作地点、收入情况(含收入值)等。",
												"explain": "指个人就职的历史记录数据"
											},
											{
												"curr": "个人资质证书信息",
												"next": [],
												"fields": "资质证书的编号、颁发机构、生效日期、到期日期等。",
												"explain": "指个人获得资质证书的记录数据"
											},
											{
												"curr": "个人党政信息",
												"next": [],
												"fields": "党派所属、加入党派时间等。",
												"explain": "指个人政治面貌相关数据"
											}
										],
										"fields": "",
										"explain": ""
									},
									{
										"curr": "个人身份鉴别信息",
										"next": [
											{
												"curr": "传统鉴别信息",
												"next": [],
												"fields": "登录系统的密码等。",
												"explain": "指各类常规个人身份鉴别技术手段所依赖的数据"
											},
											{
												"curr": "弱隐私生物特征信息",
												"next": [],
												"fields": "人脸、声纹、步态、耳纹、眼纹、笔迹等。",
												"explain": "指用于身份鉴别的弱隐私个人生物特征样本数据与特征值数据"
											},
											{
												"curr": "强隐私生物特征信息",
												"next": [],
												"fields": "指纹、虹膜等。",
												"explain": "指用于身份鉴别的强隐私个人生物特征样本数据与特征值数据"
											},
											{
												"curr": "鉴别辅助信息",
												"next": [],
												"fields": "动态口令、短信验证码、密码提示问题答案、动态声纹密码等。",
												"explain": "指辅助用于身份鉴别的数据"
											}
										],
										"fields": "",
										"explain": ""
									},
									{
										"curr": "个人资讯信息",
										"next": [
											{
												"curr": "个人信贷信息",
												"next": [],
												"fields": "个人借款信息、个人还款信息、个人欠款信息等个人在信贷过程中产生的数据。",
												"explain": "指个人信贷状况记录数据，信贷历史记录数据"
											},
											{
												"curr": "个人司法信息",
												"next": [],
												"fields": "失信被执行人信息、被执行人信息、开庭公告信息、立案公告信息、犯罪记录、违法违规记录等。",
												"explain": "指与个人相关司法信息记录数据"
											}
										],
										"fields": "",
										"explain": ""
									},
									{
										"curr": "个人关系信息",
										"next": [
											{
												"curr": "个人间关系信息",
												"next": [],
												"fields": "子或女、父母、兄弟姐妹、配偶、社交关系等。",
												"explain": "指用于描述个人与个人关联方关系的记录数据"
											},
											{
												"curr": "公私间关系信息",
												"next": [],
												"fields": "法定代表人、财务负责人、业务经办人、一般雇员、高管人员等。",
												"explain": "指用于描述个人与单位关联方关系的记录数据"
											}
										],
										"fields": "",
										"explain": ""
									},
									{
										"curr": "个人行为信息",
										"next": [
											{
												"curr": "行为信息",
												"next": [],
												"fields": "描述客户购买或使用XX产品或服务时产生的如拜访时间（含登录时间、访问时间）、地点、网页浏览记录、APP浏览记录、个人驾驶习惯等。",
												"explain": "指发生业务关系时的行为数据"
											}
										],
										"fields": "",
										"explain": ""
									},
									{
										"curr": "个人标签信息",
										"next": [
											{
												"curr": "基础标签信息",
												"next": [],
												"fields": "依据教育、职业等构建的个人标签等。",
												"explain": "指基于个人基本属性信息构建的用于刻画个人的标签数据"
											},
											{
												"curr": "关系标签信息",
												"next": [],
												"fields": "基于家庭关系、职业关系、商业关系等构建的个人标签等。",
												"explain": "指基于个人关联属性信息构建的用于刻画个人关系的标签数据"
											},
											{
												"curr": "签约标签信息",
												"next": [],
												"fields": "个人存款标签、个人贷款标签等。",
												"explain": "指基于个人持有的金融业机构产品信息构建的用于刻画个人签约情况的标签数据"
											},
											{
												"curr": "营销服务标签信息",
												"next": [],
												"fields": "营销管理标签（活动权益、白名单等）、服务管理标签（客户敏感度、服务分类等）等。",
												"explain": "指基于个人营销服务属性信息构建的标签数据（从个人营销服务属性中提取加工，并单独构建，用于刻画针对个人的营销服务情况）"
											}
										],
										"fields": "",
										"explain": ""
									}
								],
								"fields": "",
								"explain": ""
							},
							{
								"curr": "客户单位信息",
								"next": [
									{
										"curr": "单位基本信息",
										"next": [
											{
												"curr": "单位基本概况",
												"next": [],
												"fields": "法定代表人姓名、企业名称、统一社会信用代码、经营许可证、经营范围、行业分类、经济类型、人员规模、注册资本、企业地址等。",
												"explain": "指单位基础概况数据"
											},
											{
												"curr": "股东信息",
												"next": [],
												"fields": "控股股东名称、注册资本、实收资本等。",
												"explain": "指主要出资人信息数据"
											},
											{
												"curr": "管理层信息",
												"next": [],
												"fields": "其姓名、证件类型、证件号码、联系方式等。",
												"explain": "指企业经营管理层主要组成人员、实际控制人信息数据"
											},
											{
												"curr": "单位联系信息",
												"next": [],
												"fields": "联系电话、联系人、通信地址等。",
												"explain": "指单位的联系方式信息数据"
											},
											{
												"curr": "单位财务信息",
												"next": [],
												"fields": "营业收入、利润总额、资产状况、负债状况等。",
												"explain": "指单位财务信息数据"
											}
										],
										"fields": "",
										"explain": ""
									},
									{
										"curr": "单位身份鉴别信息",
										"next": [
											{
												"curr": "传统鉴别信息",
												"next": [],
												"fields": "登录重要系统的密码等。",
												"explain": "指用于验证单位是否具有访问或使用权限的数据"
											},
											{
												"curr": "鉴别辅助信息",
												"next": [],
												"fields": "动态口令、短信验证码、密码提示问题答案、动态声纹密码等。",
												"explain": "指辅助用于身份鉴别的数据"
											}
										],
										"fields": "",
										"explain": ""
									},
									{
										"curr": "单位资讯信息",
										"next": [
											{
												"curr": "企业信贷信息",
												"next": [],
												"fields": "企业借款信息、企业还款信息、企业欠款信息等企业在信贷过程中产生的数据。",
												"explain": "指信贷状况记录信息"
											},
											{
												"curr": "企业司法信息",
												"next": [],
												"fields": "失信被执行人信息、被执行人信息、开庭公告信息、立案公告信息等。",
												"explain": "指企业的司法相关数据"
											},
											{
												"curr": "企业税务信息",
												"next": [],
												"fields": "纳税情况、企业纳税人信用评级信息、企业增值税缴纳额同比减少20%以上信息等。",
												"explain": "指企业税务数据"
											},
											{
												"curr": "企业工商信息",
												"next": [],
												"fields": "企业证照信息、注册日期、企业类型、股东及出资人信息、主要管理人员信息、企业对外投资等。",
												"explain": "指企业在工商局录入的可查询数据"
											}
										],
										"fields": "",
										"explain": ""
									},
									{
										"curr": "单位关系信息",
										"next": [
											{
												"curr": "单位间关系信息",
												"next": [],
												"fields": "XX关系、家族企业、互持股情况等关系。",
												"explain": "指描述单位与单位关联方关系的数据"
											},
											{
												"curr": "公私间关系信息",
												"next": [],
												"fields": "法定代表、财务负责、业务经办、一般雇员、高管等。",
												"explain": "指描述单位与个人关联方之间关系的数据"
											}
										],
										"fields": "",
										"explain": ""
									},
									{
										"curr": "单位行为信息",
										"next": [
											{
												"curr": "行为信息",
												"next": [],
												"fields": "通过邮件、短信、社交网络、辅助渠道等咨询、购买或使用XX产品或服务时产生的拜访时间、地点、网页浏览习惯、APP浏览习惯等行为记录。",
												"explain": "指发生业务关系时的行为数据"
											}
										],
										"fields": "",
										"explain": ""
									},
									{
										"curr": "单位标签信息",
										"next": [
											{
												"curr": "基础标签信息",
												"next": [],
												"fields": "金融客户、能源客户标签等。",
												"explain": "指基于单位自有属性构建的标签数据"
											},
											{
												"curr": "关系标签信息",
												"next": [],
												"fields": "供应链核心客户标签等。",
												"explain": "指基于单位关联关系构建的标签数据"
											},
											{
												"curr": "签约标签信息",
												"next": [],
												"fields": "网银签约标签等。",
												"explain": "指基于单位的使用产品及投资偏好构建的标签数据"
											},
											{
												"curr": "营销服务标签信息",
												"next": [],
												"fields": "战略客户标签等。",
												"explain": "指基于营销策略和服务对单位构建的分类标签数据"
											}
										],
										"fields": "",
										"explain": ""
									}
								],
								"fields": "",
								"explain": ""
							}
						],
						"fields": "",
						"explain": ""
					},
					{
						"curr": "业务数据",
						"next": [
							{
								"curr": "交易信息",
								"next": [
									{
										"curr": "通用交易信息",
										"next": [
											{
												"curr": "交易基本信息",
												"next": [],
												"fields": "手工调账单、凭证相关信息、交易编号、交易日期、交易类型、交易渠道、交易来源、交易地点、生效日期、交易币别、汇率等。",
												"explain": "指交易基本数据"
											},
											{
												"curr": "交易金额信息",
												"next": [],
												"fields": "交易金额、收款金额、付款金额、开票金额实收金额等。",
												"explain": "指描述交易中实际发生的金额信息"
											},
											{
												"curr": "交易对象信息",
												"next": [],
												"fields": "类型、名称、开户行及账号、现金账号、银行卡号等。",
												"explain": "指交易活动中交易对象的相关数据"
											},
											{
												"curr": "交易清结算信息",
												"next": [],
												"fields": "发起行、接收行、支付指令、清分明细数据（商户手续费、渠道成本、代理商分润等）等。",
												"explain": "指金融业机构为客户办理货币支付、资金划拨及其资金清算业务的数据"
											},
											{
												"curr": "交易记账信息",
												"next": [],
												"fields": "记账日期、记账时间等。",
												"explain": "指会计主体因交易发生的经济活动而进行事后核算记账的数据"
											}
										],
										"fields": "",
										"explain": ""
									},
									{
										"curr": "保险收付费信息",
										"next": [
											{
												"curr": "保险收费信息",
												"next": [],
												"fields": "缴费项目、缴费账户、金额、缴费渠道、缴费日期等。",
												"explain": "指因承保或保全批改，客户需缴纳的保险费等各种费用数据"
											},
											{
												"curr": "保险赔偿和给付信息",
												"next": [],
												"fields": "给付项目、金额、赔付日期、保险金领取人等。",
												"explain": "指因保全批改或理赔，客户获得的赔偿或给付等费用数据"
											}
										],
										"fields": "",
										"explain": ""
									}
								],
								"fields": "",
								"explain": ""
							},
							{
								"curr": "账户信息",
								"next": [
									{
										"curr": "账户信息",
										"next": [
											{
												"curr": "基本信息",
												"next": [],
												"fields": "账户编号、账户类型、保证金账户标志、账户状态、开户机构编号、开户日期、销户日期、支付标记（作为支付账号等原始交易要素的替代值，用于完成特定场景支付交易）等。",
												"explain": "指账户的基本信息数据"
											},
											{
												"curr": "金额信息",
												"next": [],
												"fields": "金额、余额、币种等。",
												"explain": "指账户上的金额信息数据"
											},
											{
												"curr": "介质信息",
												"next": [],
												"fields": "介质号码、卡种类等。",
												"explain": "指账户上依附的介质相关信息数据"
											},
											{
												"curr": "冻结信息",
												"next": [],
												"fields": "冻结类型、冻结日期、冻结金额、状态等相关属性信息。",
												"explain": "指账户发生冻结时记录的相关信息数据"
											},
											{
												"curr": "特有账户信息",
												"next": [],
												"fields": "国库单一账户、清算账户信息等。",
												"explain": "指政府机构或商业银行在中国人民银行所开立的账户信息"
											}
										],
										"fields": "",
										"explain": ""
									}
								],
								"fields": "",
								"explain": ""
							},
							{
								"curr": "合约协议",
								"next": [
									{
										"curr": "合同通用信息",
										"next": [
											{
												"curr": "合同基本信息",
												"next": [],
												"fields": "合同编号、合同名称、合同种类、合同状态、生效日期、到期日期、终止日期、期限相关属性信息、合同抬头、合同项目、合同项目定价表、合同商业数据、合同凭证、合同类型表等。",
												"explain": "指合同法所规定的、各种业务通用的基本属性数据"
											},
											{
												"curr": "合同金额信息",
												"next": [],
												"fields": "金额、税额、单价、定价表等。",
												"explain": "指合同法所规定的、各种业务通用的合同金额相关数据"
											}
										],
										"fields": "",
										"explain": ""
									},
									{
										"curr": "商户签约信息",
										"next": [
											{
												"curr": "签约信息",
												"next": [],
												"fields": "商户名称、商户类型、业务场景、签约时间、签约服务提供商、合同商业数据等。",
												"explain": "指与商户签约的业务、产品相关数据"
											}
										],
										"fields": "",
										"explain": ""
									},
									{
										"curr": "中间业务信息",
										"next": [
											{
												"curr": "基本信息",
												"next": [],
												"fields": "中间业务类型、手续费、费率等。",
												"explain": "指中间业务的基本属性数据信息"
											},
											{
												"curr": "交易类中间业务信息",
												"next": [],
												"fields": "远期外汇合约、金融期货、商品期货、互换和期权等信息。",
												"explain": "指金融业机构为满足客户保值或自身风险管理等方面的需要，利用各种金融工具进行的资金交易活动的数据信息"
											},
											{
												"curr": "支付结算类中间业务信息",
												"next": [],
												"fields": "票据业务、托收业务、信用证业务、结售汇业务等信息",
												"explain": "指由金融业机构为客户办理因债权债务关系引起的与货币支付、资金划拨有关的收费业务数据信息，中间业务的支付结算类业务信息"
											},
											{
												"curr": "担保承诺类中间业务信息",
												"next": [],
												"fields": "承兑汇票、备用信用证、各类保函业务等信息，以及金融资产管理公司为其成员单位提供的维好协议等信息。",
												"explain": "指商业银行为客户债务清偿能力提供担保，承担客户违约风险业务的数据信息"
											}
										],
										"fields": "",
										"explain": ""
									},
									{
										"curr": "金融资产管理公司业务信息",
										"next": [
											{
												"curr": "债权转让合同信息",
												"next": [],
												"fields": "债权转让协议的基本信息、日期信息（如基准日、交割日、付款日）、交割价、资产出让方信息、资产买受方信息、资产交割清单（包含权证信息等）、瑕疵披露、过渡期安排、免责条款等，以及转让标的物合同的基本信息、日期信息、利率信息、债务人信息、展期信息等。",
												"explain": "指金融资产管理公司在不良资产经营管理过程中，签订的收购、处置等协议的信息和转让标的物的合同信息"
											},
											{
												"curr": "资产信息",
												"next": [],
												"fields": "基本信息、计息信息、逾期信息、回款计划信息、物权信息、抵债信息、股权信息、诉讼主体变更裁定等资产信息，以及标的债权信息（指的是收购原债权本金、利息、银行代垫费用等）。",
												"explain": "指金融资产管理公司经营的不良资产信息"
											}
										],
										"fields": "",
										"explain": ""
									},
									{
										"curr": "金融资产投资公司业务信息",
										"next": [
											{
												"curr": "债权转让合同信息",
												"next": [],
												"fields": "债权转让协议的基本信息、日期信息（如基准日、交割日、付款日）、交割价、资产出让方信息、资产买受方信息、资产交割清单（包含权证信息等）、瑕疵披露、过渡期安排、免责条款等，以及转让标的物合同的基本信息、日期信息、利率信息、债务人信息、展期信息等。",
												"explain": "指金融资产投资公司在不良资产经营管理过程中，签订的收购、处置等协议的信息和转让标的物的合同信息"
											},
											{
												"curr": "资产信息",
												"next": [],
												"fields": "基本信息、计息信息、逾期信息、回款计划信息、物权信息、抵债信息、股权信息、诉讼主体变更裁定等资产信息，以及标的债权信息（指的是收购原债权本金、利息、银行代垫费用等）。",
												"explain": "指金融资产投资公司经营的不良资产信息"
											}
										],
										"fields": "",
										"explain": ""
									},
									{
										"curr": "保险业务信息",
										"next": [
											{
												"curr": "保单基本信息",
												"next": [],
												"fields": "如投保单号、保单号、保险人、投保人、被保险人、受益人、签单日期、生效日期、保险费及支付方法、特别约定、健康告知、违约责任及争议处理等。",
												"explain": "指保险业务的保单基础信息"
											},
											{
												"curr": "保单责任信息",
												"next": [],
												"fields": "保险责任名称、保险期间、保险金额、责任保费和费率、保险金赔偿及给付方法等。",
												"explain": "指保险合同中约定的保险人向被保险人提供保险保障的范围数据"
											},
											{
												"curr": "财产险标的信息",
												"next": [],
												"fields": "车架号/Vin 码、车辆牌照号、车牌种类、车辆种类、厂牌车型、发动机号、工程信息、地块代码、地块面积、林地面积等。",
												"explain": "指具有经济价值的有形实物保险标的是投保人请求保险人承担风险、给予经济保障的保险利益的承担者。财产险标的（如车辆、房屋、工程、地块、农作物、林木等）的属性数据"
											},
											{
												"curr": "责任险标的信息",
												"next": [],
												"fields": "雇主责任、产品责任、公众责任等，具体信息包括投保企业的信息、作业方式、作业环境、以往事故信息、风险等级、危险品信息、消防设施、安全管理水平等。",
												"explain": "指属于被保险人对第三者在法律上应负的经济赔偿责任的责任险的标的数据"
											},
											{
												"curr": "信用保证险标的信息",
												"next": [],
												"fields": "工程履约信息、供货保证信息、产品质量保证信息、贷款保证信息、雇员忠诚保证信息、出口信用信息等。",
												"explain": "指履约的义务方不履行义务将使权利方遭受经济损失的数据"
											},
											{
												"curr": "特殊风险标的信息",
												"next": [],
												"fields": "相关项目或工程的地址信息、设备信息、驾驶员信息等。",
												"explain": "指主要涉及航空、航天、核电站和石油开发等的数据"
											},
											{
												"curr": "核保信息",
												"next": [],
												"fields": "风险查勘信息、核保结论、核保意见、加费、折扣、减额等。",
												"explain": "指保险核保过程及结果的相关数据"
											},
											{
												"curr": "保全批改信息",
												"next": [],
												"fields": "保全批改项目、变更原因、变更内容、变更影响、生效时间等。",
												"explain": "指客户为维持保险合同效力所进行保全（变更）的相关数据"
											},
											{
												"curr": "销售渠道费用信息",
												"next": [],
												"fields": "代理人佣金、代理机构手续费、经纪人/机构经纪费等。",
												"explain": "指保险销售过程中需要向中介机构、代理人支付的费用数据"
											},
											{
												"curr": "保单借款信息",
												"next": [],
												"fields": "贷款时间、额度、利率、还款计划等。",
												"explain": "指客户以现金价值担保，向保险人进行借款相关数据"
											},
											{
												"curr": "理赔案件信息",
												"next": [],
												"fields": "如报案人、出险人、报案日期、出险时间、出险地点、出险描述、事故结果、索赔金额、索赔材料等。",
												"explain": "指保险业务中理赔案件的基础信息"
											},
											{
												"curr": "理赔调查信息",
												"next": [],
												"fields": "文件核查、实地调查、网络调查、第三方合作调查、其他途径调查等的调查报告、调查结论（例如财产损失情况、人员伤亡情况、出险原因、事故责任、事故第三方等）。",
												"explain": "指理赔调查相关数据"
											},
											{
												"curr": "赔付结果信息",
												"next": [],
												"fields": "理赔结论、理赔时效、核赔金额、追偿费用、追偿赔款、拒赔原因、黑名单、诉讼信息等。",
												"explain": "指赔付结果相关书"
											}
										],
										"fields": "",
										"explain": ""
									},
									{
										"curr": "再保险业务信息",
										"next": [
											{
												"curr": "再保险合同信息",
												"next": [],
												"fields": "分入分出机构信息，合同期限、再保分保方式（合约、临分等）、再保险方式（比例、非比例等信息）、再保业务种类（人身险、财产险等）、保费及其计算方式、保额及其计算方式、赔款及其计算方式、财务结算信息、除外责任信息等。",
												"explain": "指再保险合同的相关数据"
											},
											{
												"curr": "再保险业务明细信息",
												"next": [],
												"fields": "分入分出机构信息，合同期限、再保分保方式（合约、临分等）、再保险方式（比例、非比例等信息）、再保业务种类（人身险、财产险等）、保费及其计算方式、保额及其计算方式、赔款及其计算方式、财务结算信息、除外责任信息等。",
												"explain": "指再保险合同的相关数据"
											},
											{
												"curr": "再保险赔案信息",
												"next": [],
												"fields": "再保合同名称及业务年度、保险标的出险信息、保额及分出比例、估计赔款及合同项下估计摊赔金额信息、赔款发生日期地点信息、赔款实际摊回信息等。",
												"explain": "指再保险赔案的相关数据"
											},
											{
												"curr": "再保险账单信息",
												"next": [],
												"fields": "分出机构信息、给付人信息、结算项目信息、合约/临分信息、给付金额信息等。",
												"explain": "指再保险业务的账单数据"
											}
										],
										"fields": "",
										"explain": ""
									},
									{
										"curr": "资金业务信息",
										"next": [
											{
												"curr": "基本信息",
												"next": [],
												"fields": "资金交易类型、金融资产三分类、资金来源、交割信息等数据。",
												"explain": "指资金交易的基本信息数据"
											},
											{
												"curr": "同业资金往来信息",
												"next": [],
												"fields": "同业拆放、同业代付等业务数据信息。",
												"explain": "指以金融同业客户为服务与合作对象，且以同业资金融通为核心的各项金融同业业务数据"
											},
											{
												"curr": "自营资金投资信息",
												"next": [],
												"fields": "自营债券交易、自营汇率交易、自营利率交易、自营衍生品交易等业务数据信息。",
												"explain": "指自营资金投资是指金融业机构利用自有资金进行投资的业务的相关数据"
											},
											{
												"curr": "托管业务信息",
												"next": [],
												"fields": "委托资产托管、信托资产托管等业务数据信息。",
												"explain": "指具备一定资格的金融业机构作为托管人，依据有关法律法规，与委托人签订委托资产托管合同，安全保管委托投资的资产，履行托管人相关职责的托管业务数据"
											}
										],
										"fields": "",
										"explain": ""
									},
									{
										"curr": "非银行支付业务信息",
										"next": [
											{
												"curr": "基本信息",
												"next": [],
												"fields": "提供资金代收代付所产生的资金交易订单信息等。",
												"explain": "指非银行支付业务的基本信息数据"
											}
										],
										"fields": "",
										"explain": ""
									},
									{
										"curr": "信托业务信息",
										"next": [
											{
												"curr": "信托产品信息",
												"next": [],
												"fields": "信托产品基本信息（信托产品代码、信托产品品牌、产品成立日期、预计到期日期）、信托产品账户及余额信息（账户类别、账户余额）、信托投资顾问合同信息（投资顾问合同编号、投资顾问名称、签订日期）、信托产品受益所有人信息（信托产品代码、受益权类型）等。",
												"explain": "指信托公司已成立信托产品相关的要素信息"
											},
											{
												"curr": "信托募集信息",
												"next": [],
												"fields": "信托合同信息（信托合同编号、委托人编号、委托资金金额）、信托受益凭据信息（受益人编号、实际持有信托金额、实际持有信托份额）、信托产品收益权信息（受益权类型、受益权起始日及到期日）、信托受益权转让信息（分配频度、分配方式、分红方式）等。",
												"explain": "指信托公司项目资金募集的相关要素信息"
											},
											{
												"curr": "信托运用信息",
												"next": [],
												"fields": "财产运用信息（运用合同编号、交易对手编号、合同签订金额）、信托担保合同信息（担保合同编号、担保总金额）、信托回款情况信息（运用合同编号、经营性现金流、预计还款来源）、信托产品的存续信息（信托产品存续标识、当前产品单位净值、风险项目标识）、信托产品清算信息（产品实际收益、损失金额、赔付金额）等。",
												"explain": "指信托公司信托财产（包括资金和财产权）运用相关的要素信息"
											},
											{
												"curr": "固有业务信息",
												"next": [],
												"fields": "固有资金运用合同信息（运用合同编号、投向行业及明细、合同签订金额、提前终止标识）、固有担保合同信息（担保合同编号、担保合同类型、担保人名称、担保总金额）等。",
												"explain": "指信托公司固有业务管理的相关要素信息"
											}
										],
										"fields": "",
										"explain": ""
									}
								],
								"fields": "",
								"explain": ""
							},
							{
								"curr": "金融监管和服务",
								"next": [
									{
										"curr": "货币金银业务信息",
										"next": [
											{
												"curr": "钞票处理信息",
												"next": [],
												"fields": "清分完整券捆数、复点残损券捆数等。",
												"explain": "指货币金银业务中货币清分、复点、销毁等信息"
											},
											{
												"curr": "发行基金管理信息",
												"next": [],
												"fields": "发行基金库存金额、发行基金类型等。",
												"explain": "指货币金银业务中基金发行管理信息"
											},
											{
												"curr": "货币管理信息",
												"next": [],
												"fields": "人民币质量检测完成量、境外代保管库人民币库存余额等。",
												"explain": "指货币金银业务中货币质量检测、货币出入境统计等信息"
											},
											{
												"curr": "金银实物管理信息",
												"next": [],
												"fields": "清查入库金银实物成色、调拨金银实物纯重等。",
												"explain": "指货币金银业务中金银实物清查、调拨等信息"
											}
										],
										"fields": "",
										"explain": ""
									},
									{
										"curr": "支付结算业务信息",
										"next": [
											{
												"curr": "表外业务信息",
												"next": [],
												"fields": "表外业务账务日期等。",
												"explain": "指支付结算业务中表外业务信息"
											},
											{
												"curr": "公开市场业务信息",
												"next": [],
												"fields": "质押融资业务融资本金、公开市场债券/中央银行票据面额等。",
												"explain": "指支付结算业务中公开市场业务信息"
											},
											{
												"curr": "开销户信息",
												"next": [],
												"fields": "开户申请处理时间等。",
												"explain": "指支付结算业务中金融业机构在中国人民银行开立、注销账户信息"
											},
											{
												"curr": "普通转账信息",
												"next": [],
												"fields": "转账业务确认日期、转账金额等。",
												"explain": "指支付结算业务中金融业机构转账业务信息"
											},
											{
												"curr": "现金及发行基金信息",
												"next": [],
												"fields": "支取现金金额、支取现金指标号码等。",
												"explain": "指支付结算业务中现金及发行基金业务信息"
											}
										],
										"fields": "",
										"explain": ""
									},
									{
										"curr": "金融消费权益保护业务信息",
										"next": [
											{
												"curr": "咨询投诉管理信信息",
												"next": [],
												"fields": "咨询工单编号、咨询处理结果等。",
												"explain": "指金融消费者进行咨询或投诉产生的业务信息"
											},
											{
												"curr": "案例管理信息",
												"next": [],
												"fields": "案例编号、案例案情简介等。",
												"explain": "指系统内录入的以往关于金融消费者保护的案例信息"
											},
											{
												"curr": "消费者教育信息",
												"next": [],
												"fields": "消费者教育知识标题、消费者教育知识关键字等。",
												"explain": "指金融知识普及和宣传相关业务信息"
											},
											{
												"curr": "电子公告管理信息",
												"next": [],
												"fields": "电子公告标题、电子公告发布时间等。",
												"explain": "指发布金融消费者权益保护相关电子公告产生的业务信息"
											},
											{
												"curr": "监督检查信息",
												"next": [],
												"fields": "现场检查内容、现场检查对象等。",
												"explain": "指对机构进行金融消费者权益保护评估产生的业务信息，包括机构自评及中国人民银行评估"
											}
										],
										"fields": "",
										"explain": ""
									}
								],
								"fields": "",
								"explain": ""
							}
						],
						"fields": "",
						"explain": ""
					},
					{
						"curr": "经营管理数据",
						"next": [
							{
								"curr": "营销服务",
								"next": [
									{
										"curr": "产品信息",
										"next": [
											{
												"curr": "新产品（项目）研发信息",
												"next": [],
												"fields": "调查研究报告、开发新产品的构思或创意、新产品或品种设计信息、新产品或品种研发报告及数据、新产品测试评估报告及数据，保险产品的定价和产品利润假设、费率表和准备金表等精算数据，以及金融资产管理公司拟收购、拟处置不良资产的项目方案及其审核审批信息等。",
												"explain": "指金融业机构研究选择适应市场需要的产品，从产品设计到投入正常生产的一系列决策过程所产生的数据"
											},
											{
												"curr": "基本信息",
												"next": [],
												"fields": "产品编号、产品名称、适用客户类型等。",
												"explain": "指用于产品管理的基础描述数据"
											},
											{
												"curr": "分类信息",
												"next": [],
												"fields": "产品分类代码、产品分类名称、产品分类层级等。",
												"explain": "指金融业机构根据各部门及业务条线的管理需求，依据产品分类标准，采用特定产品分类业务视角，对金融业机构产品进行分类的相关数据"
											},
											{
												"curr": "特征信息",
												"next": [],
												"fields": "期限特征、保险保障特征、金额及缴费方式特征、利率特征、保险条款等。",
												"explain": "指金融产品特征的集合"
											},
											{
												"curr": "管理信息",
												"next": [],
												"fields": "金融业机构向监管机构进行本机构产品报备（报批）的相关文件，以及金融资产管理公司拟收购不良资产的立项及其审核审批信息、投后管理信息等。",
												"explain": "指产品日常管理所需数据"
											}
										],
										"fields": "",
										"explain": ""
									}
								],
								"fields": "",
								"explain": ""
							},
							{
								"curr": "综合管理",
								"next": [
									{
										"curr": "财务信息",
										"next": [
											{
												"curr": "财务会计信息",
												"next": [],
												"fields": "金融资产、其他流动资产、长期投资、固定资产、无形资产、递延资产、流动负债、非流动负债、所有者权益的确认及计量等。",
												"explain": "指XX根据会计准则进行的会计确认、计量和报告过程中产生的相关数据，包括会计记录、账簿、报表、摊销数据、冲销数据、核销数据、冲回数据、财务应收数据、财务应付数据等"
											},
											{
												"curr": "企业财务管理信息",
												"next": [],
												"fields": "预算核批金额、预算执行率、财务管理范围、财务凭证等。",
												"explain": "指企事业单位预算执行、周转金拨付和年终并账等财务管理信息"
											},
											{
												"curr": "基建财务管理信息",
												"next": [],
												"fields": "基建项目代码、基建项目关张日期等。",
												"explain": "指基本建设项目管理和项目核算等信息"
											},
											{
												"curr": "管理会计信息",
												"next": [],
												"fields": "预算、考核、成本分摊、盈亏分析、资产分析（拆分、合并、清理、盘盈、盘亏等）、科目、利润及成本中心等方面的报表、报告等。",
												"explain": "指XX运用管理会计工具方法，根据财务和业务数据进行加工整理形成的，满足企业价值管理和决策支持需要的各类数据"
											},
											{
												"curr": "财务支出信息",
												"next": [],
												"fields": "采购相关信息、费用报销相关信息（项目名称、报销目的地、出行方式等）、借款单相关信息、发票相关信息等。",
												"explain": "指XX开展财务开支及招标采购过程中产生的各类数据，包括供应商管理数据"
											},
											{
												"curr": "税务信息",
												"next": [],
												"fields": "无",
												"explain": "指XX的税款形成、申报、缴纳以及发票管理等过程中产生的各类数据。"
											},
											{
												"curr": "内部资金往来信息",
												"next": [],
												"fields": "无",
												"explain": "指XX内部资金划转与借用的信息。"
											},
											{
												"curr": "支付清算汇总信息",
												"next": [],
												"fields": "商户结算数据（如结算周期、结算方式等）、结算单数据（如汇总轧差金额、结算周期等）等。",
												"explain": "指XX汇总的各类支付清算和结算数据"
											},
											{
												"curr": "资金渠道流通汇总信息",
												"next": [],
												"fields": "渠道资金流水总额、渠道对账明细、渠道对账结果等。",
												"explain": "指XX汇总的资金渠道流通相关数据"
											}
										],
										"fields": "",
										"explain": ""
									},
									{
										"curr": "战略规划信息",
										"next": [
											{
												"curr": "市场营销规划信息",
												"next": [],
												"fields": "无",
												"explain": "指金融业机构市场营销活动及其有关的各项业务管理文档等数据。"
											},
											{
												"curr": "资金规划信息",
												"next": [],
												"fields": "无",
												"explain": "指金融业机构资金统筹管理活动有关文档等数据。"
											},
											{
												"curr": "人力需求规划信息",
												"next": [],
												"fields": "无",
												"explain": "指金融业机构人力资源管理活动有关文档等数据。"
											},
											{
												"curr": "品牌战略规划信息",
												"next": [],
												"fields": "无",
												"explain": "指金融业机构品牌管理活动有关文档等数据。"
											},
											{
												"curr": "业务发展规划信息",
												"next": [],
												"fields": "一定时期内对业务发展方向、发展速度与质量、发展点及业务发展能力的重大选择和策略等。",
												"explain": "指金融业机构对于未来业务发展规划过程中记录的数据"
											}
										],
										"fields": "",
										"explain": ""
									},
									{
										"curr": "招聘信息",
										"next": [
											{
												"curr": "人员招聘报名信息",
												"next": [],
												"fields": "招聘职位、招聘人数等。",
												"explain": "指报名XX对外发布岗位产生的信息"
											},
											{
												"curr": "人员招聘考试信息",
												"next": [],
												"fields": "招聘考场编号、考点名称等。",
												"explain": "指考生参与XX招聘岗位相关考试产生的信息"
											}
										],
										"fields": "",
										"explain": ""
									},
									{
										"curr": "员工信息",
										"next": [
											{
												"curr": "内部公开员工信息",
												"next": [],
												"fields": "员工姓名、办公电话、员工号、邮箱信息、员工个人账号、用户名等。",
												"explain": "指XX记录的可内部公开的员工信息数据"
											},
											{
												"curr": "受限访问员工信息",
												"next": [],
												"fields": "手机信息、身份证信息、社保、个税情况、社会关系、奖惩情况、工作成果（如研究成果）、职级等。",
												"explain": "指XX记录的员工信息数据中不宜向他人（一定范围以外的人）广泛公开或知悉的数据"
											},
											{
												"curr": "档案管理信息",
												"next": [],
												"fields": "人事档案、履历、自传、鉴定（考评）、政治历史等。",
												"explain": "指XX记录的员工档案信息数据"
											},
											{
												"curr": "岗位角色信息",
												"next": [],
												"fields": "工作岗位名称、员工职称名称、员工职务名称、员工所属团队名称、角色信息（如政治面貌、涉密岗位、特定工作组成员等）等。",
												"explain": "指XX记录的员工工作岗位、职务等数据"
											},
											{
												"curr": "培训与资质信息",
												"next": [],
												"fields": "参加培训时间、培训地点、培训内容等和获得资质证书时间、资质证书名称、资质证书有效期等。",
												"explain": "指XX记录的员工培训情况记录数据和拥有的资质证书情况数据"
											},
											{
												"curr": "技能信息",
												"next": [],
												"fields": "销售技能、服务沟通技能、保险核保、查勘定损技能等。",
												"explain": "指XX记录的员工拥有的技能特长数据"
											},
											{
												"curr": "业绩信息",
												"next": [],
												"fields": "销售型员工的销售业绩数据（如销售指标达成情况、销售的产品情况等），服务型员工的服务业绩数据（如投诉解决率、客户满意度等),技术型员工的产出业绩数据（如代码缺陷率、公共组件贡献情况等）等。",
												"explain": "指XX记录的员工业绩考核指标及业绩完成情况等数据"
											},
											{
												"curr": "薪资信息",
												"next": [],
												"fields": "工资、津贴、奖金、福利、定薪标准及薪资等级等。",
												"explain": "指XX记录的员工薪资待遇数据"
											}
										],
										"fields": "",
										"explain": ""
									},
									{
										"curr": "机构信息",
										"next": [
											{
												"curr": "基本信息（外部公开）",
												"next": [],
												"fields": "机构编号、机构名称、机构地址、机构电话等。",
												"explain": "指XX组织架构中，其内设部门、分支机构等已公开的基础概况记录数据"
											},
											{
												"curr": "基本信息（内部公开）",
												"next": [],
												"fields": "部门编号、部门名称、部门职责等。",
												"explain": "指XX组织架构中，其内设部门、分支机构等未直接公开的基础概况记录数据"
											},
											{
												"curr": "层级信息",
												"next": [],
												"fields": "总公司、分公司、子公司等。",
												"explain": "指XX组织架构中，其内设部门、分支机构等的层级划分标识数据"
											},
											{
												"curr": "分类信息",
												"next": [],
												"fields": "无",
												"explain": "指XX组织架构中，其内设部门、分支机构等按不同管理要求维度进行分类的标识数据。"
											},
											{
												"curr": "证件信息",
												"next": [],
												"fields": "统一社会信用代码（五证合一后）、金融业机构代码证号、支付业务许可证号等。",
												"explain": "指XX各内设部门、分支机构等的证件信息"
											}
										],
										"fields": "",
										"explain": ""
									},
									{
										"curr": "行政信息",
										"next": [
											{
												"curr": "党务纪检信息",
												"next": [],
												"fields": "党的组织建设情况、党风廉政建设情况、纪律监督、违纪问题调查处理、受理党员和群众的来信来访等。",
												"explain": "指XX开展党建、纪检等工作中记录的数据"
											},
											{
												"curr": "工会信息",
												"next": [],
												"fields": "会员信息、会费管理数据、各类活动记录等。",
												"explain": "指XX开展工会工作中记录的数据"
											},
											{
												"curr": "章程制度信息",
												"next": [],
												"fields": "公司章程、公司财务管理制度、公司资产管理制度、IT治理制度、IT信息技术管理制度等。",
												"explain": "指XX为维护正常的工作秩序，依照法律、法令、政策而制订的具有法规性或指导性与约束力的章程制度电子文档数据"
											},
											{
												"curr": "信息披露信息",
												"next": [],
												"fields": "新闻、通知、公告等。",
												"explain": "指XX发布的各类公开信息数据"
											},
											{
												"curr": "公文信息",
												"next": [],
												"fields": "文号、紧急程度、发文机关标志、发文字号、签发人、标题、主送机关、正文、附件说明、发文机关署名、成文日期、印章、附注、附件、抄送机关、印发机关、印发日期等。",
												"explain": "指XX日常工作中，接收或发送的按照特定体式、经过一定的处理程序形成和使用的电子文档数据"
											},
											{
												"curr": "邮件信息",
												"next": [],
												"fields": "邮件发送时间、邮件接收时间、邮件主题、邮件内容、邮件附件和图片等。",
												"explain": "指XX员工经邮件传递方式处理的各类文件信息数据"
											},
											{
												"curr": "流程信息",
												"next": [],
												"fields": "财务流程、会议流程的流程名、流程类型、审批人、发起人、审批意见、发起时间、审批时间、附件路径、附件名、流程所属公司等。",
												"explain": "指XX内为实现业务的某一特定目的所采取的一系列有控制的步骤、活动与方法的集合"
											}
										],
										"fields": "",
										"explain": ""
									},
									{
										"curr": "音影像信息",
										"next": [
											{
												"curr": "客户及监管相关音影像信息",
												"next": [],
												"fields": "证件扫描件、业务合同扫描件、签名影像、声纹录音等。",
												"explain": "指XX在各项活动时所采集的及其处理后（如压缩、分段传输）的音影像资料，或监管资料所转化的影像资料数据"
											},
											{
												"curr": "日常管理相关音影像信息",
												"next": [],
												"fields": "会议、安防、团建活动、党建活动等过程中采集的视频、录音、照片，以及会计档案等。",
												"explain": "指XX在日常管理过程中产生的音影像资料"
											}
										],
										"fields": "",
										"explain": ""
									},
									{
										"curr": "内控合规信息",
										"next": [
											{
												"curr": "合规信息",
												"next": [],
												"fields": "信息隔离、数据保密等。",
												"explain": "指XX参照国家法律法规明文规定和要求，产生的合规结果数据"
											},
											{
												"curr": "内部审计信息",
												"next": [],
												"fields": "中长期审计规划、年度审计计划、审计方案、审计证据、审计项目编码、现场工作日期、工作底稿、审计报告（如审计目标和范围、审计依据、审计发现、审计结论、审计建议等）等。",
												"explain": "指XX在业务开展或业务管理过程中对是否符合监管、合规和内控相关要求规定，而产生的审计评估结果数据"
											},
											{
												"curr": "法务信息",
												"next": [],
												"fields": "合同签署、商务谈判、法律纠纷及诉讼事件处理情况等。",
												"explain": "指涉及XX有关法律事务的数据"
											}
										],
										"fields": "",
										"explain": ""
									}
								],
								"fields": "",
								"explain": ""
							},
							{
								"curr": "运营管理",
								"next": [
									{
										"curr": "业务运维信息",
										"next": [
											{
												"curr": "参数/指标运维信息",
												"next": [],
												"fields": "对产品和服务的汇率、费率、优惠活动、卡面（如借记卡、信用卡等）信息等运营参数或指标进行运维的过程中产生的数据等。",
												"explain": "指产品、业务或服务过程中产生的相关参数或指标数据"
											}
										],
										"fields": "",
										"explain": ""
									},
									{
										"curr": "客户服务信息",
										"next": [
											{
												"curr": "电话服务信息",
												"next": [],
												"fields": "客户服务内容、电话录音、服务人员信息、服务记录、服务评价等。",
												"explain": "指电话服务交互双方和服务过程数据"
											},
											{
												"curr": "网络服务信息",
												"next": [],
												"fields": "设备类型（pc/移动设备）、客户端类型（浏览器/APP）、IP 地址、服务内容、沟通文字或多媒体记录、服务人员信息、服务评价等。",
												"explain": "指网络服务双方和服务过程数据"
											}
										],
										"fields": "",
										"explain": ""
									},
									{
										"curr": "项目管理信息",
										"next": [
											{
												"curr": "项目基本信息",
												"next": [],
												"fields": "，项目名称、项目编号、项目来源",
												"explain": "指项目管理中对项目进行描述和标识的关键信息，明确项目的目标、范围、时间、成本、资源和相关方等方面的基本情况，以便有效地规划、执行和监控项目"
											},
											{
												"curr": "项目敏感信息",
												"next": [],
												"fields": "项目规划、项目开发、项目金额等信息。",
												"explain": "项目中包含的具有机密性、隐私性或商业敏感性的信息"
											}
										],
										"fields": "",
										"explain": ""
									},
									{
										"curr": "合作单位信息",
										"next": [
											{
												"curr": "合作单位基本信息",
												"next": [],
												"fields": "单位名称、单位类型等。",
												"explain": "指合作单位的基本情况数据"
											},
											{
												"curr": "合作单位联系人信息",
												"next": [],
												"fields": "姓名、电话、邮箱等。",
												"explain": "指合作单位联系人基本情况数据"
											},
											{
												"curr": "合作内容信息",
												"next": [],
												"fields": "合约协议文档、评价考核结果等。",
												"explain": "指与合作单位确定的合作内容"
											},
											{
												"curr": "合作单位特有信息",
												"next": [],
												"fields": "与保险业务相关的汽车修理厂的修车设备、技术人员等级等，公估公司的经营范围、公估许可证信息等，鉴定机构的鉴定人员、鉴定资质信息等，医院的医院等级、床位数量等。",
												"explain": "指与特定金融业务相关，不属于合作单位基本信息的特有信息数据"
											}
										],
										"fields": "",
										"explain": ""
									}
								],
								"fields": "",
								"explain": ""
							},
							{
								"curr": "风险管理信息",
								"next": [
									{
										"curr": "风险管控信息",
										"next": [
											{
												"curr": "风险缓释信息",
												"next": [],
												"fields": "抵押信息、保证信息、金融衍生品信息等。",
												"explain": "指金融业机构采取如抵押、保证、金融衍生品等风险缓释工具，或者采取保险、融资等手段所实施的风险转移技术等的相关数据"
											},
											{
												"curr": "风险识别信息",
												"next": [],
												"fields": "贷前风险调查、贷款分类、信用报告和金融资产管理公司的立项、尽职调查、方案信息等。",
												"explain": "指认识所面临各种风险及分析风险事故发生的潜在原因过程中所使用的统计方法及工具、分析模型及其产生的相关数据"
											},
											{
												"curr": "风险计量信息",
												"next": [],
												"fields": "客户的单笔债项或汇总的风险计量相关数据、风险加权资产信息、风险评级或评分等。",
												"explain": "指在风险识别的基础上，对风险发生的可能性、风险将导致的后果及严重程度进行充分的分析和评估，从而确定风险水平过程的相关数据，包括模型及统计指标等"
											},
											{
												"curr": "风险监测信息",
												"next": [],
												"fields": "信用卡预警信息、PD 预警信息、异常事件信息、信用风险客户监控信息、监测报告等。",
												"explain": "指动态、连续监测各种可量化的关键风险指标以及不可量化的风险因素的变化和发展趋势的相关数据"
											},
											{
												"curr": "风险控制信息",
												"next": [],
												"fields": "额度冻结信息、户口冻结信息、限额信息等。",
												"explain": "指在对经过识别和计量的风险采取分散、对冲、转移、规避和补偿等措施，进行有效管理和控制的相关数据"
											},
											{
												"curr": "黑名单信息",
												"next": [],
												"fields": "外部购买和内部产生的黑名单信息等。",
												"explain": "指有现实或潜在风险的客户信息数据"
											}
										],
										"fields": "",
										"explain": ""
									}
								],
								"fields": "",
								"explain": ""
							}
						],
						"fields": "",
						"explain": ""
					},
					{
						"curr": "系统运行和安全",
						"next": [
							{
								"curr": "网络和信息系统运维及安全管理数据",
								"next": [
									{
										"curr": "系统运行管理信息",
										"next": [
											{
												"curr": "开发测试数据",
												"next": [],
												"fields": "开发代码、过程管理、验收、测试用例、测试方案、测试计划、测试结果、测试方法、测试环境等数据。",
												"explain": "指XX信息系统开发和测试过程中的数据"
											},
											{
												"curr": "配置信息",
												"next": [],
												"fields": "配置参数、版本号、接口信息（如接口名称、接口数据类型、接口地址、请求方式等）、模板信息、信息系统基础信息（如系统名称、系统url、系统标识等）、内网IP地址",
												"explain": "指与信息系统配置相关的数据"
											},
											{
												"curr": "系统运行信息",
												"next": [],
												"fields": "id信息（如主/外键、数据库自增id、rownum、rowid等）、序号、编号信息、状态信息、类型信息（枚举类）、标识信息、时间信息（如创建/更新时间等）、备注信息、数据来源、后台任务信息（如任务名称、任务类型、任务编号、任务执行时间、任务执行状态等）",
												"explain": "指XX业务信息系统在运行过程中产生的数据"
											},
											{
												"curr": "日志信息",
												"next": [],
												"fields": "操作日志、告警日志、运行日志。",
												"explain": "指各个系统、应用、网络、机房设施、监控平台中记录的日志数据"
											},
											{
												"curr": "元数据信息",
												"next": [],
												"fields": "数据字典信息、数据库信息（如数据库名、数据库类型、数据库结构信息）、数据表信息（如表名、表注释、表结构信息）、字段信息（如字段名、字段注释、字段类型）等。",
												"explain": "指描述其他数据的数据，对数据的结构、内容、源头、关系等进行描述和定义"
											}
										],
										"fields": "",
										"explain": ""
									},
									{
										"curr": "系统安全管理信息",
										"next": [
											{
												"curr": "安全管理信息",
												"next": [],
												"fields": "无",
												"explain": "指信息系统漏洞信息、安全防护配置与策略信息、自行识别的威胁数据、安全告警信息、安全事件信息等数据。"
											},
											{
												"curr": "身份标识信息",
												"next": [],
												"fields": "用户账号、用户 ID、即时通信账号、网络社交用户账号、用户头像、昵称、个性签名等",
												"explain": "指直接标识网络或通信用户身份的信息及账户角色相关资料信息（金融账户除外）"
											},
											{
												"curr": "身份鉴别信息",
												"next": [],
												"fields": "登录密码、生物鉴别信息等",
												"explain": "指集团信息系统用于验证用户身份和确保其合法性的关键信息"
											}
										],
										"fields": "",
										"explain": ""
									}
								],
								"fields": "",
								"explain": ""
							}
						],
						"fields": "",
						"explain": ""
					},
					{
						"curr": "监管数据",
						"next": [
							{
								"curr": "数据报送",
								"next": [
									{
										"curr": "监管报送信息",
										"next": [
											{
												"curr": "监管指标上报信息",
												"next": [],
												"fields": "风险资产总额、资本净额的数量和结构、核心资本充足率、资本充足率、金融资产管理公司（法人）、传统不良资产收购处置业务统计指标、金融资产管理公司（法人）附重组条件不良资产收购业务统计指标等",
												"explain": "指XX按要求上报的监管指标数据"
											},
											{
												"curr": "监管明细数据上报信息",
												"next": [],
												"fields": "反洗钱数据（包括但不限于大额和可疑交易报告、调查协查、客户风险等级等信息）、监管标准化数据、清单级业务数据、科目级财务数据、“三会一层”信息、法人信息 、机构信息、存贷款明细",
												"explain": "指金融业机构按要求上报的各类监管明细数据"
											},
											{
												"curr": "金融统计信息",
												"next": [],
												"fields": "财务报表、调查问卷及其他重要事项",
												"explain": "指XX计算的宏观统计相关数据"
											}
										],
										"fields": "",
										"explain": ""
									}
								],
								"fields": "",
								"explain": ""
							},
							{
								"curr": "数据收取",
								"next": [
									{
										"curr": "评价、处罚与违规信息",
										"next": [
											{
												"curr": "评价、处罚与违规信息",
												"next": [],
												"fields": "罚单、通报",
												"explain": "监管机构下发的罚单、通报等"
											}
										],
										"fields": "",
										"explain": ""
									},
									{
										"curr": "监管统计及预警信息",
										"next": [
											{
												"curr": "统计分析信息",
												"next": [],
												"fields": "无",
												"explain": "指由监管机构对相关数据进行汇总、统计、分析、比对、组合而形成的数据，系统重要性金融业机构评价指标分项得分。"
											},
											{
												"curr": "预警信息",
												"next": [],
												"fields": "无",
												"explain": "指由监管机构根据特定预警策略设置，产生的预警数据，监管机构下发的风险预警信息。"
											}
										],
										"fields": "",
										"explain": ""
									},
									{
										"curr": "外部审计信息",
										"next": [],
										"fields": "无",
										"explain": "指独立于机关和企事业单位的国家审计机构针对XX集团在业务开展或业务管理的过程中是否符合监管、合规和内控相关规定要求进行审计产生的评估数据。"
									}
								],
								"fields": "",
								"explain": ""
							}
						],
						"fields": "",
						"explain": ""
					}
				]
			}`,
			key:        "list",
			expression: `$.classify[?(@.curr == '业务数据')].next[?(@.curr == '交易信息')].next[?(@.curr == '通用交易信息')].next[*].curr`,
		},
	}

	for _, tc := range testCases {
		actual, err := NewJsonpathParser(tc.expression).Parse(tc.text)
		if err != nil {
			t.Errorf("Unexpected error: %v", err)
		}

		if !reflect.DeepEqual(actual, tc.expected) {
			t.Errorf("Expected %v, got %v", tc.expected, actual)
		}
	}
}
