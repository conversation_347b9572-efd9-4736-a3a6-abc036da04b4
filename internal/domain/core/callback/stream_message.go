package callback

import (
	"secwalk/pkg/random"
	"strings"
	"time"

	"github.com/sashabaranov/go-openai"
)

const EOFMARK = "SECMARK_STREAM_EOF"

const (
	ModeVerbose  = "verbose"
	ModePreview  = "preview"
	ModeQuestion = "question"
)

// 调试信息
type FnStreamVerbose func(*VerboseMessage) error

type VerboseMessage struct {
	Mode               string `json:"mode,omitempty"`                 // 消息类型
	Type               string `json:"type,omitempty"`                 // 步骤类型
	Name               string `json:"name,omitempty"`                 // 步骤名称
	NodeID             string `json:"node_id,omitempty"`              // 节点ID
	PID                string `json:"pid,omitempty"`                  // 父调用ID
	CID                string `json:"cid,omitempty"`                  // 本调用ID
	Stage              int    `json:"stage"`                          // 0=开始; 1=完成
	State              int    `json:"state"`                          // 状态
	Error              string `json:"error,omitempty"`                // 错误信息
	Timestamp          int64  `json:"timestamp,omitempty"`            // 时间戳
	SystemPrompt       string `json:"system_prompt,omitempty"`        // 系统提示词
	SystemPromptRender string `json:"system_prompt_render,omitempty"` // 渲染后的系统提示词
	UserPrompt         string `json:"user_prompt,omitempty"`          // 用户提示词
	UserPromptRender   string `json:"user_prompt_render,omitempty"`   // 渲染后的用户提示词
	FinishReason       string `json:"finish_reason,omitempty"`        // 模型推理结束原因
	Inputs             any    `json:"inputs,omitempty"`               // 输入信息
	Output             any    `json:"output,omitempty"`               // 输出信息
}

const (
	TypeAgent        = "agent"
	TypeNode         = "node"
	TypeLLM          = "llm"
	TypeTool         = "tool"
	TypeRetrievalDoc = "retrieval_doc"
	TypeRetrievalSql = "retrieval_sql"
)

const (
	StageRun = 0
	StageEnd = 1
)

const (
	CallStateSuccess = 0
	CallStateFailure = 1
)

// 预览信息
type FnStreamPreview func(*PreviewMessage) error

const (
	TypeInline = "inline" // 内置
	TypeCustom = "custom" // 自定义
)

const (
	FromBasicLLM             = "basic_llm"     // 基础推理
	FromReactThink           = "react_think"   // 思考规划
	FromReactAction          = "react_action"  // 行动
	FromReactObserve         = "react_observe" // 观察
	FromReactAnswer          = "react_answer"  // 结果
	FromRetrievalDocAction   = "retrieval_doc_action"
	FromRetrievalDocContext  = "retrieval_doc_context"
	FromRetrievalDocAnswer   = "retrieval_doc_answer"
	FromRetrievalSqlGenerate = "retrieval_sql_generate"
	FromRetrievalSqlContenxt = "retrieval_sql_context"
	FromRetrievalSqlAnswer   = "retrieval_sql_answer"
	FromRestfulAnswer        = "restful_answer"
	FromExecuteResult        = "execute_result"
)

type PreviewMessage struct {
	Mode      string         `json:"mode,omitempty"`       // 消息模式(交互或调试消息)
	Type      string         `json:"type,omitempty"`       // 消息类型(内置或用户定义)
	NodeID    string         `json:"node_id,omitempty"`    // 节点ID
	From      string         `json:"from,omitempty"`       // 消息源头
	Name      string         `json:"name,omitempty"`       // 智能体名
	Timestamp int64          `json:"timestamp,omitempty"`  // 时间戳
	MessageID string         `json:"message_id,omitempty"` // 消息ID
	Content   string         `json:"content,omitempty"`    // 消息内容
	Results   map[string]any `json:"results,omitempty"`    // 参数数据
}

func PreviewFackStream(fn FnStreamPreview, msg *PreviewMessage, stream ...bool) {
	content := msg.Content
	results := msg.Results
	msg.Results = nil

	if (len(stream) == 1 && stream[0]) || len(content) > 5000 {
		fn(msg)
	} else {
		word := ""
		full := ""
		for index := range content {
			rand := random.RandomInt(5, 30)
			word += strings.TrimPrefix(content[:index], full)
			full = content[:index]

			if (rand%2 == 1 || len(word) > 4) && len(word) > 0 {
				msg.Content = word
				word = ""
				fn(msg)
				time.Sleep(time.Duration(rand) * time.Millisecond)
			}
		}

		if len(word) > 0 {
			msg.Content = word
			fn(msg)
		}

		word = strings.TrimPrefix(content, full)
		if len(word) > 0 {
			msg.Content = word
			fn(msg)
		}
	}

	if len(results) > 0 {
		msg.Content = ""
		msg.Results = results
		fn(msg)
	}

	msg.Content = EOFMARK
	fn(msg)
}

// Token统计
type FnStreamTokenUsage func(*openai.Usage)
