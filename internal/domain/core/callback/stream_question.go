package callback

import (
	"context"
	"errors"
	"secwalk/pkg/random"
	"sync"
	"time"
)

var ErrQuestionID = errors.New("question id not found")

var (
	qaMux  = sync.RWMutex{}
	qaChan = map[string]chan map[string]any{}
)

// 用户QA消息
type QuestionMessage struct {
	Mode      string         `json:"mode,omitempty"`
	Type      string         `json:"type,omitempty"`
	Name      string         `json:"name,omitempty"`
	NodeID    string         `json:"node_id,omitempty"`
	Timestamp int64          `json:"timestamp,omitempty"`
	MessageID string         `json:"message_id,omitempty"`
	Message   string         `json:"message,omitempty"`
	Options   map[string]any `json:"options,omitempty"`
}

type FnQuestion func(*QuestionMessage) error

func SendQuestionMessage(ctx context.Context, fn FnQuestion, msg *QuestionMessage) (map[string]any, error) {
	msg.Mode = ModeQuestion
	msg.Timestamp = time.Now().UnixMilli()
	msg.MessageID = random.UniqueID()

	// 根据消息ID存储接收管道
	qaMux.Lock()
	ch := make(chan map[string]any)
	qaChan[msg.MessageID] = ch
	qaMux.Unlock()

	// 发送问题
	fn(msg)

	// 移除管道
	defer func() {
		qaMux.Lock()
		delete(qaChan, msg.MessageID)
		qaMux.Unlock()
	}()

	select {
	case res := <-ch:
		return res, nil
	case <-ctx.Done():
		return nil, ctx.Err()
	}
}

func RecvQuestionResponse(id string, inputs map[string]any) error {
	qaMux.RLock()
	defer qaMux.RUnlock()

	ch, ok := qaChan[id]
	if !ok {
		return ErrQuestionID
	}

	ch <- inputs
	return nil
}
