package message

import (
	"encoding/json"
	"fmt"
	"secwalk/pkg/util"
	"strings"
)

// 聊天消息的角色声明
type Role string

const (
	ChatMessageRoleSystem    Role = "system"
	ChatMessageRoleUser      Role = "user"
	ChatMessageRoleAssistant Role = "assistant"
)

// 消息抽象接口定义
type Message struct {
	Role    Role   `json:"role"`
	Content string `json:"content"`
}

// 获取角色名称
func (m *Message) GetRole() Role {
	return m.Role
}

// 获取对话内容
func (m *Message) GetContent() string {
	return m.Content
}

func NewSystemChatMessage(content string) Message {
	return Message{
		Role:    ChatMessageRoleSystem,
		Content: content,
	}
}

func NewUserChatMessage(content string) Message {
	return Message{
		Role:    ChatMessageRoleUser,
		Content: content,
	}
}

func NewAssistantChatMessage(content string) Message {
	return Message{
		Role:    ChatMessageRoleAssistant,
		Content: content,
	}
}

func ToMessages(history any) []Message {
	var str string

	hstr, ok := history.(string)
	if ok {
		str = hstr
	} else {
		str = util.ToJsonString(history)
	}

	msgs := make([]Message, 0)
	if err := json.Unmarshal([]byte(str), &msgs); err != nil {
		return nil
	}
	return msgs
}

// 消息格式化前缀选项
type PrefixOption func(*PrefixOptions)

type PrefixOptions struct {
	SystemPrefix    string
	UserPrefix      string
	AssistantPrefix string
}

func WithSystemPrefix(prefix string) PrefixOption {
	return func(o *PrefixOptions) {
		o.SystemPrefix = prefix
	}
}

func WithUserPrefix(prefix string) PrefixOption {
	return func(o *PrefixOptions) {
		o.UserPrefix = prefix
	}
}

func WithAssistantPrefix(prefix string) PrefixOption {
	return func(o *PrefixOptions) {
		o.AssistantPrefix = prefix
	}
}

// 格式化消息列表为字符串,可以自定义角色前缀
func GetBufferString(messages []Message, opts ...PrefixOption) string {
	opt := PrefixOptions{
		SystemPrefix:    string(ChatMessageRoleSystem),
		UserPrefix:      string(ChatMessageRoleUser),
		AssistantPrefix: string(ChatMessageRoleAssistant),
	}
	for _, o := range opts {
		o(&opt)
	}

	msgs := make([]string, 0)
	for _, m := range messages {
		var role string

		switch m.GetRole() {
		case ChatMessageRoleSystem:
			role = opt.SystemPrefix
		case ChatMessageRoleUser:
			role = opt.UserPrefix
		case ChatMessageRoleAssistant:
			role = opt.AssistantPrefix
		default:
			return fmt.Sprintf("%v", messages)
		}

		msgs = append(msgs, fmt.Sprintf("%s: %s", role, m.GetContent()))
	}

	return strings.Join(msgs, "\n")
}
