package message

import "context"

// ChatMessageHistory is the interface for chat history in memory/store.
type ChatMessageHistory interface {
	// AddMessage adds a message to the store.
	AddMessage(ctx context.Context, message Message) error

	// AddUserMessage is a convenience method for adding a human message string
	// to the store.
	AddUserMessage(ctx context.Context, message string) error

	// AddAssistantMessage is a convenience method for adding an AI message string to
	// the store.
	AddAssistantMessage(ctx context.Context, message string) error

	// Clear removes all messages from the store.
	Clear(ctx context.Context) error

	// Messages retrieves all messages from the store
	Messages(ctx context.Context) ([]Message, error)

	// SetMessages replaces existing messages in the store
	SetMessages(ctx context.Context, messages []Message) error
}
