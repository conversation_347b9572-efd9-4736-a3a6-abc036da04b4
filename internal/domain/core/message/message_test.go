package message

import (
	"testing"
)

func TestGetBufferString(t *testing.T) {
	t.<PERSON>()
	testCases := []struct {
		name        string
		messages    []Message
		humanPrefix string
		aiPrefix    string
		expected    string
		expectError bool
	}{
		{
			name:        "No messages",
			messages:    []Message{},
			humanPrefix: "Human",
			aiPrefix:    "AI",
			expected:    "",
			expectError: false,
		},
		{
			name: "Mixed messages",
			messages: []Message{
				NewUserChatMessage("Hello, how are you?"),
				NewAssistantChatMessage("I'm doing great!"),
				NewSystemChatMessage("Please be polite."),
			},
			humanPrefix: "Human",
			aiPrefix:    "AI",
			expected:    "Human: Hello, how are you?\nAI: I'm doing great!\nSystem: Please be polite.\nModerator: Keep the conversation on topic.",
			expectError: false,
		},
	}

	for _, tc := range testCases {
		tc := tc
		t.Run(tc.name, func(t *testing.T) {
			t.<PERSON>l()
			result := GetBufferString(tc.messages,
				WithUserPrefix(tc.humanPrefix), WithAssistantPrefix(tc.aiPrefix))

			if result != tc.expected {
				t.Errorf("expected: %q, got: %q", tc.expected, result)
			}
		})
	}
}
