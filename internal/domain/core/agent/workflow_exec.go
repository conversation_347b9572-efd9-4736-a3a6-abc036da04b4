package agent

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"secwalk/internal/domain/core/callback"
	"secwalk/internal/domain/core/parser"
	"secwalk/internal/domain/core/schema"
	"secwalk/internal/domain/core/schema/ext"
	"secwalk/pkg/logger"
	"secwalk/pkg/random"

	"github.com/sirupsen/logrus"
)

// 执行工作流
func (a *Agent) Run(ctx context.Context, entry, input string, paralle bool) (string, error) {
	ctx, cancel := context.WithCancel(ctx)
	defer cancel()

	var out string = input
	var err error

LOOP:
	node, err := findNode(a.config.Nodes, entry)
	if err != nil {
		return "", err
	}

	if a.masterEntry && // 入口智能体
		!paralle && // 非并行
		!node.Iteration && // 非迭代
		a.config.OutputExpression == nil && // 无输出表达式
		schema.IsNodeTypeLLM(node.Type) && // 是模型节点
		len(findConnectEdges(a.config.Edges, entry)) == 0 { // 是最终节点
		a.outputFinal = true
	} else {
		a.outputFinal = false
	}

	nodeOut, err := a.runNode(ctx, node, out)
	if err != nil {
		logrus.WithField(logger.KeyCategory, logger.CategoryCore).
			WithField(logger.KeyEXT, a.ext).
			Errorf("[%s] node run error: %s", node.ID, err)
		return out, err
	}
	if node.Type != schema.NodeTypeActLogic && node.Type != schema.NodeTypeActInteraction {
		out = nodeOut
	}

	// 并行边
	var parErr error
	mx := &sync.Mutex{}
	wg := &sync.WaitGroup{}
	for _, edge := range findParalleEdges(a.config.Edges, node.ID) {
		match, err := a.variables.ExprMatch(edge.Condition)
		if err != nil {
			logrus.WithField(logger.KeyCategory, logger.CategoryCore).
				WithField(logger.KeyEXT, a.ext).
				Warnf("[%s] next node edge expr match error: %s", node.ID, err)
		}
		if !match {
			logrus.WithField(logger.KeyCategory, logger.CategoryCore).
				WithField(logger.KeyEXT, a.ext).
				Infof("[%s] next node edge expr not match: %s", node.ID, edge.Condition)
			continue
		}

		wg.Add(1)
		go func(entry string) {
			defer wg.Done()
			logrus.WithField(logger.KeyCategory, logger.CategoryCore).
				WithField(logger.KeyEXT, a.ext).
				Infof("[%s] paralle start", entry)

			if _, err := a.Run(ctx, entry, out, true); err != nil {
				cancel()
				mx.Lock()
				if parErr == nil {
					parErr = err
				}
				mx.Unlock()
			}
			logrus.WithField(logger.KeyCategory, logger.CategoryCore).
				WithField(logger.KeyEXT, a.ext).
				Infof("[%s] paralle final", entry)
		}(edge.DstNode)
	}
	wg.Wait()

	if parErr != nil {
		return "", parErr
	}

	// 非并行边
	for _, edge := range findConnectEdges(a.config.Edges, node.ID) {
		match, err := a.variables.ExprMatch(edge.Condition)
		if err != nil {
			logrus.WithField(logger.KeyCategory, logger.CategoryCore).
				WithField(logger.KeyEXT, a.ext).
				Warnf("[%s] next node edge expr match error: %s", node.ID, err)
		}
		if !match {
			continue
		}

		entry = edge.DstNode
		goto LOOP
	}

	if node.Iteration {
		if v := a.variables.getVariable(node.ID); v != nil {
			if vv, ok := v.(map[string]any); ok {
				if result, ok := vv[node.ResultKey].([]any); ok {
					var nop string
					for _, key := range result {
						if s, ok := key.(string); ok {
							nop += fmt.Sprintf("%s\n", s)
							continue
						}
					}
					out = nop
				}
			}
		}
	}

	return out, nil
}

// 执行节点
func (a *Agent) runNode(ctx context.Context, node *schema.Node, input string) (string, error) {
	var output string
	iters := 0
	retries := 0
	logrus.WithField(logger.KeyCategory, logger.CategoryCore).
		WithField(logger.KeyEXT, a.ext).
		Infof("[%s] node running, type: %s, node_name: %s", node.ID, node.Type, node.Name)

	// 初始化节点局部变量
	a.variables.setLocalPrune(node.ID)

	// 校验最大执行次数
	if node.MaxWorking <= 0 {
		return "", ErrNodeWorksLimit
	}
	defer func() { node.MaxWorking -= 1 }()

START:
	var cid = random.UniqueID()
	var err error
	var res map[string]any
	var locals, inputs map[string]any

	// 按照节点超时时间设置
	callctx, cancel := context.WithTimeout(ctx, time.Second*time.Duration(node.Timeout))
	defer cancel()

	// 获取节点输入
	locals, inputs, err = a.getNodeInputs(node, input, iters)
	if err != nil {
		// 迭代参数为空时，仅发送一个开始与结束调试信息，用于页面展示
		if err == ErrNodeIterateValueEmpty {
			if a.fnStreamVerbose != nil {
				var systemPrompt, userPrompt string
				if len(node.SystemPromptTemplate) > 0 {
					systemPrompt = node.SystemPromptTemplate
				} else {
					systemPrompt = a.config.SystemPromptTemplate
				}
				if len(node.UserPromptTemplate) > 0 {
					userPrompt = node.UserPromptTemplate
				} else {
					userPrompt = a.config.UserPromptTemplate
				}

				a.fnStreamVerbose(&callback.VerboseMessage{
					Type:         callback.TypeNode,
					Name:         node.Name,
					NodeID:       node.ID,
					PID:          a.cid,
					CID:          cid,
					Stage:        callback.StageRun,
					State:        callback.CallStateSuccess,
					Timestamp:    time.Now().UnixMilli(),
					SystemPrompt: systemPrompt,
					UserPrompt:   userPrompt,
				})

				a.fnStreamVerbose(&callback.VerboseMessage{
					Type:      callback.TypeNode,
					Name:      node.Name,
					NodeID:    node.ID,
					PID:       a.cid,
					CID:       cid,
					Stage:     callback.StageEnd,
					State:     callback.CallStateSuccess,
					Timestamp: time.Now().UnixMilli(),
				})
			}
			return "", nil
		}

		// 迭代次数大于迭代参数长度时，迭代结束，正常返回
		if err == ErrNodeIterateLimit {
			logrus.WithField(logger.KeyCategory, logger.CategoryCore).
				WithField(logger.KeyEXT, a.ext).
				Infof("[%s] iter done", node.ID)

			return output, nil
		}

		// 其他参数获取失败直接转到异常结束
		goto END_NONE_DEBUG
	}

	// 输出调试信息
	if a.fnStreamVerbose != nil {
		var systemPrompt, userPrompt string
		if len(node.SystemPromptTemplate) > 0 {
			systemPrompt = node.SystemPromptTemplate
		} else {
			systemPrompt = a.config.SystemPromptTemplate
		}
		if len(node.UserPromptTemplate) > 0 {
			userPrompt = node.UserPromptTemplate
		} else {
			userPrompt = a.config.UserPromptTemplate
		}

		a.fnStreamVerbose(&callback.VerboseMessage{
			Type:         callback.TypeNode,
			Name:         node.Name,
			NodeID:       node.ID,
			PID:          a.cid,
			CID:          cid,
			Stage:        callback.StageRun,
			State:        callback.CallStateSuccess,
			Timestamp:    time.Now().UnixMilli(),
			SystemPrompt: systemPrompt,
			UserPrompt:   userPrompt,
			Inputs:       locals,
		})
	}

	// 调用模型链
	if schema.IsNodeTypeLLM(node.Type) {
		output, err = a.runNodeLLM(callctx, node, locals, inputs, cid)
		if err != nil {
			goto END
		}
	}

	// 调用行动链
	if schema.IsNodeTypeAct(node.Type) {
		output, err = a.runNodeAct(callctx, node, locals, inputs, cid)
		if err != nil {
			goto END
		}
	}

	// 解析执行结果
	res, err = a.setNodeOutputs(ctx, node, output)
	if err != nil {
		goto END
	}

	// 节点休眠
	if node.Pending > 0 {
		time.Sleep(time.Second * time.Duration(node.Pending))
	}

END:
	if a.fnStreamVerbose != nil {
		if err != nil {
			a.fnStreamVerbose(&callback.VerboseMessage{
				Type:      callback.TypeNode,
				Name:      node.Name,
				NodeID:    node.ID,
				PID:       a.cid,
				CID:       cid,
				Stage:     callback.StageEnd,
				State:     callback.CallStateFailure,
				Error:     err.Error(),
				Timestamp: time.Now().UnixMilli(),
			})
		} else {
			vv, ok := a.variables.getVariable(node.ID).(map[string]any)
			if ok {
				res = vv
			}

			a.fnStreamVerbose(&callback.VerboseMessage{
				Type:      callback.TypeNode,
				Name:      node.Name,
				NodeID:    node.ID,
				PID:       a.cid,
				CID:       cid,
				Stage:     callback.StageEnd,
				State:     callback.CallStateSuccess,
				Timestamp: time.Now().UnixMilli(),
				Output:    res,
			})
		}
	}

END_NONE_DEBUG:
	if err != nil && node.OnError != schema.OnErrorBypass {
		// 错误返回
		if node.OnError == schema.OnErrorReturn {
			return "", err
		}

		// 错误重试
		if node.OnError == schema.OnErrorRetry {
			retries++
			if retries <= node.MaxRetries {
				logrus.WithField(logger.KeyCategory, logger.CategoryCore).
					WithField(logger.KeyEXT, a.ext).
					Infof("[%s] retry node times: %d", node.ID, retries)
				goto START
			} else {
				logrus.WithField(logger.KeyCategory, logger.CategoryCore).
					WithField(logger.KeyEXT, a.ext).
					Infof("[%s] retry node limit: %d", node.ID, node.MaxRetries)
				return "", fmt.Errorf("%w: %s", ErrNodeRetryLimit, err)
			}
		}
	}

	// 节点迭代
	if node.Iteration {
		iters++
		if node.IterationTimes == 0 || iters < node.IterationTimes {
			logrus.WithField(logger.KeyCategory, logger.CategoryCore).
				WithField(logger.KeyEXT, a.ext).
				Infof("[%s] iterate node times: %d", node.ID, iters)
			goto START
		}

		logrus.WithField(logger.KeyCategory, logger.CategoryCore).
			WithField(logger.KeyEXT, a.ext).
			Infof("[%s] iterate node limit", node.ID)
	}

	return output, nil
}

func (a *Agent) getNodeInputs(node *schema.Node, input string, iters int) (map[string]any, map[string]any, error) {
	logrus.WithField(logger.KeyCategory, logger.CategoryCore).
		WithField(logger.KeyEXT, a.ext).
		Infof("[%s] get node inputs", node.ID)

	// 如果设置了节点输入，则渲染输入，否则使用上一个节点输出
	hasInput := false
	locals := make(map[string]any)
	inputs := a.variables.getVariables()

	// 获取节点输入参数
	for _, v := range node.InputParameters {
		vv, err := v.GetValue(inputs)
		if err != nil {
			if !v.Required {
				continue
			}
			return nil, nil, err
		}
		locals[v.Name] = vv.V()
		inputs[v.Name] = vv.V()

		if v.Name == schema.DefaultInputKey {
			hasInput = true
		}
	}

	//获取option参数
	for _, a := range node.Agents {
		op, err := a.Extract()
		if err != nil {
			continue
		}
		for _, o := range op {
			vv, err := o.GetValue(inputs)
			if err != nil {
				continue
			}
			inputs[o.Name] = vv.V()
			locals[o.Name] = vv.V()
		}
	}
	for _, t := range node.Tools {
		op, err := t.Extract()
		if err != nil {
			continue
		}
		for _, o := range op {
			vv, err := o.GetValue(inputs)
			if err != nil {
				continue
			}
			inputs[o.Name] = vv.V()
			locals[o.Name] = vv.V()
		}
	}
	for _, m := range node.Mcps {
		for _, t := range m.Tools {
			var op []schema.Parameter
			data, err := json.Marshal(t.Options)
			if err != nil {
				continue
			}
			err = json.Unmarshal(data, &op)
			if err != nil {
				continue
			}

			for _, o := range op {
				v, err := o.GetValue(inputs)
				if err != nil {
					continue
				}
				inputs[o.Name] = v.V()
				locals[o.Name] = v.V()

			}
		}
	}
	// 获取迭代输入参数
	for _, v := range node.IterationValues {
		var vs []any
		var ok bool

		if v.ListExpr != nil {
			val, err := v.ListExpr.GetValue(inputs)
			if err != nil {
				return nil, nil, err
			}
			vs, ok = val.V().([]any)
		} else {
			vs, ok = inputs[v.ListName].([]any)
		}
		if !ok {
			return nil, nil, ErrNodeIterateValue
		}
		if len(vs) == 0 {
			return nil, nil, ErrNodeIterateValueEmpty
		}

		if len(vs) > iters {
			locals[v.DestName] = vs[iters]
			inputs[v.DestName] = vs[iters]
		} else {
			return nil, nil, ErrNodeIterateLimit
		}
	}

	if !hasInput {
		locals[schema.DefaultInputKey] = input
		inputs[schema.DefaultInputKey] = input
	}

	logrus.WithField(logger.KeyCategory, logger.CategoryCore).
		WithField(logger.KeyEXT, a.ext).
		Infof("[%s] get node inputs done", node.ID)

	if node.Type == schema.NodeTypeActInterpreter {
		return locals, locals, nil
	}

	return locals, inputs, nil
}

func (a *Agent) setNodeOutputs(ctx context.Context, node *schema.Node, output string) (map[string]any, error) {
	logrus.WithField(logger.KeyCategory, logger.CategoryCore).
		WithField(logger.KeyEXT, a.ext).
		Infof("[%s] parse node output", node.ID)

	res := make(map[string]any, 0)
	// 将节点执行结果存储为智能体输出与下一个节点输入
	if len(output) > 0 {
		res[node.ResultKey] = output
		a.variables.setPartVariables(node.ID, res, node.Iteration)
		a.variables.setLocalVariables(node.ID, res, node.Iteration)
	}

	// 根据配置的解析器解析链返回值并存储上下文变量表
	for _, op := range node.OutputParameters {
		kvs := a.variables.getVariables()

		// 若迭代节点，把当前节点输出替换为非列表
		if node.Iteration {
			kvs[node.ResultKey] = output
			nodeKvs, ok := kvs[node.ID].(map[string]any)
			if ok {
				nodeKvs[node.ResultKey] = output
				kvs[node.ID] = nodeKvs
			}
		}

		v, err := op.GetValue(kvs)
		if err != nil {
			logrus.WithField(logger.KeyCategory, logger.CategoryCore).
				WithField(logger.KeyEXT, a.ext).
				Warnf("[%s] failed to get output parameter value: %s %s", node.ID, op.Name, err.Error())
			continue
		}

		// 正则标签多个参数提取兼容
		values := map[string]any{op.Name: v.V()}
		res[op.Name] = v.V()
		if op.Value != nil && op.Value.Type == schema.ParamValueTypeRegexp {
			vs, ok := v.IsObject()
			if ok {
				values = map[string]any{}
				for k, v := range vs {
					if k == parser.ParserRootKey {
						values[op.Name] = v
						res[op.Name] = v
					} else {
						values[k] = v
						res[k] = v
					}
				}
			}
		}

		// 设置到全局变量表
		logrus.WithField(logger.KeyCategory, logger.CategoryCore).
			WithField(logger.KeyEXT, a.ext).
			Infof("[%s] set parse kvs: %s", node.ID, op.Name)

		a.variables.setPartVariables(node.ID, values, node.Iteration)
		a.variables.setLocalVariables(node.ID, values, node.Iteration)
		if op.LongTerm {
			if err := a.termRepo.Update(ctx, a.config.ID, a.ext.GetValue(ext.EXTXORG),
				node.ID, values); err != nil {
				logrus.WithField(logger.KeyCategory, logger.CategoryCore).
					WithField(logger.KeyEXT, a.ext).
					Warnf("[%s] save long-term kvs: %s error: %s", node.ID, op.Name, err)
			}
		}
	}

	return res, nil
}
