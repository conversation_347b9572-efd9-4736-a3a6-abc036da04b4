package agent

import (
	"encoding/json"
	"secwalk/internal/domain/core/callback"
	"secwalk/internal/domain/core/memory"
	"secwalk/internal/domain/core/schema"
	"secwalk/internal/domain/core/schema/ext"
	"secwalk/internal/domain/core/xfile"
	"secwalk/internal/domain/repo"
	"secwalk/pkg/selector"
	"sync"
)

type Option func(*Agent)

func WithNodeID(nodeID string) Option {
	return func(a *Agent) {
		a.nodeID = nodeID
	}
}

func WithMessageNodeID(messageNodeID string) Option {
	return func(co *Agent) {
		co.MessageNodeID = messageNodeID
	}
}

func WithTermRepo(repo *repo.TermRepo) Option {
	return func(a *Agent) {
		a.termRepo = repo
	}
}

func WithAgentConfig(cfg *schema.AgentConfig) Option {
	return func(a *Agent) {
		a.config = cfg

		envs := make(map[string]any)

		for _, p := range append(cfg.PrivateEnvs, cfg.PublicEnvs...) {
			val, err := p.GetValue(map[string]any{})
			if err != nil {
				envs[p.Name] = p.InitValue()
			} else {
				envs[p.Name] = val.V()
			}
		}

		// 默认在环境变量中添加创建者租户ID、创建者用户ID、
		envs["UID"] = cfg.UID                       // 创建者用户ID
		envs["ORG"] = cfg.ORG                       // 创建者租户ID
		envs["X-UID"] = a.ext.GetValue(ext.EXTXUID) // 调用者用户ID
		envs["X-ORG"] = a.ext.GetValue(ext.EXTXORG) // 调用者租户ID
		envs["X-SID"] = a.ext.GetValue(ext.EXTSID)  // 会话ID
		envs["X-QID"] = a.ext.GetValue(ext.EXTQID)  // 对话ID

		a.variables.setEnvVariables(envs)

		inputs := make(map[string]any, len(cfg.InputParameters))
		for _, p := range cfg.InputParameters {
			inputs[p.Name] = p.InitValue()
			if p.Type == schema.TypeFile {
				xf := xfile.File{Name: "", ID: "", Tag: "", Size: 0}
				inputs[p.Name+xfile.FileAttributeSuffix] = xf.ToMap()
				continue
			}
		}
		a.variables.setUserVariables(inputs)

		for _, n := range cfg.Nodes {
			res := make(map[string]any)
			for _, p := range n.OutputParameters {
				res[p.Name] = p.InitValue()
				if p.Type == schema.TypeFile {
					xf := xfile.File{Name: "", ID: "", Tag: "", Size: 0}
					inputs[p.Name+xfile.FileAttributeSuffix] = xf.ToMap()
					continue
				}
			}

			for _, q := range n.Questions {
				res[q.Name] = map[string]any{
					schema.ReservedOptions:  "",
					schema.ReservedQuestion: "",
					schema.ReservedAnswer:   "",
				}
			}

			res[schema.DefaultResultKey] = ""
			a.variables.setPartVariables(n.ID, res, false)
		}
	}
}

func WithVariables(vars map[string]any) Option {
	return func(a *Agent) {
		for k, v := range vars {
			kvs, ok := v.(map[string]any)
			if ok {
				a.variables.setPartVariables(k, kvs, false)
			}
		}
	}
}

func WithStreamVerbose(fn callback.FnStreamVerbose) Option {
	return func(a *Agent) { a.fnStreamVerbose = fn }
}

func WithStreamPreview(fn callback.FnStreamPreview) Option {
	return func(a *Agent) { a.fnStreamPreview = fn }
}

func WithFnQuestion(fn callback.FnQuestion) Option {
	return func(a *Agent) { a.fnQuestion = fn }
}

func WithMemory(memory memory.Memory) Option {
	return func(a *Agent) { a.memory = memory }
}

func WithGateway(gateway *selector.Instance) Option {
	return func(a *Agent) { a.gateway = gateway }
}

func WithMasterEntry() Option {
	return func(a *Agent) { a.masterEntry = true }
}

func WithPStreamTokenUsage(fn callback.FnStreamTokenUsage) Option {
	return func(a *Agent) { a.fnPStreamTokenUsage = fn }
}

func WithQAMux(mux *sync.Mutex) Option {
	return func(a *Agent) { a.qaMux = mux }
}

func WithPartOption(opts any) Option {
	return func(co *Agent) {
		var ps []schema.Parameter
		data, err := json.Marshal(opts)
		if err != nil {
			return
		}
		err = json.Unmarshal(data, &ps)
		if err != nil {
			return
		}
	LOOP:
		for _, v := range ps {
			for i, arg := range co.config.InputParameters {
				if arg.Name == v.Name {
					co.config.InputParameters[i] = v
					continue LOOP
				}
			}
		}
	}
}
