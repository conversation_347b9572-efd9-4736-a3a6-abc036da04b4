package agent

import "errors"

var (
	ErrNodeWorksLimit        = errors.New("node run out of max times")
	ErrNodeRetryLimit        = errors.New("retry limit")
	ErrNodeIterateLimit      = errors.New("iterate limit")
	ErrNodeIterateValue      = errors.New("the value type for iterator must be a list")
	ErrNodeIterateValueEmpty = errors.New("iterate value empty")
	ErrGuidedChoiceValue     = errors.New("the value type for guided choice must be a list of strings")
	ErrGuidedJsonValue       = errors.New("the value type for guiede json must be a string")
	ErrGuidedRegexValue      = errors.New("the value type for guided regex must be a string")
)
