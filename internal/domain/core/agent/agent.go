package agent

import (
	"context"
	"fmt"
	"secwalk/internal/domain/core/callback"
	"secwalk/internal/domain/core/memory"
	"secwalk/internal/domain/core/message"
	"secwalk/internal/domain/core/schema"
	"secwalk/internal/domain/core/schema/ext"
	"secwalk/internal/domain/core/xfile"
	"secwalk/internal/domain/repo"
	"secwalk/pkg/logger"
	"secwalk/pkg/random"
	"secwalk/pkg/selector"
	"secwalk/pkg/util"
	"sync"
	"time"

	"github.com/sashabaranov/go-openai"
	"github.com/sirupsen/logrus"
)

type Agent struct {
	config              *schema.AgentConfig
	gateway             *selector.Instance
	memory              memory.Memory
	variables           *Variables
	usageStats          *openai.Usage
	agentRepo           *repo.AgentRepo
	toolkitRepo         *repo.ToolkitRepo
	mcpRepo             *repo.McpRepo
	termRepo            *repo.TermRepo
	cbSetVariable       callback.CbSetVariable
	fnStreamVerbose     callback.FnStreamVerbose
	fnStreamPreview     callback.FnStreamPreview
	fnStreamTokenUsage  callback.FnStreamTokenUsage
	fnPStreamTokenUsage callback.FnStreamTokenUsage
	fnQuestion          callback.FnQuestion
	pid                 string      // 父调用ID
	cid                 string      // 本调用ID
	nodeID              string      // 节点ID
	qaMux               *sync.Mutex // 问答锁
	ext                 ext.ExtType // 扩展信息
	MessageNodeID       string
	masterEntry         bool
	outputFinal         bool
}

func NewAgent(agentRepo *repo.AgentRepo, toolkitRepo *repo.ToolkitRepo,
	mcpRepo *repo.McpRepo, extv ext.ExtType, opts ...Option) (*Agent, error) {
	agent := &Agent{
		agentRepo:   agentRepo,
		toolkitRepo: toolkitRepo,
		mcpRepo:     mcpRepo,
		variables:   NewVariables(),
		usageStats: &openai.Usage{
			PromptTokensDetails:     &openai.PromptTokensDetails{},
			CompletionTokensDetails: &openai.CompletionTokensDetails{},
		},
		ext: extv,
	}
	for _, opt := range opts {
		opt(agent)
	}

	if agent.cbSetVariable == nil {
		agent.cbSetVariable = func(name string, kvs map[string]any) {
			agent.variables.setPartVariables(name, kvs, false)
			agent.variables.setLocalVariables(name, kvs, false)
		}
	}

	if agent.termRepo != nil {
		kvs, err := agent.termRepo.Get(context.Background(),
			agent.config.ID, agent.ext.GetValue(ext.EXTXORG))
		if err == nil && kvs != nil {
			for key, val := range kvs {
				if vv, ok := val.(map[string]any); ok {
					agent.variables.setPartVariables(key, vv, false)
				}
			}
		}
	}

	if agent.qaMux == nil {
		agent.qaMux = &sync.Mutex{}
	}

	agent.fnStreamTokenUsage = func(tu *openai.Usage) {
		agent.usageStats.PromptTokens += tu.PromptTokens
		agent.usageStats.CompletionTokens += tu.CompletionTokens
		agent.usageStats.TotalTokens += tu.TotalTokens
		if tu.CompletionTokensDetails != nil {
			agent.usageStats.CompletionTokensDetails.ReasoningTokens +=
				tu.CompletionTokensDetails.ReasoningTokens
		}
	}

	return agent, nil
}

func (a *Agent) Config() *schema.AgentConfig {
	return a.config
}

func (a *Agent) Call(ctx context.Context, inputs map[string]any, opts ...schema.CallOption) (*schema.AgentChatResult, error) {
	opt := schema.NewCallOptions()
	for _, option := range opts {
		option(opt)
	}
	if a.gateway == nil {
		a.gateway = opt.Gateway
	}

	a.pid = opt.PID
	a.cid = random.UniqueID()

	// 检查智能体输入是否符合定义
	for _, in := range a.config.InputParameters {
		val, err := in.GetValue(inputs)
		if err != nil {
			if !in.Required {
				continue
			}

			if len(a.config.InputParameters) != 1 {
				return nil, fmt.Errorf("%w: parameter '%s' are required", schema.ErrInputs, in.Name)
			}
			inputs[in.Name] = inputs[schema.DefaultInputKey]
		} else {
			inputs[in.Name] = val.V()
		}

		//文件类型特殊处理
		if in.Type == schema.TypeFile || in.Type == schema.TypeFiles {
			if f, err := in.InitFile(ctx, inputs, opt.EXT.GetValue(ext.EXTXORG)); err == nil {
				inputs[fmt.Sprintf("%s%s", in.Name, xfile.FileAttributeSuffix)] = f
			}
		}

		if err := in.VerifyValueType(inputs[in.Name]); err != nil {
			return nil, fmt.Errorf("%w: %v", schema.ErrInputs, err)
		}
	}

	// 输入参数设置
	a.variables.setUserVariables(inputs)

	var ok bool
	var err error
	var input, output string

	// 设置接口返回
	res := &schema.AgentChatResult{
		Session: &message.Session{
			ID:       a.ext.GetValue(ext.EXTSID),
			Messages: []message.Message{},
		},
		Results:    make(map[string]any),
		TokenUsage: a.usageStats,
	}

	// 输出调试信息
	if a.fnStreamVerbose != nil {
		a.fnStreamVerbose(&callback.VerboseMessage{
			Type:         callback.TypeAgent,
			Name:         a.config.Name,
			PID:          a.pid,
			CID:          a.cid,
			Stage:        callback.StageRun,
			State:        callback.CallStateSuccess,
			Timestamp:    time.Now().UnixMilli(),
			SystemPrompt: a.config.SystemPromptTemplate,
			UserPrompt:   a.config.UserPromptTemplate,
			Inputs:       inputs,
		})

		defer func() {
			if err != nil {
				a.fnStreamVerbose(&callback.VerboseMessage{
					Type:      callback.TypeAgent,
					Name:      a.config.Name,
					PID:       a.pid,
					CID:       a.cid,
					Stage:     callback.StageEnd,
					State:     callback.CallStateFailure,
					Error:     err.Error(),
					Timestamp: time.Now().UnixMilli(),
				})
			} else {
				a.fnStreamVerbose(&callback.VerboseMessage{
					Type:      callback.TypeAgent,
					Name:      a.config.Name,
					PID:       a.pid,
					CID:       a.cid,
					Stage:     callback.StageEnd,
					State:     callback.CallStateSuccess,
					Timestamp: time.Now().UnixMilli(),
					Output:    res,
				})
			}
		}()
	}

	val, ok := a.variables.getUserVariable(schema.DefaultInputKey)
	if !ok {
		return nil, fmt.Errorf("%w: agent input must be string", schema.ErrInputs)
	}
	if vv, ok := val.(string); ok {
		input = vv
	} else {
		input = util.ToPureJsonString(val)
	}

	if _, ok := a.variables.getUserVariable(schema.ReservedHistory); !ok {
		history, err := a.memory.LoadMemoryVariables(ctx)
		if err == nil {
			for key, value := range history {
				if key != schema.ReservedHistory {
					continue
				}
				a.variables.setUserVariables(map[string]any{schema.ReservedHistory: value})
			}

		}
	}

	logrus.WithField(logger.KeyCategory, logger.CategoryCore).
		WithField(logger.KeyEXT, a.ext).
		Infof("[%s] workflow start", a.config.Name)
	output, err = a.Run(ctx, a.config.Entry, input, false)
	if err != nil {
		return nil, err
	}
	logrus.WithField(logger.KeyCategory, logger.CategoryCore).
		WithField(logger.KeyEXT, a.ext).
		Infof("[%s] workflow ended", a.config.Name)

	// 如果有上次智能体，返回本智能体消耗的token
	if a.fnPStreamTokenUsage != nil {
		a.fnPStreamTokenUsage(a.usageStats)
	}

	// 设置用户交互输入
	if len(input) > 0 {
		res.Session.Messages = append(res.Session.Messages,
			message.Message{Role: message.ChatMessageRoleUser, Content: input})
	}

	content, resilts, err := a.runActInteraction(
		ctx,
		"",
		a.config.Interaction,
		a.variables.getVariables(),
		output,
		true,
	)
	if err != nil {
		return res, fmt.Errorf("%w: %v", schema.ErrConfig, err)
	}
	res.Results = resilts
	res.Session.Messages = append(res.Session.Messages,
		message.Message{Role: message.ChatMessageRoleAssistant, Content: content})
	return res, nil
}
