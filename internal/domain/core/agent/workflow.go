package agent

import (
	"secwalk/internal/domain/core/schema"
)

// 查找指定节点下标
func findNode(nodes []*schema.Node, id string) (*schema.Node, error) {
	for _, v := range nodes {
		if v.ID == id {
			return v, nil
		}
	}
	return nil, schema.ErrAgentNodeNotExist
}

// 查找节点上连接的并行属性边
func findParalleEdges(edges []*schema.Edge, src string) []*schema.Edge {
	res1 := make([]*schema.Edge, 0)
	for _, edge := range edges {
		if edge.SrcNode == src && edge.Parallel {
			res1 = append(res1, edge)
		}
	}
	return res1
}

// 查找节点上连接的非并行边
func findConnectEdges(edges []*schema.Edge, src string) []*schema.Edge {
	res1 := make([]*schema.Edge, 0)
	res2 := make([]*schema.Edge, 0)
	for _, edge := range edges {
		if edge.SrcNode == src && !edge.Parallel {
			if len(edge.Condition) > 0 {
				res1 = append(res1, edge)
			} else {
				res2 = append(res2, edge)
			}
		}
	}
	return append(res1, res2...)
}
