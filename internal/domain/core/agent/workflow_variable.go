package agent

import (
	"secwalk/internal/domain/core/schema"
	"secwalk/internal/domain/core/template"
	"sync"

	"github.com/expr-lang/expr"
	"github.com/jinzhu/copier"
)

const (
	KvUserKey  = "kv_user"
	KvEnvsKey  = "kv_envs"
	KvLocalKey = "kv_local"
)

type Variables struct {
	mux sync.RWMutex
	kvs map[string]any
}

func NewVariables() *Variables {
	return &Variables{
		mux: sync.RWMutex{},
		kvs: make(map[string]any),
	}
}

func (v *Variables) setEnvVariables(kvs map[string]any) {
	v.setPartVariables(KvEnvsKey, kvs, false)
}

func (v *Variables) setUserVariables(kvs map[string]any) {
	v.setPartVariables(KvUserKey, kvs, false)
}

func (v *Variables) setPartVariables(name string, kvs map[string]any, iterator bool) {
	v.mux.Lock()
	defer v.mux.Unlock()

	vv, ok := v.kvs[name].(map[string]any)
	if !ok {
		vv = make(map[string]any)
	}

	for key, val := range kvs {
		if key == KvEnvsKey {
			continue
		}
		if iterator {
			org, ok := vv[key].([]any)
			if !ok {
				org = make([]any, 0)
			}
			vv[key] = append(org, val)
		} else {
			vv[key] = val
		}

		v.kvs[key] = val
	}
	v.kvs[name] = vv
}

func (v *Variables) setLocalPrune(name string) {
	v.mux.Lock()
	defer v.mux.Unlock()

	nv, ok := v.kvs[name].(map[string]any)
	if !ok {
		nv = make(map[string]any)
	}
	nv[KvLocalKey] = make(map[string]any)
	v.kvs[name] = nv
}

func (v *Variables) setLocalVariables(name string, kvs map[string]any, iterator bool) {
	v.mux.Lock()
	defer v.mux.Unlock()

	nv, ok := v.kvs[name].(map[string]any)
	if !ok {
		nv = make(map[string]any)
	}

	nlv, ok := nv[KvLocalKey].(map[string]any)
	if !ok {
		nlv = make(map[string]any)
	}

	for key, val := range kvs {
		if iterator {
			org, ok := nlv[key].([]any)
			if !ok {
				org = make([]any, 0)
			}
			nlv[key] = append(org, val)
		} else {
			nlv[key] = val
		}
	}

	nv[KvLocalKey] = nlv
	v.kvs[name] = nv
}

func (v *Variables) getVariables() map[string]any {
	v.mux.RLock()
	defer v.mux.RUnlock()

	res := make(map[string]any, len(v.kvs))
	copier.CopyWithOption(&res, &v.kvs, copier.Option{DeepCopy: true})
	return res
}

func (v *Variables) getVariable(key string) any {
	v.mux.RLock()
	defer v.mux.RUnlock()

	if val, ok := v.kvs[key]; ok {
		return val
	}
	return map[string]any{}
}

func (v *Variables) getUserVariable(key string) (any, bool) {
	v.mux.RLock()
	defer v.mux.RUnlock()

	kvs, ok := v.kvs[KvUserKey].(map[string]any)
	if !ok {
		return nil, false
	}

	val, ok := kvs[key]
	return val, ok
}

func (v *Variables) ExprMatch(s string) (bool, error) {
	v.mux.RLock()
	defer v.mux.RUnlock()

	if len(s) == 0 {
		return true, nil
	}

	ref, err := template.RenderTemplate(s, v.kvs)
	if err != nil {
		ref = ""
	}

	prgm, err := expr.Compile(ref)
	if err != nil {
		return false, err
	}

	out, err := expr.Run(prgm, v.kvs)
	if err != nil {
		return false, err
	}

	if m, ok := out.(bool); ok {
		return m, nil
	}

	return false, nil
}

func (v *Variables) ExprValue(e *schema.ExprValue, type1 ...string) (*schema.Value, error) {
	v.mux.RLock()
	defer v.mux.RUnlock()

	return e.GetValue(v.kvs, type1...)
}
