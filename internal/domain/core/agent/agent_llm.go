package agent

import (
	"context"
	"secwalk/internal/domain/core/chain"
	"secwalk/internal/domain/core/endpoint/hostmcp"
	"secwalk/internal/domain/core/llm"
	"secwalk/internal/domain/core/llm/dbapp"
	"secwalk/internal/domain/core/schema"
	"secwalk/internal/domain/core/schema/ext"
	"secwalk/internal/domain/core/toolkit/mcp"
	"secwalk/pkg/logger"

	"github.com/sirupsen/logrus"
)

func (a *Agent) runNodeLLM(ctx context.Context, node *schema.Node, locals, inputs map[string]any, cid string) (string, error) {
	// 初始化模型客户端
	if node.Model == nil {
		node.Model = a.config.Model
	}

	model, err := a.createLLMClient(node.Model)
	if err != nil {
		return "", err
	}

	// 创建模型链
	c, err := a.createChain(ctx, node, model)
	if err != nil {
		return "", err
	}

	copts, err := a.getCallOptions(node, c, locals, cid)
	if err != nil {
		return "", err
	}

	// 调用模型链
	return chain.Call(
		ctx,
		c,
		inputs,
		copts...,
	)
}

// 创建模型客户端
func (a *Agent) createLLMClient(c *schema.ModelConfig) (llm.Model, error) {
	switch c.Type {
	case schema.TypeChat:
		return dbapp.NewChatLLM(
			dbapp.WithAddress(a.gateway.Host),
			dbapp.WithAPIKey(a.gateway.Metas["authorization"]),
			dbapp.WithModel(c.Model),
		)
	default:
		return nil, schema.ErrModelType
	}
}

// 创建模型链
func (a *Agent) createChain(ctx context.Context, node *schema.Node, model llm.Model) (chain.Chain, error) {
	opts, err := a.getChainOptions(ctx, node)
	if err != nil {
		return nil, err
	}

	switch node.Type {
	// 混合模型链，自动选择调用工具、智能体、数据库
	case schema.NodeTypeLLMMixup:
		return chain.NewMixupChain(model,
			append(opts, chain.WithCotVersion(a.config.CotVersion))...)
	// 基础模型链
	case schema.NodeTypeLLMBasic:
		return chain.NewLLMChain(model, opts...)
	// 工具模型链
	case schema.NodeTypeLLMTools:
		return chain.NewReActChain(model,
			append(opts, chain.WithOnce())...)
	// 思维模型链
	case schema.NodeTypeLLMReact:
		return chain.NewReActChain(model, opts...)
	// function call 模型链
	case schema.NodeTypeLLMFunctionCall:
		return chain.NewFunctionCallChain(model, opts...)
	// 数据库检索模型链
	case schema.NodeTypeLLMRetrievalSql:
		return chain.NewSQLChain(model, opts...)
	// 知识库检索模型链
	case schema.NodeTypeLLMRetrievalDoc:
		return chain.NewDocChain(model, opts...)
	// 数据库查询生成
	case schema.NodeTypeLLMGeneraterSql:
		return chain.NewSQLChain(model,
			append(opts, chain.WithOnce())...)
	default:
		return nil, schema.ErrNodeType
	}
}

// 获取链创建可选项
func (a *Agent) getChainOptions(ctx context.Context, node *schema.Node) ([]chain.Option, error) {
	chainOpts := []chain.Option{
		chain.WithMaxIterations(node.MaxReflections),
		chain.WithMemory(a.memory),
		chain.WithDB(node.DB),
		chain.WithKB(node.KB),
	}

	if node.ParallelAction {
		chainOpts = append(chainOpts, chain.WithParallel())
	}

	if node.NoRepeat {
		chainOpts = append(chainOpts, chain.WithNoRepeat())
	}

	if node.NoAction {
		chainOpts = append(chainOpts, chain.WithNoAction())
	}

	// 实例化工具
	tools := make([]schema.Tool, 0)
	for _, v := range node.Tools {
		tool, err := a.toolkitRepo.FetchAbility(ctx, v.ID, v.Options)
		if err != nil {
			logrus.WithField(logger.KeyCategory, logger.CategoryCore).
				WithField(logger.KeyEXT, a.ext).
				Warnf("fetch ability failure, ignore: %s", err.Error())

			return nil, err
		}
		tools = append(tools, tool)
	}

	chainOpts = append(chainOpts, chain.WithTools(append(tools,
		a.fetchMCPTools(ctx, node)...)))

	// 实例化智能体
	agents := make([]schema.Agent, 0)
	for _, v := range node.Agents {
		logrus.WithField(logger.KeyCategory, logger.CategoryCore).
			WithField(logger.KeyEXT, a.ext).
			Infof("[%s] fetch agent: %s", node.ID, v.ID)

		ac, err := a.agentRepo.QueryAndDecrypt(ctx, v.ID)
		if err != nil {
			return nil, err
		}

		createOpts := []Option{
			WithGateway(a.gateway),
			WithMemory(a.memory),
			WithNodeID(node.ID),
			WithStreamPreview(a.fnStreamPreview),
			WithStreamVerbose(a.fnStreamVerbose),
			WithPStreamTokenUsage(a.fnStreamTokenUsage),
			WithFnQuestion(a.fnQuestion),
			WithAgentConfig(ac),
			WithTermRepo(a.termRepo),
			WithQAMux(a.qaMux),
		}
		if len(a.MessageNodeID) == 0 {
			createOpts = append(createOpts, WithMessageNodeID(node.ID))
		} else {
			createOpts = append(createOpts, WithMessageNodeID(a.nodeID))
		}

		agent, err := NewAgent(a.agentRepo, a.toolkitRepo, a.mcpRepo, a.ext, createOpts...)
		if err != nil {
			return nil, err
		}

		agents = append(agents, agent)
	}
	chainOpts = append(chainOpts, chain.WithAgents(agents))

	// 提示模板配置
	if len(node.SystemPromptTemplate) > 0 || len(a.config.SystemPromptTemplate) > 0 {
		if len(node.SystemPromptTemplate) > 0 {
			chainOpts = append(chainOpts, chain.WithSystemPrompt(node.SystemPromptTemplate))
		} else {
			chainOpts = append(chainOpts, chain.WithSystemPrompt(a.config.SystemPromptTemplate))
		}
	}

	if len(node.UserPromptTemplate) > 0 || len(a.config.UserPromptTemplate) > 0 {
		if len(node.UserPromptTemplate) > 0 {
			chainOpts = append(chainOpts, chain.WithUserPrompt(node.UserPromptTemplate))
		} else {
			chainOpts = append(chainOpts, chain.WithUserPrompt(a.config.UserPromptTemplate))
		}
	}

	return chainOpts, nil
}

// 获取链调用可选项
func (a *Agent) getCallOptions(node *schema.Node, c chain.Chain, locals map[string]any, cid string) ([]schema.CallOption, error) {
	callOpts := []schema.CallOption{
		schema.WithTemperature(node.Model.Temperature),
		schema.WithTopK(node.Model.TopK),
		schema.WithTopP(node.Model.TopP),
		schema.WithRepetitionPenalty(node.Model.RepetitionPenalty),
		schema.WithName(a.config.Name),
		schema.WithEXT(a.ext),
		schema.WithPID(cid),
		schema.WithNodeID(node.ID),
		schema.WithLocals(locals),
		schema.WithCbSetVariable(a.cbSetVariable),
		schema.WithFnStreamVerbose(a.fnStreamVerbose),
		schema.WithFnStreamPreview(a.fnStreamPreview),
		schema.WithFnStreamTokenUsage(a.fnStreamTokenUsage),
	}
	if len(a.MessageNodeID) == 0 {
		callOpts = append(callOpts, schema.WithMessageNodeID(node.ID))
	} else {
		callOpts = append(callOpts, schema.WithMessageNodeID(a.MessageNodeID))
	}

	if len(node.Model.Model) > 0 {
		callOpts = append(callOpts, schema.WithModel(node.Model.Model))
	}

	if len(node.Model.LoraType) > 0 {
		callOpts = append(callOpts, schema.WithLoraType(node.Model.LoraType))
	}

	if node.Model.MaxTokens > 0 {
		callOpts = append(callOpts, schema.WithMaxTokens(node.Model.MaxTokens))
	}

	if len(node.Model.Stop) > 0 {
		callOpts = append(callOpts, schema.WithStopWords(node.Model.Stop))
	}

	if node.Model.GuidedChoiceExpr != nil {
		val, err := a.variables.ExprValue(node.Model.GuidedChoiceExpr, schema.TypeArray)
		if err != nil {
			return nil, err
		}

		list, ok := val.IsStringList()
		if !ok {
			return nil, ErrGuidedChoiceValue
		}

		callOpts = append(callOpts, schema.WithGuidedChoices(list))
	}

	if node.Model.GuidedJsonExpr != nil {
		val, err := a.variables.ExprValue(node.Model.GuidedJsonExpr, schema.TypeString)
		if err != nil {
			return nil, err
		}

		if !val.IsString() {
			return nil, ErrGuidedJsonValue
		}
		callOpts = append(callOpts, schema.WithGuidedJson(val.String()))
	}

	if node.Model.GuidedRegexExpr != nil {
		val, err := a.variables.ExprValue(node.Model.GuidedRegexExpr, schema.TypeString)
		if err != nil {
			return nil, err
		}

		if !val.IsString() {
			return nil, ErrGuidedRegexValue
		}
		callOpts = append(callOpts, schema.WithGuidedRegex(val.String()))
	}

	if node.Model.LogProbs > 0 {
		callOpts = append(callOpts, schema.WithLogProbs(node.Model.LogProbs))
	}

	if node.Model.Seed > 0 {
		callOpts = append(callOpts, schema.WithSeed(node.Model.Seed))
	}

	if node.Model.Reasoning == schema.ReasoningDefault && node.Model.Model == schema.HengNaoR1 {
		switch c.(type) {
		case *chain.LLMChain:
			callOpts = append(callOpts, schema.WithReasoning())
		}
	} else {
		if node.Model.Reasoning == schema.ReasoningON {
			callOpts = append(callOpts, schema.WithReasoning())
		}
	}

	if node.UseMemory {
		callOpts = append(callOpts, schema.WithUseMemory())
	}

	if node.LogMemory {
		callOpts = append(callOpts, schema.WithLogMemory())
	}

	if a.masterEntry {
		callOpts = append(callOpts, schema.WithMasterEntry())
	}

	if a.outputFinal {
		callOpts = append(callOpts, schema.WithFinalOut(true))
	}

	riskLevek := a.ext.GetValue(ext.EXTXORGRiskLevel)
	if riskLevek == schema.ORGRiskLevelMedium || riskLevek == schema.ORGRiskLevelHigh {
		callOpts = append(callOpts, schema.WithFnPromptGuard(schema.NewLeakGuard(riskLevek)))
	}

	tokenChecker := schema.NewTokenChecker(chain.ReactKeyAction, chain.ReactKeyActionInput, chain.ReactKeyFinalAnswer)
	if tokenChecker != nil {
		callOpts = append(callOpts, schema.WithTokenChecker(tokenChecker))
	}

	return callOpts, nil
}

func (a *Agent) fetchMCPTools(ctx context.Context, node *schema.Node) []schema.Tool {
	tools := make([]schema.Tool, 0)

	for _, v := range node.Mcps {
		name, entry, err := a.mcpRepo.Query(ctx, v.ID)
		if err != nil {
			continue
		}

		cli, err := hostmcp.NewMCPClient(ctx, name, entry,
			schema.WithEXT(a.ext),
			schema.WithNodeID(a.nodeID),
		)
		if err != nil {
			continue
		}

		tk, err := cli.QueryTools(ctx)
		if err != nil {
			continue
		}

		if len(v.Tools) == 0 {
			for _, vv := range tk.Tools {
				tool, err := mcp.New(ctx, cli, schema.WithToolConfig(&vv))
				if err != nil {
					continue
				}

				tools = append(tools, tool)
			}
		} else {
			for _, vv := range tk.Tools {
				for _, vvv := range v.Tools {
					if vv.Name == vvv.Name {
						tool, err := mcp.New(ctx, cli,
							schema.WithToolConfig(&vv),
							schema.WithPartOption(vvv.Options))
						if err != nil {
							continue
						}

						tools = append(tools, tool)
					}
				}
			}
		}
	}

	return tools
}
