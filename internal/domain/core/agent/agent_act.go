package agent

import (
	"context"
	"errors"
	"fmt"
	"math"
	"secwalk/internal/config"
	"secwalk/internal/domain/core/callback"
	"secwalk/internal/domain/core/endpoint"
	"secwalk/internal/domain/core/endpoint/codeide"
	"secwalk/internal/domain/core/schema"
	"secwalk/internal/domain/core/schema/ext"
	"secwalk/internal/domain/core/xfile"
	"secwalk/pkg/logger"
	"secwalk/pkg/random"
	"time"

	"github.com/sirupsen/logrus"
)

func (a *Agent) runNodeAct(ctx context.Context, node *schema.Node,
	locals, inputs map[string]any, cid string) (string, error) {
	switch node.Type {
	case schema.NodeTypeActTool:
		return a.runActTool(ctx, node, locals, inputs, cid)
	case schema.NodeTypeActAgent:
		return a.runActAgent(ctx, node, inputs, cid)
	case schema.NodeTypeActLogic:
		return "", nil
	case schema.NodeTypeActInterpreter:
		return a.runActInterpreter(ctx, node, inputs)
	case schema.NodeTypeActInteraction:
		return "", a.runActInteractionQAs(ctx, node, inputs)
	default:
		return "", schema.ErrNodeType
	}
}

func (a *Agent) runActTool(ctx context.Context, node *schema.Node,
	locals, inputs map[string]any, cid string) (string, error) {
	var tool schema.Tool
	var err error

	// MCP tool
	if len(node.Tools) == 0 {
		logrus.WithField(logger.KeyCategory, logger.CategoryCore).
			WithField(logger.KeyEXT, a.ext).
			Infof("[%s] fetch mcp tool: %v", node.ID, node.Mcps)
		tools := a.fetchMCPTools(ctx, node)
		if len(tools) == 0 {
			return "", errors.New("mcp server tool not exist")
		}

		tool = tools[0]
	} else {
		logrus.WithField(logger.KeyCategory, logger.CategoryCore).
			WithField(logger.KeyEXT, a.ext).
			Infof("[%s] fetch tool: %s", node.ID, node.Tools[0].ID)

		tool, err = a.toolkitRepo.FetchAbility(ctx, node.Tools[0].ID, node.Tools[0].Options)
		if err != nil {
			return "", err
		}
	}

	return schema.ToolCall(
		ctx,
		tool,
		inputs,
		schema.WithEXT(a.ext),
		schema.WithPID(cid),
		schema.WithMessageNodeID(a.MessageNodeID),
		schema.WithNodeID(node.ID),
		schema.WithLocals(locals),
		schema.WithCbSetVariable(a.cbSetVariable),
		schema.WithFnStreamVerbose(a.fnStreamVerbose),
		schema.WithFnStreamPreview(a.fnStreamPreview),
	)
}

func (a *Agent) runActAgent(ctx context.Context, node *schema.Node,
	inputs map[string]any, cid string) (string, error) {
	logrus.WithField(logger.KeyCategory, logger.CategoryCore).
		WithField(logger.KeyEXT, a.ext).
		Infof("[%s] fetch agent: %s", node.ID, node.Agents[0].ID)

	ac, err := a.agentRepo.QueryAndDecrypt(ctx, node.Agents[0].ID)
	if err != nil {
		return "", err
	}

	if err = schema.VerifyAgent(ac, config.GetVersion()); err != nil {
		return "", fmt.Errorf("%w: %v", schema.ErrConfig, err)
	}

	e := ext.New(a.ext.ToString())
	e.SetValue(ext.EXTAID, ac.ID)

	createOpts := []Option{
		WithGateway(a.gateway),
		WithMemory(a.memory),
		WithStreamPreview(a.fnStreamPreview),
		WithStreamVerbose(a.fnStreamVerbose),
		WithPStreamTokenUsage(a.fnStreamTokenUsage),
		WithFnQuestion(a.fnQuestion),
		WithNodeID(node.ID),
		WithAgentConfig(ac),
		WithTermRepo(a.termRepo),
		WithQAMux(a.qaMux),
		WithPartOption(node.Agents[0].Options),
	}
	if len(a.MessageNodeID) == 0 {
		createOpts = append(createOpts, WithMessageNodeID(node.ID))
	} else {
		createOpts = append(createOpts, WithMessageNodeID(a.MessageNodeID))
	}

	agent, err := NewAgent(a.agentRepo, a.toolkitRepo, a.mcpRepo, e, createOpts...)
	if err != nil {
		return "", err
	}

	res, err := agent.Call(
		ctx,
		inputs,
		schema.WithPID(cid),
		schema.WithEXT(e),
	)
	if err != nil {
		return "", err
	}

	if a.cbSetVariable != nil {
		a.cbSetVariable(node.ID, res.Results)
	}

	return res.Session.Messages[len(res.Session.Messages)-1].Content, nil
}

func (a *Agent) runActInteractionQAs(ctx context.Context, node *schema.Node,
	inputs map[string]any) error {
	nodeID := node.ID
	if len(a.nodeID) > 0 {
		nodeID = a.nodeID
	}

	for _, i := range node.Interactions {
		_, _, err := a.runActInteraction(ctx, nodeID, i, inputs, "", false)
		if err != nil {
			return err
		}
	}

	for _, i := range node.Questions {
		if err := a.runActQA(ctx, nodeID, node, i, inputs); err != nil {
			if errors.Is(err, context.DeadlineExceeded) && ctx.Err() == nil {
				logrus.WithField(logger.KeyCategory, logger.CategoryCore).
					WithField(logger.KeyEXT, a.ext).
					Warnf("[%s] question answering timeout!", i.Name)
				continue
			}
			return err
		}
	}

	return nil
}

func (a *Agent) runActInteraction(_ context.Context, nodeID string,
	interaction schema.Interaction, inputs map[string]any,
	output string, final bool) (string, map[string]any, error) {
	content := ""
	results := make(map[string]any)

	for _, o := range interaction.OutputParameters {
		v, err := o.GetValue(inputs)
		if err != nil {
			continue
		}
		if v.IsString() && len(v.String()) == 0 {
			continue
		}

		if o.Type == schema.TypeFile && !a.masterEntry {
			fo := schema.Parameter{
				Name: o.Name + xfile.FileAttributeSuffix,
				Type: schema.TypeFile,
				Value: &schema.ExprValue{
					Expr: o.Value.Expr + xfile.FileAttributeSuffix,
					Type: o.Value.Type,
				},
			}
			fv, err := fo.GetValue(inputs)
			if err == nil {
				results[fo.Name] = fv.V()
			}
		}

		results[o.Name] = v.V()
	}

	if interaction.OutputExpression != nil {
		v, err := interaction.OutputExpression.GetValue(inputs)
		if err != nil {
			return "", nil, err
		}
		content = v.String()
	} else {
		content = output
	}

	if a.fnStreamPreview != nil {
		if final {
			if a.masterEntry {
				premsg := &callback.PreviewMessage{
					Type:      callback.TypeInline,
					From:      callback.FromExecuteResult,
					Name:      a.config.Name,
					Timestamp: time.Now().UnixMilli(),
					MessageID: random.UniqueID(),
					Content:   content,
					Results:   results,
				}
				if !a.outputFinal {
					callback.PreviewFackStream(a.fnStreamPreview, premsg)
				}
			}
		} else {
			premsg := &callback.PreviewMessage{
				Type:      callback.TypeCustom,
				From:      interaction.From,
				NodeID:    nodeID,
				Name:      a.config.Name,
				Timestamp: time.Now().UnixMilli(),
				MessageID: random.UniqueID(),
				Content:   content,
				Results:   results,
			}
			if interaction.OutputExpression != nil {
				callback.PreviewFackStream(a.fnStreamPreview, premsg, interaction.Stream)
			}
		}
	}

	return content, results, nil
}

func (a *Agent) runActQA(ctx context.Context, nodeID string,
	node *schema.Node, q schema.Question, inputs map[string]any) error {
	a.qaMux.Lock()
	defer a.qaMux.Unlock()

	if ctx.Err() != nil {
		return ctx.Err()
	}

	ctx, cancel := context.WithTimeout(ctx, time.Second*time.Duration(q.Timeout))
	defer cancel()

	content, err := q.Message.GetValue(inputs)
	if err != nil {
		return err
	}
	options := make(map[string]any, 0)
	for _, v := range q.Options {
		o, err := v.GetValue(inputs)
		if err != nil {
			return err
		}
		options[v.Name] = o.V()
	}

	res := map[string]any{}
	if a.fnQuestion == nil {
		if q.Type == schema.QuestionTypeInput {
			res[q.Name] = ""
		} else {
			res[q.Name] = []string{}
		}
	} else {
		res, err = callback.SendQuestionMessage(ctx, a.fnQuestion, &callback.QuestionMessage{
			Type:    q.Type,
			Name:    q.Name,
			NodeID:  nodeID,
			Message: content.String(),
			Options: options,
		})
		if err != nil {
			logrus.WithField(logger.KeyCategory, logger.CategoryCore).
				WithField(logger.KeyEXT, a.ext).
				Warnf("[%s] recv question human input error: %s", node.ID, err)
			if q.Type == schema.QuestionTypeInput || q.Type == schema.QuestionTypeFile {
				res = map[string]any{q.Name: ""}
			} else {
				res = map[string]any{q.Name: map[string]any{}}
			}
		}
	}

	kvs := map[string]any{
		schema.ReservedOptions:  options,
		schema.ReservedQuestion: content.String(),
		schema.ReservedAnswer:   res[q.Name],
	}

	switch q.Type {
	case schema.QuestionTypeFile:
		name, ok := res[q.Name].(string)
		if !ok {
			return err
		}
		f, err := xfile.Parse(name)
		if err != nil {
			break
		}

		len, err := endpoint.StatFile(ctx, f.Tag, a.ext.GetValue(ext.EXTXORG))
		if err != nil {
			return err
		}
		f.Size = math.Round(float64(len)/1024*100) / 100
		kvs[schema.ReservedAnswer] = map[string]any{
			q.Name:                             f.Tag,
			q.Name + xfile.FileAttributeSuffix: f.ToMap(),
		}
	case schema.QuestionTypeFiles:
		vs := make([]any, 0)
		fs, ok := res[q.Name].([]any)
		if !ok {
			return err
		}
		for _, v := range fs {
			fn, ok := v.(string)
			if !ok {
				continue
			}
			f, err := xfile.Parse(fn)
			if err != nil {
				return err
			}
			len, err := endpoint.StatFile(ctx, f.Tag, a.ext.GetValue(ext.EXTXORG))
			if err != nil {
				continue
			}
			f.Size = math.Round(float64(len)/1024*100) / 100
			vs = append(vs, f.ToMap())
			vs = append(vs, f.Tag)
		}

		kvs[schema.ReservedAnswer] = map[string]any{
			q.Name: vs,
		}
	}
	res = map[string]any{q.Name: kvs}
	a.variables.setPartVariables(node.ID, res, node.Iteration)
	a.variables.setLocalVariables(node.ID, res, node.Iteration)
	return err
}

func (a *Agent) runActInterpreter(ctx context.Context, node *schema.Node,
	inputs map[string]any) (string, error) {
	body := &codeide.InterpreterRequest{
		Lang:   node.Interpreter.Lang,
		Code:   node.Interpreter.Code,
		Inputs: inputs,
	}

	res, err := codeide.CodeInterpreter(ctx, body,
		schema.WithNodeID(node.ID),
		schema.WithEXT(a.ext),
	)
	if err != nil {
		return "", err
	}

	return res.Output, nil
}
