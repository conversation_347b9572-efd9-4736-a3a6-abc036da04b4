package xfile

import (
	"encoding/json"
	"errors"
	"fmt"
	"math"
	"path/filepath"
	"regexp"
	"secwalk/pkg/util"
	"strings"

	"github.com/google/uuid"
	"github.com/h2non/filetype"
)

var ErrFileNameMatch = errors.New("the file name does not match expectations")
var FileAttributeSuffix = "_attribute"

var re = regexp.MustCompile(`^(?:file|image):(?P<Name>[^<]*?)(?:\.(?P<Suffix>[^.<]+))?<(?P<Extension>.+)>`)

type File struct {
	ID        string  `json:"id"`        // 文件ID
	Name      string  `json:"name"`      // 文件名
	Size      float64 `json:"size"`      // 文件大小
	Extension string  `json:"extension"` // 文件扩展名
	Content   []byte  `json:"content"`   // 文件内容
	Tag       string  `json:"-"`
}

func New(name string, data []byte) *File {
	if len(name) == 0 {
		name = util.MD5(data)
	}

	if len(filepath.Ext(name)) == 0 {
		fty, err := filetype.Match(data)
		if err != nil {
			name += ".bin"
		} else {
			name += "." + fty.Extension
		}
	}

	xf := &File{
		ID:        uuid.New().String(),
		Name:      name,
		Size:      math.Round(float64(len(data))/1024*100) / 100,
		Content:   data,
		Extension: strings.TrimRight(filepath.Ext(name), "."),
	}
	xf.Tag = fmt.Sprintf("file:%s<%s>", xf.Name, xf.ID)
	return xf
}

func Load(data []byte) (*File, error) {
	xf := &File{}
	err := json.Unmarshal(data, xf)
	if err != nil {
		return nil, err
	}

	if len(xf.ID) <= 0 {
		return nil, errors.New("file ID must be not empty")
	}
	xf.Tag = fmt.Sprintf("file:%s<%s>", xf.Name, xf.ID)
	return xf, nil
}

func Parse(str string) (*File, error) {
	matches := re.FindStringSubmatch(str)
	if len(matches) != 4 {
		return nil, ErrFileNameMatch
	}

	f := &File{
		Name:      matches[1],
		Extension: matches[2],
		ID:        matches[3],
	}
	if len(f.Extension) > 0 {
		f.Name += "." + f.Extension
	}

	f.Tag = str
	return f, nil
}

func (m *File) String() string {
	return m.Tag
}

func (m *File) SetType(display string) {
	m.Tag = fmt.Sprintf("%s:%s<%s>", display, m.Name, m.ID)
}

func (m *File) Dump() ([]byte, error) {
	return json.Marshal(m)
}

func (m *File) SetContent(data []byte) {
	m.Content = data
}

func (m *File) SetName(name string) {
	m.Name = name
	m.Extension = strings.TrimLeft(filepath.Ext(m.Name), ".")
}

func (m *File) SetNameFromHeader(header string) {
	if len(header) > 0 {
		re := regexp.MustCompile(`(?i).*filename\*?=(?:[^']+''|\"?)([^\";]*)\"?$`)
		matches := re.FindStringSubmatch(header)
		if len(matches) < 2 {
			m.Name = header
			return
		}
		filename := util.URLDecode(matches[1])
		if strings.Contains(filename, `''`) {
			parts := strings.SplitN(filename, `''`, 2)
			if len(parts) == 2 {
				filename = parts[1]
			}
		}
		m.Name = filename
		m.Extension = strings.TrimLeft(filepath.Ext(m.Name), ".")
	}
}

func (m *File) ToMap() map[string]any {
	val := map[string]any{
		"ID":        m.ID,
		"Name":      m.Name,
		"Size":      m.Size,
		"Extension": m.Extension,
	}

	return val
}
