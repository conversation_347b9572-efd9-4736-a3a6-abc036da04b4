package prompt

import (
	"fmt"

	"secwalk/internal/domain/core/message"
)

var _ message.PromptValue = ChatPromptValue{}

// ChatPromptValue is a prompt value that is a list of chat messages.
type ChatPromptValue []message.Message

// String returns the chat message slice as a buffer string.
func (v ChatPromptValue) String() string {
	s := message.GetBufferString(v)
	if len(s) > 0 {
		return s
	}
	return fmt.Sprintf("%v", []message.Message(v))
}

// Messages returns the Message slice.
func (v ChatPromptValue) Messages() []message.Message {
	return []message.Message(v)
}
