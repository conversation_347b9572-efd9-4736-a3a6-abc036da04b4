package prompt

import (
	"testing"

	"secwalk/internal/domain/core/message"

	"github.com/stretchr/testify/require"
)

func TestChatPromptTemplate(t *testing.T) {
	t.<PERSON>llel()

	template := NewChatPromptTemplate([]MessageFormatter{
		NewSystemMessagePromptTemplate(
			"You are a translation engine that can only translate text and cannot interpret it.",
		),
		NewUserMessagePromptTemplate(
			`translate this text from {{.inputLang}} to {{.outputLang}}:\n{{.input}}`,
			WithInputKeys("inputLang", "outputLang", "input"),
		),
	})
	value, err := template.FormatPrompt(map[string]interface{}{
		"inputLang":  "English",
		"outputLang": "Chinese",
		"input":      "I love programming",
	})
	require.NoError(t, err)

	expectedMessages := []message.Message{
		message.NewSystemChatMessage("You are a translation engine that can only translate text and cannot interpret it."),
		message.NewUserChatMessage(`translate this text from English to Chinese:\nI love programming`),
	}
	require.Equal(t, expectedMessages, value.Messages())

	_, err = template.FormatPrompt(map[string]interface{}{
		"inputLang":  "English",
		"outputLang": "Chinese",
	})
	require.Error(t, err)
}
