package prompt

import (
	"secwalk/internal/domain/core/message"
)

var _ MessageFormatter = SystemMessagePromptTemplate{}
var _ MessageFormatter = AssistantMessagePromptTemplate{}
var _ MessageFormatter = UserMessagePromptTemplate{}

func NewMessagePromptTemplate(msgs []message.Message) []MessageFormatter {
	mfmts := make([]MessageFormatter, 0)

	for _, m := range msgs {
		switch m.Role {
		case message.ChatMessageRoleSystem:
			mfmts = append(mfmts, NewSystemMessagePromptTemplate(m.Content))
		case message.ChatMessageRoleUser:
			mfmts = append(mfmts, NewUserMessagePromptTemplate(m.Content))
		case message.ChatMessageRoleAssistant:
			mfmts = append(mfmts, NewAssistantMessagePromptTemplate(m.Content))
		}
	}

	return mfmts
}

// SystemMessagePromptTemplate is a message formatter that returns a system message.
type SystemMessagePromptTemplate struct {
	Prompt PromptTemplate
}

func NewSystemMessagePromptTemplate(template string, opts ...Option) *SystemMessagePromptTemplate {
	return &SystemMessagePromptTemplate{
		Prompt: NewPromptTemplate(template, opts...),
	}
}

func (p SystemMessagePromptTemplate) FormatMessages(values map[string]any) ([]message.Message, error) {
	text, err := p.Prompt.Format(values)
	return []message.Message{message.NewSystemChatMessage(text)}, err
}

func (p SystemMessagePromptTemplate) GetInputVariables() []string {
	return p.Prompt.InputVariables
}

// AssistantMessagePromptTemplate is a message formatter that returns a assistant message.
type AssistantMessagePromptTemplate struct {
	Prompt PromptTemplate
}

func NewAssistantMessagePromptTemplate(template string, opts ...Option) *AssistantMessagePromptTemplate {
	return &AssistantMessagePromptTemplate{
		Prompt: NewPromptTemplate(template, opts...),
	}
}

func (p AssistantMessagePromptTemplate) FormatMessages(values map[string]any) ([]message.Message, error) {
	text, err := p.Prompt.Format(values)
	return []message.Message{message.NewAssistantChatMessage(text)}, err
}

func (p AssistantMessagePromptTemplate) GetInputVariables() []string {
	return p.Prompt.InputVariables
}

// UserMessagePromptTemplate is a message formatter that returns a user message.
type UserMessagePromptTemplate struct {
	Prompt PromptTemplate
}

func NewUserMessagePromptTemplate(template string, opts ...Option) *UserMessagePromptTemplate {
	return &UserMessagePromptTemplate{
		Prompt: NewPromptTemplate(template, opts...),
	}
}

func (p UserMessagePromptTemplate) FormatMessages(values map[string]any) ([]message.Message, error) {
	text, err := p.Prompt.Format(values)
	return []message.Message{message.NewUserChatMessage(text)}, err
}

func (p UserMessagePromptTemplate) GetInputVariables() []string {
	return p.Prompt.InputVariables
}
