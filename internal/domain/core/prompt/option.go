package prompt

type Option func(*Options)

type Options struct {
	inputKeys     []string
	partialValues map[string]any
	messages      []MessageFormatter
}

func newOptions() *Options {
	return &Options{
		inputKeys:     make([]string, 0),
		partialValues: make(map[string]any),
		messages:      make([]MessageFormatter, 0),
	}
}

func WithInputKeys(keys ...string) Option {
	return func(o *Options) {
		o.inputKeys = append(o.inputKeys, keys...)
	}
}

func WithPartialValues(values map[string]any) Option {
	return func(o *Options) {
		for k, v := range values {
			o.partialValues[k] = v
		}
	}
}

func WithPartialValue(k string, v any) Option {
	return func(o *Options) {
		o.partialValues[k] = v
	}
}

func WithMessages(msg ...MessageFormatter) Option {
	return func(o *Options) {
		o.messages = append(o.messages, msg...)
	}
}
