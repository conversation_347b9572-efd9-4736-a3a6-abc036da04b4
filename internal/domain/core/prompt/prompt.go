package prompt

import (
	"secwalk/internal/domain/core/message"
)

// Formatter is an interface for formatting a map of values into a string.
type Formatter interface {
	Format(values map[string]any) (string, error)
}

// Formatter is an interface for formatting a map of values into a list of messages.
type MessageFormatter interface {
	FormatMessages(values map[string]any) ([]message.Message, error)
	GetInputVariables() []string
}

// FormatPrompter is an interface for formatting a map of values into a prompt.
type FormatPrompter interface {
	FormatPrompt(values map[string]any) (message.PromptValue, error)
	GetInputVariables() []string
}
