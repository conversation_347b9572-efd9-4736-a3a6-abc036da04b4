package prompt

import (
	"secwalk/internal/domain/core/message"
)

var _ message.PromptValue = TextPromptValue("")

// TextPromptValue is a prompt value that is a string.
type TextPromptValue string

func (v TextPromptValue) String() string {
	return string(v)
}

// Messages returns a single-element Message slice.
func (v TextPromptValue) Messages() []message.Message {
	return []message.Message{
		message.NewUserChatMessage(string(v)),
	}
}
