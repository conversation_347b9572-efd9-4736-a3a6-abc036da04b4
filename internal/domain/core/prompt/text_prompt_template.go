package prompt

import (
	"fmt"
	"secwalk/internal/domain/core/message"
	"secwalk/internal/domain/core/template"
)

var (
	_ Formatter      = PromptTemplate{}
	_ FormatPrompter = PromptTemplate{}
)

// PromptTemplate contains common fields for all prompt templates.
type PromptTemplate struct {
	// Template is the prompt template.
	Template string

	// A list of variable names the prompt template expects.
	InputVariables []string

	// OutputParser is a function that parses the output of the prompt template.
	// OutputParser parser.OutputParser[any]

	// PartialVariables represents a map of variable names to values or functions that return values.
	// If the value is a function, it will be called when the prompt template is rendered.
	PartialVariables map[string]any
}

// NewPromptTemplate returns a new prompt template.
func NewPromptTemplate(tmpl string, opts ...Option) PromptTemplate {
	opt := newOptions()
	for _, o := range opts {
		o(opt)
	}

	return PromptTemplate{
		Template:         tmpl,
		InputVariables:   opt.inputKeys,
		PartialVariables: opt.partialValues,
	}
}

// Format formats the prompt template and returns a string value.
func (p PromptTemplate) Format(values map[string]any) (string, error) {
	resolvedValues, err := resolvePartialValues(p.PartialVariables, values)
	if err != nil {
		return "", err
	}

	return template.RenderTemplate(p.Template, resolvedValues)
}

// FormatPrompt formats the prompt template and returns a string prompt value.
func (p PromptTemplate) FormatPrompt(values map[string]any) (message.PromptValue, error) {
	f, err := p.Format(values)
	if err != nil {
		return nil, err
	}

	return TextPromptValue(f), nil
}

// GetInputVariables returns the input variables the prompt expect.
func (p PromptTemplate) GetInputVariables() []string {
	return p.InputVariables
}

func resolvePartialValues(partialValues map[string]any, values map[string]any) (map[string]any, error) {
	resolvedValues := make(map[string]any)
	for variable, value := range partialValues {
		switch value := value.(type) {
		case string:
			resolvedValues[variable] = value
		case func() string:
			resolvedValues[variable] = value()
		default:
			return nil, fmt.Errorf("invalid partial variable type: %v", variable)
		}
	}
	for variable, value := range values {
		resolvedValues[variable] = value
	}
	return resolvedValues, nil
}
