package repo

import (
	"context"
	"errors"
	"secwalk/internal/domain/core/message"
	"secwalk/internal/domain/repo/ent"
	ses "secwalk/internal/domain/repo/ent/session"
	"secwalk/pkg/random"
	"sync"
	"time"

	"github.com/hashicorp/golang-lru/v2/expirable"
)

type SessionRepo struct {
	mutex  sync.Mutex
	cache  *expirable.LRU[string, *message.Session]
	client *ent.Client
}

func NewSessionRepo(client *ent.Client) *SessionRepo {
	return &SessionRepo{
		mutex:  sync.Mutex{},
		cache:  expirable.NewLRU[string, *message.Session](500, nil, time.Minute*10),
		client: client,
	}
}

func (r *SessionRepo) Query(ctx context.Context, id string, check bool) (*message.Session, error) {
	r.mutex.Lock()
	defer r.mutex.Unlock()

	value, ok := r.cache.Get(id)
	if ok {
		return value, nil
	}

	session := &message.Session{}
	res, err := r.client.Session.Query().Where(ses.ID(id)).First(ctx)
	if err != nil {
		if check {
			return nil, errors.New("session not found")
		}

		if len(id) > 0 {
			session = &message.Session{
				ID:       id,
				Messages: make([]message.Message, 0),
			}
		} else {
			session = &message.Session{
				ID:       random.RandomID(),
				Messages: make([]message.Message, 0),
			}
		}

		now := time.Now().Format(time.RFC3339)
		_, err := r.client.Session.Create().
			SetID(session.ID).
			SetCreateAt(now).
			SetUpdateAt(now).
			SetMessages(session.Messages).Save(ctx)
		return session, err
	}

	return &message.Session{
		ID:       res.ID,
		Messages: res.Messages,
	}, nil
}

func (r *SessionRepo) Create(ctx context.Context, session *message.Session) error {
	r.mutex.Lock()
	defer r.mutex.Unlock()

	now := time.Now().Format(time.RFC3339)
	_, err := r.client.Session.Create().
		SetID(session.ID).
		SetCreateAt(now).
		SetUpdateAt(now).
		SetMessages(session.Messages).Save(ctx)

	r.cache.Add(session.ID, session)
	return err
}

func (r *SessionRepo) Update(ctx context.Context, session *message.Session) error {
	r.mutex.Lock()
	defer r.mutex.Unlock()

	_, err := r.client.Session.UpdateOneID(session.ID).
		SetUpdateAt(time.Now().Format(time.RFC3339)).
		SetMessages(session.Messages).Save(ctx)
	if err != nil {
		return err
	}

	r.cache.Add(session.ID, session)
	return nil
}
