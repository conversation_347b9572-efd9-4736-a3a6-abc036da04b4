package repo

import (
	"context"
	"secwalk/internal/domain/core/schema"
	"secwalk/internal/domain/repo/ent"
	"time"
)

type McpRepo struct {
	cli *ent.Client
}

func NewMCPRepo(client *ent.Client) *McpRepo {
	return &McpRepo{cli: client}
}

func (r *McpRepo) Query(ctx context.Context, id string) (string, schema.ServerEntry, error) {
	res, err := r.cli.Mcp.Get(ctx, id)
	if err != nil {
		return "", schema.ServerEntry{}, err
	}

	return res.Name, schema.ServerEntry{
		ID:      res.ID,
		Type:    res.Type,
		Command: res.Command,
		Args:    res.Args,
		Env:     res.Env,
		URL:     res.URL,
		Headers: res.Headers,
		Timeout: time.Duration(res.Timeout),
	}, nil
}

func (r *McpRepo) Upsert(ctx context.Context, msc schema.ServersFile) error {
	for k, c := range msc.McpServers {
		_, err := r.cli.Mcp.Get(ctx, c.ID)
		if err != nil {
			_, err = r.cli.Mcp.Create().
				SetID(c.ID).
				SetType(c.Type).
				SetName(k).
				SetCommand(c.Command).
				SetArgs(c.Args).
				SetEnv(c.Env).
				SetURL(c.URL).
				SetHeaders(c.Headers).
				SetTimeout(int64(c.Timeout)).
				Save(ctx)
			return err
		}

		_, err = r.cli.Mcp.UpdateOneID(c.ID).
			SetType(c.Type).
			SetName(k).
			SetCommand(c.Command).
			SetArgs(c.Args).
			SetEnv(c.Env).
			SetURL(c.URL).
			SetHeaders(c.Headers).
			SetTimeout(int64(c.Timeout)).
			Save(ctx)
		return err
	}
	return nil
}

func (r *McpRepo) Delete(ctx context.Context, id string) error {
	return r.cli.Mcp.DeleteOneID(id).Exec(ctx)
}
