// Code generated by ent, DO NOT EDIT.

package ent

import (
	"secwalk/internal/domain/repo/ent/agent"
	"secwalk/internal/domain/repo/ent/mcp"
	"secwalk/internal/domain/repo/ent/schema"
	"secwalk/internal/domain/repo/ent/session"
	"secwalk/internal/domain/repo/ent/sign"
	"secwalk/internal/domain/repo/ent/term"
	"secwalk/internal/domain/repo/ent/toolkit"
	"secwalk/internal/domain/repo/ent/trait"
)

// The init function reads all schema descriptors with runtime code
// (default values, validators, hooks and policies) and stitches it
// to their package variables.
func init() {
	agentFields := schema.Agent{}.Fields()
	_ = agentFields
	// agentDescType is the schema descriptor for type field.
	agentDescType := agentFields[1].Descriptor()
	// agent.DefaultType holds the default value on creation for the type field.
	agent.DefaultType = agentDescType.Default.(int)
	// agentDescConfig is the schema descriptor for config field.
	agentDescConfig := agentFields[4].Descriptor()
	// agent.ConfigValidator is a validator for the "config" field. It is called by the builders before save.
	agent.ConfigValidator = agentDescConfig.Validators[0].(func(string) error)
	// agentDescID is the schema descriptor for id field.
	agentDescID := agentFields[0].Descriptor()
	// agent.IDValidator is a validator for the "id" field. It is called by the builders before save.
	agent.IDValidator = agentDescID.Validators[0].(func(string) error)
	mcpFields := schema.Mcp{}.Fields()
	_ = mcpFields
	// mcpDescType is the schema descriptor for type field.
	mcpDescType := mcpFields[1].Descriptor()
	// mcp.TypeValidator is a validator for the "type" field. It is called by the builders before save.
	mcp.TypeValidator = mcpDescType.Validators[0].(func(string) error)
	// mcpDescName is the schema descriptor for name field.
	mcpDescName := mcpFields[2].Descriptor()
	// mcp.NameValidator is a validator for the "name" field. It is called by the builders before save.
	mcp.NameValidator = mcpDescName.Validators[0].(func(string) error)
	// mcpDescID is the schema descriptor for id field.
	mcpDescID := mcpFields[0].Descriptor()
	// mcp.IDValidator is a validator for the "id" field. It is called by the builders before save.
	mcp.IDValidator = mcpDescID.Validators[0].(func(string) error)
	sessionFields := schema.Session{}.Fields()
	_ = sessionFields
	// sessionDescID is the schema descriptor for id field.
	sessionDescID := sessionFields[0].Descriptor()
	// session.IDValidator is a validator for the "id" field. It is called by the builders before save.
	session.IDValidator = sessionDescID.Validators[0].(func(string) error)
	signFields := schema.Sign{}.Fields()
	_ = signFields
	// signDescName is the schema descriptor for name field.
	signDescName := signFields[0].Descriptor()
	// sign.NameValidator is a validator for the "name" field. It is called by the builders before save.
	sign.NameValidator = signDescName.Validators[0].(func(string) error)
	// signDescSecret is the schema descriptor for secret field.
	signDescSecret := signFields[1].Descriptor()
	// sign.SecretValidator is a validator for the "secret" field. It is called by the builders before save.
	sign.SecretValidator = signDescSecret.Validators[0].(func(string) error)
	// signDescApikey is the schema descriptor for apikey field.
	signDescApikey := signFields[2].Descriptor()
	// sign.ApikeyValidator is a validator for the "apikey" field. It is called by the builders before save.
	sign.ApikeyValidator = signDescApikey.Validators[0].(func(string) error)
	termFields := schema.Term{}.Fields()
	_ = termFields
	// termDescAid is the schema descriptor for aid field.
	termDescAid := termFields[2].Descriptor()
	// term.AidValidator is a validator for the "aid" field. It is called by the builders before save.
	term.AidValidator = termDescAid.Validators[0].(func(string) error)
	toolkitFields := schema.Toolkit{}.Fields()
	_ = toolkitFields
	// toolkitDescType is the schema descriptor for type field.
	toolkitDescType := toolkitFields[1].Descriptor()
	// toolkit.DefaultType holds the default value on creation for the type field.
	toolkit.DefaultType = toolkitDescType.Default.(string)
	// toolkitDescConfig is the schema descriptor for config field.
	toolkitDescConfig := toolkitFields[3].Descriptor()
	// toolkit.ConfigValidator is a validator for the "config" field. It is called by the builders before save.
	toolkit.ConfigValidator = toolkitDescConfig.Validators[0].(func(string) error)
	// toolkitDescID is the schema descriptor for id field.
	toolkitDescID := toolkitFields[0].Descriptor()
	// toolkit.IDValidator is a validator for the "id" field. It is called by the builders before save.
	toolkit.IDValidator = toolkitDescID.Validators[0].(func(string) error)
	traitFields := schema.Trait{}.Fields()
	_ = traitFields
	// traitDescText is the schema descriptor for text field.
	traitDescText := traitFields[0].Descriptor()
	// trait.TextValidator is a validator for the "text" field. It is called by the builders before save.
	trait.TextValidator = traitDescText.Validators[0].(func(string) error)
	// traitDescType is the schema descriptor for type field.
	traitDescType := traitFields[1].Descriptor()
	// trait.TypeValidator is a validator for the "type" field. It is called by the builders before save.
	trait.TypeValidator = traitDescType.Validators[0].(func(string) error)
	// traitDescLevel is the schema descriptor for level field.
	traitDescLevel := traitFields[2].Descriptor()
	// trait.DefaultLevel holds the default value on creation for the level field.
	trait.DefaultLevel = traitDescLevel.Default.(int)
}
