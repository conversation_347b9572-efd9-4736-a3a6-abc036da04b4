// Code generated by ent, DO NOT EDIT.

package ent

import (
	"fmt"
	"secwalk/internal/domain/repo/ent/sign"
	"strings"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

// Sign is the model entity for the Sign schema.
type Sign struct {
	config `json:"-"`
	// ID of the ent.
	ID int `json:"id,omitempty"`
	// Name holds the value of the "name" field.
	Name string `json:"name,omitempty"`
	// Secret holds the value of the "secret" field.
	Secret string `json:"secret,omitempty"`
	// Apikey holds the value of the "apikey" field.
	Apikey       string `json:"apikey,omitempty"`
	selectValues sql.SelectValues
}

// scanValues returns the types for scanning values from sql.Rows.
func (*Sign) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case sign.FieldID:
			values[i] = new(sql.NullInt64)
		case sign.FieldName, sign.FieldSecret, sign.FieldApikey:
			values[i] = new(sql.NullString)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the Sign fields.
func (s *Sign) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case sign.FieldID:
			value, ok := values[i].(*sql.NullInt64)
			if !ok {
				return fmt.Errorf("unexpected type %T for field id", value)
			}
			s.ID = int(value.Int64)
		case sign.FieldName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field name", values[i])
			} else if value.Valid {
				s.Name = value.String
			}
		case sign.FieldSecret:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field secret", values[i])
			} else if value.Valid {
				s.Secret = value.String
			}
		case sign.FieldApikey:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field apikey", values[i])
			} else if value.Valid {
				s.Apikey = value.String
			}
		default:
			s.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the Sign.
// This includes values selected through modifiers, order, etc.
func (s *Sign) Value(name string) (ent.Value, error) {
	return s.selectValues.Get(name)
}

// Update returns a builder for updating this Sign.
// Note that you need to call Sign.Unwrap() before calling this method if this Sign
// was returned from a transaction, and the transaction was committed or rolled back.
func (s *Sign) Update() *SignUpdateOne {
	return NewSignClient(s.config).UpdateOne(s)
}

// Unwrap unwraps the Sign entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (s *Sign) Unwrap() *Sign {
	_tx, ok := s.config.driver.(*txDriver)
	if !ok {
		panic("ent: Sign is not a transactional entity")
	}
	s.config.driver = _tx.drv
	return s
}

// String implements the fmt.Stringer.
func (s *Sign) String() string {
	var builder strings.Builder
	builder.WriteString("Sign(")
	builder.WriteString(fmt.Sprintf("id=%v, ", s.ID))
	builder.WriteString("name=")
	builder.WriteString(s.Name)
	builder.WriteString(", ")
	builder.WriteString("secret=")
	builder.WriteString(s.Secret)
	builder.WriteString(", ")
	builder.WriteString("apikey=")
	builder.WriteString(s.Apikey)
	builder.WriteByte(')')
	return builder.String()
}

// Signs is a parsable slice of Sign.
type Signs []*Sign
