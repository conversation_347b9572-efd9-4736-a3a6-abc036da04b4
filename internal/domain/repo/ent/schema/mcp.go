package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/schema/field"
)

// Mc<PERSON> holds the schema definition for the Mcp entity.
type Mcp struct {
	ent.Schema
}

// Fields of the Mcp.
func (Mcp) Fields() []ent.Field {
	return []ent.Field{
		field.String("id").Unique().NotEmpty(),
		field.String("type").NotEmpty(),
		field.String("name").NotEmpty(),
		field.String("command").Optional(),
		field.JSON("args", []string{}).Optional(),
		field.JSON("env", map[string]string{}).Optional(),
		field.String("url").Optional(),
		field.JSON("headers", map[string]string{}).Optional(),
		field.Int64("timeout"),
	}
}

// Edges of the Mcp.
func (Mcp) Edges() []ent.Edge {
	return nil
}
