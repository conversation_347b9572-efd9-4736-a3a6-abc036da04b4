package schema

import (
	"secwalk/internal/domain/core/schema"

	"entgo.io/ent"
	"entgo.io/ent/schema/field"
)

// Toolkit holds the schema definition for the Toolkit entity.
type Toolkit struct {
	ent.Schema
}

// Fields of the Toolkit.
func (Toolkit) Fields() []ent.Field {
	return []ent.Field{
		field.String("id").NotEmpty().Unique(),
		field.String("type").Default(schema.ToolkitTypeCustom),
		field.Strings("tools"),
		field.Text("config").NotEmpty(),
	}
}

// Edges of the Toolkit.
func (Toolkit) Edges() []ent.Edge {
	return nil
}
