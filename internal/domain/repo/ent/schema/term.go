package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/schema/field"
	"entgo.io/ent/schema/index"
)

// Term holds the schema definition for the Term entity.
type Term struct {
	ent.Schema
}

// Fields of the Term.
func (Term) Fields() []ent.Field {
	return []ent.Field{
		field.String("create_at"),
		field.String("update_at"),
		field.String("aid").NotEmpty(),
		field.String("org"),
		field.String("sid"),
		field.JSON("content", map[string]any{}),
	}
}

func (Term) Indexes() []ent.Index {
	return []ent.Index{
		index.Fields("aid", "org"),
	}
}

// Edges of the Term.
func (Term) Edges() []ent.Edge {
	return nil
}
