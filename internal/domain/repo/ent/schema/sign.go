package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/schema/field"
)

// Sign holds the schema definition for the Sign entity.
type Sign struct {
	ent.Schema
}

// Fields of the Sign.
func (Sign) Fields() []ent.Field {
	return []ent.Field{
		field.String("name").NotEmpty().Unique(),
		field.String("secret").NotEmpty().Unique(),
		field.String("apikey").NotEmpty().Unique(),
	}
}

// Edges of the Sign.
func (Sign) Edges() []ent.Edge {
	return nil
}
