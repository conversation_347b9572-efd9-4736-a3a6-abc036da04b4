package schema

import (
	"secwalk/internal/domain/core/message"

	"entgo.io/ent"
	"entgo.io/ent/schema/field"
	"entgo.io/ent/schema/index"
)

// Session holds the schema definition for the Session entity.
type Session struct {
	ent.Schema
}

// Fields of the Session.
func (Session) Fields() []ent.Field {
	return []ent.Field{
		field.String("id").NotEmpty().Unique(),
		field.JSON("messages", []message.Message{}),
		field.String("create_at"),
		field.String("update_at"),
	}
}

func (Session) Indexes() []ent.Index {
	return []ent.Index{
		index.Fields("id").Unique(),
	}
}

// Edges of the Session.
func (Session) Edges() []ent.Edge {
	return nil
}
