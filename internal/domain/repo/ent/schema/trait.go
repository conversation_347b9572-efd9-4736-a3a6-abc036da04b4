package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/schema/field"
)

// Trait holds the schema definition for the Trait entity.
type Trait struct {
	ent.Schema
}

// Fields of the Trait.
func (Trait) Fields() []ent.Field {
	return []ent.Field{
		field.String("text").NotEmpty().Unique(),
		field.String("type").NotEmpty(),
		field.Int("level").Default(0),
	}
}

// Edges of the Trait.
func (Trait) Edges() []ent.Edge {
	return nil
}
