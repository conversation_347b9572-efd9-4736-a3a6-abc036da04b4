package schema

import (
	"secwalk/internal/domain/core/schema"

	"entgo.io/ent"
	"entgo.io/ent/schema/field"
	"entgo.io/ent/schema/index"
)

// Agent holds the schema definition for the Agent entity.
type Agent struct {
	ent.Schema
}

// Fields of the Agent.
func (Agent) Fields() []ent.Field {
	return []ent.Field{
		field.String("id").NotEmpty().Unique(),
		field.Int("type").Default(schema.TypeUser),
		field.String("version"),
		field.String("eng_version"),
		field.Text("config").NotEmpty(),
		field.Text("extension").Optional(),
	}
}

func (Agent) Indexes() []ent.Index {
	return []ent.Index{
		index.Fields("id").Unique(),
	}
}

// Edges of the Agent.
func (Agent) Edges() []ent.Edge {
	return nil
}
