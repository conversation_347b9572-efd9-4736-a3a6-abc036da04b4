// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"secwalk/internal/domain/repo/ent/trait"

	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// TraitCreate is the builder for creating a Trait entity.
type TraitCreate struct {
	config
	mutation *TraitMutation
	hooks    []Hook
}

// SetText sets the "text" field.
func (tc *TraitCreate) SetText(s string) *TraitCreate {
	tc.mutation.SetText(s)
	return tc
}

// SetType sets the "type" field.
func (tc *TraitCreate) SetType(s string) *TraitCreate {
	tc.mutation.SetType(s)
	return tc
}

// SetLevel sets the "level" field.
func (tc *TraitCreate) SetLevel(i int) *TraitCreate {
	tc.mutation.SetLevel(i)
	return tc
}

// SetNillableLevel sets the "level" field if the given value is not nil.
func (tc *TraitCreate) SetNillableLevel(i *int) *TraitCreate {
	if i != nil {
		tc.SetLevel(*i)
	}
	return tc
}

// Mutation returns the TraitMutation object of the builder.
func (tc *TraitCreate) Mutation() *TraitMutation {
	return tc.mutation
}

// Save creates the Trait in the database.
func (tc *TraitCreate) Save(ctx context.Context) (*Trait, error) {
	tc.defaults()
	return withHooks(ctx, tc.sqlSave, tc.mutation, tc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (tc *TraitCreate) SaveX(ctx context.Context) *Trait {
	v, err := tc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (tc *TraitCreate) Exec(ctx context.Context) error {
	_, err := tc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (tc *TraitCreate) ExecX(ctx context.Context) {
	if err := tc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (tc *TraitCreate) defaults() {
	if _, ok := tc.mutation.Level(); !ok {
		v := trait.DefaultLevel
		tc.mutation.SetLevel(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (tc *TraitCreate) check() error {
	if _, ok := tc.mutation.Text(); !ok {
		return &ValidationError{Name: "text", err: errors.New(`ent: missing required field "Trait.text"`)}
	}
	if v, ok := tc.mutation.Text(); ok {
		if err := trait.TextValidator(v); err != nil {
			return &ValidationError{Name: "text", err: fmt.Errorf(`ent: validator failed for field "Trait.text": %w`, err)}
		}
	}
	if _, ok := tc.mutation.GetType(); !ok {
		return &ValidationError{Name: "type", err: errors.New(`ent: missing required field "Trait.type"`)}
	}
	if v, ok := tc.mutation.GetType(); ok {
		if err := trait.TypeValidator(v); err != nil {
			return &ValidationError{Name: "type", err: fmt.Errorf(`ent: validator failed for field "Trait.type": %w`, err)}
		}
	}
	if _, ok := tc.mutation.Level(); !ok {
		return &ValidationError{Name: "level", err: errors.New(`ent: missing required field "Trait.level"`)}
	}
	return nil
}

func (tc *TraitCreate) sqlSave(ctx context.Context) (*Trait, error) {
	if err := tc.check(); err != nil {
		return nil, err
	}
	_node, _spec := tc.createSpec()
	if err := sqlgraph.CreateNode(ctx, tc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	id := _spec.ID.Value.(int64)
	_node.ID = int(id)
	tc.mutation.id = &_node.ID
	tc.mutation.done = true
	return _node, nil
}

func (tc *TraitCreate) createSpec() (*Trait, *sqlgraph.CreateSpec) {
	var (
		_node = &Trait{config: tc.config}
		_spec = sqlgraph.NewCreateSpec(trait.Table, sqlgraph.NewFieldSpec(trait.FieldID, field.TypeInt))
	)
	if value, ok := tc.mutation.Text(); ok {
		_spec.SetField(trait.FieldText, field.TypeString, value)
		_node.Text = value
	}
	if value, ok := tc.mutation.GetType(); ok {
		_spec.SetField(trait.FieldType, field.TypeString, value)
		_node.Type = value
	}
	if value, ok := tc.mutation.Level(); ok {
		_spec.SetField(trait.FieldLevel, field.TypeInt, value)
		_node.Level = value
	}
	return _node, _spec
}

// TraitCreateBulk is the builder for creating many Trait entities in bulk.
type TraitCreateBulk struct {
	config
	err      error
	builders []*TraitCreate
}

// Save creates the Trait entities in the database.
func (tcb *TraitCreateBulk) Save(ctx context.Context) ([]*Trait, error) {
	if tcb.err != nil {
		return nil, tcb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(tcb.builders))
	nodes := make([]*Trait, len(tcb.builders))
	mutators := make([]Mutator, len(tcb.builders))
	for i := range tcb.builders {
		func(i int, root context.Context) {
			builder := tcb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*TraitMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, tcb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, tcb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = int(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, tcb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (tcb *TraitCreateBulk) SaveX(ctx context.Context) []*Trait {
	v, err := tcb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (tcb *TraitCreateBulk) Exec(ctx context.Context) error {
	_, err := tcb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (tcb *TraitCreateBulk) ExecX(ctx context.Context) {
	if err := tcb.Exec(ctx); err != nil {
		panic(err)
	}
}
