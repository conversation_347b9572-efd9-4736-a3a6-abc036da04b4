// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"secwalk/internal/domain/core/message"
	"secwalk/internal/domain/repo/ent/agent"
	"secwalk/internal/domain/repo/ent/mcp"
	"secwalk/internal/domain/repo/ent/predicate"
	"secwalk/internal/domain/repo/ent/session"
	"secwalk/internal/domain/repo/ent/sign"
	"secwalk/internal/domain/repo/ent/term"
	"secwalk/internal/domain/repo/ent/toolkit"
	"secwalk/internal/domain/repo/ent/trait"
	"sync"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

const (
	// Operation types.
	OpCreate    = ent.OpCreate
	OpDelete    = ent.OpDelete
	OpDeleteOne = ent.OpDeleteOne
	OpUpdate    = ent.OpUpdate
	OpUpdateOne = ent.OpUpdateOne

	// Node types.
	TypeAgent   = "Agent"
	TypeMcp     = "Mcp"
	TypeSession = "Session"
	TypeSign    = "Sign"
	TypeTerm    = "Term"
	TypeToolkit = "Toolkit"
	TypeTrait   = "Trait"
)

// AgentMutation represents an operation that mutates the Agent nodes in the graph.
type AgentMutation struct {
	config
	op            Op
	typ           string
	id            *string
	_type         *int
	add_type      *int
	version       *string
	eng_version   *string
	_config       *string
	extension     *string
	clearedFields map[string]struct{}
	done          bool
	oldValue      func(context.Context) (*Agent, error)
	predicates    []predicate.Agent
}

var _ ent.Mutation = (*AgentMutation)(nil)

// agentOption allows management of the mutation configuration using functional options.
type agentOption func(*AgentMutation)

// newAgentMutation creates new mutation for the Agent entity.
func newAgentMutation(c config, op Op, opts ...agentOption) *AgentMutation {
	m := &AgentMutation{
		config:        c,
		op:            op,
		typ:           TypeAgent,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withAgentID sets the ID field of the mutation.
func withAgentID(id string) agentOption {
	return func(m *AgentMutation) {
		var (
			err   error
			once  sync.Once
			value *Agent
		)
		m.oldValue = func(ctx context.Context) (*Agent, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().Agent.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withAgent sets the old Agent of the mutation.
func withAgent(node *Agent) agentOption {
	return func(m *AgentMutation) {
		m.oldValue = func(context.Context) (*Agent, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m AgentMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m AgentMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of Agent entities.
func (m *AgentMutation) SetID(id string) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *AgentMutation) ID() (id string, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *AgentMutation) IDs(ctx context.Context) ([]string, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []string{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().Agent.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetType sets the "type" field.
func (m *AgentMutation) SetType(i int) {
	m._type = &i
	m.add_type = nil
}

// GetType returns the value of the "type" field in the mutation.
func (m *AgentMutation) GetType() (r int, exists bool) {
	v := m._type
	if v == nil {
		return
	}
	return *v, true
}

// OldType returns the old "type" field's value of the Agent entity.
// If the Agent object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AgentMutation) OldType(ctx context.Context) (v int, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldType is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldType requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldType: %w", err)
	}
	return oldValue.Type, nil
}

// AddType adds i to the "type" field.
func (m *AgentMutation) AddType(i int) {
	if m.add_type != nil {
		*m.add_type += i
	} else {
		m.add_type = &i
	}
}

// AddedType returns the value that was added to the "type" field in this mutation.
func (m *AgentMutation) AddedType() (r int, exists bool) {
	v := m.add_type
	if v == nil {
		return
	}
	return *v, true
}

// ResetType resets all changes to the "type" field.
func (m *AgentMutation) ResetType() {
	m._type = nil
	m.add_type = nil
}

// SetVersion sets the "version" field.
func (m *AgentMutation) SetVersion(s string) {
	m.version = &s
}

// Version returns the value of the "version" field in the mutation.
func (m *AgentMutation) Version() (r string, exists bool) {
	v := m.version
	if v == nil {
		return
	}
	return *v, true
}

// OldVersion returns the old "version" field's value of the Agent entity.
// If the Agent object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AgentMutation) OldVersion(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldVersion is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldVersion requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldVersion: %w", err)
	}
	return oldValue.Version, nil
}

// ResetVersion resets all changes to the "version" field.
func (m *AgentMutation) ResetVersion() {
	m.version = nil
}

// SetEngVersion sets the "eng_version" field.
func (m *AgentMutation) SetEngVersion(s string) {
	m.eng_version = &s
}

// EngVersion returns the value of the "eng_version" field in the mutation.
func (m *AgentMutation) EngVersion() (r string, exists bool) {
	v := m.eng_version
	if v == nil {
		return
	}
	return *v, true
}

// OldEngVersion returns the old "eng_version" field's value of the Agent entity.
// If the Agent object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AgentMutation) OldEngVersion(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldEngVersion is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldEngVersion requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldEngVersion: %w", err)
	}
	return oldValue.EngVersion, nil
}

// ResetEngVersion resets all changes to the "eng_version" field.
func (m *AgentMutation) ResetEngVersion() {
	m.eng_version = nil
}

// SetConfig sets the "config" field.
func (m *AgentMutation) SetConfig(s string) {
	m._config = &s
}

// Config returns the value of the "config" field in the mutation.
func (m *AgentMutation) Config() (r string, exists bool) {
	v := m._config
	if v == nil {
		return
	}
	return *v, true
}

// OldConfig returns the old "config" field's value of the Agent entity.
// If the Agent object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AgentMutation) OldConfig(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldConfig is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldConfig requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldConfig: %w", err)
	}
	return oldValue.Config, nil
}

// ResetConfig resets all changes to the "config" field.
func (m *AgentMutation) ResetConfig() {
	m._config = nil
}

// SetExtension sets the "extension" field.
func (m *AgentMutation) SetExtension(s string) {
	m.extension = &s
}

// Extension returns the value of the "extension" field in the mutation.
func (m *AgentMutation) Extension() (r string, exists bool) {
	v := m.extension
	if v == nil {
		return
	}
	return *v, true
}

// OldExtension returns the old "extension" field's value of the Agent entity.
// If the Agent object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AgentMutation) OldExtension(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldExtension is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldExtension requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldExtension: %w", err)
	}
	return oldValue.Extension, nil
}

// ClearExtension clears the value of the "extension" field.
func (m *AgentMutation) ClearExtension() {
	m.extension = nil
	m.clearedFields[agent.FieldExtension] = struct{}{}
}

// ExtensionCleared returns if the "extension" field was cleared in this mutation.
func (m *AgentMutation) ExtensionCleared() bool {
	_, ok := m.clearedFields[agent.FieldExtension]
	return ok
}

// ResetExtension resets all changes to the "extension" field.
func (m *AgentMutation) ResetExtension() {
	m.extension = nil
	delete(m.clearedFields, agent.FieldExtension)
}

// Where appends a list predicates to the AgentMutation builder.
func (m *AgentMutation) Where(ps ...predicate.Agent) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the AgentMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *AgentMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.Agent, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *AgentMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *AgentMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (Agent).
func (m *AgentMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *AgentMutation) Fields() []string {
	fields := make([]string, 0, 5)
	if m._type != nil {
		fields = append(fields, agent.FieldType)
	}
	if m.version != nil {
		fields = append(fields, agent.FieldVersion)
	}
	if m.eng_version != nil {
		fields = append(fields, agent.FieldEngVersion)
	}
	if m._config != nil {
		fields = append(fields, agent.FieldConfig)
	}
	if m.extension != nil {
		fields = append(fields, agent.FieldExtension)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *AgentMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case agent.FieldType:
		return m.GetType()
	case agent.FieldVersion:
		return m.Version()
	case agent.FieldEngVersion:
		return m.EngVersion()
	case agent.FieldConfig:
		return m.Config()
	case agent.FieldExtension:
		return m.Extension()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *AgentMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case agent.FieldType:
		return m.OldType(ctx)
	case agent.FieldVersion:
		return m.OldVersion(ctx)
	case agent.FieldEngVersion:
		return m.OldEngVersion(ctx)
	case agent.FieldConfig:
		return m.OldConfig(ctx)
	case agent.FieldExtension:
		return m.OldExtension(ctx)
	}
	return nil, fmt.Errorf("unknown Agent field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *AgentMutation) SetField(name string, value ent.Value) error {
	switch name {
	case agent.FieldType:
		v, ok := value.(int)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetType(v)
		return nil
	case agent.FieldVersion:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetVersion(v)
		return nil
	case agent.FieldEngVersion:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetEngVersion(v)
		return nil
	case agent.FieldConfig:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetConfig(v)
		return nil
	case agent.FieldExtension:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetExtension(v)
		return nil
	}
	return fmt.Errorf("unknown Agent field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *AgentMutation) AddedFields() []string {
	var fields []string
	if m.add_type != nil {
		fields = append(fields, agent.FieldType)
	}
	return fields
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *AgentMutation) AddedField(name string) (ent.Value, bool) {
	switch name {
	case agent.FieldType:
		return m.AddedType()
	}
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *AgentMutation) AddField(name string, value ent.Value) error {
	switch name {
	case agent.FieldType:
		v, ok := value.(int)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddType(v)
		return nil
	}
	return fmt.Errorf("unknown Agent numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *AgentMutation) ClearedFields() []string {
	var fields []string
	if m.FieldCleared(agent.FieldExtension) {
		fields = append(fields, agent.FieldExtension)
	}
	return fields
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *AgentMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *AgentMutation) ClearField(name string) error {
	switch name {
	case agent.FieldExtension:
		m.ClearExtension()
		return nil
	}
	return fmt.Errorf("unknown Agent nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *AgentMutation) ResetField(name string) error {
	switch name {
	case agent.FieldType:
		m.ResetType()
		return nil
	case agent.FieldVersion:
		m.ResetVersion()
		return nil
	case agent.FieldEngVersion:
		m.ResetEngVersion()
		return nil
	case agent.FieldConfig:
		m.ResetConfig()
		return nil
	case agent.FieldExtension:
		m.ResetExtension()
		return nil
	}
	return fmt.Errorf("unknown Agent field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *AgentMutation) AddedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *AgentMutation) AddedIDs(name string) []ent.Value {
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *AgentMutation) RemovedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *AgentMutation) RemovedIDs(name string) []ent.Value {
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *AgentMutation) ClearedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *AgentMutation) EdgeCleared(name string) bool {
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *AgentMutation) ClearEdge(name string) error {
	return fmt.Errorf("unknown Agent unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *AgentMutation) ResetEdge(name string) error {
	return fmt.Errorf("unknown Agent edge %s", name)
}

// McpMutation represents an operation that mutates the Mcp nodes in the graph.
type McpMutation struct {
	config
	op            Op
	typ           string
	id            *string
	_type         *string
	name          *string
	command       *string
	args          *[]string
	appendargs    []string
	env           *map[string]string
	url           *string
	headers       *map[string]string
	timeout       *int64
	addtimeout    *int64
	clearedFields map[string]struct{}
	done          bool
	oldValue      func(context.Context) (*Mcp, error)
	predicates    []predicate.Mcp
}

var _ ent.Mutation = (*McpMutation)(nil)

// mcpOption allows management of the mutation configuration using functional options.
type mcpOption func(*McpMutation)

// newMcpMutation creates new mutation for the Mcp entity.
func newMcpMutation(c config, op Op, opts ...mcpOption) *McpMutation {
	m := &McpMutation{
		config:        c,
		op:            op,
		typ:           TypeMcp,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withMcpID sets the ID field of the mutation.
func withMcpID(id string) mcpOption {
	return func(m *McpMutation) {
		var (
			err   error
			once  sync.Once
			value *Mcp
		)
		m.oldValue = func(ctx context.Context) (*Mcp, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().Mcp.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withMcp sets the old Mcp of the mutation.
func withMcp(node *Mcp) mcpOption {
	return func(m *McpMutation) {
		m.oldValue = func(context.Context) (*Mcp, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m McpMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m McpMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of Mcp entities.
func (m *McpMutation) SetID(id string) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *McpMutation) ID() (id string, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *McpMutation) IDs(ctx context.Context) ([]string, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []string{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().Mcp.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetType sets the "type" field.
func (m *McpMutation) SetType(s string) {
	m._type = &s
}

// GetType returns the value of the "type" field in the mutation.
func (m *McpMutation) GetType() (r string, exists bool) {
	v := m._type
	if v == nil {
		return
	}
	return *v, true
}

// OldType returns the old "type" field's value of the Mcp entity.
// If the Mcp object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *McpMutation) OldType(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldType is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldType requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldType: %w", err)
	}
	return oldValue.Type, nil
}

// ResetType resets all changes to the "type" field.
func (m *McpMutation) ResetType() {
	m._type = nil
}

// SetName sets the "name" field.
func (m *McpMutation) SetName(s string) {
	m.name = &s
}

// Name returns the value of the "name" field in the mutation.
func (m *McpMutation) Name() (r string, exists bool) {
	v := m.name
	if v == nil {
		return
	}
	return *v, true
}

// OldName returns the old "name" field's value of the Mcp entity.
// If the Mcp object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *McpMutation) OldName(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldName is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldName requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldName: %w", err)
	}
	return oldValue.Name, nil
}

// ResetName resets all changes to the "name" field.
func (m *McpMutation) ResetName() {
	m.name = nil
}

// SetCommand sets the "command" field.
func (m *McpMutation) SetCommand(s string) {
	m.command = &s
}

// Command returns the value of the "command" field in the mutation.
func (m *McpMutation) Command() (r string, exists bool) {
	v := m.command
	if v == nil {
		return
	}
	return *v, true
}

// OldCommand returns the old "command" field's value of the Mcp entity.
// If the Mcp object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *McpMutation) OldCommand(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCommand is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCommand requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCommand: %w", err)
	}
	return oldValue.Command, nil
}

// ClearCommand clears the value of the "command" field.
func (m *McpMutation) ClearCommand() {
	m.command = nil
	m.clearedFields[mcp.FieldCommand] = struct{}{}
}

// CommandCleared returns if the "command" field was cleared in this mutation.
func (m *McpMutation) CommandCleared() bool {
	_, ok := m.clearedFields[mcp.FieldCommand]
	return ok
}

// ResetCommand resets all changes to the "command" field.
func (m *McpMutation) ResetCommand() {
	m.command = nil
	delete(m.clearedFields, mcp.FieldCommand)
}

// SetArgs sets the "args" field.
func (m *McpMutation) SetArgs(s []string) {
	m.args = &s
	m.appendargs = nil
}

// Args returns the value of the "args" field in the mutation.
func (m *McpMutation) Args() (r []string, exists bool) {
	v := m.args
	if v == nil {
		return
	}
	return *v, true
}

// OldArgs returns the old "args" field's value of the Mcp entity.
// If the Mcp object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *McpMutation) OldArgs(ctx context.Context) (v []string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldArgs is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldArgs requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldArgs: %w", err)
	}
	return oldValue.Args, nil
}

// AppendArgs adds s to the "args" field.
func (m *McpMutation) AppendArgs(s []string) {
	m.appendargs = append(m.appendargs, s...)
}

// AppendedArgs returns the list of values that were appended to the "args" field in this mutation.
func (m *McpMutation) AppendedArgs() ([]string, bool) {
	if len(m.appendargs) == 0 {
		return nil, false
	}
	return m.appendargs, true
}

// ClearArgs clears the value of the "args" field.
func (m *McpMutation) ClearArgs() {
	m.args = nil
	m.appendargs = nil
	m.clearedFields[mcp.FieldArgs] = struct{}{}
}

// ArgsCleared returns if the "args" field was cleared in this mutation.
func (m *McpMutation) ArgsCleared() bool {
	_, ok := m.clearedFields[mcp.FieldArgs]
	return ok
}

// ResetArgs resets all changes to the "args" field.
func (m *McpMutation) ResetArgs() {
	m.args = nil
	m.appendargs = nil
	delete(m.clearedFields, mcp.FieldArgs)
}

// SetEnv sets the "env" field.
func (m *McpMutation) SetEnv(value map[string]string) {
	m.env = &value
}

// Env returns the value of the "env" field in the mutation.
func (m *McpMutation) Env() (r map[string]string, exists bool) {
	v := m.env
	if v == nil {
		return
	}
	return *v, true
}

// OldEnv returns the old "env" field's value of the Mcp entity.
// If the Mcp object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *McpMutation) OldEnv(ctx context.Context) (v map[string]string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldEnv is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldEnv requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldEnv: %w", err)
	}
	return oldValue.Env, nil
}

// ClearEnv clears the value of the "env" field.
func (m *McpMutation) ClearEnv() {
	m.env = nil
	m.clearedFields[mcp.FieldEnv] = struct{}{}
}

// EnvCleared returns if the "env" field was cleared in this mutation.
func (m *McpMutation) EnvCleared() bool {
	_, ok := m.clearedFields[mcp.FieldEnv]
	return ok
}

// ResetEnv resets all changes to the "env" field.
func (m *McpMutation) ResetEnv() {
	m.env = nil
	delete(m.clearedFields, mcp.FieldEnv)
}

// SetURL sets the "url" field.
func (m *McpMutation) SetURL(s string) {
	m.url = &s
}

// URL returns the value of the "url" field in the mutation.
func (m *McpMutation) URL() (r string, exists bool) {
	v := m.url
	if v == nil {
		return
	}
	return *v, true
}

// OldURL returns the old "url" field's value of the Mcp entity.
// If the Mcp object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *McpMutation) OldURL(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldURL is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldURL requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldURL: %w", err)
	}
	return oldValue.URL, nil
}

// ClearURL clears the value of the "url" field.
func (m *McpMutation) ClearURL() {
	m.url = nil
	m.clearedFields[mcp.FieldURL] = struct{}{}
}

// URLCleared returns if the "url" field was cleared in this mutation.
func (m *McpMutation) URLCleared() bool {
	_, ok := m.clearedFields[mcp.FieldURL]
	return ok
}

// ResetURL resets all changes to the "url" field.
func (m *McpMutation) ResetURL() {
	m.url = nil
	delete(m.clearedFields, mcp.FieldURL)
}

// SetHeaders sets the "headers" field.
func (m *McpMutation) SetHeaders(value map[string]string) {
	m.headers = &value
}

// Headers returns the value of the "headers" field in the mutation.
func (m *McpMutation) Headers() (r map[string]string, exists bool) {
	v := m.headers
	if v == nil {
		return
	}
	return *v, true
}

// OldHeaders returns the old "headers" field's value of the Mcp entity.
// If the Mcp object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *McpMutation) OldHeaders(ctx context.Context) (v map[string]string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldHeaders is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldHeaders requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldHeaders: %w", err)
	}
	return oldValue.Headers, nil
}

// ClearHeaders clears the value of the "headers" field.
func (m *McpMutation) ClearHeaders() {
	m.headers = nil
	m.clearedFields[mcp.FieldHeaders] = struct{}{}
}

// HeadersCleared returns if the "headers" field was cleared in this mutation.
func (m *McpMutation) HeadersCleared() bool {
	_, ok := m.clearedFields[mcp.FieldHeaders]
	return ok
}

// ResetHeaders resets all changes to the "headers" field.
func (m *McpMutation) ResetHeaders() {
	m.headers = nil
	delete(m.clearedFields, mcp.FieldHeaders)
}

// SetTimeout sets the "timeout" field.
func (m *McpMutation) SetTimeout(i int64) {
	m.timeout = &i
	m.addtimeout = nil
}

// Timeout returns the value of the "timeout" field in the mutation.
func (m *McpMutation) Timeout() (r int64, exists bool) {
	v := m.timeout
	if v == nil {
		return
	}
	return *v, true
}

// OldTimeout returns the old "timeout" field's value of the Mcp entity.
// If the Mcp object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *McpMutation) OldTimeout(ctx context.Context) (v int64, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldTimeout is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldTimeout requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldTimeout: %w", err)
	}
	return oldValue.Timeout, nil
}

// AddTimeout adds i to the "timeout" field.
func (m *McpMutation) AddTimeout(i int64) {
	if m.addtimeout != nil {
		*m.addtimeout += i
	} else {
		m.addtimeout = &i
	}
}

// AddedTimeout returns the value that was added to the "timeout" field in this mutation.
func (m *McpMutation) AddedTimeout() (r int64, exists bool) {
	v := m.addtimeout
	if v == nil {
		return
	}
	return *v, true
}

// ResetTimeout resets all changes to the "timeout" field.
func (m *McpMutation) ResetTimeout() {
	m.timeout = nil
	m.addtimeout = nil
}

// Where appends a list predicates to the McpMutation builder.
func (m *McpMutation) Where(ps ...predicate.Mcp) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the McpMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *McpMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.Mcp, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *McpMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *McpMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (Mcp).
func (m *McpMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *McpMutation) Fields() []string {
	fields := make([]string, 0, 8)
	if m._type != nil {
		fields = append(fields, mcp.FieldType)
	}
	if m.name != nil {
		fields = append(fields, mcp.FieldName)
	}
	if m.command != nil {
		fields = append(fields, mcp.FieldCommand)
	}
	if m.args != nil {
		fields = append(fields, mcp.FieldArgs)
	}
	if m.env != nil {
		fields = append(fields, mcp.FieldEnv)
	}
	if m.url != nil {
		fields = append(fields, mcp.FieldURL)
	}
	if m.headers != nil {
		fields = append(fields, mcp.FieldHeaders)
	}
	if m.timeout != nil {
		fields = append(fields, mcp.FieldTimeout)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *McpMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case mcp.FieldType:
		return m.GetType()
	case mcp.FieldName:
		return m.Name()
	case mcp.FieldCommand:
		return m.Command()
	case mcp.FieldArgs:
		return m.Args()
	case mcp.FieldEnv:
		return m.Env()
	case mcp.FieldURL:
		return m.URL()
	case mcp.FieldHeaders:
		return m.Headers()
	case mcp.FieldTimeout:
		return m.Timeout()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *McpMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case mcp.FieldType:
		return m.OldType(ctx)
	case mcp.FieldName:
		return m.OldName(ctx)
	case mcp.FieldCommand:
		return m.OldCommand(ctx)
	case mcp.FieldArgs:
		return m.OldArgs(ctx)
	case mcp.FieldEnv:
		return m.OldEnv(ctx)
	case mcp.FieldURL:
		return m.OldURL(ctx)
	case mcp.FieldHeaders:
		return m.OldHeaders(ctx)
	case mcp.FieldTimeout:
		return m.OldTimeout(ctx)
	}
	return nil, fmt.Errorf("unknown Mcp field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *McpMutation) SetField(name string, value ent.Value) error {
	switch name {
	case mcp.FieldType:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetType(v)
		return nil
	case mcp.FieldName:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetName(v)
		return nil
	case mcp.FieldCommand:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCommand(v)
		return nil
	case mcp.FieldArgs:
		v, ok := value.([]string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetArgs(v)
		return nil
	case mcp.FieldEnv:
		v, ok := value.(map[string]string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetEnv(v)
		return nil
	case mcp.FieldURL:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetURL(v)
		return nil
	case mcp.FieldHeaders:
		v, ok := value.(map[string]string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetHeaders(v)
		return nil
	case mcp.FieldTimeout:
		v, ok := value.(int64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetTimeout(v)
		return nil
	}
	return fmt.Errorf("unknown Mcp field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *McpMutation) AddedFields() []string {
	var fields []string
	if m.addtimeout != nil {
		fields = append(fields, mcp.FieldTimeout)
	}
	return fields
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *McpMutation) AddedField(name string) (ent.Value, bool) {
	switch name {
	case mcp.FieldTimeout:
		return m.AddedTimeout()
	}
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *McpMutation) AddField(name string, value ent.Value) error {
	switch name {
	case mcp.FieldTimeout:
		v, ok := value.(int64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddTimeout(v)
		return nil
	}
	return fmt.Errorf("unknown Mcp numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *McpMutation) ClearedFields() []string {
	var fields []string
	if m.FieldCleared(mcp.FieldCommand) {
		fields = append(fields, mcp.FieldCommand)
	}
	if m.FieldCleared(mcp.FieldArgs) {
		fields = append(fields, mcp.FieldArgs)
	}
	if m.FieldCleared(mcp.FieldEnv) {
		fields = append(fields, mcp.FieldEnv)
	}
	if m.FieldCleared(mcp.FieldURL) {
		fields = append(fields, mcp.FieldURL)
	}
	if m.FieldCleared(mcp.FieldHeaders) {
		fields = append(fields, mcp.FieldHeaders)
	}
	return fields
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *McpMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *McpMutation) ClearField(name string) error {
	switch name {
	case mcp.FieldCommand:
		m.ClearCommand()
		return nil
	case mcp.FieldArgs:
		m.ClearArgs()
		return nil
	case mcp.FieldEnv:
		m.ClearEnv()
		return nil
	case mcp.FieldURL:
		m.ClearURL()
		return nil
	case mcp.FieldHeaders:
		m.ClearHeaders()
		return nil
	}
	return fmt.Errorf("unknown Mcp nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *McpMutation) ResetField(name string) error {
	switch name {
	case mcp.FieldType:
		m.ResetType()
		return nil
	case mcp.FieldName:
		m.ResetName()
		return nil
	case mcp.FieldCommand:
		m.ResetCommand()
		return nil
	case mcp.FieldArgs:
		m.ResetArgs()
		return nil
	case mcp.FieldEnv:
		m.ResetEnv()
		return nil
	case mcp.FieldURL:
		m.ResetURL()
		return nil
	case mcp.FieldHeaders:
		m.ResetHeaders()
		return nil
	case mcp.FieldTimeout:
		m.ResetTimeout()
		return nil
	}
	return fmt.Errorf("unknown Mcp field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *McpMutation) AddedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *McpMutation) AddedIDs(name string) []ent.Value {
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *McpMutation) RemovedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *McpMutation) RemovedIDs(name string) []ent.Value {
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *McpMutation) ClearedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *McpMutation) EdgeCleared(name string) bool {
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *McpMutation) ClearEdge(name string) error {
	return fmt.Errorf("unknown Mcp unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *McpMutation) ResetEdge(name string) error {
	return fmt.Errorf("unknown Mcp edge %s", name)
}

// SessionMutation represents an operation that mutates the Session nodes in the graph.
type SessionMutation struct {
	config
	op             Op
	typ            string
	id             *string
	messages       *[]message.Message
	appendmessages []message.Message
	create_at      *string
	update_at      *string
	clearedFields  map[string]struct{}
	done           bool
	oldValue       func(context.Context) (*Session, error)
	predicates     []predicate.Session
}

var _ ent.Mutation = (*SessionMutation)(nil)

// sessionOption allows management of the mutation configuration using functional options.
type sessionOption func(*SessionMutation)

// newSessionMutation creates new mutation for the Session entity.
func newSessionMutation(c config, op Op, opts ...sessionOption) *SessionMutation {
	m := &SessionMutation{
		config:        c,
		op:            op,
		typ:           TypeSession,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withSessionID sets the ID field of the mutation.
func withSessionID(id string) sessionOption {
	return func(m *SessionMutation) {
		var (
			err   error
			once  sync.Once
			value *Session
		)
		m.oldValue = func(ctx context.Context) (*Session, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().Session.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withSession sets the old Session of the mutation.
func withSession(node *Session) sessionOption {
	return func(m *SessionMutation) {
		m.oldValue = func(context.Context) (*Session, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m SessionMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m SessionMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of Session entities.
func (m *SessionMutation) SetID(id string) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *SessionMutation) ID() (id string, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *SessionMutation) IDs(ctx context.Context) ([]string, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []string{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().Session.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetMessages sets the "messages" field.
func (m *SessionMutation) SetMessages(value []message.Message) {
	m.messages = &value
	m.appendmessages = nil
}

// Messages returns the value of the "messages" field in the mutation.
func (m *SessionMutation) Messages() (r []message.Message, exists bool) {
	v := m.messages
	if v == nil {
		return
	}
	return *v, true
}

// OldMessages returns the old "messages" field's value of the Session entity.
// If the Session object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *SessionMutation) OldMessages(ctx context.Context) (v []message.Message, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldMessages is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldMessages requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldMessages: %w", err)
	}
	return oldValue.Messages, nil
}

// AppendMessages adds value to the "messages" field.
func (m *SessionMutation) AppendMessages(value []message.Message) {
	m.appendmessages = append(m.appendmessages, value...)
}

// AppendedMessages returns the list of values that were appended to the "messages" field in this mutation.
func (m *SessionMutation) AppendedMessages() ([]message.Message, bool) {
	if len(m.appendmessages) == 0 {
		return nil, false
	}
	return m.appendmessages, true
}

// ResetMessages resets all changes to the "messages" field.
func (m *SessionMutation) ResetMessages() {
	m.messages = nil
	m.appendmessages = nil
}

// SetCreateAt sets the "create_at" field.
func (m *SessionMutation) SetCreateAt(s string) {
	m.create_at = &s
}

// CreateAt returns the value of the "create_at" field in the mutation.
func (m *SessionMutation) CreateAt() (r string, exists bool) {
	v := m.create_at
	if v == nil {
		return
	}
	return *v, true
}

// OldCreateAt returns the old "create_at" field's value of the Session entity.
// If the Session object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *SessionMutation) OldCreateAt(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreateAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreateAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreateAt: %w", err)
	}
	return oldValue.CreateAt, nil
}

// ResetCreateAt resets all changes to the "create_at" field.
func (m *SessionMutation) ResetCreateAt() {
	m.create_at = nil
}

// SetUpdateAt sets the "update_at" field.
func (m *SessionMutation) SetUpdateAt(s string) {
	m.update_at = &s
}

// UpdateAt returns the value of the "update_at" field in the mutation.
func (m *SessionMutation) UpdateAt() (r string, exists bool) {
	v := m.update_at
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdateAt returns the old "update_at" field's value of the Session entity.
// If the Session object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *SessionMutation) OldUpdateAt(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdateAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdateAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdateAt: %w", err)
	}
	return oldValue.UpdateAt, nil
}

// ResetUpdateAt resets all changes to the "update_at" field.
func (m *SessionMutation) ResetUpdateAt() {
	m.update_at = nil
}

// Where appends a list predicates to the SessionMutation builder.
func (m *SessionMutation) Where(ps ...predicate.Session) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the SessionMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *SessionMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.Session, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *SessionMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *SessionMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (Session).
func (m *SessionMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *SessionMutation) Fields() []string {
	fields := make([]string, 0, 3)
	if m.messages != nil {
		fields = append(fields, session.FieldMessages)
	}
	if m.create_at != nil {
		fields = append(fields, session.FieldCreateAt)
	}
	if m.update_at != nil {
		fields = append(fields, session.FieldUpdateAt)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *SessionMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case session.FieldMessages:
		return m.Messages()
	case session.FieldCreateAt:
		return m.CreateAt()
	case session.FieldUpdateAt:
		return m.UpdateAt()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *SessionMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case session.FieldMessages:
		return m.OldMessages(ctx)
	case session.FieldCreateAt:
		return m.OldCreateAt(ctx)
	case session.FieldUpdateAt:
		return m.OldUpdateAt(ctx)
	}
	return nil, fmt.Errorf("unknown Session field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *SessionMutation) SetField(name string, value ent.Value) error {
	switch name {
	case session.FieldMessages:
		v, ok := value.([]message.Message)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetMessages(v)
		return nil
	case session.FieldCreateAt:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreateAt(v)
		return nil
	case session.FieldUpdateAt:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdateAt(v)
		return nil
	}
	return fmt.Errorf("unknown Session field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *SessionMutation) AddedFields() []string {
	return nil
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *SessionMutation) AddedField(name string) (ent.Value, bool) {
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *SessionMutation) AddField(name string, value ent.Value) error {
	switch name {
	}
	return fmt.Errorf("unknown Session numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *SessionMutation) ClearedFields() []string {
	return nil
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *SessionMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *SessionMutation) ClearField(name string) error {
	return fmt.Errorf("unknown Session nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *SessionMutation) ResetField(name string) error {
	switch name {
	case session.FieldMessages:
		m.ResetMessages()
		return nil
	case session.FieldCreateAt:
		m.ResetCreateAt()
		return nil
	case session.FieldUpdateAt:
		m.ResetUpdateAt()
		return nil
	}
	return fmt.Errorf("unknown Session field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *SessionMutation) AddedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *SessionMutation) AddedIDs(name string) []ent.Value {
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *SessionMutation) RemovedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *SessionMutation) RemovedIDs(name string) []ent.Value {
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *SessionMutation) ClearedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *SessionMutation) EdgeCleared(name string) bool {
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *SessionMutation) ClearEdge(name string) error {
	return fmt.Errorf("unknown Session unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *SessionMutation) ResetEdge(name string) error {
	return fmt.Errorf("unknown Session edge %s", name)
}

// SignMutation represents an operation that mutates the Sign nodes in the graph.
type SignMutation struct {
	config
	op            Op
	typ           string
	id            *int
	name          *string
	secret        *string
	apikey        *string
	clearedFields map[string]struct{}
	done          bool
	oldValue      func(context.Context) (*Sign, error)
	predicates    []predicate.Sign
}

var _ ent.Mutation = (*SignMutation)(nil)

// signOption allows management of the mutation configuration using functional options.
type signOption func(*SignMutation)

// newSignMutation creates new mutation for the Sign entity.
func newSignMutation(c config, op Op, opts ...signOption) *SignMutation {
	m := &SignMutation{
		config:        c,
		op:            op,
		typ:           TypeSign,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withSignID sets the ID field of the mutation.
func withSignID(id int) signOption {
	return func(m *SignMutation) {
		var (
			err   error
			once  sync.Once
			value *Sign
		)
		m.oldValue = func(ctx context.Context) (*Sign, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().Sign.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withSign sets the old Sign of the mutation.
func withSign(node *Sign) signOption {
	return func(m *SignMutation) {
		m.oldValue = func(context.Context) (*Sign, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m SignMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m SignMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *SignMutation) ID() (id int, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *SignMutation) IDs(ctx context.Context) ([]int, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []int{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().Sign.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetName sets the "name" field.
func (m *SignMutation) SetName(s string) {
	m.name = &s
}

// Name returns the value of the "name" field in the mutation.
func (m *SignMutation) Name() (r string, exists bool) {
	v := m.name
	if v == nil {
		return
	}
	return *v, true
}

// OldName returns the old "name" field's value of the Sign entity.
// If the Sign object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *SignMutation) OldName(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldName is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldName requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldName: %w", err)
	}
	return oldValue.Name, nil
}

// ResetName resets all changes to the "name" field.
func (m *SignMutation) ResetName() {
	m.name = nil
}

// SetSecret sets the "secret" field.
func (m *SignMutation) SetSecret(s string) {
	m.secret = &s
}

// Secret returns the value of the "secret" field in the mutation.
func (m *SignMutation) Secret() (r string, exists bool) {
	v := m.secret
	if v == nil {
		return
	}
	return *v, true
}

// OldSecret returns the old "secret" field's value of the Sign entity.
// If the Sign object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *SignMutation) OldSecret(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldSecret is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldSecret requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldSecret: %w", err)
	}
	return oldValue.Secret, nil
}

// ResetSecret resets all changes to the "secret" field.
func (m *SignMutation) ResetSecret() {
	m.secret = nil
}

// SetApikey sets the "apikey" field.
func (m *SignMutation) SetApikey(s string) {
	m.apikey = &s
}

// Apikey returns the value of the "apikey" field in the mutation.
func (m *SignMutation) Apikey() (r string, exists bool) {
	v := m.apikey
	if v == nil {
		return
	}
	return *v, true
}

// OldApikey returns the old "apikey" field's value of the Sign entity.
// If the Sign object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *SignMutation) OldApikey(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldApikey is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldApikey requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldApikey: %w", err)
	}
	return oldValue.Apikey, nil
}

// ResetApikey resets all changes to the "apikey" field.
func (m *SignMutation) ResetApikey() {
	m.apikey = nil
}

// Where appends a list predicates to the SignMutation builder.
func (m *SignMutation) Where(ps ...predicate.Sign) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the SignMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *SignMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.Sign, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *SignMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *SignMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (Sign).
func (m *SignMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *SignMutation) Fields() []string {
	fields := make([]string, 0, 3)
	if m.name != nil {
		fields = append(fields, sign.FieldName)
	}
	if m.secret != nil {
		fields = append(fields, sign.FieldSecret)
	}
	if m.apikey != nil {
		fields = append(fields, sign.FieldApikey)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *SignMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case sign.FieldName:
		return m.Name()
	case sign.FieldSecret:
		return m.Secret()
	case sign.FieldApikey:
		return m.Apikey()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *SignMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case sign.FieldName:
		return m.OldName(ctx)
	case sign.FieldSecret:
		return m.OldSecret(ctx)
	case sign.FieldApikey:
		return m.OldApikey(ctx)
	}
	return nil, fmt.Errorf("unknown Sign field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *SignMutation) SetField(name string, value ent.Value) error {
	switch name {
	case sign.FieldName:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetName(v)
		return nil
	case sign.FieldSecret:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetSecret(v)
		return nil
	case sign.FieldApikey:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetApikey(v)
		return nil
	}
	return fmt.Errorf("unknown Sign field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *SignMutation) AddedFields() []string {
	return nil
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *SignMutation) AddedField(name string) (ent.Value, bool) {
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *SignMutation) AddField(name string, value ent.Value) error {
	switch name {
	}
	return fmt.Errorf("unknown Sign numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *SignMutation) ClearedFields() []string {
	return nil
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *SignMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *SignMutation) ClearField(name string) error {
	return fmt.Errorf("unknown Sign nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *SignMutation) ResetField(name string) error {
	switch name {
	case sign.FieldName:
		m.ResetName()
		return nil
	case sign.FieldSecret:
		m.ResetSecret()
		return nil
	case sign.FieldApikey:
		m.ResetApikey()
		return nil
	}
	return fmt.Errorf("unknown Sign field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *SignMutation) AddedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *SignMutation) AddedIDs(name string) []ent.Value {
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *SignMutation) RemovedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *SignMutation) RemovedIDs(name string) []ent.Value {
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *SignMutation) ClearedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *SignMutation) EdgeCleared(name string) bool {
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *SignMutation) ClearEdge(name string) error {
	return fmt.Errorf("unknown Sign unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *SignMutation) ResetEdge(name string) error {
	return fmt.Errorf("unknown Sign edge %s", name)
}

// TermMutation represents an operation that mutates the Term nodes in the graph.
type TermMutation struct {
	config
	op            Op
	typ           string
	id            *int
	create_at     *string
	update_at     *string
	aid           *string
	org           *string
	sid           *string
	content       *map[string]interface{}
	clearedFields map[string]struct{}
	done          bool
	oldValue      func(context.Context) (*Term, error)
	predicates    []predicate.Term
}

var _ ent.Mutation = (*TermMutation)(nil)

// termOption allows management of the mutation configuration using functional options.
type termOption func(*TermMutation)

// newTermMutation creates new mutation for the Term entity.
func newTermMutation(c config, op Op, opts ...termOption) *TermMutation {
	m := &TermMutation{
		config:        c,
		op:            op,
		typ:           TypeTerm,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withTermID sets the ID field of the mutation.
func withTermID(id int) termOption {
	return func(m *TermMutation) {
		var (
			err   error
			once  sync.Once
			value *Term
		)
		m.oldValue = func(ctx context.Context) (*Term, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().Term.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withTerm sets the old Term of the mutation.
func withTerm(node *Term) termOption {
	return func(m *TermMutation) {
		m.oldValue = func(context.Context) (*Term, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m TermMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m TermMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *TermMutation) ID() (id int, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *TermMutation) IDs(ctx context.Context) ([]int, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []int{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().Term.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetCreateAt sets the "create_at" field.
func (m *TermMutation) SetCreateAt(s string) {
	m.create_at = &s
}

// CreateAt returns the value of the "create_at" field in the mutation.
func (m *TermMutation) CreateAt() (r string, exists bool) {
	v := m.create_at
	if v == nil {
		return
	}
	return *v, true
}

// OldCreateAt returns the old "create_at" field's value of the Term entity.
// If the Term object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TermMutation) OldCreateAt(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreateAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreateAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreateAt: %w", err)
	}
	return oldValue.CreateAt, nil
}

// ResetCreateAt resets all changes to the "create_at" field.
func (m *TermMutation) ResetCreateAt() {
	m.create_at = nil
}

// SetUpdateAt sets the "update_at" field.
func (m *TermMutation) SetUpdateAt(s string) {
	m.update_at = &s
}

// UpdateAt returns the value of the "update_at" field in the mutation.
func (m *TermMutation) UpdateAt() (r string, exists bool) {
	v := m.update_at
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdateAt returns the old "update_at" field's value of the Term entity.
// If the Term object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TermMutation) OldUpdateAt(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdateAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdateAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdateAt: %w", err)
	}
	return oldValue.UpdateAt, nil
}

// ResetUpdateAt resets all changes to the "update_at" field.
func (m *TermMutation) ResetUpdateAt() {
	m.update_at = nil
}

// SetAid sets the "aid" field.
func (m *TermMutation) SetAid(s string) {
	m.aid = &s
}

// Aid returns the value of the "aid" field in the mutation.
func (m *TermMutation) Aid() (r string, exists bool) {
	v := m.aid
	if v == nil {
		return
	}
	return *v, true
}

// OldAid returns the old "aid" field's value of the Term entity.
// If the Term object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TermMutation) OldAid(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldAid is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldAid requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldAid: %w", err)
	}
	return oldValue.Aid, nil
}

// ResetAid resets all changes to the "aid" field.
func (m *TermMutation) ResetAid() {
	m.aid = nil
}

// SetOrg sets the "org" field.
func (m *TermMutation) SetOrg(s string) {
	m.org = &s
}

// Org returns the value of the "org" field in the mutation.
func (m *TermMutation) Org() (r string, exists bool) {
	v := m.org
	if v == nil {
		return
	}
	return *v, true
}

// OldOrg returns the old "org" field's value of the Term entity.
// If the Term object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TermMutation) OldOrg(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldOrg is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldOrg requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldOrg: %w", err)
	}
	return oldValue.Org, nil
}

// ResetOrg resets all changes to the "org" field.
func (m *TermMutation) ResetOrg() {
	m.org = nil
}

// SetSid sets the "sid" field.
func (m *TermMutation) SetSid(s string) {
	m.sid = &s
}

// Sid returns the value of the "sid" field in the mutation.
func (m *TermMutation) Sid() (r string, exists bool) {
	v := m.sid
	if v == nil {
		return
	}
	return *v, true
}

// OldSid returns the old "sid" field's value of the Term entity.
// If the Term object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TermMutation) OldSid(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldSid is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldSid requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldSid: %w", err)
	}
	return oldValue.Sid, nil
}

// ResetSid resets all changes to the "sid" field.
func (m *TermMutation) ResetSid() {
	m.sid = nil
}

// SetContent sets the "content" field.
func (m *TermMutation) SetContent(value map[string]interface{}) {
	m.content = &value
}

// Content returns the value of the "content" field in the mutation.
func (m *TermMutation) Content() (r map[string]interface{}, exists bool) {
	v := m.content
	if v == nil {
		return
	}
	return *v, true
}

// OldContent returns the old "content" field's value of the Term entity.
// If the Term object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TermMutation) OldContent(ctx context.Context) (v map[string]interface{}, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldContent is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldContent requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldContent: %w", err)
	}
	return oldValue.Content, nil
}

// ResetContent resets all changes to the "content" field.
func (m *TermMutation) ResetContent() {
	m.content = nil
}

// Where appends a list predicates to the TermMutation builder.
func (m *TermMutation) Where(ps ...predicate.Term) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the TermMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *TermMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.Term, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *TermMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *TermMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (Term).
func (m *TermMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *TermMutation) Fields() []string {
	fields := make([]string, 0, 6)
	if m.create_at != nil {
		fields = append(fields, term.FieldCreateAt)
	}
	if m.update_at != nil {
		fields = append(fields, term.FieldUpdateAt)
	}
	if m.aid != nil {
		fields = append(fields, term.FieldAid)
	}
	if m.org != nil {
		fields = append(fields, term.FieldOrg)
	}
	if m.sid != nil {
		fields = append(fields, term.FieldSid)
	}
	if m.content != nil {
		fields = append(fields, term.FieldContent)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *TermMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case term.FieldCreateAt:
		return m.CreateAt()
	case term.FieldUpdateAt:
		return m.UpdateAt()
	case term.FieldAid:
		return m.Aid()
	case term.FieldOrg:
		return m.Org()
	case term.FieldSid:
		return m.Sid()
	case term.FieldContent:
		return m.Content()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *TermMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case term.FieldCreateAt:
		return m.OldCreateAt(ctx)
	case term.FieldUpdateAt:
		return m.OldUpdateAt(ctx)
	case term.FieldAid:
		return m.OldAid(ctx)
	case term.FieldOrg:
		return m.OldOrg(ctx)
	case term.FieldSid:
		return m.OldSid(ctx)
	case term.FieldContent:
		return m.OldContent(ctx)
	}
	return nil, fmt.Errorf("unknown Term field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *TermMutation) SetField(name string, value ent.Value) error {
	switch name {
	case term.FieldCreateAt:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreateAt(v)
		return nil
	case term.FieldUpdateAt:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdateAt(v)
		return nil
	case term.FieldAid:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetAid(v)
		return nil
	case term.FieldOrg:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetOrg(v)
		return nil
	case term.FieldSid:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetSid(v)
		return nil
	case term.FieldContent:
		v, ok := value.(map[string]interface{})
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetContent(v)
		return nil
	}
	return fmt.Errorf("unknown Term field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *TermMutation) AddedFields() []string {
	return nil
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *TermMutation) AddedField(name string) (ent.Value, bool) {
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *TermMutation) AddField(name string, value ent.Value) error {
	switch name {
	}
	return fmt.Errorf("unknown Term numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *TermMutation) ClearedFields() []string {
	return nil
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *TermMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *TermMutation) ClearField(name string) error {
	return fmt.Errorf("unknown Term nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *TermMutation) ResetField(name string) error {
	switch name {
	case term.FieldCreateAt:
		m.ResetCreateAt()
		return nil
	case term.FieldUpdateAt:
		m.ResetUpdateAt()
		return nil
	case term.FieldAid:
		m.ResetAid()
		return nil
	case term.FieldOrg:
		m.ResetOrg()
		return nil
	case term.FieldSid:
		m.ResetSid()
		return nil
	case term.FieldContent:
		m.ResetContent()
		return nil
	}
	return fmt.Errorf("unknown Term field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *TermMutation) AddedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *TermMutation) AddedIDs(name string) []ent.Value {
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *TermMutation) RemovedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *TermMutation) RemovedIDs(name string) []ent.Value {
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *TermMutation) ClearedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *TermMutation) EdgeCleared(name string) bool {
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *TermMutation) ClearEdge(name string) error {
	return fmt.Errorf("unknown Term unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *TermMutation) ResetEdge(name string) error {
	return fmt.Errorf("unknown Term edge %s", name)
}

// ToolkitMutation represents an operation that mutates the Toolkit nodes in the graph.
type ToolkitMutation struct {
	config
	op            Op
	typ           string
	id            *string
	_type         *string
	tools         *[]string
	appendtools   []string
	_config       *string
	clearedFields map[string]struct{}
	done          bool
	oldValue      func(context.Context) (*Toolkit, error)
	predicates    []predicate.Toolkit
}

var _ ent.Mutation = (*ToolkitMutation)(nil)

// toolkitOption allows management of the mutation configuration using functional options.
type toolkitOption func(*ToolkitMutation)

// newToolkitMutation creates new mutation for the Toolkit entity.
func newToolkitMutation(c config, op Op, opts ...toolkitOption) *ToolkitMutation {
	m := &ToolkitMutation{
		config:        c,
		op:            op,
		typ:           TypeToolkit,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withToolkitID sets the ID field of the mutation.
func withToolkitID(id string) toolkitOption {
	return func(m *ToolkitMutation) {
		var (
			err   error
			once  sync.Once
			value *Toolkit
		)
		m.oldValue = func(ctx context.Context) (*Toolkit, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().Toolkit.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withToolkit sets the old Toolkit of the mutation.
func withToolkit(node *Toolkit) toolkitOption {
	return func(m *ToolkitMutation) {
		m.oldValue = func(context.Context) (*Toolkit, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m ToolkitMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m ToolkitMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of Toolkit entities.
func (m *ToolkitMutation) SetID(id string) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *ToolkitMutation) ID() (id string, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *ToolkitMutation) IDs(ctx context.Context) ([]string, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []string{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().Toolkit.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetType sets the "type" field.
func (m *ToolkitMutation) SetType(s string) {
	m._type = &s
}

// GetType returns the value of the "type" field in the mutation.
func (m *ToolkitMutation) GetType() (r string, exists bool) {
	v := m._type
	if v == nil {
		return
	}
	return *v, true
}

// OldType returns the old "type" field's value of the Toolkit entity.
// If the Toolkit object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ToolkitMutation) OldType(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldType is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldType requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldType: %w", err)
	}
	return oldValue.Type, nil
}

// ResetType resets all changes to the "type" field.
func (m *ToolkitMutation) ResetType() {
	m._type = nil
}

// SetTools sets the "tools" field.
func (m *ToolkitMutation) SetTools(s []string) {
	m.tools = &s
	m.appendtools = nil
}

// Tools returns the value of the "tools" field in the mutation.
func (m *ToolkitMutation) Tools() (r []string, exists bool) {
	v := m.tools
	if v == nil {
		return
	}
	return *v, true
}

// OldTools returns the old "tools" field's value of the Toolkit entity.
// If the Toolkit object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ToolkitMutation) OldTools(ctx context.Context) (v []string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldTools is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldTools requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldTools: %w", err)
	}
	return oldValue.Tools, nil
}

// AppendTools adds s to the "tools" field.
func (m *ToolkitMutation) AppendTools(s []string) {
	m.appendtools = append(m.appendtools, s...)
}

// AppendedTools returns the list of values that were appended to the "tools" field in this mutation.
func (m *ToolkitMutation) AppendedTools() ([]string, bool) {
	if len(m.appendtools) == 0 {
		return nil, false
	}
	return m.appendtools, true
}

// ResetTools resets all changes to the "tools" field.
func (m *ToolkitMutation) ResetTools() {
	m.tools = nil
	m.appendtools = nil
}

// SetConfig sets the "config" field.
func (m *ToolkitMutation) SetConfig(s string) {
	m._config = &s
}

// Config returns the value of the "config" field in the mutation.
func (m *ToolkitMutation) Config() (r string, exists bool) {
	v := m._config
	if v == nil {
		return
	}
	return *v, true
}

// OldConfig returns the old "config" field's value of the Toolkit entity.
// If the Toolkit object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ToolkitMutation) OldConfig(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldConfig is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldConfig requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldConfig: %w", err)
	}
	return oldValue.Config, nil
}

// ResetConfig resets all changes to the "config" field.
func (m *ToolkitMutation) ResetConfig() {
	m._config = nil
}

// Where appends a list predicates to the ToolkitMutation builder.
func (m *ToolkitMutation) Where(ps ...predicate.Toolkit) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the ToolkitMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *ToolkitMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.Toolkit, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *ToolkitMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *ToolkitMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (Toolkit).
func (m *ToolkitMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *ToolkitMutation) Fields() []string {
	fields := make([]string, 0, 3)
	if m._type != nil {
		fields = append(fields, toolkit.FieldType)
	}
	if m.tools != nil {
		fields = append(fields, toolkit.FieldTools)
	}
	if m._config != nil {
		fields = append(fields, toolkit.FieldConfig)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *ToolkitMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case toolkit.FieldType:
		return m.GetType()
	case toolkit.FieldTools:
		return m.Tools()
	case toolkit.FieldConfig:
		return m.Config()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *ToolkitMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case toolkit.FieldType:
		return m.OldType(ctx)
	case toolkit.FieldTools:
		return m.OldTools(ctx)
	case toolkit.FieldConfig:
		return m.OldConfig(ctx)
	}
	return nil, fmt.Errorf("unknown Toolkit field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *ToolkitMutation) SetField(name string, value ent.Value) error {
	switch name {
	case toolkit.FieldType:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetType(v)
		return nil
	case toolkit.FieldTools:
		v, ok := value.([]string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetTools(v)
		return nil
	case toolkit.FieldConfig:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetConfig(v)
		return nil
	}
	return fmt.Errorf("unknown Toolkit field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *ToolkitMutation) AddedFields() []string {
	return nil
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *ToolkitMutation) AddedField(name string) (ent.Value, bool) {
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *ToolkitMutation) AddField(name string, value ent.Value) error {
	switch name {
	}
	return fmt.Errorf("unknown Toolkit numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *ToolkitMutation) ClearedFields() []string {
	return nil
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *ToolkitMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *ToolkitMutation) ClearField(name string) error {
	return fmt.Errorf("unknown Toolkit nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *ToolkitMutation) ResetField(name string) error {
	switch name {
	case toolkit.FieldType:
		m.ResetType()
		return nil
	case toolkit.FieldTools:
		m.ResetTools()
		return nil
	case toolkit.FieldConfig:
		m.ResetConfig()
		return nil
	}
	return fmt.Errorf("unknown Toolkit field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *ToolkitMutation) AddedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *ToolkitMutation) AddedIDs(name string) []ent.Value {
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *ToolkitMutation) RemovedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *ToolkitMutation) RemovedIDs(name string) []ent.Value {
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *ToolkitMutation) ClearedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *ToolkitMutation) EdgeCleared(name string) bool {
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *ToolkitMutation) ClearEdge(name string) error {
	return fmt.Errorf("unknown Toolkit unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *ToolkitMutation) ResetEdge(name string) error {
	return fmt.Errorf("unknown Toolkit edge %s", name)
}

// TraitMutation represents an operation that mutates the Trait nodes in the graph.
type TraitMutation struct {
	config
	op            Op
	typ           string
	id            *int
	text          *string
	_type         *string
	level         *int
	addlevel      *int
	clearedFields map[string]struct{}
	done          bool
	oldValue      func(context.Context) (*Trait, error)
	predicates    []predicate.Trait
}

var _ ent.Mutation = (*TraitMutation)(nil)

// traitOption allows management of the mutation configuration using functional options.
type traitOption func(*TraitMutation)

// newTraitMutation creates new mutation for the Trait entity.
func newTraitMutation(c config, op Op, opts ...traitOption) *TraitMutation {
	m := &TraitMutation{
		config:        c,
		op:            op,
		typ:           TypeTrait,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withTraitID sets the ID field of the mutation.
func withTraitID(id int) traitOption {
	return func(m *TraitMutation) {
		var (
			err   error
			once  sync.Once
			value *Trait
		)
		m.oldValue = func(ctx context.Context) (*Trait, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().Trait.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withTrait sets the old Trait of the mutation.
func withTrait(node *Trait) traitOption {
	return func(m *TraitMutation) {
		m.oldValue = func(context.Context) (*Trait, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m TraitMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m TraitMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *TraitMutation) ID() (id int, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *TraitMutation) IDs(ctx context.Context) ([]int, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []int{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().Trait.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetText sets the "text" field.
func (m *TraitMutation) SetText(s string) {
	m.text = &s
}

// Text returns the value of the "text" field in the mutation.
func (m *TraitMutation) Text() (r string, exists bool) {
	v := m.text
	if v == nil {
		return
	}
	return *v, true
}

// OldText returns the old "text" field's value of the Trait entity.
// If the Trait object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TraitMutation) OldText(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldText is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldText requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldText: %w", err)
	}
	return oldValue.Text, nil
}

// ResetText resets all changes to the "text" field.
func (m *TraitMutation) ResetText() {
	m.text = nil
}

// SetType sets the "type" field.
func (m *TraitMutation) SetType(s string) {
	m._type = &s
}

// GetType returns the value of the "type" field in the mutation.
func (m *TraitMutation) GetType() (r string, exists bool) {
	v := m._type
	if v == nil {
		return
	}
	return *v, true
}

// OldType returns the old "type" field's value of the Trait entity.
// If the Trait object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TraitMutation) OldType(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldType is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldType requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldType: %w", err)
	}
	return oldValue.Type, nil
}

// ResetType resets all changes to the "type" field.
func (m *TraitMutation) ResetType() {
	m._type = nil
}

// SetLevel sets the "level" field.
func (m *TraitMutation) SetLevel(i int) {
	m.level = &i
	m.addlevel = nil
}

// Level returns the value of the "level" field in the mutation.
func (m *TraitMutation) Level() (r int, exists bool) {
	v := m.level
	if v == nil {
		return
	}
	return *v, true
}

// OldLevel returns the old "level" field's value of the Trait entity.
// If the Trait object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TraitMutation) OldLevel(ctx context.Context) (v int, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldLevel is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldLevel requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldLevel: %w", err)
	}
	return oldValue.Level, nil
}

// AddLevel adds i to the "level" field.
func (m *TraitMutation) AddLevel(i int) {
	if m.addlevel != nil {
		*m.addlevel += i
	} else {
		m.addlevel = &i
	}
}

// AddedLevel returns the value that was added to the "level" field in this mutation.
func (m *TraitMutation) AddedLevel() (r int, exists bool) {
	v := m.addlevel
	if v == nil {
		return
	}
	return *v, true
}

// ResetLevel resets all changes to the "level" field.
func (m *TraitMutation) ResetLevel() {
	m.level = nil
	m.addlevel = nil
}

// Where appends a list predicates to the TraitMutation builder.
func (m *TraitMutation) Where(ps ...predicate.Trait) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the TraitMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *TraitMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.Trait, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *TraitMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *TraitMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (Trait).
func (m *TraitMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *TraitMutation) Fields() []string {
	fields := make([]string, 0, 3)
	if m.text != nil {
		fields = append(fields, trait.FieldText)
	}
	if m._type != nil {
		fields = append(fields, trait.FieldType)
	}
	if m.level != nil {
		fields = append(fields, trait.FieldLevel)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *TraitMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case trait.FieldText:
		return m.Text()
	case trait.FieldType:
		return m.GetType()
	case trait.FieldLevel:
		return m.Level()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *TraitMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case trait.FieldText:
		return m.OldText(ctx)
	case trait.FieldType:
		return m.OldType(ctx)
	case trait.FieldLevel:
		return m.OldLevel(ctx)
	}
	return nil, fmt.Errorf("unknown Trait field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *TraitMutation) SetField(name string, value ent.Value) error {
	switch name {
	case trait.FieldText:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetText(v)
		return nil
	case trait.FieldType:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetType(v)
		return nil
	case trait.FieldLevel:
		v, ok := value.(int)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetLevel(v)
		return nil
	}
	return fmt.Errorf("unknown Trait field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *TraitMutation) AddedFields() []string {
	var fields []string
	if m.addlevel != nil {
		fields = append(fields, trait.FieldLevel)
	}
	return fields
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *TraitMutation) AddedField(name string) (ent.Value, bool) {
	switch name {
	case trait.FieldLevel:
		return m.AddedLevel()
	}
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *TraitMutation) AddField(name string, value ent.Value) error {
	switch name {
	case trait.FieldLevel:
		v, ok := value.(int)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddLevel(v)
		return nil
	}
	return fmt.Errorf("unknown Trait numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *TraitMutation) ClearedFields() []string {
	return nil
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *TraitMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *TraitMutation) ClearField(name string) error {
	return fmt.Errorf("unknown Trait nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *TraitMutation) ResetField(name string) error {
	switch name {
	case trait.FieldText:
		m.ResetText()
		return nil
	case trait.FieldType:
		m.ResetType()
		return nil
	case trait.FieldLevel:
		m.ResetLevel()
		return nil
	}
	return fmt.Errorf("unknown Trait field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *TraitMutation) AddedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *TraitMutation) AddedIDs(name string) []ent.Value {
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *TraitMutation) RemovedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *TraitMutation) RemovedIDs(name string) []ent.Value {
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *TraitMutation) ClearedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *TraitMutation) EdgeCleared(name string) bool {
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *TraitMutation) ClearEdge(name string) error {
	return fmt.Errorf("unknown Trait unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *TraitMutation) ResetEdge(name string) error {
	return fmt.Errorf("unknown Trait edge %s", name)
}
