// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"secwalk/internal/domain/repo/ent/sign"

	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// SignCreate is the builder for creating a Sign entity.
type SignCreate struct {
	config
	mutation *SignMutation
	hooks    []Hook
}

// SetName sets the "name" field.
func (sc *SignCreate) SetName(s string) *SignCreate {
	sc.mutation.SetName(s)
	return sc
}

// SetSecret sets the "secret" field.
func (sc *SignCreate) SetSecret(s string) *SignCreate {
	sc.mutation.SetSecret(s)
	return sc
}

// SetApikey sets the "apikey" field.
func (sc *SignCreate) SetApikey(s string) *SignCreate {
	sc.mutation.SetApikey(s)
	return sc
}

// Mutation returns the SignMutation object of the builder.
func (sc *SignCreate) Mutation() *SignMutation {
	return sc.mutation
}

// Save creates the Sign in the database.
func (sc *SignCreate) Save(ctx context.Context) (*Sign, error) {
	return withHooks(ctx, sc.sqlSave, sc.mutation, sc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (sc *SignCreate) SaveX(ctx context.Context) *Sign {
	v, err := sc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (sc *SignCreate) Exec(ctx context.Context) error {
	_, err := sc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (sc *SignCreate) ExecX(ctx context.Context) {
	if err := sc.Exec(ctx); err != nil {
		panic(err)
	}
}

// check runs all checks and user-defined validators on the builder.
func (sc *SignCreate) check() error {
	if _, ok := sc.mutation.Name(); !ok {
		return &ValidationError{Name: "name", err: errors.New(`ent: missing required field "Sign.name"`)}
	}
	if v, ok := sc.mutation.Name(); ok {
		if err := sign.NameValidator(v); err != nil {
			return &ValidationError{Name: "name", err: fmt.Errorf(`ent: validator failed for field "Sign.name": %w`, err)}
		}
	}
	if _, ok := sc.mutation.Secret(); !ok {
		return &ValidationError{Name: "secret", err: errors.New(`ent: missing required field "Sign.secret"`)}
	}
	if v, ok := sc.mutation.Secret(); ok {
		if err := sign.SecretValidator(v); err != nil {
			return &ValidationError{Name: "secret", err: fmt.Errorf(`ent: validator failed for field "Sign.secret": %w`, err)}
		}
	}
	if _, ok := sc.mutation.Apikey(); !ok {
		return &ValidationError{Name: "apikey", err: errors.New(`ent: missing required field "Sign.apikey"`)}
	}
	if v, ok := sc.mutation.Apikey(); ok {
		if err := sign.ApikeyValidator(v); err != nil {
			return &ValidationError{Name: "apikey", err: fmt.Errorf(`ent: validator failed for field "Sign.apikey": %w`, err)}
		}
	}
	return nil
}

func (sc *SignCreate) sqlSave(ctx context.Context) (*Sign, error) {
	if err := sc.check(); err != nil {
		return nil, err
	}
	_node, _spec := sc.createSpec()
	if err := sqlgraph.CreateNode(ctx, sc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	id := _spec.ID.Value.(int64)
	_node.ID = int(id)
	sc.mutation.id = &_node.ID
	sc.mutation.done = true
	return _node, nil
}

func (sc *SignCreate) createSpec() (*Sign, *sqlgraph.CreateSpec) {
	var (
		_node = &Sign{config: sc.config}
		_spec = sqlgraph.NewCreateSpec(sign.Table, sqlgraph.NewFieldSpec(sign.FieldID, field.TypeInt))
	)
	if value, ok := sc.mutation.Name(); ok {
		_spec.SetField(sign.FieldName, field.TypeString, value)
		_node.Name = value
	}
	if value, ok := sc.mutation.Secret(); ok {
		_spec.SetField(sign.FieldSecret, field.TypeString, value)
		_node.Secret = value
	}
	if value, ok := sc.mutation.Apikey(); ok {
		_spec.SetField(sign.FieldApikey, field.TypeString, value)
		_node.Apikey = value
	}
	return _node, _spec
}

// SignCreateBulk is the builder for creating many Sign entities in bulk.
type SignCreateBulk struct {
	config
	err      error
	builders []*SignCreate
}

// Save creates the Sign entities in the database.
func (scb *SignCreateBulk) Save(ctx context.Context) ([]*Sign, error) {
	if scb.err != nil {
		return nil, scb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(scb.builders))
	nodes := make([]*Sign, len(scb.builders))
	mutators := make([]Mutator, len(scb.builders))
	for i := range scb.builders {
		func(i int, root context.Context) {
			builder := scb.builders[i]
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*SignMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, scb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, scb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = int(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, scb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (scb *SignCreateBulk) SaveX(ctx context.Context) []*Sign {
	v, err := scb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (scb *SignCreateBulk) Exec(ctx context.Context) error {
	_, err := scb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (scb *SignCreateBulk) ExecX(ctx context.Context) {
	if err := scb.Exec(ctx); err != nil {
		panic(err)
	}
}
