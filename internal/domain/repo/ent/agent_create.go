// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"secwalk/internal/domain/repo/ent/agent"

	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// AgentC<PERSON> is the builder for creating a Agent entity.
type AgentCreate struct {
	config
	mutation *AgentMutation
	hooks    []Hook
}

// SetType sets the "type" field.
func (ac *AgentCreate) SetType(i int) *AgentCreate {
	ac.mutation.SetType(i)
	return ac
}

// SetNillableType sets the "type" field if the given value is not nil.
func (ac *AgentCreate) SetNillableType(i *int) *AgentCreate {
	if i != nil {
		ac.SetType(*i)
	}
	return ac
}

// SetVersion sets the "version" field.
func (ac *AgentCreate) SetVersion(s string) *AgentCreate {
	ac.mutation.SetVersion(s)
	return ac
}

// SetEngVersion sets the "eng_version" field.
func (ac *AgentCreate) SetEngVersion(s string) *AgentCreate {
	ac.mutation.SetEngVersion(s)
	return ac
}

// SetConfig sets the "config" field.
func (ac *AgentCreate) SetConfig(s string) *AgentCreate {
	ac.mutation.SetConfig(s)
	return ac
}

// SetExtension sets the "extension" field.
func (ac *AgentCreate) SetExtension(s string) *AgentCreate {
	ac.mutation.SetExtension(s)
	return ac
}

// SetNillableExtension sets the "extension" field if the given value is not nil.
func (ac *AgentCreate) SetNillableExtension(s *string) *AgentCreate {
	if s != nil {
		ac.SetExtension(*s)
	}
	return ac
}

// SetID sets the "id" field.
func (ac *AgentCreate) SetID(s string) *AgentCreate {
	ac.mutation.SetID(s)
	return ac
}

// Mutation returns the AgentMutation object of the builder.
func (ac *AgentCreate) Mutation() *AgentMutation {
	return ac.mutation
}

// Save creates the Agent in the database.
func (ac *AgentCreate) Save(ctx context.Context) (*Agent, error) {
	ac.defaults()
	return withHooks(ctx, ac.sqlSave, ac.mutation, ac.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (ac *AgentCreate) SaveX(ctx context.Context) *Agent {
	v, err := ac.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (ac *AgentCreate) Exec(ctx context.Context) error {
	_, err := ac.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (ac *AgentCreate) ExecX(ctx context.Context) {
	if err := ac.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (ac *AgentCreate) defaults() {
	if _, ok := ac.mutation.GetType(); !ok {
		v := agent.DefaultType
		ac.mutation.SetType(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (ac *AgentCreate) check() error {
	if _, ok := ac.mutation.GetType(); !ok {
		return &ValidationError{Name: "type", err: errors.New(`ent: missing required field "Agent.type"`)}
	}
	if _, ok := ac.mutation.Version(); !ok {
		return &ValidationError{Name: "version", err: errors.New(`ent: missing required field "Agent.version"`)}
	}
	if _, ok := ac.mutation.EngVersion(); !ok {
		return &ValidationError{Name: "eng_version", err: errors.New(`ent: missing required field "Agent.eng_version"`)}
	}
	if _, ok := ac.mutation.Config(); !ok {
		return &ValidationError{Name: "config", err: errors.New(`ent: missing required field "Agent.config"`)}
	}
	if v, ok := ac.mutation.Config(); ok {
		if err := agent.ConfigValidator(v); err != nil {
			return &ValidationError{Name: "config", err: fmt.Errorf(`ent: validator failed for field "Agent.config": %w`, err)}
		}
	}
	if v, ok := ac.mutation.ID(); ok {
		if err := agent.IDValidator(v); err != nil {
			return &ValidationError{Name: "id", err: fmt.Errorf(`ent: validator failed for field "Agent.id": %w`, err)}
		}
	}
	return nil
}

func (ac *AgentCreate) sqlSave(ctx context.Context) (*Agent, error) {
	if err := ac.check(); err != nil {
		return nil, err
	}
	_node, _spec := ac.createSpec()
	if err := sqlgraph.CreateNode(ctx, ac.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != nil {
		if id, ok := _spec.ID.Value.(string); ok {
			_node.ID = id
		} else {
			return nil, fmt.Errorf("unexpected Agent.ID type: %T", _spec.ID.Value)
		}
	}
	ac.mutation.id = &_node.ID
	ac.mutation.done = true
	return _node, nil
}

func (ac *AgentCreate) createSpec() (*Agent, *sqlgraph.CreateSpec) {
	var (
		_node = &Agent{config: ac.config}
		_spec = sqlgraph.NewCreateSpec(agent.Table, sqlgraph.NewFieldSpec(agent.FieldID, field.TypeString))
	)
	if id, ok := ac.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = id
	}
	if value, ok := ac.mutation.GetType(); ok {
		_spec.SetField(agent.FieldType, field.TypeInt, value)
		_node.Type = value
	}
	if value, ok := ac.mutation.Version(); ok {
		_spec.SetField(agent.FieldVersion, field.TypeString, value)
		_node.Version = value
	}
	if value, ok := ac.mutation.EngVersion(); ok {
		_spec.SetField(agent.FieldEngVersion, field.TypeString, value)
		_node.EngVersion = value
	}
	if value, ok := ac.mutation.Config(); ok {
		_spec.SetField(agent.FieldConfig, field.TypeString, value)
		_node.Config = value
	}
	if value, ok := ac.mutation.Extension(); ok {
		_spec.SetField(agent.FieldExtension, field.TypeString, value)
		_node.Extension = value
	}
	return _node, _spec
}

// AgentCreateBulk is the builder for creating many Agent entities in bulk.
type AgentCreateBulk struct {
	config
	err      error
	builders []*AgentCreate
}

// Save creates the Agent entities in the database.
func (acb *AgentCreateBulk) Save(ctx context.Context) ([]*Agent, error) {
	if acb.err != nil {
		return nil, acb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(acb.builders))
	nodes := make([]*Agent, len(acb.builders))
	mutators := make([]Mutator, len(acb.builders))
	for i := range acb.builders {
		func(i int, root context.Context) {
			builder := acb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*AgentMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, acb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, acb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, acb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (acb *AgentCreateBulk) SaveX(ctx context.Context) []*Agent {
	v, err := acb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (acb *AgentCreateBulk) Exec(ctx context.Context) error {
	_, err := acb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (acb *AgentCreateBulk) ExecX(ctx context.Context) {
	if err := acb.Exec(ctx); err != nil {
		panic(err)
	}
}
