// Code generated by ent, DO NOT EDIT.

package mcp

import (
	"secwalk/internal/domain/repo/ent/predicate"

	"entgo.io/ent/dialect/sql"
)

// ID filters vertices based on their ID field.
func ID(id string) predicate.Mcp {
	return predicate.Mcp(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id string) predicate.Mcp {
	return predicate.Mcp(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id string) predicate.Mcp {
	return predicate.Mcp(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...string) predicate.Mcp {
	return predicate.Mcp(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...string) predicate.Mcp {
	return predicate.Mcp(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id string) predicate.Mcp {
	return predicate.Mcp(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id string) predicate.Mcp {
	return predicate.Mcp(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id string) predicate.Mcp {
	return predicate.Mcp(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id string) predicate.Mcp {
	return predicate.Mcp(sql.FieldLTE(FieldID, id))
}

// IDEqualFold applies the EqualFold predicate on the ID field.
func IDEqualFold(id string) predicate.Mcp {
	return predicate.Mcp(sql.FieldEqualFold(FieldID, id))
}

// IDContainsFold applies the ContainsFold predicate on the ID field.
func IDContainsFold(id string) predicate.Mcp {
	return predicate.Mcp(sql.FieldContainsFold(FieldID, id))
}

// Type applies equality check predicate on the "type" field. It's identical to TypeEQ.
func Type(v string) predicate.Mcp {
	return predicate.Mcp(sql.FieldEQ(FieldType, v))
}

// Name applies equality check predicate on the "name" field. It's identical to NameEQ.
func Name(v string) predicate.Mcp {
	return predicate.Mcp(sql.FieldEQ(FieldName, v))
}

// Command applies equality check predicate on the "command" field. It's identical to CommandEQ.
func Command(v string) predicate.Mcp {
	return predicate.Mcp(sql.FieldEQ(FieldCommand, v))
}

// URL applies equality check predicate on the "url" field. It's identical to URLEQ.
func URL(v string) predicate.Mcp {
	return predicate.Mcp(sql.FieldEQ(FieldURL, v))
}

// Timeout applies equality check predicate on the "timeout" field. It's identical to TimeoutEQ.
func Timeout(v int64) predicate.Mcp {
	return predicate.Mcp(sql.FieldEQ(FieldTimeout, v))
}

// TypeEQ applies the EQ predicate on the "type" field.
func TypeEQ(v string) predicate.Mcp {
	return predicate.Mcp(sql.FieldEQ(FieldType, v))
}

// TypeNEQ applies the NEQ predicate on the "type" field.
func TypeNEQ(v string) predicate.Mcp {
	return predicate.Mcp(sql.FieldNEQ(FieldType, v))
}

// TypeIn applies the In predicate on the "type" field.
func TypeIn(vs ...string) predicate.Mcp {
	return predicate.Mcp(sql.FieldIn(FieldType, vs...))
}

// TypeNotIn applies the NotIn predicate on the "type" field.
func TypeNotIn(vs ...string) predicate.Mcp {
	return predicate.Mcp(sql.FieldNotIn(FieldType, vs...))
}

// TypeGT applies the GT predicate on the "type" field.
func TypeGT(v string) predicate.Mcp {
	return predicate.Mcp(sql.FieldGT(FieldType, v))
}

// TypeGTE applies the GTE predicate on the "type" field.
func TypeGTE(v string) predicate.Mcp {
	return predicate.Mcp(sql.FieldGTE(FieldType, v))
}

// TypeLT applies the LT predicate on the "type" field.
func TypeLT(v string) predicate.Mcp {
	return predicate.Mcp(sql.FieldLT(FieldType, v))
}

// TypeLTE applies the LTE predicate on the "type" field.
func TypeLTE(v string) predicate.Mcp {
	return predicate.Mcp(sql.FieldLTE(FieldType, v))
}

// TypeContains applies the Contains predicate on the "type" field.
func TypeContains(v string) predicate.Mcp {
	return predicate.Mcp(sql.FieldContains(FieldType, v))
}

// TypeHasPrefix applies the HasPrefix predicate on the "type" field.
func TypeHasPrefix(v string) predicate.Mcp {
	return predicate.Mcp(sql.FieldHasPrefix(FieldType, v))
}

// TypeHasSuffix applies the HasSuffix predicate on the "type" field.
func TypeHasSuffix(v string) predicate.Mcp {
	return predicate.Mcp(sql.FieldHasSuffix(FieldType, v))
}

// TypeEqualFold applies the EqualFold predicate on the "type" field.
func TypeEqualFold(v string) predicate.Mcp {
	return predicate.Mcp(sql.FieldEqualFold(FieldType, v))
}

// TypeContainsFold applies the ContainsFold predicate on the "type" field.
func TypeContainsFold(v string) predicate.Mcp {
	return predicate.Mcp(sql.FieldContainsFold(FieldType, v))
}

// NameEQ applies the EQ predicate on the "name" field.
func NameEQ(v string) predicate.Mcp {
	return predicate.Mcp(sql.FieldEQ(FieldName, v))
}

// NameNEQ applies the NEQ predicate on the "name" field.
func NameNEQ(v string) predicate.Mcp {
	return predicate.Mcp(sql.FieldNEQ(FieldName, v))
}

// NameIn applies the In predicate on the "name" field.
func NameIn(vs ...string) predicate.Mcp {
	return predicate.Mcp(sql.FieldIn(FieldName, vs...))
}

// NameNotIn applies the NotIn predicate on the "name" field.
func NameNotIn(vs ...string) predicate.Mcp {
	return predicate.Mcp(sql.FieldNotIn(FieldName, vs...))
}

// NameGT applies the GT predicate on the "name" field.
func NameGT(v string) predicate.Mcp {
	return predicate.Mcp(sql.FieldGT(FieldName, v))
}

// NameGTE applies the GTE predicate on the "name" field.
func NameGTE(v string) predicate.Mcp {
	return predicate.Mcp(sql.FieldGTE(FieldName, v))
}

// NameLT applies the LT predicate on the "name" field.
func NameLT(v string) predicate.Mcp {
	return predicate.Mcp(sql.FieldLT(FieldName, v))
}

// NameLTE applies the LTE predicate on the "name" field.
func NameLTE(v string) predicate.Mcp {
	return predicate.Mcp(sql.FieldLTE(FieldName, v))
}

// NameContains applies the Contains predicate on the "name" field.
func NameContains(v string) predicate.Mcp {
	return predicate.Mcp(sql.FieldContains(FieldName, v))
}

// NameHasPrefix applies the HasPrefix predicate on the "name" field.
func NameHasPrefix(v string) predicate.Mcp {
	return predicate.Mcp(sql.FieldHasPrefix(FieldName, v))
}

// NameHasSuffix applies the HasSuffix predicate on the "name" field.
func NameHasSuffix(v string) predicate.Mcp {
	return predicate.Mcp(sql.FieldHasSuffix(FieldName, v))
}

// NameEqualFold applies the EqualFold predicate on the "name" field.
func NameEqualFold(v string) predicate.Mcp {
	return predicate.Mcp(sql.FieldEqualFold(FieldName, v))
}

// NameContainsFold applies the ContainsFold predicate on the "name" field.
func NameContainsFold(v string) predicate.Mcp {
	return predicate.Mcp(sql.FieldContainsFold(FieldName, v))
}

// CommandEQ applies the EQ predicate on the "command" field.
func CommandEQ(v string) predicate.Mcp {
	return predicate.Mcp(sql.FieldEQ(FieldCommand, v))
}

// CommandNEQ applies the NEQ predicate on the "command" field.
func CommandNEQ(v string) predicate.Mcp {
	return predicate.Mcp(sql.FieldNEQ(FieldCommand, v))
}

// CommandIn applies the In predicate on the "command" field.
func CommandIn(vs ...string) predicate.Mcp {
	return predicate.Mcp(sql.FieldIn(FieldCommand, vs...))
}

// CommandNotIn applies the NotIn predicate on the "command" field.
func CommandNotIn(vs ...string) predicate.Mcp {
	return predicate.Mcp(sql.FieldNotIn(FieldCommand, vs...))
}

// CommandGT applies the GT predicate on the "command" field.
func CommandGT(v string) predicate.Mcp {
	return predicate.Mcp(sql.FieldGT(FieldCommand, v))
}

// CommandGTE applies the GTE predicate on the "command" field.
func CommandGTE(v string) predicate.Mcp {
	return predicate.Mcp(sql.FieldGTE(FieldCommand, v))
}

// CommandLT applies the LT predicate on the "command" field.
func CommandLT(v string) predicate.Mcp {
	return predicate.Mcp(sql.FieldLT(FieldCommand, v))
}

// CommandLTE applies the LTE predicate on the "command" field.
func CommandLTE(v string) predicate.Mcp {
	return predicate.Mcp(sql.FieldLTE(FieldCommand, v))
}

// CommandContains applies the Contains predicate on the "command" field.
func CommandContains(v string) predicate.Mcp {
	return predicate.Mcp(sql.FieldContains(FieldCommand, v))
}

// CommandHasPrefix applies the HasPrefix predicate on the "command" field.
func CommandHasPrefix(v string) predicate.Mcp {
	return predicate.Mcp(sql.FieldHasPrefix(FieldCommand, v))
}

// CommandHasSuffix applies the HasSuffix predicate on the "command" field.
func CommandHasSuffix(v string) predicate.Mcp {
	return predicate.Mcp(sql.FieldHasSuffix(FieldCommand, v))
}

// CommandIsNil applies the IsNil predicate on the "command" field.
func CommandIsNil() predicate.Mcp {
	return predicate.Mcp(sql.FieldIsNull(FieldCommand))
}

// CommandNotNil applies the NotNil predicate on the "command" field.
func CommandNotNil() predicate.Mcp {
	return predicate.Mcp(sql.FieldNotNull(FieldCommand))
}

// CommandEqualFold applies the EqualFold predicate on the "command" field.
func CommandEqualFold(v string) predicate.Mcp {
	return predicate.Mcp(sql.FieldEqualFold(FieldCommand, v))
}

// CommandContainsFold applies the ContainsFold predicate on the "command" field.
func CommandContainsFold(v string) predicate.Mcp {
	return predicate.Mcp(sql.FieldContainsFold(FieldCommand, v))
}

// ArgsIsNil applies the IsNil predicate on the "args" field.
func ArgsIsNil() predicate.Mcp {
	return predicate.Mcp(sql.FieldIsNull(FieldArgs))
}

// ArgsNotNil applies the NotNil predicate on the "args" field.
func ArgsNotNil() predicate.Mcp {
	return predicate.Mcp(sql.FieldNotNull(FieldArgs))
}

// EnvIsNil applies the IsNil predicate on the "env" field.
func EnvIsNil() predicate.Mcp {
	return predicate.Mcp(sql.FieldIsNull(FieldEnv))
}

// EnvNotNil applies the NotNil predicate on the "env" field.
func EnvNotNil() predicate.Mcp {
	return predicate.Mcp(sql.FieldNotNull(FieldEnv))
}

// URLEQ applies the EQ predicate on the "url" field.
func URLEQ(v string) predicate.Mcp {
	return predicate.Mcp(sql.FieldEQ(FieldURL, v))
}

// URLNEQ applies the NEQ predicate on the "url" field.
func URLNEQ(v string) predicate.Mcp {
	return predicate.Mcp(sql.FieldNEQ(FieldURL, v))
}

// URLIn applies the In predicate on the "url" field.
func URLIn(vs ...string) predicate.Mcp {
	return predicate.Mcp(sql.FieldIn(FieldURL, vs...))
}

// URLNotIn applies the NotIn predicate on the "url" field.
func URLNotIn(vs ...string) predicate.Mcp {
	return predicate.Mcp(sql.FieldNotIn(FieldURL, vs...))
}

// URLGT applies the GT predicate on the "url" field.
func URLGT(v string) predicate.Mcp {
	return predicate.Mcp(sql.FieldGT(FieldURL, v))
}

// URLGTE applies the GTE predicate on the "url" field.
func URLGTE(v string) predicate.Mcp {
	return predicate.Mcp(sql.FieldGTE(FieldURL, v))
}

// URLLT applies the LT predicate on the "url" field.
func URLLT(v string) predicate.Mcp {
	return predicate.Mcp(sql.FieldLT(FieldURL, v))
}

// URLLTE applies the LTE predicate on the "url" field.
func URLLTE(v string) predicate.Mcp {
	return predicate.Mcp(sql.FieldLTE(FieldURL, v))
}

// URLContains applies the Contains predicate on the "url" field.
func URLContains(v string) predicate.Mcp {
	return predicate.Mcp(sql.FieldContains(FieldURL, v))
}

// URLHasPrefix applies the HasPrefix predicate on the "url" field.
func URLHasPrefix(v string) predicate.Mcp {
	return predicate.Mcp(sql.FieldHasPrefix(FieldURL, v))
}

// URLHasSuffix applies the HasSuffix predicate on the "url" field.
func URLHasSuffix(v string) predicate.Mcp {
	return predicate.Mcp(sql.FieldHasSuffix(FieldURL, v))
}

// URLIsNil applies the IsNil predicate on the "url" field.
func URLIsNil() predicate.Mcp {
	return predicate.Mcp(sql.FieldIsNull(FieldURL))
}

// URLNotNil applies the NotNil predicate on the "url" field.
func URLNotNil() predicate.Mcp {
	return predicate.Mcp(sql.FieldNotNull(FieldURL))
}

// URLEqualFold applies the EqualFold predicate on the "url" field.
func URLEqualFold(v string) predicate.Mcp {
	return predicate.Mcp(sql.FieldEqualFold(FieldURL, v))
}

// URLContainsFold applies the ContainsFold predicate on the "url" field.
func URLContainsFold(v string) predicate.Mcp {
	return predicate.Mcp(sql.FieldContainsFold(FieldURL, v))
}

// HeadersIsNil applies the IsNil predicate on the "headers" field.
func HeadersIsNil() predicate.Mcp {
	return predicate.Mcp(sql.FieldIsNull(FieldHeaders))
}

// HeadersNotNil applies the NotNil predicate on the "headers" field.
func HeadersNotNil() predicate.Mcp {
	return predicate.Mcp(sql.FieldNotNull(FieldHeaders))
}

// TimeoutEQ applies the EQ predicate on the "timeout" field.
func TimeoutEQ(v int64) predicate.Mcp {
	return predicate.Mcp(sql.FieldEQ(FieldTimeout, v))
}

// TimeoutNEQ applies the NEQ predicate on the "timeout" field.
func TimeoutNEQ(v int64) predicate.Mcp {
	return predicate.Mcp(sql.FieldNEQ(FieldTimeout, v))
}

// TimeoutIn applies the In predicate on the "timeout" field.
func TimeoutIn(vs ...int64) predicate.Mcp {
	return predicate.Mcp(sql.FieldIn(FieldTimeout, vs...))
}

// TimeoutNotIn applies the NotIn predicate on the "timeout" field.
func TimeoutNotIn(vs ...int64) predicate.Mcp {
	return predicate.Mcp(sql.FieldNotIn(FieldTimeout, vs...))
}

// TimeoutGT applies the GT predicate on the "timeout" field.
func TimeoutGT(v int64) predicate.Mcp {
	return predicate.Mcp(sql.FieldGT(FieldTimeout, v))
}

// TimeoutGTE applies the GTE predicate on the "timeout" field.
func TimeoutGTE(v int64) predicate.Mcp {
	return predicate.Mcp(sql.FieldGTE(FieldTimeout, v))
}

// TimeoutLT applies the LT predicate on the "timeout" field.
func TimeoutLT(v int64) predicate.Mcp {
	return predicate.Mcp(sql.FieldLT(FieldTimeout, v))
}

// TimeoutLTE applies the LTE predicate on the "timeout" field.
func TimeoutLTE(v int64) predicate.Mcp {
	return predicate.Mcp(sql.FieldLTE(FieldTimeout, v))
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.Mcp) predicate.Mcp {
	return predicate.Mcp(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.Mcp) predicate.Mcp {
	return predicate.Mcp(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.Mcp) predicate.Mcp {
	return predicate.Mcp(sql.NotPredicates(p))
}
