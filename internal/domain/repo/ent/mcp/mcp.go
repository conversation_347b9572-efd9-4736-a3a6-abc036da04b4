// Code generated by ent, DO NOT EDIT.

package mcp

import (
	"entgo.io/ent/dialect/sql"
)

const (
	// Label holds the string label denoting the mcp type in the database.
	Label = "mcp"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldType holds the string denoting the type field in the database.
	FieldType = "type"
	// FieldName holds the string denoting the name field in the database.
	FieldName = "name"
	// FieldCommand holds the string denoting the command field in the database.
	FieldCommand = "command"
	// FieldArgs holds the string denoting the args field in the database.
	FieldArgs = "args"
	// FieldEnv holds the string denoting the env field in the database.
	FieldEnv = "env"
	// FieldURL holds the string denoting the url field in the database.
	FieldURL = "url"
	// FieldHeaders holds the string denoting the headers field in the database.
	FieldHeaders = "headers"
	// FieldTimeout holds the string denoting the timeout field in the database.
	FieldTimeout = "timeout"
	// Table holds the table name of the mcp in the database.
	Table = "mcps"
)

// Columns holds all SQL columns for mcp fields.
var Columns = []string{
	FieldID,
	FieldType,
	FieldName,
	FieldCommand,
	FieldArgs,
	FieldEnv,
	FieldURL,
	FieldHeaders,
	FieldTimeout,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

var (
	// TypeValidator is a validator for the "type" field. It is called by the builders before save.
	TypeValidator func(string) error
	// NameValidator is a validator for the "name" field. It is called by the builders before save.
	NameValidator func(string) error
	// IDValidator is a validator for the "id" field. It is called by the builders before save.
	IDValidator func(string) error
)

// OrderOption defines the ordering options for the Mcp queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByType orders the results by the type field.
func ByType(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldType, opts...).ToFunc()
}

// ByName orders the results by the name field.
func ByName(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldName, opts...).ToFunc()
}

// ByCommand orders the results by the command field.
func ByCommand(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCommand, opts...).ToFunc()
}

// ByURL orders the results by the url field.
func ByURL(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldURL, opts...).ToFunc()
}

// ByTimeout orders the results by the timeout field.
func ByTimeout(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldTimeout, opts...).ToFunc()
}
