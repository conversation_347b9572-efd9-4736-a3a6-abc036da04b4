// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"secwalk/internal/domain/repo/ent/agent"
	"secwalk/internal/domain/repo/ent/predicate"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// AgentUpdate is the builder for updating Agent entities.
type AgentUpdate struct {
	config
	hooks    []Hook
	mutation *AgentMutation
}

// Where appends a list predicates to the AgentUpdate builder.
func (au *AgentUpdate) Where(ps ...predicate.Agent) *AgentUpdate {
	au.mutation.Where(ps...)
	return au
}

// SetType sets the "type" field.
func (au *AgentUpdate) SetType(i int) *AgentUpdate {
	au.mutation.ResetType()
	au.mutation.SetType(i)
	return au
}

// SetNillableType sets the "type" field if the given value is not nil.
func (au *AgentUpdate) SetNillableType(i *int) *AgentUpdate {
	if i != nil {
		au.SetType(*i)
	}
	return au
}

// AddType adds i to the "type" field.
func (au *AgentUpdate) AddType(i int) *AgentUpdate {
	au.mutation.AddType(i)
	return au
}

// SetVersion sets the "version" field.
func (au *AgentUpdate) SetVersion(s string) *AgentUpdate {
	au.mutation.SetVersion(s)
	return au
}

// SetNillableVersion sets the "version" field if the given value is not nil.
func (au *AgentUpdate) SetNillableVersion(s *string) *AgentUpdate {
	if s != nil {
		au.SetVersion(*s)
	}
	return au
}

// SetEngVersion sets the "eng_version" field.
func (au *AgentUpdate) SetEngVersion(s string) *AgentUpdate {
	au.mutation.SetEngVersion(s)
	return au
}

// SetNillableEngVersion sets the "eng_version" field if the given value is not nil.
func (au *AgentUpdate) SetNillableEngVersion(s *string) *AgentUpdate {
	if s != nil {
		au.SetEngVersion(*s)
	}
	return au
}

// SetConfig sets the "config" field.
func (au *AgentUpdate) SetConfig(s string) *AgentUpdate {
	au.mutation.SetConfig(s)
	return au
}

// SetNillableConfig sets the "config" field if the given value is not nil.
func (au *AgentUpdate) SetNillableConfig(s *string) *AgentUpdate {
	if s != nil {
		au.SetConfig(*s)
	}
	return au
}

// SetExtension sets the "extension" field.
func (au *AgentUpdate) SetExtension(s string) *AgentUpdate {
	au.mutation.SetExtension(s)
	return au
}

// SetNillableExtension sets the "extension" field if the given value is not nil.
func (au *AgentUpdate) SetNillableExtension(s *string) *AgentUpdate {
	if s != nil {
		au.SetExtension(*s)
	}
	return au
}

// ClearExtension clears the value of the "extension" field.
func (au *AgentUpdate) ClearExtension() *AgentUpdate {
	au.mutation.ClearExtension()
	return au
}

// Mutation returns the AgentMutation object of the builder.
func (au *AgentUpdate) Mutation() *AgentMutation {
	return au.mutation
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (au *AgentUpdate) Save(ctx context.Context) (int, error) {
	return withHooks(ctx, au.sqlSave, au.mutation, au.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (au *AgentUpdate) SaveX(ctx context.Context) int {
	affected, err := au.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (au *AgentUpdate) Exec(ctx context.Context) error {
	_, err := au.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (au *AgentUpdate) ExecX(ctx context.Context) {
	if err := au.Exec(ctx); err != nil {
		panic(err)
	}
}

// check runs all checks and user-defined validators on the builder.
func (au *AgentUpdate) check() error {
	if v, ok := au.mutation.Config(); ok {
		if err := agent.ConfigValidator(v); err != nil {
			return &ValidationError{Name: "config", err: fmt.Errorf(`ent: validator failed for field "Agent.config": %w`, err)}
		}
	}
	return nil
}

func (au *AgentUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := au.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(agent.Table, agent.Columns, sqlgraph.NewFieldSpec(agent.FieldID, field.TypeString))
	if ps := au.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := au.mutation.GetType(); ok {
		_spec.SetField(agent.FieldType, field.TypeInt, value)
	}
	if value, ok := au.mutation.AddedType(); ok {
		_spec.AddField(agent.FieldType, field.TypeInt, value)
	}
	if value, ok := au.mutation.Version(); ok {
		_spec.SetField(agent.FieldVersion, field.TypeString, value)
	}
	if value, ok := au.mutation.EngVersion(); ok {
		_spec.SetField(agent.FieldEngVersion, field.TypeString, value)
	}
	if value, ok := au.mutation.Config(); ok {
		_spec.SetField(agent.FieldConfig, field.TypeString, value)
	}
	if value, ok := au.mutation.Extension(); ok {
		_spec.SetField(agent.FieldExtension, field.TypeString, value)
	}
	if au.mutation.ExtensionCleared() {
		_spec.ClearField(agent.FieldExtension, field.TypeString)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, au.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{agent.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	au.mutation.done = true
	return n, nil
}

// AgentUpdateOne is the builder for updating a single Agent entity.
type AgentUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *AgentMutation
}

// SetType sets the "type" field.
func (auo *AgentUpdateOne) SetType(i int) *AgentUpdateOne {
	auo.mutation.ResetType()
	auo.mutation.SetType(i)
	return auo
}

// SetNillableType sets the "type" field if the given value is not nil.
func (auo *AgentUpdateOne) SetNillableType(i *int) *AgentUpdateOne {
	if i != nil {
		auo.SetType(*i)
	}
	return auo
}

// AddType adds i to the "type" field.
func (auo *AgentUpdateOne) AddType(i int) *AgentUpdateOne {
	auo.mutation.AddType(i)
	return auo
}

// SetVersion sets the "version" field.
func (auo *AgentUpdateOne) SetVersion(s string) *AgentUpdateOne {
	auo.mutation.SetVersion(s)
	return auo
}

// SetNillableVersion sets the "version" field if the given value is not nil.
func (auo *AgentUpdateOne) SetNillableVersion(s *string) *AgentUpdateOne {
	if s != nil {
		auo.SetVersion(*s)
	}
	return auo
}

// SetEngVersion sets the "eng_version" field.
func (auo *AgentUpdateOne) SetEngVersion(s string) *AgentUpdateOne {
	auo.mutation.SetEngVersion(s)
	return auo
}

// SetNillableEngVersion sets the "eng_version" field if the given value is not nil.
func (auo *AgentUpdateOne) SetNillableEngVersion(s *string) *AgentUpdateOne {
	if s != nil {
		auo.SetEngVersion(*s)
	}
	return auo
}

// SetConfig sets the "config" field.
func (auo *AgentUpdateOne) SetConfig(s string) *AgentUpdateOne {
	auo.mutation.SetConfig(s)
	return auo
}

// SetNillableConfig sets the "config" field if the given value is not nil.
func (auo *AgentUpdateOne) SetNillableConfig(s *string) *AgentUpdateOne {
	if s != nil {
		auo.SetConfig(*s)
	}
	return auo
}

// SetExtension sets the "extension" field.
func (auo *AgentUpdateOne) SetExtension(s string) *AgentUpdateOne {
	auo.mutation.SetExtension(s)
	return auo
}

// SetNillableExtension sets the "extension" field if the given value is not nil.
func (auo *AgentUpdateOne) SetNillableExtension(s *string) *AgentUpdateOne {
	if s != nil {
		auo.SetExtension(*s)
	}
	return auo
}

// ClearExtension clears the value of the "extension" field.
func (auo *AgentUpdateOne) ClearExtension() *AgentUpdateOne {
	auo.mutation.ClearExtension()
	return auo
}

// Mutation returns the AgentMutation object of the builder.
func (auo *AgentUpdateOne) Mutation() *AgentMutation {
	return auo.mutation
}

// Where appends a list predicates to the AgentUpdate builder.
func (auo *AgentUpdateOne) Where(ps ...predicate.Agent) *AgentUpdateOne {
	auo.mutation.Where(ps...)
	return auo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (auo *AgentUpdateOne) Select(field string, fields ...string) *AgentUpdateOne {
	auo.fields = append([]string{field}, fields...)
	return auo
}

// Save executes the query and returns the updated Agent entity.
func (auo *AgentUpdateOne) Save(ctx context.Context) (*Agent, error) {
	return withHooks(ctx, auo.sqlSave, auo.mutation, auo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (auo *AgentUpdateOne) SaveX(ctx context.Context) *Agent {
	node, err := auo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (auo *AgentUpdateOne) Exec(ctx context.Context) error {
	_, err := auo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (auo *AgentUpdateOne) ExecX(ctx context.Context) {
	if err := auo.Exec(ctx); err != nil {
		panic(err)
	}
}

// check runs all checks and user-defined validators on the builder.
func (auo *AgentUpdateOne) check() error {
	if v, ok := auo.mutation.Config(); ok {
		if err := agent.ConfigValidator(v); err != nil {
			return &ValidationError{Name: "config", err: fmt.Errorf(`ent: validator failed for field "Agent.config": %w`, err)}
		}
	}
	return nil
}

func (auo *AgentUpdateOne) sqlSave(ctx context.Context) (_node *Agent, err error) {
	if err := auo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(agent.Table, agent.Columns, sqlgraph.NewFieldSpec(agent.FieldID, field.TypeString))
	id, ok := auo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "Agent.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := auo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, agent.FieldID)
		for _, f := range fields {
			if !agent.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != agent.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := auo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := auo.mutation.GetType(); ok {
		_spec.SetField(agent.FieldType, field.TypeInt, value)
	}
	if value, ok := auo.mutation.AddedType(); ok {
		_spec.AddField(agent.FieldType, field.TypeInt, value)
	}
	if value, ok := auo.mutation.Version(); ok {
		_spec.SetField(agent.FieldVersion, field.TypeString, value)
	}
	if value, ok := auo.mutation.EngVersion(); ok {
		_spec.SetField(agent.FieldEngVersion, field.TypeString, value)
	}
	if value, ok := auo.mutation.Config(); ok {
		_spec.SetField(agent.FieldConfig, field.TypeString, value)
	}
	if value, ok := auo.mutation.Extension(); ok {
		_spec.SetField(agent.FieldExtension, field.TypeString, value)
	}
	if auo.mutation.ExtensionCleared() {
		_spec.ClearField(agent.FieldExtension, field.TypeString)
	}
	_node = &Agent{config: auo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, auo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{agent.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	auo.mutation.done = true
	return _node, nil
}
