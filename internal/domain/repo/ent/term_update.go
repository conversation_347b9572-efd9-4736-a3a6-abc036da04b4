// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"secwalk/internal/domain/repo/ent/predicate"
	"secwalk/internal/domain/repo/ent/term"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// TermUpdate is the builder for updating Term entities.
type TermUpdate struct {
	config
	hooks    []Hook
	mutation *TermMutation
}

// Where appends a list predicates to the TermUpdate builder.
func (tu *TermUpdate) Where(ps ...predicate.Term) *TermUpdate {
	tu.mutation.Where(ps...)
	return tu
}

// SetCreateAt sets the "create_at" field.
func (tu *TermUpdate) SetCreateAt(s string) *TermUpdate {
	tu.mutation.SetCreateAt(s)
	return tu
}

// SetNillableCreateAt sets the "create_at" field if the given value is not nil.
func (tu *TermUpdate) SetNillableCreateAt(s *string) *TermUpdate {
	if s != nil {
		tu.SetCreateAt(*s)
	}
	return tu
}

// SetUpdateAt sets the "update_at" field.
func (tu *TermUpdate) SetUpdateAt(s string) *TermUpdate {
	tu.mutation.SetUpdateAt(s)
	return tu
}

// SetNillableUpdateAt sets the "update_at" field if the given value is not nil.
func (tu *TermUpdate) SetNillableUpdateAt(s *string) *TermUpdate {
	if s != nil {
		tu.SetUpdateAt(*s)
	}
	return tu
}

// SetAid sets the "aid" field.
func (tu *TermUpdate) SetAid(s string) *TermUpdate {
	tu.mutation.SetAid(s)
	return tu
}

// SetNillableAid sets the "aid" field if the given value is not nil.
func (tu *TermUpdate) SetNillableAid(s *string) *TermUpdate {
	if s != nil {
		tu.SetAid(*s)
	}
	return tu
}

// SetOrg sets the "org" field.
func (tu *TermUpdate) SetOrg(s string) *TermUpdate {
	tu.mutation.SetOrg(s)
	return tu
}

// SetNillableOrg sets the "org" field if the given value is not nil.
func (tu *TermUpdate) SetNillableOrg(s *string) *TermUpdate {
	if s != nil {
		tu.SetOrg(*s)
	}
	return tu
}

// SetSid sets the "sid" field.
func (tu *TermUpdate) SetSid(s string) *TermUpdate {
	tu.mutation.SetSid(s)
	return tu
}

// SetNillableSid sets the "sid" field if the given value is not nil.
func (tu *TermUpdate) SetNillableSid(s *string) *TermUpdate {
	if s != nil {
		tu.SetSid(*s)
	}
	return tu
}

// SetContent sets the "content" field.
func (tu *TermUpdate) SetContent(m map[string]interface{}) *TermUpdate {
	tu.mutation.SetContent(m)
	return tu
}

// Mutation returns the TermMutation object of the builder.
func (tu *TermUpdate) Mutation() *TermMutation {
	return tu.mutation
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (tu *TermUpdate) Save(ctx context.Context) (int, error) {
	return withHooks(ctx, tu.sqlSave, tu.mutation, tu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (tu *TermUpdate) SaveX(ctx context.Context) int {
	affected, err := tu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (tu *TermUpdate) Exec(ctx context.Context) error {
	_, err := tu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (tu *TermUpdate) ExecX(ctx context.Context) {
	if err := tu.Exec(ctx); err != nil {
		panic(err)
	}
}

// check runs all checks and user-defined validators on the builder.
func (tu *TermUpdate) check() error {
	if v, ok := tu.mutation.Aid(); ok {
		if err := term.AidValidator(v); err != nil {
			return &ValidationError{Name: "aid", err: fmt.Errorf(`ent: validator failed for field "Term.aid": %w`, err)}
		}
	}
	return nil
}

func (tu *TermUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := tu.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(term.Table, term.Columns, sqlgraph.NewFieldSpec(term.FieldID, field.TypeInt))
	if ps := tu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := tu.mutation.CreateAt(); ok {
		_spec.SetField(term.FieldCreateAt, field.TypeString, value)
	}
	if value, ok := tu.mutation.UpdateAt(); ok {
		_spec.SetField(term.FieldUpdateAt, field.TypeString, value)
	}
	if value, ok := tu.mutation.Aid(); ok {
		_spec.SetField(term.FieldAid, field.TypeString, value)
	}
	if value, ok := tu.mutation.Org(); ok {
		_spec.SetField(term.FieldOrg, field.TypeString, value)
	}
	if value, ok := tu.mutation.Sid(); ok {
		_spec.SetField(term.FieldSid, field.TypeString, value)
	}
	if value, ok := tu.mutation.Content(); ok {
		_spec.SetField(term.FieldContent, field.TypeJSON, value)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, tu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{term.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	tu.mutation.done = true
	return n, nil
}

// TermUpdateOne is the builder for updating a single Term entity.
type TermUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *TermMutation
}

// SetCreateAt sets the "create_at" field.
func (tuo *TermUpdateOne) SetCreateAt(s string) *TermUpdateOne {
	tuo.mutation.SetCreateAt(s)
	return tuo
}

// SetNillableCreateAt sets the "create_at" field if the given value is not nil.
func (tuo *TermUpdateOne) SetNillableCreateAt(s *string) *TermUpdateOne {
	if s != nil {
		tuo.SetCreateAt(*s)
	}
	return tuo
}

// SetUpdateAt sets the "update_at" field.
func (tuo *TermUpdateOne) SetUpdateAt(s string) *TermUpdateOne {
	tuo.mutation.SetUpdateAt(s)
	return tuo
}

// SetNillableUpdateAt sets the "update_at" field if the given value is not nil.
func (tuo *TermUpdateOne) SetNillableUpdateAt(s *string) *TermUpdateOne {
	if s != nil {
		tuo.SetUpdateAt(*s)
	}
	return tuo
}

// SetAid sets the "aid" field.
func (tuo *TermUpdateOne) SetAid(s string) *TermUpdateOne {
	tuo.mutation.SetAid(s)
	return tuo
}

// SetNillableAid sets the "aid" field if the given value is not nil.
func (tuo *TermUpdateOne) SetNillableAid(s *string) *TermUpdateOne {
	if s != nil {
		tuo.SetAid(*s)
	}
	return tuo
}

// SetOrg sets the "org" field.
func (tuo *TermUpdateOne) SetOrg(s string) *TermUpdateOne {
	tuo.mutation.SetOrg(s)
	return tuo
}

// SetNillableOrg sets the "org" field if the given value is not nil.
func (tuo *TermUpdateOne) SetNillableOrg(s *string) *TermUpdateOne {
	if s != nil {
		tuo.SetOrg(*s)
	}
	return tuo
}

// SetSid sets the "sid" field.
func (tuo *TermUpdateOne) SetSid(s string) *TermUpdateOne {
	tuo.mutation.SetSid(s)
	return tuo
}

// SetNillableSid sets the "sid" field if the given value is not nil.
func (tuo *TermUpdateOne) SetNillableSid(s *string) *TermUpdateOne {
	if s != nil {
		tuo.SetSid(*s)
	}
	return tuo
}

// SetContent sets the "content" field.
func (tuo *TermUpdateOne) SetContent(m map[string]interface{}) *TermUpdateOne {
	tuo.mutation.SetContent(m)
	return tuo
}

// Mutation returns the TermMutation object of the builder.
func (tuo *TermUpdateOne) Mutation() *TermMutation {
	return tuo.mutation
}

// Where appends a list predicates to the TermUpdate builder.
func (tuo *TermUpdateOne) Where(ps ...predicate.Term) *TermUpdateOne {
	tuo.mutation.Where(ps...)
	return tuo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (tuo *TermUpdateOne) Select(field string, fields ...string) *TermUpdateOne {
	tuo.fields = append([]string{field}, fields...)
	return tuo
}

// Save executes the query and returns the updated Term entity.
func (tuo *TermUpdateOne) Save(ctx context.Context) (*Term, error) {
	return withHooks(ctx, tuo.sqlSave, tuo.mutation, tuo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (tuo *TermUpdateOne) SaveX(ctx context.Context) *Term {
	node, err := tuo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (tuo *TermUpdateOne) Exec(ctx context.Context) error {
	_, err := tuo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (tuo *TermUpdateOne) ExecX(ctx context.Context) {
	if err := tuo.Exec(ctx); err != nil {
		panic(err)
	}
}

// check runs all checks and user-defined validators on the builder.
func (tuo *TermUpdateOne) check() error {
	if v, ok := tuo.mutation.Aid(); ok {
		if err := term.AidValidator(v); err != nil {
			return &ValidationError{Name: "aid", err: fmt.Errorf(`ent: validator failed for field "Term.aid": %w`, err)}
		}
	}
	return nil
}

func (tuo *TermUpdateOne) sqlSave(ctx context.Context) (_node *Term, err error) {
	if err := tuo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(term.Table, term.Columns, sqlgraph.NewFieldSpec(term.FieldID, field.TypeInt))
	id, ok := tuo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "Term.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := tuo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, term.FieldID)
		for _, f := range fields {
			if !term.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != term.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := tuo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := tuo.mutation.CreateAt(); ok {
		_spec.SetField(term.FieldCreateAt, field.TypeString, value)
	}
	if value, ok := tuo.mutation.UpdateAt(); ok {
		_spec.SetField(term.FieldUpdateAt, field.TypeString, value)
	}
	if value, ok := tuo.mutation.Aid(); ok {
		_spec.SetField(term.FieldAid, field.TypeString, value)
	}
	if value, ok := tuo.mutation.Org(); ok {
		_spec.SetField(term.FieldOrg, field.TypeString, value)
	}
	if value, ok := tuo.mutation.Sid(); ok {
		_spec.SetField(term.FieldSid, field.TypeString, value)
	}
	if value, ok := tuo.mutation.Content(); ok {
		_spec.SetField(term.FieldContent, field.TypeJSON, value)
	}
	_node = &Term{config: tuo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, tuo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{term.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	tuo.mutation.done = true
	return _node, nil
}
