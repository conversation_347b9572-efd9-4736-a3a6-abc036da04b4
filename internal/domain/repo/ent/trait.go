// Code generated by ent, DO NOT EDIT.

package ent

import (
	"fmt"
	"secwalk/internal/domain/repo/ent/trait"
	"strings"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

// Trait is the model entity for the Trait schema.
type Trait struct {
	config `json:"-"`
	// ID of the ent.
	ID int `json:"id,omitempty"`
	// Text holds the value of the "text" field.
	Text string `json:"text,omitempty"`
	// Type holds the value of the "type" field.
	Type string `json:"type,omitempty"`
	// Level holds the value of the "level" field.
	Level        int `json:"level,omitempty"`
	selectValues sql.SelectValues
}

// scanValues returns the types for scanning values from sql.Rows.
func (*Trait) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case trait.FieldID, trait.FieldLevel:
			values[i] = new(sql.NullInt64)
		case trait.FieldText, trait.FieldType:
			values[i] = new(sql.NullString)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the Trait fields.
func (t *Trait) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case trait.FieldID:
			value, ok := values[i].(*sql.NullInt64)
			if !ok {
				return fmt.Errorf("unexpected type %T for field id", value)
			}
			t.ID = int(value.Int64)
		case trait.FieldText:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field text", values[i])
			} else if value.Valid {
				t.Text = value.String
			}
		case trait.FieldType:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field type", values[i])
			} else if value.Valid {
				t.Type = value.String
			}
		case trait.FieldLevel:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field level", values[i])
			} else if value.Valid {
				t.Level = int(value.Int64)
			}
		default:
			t.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the Trait.
// This includes values selected through modifiers, order, etc.
func (t *Trait) Value(name string) (ent.Value, error) {
	return t.selectValues.Get(name)
}

// Update returns a builder for updating this Trait.
// Note that you need to call Trait.Unwrap() before calling this method if this Trait
// was returned from a transaction, and the transaction was committed or rolled back.
func (t *Trait) Update() *TraitUpdateOne {
	return NewTraitClient(t.config).UpdateOne(t)
}

// Unwrap unwraps the Trait entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (t *Trait) Unwrap() *Trait {
	_tx, ok := t.config.driver.(*txDriver)
	if !ok {
		panic("ent: Trait is not a transactional entity")
	}
	t.config.driver = _tx.drv
	return t
}

// String implements the fmt.Stringer.
func (t *Trait) String() string {
	var builder strings.Builder
	builder.WriteString("Trait(")
	builder.WriteString(fmt.Sprintf("id=%v, ", t.ID))
	builder.WriteString("text=")
	builder.WriteString(t.Text)
	builder.WriteString(", ")
	builder.WriteString("type=")
	builder.WriteString(t.Type)
	builder.WriteString(", ")
	builder.WriteString("level=")
	builder.WriteString(fmt.Sprintf("%v", t.Level))
	builder.WriteByte(')')
	return builder.String()
}

// Traits is a parsable slice of Trait.
type Traits []*Trait
