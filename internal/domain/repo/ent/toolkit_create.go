// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"secwalk/internal/domain/repo/ent/toolkit"

	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// ToolkitCreate is the builder for creating a Toolkit entity.
type ToolkitCreate struct {
	config
	mutation *ToolkitMutation
	hooks    []Hook
}

// SetType sets the "type" field.
func (tc *ToolkitCreate) SetType(s string) *ToolkitCreate {
	tc.mutation.SetType(s)
	return tc
}

// SetNillableType sets the "type" field if the given value is not nil.
func (tc *ToolkitCreate) SetNillableType(s *string) *ToolkitCreate {
	if s != nil {
		tc.SetType(*s)
	}
	return tc
}

// SetTools sets the "tools" field.
func (tc *ToolkitCreate) SetTools(s []string) *ToolkitCreate {
	tc.mutation.SetTools(s)
	return tc
}

// SetConfig sets the "config" field.
func (tc *ToolkitCreate) SetConfig(s string) *ToolkitCreate {
	tc.mutation.SetConfig(s)
	return tc
}

// SetID sets the "id" field.
func (tc *ToolkitCreate) SetID(s string) *ToolkitCreate {
	tc.mutation.SetID(s)
	return tc
}

// Mutation returns the ToolkitMutation object of the builder.
func (tc *ToolkitCreate) Mutation() *ToolkitMutation {
	return tc.mutation
}

// Save creates the Toolkit in the database.
func (tc *ToolkitCreate) Save(ctx context.Context) (*Toolkit, error) {
	tc.defaults()
	return withHooks(ctx, tc.sqlSave, tc.mutation, tc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (tc *ToolkitCreate) SaveX(ctx context.Context) *Toolkit {
	v, err := tc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (tc *ToolkitCreate) Exec(ctx context.Context) error {
	_, err := tc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (tc *ToolkitCreate) ExecX(ctx context.Context) {
	if err := tc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (tc *ToolkitCreate) defaults() {
	if _, ok := tc.mutation.GetType(); !ok {
		v := toolkit.DefaultType
		tc.mutation.SetType(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (tc *ToolkitCreate) check() error {
	if _, ok := tc.mutation.GetType(); !ok {
		return &ValidationError{Name: "type", err: errors.New(`ent: missing required field "Toolkit.type"`)}
	}
	if _, ok := tc.mutation.Tools(); !ok {
		return &ValidationError{Name: "tools", err: errors.New(`ent: missing required field "Toolkit.tools"`)}
	}
	if _, ok := tc.mutation.Config(); !ok {
		return &ValidationError{Name: "config", err: errors.New(`ent: missing required field "Toolkit.config"`)}
	}
	if v, ok := tc.mutation.Config(); ok {
		if err := toolkit.ConfigValidator(v); err != nil {
			return &ValidationError{Name: "config", err: fmt.Errorf(`ent: validator failed for field "Toolkit.config": %w`, err)}
		}
	}
	if v, ok := tc.mutation.ID(); ok {
		if err := toolkit.IDValidator(v); err != nil {
			return &ValidationError{Name: "id", err: fmt.Errorf(`ent: validator failed for field "Toolkit.id": %w`, err)}
		}
	}
	return nil
}

func (tc *ToolkitCreate) sqlSave(ctx context.Context) (*Toolkit, error) {
	if err := tc.check(); err != nil {
		return nil, err
	}
	_node, _spec := tc.createSpec()
	if err := sqlgraph.CreateNode(ctx, tc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != nil {
		if id, ok := _spec.ID.Value.(string); ok {
			_node.ID = id
		} else {
			return nil, fmt.Errorf("unexpected Toolkit.ID type: %T", _spec.ID.Value)
		}
	}
	tc.mutation.id = &_node.ID
	tc.mutation.done = true
	return _node, nil
}

func (tc *ToolkitCreate) createSpec() (*Toolkit, *sqlgraph.CreateSpec) {
	var (
		_node = &Toolkit{config: tc.config}
		_spec = sqlgraph.NewCreateSpec(toolkit.Table, sqlgraph.NewFieldSpec(toolkit.FieldID, field.TypeString))
	)
	if id, ok := tc.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = id
	}
	if value, ok := tc.mutation.GetType(); ok {
		_spec.SetField(toolkit.FieldType, field.TypeString, value)
		_node.Type = value
	}
	if value, ok := tc.mutation.Tools(); ok {
		_spec.SetField(toolkit.FieldTools, field.TypeJSON, value)
		_node.Tools = value
	}
	if value, ok := tc.mutation.Config(); ok {
		_spec.SetField(toolkit.FieldConfig, field.TypeString, value)
		_node.Config = value
	}
	return _node, _spec
}

// ToolkitCreateBulk is the builder for creating many Toolkit entities in bulk.
type ToolkitCreateBulk struct {
	config
	err      error
	builders []*ToolkitCreate
}

// Save creates the Toolkit entities in the database.
func (tcb *ToolkitCreateBulk) Save(ctx context.Context) ([]*Toolkit, error) {
	if tcb.err != nil {
		return nil, tcb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(tcb.builders))
	nodes := make([]*Toolkit, len(tcb.builders))
	mutators := make([]Mutator, len(tcb.builders))
	for i := range tcb.builders {
		func(i int, root context.Context) {
			builder := tcb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*ToolkitMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, tcb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, tcb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, tcb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (tcb *ToolkitCreateBulk) SaveX(ctx context.Context) []*Toolkit {
	v, err := tcb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (tcb *ToolkitCreateBulk) Exec(ctx context.Context) error {
	_, err := tcb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (tcb *ToolkitCreateBulk) ExecX(ctx context.Context) {
	if err := tcb.Exec(ctx); err != nil {
		panic(err)
	}
}
