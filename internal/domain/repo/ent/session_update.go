// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"secwalk/internal/domain/core/message"
	"secwalk/internal/domain/repo/ent/predicate"
	"secwalk/internal/domain/repo/ent/session"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/dialect/sql/sqljson"
	"entgo.io/ent/schema/field"
)

// SessionUpdate is the builder for updating Session entities.
type SessionUpdate struct {
	config
	hooks    []Hook
	mutation *SessionMutation
}

// Where appends a list predicates to the SessionUpdate builder.
func (su *SessionUpdate) Where(ps ...predicate.Session) *SessionUpdate {
	su.mutation.Where(ps...)
	return su
}

// SetMessages sets the "messages" field.
func (su *SessionUpdate) SetMessages(m []message.Message) *SessionUpdate {
	su.mutation.SetMessages(m)
	return su
}

// AppendMessages appends m to the "messages" field.
func (su *SessionUpdate) AppendMessages(m []message.Message) *SessionUpdate {
	su.mutation.AppendMessages(m)
	return su
}

// SetCreateAt sets the "create_at" field.
func (su *SessionUpdate) SetCreateAt(s string) *SessionUpdate {
	su.mutation.SetCreateAt(s)
	return su
}

// SetNillableCreateAt sets the "create_at" field if the given value is not nil.
func (su *SessionUpdate) SetNillableCreateAt(s *string) *SessionUpdate {
	if s != nil {
		su.SetCreateAt(*s)
	}
	return su
}

// SetUpdateAt sets the "update_at" field.
func (su *SessionUpdate) SetUpdateAt(s string) *SessionUpdate {
	su.mutation.SetUpdateAt(s)
	return su
}

// SetNillableUpdateAt sets the "update_at" field if the given value is not nil.
func (su *SessionUpdate) SetNillableUpdateAt(s *string) *SessionUpdate {
	if s != nil {
		su.SetUpdateAt(*s)
	}
	return su
}

// Mutation returns the SessionMutation object of the builder.
func (su *SessionUpdate) Mutation() *SessionMutation {
	return su.mutation
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (su *SessionUpdate) Save(ctx context.Context) (int, error) {
	return withHooks(ctx, su.sqlSave, su.mutation, su.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (su *SessionUpdate) SaveX(ctx context.Context) int {
	affected, err := su.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (su *SessionUpdate) Exec(ctx context.Context) error {
	_, err := su.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (su *SessionUpdate) ExecX(ctx context.Context) {
	if err := su.Exec(ctx); err != nil {
		panic(err)
	}
}

func (su *SessionUpdate) sqlSave(ctx context.Context) (n int, err error) {
	_spec := sqlgraph.NewUpdateSpec(session.Table, session.Columns, sqlgraph.NewFieldSpec(session.FieldID, field.TypeString))
	if ps := su.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := su.mutation.Messages(); ok {
		_spec.SetField(session.FieldMessages, field.TypeJSON, value)
	}
	if value, ok := su.mutation.AppendedMessages(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, session.FieldMessages, value)
		})
	}
	if value, ok := su.mutation.CreateAt(); ok {
		_spec.SetField(session.FieldCreateAt, field.TypeString, value)
	}
	if value, ok := su.mutation.UpdateAt(); ok {
		_spec.SetField(session.FieldUpdateAt, field.TypeString, value)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, su.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{session.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	su.mutation.done = true
	return n, nil
}

// SessionUpdateOne is the builder for updating a single Session entity.
type SessionUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *SessionMutation
}

// SetMessages sets the "messages" field.
func (suo *SessionUpdateOne) SetMessages(m []message.Message) *SessionUpdateOne {
	suo.mutation.SetMessages(m)
	return suo
}

// AppendMessages appends m to the "messages" field.
func (suo *SessionUpdateOne) AppendMessages(m []message.Message) *SessionUpdateOne {
	suo.mutation.AppendMessages(m)
	return suo
}

// SetCreateAt sets the "create_at" field.
func (suo *SessionUpdateOne) SetCreateAt(s string) *SessionUpdateOne {
	suo.mutation.SetCreateAt(s)
	return suo
}

// SetNillableCreateAt sets the "create_at" field if the given value is not nil.
func (suo *SessionUpdateOne) SetNillableCreateAt(s *string) *SessionUpdateOne {
	if s != nil {
		suo.SetCreateAt(*s)
	}
	return suo
}

// SetUpdateAt sets the "update_at" field.
func (suo *SessionUpdateOne) SetUpdateAt(s string) *SessionUpdateOne {
	suo.mutation.SetUpdateAt(s)
	return suo
}

// SetNillableUpdateAt sets the "update_at" field if the given value is not nil.
func (suo *SessionUpdateOne) SetNillableUpdateAt(s *string) *SessionUpdateOne {
	if s != nil {
		suo.SetUpdateAt(*s)
	}
	return suo
}

// Mutation returns the SessionMutation object of the builder.
func (suo *SessionUpdateOne) Mutation() *SessionMutation {
	return suo.mutation
}

// Where appends a list predicates to the SessionUpdate builder.
func (suo *SessionUpdateOne) Where(ps ...predicate.Session) *SessionUpdateOne {
	suo.mutation.Where(ps...)
	return suo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (suo *SessionUpdateOne) Select(field string, fields ...string) *SessionUpdateOne {
	suo.fields = append([]string{field}, fields...)
	return suo
}

// Save executes the query and returns the updated Session entity.
func (suo *SessionUpdateOne) Save(ctx context.Context) (*Session, error) {
	return withHooks(ctx, suo.sqlSave, suo.mutation, suo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (suo *SessionUpdateOne) SaveX(ctx context.Context) *Session {
	node, err := suo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (suo *SessionUpdateOne) Exec(ctx context.Context) error {
	_, err := suo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (suo *SessionUpdateOne) ExecX(ctx context.Context) {
	if err := suo.Exec(ctx); err != nil {
		panic(err)
	}
}

func (suo *SessionUpdateOne) sqlSave(ctx context.Context) (_node *Session, err error) {
	_spec := sqlgraph.NewUpdateSpec(session.Table, session.Columns, sqlgraph.NewFieldSpec(session.FieldID, field.TypeString))
	id, ok := suo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "Session.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := suo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, session.FieldID)
		for _, f := range fields {
			if !session.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != session.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := suo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := suo.mutation.Messages(); ok {
		_spec.SetField(session.FieldMessages, field.TypeJSON, value)
	}
	if value, ok := suo.mutation.AppendedMessages(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, session.FieldMessages, value)
		})
	}
	if value, ok := suo.mutation.CreateAt(); ok {
		_spec.SetField(session.FieldCreateAt, field.TypeString, value)
	}
	if value, ok := suo.mutation.UpdateAt(); ok {
		_spec.SetField(session.FieldUpdateAt, field.TypeString, value)
	}
	_node = &Session{config: suo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, suo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{session.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	suo.mutation.done = true
	return _node, nil
}
