// Code generated by ent, DO NOT EDIT.

package session

import (
	"secwalk/internal/domain/repo/ent/predicate"

	"entgo.io/ent/dialect/sql"
)

// ID filters vertices based on their ID field.
func ID(id string) predicate.Session {
	return predicate.Session(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id string) predicate.Session {
	return predicate.Session(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id string) predicate.Session {
	return predicate.Session(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...string) predicate.Session {
	return predicate.Session(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...string) predicate.Session {
	return predicate.Session(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id string) predicate.Session {
	return predicate.Session(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id string) predicate.Session {
	return predicate.Session(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id string) predicate.Session {
	return predicate.Session(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id string) predicate.Session {
	return predicate.Session(sql.FieldLTE(FieldID, id))
}

// IDEqualFold applies the EqualFold predicate on the ID field.
func IDEqualFold(id string) predicate.Session {
	return predicate.Session(sql.FieldEqualFold(FieldID, id))
}

// IDContainsFold applies the ContainsFold predicate on the ID field.
func IDContainsFold(id string) predicate.Session {
	return predicate.Session(sql.FieldContainsFold(FieldID, id))
}

// CreateAt applies equality check predicate on the "create_at" field. It's identical to CreateAtEQ.
func CreateAt(v string) predicate.Session {
	return predicate.Session(sql.FieldEQ(FieldCreateAt, v))
}

// UpdateAt applies equality check predicate on the "update_at" field. It's identical to UpdateAtEQ.
func UpdateAt(v string) predicate.Session {
	return predicate.Session(sql.FieldEQ(FieldUpdateAt, v))
}

// CreateAtEQ applies the EQ predicate on the "create_at" field.
func CreateAtEQ(v string) predicate.Session {
	return predicate.Session(sql.FieldEQ(FieldCreateAt, v))
}

// CreateAtNEQ applies the NEQ predicate on the "create_at" field.
func CreateAtNEQ(v string) predicate.Session {
	return predicate.Session(sql.FieldNEQ(FieldCreateAt, v))
}

// CreateAtIn applies the In predicate on the "create_at" field.
func CreateAtIn(vs ...string) predicate.Session {
	return predicate.Session(sql.FieldIn(FieldCreateAt, vs...))
}

// CreateAtNotIn applies the NotIn predicate on the "create_at" field.
func CreateAtNotIn(vs ...string) predicate.Session {
	return predicate.Session(sql.FieldNotIn(FieldCreateAt, vs...))
}

// CreateAtGT applies the GT predicate on the "create_at" field.
func CreateAtGT(v string) predicate.Session {
	return predicate.Session(sql.FieldGT(FieldCreateAt, v))
}

// CreateAtGTE applies the GTE predicate on the "create_at" field.
func CreateAtGTE(v string) predicate.Session {
	return predicate.Session(sql.FieldGTE(FieldCreateAt, v))
}

// CreateAtLT applies the LT predicate on the "create_at" field.
func CreateAtLT(v string) predicate.Session {
	return predicate.Session(sql.FieldLT(FieldCreateAt, v))
}

// CreateAtLTE applies the LTE predicate on the "create_at" field.
func CreateAtLTE(v string) predicate.Session {
	return predicate.Session(sql.FieldLTE(FieldCreateAt, v))
}

// CreateAtContains applies the Contains predicate on the "create_at" field.
func CreateAtContains(v string) predicate.Session {
	return predicate.Session(sql.FieldContains(FieldCreateAt, v))
}

// CreateAtHasPrefix applies the HasPrefix predicate on the "create_at" field.
func CreateAtHasPrefix(v string) predicate.Session {
	return predicate.Session(sql.FieldHasPrefix(FieldCreateAt, v))
}

// CreateAtHasSuffix applies the HasSuffix predicate on the "create_at" field.
func CreateAtHasSuffix(v string) predicate.Session {
	return predicate.Session(sql.FieldHasSuffix(FieldCreateAt, v))
}

// CreateAtEqualFold applies the EqualFold predicate on the "create_at" field.
func CreateAtEqualFold(v string) predicate.Session {
	return predicate.Session(sql.FieldEqualFold(FieldCreateAt, v))
}

// CreateAtContainsFold applies the ContainsFold predicate on the "create_at" field.
func CreateAtContainsFold(v string) predicate.Session {
	return predicate.Session(sql.FieldContainsFold(FieldCreateAt, v))
}

// UpdateAtEQ applies the EQ predicate on the "update_at" field.
func UpdateAtEQ(v string) predicate.Session {
	return predicate.Session(sql.FieldEQ(FieldUpdateAt, v))
}

// UpdateAtNEQ applies the NEQ predicate on the "update_at" field.
func UpdateAtNEQ(v string) predicate.Session {
	return predicate.Session(sql.FieldNEQ(FieldUpdateAt, v))
}

// UpdateAtIn applies the In predicate on the "update_at" field.
func UpdateAtIn(vs ...string) predicate.Session {
	return predicate.Session(sql.FieldIn(FieldUpdateAt, vs...))
}

// UpdateAtNotIn applies the NotIn predicate on the "update_at" field.
func UpdateAtNotIn(vs ...string) predicate.Session {
	return predicate.Session(sql.FieldNotIn(FieldUpdateAt, vs...))
}

// UpdateAtGT applies the GT predicate on the "update_at" field.
func UpdateAtGT(v string) predicate.Session {
	return predicate.Session(sql.FieldGT(FieldUpdateAt, v))
}

// UpdateAtGTE applies the GTE predicate on the "update_at" field.
func UpdateAtGTE(v string) predicate.Session {
	return predicate.Session(sql.FieldGTE(FieldUpdateAt, v))
}

// UpdateAtLT applies the LT predicate on the "update_at" field.
func UpdateAtLT(v string) predicate.Session {
	return predicate.Session(sql.FieldLT(FieldUpdateAt, v))
}

// UpdateAtLTE applies the LTE predicate on the "update_at" field.
func UpdateAtLTE(v string) predicate.Session {
	return predicate.Session(sql.FieldLTE(FieldUpdateAt, v))
}

// UpdateAtContains applies the Contains predicate on the "update_at" field.
func UpdateAtContains(v string) predicate.Session {
	return predicate.Session(sql.FieldContains(FieldUpdateAt, v))
}

// UpdateAtHasPrefix applies the HasPrefix predicate on the "update_at" field.
func UpdateAtHasPrefix(v string) predicate.Session {
	return predicate.Session(sql.FieldHasPrefix(FieldUpdateAt, v))
}

// UpdateAtHasSuffix applies the HasSuffix predicate on the "update_at" field.
func UpdateAtHasSuffix(v string) predicate.Session {
	return predicate.Session(sql.FieldHasSuffix(FieldUpdateAt, v))
}

// UpdateAtEqualFold applies the EqualFold predicate on the "update_at" field.
func UpdateAtEqualFold(v string) predicate.Session {
	return predicate.Session(sql.FieldEqualFold(FieldUpdateAt, v))
}

// UpdateAtContainsFold applies the ContainsFold predicate on the "update_at" field.
func UpdateAtContainsFold(v string) predicate.Session {
	return predicate.Session(sql.FieldContainsFold(FieldUpdateAt, v))
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.Session) predicate.Session {
	return predicate.Session(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.Session) predicate.Session {
	return predicate.Session(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.Session) predicate.Session {
	return predicate.Session(sql.NotPredicates(p))
}
