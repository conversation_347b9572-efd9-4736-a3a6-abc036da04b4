// Code generated by ent, DO NOT EDIT.

package sign

import (
	"secwalk/internal/domain/repo/ent/predicate"

	"entgo.io/ent/dialect/sql"
)

// ID filters vertices based on their ID field.
func ID(id int) predicate.Sign {
	return predicate.Sign(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id int) predicate.Sign {
	return predicate.Sign(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id int) predicate.Sign {
	return predicate.Sign(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...int) predicate.Sign {
	return predicate.Sign(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...int) predicate.Sign {
	return predicate.Sign(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id int) predicate.Sign {
	return predicate.Sign(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id int) predicate.Sign {
	return predicate.Sign(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id int) predicate.Sign {
	return predicate.Sign(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id int) predicate.Sign {
	return predicate.Sign(sql.FieldLTE(FieldID, id))
}

// Name applies equality check predicate on the "name" field. It's identical to NameEQ.
func Name(v string) predicate.Sign {
	return predicate.Sign(sql.FieldEQ(FieldName, v))
}

// Secret applies equality check predicate on the "secret" field. It's identical to SecretEQ.
func Secret(v string) predicate.Sign {
	return predicate.Sign(sql.FieldEQ(FieldSecret, v))
}

// Apikey applies equality check predicate on the "apikey" field. It's identical to ApikeyEQ.
func Apikey(v string) predicate.Sign {
	return predicate.Sign(sql.FieldEQ(FieldApikey, v))
}

// NameEQ applies the EQ predicate on the "name" field.
func NameEQ(v string) predicate.Sign {
	return predicate.Sign(sql.FieldEQ(FieldName, v))
}

// NameNEQ applies the NEQ predicate on the "name" field.
func NameNEQ(v string) predicate.Sign {
	return predicate.Sign(sql.FieldNEQ(FieldName, v))
}

// NameIn applies the In predicate on the "name" field.
func NameIn(vs ...string) predicate.Sign {
	return predicate.Sign(sql.FieldIn(FieldName, vs...))
}

// NameNotIn applies the NotIn predicate on the "name" field.
func NameNotIn(vs ...string) predicate.Sign {
	return predicate.Sign(sql.FieldNotIn(FieldName, vs...))
}

// NameGT applies the GT predicate on the "name" field.
func NameGT(v string) predicate.Sign {
	return predicate.Sign(sql.FieldGT(FieldName, v))
}

// NameGTE applies the GTE predicate on the "name" field.
func NameGTE(v string) predicate.Sign {
	return predicate.Sign(sql.FieldGTE(FieldName, v))
}

// NameLT applies the LT predicate on the "name" field.
func NameLT(v string) predicate.Sign {
	return predicate.Sign(sql.FieldLT(FieldName, v))
}

// NameLTE applies the LTE predicate on the "name" field.
func NameLTE(v string) predicate.Sign {
	return predicate.Sign(sql.FieldLTE(FieldName, v))
}

// NameContains applies the Contains predicate on the "name" field.
func NameContains(v string) predicate.Sign {
	return predicate.Sign(sql.FieldContains(FieldName, v))
}

// NameHasPrefix applies the HasPrefix predicate on the "name" field.
func NameHasPrefix(v string) predicate.Sign {
	return predicate.Sign(sql.FieldHasPrefix(FieldName, v))
}

// NameHasSuffix applies the HasSuffix predicate on the "name" field.
func NameHasSuffix(v string) predicate.Sign {
	return predicate.Sign(sql.FieldHasSuffix(FieldName, v))
}

// NameEqualFold applies the EqualFold predicate on the "name" field.
func NameEqualFold(v string) predicate.Sign {
	return predicate.Sign(sql.FieldEqualFold(FieldName, v))
}

// NameContainsFold applies the ContainsFold predicate on the "name" field.
func NameContainsFold(v string) predicate.Sign {
	return predicate.Sign(sql.FieldContainsFold(FieldName, v))
}

// SecretEQ applies the EQ predicate on the "secret" field.
func SecretEQ(v string) predicate.Sign {
	return predicate.Sign(sql.FieldEQ(FieldSecret, v))
}

// SecretNEQ applies the NEQ predicate on the "secret" field.
func SecretNEQ(v string) predicate.Sign {
	return predicate.Sign(sql.FieldNEQ(FieldSecret, v))
}

// SecretIn applies the In predicate on the "secret" field.
func SecretIn(vs ...string) predicate.Sign {
	return predicate.Sign(sql.FieldIn(FieldSecret, vs...))
}

// SecretNotIn applies the NotIn predicate on the "secret" field.
func SecretNotIn(vs ...string) predicate.Sign {
	return predicate.Sign(sql.FieldNotIn(FieldSecret, vs...))
}

// SecretGT applies the GT predicate on the "secret" field.
func SecretGT(v string) predicate.Sign {
	return predicate.Sign(sql.FieldGT(FieldSecret, v))
}

// SecretGTE applies the GTE predicate on the "secret" field.
func SecretGTE(v string) predicate.Sign {
	return predicate.Sign(sql.FieldGTE(FieldSecret, v))
}

// SecretLT applies the LT predicate on the "secret" field.
func SecretLT(v string) predicate.Sign {
	return predicate.Sign(sql.FieldLT(FieldSecret, v))
}

// SecretLTE applies the LTE predicate on the "secret" field.
func SecretLTE(v string) predicate.Sign {
	return predicate.Sign(sql.FieldLTE(FieldSecret, v))
}

// SecretContains applies the Contains predicate on the "secret" field.
func SecretContains(v string) predicate.Sign {
	return predicate.Sign(sql.FieldContains(FieldSecret, v))
}

// SecretHasPrefix applies the HasPrefix predicate on the "secret" field.
func SecretHasPrefix(v string) predicate.Sign {
	return predicate.Sign(sql.FieldHasPrefix(FieldSecret, v))
}

// SecretHasSuffix applies the HasSuffix predicate on the "secret" field.
func SecretHasSuffix(v string) predicate.Sign {
	return predicate.Sign(sql.FieldHasSuffix(FieldSecret, v))
}

// SecretEqualFold applies the EqualFold predicate on the "secret" field.
func SecretEqualFold(v string) predicate.Sign {
	return predicate.Sign(sql.FieldEqualFold(FieldSecret, v))
}

// SecretContainsFold applies the ContainsFold predicate on the "secret" field.
func SecretContainsFold(v string) predicate.Sign {
	return predicate.Sign(sql.FieldContainsFold(FieldSecret, v))
}

// ApikeyEQ applies the EQ predicate on the "apikey" field.
func ApikeyEQ(v string) predicate.Sign {
	return predicate.Sign(sql.FieldEQ(FieldApikey, v))
}

// ApikeyNEQ applies the NEQ predicate on the "apikey" field.
func ApikeyNEQ(v string) predicate.Sign {
	return predicate.Sign(sql.FieldNEQ(FieldApikey, v))
}

// ApikeyIn applies the In predicate on the "apikey" field.
func ApikeyIn(vs ...string) predicate.Sign {
	return predicate.Sign(sql.FieldIn(FieldApikey, vs...))
}

// ApikeyNotIn applies the NotIn predicate on the "apikey" field.
func ApikeyNotIn(vs ...string) predicate.Sign {
	return predicate.Sign(sql.FieldNotIn(FieldApikey, vs...))
}

// ApikeyGT applies the GT predicate on the "apikey" field.
func ApikeyGT(v string) predicate.Sign {
	return predicate.Sign(sql.FieldGT(FieldApikey, v))
}

// ApikeyGTE applies the GTE predicate on the "apikey" field.
func ApikeyGTE(v string) predicate.Sign {
	return predicate.Sign(sql.FieldGTE(FieldApikey, v))
}

// ApikeyLT applies the LT predicate on the "apikey" field.
func ApikeyLT(v string) predicate.Sign {
	return predicate.Sign(sql.FieldLT(FieldApikey, v))
}

// ApikeyLTE applies the LTE predicate on the "apikey" field.
func ApikeyLTE(v string) predicate.Sign {
	return predicate.Sign(sql.FieldLTE(FieldApikey, v))
}

// ApikeyContains applies the Contains predicate on the "apikey" field.
func ApikeyContains(v string) predicate.Sign {
	return predicate.Sign(sql.FieldContains(FieldApikey, v))
}

// ApikeyHasPrefix applies the HasPrefix predicate on the "apikey" field.
func ApikeyHasPrefix(v string) predicate.Sign {
	return predicate.Sign(sql.FieldHasPrefix(FieldApikey, v))
}

// ApikeyHasSuffix applies the HasSuffix predicate on the "apikey" field.
func ApikeyHasSuffix(v string) predicate.Sign {
	return predicate.Sign(sql.FieldHasSuffix(FieldApikey, v))
}

// ApikeyEqualFold applies the EqualFold predicate on the "apikey" field.
func ApikeyEqualFold(v string) predicate.Sign {
	return predicate.Sign(sql.FieldEqualFold(FieldApikey, v))
}

// ApikeyContainsFold applies the ContainsFold predicate on the "apikey" field.
func ApikeyContainsFold(v string) predicate.Sign {
	return predicate.Sign(sql.FieldContainsFold(FieldApikey, v))
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.Sign) predicate.Sign {
	return predicate.Sign(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.Sign) predicate.Sign {
	return predicate.Sign(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.Sign) predicate.Sign {
	return predicate.Sign(sql.NotPredicates(p))
}
