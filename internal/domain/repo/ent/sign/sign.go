// Code generated by ent, DO NOT EDIT.

package sign

import (
	"entgo.io/ent/dialect/sql"
)

const (
	// Label holds the string label denoting the sign type in the database.
	Label = "sign"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldName holds the string denoting the name field in the database.
	FieldName = "name"
	// FieldSecret holds the string denoting the secret field in the database.
	FieldSecret = "secret"
	// FieldApikey holds the string denoting the apikey field in the database.
	FieldApikey = "apikey"
	// Table holds the table name of the sign in the database.
	Table = "signs"
)

// Columns holds all SQL columns for sign fields.
var Columns = []string{
	FieldID,
	FieldName,
	FieldSecret,
	FieldApikey,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

var (
	// NameValidator is a validator for the "name" field. It is called by the builders before save.
	NameValidator func(string) error
	// SecretValidator is a validator for the "secret" field. It is called by the builders before save.
	SecretValidator func(string) error
	// ApikeyValidator is a validator for the "apikey" field. It is called by the builders before save.
	ApikeyValidator func(string) error
)

// OrderOption defines the ordering options for the Sign queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByName orders the results by the name field.
func ByName(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldName, opts...).ToFunc()
}

// BySecret orders the results by the secret field.
func BySecret(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldSecret, opts...).ToFunc()
}

// ByApikey orders the results by the apikey field.
func ByApikey(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldApikey, opts...).ToFunc()
}
