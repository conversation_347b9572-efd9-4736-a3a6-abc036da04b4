// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"secwalk/internal/domain/repo/ent/predicate"
	"secwalk/internal/domain/repo/ent/trait"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// TraitUpdate is the builder for updating Trait entities.
type TraitUpdate struct {
	config
	hooks    []Hook
	mutation *TraitMutation
}

// Where appends a list predicates to the TraitUpdate builder.
func (tu *TraitUpdate) Where(ps ...predicate.Trait) *TraitUpdate {
	tu.mutation.Where(ps...)
	return tu
}

// SetText sets the "text" field.
func (tu *TraitUpdate) SetText(s string) *TraitUpdate {
	tu.mutation.SetText(s)
	return tu
}

// SetNillableText sets the "text" field if the given value is not nil.
func (tu *TraitUpdate) SetNillableText(s *string) *TraitUpdate {
	if s != nil {
		tu.SetText(*s)
	}
	return tu
}

// SetType sets the "type" field.
func (tu *TraitUpdate) SetType(s string) *TraitUpdate {
	tu.mutation.SetType(s)
	return tu
}

// SetNillableType sets the "type" field if the given value is not nil.
func (tu *TraitUpdate) SetNillableType(s *string) *TraitUpdate {
	if s != nil {
		tu.SetType(*s)
	}
	return tu
}

// SetLevel sets the "level" field.
func (tu *TraitUpdate) SetLevel(i int) *TraitUpdate {
	tu.mutation.ResetLevel()
	tu.mutation.SetLevel(i)
	return tu
}

// SetNillableLevel sets the "level" field if the given value is not nil.
func (tu *TraitUpdate) SetNillableLevel(i *int) *TraitUpdate {
	if i != nil {
		tu.SetLevel(*i)
	}
	return tu
}

// AddLevel adds i to the "level" field.
func (tu *TraitUpdate) AddLevel(i int) *TraitUpdate {
	tu.mutation.AddLevel(i)
	return tu
}

// Mutation returns the TraitMutation object of the builder.
func (tu *TraitUpdate) Mutation() *TraitMutation {
	return tu.mutation
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (tu *TraitUpdate) Save(ctx context.Context) (int, error) {
	return withHooks(ctx, tu.sqlSave, tu.mutation, tu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (tu *TraitUpdate) SaveX(ctx context.Context) int {
	affected, err := tu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (tu *TraitUpdate) Exec(ctx context.Context) error {
	_, err := tu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (tu *TraitUpdate) ExecX(ctx context.Context) {
	if err := tu.Exec(ctx); err != nil {
		panic(err)
	}
}

// check runs all checks and user-defined validators on the builder.
func (tu *TraitUpdate) check() error {
	if v, ok := tu.mutation.Text(); ok {
		if err := trait.TextValidator(v); err != nil {
			return &ValidationError{Name: "text", err: fmt.Errorf(`ent: validator failed for field "Trait.text": %w`, err)}
		}
	}
	if v, ok := tu.mutation.GetType(); ok {
		if err := trait.TypeValidator(v); err != nil {
			return &ValidationError{Name: "type", err: fmt.Errorf(`ent: validator failed for field "Trait.type": %w`, err)}
		}
	}
	return nil
}

func (tu *TraitUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := tu.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(trait.Table, trait.Columns, sqlgraph.NewFieldSpec(trait.FieldID, field.TypeInt))
	if ps := tu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := tu.mutation.Text(); ok {
		_spec.SetField(trait.FieldText, field.TypeString, value)
	}
	if value, ok := tu.mutation.GetType(); ok {
		_spec.SetField(trait.FieldType, field.TypeString, value)
	}
	if value, ok := tu.mutation.Level(); ok {
		_spec.SetField(trait.FieldLevel, field.TypeInt, value)
	}
	if value, ok := tu.mutation.AddedLevel(); ok {
		_spec.AddField(trait.FieldLevel, field.TypeInt, value)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, tu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{trait.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	tu.mutation.done = true
	return n, nil
}

// TraitUpdateOne is the builder for updating a single Trait entity.
type TraitUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *TraitMutation
}

// SetText sets the "text" field.
func (tuo *TraitUpdateOne) SetText(s string) *TraitUpdateOne {
	tuo.mutation.SetText(s)
	return tuo
}

// SetNillableText sets the "text" field if the given value is not nil.
func (tuo *TraitUpdateOne) SetNillableText(s *string) *TraitUpdateOne {
	if s != nil {
		tuo.SetText(*s)
	}
	return tuo
}

// SetType sets the "type" field.
func (tuo *TraitUpdateOne) SetType(s string) *TraitUpdateOne {
	tuo.mutation.SetType(s)
	return tuo
}

// SetNillableType sets the "type" field if the given value is not nil.
func (tuo *TraitUpdateOne) SetNillableType(s *string) *TraitUpdateOne {
	if s != nil {
		tuo.SetType(*s)
	}
	return tuo
}

// SetLevel sets the "level" field.
func (tuo *TraitUpdateOne) SetLevel(i int) *TraitUpdateOne {
	tuo.mutation.ResetLevel()
	tuo.mutation.SetLevel(i)
	return tuo
}

// SetNillableLevel sets the "level" field if the given value is not nil.
func (tuo *TraitUpdateOne) SetNillableLevel(i *int) *TraitUpdateOne {
	if i != nil {
		tuo.SetLevel(*i)
	}
	return tuo
}

// AddLevel adds i to the "level" field.
func (tuo *TraitUpdateOne) AddLevel(i int) *TraitUpdateOne {
	tuo.mutation.AddLevel(i)
	return tuo
}

// Mutation returns the TraitMutation object of the builder.
func (tuo *TraitUpdateOne) Mutation() *TraitMutation {
	return tuo.mutation
}

// Where appends a list predicates to the TraitUpdate builder.
func (tuo *TraitUpdateOne) Where(ps ...predicate.Trait) *TraitUpdateOne {
	tuo.mutation.Where(ps...)
	return tuo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (tuo *TraitUpdateOne) Select(field string, fields ...string) *TraitUpdateOne {
	tuo.fields = append([]string{field}, fields...)
	return tuo
}

// Save executes the query and returns the updated Trait entity.
func (tuo *TraitUpdateOne) Save(ctx context.Context) (*Trait, error) {
	return withHooks(ctx, tuo.sqlSave, tuo.mutation, tuo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (tuo *TraitUpdateOne) SaveX(ctx context.Context) *Trait {
	node, err := tuo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (tuo *TraitUpdateOne) Exec(ctx context.Context) error {
	_, err := tuo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (tuo *TraitUpdateOne) ExecX(ctx context.Context) {
	if err := tuo.Exec(ctx); err != nil {
		panic(err)
	}
}

// check runs all checks and user-defined validators on the builder.
func (tuo *TraitUpdateOne) check() error {
	if v, ok := tuo.mutation.Text(); ok {
		if err := trait.TextValidator(v); err != nil {
			return &ValidationError{Name: "text", err: fmt.Errorf(`ent: validator failed for field "Trait.text": %w`, err)}
		}
	}
	if v, ok := tuo.mutation.GetType(); ok {
		if err := trait.TypeValidator(v); err != nil {
			return &ValidationError{Name: "type", err: fmt.Errorf(`ent: validator failed for field "Trait.type": %w`, err)}
		}
	}
	return nil
}

func (tuo *TraitUpdateOne) sqlSave(ctx context.Context) (_node *Trait, err error) {
	if err := tuo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(trait.Table, trait.Columns, sqlgraph.NewFieldSpec(trait.FieldID, field.TypeInt))
	id, ok := tuo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "Trait.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := tuo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, trait.FieldID)
		for _, f := range fields {
			if !trait.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != trait.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := tuo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := tuo.mutation.Text(); ok {
		_spec.SetField(trait.FieldText, field.TypeString, value)
	}
	if value, ok := tuo.mutation.GetType(); ok {
		_spec.SetField(trait.FieldType, field.TypeString, value)
	}
	if value, ok := tuo.mutation.Level(); ok {
		_spec.SetField(trait.FieldLevel, field.TypeInt, value)
	}
	if value, ok := tuo.mutation.AddedLevel(); ok {
		_spec.AddField(trait.FieldLevel, field.TypeInt, value)
	}
	_node = &Trait{config: tuo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, tuo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{trait.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	tuo.mutation.done = true
	return _node, nil
}
