// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"secwalk/internal/domain/repo/ent/term"

	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// TermCreate is the builder for creating a Term entity.
type TermCreate struct {
	config
	mutation *TermMutation
	hooks    []Hook
}

// SetCreateAt sets the "create_at" field.
func (tc *TermCreate) SetCreateAt(s string) *TermCreate {
	tc.mutation.SetCreateAt(s)
	return tc
}

// SetUpdateAt sets the "update_at" field.
func (tc *TermCreate) SetUpdateAt(s string) *TermCreate {
	tc.mutation.SetUpdateAt(s)
	return tc
}

// SetAid sets the "aid" field.
func (tc *TermCreate) SetAid(s string) *TermCreate {
	tc.mutation.SetAid(s)
	return tc
}

// SetOrg sets the "org" field.
func (tc *TermCreate) SetOrg(s string) *TermCreate {
	tc.mutation.SetOrg(s)
	return tc
}

// SetSid sets the "sid" field.
func (tc *TermCreate) SetSid(s string) *TermCreate {
	tc.mutation.SetSid(s)
	return tc
}

// SetContent sets the "content" field.
func (tc *TermCreate) SetContent(m map[string]interface{}) *TermCreate {
	tc.mutation.SetContent(m)
	return tc
}

// Mutation returns the TermMutation object of the builder.
func (tc *TermCreate) Mutation() *TermMutation {
	return tc.mutation
}

// Save creates the Term in the database.
func (tc *TermCreate) Save(ctx context.Context) (*Term, error) {
	return withHooks(ctx, tc.sqlSave, tc.mutation, tc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (tc *TermCreate) SaveX(ctx context.Context) *Term {
	v, err := tc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (tc *TermCreate) Exec(ctx context.Context) error {
	_, err := tc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (tc *TermCreate) ExecX(ctx context.Context) {
	if err := tc.Exec(ctx); err != nil {
		panic(err)
	}
}

// check runs all checks and user-defined validators on the builder.
func (tc *TermCreate) check() error {
	if _, ok := tc.mutation.CreateAt(); !ok {
		return &ValidationError{Name: "create_at", err: errors.New(`ent: missing required field "Term.create_at"`)}
	}
	if _, ok := tc.mutation.UpdateAt(); !ok {
		return &ValidationError{Name: "update_at", err: errors.New(`ent: missing required field "Term.update_at"`)}
	}
	if _, ok := tc.mutation.Aid(); !ok {
		return &ValidationError{Name: "aid", err: errors.New(`ent: missing required field "Term.aid"`)}
	}
	if v, ok := tc.mutation.Aid(); ok {
		if err := term.AidValidator(v); err != nil {
			return &ValidationError{Name: "aid", err: fmt.Errorf(`ent: validator failed for field "Term.aid": %w`, err)}
		}
	}
	if _, ok := tc.mutation.Org(); !ok {
		return &ValidationError{Name: "org", err: errors.New(`ent: missing required field "Term.org"`)}
	}
	if _, ok := tc.mutation.Sid(); !ok {
		return &ValidationError{Name: "sid", err: errors.New(`ent: missing required field "Term.sid"`)}
	}
	if _, ok := tc.mutation.Content(); !ok {
		return &ValidationError{Name: "content", err: errors.New(`ent: missing required field "Term.content"`)}
	}
	return nil
}

func (tc *TermCreate) sqlSave(ctx context.Context) (*Term, error) {
	if err := tc.check(); err != nil {
		return nil, err
	}
	_node, _spec := tc.createSpec()
	if err := sqlgraph.CreateNode(ctx, tc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	id := _spec.ID.Value.(int64)
	_node.ID = int(id)
	tc.mutation.id = &_node.ID
	tc.mutation.done = true
	return _node, nil
}

func (tc *TermCreate) createSpec() (*Term, *sqlgraph.CreateSpec) {
	var (
		_node = &Term{config: tc.config}
		_spec = sqlgraph.NewCreateSpec(term.Table, sqlgraph.NewFieldSpec(term.FieldID, field.TypeInt))
	)
	if value, ok := tc.mutation.CreateAt(); ok {
		_spec.SetField(term.FieldCreateAt, field.TypeString, value)
		_node.CreateAt = value
	}
	if value, ok := tc.mutation.UpdateAt(); ok {
		_spec.SetField(term.FieldUpdateAt, field.TypeString, value)
		_node.UpdateAt = value
	}
	if value, ok := tc.mutation.Aid(); ok {
		_spec.SetField(term.FieldAid, field.TypeString, value)
		_node.Aid = value
	}
	if value, ok := tc.mutation.Org(); ok {
		_spec.SetField(term.FieldOrg, field.TypeString, value)
		_node.Org = value
	}
	if value, ok := tc.mutation.Sid(); ok {
		_spec.SetField(term.FieldSid, field.TypeString, value)
		_node.Sid = value
	}
	if value, ok := tc.mutation.Content(); ok {
		_spec.SetField(term.FieldContent, field.TypeJSON, value)
		_node.Content = value
	}
	return _node, _spec
}

// TermCreateBulk is the builder for creating many Term entities in bulk.
type TermCreateBulk struct {
	config
	err      error
	builders []*TermCreate
}

// Save creates the Term entities in the database.
func (tcb *TermCreateBulk) Save(ctx context.Context) ([]*Term, error) {
	if tcb.err != nil {
		return nil, tcb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(tcb.builders))
	nodes := make([]*Term, len(tcb.builders))
	mutators := make([]Mutator, len(tcb.builders))
	for i := range tcb.builders {
		func(i int, root context.Context) {
			builder := tcb.builders[i]
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*TermMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, tcb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, tcb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = int(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, tcb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (tcb *TermCreateBulk) SaveX(ctx context.Context) []*Term {
	v, err := tcb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (tcb *TermCreateBulk) Exec(ctx context.Context) error {
	_, err := tcb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (tcb *TermCreateBulk) ExecX(ctx context.Context) {
	if err := tcb.Exec(ctx); err != nil {
		panic(err)
	}
}
