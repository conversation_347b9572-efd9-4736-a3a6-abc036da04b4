// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"secwalk/internal/domain/repo/ent/mcp"
	"secwalk/internal/domain/repo/ent/predicate"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/dialect/sql/sqljson"
	"entgo.io/ent/schema/field"
)

// McpUpdate is the builder for updating Mcp entities.
type McpUpdate struct {
	config
	hooks    []Hook
	mutation *McpMutation
}

// Where appends a list predicates to the McpUpdate builder.
func (mu *McpUpdate) Where(ps ...predicate.Mcp) *McpUpdate {
	mu.mutation.Where(ps...)
	return mu
}

// SetType sets the "type" field.
func (mu *McpUpdate) SetType(s string) *McpUpdate {
	mu.mutation.SetType(s)
	return mu
}

// SetNillableType sets the "type" field if the given value is not nil.
func (mu *McpUpdate) SetNillableType(s *string) *McpUpdate {
	if s != nil {
		mu.SetType(*s)
	}
	return mu
}

// SetName sets the "name" field.
func (mu *McpUpdate) SetName(s string) *McpUpdate {
	mu.mutation.SetName(s)
	return mu
}

// SetNillableName sets the "name" field if the given value is not nil.
func (mu *McpUpdate) SetNillableName(s *string) *McpUpdate {
	if s != nil {
		mu.SetName(*s)
	}
	return mu
}

// SetCommand sets the "command" field.
func (mu *McpUpdate) SetCommand(s string) *McpUpdate {
	mu.mutation.SetCommand(s)
	return mu
}

// SetNillableCommand sets the "command" field if the given value is not nil.
func (mu *McpUpdate) SetNillableCommand(s *string) *McpUpdate {
	if s != nil {
		mu.SetCommand(*s)
	}
	return mu
}

// ClearCommand clears the value of the "command" field.
func (mu *McpUpdate) ClearCommand() *McpUpdate {
	mu.mutation.ClearCommand()
	return mu
}

// SetArgs sets the "args" field.
func (mu *McpUpdate) SetArgs(s []string) *McpUpdate {
	mu.mutation.SetArgs(s)
	return mu
}

// AppendArgs appends s to the "args" field.
func (mu *McpUpdate) AppendArgs(s []string) *McpUpdate {
	mu.mutation.AppendArgs(s)
	return mu
}

// ClearArgs clears the value of the "args" field.
func (mu *McpUpdate) ClearArgs() *McpUpdate {
	mu.mutation.ClearArgs()
	return mu
}

// SetEnv sets the "env" field.
func (mu *McpUpdate) SetEnv(m map[string]string) *McpUpdate {
	mu.mutation.SetEnv(m)
	return mu
}

// ClearEnv clears the value of the "env" field.
func (mu *McpUpdate) ClearEnv() *McpUpdate {
	mu.mutation.ClearEnv()
	return mu
}

// SetURL sets the "url" field.
func (mu *McpUpdate) SetURL(s string) *McpUpdate {
	mu.mutation.SetURL(s)
	return mu
}

// SetNillableURL sets the "url" field if the given value is not nil.
func (mu *McpUpdate) SetNillableURL(s *string) *McpUpdate {
	if s != nil {
		mu.SetURL(*s)
	}
	return mu
}

// ClearURL clears the value of the "url" field.
func (mu *McpUpdate) ClearURL() *McpUpdate {
	mu.mutation.ClearURL()
	return mu
}

// SetHeaders sets the "headers" field.
func (mu *McpUpdate) SetHeaders(m map[string]string) *McpUpdate {
	mu.mutation.SetHeaders(m)
	return mu
}

// ClearHeaders clears the value of the "headers" field.
func (mu *McpUpdate) ClearHeaders() *McpUpdate {
	mu.mutation.ClearHeaders()
	return mu
}

// SetTimeout sets the "timeout" field.
func (mu *McpUpdate) SetTimeout(i int64) *McpUpdate {
	mu.mutation.ResetTimeout()
	mu.mutation.SetTimeout(i)
	return mu
}

// SetNillableTimeout sets the "timeout" field if the given value is not nil.
func (mu *McpUpdate) SetNillableTimeout(i *int64) *McpUpdate {
	if i != nil {
		mu.SetTimeout(*i)
	}
	return mu
}

// AddTimeout adds i to the "timeout" field.
func (mu *McpUpdate) AddTimeout(i int64) *McpUpdate {
	mu.mutation.AddTimeout(i)
	return mu
}

// Mutation returns the McpMutation object of the builder.
func (mu *McpUpdate) Mutation() *McpMutation {
	return mu.mutation
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (mu *McpUpdate) Save(ctx context.Context) (int, error) {
	return withHooks(ctx, mu.sqlSave, mu.mutation, mu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (mu *McpUpdate) SaveX(ctx context.Context) int {
	affected, err := mu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (mu *McpUpdate) Exec(ctx context.Context) error {
	_, err := mu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (mu *McpUpdate) ExecX(ctx context.Context) {
	if err := mu.Exec(ctx); err != nil {
		panic(err)
	}
}

// check runs all checks and user-defined validators on the builder.
func (mu *McpUpdate) check() error {
	if v, ok := mu.mutation.GetType(); ok {
		if err := mcp.TypeValidator(v); err != nil {
			return &ValidationError{Name: "type", err: fmt.Errorf(`ent: validator failed for field "Mcp.type": %w`, err)}
		}
	}
	if v, ok := mu.mutation.Name(); ok {
		if err := mcp.NameValidator(v); err != nil {
			return &ValidationError{Name: "name", err: fmt.Errorf(`ent: validator failed for field "Mcp.name": %w`, err)}
		}
	}
	return nil
}

func (mu *McpUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := mu.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(mcp.Table, mcp.Columns, sqlgraph.NewFieldSpec(mcp.FieldID, field.TypeString))
	if ps := mu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := mu.mutation.GetType(); ok {
		_spec.SetField(mcp.FieldType, field.TypeString, value)
	}
	if value, ok := mu.mutation.Name(); ok {
		_spec.SetField(mcp.FieldName, field.TypeString, value)
	}
	if value, ok := mu.mutation.Command(); ok {
		_spec.SetField(mcp.FieldCommand, field.TypeString, value)
	}
	if mu.mutation.CommandCleared() {
		_spec.ClearField(mcp.FieldCommand, field.TypeString)
	}
	if value, ok := mu.mutation.Args(); ok {
		_spec.SetField(mcp.FieldArgs, field.TypeJSON, value)
	}
	if value, ok := mu.mutation.AppendedArgs(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, mcp.FieldArgs, value)
		})
	}
	if mu.mutation.ArgsCleared() {
		_spec.ClearField(mcp.FieldArgs, field.TypeJSON)
	}
	if value, ok := mu.mutation.Env(); ok {
		_spec.SetField(mcp.FieldEnv, field.TypeJSON, value)
	}
	if mu.mutation.EnvCleared() {
		_spec.ClearField(mcp.FieldEnv, field.TypeJSON)
	}
	if value, ok := mu.mutation.URL(); ok {
		_spec.SetField(mcp.FieldURL, field.TypeString, value)
	}
	if mu.mutation.URLCleared() {
		_spec.ClearField(mcp.FieldURL, field.TypeString)
	}
	if value, ok := mu.mutation.Headers(); ok {
		_spec.SetField(mcp.FieldHeaders, field.TypeJSON, value)
	}
	if mu.mutation.HeadersCleared() {
		_spec.ClearField(mcp.FieldHeaders, field.TypeJSON)
	}
	if value, ok := mu.mutation.Timeout(); ok {
		_spec.SetField(mcp.FieldTimeout, field.TypeInt64, value)
	}
	if value, ok := mu.mutation.AddedTimeout(); ok {
		_spec.AddField(mcp.FieldTimeout, field.TypeInt64, value)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, mu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{mcp.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	mu.mutation.done = true
	return n, nil
}

// McpUpdateOne is the builder for updating a single Mcp entity.
type McpUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *McpMutation
}

// SetType sets the "type" field.
func (muo *McpUpdateOne) SetType(s string) *McpUpdateOne {
	muo.mutation.SetType(s)
	return muo
}

// SetNillableType sets the "type" field if the given value is not nil.
func (muo *McpUpdateOne) SetNillableType(s *string) *McpUpdateOne {
	if s != nil {
		muo.SetType(*s)
	}
	return muo
}

// SetName sets the "name" field.
func (muo *McpUpdateOne) SetName(s string) *McpUpdateOne {
	muo.mutation.SetName(s)
	return muo
}

// SetNillableName sets the "name" field if the given value is not nil.
func (muo *McpUpdateOne) SetNillableName(s *string) *McpUpdateOne {
	if s != nil {
		muo.SetName(*s)
	}
	return muo
}

// SetCommand sets the "command" field.
func (muo *McpUpdateOne) SetCommand(s string) *McpUpdateOne {
	muo.mutation.SetCommand(s)
	return muo
}

// SetNillableCommand sets the "command" field if the given value is not nil.
func (muo *McpUpdateOne) SetNillableCommand(s *string) *McpUpdateOne {
	if s != nil {
		muo.SetCommand(*s)
	}
	return muo
}

// ClearCommand clears the value of the "command" field.
func (muo *McpUpdateOne) ClearCommand() *McpUpdateOne {
	muo.mutation.ClearCommand()
	return muo
}

// SetArgs sets the "args" field.
func (muo *McpUpdateOne) SetArgs(s []string) *McpUpdateOne {
	muo.mutation.SetArgs(s)
	return muo
}

// AppendArgs appends s to the "args" field.
func (muo *McpUpdateOne) AppendArgs(s []string) *McpUpdateOne {
	muo.mutation.AppendArgs(s)
	return muo
}

// ClearArgs clears the value of the "args" field.
func (muo *McpUpdateOne) ClearArgs() *McpUpdateOne {
	muo.mutation.ClearArgs()
	return muo
}

// SetEnv sets the "env" field.
func (muo *McpUpdateOne) SetEnv(m map[string]string) *McpUpdateOne {
	muo.mutation.SetEnv(m)
	return muo
}

// ClearEnv clears the value of the "env" field.
func (muo *McpUpdateOne) ClearEnv() *McpUpdateOne {
	muo.mutation.ClearEnv()
	return muo
}

// SetURL sets the "url" field.
func (muo *McpUpdateOne) SetURL(s string) *McpUpdateOne {
	muo.mutation.SetURL(s)
	return muo
}

// SetNillableURL sets the "url" field if the given value is not nil.
func (muo *McpUpdateOne) SetNillableURL(s *string) *McpUpdateOne {
	if s != nil {
		muo.SetURL(*s)
	}
	return muo
}

// ClearURL clears the value of the "url" field.
func (muo *McpUpdateOne) ClearURL() *McpUpdateOne {
	muo.mutation.ClearURL()
	return muo
}

// SetHeaders sets the "headers" field.
func (muo *McpUpdateOne) SetHeaders(m map[string]string) *McpUpdateOne {
	muo.mutation.SetHeaders(m)
	return muo
}

// ClearHeaders clears the value of the "headers" field.
func (muo *McpUpdateOne) ClearHeaders() *McpUpdateOne {
	muo.mutation.ClearHeaders()
	return muo
}

// SetTimeout sets the "timeout" field.
func (muo *McpUpdateOne) SetTimeout(i int64) *McpUpdateOne {
	muo.mutation.ResetTimeout()
	muo.mutation.SetTimeout(i)
	return muo
}

// SetNillableTimeout sets the "timeout" field if the given value is not nil.
func (muo *McpUpdateOne) SetNillableTimeout(i *int64) *McpUpdateOne {
	if i != nil {
		muo.SetTimeout(*i)
	}
	return muo
}

// AddTimeout adds i to the "timeout" field.
func (muo *McpUpdateOne) AddTimeout(i int64) *McpUpdateOne {
	muo.mutation.AddTimeout(i)
	return muo
}

// Mutation returns the McpMutation object of the builder.
func (muo *McpUpdateOne) Mutation() *McpMutation {
	return muo.mutation
}

// Where appends a list predicates to the McpUpdate builder.
func (muo *McpUpdateOne) Where(ps ...predicate.Mcp) *McpUpdateOne {
	muo.mutation.Where(ps...)
	return muo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (muo *McpUpdateOne) Select(field string, fields ...string) *McpUpdateOne {
	muo.fields = append([]string{field}, fields...)
	return muo
}

// Save executes the query and returns the updated Mcp entity.
func (muo *McpUpdateOne) Save(ctx context.Context) (*Mcp, error) {
	return withHooks(ctx, muo.sqlSave, muo.mutation, muo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (muo *McpUpdateOne) SaveX(ctx context.Context) *Mcp {
	node, err := muo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (muo *McpUpdateOne) Exec(ctx context.Context) error {
	_, err := muo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (muo *McpUpdateOne) ExecX(ctx context.Context) {
	if err := muo.Exec(ctx); err != nil {
		panic(err)
	}
}

// check runs all checks and user-defined validators on the builder.
func (muo *McpUpdateOne) check() error {
	if v, ok := muo.mutation.GetType(); ok {
		if err := mcp.TypeValidator(v); err != nil {
			return &ValidationError{Name: "type", err: fmt.Errorf(`ent: validator failed for field "Mcp.type": %w`, err)}
		}
	}
	if v, ok := muo.mutation.Name(); ok {
		if err := mcp.NameValidator(v); err != nil {
			return &ValidationError{Name: "name", err: fmt.Errorf(`ent: validator failed for field "Mcp.name": %w`, err)}
		}
	}
	return nil
}

func (muo *McpUpdateOne) sqlSave(ctx context.Context) (_node *Mcp, err error) {
	if err := muo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(mcp.Table, mcp.Columns, sqlgraph.NewFieldSpec(mcp.FieldID, field.TypeString))
	id, ok := muo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "Mcp.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := muo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, mcp.FieldID)
		for _, f := range fields {
			if !mcp.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != mcp.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := muo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := muo.mutation.GetType(); ok {
		_spec.SetField(mcp.FieldType, field.TypeString, value)
	}
	if value, ok := muo.mutation.Name(); ok {
		_spec.SetField(mcp.FieldName, field.TypeString, value)
	}
	if value, ok := muo.mutation.Command(); ok {
		_spec.SetField(mcp.FieldCommand, field.TypeString, value)
	}
	if muo.mutation.CommandCleared() {
		_spec.ClearField(mcp.FieldCommand, field.TypeString)
	}
	if value, ok := muo.mutation.Args(); ok {
		_spec.SetField(mcp.FieldArgs, field.TypeJSON, value)
	}
	if value, ok := muo.mutation.AppendedArgs(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, mcp.FieldArgs, value)
		})
	}
	if muo.mutation.ArgsCleared() {
		_spec.ClearField(mcp.FieldArgs, field.TypeJSON)
	}
	if value, ok := muo.mutation.Env(); ok {
		_spec.SetField(mcp.FieldEnv, field.TypeJSON, value)
	}
	if muo.mutation.EnvCleared() {
		_spec.ClearField(mcp.FieldEnv, field.TypeJSON)
	}
	if value, ok := muo.mutation.URL(); ok {
		_spec.SetField(mcp.FieldURL, field.TypeString, value)
	}
	if muo.mutation.URLCleared() {
		_spec.ClearField(mcp.FieldURL, field.TypeString)
	}
	if value, ok := muo.mutation.Headers(); ok {
		_spec.SetField(mcp.FieldHeaders, field.TypeJSON, value)
	}
	if muo.mutation.HeadersCleared() {
		_spec.ClearField(mcp.FieldHeaders, field.TypeJSON)
	}
	if value, ok := muo.mutation.Timeout(); ok {
		_spec.SetField(mcp.FieldTimeout, field.TypeInt64, value)
	}
	if value, ok := muo.mutation.AddedTimeout(); ok {
		_spec.AddField(mcp.FieldTimeout, field.TypeInt64, value)
	}
	_node = &Mcp{config: muo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, muo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{mcp.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	muo.mutation.done = true
	return _node, nil
}
