// Code generated by ent, DO NOT EDIT.

package term

import (
	"secwalk/internal/domain/repo/ent/predicate"

	"entgo.io/ent/dialect/sql"
)

// ID filters vertices based on their ID field.
func ID(id int) predicate.Term {
	return predicate.Term(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id int) predicate.Term {
	return predicate.Term(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id int) predicate.Term {
	return predicate.Term(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...int) predicate.Term {
	return predicate.Term(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...int) predicate.Term {
	return predicate.Term(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id int) predicate.Term {
	return predicate.Term(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id int) predicate.Term {
	return predicate.Term(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id int) predicate.Term {
	return predicate.Term(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id int) predicate.Term {
	return predicate.Term(sql.FieldLTE(FieldID, id))
}

// CreateAt applies equality check predicate on the "create_at" field. It's identical to CreateAtEQ.
func CreateAt(v string) predicate.Term {
	return predicate.Term(sql.FieldEQ(FieldCreateAt, v))
}

// UpdateAt applies equality check predicate on the "update_at" field. It's identical to UpdateAtEQ.
func UpdateAt(v string) predicate.Term {
	return predicate.Term(sql.FieldEQ(FieldUpdateAt, v))
}

// Aid applies equality check predicate on the "aid" field. It's identical to AidEQ.
func Aid(v string) predicate.Term {
	return predicate.Term(sql.FieldEQ(FieldAid, v))
}

// Org applies equality check predicate on the "org" field. It's identical to OrgEQ.
func Org(v string) predicate.Term {
	return predicate.Term(sql.FieldEQ(FieldOrg, v))
}

// Sid applies equality check predicate on the "sid" field. It's identical to SidEQ.
func Sid(v string) predicate.Term {
	return predicate.Term(sql.FieldEQ(FieldSid, v))
}

// CreateAtEQ applies the EQ predicate on the "create_at" field.
func CreateAtEQ(v string) predicate.Term {
	return predicate.Term(sql.FieldEQ(FieldCreateAt, v))
}

// CreateAtNEQ applies the NEQ predicate on the "create_at" field.
func CreateAtNEQ(v string) predicate.Term {
	return predicate.Term(sql.FieldNEQ(FieldCreateAt, v))
}

// CreateAtIn applies the In predicate on the "create_at" field.
func CreateAtIn(vs ...string) predicate.Term {
	return predicate.Term(sql.FieldIn(FieldCreateAt, vs...))
}

// CreateAtNotIn applies the NotIn predicate on the "create_at" field.
func CreateAtNotIn(vs ...string) predicate.Term {
	return predicate.Term(sql.FieldNotIn(FieldCreateAt, vs...))
}

// CreateAtGT applies the GT predicate on the "create_at" field.
func CreateAtGT(v string) predicate.Term {
	return predicate.Term(sql.FieldGT(FieldCreateAt, v))
}

// CreateAtGTE applies the GTE predicate on the "create_at" field.
func CreateAtGTE(v string) predicate.Term {
	return predicate.Term(sql.FieldGTE(FieldCreateAt, v))
}

// CreateAtLT applies the LT predicate on the "create_at" field.
func CreateAtLT(v string) predicate.Term {
	return predicate.Term(sql.FieldLT(FieldCreateAt, v))
}

// CreateAtLTE applies the LTE predicate on the "create_at" field.
func CreateAtLTE(v string) predicate.Term {
	return predicate.Term(sql.FieldLTE(FieldCreateAt, v))
}

// CreateAtContains applies the Contains predicate on the "create_at" field.
func CreateAtContains(v string) predicate.Term {
	return predicate.Term(sql.FieldContains(FieldCreateAt, v))
}

// CreateAtHasPrefix applies the HasPrefix predicate on the "create_at" field.
func CreateAtHasPrefix(v string) predicate.Term {
	return predicate.Term(sql.FieldHasPrefix(FieldCreateAt, v))
}

// CreateAtHasSuffix applies the HasSuffix predicate on the "create_at" field.
func CreateAtHasSuffix(v string) predicate.Term {
	return predicate.Term(sql.FieldHasSuffix(FieldCreateAt, v))
}

// CreateAtEqualFold applies the EqualFold predicate on the "create_at" field.
func CreateAtEqualFold(v string) predicate.Term {
	return predicate.Term(sql.FieldEqualFold(FieldCreateAt, v))
}

// CreateAtContainsFold applies the ContainsFold predicate on the "create_at" field.
func CreateAtContainsFold(v string) predicate.Term {
	return predicate.Term(sql.FieldContainsFold(FieldCreateAt, v))
}

// UpdateAtEQ applies the EQ predicate on the "update_at" field.
func UpdateAtEQ(v string) predicate.Term {
	return predicate.Term(sql.FieldEQ(FieldUpdateAt, v))
}

// UpdateAtNEQ applies the NEQ predicate on the "update_at" field.
func UpdateAtNEQ(v string) predicate.Term {
	return predicate.Term(sql.FieldNEQ(FieldUpdateAt, v))
}

// UpdateAtIn applies the In predicate on the "update_at" field.
func UpdateAtIn(vs ...string) predicate.Term {
	return predicate.Term(sql.FieldIn(FieldUpdateAt, vs...))
}

// UpdateAtNotIn applies the NotIn predicate on the "update_at" field.
func UpdateAtNotIn(vs ...string) predicate.Term {
	return predicate.Term(sql.FieldNotIn(FieldUpdateAt, vs...))
}

// UpdateAtGT applies the GT predicate on the "update_at" field.
func UpdateAtGT(v string) predicate.Term {
	return predicate.Term(sql.FieldGT(FieldUpdateAt, v))
}

// UpdateAtGTE applies the GTE predicate on the "update_at" field.
func UpdateAtGTE(v string) predicate.Term {
	return predicate.Term(sql.FieldGTE(FieldUpdateAt, v))
}

// UpdateAtLT applies the LT predicate on the "update_at" field.
func UpdateAtLT(v string) predicate.Term {
	return predicate.Term(sql.FieldLT(FieldUpdateAt, v))
}

// UpdateAtLTE applies the LTE predicate on the "update_at" field.
func UpdateAtLTE(v string) predicate.Term {
	return predicate.Term(sql.FieldLTE(FieldUpdateAt, v))
}

// UpdateAtContains applies the Contains predicate on the "update_at" field.
func UpdateAtContains(v string) predicate.Term {
	return predicate.Term(sql.FieldContains(FieldUpdateAt, v))
}

// UpdateAtHasPrefix applies the HasPrefix predicate on the "update_at" field.
func UpdateAtHasPrefix(v string) predicate.Term {
	return predicate.Term(sql.FieldHasPrefix(FieldUpdateAt, v))
}

// UpdateAtHasSuffix applies the HasSuffix predicate on the "update_at" field.
func UpdateAtHasSuffix(v string) predicate.Term {
	return predicate.Term(sql.FieldHasSuffix(FieldUpdateAt, v))
}

// UpdateAtEqualFold applies the EqualFold predicate on the "update_at" field.
func UpdateAtEqualFold(v string) predicate.Term {
	return predicate.Term(sql.FieldEqualFold(FieldUpdateAt, v))
}

// UpdateAtContainsFold applies the ContainsFold predicate on the "update_at" field.
func UpdateAtContainsFold(v string) predicate.Term {
	return predicate.Term(sql.FieldContainsFold(FieldUpdateAt, v))
}

// AidEQ applies the EQ predicate on the "aid" field.
func AidEQ(v string) predicate.Term {
	return predicate.Term(sql.FieldEQ(FieldAid, v))
}

// AidNEQ applies the NEQ predicate on the "aid" field.
func AidNEQ(v string) predicate.Term {
	return predicate.Term(sql.FieldNEQ(FieldAid, v))
}

// AidIn applies the In predicate on the "aid" field.
func AidIn(vs ...string) predicate.Term {
	return predicate.Term(sql.FieldIn(FieldAid, vs...))
}

// AidNotIn applies the NotIn predicate on the "aid" field.
func AidNotIn(vs ...string) predicate.Term {
	return predicate.Term(sql.FieldNotIn(FieldAid, vs...))
}

// AidGT applies the GT predicate on the "aid" field.
func AidGT(v string) predicate.Term {
	return predicate.Term(sql.FieldGT(FieldAid, v))
}

// AidGTE applies the GTE predicate on the "aid" field.
func AidGTE(v string) predicate.Term {
	return predicate.Term(sql.FieldGTE(FieldAid, v))
}

// AidLT applies the LT predicate on the "aid" field.
func AidLT(v string) predicate.Term {
	return predicate.Term(sql.FieldLT(FieldAid, v))
}

// AidLTE applies the LTE predicate on the "aid" field.
func AidLTE(v string) predicate.Term {
	return predicate.Term(sql.FieldLTE(FieldAid, v))
}

// AidContains applies the Contains predicate on the "aid" field.
func AidContains(v string) predicate.Term {
	return predicate.Term(sql.FieldContains(FieldAid, v))
}

// AidHasPrefix applies the HasPrefix predicate on the "aid" field.
func AidHasPrefix(v string) predicate.Term {
	return predicate.Term(sql.FieldHasPrefix(FieldAid, v))
}

// AidHasSuffix applies the HasSuffix predicate on the "aid" field.
func AidHasSuffix(v string) predicate.Term {
	return predicate.Term(sql.FieldHasSuffix(FieldAid, v))
}

// AidEqualFold applies the EqualFold predicate on the "aid" field.
func AidEqualFold(v string) predicate.Term {
	return predicate.Term(sql.FieldEqualFold(FieldAid, v))
}

// AidContainsFold applies the ContainsFold predicate on the "aid" field.
func AidContainsFold(v string) predicate.Term {
	return predicate.Term(sql.FieldContainsFold(FieldAid, v))
}

// OrgEQ applies the EQ predicate on the "org" field.
func OrgEQ(v string) predicate.Term {
	return predicate.Term(sql.FieldEQ(FieldOrg, v))
}

// OrgNEQ applies the NEQ predicate on the "org" field.
func OrgNEQ(v string) predicate.Term {
	return predicate.Term(sql.FieldNEQ(FieldOrg, v))
}

// OrgIn applies the In predicate on the "org" field.
func OrgIn(vs ...string) predicate.Term {
	return predicate.Term(sql.FieldIn(FieldOrg, vs...))
}

// OrgNotIn applies the NotIn predicate on the "org" field.
func OrgNotIn(vs ...string) predicate.Term {
	return predicate.Term(sql.FieldNotIn(FieldOrg, vs...))
}

// OrgGT applies the GT predicate on the "org" field.
func OrgGT(v string) predicate.Term {
	return predicate.Term(sql.FieldGT(FieldOrg, v))
}

// OrgGTE applies the GTE predicate on the "org" field.
func OrgGTE(v string) predicate.Term {
	return predicate.Term(sql.FieldGTE(FieldOrg, v))
}

// OrgLT applies the LT predicate on the "org" field.
func OrgLT(v string) predicate.Term {
	return predicate.Term(sql.FieldLT(FieldOrg, v))
}

// OrgLTE applies the LTE predicate on the "org" field.
func OrgLTE(v string) predicate.Term {
	return predicate.Term(sql.FieldLTE(FieldOrg, v))
}

// OrgContains applies the Contains predicate on the "org" field.
func OrgContains(v string) predicate.Term {
	return predicate.Term(sql.FieldContains(FieldOrg, v))
}

// OrgHasPrefix applies the HasPrefix predicate on the "org" field.
func OrgHasPrefix(v string) predicate.Term {
	return predicate.Term(sql.FieldHasPrefix(FieldOrg, v))
}

// OrgHasSuffix applies the HasSuffix predicate on the "org" field.
func OrgHasSuffix(v string) predicate.Term {
	return predicate.Term(sql.FieldHasSuffix(FieldOrg, v))
}

// OrgEqualFold applies the EqualFold predicate on the "org" field.
func OrgEqualFold(v string) predicate.Term {
	return predicate.Term(sql.FieldEqualFold(FieldOrg, v))
}

// OrgContainsFold applies the ContainsFold predicate on the "org" field.
func OrgContainsFold(v string) predicate.Term {
	return predicate.Term(sql.FieldContainsFold(FieldOrg, v))
}

// SidEQ applies the EQ predicate on the "sid" field.
func SidEQ(v string) predicate.Term {
	return predicate.Term(sql.FieldEQ(FieldSid, v))
}

// SidNEQ applies the NEQ predicate on the "sid" field.
func SidNEQ(v string) predicate.Term {
	return predicate.Term(sql.FieldNEQ(FieldSid, v))
}

// SidIn applies the In predicate on the "sid" field.
func SidIn(vs ...string) predicate.Term {
	return predicate.Term(sql.FieldIn(FieldSid, vs...))
}

// SidNotIn applies the NotIn predicate on the "sid" field.
func SidNotIn(vs ...string) predicate.Term {
	return predicate.Term(sql.FieldNotIn(FieldSid, vs...))
}

// SidGT applies the GT predicate on the "sid" field.
func SidGT(v string) predicate.Term {
	return predicate.Term(sql.FieldGT(FieldSid, v))
}

// SidGTE applies the GTE predicate on the "sid" field.
func SidGTE(v string) predicate.Term {
	return predicate.Term(sql.FieldGTE(FieldSid, v))
}

// SidLT applies the LT predicate on the "sid" field.
func SidLT(v string) predicate.Term {
	return predicate.Term(sql.FieldLT(FieldSid, v))
}

// SidLTE applies the LTE predicate on the "sid" field.
func SidLTE(v string) predicate.Term {
	return predicate.Term(sql.FieldLTE(FieldSid, v))
}

// SidContains applies the Contains predicate on the "sid" field.
func SidContains(v string) predicate.Term {
	return predicate.Term(sql.FieldContains(FieldSid, v))
}

// SidHasPrefix applies the HasPrefix predicate on the "sid" field.
func SidHasPrefix(v string) predicate.Term {
	return predicate.Term(sql.FieldHasPrefix(FieldSid, v))
}

// SidHasSuffix applies the HasSuffix predicate on the "sid" field.
func SidHasSuffix(v string) predicate.Term {
	return predicate.Term(sql.FieldHasSuffix(FieldSid, v))
}

// SidEqualFold applies the EqualFold predicate on the "sid" field.
func SidEqualFold(v string) predicate.Term {
	return predicate.Term(sql.FieldEqualFold(FieldSid, v))
}

// SidContainsFold applies the ContainsFold predicate on the "sid" field.
func SidContainsFold(v string) predicate.Term {
	return predicate.Term(sql.FieldContainsFold(FieldSid, v))
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.Term) predicate.Term {
	return predicate.Term(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.Term) predicate.Term {
	return predicate.Term(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.Term) predicate.Term {
	return predicate.Term(sql.NotPredicates(p))
}
