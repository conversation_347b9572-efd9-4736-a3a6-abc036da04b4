// Code generated by ent, DO NOT EDIT.

package term

import (
	"entgo.io/ent/dialect/sql"
)

const (
	// Label holds the string label denoting the term type in the database.
	Label = "term"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldCreateAt holds the string denoting the create_at field in the database.
	FieldCreateAt = "create_at"
	// FieldUpdateAt holds the string denoting the update_at field in the database.
	FieldUpdateAt = "update_at"
	// FieldAid holds the string denoting the aid field in the database.
	FieldAid = "aid"
	// FieldOrg holds the string denoting the org field in the database.
	FieldOrg = "org"
	// FieldSid holds the string denoting the sid field in the database.
	FieldSid = "sid"
	// FieldContent holds the string denoting the content field in the database.
	FieldContent = "content"
	// Table holds the table name of the term in the database.
	Table = "terms"
)

// Columns holds all SQL columns for term fields.
var Columns = []string{
	FieldID,
	FieldCreateAt,
	FieldUpdateAt,
	FieldAid,
	FieldOrg,
	FieldSid,
	FieldContent,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

var (
	// AidValidator is a validator for the "aid" field. It is called by the builders before save.
	AidValidator func(string) error
)

// OrderOption defines the ordering options for the Term queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByCreateAt orders the results by the create_at field.
func ByCreateAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreateAt, opts...).ToFunc()
}

// ByUpdateAt orders the results by the update_at field.
func ByUpdateAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdateAt, opts...).ToFunc()
}

// ByAid orders the results by the aid field.
func ByAid(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldAid, opts...).ToFunc()
}

// ByOrg orders the results by the org field.
func ByOrg(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldOrg, opts...).ToFunc()
}

// BySid orders the results by the sid field.
func BySid(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldSid, opts...).ToFunc()
}
