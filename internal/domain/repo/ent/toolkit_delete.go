// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"secwalk/internal/domain/repo/ent/predicate"
	"secwalk/internal/domain/repo/ent/toolkit"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// ToolkitDelete is the builder for deleting a Toolkit entity.
type ToolkitDelete struct {
	config
	hooks    []Hook
	mutation *ToolkitMutation
}

// Where appends a list predicates to the ToolkitDelete builder.
func (td *ToolkitDelete) Where(ps ...predicate.Toolkit) *ToolkitDelete {
	td.mutation.Where(ps...)
	return td
}

// Exec executes the deletion query and returns how many vertices were deleted.
func (td *ToolkitDelete) Exec(ctx context.Context) (int, error) {
	return withHooks(ctx, td.sqlExec, td.mutation, td.hooks)
}

// ExecX is like Exec, but panics if an error occurs.
func (td *ToolkitDelete) ExecX(ctx context.Context) int {
	n, err := td.Exec(ctx)
	if err != nil {
		panic(err)
	}
	return n
}

func (td *ToolkitDelete) sqlExec(ctx context.Context) (int, error) {
	_spec := sqlgraph.NewDeleteSpec(toolkit.Table, sqlgraph.NewFieldSpec(toolkit.FieldID, field.TypeString))
	if ps := td.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	affected, err := sqlgraph.DeleteNodes(ctx, td.driver, _spec)
	if err != nil && sqlgraph.IsConstraintError(err) {
		err = &ConstraintError{msg: err.Error(), wrap: err}
	}
	td.mutation.done = true
	return affected, err
}

// ToolkitDeleteOne is the builder for deleting a single Toolkit entity.
type ToolkitDeleteOne struct {
	td *ToolkitDelete
}

// Where appends a list predicates to the ToolkitDelete builder.
func (tdo *ToolkitDeleteOne) Where(ps ...predicate.Toolkit) *ToolkitDeleteOne {
	tdo.td.mutation.Where(ps...)
	return tdo
}

// Exec executes the deletion query.
func (tdo *ToolkitDeleteOne) Exec(ctx context.Context) error {
	n, err := tdo.td.Exec(ctx)
	switch {
	case err != nil:
		return err
	case n == 0:
		return &NotFoundError{toolkit.Label}
	default:
		return nil
	}
}

// ExecX is like Exec, but panics if an error occurs.
func (tdo *ToolkitDeleteOne) ExecX(ctx context.Context) {
	if err := tdo.Exec(ctx); err != nil {
		panic(err)
	}
}
