// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"secwalk/internal/domain/repo/ent/mcp"

	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// Mcp<PERSON><PERSON> is the builder for creating a Mcp entity.
type McpCreate struct {
	config
	mutation *McpMutation
	hooks    []Hook
}

// SetType sets the "type" field.
func (mc *McpCreate) SetType(s string) *McpCreate {
	mc.mutation.SetType(s)
	return mc
}

// SetName sets the "name" field.
func (mc *McpCreate) SetName(s string) *McpCreate {
	mc.mutation.SetName(s)
	return mc
}

// SetCommand sets the "command" field.
func (mc *McpCreate) SetCommand(s string) *McpCreate {
	mc.mutation.SetCommand(s)
	return mc
}

// SetNillableCommand sets the "command" field if the given value is not nil.
func (mc *McpCreate) SetNillableCommand(s *string) *McpCreate {
	if s != nil {
		mc.SetCommand(*s)
	}
	return mc
}

// SetArgs sets the "args" field.
func (mc *McpCreate) SetArgs(s []string) *McpCreate {
	mc.mutation.SetArgs(s)
	return mc
}

// SetEnv sets the "env" field.
func (mc *McpCreate) SetEnv(m map[string]string) *McpCreate {
	mc.mutation.SetEnv(m)
	return mc
}

// SetURL sets the "url" field.
func (mc *McpCreate) SetURL(s string) *McpCreate {
	mc.mutation.SetURL(s)
	return mc
}

// SetNillableURL sets the "url" field if the given value is not nil.
func (mc *McpCreate) SetNillableURL(s *string) *McpCreate {
	if s != nil {
		mc.SetURL(*s)
	}
	return mc
}

// SetHeaders sets the "headers" field.
func (mc *McpCreate) SetHeaders(m map[string]string) *McpCreate {
	mc.mutation.SetHeaders(m)
	return mc
}

// SetTimeout sets the "timeout" field.
func (mc *McpCreate) SetTimeout(i int64) *McpCreate {
	mc.mutation.SetTimeout(i)
	return mc
}

// SetID sets the "id" field.
func (mc *McpCreate) SetID(s string) *McpCreate {
	mc.mutation.SetID(s)
	return mc
}

// Mutation returns the McpMutation object of the builder.
func (mc *McpCreate) Mutation() *McpMutation {
	return mc.mutation
}

// Save creates the Mcp in the database.
func (mc *McpCreate) Save(ctx context.Context) (*Mcp, error) {
	return withHooks(ctx, mc.sqlSave, mc.mutation, mc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (mc *McpCreate) SaveX(ctx context.Context) *Mcp {
	v, err := mc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (mc *McpCreate) Exec(ctx context.Context) error {
	_, err := mc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (mc *McpCreate) ExecX(ctx context.Context) {
	if err := mc.Exec(ctx); err != nil {
		panic(err)
	}
}

// check runs all checks and user-defined validators on the builder.
func (mc *McpCreate) check() error {
	if _, ok := mc.mutation.GetType(); !ok {
		return &ValidationError{Name: "type", err: errors.New(`ent: missing required field "Mcp.type"`)}
	}
	if v, ok := mc.mutation.GetType(); ok {
		if err := mcp.TypeValidator(v); err != nil {
			return &ValidationError{Name: "type", err: fmt.Errorf(`ent: validator failed for field "Mcp.type": %w`, err)}
		}
	}
	if _, ok := mc.mutation.Name(); !ok {
		return &ValidationError{Name: "name", err: errors.New(`ent: missing required field "Mcp.name"`)}
	}
	if v, ok := mc.mutation.Name(); ok {
		if err := mcp.NameValidator(v); err != nil {
			return &ValidationError{Name: "name", err: fmt.Errorf(`ent: validator failed for field "Mcp.name": %w`, err)}
		}
	}
	if _, ok := mc.mutation.Timeout(); !ok {
		return &ValidationError{Name: "timeout", err: errors.New(`ent: missing required field "Mcp.timeout"`)}
	}
	if v, ok := mc.mutation.ID(); ok {
		if err := mcp.IDValidator(v); err != nil {
			return &ValidationError{Name: "id", err: fmt.Errorf(`ent: validator failed for field "Mcp.id": %w`, err)}
		}
	}
	return nil
}

func (mc *McpCreate) sqlSave(ctx context.Context) (*Mcp, error) {
	if err := mc.check(); err != nil {
		return nil, err
	}
	_node, _spec := mc.createSpec()
	if err := sqlgraph.CreateNode(ctx, mc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != nil {
		if id, ok := _spec.ID.Value.(string); ok {
			_node.ID = id
		} else {
			return nil, fmt.Errorf("unexpected Mcp.ID type: %T", _spec.ID.Value)
		}
	}
	mc.mutation.id = &_node.ID
	mc.mutation.done = true
	return _node, nil
}

func (mc *McpCreate) createSpec() (*Mcp, *sqlgraph.CreateSpec) {
	var (
		_node = &Mcp{config: mc.config}
		_spec = sqlgraph.NewCreateSpec(mcp.Table, sqlgraph.NewFieldSpec(mcp.FieldID, field.TypeString))
	)
	if id, ok := mc.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = id
	}
	if value, ok := mc.mutation.GetType(); ok {
		_spec.SetField(mcp.FieldType, field.TypeString, value)
		_node.Type = value
	}
	if value, ok := mc.mutation.Name(); ok {
		_spec.SetField(mcp.FieldName, field.TypeString, value)
		_node.Name = value
	}
	if value, ok := mc.mutation.Command(); ok {
		_spec.SetField(mcp.FieldCommand, field.TypeString, value)
		_node.Command = value
	}
	if value, ok := mc.mutation.Args(); ok {
		_spec.SetField(mcp.FieldArgs, field.TypeJSON, value)
		_node.Args = value
	}
	if value, ok := mc.mutation.Env(); ok {
		_spec.SetField(mcp.FieldEnv, field.TypeJSON, value)
		_node.Env = value
	}
	if value, ok := mc.mutation.URL(); ok {
		_spec.SetField(mcp.FieldURL, field.TypeString, value)
		_node.URL = value
	}
	if value, ok := mc.mutation.Headers(); ok {
		_spec.SetField(mcp.FieldHeaders, field.TypeJSON, value)
		_node.Headers = value
	}
	if value, ok := mc.mutation.Timeout(); ok {
		_spec.SetField(mcp.FieldTimeout, field.TypeInt64, value)
		_node.Timeout = value
	}
	return _node, _spec
}

// McpCreateBulk is the builder for creating many Mcp entities in bulk.
type McpCreateBulk struct {
	config
	err      error
	builders []*McpCreate
}

// Save creates the Mcp entities in the database.
func (mcb *McpCreateBulk) Save(ctx context.Context) ([]*Mcp, error) {
	if mcb.err != nil {
		return nil, mcb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(mcb.builders))
	nodes := make([]*Mcp, len(mcb.builders))
	mutators := make([]Mutator, len(mcb.builders))
	for i := range mcb.builders {
		func(i int, root context.Context) {
			builder := mcb.builders[i]
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*McpMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, mcb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, mcb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, mcb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (mcb *McpCreateBulk) SaveX(ctx context.Context) []*Mcp {
	v, err := mcb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (mcb *McpCreateBulk) Exec(ctx context.Context) error {
	_, err := mcb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (mcb *McpCreateBulk) ExecX(ctx context.Context) {
	if err := mcb.Exec(ctx); err != nil {
		panic(err)
	}
}
