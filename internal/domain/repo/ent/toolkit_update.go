// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"secwalk/internal/domain/repo/ent/predicate"
	"secwalk/internal/domain/repo/ent/toolkit"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/dialect/sql/sqljson"
	"entgo.io/ent/schema/field"
)

// ToolkitUpdate is the builder for updating Toolkit entities.
type ToolkitUpdate struct {
	config
	hooks    []Hook
	mutation *ToolkitMutation
}

// Where appends a list predicates to the ToolkitUpdate builder.
func (tu *ToolkitUpdate) Where(ps ...predicate.Toolkit) *ToolkitUpdate {
	tu.mutation.Where(ps...)
	return tu
}

// SetType sets the "type" field.
func (tu *ToolkitUpdate) SetType(s string) *ToolkitUpdate {
	tu.mutation.SetType(s)
	return tu
}

// SetNillableType sets the "type" field if the given value is not nil.
func (tu *ToolkitUpdate) SetNillableType(s *string) *ToolkitUpdate {
	if s != nil {
		tu.SetType(*s)
	}
	return tu
}

// SetTools sets the "tools" field.
func (tu *ToolkitUpdate) SetTools(s []string) *ToolkitUpdate {
	tu.mutation.SetTools(s)
	return tu
}

// AppendTools appends s to the "tools" field.
func (tu *ToolkitUpdate) AppendTools(s []string) *ToolkitUpdate {
	tu.mutation.AppendTools(s)
	return tu
}

// SetConfig sets the "config" field.
func (tu *ToolkitUpdate) SetConfig(s string) *ToolkitUpdate {
	tu.mutation.SetConfig(s)
	return tu
}

// SetNillableConfig sets the "config" field if the given value is not nil.
func (tu *ToolkitUpdate) SetNillableConfig(s *string) *ToolkitUpdate {
	if s != nil {
		tu.SetConfig(*s)
	}
	return tu
}

// Mutation returns the ToolkitMutation object of the builder.
func (tu *ToolkitUpdate) Mutation() *ToolkitMutation {
	return tu.mutation
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (tu *ToolkitUpdate) Save(ctx context.Context) (int, error) {
	return withHooks(ctx, tu.sqlSave, tu.mutation, tu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (tu *ToolkitUpdate) SaveX(ctx context.Context) int {
	affected, err := tu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (tu *ToolkitUpdate) Exec(ctx context.Context) error {
	_, err := tu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (tu *ToolkitUpdate) ExecX(ctx context.Context) {
	if err := tu.Exec(ctx); err != nil {
		panic(err)
	}
}

// check runs all checks and user-defined validators on the builder.
func (tu *ToolkitUpdate) check() error {
	if v, ok := tu.mutation.Config(); ok {
		if err := toolkit.ConfigValidator(v); err != nil {
			return &ValidationError{Name: "config", err: fmt.Errorf(`ent: validator failed for field "Toolkit.config": %w`, err)}
		}
	}
	return nil
}

func (tu *ToolkitUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := tu.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(toolkit.Table, toolkit.Columns, sqlgraph.NewFieldSpec(toolkit.FieldID, field.TypeString))
	if ps := tu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := tu.mutation.GetType(); ok {
		_spec.SetField(toolkit.FieldType, field.TypeString, value)
	}
	if value, ok := tu.mutation.Tools(); ok {
		_spec.SetField(toolkit.FieldTools, field.TypeJSON, value)
	}
	if value, ok := tu.mutation.AppendedTools(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, toolkit.FieldTools, value)
		})
	}
	if value, ok := tu.mutation.Config(); ok {
		_spec.SetField(toolkit.FieldConfig, field.TypeString, value)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, tu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{toolkit.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	tu.mutation.done = true
	return n, nil
}

// ToolkitUpdateOne is the builder for updating a single Toolkit entity.
type ToolkitUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *ToolkitMutation
}

// SetType sets the "type" field.
func (tuo *ToolkitUpdateOne) SetType(s string) *ToolkitUpdateOne {
	tuo.mutation.SetType(s)
	return tuo
}

// SetNillableType sets the "type" field if the given value is not nil.
func (tuo *ToolkitUpdateOne) SetNillableType(s *string) *ToolkitUpdateOne {
	if s != nil {
		tuo.SetType(*s)
	}
	return tuo
}

// SetTools sets the "tools" field.
func (tuo *ToolkitUpdateOne) SetTools(s []string) *ToolkitUpdateOne {
	tuo.mutation.SetTools(s)
	return tuo
}

// AppendTools appends s to the "tools" field.
func (tuo *ToolkitUpdateOne) AppendTools(s []string) *ToolkitUpdateOne {
	tuo.mutation.AppendTools(s)
	return tuo
}

// SetConfig sets the "config" field.
func (tuo *ToolkitUpdateOne) SetConfig(s string) *ToolkitUpdateOne {
	tuo.mutation.SetConfig(s)
	return tuo
}

// SetNillableConfig sets the "config" field if the given value is not nil.
func (tuo *ToolkitUpdateOne) SetNillableConfig(s *string) *ToolkitUpdateOne {
	if s != nil {
		tuo.SetConfig(*s)
	}
	return tuo
}

// Mutation returns the ToolkitMutation object of the builder.
func (tuo *ToolkitUpdateOne) Mutation() *ToolkitMutation {
	return tuo.mutation
}

// Where appends a list predicates to the ToolkitUpdate builder.
func (tuo *ToolkitUpdateOne) Where(ps ...predicate.Toolkit) *ToolkitUpdateOne {
	tuo.mutation.Where(ps...)
	return tuo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (tuo *ToolkitUpdateOne) Select(field string, fields ...string) *ToolkitUpdateOne {
	tuo.fields = append([]string{field}, fields...)
	return tuo
}

// Save executes the query and returns the updated Toolkit entity.
func (tuo *ToolkitUpdateOne) Save(ctx context.Context) (*Toolkit, error) {
	return withHooks(ctx, tuo.sqlSave, tuo.mutation, tuo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (tuo *ToolkitUpdateOne) SaveX(ctx context.Context) *Toolkit {
	node, err := tuo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (tuo *ToolkitUpdateOne) Exec(ctx context.Context) error {
	_, err := tuo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (tuo *ToolkitUpdateOne) ExecX(ctx context.Context) {
	if err := tuo.Exec(ctx); err != nil {
		panic(err)
	}
}

// check runs all checks and user-defined validators on the builder.
func (tuo *ToolkitUpdateOne) check() error {
	if v, ok := tuo.mutation.Config(); ok {
		if err := toolkit.ConfigValidator(v); err != nil {
			return &ValidationError{Name: "config", err: fmt.Errorf(`ent: validator failed for field "Toolkit.config": %w`, err)}
		}
	}
	return nil
}

func (tuo *ToolkitUpdateOne) sqlSave(ctx context.Context) (_node *Toolkit, err error) {
	if err := tuo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(toolkit.Table, toolkit.Columns, sqlgraph.NewFieldSpec(toolkit.FieldID, field.TypeString))
	id, ok := tuo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "Toolkit.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := tuo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, toolkit.FieldID)
		for _, f := range fields {
			if !toolkit.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != toolkit.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := tuo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := tuo.mutation.GetType(); ok {
		_spec.SetField(toolkit.FieldType, field.TypeString, value)
	}
	if value, ok := tuo.mutation.Tools(); ok {
		_spec.SetField(toolkit.FieldTools, field.TypeJSON, value)
	}
	if value, ok := tuo.mutation.AppendedTools(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, toolkit.FieldTools, value)
		})
	}
	if value, ok := tuo.mutation.Config(); ok {
		_spec.SetField(toolkit.FieldConfig, field.TypeString, value)
	}
	_node = &Toolkit{config: tuo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, tuo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{toolkit.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	tuo.mutation.done = true
	return _node, nil
}
