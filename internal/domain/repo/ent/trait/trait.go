// Code generated by ent, DO NOT EDIT.

package trait

import (
	"entgo.io/ent/dialect/sql"
)

const (
	// Label holds the string label denoting the trait type in the database.
	Label = "trait"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldText holds the string denoting the text field in the database.
	FieldText = "text"
	// FieldType holds the string denoting the type field in the database.
	FieldType = "type"
	// FieldLevel holds the string denoting the level field in the database.
	FieldLevel = "level"
	// Table holds the table name of the trait in the database.
	Table = "traits"
)

// Columns holds all SQL columns for trait fields.
var Columns = []string{
	FieldID,
	FieldText,
	FieldType,
	FieldLevel,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

var (
	// TextValidator is a validator for the "text" field. It is called by the builders before save.
	TextValidator func(string) error
	// TypeValidator is a validator for the "type" field. It is called by the builders before save.
	TypeValidator func(string) error
	// DefaultLevel holds the default value on creation for the "level" field.
	DefaultLevel int
)

// OrderOption defines the ordering options for the Trait queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByText orders the results by the text field.
func ByText(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldText, opts...).ToFunc()
}

// ByType orders the results by the type field.
func ByType(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldType, opts...).ToFunc()
}

// ByLevel orders the results by the level field.
func ByLevel(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldLevel, opts...).ToFunc()
}
