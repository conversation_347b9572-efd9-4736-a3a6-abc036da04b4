// Code generated by ent, DO NOT EDIT.

package ent

import (
	"fmt"
	"secwalk/internal/domain/repo/ent/agent"
	"strings"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

// Agent is the model entity for the Agent schema.
type Agent struct {
	config `json:"-"`
	// ID of the ent.
	ID string `json:"id,omitempty"`
	// Type holds the value of the "type" field.
	Type int `json:"type,omitempty"`
	// Version holds the value of the "version" field.
	Version string `json:"version,omitempty"`
	// EngVersion holds the value of the "eng_version" field.
	EngVersion string `json:"eng_version,omitempty"`
	// Config holds the value of the "config" field.
	Config string `json:"config,omitempty"`
	// Extension holds the value of the "extension" field.
	Extension    string `json:"extension,omitempty"`
	selectValues sql.SelectValues
}

// scanValues returns the types for scanning values from sql.Rows.
func (*Agent) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case agent.FieldType:
			values[i] = new(sql.NullInt64)
		case agent.FieldID, agent.FieldVersion, agent.FieldEngVersion, agent.FieldConfig, agent.FieldExtension:
			values[i] = new(sql.NullString)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the Agent fields.
func (a *Agent) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case agent.FieldID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field id", values[i])
			} else if value.Valid {
				a.ID = value.String
			}
		case agent.FieldType:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field type", values[i])
			} else if value.Valid {
				a.Type = int(value.Int64)
			}
		case agent.FieldVersion:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field version", values[i])
			} else if value.Valid {
				a.Version = value.String
			}
		case agent.FieldEngVersion:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field eng_version", values[i])
			} else if value.Valid {
				a.EngVersion = value.String
			}
		case agent.FieldConfig:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field config", values[i])
			} else if value.Valid {
				a.Config = value.String
			}
		case agent.FieldExtension:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field extension", values[i])
			} else if value.Valid {
				a.Extension = value.String
			}
		default:
			a.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the Agent.
// This includes values selected through modifiers, order, etc.
func (a *Agent) Value(name string) (ent.Value, error) {
	return a.selectValues.Get(name)
}

// Update returns a builder for updating this Agent.
// Note that you need to call Agent.Unwrap() before calling this method if this Agent
// was returned from a transaction, and the transaction was committed or rolled back.
func (a *Agent) Update() *AgentUpdateOne {
	return NewAgentClient(a.config).UpdateOne(a)
}

// Unwrap unwraps the Agent entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (a *Agent) Unwrap() *Agent {
	_tx, ok := a.config.driver.(*txDriver)
	if !ok {
		panic("ent: Agent is not a transactional entity")
	}
	a.config.driver = _tx.drv
	return a
}

// String implements the fmt.Stringer.
func (a *Agent) String() string {
	var builder strings.Builder
	builder.WriteString("Agent(")
	builder.WriteString(fmt.Sprintf("id=%v, ", a.ID))
	builder.WriteString("type=")
	builder.WriteString(fmt.Sprintf("%v", a.Type))
	builder.WriteString(", ")
	builder.WriteString("version=")
	builder.WriteString(a.Version)
	builder.WriteString(", ")
	builder.WriteString("eng_version=")
	builder.WriteString(a.EngVersion)
	builder.WriteString(", ")
	builder.WriteString("config=")
	builder.WriteString(a.Config)
	builder.WriteString(", ")
	builder.WriteString("extension=")
	builder.WriteString(a.Extension)
	builder.WriteByte(')')
	return builder.String()
}

// Agents is a parsable slice of Agent.
type Agents []*Agent
