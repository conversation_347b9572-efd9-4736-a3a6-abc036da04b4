// Code generated by ent, DO NOT EDIT.

package migrate

import (
	"entgo.io/ent/dialect/sql/schema"
	"entgo.io/ent/schema/field"
)

var (
	// AgentsColumns holds the columns for the "agents" table.
	AgentsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString, Unique: true},
		{Name: "type", Type: field.TypeInt, Default: 0},
		{Name: "version", Type: field.TypeString},
		{Name: "eng_version", Type: field.TypeString},
		{Name: "config", Type: field.TypeString, Size: 2147483647},
		{Name: "extension", Type: field.TypeString, Nullable: true, Size: 2147483647},
	}
	// AgentsTable holds the schema information for the "agents" table.
	AgentsTable = &schema.Table{
		Name:       "agents",
		Columns:    AgentsColumns,
		PrimaryKey: []*schema.Column{AgentsColumns[0]},
		Indexes: []*schema.Index{
			{
				Name:    "agent_id",
				Unique:  true,
				Columns: []*schema.Column{AgentsColumns[0]},
			},
		},
	}
	// McpsColumns holds the columns for the "mcps" table.
	McpsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString, Unique: true},
		{Name: "type", Type: field.TypeString},
		{Name: "name", Type: field.TypeString},
		{Name: "command", Type: field.TypeString, Nullable: true},
		{Name: "args", Type: field.TypeJSON, Nullable: true},
		{Name: "env", Type: field.TypeJSON, Nullable: true},
		{Name: "url", Type: field.TypeString, Nullable: true},
		{Name: "headers", Type: field.TypeJSON, Nullable: true},
		{Name: "timeout", Type: field.TypeInt64},
	}
	// McpsTable holds the schema information for the "mcps" table.
	McpsTable = &schema.Table{
		Name:       "mcps",
		Columns:    McpsColumns,
		PrimaryKey: []*schema.Column{McpsColumns[0]},
	}
	// SessionsColumns holds the columns for the "sessions" table.
	SessionsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString, Unique: true},
		{Name: "messages", Type: field.TypeJSON},
		{Name: "create_at", Type: field.TypeString},
		{Name: "update_at", Type: field.TypeString},
	}
	// SessionsTable holds the schema information for the "sessions" table.
	SessionsTable = &schema.Table{
		Name:       "sessions",
		Columns:    SessionsColumns,
		PrimaryKey: []*schema.Column{SessionsColumns[0]},
		Indexes: []*schema.Index{
			{
				Name:    "session_id",
				Unique:  true,
				Columns: []*schema.Column{SessionsColumns[0]},
			},
		},
	}
	// SignsColumns holds the columns for the "signs" table.
	SignsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "name", Type: field.TypeString, Unique: true},
		{Name: "secret", Type: field.TypeString, Unique: true},
		{Name: "apikey", Type: field.TypeString, Unique: true},
	}
	// SignsTable holds the schema information for the "signs" table.
	SignsTable = &schema.Table{
		Name:       "signs",
		Columns:    SignsColumns,
		PrimaryKey: []*schema.Column{SignsColumns[0]},
	}
	// TermsColumns holds the columns for the "terms" table.
	TermsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "create_at", Type: field.TypeString},
		{Name: "update_at", Type: field.TypeString},
		{Name: "aid", Type: field.TypeString},
		{Name: "org", Type: field.TypeString},
		{Name: "sid", Type: field.TypeString},
		{Name: "content", Type: field.TypeJSON},
	}
	// TermsTable holds the schema information for the "terms" table.
	TermsTable = &schema.Table{
		Name:       "terms",
		Columns:    TermsColumns,
		PrimaryKey: []*schema.Column{TermsColumns[0]},
		Indexes: []*schema.Index{
			{
				Name:    "term_aid_org",
				Unique:  false,
				Columns: []*schema.Column{TermsColumns[3], TermsColumns[4]},
			},
		},
	}
	// ToolkitsColumns holds the columns for the "toolkits" table.
	ToolkitsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString, Unique: true},
		{Name: "type", Type: field.TypeString, Default: "custom"},
		{Name: "tools", Type: field.TypeJSON},
		{Name: "config", Type: field.TypeString, Size: 2147483647},
	}
	// ToolkitsTable holds the schema information for the "toolkits" table.
	ToolkitsTable = &schema.Table{
		Name:       "toolkits",
		Columns:    ToolkitsColumns,
		PrimaryKey: []*schema.Column{ToolkitsColumns[0]},
	}
	// TraitsColumns holds the columns for the "traits" table.
	TraitsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "text", Type: field.TypeString, Unique: true},
		{Name: "type", Type: field.TypeString},
		{Name: "level", Type: field.TypeInt, Default: 0},
	}
	// TraitsTable holds the schema information for the "traits" table.
	TraitsTable = &schema.Table{
		Name:       "traits",
		Columns:    TraitsColumns,
		PrimaryKey: []*schema.Column{TraitsColumns[0]},
	}
	// Tables holds all the tables in the schema.
	Tables = []*schema.Table{
		AgentsTable,
		McpsTable,
		SessionsTable,
		SignsTable,
		TermsTable,
		ToolkitsTable,
		TraitsTable,
	}
)

func init() {
}
