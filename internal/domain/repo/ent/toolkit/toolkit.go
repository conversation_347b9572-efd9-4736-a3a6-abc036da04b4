// Code generated by ent, DO NOT EDIT.

package toolkit

import (
	"entgo.io/ent/dialect/sql"
)

const (
	// Label holds the string label denoting the toolkit type in the database.
	Label = "toolkit"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldType holds the string denoting the type field in the database.
	FieldType = "type"
	// FieldTools holds the string denoting the tools field in the database.
	FieldTools = "tools"
	// FieldConfig holds the string denoting the config field in the database.
	FieldConfig = "config"
	// Table holds the table name of the toolkit in the database.
	Table = "toolkits"
)

// Columns holds all SQL columns for toolkit fields.
var Columns = []string{
	FieldID,
	FieldType,
	FieldTools,
	FieldConfig,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

var (
	// DefaultType holds the default value on creation for the "type" field.
	DefaultType string
	// ConfigValidator is a validator for the "config" field. It is called by the builders before save.
	ConfigValidator func(string) error
	// IDValidator is a validator for the "id" field. It is called by the builders before save.
	IDValidator func(string) error
)

// OrderOption defines the ordering options for the Toolkit queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByType orders the results by the type field.
func ByType(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldType, opts...).ToFunc()
}

// ByConfig orders the results by the config field.
func ByConfig(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldConfig, opts...).ToFunc()
}
