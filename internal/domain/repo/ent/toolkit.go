// Code generated by ent, DO NOT EDIT.

package ent

import (
	"encoding/json"
	"fmt"
	"secwalk/internal/domain/repo/ent/toolkit"
	"strings"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

// Toolkit is the model entity for the Toolkit schema.
type Toolkit struct {
	config `json:"-"`
	// ID of the ent.
	ID string `json:"id,omitempty"`
	// Type holds the value of the "type" field.
	Type string `json:"type,omitempty"`
	// Tools holds the value of the "tools" field.
	Tools []string `json:"tools,omitempty"`
	// Config holds the value of the "config" field.
	Config       string `json:"config,omitempty"`
	selectValues sql.SelectValues
}

// scanValues returns the types for scanning values from sql.Rows.
func (*Toolkit) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case toolkit.FieldTools:
			values[i] = new([]byte)
		case toolkit.FieldID, toolkit.FieldType, toolkit.FieldConfig:
			values[i] = new(sql.NullString)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the Toolkit fields.
func (t *Toolkit) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case toolkit.FieldID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field id", values[i])
			} else if value.Valid {
				t.ID = value.String
			}
		case toolkit.FieldType:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field type", values[i])
			} else if value.Valid {
				t.Type = value.String
			}
		case toolkit.FieldTools:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field tools", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &t.Tools); err != nil {
					return fmt.Errorf("unmarshal field tools: %w", err)
				}
			}
		case toolkit.FieldConfig:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field config", values[i])
			} else if value.Valid {
				t.Config = value.String
			}
		default:
			t.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the Toolkit.
// This includes values selected through modifiers, order, etc.
func (t *Toolkit) Value(name string) (ent.Value, error) {
	return t.selectValues.Get(name)
}

// Update returns a builder for updating this Toolkit.
// Note that you need to call Toolkit.Unwrap() before calling this method if this Toolkit
// was returned from a transaction, and the transaction was committed or rolled back.
func (t *Toolkit) Update() *ToolkitUpdateOne {
	return NewToolkitClient(t.config).UpdateOne(t)
}

// Unwrap unwraps the Toolkit entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (t *Toolkit) Unwrap() *Toolkit {
	_tx, ok := t.config.driver.(*txDriver)
	if !ok {
		panic("ent: Toolkit is not a transactional entity")
	}
	t.config.driver = _tx.drv
	return t
}

// String implements the fmt.Stringer.
func (t *Toolkit) String() string {
	var builder strings.Builder
	builder.WriteString("Toolkit(")
	builder.WriteString(fmt.Sprintf("id=%v, ", t.ID))
	builder.WriteString("type=")
	builder.WriteString(t.Type)
	builder.WriteString(", ")
	builder.WriteString("tools=")
	builder.WriteString(fmt.Sprintf("%v", t.Tools))
	builder.WriteString(", ")
	builder.WriteString("config=")
	builder.WriteString(t.Config)
	builder.WriteByte(')')
	return builder.String()
}

// Toolkits is a parsable slice of Toolkit.
type Toolkits []*Toolkit
