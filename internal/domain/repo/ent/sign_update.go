// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"secwalk/internal/domain/repo/ent/predicate"
	"secwalk/internal/domain/repo/ent/sign"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// SignUpdate is the builder for updating Sign entities.
type SignUpdate struct {
	config
	hooks    []Hook
	mutation *SignMutation
}

// Where appends a list predicates to the SignUpdate builder.
func (su *SignUpdate) Where(ps ...predicate.Sign) *SignUpdate {
	su.mutation.Where(ps...)
	return su
}

// SetName sets the "name" field.
func (su *SignUpdate) SetName(s string) *SignUpdate {
	su.mutation.SetName(s)
	return su
}

// SetNillableName sets the "name" field if the given value is not nil.
func (su *SignUpdate) SetNillableName(s *string) *SignUpdate {
	if s != nil {
		su.SetName(*s)
	}
	return su
}

// SetSecret sets the "secret" field.
func (su *SignUpdate) SetSecret(s string) *SignUpdate {
	su.mutation.SetSecret(s)
	return su
}

// SetNillableSecret sets the "secret" field if the given value is not nil.
func (su *SignUpdate) SetNillableSecret(s *string) *SignUpdate {
	if s != nil {
		su.SetSecret(*s)
	}
	return su
}

// SetApikey sets the "apikey" field.
func (su *SignUpdate) SetApikey(s string) *SignUpdate {
	su.mutation.SetApikey(s)
	return su
}

// SetNillableApikey sets the "apikey" field if the given value is not nil.
func (su *SignUpdate) SetNillableApikey(s *string) *SignUpdate {
	if s != nil {
		su.SetApikey(*s)
	}
	return su
}

// Mutation returns the SignMutation object of the builder.
func (su *SignUpdate) Mutation() *SignMutation {
	return su.mutation
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (su *SignUpdate) Save(ctx context.Context) (int, error) {
	return withHooks(ctx, su.sqlSave, su.mutation, su.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (su *SignUpdate) SaveX(ctx context.Context) int {
	affected, err := su.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (su *SignUpdate) Exec(ctx context.Context) error {
	_, err := su.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (su *SignUpdate) ExecX(ctx context.Context) {
	if err := su.Exec(ctx); err != nil {
		panic(err)
	}
}

// check runs all checks and user-defined validators on the builder.
func (su *SignUpdate) check() error {
	if v, ok := su.mutation.Name(); ok {
		if err := sign.NameValidator(v); err != nil {
			return &ValidationError{Name: "name", err: fmt.Errorf(`ent: validator failed for field "Sign.name": %w`, err)}
		}
	}
	if v, ok := su.mutation.Secret(); ok {
		if err := sign.SecretValidator(v); err != nil {
			return &ValidationError{Name: "secret", err: fmt.Errorf(`ent: validator failed for field "Sign.secret": %w`, err)}
		}
	}
	if v, ok := su.mutation.Apikey(); ok {
		if err := sign.ApikeyValidator(v); err != nil {
			return &ValidationError{Name: "apikey", err: fmt.Errorf(`ent: validator failed for field "Sign.apikey": %w`, err)}
		}
	}
	return nil
}

func (su *SignUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := su.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(sign.Table, sign.Columns, sqlgraph.NewFieldSpec(sign.FieldID, field.TypeInt))
	if ps := su.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := su.mutation.Name(); ok {
		_spec.SetField(sign.FieldName, field.TypeString, value)
	}
	if value, ok := su.mutation.Secret(); ok {
		_spec.SetField(sign.FieldSecret, field.TypeString, value)
	}
	if value, ok := su.mutation.Apikey(); ok {
		_spec.SetField(sign.FieldApikey, field.TypeString, value)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, su.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{sign.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	su.mutation.done = true
	return n, nil
}

// SignUpdateOne is the builder for updating a single Sign entity.
type SignUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *SignMutation
}

// SetName sets the "name" field.
func (suo *SignUpdateOne) SetName(s string) *SignUpdateOne {
	suo.mutation.SetName(s)
	return suo
}

// SetNillableName sets the "name" field if the given value is not nil.
func (suo *SignUpdateOne) SetNillableName(s *string) *SignUpdateOne {
	if s != nil {
		suo.SetName(*s)
	}
	return suo
}

// SetSecret sets the "secret" field.
func (suo *SignUpdateOne) SetSecret(s string) *SignUpdateOne {
	suo.mutation.SetSecret(s)
	return suo
}

// SetNillableSecret sets the "secret" field if the given value is not nil.
func (suo *SignUpdateOne) SetNillableSecret(s *string) *SignUpdateOne {
	if s != nil {
		suo.SetSecret(*s)
	}
	return suo
}

// SetApikey sets the "apikey" field.
func (suo *SignUpdateOne) SetApikey(s string) *SignUpdateOne {
	suo.mutation.SetApikey(s)
	return suo
}

// SetNillableApikey sets the "apikey" field if the given value is not nil.
func (suo *SignUpdateOne) SetNillableApikey(s *string) *SignUpdateOne {
	if s != nil {
		suo.SetApikey(*s)
	}
	return suo
}

// Mutation returns the SignMutation object of the builder.
func (suo *SignUpdateOne) Mutation() *SignMutation {
	return suo.mutation
}

// Where appends a list predicates to the SignUpdate builder.
func (suo *SignUpdateOne) Where(ps ...predicate.Sign) *SignUpdateOne {
	suo.mutation.Where(ps...)
	return suo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (suo *SignUpdateOne) Select(field string, fields ...string) *SignUpdateOne {
	suo.fields = append([]string{field}, fields...)
	return suo
}

// Save executes the query and returns the updated Sign entity.
func (suo *SignUpdateOne) Save(ctx context.Context) (*Sign, error) {
	return withHooks(ctx, suo.sqlSave, suo.mutation, suo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (suo *SignUpdateOne) SaveX(ctx context.Context) *Sign {
	node, err := suo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (suo *SignUpdateOne) Exec(ctx context.Context) error {
	_, err := suo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (suo *SignUpdateOne) ExecX(ctx context.Context) {
	if err := suo.Exec(ctx); err != nil {
		panic(err)
	}
}

// check runs all checks and user-defined validators on the builder.
func (suo *SignUpdateOne) check() error {
	if v, ok := suo.mutation.Name(); ok {
		if err := sign.NameValidator(v); err != nil {
			return &ValidationError{Name: "name", err: fmt.Errorf(`ent: validator failed for field "Sign.name": %w`, err)}
		}
	}
	if v, ok := suo.mutation.Secret(); ok {
		if err := sign.SecretValidator(v); err != nil {
			return &ValidationError{Name: "secret", err: fmt.Errorf(`ent: validator failed for field "Sign.secret": %w`, err)}
		}
	}
	if v, ok := suo.mutation.Apikey(); ok {
		if err := sign.ApikeyValidator(v); err != nil {
			return &ValidationError{Name: "apikey", err: fmt.Errorf(`ent: validator failed for field "Sign.apikey": %w`, err)}
		}
	}
	return nil
}

func (suo *SignUpdateOne) sqlSave(ctx context.Context) (_node *Sign, err error) {
	if err := suo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(sign.Table, sign.Columns, sqlgraph.NewFieldSpec(sign.FieldID, field.TypeInt))
	id, ok := suo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "Sign.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := suo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, sign.FieldID)
		for _, f := range fields {
			if !sign.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != sign.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := suo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := suo.mutation.Name(); ok {
		_spec.SetField(sign.FieldName, field.TypeString, value)
	}
	if value, ok := suo.mutation.Secret(); ok {
		_spec.SetField(sign.FieldSecret, field.TypeString, value)
	}
	if value, ok := suo.mutation.Apikey(); ok {
		_spec.SetField(sign.FieldApikey, field.TypeString, value)
	}
	_node = &Sign{config: suo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, suo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{sign.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	suo.mutation.done = true
	return _node, nil
}
