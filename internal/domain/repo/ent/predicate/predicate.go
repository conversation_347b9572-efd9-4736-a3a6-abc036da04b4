// Code generated by ent, DO NOT EDIT.

package predicate

import (
	"entgo.io/ent/dialect/sql"
)

// Agent is the predicate function for agent builders.
type Agent func(*sql.Selector)

// Mcp is the predicate function for mcp builders.
type Mcp func(*sql.Selector)

// Session is the predicate function for session builders.
type Session func(*sql.Selector)

// Sign is the predicate function for sign builders.
type Sign func(*sql.Selector)

// Term is the predicate function for term builders.
type Term func(*sql.Selector)

// Toolkit is the predicate function for toolkit builders.
type Toolkit func(*sql.Selector)

// Trait is the predicate function for trait builders.
type Trait func(*sql.Selector)
