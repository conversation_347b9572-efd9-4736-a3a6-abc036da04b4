// Code generated by ent, DO NOT EDIT.

package agent

import (
	"entgo.io/ent/dialect/sql"
)

const (
	// Label holds the string label denoting the agent type in the database.
	Label = "agent"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldType holds the string denoting the type field in the database.
	FieldType = "type"
	// FieldVersion holds the string denoting the version field in the database.
	FieldVersion = "version"
	// FieldEngVersion holds the string denoting the eng_version field in the database.
	FieldEngVersion = "eng_version"
	// FieldConfig holds the string denoting the config field in the database.
	FieldConfig = "config"
	// FieldExtension holds the string denoting the extension field in the database.
	FieldExtension = "extension"
	// Table holds the table name of the agent in the database.
	Table = "agents"
)

// Columns holds all SQL columns for agent fields.
var Columns = []string{
	FieldID,
	FieldType,
	FieldVersion,
	FieldEngVersion,
	FieldConfig,
	FieldExtension,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

var (
	// DefaultType holds the default value on creation for the "type" field.
	DefaultType int
	// ConfigValidator is a validator for the "config" field. It is called by the builders before save.
	ConfigValidator func(string) error
	// IDValidator is a validator for the "id" field. It is called by the builders before save.
	IDValidator func(string) error
)

// OrderOption defines the ordering options for the Agent queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByType orders the results by the type field.
func ByType(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldType, opts...).ToFunc()
}

// ByVersion orders the results by the version field.
func ByVersion(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldVersion, opts...).ToFunc()
}

// ByEngVersion orders the results by the eng_version field.
func ByEngVersion(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldEngVersion, opts...).ToFunc()
}

// ByConfig orders the results by the config field.
func ByConfig(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldConfig, opts...).ToFunc()
}

// ByExtension orders the results by the extension field.
func ByExtension(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldExtension, opts...).ToFunc()
}
