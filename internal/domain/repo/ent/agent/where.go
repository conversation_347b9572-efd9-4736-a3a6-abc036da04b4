// Code generated by ent, DO NOT EDIT.

package agent

import (
	"secwalk/internal/domain/repo/ent/predicate"

	"entgo.io/ent/dialect/sql"
)

// ID filters vertices based on their ID field.
func ID(id string) predicate.Agent {
	return predicate.Agent(sql.FieldEQ(FieldID, id))
}

// ID<PERSON>Q applies the EQ predicate on the ID field.
func IDEQ(id string) predicate.Agent {
	return predicate.Agent(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id string) predicate.Agent {
	return predicate.Agent(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...string) predicate.Agent {
	return predicate.Agent(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...string) predicate.Agent {
	return predicate.Agent(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id string) predicate.Agent {
	return predicate.Agent(sql.FieldGT(FieldID, id))
}

// IDG<PERSON> applies the GTE predicate on the ID field.
func IDGTE(id string) predicate.Agent {
	return predicate.Agent(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id string) predicate.Agent {
	return predicate.Agent(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id string) predicate.Agent {
	return predicate.Agent(sql.FieldLTE(FieldID, id))
}

// IDEqualFold applies the EqualFold predicate on the ID field.
func IDEqualFold(id string) predicate.Agent {
	return predicate.Agent(sql.FieldEqualFold(FieldID, id))
}

// IDContainsFold applies the ContainsFold predicate on the ID field.
func IDContainsFold(id string) predicate.Agent {
	return predicate.Agent(sql.FieldContainsFold(FieldID, id))
}

// Type applies equality check predicate on the "type" field. It's identical to TypeEQ.
func Type(v int) predicate.Agent {
	return predicate.Agent(sql.FieldEQ(FieldType, v))
}

// Version applies equality check predicate on the "version" field. It's identical to VersionEQ.
func Version(v string) predicate.Agent {
	return predicate.Agent(sql.FieldEQ(FieldVersion, v))
}

// EngVersion applies equality check predicate on the "eng_version" field. It's identical to EngVersionEQ.
func EngVersion(v string) predicate.Agent {
	return predicate.Agent(sql.FieldEQ(FieldEngVersion, v))
}

// Config applies equality check predicate on the "config" field. It's identical to ConfigEQ.
func Config(v string) predicate.Agent {
	return predicate.Agent(sql.FieldEQ(FieldConfig, v))
}

// Extension applies equality check predicate on the "extension" field. It's identical to ExtensionEQ.
func Extension(v string) predicate.Agent {
	return predicate.Agent(sql.FieldEQ(FieldExtension, v))
}

// TypeEQ applies the EQ predicate on the "type" field.
func TypeEQ(v int) predicate.Agent {
	return predicate.Agent(sql.FieldEQ(FieldType, v))
}

// TypeNEQ applies the NEQ predicate on the "type" field.
func TypeNEQ(v int) predicate.Agent {
	return predicate.Agent(sql.FieldNEQ(FieldType, v))
}

// TypeIn applies the In predicate on the "type" field.
func TypeIn(vs ...int) predicate.Agent {
	return predicate.Agent(sql.FieldIn(FieldType, vs...))
}

// TypeNotIn applies the NotIn predicate on the "type" field.
func TypeNotIn(vs ...int) predicate.Agent {
	return predicate.Agent(sql.FieldNotIn(FieldType, vs...))
}

// TypeGT applies the GT predicate on the "type" field.
func TypeGT(v int) predicate.Agent {
	return predicate.Agent(sql.FieldGT(FieldType, v))
}

// TypeGTE applies the GTE predicate on the "type" field.
func TypeGTE(v int) predicate.Agent {
	return predicate.Agent(sql.FieldGTE(FieldType, v))
}

// TypeLT applies the LT predicate on the "type" field.
func TypeLT(v int) predicate.Agent {
	return predicate.Agent(sql.FieldLT(FieldType, v))
}

// TypeLTE applies the LTE predicate on the "type" field.
func TypeLTE(v int) predicate.Agent {
	return predicate.Agent(sql.FieldLTE(FieldType, v))
}

// VersionEQ applies the EQ predicate on the "version" field.
func VersionEQ(v string) predicate.Agent {
	return predicate.Agent(sql.FieldEQ(FieldVersion, v))
}

// VersionNEQ applies the NEQ predicate on the "version" field.
func VersionNEQ(v string) predicate.Agent {
	return predicate.Agent(sql.FieldNEQ(FieldVersion, v))
}

// VersionIn applies the In predicate on the "version" field.
func VersionIn(vs ...string) predicate.Agent {
	return predicate.Agent(sql.FieldIn(FieldVersion, vs...))
}

// VersionNotIn applies the NotIn predicate on the "version" field.
func VersionNotIn(vs ...string) predicate.Agent {
	return predicate.Agent(sql.FieldNotIn(FieldVersion, vs...))
}

// VersionGT applies the GT predicate on the "version" field.
func VersionGT(v string) predicate.Agent {
	return predicate.Agent(sql.FieldGT(FieldVersion, v))
}

// VersionGTE applies the GTE predicate on the "version" field.
func VersionGTE(v string) predicate.Agent {
	return predicate.Agent(sql.FieldGTE(FieldVersion, v))
}

// VersionLT applies the LT predicate on the "version" field.
func VersionLT(v string) predicate.Agent {
	return predicate.Agent(sql.FieldLT(FieldVersion, v))
}

// VersionLTE applies the LTE predicate on the "version" field.
func VersionLTE(v string) predicate.Agent {
	return predicate.Agent(sql.FieldLTE(FieldVersion, v))
}

// VersionContains applies the Contains predicate on the "version" field.
func VersionContains(v string) predicate.Agent {
	return predicate.Agent(sql.FieldContains(FieldVersion, v))
}

// VersionHasPrefix applies the HasPrefix predicate on the "version" field.
func VersionHasPrefix(v string) predicate.Agent {
	return predicate.Agent(sql.FieldHasPrefix(FieldVersion, v))
}

// VersionHasSuffix applies the HasSuffix predicate on the "version" field.
func VersionHasSuffix(v string) predicate.Agent {
	return predicate.Agent(sql.FieldHasSuffix(FieldVersion, v))
}

// VersionEqualFold applies the EqualFold predicate on the "version" field.
func VersionEqualFold(v string) predicate.Agent {
	return predicate.Agent(sql.FieldEqualFold(FieldVersion, v))
}

// VersionContainsFold applies the ContainsFold predicate on the "version" field.
func VersionContainsFold(v string) predicate.Agent {
	return predicate.Agent(sql.FieldContainsFold(FieldVersion, v))
}

// EngVersionEQ applies the EQ predicate on the "eng_version" field.
func EngVersionEQ(v string) predicate.Agent {
	return predicate.Agent(sql.FieldEQ(FieldEngVersion, v))
}

// EngVersionNEQ applies the NEQ predicate on the "eng_version" field.
func EngVersionNEQ(v string) predicate.Agent {
	return predicate.Agent(sql.FieldNEQ(FieldEngVersion, v))
}

// EngVersionIn applies the In predicate on the "eng_version" field.
func EngVersionIn(vs ...string) predicate.Agent {
	return predicate.Agent(sql.FieldIn(FieldEngVersion, vs...))
}

// EngVersionNotIn applies the NotIn predicate on the "eng_version" field.
func EngVersionNotIn(vs ...string) predicate.Agent {
	return predicate.Agent(sql.FieldNotIn(FieldEngVersion, vs...))
}

// EngVersionGT applies the GT predicate on the "eng_version" field.
func EngVersionGT(v string) predicate.Agent {
	return predicate.Agent(sql.FieldGT(FieldEngVersion, v))
}

// EngVersionGTE applies the GTE predicate on the "eng_version" field.
func EngVersionGTE(v string) predicate.Agent {
	return predicate.Agent(sql.FieldGTE(FieldEngVersion, v))
}

// EngVersionLT applies the LT predicate on the "eng_version" field.
func EngVersionLT(v string) predicate.Agent {
	return predicate.Agent(sql.FieldLT(FieldEngVersion, v))
}

// EngVersionLTE applies the LTE predicate on the "eng_version" field.
func EngVersionLTE(v string) predicate.Agent {
	return predicate.Agent(sql.FieldLTE(FieldEngVersion, v))
}

// EngVersionContains applies the Contains predicate on the "eng_version" field.
func EngVersionContains(v string) predicate.Agent {
	return predicate.Agent(sql.FieldContains(FieldEngVersion, v))
}

// EngVersionHasPrefix applies the HasPrefix predicate on the "eng_version" field.
func EngVersionHasPrefix(v string) predicate.Agent {
	return predicate.Agent(sql.FieldHasPrefix(FieldEngVersion, v))
}

// EngVersionHasSuffix applies the HasSuffix predicate on the "eng_version" field.
func EngVersionHasSuffix(v string) predicate.Agent {
	return predicate.Agent(sql.FieldHasSuffix(FieldEngVersion, v))
}

// EngVersionEqualFold applies the EqualFold predicate on the "eng_version" field.
func EngVersionEqualFold(v string) predicate.Agent {
	return predicate.Agent(sql.FieldEqualFold(FieldEngVersion, v))
}

// EngVersionContainsFold applies the ContainsFold predicate on the "eng_version" field.
func EngVersionContainsFold(v string) predicate.Agent {
	return predicate.Agent(sql.FieldContainsFold(FieldEngVersion, v))
}

// ConfigEQ applies the EQ predicate on the "config" field.
func ConfigEQ(v string) predicate.Agent {
	return predicate.Agent(sql.FieldEQ(FieldConfig, v))
}

// ConfigNEQ applies the NEQ predicate on the "config" field.
func ConfigNEQ(v string) predicate.Agent {
	return predicate.Agent(sql.FieldNEQ(FieldConfig, v))
}

// ConfigIn applies the In predicate on the "config" field.
func ConfigIn(vs ...string) predicate.Agent {
	return predicate.Agent(sql.FieldIn(FieldConfig, vs...))
}

// ConfigNotIn applies the NotIn predicate on the "config" field.
func ConfigNotIn(vs ...string) predicate.Agent {
	return predicate.Agent(sql.FieldNotIn(FieldConfig, vs...))
}

// ConfigGT applies the GT predicate on the "config" field.
func ConfigGT(v string) predicate.Agent {
	return predicate.Agent(sql.FieldGT(FieldConfig, v))
}

// ConfigGTE applies the GTE predicate on the "config" field.
func ConfigGTE(v string) predicate.Agent {
	return predicate.Agent(sql.FieldGTE(FieldConfig, v))
}

// ConfigLT applies the LT predicate on the "config" field.
func ConfigLT(v string) predicate.Agent {
	return predicate.Agent(sql.FieldLT(FieldConfig, v))
}

// ConfigLTE applies the LTE predicate on the "config" field.
func ConfigLTE(v string) predicate.Agent {
	return predicate.Agent(sql.FieldLTE(FieldConfig, v))
}

// ConfigContains applies the Contains predicate on the "config" field.
func ConfigContains(v string) predicate.Agent {
	return predicate.Agent(sql.FieldContains(FieldConfig, v))
}

// ConfigHasPrefix applies the HasPrefix predicate on the "config" field.
func ConfigHasPrefix(v string) predicate.Agent {
	return predicate.Agent(sql.FieldHasPrefix(FieldConfig, v))
}

// ConfigHasSuffix applies the HasSuffix predicate on the "config" field.
func ConfigHasSuffix(v string) predicate.Agent {
	return predicate.Agent(sql.FieldHasSuffix(FieldConfig, v))
}

// ConfigEqualFold applies the EqualFold predicate on the "config" field.
func ConfigEqualFold(v string) predicate.Agent {
	return predicate.Agent(sql.FieldEqualFold(FieldConfig, v))
}

// ConfigContainsFold applies the ContainsFold predicate on the "config" field.
func ConfigContainsFold(v string) predicate.Agent {
	return predicate.Agent(sql.FieldContainsFold(FieldConfig, v))
}

// ExtensionEQ applies the EQ predicate on the "extension" field.
func ExtensionEQ(v string) predicate.Agent {
	return predicate.Agent(sql.FieldEQ(FieldExtension, v))
}

// ExtensionNEQ applies the NEQ predicate on the "extension" field.
func ExtensionNEQ(v string) predicate.Agent {
	return predicate.Agent(sql.FieldNEQ(FieldExtension, v))
}

// ExtensionIn applies the In predicate on the "extension" field.
func ExtensionIn(vs ...string) predicate.Agent {
	return predicate.Agent(sql.FieldIn(FieldExtension, vs...))
}

// ExtensionNotIn applies the NotIn predicate on the "extension" field.
func ExtensionNotIn(vs ...string) predicate.Agent {
	return predicate.Agent(sql.FieldNotIn(FieldExtension, vs...))
}

// ExtensionGT applies the GT predicate on the "extension" field.
func ExtensionGT(v string) predicate.Agent {
	return predicate.Agent(sql.FieldGT(FieldExtension, v))
}

// ExtensionGTE applies the GTE predicate on the "extension" field.
func ExtensionGTE(v string) predicate.Agent {
	return predicate.Agent(sql.FieldGTE(FieldExtension, v))
}

// ExtensionLT applies the LT predicate on the "extension" field.
func ExtensionLT(v string) predicate.Agent {
	return predicate.Agent(sql.FieldLT(FieldExtension, v))
}

// ExtensionLTE applies the LTE predicate on the "extension" field.
func ExtensionLTE(v string) predicate.Agent {
	return predicate.Agent(sql.FieldLTE(FieldExtension, v))
}

// ExtensionContains applies the Contains predicate on the "extension" field.
func ExtensionContains(v string) predicate.Agent {
	return predicate.Agent(sql.FieldContains(FieldExtension, v))
}

// ExtensionHasPrefix applies the HasPrefix predicate on the "extension" field.
func ExtensionHasPrefix(v string) predicate.Agent {
	return predicate.Agent(sql.FieldHasPrefix(FieldExtension, v))
}

// ExtensionHasSuffix applies the HasSuffix predicate on the "extension" field.
func ExtensionHasSuffix(v string) predicate.Agent {
	return predicate.Agent(sql.FieldHasSuffix(FieldExtension, v))
}

// ExtensionIsNil applies the IsNil predicate on the "extension" field.
func ExtensionIsNil() predicate.Agent {
	return predicate.Agent(sql.FieldIsNull(FieldExtension))
}

// ExtensionNotNil applies the NotNil predicate on the "extension" field.
func ExtensionNotNil() predicate.Agent {
	return predicate.Agent(sql.FieldNotNull(FieldExtension))
}

// ExtensionEqualFold applies the EqualFold predicate on the "extension" field.
func ExtensionEqualFold(v string) predicate.Agent {
	return predicate.Agent(sql.FieldEqualFold(FieldExtension, v))
}

// ExtensionContainsFold applies the ContainsFold predicate on the "extension" field.
func ExtensionContainsFold(v string) predicate.Agent {
	return predicate.Agent(sql.FieldContainsFold(FieldExtension, v))
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.Agent) predicate.Agent {
	return predicate.Agent(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.Agent) predicate.Agent {
	return predicate.Agent(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.Agent) predicate.Agent {
	return predicate.Agent(sql.NotPredicates(p))
}
