// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"log"
	"reflect"

	"secwalk/internal/domain/repo/ent/migrate"

	"secwalk/internal/domain/repo/ent/agent"
	"secwalk/internal/domain/repo/ent/mcp"
	"secwalk/internal/domain/repo/ent/session"
	"secwalk/internal/domain/repo/ent/sign"
	"secwalk/internal/domain/repo/ent/term"
	"secwalk/internal/domain/repo/ent/toolkit"
	"secwalk/internal/domain/repo/ent/trait"

	"entgo.io/ent"
	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/sql"
)

// Client is the client that holds all ent builders.
type Client struct {
	config
	// Schema is the client for creating, migrating and dropping schema.
	Schema *migrate.Schema
	// Agent is the client for interacting with the Agent builders.
	Agent *AgentClient
	// Mcp is the client for interacting with the Mcp builders.
	Mcp *McpClient
	// Session is the client for interacting with the Session builders.
	Session *SessionClient
	// Sign is the client for interacting with the Sign builders.
	Sign *SignClient
	// Term is the client for interacting with the Term builders.
	Term *TermClient
	// Toolkit is the client for interacting with the Toolkit builders.
	Toolkit *ToolkitClient
	// Trait is the client for interacting with the Trait builders.
	Trait *TraitClient
}

// NewClient creates a new client configured with the given options.
func NewClient(opts ...Option) *Client {
	client := &Client{config: newConfig(opts...)}
	client.init()
	return client
}

func (c *Client) init() {
	c.Schema = migrate.NewSchema(c.driver)
	c.Agent = NewAgentClient(c.config)
	c.Mcp = NewMcpClient(c.config)
	c.Session = NewSessionClient(c.config)
	c.Sign = NewSignClient(c.config)
	c.Term = NewTermClient(c.config)
	c.Toolkit = NewToolkitClient(c.config)
	c.Trait = NewTraitClient(c.config)
}

type (
	// config is the configuration for the client and its builder.
	config struct {
		// driver used for executing database requests.
		driver dialect.Driver
		// debug enable a debug logging.
		debug bool
		// log used for logging on debug mode.
		log func(...any)
		// hooks to execute on mutations.
		hooks *hooks
		// interceptors to execute on queries.
		inters *inters
	}
	// Option function to configure the client.
	Option func(*config)
)

// newConfig creates a new config for the client.
func newConfig(opts ...Option) config {
	cfg := config{log: log.Println, hooks: &hooks{}, inters: &inters{}}
	cfg.options(opts...)
	return cfg
}

// options applies the options on the config object.
func (c *config) options(opts ...Option) {
	for _, opt := range opts {
		opt(c)
	}
	if c.debug {
		c.driver = dialect.Debug(c.driver, c.log)
	}
}

// Debug enables debug logging on the ent.Driver.
func Debug() Option {
	return func(c *config) {
		c.debug = true
	}
}

// Log sets the logging function for debug mode.
func Log(fn func(...any)) Option {
	return func(c *config) {
		c.log = fn
	}
}

// Driver configures the client driver.
func Driver(driver dialect.Driver) Option {
	return func(c *config) {
		c.driver = driver
	}
}

// Open opens a database/sql.DB specified by the driver name and
// the data source name, and returns a new client attached to it.
// Optional parameters can be added for configuring the client.
func Open(driverName, dataSourceName string, options ...Option) (*Client, error) {
	switch driverName {
	case dialect.MySQL, dialect.Postgres, dialect.SQLite:
		drv, err := sql.Open(driverName, dataSourceName)
		if err != nil {
			return nil, err
		}
		return NewClient(append(options, Driver(drv))...), nil
	default:
		return nil, fmt.Errorf("unsupported driver: %q", driverName)
	}
}

// ErrTxStarted is returned when trying to start a new transaction from a transactional client.
var ErrTxStarted = errors.New("ent: cannot start a transaction within a transaction")

// Tx returns a new transactional client. The provided context
// is used until the transaction is committed or rolled back.
func (c *Client) Tx(ctx context.Context) (*Tx, error) {
	if _, ok := c.driver.(*txDriver); ok {
		return nil, ErrTxStarted
	}
	tx, err := newTx(ctx, c.driver)
	if err != nil {
		return nil, fmt.Errorf("ent: starting a transaction: %w", err)
	}
	cfg := c.config
	cfg.driver = tx
	return &Tx{
		ctx:     ctx,
		config:  cfg,
		Agent:   NewAgentClient(cfg),
		Mcp:     NewMcpClient(cfg),
		Session: NewSessionClient(cfg),
		Sign:    NewSignClient(cfg),
		Term:    NewTermClient(cfg),
		Toolkit: NewToolkitClient(cfg),
		Trait:   NewTraitClient(cfg),
	}, nil
}

// BeginTx returns a transactional client with specified options.
func (c *Client) BeginTx(ctx context.Context, opts *sql.TxOptions) (*Tx, error) {
	if _, ok := c.driver.(*txDriver); ok {
		return nil, errors.New("ent: cannot start a transaction within a transaction")
	}
	tx, err := c.driver.(interface {
		BeginTx(context.Context, *sql.TxOptions) (dialect.Tx, error)
	}).BeginTx(ctx, opts)
	if err != nil {
		return nil, fmt.Errorf("ent: starting a transaction: %w", err)
	}
	cfg := c.config
	cfg.driver = &txDriver{tx: tx, drv: c.driver}
	return &Tx{
		ctx:     ctx,
		config:  cfg,
		Agent:   NewAgentClient(cfg),
		Mcp:     NewMcpClient(cfg),
		Session: NewSessionClient(cfg),
		Sign:    NewSignClient(cfg),
		Term:    NewTermClient(cfg),
		Toolkit: NewToolkitClient(cfg),
		Trait:   NewTraitClient(cfg),
	}, nil
}

// Debug returns a new debug-client. It's used to get verbose logging on specific operations.
//
//	client.Debug().
//		Agent.
//		Query().
//		Count(ctx)
func (c *Client) Debug() *Client {
	if c.debug {
		return c
	}
	cfg := c.config
	cfg.driver = dialect.Debug(c.driver, c.log)
	client := &Client{config: cfg}
	client.init()
	return client
}

// Close closes the database connection and prevents new queries from starting.
func (c *Client) Close() error {
	return c.driver.Close()
}

// Use adds the mutation hooks to all the entity clients.
// In order to add hooks to a specific client, call: `client.Node.Use(...)`.
func (c *Client) Use(hooks ...Hook) {
	for _, n := range []interface{ Use(...Hook) }{
		c.Agent, c.Mcp, c.Session, c.Sign, c.Term, c.Toolkit, c.Trait,
	} {
		n.Use(hooks...)
	}
}

// Intercept adds the query interceptors to all the entity clients.
// In order to add interceptors to a specific client, call: `client.Node.Intercept(...)`.
func (c *Client) Intercept(interceptors ...Interceptor) {
	for _, n := range []interface{ Intercept(...Interceptor) }{
		c.Agent, c.Mcp, c.Session, c.Sign, c.Term, c.Toolkit, c.Trait,
	} {
		n.Intercept(interceptors...)
	}
}

// Mutate implements the ent.Mutator interface.
func (c *Client) Mutate(ctx context.Context, m Mutation) (Value, error) {
	switch m := m.(type) {
	case *AgentMutation:
		return c.Agent.mutate(ctx, m)
	case *McpMutation:
		return c.Mcp.mutate(ctx, m)
	case *SessionMutation:
		return c.Session.mutate(ctx, m)
	case *SignMutation:
		return c.Sign.mutate(ctx, m)
	case *TermMutation:
		return c.Term.mutate(ctx, m)
	case *ToolkitMutation:
		return c.Toolkit.mutate(ctx, m)
	case *TraitMutation:
		return c.Trait.mutate(ctx, m)
	default:
		return nil, fmt.Errorf("ent: unknown mutation type %T", m)
	}
}

// AgentClient is a client for the Agent schema.
type AgentClient struct {
	config
}

// NewAgentClient returns a client for the Agent from the given config.
func NewAgentClient(c config) *AgentClient {
	return &AgentClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `agent.Hooks(f(g(h())))`.
func (c *AgentClient) Use(hooks ...Hook) {
	c.hooks.Agent = append(c.hooks.Agent, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `agent.Intercept(f(g(h())))`.
func (c *AgentClient) Intercept(interceptors ...Interceptor) {
	c.inters.Agent = append(c.inters.Agent, interceptors...)
}

// Create returns a builder for creating a Agent entity.
func (c *AgentClient) Create() *AgentCreate {
	mutation := newAgentMutation(c.config, OpCreate)
	return &AgentCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Agent entities.
func (c *AgentClient) CreateBulk(builders ...*AgentCreate) *AgentCreateBulk {
	return &AgentCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *AgentClient) MapCreateBulk(slice any, setFunc func(*AgentCreate, int)) *AgentCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &AgentCreateBulk{err: fmt.Errorf("calling to AgentClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*AgentCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &AgentCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Agent.
func (c *AgentClient) Update() *AgentUpdate {
	mutation := newAgentMutation(c.config, OpUpdate)
	return &AgentUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *AgentClient) UpdateOne(a *Agent) *AgentUpdateOne {
	mutation := newAgentMutation(c.config, OpUpdateOne, withAgent(a))
	return &AgentUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *AgentClient) UpdateOneID(id string) *AgentUpdateOne {
	mutation := newAgentMutation(c.config, OpUpdateOne, withAgentID(id))
	return &AgentUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Agent.
func (c *AgentClient) Delete() *AgentDelete {
	mutation := newAgentMutation(c.config, OpDelete)
	return &AgentDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *AgentClient) DeleteOne(a *Agent) *AgentDeleteOne {
	return c.DeleteOneID(a.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *AgentClient) DeleteOneID(id string) *AgentDeleteOne {
	builder := c.Delete().Where(agent.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &AgentDeleteOne{builder}
}

// Query returns a query builder for Agent.
func (c *AgentClient) Query() *AgentQuery {
	return &AgentQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeAgent},
		inters: c.Interceptors(),
	}
}

// Get returns a Agent entity by its id.
func (c *AgentClient) Get(ctx context.Context, id string) (*Agent, error) {
	return c.Query().Where(agent.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *AgentClient) GetX(ctx context.Context, id string) *Agent {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *AgentClient) Hooks() []Hook {
	return c.hooks.Agent
}

// Interceptors returns the client interceptors.
func (c *AgentClient) Interceptors() []Interceptor {
	return c.inters.Agent
}

func (c *AgentClient) mutate(ctx context.Context, m *AgentMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&AgentCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&AgentUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&AgentUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&AgentDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Agent mutation op: %q", m.Op())
	}
}

// McpClient is a client for the Mcp schema.
type McpClient struct {
	config
}

// NewMcpClient returns a client for the Mcp from the given config.
func NewMcpClient(c config) *McpClient {
	return &McpClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `mcp.Hooks(f(g(h())))`.
func (c *McpClient) Use(hooks ...Hook) {
	c.hooks.Mcp = append(c.hooks.Mcp, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `mcp.Intercept(f(g(h())))`.
func (c *McpClient) Intercept(interceptors ...Interceptor) {
	c.inters.Mcp = append(c.inters.Mcp, interceptors...)
}

// Create returns a builder for creating a Mcp entity.
func (c *McpClient) Create() *McpCreate {
	mutation := newMcpMutation(c.config, OpCreate)
	return &McpCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Mcp entities.
func (c *McpClient) CreateBulk(builders ...*McpCreate) *McpCreateBulk {
	return &McpCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *McpClient) MapCreateBulk(slice any, setFunc func(*McpCreate, int)) *McpCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &McpCreateBulk{err: fmt.Errorf("calling to McpClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*McpCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &McpCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Mcp.
func (c *McpClient) Update() *McpUpdate {
	mutation := newMcpMutation(c.config, OpUpdate)
	return &McpUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *McpClient) UpdateOne(m *Mcp) *McpUpdateOne {
	mutation := newMcpMutation(c.config, OpUpdateOne, withMcp(m))
	return &McpUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *McpClient) UpdateOneID(id string) *McpUpdateOne {
	mutation := newMcpMutation(c.config, OpUpdateOne, withMcpID(id))
	return &McpUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Mcp.
func (c *McpClient) Delete() *McpDelete {
	mutation := newMcpMutation(c.config, OpDelete)
	return &McpDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *McpClient) DeleteOne(m *Mcp) *McpDeleteOne {
	return c.DeleteOneID(m.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *McpClient) DeleteOneID(id string) *McpDeleteOne {
	builder := c.Delete().Where(mcp.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &McpDeleteOne{builder}
}

// Query returns a query builder for Mcp.
func (c *McpClient) Query() *McpQuery {
	return &McpQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeMcp},
		inters: c.Interceptors(),
	}
}

// Get returns a Mcp entity by its id.
func (c *McpClient) Get(ctx context.Context, id string) (*Mcp, error) {
	return c.Query().Where(mcp.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *McpClient) GetX(ctx context.Context, id string) *Mcp {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *McpClient) Hooks() []Hook {
	return c.hooks.Mcp
}

// Interceptors returns the client interceptors.
func (c *McpClient) Interceptors() []Interceptor {
	return c.inters.Mcp
}

func (c *McpClient) mutate(ctx context.Context, m *McpMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&McpCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&McpUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&McpUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&McpDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Mcp mutation op: %q", m.Op())
	}
}

// SessionClient is a client for the Session schema.
type SessionClient struct {
	config
}

// NewSessionClient returns a client for the Session from the given config.
func NewSessionClient(c config) *SessionClient {
	return &SessionClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `session.Hooks(f(g(h())))`.
func (c *SessionClient) Use(hooks ...Hook) {
	c.hooks.Session = append(c.hooks.Session, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `session.Intercept(f(g(h())))`.
func (c *SessionClient) Intercept(interceptors ...Interceptor) {
	c.inters.Session = append(c.inters.Session, interceptors...)
}

// Create returns a builder for creating a Session entity.
func (c *SessionClient) Create() *SessionCreate {
	mutation := newSessionMutation(c.config, OpCreate)
	return &SessionCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Session entities.
func (c *SessionClient) CreateBulk(builders ...*SessionCreate) *SessionCreateBulk {
	return &SessionCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *SessionClient) MapCreateBulk(slice any, setFunc func(*SessionCreate, int)) *SessionCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &SessionCreateBulk{err: fmt.Errorf("calling to SessionClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*SessionCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &SessionCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Session.
func (c *SessionClient) Update() *SessionUpdate {
	mutation := newSessionMutation(c.config, OpUpdate)
	return &SessionUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *SessionClient) UpdateOne(s *Session) *SessionUpdateOne {
	mutation := newSessionMutation(c.config, OpUpdateOne, withSession(s))
	return &SessionUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *SessionClient) UpdateOneID(id string) *SessionUpdateOne {
	mutation := newSessionMutation(c.config, OpUpdateOne, withSessionID(id))
	return &SessionUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Session.
func (c *SessionClient) Delete() *SessionDelete {
	mutation := newSessionMutation(c.config, OpDelete)
	return &SessionDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *SessionClient) DeleteOne(s *Session) *SessionDeleteOne {
	return c.DeleteOneID(s.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *SessionClient) DeleteOneID(id string) *SessionDeleteOne {
	builder := c.Delete().Where(session.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &SessionDeleteOne{builder}
}

// Query returns a query builder for Session.
func (c *SessionClient) Query() *SessionQuery {
	return &SessionQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeSession},
		inters: c.Interceptors(),
	}
}

// Get returns a Session entity by its id.
func (c *SessionClient) Get(ctx context.Context, id string) (*Session, error) {
	return c.Query().Where(session.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *SessionClient) GetX(ctx context.Context, id string) *Session {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *SessionClient) Hooks() []Hook {
	return c.hooks.Session
}

// Interceptors returns the client interceptors.
func (c *SessionClient) Interceptors() []Interceptor {
	return c.inters.Session
}

func (c *SessionClient) mutate(ctx context.Context, m *SessionMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&SessionCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&SessionUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&SessionUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&SessionDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Session mutation op: %q", m.Op())
	}
}

// SignClient is a client for the Sign schema.
type SignClient struct {
	config
}

// NewSignClient returns a client for the Sign from the given config.
func NewSignClient(c config) *SignClient {
	return &SignClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `sign.Hooks(f(g(h())))`.
func (c *SignClient) Use(hooks ...Hook) {
	c.hooks.Sign = append(c.hooks.Sign, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `sign.Intercept(f(g(h())))`.
func (c *SignClient) Intercept(interceptors ...Interceptor) {
	c.inters.Sign = append(c.inters.Sign, interceptors...)
}

// Create returns a builder for creating a Sign entity.
func (c *SignClient) Create() *SignCreate {
	mutation := newSignMutation(c.config, OpCreate)
	return &SignCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Sign entities.
func (c *SignClient) CreateBulk(builders ...*SignCreate) *SignCreateBulk {
	return &SignCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *SignClient) MapCreateBulk(slice any, setFunc func(*SignCreate, int)) *SignCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &SignCreateBulk{err: fmt.Errorf("calling to SignClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*SignCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &SignCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Sign.
func (c *SignClient) Update() *SignUpdate {
	mutation := newSignMutation(c.config, OpUpdate)
	return &SignUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *SignClient) UpdateOne(s *Sign) *SignUpdateOne {
	mutation := newSignMutation(c.config, OpUpdateOne, withSign(s))
	return &SignUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *SignClient) UpdateOneID(id int) *SignUpdateOne {
	mutation := newSignMutation(c.config, OpUpdateOne, withSignID(id))
	return &SignUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Sign.
func (c *SignClient) Delete() *SignDelete {
	mutation := newSignMutation(c.config, OpDelete)
	return &SignDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *SignClient) DeleteOne(s *Sign) *SignDeleteOne {
	return c.DeleteOneID(s.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *SignClient) DeleteOneID(id int) *SignDeleteOne {
	builder := c.Delete().Where(sign.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &SignDeleteOne{builder}
}

// Query returns a query builder for Sign.
func (c *SignClient) Query() *SignQuery {
	return &SignQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeSign},
		inters: c.Interceptors(),
	}
}

// Get returns a Sign entity by its id.
func (c *SignClient) Get(ctx context.Context, id int) (*Sign, error) {
	return c.Query().Where(sign.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *SignClient) GetX(ctx context.Context, id int) *Sign {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *SignClient) Hooks() []Hook {
	return c.hooks.Sign
}

// Interceptors returns the client interceptors.
func (c *SignClient) Interceptors() []Interceptor {
	return c.inters.Sign
}

func (c *SignClient) mutate(ctx context.Context, m *SignMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&SignCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&SignUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&SignUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&SignDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Sign mutation op: %q", m.Op())
	}
}

// TermClient is a client for the Term schema.
type TermClient struct {
	config
}

// NewTermClient returns a client for the Term from the given config.
func NewTermClient(c config) *TermClient {
	return &TermClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `term.Hooks(f(g(h())))`.
func (c *TermClient) Use(hooks ...Hook) {
	c.hooks.Term = append(c.hooks.Term, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `term.Intercept(f(g(h())))`.
func (c *TermClient) Intercept(interceptors ...Interceptor) {
	c.inters.Term = append(c.inters.Term, interceptors...)
}

// Create returns a builder for creating a Term entity.
func (c *TermClient) Create() *TermCreate {
	mutation := newTermMutation(c.config, OpCreate)
	return &TermCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Term entities.
func (c *TermClient) CreateBulk(builders ...*TermCreate) *TermCreateBulk {
	return &TermCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *TermClient) MapCreateBulk(slice any, setFunc func(*TermCreate, int)) *TermCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &TermCreateBulk{err: fmt.Errorf("calling to TermClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*TermCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &TermCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Term.
func (c *TermClient) Update() *TermUpdate {
	mutation := newTermMutation(c.config, OpUpdate)
	return &TermUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *TermClient) UpdateOne(t *Term) *TermUpdateOne {
	mutation := newTermMutation(c.config, OpUpdateOne, withTerm(t))
	return &TermUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *TermClient) UpdateOneID(id int) *TermUpdateOne {
	mutation := newTermMutation(c.config, OpUpdateOne, withTermID(id))
	return &TermUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Term.
func (c *TermClient) Delete() *TermDelete {
	mutation := newTermMutation(c.config, OpDelete)
	return &TermDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *TermClient) DeleteOne(t *Term) *TermDeleteOne {
	return c.DeleteOneID(t.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *TermClient) DeleteOneID(id int) *TermDeleteOne {
	builder := c.Delete().Where(term.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &TermDeleteOne{builder}
}

// Query returns a query builder for Term.
func (c *TermClient) Query() *TermQuery {
	return &TermQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeTerm},
		inters: c.Interceptors(),
	}
}

// Get returns a Term entity by its id.
func (c *TermClient) Get(ctx context.Context, id int) (*Term, error) {
	return c.Query().Where(term.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *TermClient) GetX(ctx context.Context, id int) *Term {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *TermClient) Hooks() []Hook {
	return c.hooks.Term
}

// Interceptors returns the client interceptors.
func (c *TermClient) Interceptors() []Interceptor {
	return c.inters.Term
}

func (c *TermClient) mutate(ctx context.Context, m *TermMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&TermCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&TermUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&TermUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&TermDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Term mutation op: %q", m.Op())
	}
}

// ToolkitClient is a client for the Toolkit schema.
type ToolkitClient struct {
	config
}

// NewToolkitClient returns a client for the Toolkit from the given config.
func NewToolkitClient(c config) *ToolkitClient {
	return &ToolkitClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `toolkit.Hooks(f(g(h())))`.
func (c *ToolkitClient) Use(hooks ...Hook) {
	c.hooks.Toolkit = append(c.hooks.Toolkit, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `toolkit.Intercept(f(g(h())))`.
func (c *ToolkitClient) Intercept(interceptors ...Interceptor) {
	c.inters.Toolkit = append(c.inters.Toolkit, interceptors...)
}

// Create returns a builder for creating a Toolkit entity.
func (c *ToolkitClient) Create() *ToolkitCreate {
	mutation := newToolkitMutation(c.config, OpCreate)
	return &ToolkitCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Toolkit entities.
func (c *ToolkitClient) CreateBulk(builders ...*ToolkitCreate) *ToolkitCreateBulk {
	return &ToolkitCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *ToolkitClient) MapCreateBulk(slice any, setFunc func(*ToolkitCreate, int)) *ToolkitCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &ToolkitCreateBulk{err: fmt.Errorf("calling to ToolkitClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*ToolkitCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &ToolkitCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Toolkit.
func (c *ToolkitClient) Update() *ToolkitUpdate {
	mutation := newToolkitMutation(c.config, OpUpdate)
	return &ToolkitUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *ToolkitClient) UpdateOne(t *Toolkit) *ToolkitUpdateOne {
	mutation := newToolkitMutation(c.config, OpUpdateOne, withToolkit(t))
	return &ToolkitUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *ToolkitClient) UpdateOneID(id string) *ToolkitUpdateOne {
	mutation := newToolkitMutation(c.config, OpUpdateOne, withToolkitID(id))
	return &ToolkitUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Toolkit.
func (c *ToolkitClient) Delete() *ToolkitDelete {
	mutation := newToolkitMutation(c.config, OpDelete)
	return &ToolkitDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *ToolkitClient) DeleteOne(t *Toolkit) *ToolkitDeleteOne {
	return c.DeleteOneID(t.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *ToolkitClient) DeleteOneID(id string) *ToolkitDeleteOne {
	builder := c.Delete().Where(toolkit.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &ToolkitDeleteOne{builder}
}

// Query returns a query builder for Toolkit.
func (c *ToolkitClient) Query() *ToolkitQuery {
	return &ToolkitQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeToolkit},
		inters: c.Interceptors(),
	}
}

// Get returns a Toolkit entity by its id.
func (c *ToolkitClient) Get(ctx context.Context, id string) (*Toolkit, error) {
	return c.Query().Where(toolkit.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *ToolkitClient) GetX(ctx context.Context, id string) *Toolkit {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *ToolkitClient) Hooks() []Hook {
	return c.hooks.Toolkit
}

// Interceptors returns the client interceptors.
func (c *ToolkitClient) Interceptors() []Interceptor {
	return c.inters.Toolkit
}

func (c *ToolkitClient) mutate(ctx context.Context, m *ToolkitMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&ToolkitCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&ToolkitUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&ToolkitUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&ToolkitDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Toolkit mutation op: %q", m.Op())
	}
}

// TraitClient is a client for the Trait schema.
type TraitClient struct {
	config
}

// NewTraitClient returns a client for the Trait from the given config.
func NewTraitClient(c config) *TraitClient {
	return &TraitClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `trait.Hooks(f(g(h())))`.
func (c *TraitClient) Use(hooks ...Hook) {
	c.hooks.Trait = append(c.hooks.Trait, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `trait.Intercept(f(g(h())))`.
func (c *TraitClient) Intercept(interceptors ...Interceptor) {
	c.inters.Trait = append(c.inters.Trait, interceptors...)
}

// Create returns a builder for creating a Trait entity.
func (c *TraitClient) Create() *TraitCreate {
	mutation := newTraitMutation(c.config, OpCreate)
	return &TraitCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Trait entities.
func (c *TraitClient) CreateBulk(builders ...*TraitCreate) *TraitCreateBulk {
	return &TraitCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *TraitClient) MapCreateBulk(slice any, setFunc func(*TraitCreate, int)) *TraitCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &TraitCreateBulk{err: fmt.Errorf("calling to TraitClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*TraitCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &TraitCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Trait.
func (c *TraitClient) Update() *TraitUpdate {
	mutation := newTraitMutation(c.config, OpUpdate)
	return &TraitUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *TraitClient) UpdateOne(t *Trait) *TraitUpdateOne {
	mutation := newTraitMutation(c.config, OpUpdateOne, withTrait(t))
	return &TraitUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *TraitClient) UpdateOneID(id int) *TraitUpdateOne {
	mutation := newTraitMutation(c.config, OpUpdateOne, withTraitID(id))
	return &TraitUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Trait.
func (c *TraitClient) Delete() *TraitDelete {
	mutation := newTraitMutation(c.config, OpDelete)
	return &TraitDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *TraitClient) DeleteOne(t *Trait) *TraitDeleteOne {
	return c.DeleteOneID(t.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *TraitClient) DeleteOneID(id int) *TraitDeleteOne {
	builder := c.Delete().Where(trait.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &TraitDeleteOne{builder}
}

// Query returns a query builder for Trait.
func (c *TraitClient) Query() *TraitQuery {
	return &TraitQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeTrait},
		inters: c.Interceptors(),
	}
}

// Get returns a Trait entity by its id.
func (c *TraitClient) Get(ctx context.Context, id int) (*Trait, error) {
	return c.Query().Where(trait.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *TraitClient) GetX(ctx context.Context, id int) *Trait {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *TraitClient) Hooks() []Hook {
	return c.hooks.Trait
}

// Interceptors returns the client interceptors.
func (c *TraitClient) Interceptors() []Interceptor {
	return c.inters.Trait
}

func (c *TraitClient) mutate(ctx context.Context, m *TraitMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&TraitCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&TraitUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&TraitUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&TraitDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Trait mutation op: %q", m.Op())
	}
}

// hooks and interceptors per client, for fast access.
type (
	hooks struct {
		Agent, Mcp, Session, Sign, Term, Toolkit, Trait []ent.Hook
	}
	inters struct {
		Agent, Mcp, Session, Sign, Term, Toolkit, Trait []ent.Interceptor
	}
)
