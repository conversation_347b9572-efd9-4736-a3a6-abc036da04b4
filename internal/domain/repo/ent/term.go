// Code generated by ent, DO NOT EDIT.

package ent

import (
	"encoding/json"
	"fmt"
	"secwalk/internal/domain/repo/ent/term"
	"strings"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

// Term is the model entity for the Term schema.
type Term struct {
	config `json:"-"`
	// ID of the ent.
	ID int `json:"id,omitempty"`
	// CreateAt holds the value of the "create_at" field.
	CreateAt string `json:"create_at,omitempty"`
	// UpdateAt holds the value of the "update_at" field.
	UpdateAt string `json:"update_at,omitempty"`
	// Aid holds the value of the "aid" field.
	Aid string `json:"aid,omitempty"`
	// Org holds the value of the "org" field.
	Org string `json:"org,omitempty"`
	// Sid holds the value of the "sid" field.
	Sid string `json:"sid,omitempty"`
	// Content holds the value of the "content" field.
	Content      map[string]interface{} `json:"content,omitempty"`
	selectValues sql.SelectValues
}

// scanValues returns the types for scanning values from sql.Rows.
func (*Term) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case term.FieldContent:
			values[i] = new([]byte)
		case term.FieldID:
			values[i] = new(sql.NullInt64)
		case term.FieldCreateAt, term.FieldUpdateAt, term.FieldAid, term.FieldOrg, term.FieldSid:
			values[i] = new(sql.NullString)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the Term fields.
func (t *Term) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case term.FieldID:
			value, ok := values[i].(*sql.NullInt64)
			if !ok {
				return fmt.Errorf("unexpected type %T for field id", value)
			}
			t.ID = int(value.Int64)
		case term.FieldCreateAt:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field create_at", values[i])
			} else if value.Valid {
				t.CreateAt = value.String
			}
		case term.FieldUpdateAt:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field update_at", values[i])
			} else if value.Valid {
				t.UpdateAt = value.String
			}
		case term.FieldAid:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field aid", values[i])
			} else if value.Valid {
				t.Aid = value.String
			}
		case term.FieldOrg:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field org", values[i])
			} else if value.Valid {
				t.Org = value.String
			}
		case term.FieldSid:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field sid", values[i])
			} else if value.Valid {
				t.Sid = value.String
			}
		case term.FieldContent:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field content", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &t.Content); err != nil {
					return fmt.Errorf("unmarshal field content: %w", err)
				}
			}
		default:
			t.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the Term.
// This includes values selected through modifiers, order, etc.
func (t *Term) Value(name string) (ent.Value, error) {
	return t.selectValues.Get(name)
}

// Update returns a builder for updating this Term.
// Note that you need to call Term.Unwrap() before calling this method if this Term
// was returned from a transaction, and the transaction was committed or rolled back.
func (t *Term) Update() *TermUpdateOne {
	return NewTermClient(t.config).UpdateOne(t)
}

// Unwrap unwraps the Term entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (t *Term) Unwrap() *Term {
	_tx, ok := t.config.driver.(*txDriver)
	if !ok {
		panic("ent: Term is not a transactional entity")
	}
	t.config.driver = _tx.drv
	return t
}

// String implements the fmt.Stringer.
func (t *Term) String() string {
	var builder strings.Builder
	builder.WriteString("Term(")
	builder.WriteString(fmt.Sprintf("id=%v, ", t.ID))
	builder.WriteString("create_at=")
	builder.WriteString(t.CreateAt)
	builder.WriteString(", ")
	builder.WriteString("update_at=")
	builder.WriteString(t.UpdateAt)
	builder.WriteString(", ")
	builder.WriteString("aid=")
	builder.WriteString(t.Aid)
	builder.WriteString(", ")
	builder.WriteString("org=")
	builder.WriteString(t.Org)
	builder.WriteString(", ")
	builder.WriteString("sid=")
	builder.WriteString(t.Sid)
	builder.WriteString(", ")
	builder.WriteString("content=")
	builder.WriteString(fmt.Sprintf("%v", t.Content))
	builder.WriteByte(')')
	return builder.String()
}

// Terms is a parsable slice of Term.
type Terms []*Term
