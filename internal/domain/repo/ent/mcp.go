// Code generated by ent, DO NOT EDIT.

package ent

import (
	"encoding/json"
	"fmt"
	"secwalk/internal/domain/repo/ent/mcp"
	"strings"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

// Mcp is the model entity for the Mcp schema.
type Mcp struct {
	config `json:"-"`
	// ID of the ent.
	ID string `json:"id,omitempty"`
	// Type holds the value of the "type" field.
	Type string `json:"type,omitempty"`
	// Name holds the value of the "name" field.
	Name string `json:"name,omitempty"`
	// Command holds the value of the "command" field.
	Command string `json:"command,omitempty"`
	// Args holds the value of the "args" field.
	Args []string `json:"args,omitempty"`
	// Env holds the value of the "env" field.
	Env map[string]string `json:"env,omitempty"`
	// URL holds the value of the "url" field.
	URL string `json:"url,omitempty"`
	// Headers holds the value of the "headers" field.
	Headers map[string]string `json:"headers,omitempty"`
	// Timeout holds the value of the "timeout" field.
	Timeout      int64 `json:"timeout,omitempty"`
	selectValues sql.SelectValues
}

// scanValues returns the types for scanning values from sql.Rows.
func (*Mcp) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case mcp.FieldArgs, mcp.FieldEnv, mcp.FieldHeaders:
			values[i] = new([]byte)
		case mcp.FieldTimeout:
			values[i] = new(sql.NullInt64)
		case mcp.FieldID, mcp.FieldType, mcp.FieldName, mcp.FieldCommand, mcp.FieldURL:
			values[i] = new(sql.NullString)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the Mcp fields.
func (m *Mcp) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case mcp.FieldID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field id", values[i])
			} else if value.Valid {
				m.ID = value.String
			}
		case mcp.FieldType:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field type", values[i])
			} else if value.Valid {
				m.Type = value.String
			}
		case mcp.FieldName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field name", values[i])
			} else if value.Valid {
				m.Name = value.String
			}
		case mcp.FieldCommand:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field command", values[i])
			} else if value.Valid {
				m.Command = value.String
			}
		case mcp.FieldArgs:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field args", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &m.Args); err != nil {
					return fmt.Errorf("unmarshal field args: %w", err)
				}
			}
		case mcp.FieldEnv:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field env", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &m.Env); err != nil {
					return fmt.Errorf("unmarshal field env: %w", err)
				}
			}
		case mcp.FieldURL:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field url", values[i])
			} else if value.Valid {
				m.URL = value.String
			}
		case mcp.FieldHeaders:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field headers", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &m.Headers); err != nil {
					return fmt.Errorf("unmarshal field headers: %w", err)
				}
			}
		case mcp.FieldTimeout:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field timeout", values[i])
			} else if value.Valid {
				m.Timeout = value.Int64
			}
		default:
			m.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the Mcp.
// This includes values selected through modifiers, order, etc.
func (m *Mcp) Value(name string) (ent.Value, error) {
	return m.selectValues.Get(name)
}

// Update returns a builder for updating this Mcp.
// Note that you need to call Mcp.Unwrap() before calling this method if this Mcp
// was returned from a transaction, and the transaction was committed or rolled back.
func (m *Mcp) Update() *McpUpdateOne {
	return NewMcpClient(m.config).UpdateOne(m)
}

// Unwrap unwraps the Mcp entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (m *Mcp) Unwrap() *Mcp {
	_tx, ok := m.config.driver.(*txDriver)
	if !ok {
		panic("ent: Mcp is not a transactional entity")
	}
	m.config.driver = _tx.drv
	return m
}

// String implements the fmt.Stringer.
func (m *Mcp) String() string {
	var builder strings.Builder
	builder.WriteString("Mcp(")
	builder.WriteString(fmt.Sprintf("id=%v, ", m.ID))
	builder.WriteString("type=")
	builder.WriteString(m.Type)
	builder.WriteString(", ")
	builder.WriteString("name=")
	builder.WriteString(m.Name)
	builder.WriteString(", ")
	builder.WriteString("command=")
	builder.WriteString(m.Command)
	builder.WriteString(", ")
	builder.WriteString("args=")
	builder.WriteString(fmt.Sprintf("%v", m.Args))
	builder.WriteString(", ")
	builder.WriteString("env=")
	builder.WriteString(fmt.Sprintf("%v", m.Env))
	builder.WriteString(", ")
	builder.WriteString("url=")
	builder.WriteString(m.URL)
	builder.WriteString(", ")
	builder.WriteString("headers=")
	builder.WriteString(fmt.Sprintf("%v", m.Headers))
	builder.WriteString(", ")
	builder.WriteString("timeout=")
	builder.WriteString(fmt.Sprintf("%v", m.Timeout))
	builder.WriteByte(')')
	return builder.String()
}

// Mcps is a parsable slice of Mcp.
type Mcps []*Mcp
