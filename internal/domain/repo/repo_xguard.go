package repo

import (
	"context"
	"secwalk/internal/domain/repo/ent"
	"secwalk/internal/domain/repo/ent/trait"
	"sync"
)

type XGuardRepo struct {
	mux sync.Mutex
	cli *ent.Client
}

func NewXGuardRepo(cli *ent.Client) *XGuardRepo {
	return &XGuardRepo{
		mux: sync.Mutex{},
		cli: cli,
	}
}

func (r *XGuardRepo) QueryTrait(ctx context.Context) ([]*ent.Trait, error) {
	r.mux.Lock()
	defer r.mux.Unlock()

	return r.cli.Trait.Query().All(ctx)
}

func (r *XGuardRepo) UpsertTrait(ctx context.Context, traits []*ent.Trait) error {
	r.mux.Lock()
	defer r.mux.Unlock()

	for _, v := range traits {
		res, err := r.cli.Trait.Query().Where(trait.Text(v.Text)).First(ctx)
		if err == nil {
			res.Type = v.Type
			res.Level = v.Level
			_, err := res.Update().Save(ctx)
			if err != nil {
				return err
			}
		} else {
			_, err := r.cli.Trait.Create().
				SetText(v.Text).
				SetType(v.Type).
				SetLevel(v.Level).
				Save(ctx)
			if err != nil {
				return err
			}
		}
	}

	return nil
}

func (r *XGuardRepo) DeleteTrait(ctx context.Context, traits []string) error {
	r.mux.Lock()
	defer r.mux.Unlock()

	for _, v := range traits {
		_, err := r.cli.Trait.Delete().Where(trait.Text(v)).Exec(ctx)
		if err != nil {
			return err
		}
	}
	return nil
}
