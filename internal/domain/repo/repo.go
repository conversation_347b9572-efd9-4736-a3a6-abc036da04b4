package repo

import (
	"context"
	"fmt"
	"secwalk/internal/domain/repo/ent"

	_ "github.com/go-sql-driver/mysql"
)

type DB struct {
	Address  string `yaml:"address" json:"address,omitempty"`
	Username string `yaml:"username" json:"username,omitempty"`
	Password string `yaml:"password" json:"password,omitempty"`
}

func NewDBClient(cfg DB) (*ent.Client, error) {
	client, err := ent.Open(
		"mysql",
		fmt.Sprintf(
			"%s:%s@tcp(%s)/secwalk?charset=utf8mb4&parseTime=True&loc=Local",
			cfg.Username, cfg.Password, cfg.Address),
	)
	if err != nil {
		return nil, err
	}

	if err := client.Schema.Create(context.Background()); err != nil {
		return nil, err
	}

	return client, nil
}
