package repo

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"secwalk/internal/domain/core/schema"
	"secwalk/internal/domain/core/toolkit/python"
	"secwalk/internal/domain/core/toolkit/restful"
	"secwalk/internal/domain/core/toolkit/secdocx"
	"secwalk/internal/domain/repo/ent"
	"secwalk/internal/domain/repo/ent/toolkit"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqljson"
	"github.com/hashicorp/golang-lru/v2/expirable"
)

var (
	ErrToolkitNotFound        = errors.New("toolkit not found")
	ErrToolkitSystemModify    = errors.New("system plugin can not be modify")
	ErrToolkitAbilityNotFound = errors.New("toolkit ability not found")
	ErrToolkitDuplicate       = errors.New("toolkit duplicate")
)

type ToolkitRepo struct {
	toolkitCache *expirable.LRU[string, schema.Toolkit]
	toolCache    *expirable.LRU[string, string]
	dtools       []schema.Toolkit
	client       *ent.Client
}

func NewToolkitRepo(client *ent.Client) *ToolkitRepo {
	return &ToolkitRepo{
		toolkitCache: expirable.NewLRU[string, schema.Toolkit](100, nil, time.Hour),
		toolCache:    expirable.NewLRU[string, string](10000, nil, 0),
		dtools: []schema.Toolkit{
			python.NewToolkit(),
			secdocx.NewToolkit(),
			// secdb.NewToolkit(),
		},
		client: client,
	}
}

func (r *ToolkitRepo) FindAllDefaultToolkit(ctx context.Context) ([]schema.Toolkit, error) {
	rds, err := r.client.Toolkit.
		Query().
		Where(
			toolkit.TypeEQ(schema.ToolkitTypeSystem),
		).All(ctx)
	if err != nil {
		return nil, err
	}

	res := make([]schema.Toolkit, 0)
	for _, rd := range rds {
		rs := schema.Toolkit{}
		if err := json.Unmarshal([]byte(rd.Config), &rs); err != nil {
			return nil, err
		}
		res = append(res, rs)
	}

	return append(r.dtools, res...), nil
}

func (r *ToolkitRepo) FindDefaultToolkit(ctx context.Context, toolkitID string) (*schema.Toolkit, error) {
	tks, err := r.FindAllDefaultToolkit(ctx)
	if err != nil {
		return nil, err
	}

	for _, tk := range tks {
		if tk.ID != toolkitID {
			continue
		}
		return &tk, nil
	}
	return nil, ErrToolkitNotFound
}

func (r *ToolkitRepo) QueryAllToolkit(ctx context.Context) ([]schema.Toolkit, error) {
	rds, err := r.client.Toolkit.Query().All(ctx)
	if err != nil {
		return nil, err
	}

	res := make([]schema.Toolkit, 0)
	for _, rd := range rds {
		rs := schema.Toolkit{}
		if err := json.Unmarshal([]byte(rd.Config), &rs); err != nil {
			return nil, err
		}
		res = append(res, rs)
	}

	tks := append(r.dtools, res...)
	for _, tk := range tks {
		for _, t := range tk.Tools {
			r.toolCache.Add(t.ID, tk.ID)
		}
	}

	return tks, err
}

func (r *ToolkitRepo) QueryToolkit(ctx context.Context, toolkitID string) (*schema.Toolkit, error) {
	// 缓存查找
	value, ok := r.toolkitCache.Get(toolkitID)
	if ok {
		return &value, nil
	}

	tk, err := r.FindDefaultToolkit(ctx, toolkitID)
	if err != nil {
		res, err := r.client.Toolkit.Get(ctx, toolkitID)
		if err != nil {
			return nil, ErrToolkitNotFound
		}

		var toolkit schema.Toolkit
		if err := json.Unmarshal([]byte(res.Config), &toolkit); err != nil {
			return nil, err
		}
		tk = &toolkit
	}

	// 缓存更新
	r.toolkitCache.Add(toolkitID, *tk)
	for _, t := range tk.Tools {
		r.toolCache.Add(t.ID, tk.ID)
	}
	return tk, nil
}

func (r *ToolkitRepo) CreateToolkit(ctx context.Context, toolkit *schema.Toolkit) error {
	tools := make([]string, 0)
	for _, v := range toolkit.Tools {
		tools = append(tools, v.ID)
	}

	data, err := json.Marshal(toolkit)
	if err != nil {
		return err
	}

	_, err = r.client.Toolkit.
		Create().
		SetID(toolkit.ID).
		SetType(toolkit.Type).
		SetTools(tools).
		SetConfig(string(data)).
		Save(ctx)
	if err != nil {
		if ent.IsConstraintError(err) {
			return fmt.Errorf("%w: %v, %s",
				schema.ErrConfig, ErrToolkitDuplicate, toolkit.ID)
		}
		return err
	}

	// 缓存更新
	r.toolkitCache.Add(toolkit.ID, *toolkit)
	for _, t := range toolkit.Tools {
		r.toolCache.Add(t.ID, toolkit.ID)
	}
	return nil
}

func (r *ToolkitRepo) UpdateToolkit(ctx context.Context, toolkit *schema.Toolkit) error {
	// 默认工具不允许修改
	_, err := r.FindDefaultToolkit(ctx, toolkit.ID)
	if err == nil {
		return ErrToolkitSystemModify
	}

	tools := make([]string, 0)
	for _, v := range toolkit.Tools {
		tools = append(tools, v.ID)
	}

	data, err := json.Marshal(toolkit)
	if err != nil {
		return err
	}

	_, err = r.client.Toolkit.
		UpdateOneID(toolkit.ID).
		SetType(toolkit.Type).
		SetTools(tools).
		SetConfig(string(data)).
		Save(ctx)
	if err != nil {
		if ent.IsNotFound(err) {
			return fmt.Errorf("%w: %v, %s",
				schema.ErrConfig, ErrToolkitNotFound, toolkit.ID)
		}
		return err
	}

	r.toolkitCache.Add(toolkit.ID, *toolkit)
	for _, t := range toolkit.Tools {
		r.toolCache.Add(t.ID, toolkit.ID)
	}
	return nil
}

func (r *ToolkitRepo) UpsertToolkit(ctx context.Context, toolkit *schema.Toolkit) error {
	// 默认工具不允许修改
	_, err := r.FindDefaultToolkit(ctx, toolkit.ID)
	if err == nil {
		return ErrToolkitSystemModify
	}

	tools := make([]string, 0)
	for _, v := range toolkit.Tools {
		tools = append(tools, v.ID)
	}

	data, err := json.Marshal(toolkit)
	if err != nil {
		return err
	}

	if _, err = r.QueryToolkit(ctx, toolkit.ID); err == nil {
		_, err = r.client.Toolkit.
			UpdateOneID(toolkit.ID).
			SetType(toolkit.Type).
			SetTools(tools).
			SetConfig(string(data)).
			Save(ctx)
	} else {
		_, err = r.client.Toolkit.
			Create().
			SetID(toolkit.ID).
			SetType(toolkit.Type).
			SetTools(tools).
			SetConfig(string(data)).
			Save(ctx)
	}

	if err != nil {
		return err
	}

	r.toolkitCache.Add(toolkit.ID, *toolkit)
	for _, t := range toolkit.Tools {
		r.toolCache.Add(t.ID, toolkit.ID)
	}
	return nil
}

func (r *ToolkitRepo) DeleteToolkit(ctx context.Context, toolkitID string) error {
	// 默认工具不允许删除
	if _, err := r.FindDefaultToolkit(ctx, toolkitID); err == nil {
		return ErrToolkitSystemModify
	}

	tk, err := r.QueryToolkit(ctx, toolkitID)
	if err != nil {
		return err
	}

	r.toolkitCache.Remove(toolkitID)
	for _, v := range tk.Tools {
		r.toolCache.Remove(v.ID)
	}
	return r.client.Toolkit.DeleteOneID(toolkitID).Exec(ctx)
}

func (r *ToolkitRepo) FetchAbility(ctx context.Context, toolID string,
	options any) (schema.Tool, error) {
	var tk *schema.Toolkit
	var data *ent.Toolkit
	var err error

	tkid, ok := r.toolCache.Get(toolID)
	if ok {
		tk, err = r.QueryToolkit(ctx, tkid)
		if err != nil {
			return nil, err
		}
		goto OUT
	}

	for _, dt := range r.dtools {
		for _, t := range dt.Tools {
			if t.ID == toolID {
				tk = &dt
				goto OUT
			}
		}
	}

	data, err = r.client.Toolkit.
		Query().
		Where(
			func(s *sql.Selector) {
				s.Where(sqljson.ValueContains(toolkit.FieldTools, toolID))
			},
		).First(ctx)
	if err != nil {
		return nil, err
	}

	if err := json.Unmarshal([]byte(data.Config), &tk); err != nil {
		return nil, err
	}

OUT:
	for _, tool := range tk.Tools {
		if tool.ID == toolID {
			if tool.Creator == nil {
				if len(tool.Code) == 0 {
					if len(tool.Url) == 0 {
						tool.Url = tk.Url
					}

					tool.Creator = restful.New
				} else {
					tool.Creator = python.New
				}
			}

		LOOP:
			for _, tkp := range tk.Parameters {
				for _, tp := range tool.InputParameters {
					if tp.Name == tkp.Name {
						continue LOOP
					}
				}

				tool.InputParameters = append(tool.InputParameters, tkp)
			}

			return tool.Creator(
				schema.WithToolConfig(&tool),
				schema.WithPartOption(options),
			)
		}
	}

	return nil, fmt.Errorf("%s:%s", ErrToolkitAbilityNotFound, toolID)
}
