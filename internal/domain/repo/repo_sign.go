package repo

import (
	"context"
	"secwalk/internal/domain/repo/ent"
	"secwalk/internal/domain/repo/ent/sign"
)

type SignRepo struct {
	client *ent.Client
}

func NewSignRepo(client *ent.Client) *SignRepo {
	return &SignRepo{client: client}
}

func (r *SignRepo) GetSign(ctx context.Context, apikey string) (*ent.Sign, error) {
	return r.client.Sign.
		Query().
		Where(sign.Apikey(apikey)).
		First(ctx)
}
