package repo

import (
	"context"
	"secwalk/internal/domain/repo/ent"
	"secwalk/internal/domain/repo/ent/term"
	"sync"
	"time"
)

type TermRepo struct {
	mutex  sync.Mutex
	client *ent.Client
}

func NewTermRepo(client *ent.Client) *TermRepo {
	return &TermRepo{
		mutex:  sync.Mutex{},
		client: client,
	}
}

func (r *TermRepo) Get(ctx context.Context, aid, org string) (map[string]any, error) {
	r.mutex.Lock()
	defer r.mutex.Unlock()

	res, err := r.client.Term.Query().Where(
		term.And(
			term.Aid(aid),
			term.Org(org),
		),
	).First(ctx)
	if err != nil {
		return nil, err
	}

	return res.Content, nil
}

func (r *TermRepo) Update(ctx context.Context, aid, org, name string, kvs map[string]any) error {
	r.mutex.Lock()
	defer r.mutex.Unlock()

	res, err := r.client.Term.Query().Where(
		term.And(
			term.Aid(aid),
			term.Org(org),
		),
	).First(ctx)
	if err == nil {
		if res.Content == nil {
			res.Content = make(map[string]any)
		}
		res.Content[name] = kvs
		res.UpdateAt = time.Now().Format("2006-01-02 15:04:05")
		_, err := res.Update().Save(ctx)
		return err
	}

	_, err = r.client.Term.Create().
		SetAid(aid).
		SetOrg(org).
		SetContent(map[string]any{name: kvs}).
		SetCreateAt(time.Now().Format("2006-01-02 15:04:05")).
		SetUpdateAt(time.Now().Format("2006-01-02 15:04:05")).Save(ctx)

	return err
}
