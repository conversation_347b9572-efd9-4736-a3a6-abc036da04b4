package repo

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"secwalk/internal/domain/core/schema"
	"secwalk/internal/domain/repo/ent"
	"secwalk/pkg/dastea"
	"secwalk/pkg/license"
	"secwalk/pkg/logger"

	"github.com/hashicorp/golang-lru/v2/expirable"
	"github.com/sirupsen/logrus"
)

var (
	ErrAgentNotFound  = errors.New("agent not found")
	ErrAgentDuplicate = errors.New("agent duplicate")
)

type AgentRepo struct {
	cache  *expirable.LRU[string, *AgentConfig]
	client *ent.Client
}

func NewAgentRepo(client *ent.Client) *AgentRepo {
	return &AgentRepo{
		cache:  expirable.NewLRU[string, *AgentConfig](100, nil, time.Minute*10),
		client: client,
	}
}

func (r *AgentRepo) QueryAll(ctx context.Context) ([]AgentConfig, error) {
	rds, err := r.client.Agent.Query().All(ctx)
	if err != nil {
		return nil, err
	}

	ret := make([]AgentConfig, 0)
	for _, rd := range rds {
		agent := AgentConfig{}
		if err := json.Unmarshal([]byte(rd.Config), &agent); err != nil {
			continue
		}
		if len(rd.Extension) > 0 {
			base := schema.AgentExtension{}
			if err := json.Unmarshal([]byte(rd.Extension), &base); err != nil {
				continue
			}

		LOOP:
			// 覆盖全局配置参数
			for i := 0; i < len(base.PublicEnvs); i++ {
				for j := 0; j < len(agent.Base.PublicEnvs); j++ {
					if agent.Base.PublicEnvs[j].Name == base.PublicEnvs[i].Name {
						agent.Base.PublicEnvs[j].DefaultValue = base.PublicEnvs[i].DefaultValue
						continue LOOP
					}
				}
			}
		}
		ret = append(ret, agent)
	}

	return ret, nil
}

func (r *AgentRepo) QueryAndDecryptAll(ctx context.Context) ([]*schema.AgentConfig, error) {
	agents, err := r.QueryAll(ctx)
	if err != nil {
		return nil, err
	}

	ret := make([]*schema.AgentConfig, 0, len(agents))
	for _, agent := range agents {
		key, err := license.GetAgentKey(agent.Base.Type, agent.Base.ID)
		if err != nil {
			logrus.WithField(logger.KeyCategory, logger.CategoryDatabase).
				Debugf("the agent secret key cannot be obtained: %s", agent.Base.ID)
			continue
		}

		dagent, err := decryptAgentConfig(&agent, key)
		if err != nil {
			logrus.WithField(logger.KeyCategory, logger.CategoryDatabase).
				Debugf("the agent key is incorrect and cannot be used to decrypt: %s", agent.Base.ID)
			continue
		}
		ret = append(ret, dagent)
	}

	return ret, nil
}

func (r *AgentRepo) Query(ctx context.Context, id string) (*AgentConfig, error) {
	value, ok := r.cache.Get(id)
	if ok {
		val := *value
		return &val, nil
	}

	res, err := r.client.Agent.Get(ctx, id)
	if err != nil {
		return nil, ErrAgentNotFound
	}

	agent := &AgentConfig{}
	if err := json.Unmarshal([]byte(res.Config), agent); err != nil {
		return nil, err
	}

	if len(res.Extension) > 0 {
		base := schema.AgentExtension{}
		if err := json.Unmarshal([]byte(res.Extension), &base); err != nil {
			return nil, err
		}

	LOOP:
		// 覆盖全局配置参数
		for i := 0; i < len(base.PublicEnvs); i++ {
			for j := 0; j < len(agent.Base.PublicEnvs); j++ {
				if agent.Base.PublicEnvs[j].Name == base.PublicEnvs[i].Name {
					agent.Base.PublicEnvs[j].DefaultValue = base.PublicEnvs[i].DefaultValue
					continue LOOP
				}
			}
		}
	}

	r.cache.Add(id, agent)
	return agent, nil
}

func (r *AgentRepo) QueryAndDecrypt(ctx context.Context, id string) (*schema.AgentConfig, error) {
	agent, err := r.Query(ctx, id)
	if err != nil {
		return nil, err
	}

	key, err := license.GetAgentKey(agent.Base.Type, agent.Base.ID)
	if err != nil {
		return nil, fmt.Errorf("%w: %v", schema.ErrLicense, err)
	}

	return decryptAgentConfig(agent, key)
}

func (r *AgentRepo) QueryIDs(ctx context.Context) ([]string, error) {
	return r.client.Agent.Query().IDs(ctx)
}

func (r *AgentRepo) Export(ctx context.Context, id string) (*AgentConfig, error) {
	res, err := r.client.Agent.Get(ctx, id)
	if err != nil {
		return nil, ErrAgentNotFound
	}

	agent := &AgentConfig{}
	if err := json.Unmarshal([]byte(res.Config), agent); err != nil {
		return nil, err
	}

	return agent, nil
}

func (r *AgentRepo) Import(ctx context.Context, agent AgentConfig) error {
	data, err := json.Marshal(agent)
	if err != nil {
		return err
	}

	_, err = r.Query(ctx, agent.Base.ID)
	if err == nil {
		_, err = r.client.Agent.
			UpdateOneID(agent.Base.ID).
			SetType(agent.Base.Type).
			SetVersion(agent.Base.Version).
			SetEngVersion(agent.Base.EngVersion).
			SetConfig(string(data)).
			Save(ctx)
	} else {
		_, err = r.client.Agent.
			Create().
			SetID(agent.Base.ID).
			SetType(agent.Base.Type).
			SetVersion(agent.Base.Version).
			SetEngVersion(agent.Base.EngVersion).
			SetConfig(string(data)).
			Save(ctx)
	}
	if err != nil {
		return err
	}

	r.cache.Remove(agent.Base.ID)
	return nil
}

func (r *AgentRepo) Create(ctx context.Context, agent *schema.AgentConfig) error {
	key, err := license.GetAgentKey(agent.Type, agent.ID)
	if err != nil {
		return err
	}

	pagent, err := encryptAgentConfig(agent, key)
	if err != nil {
		return err
	}

	data, err := json.Marshal(pagent)
	if err != nil {
		return err
	}

	extension := schema.AgentExtension{
		PublicEnvs: agent.PublicEnvs,
		ID:         agent.ID,
	}

	ex, err := json.Marshal(extension)
	if err != nil {
		return err
	}

	_, err = r.client.Agent.
		Create().
		SetID(agent.ID).
		SetType(agent.Type).
		SetVersion(agent.Version).
		SetExtension(string(ex)).
		SetEngVersion(agent.EngVersion).
		SetConfig(string(data)).
		Save(ctx)
	if err != nil {
		if ent.IsConstraintError(err) {
			return fmt.Errorf("%w: %v, %s",
				schema.ErrConfig, ErrAgentDuplicate, agent.ID)
		}
		return err
	}

	r.cache.Add(agent.ID, pagent)
	return nil
}

func (r *AgentRepo) Update(ctx context.Context, agent *schema.AgentConfig) error {
	key, err := license.GetAgentKey(agent.Type, agent.ID)
	if err != nil {
		return err
	}

	pagent, err := encryptAgentConfig(agent, key)
	if err != nil {
		return err
	}

	data, err := json.Marshal(pagent)
	if err != nil {
		return err
	}

	_, err = r.client.Agent.
		UpdateOneID(agent.ID).
		SetType(agent.Type).
		SetVersion(agent.Version).
		SetEngVersion(agent.EngVersion).
		SetConfig(string(data)).
		Save(ctx)
	if err != nil {
		if ent.IsNotFound(err) {
			return fmt.Errorf("%w: %v, %s",
				schema.ErrConfig, ErrAgentNotFound, agent.ID)
		}
		return err
	}

	r.cache.Add(agent.ID, pagent)
	return nil
}

func (r *AgentRepo) UpdateExtension(ctx context.Context, base *schema.AgentExtension) error {
	data, err := json.Marshal(base)
	if err != nil {
		return err
	}

	_, err = r.client.Agent.
		UpdateOneID(base.ID).
		SetExtension(string(data)).
		Save(ctx)
	if err != nil {
		if ent.IsNotFound(err) {
			return fmt.Errorf("%w: %v, %s",
				schema.ErrConfig, ErrAgentNotFound, base.ID)
		}
		return err
	}

	r.cache.Remove(base.ID)
	return nil
}

func (r *AgentRepo) Delete(ctx context.Context, id string) error {
	r.cache.Remove(id)
	return r.client.Agent.DeleteOneID(id).Exec(ctx)
}

type AgentConfig struct {
	Base     schema.AgentBase `json:"base"`
	Workflow []byte           `json:"workflow,omitempty"`
}

func encryptAgentConfig(agent *schema.AgentConfig, key string) (*AgentConfig, error) {
	ret := &AgentConfig{Base: agent.AgentBase}

	data, err := json.Marshal(agent.AgentWorkflow)
	if err != nil {
		return nil, err
	}

	ret.Workflow = dastea.Encrypt(data, []byte(key))
	return ret, nil
}

func decryptAgentConfig(agent *AgentConfig, key string) (*schema.AgentConfig, error) {
	workflow := schema.AgentWorkflow{}

	data := dastea.Decrypt(agent.Workflow, []byte(key))
	if data == nil {
		data = dastea.Decrypt(agent.Workflow, []byte(license.DefaultKey))
	}

	if len(data) == 0 {
		return nil, schema.ErrLicenseSecret
	}

	if err := json.Unmarshal(
		data,
		&workflow,
	); err != nil {
		return nil, fmt.Errorf("%w: %v", schema.ErrConfig, err)
	}

	return &schema.AgentConfig{
		AgentBase:     agent.Base,
		AgentWorkflow: workflow,
	}, nil
}
