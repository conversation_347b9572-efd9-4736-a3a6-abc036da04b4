package handler

import (
	"secwalk/internal/domain/core/message"
	service "secwalk/internal/domain/service/session"
	"secwalk/pkg/license"

	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
)

type SessionHandler struct {
	service *service.Service
}

func NewSessionHandler(service *service.Service) *SessionHandler {
	return &SessionHandler{service: service}
}

func (h *SessionHandler) RegisterHandler(g *gin.RouterGroup) {
	g.GET("/session/:id", h.session)
	g.POST("/session/insert", h.sessionInsert)

	g.POST("/license/refresh", h.licenseRefresh)
}

func (h *SessionHandler) session(c *gin.Context) {
	session, err := h.service.QuerySession(c.Request.Context(), c.Param("id"))
	if err != nil {
		Response(c, StatusServerError, err)
		return
	}

	Response(c, StatusSuccess, session)
}

func (h *SessionHandler) sessionInsert(c *gin.Context) {
	type Request struct {
		ID      string       `json:"id" binding:"required"`
		Role    message.Role `json:"role" binding:"required"`
		Content string       `json:"content" binding:"required"`
	}

	var req Request

	if err := binding.
		Default(c.Request.Method, binding.MIMEJSON).
		Bind(c.Request, &req); err != nil {
		Response(c, StatusClientError, err)
		return
	}

	if err := h.service.InsertSession(
		c.Request.Context(),
		req.ID,
		req.Role,
		req.Content,
	); err != nil {
		Response(c, StatusServerError, err)
		return
	}

	Response(c, StatusSuccess, nil)
}

func (h *SessionHandler) licenseRefresh(c *gin.Context) {
	if err := license.Refresh(); err != nil {
		Response(c, StatusServerError, err)
		return
	}
	Response(c, StatusSuccess, nil)
}
