package handler

import (
	"secwalk/internal/domain/repo/ent"
	"secwalk/internal/domain/service/xguard"

	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
)

type XGuardHandler struct {
	service *xguard.Service
}

func NewXGuardHandler(service *xguard.Service) *XGuardHandler {
	return &XGuardHandler{service: service}
}

func (h *XGuardHandler) RegisterHandler(g *gin.RouterGroup) {
	g.GET("/xguard/trait", h.trait)
	g.POST("/xguard/trait/upsert", h.traitUpsert)
	g.POST("/xguard/trait/insert", h.traitInsert)
	g.POST("/xguard/trait/delete", h.traitDelete)
}

func (h *XGuardHandler) trait(c *gin.Context) {
	traits, err := h.service.TraitQuery(c.Request.Context())
	if err != nil {
		Response(c, StatusServerError, err)
		return
	}
	Response(c, StatusSuccess, traits)
}

func (h *<PERSON><PERSON><PERSON><PERSON>and<PERSON>) traitUpsert(c *gin.Context) {
	var req []*ent.Trait

	if err := binding.
		Default(c.Request.Method, binding.MIMEJSON).
		Bind(c.Request, &req); err != nil {
		Response(c, StatusClientError, err)
		return
	}

	if err := h.service.TraitUpsert(c.Request.Context(), req); err != nil {
		Response(c, StatusServerError, err)
		return
	}

	Response(c, StatusSuccess, nil)
}

func (h *XGuardHandler) traitInsert(c *gin.Context) {
	var req []*ent.Trait

	if err := binding.
		Default(c.Request.Method, binding.MIMEJSON).
		Bind(c.Request, &req); err != nil {
		Response(c, StatusClientError, err)
		return
	}

	if err := h.service.TraitInsert(c.Request.Context(), req); err != nil {
		Response(c, StatusServerError, err)
		return
	}

	Response(c, StatusSuccess, nil)
}

func (h *XGuardHandler) traitDelete(c *gin.Context) {
	var req []string

	if err := binding.
		Default(c.Request.Method, binding.MIMEJSON).
		Bind(c.Request, &req); err != nil {
		Response(c, StatusClientError, err)
		return
	}

	if err := h.service.TraitDelete(c.Request.Context(), req); err != nil {
		Response(c, StatusServerError, err)
		return
	}

	Response(c, StatusSuccess, nil)
}
