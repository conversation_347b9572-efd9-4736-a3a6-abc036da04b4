package handler

import (
	"secwalk/internal/domain/core/callback"
	"secwalk/internal/domain/core/schema"
	"secwalk/internal/domain/repo"
	service "secwalk/internal/domain/service/agent"
	"sync"

	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
)

type AgentHandler struct {
	service *service.Service
}

func NewAgentHandler(service *service.Service) Register {
	return &AgentHandler{service: service}
}

func (h *AgentHandler) RegisterHandler(g *gin.RouterGroup) {
	g.POST("/agent", h.agent)
	g.GET("/agent/ids", h.agentIDs)
	g.POST("/agent/export", h.agentExport)
	g.POST("/agent/import", h.agentImport)
	g.POST("/agent/create", h.agentCreate)
	g.POST("/agent/update", h.agentUpdate)
	g.POST("/agent/update/extension", h.agentUpdateExtension)
	g.POST("/agent/upsert", h.agentUpsert)
	g.POST("/agent/delete/:id", h.agentDelete)
	g.POST("/agent/verify", h.agentVerify)
	g.POST("/agent/execute", h.agentExecute)
	g.POST("/agent/execute/debug", h.agentExecuteDebug)
	g.POST("/agent/execute/resume", h.agentExecuteResume)

	g.POST("/agent/task/submit", h.agentTaskSubmit)
	g.POST("/agent/task/cancel", h.agentTaskCancel)
	g.POST("/agent/task/messages", h.agentTaskMessages)
}

// 获取多个智能体智能体
func (h *AgentHandler) agent(c *gin.Context) {
	type Request struct {
		IDs []string `json:"ids,omitempty"`
	}

	var req Request

	if err := binding.
		Default(c.Request.Method, binding.MIMEJSON).
		Bind(c.Request, &req); err != nil {
		Response(c, StatusClientError, err)
		return
	}

	agents, err := h.service.QueryAgents(c.Request.Context(), req.IDs)
	if err != nil {
		Response(c, StatusServerError, err)
		return
	}

	Response(c, StatusSuccess, agents)
}

func (h *AgentHandler) agentIDs(c *gin.Context) {
	ids, err := h.service.QueryIDs(c.Request.Context())
	if err != nil {
		Response(c, StatusServerError, err)
		return
	}

	Response(c, StatusSuccess, ids)
}

// 导出智能体
func (h *AgentHandler) agentExport(c *gin.Context) {
	type Request struct {
		IDs []string `json:"ids" binding:"required"`
	}

	var req Request

	if err := binding.
		Default(c.Request.Method, binding.MIMEJSON).
		Bind(c.Request, &req); err != nil {
		Response(c, StatusClientError, err)
		return
	}

	agents, err := h.service.ExportAgent(c.Request.Context(), req.IDs)
	if err != nil {
		Response(c, StatusServerError, err)
		return
	}

	Response(c, StatusSuccess, agents)
}

// 导入智能体
func (h *AgentHandler) agentImport(c *gin.Context) {
	var req []repo.AgentConfig

	if err := binding.
		Default(c.Request.Method, binding.MIMEJSON).
		Bind(c.Request, &req); err != nil {
		Response(c, StatusClientError, err)
		return
	}

	if err := h.service.ImportAgent(c.Request.Context(), req); err != nil {
		Response(c, StatusServerError, err)
		return
	}

	Response(c, StatusSuccess, nil)
}

// 智能体创建
func (h *AgentHandler) agentCreate(c *gin.Context) {
	var agent schema.AgentConfig

	if err := binding.
		Default(c.Request.Method, binding.MIMEJSON).
		Bind(c.Request, &agent); err != nil {
		Response(c, StatusClientError, err)
		return
	}

	if err := h.service.CreateAgent(c.Request.Context(), &agent); err != nil {
		Response(c, StatusServerError, err)
		return
	}

	Response(c, StatusSuccess, &agent)
}

// 智能体更新
func (h *AgentHandler) agentUpdate(c *gin.Context) {
	var agent schema.AgentConfig

	if err := binding.
		Default(c.Request.Method, binding.MIMEJSON).
		Bind(c.Request, &agent); err != nil {
		Response(c, StatusClientError, err)
		return
	}

	if err := h.service.UpdateAgent(c.Request.Context(), &agent); err != nil {
		Response(c, StatusServerError, err)
		return
	}

	Response(c, StatusSuccess, &agent)
}

// 兼容创建与更新
func (h *AgentHandler) agentUpsert(c *gin.Context) {
	var agent schema.AgentConfig

	if err := binding.
		Default(c.Request.Method, binding.MIMEJSON).
		Bind(c.Request, &agent); err != nil {
		Response(c, StatusClientError, err)
		return
	}

	if err := h.service.UpsertAgent(c.Request.Context(), &agent); err != nil {
		Response(c, StatusServerError, err)
		return
	}

	Response(c, StatusSuccess, &agent)
}

// 升级基础配置
func (h *AgentHandler) agentUpdateExtension(c *gin.Context) {
	var base schema.AgentExtension

	if err := binding.
		Default(c.Request.Method, binding.MIMEJSON).
		Bind(c.Request, &base); err != nil {
		Response(c, StatusClientError, err)
		return
	}

	if err := h.service.UpdateAgentExtension(c.Request.Context(), &base); err != nil {
		Response(c, StatusServerError, err)
		return
	}

	Response(c, StatusSuccess, &base)
}

// 智能体删除
func (h *AgentHandler) agentDelete(c *gin.Context) {
	if err := h.service.DeleteAgent(c.Request.Context(), c.Param("id")); err != nil {
		Response(c, StatusServerError, err)
		return
	}

	Response(c, StatusSuccess, nil)
}

// 智能体配置校验
func (h *AgentHandler) agentVerify(c *gin.Context) {
	var agent schema.AgentConfig

	if err := binding.
		Default(c.Request.Method, binding.MIMEJSON).
		Bind(c.Request, &agent); err != nil {
		Response(c, StatusClientError, err)
		return
	}

	if err := h.service.VerifyAgent(c.Request.Context(), &agent); err != nil {
		Response(c, StatusServerError, err)
		return
	}

	Response(c, StatusSuccess, &agent)
}

// 智能体执行
func (h *AgentHandler) agentExecute(c *gin.Context) {
	type Request struct {
		AID        string         `json:"aid,omitempty" binding:"required"`
		SID        string         `json:"sid,omitempty"`
		QID        string         `json:"qid,omitempty"`
		Input      string         `json:"input,omitempty"`
		Inputs     map[string]any `json:"inputs,omitempty"`
		Stream     bool           `json:"stream,omitempty"`
		DetailTree int            `json:"detail_tree,omitempty"`
	}

	var req Request
	var mux sync.Mutex

	if err := binding.
		Default(c.Request.Method, binding.MIMEJSON).
		Bind(c.Request, &req); err != nil {
		Response(c, StatusClientError, err)
		return
	}

	opt := []service.Option{
		service.WithEXT(c.GetString("EXT")),
		service.WithQID(req.QID),
	}
	if len(req.SID) > 0 {
		opt = append(opt, service.WithSID(req.SID))
	}

	if req.Inputs == nil {
		req.Inputs = make(map[string]any)
	}

	if req.DetailTree > 0 {
		opt = append(opt, // 详细输出步骤完整信息
			service.WithStreamVerbose(func(so *callback.VerboseMessage) error {
				mux.Lock()
				defer mux.Unlock()
				so.Mode = callback.ModeVerbose
				StreamResponse(c, StatusSuccess, so)
				return nil
			}))
	}

	_, ok := req.Inputs["input"]
	if !ok {
		req.Inputs["input"] = req.Input
	}

	if req.Stream {
		defer c.SSEvent("", "[DONE]")

		_, err := h.service.ExecuteAgent(
			c.Request.Context(),
			req.Inputs,
			append(opt,
				service.WithAID(req.AID),
				// 输出预览对话信息
				service.WithStreamPreview(func(ao *callback.PreviewMessage) error {
					mux.Lock()
					defer mux.Unlock()

					if ao.Content == callback.EOFMARK {
						return nil
					}
					ao.Mode = callback.ModePreview
					StreamResponse(c, StatusSuccess, ao)
					return nil
				}),
				service.WithFnQuestion(func(qm *callback.QuestionMessage) error {
					mux.Lock()
					defer mux.Unlock()

					StreamResponse(c, StatusSuccess, qm)
					return nil
				}),
			)...,
		)
		if err != nil {
			StreamResponse(c, StatusServerError, err)
		}
	} else {
		res, err := h.service.ExecuteAgent(
			c.Request.Context(),
			req.Inputs,
			append(opt, service.WithAID(req.AID))...,
		)
		if err != nil {
			Response(c, StatusServerError, err)
			return
		}

		Response(c, StatusSuccess, res)
	}
}

// 智能体试运行
func (h *AgentHandler) agentExecuteDebug(c *gin.Context) {
	// 结束返回[DONE]
	defer c.SSEvent("", "[DONE]")

	type Request struct {
		Config     *schema.AgentConfig `json:"config" binding:"required"`
		SID        string              `json:"sid,omitempty"`
		QID        string              `json:"qid,omitempty"`
		Input      string              `json:"input,omitempty"`
		Inputs     map[string]any      `json:"inputs,omitempty"`
		NodeID     string              `json:"node_id,omitempty"`
		Variables  map[string]any      `json:"variables,omitempty"`
		Interrupt  int                 `json:"interrupt,omitempty"`
		DetailTree int                 `json:"detail_tree,omitempty"`
	}

	var req Request
	var mux sync.Mutex

	// 参数错误直接返回
	if err := binding.
		Default(c.Request.Method, binding.MIMEJSON).
		Bind(c.Request, &req); err != nil {
		StreamResponse(c, StatusClientError, err)
		return
	}

	opt := []service.Option{
		service.WithEXT(c.GetString("EXT")),
		service.WithQID(req.QID),
	}
	if len(req.SID) > 0 {
		opt = append(opt, service.WithSID(req.SID))
	}

	if req.Inputs == nil {
		req.Inputs = make(map[string]any)
	}

	_, ok := req.Inputs["input"]
	if !ok {
		req.Inputs["input"] = req.Input
	}

	if req.Interrupt == 1 {
		req.Config.Edges = make([]*schema.Edge, 0)
	}

	if len(req.NodeID) > 0 {
		req.Config.Entry = req.NodeID
	}

	if _, err := h.service.ExecuteAgent(
		c.Request.Context(),
		req.Inputs,
		append(opt,
			service.WithConfig(req.Config),
			service.WithVariables(req.Variables),
			// 详细输出步骤完整信息
			service.WithStreamVerbose(func(so *callback.VerboseMessage) error {
				mux.Lock()
				defer mux.Unlock()

				if req.DetailTree > 0 || so.Type == callback.TypeAgent || so.Type == callback.TypeNode {
					so.Mode = callback.ModeVerbose
					StreamResponse(c, StatusSuccess, so)
				}

				return nil
			}),
			// 输出预览对话信息
			service.WithStreamPreview(func(ao *callback.PreviewMessage) error {
				mux.Lock()
				defer mux.Unlock()

				if ao.Content == callback.EOFMARK {
					return nil
				}
				ao.Mode = callback.ModePreview
				StreamResponse(c, StatusSuccess, ao)
				return nil
			}),
			service.WithFnQuestion(func(qm *callback.QuestionMessage) error {
				mux.Lock()
				defer mux.Unlock()

				StreamResponse(c, StatusSuccess, qm)
				return nil
			}),
		)...,
	); err != nil {
		StreamResponse(c, StatusServerError, err)
	}
}

func (h *AgentHandler) agentExecuteResume(c *gin.Context) {
	type Request struct {
		MessageID string         `json:"message_id" binding:"required"`
		Inputs    map[string]any `json:"inputs" binding:"required"`
	}

	var req Request

	// 参数错误直接返回
	if err := binding.
		Default(c.Request.Method, binding.MIMEJSON).
		Bind(c.Request, &req); err != nil {
		StreamResponse(c, StatusClientError, err)
		return
	}

	if err := callback.RecvQuestionResponse(req.MessageID, req.Inputs); err != nil {
		Response(c, StatusServerError, err)
		return
	}
	Response(c, StatusSuccess, nil)
}

// 智能体异步执行
func (h *AgentHandler) agentTaskSubmit(c *gin.Context) {
	type Request struct {
		AID        string         `json:"aid,omitempty" binding:"required"`
		SID        string         `json:"sid,omitempty"`
		QID        string         `json:"qid,omitempty"`
		Input      string         `json:"input,omitempty"`
		Inputs     map[string]any `json:"inputs,omitempty"`
		DetailTree int            `json:"detail_tree,omitempty"`
	}

	var req Request
	if err := binding.
		Default(c.Request.Method, binding.MIMEJSON).
		Bind(c.Request, &req); err != nil {
		Response(c, StatusClientError, err)
		return
	}

	opt := []service.Option{
		service.WithEXT(c.GetString("EXT")),
		service.WithQID(req.QID),
	}
	if len(req.SID) > 0 {
		opt = append(opt, service.WithSID(req.SID))
	}

	var mux sync.Mutex
	if req.DetailTree > 0 {
		opt = append(opt, // 详细输出步骤完整信息
			service.WithStreamVerbose(func(so *callback.VerboseMessage) error {
				mux.Lock()
				defer mux.Unlock()
				so.Mode = callback.ModeVerbose
				StreamResponse(c, StatusSuccess, so)
				return nil
			}))
	}

	if req.Inputs == nil {
		req.Inputs = make(map[string]any)
	}

	_, ok := req.Inputs["input"]
	if !ok {
		req.Inputs["input"] = req.Input
	}

	res, err := h.service.TaskSubmit(
		c.Request.Context(),
		req.Inputs,
		append(opt,
			service.WithAID(req.AID),
		)...,
	)
	if err != nil {
		Response(c, StatusServerError, err)
		return
	}

	Response(c, StatusSuccess, res)
}

func (h *AgentHandler) agentTaskCancel(c *gin.Context) {
	type Request struct {
		SID string `json:"sid,omitempty" binding:"required"`
		QID string `json:"qid,omitempty" binding:"required"`
	}

	var req Request
	if err := binding.
		Default(c.Request.Method, binding.MIMEJSON).
		Bind(c.Request, &req); err != nil {
		Response(c, StatusClientError, err)
		return
	}

	if err := h.service.TaskCancel(req.SID + req.QID); err != nil {
		Response(c, StatusServerError, err)
		return
	}
	Response(c, StatusSuccess, nil)
}

func (h *AgentHandler) agentTaskMessages(c *gin.Context) {
	type Request struct {
		SID string `json:"sid,omitempty" binding:"required"`
		QID string `json:"qid,omitempty" binding:"required"`
	}

	var req Request
	if err := binding.
		Default(c.Request.Method, binding.MIMEJSON).
		Bind(c.Request, &req); err != nil {
		Response(c, StatusClientError, err)
		return
	}

	res, err := h.service.TaskMessages(req.SID + req.QID)
	if err != nil {
		Response(c, StatusServerError, err)
		return
	}
	Response(c, StatusSuccess, res)
}
