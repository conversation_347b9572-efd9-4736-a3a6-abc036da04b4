package handler

import (
	"secwalk/internal/domain/core/schema"
	"secwalk/internal/domain/service/mcp"

	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
)

type MCPHandler struct {
	service *mcp.Service
}

func NewMCPHandler(service *mcp.Service) Register {
	return &MCPHandler{service: service}
}

func (h *MCPHandler) RegisterHandler(g *gin.RouterGroup) {
	g.POST("/mcp/server/info", h.mcpServerInfo)
	g.POST("/mcp/upsert", h.mcpUpsert)
	g.POST("/mcp/delete/:id", h.mcpDelete)
}

func (h *MCPHandler) mcpServerInfo(c *gin.Context) {
	var req schema.ServersFile

	if err := binding.
		Default(c.Request.Method, binding.MIMEJSON).
		Bind(c.Request, &req); err != nil {
		Response(c, StatusClientError, err)
		return
	}

	res, err := h.service.GetMCPServerInfo(c.Request.Context(), req,
		schema.WithEXTString(c.GetHeader("EXT")),
		schema.WithXUID(c.GetHeader("X-UID")),
		schema.WithXORG(c.GetHeader("X-ORG")))
	if err != nil {
		Response(c, StatusServerError, err)
		return
	}

	Response(c, StatusSuccess, res)
}

func (h *MCPHandler) mcpUpsert(c *gin.Context) {
	var req schema.ServersFile

	if err := binding.
		Default(c.Request.Method, binding.MIMEJSON).
		Bind(c.Request, &req); err != nil {
		Response(c, StatusClientError, err)
		return
	}

	if err := h.service.UpsertMCPServer(c.Request.Context(), req); err != nil {
		Response(c, StatusServerError, err)
		return
	}

	Response(c, StatusSuccess, nil)
}

func (h *MCPHandler) mcpDelete(c *gin.Context) {
	if err := h.service.DeleteMCPServer(c.Request.Context(),
		c.Param("id")); err != nil {
		Response(c, StatusServerError, err)
		return
	}

	Response(c, StatusSuccess, nil)
}
