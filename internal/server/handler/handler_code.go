package handler

import (
	"errors"
	"fmt"
	"net/http"
	"secwalk/internal/domain/core/schema"
	"secwalk/pkg/logger"
	"secwalk/pkg/util"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

const (
	StatusSuccess        = 200
	StatusClientError    = 400
	StatusParameterError = 401
	StatusServerError    = 500

	StatusConfigError           = 501
	StatusInputsError           = 502
	StatusMiddlewareError       = 503
	StatusGatewayError          = 504
	StatusLicenseError          = 505
	StatusLicenseSecretError    = 506
	StatusMCPConfigError        = 507
	StatusMCPServerConnectError = 508
	StatusMCPServerRequestError = 509
	StatusToolCall              = 510
	StatusLLMCall               = 511

	StatusTaskError = 601
)

var codeMap = map[int]string{
	StatusSuccess:               "Success",
	StatusClientError:           "Client request parameter error",
	StatusParameterError:        "Please check parameter",
	StatusServerError:           "Service internal error",
	StatusConfigError:           "Please check configuration",
	StatusInputsError:           "Please check inputs",
	StatusMiddlewareError:       "Middleware service error",
	StatusGatewayError:          "Model gateway service error",
	StatusLicenseError:          "Agent authorization certificate error",
	StatusLicenseSecretError:    "Agent authorization key error",
	StatusMCPConfigError:        "MCP service configuration error, please check configuration details",
	StatusMCPServerConnectError: "MCP service connection error",
	StatusMCPServerRequestError: "MCP service request error",
	StatusToolCall:              "Tool call failed",
	StatusLLMCall:               "LLM request failed",
	StatusTaskError:             "Task operation failed",
}

func GetCode(code int, err error) int {
	if errors.Is(err, schema.ErrParameter) {
		return StatusParameterError
	} else if errors.Is(err, schema.ErrConfig) {
		return StatusConfigError
	} else if errors.Is(err, schema.ErrInputs) {
		return StatusInputsError
	} else if errors.Is(err, schema.ErrMiddleware) {
		return StatusMiddlewareError
	} else if errors.Is(err, schema.ErrGateway) {
		return StatusGatewayError
	} else if errors.Is(err, schema.ErrLicense) {
		return StatusLicenseError
	} else if errors.Is(err, schema.ErrLicenseSecret) {
		return StatusLicenseSecretError
	} else if errors.Is(err, schema.ErrTaskID) {
		return StatusTaskError
	} else if errors.Is(err, schema.ErrMCPServerConnect) {
		return StatusMCPServerConnectError
	} else if errors.Is(err, schema.ErrMCPServerRequest) {
		return StatusMCPServerRequestError
	} else if errors.Is(err, schema.ErrMCPServerConfig) {
		return StatusMCPConfigError
	} else if errors.Is(err, schema.ErrAbilityExecute) ||
		errors.Is(err, schema.ErrAbilityExecuteResult) {
		return StatusToolCall
	} else if errors.Is(err, schema.ErrLLMCall) {
		return StatusLLMCall
	}

	return code
}

func GetMsg(code int) string {
	msg, ok := codeMap[code]
	if ok {
		return msg
	}

	return codeMap[StatusServerError]
}

func Response(c *gin.Context, errCode int, data any) {
	type Response struct {
		Code    int    `json:"code"`
		Message string `json:"message,omitempty"`
		Data    any    `json:"data,omitempty"`
	}

	if err, ok := data.(error); ok {
		errCode = GetCode(errCode, err)

		logrus.WithField(logger.KeyCategory, logger.CategoryInterface).
			WithField(logger.KeyTID, c.GetHeader("X-TID")).
			Errorf("restful response, status: %d, error: %s", errCode, err.Error())

		c.PureJSON(http.StatusOK, Response{
			Code:    errCode,
			Message: fmt.Sprintf("%s (%s)", GetMsg(errCode), err.Error()),
		})
	} else {
		c.PureJSON(http.StatusOK, Response{
			Code:    errCode,
			Message: GetMsg(errCode),
			Data:    data,
		})
	}
}

func StreamResponse(c *gin.Context, errCode int, data any) error {
	type Response struct {
		Code    int    `json:"code"`
		Message string `json:"message,omitempty"`
		Data    any    `json:"data,omitempty"`
	}

	var res Response

	if err, ok := data.(error); ok {
		errCode = GetCode(errCode, err)

		logrus.WithField(logger.KeyCategory, logger.CategoryInterface).
			WithField(logger.KeyTID, c.GetHeader("X-TID")).
			Errorf("restful response, status: %d, error: %s", errCode, err.Error())

		res = Response{
			Code:    errCode,
			Message: fmt.Sprintf("%s (%s)", GetMsg(errCode), err.Error()),
		}
	} else {
		res = Response{
			Code:    errCode,
			Message: GetMsg(errCode),
			Data:    data,
		}
	}

	c.SSEvent("", string(util.ToPureJsonString(res)))
	c.Writer.Flush()
	return nil
}
