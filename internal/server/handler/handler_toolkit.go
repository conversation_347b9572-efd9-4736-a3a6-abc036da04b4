package handler

import (
	"secwalk/internal/domain/core/schema"
	"secwalk/internal/domain/service/toolkit"
	"secwalk/pkg/logger"
	"secwalk/pkg/util"

	"github.com/gin-contrib/requestid"
	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
	"github.com/sirupsen/logrus"
)

type ToolkitHandler struct {
	service *toolkit.Service
}

func NewToolkitHandler(service *toolkit.Service) Register {
	return &ToolkitHandler{service: service}
}

func (h *ToolkitHandler) RegisterHandler(g *gin.RouterGroup) {
	g.POST("/toolkit", h.toolkit)
	g.POST("/toolkit/system", h.toolkitSystem)
	g.POST("/toolkit/create", h.toolkitCreate)
	g.POST("/toolkit/update", h.toolkitUpdate)
	g.POST("/toolkit/upsert", h.toolkitUpsert)
	g.POST("/toolkit/delete/:id", h.toolkitDelete)
	g.POST("/ability/execute", h.toolkitExecute)
	g.POST("/ability/execute/debug", h.toolkitExecuteDebug)
}

// 获取多个工具集
func (h *ToolkitHandler) toolkit(c *gin.Context) {
	type Request struct {
		IDs []string `json:"ids,omitempty"`
	}

	var req Request

	if err := binding.
		Default(c.Request.Method, binding.MIMEJSON).
		Bind(c.Request, &req); err != nil {
		Response(c, StatusClientError, err)
		return
	}

	tks, err := h.service.QueryToolkits(c.Request.Context(), req.IDs)
	if err != nil {
		Response(c, StatusServerError, err)
		return
	}

	Response(c, StatusSuccess, tks)
}

// 获取系统工具集
func (h *ToolkitHandler) toolkitSystem(c *gin.Context) {
	tks, err := h.service.QuerySystemToolkits(c.Request.Context())
	if err != nil {
		Response(c, StatusServerError, err)
		return
	}

	Response(c, StatusSuccess, tks)
}

// 创建工具集
func (h *ToolkitHandler) toolkitCreate(c *gin.Context) {
	var tk schema.Toolkit

	if err := binding.
		Default(c.Request.Method, binding.MIMEJSON).
		Bind(c.Request, &tk); err != nil {
		Response(c, StatusClientError, err)
		return
	}

	logrus.WithField(logger.KeyCategory, logger.CategoryService).
		Infof("request-id: %s, create toolkit: %s", requestid.Get(c), util.ToJsonString(tk))
	if err := h.service.CreateToolkit(c.Request.Context(), &tk); err != nil {
		Response(c, StatusServerError, err)
		return
	}

	Response(c, StatusSuccess, &tk)
}

// 更新工具集
func (h *ToolkitHandler) toolkitUpdate(c *gin.Context) {
	var tk schema.Toolkit

	if err := binding.
		Default(c.Request.Method, binding.MIMEJSON).
		Bind(c.Request, &tk); err != nil {
		Response(c, StatusClientError, err)
		return
	}

	logrus.WithField(logger.KeyCategory, logger.CategoryService).
		Infof("request-id: %s, update toolkit: %v", requestid.Get(c), tk)
	if err := h.service.UpdateToolkit(c.Request.Context(), &tk); err != nil {
		Response(c, StatusServerError, err)
		return
	}

	Response(c, StatusSuccess, &tk)
}

// 创建更新工具集
func (h *ToolkitHandler) toolkitUpsert(c *gin.Context) {
	var tk schema.Toolkit

	if err := binding.
		Default(c.Request.Method, binding.MIMEJSON).
		Bind(c.Request, &tk); err != nil {
		Response(c, StatusClientError, err)
		return
	}

	logrus.WithField(logger.KeyCategory, logger.CategoryService).
		Infof("request-id: %s, upsert toolkit: %s", requestid.Get(c), util.ToJsonString(tk))
	if err := h.service.UpsertToolkit(c.Request.Context(), &tk); err != nil {
		Response(c, StatusServerError, err)
		return
	}

	Response(c, StatusSuccess, &tk)
}

// 删除工具集
func (h *ToolkitHandler) toolkitDelete(c *gin.Context) {
	id := c.Param("id")
	if len(id) == 0 {
		Response(c, StatusClientError, nil)
		return
	}

	logrus.WithField(logger.KeyCategory, logger.CategoryService).
		Infof("request-id: %s, delete toolkit: %s", requestid.Get(c), id)
	if err := h.service.DeleteToolkit(c.Request.Context(), id); err != nil {
		Response(c, StatusServerError, err)
		return
	}

	Response(c, StatusSuccess, nil)
}

// 调试工具
func (h *ToolkitHandler) toolkitExecuteDebug(c *gin.Context) {
	type Request struct {
		Config *schema.Toolkit `json:"config" binding:"required"`
		Inputs map[string]any  `json:"inputs,omitempty"`
	}

	type Result struct {
		Content string `json:"content,omitempty"`
	}

	var req Request

	if err := c.BindJSON(&req); err != nil {
		Response(c, StatusClientError, err)
		return
	}

	logrus.WithField(logger.KeyCategory, logger.CategoryService).
		Infof("request-id: %s, debug ability: %s",
			requestid.Get(c), util.ToJsonString(req))
	res, err := h.service.DebugAbility(c.Request.Context(),
		req.Config, req.Inputs,
		schema.WithEXTString(c.GetString("EXT")),
	)
	if err != nil {
		Response(c, StatusServerError, err)
		return
	}

	Response(c, StatusSuccess, Result{Content: res})
}

// 执行工具
func (h *ToolkitHandler) toolkitExecute(c *gin.Context) {
	type Request struct {
		ID     string         `json:"id,omitempty" binding:"required"`
		Inputs map[string]any `json:"inputs,omitempty"`
	}

	type Result struct {
		Content string `json:"content,omitempty"`
	}

	var req Request

	if err := c.BindJSON(&req); err != nil {
		Response(c, StatusClientError, err)
		return
	}

	logrus.WithField(logger.KeyCategory, logger.CategoryService).
		Infof("request-id: %s, execute ability: %s", requestid.Get(c), util.ToJsonString(req))
	res, err := h.service.ExecuteAbility(c.Request.Context(),
		req.ID, req.Inputs,
		schema.WithEXTString(c.GetHeader("EXT")),
	)
	if err != nil {
		Response(c, StatusServerError, err)
		return
	}

	Response(c, StatusSuccess, Result{Content: res})
}
