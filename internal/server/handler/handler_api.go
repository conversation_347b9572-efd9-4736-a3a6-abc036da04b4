package handler

import (
	"net/http"
	"secwalk/internal/domain/core/endpoint"
	"secwalk/internal/domain/core/schema/ext"

	"github.com/gin-gonic/gin"
)

type APIHandler struct {
}

func NewAPIHandler() Register {
	return &APIHandler{}
}

func (h *APIHandler) RegisterHandler(g *gin.RouterGroup) {
	// oss api
	g.GET("/oss/file/:id", h.getOssFile)
	g.POST("/oss/file/:id", h.postOssFile)
}

func (h *APIHandler) getOssFile(c *gin.Context) {
	data, err := endpoint.GetFile(c.Request.Context(), c.<PERSON>m("id"),
		ext.New(c.GetString("EXT")))
	if err != nil {
		c.Status(http.StatusBadRequest)
		return
	}
	c.Data(http.StatusOK, "application/octet-stream", data)
}

func (h *APIHandler) postOssFile(c *gin.Context) {
	data, err := c.GetRawData()
	if err != nil {
		c.Status(http.StatusBadRequest)
		return
	}

	err = endpoint.PutFile(c.Request.Context(), c.Param("id"), data)
	if err != nil {
		c.Status(http.StatusBadRequest)
		return
	}
}
