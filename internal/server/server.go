package server

import (
	"secwalk/internal/config"
	"secwalk/internal/domain/repo"
	"secwalk/internal/domain/service/agent"
	"secwalk/internal/domain/service/mcp"
	"secwalk/internal/domain/service/session"
	"secwalk/internal/domain/service/toolkit"
	"secwalk/internal/domain/service/xguard"
	"secwalk/internal/server/handler"
	"secwalk/pkg/register"
	"time"

	"github.com/go-micro/plugins/v4/logger/logrus"
	"github.com/go-micro/plugins/v4/server/http"
	glog "github.com/sirupsen/logrus"
	"go-micro.dev/v4"
	"go-micro.dev/v4/logger"
	"go-micro.dev/v4/server"
)

func New(agent *agent.Service, toolkit *toolkit.Service,
	session *session.Service, xguard *xguard.Service,
	mcp *mcp.Service, signRepo *repo.SignRepo) (micro.Service, error) {
	// 设置服务日志记录器
	logger.DefaultLogger = logrus.NewLogger(
		logrus.WithLogger(glog.StandardLogger()))

	// 创建HTTP服务
	srv := http.NewServer(
		server.Name(config.GetServer().Name),
		server.Address(config.GetServer().Address),
		server.Version(config.GetVersion()),
	)

	// 设置HTTP路由
	srv.Handle(srv.NewHandler(handler.Init(
		agent, toolkit, session, xguard, mcp, signRepo)))

	// 创建Micro服务
	service := micro.NewService(
		micro.Server(srv),
		micro.Version(config.GetVersion()),
		micro.Registry(register.NewMicroRegister(config.GetRegister())),
		micro.RegisterTTL(time.Second*10),
		micro.RegisterInterval(time.Second*5),
		micro.Metadata(map[string]string{
			"version": config.GetVersion(),
		}),
	)

	return service, nil
}
