package auth

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"fmt"
	"net/http"
	"secwalk/internal/domain/repo"
	"secwalk/pkg/logger"
	"secwalk/pkg/random"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

const (
	_PrefixID  = "secark-"
	_PrefixKey = "apikey-"
)

// 请求鉴权
func Auth(repo *repo.SignRepo) gin.HandlerFunc {
	return func(c *gin.Context) {
		var key, sign string

		authorization := c.Request.Header.Get("Authorization")
		if authorization == "" {
			key = c.Query("key")
		} else {
			key = strings.TrimPrefix(authorization, "Bearer ")
		}

		if key == "" {
			c.Status(http.StatusUnauthorized)
			c.Abort()
			return
		}

		sign = c.Request.Header.Get("Sign")
		if sign == "" {
			sign = c.Query("sign")
			// 处理url编码
			sign = strings.ReplaceAll(sign, " ", "+")
		}

		product, err := repo.GetSign(c.Request.Context(), key)
		if err != nil || product == nil {
			logrus.WithField(logger.KeyCategory, logger.CategoryInterface).
				WithField(logger.KeyTID, c.GetHeader("X-TID")).
				Warnf("product not found, key: %s", key)
			c.Status(http.StatusUnauthorized)
			c.Abort()
			return
		}

		// 验签
		if !Verify(sign, product.Secret, product.Apikey) {
			logrus.WithField(logger.KeyCategory, logger.CategoryInterface).
				WithField(logger.KeyTID, c.GetHeader("X-TID")).
				Warnf("sign verification failed, key: %s, product: %s", key, product.Name)
			c.Status(http.StatusUnauthorized)
			c.Abort()
			return
		}

		logrus.WithField(logger.KeyCategory, logger.CategoryInterface).
			Debugf("sign verification success, key: %s, product: %s", key, product.Name)
		c.Set("product", product)
		c.Next()
	}
}

func GenProductID() string {
	return _PrefixID + random.RandomID()
}

func GenProductKey() string {
	return _PrefixKey + random.RandomID()
}

func Sign(ts int64, secret, key string) string {
	hash := hmac.New(sha256.New, []byte(secret))
	hash.Write([]byte(fmt.Sprintf("%d\n%s\n%s", ts, secret, key)))
	return fmt.Sprintf("%d%s", ts, base64.StdEncoding.EncodeToString(hash.Sum(nil)))
}

func Verify(sign string, secret, key string) bool {
	var timestamp string
	if len(sign) > 13 {
		timestamp = sign[:13]
	}

	ts, err := strconv.ParseInt(timestamp, 10, 64)
	if err != nil {
		return false
	}

	// 计算timestamp有效期
	if (time.Now().UnixMilli() - ts) > 24*time.Hour.Milliseconds() {
		return false
	}

	return strings.Compare(sign, Sign(ts, secret, key)) == 0
}
