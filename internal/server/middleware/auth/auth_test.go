package auth

import (
	"fmt"
	"testing"
	"time"
)

func TestGen(t *testing.T) {
	fmt.Println(GenProductID())
	fmt.Println(GenProductKey())
}

func TestSign(t *testing.T) {
	secret := "secark-2bvyffqo85xbeasazc17btdvqae7ifqe"
	key := "apikey-rghs5piuwkdqouuu8bv5c54azrgotbil"
	ts := time.Now().UnixMilli()

	sign := Sign(ts, secret, key)
	ok := Verify(sign, secret, key)
	fmt.Println(sign, ok)
}

func TestVerify(t *testing.T) {
	secret := "secark-2bvyffqo85xbeasazc17btdvqae7ifqe"
	key := "apikey-rghs5piuwkdqouuu8bv5c54azrgotbil"
	sign := "1731032034914ZtdHSUlvWYPdwU/b8pd6i+Yhz8kWVI2taUbopjHFRiY="

	ok := Verify(sign, secret, key)
	fmt.Println(sign, ok)
}
