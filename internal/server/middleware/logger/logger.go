package logger

import (
	"secwalk/internal/domain/core/schema/ext"
	"secwalk/pkg/logger"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

func NewLogger() gin.HandlerFunc {
	return func(c *gin.Context) {
		logrus.WithField(logger.KeyCategory, logger.CategoryInterface).
			WithField(logger.KeyTID, c.<PERSON>("X-TID")).
			Infof("restful request entry, client: %s, method: %s, url: %s, ext: %s",
				c.ClientIP(), c.Request.Method, c.Request.URL, c.Get<PERSON>eader("EXT"))

		// 兼容旧版通过头传递xuid/xorg
		rext := ext.New(c.Request.Header.Get("EXT"))

		sid := c.Request.Header.Get("SID")
		if len(sid) > 0 {
			rext.SetValue(ext.EXTSID, sid)
		}
		xtid := c.Request.Header.Get("X-TID")
		if len(xtid) > 0 {
			rext.SetValue(ext.EXTTID, xtid)
		}
		xuid := c.Request.Header.Get("X-UID")
		if len(xuid) > 0 {
			rext.SetValue(ext.EXTXUID, xuid)
		}
		xorg := c.Request.Header.Get("X-ORG")
		if len(xorg) > 0 {
			rext.SetValue(ext.EXTXORG, xorg)
		}
		xorgRiskLevel := c.Request.Header.Get("X-ORG-RISKLEVEL")
		if len(xtid) > 0 {
			rext.SetValue(ext.EXTXORGRiskLevel, xorgRiskLevel)
		}
		order := c.Request.Header.Get("ORDER")
		if len(xuid) > 0 {
			rext.SetValue(ext.EXTORDER, order)
		}
		platformPrinciple := c.Request.Header.Get("platformPrinciple")
		if len(xorg) > 0 {
			rext.SetValue(ext.EXTPrinciple, platformPrinciple)
		}

		c.Set("EXT", rext.ToString())

		c.Next()

		logrus.WithField(logger.KeyCategory, logger.CategoryInterface).
			WithField(logger.KeyTID, c.GetHeader("X-TID")).
			Infof("restful request final, client: %s, status: %d",
				strings.TrimSpace(c.Request.RemoteAddr), c.Writer.Status())
	}
}
