package otel

import (
	"context"
	"secwalk/internal/config"
	"secwalk/internal/domain/core/schema/ext"
	"secwalk/pkg/logger"
	pkgotel "secwalk/pkg/otel"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
	"go.opentelemetry.io/contrib/instrumentation/github.com/gin-gonic/gin/otelgin"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/baggage"
	"go.opentelemetry.io/otel/trace"
)

// OpenTelemetry中间件 - 集成EXT字段
func NewOtelMiddleware() gin.HandlerFunc {
	cfg := config.GetOtel()
	if !cfg.Enabled {
		// 如果OTEL未启用，返回空中间件
		return func(c *gin.Context) {
			c.Next()
		}
	}

	// 使用otelgin中间件作为基础
	baseMiddleware := otelgin.Middleware("secwalk")

	return func(c *gin.Context) {
		start := time.Now()

		// 处理EXT字段到Baggage的转换
		ctx := c.Request.Context()
		ctx = processEXTToBaggage(ctx, c)
		c.Request = c.Request.WithContext(ctx)

		// 调用基础OTEL中间件
		baseMiddleware(c)

		// 记录请求指标
		duration := time.Since(start)
		pkgotel.RecordHTTPRequest(
			c.Request.Context(),
			c.Request.Method,
			c.FullPath(),
			c.Writer.Status(),
			duration,
		)

		// 添加EXT字段到Span属性
		addEXTToSpan(c)

		logrus.WithField(logger.KeyCategory, logger.CategoryInterface).
			WithField("trace_id", getTraceID(c.Request.Context())).
			WithField("span_id", getSpanID(c.Request.Context())).
			Debugf("OTEL middleware processed request: %s %s", c.Request.Method, c.Request.URL.Path)
	}
}

// 将EXT字段转换为OpenTelemetry Baggage
func processEXTToBaggage(ctx context.Context, c *gin.Context) context.Context {
	cfg := config.GetOtel()
	if !cfg.Baggage.Enabled {
		return ctx
	}

	// 获取EXT字符串
	extStr := c.Request.Header.Get("EXT")
	if extStr == "" {
		return ctx
	}

	// 解析EXT字段
	rext := ext.New(extStr)

	// 创建Baggage成员
	var members []baggage.Member

	// 添加配置中指定的EXT字段到Baggage
	for _, field := range cfg.Baggage.ExtFields {
		value := rext.GetValue(field)
		if value != "" {
			member, err := baggage.NewMember(field, value)
			if err != nil {
				logrus.WithError(err).Warnf("Failed to create baggage member for %s", field)
				continue
			}
			members = append(members, member)
		}
	}

	// 添加常用的EXT字段
	commonFields := map[string]string{
		"tid":    rext.GetValue(ext.EXTTID),
		"sid":    rext.GetValue(ext.EXTSID),
		"x_uid":  rext.GetValue(ext.EXTXUID),
		"x_org":  rext.GetValue(ext.EXTXORG),
		"order":  rext.GetValue(ext.EXTORDER),
	}

	for key, value := range commonFields {
		if value != "" {
			member, err := baggage.NewMember(key, value)
			if err != nil {
				logrus.WithError(err).Warnf("Failed to create baggage member for %s", key)
				continue
			}
			members = append(members, member)
		}
	}

	// 创建新的Baggage
	if len(members) > 0 {
		bag, err := baggage.New(members...)
		if err != nil {
			logrus.WithError(err).Warn("Failed to create baggage")
			return ctx
		}
		ctx = baggage.ContextWithBaggage(ctx, bag)
	}

	return ctx
}

// 将EXT字段添加到当前Span的属性
func addEXTToSpan(c *gin.Context) {
	span := trace.SpanFromContext(c.Request.Context())
	if !span.IsRecording() {
		return
	}

	// 获取EXT字符串
	extStr := c.Request.Header.Get("EXT")
	if extStr == "" {
		return
	}

	// 解析EXT字段并添加为Span属性
	rext := ext.New(extStr)

	// 添加EXT字段作为Span属性
	span.SetAttributes(
		attribute.String("ext.raw", extStr),
		attribute.String("ext.tid", rext.GetValue(ext.EXTTID)),
		attribute.String("ext.sid", rext.GetValue(ext.EXTSID)),
		attribute.String("ext.x_uid", rext.GetValue(ext.EXTXUID)),
		attribute.String("ext.x_org", rext.GetValue(ext.EXTXORG)),
		attribute.String("ext.order", rext.GetValue(ext.EXTORDER)),
		attribute.String("ext.principle", rext.GetValue(ext.EXTPrinciple)),
	)

	// 添加HTTP相关属性
	span.SetAttributes(
		attribute.String("http.client_ip", c.ClientIP()),
		attribute.String("http.user_agent", c.Request.UserAgent()),
		attribute.String("http.remote_addr", c.Request.RemoteAddr),
	)
}

// 获取TraceID
func getTraceID(ctx context.Context) string {
	span := trace.SpanFromContext(ctx)
	if span.SpanContext().IsValid() {
		return span.SpanContext().TraceID().String()
	}
	return ""
}

// 获取SpanID
func getSpanID(ctx context.Context) string {
	span := trace.SpanFromContext(ctx)
	if span.SpanContext().IsValid() {
		return span.SpanContext().SpanID().String()
	}
	return ""
}

// 从Baggage中获取EXT字段值
func GetEXTFromBaggage(ctx context.Context, field string) string {
	bag := baggage.FromContext(ctx)
	member := bag.Member(field)
	return member.Value()
}

// 从Baggage中获取用户ID
func GetUserIDFromBaggage(ctx context.Context) string {
	return GetEXTFromBaggage(ctx, "x_uid")
}

// 从Baggage中获取组织ID
func GetOrgIDFromBaggage(ctx context.Context) string {
	return GetEXTFromBaggage(ctx, "x_org")
}

// 从Baggage中获取TraceID
func GetTraceIDFromBaggage(ctx context.Context) string {
	return GetEXTFromBaggage(ctx, "tid")
}

// 创建子Span并传递EXT信息
func StartSpanWithEXT(ctx context.Context, operationName string, opts ...trace.SpanStartOption) (context.Context, trace.Span) {
	tracer := otel.Tracer("secwalk")
	
	// 从Baggage中获取EXT信息并添加到Span属性
	bag := baggage.FromContext(ctx)
	var attrs []attribute.KeyValue
	
	for _, member := range bag.Members() {
		if strings.HasPrefix(member.Key(), "ext.") || 
		   member.Key() == "tid" || member.Key() == "sid" || 
		   member.Key() == "x_uid" || member.Key() == "x_org" || 
		   member.Key() == "order" {
			attrs = append(attrs, attribute.String(member.Key(), member.Value()))
		}
	}
	
	if len(attrs) > 0 {
		opts = append(opts, trace.WithAttributes(attrs...))
	}
	
	return tracer.Start(ctx, operationName, opts...)
}

// 记录LLM Token使用到Span
func RecordLLMTokenUsage(ctx context.Context, promptTokens, completionTokens, totalTokens int64, model string) {
	span := trace.SpanFromContext(ctx)
	if !span.IsRecording() {
		return
	}

	// 添加Token使用信息到Span
	span.SetAttributes(
		attribute.Int64("llm.prompt_tokens", promptTokens),
		attribute.Int64("llm.completion_tokens", completionTokens),
		attribute.Int64("llm.total_tokens", totalTokens),
		attribute.String("llm.model", model),
	)

	// 记录到指标系统
	userID := GetUserIDFromBaggage(ctx)
	orgID := GetOrgIDFromBaggage(ctx)
	pkgotel.RecordTokenUsage(ctx, promptTokens, completionTokens, totalTokens, userID, orgID, model)

	// 计算成本（示例价格，实际应该从配置获取）
	cost := calculateTokenCost(model, promptTokens, completionTokens)
	if cost > 0 {
		span.SetAttributes(attribute.Float64("llm.cost_usd", cost))
		pkgotel.RecordTokenCost(ctx, cost, userID, orgID, model)
	}
}

// 计算Token成本（示例实现）
func calculateTokenCost(model string, promptTokens, completionTokens int64) float64 {
	// 示例价格表（实际应该从配置文件读取）
	prices := map[string]struct {
		promptPrice     float64 // 每1K tokens的价格
		completionPrice float64
	}{
		"gpt-4": {0.03, 0.06},
		"gpt-3.5-turbo": {0.001, 0.002},
		"claude-3": {0.015, 0.075},
	}

	price, exists := prices[model]
	if !exists {
		return 0
	}

	promptCost := float64(promptTokens) / 1000.0 * price.promptPrice
	completionCost := float64(completionTokens) / 1000.0 * price.completionPrice
	
	return promptCost + completionCost
}
