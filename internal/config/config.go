package config

import (
	"secwalk/internal/config/consul"
	"secwalk/internal/domain/core/toolkit/python"
	"secwalk/internal/domain/repo"
	"secwalk/pkg/db/minio"
	"secwalk/pkg/logger"
	"secwalk/pkg/register"
	"secwalk/pkg/selector"
	"sync"

	"github.com/go-micro/plugins/v4/config/encoder/yaml"
	"github.com/sirupsen/logrus"
	"go-micro.dev/v4/config"
	"go-micro.dev/v4/config/reader"
	"go-micro.dev/v4/config/reader/json"
	"go-micro.dev/v4/config/source"
	"go-micro.dev/v4/config/source/file"
)

var (
	mux     sync.RWMutex
	cfg     config.Config
	configs Config
	version string = "dev"
)

type Config struct {
	Server   Server                     `yaml:"server" json:"server,omitempty"`
	Logger   logger.Config              `yaml:"logger" json:"logger,omitempty"`
	Register register.Config            `yaml:"register" json:"register,omitempty"`
	Services map[string]selector.Config `yaml:"services" json:"services,omitempty"`
	DB       repo.DB                    `yaml:"db" json:"db,omitempty"`
	Minio    minio.Config               `yaml:"minio" json:"minio,omitempty"`
	Feature  Feature                    `yaml:"feature" json:"feature,omitempty"`
	Otel     OtelConfig                 `yaml:"otel" json:"otel,omitempty"`
}

type Server struct {
	Name    string `yaml:"name" json:"name,omitempty"`
	Address string `yaml:"address" json:"address,omitempty"`
}

type Feature struct {
	UseXGuard bool `yaml:"use_xguard" json:"use_xguard,omitempty"`
	MaxResult int  `yaml:"max_result" json:"max_result,omitempty"`
}

func New(cfgfile, address, token string) error {
	var src source.Source
	var err error
	var encoder = yaml.NewEncoder()

	if len(address) > 0 {
		logrus.Infof("load config from consul: %s:%s", address, cfgfile)

		src = consul.NewSource(
			consul.WithAddress(address),
			consul.WithToken(token),
			consul.WithPrefix(cfgfile),
			consul.StripPrefix(true),
			source.WithEncoder(encoder),
		)
	} else {
		logrus.Infof("load config from file: %s", cfgfile)

		src = file.NewSource(
			file.WithPath(cfgfile),
			source.WithEncoder(encoder),
		)
	}

	cfg, err = config.NewConfig(
		config.WithReader(
			json.NewReader(
				reader.WithEncoder(encoder),
			),
		),
	)
	if err != nil {
		return err
	}

	if err := cfg.Load(src); err != nil {
		return err
	}

	if err := refresh(); err != nil {
		return err
	}

	return watch()
}

func watch() error {
	watcher, err := cfg.Watch()
	if err != nil {
		return err
	}

	go func() {
		for {
			v, err := watcher.Next()
			if err != nil {
				logrus.WithField(logger.KeyCategory, logger.CategorySystem).
					Errorf("watcher config error: %s", err.Error())
				return
			}

			if string(v.Bytes()) == "null" {
				continue
			}

			logrus.WithField(logger.KeyCategory, logger.CategorySystem).
				Infof("watcher config change: %s", string(v.Bytes()))

			if err := refresh(); err != nil {
				logrus.WithField(logger.KeyCategory, logger.CategorySystem).
					Errorf("refresh config error: %s", err.Error())
				return
			}
		}
	}()

	return nil
}

func refresh() error {
	mux.Lock()
	defer mux.Unlock()

	var new Config
	if err := cfg.Scan(&new); err != nil {
		return err
	}

	if err := logger.Init(&new.Logger); err != nil {
		logrus.WithField(logger.KeyCategory, logger.CategorySystem).
			Errorf("refresh logger error: %s", err.Error())
	}

	python.SetMaxResult(new.Feature.MaxResult)

	for i, v := range new.Services {
		if v.Config == nil {
			v.Config = &new.Register
		} else {
			if len(v.Type) == 0 {
				v.Type = new.Register.Type
			}
			if len(v.Host) == 0 {
				v.Host = new.Register.Host
			}
			if len(v.Token) == 0 {
				v.Token = new.Register.Token
			}
			if len(v.Username) == 0 {
				v.Username = new.Register.Username
			}
			if len(v.Password) == 0 {
				v.Password = new.Register.Password
			}
		}

		new.Services[i] = v
	}

	configs = new
	return nil
}

func GetVersion() string {
	return version
}

func GetServer() Server {
	mux.RLock()
	defer mux.RUnlock()

	return configs.Server
}

func GetRegister() register.Config {
	mux.RLock()
	defer mux.RUnlock()

	return configs.Register
}

func GetServices() map[string]selector.Config {
	mux.RLock()
	defer mux.RUnlock()

	return configs.Services
}

func GetDB() repo.DB {
	mux.RLock()
	defer mux.RUnlock()

	return configs.DB
}

func GetMinio() minio.Config {
	mux.RLock()
	defer mux.RUnlock()

	return configs.Minio
}

func GetFeature() Feature {
	mux.RLock()
	defer mux.RUnlock()

	return configs.Feature
}
