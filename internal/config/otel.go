package config

// OpenTelemetry配置
type OtelConfig struct {
	Enabled     bool              `yaml:"enabled" json:"enabled"`                         // 是否启用OTEL
	ServiceName string            `yaml:"service_name" json:"service_name"`               // 服务名称
	Endpoint    string            `yaml:"endpoint" json:"endpoint"`                       // ADOT Collector端点
	Headers     map[string]string `yaml:"headers" json:"headers,omitempty"`               // 自定义头部
	Insecure    bool              `yaml:"insecure" json:"insecure"`                       // 是否使用不安全连接
	Traces      TracesConfig      `yaml:"traces" json:"traces"`                           // 链路追踪配置
	Metrics     MetricsConfig     `yaml:"metrics" json:"metrics"`                         // 指标配置
	Logs        LogsConfig        `yaml:"logs" json:"logs"`                               // 日志配置
	Resource    ResourceConfig    `yaml:"resource" json:"resource"`                       // 资源配置
	Baggage     BaggageConfig     `yaml:"baggage" json:"baggage"`                         // Baggage配置
}

// 链路追踪配置
type TracesConfig struct {
	Enabled    bool    `yaml:"enabled" json:"enabled"`                       // 是否启用链路追踪
	SampleRate float64 `yaml:"sample_rate" json:"sample_rate"`               // 采样率 (0.0-1.0)
	MaxSpans   int     `yaml:"max_spans" json:"max_spans"`                   // 最大Span数量
}

// 指标配置
type MetricsConfig struct {
	Enabled  bool   `yaml:"enabled" json:"enabled"`                         // 是否启用指标
	Interval string `yaml:"interval" json:"interval"`                       // 指标收集间隔
	Endpoint string `yaml:"endpoint" json:"endpoint,omitempty"`             // 指标端点(可选)
}

// 日志配置
type LogsConfig struct {
	Enabled bool `yaml:"enabled" json:"enabled"`                           // 是否启用日志
}

// 资源配置
type ResourceConfig struct {
	Attributes map[string]string `yaml:"attributes" json:"attributes,omitempty"` // 资源属性
}

// Baggage配置 - 用于EXT字段传递
type BaggageConfig struct {
	Enabled    bool     `yaml:"enabled" json:"enabled"`                      // 是否启用Baggage
	MaxMembers int      `yaml:"max_members" json:"max_members"`              // 最大成员数
	ExtFields  []string `yaml:"ext_fields" json:"ext_fields"`                // 需要传递的EXT字段
}

// 获取OTEL配置
func GetOtel() OtelConfig {
	mux.RLock()
	defer mux.RUnlock()
	
	return configs.Otel
}
