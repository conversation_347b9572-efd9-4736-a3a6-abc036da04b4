# 编译阶段
FROM docker.das-security.cn/golang:1.23.6-bullseye AS builder

WORKDIR /workdir

# 使用公司源
ENV GO111MODULE=on
ENV GOPROXY=https://ci.das-security.cn/repository/ah_go

COPY . .

RUN go build -ldflags "-X 'secwalk/internal/config.version=v1.0.0' -s -w" \
        -trimpath -o bin/secwalk cmd/main.go

# 运行镜像
FROM docker.das-security.cn/debian:11.5-slim-runtime

# 设置工作目录
WORKDIR /app

# 创建切换用户
RUN useradd hn-user -u 6666
USER hn-user

# 拷贝服务文件
COPY --from=builder /workdir/bin/secwalk secwalk
COPY --from=builder /workdir/configs configs