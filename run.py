
from flask import Flask, send_file, jsonify, request
import os
import random
import string
import secrets
import tempfile
import shutil
from datetime import datetime

app = Flask(__name__)

# 配置临时文件存储目录
TEMP_DIR = os.path.join(tempfile.gettempdir(), 'random_files')
os.makedirs(TEMP_DIR, exist_ok=True)

# 清理函数 - 删除超过1小时的临时文件
def cleanup_old_files():
    now = datetime.now().timestamp()
    for filename in os.listdir(TEMP_DIR):
        filepath = os.path.join(TEMP_DIR, filename)
        if os.path.isfile(filepath):
            file_time = os.path.getctime(filepath)
            # 删除超过1小时的文件
            if now - file_time > 3600:
                try:
                    os.remove(filepath)
                except:
                    pass

# 生成随机文本内容
def generate_random_text(size_kb):
    chars = string.ascii_letters + string.digits + string.punctuation + ' ' * 10
    return ''.join(random.choice(chars) for _ in range(size_kb * 1024))

# 生成随机二进制内容
def generate_random_binary(size_kb):
    return os.urandom(size_kb * 1024)

# 生成随机Excel (XLS) 文件
def generate_random_xls(size_kb):
    # 这里只是生成一个假的XLS文件头部，后面跟随随机数据
    # 真实XLS文件头部 (OLE2 格式)
    xls_header = bytes.fromhex('D0CF11E0A1B11AE1')
    # 填充剩余部分为随机数据
    remaining_bytes = size_kb * 1024 - len(xls_header)
    return xls_header + os.urandom(remaining_bytes)

# 生成随机Excel (XLSX) 文件
def generate_random_xlsx(size_kb):
    # 这里只是生成一个假的XLSX文件头部，实际上XLSX是ZIP格式
    # 简单模拟ZIP文件头部
    xlsx_header = bytes.fromhex('504B0304')
    # 填充剩余部分为随机数据
    remaining_bytes = size_kb * 1024 - len(xlsx_header)
    return xlsx_header + os.urandom(remaining_bytes)

@app.route('/api/download', methods=['GET'])
def download_random_file():
    """
    生成并下载随机文件
    
    参数:
    - type: 文件类型 (txt, bin, xls, xlsx)，默认为txt
    - size: 文件大小 (KB)，默认为10，最大1024
    - name: 自定义文件名，默认随机生成
    
    返回:
    - 文件下载
    """
    cleanup_old_files()  # 清理旧文件
    
    # 获取参数
    file_type = request.args.get('type', 'txt').lower()
    try:
        size_kb = min(int(request.args.get('size', '10')), 1024)  # 限制最大1MB
    except ValueError:
        size_kb = 10
    
    custom_name = request.args.get('name', '')
    
    # 生成随机文件名
    if not custom_name:
        random_name = ''.join(random.choice(string.ascii_lowercase) for _ in range(8))
    else:
        # 清理自定义文件名，防止路径遍历
        random_name = os.path.basename(custom_name)
        random_name = random_name.split('.')[0]  # 移除扩展名
    
    # 根据类型生成文件内容和设置扩展名
    if file_type == 'txt':
        content = generate_random_text(size_kb).encode('utf-8')
        ext = '.txt'
        mimetype = 'text/plain'
    elif file_type == 'bin':
        content = generate_random_binary(size_kb)
        ext = '.bin'
        mimetype = 'application/octet-stream'
    elif file_type == 'xls':
        content = generate_random_xls(size_kb)
        ext = '.xls'
        mimetype = 'application/vnd.ms-excel'
    elif file_type == 'xlsx':
        content = generate_random_xlsx(size_kb)
        ext = '.xlsx'
        mimetype = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    else:
        return jsonify({"error": "Unsupported file type"}), 400
    
    # 创建临时文件
    filename = random_name + ext
    filepath = os.path.join(TEMP_DIR, filename)
    
    with open(filepath, 'wb') as f:
        f.write(content)
    
    # 返回文件
    return send_file(
        filepath,
        mimetype=mimetype,
        as_attachment=True,
        download_name=filename
    )

@app.route('/api/types', methods=['GET'])
def get_file_types():
    """获取支持的文件类型列表"""
    return jsonify({
        "types": ["txt", "bin", "xls", "xlsx"],
        "max_size": 1024
    })

if __name__ == '__main__':
    # 启动前清理旧文件
    cleanup_old_files()
    # 在生产环境中，应该使用 gunicorn 或 uwsgi 来运行
    app.run(host='0.0.0.0', port=5000, debug=True)
