# 编译阶段
FROM docker.das-security.cn/golang:1.22.5-bullseye AS builder

WORKDIR /workdir

# 使用公司源
ENV GO111MODULE=on
ENV GOPROXY=https://goproxy.cn,direct

COPY . .

RUN go build -ldflags "-X 'secwalk/internal/config.version=v1.0.0' -s -w" \
        -trimpath -o bin/secwalk cmd/main.go

# 运行镜像
FROM docker.das-security.cn/debian:11.5-slim

# 设置工作目录
WORKDIR /app

RUN sed -i "s/deb.debian.org/mirrors.tuna.tsinghua.edu.cn/g" /etc/apt/sources.list && \
    apt-get update && \
    apt-get install -y --no-install-recommends ca-certificates procps net-tools tcpdump

# 创建切换用户
RUN useradd hn-user -u 6666
USER hn-user

# 拷贝服务文件
COPY --from=builder /workdir/bin/secwalk secwalk
COPY --from=builder /workdir/configs configs