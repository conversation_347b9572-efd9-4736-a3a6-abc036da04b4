# Word文档页眉页尾解析功能

## 功能概述

在处理Word文档时，现在可以在properties属性里获取页眉页尾的信息，包括文本内容和图片信息。

## 实现的功能

### 1. 页眉信息提取
- 提取所有页眉的文本内容
- 识别页眉中的图片（内联图片和锚定图片）
- 提供图片的关系ID和目标路径

### 2. 页脚信息提取
- 提取所有页脚的文本内容
- 识别页脚中的图片（内联图片和锚定图片）
- 提供图片的关系ID和目标路径

### 3. 数据结构

#### 页眉信息格式
```json
{
  "headers": [
    {
      "index": 0,
      "text": "页眉文本内容",
      "paragraphCount": 1,
      "imageCount": 2,
      "images": [
        {
          "type": "inline",
          "index": 0,
          "relId": "rId1",
          "target": "media/image1.png"
        },
        {
          "type": "anchored",
          "index": 1,
          "relId": "rId2",
          "target": "media/image2.jpg"
        }
      ]
    }
  ]
}
```

#### 页脚信息格式
```json
{
  "footers": [
    {
      "index": 0,
      "text": "页脚文本内容",
      "paragraphCount": 1,
      "imageCount": 1,
      "images": [
        {
          "type": "inline",
          "index": 0,
          "relId": "rId3",
          "target": "media/image3.png"
        }
      ]
    }
  ]
}
```

## 修改的文件

### 1. `internal/domain/core/toolkit/secdocx/parser/word.go`

#### 修改的函数：
- `parseWordProperties()`: 添加了页眉页尾信息提取调用

#### 新增的函数：
- `extractHeaderFooterInfo()`: 提取页眉信息，包括文本和图片
- `extractFooterInfo()`: 提取页脚信息，包括文本和图片

## 技术实现细节

### 1. 使用unioffice库的方法
- `doc.Headers()`: 获取所有页眉
- `doc.Footers()`: 获取所有页脚
- `header.Paragraphs()`: 获取页眉中的段落
- `footer.Paragraphs()`: 获取页脚中的段落
- `run.DrawingAnchored()`: 获取锚定图片
- `run.DrawingInline()`: 获取内联图片

### 2. 图片信息提取
- 支持两种图片类型：内联图片（inline）和锚定图片（anchored）
- 提取图片的关系ID（RelID）和目标路径（Target）
- 为每个图片分配索引号

### 3. 数据过滤
- 只有包含文本内容或图片的页眉页尾才会被添加到结果中
- 空白的页眉页尾会被过滤掉

## 使用方式

在解析Word文档后，可以通过以下方式访问页眉页尾信息：

```go
// 解析Word文档
doc, err := parseWord(ctx, file)
if err != nil {
    return nil, err
}

// 获取页眉信息
if headers, ok := doc.Properties["headers"]; ok {
    headerList := headers.([]map[string]any)
    for _, header := range headerList {
        fmt.Printf("页眉 %d: %s\n", header["index"], header["text"])
        if images, hasImages := header["images"]; hasImages {
            imageList := images.([]map[string]any)
            for _, img := range imageList {
                fmt.Printf("  图片: %s (%s)\n", img["target"], img["type"])
            }
        }
    }
}

// 获取页脚信息
if footers, ok := doc.Properties["footers"]; ok {
    footerList := footers.([]map[string]any)
    for _, footer := range footerList {
        fmt.Printf("页脚 %d: %s\n", footer["index"], footer["text"])
        if images, hasImages := footer["images"]; hasImages {
            imageList := images.([]map[string]any)
            for _, img := range imageList {
                fmt.Printf("  图片: %s (%s)\n", img["target"], img["type"])
            }
        }
    }
}
```

## 注意事项

1. 页眉页尾信息只有在存在内容（文本或图片）时才会被添加到properties中
2. 图片信息包含关系ID和目标路径，可以用于进一步的图片处理
3. 支持多个页眉和页脚（如奇偶页不同的情况）
4. 文本内容会自动去除前后空白字符
5. 段落之间的文本会用换行符连接

## 兼容性

- 与现有的Word文档解析功能完全兼容
- 不影响其他文档属性的提取
- 向后兼容，不会破坏现有的代码
