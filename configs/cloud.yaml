# 服务基本配置
server:
  name: secwalk
  address: ":"

# 日志配置
logger:
  app: secwalk
  level: info
  format: true
  output: logs/secwalk.log
  max_size: 20
  max_age: 30
  max_backup: 10

# 服务注册配置
register:
  type: consul
  host: consul-consul-server.consul:8500
  token:
  username:
  password:

# 服务发现配置
services:
  backend:
    type: direct
    name: backend
    host: server-competence-center.platform-server:9994
    metas:
      authorization: 9KsxdRjPXY95CP4WHSytwXW7QYhBrtEV
  openapi:
    type: direct
    name: openapi
    host: server-openapi:9996
    metas:
      authorization: 9KsxdRjPXY95CP4WHSytwXW7QYhBrtEV
  gateway:
    type: consul
    name: secwall
    metas:
      authorization: "7178648308129206272"
  coder:
    type: consul
    name: secvirt
  hbrag:
    type: consul
    name: hyper-engine
    metas:
      authorization: 5M1048xJby2q2s6H4Sn9Hv5guYu2tUW0

# 存储配置
db:
  address: mycluster.mysql-operator:3306
  username: root
  password: supersecret@ah2023

minio:
  host: minio-cluster-hl-svc.minio-cluster:9000
  access_key: wiWpxZggabnJlIrH
  secret_key: Sz1Ss6qcSstQt0AMO2T7dv5rVzNU7ecK

# 内部功能开关
feature:
  use_xguard: true
  max_reslut: 0 # 0表示不限制, 单位MB

# 热更新配置项
hot_swaps:
  - path: logger.level
    desc: 日志等级
    type: string
    validate: oneof=INFO|WARN|ERROR

  - path: feature.use_xguard
    desc: 是否开启风控检测
    type: bool
