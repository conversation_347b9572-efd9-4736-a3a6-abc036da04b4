# 服务基本配置
server:
  name: secwalk
  address: :8998

# 日志配置
logger:
  app: secwalk
  level: info
  format: false
  output: logs/secwalk.log
  max_size: 20
  max_age: 30
  max_backup: 10

# 服务注册配置
register:
  type: consul
  host: hn-consul:8500
  token: d9295a36-9f55-4580-b22d-71cfa5837f76
  username:
  password:

# 服务发现配置
services:
  backend:
    type: direct
    name: backend
    host: hn-plat-core:9994
    metas:
      authorization: 9KsxdRjPXY95CP4WHSytwXW7QYhBrtEV
  openapi:
    type: direct
    name: openapi
    host: hn-openapi:9996
    metas:
      authorization: 9KsxdRjPXY95CP4WHSytwXW7QYhBrtEV
  gateway:
    type: consul
    name: secwall
    metas:
      authorization: "7178648308129206272"
  coder:
    type: consul
    name: secvirt
  hbrag:
    type: consul
    name: hyper-engine
    metas:
      authorization: 5M1048xJby2q2s6H4Sn9Hv5guYu2tUW0

# 存储配置
db:
  address: hn-mysql:3306
  username: root
  password: J7aXgk2BJUj=

minio:
  host: hn-minio:9000
  access_key: admin
  secret_key: tYqV9s#bt3ADrMy

# 内部功能开关
feature:
  use_xguard: true
  max_reslut: 0 # 0表示不限制, 单位MB

# 热更新配置项
hot_swaps:
  - path: logger.level
    desc: 日志等级
    type: string
    validate: oneof=INFO|WARN|ERROR

  - path: feature.use_xguard
    desc: 是否开启风控检测
    type: bool
