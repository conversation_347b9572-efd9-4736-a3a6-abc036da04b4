#!/bin/bash

# 可配置参数
TOTAL_REQUESTS=10      # 总请求数
CONCURRENT_REQUESTS=20 # 并发数
LOG_FILE="curl_results.log"
ERROR_LOG="curl_errors.log"

# 清空日志文件
> "$LOG_FILE"
> "$ERROR_LOG"

# 计数器
completed=0
errors=0

# 生成随机SID
generate_sid() {
    echo "sid_$(date +%s%N | md5sum | head -c 32)"
}

# 执行单个请求的函数
execute_request() {
    local request_id=$1
    local sid=$(generate_sid)

    echo "[$request_id] 开始请求，SID: $sid" >> "$LOG_FILE"

    # 执行curl请求并保存响应
    # 注意：这里使用了-N参数来处理流式响应
    response=$(curl -s -N -X POST "http://************:8998/secwalk/v1/agent/execute" \
        -H "Content-Type: application/json" \
        -H "SID: $sid" \
        -d '{
    "aid": "9bc70fb4-8e37-4265-8f3d-188ecd01882d",
    "stream":true,
    "inputs": {
        "table": "NQBasAddressCity",
        "table_description": "州/市字典表",
        "field": "AreaCode",
        "field_description": "区号",
        "field_example": "",
        "all_fields": {
            "field": [],
            "field_description": [],
            "field_example": []
        },
        "frame": {
            "industry": "医疗",
            "classify": [
                {
                    "curr": "个人属性信息",
                    "next": [
                        {
                            "curr": "个人基本资料",
                            "next": [],
                            "fields": "Author、生日、性别、姓名、出生地区、县、出生地省、国家名称、婚姻状况、民族、年龄、联系地址、联系电话、联系人、民族、姓名拼音、手机号码、邮政编码、血型、phone",
                            "explain": "",
                            "level": "3"
                        },
                        {
                            "curr": "个人身份信息",
                            "next": [],
                            "fields": "健康卡号、证件号码、证件类别、身份证、军官证、护照、工作证、出入证、社保卡、居住证、住院号",
                            "explain": "",
                            "level": "4"
                        },
                        {
                            "curr": "个人生物识别信息",
                            "next": [],
                            "fields": "个人基因、指纹、声纹、掌纹、耳廓、虹膜、面部识别特征",
                            "explain": "",
                            "level": "4"
                        },
                        {
                            "curr": "网络身份标识信息",
                            "next": [],
                            "fields": "个人信息主体账号、IP地址、用户账号、用户标识符、用户ID、即时通信账号、网络社交用户账号、用户头像、昵称、个性签名",
                            "explain": "",
                            "level": "2"
                        },
                        {
                            "curr": "个人教育工作信息",
                            "next": [],
                            "fields": "工作单位电话、工作单位及地址、工作单位邮政编码、职业",
                            "explain": "",
                            "level": "3"
                        },
                        {
                            "curr": "个人财产信息",
                            "next": [],
                            "fields": "银行账户、鉴别信息口令、存款信息包括资金数量、支付收款记录、房产信息、信贷记录、征信信息、以及虚拟货币、虚拟交易、游戏类兑换码虚拟财产信息",
                            "explain": "",
                            "level": "4"
                        },
                        {
                            "curr": "个人通信信息",
                            "next": [],
                            "fields": "通信记录和内容、短信内容、彩信内容、电子邮件内容",
                            "explain": "",
                            "level": "4"
                        },
                        {
                            "curr": "个人上网记录",
                            "next": [],
                            "fields": "通讯录、好友列表、群列表、电子邮件地址列表指通过日志储存的个人信息主体操作记录包括网站浏览记录、软件使用记录、点击记录、收藏列表、上网记录、上网习惯、上网登录方式",
                            "explain": "",
                            "level": "4"
                        },
                        {
                            "curr": "个人常用设备信息",
                            "next": [],
                            "fields": "指包括硬件序列号、设备 MAC 地址、软件列表、唯一设备识别码如IMEIAndroid IDIDFAOpenUDIDGUIDSIM 卡 IMSI 信息在内的描述个人常用设备基本情况的信息",
                            "explain": "",
                            "level": "3"
                        },
                        {
                            "curr": "个人位置信息",
                            "next": [],
                            "fields": "包括行踪轨迹、精准定位信息、住宿信息、经纬度",
                            "explain": "",
                            "level": "4"
                        },
                        {
                            "curr": "个人标签信息",
                            "next": [],
                            "fields": "VIP、普通病人、行为习惯、兴趣偏好",
                            "explain": "",
                            "level": "2"
                        },
                        {
                            "curr": "身份鉴别信息",
                            "next": [],
                            "fields": "登录信息、卡信息、账号口令、短信验证码、密码、数字证书、token、令牌",
                            "explain": "",
                            "level": "4"
                        },
                        {
                            "curr": "个人隐私信息",
                            "next": [],
                            "fields": "婚史、宗教信仰、性取向、未公开的违法犯罪记录",
                            "explain": "",
                            "level": "4"
                        }
                    ],
                    "fields": "",
                    "explain": "",
                    "level": ""
                },
                {
                    "curr": "个人健康状况",
                    "next": [
                        {
                            "curr": "健康史信息",
                            "next": [],
                            "fields": "家族近亲婚配者与本人关系、妊娠终止方式、分娩方式、出生缺陷儿结局、既往常见疾病种类、既往病史、家族病史\n避孕史描述、产次、出生缺陷儿标志、胎儿分娩结局代码、出生缺陷儿例数、出生孕周d、初潮年龄岁、传染病史、分娩标志、阴道分娩次数、分娩方式代码、分娩活产个数、分娩孕周d、分娩总出血量mL、腹水日期、腹水标志、肝昏迷日期、肝昏迷标志、肝炎标志、患病种类代码、过敏史描述、过敏史标志、患者与本人关系代码、畸形描述、疾病史含外伤、观察方法名称、观察结果、观察项目代码值、编码体系名称、观察项目名称、疾病种类代码、精神类疾病诊断名称、救治标志、门诊治疗情况代码、治疗效果代码、家庭成员患乙肝患者标志、家族疾病史代码、家族近亲婚配、产妇结局代码、个人史、婚育史、既往史、输血史、预防接种史、月经史、家族史",
                            "explain": "",
                            "level": "3"
                        },
                        {
                            "curr": "健康危险因素",
                            "next": [
                                {
                                    "curr": "行为危险因素",
                                    "next": [],
                                    "fields": "吸烟状态、被动吸烟场所、吸食烟草种类、饮酒频率、饮酒种类、饮食种类、饮食习惯、饮食频率分类、每天食用的食物食用频率、每年食用的食物食用频率、身体活动频率、每日饮水量、饮水类别、个人不良行为接触史、患重性精神疾病对家庭社会的影响、艾滋病接触史",
                                    "explain": "",
                                    "level": "3"
                                },
                                {
                                    "curr": "职业危险因素",
                                    "next": [],
                                    "fields": "职业照射种类、职业病危害因素类别、农药名称",
                                    "explain": "",
                                    "level": "2"
                                },
                                {
                                    "curr": "环境及其它危险因素",
                                    "next": [],
                                    "fields": "环境危险因素暴露类、厨房排风设施类别、燃料类型类别、厕所类别、传播途径、孕早期服药类别、孕产期高危因素、接触有害因素类别",
                                    "explain": "",
                                    "level": "2"
                                }
                            ],
                            "fields": "",
                            "explain": "",
                            "level": ""
                        },
                        {
                            "curr": "主诉与症状",
                            "next": [],
                            "fields": "主诉、症状、妇科及乳腺不适症状、乳汁量、伤害发生原因、伤害意图类别、伤害发生地点、伤害发生时活动、疑似结核患者症状、慢性丝虫病患者症状、精神症状、儿童睡眠质量、儿童睡眠情况、儿童大便行状、老年人健康状态自我评估、老年人生活自理能力自我评估、肺结核患者症状及体征",
                            "explain": "",
                            "level": "3"
                        },
                        {
                            "curr": "体格检查",
                            "next": [],
                            "fields": "附件检查结果、子宫大小、妇科检查方式、皮肤检查结果、浮肿状况、巩膜检查、口唇外观、儿童面色、黄疸部位、齿列类别、淋巴结检查结果、乳腺检查结果、肛门指诊检查结果、下肢水肿检查结果、足背动脉搏动、伤害部位、慢性丝虫病症状发作部位、前囟张力、脐带检查结果、儿童体格发育评价、可疑佝偻病症状、可疑佝偻病体征、外阴及阴道检查结果、宫颈检查结果、子宫检查结果、妇科检查临床诊断异常结果",
                            "explain": "",
                            "level": "3"
                        }
                    ],
                    "fields": "",
                    "explain": "",
                    "level": ""
                },
                {
                    "curr": "医疗应用数据",
                    "next": [
                        {
                            "curr": "门（急）诊病历",
                            "next": [],
                            "fields": "病历编号、门诊号、急诊使用、就诊序号、诊断类别、就诊时间、诊断类型、随诊期、诊断结果、就诊类别、门诊判别、分诊台、门诊类别、诊断号、确诊天数、门诊序号、就诊次数、初诊日期、就诊编号",
                            "explain": "",
                            "level": "3"
                        },
                        {
                            "curr": "诊断信息",
                            "next": [],
                            "fields": "诊断名称、诊断、诊断序号、疾病诊断、诊断依据、病人诊断、患者诊断、诊断编号、主诊断、诊断备注、病理诊断、诊断级别、诊断描述、诊断部位、诊断序列号、主诊断名称、超声诊断、诊断处置、诊疗名称、诊断内容、初步诊断、诊断ZD、住院者疾病状态、诊断状态、乳糜尿发作诱因、伤害性质、中医体质分类、孕产妇死亡死因分类、胎方位、胎先露、产时并发症",
                            "explain": "",
                            "level": "3"
                        },
                        {
                            "curr": "门（急）诊处方",
                            "next": [],
                            "fields": "处方条目、用药途径、单次用药剂量、用药频率、药物剂型、处方编号、处方开立日期、处方有效日期、药物名称、药物规格、药物剂型代码、药物使用次剂量、药物使用剂量单位、药物使用频次代码、用药途径代码、药物使用总剂量、处方药品组号、处方开立医师签名、处方审核药剂师签名、处方调配药剂师签名、处方核对药剂师签名、处方发药药剂师签名、处方备注信息",
                            "explain": "",
                            "level": "3"
                        },
                        {
                            "curr": "检查检验记录",
                            "next": [
                                {
                                    "curr": "检查检验基础信息",
                                    "next": [],
                                    "fields": "检查检验报告编号、检查检验类型、检查检验设备类型、检查检验时间、检查检验科室、检查检验状态、检查检验优先级、检查检验备注",
                                    "explain": "",
                                    "level": "2"
                                },
                                {
                                    "curr": "检查检验结果信息",
                                    "next": [],
                                    "fields": "检查检验结果值、检查检验结果解释、检查检验诊断信息、检查检验图像数据如果适用、检查检验建议或后续步骤、检查检验详细描述",
                                    "explain": "",
                                    "level": "3"
                                }
                            ],
                            "fields": "",
                            "explain": "",
                            "level": ""
                        },
                        {
                            "curr": "一般治疗处置记录",
                            "next": [
                                {
                                    "curr": "治疗",
                                    "next": [],
                                    "fields": "常用治疗项目ID、治疗项目ID、代开治疗项目ID、治疗首例标志、操作原因、放化疗情况、产妇生存情况、随诊期限年、随诊标志、随诊期限天、随诊期限、随诊期限月、治疗计划、治疗方式名称、随诊期限周、治疗单ID、治疗终止日期、随访情况、治愈情况、适用应用名称、麻醉师随访信息、治愈好转、死亡其他、未愈、治疗首例、治疗名称、治疗起始日期",
                                    "explain": "",
                                    "level": "2"
                                },
                                {
                                    "curr": "手术记录",
                                    "next": [],
                                    "fields": "手术名称、手术ID、手术间、手术等级、手术级别、手术日期、手术代码、手术专业、手术类别、术前诊断、手术首例、手术、手术组号、手术序号、切口等级、拟手术名称、主手术ID、愈合情况、术后诊断、手术天数、手术室类型、手术前诊断、手术间ID、手术后诊断、手术编号",
                                    "explain": "",
                                    "level": "2"
                                },
                                {
                                    "curr": "麻醉记录",
                                    "next": [],
                                    "fields": "手麻、麻醉",
                                    "explain": "",
                                    "level": "2"
                                },
                                {
                                    "curr": "输血记录",
                                    "next": [],
                                    "fields": "输血开始、输血结束、输血记录、不良反应记录、输血前、输血评价、输血反应类型代码、输血性质、输血血液成分、输血原因、输血指征、血袋编码、输血量",
                                    "explain": "",
                                    "level": "2"
                                }
                            ],
                            "fields": "",
                            "explain": "",
                            "level": ""
                        },
                        {
                            "curr": "护理记录",
                            "next": [
                                {
                                    "curr": "护理操作记录",
                                    "next": [],
                                    "fields": "护理记录编号、护理操作类型给药、换药、监测、护理执行时间、护理人员标识、护理设备、材料使用记录、护理计划、流程状态、护理单元、nursinglevel、护理等级、nursenumber、护理人员数、nursingday、护理天数、docno、护理文书主表流水号、executeno、护理执行序号",
                                    "explain": "",
                                    "level": "2"
                                },
                                {
                                    "curr": "护理评估与计划",
                                    "next": [],
                                    "fields": "生理指标如体温、血压、心率等、症状描述如疼痛、不适、皮肤状况等、护理干预效果评估、特殊护理需求或建议、患者自我报告的健康状况、护理过程中的特殊情况记录、护理评估详细描述",
                                    "explain": "",
                                    "level": "3"
                                }
                            ],
                            "fields": "",
                            "explain": "",
                            "level": ""
                        },
                        {
                            "curr": "助产记录",
                            "next": [],
                            "fields": "Apgar评分时长时间代码、Apgar评分值、膀胱充盈标志、膀胱气标志、产程经过、产次、产妇会阴缝合针数、产妇会阴切开标志、产后膀胱充盈标志、产后出血量值、产后宫底高度值、产后宫缩、产瘤部位、产瘤大小、产前检查标志、产前检查异常情况、处置计划、此次妊辰特殊情况、存脐带血情况标志、骶耻外径、第1产程时长、第2产程时长、第3产程时长、恶露状况、分娩过程特殊情况描述、分娩结局代码、腹腔探查附件、宫颈情况、宫口情况、宫腔探查处理情况、宫腔探查异常情况描述、宫缩剂名称、宫缩剂使用方法、宫缩情况、会阴裂伤情况代码、会阴切开位置、会阴血肿标志、计划选取的分娩方式",
                            "explain": "",
                            "level": "3"
                        },
                        {
                            "curr": "挂号预约",
                            "next": [],
                            "fields": "预检号、预约时间、预约日期、挂号ID、预约ID、上午限号、下午限号、预约号、挂号序号、晚上限号、挂号单元ID、上午预约限号、下午预约限号、晚上预约限号、预约序号",
                            "explain": "",
                            "level": "2"
                        },
                        {
                            "curr": "体检信息",
                            "next": [],
                            "fields": "体检、体检单、体检结果、身体检查",
                            "explain": "",
                            "level": "3"
                        },
                        {
                            "curr": "知情告知信息",
                            "next": [],
                            "fields": "知情告知、知情同意书编号、手术中可能出现的意外及风险、手术后可能出现的意外及并发症、替代方案、基础疾病对麻醉可能产生的影响、麻醉中麻醉后可能发生的意外及并发症、特殊检查及特殊治疗项目名称、特殊检查及特殊治疗可能引起的并发症及风险、病危通知内容",
                            "explain": "",
                            "level": "3"
                        },
                        {
                            "curr": "住院病案首页",
                            "next": [],
                            "fields": "病案首页",
                            "explain": "",
                            "level": "3"
                        },
                        {
                            "curr": "入院记录",
                            "next": [],
                            "fields": "入院登记、入院记录、入院评估、入院、入院类型、入院审批",
                            "explain": "",
                            "level": "3"
                        },
                        {
                            "curr": "病程记录",
                            "next": [],
                            "fields": "病程、病程记录、查房记录、交接班记录、病例讨论、转科记录、阶段小结、抢救记录、有创诊疗、会诊记录、术前小结、访视记录、出院记录、死亡记录、转科次数、儿童死亡、出院情况、出院日期、出院诊断、门药使用、抢救次数、住院类别",
                            "explain": "",
                            "level": "3"
                        },
                        {
                            "curr": "医嘱",
                            "next": [],
                            "fields": "医嘱、医嘱分类、医嘱号、医嘱名称、医嘱内容、医嘱状态",
                            "explain": "",
                            "level": "3"
                        },
                        {
                            "curr": "出院小结",
                            "next": [],
                            "fields": "出院病房、出院科别、出院其他诊断编码1、出院小结、出院、离院",
                            "explain": "",
                            "level": "3"
                        },
                        {
                            "curr": "转诊（院）记录",
                            "next": [],
                            "fields": "转科科别、转入社区服务机构乡镇卫生院名称、receivehospitalcommunityname、转诊日期、转诊ID、转诊原因、转院原因",
                            "explain": "",
                            "level": "3"
                        },
                        {
                            "curr": "基础数据",
                            "next": [
                                {
                                    "curr": "其它客体信息",
                                    "next": [],
                                    "fields": "Element_Code、Link_Class、LogKey、MessageDate、ReadDate、ECCode、PropertyName、Description、FieldName、PDesc、PCode、childsub、DimMonth、AvailAction、CASingTypeCode、CAVenderCode、Certificate、SignTimeStamp、数据元素、键信息、日志信息、配置信息、参数代码、签名验证方式、登录类型、签到方式、打印任务、页码、更新期限、更新日期、日期、枚举类别、数据代码、类别代码、规则详情、规则模板、备注、修改时间、创建时间、记录编号、经办时间、作废标志、备用、统筹区编码、有效标志、顺序号、序号、操作时间、数量、审核时间、无效标志、时间、排序号、业务类型、数据来源、更新时间、批次号、流水号",
                                    "explain": "",
                                    "level": "2"
                                },
                                {
                                    "curr": "基础信息",
                                    "next": [],
                                    "fields": "字典、模板、病种ID、病种编码、病种名称、疾病分类、疾病名称、代码标志类信息",
                                    "explain": "",
                                    "level": "2"
                                }
                            ],
                            "fields": "",
                            "explain": "",
                            "level": ""
                        }
                    ],
                    "fields": "",
                    "explain": "",
                    "level": ""
                },
                {
                    "curr": "医疗支付数据",
                    "next": [
                        {
                            "curr": "医疗交易信息",
                            "next": [],
                            "fields": "一般医疗服务费、临床诊断项目费、非手术治疗项目费、住院总费用、住院总费用其中自付金额、totalselfmoney、不能参与医保支付部fen金额、totalybpaidmoney、参与医保计算部fen医保支付金额、totalybmoney、参与医保支付金额、outstoreamount、出库金额、recipefee、处方金额、changeamount、调整金额、prtacount、发票总金额、totalselfpaidmoney、个人实际支付金额、记帐金额、amout、金额、totalowemoney、欠款结算时欠款部fen金额总金额、instoreamount、入库金额、ybmoney、医保交易金额、receivableamount、应收金额、payamt、支付方式对应于的金额、总金额、自费病人只有总金额、发票",
                            "explain": "",
                            "level": "3"
                        },
                        {
                            "curr": "保险信息",
                            "next": [],
                            "fields": "保险类别、参保代码、参保地、参保率、参保年月、参保人编号、参保人数、参保时间、参保险种、居民医保、可参保年度、生育保险、险种、险种编码、险种类型、险种名称、医保编码、医保代码、医保等级、医保金额、医保类别",
                            "explain": "",
                            "level": "4"
                        }
                    ],
                    "fields": "",
                    "explain": "",
                    "level": ""
                },
                {
                    "curr": "卫生资源数据",
                    "next": [
                        {
                            "curr": "医院基本信息",
                            "next": [],
                            "fields": "病房数、wardquantity、parenthospitalno、父级医院代码、parenthospitalname、父级医院名称、sendlabhospital、送检医院、hospitalno、医院编码、hospitaltypecode、医院分类代码、hospitaltypename、医院分类名称、hospitaltshortname、医院简称、hospitalname、医院名称、receivehospitalname、转入医院名称、医疗机构名称、医疗机构类别、医院学科门类、床位数、医院地址、医院电话",
                            "explain": "",
                            "level": "1"
                        },
                        {
                            "curr": "医院运营信息",
                            "next": [
                                {
                                    "curr": "医院人力资源信息",
                                    "next": [
                                        {
                                            "curr": "组织机构信息",
                                            "next": [],
                                            "fields": "HospTel、组织机构代码、报告科室编码、报告科室代码、报告科室名称、reporteddeptcode、reporteddeptname、病区id、病区代码、病区类型、病区名称、wardid、wardcode、wardname、病人科室大类、采购科室代码、采购科室名称、deptcode、deptname、acceptdeptname、接诊科室温二医院现场指标需要获取his最终接诊情况、acceptdeptcode、接诊科室代码温二医院现场指标需要获取his最终接诊情况、deptlevel、科室级别一级科室、二级科室、三级科室 医院内科室级别、重症监护室名称、岗位数量、科室归属、资质机构层面、组织机构类型、创建时间",
                                            "explain": "",
                                            "level": "1"
                                        },
                                        {
                                            "curr": "岗位信息",
                                            "next": [],
                                            "fields": "科主任编码、recipedoctorno、处方医师代码、主治医师编码、checkdoctcode、审核医生代码、surgeondoctorcode、手术医生代码、acceptedphysiciancode、接诊医生代码温二医院现场指标需要获取his最终接诊情况、operatorcode、检查医生代码、医生代码",
                                            "explain": "",
                                            "level": "1"
                                        },
                                        {
                                            "curr": "员工基本信息",
                                            "next": [],
                                            "fields": "员工ID、医生姓名、医生代码、医师、工号、助手、行政政务、技术职务、教学职称、导师类型、工作类别、学历",
                                            "explain": "",
                                            "level": "2"
                                        },
                                        {
                                            "curr": "员工敏感信息",
                                            "next": [],
                                            "fields": "员工身份证、出生日期、家庭住址、家庭成员",
                                            "explain": "",
                                            "level": "4"
                                        },
                                        {
                                            "curr": "入转调离信息",
                                            "next": [],
                                            "fields": "调转类别、调转文件号、调转日期、新入来源、调出去向、Per_LeaveMode、",
                                            "explain": "",
                                            "level": "3"
                                        },
                                        {
                                            "curr": "人员资质信息",
                                            "next": [],
                                            "fields": "职业医生资质、职业药师纸质、职业护士资质、教学资质、手术资质、操作资质、处方资质",
                                            "explain": "",
                                            "level": "2"
                                        },
                                        {
                                            "curr": "薪酬福利信息",
                                            "next": [],
                                            "fields": "薪酬类别、薪酬金额、薪酬时间、支付方式",
                                            "explain": "",
                                            "level": "4"
                                        },
                                        {
                                            "curr": "岗位绩效信息",
                                            "next": [],
                                            "fields": "数量绩效类别、数量绩效得分、质量绩效类别、质量绩效得分、服务绩效类别、服务绩效得分、绩效时间",
                                            "explain": "",
                                            "level": "3"
                                        }
                                    ],
                                    "fields": "",
                                    "explain": "",
                                    "level": ""
                                },
                                {
                                    "curr": "医院财务与成本核算信息",
                                    "next": [
                                        {
                                            "curr": "预算管理信息",
                                            "next": [],
                                            "fields": "预算类别、预算项目、项目名称、预算金额、审定金额、剩余金额、预算单位",
                                            "explain": "",
                                            "level": "2"
                                        },
                                        {
                                            "curr": "结算管理信息",
                                            "next": [],
                                            "fields": "结算类别、结算项目、结算日期、结算金额",
                                            "explain": "",
                                            "level": "3"
                                        },
                                        {
                                            "curr": "医保统筹金管信息",
                                            "next": [],
                                            "fields": "医保类别、医保金额、医保项目、支付机构、审核日期",
                                            "explain": "",
                                            "level": "3"
                                        },
                                        {
                                            "curr": "财务管理信息",
                                            "next": [],
                                            "fields": "财务编码、财务类别代码、财务类别名称、financecode、financekindcode、financekindname、采购金额、amount、采购数量、count",
                                            "explain": "",
                                            "level": "3"
                                        },
                                        {
                                            "curr": "价格管理信息",
                                            "next": [],
                                            "fields": "价格类别、项目代码、项目名称、项目规格、单位、价格、核算项目分类",
                                            "explain": "",
                                            "level": "2"
                                        },
                                        {
                                            "curr": "核算管理信息",
                                            "next": [],
                                            "fields": "核算类别、核算科目、启用日期、停止日期、成本类别、成本名称",
                                            "explain": "",
                                            "level": "2"
                                        }
                                    ],
                                    "fields": "",
                                    "explain": "",
                                    "level": ""
                                },
                                {
                                    "curr": "医院物资信息",
                                    "next": [
                                        {
                                            "curr": "药品管理信息",
                                            "next": [],
                                            "fields": "atccode、atc编码、atcname、atc名称、stockunit、包装单位 例包、medicareflag、报销标志1可报 2不可报 3部fen报 4适应症、manufacture、产地、mfrsqty、出厂规格与医嘱最小单位换算关系5、mfrsunit、出厂最小规格g、storegrade、存储级别、、药品金额、药品编码、药品名称、药品规格、药品批次、计量单位、药品分类、供应商",
                                            "explain": "",
                                            "level": "2"
                                        },
                                        {
                                            "curr": "血库管理信息",
                                            "next": [],
                                            "fields": "血液预订、入库、储存、发放",
                                            "explain": "",
                                            "level": "3"
                                        },
                                        {
                                            "curr": "医用普通耗材管理信息",
                                            "next": [],
                                            "fields": "耗材编码、普耗类型、普耗名称、普耗规格、计量单位、库存地、库存量",
                                            "explain": "",
                                            "level": "2"
                                        },
                                        {
                                            "curr": "植入材料管理信息",
                                            "next": [],
                                            "fields": "植入耗材编码、植入还差类型、植入耗材描述、植入条码信息、植入耗材规格、计量单位、库存量",
                                            "explain": "",
                                            "level": "2"
                                        },
                                        {
                                            "curr": "医疗器材管理信息",
                                            "next": [],
                                            "fields": "产品注册证号、产品注册证截止时间、产品注册证起始时间、产品注册证延期标志、产品注册延期时间、厂家名称、厂商编码、厂商名称、器械编码、器械名称、器械规格就、计量单位、库存量",
                                            "explain": "",
                                            "level": "2"
                                        },
                                        {
                                            "curr": "后勤物资管理信息",
                                            "next": [],
                                            "fields": "材料条码、materialcode、物资编码、物资类型、物资规格、物资描述、计量单位、采购价格",
                                            "explain": "",
                                            "level": "2"
                                        }
                                    ],
                                    "fields": "",
                                    "explain": "",
                                    "level": ""
                                },
                                {
                                    "curr": "系统管理信息",
                                    "next": [
                                        {
                                            "curr": "配置信息",
                                            "next": [],
                                            "fields": "关键配置参数、存放路径、重要参数",
                                            "explain": "",
                                            "level": "2"
                                        },
                                        {
                                            "curr": "办公软件资源",
                                            "next": [],
                                            "fields": "安装包、升级包、硬件产品管理信息、说明文档",
                                            "explain": "",
                                            "level": "2"
                                        },
                                        {
                                            "curr": "IT资产管理信息",
                                            "next": [],
                                            "fields": "资产类型信息、资产价值信息、资产折旧信息、资产生命周期信息、拓扑关系信息、系统网络安全等级清单",
                                            "explain": "",
                                            "level": "2"
                                        },
                                        {
                                            "curr": "数据字典信息",
                                            "next": [],
                                            "fields": "数据字典、数据符号、数据示意、说明解释、规则信息、基础数据标准、编码规则、业务逻辑",
                                            "explain": "",
                                            "level": "2"
                                        },
                                        {
                                            "curr": "安全管理信息",
                                            "next": [],
                                            "fields": "系统漏洞信息、安全防护配置与策略信息、自行识别的威胁数据、安全告警信息、安全事件信息",
                                            "explain": "",
                                            "level": "2"
                                        },
                                        {
                                            "curr": "运行信息",
                                            "next": [],
                                            "fields": "时间元素、记录时间、记录日期、记录内容、告警类型、告警等级、诊断信息、运行日志",
                                            "explain": "",
                                            "level": "2"
                                        },
                                        {
                                            "curr": "系统运维信息",
                                            "next": [],
                                            "fields": "系统维护shell脚本、系统维护SQL脚本、统计数据shell脚本、统计数据SQL脚本",
                                            "explain": "",
                                            "level": "2"
                                        },
                                        {
                                            "curr": "认证凭证信息",
                                            "next": [],
                                            "fields": "数据库密码、API密钥、SSH密钥、MRI密码",
                                            "explain": "",
                                            "level": "4"
                                        }
                                    ],
                                    "fields": "",
                                    "explain": "",
                                    "level": ""
                                },
                                {
                                    "curr": "医院固定资产信息",
                                    "next": [
                                        {
                                            "curr": "资产形成管理信息",
                                            "next": [],
                                            "fields": "病床类型、资产编码、资产分类、资产名称、资产子类、购置日期",
                                            "explain": "",
                                            "level": "2"
                                        },
                                        {
                                            "curr": "资产使用管理信息",
                                            "next": [],
                                            "fields": "质检状态、质检人",
                                            "explain": "",
                                            "level": "2"
                                        }
                                    ],
                                    "fields": "",
                                    "explain": "",
                                    "level": ""
                                },
                                {
                                    "curr": "其他单位信息",
                                    "next": [
                                        {
                                            "curr": "单位基本信息",
                                            "next": [],
                                            "fields": "单位名称、单位类型",
                                            "explain": "",
                                            "level": "2"
                                        },
                                        {
                                            "curr": "单位联系人信息",
                                            "next": [],
                                            "fields": "单位联系人姓名、单位联系人电话、单位联系人邮箱",
                                            "explain": "",
                                            "level": "3"
                                        }
                                    ],
                                    "fields": "",
                                    "explain": "",
                                    "level": ""
                                }
                            ],
                            "fields": "",
                            "explain": "",
                            "level": ""
                        }
                    ],
                    "fields": "",
                    "explain": "",
                    "level": ""
                },
                {
                    "curr": "公共卫生数据",
                    "next": [
                        {
                            "curr": "环境卫生数据",
                            "next": [],
                            "fields": "环境卫生",
                            "explain": "",
                            "level": "2"
                        },
                        {
                            "curr": "传染病疫情数据",
                            "next": [],
                            "fields": "传染病、疫情",
                            "explain": "",
                            "level": "2"
                        },
                        {
                            "curr": "疾病监测数据",
                            "next": [],
                            "fields": "疾病监测",
                            "explain": "",
                            "level": "2"
                        },
                        {
                            "curr": "疾病预防数据",
                            "next": [
                                {
                                    "curr": "艾滋病综合防治",
                                    "next": [],
                                    "fields": "艾滋病患者个人信息、艾滋病接触史代码、性病史代码、艾滋病阳性检测方法代码、艾滋病病毒感染诊新日期、艾滋病患者标志、抗病毒治疗标志、艾滋病抗病毒治疗编号、美沙酮维持治疗",
                                    "explain": "",
                                    "level": "5"
                                },
                                {
                                    "curr": "其他",
                                    "next": [],
                                    "fields": "疾病预防",
                                    "explain": "",
                                    "level": "2"
                                }
                            ],
                            "fields": "",
                            "explain": "",
                            "level": ""
                        },
                        {
                            "curr": "出生死亡数据",
                            "next": [],
                            "fields": "婴儿数量、出生人数、死亡人数",
                            "explain": "",
                            "level": "2"
                        }
                    ],
                    "fields": "",
                    "explain": "",
                    "level": ""
                },
                {
                    "curr": "其他信息",
                    "next": [
                        {
                            "curr": "其他信息",
                            "next": [],
                            "fields": "不属于其他类别的信息",
                            "explain": "",
                            "level": "2"
                        }
                    ],
                    "fields": "",
                    "explain": "",
                    "level": ""
                }
            ]
        }
    }
}'
        )

    # 获取[DONE]前面的那条消息
    last_message=$(echo "$response" | grep "^data: " | grep -v "^data: \[DONE\]" | tail -1 | sed 's/^data: //')

    # 检查是否为空
    if [ -z "$last_message" ]; then
        echo "[$request_id] 错误: 没有收到响应" >> "$ERROR_LOG"
        ((errors++))
        return
    fi

    # 提取index1和index2的值
    index1=$(echo "$last_message" | jq -r '.data.results.index1 // empty')
    index2=$(echo "$last_message" | jq -r '.data.results.index2 // empty')

    # 保存[DONE]前面的最后一条消息（不管是否有index1，index2）
    echo "[$request_id] [DONE]前最后消息: $last_message" >> "$LOG_FILE"

    # 检查index1和index2是否为空或空字符串
    if [ -z "$index1" ] || [ -z "$index2" ] || [ "$index1" = '""' ] || [ "$index2" = '""' ] || [ "$index1" = "null" ] || [ "$index2" = "null" ]; then
        echo "[$request_id] 警告: index1或index2为空/无效值 (index1=$index1, index2=$index2)" >> "$ERROR_LOG"
        echo "[$request_id] 完整响应: $last_message" >> "$ERROR_LOG"
        ((errors++))
    else
        echo "[$request_id] 成功: index1=$index1, index2=$index2" >> "$LOG_FILE"
        echo "[$request_id] 成功时[DONE]前最后消息: $last_message" >> "$LOG_FILE"
    fi

    ((completed++))
    echo "已完成: $completed/$TOTAL_REQUESTS (错误: $errors)" | tee -a "$LOG_FILE"
}

echo "开始执行批量请求 - 总数: $TOTAL_REQUESTS, 并发数: $CONCURRENT_REQUESTS"
echo "开始时间: $(date)"

# 使用GNU Parallel执行并发请求
# 如果没有安装，可以使用: apt-get install parallel 或 yum install parallel
if command -v parallel &> /dev/null; then
    seq 1 $TOTAL_REQUESTS | parallel -j $CONCURRENT_REQUESTS execute_request
else
    # 如果没有GNU Parallel，使用简单的后台作业方式
    for i in $(seq 1 $TOTAL_REQUESTS); do
        # 控制并发数
        while [ $(jobs -r | wc -l) -ge $CONCURRENT_REQUESTS ]; do
            sleep 0.5
        done

        # 在后台执行请求
        execute_request $i &
    done

    # 等待所有后台作业完成
    wait
fi

echo "结束时间: $(date)"
echo "总结: 完成 $completed/$TOTAL_REQUESTS 请求，错误: $errors"
echo "详细日志: $LOG_FILE"
echo "错误日志: $ERROR_LOG"