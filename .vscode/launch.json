{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "secwalk-run",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceFolder}/cmd/main.go",
            "args": [
                "run",
                // "--consul",
                // "***********:8500",
                // "--token",
                // "d9295a36-9f55-4580-b22d-71cfa5837f76",
                "--config",
                "configs/dev.yaml",
                "--pprof",
                "127.0.0.1:18919"
            ],
            "cwd": "${workspaceFolder}",
            // "env": {
            //     "http_proxy": "http://127.0.0.1:7890",
            //     "https_proxy": "http://127.0.0.1:7890",
            //     "OPENAI_API_KEY": "***************************************************",
            // }
        },
        {
            "name": "secwalk-init",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceFolder}/cmd/main.go",
            "args": [
                "init",
                "--config",
                "configs/dev.yaml",
            ],
            "cwd": "${workspaceFolder}"
        },
    ]
}