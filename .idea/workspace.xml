<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="ALL" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="d332aed6-425b-4e61-a6d9-126f02d02e49" name="Changes" comment="wip: 25C10">
      <change afterPath="$PROJECT_DIR$/cmd/mcp-proxy/main.go" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/internal/domain/core/toolkit/mcp/mcp.go" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/configs/dev.yaml" beforeDir="false" afterPath="$PROJECT_DIR$/configs/dev.yaml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/internal/domain/core/agent/agent.go" beforeDir="false" afterPath="$PROJECT_DIR$/internal/domain/core/agent/agent.go" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/internal/domain/core/callback/stream_message.go" beforeDir="false" afterPath="$PROJECT_DIR$/internal/domain/core/callback/stream_message.go" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/internal/domain/core/llm/llm.go" beforeDir="false" afterPath="$PROJECT_DIR$/internal/domain/core/llm/llm.go" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/internal/domain/core/schema/ext/ext.go" beforeDir="false" afterPath="$PROJECT_DIR$/internal/domain/core/schema/ext/ext.go" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/internal/domain/core/schema/toolkit.go" beforeDir="false" afterPath="$PROJECT_DIR$/internal/domain/core/schema/toolkit.go" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/internal/domain/core/toolkit/browser/browser.go" beforeDir="false" afterPath="$PROJECT_DIR$/internal/domain/core/toolkit/browser/browser.go" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/internal/domain/repo/repo_toolkit.go" beforeDir="false" afterPath="$PROJECT_DIR$/internal/domain/repo/repo_toolkit.go" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Go File" />
      </list>
    </option>
  </component>
  <component name="GOROOT" url="file://$USER_HOME$/go/pkg/mod/golang.org/<EMAIL>-amd64" />
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="25C01M02-LJH" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
    <option name="RESET_MODE" value="MIXED" />
  </component>
  <component name="GitToolBoxStore">
    <option name="recentBranches">
      <RecentBranches>
        <option name="branchesForRepo">
          <list>
            <RecentBranchesForRepo>
              <option name="branches">
                <list>
                  <RecentBranch>
                    <option name="branchName" value="25C03-ytt" />
                    <option name="lastUsedInstant" value="1743990924" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="25C01M02-LJH" />
                    <option name="lastUsedInstant" value="1743990495" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="25C01-YTT" />
                    <option name="lastUsedInstant" value="1743990329" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="25C00-LJH" />
                    <option name="lastUsedInstant" value="1742952093" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="25C01M03-cursor" />
                    <option name="lastUsedInstant" value="1742952080" />
                  </RecentBranch>
                </list>
              </option>
              <option name="repositoryRootUrl" value="file://$PROJECT_DIR$" />
            </RecentBranchesForRepo>
          </list>
        </option>
      </RecentBranches>
    </option>
  </component>
  <component name="GoLibraries">
    <option name="indexEntireGoPath" value="true" />
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="file://$PROJECT_DIR$/image:79c03863dc4386d1c850dd8b640124c8.png&lt;3241e653-c446-4313-b8a9-d97001e6724f&gt;" root0="SKIP_INSPECTION" />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="CurrentFile" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 0
}</component>
  <component name="ProjectId" id="2cilz8kZvaiPOaSliMFDq95pEPG" />
  <component name="ProjectLevelVcsManager" settingsEditedManually="true" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;DefaultGoTemplateProperty&quot;: &quot;Go File&quot;,
    &quot;Go 构建.go build secwalk/cmd.executor&quot;: &quot;Debug&quot;,
    &quot;Go 测试.secwalk/internal/domain/core/toolkit/secdocx 中的 TestDocxParsing.executor&quot;: &quot;Debug&quot;,
    &quot;Go 测试.secwalk/internal/domain/core/toolkit/secdocx 中的 TestFileTypeInference/PPTX.executor&quot;: &quot;Debug&quot;,
    &quot;Go 测试.secwalk/internal/domain/core/toolkit/secdocx 中的 TestPdfParsing.executor&quot;: &quot;Debug&quot;,
    &quot;Go 测试.secwalk/internal/domain/core/toolkit/secdocx 中的 TestPptxParsing.executor&quot;: &quot;Debug&quot;,
    &quot;Go 测试.secwalk/internal/domain/core/toolkit/secdocx 中的 TestXlsxParsing.executor&quot;: &quot;Debug&quot;,
    &quot;RunOnceActivity.OpenProjectViewOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.go.formatter.settings.were.checked&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.go.migrated.go.modules.settings&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.go.modules.automatic.dependencies.download&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.go.modules.go.list.on.any.changes.was.set&quot;: &quot;true&quot;,
    &quot;WebServerToolWindowFactoryState&quot;: &quot;false&quot;,
    &quot;git-widget-placeholder&quot;: &quot;25C03-ytt&quot;,
    &quot;go.import.settings.migrated&quot;: &quot;true&quot;,
    &quot;go.sdk.automatically.set&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;/Users/<USER>
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;go.custom.fmt.functions&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="$PROJECT_DIR$/internal/domain/core/toolkit/secdocx/secdocx/sample" />
      <recent name="$PROJECT_DIR$/pkg/unioffice" />
      <recent name="$PROJECT_DIR$/internal/domain/core/toolkit/secdocx" />
      <recent name="$PROJECT_DIR$/testdata/dataclass" />
      <recent name="$PROJECT_DIR$/core/workflow" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="$PROJECT_DIR$/internal/domain/core/toolkit/secdocx" />
      <recent name="$PROJECT_DIR$/testdata" />
      <recent name="$PROJECT_DIR$/internal/domain/core/toolkit/secdocx/secdocx" />
      <recent name="$PROJECT_DIR$/migrations/mysql/25C01" />
      <recent name="$PROJECT_DIR$/internal/domain/core/schema" />
    </key>
  </component>
  <component name="RunManager">
    <configuration default="true" type="GoApplicationRunConfiguration" factoryName="Go Application">
      <module name="secwalk" />
      <working_directory value="$PROJECT_DIR$" />
      <go_parameters value="-i" />
      <kind value="FILE" />
      <directory value="$PROJECT_DIR$" />
      <filePath value="$PROJECT_DIR$" />
      <method v="2" />
    </configuration>
    <configuration name="go build secwalk/cmd" type="GoApplicationRunConfiguration" factoryName="Go Application" temporary="true" nameIsGenerated="true">
      <module name="secwalk" />
      <working_directory value="$PROJECT_DIR$" />
      <parameters value="run --config configs/dev.yaml" />
      <kind value="PACKAGE" />
      <package value="secwalk/cmd" />
      <directory value="$PROJECT_DIR$" />
      <filePath value="$PROJECT_DIR$/cmd/main.go" />
      <method v="2" />
    </configuration>
    <configuration default="true" type="GoTestRunConfiguration" factoryName="Go Test">
      <module name="secwalk" />
      <working_directory value="$PROJECT_DIR$" />
      <go_parameters value="-i" />
      <kind value="DIRECTORY" />
      <directory value="$PROJECT_DIR$" />
      <filePath value="$PROJECT_DIR$" />
      <framework value="gotest" />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Go 构建.go build secwalk/cmd" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-gosdk-d297c17c1fbd-57c114c3cede-org.jetbrains.plugins.go.sharedIndexes.bundled-GO-243.26053.20" />
        <option value="bundled-js-predefined-d6986cc7102b-1632447f56bf-JavaScript-GO-243.26053.20" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.History.Properties">
    <option name="COLUMN_ID_ORDER">
      <list>
        <option value="Default.Root" />
        <option value="Default.Author" />
        <option value="Default.Date" />
        <option value="Default.Subject" />
        <option value="Space.CommitStatus" />
      </list>
    </option>
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="RECENT_FILTERS">
      <map>
        <entry key="Branch">
          <value>
            <list>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="main" />
                </option>
              </RecentGroup>
            </list>
          </value>
        </entry>
        <entry key="User">
          <value>
            <list>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="*" />
                </option>
              </RecentGroup>
            </list>
          </value>
        </entry>
      </map>
    </option>
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="25C01-YTT" />
                      </list>
                    </value>
                  </entry>
                  <entry key="text">
                    <value>
                      <list>
                        <option value="fix" />
                      </list>
                    </value>
                  </entry>
                  <entry key="user">
                    <value>
                      <list>
                        <option value="*" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="fix:插件变量赋值格式转换错误" />
    <MESSAGE value="feat:切换镜像仓库" />
    <MESSAGE value="feat:mapstructure库会导致混淆编译失败" />
    <MESSAGE value="fix:修复r1模型思考过程token没有记录" />
    <MESSAGE value="fix: 修复tokenchecker为nil" />
    <MESSAGE value="fix: 修复智能体options参数失效" />
    <MESSAGE value="feat:新增模型注册插件" />
    <MESSAGE value="fix:修复openapi配置错误" />
    <MESSAGE value="fix:初始化加载token文件" />
    <MESSAGE value="fix:修复Thought单词拼写错误" />
    <MESSAGE value="fix:修复r1思维链tokens计算错误问题" />
    <MESSAGE value="feat:新增MessageNodeID，流失传输里，节点ID勇MessageNodeID代替" />
    <MESSAGE value="fix:节点参数初始化时，同时加载插件/智能体的options参数" />
    <MESSAGE value="fix:修复garble编译报错问题" />
    <MESSAGE value="feat: 输出结果默认拼接思考过程" />
    <MESSAGE value="feat: 许可新逻辑" />
    <MESSAGE value="feat: 节点保存变量历史记录" />
    <MESSAGE value="fix: 修复工具调试报错" />
    <MESSAGE value="fix: 新增全局变量初始化" />
    <MESSAGE value="feat:智能体解密失败时不影响后续智能体解密" />
    <MESSAGE value="feat: history变量使用方式修改" />
    <MESSAGE value="feat: cursor写的功能" />
    <MESSAGE value="feat: react思维链 模型提示词正则过滤提取" />
    <MESSAGE value="fix:移除openai相关模型调用" />
    <MESSAGE value="wip: 25C10" />
    <option name="LAST_COMMIT_MESSAGE" value="wip: 25C10" />
  </component>
  <component name="VgoProject">
    <settings-migrated>true</settings-migrated>
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="DlvLineBreakpoint">
          <url>file://$PROJECT_DIR$/core/chain/llmchain/chain_execution_test.go</url>
          <line>43</line>
          <option name="timeStamp" value="1" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DlvLineBreakpoint">
          <url>file://$PROJECT_DIR$/core/chain/llmchain/chain_execution_test.go</url>
          <line>27</line>
          <option name="timeStamp" value="2" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DlvLineBreakpoint">
          <url>file://$PROJECT_DIR$/internal/domain/core/callback/stream_question.go</url>
          <line>57</line>
          <option name="timeStamp" value="2015" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DlvLineBreakpoint">
          <url>file://$PROJECT_DIR$/internal/domain/core/toolkit/python/python.go</url>
          <line>103</line>
          <option name="timeStamp" value="2079" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DlvLineBreakpoint">
          <url>file://$PROJECT_DIR$/internal/domain/core/schema/agent.go</url>
          <line>572</line>
          <option name="timeStamp" value="2268" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DlvLineBreakpoint">
          <url>file://$PROJECT_DIR$/internal/domain/core/endpoint/hbrag.go</url>
          <line>70</line>
          <option name="timeStamp" value="2396" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DlvLineBreakpoint">
          <url>file://$PROJECT_DIR$/internal/server/handler/handler_toolkit.go</url>
          <line>156</line>
          <option name="timeStamp" value="2401" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DlvLineBreakpoint">
          <url>file://$PROJECT_DIR$/internal/domain/core/llm/dbapp/dbapp_chat.go</url>
          <line>154</line>
          <option name="timeStamp" value="2474" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DlvLineBreakpoint">
          <url>file://$PROJECT_DIR$/internal/domain/core/toolkit/restful/restful_test.go</url>
          <line>153</line>
          <option name="timeStamp" value="2519" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DlvLineBreakpoint">
          <url>file://$PROJECT_DIR$/internal/domain/core/chain/chain_react.go</url>
          <line>500</line>
          <option name="timeStamp" value="2529" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DlvLineBreakpoint">
          <url>file://$PROJECT_DIR$/internal/domain/core/llm/openai/openai_chat.go</url>
          <line>78</line>
          <option name="timeStamp" value="2547" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DlvLineBreakpoint">
          <url>file://$USER_HOME$/go/pkg/mod/github.com/sashabaranov/go-openai@v1.20.5/chat_stream.go</url>
          <line>45</line>
          <option name="timeStamp" value="2548" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DlvLineBreakpoint">
          <url>file://$USER_HOME$/go/pkg/mod/github.com/sashabaranov/go-openai@v1.20.5/client.go</url>
          <line>184</line>
          <option name="timeStamp" value="2549" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DlvLineBreakpoint">
          <url>file://$USER_HOME$/go/pkg/mod/github.com/sashabaranov/go-openai@v1.20.5/client.go</url>
          <line>54</line>
          <option name="timeStamp" value="2552" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DlvLineBreakpoint">
          <url>file://$PROJECT_DIR$/internal/domain/core/toolkit/secdocx/secdocx/extract/pdf/pdf.go</url>
          <line>47</line>
          <option name="timeStamp" value="2568" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DlvLineBreakpoint">
          <url>file://$PROJECT_DIR$/internal/domain/core/toolkit/secdocx/secdocx/extract/pdf/pdf.go</url>
          <line>105</line>
          <option name="timeStamp" value="2569" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DlvLineBreakpoint">
          <url>file://$PROJECT_DIR$/internal/domain/core/toolkit/secdocx/secdocx/extract/extract.go</url>
          <line>26</line>
          <option name="timeStamp" value="2595" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DlvLineBreakpoint">
          <url>file://$PROJECT_DIR$/internal/domain/core/schema/parameter.go</url>
          <line>325</line>
          <option name="timeStamp" value="2671" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DlvLineBreakpoint">
          <url>file://$PROJECT_DIR$/internal/domain/core/schema/parameter.go</url>
          <line>322</line>
          <option name="timeStamp" value="2673" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DlvLineBreakpoint">
          <url>file://$PROJECT_DIR$/internal/domain/core/toolkit/browser/browser.go</url>
          <line>361</line>
          <option name="timeStamp" value="2686" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DlvLineBreakpoint">
          <url>file://$PROJECT_DIR$/internal/domain/core/toolkit/browser/browser.go</url>
          <line>357</line>
          <option name="timeStamp" value="2687" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DlvLineBreakpoint">
          <url>file://$PROJECT_DIR$/internal/domain/core/toolkit/browser/browser.go</url>
          <line>253</line>
          <option name="timeStamp" value="2692" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DlvLineBreakpoint">
          <url>file://$PROJECT_DIR$/internal/domain/core/toolkit/browser/browser.go</url>
          <line>251</line>
          <option name="timeStamp" value="2693" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DlvLineBreakpoint">
          <url>file://$PROJECT_DIR$/internal/domain/core/toolkit/browser/browser.go</url>
          <line>249</line>
          <option name="timeStamp" value="2694" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DlvLineBreakpoint">
          <url>file://$PROJECT_DIR$/internal/domain/core/toolkit/browser/browser.go</url>
          <line>279</line>
          <option name="timeStamp" value="2695" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DlvLineBreakpoint">
          <url>file://$PROJECT_DIR$/internal/server/server.go</url>
          <line>16</line>
          <option name="timeStamp" value="2696" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
    <watches-manager>
      <configuration name="GoApplicationRunConfiguration">
        <watch expression="json.Unmarshal(event.Data, &amp;BrowserPlanData)" language="go" />
        <watch expression="json.Unmarshal(event.Data, &amp;planData)" language="go" />
      </configuration>
    </watches-manager>
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/secwalk$TestWorkFlowChain_in_secwalk_core_workflow.out" NAME="TestWorkFlowChain in secwalk/core/workflow Coverage Results" MODIFIED="1709054689127" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="GoCoverage" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" />
    <SUITE FILE_PATH="coverage/secwalk$TestIntentTool_in_secwalk_core_toolkit_restful.out" NAME="TestIntentTool in secwalk/core/toolkit/restful Coverage Results" MODIFIED="1713258700796" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="GoCoverage" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" />
    <SUITE FILE_PATH="coverage/secwalk$TestRegex_in_secwalk_core_agent.out" NAME="TestRegex in secwalk/core/agent Coverage Results" MODIFIED="1715133509363" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="GoCoverage" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" />
  </component>
</project>