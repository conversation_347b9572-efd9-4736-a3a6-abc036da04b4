Index: core/agent/agent_test.go
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>package agent\n\nimport (\n\t\"encoding/json\"\n\t\"fmt\"\n\t\"secwalk/core/llm\"\n\t\"secwalk/core/llm/dbapp\"\n\t\"secwalk/core/schema\"\n\t\"secwalk/core/sqloader\"\n\t\"testing\"\n\n\t\"github.com/stretchr/testify/assert\"\n\t\"github.com/stretchr/testify/require\"\n)\n\nfunc TestNewAgent(t *testing.T) {\n\tvar agent schema.AgentConfig\n\tagent.ID = \"2ee56d84-35f1-4e42-ac4f-0039bc41f180\"\n\tagent.Name = \"工具调用测试\"\n\tagent.Description = \"工具调用测试\"\n\tagent.InputParameters = schema.Parameters{}\n\tagent.Model = &schema.ModelConfig{Model: dbapp.QWEN4}\n\tagent.Nodes = make([]*schema.Node, 0)\n\tagent.Edges = make([]*schema.Edge, 0)\n\n\tagent.Nodes = append(agent.Nodes,\n\t\t&schema.Node{\n\t\t\tID:   \"node\",\n\t\t\tName: \"mixup_tool\",\n\t\t\tType: schema.NodeTypeLLMMixup,\n\t\t\tTools: []schema.Component{\n\t\t\t\t{\n\t\t\t\t\tID: \"6a1ee208-fa8e-4ec4-9e9e-912bd9d4e3ed\",\n\t\t\t\t\tOptions: map[string]any{\n\t\t\t\t\t\t\"eval\": \"2+2\",\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t},\n\t\t\tDB: &schema.DB{\n\t\t\t\tType: sqloader.DialectSqlite3,\n\t\t\t\tDSN:  \"C:/Users/<USER>/Desktop/SQLITEDB/SQLITEDB.db\",\n\t\t\t\tTables: []sqloader.Table{\n\t\t\t\t\t{\n\t\t\t\t\t\tName:        \"spiders\",\n\t\t\t\t\t\tDescription: \"勒索软件相关知识库\",\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tName:        \"decryptions\",\n\t\t\t\t\t\tDescription: \"勒索软件解密工具库\",\n\t\t\t\t\t\tFields: []sqloader.Field{\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tName:    \"family\",\n\t\t\t\t\t\t\t\tComment: \"家族\",\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tName:    \"decrypted_lnk\",\n\t\t\t\t\t\t\t\tComment: \"解密链接\",\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t},\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t},\n\t\t\tResultKey:  schema.DefaultResultKey,\n\t\t\tTimeout:    schema.DefaultTimeout,\n\t\t\tMaxRetries: schema.DefaultMaxRetries,\n\t\t},\n\t)\n\tagent.Nodes = append(agent.Nodes,\n\t\t&schema.Node{\n\t\t\tID:   \"node0\",\n\t\t\tName: \"act_tool\",\n\t\t\tType: schema.NodeTypeActTool,\n\t\t\tTools: []schema.Component{\n\t\t\t\t{\n\t\t\t\t\tID: \"6a1ee208-fa8e-4ec4-9e9e-912bd9d4e3ed\",\n\t\t\t\t\tOptions: map[string]any{\n\t\t\t\t\t\t\"eval\": \"2+2\",\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t},\n\t\t\tResultKey:  schema.DefaultResultKey,\n\t\t\tTimeout:    schema.DefaultTimeout,\n\t\t\tMaxRetries: schema.DefaultMaxRetries,\n\t\t},\n\t)\n\tagent.Nodes = append(agent.Nodes,\n\t\t&schema.Node{\n\t\t\tID:              \"node1\",\n\t\t\tName:            \"react_tool\",\n\t\t\tType:            schema.NodeTypeLLMReact,\n\t\t\tInputExpression: \"{{.input}}\",\n\t\t\tTools: []schema.Component{\n\t\t\t\t{\n\t\t\t\t\tID: \"6a1ee208-fa8e-4ec4-9e9e-912bd9d4e3ed\",\n\t\t\t\t},\n\t\t\t},\n\t\t\tResultKey:  schema.DefaultResultKey,\n\t\t\tTimeout:    schema.DefaultTimeout,\n\t\t\tMaxRetries: schema.DefaultMaxRetries,\n\t\t},\n\t)\n\tagent.Nodes = append(agent.Nodes,\n\t\t&schema.Node{\n\t\t\tID:              \"node2\",\n\t\t\tName:            \"llm_tool\",\n\t\t\tType:            schema.NodeTypeLLMTools,\n\t\t\tInputExpression: \"{{.input}}\",\n\t\t\tTools: []schema.Component{\n\t\t\t\t{\n\t\t\t\t\tID: \"6a1ee208-fa8e-4ec4-9e9e-912bd9d4e3ed\",\n\t\t\t\t},\n\t\t\t},\n\t\t\tResultKey:  schema.DefaultResultKey,\n\t\t\tTimeout:    schema.DefaultTimeout,\n\t\t\tMaxRetries: schema.DefaultMaxRetries,\n\t\t},\n\t)\n\n\tagent.Edges = append(agent.Edges, &schema.Edge{\n\t\tSrcNode: \"node\",\n\t\tDstNode: \"node0\",\n\t})\n\tagent.Edges = append(agent.Edges, &schema.Edge{\n\t\tSrcNode: \"node0\",\n\t\tDstNode: \"node1\",\n\t})\n\tagent.Edges = append(agent.Edges, &schema.Edge{\n\t\tSrcNode: \"node1\",\n\t\tDstNode: \"node2\",\n\t})\n\tagent.Entry = \"node\"\n\n\tdata, err := json.Marshal(&agent)\n\trequire.NoError(t, err)\n\n\tfmt.Println(string(data))\n}\n\nfunc TestMultiAgents(t *testing.T) {\n\tvar master, branch schema.AgentConfig\n\n\tbranch.ID = \"954443d6-1418-4257-9cec-3f4dc8c99c70\"\n\tbranch.Name = \"网络安全专家\"\n\tbranch.Description = \"拥有丰富的网络安全领域知识，能回答一切相关的问题。\"\n\tbranch.InputParameters = schema.Parameters{\n\t\t{\n\t\t\tName:        \"input\",\n\t\t\tDescription: \"网络安全相关的问题\",\n\t\t\tType:        schema.TypeString,\n\t\t\tRequired:    true,\n\t\t},\n\t}\n\tbranch.Model = &schema.ModelConfig{Model: dbapp.QWEN4}\n\tbranch.Nodes = make([]*schema.Node, 0)\n\tbranch.Edges = make([]*schema.Edge, 0)\n\tbranch.Entry = \"node\"\n\tbranch.Nodes = append(branch.Nodes, &schema.Node{\n\t\tID:         \"node\",\n\t\tName:       \"小恒智聊\",\n\t\tType:       schema.NodeTypeLLMMixup,\n\t\tResultKey:  schema.DefaultResultKey,\n\t\tTimeout:    schema.DefaultTimeout,\n\t\tMaxRetries: schema.DefaultMaxRetries,\n\t})\n\n\tmaster.ID = \"90d2d3e5-2841-4202-a1e2-b924bb3768d2\"\n\tmaster.Name = \"leader\"\n\tmaster.Description = \"能够解决许多问题。\"\n\tmaster.InputParameters = schema.Parameters{\n\t\t{\n\t\t\tName:        \"input\",\n\t\t\tDescription: \"用户问题\",\n\t\t\tType:        schema.TypeString,\n\t\t\tRequired:    true,\n\t\t},\n\t}\n\tmaster.Model = &schema.ModelConfig{Model: dbapp.QWEN4}\n\tmaster.Nodes = make([]*schema.Node, 0)\n\tmaster.Edges = make([]*schema.Edge, 0)\n\tmaster.Entry = \"node\"\n\tmaster.Nodes = append(master.Nodes, &schema.Node{\n\t\tID:         \"node\",\n\t\tName:       \"回答\",\n\t\tType:       schema.NodeTypeLLMMixup,\n\t\tResultKey:  schema.DefaultResultKey,\n\t\tTimeout:    schema.DefaultTimeout,\n\t\tMaxRetries: schema.DefaultMaxRetries,\n\t\tAgents: []schema.Component{\n\t\t\t{ID: \"954443d6-1418-4257-9cec-3f4dc8c99c70\"},\n\t\t},\n\t})\n\n\td1, _ := json.Marshal(branch)\n\td2, _ := json.Marshal(master)\n\n\tfmt.Println(string(d1))\n\tfmt.Println(string(d2))\n}\n\nfunc TestAlertAnalyze(t *testing.T) {\n\tvar agent schema.AgentConfig\n\tagent.ID = \"6dffc412-6f28-4ee0-80e8-788a0d0d3fc6\"\n\tagent.Name = \"安全告警分析专家-蠕虫(410)\"\n\tagent.Description = \"作为安全告警分析专家，您熟悉安全告警分析以及取证流程，您将接收到一份告警聚合日志。请进行调查取证分析。\"\n\tagent.InputParameters = []schema.Parameter{\n\t\t{\n\t\t\tName:        \"alarm_log\",\n\t\t\tDescription: \"告警信息输入\",\n\t\t\tType:        schema.TypeString,\n\t\t\tRequired:    true,\n\t\t},\n\t\t{\n\t\t\tName:        \"steps\",\n\t\t\tDescription: \"分析步骤\",\n\t\t\tType:        schema.TypeArray,\n\t\t\tRequired:    true,\n\t\t},\n\t}\n\tagent.Model = &schema.ModelConfig{Type: llm.TypeChat, Model: dbapp.QWEN4}\n\tagent.Entry = \"node0\"\n\tagent.Nodes = make([]*schema.Node, 0)\n\tagent.Edges = make([]*schema.Edge, 0)\n\tagent.SystemPromptTemplate = `[身份与职责]\n你是一个安全事件分析溯源专家，首先你需要理解[蠕虫病毒的定义]与[主机失陷的判断方式]，根据[蠕虫病毒的特征]中的每一条特征顺序去调用工具获取相应的证据,来验证是否满足对应的病毒特征，最终研判设备是否失陷。\n\n[蠕虫病毒的定义]\n蠕虫病毒是一种恶意软件，它能够在不需要用户直接干预的情况下自动传播。与传统的病毒不同，蠕虫病毒不需要附着在宿主程序上，它们可以独立运行并通过网络自我复制和传播。\n蠕虫病毒通常利用操作系统或应用程序中的漏洞来传播。一旦找到漏洞，蠕虫病毒就可以通过网络发送恶意代码到其他计算机系统，从而感染它们。这种自我复制和传播的力使得蠕虫病毒能够迅速扩散，给网络系统带来严重的安全威胁。\n主流的蠕虫病毒类型包含勒索病毒、挖矿病毒。\n  \n[主机失陷的判断方式]\n* 【重要】威胁情报告警信息在非工作时间触发，会增加主机失陷的概率；\n  \n[蠕虫病毒的特征]\n- 蠕虫病毒恶意域名回连的特征：蠕虫病毒会向恶意服务器发起网络请求(告警中的公网ip、域名需要重新调用威胁情报查询工具获取威胁情报避免威胁情报过期导致的误报)。\n- 蠕虫病毒文件特征：蠕虫病毒在对应主机中以文件形式存在。\n- 蠕虫病毒系统篡改特征：蠕虫病毒会对所在主机的注册表、系统启动项、定时任务、快捷方式进行篡改的行为。\n- 蠕虫病毒内网横向攻击：因为蠕虫病毒需要复制传播，所以对应的主机在过去一段时间内有可能会有被其他同内网主机攻击的行为或者该主机可能对其他同内网大量主机攻击的行为。\n- 蠕虫病毒对硬件资源消耗特征：除上述特征以外，挖矿病毒会使对应主机的CPU/GPU负载极高；\n\n[待分析的告警日志]\n{{.alarm_log}}\n  \n[分析步骤记录]\n{{.history}}`\n\tagent.Nodes = append(agent.Nodes, &schema.Node{\n\t\tID:              \"node0\",\n\t\tName:            \"analysis\",\n\t\tType:            schema.NodeTypeLLMReact,\n\t\tInputExpression: \"判断该告警是否包含<{{.step}}>，在输出研判结果中，必须包含特征名称、是否符合特征、判断过程与依据等详细信息。\",\n\t\tTools: []schema.Component{\n\t\t\t{ID: \"dce493d8-c141-414b-9589-96fa2a676fe2\"},\n\t\t\t{ID: \"4aa2b0dd-65bb-4535-a85a-e7589cb7d24f\"},\n\t\t\t{ID: \"d069c32c-94f9-48bb-8f8a-bb6938dad354\"},\n\t\t\t{ID: \"1448b334-4ed0-44d0-be5c-9fca07632432\"},\n\t\t\t{ID: \"08d8b962-5862-404c-ab37-83f62253f5a0\"},\n\t\t\t{ID: \"26e339fe-884d-4baa-9988-7d6db4061ee7\"},\n\t\t\t{ID: \"b6ed4783-d50d-438e-9614-c2ef6de72712\"},\n\t\t\t{ID: \"a4fdff9f-b318-4688-8af0-e0323ed38ca4\"},\n\t\t\t{ID: \"fe701789-edb2-4f2e-be26-c95fbefcf430\"},\n\t\t},\n\t\tResultKey: \"output_list\",\n\t\tTimeout:   120,\n\t\tIteration: true,\n\t\tIterationValues: []schema.RangeValue{\n\t\t\t{ListName: \"steps\", DestName: \"step\"},\n\t\t},\n\t\tLogMemory: true,\n\t})\n\n\tagent.Nodes = append(agent.Nodes, &schema.Node{\n\t\tID:        \"node1\",\n\t\tName:      \"compile\",\n\t\tType:      schema.NodeTypeLLMBasic,\n\t\tResultKey: schema.DefaultResultKey,\n\t\tTimeout:   120,\n\t\tUserPromptTemplate: `现在所有取证已完成，请总结[分析步骤记录]，输出详细的分析汇总，如下第一人称内容风格：\n\n\t\t在完成了所有必要的取证工作之后，我仔细地对这次安全告警事件进行了全面的分析汇总。我的发现如下：  \n\t\t1 非工作时间的活动特征：我注意到这次告警是在非工作时间发生的，这与蠕虫病毒活动的常见特征非常吻合，从而提高了我对主机被侵害风险的评估。 \n\t\t2 ...\n  \n\t\t综合我以上的分析结果，并结合告警类型的特定特征以及基于研判知识库的评估，我得出结论：这次告警极具蠕虫病毒的行为特征，因此判断受影响设备的安全威胁等级较高。因此，我强烈建议立即采取一系列应对措施，包括但不限于隔离受影响主机、进行病毒清除、修补已识别的系统安全漏洞等，以有效遏制病毒进一步扩散并减轻潜在的影响。`,\n\t\tUseMemory: true,\n\t})\n\n\tagent.Edges = append(agent.Edges, &schema.Edge{\n\t\tSrcNode: \"node0\",\n\t\tDstNode: \"node1\",\n\t})\n\n\tdata, _ := json.Marshal(&agent)\n\tfmt.Println(string(data))\n\n\tdata, _ = json.Marshal(map[string]any{\n\t\t\"alarm_log\": `{\n\t\"聚合告警ID\": \"RANSOMWARE_ATTACK20230415\",\n\t\"聚合告警标题\": \"蠕虫软件攻击检测\",\n\t\"聚合告警生成时间\": \"2023-04-15T10:30:00Z\",\n\t\"聚合告警最后更新时间\": \"2023-04-15T11:00:00Z\",\n\t\"ATT&CK类型\": \"TA0040\",\n\t\"攻击详情\": {\n\t\t\"攻击类型\": \"蠕虫软件\",\n\t\"受害者资产\": {\n\t\t\"类型\": \"服务器\",\n\t\t\"IP地址\": \"***********\"\n\t\t\"ip地区\": \"局域网\"\n\t},\n\t\"攻击描述\": \"服务器***********被检测到尝试与已知的恶意域名back2.blockbitcoin.com进行1次通信，其中非工作时间的告警有1次，分别为2023-04-15T1:30:00Z\"\n\t},\n\t\"相关告警\": [\n\t\t{\n\t\t\t\"告警ID\":[\"ALERT987654\"]\n\t\t\t\"设备名称\": \"防火墙01\",\n\t\t\t\"设备类型\": \"防火墙\",\n\t\t\t\"设备IP\": \"***********\",\n\t\t\t\"告警产生模块\": \"入侵检测系统\"\n\t\t},\n\t]\n}`,\n\t\t\"steps\": []string{\n\t\t\t\"蠕虫病毒恶意域名回连特征\",\n\t\t\t\"蠕虫病毒文件特征\",\n\t\t\t\"蠕虫病毒系统篡改特征\",\n\t\t\t\"蠕虫病毒内网横向攻击\",\n\t\t\t\"蠕虫病毒对硬件资源消耗特征\",\n\t\t},\n\t})\n\tfmt.Println(string(data))\n}\n\nfunc TestFCTD(t *testing.T) {\n\tvar agent schema.AgentConfig\n\tagent.ID = \"bf87a582-abe7-4f0b-a798-6ba8e15c7515\"\n\tagent.Name = \"云沙箱\"\n\tagent.Description = \"上传样本，使用安恒云沙箱进行动态分析\"\n\tagent.InputParameters = schema.Parameters{}\n\tagent.Entry = \"node1\"\n\n\tagent.Nodes = []*schema.Node{\n\t\t{\n\t\t\tID:   \"node1\",\n\t\t\tName: \"生成Token\",\n\t\t\tType: schema.NodeTypeActTool,\n\t\t\tTools: []schema.Component{\n\t\t\t\t{\n\t\t\t\t\tID: \"5c1e41d0-91d4-4082-88cd-35a30a33e01f\",\n\t\t\t\t\tOptions: map[string]any{\n\t\t\t\t\t\t\"code\": `import time\nimport hashlib\n\ntoken = inputs[\"FCTD-Token\"]\ntimestamp = str(int(time.time()))\nraw_string = f\"{token}_{timestamp}_dbapp@FCTD2022\"\nmd5_hash = hashlib.md5(raw_string.encode('utf-8')).hexdigest()\nchecksum = f\"{timestamp}{md5_hash}\"\nprint(checksum)`,\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t},\n\t\t\tResultKey: schema.DefaultResultKey,\n\t\t\tResultParsers: []schema.ParseConfig{\n\t\t\t\t{\n\t\t\t\t\tType:       schema.ParserRegex,\n\t\t\t\t\tExpression: `[a-f0-9]{42}`,\n\t\t\t\t\tKey:        \"FCTD-Sign\",\n\t\t\t\t},\n\t\t\t},\n\t\t},\n\t\t{\n\t\t\tID:   \"node2\",\n\t\t\tName: \"上传样本\",\n\t\t\tType: schema.NodeTypeActTool,\n\t\t\tTools: []schema.Component{\n\t\t\t\t{\n\t\t\t\t\tID: \"97155a96-f134-4d51-a839-8b389d0f7b3f\",\n\t\t\t\t},\n\t\t\t},\n\t\t\tResultParsers: []schema.ParseConfig{\n\t\t\t\t{\n\t\t\t\t\tType:       schema.ParserRegex,\n\t\t\t\t\tExpression: `[a-f0-9]{32}`,\n\t\t\t\t\tKey:        \"taskId\",\n\t\t\t\t},\n\t\t\t},\n\t\t},\n\t\t{\n\t\t\tID:   \"node3\",\n\t\t\tName: \"查询分析状态\",\n\t\t\tType: schema.NodeTypeActTool,\n\t\t\tTools: []schema.Component{\n\t\t\t\t{\n\t\t\t\t\tID: \"595ff4db-fb9e-4666-b779-5882188fb7b4\",\n\t\t\t\t},\n\t\t\t},\n\t\t\tResultParsers: []schema.ParseConfig{\n\t\t\t\t{\n\t\t\t\t\tType:       schema.ParserJsonpath,\n\t\t\t\t\tExpression: `$data.status`,\n\t\t\t\t\tKey:        \"status\",\n\t\t\t\t},\n\t\t\t},\n\t\t\tPending: 10,\n\t\t},\n\t\t{\n\t\t\tID:   \"node4\",\n\t\t\tName: \"获取分析结果\",\n\t\t\tType: schema.NodeTypeActTool,\n\t\t\tTools: []schema.Component{\n\t\t\t\t{\n\t\t\t\t\tID: \"3849b4b6-5e13-4824-bcdb-ba00af902b9f\",\n\t\t\t\t},\n\t\t\t},\n\t\t\tResultParsers: []schema.ParseConfig{\n\t\t\t\t{\n\t\t\t\t\tType:       schema.ParserJsonpath,\n\t\t\t\t\tExpression: `$intelligences`,\n\t\t\t\t\tKey:        \"intelligences\",\n\t\t\t\t},\n\t\t\t},\n\t\t},\n\t}\n\n\tagent.Edges = []*schema.Edge{\n\t\t{\n\t\t\tSrcNode: \"node1\",\n\t\t\tDstNode: \"node2\",\n\t\t},\n\t\t{\n\t\t\tSrcNode: \"node2\",\n\t\t\tDstNode: \"node3\",\n\t\t},\n\t\t{\n\t\t\tSrcNode:   \"node3\",\n\t\t\tDstNode:   \"node3\",\n\t\t\tCondition: \"status == 0 or status == 1\",\n\t\t},\n\t\t{\n\t\t\tSrcNode:   \"node3\",\n\t\t\tDstNode:   \"node4\",\n\t\t\tCondition: \"status == 2\",\n\t\t},\n\t}\n\n\tdata, err := json.Marshal(agent)\n\tassert.NoError(t, err)\n\tfmt.Println(string(data))\n\n\tvar toolkit schema.Toolkit\n\ttoolkit.ID = \"6c76a454-e0e8-4f0f-91ce-c31a00e77d1a\"\n\ttoolkit.Name = \"云查\"\n\ttoolkit.Description = \"云查API，提供样本分析能力\"\n\ttoolkit.Url = \"https://fc.dbappsecurity.com.cn\"\n\ttoolkit.Parameters = schema.Parameters{\n\t\t{\n\t\t\tName:        \"FCTD-Token\",\n\t\t\tDescription: \"Token\",\n\t\t\tType:        schema.TypeString,\n\t\t\tRequired:    true,\n\t\t\tMethod:      schema.MethodHeader,\n\t\t},\n\t\t{\n\t\t\tName:        \"FCTD-Sign\",\n\t\t\tDescription: \"校验码\",\n\t\t\tType:        schema.TypeString,\n\t\t\tRequired:    true,\n\t\t\tMethod:      schema.MethodHeader,\n\t\t},\n\t}\n\n\ttoolkit.Tools = []schema.ToolConfig{\n\t\t{\n\t\t\tID:          \"97155a96-f134-4d51-a839-8b389d0f7b3f\",\n\t\t\tName:        \"沙箱分析\",\n\t\t\tDescription: \"上传样本文件进行沙箱动态分析\",\n\t\t\tInputParameters: schema.Parameters{\n\t\t\t\t{\n\t\t\t\t\tName:        \"file\",\n\t\t\t\t\tDescription: \"上传的文件ID\",\n\t\t\t\t\tType:        schema.TypeFile,\n\t\t\t\t\tRequired:    true,\n\t\t\t\t\tMethod:      schema.MethodBody,\n\t\t\t\t},\n\t\t\t},\n\t\t\tMethod: \"POST\",\n\t\t\tPath:   \"/fctd/v1/advanced/sandbox_upload_analysis\",\n\t\t},\n\t\t{\n\t\t\tID:          \"595ff4db-fb9e-4666-b779-5882188fb7b4\",\n\t\t\tName:        \"沙箱任务状态查询\",\n\t\t\tDescription: \"根据任务ID，查询沙箱分析状态\",\n\t\t\tInputParameters: schema.Parameters{\n\t\t\t\t{\n\t\t\t\t\tName:        \"taskId\",\n\t\t\t\t\tDescription: \"任务ID\",\n\t\t\t\t\tType:        schema.TypeString,\n\t\t\t\t\tRequired:    true,\n\t\t\t\t\tMethod:      schema.MethodQuery,\n\t\t\t\t},\n\t\t\t},\n\t\t\tMethod: \"GET\",\n\t\t\tPath:   \"/fctd/v1/advanced/get_task_status\",\n\t\t},\n\t\t{\n\t\t\tID:          \"3849b4b6-5e13-4824-bcdb-ba00af902b9f\",\n\t\t\tName:        \"沙箱结果获取\",\n\t\t\tDescription: \"根据任务ID，查询沙箱分析结果报告\",\n\t\t\tInputParameters: schema.Parameters{\n\t\t\t\t{\n\t\t\t\t\tName:        \"taskId\",\n\t\t\t\t\tDescription: \"任务ID\",\n\t\t\t\t\tType:        schema.TypeString,\n\t\t\t\t\tRequired:    true,\n\t\t\t\t\tMethod:      schema.MethodQuery,\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tName:         \"type\",\n\t\t\t\t\tDescription:  \"报告类型\",\n\t\t\t\t\tType:         schema.TypeString,\n\t\t\t\t\tRequired:     true,\n\t\t\t\t\tConfiged:     true,\n\t\t\t\t\tDefaultValue: \"0\",\n\t\t\t\t\tMethod:       schema.MethodQuery,\n\t\t\t\t},\n\t\t\t},\n\t\t\tMethod: \"GET\",\n\t\t\tPath:   \"/fctd/v1/advanced/get_sandbox_result\",\n\t\t},\n\t}\n\n\tdata, err = json.Marshal(toolkit)\n\tassert.NoError(t, err)\n\tfmt.Println(string(data))\n}\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/core/agent/agent_test.go b/core/agent/agent_test.go
--- a/core/agent/agent_test.go	
+++ b/core/agent/agent_test.go	
@@ -9,7 +9,6 @@
 	"secwalk/core/sqloader"
 	"testing"
 
-	"github.com/stretchr/testify/assert"
 	"github.com/stretchr/testify/require"
 )
 
@@ -216,6 +215,13 @@
 			Required:    true,
 		},
 	}
+	agent.OutputParameters = schema.Parameters{
+		{
+			Name:        "analyzer_list",
+			Description: "分析结果",
+			Type:        schema.TypeArray,
+		},
+	}
 	agent.Model = &schema.ModelConfig{Type: llm.TypeChat, Model: dbapp.QWEN4}
 	agent.Entry = "node0"
 	agent.Nodes = make([]*schema.Node, 0)
@@ -259,13 +265,12 @@
 			{ID: "a4fdff9f-b318-4688-8af0-e0323ed38ca4"},
 			{ID: "fe701789-edb2-4f2e-be26-c95fbefcf430"},
 		},
-		ResultKey: "output_list",
-		Timeout:   120,
+		ResultKey: "analyzer_list",
+		Timeout:   600,
 		Iteration: true,
 		IterationValues: []schema.RangeValue{
 			{ListName: "steps", DestName: "step"},
 		},
-		LogMemory: true,
 	})
 
 	agent.Nodes = append(agent.Nodes, &schema.Node{
@@ -273,15 +278,26 @@
 		Name:      "compile",
 		Type:      schema.NodeTypeLLMBasic,
 		ResultKey: schema.DefaultResultKey,
-		Timeout:   120,
-		UserPromptTemplate: `现在所有取证已完成，请总结[分析步骤记录]，输出详细的分析汇总，如下第一人称内容风格：
+		Timeout:   600,
+		UserPromptTemplate: `
+[告警日志]
+{{.alarm_log}}
 
-		在完成了所有必要的取证工作之后，我仔细地对这次安全告警事件进行了全面的分析汇总。我的发现如下：  
-		1 非工作时间的活动特征：我注意到这次告警是在非工作时间发生的，这与蠕虫病毒活动的常见特征非常吻合，从而提高了我对主机被侵害风险的评估。 
-		2 ...
-  
-		综合我以上的分析结果，并结合告警类型的特定特征以及基于研判知识库的评估，我得出结论：这次告警极具蠕虫病毒的行为特征，因此判断受影响设备的安全威胁等级较高。因此，我强烈建议立即采取一系列应对措施，包括但不限于隔离受影响主机、进行病毒清除、修补已识别的系统安全漏洞等，以有效遏制病毒进一步扩散并减轻潜在的影响。`,
-		UseMemory: true,
+[分析结果]
+{{ .analyzer_list }}
+
+
+汇总以上所有的分析过程，提取针对本次蠕虫病毒告警的[关键信息]，[关键信息]维度需包含如下字段，并且按照对应字段要求输出内容
+1. [可疑源IP]包含可疑ip。
+2. [可疑域名]包含可疑域名。
+3. [可疑文件]包含文件名称、文件路径。
+4. [可疑行为]包含篡改注册表、修改启动项、定时任务、文件删除等各类危险操作。
+5. [告警类型] 蠕虫病毒
+6. [受害者操作系统]包含受害者操作系统类型，若无法判断则为“linux，windows”
+7. [攻击payload]包含漏洞利用的关键载荷。
+8. [漏洞url]包含漏洞利用入口的url地址，例如“https://example.com”
+9. [攻击是否成功]表示本次攻击情况，为“成功”或“失败”
+10. [开发语言]表示漏洞产生的代码开发语言，若无法判断则为“未知”`,
 	})
 
 	agent.Edges = append(agent.Edges, &schema.Edge{
@@ -329,206 +345,124 @@
 	fmt.Println(string(data))
 }
 
-func TestFCTD(t *testing.T) {
+func TestSumReporter(t *testing.T) {
 	var agent schema.AgentConfig
-	agent.ID = "bf87a582-abe7-4f0b-a798-6ba8e15c7515"
-	agent.Name = "云沙箱"
-	agent.Description = "上传样本，使用安恒云沙箱进行动态分析"
-	agent.InputParameters = schema.Parameters{}
-	agent.Entry = "node1"
-
-	agent.Nodes = []*schema.Node{
+	agent.ID = "41f05a28-522c-4e73-b85e-b5b1903632de"
+	agent.Name = "安全告警分析专家"
+	agent.Description = "作为安全告警分析专家，您熟悉安全告警分析以及取证流程，您将接收到一份日志告警信息，请按照要求对日志进行分析"
+	agent.Model = &schema.ModelConfig{Type: llm.TypeChat, Model: dbapp.QWEN4}
+	agent.Entry = "node0"
+	agent.InputParameters = []schema.Parameter{
 		{
-			ID:   "node1",
-			Name: "生成Token",
-			Type: schema.NodeTypeActTool,
-			Tools: []schema.Component{
-				{
-					ID: "5c1e41d0-91d4-4082-88cd-35a30a33e01f",
-					Options: map[string]any{
-						"code": `import time
-import hashlib
-
-token = inputs["FCTD-Token"]
-timestamp = str(int(time.time()))
-raw_string = f"{token}_{timestamp}_dbapp@FCTD2022"
-md5_hash = hashlib.md5(raw_string.encode('utf-8')).hexdigest()
-checksum = f"{timestamp}{md5_hash}"
-print(checksum)`,
-					},
-				},
-			},
-			ResultKey: schema.DefaultResultKey,
-			ResultParsers: []schema.ParseConfig{
-				{
-					Type:       schema.ParserRegex,
-					Expression: `[a-f0-9]{42}`,
-					Key:        "FCTD-Sign",
-				},
-			},
-		},
-		{
-			ID:   "node2",
-			Name: "上传样本",
-			Type: schema.NodeTypeActTool,
-			Tools: []schema.Component{
-				{
-					ID: "97155a96-f134-4d51-a839-8b389d0f7b3f",
-				},
-			},
-			ResultParsers: []schema.ParseConfig{
-				{
-					Type:       schema.ParserRegex,
-					Expression: `[a-f0-9]{32}`,
-					Key:        "taskId",
-				},
-			},
-		},
-		{
-			ID:   "node3",
-			Name: "查询分析状态",
-			Type: schema.NodeTypeActTool,
-			Tools: []schema.Component{
-				{
-					ID: "595ff4db-fb9e-4666-b779-5882188fb7b4",
-				},
-			},
-			ResultParsers: []schema.ParseConfig{
-				{
-					Type:       schema.ParserJsonpath,
-					Expression: `$data.status`,
-					Key:        "status",
-				},
-			},
-			Pending: 10,
-		},
-		{
-			ID:   "node4",
-			Name: "获取分析结果",
-			Type: schema.NodeTypeActTool,
-			Tools: []schema.Component{
-				{
-					ID: "3849b4b6-5e13-4824-bcdb-ba00af902b9f",
-				},
-			},
-			ResultParsers: []schema.ParseConfig{
-				{
-					Type:       schema.ParserJsonpath,
-					Expression: `$intelligences`,
-					Key:        "intelligences",
-				},
-			},
-		},
-	}
+			Name:        "alarm_log",
+			Description: "告警信息输入",
+			Type:        schema.TypeString,
+			Required:    true,
+		},
+	}
+	agent.Nodes = make([]*schema.Node, 0)
+	agent.Edges = make([]*schema.Edge, 0)
+	agent.Entry = "node0"
+	agent.Nodes = append(agent.Nodes, &schema.Node{
+		ID:                   "node0",
+		Name:                 "sumUp",
+		Type:                 schema.NodeTypeLLMBasic,
+		ResultKey:            schema.DefaultResultKey,
+		SystemPromptTemplate: ``,
+		UserPromptTemplate: `
 
-	agent.Edges = []*schema.Edge{
-		{
-			SrcNode: "node1",
-			DstNode: "node2",
-		},
-		{
-			SrcNode: "node2",
-			DstNode: "node3",
-		},
-		{
-			SrcNode:   "node3",
-			DstNode:   "node3",
-			Condition: "status == 0 or status == 1",
-		},
-		{
-			SrcNode:   "node3",
-			DstNode:   "node4",
-			Condition: "status == 2",
-		},
-	}
 
-	data, err := json.Marshal(agent)
-	assert.NoError(t, err)
+汇总以上所有的分析过程，提取针对本次蠕虫病毒告警的[关键信息]，[关键信息]维度需包含如下字段，并且按照对应字段要求输出内容
+1. [可疑源IP]包含可疑ip。
+2. [可疑域名]包含可疑域名。
+3. [可疑文件]包含文件名称、文件路径。
+4. [可疑行为]包含恶意域名回连特征、文件特征、系统篡改特征、内网横向攻击、对硬件资源消耗特征以及特征具体内容。
+5. [告警类型] 蠕虫病毒
+6. [受害者操作系统]包含受害者操作系统类型，若无法判断则为“linux，windows”
+7. [攻击payload]包含漏洞利用的关键载荷。
+8. [漏洞url]包含漏洞利用入口的url地址，例如“https://example.com”
+9. [攻击是否成功]表示本次攻击情况，为“成功”或“失败”
+10. [开发语言]表示漏洞产生的代码开发语言，若无法判断则为“未知”`,
+	})
+
+	data, _ := json.Marshal(&agent)
 	fmt.Println(string(data))
-
-	var toolkit schema.Toolkit
-	toolkit.ID = "6c76a454-e0e8-4f0f-91ce-c31a00e77d1a"
-	toolkit.Name = "云查"
-	toolkit.Description = "云查API，提供样本分析能力"
-	toolkit.Url = "https://fc.dbappsecurity.com.cn"
-	toolkit.Parameters = schema.Parameters{
-		{
-			Name:        "FCTD-Token",
-			Description: "Token",
-			Type:        schema.TypeString,
-			Required:    true,
-			Method:      schema.MethodHeader,
-		},
-		{
-			Name:        "FCTD-Sign",
-			Description: "校验码",
-			Type:        schema.TypeString,
-			Required:    true,
-			Method:      schema.MethodHeader,
-		},
-	}
+}
 
-	toolkit.Tools = []schema.ToolConfig{
+func TestRiskDetection(t *testing.T) {
+	var agent schema.AgentConfig
+	agent.ID = "9738c948-922f-4300-94ef-b8e354e9be44"
+	agent.Name = "安全告警分析专家-web攻击分析"
+	agent.Description = "作为安全告警分析专家，您熟悉安全告警分析以及取证流程，您将接收到一份告警聚合日志。请进行调查取证分析。"
+	agent.Version = "v1.0.1"
+	agent.Model = &schema.ModelConfig{Type: llm.TypeChat, Model: dbapp.QWEN4}
+	agent.Entry = "node0"
+	agent.InputParameters = []schema.Parameter{
 		{
-			ID:          "97155a96-f134-4d51-a839-8b389d0f7b3f",
-			Name:        "沙箱分析",
-			Description: "上传样本文件进行沙箱动态分析",
-			InputParameters: schema.Parameters{
-				{
-					Name:        "file",
-					Description: "上传的文件ID",
-					Type:        schema.TypeFile,
-					Required:    true,
-					Method:      schema.MethodBody,
-				},
-			},
-			Method: "POST",
-			Path:   "/fctd/v1/advanced/sandbox_upload_analysis",
-		},
-		{
-			ID:          "595ff4db-fb9e-4666-b779-5882188fb7b4",
-			Name:        "沙箱任务状态查询",
-			Description: "根据任务ID，查询沙箱分析状态",
-			InputParameters: schema.Parameters{
-				{
-					Name:        "taskId",
-					Description: "任务ID",
-					Type:        schema.TypeString,
-					Required:    true,
-					Method:      schema.MethodQuery,
-				},
-			},
-			Method: "GET",
-			Path:   "/fctd/v1/advanced/get_task_status",
+			Name:        "requestMessage",
+			Description: "请求报文",
+			Type:        schema.TypeString,
+			Required:    true,
 		},
 		{
-			ID:          "3849b4b6-5e13-4824-bcdb-ba00af902b9f",
-			Name:        "沙箱结果获取",
-			Description: "根据任务ID，查询沙箱分析结果报告",
-			InputParameters: schema.Parameters{
-				{
-					Name:        "taskId",
-					Description: "任务ID",
-					Type:        schema.TypeString,
-					Required:    true,
-					Method:      schema.MethodQuery,
-				},
-				{
-					Name:         "type",
-					Description:  "报告类型",
-					Type:         schema.TypeString,
-					Required:     true,
-					Configed:     true,
-					DefaultValue: "0",
-					Method:       schema.MethodQuery,
-				},
+			Name:        "responseMessage",
+			Description: "响应报文",
+			Type:        schema.TypeString,
+			Required:    true,
+		},
+	}
+	agent.OutputParameters = []schema.Parameter{
+		{
+			Name:        "alarm",
+			Description: "攻击告警",
+			Type:        schema.TypeObject,
+			Required:    true,
+		},
+	}
+	agent.Nodes = make([]*schema.Node, 0)
+	agent.Edges = make([]*schema.Edge, 0)
+	agent.Nodes = append(agent.Nodes, &schema.Node{
+		ID:   "node0",
+		Name: "攻击意图研判",
+		Type: schema.NodeTypeActTool,
+		Tools: []schema.Component{
+			{
+				ID: "c2f342ff-32b1-448f-a820-08625fa06797",
 			},
-			Method: "GET",
-			Path:   "/fctd/v1/advanced/get_sandbox_result",
 		},
-	}
+		ResultKey: schema.DefaultResultKey,
+		ResultParsers: []schema.ParseConfig{
+			{
+				Type:       schema.ParserJsonpath,
+				Key:        "alarm",
+				Expression: "$.data",
+				TextKey:    "output",
+			},
+		},
+	})
 
-	data, err = json.Marshal(toolkit)
-	assert.NoError(t, err)
+	agent.Nodes = append(agent.Nodes, &schema.Node{
+		ID:                   "node1",
+		Name:                 "sumUp",
+		Type:                 schema.NodeTypeLLMBasic,
+		ResultKey:            schema.DefaultResultKey,
+		SystemPromptTemplate: ``,
+		UserPromptTemplate: `你将获得一份告警日志，你需要按照下面顺序协助完成告警日志的调查取证。
+        1、研判告警日志属于【误报、真实攻击、无法判断】中的哪一类，识别依据参考如下;
+            a、如果告警日志无法研判，尝试告警中的http报文存在明确的攻击行为，则该告警属于真实攻击
+            b、如果告警日志无法研判，尝试同一个ip触发了不同的类型的告警则该告警属于真实攻击否则同一个ip其中一个报文研判如果是正常的并且威胁情报也是正常的，则该告警属于误报；
+        2、如果告警日志属于真实攻击，需要识别告警的攻击类型;
+        3、如果告警日志属于真实攻击，需要识别攻击payload，以及对攻击payload的解读？
+        4、如果告警日志属于真实攻击，需要识别是否攻击成功？
+        5、识别主机资产归属人;
+        6、如果失陷给出对应的失陷概率百分比；
+        7、如果失陷，需要提供对应的临时解决方案;`,
+	})
+
+	data, _ := json.Marshal(&agent)
 	fmt.Println(string(data))
 }
+
+func TestAPIRisk(t *testing.T) {
+
+}
