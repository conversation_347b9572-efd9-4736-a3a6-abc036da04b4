Index: pkg/license/license.go
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>package license\n\nimport (\n\t\"encoding/json\"\n\t\"errors\"\n\t\"sync\"\n\t\"time\"\n\n\t\"secwalk/internal/domain/core/endpoint\"\n\t\"secwalk/internal/domain/core/schema\"\n\t\"secwalk/pkg/logger\"\n\n\t\"github.com/sirupsen/logrus\"\n)\n\nvar (\n\tErrIncorrectLicense = errors.New(\"incorrect license\")\n\tErrLicenseExpired   = errors.New(\"license expired\")\n\tErrLicenseNotFound  = errors.New(\"license not found\")\n\tErrLicenseType      = errors.New(\"license type not supported\")\n)\n\ntype LicenseProxy struct {\n\tmux sync.RWMutex\n\n\tlicense *License\n}\n\nfunc New() *LicenseProxy {\n\tlicense := &LicenseProxy{\n\t\tlicense: &License{\n\t\t\tModel: Model{Key: \"123\"},\n\t\t\tAgent: Agent{\n\t\t\t\tLicenses: []AgentLicense{},\n\t\t\t},\n\t\t},\n\t}\n\n\tgo license.refresh()\n\treturn license\n}\n\n// 获取智能体授权配置\nfunc (l *LicenseProxy) GetAgentKey(_type int, id string) (string, error) {\n\tl.mux.RLock()\n\tdefer l.mux.RUnlock()\n\n\tswitch _type {\n\tcase schema.TypeUser:\n\t\t// 用户类型智能体，默认使用模型加密密钥，且拥有所有操作权限\n\t\treturn l.license.Model.Key, nil\n\n\tcase schema.TypePerm:\n\t\t// 授权类型智能体\n\t\t// 在授权模式下，拥有所有操作权限，但是若没有找到授权ID对于的配置，直接报错\n\t\t// 在非授权模式下，按照授权配置限制操作\n\t\tfor _, item := range l.license.Agent.Licenses {\n\t\t\tif id == item.ID {\n\t\t\t\treturn item.Key, nil\n\t\t\t}\n\t\t}\n\t}\n\n\treturn \"\", ErrLicenseNotFound\n}\n\nfunc (l *LicenseProxy) refresh() {\n\tfor {\n\t\tlicense, err := l.fetch()\n\t\tif err != nil {\n\t\t\tlogrus.WithField(logger.KeyCategory, logger.CategorySystem).\n\t\t\t\tWarnf(\"license fetch error: %s\", err)\n\t\t} else {\n\t\t\tl.mux.Lock()\n\t\t\tif license != nil {\n\t\t\t\tl.license = license\n\t\t\t}\n\t\t\tl.mux.Unlock()\n\t\t}\n\n\t\ttime.Sleep(5 * time.Minute)\n\t}\n}\n\ntype License struct {\n\tModel Model `json:\"model\"`\n\tAgent Agent `json:\"agent\"`\n}\n\ntype Model struct {\n\tKey string `json:\"key\"`\n}\n\ntype Agent struct {\n\tLicenses []AgentLicense `json:\"authorizations\"`\n}\n\ntype AgentLicense struct {\n\tID   string `json:\"id\"`\n\tName string `json:\"name\"`\n\tKey  string `json:\"key\"`\n}\n\nfunc (l *LicenseProxy) fetch() (*License, error) {\n\ttype response struct {\n\t\tCode int      `json:\"code\"`\n\t\tMsg  string   `json:\"msg\"`\n\t\tData *License `json:\"data\"`\n\t}\n\n\tdata, err := endpoint.GetLicense()\n\tif err != nil {\n\t\treturn nil, err\n\t}\n\n\tret := &response{}\n\tif err := json.Unmarshal(data, ret); err != nil {\n\t\treturn nil, err\n\t}\n\n\tif ret.Code != 0 {\n\t\treturn nil, ErrIncorrectLicense\n\t}\n\n\treturn ret.Data, nil\n}\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/pkg/license/license.go b/pkg/license/license.go
--- a/pkg/license/license.go	
+++ b/pkg/license/license.go	
@@ -29,7 +29,7 @@
 func New() *LicenseProxy {
 	license := &LicenseProxy{
 		license: &License{
-			Model: Model{Key: "123"},
+			Model: Model{Key: "aa675b38-ff66-4848-829b-693146ae790a"},
 			Agent: Agent{
 				Licenses: []AgentLicense{},
 			},
