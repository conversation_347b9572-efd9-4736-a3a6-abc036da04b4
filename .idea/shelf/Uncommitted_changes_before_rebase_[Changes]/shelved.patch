Index: internal/domain/core/schema/parameter.go
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>package schema\n\nimport (\n\t\"errors\"\n\t\"fmt\"\n\t\"strconv\"\n\t\"unicode/utf8\"\n)\n\nconst (\n\tTypeString  = \"string\"\n\tTypeInteger = \"integer\"\n\tTypeBoolean = \"boolean\"\n\tTypeFloat   = \"float\"\n\tTypeArray   = \"array\"\n\tTypeObject  = \"object\"\n\tTypeFile    = \"file\"\n)\n\nfunc supportParameterType(s string) bool {\n\tswitch s {\n\tcase TypeString, TypeInteger, TypeBoolean,\n\t\tTypeFloat, TypeArray, TypeObject, TypeFile:\n\t\treturn true\n\tdefault:\n\t\treturn false\n\t}\n}\n\nconst (\n\tLocationBody   = \"body\"\n\tLocationPath   = \"path\"\n\tLocationQuery  = \"query\"\n\tLocationHeader = \"header\"\n)\n\nfunc supportParameterLocation(s string) bool {\n\tswitch s {\n\tcase LocationBody, LocationPath,\n\t\tLocationQuery, LocationHeader, \"\":\n\t\treturn true\n\tdefault:\n\t\treturn false\n\t}\n}\n\ntype Parameters []Parameter\n\nfunc (ps Parameters) Verify(list bool) error {\n\tfor _, p := range ps {\n\t\t// 校验参数名称\n\t\tif utf8.RuneCountInString(p.Name) < 2 || utf8.RuneCountInString(p.Name) > 128 {\n\t\t\treturn fmt.Errorf(\"参数名称长度要求2~128个字符: %s\", p.Name)\n\t\t}\n\n\t\t// 校验参数描述\n\t\tif utf8.RuneCountInString(p.Description) < 2 || utf8.RuneCountInString(p.Description) > 1024 {\n\t\t\treturn fmt.Errorf(\"参数描述长度要求2~1024个字符: %s\", p.Name)\n\t\t}\n\n\t\t// 校验参数类型\n\t\tif !supportParameterType(p.Type) {\n\t\t\treturn fmt.Errorf(\"参数类型无效: %s\", p.Name)\n\t\t}\n\n\t\tif !supportParameterLocation(p.Location) {\n\t\t\treturn fmt.Errorf(\"参数位置无效: %s\", p.Name)\n\t\t}\n\n\t\tif p.DefaultValue != nil {\n\t\t\tswitch p.Type {\n\t\t\tcase TypeString, TypeFile:\n\t\t\t\tif _, ok := p.DefaultValue.(string); !ok {\n\t\t\t\t\treturn fmt.Errorf(\"参数默认值必须为字符串: %s\", p.Name)\n\t\t\t\t}\n\t\t\tcase TypeInteger:\n\t\t\t\tif _, ok := p.DefaultValue.(float64); !ok {\n\t\t\t\t\treturn fmt.Errorf(\"参数默认值必须为整型: %s\", p.Name)\n\t\t\t\t}\n\t\t\tcase TypeBoolean:\n\t\t\t\tif _, ok := p.DefaultValue.(bool); !ok {\n\t\t\t\t\treturn fmt.Errorf(\"参数默认值必须为布尔型: %s\", p.Name)\n\t\t\t\t}\n\t\t\tcase TypeFloat:\n\t\t\t\tif _, ok := p.DefaultValue.(float64); !ok {\n\t\t\t\t\treturn fmt.Errorf(\"参数默认值必须为浮点型: %s\", p.Name)\n\t\t\t\t}\n\t\t\tcase TypeArray:\n\t\t\t\tif _, ok := p.DefaultValue.([]any); !ok {\n\t\t\t\t\treturn fmt.Errorf(\"参数默认值必须为列表: %s\", p.Name)\n\t\t\t\t}\n\t\t\tcase TypeObject:\n\t\t\t\tif _, ok := p.DefaultValue.(map[string]any); !ok {\n\t\t\t\t\treturn fmt.Errorf(\"参数默认值必须为对象: %s\", p.Name)\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tif err := p.SubParamters.Verify(p.Type == TypeArray); err != nil {\n\t\t\treturn err\n\t\t}\n\t}\n\treturn nil\n}\n\nfunc (ps Parameters) GetValueFromInputs(inputs map[string]any, key string) (Value, error) {\n\tfor _, p := range ps {\n\t\tif p.Name != key {\n\t\t\tcontinue\n\t\t}\n\n\t\tinput, ok := inputs[key]\n\t\tif ok {\n\t\t\treturn Value{input}, nil\n\t\t}\n\n\t\tif p.DefaultValue != nil {\n\t\t\treturn Value{p.DefaultValue}, nil\n\t\t}\n\t}\n\n\treturn Value{}, fmt.Errorf(\"value not found: %s\", key)\n}\n\nfunc (ps Parameters) GetValue(key string) (Value, error) {\n\tfor _, p := range ps {\n\t\tif p.Name != key {\n\t\t\tcontinue\n\t\t}\n\n\t\tif p.DefaultValue != nil {\n\t\t\treturn Value{p.DefaultValue}, nil\n\t\t}\n\t}\n\treturn Value{}, fmt.Errorf(\"value not found: %s\", key)\n}\n\ntype Parameter struct {\n\tName         string     `json:\"name,omitempty\"`          // 参数名称\n\tDescription  string     `json:\"description\"`             // 参数描述\n\tType         string     `json:\"type\"`                    // 参数类型\n\tConfiged     bool       `json:\"configed,omitempty\"`      // 用户配置\n\tRequired     bool       `json:\"required,omitempty\"`      // 是否必须\n\tDefaultValue any        `json:\"default_value,omitempty\"` // 参数默认值\n\tExampleValue any        `json:\"example_value,omitempty\"` // 参数样例值\n\tLocation     string     `json:\"location,omitempty\"`      // 参数位置，仅HTTP请求参数使用\n\tSubParamters Parameters `json:\"sub_paramters,omitempty\"` // 子参数\n}\n\nfunc (p Parameter) VerifyValueType(v any) error {\n\tswitch p.Type {\n\tcase TypeString:\n\t\tif _, ok := v.(string); !ok {\n\t\t\treturn fmt.Errorf(\"parameter '%s' is not string\", p.Name)\n\t\t}\n\tcase TypeInteger:\n\t\tif _, ok := v.(float64); !ok {\n\t\t\treturn fmt.Errorf(\"parameter '%s' is not integer\", p.Name)\n\t\t}\n\tcase TypeBoolean:\n\t\tif _, ok := v.(bool); !ok {\n\t\t\treturn fmt.Errorf(\"parameter '%s' is not boolean\", p.Name)\n\t\t}\n\tcase TypeFloat:\n\t\tif _, ok := v.(float64); !ok {\n\t\t\treturn fmt.Errorf(\"parameter '%s' is not float\", p.Name)\n\t\t}\n\tcase TypeArray:\n\t\tif _, ok := v.([]any); !ok {\n\t\t\treturn fmt.Errorf(\"parameter '%s' is not array\", p.Name)\n\t\t}\n\tcase TypeObject:\n\t\tif _, ok := v.(map[string]any); !ok {\n\t\t\treturn fmt.Errorf(\"parameter '%s' is not object\", p.Name)\n\t\t}\n\t}\n\treturn nil\n}\n\nfunc (p Parameter) GetResultValue(vs map[string]any) (any, error) {\n\tfor k, v := range vs {\n\t\tif k == p.Name {\n\t\t\tswitch p.Type {\n\t\t\tcase TypeInteger:\n\t\t\t\tif _, ok := v.(string); ok {\n\t\t\t\t\treturn strconv.Atoi(v.(string))\n\t\t\t\t}\n\t\t\tcase TypeBoolean:\n\t\t\t\tif _, ok := v.(string); ok {\n\t\t\t\t\treturn strconv.ParseBool(v.(string))\n\t\t\t\t}\n\t\t\tcase TypeFloat:\n\t\t\t\tif _, ok := v.(string); ok {\n\t\t\t\t\treturn strconv.ParseFloat(v.(string), 64)\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn v, nil\n\t\t}\n\t}\n\n\treturn nil, errors.New(\"key value not found\")\n}\n\ntype Value struct {\n\tany\n}\n\nfunc (v *Value) Set(val any) {\n\tv.any = val\n}\n\nfunc (v *Value) V() any {\n\treturn v.any\n}\n\nfunc (v *Value) String() string {\n\tswitch v.any.(type) {\n\tcase string:\n\t\treturn v.any.(string)\n\tcase int:\n\t\treturn strconv.Itoa(v.any.(int))\n\tcase bool:\n\t\treturn strconv.FormatBool(v.any.(bool))\n\tcase float32:\n\t\treturn strconv.FormatFloat(v.any.(float64), 'f', -1, 32)\n\tcase float64:\n\t\treturn strconv.FormatFloat(v.any.(float64), 'f', -1, 64)\n\tdefault:\n\t\treturn \"\"\n\t}\n}\n\nfunc (v *Value) IsString() bool {\n\t_, ok := v.any.(string)\n\treturn ok\n}\n\nfunc (v *Value) Int() int {\n\tswitch v.any.(type) {\n\tcase string:\n\t\ti, _ := strconv.Atoi(v.any.(string))\n\t\treturn i\n\tcase int:\n\t\treturn v.any.(int)\n\tcase float32:\n\t\treturn int(v.any.(float32))\n\tcase float64:\n\t\treturn int(v.any.(float64))\n\tdefault:\n\t\treturn 0\n\t}\n}\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/internal/domain/core/schema/parameter.go b/internal/domain/core/schema/parameter.go
--- a/internal/domain/core/schema/parameter.go	(revision b3dfdceec260d6f61ee80516b2bd4311de744179)
+++ b/internal/domain/core/schema/parameter.go	(date 1716975089995)
@@ -3,7 +3,9 @@
 import (
 	"errors"
 	"fmt"
+	"regexp"
 	"strconv"
+	"strings"
 	"unicode/utf8"
 )
 
@@ -250,3 +252,25 @@
 		return 0
 	}
 }
+
+func (v *Value) GetActualParameter(key string, input map[string]any) any {
+	pars := strings.Split(key, ".")
+	if len(pars) < 0 {
+		return ""
+	}
+
+	re := regexp.MustCompile(`[a-zA-Z0-9_]+`)
+	matches := re.FindAllStringSubmatch(key, -1)
+	fmt.Println(matches)
+
+	var keys any
+	for _, val := range matches {
+		if _, ok := input[val[0]]; !ok {
+			return ""
+		}
+
+		keys = input[val[0]]
+	}
+
+	return keys
+}
Index: internal/domain/core/agent/agent_test.go
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>package agent\n\nimport (\n\t\"encoding/json\"\n\t\"fmt\"\n\t\"secwalk/internal/domain/core/schema\"\n\t\"secwalk/internal/domain/core/sqloader\"\n\t\"testing\"\n\n\t\"github.com/stretchr/testify/assert\"\n\t\"github.com/stretchr/testify/require\"\n)\n\nfunc TestNewAgent(t *testing.T) {\n\tvar agent schema.AgentConfig\n\tagent.ID = \"2ee56d84-35f1-4e42-ac4f-0039bc41f180\"\n\tagent.Name = \"工具调用测试\"\n\tagent.Description = \"工具调用测试\"\n\tagent.InputParameters = schema.Parameters{}\n\tagent.Nodes = make([]*schema.Node, 0)\n\tagent.Edges = make([]*schema.Edge, 0)\n\n\tagent.Nodes = append(agent.Nodes,\n\t\t&schema.Node{\n\t\t\tID:   \"node\",\n\t\t\tName: \"mixup_tool\",\n\t\t\tType: schema.NodeTypeLLMMixup,\n\t\t\tTools: []schema.Component{\n\t\t\t\t{\n\t\t\t\t\tID: \"6a1ee208-fa8e-4ec4-9e9e-912bd9d4e3ed\",\n\t\t\t\t\tOptions: map[string]any{\n\t\t\t\t\t\t\"eval\": \"2+2\",\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t},\n\t\t\tDB: &schema.DB{\n\t\t\t\tType: sqloader.DialectSqlite3,\n\t\t\t\tDSN:  \"C:/Users/<USER>/Desktop/SQLITEDB/SQLITEDB.db\",\n\t\t\t\tTables: []sqloader.Table{\n\t\t\t\t\t{\n\t\t\t\t\t\tName:        \"spiders\",\n\t\t\t\t\t\tDescription: \"勒索软件相关知识库\",\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tName:        \"decryptions\",\n\t\t\t\t\t\tDescription: \"勒索软件解密工具库\",\n\t\t\t\t\t\tFields: []sqloader.Field{\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tName:    \"family\",\n\t\t\t\t\t\t\t\tComment: \"家族\",\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tName:    \"decrypted_lnk\",\n\t\t\t\t\t\t\t\tComment: \"解密链接\",\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t},\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t},\n\t\t\tResultKey:  schema.DefaultResultKey,\n\t\t\tTimeout:    schema.DefaultTimeout,\n\t\t\tMaxRetries: schema.DefaultMaxRetries,\n\t\t},\n\t)\n\tagent.Nodes = append(agent.Nodes,\n\t\t&schema.Node{\n\t\t\tID:   \"node0\",\n\t\t\tName: \"act_tool\",\n\t\t\tType: schema.NodeTypeActTool,\n\t\t\tTools: []schema.Component{\n\t\t\t\t{\n\t\t\t\t\tID: \"6a1ee208-fa8e-4ec4-9e9e-912bd9d4e3ed\",\n\t\t\t\t\tOptions: map[string]any{\n\t\t\t\t\t\t\"eval\": \"2+2\",\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t},\n\t\t\tResultKey:  schema.DefaultResultKey,\n\t\t\tTimeout:    schema.DefaultTimeout,\n\t\t\tMaxRetries: schema.DefaultMaxRetries,\n\t\t},\n\t)\n\tagent.Nodes = append(agent.Nodes,\n\t\t&schema.Node{\n\t\t\tID:              \"node1\",\n\t\t\tName:            \"react_tool\",\n\t\t\tType:            schema.NodeTypeLLMReact,\n\t\t\tInputExpression: \"{{.input}}\",\n\t\t\tTools: []schema.Component{\n\t\t\t\t{\n\t\t\t\t\tID: \"6a1ee208-fa8e-4ec4-9e9e-912bd9d4e3ed\",\n\t\t\t\t},\n\t\t\t},\n\t\t\tResultKey:  schema.DefaultResultKey,\n\t\t\tTimeout:    schema.DefaultTimeout,\n\t\t\tMaxRetries: schema.DefaultMaxRetries,\n\t\t},\n\t)\n\tagent.Nodes = append(agent.Nodes,\n\t\t&schema.Node{\n\t\t\tID:              \"node2\",\n\t\t\tName:            \"llm_tool\",\n\t\t\tType:            schema.NodeTypeLLMTools,\n\t\t\tInputExpression: \"{{.input}}\",\n\t\t\tTools: []schema.Component{\n\t\t\t\t{\n\t\t\t\t\tID: \"6a1ee208-fa8e-4ec4-9e9e-912bd9d4e3ed\",\n\t\t\t\t},\n\t\t\t},\n\t\t\tResultKey:  schema.DefaultResultKey,\n\t\t\tTimeout:    schema.DefaultTimeout,\n\t\t\tMaxRetries: schema.DefaultMaxRetries,\n\t\t},\n\t)\n\n\tagent.Edges = append(agent.Edges, &schema.Edge{\n\t\tSrcNode: \"node\",\n\t\tDstNode: \"node0\",\n\t})\n\tagent.Edges = append(agent.Edges, &schema.Edge{\n\t\tSrcNode: \"node0\",\n\t\tDstNode: \"node1\",\n\t})\n\tagent.Edges = append(agent.Edges, &schema.Edge{\n\t\tSrcNode: \"node1\",\n\t\tDstNode: \"node2\",\n\t})\n\tagent.Entry = \"node\"\n\n\tdata, err := json.Marshal(&agent)\n\trequire.NoError(t, err)\n\n\tfmt.Println(string(data))\n}\n\nfunc TestMultiAgents(t *testing.T) {\n\tvar master, branch schema.AgentConfig\n\n\tbranch.ID = \"954443d6-1418-4257-9cec-3f4dc8c99c70\"\n\tbranch.Name = \"网络安全专家\"\n\tbranch.Description = \"拥有丰富的网络安全领域知识，能回答一切相关的问题。\"\n\tbranch.InputParameters = schema.Parameters{\n\t\t{\n\t\t\tName:        \"input\",\n\t\t\tDescription: \"网络安全相关的问题\",\n\t\t\tType:        schema.TypeString,\n\t\t\tRequired:    true,\n\t\t},\n\t}\n\tbranch.Nodes = make([]*schema.Node, 0)\n\tbranch.Edges = make([]*schema.Edge, 0)\n\tbranch.Entry = \"node\"\n\tbranch.Nodes = append(branch.Nodes, &schema.Node{\n\t\tID:         \"node\",\n\t\tName:       \"小恒智聊\",\n\t\tType:       schema.NodeTypeLLMMixup,\n\t\tResultKey:  schema.DefaultResultKey,\n\t\tTimeout:    schema.DefaultTimeout,\n\t\tMaxRetries: schema.DefaultMaxRetries,\n\t})\n\n\tmaster.ID = \"90d2d3e5-2841-4202-a1e2-b924bb3768d2\"\n\tmaster.Name = \"leader\"\n\tmaster.Description = \"能够解决许多问题。\"\n\tmaster.InputParameters = schema.Parameters{\n\t\t{\n\t\t\tName:        \"input\",\n\t\t\tDescription: \"用户问题\",\n\t\t\tType:        schema.TypeString,\n\t\t\tRequired:    true,\n\t\t},\n\t}\n\tmaster.Nodes = make([]*schema.Node, 0)\n\tmaster.Edges = make([]*schema.Edge, 0)\n\tmaster.Entry = \"node\"\n\tmaster.Nodes = append(master.Nodes, &schema.Node{\n\t\tID:         \"node\",\n\t\tName:       \"回答\",\n\t\tType:       schema.NodeTypeLLMMixup,\n\t\tResultKey:  schema.DefaultResultKey,\n\t\tTimeout:    schema.DefaultTimeout,\n\t\tMaxRetries: schema.DefaultMaxRetries,\n\t\tAgents: []schema.Component{\n\t\t\t{ID: \"954443d6-1418-4257-9cec-3f4dc8c99c70\"},\n\t\t},\n\t})\n\n\td1, _ := json.Marshal(branch)\n\td2, _ := json.Marshal(master)\n\n\tfmt.Println(string(d1))\n\tfmt.Println(string(d2))\n}\n\nfunc TestRetrieval(t *testing.T) {\n\tvar agent schema.AgentConfig\n\tagent.ID = \"9a64aba9-8802-44fd-b4ff-32de4dbf953f\"\n\tagent.Name = \"文档检索\"\n\tagent.Description = \"文档检索文档检索文档检索\"\n\tagent.InputParameters = schema.Parameters{}\n\tagent.Nodes = make([]*schema.Node, 0)\n\tagent.Edges = make([]*schema.Edge, 0)\n\tagent.Entry = \"node1\"\n\n\tagent.Nodes = append(agent.Nodes, &schema.Node{\n\t\tID:        \"node1\",\n\t\tName:      \"文档检索\",\n\t\tType:      schema.NodeTypeLLMRetrievalDoc,\n\t\tResultKey: schema.DefaultResultKey,\n\t\tKBs: []schema.KB{\n\t\t\t{\n\t\t\t\tID:          \"1793119439886090241\",\n\t\t\t\tDescription: \"文档检索文档检索文档检索文档检索\",\n\t\t\t},\n\t\t},\n\t})\n\n\tdata, err := json.Marshal(agent)\n\tassert.NoError(t, err)\n\tfmt.Println(string(data))\n}\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/internal/domain/core/agent/agent_test.go b/internal/domain/core/agent/agent_test.go
--- a/internal/domain/core/agent/agent_test.go	(revision b3dfdceec260d6f61ee80516b2bd4311de744179)
+++ b/internal/domain/core/agent/agent_test.go	(date 1716965780178)
@@ -219,3 +219,106 @@
 	assert.NoError(t, err)
 	fmt.Println(string(data))
 }
+
+func TestFCTD2222(t *testing.T) {
+	var agent schema.AgentConfig
+	agent.ID = "bf87a582-abe7-4f0b-a798-6ba8e15c7515"
+	agent.Name = "新版本报文研判接口"
+	agent.Description = "根据用户输如，调用不同的接口"
+	agent.InputParameters = schema.Parameters{
+		schema.Parameter{
+			Name:        "tool_name",
+			Description: "插件名称",
+			Type:        schema.TypeString,
+			Required:    true,
+		},
+		{
+			Name:        "tool_parameters",
+			Description: "工具参数",
+			Type:        schema.TypeObject,
+			Required:    true,
+		},
+	}
+	agent.OutputParameters = schema.Parameters{
+		schema.Parameter{
+			Name:        "tool_output",
+			Description: "插件输出",
+			Type:        schema.TypeString,
+		},
+		{
+			Name:        "select_tool",
+			Description: "工具选择",
+			Type:        schema.TypeString,
+			Required:    true,
+		},
+	}
+	agent.Entry = "node1"
+	agent.Nodes = append(agent.Nodes, &schema.Node{
+
+		ID:   "node1",
+		Name: "选择工具",
+		Type: schema.NodeTypeActLogic,
+	})
+
+	agent.Nodes = append(agent.Nodes, &schema.Node{
+		ID:        "node2",
+		Name:      "威胁情报回连时序解读",
+		Type:      schema.NodeTypeActTool,
+		ResultKey: "tool_output",
+		Tools: []schema.Component{
+			{
+				ID: "131a3cb-996f-4f43-adc7-68282474f7a",
+				Options: map[string]any{
+					"ruleType":    "{{ $.tool_parameters.ruleType }}",
+					"name":        "{{ $.tool_parameters.name }}",
+					"appProtocol": "{{ $.tool_parameters.appProtocol }}",
+					"ioc":         "{{ $.tool_parameters.ioc}}",
+					"srcaddress":  "{{ $.tool_parameters.srcaddress }}",
+					"destaddress": "{{ $.tool_parameters.destaddress }}",
+					"windowSize":  "{{ $.tool_parameters.windowSize }}",
+					"visitTimes":  "{{ $.tool_parameters.visitTimes }}",
+					"judgeResult": "{{ $.tool_parameters.judgeResult }}",
+				},
+			},
+		},
+	})
+
+	agent.Nodes = append(agent.Nodes, &schema.Node{
+		ID:        "node3",
+		Name:      "威胁情报回连时序研判",
+		Type:      schema.NodeTypeActTool,
+		ResultKey: "tool_output",
+		Tools: []schema.Component{
+			{
+				ID: "68d0e3f2-cb39-4581-a4a0-e87229c5c170",
+				Options: map[string]any{
+					"ruleType":    "{{ $.tool_parameters.ruleType }}",
+					"name":        "{{ $.tool_parameters.name }}",
+					"appProtocol": "{{ $.tool_parameters.appProtocol }}",
+					"ioc":         "{{ $.tool_parameters.ioc }}",
+					"srcaddress":  "{{ $.tool_parameters.srcaddress }}",
+					"destaddress": "{{ $.tool_parameters.destaddress }}",
+					"windowSize":  "{{ $.tool_parameters.windowSize }}",
+					"visitTimes":  "{{ $.tool_parameters.visitTimes }}",
+				},
+			},
+		},
+	})
+
+	agent.Edges = []*schema.Edge{
+		{
+			SrcNode:   "node1",
+			DstNode:   "node2",
+			Condition: "tool_name == 'ti_timeseries_analyze'",
+		},
+		{
+			SrcNode:   "node1",
+			DstNode:   "node3",
+			Condition: "tool_name == 'ti_timeseries'",
+		},
+	}
+
+	data, err := json.Marshal(agent)
+	assert.NoError(t, err)
+	fmt.Println(string(data))
+}
Index: configs/dev.yaml
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+># 服务基本配置\nserver:\n  name: secwalk\n  address: :8998\n  version: v1.0.0\n\n# 日志配置\nlogger:\n  app: secwalk\n  level: debug\n  format: false\n  output: logs/secwalk.log\n  max_size: 20\n  max_age: 7\n  max_backup: 7\n\n# 服务注册配置\nregister:\n  type: consul\n  host: *************:8500\n  token: d9295a36-9f55-4580-b22d-71cfa5837f76\n  username:\n  password:\n\n# 服务发现配置\nservices:\n  backend:\n    type: direct\n    name: backend\n    host: ***********:9994\n    metas:\n      token: 9KsxdRjPXY95CP4WHSytwXW7QYhBrtEV\n  gateway:\n    type: direct\n    name: secwall\n    host: ************:8995\n  coder:\n    type: direct\n    name: secvirt\n    host: ***********:18503\n  hbrag:\n    type: direct\n    name: hb-rag\n    host: ***********:18504\n\n# 存储配置\nes:\n  addresses:\n    - http://*************:9200\n  username: elastic\n  password: keU06fLj3gV3mzOGInOLd9gZ3lU3VLaw\n\nminio:\n  host: ***********:9000\n  access_key: admin\n  secret_key: tYqV9s#bt3ADrMy\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/configs/dev.yaml b/configs/dev.yaml
--- a/configs/dev.yaml	(revision b3dfdceec260d6f61ee80516b2bd4311de744179)
+++ b/configs/dev.yaml	(date 1716905508844)
@@ -33,7 +33,7 @@
   gateway:
     type: direct
     name: secwall
-    host: ************:8995
+    host: ************:31702
   coder:
     type: direct
     name: secvirt
Index: internal/domain/core/template/templates_test.go
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>package template\n\nimport (\n\t\"encoding/json\"\n\t\"fmt\"\n\t\"os\"\n\t\"testing\"\n\n\t\"github.com/stretchr/testify/assert\"\n\t\"github.com/stretchr/testify/require\"\n)\n\nfunc TestInterpolateGoTemplate(t *testing.T) {\n\tt.Parallel()\n\n\ttype testCase struct {\n\t\tname           string\n\t\ttemplate       string\n\t\ttemplateValues map[string]any\n\t\texpected       string\n\t\terrValue       string\n\t}\n\n\ttestCases := []testCase{\n\t\t{\n\t\t\tname:           \"Single\",\n\t\t\ttemplate:       \"Hello {{ .key }}\",\n\t\t\ttemplateValues: map[string]any{\"key\": \"world\"},\n\t\t\texpected:       \"Hello world\",\n\t\t},\n\t\t{\n\t\t\tname:           \"Multiple\",\n\t\t\ttemplate:       \"Hello {{ .key1 }} and {{ .key2 }}\",\n\t\t\ttemplateValues: map[string]any{\"key1\": \"world\", \"key2\": \"universe\"},\n\t\t\texpected:       \"Hello world and universe\",\n\t\t},\n\t\t{\n\t\t\tname:           \"Nested\",\n\t\t\ttemplate:       \"Hello {{ .key1.key2 }}\",\n\t\t\ttemplateValues: map[string]any{\"key1\": map[string]any{\"key2\": \"world\"}},\n\t\t\texpected:       \"Hello world\",\n\t\t},\n\t}\n\n\tfor _, tc := range testCases {\n\t\ttc := tc\n\t\tt.Run(tc.name, func(t *testing.T) {\n\t\t\tt.Parallel()\n\n\t\t\tactual, err := interpolateGoTemplate(tc.template, tc.templateValues)\n\t\t\trequire.NoError(t, err)\n\t\t\tassert.Equal(t, tc.expected, actual)\n\t\t})\n\t}\n\n\terrTestCases := []testCase{\n\t\t{\n\t\t\tname:     \"ParseErrored\",\n\t\t\ttemplate: \"Hello {{{ .key1 }}\",\n\t\t\texpected: \"\",\n\t\t\terrValue: \"template: template:1: unexpected \\\"{\\\" in command\",\n\t\t},\n\t\t{\n\t\t\tname:     \"ExecuteErrored\",\n\t\t\ttemplate: \"Hello {{ .key1 .key2 }}\",\n\t\t\texpected: \"\",\n\t\t\terrValue: \"template: template:1:9: executing \\\"template\\\" at <.key1>: key1 is not a method but has arguments\",\n\t\t},\n\t}\n\n\tfor _, tc := range errTestCases {\n\t\ttc := tc\n\t\tt.Run(tc.name, func(t *testing.T) {\n\t\t\tt.Parallel()\n\n\t\t\t_, err := interpolateGoTemplate(tc.template, map[string]any{})\n\t\t\trequire.Error(t, err)\n\t\t\trequire.EqualError(t, err, tc.errValue)\n\t\t})\n\t}\n}\n\nfunc TestCheckValidTemplate(t *testing.T) {\n\tt.Parallel()\n\n\tt.Run(\"NoTemplateAvailable\", func(t *testing.T) {\n\t\tt.Parallel()\n\n\t\terr := CheckValidTemplate(\"Hello, {test}\", []string{\"test\"})\n\t\trequire.Error(t, err)\n\t\trequire.ErrorIs(t, err, ErrInvalidTemplateFormat)\n\t\trequire.EqualError(t, err, \"invalid template format, got: unknown, should be one of [go-template]\")\n\t})\n\n\tt.Run(\"TemplateErrored\", func(t *testing.T) {\n\t\tt.Parallel()\n\n\t\terr := CheckValidTemplate(\"Hello, {{{ test }}\", []string{\"test\"})\n\t\trequire.Error(t, err)\n\t\trequire.EqualError(t, err, \"template: template:1: unexpected \\\"{\\\" in command\")\n\t})\n\n\tt.Run(\"TemplateValid\", func(t *testing.T) {\n\t\tt.Parallel()\n\n\t\terr := CheckValidTemplate(\"Hello, {{ .test }}\", []string{\"test\"})\n\t\trequire.NoError(t, err)\n\t})\n}\n\nfunc TestRenderTemplate(t *testing.T) {\n\tt.Parallel()\n\n\tt.Run(\"TemplateAvailable\", func(t *testing.T) {\n\t\tt.Parallel()\n\n\t\tactual, err := RenderTemplate(\n\t\t\t\"Hello {{ .key }}\",\n\t\t\tmap[string]any{\n\t\t\t\t\"key\": \"world\",\n\t\t\t},\n\t\t)\n\t\trequire.NoError(t, err)\n\t\tassert.Equal(t, \"Hello world\", actual)\n\t})\n\n\tt.Run(\"TemplateNotAvailable\", func(t *testing.T) {\n\t\tt.Parallel()\n\n\t\t_, err := RenderTemplate(\n\t\t\t\"Hello {key}\",\n\t\t\tmap[string]any{\n\t\t\t\t\"key\": \"world\",\n\t\t\t},\n\t\t)\n\t\trequire.Error(t, err)\n\t\trequire.ErrorIs(t, err, ErrInvalidTemplateFormat)\n\t})\n}\n\n// {{- range .frame.classify -}}\n// {{- if eq .curr $.l1 -}}\n// {{- range .next -}}\n// {{- if eq .curr $.l2 -}}\n// {{- range .next -}}\n// {{- if .explain}}\n// {{$.l1}}-{{$.l2}}-{{.curr}}：{{.explain}}{{.fields}}\n// {{- else}}\n// {{$.l1}}-{{$.l2}}-{{.curr}}：\n// {{- range .next}}如{{.fields}}\n// {{- end -}}\n// {{- end -}}\n// {{- end -}}\n// {{- end -}}\n// {{- end -}}\n// {{- end -}}\n// {{- end -}}\n\nfunc TestRenderMultiMap(t *testing.T) {\n\n\tdata, err := os.ReadFile(\"../../testdata/dataclass/input.json\")\n\tif err != nil {\n\t\treturn\n\t}\n\n\tvar inputs map[string]any\n\tif err := json.Unmarshal(data, &inputs); err != nil {\n\t\treturn\n\t}\n\n\tinputs[\"mean\"] = \"\"\n\t// inputs[\"l1\"] = \"业务数据\"\n\t// inputs[\"l2\"] = \"交易信息\"\n\t// inputs[\"l3\"] = \"通用交易信息\"\n\n\tres, err := RenderTemplate(\n\t\t`\n[数据类别及说明（冒号：前面为数据类别）]：\n{{- range .frame.classify -}}\n{{- if .explain}}\n{{.curr}}：{{.explain}}{{.fields}}\n{{- else}}\n{{.curr}}：主要包括\n{{- range .next -}}\n{{.curr}}、\n{{- end -}}\n如\n{{- range .next -}}\n{{- range .next -}}\n{{.curr}}、\n{{- range .next -}}\n{{.curr}}、\n{{- end -}}\n{{- end -}}\n{{- end -}}\n{{- end -}}\n{{- end -}}\n`,\n\t\tinputs,\n\t)\n\trequire.NoError(t, err)\n\n\tfmt.Println(res)\n}\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/internal/domain/core/template/templates_test.go b/internal/domain/core/template/templates_test.go
--- a/internal/domain/core/template/templates_test.go	(revision b3dfdceec260d6f61ee80516b2bd4311de744179)
+++ b/internal/domain/core/template/templates_test.go	(date 1716966276034)
@@ -1,9 +1,7 @@
 package template
 
 import (
-	"encoding/json"
 	"fmt"
-	"os"
 	"testing"
 
 	"github.com/stretchr/testify/assert"
@@ -157,44 +155,26 @@
 // {{- end -}}
 
 func TestRenderMultiMap(t *testing.T) {
-
-	data, err := os.ReadFile("../../testdata/dataclass/input.json")
-	if err != nil {
-		return
-	}
+	inputs := make(map[string]any)
 
-	var inputs map[string]any
-	if err := json.Unmarshal(data, &inputs); err != nil {
-		return
+	inputs["tool_parameters"] = map[string]any{
+		"ruleType":    "/Mareware/Minner",
+		"name":        "矿池地址",
+		"appProtocol": "dns",
+		"ioc":         "buy1.usadota.com",
+		"srcaddress":  "**************",
+		"destaddress": "***************",
+		"windowSize":  float64(3600),
+		"visitTimes": []string{
+			"2024-01-09 09:58:39",
+			"2024-01-09 10:54:29",
+			"2024-01-09 13:51:00",
+		},
 	}
-
-	inputs["mean"] = ""
-	// inputs["l1"] = "业务数据"
-	// inputs["l2"] = "交易信息"
-	// inputs["l3"] = "通用交易信息"
 
 	res, err := RenderTemplate(
 		`
-[数据类别及说明（冒号：前面为数据类别）]：
-{{- range .frame.classify -}}
-{{- if .explain}}
-{{.curr}}：{{.explain}}{{.fields}}
-{{- else}}
-{{.curr}}：主要包括
-{{- range .next -}}
-{{.curr}}、
-{{- end -}}
-如
-{{- range .next -}}
-{{- range .next -}}
-{{.curr}}、
-{{- range .next -}}
-{{.curr}}、
-{{- end -}}
-{{- end -}}
-{{- end -}}
-{{- end -}}
-{{- end -}}
+		{{range .}} {{$.tool_parameters.visitTimes}}{{end}},
 `,
 		inputs,
 	)
