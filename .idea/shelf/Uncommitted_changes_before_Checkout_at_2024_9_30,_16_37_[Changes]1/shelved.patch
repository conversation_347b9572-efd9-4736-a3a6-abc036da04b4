Index: internal/domain/core/chain/chain_react.go
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>package chain\n\nimport (\n\t\"context\"\n\t\"encoding/json\"\n\t\"fmt\"\n\t\"regexp\"\n\t\"strings\"\n\t\"time\"\n\n\t\"secwalk/internal/domain/core/callback\"\n\t\"secwalk/internal/domain/core/llm\"\n\t\"secwalk/internal/domain/core/memory\"\n\t\"secwalk/internal/domain/core/message\"\n\t\"secwalk/internal/domain/core/prompt\"\n\t\"secwalk/internal/domain/core/schema\"\n\t\"secwalk/pkg/logger\"\n\t\"secwalk/pkg/random\"\n\n\t\"github.com/sirupsen/logrus\"\n)\n\nconst _finalAnswerAction = \"Final Answer:\"\n\nvar _ Chain = &ReActChain{}\n\ntype ReActChain struct {\n\tllm           llm.Model\n\tsystemPrompt  string\n\tuserPrompt    string\n\ttools         []schema.Tool\n\tagents        []schema.Agent\n\tmemory        memory.Memory\n\tmaxIterations int\n\tonce          bool\n\tnoDup         bool\n\tnoAction      bool\n}\n\nfunc NewReActChain(llm llm.Model, opts ...Option) (Chain, error) {\n\topt := newDefaultChainOption()\n\tfor _, option := range opts {\n\t\toption(opt)\n\t}\n\n\treturn &ReActChain{\n\t\tllm:           llm,\n\t\tsystemPrompt:  opt.systemPrompt,\n\t\tuserPrompt:    opt.userPrompt,\n\t\ttools:         opt.tools,\n\t\tagents:        opt.agents,\n\t\tmemory:        opt.memory,\n\t\tmaxIterations: opt.maxIterations,\n\t\tonce:          opt.once,\n\t\tnoDup:         opt.noDup,\n\t\tnoAction:      opt.noAction,\n\t}, nil\n}\n\nfunc (c *ReActChain) Call(ctx context.Context, inputs map[string]any, opts ...schema.CallOption) (string, error) {\n\tvar err error\n\tvar steps = make([]Step, 0)\n\tvar finish string\n\n\t// 根据设置的最大迭代次数，反复调用模型进行思考与工具调用，直到产生答案\n\tfor i := 0; i < c.maxIterations; i++ {\n\t\tfinish, err = c.doIteration(ctx, &steps, inputs, opts...)\n\t\tif len(finish) > 0 || err != nil {\n\t\t\treturn finish, err\n\t\t}\n\t}\n\n\treturn \"\", ErrNotFinished\n}\n\n// 使用MRKL模板思维模板，让模型思考得到解决问题的动作或最终答案\nfunc (c *ReActChain) doIteration(ctx context.Context, steps *[]Step, inputs map[string]any, opts ...schema.CallOption) (string, error) {\n\tvar opt schema.CallOptions\n\tfor _, option := range opts {\n\t\toption(&opt)\n\t}\n\n\t// 实时创建链，为了刷新可用工具\n\tcreateOpts := make([]Option, 0)\n\tcreateOpts = append(createOpts, WithMemory(c.memory))\n\n\t// 设置系统提示词部分\n\tif len(c.systemPrompt) > 0 {\n\t\tcreateOpts = append(createOpts, WithSystemPromptTmpl(prompt.NewSystemMessagePromptTemplate(c.systemPrompt)))\n\t}\n\n\t// 设置用户提示词部分\n\tuserPrompt := _defaultMrklPrompt\n\tif len(c.userPrompt) > 0 {\n\t\tuserPrompt = fmt.Sprintf(\"%s\\n\\n%s\", c.userPrompt, _defaultMrklPrompt)\n\t}\n\tcreateOpts = append(createOpts, WithUserPromptTmpl(prompt.NewUserMessagePromptTemplate(\n\t\tuserPrompt,\n\t\tprompt.WithInputKeys(schema.DefaultInputKey, reservedScratchpad),\n\t\tprompt.WithPartialValue(\"tool_names\", schema.ToolNames(c.tools)),\n\t\tprompt.WithPartialValue(\"tool_descriptions\", schema.ToolDescriptions(c.tools)),\n\t\tprompt.WithPartialValue(\"agent_names\", schema.AgentNames(c.agents)),\n\t\tprompt.WithPartialValue(\"agent_descriptions\", schema.AgentDescriptions(c.agents)),\n\t)))\n\n\tchain, err := NewLLMChain(c.llm, createOpts...)\n\tif err != nil {\n\t\treturn \"\", err\n\t}\n\n\t// 合并思考步骤\n\tinputs[reservedScratchpad] = c.constructScratchPad(*steps)\n\n\t// 调用模型链\n\toutput, err := chain.Call(\n\t\tctx,\n\t\tinputs,\n\t\tappend(opts,\n\t\t\tschema.WithStopWords([]string{\"\\nObservation:\", \"\\n\\tObservation:\"}),\n\t\t\tschema.WithFnStreamPreview(nil),\n\t\t)...,\n\t)\n\tif err != nil {\n\t\treturn \"\", err\n\t}\n\n\tstep := &Step{Output: output}\n\n\t// 解析模型结果\n\tif err := c.parseOutput(step, output); err != nil {\n\t\treturn \"\", err\n\t}\n\n\t// 存在结果则思考结束返回\n\tif len(step.Answer) > 0 {\n\t\tif opt.MasterEntry {\n\t\t\tlogrus.WithField(logger.KeyCategory, logger.CategoryCore).\n\t\t\t\tWithField(logger.KeySID, opt.SID).\n\t\t\t\tWithField(logger.KeyUID, opt.UID).\n\t\t\t\tWithField(logger.KeyAID, opt.AID).\n\t\t\t\tInfof(\"react answer: %s\", step.Answer)\n\n\t\t\tif opt.LogMemory {\n\t\t\t\tc.memory.SaveMessage(ctx, message.NewAssistantChatMessage(\n\t\t\t\t\tfmt.Sprintf(\"最终答案: %s\", step.Answer)))\n\t\t\t}\n\n\t\t\tif opt.FnStreamPreview != nil {\n\t\t\t\tfrom := callback.FromReactAnswer\n\t\t\t\tif opt.OutputFinal {\n\t\t\t\t\tfrom = callback.FromExecuteResult\n\t\t\t\t}\n\n\t\t\t\tcallback.PreviewFackStream(opt.FnStreamPreview, &callback.PreviewMessage{\n\t\t\t\t\tType:      callback.TypeInline,\n\t\t\t\t\tFrom:      from,\n\t\t\t\t\tNodeID:    opt.NodeID,\n\t\t\t\t\tName:      opt.Name,\n\t\t\t\t\tTimestamp: time.Now().UnixMilli(),\n\t\t\t\t\tMessageID: random.DigitString(16),\n\t\t\t\t\tContent:   step.Answer,\n\t\t\t\t})\n\t\t\t}\n\t\t}\n\n\t\treturn step.Answer, nil\n\t}\n\n\t// 输出思考信息\n\tlogrus.WithField(logger.KeyCategory, logger.CategoryCore).\n\t\tWithField(logger.KeySID, opt.SID).\n\t\tWithField(logger.KeyUID, opt.UID).\n\t\tWithField(logger.KeyAID, opt.AID).\n\t\tInfof(\"react think: %s\", step.Thought)\n\n\tif opt.LogMemory {\n\t\tc.memory.SaveMessage(ctx, message.NewAssistantChatMessage(\n\t\t\tfmt.Sprintf(\"思考过程: %s\", step.Thought)))\n\t}\n\n\tif opt.FnStreamPreview != nil {\n\t\tcallback.PreviewFackStream(opt.FnStreamPreview, &callback.PreviewMessage{\n\t\t\tType:      callback.TypeInline,\n\t\t\tFrom:      callback.FromReactThink,\n\t\t\tNodeID:    opt.NodeID,\n\t\t\tName:      opt.Name,\n\t\t\tTimestamp: time.Now().UnixMilli(),\n\t\t\tMessageID: random.DigitString(16),\n\t\t\tContent:   step.Thought,\n\t\t})\n\t}\n\n\tactionStr := fmt.Sprintf(\"%s - %s\", step.Action.Action, step.ActionInput)\n\tif c.noAction {\n\t\tif opt.MasterEntry {\n\t\t\tlogrus.WithField(logger.KeyCategory, logger.CategoryCore).\n\t\t\t\tWithField(logger.KeySID, opt.SID).\n\t\t\t\tWithField(logger.KeyUID, opt.UID).\n\t\t\t\tWithField(logger.KeyAID, opt.AID).\n\t\t\t\tInfof(\"react answer: %s\", actionStr)\n\n\t\t\tif opt.LogMemory {\n\t\t\t\tc.memory.SaveMessage(ctx, message.NewAssistantChatMessage(\n\t\t\t\t\tfmt.Sprintf(\"最终答案: %s\", actionStr)))\n\t\t\t}\n\n\t\t\tif opt.FnStreamPreview != nil {\n\t\t\t\tfrom := callback.FromReactAnswer\n\t\t\t\tif opt.OutputFinal {\n\t\t\t\t\tfrom = callback.FromExecuteResult\n\t\t\t\t}\n\n\t\t\t\tcallback.PreviewFackStream(opt.FnStreamPreview, &callback.PreviewMessage{\n\t\t\t\t\tType:      callback.TypeInline,\n\t\t\t\t\tFrom:      from,\n\t\t\t\t\tNodeID:    opt.NodeID,\n\t\t\t\t\tName:      opt.Name,\n\t\t\t\t\tTimestamp: time.Now().UnixMilli(),\n\t\t\t\t\tMessageID: random.DigitString(16),\n\t\t\t\t\tContent:   actionStr,\n\t\t\t\t})\n\t\t\t}\n\t\t}\n\n\t\treturn actionStr, nil\n\t}\n\t// 输出动作调用\n\tlogrus.WithField(logger.KeyCategory, logger.CategoryCore).\n\t\tWithField(logger.KeySID, opt.SID).\n\t\tWithField(logger.KeyUID, opt.UID).\n\t\tWithField(logger.KeyAID, opt.AID).\n\t\tInfof(\"react action: %s\", actionStr)\n\n\tif opt.LogMemory {\n\t\tc.memory.SaveMessage(ctx, message.NewAssistantChatMessage(\n\t\t\tfmt.Sprintf(\"动作调用: %s - %s\", step.Action.Action, step.Action.ActionInput)))\n\t}\n\n\tif opt.FnStreamPreview != nil {\n\t\tcallback.PreviewFackStream(opt.FnStreamPreview, &callback.PreviewMessage{\n\t\t\tType:      callback.TypeInline,\n\t\t\tFrom:      callback.FromReactAction,\n\t\t\tNodeID:    opt.NodeID,\n\t\t\tName:      opt.Name,\n\t\t\tTimestamp: time.Now().UnixMilli(),\n\t\t\tMessageID: random.DigitString(16),\n\t\t\tContent:   actionStr,\n\t\t})\n\t}\n\n\t// 调用动作\n\tstep.Observation = c.doAction(ctx, step.Action.Action, step.ActionInput, inputs, opts...)\n\n\t// 输出动作结果\n\tlogrus.WithField(logger.KeyCategory, logger.CategoryCore).\n\t\tWithField(logger.KeySID, opt.SID).\n\t\tWithField(logger.KeyUID, opt.UID).\n\t\tWithField(logger.KeyAID, opt.AID).\n\t\tInfof(\"react action observe: %s\", step.Observation)\n\n\tif opt.LogMemory {\n\t\tc.memory.SaveMessage(ctx, message.NewAssistantChatMessage(\n\t\t\tfmt.Sprintf(\"动作响应: %s\", step.Observation),\n\t\t))\n\t}\n\n\tif opt.FnStreamPreview != nil {\n\t\topt.FnStreamPreview(&callback.PreviewMessage{\n\t\t\tType:      callback.TypeInline,\n\t\t\tFrom:      callback.FromReactObserve,\n\t\t\tNodeID:    opt.NodeID,\n\t\t\tName:      opt.Name,\n\t\t\tTimestamp: time.Now().UnixMilli(),\n\t\t\tMessageID: random.DigitString(16),\n\t\t\tContent:   step.Observation,\n\t\t})\n\t}\n\n\t// 工具模型链直接返回\n\tif c.once {\n\t\treturn step.Observation, nil\n\t}\n\n\t*steps = append(*steps, *step)\n\treturn \"\", nil\n}\n\nfunc (c *ReActChain) GetMemory() memory.Memory {\n\treturn c.memory\n}\n\n// 拼接中间思考步骤\nfunc (c *ReActChain) constructScratchPad(steps []Step) string {\n\tvar scratchPad string\n\tif len(steps) > 0 {\n\t\tfor _, step := range steps {\n\t\t\tscratchPad += step.Output\n\n\t\t\t// 限制10k长度\n\t\t\tif len(step.Observation) > 30000 {\n\t\t\t\tscratchPad += \"\\nObservation: \" + step.Observation[:30000]\n\t\t\t} else {\n\t\t\t\tscratchPad += \"\\nObservation: \" + step.Observation\n\t\t\t}\n\t\t}\n\t\tscratchPad += \"\\nThought:\"\n\t}\n\n\treturn scratchPad\n}\n\n// 解析MRKL返回结构\nfunc (c *ReActChain) parseOutput(step *Step, output string) error {\n\tif strings.Contains(output, _finalAnswerAction) {\n\t\tsplits := strings.Split(output, _finalAnswerAction)\n\t\tif len(splits) == 1 {\n\t\t\tstep.Answer = strings.TrimSpace(splits[0])\n\t\t} else {\n\t\t\tstep.Action = &Action{Thought: strings.TrimSpace(splits[0])}\n\t\t\tstep.Answer = strings.TrimSpace(splits[len(splits)-1])\n\t\t}\n\t\treturn nil\n\t}\n\n\tr := regexp.MustCompile(`(?s)(.*?)Action:\\s*(.+)\\s*Action Input:\\s*(?s)(\\{.*?\\})`)\n\tmatches := r.FindStringSubmatch(output)\n\tif len(matches) == 0 {\n\t\tstep.Answer = strings.TrimSpace(output)\n\t\treturn nil\n\t}\n\n\tstep.Action = &Action{\n\t\tThought:     strings.TrimSpace(matches[1]),\n\t\tAction:      strings.TrimSpace(matches[2]),\n\t\tActionInput: strings.TrimSpace(matches[3]),\n\t}\n\treturn nil\n}\n\n// 动作调用\nfunc (c *ReActChain) doAction(ctx context.Context, action, actionInput string, inputs map[string]any, opts ...schema.CallOption) string {\n\tvar opt schema.CallOptions\n\tfor _, option := range opts {\n\t\toption(&opt)\n\t}\n\n\tvar params = make(map[string]any)\n\t// 解析工具参数\n\tif err := json.Unmarshal([]byte(actionInput), &params); err != nil {\n\t\treturn \"tool params is not a Json map, try regenerate\"\n\t}\n\tfor k, v := range params {\n\t\tinputs[k] = v\n\t}\n\n\t// 获取工具实例\n\ttool, ok := schema.NameToTool(c.tools)[strings.ToUpper(action)]\n\tif ok {\n\t\t// 调用工具\n\t\tob, err := schema.ToolCall(ctx, tool, inputs, append(opts, schema.WithLocals(params))...)\n\t\t// 调用过的工具直接移除，避免重复调用\n\t\tif c.noDup {\n\t\t\tfor i, v := range c.tools {\n\t\t\t\tif tool.Config().ID == v.Config().ID {\n\t\t\t\t\tc.tools = append(c.tools[:i], c.tools[i+1:]...)\n\t\t\t\t\tbreak\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tif err != nil {\n\t\t\treturn fmt.Sprintf(\"tool call error: %s\", err.Error())\n\t\t} else {\n\t\t\treturn ob\n\t\t}\n\t}\n\n\tagent, ok := schema.NameToAgent(c.agents)[strings.ToUpper(action)]\n\tif ok {\n\t\tres, err := agent.Call(ctx, params, opts...)\n\n\t\t// 调用过的智能体直接移除，避免重复调用\n\t\tif c.noDup {\n\t\t\tfor i, v := range c.agents {\n\t\t\t\tif agent.Config().ID == v.Config().ID {\n\t\t\t\t\tc.agents = append(c.agents[:i], c.agents[i+1:]...)\n\t\t\t\t\tbreak\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tif err != nil {\n\t\t\treturn fmt.Sprintf(\"assistant call error: %s\", err.Error())\n\t\t} else {\n\t\t\tif opt.CbSetVariable != nil {\n\t\t\t\topt.CbSetVariable(opt.NodeID, res.Results)\n\t\t\t}\n\t\t\treturn res.Session.Messages[len(res.Session.Messages)-1].Content\n\t\t}\n\t}\n\n\treturn fmt.Sprintf(\"%s is not a valid tool or assistant, try another one\", action)\n}\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/internal/domain/core/chain/chain_react.go b/internal/domain/core/chain/chain_react.go
--- a/internal/domain/core/chain/chain_react.go	
+++ b/internal/domain/core/chain/chain_react.go	
@@ -322,7 +322,7 @@
 		return nil
 	}
 
-	r := regexp.MustCompile(`(?s)(.*?)Action:\s*(.+)\s*Action Input:\s*(?s)(\{.*?\})`)
+	r := regexp.MustCompile(`(?s)(.*?)Action:\s*(.+)\s*Action Input:\s*(?s)(\{.*\})`)
 	matches := r.FindStringSubmatch(output)
 	if len(matches) == 0 {
 		step.Answer = strings.TrimSpace(output)
Index: configs/dev.yaml
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+># 服务基本配置\nserver:\n  name: secwalk\n  address: :8998\n  version: v1.0.0\n\n# 日志配置\nlogger:\n  app: secwalk\n  level: debug\n  format: false\n  output: logs/secwalk.log\n  max_size: 20\n  max_age: 30\n  max_backup: 10\n\n# 服务注册配置\nregister:\n  type: consul\n  host: *************:8500\n  token: d9295a36-9f55-4580-b22d-71cfa5837f76\n  username:\n  password:\n\n# 服务发现配置\nservices:\n  backend:\n    type: direct\n    name: backend\n    host: ************:9994\n    metas:\n      token: 9KsxdRjPXY95CP4WHSytwXW7QYhBrtEV\n  gateway:\n    type: direct\n    name: secwall\n    host: ***********:8995\n  coder:\n    type: direct\n    name: secvirt\n    host: ***********:8994\n  hbrag:\n    type: direct\n    name: hb-rag\n    host: ***********:18092\n\n# 存储配置\nes:\n  addresses:\n    - http://***********:9200\n  username: elastic\n  password: keU06fLj3gV3mzOGInOLd9gZ3lU3VLaw\n\nminio:\n  host: ***********:9000\n  access_key: admin\n  secret_key: tYqV9s#bt3ADrMy\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/configs/dev.yaml b/configs/dev.yaml
--- a/configs/dev.yaml	
+++ b/configs/dev.yaml	
@@ -33,7 +33,7 @@
   gateway:
     type: direct
     name: secwall
-    host: ***********:8995
+    host: ***********:18501
   coder:
     type: direct
     name: secvirt
