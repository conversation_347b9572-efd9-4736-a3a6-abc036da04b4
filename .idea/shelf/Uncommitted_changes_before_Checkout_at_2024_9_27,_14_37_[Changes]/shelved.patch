Index: pkg/license/license.go
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>package license\n\nimport (\n\t\"encoding/json\"\n\t\"errors\"\n\t\"sync\"\n\t\"time\"\n\n\t\"secwalk/internal/domain/core/endpoint\"\n\t\"secwalk/internal/domain/core/schema\"\n\t\"secwalk/pkg/logger\"\n\n\t\"github.com/sirupsen/logrus\"\n)\n\nvar (\n\tErrIncorrectLicense = errors.New(\"incorrect license\")\n\tErrLicenseExpired   = errors.New(\"license expired\")\n\tErrLicenseNotFound  = errors.New(\"license not found\")\n\tErrLicenseType      = errors.New(\"license type not supported\")\n)\n\nvar (\n\tmux sync.RWMutex\n\tlic = &License{\n\t\tModel: Model{Key: \"123\"},\n\t\tAgent: Agent{\n\t\t\tLicenses: []AgentLicense{},\n\t\t},\n\t}\n)\n\nfunc Init() {\n\tgo func() {\n\t\tfor {\n\t\t\tif err := Refresh(); err != nil {\n\t\t\t\tlogrus.WithField(logger.KeyCategory, logger.CategorySystem).\n\t\t\t\t\tWarnf(\"license fetch error: %s\", err)\n\t\t\t}\n\t\t\ttime.Sleep(5 * time.Minute)\n\t\t}\n\t}()\n}\n\n// 获取智能体授权配置\nfunc GetAgentKey(_type int, id string) (string, error) {\n\tmux.RLock()\n\tdefer mux.RUnlock()\n\n\tswitch _type {\n\tcase schema.TypeUser:\n\t\t// 用户类型智能体，默认使用模型加密密钥，且拥有所有操作权限\n\t\treturn lic.Model.Key, nil\n\n\tcase schema.TypePerm:\n\t\t// 授权类型智能体\n\t\t// 在授权模式下，拥有所有操作权限，但是若没有找到授权ID对于的配置，直接报错\n\t\t// 在非授权模式下，按照授权配置限制操作\n\t\tfor _, item := range lic.Agent.Licenses {\n\t\t\tif id == item.ID {\n\t\t\t\treturn item.Key, nil\n\t\t\t}\n\t\t}\n\t}\n\n\treturn \"\", ErrLicenseNotFound\n}\n\nfunc Refresh() error {\n\tlicense, err := fetch()\n\tif err != nil {\n\t\treturn err\n\t}\n\n\tmux.Lock()\n\tif license != nil {\n\t\tlic = license\n\t}\n\tmux.Unlock()\n\treturn nil\n}\n\ntype License struct {\n\tModel Model `json:\"model\"`\n\tAgent Agent `json:\"agent\"`\n}\n\ntype Model struct {\n\tKey string `json:\"key\"`\n}\n\ntype Agent struct {\n\tLicenses []AgentLicense `json:\"authorizations\"`\n}\n\ntype AgentLicense struct {\n\tID   string `json:\"id\"`\n\tName string `json:\"name\"`\n\tKey  string `json:\"key\"`\n}\n\nfunc fetch() (*License, error) {\n\ttype response struct {\n\t\tCode int      `json:\"code\"`\n\t\tMsg  string   `json:\"msg\"`\n\t\tData *License `json:\"data\"`\n\t}\n\n\tdata, err := endpoint.GetLicense()\n\tif err != nil {\n\t\treturn nil, err\n\t}\n\n\tret := &response{}\n\tif err := json.Unmarshal(data, ret); err != nil {\n\t\treturn nil, err\n\t}\n\n\tif ret.Code != 0 {\n\t\treturn nil, ErrIncorrectLicense\n\t}\n\n\treturn ret.Data, nil\n}\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/pkg/license/license.go b/pkg/license/license.go
--- a/pkg/license/license.go	
+++ b/pkg/license/license.go	
@@ -23,7 +23,7 @@
 var (
 	mux sync.RWMutex
 	lic = &License{
-		Model: Model{Key: "123"},
+		Model: Model{Key: "aa675b38-ff66-4848-829b-693146ae790a"},
 		Agent: Agent{
 			Licenses: []AgentLicense{},
 		},
Index: configs/dev.yaml
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+># 服务基本配置\nserver:\n  name: secwalk\n  address: :8998\n\n# 日志配置\nlogger:\n  app: secwalk\n  level: debug\n  format: false\n  output: logs/secwalk.log\n  max_size: 20\n  max_age: 30\n  max_backup: 10\n\n# 服务注册配置\nregister:\n  type: consul\n  host: *************:8500\n  token: d9295a36-9f55-4580-b22d-71cfa5837f76\n  username:\n  password:\n\n# 服务发现配置\nservices:\n  backend:\n    type: direct\n    name: backend\n    host: ************:9994\n    metas:\n      token: 9KsxdRjPXY95CP4WHSytwXW7QYhBrtEV\n  gateway:\n    type: direct\n    name: secwall\n    host: ***********:8995\n  coder:\n    type: direct\n    name: secvirt\n    host: ***********:8994\n  hbrag:\n    type: direct\n    name: hb-rag\n    host: ***********:18092\n    metas:\n      authorization: 5M1048xJby2q2s6H4Sn9Hv5guYu2tUW0\n\n# 存储配置\ndb:\n  address: ***********:3306\n  username: root\n  password: J7aXgk2BJUj=\n\nes:\n  addresses:\n    - http://***********:9200\n  username: elastic\n  password: keU06fLj3gV3mzOGInOLd9gZ3lU3VLaw\n\nminio:\n  host: ***********:9000\n  access_key: admin\n  secret_key: tYqV9s#bt3ADrMy\n\n# 内部功能开关\nfeature:\n  use_xguard: true\n\n# 热更新配置项\nhot_swaps:\n  - path: logger.level\n    desc: 日志等级，可选（debug/info/warn/error）\n    type: string\n\n  - path: feature.use_xguard\n    desc: 是否开启风控检测\n    type: bool\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/configs/dev.yaml b/configs/dev.yaml
--- a/configs/dev.yaml	
+++ b/configs/dev.yaml	
@@ -32,7 +32,7 @@
   gateway:
     type: direct
     name: secwall
-    host: ***********:8995
+    host: ***********:18501
   coder:
     type: direct
     name: secvirt
@@ -63,7 +63,7 @@
 
 # 内部功能开关
 feature:
-  use_xguard: true
+  use_xguard: false
 
 # 热更新配置项
 hot_swaps:
