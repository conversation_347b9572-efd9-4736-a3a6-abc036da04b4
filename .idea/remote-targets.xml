<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="RemoteTargetsManager">
    <targets>
      <target name="root@111.170.7.235:22" type="ssh/sftp" uuid="abe04230-2325-4c46-aad2-2f7c5c2d5f66">
        <config>
          <option name="projectRootOnTarget" value="/root/secwalk" />
          <option name="serverName" value="root@111.170.7.235:22 password" />
        </config>
        <ContributedStateBase type="GoLanguageRuntime">
          <config>
            <option name="goPath" value="mesg: ttyname failed: Inappropriate ioctl for device" />
            <option name="goRoot" value="mesg: ttyname failed: Inappropriate ioctl for device" />
            <option name="goVersion" value="mesg: ttyname failed: Inappropriate ioctl for device" />
          </config>
        </ContributedStateBase>
      </target>
    </targets>
  </component>
</project>