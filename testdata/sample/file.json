{"inputs": {}, "config": {"private_envs": [], "session_round": 5, "public_envs": [], "org": "1802543778616180738", "input_parameters": [{"type": "string", "name": "input"}], "edges": [], "description": "hellowordhellowordhellowordhelloword", "envs": [], "cot_version": "v2", "uid": "1826484063792656386", "entry": "node3", "nodes": [{"max_retries": 3, "input_parameters": [], "iteration_values": [], "description": "这是一个智能体_YqZH2C", "max_working": 30, "type": "act_agent", "tools": [], "timeout": 300, "agents": [{"options": [{"name": "input", "type": "string", "value": {"expr": "234234", "type": "literal"}, "required": false}, {"name": "X", "type": "string", "required": false}], "id": "befb755e-fdab-47b6-aa23-49baa8b62bac"}], "result_key": "result_key", "output_parameters": [], "name": "智能体_YqZH2C", "iteration": false, "on_error": 0, "model": {"top_p": 1, "max_tokens": 1024, "reasoning": 1, "top_k": -1, "temperature": 0, "model": "HengNao-v4"}, "id": "node3"}], "name": "hellowordhelloword", "model": {"model": "HengNao-v4", "reasoning": 1, "temperature": 0, "max_tokens": 1024, "top_k": -1, "top_p": 1}, "prologue": ""}}