package dataclass

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"testing"

	"github.com/stretchr/testify/require"
	"github.com/xuri/excelize/v2"
)

func TestEval(t *testing.T) {
	frame, err := GetFramework()
	require.NoError(t, err)

	f, err := excelize.OpenFile("source.xlsx")
	require.NoError(t, err)
	defer f.Close()

	rows, err := f.GetRows("Sheet1")
	require.NoError(t, err)

	srcs := make(DataSources, 0)
	for i := 1; i < len(rows); i++ {
		srcs = srcs.add(rows[i], frame)
	}

	require.NoError(t, PostAgent(srcs))
}

type DataSources []DataSource

func (s DataSources) add(row []string, frame *Frame) DataSources {
	table := row[2]
	table_description := row[3]
	field := row[4]
	field_description := row[5]
	field_example := ""
	if len(row) == 7 {
		field_example = row[6]
	}

	added := false
	for i, v := range s {
		if v.Table == table {
			s[i].AllFields.Field = append(s[i].AllFields.Field, field)
			s[i].AllFields.FieldDescription = append(s[i].AllFields.FieldDescription, field_description)
			s[i].AllFields.FieldExample = append(s[i].AllFields.FieldExample, field_example)
			added = true
			break
		}
	}

	if !added {
		s = append(s, DataSource{
			Frame:            frame,
			Table:            table,
			TableDescription: table_description,
			AllFields: AllFields{
				Field:            []string{field},
				FieldDescription: []string{field_description},
				FieldExample:     []string{field_example},
			},
		})
	}

	return s
}

type DataSource struct {
	Table            string    `json:"table"`
	TableDescription string    `json:"table_description"`
	Field            string    `json:"field"`
	FieldDescription string    `json:"field_description"`
	FieldExample     string    `json:"field_example"`
	AllFields        AllFields `json:"all_fields"`
	Frame            any       `json:"frame"`
}

type AllFields struct {
	Field            []string `json:"field"`
	FieldDescription []string `json:"field_description"`
	FieldExample     []string `json:"field_example"`
}

type Request struct {
	AID    string `json:"aid"`
	Inputs any    `json:"inputs"`
}

func PostAgent(dss DataSources) error {
	for _, ds := range dss {
		for i := range ds.AllFields.Field {
			ds.Field = ds.AllFields.Field[i]
			ds.FieldDescription = ds.AllFields.FieldDescription[i]
			ds.FieldExample = ds.AllFields.FieldExample[i]

			input, err := json.Marshal(ds)
			if err != nil {
				return err
			}
			fmt.Println(string(input))

			body, err := json.Marshal(&Request{
				AID:    "61f92957-59be-4a14-8b71-816fa7175030",
				Inputs: ds,
			})
			if err != nil {
				return err
			}

			req, err := http.NewRequest(
				http.MethodPost,
				"http://127.0.0.1:8998/secwalk/v1/agent/execute",
				bytes.NewBuffer(body),
			)
			if err != nil {
				return err
			}

			req.Header.Set("Accept", "application/json")
			req.Header.Set("Content-Type", "application/json")
			res, err := http.DefaultClient.Do(req)
			if err != nil {
				return err
			}
			defer res.Body.Close()

			if res.StatusCode != http.StatusOK {
				return errors.New("http request error")
			}

			data, err := io.ReadAll(res.Body)
			if err != nil {
				return err
			}

			fmt.Println(string(data))
		}
	}
	return nil
}
