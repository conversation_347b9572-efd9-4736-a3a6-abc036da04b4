package dataclass

import (
	"encoding/json"
	"fmt"
	"os"
	"strings"

	"github.com/xuri/excelize/v2"
)

func GetFramework() (*Frame, error) {
	f, err := excelize.OpenFile("framework.xlsx")
	if err != nil {
		return nil, err
	}
	defer f.Close()

	rows, err := f.GetRows("Sheet1")
	if err != nil {
		return nil, err
	}

	frames := &Frame{Classify: make([]Classify, 0)}

	for i := 1; i < len(rows); i++ {
		industry := strings.TrimSpace(rows[i][0])
		l1 := strings.TrimSpace(rows[i][1])
		l2 := strings.TrimSpace(rows[i][2])
		l3 := strings.TrimSpace(rows[i][3])
		l4 := strings.TrimSpace(rows[i][4])
		l5 := strings.TrimSpace(rows[i][5])
		explain := strings.TrimSpace(rows[i][6])
		fields := strings.TrimSpace(rows[i][7])

		if len(frames.Industry) == 0 {
			frames.Industry = industry
		}

		added := false
		l1index := 0
		l2index := 0
		l3index := 0
		l4index := 0

		index, ok := inList(l1, frames.Classify)
		if ok {
			added = true
			l1index = index
		}

		if !added {
			l1index = len(frames.Classify)

			if l2 != "/" {
				frames.Classify = append(frames.Classify, Classify{
					Curr: l1,
					Next: make([]Classify, 0),
				})
			} else {
				frames.Classify = append(frames.Classify, Classify{
					Curr:    l1,
					Next:    make([]Classify, 0),
					Fields:  fields,
					Explain: explain,
				})
				continue
			}
		}

		added = false
		index, ok = inList(l2, frames.Classify[l1index].Next)
		if ok {
			added = true
			l2index = index
		}

		if !added {
			l2index = len(frames.Classify[l1index].Next)
			if l3 != "/" {
				frames.Classify[l1index].Next = append(frames.Classify[l1index].Next, Classify{
					Curr: l2,
					Next: make([]Classify, 0),
				})
			} else {
				frames.Classify[l1index].Next = append(frames.Classify[l1index].Next, Classify{
					Curr:    l2,
					Next:    make([]Classify, 0),
					Fields:  fields,
					Explain: explain,
				})
				continue
			}
		}

		added = false
		index, ok = inList(l3, frames.Classify[l1index].Next[l2index].Next)
		if ok {
			added = true
			l3index = index
		}

		if !added {
			l3index = len(frames.Classify[l1index].Next[l2index].Next)
			if l4 != "/" {
				frames.Classify[l1index].Next[l2index].Next = append(frames.Classify[l1index].Next[l2index].Next, Classify{
					Curr: l3,
					Next: make([]Classify, 0),
				})
			} else {
				frames.Classify[l1index].Next[l2index].Next = append(frames.Classify[l1index].Next[l2index].Next, Classify{
					Curr:    l3,
					Next:    make([]Classify, 0),
					Fields:  fields,
					Explain: explain,
				})
				continue
			}
		}

		added = false
		index, ok = inList(l4, frames.Classify[l1index].Next[l2index].Next[l3index].Next)
		if ok {
			added = true
			l4index = index
		}

		if !added {
			l4index = len(frames.Classify[l1index].Next[l2index].Next[l3index].Next)
			if l5 != "/" {
				frames.Classify[l1index].Next[l2index].Next[l3index].Next = append(frames.Classify[l1index].Next[l2index].Next[l3index].Next, Classify{
					Curr: l4,
					Next: make([]Classify, 0),
				})
			} else {
				frames.Classify[l1index].Next[l2index].Next[l3index].Next = append(frames.Classify[l1index].Next[l2index].Next[l3index].Next, Classify{
					Curr:    l4,
					Next:    make([]Classify, 0),
					Fields:  fields,
					Explain: explain,
				})
				continue
			}
		}

		if l5 != "/" {
			frames.Classify[l1index].Next[l2index].Next[l3index].Next[l4index].Next = append(frames.Classify[l1index].Next[l2index].Next[l3index].Next[l4index].Next, Classify{
				Curr:    l5,
				Next:    make([]Classify, 0),
				Fields:  fields,
				Explain: explain,
			})
		}
	}

	// framework
	mapframe := map[string]any{"frame": frames}
	buf, err := json.Marshal(mapframe)
	if err != nil {
		return nil, err
	}
	fmt.Println(string(buf))

	fi, err := os.Create("framework.json")
	if err != nil {
		return nil, err
	}

	fi.Write(buf)
	fi.Close()

	return frames, nil
}

type Frame struct {
	Industry string     `json:"industry"`
	Classify []Classify `json:"classify"`
}

type Classify struct {
	Curr    string     `json:"curr"`
	Next    []Classify `json:"next"`
	Fields  string     `json:"fields"`
	Explain string     `json:"explain"`
}

func inList(val string, list []Classify) (int, bool) {
	for i, v := range list {
		if v.Curr == val {
			return i, true
		}
	}
	return 0, false
}
