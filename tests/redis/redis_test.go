package redis

import (
	"context"
	"encoding/json"
	"fmt"
	"testing"

	"github.com/redis/go-redis/v9"
)

type Request struct {
	PromptTokens     int64 `json:"prompt_tokens"`     // 请求的Token数
	CompletionTokens int64 `json:"completion_tokens"` // 生成的Token数量
}

func TestCache(t *testing.T) {
	client := redis.NewClient(&redis.Options{
		Addr:     "10.20.152.35:32747",
		Password: "V3l}yGP:*%KY",
		DB:       10,
	})

	var k1, k2, k3, k4, k5, k10, k20, kmax int

	var cursor uint64
	var keys []string
	for {
		var err error
		keys, cursor, err = client.Scan(
			context.Background(),
			cursor,
			"secwall:requests*",
			100,
		).Result()
		if err != nil {
			fmt.Println("Error scanning keys:", err)
			return
		}

		for _, key := range keys {
			val, err := client.Get(context.Background(), key).Result()
			if err != nil {
				fmt.Printf("Error getting value for key %s: %v\n", key, err)
				continue
			}

			var res Request
			if err := json.Unmarshal([]byte(val), &res); err != nil {
				fmt.Printf("Error data format %s: %v\n", key, err)
				continue
			}
			fmt.Println("generate", res.CompletionTokens)
			if res.CompletionTokens <= 0 {
				continue
			}
			if res.CompletionTokens > 0 && res.CompletionTokens < 1000 {
				k1++
			} else if res.CompletionTokens > 1000 && res.CompletionTokens < 2000 {
				k2++
			} else if res.CompletionTokens > 2000 && res.CompletionTokens < 3000 {
				k3++
			} else if res.CompletionTokens > 3000 && res.CompletionTokens < 4000 {
				k4++
			} else if res.CompletionTokens > 4000 && res.CompletionTokens < 5000 {
				k5++
			} else if res.CompletionTokens < 10000 {
				k10++
			} else if res.CompletionTokens < 20000 {
				k20++
			} else {
				kmax++
			}
		}

		if cursor == 0 {
			break
		}
	}
}
