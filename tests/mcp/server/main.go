package main

import (
	"context"
	"errors"
	"fmt"

	"github.com/mark3labs/mcp-go/mcp"
	"github.com/mark3labs/mcp-go/server"
)

func main() {
	// Create a new MCP server
	s := server.NewMCPServer(
		"Calculator Demo",
		"1.0.0",
		server.WithResourceCapabilities(true, true),
		server.WithLogging(),
	)

	// Add a calculator tool
	calculatorTool := mcp.NewTool("calculate",
		mcp.WithDescription("Perform basic arithmetic operations"),
		mcp.WithString("operation",
			mcp.Required(),
			mcp.Description("The operation to perform (add, subtract, multiply, divide)"),
			mcp.Enum("add", "subtract", "multiply", "divide"),
		),
		mcp.WithNumber("x",
			mcp.Required(),
			mcp.Description("First number"),
		),
		mcp.WithNumber("y",
			mcp.Required(),
			mcp.Description("Second number"),
		),
	)

	// Add the calculator handler
	s.AddTool(calculatorTool, func(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
		// op := request.Params.Arguments["operation"].(string)
		// x := request.Params.Arguments["x"].(float64)
		// y := request.Params.Arguments["y"].(float64)
		op := "add"
		var x float64 = 1
		var y float64 = 1

		var result float64
		switch op {
		case "add":
			result = x + y
		case "subtract":
			result = x - y
		case "multiply":
			result = x * y
		case "divide":
			if y == 0 {
				return nil, errors.New("cannot divide by zero")
			}
			result = x / y
		}

		return mcp.NewToolResultResource(fmt.Sprintf("%.2f", result),
			mcp.TextResourceContents{
				URI:      "test://static/resource",
				MIMEType: "text/plain",
				Text:     "This is a sample resource",
			}), nil
		// return mcp.NewToolResultImage(fmt.Sprintf("%.2f", result), "data", "mime"), nil
		// return mcp.NewToolResultText(fmt.Sprintf("%.2f", result)), nil
	})

	// add other
	otherTool := mcp.NewTool("other",
		mcp.WithBoolean("b"),
	)
	s.AddTool(otherTool, func(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
		return mcp.NewToolResultImage("", "", ""), nil
	})

	// Start the server
	fmt.Println("mcp server: 127.0.0.1:8099")
	if err := server.NewSSEServer(s).Start(":8099"); err != nil {
		fmt.Printf("Server error: %v\n", err)
	}
}
