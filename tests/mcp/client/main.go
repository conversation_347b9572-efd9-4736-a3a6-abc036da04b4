package main

import (
	"context"
	"fmt"

	"github.com/mark3labs/mcp-go/client"
	"github.com/mark3labs/mcp-go/mcp"
)

func main() {
	c, err := client.NewSSEMCPClient(
		"http://127.0.0.1:8089/sse",
		client.WithHeaders(nil))
	if err != nil {
		return
	}

	if err := c.Start(context.TODO()); err != nil {
		return
	}
	defer c.Close()

	fmt.Println("Initializing client...")
	initRequest := mcp.InitializeRequest{}
	initRequest.Params.ProtocolVersion = mcp.LATEST_PROTOCOL_VERSION
	initRequest.Params.ClientInfo = mcp.Implementation{
		Name:    "secwalk-client",
		Version: "1.0.0",
	}
	initRequest.Params.Capabilities = mcp.ClientCapabilities{}

	initResult, err := c.Initialize(context.TODO(), initRequest)
	if err != nil {
		return
	}

	fmt.Println("Initializing server:", initResult.ServerInfo)

	toolResult, err := c.ListTools(context.TODO(), mcp.ListToolsRequest{})
	if err != nil {
		return
	}

	for _, v := range toolResult.Tools {
		fmt.Println("tool:", v.Name)
	}

	callRequest := mcp.CallToolRequest{}
	callRequest.Params.Name = "calculate"
	callRequest.Params.Arguments = map[string]any{
		"operation": "add",
		"x":         1,
		"y":         2,
	}
	callResult, err := c.CallTool(context.TODO(), callRequest)
	if err != nil {
		return
	}

	fmt.Println(callResult)
}
