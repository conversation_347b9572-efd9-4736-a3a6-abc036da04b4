package stream

import (
	"bufio"
	"bytes"
	"context"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"secwalk/internal/domain/core/callback"
	"secwalk/pkg/util"
	"strings"
	"testing"

	"github.com/sashabaranov/go-openai"
	"github.com/stretchr/testify/assert"
)

type Response struct {
	Data callback.PreviewMessage `json:"data"`
}

func TestStreamExecute(t *testing.T) {
	url := "http://************:8998/secwalk/v1/agent/execute"
	cli := &http.Client{
		Transport: &http.Transport{
			TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
		},
	}
	body, err := json.Marshal(map[string]any{
		"aid": "50e5a979-5b25-4791-8e86-620d5e57cba3",
		"inputs": map[string]any{
			"biddingFileId": "c77f66f9-dd62-4366-a355-99932c6fb0cd",
			"biddingParams": "★支持基于 ARIMA、RPCA-SST、Exponential Smoothing、Weekly Gaussian Estimation、TTT、LLM、ACL七种 AI 算法，动态展示各算法的分析结果、残差置信水平、风险异常分布等；支持按照时间范围维度进行筛选展示（提供产品截图或其他证明材料）；\\n★网络星空态势支持立体、平面、球面，天空，大地，水底等多种维度的网络实体关系透视，点击每个实体可展示资产名称、风险评级、告警 TOP3、最近异常发生时间等，点击实体间的访问连线可展示实体间访问方向、访问类型、累计流量、最近访问时间等（提供产品截图或其他证明材料）；\\n★资产态势感知支持以卡片形式展示每个资产的安全情况，包括 7 天告警统计、本日日志统计、漏洞统计、资产评分、资产 IP 及其资产风险状态、资产类型、资产重要性、组织架构、责任人等信息；支持点击资产威胁溯源，对资产进一步溯源分析，分析维度包括资产基本信息、攻击源排名、告警分布、趋势分析、攻击取证等；支持按照组织架构和资产名称进行筛选展示（提供产品截图或其他证明材料）；\\n★攻击者威胁溯源态势站在攻击者视角，为安全运维人员提供包括攻击者基本信息、攻击对象情况、告警分布、攻击趋势、攻击取证等信息，并支持一键生成导出攻击者取证报告，报告内容包括摘要及简报、攻击者行为分析、攻击团伙分析、威胁情报分析、分析结论与建议等；支持按照组织架构、时间范围、攻击 IP 等条件进行筛选展示（提供产品截图或其他证明材料）；\\n★平台运行状态监测支持监测安全运营情况，包括平台已运行天数、本周告警数、待办工单数、通报数；支持安全监测，监测内容包括已失陷资产数、风险资产数、安全域数量、WEB 业务系统数量、资产数量等；支持实时流量监控、日志吞吐量监控、运维告警监控、磁盘容量监控；支持 WAF、IDS、防火墙、EDR 等安全设备接入情况监测，支持失陷资产、横向威胁、外部态势、威胁狩猎、风险资产、业务全景、关联引擎、情报引擎等一键跳转（提供产品截图或其他证明材料）；\\n★为方便用户运维，告警展示条件支持用户自定义“偏好设置”，设置包括攻击结果、告警类型、威胁等级、攻击阶段、攻击方向、处置状态、时间范围等信息（提供产品截图或其他证明材料）；\\n★★支持用户自定义配置归并场景，支持场景名称、归并条件、归并字段、场景描述的配置，其中可根据字段过滤配置归并条件；",
			"input":         "",
		},
		"stream": true,
	})
	assert.NoError(t, err)

	req, err := http.NewRequestWithContext(
		context.Background(),
		"POST",
		url,
		bytes.NewReader(body),
	)
	assert.NoError(t, err)

	resp, err := cli.Do(req)
	assert.NoError(t, err)

	var msgID string
	reader := bufio.NewReader(resp.Body)
	for {
		rawLine, err := reader.ReadString('\n')
		if err == io.EOF {
			break
		}
		assert.NoError(t, err)

		noSpaceline := strings.TrimSpace(rawLine)
		if len(noSpaceline) == 0 {
			continue
		}
		line := strings.TrimPrefix(noSpaceline, "data:")
		line = strings.TrimSpace(line)

		if line == "[DONE]" {
			break
		}

		var data Response
		json.Unmarshal([]byte(line), &data)
		assert.NoError(t, err)

		if data.Data.MessageID != msgID {
			msgID = data.Data.MessageID
			fmt.Printf("\n%s:\n", data.Data.From)
		}
		if data.Data.Results != nil {
			fmt.Printf("\nresults:%s", util.ToJsonString(data.Data.Results))
		}
		fmt.Printf("%s", data.Data.Content)
	}
}

func TestModelCase(t *testing.T) {
	fmt.Println(util.ToJsonString(openai.ChatCompletionRequest{
		Model:      "HengNao-v4",
		TopK:       -1,
		TopP:       1,
		GuidedJson: "{\"type\": \"object\", \"properties\": {\"api_function_analysis\": {\"type\": \"string\"}, \"api_function_description\": {\"type\": \"string\"}, \"api_type\": {\"type\": \"string\", \"enum\": [\"login\", \"logout\", \"register\", \"upload\", \"download\", \"add\", \"delete\", \"update\", \"query\", \"message\", \"other\"]}, \"sensitive_information\": {\"type\": \"string\"}, \"result_tag\": {\"type\": \"array\", \"items\": {\"type\": \"string\", \"enum\": [\"纪要文件\", \"客户信息\", \"员工信息\", \"个人隐私信息\", \"工资单\", \"数据库配置信息\", \"制度文件\", \"合同信息\", \"单位通讯录\", \"货源\", \"产销策略\", \"财务信息\", \"投融资计划\", \"标书标底\", \"谈判方案\", \"项目信息\", \"其他\"]}, \"minItems\": 1, \"uniqueItems\": true}, \"result\": {\"type\": \"array\", \"items\": {\"type\": \"object\", \"properties\": {\"analysis\": {\"type\": \"string\"}, \"tag\": {\"type\": \"string\", \"enum\": [\"纪要文件\", \"客户信息\", \"员工信息\", \"个人隐私信息\", \"工资单\", \"数据库配置信息\", \"制度文件\", \"合同信息\", \"单位通讯录\", \"货源\", \"产销策略\", \"财务信息\", \"投融资计划\", \"标书标底\", \"谈判方案\", \"项目信息\", \"其他\"]}}, \"required\": [\"analysis\", \"tag\"]}}}, \"required\": [\"api_function_description\", \"result_tag\", \"api_type\", \"result\"]}",
		Messages: []openai.ChatCompletionMessage{
			{
				Role: openai.ChatMessageRoleSystem,
				Content: `[角色定义]
你是一名资料管理专员,具有组织性、细致观察力和优秀的数据管理能力。
	
[格式说明]
1.[功能用途和功能对应值]：格式为“功能用途：功能对应值”。
2.[信息标签和对应特征]：格式为“信息标签：对应特征”。
3.[HTTP报文]：HTTP报文中包含[请求报文]和[响应报文]。
	
[任务说明]
通过对[HTTP报文]中的[请求报文]和[响应报文]内容进行分析：
1.识别该HTTP报文的[功能用途]，给出你的[功能推理分析]，根据[功能推理分析]得出接口的[功能用途]。[功能推理分析]字数必须大于100字且小于200字，[功能用途]字数必须大于50字。
2.根据[功能用途]从[功能用途和功能对应值]中选择匹配的[功能对应值]。
3.按请求URL、请求体、响应体的顺序提取[HTTP报文]中全部非字段名信息中符合[信息标签和对应特征]中描述的[敏感信息]。并分别给出每个[信息标签]的[符合原因]。[符合原因]字数必须大于50字。
注意：
1.[功能推理分析]和[符合原因]的表达需要先给出支持结论的依据再给出结论的方式进行表述，必须确保输出内容全面、准确且推理逻辑正确，不能自相矛盾。
2.一个[功能用途]只有一个[功能对应值]，如果[功能用途]不属于上述任何[功能对应值]时，[功能对应值]为“other”。
3.[敏感信息]必须是接口上传输的非字段名信息，不是接口的字段名称。
4.一个[HTTP报文]可能属于多个[信息标签]，如果[HTTP报文]中没有符合上述任何[信息标签]时，则[信息标签]为“其他”。
5.[符合原因]中必须指出符合原因是因为请求URL、请求体、响应体中有哪些[敏感信息]符合了信息标签。
6.[符合原因]中必须先列出1至3个[敏感信息]，[敏感信息]存在以下情况：
	情况一：如果敏感信息属于某个字段则先列出[字段名]和对应[值]，形如:“'字段'的值为'值1'、...、'值n'”，再给出符合[信息标签]的结论。
	情况二：如果敏感信息不属于任何字段则先列出[敏感信息]，形如：“'值1'、...、'值n'”，再给出符合[信息标签]的结论。
	情况三：如果没有敏感信息，则需要指出在HTTP报文中没有发现敏感信息。
7.[符合原因]必须是正确的推理逻辑，不能自相矛盾。
8.必须严格按照[输出格式的json schema表达式]中定义的结构输出。
	
[输出格式的json schema表达式]
{"type": "object", "properties": {"api_function_analysis": {"type": "string"}, "api_function_description": {"type": "string"}, "api_type": {"type": "string", "enum": ["login", "logout", "register", "upload", "download", "add", "delete", "update", "query", "message", "other"]}, "sensitive_information": {"type": "string"}, "result_tag": {"type": "array", "items": {"type": "string", "enum": ["纪要文件", "客户信息", "员工信息", "个人隐私信息", "工资单", "数据库配置信息", "制度文件", "合同信息", "单位通讯录", "货源", "产销策略", "财务信息", "投融资计划", "标书标底", "谈判方案", "项目信息", "其他"]}, "minItems": 1, "uniqueItems": true}, "result": {"type": "array", "items": {"type": "object", "properties": {"analysis": {"type": "string"}, "tag": {"type": "string", "enum": ["纪要文件", "客户信息", "员工信息", "个人隐私信息", "工资单", "数据库配置信息", "制度文件", "合同信息", "单位通讯录", "货源", "产销策略", "财务信息", "投融资计划", "标书标底", "谈判方案", "项目信息", "其他"]}}, "required": ["analysis", "tag"]}}}, "required": ["api_function_description", "result_tag", "api_type", "result"]}
	
[输出字段说明]
1.api_function_analysis：该字段值是分析接口功能用途的[功能推理分析]。
2.api_function_description：该字段是用来描述接口的[功能用途]。
3.sensitive_information：提取到的[HTTP报文]请求URL、请求体、响应体中的所有[敏感信息]，敏感信息之间用“|”分隔。
4.api_type：该字段是[功能对应值]。
5.result：该字段用来保存http报文符合的[信息标签]和[符合原因]，tag是[信息标签]，analysis是对应的[符合原因]。
6.result_tag：该字段值是result字段中的全部tag值组成的一个列表。

[功能用途和功能对应值]
登录：login。登录接口中常用的登录凭证包含以下方式：用户名和密码;邮箱和密码;手机号和验证码;二维码扫描、指纹、面部识别等生物特征用作登录凭证;安全令牌或密钥;单点登录（SSO）。
登出：logout
注册：register
上传：upload
下载：download
增加：add
删除：delete
修改：update
查询：query
发消息：message
其他：other
`,
			},
			{
				Role: openai.ChatMessageRoleUser,
				Content: `[信息标签和对应特征]
纪要文件：内部讨论或会议的记录，可能包含业务信息。
客户信息：包含客户姓名、联系方式、购买记录等信息。
员工信息：包含员工的姓名、性别、工号、入职日期等基本个人信息和人力资源管理数据，可能涉及员工隐私。
个人隐私信息：包含个人信息，如身份证号、银行账号、联系方式等。
工资单：包含员工的薪资、奖金等信息。
数据库配置信息：包含数据库连接地址、账号和密码等信息，特别是密码信息需明确标识，非常重要。
制度文件：包含公司章程、组织架构、人事任命等具体制度内容，对公司运营至关重要。
合同信息：包含合同条款、价格、合作方等信息。
单位通讯录：包含姓名、职位、部门、电话号码、电子邮件地址等多组数据，可能涉及员工隐私。
货源：包含供应商信息、价格、采购量等信息。
产销策略：包含产品生产和销售的策略和计划，对公司运营至关重要。
财务信息：包含公司的财务报表、预算、成本等信息。
投融资计划：包含公司的投资和融资计划，对公司运营至关重要。
标书标底：包含投标书和标底，对公司运营至关重要。
谈判方案：包含谈判策略和计划，对公司运营至关重要。
项目信息：包含项目的计划、进度、预算等信息。
其他：不符合上述信息标签的其他信息。

[HTTP报文]
[请求报文]
'''
GET /ding/fapi/aht/contract/m9?sn=530001400121120306014003 HTTP/1.1
accept-encoding: gzip
connection: Keep-Alive
host: app2.eit-api.das-security.cn
user-agent: okhttp/4.10.0


'''

[响应报文]
'''
HTTP/1.1 200 OK
connection: keep-alive
content-length: 721
content-type: text/plain; charset=utf-8
date: Sat, 20 Apr 2024 22:01:05 GMT
server: nginx
x-frame-options: sameorigin

{"Total_ZS":1,"Total_CS":0,"LastUpTime_ZS":"","LastUpTime_CS":"","NextReloadTime":"","Item":[{"contractCode":"AH21-P12-1273","contractType":"正式合同","saleMode":"渠道","saleMan":"王幸涛","saleMobile":"18565056869","saleDep":"广州办","saleDepCode":"广州办","contractName":"2021韩山师范学院等级保护项目","companyName":"韩山师范学院","customerName":"","customerMobile":"","customerTel":"","signDate":"2021-12-23","devItem":{"pdName":"明御安全网关","pdMode":"DAS-Gateway","pdModule":"DAS-NGFW2900","pdServiceCode":"2112X284286","Status":"已发货","SendDate":"2021-12-27","StartDate":"2022-01-03","ExpDate":"2025-01-03","ServiceStatus":"","Sn":"","LicStartDate":"","LicExpDate":""}}]}
'''`,
			},
		},
	}))
}
