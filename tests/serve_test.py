
import aiohttp
import asyncio
import time
import json
from pydantic import BaseModel, Field
from typing import Dict, List, Optional, Union, Tuple

chat_url = f"http://10.50.10.18:8995/v1/chat/completions"
completions_url = f"http://10.50.10.18:8995/v1/completions"

TIMEOUT_KEEP_ALIVE = 5  # seconds.
TIMEOUT_TO_PREVENT_DEADLOCK = 1  # seconds.


class ChatCompletionRequest(BaseModel):
    model: str
    messages: Union[str, List[Dict[str, str]]]
    temperature: Optional[float] = 0.7
    top_p: Optional[float] = 1.0
    n: Optional[int] = 1
    max_tokens: Optional[int] = None
    stream: Optional[bool] = False
    repetition_penalty: Optional[float] = 1.0
    top_k: Optional[int] = -1
    lora_type: Optional[str] = None


class CompletionRequest(BaseModel):
    model: str
    prompt: str
    max_tokens: Optional[int] = 16
    temperature: Optional[float] = 0.0
    top_p: Optional[float] = 1.0
    n: Optional[int] = 1
    stream: Optional[bool] = False
    repetition_penalty: Optional[float] = 1.0
    # Additional parameters supported by vLLM
    top_k: Optional[int] = -1


def get_chat_request(query):
    BASE_SYSTEM_TEMPLATE = "你是由安恒信息技术股份有限公司开发的名为小恒的大语言模型。\n你需在确保安全、诚实、无害的同时，尽可能提供有帮助的回答。\n你的回答不应该包含任何有害、不道德、种族主义、性别歧视、有毒、危险或非法内容。\n如果一个问题没有任何意义或逻辑上不一致，请解释原因而不是给出不正确的答案。\n如果你不知道一个问题的答案，请不要提供错误的信息。\n在网络安全相关问题上，你的回答可以更深入、全面。"
    model = 'Qwen-v4'
    messages = [
        # {'role': 'system', "content": 'You are a helpful assistant.'},
        {'role': 'system', "content": BASE_SYSTEM_TEMPLATE},
        {'role': 'user', "content": f"{query}"}]
    chat_completion_request = ChatCompletionRequest(
        model=model, messages=messages)
    chat_completion_request.stream = True
    # chat_completion_request.max_tokens = GENERATE_PARAMS['max_new_tokens']
    chat_completion_request.max_tokens = 1024
    # chat_completion_request.top_p = 0.8
    # chat_completion_request.repetition_penalty = 1.1
    # chat_completion_request.temperature = 0.15

    chat_completion_request.top_p = 0.8
    chat_completion_request.repetition_penalty = 1.1
    chat_completion_request.temperature = 0.15
    return chat_completion_request, chat_url


class LLMClient:

    async def stream_chat(
        self,
        query: str,
    ):
        request, url = get_chat_request(query)
        # print('request: {}'.format(request.json(ensure_ascii=False)))

        headers = {"User-Agent": "Guanyu Client"}
        timeout = aiohttp.ClientTimeout(total=3 * 3600)
        rr = ''
        async with aiohttp.ClientSession(timeout=timeout) as session:
            async with session.post(url, headers=headers, json=request.dict()) as response:
                prompt = ''
                last_prompt = b''
                async for chunk, _ in response.content.iter_chunks():
                    # print('chunk: {}'.format(chunk))
                    if chunk[:6] == b'data: ':
                        prompt = chunk[6:]
                    else:
                        # prompt += chunk
                        prompt = ''
                        # continue
                    prompt = prompt.decode("utf-8").strip()
                    # print('rsp: {}'.format(rsp))
                    # rsp = rsp[6:]
                    if prompt == '[DONE]':
                        # print('over: ', prompt)
                        rsp = ''
                        print(rr)
                        return rr
                    else:
                        rsp = json.loads(prompt)
                        if "choices" in rsp and type(rsp["choices"]) is list and len(rsp["choices"]) > 0 \
                                and "delta" in rsp["choices"][0]:
                            if 'prompt' in rsp and rsp['prompt']:
                                pp = rsp['prompt']
                                # print('ppppppppppp: ', pp)
                            rsp = rsp["choices"][0]["delta"].get("content", '')

                        else:
                            rsp = 'error'
                        # prompt = json.loads(rsp)["choices"][0]["text"]
                    # prompt = rsp
                        # print(' rsp: ', rsp)
                    rr += rsp
                    # yield rsp
                    # print('prompt', prompt)
        # print('prompt ', rr)

        # print('time', int(time.monotonic()))
        # print('time', time.monotonic())
        rs = CompletionResponse(model='aaa')
        # print('time', rs.json)
        return rr


class CompletionResponse(BaseModel):
    object: str = "text_completion"
    created: int = Field(default_factory=lambda: int(time.time()))
    model: str


if __name__ == '__main__':
    original_loop = asyncio.get_event_loop()
    client = LLMClient()
    original_loop.run_until_complete(client.stream_chat('hello'))
