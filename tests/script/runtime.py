# coding=utf-8
import json


class AttrDict(dict):
    def __init__(self, **kwargs):
        for key, value in kwargs.items():
            self[key] = AttrDict(**value) if isinstance(value, dict) else value

    def __getattr__(self, item):
        try:
            return self[item]
        except KeyError:
            return None

    def __setattr__(self, key, value):
        if isinstance(value, dict):
            value = AttrDict(**value)
        self[key] = value


class Args:
    def __init__(self, inputs: str, level="debug"):
        self._input = AttrDict(**json.loads(inputs))

    @property
    def input(self) -> AttrDict:
        return self._input
