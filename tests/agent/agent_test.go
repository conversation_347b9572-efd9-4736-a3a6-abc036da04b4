package agent

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"secwalk/pkg/util"
	"sync"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func TestFuzzAgent(t *testing.T) {
	wg := sync.WaitGroup{}

	for i := 0; i < 1; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			url := "http://10.50.10.18:18502/secwalk/v1/agent/execute"
			method := "POST"

			data := map[string]any{
				"aid":    "366920b6-1dc4-4623-9e90-b6affad84e34",
				"inputs": map[string]any{"alarm_log": "{\n \"聚合告警ID\": \"SQL_INJECTION_ATTACK20230415\",\n \"聚合告警标题\": \"SQL注入攻击\",\n \"聚合告警生成时间\": \"2023-04-15 10:30:00\",\n \"聚合告警最后更新时间\": \"2023-04-15 11:00:00\",\n \"ATT&CK类型\": \"TA0010\",\n \"攻击详情\": {\n \"攻击类型\": \"SQL注入\",\n \"受害者资产\": {\n \"类型\": \"数据库服务器\",\n \"IP地址\": \"*************\",\n \"ip地区\": \"内网\"\n },\n \"攻击描述\": \"数据库服务器*************检测到多次异常SQL查询请求，这些请求尝试利用SQL注入漏洞获取敏感数据。攻击发生时间为2023-04-15 10:45:00。\",\n \"request\": \"GET /app/search.php?q=admin'%20union%20select%20'xx'%20into%20outfile%20'/tmp/xx.php' HTTP/1.1\\\\r\\\\nHost: *************\\\\r\\\\nUser-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.36\\\\r\\\\nAccept: text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8\\\\r\\\\nAccept-Language: en-US,en;q=0.5\\\\r\\\\nAccept-Encoding: gzip, deflate\\\\r\\\\nConnection: keep-alive\\\\r\\\\n\\\\r\\\\n\",\n \"response\":\"HTTP/1.1 404 Not Found\\\\r\\\\nServer: Tengine\\\\r\\\\nDate: Tue, 07 May 2024 02:58:05 GMT\\\\r\\\\nContent-Type: text/plain\\\\r\\\\nContent-Length: 0\\\\r\\\\nConnection: keep-alive\\\\r\\\\nKeep-Alive: timeout=5\\\\r\\\\n\"\n },\n \"相关告警\": [\n {\n \"告警ID\": [\n \"ALERT999888\"\n ],\n \"设备名称\": \"Web应用防火墙\",\n \"设备类型\": \"Web应用防火墙\",\n \"设备IP\": \"*************\",\n \"告警产生模块\": \"Web流量监测\"\n }\n ]\n}{\n \"聚合告警ID\": \"SQL_INJECTION_ATTACK20230415\",\n \"聚合告警标题\": \"SQL注入攻击\",\n \"聚合告警生成时间\": \"2023-04-15 10:30:00\",\n \"聚合告警最后更新时间\": \"2023-04-15 11:00:00\",\n \"ATT&CK类型\": \"TA0010\",\n \"攻击详情\": {\n \"攻击类型\": \"SQL注入\",\n \"受害者资产\": {\n \"类型\": \"数据库服务器\",\n \"IP地址\": \"*************\",\n \"ip地区\": \"内网\"\n },\n \"攻击描述\": \"数据库服务器*************检测到多次异常SQL查询请求，这些请求尝试利用SQL注入漏洞获取敏感数据。攻击发生时间为2023-04-15 10:45:00。\",\n \"request\": \"GET /app/search.php?q=admin'%20union%20select%20'xx'%20into%20outfile%20'/tmp/xx.php' HTTP/1.1\\\\r\\\\nHost: *************\\\\r\\\\nUser-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.36\\\\r\\\\nAccept: text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8\\\\r\\\\nAccept-Language: en-US,en;q=0.5\\\\r\\\\nAccept-Encoding: gzip, deflate\\\\r\\\\nConnection: keep-alive\\\\r\\\\n\\\\r\\\\n\",\n \"response\":\"HTTP/1.1 404 Not Found\\\\r\\\\nServer: Tengine\\\\r\\\\nDate: Tue, 07 May 2024 02:58:05 GMT\\\\r\\\\nContent-Type: text/plain\\\\r\\\\nContent-Length: 0\\\\r\\\\nConnection: keep-alive\\\\r\\\\nKeep-Alive: timeout=5\\\\r\\\\n\"\n },\n \"相关告警\": [\n {\n \"告警ID\": [\n \"ALERT999888\"\n ],\n \"设备名称\": \"Web应用防火墙\",\n \"设备类型\": \"Web应用防火墙\",\n \"设备IP\": \"*************\",\n \"告警产生模块\": \"Web流量监测\"\n }\n ]\n}"},
			}

			payload, err := json.Marshal(data)
			assert.NoError(t, err)

			client := &http.Client{}
			req, err := http.NewRequest(method, url, bytes.NewReader(payload))
			if err != nil {
				fmt.Println(err)
				return
			}
			req.Header.Add("EXT", "fix:dofuzz;")
			req.Header.Add("Content-Type", "application/json")

			res, err := client.Do(req)
			if err != nil {
				fmt.Println(err)
				return
			}
			defer res.Body.Close()

			body, err := io.ReadAll(res.Body)
			if err != nil {
				fmt.Println(err)
				return
			}
			fmt.Println(string(body))
		}()
		time.Sleep(time.Second)
	}

	wg.Wait()
}

func TestJson(t *testing.T) {
	data := []string{"123>321"}
	fmt.Println(util.ToJsonString(data))
}
