package main

import (
	"bufio"
	"crypto/tls"
	"fmt"
	"net/http"
	"time"

	"github.com/urfave/cli/v2"
)

var version = "dev"

func main() {
	app := cli.NewApp()
	app.Name = "mcp-proxy"
	app.Version = version
	app.Commands = []*cli.Command{
		{
			Name:    "connect",
			Aliases: []string{"conn"},
			Usage:   "connect mcp-hub",
			Flags: []cli.Flag{
				&cli.StringFlag{Name: "dst"},
				&cli.StringFlag{Name: "org"},
			},
			Action: connAction,
		},
	}
}

func connAction(ctx *cli.Context) error {
	trs := &http.Transport{
		TLSClientConfig:     &tls.Config{InsecureSkipVerify: true},
		MaxIdleConns:        100,
		MaxIdleConnsPerHost: 50,
		IdleConnTimeout:     90 * time.Second,
	}
	cli := &http.Client{Transport: trs}

	req, err := http.NewRequest(http.MethodPost, ctx.String("dst"), nil)
	if err != nil {
		return err
	}

	res, err := cli.Do(req)
	if err != nil {
		return err
	}
	defer res.Body.Close()
	defer trs.CloseIdleConnections()

	reader := bufio.NewReader(res.Body)
	for {
		data, err := reader.ReadBytes('\n')
		if err != nil {
			return err
		}

		fmt.Println(string(data))
	}
	return nil
}
