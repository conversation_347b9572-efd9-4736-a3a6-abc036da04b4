package otel

import (
	"context"
	"fmt"
	"secwalk/internal/config"
	"time"

	"github.com/sirupsen/logrus"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/exporters/otlp/otlptrace/otlptracehttp"
	"go.opentelemetry.io/otel/exporters/prometheus"
	"go.opentelemetry.io/otel/metric"
	"go.opentelemetry.io/otel/propagation"
	"go.opentelemetry.io/otel/sdk/instrumentation"
	sdkmetric "go.opentelemetry.io/otel/sdk/metric"
	"go.opentelemetry.io/otel/sdk/resource"
	sdktrace "go.opentelemetry.io/otel/sdk/trace"
	semconv "go.opentelemetry.io/otel/semconv/v1.26.0"
	"go.opentelemetry.io/otel/trace"
)

var (
	tracer trace.Tracer
	meter  metric.Meter
	
	// Token消耗指标
	tokenUsageCounter metric.Int64Counter
	tokenCostGauge    metric.Float64Gauge
	requestCounter    metric.Int64Counter
	requestDuration   metric.Float64Histogram
)

// 初始化OpenTelemetry
func Init(cfg config.OtelConfig) error {
	if !cfg.Enabled {
		logrus.Info("OpenTelemetry disabled")
		return nil
	}

	logrus.WithField("endpoint", cfg.Endpoint).Info("Initializing OpenTelemetry")

	// 创建资源
	res, err := createResource(cfg)
	if err != nil {
		return fmt.Errorf("failed to create resource: %w", err)
	}

	// 初始化链路追踪
	if cfg.Traces.Enabled {
		if err := initTracing(cfg, res); err != nil {
			return fmt.Errorf("failed to initialize tracing: %w", err)
		}
	}

	// 初始化指标
	if cfg.Metrics.Enabled {
		if err := initMetrics(cfg, res); err != nil {
			return fmt.Errorf("failed to initialize metrics: %w", err)
		}
	}

	// 设置传播器 - 支持EXT字段传递
	otel.SetTextMapPropagator(propagation.NewCompositeTextMapPropagator(
		propagation.TraceContext{},
		propagation.Baggage{},
	))

	// 创建tracer和meter
	tracer = otel.Tracer("secwalk")
	meter = otel.Meter("secwalk")

	// 初始化指标
	if err := initInstruments(); err != nil {
		return fmt.Errorf("failed to initialize instruments: %w", err)
	}

	logrus.Info("OpenTelemetry initialized successfully")
	return nil
}

// 创建资源
func createResource(cfg config.OtelConfig) (*resource.Resource, error) {
	attrs := []attribute.KeyValue{
		semconv.ServiceNameKey.String(cfg.ServiceName),
		semconv.ServiceVersionKey.String(config.GetVersion()),
		attribute.String("service.environment", "production"),
	}

	// 添加自定义资源属性
	for k, v := range cfg.Resource.Attributes {
		attrs = append(attrs, attribute.String(k, v))
	}

	return resource.Merge(
		resource.Default(),
		resource.NewWithAttributes(
			semconv.SchemaURL,
			attrs...,
		),
	)
}

// 初始化链路追踪
func initTracing(cfg config.OtelConfig, res *resource.Resource) error {
	// 创建OTLP HTTP导出器
	exporter, err := otlptracehttp.New(
		context.Background(),
		otlptracehttp.WithEndpoint(cfg.Endpoint),
		otlptracehttp.WithInsecure(cfg.Insecure),
		otlptracehttp.WithHeaders(cfg.Headers),
	)
	if err != nil {
		return err
	}

	// 创建采样器
	var sampler sdktrace.Sampler
	if cfg.Traces.SampleRate >= 1.0 {
		sampler = sdktrace.AlwaysSample()
	} else if cfg.Traces.SampleRate <= 0.0 {
		sampler = sdktrace.NeverSample()
	} else {
		sampler = sdktrace.TraceIDRatioBased(cfg.Traces.SampleRate)
	}

	// 创建TracerProvider
	tp := sdktrace.NewTracerProvider(
		sdktrace.WithBatcher(exporter),
		sdktrace.WithResource(res),
		sdktrace.WithSampler(sampler),
	)

	otel.SetTracerProvider(tp)
	return nil
}

// 初始化指标
func initMetrics(cfg config.OtelConfig, res *resource.Resource) error {
	// 创建Prometheus导出器
	exporter, err := prometheus.New()
	if err != nil {
		return err
	}

	// 创建MeterProvider
	mp := sdkmetric.NewMeterProvider(
		sdkmetric.WithResource(res),
		sdkmetric.WithReader(exporter),
	)

	otel.SetMeterProvider(mp)
	return nil
}

// 初始化指标工具
func initInstruments() error {
	var err error

	// Token使用计数器
	tokenUsageCounter, err = meter.Int64Counter(
		"llm_token_usage_total",
		metric.WithDescription("Total number of LLM tokens used"),
		metric.WithUnit("tokens"),
	)
	if err != nil {
		return err
	}

	// Token成本计量器
	tokenCostGauge, err = meter.Float64Gauge(
		"llm_token_cost_usd",
		metric.WithDescription("Cost of LLM tokens in USD"),
		metric.WithUnit("USD"),
	)
	if err != nil {
		return err
	}

	// 请求计数器
	requestCounter, err = meter.Int64Counter(
		"http_requests_total",
		metric.WithDescription("Total number of HTTP requests"),
	)
	if err != nil {
		return err
	}

	// 请求持续时间直方图
	requestDuration, err = meter.Float64Histogram(
		"http_request_duration_seconds",
		metric.WithDescription("HTTP request duration in seconds"),
		metric.WithUnit("s"),
	)
	if err != nil {
		return err
	}

	return nil
}

// 获取Tracer
func GetTracer() trace.Tracer {
	return tracer
}

// 获取Meter
func GetMeter() metric.Meter {
	return meter
}

// 记录Token使用
func RecordTokenUsage(ctx context.Context, promptTokens, completionTokens, totalTokens int64, userID, orgID, model string) {
	if tokenUsageCounter == nil {
		return
	}

	attrs := []attribute.KeyValue{
		attribute.String("user_id", userID),
		attribute.String("org_id", orgID),
		attribute.String("model", model),
		attribute.String("token_type", "prompt"),
	}
	tokenUsageCounter.Add(ctx, promptTokens, metric.WithAttributes(attrs...))

	attrs[3] = attribute.String("token_type", "completion")
	tokenUsageCounter.Add(ctx, completionTokens, metric.WithAttributes(attrs...))

	attrs[3] = attribute.String("token_type", "total")
	tokenUsageCounter.Add(ctx, totalTokens, metric.WithAttributes(attrs...))
}

// 记录Token成本
func RecordTokenCost(ctx context.Context, cost float64, userID, orgID, model string) {
	if tokenCostGauge == nil {
		return
	}

	attrs := []attribute.KeyValue{
		attribute.String("user_id", userID),
		attribute.String("org_id", orgID),
		attribute.String("model", model),
	}
	tokenCostGauge.Record(ctx, cost, metric.WithAttributes(attrs...))
}

// 记录HTTP请求
func RecordHTTPRequest(ctx context.Context, method, path string, statusCode int, duration time.Duration) {
	if requestCounter == nil || requestDuration == nil {
		return
	}

	attrs := []attribute.KeyValue{
		attribute.String("method", method),
		attribute.String("path", path),
		attribute.Int("status_code", statusCode),
	}

	requestCounter.Add(ctx, 1, metric.WithAttributes(attrs...))
	requestDuration.Record(ctx, duration.Seconds(), metric.WithAttributes(attrs...))
}

// 关闭OpenTelemetry
func Shutdown(ctx context.Context) error {
	if tp, ok := otel.GetTracerProvider().(*sdktrace.TracerProvider); ok {
		return tp.Shutdown(ctx)
	}
	return nil
}
