// Package util contins general helper functions.
package util

import (
	"bytes"
	"crypto/md5"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"errors"
	"net/url"
	"regexp"
	"strings"
)

// ToSet converts a list to a set.
func ToSet(list []string) map[string]struct{} {
	set := make(map[string]struct{}, 0)
	for _, v := range list {
		set[v] = struct{}{}
	}
	return set
}

// Difference returns the elements in list that are not in set.
func Difference(list []string, set map[string]struct{}) []string {
	diff := make([]string, 0)
	for _, v := range list {
		if _, ok := set[v]; !ok {
			diff = append(diff, v)
		}
	}
	return diff
}

// Intersection returns the elements in list that are in set.
func Intersection(list []string, set map[string]struct{}) []string {
	intersection := make([]string, 0)
	for _, v := range list {
		if _, ok := set[v]; ok {
			intersection = append(intersection, v)
		}
	}
	return intersection
}

// map中的key转换为列表
func ListKeys[T any](m map[string]T) []string {
	keys := make([]string, 0, len(m))
	for k := range m {
		keys = append(keys, k)
	}
	return keys
}

// 列表中是否存在子集
func ValueInList[T comparable](value T, list []T) bool {
	for _, v := range list {
		if v == value {
			return true
		}
	}
	return false
}

// 去重拼接list
func AppendDiff[T comparable](list []T, value T) []T {
	for _, v := range list {
		if v == value {
			return list
		}
	}
	return append(list, value)
}

// 获取Map中一个字符串值
func MapOneValue(values map[string]any) (string, error) {
	for _, v := range values {
		vv, ok := v.(string)
		if ok {
			return vv, nil
		}
	}
	return "", errors.New("not found string value in map")
}

func ToJsonString(v any) string {
	data, err := json.Marshal(v)
	if err != nil {
		return ""
	}
	return string(data)
}

func ToPureJsonString(v any) string {
	buffer := &bytes.Buffer{}
	encoder := json.NewEncoder(buffer)
	encoder.SetEscapeHTML(false)
	encoder.Encode(v)
	return strings.TrimSuffix(buffer.String(), "\n")
}

func Hash(data []byte) string {
	hash := sha256.Sum256(data)
	return hex.EncodeToString(hash[:])
}

func MD5(data []byte) string {
	hash := md5.Sum(data)
	return hex.EncodeToString(hash[:])
}

// 匹配IP地址的正则表达式
var ipv4Regex = regexp.MustCompile(`\b(?:\d{1,3}\.){3}\d{1,3}\b`)
var ipv6Regex = regexp.MustCompile(`\b([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}\b`)

func ContainIP(data string) bool {
	return ipv4Regex.MatchString(data) || ipv6Regex.MatchString(data)
}

// 匹配HTTP地址
var httpRegex = regexp.MustCompile(`https?://[^\s]+`)

func ContainHTTP(data string) bool {
	return httpRegex.MatchString(data)
}

// 是否包含中文
var zhRegex = regexp.MustCompile(`[\p{Han}]`)

func HasZH(data string) bool {
	return zhRegex.MatchString(data)
}

func ObjectToMap(v any) map[string]any {
	data, err := json.Marshal(v)
	if err != nil {
		return map[string]any{}
	}

	var ret map[string]any
	if err := json.Unmarshal(data, &ret); err != nil {
		return map[string]any{}
	}
	return ret
}

func URLDecode(data string) string {
	decodedStr, err := url.QueryUnescape(data)
	if err != nil {
		return data
	}
	return decodedStr
}
