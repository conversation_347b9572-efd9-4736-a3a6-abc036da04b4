package license

import (
	"encoding/json"
	"errors"
	"os"
	"sync"
	"time"

	"secwalk/internal/domain/core/endpoint"
	"secwalk/internal/domain/core/schema"
	"secwalk/pkg/logger"

	"github.com/sirupsen/logrus"
)

var (
	ErrLicenseExpired  = errors.New("license expired")
	ErrLicenseNotFound = errors.New("license not found")
	ErrLicenseType     = errors.New("license type not supported")
)

var DefaultKey = "aa675b38-ff66-4848-829b-693146ae790a"

var (
	mux sync.RWMutex
	lic = &endpoint.License{
		Model: endpoint.Model{Key: DefaultKey},
		Agent: endpoint.Agent{
			Licenses: []endpoint.AgentLicense{},
		},
	}

	envLic = []EnvLicense{}
)

type EnvLicense struct {
	ID   int    `json:"id"`
	Name string `json:"name"`
	Aid  string `json:"aid"`
	Key  string `json:"key"`
}

func Init() {
	data := os.Getenv("SECWALK_AGENT_KEYS")
	if err := json.Unmarshal([]byte(data), &envLic); err != nil {
		logrus.WithField(logger.KeyCategory, logger.CategorySystem).
			Warnf("env license fetch error: %s", err)
	}

	go func() {
		for {
			if err := Refresh(); err != nil {
				logrus.WithField(logger.KeyCategory, logger.CategorySystem).
					Warnf("license fetch error: %s", err)
			}
			time.Sleep(5 * time.Minute)
		}
	}()
}

// 获取智能体授权配置
func GetAgentKey(_type int, id string) (string, error) {
	mux.RLock()
	defer mux.RUnlock()

	switch _type {
	case schema.TypeUser:
		// 用户类型智能体，默认使用模型加密密钥，且拥有所有操作权限
		return lic.Model.Key, nil

	case schema.TypePerm:
		// 授权类型智能体
		// 在授权模式下，拥有所有操作权限，但是若没有找到授权ID对于的配置，直接报错
		// 在非授权模式下，按照授权配置限制操作
		for _, item := range lic.Agent.Licenses {
			if id == item.ID {
				return item.Key, nil
			}
		}

		for _, item := range envLic {
			if id == item.Aid {
				return item.Key, nil
			}
		}

		return lic.Model.Key, nil
	}

	return "", ErrLicenseNotFound
}

func Refresh() error {
	license, err := endpoint.GetLicense()
	if err != nil {
		return err
	}

	mux.Lock()
	if license != nil {
		lic = license
	}
	mux.Unlock()
	return nil
}
