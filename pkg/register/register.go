package register

import (
	"github.com/go-micro/plugins/v4/registry/consul"
	"github.com/go-micro/plugins/v4/registry/nacos"
	"github.com/hashicorp/consul/api"
	"go-micro.dev/v4/registry"
)

const (
	RegisterTypeConsul = "consul"
	RegisterTypeNacos  = "nacos"
)

type Config struct {
	Type     string `yaml:"type" json:"type,omitempty"`
	Host     string `yaml:"host" json:"host,omitempty"`
	Token    string `yaml:"token" json:"token,omitempty"`
	Username string `yaml:"username" json:"username,omitempty"`
	Password string `yaml:"password" json:"password,omitempty"`
}

func NewMicroRegister(cfg Config) registry.Registry {
	switch cfg.Type {
	case RegisterTypeConsul:
		config := api.DefaultNonPooledConfig()
		config.Address = cfg.Host
		config.Token = cfg.Token
		return consul.NewRegistry(consul.Config(config))
	case RegisterTypeNacos:
		return nacos.NewRegistry(registry.Addrs(cfg.Host))
	default:
		return registry.NewRegistry()
	}
}
