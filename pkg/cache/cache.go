package cache

import (
	"encoding/json"
	"errors"
	"sync"
)

const (
	TypeMemory = "memory"
)

type Cache interface {
	Get(key string, value any) error
	Put(key string, value any) error
}

type Config struct {
	Type     string
	Host     string
	Username string
	Password string
}

func NewCache(cfg *Config) (Cache, error) {
	switch cfg.Type {
	case TypeMemory:
		return NewMemoryCache(), nil
	default:
		return nil, errors.New("cache type not support")
	}
}

// memory cache
type MemoryCache struct {
	sync.RWMutex

	buf map[string]string
}

func NewMemoryCache() *MemoryCache {
	return &MemoryCache{
		RWMutex: sync.RWMutex{},
		buf:     make(map[string]string),
	}
}

func (c *MemoryCache) Get(key string, value any) error {
	c.RLock()
	defer c.RUnlock()

	buf, ok := c.buf[key]
	if !ok {
		return nil
	}

	return json.Unmarshal([]byte(buf), value)
}

func (c *MemoryCache) Put(key string, value any) error {
	c.Lock()
	defer c.<PERSON>()

	buf, err := json.Marshal(value)
	if err != nil {
		return err
	}

	c.buf[key] = string(buf)
	return nil
}
