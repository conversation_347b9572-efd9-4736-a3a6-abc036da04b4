package token

import (
	"fmt"
	"secwalk/pkg/random"
	"strings"
	"testing"
	"time"
)

var data = `最近微软曝出了一些安全漏洞，以下是其中的三个：

1. **开放管理基础设施（OMI）远程代码执行漏洞**：
   - 发现日期：2023年12月9日
   - CVE ID：CVE-2024-21334
   - CNNVD ID：CNNVD-202403-1033
   - 描述：该漏洞可能导致远程代码执行，CVSS 3.1评分为9.8，属于高危漏洞。

2. **开放管理基础设施（OMI）提升特权漏洞**：
   - 发现日期：2023年12月9日
   - CVE ID：CVE-2024-21330
   - CNNVD ID：CNNVD-202403-1031
   - 描述：该漏洞可能导致权限提升，CVSS 3.1评分为7.8，属于中高危漏洞。

3. **Windows NT CVE-1999-0591 Remote Security Vulnerability**：
   - 发现日期：1999年1月1日
   - CVE ID：CVE-1999-0591
   - CNNVD ID：CNNVD-199901-105
   - 描述：Windows NT的事件日志存在不适当的访问权限，可能导致完全控制，CVSS 2评分为10，CVSS 3.1评分为7.8，属于高危漏洞。

这些漏洞的详细信息和缓解措施需要进一步查看微软官方的安全公告或相关安全资源以获取。建议及时更新受影响的产品以修复这些漏洞。
`

func TestFakeStream(t *testing.T) {
	word := ""
	full := ""
	for index := range data {
		rand := random.RandomInt(5, 30)
		word += strings.TrimPrefix(data[:index], full)
		full = data[:index]

		if (rand%2 == 1 || len(word) > 4) && len(word) > 0 {
			fmt.Println(word)
			word = ""
			time.Sleep(time.Duration(rand) * time.Millisecond)
		}
	}

	if len(word) > 0 {
		fmt.Println(word)
	}
}
