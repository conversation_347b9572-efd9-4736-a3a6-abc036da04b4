package token

import (
	_ "embed"
	"encoding/base64"
	"fmt"
	"strconv"
	"strings"
	"sync"

	"github.com/pkoukk/tiktoken-go"
)

//go:embed qwen.tiktoken
var qwenTiToken string
var once sync.Once
var TikToken *Tiktoken

type Tiktoken struct {
	*tiktoken.Tiktoken
}

func NewTokenCounter() (*Tiktoken, error) {

	if TikToken != nil {
		return TikToken, nil
	}

	enc := &tiktoken.Encoding{
		Name:           "qwen_base",
		PatStr:         `'s|'t|'re|'ve|'m|'ll|'d| ?\p{L}+| ?\p{N}+| ?[^\s\p{L}\p{N}]+|\s+(?!\S)|\s+`,
		SpecialTokens:  map[string]int{tiktoken.ENDOFTEXT: 50256},
		ExplicitNVocab: 50257,
	}

	TikToken = &Tiktoken{
		Tiktoken: nil,
	}

	once.Do(
		func() {
			ranks, err := loadTiktokenBpe()
			if err != nil {
				return
			}
			enc.MergeableRanks = ranks
			pbe, err := tiktoken.NewCoreBPE(enc.MergeableRanks, enc.SpecialTokens, enc.PatStr)
			if err != nil {
				return
			}
			specialTokensSet := map[string]any{}
			for k := range enc.SpecialTokens {
				specialTokensSet[k] = true
			}
			TikToken.Tiktoken = tiktoken.NewTiktoken(pbe, enc, specialTokensSet)
		},
	)

	if TikToken.Tiktoken == nil {
		return nil, fmt.Errorf("failed to init token checker")
	}

	return TikToken, nil
}

// From https://github.com/pkoukk/tiktoken-go/blob/main/load.go#L74
func loadTiktokenBpe() (map[string]int, error) {
	bpeRanks := make(map[string]int)
	for _, line := range strings.Split(qwenTiToken, "\n") {
		if line == "" {
			continue
		}
		parts := strings.Split(line, " ")
		token, err := base64.StdEncoding.DecodeString(parts[0])
		if err != nil {
			return nil, err
		}
		rank, err := strconv.Atoi(strings.TrimSpace(parts[1]))
		if err != nil {
			return nil, err
		}
		bpeRanks[string(token)] = rank
	}
	return bpeRanks, nil
}

func (c *Tiktoken) CountTokens(text string) int {
	return len(c.Encode(text, nil, nil))
}

func (c *Tiktoken) CutTokens(text string) []string {
	ws := make([]string, 0)
	tokens := c.Encode(text, nil, nil)
	for _, token := range tokens {
		ws = append(ws, c.Decode([]int{token}))
	}
	return ws
}
