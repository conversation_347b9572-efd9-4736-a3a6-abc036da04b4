/*
 * Copyright 2022 by Mel2<PERSON> <https://github.com/saferun/owl>
 *
 * Licensed under the GNU General Public License version 3 (GPLv3)
 *
 * If you distribute GPL-licensed software the license requires
 * that you also distribute the complete, corresponding source
 * code (as defined by GPL) to that GPL-licensed software.
 *
 * You should have received a copy of the GNU General Public License
 * with this program. If not, see <https://www.gnu.org/licenses/>
 */

package logger

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"runtime"
	"secwalk/internal/domain/core/schema/ext"
	"strings"

	nested "github.com/antonfisher/nested-logrus-formatter"
	"github.com/sirupsen/logrus"
	"gopkg.in/natefinch/lumberjack.v2"
)

const (
	TimeFormat = "2006-01-02T15:04:05.000Z0700"
)

const (
	KeyCategory = "category"
	KeyEXT      = "ext"
	KeyTID      = "tid"
	KeySID      = "sid"
	KeyAID      = "aid"
)

const (
	CategorySystem    = "system"
	CategoryInterface = "interface"
	CategoryService   = "service"
	CategorySelector  = "selector"
	CategoryDatabase  = "database"
	CategoryCore      = "core"
)

type Config struct {
	App       string `yaml:"app" json:"app,omitempty"`
	Level     string `yaml:"level" json:"level,omitempty"`
	Format    bool   `yaml:"format" json:"format,omitempty"`
	Output    string `yaml:"output" json:"output,omitempty"`
	MaxSize   int    `yaml:"max_size" json:"max_size,omitempty"`
	MaxAge    int    `yaml:"max_age" json:"max_age,omitempty"`
	MaxBackup int    `yaml:"max_backup" json:"max_backup,omitempty"`
}

func Init(cfg *Config) error {
	// setup log formatter
	if cfg.Format {
		logrus.SetFormatter(&SimpleFormatter{})
	} else {
		logrus.SetFormatter(&nested.Formatter{TimestampFormat: TimeFormat, HideKeys: false})
	}

	level, err := logrus.ParseLevel(cfg.Level)
	if err != nil {
		return err
	}
	logrus.SetLevel(level)

	// initialize log rotate hook
	rhook, err := NewHook(cfg, level)
	if err != nil {
		return err
	}

	l := logrus.StandardLogger()
	for _, level := range rhook.Levels() {
		l.Hooks[level] = []logrus.Hook{rhook}
	}
	return err
}

type SimpleFormatter struct{}

func (f *SimpleFormatter) Format(e *logrus.Entry) ([]byte, error) {
	data := map[string]any{
		"timestamp":   e.Time.Format(TimeFormat),
		"application": e.Data["app"],
		"category":    e.Data["category"],
		"module":      e.Data["module"],
		"level":       strings.ToUpper(e.Level.String()),
		"file":        e.Data["file"],
		"msg":         e.Message,
	}

	if e.Level == logrus.WarnLevel {
		data["level"] = strings.ToUpper("Warn")
	}

	if _, ok := e.Data[KeyTID]; ok {
		data[KeyTID] = e.Data[KeyTID]
	}
	if _, ok := e.Data[KeySID]; ok {
		data[KeySID] = e.Data[KeySID]
	}
	if _, ok := e.Data[KeyAID]; ok {
		data[KeyAID] = e.Data[KeyAID]
	}

	b := &bytes.Buffer{}
	if err := json.NewEncoder(b).Encode(data); err != nil {
		return nil, err
	}
	return b.Bytes(), nil
}

// File represents the rotate file hook.
type File struct {
	app       string
	level     logrus.Level
	formatter logrus.Formatter
	filew     io.Writer
}

// NewHook builds a new rotate file hook.
func NewHook(cfg *Config, level logrus.Level) (logrus.Hook, error) {
	return &File{
		app:       cfg.App,
		level:     level,
		formatter: &SimpleFormatter{},
		filew: &lumberjack.Logger{
			Filename:   cfg.Output,
			MaxSize:    cfg.MaxSize,
			MaxAge:     cfg.MaxAge,
			MaxBackups: cfg.MaxBackup,
		},
	}, nil
}

// Levels determines log levels that for which the logs are written.
func (hook *File) Levels() []logrus.Level {
	return logrus.AllLevels[:hook.level+1]
}

// Fire is called by logrus when it is about to write the log entry.
func (hook *File) Fire(entry *logrus.Entry) (err error) {
	entry.Data["app"] = hook.app

	filepaths := findCaller()
	entry.Data["file"] = filepaths

	_, ok := entry.Data["category"].(string)
	if !ok {
		entry.Data["category"] = "system" // default category
	}

	_, ok = entry.Data["module"].(string)
	if !ok {
		files := strings.Split(filepaths, "/")
		if len(files) > 1 {
			entry.Data["module"] = files[0]
		} else {
			entry.Data["module"] = "main"
		}
	}

	extstr, ok := entry.Data[KeyEXT].(ext.ExtType)
	if ok {
		if tid := extstr.GetValue(ext.EXTTID); tid != "" {
			entry.Data[KeyTID] = tid
		}

		if sid := extstr.GetValue(ext.EXTSID); sid != "" {
			entry.Data[KeySID] = sid
		}

		if aid := extstr.GetValue(ext.EXTAID); aid != "" {
			entry.Data[KeyAID] = aid
		}

		delete(entry.Data, KeyEXT)
	}

	data, err := hook.formatter.Format(entry)
	if err != nil {
		return err
	}

	// write to file
	if hook.filew != nil {
		hook.filew.Write(data)
	}

	return err
}

func findCaller() string {
	var (
		file string
		line int
	)

	for i := 0; i < 20; i++ {
		file, line = getCaller(i + 5)
		if !skipFile(file) {
			break
		}
	}

	return fmt.Sprintf("%s:%d", file, line)
}

var skipPrefixes = []string{"logrus/", "logrus@", "v4@", "logger/"}

func skipFile(file string) bool {
	for i := range skipPrefixes {
		if strings.HasPrefix(file, skipPrefixes[i]) {
			return true
		}
	}
	return false
}

func getCaller(skip int) (string, int) {
	_, file, line, ok := runtime.Caller(skip)
	if !ok {
		return "", 0
	}

	n := 0
	for i := len(file) - 1; i > 0; i-- {
		if file[i] == '/' {
			n++
			if n >= 2 {
				file = file[i+1:]
				break
			}
		}
	}

	return file, line
}
