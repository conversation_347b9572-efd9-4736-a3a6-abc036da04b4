package es

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"strings"

	"github.com/elastic/go-elasticsearch/v7"
	"github.com/tidwall/gjson"
)

type Config struct {
	Addresses []string `yaml:"addresses" json:"addresses,omitempty"`
	Username  string   `yaml:"username" json:"username,omitempty"`
	Password  string   `yaml:"password" json:"password,omitempty"`
}

type Client struct {
	*elasticsearch.Client
}

func New(cfg Config) (*Client, error) {
	client, err := elasticsearch.NewClient(elasticsearch.Config{
		Addresses: cfg.Addresses,
		Username:  cfg.Username,
		Password:  cfg.Password,
	})
	if err != nil {
		return nil, err
	}

	return &Client{Client: client}, nil
}

func (c *Client) QueryAll(ctx context.Context, index string) ([][]byte, error) {
	res, err := c.Search(
		c.Search.WithIndex(index),
		c.Search.WithBody(strings.NewReader(`{ "size": 1000, "query": { "match_all": {} } }`)),
		c.Search.WithContext(ctx),
	)
	if err != nil {
		return nil, err
	}
	defer res.Body.Close()

	if res.IsError() {
		return nil, fmt.Errorf("search all document error: %s", res)
	}

	data, err := io.ReadAll(res.Body)
	if err != nil {
		return nil, err
	}

	list := make([]any, 0)
	if err := json.Unmarshal(
		[]byte(gjson.GetBytes(data, "hits.hits.#._source").Raw), &list); err != nil {
		return nil, err
	}

	rds := make([][]byte, 0)
	for _, l := range list {
		d, err := json.Marshal(l)
		if err != nil {
			return nil, err
		}
		rds = append(rds, d)
	}
	return rds, nil
}

func (c *Client) Query(ctx context.Context, index, docid string) ([]byte, error) {
	res, err := c.GetSource(index, docid,
		c.GetSource.WithContext(ctx),
	)
	if err != nil {
		return nil, err
	}
	defer res.Body.Close()

	if res.IsError() {
		if res.StatusCode == http.StatusNotFound {
			return nil, fmt.Errorf("document not found: %s", docid)
		}
		return nil, fmt.Errorf("document not found: %s", res)
	}

	return io.ReadAll(res.Body)
}

func (c *Client) Create(ctx context.Context, index, docid string, r *bytes.Reader) error {
	res, err := c.Index(index, r,
		c.Index.WithContext(ctx),
		c.Index.WithDocumentID(docid),
		c.Index.WithOpType("create"),
	)
	if err != nil {
		return err
	}
	defer res.Body.Close()

	if res.IsError() {
		if res.StatusCode == http.StatusConflict { // 409: conflict, document already exists.
			return errors.New("cannot create index: already exists")
		}
		return fmt.Errorf("cannot create index: %s", res)
	}

	return nil
}

func (c *Client) Update(ctx context.Context, index, docid string, r *bytes.Reader) error {
	res, err := c.Index(index, r,
		c.Index.WithContext(ctx),
		c.Index.WithDocumentID(docid),
		c.Index.WithRefresh("true"),
	)
	if err != nil {
		return err
	}
	defer res.Body.Close()

	if res.IsError() {
		return fmt.Errorf("cannot update index: %s", res)
	}

	return nil
}

func (c *Client) Upsert(ctx context.Context, index, docid string, r *bytes.Reader) error {
	res, err := c.Index(index, r,
		c.Index.WithContext(ctx),
		c.Index.WithDocumentID(docid),
		c.Index.WithRefresh("true"),
	)
	if err != nil {
		return err
	}
	defer res.Body.Close()

	if res.IsError() {
		return fmt.Errorf("cannot upsert index: %s", res)
	}

	return nil
}

func (c *Client) Delete(ctx context.Context, index, docid string) error {
	res, err := c.Client.Delete(index, docid, c.Client.Delete.WithContext(ctx))
	if err != nil {
		return err
	}
	defer res.Body.Close()

	if res.IsError() {
		return fmt.Errorf("cannot delete index: %s", res)
	}

	return nil
}
