package minio

import (
	"bytes"
	"context"
	"io"

	"github.com/minio/minio-go/v7"
	"github.com/minio/minio-go/v7/pkg/credentials"
)

var _client *Client

func G() *Client { return _client }

type Config struct {
	Host      string `yaml:"host" json:"host,omitempty"`
	AccessKey string `yaml:"access_key" json:"access_key,omitempty"`
	SecretKey string `yaml:"secret_key" json:"secret_key,omitempty"`
}

type Client struct {
	*minio.Client
}

func Init(cfg Config) error {
	cli, err := minio.New(cfg.Host, &minio.Options{
		Creds: credentials.NewStaticV4(cfg.AccessKey, cfg.SecretKey, ""),
	})
	if err != nil {
		return err
	}

	_client = &Client{cli}
	return nil
}

func (c *Client) checkBucket(ctx context.Context, bucket string) error {
	exists, err := c.Client.BucketExists(ctx, bucket)
	if err == nil && exists {
		return nil
	}

	return c.Client.MakeBucket(ctx, bucket, minio.MakeBucketOptions{})
}

func (c *Client) QueryAll(ctx context.Context, bucket string) ([][]byte, error) {
	if err := c.checkBucket(ctx, bucket); err != nil {
		return nil, err
	}

	res := make([][]byte, 0)

	for obj := range c.Client.ListObjects(ctx, bucket, minio.ListObjectsOptions{}) {
		if obj.Err != nil {
			return nil, obj.Err
		}

		obj, err := c.Client.GetObject(ctx, bucket, obj.Key, minio.GetObjectOptions{})
		if err != nil {
			return nil, err
		}
		defer obj.Close()

		buf, err := io.ReadAll(obj)
		if err != nil {
			return nil, err
		}
		res = append(res, buf)
	}

	return res, nil
}

func (c *Client) Query(ctx context.Context, bucket, docid string) ([]byte, error) {
	if err := c.checkBucket(ctx, bucket); err != nil {
		return nil, err
	}

	obj, err := c.Client.GetObject(ctx, bucket, docid, minio.GetObjectOptions{})
	if err != nil {
		return nil, err
	}
	defer obj.Close()

	return io.ReadAll(obj)
}

func (c *Client) QueryStat(ctx context.Context, bucket, docid string) (int64, error) {
	if err := c.checkBucket(ctx, bucket); err != nil {
		return 0, err
	}

	obj, err := c.Client.StatObject(ctx, bucket, docid, minio.StatObjectOptions{})
	if err != nil {
		return 0, err
	}

	return obj.Size, nil
}

func (c *Client) Create(ctx context.Context, bucket, docid string, r *bytes.Reader) error {
	return c.Upsert(ctx, bucket, docid, r)
}

func (c *Client) Update(ctx context.Context, bucket, docid string, r *bytes.Reader) error {
	return c.Upsert(ctx, bucket, docid, r)
}

func (c *Client) Upsert(ctx context.Context, bucket, docid string, r *bytes.Reader) error {
	if err := c.checkBucket(ctx, bucket); err != nil {
		return err
	}

	_, err := c.Client.PutObject(ctx, bucket, docid,
		r, r.Size(), minio.PutObjectOptions{})
	return err
}

func (c *Client) Delete(ctx context.Context, bucket, docid string) error {
	if err := c.checkBucket(ctx, bucket); err != nil {
		return err
	}

	return c.Client.RemoveObject(ctx, bucket, docid, minio.RemoveObjectOptions{})
}
