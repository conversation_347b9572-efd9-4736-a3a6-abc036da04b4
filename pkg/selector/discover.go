package selector

import (
	"errors"
	"fmt"
	"secwalk/pkg/register"
	"strings"

	"github.com/hashicorp/consul/api"
)

const DiscoverTypeDirect = "direct"

type Discover interface {
	Get() ([]Instance, error)
}

type Instance struct {
	ID    string            `json:"id,omitempty"`
	Name  string            `json:"name,omitempty"`
	Host  string            `json:"host,omitempty"`
	Token string            `json:"token,omitempty"`
	Metas map[string]string `json:"metas,omitempty"`
}

func NewDiscover(cfg *Config) (Discover, error) {
	switch strings.ToLower(cfg.Type) {
	case register.RegisterTypeConsul:
		return newConsulDiscover(cfg)
	case DiscoverTypeDirect:
		return NewDirectDiscover(cfg)
	}

	return nil, errors.New("discover type not support")
}

// 通过consul发现服务实例
type ConsulDiscover struct {
	server string
	client *api.Client
	metas  map[string]string
}

func newConsulDiscover(cfg *Config) (Discover, error) {
	config := api.DefaultConfig()
	config.Address = cfg.Host
	config.Token = cfg.Token

	client, err := api.NewClient(config)
	if err != nil {
		return nil, err
	}

	return &ConsulDiscover{
		server: cfg.Name,
		client: client,
		metas:  cfg.Metas,
	}, nil
}

func (d *ConsulDiscover) Get() ([]Instance, error) {
	instances := make([]Instance, 0)
	services, err := d.client.Agent().Services()
	if err != nil {
		return nil, err
	}

	for i, v := range services {
		if v.Service == d.server {
			for k1, v1 := range d.metas {
				services[i].Meta[k1] = v1
			}

			instances = append(instances, Instance{
				Name:  v.Service,
				ID:    v.ID,
				Host:  fmt.Sprintf("%s:%d", v.Address, v.Port),
				Metas: services[i].Meta,
			})
		}
	}

	return instances, nil
}

// 直连使用
type DirectDiscover struct {
	host  string
	metas map[string]string
}

func NewDirectDiscover(cfg *Config) (Discover, error) {
	return &DirectDiscover{
		host:  cfg.Host,
		metas: cfg.Metas,
	}, nil
}

func (d *DirectDiscover) Get() ([]Instance, error) {
	return []Instance{{ID: "direct", Host: d.host, Metas: d.metas}}, nil
}
