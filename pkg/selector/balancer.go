package selector

import (
	"errors"
	"math/rand"
	"sync"
	"sync/atomic"
	"time"
)

var ErrNoHost = errors.New("host none")

const (
	StrategyRandom     = "random"
	StrategyRoundRobin = "round-robin"
)

type Balancer interface {
	Add(...string)
	Remove(...string)
	Balance() (string, error)
}

func NewBalancer(strategy string, hosts ...string) (Balancer, error) {
	switch strategy {
	case StrategyRandom:
		return NewRandomBalancer(hosts...), nil
	case StrategyRoundRobin:
		return NewRoundRobinBalancer(hosts...), nil
	default:
		return NewRandomBalancer(hosts...), nil
	}
}

// 基础均衡器
type BaseBalancer struct {
	mutex sync.RWMutex
	hosts []string
}

func (b *BaseBalancer) Add(hosts ...string) {
	b.mutex.Lock()
	defer b.mutex.Unlock()

LOOP:
	for _, host := range hosts {
		for _, h := range b.hosts {
			if h == host {
				continue LOOP
			}
		}
		b.hosts = append(b.hosts, host)
	}
}

func (b *BaseBalancer) Remove(hosts ...string) {
	b.mutex.Lock()
	defer b.mutex.Unlock()

	for _, host := range hosts {
		for i, h := range b.hosts {
			if h != host {
				continue
			}
			b.hosts = append(b.hosts[:i], b.hosts[i+1:]...)
			break
		}
	}
}

func (b *BaseBalancer) Balance() (string, error) {
	return "", nil
}

// random
type RandomBalancer struct {
	BaseBalancer

	rand *rand.Rand
}

func NewRandomBalancer(hosts ...string) Balancer {
	return &RandomBalancer{
		BaseBalancer: BaseBalancer{hosts: hosts},
		rand:         rand.New(rand.NewSource(time.Now().Unix())),
	}
}

func (b *RandomBalancer) Balance() (string, error) {
	b.mutex.RLock()
	defer b.mutex.RUnlock()

	if len(b.hosts) == 0 {
		return "", ErrNoHost
	}

	return b.hosts[b.rand.Intn(len(b.hosts))], nil
}

// round-robin
type RoundRobinBalancer struct {
	BaseBalancer

	i atomic.Uint64
}

func NewRoundRobinBalancer(hosts ...string) Balancer {
	return &RoundRobinBalancer{
		BaseBalancer: BaseBalancer{hosts: hosts},
		i:            atomic.Uint64{},
	}
}

func (b *RoundRobinBalancer) Balance() (string, error) {
	b.mutex.RLock()
	defer b.mutex.RUnlock()

	if len(b.hosts) == 0 {
		return "", ErrNoHost
	}

	return b.hosts[b.i.Add(1)%uint64(len(b.hosts))], nil
}
