package selector

import (
	"fmt"
	"secwalk/pkg/register"
	"testing"

	"github.com/stretchr/testify/require"
)

func TestConsulDiscover(t *testing.T) {
	d, err := newConsulDiscover(&Config{
		Config: &register.Config{
			Type:  "consul",
			Host:  "***********:8500",
			Token: "d9295a36-9f55-4580-b22d-71cfa5837f76",
		},
		Name: "go.micro.srv.secwalk",
	})
	require.NoError(t, err)

	ins, err := d.Get()
	require.NoError(t, err)

	fmt.Println(ins)
}
