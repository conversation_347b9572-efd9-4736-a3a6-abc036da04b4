package selector

import (
	"errors"
	"secwalk/pkg/cache"
	"secwalk/pkg/logger"
	"secwalk/pkg/register"
	"time"

	"github.com/sirupsen/logrus"
)

var _selectors Selectors

func G() Selectors { return _selectors }

const (
	_BackendServiceNameKey = "backend"
	_GatewayServiceNameKey = "gateway"
	_SecvirtServiceNameKey = "secvirt"
	_HBRAGServiceNameKey   = "hbrag"
	_OpenAPoServiceNameKey = "openapi"
)

type Config struct {
	*register.Config

	Name     string            `yaml:"name" json:"name,omitempty"`
	Strategy string            `yaml:"strategy" json:"strategy,omitempty"`
	Metas    map[string]string `yaml:"metas" json:"metas,omitempty"`
}

type Selectors map[string]*Selector

func Init(cfgs map[string]Config) error {
	_selectors = make(Selectors)

	for k, v := range cfgs {
		sel, err := NewSelector(&v)
		if err != nil {
			return err
		}

		_selectors[k] = sel
	}

	return nil
}

func (s Selectors) BackendInstance() (*Instance, error) {
	sel, ok := s[_BackendServiceNameKey]
	if !ok {
		return nil, errors.New("backend service not found")
	}

	return sel.GetInstance()
}

func (s Selectors) OpenApiInstance() (*Instance, error) {
	sel, ok := s[_OpenAPoServiceNameKey]
	if !ok {
		return nil, errors.New("openapi service not found")
	}

	return sel.GetInstance()
}

func (s Selectors) GatewayInstance() (*Instance, error) {
	sel, ok := s[_GatewayServiceNameKey]
	if !ok {
		return nil, errors.New("gateway service not found")
	}

	return sel.GetInstance()
}

func (s Selectors) SecvirtInstance() (*Instance, error) {
	sel, ok := s[_SecvirtServiceNameKey]
	if !ok {
		return nil, errors.New("secvirt service not found")
	}

	return sel.GetInstance()
}

func (s Selectors) HBRAGInstance() (*Instance, error) {
	sel, ok := s[_HBRAGServiceNameKey]
	if !ok {
		return nil, errors.New("hb-rag service not found")
	}

	return sel.GetInstance()
}

func (s Selectors) BackendAddress() (string, error) {
	ins, err := s.BackendInstance()
	if err != nil {
		return "", err
	}

	return ins.Host, nil
}

func (s Selectors) GatewayAddress() (string, error) {
	ins, err := s.GatewayInstance()
	if err != nil {
		return "", err
	}

	return ins.Host, nil
}

func (s Selectors) SecvirtAddress() (string, error) {
	ins, err := s.SecvirtInstance()
	if err != nil {
		return "", err
	}

	return ins.Host, nil
}

func (s Selectors) HBRAGAddress() (string, error) {
	ins, err := s.HBRAGInstance()
	if err != nil {
		return "", err
	}

	return ins.Host, nil
}

type Selector struct {
	sv    string
	dc    Discover
	bl    Balancer
	cache cache.Cache
}

func NewSelector(cfg *Config, opts ...Option) (*Selector, error) {
	// 服务发现
	dc, err := NewDiscover(cfg)
	if err != nil {
		return nil, err
	}

	// 负载均衡
	bl, err := NewBalancer(cfg.Strategy)
	if err != nil {
		return nil, err
	}

	fd := &Selector{
		sv:    cfg.Name,
		dc:    dc,
		bl:    bl,
		cache: cache.NewMemoryCache(),
	}

	for _, opt := range opts {
		opt(fd)
	}

	// 定时刷新实例信息
	go fd.refresh()

	time.Sleep(time.Millisecond * 100)
	return fd, nil
}

func (f *Selector) GetInstance() (*Instance, error) {
	id, err := f.bl.Balance()
	if err != nil {
		return nil, err
	}

	ins := make([]Instance, 0)
	if err := f.cache.Get(f.sv, &ins); err != nil {
		return nil, err
	}

	for _, i := range ins {
		if i.ID == id {
			return &i, nil
		}
	}

	return nil, errors.New("instance not found")
}

func (f *Selector) refresh() {
	for {
		// 查询缓存实例信息
		oldins := make([]Instance, 0)
		if err := f.cache.Get(f.sv, &oldins); err != nil {
			continue
		}

		// 获取当前实例信息
		newins, err := f.dc.Get()
		if err != nil {
			continue
		}

	REMOVE:
		// 移除已下线实例
		for i := 0; i < len(oldins); i++ {
			for _, v := range newins {
				if oldins[i].ID == v.ID {
					continue REMOVE
				}
			}

			logrus.WithField(logger.KeyCategory, logger.CategorySelector).
				Debugf("remove instance: %s", oldins[i].ID)
			// 从负载均衡器中移除
			f.bl.Remove(oldins[i].ID)
			// 从实例缓存中移除
			oldins = append(oldins[:i], oldins[i+1:]...)
			i--
		}

	ONLINE:
		// 添加新上线实例
		for _, new := range newins {
			for _, old := range oldins {
				if new.ID == old.ID {
					continue ONLINE
				}
			}

			logrus.WithField(logger.KeyCategory, logger.CategorySelector).
				Debugf("online instance: %s %s %s", f.sv, new.ID, new.Host)
			// 加入实例缓存
			oldins = append(oldins, new)
			// 加入负载均衡器
			f.bl.Add(new.ID)
		}

		// 更新缓存
		f.cache.Put(f.sv, oldins)

		// 10秒后刷新
		time.Sleep(time.Second * 10)
	}
}
