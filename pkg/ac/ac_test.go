package ac

import (
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestAC(t *testing.T) {
	kws := []string{
		"hers", "his", "she", "he",
	}

	ac, err := New(kws)
	assert.NoError(t, err)

	search := ac.MultiPatternSearch([]rune("ushers"))
	for _, v := range search {
		fmt.Printf("%d\t%d\t%s\n", v.Begin, v.End, string(v.Value))
	}
}

func TestSearchStream(t *testing.T) {
	kws := []string{
		"中华人民", "中华人民共和国",
	}

	ac, err := New(kws)
	assert.NoError(t, err)

	streamf := ac.MultiPatternHitSearchStream()

	for _, v := range []string{"中华", "北京", "中华人民", "中华", "人民", "听我说中华人", "民"} {
		hit, ok, _ := streamf([]rune(v))
		if ok {
			fmt.Printf("%d\t%d\t%s\n", hit.Begin, hit.End, v)
		}
	}
}
