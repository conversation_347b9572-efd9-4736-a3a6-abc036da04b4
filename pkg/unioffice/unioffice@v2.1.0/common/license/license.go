//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

// Package license helps manage commercial licenses and check if they
// are valid for the version of UniOffice used.
package license ;import _g "github.com/unidoc/unioffice/v2/internal/license";

// SetMeteredKey sets the metered License API key required for SaaS operation.
// Document usage is reported periodically for the product to function correctly.
func SetMeteredKey (apiKey string )error {return _g .SetMeteredKey (apiKey )};

// GetLicenseKey returns the currently loaded license key.
func GetLicenseKey ()*LicenseKey {return _g .GetLicenseKey ()};

// SetMeteredKeyUsageLogVerboseMode sets the metered License API Key usage log verbose mode.
// Default value `false`, set to `true` will log the credit usages and print out to console with log level INFO.
func SetMeteredKeyUsageLogVerboseMode (val bool ){_g .SetMeteredKeyUsageLogVerboseMode (val )};

// LicenseKey represents a loaded license key.
type LicenseKey =_g .LicenseKey ;

// LegacyLicenseType is the type of license
type LegacyLicenseType =_g .LegacyLicenseType ;

// SetMeteredKeyPersistentCache sets the metered License API Key persistent cache.
// Default value `true`, set to `false` will report the usage immediately to license server,
// this can be used when there's no access to persistent data storage.
func SetMeteredKeyPersistentCache (val bool ){_g .SetMeteredKeyPersistentCache (val )};

// SetLicenseKey sets and validates the license key.
func SetLicenseKey (content string ,customerName string )error {return _g .SetLicenseKey (content ,customerName );};

// MakeUnlicensedKey returns a default key.
func MakeUnlicensedKey ()*LicenseKey {return _g .MakeUnlicensedKey ()};

// GetMeteredState checks the currently used metered document usage status,
// documents used and credits available.
func GetMeteredState ()(_g .MeteredStatus ,error ){return _g .GetMeteredState ()};const (LicenseTierUnlicensed =_g .LicenseTierUnlicensed ;LicenseTierCommunity =_g .LicenseTierCommunity ;LicenseTierIndividual =_g .LicenseTierIndividual ;LicenseTierBusiness =_g .LicenseTierBusiness ;
);

// LegacyLicense holds the old-style unioffice license information.
type LegacyLicense =_g .LegacyLicense ;