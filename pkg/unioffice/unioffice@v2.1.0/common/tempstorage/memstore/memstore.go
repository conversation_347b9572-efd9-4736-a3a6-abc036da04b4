//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

// Package memstore implements tempStorage interface
// by using memory as a storage
package memstore ;import (_g "encoding/hex";_ae "errors";_a "fmt";_ga "github.com/unidoc/unioffice/v2/common/tempstorage";_d "io";_e "io/ioutil";_dc "math/rand";_db "sync";);

// Read reads from the underlying memDataCell in order to implement Reader interface
func (_fe *memFile )Read (p []byte )(int ,error ){_c :=_fe ._dg ;_fb :=_fe ._gaa ._fa ;_fef :=int64 (len (p ));if _fef > _fb {_fef =_fb ;p =p [:_fef ];};if _c >=_fb {return 0,_d .EOF ;};_eb :=_c +_fef ;if _eb >=_fb {_eb =_fb ;};_cb :=copy (p ,_fe ._gaa ._be [_c :_eb ]);
_fe ._dg =_eb ;return _cb ,nil ;};

// Add reads a file from a disk and adds it to the storage
func (_edb *memStorage )Add (path string )error {_ ,_baa :=_edb ._gg .Load (path );if _baa {return nil ;};_ge ,_gdd :=_e .ReadFile (path );if _gdd !=nil {return _gdd ;};_edb ._gg .Store (path ,&memDataCell {_ggg :path ,_be :_ge ,_fa :int64 (len (_ge ))});
return nil ;};

// Write writes to the end of the underlying memDataCell in order to implement Writer interface
func (_fea *memFile )Write (p []byte )(int ,error ){_fea ._gaa ._be =append (_fea ._gaa ._be ,p ...);_fea ._gaa ._fa +=int64 (len (p ));return len (p ),nil ;};

// Open returns tempstorage File object by name
func (_cd *memStorage )Open (path string )(_ga .File ,error ){_aed ,_cbb :=_cd ._gg .Load (path );if !_cbb {return nil ,_ae .New (_a .Sprintf ("\u0043\u0061\u006eno\u0074\u0020\u006f\u0070\u0065\u006e\u0020\u0074\u0068\u0065\u0020\u0066\u0069\u006c\u0065\u0020\u0025\u0073",path ));
};return &memFile {_gaa :_aed .(*memDataCell )},nil ;};

// ReadAt reads from the underlying memDataCell at an offset provided in order to implement ReaderAt interface.
// It does not affect f.readOffset.
func (_ce *memFile )ReadAt (p []byte ,readOffset int64 )(int ,error ){_ed :=_ce ._gaa ._fa ;_gd :=int64 (len (p ));if _gd > _ed {_gd =_ed ;p =p [:_gd ];};if readOffset >=_ed {return 0,_d .EOF ;};_ab :=readOffset +_gd ;if _ab >=_ed {_ab =_ed ;};_b :=copy (p ,_ce ._gaa ._be [readOffset :_ab ]);
return _b ,nil ;};func _ea (_bb string )string {_dde ,_ :=_dce (6);return _bb +_dde };type memDataCell struct{_ggg string ;_be []byte ;_fa int64 ;};

// SetAsStorage sets temp storage as a memory storage
func SetAsStorage (){_ba :=memStorage {_gg :_db .Map {}};_ga .SetAsStorage (&_ba )};

// RemoveAll removes all files according to the dir argument prefix
func (_cf *memStorage )RemoveAll (dir string )error {_cf ._gg .Range (func (_gad ,_fc interface{})bool {_cf ._gg .Delete (_gad );return true });return nil ;};type memFile struct{_gaa *memDataCell ;_dg int64 ;};

// TempDir creates a name for a new temp directory using a pattern argument
func (_ddd *memStorage )TempDir (pattern string )(string ,error ){return _ea (pattern ),nil };

// Name returns the filename of the underlying memDataCell
func (_fbe *memFile )Name ()string {return _fbe ._gaa ._ggg };type memStorage struct{_gg _db .Map };

// TempFile creates a new empty file in the storage and returns it
func (_df *memStorage )TempFile (dir ,pattern string )(_ga .File ,error ){_dd :=dir +"\u002f"+_ea (pattern );_cbg :=&memDataCell {_ggg :_dd ,_be :[]byte {}};_af :=&memFile {_gaa :_cbg };_df ._gg .Store (_dd ,_cbg );return _af ,nil ;};func _dce (_cff int )(string ,error ){_fab :=make ([]byte ,_cff );
if _ ,_bc :=_dc .Read (_fab );_bc !=nil {return "",_bc ;};return _g .EncodeToString (_fab ),nil ;};

// Close is not applicable in this implementation
func (_dga *memFile )Close ()error {return nil };