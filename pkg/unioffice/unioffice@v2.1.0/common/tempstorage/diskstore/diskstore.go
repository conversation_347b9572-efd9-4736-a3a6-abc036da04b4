//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

// Package diskstore implements tempStorage interface
// by using disk as a storage
package diskstore ;import (_eg "github.com/unidoc/unioffice/v2/common/tempstorage";_c "io/ioutil";_ee "os";_e "strings";);

// Open opens file from disk according to a path
func (_d diskStorage )Open (path string )(_eg .File ,error ){return _ee .OpenFile (path ,_ee .O_RDWR ,0644);};

// TempFile creates a new temp directory by calling ioutil TempDir
func (_a diskStorage )TempDir (pattern string )(string ,error ){return _c .TempDir ("",pattern )};type diskStorage struct{};

// RemoveAll removes all files in the directory
func (_cg diskStorage )RemoveAll (dir string )error {if _e .HasPrefix (dir ,_ee .TempDir ()){return _ee .RemoveAll (dir );};return nil ;};

// TempFile creates a new temp file by calling ioutil TempFile
func (_f diskStorage )TempFile (dir ,pattern string )(_eg .File ,error ){return _c .TempFile (dir ,pattern );};

// Add is not applicable in the diskstore implementation
func (_dd diskStorage )Add (path string )error {return nil };

// SetAsStorage sets temp storage as a disk storage
func SetAsStorage (){_b :=diskStorage {};_eg .SetAsStorage (&_b )};