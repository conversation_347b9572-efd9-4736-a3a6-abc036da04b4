//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package logger ;import (_g "fmt";_ag "io";_ce "os";_c "path/filepath";_ae "runtime";);const (LogLevelTrace LogLevel =5;LogLevelDebug LogLevel =4;LogLevelInfo LogLevel =3;LogLevelNotice LogLevel =2;LogLevelWarning LogLevel =1;LogLevelError LogLevel =0;);


// Warning does nothing for dummy logger.
func (DummyLogger )Warning (format string ,args ...interface{}){};

// ConsoleLogger is a logger that writes logs to the 'os.Stdout'
type ConsoleLogger struct{LogLevel LogLevel ;};

// NewWriterLogger creates new 'writer' logger.
func NewWriterLogger (logLevel LogLevel ,writer _ag .Writer )*WriterLogger {logger :=WriterLogger {Output :writer ,LogLevel :logLevel };return &logger ;};

// Error logs error message.
func (_gb ConsoleLogger )Error (format string ,args ...interface{}){if _gb .LogLevel >=LogLevelError {_ddc :="\u005b\u0045\u0052\u0052\u004f\u0052\u005d\u0020";_gb .output (_ce .Stdout ,_ddc ,format ,args ...);};};

// LogLevel is the verbosity level for logging.
type LogLevel int ;

// Notice does nothing for dummy logger.
func (DummyLogger )Notice (format string ,args ...interface{}){};

// Warning logs warning message.
func (_gbg WriterLogger )Warning (format string ,args ...interface{}){if _gbg .LogLevel >=LogLevelWarning {_bd :="\u005b\u0057\u0041\u0052\u004e\u0049\u004e\u0047\u005d\u0020";_gbg .logToWriter (_gbg .Output ,_bd ,format ,args ...);};};

// Info logs info message.
func (_ea WriterLogger )Info (format string ,args ...interface{}){if _ea .LogLevel >=LogLevelInfo {_ceb :="\u005bI\u004e\u0046\u004f\u005d\u0020";_ea .logToWriter (_ea .Output ,_ceb ,format ,args ...);};};

// DummyLogger does nothing.
type DummyLogger struct{};

// Debug logs debug message.
func (_db ConsoleLogger )Debug (format string ,args ...interface{}){if _db .LogLevel >=LogLevelDebug {_ac :="\u005b\u0044\u0045\u0042\u0055\u0047\u005d\u0020";_db .output (_ce .Stdout ,_ac ,format ,args ...);};};

// IsLogLevel returns true from dummy logger.
func (DummyLogger )IsLogLevel (level LogLevel )bool {return true };

// Debug does nothing for dummy logger.
func (DummyLogger )Debug (format string ,args ...interface{}){};

// Info logs info message.
func (_f ConsoleLogger )Info (format string ,args ...interface{}){if _f .LogLevel >=LogLevelInfo {_cad :="\u005bI\u004e\u0046\u004f\u005d\u0020";_f .output (_ce .Stdout ,_cad ,format ,args ...);};};func (_cg WriterLogger )logToWriter (_dac _ag .Writer ,_ff string ,_gg string ,_eb ...interface{}){_abc (_dac ,_ff ,_gg ,_eb );
};

// IsLogLevel returns true if log level is greater or equal than `level`.
// Can be used to avoid resource intensive calls to loggers.
func (_bfa ConsoleLogger )IsLogLevel (level LogLevel )bool {return _bfa .LogLevel >=level };

// Debug logs debug message.
func (_bfcd WriterLogger )Debug (format string ,args ...interface{}){if _bfcd .LogLevel >=LogLevelDebug {_ba :="\u005b\u0044\u0045\u0042\u0055\u0047\u005d\u0020";_bfcd .logToWriter (_bfcd .Output ,_ba ,format ,args ...);};};func _abc (_cga _ag .Writer ,_dbd string ,_aee string ,_eg ...interface{}){_ ,_gdf ,_gea ,_fd :=_ae .Caller (3);
if !_fd {_gdf ="\u003f\u003f\u003f";_gea =0;}else {_gdf =_c .Base (_gdf );};_ec :=_g .Sprintf ("\u0025s\u0020\u0025\u0073\u003a\u0025\u0064 ",_dbd ,_gdf ,_gea )+_aee +"\u000a";_g .Fprintf (_cga ,_ec ,_eg ...);};

// Notice logs notice message.
func (_bfc ConsoleLogger )Notice (format string ,args ...interface{}){if _bfc .LogLevel >=LogLevelNotice {_cec :="\u005bN\u004f\u0054\u0049\u0043\u0045\u005d ";_bfc .output (_ce .Stdout ,_cec ,format ,args ...);};};

// Info does nothing for dummy logger.
func (DummyLogger )Info (format string ,args ...interface{}){};

// Error logs error message.
func (_cd WriterLogger )Error (format string ,args ...interface{}){if _cd .LogLevel >=LogLevelError {_fc :="\u005b\u0045\u0052\u0052\u004f\u0052\u005d\u0020";_cd .logToWriter (_cd .Output ,_fc ,format ,args ...);};};

// NewConsoleLogger creates new console logger.
func NewConsoleLogger (logLevel LogLevel )*ConsoleLogger {return &ConsoleLogger {LogLevel :logLevel }};

// Warning logs warning message.
func (_dc ConsoleLogger )Warning (format string ,args ...interface{}){if _dc .LogLevel >=LogLevelWarning {_e :="\u005b\u0057\u0041\u0052\u004e\u0049\u004e\u0047\u005d\u0020";_dc .output (_ce .Stdout ,_e ,format ,args ...);};};

// Error does nothing for dummy logger.
func (DummyLogger )Error (format string ,args ...interface{}){};

// Trace logs trace message.
func (_afd ConsoleLogger )Trace (format string ,args ...interface{}){if _afd .LogLevel >=LogLevelTrace {_ee :="\u005b\u0054\u0052\u0041\u0043\u0045\u005d\u0020";_afd .output (_ce .Stdout ,_ee ,format ,args ...);};};

// IsLogLevel returns true if log level is greater or equal than `level`.
// Can be used to avoid resource intensive calls to loggers.
func (_ga WriterLogger )IsLogLevel (level LogLevel )bool {return _ga .LogLevel >=level };func (_fg ConsoleLogger )output (_dae _ag .Writer ,_bb string ,_agb string ,_bg ...interface{}){_abc (_dae ,_bb ,_agb ,_bg ...);};

// Trace does nothing for dummy logger.
func (DummyLogger )Trace (format string ,args ...interface{}){};var Log Logger =DummyLogger {};

// SetLogger sets 'logger' to be used by the unidoc unipdf library.
func SetLogger (logger Logger ){Log =logger };

// Trace logs trace message.
func (_ge WriterLogger )Trace (format string ,args ...interface{}){if _ge .LogLevel >=LogLevelTrace {_ab :="\u005b\u0054\u0052\u0041\u0043\u0045\u005d\u0020";_ge .logToWriter (_ge .Output ,_ab ,format ,args ...);};};

// Notice logs notice message.
func (_ddb WriterLogger )Notice (format string ,args ...interface{}){if _ddb .LogLevel >=LogLevelNotice {_dde :="\u005bN\u004f\u0054\u0049\u0043\u0045\u005d ";_ddb .logToWriter (_ddb .Output ,_dde ,format ,args ...);};};

// Logger is the interface used for logging in the unipdf package.
type Logger interface{Error (_ca string ,_d ...interface{});Warning (_af string ,_cb ...interface{});Notice (_cf string ,_cbb ...interface{});Info (_b string ,_gd ...interface{});Debug (_da string ,_be ...interface{});Trace (_de string ,_bf ...interface{});
IsLogLevel (_dd LogLevel )bool ;};

// WriterLogger is the logger that writes data to the Output writer
type WriterLogger struct{LogLevel LogLevel ;Output _ag .Writer ;};