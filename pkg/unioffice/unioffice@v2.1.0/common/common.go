//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

// Package common contains wrapper types and utilities common to all of the
// OOXML document formats.
//
// Package common contains common properties used by the subpackages.
package common ;import (_db "archive/zip";_ec "bytes";_bg "encoding/xml";_ae "errors";_ff "fmt";_fb "github.com/unidoc/unioffice/v2";_ce "github.com/unidoc/unioffice/v2/common/logger";_ceg "github.com/unidoc/unioffice/v2/common/tempstorage";_fc "github.com/unidoc/unioffice/v2/common/tempstorage/diskstore";
_be "github.com/unidoc/unioffice/v2/measurement";_eag "github.com/unidoc/unioffice/v2/schema/soo/dml";_dbe "github.com/unidoc/unioffice/v2/schema/soo/ofc/custom_properties";_c "github.com/unidoc/unioffice/v2/schema/soo/ofc/docPropsVTypes";_dae "github.com/unidoc/unioffice/v2/schema/soo/ofc/extended_properties";
_de "github.com/unidoc/unioffice/v2/schema/soo/pkg/content_types";_fg "github.com/unidoc/unioffice/v2/schema/soo/pkg/metadata/core_properties";_ee "github.com/unidoc/unioffice/v2/schema/soo/pkg/relationships";_g "github.com/unidoc/unioffice/v2/zippkg";
_df "image";_ "image/gif";_ "image/jpeg";_ "image/png";_ed "os";_b "reflect";_f "regexp";_dg "strconv";_ea "strings";_da "time";);

// X returns the inner wrapped XML type.
func (_fgf AppProperties )X ()*_dae .Properties {return _fgf ._ffe };

// AddOverride adds an override content type for a given path name.
func (_dgdc ContentTypes )AddOverride (path ,contentType string ){if !_ea .HasPrefix (path ,"\u002f"){path ="\u002f"+path ;};if _ea .HasPrefix (contentType ,"\u0068\u0074\u0074\u0070"){_ce .Log .Debug ("\u0063\u006f\u006e\u0074\u0065\u006et\u0020\u0074\u0079p\u0065\u0020\u0027%\u0073\u0027\u0020\u0069\u0073\u0020\u0069\u006e\u0063\u006fr\u0072\u0065\u0063\u0074\u002c m\u0075\u0073\u0074\u0020\u006e\u006f\u0074\u0020\u0073\u0074\u0061\u0072\u0074\u0020\u0077\u0069\u0074\u0068\u0020\u0068\u0074\u0074\u0070",contentType );
};for _ ,_cfb :=range _dgdc ._cde .TypesChoice {if _cfb .Override !=nil &&_cfb .Override .PartNameAttr ==path {return ;};};_gae :=_de .NewOverride ();_gae .PartNameAttr =path ;_gae .ContentTypeAttr =contentType ;_dd :=_de .NewCT_TypesChoice ();_dd .Override =_gae ;
_dgdc ._cde .TypesChoice =append (_dgdc ._cde .TypesChoice ,_dd );};

// ContentTypes is the top level "[Content_Types].xml" in a zip package.
type ContentTypes struct{_cde *_de .Types };

// Table represents a table in the document.
type Table struct{_ebe *_eag .Tbl ;_ecaa *_eag .CT_Transform2D ;};func (_bbad CustomProperties )SetPropertyAsNull (name string ){_dcae :=_bbad .getNewProperty (name );_dcae .PropertyChoice .Null =_c .NewNull ();_bbad .setOrReplaceProperty (_dcae );};const Version ="\u0032\u002e\u0031.\u0030";
const _bdbc ="\u0032\u0020\u004aan\u0075\u0061\u0072\u0079\u0020\u0032\u0030\u0030\u0036\u0020\u0061\u0074\u0020\u0031\u0035\u003a\u0030\u0034";func (_bcff CustomProperties )SetPropertyAsUi1 (name string ,ui1 uint8 ){_gda :=_bcff .getNewProperty (name );
_gda .PropertyChoice .Ui1 =&ui1 ;_bcff .setOrReplaceProperty (_gda );};

// CustomProperty contains document specific property.
// Using of this type is deprecated.
type CustomProperty struct{_ecda *_dbe .CT_Property };const _beae =21;

// X returns the inner raw content types.
func (_cc ContentTypes )X ()*_de .Types {return _cc ._cde };

// RemoveOverride removes an override given a path.
func (_caf ContentTypes )RemoveOverride (path string ){if !_ea .HasPrefix (path ,"\u002f"){path ="\u002f"+path ;};for _efg ,_ade :=range _caf ._cde .TypesChoice {_fd :=_ade .Override ;if _fd !=nil &&_fd .PartNameAttr ==path {copy (_caf ._cde .TypesChoice [_efg :],_caf ._cde .TypesChoice [_efg +1:]);
_caf ._cde .TypesChoice =_caf ._cde .TypesChoice [0:len (_caf ._cde .TypesChoice )-1];};};};

// AddAutoRelationship adds a relationship with an automatically generated
// filename based off of the type. It should be preferred over AddRelationship
// to ensure consistent filenames are maintained.
func (_eceb Relationships )AddAutoRelationship (dt _fb .DocType ,src string ,idx int ,ctype string )Relationship {return _eceb .AddRelationship (_fb .RelativeFilename (dt ,src ,ctype ,idx ),ctype );};

// X returns the inner wrapped XML type.
func (_eef CustomProperties )X ()*_dbe .Properties {return _eef ._acgb };

// Relationship is a relationship within a .rels file.
type Relationship struct{_fdc *_ee .Relationship };func (_faa CustomProperties )SetPropertyAsDate (name string ,date _da .Time ){date =date .UTC ();_cfbe ,_ffc ,_fgbf :=date .Date ();_abc ,_bbab ,_bea :=date .Clock ();_dccc :=_da .Date (_cfbe ,_ffc ,_fgbf ,_abc ,_bbab ,_bea ,0,_da .UTC );
_ggf :=_faa .getNewProperty (name );_ggf .PropertyChoice .Filetime =&_dccc ;_faa .setOrReplaceProperty (_ggf );};

// SetDocSecurity sets the document security flag.
func (_edc AppProperties )SetDocSecurity (v int32 ){_edc ._ffe .DocSecurity =_fb .Int32 (v )};

// SetCompany sets the name of the company that created the document.
func (_agb AppProperties )SetCompany (s string ){_agb ._ffe .Company =&s };

// ImageFromFile reads an image from a file on disk. It doesn't keep the image
// in memory and only reads it to determine the format and size. You can also
// construct an Image directly if the file and size are known.
// NOTE: See also ImageFromStorage.
func ImageFromFile (path string )(Image ,error ){_fdb ,_bad :=_ed .Open (path );_afgd :=Image {};if _bad !=nil {return _afgd ,_ff .Errorf ("\u0065\u0072\u0072or\u0020\u0072\u0065\u0061\u0064\u0069\u006e\u0067\u0020\u0069\u006d\u0061\u0067\u0065\u003a\u0020\u0025\u0073",_bad );
};defer _fdb .Close ();_ceef ,_aab ,_bad :=_df .Decode (_fdb );if _bad !=nil {return _afgd ,_ff .Errorf ("\u0075n\u0061\u0062\u006c\u0065 \u0074\u006f\u0020\u0070\u0061r\u0073e\u0020i\u006d\u0061\u0067\u0065\u003a\u0020\u0025s",_bad );};_afgd .Path =path ;
_afgd .Format =_aab ;_afgd .Size =_ceef .Bounds ().Size ();return _afgd ,nil ;};

// SetOffsetX sets horizontal offset of a table in distance units (see measurement package).
func (_efbe Table )SetOffsetX (offX float64 ){if _efbe ._ecaa .Off ==nil {_efbe ._ecaa .Off =_eag .NewCT_Point2D ();_afdd :=int64 (0);_efbe ._ecaa .Off .YAttr =_eag .ST_Coordinate {ST_CoordinateUnqualified :&_afdd };};_fab :=_be .ToEMU (offX );_efbe ._ecaa .Off .XAttr =_eag .ST_Coordinate {ST_CoordinateUnqualified :&_fab };
};

// NewTableWithXfrm makes a new table with a pointer to its parent Xfrm for changing its offset and size.
func NewTableWithXfrm (xfrm *_eag .CT_Transform2D )*Table {_acec :=_eag .NewTbl ();_acec .TblPr =_eag .NewCT_TableProperties ();return &Table {_ebe :_acec ,_ecaa :xfrm };};

// AddRelationship adds a relationship.
func (_fcg Relationships )AddRelationship (target ,ctype string )Relationship {if !_ea .HasPrefix (ctype ,"\u0068t\u0074\u0070\u003a\u002f\u002f"){_ce .Log .Debug ("\u0072\u0065\u006c\u0061\u0074\u0069\u006f\u006es\u0068\u0069\u0070 t\u0079\u0070\u0065\u0020\u0025\u0073 \u0073\u0068\u006f\u0075\u006c\u0064\u0020\u0073\u0074\u0061\u0072\u0074\u0020\u0077\u0069t\u0068\u0020\u0027\u0068\u0074\u0074\u0070\u003a/\u002f\u0027",ctype );
};_adaf :=_ee .NewRelationship ();_efc :=len (_fcg ._fac .Relationship )+1;_egb :=map[string ]struct{}{};for _ ,_agc :=range _fcg ._fac .Relationship {_egb [_agc .IdAttr ]=struct{}{};};for _ ,_agbg :=_egb [_ff .Sprintf ("\u0072\u0049\u0064%\u0064",_efc )];
_agbg ;_ ,_agbg =_egb [_ff .Sprintf ("\u0072\u0049\u0064%\u0064",_efc )]{_efc ++;};_adaf .IdAttr =_ff .Sprintf ("\u0072\u0049\u0064%\u0064",_efc );_adaf .TargetAttr =target ;_adaf .TypeAttr =ctype ;_fcg ._fac .Relationship =append (_fcg ._fac .Relationship ,_adaf );
return Relationship {_fdc :_adaf };};func (_efa CustomProperties )SetPropertyAsStorage (name string ,storage string ){_cef :=_efa .getNewProperty (name );_cef .PropertyChoice .Storage =&storage ;_efa .setOrReplaceProperty (_cef );};

// SetLanguage records the language of the document.
func (_eed CoreProperties )SetLanguage (s string ){_eed ._dcb .Language =&_fb .XSDAny {XMLName :_bg .Name {Local :"d\u0063\u003a\u006c\u0061\u006e\u0067\u0075\u0061\u0067\u0065"}};_eed ._dcb .Language .Data =[]byte (s );};

// Append appends DocBase part of an office document to another DocBase.
func (_aeg DocBase )Append (docBase1 DocBase )DocBase {_ca :=docBase1 .ContentTypes .X ();for _ ,_dgd :=range _ca .TypesChoice {if _dgd .Default !=nil {_aeg .ContentTypes .AddDefault (_dgd .Default .ExtensionAttr ,_dgd .Default .ContentTypeAttr );}else if _dgd .Override !=nil {_aeg .ContentTypes .AddOverride (_dgd .Override .PartNameAttr ,_dgd .Override .ContentTypeAttr );
};};_af :=_aeg .AppProperties .X ();_bd :=docBase1 .AppProperties .X ();if _af .Pages !=nil {if _bd .Pages !=nil {*_af .Pages +=*_bd .Pages ;};}else if _bd .Pages !=nil {_af .Pages =_bd .Pages ;};if _af .Words !=nil {if _bd .Words !=nil {*_af .Words +=*_bd .Words ;
};}else if _bd .Words !=nil {_af .Words =_bd .Words ;};if _af .Characters !=nil {if _bd .Characters !=nil {*_af .Characters +=*_bd .Characters ;};}else if _bd .Characters !=nil {_af .Characters =_bd .Characters ;};if _af .Lines !=nil {if _bd .Lines !=nil {*_af .Lines +=*_bd .Lines ;
};}else if _bd .Lines !=nil {_af .Lines =_bd .Lines ;};if _af .Paragraphs !=nil {if _bd .Paragraphs !=nil {*_af .Paragraphs +=*_bd .Paragraphs ;};}else if _bd .Paragraphs !=nil {_af .Paragraphs =_bd .Paragraphs ;};if _af .Notes !=nil {if _bd .Notes !=nil {*_af .Notes +=*_bd .Notes ;
};}else if _bd .Notes !=nil {_af .Notes =_bd .Notes ;};if _af .HiddenSlides !=nil {if _bd .HiddenSlides !=nil {*_af .HiddenSlides +=*_bd .HiddenSlides ;};}else if _bd .HiddenSlides !=nil {_af .HiddenSlides =_bd .HiddenSlides ;};if _af .MMClips !=nil {if _bd .MMClips !=nil {*_af .MMClips +=*_bd .MMClips ;
};}else if _bd .MMClips !=nil {_af .MMClips =_bd .MMClips ;};if _af .LinksUpToDate !=nil {if _bd .LinksUpToDate !=nil {*_af .LinksUpToDate =*_af .LinksUpToDate &&*_bd .LinksUpToDate ;};}else if _bd .LinksUpToDate !=nil {_af .LinksUpToDate =_bd .LinksUpToDate ;
};if _af .CharactersWithSpaces !=nil {if _bd .CharactersWithSpaces !=nil {*_af .CharactersWithSpaces +=*_bd .CharactersWithSpaces ;};}else if _bd .CharactersWithSpaces !=nil {_af .CharactersWithSpaces =_bd .CharactersWithSpaces ;};if _af .SharedDoc !=nil {if _bd .SharedDoc !=nil {*_af .SharedDoc =*_af .SharedDoc ||*_bd .SharedDoc ;
};}else if _bd .SharedDoc !=nil {_af .SharedDoc =_bd .SharedDoc ;};if _af .HyperlinksChanged !=nil {if _bd .HyperlinksChanged !=nil {*_af .HyperlinksChanged =*_af .HyperlinksChanged ||*_bd .HyperlinksChanged ;};}else if _bd .HyperlinksChanged !=nil {_af .HyperlinksChanged =_bd .HyperlinksChanged ;
};_af .DigSig =nil ;if _af .TitlesOfParts ==nil &&_bd .TitlesOfParts !=nil {_af .TitlesOfParts =_bd .TitlesOfParts ;};if _af .HeadingPairs !=nil {if _bd .HeadingPairs !=nil {_dac :=_af .HeadingPairs .Vector ;_ga :=_bd .HeadingPairs .Vector ;_ece :=_dac .VectorChoice ;
_gb :=_ga .VectorChoice ;_cb :=[]*_c .CT_VectorChoice {};for _ecd :=0;_ecd < len (_gb );_ecd +=2{_ecc :=_gb [_ecd ].Lpstr ;_ecee :=false ;for _ecb :=0;_ecb < len (_ece );_ecb +=2{_ge :=_ece [_ecb ].Lpstr ;if _ge !=nil &&_ecc !=nil &&*_ge ==*_ecc {*_ece [_ecb +1].I4 =*_ece [_ecb +1].I4 +*_gb [_ecd +1].I4 ;
_ecee =true ;break ;};};if !_ecee {_gf :=_c .NewCT_VectorChoice ();_gf .Lpstr =_gb [_ecd ].Lpstr ;_cb =append (_cb ,_gf );_ab :=_c .NewCT_VectorChoice ();_ab .I4 =_gb [_ecd ].I4 ;_cb =append (_cb ,_ab );};};_ece =append (_ece ,_cb ...);_dac .SizeAttr =uint32 (len (_ece ));
};}else if _bd .HeadingPairs !=nil {_af .HeadingPairs =_bd .HeadingPairs ;};if _af .HLinks !=nil {if _bd .HLinks !=nil {_gd :=_af .HLinks .Vector ;_eaec :=_bd .HLinks .Vector ;_eb :=_gd .VectorChoice ;_eg :=_eaec .VectorChoice ;for _ ,_bf :=range _eg {_bb :=true ;
for _ ,_aa :=range _eb {if _b .DeepEqual (_aa ,_bf ){_bb =false ;break ;};};if _bb {_eb =append (_eb ,_bf );_gd .SizeAttr ++;};};};}else if _bd .HLinks !=nil {_af .HLinks =_bd .HLinks ;};_ffd :=_aeg .GetOrCreateCustomProperties ();_dfg :=docBase1 .GetOrCreateCustomProperties ();
for _ ,_bca :=range _dfg .PropertiesList (){_ffd .setProperty (_bca );};_aeg .CustomProperties =_ffd ;_fgc :=_aeg .Rels .X ().Relationship ;for _ ,_fcd :=range docBase1 .Rels .X ().Relationship {_dbf :=true ;for _ ,_cae :=range _fgc {if _cae .TargetAttr ==_fcd .TargetAttr &&_cae .TypeAttr ==_fcd .TypeAttr {_dbf =false ;
break ;};};if _dbf {_aeg .Rels .AddRelationship (_fcd .TargetAttr ,_fcd .TypeAttr );};};for _ ,_cd :=range docBase1 .ExtraFiles {_ag :=_cd .ZipPath ;_fgg :=true ;for _ ,_cdb :=range _aeg .ExtraFiles {if _cdb .ZipPath ==_ag {_fgg =false ;break ;};};if _fgg {_aeg .ExtraFiles =append (_aeg .ExtraFiles ,_cd );
};};return _aeg ;};

// NewRelationship constructs a new relationship.
func NewRelationship ()Relationship {return Relationship {_fdc :_ee .NewRelationship ()}};

// Category returns the category of the document
func (_acg CoreProperties )Category ()string {if _acg ._dcb .Category !=nil {return *_acg ._dcb .Category ;};return "";};const _ede ="2\u00300\u0036\u002d\u0030\u0031\u002d\u0030\u0032\u00541\u0035\u003a\u0030\u0034:0\u0035\u005a";func (_fcc CustomProperties )SetPropertyAsUi8 (name string ,ui8 uint64 ){_bcfd :=_fcc .getNewProperty (name );
_bcfd .PropertyChoice .Ui8 =&ui8 ;_fcc .setOrReplaceProperty (_bcfd );};

// Description returns the description of the document
func (_gef CoreProperties )Description ()string {if _gef ._dcb .Description !=nil {return string (_gef ._dcb .Description .Data );};return "";};func (_fca CustomProperties )SetPropertyAsVstream (name string ,vstream *_c .Vstream ){_cfe :=_fca .getNewProperty (name );
_cfe .PropertyChoice .Vstream =vstream ;_fca .setOrReplaceProperty (_cfe );};

// NewAppProperties constructs a new AppProperties.
func NewAppProperties ()AppProperties {_aac :=AppProperties {_ffe :_dae .NewProperties ()};_aac .SetCompany ("\u0046\u006f\u0078\u0079\u0055\u0074\u0069\u006c\u0073\u0020\u0065\u0068\u0066");_aac .SetApplication ("g\u0069\u0074\u0068\u0075\u0062\u002ec\u006f\u006d\u002f\u0075\u006e\u0069\u0064\u006f\u0063/\u0075\u006e\u0069o\u0066f\u0069\u0063\u0065");
_aac .SetDocSecurity (0);_aac .SetLinksUpToDate (false );var _caec ,_dfc ,_cf int64 ;_ff .Sscanf (Version ,"\u0025\u0064\u002e\u0025\u0064\u002e\u0025\u0064",&_caec ,&_dfc ,&_cf );_gbc :=float64 (_caec )+float64 (_dfc )/10000.0;_aac .SetApplicationVersion (_ff .Sprintf ("\u0025\u0030\u0037\u002e\u0034\u0066",_gbc ));
return _aac ;};func (_ddd CustomProperties )SetPropertyAsStream (name string ,stream string ){_cbf :=_ddd .getNewProperty (name );_cbf .PropertyChoice .Stream =&stream ;_ddd .setOrReplaceProperty (_cbf );};

// SetLinksUpToDate sets the links up to date flag.
func (_aed AppProperties )SetLinksUpToDate (v bool ){_aed ._ffe .LinksUpToDate =_fb .Bool (v )};

// ImageRef is a reference to an image within a document.
type ImageRef struct{_fbe *DocBase ;_cbgbg Relationships ;_efbg Image ;_ddde string ;_baa string ;};

// WriteExtraFiles writes the extra files to the zip package.
func (_bfa *DocBase )WriteExtraFiles (z *_db .Writer )error {for _ ,_eaa :=range _bfa .ExtraFiles {if _gbdg :=_g .AddFileFromStorage (z ,_eaa .ZipPath ,_eaa .StoragePath );_gbdg !=nil {return _gbdg ;};};return nil ;};func (_fef CustomProperties )SetPropertyAsR8 (name string ,r8 float64 ){_dbd :=_fef .getNewProperty (name );
_dbd .PropertyChoice .R8 =&r8 ;_fef .setOrReplaceProperty (_dbd );};

// SetWidth sets column width, see measurement package.
func (_ceda TableCol )SetWidth (m _be .Distance ){_dde :=_be .ToEMU (float64 (m ));_ceda ._bbbf .WAttr =_eag .ST_Coordinate {ST_CoordinateUnqualified :&_dde };};func _cfg (_bfd _da .Time ,_dbff string )*_fb .XSDAny {_bdb :=&_fb .XSDAny {XMLName :_bg .Name {Local :_dbff }};
_bdb .Attrs =append (_bdb .Attrs ,_bg .Attr {Name :_bg .Name {Local :"\u0078\u0073\u0069\u003a\u0074\u0079\u0070\u0065"},Value :"\u0064\u0063\u0074\u0065\u0072\u006d\u0073\u003a\u00573\u0043\u0044\u0054\u0046"});_bdb .Attrs =append (_bdb .Attrs ,_bg .Attr {Name :_bg .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0078\u0073i"},Value :"\u0068\u0074\u0074\u0070\u003a/\u002f\u0077\u0077\u0077\u002e\u0077\u0033\u002e\u006f\u0072\u0067\u002f\u00320\u0030\u0031\u002f\u0058\u004d\u004c\u0053\u0063\u0068\u0065\u006d\u0061\u002d\u0069\u006e\u0073\u0074\u0061\u006e\u0063\u0065"});
_bdb .Attrs =append (_bdb .Attrs ,_bg .Attr {Name :_bg .Name {Local :"\u0078\u006d\u006c\u006e\u0073\u003a\u0064\u0063\u0074\u0065\u0072\u006d\u0073"},Value :"\u0068t\u0074\u0070\u003a\u002f/\u0070\u0075\u0072\u006c\u002eo\u0072g\u002fd\u0063\u002f\u0074\u0065\u0072\u006d\u0073/"});
_bdb .Data =[]byte (_bfd .Format (_ede ));return _bdb ;};func (_agbd CustomProperties )SetPropertyAsOstorage (name string ,ostorage string ){_fed :=_agbd .getNewProperty (name );_fed .PropertyChoice .Ostorage =&ostorage ;_agbd .setOrReplaceProperty (_fed );
};

// NewCoreProperties constructs a new CoreProperties.
func NewCoreProperties ()CoreProperties {return CoreProperties {_dcb :_fg .NewCoreProperties ()}};

// LastModifiedBy returns the name of the last person to modify the document
func (_bcacc CoreProperties )LastModifiedBy ()string {if _bcacc ._dcb .LastModifiedBy !=nil {return *_bcacc ._dcb .LastModifiedBy ;};return "";};

// SetHeight sets row height, see measurement package.
func (_effe TableRow )SetHeight (m _be .Distance ){_ebd :=_be .ToEMU (float64 (m ));_effe ._cgfe .HAttr =_eag .ST_Coordinate {ST_CoordinateUnqualified :&_ebd };};

// GetPropertyByName returns a custom property selected by it's name.
func (_gfbc CustomProperties )GetPropertyByName (name string )CustomProperty {_eff :=_gfbc ._acgb .Property ;for _ ,_fgd :=range _eff {if *_fgd .NameAttr ==name {return CustomProperty {_ecda :_fgd };};};return CustomProperty {};};func _cbg (_ccb *_fb .XSDAny )_da .Time {if _ccb ==nil {return _da .Time {};
};_dge ,_dea :=_da .Parse (_ede ,string (_ccb .Data ));if _dea !=nil {_ce .Log .Debug ("\u0065\u0072\u0072\u006f\u0072\u0020\u0070\u0061\u0072\u0073i\u006e\u0067\u0020\u0074\u0069\u006d\u0065 \u0066\u0072\u006f\u006d\u0020\u0025\u0073\u003a\u0020\u0025\u0073",string (_ccb .Data ),_dea );
};return _dge ;};

// Clear removes any existing relationships.
func (_ace Relationships )Clear (){_ace ._fac .Relationship =nil };

// Size returns the size of an image
func (_bfab ImageRef )Size ()_df .Point {return _bfab ._efbg .Size };func (_cdc CustomProperties )SetPropertyAsLpwstr (name string ,lpwstr string ){_abf :=_cdc .getNewProperty (name );_abf .PropertyChoice .Lpwstr =&lpwstr ;_cdc .setOrReplaceProperty (_abf );
};

// SetDescription records the description of the document.
func (_fad CoreProperties )SetDescription (s string ){if _fad ._dcb .Description ==nil {_fad ._dcb .Description =&_fb .XSDAny {XMLName :_bg .Name {Local :"\u0064\u0063\u003a\u0064\u0065\u0073\u0063\u0072\u0069p\u0074\u0069\u006f\u006e"}};};_fad ._dcb .Description .Data =[]byte (s );
};

// ImageFromBytes returns an Image struct for an in-memory image. You can also
// construct an Image directly if the file and size are known.
func ImageFromBytes (data []byte )(Image ,error ){_acge :=Image {};_gbcc ,_defb ,_ded :=_df .Decode (_ec .NewReader (data ));if _ded !=nil {return _acge ,_ff .Errorf ("\u0075n\u0061\u0062\u006c\u0065 \u0074\u006f\u0020\u0070\u0061r\u0073e\u0020i\u006d\u0061\u0067\u0065\u003a\u0020\u0025s",_ded );
};_acge .Data =&data ;_acge .Format =_defb ;_acge .Size =_gbcc .Bounds ().Size ();return _acge ,nil ;};func (_gaf CustomProperties )SetPropertyAsVector (name string ,vector *_c .Vector ){_gba :=_gaf .getNewProperty (name );_gba .PropertyChoice .Vector =vector ;
_gaf .setOrReplaceProperty (_gba );};func (_agg CustomProperties )SetPropertyAsDecimal (name string ,decimal float64 ){_ebag :=_agg .getNewProperty (name );_ebag .PropertyChoice .Decimal =&decimal ;_agg .setOrReplaceProperty (_ebag );};

// DocBase is the type embedded in in the Document/Workbook/Presentation types
// that contains members common to all.
type DocBase struct{ContentTypes ContentTypes ;AppProperties AppProperties ;Rels Relationships ;CoreProperties CoreProperties ;CustomProperties CustomProperties ;Thumbnail _df .Image ;Images []ImageRef ;ExtraFiles []ExtraFile ;TmpPath string ;};

// TableCol represents a column in a table.
type TableCol struct{_bbbf *_eag .CT_TableCol };

// AddDefault registers a default content type for a given file extension.
func (_abe ContentTypes )AddDefault (fileExtension string ,contentType string ){fileExtension =_ea .ToLower (fileExtension );for _ ,_deb :=range _abe ._cde .TypesChoice {if _deb .Default !=nil &&_deb .Default .ExtensionAttr ==fileExtension {return ;};};
_cg :=_de .NewDefault ();_cg .ExtensionAttr =fileExtension ;_cg .ContentTypeAttr =contentType ;_eaecb :=_de .NewCT_TypesChoice ();_eaecb .Default =_cg ;_abe ._cde .TypesChoice =append (_abe ._cde .TypesChoice ,_eaecb );};func (_dff CustomProperties )SetPropertyAsError (name string ,error string ){_dacgd :=_dff .getNewProperty (name );
_dacgd .PropertyChoice .Error =&error ;_dff .setOrReplaceProperty (_dacgd );};

// SetApplicationVersion sets the version of the application that created the
// document.  Per MS, the verison string mut be in the form 'XX.YYYY'.
func (_gec AppProperties )SetApplicationVersion (s string ){_gec ._ffe .AppVersion =&s };

// Author returns the author of the document
func (_edg CoreProperties )Author ()string {if _edg ._dcb .Creator !=nil {return string (_edg ._dcb .Creator .Data );};return "";};

// DefAttr returns the DefAttr property.
func (_fefg TableStyles )DefAttr ()string {return _fefg ._bcffe .DefAttr };

// SetCategory records the category of the document.
func (_cbb CoreProperties )SetCategory (s string ){_cbb ._dcb .Category =&s };

// X returns the inner wrapped XML type.
func (_bae TableStyles )X ()*_eag .TblStyleLst {return _bae ._bcffe };

// Data returns the data of an image file, if any.
func (_efbd ImageRef )Data ()*[]byte {return _efbd ._efbg .Data };

// AddExtraFileFromZip is used when reading an unsupported file from an OOXML
// file. This ensures that unsupported file content will at least round-trip
// correctly.
func (_ebc *DocBase )AddExtraFileFromZip (f *_db .File )error {_dgg ,_bda :=_g .ExtractToDiskTmp (f ,_ebc .TmpPath );if _bda !=nil {return _ff .Errorf ("\u0065\u0072r\u006f\u0072\u0020\u0065x\u0074\u0072a\u0063\u0074\u0069\u006e\u0067\u0020\u0075\u006es\u0075\u0070\u0070\u006f\u0072\u0074\u0065\u0064\u0020\u0066\u0069\u006ce\u003a\u0020\u0025\u0073",_bda );
};_ebc .ExtraFiles =append (_ebc .ExtraFiles ,ExtraFile {ZipPath :f .Name ,StoragePath :_dgg });return nil ;};

// PropertiesList returns the list of all custom properties of the document.
func (_gbgc CustomProperties )PropertiesList ()[]*_dbe .CT_Property {return _gbgc ._acgb .Property };

// Title returns the Title of the document
func (_bcc CoreProperties )Title ()string {if _bcc ._dcb .Title !=nil {return string (_bcc ._dcb .Title .Data );};return "";};

// X returns the inner wrapped XML type.
func (_fdcc Table )X ()*_eag .Tbl {return _fdcc ._ebe };func (_fefb *ImageRef )SetRelID (id string ){_fefb ._ddde =id };

// X returns the underlying raw XML data.
func (_cdcg Relationships )X ()*_ee .Relationships {return _cdcg ._fac };func (_ecgc CustomProperties )SetPropertyAsBool (name string ,b bool ){_dbeb :=_ecgc .getNewProperty (name );_dbeb .PropertyChoice .Bool =&b ;_ecgc .setOrReplaceProperty (_dbeb );
};

// Modified returns the time that the document was modified.
func (_abdf CoreProperties )Modified ()_da .Time {return _cbg (_abdf ._dcb .Modified )};

// RemoveOverrideByIndex removes an override given a path and override index.
func (_caa ContentTypes )RemoveOverrideByIndex (path string ,indexToFind int )error {_fbd :=path [0:len (path )-5];if !_ea .HasPrefix (_fbd ,"\u002f"){_fbd ="\u002f"+_fbd ;};_eba ,_ac :=_f .Compile (_fbd +"\u0028\u005b\u0030-\u0039\u005d\u002b\u0029\u002e\u0078\u006d\u006c");
if _ac !=nil {return _ac ;};_fbfg :=0;_fgb :=-1;for _agbb ,_cda :=range _caa ._cde .TypesChoice {_cbd :=_cda .Override ;if _cbd ==nil {continue ;};if _dgf :=_eba .FindStringSubmatch (_cbd .PartNameAttr );len (_dgf )> 1{if _fbfg ==indexToFind {_fgb =_agbb ;
}else if _fbfg > indexToFind {_cffe ,_ :=_dg .Atoi (_dgf [1]);_cffe --;_cbd .PartNameAttr =_ff .Sprintf ("\u0025\u0073\u0025\u0064\u002e\u0078\u006d\u006c",_fbd ,_cffe );};_fbfg ++;};};if _fgb > -1{copy (_caa ._cde .TypesChoice [_fgb :],_caa ._cde .TypesChoice [_fgb +1:]);
_caa ._cde .TypesChoice =_caa ._cde .TypesChoice [0:len (_caa ._cde .TypesChoice )-1];};return nil ;};

// TblStyle returns the TblStyle property.
func (_gece TableStyles )TblStyle ()[]*_eag .CT_TableStyle {return _gece ._bcffe .TblStyle };

// GetImageBytesByTarget returns Image object with Data bytes read from its target.
func (_ced *DocBase )GetImageBytesByTarget (target string )(Image ,error ){if target !=""{target ="\u0077\u006f\u0072d\u002f"+target ;for _ ,_dcgg :=range _ced .Images {if _dcgg .Target ()==target {if _ea .ToLower (_dcgg .Format ())!="\u0065\u006d\u0066"{return ImageFromStorage (_dcgg .Path ());
};return Image {Path :_dcgg .Path (),Format :_dcgg .Format ()},nil ;};};};return Image {},_dag ;};func (_ffgf CustomProperties )SetPropertyAsArray (name string ,array *_c .Array ){_dgea :=_ffgf .getNewProperty (name );_dgea .PropertyChoice .Array =array ;
_ffgf .setOrReplaceProperty (_dgea );};

// NewContentTypes returns a wrapper around a newly constructed content-types.
func NewContentTypes ()ContentTypes {_caef :=ContentTypes {_cde :_de .NewTypes ()};_caef .AddDefault ("\u0078\u006d\u006c","\u0061p\u0070l\u0069\u0063\u0061\u0074\u0069\u006f\u006e\u002f\u0078\u006d\u006c");_caef .AddDefault ("\u0072\u0065\u006c\u0073","\u0061\u0070\u0070\u006c\u0069\u0063a\u0074\u0069\u006fn\u002f\u0076\u006ed\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006fr\u006d\u0061\u0074\u0073\u002dpa\u0063\u006b\u0061\u0067\u0065\u002e\u0072\u0065\u006c\u0061\u0074\u0069\u006f\u006e\u0073\u0068\u0069\u0070\u0073\u002b\u0078\u006d\u006c");
_caef .AddDefault ("\u0070\u006e\u0067","\u0069m\u0061\u0067\u0065\u002f\u0070\u006eg");_caef .AddDefault ("\u006a\u0070\u0065\u0067","\u0069\u006d\u0061\u0067\u0065\u002f\u006a\u0070\u0065\u0067");_caef .AddDefault ("\u006a\u0070\u0067","\u0069m\u0061\u0067\u0065\u002f\u006a\u0070g");
_caef .AddDefault ("\u0077\u006d\u0066","i\u006d\u0061\u0067\u0065\u002f\u0078\u002d\u0077\u006d\u0066");_caef .AddOverride ("\u002fd\u006fc\u0050\u0072\u006f\u0070\u0073/\u0063\u006fr\u0065\u002e\u0078\u006d\u006c","\u0061\u0070\u0070\u006c\u0069\u0063\u0061\u0074\u0069\u006f\u006e\u002f\u0076\u006e\u0064\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073-\u0070\u0061\u0063\u006b\u0061\u0067\u0065\u002e\u0063\u006f\u0072\u0065\u002dp\u0072\u006f\u0070\u0065\u0072\u0074i\u0065\u0073\u002bx\u006d\u006c");
_caef .AddOverride ("\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u002f\u0061\u0070p\u002e\u0078\u006d\u006c","a\u0070\u0070l\u0069\u0063\u0061\u0074\u0069\u006f\u006e\u002f\u0076\u006e\u0064\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066o\u0072\u006d\u0061\u0074\u0073\u002d\u006f\u0066\u0066\u0069\u0063\u0065\u0064\u006f\u0063\u0075m\u0065\u006e\u0074\u002e\u0065\u0078\u0074\u0065\u006e\u0064\u0065\u0064\u002dp\u0072\u006f\u0070\u0065\u0072\u0074\u0069\u0065\u0073\u002b\u0078m\u006c");
return _caef ;};

// Relationships represents a .rels file.
type Relationships struct{_fac *_ee .Relationships };func (_afg CustomProperties )SetPropertyAsI4 (name string ,i4 int32 ){_aadd :=_afg .getNewProperty (name );_aadd .PropertyChoice .I4 =&i4 ;_afg .setOrReplaceProperty (_aadd );};const _cdd =2;

// IsEmpty returns true if there are no relationships.
func (_dcbg Relationships )IsEmpty ()bool {return _dcbg ._fac ==nil ||len (_dcbg ._fac .Relationship )==0;};func (_agd CustomProperties )SetPropertyAsInt (name string ,i int ){_afbc :=_agd .getNewProperty (name );_dacg :=int32 (i );_afbc .PropertyChoice .Int =&_dacg ;
_agd .setOrReplaceProperty (_afbc );};

// NewRelationshipsCopy creates a new relationships wrapper as a copy of passed in instance.
func NewRelationshipsCopy (rels Relationships )Relationships {_cgf :=*rels ._fac ;return Relationships {_fac :&_cgf };};

// TableStyles contains document specific properties.
type TableStyles struct{_bcffe *_eag .TblStyleLst };

// ApplicationVersion returns the version of the application that created the
// document.
func (_fbf AppProperties )ApplicationVersion ()string {if _fbf ._ffe .AppVersion !=nil {return *_fbf ._ffe .AppVersion ;};return "";};

// Company returns the name of the company that created the document.
// For unioffice created documents, it defaults to github.com/unidoc/unioffice
func (_bgg AppProperties )Company ()string {if _bgg ._ffe .Company !=nil {return *_bgg ._ffe .Company ;};return "";};

// GetTargetByRelIdAndType returns a target path with the associated relation ID.
func (_daaa Relationships )GetTargetByRelIdAndType (idAttr string ,typeAttr string )string {for _ ,_egcg :=range _daaa ._fac .Relationship {if _egcg .IdAttr ==idAttr &&_egcg .TypeAttr ==typeAttr {return _egcg .TargetAttr ;};};return "";};

// AddRow adds a row to a table.
func (_bgf Table )AddRow ()*TableRow {_feab :=_eag .NewCT_TableRow ();for _fefa :=0;_fefa < len (_bgf ._ebe .TblGrid .GridCol );_fefa ++{_feab .Tc =append (_feab .Tc ,_eag .NewCT_TableCell ());};_bgf ._ebe .Tr =append (_bgf ._ebe .Tr ,_feab );return &TableRow {_cgfe :_feab };
};var _dag =_ae .New ("\u0069\u006d\u0061\u0067\u0065\u0020\u006e\u006f\u0074\u0020\u0066o\u0075\u006e\u0064\u0020\u0069\u006e\u0020\u0073\u0074\u006fr\u0061\u0067\u0065");

// Hyperlink is just an appropriately configured relationship.
type Hyperlink Relationship ;

// Format returns the format of the underlying image
func (_bcg ImageRef )Format ()string {return _bcg ._efbg .Format };const _cad =15;

// Image is a container for image information. It's used as we need format and
// and size information to use images.
// It contains either the filesystem path to the image, or the image itself.
type Image struct{Size _df .Point ;Format string ;Path string ;Data *[]byte ;};

// MakeImageRef constructs an image reference which is a reference to a
// particular image file inside a document.  The same image can be used multiple
// times in a document by re-use the ImageRef.
func MakeImageRef (img Image ,d *DocBase ,rels Relationships )ImageRef {return ImageRef {_efbg :img ,_fbe :d ,_cbgbg :rels };};

// Path returns the path to an image file, if any.
func (_ecbc ImageRef )Path ()string {return _ecbc ._efbg .Path };

// Created returns the time that the document was created.
func (_cdg CoreProperties )Created ()_da .Time {return _cbg (_cdg ._dcb .Created )};func (_caaa CustomProperties )SetPropertyAsBlob (name ,blob string ){_dbfe :=_caaa .getNewProperty (name );_dbfe .PropertyChoice .Blob =&blob ;_caaa .setOrReplaceProperty (_dbfe );
};

// EnsureDefault esnures that an extension and default content type exist,
// adding it if necessary.
func (_gfg ContentTypes )EnsureDefault (ext ,contentType string ){ext =_ea .ToLower (ext );for _ ,_fa :=range _gfg ._cde .TypesChoice {_def :=_fa .Default ;if _def !=nil &&_def .ExtensionAttr ==ext {_def .ContentTypeAttr =contentType ;return ;};};_ccd :=&_de .Default {};
_ccd .ContentTypeAttr =contentType ;_ccd .ExtensionAttr =ext ;_dga :=_de .NewCT_TypesChoice ();_dga .Default =_ccd ;_gfg ._cde .TypesChoice =append (_gfg ._cde .TypesChoice ,_dga );};

// EnsureOverride ensures that an override for the given path exists, adding it if necessary
func (_gfb ContentTypes )EnsureOverride (path ,contentType string ){for _ ,_fe :=range _gfb ._cde .TypesChoice {_fggg :=_fe .Override ;if _fggg !=nil &&_fggg .PartNameAttr ==path {if _ea .HasPrefix (contentType ,"\u0068\u0074\u0074\u0070"){_ce .Log .Debug ("\u0063\u006f\u006e\u0074\u0065\u006et\u0020\u0074\u0079p\u0065\u0020\u0027%\u0073\u0027\u0020\u0069\u0073\u0020\u0069\u006e\u0063\u006fr\u0072\u0065\u0063\u0074\u002c m\u0075\u0073\u0074\u0020\u006e\u006f\u0074\u0020\u0073\u0074\u0061\u0072\u0074\u0020\u0077\u0069\u0074\u0068\u0020\u0068\u0074\u0074\u0070",contentType );
};_fggg .ContentTypeAttr =contentType ;return ;};};_gfb .AddOverride (path ,contentType );};

// Relationships returns a slice of all of the relationships.
func (_dgfa Relationships )Relationships ()[]Relationship {_fadb :=[]Relationship {};for _ ,_baae :=range _dgfa ._fac .Relationship {_fadb =append (_fadb ,Relationship {_fdc :_baae });};return _fadb ;};func (_gfc CustomProperties )SetPropertyAsI8 (name string ,i8 int64 ){_cfga :=_gfc .getNewProperty (name );
_cfga .PropertyChoice .I8 =&i8 ;_gfc .setOrReplaceProperty (_cfga );};func (_cdgd CustomProperties )SetPropertyAsR4 (name string ,r4 float32 ){_ebaf :=_cdgd .getNewProperty (name );_ebaf .PropertyChoice .R4 =&r4 ;_cdgd .setOrReplaceProperty (_ebaf );};


// SetTarget changes the target attribute of the image reference (e.g. in the case of the creation of the reference or if the image which the reference is related to was moved from one location to another).
func (_ggd *ImageRef )SetTarget (target string ){_ggd ._baa =target };func (_daed CustomProperties )SetPropertyAsUi2 (name string ,ui2 uint16 ){_bgc :=_daed .getNewProperty (name );_bgc .PropertyChoice .Ui2 =&ui2 ;_daed .setOrReplaceProperty (_bgc );};
func (_ebf CustomProperties )SetPropertyAsFiletime (name string ,filetime _da .Time ){_gefb :=_ebf .getNewProperty (name );_gefb .PropertyChoice .Filetime =&filetime ;_ebf .setOrReplaceProperty (_gefb );};

// X returns the inner wrapped XML type of CustomProperty.
func (_eaf CustomProperty )X ()*_dbe .CT_Property {return _eaf ._ecda };

// SetTarget set the target (path) of a relationship.
func (_fcf Relationship )SetTarget (s string ){_fcf ._fdc .TargetAttr =s };func (_gg CustomProperties )setOrReplaceProperty (_eca *_dbe .CT_Property ){_gg .setPropertyHelper (_eca ,true );};var ReleasedAt =_da .Date (_bcd ,_cdd ,_beae ,_cad ,_bdca ,0,0,_da .UTC );


// ContentStatus returns the content status of the document (e.g. "Final", "Draft")
func (_fggb CoreProperties )ContentStatus ()string {if _fggb ._dcb .ContentStatus !=nil {return *_fggb ._dcb .ContentStatus ;};return "";};func (_dgb CustomProperties )SetPropertyAsLpstr (name string ,lpstr string ){_cfgad :=_dgb .getNewProperty (name );
_cfgad .PropertyChoice .Lpstr =&lpstr ;_dgb .setOrReplaceProperty (_cfgad );};

// TableRow represents a row in a table.
type TableRow struct{_cgfe *_eag .CT_TableRow };func (_eccd CustomProperties )SetPropertyAsI1 (name string ,i1 int8 ){_acd :=_eccd .getNewProperty (name );_acd .PropertyChoice .I1 =&i1 ;_eccd .setOrReplaceProperty (_acd );};

// SetID set the ID of a relationship.
func (_afd Relationship )SetID (ID string ){_afd ._fdc .IdAttr =ID ;};

// Properties returns table properties.
func (_fec Table )Properties ()*_eag .CT_TableProperties {return _fec ._ebe .TblPr };

// ExtraFile is an unsupported file type extracted from, or to be written to a
// zip package
type ExtraFile struct{ZipPath string ;StoragePath string ;};

// CopyOverride copies override content type for a given `path` and puts it with a path `newPath`.
func (_bdc ContentTypes )CopyOverride (path ,newPath string ){if !_ea .HasPrefix (path ,"\u002f"){path ="\u002f"+path ;};if !_ea .HasPrefix (newPath ,"\u002f"){newPath ="\u002f"+newPath ;};for _ ,_acf :=range _bdc ._cde .TypesChoice {if _acf .Override ==nil {continue ;
};if _acf .Override .PartNameAttr ==path {_dca :=*_acf ;_dca .Override .PartNameAttr =newPath ;_bdc ._cde .TypesChoice =append (_bdc ._cde .TypesChoice ,&_dca );};};};

// Properties returns table properties.
func (_dbed Table )Grid ()*_eag .CT_TableGrid {return _dbed ._ebe .TblGrid };

// Pages returns total number of pages which are saved by the text editor which produced the document.
// For unioffice created documents, it is 0.
func (_ffg AppProperties )Pages ()int32 {if _ffg ._ffe .Pages !=nil {return *_ffg ._ffe .Pages ;};return 0;};func (_ccg CustomProperties )SetPropertyAsUint (name string ,ui uint ){_efb :=_ccg .getNewProperty (name );_adg :=uint32 (ui );_efb .PropertyChoice .Uint =&_adg ;
_ccg .setOrReplaceProperty (_efb );};

// X returns the inner wrapped XML type.
func (_dcg CoreProperties )X ()*_fg .CoreProperties {return _dcg ._dcb };

// GetByRelId returns a relationship with the associated relation ID.
func (_fccc Relationships )GetByRelId (idAttr string )Relationship {for _ ,_cfgf :=range _fccc ._fac .Relationship {if _cfgf .IdAttr ==idAttr {return Relationship {_fdc :_cfgf };};};return Relationship {};};

// AddCol adds a column to a table.
func (_fge Table )AddCol ()*TableCol {_dfcca :=_eag .NewCT_TableCol ();_fge ._ebe .TblGrid .GridCol =append (_fge ._ebe .TblGrid .GridCol ,_dfcca );for _ ,_egf :=range _fge ._ebe .Tr {_efd :=_eag .NewCT_TableCell ();_egf .Tc =append (_egf .Tc ,_efd );};
return &TableCol {_bbbf :_dfcca };};

// SetModified sets the time that the document was modified.
func (_ba CoreProperties )SetModified (t _da .Time ){_ba ._dcb .Modified =_cfg (t ,"\u0064\u0063t\u0065\u0072\u006ds\u003a\u006d\u006f\u0064\u0069\u0066\u0069\u0065\u0064");};

// SetOffsetY sets vertical offset of a table in distance units (see measurement package).
func (_gacd Table )SetOffsetY (offY float64 ){if _gacd ._ecaa .Off ==nil {_gacd ._ecaa .Off =_eag .NewCT_Point2D ();_geg :=int64 (0);_gacd ._ecaa .Off .XAttr =_eag .ST_Coordinate {ST_CoordinateUnqualified :&_geg };};_dcfe :=_be .ToEMU (offY );_gacd ._ecaa .Off .YAttr =_eag .ST_Coordinate {ST_CoordinateUnqualified :&_dcfe };
};

// AddImageToZip adds an image (either from bytes or from disk) and adds it to the zip file.
func AddImageToZip (z *_db .Writer ,img ImageRef ,imageNum int ,dt _fb .DocType )error {_gafe :=_fb .AbsoluteImageFilename (dt ,imageNum ,_ea .ToLower (img .Format ()));if img .Data ()!=nil &&len (*img .Data ())> 0{if _acde :=_g .AddFileFromBytes (z ,_gafe ,*img .Data ());
_acde !=nil {return _acde ;};}else if img .Path ()!=""{if _ddf :=_g .AddFileFromStorage (z ,_gafe ,img .Path ());_ddf !=nil {return _ddf ;};}else {return _ff .Errorf ("\u0075\u006es\u0075\u0070\u0070\u006f\u0072\u0074\u0065\u0064\u0020\u0069\u006d\u0061\u0067\u0065\u0020\u0073\u006f\u0075\u0072\u0063\u0065\u003a %\u002b\u0076",img );
};return nil ;};func (_dggf TableRow )addCell ()*_eag .CT_TableCell {_ceed :=_eag .NewCT_TableCell ();_dggf ._cgfe .Tc =append (_dggf ._cgfe .Tc ,_ceed );return _ceed ;};

// NewTable makes a new table.
func NewTable ()*Table {_dbc :=_eag .NewTbl ();_dbc .TblPr =_eag .NewCT_TableProperties ();return &Table {_ebe :_dbc };};

// NewTableStyles constructs a new TableStyles.
func NewTableStyles ()TableStyles {return TableStyles {_bcffe :_eag .NewTblStyleLst ()}};

// SetContentStatus records the content status of the document.
func (_dcd CoreProperties )SetContentStatus (s string ){_dcd ._dcb .ContentStatus =&s };

// AddHyperlink adds an external hyperlink relationship.
func (_ddfg Relationships )AddHyperlink (target string )Hyperlink {_aca :=_ddfg .AddRelationship (target ,_fb .HyperLinkType );_aca ._fdc .TargetModeAttr =_ee .ST_TargetModeExternal ;return Hyperlink (_aca );};

// CopyRelationship copies the relationship.
func (_faae Relationships )CopyRelationship (idAttr string )(Relationship ,bool ){for _cefd :=range _faae ._fac .Relationship {if _faae ._fac .Relationship [_cefd ].IdAttr ==idAttr {_afe :=*_faae ._fac .Relationship [_cefd ];_baf :=len (_faae ._fac .Relationship )+1;
_fce :=map[string ]struct{}{};for _ ,_egd :=range _faae ._fac .Relationship {_fce [_egd .IdAttr ]=struct{}{};};for _ ,_gbgf :=_fce [_ff .Sprintf ("\u0072\u0049\u0064%\u0064",_baf )];_gbgf ;_ ,_gbgf =_fce [_ff .Sprintf ("\u0072\u0049\u0064%\u0064",_baf )]{_baf ++;
};_afe .IdAttr =_ff .Sprintf ("\u0072\u0049\u0064%\u0064",_baf );_faae ._fac .Relationship =append (_faae ._fac .Relationship ,&_afe );return Relationship {_fdc :&_afe },true ;};};return Relationship {},false ;};

// CreateCustomProperties creates the custom properties of the document.
func (_gdg *DocBase )CreateCustomProperties (){_gdg .CustomProperties =NewCustomProperties ();_gdg .AddCustomRelationships ();};

// NewRelationships creates a new relationship wrapper.
func NewRelationships ()Relationships {return Relationships {_fac :_ee .NewRelationships ()}};

// Rows returns all table rows.
func (_bfca Table )Rows ()[]*TableRow {_cgdc :=_bfca ._ebe .Tr ;_gfa :=[]*TableRow {};for _ ,_cafg :=range _cgdc {_gfa =append (_gfa ,&TableRow {_cgfe :_cafg });};return _gfa ;};

// RelID returns the relationship ID.
func (_cfa ImageRef )RelID ()string {return _cfa ._ddde };

// SetTitle records the title of the document.
func (_cbgb CoreProperties )SetTitle (s string ){if _cbgb ._dcb .Title ==nil {_cbgb ._dcb .Title =&_fb .XSDAny {XMLName :_bg .Name {Local :"\u0064\u0063\u003a\u0074\u0069\u0074\u006c\u0065"}};};_cbgb ._dcb .Title .Data =[]byte (s );};

// ID returns the ID of a relationship.
func (_aace Relationship )ID ()string {return _aace ._fdc .IdAttr };const _bcd =2025;

// RelativeWidth returns the relative width of an image given a fixed height.
// This is used when setting image to a fixed height to calculate the width
// required to keep the same image aspect ratio.
func (_eccc ImageRef )RelativeWidth (h _be .Distance )_be .Distance {_fgdc :=float64 (_eccc .Size ().X )/float64 (_eccc .Size ().Y );return h *_be .Distance (_fgdc );};

// SetPages sets the pages count of the document.
func (_dc AppProperties )SetPages (n int32 ){_dc ._ffe .Pages =&n };

// CustomProperties contains document specific properties.
type CustomProperties struct{_acgb *_dbe .Properties };

// ImageFromStorage reads an image using the currently set
// temporary storage mechanism (see tempstorage). You can also
// construct an Image directly if the file and size are known.
func ImageFromStorage (path string )(Image ,error ){_bdag :=Image {};_fgda ,_add :=_ceg .Open (path );if _add !=nil {return _bdag ,_ff .Errorf ("\u0065\u0072\u0072or\u0020\u0072\u0065\u0061\u0064\u0069\u006e\u0067\u0020\u0069\u006d\u0061\u0067\u0065\u003a\u0020\u0025\u0073",_add );
};defer _fgda .Close ();_cgd ,_gfba ,_add :=_df .Decode (_fgda );if _add !=nil {return _bdag ,_ff .Errorf ("\u0075n\u0061\u0062\u006c\u0065 \u0074\u006f\u0020\u0070\u0061r\u0073e\u0020i\u006d\u0061\u0067\u0065\u003a\u0020\u0025s",_add );};_bdag .Path =path ;
_bdag .Format =_gfba ;_bdag .Size =_cgd .Bounds ().Size ();return _bdag ,nil ;};

// SetCreated sets the time that the document was created.
func (_aega CoreProperties )SetCreated (t _da .Time ){_aega ._dcb .Created =_cfg (t ,"\u0064c\u0074e\u0072\u006d\u0073\u003a\u0063\u0072\u0065\u0061\u0074\u0065\u0064");};

// X returns the inner wrapped XML type.
func (_dead Relationship )X ()*_ee .Relationship {return _dead ._fdc };func init (){_fc .SetAsStorage ()};

// AddCustomRelationships adds relationships related to custom properties to the document.
func (_cbc *DocBase )AddCustomRelationships (){_cbc .ContentTypes .AddOverride ("/\u0064o\u0063\u0050\u0072\u006f\u0070\u0073\u002f\u0063u\u0073\u0074\u006f\u006d.x\u006d\u006c","\u0061\u0070\u0070\u006c\u0069\u0063a\u0074\u0069\u006f\u006e\u002fv\u006e\u0064\u002e\u006f\u0070\u0065n\u0078\u006d\u006c\u0066\u006fr\u006d\u0061\u0074\u0073\u002d\u006f\u0066\u0066\u0069\u0063\u0065\u0064o\u0063\u0075\u006d\u0065\u006e\u0074\u002e\u0063\u0075\u0073\u0074\u006f\u006d\u002d\u0070r\u006f\u0070\u0065\u0072\u0074\u0069\u0065\u0073+\u0078\u006d\u006c");
_cbc .Rels .AddRelationship ("\u0064\u006f\u0063\u0050ro\u0070\u0073\u002f\u0063\u0075\u0073\u0074\u006f\u006d\u002e\u0078\u006d\u006c",_fb .CustomPropertiesType );};func (_gfe CustomProperties )SetPropertyAsI2 (name string ,i2 int16 ){_bfc :=_gfe .getNewProperty (name );
_bfc .PropertyChoice .I2 =&i2 ;_gfe .setOrReplaceProperty (_bfc );};func (_defe CustomProperties )getNewProperty (_ged string )*_dbe .CT_Property {_afb :=_defe ._acgb .Property ;_dbee :=int32 (1);for _ ,_ecg :=range _afb {if _ecg .PidAttr > _dbee {_dbee =_ecg .PidAttr ;
};};_cag :=_dbe .NewCT_Property ();_cag .NameAttr =&_ged ;_cag .PidAttr =_dbee +1;_cag .FmtidAttr ="\u007b\u0044\u0035\u0043\u0044\u0044\u0035\u0030\u0035\u002d\u0032\u0045\u0039\u0043\u002d\u0031\u0030\u0031\u0042\u002d\u0039\u0033\u0039\u0037-\u0030\u0038\u0030\u0030\u0032B\u0032\u0043F\u0039\u0041\u0045\u007d";
return _cag ;};

// SetApplication sets the name of the application that created the document.
func (_gbd AppProperties )SetApplication (s string ){_gbd ._ffe .Application =&s };func (_aede CustomProperties )SetPropertyAsClsid (name string ,clsid string ){_cee :=_aede .getNewProperty (name );_cee .PropertyChoice .Clsid =&clsid ;_aede .setOrReplaceProperty (_cee );
};

// Cells returns an array of row cells.
func (_feeb TableRow )Cells ()[]*_eag .CT_TableCell {return _feeb ._cgfe .Tc };

// RelativeHeight returns the relative height of an image given a fixed width.
// This is used when setting image to a fixed width to calculate the height
// required to keep the same image aspect ratio.
func (_faag ImageRef )RelativeHeight (w _be .Distance )_be .Distance {_dgge :=float64 (_faag .Size ().Y )/float64 (_faag .Size ().X );return w *_be .Distance (_dgge );};

// Type returns the type of a relationship.
func (_aae Relationship )Type ()string {return _aae ._fdc .TypeAttr };

// CoreProperties contains document specific properties.
type CoreProperties struct{_dcb *_fg .CoreProperties };func (_fgcg CustomProperties )setPropertyHelper (_dfcc *_dbe .CT_Property ,_bedf bool ){_ada :=_fgcg .GetPropertyByName (*_dfcc .NameAttr );if (_ada ==CustomProperty {}){_fgcg ._acgb .Property =append (_fgcg ._acgb .Property ,_dfcc );
}else if _bedf {_dfcc .FmtidAttr =_ada ._ecda .FmtidAttr ;if _ada ._ecda .PidAttr ==0{_dfcc .PidAttr =_ada ._ecda .PidAttr ;};_dfcc .LinkTargetAttr =_ada ._ecda .LinkTargetAttr ;*_ada ._ecda =*_dfcc ;};};

// Target returns the target attrubute of the image reference (a path where the image file is located in the document structure).
func (_aacg *ImageRef )Target ()string {return _aacg ._baa };const _bdca =30;

// SetLastModifiedBy records the last person to modify the document.
func (_cbe CoreProperties )SetLastModifiedBy (s string ){_cbe ._dcb .LastModifiedBy =&s };func (_edf CustomProperties )SetPropertyAsOblob (name ,oblob string ){_gbge :=_edf .getNewProperty (name );_gbge .PropertyChoice .Oblob =&oblob ;_edf .setOrReplaceProperty (_gbge );
};

// FindRIDForN returns the relationship ID for the i'th relationship of type t.
func (_ebg Relationships )FindRIDForN (i int ,t string )string {for _ ,_gbcca :=range _ebg ._fac .CT_Relationships .Relationship {if _gbcca .TypeAttr ==t {if i ==0{return _gbcca .IdAttr ;};i --;};};return "";};func (_gc CustomProperties )SetPropertyAsBstr (name string ,bstr string ){_ddc :=_gc .getNewProperty (name );
_ddc .PropertyChoice .Bstr =&bstr ;_gc .setOrReplaceProperty (_ddc );};

// Remove removes an existing relationship.
func (_bgga Relationships )Remove (rel Relationship )bool {for _bggc ,_dbebg :=range _bgga ._fac .Relationship {if _dbebg ==rel ._fdc {copy (_bgga ._fac .Relationship [_bggc :],_bgga ._fac .Relationship [_bggc +1:]);_bgga ._fac .Relationship =_bgga ._fac .Relationship [0:len (_bgga ._fac .Relationship )-1];
return true ;};};return false ;};func (_dfb CustomProperties )setProperty (_egce *_dbe .CT_Property ){_dfb .setPropertyHelper (_egce ,false );};

// GetTargetByRelId returns a target path with the associated relation ID.
func (_fee Relationships )GetTargetByRelId (idAttr string )string {for _ ,_deg :=range _fee ._fac .Relationship {if _deg .IdAttr ==idAttr {return _deg .TargetAttr ;};};return "";};

// NewCustomProperties constructs a new CustomProperties.
func NewCustomProperties ()CustomProperties {return CustomProperties {_acgb :_dbe .NewProperties ()}};func (_aag Relationship )String ()string {return _ff .Sprintf ("\u007b\u0049\u0044\u003a \u0025\u0073\u0020\u0054\u0061\u0072\u0067\u0065\u0074\u003a \u0025s\u0020\u0054\u0079\u0070\u0065\u003a\u0020%\u0073\u007d",_aag .ID (),_aag .Target (),_aag .Type ());
};func (_fea CustomProperties )SetPropertyAsUi4 (name string ,ui4 uint32 ){_gdc :=_fea .getNewProperty (name );_gdc .PropertyChoice .Ui4 =&ui4 ;_fea .setOrReplaceProperty (_gdc );};

// NewTheme constructs a new theme.
func NewTheme ()Theme {return Theme {_eag .NewTheme ()}};func (_daf CustomProperties )SetPropertyAsCy (name string ,cy string ){_acdg :=_daf .getNewProperty (name );_acdg .PropertyChoice .Cy =&cy ;_daf .setOrReplaceProperty (_acdg );};func UtcTimeFormat (t _da .Time )string {return t .Format (_bdbc )+"\u0020\u0055\u0054\u0043"};


// GetOrCreateCustomProperties returns the custom properties of the document (and if they not exist yet, creating them first).
func (_caea *DocBase )GetOrCreateCustomProperties ()CustomProperties {if _caea .CustomProperties .X ()==nil {_caea .CreateCustomProperties ();};return _caea .CustomProperties ;};

// Target returns the target (path) of a relationship.
func (_eefc Relationship )Target ()string {return _eefc ._fdc .TargetAttr };

// Application returns the name of the application that created the document.
// For unioffice created documents, it defaults to github.com/unidoc/unioffice
func (_abd AppProperties )Application ()string {if _abd ._ffe .Application !=nil {return *_abd ._ffe .Application ;};return "";};

// SetStyle assigns TableStyle to a table.
func (_edgd Table )SetStyle (style *_eag .CT_TableStyle ){if _edgd ._ebe .TblPr ==nil {_edgd ._ebe .TblPr =_eag .NewCT_TableProperties ();};if _edgd ._ebe .TblPr .TablePropertiesChoice ==nil {_edgd ._ebe .TblPr .TablePropertiesChoice =_eag .NewCT_TablePropertiesChoice ();
};_edgd ._ebe .TblPr .TablePropertiesChoice .TableStyle =style ;};

// AppProperties contains properties specific to the document and the
// application that created it.
type AppProperties struct{_ffe *_dae .Properties };func (_bba CustomProperties )SetPropertyAsEmpty (name string ){_aea :=_bba .getNewProperty (name );_aea .PropertyChoice .Empty =_c .NewEmpty ();_bba .setOrReplaceProperty (_aea );};

// Theme is a drawingml theme.
type Theme struct{_eda *_eag .Theme };

// SetAuthor records the author of the document.
func (_dcc CoreProperties )SetAuthor (s string ){if _dcc ._dcb .Creator ==nil {_dcc ._dcb .Creator =&_fb .XSDAny {XMLName :_bg .Name {Local :"\u0064\u0063\u003a\u0063\u0072\u0065\u0061\u0074\u006f\u0072"}};};_dcc ._dcb .Creator .Data =[]byte (s );};

// X returns the inner wrapped XML type.
func (_age Theme )X ()*_eag .Theme {return _age ._eda };func (_eee CustomProperties )SetPropertyAsOstream (name string ,ostream string ){_bgcf :=_eee .getNewProperty (name );_bgcf .PropertyChoice .Ostream =&ostream ;_eee .setOrReplaceProperty (_bgcf );
};