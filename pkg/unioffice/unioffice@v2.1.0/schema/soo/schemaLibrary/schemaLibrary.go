//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package schemaLibrary ;import (_e "encoding/xml";_a "fmt";_b "github.com/unidoc/unioffice/v2";_f "github.com/unidoc/unioffice/v2/common/logger";);func (_cg *SchemaLibrary )MarshalXML (e *_e .Encoder ,start _e .StartElement )error {start .Attr =append (start .Attr ,_e .Attr {Name :_e .Name {Local :"\u0078\u006d\u006cn\u0073"},Value :"\u0068\u0074\u0074\u0070:\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073.\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067/\u0073\u0063\u0068\u0065\u006da\u004c\u0069\u0062\u0072\u0061\u0072\u0079\u002f\u0032\u0030\u0030\u0036\u002f\u006d\u0061\u0069\u006e"});
start .Attr =append (start .Attr ,_e .Attr {Name :_e .Name {Local :"\u0078\u006d\u006c\u006e\u0073\u003a\u006d\u0061"},Value :"\u0068\u0074\u0074\u0070:\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073.\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067/\u0073\u0063\u0068\u0065\u006da\u004c\u0069\u0062\u0072\u0061\u0072\u0079\u002f\u0032\u0030\u0030\u0036\u002f\u006d\u0061\u0069\u006e"});
start .Attr =append (start .Attr ,_e .Attr {Name :_e .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0078\u006dl"},Value :"\u0068\u0074tp\u003a\u002f\u002fw\u0077\u0077\u002e\u00773.o\u0072g/\u0058\u004d\u004c\u002f\u0031\u0039\u00398/\u006e\u0061\u006d\u0065\u0073\u0070\u0061c\u0065"});
start .Name .Local ="\u006d\u0061:\u0073\u0063\u0068e\u006d\u0061\u004c\u0069\u0062\u0072\u0061\u0072\u0079";return _cg .CT_SchemaLibrary .MarshalXML (e ,start );};type SchemaLibrary struct{CT_SchemaLibrary };func NewCT_SchemaLibrary ()*CT_SchemaLibrary {_fb :=&CT_SchemaLibrary {};
return _fb };func (_ff *CT_SchemaLibrary )UnmarshalXML (d *_e .Decoder ,start _e .StartElement )error {_ed :for {_cc ,_ab :=d .Token ();if _ab !=nil {return _ab ;};switch _ac :=_cc .(type ){case _e .StartElement :switch _ac .Name {case _e .Name {Space :"\u0068\u0074\u0074\u0070:\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073.\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067/\u0073\u0063\u0068\u0065\u006da\u004c\u0069\u0062\u0072\u0061\u0072\u0079\u002f\u0032\u0030\u0030\u0036\u002f\u006d\u0061\u0069\u006e",Local :"\u0073\u0063\u0068\u0065\u006d\u0061"}:_abb :=NewCT_Schema ();
if _ec :=d .DecodeElement (_abb ,&_ac );_ec !=nil {return _ec ;};_ff .Schema =append (_ff .Schema ,_abb );default:_f .Log .Debug ("\u0073\u006b\u0069\u0070\u0070i\u006e\u0067\u0020\u0075\u006e\u0073\u0075\u0070\u0070\u006f\u0072\u0074\u0065d\u0020\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0043\u0054\u005f\u0053\u0063\u0068\u0065\u006d\u0061\u004c\u0069\u0062\u0072\u0061\u0072\u0079\u0020\u0025v",_ac .Name );
if _gf :=d .Skip ();_gf !=nil {return _gf ;};};case _e .EndElement :break _ed ;case _e .CharData :};};return nil ;};

// Validate validates the SchemaLibrary and its children
func (_gaa *SchemaLibrary )Validate ()error {return _gaa .ValidateWithPath ("\u0053\u0063\u0068\u0065\u006d\u0061\u004c\u0069\u0062\u0072\u0061\u0072\u0079");};

// Validate validates the CT_SchemaLibrary and its children
func (_age *CT_SchemaLibrary )Validate ()error {return _age .ValidateWithPath ("\u0043\u0054_\u0053\u0063\u0068e\u006d\u0061\u004c\u0069\u0062\u0072\u0061\u0072\u0079");};func NewCT_Schema ()*CT_Schema {_be :=&CT_Schema {};return _be };

// Validate validates the CT_Schema and its children
func (_fd *CT_Schema )Validate ()error {return _fd .ValidateWithPath ("\u0043T\u005f\u0053\u0063\u0068\u0065\u006da");};func NewSchemaLibrary ()*SchemaLibrary {_gg :=&SchemaLibrary {};_gg .CT_SchemaLibrary =*NewCT_SchemaLibrary ();return _gg ;};

// ValidateWithPath validates the CT_Schema and its children, prefixing error messages with path
func (_dfa *CT_Schema )ValidateWithPath (path string )error {return nil };

// ValidateWithPath validates the SchemaLibrary and its children, prefixing error messages with path
func (_eb *SchemaLibrary )ValidateWithPath (path string )error {if _ccf :=_eb .CT_SchemaLibrary .ValidateWithPath (path );_ccf !=nil {return _ccf ;};return nil ;};func (_cgf *SchemaLibrary )UnmarshalXML (d *_e .Decoder ,start _e .StartElement )error {_cgf .CT_SchemaLibrary =*NewCT_SchemaLibrary ();
_ggg :for {_gea ,_eca :=d .Token ();if _eca !=nil {return _eca ;};switch _bg :=_gea .(type ){case _e .StartElement :switch _bg .Name {case _e .Name {Space :"\u0068\u0074\u0074\u0070:\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073.\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067/\u0073\u0063\u0068\u0065\u006da\u004c\u0069\u0062\u0072\u0061\u0072\u0079\u002f\u0032\u0030\u0030\u0036\u002f\u006d\u0061\u0069\u006e",Local :"\u0073\u0063\u0068\u0065\u006d\u0061"}:_fdf :=NewCT_Schema ();
if _fc :=d .DecodeElement (_fdf ,&_bg );_fc !=nil {return _fc ;};_cgf .Schema =append (_cgf .Schema ,_fdf );default:_f .Log .Debug ("\u0073\u006b\u0069\u0070\u0070\u0069\u006e\u0067 \u0075\u006e\u0073up\u0070\u006f\u0072\u0074\u0065\u0064 \u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0053\u0063\u0068\u0065m\u0061\u004c\u0069\u0062\u0072\u0061\u0072\u0079 \u0025\u0076",_bg .Name );
if _ad :=d .Skip ();_ad !=nil {return _ad ;};};case _e .EndElement :break _ggg ;case _e .CharData :};};return nil ;};type CT_SchemaLibrary struct{

// Custom XML Schema Reference
Schema []*CT_Schema ;};func (_df *CT_Schema )MarshalXML (e *_e .Encoder ,start _e .StartElement )error {if _df .UriAttr !=nil {start .Attr =append (start .Attr ,_e .Attr {Name :_e .Name {Local :"\u006d\u0061\u003a\u0075\u0072\u0069"},Value :_a .Sprintf ("\u0025\u0076",*_df .UriAttr )});
};if _df .ManifestLocationAttr !=nil {start .Attr =append (start .Attr ,_e .Attr {Name :_e .Name {Local :"\u006d\u0061\u003a\u006dan\u0069\u0066\u0065\u0073\u0074\u004c\u006f\u0063\u0061\u0074\u0069\u006f\u006e"},Value :_a .Sprintf ("\u0025\u0076",*_df .ManifestLocationAttr )});
};if _df .SchemaLocationAttr !=nil {start .Attr =append (start .Attr ,_e .Attr {Name :_e .Name {Local :"\u006d\u0061\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u004c\u006f\u0063a\u0074\u0069\u006f\u006e"},Value :_a .Sprintf ("\u0025\u0076",*_df .SchemaLocationAttr )});
};if _df .SchemaLanguageAttr !=nil {start .Attr =append (start .Attr ,_e .Attr {Name :_e .Name {Local :"\u006d\u0061\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u004c\u0061\u006eg\u0075\u0061\u0067\u0065"},Value :_a .Sprintf ("\u0025\u0076",*_df .SchemaLanguageAttr )});
};e .EncodeToken (start );e .EncodeToken (_e .EndElement {Name :start .Name });return nil ;};type CT_Schema struct{

// Custom XML Schema Namespace
UriAttr *string ;

// Supplementary XML File Location
ManifestLocationAttr *string ;

// Custom XML Schema Location
SchemaLocationAttr *string ;

// Schema Language
SchemaLanguageAttr *string ;};

// ValidateWithPath validates the CT_SchemaLibrary and its children, prefixing error messages with path
func (_fad *CT_SchemaLibrary )ValidateWithPath (path string )error {for _fbf ,_acb :=range _fad .Schema {if _gfg :=_acb .ValidateWithPath (_a .Sprintf ("\u0025\u0073\u002f\u0053\u0063\u0068\u0065\u006d\u0061\u005b\u0025\u0064\u005d",path ,_fbf ));_gfg !=nil {return _gfg ;
};};return nil ;};func (_fa *CT_SchemaLibrary )MarshalXML (e *_e .Encoder ,start _e .StartElement )error {e .EncodeToken (start );if _fa .Schema !=nil {_da :=_e .StartElement {Name :_e .Name {Local :"\u006da\u003a\u0073\u0063\u0068\u0065\u006da"}};for _ ,_dgd :=range _fa .Schema {e .EncodeElement (_dgd ,_da );
};};e .EncodeToken (_e .EndElement {Name :start .Name });return nil ;};func (_fg *CT_Schema )UnmarshalXML (d *_e .Decoder ,start _e .StartElement )error {for _ ,_gc :=range start .Attr {if _gc .Name .Local =="\u0075\u0072\u0069"{_aa :=_gc .Value ;_fg .UriAttr =&_aa ;
continue ;};if _gc .Name .Local =="\u006d\u0061n\u0069\u0066\u0065s\u0074\u004c\u006f\u0063\u0061\u0074\u0069\u006f\u006e"{_ag :=_gc .Value ;_fg .ManifestLocationAttr =&_ag ;continue ;};if _gc .Name .Local =="\u0073\u0063\u0068\u0065\u006d\u0061\u004c\u006f\u0063a\u0074\u0069\u006f\u006e"{_ea :=_gc .Value ;
_fg .SchemaLocationAttr =&_ea ;continue ;};if _gc .Name .Local =="\u0073\u0063\u0068\u0065\u006d\u0061\u004c\u0061\u006eg\u0075\u0061\u0067\u0065"{_gb :=_gc .Value ;_fg .SchemaLanguageAttr =&_gb ;continue ;};};for {_ge ,_ga :=d .Token ();if _ga !=nil {return _a .Errorf ("p\u0061\u0072\u0073\u0069ng\u0020C\u0054\u005f\u0053\u0063\u0068e\u006d\u0061\u003a\u0020\u0025\u0073",_ga );
};if _dg ,_c :=_ge .(_e .EndElement );_c &&_dg .Name ==start .Name {break ;};};return nil ;};func init (){_b .RegisterConstructor ("\u0068\u0074\u0074\u0070:\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073.\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067/\u0073\u0063\u0068\u0065\u006da\u004c\u0069\u0062\u0072\u0061\u0072\u0079\u002f\u0032\u0030\u0030\u0036\u002f\u006d\u0061\u0069\u006e","\u0043T\u005f\u0053\u0063\u0068\u0065\u006da",NewCT_Schema );
_b .RegisterConstructor ("\u0068\u0074\u0074\u0070:\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073.\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067/\u0073\u0063\u0068\u0065\u006da\u004c\u0069\u0062\u0072\u0061\u0072\u0079\u002f\u0032\u0030\u0030\u0036\u002f\u006d\u0061\u0069\u006e","\u0043\u0054_\u0053\u0063\u0068e\u006d\u0061\u004c\u0069\u0062\u0072\u0061\u0072\u0079",NewCT_SchemaLibrary );
_b .RegisterConstructor ("\u0068\u0074\u0074\u0070:\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073.\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067/\u0073\u0063\u0068\u0065\u006da\u004c\u0069\u0062\u0072\u0061\u0072\u0079\u002f\u0032\u0030\u0030\u0036\u002f\u006d\u0061\u0069\u006e","\u0073\u0063\u0068\u0065\u006d\u0061\u004c\u0069\u0062\u0072\u0061\u0072\u0079",NewSchemaLibrary );
};