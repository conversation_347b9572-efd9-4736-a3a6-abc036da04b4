//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package picture ;import (_b "encoding/xml";_e "github.com/unidoc/unioffice/v2";_c "github.com/unidoc/unioffice/v2/common/logger";_a "github.com/unidoc/unioffice/v2/schema/soo/dml";);

// ValidateWithPath validates the CT_Picture and its children, prefixing error messages with path
func (_ec *CT_Picture )ValidateWithPath (path string )error {if _ed :=_ec .NvPicPr .ValidateWithPath (path +"\u002f\u004e\u0076\u0050\u0069\u0063\u0050\u0072");_ed !=nil {return _ed ;};if _bf :=_ec .BlipFill .ValidateWithPath (path +"\u002fB\u006c\u0069\u0070\u0046\u0069\u006cl");
_bf !=nil {return _bf ;};if _ba :=_ec .SpPr .ValidateWithPath (path +"\u002f\u0053\u0070P\u0072");_ba !=nil {return _ba ;};return nil ;};func NewPic ()*Pic {_fa :=&Pic {};_fa .CT_Picture =*NewCT_Picture ();return _fa };func (_ge *CT_Picture )MarshalXML (e *_b .Encoder ,start _b .StartElement )error {e .EncodeToken (start );
_d :=_b .StartElement {Name :_b .Name {Local :"p\u0069\u0063\u003a\u006e\u0076\u0050\u0069\u0063\u0050\u0072"}};e .EncodeElement (_ge .NvPicPr ,_d );_ga :=_b .StartElement {Name :_b .Name {Local :"\u0070\u0069\u0063:\u0062\u006c\u0069\u0070\u0046\u0069\u006c\u006c"}};
e .EncodeElement (_ge .BlipFill ,_ga );_dc :=_b .StartElement {Name :_b .Name {Local :"\u0070\u0069\u0063\u003a\u0073\u0070\u0050\u0072"}};e .EncodeElement (_ge .SpPr ,_dc );e .EncodeToken (_b .EndElement {Name :start .Name });return nil ;};

// ValidateWithPath validates the CT_PictureNonVisual and its children, prefixing error messages with path
func (_dbg *CT_PictureNonVisual )ValidateWithPath (path string )error {if _ad :=_dbg .CNvPr .ValidateWithPath (path +"\u002f\u0043\u004e\u0076\u0050\u0072");_ad !=nil {return _ad ;};if _cab :=_dbg .CNvPicPr .ValidateWithPath (path +"\u002fC\u004e\u0076\u0050\u0069\u0063\u0050r");
_cab !=nil {return _cab ;};return nil ;};func (_eg *CT_Picture )UnmarshalXML (d *_b .Decoder ,start _b .StartElement )error {_eg .NvPicPr =NewCT_PictureNonVisual ();_eg .BlipFill =_a .NewCT_BlipFillProperties ();_eg .SpPr =_a .NewCT_ShapeProperties ();
_ag :for {_cc ,_df :=d .Token ();if _df !=nil {return _df ;};switch _gb :=_cc .(type ){case _b .StartElement :switch _gb .Name {case _b .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068e\u006d\u0061\u0073.\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006frg\u002f\u0064\u0072\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0070\u0069\u0063\u0074\u0075\u0072\u0065",Local :"\u006ev\u0050\u0069\u0063\u0050\u0072"},_b .Name {Space :"\u0068t\u0074\u0070:\u002f\u002f\u0070\u0075r\u006c\u002e\u006fc\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006fxm\u006c\u002f\u0064r\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0070i\u0063\u0074u\u0072\u0065",Local :"\u006ev\u0050\u0069\u0063\u0050\u0072"}:if _db :=d .DecodeElement (_eg .NvPicPr ,&_gb );
_db !=nil {return _db ;};case _b .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068e\u006d\u0061\u0073.\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006frg\u002f\u0064\u0072\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0070\u0069\u0063\u0074\u0075\u0072\u0065",Local :"\u0062\u006c\u0069\u0070\u0046\u0069\u006c\u006c"},_b .Name {Space :"\u0068t\u0074\u0070:\u002f\u002f\u0070\u0075r\u006c\u002e\u006fc\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006fxm\u006c\u002f\u0064r\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0070i\u0063\u0074u\u0072\u0065",Local :"\u0062\u006c\u0069\u0070\u0046\u0069\u006c\u006c"}:if _bc :=d .DecodeElement (_eg .BlipFill ,&_gb );
_bc !=nil {return _bc ;};case _b .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068e\u006d\u0061\u0073.\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006frg\u002f\u0064\u0072\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0070\u0069\u0063\u0074\u0075\u0072\u0065",Local :"\u0073\u0070\u0050\u0072"},_b .Name {Space :"\u0068t\u0074\u0070:\u002f\u002f\u0070\u0075r\u006c\u002e\u006fc\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006fxm\u006c\u002f\u0064r\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0070i\u0063\u0074u\u0072\u0065",Local :"\u0073\u0070\u0050\u0072"}:if _be :=d .DecodeElement (_eg .SpPr ,&_gb );
_be !=nil {return _be ;};default:_c .Log .Debug ("\u0073k\u0069\u0070p\u0069\u006e\u0067 \u0075\u006e\u0073\u0075\u0070\u0070\u006fr\u0074\u0065\u0064\u0020\u0065\u006ce\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0043\u0054\u005fP\u0069\u0063\u0074\u0075\u0072\u0065\u0020\u0025\u0076",_gb .Name );
if _cb :=d .Skip ();_cb !=nil {return _cb ;};};case _b .EndElement :break _ag ;case _b .CharData :};};return nil ;};func NewCT_Picture ()*CT_Picture {_f :=&CT_Picture {};_f .NvPicPr =NewCT_PictureNonVisual ();_f .BlipFill =_a .NewCT_BlipFillProperties ();
_f .SpPr =_a .NewCT_ShapeProperties ();return _f ;};

// Validate validates the CT_PictureNonVisual and its children
func (_dg *CT_PictureNonVisual )Validate ()error {return _dg .ValidateWithPath ("\u0043\u0054\u005f\u0050ic\u0074\u0075\u0072\u0065\u004e\u006f\u006e\u0056\u0069\u0073\u0075\u0061\u006c");};type CT_PictureNonVisual struct{

// Non-Visual Drawing Properties
CNvPr *_a .CT_NonVisualDrawingProps ;

// Non-Visual Picture Drawing Properties
CNvPicPr *_a .CT_NonVisualPictureProperties ;};type CT_Picture struct{

// Non-Visual Picture Properties
NvPicPr *CT_PictureNonVisual ;

// Picture Fill
BlipFill *_a .CT_BlipFillProperties ;

// Shape Properties
SpPr *_a .CT_ShapeProperties ;};

// Validate validates the CT_Picture and its children
func (_dfc *CT_Picture )Validate ()error {return _dfc .ValidateWithPath ("\u0043\u0054\u005f\u0050\u0069\u0063\u0074\u0075\u0072\u0065");};type Pic struct{CT_Picture };

// Validate validates the Pic and its children
func (_fdc *Pic )Validate ()error {return _fdc .ValidateWithPath ("\u0050\u0069\u0063")};

// ValidateWithPath validates the Pic and its children, prefixing error messages with path
func (_eda *Pic )ValidateWithPath (path string )error {if _gbe :=_eda .CT_Picture .ValidateWithPath (path );_gbe !=nil {return _gbe ;};return nil ;};func (_bfe *CT_PictureNonVisual )UnmarshalXML (d *_b .Decoder ,start _b .StartElement )error {_bfe .CNvPr =_a .NewCT_NonVisualDrawingProps ();
_bfe .CNvPicPr =_a .NewCT_NonVisualPictureProperties ();_gd :for {_bb ,_ca :=d .Token ();if _ca !=nil {return _ca ;};switch _ce :=_bb .(type ){case _b .StartElement :switch _ce .Name {case _b .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068e\u006d\u0061\u0073.\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006frg\u002f\u0064\u0072\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0070\u0069\u0063\u0074\u0075\u0072\u0065",Local :"\u0063\u004e\u0076P\u0072"},_b .Name {Space :"\u0068t\u0074\u0070:\u002f\u002f\u0070\u0075r\u006c\u002e\u006fc\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006fxm\u006c\u002f\u0064r\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0070i\u0063\u0074u\u0072\u0065",Local :"\u0063\u004e\u0076P\u0072"}:if _gde :=d .DecodeElement (_bfe .CNvPr ,&_ce );
_gde !=nil {return _gde ;};case _b .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068e\u006d\u0061\u0073.\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006frg\u002f\u0064\u0072\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0070\u0069\u0063\u0074\u0075\u0072\u0065",Local :"\u0063\u004e\u0076\u0050\u0069\u0063\u0050\u0072"},_b .Name {Space :"\u0068t\u0074\u0070:\u002f\u002f\u0070\u0075r\u006c\u002e\u006fc\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006fxm\u006c\u002f\u0064r\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0070i\u0063\u0074u\u0072\u0065",Local :"\u0063\u004e\u0076\u0050\u0069\u0063\u0050\u0072"}:if _af :=d .DecodeElement (_bfe .CNvPicPr ,&_ce );
_af !=nil {return _af ;};default:_c .Log .Debug ("\u0073\u006b\u0069\u0070\u0070\u0069\u006e\u0067\u0020\u0075\u006e\u0073\u0075\u0070\u0070o\u0072\u0074\u0065\u0064\u0020\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020o\u006e\u0020\u0043\u0054\u005f\u0050\u0069\u0063\u0074\u0075\u0072\u0065No\u006e\u0056\u0069\u0073\u0075\u0061\u006c\u0020\u0025\u0076",_ce .Name );
if _gf :=d .Skip ();_gf !=nil {return _gf ;};};case _b .EndElement :break _gd ;case _b .CharData :};};return nil ;};func (_dbe *Pic )UnmarshalXML (d *_b .Decoder ,start _b .StartElement )error {_dbe .CT_Picture =*NewCT_Picture ();_adb :for {_cbf ,_eb :=d .Token ();
if _eb !=nil {return _eb ;};switch _bee :=_cbf .(type ){case _b .StartElement :switch _bee .Name {case _b .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068e\u006d\u0061\u0073.\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006frg\u002f\u0064\u0072\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0070\u0069\u0063\u0074\u0075\u0072\u0065",Local :"\u006ev\u0050\u0069\u0063\u0050\u0072"},_b .Name {Space :"\u0068t\u0074\u0070:\u002f\u002f\u0070\u0075r\u006c\u002e\u006fc\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006fxm\u006c\u002f\u0064r\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0070i\u0063\u0074u\u0072\u0065",Local :"\u006ev\u0050\u0069\u0063\u0050\u0072"}:if _bbf :=d .DecodeElement (_dbe .NvPicPr ,&_bee );
_bbf !=nil {return _bbf ;};case _b .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068e\u006d\u0061\u0073.\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006frg\u002f\u0064\u0072\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0070\u0069\u0063\u0074\u0075\u0072\u0065",Local :"\u0062\u006c\u0069\u0070\u0046\u0069\u006c\u006c"},_b .Name {Space :"\u0068t\u0074\u0070:\u002f\u002f\u0070\u0075r\u006c\u002e\u006fc\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006fxm\u006c\u002f\u0064r\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0070i\u0063\u0074u\u0072\u0065",Local :"\u0062\u006c\u0069\u0070\u0046\u0069\u006c\u006c"}:if _gfb :=d .DecodeElement (_dbe .BlipFill ,&_bee );
_gfb !=nil {return _gfb ;};case _b .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068e\u006d\u0061\u0073.\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006frg\u002f\u0064\u0072\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0070\u0069\u0063\u0074\u0075\u0072\u0065",Local :"\u0073\u0070\u0050\u0072"},_b .Name {Space :"\u0068t\u0074\u0070:\u002f\u002f\u0070\u0075r\u006c\u002e\u006fc\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006fxm\u006c\u002f\u0064r\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0070i\u0063\u0074u\u0072\u0065",Local :"\u0073\u0070\u0050\u0072"}:if _ff :=d .DecodeElement (_dbe .SpPr ,&_bee );
_ff !=nil {return _ff ;};default:_c .Log .Debug ("\u0073\u006b\u0069\u0070\u0070\u0069\u006e\u0067\u0020\u0075\u006e\u0073\u0075\u0070\u0070\u006f\u0072\u0074\u0065\u0064\u0020\u0065\u006c\u0065m\u0065\u006e\u0074\u0020\u006fn\u0020\u0050i\u0063\u0020\u0025\u0076",_bee .Name );
if _de :=d .Skip ();_de !=nil {return _de ;};};case _b .EndElement :break _adb ;case _b .CharData :};};return nil ;};func NewCT_PictureNonVisual ()*CT_PictureNonVisual {_ecg :=&CT_PictureNonVisual {};_ecg .CNvPr =_a .NewCT_NonVisualDrawingProps ();_ecg .CNvPicPr =_a .NewCT_NonVisualPictureProperties ();
return _ecg ;};func (_fe *CT_PictureNonVisual )MarshalXML (e *_b .Encoder ,start _b .StartElement )error {e .EncodeToken (start );_fd :=_b .StartElement {Name :_b .Name {Local :"\u0070i\u0063\u003a\u0063\u004e\u0076\u0050r"}};e .EncodeElement (_fe .CNvPr ,_fd );
_dcf :=_b .StartElement {Name :_b .Name {Local :"\u0070\u0069\u0063:\u0063\u004e\u0076\u0050\u0069\u0063\u0050\u0072"}};e .EncodeElement (_fe .CNvPicPr ,_dcf );e .EncodeToken (_b .EndElement {Name :start .Name });return nil ;};func (_egb *Pic )MarshalXML (e *_b .Encoder ,start _b .StartElement )error {start .Attr =append (start .Attr ,_b .Attr {Name :_b .Name {Local :"\u0078\u006d\u006cn\u0073"},Value :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068e\u006d\u0061\u0073.\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006frg\u002f\u0064\u0072\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0070\u0069\u0063\u0074\u0075\u0072\u0065"});
start .Attr =append (start .Attr ,_b .Attr {Name :_b .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0070\u0069c"},Value :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068e\u006d\u0061\u0073.\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006frg\u002f\u0064\u0072\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0070\u0069\u0063\u0074\u0075\u0072\u0065"});
start .Attr =append (start .Attr ,_b .Attr {Name :_b .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0078\u006dl"},Value :"\u0068\u0074tp\u003a\u002f\u002fw\u0077\u0077\u002e\u00773.o\u0072g/\u0058\u004d\u004c\u002f\u0031\u0039\u00398/\u006e\u0061\u006d\u0065\u0073\u0070\u0061c\u0065"});
start .Name .Local ="\u0070i\u0063\u003a\u0070\u0069\u0063";return _egb .CT_Picture .MarshalXML (e ,start );};func init (){_e .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068e\u006d\u0061\u0073.\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006frg\u002f\u0064\u0072\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0070\u0069\u0063\u0074\u0075\u0072\u0065","\u0043\u0054\u005f\u0050ic\u0074\u0075\u0072\u0065\u004e\u006f\u006e\u0056\u0069\u0073\u0075\u0061\u006c",NewCT_PictureNonVisual );
_e .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068e\u006d\u0061\u0073.\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006frg\u002f\u0064\u0072\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0070\u0069\u0063\u0074\u0075\u0072\u0065","\u0043\u0054\u005f\u0050\u0069\u0063\u0074\u0075\u0072\u0065",NewCT_Picture );
_e .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068e\u006d\u0061\u0073.\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006frg\u002f\u0064\u0072\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0070\u0069\u0063\u0074\u0075\u0072\u0065","\u0070\u0069\u0063",NewPic );
};