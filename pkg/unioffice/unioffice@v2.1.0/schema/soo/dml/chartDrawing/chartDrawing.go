//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package chartDrawing ;import (_a "encoding/xml";_g "fmt";_e "github.com/unidoc/unioffice/v2";_dd "github.com/unidoc/unioffice/v2/common/logger";_dg "github.com/unidoc/unioffice/v2/schema/soo/dml";_f "strconv";);type CT_ConnectorNonVisual struct{

// Chart Non Visual Properties
CNvPr *_dg .CT_NonVisualDrawingProps ;

// Non-Visual Connection Shape Drawing Properties
CNvCxnSpPr *_dg .CT_NonVisualConnectorProperties ;};func NewCT_GraphicFrameNonVisual ()*CT_GraphicFrameNonVisual {_fe :=&CT_GraphicFrameNonVisual {};_fe .CNvPr =_dg .NewCT_NonVisualDrawingProps ();_fe .CNvGraphicFramePr =_dg .NewCT_NonVisualGraphicFrameProperties ();
return _fe ;};func (_geb *CT_Picture )MarshalXML (e *_a .Encoder ,start _a .StartElement )error {if _geb .MacroAttr !=nil {start .Attr =append (start .Attr ,_a .Attr {Name :_a .Name {Local :"\u006d\u0061\u0063r\u006f"},Value :_g .Sprintf ("\u0025\u0076",*_geb .MacroAttr )});
};if _geb .FPublishedAttr !=nil {start .Attr =append (start .Attr ,_a .Attr {Name :_a .Name {Local :"\u0066\u0050\u0075\u0062\u006c\u0069\u0073\u0068\u0065\u0064"},Value :_g .Sprintf ("\u0025\u0064",_fffd (*_geb .FPublishedAttr ))});};e .EncodeToken (start );
_cfg :=_a .StartElement {Name :_a .Name {Local :"\u006ev\u0050\u0069\u0063\u0050\u0072"}};e .EncodeElement (_geb .NvPicPr ,_cfg );_baf :=_a .StartElement {Name :_a .Name {Local :"\u0062\u006c\u0069\u0070\u0046\u0069\u006c\u006c"}};e .EncodeElement (_geb .BlipFill ,_baf );
_defa :=_a .StartElement {Name :_a .Name {Local :"\u0073\u0070\u0050\u0072"}};e .EncodeElement (_geb .SpPr ,_defa );if _geb .Style !=nil {_bbe :=_a .StartElement {Name :_a .Name {Local :"\u0073\u0074\u0079l\u0065"}};e .EncodeElement (_geb .Style ,_bbe );
};e .EncodeToken (_a .EndElement {Name :start .Name });return nil ;};func (_gce *CT_ShapeNonVisual )MarshalXML (e *_a .Encoder ,start _a .StartElement )error {e .EncodeToken (start );_fec :=_a .StartElement {Name :_a .Name {Local :"\u0063\u004e\u0076P\u0072"}};
e .EncodeElement (_gce .CNvPr ,_fec );_cdb :=_a .StartElement {Name :_a .Name {Local :"\u0063N\u0076\u0053\u0070\u0050\u0072"}};e .EncodeElement (_gce .CNvSpPr ,_cdb );e .EncodeToken (_a .EndElement {Name :start .Name });return nil ;};

// Validate validates the CT_GroupShapeChoice and its children
func (_gcg *CT_GroupShapeChoice )Validate ()error {return _gcg .ValidateWithPath ("\u0043\u0054\u005f\u0047ro\u0075\u0070\u0053\u0068\u0061\u0070\u0065\u0043\u0068\u006f\u0069\u0063\u0065");};

// ValidateWithPath validates the CT_Connector and its children, prefixing error messages with path
func (_fce *CT_Connector )ValidateWithPath (path string )error {if _adg :=_fce .NvCxnSpPr .ValidateWithPath (path +"\u002f\u004e\u0076\u0043\u0078\u006e\u0053\u0070\u0050\u0072");_adg !=nil {return _adg ;};if _ge :=_fce .SpPr .ValidateWithPath (path +"\u002f\u0053\u0070P\u0072");
_ge !=nil {return _ge ;};if _fce .Style !=nil {if _gg :=_fce .Style .ValidateWithPath (path +"\u002f\u0053\u0074\u0079\u006c\u0065");_gg !=nil {return _gg ;};};return nil ;};type CT_AbsSizeAnchor struct{From *CT_Marker ;

// Shape Extent
Ext *_dg .CT_PositiveSize2D ;ObjectChoicesChoice *EG_ObjectChoicesChoice ;};func (_gge *EG_Anchor )UnmarshalXML (d *_a .Decoder ,start _a .StartElement )error {_gge .AnchorChoice =NewEG_AnchorChoice ();_abge :for {_cefg ,_abe :=d .Token ();if _abe !=nil {return _abe ;
};switch _aebc :=_cefg .(type ){case _a .StartElement :switch _aebc .Name {case _a .Name {Space :"h\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061t\u0073.\u006f\u0072\u0067\u002fd\u0072\u0061w\u0069\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0063\u0068\u0061\u0072\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0072\u0065\u006c\u0053\u0069\u007a\u0065\u0041\u006e\u0063\u0068\u006f\u0072"}:_gge .AnchorChoice =NewEG_AnchorChoice ();
if _bfgc :=d .DecodeElement (&_gge .AnchorChoice .RelSizeAnchor ,&_aebc );_bfgc !=nil {return _bfgc ;};case _a .Name {Space :"h\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061t\u0073.\u006f\u0072\u0067\u002fd\u0072\u0061w\u0069\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0063\u0068\u0061\u0072\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0061\u0062\u0073\u0053\u0069\u007a\u0065\u0041\u006e\u0063\u0068\u006f\u0072"}:_gge .AnchorChoice =NewEG_AnchorChoice ();
if _cfcd :=d .DecodeElement (&_gge .AnchorChoice .AbsSizeAnchor ,&_aebc );_cfcd !=nil {return _cfcd ;};default:_dd .Log .Debug ("\u0073k\u0069\u0070p\u0069\u006e\u0067\u0020u\u006e\u0073\u0075p\u0070\u006f\u0072\u0074\u0065\u0064\u0020\u0065\u006cem\u0065\u006e\u0074 \u006f\u006e \u0045\u0047\u005f\u0041\u006e\u0063h\u006f\u0072 \u0025\u0076",_aebc .Name );
if _agag :=d .Skip ();_agag !=nil {return _agag ;};};case _a .EndElement :break _abge ;case _a .CharData :};};return nil ;};func NewCT_GraphicFrame ()*CT_GraphicFrame {_cbe :=&CT_GraphicFrame {};_cbe .NvGraphicFramePr =NewCT_GraphicFrameNonVisual ();_cbe .Xfrm =_dg .NewCT_Transform2D ();
_cbe .Graphic =_dg .NewGraphic ();return _cbe ;};func NewEG_Anchor ()*EG_Anchor {_dafa :=&EG_Anchor {};_dafa .AnchorChoice =NewEG_AnchorChoice ();return _dafa ;};

// ValidateWithPath validates the CT_GroupShape and its children, prefixing error messages with path
func (_acf *CT_GroupShape )ValidateWithPath (path string )error {if _bg :=_acf .NvGrpSpPr .ValidateWithPath (path +"\u002f\u004e\u0076\u0047\u0072\u0070\u0053\u0070\u0050\u0072");_bg !=nil {return _bg ;};if _dcd :=_acf .GrpSpPr .ValidateWithPath (path +"\u002f\u0047\u0072\u0070\u0053\u0070\u0050\u0072");
_dcd !=nil {return _dcd ;};for _caad ,_ebd :=range _acf .GroupShapeChoice {if _fgbf :=_ebd .ValidateWithPath (_g .Sprintf ("\u0025\u0073\u002fGr\u006f\u0075\u0070\u0053\u0068\u0061\u0070\u0065\u0043\u0068\u006f\u0069\u0063\u0065\u005b\u0025\u0064\u005d",path ,_caad ));
_fgbf !=nil {return _fgbf ;};};return nil ;};type CT_RelSizeAnchor struct{

// Starting Anchor Point
From *CT_Marker ;

// Ending Anchor Point
To *CT_Marker ;ObjectChoicesChoice *EG_ObjectChoicesChoice ;};

// Validate validates the CT_GroupShape and its children
func (_fgbc *CT_GroupShape )Validate ()error {return _fgbc .ValidateWithPath ("\u0043\u0054\u005f\u0047\u0072\u006f\u0075\u0070\u0053\u0068\u0061\u0070\u0065");};func (_fa *CT_ConnectorNonVisual )UnmarshalXML (d *_a .Decoder ,start _a .StartElement )error {_fa .CNvPr =_dg .NewCT_NonVisualDrawingProps ();
_fa .CNvCxnSpPr =_dg .NewCT_NonVisualConnectorProperties ();_eb :for {_cab ,_efc :=d .Token ();if _efc !=nil {return _efc ;};switch _bac :=_cab .(type ){case _a .StartElement :switch _bac .Name {case _a .Name {Space :"h\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061t\u0073.\u006f\u0072\u0067\u002fd\u0072\u0061w\u0069\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0063\u0068\u0061\u0072\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0063\u004e\u0076P\u0072"}:if _ce :=d .DecodeElement (_fa .CNvPr ,&_bac );
_ce !=nil {return _ce ;};case _a .Name {Space :"h\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061t\u0073.\u006f\u0072\u0067\u002fd\u0072\u0061w\u0069\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0063\u0068\u0061\u0072\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0063\u004e\u0076\u0043\u0078\u006e\u0053\u0070\u0050\u0072"}:if _ac :=d .DecodeElement (_fa .CNvCxnSpPr ,&_bac );
_ac !=nil {return _ac ;};default:_dd .Log .Debug ("\u0073\u006b\u0069\u0070\u0070\u0069n\u0067\u0020\u0075n\u0073\u0075\u0070p\u006f\u0072\u0074\u0065\u0064\u0020\u0065\u006c\u0065\u006de\u006e\u0074\u0020\u006f\u006e C\u0054\u005f\u0043\u006f\u006e\u006e\u0065\u0063\u0074\u006f\u0072\u004e\u006f\u006e\u0056\u0069\u0073\u0075\u0061\u006c\u0020\u0025\u0076",_bac .Name );
if _bcc :=d .Skip ();_bcc !=nil {return _bcc ;};};case _a .EndElement :break _eb ;case _a .CharData :};};return nil ;};func (_ae *CT_Drawing )MarshalXML (e *_a .Encoder ,start _a .StartElement )error {start .Name .Local ="\u0043\u0054\u005f\u0044\u0072\u0061\u0077\u0069\u006e\u0067";
e .EncodeToken (start );if _ae .EG_Anchor !=nil {for _ ,_eba :=range _ae .EG_Anchor {_eba .MarshalXML (e ,_a .StartElement {});};};e .EncodeToken (_a .EndElement {Name :start .Name });return nil ;};func (_ceb *CT_Marker )UnmarshalXML (d *_a .Decoder ,start _a .StartElement )error {_ceb .X =0.0;
_ceb .Y =0.0;_ffff :for {_fabc ,_ccdd :=d .Token ();if _ccdd !=nil {return _ccdd ;};switch _ccc :=_fabc .(type ){case _a .StartElement :switch _ccc .Name {case _a .Name {Space :"h\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061t\u0073.\u006f\u0072\u0067\u002fd\u0072\u0061w\u0069\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0063\u0068\u0061\u0072\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0078"}:if _fcd :=d .DecodeElement (&_ceb .X ,&_ccc );
_fcd !=nil {return _fcd ;};case _a .Name {Space :"h\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061t\u0073.\u006f\u0072\u0067\u002fd\u0072\u0061w\u0069\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0063\u0068\u0061\u0072\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0079"}:if _fgg :=d .DecodeElement (&_ceb .Y ,&_ccc );
_fgg !=nil {return _fgg ;};default:_dd .Log .Debug ("\u0073k\u0069\u0070p\u0069\u006e\u0067\u0020u\u006e\u0073\u0075p\u0070\u006f\u0072\u0074\u0065\u0064\u0020\u0065\u006cem\u0065\u006e\u0074 \u006f\u006e \u0043\u0054\u005f\u004d\u0061\u0072k\u0065\u0072 \u0025\u0076",_ccc .Name );
if _abf :=d .Skip ();_abf !=nil {return _abf ;};};case _a .EndElement :break _ffff ;case _a .CharData :};};return nil ;};func (_ffcd *CT_Shape )MarshalXML (e *_a .Encoder ,start _a .StartElement )error {if _ffcd .MacroAttr !=nil {start .Attr =append (start .Attr ,_a .Attr {Name :_a .Name {Local :"\u006d\u0061\u0063r\u006f"},Value :_g .Sprintf ("\u0025\u0076",*_ffcd .MacroAttr )});
};if _ffcd .TextlinkAttr !=nil {start .Attr =append (start .Attr ,_a .Attr {Name :_a .Name {Local :"\u0074\u0065\u0078\u0074\u006c\u0069\u006e\u006b"},Value :_g .Sprintf ("\u0025\u0076",*_ffcd .TextlinkAttr )});};if _ffcd .FLocksTextAttr !=nil {start .Attr =append (start .Attr ,_a .Attr {Name :_a .Name {Local :"\u0066\u004c\u006f\u0063\u006b\u0073\u0054\u0065\u0078\u0074"},Value :_g .Sprintf ("\u0025\u0064",_fffd (*_ffcd .FLocksTextAttr ))});
};if _ffcd .FPublishedAttr !=nil {start .Attr =append (start .Attr ,_a .Attr {Name :_a .Name {Local :"\u0066\u0050\u0075\u0062\u006c\u0069\u0073\u0068\u0065\u0064"},Value :_g .Sprintf ("\u0025\u0064",_fffd (*_ffcd .FPublishedAttr ))});};e .EncodeToken (start );
_agff :=_a .StartElement {Name :_a .Name {Local :"\u006e\u0076\u0053\u0070\u0050\u0072"}};e .EncodeElement (_ffcd .NvSpPr ,_agff );_deg :=_a .StartElement {Name :_a .Name {Local :"\u0073\u0070\u0050\u0072"}};e .EncodeElement (_ffcd .SpPr ,_deg );if _ffcd .Style !=nil {_gbae :=_a .StartElement {Name :_a .Name {Local :"\u0073\u0074\u0079l\u0065"}};
e .EncodeElement (_ffcd .Style ,_gbae );};if _ffcd .TxBody !=nil {_bfg :=_a .StartElement {Name :_a .Name {Local :"\u0074\u0078\u0042\u006f\u0064\u0079"}};e .EncodeElement (_ffcd .TxBody ,_bfg );};e .EncodeToken (_a .EndElement {Name :start .Name });return nil ;
};func (_bdd *CT_Drawing )UnmarshalXML (d *_a .Decoder ,start _a .StartElement )error {_db :for {_dfe ,_cga :=d .Token ();if _cga !=nil {return _cga ;};switch _fd :=_dfe .(type ){case _a .StartElement :switch _fd .Name {case _a .Name {Space :"h\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061t\u0073.\u006f\u0072\u0067\u002fd\u0072\u0061w\u0069\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0063\u0068\u0061\u0072\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0072\u0065\u006c\u0053\u0069\u007a\u0065\u0041\u006e\u0063\u0068\u006f\u0072"}:_eg :=NewEG_Anchor ();
_eg .AnchorChoice =NewEG_AnchorChoice ();_bdd .EG_Anchor =append (_bdd .EG_Anchor ,_eg );if _aef :=d .DecodeElement (&_eg .AnchorChoice .RelSizeAnchor ,&_fd );_aef !=nil {return _aef ;};case _a .Name {Space :"h\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061t\u0073.\u006f\u0072\u0067\u002fd\u0072\u0061w\u0069\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0063\u0068\u0061\u0072\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0061\u0062\u0073\u0053\u0069\u007a\u0065\u0041\u006e\u0063\u0068\u006f\u0072"}:_cb :=NewEG_Anchor ();
_cb .AnchorChoice =NewEG_AnchorChoice ();_bdd .EG_Anchor =append (_bdd .EG_Anchor ,_cb );if _cge :=d .DecodeElement (&_cb .AnchorChoice .AbsSizeAnchor ,&_fd );_cge !=nil {return _cge ;};default:_dd .Log .Debug ("\u0073k\u0069\u0070p\u0069\u006e\u0067 \u0075\u006e\u0073\u0075\u0070\u0070\u006fr\u0074\u0065\u0064\u0020\u0065\u006ce\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0043\u0054\u005fD\u0072\u0061\u0077\u0069\u006e\u0067\u0020\u0025\u0076",_fd .Name );
if _bfe :=d .Skip ();_bfe !=nil {return _bfe ;};};case _a .EndElement :break _db ;case _a .CharData :};};return nil ;};

// ValidateWithPath validates the CT_Drawing and its children, prefixing error messages with path
func (_aefd *CT_Drawing )ValidateWithPath (path string )error {for _cbc ,_def :=range _aefd .EG_Anchor {if _ebg :=_def .ValidateWithPath (_g .Sprintf ("\u0025\u0073/\u0045\u0047\u005fA\u006e\u0063\u0068\u006f\u0072\u005b\u0025\u0064\u005d",path ,_cbc ));
_ebg !=nil {return _ebg ;};};return nil ;};func (_c *CT_AbsSizeAnchor )MarshalXML (e *_a .Encoder ,start _a .StartElement )error {e .EncodeToken (start );_ad :=_a .StartElement {Name :_a .Name {Local :"\u0066\u0072\u006f\u006d"}};e .EncodeElement (_c .From ,_ad );
_de :=_a .StartElement {Name :_a .Name {Local :"\u0065\u0078\u0074"}};e .EncodeElement (_c .Ext ,_de );_c .ObjectChoicesChoice .MarshalXML (e ,_a .StartElement {});e .EncodeToken (_a .EndElement {Name :start .Name });return nil ;};func (_fba *EG_AnchorChoice )UnmarshalXML (d *_a .Decoder ,start _a .StartElement )error {_eegaa :=start ;
switch start .Name {case _a .Name {Space :"h\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061t\u0073.\u006f\u0072\u0067\u002fd\u0072\u0061w\u0069\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0063\u0068\u0061\u0072\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0072\u0065\u006c\u0053\u0069\u007a\u0065\u0041\u006e\u0063\u0068\u006f\u0072"}:_fba .RelSizeAnchor =NewCT_RelSizeAnchor ();
if _bcfe :=d .DecodeElement (_fba .RelSizeAnchor ,&_eegaa );_bcfe !=nil {return _bcfe ;};case _a .Name {Space :"h\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061t\u0073.\u006f\u0072\u0067\u002fd\u0072\u0061w\u0069\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0063\u0068\u0061\u0072\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0061\u0062\u0073\u0053\u0069\u007a\u0065\u0041\u006e\u0063\u0068\u006f\u0072"}:_fba .AbsSizeAnchor =NewCT_AbsSizeAnchor ();
if _dca :=d .DecodeElement (_fba .AbsSizeAnchor ,&_eegaa );_dca !=nil {return _dca ;};default:_dd .Log .Debug ("\u0073\u006b\u0069\u0070\u0070\u0069\u006e\u0067\u0020\u0075\u006e\u0073\u0075\u0070\u0070\u006f\u0072\u0074e\u0064\u0020\u0065\u006c\u0065\u006d\u0065n\u0074\u0020\u006f\u006e\u0020\u0045\u0047\u005f\u0041\u006e\u0063h\u006f\u0072\u0043\u0068\u006f\u0069\u0063\u0065\u0020\u0025\u0076",_eegaa .Name );
if _ebgf :=d .Skip ();_ebgf !=nil {return _ebgf ;};};return nil ;};

// ValidateWithPath validates the CT_AbsSizeAnchor and its children, prefixing error messages with path
func (_cc *CT_AbsSizeAnchor )ValidateWithPath (path string )error {if _fc :=_cc .From .ValidateWithPath (path +"\u002f\u0046\u0072o\u006d");_fc !=nil {return _fc ;};if _eef :=_cc .Ext .ValidateWithPath (path +"\u002f\u0045\u0078\u0074");_eef !=nil {return _eef ;
};if _dgg :=_cc .ObjectChoicesChoice .ValidateWithPath (path +"/\u004fb\u006a\u0065\u0063\u0074\u0043\u0068\u006f\u0069c\u0065\u0073\u0043\u0068oi\u0063\u0065");_dgg !=nil {return _dgg ;};return nil ;};

// ValidateWithPath validates the CT_GraphicFrame and its children, prefixing error messages with path
func (_cbg *CT_GraphicFrame )ValidateWithPath (path string )error {if _fgd :=_cbg .NvGraphicFramePr .ValidateWithPath (path +"\u002f\u004e\u0076\u0047\u0072\u0061\u0070\u0068\u0069\u0063\u0046\u0072a\u006d\u0065\u0050\u0072");_fgd !=nil {return _fgd ;};
if _ggc :=_cbg .Xfrm .ValidateWithPath (path +"\u002f\u0058\u0066r\u006d");_ggc !=nil {return _ggc ;};if _ggg :=_cbg .Graphic .ValidateWithPath (path +"\u002f\u0047\u0072\u0061\u0070\u0068\u0069\u0063");_ggg !=nil {return _ggg ;};return nil ;};

// ValidateWithPath validates the EG_ObjectChoices and its children, prefixing error messages with path
func (_ffe *EG_ObjectChoices )ValidateWithPath (path string )error {if _abdg :=_ffe .ObjectChoicesChoice .ValidateWithPath (path +"/\u004fb\u006a\u0065\u0063\u0074\u0043\u0068\u006f\u0069c\u0065\u0073\u0043\u0068oi\u0063\u0065");_abdg !=nil {return _abdg ;
};return nil ;};

// Validate validates the CT_Picture and its children
func (_fbc *CT_Picture )Validate ()error {return _fbc .ValidateWithPath ("\u0043\u0054\u005f\u0050\u0069\u0063\u0074\u0075\u0072\u0065");};

// Validate validates the EG_ObjectChoices and its children
func (_fgdg *EG_ObjectChoices )Validate ()error {return _fgdg .ValidateWithPath ("\u0045\u0047_\u004f\u0062\u006ae\u0063\u0074\u0043\u0068\u006f\u0069\u0063\u0065\u0073");};func NewCT_Picture ()*CT_Picture {_ffc :=&CT_Picture {};_ffc .NvPicPr =NewCT_PictureNonVisual ();
_ffc .BlipFill =_dg .NewCT_BlipFillProperties ();_ffc .SpPr =_dg .NewCT_ShapeProperties ();return _ffc ;};func (_dda *CT_GraphicFrameNonVisual )MarshalXML (e *_a .Encoder ,start _a .StartElement )error {e .EncodeToken (start );_ffa :=_a .StartElement {Name :_a .Name {Local :"\u0063\u004e\u0076P\u0072"}};
e .EncodeElement (_dda .CNvPr ,_ffa );_cad :=_a .StartElement {Name :_a .Name {Local :"\u0063\u004e\u0076\u0047\u0072\u0061\u0070\u0068\u0069\u0063\u0046\u0072a\u006d\u0065\u0050\u0072"}};e .EncodeElement (_dda .CNvGraphicFramePr ,_cad );e .EncodeToken (_a .EndElement {Name :start .Name });
return nil ;};

// ValidateWithPath validates the CT_RelSizeAnchor and its children, prefixing error messages with path
func (_ddg *CT_RelSizeAnchor )ValidateWithPath (path string )error {if _fag :=_ddg .From .ValidateWithPath (path +"\u002f\u0046\u0072o\u006d");_fag !=nil {return _fag ;};if _abgd :=_ddg .To .ValidateWithPath (path +"\u002f\u0054\u006f");_abgd !=nil {return _abgd ;
};if _ggd :=_ddg .ObjectChoicesChoice .ValidateWithPath (path +"/\u004fb\u006a\u0065\u0063\u0074\u0043\u0068\u006f\u0069c\u0065\u0073\u0043\u0068oi\u0063\u0065");_ggd !=nil {return _ggd ;};return nil ;};

// ValidateWithPath validates the EG_Anchor and its children, prefixing error messages with path
func (_bgbf *EG_Anchor )ValidateWithPath (path string )error {if _dgd :=_bgbf .AnchorChoice .ValidateWithPath (path +"\u002f\u0041\u006e\u0063\u0068\u006f\u0072\u0043\u0068\u006f\u0069\u0063\u0065");_dgd !=nil {return _dgd ;};return nil ;};func (_efb *CT_Marker )MarshalXML (e *_a .Encoder ,start _a .StartElement )error {e .EncodeToken (start );
_aab :=_a .StartElement {Name :_a .Name {Local :"\u0078"}};e .EncodeElement (_efb .X ,_aab );_fcea :=_a .StartElement {Name :_a .Name {Local :"\u0079"}};e .EncodeElement (_efb .Y ,_fcea );e .EncodeToken (_a .EndElement {Name :start .Name });return nil ;
};type CT_GroupShape struct{

// Non-Visual Group Shape Properties
NvGrpSpPr *CT_GroupShapeNonVisual ;

// Group Shape Properties
GrpSpPr *_dg .CT_GroupShapeProperties ;GroupShapeChoice []*CT_GroupShapeChoice ;};func NewCT_RelSizeAnchor ()*CT_RelSizeAnchor {_aage :=&CT_RelSizeAnchor {};_aage .From =NewCT_Marker ();_aage .To =NewCT_Marker ();_aage .ObjectChoicesChoice =NewEG_ObjectChoicesChoice ();
return _aage ;};func NewCT_GroupShapeNonVisual ()*CT_GroupShapeNonVisual {_dgb :=&CT_GroupShapeNonVisual {};_dgb .CNvPr =_dg .NewCT_NonVisualDrawingProps ();_dgb .CNvGrpSpPr =_dg .NewCT_NonVisualGroupDrawingShapeProps ();return _dgb ;};

// Validate validates the CT_ConnectorNonVisual and its children
func (_dc *CT_ConnectorNonVisual )Validate ()error {return _dc .ValidateWithPath ("C\u0054\u005f\u0043\u006fnn\u0065c\u0074\u006f\u0072\u004e\u006fn\u0056\u0069\u0073\u0075\u0061\u006c");};type CT_GroupShapeChoice struct{Sp *CT_Shape ;GrpSp *CT_GroupShape ;
GraphicFrame *CT_GraphicFrame ;CxnSp *CT_Connector ;Pic *CT_Picture ;};

// ValidateWithPath validates the EG_AnchorChoice and its children, prefixing error messages with path
func (_fga *EG_AnchorChoice )ValidateWithPath (path string )error {if _fga .RelSizeAnchor !=nil {if _gca :=_fga .RelSizeAnchor .ValidateWithPath (path +"\u002f\u0052\u0065\u006c\u0053\u0069\u007a\u0065\u0041n\u0063\u0068\u006f\u0072");_gca !=nil {return _gca ;
};};if _fga .AbsSizeAnchor !=nil {if _eggf :=_fga .AbsSizeAnchor .ValidateWithPath (path +"\u002f\u0041\u0062\u0073\u0053\u0069\u007a\u0065\u0041n\u0063\u0068\u006f\u0072");_eggf !=nil {return _eggf ;};};return nil ;};func (_becf *EG_Anchor )MarshalXML (e *_a .Encoder ,start _a .StartElement )error {_becf .AnchorChoice .MarshalXML (e ,_a .StartElement {});
return nil ;};

// ValidateWithPath validates the CT_Picture and its children, prefixing error messages with path
func (_cbef *CT_Picture )ValidateWithPath (path string )error {if _dcda :=_cbef .NvPicPr .ValidateWithPath (path +"\u002f\u004e\u0076\u0050\u0069\u0063\u0050\u0072");_dcda !=nil {return _dcda ;};if _defe :=_cbef .BlipFill .ValidateWithPath (path +"\u002fB\u006c\u0069\u0070\u0046\u0069\u006cl");
_defe !=nil {return _defe ;};if _befa :=_cbef .SpPr .ValidateWithPath (path +"\u002f\u0053\u0070P\u0072");_befa !=nil {return _befa ;};if _cbef .Style !=nil {if _daf :=_cbef .Style .ValidateWithPath (path +"\u002f\u0053\u0074\u0079\u006c\u0065");_daf !=nil {return _daf ;
};};return nil ;};

// ValidateWithPath validates the CT_GroupShapeChoice and its children, prefixing error messages with path
func (_ddbc *CT_GroupShapeChoice )ValidateWithPath (path string )error {if _ddbc .Sp !=nil {if _bea :=_ddbc .Sp .ValidateWithPath (path +"\u002f\u0053\u0070");_bea !=nil {return _bea ;};};if _ddbc .GrpSp !=nil {if _bfa :=_ddbc .GrpSp .ValidateWithPath (path +"\u002f\u0047\u0072\u0070\u0053\u0070");
_bfa !=nil {return _bfa ;};};if _ddbc .GraphicFrame !=nil {if _bda :=_ddbc .GraphicFrame .ValidateWithPath (path +"\u002f\u0047\u0072\u0061\u0070\u0068\u0069\u0063\u0046\u0072\u0061\u006d\u0065");_bda !=nil {return _bda ;};};if _ddbc .CxnSp !=nil {if _eea :=_ddbc .CxnSp .ValidateWithPath (path +"\u002f\u0043\u0078\u006e\u0053\u0070");
_eea !=nil {return _eea ;};};if _ddbc .Pic !=nil {if _bgc :=_ddbc .Pic .ValidateWithPath (path +"\u002f\u0050\u0069\u0063");_bgc !=nil {return _bgc ;};};return nil ;};func (_bgb *CT_GroupShapeNonVisual )MarshalXML (e *_a .Encoder ,start _a .StartElement )error {e .EncodeToken (start );
_dcf :=_a .StartElement {Name :_a .Name {Local :"\u0063\u004e\u0076P\u0072"}};e .EncodeElement (_bgb .CNvPr ,_dcf );_gee :=_a .StartElement {Name :_a .Name {Local :"\u0063\u004e\u0076\u0047\u0072\u0070\u0053\u0070\u0050\u0072"}};e .EncodeElement (_bgb .CNvGrpSpPr ,_gee );
e .EncodeToken (_a .EndElement {Name :start .Name });return nil ;};

// Validate validates the CT_ShapeNonVisual and its children
func (_ace *CT_ShapeNonVisual )Validate ()error {return _ace .ValidateWithPath ("\u0043\u0054\u005f\u0053\u0068\u0061\u0070\u0065\u004e\u006f\u006e\u0056i\u0073\u0075\u0061\u006c");};func (_efa *CT_GraphicFrame )MarshalXML (e *_a .Encoder ,start _a .StartElement )error {if _efa .MacroAttr !=nil {start .Attr =append (start .Attr ,_a .Attr {Name :_a .Name {Local :"\u006d\u0061\u0063r\u006f"},Value :_g .Sprintf ("\u0025\u0076",*_efa .MacroAttr )});
};if _efa .FPublishedAttr !=nil {start .Attr =append (start .Attr ,_a .Attr {Name :_a .Name {Local :"\u0066\u0050\u0075\u0062\u006c\u0069\u0073\u0068\u0065\u0064"},Value :_g .Sprintf ("\u0025\u0064",_fffd (*_efa .FPublishedAttr ))});};e .EncodeToken (start );
_debc :=_a .StartElement {Name :_a .Name {Local :"\u006e\u0076G\u0072\u0061\u0070h\u0069\u0063\u0046\u0072\u0061\u006d\u0065\u0050\u0072"}};e .EncodeElement (_efa .NvGraphicFramePr ,_debc );_cf :=_a .StartElement {Name :_a .Name {Local :"\u0078\u0066\u0072\u006d"}};
e .EncodeElement (_efa .Xfrm ,_cf );_cfc :=_a .StartElement {Name :_a .Name {Local :"\u0061:\u0067\u0072\u0061\u0070\u0068\u0069c"}};_cfc .Attr =append (_cfc .Attr ,_a .Attr {Name :_a .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0061"},Value :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065m\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006cf\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077\u0069\u006e\u0067m\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u006d\u0061\u0069\u006e"});
e .EncodeElement (_efa .Graphic ,_cfc );e .EncodeToken (_a .EndElement {Name :start .Name });return nil ;};

// Validate validates the EG_ObjectChoicesChoice and its children
func (_aggd *EG_ObjectChoicesChoice )Validate ()error {return _aggd .ValidateWithPath ("\u0045\u0047\u005f\u004fbj\u0065\u0063\u0074\u0043\u0068\u006f\u0069\u0063\u0065\u0073\u0043\u0068\u006f\u0069c\u0065");};func (_dde *EG_ObjectChoices )UnmarshalXML (d *_a .Decoder ,start _a .StartElement )error {_dde .ObjectChoicesChoice =NewEG_ObjectChoicesChoice ();
_bbbe :for {_ddad ,_agab :=d .Token ();if _agab !=nil {return _agab ;};switch _gfa :=_ddad .(type ){case _a .StartElement :switch _gfa .Name {case _a .Name {Space :"h\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061t\u0073.\u006f\u0072\u0067\u002fd\u0072\u0061w\u0069\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0063\u0068\u0061\u0072\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0073\u0070"}:_dde .ObjectChoicesChoice =NewEG_ObjectChoicesChoice ();
if _gea :=d .DecodeElement (&_dde .ObjectChoicesChoice .Sp ,&_gfa );_gea !=nil {return _gea ;};case _a .Name {Space :"h\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061t\u0073.\u006f\u0072\u0067\u002fd\u0072\u0061w\u0069\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0063\u0068\u0061\u0072\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0067\u0072\u0070S\u0070"}:_dde .ObjectChoicesChoice =NewEG_ObjectChoicesChoice ();
if _abd :=d .DecodeElement (&_dde .ObjectChoicesChoice .GrpSp ,&_gfa );_abd !=nil {return _abd ;};case _a .Name {Space :"h\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061t\u0073.\u006f\u0072\u0067\u002fd\u0072\u0061w\u0069\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0063\u0068\u0061\u0072\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0067\u0072\u0061p\u0068\u0069\u0063\u0046\u0072\u0061\u006d\u0065"}:_dde .ObjectChoicesChoice =NewEG_ObjectChoicesChoice ();
if _gcbc :=d .DecodeElement (&_dde .ObjectChoicesChoice .GraphicFrame ,&_gfa );_gcbc !=nil {return _gcbc ;};case _a .Name {Space :"h\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061t\u0073.\u006f\u0072\u0067\u002fd\u0072\u0061w\u0069\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0063\u0068\u0061\u0072\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0063\u0078\u006eS\u0070"}:_dde .ObjectChoicesChoice =NewEG_ObjectChoicesChoice ();
if _cgdg :=d .DecodeElement (&_dde .ObjectChoicesChoice .CxnSp ,&_gfa );_cgdg !=nil {return _cgdg ;};case _a .Name {Space :"h\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061t\u0073.\u006f\u0072\u0067\u002fd\u0072\u0061w\u0069\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0063\u0068\u0061\u0072\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0070\u0069\u0063"}:_dde .ObjectChoicesChoice =NewEG_ObjectChoicesChoice ();
if _ddbg :=d .DecodeElement (&_dde .ObjectChoicesChoice .Pic ,&_gfa );_ddbg !=nil {return _ddbg ;};default:_dd .Log .Debug ("\u0073\u006b\u0069\u0070\u0070i\u006e\u0067\u0020\u0075\u006e\u0073\u0075\u0070\u0070\u006f\u0072\u0074\u0065d\u0020\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0045\u0047\u005f\u004f\u0062\u006a\u0065\u0063\u0074\u0043\u0068\u006f\u0069\u0063\u0065\u0073\u0020\u0025v",_gfa .Name );
if _bbgf :=d .Skip ();_bbgf !=nil {return _bbgf ;};};case _a .EndElement :break _bbbe ;case _a .CharData :};};return nil ;};func (_ccd *CT_GroupShapeChoice )MarshalXML (e *_a .Encoder ,start _a .StartElement )error {if _ccd .Sp !=nil {_fed :=_a .StartElement {Name :_a .Name {Local :"\u0073\u0070"}};
e .EncodeElement (_ccd .Sp ,_fed );}else if _ccd .GrpSp !=nil {_dbe :=_a .StartElement {Name :_a .Name {Local :"\u0067\u0072\u0070S\u0070"}};e .EncodeElement (_ccd .GrpSp ,_dbe );}else if _ccd .GraphicFrame !=nil {_fab :=_a .StartElement {Name :_a .Name {Local :"\u0067\u0072\u0061p\u0068\u0069\u0063\u0046\u0072\u0061\u006d\u0065"}};
e .EncodeElement (_ccd .GraphicFrame ,_fab );}else if _ccd .CxnSp !=nil {_gdb :=_a .StartElement {Name :_a .Name {Local :"\u0063\u0078\u006eS\u0070"}};e .EncodeElement (_ccd .CxnSp ,_gdb );}else if _ccd .Pic !=nil {_cbf :=_a .StartElement {Name :_a .Name {Local :"\u0070\u0069\u0063"}};
e .EncodeElement (_ccd .Pic ,_cbf );};return nil ;};func NewCT_AbsSizeAnchor ()*CT_AbsSizeAnchor {_ag :=&CT_AbsSizeAnchor {};_ag .From =NewCT_Marker ();_ag .Ext =_dg .NewCT_PositiveSize2D ();_ag .ObjectChoicesChoice =NewEG_ObjectChoicesChoice ();return _ag ;
};func (_ccg *CT_GroupShape )MarshalXML (e *_a .Encoder ,start _a .StartElement )error {e .EncodeToken (start );_gef :=_a .StartElement {Name :_a .Name {Local :"\u006ev\u0047\u0072\u0070\u0053\u0070\u0050r"}};e .EncodeElement (_ccg .NvGrpSpPr ,_gef );_dag :=_a .StartElement {Name :_a .Name {Local :"\u0067r\u0070\u0053\u0070\u0050\u0072"}};
e .EncodeElement (_ccg .GrpSpPr ,_dag );if _ccg .GroupShapeChoice !=nil {for _ ,_aeb :=range _ccg .GroupShapeChoice {_aeb .MarshalXML (e ,_a .StartElement {});};};e .EncodeToken (_a .EndElement {Name :start .Name });return nil ;};func (_fcf *EG_AnchorChoice )MarshalXML (e *_a .Encoder ,start _a .StartElement )error {e .EncodeToken (start );
if _fcf .RelSizeAnchor !=nil {_gcge :=_a .StartElement {Name :_a .Name {Local :"\u0072\u0065\u006c\u0053\u0069\u007a\u0065\u0041\u006e\u0063\u0068\u006f\u0072"}};e .EncodeElement (_fcf .RelSizeAnchor ,_gcge );}else if _fcf .AbsSizeAnchor !=nil {_fdc :=_a .StartElement {Name :_a .Name {Local :"\u0061\u0062\u0073\u0053\u0069\u007a\u0065\u0041\u006e\u0063\u0068\u006f\u0072"}};
e .EncodeElement (_fcf .AbsSizeAnchor ,_fdc );};e .EncodeToken (_a .EndElement {Name :start .Name });return nil ;};func NewCT_Marker ()*CT_Marker {_bad :=&CT_Marker {};_bad .X =0.0;_bad .Y =0.0;return _bad };func (_cea *CT_GroupShapeChoice )UnmarshalXML (d *_a .Decoder ,start _a .StartElement )error {_eega :=start ;
switch start .Name {case _a .Name {Space :"h\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061t\u0073.\u006f\u0072\u0067\u002fd\u0072\u0061w\u0069\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0063\u0068\u0061\u0072\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0073\u0070"}:_cea .Sp =NewCT_Shape ();
if _gf :=d .DecodeElement (_cea .Sp ,&_eega );_gf !=nil {return _gf ;};case _a .Name {Space :"h\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061t\u0073.\u006f\u0072\u0067\u002fd\u0072\u0061w\u0069\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0063\u0068\u0061\u0072\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0067\u0072\u0070S\u0070"}:_cea .GrpSp =NewCT_GroupShape ();
if _fgbfd :=d .DecodeElement (_cea .GrpSp ,&_eega );_fgbfd !=nil {return _fgbfd ;};case _a .Name {Space :"h\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061t\u0073.\u006f\u0072\u0067\u002fd\u0072\u0061w\u0069\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0063\u0068\u0061\u0072\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0067\u0072\u0061p\u0068\u0069\u0063\u0046\u0072\u0061\u006d\u0065"}:_cea .GraphicFrame =NewCT_GraphicFrame ();
if _ceaf :=d .DecodeElement (_cea .GraphicFrame ,&_eega );_ceaf !=nil {return _ceaf ;};case _a .Name {Space :"h\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061t\u0073.\u006f\u0072\u0067\u002fd\u0072\u0061w\u0069\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0063\u0068\u0061\u0072\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0063\u0078\u006eS\u0070"}:_cea .CxnSp =NewCT_Connector ();
if _aga :=d .DecodeElement (_cea .CxnSp ,&_eega );_aga !=nil {return _aga ;};case _a .Name {Space :"h\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061t\u0073.\u006f\u0072\u0067\u002fd\u0072\u0061w\u0069\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0063\u0068\u0061\u0072\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0070\u0069\u0063"}:_cea .Pic =NewCT_Picture ();
if _ebga :=d .DecodeElement (_cea .Pic ,&_eega );_ebga !=nil {return _ebga ;};default:_dd .Log .Debug ("\u0073\u006b\u0069\u0070\u0070\u0069\u006e\u0067\u0020\u0075\u006e\u0073\u0075\u0070\u0070o\u0072\u0074\u0065\u0064\u0020\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020o\u006e\u0020\u0043\u0054\u005f\u0047\u0072\u006f\u0075\u0070\u0053\u0068ap\u0065\u0043\u0068\u006f\u0069\u0063\u0065\u0020\u0025\u0076",_eega .Name );
if _eca :=d .Skip ();_eca !=nil {return _eca ;};};return nil ;};type CT_ShapeNonVisual struct{

// Chart Non Visual Properties
CNvPr *_dg .CT_NonVisualDrawingProps ;

// Non-Visual Shape Drawing Properties
CNvSpPr *_dg .CT_NonVisualDrawingShapeProps ;};type CT_Shape struct{

// Reference to Custom Function
MacroAttr *string ;

// Text Link
TextlinkAttr *string ;

// Lock Text
FLocksTextAttr *bool ;

// Publish to Server
FPublishedAttr *bool ;

// Non-Visual Shape Properties
NvSpPr *CT_ShapeNonVisual ;

// Shape Properties
SpPr *_dg .CT_ShapeProperties ;

// Shape Style
Style *_dg .CT_ShapeStyle ;

// Shape Text Body
TxBody *_dg .CT_TextBody ;};

// Validate validates the CT_Drawing and its children
func (_dgf *CT_Drawing )Validate ()error {return _dgf .ValidateWithPath ("\u0043\u0054\u005f\u0044\u0072\u0061\u0077\u0069\u006e\u0067");};

// ValidateWithPath validates the CT_GroupShapeNonVisual and its children, prefixing error messages with path
func (_cef *CT_GroupShapeNonVisual )ValidateWithPath (path string )error {if _fcee :=_cef .CNvPr .ValidateWithPath (path +"\u002f\u0043\u004e\u0076\u0050\u0072");_fcee !=nil {return _fcee ;};if _dccg :=_cef .CNvGrpSpPr .ValidateWithPath (path +"/\u0043\u004e\u0076\u0047\u0072\u0070\u0053\u0070\u0050\u0072");
_dccg !=nil {return _dccg ;};return nil ;};func NewCT_ShapeNonVisual ()*CT_ShapeNonVisual {_begf :=&CT_ShapeNonVisual {};_begf .CNvPr =_dg .NewCT_NonVisualDrawingProps ();_begf .CNvSpPr =_dg .NewCT_NonVisualDrawingShapeProps ();return _begf ;};

// Validate validates the CT_RelSizeAnchor and its children
func (_fegc *CT_RelSizeAnchor )Validate ()error {return _fegc .ValidateWithPath ("\u0043\u0054_\u0052\u0065\u006cS\u0069\u007a\u0065\u0041\u006e\u0063\u0068\u006f\u0072");};type CT_Picture struct{

// Reference to Custom Function
MacroAttr *string ;

// Publish to Server
FPublishedAttr *bool ;

// Non-Visual Picture Properties
NvPicPr *CT_PictureNonVisual ;

// Picture Fill
BlipFill *_dg .CT_BlipFillProperties ;SpPr *_dg .CT_ShapeProperties ;Style *_dg .CT_ShapeStyle ;};func (_cdd *EG_ObjectChoicesChoice )UnmarshalXML (d *_a .Decoder ,start _a .StartElement )error {_dad :=start ;switch start .Name {case _a .Name {Space :"h\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061t\u0073.\u006f\u0072\u0067\u002fd\u0072\u0061w\u0069\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0063\u0068\u0061\u0072\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0073\u0070"}:_cdd .Sp =NewCT_Shape ();
if _bde :=d .DecodeElement (_cdd .Sp ,&_dad );_bde !=nil {return _bde ;};case _a .Name {Space :"h\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061t\u0073.\u006f\u0072\u0067\u002fd\u0072\u0061w\u0069\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0063\u0068\u0061\u0072\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0067\u0072\u0070S\u0070"}:_cdd .GrpSp =NewCT_GroupShape ();
if _acdc :=d .DecodeElement (_cdd .GrpSp ,&_dad );_acdc !=nil {return _acdc ;};case _a .Name {Space :"h\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061t\u0073.\u006f\u0072\u0067\u002fd\u0072\u0061w\u0069\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0063\u0068\u0061\u0072\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0067\u0072\u0061p\u0068\u0069\u0063\u0046\u0072\u0061\u006d\u0065"}:_cdd .GraphicFrame =NewCT_GraphicFrame ();
if _dagg :=d .DecodeElement (_cdd .GraphicFrame ,&_dad );_dagg !=nil {return _dagg ;};case _a .Name {Space :"h\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061t\u0073.\u006f\u0072\u0067\u002fd\u0072\u0061w\u0069\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0063\u0068\u0061\u0072\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0063\u0078\u006eS\u0070"}:_cdd .CxnSp =NewCT_Connector ();
if _fgae :=d .DecodeElement (_cdd .CxnSp ,&_dad );_fgae !=nil {return _fgae ;};case _a .Name {Space :"h\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061t\u0073.\u006f\u0072\u0067\u002fd\u0072\u0061w\u0069\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0063\u0068\u0061\u0072\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0070\u0069\u0063"}:_cdd .Pic =NewCT_Picture ();
if _afab :=d .DecodeElement (_cdd .Pic ,&_dad );_afab !=nil {return _afab ;};default:_dd .Log .Debug ("\u0073\u006b\u0069\u0070p\u0069\u006e\u0067\u0020\u0075\u006e\u0073\u0075\u0070p\u006f\u0072\u0074\u0065\u0064\u0020\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0045G\u005f\u004f\u0062\u006a\u0065c\u0074\u0043\u0068\u006f\u0069\u0063\u0065\u0073\u0043\u0068\u006f\u0069\u0063\u0065\u0020\u0025\u0076",_dad .Name );
if _aeg :=d .Skip ();_aeg !=nil {return _aeg ;};};return nil ;};func (_dff *EG_ObjectChoicesChoice )MarshalXML (e *_a .Encoder ,start _a .StartElement )error {if _dff .Sp !=nil {_cgde :=_a .StartElement {Name :_a .Name {Local :"\u0073\u0070"}};e .EncodeElement (_dff .Sp ,_cgde );
}else if _dff .GrpSp !=nil {_efg :=_a .StartElement {Name :_a .Name {Local :"\u0067\u0072\u0070S\u0070"}};e .EncodeElement (_dff .GrpSp ,_efg );}else if _dff .GraphicFrame !=nil {_ebdg :=_a .StartElement {Name :_a .Name {Local :"\u0067\u0072\u0061p\u0068\u0069\u0063\u0046\u0072\u0061\u006d\u0065"}};
e .EncodeElement (_dff .GraphicFrame ,_ebdg );}else if _dff .CxnSp !=nil {_gdf :=_a .StartElement {Name :_a .Name {Local :"\u0063\u0078\u006eS\u0070"}};e .EncodeElement (_dff .CxnSp ,_gdf );}else if _dff .Pic !=nil {_aba :=_a .StartElement {Name :_a .Name {Local :"\u0070\u0069\u0063"}};
e .EncodeElement (_dff .Pic ,_aba );};return nil ;};

// Validate validates the CT_GroupShapeNonVisual and its children
func (_bge *CT_GroupShapeNonVisual )Validate ()error {return _bge .ValidateWithPath ("\u0043\u0054\u005f\u0047ro\u0075\u0070\u0053\u0068\u0061\u0070\u0065\u004e\u006f\u006e\u0056\u0069\u0073\u0075a\u006c");};func (_gb *CT_GraphicFrameNonVisual )UnmarshalXML (d *_a .Decoder ,start _a .StartElement )error {_gb .CNvPr =_dg .NewCT_NonVisualDrawingProps ();
_gb .CNvGraphicFramePr =_dg .NewCT_NonVisualGraphicFrameProperties ();_ecc :for {_gab ,_eeb :=d .Token ();if _eeb !=nil {return _eeb ;};switch _ega :=_gab .(type ){case _a .StartElement :switch _ega .Name {case _a .Name {Space :"h\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061t\u0073.\u006f\u0072\u0067\u002fd\u0072\u0061w\u0069\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0063\u0068\u0061\u0072\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0063\u004e\u0076P\u0072"}:if _dge :=d .DecodeElement (_gb .CNvPr ,&_ega );
_dge !=nil {return _dge ;};case _a .Name {Space :"h\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061t\u0073.\u006f\u0072\u0067\u002fd\u0072\u0061w\u0069\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0063\u0068\u0061\u0072\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0063\u004e\u0076\u0047\u0072\u0061\u0070\u0068\u0069\u0063\u0046\u0072a\u006d\u0065\u0050\u0072"}:if _dcc :=d .DecodeElement (_gb .CNvGraphicFramePr ,&_ega );
_dcc !=nil {return _dcc ;};default:_dd .Log .Debug ("\u0073\u006b\u0069\u0070\u0070\u0069\u006e\u0067\u0020\u0075\u006e\u0073\u0075\u0070\u0070\u006f\u0072\u0074\u0065\u0064 \u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0043\u0054\u005f\u0047\u0072\u0061\u0070\u0068\u0069\u0063\u0046\u0072\u0061\u006d\u0065\u004e\u006f\u006e\u0056i\u0073\u0075\u0061\u006c\u0020%\u0076",_ega .Name );
if _gec :=d .Skip ();_gec !=nil {return _gec ;};};case _a .EndElement :break _ecc ;case _a .CharData :};};return nil ;};

// ValidateWithPath validates the EG_ObjectChoicesChoice and its children, prefixing error messages with path
func (_bccd *EG_ObjectChoicesChoice )ValidateWithPath (path string )error {if _bccd .Sp !=nil {if _bbbea :=_bccd .Sp .ValidateWithPath (path +"\u002f\u0053\u0070");_bbbea !=nil {return _bbbea ;};};if _bccd .GrpSp !=nil {if _fffb :=_bccd .GrpSp .ValidateWithPath (path +"\u002f\u0047\u0072\u0070\u0053\u0070");
_fffb !=nil {return _fffb ;};};if _bccd .GraphicFrame !=nil {if _eegc :=_bccd .GraphicFrame .ValidateWithPath (path +"\u002f\u0047\u0072\u0061\u0070\u0068\u0069\u0063\u0046\u0072\u0061\u006d\u0065");_eegc !=nil {return _eegc ;};};if _bccd .CxnSp !=nil {if _gaba :=_bccd .CxnSp .ValidateWithPath (path +"\u002f\u0043\u0078\u006e\u0053\u0070");
_gaba !=nil {return _gaba ;};};if _bccd .Pic !=nil {if _cgc :=_bccd .Pic .ValidateWithPath (path +"\u002f\u0050\u0069\u0063");_cgc !=nil {return _cgc ;};};return nil ;};func (_afd *CT_GroupShapeNonVisual )UnmarshalXML (d *_a .Decoder ,start _a .StartElement )error {_afd .CNvPr =_dg .NewCT_NonVisualDrawingProps ();
_afd .CNvGrpSpPr =_dg .NewCT_NonVisualGroupDrawingShapeProps ();_acg :for {_cbb ,_bce :=d .Token ();if _bce !=nil {return _bce ;};switch _ecce :=_cbb .(type ){case _a .StartElement :switch _ecce .Name {case _a .Name {Space :"h\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061t\u0073.\u006f\u0072\u0067\u002fd\u0072\u0061w\u0069\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0063\u0068\u0061\u0072\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0063\u004e\u0076P\u0072"}:if _ab :=d .DecodeElement (_afd .CNvPr ,&_ecce );
_ab !=nil {return _ab ;};case _a .Name {Space :"h\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061t\u0073.\u006f\u0072\u0067\u002fd\u0072\u0061w\u0069\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0063\u0068\u0061\u0072\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0063\u004e\u0076\u0047\u0072\u0070\u0053\u0070\u0050\u0072"}:if _gba :=d .DecodeElement (_afd .CNvGrpSpPr ,&_ecce );
_gba !=nil {return _gba ;};default:_dd .Log .Debug ("\u0073\u006b\u0069\u0070p\u0069\u006e\u0067\u0020\u0075\u006e\u0073\u0075\u0070p\u006f\u0072\u0074\u0065\u0064\u0020\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0043T\u005f\u0047\u0072\u006f\u0075p\u0053\u0068\u0061\u0070\u0065\u004e\u006f\u006e\u0056\u0069\u0073\u0075\u0061\u006c\u0020\u0025\u0076",_ecce .Name );
if _gfb :=d .Skip ();_gfb !=nil {return _gfb ;};};case _a .EndElement :break _acg ;case _a .CharData :};};return nil ;};func (_cac *CT_GraphicFrame )UnmarshalXML (d *_a .Decoder ,start _a .StartElement )error {_cac .NvGraphicFramePr =NewCT_GraphicFrameNonVisual ();
_cac .Xfrm =_dg .NewCT_Transform2D ();_cac .Graphic =_dg .NewGraphic ();for _ ,_ff :=range start .Attr {if _ff .Name .Local =="\u006d\u0061\u0063r\u006f"{_aefc :=_ff .Value ;_cac .MacroAttr =&_aefc ;continue ;};if _ff .Name .Local =="\u0066\u0050\u0075\u0062\u006c\u0069\u0073\u0068\u0065\u0064"{_egg ,_cdga :=_f .ParseBool (_ff .Value );
if _cdga !=nil {return _cdga ;};_cac .FPublishedAttr =&_egg ;continue ;};};_gcc :for {_adf ,_bff :=d .Token ();if _bff !=nil {return _bff ;};switch _ddf :=_adf .(type ){case _a .StartElement :switch _ddf .Name {case _a .Name {Space :"h\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061t\u0073.\u006f\u0072\u0067\u002fd\u0072\u0061w\u0069\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0063\u0068\u0061\u0072\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u006e\u0076G\u0072\u0061\u0070h\u0069\u0063\u0046\u0072\u0061\u006d\u0065\u0050\u0072"}:if _da :=d .DecodeElement (_cac .NvGraphicFramePr ,&_ddf );
_da !=nil {return _da ;};case _a .Name {Space :"h\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061t\u0073.\u006f\u0072\u0067\u002fd\u0072\u0061w\u0069\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0063\u0068\u0061\u0072\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0078\u0066\u0072\u006d"}:if _gcd :=d .DecodeElement (_cac .Xfrm ,&_ddf );
_gcd !=nil {return _gcd ;};case _a .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065m\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006cf\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077\u0069\u006e\u0067m\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u006d\u0061\u0069\u006e",Local :"\u0067r\u0061\u0070\u0068\u0069\u0063"},_a .Name {Space :"\u0068\u0074\u0074\u0070\u003a/\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072g\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u0064\u0072\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u006d\u0061\u0069\u006e",Local :"\u0067r\u0061\u0070\u0068\u0069\u0063"}:if _agf :=d .DecodeElement (_cac .Graphic ,&_ddf );
_agf !=nil {return _agf ;};default:_dd .Log .Debug ("\u0073\u006b\u0069\u0070\u0070\u0069\u006e\u0067\u0020\u0075\u006e\u0073\u0075\u0070\u0070\u006f\u0072\u0074e\u0064\u0020\u0065\u006c\u0065\u006d\u0065n\u0074\u0020\u006f\u006e\u0020\u0043\u0054\u005f\u0047\u0072\u0061p\u0068\u0069\u0063\u0046\u0072\u0061\u006d\u0065\u0020\u0025\u0076",_ddf .Name );
if _bfef :=d .Skip ();_bfef !=nil {return _bfef ;};};case _a .EndElement :break _gcc ;case _a .CharData :};};return nil ;};type CT_GraphicFrameNonVisual struct{

// Non-Visual Drawing Properties
CNvPr *_dg .CT_NonVisualDrawingProps ;

// Non-Visual Graphic Frame Drawing Properties
CNvGraphicFramePr *_dg .CT_NonVisualGraphicFrameProperties ;};type CT_Drawing struct{EG_Anchor []*EG_Anchor ;};func NewCT_GroupShape ()*CT_GroupShape {_af :=&CT_GroupShape {};_af .NvGrpSpPr =NewCT_GroupShapeNonVisual ();_af .GrpSpPr =_dg .NewCT_GroupShapeProperties ();
return _af ;};

// ValidateWithPath validates the CT_Marker and its children, prefixing error messages with path
func (_acfb *CT_Marker )ValidateWithPath (path string )error {if _acfb .X < 0.0{return _g .Errorf ("\u0025\u0073\u002fm\u002e\u0058\u0020\u006du\u0073\u0074\u0020\u0062\u0065\u0020\u003e=\u0020\u0030\u002e\u0030\u0020\u0028\u0068\u0061\u0076\u0065\u0020\u0025\u0076\u0029",path ,_acfb .X );
};if _acfb .X > 1.0{return _g .Errorf ("\u0025\u0073\u002fm\u002e\u0058\u0020\u006du\u0073\u0074\u0020\u0062\u0065\u0020\u003c=\u0020\u0031\u002e\u0030\u0020\u0028\u0068\u0061\u0076\u0065\u0020\u0025\u0076\u0029",path ,_acfb .X );};if _acfb .Y < 0.0{return _g .Errorf ("\u0025\u0073\u002fm\u002e\u0059\u0020\u006du\u0073\u0074\u0020\u0062\u0065\u0020\u003e=\u0020\u0030\u002e\u0030\u0020\u0028\u0068\u0061\u0076\u0065\u0020\u0025\u0076\u0029",path ,_acfb .Y );
};if _acfb .Y > 1.0{return _g .Errorf ("\u0025\u0073\u002fm\u002e\u0059\u0020\u006du\u0073\u0074\u0020\u0062\u0065\u0020\u003c=\u0020\u0031\u002e\u0030\u0020\u0028\u0068\u0061\u0076\u0065\u0020\u0025\u0076\u0029",path ,_acfb .Y );};return nil ;};

// Validate validates the CT_GraphicFrameNonVisual and its children
func (_dfa *CT_GraphicFrameNonVisual )Validate ()error {return _dfa .ValidateWithPath ("\u0043T\u005f\u0047\u0072\u0061\u0070\u0068\u0069\u0063\u0046\u0072\u0061m\u0065\u004e\u006f\u006e\u0056\u0069\u0073\u0075\u0061\u006c");};type CT_GroupShapeNonVisual struct{

// Chart Non Visual Properties
CNvPr *_dg .CT_NonVisualDrawingProps ;

// Non-Visual Group Shape Drawing Properties
CNvGrpSpPr *_dg .CT_NonVisualGroupDrawingShapeProps ;};type EG_AnchorChoice struct{

// Relative Anchor Shape Size
RelSizeAnchor *CT_RelSizeAnchor ;

// Absolute Anchor Shape Size
AbsSizeAnchor *CT_AbsSizeAnchor ;};func (_fb *CT_Connector )MarshalXML (e *_a .Encoder ,start _a .StartElement )error {if _fb .MacroAttr !=nil {start .Attr =append (start .Attr ,_a .Attr {Name :_a .Name {Local :"\u006d\u0061\u0063r\u006f"},Value :_g .Sprintf ("\u0025\u0076",*_fb .MacroAttr )});
};if _fb .FPublishedAttr !=nil {start .Attr =append (start .Attr ,_a .Attr {Name :_a .Name {Local :"\u0066\u0050\u0075\u0062\u006c\u0069\u0073\u0068\u0065\u0064"},Value :_g .Sprintf ("\u0025\u0064",_fffd (*_fb .FPublishedAttr ))});};e .EncodeToken (start );
_ga :=_a .StartElement {Name :_a .Name {Local :"\u006ev\u0043\u0078\u006e\u0053\u0070\u0050r"}};e .EncodeElement (_fb .NvCxnSpPr ,_ga );_cag :=_a .StartElement {Name :_a .Name {Local :"\u0073\u0070\u0050\u0072"}};e .EncodeElement (_fb .SpPr ,_cag );if _fb .Style !=nil {_aa :=_a .StartElement {Name :_a .Name {Local :"\u0073\u0074\u0079l\u0065"}};
e .EncodeElement (_fb .Style ,_aa );};e .EncodeToken (_a .EndElement {Name :start .Name });return nil ;};func _fffd (_bbf bool )uint8 {if _bbf {return 1;};return 0;};func (_agef *CT_Shape )UnmarshalXML (d *_a .Decoder ,start _a .StartElement )error {_agef .NvSpPr =NewCT_ShapeNonVisual ();
_agef .SpPr =_dg .NewCT_ShapeProperties ();for _ ,_eda :=range start .Attr {if _eda .Name .Local =="\u006d\u0061\u0063r\u006f"{_agb :=_eda .Value ;_agef .MacroAttr =&_agb ;continue ;};if _eda .Name .Local =="\u0074\u0065\u0078\u0074\u006c\u0069\u006e\u006b"{_fdfe :=_eda .Value ;
_agef .TextlinkAttr =&_fdfe ;continue ;};if _eda .Name .Local =="\u0066\u004c\u006f\u0063\u006b\u0073\u0054\u0065\u0078\u0074"{_fgc ,_bdf :=_f .ParseBool (_eda .Value );if _bdf !=nil {return _bdf ;};_agef .FLocksTextAttr =&_fgc ;continue ;};if _eda .Name .Local =="\u0066\u0050\u0075\u0062\u006c\u0069\u0073\u0068\u0065\u0064"{_efbc ,_bgbb :=_f .ParseBool (_eda .Value );
if _bgbb !=nil {return _bgbb ;};_agef .FPublishedAttr =&_efbc ;continue ;};};_adfe :for {_aaf ,_ede :=d .Token ();if _ede !=nil {return _ede ;};switch _afb :=_aaf .(type ){case _a .StartElement :switch _afb .Name {case _a .Name {Space :"h\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061t\u0073.\u006f\u0072\u0067\u002fd\u0072\u0061w\u0069\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0063\u0068\u0061\u0072\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u006e\u0076\u0053\u0070\u0050\u0072"}:if _dgbf :=d .DecodeElement (_agef .NvSpPr ,&_afb );
_dgbf !=nil {return _dgbf ;};case _a .Name {Space :"h\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061t\u0073.\u006f\u0072\u0067\u002fd\u0072\u0061w\u0069\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0063\u0068\u0061\u0072\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0073\u0070\u0050\u0072"}:if _afa :=d .DecodeElement (_agef .SpPr ,&_afb );
_afa !=nil {return _afa ;};case _a .Name {Space :"h\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061t\u0073.\u006f\u0072\u0067\u002fd\u0072\u0061w\u0069\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0063\u0068\u0061\u0072\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0073\u0074\u0079l\u0065"}:_agef .Style =_dg .NewCT_ShapeStyle ();
if _gdeb :=d .DecodeElement (_agef .Style ,&_afb );_gdeb !=nil {return _gdeb ;};case _a .Name {Space :"h\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061t\u0073.\u006f\u0072\u0067\u002fd\u0072\u0061w\u0069\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0063\u0068\u0061\u0072\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0074\u0078\u0042\u006f\u0064\u0079"}:_agef .TxBody =_dg .NewCT_TextBody ();
if _efe :=d .DecodeElement (_agef .TxBody ,&_afb );_efe !=nil {return _efe ;};default:_dd .Log .Debug ("\u0073\u006b\u0069\u0070\u0070\u0069\u006eg\u0020\u0075\u006es\u0075\u0070\u0070\u006fr\u0074\u0065\u0064\u0020\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0043\u0054\u005f\u0053\u0068\u0061\u0070\u0065\u0020\u0025\u0076",_afb .Name );
if _bbc :=d .Skip ();_bbc !=nil {return _bbc ;};};case _a .EndElement :break _adfe ;case _a .CharData :};};return nil ;};func (_fabd *CT_RelSizeAnchor )MarshalXML (e *_a .Encoder ,start _a .StartElement )error {e .EncodeToken (start );_bcg :=_a .StartElement {Name :_a .Name {Local :"\u0066\u0072\u006f\u006d"}};
e .EncodeElement (_fabd .From ,_bcg );_cgdf :=_a .StartElement {Name :_a .Name {Local :"\u0074\u006f"}};e .EncodeElement (_fabd .To ,_cgdf );_fabd .ObjectChoicesChoice .MarshalXML (e ,_a .StartElement {});e .EncodeToken (_a .EndElement {Name :start .Name });
return nil ;};func (_agfa *CT_PictureNonVisual )MarshalXML (e *_a .Encoder ,start _a .StartElement )error {e .EncodeToken (start );_gcgc :=_a .StartElement {Name :_a .Name {Local :"\u0063\u004e\u0076P\u0072"}};e .EncodeElement (_agfa .CNvPr ,_gcgc );_dgfc :=_a .StartElement {Name :_a .Name {Local :"\u0063\u004e\u0076\u0050\u0069\u0063\u0050\u0072"}};
e .EncodeElement (_agfa .CNvPicPr ,_dgfc );e .EncodeToken (_a .EndElement {Name :start .Name });return nil ;};type CT_GraphicFrame struct{

// Reference to Custom Function
MacroAttr *string ;

// Publish To Server
FPublishedAttr *bool ;

// Non-Visual Graphic Frame Properties
NvGraphicFramePr *CT_GraphicFrameNonVisual ;

// Graphic Frame Transform
Xfrm *_dg .CT_Transform2D ;Graphic *_dg .Graphic ;};type EG_Anchor struct{AnchorChoice *EG_AnchorChoice ;};func NewCT_ConnectorNonVisual ()*CT_ConnectorNonVisual {_gc :=&CT_ConnectorNonVisual {};_gc .CNvPr =_dg .NewCT_NonVisualDrawingProps ();_gc .CNvCxnSpPr =_dg .NewCT_NonVisualConnectorProperties ();
return _gc ;};func (_ece *CT_ConnectorNonVisual )MarshalXML (e *_a .Encoder ,start _a .StartElement )error {e .EncodeToken (start );_cdg :=_a .StartElement {Name :_a .Name {Local :"\u0063\u004e\u0076P\u0072"}};e .EncodeElement (_ece .CNvPr ,_cdg );_adgf :=_a .StartElement {Name :_a .Name {Local :"\u0063\u004e\u0076\u0043\u0078\u006e\u0053\u0070\u0050\u0072"}};
e .EncodeElement (_ece .CNvCxnSpPr ,_adgf );e .EncodeToken (_a .EndElement {Name :start .Name });return nil ;};

// Validate validates the CT_Shape and its children
func (_edf *CT_Shape )Validate ()error {return _edf .ValidateWithPath ("\u0043\u0054\u005f\u0053\u0068\u0061\u0070\u0065");};type CT_PictureNonVisual struct{CNvPr *_dg .CT_NonVisualDrawingProps ;

// Non-Visual Picture Drawing Properties
CNvPicPr *_dg .CT_NonVisualPictureProperties ;};func (_ade *CT_AbsSizeAnchor )UnmarshalXML (d *_a .Decoder ,start _a .StartElement )error {_ade .From =NewCT_Marker ();_ade .Ext =_dg .NewCT_PositiveSize2D ();_ade .ObjectChoicesChoice =NewEG_ObjectChoicesChoice ();
_ca :for {_caa ,_ec :=d .Token ();if _ec !=nil {return _ec ;};switch _ee :=_caa .(type ){case _a .StartElement :switch _ee .Name {case _a .Name {Space :"h\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061t\u0073.\u006f\u0072\u0067\u002fd\u0072\u0061w\u0069\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0063\u0068\u0061\u0072\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0066\u0072\u006f\u006d"}:if _fg :=d .DecodeElement (_ade .From ,&_ee );
_fg !=nil {return _fg ;};case _a .Name {Space :"h\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061t\u0073.\u006f\u0072\u0067\u002fd\u0072\u0061w\u0069\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0063\u0068\u0061\u0072\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0065\u0078\u0074"}:if _cd :=d .DecodeElement (_ade .Ext ,&_ee );
_cd !=nil {return _cd ;};case _a .Name {Space :"h\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061t\u0073.\u006f\u0072\u0067\u002fd\u0072\u0061w\u0069\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0063\u0068\u0061\u0072\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0073\u0070"}:_ade .ObjectChoicesChoice =NewEG_ObjectChoicesChoice ();
if _eeg :=d .DecodeElement (&_ade .ObjectChoicesChoice .Sp ,&_ee );_eeg !=nil {return _eeg ;};case _a .Name {Space :"h\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061t\u0073.\u006f\u0072\u0067\u002fd\u0072\u0061w\u0069\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0063\u0068\u0061\u0072\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0067\u0072\u0070S\u0070"}:_ade .ObjectChoicesChoice =NewEG_ObjectChoicesChoice ();
if _b :=d .DecodeElement (&_ade .ObjectChoicesChoice .GrpSp ,&_ee );_b !=nil {return _b ;};case _a .Name {Space :"h\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061t\u0073.\u006f\u0072\u0067\u002fd\u0072\u0061w\u0069\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0063\u0068\u0061\u0072\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0067\u0072\u0061p\u0068\u0069\u0063\u0046\u0072\u0061\u006d\u0065"}:_ade .ObjectChoicesChoice =NewEG_ObjectChoicesChoice ();
if _df :=d .DecodeElement (&_ade .ObjectChoicesChoice .GraphicFrame ,&_ee );_df !=nil {return _df ;};case _a .Name {Space :"h\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061t\u0073.\u006f\u0072\u0067\u002fd\u0072\u0061w\u0069\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0063\u0068\u0061\u0072\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0063\u0078\u006eS\u0070"}:_ade .ObjectChoicesChoice =NewEG_ObjectChoicesChoice ();
if _ea :=d .DecodeElement (&_ade .ObjectChoicesChoice .CxnSp ,&_ee );_ea !=nil {return _ea ;};case _a .Name {Space :"h\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061t\u0073.\u006f\u0072\u0067\u002fd\u0072\u0061w\u0069\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0063\u0068\u0061\u0072\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0070\u0069\u0063"}:_ade .ObjectChoicesChoice =NewEG_ObjectChoicesChoice ();
if _gd :=d .DecodeElement (&_ade .ObjectChoicesChoice .Pic ,&_ee );_gd !=nil {return _gd ;};default:_dd .Log .Debug ("\u0073\u006b\u0069\u0070\u0070i\u006e\u0067\u0020\u0075\u006e\u0073\u0075\u0070\u0070\u006f\u0072\u0074\u0065d\u0020\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0043\u0054\u005f\u0041\u0062\u0073\u0053\u0069\u007a\u0065\u0041\u006e\u0063\u0068\u006f\u0072\u0020\u0025v",_ee .Name );
if _age :=d .Skip ();_age !=nil {return _age ;};};case _a .EndElement :break _ca ;case _a .CharData :};};return nil ;};func NewCT_Drawing ()*CT_Drawing {_edb :=&CT_Drawing {};return _edb };

// Validate validates the CT_Marker and its children
func (_acgf *CT_Marker )Validate ()error {return _acgf .ValidateWithPath ("\u0043T\u005f\u004d\u0061\u0072\u006b\u0065r");};type EG_ObjectChoicesChoice struct{Sp *CT_Shape ;GrpSp *CT_GroupShape ;GraphicFrame *CT_GraphicFrame ;CxnSp *CT_Connector ;Pic *CT_Picture ;
};

// Validate validates the CT_PictureNonVisual and its children
func (_cgd *CT_PictureNonVisual )Validate ()error {return _cgd .ValidateWithPath ("\u0043\u0054\u005f\u0050ic\u0074\u0075\u0072\u0065\u004e\u006f\u006e\u0056\u0069\u0073\u0075\u0061\u006c");};

// ValidateWithPath validates the CT_Shape and its children, prefixing error messages with path
func (_gcf *CT_Shape )ValidateWithPath (path string )error {if _caf :=_gcf .NvSpPr .ValidateWithPath (path +"\u002fN\u0076\u0053\u0070\u0050\u0072");_caf !=nil {return _caf ;};if _bace :=_gcf .SpPr .ValidateWithPath (path +"\u002f\u0053\u0070P\u0072");
_bace !=nil {return _bace ;};if _gcf .Style !=nil {if _dfbc :=_gcf .Style .ValidateWithPath (path +"\u002f\u0053\u0074\u0079\u006c\u0065");_dfbc !=nil {return _dfbc ;};};if _gcf .TxBody !=nil {if _eee :=_gcf .TxBody .ValidateWithPath (path +"\u002fT\u0078\u0042\u006f\u0064\u0079");
_eee !=nil {return _eee ;};};return nil ;};type CT_Connector struct{

// Reference to Custom Function
MacroAttr *string ;

// Publish to Server
FPublishedAttr *bool ;

// Connector Non Visual Properties
NvCxnSpPr *CT_ConnectorNonVisual ;

// Shape Properties
SpPr *_dg .CT_ShapeProperties ;

// Connection Shape Style
Style *_dg .CT_ShapeStyle ;};type EG_ObjectChoices struct{ObjectChoicesChoice *EG_ObjectChoicesChoice ;};

// Validate validates the CT_GraphicFrame and its children
func (_fgb *CT_GraphicFrame )Validate ()error {return _fgb .ValidateWithPath ("\u0043T\u005fG\u0072\u0061\u0070\u0068\u0069\u0063\u0046\u0072\u0061\u006d\u0065");};

// ValidateWithPath validates the CT_ConnectorNonVisual and its children, prefixing error messages with path
func (_baa *CT_ConnectorNonVisual )ValidateWithPath (path string )error {if _cabb :=_baa .CNvPr .ValidateWithPath (path +"\u002f\u0043\u004e\u0076\u0050\u0072");_cabb !=nil {return _cabb ;};if _dfg :=_baa .CNvCxnSpPr .ValidateWithPath (path +"/\u0043\u004e\u0076\u0043\u0078\u006e\u0053\u0070\u0050\u0072");
_dfg !=nil {return _dfg ;};return nil ;};func (_gcb *CT_ShapeNonVisual )UnmarshalXML (d *_a .Decoder ,start _a .StartElement )error {_gcb .CNvPr =_dg .NewCT_NonVisualDrawingProps ();_gcb .CNvSpPr =_dg .NewCT_NonVisualDrawingShapeProps ();_cebe :for {_faa ,_bbb :=d .Token ();
if _bbb !=nil {return _bbb ;};switch _cbgc :=_faa .(type ){case _a .StartElement :switch _cbgc .Name {case _a .Name {Space :"h\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061t\u0073.\u006f\u0072\u0067\u002fd\u0072\u0061w\u0069\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0063\u0068\u0061\u0072\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0063\u004e\u0076P\u0072"}:if _efbd :=d .DecodeElement (_gcb .CNvPr ,&_cbgc );
_efbd !=nil {return _efbd ;};case _a .Name {Space :"h\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061t\u0073.\u006f\u0072\u0067\u002fd\u0072\u0061w\u0069\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0063\u0068\u0061\u0072\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0063N\u0076\u0053\u0070\u0050\u0072"}:if _feea :=d .DecodeElement (_gcb .CNvSpPr ,&_cbgc );
_feea !=nil {return _feea ;};default:_dd .Log .Debug ("\u0073\u006bi\u0070\u0070\u0069\u006e\u0067 \u0075\u006e\u0073\u0075\u0070p\u006f\u0072\u0074\u0065\u0064\u0020\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0043\u0054\u005f\u0053\u0068\u0061\u0070\u0065\u004e\u006f\u006e\u0056\u0069\u0073\u0075\u0061\u006c\u0020\u0025\u0076",_cbgc .Name );
if _afe :=d .Skip ();_afe !=nil {return _afe ;};};case _a .EndElement :break _cebe ;case _a .CharData :};};return nil ;};func (_fafg *CT_RelSizeAnchor )UnmarshalXML (d *_a .Decoder ,start _a .StartElement )error {_fafg .From =NewCT_Marker ();_fafg .To =NewCT_Marker ();
_fafg .ObjectChoicesChoice =NewEG_ObjectChoicesChoice ();_bcb :for {_acde ,_bafc :=d .Token ();if _bafc !=nil {return _bafc ;};switch _dabb :=_acde .(type ){case _a .StartElement :switch _dabb .Name {case _a .Name {Space :"h\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061t\u0073.\u006f\u0072\u0067\u002fd\u0072\u0061w\u0069\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0063\u0068\u0061\u0072\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0066\u0072\u006f\u006d"}:if _ceab :=d .DecodeElement (_fafg .From ,&_dabb );
_ceab !=nil {return _ceab ;};case _a .Name {Space :"h\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061t\u0073.\u006f\u0072\u0067\u002fd\u0072\u0061w\u0069\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0063\u0068\u0061\u0072\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0074\u006f"}:if _gfbe :=d .DecodeElement (_fafg .To ,&_dabb );
_gfbe !=nil {return _gfbe ;};case _a .Name {Space :"h\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061t\u0073.\u006f\u0072\u0067\u002fd\u0072\u0061w\u0069\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0063\u0068\u0061\u0072\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0073\u0070"}:_fafg .ObjectChoicesChoice =NewEG_ObjectChoicesChoice ();
if _adae :=d .DecodeElement (&_fafg .ObjectChoicesChoice .Sp ,&_dabb );_adae !=nil {return _adae ;};case _a .Name {Space :"h\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061t\u0073.\u006f\u0072\u0067\u002fd\u0072\u0061w\u0069\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0063\u0068\u0061\u0072\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0067\u0072\u0070S\u0070"}:_fafg .ObjectChoicesChoice =NewEG_ObjectChoicesChoice ();
if _gde :=d .DecodeElement (&_fafg .ObjectChoicesChoice .GrpSp ,&_dabb );_gde !=nil {return _gde ;};case _a .Name {Space :"h\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061t\u0073.\u006f\u0072\u0067\u002fd\u0072\u0061w\u0069\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0063\u0068\u0061\u0072\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0067\u0072\u0061p\u0068\u0069\u0063\u0046\u0072\u0061\u006d\u0065"}:_fafg .ObjectChoicesChoice =NewEG_ObjectChoicesChoice ();
if _abbc :=d .DecodeElement (&_fafg .ObjectChoicesChoice .GraphicFrame ,&_dabb );_abbc !=nil {return _abbc ;};case _a .Name {Space :"h\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061t\u0073.\u006f\u0072\u0067\u002fd\u0072\u0061w\u0069\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0063\u0068\u0061\u0072\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0063\u0078\u006eS\u0070"}:_fafg .ObjectChoicesChoice =NewEG_ObjectChoicesChoice ();
if _fbg :=d .DecodeElement (&_fafg .ObjectChoicesChoice .CxnSp ,&_dabb );_fbg !=nil {return _fbg ;};case _a .Name {Space :"h\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061t\u0073.\u006f\u0072\u0067\u002fd\u0072\u0061w\u0069\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0063\u0068\u0061\u0072\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0070\u0069\u0063"}:_fafg .ObjectChoicesChoice =NewEG_ObjectChoicesChoice ();
if _feg :=d .DecodeElement (&_fafg .ObjectChoicesChoice .Pic ,&_dabb );_feg !=nil {return _feg ;};default:_dd .Log .Debug ("\u0073\u006b\u0069\u0070\u0070i\u006e\u0067\u0020\u0075\u006e\u0073\u0075\u0070\u0070\u006f\u0072\u0074\u0065d\u0020\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0043\u0054\u005f\u0052\u0065\u006c\u0053\u0069\u007a\u0065\u0041\u006e\u0063\u0068\u006f\u0072\u0020\u0025v",_dabb .Name );
if _acb :=d .Skip ();_acb !=nil {return _acb ;};};case _a .EndElement :break _bcb ;case _a .CharData :};};return nil ;};func (_dbf *CT_PictureNonVisual )UnmarshalXML (d *_a .Decoder ,start _a .StartElement )error {_dbf .CNvPr =_dg .NewCT_NonVisualDrawingProps ();
_dbf .CNvPicPr =_dg .NewCT_NonVisualPictureProperties ();_aed :for {_ddfc ,_fbb :=d .Token ();if _fbb !=nil {return _fbb ;};switch _ccb :=_ddfc .(type ){case _a .StartElement :switch _ccb .Name {case _a .Name {Space :"h\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061t\u0073.\u006f\u0072\u0067\u002fd\u0072\u0061w\u0069\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0063\u0068\u0061\u0072\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0063\u004e\u0076P\u0072"}:if _gaa :=d .DecodeElement (_dbf .CNvPr ,&_ccb );
_gaa !=nil {return _gaa ;};case _a .Name {Space :"h\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061t\u0073.\u006f\u0072\u0067\u002fd\u0072\u0061w\u0069\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0063\u0068\u0061\u0072\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0063\u004e\u0076\u0050\u0069\u0063\u0050\u0072"}:if _eeag :=d .DecodeElement (_dbf .CNvPicPr ,&_ccb );
_eeag !=nil {return _eeag ;};default:_dd .Log .Debug ("\u0073\u006b\u0069\u0070\u0070\u0069\u006e\u0067\u0020\u0075\u006e\u0073\u0075\u0070\u0070o\u0072\u0074\u0065\u0064\u0020\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020o\u006e\u0020\u0043\u0054\u005f\u0050\u0069\u0063\u0074\u0075\u0072\u0065No\u006e\u0056\u0069\u0073\u0075\u0061\u006c\u0020\u0025\u0076",_ccb .Name );
if _bdaa :=d .Skip ();_bdaa !=nil {return _bdaa ;};};case _a .EndElement :break _aed ;case _a .CharData :};};return nil ;};func NewEG_AnchorChoice ()*EG_AnchorChoice {_agg :=&EG_AnchorChoice {};return _agg };func (_facc *EG_ObjectChoices )MarshalXML (e *_a .Encoder ,start _a .StartElement )error {_facc .ObjectChoicesChoice .MarshalXML (e ,_a .StartElement {});
return nil ;};func NewCT_GroupShapeChoice ()*CT_GroupShapeChoice {_add :=&CT_GroupShapeChoice {};return _add };

// ValidateWithPath validates the CT_GraphicFrameNonVisual and its children, prefixing error messages with path
func (_fdf *CT_GraphicFrameNonVisual )ValidateWithPath (path string )error {if _dee :=_fdf .CNvPr .ValidateWithPath (path +"\u002f\u0043\u004e\u0076\u0050\u0072");_dee !=nil {return _dee ;};if _dbd :=_fdf .CNvGraphicFramePr .ValidateWithPath (path +"\u002fC\u004ev\u0047\u0072\u0061\u0070\u0068i\u0063\u0046r\u0061\u006d\u0065\u0050\u0072");
_dbd !=nil {return _dbd ;};return nil ;};func NewCT_PictureNonVisual ()*CT_PictureNonVisual {_begg :=&CT_PictureNonVisual {};_begg .CNvPr =_dg .NewCT_NonVisualDrawingProps ();_begg .CNvPicPr =_dg .NewCT_NonVisualPictureProperties ();return _begg ;};func (_ggb *CT_Picture )UnmarshalXML (d *_a .Decoder ,start _a .StartElement )error {_ggb .NvPicPr =NewCT_PictureNonVisual ();
_ggb .BlipFill =_dg .NewCT_BlipFillProperties ();_ggb .SpPr =_dg .NewCT_ShapeProperties ();for _ ,_edc :=range start .Attr {if _edc .Name .Local =="\u006d\u0061\u0063r\u006f"{_ffg :=_edc .Value ;_ggb .MacroAttr =&_ffg ;continue ;};if _edc .Name .Local =="\u0066\u0050\u0075\u0062\u006c\u0069\u0073\u0068\u0065\u0064"{_feb ,_fceg :=_f .ParseBool (_edc .Value );
if _fceg !=nil {return _fceg ;};_ggb .FPublishedAttr =&_feb ;continue ;};};_cdc :for {_fad ,_agc :=d .Token ();if _agc !=nil {return _agc ;};switch _edcg :=_fad .(type ){case _a .StartElement :switch _edcg .Name {case _a .Name {Space :"h\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061t\u0073.\u006f\u0072\u0067\u002fd\u0072\u0061w\u0069\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0063\u0068\u0061\u0072\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u006ev\u0050\u0069\u0063\u0050\u0072"}:if _dgbe :=d .DecodeElement (_ggb .NvPicPr ,&_edcg );
_dgbe !=nil {return _dgbe ;};case _a .Name {Space :"h\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061t\u0073.\u006f\u0072\u0067\u002fd\u0072\u0061w\u0069\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0063\u0068\u0061\u0072\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0062\u006c\u0069\u0070\u0046\u0069\u006c\u006c"}:if _addd :=d .DecodeElement (_ggb .BlipFill ,&_edcg );
_addd !=nil {return _addd ;};case _a .Name {Space :"h\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061t\u0073.\u006f\u0072\u0067\u002fd\u0072\u0061w\u0069\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0063\u0068\u0061\u0072\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0073\u0070\u0050\u0072"}:if _abg :=d .DecodeElement (_ggb .SpPr ,&_edcg );
_abg !=nil {return _abg ;};case _a .Name {Space :"h\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061t\u0073.\u006f\u0072\u0067\u002fd\u0072\u0061w\u0069\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0063\u0068\u0061\u0072\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0073\u0074\u0079l\u0065"}:_ggb .Style =_dg .NewCT_ShapeStyle ();
if _abb :=d .DecodeElement (_ggb .Style ,&_edcg );_abb !=nil {return _abb ;};default:_dd .Log .Debug ("\u0073k\u0069\u0070p\u0069\u006e\u0067 \u0075\u006e\u0073\u0075\u0070\u0070\u006fr\u0074\u0065\u0064\u0020\u0065\u006ce\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0043\u0054\u005fP\u0069\u0063\u0074\u0075\u0072\u0065\u0020\u0025\u0076",_edcg .Name );
if _gfbb :=d .Skip ();_gfbb !=nil {return _gfbb ;};};case _a .EndElement :break _cdc ;case _a .CharData :};};return nil ;};

// Validate validates the EG_AnchorChoice and its children
func (_dfgf *EG_AnchorChoice )Validate ()error {return _dfgf .ValidateWithPath ("\u0045G\u005fA\u006e\u0063\u0068\u006f\u0072\u0043\u0068\u006f\u0069\u0063\u0065");};func NewCT_Shape ()*CT_Shape {_ebdf :=&CT_Shape {};_ebdf .NvSpPr =NewCT_ShapeNonVisual ();
_ebdf .SpPr =_dg .NewCT_ShapeProperties ();return _ebdf ;};func (_fee *CT_GroupShape )UnmarshalXML (d *_a .Decoder ,start _a .StartElement )error {_fee .NvGrpSpPr =NewCT_GroupShapeNonVisual ();_fee .GrpSpPr =_dg .NewCT_GroupShapeProperties ();_ada :for {_bcf ,_acd :=d .Token ();
if _acd !=nil {return _acd ;};switch _fff :=_bcf .(type ){case _a .StartElement :switch _fff .Name {case _a .Name {Space :"h\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061t\u0073.\u006f\u0072\u0067\u002fd\u0072\u0061w\u0069\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0063\u0068\u0061\u0072\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u006ev\u0047\u0072\u0070\u0053\u0070\u0050r"}:if _cgb :=d .DecodeElement (_fee .NvGrpSpPr ,&_fff );
_cgb !=nil {return _cgb ;};case _a .Name {Space :"h\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061t\u0073.\u006f\u0072\u0067\u002fd\u0072\u0061w\u0069\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0063\u0068\u0061\u0072\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0067r\u0070\u0053\u0070\u0050\u0072"}:if _fac :=d .DecodeElement (_fee .GrpSpPr ,&_fff );
_fac !=nil {return _fac ;};case _a .Name {Space :"h\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061t\u0073.\u006f\u0072\u0067\u002fd\u0072\u0061w\u0069\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0063\u0068\u0061\u0072\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0073\u0070"}:_dea :=NewCT_GroupShapeChoice ();
if _aee :=d .DecodeElement (&_dea .Sp ,&_fff );_aee !=nil {return _aee ;};_fee .GroupShapeChoice =append (_fee .GroupShapeChoice ,_dea );case _a .Name {Space :"h\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061t\u0073.\u006f\u0072\u0067\u002fd\u0072\u0061w\u0069\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0063\u0068\u0061\u0072\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0067\u0072\u0070S\u0070"}:_bfd :=NewCT_GroupShapeChoice ();
if _cacg :=d .DecodeElement (&_bfd .GrpSp ,&_fff );_cacg !=nil {return _cacg ;};_fee .GroupShapeChoice =append (_fee .GroupShapeChoice ,_bfd );case _a .Name {Space :"h\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061t\u0073.\u006f\u0072\u0067\u002fd\u0072\u0061w\u0069\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0063\u0068\u0061\u0072\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0067\u0072\u0061p\u0068\u0069\u0063\u0046\u0072\u0061\u006d\u0065"}:_faf :=NewCT_GroupShapeChoice ();
if _edd :=d .DecodeElement (&_faf .GraphicFrame ,&_fff );_edd !=nil {return _edd ;};_fee .GroupShapeChoice =append (_fee .GroupShapeChoice ,_faf );case _a .Name {Space :"h\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061t\u0073.\u006f\u0072\u0067\u002fd\u0072\u0061w\u0069\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0063\u0068\u0061\u0072\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0063\u0078\u006eS\u0070"}:_bcd :=NewCT_GroupShapeChoice ();
if _dfd :=d .DecodeElement (&_bcd .CxnSp ,&_fff );_dfd !=nil {return _dfd ;};_fee .GroupShapeChoice =append (_fee .GroupShapeChoice ,_bcd );case _a .Name {Space :"h\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061t\u0073.\u006f\u0072\u0067\u002fd\u0072\u0061w\u0069\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0063\u0068\u0061\u0072\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0070\u0069\u0063"}:_bbd :=NewCT_GroupShapeChoice ();
if _cde :=d .DecodeElement (&_bbd .Pic ,&_fff );_cde !=nil {return _cde ;};_fee .GroupShapeChoice =append (_fee .GroupShapeChoice ,_bbd );default:_dd .Log .Debug ("\u0073\u006b\u0069\u0070\u0070\u0069\u006e\u0067 \u0075\u006e\u0073up\u0070\u006f\u0072\u0074\u0065\u0064 \u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0043\u0054\u005f\u0047r\u006f\u0075\u0070\u0053\u0068\u0061\u0070\u0065 \u0025\u0076",_fff .Name );
if _dab :=d .Skip ();_dab !=nil {return _dab ;};};case _a .EndElement :break _ada ;case _a .CharData :};};return nil ;};func (_cg *CT_Connector )UnmarshalXML (d *_a .Decoder ,start _a .StartElement )error {_cg .NvCxnSpPr =NewCT_ConnectorNonVisual ();_cg .SpPr =_dg .NewCT_ShapeProperties ();
for _ ,_ed :=range start .Attr {if _ed .Name .Local =="\u006d\u0061\u0063r\u006f"{_ded :=_ed .Value ;_cg .MacroAttr =&_ded ;continue ;};if _ed .Name .Local =="\u0066\u0050\u0075\u0062\u006c\u0069\u0073\u0068\u0065\u0064"{_ba ,_adb :=_f .ParseBool (_ed .Value );
if _adb !=nil {return _adb ;};_cg .FPublishedAttr =&_ba ;continue ;};};_bd :for {_bf ,_ef :=d .Token ();if _ef !=nil {return _ef ;};switch _gag :=_bf .(type ){case _a .StartElement :switch _gag .Name {case _a .Name {Space :"h\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061t\u0073.\u006f\u0072\u0067\u002fd\u0072\u0061w\u0069\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0063\u0068\u0061\u0072\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u006ev\u0043\u0078\u006e\u0053\u0070\u0050r"}:if _bb :=d .DecodeElement (_cg .NvCxnSpPr ,&_gag );
_bb !=nil {return _bb ;};case _a .Name {Space :"h\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061t\u0073.\u006f\u0072\u0067\u002fd\u0072\u0061w\u0069\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0063\u0068\u0061\u0072\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0073\u0070\u0050\u0072"}:if _bbg :=d .DecodeElement (_cg .SpPr ,&_gag );
_bbg !=nil {return _bbg ;};case _a .Name {Space :"h\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061t\u0073.\u006f\u0072\u0067\u002fd\u0072\u0061w\u0069\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0063\u0068\u0061\u0072\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0073\u0074\u0079l\u0065"}:_cg .Style =_dg .NewCT_ShapeStyle ();
if _beg :=d .DecodeElement (_cg .Style ,&_gag );_beg !=nil {return _beg ;};default:_dd .Log .Debug ("s\u006b\u0069\u0070\u0070\u0069\u006e\u0067\u0020\u0075n\u0073\u0075\u0070\u0070\u006f\u0072\u0074ed\u0020\u0065\u006c\u0065m\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0043\u0054_C\u006f\u006en\u0065\u0063\u0074\u006f\u0072\u0020\u0025\u0076",_gag .Name );
if _bc :=d .Skip ();_bc !=nil {return _bc ;};};case _a .EndElement :break _bd ;case _a .CharData :};};return nil ;};type CT_Marker struct{

// Relative X Coordinate
X float64 ;

// Relative Y Coordinate
Y float64 ;};

// Validate validates the CT_AbsSizeAnchor and its children
func (_dfb *CT_AbsSizeAnchor )Validate ()error {return _dfb .ValidateWithPath ("\u0043\u0054_\u0041\u0062\u0073S\u0069\u007a\u0065\u0041\u006e\u0063\u0068\u006f\u0072");};func NewEG_ObjectChoices ()*EG_ObjectChoices {_ecf :=&EG_ObjectChoices {};_ecf .ObjectChoicesChoice =NewEG_ObjectChoicesChoice ();
return _ecf ;};

// ValidateWithPath validates the CT_ShapeNonVisual and its children, prefixing error messages with path
func (_bec *CT_ShapeNonVisual )ValidateWithPath (path string )error {if _bab :=_bec .CNvPr .ValidateWithPath (path +"\u002f\u0043\u004e\u0076\u0050\u0072");_bab !=nil {return _bab ;};if _cec :=_bec .CNvSpPr .ValidateWithPath (path +"\u002f\u0043\u004e\u0076\u0053\u0070\u0050\u0072");
_cec !=nil {return _cec ;};return nil ;};

// Validate validates the CT_Connector and its children
func (_eaf *CT_Connector )Validate ()error {return _eaf .ValidateWithPath ("\u0043\u0054\u005fC\u006f\u006e\u006e\u0065\u0063\u0074\u006f\u0072");};

// Validate validates the EG_Anchor and its children
func (_cfa *EG_Anchor )Validate ()error {return _cfa .ValidateWithPath ("\u0045G\u005f\u0041\u006e\u0063\u0068\u006fr");};func NewEG_ObjectChoicesChoice ()*EG_ObjectChoicesChoice {_aec :=&EG_ObjectChoicesChoice {};return _aec ;};

// ValidateWithPath validates the CT_PictureNonVisual and its children, prefixing error messages with path
func (_fgf *CT_PictureNonVisual )ValidateWithPath (path string )error {if _fdfd :=_fgf .CNvPr .ValidateWithPath (path +"\u002f\u0043\u004e\u0076\u0050\u0072");_fdfd !=nil {return _fdfd ;};if _aag :=_fgf .CNvPicPr .ValidateWithPath (path +"\u002fC\u004e\u0076\u0050\u0069\u0063\u0050r");
_aag !=nil {return _aag ;};return nil ;};func NewCT_Connector ()*CT_Connector {_be :=&CT_Connector {};_be .NvCxnSpPr =NewCT_ConnectorNonVisual ();_be .SpPr =_dg .NewCT_ShapeProperties ();return _be ;};func init (){_e .RegisterConstructor ("h\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061t\u0073.\u006f\u0072\u0067\u002fd\u0072\u0061w\u0069\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0063\u0068\u0061\u0072\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067","\u0043\u0054\u005f\u0053\u0068\u0061\u0070\u0065\u004e\u006f\u006e\u0056i\u0073\u0075\u0061\u006c",NewCT_ShapeNonVisual );
_e .RegisterConstructor ("h\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061t\u0073.\u006f\u0072\u0067\u002fd\u0072\u0061w\u0069\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0063\u0068\u0061\u0072\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067","\u0043\u0054\u005f\u0053\u0068\u0061\u0070\u0065",NewCT_Shape );
_e .RegisterConstructor ("h\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061t\u0073.\u006f\u0072\u0067\u002fd\u0072\u0061w\u0069\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0063\u0068\u0061\u0072\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067","C\u0054\u005f\u0043\u006fnn\u0065c\u0074\u006f\u0072\u004e\u006fn\u0056\u0069\u0073\u0075\u0061\u006c",NewCT_ConnectorNonVisual );
_e .RegisterConstructor ("h\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061t\u0073.\u006f\u0072\u0067\u002fd\u0072\u0061w\u0069\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0063\u0068\u0061\u0072\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067","\u0043\u0054\u005fC\u006f\u006e\u006e\u0065\u0063\u0074\u006f\u0072",NewCT_Connector );
_e .RegisterConstructor ("h\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061t\u0073.\u006f\u0072\u0067\u002fd\u0072\u0061w\u0069\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0063\u0068\u0061\u0072\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067","\u0043\u0054\u005f\u0050ic\u0074\u0075\u0072\u0065\u004e\u006f\u006e\u0056\u0069\u0073\u0075\u0061\u006c",NewCT_PictureNonVisual );
_e .RegisterConstructor ("h\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061t\u0073.\u006f\u0072\u0067\u002fd\u0072\u0061w\u0069\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0063\u0068\u0061\u0072\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067","\u0043\u0054\u005f\u0050\u0069\u0063\u0074\u0075\u0072\u0065",NewCT_Picture );
_e .RegisterConstructor ("h\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061t\u0073.\u006f\u0072\u0067\u002fd\u0072\u0061w\u0069\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0063\u0068\u0061\u0072\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067","\u0043T\u005f\u0047\u0072\u0061\u0070\u0068\u0069\u0063\u0046\u0072\u0061m\u0065\u004e\u006f\u006e\u0056\u0069\u0073\u0075\u0061\u006c",NewCT_GraphicFrameNonVisual );
_e .RegisterConstructor ("h\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061t\u0073.\u006f\u0072\u0067\u002fd\u0072\u0061w\u0069\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0063\u0068\u0061\u0072\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067","\u0043T\u005fG\u0072\u0061\u0070\u0068\u0069\u0063\u0046\u0072\u0061\u006d\u0065",NewCT_GraphicFrame );
_e .RegisterConstructor ("h\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061t\u0073.\u006f\u0072\u0067\u002fd\u0072\u0061w\u0069\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0063\u0068\u0061\u0072\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067","\u0043\u0054\u005f\u0047ro\u0075\u0070\u0053\u0068\u0061\u0070\u0065\u004e\u006f\u006e\u0056\u0069\u0073\u0075a\u006c",NewCT_GroupShapeNonVisual );
_e .RegisterConstructor ("h\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061t\u0073.\u006f\u0072\u0067\u002fd\u0072\u0061w\u0069\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0063\u0068\u0061\u0072\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067","\u0043\u0054\u005f\u0047\u0072\u006f\u0075\u0070\u0053\u0068\u0061\u0070\u0065",NewCT_GroupShape );
_e .RegisterConstructor ("h\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061t\u0073.\u006f\u0072\u0067\u002fd\u0072\u0061w\u0069\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0063\u0068\u0061\u0072\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067","\u0043T\u005f\u004d\u0061\u0072\u006b\u0065r",NewCT_Marker );
_e .RegisterConstructor ("h\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061t\u0073.\u006f\u0072\u0067\u002fd\u0072\u0061w\u0069\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0063\u0068\u0061\u0072\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067","\u0043\u0054_\u0052\u0065\u006cS\u0069\u007a\u0065\u0041\u006e\u0063\u0068\u006f\u0072",NewCT_RelSizeAnchor );
_e .RegisterConstructor ("h\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061t\u0073.\u006f\u0072\u0067\u002fd\u0072\u0061w\u0069\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0063\u0068\u0061\u0072\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067","\u0043\u0054_\u0041\u0062\u0073S\u0069\u007a\u0065\u0041\u006e\u0063\u0068\u006f\u0072",NewCT_AbsSizeAnchor );
_e .RegisterConstructor ("h\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061t\u0073.\u006f\u0072\u0067\u002fd\u0072\u0061w\u0069\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0063\u0068\u0061\u0072\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067","\u0043\u0054\u005f\u0044\u0072\u0061\u0077\u0069\u006e\u0067",NewCT_Drawing );
_e .RegisterConstructor ("h\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061t\u0073.\u006f\u0072\u0067\u002fd\u0072\u0061w\u0069\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0063\u0068\u0061\u0072\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067","\u0045\u0047_\u004f\u0062\u006ae\u0063\u0074\u0043\u0068\u006f\u0069\u0063\u0065\u0073",NewEG_ObjectChoices );
_e .RegisterConstructor ("h\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061t\u0073.\u006f\u0072\u0067\u002fd\u0072\u0061w\u0069\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0063\u0068\u0061\u0072\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067","\u0045G\u005f\u0041\u006e\u0063\u0068\u006fr",NewEG_Anchor );
};