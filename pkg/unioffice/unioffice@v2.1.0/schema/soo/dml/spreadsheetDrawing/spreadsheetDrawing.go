//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package spreadsheetDrawing ;import (_bb "encoding/xml";_d "fmt";_a "github.com/unidoc/unioffice/v2";_c "github.com/unidoc/unioffice/v2/common/logger";_ba "github.com/unidoc/unioffice/v2/schema/soo/dml";_bg "strconv";);type CT_AbsoluteAnchor struct{

// Position
Pos *_ba .CT_Point2D ;

// Shape Extent
Ext *_ba .CT_PositiveSize2D ;ObjectChoicesChoice *EG_ObjectChoicesChoice ;ClientData *CT_AnchorClientData ;};type CT_Connector struct{

// Reference to Custom Function
MacroAttr *string ;

// Publish to Server Flag
FPublishedAttr *bool ;

// Non-Visual Properties for a Connection Shape
NvCxnSpPr *CT_ConnectorNonVisual ;

// Connector Shape Properties
SpPr *_ba .CT_ShapeProperties ;Style *_ba .CT_ShapeStyle ;};

// Validate validates the CT_GraphicalObjectFrameNonVisual and its children
func (_fga *CT_GraphicalObjectFrameNonVisual )Validate ()error {return _fga .ValidateWithPath ("\u0043\u0054\u005f\u0047\u0072\u0061\u0070\u0068\u0069\u0063\u0061\u006c\u004f\u0062\u006ae\u0063t\u0046\u0072\u0061\u006d\u0065\u004e\u006f\u006e\u0056\u0069\u0073\u0075\u0061\u006c");
};

// ValidateWithPath validates the CT_OneCellAnchor and its children, prefixing error messages with path
func (_aaab *CT_OneCellAnchor )ValidateWithPath (path string )error {if _deg :=_aaab .From .ValidateWithPath (path +"\u002f\u0046\u0072o\u006d");_deg !=nil {return _deg ;};if _aaaa :=_aaab .Ext .ValidateWithPath (path +"\u002f\u0045\u0078\u0074");_aaaa !=nil {return _aaaa ;
};if _ffb :=_aaab .ObjectChoicesChoice .ValidateWithPath (path +"/\u004fb\u006a\u0065\u0063\u0074\u0043\u0068\u006f\u0069c\u0065\u0073\u0043\u0068oi\u0063\u0065");_ffb !=nil {return _ffb ;};if _aeae :=_aaab .ClientData .ValidateWithPath (path +"/\u0043\u006c\u0069\u0065\u006e\u0074\u0044\u0061\u0074\u0061");
_aeae !=nil {return _aeae ;};return nil ;};

// ValidateWithPath validates the CT_GraphicalObjectFrameNonVisual and its children, prefixing error messages with path
func (_eag *CT_GraphicalObjectFrameNonVisual )ValidateWithPath (path string )error {if _ege :=_eag .CNvPr .ValidateWithPath (path +"\u002f\u0043\u004e\u0076\u0050\u0072");_ege !=nil {return _ege ;};if _gge :=_eag .CNvGraphicFramePr .ValidateWithPath (path +"\u002fC\u004ev\u0047\u0072\u0061\u0070\u0068i\u0063\u0046r\u0061\u006d\u0065\u0050\u0072");
_gge !=nil {return _gge ;};return nil ;};

// ValidateWithPath validates the CT_GroupShapeChoice and its children, prefixing error messages with path
func (_efa *CT_GroupShapeChoice )ValidateWithPath (path string )error {if _efa .Sp !=nil {if _cbg :=_efa .Sp .ValidateWithPath (path +"\u002f\u0053\u0070");_cbg !=nil {return _cbg ;};};if _efa .GrpSp !=nil {if _ceg :=_efa .GrpSp .ValidateWithPath (path +"\u002f\u0047\u0072\u0070\u0053\u0070");
_ceg !=nil {return _ceg ;};};if _efa .GraphicFrame !=nil {if _dfc :=_efa .GraphicFrame .ValidateWithPath (path +"\u002f\u0047\u0072\u0061\u0070\u0068\u0069\u0063\u0046\u0072\u0061\u006d\u0065");_dfc !=nil {return _dfc ;};};if _efa .CxnSp !=nil {if _cgb :=_efa .CxnSp .ValidateWithPath (path +"\u002f\u0043\u0078\u006e\u0053\u0070");
_cgb !=nil {return _cgb ;};};if _efa .Pic !=nil {if _egbd :=_efa .Pic .ValidateWithPath (path +"\u002f\u0050\u0069\u0063");_egbd !=nil {return _egbd ;};};return nil ;};func (_bacc *CT_GroupShapeNonVisual )MarshalXML (e *_bb .Encoder ,start _bb .StartElement )error {e .EncodeToken (start );
_gac :=_bb .StartElement {Name :_bb .Name {Local :"\u0078d\u0072\u003a\u0063\u004e\u0076\u0050r"}};e .EncodeElement (_bacc .CNvPr ,_gac );_agcb :=_bb .StartElement {Name :_bb .Name {Local :"\u0078\u0064\u0072\u003a\u0063\u004e\u0076\u0047\u0072p\u0053\u0070\u0050\u0072"}};
e .EncodeElement (_bacc .CNvGrpSpPr ,_agcb );e .EncodeToken (_bb .EndElement {Name :start .Name });return nil ;};

// Validate validates the CT_AnchorClientData and its children
func (_fc *CT_AnchorClientData )Validate ()error {return _fc .ValidateWithPath ("\u0043\u0054\u005f\u0041nc\u0068\u006f\u0072\u0043\u006c\u0069\u0065\u006e\u0074\u0044\u0061\u0074\u0061");};

// ValidateWithPath validates the CT_Connector and its children, prefixing error messages with path
func (_af *CT_Connector )ValidateWithPath (path string )error {if _aga :=_af .NvCxnSpPr .ValidateWithPath (path +"\u002f\u004e\u0076\u0043\u0078\u006e\u0053\u0070\u0050\u0072");_aga !=nil {return _aga ;};if _db :=_af .SpPr .ValidateWithPath (path +"\u002f\u0053\u0070P\u0072");
_db !=nil {return _db ;};if _af .Style !=nil {if _faa :=_af .Style .ValidateWithPath (path +"\u002f\u0053\u0074\u0079\u006c\u0065");_faa !=nil {return _faa ;};};return nil ;};

// Validate validates the WsDr and its children
func (_abgf *WsDr )Validate ()error {return _abgf .ValidateWithPath ("\u0057\u0073\u0044\u0072")};func NewCT_Connector ()*CT_Connector {_gf :=&CT_Connector {};_gf .NvCxnSpPr =NewCT_ConnectorNonVisual ();_gf .SpPr =_ba .NewCT_ShapeProperties ();return _gf ;
};

// ValidateWithPath validates the CT_Picture and its children, prefixing error messages with path
func (_egee *CT_Picture )ValidateWithPath (path string )error {if _dfe :=_egee .NvPicPr .ValidateWithPath (path +"\u002f\u004e\u0076\u0050\u0069\u0063\u0050\u0072");_dfe !=nil {return _dfe ;};if _acg :=_egee .BlipFill .ValidateWithPath (path +"\u002fB\u006c\u0069\u0070\u0046\u0069\u006cl");
_acg !=nil {return _acg ;};if _dbad :=_egee .SpPr .ValidateWithPath (path +"\u002f\u0053\u0070P\u0072");_dbad !=nil {return _dbad ;};if _egee .Style !=nil {if _adf :=_egee .Style .ValidateWithPath (path +"\u002f\u0053\u0074\u0079\u006c\u0065");_adf !=nil {return _adf ;
};};return nil ;};func (_fffb ST_EditAs )String ()string {switch _fffb {case 0:return "";case 1:return "\u0074w\u006f\u0043\u0065\u006c\u006c";case 2:return "\u006fn\u0065\u0043\u0065\u006c\u006c";case 3:return "\u0061\u0062\u0073\u006f\u006c\u0075\u0074\u0065";
};return "";};func NewCT_AnchorClientData ()*CT_AnchorClientData {_fgd :=&CT_AnchorClientData {};return _fgd };func NewCT_GroupShapeNonVisual ()*CT_GroupShapeNonVisual {_bcg :=&CT_GroupShapeNonVisual {};_bcg .CNvPr =_ba .NewCT_NonVisualDrawingProps ();
_bcg .CNvGrpSpPr =_ba .NewCT_NonVisualGroupDrawingShapeProps ();return _bcg ;};type ST_EditAs byte ;func (_aea *CT_Marker )UnmarshalXML (d *_bb .Decoder ,start _bb .StartElement )error {_aea .Col =0;_aea .Row =0;_dbc :for {_cfb ,_gcc :=d .Token ();if _gcc !=nil {return _gcc ;
};switch _cbae :=_cfb .(type ){case _bb .StartElement :switch _cbae .Name {case _bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067",Local :"\u0063\u006f\u006c"},_bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fdr\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0073\u0070\u0072\u0065\u0061\u0064\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0063\u006f\u006c"}:if _abdg :=d .DecodeElement (&_aea .Col ,&_cbae );
_abdg !=nil {return _abdg ;};case _bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067",Local :"\u0063\u006f\u006c\u004f\u0066\u0066"},_bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fdr\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0073\u0070\u0072\u0065\u0061\u0064\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0063\u006f\u006c\u004f\u0066\u0066"}:_dbd ,_fccc :=d .Token ();
if _fccc !=nil {return _fccc ;};switch _dbaf :=_dbd .(type ){case _bb .CharData :_dcc :=string (_dbaf );_dab ,_bbeb :=_ba .ParseUnionST_Coordinate (_dcc );if _bbeb !=nil {return nil ;};_aea .ColOff =_dab ;d .Skip ();case _bb .EndElement :};case _bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067",Local :"\u0072\u006f\u0077"},_bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fdr\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0073\u0070\u0072\u0065\u0061\u0064\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0072\u006f\u0077"}:if _aead :=d .DecodeElement (&_aea .Row ,&_cbae );
_aead !=nil {return _aead ;};case _bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067",Local :"\u0072\u006f\u0077\u004f\u0066\u0066"},_bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fdr\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0073\u0070\u0072\u0065\u0061\u0064\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0072\u006f\u0077\u004f\u0066\u0066"}:_gbcc ,_fecb :=d .Token ();
if _fecb !=nil {return _fecb ;};switch _abcb :=_gbcc .(type ){case _bb .CharData :_dfa :=string (_abcb );_dcg ,_fed :=_ba .ParseUnionST_Coordinate (_dfa );if _fed !=nil {return nil ;};_aea .RowOff =_dcg ;d .Skip ();case _bb .EndElement :};default:_c .Log .Debug ("\u0073k\u0069\u0070p\u0069\u006e\u0067\u0020u\u006e\u0073\u0075p\u0070\u006f\u0072\u0074\u0065\u0064\u0020\u0065\u006cem\u0065\u006e\u0074 \u006f\u006e \u0043\u0054\u005f\u004d\u0061\u0072k\u0065\u0072 \u0025\u0076",_cbae .Name );
if _bee :=d .Skip ();_bee !=nil {return _bee ;};};case _bb .EndElement :break _dbc ;case _bb .CharData :};};return nil ;};const (ST_EditAsUnset ST_EditAs =0;ST_EditAsTwoCell ST_EditAs =1;ST_EditAsOneCell ST_EditAs =2;ST_EditAsAbsolute ST_EditAs =3;);type CT_Picture struct{

// Reference To Custom Function
MacroAttr *string ;

// Publish to Server Flag
FPublishedAttr *bool ;

// Non-Visual Properties for a Picture
NvPicPr *CT_PictureNonVisual ;

// Picture Fill
BlipFill *_ba .CT_BlipFillProperties ;SpPr *_ba .CT_ShapeProperties ;

// Shape Style
Style *_ba .CT_ShapeStyle ;};func (_fef *EG_ObjectChoices )MarshalXML (e *_bb .Encoder ,start _bb .StartElement )error {_fef .ObjectChoicesChoice .MarshalXML (e ,_bb .StartElement {});return nil ;};func NewCT_GraphicalObjectFrameNonVisual ()*CT_GraphicalObjectFrameNonVisual {_fgc :=&CT_GraphicalObjectFrameNonVisual {};
_fgc .CNvPr =_ba .NewCT_NonVisualDrawingProps ();_fgc .CNvGraphicFramePr =_ba .NewCT_NonVisualGraphicFrameProperties ();return _fgc ;};

// ValidateWithPath validates the CT_Marker and its children, prefixing error messages with path
func (_fcaf *CT_Marker )ValidateWithPath (path string )error {if _fcaf .Col < 0{return _d .Errorf ("\u0025\u0073\u002fm\u002e\u0043\u006f\u006c \u006d\u0075\u0073\u0074\u0020\u0062\u0065 \u003e\u003d\u0020\u0030\u0020\u0028\u0068\u0061\u0076\u0065\u0020\u0025\u0076\u0029",path ,_fcaf .Col );
};if _ecdf :=_fcaf .ColOff .ValidateWithPath (path +"\u002fC\u006f\u006c\u004f\u0066\u0066");_ecdf !=nil {return _ecdf ;};if _fcaf .Row < 0{return _d .Errorf ("\u0025\u0073\u002fm\u002e\u0052\u006f\u0077 \u006d\u0075\u0073\u0074\u0020\u0062\u0065 \u003e\u003d\u0020\u0030\u0020\u0028\u0068\u0061\u0076\u0065\u0020\u0025\u0076\u0029",path ,_fcaf .Row );
};if _feef :=_fcaf .RowOff .ValidateWithPath (path +"\u002fR\u006f\u0077\u004f\u0066\u0066");_feef !=nil {return _feef ;};return nil ;};func NewEG_AnchorChoice ()*EG_AnchorChoice {_dbgag :=&EG_AnchorChoice {};return _dbgag };func (_dfd *CT_OneCellAnchor )UnmarshalXML (d *_bb .Decoder ,start _bb .StartElement )error {_dfd .From =NewCT_Marker ();
_dfd .Ext =_ba .NewCT_PositiveSize2D ();_dfd .ObjectChoicesChoice =NewEG_ObjectChoicesChoice ();_dfd .ClientData =NewCT_AnchorClientData ();_efgb :for {_dad ,_edfa :=d .Token ();if _edfa !=nil {return _edfa ;};switch _gfdd :=_dad .(type ){case _bb .StartElement :switch _gfdd .Name {case _bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067",Local :"\u0066\u0072\u006f\u006d"},_bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fdr\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0073\u0070\u0072\u0065\u0061\u0064\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0066\u0072\u006f\u006d"}:if _fae :=d .DecodeElement (_dfd .From ,&_gfdd );
_fae !=nil {return _fae ;};case _bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067",Local :"\u0065\u0078\u0074"},_bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fdr\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0073\u0070\u0072\u0065\u0061\u0064\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0065\u0078\u0074"}:if _bda :=d .DecodeElement (_dfd .Ext ,&_gfdd );
_bda !=nil {return _bda ;};case _bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067",Local :"\u0073\u0070"},_bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fdr\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0073\u0070\u0072\u0065\u0061\u0064\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0073\u0070"}:_dfd .ObjectChoicesChoice =NewEG_ObjectChoicesChoice ();
if _gdg :=d .DecodeElement (&_dfd .ObjectChoicesChoice .Sp ,&_gfdd );_gdg !=nil {return _gdg ;};case _bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067",Local :"\u0067\u0072\u0070S\u0070"},_bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fdr\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0073\u0070\u0072\u0065\u0061\u0064\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0067\u0072\u0070S\u0070"}:_dfd .ObjectChoicesChoice =NewEG_ObjectChoicesChoice ();
if _cedg :=d .DecodeElement (&_dfd .ObjectChoicesChoice .GrpSp ,&_gfdd );_cedg !=nil {return _cedg ;};case _bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067",Local :"\u0067\u0072\u0061p\u0068\u0069\u0063\u0046\u0072\u0061\u006d\u0065"},_bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fdr\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0073\u0070\u0072\u0065\u0061\u0064\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0067\u0072\u0061p\u0068\u0069\u0063\u0046\u0072\u0061\u006d\u0065"}:_dfd .ObjectChoicesChoice =NewEG_ObjectChoicesChoice ();
if _gaad :=d .DecodeElement (&_dfd .ObjectChoicesChoice .GraphicFrame ,&_gfdd );_gaad !=nil {return _gaad ;};case _bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067",Local :"\u0063\u0078\u006eS\u0070"},_bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fdr\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0073\u0070\u0072\u0065\u0061\u0064\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0063\u0078\u006eS\u0070"}:_dfd .ObjectChoicesChoice =NewEG_ObjectChoicesChoice ();
if _gdd :=d .DecodeElement (&_dfd .ObjectChoicesChoice .CxnSp ,&_gfdd );_gdd !=nil {return _gdd ;};case _bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067",Local :"\u0070\u0069\u0063"},_bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fdr\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0073\u0070\u0072\u0065\u0061\u0064\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0070\u0069\u0063"}:_dfd .ObjectChoicesChoice =NewEG_ObjectChoicesChoice ();
if _afa :=d .DecodeElement (&_dfd .ObjectChoicesChoice .Pic ,&_gfdd );_afa !=nil {return _afa ;};case _bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067",Local :"c\u006f\u006e\u0074\u0065\u006e\u0074\u0050\u0061\u0072\u0074"},_bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fdr\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0073\u0070\u0072\u0065\u0061\u0064\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"c\u006f\u006e\u0074\u0065\u006e\u0074\u0050\u0061\u0072\u0074"}:_dfd .ObjectChoicesChoice =NewEG_ObjectChoicesChoice ();
if _beb :=d .DecodeElement (&_dfd .ObjectChoicesChoice .ContentPart ,&_gfdd );_beb !=nil {return _beb ;};case _bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067",Local :"\u0063\u006c\u0069\u0065\u006e\u0074\u0044\u0061\u0074\u0061"},_bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fdr\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0073\u0070\u0072\u0065\u0061\u0064\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0063\u006c\u0069\u0065\u006e\u0074\u0044\u0061\u0074\u0061"}:if _dag :=d .DecodeElement (_dfd .ClientData ,&_gfdd );
_dag !=nil {return _dag ;};default:_c .Log .Debug ("\u0073\u006b\u0069\u0070\u0070i\u006e\u0067\u0020\u0075\u006e\u0073\u0075\u0070\u0070\u006f\u0072\u0074\u0065d\u0020\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0043\u0054\u005f\u004f\u006e\u0065\u0043\u0065\u006c\u006c\u0041\u006e\u0063\u0068\u006f\u0072\u0020\u0025v",_gfdd .Name );
if _ggg :=d .Skip ();_ggg !=nil {return _ggg ;};};case _bb .EndElement :break _efgb ;case _bb .CharData :};};return nil ;};func (_feb *CT_ConnectorNonVisual )MarshalXML (e *_bb .Encoder ,start _bb .StartElement )error {e .EncodeToken (start );_eg :=_bb .StartElement {Name :_bb .Name {Local :"\u0078d\u0072\u003a\u0063\u004e\u0076\u0050r"}};
e .EncodeElement (_feb .CNvPr ,_eg );_de :=_bb .StartElement {Name :_bb .Name {Local :"\u0078\u0064\u0072\u003a\u0063\u004e\u0076\u0043\u0078n\u0053\u0070\u0050\u0072"}};e .EncodeElement (_feb .CNvCxnSpPr ,_de );e .EncodeToken (_bb .EndElement {Name :start .Name });
return nil ;};

// ValidateWithPath validates the EG_ObjectChoicesChoice and its children, prefixing error messages with path
func (_cfg *EG_ObjectChoicesChoice )ValidateWithPath (path string )error {if _cfg .Sp !=nil {if _cfbc :=_cfg .Sp .ValidateWithPath (path +"\u002f\u0053\u0070");_cfbc !=nil {return _cfbc ;};};if _cfg .GrpSp !=nil {if _badd :=_cfg .GrpSp .ValidateWithPath (path +"\u002f\u0047\u0072\u0070\u0053\u0070");
_badd !=nil {return _badd ;};};if _cfg .GraphicFrame !=nil {if _dcgg :=_cfg .GraphicFrame .ValidateWithPath (path +"\u002f\u0047\u0072\u0061\u0070\u0068\u0069\u0063\u0046\u0072\u0061\u006d\u0065");_dcgg !=nil {return _dcgg ;};};if _cfg .CxnSp !=nil {if _cfdf :=_cfg .CxnSp .ValidateWithPath (path +"\u002f\u0043\u0078\u006e\u0053\u0070");
_cfdf !=nil {return _cfdf ;};};if _cfg .Pic !=nil {if _ceaf :=_cfg .Pic .ValidateWithPath (path +"\u002f\u0050\u0069\u0063");_ceaf !=nil {return _ceaf ;};};if _cfg .ContentPart !=nil {if _bfbb :=_cfg .ContentPart .ValidateWithPath (path +"\u002f\u0043\u006fn\u0074\u0065\u006e\u0074\u0050\u0061\u0072\u0074");
_bfbb !=nil {return _bfbb ;};};return nil ;};func (_accg *From )MarshalXML (e *_bb .Encoder ,start _bb .StartElement )error {start .Attr =append (start .Attr ,_bb .Attr {Name :_bb .Name {Local :"\u0078\u006d\u006cn\u0073"},Value :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067"});
start .Attr =append (start .Attr ,_bb .Attr {Name :_bb .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0061"},Value :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065m\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006cf\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077\u0069\u006e\u0067m\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u006d\u0061\u0069\u006e"});
start .Attr =append (start .Attr ,_bb .Attr {Name :_bb .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0072"},Value :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069c\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002fr\u0065\u006c\u0061\u0074\u0069\u006f\u006e\u0073h\u0069\u0070\u0073"});
start .Attr =append (start .Attr ,_bb .Attr {Name :_bb .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0078\u0064r"},Value :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067"});
start .Attr =append (start .Attr ,_bb .Attr {Name :_bb .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0078\u006dl"},Value :"\u0068\u0074tp\u003a\u002f\u002fw\u0077\u0077\u002e\u00773.o\u0072g/\u0058\u004d\u004c\u002f\u0031\u0039\u00398/\u006e\u0061\u006d\u0065\u0073\u0070\u0061c\u0065"});
start .Name .Local ="\u0078\u0064\u0072\u003a\u0066\u0072\u006f\u006d";return _accg .CT_Marker .MarshalXML (e ,start );};func (_baca *CT_GroupShapeChoice )MarshalXML (e *_bb .Encoder ,start _bb .StartElement )error {if _baca .Sp !=nil {_bga :=_bb .StartElement {Name :_bb .Name {Local :"\u0078\u0064\u0072\u003a\u0073\u0070"}};
e .EncodeElement (_baca .Sp ,_bga );}else if _baca .GrpSp !=nil {_bef :=_bb .StartElement {Name :_bb .Name {Local :"\u0078d\u0072\u003a\u0067\u0072\u0070\u0053p"}};e .EncodeElement (_baca .GrpSp ,_bef );}else if _baca .GraphicFrame !=nil {_dge :=_bb .StartElement {Name :_bb .Name {Local :"\u0078\u0064r\u003a\u0067\u0072a\u0070\u0068\u0069\u0063\u0046\u0072\u0061\u006d\u0065"}};
e .EncodeElement (_baca .GraphicFrame ,_dge );}else if _baca .CxnSp !=nil {_bdf :=_bb .StartElement {Name :_bb .Name {Local :"\u0078d\u0072\u003a\u0063\u0078\u006e\u0053p"}};e .EncodeElement (_baca .CxnSp ,_bdf );}else if _baca .Pic !=nil {_dfb :=_bb .StartElement {Name :_bb .Name {Local :"\u0078d\u0072\u003a\u0070\u0069\u0063"}};
e .EncodeElement (_baca .Pic ,_dfb );};return nil ;};func NewTo ()*To {_egec :=&To {};_egec .CT_Marker =*NewCT_Marker ();return _egec };type To struct{CT_Marker };func (_fca *CT_GraphicalObjectFrame )UnmarshalXML (d *_bb .Decoder ,start _bb .StartElement )error {_fca .NvGraphicFramePr =NewCT_GraphicalObjectFrameNonVisual ();
_fca .Xfrm =_ba .NewCT_Transform2D ();_fca .Graphic =_ba .NewGraphic ();for _ ,_acab :=range start .Attr {if _acab .Name .Local =="\u006d\u0061\u0063r\u006f"{_cdad :=_acab .Value ;_fca .MacroAttr =&_cdad ;continue ;};if _acab .Name .Local =="\u0066\u0050\u0075\u0062\u006c\u0069\u0073\u0068\u0065\u0064"{_bgga ,_bbe :=_bg .ParseBool (_acab .Value );
if _bbe !=nil {return _bbe ;};_fca .FPublishedAttr =&_bgga ;continue ;};};_cbb :for {_gfg ,_edgb :=d .Token ();if _edgb !=nil {return _edgb ;};switch _fccb :=_gfg .(type ){case _bb .StartElement :switch _fccb .Name {case _bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067",Local :"\u006e\u0076G\u0072\u0061\u0070h\u0069\u0063\u0046\u0072\u0061\u006d\u0065\u0050\u0072"},_bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fdr\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0073\u0070\u0072\u0065\u0061\u0064\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u006e\u0076G\u0072\u0061\u0070h\u0069\u0063\u0046\u0072\u0061\u006d\u0065\u0050\u0072"}:if _geeb :=d .DecodeElement (_fca .NvGraphicFramePr ,&_fccb );
_geeb !=nil {return _geeb ;};case _bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067",Local :"\u0078\u0066\u0072\u006d"},_bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fdr\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0073\u0070\u0072\u0065\u0061\u0064\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0078\u0066\u0072\u006d"}:if _aag :=d .DecodeElement (_fca .Xfrm ,&_fccb );
_aag !=nil {return _aag ;};case _bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065m\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006cf\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077\u0069\u006e\u0067m\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u006d\u0061\u0069\u006e",Local :"\u0067r\u0061\u0070\u0068\u0069\u0063"},_bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a/\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072g\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u0064\u0072\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u006d\u0061\u0069\u006e",Local :"\u0067r\u0061\u0070\u0068\u0069\u0063"}:if _bc :=d .DecodeElement (_fca .Graphic ,&_fccb );
_bc !=nil {return _bc ;};default:_c .Log .Debug ("\u0073\u006b\u0069\u0070\u0070\u0069\u006e\u0067\u0020\u0075\u006e\u0073\u0075\u0070\u0070\u006f\u0072\u0074\u0065\u0064 \u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006fn\u0020\u0043\u0054\u005f\u0047\u0072\u0061\u0070\u0068\u0069\u0063\u0061\u006cO\u0062\u006a\u0065\u0063\u0074\u0046r\u0061\u006d\u0065 \u0025\u0076",_fccb .Name );
if _bfg :=d .Skip ();_bfg !=nil {return _bfg ;};};case _bb .EndElement :break _cbb ;case _bb .CharData :};};return nil ;};

// ValidateWithPath validates the CT_Drawing and its children, prefixing error messages with path
func (_efc *CT_Drawing )ValidateWithPath (path string )error {for _gbc ,_baf :=range _efc .EG_Anchor {if _ccf :=_baf .ValidateWithPath (_d .Sprintf ("\u0025\u0073/\u0045\u0047\u005fA\u006e\u0063\u0068\u006f\u0072\u005b\u0025\u0064\u005d",path ,_gbc ));
_ccf !=nil {return _ccf ;};};return nil ;};

// Validate validates the CT_GroupShapeChoice and its children
func (_ad *CT_GroupShapeChoice )Validate ()error {return _ad .ValidateWithPath ("\u0043\u0054\u005f\u0047ro\u0075\u0070\u0053\u0068\u0061\u0070\u0065\u0043\u0068\u006f\u0069\u0063\u0065");};func (_beade *To )MarshalXML (e *_bb .Encoder ,start _bb .StartElement )error {start .Attr =append (start .Attr ,_bb .Attr {Name :_bb .Name {Local :"\u0078\u006d\u006cn\u0073"},Value :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067"});
start .Attr =append (start .Attr ,_bb .Attr {Name :_bb .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0061"},Value :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065m\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006cf\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077\u0069\u006e\u0067m\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u006d\u0061\u0069\u006e"});
start .Attr =append (start .Attr ,_bb .Attr {Name :_bb .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0072"},Value :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069c\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002fr\u0065\u006c\u0061\u0074\u0069\u006f\u006e\u0073h\u0069\u0070\u0073"});
start .Attr =append (start .Attr ,_bb .Attr {Name :_bb .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0078\u0064r"},Value :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067"});
start .Attr =append (start .Attr ,_bb .Attr {Name :_bb .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0078\u006dl"},Value :"\u0068\u0074tp\u003a\u002f\u002fw\u0077\u0077\u002e\u00773.o\u0072g/\u0058\u004d\u004c\u002f\u0031\u0039\u00398/\u006e\u0061\u006d\u0065\u0073\u0070\u0061c\u0065"});
start .Name .Local ="\u0078\u0064\u0072\u003a\u0074\u006f";return _beade .CT_Marker .MarshalXML (e ,start );};func _dgec (_gfdf bool )uint8 {if _gfdf {return 1;};return 0;};func NewWsDr ()*WsDr {_cdeg :=&WsDr {};_cdeg .CT_Drawing =*NewCT_Drawing ();return _cdeg };


// Validate validates the To and its children
func (_gdba *To )Validate ()error {return _gdba .ValidateWithPath ("\u0054\u006f")};

// Validate validates the EG_ObjectChoicesChoice and its children
func (_ffda *EG_ObjectChoicesChoice )Validate ()error {return _ffda .ValidateWithPath ("\u0045\u0047\u005f\u004fbj\u0065\u0063\u0074\u0043\u0068\u006f\u0069\u0063\u0065\u0073\u0043\u0068\u006f\u0069c\u0065");};func (_ff *CT_GraphicalObjectFrame )MarshalXML (e *_bb .Encoder ,start _bb .StartElement )error {if _ff .MacroAttr !=nil {start .Attr =append (start .Attr ,_bb .Attr {Name :_bb .Name {Local :"\u006d\u0061\u0063r\u006f"},Value :_d .Sprintf ("\u0025\u0076",*_ff .MacroAttr )});
};if _ff .FPublishedAttr !=nil {start .Attr =append (start .Attr ,_bb .Attr {Name :_bb .Name {Local :"\u0066\u0050\u0075\u0062\u006c\u0069\u0073\u0068\u0065\u0064"},Value :_d .Sprintf ("\u0025\u0064",_dgec (*_ff .FPublishedAttr ))});};e .EncodeToken (start );
_cdc :=_bb .StartElement {Name :_bb .Name {Local :"x\u0064r\u003a\u006e\u0076\u0047\u0072\u0061\u0070\u0068i\u0063\u0046\u0072\u0061me\u0050\u0072"}};e .EncodeElement (_ff .NvGraphicFramePr ,_cdc );_ebd :=_bb .StartElement {Name :_bb .Name {Local :"\u0078\u0064\u0072\u003a\u0078\u0066\u0072\u006d"}};
e .EncodeElement (_ff .Xfrm ,_ebd );_baed :=_bb .StartElement {Name :_bb .Name {Local :"\u0061:\u0067\u0072\u0061\u0070\u0068\u0069c"}};_baed .Attr =append (_baed .Attr ,_bb .Attr {Name :_bb .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0061"},Value :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065m\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006cf\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077\u0069\u006e\u0067m\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u006d\u0061\u0069\u006e"});
e .EncodeElement (_ff .Graphic ,_baed );e .EncodeToken (_bb .EndElement {Name :start .Name });return nil ;};

// ValidateWithPath validates the EG_AnchorChoice and its children, prefixing error messages with path
func (_adca *EG_AnchorChoice )ValidateWithPath (path string )error {if _adca .TwoCellAnchor !=nil {if _efd :=_adca .TwoCellAnchor .ValidateWithPath (path +"\u002f\u0054\u0077\u006f\u0043\u0065\u006c\u006c\u0041n\u0063\u0068\u006f\u0072");_efd !=nil {return _efd ;
};};if _adca .OneCellAnchor !=nil {if _bage :=_adca .OneCellAnchor .ValidateWithPath (path +"\u002f\u004f\u006e\u0065\u0043\u0065\u006c\u006c\u0041n\u0063\u0068\u006f\u0072");_bage !=nil {return _bage ;};};if _adca .AbsoluteAnchor !=nil {if _dff :=_adca .AbsoluteAnchor .ValidateWithPath (path +"\u002fA\u0062s\u006f\u006c\u0075\u0074\u0065\u0041\u006e\u0063\u0068\u006f\u0072");
_dff !=nil {return _dff ;};};return nil ;};

// Validate validates the CT_OneCellAnchor and its children
func (_dgc *CT_OneCellAnchor )Validate ()error {return _dgc .ValidateWithPath ("\u0043\u0054_\u004f\u006e\u0065C\u0065\u006c\u006c\u0041\u006e\u0063\u0068\u006f\u0072");};func (_edgbe *WsDr )UnmarshalXML (d *_bb .Decoder ,start _bb .StartElement )error {_edgbe .CT_Drawing =*NewCT_Drawing ();
_ddcg :for {_dbfd ,_daed :=d .Token ();if _daed !=nil {return _daed ;};switch _fda :=_dbfd .(type ){case _bb .StartElement :switch _fda .Name {case _bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067",Local :"\u0074\u0077\u006f\u0043\u0065\u006c\u006c\u0041\u006e\u0063\u0068\u006f\u0072"},_bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fdr\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0073\u0070\u0072\u0065\u0061\u0064\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0074\u0077\u006f\u0043\u0065\u006c\u006c\u0041\u006e\u0063\u0068\u006f\u0072"}:_ecca :=NewEG_Anchor ();
_ecca .AnchorChoice =NewEG_AnchorChoice ();_edgbe .EG_Anchor =append (_edgbe .EG_Anchor ,_ecca );if _dffg :=d .DecodeElement (&_ecca .AnchorChoice .TwoCellAnchor ,&_fda );_dffg !=nil {return _dffg ;};case _bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067",Local :"\u006f\u006e\u0065\u0043\u0065\u006c\u006c\u0041\u006e\u0063\u0068\u006f\u0072"},_bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fdr\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0073\u0070\u0072\u0065\u0061\u0064\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u006f\u006e\u0065\u0043\u0065\u006c\u006c\u0041\u006e\u0063\u0068\u006f\u0072"}:_cfdfe :=NewEG_Anchor ();
_cfdfe .AnchorChoice =NewEG_AnchorChoice ();_edgbe .EG_Anchor =append (_edgbe .EG_Anchor ,_cfdfe );if _gddf :=d .DecodeElement (&_cfdfe .AnchorChoice .OneCellAnchor ,&_fda );_gddf !=nil {return _gddf ;};case _bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067",Local :"\u0061\u0062\u0073\u006f\u006c\u0075\u0074\u0065\u0041n\u0063\u0068\u006f\u0072"},_bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fdr\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0073\u0070\u0072\u0065\u0061\u0064\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0061\u0062\u0073\u006f\u006c\u0075\u0074\u0065\u0041n\u0063\u0068\u006f\u0072"}:_baa :=NewEG_Anchor ();
_baa .AnchorChoice =NewEG_AnchorChoice ();_edgbe .EG_Anchor =append (_edgbe .EG_Anchor ,_baa );if _aed :=d .DecodeElement (&_baa .AnchorChoice .AbsoluteAnchor ,&_fda );_aed !=nil {return _aed ;};default:_c .Log .Debug ("\u0073\u006b\u0069\u0070\u0070\u0069\u006e\u0067\u0020\u0075\u006e\u0073\u0075p\u0070\u006f\u0072\u0074\u0065\u0064 \u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0057\u0073D\u0072\u0020\u0025\u0076",_fda .Name );
if _bgbf :=d .Skip ();_bgbf !=nil {return _bgbf ;};};case _bb .EndElement :break _ddcg ;case _bb .CharData :};};return nil ;};func (_eb *CT_AnchorClientData )MarshalXML (e *_bb .Encoder ,start _bb .StartElement )error {if _eb .FLocksWithSheetAttr !=nil {start .Attr =append (start .Attr ,_bb .Attr {Name :_bb .Name {Local :"\u0066L\u006fc\u006b\u0073\u0057\u0069\u0074\u0068\u0053\u0068\u0065\u0065\u0074"},Value :_d .Sprintf ("\u0025\u0064",_dgec (*_eb .FLocksWithSheetAttr ))});
};if _eb .FPrintsWithSheetAttr !=nil {start .Attr =append (start .Attr ,_bb .Attr {Name :_bb .Name {Local :"\u0066\u0050r\u0069\u006e\u0074s\u0057\u0069\u0074\u0068\u0053\u0068\u0065\u0065\u0074"},Value :_d .Sprintf ("\u0025\u0064",_dgec (*_eb .FPrintsWithSheetAttr ))});
};e .EncodeToken (start );e .EncodeToken (_bb .EndElement {Name :start .Name });return nil ;};type EG_ObjectChoices struct{ObjectChoicesChoice *EG_ObjectChoicesChoice ;};func (_cfc *CT_Rel )UnmarshalXML (d *_bb .Decoder ,start _bb .StartElement )error {for _ ,_ggec :=range start .Attr {if _ggec .Name .Space =="\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069c\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002fr\u0065\u006c\u0061\u0074\u0069\u006f\u006e\u0073h\u0069\u0070\u0073"&&_ggec .Name .Local =="\u0069\u0064"||_ggec .Name .Space =="\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fof\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0072\u0065\u006c\u0061\u0074\u0069\u006f\u006e\u0073\u0068\u0069\u0070\u0073"&&_ggec .Name .Local =="\u0069\u0064"{_gfa :=_ggec .Value ;
_cfc .IdAttr =_gfa ;continue ;};};for {_egd ,_bgad :=d .Token ();if _bgad !=nil {return _d .Errorf ("\u0070a\u0072s\u0069\u006e\u0067\u0020\u0043T\u005f\u0052e\u006c\u003a\u0020\u0025\u0073",_bgad );};if _aee ,_cfcb :=_egd .(_bb .EndElement );_cfcb &&_aee .Name ==start .Name {break ;
};};return nil ;};

// Validate validates the CT_ConnectorNonVisual and its children
func (_ea *CT_ConnectorNonVisual )Validate ()error {return _ea .ValidateWithPath ("C\u0054\u005f\u0043\u006fnn\u0065c\u0074\u006f\u0072\u004e\u006fn\u0056\u0069\u0073\u0075\u0061\u006c");};type CT_GraphicalObjectFrameNonVisual struct{

// Connection Non-Visual Properties
CNvPr *_ba .CT_NonVisualDrawingProps ;

// Non-Visual Graphic Frame Drawing Properties
CNvGraphicFramePr *_ba .CT_NonVisualGraphicFrameProperties ;};type CT_AnchorClientData struct{

// Locks With Sheet Flag
FLocksWithSheetAttr *bool ;

// Prints With Sheet Flag
FPrintsWithSheetAttr *bool ;};func (_bbc *EG_ObjectChoices )UnmarshalXML (d *_bb .Decoder ,start _bb .StartElement )error {_bbc .ObjectChoicesChoice =NewEG_ObjectChoicesChoice ();_adcb :for {_baeda ,_bddeb :=d .Token ();if _bddeb !=nil {return _bddeb ;
};switch _acge :=_baeda .(type ){case _bb .StartElement :switch _acge .Name {case _bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067",Local :"\u0073\u0070"},_bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fdr\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0073\u0070\u0072\u0065\u0061\u0064\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0073\u0070"}:_bbc .ObjectChoicesChoice =NewEG_ObjectChoicesChoice ();
if _dbb :=d .DecodeElement (&_bbc .ObjectChoicesChoice .Sp ,&_acge );_dbb !=nil {return _dbb ;};case _bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067",Local :"\u0067\u0072\u0070S\u0070"},_bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fdr\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0073\u0070\u0072\u0065\u0061\u0064\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0067\u0072\u0070S\u0070"}:_bbc .ObjectChoicesChoice =NewEG_ObjectChoicesChoice ();
if _fbfa :=d .DecodeElement (&_bbc .ObjectChoicesChoice .GrpSp ,&_acge );_fbfa !=nil {return _fbfa ;};case _bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067",Local :"\u0067\u0072\u0061p\u0068\u0069\u0063\u0046\u0072\u0061\u006d\u0065"},_bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fdr\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0073\u0070\u0072\u0065\u0061\u0064\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0067\u0072\u0061p\u0068\u0069\u0063\u0046\u0072\u0061\u006d\u0065"}:_bbc .ObjectChoicesChoice =NewEG_ObjectChoicesChoice ();
if _dga :=d .DecodeElement (&_bbc .ObjectChoicesChoice .GraphicFrame ,&_acge );_dga !=nil {return _dga ;};case _bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067",Local :"\u0063\u0078\u006eS\u0070"},_bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fdr\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0073\u0070\u0072\u0065\u0061\u0064\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0063\u0078\u006eS\u0070"}:_bbc .ObjectChoicesChoice =NewEG_ObjectChoicesChoice ();
if _gce :=d .DecodeElement (&_bbc .ObjectChoicesChoice .CxnSp ,&_acge );_gce !=nil {return _gce ;};case _bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067",Local :"\u0070\u0069\u0063"},_bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fdr\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0073\u0070\u0072\u0065\u0061\u0064\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0070\u0069\u0063"}:_bbc .ObjectChoicesChoice =NewEG_ObjectChoicesChoice ();
if _edgd :=d .DecodeElement (&_bbc .ObjectChoicesChoice .Pic ,&_acge );_edgd !=nil {return _edgd ;};case _bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067",Local :"c\u006f\u006e\u0074\u0065\u006e\u0074\u0050\u0061\u0072\u0074"},_bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fdr\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0073\u0070\u0072\u0065\u0061\u0064\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"c\u006f\u006e\u0074\u0065\u006e\u0074\u0050\u0061\u0072\u0074"}:_bbc .ObjectChoicesChoice =NewEG_ObjectChoicesChoice ();
if _dcb :=d .DecodeElement (&_bbc .ObjectChoicesChoice .ContentPart ,&_acge );_dcb !=nil {return _dcb ;};default:_c .Log .Debug ("\u0073\u006b\u0069\u0070\u0070i\u006e\u0067\u0020\u0075\u006e\u0073\u0075\u0070\u0070\u006f\u0072\u0074\u0065d\u0020\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0045\u0047\u005f\u004f\u0062\u006a\u0065\u0063\u0074\u0043\u0068\u006f\u0069\u0063\u0065\u0073\u0020\u0025v",_acge .Name );
if _fbe :=d .Skip ();_fbe !=nil {return _fbe ;};};case _bb .EndElement :break _adcb ;case _bb .CharData :};};return nil ;};func (_befa *CT_OneCellAnchor )MarshalXML (e *_bb .Encoder ,start _bb .StartElement )error {e .EncodeToken (start );_ace :=_bb .StartElement {Name :_bb .Name {Local :"\u0078\u0064\u0072\u003a\u0066\u0072\u006f\u006d"}};
e .EncodeElement (_befa .From ,_ace );_aaba :=_bb .StartElement {Name :_bb .Name {Local :"\u0078d\u0072\u003a\u0065\u0078\u0074"}};e .EncodeElement (_befa .Ext ,_aaba );_befa .ObjectChoicesChoice .MarshalXML (e ,_bb .StartElement {});_fdg :=_bb .StartElement {Name :_bb .Name {Local :"\u0078\u0064\u0072\u003a\u0063\u006c\u0069\u0065\u006et\u0044\u0061\u0074\u0061"}};
e .EncodeElement (_befa .ClientData ,_fdg );e .EncodeToken (_bb .EndElement {Name :start .Name });return nil ;};

// Validate validates the CT_Picture and its children
func (_beg *CT_Picture )Validate ()error {return _beg .ValidateWithPath ("\u0043\u0054\u005f\u0050\u0069\u0063\u0074\u0075\u0072\u0065");};

// ValidateWithPath validates the CT_TwoCellAnchor and its children, prefixing error messages with path
func (_ddfg *CT_TwoCellAnchor )ValidateWithPath (path string )error {if _fbd :=_ddfg .EditAsAttr .ValidateWithPath (path +"/\u0045\u0064\u0069\u0074\u0041\u0073\u0041\u0074\u0074\u0072");_fbd !=nil {return _fbd ;};if _cbd :=_ddfg .From .ValidateWithPath (path +"\u002f\u0046\u0072o\u006d");
_cbd !=nil {return _cbd ;};if _eage :=_ddfg .To .ValidateWithPath (path +"\u002f\u0054\u006f");_eage !=nil {return _eage ;};if _bec :=_ddfg .ObjectChoicesChoice .ValidateWithPath (path +"/\u004fb\u006a\u0065\u0063\u0074\u0043\u0068\u006f\u0069c\u0065\u0073\u0043\u0068oi\u0063\u0065");
_bec !=nil {return _bec ;};if _aeb :=_ddfg .ClientData .ValidateWithPath (path +"/\u0043\u006c\u0069\u0065\u006e\u0074\u0044\u0061\u0074\u0061");_aeb !=nil {return _aeb ;};return nil ;};type CT_GroupShapeNonVisual struct{

// Connection Non-Visual Properties
CNvPr *_ba .CT_NonVisualDrawingProps ;

// Non-Visual Group Shape Drawing Properties
CNvGrpSpPr *_ba .CT_NonVisualGroupDrawingShapeProps ;};func (_cc *CT_AbsoluteAnchor )UnmarshalXML (d *_bb .Decoder ,start _bb .StartElement )error {_cc .Pos =_ba .NewCT_Point2D ();_cc .Ext =_ba .NewCT_PositiveSize2D ();_cc .ObjectChoicesChoice =NewEG_ObjectChoicesChoice ();
_cc .ClientData =NewCT_AnchorClientData ();_cf :for {_e ,_ab :=d .Token ();if _ab !=nil {return _ab ;};switch _fg :=_e .(type ){case _bb .StartElement :switch _fg .Name {case _bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067",Local :"\u0070\u006f\u0073"},_bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fdr\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0073\u0070\u0072\u0065\u0061\u0064\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0070\u006f\u0073"}:if _dd :=d .DecodeElement (_cc .Pos ,&_fg );
_dd !=nil {return _dd ;};case _bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067",Local :"\u0065\u0078\u0074"},_bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fdr\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0073\u0070\u0072\u0065\u0061\u0064\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0065\u0078\u0074"}:if _gb :=d .DecodeElement (_cc .Ext ,&_fg );
_gb !=nil {return _gb ;};case _bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067",Local :"\u0073\u0070"},_bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fdr\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0073\u0070\u0072\u0065\u0061\u0064\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0073\u0070"}:_cc .ObjectChoicesChoice =NewEG_ObjectChoicesChoice ();
if _ag :=d .DecodeElement (&_cc .ObjectChoicesChoice .Sp ,&_fg );_ag !=nil {return _ag ;};case _bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067",Local :"\u0067\u0072\u0070S\u0070"},_bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fdr\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0073\u0070\u0072\u0065\u0061\u0064\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0067\u0072\u0070S\u0070"}:_cc .ObjectChoicesChoice =NewEG_ObjectChoicesChoice ();
if _bdd :=d .DecodeElement (&_cc .ObjectChoicesChoice .GrpSp ,&_fg );_bdd !=nil {return _bdd ;};case _bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067",Local :"\u0067\u0072\u0061p\u0068\u0069\u0063\u0046\u0072\u0061\u006d\u0065"},_bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fdr\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0073\u0070\u0072\u0065\u0061\u0064\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0067\u0072\u0061p\u0068\u0069\u0063\u0046\u0072\u0061\u006d\u0065"}:_cc .ObjectChoicesChoice =NewEG_ObjectChoicesChoice ();
if _aa :=d .DecodeElement (&_cc .ObjectChoicesChoice .GraphicFrame ,&_fg );_aa !=nil {return _aa ;};case _bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067",Local :"\u0063\u0078\u006eS\u0070"},_bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fdr\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0073\u0070\u0072\u0065\u0061\u0064\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0063\u0078\u006eS\u0070"}:_cc .ObjectChoicesChoice =NewEG_ObjectChoicesChoice ();
if _bge :=d .DecodeElement (&_cc .ObjectChoicesChoice .CxnSp ,&_fg );_bge !=nil {return _bge ;};case _bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067",Local :"\u0070\u0069\u0063"},_bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fdr\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0073\u0070\u0072\u0065\u0061\u0064\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0070\u0069\u0063"}:_cc .ObjectChoicesChoice =NewEG_ObjectChoicesChoice ();
if _ce :=d .DecodeElement (&_cc .ObjectChoicesChoice .Pic ,&_fg );_ce !=nil {return _ce ;};case _bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067",Local :"c\u006f\u006e\u0074\u0065\u006e\u0074\u0050\u0061\u0072\u0074"},_bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fdr\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0073\u0070\u0072\u0065\u0061\u0064\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"c\u006f\u006e\u0074\u0065\u006e\u0074\u0050\u0061\u0072\u0074"}:_cc .ObjectChoicesChoice =NewEG_ObjectChoicesChoice ();
if _cd :=d .DecodeElement (&_cc .ObjectChoicesChoice .ContentPart ,&_fg );_cd !=nil {return _cd ;};case _bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067",Local :"\u0063\u006c\u0069\u0065\u006e\u0074\u0044\u0061\u0074\u0061"},_bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fdr\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0073\u0070\u0072\u0065\u0061\u0064\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0063\u006c\u0069\u0065\u006e\u0074\u0044\u0061\u0074\u0061"}:if _da :=d .DecodeElement (_cc .ClientData ,&_fg );
_da !=nil {return _da ;};default:_c .Log .Debug ("\u0073\u006bi\u0070\u0070\u0069\u006e\u0067 \u0075\u006e\u0073\u0075\u0070p\u006f\u0072\u0074\u0065\u0064\u0020\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0043\u0054\u005f\u0041\u0062\u0073\u006f\u006c\u0075\u0074\u0065\u0041\u006e\u0063\u0068\u006f\u0072\u0020\u0025\u0076",_fg .Name );
if _fe :=d .Skip ();_fe !=nil {return _fe ;};};case _bb .EndElement :break _cf ;case _bb .CharData :};};return nil ;};func NewCT_GroupShape ()*CT_GroupShape {_fdc :=&CT_GroupShape {};_fdc .NvGrpSpPr =NewCT_GroupShapeNonVisual ();_fdc .GrpSpPr =_ba .NewCT_GroupShapeProperties ();
return _fdc ;};type CT_Shape struct{

// Reference to Custom Function
MacroAttr *string ;

// Text Link
TextlinkAttr *string ;

// Lock Text Flag
FLocksTextAttr *bool ;

// Publish to Server Flag
FPublishedAttr *bool ;

// Non-Visual Properties for a Shape
NvSpPr *CT_ShapeNonVisual ;

// Shape Properties
SpPr *_ba .CT_ShapeProperties ;Style *_ba .CT_ShapeStyle ;

// Shape Text Body
TxBody *_ba .CT_TextBody ;};func (_edfc *CT_GraphicalObjectFrameNonVisual )MarshalXML (e *_bb .Encoder ,start _bb .StartElement )error {e .EncodeToken (start );_eae :=_bb .StartElement {Name :_bb .Name {Local :"\u0078d\u0072\u003a\u0063\u004e\u0076\u0050r"}};
e .EncodeElement (_edfc .CNvPr ,_eae );_caa :=_bb .StartElement {Name :_bb .Name {Local :"x\u0064\u0072\u003a\u0063Nv\u0047r\u0061\u0070\u0068\u0069\u0063F\u0072\u0061\u006d\u0065\u0050\u0072"}};e .EncodeElement (_edfc .CNvGraphicFramePr ,_caa );e .EncodeToken (_bb .EndElement {Name :start .Name });
return nil ;};func NewCT_Rel ()*CT_Rel {_eefe :=&CT_Rel {};return _eefe };

// ValidateWithPath validates the WsDr and its children, prefixing error messages with path
func (_bbfc *WsDr )ValidateWithPath (path string )error {if _defef :=_bbfc .CT_Drawing .ValidateWithPath (path );_defef !=nil {return _defef ;};return nil ;};

// ValidateWithPath validates the CT_Rel and its children, prefixing error messages with path
func (_ede *CT_Rel )ValidateWithPath (path string )error {return nil };func (_ec *CT_ConnectorNonVisual )UnmarshalXML (d *_bb .Decoder ,start _bb .StartElement )error {_ec .CNvPr =_ba .NewCT_NonVisualDrawingProps ();_ec .CNvCxnSpPr =_ba .NewCT_NonVisualConnectorProperties ();
_fcb :for {_acc ,_dae :=d .Token ();if _dae !=nil {return _dae ;};switch _gcg :=_acc .(type ){case _bb .StartElement :switch _gcg .Name {case _bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067",Local :"\u0063\u004e\u0076P\u0072"},_bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fdr\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0073\u0070\u0072\u0065\u0061\u0064\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0063\u004e\u0076P\u0072"}:if _ddf :=d .DecodeElement (_ec .CNvPr ,&_gcg );
_ddf !=nil {return _ddf ;};case _bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067",Local :"\u0063\u004e\u0076\u0043\u0078\u006e\u0053\u0070\u0050\u0072"},_bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fdr\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0073\u0070\u0072\u0065\u0061\u0064\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0063\u004e\u0076\u0043\u0078\u006e\u0053\u0070\u0050\u0072"}:if _fcc :=d .DecodeElement (_ec .CNvCxnSpPr ,&_gcg );
_fcc !=nil {return _fcc ;};default:_c .Log .Debug ("\u0073\u006b\u0069\u0070\u0070\u0069n\u0067\u0020\u0075n\u0073\u0075\u0070p\u006f\u0072\u0074\u0065\u0064\u0020\u0065\u006c\u0065\u006de\u006e\u0074\u0020\u006f\u006e C\u0054\u005f\u0043\u006f\u006e\u006e\u0065\u0063\u0074\u006f\u0072\u004e\u006f\u006e\u0056\u0069\u0073\u0075\u0061\u006c\u0020\u0025\u0076",_gcg .Name );
if _be :=d .Skip ();_be !=nil {return _be ;};};case _bb .EndElement :break _fcb ;case _bb .CharData :};};return nil ;};

// Validate validates the From and its children
func (_cbaa *From )Validate ()error {return _cbaa .ValidateWithPath ("\u0046\u0072\u006f\u006d")};

// Validate validates the CT_Drawing and its children
func (_dg *CT_Drawing )Validate ()error {return _dg .ValidateWithPath ("\u0043\u0054\u005f\u0044\u0072\u0061\u0077\u0069\u006e\u0067");};

// ValidateWithPath validates the CT_PictureNonVisual and its children, prefixing error messages with path
func (_eccg *CT_PictureNonVisual )ValidateWithPath (path string )error {if _ged :=_eccg .CNvPr .ValidateWithPath (path +"\u002f\u0043\u004e\u0076\u0050\u0072");_ged !=nil {return _ged ;};if _cee :=_eccg .CNvPicPr .ValidateWithPath (path +"\u002fC\u004e\u0076\u0050\u0069\u0063\u0050r");
_cee !=nil {return _cee ;};return nil ;};type CT_Drawing struct{EG_Anchor []*EG_Anchor ;};func (_bfb *CT_ShapeNonVisual )MarshalXML (e *_bb .Encoder ,start _bb .StartElement )error {e .EncodeToken (start );_bagf :=_bb .StartElement {Name :_bb .Name {Local :"\u0078d\u0072\u003a\u0063\u004e\u0076\u0050r"}};
e .EncodeElement (_bfb .CNvPr ,_bagf );_eaa :=_bb .StartElement {Name :_bb .Name {Local :"x\u0064\u0072\u003a\u0063\u004e\u0076\u0053\u0070\u0050\u0072"}};e .EncodeElement (_bfb .CNvSpPr ,_eaa );e .EncodeToken (_bb .EndElement {Name :start .Name });return nil ;
};func (_ecdb *ST_EditAs )UnmarshalXMLAttr (attr _bb .Attr )error {switch attr .Value {case "":*_ecdb =0;case "\u0074w\u006f\u0043\u0065\u006c\u006c":*_ecdb =1;case "\u006fn\u0065\u0043\u0065\u006c\u006c":*_ecdb =2;case "\u0061\u0062\u0073\u006f\u006c\u0075\u0074\u0065":*_ecdb =3;
};return nil ;};

// Validate validates the EG_Anchor and its children
func (_cdg *EG_Anchor )Validate ()error {return _cdg .ValidateWithPath ("\u0045G\u005f\u0041\u006e\u0063\u0068\u006fr");};type CT_ShapeNonVisual struct{

// Non-Visual Drawing Properties
CNvPr *_ba .CT_NonVisualDrawingProps ;

// Connection Non-Visual Shape Properties
CNvSpPr *_ba .CT_NonVisualDrawingShapeProps ;};

// Validate validates the CT_Marker and its children
func (_efg *CT_Marker )Validate ()error {return _efg .ValidateWithPath ("\u0043T\u005f\u004d\u0061\u0072\u006b\u0065r");};func (_fgae *EG_AnchorChoice )UnmarshalXML (d *_bb .Decoder ,start _bb .StartElement )error {_fdfg :=start ;switch start .Name {case _bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067",Local :"\u0074\u0077\u006f\u0043\u0065\u006c\u006c\u0041\u006e\u0063\u0068\u006f\u0072"},_bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fdr\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0073\u0070\u0072\u0065\u0061\u0064\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0074\u0077\u006f\u0043\u0065\u006c\u006c\u0041\u006e\u0063\u0068\u006f\u0072"}:_fgae .TwoCellAnchor =NewCT_TwoCellAnchor ();
if _faf :=d .DecodeElement (_fgae .TwoCellAnchor ,&_fdfg );_faf !=nil {return _faf ;};case _bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067",Local :"\u006f\u006e\u0065\u0043\u0065\u006c\u006c\u0041\u006e\u0063\u0068\u006f\u0072"},_bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fdr\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0073\u0070\u0072\u0065\u0061\u0064\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u006f\u006e\u0065\u0043\u0065\u006c\u006c\u0041\u006e\u0063\u0068\u006f\u0072"}:_fgae .OneCellAnchor =NewCT_OneCellAnchor ();
if _bebg :=d .DecodeElement (_fgae .OneCellAnchor ,&_fdfg );_bebg !=nil {return _bebg ;};case _bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067",Local :"\u0061\u0062\u0073\u006f\u006c\u0075\u0074\u0065\u0041n\u0063\u0068\u006f\u0072"},_bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fdr\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0073\u0070\u0072\u0065\u0061\u0064\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0061\u0062\u0073\u006f\u006c\u0075\u0074\u0065\u0041n\u0063\u0068\u006f\u0072"}:_fgae .AbsoluteAnchor =NewCT_AbsoluteAnchor ();
if _agf :=d .DecodeElement (_fgae .AbsoluteAnchor ,&_fdfg );_agf !=nil {return _agf ;};default:_c .Log .Debug ("\u0073\u006b\u0069\u0070\u0070\u0069\u006e\u0067\u0020\u0075\u006e\u0073\u0075\u0070\u0070\u006f\u0072\u0074e\u0064\u0020\u0065\u006c\u0065\u006d\u0065n\u0074\u0020\u006f\u006e\u0020\u0045\u0047\u005f\u0041\u006e\u0063h\u006f\u0072\u0043\u0068\u006f\u0069\u0063\u0065\u0020\u0025\u0076",_fdfg .Name );
if _begc :=d .Skip ();_begc !=nil {return _begc ;};};return nil ;};func (_edf *CT_Connector )MarshalXML (e *_bb .Encoder ,start _bb .StartElement )error {if _edf .MacroAttr !=nil {start .Attr =append (start .Attr ,_bb .Attr {Name :_bb .Name {Local :"\u006d\u0061\u0063r\u006f"},Value :_d .Sprintf ("\u0025\u0076",*_edf .MacroAttr )});
};if _edf .FPublishedAttr !=nil {start .Attr =append (start .Attr ,_bb .Attr {Name :_bb .Name {Local :"\u0066\u0050\u0075\u0062\u006c\u0069\u0073\u0068\u0065\u0064"},Value :_d .Sprintf ("\u0025\u0064",_dgec (*_edf .FPublishedAttr ))});};e .EncodeToken (start );
_ca :=_bb .StartElement {Name :_bb .Name {Local :"\u0078\u0064\u0072\u003a\u006e\u0076\u0043\u0078\u006e\u0053\u0070\u0050\u0072"}};e .EncodeElement (_edf .NvCxnSpPr ,_ca );_gef :=_bb .StartElement {Name :_bb .Name {Local :"\u0078\u0064\u0072\u003a\u0073\u0070\u0050\u0072"}};
e .EncodeElement (_edf .SpPr ,_gef );if _edf .Style !=nil {_agd :=_bb .StartElement {Name :_bb .Name {Local :"\u0078d\u0072\u003a\u0073\u0074\u0079\u006ce"}};e .EncodeElement (_edf .Style ,_agd );};e .EncodeToken (_bb .EndElement {Name :start .Name });
return nil ;};func NewCT_PictureNonVisual ()*CT_PictureNonVisual {_gfeec :=&CT_PictureNonVisual {};_gfeec .CNvPr =_ba .NewCT_NonVisualDrawingProps ();_gfeec .CNvPicPr =_ba .NewCT_NonVisualPictureProperties ();return _gfeec ;};

// ValidateWithPath validates the CT_AnchorClientData and its children, prefixing error messages with path
func (_ae *CT_AnchorClientData )ValidateWithPath (path string )error {return nil };

// ValidateWithPath validates the EG_Anchor and its children, prefixing error messages with path
func (_fgaa *EG_Anchor )ValidateWithPath (path string )error {if _cga :=_fgaa .AnchorChoice .ValidateWithPath (path +"\u002f\u0041\u006e\u0063\u0068\u006f\u0072\u0043\u0068\u006f\u0069\u0063\u0065");_cga !=nil {return _cga ;};return nil ;};type EG_AnchorChoice struct{

// Two Cell Anchor Shape Size
TwoCellAnchor *CT_TwoCellAnchor ;

// One Cell Anchor Shape Size
OneCellAnchor *CT_OneCellAnchor ;

// Absolute Anchor Shape Size
AbsoluteAnchor *CT_AbsoluteAnchor ;};type CT_Rel struct{IdAttr string ;};

// ValidateWithPath validates the CT_GroupShapeNonVisual and its children, prefixing error messages with path
func (_agae *CT_GroupShapeNonVisual )ValidateWithPath (path string )error {if _bgd :=_agae .CNvPr .ValidateWithPath (path +"\u002f\u0043\u004e\u0076\u0050\u0072");_bgd !=nil {return _bgd ;};if _cae :=_agae .CNvGrpSpPr .ValidateWithPath (path +"/\u0043\u004e\u0076\u0047\u0072\u0070\u0053\u0070\u0050\u0072");
_cae !=nil {return _cae ;};return nil ;};func NewCT_Marker ()*CT_Marker {_ggeb :=&CT_Marker {};_ggeb .Col =0;_ggeb .Row =0;return _ggeb };

// Validate validates the EG_AnchorChoice and its children
func (_dfg *EG_AnchorChoice )Validate ()error {return _dfg .ValidateWithPath ("\u0045G\u005fA\u006e\u0063\u0068\u006f\u0072\u0043\u0068\u006f\u0069\u0063\u0065");};func (_gaaff *EG_Anchor )UnmarshalXML (d *_bb .Decoder ,start _bb .StartElement )error {_gaaff .AnchorChoice =NewEG_AnchorChoice ();
_ggge :for {_dfdd ,_gbcf :=d .Token ();if _gbcf !=nil {return _gbcf ;};switch _bead :=_dfdd .(type ){case _bb .StartElement :switch _bead .Name {case _bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067",Local :"\u0074\u0077\u006f\u0043\u0065\u006c\u006c\u0041\u006e\u0063\u0068\u006f\u0072"},_bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fdr\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0073\u0070\u0072\u0065\u0061\u0064\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0074\u0077\u006f\u0043\u0065\u006c\u006c\u0041\u006e\u0063\u0068\u006f\u0072"}:_gaaff .AnchorChoice =NewEG_AnchorChoice ();
if _fac :=d .DecodeElement (&_gaaff .AnchorChoice .TwoCellAnchor ,&_bead );_fac !=nil {return _fac ;};case _bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067",Local :"\u006f\u006e\u0065\u0043\u0065\u006c\u006c\u0041\u006e\u0063\u0068\u006f\u0072"},_bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fdr\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0073\u0070\u0072\u0065\u0061\u0064\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u006f\u006e\u0065\u0043\u0065\u006c\u006c\u0041\u006e\u0063\u0068\u006f\u0072"}:_gaaff .AnchorChoice =NewEG_AnchorChoice ();
if _ffde :=d .DecodeElement (&_gaaff .AnchorChoice .OneCellAnchor ,&_bead );_ffde !=nil {return _ffde ;};case _bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067",Local :"\u0061\u0062\u0073\u006f\u006c\u0075\u0074\u0065\u0041n\u0063\u0068\u006f\u0072"},_bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fdr\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0073\u0070\u0072\u0065\u0061\u0064\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0061\u0062\u0073\u006f\u006c\u0075\u0074\u0065\u0041n\u0063\u0068\u006f\u0072"}:_gaaff .AnchorChoice =NewEG_AnchorChoice ();
if _gga :=d .DecodeElement (&_gaaff .AnchorChoice .AbsoluteAnchor ,&_bead );_gga !=nil {return _gga ;};default:_c .Log .Debug ("\u0073k\u0069\u0070p\u0069\u006e\u0067\u0020u\u006e\u0073\u0075p\u0070\u006f\u0072\u0074\u0065\u0064\u0020\u0065\u006cem\u0065\u006e\u0074 \u006f\u006e \u0045\u0047\u005f\u0041\u006e\u0063h\u006f\u0072 \u0025\u0076",_bead .Name );
if _dddb :=d .Skip ();_dddb !=nil {return _dddb ;};};case _bb .EndElement :break _ggge ;case _bb .CharData :};};return nil ;};

// Validate validates the CT_GroupShapeNonVisual and its children
func (_cdfg *CT_GroupShapeNonVisual )Validate ()error {return _cdfg .ValidateWithPath ("\u0043\u0054\u005f\u0047ro\u0075\u0070\u0053\u0068\u0061\u0070\u0065\u004e\u006f\u006e\u0056\u0069\u0073\u0075a\u006c");};func NewCT_ConnectorNonVisual ()*CT_ConnectorNonVisual {_bbf :=&CT_ConnectorNonVisual {};
_bbf .CNvPr =_ba .NewCT_NonVisualDrawingProps ();_bbf .CNvCxnSpPr =_ba .NewCT_NonVisualConnectorProperties ();return _bbf ;};type EG_Anchor struct{AnchorChoice *EG_AnchorChoice ;};func (_eac ST_EditAs )MarshalXML (e *_bb .Encoder ,start _bb .StartElement )error {return e .EncodeElement (_eac .String (),start );
};

// Validate validates the CT_Rel and its children
func (_abg *CT_Rel )Validate ()error {return _abg .ValidateWithPath ("\u0043\u0054\u005f\u0052\u0065\u006c");};type CT_GraphicalObjectFrame struct{

// Reference To Custom Function
MacroAttr *string ;

// Publish to Server Flag
FPublishedAttr *bool ;

// Non-Visual Properties for a Graphic Frame
NvGraphicFramePr *CT_GraphicalObjectFrameNonVisual ;

// 2D Transform for Graphic Frames
Xfrm *_ba .CT_Transform2D ;Graphic *_ba .Graphic ;};func (_gggg ST_EditAs )MarshalXMLAttr (name _bb .Name )(_bb .Attr ,error ){_baaf :=_bb .Attr {};_baaf .Name =name ;switch _gggg {case ST_EditAsUnset :_baaf .Value ="";case ST_EditAsTwoCell :_baaf .Value ="\u0074w\u006f\u0043\u0065\u006c\u006c";
case ST_EditAsOneCell :_baaf .Value ="\u006fn\u0065\u0043\u0065\u006c\u006c";case ST_EditAsAbsolute :_baaf .Value ="\u0061\u0062\u0073\u006f\u006c\u0075\u0074\u0065";};return _baaf ,nil ;};func NewCT_AbsoluteAnchor ()*CT_AbsoluteAnchor {_f :=&CT_AbsoluteAnchor {};
_f .Pos =_ba .NewCT_Point2D ();_f .Ext =_ba .NewCT_PositiveSize2D ();_f .ObjectChoicesChoice =NewEG_ObjectChoicesChoice ();_f .ClientData =NewCT_AnchorClientData ();return _f ;};

// Validate validates the CT_PictureNonVisual and its children
func (_gaf *CT_PictureNonVisual )Validate ()error {return _gaf .ValidateWithPath ("\u0043\u0054\u005f\u0050ic\u0074\u0075\u0072\u0065\u004e\u006f\u006e\u0056\u0069\u0073\u0075\u0061\u006c");};func (_bbaa *WsDr )MarshalXML (e *_bb .Encoder ,start _bb .StartElement )error {start .Attr =append (start .Attr ,_bb .Attr {Name :_bb .Name {Local :"\u0078\u006d\u006cn\u0073"},Value :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067"});
start .Attr =append (start .Attr ,_bb .Attr {Name :_bb .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0061"},Value :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065m\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006cf\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077\u0069\u006e\u0067m\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u006d\u0061\u0069\u006e"});
start .Attr =append (start .Attr ,_bb .Attr {Name :_bb .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0072"},Value :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069c\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002fr\u0065\u006c\u0061\u0074\u0069\u006f\u006e\u0073h\u0069\u0070\u0073"});
start .Attr =append (start .Attr ,_bb .Attr {Name :_bb .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0078\u0064r"},Value :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067"});
start .Attr =append (start .Attr ,_bb .Attr {Name :_bb .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0078\u006dl"},Value :"\u0068\u0074tp\u003a\u002f\u002fw\u0077\u0077\u002e\u00773.o\u0072g/\u0058\u004d\u004c\u002f\u0031\u0039\u00398/\u006e\u0061\u006d\u0065\u0073\u0070\u0061c\u0065"});
start .Name .Local ="\u0078\u0064\u0072\u003a\u0077\u0073\u0044\u0072";return _bbaa .CT_Drawing .MarshalXML (e ,start );};func (_gefe *CT_TwoCellAnchor )MarshalXML (e *_bb .Encoder ,start _bb .StartElement )error {if _gefe .EditAsAttr !=ST_EditAsUnset {_gfeb ,_dagf :=_gefe .EditAsAttr .MarshalXMLAttr (_bb .Name {Local :"\u0065\u0064\u0069\u0074\u0041\u0073"});
if _dagf !=nil {return _dagf ;};start .Attr =append (start .Attr ,_gfeb );};e .EncodeToken (start );_ebg :=_bb .StartElement {Name :_bb .Name {Local :"\u0078\u0064\u0072\u003a\u0066\u0072\u006f\u006d"}};e .EncodeElement (_gefe .From ,_ebg );_fgga :=_bb .StartElement {Name :_bb .Name {Local :"\u0078\u0064\u0072\u003a\u0074\u006f"}};
e .EncodeElement (_gefe .To ,_fgga );_gefe .ObjectChoicesChoice .MarshalXML (e ,_bb .StartElement {});_gdgd :=_bb .StartElement {Name :_bb .Name {Local :"\u0078\u0064\u0072\u003a\u0063\u006c\u0069\u0065\u006et\u0044\u0061\u0074\u0061"}};e .EncodeElement (_gefe .ClientData ,_gdgd );
e .EncodeToken (_bb .EndElement {Name :start .Name });return nil ;};

// ValidateWithPath validates the To and its children, prefixing error messages with path
func (_efaf *To )ValidateWithPath (path string )error {if _daca :=_efaf .CT_Marker .ValidateWithPath (path );_daca !=nil {return _daca ;};return nil ;};func NewCT_TwoCellAnchor ()*CT_TwoCellAnchor {_bff :=&CT_TwoCellAnchor {};_bff .From =NewCT_Marker ();
_bff .To =NewCT_Marker ();_bff .ObjectChoicesChoice =NewEG_ObjectChoicesChoice ();_bff .ClientData =NewCT_AnchorClientData ();return _bff ;};func NewCT_Picture ()*CT_Picture {_dbfe :=&CT_Picture {};_dbfe .NvPicPr =NewCT_PictureNonVisual ();_dbfe .BlipFill =_ba .NewCT_BlipFillProperties ();
_dbfe .SpPr =_ba .NewCT_ShapeProperties ();return _dbfe ;};func NewFrom ()*From {_cafg :=&From {};_cafg .CT_Marker =*NewCT_Marker ();return _cafg };

// Validate validates the CT_ShapeNonVisual and its children
func (_cecd *CT_ShapeNonVisual )Validate ()error {return _cecd .ValidateWithPath ("\u0043\u0054\u005f\u0053\u0068\u0061\u0070\u0065\u004e\u006f\u006e\u0056i\u0073\u0075\u0061\u006c");};func (_bgae *CT_ShapeNonVisual )UnmarshalXML (d *_bb .Decoder ,start _bb .StartElement )error {_bgae .CNvPr =_ba .NewCT_NonVisualDrawingProps ();
_bgae .CNvSpPr =_ba .NewCT_NonVisualDrawingShapeProps ();_beee :for {_gedf ,_cgf :=d .Token ();if _cgf !=nil {return _cgf ;};switch _gcfc :=_gedf .(type ){case _bb .StartElement :switch _gcfc .Name {case _bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067",Local :"\u0063\u004e\u0076P\u0072"},_bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fdr\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0073\u0070\u0072\u0065\u0061\u0064\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0063\u004e\u0076P\u0072"}:if _cfd :=d .DecodeElement (_bgae .CNvPr ,&_gcfc );
_cfd !=nil {return _cfd ;};case _bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067",Local :"\u0063N\u0076\u0053\u0070\u0050\u0072"},_bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fdr\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0073\u0070\u0072\u0065\u0061\u0064\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0063N\u0076\u0053\u0070\u0050\u0072"}:if _cedd :=d .DecodeElement (_bgae .CNvSpPr ,&_gcfc );
_cedd !=nil {return _cedd ;};default:_c .Log .Debug ("\u0073\u006bi\u0070\u0070\u0069\u006e\u0067 \u0075\u006e\u0073\u0075\u0070p\u006f\u0072\u0074\u0065\u0064\u0020\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0043\u0054\u005f\u0053\u0068\u0061\u0070\u0065\u004e\u006f\u006e\u0056\u0069\u0073\u0075\u0061\u006c\u0020\u0025\u0076",_gcfc .Name );
if _gddg :=d .Skip ();_gddg !=nil {return _gddg ;};};case _bb .EndElement :break _beee ;case _bb .CharData :};};return nil ;};type EG_ObjectChoicesChoice struct{Sp *CT_Shape ;GrpSp *CT_GroupShape ;GraphicFrame *CT_GraphicalObjectFrame ;CxnSp *CT_Connector ;
Pic *CT_Picture ;ContentPart *CT_Rel ;};func (_gbd *CT_Rel )MarshalXML (e *_bb .Encoder ,start _bb .StartElement )error {start .Attr =append (start .Attr ,_bb .Attr {Name :_bb .Name {Local :"\u0072\u003a\u0069\u0064"},Value :_d .Sprintf ("\u0025\u0076",_gbd .IdAttr )});
e .EncodeToken (start );e .EncodeToken (_bb .EndElement {Name :start .Name });return nil ;};func (_fdd *To )UnmarshalXML (d *_bb .Decoder ,start _bb .StartElement )error {_fdd .CT_Marker =*NewCT_Marker ();_bcca :for {_bbaf ,_gae :=d .Token ();if _gae !=nil {return _gae ;
};switch _egecb :=_bbaf .(type ){case _bb .StartElement :switch _egecb .Name {case _bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067",Local :"\u0063\u006f\u006c"},_bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fdr\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0073\u0070\u0072\u0065\u0061\u0064\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0063\u006f\u006c"}:if _gec :=d .DecodeElement (&_fdd .Col ,&_egecb );
_gec !=nil {return _gec ;};case _bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067",Local :"\u0063\u006f\u006c\u004f\u0066\u0066"},_bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fdr\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0073\u0070\u0072\u0065\u0061\u0064\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0063\u006f\u006c\u004f\u0066\u0066"}:_dee ,_fbeb :=d .Token ();
if _fbeb !=nil {return _fbeb ;};switch _cef :=_dee .(type ){case _bb .CharData :_ceag :=string (_cef );_agfd ,_gced :=_ba .ParseUnionST_Coordinate (_ceag );if _gced !=nil {return nil ;};_fdd .ColOff =_agfd ;d .Skip ();case _bb .EndElement :};case _bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067",Local :"\u0072\u006f\u0077"},_bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fdr\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0073\u0070\u0072\u0065\u0061\u0064\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0072\u006f\u0077"}:if _fddb :=d .DecodeElement (&_fdd .Row ,&_egecb );
_fddb !=nil {return _fddb ;};case _bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067",Local :"\u0072\u006f\u0077\u004f\u0066\u0066"},_bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fdr\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0073\u0070\u0072\u0065\u0061\u0064\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0072\u006f\u0077\u004f\u0066\u0066"}:_fbed ,_fea :=d .Token ();
if _fea !=nil {return _fea ;};switch _dfafc :=_fbed .(type ){case _bb .CharData :_cdb :=string (_dfafc );_bfcb ,_ccad :=_ba .ParseUnionST_Coordinate (_cdb );if _ccad !=nil {return nil ;};_fdd .RowOff =_bfcb ;d .Skip ();case _bb .EndElement :};default:_c .Log .Debug ("\u0073\u006bi\u0070\u0070\u0069\u006eg\u0020\u0075n\u0073\u0075\u0070\u0070\u006f\u0072\u0074\u0065d\u0020\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020T\u006f\u0020\u0025\u0076",_egecb .Name );
if _cefb :=d .Skip ();_cefb !=nil {return _cefb ;};};case _bb .EndElement :break _bcca ;case _bb .CharData :};};return nil ;};

// ValidateWithPath validates the CT_AbsoluteAnchor and its children, prefixing error messages with path
func (_ge *CT_AbsoluteAnchor )ValidateWithPath (path string )error {if _fee :=_ge .Pos .ValidateWithPath (path +"\u002f\u0050\u006f\u0073");_fee !=nil {return _fee ;};if _ga :=_ge .Ext .ValidateWithPath (path +"\u002f\u0045\u0078\u0074");_ga !=nil {return _ga ;
};if _bae :=_ge .ObjectChoicesChoice .ValidateWithPath (path +"/\u004fb\u006a\u0065\u0063\u0074\u0043\u0068\u006f\u0069c\u0065\u0073\u0043\u0068oi\u0063\u0065");_bae !=nil {return _bae ;};if _bdg :=_ge .ClientData .ValidateWithPath (path +"/\u0043\u006c\u0069\u0065\u006e\u0074\u0044\u0061\u0074\u0061");
_bdg !=nil {return _bdg ;};return nil ;};func (_eaf *CT_Shape )UnmarshalXML (d *_bb .Decoder ,start _bb .StartElement )error {_eaf .NvSpPr =NewCT_ShapeNonVisual ();_eaf .SpPr =_ba .NewCT_ShapeProperties ();for _ ,_adc :=range start .Attr {if _adc .Name .Local =="\u006d\u0061\u0063r\u006f"{_bcc :=_adc .Value ;
_eaf .MacroAttr =&_bcc ;continue ;};if _adc .Name .Local =="\u0074\u0065\u0078\u0074\u006c\u0069\u006e\u006b"{_ebf :=_adc .Value ;_eaf .TextlinkAttr =&_ebf ;continue ;};if _adc .Name .Local =="\u0066\u004c\u006f\u0063\u006b\u0073\u0054\u0065\u0078\u0074"{_adfd ,_gbdg :=_bg .ParseBool (_adc .Value );
if _gbdg !=nil {return _gbdg ;};_eaf .FLocksTextAttr =&_adfd ;continue ;};if _adc .Name .Local =="\u0066\u0050\u0075\u0062\u006c\u0069\u0073\u0068\u0065\u0064"{_degg ,_eafg :=_bg .ParseBool (_adc .Value );if _eafg !=nil {return _eafg ;};_eaf .FPublishedAttr =&_degg ;
continue ;};};_ece :for {_afe ,_ebb :=d .Token ();if _ebb !=nil {return _ebb ;};switch _aefag :=_afe .(type ){case _bb .StartElement :switch _aefag .Name {case _bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067",Local :"\u006e\u0076\u0053\u0070\u0050\u0072"},_bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fdr\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0073\u0070\u0072\u0065\u0061\u0064\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u006e\u0076\u0053\u0070\u0050\u0072"}:if _dbg :=d .DecodeElement (_eaf .NvSpPr ,&_aefag );
_dbg !=nil {return _dbg ;};case _bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067",Local :"\u0073\u0070\u0050\u0072"},_bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fdr\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0073\u0070\u0072\u0065\u0061\u0064\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0073\u0070\u0050\u0072"}:if _ccae :=d .DecodeElement (_eaf .SpPr ,&_aefag );
_ccae !=nil {return _ccae ;};case _bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067",Local :"\u0073\u0074\u0079l\u0065"},_bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fdr\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0073\u0070\u0072\u0065\u0061\u0064\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0073\u0074\u0079l\u0065"}:_eaf .Style =_ba .NewCT_ShapeStyle ();
if _fgcbg :=d .DecodeElement (_eaf .Style ,&_aefag );_fgcbg !=nil {return _fgcbg ;};case _bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067",Local :"\u0074\u0078\u0042\u006f\u0064\u0079"},_bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fdr\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0073\u0070\u0072\u0065\u0061\u0064\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0074\u0078\u0042\u006f\u0064\u0079"}:_eaf .TxBody =_ba .NewCT_TextBody ();
if _ddd :=d .DecodeElement (_eaf .TxBody ,&_aefag );_ddd !=nil {return _ddd ;};default:_c .Log .Debug ("\u0073\u006b\u0069\u0070\u0070\u0069\u006eg\u0020\u0075\u006es\u0075\u0070\u0070\u006fr\u0074\u0065\u0064\u0020\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0043\u0054\u005f\u0053\u0068\u0061\u0070\u0065\u0020\u0025\u0076",_aefag .Name );
if _gbba :=d .Skip ();_gbba !=nil {return _gbba ;};};case _bb .EndElement :break _ece ;case _bb .CharData :};};return nil ;};func (_ebcg *CT_TwoCellAnchor )UnmarshalXML (d *_bb .Decoder ,start _bb .StartElement )error {_ebcg .From =NewCT_Marker ();_ebcg .To =NewCT_Marker ();
_ebcg .ObjectChoicesChoice =NewEG_ObjectChoicesChoice ();_ebcg .ClientData =NewCT_AnchorClientData ();for _ ,_efba :=range start .Attr {if _efba .Name .Local =="\u0065\u0064\u0069\u0074\u0041\u0073"{_ebcg .EditAsAttr .UnmarshalXMLAttr (_efba );continue ;
};};_cbf :for {_eccgg ,_befg :=d .Token ();if _befg !=nil {return _befg ;};switch _cbfg :=_eccgg .(type ){case _bb .StartElement :switch _cbfg .Name {case _bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067",Local :"\u0066\u0072\u006f\u006d"},_bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fdr\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0073\u0070\u0072\u0065\u0061\u0064\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0066\u0072\u006f\u006d"}:if _acbf :=d .DecodeElement (_ebcg .From ,&_cbfg );
_acbf !=nil {return _acbf ;};case _bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067",Local :"\u0074\u006f"},_bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fdr\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0073\u0070\u0072\u0065\u0061\u0064\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0074\u006f"}:if _ddc :=d .DecodeElement (_ebcg .To ,&_cbfg );
_ddc !=nil {return _ddc ;};case _bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067",Local :"\u0073\u0070"},_bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fdr\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0073\u0070\u0072\u0065\u0061\u0064\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0073\u0070"}:_ebcg .ObjectChoicesChoice =NewEG_ObjectChoicesChoice ();
if _bdc :=d .DecodeElement (&_ebcg .ObjectChoicesChoice .Sp ,&_cbfg );_bdc !=nil {return _bdc ;};case _bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067",Local :"\u0067\u0072\u0070S\u0070"},_bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fdr\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0073\u0070\u0072\u0065\u0061\u0064\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0067\u0072\u0070S\u0070"}:_ebcg .ObjectChoicesChoice =NewEG_ObjectChoicesChoice ();
if _acf :=d .DecodeElement (&_ebcg .ObjectChoicesChoice .GrpSp ,&_cbfg );_acf !=nil {return _acf ;};case _bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067",Local :"\u0067\u0072\u0061p\u0068\u0069\u0063\u0046\u0072\u0061\u006d\u0065"},_bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fdr\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0073\u0070\u0072\u0065\u0061\u0064\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0067\u0072\u0061p\u0068\u0069\u0063\u0046\u0072\u0061\u006d\u0065"}:_ebcg .ObjectChoicesChoice =NewEG_ObjectChoicesChoice ();
if _dbdd :=d .DecodeElement (&_ebcg .ObjectChoicesChoice .GraphicFrame ,&_cbfg );_dbdd !=nil {return _dbdd ;};case _bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067",Local :"\u0063\u0078\u006eS\u0070"},_bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fdr\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0073\u0070\u0072\u0065\u0061\u0064\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0063\u0078\u006eS\u0070"}:_ebcg .ObjectChoicesChoice =NewEG_ObjectChoicesChoice ();
if _gaaf :=d .DecodeElement (&_ebcg .ObjectChoicesChoice .CxnSp ,&_cbfg );_gaaf !=nil {return _gaaf ;};case _bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067",Local :"\u0070\u0069\u0063"},_bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fdr\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0073\u0070\u0072\u0065\u0061\u0064\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0070\u0069\u0063"}:_ebcg .ObjectChoicesChoice =NewEG_ObjectChoicesChoice ();
if _dbga :=d .DecodeElement (&_ebcg .ObjectChoicesChoice .Pic ,&_cbfg );_dbga !=nil {return _dbga ;};case _bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067",Local :"c\u006f\u006e\u0074\u0065\u006e\u0074\u0050\u0061\u0072\u0074"},_bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fdr\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0073\u0070\u0072\u0065\u0061\u0064\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"c\u006f\u006e\u0074\u0065\u006e\u0074\u0050\u0061\u0072\u0074"}:_ebcg .ObjectChoicesChoice =NewEG_ObjectChoicesChoice ();
if _baec :=d .DecodeElement (&_ebcg .ObjectChoicesChoice .ContentPart ,&_cbfg );_baec !=nil {return _baec ;};case _bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067",Local :"\u0063\u006c\u0069\u0065\u006e\u0074\u0044\u0061\u0074\u0061"},_bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fdr\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0073\u0070\u0072\u0065\u0061\u0064\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0063\u006c\u0069\u0065\u006e\u0074\u0044\u0061\u0074\u0061"}:if _eea :=d .DecodeElement (_ebcg .ClientData ,&_cbfg );
_eea !=nil {return _eea ;};default:_c .Log .Debug ("\u0073\u006b\u0069\u0070\u0070i\u006e\u0067\u0020\u0075\u006e\u0073\u0075\u0070\u0070\u006f\u0072\u0074\u0065d\u0020\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0043\u0054\u005f\u0054\u0077\u006f\u0043\u0065\u006c\u006c\u0041\u006e\u0063\u0068\u006f\u0072\u0020\u0025v",_cbfg .Name );
if _gedg :=d .Skip ();_gedg !=nil {return _gedg ;};};case _bb .EndElement :break _cbf ;case _bb .CharData :};};return nil ;};func (_cded *EG_Anchor )MarshalXML (e *_bb .Encoder ,start _bb .StartElement )error {_cded .AnchorChoice .MarshalXML (e ,_bb .StartElement {});
return nil ;};func (_aeg *ST_EditAs )UnmarshalXML (d *_bb .Decoder ,start _bb .StartElement )error {_ade ,_ega :=d .Token ();if _ega !=nil {return _ega ;};if _cbbd ,_adcg :=_ade .(_bb .EndElement );_adcg &&_cbbd .Name ==start .Name {*_aeg =1;return nil ;
};if _eec ,_dbab :=_ade .(_bb .CharData );!_dbab {return _d .Errorf ("\u0065\u0078\u0070\u0065\u0063\u0074\u0065\u0064\u0020\u0063\u0068a\u0072\u0020\u0064\u0061\u0074\u0061\u002c\u0020\u0067\u006ft\u0020\u0025\u0054",_ade );}else {switch string (_eec ){case "":*_aeg =0;
case "\u0074w\u006f\u0043\u0065\u006c\u006c":*_aeg =1;case "\u006fn\u0065\u0043\u0065\u006c\u006c":*_aeg =2;case "\u0061\u0062\u0073\u006f\u006c\u0075\u0074\u0065":*_aeg =3;};};_ade ,_ega =d .Token ();if _ega !=nil {return _ega ;};if _fecd ,_add :=_ade .(_bb .EndElement );
_add &&_fecd .Name ==start .Name {return nil ;};return _d .Errorf ("\u0065\u0078\u0070\u0065c\u0074\u0065\u0064\u0020\u0065\u006e\u0064\u0020\u0065\u006ce\u006de\u006e\u0074\u002c\u0020\u0067\u006f\u0074 \u0025\u0076",_ade );};

// Validate validates the CT_GroupShape and its children
func (_gcda *CT_GroupShape )Validate ()error {return _gcda .ValidateWithPath ("\u0043\u0054\u005f\u0047\u0072\u006f\u0075\u0070\u0053\u0068\u0061\u0070\u0065");};type CT_Marker struct{

// Column)
Col int32 ;

// Column Offset
ColOff _ba .ST_Coordinate ;

// Row
Row int32 ;

// Row Offset
RowOff _ba .ST_Coordinate ;};func NewEG_ObjectChoicesChoice ()*EG_ObjectChoicesChoice {_eab :=&EG_ObjectChoicesChoice {};return _eab ;};

// ValidateWithPath validates the CT_GraphicalObjectFrame and its children, prefixing error messages with path
func (_gd *CT_GraphicalObjectFrame )ValidateWithPath (path string )error {if _ecf :=_gd .NvGraphicFramePr .ValidateWithPath (path +"\u002f\u004e\u0076\u0047\u0072\u0061\u0070\u0068\u0069\u0063\u0046\u0072a\u006d\u0065\u0050\u0072");_ecf !=nil {return _ecf ;
};if _agc :=_gd .Xfrm .ValidateWithPath (path +"\u002f\u0058\u0066r\u006d");_agc !=nil {return _agc ;};if _bgb :=_gd .Graphic .ValidateWithPath (path +"\u002f\u0047\u0072\u0061\u0070\u0068\u0069\u0063");_bgb !=nil {return _bgb ;};return nil ;};func (_feca *CT_Picture )MarshalXML (e *_bb .Encoder ,start _bb .StartElement )error {if _feca .MacroAttr !=nil {start .Attr =append (start .Attr ,_bb .Attr {Name :_bb .Name {Local :"\u006d\u0061\u0063r\u006f"},Value :_d .Sprintf ("\u0025\u0076",*_feca .MacroAttr )});
};if _feca .FPublishedAttr !=nil {start .Attr =append (start .Attr ,_bb .Attr {Name :_bb .Name {Local :"\u0066\u0050\u0075\u0062\u006c\u0069\u0073\u0068\u0065\u0064"},Value :_d .Sprintf ("\u0025\u0064",_dgec (*_feca .FPublishedAttr ))});};e .EncodeToken (start );
_def :=_bb .StartElement {Name :_bb .Name {Local :"x\u0064\u0072\u003a\u006e\u0076\u0050\u0069\u0063\u0050\u0072"}};e .EncodeElement (_feca .NvPicPr ,_def );_ffd :=_bb .StartElement {Name :_bb .Name {Local :"\u0078\u0064\u0072:\u0062\u006c\u0069\u0070\u0046\u0069\u006c\u006c"}};
e .EncodeElement (_feca .BlipFill ,_ffd );_ecce :=_bb .StartElement {Name :_bb .Name {Local :"\u0078\u0064\u0072\u003a\u0073\u0070\u0050\u0072"}};e .EncodeElement (_feca .SpPr ,_ecce );if _feca .Style !=nil {_aeag :=_bb .StartElement {Name :_bb .Name {Local :"\u0078d\u0072\u003a\u0073\u0074\u0079\u006ce"}};
e .EncodeElement (_feca .Style ,_aeag );};e .EncodeToken (_bb .EndElement {Name :start .Name });return nil ;};func (_dfac *EG_AnchorChoice )MarshalXML (e *_bb .Encoder ,start _bb .StartElement )error {e .EncodeToken (start );if _dfac .TwoCellAnchor !=nil {_gfba :=_bb .StartElement {Name :_bb .Name {Local :"\u0078\u0064\u0072\u003a\u0074\u0077\u006f\u0043\u0065\u006c\u006c\u0041n\u0063\u0068\u006f\u0072"}};
e .EncodeElement (_dfac .TwoCellAnchor ,_gfba );}else if _dfac .OneCellAnchor !=nil {_dabd :=_bb .StartElement {Name :_bb .Name {Local :"\u0078\u0064\u0072\u003a\u006f\u006e\u0065\u0043\u0065\u006c\u006c\u0041n\u0063\u0068\u006f\u0072"}};e .EncodeElement (_dfac .OneCellAnchor ,_dabd );
}else if _dfac .AbsoluteAnchor !=nil {_bggd :=_bb .StartElement {Name :_bb .Name {Local :"\u0078d\u0072:\u0061\u0062\u0073\u006f\u006cu\u0074\u0065A\u006e\u0063\u0068\u006f\u0072"}};e .EncodeElement (_dfac .AbsoluteAnchor ,_bggd );};e .EncodeToken (_bb .EndElement {Name :start .Name });
return nil ;};type CT_OneCellAnchor struct{From *CT_Marker ;Ext *_ba .CT_PositiveSize2D ;ObjectChoicesChoice *EG_ObjectChoicesChoice ;ClientData *CT_AnchorClientData ;};type WsDr struct{CT_Drawing };func NewCT_GroupShapeChoice ()*CT_GroupShapeChoice {_bdde :=&CT_GroupShapeChoice {};
return _bdde };func (_fbf *CT_PictureNonVisual )UnmarshalXML (d *_bb .Decoder ,start _bb .StartElement )error {_fbf .CNvPr =_ba .NewCT_NonVisualDrawingProps ();_fbf .CNvPicPr =_ba .NewCT_NonVisualPictureProperties ();_eaec :for {_dfbd ,_agb :=d .Token ();
if _agb !=nil {return _agb ;};switch _cde :=_dfbd .(type ){case _bb .StartElement :switch _cde .Name {case _bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067",Local :"\u0063\u004e\u0076P\u0072"},_bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fdr\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0073\u0070\u0072\u0065\u0061\u0064\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0063\u004e\u0076P\u0072"}:if _cdcd :=d .DecodeElement (_fbf .CNvPr ,&_cde );
_cdcd !=nil {return _cdcd ;};case _bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067",Local :"\u0063\u004e\u0076\u0050\u0069\u0063\u0050\u0072"},_bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fdr\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0073\u0070\u0072\u0065\u0061\u0064\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0063\u004e\u0076\u0050\u0069\u0063\u0050\u0072"}:if _efbc :=d .DecodeElement (_fbf .CNvPicPr ,&_cde );
_efbc !=nil {return _efbc ;};default:_c .Log .Debug ("\u0073\u006b\u0069\u0070\u0070\u0069\u006e\u0067\u0020\u0075\u006e\u0073\u0075\u0070\u0070o\u0072\u0074\u0065\u0064\u0020\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020o\u006e\u0020\u0043\u0054\u005f\u0050\u0069\u0063\u0074\u0075\u0072\u0065No\u006e\u0056\u0069\u0073\u0075\u0061\u006c\u0020\u0025\u0076",_cde .Name );
if _bfc :=d .Skip ();_bfc !=nil {return _bfc ;};};case _bb .EndElement :break _eaec ;case _bb .CharData :};};return nil ;};func (_gba *CT_Marker )MarshalXML (e *_bb .Encoder ,start _bb .StartElement )error {e .EncodeToken (start );_efe :=_bb .StartElement {Name :_bb .Name {Local :"\u0078d\u0072\u003a\u0063\u006f\u006c"}};
e .EncodeElement (_gba .Col ,_efe );_dgb :=_bb .StartElement {Name :_bb .Name {Local :"\u0078\u0064\u0072\u003a\u0063\u006f\u006c\u004f\u0066\u0066"}};e .EncodeElement (_gba .ColOff ,_dgb );_faac :=_bb .StartElement {Name :_bb .Name {Local :"\u0078d\u0072\u003a\u0072\u006f\u0077"}};
e .EncodeElement (_gba .Row ,_faac );_dafb :=_bb .StartElement {Name :_bb .Name {Local :"\u0078\u0064\u0072\u003a\u0072\u006f\u0077\u004f\u0066\u0066"}};e .EncodeElement (_gba .RowOff ,_dafb );e .EncodeToken (_bb .EndElement {Name :start .Name });return nil ;
};type CT_TwoCellAnchor struct{

// Positioning and Resizing Behaviors
EditAsAttr ST_EditAs ;

// Starting Anchor Point
From *CT_Marker ;

// Ending Anchor Point
To *CT_Marker ;ObjectChoicesChoice *EG_ObjectChoicesChoice ;

// Client Data
ClientData *CT_AnchorClientData ;};func (_aab *CT_GroupShape )MarshalXML (e *_bb .Encoder ,start _bb .StartElement )error {e .EncodeToken (start );_bag :=_bb .StartElement {Name :_bb .Name {Local :"\u0078\u0064\u0072\u003a\u006e\u0076\u0047\u0072\u0070\u0053\u0070\u0050\u0072"}};
e .EncodeElement (_aab .NvGrpSpPr ,_bag );_aae :=_bb .StartElement {Name :_bb .Name {Local :"x\u0064\u0072\u003a\u0067\u0072\u0070\u0053\u0070\u0050\u0072"}};e .EncodeElement (_aab .GrpSpPr ,_aae );if _aab .GroupShapeChoice !=nil {for _ ,_daa :=range _aab .GroupShapeChoice {_daa .MarshalXML (e ,_bb .StartElement {});
};};e .EncodeToken (_bb .EndElement {Name :start .Name });return nil ;};type CT_GroupShapeChoice struct{Sp *CT_Shape ;GrpSp *CT_GroupShape ;GraphicFrame *CT_GraphicalObjectFrame ;CxnSp *CT_Connector ;Pic *CT_Picture ;};

// Validate validates the CT_Shape and its children
func (_fcbb *CT_Shape )Validate ()error {return _fcbb .ValidateWithPath ("\u0043\u0054\u005f\u0053\u0068\u0061\u0070\u0065");};func (_cage *EG_ObjectChoicesChoice )UnmarshalXML (d *_bb .Decoder ,start _bb .StartElement )error {_agee :=start ;switch start .Name {case _bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067",Local :"\u0073\u0070"},_bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fdr\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0073\u0070\u0072\u0065\u0061\u0064\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0073\u0070"}:_cage .Sp =NewCT_Shape ();
if _feg :=d .DecodeElement (_cage .Sp ,&_agee );_feg !=nil {return _feg ;};case _bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067",Local :"\u0067\u0072\u0070S\u0070"},_bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fdr\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0073\u0070\u0072\u0065\u0061\u0064\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0067\u0072\u0070S\u0070"}:_cage .GrpSp =NewCT_GroupShape ();
if _abf :=d .DecodeElement (_cage .GrpSp ,&_agee );_abf !=nil {return _abf ;};case _bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067",Local :"\u0067\u0072\u0061p\u0068\u0069\u0063\u0046\u0072\u0061\u006d\u0065"},_bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fdr\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0073\u0070\u0072\u0065\u0061\u0064\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0067\u0072\u0061p\u0068\u0069\u0063\u0046\u0072\u0061\u006d\u0065"}:_cage .GraphicFrame =NewCT_GraphicalObjectFrame ();
if _cfcc :=d .DecodeElement (_cage .GraphicFrame ,&_agee );_cfcc !=nil {return _cfcc ;};case _bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067",Local :"\u0063\u0078\u006eS\u0070"},_bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fdr\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0073\u0070\u0072\u0065\u0061\u0064\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0063\u0078\u006eS\u0070"}:_cage .CxnSp =NewCT_Connector ();
if _cbag :=d .DecodeElement (_cage .CxnSp ,&_agee );_cbag !=nil {return _cbag ;};case _bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067",Local :"\u0070\u0069\u0063"},_bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fdr\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0073\u0070\u0072\u0065\u0061\u0064\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0070\u0069\u0063"}:_cage .Pic =NewCT_Picture ();
if _gbg :=d .DecodeElement (_cage .Pic ,&_agee );_gbg !=nil {return _gbg ;};case _bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067",Local :"c\u006f\u006e\u0074\u0065\u006e\u0074\u0050\u0061\u0072\u0074"},_bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fdr\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0073\u0070\u0072\u0065\u0061\u0064\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"c\u006f\u006e\u0074\u0065\u006e\u0074\u0050\u0061\u0072\u0074"}:_cage .ContentPart =NewCT_Rel ();
if _ebgf :=d .DecodeElement (_cage .ContentPart ,&_agee );_ebgf !=nil {return _ebgf ;};default:_c .Log .Debug ("\u0073\u006b\u0069\u0070p\u0069\u006e\u0067\u0020\u0075\u006e\u0073\u0075\u0070p\u006f\u0072\u0074\u0065\u0064\u0020\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0045G\u005f\u004f\u0062\u006a\u0065c\u0074\u0043\u0068\u006f\u0069\u0063\u0065\u0073\u0043\u0068\u006f\u0069\u0063\u0065\u0020\u0025\u0076",_agee .Name );
if _edfb :=d .Skip ();_edfb !=nil {return _edfb ;};};return nil ;};func (_cdab *EG_ObjectChoicesChoice )MarshalXML (e *_bb .Encoder ,start _bb .StartElement )error {if _cdab .Sp !=nil {_bfff :=_bb .StartElement {Name :_bb .Name {Local :"\u0078\u0064\u0072\u003a\u0073\u0070"}};
e .EncodeElement (_cdab .Sp ,_bfff );}else if _cdab .GrpSp !=nil {_fffd :=_bb .StartElement {Name :_bb .Name {Local :"\u0078d\u0072\u003a\u0067\u0072\u0070\u0053p"}};e .EncodeElement (_cdab .GrpSp ,_fffd );}else if _cdab .GraphicFrame !=nil {_gfgf :=_bb .StartElement {Name :_bb .Name {Local :"\u0078\u0064r\u003a\u0067\u0072a\u0070\u0068\u0069\u0063\u0046\u0072\u0061\u006d\u0065"}};
e .EncodeElement (_cdab .GraphicFrame ,_gfgf );}else if _cdab .CxnSp !=nil {_gfbc :=_bb .StartElement {Name :_bb .Name {Local :"\u0078d\u0072\u003a\u0063\u0078\u006e\u0053p"}};e .EncodeElement (_cdab .CxnSp ,_gfbc );}else if _cdab .Pic !=nil {_dcf :=_bb .StartElement {Name :_bb .Name {Local :"\u0078d\u0072\u003a\u0070\u0069\u0063"}};
e .EncodeElement (_cdab .Pic ,_dcf );}else if _cdab .ContentPart !=nil {_bdcb :=_bb .StartElement {Name :_bb .Name {Local :"\u0078d\u0072:\u0063\u006f\u006e\u0074\u0065\u006e\u0074\u0050\u0061\u0072\u0074"}};e .EncodeElement (_cdab .ContentPart ,_bdcb );
};return nil ;};func (_gfgg *From )UnmarshalXML (d *_bb .Decoder ,start _bb .StartElement )error {_gfgg .CT_Marker =*NewCT_Marker ();_cffc :for {_bbfg ,_bgc :=d .Token ();if _bgc !=nil {return _bgc ;};switch _egea :=_bbfg .(type ){case _bb .StartElement :switch _egea .Name {case _bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067",Local :"\u0063\u006f\u006c"},_bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fdr\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0073\u0070\u0072\u0065\u0061\u0064\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0063\u006f\u006c"}:if _gefd :=d .DecodeElement (&_gfgg .Col ,&_egea );
_gefd !=nil {return _gefd ;};case _bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067",Local :"\u0063\u006f\u006c\u004f\u0066\u0066"},_bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fdr\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0073\u0070\u0072\u0065\u0061\u0064\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0063\u006f\u006c\u004f\u0066\u0066"}:_defe ,_adcag :=d .Token ();
if _adcag !=nil {return _adcag ;};switch _fade :=_defe .(type ){case _bb .CharData :_gab :=string (_fade );_ffa ,_daac :=_ba .ParseUnionST_Coordinate (_gab );if _daac !=nil {return nil ;};_gfgg .ColOff =_ffa ;d .Skip ();case _bb .EndElement :};case _bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067",Local :"\u0072\u006f\u0077"},_bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fdr\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0073\u0070\u0072\u0065\u0061\u0064\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0072\u006f\u0077"}:if _dec :=d .DecodeElement (&_gfgg .Row ,&_egea );
_dec !=nil {return _dec ;};case _bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067",Local :"\u0072\u006f\u0077\u004f\u0066\u0066"},_bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fdr\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0073\u0070\u0072\u0065\u0061\u0064\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0072\u006f\u0077\u004f\u0066\u0066"}:_geeba ,_cbab :=d .Token ();
if _cbab !=nil {return _cbab ;};switch _fggcd :=_geeba .(type ){case _bb .CharData :_fdbg :=string (_fggcd );_bcf ,_cfdg :=_ba .ParseUnionST_Coordinate (_fdbg );if _cfdg !=nil {return nil ;};_gfgg .RowOff =_bcf ;d .Skip ();case _bb .EndElement :};default:_c .Log .Debug ("\u0073\u006b\u0069\u0070\u0070\u0069\u006e\u0067\u0020\u0075\u006e\u0073\u0075p\u0070\u006f\u0072\u0074\u0065\u0064 \u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0046\u0072o\u006d\u0020\u0025\u0076",_egea .Name );
if _afd :=d .Skip ();_afd !=nil {return _afd ;};};case _bb .EndElement :break _cffc ;case _bb .CharData :};};return nil ;};type CT_GroupShape struct{

// Non-Visual Properties for a Group Shape
NvGrpSpPr *CT_GroupShapeNonVisual ;

// Group Shape Properties
GrpSpPr *_ba .CT_GroupShapeProperties ;GroupShapeChoice []*CT_GroupShapeChoice ;};func (_fbb *CT_Shape )MarshalXML (e *_bb .Encoder ,start _bb .StartElement )error {if _fbb .MacroAttr !=nil {start .Attr =append (start .Attr ,_bb .Attr {Name :_bb .Name {Local :"\u006d\u0061\u0063r\u006f"},Value :_d .Sprintf ("\u0025\u0076",*_fbb .MacroAttr )});
};if _fbb .TextlinkAttr !=nil {start .Attr =append (start .Attr ,_bb .Attr {Name :_bb .Name {Local :"\u0074\u0065\u0078\u0074\u006c\u0069\u006e\u006b"},Value :_d .Sprintf ("\u0025\u0076",*_fbb .TextlinkAttr )});};if _fbb .FLocksTextAttr !=nil {start .Attr =append (start .Attr ,_bb .Attr {Name :_bb .Name {Local :"\u0066\u004c\u006f\u0063\u006b\u0073\u0054\u0065\u0078\u0074"},Value :_d .Sprintf ("\u0025\u0064",_dgec (*_fbb .FLocksTextAttr ))});
};if _fbb .FPublishedAttr !=nil {start .Attr =append (start .Attr ,_bb .Attr {Name :_bb .Name {Local :"\u0066\u0050\u0075\u0062\u006c\u0069\u0073\u0068\u0065\u0064"},Value :_d .Sprintf ("\u0025\u0064",_dgec (*_fbb .FPublishedAttr ))});};e .EncodeToken (start );
_gdb :=_bb .StartElement {Name :_bb .Name {Local :"\u0078\u0064\u0072\u003a\u006e\u0076\u0053\u0070\u0050\u0072"}};e .EncodeElement (_fbb .NvSpPr ,_gdb );_agbg :=_bb .StartElement {Name :_bb .Name {Local :"\u0078\u0064\u0072\u003a\u0073\u0070\u0050\u0072"}};
e .EncodeElement (_fbb .SpPr ,_agbg );if _fbb .Style !=nil {_dafbe :=_bb .StartElement {Name :_bb .Name {Local :"\u0078d\u0072\u003a\u0073\u0074\u0079\u006ce"}};e .EncodeElement (_fbb .Style ,_dafbe );};if _fbb .TxBody !=nil {_gdde :=_bb .StartElement {Name :_bb .Name {Local :"\u0078\u0064\u0072\u003a\u0074\u0078\u0042\u006f\u0064\u0079"}};
e .EncodeElement (_fbb .TxBody ,_gdde );};e .EncodeToken (_bb .EndElement {Name :start .Name });return nil ;};

// ValidateWithPath validates the CT_Shape and its children, prefixing error messages with path
func (_acbb *CT_Shape )ValidateWithPath (path string )error {if _afab :=_acbb .NvSpPr .ValidateWithPath (path +"\u002fN\u0076\u0053\u0070\u0050\u0072");_afab !=nil {return _afab ;};if _gdbb :=_acbb .SpPr .ValidateWithPath (path +"\u002f\u0053\u0070P\u0072");
_gdbb !=nil {return _gdbb ;};if _acbb .Style !=nil {if _abcbb :=_acbb .Style .ValidateWithPath (path +"\u002f\u0053\u0074\u0079\u006c\u0065");_abcbb !=nil {return _abcbb ;};};if _acbb .TxBody !=nil {if _fff :=_acbb .TxBody .ValidateWithPath (path +"\u002fT\u0078\u0042\u006f\u0064\u0079");
_fff !=nil {return _fff ;};};return nil ;};func (_acd *CT_Drawing )UnmarshalXML (d *_bb .Decoder ,start _bb .StartElement )error {_fgf :for {_eba ,_aef :=d .Token ();if _aef !=nil {return _aef ;};switch _cfed :=_eba .(type ){case _bb .StartElement :switch _cfed .Name {case _bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067",Local :"\u0074\u0077\u006f\u0043\u0065\u006c\u006c\u0041\u006e\u0063\u0068\u006f\u0072"},_bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fdr\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0073\u0070\u0072\u0065\u0061\u0064\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0074\u0077\u006f\u0043\u0065\u006c\u006c\u0041\u006e\u0063\u0068\u006f\u0072"}:_dba :=NewEG_Anchor ();
_dba .AnchorChoice =NewEG_AnchorChoice ();_acd .EG_Anchor =append (_acd .EG_Anchor ,_dba );if _cad :=d .DecodeElement (&_dba .AnchorChoice .TwoCellAnchor ,&_cfed );_cad !=nil {return _cad ;};case _bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067",Local :"\u006f\u006e\u0065\u0043\u0065\u006c\u006c\u0041\u006e\u0063\u0068\u006f\u0072"},_bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fdr\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0073\u0070\u0072\u0065\u0061\u0064\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u006f\u006e\u0065\u0043\u0065\u006c\u006c\u0041\u006e\u0063\u0068\u006f\u0072"}:_bace :=NewEG_Anchor ();
_bace .AnchorChoice =NewEG_AnchorChoice ();_acd .EG_Anchor =append (_acd .EG_Anchor ,_bace );if _gff :=d .DecodeElement (&_bace .AnchorChoice .OneCellAnchor ,&_cfed );_gff !=nil {return _gff ;};case _bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067",Local :"\u0061\u0062\u0073\u006f\u006c\u0075\u0074\u0065\u0041n\u0063\u0068\u006f\u0072"},_bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fdr\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0073\u0070\u0072\u0065\u0061\u0064\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0061\u0062\u0073\u006f\u006c\u0075\u0074\u0065\u0041n\u0063\u0068\u006f\u0072"}:_gg :=NewEG_Anchor ();
_gg .AnchorChoice =NewEG_AnchorChoice ();_acd .EG_Anchor =append (_acd .EG_Anchor ,_gg );if _df :=d .DecodeElement (&_gg .AnchorChoice .AbsoluteAnchor ,&_cfed );_df !=nil {return _df ;};default:_c .Log .Debug ("\u0073k\u0069\u0070p\u0069\u006e\u0067 \u0075\u006e\u0073\u0075\u0070\u0070\u006fr\u0074\u0065\u0064\u0020\u0065\u006ce\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0043\u0054\u005fD\u0072\u0061\u0077\u0069\u006e\u0067\u0020\u0025\u0076",_cfed .Name );
if _daf :=d .Skip ();_daf !=nil {return _daf ;};};case _bb .EndElement :break _fgf ;case _bb .CharData :};};return nil ;};func NewCT_ShapeNonVisual ()*CT_ShapeNonVisual {_dbfa :=&CT_ShapeNonVisual {};_dbfa .CNvPr =_ba .NewCT_NonVisualDrawingProps ();_dbfa .CNvSpPr =_ba .NewCT_NonVisualDrawingShapeProps ();
return _dbfa ;};

// Validate validates the CT_GraphicalObjectFrame and its children
func (_dac *CT_GraphicalObjectFrame )Validate ()error {return _dac .ValidateWithPath ("\u0043\u0054\u005fGr\u0061\u0070\u0068\u0069\u0063\u0061\u006c\u004f\u0062\u006a\u0065\u0063\u0074\u0046\u0072\u0061\u006d\u0065");};func NewCT_Shape ()*CT_Shape {_edc :=&CT_Shape {};
_edc .NvSpPr =NewCT_ShapeNonVisual ();_edc .SpPr =_ba .NewCT_ShapeProperties ();return _edc ;};

// ValidateWithPath validates the CT_ConnectorNonVisual and its children, prefixing error messages with path
func (_edg *CT_ConnectorNonVisual )ValidateWithPath (path string )error {if _afc :=_edg .CNvPr .ValidateWithPath (path +"\u002f\u0043\u004e\u0076\u0050\u0072");_afc !=nil {return _afc ;};if _bgg :=_edg .CNvCxnSpPr .ValidateWithPath (path +"/\u0043\u004e\u0076\u0043\u0078\u006e\u0053\u0070\u0050\u0072");
_bgg !=nil {return _bgg ;};return nil ;};func (_bba *CT_PictureNonVisual )MarshalXML (e *_bb .Encoder ,start _bb .StartElement )error {e .EncodeToken (start );_dfaf :=_bb .StartElement {Name :_bb .Name {Local :"\u0078d\u0072\u003a\u0063\u004e\u0076\u0050r"}};
e .EncodeElement (_bba .CNvPr ,_dfaf );_gbe :=_bb .StartElement {Name :_bb .Name {Local :"\u0078\u0064\u0072:\u0063\u004e\u0076\u0050\u0069\u0063\u0050\u0072"}};e .EncodeElement (_bba .CNvPicPr ,_gbe );e .EncodeToken (_bb .EndElement {Name :start .Name });
return nil ;};type CT_ConnectorNonVisual struct{

// Connection Non-Visual Properties
CNvPr *_ba .CT_NonVisualDrawingProps ;

// Non-Visual Connector Shape Drawing Properties
CNvCxnSpPr *_ba .CT_NonVisualConnectorProperties ;};func (_cbe *CT_AnchorClientData )UnmarshalXML (d *_bb .Decoder ,start _bb .StartElement )error {for _ ,_ed :=range start .Attr {if _ed .Name .Local =="\u0066L\u006fc\u006b\u0073\u0057\u0069\u0074\u0068\u0053\u0068\u0065\u0065\u0074"{_gee ,_bf :=_bg .ParseBool (_ed .Value );
if _bf !=nil {return _bf ;};_cbe .FLocksWithSheetAttr =&_gee ;continue ;};if _ed .Name .Local =="\u0066\u0050r\u0069\u006e\u0074s\u0057\u0069\u0074\u0068\u0053\u0068\u0065\u0065\u0074"{_gad ,_cdf :=_bg .ParseBool (_ed .Value );if _cdf !=nil {return _cdf ;
};_cbe .FPrintsWithSheetAttr =&_gad ;continue ;};};for {_abd ,_cfe :=d .Token ();if _cfe !=nil {return _d .Errorf ("\u0070\u0061\u0072s\u0069\u006e\u0067\u0020C\u0054\u005f\u0041\u006e\u0063\u0068\u006fr\u0043\u006c\u0069\u0065\u006e\u0074\u0044\u0061\u0074\u0061\u003a\u0020\u0025\u0073",_cfe );
};if _ac ,_ef :=_abd .(_bb .EndElement );_ef &&_ac .Name ==start .Name {break ;};};return nil ;};func (_abc *CT_GroupShapeChoice )UnmarshalXML (d *_bb .Decoder ,start _bb .StartElement )error {_fdcd :=start ;switch start .Name {case _bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067",Local :"\u0073\u0070"},_bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fdr\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0073\u0070\u0072\u0065\u0061\u0064\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0073\u0070"}:_abc .Sp =NewCT_Shape ();
if _fcd :=d .DecodeElement (_abc .Sp ,&_fdcd );_fcd !=nil {return _fcd ;};case _bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067",Local :"\u0067\u0072\u0070S\u0070"},_bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fdr\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0073\u0070\u0072\u0065\u0061\u0064\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0067\u0072\u0070S\u0070"}:_abc .GrpSp =NewCT_GroupShape ();
if _cca :=d .DecodeElement (_abc .GrpSp ,&_fdcd );_cca !=nil {return _cca ;};case _bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067",Local :"\u0067\u0072\u0061p\u0068\u0069\u0063\u0046\u0072\u0061\u006d\u0065"},_bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fdr\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0073\u0070\u0072\u0065\u0061\u0064\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0067\u0072\u0061p\u0068\u0069\u0063\u0046\u0072\u0061\u006d\u0065"}:_abc .GraphicFrame =NewCT_GraphicalObjectFrame ();
if _cea :=d .DecodeElement (_abc .GraphicFrame ,&_fdcd );_cea !=nil {return _cea ;};case _bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067",Local :"\u0063\u0078\u006eS\u0070"},_bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fdr\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0073\u0070\u0072\u0065\u0061\u0064\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0063\u0078\u006eS\u0070"}:_abc .CxnSp =NewCT_Connector ();
if _gcf :=d .DecodeElement (_abc .CxnSp ,&_fdcd );_gcf !=nil {return _gcf ;};case _bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067",Local :"\u0070\u0069\u0063"},_bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fdr\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0073\u0070\u0072\u0065\u0061\u0064\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0070\u0069\u0063"}:_abc .Pic =NewCT_Picture ();
if _ddfc :=d .DecodeElement (_abc .Pic ,&_fdcd );_ddfc !=nil {return _ddfc ;};default:_c .Log .Debug ("\u0073\u006b\u0069\u0070\u0070\u0069\u006e\u0067\u0020\u0075\u006e\u0073\u0075\u0070\u0070o\u0072\u0074\u0065\u0064\u0020\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020o\u006e\u0020\u0043\u0054\u005f\u0047\u0072\u006f\u0075\u0070\u0053\u0068ap\u0065\u0043\u0068\u006f\u0069\u0063\u0065\u0020\u0025\u0076",_fdcd .Name );
if _gfdc :=d .Skip ();_gfdc !=nil {return _gfdc ;};};return nil ;};

// Validate validates the CT_Connector and its children
func (_cec *CT_Connector )Validate ()error {return _cec .ValidateWithPath ("\u0043\u0054\u005fC\u006f\u006e\u006e\u0065\u0063\u0074\u006f\u0072");};

// ValidateWithPath validates the From and its children, prefixing error messages with path
func (_bgcf *From )ValidateWithPath (path string )error {if _gbbf :=_bgcf .CT_Marker .ValidateWithPath (path );_gbbf !=nil {return _gbbf ;};return nil ;};

// ValidateWithPath validates the CT_GroupShape and its children, prefixing error messages with path
func (_eca *CT_GroupShape )ValidateWithPath (path string )error {if _gfd :=_eca .NvGrpSpPr .ValidateWithPath (path +"\u002f\u004e\u0076\u0047\u0072\u0070\u0053\u0070\u0050\u0072");_gfd !=nil {return _gfd ;};if _ecd :=_eca .GrpSpPr .ValidateWithPath (path +"\u002f\u0047\u0072\u0070\u0053\u0070\u0050\u0072");
_ecd !=nil {return _ecd ;};for _aaec ,_ecc :=range _eca .GroupShapeChoice {if _gag :=_ecc .ValidateWithPath (_d .Sprintf ("\u0025\u0073\u002fGr\u006f\u0075\u0070\u0053\u0068\u0061\u0070\u0065\u0043\u0068\u006f\u0069\u0063\u0065\u005b\u0025\u0064\u005d",path ,_aaec ));
_gag !=nil {return _gag ;};};return nil ;};

// Validate validates the CT_AbsoluteAnchor and its children
func (_cb *CT_AbsoluteAnchor )Validate ()error {return _cb .ValidateWithPath ("\u0043\u0054\u005f\u0041\u0062\u0073\u006f\u006c\u0075\u0074\u0065\u0041n\u0063\u0068\u006f\u0072");};func (_ebcd ST_EditAs )ValidateWithPath (path string )error {switch _ebcd {case 0,1,2,3:default:return _d .Errorf ("\u0025s\u003a\u0020\u006f\u0075t\u0020\u006f\u0066\u0020\u0072a\u006eg\u0065 \u0076\u0061\u006c\u0075\u0065\u0020\u0025d",path ,int (_ebcd ));
};return nil ;};func NewEG_ObjectChoices ()*EG_ObjectChoices {_acaf :=&EG_ObjectChoices {};_acaf .ObjectChoicesChoice =NewEG_ObjectChoicesChoice ();return _acaf ;};func (_ced *CT_Drawing )MarshalXML (e *_bb .Encoder ,start _bb .StartElement )error {e .EncodeToken (start );
if _ced .EG_Anchor !=nil {for _ ,_egb :=range _ced .EG_Anchor {_egb .MarshalXML (e ,_bb .StartElement {});};};e .EncodeToken (_bb .EndElement {Name :start .Name });return nil ;};func NewCT_OneCellAnchor ()*CT_OneCellAnchor {_fdf :=&CT_OneCellAnchor {};
_fdf .From =NewCT_Marker ();_fdf .Ext =_ba .NewCT_PositiveSize2D ();_fdf .ObjectChoicesChoice =NewEG_ObjectChoicesChoice ();_fdf .ClientData =NewCT_AnchorClientData ();return _fdf ;};func (_cba *CT_GroupShape )UnmarshalXML (d *_bb .Decoder ,start _bb .StartElement )error {_cba .NvGrpSpPr =NewCT_GroupShapeNonVisual ();
_cba .GrpSpPr =_ba .NewCT_GroupShapeProperties ();_fb :for {_aaf ,_fba :=d .Token ();if _fba !=nil {return _fba ;};switch _bea :=_aaf .(type ){case _bb .StartElement :switch _bea .Name {case _bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067",Local :"\u006ev\u0047\u0072\u0070\u0053\u0070\u0050r"},_bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fdr\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0073\u0070\u0072\u0065\u0061\u0064\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u006ev\u0047\u0072\u0070\u0053\u0070\u0050r"}:if _gbb :=d .DecodeElement (_cba .NvGrpSpPr ,&_bea );
_gbb !=nil {return _gbb ;};case _bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067",Local :"\u0067r\u0070\u0053\u0070\u0050\u0072"},_bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fdr\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0073\u0070\u0072\u0065\u0061\u0064\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0067r\u0070\u0053\u0070\u0050\u0072"}:if _egg :=d .DecodeElement (_cba .GrpSpPr ,&_bea );
_egg !=nil {return _egg ;};case _bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067",Local :"\u0073\u0070"},_bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fdr\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0073\u0070\u0072\u0065\u0061\u0064\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0073\u0070"}:_abb :=NewCT_GroupShapeChoice ();
if _fcbd :=d .DecodeElement (&_abb .Sp ,&_bea );_fcbd !=nil {return _fcbd ;};_cba .GroupShapeChoice =append (_cba .GroupShapeChoice ,_abb );case _bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067",Local :"\u0067\u0072\u0070S\u0070"},_bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fdr\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0073\u0070\u0072\u0065\u0061\u0064\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0067\u0072\u0070S\u0070"}:_cfaf :=NewCT_GroupShapeChoice ();
if _bfge :=d .DecodeElement (&_cfaf .GrpSp ,&_bea );_bfge !=nil {return _bfge ;};_cba .GroupShapeChoice =append (_cba .GroupShapeChoice ,_cfaf );case _bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067",Local :"\u0067\u0072\u0061p\u0068\u0069\u0063\u0046\u0072\u0061\u006d\u0065"},_bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fdr\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0073\u0070\u0072\u0065\u0061\u0064\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0067\u0072\u0061p\u0068\u0069\u0063\u0046\u0072\u0061\u006d\u0065"}:_ccg :=NewCT_GroupShapeChoice ();
if _fdb :=d .DecodeElement (&_ccg .GraphicFrame ,&_bea );_fdb !=nil {return _fdb ;};_cba .GroupShapeChoice =append (_cba .GroupShapeChoice ,_ccg );case _bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067",Local :"\u0063\u0078\u006eS\u0070"},_bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fdr\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0073\u0070\u0072\u0065\u0061\u0064\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0063\u0078\u006eS\u0070"}:_fggc :=NewCT_GroupShapeChoice ();
if _aff :=d .DecodeElement (&_fggc .CxnSp ,&_bea );_aff !=nil {return _aff ;};_cba .GroupShapeChoice =append (_cba .GroupShapeChoice ,_fggc );case _bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067",Local :"\u0070\u0069\u0063"},_bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fdr\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0073\u0070\u0072\u0065\u0061\u0064\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0070\u0069\u0063"}:_cag :=NewCT_GroupShapeChoice ();
if _dce :=d .DecodeElement (&_cag .Pic ,&_bea );_dce !=nil {return _dce ;};_cba .GroupShapeChoice =append (_cba .GroupShapeChoice ,_cag );default:_c .Log .Debug ("\u0073\u006b\u0069\u0070\u0070\u0069\u006e\u0067 \u0075\u006e\u0073up\u0070\u006f\u0072\u0074\u0065\u0064 \u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0043\u0054\u005f\u0047r\u006f\u0075\u0070\u0053\u0068\u0061\u0070\u0065 \u0025\u0076",_bea .Name );
if _dbf :=d .Skip ();_dbf !=nil {return _dbf ;};};case _bb .EndElement :break _fb ;case _bb .CharData :};};return nil ;};

// Validate validates the EG_ObjectChoices and its children
func (_dceg *EG_ObjectChoices )Validate ()error {return _dceg .ValidateWithPath ("\u0045\u0047_\u004f\u0062\u006ae\u0063\u0074\u0043\u0068\u006f\u0069\u0063\u0065\u0073");};func (_gbf *CT_GroupShapeNonVisual )UnmarshalXML (d *_bb .Decoder ,start _bb .StartElement )error {_gbf .CNvPr =_ba .NewCT_NonVisualDrawingProps ();
_gbf .CNvGrpSpPr =_ba .NewCT_NonVisualGroupDrawingShapeProps ();_aaa :for {_ebe ,_age :=d .Token ();if _age !=nil {return _age ;};switch _fgff :=_ebe .(type ){case _bb .StartElement :switch _fgff .Name {case _bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067",Local :"\u0063\u004e\u0076P\u0072"},_bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fdr\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0073\u0070\u0072\u0065\u0061\u0064\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0063\u004e\u0076P\u0072"}:if _cff :=d .DecodeElement (_gbf .CNvPr ,&_fgff );
_cff !=nil {return _cff ;};case _bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067",Local :"\u0063\u004e\u0076\u0047\u0072\u0070\u0053\u0070\u0050\u0072"},_bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fdr\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0073\u0070\u0072\u0065\u0061\u0064\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0063\u004e\u0076\u0047\u0072\u0070\u0053\u0070\u0050\u0072"}:if _caga :=d .DecodeElement (_gbf .CNvGrpSpPr ,&_fgff );
_caga !=nil {return _caga ;};default:_c .Log .Debug ("\u0073\u006b\u0069\u0070p\u0069\u006e\u0067\u0020\u0075\u006e\u0073\u0075\u0070p\u006f\u0072\u0074\u0065\u0064\u0020\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0043T\u005f\u0047\u0072\u006f\u0075p\u0053\u0068\u0061\u0070\u0065\u004e\u006f\u006e\u0056\u0069\u0073\u0075\u0061\u006c\u0020\u0025\u0076",_fgff .Name );
if _gfb :=d .Skip ();_gfb !=nil {return _gfb ;};};case _bb .EndElement :break _aaa ;case _bb .CharData :};};return nil ;};func (_fdcg ST_EditAs )Validate ()error {return _fdcg .ValidateWithPath ("")};func (_fa *CT_AbsoluteAnchor )MarshalXML (e *_bb .Encoder ,start _bb .StartElement )error {e .EncodeToken (start );
_g :=_bb .StartElement {Name :_bb .Name {Local :"\u0078d\u0072\u003a\u0070\u006f\u0073"}};e .EncodeElement (_fa .Pos ,_g );_bd :=_bb .StartElement {Name :_bb .Name {Local :"\u0078d\u0072\u003a\u0065\u0078\u0074"}};e .EncodeElement (_fa .Ext ,_bd );_fa .ObjectChoicesChoice .MarshalXML (e ,_bb .StartElement {});
_fd :=_bb .StartElement {Name :_bb .Name {Local :"\u0078\u0064\u0072\u003a\u0063\u006c\u0069\u0065\u006et\u0044\u0061\u0074\u0061"}};e .EncodeElement (_fa .ClientData ,_fd );e .EncodeToken (_bb .EndElement {Name :start .Name });return nil ;};func NewCT_GraphicalObjectFrame ()*CT_GraphicalObjectFrame {_ddg :=&CT_GraphicalObjectFrame {};
_ddg .NvGraphicFramePr =NewCT_GraphicalObjectFrameNonVisual ();_ddg .Xfrm =_ba .NewCT_Transform2D ();_ddg .Graphic =_ba .NewGraphic ();return _ddg ;};func (_eef *CT_GraphicalObjectFrameNonVisual )UnmarshalXML (d *_bb .Decoder ,start _bb .StartElement )error {_eef .CNvPr =_ba .NewCT_NonVisualDrawingProps ();
_eef .CNvGraphicFramePr =_ba .NewCT_NonVisualGraphicFrameProperties ();_gcdb :for {_dea ,_fgcb :=d .Token ();if _fgcb !=nil {return _fgcb ;};switch _dca :=_dea .(type ){case _bb .StartElement :switch _dca .Name {case _bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067",Local :"\u0063\u004e\u0076P\u0072"},_bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fdr\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0073\u0070\u0072\u0065\u0061\u0064\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0063\u004e\u0076P\u0072"}:if _bad :=d .DecodeElement (_eef .CNvPr ,&_dca );
_bad !=nil {return _bad ;};case _bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067",Local :"\u0063\u004e\u0076\u0047\u0072\u0061\u0070\u0068\u0069\u0063\u0046\u0072a\u006d\u0065\u0050\u0072"},_bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fdr\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0073\u0070\u0072\u0065\u0061\u0064\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0063\u004e\u0076\u0047\u0072\u0061\u0070\u0068\u0069\u0063\u0046\u0072a\u006d\u0065\u0050\u0072"}:if _acb :=d .DecodeElement (_eef .CNvGraphicFramePr ,&_dca );
_acb !=nil {return _acb ;};default:_c .Log .Debug ("\u0073\u006b\u0069\u0070\u0070\u0069n\u0067\u0020\u0075\u006e\u0073u\u0070\u0070\u006f\u0072\u0074\u0065d\u0020\u0065\u006c\u0065\u006de\u006e\u0074\u0020\u006f\u006e\u0020\u0043\u0054\u005f\u0047\u0072\u0061p\u0068\u0069\u0063\u0061\u006c\u004f\u0062\u006a\u0065\u0063\u0074\u0046\u0072\u0061\u006de\u004e\u006f\u006e\u0056\u0069\u0073\u0075\u0061l\u0020\u0025\u0076",_dca .Name );
if _ccfc :=d .Skip ();_ccfc !=nil {return _ccfc ;};};case _bb .EndElement :break _gcdb ;case _bb .CharData :};};return nil ;};

// ValidateWithPath validates the EG_ObjectChoices and its children, prefixing error messages with path
func (_acca *EG_ObjectChoices )ValidateWithPath (path string )error {if _fad :=_acca .ObjectChoicesChoice .ValidateWithPath (path +"/\u004fb\u006a\u0065\u0063\u0074\u0043\u0068\u006f\u0069c\u0065\u0073\u0043\u0068oi\u0063\u0065");_fad !=nil {return _fad ;
};return nil ;};

// ValidateWithPath validates the CT_ShapeNonVisual and its children, prefixing error messages with path
func (_gca *CT_ShapeNonVisual )ValidateWithPath (path string )error {if _fdga :=_gca .CNvPr .ValidateWithPath (path +"\u002f\u0043\u004e\u0076\u0050\u0072");_fdga !=nil {return _fdga ;};if _edce :=_gca .CNvSpPr .ValidateWithPath (path +"\u002f\u0043\u004e\u0076\u0053\u0070\u0050\u0072");
_edce !=nil {return _edce ;};return nil ;};type CT_PictureNonVisual struct{CNvPr *_ba .CT_NonVisualDrawingProps ;

// Non-Visual Picture Drawing Properties
CNvPicPr *_ba .CT_NonVisualPictureProperties ;};func NewEG_Anchor ()*EG_Anchor {_dbgc :=&EG_Anchor {};_dbgc .AnchorChoice =NewEG_AnchorChoice ();return _dbgc ;};func (_gc *CT_Connector )UnmarshalXML (d *_bb .Decoder ,start _bb .StartElement )error {_gc .NvCxnSpPr =NewCT_ConnectorNonVisual ();
_gc .SpPr =_ba .NewCT_ShapeProperties ();for _ ,_dc :=range start .Attr {if _dc .Name .Local =="\u006d\u0061\u0063r\u006f"{_fec :=_dc .Value ;_gc .MacroAttr =&_fec ;continue ;};if _dc .Name .Local =="\u0066\u0050\u0075\u0062\u006c\u0069\u0073\u0068\u0065\u0064"{_cg ,_aca :=_bg .ParseBool (_dc .Value );
if _aca !=nil {return _aca ;};_gc .FPublishedAttr =&_cg ;continue ;};};_efb :for {_gaa ,_cbec :=d .Token ();if _cbec !=nil {return _cbec ;};switch _fgg :=_gaa .(type ){case _bb .StartElement :switch _fgg .Name {case _bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067",Local :"\u006ev\u0043\u0078\u006e\u0053\u0070\u0050r"},_bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fdr\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0073\u0070\u0072\u0065\u0061\u0064\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u006ev\u0043\u0078\u006e\u0053\u0070\u0050r"}:if _cda :=d .DecodeElement (_gc .NvCxnSpPr ,&_fgg );
_cda !=nil {return _cda ;};case _bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067",Local :"\u0073\u0070\u0050\u0072"},_bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fdr\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0073\u0070\u0072\u0065\u0061\u0064\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0073\u0070\u0050\u0072"}:if _bac :=d .DecodeElement (_gc .SpPr ,&_fgg );
_bac !=nil {return _bac ;};case _bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067",Local :"\u0073\u0074\u0079l\u0065"},_bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fdr\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0073\u0070\u0072\u0065\u0061\u0064\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0073\u0074\u0079l\u0065"}:_gc .Style =_ba .NewCT_ShapeStyle ();
if _gfe :=d .DecodeElement (_gc .Style ,&_fgg );_gfe !=nil {return _gfe ;};default:_c .Log .Debug ("s\u006b\u0069\u0070\u0070\u0069\u006e\u0067\u0020\u0075n\u0073\u0075\u0070\u0070\u006f\u0072\u0074ed\u0020\u0065\u006c\u0065m\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0043\u0054_C\u006f\u006en\u0065\u0063\u0074\u006f\u0072\u0020\u0025\u0076",_fgg .Name );
if _ee :=d .Skip ();_ee !=nil {return _ee ;};};case _bb .EndElement :break _efb ;case _bb .CharData :};};return nil ;};func (_fbg *CT_Picture )UnmarshalXML (d *_bb .Decoder ,start _bb .StartElement )error {_fbg .NvPicPr =NewCT_PictureNonVisual ();_fbg .BlipFill =_ba .NewCT_BlipFillProperties ();
_fbg .SpPr =_ba .NewCT_ShapeProperties ();for _ ,_afg :=range start .Attr {if _afg .Name .Local =="\u006d\u0061\u0063r\u006f"{_adg :=_afg .Value ;_fbg .MacroAttr =&_adg ;continue ;};if _afg .Name .Local =="\u0066\u0050\u0075\u0062\u006c\u0069\u0073\u0068\u0065\u0064"{_befc ,_ggd :=_bg .ParseBool (_afg .Value );
if _ggd !=nil {return _ggd ;};_fbg .FPublishedAttr =&_befc ;continue ;};};_acad :for {_egbb ,_ddge :=d .Token ();if _ddge !=nil {return _ddge ;};switch _beed :=_egbb .(type ){case _bb .StartElement :switch _beed .Name {case _bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067",Local :"\u006ev\u0050\u0069\u0063\u0050\u0072"},_bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fdr\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0073\u0070\u0072\u0065\u0061\u0064\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u006ev\u0050\u0069\u0063\u0050\u0072"}:if _aefa :=d .DecodeElement (_fbg .NvPicPr ,&_beed );
_aefa !=nil {return _aefa ;};case _bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067",Local :"\u0062\u006c\u0069\u0070\u0046\u0069\u006c\u006c"},_bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fdr\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0073\u0070\u0072\u0065\u0061\u0064\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0062\u006c\u0069\u0070\u0046\u0069\u006c\u006c"}:if _aeab :=d .DecodeElement (_fbg .BlipFill ,&_beed );
_aeab !=nil {return _aeab ;};case _bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067",Local :"\u0073\u0070\u0050\u0072"},_bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fdr\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0073\u0070\u0072\u0065\u0061\u0064\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0073\u0070\u0050\u0072"}:if _bfa :=d .DecodeElement (_fbg .SpPr ,&_beed );
_bfa !=nil {return _bfa ;};case _bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067",Local :"\u0073\u0074\u0079l\u0065"},_bb .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fdr\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0073\u0070\u0072\u0065\u0061\u0064\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061\u0077\u0069\u006e\u0067",Local :"\u0073\u0074\u0079l\u0065"}:_fbg .Style =_ba .NewCT_ShapeStyle ();
if _bbd :=d .DecodeElement (_fbg .Style ,&_beed );_bbd !=nil {return _bbd ;};default:_c .Log .Debug ("\u0073k\u0069\u0070p\u0069\u006e\u0067 \u0075\u006e\u0073\u0075\u0070\u0070\u006fr\u0074\u0065\u0064\u0020\u0065\u006ce\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0043\u0054\u005fP\u0069\u0063\u0074\u0075\u0072\u0065\u0020\u0025\u0076",_beed .Name );
if _caf :=d .Skip ();_caf !=nil {return _caf ;};};case _bb .EndElement :break _acad ;case _bb .CharData :};};return nil ;};func NewCT_Drawing ()*CT_Drawing {_bdb :=&CT_Drawing {};return _bdb };

// Validate validates the CT_TwoCellAnchor and its children
func (_ebbg *CT_TwoCellAnchor )Validate ()error {return _ebbg .ValidateWithPath ("\u0043\u0054_\u0054\u0077\u006fC\u0065\u006c\u006c\u0041\u006e\u0063\u0068\u006f\u0072");};type From struct{CT_Marker };func init (){_a .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067","\u0043\u0054\u005f\u0041nc\u0068\u006f\u0072\u0043\u006c\u0069\u0065\u006e\u0074\u0044\u0061\u0074\u0061",NewCT_AnchorClientData );
_a .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067","\u0043\u0054\u005f\u0053\u0068\u0061\u0070\u0065\u004e\u006f\u006e\u0056i\u0073\u0075\u0061\u006c",NewCT_ShapeNonVisual );
_a .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067","\u0043\u0054\u005f\u0053\u0068\u0061\u0070\u0065",NewCT_Shape );
_a .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067","C\u0054\u005f\u0043\u006fnn\u0065c\u0074\u006f\u0072\u004e\u006fn\u0056\u0069\u0073\u0075\u0061\u006c",NewCT_ConnectorNonVisual );
_a .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067","\u0043\u0054\u005fC\u006f\u006e\u006e\u0065\u0063\u0074\u006f\u0072",NewCT_Connector );
_a .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067","\u0043\u0054\u005f\u0050ic\u0074\u0075\u0072\u0065\u004e\u006f\u006e\u0056\u0069\u0073\u0075\u0061\u006c",NewCT_PictureNonVisual );
_a .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067","\u0043\u0054\u005f\u0050\u0069\u0063\u0074\u0075\u0072\u0065",NewCT_Picture );
_a .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067","\u0043\u0054\u005f\u0047\u0072\u0061\u0070\u0068\u0069\u0063\u0061\u006c\u004f\u0062\u006ae\u0063t\u0046\u0072\u0061\u006d\u0065\u004e\u006f\u006e\u0056\u0069\u0073\u0075\u0061\u006c",NewCT_GraphicalObjectFrameNonVisual );
_a .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067","\u0043\u0054\u005fGr\u0061\u0070\u0068\u0069\u0063\u0061\u006c\u004f\u0062\u006a\u0065\u0063\u0074\u0046\u0072\u0061\u006d\u0065",NewCT_GraphicalObjectFrame );
_a .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067","\u0043\u0054\u005f\u0047ro\u0075\u0070\u0053\u0068\u0061\u0070\u0065\u004e\u006f\u006e\u0056\u0069\u0073\u0075a\u006c",NewCT_GroupShapeNonVisual );
_a .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067","\u0043\u0054\u005f\u0047\u0072\u006f\u0075\u0070\u0053\u0068\u0061\u0070\u0065",NewCT_GroupShape );
_a .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067","\u0043\u0054\u005f\u0052\u0065\u006c",NewCT_Rel );
_a .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067","\u0043T\u005f\u004d\u0061\u0072\u006b\u0065r",NewCT_Marker );
_a .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067","\u0043\u0054_\u0054\u0077\u006fC\u0065\u006c\u006c\u0041\u006e\u0063\u0068\u006f\u0072",NewCT_TwoCellAnchor );
_a .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067","\u0043\u0054_\u004f\u006e\u0065C\u0065\u006c\u006c\u0041\u006e\u0063\u0068\u006f\u0072",NewCT_OneCellAnchor );
_a .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067","\u0043\u0054\u005f\u0041\u0062\u0073\u006f\u006c\u0075\u0074\u0065\u0041n\u0063\u0068\u006f\u0072",NewCT_AbsoluteAnchor );
_a .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067","\u0043\u0054\u005f\u0044\u0072\u0061\u0077\u0069\u006e\u0067",NewCT_Drawing );
_a .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067","\u0066\u0072\u006f\u006d",NewFrom );
_a .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067","\u0074\u006f",NewTo );
_a .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067","\u0077\u0073\u0044\u0072",NewWsDr );
_a .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067","\u0045\u0047_\u004f\u0062\u006ae\u0063\u0074\u0043\u0068\u006f\u0069\u0063\u0065\u0073",NewEG_ObjectChoices );
_a .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0070\u0072\u0065\u0061d\u0073\u0068\u0065\u0065\u0074\u0044\u0072\u0061w\u0069\u006e\u0067","\u0045G\u005f\u0041\u006e\u0063\u0068\u006fr",NewEG_Anchor );
};