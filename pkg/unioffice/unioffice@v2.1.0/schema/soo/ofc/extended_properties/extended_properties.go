//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package extended_properties ;import (_a "encoding/xml";_ac "github.com/unidoc/unioffice/v2";_b "github.com/unidoc/unioffice/v2/common/logger";_cb "github.com/unidoc/unioffice/v2/schema/soo/ofc/docPropsVTypes";);func (_ab *CT_DigSigBlob )MarshalXML (e *_a .Encoder ,start _a .StartElement )error {e .EncodeToken (start );
_ce :=_a .StartElement {Name :_a .Name {Local :"\u0076t\u003a\u0062\u006c\u006f\u0062"}};_ac .AddPreserveSpaceAttr (&_ce ,_ab .Blob );e .EncodeElement (_ab .Blob ,_ce );e .EncodeToken (_a .EndElement {Name :start .Name });return nil ;};func (_cda *Properties )MarshalXML (e *_a .Encoder ,start _a .StartElement )error {start .Attr =append (start .Attr ,_a .Attr {Name :_a .Name {Local :"\u0078\u006d\u006cn\u0073"},Value :"\u0068t\u0074p\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002eo\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006da\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0065\u0078\u0074\u0065n\u0064\u0065\u0064\u002d\u0070\u0072\u006f\u0070e\u0072\u0074\u0069\u0065\u0073"});
start .Attr =append (start .Attr ,_a .Attr {Name :_a .Name {Local :"\u0078\u006d\u006c\u006e\u0073\u003a\u0076\u0074"},Value :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073"});
start .Attr =append (start .Attr ,_a .Attr {Name :_a .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0078\u006dl"},Value :"\u0068\u0074tp\u003a\u002f\u002fw\u0077\u0077\u002e\u00773.o\u0072g/\u0058\u004d\u004c\u002f\u0031\u0039\u00398/\u006e\u0061\u006d\u0065\u0073\u0070\u0061c\u0065"});
start .Name .Local ="\u0050\u0072\u006f\u0070\u0065\u0072\u0074\u0069\u0065\u0073";return _cda .CT_Properties .MarshalXML (e ,start );};

// Validate validates the CT_VectorVariant and its children
func (_fec *CT_VectorVariant )Validate ()error {return _fec .ValidateWithPath ("\u0043\u0054_\u0056\u0065\u0063t\u006f\u0072\u0056\u0061\u0072\u0069\u0061\u006e\u0074");};type CT_VectorLpstr struct{Vector *_cb .Vector ;};func (_dbe *CT_VectorVariant )UnmarshalXML (d *_a .Decoder ,start _a .StartElement )error {_dbe .Vector =_cb .NewVector ();
_bgb :for {_bdg ,_daa :=d .Token ();if _daa !=nil {return _daa ;};switch _bfb :=_bdg .(type ){case _a .StartElement :switch _bfb .Name {case _a .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0076\u0065\u0063\u0074\u006f\u0072"},_a .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0076\u0065\u0063\u0074\u006f\u0072"}:if _fcb :=d .DecodeElement (_dbe .Vector ,&_bfb );
_fcb !=nil {return _fcb ;};default:_b .Log .Debug ("\u0073\u006b\u0069\u0070\u0070i\u006e\u0067\u0020\u0075\u006e\u0073\u0075\u0070\u0070\u006f\u0072\u0074\u0065d\u0020\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0043\u0054\u005f\u0056\u0065\u0063\u0074\u006f\u0072\u0056\u0061\u0072\u0069\u0061\u006e\u0074\u0020\u0025v",_bfb .Name );
if _cee :=d .Skip ();_cee !=nil {return _cee ;};};case _a .EndElement :break _bgb ;case _a .CharData :};};return nil ;};type CT_Properties struct{

// Name of Document Template
Template *string ;

// Name of Manager
Manager *string ;

// Name of Company
Company *string ;

// Total Number of Pages
Pages *int32 ;

// Word Count
Words *int32 ;

// Total Number of Characters
Characters *int32 ;

// Intended Format of Presentation
PresentationFormat *string ;

// Number of Lines
Lines *int32 ;

// Total Number of Paragraphs
Paragraphs *int32 ;

// Slides Metadata Element
Slides *int32 ;

// Number of Slides Containing Notes
Notes *int32 ;

// Total Edit Time Metadata Element
TotalTime *int32 ;

// Number of Hidden Slides
HiddenSlides *int32 ;

// Total Number of Multimedia Clips
MMClips *int32 ;

// Thumbnail Display Mode
ScaleCrop *bool ;

// Heading Pairs
HeadingPairs *CT_VectorVariant ;

// Part Titles
TitlesOfParts *CT_VectorLpstr ;

// Links Up-to-Date
LinksUpToDate *bool ;

// Number of Characters (With Spaces)
CharactersWithSpaces *int32 ;

// Shared Document
SharedDoc *bool ;

// Relative Hyperlink Base
HyperlinkBase *string ;

// Hyperlink List
HLinks *CT_VectorVariant ;

// Hyperlinks Changed
HyperlinksChanged *bool ;

// Digital Signature
DigSig *CT_DigSigBlob ;

// Application Name
Application *string ;

// Application Version
AppVersion *string ;

// Document Security
DocSecurity *int32 ;};

// Validate validates the CT_Properties and its children
func (_ecdc *CT_Properties )Validate ()error {return _ecdc .ValidateWithPath ("\u0043\u0054\u005f\u0050\u0072\u006f\u0070\u0065\u0072\u0074\u0069\u0065\u0073");};func NewCT_Properties ()*CT_Properties {_de :=&CT_Properties {};return _de };func (_ega *CT_VectorLpstr )MarshalXML (e *_a .Encoder ,start _a .StartElement )error {e .EncodeToken (start );
_aae :=_a .StartElement {Name :_a .Name {Local :"\u0076t\u003a\u0076\u0065\u0063\u0074\u006fr"}};e .EncodeElement (_ega .Vector ,_aae );e .EncodeToken (_a .EndElement {Name :start .Name });return nil ;};type CT_VectorVariant struct{Vector *_cb .Vector ;
};func NewCT_DigSigBlob ()*CT_DigSigBlob {_e :=&CT_DigSigBlob {};return _e };func NewProperties ()*Properties {_ed :=&Properties {};_ed .CT_Properties =*NewCT_Properties ();return _ed ;};

// ValidateWithPath validates the Properties and its children, prefixing error messages with path
func (_eee *Properties )ValidateWithPath (path string )error {if _fdf :=_eee .CT_Properties .ValidateWithPath (path );_fdf !=nil {return _fdf ;};return nil ;};

// ValidateWithPath validates the CT_VectorVariant and its children, prefixing error messages with path
func (_ffg *CT_VectorVariant )ValidateWithPath (path string )error {if _gbdf :=_ffg .Vector .ValidateWithPath (path +"\u002fV\u0065\u0063\u0074\u006f\u0072");_gbdf !=nil {return _gbdf ;};return nil ;};func (_aef *Properties )UnmarshalXML (d *_a .Decoder ,start _a .StartElement )error {_aef .CT_Properties =*NewCT_Properties ();
_cfe :for {_cg ,_ag :=d .Token ();if _ag !=nil {return _ag ;};switch _cca :=_cg .(type ){case _a .StartElement :switch _cca .Name {case _a .Name {Space :"\u0068t\u0074p\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002eo\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006da\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0065\u0078\u0074\u0065n\u0064\u0065\u0064\u002d\u0070\u0072\u006f\u0070e\u0072\u0074\u0069\u0065\u0073",Local :"\u0054\u0065\u006d\u0070\u006c\u0061\u0074\u0065"},_a .Name {Space :"ht\u0074\u0070:\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0065x\u0074e\u006e\u0064\u0065\u0064\u0050\u0072\u006f\u0070e\u0072t\u0069\u0065s",Local :"\u0054\u0065\u006d\u0070\u006c\u0061\u0074\u0065"}:_aef .Template =new (string );
if _ecef :=d .DecodeElement (_aef .Template ,&_cca );_ecef !=nil {return _ecef ;};case _a .Name {Space :"\u0068t\u0074p\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002eo\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006da\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0065\u0078\u0074\u0065n\u0064\u0065\u0064\u002d\u0070\u0072\u006f\u0070e\u0072\u0074\u0069\u0065\u0073",Local :"\u004da\u006e\u0061\u0067\u0065\u0072"},_a .Name {Space :"ht\u0074\u0070:\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0065x\u0074e\u006e\u0064\u0065\u0064\u0050\u0072\u006f\u0070e\u0072t\u0069\u0065s",Local :"\u004da\u006e\u0061\u0067\u0065\u0072"}:_aef .Manager =new (string );
if _gbde :=d .DecodeElement (_aef .Manager ,&_cca );_gbde !=nil {return _gbde ;};case _a .Name {Space :"\u0068t\u0074p\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002eo\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006da\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0065\u0078\u0074\u0065n\u0064\u0065\u0064\u002d\u0070\u0072\u006f\u0070e\u0072\u0074\u0069\u0065\u0073",Local :"\u0043o\u006d\u0070\u0061\u006e\u0079"},_a .Name {Space :"ht\u0074\u0070:\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0065x\u0074e\u006e\u0064\u0065\u0064\u0050\u0072\u006f\u0070e\u0072t\u0069\u0065s",Local :"\u0043o\u006d\u0070\u0061\u006e\u0079"}:_aef .Company =new (string );
if _dbc :=d .DecodeElement (_aef .Company ,&_cca );_dbc !=nil {return _dbc ;};case _a .Name {Space :"\u0068t\u0074p\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002eo\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006da\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0065\u0078\u0074\u0065n\u0064\u0065\u0064\u002d\u0070\u0072\u006f\u0070e\u0072\u0074\u0069\u0065\u0073",Local :"\u0050\u0061\u0067e\u0073"},_a .Name {Space :"ht\u0074\u0070:\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0065x\u0074e\u006e\u0064\u0065\u0064\u0050\u0072\u006f\u0070e\u0072t\u0069\u0065s",Local :"\u0050\u0061\u0067e\u0073"}:_aef .Pages =new (int32 );
if _abd :=d .DecodeElement (_aef .Pages ,&_cca );_abd !=nil {return _abd ;};case _a .Name {Space :"\u0068t\u0074p\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002eo\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006da\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0065\u0078\u0074\u0065n\u0064\u0065\u0064\u002d\u0070\u0072\u006f\u0070e\u0072\u0074\u0069\u0065\u0073",Local :"\u0057\u006f\u0072d\u0073"},_a .Name {Space :"ht\u0074\u0070:\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0065x\u0074e\u006e\u0064\u0065\u0064\u0050\u0072\u006f\u0070e\u0072t\u0069\u0065s",Local :"\u0057\u006f\u0072d\u0073"}:_aef .Words =new (int32 );
if _edb :=d .DecodeElement (_aef .Words ,&_cca );_edb !=nil {return _edb ;};case _a .Name {Space :"\u0068t\u0074p\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002eo\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006da\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0065\u0078\u0074\u0065n\u0064\u0065\u0064\u002d\u0070\u0072\u006f\u0070e\u0072\u0074\u0069\u0065\u0073",Local :"\u0043\u0068\u0061\u0072\u0061\u0063\u0074\u0065\u0072\u0073"},_a .Name {Space :"ht\u0074\u0070:\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0065x\u0074e\u006e\u0064\u0065\u0064\u0050\u0072\u006f\u0070e\u0072t\u0069\u0065s",Local :"\u0043\u0068\u0061\u0072\u0061\u0063\u0074\u0065\u0072\u0073"}:_aef .Characters =new (int32 );
if _bebd :=d .DecodeElement (_aef .Characters ,&_cca );_bebd !=nil {return _bebd ;};case _a .Name {Space :"\u0068t\u0074p\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002eo\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006da\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0065\u0078\u0074\u0065n\u0064\u0065\u0064\u002d\u0070\u0072\u006f\u0070e\u0072\u0074\u0069\u0065\u0073",Local :"\u0050r\u0065s\u0065\u006e\u0074\u0061\u0074i\u006f\u006eF\u006f\u0072\u006d\u0061\u0074"},_a .Name {Space :"ht\u0074\u0070:\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0065x\u0074e\u006e\u0064\u0065\u0064\u0050\u0072\u006f\u0070e\u0072t\u0069\u0065s",Local :"\u0050r\u0065s\u0065\u006e\u0074\u0061\u0074i\u006f\u006eF\u006f\u0072\u006d\u0061\u0074"}:_aef .PresentationFormat =new (string );
if _ccad :=d .DecodeElement (_aef .PresentationFormat ,&_cca );_ccad !=nil {return _ccad ;};case _a .Name {Space :"\u0068t\u0074p\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002eo\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006da\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0065\u0078\u0074\u0065n\u0064\u0065\u0064\u002d\u0070\u0072\u006f\u0070e\u0072\u0074\u0069\u0065\u0073",Local :"\u004c\u0069\u006ee\u0073"},_a .Name {Space :"ht\u0074\u0070:\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0065x\u0074e\u006e\u0064\u0065\u0064\u0050\u0072\u006f\u0070e\u0072t\u0069\u0065s",Local :"\u004c\u0069\u006ee\u0073"}:_aef .Lines =new (int32 );
if _eda :=d .DecodeElement (_aef .Lines ,&_cca );_eda !=nil {return _eda ;};case _a .Name {Space :"\u0068t\u0074p\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002eo\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006da\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0065\u0078\u0074\u0065n\u0064\u0065\u0064\u002d\u0070\u0072\u006f\u0070e\u0072\u0074\u0069\u0065\u0073",Local :"\u0050\u0061\u0072\u0061\u0067\u0072\u0061\u0070\u0068\u0073"},_a .Name {Space :"ht\u0074\u0070:\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0065x\u0074e\u006e\u0064\u0065\u0064\u0050\u0072\u006f\u0070e\u0072t\u0069\u0065s",Local :"\u0050\u0061\u0072\u0061\u0067\u0072\u0061\u0070\u0068\u0073"}:_aef .Paragraphs =new (int32 );
if _dga :=d .DecodeElement (_aef .Paragraphs ,&_cca );_dga !=nil {return _dga ;};case _a .Name {Space :"\u0068t\u0074p\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002eo\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006da\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0065\u0078\u0074\u0065n\u0064\u0065\u0064\u002d\u0070\u0072\u006f\u0070e\u0072\u0074\u0069\u0065\u0073",Local :"\u0053\u006c\u0069\u0064\u0065\u0073"},_a .Name {Space :"ht\u0074\u0070:\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0065x\u0074e\u006e\u0064\u0065\u0064\u0050\u0072\u006f\u0070e\u0072t\u0069\u0065s",Local :"\u0053\u006c\u0069\u0064\u0065\u0073"}:_aef .Slides =new (int32 );
if _ceeg :=d .DecodeElement (_aef .Slides ,&_cca );_ceeg !=nil {return _ceeg ;};case _a .Name {Space :"\u0068t\u0074p\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002eo\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006da\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0065\u0078\u0074\u0065n\u0064\u0065\u0064\u002d\u0070\u0072\u006f\u0070e\u0072\u0074\u0069\u0065\u0073",Local :"\u004e\u006f\u0074e\u0073"},_a .Name {Space :"ht\u0074\u0070:\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0065x\u0074e\u006e\u0064\u0065\u0064\u0050\u0072\u006f\u0070e\u0072t\u0069\u0065s",Local :"\u004e\u006f\u0074e\u0073"}:_aef .Notes =new (int32 );
if _daag :=d .DecodeElement (_aef .Notes ,&_cca );_daag !=nil {return _daag ;};case _a .Name {Space :"\u0068t\u0074p\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002eo\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006da\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0065\u0078\u0074\u0065n\u0064\u0065\u0064\u002d\u0070\u0072\u006f\u0070e\u0072\u0074\u0069\u0065\u0073",Local :"\u0054o\u0074\u0061\u006c\u0054\u0069\u006de"},_a .Name {Space :"ht\u0074\u0070:\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0065x\u0074e\u006e\u0064\u0065\u0064\u0050\u0072\u006f\u0070e\u0072t\u0069\u0065s",Local :"\u0054o\u0074\u0061\u006c\u0054\u0069\u006de"}:_aef .TotalTime =new (int32 );
if _ca :=d .DecodeElement (_aef .TotalTime ,&_cca );_ca !=nil {return _ca ;};case _a .Name {Space :"\u0068t\u0074p\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002eo\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006da\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0065\u0078\u0074\u0065n\u0064\u0065\u0064\u002d\u0070\u0072\u006f\u0070e\u0072\u0074\u0069\u0065\u0073",Local :"\u0048\u0069\u0064d\u0065\u006e\u0053\u006c\u0069\u0064\u0065\u0073"},_a .Name {Space :"ht\u0074\u0070:\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0065x\u0074e\u006e\u0064\u0065\u0064\u0050\u0072\u006f\u0070e\u0072t\u0069\u0065s",Local :"\u0048\u0069\u0064d\u0065\u006e\u0053\u006c\u0069\u0064\u0065\u0073"}:_aef .HiddenSlides =new (int32 );
if _gg :=d .DecodeElement (_aef .HiddenSlides ,&_cca );_gg !=nil {return _gg ;};case _a .Name {Space :"\u0068t\u0074p\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002eo\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006da\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0065\u0078\u0074\u0065n\u0064\u0065\u0064\u002d\u0070\u0072\u006f\u0070e\u0072\u0074\u0069\u0065\u0073",Local :"\u004dM\u0043\u006c\u0069\u0070\u0073"},_a .Name {Space :"ht\u0074\u0070:\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0065x\u0074e\u006e\u0064\u0065\u0064\u0050\u0072\u006f\u0070e\u0072t\u0069\u0065s",Local :"\u004dM\u0043\u006c\u0069\u0070\u0073"}:_aef .MMClips =new (int32 );
if _beg :=d .DecodeElement (_aef .MMClips ,&_cca );_beg !=nil {return _beg ;};case _a .Name {Space :"\u0068t\u0074p\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002eo\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006da\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0065\u0078\u0074\u0065n\u0064\u0065\u0064\u002d\u0070\u0072\u006f\u0070e\u0072\u0074\u0069\u0065\u0073",Local :"\u0053c\u0061\u006c\u0065\u0043\u0072\u006fp"},_a .Name {Space :"ht\u0074\u0070:\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0065x\u0074e\u006e\u0064\u0065\u0064\u0050\u0072\u006f\u0070e\u0072t\u0069\u0065s",Local :"\u0053c\u0061\u006c\u0065\u0043\u0072\u006fp"}:_aef .ScaleCrop =new (bool );
if _fecb :=d .DecodeElement (_aef .ScaleCrop ,&_cca );_fecb !=nil {return _fecb ;};case _a .Name {Space :"\u0068t\u0074p\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002eo\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006da\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0065\u0078\u0074\u0065n\u0064\u0065\u0064\u002d\u0070\u0072\u006f\u0070e\u0072\u0074\u0069\u0065\u0073",Local :"\u0048\u0065\u0061d\u0069\u006e\u0067\u0050\u0061\u0069\u0072\u0073"},_a .Name {Space :"ht\u0074\u0070:\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0065x\u0074e\u006e\u0064\u0065\u0064\u0050\u0072\u006f\u0070e\u0072t\u0069\u0065s",Local :"\u0048\u0065\u0061d\u0069\u006e\u0067\u0050\u0061\u0069\u0072\u0073"}:_aef .HeadingPairs =NewCT_VectorVariant ();
if _gbc :=d .DecodeElement (_aef .HeadingPairs ,&_cca );_gbc !=nil {return _gbc ;};case _a .Name {Space :"\u0068t\u0074p\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002eo\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006da\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0065\u0078\u0074\u0065n\u0064\u0065\u0064\u002d\u0070\u0072\u006f\u0070e\u0072\u0074\u0069\u0065\u0073",Local :"\u0054\u0069\u0074\u006c\u0065\u0073\u004f\u0066\u0050\u0061\u0072\u0074\u0073"},_a .Name {Space :"ht\u0074\u0070:\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0065x\u0074e\u006e\u0064\u0065\u0064\u0050\u0072\u006f\u0070e\u0072t\u0069\u0065s",Local :"\u0054\u0069\u0074\u006c\u0065\u0073\u004f\u0066\u0050\u0061\u0072\u0074\u0073"}:_aef .TitlesOfParts =NewCT_VectorLpstr ();
if _dda :=d .DecodeElement (_aef .TitlesOfParts ,&_cca );_dda !=nil {return _dda ;};case _a .Name {Space :"\u0068t\u0074p\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002eo\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006da\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0065\u0078\u0074\u0065n\u0064\u0065\u0064\u002d\u0070\u0072\u006f\u0070e\u0072\u0074\u0069\u0065\u0073",Local :"\u004c\u0069\u006e\u006b\u0073\u0055\u0070\u0054\u006f\u0044\u0061\u0074\u0065"},_a .Name {Space :"ht\u0074\u0070:\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0065x\u0074e\u006e\u0064\u0065\u0064\u0050\u0072\u006f\u0070e\u0072t\u0069\u0065s",Local :"\u004c\u0069\u006e\u006b\u0073\u0055\u0070\u0054\u006f\u0044\u0061\u0074\u0065"}:_aef .LinksUpToDate =new (bool );
if _ebe :=d .DecodeElement (_aef .LinksUpToDate ,&_cca );_ebe !=nil {return _ebe ;};case _a .Name {Space :"\u0068t\u0074p\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002eo\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006da\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0065\u0078\u0074\u0065n\u0064\u0065\u0064\u002d\u0070\u0072\u006f\u0070e\u0072\u0074\u0069\u0065\u0073",Local :"C\u0068a\u0072\u0061\u0063\u0074\u0065\u0072\u0073\u0057i\u0074\u0068\u0053\u0070ac\u0065\u0073"},_a .Name {Space :"ht\u0074\u0070:\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0065x\u0074e\u006e\u0064\u0065\u0064\u0050\u0072\u006f\u0070e\u0072t\u0069\u0065s",Local :"C\u0068a\u0072\u0061\u0063\u0074\u0065\u0072\u0073\u0057i\u0074\u0068\u0053\u0070ac\u0065\u0073"}:_aef .CharactersWithSpaces =new (int32 );
if _bdgg :=d .DecodeElement (_aef .CharactersWithSpaces ,&_cca );_bdgg !=nil {return _bdgg ;};case _a .Name {Space :"\u0068t\u0074p\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002eo\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006da\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0065\u0078\u0074\u0065n\u0064\u0065\u0064\u002d\u0070\u0072\u006f\u0070e\u0072\u0074\u0069\u0065\u0073",Local :"\u0053h\u0061\u0072\u0065\u0064\u0044\u006fc"},_a .Name {Space :"ht\u0074\u0070:\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0065x\u0074e\u006e\u0064\u0065\u0064\u0050\u0072\u006f\u0070e\u0072t\u0069\u0065s",Local :"\u0053h\u0061\u0072\u0065\u0064\u0044\u006fc"}:_aef .SharedDoc =new (bool );
if _aege :=d .DecodeElement (_aef .SharedDoc ,&_cca );_aege !=nil {return _aege ;};case _a .Name {Space :"\u0068t\u0074p\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002eo\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006da\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0065\u0078\u0074\u0065n\u0064\u0065\u0064\u002d\u0070\u0072\u006f\u0070e\u0072\u0074\u0069\u0065\u0073",Local :"\u0048\u0079\u0070\u0065\u0072\u006c\u0069\u006e\u006b\u0042\u0061\u0073\u0065"},_a .Name {Space :"ht\u0074\u0070:\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0065x\u0074e\u006e\u0064\u0065\u0064\u0050\u0072\u006f\u0070e\u0072t\u0069\u0065s",Local :"\u0048\u0079\u0070\u0065\u0072\u006c\u0069\u006e\u006b\u0042\u0061\u0073\u0065"}:_aef .HyperlinkBase =new (string );
if _bfaa :=d .DecodeElement (_aef .HyperlinkBase ,&_cca );_bfaa !=nil {return _bfaa ;};case _a .Name {Space :"\u0068t\u0074p\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002eo\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006da\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0065\u0078\u0074\u0065n\u0064\u0065\u0064\u002d\u0070\u0072\u006f\u0070e\u0072\u0074\u0069\u0065\u0073",Local :"\u0048\u004c\u0069\u006e\u006b\u0073"},_a .Name {Space :"ht\u0074\u0070:\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0065x\u0074e\u006e\u0064\u0065\u0064\u0050\u0072\u006f\u0070e\u0072t\u0069\u0065s",Local :"\u0048\u004c\u0069\u006e\u006b\u0073"}:_aef .HLinks =NewCT_VectorVariant ();
if _efc :=d .DecodeElement (_aef .HLinks ,&_cca );_efc !=nil {return _efc ;};case _a .Name {Space :"\u0068t\u0074p\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002eo\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006da\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0065\u0078\u0074\u0065n\u0064\u0065\u0064\u002d\u0070\u0072\u006f\u0070e\u0072\u0074\u0069\u0065\u0073",Local :"\u0048\u0079\u0070\u0065\u0072\u006c\u0069\u006e\u006b\u0073\u0043\u0068a\u006e\u0067\u0065\u0064"},_a .Name {Space :"ht\u0074\u0070:\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0065x\u0074e\u006e\u0064\u0065\u0064\u0050\u0072\u006f\u0070e\u0072t\u0069\u0065s",Local :"\u0048\u0079\u0070\u0065\u0072\u006c\u0069\u006e\u006b\u0073\u0043\u0068a\u006e\u0067\u0065\u0064"}:_aef .HyperlinksChanged =new (bool );
if _fdg :=d .DecodeElement (_aef .HyperlinksChanged ,&_cca );_fdg !=nil {return _fdg ;};case _a .Name {Space :"\u0068t\u0074p\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002eo\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006da\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0065\u0078\u0074\u0065n\u0064\u0065\u0064\u002d\u0070\u0072\u006f\u0070e\u0072\u0074\u0069\u0065\u0073",Local :"\u0044\u0069\u0067\u0053\u0069\u0067"},_a .Name {Space :"ht\u0074\u0070:\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0065x\u0074e\u006e\u0064\u0065\u0064\u0050\u0072\u006f\u0070e\u0072t\u0069\u0065s",Local :"\u0044\u0069\u0067\u0053\u0069\u0067"}:_aef .DigSig =NewCT_DigSigBlob ();
if _gc :=d .DecodeElement (_aef .DigSig ,&_cca );_gc !=nil {return _gc ;};case _a .Name {Space :"\u0068t\u0074p\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002eo\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006da\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0065\u0078\u0074\u0065n\u0064\u0065\u0064\u002d\u0070\u0072\u006f\u0070e\u0072\u0074\u0069\u0065\u0073",Local :"A\u0070\u0070\u006c\u0069\u0063\u0061\u0074\u0069\u006f\u006e"},_a .Name {Space :"ht\u0074\u0070:\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0065x\u0074e\u006e\u0064\u0065\u0064\u0050\u0072\u006f\u0070e\u0072t\u0069\u0065s",Local :"A\u0070\u0070\u006c\u0069\u0063\u0061\u0074\u0069\u006f\u006e"}:_aef .Application =new (string );
if _dbef :=d .DecodeElement (_aef .Application ,&_cca );_dbef !=nil {return _dbef ;};case _a .Name {Space :"\u0068t\u0074p\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002eo\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006da\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0065\u0078\u0074\u0065n\u0064\u0065\u0064\u002d\u0070\u0072\u006f\u0070e\u0072\u0074\u0069\u0065\u0073",Local :"\u0041\u0070\u0070\u0056\u0065\u0072\u0073\u0069\u006f\u006e"},_a .Name {Space :"ht\u0074\u0070:\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0065x\u0074e\u006e\u0064\u0065\u0064\u0050\u0072\u006f\u0070e\u0072t\u0069\u0065s",Local :"\u0041\u0070\u0070\u0056\u0065\u0072\u0073\u0069\u006f\u006e"}:_aef .AppVersion =new (string );
if _ceb :=d .DecodeElement (_aef .AppVersion ,&_cca );_ceb !=nil {return _ceb ;};case _a .Name {Space :"\u0068t\u0074p\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002eo\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006da\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0065\u0078\u0074\u0065n\u0064\u0065\u0064\u002d\u0070\u0072\u006f\u0070e\u0072\u0074\u0069\u0065\u0073",Local :"D\u006f\u0063\u0053\u0065\u0063\u0075\u0072\u0069\u0074\u0079"},_a .Name {Space :"ht\u0074\u0070:\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0065x\u0074e\u006e\u0064\u0065\u0064\u0050\u0072\u006f\u0070e\u0072t\u0069\u0065s",Local :"D\u006f\u0063\u0053\u0065\u0063\u0075\u0072\u0069\u0074\u0079"}:_aef .DocSecurity =new (int32 );
if _bge :=d .DecodeElement (_aef .DocSecurity ,&_cca );_bge !=nil {return _bge ;};default:_b .Log .Debug ("\u0073k\u0069\u0070p\u0069\u006e\u0067 \u0075\u006e\u0073\u0075\u0070\u0070\u006fr\u0074\u0065\u0064\u0020\u0065\u006ce\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0050\u0072\u006fp\u0065\u0072\u0074\u0069\u0065\u0073\u0020\u0025\u0076",_cca .Name );
if _cbg :=d .Skip ();_cbg !=nil {return _cbg ;};};case _a .EndElement :break _cfe ;case _a .CharData :};};return nil ;};

// ValidateWithPath validates the CT_VectorLpstr and its children, prefixing error messages with path
func (_cfa *CT_VectorLpstr )ValidateWithPath (path string )error {if _dcf :=_cfa .Vector .ValidateWithPath (path +"\u002fV\u0065\u0063\u0074\u006f\u0072");_dcf !=nil {return _dcf ;};return nil ;};

// ValidateWithPath validates the CT_Properties and its children, prefixing error messages with path
func (_fee *CT_Properties )ValidateWithPath (path string )error {if _fee .HeadingPairs !=nil {if _db :=_fee .HeadingPairs .ValidateWithPath (path +"\u002f\u0048\u0065\u0061\u0064\u0069\u006e\u0067\u0050\u0061\u0069\u0072\u0073");_db !=nil {return _db ;
};};if _fee .TitlesOfParts !=nil {if _cbb :=_fee .TitlesOfParts .ValidateWithPath (path +"\u002f\u0054\u0069\u0074\u006c\u0065\u0073\u004f\u0066P\u0061\u0072\u0074\u0073");_cbb !=nil {return _cbb ;};};if _fee .HLinks !=nil {if _aca :=_fee .HLinks .ValidateWithPath (path +"\u002fH\u004c\u0069\u006e\u006b\u0073");
_aca !=nil {return _aca ;};};if _fee .DigSig !=nil {if _ebbg :=_fee .DigSig .ValidateWithPath (path +"\u002fD\u0069\u0067\u0053\u0069\u0067");_ebbg !=nil {return _ebbg ;};};return nil ;};func NewCT_VectorLpstr ()*CT_VectorLpstr {_ee :=&CT_VectorLpstr {};
_ee .Vector =_cb .NewVector ();return _ee ;};func (_gdg *CT_VectorVariant )MarshalXML (e *_a .Encoder ,start _a .StartElement )error {e .EncodeToken (start );_egd :=_a .StartElement {Name :_a .Name {Local :"\u0076t\u003a\u0076\u0065\u0063\u0074\u006fr"}};
e .EncodeElement (_gdg .Vector ,_egd );e .EncodeToken (_a .EndElement {Name :start .Name });return nil ;};

// Validate validates the CT_DigSigBlob and its children
func (_d *CT_DigSigBlob )Validate ()error {return _d .ValidateWithPath ("\u0043\u0054\u005f\u0044\u0069\u0067\u0053\u0069\u0067\u0042\u006c\u006f\u0062");};func (_eb *CT_DigSigBlob )UnmarshalXML (d *_a .Decoder ,start _a .StartElement )error {_f :for {_ec ,_cc :=d .Token ();
if _cc !=nil {return _cc ;};switch _g :=_ec .(type ){case _a .StartElement :switch _g .Name {case _a .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0062\u006c\u006f\u0062"},_a .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0062\u006c\u006f\u0062"}:if _eg :=d .DecodeElement (&_eb .Blob ,&_g );
_eg !=nil {return _eg ;};default:_b .Log .Debug ("\u0073\u006b\u0069\u0070\u0070\u0069\u006e\u0067 \u0075\u006e\u0073up\u0070\u006f\u0072\u0074\u0065\u0064 \u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0043\u0054\u005f\u0044i\u0067\u0053\u0069\u0067\u0042\u006c\u006f\u0062 \u0025\u0076",_g .Name );
if _af :=d .Skip ();_af !=nil {return _af ;};};case _a .EndElement :break _f ;case _a .CharData :};};return nil ;};func (_fbe *CT_VectorLpstr )UnmarshalXML (d *_a .Decoder ,start _a .StartElement )error {_fbe .Vector =_cb .NewVector ();_egg :for {_acaa ,_gda :=d .Token ();
if _gda !=nil {return _gda ;};switch _egc :=_acaa .(type ){case _a .StartElement :switch _egc .Name {case _a .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0076\u0065\u0063\u0074\u006f\u0072"},_a .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0076\u0065\u0063\u0074\u006f\u0072"}:if _adc :=d .DecodeElement (_fbe .Vector ,&_egc );
_adc !=nil {return _adc ;};default:_b .Log .Debug ("\u0073\u006b\u0069\u0070\u0070\u0069n\u0067\u0020\u0075\u006e\u0073\u0075\u0070\u0070\u006f\u0072\u0074\u0065\u0064\u0020\u0065\u006c\u0065\u006d\u0065\u006et\u0020\u006f\u006e\u0020\u0043\u0054\u005f\u0056\u0065\u0063\u0074\u006f\u0072\u004cp\u0073t\u0072\u0020\u0025\u0076",_egc .Name );
if _aed :=d .Skip ();_aed !=nil {return _aed ;};};case _a .EndElement :break _egg ;case _a .CharData :};};return nil ;};func (_gd *CT_Properties )MarshalXML (e *_a .Encoder ,start _a .StartElement )error {e .EncodeToken (start );if _gd .Template !=nil {_fa :=_a .StartElement {Name :_a .Name {Local :"\u0054\u0065\u006d\u0070\u006c\u0061\u0074\u0065"}};
_ac .AddPreserveSpaceAttr (&_fa ,*_gd .Template );e .EncodeElement (_gd .Template ,_fa );};if _gd .Manager !=nil {_df :=_a .StartElement {Name :_a .Name {Local :"\u004da\u006e\u0061\u0067\u0065\u0072"}};_ac .AddPreserveSpaceAttr (&_df ,*_gd .Manager );
e .EncodeElement (_gd .Manager ,_df );};if _gd .Company !=nil {_bg :=_a .StartElement {Name :_a .Name {Local :"\u0043o\u006d\u0070\u0061\u006e\u0079"}};_ac .AddPreserveSpaceAttr (&_bg ,*_gd .Company );e .EncodeElement (_gd .Company ,_bg );};if _gd .Pages !=nil {_ga :=_a .StartElement {Name :_a .Name {Local :"\u0050\u0061\u0067e\u0073"}};
e .EncodeElement (_gd .Pages ,_ga );};if _gd .Words !=nil {_ae :=_a .StartElement {Name :_a .Name {Local :"\u0057\u006f\u0072d\u0073"}};e .EncodeElement (_gd .Words ,_ae );};if _gd .Characters !=nil {_da :=_a .StartElement {Name :_a .Name {Local :"\u0043\u0068\u0061\u0072\u0061\u0063\u0074\u0065\u0072\u0073"}};
e .EncodeElement (_gd .Characters ,_da );};if _gd .PresentationFormat !=nil {_cf :=_a .StartElement {Name :_a .Name {Local :"\u0050r\u0065s\u0065\u006e\u0074\u0061\u0074i\u006f\u006eF\u006f\u0072\u006d\u0061\u0074"}};_ac .AddPreserveSpaceAttr (&_cf ,*_gd .PresentationFormat );
e .EncodeElement (_gd .PresentationFormat ,_cf );};if _gd .Lines !=nil {_dd :=_a .StartElement {Name :_a .Name {Local :"\u004c\u0069\u006ee\u0073"}};e .EncodeElement (_gd .Lines ,_dd );};if _gd .Paragraphs !=nil {_fag :=_a .StartElement {Name :_a .Name {Local :"\u0050\u0061\u0072\u0061\u0067\u0072\u0061\u0070\u0068\u0073"}};
e .EncodeElement (_gd .Paragraphs ,_fag );};if _gd .Slides !=nil {_gb :=_a .StartElement {Name :_a .Name {Local :"\u0053\u006c\u0069\u0064\u0065\u0073"}};e .EncodeElement (_gd .Slides ,_gb );};if _gd .Notes !=nil {_ge :=_a .StartElement {Name :_a .Name {Local :"\u004e\u006f\u0074e\u0073"}};
e .EncodeElement (_gd .Notes ,_ge );};if _gd .TotalTime !=nil {_ccb :=_a .StartElement {Name :_a .Name {Local :"\u0054o\u0074\u0061\u006c\u0054\u0069\u006de"}};e .EncodeElement (_gd .TotalTime ,_ccb );};if _gd .HiddenSlides !=nil {_gbb :=_a .StartElement {Name :_a .Name {Local :"\u0048\u0069\u0064d\u0065\u006e\u0053\u006c\u0069\u0064\u0065\u0073"}};
e .EncodeElement (_gd .HiddenSlides ,_gbb );};if _gd .MMClips !=nil {_be :=_a .StartElement {Name :_a .Name {Local :"\u004dM\u0043\u006c\u0069\u0070\u0073"}};e .EncodeElement (_gd .MMClips ,_be );};if _gd .ScaleCrop !=nil {_gbd :=_a .StartElement {Name :_a .Name {Local :"\u0053c\u0061\u006c\u0065\u0043\u0072\u006fp"}};
e .EncodeElement (_gd .ScaleCrop ,_gbd );};if _gd .HeadingPairs !=nil {_fd :=_a .StartElement {Name :_a .Name {Local :"\u0048\u0065\u0061d\u0069\u006e\u0067\u0050\u0061\u0069\u0072\u0073"}};e .EncodeElement (_gd .HeadingPairs ,_fd );};if _gd .TitlesOfParts !=nil {_ad :=_a .StartElement {Name :_a .Name {Local :"\u0054\u0069\u0074\u006c\u0065\u0073\u004f\u0066\u0050\u0061\u0072\u0074\u0073"}};
e .EncodeElement (_gd .TitlesOfParts ,_ad );};if _gd .LinksUpToDate !=nil {_fg :=_a .StartElement {Name :_a .Name {Local :"\u004c\u0069\u006e\u006b\u0073\u0055\u0070\u0054\u006f\u0044\u0061\u0074\u0065"}};e .EncodeElement (_gd .LinksUpToDate ,_fg );};if _gd .CharactersWithSpaces !=nil {_ff :=_a .StartElement {Name :_a .Name {Local :"C\u0068a\u0072\u0061\u0063\u0074\u0065\u0072\u0073\u0057i\u0074\u0068\u0053\u0070ac\u0065\u0073"}};
e .EncodeElement (_gd .CharactersWithSpaces ,_ff );};if _gd .SharedDoc !=nil {_ege :=_a .StartElement {Name :_a .Name {Local :"\u0053h\u0061\u0072\u0065\u0064\u0044\u006fc"}};e .EncodeElement (_gd .SharedDoc ,_ege );};if _gd .HyperlinkBase !=nil {_ef :=_a .StartElement {Name :_a .Name {Local :"\u0048\u0079\u0070\u0065\u0072\u006c\u0069\u006e\u006b\u0042\u0061\u0073\u0065"}};
_ac .AddPreserveSpaceAttr (&_ef ,*_gd .HyperlinkBase );e .EncodeElement (_gd .HyperlinkBase ,_ef );};if _gd .HLinks !=nil {_bc :=_a .StartElement {Name :_a .Name {Local :"\u0048\u004c\u0069\u006e\u006b\u0073"}};e .EncodeElement (_gd .HLinks ,_bc );};if _gd .HyperlinksChanged !=nil {_cea :=_a .StartElement {Name :_a .Name {Local :"\u0048\u0079\u0070\u0065\u0072\u006c\u0069\u006e\u006b\u0073\u0043\u0068a\u006e\u0067\u0065\u0064"}};
e .EncodeElement (_gd .HyperlinksChanged ,_cea );};if _gd .DigSig !=nil {_ecg :=_a .StartElement {Name :_a .Name {Local :"\u0044\u0069\u0067\u0053\u0069\u0067"}};e .EncodeElement (_gd .DigSig ,_ecg );};if _gd .Application !=nil {_bd :=_a .StartElement {Name :_a .Name {Local :"A\u0070\u0070\u006c\u0069\u0063\u0061\u0074\u0069\u006f\u006e"}};
_ac .AddPreserveSpaceAttr (&_bd ,*_gd .Application );e .EncodeElement (_gd .Application ,_bd );};if _gd .AppVersion !=nil {_cd :=_a .StartElement {Name :_a .Name {Local :"\u0041\u0070\u0070\u0056\u0065\u0072\u0073\u0069\u006f\u006e"}};_ac .AddPreserveSpaceAttr (&_cd ,*_gd .AppVersion );
e .EncodeElement (_gd .AppVersion ,_cd );};if _gd .DocSecurity !=nil {_cef :=_a .StartElement {Name :_a .Name {Local :"D\u006f\u0063\u0053\u0065\u0063\u0075\u0072\u0069\u0074\u0079"}};e .EncodeElement (_gd .DocSecurity ,_cef );};e .EncodeToken (_a .EndElement {Name :start .Name });
return nil ;};type CT_DigSigBlob struct{Blob string ;};func (_fc *CT_Properties )UnmarshalXML (d *_a .Decoder ,start _a .StartElement )error {_dc :for {_cdg ,_ebd :=d .Token ();if _ebd !=nil {return _ebd ;};switch _aeb :=_cdg .(type ){case _a .StartElement :switch _aeb .Name {case _a .Name {Space :"\u0068t\u0074p\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002eo\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006da\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0065\u0078\u0074\u0065n\u0064\u0065\u0064\u002d\u0070\u0072\u006f\u0070e\u0072\u0074\u0069\u0065\u0073",Local :"\u0054\u0065\u006d\u0070\u006c\u0061\u0074\u0065"},_a .Name {Space :"ht\u0074\u0070:\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0065x\u0074e\u006e\u0064\u0065\u0064\u0050\u0072\u006f\u0070e\u0072t\u0069\u0065s",Local :"\u0054\u0065\u006d\u0070\u006c\u0061\u0074\u0065"}:_fc .Template =new (string );
if _bb :=d .DecodeElement (_fc .Template ,&_aeb );_bb !=nil {return _bb ;};case _a .Name {Space :"\u0068t\u0074p\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002eo\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006da\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0065\u0078\u0074\u0065n\u0064\u0065\u0064\u002d\u0070\u0072\u006f\u0070e\u0072\u0074\u0069\u0065\u0073",Local :"\u004da\u006e\u0061\u0067\u0065\u0072"},_a .Name {Space :"ht\u0074\u0070:\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0065x\u0074e\u006e\u0064\u0065\u0064\u0050\u0072\u006f\u0070e\u0072t\u0069\u0065s",Local :"\u004da\u006e\u0061\u0067\u0065\u0072"}:_fc .Manager =new (string );
if _bf :=d .DecodeElement (_fc .Manager ,&_aeb );_bf !=nil {return _bf ;};case _a .Name {Space :"\u0068t\u0074p\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002eo\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006da\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0065\u0078\u0074\u0065n\u0064\u0065\u0064\u002d\u0070\u0072\u006f\u0070e\u0072\u0074\u0069\u0065\u0073",Local :"\u0043o\u006d\u0070\u0061\u006e\u0079"},_a .Name {Space :"ht\u0074\u0070:\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0065x\u0074e\u006e\u0064\u0065\u0064\u0050\u0072\u006f\u0070e\u0072t\u0069\u0065s",Local :"\u0043o\u006d\u0070\u0061\u006e\u0079"}:_fc .Company =new (string );
if _bdf :=d .DecodeElement (_fc .Company ,&_aeb );_bdf !=nil {return _bdf ;};case _a .Name {Space :"\u0068t\u0074p\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002eo\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006da\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0065\u0078\u0074\u0065n\u0064\u0065\u0064\u002d\u0070\u0072\u006f\u0070e\u0072\u0074\u0069\u0065\u0073",Local :"\u0050\u0061\u0067e\u0073"},_a .Name {Space :"ht\u0074\u0070:\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0065x\u0074e\u006e\u0064\u0065\u0064\u0050\u0072\u006f\u0070e\u0072t\u0069\u0065s",Local :"\u0050\u0061\u0067e\u0073"}:_fc .Pages =new (int32 );
if _fge :=d .DecodeElement (_fc .Pages ,&_aeb );_fge !=nil {return _fge ;};case _a .Name {Space :"\u0068t\u0074p\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002eo\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006da\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0065\u0078\u0074\u0065n\u0064\u0065\u0064\u002d\u0070\u0072\u006f\u0070e\u0072\u0074\u0069\u0065\u0073",Local :"\u0057\u006f\u0072d\u0073"},_a .Name {Space :"ht\u0074\u0070:\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0065x\u0074e\u006e\u0064\u0065\u0064\u0050\u0072\u006f\u0070e\u0072t\u0069\u0065s",Local :"\u0057\u006f\u0072d\u0073"}:_fc .Words =new (int32 );
if _ecd :=d .DecodeElement (_fc .Words ,&_aeb );_ecd !=nil {return _ecd ;};case _a .Name {Space :"\u0068t\u0074p\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002eo\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006da\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0065\u0078\u0074\u0065n\u0064\u0065\u0064\u002d\u0070\u0072\u006f\u0070e\u0072\u0074\u0069\u0065\u0073",Local :"\u0043\u0068\u0061\u0072\u0061\u0063\u0074\u0065\u0072\u0073"},_a .Name {Space :"ht\u0074\u0070:\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0065x\u0074e\u006e\u0064\u0065\u0064\u0050\u0072\u006f\u0070e\u0072t\u0069\u0065s",Local :"\u0043\u0068\u0061\u0072\u0061\u0063\u0074\u0065\u0072\u0073"}:_fc .Characters =new (int32 );
if _ece :=d .DecodeElement (_fc .Characters ,&_aeb );_ece !=nil {return _ece ;};case _a .Name {Space :"\u0068t\u0074p\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002eo\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006da\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0065\u0078\u0074\u0065n\u0064\u0065\u0064\u002d\u0070\u0072\u006f\u0070e\u0072\u0074\u0069\u0065\u0073",Local :"\u0050r\u0065s\u0065\u006e\u0074\u0061\u0074i\u006f\u006eF\u006f\u0072\u006d\u0061\u0074"},_a .Name {Space :"ht\u0074\u0070:\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0065x\u0074e\u006e\u0064\u0065\u0064\u0050\u0072\u006f\u0070e\u0072t\u0069\u0065s",Local :"\u0050r\u0065s\u0065\u006e\u0074\u0061\u0074i\u006f\u006eF\u006f\u0072\u006d\u0061\u0074"}:_fc .PresentationFormat =new (string );
if _bde :=d .DecodeElement (_fc .PresentationFormat ,&_aeb );_bde !=nil {return _bde ;};case _a .Name {Space :"\u0068t\u0074p\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002eo\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006da\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0065\u0078\u0074\u0065n\u0064\u0065\u0064\u002d\u0070\u0072\u006f\u0070e\u0072\u0074\u0069\u0065\u0073",Local :"\u004c\u0069\u006ee\u0073"},_a .Name {Space :"ht\u0074\u0070:\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0065x\u0074e\u006e\u0064\u0065\u0064\u0050\u0072\u006f\u0070e\u0072t\u0069\u0065s",Local :"\u004c\u0069\u006ee\u0073"}:_fc .Lines =new (int32 );
if _afe :=d .DecodeElement (_fc .Lines ,&_aeb );_afe !=nil {return _afe ;};case _a .Name {Space :"\u0068t\u0074p\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002eo\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006da\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0065\u0078\u0074\u0065n\u0064\u0065\u0064\u002d\u0070\u0072\u006f\u0070e\u0072\u0074\u0069\u0065\u0073",Local :"\u0050\u0061\u0072\u0061\u0067\u0072\u0061\u0070\u0068\u0073"},_a .Name {Space :"ht\u0074\u0070:\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0065x\u0074e\u006e\u0064\u0065\u0064\u0050\u0072\u006f\u0070e\u0072t\u0069\u0065s",Local :"\u0050\u0061\u0072\u0061\u0067\u0072\u0061\u0070\u0068\u0073"}:_fc .Paragraphs =new (int32 );
if _eceg :=d .DecodeElement (_fc .Paragraphs ,&_aeb );_eceg !=nil {return _eceg ;};case _a .Name {Space :"\u0068t\u0074p\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002eo\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006da\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0065\u0078\u0074\u0065n\u0064\u0065\u0064\u002d\u0070\u0072\u006f\u0070e\u0072\u0074\u0069\u0065\u0073",Local :"\u0053\u006c\u0069\u0064\u0065\u0073"},_a .Name {Space :"ht\u0074\u0070:\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0065x\u0074e\u006e\u0064\u0065\u0064\u0050\u0072\u006f\u0070e\u0072t\u0069\u0065s",Local :"\u0053\u006c\u0069\u0064\u0065\u0073"}:_fc .Slides =new (int32 );
if _ea :=d .DecodeElement (_fc .Slides ,&_aeb );_ea !=nil {return _ea ;};case _a .Name {Space :"\u0068t\u0074p\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002eo\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006da\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0065\u0078\u0074\u0065n\u0064\u0065\u0064\u002d\u0070\u0072\u006f\u0070e\u0072\u0074\u0069\u0065\u0073",Local :"\u004e\u006f\u0074e\u0073"},_a .Name {Space :"ht\u0074\u0070:\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0065x\u0074e\u006e\u0064\u0065\u0064\u0050\u0072\u006f\u0070e\u0072t\u0069\u0065s",Local :"\u004e\u006f\u0074e\u0073"}:_fc .Notes =new (int32 );
if _aea :=d .DecodeElement (_fc .Notes ,&_aeb );_aea !=nil {return _aea ;};case _a .Name {Space :"\u0068t\u0074p\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002eo\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006da\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0065\u0078\u0074\u0065n\u0064\u0065\u0064\u002d\u0070\u0072\u006f\u0070e\u0072\u0074\u0069\u0065\u0073",Local :"\u0054o\u0074\u0061\u006c\u0054\u0069\u006de"},_a .Name {Space :"ht\u0074\u0070:\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0065x\u0074e\u006e\u0064\u0065\u0064\u0050\u0072\u006f\u0070e\u0072t\u0069\u0065s",Local :"\u0054o\u0074\u0061\u006c\u0054\u0069\u006de"}:_fc .TotalTime =new (int32 );
if _ddd :=d .DecodeElement (_fc .TotalTime ,&_aeb );_ddd !=nil {return _ddd ;};case _a .Name {Space :"\u0068t\u0074p\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002eo\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006da\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0065\u0078\u0074\u0065n\u0064\u0065\u0064\u002d\u0070\u0072\u006f\u0070e\u0072\u0074\u0069\u0065\u0073",Local :"\u0048\u0069\u0064d\u0065\u006e\u0053\u006c\u0069\u0064\u0065\u0073"},_a .Name {Space :"ht\u0074\u0070:\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0065x\u0074e\u006e\u0064\u0065\u0064\u0050\u0072\u006f\u0070e\u0072t\u0069\u0065s",Local :"\u0048\u0069\u0064d\u0065\u006e\u0053\u006c\u0069\u0064\u0065\u0073"}:_fc .HiddenSlides =new (int32 );
if _aa :=d .DecodeElement (_fc .HiddenSlides ,&_aeb );_aa !=nil {return _aa ;};case _a .Name {Space :"\u0068t\u0074p\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002eo\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006da\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0065\u0078\u0074\u0065n\u0064\u0065\u0064\u002d\u0070\u0072\u006f\u0070e\u0072\u0074\u0069\u0065\u0073",Local :"\u004dM\u0043\u006c\u0069\u0070\u0073"},_a .Name {Space :"ht\u0074\u0070:\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0065x\u0074e\u006e\u0064\u0065\u0064\u0050\u0072\u006f\u0070e\u0072t\u0069\u0065s",Local :"\u004dM\u0043\u006c\u0069\u0070\u0073"}:_fc .MMClips =new (int32 );
if _dfd :=d .DecodeElement (_fc .MMClips ,&_aeb );_dfd !=nil {return _dfd ;};case _a .Name {Space :"\u0068t\u0074p\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002eo\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006da\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0065\u0078\u0074\u0065n\u0064\u0065\u0064\u002d\u0070\u0072\u006f\u0070e\u0072\u0074\u0069\u0065\u0073",Local :"\u0053c\u0061\u006c\u0065\u0043\u0072\u006fp"},_a .Name {Space :"ht\u0074\u0070:\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0065x\u0074e\u006e\u0064\u0065\u0064\u0050\u0072\u006f\u0070e\u0072t\u0069\u0065s",Local :"\u0053c\u0061\u006c\u0065\u0043\u0072\u006fp"}:_fc .ScaleCrop =new (bool );
if _fe :=d .DecodeElement (_fc .ScaleCrop ,&_aeb );_fe !=nil {return _fe ;};case _a .Name {Space :"\u0068t\u0074p\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002eo\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006da\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0065\u0078\u0074\u0065n\u0064\u0065\u0064\u002d\u0070\u0072\u006f\u0070e\u0072\u0074\u0069\u0065\u0073",Local :"\u0048\u0065\u0061d\u0069\u006e\u0067\u0050\u0061\u0069\u0072\u0073"},_a .Name {Space :"ht\u0074\u0070:\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0065x\u0074e\u006e\u0064\u0065\u0064\u0050\u0072\u006f\u0070e\u0072t\u0069\u0065s",Local :"\u0048\u0065\u0061d\u0069\u006e\u0067\u0050\u0061\u0069\u0072\u0073"}:_fc .HeadingPairs =NewCT_VectorVariant ();
if _beb :=d .DecodeElement (_fc .HeadingPairs ,&_aeb );_beb !=nil {return _beb ;};case _a .Name {Space :"\u0068t\u0074p\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002eo\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006da\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0065\u0078\u0074\u0065n\u0064\u0065\u0064\u002d\u0070\u0072\u006f\u0070e\u0072\u0074\u0069\u0065\u0073",Local :"\u0054\u0069\u0074\u006c\u0065\u0073\u004f\u0066\u0050\u0061\u0072\u0074\u0073"},_a .Name {Space :"ht\u0074\u0070:\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0065x\u0074e\u006e\u0064\u0065\u0064\u0050\u0072\u006f\u0070e\u0072t\u0069\u0065s",Local :"\u0054\u0069\u0074\u006c\u0065\u0073\u004f\u0066\u0050\u0061\u0072\u0074\u0073"}:_fc .TitlesOfParts =NewCT_VectorLpstr ();
if _ebb :=d .DecodeElement (_fc .TitlesOfParts ,&_aeb );_ebb !=nil {return _ebb ;};case _a .Name {Space :"\u0068t\u0074p\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002eo\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006da\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0065\u0078\u0074\u0065n\u0064\u0065\u0064\u002d\u0070\u0072\u006f\u0070e\u0072\u0074\u0069\u0065\u0073",Local :"\u004c\u0069\u006e\u006b\u0073\u0055\u0070\u0054\u006f\u0044\u0061\u0074\u0065"},_a .Name {Space :"ht\u0074\u0070:\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0065x\u0074e\u006e\u0064\u0065\u0064\u0050\u0072\u006f\u0070e\u0072t\u0069\u0065s",Local :"\u004c\u0069\u006e\u006b\u0073\u0055\u0070\u0054\u006f\u0044\u0061\u0074\u0065"}:_fc .LinksUpToDate =new (bool );
if _aeg :=d .DecodeElement (_fc .LinksUpToDate ,&_aeb );_aeg !=nil {return _aeg ;};case _a .Name {Space :"\u0068t\u0074p\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002eo\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006da\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0065\u0078\u0074\u0065n\u0064\u0065\u0064\u002d\u0070\u0072\u006f\u0070e\u0072\u0074\u0069\u0065\u0073",Local :"C\u0068a\u0072\u0061\u0063\u0074\u0065\u0072\u0073\u0057i\u0074\u0068\u0053\u0070ac\u0065\u0073"},_a .Name {Space :"ht\u0074\u0070:\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0065x\u0074e\u006e\u0064\u0065\u0064\u0050\u0072\u006f\u0070e\u0072t\u0069\u0065s",Local :"C\u0068a\u0072\u0061\u0063\u0074\u0065\u0072\u0073\u0057i\u0074\u0068\u0053\u0070ac\u0065\u0073"}:_fc .CharactersWithSpaces =new (int32 );
if _dec :=d .DecodeElement (_fc .CharactersWithSpaces ,&_aeb );_dec !=nil {return _dec ;};case _a .Name {Space :"\u0068t\u0074p\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002eo\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006da\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0065\u0078\u0074\u0065n\u0064\u0065\u0064\u002d\u0070\u0072\u006f\u0070e\u0072\u0074\u0069\u0065\u0073",Local :"\u0053h\u0061\u0072\u0065\u0064\u0044\u006fc"},_a .Name {Space :"ht\u0074\u0070:\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0065x\u0074e\u006e\u0064\u0065\u0064\u0050\u0072\u006f\u0070e\u0072t\u0069\u0065s",Local :"\u0053h\u0061\u0072\u0065\u0064\u0044\u006fc"}:_fc .SharedDoc =new (bool );
if _bfd :=d .DecodeElement (_fc .SharedDoc ,&_aeb );_bfd !=nil {return _bfd ;};case _a .Name {Space :"\u0068t\u0074p\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002eo\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006da\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0065\u0078\u0074\u0065n\u0064\u0065\u0064\u002d\u0070\u0072\u006f\u0070e\u0072\u0074\u0069\u0065\u0073",Local :"\u0048\u0079\u0070\u0065\u0072\u006c\u0069\u006e\u006b\u0042\u0061\u0073\u0065"},_a .Name {Space :"ht\u0074\u0070:\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0065x\u0074e\u006e\u0064\u0065\u0064\u0050\u0072\u006f\u0070e\u0072t\u0069\u0065s",Local :"\u0048\u0079\u0070\u0065\u0072\u006c\u0069\u006e\u006b\u0042\u0061\u0073\u0065"}:_fc .HyperlinkBase =new (string );
if _fba :=d .DecodeElement (_fc .HyperlinkBase ,&_aeb );_fba !=nil {return _fba ;};case _a .Name {Space :"\u0068t\u0074p\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002eo\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006da\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0065\u0078\u0074\u0065n\u0064\u0065\u0064\u002d\u0070\u0072\u006f\u0070e\u0072\u0074\u0069\u0065\u0073",Local :"\u0048\u004c\u0069\u006e\u006b\u0073"},_a .Name {Space :"ht\u0074\u0070:\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0065x\u0074e\u006e\u0064\u0065\u0064\u0050\u0072\u006f\u0070e\u0072t\u0069\u0065s",Local :"\u0048\u004c\u0069\u006e\u006b\u0073"}:_fc .HLinks =NewCT_VectorVariant ();
if _bdeb :=d .DecodeElement (_fc .HLinks ,&_aeb );_bdeb !=nil {return _bdeb ;};case _a .Name {Space :"\u0068t\u0074p\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002eo\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006da\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0065\u0078\u0074\u0065n\u0064\u0065\u0064\u002d\u0070\u0072\u006f\u0070e\u0072\u0074\u0069\u0065\u0073",Local :"\u0048\u0079\u0070\u0065\u0072\u006c\u0069\u006e\u006b\u0073\u0043\u0068a\u006e\u0067\u0065\u0064"},_a .Name {Space :"ht\u0074\u0070:\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0065x\u0074e\u006e\u0064\u0065\u0064\u0050\u0072\u006f\u0070e\u0072t\u0069\u0065s",Local :"\u0048\u0079\u0070\u0065\u0072\u006c\u0069\u006e\u006b\u0073\u0043\u0068a\u006e\u0067\u0065\u0064"}:_fc .HyperlinksChanged =new (bool );
if _dg :=d .DecodeElement (_fc .HyperlinksChanged ,&_aeb );_dg !=nil {return _dg ;};case _a .Name {Space :"\u0068t\u0074p\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002eo\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006da\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0065\u0078\u0074\u0065n\u0064\u0065\u0064\u002d\u0070\u0072\u006f\u0070e\u0072\u0074\u0069\u0065\u0073",Local :"\u0044\u0069\u0067\u0053\u0069\u0067"},_a .Name {Space :"ht\u0074\u0070:\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0065x\u0074e\u006e\u0064\u0065\u0064\u0050\u0072\u006f\u0070e\u0072t\u0069\u0065s",Local :"\u0044\u0069\u0067\u0053\u0069\u0067"}:_fc .DigSig =NewCT_DigSigBlob ();
if _ecc :=d .DecodeElement (_fc .DigSig ,&_aeb );_ecc !=nil {return _ecc ;};case _a .Name {Space :"\u0068t\u0074p\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002eo\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006da\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0065\u0078\u0074\u0065n\u0064\u0065\u0064\u002d\u0070\u0072\u006f\u0070e\u0072\u0074\u0069\u0065\u0073",Local :"A\u0070\u0070\u006c\u0069\u0063\u0061\u0074\u0069\u006f\u006e"},_a .Name {Space :"ht\u0074\u0070:\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0065x\u0074e\u006e\u0064\u0065\u0064\u0050\u0072\u006f\u0070e\u0072t\u0069\u0065s",Local :"A\u0070\u0070\u006c\u0069\u0063\u0061\u0074\u0069\u006f\u006e"}:_fc .Application =new (string );
if _bfa :=d .DecodeElement (_fc .Application ,&_aeb );_bfa !=nil {return _bfa ;};case _a .Name {Space :"\u0068t\u0074p\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002eo\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006da\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0065\u0078\u0074\u0065n\u0064\u0065\u0064\u002d\u0070\u0072\u006f\u0070e\u0072\u0074\u0069\u0065\u0073",Local :"\u0041\u0070\u0070\u0056\u0065\u0072\u0073\u0069\u006f\u006e"},_a .Name {Space :"ht\u0074\u0070:\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0065x\u0074e\u006e\u0064\u0065\u0064\u0050\u0072\u006f\u0070e\u0072t\u0069\u0065s",Local :"\u0041\u0070\u0070\u0056\u0065\u0072\u0073\u0069\u006f\u006e"}:_fc .AppVersion =new (string );
if _bgg :=d .DecodeElement (_fc .AppVersion ,&_aeb );_bgg !=nil {return _bgg ;};case _a .Name {Space :"\u0068t\u0074p\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002eo\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006da\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0065\u0078\u0074\u0065n\u0064\u0065\u0064\u002d\u0070\u0072\u006f\u0070e\u0072\u0074\u0069\u0065\u0073",Local :"D\u006f\u0063\u0053\u0065\u0063\u0075\u0072\u0069\u0074\u0079"},_a .Name {Space :"ht\u0074\u0070:\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0065x\u0074e\u006e\u0064\u0065\u0064\u0050\u0072\u006f\u0070e\u0072t\u0069\u0065s",Local :"D\u006f\u0063\u0053\u0065\u0063\u0075\u0072\u0069\u0074\u0079"}:_fc .DocSecurity =new (int32 );
if _add :=d .DecodeElement (_fc .DocSecurity ,&_aeb );_add !=nil {return _add ;};default:_b .Log .Debug ("\u0073\u006b\u0069\u0070\u0070\u0069\u006e\u0067 \u0075\u006e\u0073up\u0070\u006f\u0072\u0074\u0065\u0064 \u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0043\u0054\u005f\u0050r\u006f\u0070\u0065\u0072\u0074\u0069\u0065\u0073 \u0025\u0076",_aeb .Name );
if _cdf :=d .Skip ();_cdf !=nil {return _cdf ;};};case _a .EndElement :break _dc ;case _a .CharData :};};return nil ;};

// Validate validates the Properties and its children
func (_bdd *Properties )Validate ()error {return _bdd .ValidateWithPath ("\u0050\u0072\u006f\u0070\u0065\u0072\u0074\u0069\u0065\u0073");};

// ValidateWithPath validates the CT_DigSigBlob and its children, prefixing error messages with path
func (_fb *CT_DigSigBlob )ValidateWithPath (path string )error {return nil };

// Validate validates the CT_VectorLpstr and its children
func (_afb *CT_VectorLpstr )Validate ()error {return _afb .ValidateWithPath ("\u0043\u0054\u005f\u0056\u0065\u0063\u0074\u006f\u0072L\u0070\u0073\u0074\u0072");};func NewCT_VectorVariant ()*CT_VectorVariant {_ecdf :=&CT_VectorVariant {};_ecdf .Vector =_cb .NewVector ();
return _ecdf ;};type Properties struct{CT_Properties };func init (){_ac .RegisterConstructor ("\u0068t\u0074p\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002eo\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006da\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0065\u0078\u0074\u0065n\u0064\u0065\u0064\u002d\u0070\u0072\u006f\u0070e\u0072\u0074\u0069\u0065\u0073","\u0043\u0054\u005f\u0050\u0072\u006f\u0070\u0065\u0072\u0074\u0069\u0065\u0073",NewCT_Properties );
_ac .RegisterConstructor ("\u0068t\u0074p\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002eo\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006da\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0065\u0078\u0074\u0065n\u0064\u0065\u0064\u002d\u0070\u0072\u006f\u0070e\u0072\u0074\u0069\u0065\u0073","\u0043\u0054_\u0056\u0065\u0063t\u006f\u0072\u0056\u0061\u0072\u0069\u0061\u006e\u0074",NewCT_VectorVariant );
_ac .RegisterConstructor ("\u0068t\u0074p\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002eo\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006da\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0065\u0078\u0074\u0065n\u0064\u0065\u0064\u002d\u0070\u0072\u006f\u0070e\u0072\u0074\u0069\u0065\u0073","\u0043\u0054\u005f\u0056\u0065\u0063\u0074\u006f\u0072L\u0070\u0073\u0074\u0072",NewCT_VectorLpstr );
_ac .RegisterConstructor ("\u0068t\u0074p\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002eo\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006da\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0065\u0078\u0074\u0065n\u0064\u0065\u0064\u002d\u0070\u0072\u006f\u0070e\u0072\u0074\u0069\u0065\u0073","\u0043\u0054\u005f\u0044\u0069\u0067\u0053\u0069\u0067\u0042\u006c\u006f\u0062",NewCT_DigSigBlob );
_ac .RegisterConstructor ("\u0068t\u0074p\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002eo\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006da\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0065\u0078\u0074\u0065n\u0064\u0065\u0064\u002d\u0070\u0072\u006f\u0070e\u0072\u0074\u0069\u0065\u0073","\u0050\u0072\u006f\u0070\u0065\u0072\u0074\u0069\u0065\u0073",NewProperties );
};