//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package docPropsVTypes ;import (_eg "encoding/xml";_ed "fmt";_ea "github.com/unidoc/unioffice/v2";_cb "github.com/unidoc/unioffice/v2/common/logger";_g "github.com/unidoc/unioffice/v2/schema/soo/ofc/sharedTypes";_c "regexp";_ec "strconv";_b "time";);func (_be *Array )UnmarshalXML (d *_eg .Decoder ,start _eg .StartElement )error {_be .CT_Array =*NewCT_Array ();
for _ ,_d :=range start .Attr {if _d .Name .Local =="\u006cB\u006f\u0075\u006e\u0064\u0073"{_f ,_bb :=_ec .ParseInt (_d .Value ,10,32);if _bb !=nil {return _bb ;};_be .LBoundsAttr =int32 (_f );continue ;};if _d .Name .Local =="\u0075B\u006f\u0075\u006e\u0064\u0073"{_beg ,_fb :=_ec .ParseInt (_d .Value ,10,32);
if _fb !=nil {return _fb ;};_be .UBoundsAttr =int32 (_beg );continue ;};if _d .Name .Local =="\u0062\u0061\u0073\u0065\u0054\u0079\u0070\u0065"{_be .BaseTypeAttr .UnmarshalXMLAttr (_d );continue ;};};_ff :for {_db ,_cd :=d .Token ();if _cd !=nil {return _cd ;
};switch _de :=_db .(type ){case _eg .StartElement :switch _de .Name {case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0076a\u0072\u0069\u0061\u006e\u0074"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0076a\u0072\u0069\u0061\u006e\u0074"}:_gd :=NewCT_ArrayChoice ();
if _a :=d .DecodeElement (&_gd .Variant ,&_de );_a !=nil {return _a ;};_be .ArrayChoice =append (_be .ArrayChoice ,_gd );case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0069\u0031"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0069\u0031"}:_gdg :=NewCT_ArrayChoice ();
if _dec :=d .DecodeElement (&_gdg .I1 ,&_de );_dec !=nil {return _dec ;};_be .ArrayChoice =append (_be .ArrayChoice ,_gdg );case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0069\u0032"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0069\u0032"}:_ba :=NewCT_ArrayChoice ();
if _cbe :=d .DecodeElement (&_ba .I2 ,&_de );_cbe !=nil {return _cbe ;};_be .ArrayChoice =append (_be .ArrayChoice ,_ba );case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0069\u0034"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0069\u0034"}:_fa :=NewCT_ArrayChoice ();
if _aa :=d .DecodeElement (&_fa .I4 ,&_de );_aa !=nil {return _aa ;};_be .ArrayChoice =append (_be .ArrayChoice ,_fa );case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0069\u006e\u0074"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0069\u006e\u0074"}:_ab :=NewCT_ArrayChoice ();
if _bad :=d .DecodeElement (&_ab .Int ,&_de );_bad !=nil {return _bad ;};_be .ArrayChoice =append (_be .ArrayChoice ,_ab );case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0075\u0069\u0031"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0075\u0069\u0031"}:_cdd :=NewCT_ArrayChoice ();
if _dd :=d .DecodeElement (&_cdd .Ui1 ,&_de );_dd !=nil {return _dd ;};_be .ArrayChoice =append (_be .ArrayChoice ,_cdd );case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0075\u0069\u0032"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0075\u0069\u0032"}:_aaa :=NewCT_ArrayChoice ();
if _dg :=d .DecodeElement (&_aaa .Ui2 ,&_de );_dg !=nil {return _dg ;};_be .ArrayChoice =append (_be .ArrayChoice ,_aaa );case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0075\u0069\u0034"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0075\u0069\u0034"}:_bd :=NewCT_ArrayChoice ();
if _bdd :=d .DecodeElement (&_bd .Ui4 ,&_de );_bdd !=nil {return _bdd ;};_be .ArrayChoice =append (_be .ArrayChoice ,_bd );case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0075\u0069\u006e\u0074"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0075\u0069\u006e\u0074"}:_ca :=NewCT_ArrayChoice ();
if _fd :=d .DecodeElement (&_ca .Uint ,&_de );_fd !=nil {return _fd ;};_be .ArrayChoice =append (_be .ArrayChoice ,_ca );case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0072\u0034"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0072\u0034"}:_dea :=NewCT_ArrayChoice ();
if _fbg :=d .DecodeElement (&_dea .R4 ,&_de );_fbg !=nil {return _fbg ;};_be .ArrayChoice =append (_be .ArrayChoice ,_dea );case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0072\u0038"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0072\u0038"}:_gdb :=NewCT_ArrayChoice ();
if _egg :=d .DecodeElement (&_gdb .R8 ,&_de );_egg !=nil {return _egg ;};_be .ArrayChoice =append (_be .ArrayChoice ,_gdb );case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0064e\u0063\u0069\u006d\u0061\u006c"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0064e\u0063\u0069\u006d\u0061\u006c"}:_ce :=NewCT_ArrayChoice ();
if _aad :=d .DecodeElement (&_ce .Decimal ,&_de );_aad !=nil {return _aad ;};_be .ArrayChoice =append (_be .ArrayChoice ,_ce );case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0062\u0073\u0074\u0072"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0062\u0073\u0074\u0072"}:_bg :=NewCT_ArrayChoice ();
if _ee :=d .DecodeElement (&_bg .Bstr ,&_de );_ee !=nil {return _ee ;};_be .ArrayChoice =append (_be .ArrayChoice ,_bg );case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0064\u0061\u0074\u0065"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0064\u0061\u0074\u0065"}:_cef :=NewCT_ArrayChoice ();
if _dc :=d .DecodeElement (&_cef .Date ,&_de );_dc !=nil {return _dc ;};_be .ArrayChoice =append (_be .ArrayChoice ,_cef );case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0062\u006f\u006f\u006c"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0062\u006f\u006f\u006c"}:_eda :=NewCT_ArrayChoice ();
if _fc :=d .DecodeElement (&_eda .Bool ,&_de );_fc !=nil {return _fc ;};_be .ArrayChoice =append (_be .ArrayChoice ,_eda );case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0065\u0072\u0072o\u0072"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0065\u0072\u0072o\u0072"}:_abf :=NewCT_ArrayChoice ();
if _ge :=d .DecodeElement (&_abf .Error ,&_de );_ge !=nil {return _ge ;};_be .ArrayChoice =append (_be .ArrayChoice ,_abf );case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0063\u0079"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0063\u0079"}:_ef :=NewCT_ArrayChoice ();
if _deb :=d .DecodeElement (&_ef .Cy ,&_de );_deb !=nil {return _deb ;};_be .ArrayChoice =append (_be .ArrayChoice ,_ef );default:_cb .Log .Debug ("s\u006b\u0069\u0070\u0070\u0069\u006e\u0067\u0020\u0075\u006e\u0073\u0075\u0070\u0070\u006f\u0072\u0074\u0065d\u0020\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006fn \u0041\u0072\u0072a\u0079 \u0025\u0076",_de .Name );
if _cdg :=d .Skip ();_cdg !=nil {return _cdg ;};};case _eg .EndElement :break _ff ;case _eg .CharData :};};return nil ;};

// ValidateWithPath validates the CT_Array and its children, prefixing error messages with path
func (_fg *CT_Array )ValidateWithPath (path string )error {if _fg .BaseTypeAttr ==ST_ArrayBaseTypeUnset {return _ed .Errorf ("\u0025\u0073/B\u0061\u0073\u0065T\u0079\u0070\u0065\u0041ttr\u0020is\u0020\u0061\u0020\u006d\u0061\u006e\u0064at\u006f\u0072\u0079\u0020\u0066\u0069\u0065l\u0064",path );
};if _egc :=_fg .BaseTypeAttr .ValidateWithPath (path +"\u002f\u0042\u0061\u0073\u0065\u0054\u0079\u0070\u0065\u0041\u0074\u0074\u0072");_egc !=nil {return _egc ;};for _fca ,_eea :=range _fg .ArrayChoice {if _gea :=_eea .ValidateWithPath (_ed .Sprintf ("\u0025s\u002fA\u0072\u0072\u0061\u0079\u0043h\u006f\u0069c\u0065\u005b\u0025\u0064\u005d",path ,_fca ));
_gea !=nil {return _gea ;};};return nil ;};var ST_CyPatternRe =_c .MustCompile (ST_CyPattern );func (_egda ST_ArrayBaseType )String ()string {switch _egda {case 0:return "";case 1:return "\u0076a\u0072\u0069\u0061\u006e\u0074";case 2:return "\u0069\u0031";
case 3:return "\u0069\u0032";case 4:return "\u0069\u0034";case 5:return "\u0069\u006e\u0074";case 6:return "\u0075\u0069\u0031";case 7:return "\u0075\u0069\u0032";case 8:return "\u0075\u0069\u0034";case 9:return "\u0075\u0069\u006e\u0074";case 10:return "\u0072\u0034";
case 11:return "\u0072\u0038";case 12:return "\u0064e\u0063\u0069\u006d\u0061\u006c";case 13:return "\u0062\u0073\u0074\u0072";case 14:return "\u0064\u0061\u0074\u0065";case 15:return "\u0062\u006f\u006f\u006c";case 16:return "\u0063\u0079";case 17:return "\u0065\u0072\u0072o\u0072";
};return "";};type Empty struct{CT_Empty };func (_dfbd *CT_VectorChoice )UnmarshalXML (d *_eg .Decoder ,start _eg .StartElement )error {_bdf :=start ;switch start .Name {case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0076a\u0072\u0069\u0061\u006e\u0074"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0076a\u0072\u0069\u0061\u006e\u0074"}:_dfbd .Variant =NewVariant ();
if _gbf :=d .DecodeElement (_dfbd .Variant ,&_bdf );_gbf !=nil {return _gbf ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0069\u0031"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0069\u0031"}:_dfbd .I1 =new (int8 );
if _ggcg :=d .DecodeElement (_dfbd .I1 ,&_bdf );_ggcg !=nil {return _ggcg ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0069\u0032"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0069\u0032"}:_dfbd .I2 =new (int16 );
if _faga :=d .DecodeElement (_dfbd .I2 ,&_bdf );_faga !=nil {return _faga ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0069\u0034"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0069\u0034"}:_dfbd .I4 =new (int32 );
if _cgca :=d .DecodeElement (_dfbd .I4 ,&_bdf );_cgca !=nil {return _cgca ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0069\u0038"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0069\u0038"}:_dfbd .I8 =new (int64 );
if _efgc :=d .DecodeElement (_dfbd .I8 ,&_bdf );_efgc !=nil {return _efgc ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0075\u0069\u0031"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0075\u0069\u0031"}:_dfbd .Ui1 =new (uint8 );
if _fagf :=d .DecodeElement (_dfbd .Ui1 ,&_bdf );_fagf !=nil {return _fagf ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0075\u0069\u0032"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0075\u0069\u0032"}:_dfbd .Ui2 =new (uint16 );
if _geec :=d .DecodeElement (_dfbd .Ui2 ,&_bdf );_geec !=nil {return _geec ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0075\u0069\u0034"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0075\u0069\u0034"}:_dfbd .Ui4 =new (uint32 );
if _bgg :=d .DecodeElement (_dfbd .Ui4 ,&_bdf );_bgg !=nil {return _bgg ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0075\u0069\u0038"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0075\u0069\u0038"}:_dfbd .Ui8 =new (uint64 );
if _ebb :=d .DecodeElement (_dfbd .Ui8 ,&_bdf );_ebb !=nil {return _ebb ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0072\u0034"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0072\u0034"}:_dfbd .R4 =new (float32 );
if _gage :=d .DecodeElement (_dfbd .R4 ,&_bdf );_gage !=nil {return _gage ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0072\u0038"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0072\u0038"}:_dfbd .R8 =new (float64 );
if _eaga :=d .DecodeElement (_dfbd .R8 ,&_bdf );_eaga !=nil {return _eaga ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u006c\u0070\u0073t\u0072"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u006c\u0070\u0073t\u0072"}:_dfbd .Lpstr =new (string );
if _ebac :=d .DecodeElement (_dfbd .Lpstr ,&_bdf );_ebac !=nil {return _ebac ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u006c\u0070\u0077\u0073\u0074\u0072"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u006c\u0070\u0077\u0073\u0074\u0072"}:_dfbd .Lpwstr =new (string );
if _fgab :=d .DecodeElement (_dfbd .Lpwstr ,&_bdf );_fgab !=nil {return _fgab ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0062\u0073\u0074\u0072"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0062\u0073\u0074\u0072"}:_dfbd .Bstr =new (string );
if _fdg :=d .DecodeElement (_dfbd .Bstr ,&_bdf );_fdg !=nil {return _fdg ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0064\u0061\u0074\u0065"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0064\u0061\u0074\u0065"}:var _gcda string ;
if _dbgg :=d .DecodeElement (&_gcda ,&_bdf );_dbgg !=nil {return _dbgg ;};_adfa ,_agg :=_g .ParseStdlibTime (_gcda );if _agg !=nil {return _agg ;};_dfbd .Date =&_adfa ;case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0066\u0069\u006c\u0065\u0074\u0069\u006d\u0065"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0066\u0069\u006c\u0065\u0074\u0069\u006d\u0065"}:_dfbd .Filetime =new (_b .Time );
if _efb :=d .DecodeElement (_dfbd .Filetime ,&_bdf );_efb !=nil {return _efb ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0062\u006f\u006f\u006c"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0062\u006f\u006f\u006c"}:_dfbd .Bool =new (bool );
if _fbc :=d .DecodeElement (_dfbd .Bool ,&_bdf );_fbc !=nil {return _fbc ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0063\u0079"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0063\u0079"}:_dfbd .Cy =new (string );
if _bga :=d .DecodeElement (_dfbd .Cy ,&_bdf );_bga !=nil {return _bga ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0065\u0072\u0072o\u0072"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0065\u0072\u0072o\u0072"}:_dfbd .Error =new (string );
if _cccc :=d .DecodeElement (_dfbd .Error ,&_bdf );_cccc !=nil {return _cccc ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0063\u006c\u0073i\u0064"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0063\u006c\u0073i\u0064"}:_dfbd .Clsid =new (string );
if _fgabf :=d .DecodeElement (_dfbd .Clsid ,&_bdf );_fgabf !=nil {return _fgabf ;};default:_cb .Log .Debug ("\u0073\u006b\u0069\u0070\u0070\u0069\u006e\u0067\u0020\u0075\u006e\u0073\u0075\u0070\u0070\u006f\u0072\u0074e\u0064\u0020\u0065\u006c\u0065\u006d\u0065n\u0074\u0020\u006f\u006e\u0020\u0043\u0054\u005f\u0056\u0065\u0063t\u006f\u0072\u0043\u0068\u006f\u0069\u0063\u0065\u0020\u0025\u0076",_bdf .Name );
if _fbge :=d .Skip ();_fbge !=nil {return _fbge ;};};return nil ;};func NewVariant ()*Variant {_ccef :=&Variant {};_ccef .CT_Variant =*NewCT_Variant ();return _ccef };const ST_CyPattern ="\u005c\u0073\u002a\u005b0-\u0039\u005d\u002a\u005c\u002e\u005b\u0030\u002d\u0039\u005d\u007b\u0034\u007d\u005cs\u002a";
func (_gcg *CT_ArrayChoice )UnmarshalXML (d *_eg .Decoder ,start _eg .StartElement )error {_fga :=start ;switch start .Name {case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0076a\u0072\u0069\u0061\u006e\u0074"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0076a\u0072\u0069\u0061\u006e\u0074"}:_gcg .Variant =NewVariant ();
if _aec :=d .DecodeElement (_gcg .Variant ,&_fga );_aec !=nil {return _aec ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0069\u0031"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0069\u0031"}:_gcg .I1 =new (int8 );
if _ad :=d .DecodeElement (_gcg .I1 ,&_fga );_ad !=nil {return _ad ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0069\u0032"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0069\u0032"}:_gcg .I2 =new (int16 );
if _dfe :=d .DecodeElement (_gcg .I2 ,&_fga );_dfe !=nil {return _dfe ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0069\u0034"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0069\u0034"}:_gcg .I4 =new (int32 );
if _dbc :=d .DecodeElement (_gcg .I4 ,&_fga );_dbc !=nil {return _dbc ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0069\u006e\u0074"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0069\u006e\u0074"}:_gcg .Int =new (int32 );
if _cag :=d .DecodeElement (_gcg .Int ,&_fga );_cag !=nil {return _cag ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0075\u0069\u0031"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0075\u0069\u0031"}:_gcg .Ui1 =new (uint8 );
if _cea :=d .DecodeElement (_gcg .Ui1 ,&_fga );_cea !=nil {return _cea ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0075\u0069\u0032"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0075\u0069\u0032"}:_gcg .Ui2 =new (uint16 );
if _egga :=d .DecodeElement (_gcg .Ui2 ,&_fga );_egga !=nil {return _egga ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0075\u0069\u0034"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0075\u0069\u0034"}:_gcg .Ui4 =new (uint32 );
if _cfb :=d .DecodeElement (_gcg .Ui4 ,&_fga );_cfb !=nil {return _cfb ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0075\u0069\u006e\u0074"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0075\u0069\u006e\u0074"}:_gcg .Uint =new (uint32 );
if _bbb :=d .DecodeElement (_gcg .Uint ,&_fga );_bbb !=nil {return _bbb ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0072\u0034"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0072\u0034"}:_gcg .R4 =new (float32 );
if _edd :=d .DecodeElement (_gcg .R4 ,&_fga );_edd !=nil {return _edd ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0072\u0038"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0072\u0038"}:_gcg .R8 =new (float64 );
if _ggbf :=d .DecodeElement (_gcg .R8 ,&_fga );_ggbf !=nil {return _ggbf ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0064e\u0063\u0069\u006d\u0061\u006c"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0064e\u0063\u0069\u006d\u0061\u006c"}:_gcg .Decimal =new (float64 );
if _fcb :=d .DecodeElement (_gcg .Decimal ,&_fga );_fcb !=nil {return _fcb ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0062\u0073\u0074\u0072"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0062\u0073\u0074\u0072"}:_gcg .Bstr =new (string );
if _dbd :=d .DecodeElement (_gcg .Bstr ,&_fga );_dbd !=nil {return _dbd ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0064\u0061\u0074\u0065"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0064\u0061\u0074\u0065"}:var _dbf string ;
if _bce :=d .DecodeElement (&_dbf ,&_fga );_bce !=nil {return _bce ;};_dga ,_cdbd :=_g .ParseStdlibTime (_dbf );if _cdbd !=nil {return _cdbd ;};_gcg .Date =&_dga ;case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0062\u006f\u006f\u006c"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0062\u006f\u006f\u006c"}:_gcg .Bool =new (bool );
if _fcbb :=d .DecodeElement (_gcg .Bool ,&_fga );_fcbb !=nil {return _fcbb ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0065\u0072\u0072o\u0072"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0065\u0072\u0072o\u0072"}:_gcg .Error =new (string );
if _ddd :=d .DecodeElement (_gcg .Error ,&_fga );_ddd !=nil {return _ddd ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0063\u0079"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0063\u0079"}:_gcg .Cy =new (string );
if _eaab :=d .DecodeElement (_gcg .Cy ,&_fga );_eaab !=nil {return _eaab ;};default:_cb .Log .Debug ("\u0073\u006b\u0069\u0070\u0070\u0069n\u0067\u0020\u0075\u006e\u0073\u0075\u0070\u0070\u006f\u0072\u0074\u0065\u0064\u0020\u0065\u006c\u0065\u006d\u0065\u006et\u0020\u006f\u006e\u0020\u0043\u0054\u005f\u0041\u0072\u0072\u0061\u0079\u0043\u0068o\u0069c\u0065\u0020\u0025\u0076",_fga .Name );
if _cdbdb :=d .Skip ();_cdbdb !=nil {return _cdbdb ;};};return nil ;};func (_afe *Vector )MarshalXML (e *_eg .Encoder ,start _eg .StartElement )error {return _afe .CT_Vector .MarshalXML (e ,start );};type CT_ArrayChoice struct{Variant *Variant ;I1 *int8 ;
I2 *int16 ;I4 *int32 ;Int *int32 ;Ui1 *uint8 ;Ui2 *uint16 ;Ui4 *uint32 ;Uint *uint32 ;R4 *float32 ;R8 *float64 ;Decimal *float64 ;Bstr *string ;Date *_b .Time ;Bool *bool ;Error *string ;Cy *string ;};

// ValidateWithPath validates the CT_Variant and its children, prefixing error messages with path
func (_bdg *CT_Variant )ValidateWithPath (path string )error {if _ddf :=_bdg .VariantChoice .ValidateWithPath (path +"\u002f\u0056\u0061\u0072\u0069\u0061\u006e\u0074\u0043h\u006f\u0069\u0063\u0065");_ddf !=nil {return _ddf ;};return nil ;};

// ValidateWithPath validates the CT_VariantChoice and its children, prefixing error messages with path
func (_fcd *CT_VariantChoice )ValidateWithPath (path string )error {if _fcd .Variant !=nil {if _baag :=_fcd .Variant .ValidateWithPath (path +"\u002f\u0056\u0061\u0072\u0069\u0061\u006e\u0074");_baag !=nil {return _baag ;};};if _fcd .Vector !=nil {if _cbea :=_fcd .Vector .ValidateWithPath (path +"\u002fV\u0065\u0063\u0074\u006f\u0072");
_cbea !=nil {return _cbea ;};};if _fcd .Array !=nil {if _ffb :=_fcd .Array .ValidateWithPath (path +"\u002f\u0041\u0072\u0072\u0061\u0079");_ffb !=nil {return _ffb ;};};if _fcd .Empty !=nil {if _dcgf :=_fcd .Empty .ValidateWithPath (path +"\u002f\u0045\u006d\u0070\u0074\u0079");
_dcgf !=nil {return _dcgf ;};};if _fcd .Null !=nil {if _cce :=_fcd .Null .ValidateWithPath (path +"\u002f\u004e\u0075l\u006c");_cce !=nil {return _cce ;};};if _fcd .Cy !=nil {if !ST_CyPatternRe .MatchString (*_fcd .Cy ){return _ed .Errorf ("\u0025\u0073\u002f\u006d\u002e\u0043y\u0020\u006d\u0075\u0073\u0074\u0020\u006d\u0061\u0074\u0063\u0068\u0020\u0027%\u0073\u0027\u0020\u0028\u0068\u0061\u0076e\u0020\u0025\u0076\u0029",path ,ST_CyPatternRe ,*_fcd .Cy );
};};if _fcd .Error !=nil {if !ST_ErrorPatternRe .MatchString (*_fcd .Error ){return _ed .Errorf ("\u0025\u0073/m\u002e\u0045\u0072r\u006f\u0072\u0020\u006dust\u0020ma\u0074\u0063\u0068\u0020\u0027\u0025\u0073' \u0028\u0068\u0061\u0076\u0065\u0020\u0025v\u0029",path ,ST_ErrorPatternRe ,*_fcd .Error );
};};if _fcd .Vstream !=nil {if _febc :=_fcd .Vstream .ValidateWithPath (path +"\u002f\u0056\u0073\u0074\u0072\u0065\u0061\u006d");_febc !=nil {return _febc ;};};if _fcd .Clsid !=nil {if !_g .ST_GuidPatternRe .MatchString (*_fcd .Clsid ){return _ed .Errorf ("\u0025\u0073/m\u002e\u0043\u006cs\u0069\u0064\u0020\u006dust\u0020ma\u0074\u0063\u0068\u0020\u0027\u0025\u0073' \u0028\u0068\u0061\u0076\u0065\u0020\u0025v\u0029",path ,_g .ST_GuidPatternRe ,*_fcd .Clsid );
};};return nil ;};func (_bgce ST_VectorBaseType )String ()string {switch _bgce {case 0:return "";case 1:return "\u0076a\u0072\u0069\u0061\u006e\u0074";case 2:return "\u0069\u0031";case 3:return "\u0069\u0032";case 4:return "\u0069\u0034";case 5:return "\u0069\u0038";
case 6:return "\u0075\u0069\u0031";case 7:return "\u0075\u0069\u0032";case 8:return "\u0075\u0069\u0034";case 9:return "\u0075\u0069\u0038";case 10:return "\u0072\u0034";case 11:return "\u0072\u0038";case 12:return "\u006c\u0070\u0073t\u0072";case 13:return "\u006c\u0070\u0077\u0073\u0074\u0072";
case 14:return "\u0062\u0073\u0074\u0072";case 15:return "\u0064\u0061\u0074\u0065";case 16:return "\u0066\u0069\u006c\u0065\u0074\u0069\u006d\u0065";case 17:return "\u0062\u006f\u006f\u006c";case 18:return "\u0063\u0079";case 19:return "\u0065\u0072\u0072o\u0072";
case 20:return "\u0063\u006c\u0073i\u0064";};return "";};

// Validate validates the Empty and its children
func (_bde *Empty )Validate ()error {return _bde .ValidateWithPath ("\u0045\u006d\u0070t\u0079")};func (_dcd *CT_Array )UnmarshalXML (d *_eg .Decoder ,start _eg .StartElement )error {_dcd .BaseTypeAttr =ST_ArrayBaseType (1);for _ ,_gee :=range start .Attr {if _gee .Name .Local =="\u006cB\u006f\u0075\u006e\u0064\u0073"{_cg ,_fea :=_ec .ParseInt (_gee .Value ,10,32);
if _fea !=nil {return _fea ;};_dcd .LBoundsAttr =int32 (_cg );continue ;};if _gee .Name .Local =="\u0075B\u006f\u0075\u006e\u0064\u0073"{_fef ,_gf :=_ec .ParseInt (_gee .Value ,10,32);if _gf !=nil {return _gf ;};_dcd .UBoundsAttr =int32 (_fef );continue ;
};if _gee .Name .Local =="\u0062\u0061\u0073\u0065\u0054\u0079\u0070\u0065"{_dcd .BaseTypeAttr .UnmarshalXMLAttr (_gee );continue ;};};_ccb :for {_da ,_ded :=d .Token ();if _ded !=nil {return _ded ;};switch _bc :=_da .(type ){case _eg .StartElement :switch _bc .Name {case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0076a\u0072\u0069\u0061\u006e\u0074"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0076a\u0072\u0069\u0061\u006e\u0074"}:_cgb :=NewCT_ArrayChoice ();
if _cab :=d .DecodeElement (&_cgb .Variant ,&_bc );_cab !=nil {return _cab ;};_dcd .ArrayChoice =append (_dcd .ArrayChoice ,_cgb );case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0069\u0031"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0069\u0031"}:_fee :=NewCT_ArrayChoice ();
if _ae :=d .DecodeElement (&_fee .I1 ,&_bc );_ae !=nil {return _ae ;};_dcd .ArrayChoice =append (_dcd .ArrayChoice ,_fee );case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0069\u0032"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0069\u0032"}:_bcc :=NewCT_ArrayChoice ();
if _dbg :=d .DecodeElement (&_bcc .I2 ,&_bc );_dbg !=nil {return _dbg ;};_dcd .ArrayChoice =append (_dcd .ArrayChoice ,_bcc );case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0069\u0034"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0069\u0034"}:_cf :=NewCT_ArrayChoice ();
if _eab :=d .DecodeElement (&_cf .I4 ,&_bc );_eab !=nil {return _eab ;};_dcd .ArrayChoice =append (_dcd .ArrayChoice ,_cf );case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0069\u006e\u0074"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0069\u006e\u0074"}:_ege :=NewCT_ArrayChoice ();
if _cgg :=d .DecodeElement (&_ege .Int ,&_bc );_cgg !=nil {return _cgg ;};_dcd .ArrayChoice =append (_dcd .ArrayChoice ,_ege );case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0075\u0069\u0031"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0075\u0069\u0031"}:_faf :=NewCT_ArrayChoice ();
if _cefg :=d .DecodeElement (&_faf .Ui1 ,&_bc );_cefg !=nil {return _cefg ;};_dcd .ArrayChoice =append (_dcd .ArrayChoice ,_faf );case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0075\u0069\u0032"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0075\u0069\u0032"}:_ccf :=NewCT_ArrayChoice ();
if _gb :=d .DecodeElement (&_ccf .Ui2 ,&_bc );_gb !=nil {return _gb ;};_dcd .ArrayChoice =append (_dcd .ArrayChoice ,_ccf );case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0075\u0069\u0034"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0075\u0069\u0034"}:_gdbd :=NewCT_ArrayChoice ();
if _baa :=d .DecodeElement (&_gdbd .Ui4 ,&_bc );_baa !=nil {return _baa ;};_dcd .ArrayChoice =append (_dcd .ArrayChoice ,_gdbd );case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0075\u0069\u006e\u0074"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0075\u0069\u006e\u0074"}:_dca :=NewCT_ArrayChoice ();
if _cfg :=d .DecodeElement (&_dca .Uint ,&_bc );_cfg !=nil {return _cfg ;};_dcd .ArrayChoice =append (_dcd .ArrayChoice ,_dca );case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0072\u0034"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0072\u0034"}:_ecf :=NewCT_ArrayChoice ();
if _agd :=d .DecodeElement (&_ecf .R4 ,&_bc );_agd !=nil {return _agd ;};_dcd .ArrayChoice =append (_dcd .ArrayChoice ,_ecf );case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0072\u0038"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0072\u0038"}:_bag :=NewCT_ArrayChoice ();
if _dcg :=d .DecodeElement (&_bag .R8 ,&_bc );_dcg !=nil {return _dcg ;};_dcd .ArrayChoice =append (_dcd .ArrayChoice ,_bag );case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0064e\u0063\u0069\u006d\u0061\u006c"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0064e\u0063\u0069\u006d\u0061\u006c"}:_gdgd :=NewCT_ArrayChoice ();
if _gfa :=d .DecodeElement (&_gdgd .Decimal ,&_bc );_gfa !=nil {return _gfa ;};_dcd .ArrayChoice =append (_dcd .ArrayChoice ,_gdgd );case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0062\u0073\u0074\u0072"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0062\u0073\u0074\u0072"}:_dcdb :=NewCT_ArrayChoice ();
if _ddg :=d .DecodeElement (&_dcdb .Bstr ,&_bc );_ddg !=nil {return _ddg ;};_dcd .ArrayChoice =append (_dcd .ArrayChoice ,_dcdb );case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0064\u0061\u0074\u0065"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0064\u0061\u0074\u0065"}:_dad :=NewCT_ArrayChoice ();
if _cdb :=d .DecodeElement (&_dad .Date ,&_bc );_cdb !=nil {return _cdb ;};_dcd .ArrayChoice =append (_dcd .ArrayChoice ,_dad );case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0062\u006f\u006f\u006c"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0062\u006f\u006f\u006c"}:_df :=NewCT_ArrayChoice ();
if _aea :=d .DecodeElement (&_df .Bool ,&_bc );_aea !=nil {return _aea ;};_dcd .ArrayChoice =append (_dcd .ArrayChoice ,_df );case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0065\u0072\u0072o\u0072"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0065\u0072\u0072o\u0072"}:_ccd :=NewCT_ArrayChoice ();
if _decf :=d .DecodeElement (&_ccd .Error ,&_bc );_decf !=nil {return _decf ;};_dcd .ArrayChoice =append (_dcd .ArrayChoice ,_ccd );case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0063\u0079"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0063\u0079"}:_dag :=NewCT_ArrayChoice ();
if _edb :=d .DecodeElement (&_dag .Cy ,&_bc );_edb !=nil {return _edb ;};_dcd .ArrayChoice =append (_dcd .ArrayChoice ,_dag );default:_cb .Log .Debug ("\u0073\u006b\u0069\u0070\u0070\u0069\u006eg\u0020\u0075\u006es\u0075\u0070\u0070\u006fr\u0074\u0065\u0064\u0020\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0043\u0054\u005f\u0041\u0072\u0072\u0061\u0079\u0020\u0025\u0076",_bc .Name );
if _cge :=d .Skip ();_cge !=nil {return _cge ;};};case _eg .EndElement :break _ccb ;case _eg .CharData :};};return nil ;};func (_egd *CT_Null )MarshalXML (e *_eg .Encoder ,start _eg .StartElement )error {e .EncodeToken (start );e .EncodeToken (_eg .EndElement {Name :start .Name });
return nil ;};

// Validate validates the CT_Empty and its children
func (_ggd *CT_Empty )Validate ()error {return _ggd .ValidateWithPath ("\u0043\u0054\u005f\u0045\u006d\u0070\u0074\u0079");};func (_deeg ST_ArrayBaseType )ValidateWithPath (path string )error {switch _deeg {case 0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17:default:return _ed .Errorf ("\u0025s\u003a\u0020\u006f\u0075t\u0020\u006f\u0066\u0020\u0072a\u006eg\u0065 \u0076\u0061\u006c\u0075\u0065\u0020\u0025d",path ,int (_deeg ));
};return nil ;};func NewCT_Vector ()*CT_Vector {_befe :=&CT_Vector {};_befe .BaseTypeAttr =ST_VectorBaseType (1);return _befe ;};func (_cdbf *CT_Vector )UnmarshalXML (d *_eg .Decoder ,start _eg .StartElement )error {_cdbf .BaseTypeAttr =ST_VectorBaseType (1);
for _ ,_eggab :=range start .Attr {if _eggab .Name .Local =="\u0062\u0061\u0073\u0065\u0054\u0079\u0070\u0065"{_cdbf .BaseTypeAttr .UnmarshalXMLAttr (_eggab );continue ;};if _eggab .Name .Local =="\u0073\u0069\u007a\u0065"{_gbd ,_ddea :=_ec .ParseUint (_eggab .Value ,10,32);
if _ddea !=nil {return _ddea ;};_cdbf .SizeAttr =uint32 (_gbd );continue ;};};_ffc :for {_ffa ,_fge :=d .Token ();if _fge !=nil {return _fge ;};switch _bfa :=_ffa .(type ){case _eg .StartElement :switch _bfa .Name {case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0076a\u0072\u0069\u0061\u006e\u0074"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0076a\u0072\u0069\u0061\u006e\u0074"}:_eee :=NewCT_VectorChoice ();
if _fba :=d .DecodeElement (&_eee .Variant ,&_bfa );_fba !=nil {return _fba ;};_cdbf .VectorChoice =append (_cdbf .VectorChoice ,_eee );case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0069\u0031"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0069\u0031"}:_eff :=NewCT_VectorChoice ();
if _ggfc :=d .DecodeElement (&_eff .I1 ,&_bfa );_ggfc !=nil {return _ggfc ;};_cdbf .VectorChoice =append (_cdbf .VectorChoice ,_eff );case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0069\u0032"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0069\u0032"}:_ffd :=NewCT_VectorChoice ();
if _dcda :=d .DecodeElement (&_ffd .I2 ,&_bfa );_dcda !=nil {return _dcda ;};_cdbf .VectorChoice =append (_cdbf .VectorChoice ,_ffd );case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0069\u0034"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0069\u0034"}:_fdd :=NewCT_VectorChoice ();
if _ecfg :=d .DecodeElement (&_fdd .I4 ,&_bfa );_ecfg !=nil {return _ecfg ;};_cdbf .VectorChoice =append (_cdbf .VectorChoice ,_fdd );case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0069\u0038"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0069\u0038"}:_fgadf :=NewCT_VectorChoice ();
if _gadc :=d .DecodeElement (&_fgadf .I8 ,&_bfa );_gadc !=nil {return _gadc ;};_cdbf .VectorChoice =append (_cdbf .VectorChoice ,_fgadf );case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0075\u0069\u0031"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0075\u0069\u0031"}:_fafa :=NewCT_VectorChoice ();
if _bcef :=d .DecodeElement (&_fafa .Ui1 ,&_bfa );_bcef !=nil {return _bcef ;};_cdbf .VectorChoice =append (_cdbf .VectorChoice ,_fafa );case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0075\u0069\u0032"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0075\u0069\u0032"}:_cbeb :=NewCT_VectorChoice ();
if _eba :=d .DecodeElement (&_cbeb .Ui2 ,&_bfa );_eba !=nil {return _eba ;};_cdbf .VectorChoice =append (_cdbf .VectorChoice ,_cbeb );case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0075\u0069\u0034"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0075\u0069\u0034"}:_eaf :=NewCT_VectorChoice ();
if _bddg :=d .DecodeElement (&_eaf .Ui4 ,&_bfa );_bddg !=nil {return _bddg ;};_cdbf .VectorChoice =append (_cdbf .VectorChoice ,_eaf );case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0075\u0069\u0038"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0075\u0069\u0038"}:_eeee :=NewCT_VectorChoice ();
if _ggdb :=d .DecodeElement (&_eeee .Ui8 ,&_bfa );_ggdb !=nil {return _ggdb ;};_cdbf .VectorChoice =append (_cdbf .VectorChoice ,_eeee );case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0072\u0034"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0072\u0034"}:_adb :=NewCT_VectorChoice ();
if _deae :=d .DecodeElement (&_adb .R4 ,&_bfa );_deae !=nil {return _deae ;};_cdbf .VectorChoice =append (_cdbf .VectorChoice ,_adb );case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0072\u0038"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0072\u0038"}:_cecb :=NewCT_VectorChoice ();
if _fgeb :=d .DecodeElement (&_cecb .R8 ,&_bfa );_fgeb !=nil {return _fgeb ;};_cdbf .VectorChoice =append (_cdbf .VectorChoice ,_cecb );case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u006c\u0070\u0073t\u0072"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u006c\u0070\u0073t\u0072"}:_cgd :=NewCT_VectorChoice ();
if _ddbg :=d .DecodeElement (&_cgd .Lpstr ,&_bfa );_ddbg !=nil {return _ddbg ;};_cdbf .VectorChoice =append (_cdbf .VectorChoice ,_cgd );case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u006c\u0070\u0077\u0073\u0074\u0072"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u006c\u0070\u0077\u0073\u0074\u0072"}:_cba :=NewCT_VectorChoice ();
if _cgc :=d .DecodeElement (&_cba .Lpwstr ,&_bfa );_cgc !=nil {return _cgc ;};_cdbf .VectorChoice =append (_cdbf .VectorChoice ,_cba );case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0062\u0073\u0074\u0072"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0062\u0073\u0074\u0072"}:_gagg :=NewCT_VectorChoice ();
if _gba :=d .DecodeElement (&_gagg .Bstr ,&_bfa );_gba !=nil {return _gba ;};_cdbf .VectorChoice =append (_cdbf .VectorChoice ,_gagg );case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0064\u0061\u0074\u0065"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0064\u0061\u0074\u0065"}:_cfef :=NewCT_VectorChoice ();
if _efge :=d .DecodeElement (&_cfef .Date ,&_bfa );_efge !=nil {return _efge ;};_cdbf .VectorChoice =append (_cdbf .VectorChoice ,_cfef );case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0066\u0069\u006c\u0065\u0074\u0069\u006d\u0065"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0066\u0069\u006c\u0065\u0074\u0069\u006d\u0065"}:_baee :=NewCT_VectorChoice ();
if _ffdc :=d .DecodeElement (&_baee .Filetime ,&_bfa );_ffdc !=nil {return _ffdc ;};_cdbf .VectorChoice =append (_cdbf .VectorChoice ,_baee );case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0062\u006f\u006f\u006c"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0062\u006f\u006f\u006c"}:_aae :=NewCT_VectorChoice ();
if _bda :=d .DecodeElement (&_aae .Bool ,&_bfa );_bda !=nil {return _bda ;};_cdbf .VectorChoice =append (_cdbf .VectorChoice ,_aae );case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0063\u0079"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0063\u0079"}:_bged :=NewCT_VectorChoice ();
if _def :=d .DecodeElement (&_bged .Cy ,&_bfa );_def !=nil {return _def ;};_cdbf .VectorChoice =append (_cdbf .VectorChoice ,_bged );case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0065\u0072\u0072o\u0072"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0065\u0072\u0072o\u0072"}:_gfb :=NewCT_VectorChoice ();
if _fbde :=d .DecodeElement (&_gfb .Error ,&_bfa );_fbde !=nil {return _fbde ;};_cdbf .VectorChoice =append (_cdbf .VectorChoice ,_gfb );case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0063\u006c\u0073i\u0064"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0063\u006c\u0073i\u0064"}:_geaae :=NewCT_VectorChoice ();
if _gcd :=d .DecodeElement (&_geaae .Clsid ,&_bfa );_gcd !=nil {return _gcd ;};_cdbf .VectorChoice =append (_cdbf .VectorChoice ,_geaae );default:_cb .Log .Debug ("\u0073k\u0069\u0070p\u0069\u006e\u0067\u0020u\u006e\u0073\u0075p\u0070\u006f\u0072\u0074\u0065\u0064\u0020\u0065\u006cem\u0065\u006e\u0074 \u006f\u006e \u0043\u0054\u005f\u0056\u0065\u0063t\u006f\u0072 \u0025\u0076",_bfa .Name );
if _dgad :=d .Skip ();_dgad !=nil {return _dgad ;};};case _eg .EndElement :break _ffc ;case _eg .CharData :};};return nil ;};func (_cgda *CT_Vstream )UnmarshalXML (d *_eg .Decoder ,start _eg .StartElement )error {_cgda .VersionAttr ="\u007b\u0030\u0030\u0030\u0030\u0030\u0030\u0030\u0030\u002d\u0030\u0030\u0030\u0030\u002d\u0030\u0030\u0030\u0030\u002d\u0030\u0030\u0030\u0030-\u0030\u0030\u0030\u0030\u00300\u0030\u00300\u0030\u0030\u0030\u007d";
for _ ,_dbfc :=range start .Attr {if _dbfc .Name .Local =="\u0076e\u0072\u0073\u0069\u006f\u006e"{_acda :=_dbfc .Value ;_cgda .VersionAttr =_acda ;continue ;};};for {_eccb ,_dcbd :=d .Token ();if _dcbd !=nil {return _ed .Errorf ("\u0070\u0061\u0072\u0073in\u0067\u0020\u0043\u0054\u005f\u0056\u0073\u0074\u0072\u0065\u0061\u006d\u003a\u0020%\u0073",_dcbd );
};if _fbe ,_dge :=_eccb .(_eg .CharData );_dge {_cgda .Content =string (_fbe );};if _egce ,_dagf :=_eccb .(_eg .EndElement );_dagf &&_egce .Name ==start .Name {break ;};};return nil ;};type CT_Null struct{};func (_gcbd ST_ArrayBaseType )MarshalXMLAttr (name _eg .Name )(_eg .Attr ,error ){_fgef :=_eg .Attr {};
_fgef .Name =name ;switch _gcbd {case ST_ArrayBaseTypeUnset :_fgef .Value ="";case ST_ArrayBaseTypeVariant :_fgef .Value ="\u0076a\u0072\u0069\u0061\u006e\u0074";case ST_ArrayBaseTypeI1 :_fgef .Value ="\u0069\u0031";case ST_ArrayBaseTypeI2 :_fgef .Value ="\u0069\u0032";
case ST_ArrayBaseTypeI4 :_fgef .Value ="\u0069\u0034";case ST_ArrayBaseTypeInt :_fgef .Value ="\u0069\u006e\u0074";case ST_ArrayBaseTypeUi1 :_fgef .Value ="\u0075\u0069\u0031";case ST_ArrayBaseTypeUi2 :_fgef .Value ="\u0075\u0069\u0032";case ST_ArrayBaseTypeUi4 :_fgef .Value ="\u0075\u0069\u0034";
case ST_ArrayBaseTypeUint :_fgef .Value ="\u0075\u0069\u006e\u0074";case ST_ArrayBaseTypeR4 :_fgef .Value ="\u0072\u0034";case ST_ArrayBaseTypeR8 :_fgef .Value ="\u0072\u0038";case ST_ArrayBaseTypeDecimal :_fgef .Value ="\u0064e\u0063\u0069\u006d\u0061\u006c";
case ST_ArrayBaseTypeBstr :_fgef .Value ="\u0062\u0073\u0074\u0072";case ST_ArrayBaseTypeDate :_fgef .Value ="\u0064\u0061\u0074\u0065";case ST_ArrayBaseTypeBool :_fgef .Value ="\u0062\u006f\u006f\u006c";case ST_ArrayBaseTypeCy :_fgef .Value ="\u0063\u0079";
case ST_ArrayBaseTypeError :_fgef .Value ="\u0065\u0072\u0072o\u0072";};return _fgef ,nil ;};func NewCT_Null ()*CT_Null {_caf :=&CT_Null {};return _caf };

// ValidateWithPath validates the CT_Null and its children, prefixing error messages with path
func (_fag *CT_Null )ValidateWithPath (path string )error {return nil };type CT_VectorChoice struct{Variant *Variant ;I1 *int8 ;I2 *int16 ;I4 *int32 ;I8 *int64 ;Ui1 *uint8 ;Ui2 *uint16 ;Ui4 *uint32 ;Ui8 *uint64 ;R4 *float32 ;R8 *float64 ;Lpstr *string ;
Lpwstr *string ;Bstr *string ;Date *_b .Time ;Filetime *_b .Time ;Bool *bool ;Cy *string ;Error *string ;Clsid *string ;};var ST_ErrorPatternRe =_c .MustCompile (ST_ErrorPattern );func NewCT_Empty ()*CT_Empty {_cdc :=&CT_Empty {};return _cdc };

// Validate validates the CT_Null and its children
func (_eag *CT_Null )Validate ()error {return _eag .ValidateWithPath ("\u0043T\u005f\u004e\u0075\u006c\u006c");};type Variant struct{CT_Variant };func (_eade *ST_VectorBaseType )UnmarshalXMLAttr (attr _eg .Attr )error {switch attr .Value {case "":*_eade =0;
case "\u0076a\u0072\u0069\u0061\u006e\u0074":*_eade =1;case "\u0069\u0031":*_eade =2;case "\u0069\u0032":*_eade =3;case "\u0069\u0034":*_eade =4;case "\u0069\u0038":*_eade =5;case "\u0075\u0069\u0031":*_eade =6;case "\u0075\u0069\u0032":*_eade =7;case "\u0075\u0069\u0034":*_eade =8;
case "\u0075\u0069\u0038":*_eade =9;case "\u0072\u0034":*_eade =10;case "\u0072\u0038":*_eade =11;case "\u006c\u0070\u0073t\u0072":*_eade =12;case "\u006c\u0070\u0077\u0073\u0074\u0072":*_eade =13;case "\u0062\u0073\u0074\u0072":*_eade =14;case "\u0064\u0061\u0074\u0065":*_eade =15;
case "\u0066\u0069\u006c\u0065\u0074\u0069\u006d\u0065":*_eade =16;case "\u0062\u006f\u006f\u006c":*_eade =17;case "\u0063\u0079":*_eade =18;case "\u0065\u0072\u0072o\u0072":*_eade =19;case "\u0063\u006c\u0073i\u0064":*_eade =20;};return nil ;};type CT_Vector struct{

// Vector Base Type
BaseTypeAttr ST_VectorBaseType ;

// Vector Size
SizeAttr uint32 ;VectorChoice []*CT_VectorChoice ;};func (_aba ST_VectorBaseType )Validate ()error {return _aba .ValidateWithPath ("")};func (_gef *Variant )MarshalXML (e *_eg .Encoder ,start _eg .StartElement )error {return _gef .CT_Variant .MarshalXML (e ,start );
};func (_deac *Vector )UnmarshalXML (d *_eg .Decoder ,start _eg .StartElement )error {_deac .CT_Vector =*NewCT_Vector ();for _ ,_agea :=range start .Attr {if _agea .Name .Local =="\u0062\u0061\u0073\u0065\u0054\u0079\u0070\u0065"{_deac .BaseTypeAttr .UnmarshalXMLAttr (_agea );
continue ;};if _agea .Name .Local =="\u0073\u0069\u007a\u0065"{_acc ,_adcec :=_ec .ParseUint (_agea .Value ,10,32);if _adcec !=nil {return _adcec ;};_deac .SizeAttr =uint32 (_acc );continue ;};};_bca :for {_ccdc ,_fgga :=d .Token ();if _fgga !=nil {return _fgga ;
};switch _cfc :=_ccdc .(type ){case _eg .StartElement :switch _cfc .Name {case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0076a\u0072\u0069\u0061\u006e\u0074"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0076a\u0072\u0069\u0061\u006e\u0074"}:_dce :=NewCT_VectorChoice ();
if _faef :=d .DecodeElement (&_dce .Variant ,&_cfc );_faef !=nil {return _faef ;};_deac .VectorChoice =append (_deac .VectorChoice ,_dce );case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0069\u0031"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0069\u0031"}:_gdcc :=NewCT_VectorChoice ();
if _faeb :=d .DecodeElement (&_gdcc .I1 ,&_cfc );_faeb !=nil {return _faeb ;};_deac .VectorChoice =append (_deac .VectorChoice ,_gdcc );case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0069\u0032"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0069\u0032"}:_ddcf :=NewCT_VectorChoice ();
if _cdbc :=d .DecodeElement (&_ddcf .I2 ,&_cfc );_cdbc !=nil {return _cdbc ;};_deac .VectorChoice =append (_deac .VectorChoice ,_ddcf );case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0069\u0034"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0069\u0034"}:_cda :=NewCT_VectorChoice ();
if _efeg :=d .DecodeElement (&_cda .I4 ,&_cfc );_efeg !=nil {return _efeg ;};_deac .VectorChoice =append (_deac .VectorChoice ,_cda );case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0069\u0038"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0069\u0038"}:_agfg :=NewCT_VectorChoice ();
if _cegf :=d .DecodeElement (&_agfg .I8 ,&_cfc );_cegf !=nil {return _cegf ;};_deac .VectorChoice =append (_deac .VectorChoice ,_agfg );case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0075\u0069\u0031"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0075\u0069\u0031"}:_fcbf :=NewCT_VectorChoice ();
if _gec :=d .DecodeElement (&_fcbf .Ui1 ,&_cfc );_gec !=nil {return _gec ;};_deac .VectorChoice =append (_deac .VectorChoice ,_fcbf );case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0075\u0069\u0032"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0075\u0069\u0032"}:_ceec :=NewCT_VectorChoice ();
if _cfgdb :=d .DecodeElement (&_ceec .Ui2 ,&_cfc );_cfgdb !=nil {return _cfgdb ;};_deac .VectorChoice =append (_deac .VectorChoice ,_ceec );case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0075\u0069\u0034"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0075\u0069\u0034"}:_gaff :=NewCT_VectorChoice ();
if _ecda :=d .DecodeElement (&_gaff .Ui4 ,&_cfc );_ecda !=nil {return _ecda ;};_deac .VectorChoice =append (_deac .VectorChoice ,_gaff );case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0075\u0069\u0038"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0075\u0069\u0038"}:_fed :=NewCT_VectorChoice ();
if _ceae :=d .DecodeElement (&_fed .Ui8 ,&_cfc );_ceae !=nil {return _ceae ;};_deac .VectorChoice =append (_deac .VectorChoice ,_fed );case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0072\u0034"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0072\u0034"}:_gafe :=NewCT_VectorChoice ();
if _fcf :=d .DecodeElement (&_gafe .R4 ,&_cfc );_fcf !=nil {return _fcf ;};_deac .VectorChoice =append (_deac .VectorChoice ,_gafe );case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0072\u0038"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0072\u0038"}:_dcdf :=NewCT_VectorChoice ();
if _abb :=d .DecodeElement (&_dcdf .R8 ,&_cfc );_abb !=nil {return _abb ;};_deac .VectorChoice =append (_deac .VectorChoice ,_dcdf );case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u006c\u0070\u0073t\u0072"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u006c\u0070\u0073t\u0072"}:_egcb :=NewCT_VectorChoice ();
if _acg :=d .DecodeElement (&_egcb .Lpstr ,&_cfc );_acg !=nil {return _acg ;};_deac .VectorChoice =append (_deac .VectorChoice ,_egcb );case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u006c\u0070\u0077\u0073\u0074\u0072"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u006c\u0070\u0077\u0073\u0074\u0072"}:_fddga :=NewCT_VectorChoice ();
if _cff :=d .DecodeElement (&_fddga .Lpwstr ,&_cfc );_cff !=nil {return _cff ;};_deac .VectorChoice =append (_deac .VectorChoice ,_fddga );case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0062\u0073\u0074\u0072"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0062\u0073\u0074\u0072"}:_ced :=NewCT_VectorChoice ();
if _dac :=d .DecodeElement (&_ced .Bstr ,&_cfc );_dac !=nil {return _dac ;};_deac .VectorChoice =append (_deac .VectorChoice ,_ced );case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0064\u0061\u0074\u0065"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0064\u0061\u0074\u0065"}:_eeba :=NewCT_VectorChoice ();
if _cdf :=d .DecodeElement (&_eeba .Date ,&_cfc );_cdf !=nil {return _cdf ;};_deac .VectorChoice =append (_deac .VectorChoice ,_eeba );case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0066\u0069\u006c\u0065\u0074\u0069\u006d\u0065"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0066\u0069\u006c\u0065\u0074\u0069\u006d\u0065"}:_fddd :=NewCT_VectorChoice ();
if _cgea :=d .DecodeElement (&_fddd .Filetime ,&_cfc );_cgea !=nil {return _cgea ;};_deac .VectorChoice =append (_deac .VectorChoice ,_fddd );case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0062\u006f\u006f\u006c"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0062\u006f\u006f\u006c"}:_gada :=NewCT_VectorChoice ();
if _dgdf :=d .DecodeElement (&_gada .Bool ,&_cfc );_dgdf !=nil {return _dgdf ;};_deac .VectorChoice =append (_deac .VectorChoice ,_gada );case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0063\u0079"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0063\u0079"}:_edef :=NewCT_VectorChoice ();
if _dcab :=d .DecodeElement (&_edef .Cy ,&_cfc );_dcab !=nil {return _dcab ;};_deac .VectorChoice =append (_deac .VectorChoice ,_edef );case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0065\u0072\u0072o\u0072"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0065\u0072\u0072o\u0072"}:_fgaa :=NewCT_VectorChoice ();
if _dcbdb :=d .DecodeElement (&_fgaa .Error ,&_cfc );_dcbdb !=nil {return _dcbdb ;};_deac .VectorChoice =append (_deac .VectorChoice ,_fgaa );case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0063\u006c\u0073i\u0064"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0063\u006c\u0073i\u0064"}:_dcf :=NewCT_VectorChoice ();
if _gagd :=d .DecodeElement (&_dcf .Clsid ,&_cfc );_gagd !=nil {return _gagd ;};_deac .VectorChoice =append (_deac .VectorChoice ,_dcf );default:_cb .Log .Debug ("\u0073\u006b\u0069\u0070\u0070i\u006e\u0067\u0020\u0075\u006e\u0073\u0075\u0070\u0070\u006f\u0072\u0074\u0065d\u0020\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0056\u0065\u0063\u0074\u006f\u0072\u0020\u0025\u0076",_cfc .Name );
if _bgb :=d .Skip ();_bgb !=nil {return _bgb ;};};case _eg .EndElement :break _bca ;case _eg .CharData :};};return nil ;};

// ValidateWithPath validates the CT_VectorChoice and its children, prefixing error messages with path
func (_cca *CT_VectorChoice )ValidateWithPath (path string )error {if _cca .Variant !=nil {if _fgge :=_cca .Variant .ValidateWithPath (path +"\u002f\u0056\u0061\u0072\u0069\u0061\u006e\u0074");_fgge !=nil {return _fgge ;};};if _cca .Cy !=nil {if !ST_CyPatternRe .MatchString (*_cca .Cy ){return _ed .Errorf ("\u0025\u0073\u002f\u006d\u002e\u0043y\u0020\u006d\u0075\u0073\u0074\u0020\u006d\u0061\u0074\u0063\u0068\u0020\u0027%\u0073\u0027\u0020\u0028\u0068\u0061\u0076e\u0020\u0025\u0076\u0029",path ,ST_CyPatternRe ,*_cca .Cy );
};};if _cca .Error !=nil {if !ST_ErrorPatternRe .MatchString (*_cca .Error ){return _ed .Errorf ("\u0025\u0073/m\u002e\u0045\u0072r\u006f\u0072\u0020\u006dust\u0020ma\u0074\u0063\u0068\u0020\u0027\u0025\u0073' \u0028\u0068\u0061\u0076\u0065\u0020\u0025v\u0029",path ,ST_ErrorPatternRe ,*_cca .Error );
};};if _cca .Clsid !=nil {if !_g .ST_GuidPatternRe .MatchString (*_cca .Clsid ){return _ed .Errorf ("\u0025\u0073/m\u002e\u0043\u006cs\u0069\u0064\u0020\u006dust\u0020ma\u0074\u0063\u0068\u0020\u0027\u0025\u0073' \u0028\u0068\u0061\u0076\u0065\u0020\u0025v\u0029",path ,_g .ST_GuidPatternRe ,*_cca .Clsid );
};};return nil ;};

// Validate validates the Vector and its children
func (_abbe *Vector )Validate ()error {return _abbe .ValidateWithPath ("\u0056\u0065\u0063\u0074\u006f\u0072");};type CT_Vstream struct{VersionAttr string ;Content string ;};func NewEmpty ()*Empty {_abg :=&Empty {};_abg .CT_Empty =*NewCT_Empty ();return _abg };


// Validate validates the Array and its children
func (_cbc *Array )Validate ()error {return _cbc .ValidateWithPath ("\u0041\u0072\u0072a\u0079")};const ST_ErrorPattern ="\u005c\u0073\u002a\u0030x[\u0030\u002d\u0039\u0041\u002d\u005a\u0061\u002d\u007a\u005d\u007b\u0038\u007d\u005cs\u002a";func (_gde *CT_Empty )MarshalXML (e *_eg .Encoder ,start _eg .StartElement )error {e .EncodeToken (start );
e .EncodeToken (_eg .EndElement {Name :start .Name });return nil ;};func (_acd *CT_Empty )UnmarshalXML (d *_eg .Decoder ,start _eg .StartElement )error {for {_ccc ,_dgf :=d .Token ();if _dgf !=nil {return _ed .Errorf ("p\u0061r\u0073\u0069\u006e\u0067\u0020\u0043\u0054\u005fE\u006d\u0070\u0074\u0079: \u0025\u0073",_dgf );
};if _eec ,_baf :=_ccc .(_eg .EndElement );_baf &&_eec .Name ==start .Name {break ;};};return nil ;};

// ValidateWithPath validates the Empty and its children, prefixing error messages with path
func (_dgfa *Empty )ValidateWithPath (path string )error {if _gcba :=_dgfa .CT_Empty .ValidateWithPath (path );_gcba !=nil {return _gcba ;};return nil ;};func NewCT_Vstream ()*CT_Vstream {_afg :=&CT_Vstream {};_afg .VersionAttr ="\u007b\u0030\u0030\u0030\u0030\u0030\u0030\u0030\u0030\u002d\u0030\u0030\u0030\u0030\u002d\u0030\u0030\u0030\u0030\u002d\u0030\u0030\u0030\u0030-\u0030\u0030\u0030\u0030\u00300\u0030\u00300\u0030\u0030\u0030\u007d";
return _afg ;};

// Validate validates the CT_VectorChoice and its children
func (_dfa *CT_VectorChoice )Validate ()error {return _dfa .ValidateWithPath ("\u0043T\u005fV\u0065\u0063\u0074\u006f\u0072\u0043\u0068\u006f\u0069\u0063\u0065");};type Array struct{CT_Array };func (_baae *CT_ArrayChoice )MarshalXML (e *_eg .Encoder ,start _eg .StartElement )error {e .EncodeToken (start );
if _baae .Variant !=nil {_bac :=_eg .StartElement {Name :_eg .Name {Local :"\u0076\u0074\u003a\u0076\u0061\u0072\u0069\u0061\u006e\u0074"}};e .EncodeElement (_baae .Variant ,_bac );}else if _baae .I1 !=nil {_cee :=_eg .StartElement {Name :_eg .Name {Local :"\u0076\u0074\u003ai\u0031"}};
e .EncodeElement (_baae .I1 ,_cee );}else if _baae .I2 !=nil {_fce :=_eg .StartElement {Name :_eg .Name {Local :"\u0076\u0074\u003ai\u0032"}};e .EncodeElement (_baae .I2 ,_fce );}else if _baae .I4 !=nil {_edaa :=_eg .StartElement {Name :_eg .Name {Local :"\u0076\u0074\u003ai\u0034"}};
e .EncodeElement (_baae .I4 ,_edaa );}else if _baae .Int !=nil {_dgc :=_eg .StartElement {Name :_eg .Name {Local :"\u0076\u0074\u003a\u0069\u006e\u0074"}};e .EncodeElement (_baae .Int ,_dgc );}else if _baae .Ui1 !=nil {_efa :=_eg .StartElement {Name :_eg .Name {Local :"\u0076\u0074\u003a\u0075\u0069\u0031"}};
e .EncodeElement (_baae .Ui1 ,_efa );}else if _baae .Ui2 !=nil {_ccbe :=_eg .StartElement {Name :_eg .Name {Local :"\u0076\u0074\u003a\u0075\u0069\u0032"}};e .EncodeElement (_baae .Ui2 ,_ccbe );}else if _baae .Ui4 !=nil {_ggf :=_eg .StartElement {Name :_eg .Name {Local :"\u0076\u0074\u003a\u0075\u0069\u0034"}};
e .EncodeElement (_baae .Ui4 ,_ggf );}else if _baae .Uint !=nil {_bgc :=_eg .StartElement {Name :_eg .Name {Local :"\u0076t\u003a\u0075\u0069\u006e\u0074"}};e .EncodeElement (_baae .Uint ,_bgc );}else if _baae .R4 !=nil {_bbg :=_eg .StartElement {Name :_eg .Name {Local :"\u0076\u0074\u003ar\u0034"}};
e .EncodeElement (_baae .R4 ,_bbg );}else if _baae .R8 !=nil {_egec :=_eg .StartElement {Name :_eg .Name {Local :"\u0076\u0074\u003ar\u0038"}};e .EncodeElement (_baae .R8 ,_egec );}else if _baae .Decimal !=nil {_ggb :=_eg .StartElement {Name :_eg .Name {Local :"\u0076\u0074\u003a\u0064\u0065\u0063\u0069\u006d\u0061\u006c"}};
e .EncodeElement (_baae .Decimal ,_ggb );}else if _baae .Bstr !=nil {_gge :=_eg .StartElement {Name :_eg .Name {Local :"\u0076t\u003a\u0062\u0073\u0074\u0072"}};_ea .AddPreserveSpaceAttr (&_gge ,*_baae .Bstr );e .EncodeElement (_baae .Bstr ,_gge );}else if _baae .Date !=nil {_bbge :=_eg .StartElement {Name :_eg .Name {Local :"\u0076t\u003a\u0064\u0061\u0074\u0065"}};
e .EncodeElement (_g .FormatDate (*_baae .Date ),_bbge );}else if _baae .Bool !=nil {_eb :=_eg .StartElement {Name :_eg .Name {Local :"\u0076t\u003a\u0062\u006f\u006f\u006c"}};e .EncodeElement (_baae .Bool ,_eb );}else if _baae .Error !=nil {_cbg :=_eg .StartElement {Name :_eg .Name {Local :"\u0076\u0074\u003a\u0065\u0072\u0072\u006f\u0072"}};
_ea .AddPreserveSpaceAttr (&_cbg ,*_baae .Error );e .EncodeElement (_baae .Error ,_cbg );}else if _baae .Cy !=nil {_ecc :=_eg .StartElement {Name :_eg .Name {Local :"\u0076\u0074\u003ac\u0079"}};_ea .AddPreserveSpaceAttr (&_ecc ,*_baae .Cy );e .EncodeElement (_baae .Cy ,_ecc );
};e .EncodeToken (_eg .EndElement {Name :start .Name });return nil ;};func NewVstream ()*Vstream {_gca :=&Vstream {};_gca .CT_Vstream =*NewCT_Vstream ();return _gca };func (_ebfg *ST_VectorBaseType )UnmarshalXML (d *_eg .Decoder ,start _eg .StartElement )error {_gfce ,_ggcd :=d .Token ();
if _ggcd !=nil {return _ggcd ;};if _edab ,_dcfb :=_gfce .(_eg .EndElement );_dcfb &&_edab .Name ==start .Name {*_ebfg =1;return nil ;};if _baab ,_afa :=_gfce .(_eg .CharData );!_afa {return _ed .Errorf ("\u0065\u0078\u0070\u0065\u0063\u0074\u0065\u0064\u0020\u0063\u0068a\u0072\u0020\u0064\u0061\u0074\u0061\u002c\u0020\u0067\u006ft\u0020\u0025\u0054",_gfce );
}else {switch string (_baab ){case "":*_ebfg =0;case "\u0076a\u0072\u0069\u0061\u006e\u0074":*_ebfg =1;case "\u0069\u0031":*_ebfg =2;case "\u0069\u0032":*_ebfg =3;case "\u0069\u0034":*_ebfg =4;case "\u0069\u0038":*_ebfg =5;case "\u0075\u0069\u0031":*_ebfg =6;
case "\u0075\u0069\u0032":*_ebfg =7;case "\u0075\u0069\u0034":*_ebfg =8;case "\u0075\u0069\u0038":*_ebfg =9;case "\u0072\u0034":*_ebfg =10;case "\u0072\u0038":*_ebfg =11;case "\u006c\u0070\u0073t\u0072":*_ebfg =12;case "\u006c\u0070\u0077\u0073\u0074\u0072":*_ebfg =13;
case "\u0062\u0073\u0074\u0072":*_ebfg =14;case "\u0064\u0061\u0074\u0065":*_ebfg =15;case "\u0066\u0069\u006c\u0065\u0074\u0069\u006d\u0065":*_ebfg =16;case "\u0062\u006f\u006f\u006c":*_ebfg =17;case "\u0063\u0079":*_ebfg =18;case "\u0065\u0072\u0072o\u0072":*_ebfg =19;
case "\u0063\u006c\u0073i\u0064":*_ebfg =20;};};_gfce ,_ggcd =d .Token ();if _ggcd !=nil {return _ggcd ;};if _cfcb ,_bab :=_gfce .(_eg .EndElement );_bab &&_cfcb .Name ==start .Name {return nil ;};return _ed .Errorf ("\u0065\u0078\u0070\u0065c\u0074\u0065\u0064\u0020\u0065\u006e\u0064\u0020\u0065\u006ce\u006de\u006e\u0074\u002c\u0020\u0067\u006f\u0074 \u0025\u0076",_gfce );
};const (ST_ArrayBaseTypeUnset ST_ArrayBaseType =0;ST_ArrayBaseTypeVariant ST_ArrayBaseType =1;ST_ArrayBaseTypeI1 ST_ArrayBaseType =2;ST_ArrayBaseTypeI2 ST_ArrayBaseType =3;ST_ArrayBaseTypeI4 ST_ArrayBaseType =4;ST_ArrayBaseTypeInt ST_ArrayBaseType =5;
ST_ArrayBaseTypeUi1 ST_ArrayBaseType =6;ST_ArrayBaseTypeUi2 ST_ArrayBaseType =7;ST_ArrayBaseTypeUi4 ST_ArrayBaseType =8;ST_ArrayBaseTypeUint ST_ArrayBaseType =9;ST_ArrayBaseTypeR4 ST_ArrayBaseType =10;ST_ArrayBaseTypeR8 ST_ArrayBaseType =11;ST_ArrayBaseTypeDecimal ST_ArrayBaseType =12;
ST_ArrayBaseTypeBstr ST_ArrayBaseType =13;ST_ArrayBaseTypeDate ST_ArrayBaseType =14;ST_ArrayBaseTypeBool ST_ArrayBaseType =15;ST_ArrayBaseTypeCy ST_ArrayBaseType =16;ST_ArrayBaseTypeError ST_ArrayBaseType =17;);func NewCT_VariantChoice ()*CT_VariantChoice {_gfcg :=&CT_VariantChoice {};
return _gfcg };

// Validate validates the CT_Vstream and its children
func (_gdab *CT_Vstream )Validate ()error {return _gdab .ValidateWithPath ("\u0043\u0054\u005f\u0056\u0073\u0074\u0072\u0065\u0061\u006d");};const (ST_VectorBaseTypeUnset ST_VectorBaseType =0;ST_VectorBaseTypeVariant ST_VectorBaseType =1;ST_VectorBaseTypeI1 ST_VectorBaseType =2;
ST_VectorBaseTypeI2 ST_VectorBaseType =3;ST_VectorBaseTypeI4 ST_VectorBaseType =4;ST_VectorBaseTypeI8 ST_VectorBaseType =5;ST_VectorBaseTypeUi1 ST_VectorBaseType =6;ST_VectorBaseTypeUi2 ST_VectorBaseType =7;ST_VectorBaseTypeUi4 ST_VectorBaseType =8;ST_VectorBaseTypeUi8 ST_VectorBaseType =9;
ST_VectorBaseTypeR4 ST_VectorBaseType =10;ST_VectorBaseTypeR8 ST_VectorBaseType =11;ST_VectorBaseTypeLpstr ST_VectorBaseType =12;ST_VectorBaseTypeLpwstr ST_VectorBaseType =13;ST_VectorBaseTypeBstr ST_VectorBaseType =14;ST_VectorBaseTypeDate ST_VectorBaseType =15;
ST_VectorBaseTypeFiletime ST_VectorBaseType =16;ST_VectorBaseTypeBool ST_VectorBaseType =17;ST_VectorBaseTypeCy ST_VectorBaseType =18;ST_VectorBaseTypeError ST_VectorBaseType =19;ST_VectorBaseTypeClsid ST_VectorBaseType =20;);

// Validate validates the Null and its children
func (_gdabf *Null )Validate ()error {return _gdabf .ValidateWithPath ("\u004e\u0075\u006c\u006c")};func NewCT_Array ()*CT_Array {_cc :=&CT_Array {};_cc .BaseTypeAttr =ST_ArrayBaseType (1);return _cc };type CT_Array struct{

// Array Lower Bounds Attribute
LBoundsAttr int32 ;

// Array Upper Bounds Attribute
UBoundsAttr int32 ;

// Array Base Type
BaseTypeAttr ST_ArrayBaseType ;ArrayChoice []*CT_ArrayChoice ;};func (_eca *CT_Vstream )MarshalXML (e *_eg .Encoder ,start _eg .StartElement )error {start .Attr =append (start .Attr ,_eg .Attr {Name :_eg .Name {Local :"\u0076e\u0072\u0073\u0069\u006f\u006e"},Value :_ed .Sprintf ("\u0025\u0076",_eca .VersionAttr )});
e .EncodeElement (_eca .Content ,start );e .EncodeToken (_eg .EndElement {Name :start .Name });return nil ;};func (_cfgg *CT_VariantChoice )UnmarshalXML (d *_eg .Decoder ,start _eg .StartElement )error {_daaa :=start ;switch start .Name {case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0076a\u0072\u0069\u0061\u006e\u0074"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0076a\u0072\u0069\u0061\u006e\u0074"}:_cfgg .Variant =NewVariant ();
if _faff :=d .DecodeElement (_cfgg .Variant ,&_daaa );_faff !=nil {return _faff ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0076\u0065\u0063\u0074\u006f\u0072"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0076\u0065\u0063\u0074\u006f\u0072"}:_cfgg .Vector =NewVector ();
if _gdd :=d .DecodeElement (_cfgg .Vector ,&_daaa );_gdd !=nil {return _gdd ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0061\u0072\u0072a\u0079"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0061\u0072\u0072a\u0079"}:_cfgg .Array =NewArray ();
if _ddc :=d .DecodeElement (_cfgg .Array ,&_daaa );_ddc !=nil {return _ddc ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0062\u006c\u006f\u0062"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0062\u006c\u006f\u0062"}:_cfgg .Blob =new (string );
if _fbbd :=d .DecodeElement (_cfgg .Blob ,&_daaa );_fbbd !=nil {return _fbbd ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u006f\u0062\u006co\u0062"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u006f\u0062\u006co\u0062"}:_cfgg .Oblob =new (string );
if _aeaf :=d .DecodeElement (_cfgg .Oblob ,&_daaa );_aeaf !=nil {return _aeaf ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0065\u006d\u0070t\u0079"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0065\u006d\u0070t\u0079"}:_cfgg .Empty =NewEmpty ();
if _dgb :=d .DecodeElement (_cfgg .Empty ,&_daaa );_dgb !=nil {return _dgb ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u006e\u0075\u006c\u006c"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u006e\u0075\u006c\u006c"}:_cfgg .Null =NewNull ();
if _eggd :=d .DecodeElement (_cfgg .Null ,&_daaa );_eggd !=nil {return _eggd ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0069\u0031"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0069\u0031"}:_cfgg .I1 =new (int8 );
if _cecd :=d .DecodeElement (_cfgg .I1 ,&_daaa );_cecd !=nil {return _cecd ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0069\u0032"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0069\u0032"}:_cfgg .I2 =new (int16 );
if _ggcc :=d .DecodeElement (_cfgg .I2 ,&_daaa );_ggcc !=nil {return _ggcc ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0069\u0034"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0069\u0034"}:_cfgg .I4 =new (int32 );
if _ffg :=d .DecodeElement (_cfgg .I4 ,&_daaa );_ffg !=nil {return _ffg ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0069\u0038"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0069\u0038"}:_cfgg .I8 =new (int64 );
if _bgfg :=d .DecodeElement (_cfgg .I8 ,&_daaa );_bgfg !=nil {return _bgfg ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0069\u006e\u0074"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0069\u006e\u0074"}:_cfgg .Int =new (int32 );
if _cbb :=d .DecodeElement (_cfgg .Int ,&_daaa );_cbb !=nil {return _cbb ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0075\u0069\u0031"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0075\u0069\u0031"}:_cfgg .Ui1 =new (uint8 );
if _agad :=d .DecodeElement (_cfgg .Ui1 ,&_daaa );_agad !=nil {return _agad ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0075\u0069\u0032"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0075\u0069\u0032"}:_cfgg .Ui2 =new (uint16 );
if _fgg :=d .DecodeElement (_cfgg .Ui2 ,&_daaa );_fgg !=nil {return _fgg ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0075\u0069\u0034"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0075\u0069\u0034"}:_cfgg .Ui4 =new (uint32 );
if _agada :=d .DecodeElement (_cfgg .Ui4 ,&_daaa );_agada !=nil {return _agada ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0075\u0069\u0038"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0075\u0069\u0038"}:_cfgg .Ui8 =new (uint64 );
if _ddfc :=d .DecodeElement (_cfgg .Ui8 ,&_daaa );_ddfc !=nil {return _ddfc ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0075\u0069\u006e\u0074"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0075\u0069\u006e\u0074"}:_cfgg .Uint =new (uint32 );
if _bafe :=d .DecodeElement (_cfgg .Uint ,&_daaa );_bafe !=nil {return _bafe ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0072\u0034"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0072\u0034"}:_cfgg .R4 =new (float32 );
if _dccf :=d .DecodeElement (_cfgg .R4 ,&_daaa );_dccf !=nil {return _dccf ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0072\u0038"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0072\u0038"}:_cfgg .R8 =new (float64 );
if _abe :=d .DecodeElement (_cfgg .R8 ,&_daaa );_abe !=nil {return _abe ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0064e\u0063\u0069\u006d\u0061\u006c"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0064e\u0063\u0069\u006d\u0061\u006c"}:_cfgg .Decimal =new (float64 );
if _ebf :=d .DecodeElement (_cfgg .Decimal ,&_daaa );_ebf !=nil {return _ebf ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u006c\u0070\u0073t\u0072"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u006c\u0070\u0073t\u0072"}:_cfgg .Lpstr =new (string );
if _fda :=d .DecodeElement (_cfgg .Lpstr ,&_daaa );_fda !=nil {return _fda ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u006c\u0070\u0077\u0073\u0074\u0072"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u006c\u0070\u0077\u0073\u0074\u0072"}:_cfgg .Lpwstr =new (string );
if _fgad :=d .DecodeElement (_cfgg .Lpwstr ,&_daaa );_fgad !=nil {return _fgad ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0062\u0073\u0074\u0072"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0062\u0073\u0074\u0072"}:_cfgg .Bstr =new (string );
if _gad :=d .DecodeElement (_cfgg .Bstr ,&_daaa );_gad !=nil {return _gad ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0064\u0061\u0074\u0065"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0064\u0061\u0074\u0065"}:var _ggba string ;
if _ede :=d .DecodeElement (&_ggba ,&_daaa );_ede !=nil {return _ede ;};_gdf ,_agdg :=_g .ParseStdlibTime (_ggba );if _agdg !=nil {return _agdg ;};_cfgg .Date =&_gdf ;case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0066\u0069\u006c\u0065\u0074\u0069\u006d\u0065"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0066\u0069\u006c\u0065\u0074\u0069\u006d\u0065"}:_cfgg .Filetime =new (_b .Time );
if _cbeg :=d .DecodeElement (_cfgg .Filetime ,&_daaa );_cbeg !=nil {return _cbeg ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0062\u006f\u006f\u006c"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0062\u006f\u006f\u006c"}:_cfgg .Bool =new (bool );
if _gcbb :=d .DecodeElement (_cfgg .Bool ,&_daaa );_gcbb !=nil {return _gcbb ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0063\u0079"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0063\u0079"}:_cfgg .Cy =new (string );
if _bge :=d .DecodeElement (_cfgg .Cy ,&_daaa );_bge !=nil {return _bge ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0065\u0072\u0072o\u0072"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0065\u0072\u0072o\u0072"}:_cfgg .Error =new (string );
if _gae :=d .DecodeElement (_cfgg .Error ,&_daaa );_gae !=nil {return _gae ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0073\u0074\u0072\u0065\u0061\u006d"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0073\u0074\u0072\u0065\u0061\u006d"}:_cfgg .Stream =new (string );
if _ddb :=d .DecodeElement (_cfgg .Stream ,&_daaa );_ddb !=nil {return _ddb ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u006fs\u0074\u0072\u0065\u0061\u006d"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u006fs\u0074\u0072\u0065\u0061\u006d"}:_cfgg .Ostream =new (string );
if _dadg :=d .DecodeElement (_cfgg .Ostream ,&_daaa );_dadg !=nil {return _dadg ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0073t\u006f\u0072\u0061\u0067\u0065"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0073t\u006f\u0072\u0061\u0067\u0065"}:_cfgg .Storage =new (string );
if _fdb :=d .DecodeElement (_cfgg .Storage ,&_daaa );_fdb !=nil {return _fdb ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u006f\u0073\u0074\u006f\u0072\u0061\u0067\u0065"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u006f\u0073\u0074\u006f\u0072\u0061\u0067\u0065"}:_cfgg .Ostorage =new (string );
if _gbcb :=d .DecodeElement (_cfgg .Ostorage ,&_daaa );_gbcb !=nil {return _gbcb ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0076s\u0074\u0072\u0065\u0061\u006d"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0076s\u0074\u0072\u0065\u0061\u006d"}:_cfgg .Vstream =NewVstream ();
if _eggc :=d .DecodeElement (_cfgg .Vstream ,&_daaa );_eggc !=nil {return _eggc ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0063\u006c\u0073i\u0064"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0063\u006c\u0073i\u0064"}:_cfgg .Clsid =new (string );
if _bae :=d .DecodeElement (_cfgg .Clsid ,&_daaa );_bae !=nil {return _bae ;};default:_cb .Log .Debug ("\u0073\u006b\u0069\u0070\u0070i\u006e\u0067\u0020\u0075\u006e\u0073\u0075\u0070\u0070\u006f\u0072\u0074\u0065d\u0020\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0043\u0054\u005f\u0056\u0061\u0072\u0069\u0061\u006e\u0074\u0043\u0068\u006f\u0069\u0063\u0065\u0020\u0025v",_daaa .Name );
if _cdcc :=d .Skip ();_cdcc !=nil {return _cdcc ;};};return nil ;};func NewCT_Variant ()*CT_Variant {_dfc :=&CT_Variant {};_dfc .VariantChoice =NewCT_VariantChoice ();return _dfc ;};

// ValidateWithPath validates the CT_Vstream and its children, prefixing error messages with path
func (_aaaa *CT_Vstream )ValidateWithPath (path string )error {if !_g .ST_GuidPatternRe .MatchString (_aaaa .VersionAttr ){return _ed .Errorf ("\u0025\u0073\u002fm\u002e\u0056\u0065\u0072\u0073\u0069\u006f\u006e\u0041\u0074\u0074\u0072\u0020\u006d\u0075\u0073\u0074\u0020\u006d\u0061\u0074\u0063\u0068\u0020\u0027\u0025\u0073\u0027\u0020(\u0068\u0061\u0076\u0065\u0020\u0025\u0076\u0029",path ,_g .ST_GuidPatternRe ,_aaaa .VersionAttr );
};return nil ;};func (_aegf ST_ArrayBaseType )Validate ()error {return _aegf .ValidateWithPath ("")};

// ValidateWithPath validates the Vstream and its children, prefixing error messages with path
func (_cbbe *Vstream )ValidateWithPath (path string )error {if _efgb :=_cbbe .CT_Vstream .ValidateWithPath (path );_efgb !=nil {return _efgb ;};return nil ;};type Null struct{CT_Null };type ST_ArrayBaseType byte ;func (_dcae *Null )UnmarshalXML (d *_eg .Decoder ,start _eg .StartElement )error {_dcae .CT_Null =*NewCT_Null ();
for {_aecb ,_dbcd :=d .Token ();if _dbcd !=nil {return _ed .Errorf ("\u0070\u0061r\u0073\u0069\u006eg\u0020\u004e\u0075\u006c\u006c\u003a\u0020\u0025\u0073",_dbcd );};if _baefd ,_gbg :=_aecb .(_eg .EndElement );_gbg &&_baefd .Name ==start .Name {break ;
};};return nil ;};type CT_Empty struct{};func (_eaa *Array )MarshalXML (e *_eg .Encoder ,start _eg .StartElement )error {return _eaa .CT_Array .MarshalXML (e ,start );};

// ValidateWithPath validates the CT_ArrayChoice and its children, prefixing error messages with path
func (_ga *CT_ArrayChoice )ValidateWithPath (path string )error {if _ga .Variant !=nil {if _fcc :=_ga .Variant .ValidateWithPath (path +"\u002f\u0056\u0061\u0072\u0069\u0061\u006e\u0074");_fcc !=nil {return _fcc ;};};if _ga .Error !=nil {if !ST_ErrorPatternRe .MatchString (*_ga .Error ){return _ed .Errorf ("\u0025\u0073/m\u002e\u0045\u0072r\u006f\u0072\u0020\u006dust\u0020ma\u0074\u0063\u0068\u0020\u0027\u0025\u0073' \u0028\u0068\u0061\u0076\u0065\u0020\u0025v\u0029",path ,ST_ErrorPatternRe ,*_ga .Error );
};};if _ga .Cy !=nil {if !ST_CyPatternRe .MatchString (*_ga .Cy ){return _ed .Errorf ("\u0025\u0073\u002f\u006d\u002e\u0043y\u0020\u006d\u0075\u0073\u0074\u0020\u006d\u0061\u0074\u0063\u0068\u0020\u0027%\u0073\u0027\u0020\u0028\u0068\u0061\u0076e\u0020\u0025\u0076\u0029",path ,ST_CyPatternRe ,*_ga .Cy );
};};return nil ;};func (_gab ST_VectorBaseType )MarshalXMLAttr (name _eg .Name )(_eg .Attr ,error ){_ggdfa :=_eg .Attr {};_ggdfa .Name =name ;switch _gab {case ST_VectorBaseTypeUnset :_ggdfa .Value ="";case ST_VectorBaseTypeVariant :_ggdfa .Value ="\u0076a\u0072\u0069\u0061\u006e\u0074";
case ST_VectorBaseTypeI1 :_ggdfa .Value ="\u0069\u0031";case ST_VectorBaseTypeI2 :_ggdfa .Value ="\u0069\u0032";case ST_VectorBaseTypeI4 :_ggdfa .Value ="\u0069\u0034";case ST_VectorBaseTypeI8 :_ggdfa .Value ="\u0069\u0038";case ST_VectorBaseTypeUi1 :_ggdfa .Value ="\u0075\u0069\u0031";
case ST_VectorBaseTypeUi2 :_ggdfa .Value ="\u0075\u0069\u0032";case ST_VectorBaseTypeUi4 :_ggdfa .Value ="\u0075\u0069\u0034";case ST_VectorBaseTypeUi8 :_ggdfa .Value ="\u0075\u0069\u0038";case ST_VectorBaseTypeR4 :_ggdfa .Value ="\u0072\u0034";case ST_VectorBaseTypeR8 :_ggdfa .Value ="\u0072\u0038";
case ST_VectorBaseTypeLpstr :_ggdfa .Value ="\u006c\u0070\u0073t\u0072";case ST_VectorBaseTypeLpwstr :_ggdfa .Value ="\u006c\u0070\u0077\u0073\u0074\u0072";case ST_VectorBaseTypeBstr :_ggdfa .Value ="\u0062\u0073\u0074\u0072";case ST_VectorBaseTypeDate :_ggdfa .Value ="\u0064\u0061\u0074\u0065";
case ST_VectorBaseTypeFiletime :_ggdfa .Value ="\u0066\u0069\u006c\u0065\u0074\u0069\u006d\u0065";case ST_VectorBaseTypeBool :_ggdfa .Value ="\u0062\u006f\u006f\u006c";case ST_VectorBaseTypeCy :_ggdfa .Value ="\u0063\u0079";case ST_VectorBaseTypeError :_ggdfa .Value ="\u0065\u0072\u0072o\u0072";
case ST_VectorBaseTypeClsid :_ggdfa .Value ="\u0063\u006c\u0073i\u0064";};return _ggdfa ,nil ;};func NewVector ()*Vector {_dbge :=&Vector {};_dbge .CT_Vector =*NewCT_Vector ();return _dbge };func (_afcb *ST_ArrayBaseType )UnmarshalXMLAttr (attr _eg .Attr )error {switch attr .Value {case "":*_afcb =0;
case "\u0076a\u0072\u0069\u0061\u006e\u0074":*_afcb =1;case "\u0069\u0031":*_afcb =2;case "\u0069\u0032":*_afcb =3;case "\u0069\u0034":*_afcb =4;case "\u0069\u006e\u0074":*_afcb =5;case "\u0075\u0069\u0031":*_afcb =6;case "\u0075\u0069\u0032":*_afcb =7;
case "\u0075\u0069\u0034":*_afcb =8;case "\u0075\u0069\u006e\u0074":*_afcb =9;case "\u0072\u0034":*_afcb =10;case "\u0072\u0038":*_afcb =11;case "\u0064e\u0063\u0069\u006d\u0061\u006c":*_afcb =12;case "\u0062\u0073\u0074\u0072":*_afcb =13;case "\u0064\u0061\u0074\u0065":*_afcb =14;
case "\u0062\u006f\u006f\u006c":*_afcb =15;case "\u0063\u0079":*_afcb =16;case "\u0065\u0072\u0072o\u0072":*_afcb =17;};return nil ;};func (_dcaa *CT_Vector )MarshalXML (e *_eg .Encoder ,start _eg .StartElement )error {_dee ,_aaf :=_dcaa .BaseTypeAttr .MarshalXMLAttr (_eg .Name {Local :"\u0062\u0061\u0073\u0065\u0054\u0079\u0070\u0065"});
if _aaf !=nil {return _aaf ;};start .Attr =append (start .Attr ,_dee );start .Attr =append (start .Attr ,_eg .Attr {Name :_eg .Name {Local :"\u0073\u0069\u007a\u0065"},Value :_ed .Sprintf ("\u0025\u0076",_dcaa .SizeAttr )});e .EncodeToken (start );for _ ,_gag :=range _dcaa .VectorChoice {_gag .MarshalXML (e ,_eg .StartElement {});
};e .EncodeToken (_eg .EndElement {Name :start .Name });return nil ;};func NewCT_VectorChoice ()*CT_VectorChoice {_gedc :=&CT_VectorChoice {};return _gedc };func (_aacf *Vstream )MarshalXML (e *_eg .Encoder ,start _eg .StartElement )error {return _aacf .CT_Vstream .MarshalXML (e ,start );
};

// Validate validates the CT_VariantChoice and its children
func (_ccg *CT_VariantChoice )Validate ()error {return _ccg .ValidateWithPath ("\u0043\u0054_\u0056\u0061\u0072i\u0061\u006e\u0074\u0043\u0068\u006f\u0069\u0063\u0065");};

// Validate validates the CT_ArrayChoice and its children
func (_ac *CT_ArrayChoice )Validate ()error {return _ac .ValidateWithPath ("\u0043\u0054\u005f\u0041\u0072\u0072\u0061\u0079\u0043h\u006f\u0069\u0063\u0065");};

// Validate validates the CT_Vector and its children
func (_cagc *CT_Vector )Validate ()error {return _cagc .ValidateWithPath ("\u0043T\u005f\u0056\u0065\u0063\u0074\u006fr");};

// ValidateWithPath validates the CT_Vector and its children, prefixing error messages with path
func (_aee *CT_Vector )ValidateWithPath (path string )error {if _aee .BaseTypeAttr ==ST_VectorBaseTypeUnset {return _ed .Errorf ("\u0025\u0073/B\u0061\u0073\u0065T\u0079\u0070\u0065\u0041ttr\u0020is\u0020\u0061\u0020\u006d\u0061\u006e\u0064at\u006f\u0072\u0079\u0020\u0066\u0069\u0065l\u0064",path );
};if _aca :=_aee .BaseTypeAttr .ValidateWithPath (path +"\u002f\u0042\u0061\u0073\u0065\u0054\u0079\u0070\u0065\u0041\u0074\u0074\u0072");_aca !=nil {return _aca ;};for _fbdf ,_dfga :=range _aee .VectorChoice {if _gdeb :=_dfga .ValidateWithPath (_ed .Sprintf ("\u0025\u0073\u002f\u0056ec\u0074\u006f\u0072\u0043\u0068\u006f\u0069\u0063\u0065\u005b\u0025\u0064\u005d",path ,_fbdf ));
_gdeb !=nil {return _gdeb ;};};return nil ;};func (_dcc *CT_Array )MarshalXML (e *_eg .Encoder ,start _eg .StartElement )error {start .Attr =append (start .Attr ,_eg .Attr {Name :_eg .Name {Local :"\u006cB\u006f\u0075\u006e\u0064\u0073"},Value :_ed .Sprintf ("\u0025\u0076",_dcc .LBoundsAttr )});
start .Attr =append (start .Attr ,_eg .Attr {Name :_eg .Name {Local :"\u0075B\u006f\u0075\u006e\u0064\u0073"},Value :_ed .Sprintf ("\u0025\u0076",_dcc .UBoundsAttr )});_gg ,_dcb :=_dcc .BaseTypeAttr .MarshalXMLAttr (_eg .Name {Local :"\u0062\u0061\u0073\u0065\u0054\u0079\u0070\u0065"});
if _dcb !=nil {return _dcb ;};start .Attr =append (start .Attr ,_gg );e .EncodeToken (start );for _ ,_cad :=range _dcc .ArrayChoice {_cad .MarshalXML (e ,_eg .StartElement {});};e .EncodeToken (_eg .EndElement {Name :start .Name });return nil ;};

// Validate validates the Vstream and its children
func (_egcf *Vstream )Validate ()error {return _egcf .ValidateWithPath ("\u0056s\u0074\u0072\u0065\u0061\u006d");};func (_eed *CT_VariantChoice )MarshalXML (e *_eg .Encoder ,start _eg .StartElement )error {e .EncodeToken (start );if _eed .Variant !=nil {_gdc :=_eg .StartElement {Name :_eg .Name {Local :"\u0076\u0074\u003a\u0076\u0061\u0072\u0069\u0061\u006e\u0074"}};
e .EncodeElement (_eed .Variant ,_gdc );}else if _eed .Vector !=nil {_gcgg :=_eg .StartElement {Name :_eg .Name {Local :"\u0076t\u003a\u0076\u0065\u0063\u0074\u006fr"}};e .EncodeElement (_eed .Vector ,_gcgg );}else if _eed .Array !=nil {_dba :=_eg .StartElement {Name :_eg .Name {Local :"\u0076\u0074\u003a\u0061\u0072\u0072\u0061\u0079"}};
e .EncodeElement (_eed .Array ,_dba );}else if _eed .Blob !=nil {_geaa :=_eg .StartElement {Name :_eg .Name {Local :"\u0076t\u003a\u0062\u006c\u006f\u0062"}};_ea .AddPreserveSpaceAttr (&_geaa ,*_eed .Blob );e .EncodeElement (_eed .Blob ,_geaa );}else if _eed .Oblob !=nil {_dcge :=_eg .StartElement {Name :_eg .Name {Local :"\u0076\u0074\u003a\u006f\u0062\u006c\u006f\u0062"}};
_ea .AddPreserveSpaceAttr (&_dcge ,*_eed .Oblob );e .EncodeElement (_eed .Oblob ,_dcge );}else if _eed .Empty !=nil {_cec :=_eg .StartElement {Name :_eg .Name {Local :"\u0076\u0074\u003a\u0065\u006d\u0070\u0074\u0079"}};e .EncodeElement (_eed .Empty ,_cec );
}else if _eed .Null !=nil {_cgef :=_eg .StartElement {Name :_eg .Name {Local :"\u0076t\u003a\u006e\u0075\u006c\u006c"}};e .EncodeElement (_eed .Null ,_cgef );}else if _eed .I1 !=nil {_bbbb :=_eg .StartElement {Name :_eg .Name {Local :"\u0076\u0074\u003ai\u0031"}};
e .EncodeElement (_eed .I1 ,_bbbb );}else if _eed .I2 !=nil {_fae :=_eg .StartElement {Name :_eg .Name {Local :"\u0076\u0074\u003ai\u0032"}};e .EncodeElement (_eed .I2 ,_fae );}else if _eed .I4 !=nil {_bacb :=_eg .StartElement {Name :_eg .Name {Local :"\u0076\u0074\u003ai\u0034"}};
e .EncodeElement (_eed .I4 ,_bacb );}else if _eed .I8 !=nil {_dedg :=_eg .StartElement {Name :_eg .Name {Local :"\u0076\u0074\u003ai\u0038"}};e .EncodeElement (_eed .I8 ,_dedg );}else if _eed .Int !=nil {_cadc :=_eg .StartElement {Name :_eg .Name {Local :"\u0076\u0074\u003a\u0069\u006e\u0074"}};
e .EncodeElement (_eed .Int ,_cadc );}else if _eed .Ui1 !=nil {_bbdd :=_eg .StartElement {Name :_eg .Name {Local :"\u0076\u0074\u003a\u0075\u0069\u0031"}};e .EncodeElement (_eed .Ui1 ,_bbdd );}else if _eed .Ui2 !=nil {_cdge :=_eg .StartElement {Name :_eg .Name {Local :"\u0076\u0074\u003a\u0075\u0069\u0032"}};
e .EncodeElement (_eed .Ui2 ,_cdge );}else if _eed .Ui4 !=nil {_daa :=_eg .StartElement {Name :_eg .Name {Local :"\u0076\u0074\u003a\u0075\u0069\u0034"}};e .EncodeElement (_eed .Ui4 ,_daa );}else if _eed .Ui8 !=nil {_dagb :=_eg .StartElement {Name :_eg .Name {Local :"\u0076\u0074\u003a\u0075\u0069\u0038"}};
e .EncodeElement (_eed .Ui8 ,_dagb );}else if _eed .Uint !=nil {_ged :=_eg .StartElement {Name :_eg .Name {Local :"\u0076t\u003a\u0075\u0069\u006e\u0074"}};e .EncodeElement (_eed .Uint ,_ged );}else if _eed .R4 !=nil {_ggdf :=_eg .StartElement {Name :_eg .Name {Local :"\u0076\u0074\u003ar\u0034"}};
e .EncodeElement (_eed .R4 ,_ggdf );}else if _eed .R8 !=nil {_ggea :=_eg .StartElement {Name :_eg .Name {Local :"\u0076\u0074\u003ar\u0038"}};e .EncodeElement (_eed .R8 ,_ggea );}else if _eed .Decimal !=nil {_cddfa :=_eg .StartElement {Name :_eg .Name {Local :"\u0076\u0074\u003a\u0064\u0065\u0063\u0069\u006d\u0061\u006c"}};
e .EncodeElement (_eed .Decimal ,_cddfa );}else if _eed .Lpstr !=nil {_efg :=_eg .StartElement {Name :_eg .Name {Local :"\u0076\u0074\u003a\u006c\u0070\u0073\u0074\u0072"}};_ea .AddPreserveSpaceAttr (&_efg ,*_eed .Lpstr );e .EncodeElement (_eed .Lpstr ,_efg );
}else if _eed .Lpwstr !=nil {_eadc :=_eg .StartElement {Name :_eg .Name {Local :"\u0076t\u003a\u006c\u0070\u0077\u0073\u0074r"}};_ea .AddPreserveSpaceAttr (&_eadc ,*_eed .Lpwstr );e .EncodeElement (_eed .Lpwstr ,_eadc );}else if _eed .Bstr !=nil {_eddd :=_eg .StartElement {Name :_eg .Name {Local :"\u0076t\u003a\u0062\u0073\u0074\u0072"}};
_ea .AddPreserveSpaceAttr (&_eddd ,*_eed .Bstr );e .EncodeElement (_eed .Bstr ,_eddd );}else if _eed .Date !=nil {_efe :=_eg .StartElement {Name :_eg .Name {Local :"\u0076t\u003a\u0064\u0061\u0074\u0065"}};e .EncodeElement (_g .FormatDate (*_eed .Date ),_efe );
}else if _eed .Filetime !=nil {_agf :=_eg .StartElement {Name :_eg .Name {Local :"v\u0074\u003a\u0066\u0069\u006c\u0065\u0074\u0069\u006d\u0065"}};e .EncodeElement (_eed .Filetime ,_agf );}else if _eed .Bool !=nil {_dgd :=_eg .StartElement {Name :_eg .Name {Local :"\u0076t\u003a\u0062\u006f\u006f\u006c"}};
e .EncodeElement (_eed .Bool ,_dgd );}else if _eed .Cy !=nil {_bbf :=_eg .StartElement {Name :_eg .Name {Local :"\u0076\u0074\u003ac\u0079"}};_ea .AddPreserveSpaceAttr (&_bbf ,*_eed .Cy );e .EncodeElement (_eed .Cy ,_bbf );}else if _eed .Error !=nil {_bea :=_eg .StartElement {Name :_eg .Name {Local :"\u0076\u0074\u003a\u0065\u0072\u0072\u006f\u0072"}};
_ea .AddPreserveSpaceAttr (&_bea ,*_eed .Error );e .EncodeElement (_eed .Error ,_bea );}else if _eed .Stream !=nil {_cfe :=_eg .StartElement {Name :_eg .Name {Local :"\u0076t\u003a\u0073\u0074\u0072\u0065\u0061m"}};_ea .AddPreserveSpaceAttr (&_cfe ,*_eed .Stream );
e .EncodeElement (_eed .Stream ,_cfe );}else if _eed .Ostream !=nil {_feab :=_eg .StartElement {Name :_eg .Name {Local :"\u0076\u0074\u003a\u006f\u0073\u0074\u0072\u0065\u0061\u006d"}};_ea .AddPreserveSpaceAttr (&_feab ,*_eed .Ostream );e .EncodeElement (_eed .Ostream ,_feab );
}else if _eed .Storage !=nil {_bfe :=_eg .StartElement {Name :_eg .Name {Local :"\u0076\u0074\u003a\u0073\u0074\u006f\u0072\u0061\u0067\u0065"}};_ea .AddPreserveSpaceAttr (&_bfe ,*_eed .Storage );e .EncodeElement (_eed .Storage ,_bfe );}else if _eed .Ostorage !=nil {_dfcg :=_eg .StartElement {Name :_eg .Name {Local :"v\u0074\u003a\u006f\u0073\u0074\u006f\u0072\u0061\u0067\u0065"}};
_ea .AddPreserveSpaceAttr (&_dfcg ,*_eed .Ostorage );e .EncodeElement (_eed .Ostorage ,_dfcg );}else if _eed .Vstream !=nil {_dgca :=_eg .StartElement {Name :_eg .Name {Local :"\u0076\u0074\u003a\u0076\u0073\u0074\u0072\u0065\u0061\u006d"}};e .EncodeElement (_eed .Vstream ,_dgca );
}else if _eed .Clsid !=nil {_fdee :=_eg .StartElement {Name :_eg .Name {Local :"\u0076\u0074\u003a\u0063\u006c\u0073\u0069\u0064"}};_ea .AddPreserveSpaceAttr (&_fdee ,*_eed .Clsid );e .EncodeElement (_eed .Clsid ,_fdee );};e .EncodeToken (_eg .EndElement {Name :start .Name });
return nil ;};

// Validate validates the CT_Array and its children
func (_eeb *CT_Array )Validate ()error {return _eeb .ValidateWithPath ("\u0043\u0054\u005f\u0041\u0072\u0072\u0061\u0079");};

// Validate validates the CT_Variant and its children
func (_ggc *CT_Variant )Validate ()error {return _ggc .ValidateWithPath ("\u0043\u0054\u005f\u0056\u0061\u0072\u0069\u0061\u006e\u0074");};func (_bdcb *CT_VectorChoice )MarshalXML (e *_eg .Encoder ,start _eg .StartElement )error {e .EncodeToken (start );
if _bdcb .Variant !=nil {_af :=_eg .StartElement {Name :_eg .Name {Local :"\u0076\u0074\u003a\u0076\u0061\u0072\u0069\u0061\u006e\u0074"}};e .EncodeElement (_bdcb .Variant ,_af );}else if _bdcb .I1 !=nil {_defg :=_eg .StartElement {Name :_eg .Name {Local :"\u0076\u0074\u003ai\u0031"}};
e .EncodeElement (_bdcb .I1 ,_defg );}else if _bdcb .I2 !=nil {_eaae :=_eg .StartElement {Name :_eg .Name {Local :"\u0076\u0074\u003ai\u0032"}};e .EncodeElement (_bdcb .I2 ,_eaae );}else if _bdcb .I4 !=nil {_cfefg :=_eg .StartElement {Name :_eg .Name {Local :"\u0076\u0074\u003ai\u0034"}};
e .EncodeElement (_bdcb .I4 ,_cfefg );}else if _bdcb .I8 !=nil {_bdb :=_eg .StartElement {Name :_eg .Name {Local :"\u0076\u0074\u003ai\u0038"}};e .EncodeElement (_bdcb .I8 ,_bdb );}else if _bdcb .Ui1 !=nil {_egf :=_eg .StartElement {Name :_eg .Name {Local :"\u0076\u0074\u003a\u0075\u0069\u0031"}};
e .EncodeElement (_bdcb .Ui1 ,_egf );}else if _bdcb .Ui2 !=nil {_aab :=_eg .StartElement {Name :_eg .Name {Local :"\u0076\u0074\u003a\u0075\u0069\u0032"}};e .EncodeElement (_bdcb .Ui2 ,_aab );}else if _bdcb .Ui4 !=nil {_dfgb :=_eg .StartElement {Name :_eg .Name {Local :"\u0076\u0074\u003a\u0075\u0069\u0034"}};
e .EncodeElement (_bdcb .Ui4 ,_dfgb );}else if _bdcb .Ui8 !=nil {_agadg :=_eg .StartElement {Name :_eg .Name {Local :"\u0076\u0074\u003a\u0075\u0069\u0038"}};e .EncodeElement (_bdcb .Ui8 ,_agadg );}else if _bdcb .R4 !=nil {_fbgf :=_eg .StartElement {Name :_eg .Name {Local :"\u0076\u0074\u003ar\u0034"}};
e .EncodeElement (_bdcb .R4 ,_fbgf );}else if _bdcb .R8 !=nil {_eaad :=_eg .StartElement {Name :_eg .Name {Local :"\u0076\u0074\u003ar\u0038"}};e .EncodeElement (_bdcb .R8 ,_eaad );}else if _bdcb .Lpstr !=nil {_afc :=_eg .StartElement {Name :_eg .Name {Local :"\u0076\u0074\u003a\u006c\u0070\u0073\u0074\u0072"}};
_ea .AddPreserveSpaceAttr (&_afc ,*_bdcb .Lpstr );e .EncodeElement (_bdcb .Lpstr ,_afc );}else if _bdcb .Lpwstr !=nil {_bed :=_eg .StartElement {Name :_eg .Name {Local :"\u0076t\u003a\u006c\u0070\u0077\u0073\u0074r"}};_ea .AddPreserveSpaceAttr (&_bed ,*_bdcb .Lpwstr );
e .EncodeElement (_bdcb .Lpwstr ,_bed );}else if _bdcb .Bstr !=nil {_begf :=_eg .StartElement {Name :_eg .Name {Local :"\u0076t\u003a\u0062\u0073\u0074\u0072"}};_ea .AddPreserveSpaceAttr (&_begf ,*_bdcb .Bstr );e .EncodeElement (_bdcb .Bstr ,_begf );}else if _bdcb .Date !=nil {_agff :=_eg .StartElement {Name :_eg .Name {Local :"\u0076t\u003a\u0064\u0061\u0074\u0065"}};
e .EncodeElement (_g .FormatDate (*_bdcb .Date ),_agff );}else if _bdcb .Filetime !=nil {_baef :=_eg .StartElement {Name :_eg .Name {Local :"v\u0074\u003a\u0066\u0069\u006c\u0065\u0074\u0069\u006d\u0065"}};e .EncodeElement (_bdcb .Filetime ,_baef );}else if _bdcb .Bool !=nil {_feg :=_eg .StartElement {Name :_eg .Name {Local :"\u0076t\u003a\u0062\u006f\u006f\u006c"}};
e .EncodeElement (_bdcb .Bool ,_feg );}else if _bdcb .Cy !=nil {_fab :=_eg .StartElement {Name :_eg .Name {Local :"\u0076\u0074\u003ac\u0079"}};_ea .AddPreserveSpaceAttr (&_fab ,*_bdcb .Cy );e .EncodeElement (_bdcb .Cy ,_fab );}else if _bdcb .Error !=nil {_ddcd :=_eg .StartElement {Name :_eg .Name {Local :"\u0076\u0074\u003a\u0065\u0072\u0072\u006f\u0072"}};
_ea .AddPreserveSpaceAttr (&_ddcd ,*_bdcb .Error );e .EncodeElement (_bdcb .Error ,_ddcd );}else if _bdcb .Clsid !=nil {_bcd :=_eg .StartElement {Name :_eg .Name {Local :"\u0076\u0074\u003a\u0063\u006c\u0073\u0069\u0064"}};_ea .AddPreserveSpaceAttr (&_bcd ,*_bdcb .Clsid );
e .EncodeElement (_bdcb .Clsid ,_bcd );};e .EncodeToken (_eg .EndElement {Name :start .Name });return nil ;};func (_dbfb *Empty )UnmarshalXML (d *_eg .Decoder ,start _eg .StartElement )error {_dbfb .CT_Empty =*NewCT_Empty ();for {_edad ,_fafb :=d .Token ();
if _fafb !=nil {return _ed .Errorf ("\u0070\u0061\u0072\u0073\u0069\u006e\u0067\u0020\u0045\u006d\u0070\u0074y\u003a\u0020\u0025\u0073",_fafb );};if _adc ,_dbaf :=_edad .(_eg .EndElement );_dbaf &&_adc .Name ==start .Name {break ;};};return nil ;};func (_gbbg ST_VectorBaseType )ValidateWithPath (path string )error {switch _gbbg {case 0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20:default:return _ed .Errorf ("\u0025s\u003a\u0020\u006f\u0075t\u0020\u006f\u0066\u0020\u0072a\u006eg\u0065 \u0076\u0061\u006c\u0075\u0065\u0020\u0025d",path ,int (_gbbg ));
};return nil ;};func (_daef *ST_ArrayBaseType )UnmarshalXML (d *_eg .Decoder ,start _eg .StartElement )error {_fcfe ,_gce :=d .Token ();if _gce !=nil {return _gce ;};if _bdaa ,_becb :=_fcfe .(_eg .EndElement );_becb &&_bdaa .Name ==start .Name {*_daef =1;
return nil ;};if _ccdg ,_fdc :=_fcfe .(_eg .CharData );!_fdc {return _ed .Errorf ("\u0065\u0078\u0070\u0065\u0063\u0074\u0065\u0064\u0020\u0063\u0068a\u0072\u0020\u0064\u0061\u0074\u0061\u002c\u0020\u0067\u006ft\u0020\u0025\u0054",_fcfe );}else {switch string (_ccdg ){case "":*_daef =0;
case "\u0076a\u0072\u0069\u0061\u006e\u0074":*_daef =1;case "\u0069\u0031":*_daef =2;case "\u0069\u0032":*_daef =3;case "\u0069\u0034":*_daef =4;case "\u0069\u006e\u0074":*_daef =5;case "\u0075\u0069\u0031":*_daef =6;case "\u0075\u0069\u0032":*_daef =7;
case "\u0075\u0069\u0034":*_daef =8;case "\u0075\u0069\u006e\u0074":*_daef =9;case "\u0072\u0034":*_daef =10;case "\u0072\u0038":*_daef =11;case "\u0064e\u0063\u0069\u006d\u0061\u006c":*_daef =12;case "\u0062\u0073\u0074\u0072":*_daef =13;case "\u0064\u0061\u0074\u0065":*_daef =14;
case "\u0062\u006f\u006f\u006c":*_daef =15;case "\u0063\u0079":*_daef =16;case "\u0065\u0072\u0072o\u0072":*_daef =17;};};_fcfe ,_gce =d .Token ();if _gce !=nil {return _gce ;};if _dcged ,_acgb :=_fcfe .(_eg .EndElement );_acgb &&_dcged .Name ==start .Name {return nil ;
};return _ed .Errorf ("\u0065\u0078\u0070\u0065c\u0074\u0065\u0064\u0020\u0065\u006e\u0064\u0020\u0065\u006ce\u006de\u006e\u0074\u002c\u0020\u0067\u006f\u0074 \u0025\u0076",_fcfe );};func (_dcaaf *Vstream )UnmarshalXML (d *_eg .Decoder ,start _eg .StartElement )error {_dcaaf .CT_Vstream =*NewCT_Vstream ();
for _ ,_fegc :=range start .Attr {if _fegc .Name .Local =="\u0076e\u0072\u0073\u0069\u006f\u006e"{_debd :=_fegc .Value ;_dcaaf .VersionAttr =_debd ;continue ;};};for {_faec ,_aeea :=d .Token ();if _aeea !=nil {return _ed .Errorf ("\u0070\u0061\u0072\u0073in\u0067\u0020\u0056\u0073\u0074\u0072\u0065\u0061\u006d\u003a\u0020\u0025\u0073",_aeea );
};if _fabf ,_eadb :=_faec .(_eg .EndElement );_eadb &&_fabf .Name ==start .Name {break ;};};return nil ;};func (_dcaeb *Variant )UnmarshalXML (d *_eg .Decoder ,start _eg .StartElement )error {_dcaeb .CT_Variant =*NewCT_Variant ();_bggd :for {_cbd ,_abfde :=d .Token ();
if _abfde !=nil {return _abfde ;};switch _aaad :=_cbd .(type ){case _eg .StartElement :switch _aaad .Name {case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0076a\u0072\u0069\u0061\u006e\u0074"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0076a\u0072\u0069\u0061\u006e\u0074"}:_dcaeb .VariantChoice =NewCT_VariantChoice ();
if _bagg :=d .DecodeElement (&_dcaeb .VariantChoice .Variant ,&_aaad );_bagg !=nil {return _bagg ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0076\u0065\u0063\u0074\u006f\u0072"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0076\u0065\u0063\u0074\u006f\u0072"}:_dcaeb .VariantChoice =NewCT_VariantChoice ();
if _ddff :=d .DecodeElement (&_dcaeb .VariantChoice .Vector ,&_aaad );_ddff !=nil {return _ddff ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0061\u0072\u0072a\u0079"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0061\u0072\u0072a\u0079"}:_dcaeb .VariantChoice =NewCT_VariantChoice ();
if _ggde :=d .DecodeElement (&_dcaeb .VariantChoice .Array ,&_aaad );_ggde !=nil {return _ggde ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0062\u006c\u006f\u0062"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0062\u006c\u006f\u0062"}:_dcaeb .VariantChoice =NewCT_VariantChoice ();
if _fbf :=d .DecodeElement (&_dcaeb .VariantChoice .Blob ,&_aaad );_fbf !=nil {return _fbf ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u006f\u0062\u006co\u0062"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u006f\u0062\u006co\u0062"}:_dcaeb .VariantChoice =NewCT_VariantChoice ();
if _ebcc :=d .DecodeElement (&_dcaeb .VariantChoice .Oblob ,&_aaad );_ebcc !=nil {return _ebcc ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0065\u006d\u0070t\u0079"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0065\u006d\u0070t\u0079"}:_dcaeb .VariantChoice =NewCT_VariantChoice ();
if _ceafb :=d .DecodeElement (&_dcaeb .VariantChoice .Empty ,&_aaad );_ceafb !=nil {return _ceafb ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u006e\u0075\u006c\u006c"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u006e\u0075\u006c\u006c"}:_dcaeb .VariantChoice =NewCT_VariantChoice ();
if _ceca :=d .DecodeElement (&_dcaeb .VariantChoice .Null ,&_aaad );_ceca !=nil {return _ceca ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0069\u0031"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0069\u0031"}:_dcaeb .VariantChoice =NewCT_VariantChoice ();
if _gbgg :=d .DecodeElement (&_dcaeb .VariantChoice .I1 ,&_aaad );_gbgg !=nil {return _gbgg ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0069\u0032"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0069\u0032"}:_dcaeb .VariantChoice =NewCT_VariantChoice ();
if _debb :=d .DecodeElement (&_dcaeb .VariantChoice .I2 ,&_aaad );_debb !=nil {return _debb ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0069\u0034"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0069\u0034"}:_dcaeb .VariantChoice =NewCT_VariantChoice ();
if _fbgc :=d .DecodeElement (&_dcaeb .VariantChoice .I4 ,&_aaad );_fbgc !=nil {return _fbgc ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0069\u0038"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0069\u0038"}:_dcaeb .VariantChoice =NewCT_VariantChoice ();
if _aaeg :=d .DecodeElement (&_dcaeb .VariantChoice .I8 ,&_aaad );_aaeg !=nil {return _aaeg ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0069\u006e\u0074"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0069\u006e\u0074"}:_dcaeb .VariantChoice =NewCT_VariantChoice ();
if _adbg :=d .DecodeElement (&_dcaeb .VariantChoice .Int ,&_aaad );_adbg !=nil {return _adbg ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0075\u0069\u0031"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0075\u0069\u0031"}:_dcaeb .VariantChoice =NewCT_VariantChoice ();
if _dae :=d .DecodeElement (&_dcaeb .VariantChoice .Ui1 ,&_aaad );_dae !=nil {return _dae ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0075\u0069\u0032"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0075\u0069\u0032"}:_dcaeb .VariantChoice =NewCT_VariantChoice ();
if _ece :=d .DecodeElement (&_dcaeb .VariantChoice .Ui2 ,&_aaad );_ece !=nil {return _ece ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0075\u0069\u0034"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0075\u0069\u0034"}:_dcaeb .VariantChoice =NewCT_VariantChoice ();
if _dddd :=d .DecodeElement (&_dcaeb .VariantChoice .Ui4 ,&_aaad );_dddd !=nil {return _dddd ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0075\u0069\u0038"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0075\u0069\u0038"}:_dcaeb .VariantChoice =NewCT_VariantChoice ();
if _adce :=d .DecodeElement (&_dcaeb .VariantChoice .Ui8 ,&_aaad );_adce !=nil {return _adce ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0075\u0069\u006e\u0074"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0075\u0069\u006e\u0074"}:_dcaeb .VariantChoice =NewCT_VariantChoice ();
if _dgbe :=d .DecodeElement (&_dcaeb .VariantChoice .Uint ,&_aaad );_dgbe !=nil {return _dgbe ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0072\u0034"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0072\u0034"}:_dcaeb .VariantChoice =NewCT_VariantChoice ();
if _ffae :=d .DecodeElement (&_dcaeb .VariantChoice .R4 ,&_aaad );_ffae !=nil {return _ffae ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0072\u0038"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0072\u0038"}:_dcaeb .VariantChoice =NewCT_VariantChoice ();
if _cada :=d .DecodeElement (&_dcaeb .VariantChoice .R8 ,&_aaad );_cada !=nil {return _cada ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0064e\u0063\u0069\u006d\u0061\u006c"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0064e\u0063\u0069\u006d\u0061\u006c"}:_dcaeb .VariantChoice =NewCT_VariantChoice ();
if _dgfb :=d .DecodeElement (&_dcaeb .VariantChoice .Decimal ,&_aaad );_dgfb !=nil {return _dgfb ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u006c\u0070\u0073t\u0072"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u006c\u0070\u0073t\u0072"}:_dcaeb .VariantChoice =NewCT_VariantChoice ();
if _dabf :=d .DecodeElement (&_dcaeb .VariantChoice .Lpstr ,&_aaad );_dabf !=nil {return _dabf ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u006c\u0070\u0077\u0073\u0074\u0072"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u006c\u0070\u0077\u0073\u0074\u0072"}:_dcaeb .VariantChoice =NewCT_VariantChoice ();
if _ffcb :=d .DecodeElement (&_dcaeb .VariantChoice .Lpwstr ,&_aaad );_ffcb !=nil {return _ffcb ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0062\u0073\u0074\u0072"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0062\u0073\u0074\u0072"}:_dcaeb .VariantChoice =NewCT_VariantChoice ();
if _eabg :=d .DecodeElement (&_dcaeb .VariantChoice .Bstr ,&_aaad );_eabg !=nil {return _eabg ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0064\u0061\u0074\u0065"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0064\u0061\u0074\u0065"}:_dcaeb .VariantChoice =NewCT_VariantChoice ();
if _gbe :=d .DecodeElement (&_dcaeb .VariantChoice .Date ,&_aaad );_gbe !=nil {return _gbe ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0066\u0069\u006c\u0065\u0074\u0069\u006d\u0065"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0066\u0069\u006c\u0065\u0074\u0069\u006d\u0065"}:_dcaeb .VariantChoice =NewCT_VariantChoice ();
if _agaa :=d .DecodeElement (&_dcaeb .VariantChoice .Filetime ,&_aaad );_agaa !=nil {return _agaa ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0062\u006f\u006f\u006c"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0062\u006f\u006f\u006c"}:_dcaeb .VariantChoice =NewCT_VariantChoice ();
if _fgec :=d .DecodeElement (&_dcaeb .VariantChoice .Bool ,&_aaad );_fgec !=nil {return _fgec ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0063\u0079"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0063\u0079"}:_dcaeb .VariantChoice =NewCT_VariantChoice ();
if _feeb :=d .DecodeElement (&_dcaeb .VariantChoice .Cy ,&_aaad );_feeb !=nil {return _feeb ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0065\u0072\u0072o\u0072"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0065\u0072\u0072o\u0072"}:_dcaeb .VariantChoice =NewCT_VariantChoice ();
if _cabd :=d .DecodeElement (&_dcaeb .VariantChoice .Error ,&_aaad );_cabd !=nil {return _cabd ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0073\u0074\u0072\u0065\u0061\u006d"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0073\u0074\u0072\u0065\u0061\u006d"}:_dcaeb .VariantChoice =NewCT_VariantChoice ();
if _dgcc :=d .DecodeElement (&_dcaeb .VariantChoice .Stream ,&_aaad );_dgcc !=nil {return _dgcc ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u006fs\u0074\u0072\u0065\u0061\u006d"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u006fs\u0074\u0072\u0065\u0061\u006d"}:_dcaeb .VariantChoice =NewCT_VariantChoice ();
if _fec :=d .DecodeElement (&_dcaeb .VariantChoice .Ostream ,&_aaad );_fec !=nil {return _fec ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0073t\u006f\u0072\u0061\u0067\u0065"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0073t\u006f\u0072\u0061\u0067\u0065"}:_dcaeb .VariantChoice =NewCT_VariantChoice ();
if _bcf :=d .DecodeElement (&_dcaeb .VariantChoice .Storage ,&_aaad );_bcf !=nil {return _bcf ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u006f\u0073\u0074\u006f\u0072\u0061\u0067\u0065"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u006f\u0073\u0074\u006f\u0072\u0061\u0067\u0065"}:_dcaeb .VariantChoice =NewCT_VariantChoice ();
if _ceb :=d .DecodeElement (&_dcaeb .VariantChoice .Ostorage ,&_aaad );_ceb !=nil {return _ceb ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0076s\u0074\u0072\u0065\u0061\u006d"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0076s\u0074\u0072\u0065\u0061\u006d"}:_dcaeb .VariantChoice =NewCT_VariantChoice ();
if _edcg :=d .DecodeElement (&_dcaeb .VariantChoice .Vstream ,&_aaad );_edcg !=nil {return _edcg ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0063\u006c\u0073i\u0064"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0063\u006c\u0073i\u0064"}:_dcaeb .VariantChoice =NewCT_VariantChoice ();
if _aac :=d .DecodeElement (&_dcaeb .VariantChoice .Clsid ,&_aaad );_aac !=nil {return _aac ;};default:_cb .Log .Debug ("\u0073\u006b\u0069p\u0070\u0069\u006e\u0067\u0020\u0075\u006e\u0073\u0075\u0070\u0070\u006f\u0072\u0074\u0065\u0064\u0020\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0056a\u0072\u0069\u0061\u006e\u0074\u0020\u0025\u0076",_aaad .Name );
if _fgcd :=d .Skip ();_fgcd !=nil {return _fgcd ;};};case _eg .EndElement :break _bggd ;case _eg .CharData :};};return nil ;};

// ValidateWithPath validates the Null and its children, prefixing error messages with path
func (_feff *Null )ValidateWithPath (path string )error {if _gcgf :=_feff .CT_Null .ValidateWithPath (path );_gcgf !=nil {return _gcgf ;};return nil ;};

// ValidateWithPath validates the Variant and its children, prefixing error messages with path
func (_efc *Variant )ValidateWithPath (path string )error {if _gbb :=_efc .CT_Variant .ValidateWithPath (path );_gbb !=nil {return _gbb ;};return nil ;};func NewArray ()*Array {_ecd :=&Array {};_ecd .CT_Array =*NewCT_Array ();return _ecd };func (_fbb *CT_Null )UnmarshalXML (d *_eg .Decoder ,start _eg .StartElement )error {for {_dab ,_gcga :=d .Token ();
if _gcga !=nil {return _ed .Errorf ("\u0070\u0061\u0072\u0073in\u0067\u0020\u0043\u0054\u005f\u004e\u0075\u006c\u006c\u003a\u0020\u0025\u0073",_gcga );};if _bcg ,_gcb :=_dab .(_eg .EndElement );_gcb &&_bcg .Name ==start .Name {break ;};};return nil ;};
func (_bgf *CT_Variant )MarshalXML (e *_eg .Encoder ,start _eg .StartElement )error {e .EncodeToken (start );_bgf .VariantChoice .MarshalXML (e ,_eg .StartElement {});e .EncodeToken (_eg .EndElement {Name :start .Name });return nil ;};type Vstream struct{CT_Vstream };


// ValidateWithPath validates the CT_Empty and its children, prefixing error messages with path
func (_agc *CT_Empty )ValidateWithPath (path string )error {return nil };func NewNull ()*Null {_cfgd :=&Null {};_cfgd .CT_Null =*NewCT_Null ();return _cfgd };type Vector struct{CT_Vector };func (_cbgg ST_VectorBaseType )MarshalXML (e *_eg .Encoder ,start _eg .StartElement )error {return e .EncodeElement (_cbgg .String (),start );
};func (_aga *CT_Variant )UnmarshalXML (d *_eg .Decoder ,start _eg .StartElement )error {_aga .VariantChoice =NewCT_VariantChoice ();_gda :for {_ecdb ,_aeg :=d .Token ();if _aeg !=nil {return _aeg ;};switch _gded :=_ecdb .(type ){case _eg .StartElement :switch _gded .Name {case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0076a\u0072\u0069\u0061\u006e\u0074"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0076a\u0072\u0069\u0061\u006e\u0074"}:_aga .VariantChoice =NewCT_VariantChoice ();
if _fcag :=d .DecodeElement (&_aga .VariantChoice .Variant ,&_gded );_fcag !=nil {return _fcag ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0076\u0065\u0063\u0074\u006f\u0072"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0076\u0065\u0063\u0074\u006f\u0072"}:_aga .VariantChoice =NewCT_VariantChoice ();
if _ead :=d .DecodeElement (&_aga .VariantChoice .Vector ,&_gded );_ead !=nil {return _ead ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0061\u0072\u0072a\u0079"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0061\u0072\u0072a\u0079"}:_aga .VariantChoice =NewCT_VariantChoice ();
if _bcgb :=d .DecodeElement (&_aga .VariantChoice .Array ,&_gded );_bcgb !=nil {return _bcgb ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0062\u006c\u006f\u0062"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0062\u006c\u006f\u0062"}:_aga .VariantChoice =NewCT_VariantChoice ();
if _ebc :=d .DecodeElement (&_aga .VariantChoice .Blob ,&_gded );_ebc !=nil {return _ebc ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u006f\u0062\u006co\u0062"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u006f\u0062\u006co\u0062"}:_aga .VariantChoice =NewCT_VariantChoice ();
if _decg :=d .DecodeElement (&_aga .VariantChoice .Oblob ,&_gded );_decg !=nil {return _decg ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0065\u006d\u0070t\u0079"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0065\u006d\u0070t\u0079"}:_aga .VariantChoice =NewCT_VariantChoice ();
if _feba :=d .DecodeElement (&_aga .VariantChoice .Empty ,&_gded );_feba !=nil {return _feba ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u006e\u0075\u006c\u006c"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u006e\u0075\u006c\u006c"}:_aga .VariantChoice =NewCT_VariantChoice ();
if _bccg :=d .DecodeElement (&_aga .VariantChoice .Null ,&_gded );_bccg !=nil {return _bccg ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0069\u0031"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0069\u0031"}:_aga .VariantChoice =NewCT_VariantChoice ();
if _gcgae :=d .DecodeElement (&_aga .VariantChoice .I1 ,&_gded );_gcgae !=nil {return _gcgae ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0069\u0032"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0069\u0032"}:_aga .VariantChoice =NewCT_VariantChoice ();
if _dde :=d .DecodeElement (&_aga .VariantChoice .I2 ,&_gded );_dde !=nil {return _dde ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0069\u0034"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0069\u0034"}:_aga .VariantChoice =NewCT_VariantChoice ();
if _ada :=d .DecodeElement (&_aga .VariantChoice .I4 ,&_gded );_ada !=nil {return _ada ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0069\u0038"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0069\u0038"}:_aga .VariantChoice =NewCT_VariantChoice ();
if _dfg :=d .DecodeElement (&_aga .VariantChoice .I8 ,&_gded );_dfg !=nil {return _dfg ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0069\u006e\u0074"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0069\u006e\u0074"}:_aga .VariantChoice =NewCT_VariantChoice ();
if _abc :=d .DecodeElement (&_aga .VariantChoice .Int ,&_gded );_abc !=nil {return _abc ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0075\u0069\u0031"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0075\u0069\u0031"}:_aga .VariantChoice =NewCT_VariantChoice ();
if _eaba :=d .DecodeElement (&_aga .VariantChoice .Ui1 ,&_gded );_eaba !=nil {return _eaba ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0075\u0069\u0032"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0075\u0069\u0032"}:_aga .VariantChoice =NewCT_VariantChoice ();
if _fgb :=d .DecodeElement (&_aga .VariantChoice .Ui2 ,&_gded );_fgb !=nil {return _fgb ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0075\u0069\u0034"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0075\u0069\u0034"}:_aga .VariantChoice =NewCT_VariantChoice ();
if _dfb :=d .DecodeElement (&_aga .VariantChoice .Ui4 ,&_gded );_dfb !=nil {return _dfb ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0075\u0069\u0038"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0075\u0069\u0038"}:_aga .VariantChoice =NewCT_VariantChoice ();
if _adf :=d .DecodeElement (&_aga .VariantChoice .Ui8 ,&_gded );_adf !=nil {return _adf ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0075\u0069\u006e\u0074"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0075\u0069\u006e\u0074"}:_aga .VariantChoice =NewCT_VariantChoice ();
if _ceg :=d .DecodeElement (&_aga .VariantChoice .Uint ,&_gded );_ceg !=nil {return _ceg ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0072\u0034"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0072\u0034"}:_aga .VariantChoice =NewCT_VariantChoice ();
if _fbba :=d .DecodeElement (&_aga .VariantChoice .R4 ,&_gded );_fbba !=nil {return _fbba ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0072\u0038"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0072\u0038"}:_aga .VariantChoice =NewCT_VariantChoice ();
if _cccd :=d .DecodeElement (&_aga .VariantChoice .R8 ,&_gded );_cccd !=nil {return _cccd ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0064e\u0063\u0069\u006d\u0061\u006c"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0064e\u0063\u0069\u006d\u0061\u006c"}:_aga .VariantChoice =NewCT_VariantChoice ();
if _cddf :=d .DecodeElement (&_aga .VariantChoice .Decimal ,&_gded );_cddf !=nil {return _cddf ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u006c\u0070\u0073t\u0072"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u006c\u0070\u0073t\u0072"}:_aga .VariantChoice =NewCT_VariantChoice ();
if _decfg :=d .DecodeElement (&_aga .VariantChoice .Lpstr ,&_gded );_decfg !=nil {return _decfg ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u006c\u0070\u0077\u0073\u0074\u0072"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u006c\u0070\u0077\u0073\u0074\u0072"}:_aga .VariantChoice =NewCT_VariantChoice ();
if _gbc :=d .DecodeElement (&_aga .VariantChoice .Lpwstr ,&_gded );_gbc !=nil {return _gbc ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0062\u0073\u0074\u0072"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0062\u0073\u0074\u0072"}:_aga .VariantChoice =NewCT_VariantChoice ();
if _age :=d .DecodeElement (&_aga .VariantChoice .Bstr ,&_gded );_age !=nil {return _age ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0064\u0061\u0074\u0065"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0064\u0061\u0074\u0065"}:_aga .VariantChoice =NewCT_VariantChoice ();
if _bbc :=d .DecodeElement (&_aga .VariantChoice .Date ,&_gded );_bbc !=nil {return _bbc ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0066\u0069\u006c\u0065\u0074\u0069\u006d\u0065"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0066\u0069\u006c\u0065\u0074\u0069\u006d\u0065"}:_aga .VariantChoice =NewCT_VariantChoice ();
if _bbd :=d .DecodeElement (&_aga .VariantChoice .Filetime ,&_gded );_bbd !=nil {return _bbd ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0062\u006f\u006f\u006c"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0062\u006f\u006f\u006c"}:_aga .VariantChoice =NewCT_VariantChoice ();
if _fde :=d .DecodeElement (&_aga .VariantChoice .Bool ,&_gded );_fde !=nil {return _fde ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0063\u0079"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0063\u0079"}:_aga .VariantChoice =NewCT_VariantChoice ();
if _fbd :=d .DecodeElement (&_aga .VariantChoice .Cy ,&_gded );_fbd !=nil {return _fbd ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0065\u0072\u0072o\u0072"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0065\u0072\u0072o\u0072"}:_aga .VariantChoice =NewCT_VariantChoice ();
if _ceaf :=d .DecodeElement (&_aga .VariantChoice .Error ,&_gded );_ceaf !=nil {return _ceaf ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0073\u0074\u0072\u0065\u0061\u006d"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0073\u0074\u0072\u0065\u0061\u006d"}:_aga .VariantChoice =NewCT_VariantChoice ();
if _adac :=d .DecodeElement (&_aga .VariantChoice .Stream ,&_gded );_adac !=nil {return _adac ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u006fs\u0074\u0072\u0065\u0061\u006d"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u006fs\u0074\u0072\u0065\u0061\u006d"}:_aga .VariantChoice =NewCT_VariantChoice ();
if _bf :=d .DecodeElement (&_aga .VariantChoice .Ostream ,&_gded );_bf !=nil {return _bf ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0073t\u006f\u0072\u0061\u0067\u0065"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0073t\u006f\u0072\u0061\u0067\u0065"}:_aga .VariantChoice =NewCT_VariantChoice ();
if _gcc :=d .DecodeElement (&_aga .VariantChoice .Storage ,&_gded );_gcc !=nil {return _gcc ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u006f\u0073\u0074\u006f\u0072\u0061\u0067\u0065"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u006f\u0073\u0074\u006f\u0072\u0061\u0067\u0065"}:_aga .VariantChoice =NewCT_VariantChoice ();
if _dagd :=d .DecodeElement (&_aga .VariantChoice .Ostorage ,&_gded );_dagd !=nil {return _dagd ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0076s\u0074\u0072\u0065\u0061\u006d"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0076s\u0074\u0072\u0065\u0061\u006d"}:_aga .VariantChoice =NewCT_VariantChoice ();
if _bef :=d .DecodeElement (&_aga .VariantChoice .Vstream ,&_gded );_bef !=nil {return _bef ;};case _eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0063\u006c\u0073i\u0064"},_eg .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0063\u006c\u0073i\u0064"}:_aga .VariantChoice =NewCT_VariantChoice ();
if _gfc :=d .DecodeElement (&_aga .VariantChoice .Clsid ,&_gded );_gfc !=nil {return _gfc ;};default:_cb .Log .Debug ("\u0073k\u0069\u0070p\u0069\u006e\u0067 \u0075\u006e\u0073\u0075\u0070\u0070\u006fr\u0074\u0065\u0064\u0020\u0065\u006ce\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0043\u0054\u005fV\u0061\u0072\u0069\u0061\u006e\u0074\u0020\u0025\u0076",_gded .Name );
if _edc :=d .Skip ();_edc !=nil {return _edc ;};};case _eg .EndElement :break _gda ;case _eg .CharData :};};return nil ;};type CT_Variant struct{VariantChoice *CT_VariantChoice ;};

// ValidateWithPath validates the Vector and its children, prefixing error messages with path
func (_geb *Vector )ValidateWithPath (path string )error {if _effc :=_geb .CT_Vector .ValidateWithPath (path );_effc !=nil {return _effc ;};return nil ;};type CT_VariantChoice struct{Variant *Variant ;Vector *Vector ;Array *Array ;Blob *string ;Oblob *string ;
Empty *Empty ;Null *Null ;I1 *int8 ;I2 *int16 ;I4 *int32 ;I8 *int64 ;Int *int32 ;Ui1 *uint8 ;Ui2 *uint16 ;Ui4 *uint32 ;Ui8 *uint64 ;Uint *uint32 ;R4 *float32 ;R8 *float64 ;Decimal *float64 ;Lpstr *string ;Lpwstr *string ;Bstr *string ;Date *_b .Time ;Filetime *_b .Time ;
Bool *bool ;Cy *string ;Error *string ;Stream *string ;Ostream *string ;Storage *string ;Ostorage *string ;Vstream *Vstream ;Clsid *string ;};func (_edg *Empty )MarshalXML (e *_eg .Encoder ,start _eg .StartElement )error {return _edg .CT_Empty .MarshalXML (e ,start );
};func (_gcag ST_ArrayBaseType )MarshalXML (e *_eg .Encoder ,start _eg .StartElement )error {return e .EncodeElement (_gcag .String (),start );};func (_fddg *Null )MarshalXML (e *_eg .Encoder ,start _eg .StartElement )error {return _fddg .CT_Null .MarshalXML (e ,start );
};func NewCT_ArrayChoice ()*CT_ArrayChoice {_feb :=&CT_ArrayChoice {};return _feb };

// Validate validates the Variant and its children
func (_bbcb *Variant )Validate ()error {return _bbcb .ValidateWithPath ("\u0056a\u0072\u0069\u0061\u006e\u0074");};type ST_VectorBaseType byte ;

// ValidateWithPath validates the Array and its children, prefixing error messages with path
func (_ag *Array )ValidateWithPath (path string )error {if _bdc :=_ag .CT_Array .ValidateWithPath (path );_bdc !=nil {return _bdc ;};return nil ;};func init (){_ea .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073","\u0043\u0054\u005f\u0045\u006d\u0070\u0074\u0079",NewCT_Empty );
_ea .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073","\u0043T\u005f\u004e\u0075\u006c\u006c",NewCT_Null );
_ea .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073","\u0043T\u005f\u0056\u0065\u0063\u0074\u006fr",NewCT_Vector );
_ea .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073","\u0043\u0054\u005f\u0041\u0072\u0072\u0061\u0079",NewCT_Array );
_ea .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073","\u0043\u0054\u005f\u0056\u0061\u0072\u0069\u0061\u006e\u0074",NewCT_Variant );
_ea .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073","\u0043\u0054\u005f\u0056\u0073\u0074\u0072\u0065\u0061\u006d",NewCT_Vstream );
_ea .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073","\u0076a\u0072\u0069\u0061\u006e\u0074",NewVariant );
_ea .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073","\u0076\u0065\u0063\u0074\u006f\u0072",NewVector );
_ea .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073","\u0061\u0072\u0072a\u0079",NewArray );
_ea .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073","\u0065\u006d\u0070t\u0079",NewEmpty );
_ea .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073","\u006e\u0075\u006c\u006c",NewNull );
_ea .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073","\u0076s\u0074\u0072\u0065\u0061\u006d",NewVstream );
};