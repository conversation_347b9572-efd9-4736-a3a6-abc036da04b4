//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package custom_properties ;import (_e "encoding/xml";_d "fmt";_ef "github.com/unidoc/unioffice/v2";_g "github.com/unidoc/unioffice/v2/common/logger";_efc "github.com/unidoc/unioffice/v2/schema/soo/ofc/docPropsVTypes";_gb "github.com/unidoc/unioffice/v2/schema/soo/ofc/sharedTypes";
_b "strconv";_ea "time";);func (_gda *CT_PropertyChoice )MarshalXML (e *_e .Encoder ,start _e .StartElement )error {e .EncodeToken (start );if _gda .Vector !=nil {_fc :=_e .StartElement {Name :_e .Name {Local :"\u0076t\u003a\u0076\u0065\u0063\u0074\u006fr"}};
e .EncodeElement (_gda .Vector ,_fc );}else if _gda .Array !=nil {_feb :=_e .StartElement {Name :_e .Name {Local :"\u0076\u0074\u003a\u0061\u0072\u0072\u0061\u0079"}};e .EncodeElement (_gda .Array ,_feb );}else if _gda .Blob !=nil {_gae :=_e .StartElement {Name :_e .Name {Local :"\u0076t\u003a\u0062\u006c\u006f\u0062"}};
_ef .AddPreserveSpaceAttr (&_gae ,*_gda .Blob );e .EncodeElement (_gda .Blob ,_gae );}else if _gda .Oblob !=nil {_bb :=_e .StartElement {Name :_e .Name {Local :"\u0076\u0074\u003a\u006f\u0062\u006c\u006f\u0062"}};_ef .AddPreserveSpaceAttr (&_bb ,*_gda .Oblob );
e .EncodeElement (_gda .Oblob ,_bb );}else if _gda .Empty !=nil {_dcc :=_e .StartElement {Name :_e .Name {Local :"\u0076\u0074\u003a\u0065\u006d\u0070\u0074\u0079"}};e .EncodeElement (_gda .Empty ,_dcc );}else if _gda .Null !=nil {_gee :=_e .StartElement {Name :_e .Name {Local :"\u0076t\u003a\u006e\u0075\u006c\u006c"}};
e .EncodeElement (_gda .Null ,_gee );}else if _gda .I1 !=nil {_gef :=_e .StartElement {Name :_e .Name {Local :"\u0076\u0074\u003ai\u0031"}};e .EncodeElement (_gda .I1 ,_gef );}else if _gda .I2 !=nil {_fgg :=_e .StartElement {Name :_e .Name {Local :"\u0076\u0074\u003ai\u0032"}};
e .EncodeElement (_gda .I2 ,_fgg );}else if _gda .I4 !=nil {_de :=_e .StartElement {Name :_e .Name {Local :"\u0076\u0074\u003ai\u0034"}};e .EncodeElement (_gda .I4 ,_de );}else if _gda .I8 !=nil {_bebc :=_e .StartElement {Name :_e .Name {Local :"\u0076\u0074\u003ai\u0038"}};
e .EncodeElement (_gda .I8 ,_bebc );}else if _gda .Int !=nil {_fafc :=_e .StartElement {Name :_e .Name {Local :"\u0076\u0074\u003a\u0069\u006e\u0074"}};e .EncodeElement (_gda .Int ,_fafc );}else if _gda .Ui1 !=nil {_bga :=_e .StartElement {Name :_e .Name {Local :"\u0076\u0074\u003a\u0075\u0069\u0031"}};
e .EncodeElement (_gda .Ui1 ,_bga );}else if _gda .Ui2 !=nil {_aab :=_e .StartElement {Name :_e .Name {Local :"\u0076\u0074\u003a\u0075\u0069\u0032"}};e .EncodeElement (_gda .Ui2 ,_aab );}else if _gda .Ui4 !=nil {_ed :=_e .StartElement {Name :_e .Name {Local :"\u0076\u0074\u003a\u0075\u0069\u0034"}};
e .EncodeElement (_gda .Ui4 ,_ed );}else if _gda .Ui8 !=nil {_ffe :=_e .StartElement {Name :_e .Name {Local :"\u0076\u0074\u003a\u0075\u0069\u0038"}};e .EncodeElement (_gda .Ui8 ,_ffe );}else if _gda .Uint !=nil {_eag :=_e .StartElement {Name :_e .Name {Local :"\u0076t\u003a\u0075\u0069\u006e\u0074"}};
e .EncodeElement (_gda .Uint ,_eag );}else if _gda .R4 !=nil {_egg :=_e .StartElement {Name :_e .Name {Local :"\u0076\u0074\u003ar\u0034"}};e .EncodeElement (_gda .R4 ,_egg );}else if _gda .R8 !=nil {_gc :=_e .StartElement {Name :_e .Name {Local :"\u0076\u0074\u003ar\u0038"}};
e .EncodeElement (_gda .R8 ,_gc );}else if _gda .Decimal !=nil {_ad :=_e .StartElement {Name :_e .Name {Local :"\u0076\u0074\u003a\u0064\u0065\u0063\u0069\u006d\u0061\u006c"}};e .EncodeElement (_gda .Decimal ,_ad );}else if _gda .Lpstr !=nil {_abd :=_e .StartElement {Name :_e .Name {Local :"\u0076\u0074\u003a\u006c\u0070\u0073\u0074\u0072"}};
_ef .AddPreserveSpaceAttr (&_abd ,*_gda .Lpstr );e .EncodeElement (_gda .Lpstr ,_abd );}else if _gda .Lpwstr !=nil {_bce :=_e .StartElement {Name :_e .Name {Local :"\u0076t\u003a\u006c\u0070\u0077\u0073\u0074r"}};_ef .AddPreserveSpaceAttr (&_bce ,*_gda .Lpwstr );
e .EncodeElement (_gda .Lpwstr ,_bce );}else if _gda .Bstr !=nil {_begg :=_e .StartElement {Name :_e .Name {Local :"\u0076t\u003a\u0062\u0073\u0074\u0072"}};_ef .AddPreserveSpaceAttr (&_begg ,*_gda .Bstr );e .EncodeElement (_gda .Bstr ,_begg );}else if _gda .Date !=nil {_dcd :=_e .StartElement {Name :_e .Name {Local :"\u0076t\u003a\u0064\u0061\u0074\u0065"}};
e .EncodeElement (_gb .FormatDate (*_gda .Date ),_dcd );}else if _gda .Filetime !=nil {_aad :=_e .StartElement {Name :_e .Name {Local :"v\u0074\u003a\u0066\u0069\u006c\u0065\u0074\u0069\u006d\u0065"}};e .EncodeElement (_gda .Filetime ,_aad );}else if _gda .Bool !=nil {_gab :=_e .StartElement {Name :_e .Name {Local :"\u0076t\u003a\u0062\u006f\u006f\u006c"}};
e .EncodeElement (_gda .Bool ,_gab );}else if _gda .Cy !=nil {_gbe :=_e .StartElement {Name :_e .Name {Local :"\u0076\u0074\u003ac\u0079"}};_ef .AddPreserveSpaceAttr (&_gbe ,*_gda .Cy );e .EncodeElement (_gda .Cy ,_gbe );}else if _gda .Error !=nil {_gec :=_e .StartElement {Name :_e .Name {Local :"\u0076\u0074\u003a\u0065\u0072\u0072\u006f\u0072"}};
_ef .AddPreserveSpaceAttr (&_gec ,*_gda .Error );e .EncodeElement (_gda .Error ,_gec );}else if _gda .Stream !=nil {_cg :=_e .StartElement {Name :_e .Name {Local :"\u0076t\u003a\u0073\u0074\u0072\u0065\u0061m"}};_ef .AddPreserveSpaceAttr (&_cg ,*_gda .Stream );
e .EncodeElement (_gda .Stream ,_cg );}else if _gda .Ostream !=nil {_gfg :=_e .StartElement {Name :_e .Name {Local :"\u0076\u0074\u003a\u006f\u0073\u0074\u0072\u0065\u0061\u006d"}};_ef .AddPreserveSpaceAttr (&_gfg ,*_gda .Ostream );e .EncodeElement (_gda .Ostream ,_gfg );
}else if _gda .Storage !=nil {_fagc :=_e .StartElement {Name :_e .Name {Local :"\u0076\u0074\u003a\u0073\u0074\u006f\u0072\u0061\u0067\u0065"}};_ef .AddPreserveSpaceAttr (&_fagc ,*_gda .Storage );e .EncodeElement (_gda .Storage ,_fagc );}else if _gda .Ostorage !=nil {_gdaa :=_e .StartElement {Name :_e .Name {Local :"v\u0074\u003a\u006f\u0073\u0074\u006f\u0072\u0061\u0067\u0065"}};
_ef .AddPreserveSpaceAttr (&_gdaa ,*_gda .Ostorage );e .EncodeElement (_gda .Ostorage ,_gdaa );}else if _gda .Vstream !=nil {_bbe :=_e .StartElement {Name :_e .Name {Local :"\u0076\u0074\u003a\u0076\u0073\u0074\u0072\u0065\u0061\u006d"}};e .EncodeElement (_gda .Vstream ,_bbe );
}else if _gda .Clsid !=nil {_bcbd :=_e .StartElement {Name :_e .Name {Local :"\u0076\u0074\u003a\u0063\u006c\u0073\u0069\u0064"}};_ef .AddPreserveSpaceAttr (&_bcbd ,*_gda .Clsid );e .EncodeElement (_gda .Clsid ,_bcbd );};e .EncodeToken (_e .EndElement {Name :start .Name });
return nil ;};func NewCT_Property ()*CT_Property {_dcg :=&CT_Property {};_dcg .FmtidAttr ="\u007b\u0030\u0030\u0030\u0030\u0030\u0030\u0030\u0030\u002d\u0030\u0030\u0030\u0030\u002d\u0030\u0030\u0030\u0030\u002d\u0030\u0030\u0030\u0030-\u0030\u0030\u0030\u0030\u00300\u0030\u00300\u0030\u0030\u0030\u007d";
_dcg .PropertyChoice =NewCT_PropertyChoice ();return _dcg ;};

// ValidateWithPath validates the Properties and its children, prefixing error messages with path
func (_dd *Properties )ValidateWithPath (path string )error {if _ca :=_dd .CT_Properties .ValidateWithPath (path );_ca !=nil {return _ca ;};return nil ;};func (_ga *CT_Property )UnmarshalXML (d *_e .Decoder ,start _e .StartElement )error {_ga .FmtidAttr ="\u007b\u0030\u0030\u0030\u0030\u0030\u0030\u0030\u0030\u002d\u0030\u0030\u0030\u0030\u002d\u0030\u0030\u0030\u0030\u002d\u0030\u0030\u0030\u0030-\u0030\u0030\u0030\u0030\u00300\u0030\u00300\u0030\u0030\u0030\u007d";
_ga .PropertyChoice =NewCT_PropertyChoice ();for _ ,_gba :=range start .Attr {if _gba .Name .Local =="\u0066\u006d\u0074i\u0064"{_bd :=_gba .Value ;_ga .FmtidAttr =_bd ;continue ;};if _gba .Name .Local =="\u0070\u0069\u0064"{_gf ,_faa :=_b .ParseInt (_gba .Value ,10,32);
if _faa !=nil {return _faa ;};_ga .PidAttr =int32 (_gf );continue ;};if _gba .Name .Local =="\u006e\u0061\u006d\u0065"{_gfb :=_gba .Value ;_ga .NameAttr =&_gfb ;continue ;};if _gba .Name .Local =="\u006c\u0069\u006e\u006b\u0054\u0061\u0072\u0067\u0065\u0074"{_bg :=_gba .Value ;
_ga .LinkTargetAttr =&_bg ;continue ;};};_fd :for {_ce ,_dcf :=d .Token ();if _dcf !=nil {return _dcf ;};switch _gd :=_ce .(type ){case _e .StartElement :switch _gd .Name {case _e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0076\u0065\u0063\u0074\u006f\u0072"},_e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0076\u0065\u0063\u0074\u006f\u0072"}:_ga .PropertyChoice =NewCT_PropertyChoice ();
if _fag :=d .DecodeElement (&_ga .PropertyChoice .Vector ,&_gd );_fag !=nil {return _fag ;};case _e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0061\u0072\u0072a\u0079"},_e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0061\u0072\u0072a\u0079"}:_ga .PropertyChoice =NewCT_PropertyChoice ();
if _gg :=d .DecodeElement (&_ga .PropertyChoice .Array ,&_gd );_gg !=nil {return _gg ;};case _e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0062\u006c\u006f\u0062"},_e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0062\u006c\u006f\u0062"}:_ga .PropertyChoice =NewCT_PropertyChoice ();
if _dgf :=d .DecodeElement (&_ga .PropertyChoice .Blob ,&_gd );_dgf !=nil {return _dgf ;};case _e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u006f\u0062\u006co\u0062"},_e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u006f\u0062\u006co\u0062"}:_ga .PropertyChoice =NewCT_PropertyChoice ();
if _ge :=d .DecodeElement (&_ga .PropertyChoice .Oblob ,&_gd );_ge !=nil {return _ge ;};case _e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0065\u006d\u0070t\u0079"},_e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0065\u006d\u0070t\u0079"}:_ga .PropertyChoice =NewCT_PropertyChoice ();
if _gff :=d .DecodeElement (&_ga .PropertyChoice .Empty ,&_gd );_gff !=nil {return _gff ;};case _e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u006e\u0075\u006c\u006c"},_e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u006e\u0075\u006c\u006c"}:_ga .PropertyChoice =NewCT_PropertyChoice ();
if _af :=d .DecodeElement (&_ga .PropertyChoice .Null ,&_gd );_af !=nil {return _af ;};case _e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0069\u0031"},_e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0069\u0031"}:_ga .PropertyChoice =NewCT_PropertyChoice ();
if _bc :=d .DecodeElement (&_ga .PropertyChoice .I1 ,&_gd );_bc !=nil {return _bc ;};case _e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0069\u0032"},_e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0069\u0032"}:_ga .PropertyChoice =NewCT_PropertyChoice ();
if _agd :=d .DecodeElement (&_ga .PropertyChoice .I2 ,&_gd );_agd !=nil {return _agd ;};case _e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0069\u0034"},_e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0069\u0034"}:_ga .PropertyChoice =NewCT_PropertyChoice ();
if _afd :=d .DecodeElement (&_ga .PropertyChoice .I4 ,&_gd );_afd !=nil {return _afd ;};case _e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0069\u0038"},_e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0069\u0038"}:_ga .PropertyChoice =NewCT_PropertyChoice ();
if _fdg :=d .DecodeElement (&_ga .PropertyChoice .I8 ,&_gd );_fdg !=nil {return _fdg ;};case _e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0069\u006e\u0074"},_e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0069\u006e\u0074"}:_ga .PropertyChoice =NewCT_PropertyChoice ();
if _ggb :=d .DecodeElement (&_ga .PropertyChoice .Int ,&_gd );_ggb !=nil {return _ggb ;};case _e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0075\u0069\u0031"},_e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0075\u0069\u0031"}:_ga .PropertyChoice =NewCT_PropertyChoice ();
if _cb :=d .DecodeElement (&_ga .PropertyChoice .Ui1 ,&_gd );_cb !=nil {return _cb ;};case _e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0075\u0069\u0032"},_e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0075\u0069\u0032"}:_ga .PropertyChoice =NewCT_PropertyChoice ();
if _fda :=d .DecodeElement (&_ga .PropertyChoice .Ui2 ,&_gd );_fda !=nil {return _fda ;};case _e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0075\u0069\u0034"},_e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0075\u0069\u0034"}:_ga .PropertyChoice =NewCT_PropertyChoice ();
if _bcb :=d .DecodeElement (&_ga .PropertyChoice .Ui4 ,&_gd );_bcb !=nil {return _bcb ;};case _e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0075\u0069\u0038"},_e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0075\u0069\u0038"}:_ga .PropertyChoice =NewCT_PropertyChoice ();
if _cea :=d .DecodeElement (&_ga .PropertyChoice .Ui8 ,&_gd );_cea !=nil {return _cea ;};case _e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0075\u0069\u006e\u0074"},_e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0075\u0069\u006e\u0074"}:_ga .PropertyChoice =NewCT_PropertyChoice ();
if _ff :=d .DecodeElement (&_ga .PropertyChoice .Uint ,&_gd );_ff !=nil {return _ff ;};case _e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0072\u0034"},_e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0072\u0034"}:_ga .PropertyChoice =NewCT_PropertyChoice ();
if _ffc :=d .DecodeElement (&_ga .PropertyChoice .R4 ,&_gd );_ffc !=nil {return _ffc ;};case _e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0072\u0038"},_e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0072\u0038"}:_ga .PropertyChoice =NewCT_PropertyChoice ();
if _gac :=d .DecodeElement (&_ga .PropertyChoice .R8 ,&_gd );_gac !=nil {return _gac ;};case _e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0064e\u0063\u0069\u006d\u0061\u006c"},_e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0064e\u0063\u0069\u006d\u0061\u006c"}:_ga .PropertyChoice =NewCT_PropertyChoice ();
if _da :=d .DecodeElement (&_ga .PropertyChoice .Decimal ,&_gd );_da !=nil {return _da ;};case _e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u006c\u0070\u0073t\u0072"},_e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u006c\u0070\u0073t\u0072"}:_ga .PropertyChoice =NewCT_PropertyChoice ();
if _aag :=d .DecodeElement (&_ga .PropertyChoice .Lpstr ,&_gd );_aag !=nil {return _aag ;};case _e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u006c\u0070\u0077\u0073\u0074\u0072"},_e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u006c\u0070\u0077\u0073\u0074\u0072"}:_ga .PropertyChoice =NewCT_PropertyChoice ();
if _ac :=d .DecodeElement (&_ga .PropertyChoice .Lpwstr ,&_gd );_ac !=nil {return _ac ;};case _e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0062\u0073\u0074\u0072"},_e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0062\u0073\u0074\u0072"}:_ga .PropertyChoice =NewCT_PropertyChoice ();
if _fbc :=d .DecodeElement (&_ga .PropertyChoice .Bstr ,&_gd );_fbc !=nil {return _fbc ;};case _e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0064\u0061\u0074\u0065"},_e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0064\u0061\u0074\u0065"}:_ga .PropertyChoice =NewCT_PropertyChoice ();
if _dbg :=d .DecodeElement (&_ga .PropertyChoice .Date ,&_gd );_dbg !=nil {return _dbg ;};case _e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0066\u0069\u006c\u0065\u0074\u0069\u006d\u0065"},_e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0066\u0069\u006c\u0065\u0074\u0069\u006d\u0065"}:_ga .PropertyChoice =NewCT_PropertyChoice ();
if _ab :=d .DecodeElement (&_ga .PropertyChoice .Filetime ,&_gd );_ab !=nil {return _ab ;};case _e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0062\u006f\u006f\u006c"},_e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0062\u006f\u006f\u006c"}:_ga .PropertyChoice =NewCT_PropertyChoice ();
if _ffcf :=d .DecodeElement (&_ga .PropertyChoice .Bool ,&_gd );_ffcf !=nil {return _ffcf ;};case _e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0063\u0079"},_e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0063\u0079"}:_ga .PropertyChoice =NewCT_PropertyChoice ();
if _fgd :=d .DecodeElement (&_ga .PropertyChoice .Cy ,&_gd );_fgd !=nil {return _fgd ;};case _e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0065\u0072\u0072o\u0072"},_e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0065\u0072\u0072o\u0072"}:_ga .PropertyChoice =NewCT_PropertyChoice ();
if _gdg :=d .DecodeElement (&_ga .PropertyChoice .Error ,&_gd );_gdg !=nil {return _gdg ;};case _e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0073\u0074\u0072\u0065\u0061\u006d"},_e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0073\u0074\u0072\u0065\u0061\u006d"}:_ga .PropertyChoice =NewCT_PropertyChoice ();
if _be :=d .DecodeElement (&_ga .PropertyChoice .Stream ,&_gd );_be !=nil {return _be ;};case _e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u006fs\u0074\u0072\u0065\u0061\u006d"},_e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u006fs\u0074\u0072\u0065\u0061\u006d"}:_ga .PropertyChoice =NewCT_PropertyChoice ();
if _abe :=d .DecodeElement (&_ga .PropertyChoice .Ostream ,&_gd );_abe !=nil {return _abe ;};case _e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0073t\u006f\u0072\u0061\u0067\u0065"},_e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0073t\u006f\u0072\u0061\u0067\u0065"}:_ga .PropertyChoice =NewCT_PropertyChoice ();
if _beg :=d .DecodeElement (&_ga .PropertyChoice .Storage ,&_gd );_beg !=nil {return _beg ;};case _e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u006f\u0073\u0074\u006f\u0072\u0061\u0067\u0065"},_e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u006f\u0073\u0074\u006f\u0072\u0061\u0067\u0065"}:_ga .PropertyChoice =NewCT_PropertyChoice ();
if _bde :=d .DecodeElement (&_ga .PropertyChoice .Ostorage ,&_gd );_bde !=nil {return _bde ;};case _e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0076s\u0074\u0072\u0065\u0061\u006d"},_e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0076s\u0074\u0072\u0065\u0061\u006d"}:_ga .PropertyChoice =NewCT_PropertyChoice ();
if _gdf :=d .DecodeElement (&_ga .PropertyChoice .Vstream ,&_gd );_gdf !=nil {return _gdf ;};case _e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0063\u006c\u0073i\u0064"},_e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0063\u006c\u0073i\u0064"}:_ga .PropertyChoice =NewCT_PropertyChoice ();
if _faf :=d .DecodeElement (&_ga .PropertyChoice .Clsid ,&_gd );_faf !=nil {return _faf ;};default:_g .Log .Debug ("\u0073\u006bi\u0070\u0070\u0069\u006e\u0067\u0020\u0075\u006e\u0073\u0075\u0070\u0070\u006f\u0072\u0074\u0065\u0064\u0020\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0043\u0054\u005f\u0050\u0072\u006f\u0070\u0065\u0072\u0074\u0079\u0020\u0025\u0076",_gd .Name );
if _eaf :=d .Skip ();_eaf !=nil {return _eaf ;};};case _e .EndElement :break _fd ;case _e .CharData :};};return nil ;};

// ValidateWithPath validates the CT_PropertyChoice and its children, prefixing error messages with path
func (_ffgd *CT_PropertyChoice )ValidateWithPath (path string )error {if _ffgd .Vector !=nil {if _ebb :=_ffgd .Vector .ValidateWithPath (path +"\u002fV\u0065\u0063\u0074\u006f\u0072");_ebb !=nil {return _ebb ;};};if _ffgd .Array !=nil {if _dab :=_ffgd .Array .ValidateWithPath (path +"\u002f\u0041\u0072\u0072\u0061\u0079");
_dab !=nil {return _dab ;};};if _ffgd .Empty !=nil {if _cec :=_ffgd .Empty .ValidateWithPath (path +"\u002f\u0045\u006d\u0070\u0074\u0079");_cec !=nil {return _cec ;};};if _ffgd .Null !=nil {if _geea :=_ffgd .Null .ValidateWithPath (path +"\u002f\u004e\u0075l\u006c");
_geea !=nil {return _geea ;};};if _ffgd .Cy !=nil {if !_efc .ST_CyPatternRe .MatchString (*_ffgd .Cy ){return _d .Errorf ("\u0025\u0073\u002f\u006d\u002e\u0043y\u0020\u006d\u0075\u0073\u0074\u0020\u006d\u0061\u0074\u0063\u0068\u0020\u0027%\u0073\u0027\u0020\u0028\u0068\u0061\u0076e\u0020\u0025\u0076\u0029",path ,_efc .ST_CyPatternRe ,*_ffgd .Cy );
};};if _ffgd .Error !=nil {if !_efc .ST_ErrorPatternRe .MatchString (*_ffgd .Error ){return _d .Errorf ("\u0025\u0073/m\u002e\u0045\u0072r\u006f\u0072\u0020\u006dust\u0020ma\u0074\u0063\u0068\u0020\u0027\u0025\u0073' \u0028\u0068\u0061\u0076\u0065\u0020\u0025v\u0029",path ,_efc .ST_ErrorPatternRe ,*_ffgd .Error );
};};if _ffgd .Vstream !=nil {if _cfc :=_ffgd .Vstream .ValidateWithPath (path +"\u002f\u0056\u0073\u0074\u0072\u0065\u0061\u006d");_cfc !=nil {return _cfc ;};};if _ffgd .Clsid !=nil {if !_gb .ST_GuidPatternRe .MatchString (*_ffgd .Clsid ){return _d .Errorf ("\u0025\u0073/m\u002e\u0043\u006cs\u0069\u0064\u0020\u006dust\u0020ma\u0074\u0063\u0068\u0020\u0027\u0025\u0073' \u0028\u0068\u0061\u0076\u0065\u0020\u0025v\u0029",path ,_gb .ST_GuidPatternRe ,*_ffgd .Clsid );
};};return nil ;};func (_agc *CT_Property )MarshalXML (e *_e .Encoder ,start _e .StartElement )error {start .Attr =append (start .Attr ,_e .Attr {Name :_e .Name {Local :"\u0066\u006d\u0074i\u0064"},Value :_d .Sprintf ("\u0025\u0076",_agc .FmtidAttr )});
start .Attr =append (start .Attr ,_e .Attr {Name :_e .Name {Local :"\u0070\u0069\u0064"},Value :_d .Sprintf ("\u0025\u0076",_agc .PidAttr )});if _agc .NameAttr !=nil {start .Attr =append (start .Attr ,_e .Attr {Name :_e .Name {Local :"\u006e\u0061\u006d\u0065"},Value :_d .Sprintf ("\u0025\u0076",*_agc .NameAttr )});
};if _agc .LinkTargetAttr !=nil {start .Attr =append (start .Attr ,_e .Attr {Name :_e .Name {Local :"\u006c\u0069\u006e\u006b\u0054\u0061\u0072\u0067\u0065\u0074"},Value :_d .Sprintf ("\u0025\u0076",*_agc .LinkTargetAttr )});};e .EncodeToken (start );_agc .PropertyChoice .MarshalXML (e ,_e .StartElement {});
e .EncodeToken (_e .EndElement {Name :start .Name });return nil ;};func (_c *CT_Properties )UnmarshalXML (d *_e .Decoder ,start _e .StartElement )error {_ee :for {_fe ,_gbb :=d .Token ();if _gbb !=nil {return _gbb ;};switch _age :=_fe .(type ){case _e .StartElement :switch _age .Name {case _e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063h\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067/\u006f\u0066f\u0069\u0063e\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036/c\u0075\u0073\u0074\u006f\u006d\u002d\u0070\u0072\u006fp\u0065\u0072\u0074\u0069\u0065\u0073",Local :"\u0070\u0072\u006f\u0070\u0065\u0072\u0074\u0079"}:_dc :=NewCT_Property ();
if _aa :=d .DecodeElement (_dc ,&_age );_aa !=nil {return _aa ;};_c .Property =append (_c .Property ,_dc );default:_g .Log .Debug ("\u0073\u006b\u0069\u0070\u0070\u0069\u006e\u0067 \u0075\u006e\u0073up\u0070\u006f\u0072\u0074\u0065\u0064 \u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0043\u0054\u005f\u0050r\u006f\u0070\u0065\u0072\u0074\u0069\u0065\u0073 \u0025\u0076",_age .Name );
if _fb :=d .Skip ();_fb !=nil {return _fb ;};};case _e .EndElement :break _ee ;case _e .CharData :};};return nil ;};func NewCT_Properties ()*CT_Properties {_f :=&CT_Properties {};return _f };func (_fa *CT_Properties )MarshalXML (e *_e .Encoder ,start _e .StartElement )error {e .EncodeToken (start );
if _fa .Property !=nil {_ag :=_e .StartElement {Name :_e .Name {Local :"\u0070\u0072\u006f\u0070\u0065\u0072\u0074\u0079"}};for _ ,_eb :=range _fa .Property {e .EncodeElement (_eb ,_ag );};};e .EncodeToken (_e .EndElement {Name :start .Name });return nil ;
};

// Validate validates the CT_PropertyChoice and its children
func (_cbf *CT_PropertyChoice )Validate ()error {return _cbf .ValidateWithPath ("\u0043\u0054\u005f\u0050\u0072\u006f\u0070\u0065\u0072\u0074\u0079\u0043h\u006f\u0069\u0063\u0065");};type Properties struct{CT_Properties };type CT_PropertyChoice struct{Vector *_efc .Vector ;
Array *_efc .Array ;Blob *string ;Oblob *string ;Empty *_efc .Empty ;Null *_efc .Null ;I1 *int8 ;I2 *int16 ;I4 *int32 ;I8 *int64 ;Int *int32 ;Ui1 *uint8 ;Ui2 *uint16 ;Ui4 *uint32 ;Ui8 *uint64 ;Uint *uint32 ;R4 *float32 ;R8 *float64 ;Decimal *float64 ;Lpstr *string ;
Lpwstr *string ;Bstr *string ;Date *_ea .Time ;Filetime *_ea .Time ;Bool *bool ;Cy *string ;Error *string ;Stream *string ;Ostream *string ;Storage *string ;Ostorage *string ;Vstream *_efc .Vstream ;Clsid *string ;};func NewProperties ()*Properties {_bgf :=&Properties {};
_bgf .CT_Properties =*NewCT_Properties ();return _bgf ;};func (_fcf *CT_PropertyChoice )UnmarshalXML (d *_e .Decoder ,start _e .StartElement )error {_aba :=start ;switch start .Name {case _e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0076\u0065\u0063\u0074\u006f\u0072"},_e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0076\u0065\u0063\u0074\u006f\u0072"}:_fcf .Vector =_efc .NewVector ();
if _dcgc :=d .DecodeElement (_fcf .Vector ,&_aba );_dcgc !=nil {return _dcgc ;};case _e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0061\u0072\u0072a\u0079"},_e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0061\u0072\u0072a\u0079"}:_fcf .Array =_efc .NewArray ();
if _bega :=d .DecodeElement (_fcf .Array ,&_aba );_bega !=nil {return _bega ;};case _e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0062\u006c\u006f\u0062"},_e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0062\u006c\u006f\u0062"}:_fcf .Blob =new (string );
if _efb :=d .DecodeElement (_fcf .Blob ,&_aba );_efb !=nil {return _efb ;};case _e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u006f\u0062\u006co\u0062"},_e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u006f\u0062\u006co\u0062"}:_fcf .Oblob =new (string );
if _egd :=d .DecodeElement (_fcf .Oblob ,&_aba );_egd !=nil {return _egd ;};case _e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0065\u006d\u0070t\u0079"},_e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0065\u006d\u0070t\u0079"}:_fcf .Empty =_efc .NewEmpty ();
if _eecf :=d .DecodeElement (_fcf .Empty ,&_aba );_eecf !=nil {return _eecf ;};case _e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u006e\u0075\u006c\u006c"},_e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u006e\u0075\u006c\u006c"}:_fcf .Null =_efc .NewNull ();
if _ae :=d .DecodeElement (_fcf .Null ,&_aba );_ae !=nil {return _ae ;};case _e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0069\u0031"},_e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0069\u0031"}:_fcf .I1 =new (int8 );
if _dac :=d .DecodeElement (_fcf .I1 ,&_aba );_dac !=nil {return _dac ;};case _e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0069\u0032"},_e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0069\u0032"}:_fcf .I2 =new (int16 );
if _gfa :=d .DecodeElement (_fcf .I2 ,&_aba );_gfa !=nil {return _gfa ;};case _e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0069\u0034"},_e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0069\u0034"}:_fcf .I4 =new (int32 );
if _dbf :=d .DecodeElement (_fcf .I4 ,&_aba );_dbf !=nil {return _dbf ;};case _e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0069\u0038"},_e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0069\u0038"}:_fcf .I8 =new (int64 );
if _fbe :=d .DecodeElement (_fcf .I8 ,&_aba );_fbe !=nil {return _fbe ;};case _e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0069\u006e\u0074"},_e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0069\u006e\u0074"}:_fcf .Int =new (int32 );
if _bcec :=d .DecodeElement (_fcf .Int ,&_aba );_bcec !=nil {return _bcec ;};case _e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0075\u0069\u0031"},_e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0075\u0069\u0031"}:_fcf .Ui1 =new (uint8 );
if _cbc :=d .DecodeElement (_fcf .Ui1 ,&_aba );_cbc !=nil {return _cbc ;};case _e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0075\u0069\u0032"},_e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0075\u0069\u0032"}:_fcf .Ui2 =new (uint16 );
if _eage :=d .DecodeElement (_fcf .Ui2 ,&_aba );_eage !=nil {return _eage ;};case _e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0075\u0069\u0034"},_e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0075\u0069\u0034"}:_fcf .Ui4 =new (uint32 );
if _ffg :=d .DecodeElement (_fcf .Ui4 ,&_aba );_ffg !=nil {return _ffg ;};case _e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0075\u0069\u0038"},_e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0075\u0069\u0038"}:_fcf .Ui8 =new (uint64 );
if _efd :=d .DecodeElement (_fcf .Ui8 ,&_aba );_efd !=nil {return _efd ;};case _e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0075\u0069\u006e\u0074"},_e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0075\u0069\u006e\u0074"}:_fcf .Uint =new (uint32 );
if _fdgc :=d .DecodeElement (_fcf .Uint ,&_aba );_fdgc !=nil {return _fdgc ;};case _e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0072\u0034"},_e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0072\u0034"}:_fcf .R4 =new (float32 );
if _gbd :=d .DecodeElement (_fcf .R4 ,&_aba );_gbd !=nil {return _gbd ;};case _e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0072\u0038"},_e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0072\u0038"}:_fcf .R8 =new (float64 );
if _fcd :=d .DecodeElement (_fcf .R8 ,&_aba );_fcd !=nil {return _fcd ;};case _e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0064e\u0063\u0069\u006d\u0061\u006c"},_e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0064e\u0063\u0069\u006d\u0061\u006c"}:_fcf .Decimal =new (float64 );
if _dbb :=d .DecodeElement (_fcf .Decimal ,&_aba );_dbb !=nil {return _dbb ;};case _e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u006c\u0070\u0073t\u0072"},_e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u006c\u0070\u0073t\u0072"}:_fcf .Lpstr =new (string );
if _efcg :=d .DecodeElement (_fcf .Lpstr ,&_aba );_efcg !=nil {return _efcg ;};case _e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u006c\u0070\u0077\u0073\u0074\u0072"},_e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u006c\u0070\u0077\u0073\u0074\u0072"}:_fcf .Lpwstr =new (string );
if _gea :=d .DecodeElement (_fcf .Lpwstr ,&_aba );_gea !=nil {return _gea ;};case _e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0062\u0073\u0074\u0072"},_e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0062\u0073\u0074\u0072"}:_fcf .Bstr =new (string );
if _gfe :=d .DecodeElement (_fcf .Bstr ,&_aba );_gfe !=nil {return _gfe ;};case _e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0064\u0061\u0074\u0065"},_e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0064\u0061\u0074\u0065"}:var _cf string ;
if _gabd :=d .DecodeElement (&_cf ,&_aba );_gabd !=nil {return _gabd ;};_cfa ,_dcda :=_gb .ParseStdlibTime (_cf );if _dcda !=nil {return _dcda ;};_fcf .Date =&_cfa ;case _e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0066\u0069\u006c\u0065\u0074\u0069\u006d\u0065"},_e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0066\u0069\u006c\u0065\u0074\u0069\u006d\u0065"}:_fcf .Filetime =new (_ea .Time );
if _bcba :=d .DecodeElement (_fcf .Filetime ,&_aba );_bcba !=nil {return _bcba ;};case _e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0062\u006f\u006f\u006c"},_e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0062\u006f\u006f\u006c"}:_fcf .Bool =new (bool );
if _fee :=d .DecodeElement (_fcf .Bool ,&_aba );_fee !=nil {return _fee ;};case _e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0063\u0079"},_e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0063\u0079"}:_fcf .Cy =new (string );
if _gfd :=d .DecodeElement (_fcf .Cy ,&_aba );_gfd !=nil {return _gfd ;};case _e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0065\u0072\u0072o\u0072"},_e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0065\u0072\u0072o\u0072"}:_fcf .Error =new (string );
if _aea :=d .DecodeElement (_fcf .Error ,&_aba );_aea !=nil {return _aea ;};case _e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0073\u0074\u0072\u0065\u0061\u006d"},_e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0073\u0074\u0072\u0065\u0061\u006d"}:_fcf .Stream =new (string );
if _cgg :=d .DecodeElement (_fcf .Stream ,&_aba );_cgg !=nil {return _cgg ;};case _e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u006fs\u0074\u0072\u0065\u0061\u006d"},_e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u006fs\u0074\u0072\u0065\u0061\u006d"}:_fcf .Ostream =new (string );
if _gaf :=d .DecodeElement (_fcf .Ostream ,&_aba );_gaf !=nil {return _gaf ;};case _e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0073t\u006f\u0072\u0061\u0067\u0065"},_e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0073t\u006f\u0072\u0061\u0067\u0065"}:_fcf .Storage =new (string );
if _fef :=d .DecodeElement (_fcf .Storage ,&_aba );_fef !=nil {return _fef ;};case _e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u006f\u0073\u0074\u006f\u0072\u0061\u0067\u0065"},_e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u006f\u0073\u0074\u006f\u0072\u0061\u0067\u0065"}:_fcf .Ostorage =new (string );
if _gca :=d .DecodeElement (_fcf .Ostorage ,&_aba );_gca !=nil {return _gca ;};case _e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0076s\u0074\u0072\u0065\u0061\u006d"},_e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0076s\u0074\u0072\u0065\u0061\u006d"}:_fcf .Vstream =_efc .NewVstream ();
if _gfc :=d .DecodeElement (_fcf .Vstream ,&_aba );_gfc !=nil {return _gfc ;};case _e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0063\u006c\u0073i\u0064"},_e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072l\u002e\u006f\u0063l\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069ce\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u0056\u0054\u0079\u0070\u0065\u0073",Local :"\u0063\u006c\u0073i\u0064"}:_fcf .Clsid =new (string );
if _fac :=d .DecodeElement (_fcf .Clsid ,&_aba );_fac !=nil {return _fac ;};default:_g .Log .Debug ("\u0073\u006bi\u0070\u0070\u0069\u006e\u0067 \u0075\u006e\u0073\u0075\u0070p\u006f\u0072\u0074\u0065\u0064\u0020\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0043\u0054\u005f\u0050\u0072\u006f\u0070\u0065\u0072\u0074\u0079\u0043\u0068\u006f\u0069\u0063\u0065\u0020\u0025\u0076",_aba .Name );
if _abee :=d .Skip ();_abee !=nil {return _abee ;};};return nil ;};func NewCT_PropertyChoice ()*CT_PropertyChoice {_eec :=&CT_PropertyChoice {};return _eec };

// Validate validates the CT_Properties and its children
func (_db *CT_Properties )Validate ()error {return _db .ValidateWithPath ("\u0043\u0054\u005f\u0050\u0072\u006f\u0070\u0065\u0072\u0074\u0069\u0065\u0073");};

// Validate validates the CT_Property and its children
func (_beb *CT_Property )Validate ()error {return _beb .ValidateWithPath ("C\u0054\u005f\u0050\u0072\u006f\u0070\u0065\u0072\u0074\u0079");};

// ValidateWithPath validates the CT_Properties and its children, prefixing error messages with path
func (_fg *CT_Properties )ValidateWithPath (path string )error {for _bf ,_eg :=range _fg .Property {if _cd :=_eg .ValidateWithPath (_d .Sprintf ("\u0025s\u002fP\u0072\u006f\u0070\u0065\u0072\u0074\u0079\u005b\u0025\u0064\u005d",path ,_bf ));_cd !=nil {return _cd ;
};};return nil ;};

// ValidateWithPath validates the CT_Property and its children, prefixing error messages with path
func (_df *CT_Property )ValidateWithPath (path string )error {if !_gb .ST_GuidPatternRe .MatchString (_df .FmtidAttr ){return _d .Errorf ("%\u0073\u002f\u006d\u002e\u0046\u006d\u0074\u0069\u0064\u0041\u0074\u0074\u0072\u0020\u006d\u0075\u0073\u0074 \u006d\u0061\u0074\u0063\u0068\u0020\u0027\u0025\u0073\u0027 (\u0068\u0061\u0076e\u0020%\u0076\u0029",path ,_gb .ST_GuidPatternRe ,_df .FmtidAttr );
};if _feg :=_df .PropertyChoice .ValidateWithPath (path +"\u002fP\u0072o\u0070\u0065\u0072\u0074\u0079\u0043\u0068\u006f\u0069\u0063\u0065");_feg !=nil {return _feg ;};return nil ;};func (_cfag *Properties )MarshalXML (e *_e .Encoder ,start _e .StartElement )error {start .Attr =append (start .Attr ,_e .Attr {Name :_e .Name {Local :"\u0078\u006d\u006cn\u0073"},Value :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063h\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067/\u006f\u0066f\u0069\u0063e\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036/c\u0075\u0073\u0074\u006f\u006d\u002d\u0070\u0072\u006fp\u0065\u0072\u0074\u0069\u0065\u0073"});
start .Attr =append (start .Attr ,_e .Attr {Name :_e .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0073"},Value :"\u0068\u0074\u0074\u0070\u003a/\u002f\u0073\u0063\u0068\u0065m\u0061s\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067/\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0068\u0061\u0072e\u0064\u0054\u0079\u0070\u0065\u0073"});
start .Attr =append (start .Attr ,_e .Attr {Name :_e .Name {Local :"\u0078\u006d\u006c\u006e\u0073\u003a\u0076\u0074"},Value :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0064\u006f\u0063P\u0072\u006f\u0070s\u0056\u0054\u0079\u0070\u0065\u0073"});
start .Attr =append (start .Attr ,_e .Attr {Name :_e .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0078\u006dl"},Value :"\u0068\u0074tp\u003a\u002f\u002fw\u0077\u0077\u002e\u00773.o\u0072g/\u0058\u004d\u004c\u002f\u0031\u0039\u00398/\u006e\u0061\u006d\u0065\u0073\u0070\u0061c\u0065"});
start .Name .Local ="\u0050\u0072\u006f\u0070\u0065\u0072\u0074\u0069\u0065\u0073";return _cfag .CT_Properties .MarshalXML (e ,start );};type CT_Property struct{

// Format ID
FmtidAttr string ;

// Property ID
PidAttr int32 ;

// Custom File Property Name
NameAttr *string ;

// Bookmark Link Target
LinkTargetAttr *string ;PropertyChoice *CT_PropertyChoice ;};type CT_Properties struct{

// Custom File Property
Property []*CT_Property ;};

// Validate validates the Properties and its children
func (_ec *Properties )Validate ()error {return _ec .ValidateWithPath ("\u0050\u0072\u006f\u0070\u0065\u0072\u0074\u0069\u0065\u0073");};func (_efg *Properties )UnmarshalXML (d *_e .Decoder ,start _e .StartElement )error {_efg .CT_Properties =*NewCT_Properties ();
_dfc :for {_geg ,_dfd :=d .Token ();if _dfd !=nil {return _dfd ;};switch _aff :=_geg .(type ){case _e .StartElement :switch _aff .Name {case _e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063h\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067/\u006f\u0066f\u0069\u0063e\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036/c\u0075\u0073\u0074\u006f\u006d\u002d\u0070\u0072\u006fp\u0065\u0072\u0074\u0069\u0065\u0073",Local :"\u0070\u0072\u006f\u0070\u0065\u0072\u0074\u0079"}:_gdd :=NewCT_Property ();
if _gbaf :=d .DecodeElement (_gdd ,&_aff );_gbaf !=nil {return _gbaf ;};_efg .Property =append (_efg .Property ,_gdd );default:_g .Log .Debug ("\u0073k\u0069\u0070p\u0069\u006e\u0067 \u0075\u006e\u0073\u0075\u0070\u0070\u006fr\u0074\u0065\u0064\u0020\u0065\u006ce\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0050\u0072\u006fp\u0065\u0072\u0074\u0069\u0065\u0073\u0020\u0025\u0076",_aff .Name );
if _fdc :=d .Skip ();_fdc !=nil {return _fdc ;};};case _e .EndElement :break _dfc ;case _e .CharData :};};return nil ;};func init (){_ef .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063h\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067/\u006f\u0066f\u0069\u0063e\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036/c\u0075\u0073\u0074\u006f\u006d\u002d\u0070\u0072\u006fp\u0065\u0072\u0074\u0069\u0065\u0073","\u0043\u0054\u005f\u0050\u0072\u006f\u0070\u0065\u0072\u0074\u0069\u0065\u0073",NewCT_Properties );
_ef .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063h\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067/\u006f\u0066f\u0069\u0063e\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036/c\u0075\u0073\u0074\u006f\u006d\u002d\u0070\u0072\u006fp\u0065\u0072\u0074\u0069\u0065\u0073","C\u0054\u005f\u0050\u0072\u006f\u0070\u0065\u0072\u0074\u0079",NewCT_Property );
_ef .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063h\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067/\u006f\u0066f\u0069\u0063e\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036/c\u0075\u0073\u0074\u006f\u006d\u002d\u0070\u0072\u006fp\u0065\u0072\u0074\u0069\u0065\u0073","\u0050\u0072\u006f\u0070\u0065\u0072\u0074\u0069\u0065\u0073",NewProperties );
};