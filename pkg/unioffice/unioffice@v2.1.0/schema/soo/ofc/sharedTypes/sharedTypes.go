//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package sharedTypes ;import (_b "encoding/xml";_e "fmt";_ab "regexp";_a "time";);func (_cge ST_CalendarType )MarshalXML (e *_b .Encoder ,start _b .StartElement )error {return e .EncodeElement (_cge .String (),start );};func (_deg ST_CalendarType )Validate ()error {return _deg .ValidateWithPath ("")};
const ST_UniversalMeasurePattern ="\u002d\u003f\u005b\u0030\u002d\u0039\u005d\u002b\u0028\u005c\u002e\u005b\u0030\u002d\u0039\u005d\u002b\u0029\u003f\u0028\u006d\u006d\u007c\u0063m\u007c\u0069\u006e\u007c\u0070t\u007c\u0070c\u007c\u0070\u0069\u0029";func (_df ST_CryptProv )MarshalXML (e *_b .Encoder ,start _b .StartElement )error {return e .EncodeElement (_df .String (),start );
};const (_ef ="2\u0030\u0030\u0036\u002d\u004a\u0061\u006e\u002d\u0030\u0032";_ag ="\u0031\u0035\u003a\u0030\u0034\u003a\u0030\u0035";_fgd ="\u00320\u0030\u0036\u002d\u00301\u002d\u0030\u0032\u0054\u00315\u003a0\u0034:\u0030\u0035\u005a\u0030\u0037\u003a\u00300";
);type ST_TrueFalse byte ;func (_fd *ST_TrueFalseBlank )UnmarshalXMLAttr (attr _b .Attr )error {switch attr .Value {case "":*_fd =0;case "\u0074":*_fd =1;case "\u0066":*_fd =2;case "\u0074\u0072\u0075\u0065":*_fd =3;case "\u0066\u0061\u006cs\u0065":*_fd =4;
case "\u0054\u0072\u0075\u0065":*_fd =6;case "\u0046\u0061\u006cs\u0065":*_fd =7;};return nil ;};func (_cdd *ST_VerticalAlignRun )UnmarshalXMLAttr (attr _b .Attr )error {switch attr .Value {case "":*_cdd =0;case "\u0062\u0061\u0073\u0065\u006c\u0069\u006e\u0065":*_cdd =1;
case "s\u0075\u0070\u0065\u0072\u0073\u0063\u0072\u0069\u0070\u0074":*_cdd =2;case "\u0073u\u0062\u0073\u0063\u0072\u0069\u0070t":*_cdd =3;};return nil ;};func FormatDate (t _a .Time )string {return t .Format (_ef )};const (ST_CalendarTypeUnset ST_CalendarType =0;
ST_CalendarTypeGregorian ST_CalendarType =1;ST_CalendarTypeGregorianUs ST_CalendarType =2;ST_CalendarTypeGregorianMeFrench ST_CalendarType =3;ST_CalendarTypeGregorianArabic ST_CalendarType =4;ST_CalendarTypeHijri ST_CalendarType =5;ST_CalendarTypeHebrew ST_CalendarType =6;
ST_CalendarTypeTaiwan ST_CalendarType =7;ST_CalendarTypeJapan ST_CalendarType =8;ST_CalendarTypeThai ST_CalendarType =9;ST_CalendarTypeKorea ST_CalendarType =10;ST_CalendarTypeSaka ST_CalendarType =11;ST_CalendarTypeGregorianXlitEnglish ST_CalendarType =12;
ST_CalendarTypeGregorianXlitFrench ST_CalendarType =13;ST_CalendarTypeNone ST_CalendarType =14;);func (_ge ST_TrueFalse )String ()string {switch _ge {case 0:return "";case 1:return "\u0074";case 2:return "\u0066";case 3:return "\u0074\u0072\u0075\u0065";
case 4:return "\u0066\u0061\u006cs\u0065";};return "";};type ST_OnOff1 byte ;func (_dgef ST_AlgType )ValidateWithPath (path string )error {switch _dgef {case 0,1,2:default:return _e .Errorf ("\u0025s\u003a\u0020\u006f\u0075t\u0020\u006f\u0066\u0020\u0072a\u006eg\u0065 \u0076\u0061\u006c\u0075\u0065\u0020\u0025d",path ,int (_dgef ));
};return nil ;};func (_ccd ST_AlgType )String ()string {switch _ccd {case 0:return "";case 1:return "\u0074y\u0070\u0065\u0041\u006e\u0079";case 2:return "\u0063\u0075\u0073\u0074\u006f\u006d";};return "";};func (_d *ST_TwipsMeasure )Validate ()error {return _d .ValidateWithPath ("")};
var ST_UniversalMeasurePatternRe =_ab .MustCompile (ST_UniversalMeasurePattern );func FormatTime (t _a .Time )string {return t .Format (_ag )};func (_fcf ST_AlgType )Validate ()error {return _fcf .ValidateWithPath ("")};func (_ff ST_CryptProv )ValidateWithPath (path string )error {switch _ff {case 0,1,2,3:default:return _e .Errorf ("\u0025s\u003a\u0020\u006f\u0075t\u0020\u006f\u0066\u0020\u0072a\u006eg\u0065 \u0076\u0061\u006c\u0075\u0065\u0020\u0025d",path ,int (_ff ));
};return nil ;};func (_ada ST_TrueFalse )ValidateWithPath (path string )error {switch _ada {case 0,1,2,3,4:default:return _e .Errorf ("\u0025s\u003a\u0020\u006f\u0075t\u0020\u006f\u0066\u0020\u0072a\u006eg\u0065 \u0076\u0061\u006c\u0075\u0065\u0020\u0025d",path ,int (_ada ));
};return nil ;};func (_agfg ST_YAlign )ValidateWithPath (path string )error {switch _agfg {case 0,1,2,3,4,5,6:default:return _e .Errorf ("\u0025s\u003a\u0020\u006f\u0075t\u0020\u006f\u0066\u0020\u0072a\u006eg\u0065 \u0076\u0061\u006c\u0075\u0065\u0020\u0025d",path ,int (_agfg ));
};return nil ;};func (_efbe ST_TrueFalseBlank )MarshalXML (e *_b .Encoder ,start _b .StartElement )error {return e .EncodeElement (_efbe .String (),start );};

// ST_TwipsMeasure is a union type
type ST_TwipsMeasure struct{ST_UnsignedDecimalNumber *uint64 ;ST_PositiveUniversalMeasure *string ;};type ST_CalendarType byte ;func (_dge *ST_AlgType )UnmarshalXML (d *_b .Decoder ,start _b .StartElement )error {_fa ,_fb :=d .Token ();if _fb !=nil {return _fb ;
};if _cdg ,_dbb :=_fa .(_b .EndElement );_dbb &&_cdg .Name ==start .Name {*_dge =1;return nil ;};if _bfd ,_faf :=_fa .(_b .CharData );!_faf {return _e .Errorf ("\u0065\u0078\u0070\u0065\u0063\u0074\u0065\u0064\u0020\u0063\u0068a\u0072\u0020\u0064\u0061\u0074\u0061\u002c\u0020\u0067\u006ft\u0020\u0025\u0054",_fa );
}else {switch string (_bfd ){case "":*_dge =0;case "\u0074y\u0070\u0065\u0041\u006e\u0079":*_dge =1;case "\u0063\u0075\u0073\u0074\u006f\u006d":*_dge =2;};};_fa ,_fb =d .Token ();if _fb !=nil {return _fb ;};if _aef ,_dgd :=_fa .(_b .EndElement );_dgd &&_aef .Name ==start .Name {return nil ;
};return _e .Errorf ("\u0065\u0078\u0070\u0065c\u0074\u0065\u0064\u0020\u0065\u006e\u0064\u0020\u0065\u006ce\u006de\u006e\u0074\u002c\u0020\u0067\u006f\u0074 \u0025\u0076",_fa );};func (_fgdf ST_CryptProv )Validate ()error {return _fgdf .ValidateWithPath ("")};
type ST_YAlign byte ;var ST_PositiveFixedPercentagePatternRe =_ab .MustCompile (ST_PositiveFixedPercentagePattern );func (_ad ST_TrueFalse )MarshalXML (e *_b .Encoder ,start _b .StartElement )error {return e .EncodeElement (_ad .String (),start );};func (_cadb *ST_ConformanceClass )UnmarshalXML (d *_b .Decoder ,start _b .StartElement )error {_ege ,_cff :=d .Token ();
if _cff !=nil {return _cff ;};if _ebd ,_bade :=_ege .(_b .EndElement );_bade &&_ebd .Name ==start .Name {*_cadb =1;return nil ;};if _dda ,_eaf :=_ege .(_b .CharData );!_eaf {return _e .Errorf ("\u0065\u0078\u0070\u0065\u0063\u0074\u0065\u0064\u0020\u0063\u0068a\u0072\u0020\u0064\u0061\u0074\u0061\u002c\u0020\u0067\u006ft\u0020\u0025\u0054",_ege );
}else {switch string (_dda ){case "":*_cadb =0;case "\u0073\u0074\u0072\u0069\u0063\u0074":*_cadb =1;case "\u0074\u0072\u0061n\u0073\u0069\u0074\u0069\u006f\u006e\u0061\u006c":*_cadb =2;};};_ege ,_cff =d .Token ();if _cff !=nil {return _cff ;};if _egd ,_ffcf :=_ege .(_b .EndElement );
_ffcf &&_egd .Name ==start .Name {return nil ;};return _e .Errorf ("\u0065\u0078\u0070\u0065c\u0074\u0065\u0064\u0020\u0065\u006e\u0064\u0020\u0065\u006ce\u006de\u006e\u0074\u002c\u0020\u0067\u006f\u0074 \u0025\u0076",_ege );};func (_efc *ST_YAlign )UnmarshalXMLAttr (attr _b .Attr )error {switch attr .Value {case "":*_efc =0;
case "\u0069\u006e\u006c\u0069\u006e\u0065":*_efc =1;case "\u0074\u006f\u0070":*_efc =2;case "\u0063\u0065\u006e\u0074\u0065\u0072":*_efc =3;case "\u0062\u006f\u0074\u0074\u006f\u006d":*_efc =4;case "\u0069\u006e\u0073\u0069\u0064\u0065":*_efc =5;case "\u006fu\u0074\u0073\u0069\u0064\u0065":*_efc =6;
};return nil ;};var ST_PositivePercentagePatternRe =_ab .MustCompile (ST_PositivePercentagePattern );var ST_FixedPercentagePatternRe =_ab .MustCompile (ST_FixedPercentagePattern );var ST_PositiveUniversalMeasurePatternRe =_ab .MustCompile (ST_PositiveUniversalMeasurePattern );
type ST_XAlign byte ;const ST_PercentagePattern ="-\u003f[\u0030\u002d\u0039\u005d\u002b\u0028\u005c\u002e[\u0030\u002d\u0039\u005d+)\u003f\u0025";const (ST_AlgClassUnset ST_AlgClass =0;ST_AlgClassHash ST_AlgClass =1;ST_AlgClassCustom ST_AlgClass =2;);
func (_bea *ST_AlgClass )UnmarshalXML (d *_b .Decoder ,start _b .StartElement )error {_fga ,_caa :=d .Token ();if _caa !=nil {return _caa ;};if _fgaa ,_cdb :=_fga .(_b .EndElement );_cdb &&_fgaa .Name ==start .Name {*_bea =1;return nil ;};if _ed ,_bged :=_fga .(_b .CharData );
!_bged {return _e .Errorf ("\u0065\u0078\u0070\u0065\u0063\u0074\u0065\u0064\u0020\u0063\u0068a\u0072\u0020\u0064\u0061\u0074\u0061\u002c\u0020\u0067\u006ft\u0020\u0025\u0054",_fga );}else {switch string (_ed ){case "":*_bea =0;case "\u0068\u0061\u0073\u0068":*_bea =1;
case "\u0063\u0075\u0073\u0074\u006f\u006d":*_bea =2;};};_fga ,_caa =d .Token ();if _caa !=nil {return _caa ;};if _bed ,_gg :=_fga .(_b .EndElement );_gg &&_bed .Name ==start .Name {return nil ;};return _e .Errorf ("\u0065\u0078\u0070\u0065c\u0074\u0065\u0064\u0020\u0065\u006e\u0064\u0020\u0065\u006ce\u006de\u006e\u0074\u002c\u0020\u0067\u006f\u0074 \u0025\u0076",_fga );
};func (_edc ST_OnOff1 )ValidateWithPath (path string )error {switch _edc {case 0,1,2:default:return _e .Errorf ("\u0025s\u003a\u0020\u006f\u0075t\u0020\u006f\u0066\u0020\u0072a\u006eg\u0065 \u0076\u0061\u006c\u0075\u0065\u0020\u0025d",path ,int (_edc ));
};return nil ;};const (ST_YAlignUnset ST_YAlign =0;ST_YAlignInline ST_YAlign =1;ST_YAlignTop ST_YAlign =2;ST_YAlignCenter ST_YAlign =3;ST_YAlignBottom ST_YAlign =4;ST_YAlignInside ST_YAlign =5;ST_YAlignOutside ST_YAlign =6;);func (_af ST_OnOff )String ()string {if _af .Bool !=nil {return _e .Sprintf ("\u0025\u0076",*_af .Bool );
};if _af .ST_OnOff1 !=ST_OnOff1Unset {return _af .ST_OnOff1 .String ();};return "";};func (_be *ST_OnOff )ValidateWithPath (path string )error {_c :=[]string {};if _be .Bool !=nil {_c =append (_c ,"\u0042\u006f\u006f\u006c");};if _be .ST_OnOff1 !=ST_OnOff1Unset {_c =append (_c ,"\u0053T\u005f\u004f\u006e\u004f\u0066\u00661");
};if len (_c )> 1{return _e .Errorf ("%\u0073\u0020\u0074\u006f\u006f\u0020m\u0061\u006e\u0079\u0020\u006d\u0065\u006d\u0062\u0065r\u0073\u0020\u0073e\u0074:\u0020\u0025\u0076",path ,_c );};return nil ;};func (_fbbd ST_TrueFalse )Validate ()error {return _fbbd .ValidateWithPath ("")};
func (_bcf *ST_AlgType )UnmarshalXMLAttr (attr _b .Attr )error {switch attr .Value {case "":*_bcf =0;case "\u0074y\u0070\u0065\u0041\u006e\u0079":*_bcf =1;case "\u0063\u0075\u0073\u0074\u006f\u006d":*_bcf =2;};return nil ;};func (_agf ST_OnOff1 )MarshalXMLAttr (name _b .Name )(_b .Attr ,error ){_da :=_b .Attr {};
_da .Name =name ;switch _agf {case ST_OnOff1Unset :_da .Value ="";case ST_OnOff1On :_da .Value ="\u006f\u006e";case ST_OnOff1Off :_da .Value ="\u006f\u0066\u0066";};return _da ,nil ;};func (_cf *ST_XAlign )UnmarshalXMLAttr (attr _b .Attr )error {switch attr .Value {case "":*_cf =0;
case "\u006c\u0065\u0066\u0074":*_cf =1;case "\u0063\u0065\u006e\u0074\u0065\u0072":*_cf =2;case "\u0072\u0069\u0067h\u0074":*_cf =3;case "\u0069\u006e\u0073\u0069\u0064\u0065":*_cf =4;case "\u006fu\u0074\u0073\u0069\u0064\u0065":*_cf =5;};return nil ;
};func (_acb ST_VerticalAlignRun )MarshalXMLAttr (name _b .Name )(_b .Attr ,error ){_ec :=_b .Attr {};_ec .Name =name ;switch _acb {case ST_VerticalAlignRunUnset :_ec .Value ="";case ST_VerticalAlignRunBaseline :_ec .Value ="\u0062\u0061\u0073\u0065\u006c\u0069\u006e\u0065";
case ST_VerticalAlignRunSuperscript :_ec .Value ="s\u0075\u0070\u0065\u0072\u0073\u0063\u0072\u0069\u0070\u0074";case ST_VerticalAlignRunSubscript :_ec .Value ="\u0073u\u0062\u0073\u0063\u0072\u0069\u0070t";};return _ec ,nil ;};func (_ffcg ST_ConformanceClass )MarshalXML (e *_b .Encoder ,start _b .StartElement )error {return e .EncodeElement (_ffcg .String (),start );
};type ST_ConformanceClass byte ;func (_bd *ST_CalendarType )UnmarshalXML (d *_b .Decoder ,start _b .StartElement )error {_ba ,_abf :=d .Token ();if _abf !=nil {return _abf ;};if _efb ,_cd :=_ba .(_b .EndElement );_cd &&_efb .Name ==start .Name {*_bd =1;
return nil ;};if _cab ,_bc :=_ba .(_b .CharData );!_bc {return _e .Errorf ("\u0065\u0078\u0070\u0065\u0063\u0074\u0065\u0064\u0020\u0063\u0068a\u0072\u0020\u0064\u0061\u0074\u0061\u002c\u0020\u0067\u006ft\u0020\u0025\u0054",_ba );}else {switch string (_cab ){case "":*_bd =0;
case "\u0067r\u0065\u0067\u006f\u0072\u0069\u0061n":*_bd =1;case "g\u0072\u0065\u0067\u006f\u0072\u0069\u0061\u006e\u0055\u0073":*_bd =2;case "\u0067\u0072\u0065\u0067\u006f\u0072\u0069\u0061\u006e\u004d\u0065\u0046r\u0065\u006e\u0063\u0068":*_bd =3;case "\u0067r\u0065g\u006f\u0072\u0069\u0061\u006e\u0041\u0072\u0061\u0062\u0069\u0063":*_bd =4;
case "\u0068\u0069\u006ar\u0069":*_bd =5;case "\u0068\u0065\u0062\u0072\u0065\u0077":*_bd =6;case "\u0074\u0061\u0069\u0077\u0061\u006e":*_bd =7;case "\u006a\u0061\u0070a\u006e":*_bd =8;case "\u0074\u0068\u0061\u0069":*_bd =9;case "\u006b\u006f\u0072e\u0061":*_bd =10;
case "\u0073\u0061\u006b\u0061":*_bd =11;case "g\u0072e\u0067\u006f\u0072\u0069\u0061\u006e\u0058\u006ci\u0074\u0045\u006e\u0067li\u0073\u0068":*_bd =12;case "\u0067\u0072\u0065\u0067or\u0069\u0061\u006e\u0058\u006c\u0069\u0074\u0046\u0072\u0065\u006e\u0063\u0068":*_bd =13;
case "\u006e\u006f\u006e\u0065":*_bd =14;};};_ba ,_abf =d .Token ();if _abf !=nil {return _abf ;};if _bbd ,_dgc :=_ba .(_b .EndElement );_dgc &&_bbd .Name ==start .Name {return nil ;};return _e .Errorf ("\u0065\u0078\u0070\u0065c\u0074\u0065\u0064\u0020\u0065\u006e\u0064\u0020\u0065\u006ce\u006de\u006e\u0074\u002c\u0020\u0067\u006f\u0074 \u0025\u0076",_ba );
};func (_cdgf *ST_TrueFalse )UnmarshalXML (d *_b .Decoder ,start _b .StartElement )error {_gcc ,_bec :=d .Token ();if _bec !=nil {return _bec ;};if _bbdd ,_gdd :=_gcc .(_b .EndElement );_gdd &&_bbdd .Name ==start .Name {*_cdgf =1;return nil ;};if _cag ,_ffc :=_gcc .(_b .CharData );
!_ffc {return _e .Errorf ("\u0065\u0078\u0070\u0065\u0063\u0074\u0065\u0064\u0020\u0063\u0068a\u0072\u0020\u0064\u0061\u0074\u0061\u002c\u0020\u0067\u006ft\u0020\u0025\u0054",_gcc );}else {switch string (_cag ){case "":*_cdgf =0;case "\u0074":*_cdgf =1;
case "\u0066":*_cdgf =2;case "\u0074\u0072\u0075\u0065":*_cdgf =3;case "\u0066\u0061\u006cs\u0065":*_cdgf =4;};};_gcc ,_bec =d .Token ();if _bec !=nil {return _bec ;};if _bda ,_cdgd :=_gcc .(_b .EndElement );_cdgd &&_bda .Name ==start .Name {return nil ;
};return _e .Errorf ("\u0065\u0078\u0070\u0065c\u0074\u0065\u0064\u0020\u0065\u006e\u0064\u0020\u0065\u006ce\u006de\u006e\u0074\u002c\u0020\u0067\u006f\u0074 \u0025\u0076",_gcc );};type ST_AlgType byte ;func (_dee ST_XAlign )ValidateWithPath (path string )error {switch _dee {case 0,1,2,3,4,5:default:return _e .Errorf ("\u0025s\u003a\u0020\u006f\u0075t\u0020\u006f\u0066\u0020\u0072a\u006eg\u0065 \u0076\u0061\u006c\u0075\u0065\u0020\u0025d",path ,int (_dee ));
};return nil ;};func FormatDateTime (t _a .Time )string {return t .Format (_fgd )};func (_acc ST_XAlign )MarshalXML (e *_b .Encoder ,start _b .StartElement )error {return e .EncodeElement (_acc .String (),start );};func (_dcg ST_AlgClass )MarshalXMLAttr (name _b .Name )(_b .Attr ,error ){_cc :=_b .Attr {};
_cc .Name =name ;switch _dcg {case ST_AlgClassUnset :_cc .Value ="";case ST_AlgClassHash :_cc .Value ="\u0068\u0061\u0073\u0068";case ST_AlgClassCustom :_cc .Value ="\u0063\u0075\u0073\u0074\u006f\u006d";};return _cc ,nil ;};const ST_PositivePercentagePattern ="\u005b0\u002d9\u005d\u002b\u0028\u005c\u002e[\u0030\u002d9\u005d\u002b\u0029\u003f\u0025";
func (_eab *ST_XAlign )UnmarshalXML (d *_b .Decoder ,start _b .StartElement )error {_ecb ,_fae :=d .Token ();if _fae !=nil {return _fae ;};if _faa ,_fgba :=_ecb .(_b .EndElement );_fgba &&_faa .Name ==start .Name {*_eab =1;return nil ;};if _bff ,_abfd :=_ecb .(_b .CharData );
!_abfd {return _e .Errorf ("\u0065\u0078\u0070\u0065\u0063\u0074\u0065\u0064\u0020\u0063\u0068a\u0072\u0020\u0064\u0061\u0074\u0061\u002c\u0020\u0067\u006ft\u0020\u0025\u0054",_ecb );}else {switch string (_bff ){case "":*_eab =0;case "\u006c\u0065\u0066\u0074":*_eab =1;
case "\u0063\u0065\u006e\u0074\u0065\u0072":*_eab =2;case "\u0072\u0069\u0067h\u0074":*_eab =3;case "\u0069\u006e\u0073\u0069\u0064\u0065":*_eab =4;case "\u006fu\u0074\u0073\u0069\u0064\u0065":*_eab =5;};};_ecb ,_fae =d .Token ();if _fae !=nil {return _fae ;
};if _ceda ,_bae :=_ecb .(_b .EndElement );_bae &&_ceda .Name ==start .Name {return nil ;};return _e .Errorf ("\u0065\u0078\u0070\u0065c\u0074\u0065\u0064\u0020\u0065\u006e\u0064\u0020\u0065\u006ce\u006de\u006e\u0074\u002c\u0020\u0067\u006f\u0074 \u0025\u0076",_ecb );
};func (_ccg *ST_OnOff1 )UnmarshalXMLAttr (attr _b .Attr )error {switch attr .Value {case "":*_ccg =0;case "\u006f\u006e":*_ccg =1;case "\u006f\u0066\u0066":*_ccg =2;};return nil ;};func (_ac *ST_TwipsMeasure )ValidateWithPath (path string )error {_bg :=[]string {};
if _ac .ST_UnsignedDecimalNumber !=nil {_bg =append (_bg ,"\u0053T\u005f\u0055\u006e\u0073\u0069\u0067\u006e\u0065\u0064\u0044\u0065c\u0069\u006d\u0061\u006c\u004e\u0075\u006d\u0062\u0065\u0072");};if _ac .ST_PositiveUniversalMeasure !=nil {_bg =append (_bg ,"S\u0054\u005f\u0050\u006f\u0073\u0069t\u0069\u0076\u0065\u0055\u006e\u0069\u0076\u0065\u0072s\u0061\u006c\u004de\u0061s\u0075\u0072\u0065");
};if len (_bg )> 1{return _e .Errorf ("%\u0073\u0020\u0074\u006f\u006f\u0020m\u0061\u006e\u0079\u0020\u006d\u0065\u006d\u0062\u0065r\u0073\u0020\u0073e\u0074:\u0020\u0025\u0076",path ,_bg );};return nil ;};func (_abd ST_TwipsMeasure )String ()string {if _abd .ST_UnsignedDecimalNumber !=nil {return _e .Sprintf ("\u0025\u0076",*_abd .ST_UnsignedDecimalNumber );
};if _abd .ST_PositiveUniversalMeasure !=nil {return _e .Sprintf ("\u0025\u0076",*_abd .ST_PositiveUniversalMeasure );};return "";};func (_afg ST_OnOff1 )String ()string {switch _afg {case 0:return "";case 1:return "\u006f\u006e";case 2:return "\u006f\u0066\u0066";
};return "";};func (_dae ST_TrueFalseBlank )MarshalXMLAttr (name _b .Name )(_b .Attr ,error ){_ga :=_b .Attr {};_ga .Name =name ;switch _dae {case ST_TrueFalseBlankUnset :_ga .Value ="";case ST_TrueFalseBlankT :_ga .Value ="\u0074";case ST_TrueFalseBlankF :_ga .Value ="\u0066";
case ST_TrueFalseBlankTrue :_ga .Value ="\u0074\u0072\u0075\u0065";case ST_TrueFalseBlankFalse :_ga .Value ="\u0066\u0061\u006cs\u0065";case ST_TrueFalseBlankTrue_ :_ga .Value ="\u0054\u0072\u0075\u0065";case ST_TrueFalseBlankFalse_ :_ga .Value ="\u0046\u0061\u006cs\u0065";
};return _ga ,nil ;};var ST_PercentagePatternRe =_ab .MustCompile (ST_PercentagePattern );func (_db ST_AlgClass )MarshalXML (e *_b .Encoder ,start _b .StartElement )error {return e .EncodeElement (_db .String (),start );};func (_fec ST_VerticalAlignRun )String ()string {switch _fec {case 0:return "";
case 1:return "\u0062\u0061\u0073\u0065\u006c\u0069\u006e\u0065";case 2:return "s\u0075\u0070\u0065\u0072\u0073\u0063\u0072\u0069\u0070\u0074";case 3:return "\u0073u\u0062\u0073\u0063\u0072\u0069\u0070t";};return "";};func (_bge ST_CalendarType )ValidateWithPath (path string )error {switch _bge {case 0,1,2,3,4,5,6,7,8,9,10,11,12,13,14:default:return _e .Errorf ("\u0025s\u003a\u0020\u006f\u0075t\u0020\u006f\u0066\u0020\u0072a\u006eg\u0065 \u0076\u0061\u006c\u0075\u0065\u0020\u0025d",path ,int (_bge ));
};return nil ;};func (_gcg *ST_CryptProv )UnmarshalXMLAttr (attr _b .Attr )error {switch attr .Value {case "":*_gcg =0;case "\u0072\u0073\u0061\u0041\u0045\u0053":*_gcg =1;case "\u0072s\u0061\u0046\u0075\u006c\u006c":*_gcg =2;case "\u0063\u0075\u0073\u0074\u006f\u006d":*_gcg =3;
};return nil ;};func (_cdc *ST_YAlign )UnmarshalXML (d *_b .Decoder ,start _b .StartElement )error {_afc ,_bfce :=d .Token ();if _bfce !=nil {return _bfce ;};if _cgb ,_efa :=_afc .(_b .EndElement );_efa &&_cgb .Name ==start .Name {*_cdc =1;return nil ;
};if _ega ,_agb :=_afc .(_b .CharData );!_agb {return _e .Errorf ("\u0065\u0078\u0070\u0065\u0063\u0074\u0065\u0064\u0020\u0063\u0068a\u0072\u0020\u0064\u0061\u0074\u0061\u002c\u0020\u0067\u006ft\u0020\u0025\u0054",_afc );}else {switch string (_ega ){case "":*_cdc =0;
case "\u0069\u006e\u006c\u0069\u006e\u0065":*_cdc =1;case "\u0074\u006f\u0070":*_cdc =2;case "\u0063\u0065\u006e\u0074\u0065\u0072":*_cdc =3;case "\u0062\u006f\u0074\u0074\u006f\u006d":*_cdc =4;case "\u0069\u006e\u0073\u0069\u0064\u0065":*_cdc =5;case "\u006fu\u0074\u0073\u0069\u0064\u0065":*_cdc =6;
};};_afc ,_bfce =d .Token ();if _bfce !=nil {return _bfce ;};if _dgf ,_dfeg :=_afc .(_b .EndElement );_dfeg &&_dgf .Name ==start .Name {return nil ;};return _e .Errorf ("\u0065\u0078\u0070\u0065c\u0074\u0065\u0064\u0020\u0065\u006e\u0064\u0020\u0065\u006ce\u006de\u006e\u0074\u002c\u0020\u0067\u006f\u0074 \u0025\u0076",_afc );
};func (_fbb ST_OnOff1 )Validate ()error {return _fbb .ValidateWithPath ("")};func (_ddfe *ST_VerticalAlignRun )UnmarshalXML (d *_b .Decoder ,start _b .StartElement )error {_ddfa ,_ffd :=d .Token ();if _ffd !=nil {return _ffd ;};if _ced ,_fcd :=_ddfa .(_b .EndElement );
_fcd &&_ced .Name ==start .Name {*_ddfe =1;return nil ;};if _eg ,_gddd :=_ddfa .(_b .CharData );!_gddd {return _e .Errorf ("\u0065\u0078\u0070\u0065\u0063\u0074\u0065\u0064\u0020\u0063\u0068a\u0072\u0020\u0064\u0061\u0074\u0061\u002c\u0020\u0067\u006ft\u0020\u0025\u0054",_ddfa );
}else {switch string (_eg ){case "":*_ddfe =0;case "\u0062\u0061\u0073\u0065\u006c\u0069\u006e\u0065":*_ddfe =1;case "s\u0075\u0070\u0065\u0072\u0073\u0063\u0072\u0069\u0070\u0074":*_ddfe =2;case "\u0073u\u0062\u0073\u0063\u0072\u0069\u0070t":*_ddfe =3;
};};_ddfa ,_ffd =d .Token ();if _ffd !=nil {return _ffd ;};if _eag ,_gga :=_ddfa .(_b .EndElement );_gga &&_eag .Name ==start .Name {return nil ;};return _e .Errorf ("\u0065\u0078\u0070\u0065c\u0074\u0065\u0064\u0020\u0065\u006e\u0064\u0020\u0065\u006ce\u006de\u006e\u0074\u002c\u0020\u0067\u006f\u0074 \u0025\u0076",_ddfa );
};func (_gc *ST_CalendarType )UnmarshalXMLAttr (attr _b .Attr )error {switch attr .Value {case "":*_gc =0;case "\u0067r\u0065\u0067\u006f\u0072\u0069\u0061n":*_gc =1;case "g\u0072\u0065\u0067\u006f\u0072\u0069\u0061\u006e\u0055\u0073":*_gc =2;case "\u0067\u0072\u0065\u0067\u006f\u0072\u0069\u0061\u006e\u004d\u0065\u0046r\u0065\u006e\u0063\u0068":*_gc =3;
case "\u0067r\u0065g\u006f\u0072\u0069\u0061\u006e\u0041\u0072\u0061\u0062\u0069\u0063":*_gc =4;case "\u0068\u0069\u006ar\u0069":*_gc =5;case "\u0068\u0065\u0062\u0072\u0065\u0077":*_gc =6;case "\u0074\u0061\u0069\u0077\u0061\u006e":*_gc =7;case "\u006a\u0061\u0070a\u006e":*_gc =8;
case "\u0074\u0068\u0061\u0069":*_gc =9;case "\u006b\u006f\u0072e\u0061":*_gc =10;case "\u0073\u0061\u006b\u0061":*_gc =11;case "g\u0072e\u0067\u006f\u0072\u0069\u0061\u006e\u0058\u006ci\u0074\u0045\u006e\u0067li\u0073\u0068":*_gc =12;case "\u0067\u0072\u0065\u0067or\u0069\u0061\u006e\u0058\u006c\u0069\u0074\u0046\u0072\u0065\u006e\u0063\u0068":*_gc =13;
case "\u006e\u006f\u006e\u0065":*_gc =14;};return nil ;};func (_cgd ST_YAlign )MarshalXMLAttr (name _b .Name )(_b .Attr ,error ){_aga :=_b .Attr {};_aga .Name =name ;switch _cgd {case ST_YAlignUnset :_aga .Value ="";case ST_YAlignInline :_aga .Value ="\u0069\u006e\u006c\u0069\u006e\u0065";
case ST_YAlignTop :_aga .Value ="\u0074\u006f\u0070";case ST_YAlignCenter :_aga .Value ="\u0063\u0065\u006e\u0074\u0065\u0072";case ST_YAlignBottom :_aga .Value ="\u0062\u006f\u0074\u0074\u006f\u006d";case ST_YAlignInside :_aga .Value ="\u0069\u006e\u0073\u0069\u0064\u0065";
case ST_YAlignOutside :_aga .Value ="\u006fu\u0074\u0073\u0069\u0064\u0065";};return _aga ,nil ;};func _cg (_fg bool )uint8 {if _fg {return 1;};return 0;};func (_ebe ST_OnOff1 )MarshalXML (e *_b .Encoder ,start _b .StartElement )error {return e .EncodeElement (_ebe .String (),start );
};func ParseUnionST_OnOff (s string )(ST_OnOff ,error ){_dg :=ST_OnOff {};switch s {case "\u0074\u0072\u0075\u0065","\u0031","\u006f\u006e":_g :=true ;_dg .Bool =&_g ;default:_gd :=false ;_dg .Bool =&_gd ;};return _dg ,nil ;};const (ST_TrueFalseBlankUnset ST_TrueFalseBlank =0;
ST_TrueFalseBlankT ST_TrueFalseBlank =1;ST_TrueFalseBlankF ST_TrueFalseBlank =2;ST_TrueFalseBlankTrue ST_TrueFalseBlank =3;ST_TrueFalseBlankFalse ST_TrueFalseBlank =4;ST_TrueFalseBlankTrue_ ST_TrueFalseBlank =6;ST_TrueFalseBlankFalse_ ST_TrueFalseBlank =7;
);func (_bgc ST_AlgClass )String ()string {switch _bgc {case 0:return "";case 1:return "\u0068\u0061\u0073\u0068";case 2:return "\u0063\u0075\u0073\u0074\u006f\u006d";};return "";};func (_fdb ST_YAlign )String ()string {switch _fdb {case 0:return "";case 1:return "\u0069\u006e\u006c\u0069\u006e\u0065";
case 2:return "\u0074\u006f\u0070";case 3:return "\u0063\u0065\u006e\u0074\u0065\u0072";case 4:return "\u0062\u006f\u0074\u0074\u006f\u006d";case 5:return "\u0069\u006e\u0073\u0069\u0064\u0065";case 6:return "\u006fu\u0074\u0073\u0069\u0064\u0065";};return "";
};const (ST_CryptProvUnset ST_CryptProv =0;ST_CryptProvRsaAES ST_CryptProv =1;ST_CryptProvRsaFull ST_CryptProv =2;ST_CryptProvCustom ST_CryptProv =3;);func (_gba *ST_TrueFalse )UnmarshalXMLAttr (attr _b .Attr )error {switch attr .Value {case "":*_gba =0;
case "\u0074":*_gba =1;case "\u0066":*_gba =2;case "\u0074\u0072\u0075\u0065":*_gba =3;case "\u0066\u0061\u006cs\u0065":*_gba =4;};return nil ;};func (_fcfe ST_VerticalAlignRun )MarshalXML (e *_b .Encoder ,start _b .StartElement )error {return e .EncodeElement (_fcfe .String (),start );
};func (_dbcb ST_ConformanceClass )Validate ()error {return _dbcb .ValidateWithPath ("")};func (_fc ST_CalendarType )String ()string {switch _fc {case 0:return "";case 1:return "\u0067r\u0065\u0067\u006f\u0072\u0069\u0061n";case 2:return "g\u0072\u0065\u0067\u006f\u0072\u0069\u0061\u006e\u0055\u0073";
case 3:return "\u0067\u0072\u0065\u0067\u006f\u0072\u0069\u0061\u006e\u004d\u0065\u0046r\u0065\u006e\u0063\u0068";case 4:return "\u0067r\u0065g\u006f\u0072\u0069\u0061\u006e\u0041\u0072\u0061\u0062\u0069\u0063";case 5:return "\u0068\u0069\u006ar\u0069";
case 6:return "\u0068\u0065\u0062\u0072\u0065\u0077";case 7:return "\u0074\u0061\u0069\u0077\u0061\u006e";case 8:return "\u006a\u0061\u0070a\u006e";case 9:return "\u0074\u0068\u0061\u0069";case 10:return "\u006b\u006f\u0072e\u0061";case 11:return "\u0073\u0061\u006b\u0061";
case 12:return "g\u0072e\u0067\u006f\u0072\u0069\u0061\u006e\u0058\u006ci\u0074\u0045\u006e\u0067li\u0073\u0068";case 13:return "\u0067\u0072\u0065\u0067or\u0069\u0061\u006e\u0058\u006c\u0069\u0074\u0046\u0072\u0065\u006e\u0063\u0068";case 14:return "\u006e\u006f\u006e\u0065";
};return "";};func (_cgdb ST_ConformanceClass )ValidateWithPath (path string )error {switch _cgdb {case 0,1,2:default:return _e .Errorf ("\u0025s\u003a\u0020\u006f\u0075t\u0020\u006f\u0066\u0020\u0072a\u006eg\u0065 \u0076\u0061\u006c\u0075\u0065\u0020\u0025d",path ,int (_cgdb ));
};return nil ;};func (_degc ST_TrueFalse )MarshalXMLAttr (name _b .Name )(_b .Attr ,error ){_afge :=_b .Attr {};_afge .Name =name ;switch _degc {case ST_TrueFalseUnset :_afge .Value ="";case ST_TrueFalseT :_afge .Value ="\u0074";case ST_TrueFalseF :_afge .Value ="\u0066";
case ST_TrueFalseTrue :_afge .Value ="\u0074\u0072\u0075\u0065";case ST_TrueFalseFalse :_afge .Value ="\u0066\u0061\u006cs\u0065";};return _afge ,nil ;};func (_cgf ST_CryptProv )String ()string {switch _cgf {case 0:return "";case 1:return "\u0072\u0073\u0061\u0041\u0045\u0053";
case 2:return "\u0072s\u0061\u0046\u0075\u006c\u006c";case 3:return "\u0063\u0075\u0073\u0074\u006f\u006d";};return "";};const (ST_ConformanceClassUnset ST_ConformanceClass =0;ST_ConformanceClassStrict ST_ConformanceClass =1;ST_ConformanceClassTransitional ST_ConformanceClass =2;
);func (_bf ST_OnOff )MarshalXML (e *_b .Encoder ,start _b .StartElement )error {e .EncodeToken (start );if _bf .Bool !=nil {e .EncodeToken (_b .CharData (_e .Sprintf ("\u0025\u0064",_cg (*_bf .Bool ))));};if _bf .ST_OnOff1 !=ST_OnOff1Unset {e .EncodeToken (_b .CharData (_bf .ST_OnOff1 .String ()));
};return e .EncodeToken (_b .EndElement {Name :start .Name });};func (_ce ST_TwipsMeasure )MarshalXML (e *_b .Encoder ,start _b .StartElement )error {e .EncodeToken (start );if _ce .ST_UnsignedDecimalNumber !=nil {e .EncodeToken (_b .CharData (_e .Sprintf ("\u0025\u0064",*_ce .ST_UnsignedDecimalNumber )));
};if _ce .ST_PositiveUniversalMeasure !=nil {e .EncodeToken (_b .CharData (*_ce .ST_PositiveUniversalMeasure ));};return e .EncodeToken (_b .EndElement {Name :start .Name });};func (_dcd *ST_AlgClass )UnmarshalXMLAttr (attr _b .Attr )error {switch attr .Value {case "":*_dcd =0;
case "\u0068\u0061\u0073\u0068":*_dcd =1;case "\u0063\u0075\u0073\u0074\u006f\u006d":*_dcd =2;};return nil ;};type ST_VerticalAlignRun byte ;func (_fgb ST_CryptProv )MarshalXMLAttr (name _b .Name )(_b .Attr ,error ){_cdbf :=_b .Attr {};_cdbf .Name =name ;
switch _fgb {case ST_CryptProvUnset :_cdbf .Value ="";case ST_CryptProvRsaAES :_cdbf .Value ="\u0072\u0073\u0061\u0041\u0045\u0053";case ST_CryptProvRsaFull :_cdbf .Value ="\u0072s\u0061\u0046\u0075\u006c\u006c";case ST_CryptProvCustom :_cdbf .Value ="\u0063\u0075\u0073\u0074\u006f\u006d";
};return _cdbf ,nil ;};func (_dc ST_CalendarType )MarshalXMLAttr (name _b .Name )(_b .Attr ,error ){_ee :=_b .Attr {};_ee .Name =name ;switch _dc {case ST_CalendarTypeUnset :_ee .Value ="";case ST_CalendarTypeGregorian :_ee .Value ="\u0067r\u0065\u0067\u006f\u0072\u0069\u0061n";
case ST_CalendarTypeGregorianUs :_ee .Value ="g\u0072\u0065\u0067\u006f\u0072\u0069\u0061\u006e\u0055\u0073";case ST_CalendarTypeGregorianMeFrench :_ee .Value ="\u0067\u0072\u0065\u0067\u006f\u0072\u0069\u0061\u006e\u004d\u0065\u0046r\u0065\u006e\u0063\u0068";
case ST_CalendarTypeGregorianArabic :_ee .Value ="\u0067r\u0065g\u006f\u0072\u0069\u0061\u006e\u0041\u0072\u0061\u0062\u0069\u0063";case ST_CalendarTypeHijri :_ee .Value ="\u0068\u0069\u006ar\u0069";case ST_CalendarTypeHebrew :_ee .Value ="\u0068\u0065\u0062\u0072\u0065\u0077";
case ST_CalendarTypeTaiwan :_ee .Value ="\u0074\u0061\u0069\u0077\u0061\u006e";case ST_CalendarTypeJapan :_ee .Value ="\u006a\u0061\u0070a\u006e";case ST_CalendarTypeThai :_ee .Value ="\u0074\u0068\u0061\u0069";case ST_CalendarTypeKorea :_ee .Value ="\u006b\u006f\u0072e\u0061";
case ST_CalendarTypeSaka :_ee .Value ="\u0073\u0061\u006b\u0061";case ST_CalendarTypeGregorianXlitEnglish :_ee .Value ="g\u0072e\u0067\u006f\u0072\u0069\u0061\u006e\u0058\u006ci\u0074\u0045\u006e\u0067li\u0073\u0068";case ST_CalendarTypeGregorianXlitFrench :_ee .Value ="\u0067\u0072\u0065\u0067or\u0069\u0061\u006e\u0058\u006c\u0069\u0074\u0046\u0072\u0065\u006e\u0063\u0068";
case ST_CalendarTypeNone :_ee .Value ="\u006e\u006f\u006e\u0065";};return _ee ,nil ;};type ST_AlgClass byte ;type ST_CryptProv byte ;type ST_TrueFalseBlank byte ;const ST_PositiveFixedPercentagePattern ="\u0028\u0028\u0031\u0030\u0030\u0029\u007c\u0028\u005b\u0030\u002d\u0039\u005d\u005b\u0030\u002d\u0039\u005d\u003f\u0029\u0029\u0028\u005c\u002e[\u0030\u002d\u0039\u005d\u005b0\u002d\u0039]\u003f\u0029\u003f\u0025";
func (_gaa ST_YAlign )Validate ()error {return _gaa .ValidateWithPath ("")};func (_dde ST_VerticalAlignRun )ValidateWithPath (path string )error {switch _dde {case 0,1,2,3:default:return _e .Errorf ("\u0025s\u003a\u0020\u006f\u0075t\u0020\u006f\u0066\u0020\u0072a\u006eg\u0065 \u0076\u0061\u006c\u0075\u0065\u0020\u0025d",path ,int (_dde ));
};return nil ;};

// ST_OnOff is a union type
type ST_OnOff struct{Bool *bool ;ST_OnOff1 ST_OnOff1 ;};func (_cfg ST_ConformanceClass )MarshalXMLAttr (name _b .Name )(_b .Attr ,error ){_gag :=_b .Attr {};_gag .Name =name ;switch _cfg {case ST_ConformanceClassUnset :_gag .Value ="";case ST_ConformanceClassStrict :_gag .Value ="\u0073\u0074\u0072\u0069\u0063\u0074";
case ST_ConformanceClassTransitional :_gag .Value ="\u0074\u0072\u0061n\u0073\u0069\u0074\u0069\u006f\u006e\u0061\u006c";};return _gag ,nil ;};func (_eeb ST_AlgType )MarshalXML (e *_b .Encoder ,start _b .StartElement )error {return e .EncodeElement (_eeb .String (),start );
};func (_fca *ST_TrueFalseBlank )UnmarshalXML (d *_b .Decoder ,start _b .StartElement )error {_dba ,_bdg :=d .Token ();if _bdg !=nil {return _bdg ;};if _dcgf ,_fcfd :=_dba .(_b .EndElement );_fcfd &&_dcgf .Name ==start .Name {*_fca =1;return nil ;};if _bga ,_bfc :=_dba .(_b .CharData );
!_bfc {return _e .Errorf ("\u0065\u0078\u0070\u0065\u0063\u0074\u0065\u0064\u0020\u0063\u0068a\u0072\u0020\u0064\u0061\u0074\u0061\u002c\u0020\u0067\u006ft\u0020\u0025\u0054",_dba );}else {switch string (_bga ){case "":*_fca =0;case "\u0074":*_fca =1;case "\u0066":*_fca =2;
case "\u0074\u0072\u0075\u0065":*_fca =3;case "\u0066\u0061\u006cs\u0065":*_fca =4;case "\u0054\u0072\u0075\u0065":*_fca =6;case "\u0046\u0061\u006cs\u0065":*_fca =7;};};_dba ,_bdg =d .Token ();if _bdg !=nil {return _bdg ;};if _gf ,_gddf :=_dba .(_b .EndElement );
_gddf &&_gf .Name ==start .Name {return nil ;};return _e .Errorf ("\u0065\u0078\u0070\u0065c\u0074\u0065\u0064\u0020\u0065\u006e\u0064\u0020\u0065\u006ce\u006de\u006e\u0074\u002c\u0020\u0067\u006f\u0074 \u0025\u0076",_dba );};const (ST_VerticalAlignRunUnset ST_VerticalAlignRun =0;
ST_VerticalAlignRunBaseline ST_VerticalAlignRun =1;ST_VerticalAlignRunSuperscript ST_VerticalAlignRun =2;ST_VerticalAlignRunSubscript ST_VerticalAlignRun =3;);const (ST_OnOff1Unset ST_OnOff1 =0;ST_OnOff1On ST_OnOff1 =1;ST_OnOff1Off ST_OnOff1 =2;);func (_bcc ST_XAlign )MarshalXMLAttr (name _b .Name )(_b .Attr ,error ){_afd :=_b .Attr {};
_afd .Name =name ;switch _bcc {case ST_XAlignUnset :_afd .Value ="";case ST_XAlignLeft :_afd .Value ="\u006c\u0065\u0066\u0074";case ST_XAlignCenter :_afd .Value ="\u0063\u0065\u006e\u0074\u0065\u0072";case ST_XAlignRight :_afd .Value ="\u0072\u0069\u0067h\u0074";
case ST_XAlignInside :_afd .Value ="\u0069\u006e\u0073\u0069\u0064\u0065";case ST_XAlignOutside :_afd .Value ="\u006fu\u0074\u0073\u0069\u0064\u0065";};return _afd ,nil ;};const (ST_AlgTypeUnset ST_AlgType =0;ST_AlgTypeTypeAny ST_AlgType =1;ST_AlgTypeCustom ST_AlgType =2;
);func (_ggd ST_YAlign )MarshalXML (e *_b .Encoder ,start _b .StartElement )error {return e .EncodeElement (_ggd .String (),start );};var ST_GuidPatternRe =_ab .MustCompile (ST_GuidPattern );const ST_PositiveUniversalMeasurePattern ="\u005b\u0030-9\u005d\u002b\u0028\\\u002e\u005b\u0030\u002d9]+\u0029?(\u006d\u006d\u007c\u0063\u006d\u007c\u0069n|\u0070\u0074\u007c\u0070\u0063\u007c\u0070i\u0029";
func (_fgde ST_XAlign )Validate ()error {return _fgde .ValidateWithPath ("")};func (_bcb ST_AlgClass )Validate ()error {return _bcb .ValidateWithPath ("")};func (_ebc ST_ConformanceClass )String ()string {switch _ebc {case 0:return "";case 1:return "\u0073\u0074\u0072\u0069\u0063\u0074";
case 2:return "\u0074\u0072\u0061n\u0073\u0069\u0074\u0069\u006f\u006e\u0061\u006c";};return "";};func (_defa ST_TrueFalseBlank )ValidateWithPath (path string )error {switch _defa {case 0,1,2,3,4,6,7:default:return _e .Errorf ("\u0025s\u003a\u0020\u006f\u0075t\u0020\u006f\u0066\u0020\u0072a\u006eg\u0065 \u0076\u0061\u006c\u0075\u0065\u0020\u0025d",path ,int (_defa ));
};return nil ;};func (_ddf *ST_OnOff1 )UnmarshalXML (d *_b .Decoder ,start _b .StartElement )error {_dbf ,_fgg :=d .Token ();if _fgg !=nil {return _fgg ;};if _fef ,_bad :=_dbf .(_b .EndElement );_bad &&_fef .Name ==start .Name {*_ddf =1;return nil ;};if _dff ,_aea :=_dbf .(_b .CharData );
!_aea {return _e .Errorf ("\u0065\u0078\u0070\u0065\u0063\u0074\u0065\u0064\u0020\u0063\u0068a\u0072\u0020\u0064\u0061\u0074\u0061\u002c\u0020\u0067\u006ft\u0020\u0025\u0054",_dbf );}else {switch string (_dff ){case "":*_ddf =0;case "\u006f\u006e":*_ddf =1;
case "\u006f\u0066\u0066":*_ddf =2;};};_dbf ,_fgg =d .Token ();if _fgg !=nil {return _fgg ;};if _cad ,_eec :=_dbf .(_b .EndElement );_eec &&_cad .Name ==start .Name {return nil ;};return _e .Errorf ("\u0065\u0078\u0070\u0065c\u0074\u0065\u0064\u0020\u0065\u006e\u0064\u0020\u0065\u006ce\u006de\u006e\u0074\u002c\u0020\u0067\u006f\u0074 \u0025\u0076",_dbf );
};func (_ged ST_TrueFalseBlank )String ()string {switch _ged {case 0:return "";case 1:return "\u0074";case 2:return "\u0066";case 3:return "\u0074\u0072\u0075\u0065";case 4:return "\u0066\u0061\u006cs\u0065";case 6:return "\u0054\u0072\u0075\u0065";case 7:return "\u0046\u0061\u006cs\u0065";
};return "";};func (_fed ST_AlgType )MarshalXMLAttr (name _b .Name )(_b .Attr ,error ){_aa :=_b .Attr {};_aa .Name =name ;switch _fed {case ST_AlgTypeUnset :_aa .Value ="";case ST_AlgTypeTypeAny :_aa .Value ="\u0074y\u0070\u0065\u0041\u006e\u0079";case ST_AlgTypeCustom :_aa .Value ="\u0063\u0075\u0073\u0074\u006f\u006d";
};return _aa ,nil ;};func (_dbg ST_AlgClass )ValidateWithPath (path string )error {switch _dbg {case 0,1,2:default:return _e .Errorf ("\u0025s\u003a\u0020\u006f\u0075t\u0020\u006f\u0066\u0020\u0072a\u006eg\u0065 \u0076\u0061\u006c\u0075\u0065\u0020\u0025d",path ,int (_dbg ));
};return nil ;};const ST_GuidPattern ="\u005c\u007b\u005b\u0030\u002d\u0039\u0041\u002d\u0046\u005d\u007b\u0038\u007d\u002d\u005b\u0030\u002d9\u0041\u002d\u0046\u005d\u007b\u0034\u007d\u002d\u005b\u0030-\u0039\u0041\u002d\u0046\u005d\u007b\u0034\u007d\u002d\u005b\u0030\u002d\u0039\u0041\u002d\u0046\u005d\u007b4\u007d\u002d\u005b\u0030\u002d\u0039A\u002d\u0046]\u007b\u00312\u007d\\\u007d";
func (_dad *ST_ConformanceClass )UnmarshalXMLAttr (attr _b .Attr )error {switch attr .Value {case "":*_dad =0;case "\u0073\u0074\u0072\u0069\u0063\u0074":*_dad =1;case "\u0074\u0072\u0061n\u0073\u0069\u0074\u0069\u006f\u006e\u0061\u006c":*_dad =2;};return nil ;
};func (_bb *ST_OnOff )Validate ()error {return _bb .ValidateWithPath ("")};func (_dd *ST_CryptProv )UnmarshalXML (d *_b .Decoder ,start _b .StartElement )error {_gb ,_gcd :=d .Token ();if _gcd !=nil {return _gcd ;};if _ea ,_ae :=_gb .(_b .EndElement );
_ae &&_ea .Name ==start .Name {*_dd =1;return nil ;};if _cea ,_eb :=_gb .(_b .CharData );!_eb {return _e .Errorf ("\u0065\u0078\u0070\u0065\u0063\u0074\u0065\u0064\u0020\u0063\u0068a\u0072\u0020\u0064\u0061\u0074\u0061\u002c\u0020\u0067\u006ft\u0020\u0025\u0054",_gb );
}else {switch string (_cea ){case "":*_dd =0;case "\u0072\u0073\u0061\u0041\u0045\u0053":*_dd =1;case "\u0072s\u0061\u0046\u0075\u006c\u006c":*_dd =2;case "\u0063\u0075\u0073\u0074\u006f\u006d":*_dd =3;};};_gb ,_gcd =d .Token ();if _gcd !=nil {return _gcd ;
};if _dgb ,_cbb :=_gb .(_b .EndElement );_cbb &&_dgb .Name ==start .Name {return nil ;};return _e .Errorf ("\u0065\u0078\u0070\u0065c\u0074\u0065\u0064\u0020\u0065\u006e\u0064\u0020\u0065\u006ce\u006de\u006e\u0074\u002c\u0020\u0067\u006f\u0074 \u0025\u0076",_gb );
};const (ST_TrueFalseUnset ST_TrueFalse =0;ST_TrueFalseT ST_TrueFalse =1;ST_TrueFalseF ST_TrueFalse =2;ST_TrueFalseTrue ST_TrueFalse =3;ST_TrueFalseFalse ST_TrueFalse =4;);func (_fgdc ST_XAlign )String ()string {switch _fgdc {case 0:return "";case 1:return "\u006c\u0065\u0066\u0074";
case 2:return "\u0063\u0065\u006e\u0074\u0065\u0072";case 3:return "\u0072\u0069\u0067h\u0074";case 4:return "\u0069\u006e\u0073\u0069\u0064\u0065";case 5:return "\u006fu\u0074\u0073\u0069\u0064\u0065";};return "";};func (_dbc ST_VerticalAlignRun )Validate ()error {return _dbc .ValidateWithPath ("")};
func (_dfe ST_TrueFalseBlank )Validate ()error {return _dfe .ValidateWithPath ("")};const ST_FixedPercentagePattern ="-\u003f\u0028\u0028\u0031\u0030\u0030\u0029\u007c\u0028\u005b\u0030\u002d\u0039\u005d\u005b\u0030\u002d\u0039]\u003f\u0029\u0029\u0028\u005c\u002e\u005b\u0030\u002d\u0039][\u0030\u002d\u0039]\u003f)\u003f\u0025";
const (ST_XAlignUnset ST_XAlign =0;ST_XAlignLeft ST_XAlign =1;ST_XAlignCenter ST_XAlign =2;ST_XAlignRight ST_XAlign =3;ST_XAlignInside ST_XAlign =4;ST_XAlignOutside ST_XAlign =5;);func ParseStdlibTime (s string )(_a .Time ,error ){var _bfe _a .Time ;if _de ,_def :=_a .Parse (_ef ,s );
_def ==nil {return _de ,nil ;};if _ca ,_cb :=_a .Parse (_ag ,s );_cb ==nil {return _ca ,nil ;};if _fe ,_bgg :=_a .Parse (_fgd ,s );_bgg ==nil {return _fe ,nil ;};return _bfe ,_e .Errorf ("\u0070\u0061\u0072si\u006e\u0067\u0020\u0025\u0073\u0020\u0061\u0073\u0020\u0064\u0061\u0074\u0065\u002f\u0074\u0069\u006d\u0065",s );
};