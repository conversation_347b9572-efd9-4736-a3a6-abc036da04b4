//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package math ;import (_g "encoding/xml";_a "fmt";_ad "github.com/unidoc/unioffice/v2";_b "github.com/unidoc/unioffice/v2/common/logger";_ab "github.com/unidoc/unioffice/v2/schema/soo/ofc/sharedTypes";_f "strconv";);type CT_LimLoc struct{

// Value
ValAttr ST_LimLoc ;};func (_bcce *CT_FType )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {_bcce .ValAttr =ST_FType (1);for _ ,_fdcb :=range start .Attr {if _fdcb .Name .Local =="\u0076\u0061\u006c"{_bcce .ValAttr .UnmarshalXMLAttr (_fdcb );
continue ;};};for {_beec ,_age :=d .Token ();if _age !=nil {return _a .Errorf ("p\u0061r\u0073\u0069\u006e\u0067\u0020\u0043\u0054\u005fF\u0054\u0079\u0070\u0065: \u0025\u0073",_age );};if _dcg ,_ccab :=_beec .(_g .EndElement );_ccab &&_dcg .Name ==start .Name {break ;
};};return nil ;};func (_ecba ST_LimLoc )MarshalXMLAttr (name _g .Name )(_g .Attr ,error ){_ffdbfb :=_g .Attr {};_ffdbfb .Name =name ;switch _ecba {case ST_LimLocUnset :_ffdbfb .Value ="";case ST_LimLocUndOvr :_ffdbfb .Value ="\u0075\u006e\u0064\u004f\u0076\u0072";
case ST_LimLocSubSup :_ffdbfb .Value ="\u0073\u0075\u0062\u0053\u0075\u0070";};return _ffdbfb ,nil ;};func (_bgcd *CT_EqArr )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {e .EncodeToken (start );if _bgcd .EqArrPr !=nil {_dba :=_g .StartElement {Name :_g .Name {Local :"\u006d:\u0065\u0071\u0041\u0072\u0072\u0050r"}};
e .EncodeElement (_bgcd .EqArrPr ,_dba );};_bee :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0065"}};for _ ,_gfcag :=range _bgcd .E {e .EncodeElement (_gfcag ,_bee );};e .EncodeToken (_g .EndElement {Name :start .Name });return nil ;};func (_feabgf *CT_TwipsMeasure )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {for _ ,_cfgg :=range start .Attr {if _cfgg .Name .Local =="\u0076\u0061\u006c"{_cggdf ,_badb :=ParseUnionST_TwipsMeasure (_cfgg .Value );
if _badb !=nil {return _badb ;};_feabgf .ValAttr =_cggdf ;continue ;};};for {_bagg ,_baba :=d .Token ();if _baba !=nil {return _a .Errorf ("p\u0061\u0072\u0073\u0069\u006e\u0067 \u0043\u0054\u005f\u0054\u0077\u0069\u0070\u0073\u004de\u0061\u0073\u0075r\u0065:\u0020\u0025\u0073",_baba );
};if _cfee ,_ggga :=_bagg .(_g .EndElement );_ggga &&_cfee .Name ==start .Name {break ;};};return nil ;};func (_dbc *CT_Char )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {start .Attr =append (start .Attr ,_g .Attr {Name :_g .Name {Local :"\u006d\u003a\u0076a\u006c"},Value :_a .Sprintf ("\u0025\u0076",_dbc .ValAttr )});
e .EncodeToken (start );e .EncodeToken (_g .EndElement {Name :start .Name });return nil ;};func (_de *CT_Acc )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {_de .E =NewCT_OMathArg ();_fe :for {_ag ,_gf :=d .Token ();if _gf !=nil {return _gf ;
};switch _abf :=_ag .(type ){case _g .StartElement :switch _abf .Name {case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0061\u0063\u0063P\u0072"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0061\u0063\u0063P\u0072"}:_de .AccPr =NewCT_AccPr ();
if _agd :=d .DecodeElement (_de .AccPr ,&_abf );_agd !=nil {return _agd ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0065"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0065"}:if _gc :=d .DecodeElement (_de .E ,&_abf );
_gc !=nil {return _gc ;};default:_b .Log .Debug ("\u0073\u006b\u0069\u0070\u0070i\u006e\u0067\u0020\u0075\u006e\u0073\u0075\u0070\u0070\u006f\u0072\u0074\u0065d\u0020\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0043\u0054\u005f\u0041\u0063\u0063\u0020\u0025\u0076",_abf .Name );
if _c :=d .Skip ();_c !=nil {return _c ;};};case _g .EndElement :break _fe ;case _g .CharData :};};return nil ;};

// ValidateWithPath validates the CT_Func and its children, prefixing error messages with path
func (_baf *CT_Func )ValidateWithPath (path string )error {if _baf .FuncPr !=nil {if _ade :=_baf .FuncPr .ValidateWithPath (path +"\u002fF\u0075\u006e\u0063\u0050\u0072");_ade !=nil {return _ade ;};};if _dgff :=_baf .FName .ValidateWithPath (path +"\u002f\u0046\u004e\u0061\u006d\u0065");
_dgff !=nil {return _dgff ;};if _bdcee :=_baf .E .ValidateWithPath (path +"\u002f\u0045");_bdcee !=nil {return _bdcee ;};return nil ;};func (_adc *CT_LimUppPr )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {_fbbb :for {_fba ,_gacb :=d .Token ();
if _gacb !=nil {return _gacb ;};switch _afdf :=_fba .(type ){case _g .StartElement :switch _afdf .Name {case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0063\u0074\u0072\u006c\u0050\u0072"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0063\u0074\u0072\u006c\u0050\u0072"}:_adc .CtrlPr =NewCT_CtrlPr ();
if _aacfe :=d .DecodeElement (_adc .CtrlPr ,&_afdf );_aacfe !=nil {return _aacfe ;};default:_b .Log .Debug ("\u0073\u006bi\u0070\u0070\u0069\u006e\u0067\u0020\u0075\u006e\u0073\u0075\u0070\u0070\u006f\u0072\u0074\u0065\u0064\u0020\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0043\u0054\u005f\u004c\u0069\u006d\u0055\u0070\u0070\u0050\u0072\u0020\u0025\u0076",_afdf .Name );
if _gcc :=d .Skip ();_gcc !=nil {return _gcc ;};};case _g .EndElement :break _fbbb ;case _g .CharData :};};return nil ;};type CT_MCPr struct{

// Matrix Column Count
Count *CT_Integer255 ;

// Matrix Column Justification
McJc *CT_XAlign ;};type CT_Box struct{

// Box Properties
BoxPr *CT_BoxPr ;

// Base
E *CT_OMathArg ;};func (_dcea *CT_MathPrChoice )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {if _dcea .WrapIndent !=nil {_fadc :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0077r\u0061\u0070\u0049\u006e\u0064\u0065\u006e\u0074"}};
e .EncodeElement (_dcea .WrapIndent ,_fadc );}else if _dcea .WrapRight !=nil {_afcbf :=_g .StartElement {Name :_g .Name {Local :"m\u003a\u0077\u0072\u0061\u0070\u0052\u0069\u0067\u0068\u0074"}};e .EncodeElement (_dcea .WrapRight ,_afcbf );};return nil ;
};

// Validate validates the CT_OnOff and its children
func (_fbda *CT_OnOff )Validate ()error {return _fbda .ValidateWithPath ("\u0043\u0054\u005f\u004f\u006e\u004f\u0066\u0066");};func (_cbggc *ST_BreakBinSub )UnmarshalXMLAttr (attr _g .Attr )error {switch attr .Value {case "":*_cbggc =0;case "\u002d\u002d":*_cbggc =1;
case "\u002d\u002b":*_cbggc =2;case "\u002b\u002d":*_cbggc =3;};return nil ;};func NewCT_Text ()*CT_Text {_cddf :=&CT_Text {};return _cddf };func (_cgff *CT_Phant )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {_cgff .E =NewCT_OMathArg ();
_bafb :for {_abaa ,_eced :=d .Token ();if _eced !=nil {return _eced ;};switch _dcaefg :=_abaa .(type ){case _g .StartElement :switch _dcaefg .Name {case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0070h\u0061\u006e\u0074\u0050\u0072"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0070h\u0061\u006e\u0074\u0050\u0072"}:_cgff .PhantPr =NewCT_PhantPr ();
if _aegg :=d .DecodeElement (_cgff .PhantPr ,&_dcaefg );_aegg !=nil {return _aegg ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0065"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0065"}:if _efcb :=d .DecodeElement (_cgff .E ,&_dcaefg );
_efcb !=nil {return _efcb ;};default:_b .Log .Debug ("\u0073\u006b\u0069\u0070\u0070\u0069\u006eg\u0020\u0075\u006es\u0075\u0070\u0070\u006fr\u0074\u0065\u0064\u0020\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0043\u0054\u005f\u0050\u0068\u0061\u006e\u0074\u0020\u0025\u0076",_dcaefg .Name );
if _gbf :=d .Skip ();_gbf !=nil {return _gbf ;};};case _g .EndElement :break _bafb ;case _g .CharData :};};return nil ;};func (_bggb ST_BreakBinSub )ValidateWithPath (path string )error {switch _bggb {case 0,1,2,3:default:return _a .Errorf ("\u0025s\u003a\u0020\u006f\u0075t\u0020\u006f\u0066\u0020\u0072a\u006eg\u0065 \u0076\u0061\u006c\u0075\u0065\u0020\u0025d",path ,int (_bggb ));
};return nil ;};

// Validate validates the CT_OMath and its children
func (_cfge *CT_OMath )Validate ()error {return _cfge .ValidateWithPath ("\u0043\u0054\u005f\u004f\u004d\u0061\u0074\u0068");};func NewCT_MR ()*CT_MR {_gece :=&CT_MR {};return _gece };func (_dgeac ST_Jc )MarshalXMLAttr (name _g .Name )(_g .Attr ,error ){_gbbe :=_g .Attr {};
_gbbe .Name =name ;switch _dgeac {case ST_JcUnset :_gbbe .Value ="";case ST_JcLeft :_gbbe .Value ="\u006c\u0065\u0066\u0074";case ST_JcRight :_gbbe .Value ="\u0072\u0069\u0067h\u0074";case ST_JcCenter :_gbbe .Value ="\u0063\u0065\u006e\u0074\u0065\u0072";
case ST_JcCenterGroup :_gbbe .Value ="c\u0065\u006e\u0074\u0065\u0072\u0047\u0072\u006f\u0075\u0070";};return _gbbe ,nil ;};const (ST_LimLocUnset ST_LimLoc =0;ST_LimLocUndOvr ST_LimLoc =1;ST_LimLocSubSup ST_LimLoc =2;);type CT_DPr struct{

// Delimiter Beginning Character
BegChr *CT_Char ;

// Delimiter Separator Character
SepChr *CT_Char ;

// Delimiter Ending Character
EndChr *CT_Char ;

// Delimiter Grow
Grow *CT_OnOff ;

// Shape (Delimiters)
Shp *CT_Shp ;CtrlPr *CT_CtrlPr ;};func NewCT_NaryPr ()*CT_NaryPr {_fage :=&CT_NaryPr {};return _fage };func (_gffce *CT_SSub )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {_gffce .E =NewCT_OMathArg ();_gffce .Sub =NewCT_OMathArg ();_bffda :for {_afec ,_dfce :=d .Token ();
if _dfce !=nil {return _dfce ;};switch _geceg :=_afec .(type ){case _g .StartElement :switch _geceg .Name {case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0073\u0053\u0075\u0062\u0050\u0072"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0073\u0053\u0075\u0062\u0050\u0072"}:_gffce .SSubPr =NewCT_SSubPr ();
if _fcga :=d .DecodeElement (_gffce .SSubPr ,&_geceg );_fcga !=nil {return _fcga ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0065"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0065"}:if _aefa :=d .DecodeElement (_gffce .E ,&_geceg );
_aefa !=nil {return _aefa ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0073\u0075\u0062"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0073\u0075\u0062"}:if _aade :=d .DecodeElement (_gffce .Sub ,&_geceg );
_aade !=nil {return _aade ;};default:_b .Log .Debug ("\u0073\u006b\u0069p\u0070\u0069\u006e\u0067\u0020\u0075\u006e\u0073\u0075\u0070\u0070\u006f\u0072\u0074\u0065\u0064\u0020\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0043T\u005f\u0053\u0053\u0075\u0062\u0020\u0025\u0076",_geceg .Name );
if _bcac :=d .Skip ();_bcac !=nil {return _bcac ;};};case _g .EndElement :break _bffda ;case _g .CharData :};};return nil ;};func (_aebg *CT_Integer255 )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {start .Attr =append (start .Attr ,_g .Attr {Name :_g .Name {Local :"\u006d\u003a\u0076a\u006c"},Value :_a .Sprintf ("\u0025\u0076",_aebg .ValAttr )});
e .EncodeToken (start );e .EncodeToken (_g .EndElement {Name :start .Name });return nil ;};func (_daeag *CT_LimLow )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {e .EncodeToken (start );if _daeag .LimLowPr !=nil {_ebba :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u006c\u0069\u006d\u004c\u006f\u0077\u0050\u0072"}};
e .EncodeElement (_daeag .LimLowPr ,_ebba );};_cec :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0065"}};e .EncodeElement (_daeag .E ,_cec );_aace :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u006ci\u006d"}};e .EncodeElement (_daeag .Lim ,_aace );
e .EncodeToken (_g .EndElement {Name :start .Name });return nil ;};func NewCT_TwipsMeasure ()*CT_TwipsMeasure {_baad :=&CT_TwipsMeasure {};return _baad };

// ValidateWithPath validates the CT_OMathPara and its children, prefixing error messages with path
func (_abcc *CT_OMathPara )ValidateWithPath (path string )error {if _abcc .OMathParaPr !=nil {if _dfcf :=_abcc .OMathParaPr .ValidateWithPath (path +"\u002f\u004f\u004da\u0074\u0068\u0050\u0061\u0072\u0061\u0050\u0072");_dfcf !=nil {return _dfcf ;};};for _dcce ,_afaa :=range _abcc .OMath {if _gacbd :=_afaa .ValidateWithPath (_a .Sprintf ("\u0025\u0073\u002fO\u004d\u0061\u0074\u0068\u005b\u0025\u0064\u005d",path ,_dcce ));
_gacbd !=nil {return _gacbd ;};};return nil ;};type ST_Style byte ;

// Validate validates the CT_TopBot and its children
func (_daf *CT_TopBot )Validate ()error {return _daf .ValidateWithPath ("\u0043T\u005f\u0054\u006f\u0070\u0042\u006ft");};func (_gcbg ST_BreakBin )String ()string {switch _gcbg {case 0:return "";case 1:return "\u0062\u0065\u0066\u006f\u0072\u0065";case 2:return "\u0061\u0066\u0074e\u0072";
case 3:return "\u0072\u0065\u0070\u0065\u0061\u0074";};return "";};func NewCT_MPr ()*CT_MPr {_fdbf :=&CT_MPr {};return _fdbf };func (_aced ST_Style )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {return e .EncodeElement (_aced .String (),start );
};func (_gcbeb ST_Shp )Validate ()error {return _gcbeb .ValidateWithPath ("")};func (_bbcd *CT_YAlign )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {_eage ,_aadbd :=_bbcd .ValAttr .MarshalXMLAttr (_g .Name {Local :"\u006d\u003a\u0076a\u006c"});
if _aadbd !=nil {return _aadbd ;};start .Attr =append (start .Attr ,_eage );e .EncodeToken (start );e .EncodeToken (_g .EndElement {Name :start .Name });return nil ;};

// Validate validates the CT_GroupChr and its children
func (_fgegf *CT_GroupChr )Validate ()error {return _fgegf .ValidateWithPath ("C\u0054\u005f\u0047\u0072\u006f\u0075\u0070\u0043\u0068\u0072");};func (_fggc ST_FType )Validate ()error {return _fggc .ValidateWithPath ("")};func NewCT_BarPr ()*CT_BarPr {_bg :=&CT_BarPr {};
return _bg };

// ValidateWithPath validates the CT_GroupChrPr and its children, prefixing error messages with path
func (_gdcg *CT_GroupChrPr )ValidateWithPath (path string )error {if _gdcg .Chr !=nil {if _dfef :=_gdcg .Chr .ValidateWithPath (path +"\u002f\u0043\u0068\u0072");_dfef !=nil {return _dfef ;};};if _gdcg .Pos !=nil {if _ace :=_gdcg .Pos .ValidateWithPath (path +"\u002f\u0050\u006f\u0073");
_ace !=nil {return _ace ;};};if _gdcg .VertJc !=nil {if _ffa :=_gdcg .VertJc .ValidateWithPath (path +"\u002fV\u0065\u0072\u0074\u004a\u0063");_ffa !=nil {return _ffa ;};};if _gdcg .CtrlPr !=nil {if _adac :=_gdcg .CtrlPr .ValidateWithPath (path +"\u002fC\u0074\u0072\u006c\u0050\u0072");
_adac !=nil {return _adac ;};};return nil ;};

// ValidateWithPath validates the CT_LimUpp and its children, prefixing error messages with path
func (_fffe *CT_LimUpp )ValidateWithPath (path string )error {if _fffe .LimUppPr !=nil {if _gcgg :=_fffe .LimUppPr .ValidateWithPath (path +"\u002fL\u0069\u006d\u0055\u0070\u0070\u0050r");_gcgg !=nil {return _gcgg ;};};if _ffag :=_fffe .E .ValidateWithPath (path +"\u002f\u0045");
_ffag !=nil {return _ffag ;};if _cafc :=_fffe .Lim .ValidateWithPath (path +"\u002f\u004c\u0069\u006d");_cafc !=nil {return _cafc ;};return nil ;};func (_ggcd *MathPr )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {_ggcd .CT_MathPr =*NewCT_MathPr ();
_cfff :for {_bccc ,_ceef :=d .Token ();if _ceef !=nil {return _ceef ;};switch _ddad :=_bccc .(type ){case _g .StartElement :switch _ddad .Name {case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u006d\u0061\u0074\u0068\u0046\u006f\u006e\u0074"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u006d\u0061\u0074\u0068\u0046\u006f\u006e\u0074"}:_ggcd .MathFont =NewCT_String ();
if _fegd :=d .DecodeElement (_ggcd .MathFont ,&_ddad );_fegd !=nil {return _fegd ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0062\u0072\u006b\u0042\u0069\u006e"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0062\u0072\u006b\u0042\u0069\u006e"}:_ggcd .BrkBin =NewCT_BreakBin ();
if _bgba :=d .DecodeElement (_ggcd .BrkBin ,&_ddad );_bgba !=nil {return _bgba ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0062r\u006b\u0042\u0069\u006e\u0053\u0075b"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0062r\u006b\u0042\u0069\u006e\u0053\u0075b"}:_ggcd .BrkBinSub =NewCT_BreakBinSub ();
if _bdeae :=d .DecodeElement (_ggcd .BrkBinSub ,&_ddad );_bdeae !=nil {return _bdeae ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0073m\u0061\u006c\u006c\u0046\u0072\u0061c"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0073m\u0061\u006c\u006c\u0046\u0072\u0061c"}:_ggcd .SmallFrac =NewCT_OnOff ();
if _abcddg :=d .DecodeElement (_ggcd .SmallFrac ,&_ddad );_abcddg !=nil {return _abcddg ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0064i\u0073\u0070\u0044\u0065\u0066"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0064i\u0073\u0070\u0044\u0065\u0066"}:_ggcd .DispDef =NewCT_OnOff ();
if _agaba :=d .DecodeElement (_ggcd .DispDef ,&_ddad );_agaba !=nil {return _agaba ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u006cM\u0061\u0072\u0067\u0069\u006e"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u006cM\u0061\u0072\u0067\u0069\u006e"}:_ggcd .LMargin =NewCT_TwipsMeasure ();
if _cdag :=d .DecodeElement (_ggcd .LMargin ,&_ddad );_cdag !=nil {return _cdag ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0072M\u0061\u0072\u0067\u0069\u006e"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0072M\u0061\u0072\u0067\u0069\u006e"}:_ggcd .RMargin =NewCT_TwipsMeasure ();
if _aabc :=d .DecodeElement (_ggcd .RMargin ,&_ddad );_aabc !=nil {return _aabc ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0064\u0065\u0066J\u0063"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0064\u0065\u0066J\u0063"}:_ggcd .DefJc =NewCT_OMathJc ();
if _cagb :=d .DecodeElement (_ggcd .DefJc ,&_ddad );_cagb !=nil {return _cagb ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0070\u0072\u0065S\u0070"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0070\u0072\u0065S\u0070"}:_ggcd .PreSp =NewCT_TwipsMeasure ();
if _gfdeg :=d .DecodeElement (_ggcd .PreSp ,&_ddad );_gfdeg !=nil {return _gfdeg ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0070\u006f\u0073\u0074\u0053\u0070"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0070\u006f\u0073\u0074\u0053\u0070"}:_ggcd .PostSp =NewCT_TwipsMeasure ();
if _fedcf :=d .DecodeElement (_ggcd .PostSp ,&_ddad );_fedcf !=nil {return _fedcf ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0069n\u0074\u0065\u0072\u0053\u0070"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0069n\u0074\u0065\u0072\u0053\u0070"}:_ggcd .InterSp =NewCT_TwipsMeasure ();
if _aafe :=d .DecodeElement (_ggcd .InterSp ,&_ddad );_aafe !=nil {return _aafe ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0069n\u0074\u0072\u0061\u0053\u0070"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0069n\u0074\u0072\u0061\u0053\u0070"}:_ggcd .IntraSp =NewCT_TwipsMeasure ();
if _fbbea :=d .DecodeElement (_ggcd .IntraSp ,&_ddad );_fbbea !=nil {return _fbbea ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0077\u0072\u0061\u0070\u0049\u006e\u0064\u0065\u006e\u0074"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0077\u0072\u0061\u0070\u0049\u006e\u0064\u0065\u006e\u0074"}:_ggcd .MathPrChoice =NewCT_MathPrChoice ();
if _aebf :=d .DecodeElement (&_ggcd .MathPrChoice .WrapIndent ,&_ddad );_aebf !=nil {return _aebf ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0077r\u0061\u0070\u0052\u0069\u0067\u0068t"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0077r\u0061\u0070\u0052\u0069\u0067\u0068t"}:_ggcd .MathPrChoice =NewCT_MathPrChoice ();
if _fcdddg :=d .DecodeElement (&_ggcd .MathPrChoice .WrapRight ,&_ddad );_fcdddg !=nil {return _fcdddg ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0069\u006e\u0074\u004c\u0069\u006d"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0069\u006e\u0074\u004c\u0069\u006d"}:_ggcd .IntLim =NewCT_LimLoc ();
if _aefac :=d .DecodeElement (_ggcd .IntLim ,&_ddad );_aefac !=nil {return _aefac ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u006ea\u0072\u0079\u004c\u0069\u006d"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u006ea\u0072\u0079\u004c\u0069\u006d"}:_ggcd .NaryLim =NewCT_LimLoc ();
if _bgbeg :=d .DecodeElement (_ggcd .NaryLim ,&_ddad );_bgbeg !=nil {return _bgbeg ;};default:_b .Log .Debug ("\u0073\u006b\u0069\u0070\u0070i\u006e\u0067\u0020\u0075\u006e\u0073\u0075\u0070\u0070\u006f\u0072\u0074\u0065d\u0020\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u004d\u0061\u0074\u0068\u0050\u0072\u0020\u0025\u0076",_ddad .Name );
if _cefb :=d .Skip ();_cefb !=nil {return _cefb ;};};case _g .EndElement :break _cfff ;case _g .CharData :};};return nil ;};

// ValidateWithPath validates the CT_FType and its children, prefixing error messages with path
func (_bfe *CT_FType )ValidateWithPath (path string )error {if _bfe .ValAttr ==ST_FTypeUnset {return _a .Errorf ("\u0025\u0073\u002fV\u0061\u006c\u0041\u0074t\u0072\u0020\u0069\u0073\u0020\u0061\u0020m\u0061\u006e\u0064\u0061\u0074\u006f\u0072\u0079\u0020\u0066\u0069\u0065\u006c\u0064",path );
};if _eda :=_bfe .ValAttr .ValidateWithPath (path +"\u002f\u0056\u0061\u006c\u0041\u0074\u0074\u0072");_eda !=nil {return _eda ;};return nil ;};func NewCT_M ()*CT_M {_aea :=&CT_M {};return _aea };type CT_SSup struct{

// Superscript Properties
SSupPr *CT_SSupPr ;

// Base
E *CT_OMathArg ;

// Superscript (Superscript object)
Sup *CT_OMathArg ;};

// Validate validates the CT_R and its children
func (_fgcfd *CT_R )Validate ()error {return _fgcfd .ValidateWithPath ("\u0043\u0054\u005f\u0052")};func (_ggced *EG_OMathElementsChoice )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {e .EncodeToken (start );_ggced .OMathMathElementsChoice .MarshalXML (e ,_g .StartElement {});
e .EncodeToken (_g .EndElement {Name :start .Name });return nil ;};

// Validate validates the CT_ManualBreak and its children
func (_bbca *CT_ManualBreak )Validate ()error {return _bbca .ValidateWithPath ("\u0043\u0054\u005f\u004d\u0061\u006e\u0075\u0061\u006cB\u0072\u0065\u0061\u006b");};type CT_M struct{

// Matrix Properties
MPr *CT_MPr ;

// Matrix Row
Mr []*CT_MR ;};func NewCT_DPr ()*CT_DPr {_baaa :=&CT_DPr {};return _baaa };

// Validate validates the CT_SSubSup and its children
func (_ecaf *CT_SSubSup )Validate ()error {return _ecaf .ValidateWithPath ("\u0043\u0054\u005f\u0053\u0053\u0075\u0062\u0053\u0075\u0070");};func NewCT_OMath ()*CT_OMath {_fgdc :=&CT_OMath {};return _fgdc };func (_geb *CT_Func )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {_geb .FName =NewCT_OMathArg ();
_geb .E =NewCT_OMathArg ();_cgee :for {_daea ,_gba :=d .Token ();if _gba !=nil {return _gba ;};switch _cdbc :=_daea .(type ){case _g .StartElement :switch _cdbc .Name {case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0066\u0075\u006e\u0063\u0050\u0072"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0066\u0075\u006e\u0063\u0050\u0072"}:_geb .FuncPr =NewCT_FuncPr ();
if _adfa :=d .DecodeElement (_geb .FuncPr ,&_cdbc );_adfa !=nil {return _adfa ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0066\u004e\u0061m\u0065"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0066\u004e\u0061m\u0065"}:if _afgb :=d .DecodeElement (_geb .FName ,&_cdbc );
_afgb !=nil {return _afgb ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0065"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0065"}:if _cfag :=d .DecodeElement (_geb .E ,&_cdbc );
_cfag !=nil {return _cfag ;};default:_b .Log .Debug ("\u0073\u006b\u0069p\u0070\u0069\u006e\u0067\u0020\u0075\u006e\u0073\u0075\u0070\u0070\u006f\u0072\u0074\u0065\u0064\u0020\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0043T\u005f\u0046\u0075\u006e\u0063\u0020\u0025\u0076",_cdbc .Name );
if _dcbd :=d .Skip ();_dcbd !=nil {return _dcbd ;};};case _g .EndElement :break _cgee ;case _g .CharData :};};return nil ;};

// Validate validates the CT_BarPr and its children
func (_cg *CT_BarPr )Validate ()error {return _cg .ValidateWithPath ("\u0043\u0054\u005f\u0042\u0061\u0072\u0050\u0072");};

// Validate validates the CT_MCS and its children
func (_gccd *CT_MCS )Validate ()error {return _gccd .ValidateWithPath ("\u0043\u0054\u005f\u004d\u0043\u0053");};func (_fdeg *CT_TopBot )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {_bcgf ,_fagg :=_fdeg .ValAttr .MarshalXMLAttr (_g .Name {Local :"\u006d\u003a\u0076a\u006c"});
if _fagg !=nil {return _fagg ;};start .Attr =append (start .Attr ,_bcgf );e .EncodeToken (start );e .EncodeToken (_g .EndElement {Name :start .Name });return nil ;};

// Validate validates the CT_Char and its children
func (_cff *CT_Char )Validate ()error {return _cff .ValidateWithPath ("\u0043T\u005f\u0043\u0068\u0061\u0072");};func NewCT_LimLow ()*CT_LimLow {_bbe :=&CT_LimLow {};_bbe .E =NewCT_OMathArg ();_bbe .Lim =NewCT_OMathArg ();return _bbe ;};

// ValidateWithPath validates the CT_LimLow and its children, prefixing error messages with path
func (_cdf *CT_LimLow )ValidateWithPath (path string )error {if _cdf .LimLowPr !=nil {if _gffd :=_cdf .LimLowPr .ValidateWithPath (path +"\u002fL\u0069\u006d\u004c\u006f\u0077\u0050r");_gffd !=nil {return _gffd ;};};if _aed :=_cdf .E .ValidateWithPath (path +"\u002f\u0045");
_aed !=nil {return _aed ;};if _dfcb :=_cdf .Lim .ValidateWithPath (path +"\u002f\u004c\u0069\u006d");_dfcb !=nil {return _dfcb ;};return nil ;};func NewCT_D ()*CT_D {_bff :=&CT_D {};return _bff };func (_adad *CT_RChoice )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {_adef :=start ;
switch start .Name {case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0074"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0074"}:_adad .T =NewCT_Text ();
if _dbbe :=d .DecodeElement (_adad .T ,&_adef );_dbbe !=nil {return _dbbe ;};default:_b .Log .Debug ("\u0073k\u0069\u0070p\u0069\u006e\u0067 \u0075\u006e\u0073\u0075\u0070\u0070\u006fr\u0074\u0065\u0064\u0020\u0065\u006ce\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0043\u0054\u005fR\u0043\u0068\u006f\u0069\u0063\u0065\u0020\u0025\u0076",_adef .Name );
if _gdcd :=d .Skip ();_gdcd !=nil {return _gdcd ;};};return nil ;};

// Validate validates the CT_UnSignedInteger and its children
func (_aaca *CT_UnSignedInteger )Validate ()error {return _aaca .ValidateWithPath ("\u0043T\u005fU\u006e\u0053\u0069\u0067\u006ee\u0064\u0049n\u0074\u0065\u0067\u0065\u0072");};func NewCT_Nary ()*CT_Nary {_gcggf :=&CT_Nary {};_gcggf .Sub =NewCT_OMathArg ();
_gcggf .Sup =NewCT_OMathArg ();_gcggf .E =NewCT_OMathArg ();return _gcggf ;};type CT_BreakBinSub struct{

// Value
ValAttr ST_BreakBinSub ;};func (_cac *CT_M )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {e .EncodeToken (start );if _cac .MPr !=nil {_ecdf :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u006dP\u0072"}};e .EncodeElement (_cac .MPr ,_ecdf );
};_ccgb :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u006d\u0072"}};for _ ,_beb :=range _cac .Mr {e .EncodeElement (_beb ,_ccgb );};e .EncodeToken (_g .EndElement {Name :start .Name });return nil ;};func NewCT_LimUpp ()*CT_LimUpp {_cdgf :=&CT_LimUpp {};
_cdgf .E =NewCT_OMathArg ();_cdgf .Lim =NewCT_OMathArg ();return _cdgf ;};

// ValidateWithPath validates the CT_NaryPr and its children, prefixing error messages with path
func (_fgdfg *CT_NaryPr )ValidateWithPath (path string )error {if _fgdfg .Chr !=nil {if _fdg :=_fgdfg .Chr .ValidateWithPath (path +"\u002f\u0043\u0068\u0072");_fdg !=nil {return _fdg ;};};if _fgdfg .LimLoc !=nil {if _gdcf :=_fgdfg .LimLoc .ValidateWithPath (path +"\u002fL\u0069\u006d\u004c\u006f\u0063");
_gdcf !=nil {return _gdcf ;};};if _fgdfg .Grow !=nil {if _gbab :=_fgdfg .Grow .ValidateWithPath (path +"\u002f\u0047\u0072o\u0077");_gbab !=nil {return _gbab ;};};if _fgdfg .SubHide !=nil {if _bbab :=_fgdfg .SubHide .ValidateWithPath (path +"\u002f\u0053\u0075\u0062\u0048\u0069\u0064\u0065");
_bbab !=nil {return _bbab ;};};if _fgdfg .SupHide !=nil {if _acfdd :=_fgdfg .SupHide .ValidateWithPath (path +"\u002f\u0053\u0075\u0070\u0048\u0069\u0064\u0065");_acfdd !=nil {return _acfdd ;};};if _fgdfg .CtrlPr !=nil {if _egda :=_fgdfg .CtrlPr .ValidateWithPath (path +"\u002fC\u0074\u0072\u006c\u0050\u0072");
_egda !=nil {return _egda ;};};return nil ;};func (_cgcg *CT_ManualBreak )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {for _ ,_daag :=range start .Attr {if _daag .Name .Local =="\u0061\u006c\u006eA\u0074"{_bgfca ,_adec :=_f .ParseInt (_daag .Value ,10,64);
if _adec !=nil {return _adec ;};_cgcg .AlnAtAttr =&_bgfca ;continue ;};};for {_ececb ,_fced :=d .Token ();if _fced !=nil {return _a .Errorf ("\u0070\u0061\u0072\u0073\u0069\u006e\u0067\u0020\u0043\u0054\u005fM\u0061\u006e\u0075\u0061\u006c\u0042\u0072\u0065\u0061\u006b:\u0020\u0025\u0073",_fced );
};if _debg ,_fadb :=_ececb .(_g .EndElement );_fadb &&_debg .Name ==start .Name {break ;};};return nil ;};func (_ceec *CT_Rad )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {_ceec .Deg =NewCT_OMathArg ();_ceec .E =NewCT_OMathArg ();_eadb :for {_facf ,_eed :=d .Token ();
if _eed !=nil {return _eed ;};switch _bfec :=_facf .(type ){case _g .StartElement :switch _bfec .Name {case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0072\u0061\u0064P\u0072"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0072\u0061\u0064P\u0072"}:_ceec .RadPr =NewCT_RadPr ();
if _fecdc :=d .DecodeElement (_ceec .RadPr ,&_bfec );_fecdc !=nil {return _fecdc ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0064\u0065\u0067"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0064\u0065\u0067"}:if _aefg :=d .DecodeElement (_ceec .Deg ,&_bfec );
_aefg !=nil {return _aefg ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0065"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0065"}:if _egbcc :=d .DecodeElement (_ceec .E ,&_bfec );
_egbcc !=nil {return _egbcc ;};default:_b .Log .Debug ("\u0073\u006b\u0069\u0070\u0070i\u006e\u0067\u0020\u0075\u006e\u0073\u0075\u0070\u0070\u006f\u0072\u0074\u0065d\u0020\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0043\u0054\u005f\u0052\u0061\u0064\u0020\u0025\u0076",_bfec .Name );
if _bfee :=d .Skip ();_bfee !=nil {return _bfee ;};};case _g .EndElement :break _eadb ;case _g .CharData :};};return nil ;};func (_fbg *CT_BreakBin )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {for _ ,_ggca :=range start .Attr {if _ggca .Name .Local =="\u0076\u0061\u006c"{_fbg .ValAttr .UnmarshalXMLAttr (_ggca );
continue ;};};for {_dfc ,_ggd :=d .Token ();if _ggd !=nil {return _a .Errorf ("\u0070\u0061\u0072si\u006e\u0067\u0020\u0043\u0054\u005f\u0042\u0072\u0065\u0061\u006b\u0042\u0069\u006e\u003a\u0020\u0025\u0073",_ggd );};if _cbca ,_bgbe :=_dfc .(_g .EndElement );
_bgbe &&_cbca .Name ==start .Name {break ;};};return nil ;};

// Validate validates the CT_EqArr and its children
func (_dfbe *CT_EqArr )Validate ()error {return _dfbe .ValidateWithPath ("\u0043\u0054\u005f\u0045\u0071\u0041\u0072\u0072");};func (_cbdcf *EG_ScriptStyle )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {start .Name .Local ="\u006d\u003aE\u0047\u005f\u0053c\u0072\u0069\u0070\u0074\u0053\u0074\u0079\u006c\u0065";
if _cbdcf .Scr !=nil {_gbec :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0073c\u0072"}};e .EncodeElement (_cbdcf .Scr ,_gbec );};if _cbdcf .Sty !=nil {_gdaea :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0073t\u0079"}};e .EncodeElement (_cbdcf .Sty ,_gdaea );
};return nil ;};

// ValidateWithPath validates the CT_BarPr and its children, prefixing error messages with path
func (_gdf *CT_BarPr )ValidateWithPath (path string )error {if _gdf .Pos !=nil {if _cga :=_gdf .Pos .ValidateWithPath (path +"\u002f\u0050\u006f\u0073");_cga !=nil {return _cga ;};};if _gdf .CtrlPr !=nil {if _gfc :=_gdf .CtrlPr .ValidateWithPath (path +"\u002fC\u0074\u0072\u006c\u0050\u0072");
_gfc !=nil {return _gfc ;};};return nil ;};type ST_BreakBin byte ;

// ValidateWithPath validates the CT_XAlign and its children, prefixing error messages with path
func (_dabdg *CT_XAlign )ValidateWithPath (path string )error {if _dabdg .ValAttr ==_ab .ST_XAlignUnset {return _a .Errorf ("\u0025\u0073\u002fV\u0061\u006c\u0041\u0074t\u0072\u0020\u0069\u0073\u0020\u0061\u0020m\u0061\u006e\u0064\u0061\u0074\u006f\u0072\u0079\u0020\u0066\u0069\u0065\u006c\u0064",path );
};if _dafe :=_dabdg .ValAttr .ValidateWithPath (path +"\u002f\u0056\u0061\u006c\u0041\u0074\u0074\u0072");_dafe !=nil {return _dafe ;};return nil ;};const (ST_ShpUnset ST_Shp =0;ST_ShpCentered ST_Shp =1;ST_ShpMatch ST_Shp =2;);func (_ggggc *CT_SSubSup )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {e .EncodeToken (start );
if _ggggc .SSubSupPr !=nil {_cddae :=_g .StartElement {Name :_g .Name {Local :"m\u003a\u0073\u0053\u0075\u0062\u0053\u0075\u0070\u0050\u0072"}};e .EncodeElement (_ggggc .SSubSupPr ,_cddae );};_ebdfg :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0065"}};
e .EncodeElement (_ggggc .E ,_ebdfg );_ccgc :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0073u\u0062"}};e .EncodeElement (_ggggc .Sub ,_ccgc );_gdfbg :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0073u\u0070"}};e .EncodeElement (_ggggc .Sup ,_gdfbg );
e .EncodeToken (_g .EndElement {Name :start .Name });return nil ;};func (_eceg *CT_Text )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {if _eceg .SpaceAttr !=nil {start .Attr =append (start .Attr ,_g .Attr {Name :_g .Name {Local :"\u0078m\u006c\u003a\u0073\u0070\u0061\u0063e"},Value :_a .Sprintf ("\u0025\u0076",*_eceg .SpaceAttr )});
};e .EncodeElement (_eceg .Content ,start );e .EncodeToken (_g .EndElement {Name :start .Name });return nil ;};

// ValidateWithPath validates the CT_OMathJc and its children, prefixing error messages with path
func (_bcfd *CT_OMathJc )ValidateWithPath (path string )error {if _bbbc :=_bcfd .ValAttr .ValidateWithPath (path +"\u002f\u0056\u0061\u006c\u0041\u0074\u0074\u0072");_bbbc !=nil {return _bbbc ;};return nil ;};func NewCT_Integer255 ()*CT_Integer255 {_gga :=&CT_Integer255 {};
_gga .ValAttr =1;return _gga };

// ValidateWithPath validates the CT_MathPr and its children, prefixing error messages with path
func (_bag *CT_MathPr )ValidateWithPath (path string )error {if _bag .MathFont !=nil {if _bafd :=_bag .MathFont .ValidateWithPath (path +"\u002fM\u0061\u0074\u0068\u0046\u006f\u006et");_bafd !=nil {return _bafd ;};};if _bag .BrkBin !=nil {if _eab :=_bag .BrkBin .ValidateWithPath (path +"\u002fB\u0072\u006b\u0042\u0069\u006e");
_eab !=nil {return _eab ;};};if _bag .BrkBinSub !=nil {if _fgdf :=_bag .BrkBinSub .ValidateWithPath (path +"\u002f\u0042\u0072\u006b\u0042\u0069\u006e\u0053\u0075\u0062");_fgdf !=nil {return _fgdf ;};};if _bag .SmallFrac !=nil {if _acece :=_bag .SmallFrac .ValidateWithPath (path +"\u002f\u0053\u006d\u0061\u006c\u006c\u0046\u0072\u0061\u0063");
_acece !=nil {return _acece ;};};if _bag .DispDef !=nil {if _ccba :=_bag .DispDef .ValidateWithPath (path +"\u002f\u0044\u0069\u0073\u0070\u0044\u0065\u0066");_ccba !=nil {return _ccba ;};};if _bag .LMargin !=nil {if _ebcg :=_bag .LMargin .ValidateWithPath (path +"\u002f\u004c\u004d\u0061\u0072\u0067\u0069\u006e");
_ebcg !=nil {return _ebcg ;};};if _bag .RMargin !=nil {if _afdfc :=_bag .RMargin .ValidateWithPath (path +"\u002f\u0052\u004d\u0061\u0072\u0067\u0069\u006e");_afdfc !=nil {return _afdfc ;};};if _bag .DefJc !=nil {if _eeaa :=_bag .DefJc .ValidateWithPath (path +"\u002f\u0044\u0065\u0066\u004a\u0063");
_eeaa !=nil {return _eeaa ;};};if _bag .PreSp !=nil {if _aafa :=_bag .PreSp .ValidateWithPath (path +"\u002f\u0050\u0072\u0065\u0053\u0070");_aafa !=nil {return _aafa ;};};if _bag .PostSp !=nil {if _aeea :=_bag .PostSp .ValidateWithPath (path +"\u002fP\u006f\u0073\u0074\u0053\u0070");
_aeea !=nil {return _aeea ;};};if _bag .InterSp !=nil {if _gggc :=_bag .InterSp .ValidateWithPath (path +"\u002f\u0049\u006e\u0074\u0065\u0072\u0053\u0070");_gggc !=nil {return _gggc ;};};if _bag .IntraSp !=nil {if _ddg :=_bag .IntraSp .ValidateWithPath (path +"\u002f\u0049\u006e\u0074\u0072\u0061\u0053\u0070");
_ddg !=nil {return _ddg ;};};if _bag .MathPrChoice !=nil {if _dbcf :=_bag .MathPrChoice .ValidateWithPath (path +"\u002f\u004d\u0061\u0074\u0068\u0050\u0072\u0043\u0068\u006f\u0069\u0063\u0065");_dbcf !=nil {return _dbcf ;};};if _bag .IntLim !=nil {if _ggdf :=_bag .IntLim .ValidateWithPath (path +"\u002fI\u006e\u0074\u004c\u0069\u006d");
_ggdf !=nil {return _ggdf ;};};if _bag .NaryLim !=nil {if _cacf :=_bag .NaryLim .ValidateWithPath (path +"\u002f\u004e\u0061\u0072\u0079\u004c\u0069\u006d");_cacf !=nil {return _cacf ;};};return nil ;};func (_aaceec *CT_SSubSupPr )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {e .EncodeToken (start );
if _aaceec .AlnScr !=nil {_defc :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0061\u006c\u006e\u0053\u0063\u0072"}};e .EncodeElement (_aaceec .AlnScr ,_defc );};if _aaceec .CtrlPr !=nil {_abafe :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0063\u0074\u0072\u006c\u0050\u0072"}};
e .EncodeElement (_aaceec .CtrlPr ,_abafe );};e .EncodeToken (_g .EndElement {Name :start .Name });return nil ;};type CT_MathPrChoice struct{WrapIndent *CT_TwipsMeasure ;WrapRight *CT_OnOff ;};func (_bfb *CT_MCS )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {e .EncodeToken (start );
_bbde :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u006d\u0063"}};for _ ,_aacee :=range _bfb .Mc {e .EncodeElement (_aacee ,_bbde );};e .EncodeToken (_g .EndElement {Name :start .Name });return nil ;};func NewCT_SPrePr ()*CT_SPrePr {_cega :=&CT_SPrePr {};
return _cega };func (_bdaf *CT_MathPr )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {e .EncodeToken (start );if _bdaf .MathFont !=nil {_fbeee :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u006d\u0061\u0074\u0068\u0046\u006f\u006e\u0074"}};
e .EncodeElement (_bdaf .MathFont ,_fbeee );};if _bdaf .BrkBin !=nil {_aec :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0062\u0072\u006b\u0042\u0069\u006e"}};e .EncodeElement (_bdaf .BrkBin ,_aec );};if _bdaf .BrkBinSub !=nil {_gfgb :=_g .StartElement {Name :_g .Name {Local :"m\u003a\u0062\u0072\u006b\u0042\u0069\u006e\u0053\u0075\u0062"}};
e .EncodeElement (_bdaf .BrkBinSub ,_gfgb );};if _bdaf .SmallFrac !=nil {_cacd :=_g .StartElement {Name :_g .Name {Local :"m\u003a\u0073\u006d\u0061\u006c\u006c\u0046\u0072\u0061\u0063"}};e .EncodeElement (_bdaf .SmallFrac ,_cacd );};if _bdaf .DispDef !=nil {_ccfb :=_g .StartElement {Name :_g .Name {Local :"\u006d:\u0064\u0069\u0073\u0070\u0044\u0065f"}};
e .EncodeElement (_bdaf .DispDef ,_ccfb );};if _bdaf .LMargin !=nil {_faa :=_g .StartElement {Name :_g .Name {Local :"\u006d:\u006c\u004d\u0061\u0072\u0067\u0069n"}};e .EncodeElement (_bdaf .LMargin ,_faa );};if _bdaf .RMargin !=nil {_fffc :=_g .StartElement {Name :_g .Name {Local :"\u006d:\u0072\u004d\u0061\u0072\u0067\u0069n"}};
e .EncodeElement (_bdaf .RMargin ,_fffc );};if _bdaf .DefJc !=nil {_fddb :=_g .StartElement {Name :_g .Name {Local :"\u006d:\u0064\u0065\u0066\u004a\u0063"}};e .EncodeElement (_bdaf .DefJc ,_fddb );};if _bdaf .PreSp !=nil {_gdbf :=_g .StartElement {Name :_g .Name {Local :"\u006d:\u0070\u0072\u0065\u0053\u0070"}};
e .EncodeElement (_bdaf .PreSp ,_gdbf );};if _bdaf .PostSp !=nil {_dccb :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0070\u006f\u0073\u0074\u0053\u0070"}};e .EncodeElement (_bdaf .PostSp ,_dccb );};if _bdaf .InterSp !=nil {_fddd :=_g .StartElement {Name :_g .Name {Local :"\u006d:\u0069\u006e\u0074\u0065\u0072\u0053p"}};
e .EncodeElement (_bdaf .InterSp ,_fddd );};if _bdaf .IntraSp !=nil {_bbaf :=_g .StartElement {Name :_g .Name {Local :"\u006d:\u0069\u006e\u0074\u0072\u0061\u0053p"}};e .EncodeElement (_bdaf .IntraSp ,_bbaf );};if _bdaf .MathPrChoice !=nil {_bdaf .MathPrChoice .MarshalXML (e ,_g .StartElement {});
};if _bdaf .IntLim !=nil {_befc :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0069\u006e\u0074\u004c\u0069\u006d"}};e .EncodeElement (_bdaf .IntLim ,_befc );};if _bdaf .NaryLim !=nil {_egage :=_g .StartElement {Name :_g .Name {Local :"\u006d:\u006e\u0061\u0072\u0079\u004c\u0069m"}};
e .EncodeElement (_bdaf .NaryLim ,_egage );};e .EncodeToken (_g .EndElement {Name :start .Name });return nil ;};func NewCT_MathPrChoice ()*CT_MathPrChoice {_bcggb :=&CT_MathPrChoice {};return _bcggb };func NewCT_SPre ()*CT_SPre {_fcgc :=&CT_SPre {};_fcgc .Sub =NewCT_OMathArg ();
_fcgc .Sup =NewCT_OMathArg ();_fcgc .E =NewCT_OMathArg ();return _fcgc ;};func (_ffeb *CT_GroupChr )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {e .EncodeToken (start );if _ffeb .GroupChrPr !=nil {_fdd :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0067r\u006f\u0075\u0070\u0043\u0068\u0072\u0050\u0072"}};
e .EncodeElement (_ffeb .GroupChrPr ,_fdd );};_eag :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0065"}};e .EncodeElement (_ffeb .E ,_eag );e .EncodeToken (_g .EndElement {Name :start .Name });return nil ;};func NewCT_GroupChrPr ()*CT_GroupChrPr {_bcae :=&CT_GroupChrPr {};
return _bcae };func (_afae *CT_LimUppPr )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {e .EncodeToken (start );if _afae .CtrlPr !=nil {_dfa :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0063\u0074\u0072\u006c\u0050\u0072"}};e .EncodeElement (_afae .CtrlPr ,_dfa );
};e .EncodeToken (_g .EndElement {Name :start .Name });return nil ;};func (_ffd *CT_BorderBoxPr )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {e .EncodeToken (start );if _ffd .HideTop !=nil {_baec :=_g .StartElement {Name :_g .Name {Local :"\u006d:\u0068\u0069\u0064\u0065\u0054\u006fp"}};
e .EncodeElement (_ffd .HideTop ,_baec );};if _ffd .HideBot !=nil {_fd :=_g .StartElement {Name :_g .Name {Local :"\u006d:\u0068\u0069\u0064\u0065\u0042\u006ft"}};e .EncodeElement (_ffd .HideBot ,_fd );};if _ffd .HideLeft !=nil {_bba :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0068\u0069\u0064\u0065\u004c\u0065\u0066\u0074"}};
e .EncodeElement (_ffd .HideLeft ,_bba );};if _ffd .HideRight !=nil {_ed :=_g .StartElement {Name :_g .Name {Local :"m\u003a\u0068\u0069\u0064\u0065\u0052\u0069\u0067\u0068\u0074"}};e .EncodeElement (_ffd .HideRight ,_ed );};if _ffd .StrikeH !=nil {_dbf :=_g .StartElement {Name :_g .Name {Local :"\u006d:\u0073\u0074\u0072\u0069\u006b\u0065H"}};
e .EncodeElement (_ffd .StrikeH ,_dbf );};if _ffd .StrikeV !=nil {_bc :=_g .StartElement {Name :_g .Name {Local :"\u006d:\u0073\u0074\u0072\u0069\u006b\u0065V"}};e .EncodeElement (_ffd .StrikeV ,_bc );};if _ffd .StrikeBLTR !=nil {_cgg :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0073t\u0072\u0069\u006b\u0065\u0042\u004c\u0054\u0052"}};
e .EncodeElement (_ffd .StrikeBLTR ,_cgg );};if _ffd .StrikeTLBR !=nil {_fdb :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0073t\u0072\u0069\u006b\u0065\u0054\u004c\u0042\u0052"}};e .EncodeElement (_ffd .StrikeTLBR ,_fdb );};if _ffd .CtrlPr !=nil {_fbf :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0063\u0074\u0072\u006c\u0050\u0072"}};
e .EncodeElement (_ffd .CtrlPr ,_fbf );};e .EncodeToken (_g .EndElement {Name :start .Name });return nil ;};func (_daad *CT_SpacingRule )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {start .Attr =append (start .Attr ,_g .Attr {Name :_g .Name {Local :"\u006d\u003a\u0076a\u006c"},Value :_a .Sprintf ("\u0025\u0076",_daad .ValAttr )});
e .EncodeToken (start );e .EncodeToken (_g .EndElement {Name :start .Name });return nil ;};func (_edg *CT_LimLowPr )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {e .EncodeToken (start );if _edg .CtrlPr !=nil {_cceg :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0063\u0074\u0072\u006c\u0050\u0072"}};
e .EncodeElement (_edg .CtrlPr ,_cceg );};e .EncodeToken (_g .EndElement {Name :start .Name });return nil ;};func NewCT_ManualBreak ()*CT_ManualBreak {_abga :=&CT_ManualBreak {};return _abga };

// Validate validates the CT_LimUppPr and its children
func (_bdac *CT_LimUppPr )Validate ()error {return _bdac .ValidateWithPath ("C\u0054\u005f\u004c\u0069\u006d\u0055\u0070\u0070\u0050\u0072");};func NewCT_OMathParaPr ()*CT_OMathParaPr {_dab :=&CT_OMathParaPr {};return _dab };func NewCT_BorderBoxPr ()*CT_BorderBoxPr {_gbe :=&CT_BorderBoxPr {};
return _gbe };func NewCT_YAlign ()*CT_YAlign {_ccafa :=&CT_YAlign {};_ccafa .ValAttr =_ab .ST_YAlign (1);return _ccafa ;};

// ValidateWithPath validates the CT_MathPrChoice and its children, prefixing error messages with path
func (_fccc *CT_MathPrChoice )ValidateWithPath (path string )error {if _fccc .WrapIndent !=nil {if _adfd :=_fccc .WrapIndent .ValidateWithPath (path +"/\u0057\u0072\u0061\u0070\u0049\u006e\u0064\u0065\u006e\u0074");_adfd !=nil {return _adfd ;};};if _fccc .WrapRight !=nil {if _afce :=_fccc .WrapRight .ValidateWithPath (path +"\u002f\u0057\u0072\u0061\u0070\u0052\u0069\u0067\u0068\u0074");
_afce !=nil {return _afce ;};};return nil ;};

// ValidateWithPath validates the CT_OMathArg and its children, prefixing error messages with path
func (_bcggbc *CT_OMathArg )ValidateWithPath (path string )error {if _bcggbc .ArgPr !=nil {if _dceb :=_bcggbc .ArgPr .ValidateWithPath (path +"\u002f\u0041\u0072\u0067\u0050\u0072");_dceb !=nil {return _dceb ;};};for _ddbe ,_acca :=range _bcggbc .EG_OMathElements {if _bdccd :=_acca .ValidateWithPath (_a .Sprintf ("\u0025\u0073\u002fEG\u005f\u004f\u004d\u0061\u0074\u0068\u0045\u006c\u0065\u006d\u0065\u006e\u0074\u0073\u005b\u0025\u0064\u005d",path ,_ddbe ));
_bdccd !=nil {return _bdccd ;};};if _bcggbc .CtrlPr !=nil {if _bggc :=_bcggbc .CtrlPr .ValidateWithPath (path +"\u002fC\u0074\u0072\u006c\u0050\u0072");_bggc !=nil {return _bggc ;};};return nil ;};

// ValidateWithPath validates the CT_TopBot and its children, prefixing error messages with path
func (_deeb *CT_TopBot )ValidateWithPath (path string )error {if _deeb .ValAttr ==ST_TopBotUnset {return _a .Errorf ("\u0025\u0073\u002fV\u0061\u006c\u0041\u0074t\u0072\u0020\u0069\u0073\u0020\u0061\u0020m\u0061\u006e\u0064\u0061\u0074\u006f\u0072\u0079\u0020\u0066\u0069\u0065\u006c\u0064",path );
};if _bdga :=_deeb .ValAttr .ValidateWithPath (path +"\u002f\u0056\u0061\u006c\u0041\u0074\u0074\u0072");_bdga !=nil {return _bdga ;};return nil ;};func (_dgba ST_Script )MarshalXMLAttr (name _g .Name )(_g .Attr ,error ){_cdeg :=_g .Attr {};_cdeg .Name =name ;
switch _dgba {case ST_ScriptUnset :_cdeg .Value ="";case ST_ScriptRoman :_cdeg .Value ="\u0072\u006f\u006da\u006e";case ST_ScriptScript :_cdeg .Value ="\u0073\u0063\u0072\u0069\u0070\u0074";case ST_ScriptFraktur :_cdeg .Value ="\u0066r\u0061\u006b\u0074\u0075\u0072";
case ST_ScriptDouble_struck :_cdeg .Value ="\u0064\u006f\u0075\u0062\u006c\u0065\u002d\u0073\u0074\u0072\u0075\u0063\u006b";case ST_ScriptSans_serif :_cdeg .Value ="\u0073\u0061\u006e\u0073\u002d\u0073\u0065\u0072\u0069\u0066";case ST_ScriptMonospace :_cdeg .Value ="\u006do\u006e\u006f\u0073\u0070\u0061\u0063e";
};return _cdeg ,nil ;};func (_ccegb ST_Jc )Validate ()error {return _ccegb .ValidateWithPath ("")};type ST_BreakBinSub byte ;

// Validate validates the CT_OMathParaPr and its children
func (_gfag *CT_OMathParaPr )Validate ()error {return _gfag .ValidateWithPath ("\u0043\u0054\u005f\u004f\u004d\u0061\u0074\u0068\u0050a\u0072\u0061\u0050\u0072");};func NewCT_OMathArgPr ()*CT_OMathArgPr {_ebdfa :=&CT_OMathArgPr {};return _ebdfa };

// Validate validates the CT_BreakBinSub and its children
func (_dbg *CT_BreakBinSub )Validate ()error {return _dbg .ValidateWithPath ("\u0043\u0054\u005f\u0042\u0072\u0065\u0061\u006b\u0042i\u006e\u0053\u0075\u0062");};func (_adee *CT_NaryPr )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {e .EncodeToken (start );
if _adee .Chr !=nil {_bffd :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0063h\u0072"}};e .EncodeElement (_adee .Chr ,_bffd );};if _adee .LimLoc !=nil {_cbcd :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u006c\u0069\u006d\u004c\u006f\u0063"}};
e .EncodeElement (_adee .LimLoc ,_cbcd );};if _adee .Grow !=nil {_fdef :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0067\u0072\u006f\u0077"}};e .EncodeElement (_adee .Grow ,_fdef );};if _adee .SubHide !=nil {_ggb :=_g .StartElement {Name :_g .Name {Local :"\u006d:\u0073\u0075\u0062\u0048\u0069\u0064e"}};
e .EncodeElement (_adee .SubHide ,_ggb );};if _adee .SupHide !=nil {_aeed :=_g .StartElement {Name :_g .Name {Local :"\u006d:\u0073\u0075\u0070\u0048\u0069\u0064e"}};e .EncodeElement (_adee .SupHide ,_aeed );};if _adee .CtrlPr !=nil {_befa :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0063\u0074\u0072\u006c\u0050\u0072"}};
e .EncodeElement (_adee .CtrlPr ,_befa );};e .EncodeToken (_g .EndElement {Name :start .Name });return nil ;};type CT_EqArr struct{

// Array Properties
EqArrPr *CT_EqArrPr ;

// Element
E []*CT_OMathArg ;};const (ST_TopBotUnset ST_TopBot =0;ST_TopBotTop ST_TopBot =1;ST_TopBotBot ST_TopBot =2;);

// ValidateWithPath validates the CT_OMathParaPr and its children, prefixing error messages with path
func (_ggbee *CT_OMathParaPr )ValidateWithPath (path string )error {if _ggbee .Jc !=nil {if _agab :=_ggbee .Jc .ValidateWithPath (path +"\u002f\u004a\u0063");_agab !=nil {return _agab ;};};return nil ;};func (_ddecg *MathPr )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {start .Attr =append (start .Attr ,_g .Attr {Name :_g .Name {Local :"\u0078\u006d\u006cn\u0073"},Value :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068"});
start .Attr =append (start .Attr ,_g .Attr {Name :_g .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u006d"},Value :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068"});
start .Attr =append (start .Attr ,_g .Attr {Name :_g .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0073"},Value :"\u0068\u0074\u0074\u0070\u003a/\u002f\u0073\u0063\u0068\u0065m\u0061s\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067/\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0068\u0061\u0072e\u0064\u0054\u0079\u0070\u0065\u0073"});
start .Attr =append (start .Attr ,_g .Attr {Name :_g .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0077"},Value :"ht\u0074\u0070:\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073.\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0077\u006f\u0072\u0064\u0070\u0072\u006f\u0063\u0065s\u0073i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u00306\u002fm\u0061\u0069n"});
start .Attr =append (start .Attr ,_g .Attr {Name :_g .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0078\u006dl"},Value :"\u0068\u0074tp\u003a\u002f\u002fw\u0077\u0077\u002e\u00773.o\u0072g/\u0058\u004d\u004c\u002f\u0031\u0039\u00398/\u006e\u0061\u006d\u0065\u0073\u0070\u0061c\u0065"});
start .Name .Local ="\u006d\u003a\u006d\u0061\u0074\u0068\u0050\u0072";return _ddecg .CT_MathPr .MarshalXML (e ,start );};func (_dgbg *CT_GroupChr )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {_dgbg .E =NewCT_OMathArg ();_beg :for {_dfea ,_ebb :=d .Token ();
if _ebb !=nil {return _ebb ;};switch _afd :=_dfea .(type ){case _g .StartElement :switch _afd .Name {case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0067\u0072\u006f\u0075\u0070\u0043\u0068\u0072\u0050\u0072"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0067\u0072\u006f\u0075\u0070\u0043\u0068\u0072\u0050\u0072"}:_dgbg .GroupChrPr =NewCT_GroupChrPr ();
if _dfbd :=d .DecodeElement (_dgbg .GroupChrPr ,&_afd );_dfbd !=nil {return _dfbd ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0065"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0065"}:if _fde :=d .DecodeElement (_dgbg .E ,&_afd );
_fde !=nil {return _fde ;};default:_b .Log .Debug ("\u0073\u006bi\u0070\u0070\u0069\u006e\u0067\u0020\u0075\u006e\u0073\u0075\u0070\u0070\u006f\u0072\u0074\u0065\u0064\u0020\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0043\u0054\u005f\u0047\u0072\u006f\u0075\u0070\u0043\u0068\u0072\u0020\u0025\u0076",_afd .Name );
if _bgge :=d .Skip ();_bgge !=nil {return _bgge ;};};case _g .EndElement :break _beg ;case _g .CharData :};};return nil ;};

// ValidateWithPath validates the CT_EqArr and its children, prefixing error messages with path
func (_bdff *CT_EqArr )ValidateWithPath (path string )error {if _bdff .EqArrPr !=nil {if _ddfd :=_bdff .EqArrPr .ValidateWithPath (path +"\u002f\u0045\u0071\u0041\u0072\u0072\u0050\u0072");_ddfd !=nil {return _ddfd ;};};for _fgd ,_eeea :=range _bdff .E {if _egbc :=_eeea .ValidateWithPath (_a .Sprintf ("\u0025\u0073\u002f\u0045\u005b\u0025\u0064\u005d",path ,_fgd ));
_egbc !=nil {return _egbc ;};};return nil ;};func NewCT_Phant ()*CT_Phant {_dgbc :=&CT_Phant {};_dgbc .E =NewCT_OMathArg ();return _dgbc };

// Validate validates the CT_Integer255 and its children
func (_ddef *CT_Integer255 )Validate ()error {return _ddef .ValidateWithPath ("\u0043\u0054\u005f\u0049\u006e\u0074\u0065\u0067\u0065\u0072\u0032\u0035\u0035");};func NewCT_FuncPr ()*CT_FuncPr {_feab :=&CT_FuncPr {};return _feab };func (_aegb ST_Script )ValidateWithPath (path string )error {switch _aegb {case 0,1,2,3,4,5,6:default:return _a .Errorf ("\u0025s\u003a\u0020\u006f\u0075t\u0020\u006f\u0066\u0020\u0072a\u006eg\u0065 \u0076\u0061\u006c\u0075\u0065\u0020\u0025d",path ,int (_aegb ));
};return nil ;};

// Validate validates the CT_MathPr and its children
func (_cdff *CT_MathPr )Validate ()error {return _cdff .ValidateWithPath ("\u0043T\u005f\u004d\u0061\u0074\u0068\u0050r");};type CT_Phant struct{

// Phantom Properties
PhantPr *CT_PhantPr ;

// Base
E *CT_OMathArg ;};func (_cee *CT_OnOff )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {for _ ,_aagc :=range start .Attr {if _aagc .Name .Local =="\u0076\u0061\u006c"{_ccaff ,_eabd :=ParseUnionST_OnOff (_aagc .Value );if _eabd !=nil {return _eabd ;
};_cee .ValAttr =&_ccaff ;continue ;};};for {_bggf ,_bgcf :=d .Token ();if _bgcf !=nil {return _a .Errorf ("p\u0061r\u0073\u0069\u006e\u0067\u0020\u0043\u0054\u005fO\u006e\u004f\u0066\u0066: \u0025\u0073",_bgcf );};if _agabc ,_aeae :=_bggf .(_g .EndElement );
_aeae &&_agabc .Name ==start .Name {break ;};};return nil ;};func (_cbgf ST_TopBot )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {return e .EncodeElement (_cbgf .String (),start );};type CT_TopBot struct{

// Value
ValAttr ST_TopBot ;};func NewCT_SSubSup ()*CT_SSubSup {_fafd :=&CT_SSubSup {};_fafd .E =NewCT_OMathArg ();_fafd .Sub =NewCT_OMathArg ();_fafd .Sup =NewCT_OMathArg ();return _fafd ;};type CT_Shp struct{

// Value
ValAttr ST_Shp ;};

// ValidateWithPath validates the CT_Integer255 and its children, prefixing error messages with path
func (_dgcb *CT_Integer255 )ValidateWithPath (path string )error {if _dgcb .ValAttr < 1{return _a .Errorf ("%\u0073\u002f\u006d\u002e\u0056\u0061l\u0041\u0074\u0074\u0072\u0020\u006du\u0073\u0074\u0020\u0062\u0065\u0020\u003e=\u0020\u0031\u0020\u0028\u0068\u0061\u0076\u0065\u0020\u0025v\u0029",path ,_dgcb .ValAttr );
};if _dgcb .ValAttr > 255{return _a .Errorf ("\u0025\u0073/\u006d\u002e\u0056\u0061l\u0041\u0074t\u0072\u0020\u006d\u0075\u0073\u0074\u0020\u0062e\u0020\u003c\u003d\u0020\u0032\u0035\u0035\u0020\u0028\u0068\u0061\u0076e\u0020\u0025\u0076\u0029",path ,_dgcb .ValAttr );
};return nil ;};func NewEG_OMathElementsChoice ()*EG_OMathElementsChoice {_acbg :=&EG_OMathElementsChoice {};_acbg .OMathMathElementsChoice =NewEG_OMathMathElementsChoice ();return _acbg ;};

// ValidateWithPath validates the CT_SSubSup and its children, prefixing error messages with path
func (_gdec *CT_SSubSup )ValidateWithPath (path string )error {if _gdec .SSubSupPr !=nil {if _acb :=_gdec .SSubSupPr .ValidateWithPath (path +"\u002f\u0053\u0053\u0075\u0062\u0053\u0075\u0070\u0050\u0072");_acb !=nil {return _acb ;};};if _bgcbb :=_gdec .E .ValidateWithPath (path +"\u002f\u0045");
_bgcbb !=nil {return _bgcbb ;};if _beeg :=_gdec .Sub .ValidateWithPath (path +"\u002f\u0053\u0075\u0062");_beeg !=nil {return _beeg ;};if _agaf :=_gdec .Sup .ValidateWithPath (path +"\u002f\u0053\u0075\u0070");_agaf !=nil {return _agaf ;};return nil ;};
type MathPr struct{CT_MathPr };func (_fefe *CT_Nary )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {e .EncodeToken (start );if _fefe .NaryPr !=nil {_bec :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u006e\u0061\u0072\u0079\u0050\u0072"}};
e .EncodeElement (_fefe .NaryPr ,_bec );};_aadgf :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0073u\u0062"}};e .EncodeElement (_fefe .Sub ,_aadgf );_ebdfb :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0073u\u0070"}};e .EncodeElement (_fefe .Sup ,_ebdfb );
_aaae :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0065"}};e .EncodeElement (_fefe .E ,_aaae );e .EncodeToken (_g .EndElement {Name :start .Name });return nil ;};func (_eebc ST_BreakBin )ValidateWithPath (path string )error {switch _eebc {case 0,1,2,3:default:return _a .Errorf ("\u0025s\u003a\u0020\u006f\u0075t\u0020\u006f\u0066\u0020\u0072a\u006eg\u0065 \u0076\u0061\u006c\u0075\u0065\u0020\u0025d",path ,int (_eebc ));
};return nil ;};

// Validate validates the CT_RChoice and its children
func (_dfda *CT_RChoice )Validate ()error {return _dfda .ValidateWithPath ("\u0043\u0054\u005f\u0052\u0043\u0068\u006f\u0069\u0063\u0065");};func (_aafd *CT_YAlign )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {_aafd .ValAttr =_ab .ST_YAlign (1);
for _ ,_eadg :=range start .Attr {if _eadg .Name .Local =="\u0076\u0061\u006c"{_aafd .ValAttr .UnmarshalXMLAttr (_eadg );continue ;};};for {_fbea ,_cface :=d .Token ();if _cface !=nil {return _a .Errorf ("p\u0061\u0072\u0073\u0069ng\u0020C\u0054\u005f\u0059\u0041\u006ci\u0067\u006e\u003a\u0020\u0025\u0073",_cface );
};if _fbaad ,_bedff :=_fbea .(_g .EndElement );_bedff &&_fbaad .Name ==start .Name {break ;};};return nil ;};

// ValidateWithPath validates the EG_OMathMathElementsChoice and its children, prefixing error messages with path
func (_gaed *EG_OMathMathElementsChoice )ValidateWithPath (path string )error {if _gaed .Acc !=nil {if _bafdd :=_gaed .Acc .ValidateWithPath (path +"\u002f\u0041\u0063\u0063");_bafdd !=nil {return _bafdd ;};};if _gaed .Bar !=nil {if _gdfga :=_gaed .Bar .ValidateWithPath (path +"\u002f\u0042\u0061\u0072");
_gdfga !=nil {return _gdfga ;};};if _gaed .Box !=nil {if _bcga :=_gaed .Box .ValidateWithPath (path +"\u002f\u0042\u006f\u0078");_bcga !=nil {return _bcga ;};};if _gaed .BorderBox !=nil {if _cbcaa :=_gaed .BorderBox .ValidateWithPath (path +"\u002f\u0042\u006f\u0072\u0064\u0065\u0072\u0042\u006f\u0078");
_cbcaa !=nil {return _cbcaa ;};};if _gaed .D !=nil {if _eccb :=_gaed .D .ValidateWithPath (path +"\u002f\u0044");_eccb !=nil {return _eccb ;};};if _gaed .EqArr !=nil {if _cgba :=_gaed .EqArr .ValidateWithPath (path +"\u002f\u0045\u0071\u0041\u0072\u0072");
_cgba !=nil {return _cgba ;};};if _gaed .F !=nil {if _cbgb :=_gaed .F .ValidateWithPath (path +"\u002f\u0046");_cbgb !=nil {return _cbgb ;};};if _gaed .Func !=nil {if _dffcc :=_gaed .Func .ValidateWithPath (path +"\u002f\u0046\u0075n\u0063");_dffcc !=nil {return _dffcc ;
};};if _gaed .GroupChr !=nil {if _ebeb :=_gaed .GroupChr .ValidateWithPath (path +"\u002fG\u0072\u006f\u0075\u0070\u0043\u0068r");_ebeb !=nil {return _ebeb ;};};if _gaed .LimLow !=nil {if _ddbc :=_gaed .LimLow .ValidateWithPath (path +"\u002fL\u0069\u006d\u004c\u006f\u0077");
_ddbc !=nil {return _ddbc ;};};if _gaed .LimUpp !=nil {if _faae :=_gaed .LimUpp .ValidateWithPath (path +"\u002fL\u0069\u006d\u0055\u0070\u0070");_faae !=nil {return _faae ;};};if _gaed .M !=nil {if _fbgeb :=_gaed .M .ValidateWithPath (path +"\u002f\u004d");
_fbgeb !=nil {return _fbgeb ;};};if _gaed .Nary !=nil {if _fcbgd :=_gaed .Nary .ValidateWithPath (path +"\u002f\u004e\u0061r\u0079");_fcbgd !=nil {return _fcbgd ;};};if _gaed .Phant !=nil {if _bdfa :=_gaed .Phant .ValidateWithPath (path +"\u002f\u0050\u0068\u0061\u006e\u0074");
_bdfa !=nil {return _bdfa ;};};if _gaed .Rad !=nil {if _fcaa :=_gaed .Rad .ValidateWithPath (path +"\u002f\u0052\u0061\u0064");_fcaa !=nil {return _fcaa ;};};if _gaed .SPre !=nil {if _bgega :=_gaed .SPre .ValidateWithPath (path +"\u002f\u0053\u0050r\u0065");
_bgega !=nil {return _bgega ;};};if _gaed .SSub !=nil {if _fbed :=_gaed .SSub .ValidateWithPath (path +"\u002f\u0053\u0053u\u0062");_fbed !=nil {return _fbed ;};};if _gaed .SSubSup !=nil {if _gdcb :=_gaed .SSubSup .ValidateWithPath (path +"\u002f\u0053\u0053\u0075\u0062\u0053\u0075\u0070");
_gdcb !=nil {return _gdcb ;};};if _gaed .SSup !=nil {if _cdee :=_gaed .SSup .ValidateWithPath (path +"\u002f\u0053\u0053u\u0070");_cdee !=nil {return _cdee ;};};if _gaed .R !=nil {if _bdda :=_gaed .R .ValidateWithPath (path +"\u002f\u0052");_bdda !=nil {return _bdda ;
};};return nil ;};

// Validate validates the CT_OMathPara and its children
func (_eeb *CT_OMathPara )Validate ()error {return _eeb .ValidateWithPath ("\u0043\u0054\u005fO\u004d\u0061\u0074\u0068\u0050\u0061\u0072\u0061");};func (_bdea *CT_XAlign )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {_facc ,_abdb :=_bdea .ValAttr .MarshalXMLAttr (_g .Name {Local :"\u006d\u003a\u0076a\u006c"});
if _abdb !=nil {return _abdb ;};start .Attr =append (start .Attr ,_facc );e .EncodeToken (start );e .EncodeToken (_g .EndElement {Name :start .Name });return nil ;};type CT_GroupChr struct{

// Group-Character Properties
GroupChrPr *CT_GroupChrPr ;

// Base
E *CT_OMathArg ;};func NewCT_OMathJc ()*CT_OMathJc {_fbbe :=&CT_OMathJc {};return _fbbe };func (_gbg *CT_DPr )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {_gffc :for {_fea ,_ffe :=d .Token ();if _ffe !=nil {return _ffe ;};switch _bcdc :=_fea .(type ){case _g .StartElement :switch _bcdc .Name {case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0062\u0065\u0067\u0043\u0068\u0072"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0062\u0065\u0067\u0043\u0068\u0072"}:_gbg .BegChr =NewCT_Char ();
if _aeb :=d .DecodeElement (_gbg .BegChr ,&_bcdc );_aeb !=nil {return _aeb ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0073\u0065\u0070\u0043\u0068\u0072"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0073\u0065\u0070\u0043\u0068\u0072"}:_gbg .SepChr =NewCT_Char ();
if _bbfa :=d .DecodeElement (_gbg .SepChr ,&_bcdc );_bbfa !=nil {return _bbfa ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0065\u006e\u0064\u0043\u0068\u0072"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0065\u006e\u0064\u0043\u0068\u0072"}:_gbg .EndChr =NewCT_Char ();
if _cgc :=d .DecodeElement (_gbg .EndChr ,&_bcdc );_cgc !=nil {return _cgc ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0067\u0072\u006f\u0077"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0067\u0072\u006f\u0077"}:_gbg .Grow =NewCT_OnOff ();
if _ecce :=d .DecodeElement (_gbg .Grow ,&_bcdc );_ecce !=nil {return _ecce ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0073\u0068\u0070"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0073\u0068\u0070"}:_gbg .Shp =NewCT_Shp ();
if _cgf :=d .DecodeElement (_gbg .Shp ,&_bcdc );_cgf !=nil {return _cgf ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0063\u0074\u0072\u006c\u0050\u0072"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0063\u0074\u0072\u006c\u0050\u0072"}:_gbg .CtrlPr =NewCT_CtrlPr ();
if _cbbb :=d .DecodeElement (_gbg .CtrlPr ,&_bcdc );_cbbb !=nil {return _cbbb ;};default:_b .Log .Debug ("\u0073\u006b\u0069\u0070\u0070i\u006e\u0067\u0020\u0075\u006e\u0073\u0075\u0070\u0070\u006f\u0072\u0074\u0065d\u0020\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0043\u0054\u005f\u0044\u0050\u0072\u0020\u0025\u0076",_bcdc .Name );
if _cge :=d .Skip ();_cge !=nil {return _cge ;};};case _g .EndElement :break _gffc ;case _g .CharData :};};return nil ;};func (_cbafb ST_FType )String ()string {switch _cbafb {case 0:return "";case 1:return "\u0062\u0061\u0072";case 2:return "\u0073\u006b\u0077";
case 3:return "\u006c\u0069\u006e";case 4:return "\u006e\u006f\u0042a\u0072";};return "";};

// Validate validates the CT_SPrePr and its children
func (_gafc *CT_SPrePr )Validate ()error {return _gafc .ValidateWithPath ("\u0043T\u005f\u0053\u0050\u0072\u0065\u0050r");};func NewCT_Script ()*CT_Script {_gbgc :=&CT_Script {};return _gbgc };func NewCT_String ()*CT_String {_bfaa :=&CT_String {};return _bfaa };


// Validate validates the CT_Acc and its children
func (_dg *CT_Acc )Validate ()error {return _dg .ValidateWithPath ("\u0043\u0054\u005f\u0041\u0063\u0063");};type CT_String struct{

// value
ValAttr *string ;};type CT_Script struct{

// Value
ValAttr ST_Script ;};func (_aeec *CT_MPr )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {e .EncodeToken (start );if _aeec .BaseJc !=nil {_agfc :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0062\u0061\u0073\u0065\u004a\u0063"}};e .EncodeElement (_aeec .BaseJc ,_agfc );
};if _aeec .PlcHide !=nil {_dgca :=_g .StartElement {Name :_g .Name {Local :"\u006d:\u0070\u006c\u0063\u0048\u0069\u0064e"}};e .EncodeElement (_aeec .PlcHide ,_dgca );};if _aeec .RSpRule !=nil {_gcgf :=_g .StartElement {Name :_g .Name {Local :"\u006d:\u0072\u0053\u0070\u0052\u0075\u006ce"}};
e .EncodeElement (_aeec .RSpRule ,_gcgf );};if _aeec .CGpRule !=nil {_abd :=_g .StartElement {Name :_g .Name {Local :"\u006d:\u0063\u0047\u0070\u0052\u0075\u006ce"}};e .EncodeElement (_aeec .CGpRule ,_abd );};if _aeec .RSp !=nil {_abce :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0072S\u0070"}};
e .EncodeElement (_aeec .RSp ,_abce );};if _aeec .CSp !=nil {_ggcg :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0063S\u0070"}};e .EncodeElement (_aeec .CSp ,_ggcg );};if _aeec .CGp !=nil {_fgegff :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0063G\u0070"}};
e .EncodeElement (_aeec .CGp ,_fgegff );};if _aeec .Mcs !=nil {_ccded :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u006dc\u0073"}};e .EncodeElement (_aeec .Mcs ,_ccded );};if _aeec .CtrlPr !=nil {_fgcf :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0063\u0074\u0072\u006c\u0050\u0072"}};
e .EncodeElement (_aeec .CtrlPr ,_fgcf );};e .EncodeToken (_g .EndElement {Name :start .Name });return nil ;};func NewOMathPara ()*OMathPara {_baaga :=&OMathPara {};_baaga .CT_OMathPara =*NewCT_OMathPara ();return _baaga ;};type CT_MC struct{

// Matrix Column Properties
McPr *CT_MCPr ;};

// Validate validates the EG_OMathMathElementsChoice and its children
func (_ebea *EG_OMathMathElementsChoice )Validate ()error {return _ebea .ValidateWithPath ("\u0045\u0047\u005f\u004f\u004d\u0061\u0074\u0068\u004d\u0061\u0074h\u0045\u006c\u0065\u006d\u0065\u006e\u0074\u0073\u0043\u0068o\u0069\u0063\u0065");};func (_agg *CT_Integer2 )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {_agg .ValAttr =-2;
for _ ,_bdg :=range start .Attr {if _bdg .Name .Local =="\u0076\u0061\u006c"{_fad ,_adbc :=_f .ParseInt (_bdg .Value ,10,64);if _adbc !=nil {return _adbc ;};_agg .ValAttr =_fad ;continue ;};};for {_dec ,_bgf :=d .Token ();if _bgf !=nil {return _a .Errorf ("\u0070\u0061\u0072si\u006e\u0067\u0020\u0043\u0054\u005f\u0049\u006e\u0074\u0065\u0067\u0065\u0072\u0032\u003a\u0020\u0025\u0073",_bgf );
};if _eaa ,_efc :=_dec .(_g .EndElement );_efc &&_eaa .Name ==start .Name {break ;};};return nil ;};

// ValidateWithPath validates the CT_LimLoc and its children, prefixing error messages with path
func (_fab *CT_LimLoc )ValidateWithPath (path string )error {if _fab .ValAttr ==ST_LimLocUnset {return _a .Errorf ("\u0025\u0073\u002fV\u0061\u006c\u0041\u0074t\u0072\u0020\u0069\u0073\u0020\u0061\u0020m\u0061\u006e\u0064\u0061\u0074\u006f\u0072\u0079\u0020\u0066\u0069\u0065\u006c\u0064",path );
};if _cab :=_fab .ValAttr .ValidateWithPath (path +"\u002f\u0056\u0061\u006c\u0041\u0074\u0074\u0072");_cab !=nil {return _cab ;};return nil ;};func (_edgb *CT_MCS )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {_bcbad :for {_gfdf ,_abcdd :=d .Token ();
if _abcdd !=nil {return _abcdd ;};switch _aeee :=_gfdf .(type ){case _g .StartElement :switch _aeee .Name {case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u006d\u0063"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u006d\u0063"}:_ggaa :=NewCT_MC ();
if _agbg :=d .DecodeElement (_ggaa ,&_aeee );_agbg !=nil {return _agbg ;};_edgb .Mc =append (_edgb .Mc ,_ggaa );default:_b .Log .Debug ("\u0073\u006b\u0069\u0070\u0070i\u006e\u0067\u0020\u0075\u006e\u0073\u0075\u0070\u0070\u006f\u0072\u0074\u0065d\u0020\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0043\u0054\u005f\u004d\u0043\u0053\u0020\u0025\u0076",_aeee .Name );
if _cgeb :=d .Skip ();_cgeb !=nil {return _cgeb ;};};case _g .EndElement :break _bcbad ;case _g .CharData :};};return nil ;};func (_ebf *CT_RChoice )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {if _ebf .T !=nil {_adaec :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0074"}};
e .EncodeElement (_ebf .T ,_adaec );};return nil ;};func (_fbdc ST_Shp )String ()string {switch _fbdc {case 0:return "";case 1:return "\u0063\u0065\u006e\u0074\u0065\u0072\u0065\u0064";case 2:return "\u006d\u0061\u0074c\u0068";};return "";};const (ST_JcUnset ST_Jc =0;
ST_JcLeft ST_Jc =1;ST_JcRight ST_Jc =2;ST_JcCenter ST_Jc =3;ST_JcCenterGroup ST_Jc =4;);

// ValidateWithPath validates the CT_BorderBoxPr and its children, prefixing error messages with path
func (_abc *CT_BorderBoxPr )ValidateWithPath (path string )error {if _abc .HideTop !=nil {if _dad :=_abc .HideTop .ValidateWithPath (path +"\u002f\u0048\u0069\u0064\u0065\u0054\u006f\u0070");_dad !=nil {return _dad ;};};if _abc .HideBot !=nil {if _fbc :=_abc .HideBot .ValidateWithPath (path +"\u002f\u0048\u0069\u0064\u0065\u0042\u006f\u0074");
_fbc !=nil {return _fbc ;};};if _abc .HideLeft !=nil {if _cca :=_abc .HideLeft .ValidateWithPath (path +"\u002fH\u0069\u0064\u0065\u004c\u0065\u0066t");_cca !=nil {return _cca ;};};if _abc .HideRight !=nil {if _fgeb :=_abc .HideRight .ValidateWithPath (path +"\u002f\u0048\u0069\u0064\u0065\u0052\u0069\u0067\u0068\u0074");
_fgeb !=nil {return _fgeb ;};};if _abc .StrikeH !=nil {if _daed :=_abc .StrikeH .ValidateWithPath (path +"\u002f\u0053\u0074\u0072\u0069\u006b\u0065\u0048");_daed !=nil {return _daed ;};};if _abc .StrikeV !=nil {if _bd :=_abc .StrikeV .ValidateWithPath (path +"\u002f\u0053\u0074\u0072\u0069\u006b\u0065\u0056");
_bd !=nil {return _bd ;};};if _abc .StrikeBLTR !=nil {if _ddb :=_abc .StrikeBLTR .ValidateWithPath (path +"/\u0053\u0074\u0072\u0069\u006b\u0065\u0042\u004c\u0054\u0052");_ddb !=nil {return _ddb ;};};if _abc .StrikeTLBR !=nil {if _cag :=_abc .StrikeTLBR .ValidateWithPath (path +"/\u0053\u0074\u0072\u0069\u006b\u0065\u0054\u004c\u0042\u0052");
_cag !=nil {return _cag ;};};if _abc .CtrlPr !=nil {if _bcg :=_abc .CtrlPr .ValidateWithPath (path +"\u002fC\u0074\u0072\u006c\u0050\u0072");_bcg !=nil {return _bcg ;};};return nil ;};func (_bface *CT_Script )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {for _ ,_gfegg :=range start .Attr {if _gfegg .Name .Local =="\u0076\u0061\u006c"{_bface .ValAttr .UnmarshalXMLAttr (_gfegg );
continue ;};};for {_aabf ,_gggce :=d .Token ();if _gggce !=nil {return _a .Errorf ("p\u0061\u0072\u0073\u0069ng\u0020C\u0054\u005f\u0053\u0063\u0072i\u0070\u0074\u003a\u0020\u0025\u0073",_gggce );};if _ffbc ,_cgcgb :=_aabf .(_g .EndElement );_cgcgb &&_ffbc .Name ==start .Name {break ;
};};return nil ;};func (_bbd *CT_MCPr )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {e .EncodeToken (start );if _bbd .Count !=nil {_bgea :=_g .StartElement {Name :_g .Name {Local :"\u006d:\u0063\u006f\u0075\u006e\u0074"}};e .EncodeElement (_bbd .Count ,_bgea );
};if _bbd .McJc !=nil {_dbb :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u006d\u0063\u004a\u0063"}};e .EncodeElement (_bbd .McJc ,_dbb );};e .EncodeToken (_g .EndElement {Name :start .Name });return nil ;};

// Validate validates the CT_SSubPr and its children
func (_dabg *CT_SSubPr )Validate ()error {return _dabg .ValidateWithPath ("\u0043T\u005f\u0053\u0053\u0075\u0062\u0050r");};

// Validate validates the CT_Integer2 and its children
func (_cbag *CT_Integer2 )Validate ()error {return _cbag .ValidateWithPath ("C\u0054\u005f\u0049\u006e\u0074\u0065\u0067\u0065\u0072\u0032");};func NewCT_EqArr ()*CT_EqArr {_egfd :=&CT_EqArr {};return _egfd };func (_agga *CT_LimUpp )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {_agga .E =NewCT_OMathArg ();
_agga .Lim =NewCT_OMathArg ();_gdfa :for {_ebab ,_cfeb :=d .Token ();if _cfeb !=nil {return _cfeb ;};switch _ccde :=_ebab .(type ){case _g .StartElement :switch _ccde .Name {case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u006c\u0069\u006d\u0055\u0070\u0070\u0050\u0072"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u006c\u0069\u006d\u0055\u0070\u0070\u0050\u0072"}:_agga .LimUppPr =NewCT_LimUppPr ();
if _bgbb :=d .DecodeElement (_agga .LimUppPr ,&_ccde );_bgbb !=nil {return _bgbb ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0065"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0065"}:if _dea :=d .DecodeElement (_agga .E ,&_ccde );
_dea !=nil {return _dea ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u006c\u0069\u006d"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u006c\u0069\u006d"}:if _gfeg :=d .DecodeElement (_agga .Lim ,&_ccde );
_gfeg !=nil {return _gfeg ;};default:_b .Log .Debug ("\u0073k\u0069\u0070p\u0069\u006e\u0067\u0020u\u006e\u0073\u0075p\u0070\u006f\u0072\u0074\u0065\u0064\u0020\u0065\u006cem\u0065\u006e\u0074 \u006f\u006e \u0043\u0054\u005f\u004c\u0069\u006dU\u0070\u0070 \u0025\u0076",_ccde .Name );
if _aga :=d .Skip ();_aga !=nil {return _aga ;};};case _g .EndElement :break _gdfa ;case _g .CharData :};};return nil ;};func (_eeg *CT_CtrlPr )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {e .EncodeToken (start );e .EncodeToken (_g .EndElement {Name :start .Name });
return nil ;};func NewOMath ()*OMath {_adcc :=&OMath {};_adcc .CT_OMath =*NewCT_OMath ();return _adcc };func NewCT_Acc ()*CT_Acc {_ge :=&CT_Acc {};_ge .E =NewCT_OMathArg ();return _ge };func (_efab *CT_Style )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {for _ ,_gcbe :=range start .Attr {if _gcbe .Name .Local =="\u0076\u0061\u006c"{_efab .ValAttr .UnmarshalXMLAttr (_gcbe );
continue ;};};for {_edfg ,_agcd :=d .Token ();if _agcd !=nil {return _a .Errorf ("p\u0061r\u0073\u0069\u006e\u0067\u0020\u0043\u0054\u005fS\u0074\u0079\u006c\u0065: \u0025\u0073",_agcd );};if _cadc ,_ggae :=_edfg .(_g .EndElement );_ggae &&_cadc .Name ==start .Name {break ;
};};return nil ;};type CT_OMath struct{EG_OMathElements []*EG_OMathElements ;};func NewCT_BreakBinSub ()*CT_BreakBinSub {_ecc :=&CT_BreakBinSub {};return _ecc };func (_aadd *CT_RadPr )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {e .EncodeToken (start );
if _aadd .DegHide !=nil {_eadc :=_g .StartElement {Name :_g .Name {Local :"\u006d:\u0064\u0065\u0067\u0048\u0069\u0064e"}};e .EncodeElement (_aadd .DegHide ,_eadc );};if _aadd .CtrlPr !=nil {_bdcb :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0063\u0074\u0072\u006c\u0050\u0072"}};
e .EncodeElement (_aadd .CtrlPr ,_bdcb );};e .EncodeToken (_g .EndElement {Name :start .Name });return nil ;};

// Validate validates the CT_LimLow and its children
func (_ddd *CT_LimLow )Validate ()error {return _ddd .ValidateWithPath ("\u0043T\u005f\u004c\u0069\u006d\u004c\u006fw");};

// Validate validates the CT_MPr and its children
func (_agbff *CT_MPr )Validate ()error {return _agbff .ValidateWithPath ("\u0043\u0054\u005f\u004d\u0050\u0072");};func NewCT_FType ()*CT_FType {_ecd :=&CT_FType {};_ecd .ValAttr =ST_FType (1);return _ecd };func (_dbgg *CT_OMathPara )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {e .EncodeToken (start );
if _dbgg .OMathParaPr !=nil {_fdac :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u006f\u004d\u0061\u0074\u0068\u0050\u0061\u0072\u0061\u0050\u0072"}};e .EncodeElement (_dbgg .OMathParaPr ,_fdac );};_degg :=_g .StartElement {Name :_g .Name {Local :"\u006d:\u006f\u004d\u0061\u0074\u0068"}};
for _ ,_ebee :=range _dbgg .OMath {e .EncodeElement (_ebee ,_degg );};e .EncodeToken (_g .EndElement {Name :start .Name });return nil ;};

// ValidateWithPath validates the CT_SPrePr and its children, prefixing error messages with path
func (_dfdec *CT_SPrePr )ValidateWithPath (path string )error {if _dfdec .CtrlPr !=nil {if _edd :=_dfdec .CtrlPr .ValidateWithPath (path +"\u002fC\u0074\u0072\u006c\u0050\u0072");_edd !=nil {return _edd ;};};return nil ;};type CT_SSubPr struct{CtrlPr *CT_CtrlPr ;
};

// ValidateWithPath validates the CT_FPr and its children, prefixing error messages with path
func (_gab *CT_FPr )ValidateWithPath (path string )error {if _gab .Type !=nil {if _ecb :=_gab .Type .ValidateWithPath (path +"\u002f\u0054\u0079p\u0065");_ecb !=nil {return _ecb ;};};if _gab .CtrlPr !=nil {if _afc :=_gab .CtrlPr .ValidateWithPath (path +"\u002fC\u0074\u0072\u006c\u0050\u0072");
_afc !=nil {return _afc ;};};return nil ;};func (_gad *CT_FType )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {_bcf ,_dcf :=_gad .ValAttr .MarshalXMLAttr (_g .Name {Local :"\u006d\u003a\u0076a\u006c"});if _dcf !=nil {return _dcf ;};start .Attr =append (start .Attr ,_bcf );
e .EncodeToken (start );e .EncodeToken (_g .EndElement {Name :start .Name });return nil ;};func (_bcc *CT_EqArrPr )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {_bga :for {_gag ,_bdd :=d .Token ();if _bdd !=nil {return _bdd ;};switch _egff :=_gag .(type ){case _g .StartElement :switch _egff .Name {case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0062\u0061\u0073\u0065\u004a\u0063"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0062\u0061\u0073\u0065\u004a\u0063"}:_bcc .BaseJc =NewCT_YAlign ();
if _dfga :=d .DecodeElement (_bcc .BaseJc ,&_egff );_dfga !=nil {return _dfga ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u006da\u0078\u0044\u0069\u0073\u0074"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u006da\u0078\u0044\u0069\u0073\u0074"}:_bcc .MaxDist =NewCT_OnOff ();
if _bgg :=d .DecodeElement (_bcc .MaxDist ,&_egff );_bgg !=nil {return _bgg ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u006fb\u006a\u0044\u0069\u0073\u0074"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u006fb\u006a\u0044\u0069\u0073\u0074"}:_bcc .ObjDist =NewCT_OnOff ();
if _fce :=d .DecodeElement (_bcc .ObjDist ,&_egff );_fce !=nil {return _fce ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0072S\u0070\u0052\u0075\u006c\u0065"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0072S\u0070\u0052\u0075\u006c\u0065"}:_bcc .RSpRule =NewCT_SpacingRule ();
if _fdce :=d .DecodeElement (_bcc .RSpRule ,&_egff );_fdce !=nil {return _fdce ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0072\u0053\u0070"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0072\u0053\u0070"}:_bcc .RSp =NewCT_UnSignedInteger ();
if _ecg :=d .DecodeElement (_bcc .RSp ,&_egff );_ecg !=nil {return _ecg ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0063\u0074\u0072\u006c\u0050\u0072"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0063\u0074\u0072\u006c\u0050\u0072"}:_bcc .CtrlPr =NewCT_CtrlPr ();
if _ddfdg :=d .DecodeElement (_bcc .CtrlPr ,&_egff );_ddfdg !=nil {return _ddfdg ;};default:_b .Log .Debug ("\u0073k\u0069\u0070p\u0069\u006e\u0067 \u0075\u006e\u0073\u0075\u0070\u0070\u006fr\u0074\u0065\u0064\u0020\u0065\u006ce\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0043\u0054\u005fE\u0071\u0041\u0072\u0072\u0050\u0072\u0020\u0025\u0076",_egff .Name );
if _gdac :=d .Skip ();_gdac !=nil {return _gdac ;};};case _g .EndElement :break _bga ;case _g .CharData :};};return nil ;};func (_dabc *ST_FType )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {_cffbe ,_dgea :=d .Token ();if _dgea !=nil {return _dgea ;
};if _aefge ,_egbe :=_cffbe .(_g .EndElement );_egbe &&_aefge .Name ==start .Name {*_dabc =1;return nil ;};if _dadga ,_ddbb :=_cffbe .(_g .CharData );!_ddbb {return _a .Errorf ("\u0065\u0078\u0070\u0065\u0063\u0074\u0065\u0064\u0020\u0063\u0068a\u0072\u0020\u0064\u0061\u0074\u0061\u002c\u0020\u0067\u006ft\u0020\u0025\u0054",_cffbe );
}else {switch string (_dadga ){case "":*_dabc =0;case "\u0062\u0061\u0072":*_dabc =1;case "\u0073\u006b\u0077":*_dabc =2;case "\u006c\u0069\u006e":*_dabc =3;case "\u006e\u006f\u0042a\u0072":*_dabc =4;};};_cffbe ,_dgea =d .Token ();if _dgea !=nil {return _dgea ;
};if _gabbg ,_feaa :=_cffbe .(_g .EndElement );_feaa &&_gabbg .Name ==start .Name {return nil ;};return _a .Errorf ("\u0065\u0078\u0070\u0065c\u0074\u0065\u0064\u0020\u0065\u006e\u0064\u0020\u0065\u006ce\u006de\u006e\u0074\u002c\u0020\u0067\u006f\u0074 \u0025\u0076",_cffbe );
};type CT_FType struct{

// Value
ValAttr ST_FType ;};func (_efabf *ST_Shp )UnmarshalXMLAttr (attr _g .Attr )error {switch attr .Value {case "":*_efabf =0;case "\u0063\u0065\u006e\u0074\u0065\u0072\u0065\u0064":*_efabf =1;case "\u006d\u0061\u0074c\u0068":*_efabf =2;};return nil ;};

// ValidateWithPath validates the CT_YAlign and its children, prefixing error messages with path
func (_ecgb *CT_YAlign )ValidateWithPath (path string )error {if _ecgb .ValAttr ==_ab .ST_YAlignUnset {return _a .Errorf ("\u0025\u0073\u002fV\u0061\u006c\u0041\u0074t\u0072\u0020\u0069\u0073\u0020\u0061\u0020m\u0061\u006e\u0064\u0061\u0074\u006f\u0072\u0079\u0020\u0066\u0069\u0065\u006c\u0064",path );
};if _ebae :=_ecgb .ValAttr .ValidateWithPath (path +"\u002f\u0056\u0061\u006c\u0041\u0074\u0074\u0072");_ebae !=nil {return _ebae ;};return nil ;};

// Validate validates the CT_MathPrChoice and its children
func (_cadg *CT_MathPrChoice )Validate ()error {return _cadg .ValidateWithPath ("\u0043T\u005fM\u0061\u0074\u0068\u0050\u0072\u0043\u0068\u006f\u0069\u0063\u0065");};type ST_FType byte ;type CT_OMathJc struct{

// Value
ValAttr ST_Jc ;};func (_gdfg *CT_SpacingRule )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {_gdfg .ValAttr =0;for _ ,_cccb :=range start .Attr {if _cccb .Name .Local =="\u0076\u0061\u006c"{_bagc ,_edfa :=_f .ParseInt (_cccb .Value ,10,64);
if _edfa !=nil {return _edfa ;};_gdfg .ValAttr =_bagc ;continue ;};};for {_dbbgf ,_edeg :=d .Token ();if _edeg !=nil {return _a .Errorf ("\u0070\u0061\u0072\u0073\u0069\u006e\u0067\u0020\u0043\u0054\u005fS\u0070\u0061\u0063\u0069\u006e\u0067\u0052\u0075\u006c\u0065:\u0020\u0025\u0073",_edeg );
};if _bdgb ,_defcc :=_dbbgf .(_g .EndElement );_defcc &&_bdgb .Name ==start .Name {break ;};};return nil ;};type CT_UnSignedInteger struct{

// Value
ValAttr uint32 ;};func (_gfde *CT_OMathArg )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {e .EncodeToken (start );if _gfde .ArgPr !=nil {_cffa :=_g .StartElement {Name :_g .Name {Local :"\u006d:\u0061\u0072\u0067\u0050\u0072"}};e .EncodeElement (_gfde .ArgPr ,_cffa );
};if _gfde .EG_OMathElements !=nil {for _ ,_fca :=range _gfde .EG_OMathElements {_fca .MarshalXML (e ,_g .StartElement {});};};if _gfde .CtrlPr !=nil {_fbga :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0063\u0074\u0072\u006c\u0050\u0072"}};
e .EncodeElement (_gfde .CtrlPr ,_fbga );};e .EncodeToken (_g .EndElement {Name :start .Name });return nil ;};

// ValidateWithPath validates the CT_GroupChr and its children, prefixing error messages with path
func (_fcec *CT_GroupChr )ValidateWithPath (path string )error {if _fcec .GroupChrPr !=nil {if _fbeg :=_fcec .GroupChrPr .ValidateWithPath (path +"/\u0047\u0072\u006f\u0075\u0070\u0043\u0068\u0072\u0050\u0072");_fbeg !=nil {return _fbeg ;};};if _bce :=_fcec .E .ValidateWithPath (path +"\u002f\u0045");
_bce !=nil {return _bce ;};return nil ;};type CT_BoxPr struct{

// Operator Emulator
OpEmu *CT_OnOff ;

// No Break
NoBreak *CT_OnOff ;

// Differential
Diff *CT_OnOff ;

// Break
Brk *CT_ManualBreak ;

// Alignment
Aln *CT_OnOff ;CtrlPr *CT_CtrlPr ;};func (_aaf *CT_BoxPr )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {e .EncodeToken (start );if _aaf .OpEmu !=nil {_bdb :=_g .StartElement {Name :_g .Name {Local :"\u006d:\u006f\u0070\u0045\u006d\u0075"}};
e .EncodeElement (_aaf .OpEmu ,_bdb );};if _aaf .NoBreak !=nil {_cfe :=_g .StartElement {Name :_g .Name {Local :"\u006d:\u006e\u006f\u0042\u0072\u0065\u0061k"}};e .EncodeElement (_aaf .NoBreak ,_cfe );};if _aaf .Diff !=nil {_aagf :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0064\u0069\u0066\u0066"}};
e .EncodeElement (_aaf .Diff ,_aagf );};if _aaf .Brk !=nil {_gdd :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0062r\u006b"}};e .EncodeElement (_aaf .Brk ,_gdd );};if _aaf .Aln !=nil {_agda :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0061l\u006e"}};
e .EncodeElement (_aaf .Aln ,_agda );};if _aaf .CtrlPr !=nil {_abfa :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0063\u0074\u0072\u006c\u0050\u0072"}};e .EncodeElement (_aaf .CtrlPr ,_abfa );};e .EncodeToken (_g .EndElement {Name :start .Name });
return nil ;};

// ValidateWithPath validates the EG_OMathMathElements and its children, prefixing error messages with path
func (_begea *EG_OMathMathElements )ValidateWithPath (path string )error {if _agbac :=_begea .OMathMathElementsChoice .ValidateWithPath (path +"\u002fO\u004d\u0061\u0074\u0068\u004d\u0061\u0074\u0068\u0045\u006c\u0065m\u0065\u006e\u0074\u0073\u0043\u0068\u006f\u0069\u0063\u0065");
_agbac !=nil {return _agbac ;};return nil ;};func NewCT_Rad ()*CT_Rad {_eecdf :=&CT_Rad {};_eecdf .Deg =NewCT_OMathArg ();_eecdf .E =NewCT_OMathArg ();return _eecdf ;};type CT_ManualBreak struct{

// Index of Operator to Align To
AlnAtAttr *int64 ;};

// ValidateWithPath validates the CT_DPr and its children, prefixing error messages with path
func (_eec *CT_DPr )ValidateWithPath (path string )error {if _eec .BegChr !=nil {if _dcec :=_eec .BegChr .ValidateWithPath (path +"\u002fB\u0065\u0067\u0043\u0068\u0072");_dcec !=nil {return _dcec ;};};if _eec .SepChr !=nil {if _ggdg :=_eec .SepChr .ValidateWithPath (path +"\u002fS\u0065\u0070\u0043\u0068\u0072");
_ggdg !=nil {return _ggdg ;};};if _eec .EndChr !=nil {if _efdg :=_eec .EndChr .ValidateWithPath (path +"\u002fE\u006e\u0064\u0043\u0068\u0072");_efdg !=nil {return _efdg ;};};if _eec .Grow !=nil {if _debc :=_eec .Grow .ValidateWithPath (path +"\u002f\u0047\u0072o\u0077");
_debc !=nil {return _debc ;};};if _eec .Shp !=nil {if _ccf :=_eec .Shp .ValidateWithPath (path +"\u002f\u0053\u0068\u0070");_ccf !=nil {return _ccf ;};};if _eec .CtrlPr !=nil {if _ccg :=_eec .CtrlPr .ValidateWithPath (path +"\u002fC\u0074\u0072\u006c\u0050\u0072");
_ccg !=nil {return _ccg ;};};return nil ;};

// Validate validates the CT_EqArrPr and its children
func (_eecc *CT_EqArrPr )Validate ()error {return _eecc .ValidateWithPath ("\u0043\u0054\u005f\u0045\u0071\u0041\u0072\u0072\u0050\u0072");};func (_bdcd *ST_BreakBin )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {_adddg ,_dgbb :=d .Token ();
if _dgbb !=nil {return _dgbb ;};if _dgce ,_egaf :=_adddg .(_g .EndElement );_egaf &&_dgce .Name ==start .Name {*_bdcd =1;return nil ;};if _gdad ,_cffbg :=_adddg .(_g .CharData );!_cffbg {return _a .Errorf ("\u0065\u0078\u0070\u0065\u0063\u0074\u0065\u0064\u0020\u0063\u0068a\u0072\u0020\u0064\u0061\u0074\u0061\u002c\u0020\u0067\u006ft\u0020\u0025\u0054",_adddg );
}else {switch string (_gdad ){case "":*_bdcd =0;case "\u0062\u0065\u0066\u006f\u0072\u0065":*_bdcd =1;case "\u0061\u0066\u0074e\u0072":*_bdcd =2;case "\u0072\u0065\u0070\u0065\u0061\u0074":*_bdcd =3;};};_adddg ,_dgbb =d .Token ();if _dgbb !=nil {return _dgbb ;
};if _fbde ,_bddbb :=_adddg .(_g .EndElement );_bddbb &&_fbde .Name ==start .Name {return nil ;};return _a .Errorf ("\u0065\u0078\u0070\u0065c\u0074\u0065\u0064\u0020\u0065\u006e\u0064\u0020\u0065\u006ce\u006de\u006e\u0074\u002c\u0020\u0067\u006f\u0074 \u0025\u0076",_adddg );
};func (_dgcc *CT_OMathParaPr )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {_ecbg :for {_fcf ,_gfae :=d .Token ();if _gfae !=nil {return _gfae ;};switch _fdec :=_fcf .(type ){case _g .StartElement :switch _fdec .Name {case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u006a\u0063"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u006a\u0063"}:_dgcc .Jc =NewCT_OMathJc ();
if _edaa :=d .DecodeElement (_dgcc .Jc ,&_fdec );_edaa !=nil {return _edaa ;};default:_b .Log .Debug ("\u0073\u006b\u0069\u0070\u0070\u0069n\u0067\u0020\u0075\u006e\u0073\u0075\u0070\u0070\u006f\u0072\u0074\u0065\u0064\u0020\u0065\u006c\u0065\u006d\u0065\u006et\u0020\u006f\u006e\u0020\u0043\u0054\u005f\u004f\u004d\u0061\u0074\u0068\u0050\u0061r\u0061P\u0072\u0020\u0025\u0076",_fdec .Name );
if _cabbg :=d .Skip ();_cabbg !=nil {return _cabbg ;};};case _g .EndElement :break _ecbg ;case _g .CharData :};};return nil ;};func (_bdae *CT_Phant )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {e .EncodeToken (start );if _bdae .PhantPr !=nil {_fcaf :=_g .StartElement {Name :_g .Name {Local :"\u006d:\u0070\u0068\u0061\u006e\u0074\u0050r"}};
e .EncodeElement (_bdae .PhantPr ,_fcaf );};_dccg :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0065"}};e .EncodeElement (_bdae .E ,_dccg );e .EncodeToken (_g .EndElement {Name :start .Name });return nil ;};func (_ccbc *EG_OMathMathElementsChoice )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {_daeac :=start ;
switch start .Name {case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0061\u0063\u0063"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0061\u0063\u0063"}:_ccbc .Acc =NewCT_Acc ();
if _befcb :=d .DecodeElement (_ccbc .Acc ,&_daeac );_befcb !=nil {return _befcb ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0062\u0061\u0072"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0062\u0061\u0072"}:_ccbc .Bar =NewCT_Bar ();
if _bdfc :=d .DecodeElement (_ccbc .Bar ,&_daeac );_bdfc !=nil {return _bdfc ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0062\u006f\u0078"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0062\u006f\u0078"}:_ccbc .Box =NewCT_Box ();
if _ddff :=d .DecodeElement (_ccbc .Box ,&_daeac );_ddff !=nil {return _ddff ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0062o\u0072\u0064\u0065\u0072\u0042\u006fx"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0062o\u0072\u0064\u0065\u0072\u0042\u006fx"}:_ccbc .BorderBox =NewCT_BorderBox ();
if _fcgd :=d .DecodeElement (_ccbc .BorderBox ,&_daeac );_fcgd !=nil {return _fcgd ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0064"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0064"}:_ccbc .D =NewCT_D ();
if _ebef :=d .DecodeElement (_ccbc .D ,&_daeac );_ebef !=nil {return _ebef ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0065\u0071\u0041r\u0072"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0065\u0071\u0041r\u0072"}:_ccbc .EqArr =NewCT_EqArr ();
if _fbadg :=d .DecodeElement (_ccbc .EqArr ,&_daeac );_fbadg !=nil {return _fbadg ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0066"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0066"}:_ccbc .F =NewCT_F ();
if _egfg :=d .DecodeElement (_ccbc .F ,&_daeac );_egfg !=nil {return _egfg ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0066\u0075\u006e\u0063"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0066\u0075\u006e\u0063"}:_ccbc .Func =NewCT_Func ();
if _gabb :=d .DecodeElement (_ccbc .Func ,&_daeac );_gabb !=nil {return _gabb ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0067\u0072\u006f\u0075\u0070\u0043\u0068\u0072"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0067\u0072\u006f\u0075\u0070\u0043\u0068\u0072"}:_ccbc .GroupChr =NewCT_GroupChr ();
if _fgbdb :=d .DecodeElement (_ccbc .GroupChr ,&_daeac );_fgbdb !=nil {return _fgbdb ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u006c\u0069\u006d\u004c\u006f\u0077"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u006c\u0069\u006d\u004c\u006f\u0077"}:_ccbc .LimLow =NewCT_LimLow ();
if _gggad :=d .DecodeElement (_ccbc .LimLow ,&_daeac );_gggad !=nil {return _gggad ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u006c\u0069\u006d\u0055\u0070\u0070"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u006c\u0069\u006d\u0055\u0070\u0070"}:_ccbc .LimUpp =NewCT_LimUpp ();
if _cdea :=d .DecodeElement (_ccbc .LimUpp ,&_daeac );_cdea !=nil {return _cdea ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u006d"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u006d"}:_ccbc .M =NewCT_M ();
if _faab :=d .DecodeElement (_ccbc .M ,&_daeac );_faab !=nil {return _faab ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u006e\u0061\u0072\u0079"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u006e\u0061\u0072\u0079"}:_ccbc .Nary =NewCT_Nary ();
if _cafe :=d .DecodeElement (_ccbc .Nary ,&_daeac );_cafe !=nil {return _cafe ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0070\u0068\u0061n\u0074"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0070\u0068\u0061n\u0074"}:_ccbc .Phant =NewCT_Phant ();
if _efg :=d .DecodeElement (_ccbc .Phant ,&_daeac );_efg !=nil {return _efg ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0072\u0061\u0064"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0072\u0061\u0064"}:_ccbc .Rad =NewCT_Rad ();
if _agdag :=d .DecodeElement (_ccbc .Rad ,&_daeac );_agdag !=nil {return _agdag ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0073\u0050\u0072\u0065"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0073\u0050\u0072\u0065"}:_ccbc .SPre =NewCT_SPre ();
if _aaag :=d .DecodeElement (_ccbc .SPre ,&_daeac );_aaag !=nil {return _aaag ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0073\u0053\u0075\u0062"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0073\u0053\u0075\u0062"}:_ccbc .SSub =NewCT_SSub ();
if _eggb :=d .DecodeElement (_ccbc .SSub ,&_daeac );_eggb !=nil {return _eggb ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0073S\u0075\u0062\u0053\u0075\u0070"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0073S\u0075\u0062\u0053\u0075\u0070"}:_ccbc .SSubSup =NewCT_SSubSup ();
if _cdcc :=d .DecodeElement (_ccbc .SSubSup ,&_daeac );_cdcc !=nil {return _cdcc ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0073\u0053\u0075\u0070"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0073\u0053\u0075\u0070"}:_ccbc .SSup =NewCT_SSup ();
if _fefc :=d .DecodeElement (_ccbc .SSup ,&_daeac );_fefc !=nil {return _fefc ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0072"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0072"}:_ccbc .R =NewCT_R ();
if _bcda :=d .DecodeElement (_ccbc .R ,&_daeac );_bcda !=nil {return _bcda ;};default:_b .Log .Debug ("s\u006b\u0069\u0070\u0070\u0069\u006e\u0067\u0020\u0075\u006e\u0073\u0075\u0070\u0070\u006f\u0072\u0074\u0065\u0064\u0020\u0065\u006c\u0065\u006d\u0065\u006et\u0020o\u006e\u0020\u0045\u0047_\u004f\u004da\u0074\u0068\u004d\u0061\u0074\u0068\u0045\u006c\u0065\u006d\u0065\u006e\u0074\u0073\u0043\u0068\u006f\u0069\u0063\u0065\u0020\u0025\u0076",_daeac .Name );
if _eegd :=d .Skip ();_eegd !=nil {return _eegd ;};};return nil ;};type CT_OnOff struct{

// value
ValAttr *_ab .ST_OnOff ;};

// Validate validates the CT_SPre and its children
func (_eecdff *CT_SPre )Validate ()error {return _eecdff .ValidateWithPath ("\u0043T\u005f\u0053\u0050\u0072\u0065");};func NewCT_RPR ()*CT_RPR {_aege :=&CT_RPR {};return _aege };func NewCT_PhantPr ()*CT_PhantPr {_edaca :=&CT_PhantPr {};return _edaca };
func (_bge *CT_CtrlPr )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {for {_ce ,_edb :=d .Token ();if _edb !=nil {return _a .Errorf ("p\u0061\u0072\u0073\u0069ng\u0020C\u0054\u005f\u0043\u0074\u0072l\u0050\u0072\u003a\u0020\u0025\u0073",_edb );
};if _cfd ,_ggce :=_ce .(_g .EndElement );_ggce &&_cfd .Name ==start .Name {break ;};};return nil ;};func (_bcge ST_LimLoc )ValidateWithPath (path string )error {switch _bcge {case 0,1,2:default:return _a .Errorf ("\u0025s\u003a\u0020\u006f\u0075t\u0020\u006f\u0066\u0020\u0072a\u006eg\u0065 \u0076\u0061\u006c\u0075\u0065\u0020\u0025d",path ,int (_bcge ));
};return nil ;};

// Validate validates the CT_Nary and its children
func (_abcdb *CT_Nary )Validate ()error {return _abcdb .ValidateWithPath ("\u0043T\u005f\u004e\u0061\u0072\u0079");};

// ValidateWithPath validates the CT_SSubPr and its children, prefixing error messages with path
func (_bcdd *CT_SSubPr )ValidateWithPath (path string )error {if _bcdd .CtrlPr !=nil {if _fcfg :=_bcdd .CtrlPr .ValidateWithPath (path +"\u002fC\u0074\u0072\u006c\u0050\u0072");_fcfg !=nil {return _fcfg ;};};return nil ;};

// Validate validates the CT_TwipsMeasure and its children
func (_fddbf *CT_TwipsMeasure )Validate ()error {return _fddbf .ValidateWithPath ("\u0043T\u005fT\u0077\u0069\u0070\u0073\u004d\u0065\u0061\u0073\u0075\u0072\u0065");};

// ValidateWithPath validates the CT_M and its children, prefixing error messages with path
func (_fabb *CT_M )ValidateWithPath (path string )error {if _fabb .MPr !=nil {if _efdga :=_fabb .MPr .ValidateWithPath (path +"\u002f\u004d\u0050\u0072");_efdga !=nil {return _efdga ;};};for _baeg ,_acec :=range _fabb .Mr {if _bfeg :=_acec .ValidateWithPath (_a .Sprintf ("\u0025s\u002f\u004d\u0072\u005b\u0025\u0064]",path ,_baeg ));
_bfeg !=nil {return _bfeg ;};};return nil ;};

// Validate validates the CT_Rad and its children
func (_ddbd *CT_Rad )Validate ()error {return _ddbd .ValidateWithPath ("\u0043\u0054\u005f\u0052\u0061\u0064");};func (_dbgdc *CT_Integer2 )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {start .Attr =append (start .Attr ,_g .Attr {Name :_g .Name {Local :"\u006d\u003a\u0076a\u006c"},Value :_a .Sprintf ("\u0025\u0076",_dbgdc .ValAttr )});
e .EncodeToken (start );e .EncodeToken (_g .EndElement {Name :start .Name });return nil ;};func NewCT_MCS ()*CT_MCS {_fdcg :=&CT_MCS {};return _fdcg };func (_eefg *CT_FuncPr )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {e .EncodeToken (start );
if _eefg .CtrlPr !=nil {_afe :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0063\u0074\u0072\u006c\u0050\u0072"}};e .EncodeElement (_eefg .CtrlPr ,_afe );};e .EncodeToken (_g .EndElement {Name :start .Name });return nil ;};

// ValidateWithPath validates the CT_R and its children, prefixing error messages with path
func (_dffc *CT_R )ValidateWithPath (path string )error {if _dffc .RPr !=nil {if _ffb :=_dffc .RPr .ValidateWithPath (path +"\u002f\u0052\u0050\u0072");_ffb !=nil {return _ffb ;};};for _babe ,_ddea :=range _dffc .RChoice {if _dggd :=_ddea .ValidateWithPath (_a .Sprintf ("\u0025\u0073\u002f\u0052\u0043\u0068\u006f\u0069\u0063e\u005b\u0025\u0064\u005d",path ,_babe ));
_dggd !=nil {return _dggd ;};};return nil ;};func (_gceb *CT_String )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {if _gceb .ValAttr !=nil {start .Attr =append (start .Attr ,_g .Attr {Name :_g .Name {Local :"\u006d\u003a\u0076a\u006c"},Value :_a .Sprintf ("\u0025\u0076",*_gceb .ValAttr )});
};e .EncodeToken (start );e .EncodeToken (_g .EndElement {Name :start .Name });return nil ;};func NewCT_F ()*CT_F {_dcc :=&CT_F {};_dcc .Num =NewCT_OMathArg ();_dcc .Den =NewCT_OMathArg ();return _dcc ;};

// ValidateWithPath validates the CT_BreakBinSub and its children, prefixing error messages with path
func (_daa *CT_BreakBinSub )ValidateWithPath (path string )error {if _bf :=_daa .ValAttr .ValidateWithPath (path +"\u002f\u0056\u0061\u006c\u0041\u0074\u0074\u0072");_bf !=nil {return _bf ;};return nil ;};func (_cbdce *OMathPara )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {_cbdce .CT_OMathPara =*NewCT_OMathPara ();
_cffb :for {_bfad ,_dbde :=d .Token ();if _dbde !=nil {return _dbde ;};switch _aecgd :=_bfad .(type ){case _g .StartElement :switch _aecgd .Name {case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"o\u004d\u0061\u0074\u0068\u0050\u0061\u0072\u0061\u0050\u0072"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"o\u004d\u0061\u0074\u0068\u0050\u0061\u0072\u0061\u0050\u0072"}:_cbdce .OMathParaPr =NewCT_OMathParaPr ();
if _gacg :=d .DecodeElement (_cbdce .OMathParaPr ,&_aecgd );_gacg !=nil {return _gacg ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u006f\u004d\u0061t\u0068"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u006f\u004d\u0061t\u0068"}:_dafg :=NewCT_OMath ();
if _ebgg :=d .DecodeElement (_dafg ,&_aecgd );_ebgg !=nil {return _ebgg ;};_cbdce .OMath =append (_cbdce .OMath ,_dafg );default:_b .Log .Debug ("\u0073k\u0069\u0070p\u0069\u006e\u0067\u0020u\u006e\u0073\u0075p\u0070\u006f\u0072\u0074\u0065\u0064\u0020\u0065\u006cem\u0065\u006e\u0074 \u006f\u006e \u004f\u004d\u0061\u0074\u0068\u0050a\u0072\u0061 \u0025\u0076",_aecgd .Name );
if _ggada :=d .Skip ();_ggada !=nil {return _ggada ;};};case _g .EndElement :break _cffb ;case _g .CharData :};};return nil ;};func (_eddc *ST_Style )UnmarshalXMLAttr (attr _g .Attr )error {switch attr .Value {case "":*_eddc =0;case "\u0070":*_eddc =1;
case "\u0062":*_eddc =2;case "\u0069":*_eddc =3;case "\u0062\u0069":*_eddc =4;};return nil ;};func NewEG_OMathElements ()*EG_OMathElements {_dgfaf :=&EG_OMathElements {};_dgfaf .OMathElementsChoice =NewEG_OMathElementsChoice ();return _dgfaf ;};func (_cdgbe ST_Style )MarshalXMLAttr (name _g .Name )(_g .Attr ,error ){_feba :=_g .Attr {};
_feba .Name =name ;switch _cdgbe {case ST_StyleUnset :_feba .Value ="";case ST_StyleP :_feba .Value ="\u0070";case ST_StyleB :_feba .Value ="\u0062";case ST_StyleI :_feba .Value ="\u0069";case ST_StyleBi :_feba .Value ="\u0062\u0069";};return _feba ,nil ;
};func (_ebec *ST_Shp )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {_dbag ,_decc :=d .Token ();if _decc !=nil {return _decc ;};if _gefc ,_gfce :=_dbag .(_g .EndElement );_gfce &&_gefc .Name ==start .Name {*_ebec =1;return nil ;};if _fdgb ,_gfdc :=_dbag .(_g .CharData );
!_gfdc {return _a .Errorf ("\u0065\u0078\u0070\u0065\u0063\u0074\u0065\u0064\u0020\u0063\u0068a\u0072\u0020\u0064\u0061\u0074\u0061\u002c\u0020\u0067\u006ft\u0020\u0025\u0054",_dbag );}else {switch string (_fdgb ){case "":*_ebec =0;case "\u0063\u0065\u006e\u0074\u0065\u0072\u0065\u0064":*_ebec =1;
case "\u006d\u0061\u0074c\u0068":*_ebec =2;};};_dbag ,_decc =d .Token ();if _decc !=nil {return _decc ;};if _dfgd ,_bcbg :=_dbag .(_g .EndElement );_bcbg &&_dfgd .Name ==start .Name {return nil ;};return _a .Errorf ("\u0065\u0078\u0070\u0065c\u0074\u0065\u0064\u0020\u0065\u006e\u0064\u0020\u0065\u006ce\u006de\u006e\u0074\u002c\u0020\u0067\u006f\u0074 \u0025\u0076",_dbag );
};func (_cacdb ST_LimLoc )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {return e .EncodeElement (_cacdb .String (),start );};

// Validate validates the CT_OMathArgPr and its children
func (_bfcc *CT_OMathArgPr )Validate ()error {return _bfcc .ValidateWithPath ("\u0043\u0054\u005f\u004f\u004d\u0061\u0074\u0068\u0041\u0072\u0067\u0050\u0072");};func (_cgcaa *ST_TopBot )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {_cdcg ,_eadbg :=d .Token ();
if _eadbg !=nil {return _eadbg ;};if _dbdb ,_edbg :=_cdcg .(_g .EndElement );_edbg &&_dbdb .Name ==start .Name {*_cgcaa =1;return nil ;};if _agac ,_fedb :=_cdcg .(_g .CharData );!_fedb {return _a .Errorf ("\u0065\u0078\u0070\u0065\u0063\u0074\u0065\u0064\u0020\u0063\u0068a\u0072\u0020\u0064\u0061\u0074\u0061\u002c\u0020\u0067\u006ft\u0020\u0025\u0054",_cdcg );
}else {switch string (_agac ){case "":*_cgcaa =0;case "\u0074\u006f\u0070":*_cgcaa =1;case "\u0062\u006f\u0074":*_cgcaa =2;};};_cdcg ,_eadbg =d .Token ();if _eadbg !=nil {return _eadbg ;};if _cea ,_abgb :=_cdcg .(_g .EndElement );_abgb &&_cea .Name ==start .Name {return nil ;
};return _a .Errorf ("\u0065\u0078\u0070\u0065c\u0074\u0065\u0064\u0020\u0065\u006e\u0064\u0020\u0065\u006ce\u006de\u006e\u0074\u002c\u0020\u0067\u006f\u0074 \u0025\u0076",_cdcg );};func (_ebe *CT_BreakBinSub )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {for _ ,_ggf :=range start .Attr {if _ggf .Name .Local =="\u0076\u0061\u006c"{_ebe .ValAttr .UnmarshalXMLAttr (_ggf );
continue ;};};for {_gec ,_eeef :=d .Token ();if _eeef !=nil {return _a .Errorf ("\u0070\u0061\u0072\u0073\u0069\u006e\u0067\u0020\u0043\u0054\u005fB\u0072\u0065\u0061\u006b\u0042\u0069\u006e\u0053\u0075\u0062:\u0020\u0025\u0073",_eeef );};if _fcd ,_cgaa :=_gec .(_g .EndElement );
_cgaa &&_fcd .Name ==start .Name {break ;};};return nil ;};func (_adfbf *CT_R )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {e .EncodeToken (start );if _adfbf .RPr !=nil {_gfeb :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0072P\u0072"}};
e .EncodeElement (_adfbf .RPr ,_gfeb );};if _adfbf .RChoice !=nil {for _ ,_aabe :=range _adfbf .RChoice {_aabe .MarshalXML (e ,_g .StartElement {});};};e .EncodeToken (_g .EndElement {Name :start .Name });return nil ;};

// Validate validates the CT_OMathJc and its children
func (_ddbg *CT_OMathJc )Validate ()error {return _ddbg .ValidateWithPath ("\u0043\u0054\u005f\u004f\u004d\u0061\u0074\u0068\u004a\u0063");};

// ValidateWithPath validates the CT_Acc and its children, prefixing error messages with path
func (_df *CT_Acc )ValidateWithPath (path string )error {if _df .AccPr !=nil {if _ac :=_df .AccPr .ValidateWithPath (path +"\u002f\u0041\u0063\u0063\u0050\u0072");_ac !=nil {return _ac ;};};if _ff :=_df .E .ValidateWithPath (path +"\u002f\u0045");_ff !=nil {return _ff ;
};return nil ;};func (_dbccg ST_Shp )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {return e .EncodeElement (_dbccg .String (),start );};func NewCT_BoxPr ()*CT_BoxPr {_dfb :=&CT_BoxPr {};return _dfb };type CT_Style struct{

// Value
ValAttr ST_Style ;};func (_gcdgf *ST_Style )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {_fdfaf ,_bbce :=d .Token ();if _bbce !=nil {return _bbce ;};if _aeag ,_fcea :=_fdfaf .(_g .EndElement );_fcea &&_aeag .Name ==start .Name {*_gcdgf =1;
return nil ;};if _fggd ,_fgcfef :=_fdfaf .(_g .CharData );!_fgcfef {return _a .Errorf ("\u0065\u0078\u0070\u0065\u0063\u0074\u0065\u0064\u0020\u0063\u0068a\u0072\u0020\u0064\u0061\u0074\u0061\u002c\u0020\u0067\u006ft\u0020\u0025\u0054",_fdfaf );}else {switch string (_fggd ){case "":*_gcdgf =0;
case "\u0070":*_gcdgf =1;case "\u0062":*_gcdgf =2;case "\u0069":*_gcdgf =3;case "\u0062\u0069":*_gcdgf =4;};};_fdfaf ,_bbce =d .Token ();if _bbce !=nil {return _bbce ;};if _adgg ,_badd :=_fdfaf .(_g .EndElement );_badd &&_adgg .Name ==start .Name {return nil ;
};return _a .Errorf ("\u0065\u0078\u0070\u0065c\u0074\u0065\u0064\u0020\u0065\u006e\u0064\u0020\u0065\u006ce\u006de\u006e\u0074\u002c\u0020\u0067\u006f\u0074 \u0025\u0076",_fdfaf );};func (_ecaa *CT_LimLoc )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {_ecaa .ValAttr =ST_LimLoc (1);
for _ ,_fef :=range start .Attr {if _fef .Name .Local =="\u0076\u0061\u006c"{_ecaa .ValAttr .UnmarshalXMLAttr (_fef );continue ;};};for {_cfage ,_ecec :=d .Token ();if _ecec !=nil {return _a .Errorf ("p\u0061\u0072\u0073\u0069ng\u0020C\u0054\u005f\u004c\u0069\u006dL\u006f\u0063\u003a\u0020\u0025\u0073",_ecec );
};if _efee ,_eae :=_cfage .(_g .EndElement );_eae &&_efee .Name ==start .Name {break ;};};return nil ;};func (_gccbf *EG_OMathMathElements )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {_gccbf .OMathMathElementsChoice =NewEG_OMathMathElementsChoice ();
_ecgbe :for {_fbeae ,_dadg :=d .Token ();if _dadg !=nil {return _dadg ;};switch _dggde :=_fbeae .(type ){case _g .StartElement :switch _dggde .Name {case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0061\u0063\u0063"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0061\u0063\u0063"}:_gccbf .OMathMathElementsChoice =NewEG_OMathMathElementsChoice ();
if _eada :=d .DecodeElement (&_gccbf .OMathMathElementsChoice .Acc ,&_dggde );_eada !=nil {return _eada ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0062\u0061\u0072"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0062\u0061\u0072"}:_gccbf .OMathMathElementsChoice =NewEG_OMathMathElementsChoice ();
if _gbafc :=d .DecodeElement (&_gccbf .OMathMathElementsChoice .Bar ,&_dggde );_gbafc !=nil {return _gbafc ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0062\u006f\u0078"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0062\u006f\u0078"}:_gccbf .OMathMathElementsChoice =NewEG_OMathMathElementsChoice ();
if _gdcgc :=d .DecodeElement (&_gccbf .OMathMathElementsChoice .Box ,&_dggde );_gdcgc !=nil {return _gdcgc ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0062o\u0072\u0064\u0065\u0072\u0042\u006fx"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0062o\u0072\u0064\u0065\u0072\u0042\u006fx"}:_gccbf .OMathMathElementsChoice =NewEG_OMathMathElementsChoice ();
if _dccd :=d .DecodeElement (&_gccbf .OMathMathElementsChoice .BorderBox ,&_dggde );_dccd !=nil {return _dccd ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0064"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0064"}:_gccbf .OMathMathElementsChoice =NewEG_OMathMathElementsChoice ();
if _abccb :=d .DecodeElement (&_gccbf .OMathMathElementsChoice .D ,&_dggde );_abccb !=nil {return _abccb ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0065\u0071\u0041r\u0072"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0065\u0071\u0041r\u0072"}:_gccbf .OMathMathElementsChoice =NewEG_OMathMathElementsChoice ();
if _bdba :=d .DecodeElement (&_gccbf .OMathMathElementsChoice .EqArr ,&_dggde );_bdba !=nil {return _bdba ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0066"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0066"}:_gccbf .OMathMathElementsChoice =NewEG_OMathMathElementsChoice ();
if _cbefe :=d .DecodeElement (&_gccbf .OMathMathElementsChoice .F ,&_dggde );_cbefe !=nil {return _cbefe ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0066\u0075\u006e\u0063"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0066\u0075\u006e\u0063"}:_gccbf .OMathMathElementsChoice =NewEG_OMathMathElementsChoice ();
if _aabgg :=d .DecodeElement (&_gccbf .OMathMathElementsChoice .Func ,&_dggde );_aabgg !=nil {return _aabgg ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0067\u0072\u006f\u0075\u0070\u0043\u0068\u0072"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0067\u0072\u006f\u0075\u0070\u0043\u0068\u0072"}:_gccbf .OMathMathElementsChoice =NewEG_OMathMathElementsChoice ();
if _ebbd :=d .DecodeElement (&_gccbf .OMathMathElementsChoice .GroupChr ,&_dggde );_ebbd !=nil {return _ebbd ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u006c\u0069\u006d\u004c\u006f\u0077"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u006c\u0069\u006d\u004c\u006f\u0077"}:_gccbf .OMathMathElementsChoice =NewEG_OMathMathElementsChoice ();
if _dbbc :=d .DecodeElement (&_gccbf .OMathMathElementsChoice .LimLow ,&_dggde );_dbbc !=nil {return _dbbc ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u006c\u0069\u006d\u0055\u0070\u0070"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u006c\u0069\u006d\u0055\u0070\u0070"}:_gccbf .OMathMathElementsChoice =NewEG_OMathMathElementsChoice ();
if _ecda :=d .DecodeElement (&_gccbf .OMathMathElementsChoice .LimUpp ,&_dggde );_ecda !=nil {return _ecda ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u006d"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u006d"}:_gccbf .OMathMathElementsChoice =NewEG_OMathMathElementsChoice ();
if _bcgcb :=d .DecodeElement (&_gccbf .OMathMathElementsChoice .M ,&_dggde );_bcgcb !=nil {return _bcgcb ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u006e\u0061\u0072\u0079"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u006e\u0061\u0072\u0079"}:_gccbf .OMathMathElementsChoice =NewEG_OMathMathElementsChoice ();
if _abaaad :=d .DecodeElement (&_gccbf .OMathMathElementsChoice .Nary ,&_dggde );_abaaad !=nil {return _abaaad ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0070\u0068\u0061n\u0074"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0070\u0068\u0061n\u0074"}:_gccbf .OMathMathElementsChoice =NewEG_OMathMathElementsChoice ();
if _bcfc :=d .DecodeElement (&_gccbf .OMathMathElementsChoice .Phant ,&_dggde );_bcfc !=nil {return _bcfc ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0072\u0061\u0064"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0072\u0061\u0064"}:_gccbf .OMathMathElementsChoice =NewEG_OMathMathElementsChoice ();
if _gebca :=d .DecodeElement (&_gccbf .OMathMathElementsChoice .Rad ,&_dggde );_gebca !=nil {return _gebca ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0073\u0050\u0072\u0065"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0073\u0050\u0072\u0065"}:_gccbf .OMathMathElementsChoice =NewEG_OMathMathElementsChoice ();
if _adce :=d .DecodeElement (&_gccbf .OMathMathElementsChoice .SPre ,&_dggde );_adce !=nil {return _adce ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0073\u0053\u0075\u0062"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0073\u0053\u0075\u0062"}:_gccbf .OMathMathElementsChoice =NewEG_OMathMathElementsChoice ();
if _fbad :=d .DecodeElement (&_gccbf .OMathMathElementsChoice .SSub ,&_dggde );_fbad !=nil {return _fbad ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0073S\u0075\u0062\u0053\u0075\u0070"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0073S\u0075\u0062\u0053\u0075\u0070"}:_gccbf .OMathMathElementsChoice =NewEG_OMathMathElementsChoice ();
if _fdbbe :=d .DecodeElement (&_gccbf .OMathMathElementsChoice .SSubSup ,&_dggde );_fdbbe !=nil {return _fdbbe ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0073\u0053\u0075\u0070"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0073\u0053\u0075\u0070"}:_gccbf .OMathMathElementsChoice =NewEG_OMathMathElementsChoice ();
if _eabe :=d .DecodeElement (&_gccbf .OMathMathElementsChoice .SSup ,&_dggde );_eabe !=nil {return _eabe ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0072"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0072"}:_gccbf .OMathMathElementsChoice =NewEG_OMathMathElementsChoice ();
if _gffea :=d .DecodeElement (&_gccbf .OMathMathElementsChoice .R ,&_dggde );_gffea !=nil {return _gffea ;};default:_b .Log .Debug ("\u0073\u006b\u0069\u0070\u0070\u0069\u006eg\u0020\u0075\u006es\u0075\u0070\u0070o\u0072\u0074e\u0064\u0020\u0065\u006c\u0065\u006de\u006et \u006f\u006e\u0020\u0045\u0047\u005f\u004f\u004d\u0061\u0074\u0068\u004d\u0061\u0074\u0068\u0045\u006c\u0065\u006d\u0065\u006e\u0074\u0073\u0020\u0025\u0076",_dggde .Name );
if _aedfg :=d .Skip ();_aedfg !=nil {return _aedfg ;};};case _g .EndElement :break _ecgbe ;case _g .CharData :};};return nil ;};func (_dfeg *EG_OMathElements )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {_dfeg .OMathElementsChoice .MarshalXML (e ,_g .StartElement {});
return nil ;};func (_gbae *ST_TopBot )UnmarshalXMLAttr (attr _g .Attr )error {switch attr .Value {case "":*_gbae =0;case "\u0074\u006f\u0070":*_gbae =1;case "\u0062\u006f\u0074":*_gbae =2;};return nil ;};

// Validate validates the CT_XAlign and its children
func (_bedb *CT_XAlign )Validate ()error {return _bedb .ValidateWithPath ("\u0043T\u005f\u0058\u0041\u006c\u0069\u0067n");};func (_fdgd ST_FType )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {return e .EncodeElement (_fdgd .String (),start );
};type CT_LimLow struct{

// Lower-Limit Properties
LimLowPr *CT_LimLowPr ;

// Base
E *CT_OMathArg ;

// Limit
Lim *CT_OMathArg ;};func (_cb *CT_Bar )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {e .EncodeToken (start );if _cb .BarPr !=nil {_ae :=_g .StartElement {Name :_g .Name {Local :"\u006d:\u0062\u0061\u0072\u0050\u0072"}};e .EncodeElement (_cb .BarPr ,_ae );
};_ef :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0065"}};e .EncodeElement (_cb .E ,_ef );e .EncodeToken (_g .EndElement {Name :start .Name });return nil ;};

// ValidateWithPath validates the CT_Box and its children, prefixing error messages with path
func (_bgd *CT_Box )ValidateWithPath (path string )error {if _bgd .BoxPr !=nil {if _gff :=_bgd .BoxPr .ValidateWithPath (path +"\u002f\u0042\u006f\u0078\u0050\u0072");_gff !=nil {return _gff ;};};if _egfc :=_bgd .E .ValidateWithPath (path +"\u002f\u0045");
_egfc !=nil {return _egfc ;};return nil ;};type CT_MPr struct{

// Matrix Base Justification
BaseJc *CT_YAlign ;

// Hide Placeholders (Matrix)
PlcHide *CT_OnOff ;

// Row Spacing Rule
RSpRule *CT_SpacingRule ;

// Matrix Column Gap Rule
CGpRule *CT_SpacingRule ;

// Row Spacing (Matrix)
RSp *CT_UnSignedInteger ;

// Minimum Matrix Column Width
CSp *CT_UnSignedInteger ;

// Matrix Column Gap
CGp *CT_UnSignedInteger ;

// Matrix Columns
Mcs *CT_MCS ;CtrlPr *CT_CtrlPr ;};

// Validate validates the CT_MC and its children
func (_bfab *CT_MC )Validate ()error {return _bfab .ValidateWithPath ("\u0043\u0054\u005fM\u0043")};

// ValidateWithPath validates the CT_MR and its children, prefixing error messages with path
func (_fgf *CT_MR )ValidateWithPath (path string )error {for _gaa ,_geebf :=range _fgf .E {if _dbdf :=_geebf .ValidateWithPath (_a .Sprintf ("\u0025\u0073\u002f\u0045\u005b\u0025\u0064\u005d",path ,_gaa ));_dbdf !=nil {return _dbdf ;};};return nil ;};type CT_YAlign struct{

// Value
ValAttr _ab .ST_YAlign ;};const (ST_FTypeUnset ST_FType =0;ST_FTypeBar ST_FType =1;ST_FTypeSkw ST_FType =2;ST_FTypeLin ST_FType =3;ST_FTypeNoBar ST_FType =4;);type ST_LimLoc byte ;

// Validate validates the CT_Script and its children
func (_aefb *CT_Script )Validate ()error {return _aefb .ValidateWithPath ("\u0043T\u005f\u0053\u0063\u0072\u0069\u0070t");};func (_fcce ST_BreakBin )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {return e .EncodeElement (_fcce .String (),start );
};

// Validate validates the CT_BorderBox and its children
func (_cd *CT_BorderBox )Validate ()error {return _cd .ValidateWithPath ("\u0043\u0054\u005fB\u006f\u0072\u0064\u0065\u0072\u0042\u006f\u0078");};type CT_AccPr struct{

// Character
Chr *CT_Char ;

// Control Properties
CtrlPr *CT_CtrlPr ;};type EG_OMathElements struct{OMathElementsChoice *EG_OMathElementsChoice ;};type CT_SSupPr struct{CtrlPr *CT_CtrlPr ;};type EG_OMathMathElementsChoice struct{

// Accent
Acc *CT_Acc ;

// Bar
Bar *CT_Bar ;

// Box Object
Box *CT_Box ;

// Border-Box Object
BorderBox *CT_BorderBox ;

// Delimiter Object
D *CT_D ;

// Array Object
EqArr *CT_EqArr ;

// Fraction Object
F *CT_F ;

// Function Apply Object
Func *CT_Func ;

// Group-Character Object
GroupChr *CT_GroupChr ;

// Lower-Limit Object
LimLow *CT_LimLow ;

// Upper-Limit Object
LimUpp *CT_LimUpp ;

// Matrix Object
M *CT_M ;

// n-ary Operator Object
Nary *CT_Nary ;

// Phantom Object
Phant *CT_Phant ;

// Radical Object
Rad *CT_Rad ;

// Pre-Sub-Superscript Object
SPre *CT_SPre ;

// Subscript Object
SSub *CT_SSub ;

// Sub-Superscript Object
SSubSup *CT_SSubSup ;

// Superscript Object
SSup *CT_SSup ;

// Run
R *CT_R ;};func (_eecd *CT_EqArr )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {_ega :for {_eca ,_fac :=d .Token ();if _fac !=nil {return _fac ;};switch _adf :=_eca .(type ){case _g .StartElement :switch _adf .Name {case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0065q\u0041\u0072\u0072\u0050\u0072"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0065q\u0041\u0072\u0072\u0050\u0072"}:_eecd .EqArrPr =NewCT_EqArrPr ();
if _dgc :=d .DecodeElement (_eecd .EqArrPr ,&_adf );_dgc !=nil {return _dgc ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0065"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0065"}:_gegg :=NewCT_OMathArg ();
if _ced :=d .DecodeElement (_gegg ,&_adf );_ced !=nil {return _ced ;};_eecd .E =append (_eecd .E ,_gegg );default:_b .Log .Debug ("\u0073\u006b\u0069\u0070\u0070\u0069\u006eg\u0020\u0075\u006es\u0075\u0070\u0070\u006fr\u0074\u0065\u0064\u0020\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0043\u0054\u005f\u0045\u0071\u0041\u0072\u0072\u0020\u0025\u0076",_adf .Name );
if _gffcb :=d .Skip ();_gffcb !=nil {return _gffcb ;};};case _g .EndElement :break _ega ;case _g .CharData :};};return nil ;};func NewMathPr ()*MathPr {_ddae :=&MathPr {};_ddae .CT_MathPr =*NewCT_MathPr ();return _ddae };func (_ecdd *CT_SSubSup )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {_ecdd .E =NewCT_OMathArg ();
_ecdd .Sub =NewCT_OMathArg ();_ecdd .Sup =NewCT_OMathArg ();_bbge :for {_fgba ,_gccb :=d .Token ();if _gccb !=nil {return _gccb ;};switch _cdge :=_fgba .(type ){case _g .StartElement :switch _cdge .Name {case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0073S\u0075\u0062\u0053\u0075\u0070\u0050r"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0073S\u0075\u0062\u0053\u0075\u0070\u0050r"}:_ecdd .SSubSupPr =NewCT_SSubSupPr ();
if _ebce :=d .DecodeElement (_ecdd .SSubSupPr ,&_cdge );_ebce !=nil {return _ebce ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0065"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0065"}:if _adfaa :=d .DecodeElement (_ecdd .E ,&_cdge );
_adfaa !=nil {return _adfaa ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0073\u0075\u0062"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0073\u0075\u0062"}:if _fdba :=d .DecodeElement (_ecdd .Sub ,&_cdge );
_fdba !=nil {return _fdba ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0073\u0075\u0070"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0073\u0075\u0070"}:if _dbe :=d .DecodeElement (_ecdd .Sup ,&_cdge );
_dbe !=nil {return _dbe ;};default:_b .Log .Debug ("\u0073k\u0069\u0070p\u0069\u006e\u0067 \u0075\u006e\u0073\u0075\u0070\u0070\u006fr\u0074\u0065\u0064\u0020\u0065\u006ce\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0043\u0054\u005fS\u0053\u0075\u0062\u0053\u0075\u0070\u0020\u0025\u0076",_cdge .Name );
if _abcdg :=d .Skip ();_abcdg !=nil {return _abcdg ;};};case _g .EndElement :break _bbge ;case _g .CharData :};};return nil ;};

// ValidateWithPath validates the CT_MCPr and its children, prefixing error messages with path
func (_edab *CT_MCPr )ValidateWithPath (path string )error {if _edab .Count !=nil {if _bgbc :=_edab .Count .ValidateWithPath (path +"\u002f\u0043\u006f\u0075\u006e\u0074");_bgbc !=nil {return _bgbc ;};};if _edab .McJc !=nil {if _gabf :=_edab .McJc .ValidateWithPath (path +"\u002f\u004d\u0063J\u0063");
_gabf !=nil {return _gabf ;};};return nil ;};func (_eaf ST_BreakBinSub )MarshalXMLAttr (name _g .Name )(_g .Attr ,error ){_dbef :=_g .Attr {};_dbef .Name =name ;switch _eaf {case ST_BreakBinSubUnset :_dbef .Value ="";case ST_BreakBinSub__ :_dbef .Value ="\u002d\u002d";
case ST_BreakBinSub___ :_dbef .Value ="\u002d\u002b";case ST_BreakBinSub____ :_dbef .Value ="\u002b\u002d";};return _dbef ,nil ;};func (_dbff *CT_UnSignedInteger )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {start .Attr =append (start .Attr ,_g .Attr {Name :_g .Name {Local :"\u006d\u003a\u0076a\u006c"},Value :_a .Sprintf ("\u0025\u0076",_dbff .ValAttr )});
e .EncodeToken (start );e .EncodeToken (_g .EndElement {Name :start .Name });return nil ;};type CT_OMathParaPr struct{

// Justification
Jc *CT_OMathJc ;};

// Validate validates the CT_Shp and its children
func (_bceg *CT_Shp )Validate ()error {return _bceg .ValidateWithPath ("\u0043\u0054\u005f\u0053\u0068\u0070");};func (_dccf *CT_TwipsMeasure )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {start .Attr =append (start .Attr ,_g .Attr {Name :_g .Name {Local :"\u006d\u003a\u0076a\u006c"},Value :_a .Sprintf ("\u0025\u0076",_dccf .ValAttr )});
e .EncodeToken (start );e .EncodeToken (_g .EndElement {Name :start .Name });return nil ;};func (_ebcfa *CT_Script )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {if _ebcfa .ValAttr !=ST_ScriptUnset {_dfaf ,_fedad :=_ebcfa .ValAttr .MarshalXMLAttr (_g .Name {Local :"\u006d\u003a\u0076a\u006c"});
if _fedad !=nil {return _fedad ;};start .Attr =append (start .Attr ,_dfaf );};e .EncodeToken (start );e .EncodeToken (_g .EndElement {Name :start .Name });return nil ;};type CT_BorderBox struct{

// Border-Box Properties
BorderBoxPr *CT_BorderBoxPr ;

// Base
E *CT_OMathArg ;};func (_feff *CT_MR )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {e .EncodeToken (start );_aeaf :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0065"}};for _ ,_bdde :=range _feff .E {e .EncodeElement (_bdde ,_aeaf );
};e .EncodeToken (_g .EndElement {Name :start .Name });return nil ;};func (_bcbac ST_Jc )String ()string {switch _bcbac {case 0:return "";case 1:return "\u006c\u0065\u0066\u0074";case 2:return "\u0072\u0069\u0067h\u0074";case 3:return "\u0063\u0065\u006e\u0074\u0065\u0072";
case 4:return "c\u0065\u006e\u0074\u0065\u0072\u0047\u0072\u006f\u0075\u0070";};return "";};

// ValidateWithPath validates the CT_LimUppPr and its children, prefixing error messages with path
func (_fdee *CT_LimUppPr )ValidateWithPath (path string )error {if _fdee .CtrlPr !=nil {if _ffaa :=_fdee .CtrlPr .ValidateWithPath (path +"\u002fC\u0074\u0072\u006c\u0050\u0072");_ffaa !=nil {return _ffaa ;};};return nil ;};func (_eff *CT_Nary )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {_eff .Sub =NewCT_OMathArg ();
_eff .Sup =NewCT_OMathArg ();_eff .E =NewCT_OMathArg ();_abff :for {_cdda ,_cbaf :=d .Token ();if _cbaf !=nil {return _cbaf ;};switch _egec :=_cdda .(type ){case _g .StartElement :switch _egec .Name {case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u006e\u0061\u0072\u0079\u0050\u0072"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u006e\u0061\u0072\u0079\u0050\u0072"}:_eff .NaryPr =NewCT_NaryPr ();
if _fgb :=d .DecodeElement (_eff .NaryPr ,&_egec );_fgb !=nil {return _fgb ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0073\u0075\u0062"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0073\u0075\u0062"}:if _gdef :=d .DecodeElement (_eff .Sub ,&_egec );
_gdef !=nil {return _gdef ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0073\u0075\u0070"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0073\u0075\u0070"}:if _ceg :=d .DecodeElement (_eff .Sup ,&_egec );
_ceg !=nil {return _ceg ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0065"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0065"}:if _bgcb :=d .DecodeElement (_eff .E ,&_egec );
_bgcb !=nil {return _bgcb ;};default:_b .Log .Debug ("\u0073\u006b\u0069p\u0070\u0069\u006e\u0067\u0020\u0075\u006e\u0073\u0075\u0070\u0070\u006f\u0072\u0074\u0065\u0064\u0020\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0043T\u005f\u004e\u0061\u0072\u0079\u0020\u0025\u0076",_egec .Name );
if _agge :=d .Skip ();_agge !=nil {return _agge ;};};case _g .EndElement :break _abff ;case _g .CharData :};};return nil ;};

// Validate validates the EG_OMathElements and its children
func (_cecc *EG_OMathElements )Validate ()error {return _cecc .ValidateWithPath ("\u0045\u0047_\u004f\u004d\u0061t\u0068\u0045\u006c\u0065\u006d\u0065\u006e\u0074\u0073");};

// ValidateWithPath validates the CT_EqArrPr and its children, prefixing error messages with path
func (_cdb *CT_EqArrPr )ValidateWithPath (path string )error {if _cdb .BaseJc !=nil {if _ccd :=_cdb .BaseJc .ValidateWithPath (path +"\u002fB\u0061\u0073\u0065\u004a\u0063");_ccd !=nil {return _ccd ;};};if _cdb .MaxDist !=nil {if _cgda :=_cdb .MaxDist .ValidateWithPath (path +"\u002f\u004d\u0061\u0078\u0044\u0069\u0073\u0074");
_cgda !=nil {return _cgda ;};};if _cdb .ObjDist !=nil {if _gcad :=_cdb .ObjDist .ValidateWithPath (path +"\u002f\u004f\u0062\u006a\u0044\u0069\u0073\u0074");_gcad !=nil {return _gcad ;};};if _cdb .RSpRule !=nil {if _bgdd :=_cdb .RSpRule .ValidateWithPath (path +"\u002f\u0052\u0053\u0070\u0052\u0075\u006c\u0065");
_bgdd !=nil {return _bgdd ;};};if _cdb .RSp !=nil {if _dcecd :=_cdb .RSp .ValidateWithPath (path +"\u002f\u0052\u0053\u0070");_dcecd !=nil {return _dcecd ;};};if _cdb .CtrlPr !=nil {if _gfed :=_cdb .CtrlPr .ValidateWithPath (path +"\u002fC\u0074\u0072\u006c\u0050\u0072");
_gfed !=nil {return _gfed ;};};return nil ;};

// Validate validates the CT_Phant and its children
func (_fbae *CT_Phant )Validate ()error {return _fbae .ValidateWithPath ("\u0043\u0054\u005f\u0050\u0068\u0061\u006e\u0074");};

// ValidateWithPath validates the EG_ScriptStyle and its children, prefixing error messages with path
func (_aefbc *EG_ScriptStyle )ValidateWithPath (path string )error {if _aefbc .Scr !=nil {if _dbbgff :=_aefbc .Scr .ValidateWithPath (path +"\u002f\u0053\u0063\u0072");_dbbgff !=nil {return _dbbgff ;};};if _aefbc .Sty !=nil {if _fcdc :=_aefbc .Sty .ValidateWithPath (path +"\u002f\u0053\u0074\u0079");
_fcdc !=nil {return _fcdc ;};};return nil ;};

// Validate validates the CT_SSup and its children
func (_edea *CT_SSup )Validate ()error {return _edea .ValidateWithPath ("\u0043T\u005f\u0053\u0053\u0075\u0070");};type CT_Text struct{SpaceAttr *string ;Content string ;};

// Validate validates the CT_SSubSupPr and its children
func (_baff *CT_SSubSupPr )Validate ()error {return _baff .ValidateWithPath ("\u0043\u0054\u005fS\u0053\u0075\u0062\u0053\u0075\u0070\u0050\u0072");};

// ValidateWithPath validates the OMathPara and its children, prefixing error messages with path
func (_dbgc *OMathPara )ValidateWithPath (path string )error {if _cdgb :=_dbgc .CT_OMathPara .ValidateWithPath (path );_cdgb !=nil {return _cdgb ;};return nil ;};type CT_OMathArgPr struct{

// Argument Size
ArgSz *CT_Integer2 ;};

// ValidateWithPath validates the CT_MPr and its children, prefixing error messages with path
func (_gce *CT_MPr )ValidateWithPath (path string )error {if _gce .BaseJc !=nil {if _dbaa :=_gce .BaseJc .ValidateWithPath (path +"\u002fB\u0061\u0073\u0065\u004a\u0063");_dbaa !=nil {return _dbaa ;};};if _gce .PlcHide !=nil {if _fcc :=_gce .PlcHide .ValidateWithPath (path +"\u002f\u0050\u006c\u0063\u0048\u0069\u0064\u0065");
_fcc !=nil {return _fcc ;};};if _gce .RSpRule !=nil {if _cad :=_gce .RSpRule .ValidateWithPath (path +"\u002f\u0052\u0053\u0070\u0052\u0075\u006c\u0065");_cad !=nil {return _cad ;};};if _gce .CGpRule !=nil {if _aca :=_gce .CGpRule .ValidateWithPath (path +"\u002f\u0043\u0047\u0070\u0052\u0075\u006c\u0065");
_aca !=nil {return _aca ;};};if _gce .RSp !=nil {if _debcf :=_gce .RSp .ValidateWithPath (path +"\u002f\u0052\u0053\u0070");_debcf !=nil {return _debcf ;};};if _gce .CSp !=nil {if _feda :=_gce .CSp .ValidateWithPath (path +"\u002f\u0043\u0053\u0070");_feda !=nil {return _feda ;
};};if _gce .CGp !=nil {if _gde :=_gce .CGp .ValidateWithPath (path +"\u002f\u0043\u0047\u0070");_gde !=nil {return _gde ;};};if _gce .Mcs !=nil {if _afcb :=_gce .Mcs .ValidateWithPath (path +"\u002f\u004d\u0063\u0073");_afcb !=nil {return _afcb ;};};if _gce .CtrlPr !=nil {if _bbaa :=_gce .CtrlPr .ValidateWithPath (path +"\u002fC\u0074\u0072\u006c\u0050\u0072");
_bbaa !=nil {return _bbaa ;};};return nil ;};

// Validate validates the CT_MCPr and its children
func (_ccbea *CT_MCPr )Validate ()error {return _ccbea .ValidateWithPath ("\u0043T\u005f\u004d\u0043\u0050\u0072");};

// Validate validates the CT_CtrlPr and its children
func (_dbcd *CT_CtrlPr )Validate ()error {return _dbcd .ValidateWithPath ("\u0043T\u005f\u0043\u0074\u0072\u006c\u0050r");};type CT_Acc struct{

// Accent Properties
AccPr *CT_AccPr ;

// Base
E *CT_OMathArg ;};

// ValidateWithPath validates the CT_Style and its children, prefixing error messages with path
func (_cgdc *CT_Style )ValidateWithPath (path string )error {if _caae :=_cgdc .ValAttr .ValidateWithPath (path +"\u002f\u0056\u0061\u006c\u0041\u0074\u0074\u0072");_caae !=nil {return _caae ;};return nil ;};type CT_SpacingRule struct{

// Value
ValAttr int64 ;};func NewCT_GroupChr ()*CT_GroupChr {_bccec :=&CT_GroupChr {};_bccec .E =NewCT_OMathArg ();return _bccec ;};

// Validate validates the CT_DPr and its children
func (_cdgc *CT_DPr )Validate ()error {return _cdgc .ValidateWithPath ("\u0043\u0054\u005f\u0044\u0050\u0072");};

// ValidateWithPath validates the OMath and its children, prefixing error messages with path
func (_bgfe *OMath )ValidateWithPath (path string )error {if _fafbd :=_bgfe .CT_OMath .ValidateWithPath (path );_fafbd !=nil {return _fafbd ;};return nil ;};type OMath struct{CT_OMath };

// Validate validates the CT_Bar and its children
func (_gfd *CT_Bar )Validate ()error {return _gfd .ValidateWithPath ("\u0043\u0054\u005f\u0042\u0061\u0072");};func NewCT_TopBot ()*CT_TopBot {_fbaa :=&CT_TopBot {};_fbaa .ValAttr =ST_TopBot (1);return _fbaa };func (_eccg *CT_RadPr )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {_gbcb :for {_dfeb ,_fgdb :=d .Token ();
if _fgdb !=nil {return _fgdb ;};switch _feg :=_dfeb .(type ){case _g .StartElement :switch _feg .Name {case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0064e\u0067\u0048\u0069\u0064\u0065"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0064e\u0067\u0048\u0069\u0064\u0065"}:_eccg .DegHide =NewCT_OnOff ();
if _cggd :=d .DecodeElement (_eccg .DegHide ,&_feg );_cggd !=nil {return _cggd ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0063\u0074\u0072\u006c\u0050\u0072"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0063\u0074\u0072\u006c\u0050\u0072"}:_eccg .CtrlPr =NewCT_CtrlPr ();
if _adg :=d .DecodeElement (_eccg .CtrlPr ,&_feg );_adg !=nil {return _adg ;};default:_b .Log .Debug ("\u0073\u006b\u0069\u0070\u0070\u0069\u006eg\u0020\u0075\u006es\u0075\u0070\u0070\u006fr\u0074\u0065\u0064\u0020\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0043\u0054\u005f\u0052\u0061\u0064\u0050\u0072\u0020\u0025\u0076",_feg .Name );
if _fcbb :=d .Skip ();_fcbb !=nil {return _fcbb ;};};case _g .EndElement :break _gbcb ;case _g .CharData :};};return nil ;};type CT_R struct{

// Run Properties
RPr *CT_RPR ;RChoice []*CT_RChoice ;};

// Validate validates the CT_Style and its children
func (_dcda *CT_Style )Validate ()error {return _dcda .ValidateWithPath ("\u0043\u0054\u005f\u0053\u0074\u0079\u006c\u0065");};func (_bfbgc *ST_LimLoc )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {_cabe ,_dcgf :=d .Token ();if _dcgf !=nil {return _dcgf ;
};if _ccgef ,_bccdg :=_cabe .(_g .EndElement );_bccdg &&_ccgef .Name ==start .Name {*_bfbgc =1;return nil ;};if _gbac ,_cbcdg :=_cabe .(_g .CharData );!_cbcdg {return _a .Errorf ("\u0065\u0078\u0070\u0065\u0063\u0074\u0065\u0064\u0020\u0063\u0068a\u0072\u0020\u0064\u0061\u0074\u0061\u002c\u0020\u0067\u006ft\u0020\u0025\u0054",_cabe );
}else {switch string (_gbac ){case "":*_bfbgc =0;case "\u0075\u006e\u0064\u004f\u0076\u0072":*_bfbgc =1;case "\u0073\u0075\u0062\u0053\u0075\u0070":*_bfbgc =2;};};_cabe ,_dcgf =d .Token ();if _dcgf !=nil {return _dcgf ;};if _baabg ,_cgfa :=_cabe .(_g .EndElement );
_cgfa &&_baabg .Name ==start .Name {return nil ;};return _a .Errorf ("\u0065\u0078\u0070\u0065c\u0074\u0065\u0064\u0020\u0065\u006e\u0064\u0020\u0065\u006ce\u006de\u006e\u0074\u002c\u0020\u0067\u006f\u0074 \u0025\u0076",_cabe );};func (_afeb *CT_FuncPr )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {_gagc :for {_abe ,_bgcc :=d .Token ();
if _bgcc !=nil {return _bgcc ;};switch _gddg :=_abe .(type ){case _g .StartElement :switch _gddg .Name {case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0063\u0074\u0072\u006c\u0050\u0072"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0063\u0074\u0072\u006c\u0050\u0072"}:_afeb .CtrlPr =NewCT_CtrlPr ();
if _bgeg :=d .DecodeElement (_afeb .CtrlPr ,&_gddg );_bgeg !=nil {return _bgeg ;};default:_b .Log .Debug ("\u0073k\u0069\u0070p\u0069\u006e\u0067\u0020u\u006e\u0073\u0075p\u0070\u006f\u0072\u0074\u0065\u0064\u0020\u0065\u006cem\u0065\u006e\u0074 \u006f\u006e \u0043\u0054\u005f\u0046\u0075\u006ec\u0050\u0072 \u0025\u0076",_gddg .Name );
if _cfg :=d .Skip ();_cfg !=nil {return _cfg ;};};case _g .EndElement :break _gagc ;case _g .CharData :};};return nil ;};

// ValidateWithPath validates the CT_Rad and its children, prefixing error messages with path
func (_deeg *CT_Rad )ValidateWithPath (path string )error {if _deeg .RadPr !=nil {if _fgdfgc :=_deeg .RadPr .ValidateWithPath (path +"\u002f\u0052\u0061\u0064\u0050\u0072");_fgdfgc !=nil {return _fgdfgc ;};};if _dbbee :=_deeg .Deg .ValidateWithPath (path +"\u002f\u0044\u0065\u0067");
_dbbee !=nil {return _dbbee ;};if _dbcfg :=_deeg .E .ValidateWithPath (path +"\u002f\u0045");_dbcfg !=nil {return _dbcfg ;};return nil ;};func NewCT_BorderBox ()*CT_BorderBox {_ege :=&CT_BorderBox {};_ege .E =NewCT_OMathArg ();return _ege };

// ValidateWithPath validates the CT_ManualBreak and its children, prefixing error messages with path
func (_ccge *CT_ManualBreak )ValidateWithPath (path string )error {if _ccge .AlnAtAttr !=nil {if *_ccge .AlnAtAttr < 1{return _a .Errorf ("\u0025\u0073/\u006d\u002e\u0041\u006cn\u0041\u0074A\u0074\u0074\u0072\u0020\u006d\u0075\u0073\u0074 \u0062\u0065\u0020\u003e\u003d\u0020\u0031\u0020\u0028\u0068\u0061\u0076e\u0020\u0025\u0076\u0029",path ,*_ccge .AlnAtAttr );
};if *_ccge .AlnAtAttr > 255{return _a .Errorf ("\u0025\u0073\u002f\u006d\u002e\u0041\u006c\u006e\u0041\u0074\u0041\u0074\u0074r\u0020\u006d\u0075\u0073\u0074\u0020b\u0065\u0020\u003c\u003d\u0020\u0032\u0035\u0035\u0020\u0028\u0068\u0061\u0076e\u0020\u0025\u0076\u0029",path ,*_ccge .AlnAtAttr );
};};return nil ;};

// ValidateWithPath validates the CT_Char and its children, prefixing error messages with path
func (_agb *CT_Char )ValidateWithPath (path string )error {return nil };func (_acdf ST_TopBot )Validate ()error {return _acdf .ValidateWithPath ("")};func (_geebff *CT_SSub )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {e .EncodeToken (start );
if _geebff .SSubPr !=nil {_eaca :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0073\u0053\u0075\u0062\u0050\u0072"}};e .EncodeElement (_geebff .SSubPr ,_eaca );};_afaf :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0065"}};e .EncodeElement (_geebff .E ,_afaf );
_cde :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0073u\u0062"}};e .EncodeElement (_geebff .Sub ,_cde );e .EncodeToken (_g .EndElement {Name :start .Name });return nil ;};func NewCT_EqArrPr ()*CT_EqArrPr {_cba :=&CT_EqArrPr {};return _cba };
func (_aabed *CT_Text )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {for _ ,_bfecg :=range start .Attr {if _bfecg .Name .Space =="\u0068\u0074tp\u003a\u002f\u002fw\u0077\u0077\u002e\u00773.o\u0072g/\u0058\u004d\u004c\u002f\u0031\u0039\u00398/\u006e\u0061\u006d\u0065\u0073\u0070\u0061c\u0065"&&_bfecg .Name .Local =="\u0073\u0070\u0061c\u0065"{_eedd :=_bfecg .Value ;
_aabed .SpaceAttr =&_eedd ;continue ;};};for {_fgfe ,_affd :=d .Token ();if _affd !=nil {return _a .Errorf ("\u0070\u0061\u0072\u0073in\u0067\u0020\u0043\u0054\u005f\u0054\u0065\u0078\u0074\u003a\u0020\u0025\u0073",_affd );};if _egae ,_dacf :=_fgfe .(_g .CharData );
_dacf {_aabed .Content =string (_egae );};if _acfc ,_cbcc :=_fgfe .(_g .EndElement );_cbcc &&_acfc .Name ==start .Name {break ;};};return nil ;};func (_eabf *CT_TopBot )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {_eabf .ValAttr =ST_TopBot (1);
for _ ,_agag :=range start .Attr {if _agag .Name .Local =="\u0076\u0061\u006c"{_eabf .ValAttr .UnmarshalXMLAttr (_agag );continue ;};};for {_bddb ,_dfdeg :=d .Token ();if _dfdeg !=nil {return _a .Errorf ("p\u0061\u0072\u0073\u0069ng\u0020C\u0054\u005f\u0054\u006f\u0070B\u006f\u0074\u003a\u0020\u0025\u0073",_dfdeg );
};if _geac ,_dbac :=_bddb .(_g .EndElement );_dbac &&_geac .Name ==start .Name {break ;};};return nil ;};func (_gaac ST_BreakBin )Validate ()error {return _gaac .ValidateWithPath ("")};func (_aa *CT_Bar )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {_aa .E =NewCT_OMathArg ();
_bed :for {_db ,_ca :=d .Token ();if _ca !=nil {return _ca ;};switch _bb :=_db .(type ){case _g .StartElement :switch _bb .Name {case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0062\u0061\u0072P\u0072"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0062\u0061\u0072P\u0072"}:_aa .BarPr =NewCT_BarPr ();
if _ee :=d .DecodeElement (_aa .BarPr ,&_bb );_ee !=nil {return _ee ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0065"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0065"}:if _cfa :=d .DecodeElement (_aa .E ,&_bb );
_cfa !=nil {return _cfa ;};default:_b .Log .Debug ("\u0073\u006b\u0069\u0070\u0070i\u006e\u0067\u0020\u0075\u006e\u0073\u0075\u0070\u0070\u006f\u0072\u0074\u0065d\u0020\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0043\u0054\u005f\u0042\u0061\u0072\u0020\u0025\u0076",_bb .Name );
if _dde :=d .Skip ();_dde !=nil {return _dde ;};};case _g .EndElement :break _bed ;case _g .CharData :};};return nil ;};func (_bdge *CT_OMathJc )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {if _bdge .ValAttr !=ST_JcUnset {_ddab ,_baaee :=_bdge .ValAttr .MarshalXMLAttr (_g .Name {Local :"\u006d\u003a\u0076a\u006c"});
if _baaee !=nil {return _baaee ;};start .Attr =append (start .Attr ,_ddab );};e .EncodeToken (start );e .EncodeToken (_g .EndElement {Name :start .Name });return nil ;};func NewCT_Char ()*CT_Char {_cbg :=&CT_Char {};return _cbg };func NewEG_OMathMathElements ()*EG_OMathMathElements {_gcgggd :=&EG_OMathMathElements {};
_gcgggd .OMathMathElementsChoice =NewEG_OMathMathElementsChoice ();return _gcgggd ;};

// Validate validates the EG_ScriptStyle and its children
func (_dgebf *EG_ScriptStyle )Validate ()error {return _dgebf .ValidateWithPath ("\u0045\u0047\u005f\u0053\u0063\u0072\u0069\u0070\u0074S\u0074\u0079\u006c\u0065");};

// ValidateWithPath validates the CT_Nary and its children, prefixing error messages with path
func (_fag *CT_Nary )ValidateWithPath (path string )error {if _fag .NaryPr !=nil {if _bcdg :=_fag .NaryPr .ValidateWithPath (path +"\u002fN\u0061\u0072\u0079\u0050\u0072");_bcdg !=nil {return _bcdg ;};};if _baae :=_fag .Sub .ValidateWithPath (path +"\u002f\u0053\u0075\u0062");
_baae !=nil {return _baae ;};if _dfbab :=_fag .Sup .ValidateWithPath (path +"\u002f\u0053\u0075\u0070");_dfbab !=nil {return _dfbab ;};if _dcd :=_fag .E .ValidateWithPath (path +"\u002f\u0045");_dcd !=nil {return _dcd ;};return nil ;};type CT_BorderBoxPr struct{

// Hide Top Edge
HideTop *CT_OnOff ;

// Hide Bottom Edge
HideBot *CT_OnOff ;

// Hide Left Edge
HideLeft *CT_OnOff ;

// Hide Right Edge
HideRight *CT_OnOff ;

// Border Box Strikethrough Horizontal
StrikeH *CT_OnOff ;

// Border Box Strikethrough Vertical
StrikeV *CT_OnOff ;

// Border Box Strikethrough Bottom-Left to Top-Right
StrikeBLTR *CT_OnOff ;

// Border Box Strikethrough Top-Left to Bottom-Right
StrikeTLBR *CT_OnOff ;CtrlPr *CT_CtrlPr ;};func (_gbbd *OMathPara )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {start .Attr =append (start .Attr ,_g .Attr {Name :_g .Name {Local :"\u0078\u006d\u006cn\u0073"},Value :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068"});
start .Attr =append (start .Attr ,_g .Attr {Name :_g .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u006d"},Value :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068"});
start .Attr =append (start .Attr ,_g .Attr {Name :_g .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0073"},Value :"\u0068\u0074\u0074\u0070\u003a/\u002f\u0073\u0063\u0068\u0065m\u0061s\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067/\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0068\u0061\u0072e\u0064\u0054\u0079\u0070\u0065\u0073"});
start .Attr =append (start .Attr ,_g .Attr {Name :_g .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0077"},Value :"ht\u0074\u0070:\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073.\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0077\u006f\u0072\u0064\u0070\u0072\u006f\u0063\u0065s\u0073i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u00306\u002fm\u0061\u0069n"});
start .Attr =append (start .Attr ,_g .Attr {Name :_g .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0078\u006dl"},Value :"\u0068\u0074tp\u003a\u002f\u002fw\u0077\u0077\u002e\u00773.o\u0072g/\u0058\u004d\u004c\u002f\u0031\u0039\u00398/\u006e\u0061\u006d\u0065\u0073\u0070\u0061c\u0065"});
start .Name .Local ="m\u003a\u006f\u004d\u0061\u0074\u0068\u0050\u0061\u0072\u0061";return _gbbd .CT_OMathPara .MarshalXML (e ,start );};func (_afdd *CT_ManualBreak )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {if _afdd .AlnAtAttr !=nil {start .Attr =append (start .Attr ,_g .Attr {Name :_g .Name {Local :"\u006d:\u0061\u006c\u006e\u0041\u0074"},Value :_a .Sprintf ("\u0025\u0076",*_afdd .AlnAtAttr )});
};e .EncodeToken (start );e .EncodeToken (_g .EndElement {Name :start .Name });return nil ;};func NewCT_AccPr ()*CT_AccPr {_dge :=&CT_AccPr {};return _dge };

// Validate validates the CT_OMathArg and its children
func (_cfc *CT_OMathArg )Validate ()error {return _cfc .ValidateWithPath ("C\u0054\u005f\u004f\u004d\u0061\u0074\u0068\u0041\u0072\u0067");};func (_bfa *CT_Func )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {e .EncodeToken (start );if _bfa .FuncPr !=nil {_gfg :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0066\u0075\u006e\u0063\u0050\u0072"}};
e .EncodeElement (_bfa .FuncPr ,_gfg );};_bfg :=_g .StartElement {Name :_g .Name {Local :"\u006d:\u0066\u004e\u0061\u006d\u0065"}};e .EncodeElement (_bfa .FName ,_bfg );_ece :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0065"}};e .EncodeElement (_bfa .E ,_ece );
e .EncodeToken (_g .EndElement {Name :start .Name });return nil ;};func (_aedbg ST_LimLoc )Validate ()error {return _aedbg .ValidateWithPath ("")};func (_adeb *CT_SSupPr )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {e .EncodeToken (start );
if _adeb .CtrlPr !=nil {_bega :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0063\u0074\u0072\u006c\u0050\u0072"}};e .EncodeElement (_adeb .CtrlPr ,_bega );};e .EncodeToken (_g .EndElement {Name :start .Name });return nil ;};func (_fgcg *CT_OMathArgPr )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {e .EncodeToken (start );
if _fgcg .ArgSz !=nil {_abed :=_g .StartElement {Name :_g .Name {Local :"\u006d:\u0061\u0072\u0067\u0053\u007a"}};e .EncodeElement (_fgcg .ArgSz ,_abed );};e .EncodeToken (_g .EndElement {Name :start .Name });return nil ;};const (ST_ScriptUnset ST_Script =0;
ST_ScriptRoman ST_Script =1;ST_ScriptScript ST_Script =2;ST_ScriptFraktur ST_Script =3;ST_ScriptDouble_struck ST_Script =4;ST_ScriptSans_serif ST_Script =5;ST_ScriptMonospace ST_Script =6;);

// Validate validates the CT_FType and its children
func (_gdc *CT_FType )Validate ()error {return _gdc .ValidateWithPath ("\u0043\u0054\u005f\u0046\u0054\u0079\u0070\u0065");};

// ValidateWithPath validates the CT_FuncPr and its children, prefixing error messages with path
func (_dfcg *CT_FuncPr )ValidateWithPath (path string )error {if _dfcg .CtrlPr !=nil {if _dfgaa :=_dfcg .CtrlPr .ValidateWithPath (path +"\u002fC\u0074\u0072\u006c\u0050\u0072");_dfgaa !=nil {return _dfgaa ;};};return nil ;};

// ValidateWithPath validates the CT_MCS and its children, prefixing error messages with path
func (_bcbc *CT_MCS )ValidateWithPath (path string )error {for _geeb ,_cecf :=range _bcbc .Mc {if _dbbd :=_cecf .ValidateWithPath (_a .Sprintf ("\u0025s\u002f\u004d\u0063\u005b\u0025\u0064]",path ,_geeb ));_dbbd !=nil {return _dbbd ;};};return nil ;};type CT_Integer255 struct{

// Value
ValAttr int64 ;};

// Validate validates the CT_D and its children
func (_aee *CT_D )Validate ()error {return _aee .ValidateWithPath ("\u0043\u0054\u005f\u0044")};func (_cafcd *CT_MC )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {_bdca :for {_cggc ,_gdfe :=d .Token ();if _gdfe !=nil {return _gdfe ;};switch _afggf :=_cggc .(type ){case _g .StartElement :switch _afggf .Name {case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u006d\u0063\u0050\u0072"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u006d\u0063\u0050\u0072"}:_cafcd .McPr =NewCT_MCPr ();
if _ccabe :=d .DecodeElement (_cafcd .McPr ,&_afggf );_ccabe !=nil {return _ccabe ;};default:_b .Log .Debug ("s\u006b\u0069\u0070\u0070\u0069\u006e\u0067\u0020\u0075\u006e\u0073\u0075\u0070\u0070\u006f\u0072\u0074\u0065d\u0020\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006fn \u0043\u0054\u005fM\u0043 \u0025\u0076",_afggf .Name );
if _fcg :=d .Skip ();_fcg !=nil {return _fcg ;};};case _g .EndElement :break _bdca ;case _g .CharData :};};return nil ;};

// Validate validates the EG_OMathElementsChoice and its children
func (_dccbbc *EG_OMathElementsChoice )Validate ()error {return _dccbbc .ValidateWithPath ("\u0045\u0047\u005f\u004fMa\u0074\u0068\u0045\u006c\u0065\u006d\u0065\u006e\u0074\u0073\u0043\u0068\u006f\u0069c\u0065");};

// ValidateWithPath validates the CT_SPre and its children, prefixing error messages with path
func (_deba *CT_SPre )ValidateWithPath (path string )error {if _deba .SPrePr !=nil {if _cgabc :=_deba .SPrePr .ValidateWithPath (path +"\u002fS\u0050\u0072\u0065\u0050\u0072");_cgabc !=nil {return _cgabc ;};};if _fgcfe :=_deba .Sub .ValidateWithPath (path +"\u002f\u0053\u0075\u0062");
_fgcfe !=nil {return _fgcfe ;};if _ccbg :=_deba .Sup .ValidateWithPath (path +"\u002f\u0053\u0075\u0070");_ccbg !=nil {return _ccbg ;};if _gbdag :=_deba .E .ValidateWithPath (path +"\u002f\u0045");_gbdag !=nil {return _gbdag ;};return nil ;};func NewCT_R ()*CT_R {_gcec :=&CT_R {};
return _gcec };

// Validate validates the CT_FPr and its children
func (_gfee *CT_FPr )Validate ()error {return _gfee .ValidateWithPath ("\u0043\u0054\u005f\u0046\u0050\u0072");};func (_edba *CT_String )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {for _ ,_fedc :=range start .Attr {if _fedc .Name .Local =="\u0076\u0061\u006c"{_fbaca :=_fedc .Value ;
_edba .ValAttr =&_fbaca ;continue ;};};for {_adcd ,_ecdfe :=d .Token ();if _ecdfe !=nil {return _a .Errorf ("p\u0061\u0072\u0073\u0069ng\u0020C\u0054\u005f\u0053\u0074\u0072i\u006e\u0067\u003a\u0020\u0025\u0073",_ecdfe );};if _gcgb ,_cbdc :=_adcd .(_g .EndElement );
_cbdc &&_gcgb .Name ==start .Name {break ;};};return nil ;};

// ValidateWithPath validates the CT_RChoice and its children, prefixing error messages with path
func (_geccd *CT_RChoice )ValidateWithPath (path string )error {if _geccd .T !=nil {if _cccf :=_geccd .T .ValidateWithPath (path +"\u002f\u0054");_cccf !=nil {return _cccf ;};};return nil ;};func (_gedb *ST_BreakBinSub )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {_degb ,_caca :=d .Token ();
if _caca !=nil {return _caca ;};if _fdad ,_fgef :=_degb .(_g .EndElement );_fgef &&_fdad .Name ==start .Name {*_gedb =1;return nil ;};if _cedac ,_adaf :=_degb .(_g .CharData );!_adaf {return _a .Errorf ("\u0065\u0078\u0070\u0065\u0063\u0074\u0065\u0064\u0020\u0063\u0068a\u0072\u0020\u0064\u0061\u0074\u0061\u002c\u0020\u0067\u006ft\u0020\u0025\u0054",_degb );
}else {switch string (_cedac ){case "":*_gedb =0;case "\u002d\u002d":*_gedb =1;case "\u002d\u002b":*_gedb =2;case "\u002b\u002d":*_gedb =3;};};_degb ,_caca =d .Token ();if _caca !=nil {return _caca ;};if _eagc ,_afbe :=_degb .(_g .EndElement );_afbe &&_eagc .Name ==start .Name {return nil ;
};return _a .Errorf ("\u0065\u0078\u0070\u0065c\u0074\u0065\u0064\u0020\u0065\u006e\u0064\u0020\u0065\u006ce\u006de\u006e\u0074\u002c\u0020\u0067\u006f\u0074 \u0025\u0076",_degb );};func (_cgb *CT_DPr )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {e .EncodeToken (start );
if _cgb .BegChr !=nil {_egg :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0062\u0065\u0067\u0043\u0068\u0072"}};e .EncodeElement (_cgb .BegChr ,_egg );};if _cgb .SepChr !=nil {_bdce :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0073\u0065\u0070\u0043\u0068\u0072"}};
e .EncodeElement (_cgb .SepChr ,_bdce );};if _cgb .EndChr !=nil {_fcb :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0065\u006e\u0064\u0043\u0068\u0072"}};e .EncodeElement (_cgb .EndChr ,_fcb );};if _cgb .Grow !=nil {_cfb :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0067\u0072\u006f\u0077"}};
e .EncodeElement (_cgb .Grow ,_cfb );};if _cgb .Shp !=nil {_dac :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0073h\u0070"}};e .EncodeElement (_cgb .Shp ,_dac );};if _cgb .CtrlPr !=nil {_fbee :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0063\u0074\u0072\u006c\u0050\u0072"}};
e .EncodeElement (_cgb .CtrlPr ,_fbee );};e .EncodeToken (_g .EndElement {Name :start .Name });return nil ;};

// Validate validates the CT_BreakBin and its children
func (_fbef *CT_BreakBin )Validate ()error {return _fbef .ValidateWithPath ("C\u0054\u005f\u0042\u0072\u0065\u0061\u006b\u0042\u0069\u006e");};func (_ceea *CT_RPR )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {e .EncodeToken (start );if _ceea .Lit !=nil {_cfab :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u006ci\u0074"}};
e .EncodeElement (_ceea .Lit ,_cfab );};if _ceea .Nor !=nil {_aabea :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u006eo\u0072"}};e .EncodeElement (_ceea .Nor ,_aabea );};if _ceea .Brk !=nil {_babd :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0062r\u006b"}};
e .EncodeElement (_ceea .Brk ,_babd );};if _ceea .Aln !=nil {_agba :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0061l\u006e"}};e .EncodeElement (_ceea .Aln ,_agba );};e .EncodeToken (_g .EndElement {Name :start .Name });return nil ;};func (_efda *CT_D )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {_gffe :for {_bcd ,_cfde :=d .Token ();
if _cfde !=nil {return _cfde ;};switch _cgac :=_bcd .(type ){case _g .StartElement :switch _cgac .Name {case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0064\u0050\u0072"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0064\u0050\u0072"}:_efda .DPr =NewCT_DPr ();
if _aaa :=d .DecodeElement (_efda .DPr ,&_cgac );_aaa !=nil {return _aaa ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0065"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0065"}:_egc :=NewCT_OMathArg ();
if _bdf :=d .DecodeElement (_egc ,&_cgac );_bdf !=nil {return _bdf ;};_efda .E =append (_efda .E ,_egc );default:_b .Log .Debug ("\u0073\u006b\u0069\u0070\u0070\u0069\u006e\u0067\u0020\u0075\u006e\u0073\u0075p\u0070\u006f\u0072\u0074\u0065\u0064 \u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0043\u0054_\u0044\u0020\u0025\u0076",_cgac .Name );
if _ada :=d .Skip ();_ada !=nil {return _ada ;};};case _g .EndElement :break _gffe ;case _g .CharData :};};return nil ;};

// ValidateWithPath validates the CT_OnOff and its children, prefixing error messages with path
func (_agdg *CT_OnOff )ValidateWithPath (path string )error {if _agdg .ValAttr !=nil {if _fae :=_agdg .ValAttr .ValidateWithPath (path +"\u002f\u0056\u0061\u006c\u0041\u0074\u0074\u0072");_fae !=nil {return _fae ;};};return nil ;};const (ST_BreakBinSubUnset ST_BreakBinSub =0;
ST_BreakBinSub__ ST_BreakBinSub =1;ST_BreakBinSub___ ST_BreakBinSub =2;ST_BreakBinSub____ ST_BreakBinSub =3;);

// ValidateWithPath validates the CT_SSup and its children, prefixing error messages with path
func (_edde *CT_SSup )ValidateWithPath (path string )error {if _edde .SSupPr !=nil {if _cgaab :=_edde .SSupPr .ValidateWithPath (path +"\u002fS\u0053\u0075\u0070\u0050\u0072");_cgaab !=nil {return _cgaab ;};};if _gedfd :=_edde .E .ValidateWithPath (path +"\u002f\u0045");
_gedfd !=nil {return _gedfd ;};if _facg :=_edde .Sup .ValidateWithPath (path +"\u002f\u0053\u0075\u0070");_facg !=nil {return _facg ;};return nil ;};func (_ggef *EG_OMathMathElementsChoice )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {e .EncodeToken (start );
if _ggef .Acc !=nil {_cfca :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0061c\u0063"}};e .EncodeElement (_ggef .Acc ,_cfca );}else if _ggef .Bar !=nil {_bcgdd :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0062a\u0072"}};e .EncodeElement (_ggef .Bar ,_bcgdd );
}else if _ggef .Box !=nil {_ddgf :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0062o\u0078"}};e .EncodeElement (_ggef .Box ,_ddgf );}else if _ggef .BorderBox !=nil {_facbg :=_g .StartElement {Name :_g .Name {Local :"m\u003a\u0062\u006f\u0072\u0064\u0065\u0072\u0042\u006f\u0078"}};
e .EncodeElement (_ggef .BorderBox ,_facbg );}else if _ggef .D !=nil {_aeff :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0064"}};e .EncodeElement (_ggef .D ,_aeff );}else if _ggef .EqArr !=nil {_ggeb :=_g .StartElement {Name :_g .Name {Local :"\u006d:\u0065\u0071\u0041\u0072\u0072"}};
e .EncodeElement (_ggef .EqArr ,_ggeb );}else if _ggef .F !=nil {_ceeef :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0066"}};e .EncodeElement (_ggef .F ,_ceeef );}else if _ggef .Func !=nil {_affdc :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0066\u0075\u006e\u0063"}};
e .EncodeElement (_ggef .Func ,_affdc );}else if _ggef .GroupChr !=nil {_ecbd :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0067\u0072\u006f\u0075\u0070\u0043\u0068\u0072"}};e .EncodeElement (_ggef .GroupChr ,_ecbd );}else if _ggef .LimLow !=nil {_fcfgd :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u006c\u0069\u006d\u004c\u006f\u0077"}};
e .EncodeElement (_ggef .LimLow ,_fcfgd );}else if _ggef .LimUpp !=nil {_fcbbd :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u006c\u0069\u006d\u0055\u0070\u0070"}};e .EncodeElement (_ggef .LimUpp ,_fcbbd );}else if _ggef .M !=nil {_ddfdc :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u006d"}};
e .EncodeElement (_ggef .M ,_ddfdc );}else if _ggef .Nary !=nil {_adba :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u006e\u0061\u0072\u0079"}};e .EncodeElement (_ggef .Nary ,_adba );}else if _ggef .Phant !=nil {_bagb :=_g .StartElement {Name :_g .Name {Local :"\u006d:\u0070\u0068\u0061\u006e\u0074"}};
e .EncodeElement (_ggef .Phant ,_bagb );}else if _ggef .Rad !=nil {_efdd :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0072a\u0064"}};e .EncodeElement (_ggef .Rad ,_efdd );}else if _ggef .SPre !=nil {_gagf :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0073\u0050\u0072\u0065"}};
e .EncodeElement (_ggef .SPre ,_gagf );}else if _ggef .SSub !=nil {_gccc :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0073\u0053\u0075\u0062"}};e .EncodeElement (_ggef .SSub ,_gccc );}else if _ggef .SSubSup !=nil {_ggdb :=_g .StartElement {Name :_g .Name {Local :"\u006d:\u0073\u0053\u0075\u0062\u0053\u0075p"}};
e .EncodeElement (_ggef .SSubSup ,_ggdb );}else if _ggef .SSup !=nil {_bgbef :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0073\u0053\u0075\u0070"}};e .EncodeElement (_ggef .SSup ,_bgbef );}else if _ggef .R !=nil {_cfeeg :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0072"}};
e .EncodeElement (_ggef .R ,_cfeeg );};e .EncodeToken (_g .EndElement {Name :start .Name });return nil ;};type CT_RadPr struct{

// Hide Degree
DegHide *CT_OnOff ;CtrlPr *CT_CtrlPr ;};func (_dagb ST_BreakBinSub )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {return e .EncodeElement (_dagb .String (),start );};func (_dbaf *CT_OMath )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {_eeed :for {_beed ,_gdea :=d .Token ();
if _gdea !=nil {return _gdea ;};switch _fefd :=_beed .(type ){case _g .StartElement :switch _fefd .Name {default:_b .Log .Debug ("\u0073\u006b\u0069\u0070\u0070\u0069\u006eg\u0020\u0075\u006es\u0075\u0070\u0070\u006fr\u0074\u0065\u0064\u0020\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0043\u0054\u005f\u004f\u004d\u0061\u0074\u0068\u0020\u0025\u0076",_fefd .Name );
if _fgbd :=d .Skip ();_fgbd !=nil {return _fgbd ;};};case _g .EndElement :break _eeed ;case _g .CharData :};};return nil ;};func (_degc *CT_OMathArg )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {_gfgc :for {_bedfb ,_afdc :=d .Token ();if _afdc !=nil {return _afdc ;
};switch _ccgf :=_bedfb .(type ){case _g .StartElement :switch _ccgf .Name {case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0061\u0072\u0067P\u0072"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0061\u0072\u0067P\u0072"}:_degc .ArgPr =NewCT_OMathArgPr ();
if _bac :=d .DecodeElement (_degc .ArgPr ,&_ccgf );_bac !=nil {return _bac ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0063\u0074\u0072\u006c\u0050\u0072"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0063\u0074\u0072\u006c\u0050\u0072"}:_degc .CtrlPr =NewCT_CtrlPr ();
if _gdfee :=d .DecodeElement (_degc .CtrlPr ,&_ccgf );_gdfee !=nil {return _gdfee ;};default:_b .Log .Debug ("\u0073\u006bi\u0070\u0070\u0069\u006e\u0067\u0020\u0075\u006e\u0073\u0075\u0070\u0070\u006f\u0072\u0074\u0065\u0064\u0020\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0043\u0054\u005f\u004f\u004d\u0061\u0074\u0068\u0041\u0072\u0067\u0020\u0025\u0076",_ccgf .Name );
if _fefed :=d .Skip ();_fefed !=nil {return _fefed ;};};case _g .EndElement :break _gfgc ;case _g .CharData :};};return nil ;};

// Validate validates the CT_MR and its children
func (_gada *CT_MR )Validate ()error {return _gada .ValidateWithPath ("\u0043\u0054\u005fM\u0052")};type ST_Jc byte ;

// ValidateWithPath validates the CT_SSub and its children, prefixing error messages with path
func (_face *CT_SSub )ValidateWithPath (path string )error {if _face .SSubPr !=nil {if _abee :=_face .SSubPr .ValidateWithPath (path +"\u002fS\u0053\u0075\u0062\u0050\u0072");_abee !=nil {return _abee ;};};if _afaae :=_face .E .ValidateWithPath (path +"\u002f\u0045");
_afaae !=nil {return _afaae ;};if _gfdd :=_face .Sub .ValidateWithPath (path +"\u002f\u0053\u0075\u0062");_gfdd !=nil {return _gfdd ;};return nil ;};func (_gg *CT_BarPr )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {e .EncodeToken (start );
if _gg .Pos !=nil {_fg :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0070o\u0073"}};e .EncodeElement (_gg .Pos ,_fg );};if _gg .CtrlPr !=nil {_dgaa :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0063\u0074\u0072\u006c\u0050\u0072"}};
e .EncodeElement (_gg .CtrlPr ,_dgaa );};e .EncodeToken (_g .EndElement {Name :start .Name });return nil ;};func (_cgaae *CT_GroupChrPr )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {_dgg :for {_abaf ,_adbg :=d .Token ();if _adbg !=nil {return _adbg ;
};switch _bege :=_abaf .(type ){case _g .StartElement :switch _bege .Name {case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0063\u0068\u0072"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0063\u0068\u0072"}:_cgaae .Chr =NewCT_Char ();
if _abad :=d .DecodeElement (_cgaae .Chr ,&_bege );_abad !=nil {return _abad ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0070\u006f\u0073"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0070\u006f\u0073"}:_cgaae .Pos =NewCT_TopBot ();
if _daee :=d .DecodeElement (_cgaae .Pos ,&_bege );_daee !=nil {return _daee ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0076\u0065\u0072\u0074\u004a\u0063"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0076\u0065\u0072\u0074\u004a\u0063"}:_cgaae .VertJc =NewCT_TopBot ();
if _egd :=d .DecodeElement (_cgaae .VertJc ,&_bege );_egd !=nil {return _egd ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0063\u0074\u0072\u006c\u0050\u0072"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0063\u0074\u0072\u006c\u0050\u0072"}:_cgaae .CtrlPr =NewCT_CtrlPr ();
if _afde :=d .DecodeElement (_cgaae .CtrlPr ,&_bege );_afde !=nil {return _afde ;};default:_b .Log .Debug ("\u0073\u006b\u0069\u0070\u0070\u0069\u006e\u0067 \u0075\u006e\u0073up\u0070\u006f\u0072\u0074\u0065\u0064 \u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0043\u0054\u005f\u0047r\u006f\u0075\u0070\u0043\u0068\u0072\u0050\u0072 \u0025\u0076",_bege .Name );
if _gbef :=d .Skip ();_gbef !=nil {return _gbef ;};};case _g .EndElement :break _dgg ;case _g .CharData :};};return nil ;};func NewCT_BreakBin ()*CT_BreakBin {_afff :=&CT_BreakBin {};return _afff };func (_gbaf *CT_OMathArgPr )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {_dbgef :for {_bbb ,_aedb :=d .Token ();
if _aedb !=nil {return _aedb ;};switch _eeaf :=_bbb .(type ){case _g .StartElement :switch _eeaf .Name {case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0061\u0072\u0067S\u007a"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0061\u0072\u0067S\u007a"}:_gbaf .ArgSz =NewCT_Integer2 ();
if _fbfa :=d .DecodeElement (_gbaf .ArgSz ,&_eeaf );_fbfa !=nil {return _fbfa ;};default:_b .Log .Debug ("\u0073\u006b\u0069\u0070\u0070\u0069\u006e\u0067 \u0075\u006e\u0073up\u0070\u006f\u0072\u0074\u0065\u0064 \u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0043\u0054\u005f\u004fM\u0061\u0074\u0068\u0041\u0072\u0067\u0050\u0072 \u0025\u0076",_eeaf .Name );
if _afdde :=d .Skip ();_afdde !=nil {return _afdde ;};};case _g .EndElement :break _dbgef ;case _g .CharData :};};return nil ;};func (_ddgd *CT_SSubPr )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {e .EncodeToken (start );if _ddgd .CtrlPr !=nil {_cfaa :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0063\u0074\u0072\u006c\u0050\u0072"}};
e .EncodeElement (_ddgd .CtrlPr ,_cfaa );};e .EncodeToken (_g .EndElement {Name :start .Name });return nil ;};func NewCT_MC ()*CT_MC {_bggeb :=&CT_MC {};return _bggeb };

// Validate validates the CT_LimLoc and its children
func (_fbb *CT_LimLoc )Validate ()error {return _fbb .ValidateWithPath ("\u0043T\u005f\u004c\u0069\u006d\u004c\u006fc");};type CT_NaryPr struct{

// n-ary Operator Character
Chr *CT_Char ;

// n-ary Limit Location
LimLoc *CT_LimLoc ;

// n-ary Grow
Grow *CT_OnOff ;

// Hide Subscript (n-ary)
SubHide *CT_OnOff ;

// Hide Superscript (n-ary)
SupHide *CT_OnOff ;CtrlPr *CT_CtrlPr ;};

// ValidateWithPath validates the CT_Text and its children, prefixing error messages with path
func (_gccg *CT_Text )ValidateWithPath (path string )error {return nil };func (_eacg *CT_SPre )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {e .EncodeToken (start );if _eacg .SPrePr !=nil {_dgabc :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0073\u0050\u0072\u0065\u0050\u0072"}};
e .EncodeElement (_eacg .SPrePr ,_dgabc );};_gcge :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0073u\u0062"}};e .EncodeElement (_eacg .Sub ,_gcge );_eecf :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0073u\u0070"}};e .EncodeElement (_eacg .Sup ,_eecf );
_deea :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0065"}};e .EncodeElement (_eacg .E ,_deea );e .EncodeToken (_g .EndElement {Name :start .Name });return nil ;};func (_bfbfga *ST_Jc )UnmarshalXMLAttr (attr _g .Attr )error {switch attr .Value {case "":*_bfbfga =0;
case "\u006c\u0065\u0066\u0074":*_bfbfga =1;case "\u0072\u0069\u0067h\u0074":*_bfbfga =2;case "\u0063\u0065\u006e\u0074\u0065\u0072":*_bfbfga =3;case "c\u0065\u006e\u0074\u0065\u0072\u0047\u0072\u006f\u0075\u0070":*_bfbfga =4;};return nil ;};func (_bgfg *CT_SPre )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {_bgfg .Sub =NewCT_OMathArg ();
_bgfg .Sup =NewCT_OMathArg ();_bgfg .E =NewCT_OMathArg ();_fcdf :for {_fefa ,_ffdb :=d .Token ();if _ffdb !=nil {return _ffdb ;};switch _afffe :=_fefa .(type ){case _g .StartElement :switch _afffe .Name {case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0073\u0050\u0072\u0065\u0050\u0072"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0073\u0050\u0072\u0065\u0050\u0072"}:_bgfg .SPrePr =NewCT_SPrePr ();
if _baagd :=d .DecodeElement (_bgfg .SPrePr ,&_afffe );_baagd !=nil {return _baagd ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0073\u0075\u0062"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0073\u0075\u0062"}:if _fcae :=d .DecodeElement (_bgfg .Sub ,&_afffe );
_fcae !=nil {return _fcae ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0073\u0075\u0070"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0073\u0075\u0070"}:if _eecag :=d .DecodeElement (_bgfg .Sup ,&_afffe );
_eecag !=nil {return _eecag ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0065"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0065"}:if _fcddd :=d .DecodeElement (_bgfg .E ,&_afffe );
_fcddd !=nil {return _fcddd ;};default:_b .Log .Debug ("\u0073\u006b\u0069p\u0070\u0069\u006e\u0067\u0020\u0075\u006e\u0073\u0075\u0070\u0070\u006f\u0072\u0074\u0065\u0064\u0020\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0043T\u005f\u0053\u0050\u0072\u0065\u0020\u0025\u0076",_afffe .Name );
if _aadbb :=d .Skip ();_aadbb !=nil {return _aadbb ;};};case _g .EndElement :break _fcdf ;case _g .CharData :};};return nil ;};

// ValidateWithPath validates the CT_RadPr and its children, prefixing error messages with path
func (_dggb *CT_RadPr )ValidateWithPath (path string )error {if _dggb .DegHide !=nil {if _bgedd :=_dggb .DegHide .ValidateWithPath (path +"\u002f\u0044\u0065\u0067\u0048\u0069\u0064\u0065");_bgedd !=nil {return _bgedd ;};};if _dggb .CtrlPr !=nil {if _abac :=_dggb .CtrlPr .ValidateWithPath (path +"\u002fC\u0074\u0072\u006c\u0050\u0072");
_abac !=nil {return _abac ;};};return nil ;};func (_dgffb ST_Shp )MarshalXMLAttr (name _g .Name )(_g .Attr ,error ){_bcea :=_g .Attr {};_bcea .Name =name ;switch _dgffb {case ST_ShpUnset :_bcea .Value ="";case ST_ShpCentered :_bcea .Value ="\u0063\u0065\u006e\u0074\u0065\u0072\u0065\u0064";
case ST_ShpMatch :_bcea .Value ="\u006d\u0061\u0074c\u0068";};return _bcea ,nil ;};func NewCT_XAlign ()*CT_XAlign {_bbeg :=&CT_XAlign {};_bbeg .ValAttr =_ab .ST_XAlign (1);return _bbeg };func (_gdfb *CT_FPr )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {e .EncodeToken (start );
if _gdfb .Type !=nil {_ggdd :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0074\u0079\u0070\u0065"}};e .EncodeElement (_gdfb .Type ,_ggdd );};if _gdfb .CtrlPr !=nil {_cce :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0063\u0074\u0072\u006c\u0050\u0072"}};
e .EncodeElement (_gdfb .CtrlPr ,_cce );};e .EncodeToken (_g .EndElement {Name :start .Name });return nil ;};func (_cgfeb *ST_BreakBin )UnmarshalXMLAttr (attr _g .Attr )error {switch attr .Value {case "":*_cgfeb =0;case "\u0062\u0065\u0066\u006f\u0072\u0065":*_cgfeb =1;
case "\u0061\u0066\u0074e\u0072":*_cgfeb =2;case "\u0072\u0065\u0070\u0065\u0061\u0074":*_cgfeb =3;};return nil ;};

// ValidateWithPath validates the CT_PhantPr and its children, prefixing error messages with path
func (_dgab *CT_PhantPr )ValidateWithPath (path string )error {if _dgab .Show !=nil {if _agcf :=_dgab .Show .ValidateWithPath (path +"\u002f\u0053\u0068o\u0077");_agcf !=nil {return _agcf ;};};if _dgab .ZeroWid !=nil {if _fcfd :=_dgab .ZeroWid .ValidateWithPath (path +"\u002f\u005a\u0065\u0072\u006f\u0057\u0069\u0064");
_fcfd !=nil {return _fcfd ;};};if _dgab .ZeroAsc !=nil {if _aef :=_dgab .ZeroAsc .ValidateWithPath (path +"\u002f\u005a\u0065\u0072\u006f\u0041\u0073\u0063");_aef !=nil {return _aef ;};};if _dgab .ZeroDesc !=nil {if _eace :=_dgab .ZeroDesc .ValidateWithPath (path +"\u002fZ\u0065\u0072\u006f\u0044\u0065\u0073c");
_eace !=nil {return _eace ;};};if _dgab .Transp !=nil {if _adfb :=_dgab .Transp .ValidateWithPath (path +"\u002fT\u0072\u0061\u006e\u0073\u0070");_adfb !=nil {return _adfb ;};};if _dgab .CtrlPr !=nil {if _feeb :=_dgab .CtrlPr .ValidateWithPath (path +"\u002fC\u0074\u0072\u006c\u0050\u0072");
_feeb !=nil {return _feeb ;};};return nil ;};func (_cedef *CT_Integer255 )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {_cedef .ValAttr =1;for _ ,_fbd :=range start .Attr {if _fbd .Name .Local =="\u0076\u0061\u006c"{_aadg ,_dbab :=_f .ParseInt (_fbd .Value ,10,64);
if _dbab !=nil {return _dbab ;};_cedef .ValAttr =_aadg ;continue ;};};for {_bfc ,_aab :=d .Token ();if _aab !=nil {return _a .Errorf ("\u0070a\u0072\u0073\u0069\u006eg\u0020\u0043\u0054\u005f\u0049n\u0074e\u0067e\u0072\u0032\u0035\u0035\u003a\u0020\u0025s",_aab );
};if _gfa ,_bcba :=_bfc .(_g .EndElement );_bcba &&_gfa .Name ==start .Name {break ;};};return nil ;};func (_gadb *CT_OMathParaPr )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {e .EncodeToken (start );if _gadb .Jc !=nil {_bdacb :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u006a\u0063"}};
e .EncodeElement (_gadb .Jc ,_bdacb );};e .EncodeToken (_g .EndElement {Name :start .Name });return nil ;};func NewCT_RChoice ()*CT_RChoice {_ebed :=&CT_RChoice {};return _ebed };

// Validate validates the CT_BoxPr and its children
func (_dcae *CT_BoxPr )Validate ()error {return _dcae .ValidateWithPath ("\u0043\u0054\u005f\u0042\u006f\u0078\u0050\u0072");};func (_addd *ST_Script )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {_dacg ,_bdccbd :=d .Token ();if _bdccbd !=nil {return _bdccbd ;
};if _dfge ,_fdcc :=_dacg .(_g .EndElement );_fdcc &&_dfge .Name ==start .Name {*_addd =1;return nil ;};if _bdgf ,_cae :=_dacg .(_g .CharData );!_cae {return _a .Errorf ("\u0065\u0078\u0070\u0065\u0063\u0074\u0065\u0064\u0020\u0063\u0068a\u0072\u0020\u0064\u0061\u0074\u0061\u002c\u0020\u0067\u006ft\u0020\u0025\u0054",_dacg );
}else {switch string (_bdgf ){case "":*_addd =0;case "\u0072\u006f\u006da\u006e":*_addd =1;case "\u0073\u0063\u0072\u0069\u0070\u0074":*_addd =2;case "\u0066r\u0061\u006b\u0074\u0075\u0072":*_addd =3;case "\u0064\u006f\u0075\u0062\u006c\u0065\u002d\u0073\u0074\u0072\u0075\u0063\u006b":*_addd =4;
case "\u0073\u0061\u006e\u0073\u002d\u0073\u0065\u0072\u0069\u0066":*_addd =5;case "\u006do\u006e\u006f\u0073\u0070\u0061\u0063e":*_addd =6;};};_dacg ,_bdccbd =d .Token ();if _bdccbd !=nil {return _bdccbd ;};if _cfbb ,_cgebg :=_dacg .(_g .EndElement );
_cgebg &&_cfbb .Name ==start .Name {return nil ;};return _a .Errorf ("\u0065\u0078\u0070\u0065c\u0074\u0065\u0064\u0020\u0065\u006e\u0064\u0020\u0065\u006ce\u006de\u006e\u0074\u002c\u0020\u0067\u006f\u0074 \u0025\u0076",_dacg );};

// Validate validates the CT_RadPr and its children
func (_fdfg *CT_RadPr )Validate ()error {return _fdfg .ValidateWithPath ("\u0043\u0054\u005f\u0052\u0061\u0064\u0050\u0072");};func (_aaaa ST_BreakBinSub )String ()string {switch _aaaa {case 0:return "";case 1:return "\u002d\u002d";case 2:return "\u002d\u002b";
case 3:return "\u002b\u002d";};return "";};

// ValidateWithPath validates the CT_RPR and its children, prefixing error messages with path
func (_dccba *CT_RPR )ValidateWithPath (path string )error {if _dccba .Lit !=nil {if _ggad :=_dccba .Lit .ValidateWithPath (path +"\u002f\u004c\u0069\u0074");_ggad !=nil {return _ggad ;};};if _dccba .Nor !=nil {if _adcb :=_dccba .Nor .ValidateWithPath (path +"\u002f\u004e\u006f\u0072");
_adcb !=nil {return _adcb ;};};if _dccba .Brk !=nil {if _faad :=_dccba .Brk .ValidateWithPath (path +"\u002f\u0042\u0072\u006b");_faad !=nil {return _faad ;};};if _dccba .Aln !=nil {if _eebb :=_dccba .Aln .ValidateWithPath (path +"\u002f\u0041\u006c\u006e");
_eebb !=nil {return _eebb ;};};return nil ;};

// Validate validates the CT_String and its children
func (_cggce *CT_String )Validate ()error {return _cggce .ValidateWithPath ("\u0043T\u005f\u0053\u0074\u0072\u0069\u006eg");};const (ST_BreakBinUnset ST_BreakBin =0;ST_BreakBinBefore ST_BreakBin =1;ST_BreakBinAfter ST_BreakBin =2;ST_BreakBinRepeat ST_BreakBin =3;
);

// Validate validates the CT_BorderBoxPr and its children
func (_dega *CT_BorderBoxPr )Validate ()error {return _dega .ValidateWithPath ("\u0043\u0054\u005f\u0042\u006f\u0072\u0064\u0065\u0072B\u006f\u0078\u0050\u0072");};func (_befd ST_Script )Validate ()error {return _befd .ValidateWithPath ("")};

// Validate validates the MathPr and its children
func (_bacc *MathPr )Validate ()error {return _bacc .ValidateWithPath ("\u004d\u0061\u0074\u0068\u0050\u0072");};func (_fddc *EG_OMathMathElements )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {_fddc .OMathMathElementsChoice .MarshalXML (e ,_g .StartElement {});
return nil ;};func (_edf *CT_GroupChrPr )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {e .EncodeToken (start );if _edf .Chr !=nil {_dgeb :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0063h\u0072"}};e .EncodeElement (_edf .Chr ,_dgeb );
};if _edf .Pos !=nil {_abab :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0070o\u0073"}};e .EncodeElement (_edf .Pos ,_abab );};if _edf .VertJc !=nil {_dff :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0076\u0065\u0072\u0074\u004a\u0063"}};
e .EncodeElement (_edf .VertJc ,_dff );};if _edf .CtrlPr !=nil {_acfd :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0063\u0074\u0072\u006c\u0050\u0072"}};e .EncodeElement (_edf .CtrlPr ,_acfd );};e .EncodeToken (_g .EndElement {Name :start .Name });
return nil ;};func (_dffd ST_TopBot )MarshalXMLAttr (name _g .Name )(_g .Attr ,error ){_dggc :=_g .Attr {};_dggc .Name =name ;switch _dffd {case ST_TopBotUnset :_dggc .Value ="";case ST_TopBotTop :_dggc .Value ="\u0074\u006f\u0070";case ST_TopBotBot :_dggc .Value ="\u0062\u006f\u0074";
};return _dggc ,nil ;};

// ValidateWithPath validates the CT_OMathArgPr and its children, prefixing error messages with path
func (_cefe *CT_OMathArgPr )ValidateWithPath (path string )error {if _cefe .ArgSz !=nil {if _cbad :=_cefe .ArgSz .ValidateWithPath (path +"\u002f\u0041\u0072\u0067\u0053\u007a");_cbad !=nil {return _cbad ;};};return nil ;};type CT_SSubSupPr struct{

// Align Scripts
AlnScr *CT_OnOff ;CtrlPr *CT_CtrlPr ;};func (_daba *EG_OMathElements )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {_daba .OMathElementsChoice =NewEG_OMathElementsChoice ();_daeb :for {_abbb ,_fbec :=d .Token ();if _fbec !=nil {return _fbec ;
};switch _begb :=_abbb .(type ){case _g .StartElement :switch _begb .Name {default:_dbbdg :=_daba .OMathElementsChoice ;if _dbbdg ==nil {_dbbdg =NewEG_OMathElementsChoice ();};if _dfbfa :=d .DecodeElement (&_dbbdg ,&_begb );_dfbfa ==nil {_daba .OMathElementsChoice =_dbbdg ;
};_b .Log .Debug ("\u0073\u006b\u0069\u0070\u0070i\u006e\u0067\u0020\u0075\u006e\u0073\u0075\u0070\u0070\u006f\u0072\u0074\u0065d\u0020\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0045\u0047\u005f\u004f\u004d\u0061\u0074\u0068\u0045\u006c\u0065\u006d\u0065\u006e\u0074\u0073\u0020\u0025v",_begb .Name );
if _ccbag :=d .Skip ();_ccbag !=nil {return _ccbag ;};};case _g .EndElement :break _daeb ;case _g .CharData :};};return nil ;};func (_aceaf *OMath )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {_aceaf .CT_OMath =*NewCT_OMath ();_bbgg :for {_bceb ,_cbga :=d .Token ();
if _cbga !=nil {return _cbga ;};switch _cbba :=_bceb .(type ){case _g .StartElement :switch _cbba .Name {default:_b .Log .Debug ("s\u006b\u0069\u0070\u0070\u0069\u006e\u0067\u0020\u0075\u006e\u0073\u0075\u0070\u0070\u006f\u0072\u0074\u0065d\u0020\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006fn \u004f\u004d\u0061t\u0068 \u0025\u0076",_cbba .Name );
if _eedc :=d .Skip ();_eedc !=nil {return _eedc ;};};case _g .EndElement :break _bbgg ;case _g .CharData :};};return nil ;};type CT_Integer2 struct{

// Value
ValAttr int64 ;};type CT_EqArrPr struct{

// Equation Array Base Justification
BaseJc *CT_YAlign ;

// Maximum Distribution
MaxDist *CT_OnOff ;

// Object Distribution
ObjDist *CT_OnOff ;

// Row Spacing Rule
RSpRule *CT_SpacingRule ;

// Row Spacing (Array)
RSp *CT_UnSignedInteger ;CtrlPr *CT_CtrlPr ;};type CT_PhantPr struct{

// Phantom Show
Show *CT_OnOff ;

// Phantom Zero Width
ZeroWid *CT_OnOff ;

// Phantom Zero Ascent
ZeroAsc *CT_OnOff ;

// Phantom Zero Descent
ZeroDesc *CT_OnOff ;

// Transparent (Phantom)
Transp *CT_OnOff ;CtrlPr *CT_CtrlPr ;};type CT_OMathArg struct{

// Argument Properties
ArgPr *CT_OMathArgPr ;EG_OMathElements []*EG_OMathElements ;CtrlPr *CT_CtrlPr ;};func NewCT_Shp ()*CT_Shp {_ddfc :=&CT_Shp {};_ddfc .ValAttr =ST_Shp (1);return _ddfc };func (_dfde *CT_LimLoc )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {_fcdg ,_cbe :=_dfde .ValAttr .MarshalXMLAttr (_g .Name {Local :"\u006d\u003a\u0076a\u006c"});
if _cbe !=nil {return _cbe ;};start .Attr =append (start .Attr ,_fcdg );e .EncodeToken (start );e .EncodeToken (_g .EndElement {Name :start .Name });return nil ;};func (_efd *CT_BreakBinSub )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {if _efd .ValAttr !=ST_BreakBinSubUnset {_agca ,_aae :=_efd .ValAttr .MarshalXMLAttr (_g .Name {Local :"\u006d\u003a\u0076a\u006c"});
if _aae !=nil {return _aae ;};start .Attr =append (start .Attr ,_agca );};e .EncodeToken (start );e .EncodeToken (_g .EndElement {Name :start .Name });return nil ;};func (_cdg *CT_D )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {e .EncodeToken (start );
if _cdg .DPr !=nil {_feb :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0064P\u0072"}};e .EncodeElement (_cdg .DPr ,_feb );};_cddb :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0065"}};for _ ,_fecd :=range _cdg .E {e .EncodeElement (_fecd ,_cddb );
};e .EncodeToken (_g .EndElement {Name :start .Name });return nil ;};

// Validate validates the CT_SpacingRule and its children
func (_cgbf *CT_SpacingRule )Validate ()error {return _cgbf .ValidateWithPath ("\u0043\u0054\u005f\u0053\u0070\u0061\u0063\u0069\u006eg\u0052\u0075\u006c\u0065");};func (_abeb *CT_LimLow )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {_abeb .E =NewCT_OMathArg ();
_abeb .Lim =NewCT_OMathArg ();_gagb :for {_dbd ,_cabb :=d .Token ();if _cabb !=nil {return _cabb ;};switch _bedf :=_dbd .(type ){case _g .StartElement :switch _bedf .Name {case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u006c\u0069\u006d\u004c\u006f\u0077\u0050\u0072"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u006c\u0069\u006d\u004c\u006f\u0077\u0050\u0072"}:_abeb .LimLowPr =NewCT_LimLowPr ();
if _dbge :=d .DecodeElement (_abeb .LimLowPr ,&_bedf );_dbge !=nil {return _dbge ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0065"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0065"}:if _gcfd :=d .DecodeElement (_abeb .E ,&_bedf );
_gcfd !=nil {return _gcfd ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u006c\u0069\u006d"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u006c\u0069\u006d"}:if _bgdg :=d .DecodeElement (_abeb .Lim ,&_bedf );
_bgdg !=nil {return _bgdg ;};default:_b .Log .Debug ("\u0073k\u0069\u0070p\u0069\u006e\u0067\u0020u\u006e\u0073\u0075p\u0070\u006f\u0072\u0074\u0065\u0064\u0020\u0065\u006cem\u0065\u006e\u0074 \u006f\u006e \u0043\u0054\u005f\u004c\u0069\u006dL\u006f\u0077 \u0025\u0076",_bedf .Name );
if _bfae :=d .Skip ();_bfae !=nil {return _bfae ;};};case _g .EndElement :break _gagb ;case _g .CharData :};};return nil ;};

// ValidateWithPath validates the EG_OMathElementsChoice and its children, prefixing error messages with path
func (_ffc *EG_OMathElementsChoice )ValidateWithPath (path string )error {if _fbff :=_ffc .OMathMathElementsChoice .ValidateWithPath (path +"\u002fO\u004d\u0061\u0074\u0068\u004d\u0061\u0074\u0068\u0045\u006c\u0065m\u0065\u006e\u0074\u0073\u0043\u0068\u006f\u0069\u0063\u0065");
_fbff !=nil {return _fbff ;};return nil ;};

// Validate validates the OMath and its children
func (_dag *OMath )Validate ()error {return _dag .ValidateWithPath ("\u004f\u004d\u0061t\u0068")};func ParseUnionST_OnOff (s string )(_ab .ST_OnOff ,error ){return _ab .ParseUnionST_OnOff (s )};func (_af *CT_Acc )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {e .EncodeToken (start );
if _af .AccPr !=nil {_e :=_g .StartElement {Name :_g .Name {Local :"\u006d:\u0061\u0063\u0063\u0050\u0072"}};e .EncodeElement (_af .AccPr ,_e );};_be :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0065"}};e .EncodeElement (_af .E ,_be );e .EncodeToken (_g .EndElement {Name :start .Name });
return nil ;};func NewEG_ScriptStyle ()*EG_ScriptStyle {_cbdd :=&EG_ScriptStyle {};return _cbdd };func NewCT_MCPr ()*CT_MCPr {_cbee :=&CT_MCPr {};return _cbee };func NewCT_CtrlPr ()*CT_CtrlPr {_gbda :=&CT_CtrlPr {};return _gbda };func NewEG_OMathMathElementsChoice ()*EG_OMathMathElementsChoice {_cdfc :=&EG_OMathMathElementsChoice {};
return _cdfc ;};func (_bcgdg *CT_PhantPr )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {e .EncodeToken (start );if _bcgdg .Show !=nil {_bged :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0073\u0068\u006f\u0077"}};e .EncodeElement (_bcgdg .Show ,_bged );
};if _bcgdg .ZeroWid !=nil {_ggac :=_g .StartElement {Name :_g .Name {Local :"\u006d:\u007a\u0065\u0072\u006f\u0057\u0069d"}};e .EncodeElement (_bcgdg .ZeroWid ,_ggac );};if _bcgdg .ZeroAsc !=nil {_gbdb :=_g .StartElement {Name :_g .Name {Local :"\u006d:\u007a\u0065\u0072\u006f\u0041\u0073c"}};
e .EncodeElement (_bcgdg .ZeroAsc ,_gbdb );};if _bcgdg .ZeroDesc !=nil {_dccbb :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u007a\u0065\u0072\u006f\u0044\u0065\u0073\u0063"}};e .EncodeElement (_bcgdg .ZeroDesc ,_dccbb );};if _bcgdg .Transp !=nil {_bgfd :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0074\u0072\u0061\u006e\u0073\u0070"}};
e .EncodeElement (_bcgdg .Transp ,_bgfd );};if _bcgdg .CtrlPr !=nil {_ceb :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0063\u0074\u0072\u006c\u0050\u0072"}};e .EncodeElement (_bcgdg .CtrlPr ,_ceb );};e .EncodeToken (_g .EndElement {Name :start .Name });
return nil ;};func (_bde *CT_M )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {_baab :for {_afef ,_egag :=d .Token ();if _egag !=nil {return _egag ;};switch _agcg :=_afef .(type ){case _g .StartElement :switch _agcg .Name {case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u006d\u0050\u0072"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u006d\u0050\u0072"}:_bde .MPr =NewCT_MPr ();
if _fbgg :=d .DecodeElement (_bde .MPr ,&_agcg );_fbgg !=nil {return _fbgg ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u006d\u0072"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u006d\u0072"}:_eeca :=NewCT_MR ();
if _ebd :=d .DecodeElement (_eeca ,&_agcg );_ebd !=nil {return _ebd ;};_bde .Mr =append (_bde .Mr ,_eeca );default:_b .Log .Debug ("\u0073\u006b\u0069\u0070\u0070\u0069\u006e\u0067\u0020\u0075\u006e\u0073\u0075p\u0070\u006f\u0072\u0074\u0065\u0064 \u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0043\u0054_\u004d\u0020\u0025\u0076",_agcg .Name );
if _gcdb :=d .Skip ();_gcdb !=nil {return _gcdb ;};};case _g .EndElement :break _baab ;case _g .CharData :};};return nil ;};func (_fgfbc *ST_Jc )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {_fgbe ,_gdfbe :=d .Token ();if _gdfbe !=nil {return _gdfbe ;
};if _affg ,_bcag :=_fgbe .(_g .EndElement );_bcag &&_affg .Name ==start .Name {*_fgfbc =1;return nil ;};if _eggd ,_cebe :=_fgbe .(_g .CharData );!_cebe {return _a .Errorf ("\u0065\u0078\u0070\u0065\u0063\u0074\u0065\u0064\u0020\u0063\u0068a\u0072\u0020\u0064\u0061\u0074\u0061\u002c\u0020\u0067\u006ft\u0020\u0025\u0054",_fgbe );
}else {switch string (_eggd ){case "":*_fgfbc =0;case "\u006c\u0065\u0066\u0074":*_fgfbc =1;case "\u0072\u0069\u0067h\u0074":*_fgfbc =2;case "\u0063\u0065\u006e\u0074\u0065\u0072":*_fgfbc =3;case "c\u0065\u006e\u0074\u0065\u0072\u0047\u0072\u006f\u0075\u0070":*_fgfbc =4;
};};_fgbe ,_gdfbe =d .Token ();if _gdfbe !=nil {return _gdfbe ;};if _ebcfaa ,_aafad :=_fgbe .(_g .EndElement );_aafad &&_ebcfaa .Name ==start .Name {return nil ;};return _a .Errorf ("\u0065\u0078\u0070\u0065c\u0074\u0065\u0064\u0020\u0065\u006e\u0064\u0020\u0065\u006ce\u006de\u006e\u0074\u002c\u0020\u0067\u006f\u0074 \u0025\u0076",_fgbe );
};type CT_TwipsMeasure struct{

// Value
ValAttr _ab .ST_TwipsMeasure ;};type EG_OMathMathElements struct{OMathMathElementsChoice *EG_OMathMathElementsChoice ;};

// Validate validates the CT_F and its children
func (_bab *CT_F )Validate ()error {return _bab .ValidateWithPath ("\u0043\u0054\u005f\u0046")};func (_aegd *CT_Rad )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {e .EncodeToken (start );if _aegd .RadPr !=nil {_dfcfg :=_g .StartElement {Name :_g .Name {Local :"\u006d:\u0072\u0061\u0064\u0050\u0072"}};
e .EncodeElement (_aegd .RadPr ,_dfcfg );};_bbbf :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0064e\u0067"}};e .EncodeElement (_aegd .Deg ,_bbbf );_affe :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0065"}};e .EncodeElement (_aegd .E ,_affe );
e .EncodeToken (_g .EndElement {Name :start .Name });return nil ;};func (_gcf *CT_BorderBox )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {_gcf .E =NewCT_OMathArg ();_eb :for {_bae ,_fbe :=d .Token ();if _fbe !=nil {return _fbe ;};switch _dgag :=_bae .(type ){case _g .StartElement :switch _dgag .Name {case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"b\u006f\u0072\u0064\u0065\u0072\u0042\u006f\u0078\u0050\u0072"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"b\u006f\u0072\u0064\u0065\u0072\u0042\u006f\u0078\u0050\u0072"}:_gcf .BorderBoxPr =NewCT_BorderBoxPr ();
if _gb :=d .DecodeElement (_gcf .BorderBoxPr ,&_dgag );_gb !=nil {return _gb ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0065"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0065"}:if _gcd :=d .DecodeElement (_gcf .E ,&_dgag );
_gcd !=nil {return _gcd ;};default:_b .Log .Debug ("s\u006b\u0069\u0070\u0070\u0069\u006e\u0067\u0020\u0075n\u0073\u0075\u0070\u0070\u006f\u0072\u0074ed\u0020\u0065\u006c\u0065m\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0043\u0054_B\u006f\u0072d\u0065\u0072\u0042\u006f\u0078\u0020\u0025\u0076",_dgag .Name );
if _gfe :=d .Skip ();_gfe !=nil {return _gfe ;};};case _g .EndElement :break _eb ;case _g .CharData :};};return nil ;};func (_cfae *CT_Box )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {e .EncodeToken (start );if _cfae .BoxPr !=nil {_agf :=_g .StartElement {Name :_g .Name {Local :"\u006d:\u0062\u006f\u0078\u0050\u0072"}};
e .EncodeElement (_cfae .BoxPr ,_agf );};_afa :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0065"}};e .EncodeElement (_cfae .E ,_afa );e .EncodeToken (_g .EndElement {Name :start .Name });return nil ;};

// Validate validates the CT_PhantPr and its children
func (_gdgd *CT_PhantPr )Validate ()error {return _gdgd .ValidateWithPath ("\u0043\u0054\u005f\u0050\u0068\u0061\u006e\u0074\u0050\u0072");};type CT_LimUppPr struct{CtrlPr *CT_CtrlPr ;};func (_baabc ST_FType )ValidateWithPath (path string )error {switch _baabc {case 0,1,2,3,4:default:return _a .Errorf ("\u0025s\u003a\u0020\u006f\u0075t\u0020\u006f\u0066\u0020\u0072a\u006eg\u0065 \u0076\u0061\u006c\u0075\u0065\u0020\u0025d",path ,int (_baabc ));
};return nil ;};func (_gdaf *ST_LimLoc )UnmarshalXMLAttr (attr _g .Attr )error {switch attr .Value {case "":*_gdaf =0;case "\u0075\u006e\u0064\u004f\u0076\u0072":*_gdaf =1;case "\u0073\u0075\u0062\u0053\u0075\u0070":*_gdaf =2;};return nil ;};func NewCT_Box ()*CT_Box {_gfca :=&CT_Box {};
_gfca .E =NewCT_OMathArg ();return _gfca };func (_aecb *CT_SSupPr )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {_gbeg :for {_cdce ,_fbac :=d .Token ();if _fbac !=nil {return _fbac ;};switch _gffdg :=_cdce .(type ){case _g .StartElement :switch _gffdg .Name {case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0063\u0074\u0072\u006c\u0050\u0072"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0063\u0074\u0072\u006c\u0050\u0072"}:_aecb .CtrlPr =NewCT_CtrlPr ();
if _gcda :=d .DecodeElement (_aecb .CtrlPr ,&_gffdg );_gcda !=nil {return _gcda ;};default:_b .Log .Debug ("\u0073k\u0069\u0070p\u0069\u006e\u0067\u0020u\u006e\u0073\u0075p\u0070\u006f\u0072\u0074\u0065\u0064\u0020\u0065\u006cem\u0065\u006e\u0074 \u006f\u006e \u0043\u0054\u005f\u0053\u0053\u0075p\u0050\u0072 \u0025\u0076",_gffdg .Name );
if _bbcb :=d .Skip ();_bbcb !=nil {return _bbcb ;};};case _g .EndElement :break _gbeg ;case _g .CharData :};};return nil ;};func (_aacf *CT_LimLowPr )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {_gddb :for {_gac ,_gdab :=d .Token ();if _gdab !=nil {return _gdab ;
};switch _fgg :=_gac .(type ){case _g .StartElement :switch _fgg .Name {case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0063\u0074\u0072\u006c\u0050\u0072"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0063\u0074\u0072\u006c\u0050\u0072"}:_aacf .CtrlPr =NewCT_CtrlPr ();
if _eefe :=d .DecodeElement (_aacf .CtrlPr ,&_fgg );_eefe !=nil {return _eefe ;};default:_b .Log .Debug ("\u0073\u006bi\u0070\u0070\u0069\u006e\u0067\u0020\u0075\u006e\u0073\u0075\u0070\u0070\u006f\u0072\u0074\u0065\u0064\u0020\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0043\u0054\u005f\u004c\u0069\u006d\u004c\u006f\u0077\u0050\u0072\u0020\u0025\u0076",_fgg .Name );
if _ebg :=d .Skip ();_ebg !=nil {return _ebg ;};};case _g .EndElement :break _gddb ;case _g .CharData :};};return nil ;};type CT_SSub struct{

// Subscript Properties
SSubPr *CT_SSubPr ;

// Base
E *CT_OMathArg ;

// Subscript (Subscript function)
Sub *CT_OMathArg ;};

// Validate validates the CT_GroupChrPr and its children
func (_cdba *CT_GroupChrPr )Validate ()error {return _cdba .ValidateWithPath ("\u0043\u0054\u005f\u0047\u0072\u006f\u0075\u0070\u0043\u0068\u0072\u0050\u0072");};func (_bgc *CT_BorderBoxPr )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {_aac :for {_dfd ,_bef :=d .Token ();
if _bef !=nil {return _bef ;};switch _ggc :=_dfd .(type ){case _g .StartElement :switch _ggc .Name {case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0068i\u0064\u0065\u0054\u006f\u0070"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0068i\u0064\u0065\u0054\u006f\u0070"}:_bgc .HideTop =NewCT_OnOff ();
if _dae :=d .DecodeElement (_bgc .HideTop ,&_ggc );_dae !=nil {return _dae ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0068i\u0064\u0065\u0042\u006f\u0074"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0068i\u0064\u0065\u0042\u006f\u0074"}:_bgc .HideBot =NewCT_OnOff ();
if _bbf :=d .DecodeElement (_bgc .HideBot ,&_ggc );_bbf !=nil {return _bbf ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0068\u0069\u0064\u0065\u004c\u0065\u0066\u0074"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0068\u0069\u0064\u0065\u004c\u0065\u0066\u0074"}:_bgc .HideLeft =NewCT_OnOff ();
if _fdc :=d .DecodeElement (_bgc .HideLeft ,&_ggc );_fdc !=nil {return _fdc ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0068i\u0064\u0065\u0052\u0069\u0067\u0068t"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0068i\u0064\u0065\u0052\u0069\u0067\u0068t"}:_bgc .HideRight =NewCT_OnOff ();
if _aagd :=d .DecodeElement (_bgc .HideRight ,&_ggc );_aagd !=nil {return _aagd ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0073t\u0072\u0069\u006b\u0065\u0048"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0073t\u0072\u0069\u006b\u0065\u0048"}:_bgc .StrikeH =NewCT_OnOff ();
if _gca :=d .DecodeElement (_bgc .StrikeH ,&_ggc );_gca !=nil {return _gca ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0073t\u0072\u0069\u006b\u0065\u0056"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0073t\u0072\u0069\u006b\u0065\u0056"}:_bgc .StrikeV =NewCT_OnOff ();
if _dgb :=d .DecodeElement (_bgc .StrikeV ,&_ggc );_dgb !=nil {return _dgb ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0073\u0074\u0072\u0069\u006b\u0065\u0042\u004c\u0054\u0052"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0073\u0074\u0072\u0069\u006b\u0065\u0042\u004c\u0054\u0052"}:_bgc .StrikeBLTR =NewCT_OnOff ();
if _ede :=d .DecodeElement (_bgc .StrikeBLTR ,&_ggc );_ede !=nil {return _ede ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0073\u0074\u0072\u0069\u006b\u0065\u0054\u004c\u0042\u0052"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0073\u0074\u0072\u0069\u006b\u0065\u0054\u004c\u0042\u0052"}:_bgc .StrikeTLBR =NewCT_OnOff ();
if _fge :=d .DecodeElement (_bgc .StrikeTLBR ,&_ggc );_fge !=nil {return _fge ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0063\u0074\u0072\u006c\u0050\u0072"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0063\u0074\u0072\u006c\u0050\u0072"}:_bgc .CtrlPr =NewCT_CtrlPr ();
if _dgf :=d .DecodeElement (_bgc .CtrlPr ,&_ggc );_dgf !=nil {return _dgf ;};default:_b .Log .Debug ("\u0073\u006b\u0069\u0070\u0070\u0069n\u0067\u0020\u0075\u006e\u0073\u0075\u0070\u0070\u006f\u0072\u0074\u0065\u0064\u0020\u0065\u006c\u0065\u006d\u0065\u006et\u0020\u006f\u006e\u0020\u0043\u0054\u005f\u0042\u006f\u0072\u0064\u0065\u0072\u0042o\u0078P\u0072\u0020\u0025\u0076",_ggc .Name );
if _gbd :=d .Skip ();_gbd !=nil {return _gbd ;};};case _g .EndElement :break _aac ;case _g .CharData :};};return nil ;};func NewCT_Bar ()*CT_Bar {_fc :=&CT_Bar {};_fc .E =NewCT_OMathArg ();return _fc };func NewCT_FPr ()*CT_FPr {_bbc :=&CT_FPr {};return _bbc };
func (_bffb *CT_MathPrChoice )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {_fbeb :=start ;switch start .Name {case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0077\u0072\u0061\u0070\u0049\u006e\u0064\u0065\u006e\u0074"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0077\u0072\u0061\u0070\u0049\u006e\u0064\u0065\u006e\u0074"}:_bffb .WrapIndent =NewCT_TwipsMeasure ();
if _dcaef :=d .DecodeElement (_bffb .WrapIndent ,&_fbeb );_dcaef !=nil {return _dcaef ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0077r\u0061\u0070\u0052\u0069\u0067\u0068t"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0077r\u0061\u0070\u0052\u0069\u0067\u0068t"}:_bffb .WrapRight =NewCT_OnOff ();
if _bdgd :=d .DecodeElement (_bffb .WrapRight ,&_fbeb );_bdgd !=nil {return _bdgd ;};default:_b .Log .Debug ("\u0073\u006b\u0069\u0070\u0070\u0069\u006e\u0067\u0020\u0075\u006e\u0073\u0075\u0070\u0070\u006f\u0072\u0074e\u0064\u0020\u0065\u006c\u0065\u006d\u0065n\u0074\u0020\u006f\u006e\u0020\u0043\u0054\u005f\u004d\u0061\u0074h\u0050\u0072\u0043\u0068\u006f\u0069\u0063\u0065\u0020\u0025\u0076",_fbeb .Name );
if _gged :=d .Skip ();_gged !=nil {return _gged ;};};return nil ;};func (_gccbd ST_Shp )ValidateWithPath (path string )error {switch _gccbd {case 0,1,2:default:return _a .Errorf ("\u0025s\u003a\u0020\u006f\u0075t\u0020\u006f\u0066\u0020\u0072a\u006eg\u0065 \u0076\u0061\u006c\u0075\u0065\u0020\u0025d",path ,int (_gccbd ));
};return nil ;};func NewCT_Style ()*CT_Style {_bcaea :=&CT_Style {};return _bcaea };func (_dfead *CT_SSup )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {_dfead .E =NewCT_OMathArg ();_dfead .Sup =NewCT_OMathArg ();_edbf :for {_gadg ,_ceecd :=d .Token ();
if _ceecd !=nil {return _ceecd ;};switch _edfd :=_gadg .(type ){case _g .StartElement :switch _edfd .Name {case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0073\u0053\u0075\u0070\u0050\u0072"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0073\u0053\u0075\u0070\u0050\u0072"}:_dfead .SSupPr =NewCT_SSupPr ();
if _ceda :=d .DecodeElement (_dfead .SSupPr ,&_edfd );_ceda !=nil {return _ceda ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0065"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0065"}:if _egad :=d .DecodeElement (_dfead .E ,&_edfd );
_egad !=nil {return _egad ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0073\u0075\u0070"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0073\u0075\u0070"}:if _gaagf :=d .DecodeElement (_dfead .Sup ,&_edfd );
_gaagf !=nil {return _gaagf ;};default:_b .Log .Debug ("\u0073\u006b\u0069p\u0070\u0069\u006e\u0067\u0020\u0075\u006e\u0073\u0075\u0070\u0070\u006f\u0072\u0074\u0065\u0064\u0020\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0043T\u005f\u0053\u0053\u0075\u0070\u0020\u0025\u0076",_edfd .Name );
if _ddge :=d .Skip ();_ddge !=nil {return _ddge ;};};case _g .EndElement :break _edbf ;case _g .CharData :};};return nil ;};type CT_LimLowPr struct{CtrlPr *CT_CtrlPr ;};func (_dabgc ST_BreakBinSub )Validate ()error {return _dabgc .ValidateWithPath ("")};
func (_fecb *CT_SPrePr )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {_dfbdd :for {_fgcfb ,_eagb :=d .Token ();if _eagb !=nil {return _eagb ;};switch _bgad :=_fgcfb .(type ){case _g .StartElement :switch _bgad .Name {case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0063\u0074\u0072\u006c\u0050\u0072"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0063\u0074\u0072\u006c\u0050\u0072"}:_fecb .CtrlPr =NewCT_CtrlPr ();
if _afdcf :=d .DecodeElement (_fecb .CtrlPr ,&_bgad );_afdcf !=nil {return _afdcf ;};default:_b .Log .Debug ("\u0073k\u0069\u0070p\u0069\u006e\u0067\u0020u\u006e\u0073\u0075p\u0070\u006f\u0072\u0074\u0065\u0064\u0020\u0065\u006cem\u0065\u006e\u0074 \u006f\u006e \u0043\u0054\u005f\u0053\u0050\u0072e\u0050\u0072 \u0025\u0076",_bgad .Name );
if _cda :=d .Skip ();_cda !=nil {return _cda ;};};case _g .EndElement :break _dfbdd ;case _g .CharData :};};return nil ;};type CT_MR struct{

// Element
E []*CT_OMathArg ;};func (_bdcc *CT_LimUpp )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {e .EncodeToken (start );if _bdcc .LimUppPr !=nil {_fga :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u006c\u0069\u006d\u0055\u0070\u0070\u0050\u0072"}};
e .EncodeElement (_bdcc .LimUppPr ,_fga );};_aaee :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0065"}};e .EncodeElement (_bdcc .E ,_aaee );_edgc :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u006ci\u006d"}};e .EncodeElement (_bdcc .Lim ,_edgc );
e .EncodeToken (_g .EndElement {Name :start .Name });return nil ;};func (_dfca *CT_F )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {e .EncodeToken (start );if _dfca .FPr !=nil {_abg :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0066P\u0072"}};
e .EncodeElement (_dfca .FPr ,_abg );};_ggde :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u006eu\u006d"}};e .EncodeElement (_dfca .Num ,_ggde );_gbb :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0064e\u006e"}};e .EncodeElement (_dfca .Den ,_gbb );
e .EncodeToken (_g .EndElement {Name :start .Name });return nil ;};func NewCT_LimLoc ()*CT_LimLoc {_adbcc :=&CT_LimLoc {};_adbcc .ValAttr =ST_LimLoc (1);return _adbcc };func (_bffg *EG_OMathElementsChoice )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {_bffg .OMathMathElementsChoice =NewEG_OMathMathElementsChoice ();
_bccd :=start ;switch start .Name {case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0061\u0063\u0063"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0061\u0063\u0063"}:_bffg .OMathMathElementsChoice =NewEG_OMathMathElementsChoice ();
if _ddabc :=d .DecodeElement (&_bffg .OMathMathElementsChoice .Acc ,&_bccd );_ddabc !=nil {return _ddabc ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0062\u0061\u0072"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0062\u0061\u0072"}:_bffg .OMathMathElementsChoice =NewEG_OMathMathElementsChoice ();
if _ffbd :=d .DecodeElement (&_bffg .OMathMathElementsChoice .Bar ,&_bccd );_ffbd !=nil {return _ffbd ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0062\u006f\u0078"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0062\u006f\u0078"}:_bffg .OMathMathElementsChoice =NewEG_OMathMathElementsChoice ();
if _ddec :=d .DecodeElement (&_bffg .OMathMathElementsChoice .Box ,&_bccd );_ddec !=nil {return _ddec ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0062o\u0072\u0064\u0065\u0072\u0042\u006fx"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0062o\u0072\u0064\u0065\u0072\u0042\u006fx"}:_bffg .OMathMathElementsChoice =NewEG_OMathMathElementsChoice ();
if _gdae :=d .DecodeElement (&_bffg .OMathMathElementsChoice .BorderBox ,&_bccd );_gdae !=nil {return _gdae ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0064"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0064"}:_bffg .OMathMathElementsChoice =NewEG_OMathMathElementsChoice ();
if _fagga :=d .DecodeElement (&_bffg .OMathMathElementsChoice .D ,&_bccd );_fagga !=nil {return _fagga ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0065\u0071\u0041r\u0072"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0065\u0071\u0041r\u0072"}:_bffg .OMathMathElementsChoice =NewEG_OMathMathElementsChoice ();
if _bfbb :=d .DecodeElement (&_bffg .OMathMathElementsChoice .EqArr ,&_bccd );_bfbb !=nil {return _bfbb ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0066"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0066"}:_bffg .OMathMathElementsChoice =NewEG_OMathMathElementsChoice ();
if _bagf :=d .DecodeElement (&_bffg .OMathMathElementsChoice .F ,&_bccd );_bagf !=nil {return _bagf ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0066\u0075\u006e\u0063"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0066\u0075\u006e\u0063"}:_bffg .OMathMathElementsChoice =NewEG_OMathMathElementsChoice ();
if _agdf :=d .DecodeElement (&_bffg .OMathMathElementsChoice .Func ,&_bccd );_agdf !=nil {return _agdf ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0067\u0072\u006f\u0075\u0070\u0043\u0068\u0072"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0067\u0072\u006f\u0075\u0070\u0043\u0068\u0072"}:_bffg .OMathMathElementsChoice =NewEG_OMathMathElementsChoice ();
if _gdgdc :=d .DecodeElement (&_bffg .OMathMathElementsChoice .GroupChr ,&_bccd );_gdgdc !=nil {return _gdgdc ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u006c\u0069\u006d\u004c\u006f\u0077"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u006c\u0069\u006d\u004c\u006f\u0077"}:_bffg .OMathMathElementsChoice =NewEG_OMathMathElementsChoice ();
if _gdgc :=d .DecodeElement (&_bffg .OMathMathElementsChoice .LimLow ,&_bccd );_gdgc !=nil {return _gdgc ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u006c\u0069\u006d\u0055\u0070\u0070"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u006c\u0069\u006d\u0055\u0070\u0070"}:_bffg .OMathMathElementsChoice =NewEG_OMathMathElementsChoice ();
if _ceee :=d .DecodeElement (&_bffg .OMathMathElementsChoice .LimUpp ,&_bccd );_ceee !=nil {return _ceee ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u006d"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u006d"}:_bffg .OMathMathElementsChoice =NewEG_OMathMathElementsChoice ();
if _gcca :=d .DecodeElement (&_bffg .OMathMathElementsChoice .M ,&_bccd );_gcca !=nil {return _gcca ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u006e\u0061\u0072\u0079"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u006e\u0061\u0072\u0079"}:_bffg .OMathMathElementsChoice =NewEG_OMathMathElementsChoice ();
if _fead :=d .DecodeElement (&_bffg .OMathMathElementsChoice .Nary ,&_bccd );_fead !=nil {return _fead ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0070\u0068\u0061n\u0074"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0070\u0068\u0061n\u0074"}:_bffg .OMathMathElementsChoice =NewEG_OMathMathElementsChoice ();
if _cbadb :=d .DecodeElement (&_bffg .OMathMathElementsChoice .Phant ,&_bccd );_cbadb !=nil {return _cbadb ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0072\u0061\u0064"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0072\u0061\u0064"}:_bffg .OMathMathElementsChoice =NewEG_OMathMathElementsChoice ();
if _abgf :=d .DecodeElement (&_bffg .OMathMathElementsChoice .Rad ,&_bccd );_abgf !=nil {return _abgf ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0073\u0050\u0072\u0065"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0073\u0050\u0072\u0065"}:_bffg .OMathMathElementsChoice =NewEG_OMathMathElementsChoice ();
if _daaa :=d .DecodeElement (&_bffg .OMathMathElementsChoice .SPre ,&_bccd );_daaa !=nil {return _daaa ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0073\u0053\u0075\u0062"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0073\u0053\u0075\u0062"}:_bffg .OMathMathElementsChoice =NewEG_OMathMathElementsChoice ();
if _ffgg :=d .DecodeElement (&_bffg .OMathMathElementsChoice .SSub ,&_bccd );_ffgg !=nil {return _ffgg ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0073S\u0075\u0062\u0053\u0075\u0070"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0073S\u0075\u0062\u0053\u0075\u0070"}:_bffg .OMathMathElementsChoice =NewEG_OMathMathElementsChoice ();
if _fbacd :=d .DecodeElement (&_bffg .OMathMathElementsChoice .SSubSup ,&_bccd );_fbacd !=nil {return _fbacd ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0073\u0053\u0075\u0070"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0073\u0053\u0075\u0070"}:_bffg .OMathMathElementsChoice =NewEG_OMathMathElementsChoice ();
if _aabg :=d .DecodeElement (&_bffg .OMathMathElementsChoice .SSup ,&_bccd );_aabg !=nil {return _aabg ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0072"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0072"}:_bffg .OMathMathElementsChoice =NewEG_OMathMathElementsChoice ();
if _eaeb :=d .DecodeElement (&_bffg .OMathMathElementsChoice .R ,&_bccd );_eaeb !=nil {return _eaeb ;};default:_b .Log .Debug ("\u0073\u006b\u0069\u0070p\u0069\u006e\u0067\u0020\u0075\u006e\u0073\u0075\u0070p\u006f\u0072\u0074\u0065\u0064\u0020\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0045G\u005f\u004f\u004d\u0061\u0074h\u0045\u006c\u0065\u006d\u0065\u006e\u0074\u0073\u0043\u0068\u006f\u0069\u0063\u0065\u0020\u0025\u0076",_bccd .Name );
if _cgdb :=d .Skip ();_cgdb !=nil {return _cgdb ;};};return nil ;};func (_eeddd *EG_ScriptStyle )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {_bfd :for {_cefd ,_cgfd :=d .Token ();if _cgfd !=nil {return _cgfd ;};switch _cbgd :=_cefd .(type ){case _g .StartElement :switch _cbgd .Name {case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0073\u0063\u0072"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0073\u0063\u0072"}:_eeddd .Scr =NewCT_Script ();
if _fcdddf :=d .DecodeElement (_eeddd .Scr ,&_cbgd );_fcdddf !=nil {return _fcdddf ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0073\u0074\u0079"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0073\u0074\u0079"}:_eeddd .Sty =NewCT_Style ();
if _dgcg :=d .DecodeElement (_eeddd .Sty ,&_cbgd );_dgcg !=nil {return _dgcg ;};default:_b .Log .Debug ("\u0073\u006b\u0069\u0070\u0070\u0069n\u0067\u0020\u0075\u006e\u0073\u0075\u0070\u0070\u006f\u0072\u0074\u0065\u0064\u0020\u0065\u006c\u0065\u006d\u0065\u006et\u0020\u006f\u006e\u0020\u0045\u0047\u005f\u0053\u0063\u0072\u0069\u0070\u0074\u0053t\u0079l\u0065\u0020\u0025\u0076",_cbgd .Name );
if _beegd :=d .Skip ();_beegd !=nil {return _beegd ;};};case _g .EndElement :break _bfd ;case _g .CharData :};};return nil ;};type CT_GroupChrPr struct{

// Group Character (Grouping Character)
Chr *CT_Char ;

// Position (Group Character)
Pos *CT_TopBot ;

// Vertical Justification
VertJc *CT_TopBot ;CtrlPr *CT_CtrlPr ;};type EG_ScriptStyle struct{

// Script
Scr *CT_Script ;

// style
Sty *CT_Style ;};func (_edadf *ST_Script )UnmarshalXMLAttr (attr _g .Attr )error {switch attr .Value {case "":*_edadf =0;case "\u0072\u006f\u006da\u006e":*_edadf =1;case "\u0073\u0063\u0072\u0069\u0070\u0074":*_edadf =2;case "\u0066r\u0061\u006b\u0074\u0075\u0072":*_edadf =3;
case "\u0064\u006f\u0075\u0062\u006c\u0065\u002d\u0073\u0074\u0072\u0075\u0063\u006b":*_edadf =4;case "\u0073\u0061\u006e\u0073\u002d\u0073\u0065\u0072\u0069\u0066":*_edadf =5;case "\u006do\u006e\u006f\u0073\u0070\u0061\u0063e":*_edadf =6;};return nil ;
};func (_fgea *CT_NaryPr )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {_egecg :for {_fdfa ,_gbbf :=d .Token ();if _gbbf !=nil {return _gbbf ;};switch _bbad :=_fdfa .(type ){case _g .StartElement :switch _bbad .Name {case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0063\u0068\u0072"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0063\u0068\u0072"}:_fgea .Chr =NewCT_Char ();
if _bcgc :=d .DecodeElement (_fgea .Chr ,&_bbad );_bcgc !=nil {return _bcgc ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u006c\u0069\u006d\u004c\u006f\u0063"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u006c\u0069\u006d\u004c\u006f\u0063"}:_fgea .LimLoc =NewCT_LimLoc ();
if _gade :=d .DecodeElement (_fgea .LimLoc ,&_bbad );_gade !=nil {return _gade ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0067\u0072\u006f\u0077"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0067\u0072\u006f\u0077"}:_fgea .Grow =NewCT_OnOff ();
if _gcb :=d .DecodeElement (_fgea .Grow ,&_bbad );_gcb !=nil {return _gcb ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0073u\u0062\u0048\u0069\u0064\u0065"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0073u\u0062\u0048\u0069\u0064\u0065"}:_fgea .SubHide =NewCT_OnOff ();
if _gcdg :=d .DecodeElement (_fgea .SubHide ,&_bbad );_gcdg !=nil {return _gcdg ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0073u\u0070\u0048\u0069\u0064\u0065"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0073u\u0070\u0048\u0069\u0064\u0065"}:_fgea .SupHide =NewCT_OnOff ();
if _ggbe :=d .DecodeElement (_fgea .SupHide ,&_bbad );_ggbe !=nil {return _ggbe ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0063\u0074\u0072\u006c\u0050\u0072"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0063\u0074\u0072\u006c\u0050\u0072"}:_fgea .CtrlPr =NewCT_CtrlPr ();
if _ead :=d .DecodeElement (_fgea .CtrlPr ,&_bbad );_ead !=nil {return _ead ;};default:_b .Log .Debug ("\u0073k\u0069\u0070p\u0069\u006e\u0067\u0020u\u006e\u0073\u0075p\u0070\u006f\u0072\u0074\u0065\u0064\u0020\u0065\u006cem\u0065\u006e\u0074 \u006f\u006e \u0043\u0054\u005f\u004e\u0061\u0072y\u0050\u0072 \u0025\u0076",_bbad .Name );
if _becg :=d .Skip ();_becg !=nil {return _becg ;};};case _g .EndElement :break _egecg ;case _g .CharData :};};return nil ;};func (_adadf *CT_RPR )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {_geaa :for {_fcbg ,_gcdff :=d .Token ();if _gcdff !=nil {return _gcdff ;
};switch _ggba :=_fcbg .(type ){case _g .StartElement :switch _ggba .Name {case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u006c\u0069\u0074"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u006c\u0069\u0074"}:_adadf .Lit =NewCT_OnOff ();
if _cgca :=d .DecodeElement (_adadf .Lit ,&_ggba );_cgca !=nil {return _cgca ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u006e\u006f\u0072"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u006e\u006f\u0072"}:_adadf .Nor =NewCT_OnOff ();
if _bad :=d .DecodeElement (_adadf .Nor ,&_ggba );_bad !=nil {return _bad ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0062\u0072\u006b"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0062\u0072\u006b"}:_adadf .Brk =NewCT_ManualBreak ();
if _ecab :=d .DecodeElement (_adadf .Brk ,&_ggba );_ecab !=nil {return _ecab ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0061\u006c\u006e"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0061\u006c\u006e"}:_adadf .Aln =NewCT_OnOff ();
if _dfae :=d .DecodeElement (_adadf .Aln ,&_ggba );_dfae !=nil {return _dfae ;};default:_b .Log .Debug ("\u0073\u006b\u0069\u0070\u0070i\u006e\u0067\u0020\u0075\u006e\u0073\u0075\u0070\u0070\u006f\u0072\u0074\u0065d\u0020\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0043\u0054\u005f\u0052\u0050\u0052\u0020\u0025\u0076",_ggba .Name );
if _fcde :=d .Skip ();_fcde !=nil {return _fcde ;};};case _g .EndElement :break _geaa ;case _g .CharData :};};return nil ;};func (_cfad *CT_F )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {_cfad .Num =NewCT_OMathArg ();_cfad .Den =NewCT_OMathArg ();
_ffdf :for {_cef ,_ebc :=d .Token ();if _ebc !=nil {return _ebc ;};switch _egaa :=_cef .(type ){case _g .StartElement :switch _egaa .Name {case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0066\u0050\u0072"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0066\u0050\u0072"}:_cfad .FPr =NewCT_FPr ();
if _cfed :=d .DecodeElement (_cfad .FPr ,&_egaa );_cfed !=nil {return _cfed ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u006e\u0075\u006d"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u006e\u0075\u006d"}:if _cede :=d .DecodeElement (_cfad .Num ,&_egaa );
_cede !=nil {return _cede ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0064\u0065\u006e"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0064\u0065\u006e"}:if _efbg :=d .DecodeElement (_cfad .Den ,&_egaa );
_efbg !=nil {return _efbg ;};default:_b .Log .Debug ("\u0073\u006b\u0069\u0070\u0070\u0069\u006e\u0067\u0020\u0075\u006e\u0073\u0075p\u0070\u006f\u0072\u0074\u0065\u0064 \u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0043\u0054_\u0046\u0020\u0025\u0076",_egaa .Name );
if _fdbb :=d .Skip ();_fdbb !=nil {return _fdbb ;};};case _g .EndElement :break _ffdf ;case _g .CharData :};};return nil ;};

// ValidateWithPath validates the CT_D and its children, prefixing error messages with path
func (_cbf *CT_D )ValidateWithPath (path string )error {if _cbf .DPr !=nil {if _gdg :=_cbf .DPr .ValidateWithPath (path +"\u002f\u0044\u0050\u0072");_gdg !=nil {return _gdg ;};};for _gecc ,_cfec :=range _cbf .E {if _cdgd :=_cfec .ValidateWithPath (_a .Sprintf ("\u0025\u0073\u002f\u0045\u005b\u0025\u0064\u005d",path ,_gecc ));
_cdgd !=nil {return _cdgd ;};};return nil ;};func NewCT_SSub ()*CT_SSub {_gcecg :=&CT_SSub {};_gcecg .E =NewCT_OMathArg ();_gcecg .Sub =NewCT_OMathArg ();return _gcecg ;};type CT_F struct{

// Fraction Properties
FPr *CT_FPr ;

// Numerator
Num *CT_OMathArg ;

// Denominator
Den *CT_OMathArg ;};type CT_SPre struct{

// Pre-Sub-Superscript Properties
SPrePr *CT_SPrePr ;

// Subscript (Pre-Sub-Superscript)
Sub *CT_OMathArg ;

// Superscript(Pre-Sub-Superscript function)
Sup *CT_OMathArg ;

// Base
E *CT_OMathArg ;};type ST_Script byte ;func (_fabe *CT_OMathPara )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {_bfbc :for {_beaf ,_gecf :=d .Token ();if _gecf !=nil {return _gecf ;};switch _baag :=_beaf .(type ){case _g .StartElement :switch _baag .Name {case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"o\u004d\u0061\u0074\u0068\u0050\u0061\u0072\u0061\u0050\u0072"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"o\u004d\u0061\u0074\u0068\u0050\u0061\u0072\u0061\u0050\u0072"}:_fabe .OMathParaPr =NewCT_OMathParaPr ();
if _eabc :=d .DecodeElement (_fabe .OMathParaPr ,&_baag );_eabc !=nil {return _eabc ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u006f\u004d\u0061t\u0068"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u006f\u004d\u0061t\u0068"}:_cbeg :=NewCT_OMath ();
if _abb :=d .DecodeElement (_cbeg ,&_baag );_abb !=nil {return _abb ;};_fabe .OMath =append (_fabe .OMath ,_cbeg );default:_b .Log .Debug ("s\u006b\u0069\u0070\u0070\u0069\u006e\u0067\u0020\u0075n\u0073\u0075\u0070\u0070\u006f\u0072\u0074ed\u0020\u0065\u006c\u0065m\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0043\u0054_O\u004d\u0061t\u0068\u0050\u0061\u0072\u0061\u0020\u0025\u0076",_baag .Name );
if _bfbg :=d .Skip ();_bfbg !=nil {return _bfbg ;};};case _g .EndElement :break _bfbc ;case _g .CharData :};};return nil ;};func (_eccf ST_Style )Validate ()error {return _eccf .ValidateWithPath ("")};

// Validate validates the OMathPara and its children
func (_bccdf *OMathPara )Validate ()error {return _bccdf .ValidateWithPath ("\u004fM\u0061\u0074\u0068\u0050\u0061\u0072a");};type CT_LimUpp struct{

// Upper-Limit Properties
LimUppPr *CT_LimUppPr ;

// Base
E *CT_OMathArg ;

// Limit (Upper)
Lim *CT_OMathArg ;};type EG_OMathElementsChoice struct{OMathMathElementsChoice *EG_OMathMathElementsChoice ;};func (_efe *CT_Box )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {_efe .E =NewCT_OMathArg ();_dca :for {_ec ,_cbd :=d .Token ();
if _cbd !=nil {return _cbd ;};switch _abcf :=_ec .(type ){case _g .StartElement :switch _abcf .Name {case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0062\u006f\u0078P\u0072"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0062\u006f\u0078P\u0072"}:_efe .BoxPr =NewCT_BoxPr ();
if _gcdf :=d .DecodeElement (_efe .BoxPr ,&_abcf );_gcdf !=nil {return _gcdf ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0065"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0065"}:if _dfg :=d .DecodeElement (_efe .E ,&_abcf );
_dfg !=nil {return _dfg ;};default:_b .Log .Debug ("\u0073\u006b\u0069\u0070\u0070i\u006e\u0067\u0020\u0075\u006e\u0073\u0075\u0070\u0070\u006f\u0072\u0074\u0065d\u0020\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0043\u0054\u005f\u0042\u006f\u0078\u0020\u0025\u0076",_abcf .Name );
if _gcg :=d .Skip ();_gcg !=nil {return _gcg ;};};case _g .EndElement :break _dca ;case _g .CharData :};};return nil ;};

// Validate validates the CT_YAlign and its children
func (_cgfe *CT_YAlign )Validate ()error {return _cgfe .ValidateWithPath ("\u0043T\u005f\u0059\u0041\u006c\u0069\u0067n");};

// Validate validates the CT_Func and its children
func (_bdbb *CT_Func )Validate ()error {return _bdbb .ValidateWithPath ("\u0043T\u005f\u0046\u0075\u006e\u0063");};func (_dgfd *CT_SSubSupPr )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {_gae :for {_ddgg ,_bfgb :=d .Token ();if _bfgb !=nil {return _bfgb ;
};switch _ffdfb :=_ddgg .(type ){case _g .StartElement :switch _ffdfb .Name {case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0061\u006c\u006e\u0053\u0063\u0072"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0061\u006c\u006e\u0053\u0063\u0072"}:_dgfd .AlnScr =NewCT_OnOff ();
if _edafg :=d .DecodeElement (_dgfd .AlnScr ,&_ffdfb );_edafg !=nil {return _edafg ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0063\u0074\u0072\u006c\u0050\u0072"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0063\u0074\u0072\u006c\u0050\u0072"}:_dgfd .CtrlPr =NewCT_CtrlPr ();
if _ebfb :=d .DecodeElement (_dgfd .CtrlPr ,&_ffdfb );_ebfb !=nil {return _ebfb ;};default:_b .Log .Debug ("s\u006b\u0069\u0070\u0070\u0069\u006e\u0067\u0020\u0075n\u0073\u0075\u0070\u0070\u006f\u0072\u0074ed\u0020\u0065\u006c\u0065m\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0043\u0054_S\u0053\u0075b\u0053\u0075\u0070\u0050\u0072\u0020\u0025\u0076",_ffdfb .Name );
if _fdaef :=d .Skip ();_fdaef !=nil {return _fdaef ;};};case _g .EndElement :break _gae ;case _g .CharData :};};return nil ;};const (ST_StyleUnset ST_Style =0;ST_StyleP ST_Style =1;ST_StyleB ST_Style =2;ST_StyleI ST_Style =3;ST_StyleBi ST_Style =4;);

// ValidateWithPath validates the CT_String and its children, prefixing error messages with path
func (_debcc *CT_String )ValidateWithPath (path string )error {return nil };

// Validate validates the CT_AccPr and its children
func (_ged *CT_AccPr )Validate ()error {return _ged .ValidateWithPath ("\u0043\u0054\u005f\u0041\u0063\u0063\u0050\u0072");};

// ValidateWithPath validates the CT_Bar and its children, prefixing error messages with path
func (_dce *CT_Bar )ValidateWithPath (path string )error {if _dce .BarPr !=nil {if _dda :=_dce .BarPr .ValidateWithPath (path +"\u002f\u0042\u0061\u0072\u0050\u0072");_dda !=nil {return _dda ;};};if _acc :=_dce .E .ValidateWithPath (path +"\u002f\u0045");
_acc !=nil {return _acc ;};return nil ;};func (_gddba ST_LimLoc )String ()string {switch _gddba {case 0:return "";case 1:return "\u0075\u006e\u0064\u004f\u0076\u0072";case 2:return "\u0073\u0075\u0062\u0053\u0075\u0070";};return "";};func (_gee *CT_BreakBin )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {if _gee .ValAttr !=ST_BreakBinUnset {_aad ,_eee :=_gee .ValAttr .MarshalXMLAttr (_g .Name {Local :"\u006d\u003a\u0076a\u006c"});
if _eee !=nil {return _eee ;};start .Attr =append (start .Attr ,_aad );};e .EncodeToken (start );e .EncodeToken (_g .EndElement {Name :start .Name });return nil ;};type CT_CtrlPr struct{};func (_fdab *ST_FType )UnmarshalXMLAttr (attr _g .Attr )error {switch attr .Value {case "":*_fdab =0;
case "\u0062\u0061\u0072":*_fdab =1;case "\u0073\u006b\u0077":*_fdab =2;case "\u006c\u0069\u006e":*_fdab =3;case "\u006e\u006f\u0042a\u0072":*_fdab =4;};return nil ;};func (_dd *CT_AccPr )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {_afg :for {_adb ,_dga :=d .Token ();
if _dga !=nil {return _dga ;};switch _cf :=_adb .(type ){case _g .StartElement :switch _cf .Name {case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0063\u0068\u0072"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0063\u0068\u0072"}:_dd .Chr =NewCT_Char ();
if _dc :=d .DecodeElement (_dd .Chr ,&_cf );_dc !=nil {return _dc ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0063\u0074\u0072\u006c\u0050\u0072"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0063\u0074\u0072\u006c\u0050\u0072"}:_dd .CtrlPr =NewCT_CtrlPr ();
if _ba :=d .DecodeElement (_dd .CtrlPr ,&_cf );_ba !=nil {return _ba ;};default:_b .Log .Debug ("\u0073\u006b\u0069\u0070\u0070\u0069\u006eg\u0020\u0075\u006es\u0075\u0070\u0070\u006fr\u0074\u0065\u0064\u0020\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0043\u0054\u005f\u0041\u0063\u0063\u0050\u0072\u0020\u0025\u0076",_cf .Name );
if _fff :=d .Skip ();_fff !=nil {return _fff ;};};case _g .EndElement :break _afg ;case _g .CharData :};};return nil ;};

// ValidateWithPath validates the CT_OMath and its children, prefixing error messages with path
func (_gbc *CT_OMath )ValidateWithPath (path string )error {for _faf ,_efae :=range _gbc .EG_OMathElements {if _bcgd :=_efae .ValidateWithPath (_a .Sprintf ("\u0025\u0073\u002fEG\u005f\u004f\u004d\u0061\u0074\u0068\u0045\u006c\u0065\u006d\u0065\u006e\u0074\u0073\u005b\u0025\u0064\u005d",path ,_faf ));
_bcgd !=nil {return _bcgd ;};};return nil ;};func NewCT_OnOff ()*CT_OnOff {_ecac :=&CT_OnOff {};return _ecac };func NewCT_SSubPr ()*CT_SSubPr {_fbab :=&CT_SSubPr {};return _fbab };

// ValidateWithPath validates the EG_OMathElements and its children, prefixing error messages with path
func (_bedg *EG_OMathElements )ValidateWithPath (path string )error {if _afea :=_bedg .OMathElementsChoice .ValidateWithPath (path +"/\u004fM\u0061\u0074\u0068\u0045\u006c\u0065\u006d\u0065n\u0074\u0073\u0043\u0068oi\u0063\u0065");_afea !=nil {return _afea ;
};return nil ;};

// ValidateWithPath validates the CT_Script and its children, prefixing error messages with path
func (_ffg *CT_Script )ValidateWithPath (path string )error {if _ffdbf :=_ffg .ValAttr .ValidateWithPath (path +"\u002f\u0056\u0061\u006c\u0041\u0074\u0074\u0072");_ffdbf !=nil {return _ffdbf ;};return nil ;};

// ValidateWithPath validates the CT_Shp and its children, prefixing error messages with path
func (_fabc *CT_Shp )ValidateWithPath (path string )error {if _fabc .ValAttr ==ST_ShpUnset {return _a .Errorf ("\u0025\u0073\u002fV\u0061\u006c\u0041\u0074t\u0072\u0020\u0069\u0073\u0020\u0061\u0020m\u0061\u006e\u0064\u0061\u0074\u006f\u0072\u0079\u0020\u0066\u0069\u0065\u006c\u0064",path );
};if _aaba :=_fabc .ValAttr .ValidateWithPath (path +"\u002f\u0056\u0061\u006c\u0041\u0074\u0074\u0072");_aaba !=nil {return _aaba ;};return nil ;};func (_eaed *CT_OMathJc )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {for _ ,_caa :=range start .Attr {if _caa .Name .Local =="\u0076\u0061\u006c"{_eaed .ValAttr .UnmarshalXMLAttr (_caa );
continue ;};};for {_gddf ,_gaag :=d .Token ();if _gaag !=nil {return _a .Errorf ("\u0070\u0061\u0072\u0073in\u0067\u0020\u0043\u0054\u005f\u004f\u004d\u0061\u0074\u0068\u004a\u0063\u003a\u0020%\u0073",_gaag );};if _bea ,_faaf :=_gddf .(_g .EndElement );
_faaf &&_bea .Name ==start .Name {break ;};};return nil ;};type CT_BreakBin struct{

// Value
ValAttr ST_BreakBin ;};func (_fcff *CT_OnOff )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {if _fcff .ValAttr !=nil {start .Attr =append (start .Attr ,_g .Attr {Name :_g .Name {Local :"\u006d\u003a\u0076a\u006c"},Value :_a .Sprintf ("\u0025\u0076",*_fcff .ValAttr )});
};e .EncodeToken (start );e .EncodeToken (_g .EndElement {Name :start .Name });return nil ;};func (_geee ST_Script )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {return e .EncodeElement (_geee .String (),start );};

// ValidateWithPath validates the CT_LimLowPr and its children, prefixing error messages with path
func (_egffe *CT_LimLowPr )ValidateWithPath (path string )error {if _egffe .CtrlPr !=nil {if _cfbe :=_egffe .CtrlPr .ValidateWithPath (path +"\u002fC\u0074\u0072\u006c\u0050\u0072");_cfbe !=nil {return _cfbe ;};};return nil ;};

// Validate validates the CT_Text and its children
func (_fafb *CT_Text )Validate ()error {return _fafb .ValidateWithPath ("\u0043T\u005f\u0054\u0065\u0078\u0074");};func ParseUnionST_TwipsMeasure (s string )(_ab .ST_TwipsMeasure ,error ){_ccged :=_ab .ST_TwipsMeasure {};if _ab .ST_PositiveUniversalMeasurePatternRe .MatchString (s ){_ccged .ST_PositiveUniversalMeasure =&s ;
}else {_cbbba ,_ddaed :=_f .ParseFloat (s ,64);if _ddaed !=nil {return _ccged ,_a .Errorf ("\u0070\u0061\u0072\u0073in\u0067\u0020\u0025\u0073\u0020\u0061\u0073\u0020\u0075\u0069\u006e\u0074\u003a\u0020%\u0073",s ,_ddaed );};_ccged .ST_UnsignedDecimalNumber =_ad .Uint64 (uint64 (_cbbba ));
};return _ccged ,nil ;};func (_bdccb *CT_MCPr )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {_aafb :for {_bcbb ,_bfac :=d .Token ();if _bfac !=nil {return _bfac ;};switch _ggaf :=_bcbb .(type ){case _g .StartElement :switch _ggaf .Name {case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0063\u006f\u0075n\u0074"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0063\u006f\u0075n\u0074"}:_bdccb .Count =NewCT_Integer255 ();
if _bgfc :=d .DecodeElement (_bdccb .Count ,&_ggaf );_bgfc !=nil {return _bgfc ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u006d\u0063\u004a\u0063"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u006d\u0063\u004a\u0063"}:_bdccb .McJc =NewCT_XAlign ();
if _bdcf :=d .DecodeElement (_bdccb .McJc ,&_ggaf );_bdcf !=nil {return _bdcf ;};default:_b .Log .Debug ("\u0073\u006b\u0069p\u0070\u0069\u006e\u0067\u0020\u0075\u006e\u0073\u0075\u0070\u0070\u006f\u0072\u0074\u0065\u0064\u0020\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0043T\u005f\u004d\u0043\u0050\u0072\u0020\u0025\u0076",_ggaf .Name );
if _bbg :=d .Skip ();_bbg !=nil {return _bbg ;};};case _g .EndElement :break _aafb ;case _g .CharData :};};return nil ;};type CT_BarPr struct{

// Position
Pos *CT_TopBot ;CtrlPr *CT_CtrlPr ;};func NewCT_SpacingRule ()*CT_SpacingRule {_bgee :=&CT_SpacingRule {};_bgee .ValAttr =0;return _bgee };

// Validate validates the CT_Box and its children
func (_agc *CT_Box )Validate ()error {return _agc .ValidateWithPath ("\u0043\u0054\u005f\u0042\u006f\u0078");};func (_bbcf ST_TopBot )String ()string {switch _bbcf {case 0:return "";case 1:return "\u0074\u006f\u0070";case 2:return "\u0062\u006f\u0074";
};return "";};

// Validate validates the EG_OMathMathElements and its children
func (_bbcg *EG_OMathMathElements )Validate ()error {return _bbcg .ValidateWithPath ("E\u0047_\u004f\u004d\u0061\u0074\u0068\u004d\u0061\u0074h\u0045\u006c\u0065\u006den\u0074\u0073");};func (_fed *CT_EqArrPr )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {e .EncodeToken (start );
if _fed .BaseJc !=nil {_dgfa :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0062\u0061\u0073\u0065\u004a\u0063"}};e .EncodeElement (_fed .BaseJc ,_dgfa );};if _fed .MaxDist !=nil {_cgab :=_g .StartElement {Name :_g .Name {Local :"\u006d:\u006d\u0061\u0078\u0044\u0069\u0073t"}};
e .EncodeElement (_fed .MaxDist ,_cgab );};if _fed .ObjDist !=nil {_dfdd :=_g .StartElement {Name :_g .Name {Local :"\u006d:\u006f\u0062\u006a\u0044\u0069\u0073t"}};e .EncodeElement (_fed .ObjDist ,_dfdd );};if _fed .RSpRule !=nil {_gegc :=_g .StartElement {Name :_g .Name {Local :"\u006d:\u0072\u0053\u0070\u0052\u0075\u006ce"}};
e .EncodeElement (_fed .RSpRule ,_gegc );};if _fed .RSp !=nil {_efa :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0072S\u0070"}};e .EncodeElement (_fed .RSp ,_efa );};if _fed .CtrlPr !=nil {_cffg :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0063\u0074\u0072\u006c\u0050\u0072"}};
e .EncodeElement (_fed .CtrlPr ,_cffg );};e .EncodeToken (_g .EndElement {Name :start .Name });return nil ;};func (_bgfcg ST_BreakBin )MarshalXMLAttr (name _g .Name )(_g .Attr ,error ){_dfad :=_g .Attr {};_dfad .Name =name ;switch _bgfcg {case ST_BreakBinUnset :_dfad .Value ="";
case ST_BreakBinBefore :_dfad .Value ="\u0062\u0065\u0066\u006f\u0072\u0065";case ST_BreakBinAfter :_dfad .Value ="\u0061\u0066\u0074e\u0072";case ST_BreakBinRepeat :_dfad .Value ="\u0072\u0065\u0070\u0065\u0061\u0074";};return _dfad ,nil ;};func (_adea ST_Style )String ()string {switch _adea {case 0:return "";
case 1:return "\u0070";case 2:return "\u0062";case 3:return "\u0069";case 4:return "\u0062\u0069";};return "";};type CT_D struct{

// Delimiter Properties
DPr *CT_DPr ;

// Base
E []*CT_OMathArg ;};

// Validate validates the CT_LimUpp and its children
func (_eacf *CT_LimUpp )Validate ()error {return _eacf .ValidateWithPath ("\u0043T\u005f\u004c\u0069\u006d\u0055\u0070p");};func (_gdefg *CT_UnSignedInteger )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {for _ ,_edaaa :=range start .Attr {if _edaaa .Name .Local =="\u0076\u0061\u006c"{_gbce ,_ffdfg :=_f .ParseUint (_edaaa .Value ,10,32);
if _ffdfg !=nil {return _ffdfg ;};_gdefg .ValAttr =uint32 (_gbce );continue ;};};for {_aedfc ,_adgd :=d .Token ();if _adgd !=nil {return _a .Errorf ("\u0070\u0061\u0072\u0073\u0069\u006e\u0067\u0020\u0043\u0054_\u0055\u006e\u0053\u0069\u0067\u006e\u0065d\u0049\u006e\u0074\u0065\u0067\u0065\u0072\u003a\u0020\u0025\u0073",_adgd );
};if _geecb ,_dbga :=_aedfc .(_g .EndElement );_dbga &&_geecb .Name ==start .Name {break ;};};return nil ;};

// ValidateWithPath validates the CT_CtrlPr and its children, prefixing error messages with path
func (_aba *CT_CtrlPr )ValidateWithPath (path string )error {return nil };

// Validate validates the CT_LimLowPr and its children
func (_add *CT_LimLowPr )Validate ()error {return _add .ValidateWithPath ("C\u0054\u005f\u004c\u0069\u006d\u004c\u006f\u0077\u0050\u0072");};type CT_SPrePr struct{CtrlPr *CT_CtrlPr ;};type ST_Shp byte ;func (_gaae ST_Jc )ValidateWithPath (path string )error {switch _gaae {case 0,1,2,3,4:default:return _a .Errorf ("\u0025s\u003a\u0020\u006f\u0075t\u0020\u006f\u0066\u0020\u0072a\u006eg\u0065 \u0076\u0061\u006c\u0075\u0065\u0020\u0025d",path ,int (_gaae ));
};return nil ;};func (_fgc *CT_Char )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {for _ ,_bdc :=range start .Attr {if _bdc .Name .Local =="\u0076\u0061\u006c"{_dbgd :=_bdc .Value ;_fgc .ValAttr =_dbgd ;continue ;};};for {_dcaf ,_gda :=d .Token ();
if _gda !=nil {return _a .Errorf ("\u0070\u0061\u0072\u0073in\u0067\u0020\u0043\u0054\u005f\u0043\u0068\u0061\u0072\u003a\u0020\u0025\u0073",_gda );};if _geec ,_gge :=_dcaf .(_g .EndElement );_gge &&_geec .Name ==start .Name {break ;};};return nil ;};func (_bcb *CT_BoxPr )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {_cgd :for {_caf ,_edc :=d .Token ();
if _edc !=nil {return _edc ;};switch _egb :=_caf .(type ){case _g .StartElement :switch _egb .Name {case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u006f\u0070\u0045m\u0075"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u006f\u0070\u0045m\u0075"}:_bcb .OpEmu =NewCT_OnOff ();
if _aacc :=d .DecodeElement (_bcb .OpEmu ,&_egb );_aacc !=nil {return _aacc ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u006eo\u0042\u0072\u0065\u0061\u006b"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u006eo\u0042\u0072\u0065\u0061\u006b"}:_bcb .NoBreak =NewCT_OnOff ();
if _afgg :=d .DecodeElement (_bcb .NoBreak ,&_egb );_afgg !=nil {return _afgg ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0064\u0069\u0066\u0066"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0064\u0069\u0066\u0066"}:_bcb .Diff =NewCT_OnOff ();
if _dcad :=d .DecodeElement (_bcb .Diff ,&_egb );_dcad !=nil {return _dcad ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0062\u0072\u006b"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0062\u0072\u006b"}:_bcb .Brk =NewCT_ManualBreak ();
if _fgeg :=d .DecodeElement (_bcb .Brk ,&_egb );_fgeg !=nil {return _fgeg ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0061\u006c\u006e"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0061\u006c\u006e"}:_bcb .Aln =NewCT_OnOff ();
if _cbc :=d .DecodeElement (_bcb .Aln ,&_egb );_cbc !=nil {return _cbc ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0063\u0074\u0072\u006c\u0050\u0072"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0063\u0074\u0072\u006c\u0050\u0072"}:_bcb .CtrlPr =NewCT_CtrlPr ();
if _cbb :=d .DecodeElement (_bcb .CtrlPr ,&_egb );_cbb !=nil {return _cbb ;};default:_b .Log .Debug ("\u0073\u006b\u0069\u0070\u0070\u0069\u006eg\u0020\u0075\u006es\u0075\u0070\u0070\u006fr\u0074\u0065\u0064\u0020\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0043\u0054\u005f\u0042\u006f\u0078\u0050\u0072\u0020\u0025\u0076",_egb .Name );
if _bgb :=d .Skip ();_bgb !=nil {return _bgb ;};};case _g .EndElement :break _cgd ;case _g .CharData :};};return nil ;};type CT_MathPr struct{

// Math Font
MathFont *CT_String ;

// Break on Binary Operators
BrkBin *CT_BreakBin ;

// Break on Binary Subtraction
BrkBinSub *CT_BreakBinSub ;

// Small Fraction
SmallFrac *CT_OnOff ;

// Use Display Math Defaults
DispDef *CT_OnOff ;

// Left Margin
LMargin *CT_TwipsMeasure ;

// Right Margin
RMargin *CT_TwipsMeasure ;

// Default Justification
DefJc *CT_OMathJc ;

// Pre-Paragraph Spacing
PreSp *CT_TwipsMeasure ;

// Post-Paragraph Spacing
PostSp *CT_TwipsMeasure ;

// Inter-Equation Spacing
InterSp *CT_TwipsMeasure ;

// Intra-Equation Spacing
IntraSp *CT_TwipsMeasure ;MathPrChoice *CT_MathPrChoice ;

// Integral Limit Locations
IntLim *CT_LimLoc ;

// n-ary Limit Location
NaryLim *CT_LimLoc ;};func (_gfedc ST_Jc )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {return e .EncodeElement (_gfedc .String (),start );};func NewCT_Func ()*CT_Func {_eef :=&CT_Func {};_eef .FName =NewCT_OMathArg ();_eef .E =NewCT_OMathArg ();
return _eef ;};func (_bbcda ST_Style )ValidateWithPath (path string )error {switch _bbcda {case 0,1,2,3,4:default:return _a .Errorf ("\u0025s\u003a\u0020\u006f\u0075t\u0020\u006f\u0066\u0020\u0072a\u006eg\u0065 \u0076\u0061\u006c\u0075\u0065\u0020\u0025d",path ,int (_bbcda ));
};return nil ;};

// ValidateWithPath validates the MathPr and its children, prefixing error messages with path
func (_dcadg *MathPr )ValidateWithPath (path string )error {if _edfde :=_dcadg .CT_MathPr .ValidateWithPath (path );_edfde !=nil {return _edfde ;};return nil ;};

// Validate validates the CT_SSupPr and its children
func (_gcgd *CT_SSupPr )Validate ()error {return _gcgd .ValidateWithPath ("\u0043T\u005f\u0053\u0053\u0075\u0070\u0050r");};type CT_FPr struct{

// Fraction type
Type *CT_FType ;CtrlPr *CT_CtrlPr ;};

// Validate validates the CT_NaryPr and its children
func (_aecg *CT_NaryPr )Validate ()error {return _aecg .ValidateWithPath ("\u0043T\u005f\u004e\u0061\u0072\u0079\u0050r");};func (_cedab *CT_Shp )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {_fdbe ,_cafd :=_cedab .ValAttr .MarshalXMLAttr (_g .Name {Local :"\u006d\u003a\u0076a\u006c"});
if _cafd !=nil {return _cafd ;};start .Attr =append (start .Attr ,_fdbe );e .EncodeToken (start );e .EncodeToken (_g .EndElement {Name :start .Name });return nil ;};

// ValidateWithPath validates the CT_SSupPr and its children, prefixing error messages with path
func (_gebf *CT_SSupPr )ValidateWithPath (path string )error {if _gebf .CtrlPr !=nil {if _gggb :=_gebf .CtrlPr .ValidateWithPath (path +"\u002fC\u0074\u0072\u006c\u0050\u0072");_gggb !=nil {return _gggb ;};};return nil ;};

// ValidateWithPath validates the CT_BoxPr and its children, prefixing error messages with path
func (_geg *CT_BoxPr )ValidateWithPath (path string )error {if _geg .OpEmu !=nil {if _bca :=_geg .OpEmu .ValidateWithPath (path +"\u002f\u004f\u0070\u0045\u006d\u0075");_bca !=nil {return _bca ;};};if _geg .NoBreak !=nil {if _cdd :=_geg .NoBreak .ValidateWithPath (path +"\u002f\u004e\u006f\u0042\u0072\u0065\u0061\u006b");
_cdd !=nil {return _cdd ;};};if _geg .Diff !=nil {if _cfac :=_geg .Diff .ValidateWithPath (path +"\u002f\u0044\u0069f\u0066");_cfac !=nil {return _cfac ;};};if _geg .Brk !=nil {if _afge :=_geg .Brk .ValidateWithPath (path +"\u002f\u0042\u0072\u006b");_afge !=nil {return _afge ;
};};if _geg .Aln !=nil {if _baa :=_geg .Aln .ValidateWithPath (path +"\u002f\u0041\u006c\u006e");_baa !=nil {return _baa ;};};if _geg .CtrlPr !=nil {if _fa :=_geg .CtrlPr .ValidateWithPath (path +"\u002fC\u0074\u0072\u006c\u0050\u0072");_fa !=nil {return _fa ;
};};return nil ;};

// ValidateWithPath validates the CT_SSubSupPr and its children, prefixing error messages with path
func (_fcba *CT_SSubSupPr )ValidateWithPath (path string )error {if _fcba .AlnScr !=nil {if _geab :=_fcba .AlnScr .ValidateWithPath (path +"\u002fA\u006c\u006e\u0053\u0063\u0072");_geab !=nil {return _geab ;};};if _fcba .CtrlPr !=nil {if _fdgc :=_fcba .CtrlPr .ValidateWithPath (path +"\u002fC\u0074\u0072\u006c\u0050\u0072");
_fdgc !=nil {return _fdgc ;};};return nil ;};func NewCT_RadPr ()*CT_RadPr {_abfg :=&CT_RadPr {};return _abfg };

// ValidateWithPath validates the CT_TwipsMeasure and its children, prefixing error messages with path
func (_edadg *CT_TwipsMeasure )ValidateWithPath (path string )error {if _gcde :=_edadg .ValAttr .ValidateWithPath (path +"\u002f\u0056\u0061\u006c\u0041\u0074\u0074\u0072");_gcde !=nil {return _gcde ;};return nil ;};func (_eceb ST_TopBot )ValidateWithPath (path string )error {switch _eceb {case 0,1,2:default:return _a .Errorf ("\u0025s\u003a\u0020\u006f\u0075t\u0020\u006f\u0066\u0020\u0072a\u006eg\u0065 \u0076\u0061\u006c\u0075\u0065\u0020\u0025d",path ,int (_eceb ));
};return nil ;};func (_dee *CT_MPr )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {_edac :for {_acfeb ,_ebcf :=d .Token ();if _ebcf !=nil {return _ebcf ;};switch _fbbbf :=_acfeb .(type ){case _g .StartElement :switch _fbbbf .Name {case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0062\u0061\u0073\u0065\u004a\u0063"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0062\u0061\u0073\u0065\u004a\u0063"}:_dee .BaseJc =NewCT_YAlign ();
if _fbdd :=d .DecodeElement (_dee .BaseJc ,&_fbbbf );_fbdd !=nil {return _fbdd ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0070l\u0063\u0048\u0069\u0064\u0065"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0070l\u0063\u0048\u0069\u0064\u0065"}:_dee .PlcHide =NewCT_OnOff ();
if _edaf :=d .DecodeElement (_dee .PlcHide ,&_fbbbf );_edaf !=nil {return _edaf ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0072S\u0070\u0052\u0075\u006c\u0065"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0072S\u0070\u0052\u0075\u006c\u0065"}:_dee .RSpRule =NewCT_SpacingRule ();
if _efad :=d .DecodeElement (_dee .RSpRule ,&_fbbbf );_efad !=nil {return _efad ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0063G\u0070\u0052\u0075\u006c\u0065"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0063G\u0070\u0052\u0075\u006c\u0065"}:_dee .CGpRule =NewCT_SpacingRule ();
if _dgac :=d .DecodeElement (_dee .CGpRule ,&_fbbbf );_dgac !=nil {return _dgac ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0072\u0053\u0070"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0072\u0053\u0070"}:_dee .RSp =NewCT_UnSignedInteger ();
if _fcee :=d .DecodeElement (_dee .RSp ,&_fbbbf );_fcee !=nil {return _fcee ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0063\u0053\u0070"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0063\u0053\u0070"}:_dee .CSp =NewCT_UnSignedInteger ();
if _ebdf :=d .DecodeElement (_dee .CSp ,&_fbbbf );_ebdf !=nil {return _ebdf ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0063\u0047\u0070"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0063\u0047\u0070"}:_dee .CGp =NewCT_UnSignedInteger ();
if _bcaaa :=d .DecodeElement (_dee .CGp ,&_fbbbf );_bcaaa !=nil {return _bcaaa ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u006d\u0063\u0073"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u006d\u0063\u0073"}:_dee .Mcs =NewCT_MCS ();
if _bfbf :=d .DecodeElement (_dee .Mcs ,&_fbbbf );_bfbf !=nil {return _bfbf ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0063\u0074\u0072\u006c\u0050\u0072"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0063\u0074\u0072\u006c\u0050\u0072"}:_dee .CtrlPr =NewCT_CtrlPr ();
if _cbef :=d .DecodeElement (_dee .CtrlPr ,&_fbbbf );_cbef !=nil {return _cbef ;};default:_b .Log .Debug ("\u0073\u006b\u0069\u0070\u0070i\u006e\u0067\u0020\u0075\u006e\u0073\u0075\u0070\u0070\u006f\u0072\u0074\u0065d\u0020\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0043\u0054\u005f\u004d\u0050\u0072\u0020\u0025\u0076",_fbbbf .Name );
if _adae :=d .Skip ();_adae !=nil {return _adae ;};};case _g .EndElement :break _edac ;case _g .CharData :};};return nil ;};

// Validate validates the CT_SSub and its children
func (_gdbe *CT_SSub )Validate ()error {return _gdbe .ValidateWithPath ("\u0043T\u005f\u0053\u0053\u0075\u0062");};func (_afed *CT_PhantPr )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {_gea :for {_dcbb ,_ccc :=d .Token ();if _ccc !=nil {return _ccc ;
};switch _aedf :=_dcbb .(type ){case _g .StartElement :switch _aedf .Name {case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0073\u0068\u006f\u0077"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0073\u0068\u006f\u0077"}:_afed .Show =NewCT_OnOff ();
if _eeccd :=d .DecodeElement (_afed .Show ,&_aedf );_eeccd !=nil {return _eeccd ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u007ae\u0072\u006f\u0057\u0069\u0064"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u007ae\u0072\u006f\u0057\u0069\u0064"}:_afed .ZeroWid =NewCT_OnOff ();
if _abaaa :=d .DecodeElement (_afed .ZeroWid ,&_aedf );_abaaa !=nil {return _abaaa ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u007ae\u0072\u006f\u0041\u0073\u0063"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u007ae\u0072\u006f\u0041\u0073\u0063"}:_afed .ZeroAsc =NewCT_OnOff ();
if _gebc :=d .DecodeElement (_afed .ZeroAsc ,&_aedf );_gebc !=nil {return _gebc ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u007a\u0065\u0072\u006f\u0044\u0065\u0073\u0063"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u007a\u0065\u0072\u006f\u0044\u0065\u0073\u0063"}:_afed .ZeroDesc =NewCT_OnOff ();
if _fcfa :=d .DecodeElement (_afed .ZeroDesc ,&_aedf );_fcfa !=nil {return _fcfa ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0074\u0072\u0061\u006e\u0073\u0070"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0074\u0072\u0061\u006e\u0073\u0070"}:_afed .Transp =NewCT_OnOff ();
if _fgbf :=d .DecodeElement (_afed .Transp ,&_aedf );_fgbf !=nil {return _fgbf ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0063\u0074\u0072\u006c\u0050\u0072"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0063\u0074\u0072\u006c\u0050\u0072"}:_afed .CtrlPr =NewCT_CtrlPr ();
if _agff :=d .DecodeElement (_afed .CtrlPr ,&_aedf );_agff !=nil {return _agff ;};default:_b .Log .Debug ("\u0073k\u0069\u0070p\u0069\u006e\u0067 \u0075\u006e\u0073\u0075\u0070\u0070\u006fr\u0074\u0065\u0064\u0020\u0065\u006ce\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0043\u0054\u005fP\u0068\u0061\u006e\u0074\u0050\u0072\u0020\u0025\u0076",_aedf .Name );
if _bcec :=d .Skip ();_bcec !=nil {return _bcec ;};};case _g .EndElement :break _gea ;case _g .CharData :};};return nil ;};func (_fgfb *CT_MathPr )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {_aeeb :for {_aeg ,_fabd :=d .Token ();if _fabd !=nil {return _fabd ;
};switch _cbcg :=_aeg .(type ){case _g .StartElement :switch _cbcg .Name {case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u006d\u0061\u0074\u0068\u0046\u006f\u006e\u0074"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u006d\u0061\u0074\u0068\u0046\u006f\u006e\u0074"}:_fgfb .MathFont =NewCT_String ();
if _bbdd :=d .DecodeElement (_fgfb .MathFont ,&_cbcg );_bbdd !=nil {return _bbdd ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0062\u0072\u006b\u0042\u0069\u006e"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0062\u0072\u006b\u0042\u0069\u006e"}:_fgfb .BrkBin =NewCT_BreakBin ();
if _fbdg :=d .DecodeElement (_fgfb .BrkBin ,&_cbcg );_fbdg !=nil {return _fbdg ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0062r\u006b\u0042\u0069\u006e\u0053\u0075b"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0062r\u006b\u0042\u0069\u006e\u0053\u0075b"}:_fgfb .BrkBinSub =NewCT_BreakBinSub ();
if _fdff :=d .DecodeElement (_fgfb .BrkBinSub ,&_cbcg );_fdff !=nil {return _fdff ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0073m\u0061\u006c\u006c\u0046\u0072\u0061c"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0073m\u0061\u006c\u006c\u0046\u0072\u0061c"}:_fgfb .SmallFrac =NewCT_OnOff ();
if _gedf :=d .DecodeElement (_fgfb .SmallFrac ,&_cbcg );_gedf !=nil {return _gedf ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0064i\u0073\u0070\u0044\u0065\u0066"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0064i\u0073\u0070\u0044\u0065\u0066"}:_fgfb .DispDef =NewCT_OnOff ();
if _gefd :=d .DecodeElement (_fgfb .DispDef ,&_cbcg );_gefd !=nil {return _gefd ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u006cM\u0061\u0072\u0067\u0069\u006e"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u006cM\u0061\u0072\u0067\u0069\u006e"}:_fgfb .LMargin =NewCT_TwipsMeasure ();
if _accb :=d .DecodeElement (_fgfb .LMargin ,&_cbcg );_accb !=nil {return _accb ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0072M\u0061\u0072\u0067\u0069\u006e"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0072M\u0061\u0072\u0067\u0069\u006e"}:_fgfb .RMargin =NewCT_TwipsMeasure ();
if _dfaa :=d .DecodeElement (_fgfb .RMargin ,&_cbcg );_dfaa !=nil {return _dfaa ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0064\u0065\u0066J\u0063"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0064\u0065\u0066J\u0063"}:_fgfb .DefJc =NewCT_OMathJc ();
if _fbge :=d .DecodeElement (_fgfb .DefJc ,&_cbcg );_fbge !=nil {return _fbge ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0070\u0072\u0065S\u0070"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0070\u0072\u0065S\u0070"}:_fgfb .PreSp =NewCT_TwipsMeasure ();
if _ccaf :=d .DecodeElement (_fgfb .PreSp ,&_cbcg );_ccaf !=nil {return _ccaf ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0070\u006f\u0073\u0074\u0053\u0070"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0070\u006f\u0073\u0074\u0053\u0070"}:_fgfb .PostSp =NewCT_TwipsMeasure ();
if _dfbf :=d .DecodeElement (_fgfb .PostSp ,&_cbcg );_dfbf !=nil {return _dfbf ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0069n\u0074\u0065\u0072\u0053\u0070"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0069n\u0074\u0065\u0072\u0053\u0070"}:_fgfb .InterSp =NewCT_TwipsMeasure ();
if _gaf :=d .DecodeElement (_fgfb .InterSp ,&_cbcg );_gaf !=nil {return _gaf ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0069n\u0074\u0072\u0061\u0053\u0070"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0069n\u0074\u0072\u0061\u0053\u0070"}:_fgfb .IntraSp =NewCT_TwipsMeasure ();
if _feabg :=d .DecodeElement (_fgfb .IntraSp ,&_cbcg );_feabg !=nil {return _feabg ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0077\u0072\u0061\u0070\u0049\u006e\u0064\u0065\u006e\u0074"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0077\u0072\u0061\u0070\u0049\u006e\u0064\u0065\u006e\u0074"}:_fgfb .MathPrChoice =NewCT_MathPrChoice ();
if _acea :=d .DecodeElement (&_fgfb .MathPrChoice .WrapIndent ,&_cbcg );_acea !=nil {return _acea ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0077r\u0061\u0070\u0052\u0069\u0067\u0068t"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0077r\u0061\u0070\u0052\u0069\u0067\u0068t"}:_fgfb .MathPrChoice =NewCT_MathPrChoice ();
if _befe :=d .DecodeElement (&_fgfb .MathPrChoice .WrapRight ,&_cbcg );_befe !=nil {return _befe ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0069\u006e\u0074\u004c\u0069\u006d"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0069\u006e\u0074\u004c\u0069\u006d"}:_fgfb .IntLim =NewCT_LimLoc ();
if _abcg :=d .DecodeElement (_fgfb .IntLim ,&_cbcg );_abcg !=nil {return _abcg ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u006ea\u0072\u0079\u004c\u0069\u006d"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u006ea\u0072\u0079\u004c\u0069\u006d"}:_fgfb .NaryLim =NewCT_LimLoc ();
if _begf :=d .DecodeElement (_fgfb .NaryLim ,&_cbcg );_begf !=nil {return _begf ;};default:_b .Log .Debug ("\u0073k\u0069\u0070p\u0069\u006e\u0067\u0020u\u006e\u0073\u0075p\u0070\u006f\u0072\u0074\u0065\u0064\u0020\u0065\u006cem\u0065\u006e\u0074 \u006f\u006e \u0043\u0054\u005f\u004d\u0061\u0074h\u0050\u0072 \u0025\u0076",_cbcg .Name );
if _fgag :=d .Skip ();_fgag !=nil {return _fgag ;};};case _g .EndElement :break _aeeb ;case _g .CharData :};};return nil ;};type CT_MCS struct{

// Matrix Column
Mc []*CT_MC ;};func (_dcdg ST_Script )String ()string {switch _dcdg {case 0:return "";case 1:return "\u0072\u006f\u006da\u006e";case 2:return "\u0073\u0063\u0072\u0069\u0070\u0074";case 3:return "\u0066r\u0061\u006b\u0074\u0075\u0072";case 4:return "\u0064\u006f\u0075\u0062\u006c\u0065\u002d\u0073\u0074\u0072\u0075\u0063\u006b";
case 5:return "\u0073\u0061\u006e\u0073\u002d\u0073\u0065\u0072\u0069\u0066";case 6:return "\u006do\u006e\u006f\u0073\u0070\u0061\u0063e";};return "";};type CT_Func struct{

// Function Properties
FuncPr *CT_FuncPr ;

// Function Name
FName *CT_OMathArg ;

// Element (Argument)
E *CT_OMathArg ;};func NewCT_MathPr ()*CT_MathPr {_facb :=&CT_MathPr {};return _facb };func (_dfbg *CT_SSubPr )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {_dggg :for {_fbfe ,_gfbg :=d .Token ();if _gfbg !=nil {return _gfbg ;};switch _gddbc :=_fbfe .(type ){case _g .StartElement :switch _gddbc .Name {case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0063\u0074\u0072\u006c\u0050\u0072"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0063\u0074\u0072\u006c\u0050\u0072"}:_dfbg .CtrlPr =NewCT_CtrlPr ();
if _bdag :=d .DecodeElement (_dfbg .CtrlPr ,&_gddbc );_bdag !=nil {return _bdag ;};default:_b .Log .Debug ("\u0073k\u0069\u0070p\u0069\u006e\u0067\u0020u\u006e\u0073\u0075p\u0070\u006f\u0072\u0074\u0065\u0064\u0020\u0065\u006cem\u0065\u006e\u0074 \u006f\u006e \u0043\u0054\u005f\u0053\u0053\u0075b\u0050\u0072 \u0025\u0076",_gddbc .Name );
if _bceca :=d .Skip ();_bceca !=nil {return _bceca ;};};case _g .EndElement :break _dggg ;case _g .CharData :};};return nil ;};

// ValidateWithPath validates the CT_BreakBin and its children, prefixing error messages with path
func (_fda *CT_BreakBin )ValidateWithPath (path string )error {if _eac :=_fda .ValAttr .ValidateWithPath (path +"\u002f\u0056\u0061\u006c\u0041\u0074\u0074\u0072");_eac !=nil {return _eac ;};return nil ;};func NewCT_Integer2 ()*CT_Integer2 {_feca :=&CT_Integer2 {};
_feca .ValAttr =-2;return _feca };type CT_OMathPara struct{

// Office Math Paragraph Properties
OMathParaPr *CT_OMathParaPr ;

// Office Math
OMath []*CT_OMath ;};func (_aaaf *CT_FPr )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {_abcd :for {_acfe ,_fee :=d .Token ();if _fee !=nil {return _fee ;};switch _ecae :=_acfe .(type ){case _g .StartElement :switch _ecae .Name {case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0074\u0079\u0070\u0065"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0074\u0079\u0070\u0065"}:_aaaf .Type =NewCT_FType ();
if _adfe :=d .DecodeElement (_aaaf .Type ,&_ecae );_adfe !=nil {return _adfe ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0063\u0074\u0072\u006c\u0050\u0072"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0063\u0074\u0072\u006c\u0050\u0072"}:_aaaf .CtrlPr =NewCT_CtrlPr ();
if _bcgg :=d .DecodeElement (_aaaf .CtrlPr ,&_ecae );_bcgg !=nil {return _bcgg ;};default:_b .Log .Debug ("\u0073\u006b\u0069\u0070\u0070i\u006e\u0067\u0020\u0075\u006e\u0073\u0075\u0070\u0070\u006f\u0072\u0074\u0065d\u0020\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0043\u0054\u005f\u0046\u0050\u0072\u0020\u0025\u0076",_ecae .Name );
if _cdc :=d .Skip ();_cdc !=nil {return _cdc ;};};case _g .EndElement :break _abcd ;case _g .CharData :};};return nil ;};

// ValidateWithPath validates the CT_BorderBox and its children, prefixing error messages with path
func (_deg *CT_BorderBox )ValidateWithPath (path string )error {if _deg .BorderBoxPr !=nil {if _da :=_deg .BorderBoxPr .ValidateWithPath (path +"\u002f\u0042\u006fr\u0064\u0065\u0072\u0042\u006f\u0078\u0050\u0072");_da !=nil {return _da ;};};if _ccb :=_deg .E .ValidateWithPath (path +"\u002f\u0045");
_ccb !=nil {return _ccb ;};return nil ;};func NewCT_SSup ()*CT_SSup {_fffd :=&CT_SSup {};_fffd .E =NewCT_OMathArg ();_fffd .Sup =NewCT_OMathArg ();return _fffd ;};func NewCT_LimUppPr ()*CT_LimUppPr {_dbcc :=&CT_LimUppPr {};return _dbcc };type CT_RPR struct{

// Literal
Lit *CT_OnOff ;Nor *CT_OnOff ;

// Break
Brk *CT_ManualBreak ;

// Align
Aln *CT_OnOff ;};type CT_RChoice struct{T *CT_Text ;};type CT_Nary struct{

// n-ary Properties
NaryPr *CT_NaryPr ;

// Lower limit (n-ary)
Sub *CT_OMathArg ;

// Upper limit (n-ary)
Sup *CT_OMathArg ;

// Base (Argument)
E *CT_OMathArg ;};

// ValidateWithPath validates the CT_F and its children, prefixing error messages with path
func (_bda *CT_F )ValidateWithPath (path string )error {if _bda .FPr !=nil {if _agbf :=_bda .FPr .ValidateWithPath (path +"\u002f\u0046\u0050\u0072");_agbf !=nil {return _agbf ;};};if _gef :=_bda .Num .ValidateWithPath (path +"\u002f\u004e\u0075\u006d");
_gef !=nil {return _gef ;};if _acf :=_bda .Den .ValidateWithPath (path +"\u002f\u0044\u0065\u006e");_acf !=nil {return _acf ;};return nil ;};func NewCT_OMathPara ()*CT_OMathPara {_agfb :=&CT_OMathPara {};return _agfb };func (_efec *CT_Style )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {if _efec .ValAttr !=ST_StyleUnset {_edad ,_gddbf :=_efec .ValAttr .MarshalXMLAttr (_g .Name {Local :"\u006d\u003a\u0076a\u006c"});
if _gddbf !=nil {return _gddbf ;};start .Attr =append (start .Attr ,_edad );};e .EncodeToken (start );e .EncodeToken (_g .EndElement {Name :start .Name });return nil ;};type OMathPara struct{CT_OMathPara };func (_bgbee *CT_MR )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {_ffed :for {_bfbfg ,_gfb :=d .Token ();
if _gfb !=nil {return _gfb ;};switch _eaaa :=_bfbfg .(type ){case _g .StartElement :switch _eaaa .Name {case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0065"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0065"}:_cgae :=NewCT_OMathArg ();
if _aaec :=d .DecodeElement (_cgae ,&_eaaa );_aaec !=nil {return _aaec ;};_bgbee .E =append (_bgbee .E ,_cgae );default:_b .Log .Debug ("s\u006b\u0069\u0070\u0070\u0069\u006e\u0067\u0020\u0075\u006e\u0073\u0075\u0070\u0070\u006f\u0072\u0074\u0065d\u0020\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006fn \u0043\u0054\u005fM\u0052 \u0025\u0076",_eaaa .Name );
if _fcbf :=d .Skip ();_fcbf !=nil {return _fcbf ;};};case _g .EndElement :break _ffed ;case _g .CharData :};};return nil ;};

// ValidateWithPath validates the CT_Phant and its children, prefixing error messages with path
func (_aggab *CT_Phant )ValidateWithPath (path string )error {if _aggab .PhantPr !=nil {if _fdae :=_aggab .PhantPr .ValidateWithPath (path +"\u002f\u0050\u0068\u0061\u006e\u0074\u0050\u0072");_fdae !=nil {return _fdae ;};};if _fbcd :=_aggab .E .ValidateWithPath (path +"\u002f\u0045");
_fbcd !=nil {return _fbcd ;};return nil ;};func (_aaab *CT_SPrePr )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {e .EncodeToken (start );if _aaab .CtrlPr !=nil {_ggfb :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0063\u0074\u0072\u006c\u0050\u0072"}};
e .EncodeElement (_aaab .CtrlPr ,_ggfb );};e .EncodeToken (_g .EndElement {Name :start .Name });return nil ;};func NewCT_UnSignedInteger ()*CT_UnSignedInteger {_bcfa :=&CT_UnSignedInteger {};return _bcfa };

// ValidateWithPath validates the CT_SpacingRule and its children, prefixing error messages with path
func (_bbcaa *CT_SpacingRule )ValidateWithPath (path string )error {if _bbcaa .ValAttr < 0{return _a .Errorf ("%\u0073\u002f\u006d\u002e\u0056\u0061l\u0041\u0074\u0074\u0072\u0020\u006du\u0073\u0074\u0020\u0062\u0065\u0020\u003e=\u0020\u0030\u0020\u0028\u0068\u0061\u0076\u0065\u0020\u0025v\u0029",path ,_bbcaa .ValAttr );
};if _bbcaa .ValAttr > 4{return _a .Errorf ("%\u0073\u002f\u006d\u002e\u0056\u0061l\u0041\u0074\u0074\u0072\u0020\u006du\u0073\u0074\u0020\u0062\u0065\u0020\u003c=\u0020\u0034\u0020\u0028\u0068\u0061\u0076\u0065\u0020\u0025v\u0029",path ,_bbcaa .ValAttr );
};return nil ;};func NewCT_SSubSupPr ()*CT_SSubSupPr {_fbgc :=&CT_SSubSupPr {};return _fbgc };

// ValidateWithPath validates the CT_UnSignedInteger and its children, prefixing error messages with path
func (_ffgd *CT_UnSignedInteger )ValidateWithPath (path string )error {return nil };func (_egef *CT_R )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {_agffc :for {_egfa ,_bggce :=d .Token ();if _bggce !=nil {return _bggce ;};switch _cdcf :=_egfa .(type ){case _g .StartElement :switch _cdcf .Name {case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0072\u0050\u0072"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0072\u0050\u0072"}:_egef .RPr =NewCT_RPR ();
if _bacd :=d .DecodeElement (_egef .RPr ,&_cdcf );_bacd !=nil {return _bacd ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0074"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0074"}:_bcde :=NewCT_RChoice ();
if _ecaeg :=d .DecodeElement (&_bcde .T ,&_cdcf );_ecaeg !=nil {return _ecaeg ;};_egef .RChoice =append (_egef .RChoice ,_bcde );default:_b .Log .Debug ("\u0073\u006b\u0069\u0070\u0070\u0069\u006e\u0067\u0020\u0075\u006e\u0073\u0075p\u0070\u006f\u0072\u0074\u0065\u0064 \u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0043\u0054_\u0052\u0020\u0025\u0076",_cdcf .Name );
if _abdg :=d .Skip ();_abdg !=nil {return _abdg ;};};case _g .EndElement :break _agffc ;case _g .CharData :};};return nil ;};type CT_XAlign struct{

// Value
ValAttr _ab .ST_XAlign ;};func (_ebcb *CT_XAlign )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {_ebcb .ValAttr =_ab .ST_XAlign (1);for _ ,_dgaae :=range start .Attr {if _dgaae .Name .Local =="\u0076\u0061\u006c"{_ebcb .ValAttr .UnmarshalXMLAttr (_dgaae );
continue ;};};for {_abca ,_cagd :=d .Token ();if _cagd !=nil {return _a .Errorf ("p\u0061\u0072\u0073\u0069ng\u0020C\u0054\u005f\u0058\u0041\u006ci\u0067\u006e\u003a\u0020\u0025\u0073",_cagd );};if _cbdb ,_fecbg :=_abca .(_g .EndElement );_fecbg &&_cbdb .Name ==start .Name {break ;
};};return nil ;};func NewCT_LimLowPr ()*CT_LimLowPr {_eba :=&CT_LimLowPr {};return _eba };func (_fdf *CT_MC )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {e .EncodeToken (start );if _fdf .McPr !=nil {_ecgg :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u006d\u0063\u0050\u0072"}};
e .EncodeElement (_fdf .McPr ,_ecgg );};e .EncodeToken (_g .EndElement {Name :start .Name });return nil ;};func (_dfe *CT_AccPr )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {e .EncodeToken (start );if _dfe .Chr !=nil {_gd :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0063h\u0072"}};
e .EncodeElement (_dfe .Chr ,_gd );};if _dfe .CtrlPr !=nil {_eg :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0063\u0074\u0072\u006c\u0050\u0072"}};e .EncodeElement (_dfe .CtrlPr ,_eg );};e .EncodeToken (_g .EndElement {Name :start .Name });
return nil ;};type CT_FuncPr struct{CtrlPr *CT_CtrlPr ;};

// Validate validates the CT_M and its children
func (_cceb *CT_M )Validate ()error {return _cceb .ValidateWithPath ("\u0043\u0054\u005f\u004d")};func (_def *CT_OMath )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {e .EncodeToken (start );if _def .EG_OMathElements !=nil {for _ ,_bcfg :=range _def .EG_OMathElements {_bcfg .MarshalXML (e ,_g .StartElement {});
};};e .EncodeToken (_g .EndElement {Name :start .Name });return nil ;};func (_aff *CT_BarPr )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {_ea :for {_deb ,_aag :=d .Token ();if _aag !=nil {return _aag ;};switch _ddf :=_deb .(type ){case _g .StartElement :switch _ddf .Name {case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0070\u006f\u0073"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0070\u006f\u0073"}:_aff .Pos =NewCT_TopBot ();
if _cc :=d .DecodeElement (_aff .Pos ,&_ddf );_cc !=nil {return _cc ;};case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068",Local :"\u0063\u0074\u0072\u006c\u0050\u0072"},_g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u006d\u0061\u0074\u0068",Local :"\u0063\u0074\u0072\u006c\u0050\u0072"}:_aff .CtrlPr =NewCT_CtrlPr ();
if _fb :=d .DecodeElement (_aff .CtrlPr ,&_ddf );_fb !=nil {return _fb ;};default:_b .Log .Debug ("\u0073\u006b\u0069\u0070\u0070\u0069\u006eg\u0020\u0075\u006es\u0075\u0070\u0070\u006fr\u0074\u0065\u0064\u0020\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0043\u0054\u005f\u0042\u0061\u0072\u0050\u0072\u0020\u0025\u0076",_ddf .Name );
if _gdb :=d .Skip ();_gdb !=nil {return _gdb ;};};case _g .EndElement :break _ea ;case _g .CharData :};};return nil ;};func NewCT_OMathArg ()*CT_OMathArg {_abdc :=&CT_OMathArg {};return _abdc };

// Validate validates the CT_RPR and its children
func (_dcac *CT_RPR )Validate ()error {return _dcac .ValidateWithPath ("\u0043\u0054\u005f\u0052\u0050\u0052");};

// ValidateWithPath validates the CT_Integer2 and its children, prefixing error messages with path
func (_gggg *CT_Integer2 )ValidateWithPath (path string )error {if _gggg .ValAttr < -2{return _a .Errorf ("\u0025\u0073/m\u002e\u0056\u0061l\u0041\u0074\u0074\u0072 mu\u0073t \u0062\u0065\u0020\u003e\u003d\u0020\u002d2 \u0028\u0068\u0061\u0076\u0065\u0020\u0025v\u0029",path ,_gggg .ValAttr );
};if _gggg .ValAttr > 2{return _a .Errorf ("%\u0073\u002f\u006d\u002e\u0056\u0061l\u0041\u0074\u0074\u0072\u0020\u006du\u0073\u0074\u0020\u0062\u0065\u0020\u003c=\u0020\u0032\u0020\u0028\u0068\u0061\u0076\u0065\u0020\u0025v\u0029",path ,_gggg .ValAttr );
};return nil ;};type CT_Char struct{

// value
ValAttr string ;};type CT_Bar struct{

// Bar Properties
BarPr *CT_BarPr ;

// Base
E *CT_OMathArg ;};func (_dfefe *CT_SSup )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {e .EncodeToken (start );if _dfefe .SSupPr !=nil {_dfgg :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0073\u0053\u0075\u0070\u0050\u0072"}};e .EncodeElement (_dfefe .SSupPr ,_dfgg );
};_gbfa :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0065"}};e .EncodeElement (_dfefe .E ,_gbfa );_aacb :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0073u\u0070"}};e .EncodeElement (_dfefe .Sup ,_aacb );e .EncodeToken (_g .EndElement {Name :start .Name });
return nil ;};type ST_TopBot byte ;func NewCT_SSupPr ()*CT_SSupPr {_gcggg :=&CT_SSupPr {};return _gcggg };

// Validate validates the CT_FuncPr and its children
func (_ggg *CT_FuncPr )Validate ()error {return _ggg .ValidateWithPath ("\u0043T\u005f\u0046\u0075\u006e\u0063\u0050r");};func (_ddfcb *OMath )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {start .Attr =append (start .Attr ,_g .Attr {Name :_g .Name {Local :"\u0078\u006d\u006cn\u0073"},Value :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068"});
start .Attr =append (start .Attr ,_g .Attr {Name :_g .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u006d"},Value :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068"});
start .Attr =append (start .Attr ,_g .Attr {Name :_g .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0073"},Value :"\u0068\u0074\u0074\u0070\u003a/\u002f\u0073\u0063\u0068\u0065m\u0061s\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067/\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0068\u0061\u0072e\u0064\u0054\u0079\u0070\u0065\u0073"});
start .Attr =append (start .Attr ,_g .Attr {Name :_g .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0077"},Value :"ht\u0074\u0070:\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073.\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0077\u006f\u0072\u0064\u0070\u0072\u006f\u0063\u0065s\u0073i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u00306\u002fm\u0061\u0069n"});
start .Attr =append (start .Attr ,_g .Attr {Name :_g .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0078\u006dl"},Value :"\u0068\u0074tp\u003a\u002f\u002fw\u0077\u0077\u002e\u00773.o\u0072g/\u0058\u004d\u004c\u002f\u0031\u0039\u00398/\u006e\u0061\u006d\u0065\u0073\u0070\u0061c\u0065"});
start .Name .Local ="\u006d:\u006f\u004d\u0061\u0074\u0068";return _ddfcb .CT_OMath .MarshalXML (e ,start );};func (_dcb *CT_BorderBox )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {e .EncodeToken (start );if _dcb .BorderBoxPr !=nil {_efb :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0062\u006f\u0072\u0064\u0065\u0072\u0042\u006f\u0078\u0050\u0072"}};
e .EncodeElement (_dcb .BorderBoxPr ,_efb );};_dcef :=_g .StartElement {Name :_g .Name {Local :"\u006d\u003a\u0065"}};e .EncodeElement (_dcb .E ,_dcef );e .EncodeToken (_g .EndElement {Name :start .Name });return nil ;};type CT_SSubSup struct{

// Sub-Superscript Properties
SSubSupPr *CT_SSubSupPr ;

// Base
E *CT_OMathArg ;

// Subscript (Sub-Superscript)
Sub *CT_OMathArg ;

// Superscript (Sub-Superscript function)
Sup *CT_OMathArg ;};

// ValidateWithPath validates the CT_MC and its children, prefixing error messages with path
func (_bfgf *CT_MC )ValidateWithPath (path string )error {if _bfgf .McPr !=nil {if _bgde :=_bfgf .McPr .ValidateWithPath (path +"\u002f\u004d\u0063P\u0072");_bgde !=nil {return _bgde ;};};return nil ;};type CT_Rad struct{

// Radical Properties
RadPr *CT_RadPr ;

// Degree
Deg *CT_OMathArg ;

// Base
E *CT_OMathArg ;};func (_cedd *CT_Shp )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {_cedd .ValAttr =ST_Shp (1);for _ ,_gfbb :=range start .Attr {if _gfbb .Name .Local =="\u0076\u0061\u006c"{_cedd .ValAttr .UnmarshalXMLAttr (_gfbb );continue ;
};};for {_dbbg ,_cdbf :=d .Token ();if _cdbf !=nil {return _a .Errorf ("\u0070a\u0072s\u0069\u006e\u0067\u0020\u0043T\u005f\u0053h\u0070\u003a\u0020\u0025\u0073",_cdbf );};if _bggfg ,_baecd :=_dbbg .(_g .EndElement );_baecd &&_bggfg .Name ==start .Name {break ;
};};return nil ;};

// ValidateWithPath validates the CT_AccPr and its children, prefixing error messages with path
func (_ga *CT_AccPr )ValidateWithPath (path string )error {if _ga .Chr !=nil {if _fec :=_ga .Chr .ValidateWithPath (path +"\u002f\u0043\u0068\u0072");_fec !=nil {return _fec ;};};if _ga .CtrlPr !=nil {if _egf :=_ga .CtrlPr .ValidateWithPath (path +"\u002fC\u0074\u0072\u006c\u0050\u0072");
_egf !=nil {return _egf ;};};return nil ;};func (_fbgf ST_FType )MarshalXMLAttr (name _g .Name )(_g .Attr ,error ){_baagb :=_g .Attr {};_baagb .Name =name ;switch _fbgf {case ST_FTypeUnset :_baagb .Value ="";case ST_FTypeBar :_baagb .Value ="\u0062\u0061\u0072";
case ST_FTypeSkw :_baagb .Value ="\u0073\u006b\u0077";case ST_FTypeLin :_baagb .Value ="\u006c\u0069\u006e";case ST_FTypeNoBar :_baagb .Value ="\u006e\u006f\u0042a\u0072";};return _baagb ,nil ;};func init (){_ad .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068","\u0043\u0054\u005f\u0049\u006e\u0074\u0065\u0067\u0065\u0072\u0032\u0035\u0035",NewCT_Integer255 );
_ad .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068","C\u0054\u005f\u0049\u006e\u0074\u0065\u0067\u0065\u0072\u0032",NewCT_Integer2 );
_ad .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068","\u0043\u0054\u005f\u0053\u0070\u0061\u0063\u0069\u006eg\u0052\u0075\u006c\u0065",NewCT_SpacingRule );
_ad .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068","\u0043T\u005fU\u006e\u0053\u0069\u0067\u006ee\u0064\u0049n\u0074\u0065\u0067\u0065\u0072",NewCT_UnSignedInteger );
_ad .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068","\u0043T\u005f\u0043\u0068\u0061\u0072",NewCT_Char );
_ad .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068","\u0043\u0054\u005f\u004f\u006e\u004f\u0066\u0066",NewCT_OnOff );
_ad .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068","\u0043T\u005f\u0053\u0074\u0072\u0069\u006eg",NewCT_String );
_ad .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068","\u0043T\u005f\u0058\u0041\u006c\u0069\u0067n",NewCT_XAlign );
_ad .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068","\u0043T\u005f\u0059\u0041\u006c\u0069\u0067n",NewCT_YAlign );
_ad .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068","\u0043\u0054\u005f\u0053\u0068\u0070",NewCT_Shp );
_ad .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068","\u0043\u0054\u005f\u0046\u0054\u0079\u0070\u0065",NewCT_FType );
_ad .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068","\u0043T\u005f\u004c\u0069\u006d\u004c\u006fc",NewCT_LimLoc );
_ad .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068","\u0043T\u005f\u0054\u006f\u0070\u0042\u006ft",NewCT_TopBot );
_ad .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068","\u0043T\u005f\u0053\u0063\u0072\u0069\u0070t",NewCT_Script );
_ad .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068","\u0043\u0054\u005f\u0053\u0074\u0079\u006c\u0065",NewCT_Style );
_ad .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068","\u0043\u0054\u005f\u004d\u0061\u006e\u0075\u0061\u006cB\u0072\u0065\u0061\u006b",NewCT_ManualBreak );
_ad .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068","\u0043\u0054\u005f\u0052\u0050\u0052",NewCT_RPR );
_ad .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068","\u0043T\u005f\u0054\u0065\u0078\u0074",NewCT_Text );
_ad .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068","\u0043\u0054\u005f\u0052",NewCT_R );
_ad .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068","\u0043T\u005f\u0043\u0074\u0072\u006c\u0050r",NewCT_CtrlPr );
_ad .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068","\u0043\u0054\u005f\u0041\u0063\u0063\u0050\u0072",NewCT_AccPr );
_ad .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068","\u0043\u0054\u005f\u0041\u0063\u0063",NewCT_Acc );
_ad .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068","\u0043\u0054\u005f\u0042\u0061\u0072\u0050\u0072",NewCT_BarPr );
_ad .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068","\u0043\u0054\u005f\u0042\u0061\u0072",NewCT_Bar );
_ad .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068","\u0043\u0054\u005f\u0042\u006f\u0078\u0050\u0072",NewCT_BoxPr );
_ad .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068","\u0043\u0054\u005f\u0042\u006f\u0078",NewCT_Box );
_ad .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068","\u0043\u0054\u005f\u0042\u006f\u0072\u0064\u0065\u0072B\u006f\u0078\u0050\u0072",NewCT_BorderBoxPr );
_ad .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068","\u0043\u0054\u005fB\u006f\u0072\u0064\u0065\u0072\u0042\u006f\u0078",NewCT_BorderBox );
_ad .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068","\u0043\u0054\u005f\u0044\u0050\u0072",NewCT_DPr );
_ad .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068","\u0043\u0054\u005f\u0044",NewCT_D );
_ad .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068","\u0043\u0054\u005f\u0045\u0071\u0041\u0072\u0072\u0050\u0072",NewCT_EqArrPr );
_ad .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068","\u0043\u0054\u005f\u0045\u0071\u0041\u0072\u0072",NewCT_EqArr );
_ad .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068","\u0043\u0054\u005f\u0046\u0050\u0072",NewCT_FPr );
_ad .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068","\u0043\u0054\u005f\u0046",NewCT_F );
_ad .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068","\u0043T\u005f\u0046\u0075\u006e\u0063\u0050r",NewCT_FuncPr );
_ad .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068","\u0043T\u005f\u0046\u0075\u006e\u0063",NewCT_Func );
_ad .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068","\u0043\u0054\u005f\u0047\u0072\u006f\u0075\u0070\u0043\u0068\u0072\u0050\u0072",NewCT_GroupChrPr );
_ad .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068","C\u0054\u005f\u0047\u0072\u006f\u0075\u0070\u0043\u0068\u0072",NewCT_GroupChr );
_ad .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068","C\u0054\u005f\u004c\u0069\u006d\u004c\u006f\u0077\u0050\u0072",NewCT_LimLowPr );
_ad .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068","\u0043T\u005f\u004c\u0069\u006d\u004c\u006fw",NewCT_LimLow );
_ad .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068","C\u0054\u005f\u004c\u0069\u006d\u0055\u0070\u0070\u0050\u0072",NewCT_LimUppPr );
_ad .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068","\u0043T\u005f\u004c\u0069\u006d\u0055\u0070p",NewCT_LimUpp );
_ad .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068","\u0043T\u005f\u004d\u0043\u0050\u0072",NewCT_MCPr );
_ad .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068","\u0043\u0054\u005fM\u0043",NewCT_MC );
_ad .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068","\u0043\u0054\u005f\u004d\u0043\u0053",NewCT_MCS );
_ad .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068","\u0043\u0054\u005f\u004d\u0050\u0072",NewCT_MPr );
_ad .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068","\u0043\u0054\u005fM\u0052",NewCT_MR );
_ad .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068","\u0043\u0054\u005f\u004d",NewCT_M );
_ad .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068","\u0043T\u005f\u004e\u0061\u0072\u0079\u0050r",NewCT_NaryPr );
_ad .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068","\u0043T\u005f\u004e\u0061\u0072\u0079",NewCT_Nary );
_ad .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068","\u0043\u0054\u005f\u0050\u0068\u0061\u006e\u0074\u0050\u0072",NewCT_PhantPr );
_ad .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068","\u0043\u0054\u005f\u0050\u0068\u0061\u006e\u0074",NewCT_Phant );
_ad .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068","\u0043\u0054\u005f\u0052\u0061\u0064\u0050\u0072",NewCT_RadPr );
_ad .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068","\u0043\u0054\u005f\u0052\u0061\u0064",NewCT_Rad );
_ad .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068","\u0043T\u005f\u0053\u0050\u0072\u0065\u0050r",NewCT_SPrePr );
_ad .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068","\u0043T\u005f\u0053\u0050\u0072\u0065",NewCT_SPre );
_ad .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068","\u0043T\u005f\u0053\u0053\u0075\u0062\u0050r",NewCT_SSubPr );
_ad .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068","\u0043T\u005f\u0053\u0053\u0075\u0062",NewCT_SSub );
_ad .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068","\u0043\u0054\u005fS\u0053\u0075\u0062\u0053\u0075\u0070\u0050\u0072",NewCT_SSubSupPr );
_ad .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068","\u0043\u0054\u005f\u0053\u0053\u0075\u0062\u0053\u0075\u0070",NewCT_SSubSup );
_ad .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068","\u0043T\u005f\u0053\u0053\u0075\u0070\u0050r",NewCT_SSupPr );
_ad .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068","\u0043T\u005f\u0053\u0053\u0075\u0070",NewCT_SSup );
_ad .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068","\u0043\u0054\u005f\u004f\u004d\u0061\u0074\u0068\u0041\u0072\u0067\u0050\u0072",NewCT_OMathArgPr );
_ad .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068","C\u0054\u005f\u004f\u004d\u0061\u0074\u0068\u0041\u0072\u0067",NewCT_OMathArg );
_ad .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068","\u0043\u0054\u005f\u004f\u004d\u0061\u0074\u0068\u004a\u0063",NewCT_OMathJc );
_ad .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068","\u0043\u0054\u005f\u004f\u004d\u0061\u0074\u0068\u0050a\u0072\u0061\u0050\u0072",NewCT_OMathParaPr );
_ad .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068","\u0043T\u005fT\u0077\u0069\u0070\u0073\u004d\u0065\u0061\u0073\u0075\u0072\u0065",NewCT_TwipsMeasure );
_ad .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068","C\u0054\u005f\u0042\u0072\u0065\u0061\u006b\u0042\u0069\u006e",NewCT_BreakBin );
_ad .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068","\u0043\u0054\u005f\u0042\u0072\u0065\u0061\u006b\u0042i\u006e\u0053\u0075\u0062",NewCT_BreakBinSub );
_ad .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068","\u0043T\u005f\u004d\u0061\u0074\u0068\u0050r",NewCT_MathPr );
_ad .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068","\u0043\u0054\u005fO\u004d\u0061\u0074\u0068\u0050\u0061\u0072\u0061",NewCT_OMathPara );
_ad .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068","\u0043\u0054\u005f\u004f\u004d\u0061\u0074\u0068",NewCT_OMath );
_ad .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068","\u006d\u0061\u0074\u0068\u0050\u0072",NewMathPr );
_ad .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068","\u006fM\u0061\u0074\u0068\u0050\u0061\u0072a",NewOMathPara );
_ad .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068","\u006f\u004d\u0061t\u0068",NewOMath );
_ad .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068","\u0045\u0047\u005f\u0053\u0063\u0072\u0069\u0070\u0074S\u0074\u0079\u006c\u0065",NewEG_ScriptStyle );
_ad .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068","E\u0047_\u004f\u004d\u0061\u0074\u0068\u004d\u0061\u0074h\u0045\u006c\u0065\u006den\u0074\u0073",NewEG_OMathMathElements );
_ad .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075m\u0065\u006e\u0074\u002f\u0032\u00300\u0036\u002f\u006da\u0074\u0068","\u0045\u0047_\u004f\u004d\u0061t\u0068\u0045\u006c\u0065\u006d\u0065\u006e\u0074\u0073",NewEG_OMathElements );
};