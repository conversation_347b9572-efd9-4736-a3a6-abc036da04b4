//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package content_types ;import (_e "encoding/xml";_ea "fmt";_b "github.com/unidoc/unioffice/v2";_d "github.com/unidoc/unioffice/v2/common/logger";_ad "regexp";);type CT_Types struct{TypesChoice []*CT_TypesChoice ;};func (_af *CT_Override )MarshalXML (e *_e .Encoder ,start _e .StartElement )error {start .Attr =append (start .Attr ,_e .Attr {Name :_e .Name {Local :"C\u006f\u006e\u0074\u0065\u006e\u0074\u0054\u0079\u0070\u0065"},Value :_ea .Sprintf ("\u0025\u0076",_af .ContentTypeAttr )});
start .Attr =append (start .Attr ,_e .Attr {Name :_e .Name {Local :"\u0050\u0061\u0072\u0074\u004e\u0061\u006d\u0065"},Value :_ea .Sprintf ("\u0025\u0076",_af .PartNameAttr )});e .EncodeToken (start );e .EncodeToken (_e .EndElement {Name :start .Name });
return nil ;};

// ValidateWithPath validates the CT_Default and its children, prefixing error messages with path
func (_gd *CT_Default )ValidateWithPath (path string )error {if !ST_ExtensionPatternRe .MatchString (_gd .ExtensionAttr ){return _ea .Errorf ("\u0025s\u002f\u006d.\u0045\u0078\u0074\u0065n\u0073\u0069\u006fn\u0041\u0074\u0074\u0072\u0020\u006d\u0075\u0073\u0074 m\u0061\u0074\u0063h\u0020\u0027%\u0073\u0027\u0020\u0028\u0068\u0061v\u0065\u0020%\u0076\u0029",path ,ST_ExtensionPatternRe ,_gd .ExtensionAttr );
};if !ST_ContentTypePatternRe .MatchString (_gd .ContentTypeAttr ){return _ea .Errorf ("\u0025\u0073/\u006d\u002e\u0043\u006f\u006e\u0074\u0065\u006e\u0074\u0054\u0079\u0070\u0065\u0041\u0074\u0074\u0072\u0020\u006d\u0075\u0073\u0074\u0020\u006d\u0061\u0074\u0063\u0068\u0020\u0027\u0025\u0073\u0027\u0020\u0028\u0068\u0061\u0076\u0065\u0020\u0025\u0076\u0029",path ,ST_ContentTypePatternRe ,_gd .ContentTypeAttr );
};return nil ;};type CT_TypesChoice struct{Default *Default ;Override *Override ;};type CT_Override struct{ContentTypeAttr string ;PartNameAttr string ;};

// Validate validates the CT_Override and its children
func (_ef *CT_Override )Validate ()error {return _ef .ValidateWithPath ("C\u0054\u005f\u004f\u0076\u0065\u0072\u0072\u0069\u0064\u0065");};func (_db *CT_Default )MarshalXML (e *_e .Encoder ,start _e .StartElement )error {start .Attr =append (start .Attr ,_e .Attr {Name :_e .Name {Local :"\u0045x\u0074\u0065\u006e\u0073\u0069\u006fn"},Value :_ea .Sprintf ("\u0025\u0076",_db .ExtensionAttr )});
start .Attr =append (start .Attr ,_e .Attr {Name :_e .Name {Local :"C\u006f\u006e\u0074\u0065\u006e\u0074\u0054\u0079\u0070\u0065"},Value :_ea .Sprintf ("\u0025\u0076",_db .ContentTypeAttr )});e .EncodeToken (start );e .EncodeToken (_e .EndElement {Name :start .Name });
return nil ;};

// ValidateWithPath validates the CT_Override and its children, prefixing error messages with path
func (_dbg *CT_Override )ValidateWithPath (path string )error {if !ST_ContentTypePatternRe .MatchString (_dbg .ContentTypeAttr ){return _ea .Errorf ("\u0025\u0073/\u006d\u002e\u0043\u006f\u006e\u0074\u0065\u006e\u0074\u0054\u0079\u0070\u0065\u0041\u0074\u0074\u0072\u0020\u006d\u0075\u0073\u0074\u0020\u006d\u0061\u0074\u0063\u0068\u0020\u0027\u0025\u0073\u0027\u0020\u0028\u0068\u0061\u0076\u0065\u0020\u0025\u0076\u0029",path ,ST_ContentTypePatternRe ,_dbg .ContentTypeAttr );
};return nil ;};

// Validate validates the CT_Default and its children
func (_ee *CT_Default )Validate ()error {return _ee .ValidateWithPath ("\u0043\u0054\u005f\u0044\u0065\u0066\u0061\u0075\u006c\u0074");};type Default struct{CT_Default };func NewCT_Default ()*CT_Default {_g :=&CT_Default {};_g .ExtensionAttr ="\u0078\u006d\u006c";
_g .ContentTypeAttr ="\u0061p\u0070l\u0069\u0063\u0061\u0074\u0069\u006f\u006e\u002f\u0078\u006d\u006c";return _g ;};func (_ge *Default )MarshalXML (e *_e .Encoder ,start _e .StartElement )error {return _ge .CT_Default .MarshalXML (e ,start );};func NewDefault ()*Default {_cf :=&Default {};
_cf .CT_Default =*NewCT_Default ();return _cf };func (_ca *Default )UnmarshalXML (d *_e .Decoder ,start _e .StartElement )error {_ca .CT_Default =*NewCT_Default ();for _ ,_eg :=range start .Attr {if _eg .Name .Local =="\u0045x\u0074\u0065\u006e\u0073\u0069\u006fn"{_gfe :=_eg .Value ;
_ca .ExtensionAttr =_gfe ;continue ;};if _eg .Name .Local =="C\u006f\u006e\u0074\u0065\u006e\u0074\u0054\u0079\u0070\u0065"{_cdc :=_eg .Value ;_ca .ContentTypeAttr =_cdc ;continue ;};};for {_edaf ,_bgf :=d .Token ();if _bgf !=nil {return _ea .Errorf ("\u0070\u0061\u0072\u0073in\u0067\u0020\u0044\u0065\u0066\u0061\u0075\u006c\u0074\u003a\u0020\u0025\u0073",_bgf );
};if _ec ,_dgc :=_edaf .(_e .EndElement );_dgc &&_ec .Name ==start .Name {break ;};};return nil ;};func (_egf *Override )MarshalXML (e *_e .Encoder ,start _e .StartElement )error {return _egf .CT_Override .MarshalXML (e ,start );};func (_dd *Override )UnmarshalXML (d *_e .Decoder ,start _e .StartElement )error {_dd .CT_Override =*NewCT_Override ();
for _ ,_ae :=range start .Attr {if _ae .Name .Local =="C\u006f\u006e\u0074\u0065\u006e\u0074\u0054\u0079\u0070\u0065"{_eeg :=_ae .Value ;_dd .ContentTypeAttr =_eeg ;continue ;};if _ae .Name .Local =="\u0050\u0061\u0072\u0074\u004e\u0061\u006d\u0065"{_dc :=_ae .Value ;
_dd .PartNameAttr =_dc ;continue ;};};for {_fec ,_cfa :=d .Token ();if _cfa !=nil {return _ea .Errorf ("p\u0061r\u0073\u0069\u006e\u0067\u0020\u004f\u0076\u0065r\u0072\u0069\u0064\u0065: \u0025\u0073",_cfa );};if _fc ,_cee :=_fec .(_e .EndElement );_cee &&_fc .Name ==start .Name {break ;
};};return nil ;};

// Validate validates the Override and its children
func (_eabe *Override )Validate ()error {return _eabe .ValidateWithPath ("\u004f\u0076\u0065\u0072\u0072\u0069\u0064\u0065");};

// ValidateWithPath validates the CT_TypesChoice and its children, prefixing error messages with path
func (_ba *CT_TypesChoice )ValidateWithPath (path string )error {if _ba .Default !=nil {if _bdf :=_ba .Default .ValidateWithPath (path +"\u002f\u0044\u0065\u0066\u0061\u0075\u006c\u0074");_bdf !=nil {return _bdf ;};};if _ba .Override !=nil {if _eec :=_ba .Override .ValidateWithPath (path +"\u002fO\u0076\u0065\u0072\u0072\u0069\u0064e");
_eec !=nil {return _eec ;};};return nil ;};func NewOverride ()*Override {_ffd :=&Override {};_ffd .CT_Override =*NewCT_Override ();return _ffd };func NewCT_Types ()*CT_Types {_gc :=&CT_Types {};return _gc };type CT_Default struct{ExtensionAttr string ;
ContentTypeAttr string ;};

// ValidateWithPath validates the CT_Types and its children, prefixing error messages with path
func (_aa *CT_Types )ValidateWithPath (path string )error {for _dagg ,_cd :=range _aa .TypesChoice {if _gdc :=_cd .ValidateWithPath (_ea .Sprintf ("\u0025s\u002fT\u0079\u0070\u0065\u0073\u0043h\u006f\u0069c\u0065\u005b\u0025\u0064\u005d",path ,_dagg ));
_gdc !=nil {return _gdc ;};};return nil ;};var ST_ContentTypePatternRe =_ad .MustCompile (ST_ContentTypePattern );

// Validate validates the CT_TypesChoice and its children
func (_gf *CT_TypesChoice )Validate ()error {return _gf .ValidateWithPath ("\u0043\u0054\u005f\u0054\u0079\u0070\u0065\u0073\u0043h\u006f\u0069\u0063\u0065");};func (_ag *Types )UnmarshalXML (d *_e .Decoder ,start _e .StartElement )error {_ag .CT_Types =*NewCT_Types ();
_gca :for {_gea ,_abf :=d .Token ();if _abf !=nil {return _abf ;};switch _fcb :=_gea .(type ){case _e .StartElement :switch _fcb .Name {case _e .Name {Space :"ht\u0074\u0070:\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073.\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0070\u0061\u0063\u006b\u0061\u0067\u0065\u002f\u00320\u00306\u002f\u0063\u006f\u006e\u0074\u0065\u006e\u0074-\u0074y\u0070\u0065s",Local :"\u0044e\u0066\u0061\u0075\u006c\u0074"}:_cff :=NewCT_TypesChoice ();
if _cba :=d .DecodeElement (&_cff .Default ,&_fcb );_cba !=nil {return _cba ;};_ag .TypesChoice =append (_ag .TypesChoice ,_cff );case _e .Name {Space :"ht\u0074\u0070:\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073.\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0070\u0061\u0063\u006b\u0061\u0067\u0065\u002f\u00320\u00306\u002f\u0063\u006f\u006e\u0074\u0065\u006e\u0074-\u0074y\u0070\u0065s",Local :"\u004f\u0076\u0065\u0072\u0072\u0069\u0064\u0065"}:_fd :=NewCT_TypesChoice ();
if _fgf :=d .DecodeElement (&_fd .Override ,&_fcb );_fgf !=nil {return _fgf ;};_ag .TypesChoice =append (_ag .TypesChoice ,_fd );default:_d .Log .Debug ("s\u006b\u0069\u0070\u0070\u0069\u006e\u0067\u0020\u0075\u006e\u0073\u0075\u0070\u0070\u006f\u0072\u0074\u0065d\u0020\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006fn \u0054\u0079\u0070e\u0073 \u0025\u0076",_fcb .Name );
if _agc :=d .Skip ();_agc !=nil {return _agc ;};};case _e .EndElement :break _gca ;case _e .CharData :};};return nil ;};

// Validate validates the Types and its children
func (_dbd *Types )Validate ()error {return _dbd .ValidateWithPath ("\u0054\u0079\u0070e\u0073")};const ST_ExtensionPattern ="\u0028\u005b\u0021\u0024\u0026\u0027\\\u0028\u005c\u0029\u005c\u002a\\\u002b\u002c\u003a\u003d\u005d\u007c(\u0025\u005b\u0030\u002d\u0039a\u002d\u0066\u0041\u002d\u0046\u005d\u005b\u0030\u002d\u0039\u0061\u002df\u0041\u002d\u0046\u005d\u0029\u007c\u005b\u003a\u0040\u005d\u007c\u005b\u0061\u002d\u007aA\u002d\u005a\u0030\u002d\u0039\u005c\u002d\u005f~\u005d\u0029\u002b";


// Validate validates the CT_Types and its children
func (_fef *CT_Types )Validate ()error {return _fef .ValidateWithPath ("\u0043\u0054\u005f\u0054\u0079\u0070\u0065\u0073");};func (_aff *CT_Types )UnmarshalXML (d *_e .Decoder ,start _e .StartElement )error {_cbf :for {_bfc ,_gg :=d .Token ();if _gg !=nil {return _gg ;
};switch _bdc :=_bfc .(type ){case _e .StartElement :switch _bdc .Name {case _e .Name {Space :"ht\u0074\u0070:\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073.\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0070\u0061\u0063\u006b\u0061\u0067\u0065\u002f\u00320\u00306\u002f\u0063\u006f\u006e\u0074\u0065\u006e\u0074-\u0074y\u0070\u0065s",Local :"\u0044e\u0066\u0061\u0075\u006c\u0074"}:_cgc :=NewCT_TypesChoice ();
if _cgg :=d .DecodeElement (&_cgc .Default ,&_bdc );_cgg !=nil {return _cgg ;};_aff .TypesChoice =append (_aff .TypesChoice ,_cgc );case _e .Name {Space :"ht\u0074\u0070:\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073.\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0070\u0061\u0063\u006b\u0061\u0067\u0065\u002f\u00320\u00306\u002f\u0063\u006f\u006e\u0074\u0065\u006e\u0074-\u0074y\u0070\u0065s",Local :"\u004f\u0076\u0065\u0072\u0072\u0069\u0064\u0065"}:_bb :=NewCT_TypesChoice ();
if _eab :=d .DecodeElement (&_bb .Override ,&_bdc );_eab !=nil {return _eab ;};_aff .TypesChoice =append (_aff .TypesChoice ,_bb );default:_d .Log .Debug ("\u0073\u006b\u0069\u0070\u0070\u0069\u006eg\u0020\u0075\u006es\u0075\u0070\u0070\u006fr\u0074\u0065\u0064\u0020\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0043\u0054\u005f\u0054\u0079\u0070\u0065\u0073\u0020\u0025\u0076",_bdc .Name );
if _ac :=d .Skip ();_ac !=nil {return _ac ;};};case _e .EndElement :break _cbf ;case _e .CharData :};};return nil ;};

// ValidateWithPath validates the Types and its children, prefixing error messages with path
func (_gcaf *Types )ValidateWithPath (path string )error {if _cbga :=_gcaf .CT_Types .ValidateWithPath (path );_cbga !=nil {return _cbga ;};return nil ;};

// ValidateWithPath validates the Default and its children, prefixing error messages with path
func (_fff *Default )ValidateWithPath (path string )error {if _dgb :=_fff .CT_Default .ValidateWithPath (path );_dgb !=nil {return _dgb ;};return nil ;};var ST_ExtensionPatternRe =_ad .MustCompile (ST_ExtensionPattern );func (_fab *CT_TypesChoice )UnmarshalXML (d *_e .Decoder ,start _e .StartElement )error {_dbaa :=start ;
switch start .Name {case _e .Name {Space :"ht\u0074\u0070:\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073.\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0070\u0061\u0063\u006b\u0061\u0067\u0065\u002f\u00320\u00306\u002f\u0063\u006f\u006e\u0074\u0065\u006e\u0074-\u0074y\u0070\u0065s",Local :"\u0044e\u0066\u0061\u0075\u006c\u0074"}:_fab .Default =NewDefault ();
if _ab :=d .DecodeElement (_fab .Default ,&_dbaa );_ab !=nil {return _ab ;};case _e .Name {Space :"ht\u0074\u0070:\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073.\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0070\u0061\u0063\u006b\u0061\u0067\u0065\u002f\u00320\u00306\u002f\u0063\u006f\u006e\u0074\u0065\u006e\u0074-\u0074y\u0070\u0065s",Local :"\u004f\u0076\u0065\u0072\u0072\u0069\u0064\u0065"}:_fab .Override =NewOverride ();
if _cbg :=d .DecodeElement (_fab .Override ,&_dbaa );_cbg !=nil {return _cbg ;};default:_d .Log .Debug ("\u0073\u006b\u0069\u0070\u0070\u0069n\u0067\u0020\u0075\u006e\u0073\u0075\u0070\u0070\u006f\u0072\u0074\u0065\u0064\u0020\u0065\u006c\u0065\u006d\u0065\u006et\u0020\u006f\u006e\u0020\u0043\u0054\u005f\u0054\u0079\u0070\u0065\u0073\u0043\u0068o\u0069c\u0065\u0020\u0025\u0076",_dbaa .Name );
if _acf :=d .Skip ();_acf !=nil {return _acf ;};};return nil ;};type Types struct{CT_Types };func NewCT_Override ()*CT_Override {_bd :=&CT_Override {};_bd .ContentTypeAttr ="\u0061p\u0070l\u0069\u0063\u0061\u0074\u0069\u006f\u006e\u002f\u0078\u006d\u006c";
return _bd ;};

// ValidateWithPath validates the Override and its children, prefixing error messages with path
func (_dfd *Override )ValidateWithPath (path string )error {if _cgcc :=_dfd .CT_Override .ValidateWithPath (path );_cgcc !=nil {return _cgcc ;};return nil ;};func (_ce *CT_TypesChoice )MarshalXML (e *_e .Encoder ,start _e .StartElement )error {e .EncodeToken (start );
if _ce .Default !=nil {_ga :=_e .StartElement {Name :_e .Name {Local :"\u0044e\u0066\u0061\u0075\u006c\u0074"}};e .EncodeElement (_ce .Default ,_ga );}else if _ce .Override !=nil {_bg :=_e .StartElement {Name :_e .Name {Local :"\u004f\u0076\u0065\u0072\u0072\u0069\u0064\u0065"}};
e .EncodeElement (_ce .Override ,_bg );};e .EncodeToken (_e .EndElement {Name :start .Name });return nil ;};func NewTypes ()*Types {_dbf :=&Types {};_dbf .CT_Types =*NewCT_Types ();return _dbf };func (_cg *CT_Types )MarshalXML (e *_e .Encoder ,start _e .StartElement )error {e .EncodeToken (start );
if _cg .TypesChoice !=nil {for _ ,_bf :=range _cg .TypesChoice {_bf .MarshalXML (e ,_e .StartElement {});};};e .EncodeToken (_e .EndElement {Name :start .Name });return nil ;};const ST_ContentTypePattern ="\u005e\\\u0070{\u004c\u0061\u0074\u0069\u006e\u007d\u002b\u002f\u002e\u002a\u0024";
func NewCT_TypesChoice ()*CT_TypesChoice {_eda :=&CT_TypesChoice {};return _eda };func (_gb *CT_Override )UnmarshalXML (d *_e .Decoder ,start _e .StartElement )error {_gb .ContentTypeAttr ="\u0061p\u0070l\u0069\u0063\u0061\u0074\u0069\u006f\u006e\u002f\u0078\u006d\u006c";
for _ ,_ff :=range start .Attr {if _ff .Name .Local =="C\u006f\u006e\u0074\u0065\u006e\u0074\u0054\u0079\u0070\u0065"{_fgc :=_ff .Value ;_gb .ContentTypeAttr =_fgc ;continue ;};if _ff .Name .Local =="\u0050\u0061\u0072\u0074\u004e\u0061\u006d\u0065"{_fa :=_ff .Value ;
_gb .PartNameAttr =_fa ;continue ;};};for {_de ,_cb :=d .Token ();if _cb !=nil {return _ea .Errorf ("\u0070\u0061\u0072si\u006e\u0067\u0020\u0043\u0054\u005f\u004f\u0076\u0065\u0072\u0072\u0069\u0064\u0065\u003a\u0020\u0025\u0073",_cb );};if _df ,_fe :=_de .(_e .EndElement );
_fe &&_df .Name ==start .Name {break ;};};return nil ;};func (_da *CT_Default )UnmarshalXML (d *_e .Decoder ,start _e .StartElement )error {_da .ExtensionAttr ="\u0078\u006d\u006c";_da .ContentTypeAttr ="\u0061p\u0070l\u0069\u0063\u0061\u0074\u0069\u006f\u006e\u002f\u0078\u006d\u006c";
for _ ,_dbc :=range start .Attr {if _dbc .Name .Local =="\u0045x\u0074\u0065\u006e\u0073\u0069\u006fn"{_f :=_dbc .Value ;_da .ExtensionAttr =_f ;continue ;};if _dbc .Name .Local =="C\u006f\u006e\u0074\u0065\u006e\u0074\u0054\u0079\u0070\u0065"{_fg :=_dbc .Value ;
_da .ContentTypeAttr =_fg ;continue ;};};for {_dag ,_dg :=d .Token ();if _dg !=nil {return _ea .Errorf ("\u0070\u0061\u0072\u0073in\u0067\u0020\u0043\u0054\u005f\u0044\u0065\u0066\u0061\u0075\u006c\u0074\u003a\u0020%\u0073",_dg );};if _ed ,_edb :=_dag .(_e .EndElement );
_edb &&_ed .Name ==start .Name {break ;};};return nil ;};func (_aef *Types )MarshalXML (e *_e .Encoder ,start _e .StartElement )error {start .Attr =append (start .Attr ,_e .Attr {Name :_e .Name {Local :"\u0078\u006d\u006cn\u0073"},Value :"ht\u0074\u0070:\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073.\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0070\u0061\u0063\u006b\u0061\u0067\u0065\u002f\u00320\u00306\u002f\u0063\u006f\u006e\u0074\u0065\u006e\u0074-\u0074y\u0070\u0065s"});
start .Attr =append (start .Attr ,_e .Attr {Name :_e .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0078\u006dl"},Value :"\u0068\u0074tp\u003a\u002f\u002fw\u0077\u0077\u002e\u00773.o\u0072g/\u0058\u004d\u004c\u002f\u0031\u0039\u00398/\u006e\u0061\u006d\u0065\u0073\u0070\u0061c\u0065"});
start .Name .Local ="\u0054\u0079\u0070e\u0073";return _aef .CT_Types .MarshalXML (e ,start );};

// Validate validates the Default and its children
func (_bge *Default )Validate ()error {return _bge .ValidateWithPath ("\u0044e\u0066\u0061\u0075\u006c\u0074");};type Override struct{CT_Override };func init (){_b .RegisterConstructor ("ht\u0074\u0070:\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073.\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0070\u0061\u0063\u006b\u0061\u0067\u0065\u002f\u00320\u00306\u002f\u0063\u006f\u006e\u0074\u0065\u006e\u0074-\u0074y\u0070\u0065s","\u0043\u0054\u005f\u0054\u0079\u0070\u0065\u0073",NewCT_Types );
_b .RegisterConstructor ("ht\u0074\u0070:\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073.\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0070\u0061\u0063\u006b\u0061\u0067\u0065\u002f\u00320\u00306\u002f\u0063\u006f\u006e\u0074\u0065\u006e\u0074-\u0074y\u0070\u0065s","\u0043\u0054\u005f\u0044\u0065\u0066\u0061\u0075\u006c\u0074",NewCT_Default );
_b .RegisterConstructor ("ht\u0074\u0070:\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073.\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0070\u0061\u0063\u006b\u0061\u0067\u0065\u002f\u00320\u00306\u002f\u0063\u006f\u006e\u0074\u0065\u006e\u0074-\u0074y\u0070\u0065s","C\u0054\u005f\u004f\u0076\u0065\u0072\u0072\u0069\u0064\u0065",NewCT_Override );
_b .RegisterConstructor ("ht\u0074\u0070:\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073.\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0070\u0061\u0063\u006b\u0061\u0067\u0065\u002f\u00320\u00306\u002f\u0063\u006f\u006e\u0074\u0065\u006e\u0074-\u0074y\u0070\u0065s","\u0054\u0079\u0070e\u0073",NewTypes );
_b .RegisterConstructor ("ht\u0074\u0070:\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073.\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0070\u0061\u0063\u006b\u0061\u0067\u0065\u002f\u00320\u00306\u002f\u0063\u006f\u006e\u0074\u0065\u006e\u0074-\u0074y\u0070\u0065s","\u0044e\u0066\u0061\u0075\u006c\u0074",NewDefault );
_b .RegisterConstructor ("ht\u0074\u0070:\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073.\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0070\u0061\u0063\u006b\u0061\u0067\u0065\u002f\u00320\u00306\u002f\u0063\u006f\u006e\u0074\u0065\u006e\u0074-\u0074y\u0070\u0065s","\u004f\u0076\u0065\u0072\u0072\u0069\u0064\u0065",NewOverride );
};