//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package relationships ;import (_bg "encoding/xml";_c "fmt";_e "github.com/unidoc/unioffice/v2";_ce "github.com/unidoc/unioffice/v2/common/logger";);func (_fe *CT_Relationship )UnmarshalXML (d *_bg .Decoder ,start _bg .StartElement )error {for _ ,_fb :=range start .Attr {if _fb .Name .Local =="\u0054\u0061\u0072\u0067\u0065\u0074\u004d\u006f\u0064\u0065"{_fe .TargetModeAttr .UnmarshalXMLAttr (_fb );
continue ;};if _fb .Name .Local =="\u0054\u0061\u0072\u0067\u0065\u0074"{_d :=_fb .Value ;_fe .TargetAttr =_d ;continue ;};if _fb .Name .Local =="\u0054\u0079\u0070\u0065"{_ga :=_fb .Value ;_fe .TypeAttr =_ga ;continue ;};if _fb .Name .Local =="\u0049\u0064"{_fed :=_fb .Value ;
_fe .IdAttr =_fed ;continue ;};};for {_fa ,_a :=d .Token ();if _a !=nil {return _c .Errorf ("p\u0061\u0072\u0073\u0069\u006e\u0067 \u0043\u0054\u005f\u0052\u0065\u006c\u0061\u0074\u0069o\u006e\u0073\u0068i\u0070:\u0020\u0025\u0073",_a );};if _eaf ,_cg :=_fa .(_bg .CharData );
_cg {_fe .Content =string (_eaf );};if _bgf ,_af :=_fa .(_bg .EndElement );_af &&_bgf .Name ==start .Name {break ;};};return nil ;};func NewCT_Relationship ()*CT_Relationship {_g :=&CT_Relationship {};return _g };

// Validate validates the CT_Relationships and its children
func (_fc *CT_Relationships )Validate ()error {return _fc .ValidateWithPath ("\u0043\u0054_\u0052\u0065\u006ca\u0074\u0069\u006f\u006e\u0073\u0068\u0069\u0070\u0073");};func (_be *CT_Relationships )MarshalXML (e *_bg .Encoder ,start _bg .StartElement )error {e .EncodeToken (start );
if _be .Relationship !=nil {_da :=_bg .StartElement {Name :_bg .Name {Local :"\u0052\u0065\u006ca\u0074\u0069\u006f\u006e\u0073\u0068\u0069\u0070"}};for _ ,_ge :=range _be .Relationship {e .EncodeElement (_ge ,_da );};};e .EncodeToken (_bg .EndElement {Name :start .Name });
return nil ;};

// Validate validates the CT_Relationship and its children
func (_ca *CT_Relationship )Validate ()error {return _ca .ValidateWithPath ("\u0043T\u005fR\u0065\u006c\u0061\u0074\u0069\u006f\u006e\u0073\u0068\u0069\u0070");};type CT_Relationship struct{TargetModeAttr ST_TargetMode ;TargetAttr string ;TypeAttr string ;
IdAttr string ;Content string ;};func NewCT_Relationships ()*CT_Relationships {_fg :=&CT_Relationships {};return _fg };func NewRelationships ()*Relationships {_dd :=&Relationships {};_dd .CT_Relationships =*NewCT_Relationships ();return _dd ;};func (_ea *CT_Relationship )MarshalXML (e *_bg .Encoder ,start _bg .StartElement )error {if _ea .TargetModeAttr !=ST_TargetModeUnset {_f ,_cf :=_ea .TargetModeAttr .MarshalXMLAttr (_bg .Name {Local :"\u0054\u0061\u0072\u0067\u0065\u0074\u004d\u006f\u0064\u0065"});
if _cf !=nil {return _cf ;};start .Attr =append (start .Attr ,_f );};start .Attr =append (start .Attr ,_bg .Attr {Name :_bg .Name {Local :"\u0054\u0061\u0072\u0067\u0065\u0074"},Value :_c .Sprintf ("\u0025\u0076",_ea .TargetAttr )});start .Attr =append (start .Attr ,_bg .Attr {Name :_bg .Name {Local :"\u0054\u0079\u0070\u0065"},Value :_c .Sprintf ("\u0025\u0076",_ea .TypeAttr )});
start .Attr =append (start .Attr ,_bg .Attr {Name :_bg .Name {Local :"\u0049\u0064"},Value :_c .Sprintf ("\u0025\u0076",_ea .IdAttr )});e .EncodeElement (_ea .Content ,start );e .EncodeToken (_bg .EndElement {Name :start .Name });return nil ;};

// Validate validates the Relationship and its children
func (_ceg *Relationship )Validate ()error {return _ceg .ValidateWithPath ("\u0052\u0065\u006ca\u0074\u0069\u006f\u006e\u0073\u0068\u0069\u0070");};func (_eef *Relationship )MarshalXML (e *_bg .Encoder ,start _bg .StartElement )error {return _eef .CT_Relationship .MarshalXML (e ,start );
};func (_dff ST_TargetMode )MarshalXMLAttr (name _bg .Name )(_bg .Attr ,error ){_dad :=_bg .Attr {};_dad .Name =name ;switch _dff {case ST_TargetModeUnset :_dad .Value ="";case ST_TargetModeExternal :_dad .Value ="\u0045\u0078\u0074\u0065\u0072\u006e\u0061\u006c";
case ST_TargetModeInternal :_dad .Value ="\u0049\u006e\u0074\u0065\u0072\u006e\u0061\u006c";};return _dad ,nil ;};type Relationship struct{CT_Relationship };func (_fedd ST_TargetMode )String ()string {switch _fedd {case 0:return "";case 1:return "\u0045\u0078\u0074\u0065\u0072\u006e\u0061\u006c";
case 2:return "\u0049\u006e\u0074\u0065\u0072\u006e\u0061\u006c";};return "";};

// ValidateWithPath validates the Relationships and its children, prefixing error messages with path
func (_dc *Relationships )ValidateWithPath (path string )error {if _dfe :=_dc .CT_Relationships .ValidateWithPath (path );_dfe !=nil {return _dfe ;};return nil ;};func (_ba ST_TargetMode )ValidateWithPath (path string )error {switch _ba {case 0,1,2:default:return _c .Errorf ("\u0025s\u003a\u0020\u006f\u0075t\u0020\u006f\u0066\u0020\u0072a\u006eg\u0065 \u0076\u0061\u006c\u0075\u0065\u0020\u0025d",path ,int (_ba ));
};return nil ;};type Relationships struct{CT_Relationships };func (_aga ST_TargetMode )Validate ()error {return _aga .ValidateWithPath ("")};

// Validate validates the Relationships and its children
func (_faf *Relationships )Validate ()error {return _faf .ValidateWithPath ("\u0052\u0065\u006c\u0061\u0074\u0069\u006f\u006e\u0073\u0068\u0069\u0070\u0073");};const (ST_TargetModeUnset ST_TargetMode =0;ST_TargetModeExternal ST_TargetMode =1;ST_TargetModeInternal ST_TargetMode =2;
);

// ValidateWithPath validates the CT_Relationship and its children, prefixing error messages with path
func (_df *CT_Relationship )ValidateWithPath (path string )error {if _bf :=_df .TargetModeAttr .ValidateWithPath (path +"\u002fT\u0061r\u0067\u0065\u0074\u004d\u006f\u0064\u0065\u0041\u0074\u0074\u0072");_bf !=nil {return _bf ;};return nil ;};type ST_TargetMode byte ;
func (_gc *ST_TargetMode )UnmarshalXML (d *_bg .Decoder ,start _bg .StartElement )error {_ec ,_becf :=d .Token ();if _becf !=nil {return _becf ;};if _abb ,_dacb :=_ec .(_bg .EndElement );_dacb &&_abb .Name ==start .Name {*_gc =1;return nil ;};if _gac ,_gbg :=_ec .(_bg .CharData );
!_gbg {return _c .Errorf ("\u0065\u0078\u0070\u0065\u0063\u0074\u0065\u0064\u0020\u0063\u0068a\u0072\u0020\u0064\u0061\u0074\u0061\u002c\u0020\u0067\u006ft\u0020\u0025\u0054",_ec );}else {switch string (_gac ){case "":*_gc =0;case "\u0045\u0078\u0074\u0065\u0072\u006e\u0061\u006c":*_gc =1;
case "\u0049\u006e\u0074\u0065\u0072\u006e\u0061\u006c":*_gc =2;};};_ec ,_becf =d .Token ();if _becf !=nil {return _becf ;};if _dg ,_gcc :=_ec .(_bg .EndElement );_gcc &&_dg .Name ==start .Name {return nil ;};return _c .Errorf ("\u0065\u0078\u0070\u0065c\u0074\u0065\u0064\u0020\u0065\u006e\u0064\u0020\u0065\u006ce\u006de\u006e\u0074\u002c\u0020\u0067\u006f\u0074 \u0025\u0076",_ec );
};

// ValidateWithPath validates the Relationship and its children, prefixing error messages with path
func (_dba *Relationship )ValidateWithPath (path string )error {if _geg :=_dba .CT_Relationship .ValidateWithPath (path );_geg !=nil {return _geg ;};return nil ;};func NewRelationship ()*Relationship {_dfa :=&Relationship {};_dfa .CT_Relationship =*NewCT_Relationship ();
return _dfa ;};func (_ead ST_TargetMode )MarshalXML (e *_bg .Encoder ,start _bg .StartElement )error {return e .EncodeElement (_ead .String (),start );};func (_aed *Relationship )UnmarshalXML (d *_bg .Decoder ,start _bg .StartElement )error {_aed .CT_Relationship =*NewCT_Relationship ();
for _ ,_gb :=range start .Attr {if _gb .Name .Local =="\u0054\u0061\u0072\u0067\u0065\u0074\u004d\u006f\u0064\u0065"{_aed .TargetModeAttr .UnmarshalXMLAttr (_gb );continue ;};if _gb .Name .Local =="\u0054\u0061\u0072\u0067\u0065\u0074"{_ag :=_gb .Value ;
_aed .TargetAttr =_ag ;continue ;};if _gb .Name .Local =="\u0054\u0079\u0070\u0065"{_bga :=_gb .Value ;_aed .TypeAttr =_bga ;continue ;};if _gb .Name .Local =="\u0049\u0064"{_bec :=_gb .Value ;_aed .IdAttr =_bec ;continue ;};};for {_cee ,_gec :=d .Token ();
if _gec !=nil {return _c .Errorf ("\u0070a\u0072\u0073\u0069\u006e\u0067\u0020\u0052\u0065\u006c\u0061\u0074i\u006f\u006e\u0073\u0068\u0069\u0070\u003a\u0020\u0025\u0073",_gec );};if _aa ,_ac :=_cee .(_bg .EndElement );_ac &&_aa .Name ==start .Name {break ;
};};return nil ;};func (_ae *CT_Relationships )UnmarshalXML (d *_bg .Decoder ,start _bg .StartElement )error {_bc :for {_db ,_eg :=d .Token ();if _eg !=nil {return _eg ;};switch _bb :=_db .(type ){case _bg .StartElement :switch _bb .Name {case _bg .Name {Space :"ht\u0074\u0070:\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073.\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0070\u0061\u0063\u006b\u0061\u0067\u0065\u002f\u00320\u00306\u002f\u0072\u0065\u006c\u0061\u0074\u0069\u006fn\u0073h\u0069\u0070s",Local :"\u0052\u0065\u006ca\u0074\u0069\u006f\u006e\u0073\u0068\u0069\u0070"}:_bgfe :=NewRelationship ();
if _cd :=d .DecodeElement (_bgfe ,&_bb );_cd !=nil {return _cd ;};_ae .Relationship =append (_ae .Relationship ,_bgfe );default:_ce .Log .Debug ("\u0073\u006b\u0069\u0070\u0070i\u006e\u0067\u0020\u0075\u006e\u0073\u0075\u0070\u0070\u006f\u0072\u0074\u0065d\u0020\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0043\u0054\u005f\u0052\u0065\u006c\u0061\u0074\u0069\u006f\u006e\u0073\u0068\u0069\u0070\u0073\u0020\u0025v",_bb .Name );
if _eb :=d .Skip ();_eb !=nil {return _eb ;};};case _bg .EndElement :break _bc ;case _bg .CharData :};};return nil ;};type CT_Relationships struct{Relationship []*Relationship ;};func (_aeda *ST_TargetMode )UnmarshalXMLAttr (attr _bg .Attr )error {switch attr .Value {case "":*_aeda =0;
case "\u0045\u0078\u0074\u0065\u0072\u006e\u0061\u006c":*_aeda =1;case "\u0049\u006e\u0074\u0065\u0072\u006e\u0061\u006c":*_aeda =2;};return nil ;};func (_ad *Relationships )UnmarshalXML (d *_bg .Decoder ,start _bg .StartElement )error {_ad .CT_Relationships =*NewCT_Relationships ();
_bfb :for {_aaf ,_egca :=d .Token ();if _egca !=nil {return _egca ;};switch _dac :=_aaf .(type ){case _bg .StartElement :switch _dac .Name {case _bg .Name {Space :"ht\u0074\u0070:\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073.\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0070\u0061\u0063\u006b\u0061\u0067\u0065\u002f\u00320\u00306\u002f\u0072\u0065\u006c\u0061\u0074\u0069\u006fn\u0073h\u0069\u0070s",Local :"\u0052\u0065\u006ca\u0074\u0069\u006f\u006e\u0073\u0068\u0069\u0070"}:_edb :=NewRelationship ();
if _cda :=d .DecodeElement (_edb ,&_dac );_cda !=nil {return _cda ;};_ad .Relationship =append (_ad .Relationship ,_edb );default:_ce .Log .Debug ("\u0073\u006b\u0069\u0070\u0070\u0069\u006e\u0067 \u0075\u006e\u0073up\u0070\u006f\u0072\u0074\u0065\u0064 \u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0052\u0065\u006c\u0061t\u0069\u006f\u006e\u0073\u0068\u0069\u0070\u0073 \u0025\u0076",_dac .Name );
if _eeg :=d .Skip ();_eeg !=nil {return _eeg ;};};case _bg .EndElement :break _bfb ;case _bg .CharData :};};return nil ;};func (_bce *Relationships )MarshalXML (e *_bg .Encoder ,start _bg .StartElement )error {start .Attr =append (start .Attr ,_bg .Attr {Name :_bg .Name {Local :"\u0078\u006d\u006cn\u0073"},Value :"ht\u0074\u0070:\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073.\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0070\u0061\u0063\u006b\u0061\u0067\u0065\u002f\u00320\u00306\u002f\u0072\u0065\u006c\u0061\u0074\u0069\u006fn\u0073h\u0069\u0070s"});
start .Attr =append (start .Attr ,_bg .Attr {Name :_bg .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0078\u006dl"},Value :"\u0068\u0074tp\u003a\u002f\u002fw\u0077\u0077\u002e\u00773.o\u0072g/\u0058\u004d\u004c\u002f\u0031\u0039\u00398/\u006e\u0061\u006d\u0065\u0073\u0070\u0061c\u0065"});
start .Name .Local ="\u0052\u0065\u006c\u0061\u0074\u0069\u006f\u006e\u0073\u0068\u0069\u0070\u0073";return _bce .CT_Relationships .MarshalXML (e ,start );};

// ValidateWithPath validates the CT_Relationships and its children, prefixing error messages with path
func (_egc *CT_Relationships )ValidateWithPath (path string )error {for _edg ,_dbg :=range _egc .Relationship {if _ee :=_dbg .ValidateWithPath (_c .Sprintf ("\u0025\u0073\u002f\u0052el\u0061\u0074\u0069\u006f\u006e\u0073\u0068\u0069\u0070\u005b\u0025\u0064\u005d",path ,_edg ));
_ee !=nil {return _ee ;};};return nil ;};func init (){_e .RegisterConstructor ("ht\u0074\u0070:\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073.\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0070\u0061\u0063\u006b\u0061\u0067\u0065\u002f\u00320\u00306\u002f\u0072\u0065\u006c\u0061\u0074\u0069\u006fn\u0073h\u0069\u0070s","\u0043\u0054_\u0052\u0065\u006ca\u0074\u0069\u006f\u006e\u0073\u0068\u0069\u0070\u0073",NewCT_Relationships );
_e .RegisterConstructor ("ht\u0074\u0070:\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073.\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0070\u0061\u0063\u006b\u0061\u0067\u0065\u002f\u00320\u00306\u002f\u0072\u0065\u006c\u0061\u0074\u0069\u006fn\u0073h\u0069\u0070s","\u0043T\u005fR\u0065\u006c\u0061\u0074\u0069\u006f\u006e\u0073\u0068\u0069\u0070",NewCT_Relationship );
_e .RegisterConstructor ("ht\u0074\u0070:\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073.\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0070\u0061\u0063\u006b\u0061\u0067\u0065\u002f\u00320\u00306\u002f\u0072\u0065\u006c\u0061\u0074\u0069\u006fn\u0073h\u0069\u0070s","\u0052\u0065\u006c\u0061\u0074\u0069\u006f\u006e\u0073\u0068\u0069\u0070\u0073",NewRelationships );
_e .RegisterConstructor ("ht\u0074\u0070:\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073.\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0070\u0061\u0063\u006b\u0061\u0067\u0065\u002f\u00320\u00306\u002f\u0072\u0065\u006c\u0061\u0074\u0069\u006fn\u0073h\u0069\u0070s","\u0052\u0065\u006ca\u0074\u0069\u006f\u006e\u0073\u0068\u0069\u0070",NewRelationship );
};