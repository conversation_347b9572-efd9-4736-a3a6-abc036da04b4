//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package core_properties ;import (_e "encoding/xml";_fa "fmt";_ec "github.com/unidoc/unioffice/v2";_a "github.com/unidoc/unioffice/v2/common/logger";_b "github.com/unidoc/unioffice/v2/schema/soo/ofc/sharedTypes";_g "time";);func NewCT_CoreProperties ()*CT_CoreProperties {_c :=&CT_CoreProperties {};
return _c };

// Validate validates the CT_CoreProperties and its children
func (_cef *CT_CoreProperties )Validate ()error {return _cef .ValidateWithPath ("\u0043\u0054\u005f\u0043\u006f\u0072\u0065\u0050\u0072\u006f\u0070\u0065r\u0074\u0069\u0065\u0073");};func (_dd *CT_Keywords )MarshalXML (e *_e .Encoder ,start _e .StartElement )error {if _dd .LangAttr !=nil {start .Attr =append (start .Attr ,_e .Attr {Name :_e .Name {Local :"\u0078\u006d\u006c\u003a\u006c\u0061\u006e\u0067"},Value :_fa .Sprintf ("\u0025\u0076",*_dd .LangAttr )});
};e .EncodeToken (start );if _dd .Value !=nil {_be :=_e .StartElement {Name :_e .Name {Local :"\u0063\u0070\u003a\u0076\u0061\u006c\u0075\u0065"}};for _ ,_gg :=range _dd .Value {e .EncodeElement (_gg ,_be );};};e .EncodeToken (_e .EndElement {Name :start .Name });
return nil ;};

// Validate validates the CT_Keyword and its children
func (_aab *CT_Keyword )Validate ()error {return _aab .ValidateWithPath ("\u0043\u0054\u005f\u004b\u0065\u0079\u0077\u006f\u0072\u0064");};func NewCT_Keywords ()*CT_Keywords {_bgg :=&CT_Keywords {};return _bgg };func NewCT_Keyword ()*CT_Keyword {_gaf :=&CT_Keyword {};
return _gaf };type CT_CoreProperties struct{Category *string ;ContentStatus *string ;Created *_ec .XSDAny ;Creator *_ec .XSDAny ;Description *_ec .XSDAny ;Identifier *_ec .XSDAny ;Keywords *CT_Keywords ;Language *_ec .XSDAny ;LastModifiedBy *string ;LastPrinted *_g .Time ;
Modified *_ec .XSDAny ;Revision *string ;Subject *_ec .XSDAny ;Title *_ec .XSDAny ;Version *string ;};func (_dbe *CT_Keyword )UnmarshalXML (d *_e .Decoder ,start _e .StartElement )error {for _ ,_de :=range start .Attr {if _de .Name .Space =="\u0068\u0074tp\u003a\u002f\u002fw\u0077\u0077\u002e\u00773.o\u0072g/\u0058\u004d\u004c\u002f\u0031\u0039\u00398/\u006e\u0061\u006d\u0065\u0073\u0070\u0061c\u0065"&&_de .Name .Local =="\u006c\u0061\u006e\u0067"{_ab :=_de .Value ;
_dbe .LangAttr =&_ab ;continue ;};};for {_ecga ,_bg :=d .Token ();if _bg !=nil {return _fa .Errorf ("\u0070\u0061\u0072\u0073in\u0067\u0020\u0043\u0054\u005f\u004b\u0065\u0079\u0077\u006f\u0072\u0064\u003a\u0020%\u0073",_bg );};if _gccc ,_cgg :=_ecga .(_e .CharData );
_cgg {_dbe .Content =string (_gccc );};if _ebe ,_cgb :=_ecga .(_e .EndElement );_cgb &&_ebe .Name ==start .Name {break ;};};return nil ;};func NewCoreProperties ()*CoreProperties {_efgb :=&CoreProperties {};_efgb .CT_CoreProperties =*NewCT_CoreProperties ();
return _efgb ;};func (_ffa *CT_Keywords )UnmarshalXML (d *_e .Decoder ,start _e .StartElement )error {for _ ,_efd :=range start .Attr {if _efd .Name .Space =="\u0068\u0074tp\u003a\u002f\u002fw\u0077\u0077\u002e\u00773.o\u0072g/\u0058\u004d\u004c\u002f\u0031\u0039\u00398/\u006e\u0061\u006d\u0065\u0073\u0070\u0061c\u0065"&&_efd .Name .Local =="\u006c\u0061\u006e\u0067"{_gab :=_efd .Value ;
_ffa .LangAttr =&_gab ;continue ;};};_fb :for {_gea ,_gf :=d .Token ();if _gf !=nil {return _gf ;};switch _eeg :=_gea .(type ){case _e .StartElement :switch _eeg .Name {case _e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063h\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067/\u0070\u0061c\u006b\u0061g\u0065\u002f\u0032\u0030\u0030\u0036\u002f\u006d\u0065\u0074\u0061\u0064\u0061ta\u002f\u0063\u006f\u0072\u0065\u002d\u0070\u0072\u006fp\u0065\u0072\u0074\u0069\u0065\u0073",Local :"\u0076\u0061\u006cu\u0065"}:_cf :=NewCT_Keyword ();
if _dg :=d .DecodeElement (_cf ,&_eeg );_dg !=nil {return _dg ;};_ffa .Value =append (_ffa .Value ,_cf );default:_a .Log .Debug ("\u0073\u006bi\u0070\u0070\u0069\u006e\u0067\u0020\u0075\u006e\u0073\u0075\u0070\u0070\u006f\u0072\u0074\u0065\u0064\u0020\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0043\u0054\u005f\u004b\u0065\u0079\u0077\u006f\u0072\u0064\u0073\u0020\u0025\u0076",_eeg .Name );
if _dc :=d .Skip ();_dc !=nil {return _dc ;};};case _e .EndElement :break _fb ;case _e .CharData :};};return nil ;};type CT_Keyword struct{LangAttr *string ;Content string ;};func (_gda *CoreProperties )UnmarshalXML (d *_e .Decoder ,start _e .StartElement )error {_gda .CT_CoreProperties =*NewCT_CoreProperties ();
_gdab :for {_ccf ,_gdc :=d .Token ();if _gdc !=nil {return _gdc ;};switch _ad :=_ccf .(type ){case _e .StartElement :switch _ad .Name {case _e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063h\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067/\u0070\u0061c\u006b\u0061g\u0065\u002f\u0032\u0030\u0030\u0036\u002f\u006d\u0065\u0074\u0061\u0064\u0061ta\u002f\u0063\u006f\u0072\u0065\u002d\u0070\u0072\u006fp\u0065\u0072\u0074\u0069\u0065\u0073",Local :"\u0063\u0061\u0074\u0065\u0067\u006f\u0072\u0079"}:_gda .Category =new (string );
if _fca :=d .DecodeElement (_gda .Category ,&_ad );_fca !=nil {return _fca ;};case _e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063h\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067/\u0070\u0061c\u006b\u0061g\u0065\u002f\u0032\u0030\u0030\u0036\u002f\u006d\u0065\u0074\u0061\u0064\u0061ta\u002f\u0063\u006f\u0072\u0065\u002d\u0070\u0072\u006fp\u0065\u0072\u0074\u0069\u0065\u0073",Local :"\u0063\u006f\u006e\u0074\u0065\u006e\u0074\u0053\u0074\u0061\u0074\u0075\u0073"}:_gda .ContentStatus =new (string );
if _eaf :=d .DecodeElement (_gda .ContentStatus ,&_ad );_eaf !=nil {return _eaf ;};case _e .Name {Space :"\u0068t\u0074\u0070\u003a\u002f/\u0070\u0075\u0072\u006c\u002eo\u0072g\u002fd\u0063\u002f\u0074\u0065\u0072\u006d\u0073/",Local :"\u0063r\u0065\u0061\u0074\u0065\u0064"}:_gda .Created =new (_ec .XSDAny );
if _egg :=d .DecodeElement (_gda .Created ,&_ad );_egg !=nil {return _egg ;};case _e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0072\u0067/\u0064c\u002f\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0073\u002f\u0031\u002e\u0031\u002f",Local :"\u0063r\u0065\u0061\u0074\u006f\u0072"}:_gda .Creator =new (_ec .XSDAny );
if _acf :=d .DecodeElement (_gda .Creator ,&_ad );_acf !=nil {return _acf ;};case _e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0072\u0067/\u0064c\u002f\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0073\u002f\u0031\u002e\u0031\u002f",Local :"d\u0065\u0073\u0063\u0072\u0069\u0070\u0074\u0069\u006f\u006e"}:_gda .Description =new (_ec .XSDAny );
if _afe :=d .DecodeElement (_gda .Description ,&_ad );_afe !=nil {return _afe ;};case _e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0072\u0067/\u0064c\u002f\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0073\u002f\u0031\u002e\u0031\u002f",Local :"\u0069\u0064\u0065\u006e\u0074\u0069\u0066\u0069\u0065\u0072"}:_gda .Identifier =new (_ec .XSDAny );
if _dfg :=d .DecodeElement (_gda .Identifier ,&_ad );_dfg !=nil {return _dfg ;};case _e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063h\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067/\u0070\u0061c\u006b\u0061g\u0065\u002f\u0032\u0030\u0030\u0036\u002f\u006d\u0065\u0074\u0061\u0064\u0061ta\u002f\u0063\u006f\u0072\u0065\u002d\u0070\u0072\u006fp\u0065\u0072\u0074\u0069\u0065\u0073",Local :"\u006b\u0065\u0079\u0077\u006f\u0072\u0064\u0073"}:_gda .Keywords =NewCT_Keywords ();
if _dag :=d .DecodeElement (_gda .Keywords ,&_ad );_dag !=nil {return _dag ;};case _e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0072\u0067/\u0064c\u002f\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0073\u002f\u0031\u002e\u0031\u002f",Local :"\u006c\u0061\u006e\u0067\u0075\u0061\u0067\u0065"}:_gda .Language =new (_ec .XSDAny );
if _gef :=d .DecodeElement (_gda .Language ,&_ad );_gef !=nil {return _gef ;};case _e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063h\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067/\u0070\u0061c\u006b\u0061g\u0065\u002f\u0032\u0030\u0030\u0036\u002f\u006d\u0065\u0074\u0061\u0064\u0061ta\u002f\u0063\u006f\u0072\u0065\u002d\u0070\u0072\u006fp\u0065\u0072\u0074\u0069\u0065\u0073",Local :"\u006c\u0061\u0073\u0074\u004d\u006f\u0064\u0069\u0066i\u0065\u0064\u0042\u0079"}:_gda .LastModifiedBy =new (string );
if _bf :=d .DecodeElement (_gda .LastModifiedBy ,&_ad );_bf !=nil {return _bf ;};case _e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063h\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067/\u0070\u0061c\u006b\u0061g\u0065\u002f\u0032\u0030\u0030\u0036\u002f\u006d\u0065\u0074\u0061\u0064\u0061ta\u002f\u0063\u006f\u0072\u0065\u002d\u0070\u0072\u006fp\u0065\u0072\u0074\u0069\u0065\u0073",Local :"l\u0061\u0073\u0074\u0050\u0072\u0069\u006e\u0074\u0065\u0064"}:var _bdf string ;
if _dcf :=d .DecodeElement (&_bdf ,&_ad );_dcf !=nil {return _dcf ;};_dcc ,_caf :=_b .ParseStdlibTime (_bdf );if _caf !=nil {return _caf ;};_gda .LastPrinted =&_dcc ;case _e .Name {Space :"\u0068t\u0074\u0070\u003a\u002f/\u0070\u0075\u0072\u006c\u002eo\u0072g\u002fd\u0063\u002f\u0074\u0065\u0072\u006d\u0073/",Local :"\u006d\u006f\u0064\u0069\u0066\u0069\u0065\u0064"}:_gda .Modified =new (_ec .XSDAny );
if _gfe :=d .DecodeElement (_gda .Modified ,&_ad );_gfe !=nil {return _gfe ;};case _e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063h\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067/\u0070\u0061c\u006b\u0061g\u0065\u002f\u0032\u0030\u0030\u0036\u002f\u006d\u0065\u0074\u0061\u0064\u0061ta\u002f\u0063\u006f\u0072\u0065\u002d\u0070\u0072\u006fp\u0065\u0072\u0074\u0069\u0065\u0073",Local :"\u0072\u0065\u0076\u0069\u0073\u0069\u006f\u006e"}:_gda .Revision =new (string );
if _eec :=d .DecodeElement (_gda .Revision ,&_ad );_eec !=nil {return _eec ;};case _e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0072\u0067/\u0064c\u002f\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0073\u002f\u0031\u002e\u0031\u002f",Local :"\u0073u\u0062\u006a\u0065\u0063\u0074"}:_gda .Subject =new (_ec .XSDAny );
if _gce :=d .DecodeElement (_gda .Subject ,&_ad );_gce !=nil {return _gce ;};case _e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0072\u0067/\u0064c\u002f\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0073\u002f\u0031\u002e\u0031\u002f",Local :"\u0074\u0069\u0074l\u0065"}:_gda .Title =new (_ec .XSDAny );
if _fcc :=d .DecodeElement (_gda .Title ,&_ad );_fcc !=nil {return _fcc ;};case _e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063h\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067/\u0070\u0061c\u006b\u0061g\u0065\u002f\u0032\u0030\u0030\u0036\u002f\u006d\u0065\u0074\u0061\u0064\u0061ta\u002f\u0063\u006f\u0072\u0065\u002d\u0070\u0072\u006fp\u0065\u0072\u0074\u0069\u0065\u0073",Local :"\u0076e\u0072\u0073\u0069\u006f\u006e"}:_gda .Version =new (string );
if _ggf :=d .DecodeElement (_gda .Version ,&_ad );_ggf !=nil {return _ggf ;};default:_a .Log .Debug ("\u0073\u006b\u0069\u0070\u0070\u0069n\u0067\u0020\u0075\u006e\u0073\u0075\u0070\u0070\u006f\u0072\u0074\u0065\u0064\u0020\u0065\u006c\u0065\u006d\u0065\u006et\u0020\u006f\u006e\u0020\u0043\u006f\u0072\u0065\u0050\u0072\u006f\u0070\u0065\u0072t\u0069e\u0073\u0020\u0025\u0076",_ad .Name );
if _cgd :=d .Skip ();_cgd !=nil {return _cgd ;};};case _e .EndElement :break _gdab ;case _e .CharData :};};return nil ;};

// Validate validates the CoreProperties and its children
func (_agb *CoreProperties )Validate ()error {return _agb .ValidateWithPath ("\u0043\u006f\u0072\u0065\u0050\u0072\u006f\u0070\u0065r\u0074\u0069\u0065\u0073");};func (_egc *CoreProperties )MarshalXML (e *_e .Encoder ,start _e .StartElement )error {start .Attr =append (start .Attr ,_e .Attr {Name :_e .Name {Local :"\u0078\u006d\u006cn\u0073"},Value :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063h\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067/\u0070\u0061c\u006b\u0061g\u0065\u002f\u0032\u0030\u0030\u0036\u002f\u006d\u0065\u0074\u0061\u0064\u0061ta\u002f\u0063\u006f\u0072\u0065\u002d\u0070\u0072\u006fp\u0065\u0072\u0074\u0069\u0065\u0073"});
start .Attr =append (start .Attr ,_e .Attr {Name :_e .Name {Local :"\u0078\u006d\u006c\u006e\u0073\u003a\u0063\u0070"},Value :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063h\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067/\u0070\u0061c\u006b\u0061g\u0065\u002f\u0032\u0030\u0030\u0036\u002f\u006d\u0065\u0074\u0061\u0064\u0061ta\u002f\u0063\u006f\u0072\u0065\u002d\u0070\u0072\u006fp\u0065\u0072\u0074\u0069\u0065\u0073"});
start .Attr =append (start .Attr ,_e .Attr {Name :_e .Name {Local :"\u0078\u006d\u006c\u006e\u0073\u003a\u0064\u0063"},Value :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0072\u0067/\u0064c\u002f\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0073\u002f\u0031\u002e\u0031\u002f"});
start .Attr =append (start .Attr ,_e .Attr {Name :_e .Name {Local :"\u0078\u006d\u006c\u006e\u0073\u003a\u0064\u0063\u0074\u0065\u0072\u006d\u0073"},Value :"\u0068t\u0074\u0070\u003a\u002f/\u0070\u0075\u0072\u006c\u002eo\u0072g\u002fd\u0063\u002f\u0074\u0065\u0072\u006d\u0073/"});
start .Attr =append (start .Attr ,_e .Attr {Name :_e .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0078\u006dl"},Value :"\u0068\u0074tp\u003a\u002f\u002fw\u0077\u0077\u002e\u00773.o\u0072g/\u0058\u004d\u004c\u002f\u0031\u0039\u00398/\u006e\u0061\u006d\u0065\u0073\u0070\u0061c\u0065"});
start .Name .Local ="\u0063\u0070\u003a\u0063\u006f\u0072\u0065\u0050\u0072\u006f\u0070\u0065r\u0074\u0069\u0065\u0073";return _egc .CT_CoreProperties .MarshalXML (e ,start );};

// ValidateWithPath validates the CT_Keywords and its children, prefixing error messages with path
func (_df *CT_Keywords )ValidateWithPath (path string )error {for _dac ,_bdc :=range _df .Value {if _cce :=_bdc .ValidateWithPath (_fa .Sprintf ("\u0025\u0073\u002fV\u0061\u006c\u0075\u0065\u005b\u0025\u0064\u005d",path ,_dac ));_cce !=nil {return _cce ;
};};return nil ;};

// ValidateWithPath validates the CoreProperties and its children, prefixing error messages with path
func (_cb *CoreProperties )ValidateWithPath (path string )error {if _cda :=_cb .CT_CoreProperties .ValidateWithPath (path );_cda !=nil {return _cda ;};return nil ;};func (_cg *CT_Keyword )MarshalXML (e *_e .Encoder ,start _e .StartElement )error {if _cg .LangAttr !=nil {start .Attr =append (start .Attr ,_e .Attr {Name :_e .Name {Local :"\u0078\u006d\u006c\u003a\u006c\u0061\u006e\u0067"},Value :_fa .Sprintf ("\u0025\u0076",*_cg .LangAttr )});
};e .EncodeElement (_cg .Content ,start );e .EncodeToken (_e .EndElement {Name :start .Name });return nil ;};func (_af *CT_CoreProperties )UnmarshalXML (d *_e .Decoder ,start _e .StartElement )error {_ef :for {_ac ,_afg :=d .Token ();if _afg !=nil {return _afg ;
};switch _eg :=_ac .(type ){case _e .StartElement :switch _eg .Name {case _e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063h\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067/\u0070\u0061c\u006b\u0061g\u0065\u002f\u0032\u0030\u0030\u0036\u002f\u006d\u0065\u0074\u0061\u0064\u0061ta\u002f\u0063\u006f\u0072\u0065\u002d\u0070\u0072\u006fp\u0065\u0072\u0074\u0069\u0065\u0073",Local :"\u0063\u0061\u0074\u0065\u0067\u006f\u0072\u0079"}:_af .Category =new (string );
if _ag :=d .DecodeElement (_af .Category ,&_eg );_ag !=nil {return _ag ;};case _e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063h\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067/\u0070\u0061c\u006b\u0061g\u0065\u002f\u0032\u0030\u0030\u0036\u002f\u006d\u0065\u0074\u0061\u0064\u0061ta\u002f\u0063\u006f\u0072\u0065\u002d\u0070\u0072\u006fp\u0065\u0072\u0074\u0069\u0065\u0073",Local :"\u0063\u006f\u006e\u0074\u0065\u006e\u0074\u0053\u0074\u0061\u0074\u0075\u0073"}:_af .ContentStatus =new (string );
if _bd :=d .DecodeElement (_af .ContentStatus ,&_eg );_bd !=nil {return _bd ;};case _e .Name {Space :"\u0068t\u0074\u0070\u003a\u002f/\u0070\u0075\u0072\u006c\u002eo\u0072g\u002fd\u0063\u002f\u0074\u0065\u0072\u006d\u0073/",Local :"\u0063r\u0065\u0061\u0074\u0065\u0064"}:_af .Created =new (_ec .XSDAny );
if _ceb :=d .DecodeElement (_af .Created ,&_eg );_ceb !=nil {return _ceb ;};case _e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0072\u0067/\u0064c\u002f\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0073\u002f\u0031\u002e\u0031\u002f",Local :"\u0063r\u0065\u0061\u0074\u006f\u0072"}:_af .Creator =new (_ec .XSDAny );
if _ba :=d .DecodeElement (_af .Creator ,&_eg );_ba !=nil {return _ba ;};case _e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0072\u0067/\u0064c\u002f\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0073\u002f\u0031\u002e\u0031\u002f",Local :"d\u0065\u0073\u0063\u0072\u0069\u0070\u0074\u0069\u006f\u006e"}:_af .Description =new (_ec .XSDAny );
if _egf :=d .DecodeElement (_af .Description ,&_eg );_egf !=nil {return _egf ;};case _e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0072\u0067/\u0064c\u002f\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0073\u002f\u0031\u002e\u0031\u002f",Local :"\u0069\u0064\u0065\u006e\u0074\u0069\u0066\u0069\u0065\u0072"}:_af .Identifier =new (_ec .XSDAny );
if _afd :=d .DecodeElement (_af .Identifier ,&_eg );_afd !=nil {return _afd ;};case _e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063h\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067/\u0070\u0061c\u006b\u0061g\u0065\u002f\u0032\u0030\u0030\u0036\u002f\u006d\u0065\u0074\u0061\u0064\u0061ta\u002f\u0063\u006f\u0072\u0065\u002d\u0070\u0072\u006fp\u0065\u0072\u0074\u0069\u0065\u0073",Local :"\u006b\u0065\u0079\u0077\u006f\u0072\u0064\u0073"}:_af .Keywords =NewCT_Keywords ();
if _faa :=d .DecodeElement (_af .Keywords ,&_eg );_faa !=nil {return _faa ;};case _e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0072\u0067/\u0064c\u002f\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0073\u002f\u0031\u002e\u0031\u002f",Local :"\u006c\u0061\u006e\u0067\u0075\u0061\u0067\u0065"}:_af .Language =new (_ec .XSDAny );
if _geg :=d .DecodeElement (_af .Language ,&_eg );_geg !=nil {return _geg ;};case _e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063h\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067/\u0070\u0061c\u006b\u0061g\u0065\u002f\u0032\u0030\u0030\u0036\u002f\u006d\u0065\u0074\u0061\u0064\u0061ta\u002f\u0063\u006f\u0072\u0065\u002d\u0070\u0072\u006fp\u0065\u0072\u0074\u0069\u0065\u0073",Local :"\u006c\u0061\u0073\u0074\u004d\u006f\u0064\u0069\u0066i\u0065\u0064\u0042\u0079"}:_af .LastModifiedBy =new (string );
if _gbg :=d .DecodeElement (_af .LastModifiedBy ,&_eg );_gbg !=nil {return _gbg ;};case _e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063h\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067/\u0070\u0061c\u006b\u0061g\u0065\u002f\u0032\u0030\u0030\u0036\u002f\u006d\u0065\u0074\u0061\u0064\u0061ta\u002f\u0063\u006f\u0072\u0065\u002d\u0070\u0072\u006fp\u0065\u0072\u0074\u0069\u0065\u0073",Local :"l\u0061\u0073\u0074\u0050\u0072\u0069\u006e\u0074\u0065\u0064"}:var _bc string ;
if _fc :=d .DecodeElement (&_bc ,&_eg );_fc !=nil {return _fc ;};_fg ,_fd :=_b .ParseStdlibTime (_bc );if _fd !=nil {return _fd ;};_af .LastPrinted =&_fg ;case _e .Name {Space :"\u0068t\u0074\u0070\u003a\u002f/\u0070\u0075\u0072\u006c\u002eo\u0072g\u002fd\u0063\u002f\u0074\u0065\u0072\u006d\u0073/",Local :"\u006d\u006f\u0064\u0069\u0066\u0069\u0065\u0064"}:_af .Modified =new (_ec .XSDAny );
if _eb :=d .DecodeElement (_af .Modified ,&_eg );_eb !=nil {return _eb ;};case _e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063h\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067/\u0070\u0061c\u006b\u0061g\u0065\u002f\u0032\u0030\u0030\u0036\u002f\u006d\u0065\u0074\u0061\u0064\u0061ta\u002f\u0063\u006f\u0072\u0065\u002d\u0070\u0072\u006fp\u0065\u0072\u0074\u0069\u0065\u0073",Local :"\u0072\u0065\u0076\u0069\u0073\u0069\u006f\u006e"}:_af .Revision =new (string );
if _cebc :=d .DecodeElement (_af .Revision ,&_eg );_cebc !=nil {return _cebc ;};case _e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0072\u0067/\u0064c\u002f\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0073\u002f\u0031\u002e\u0031\u002f",Local :"\u0073u\u0062\u006a\u0065\u0063\u0074"}:_af .Subject =new (_ec .XSDAny );
if _ae :=d .DecodeElement (_af .Subject ,&_eg );_ae !=nil {return _ae ;};case _e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0072\u0067/\u0064c\u002f\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0073\u002f\u0031\u002e\u0031\u002f",Local :"\u0074\u0069\u0074l\u0065"}:_af .Title =new (_ec .XSDAny );
if _ea :=d .DecodeElement (_af .Title ,&_eg );_ea !=nil {return _ea ;};case _e .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063h\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067/\u0070\u0061c\u006b\u0061g\u0065\u002f\u0032\u0030\u0030\u0036\u002f\u006d\u0065\u0074\u0061\u0064\u0061ta\u002f\u0063\u006f\u0072\u0065\u002d\u0070\u0072\u006fp\u0065\u0072\u0074\u0069\u0065\u0073",Local :"\u0076e\u0072\u0073\u0069\u006f\u006e"}:_af .Version =new (string );
if _bcc :=d .DecodeElement (_af .Version ,&_eg );_bcc !=nil {return _bcc ;};default:_a .Log .Debug ("\u0073\u006bi\u0070\u0070\u0069\u006e\u0067 \u0075\u006e\u0073\u0075\u0070p\u006f\u0072\u0074\u0065\u0064\u0020\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0043\u0054\u005f\u0043\u006f\u0072\u0065\u0050\u0072\u006f\u0070\u0065\u0072\u0074\u0069\u0065\u0073\u0020\u0025\u0076",_eg .Name );
if _db :=d .Skip ();_db !=nil {return _db ;};};case _e .EndElement :break _ef ;case _e .CharData :};};return nil ;};

// ValidateWithPath validates the CT_Keyword and its children, prefixing error messages with path
func (_bbd *CT_Keyword )ValidateWithPath (path string )error {return nil };type CT_Keywords struct{LangAttr *string ;Value []*CT_Keyword ;};func (_d *CT_CoreProperties )MarshalXML (e *_e .Encoder ,start _e .StartElement )error {e .EncodeToken (start );
if _d .Category !=nil {_cc :=_e .StartElement {Name :_e .Name {Local :"c\u0070\u003a\u0063\u0061\u0074\u0065\u0067\u006f\u0072\u0079"}};_ec .AddPreserveSpaceAttr (&_cc ,*_d .Category );e .EncodeElement (_d .Category ,_cc );};if _d .ContentStatus !=nil {_ff :=_e .StartElement {Name :_e .Name {Local :"\u0063\u0070:\u0063\u006f\u006et\u0065\u006e\u0074\u0053\u0074\u0061\u0074\u0075\u0073"}};
_ec .AddPreserveSpaceAttr (&_ff ,*_d .ContentStatus );e .EncodeElement (_d .ContentStatus ,_ff );};if _d .Created !=nil {_gc :=_e .StartElement {Name :_e .Name {Local :"\u0064c\u0074e\u0072\u006d\u0073\u003a\u0063\u0072\u0065\u0061\u0074\u0065\u0064"}};
e .EncodeElement (_d .Created ,_gc );};if _d .Creator !=nil {_aa :=_e .StartElement {Name :_e .Name {Local :"\u0064\u0063\u003a\u0063\u0072\u0065\u0061\u0074\u006f\u0072"}};e .EncodeElement (_d .Creator ,_aa );};if _d .Description !=nil {_ca :=_e .StartElement {Name :_e .Name {Local :"\u0064\u0063\u003a\u0064\u0065\u0073\u0063\u0072\u0069p\u0074\u0069\u006f\u006e"}};
e .EncodeElement (_d .Description ,_ca );};if _d .Identifier !=nil {_ffb :=_e .StartElement {Name :_e .Name {Local :"\u0064\u0063\u003a\u0069\u0064\u0065\u006e\u0074\u0069\u0066\u0069\u0065\u0072"}};e .EncodeElement (_d .Identifier ,_ffb );};if _d .Keywords !=nil {_ge :=_e .StartElement {Name :_e .Name {Local :"c\u0070\u003a\u006b\u0065\u0079\u0077\u006f\u0072\u0064\u0073"}};
e .EncodeElement (_d .Keywords ,_ge );};if _d .Language !=nil {_cd :=_e .StartElement {Name :_e .Name {Local :"d\u0063\u003a\u006c\u0061\u006e\u0067\u0075\u0061\u0067\u0065"}};e .EncodeElement (_d .Language ,_cd );};if _d .LastModifiedBy !=nil {_ecg :=_e .StartElement {Name :_e .Name {Local :"\u0063\u0070\u003a\u006c\u0061\u0073\u0074\u004d\u006f\u0064\u0069\u0066i\u0065\u0064\u0042\u0079"}};
_ec .AddPreserveSpaceAttr (&_ecg ,*_d .LastModifiedBy );e .EncodeElement (_d .LastModifiedBy ,_ecg );};if _d .LastPrinted !=nil {_gcc :=_e .StartElement {Name :_e .Name {Local :"\u0063\u0070\u003a\u006c\u0061\u0073\u0074\u0050\u0072i\u006e\u0074\u0065\u0064"}};
e .EncodeElement (_b .FormatDateTime (*_d .LastPrinted ),_gcc );};if _d .Modified !=nil {_gb :=_e .StartElement {Name :_e .Name {Local :"\u0064\u0063t\u0065\u0072\u006ds\u003a\u006d\u006f\u0064\u0069\u0066\u0069\u0065\u0064"}};e .EncodeElement (_d .Modified ,_gb );
};if _d .Revision !=nil {_ce :=_e .StartElement {Name :_e .Name {Local :"c\u0070\u003a\u0072\u0065\u0076\u0069\u0073\u0069\u006f\u006e"}};_ec .AddPreserveSpaceAttr (&_ce ,*_d .Revision );e .EncodeElement (_d .Revision ,_ce );};if _d .Subject !=nil {_bb :=_e .StartElement {Name :_e .Name {Local :"\u0064\u0063\u003a\u0073\u0075\u0062\u006a\u0065\u0063\u0074"}};
e .EncodeElement (_d .Subject ,_bb );};if _d .Title !=nil {_da :=_e .StartElement {Name :_e .Name {Local :"\u0064\u0063\u003a\u0074\u0069\u0074\u006c\u0065"}};e .EncodeElement (_d .Title ,_da );};if _d .Version !=nil {_ga :=_e .StartElement {Name :_e .Name {Local :"\u0063\u0070\u003a\u0076\u0065\u0072\u0073\u0069\u006f\u006e"}};
_ec .AddPreserveSpaceAttr (&_ga ,*_d .Version );e .EncodeElement (_d .Version ,_ga );};e .EncodeToken (_e .EndElement {Name :start .Name });return nil ;};

// ValidateWithPath validates the CT_CoreProperties and its children, prefixing error messages with path
func (_gd *CT_CoreProperties )ValidateWithPath (path string )error {if _gd .Keywords !=nil {if _gcg :=_gd .Keywords .ValidateWithPath (path +"\u002fK\u0065\u0079\u0077\u006f\u0072\u0064s");_gcg !=nil {return _gcg ;};};return nil ;};type CoreProperties struct{CT_CoreProperties };


// Validate validates the CT_Keywords and its children
func (_efg *CT_Keywords )Validate ()error {return _efg .ValidateWithPath ("C\u0054\u005f\u004b\u0065\u0079\u0077\u006f\u0072\u0064\u0073");};func init (){_ec .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063h\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067/\u0070\u0061c\u006b\u0061g\u0065\u002f\u0032\u0030\u0030\u0036\u002f\u006d\u0065\u0074\u0061\u0064\u0061ta\u002f\u0063\u006f\u0072\u0065\u002d\u0070\u0072\u006fp\u0065\u0072\u0074\u0069\u0065\u0073","\u0043\u0054\u005f\u0043\u006f\u0072\u0065\u0050\u0072\u006f\u0070\u0065r\u0074\u0069\u0065\u0073",NewCT_CoreProperties );
_ec .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063h\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067/\u0070\u0061c\u006b\u0061g\u0065\u002f\u0032\u0030\u0030\u0036\u002f\u006d\u0065\u0074\u0061\u0064\u0061ta\u002f\u0063\u006f\u0072\u0065\u002d\u0070\u0072\u006fp\u0065\u0072\u0074\u0069\u0065\u0073","C\u0054\u005f\u004b\u0065\u0079\u0077\u006f\u0072\u0064\u0073",NewCT_Keywords );
_ec .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063h\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067/\u0070\u0061c\u006b\u0061g\u0065\u002f\u0032\u0030\u0030\u0036\u002f\u006d\u0065\u0074\u0061\u0064\u0061ta\u002f\u0063\u006f\u0072\u0065\u002d\u0070\u0072\u006fp\u0065\u0072\u0074\u0069\u0065\u0073","\u0043\u0054\u005f\u004b\u0065\u0079\u0077\u006f\u0072\u0064",NewCT_Keyword );
_ec .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063h\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067/\u0070\u0061c\u006b\u0061g\u0065\u002f\u0032\u0030\u0030\u0036\u002f\u006d\u0065\u0074\u0061\u0064\u0061ta\u002f\u0063\u006f\u0072\u0065\u002d\u0070\u0072\u006fp\u0065\u0072\u0074\u0069\u0065\u0073","\u0063\u006f\u0072\u0065\u0050\u0072\u006f\u0070\u0065r\u0074\u0069\u0065\u0073",NewCoreProperties );
};