//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package powerpoint ;import (_f "encoding/xml";_d "fmt";_gf "github.com/unidoc/unioffice/v2";);

// ValidateWithPath validates the CT_Rel and its children, prefixing error messages with path
func (_dee *CT_Rel )ValidateWithPath (path string )error {return nil };func NewIscomment ()*Iscomment {_gbg :=&Iscomment {};_gbg .CT_Empty =*NewCT_Empty ();return _gbg };

// Validate validates the CT_Empty and its children
func (_ec *CT_Empty )Validate ()error {return _ec .ValidateWithPath ("\u0043\u0054\u005f\u0045\u006d\u0070\u0074\u0079");};func NewCT_Empty ()*CT_Empty {_c :=&CT_Empty {};return _c };

// ValidateWithPath validates the CT_Empty and its children, prefixing error messages with path
func (_bg *CT_Empty )ValidateWithPath (path string )error {return nil };func (_ga *Textdata )UnmarshalXML (d *_f .Decoder ,start _f .StartElement )error {_ga .CT_Rel =*NewCT_Rel ();for _ ,_dd :=range start .Attr {if _dd .Name .Local =="\u0069\u0064"{_bge :=_dd .Value ;
_ga .IdAttr =&_bge ;continue ;};};for {_db ,_fd :=d .Token ();if _fd !=nil {return _d .Errorf ("p\u0061r\u0073\u0069\u006e\u0067\u0020\u0054\u0065\u0078t\u0064\u0061\u0074\u0061: \u0025\u0073",_fd );};if _dfc ,_cdg :=_db .(_f .EndElement );_cdg &&_dfc .Name ==start .Name {break ;
};};return nil ;};

// ValidateWithPath validates the Iscomment and its children, prefixing error messages with path
func (_bb *Iscomment )ValidateWithPath (path string )error {if _gfc :=_bb .CT_Empty .ValidateWithPath (path );_gfc !=nil {return _gfc ;};return nil ;};func (_cd *Iscomment )MarshalXML (e *_f .Encoder ,start _f .StartElement )error {start .Attr =append (start .Attr ,_f .Attr {Name :_f .Name {Local :"\u0078\u006d\u006cn\u0073"},Value :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068e\u006d\u0061\u0073-\u006d\u0069\u0063\u0072o\u0073\u006f\u0066\u0074\u002d\u0063\u006f\u006d\u003a\u006f\u0066\u0066\u0069\u0063\u0065\u003a\u0070\u006f\u0077\u0065\u0072\u0070\u006f\u0069\u006e\u0074"});
start .Attr =append (start .Attr ,_f .Attr {Name :_f .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0078\u006dl"},Value :"\u0068\u0074tp\u003a\u002f\u002fw\u0077\u0077\u002e\u00773.o\u0072g/\u0058\u004d\u004c\u002f\u0031\u0039\u00398/\u006e\u0061\u006d\u0065\u0073\u0070\u0061c\u0065"});
start .Name .Local ="\u0069s\u0063\u006f\u006d\u006d\u0065\u006et";return _cd .CT_Empty .MarshalXML (e ,start );};func (_gb *CT_Empty )UnmarshalXML (d *_f .Decoder ,start _f .StartElement )error {for {_cg ,_b :=d .Token ();if _b !=nil {return _d .Errorf ("p\u0061r\u0073\u0069\u006e\u0067\u0020\u0043\u0054\u005fE\u006d\u0070\u0074\u0079: \u0025\u0073",_b );
};if _gd ,_a :=_cg .(_f .EndElement );_a &&_gd .Name ==start .Name {break ;};};return nil ;};

// Validate validates the CT_Rel and its children
func (_gbf *CT_Rel )Validate ()error {return _gbf .ValidateWithPath ("\u0043\u0054\u005f\u0052\u0065\u006c");};type CT_Empty struct{};func (_fe *CT_Rel )UnmarshalXML (d *_f .Decoder ,start _f .StartElement )error {for _ ,_de :=range start .Attr {if _de .Name .Local =="\u0069\u0064"{_ed :=_de .Value ;
_fe .IdAttr =&_ed ;continue ;};};for {_cc ,_cgb :=d .Token ();if _cgb !=nil {return _d .Errorf ("\u0070a\u0072s\u0069\u006e\u0067\u0020\u0043T\u005f\u0052e\u006c\u003a\u0020\u0025\u0073",_cgb );};if _eb ,_da :=_cc .(_f .EndElement );_da &&_eb .Name ==start .Name {break ;
};};return nil ;};func NewTextdata ()*Textdata {_ba :=&Textdata {};_ba .CT_Rel =*NewCT_Rel ();return _ba };

// Validate validates the Textdata and its children
func (_bba *Textdata )Validate ()error {return _bba .ValidateWithPath ("\u0054\u0065\u0078\u0074\u0064\u0061\u0074\u0061");};func NewCT_Rel ()*CT_Rel {_ad :=&CT_Rel {};return _ad };

// ValidateWithPath validates the Textdata and its children, prefixing error messages with path
func (_bd *Textdata )ValidateWithPath (path string )error {if _ecd :=_bd .CT_Rel .ValidateWithPath (path );_ecd !=nil {return _ecd ;};return nil ;};type Iscomment struct{CT_Empty };type Textdata struct{CT_Rel };func (_cf *Iscomment )UnmarshalXML (d *_f .Decoder ,start _f .StartElement )error {_cf .CT_Empty =*NewCT_Empty ();
for {_ce ,_cb :=d .Token ();if _cb !=nil {return _d .Errorf ("p\u0061\u0072\u0073\u0069ng\u0020I\u0073\u0063\u006f\u006d\u006de\u006e\u0074\u003a\u0020\u0025\u0073",_cb );};if _ae ,_ggg :=_ce .(_f .EndElement );_ggg &&_ae .Name ==start .Name {break ;};
};return nil ;};func (_eg *CT_Rel )MarshalXML (e *_f .Encoder ,start _f .StartElement )error {if _eg .IdAttr !=nil {start .Attr =append (start .Attr ,_f .Attr {Name :_f .Name {Local :"\u0069\u0064"},Value :_d .Sprintf ("\u0025\u0076",*_eg .IdAttr )});};
e .EncodeToken (start );e .EncodeToken (_f .EndElement {Name :start .Name });return nil ;};type CT_Rel struct{

// Text Reference
IdAttr *string ;};func (_e *CT_Empty )MarshalXML (e *_f .Encoder ,start _f .StartElement )error {e .EncodeToken (start );e .EncodeToken (_f .EndElement {Name :start .Name });return nil ;};

// Validate validates the Iscomment and its children
func (_cgf *Iscomment )Validate ()error {return _cgf .ValidateWithPath ("\u0049s\u0063\u006f\u006d\u006d\u0065\u006et");};func (_df *Textdata )MarshalXML (e *_f .Encoder ,start _f .StartElement )error {start .Attr =append (start .Attr ,_f .Attr {Name :_f .Name {Local :"\u0078\u006d\u006cn\u0073"},Value :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068e\u006d\u0061\u0073-\u006d\u0069\u0063\u0072o\u0073\u006f\u0066\u0074\u002d\u0063\u006f\u006d\u003a\u006f\u0066\u0066\u0069\u0063\u0065\u003a\u0070\u006f\u0077\u0065\u0072\u0070\u006f\u0069\u006e\u0074"});
start .Attr =append (start .Attr ,_f .Attr {Name :_f .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0078\u006dl"},Value :"\u0068\u0074tp\u003a\u002f\u002fw\u0077\u0077\u002e\u00773.o\u0072g/\u0058\u004d\u004c\u002f\u0031\u0039\u00398/\u006e\u0061\u006d\u0065\u0073\u0070\u0061c\u0065"});
start .Name .Local ="\u0074\u0065\u0078\u0074\u0064\u0061\u0074\u0061";return _df .CT_Rel .MarshalXML (e ,start );};func init (){_gf .RegisterConstructor ("\u0075\u0072\u006e\u003a\u0073\u0063\u0068e\u006d\u0061\u0073-\u006d\u0069\u0063\u0072o\u0073\u006f\u0066\u0074\u002d\u0063\u006f\u006d\u003a\u006f\u0066\u0066\u0069\u0063\u0065\u003a\u0070\u006f\u0077\u0065\u0072\u0070\u006f\u0069\u006e\u0074","\u0043\u0054\u005f\u0045\u006d\u0070\u0074\u0079",NewCT_Empty );
_gf .RegisterConstructor ("\u0075\u0072\u006e\u003a\u0073\u0063\u0068e\u006d\u0061\u0073-\u006d\u0069\u0063\u0072o\u0073\u006f\u0066\u0074\u002d\u0063\u006f\u006d\u003a\u006f\u0066\u0066\u0069\u0063\u0065\u003a\u0070\u006f\u0077\u0065\u0072\u0070\u006f\u0069\u006e\u0074","\u0043\u0054\u005f\u0052\u0065\u006c",NewCT_Rel );
_gf .RegisterConstructor ("\u0075\u0072\u006e\u003a\u0073\u0063\u0068e\u006d\u0061\u0073-\u006d\u0069\u0063\u0072o\u0073\u006f\u0066\u0074\u002d\u0063\u006f\u006d\u003a\u006f\u0066\u0066\u0069\u0063\u0065\u003a\u0070\u006f\u0077\u0065\u0072\u0070\u006f\u0069\u006e\u0074","\u0069s\u0063\u006f\u006d\u006d\u0065\u006et",NewIscomment );
_gf .RegisterConstructor ("\u0075\u0072\u006e\u003a\u0073\u0063\u0068e\u006d\u0061\u0073-\u006d\u0069\u0063\u0072o\u0073\u006f\u0066\u0074\u002d\u0063\u006f\u006d\u003a\u006f\u0066\u0066\u0069\u0063\u0065\u003a\u0070\u006f\u0077\u0065\u0072\u0070\u006f\u0069\u006e\u0074","\u0074\u0065\u0078\u0074\u0064\u0061\u0074\u0061",NewTextdata );
};