//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package word ;import (_c "encoding/xml";_d "fmt";_af "github.com/unidoc/unioffice/v2";_a "strconv";);func NewBorderleft ()*Borderleft {_fg :=&Borderleft {};_fg .CT_Border =*NewCT_Border ();return _fg };type Borderleft struct{CT_Border };func (_ggd *ST_BorderType )UnmarshalXMLAttr (attr _c .Attr )error {switch attr .Value {case "":*_ggd =0;
case "\u006e\u006f\u006e\u0065":*_ggd =1;case "\u0073\u0069\u006e\u0067\u006c\u0065":*_ggd =2;case "\u0074\u0068\u0069c\u006b":*_ggd =3;case "\u0064\u006f\u0075\u0062\u006c\u0065":*_ggd =4;case "\u0068\u0061\u0069\u0072\u006c\u0069\u006e\u0065":*_ggd =5;
case "\u0064\u006f\u0074":*_ggd =6;case "\u0064\u0061\u0073\u0068":*_ggd =7;case "\u0064o\u0074\u0044\u0061\u0073\u0068":*_ggd =8;case "\u0064\u0061\u0073\u0068\u0044\u006f\u0074\u0044\u006f\u0074":*_ggd =9;case "\u0074\u0072\u0069\u0070\u006c\u0065":*_ggd =10;
case "\u0074\u0068\u0069\u006e\u0054\u0068\u0069\u0063\u006bS\u006d\u0061\u006c\u006c":*_ggd =11;case "\u0074\u0068\u0069\u0063\u006b\u0054\u0068\u0069\u006eS\u006d\u0061\u006c\u006c":*_ggd =12;case "t\u0068\u0069\u0063\u006bBe\u0074w\u0065\u0065\u006e\u0054\u0068i\u006e\u0053\u006d\u0061\u006c\u006c":*_ggd =13;
case "\u0074h\u0069\u006e\u0054\u0068\u0069\u0063k":*_ggd =14;case "\u0074h\u0069\u0063\u006b\u0054\u0068\u0069n":*_ggd =15;case "\u0074\u0068i\u0063\u006b\u0042e\u0074\u0077\u0065\u0065\u006e\u0054\u0068\u0069\u006e":*_ggd =16;case "\u0074\u0068\u0069\u006e\u0054\u0068\u0069\u0063\u006bL\u0061\u0072\u0067\u0065":*_ggd =17;
case "\u0074\u0068\u0069\u0063\u006b\u0054\u0068\u0069\u006eL\u0061\u0072\u0067\u0065":*_ggd =18;case "t\u0068\u0069\u0063\u006bBe\u0074w\u0065\u0065\u006e\u0054\u0068i\u006e\u004c\u0061\u0072\u0067\u0065":*_ggd =19;case "\u0077\u0061\u0076\u0065":*_ggd =20;
case "\u0064\u006f\u0075\u0062\u006c\u0065\u0057\u0061\u0076\u0065":*_ggd =21;case "d\u0061\u0073\u0068\u0065\u0064\u0053\u006d\u0061\u006c\u006c":*_ggd =22;case "\u0064\u0061\u0073\u0068\u0044\u006f\u0074\u0053\u0074r\u006f\u006b\u0065\u0064":*_ggd =23;
case "\u0074\u0068\u0072e\u0065\u0044\u0045\u006d\u0062\u006f\u0073\u0073":*_ggd =24;case "\u0074\u0068\u0072\u0065\u0065\u0044\u0045\u006e\u0067\u0072\u0061\u0076\u0065":*_ggd =25;case "\u0048\u0054\u004d\u004c\u004f\u0075\u0074\u0073\u0065\u0074":*_ggd =26;
case "\u0048T\u004d\u004c\u0049\u006e\u0073\u0065t":*_ggd =27;};return nil ;};func (_dc *Borderright )MarshalXML (e *_c .Encoder ,start _c .StartElement )error {start .Attr =append (start .Attr ,_c .Attr {Name :_c .Name {Local :"\u0078\u006d\u006cn\u0073"},Value :"\u0075\u0072n\u003a\u0073\u0063\u0068e\u006d\u0061s\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006ff\u0074\u002d\u0063\u006f\u006d\u003a\u006f\u0066\u0066\u0069\u0063\u0065:\u0077\u006f\u0072\u0064"});
start .Attr =append (start .Attr ,_c .Attr {Name :_c .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0078\u006dl"},Value :"\u0068\u0074tp\u003a\u002f\u002fw\u0077\u0077\u002e\u00773.o\u0072g/\u0058\u004d\u004c\u002f\u0031\u0039\u00398/\u006e\u0061\u006d\u0065\u0073\u0070\u0061c\u0065"});
start .Name .Local ="b\u006f\u0072\u0064\u0065\u0072\u0072\u0069\u0067\u0068\u0074";return _dc .CT_Border .MarshalXML (e ,start );};func (_aegd ST_WrapType )ValidateWithPath (path string )error {switch _aegd {case 0,1,2,3,4,5:default:return _d .Errorf ("\u0025s\u003a\u0020\u006f\u0075t\u0020\u006f\u0066\u0020\u0072a\u006eg\u0065 \u0076\u0061\u006c\u0075\u0065\u0020\u0025d",path ,int (_aegd ));
};return nil ;};func NewCT_Wrap ()*CT_Wrap {_efg :=&CT_Wrap {};return _efg };func (_eca *CT_Wrap )UnmarshalXML (d *_c .Decoder ,start _c .StartElement )error {for _ ,_ebg :=range start .Attr {if _ebg .Name .Local =="\u0074\u0079\u0070\u0065"{_eca .TypeAttr .UnmarshalXMLAttr (_ebg );
continue ;};if _ebg .Name .Local =="\u0073\u0069\u0064\u0065"{_eca .SideAttr .UnmarshalXMLAttr (_ebg );continue ;};if _ebg .Name .Local =="\u0061n\u0063\u0068\u006f\u0072\u0078"{_eca .AnchorxAttr .UnmarshalXMLAttr (_ebg );continue ;};if _ebg .Name .Local =="\u0061n\u0063\u0068\u006f\u0072\u0079"{_eca .AnchoryAttr .UnmarshalXMLAttr (_ebg );
continue ;};};for {_eae ,_bfeg :=d .Token ();if _bfeg !=nil {return _d .Errorf ("\u0070\u0061\u0072\u0073in\u0067\u0020\u0043\u0054\u005f\u0057\u0072\u0061\u0070\u003a\u0020\u0025\u0073",_bfeg );};if _fcf ,_gaa :=_eae .(_c .EndElement );_gaa &&_fcf .Name ==start .Name {break ;
};};return nil ;};

// Validate validates the CT_AnchorLock and its children
func (_dgd *CT_AnchorLock )Validate ()error {return _dgd .ValidateWithPath ("\u0043\u0054\u005f\u0041\u006e\u0063\u0068\u006f\u0072\u004c\u006f\u0063\u006b");};func (_bae *CT_Wrap )MarshalXML (e *_c .Encoder ,start _c .StartElement )error {if _bae .TypeAttr !=ST_WrapTypeUnset {_aeg ,_bea :=_bae .TypeAttr .MarshalXMLAttr (_c .Name {Local :"\u0074\u0079\u0070\u0065"});
if _bea !=nil {return _bea ;};start .Attr =append (start .Attr ,_aeg );};if _bae .SideAttr !=ST_WrapSideUnset {_ad ,_cbdd :=_bae .SideAttr .MarshalXMLAttr (_c .Name {Local :"\u0073\u0069\u0064\u0065"});if _cbdd !=nil {return _cbdd ;};start .Attr =append (start .Attr ,_ad );
};if _bae .AnchorxAttr !=ST_HorizontalAnchorUnset {_ccf ,_ecg :=_bae .AnchorxAttr .MarshalXMLAttr (_c .Name {Local :"\u0061n\u0063\u0068\u006f\u0072\u0078"});if _ecg !=nil {return _ecg ;};start .Attr =append (start .Attr ,_ccf );};if _bae .AnchoryAttr !=ST_VerticalAnchorUnset {_abd ,_gfe :=_bae .AnchoryAttr .MarshalXMLAttr (_c .Name {Local :"\u0061n\u0063\u0068\u006f\u0072\u0079"});
if _gfe !=nil {return _gfe ;};start .Attr =append (start .Attr ,_abd );};e .EncodeToken (start );e .EncodeToken (_c .EndElement {Name :start .Name });return nil ;};

// Validate validates the Borderbottom and its children
func (_dac *Borderbottom )Validate ()error {return _dac .ValidateWithPath ("\u0042\u006f\u0072d\u0065\u0072\u0062\u006f\u0074\u0074\u006f\u006d");};func (_b *Borderbottom )MarshalXML (e *_c .Encoder ,start _c .StartElement )error {start .Attr =append (start .Attr ,_c .Attr {Name :_c .Name {Local :"\u0078\u006d\u006cn\u0073"},Value :"\u0075\u0072n\u003a\u0073\u0063\u0068e\u006d\u0061s\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006ff\u0074\u002d\u0063\u006f\u006d\u003a\u006f\u0066\u0066\u0069\u0063\u0065:\u0077\u006f\u0072\u0064"});
start .Attr =append (start .Attr ,_c .Attr {Name :_c .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0078\u006dl"},Value :"\u0068\u0074tp\u003a\u002f\u002fw\u0077\u0077\u002e\u00773.o\u0072g/\u0058\u004d\u004c\u002f\u0031\u0039\u00398/\u006e\u0061\u006d\u0065\u0073\u0070\u0061c\u0065"});
start .Name .Local ="\u0062\u006f\u0072d\u0065\u0072\u0062\u006f\u0074\u0074\u006f\u006d";return _b .CT_Border .MarshalXML (e ,start );};func (_gbc ST_WrapSide )ValidateWithPath (path string )error {switch _gbc {case 0,1,2,3,4:default:return _d .Errorf ("\u0025s\u003a\u0020\u006f\u0075t\u0020\u006f\u0066\u0020\u0072a\u006eg\u0065 \u0076\u0061\u006c\u0075\u0065\u0020\u0025d",path ,int (_gbc ));
};return nil ;};

// Validate validates the Bordertop and its children
func (_eb *Bordertop )Validate ()error {return _eb .ValidateWithPath ("\u0042o\u0072\u0064\u0065\u0072\u0074\u006fp");};func (_cdf ST_VerticalAnchor )MarshalXMLAttr (name _c .Name )(_c .Attr ,error ){_bgdc :=_c .Attr {};_bgdc .Name =name ;switch _cdf {case ST_VerticalAnchorUnset :_bgdc .Value ="";
case ST_VerticalAnchorMargin :_bgdc .Value ="\u006d\u0061\u0072\u0067\u0069\u006e";case ST_VerticalAnchorPage :_bgdc .Value ="\u0070\u0061\u0067\u0065";case ST_VerticalAnchorText :_bgdc .Value ="\u0074\u0065\u0078\u0074";case ST_VerticalAnchorLine :_bgdc .Value ="\u006c\u0069\u006e\u0065";
};return _bgdc ,nil ;};func NewBordertop ()*Bordertop {_fca :=&Bordertop {};_fca .CT_Border =*NewCT_Border ();return _fca };func NewAnchorlock ()*Anchorlock {_fa :=&Anchorlock {};_fa .CT_AnchorLock =*NewCT_AnchorLock ();return _fa ;};func (_bca ST_BorderType )ValidateWithPath (path string )error {switch _bca {case 0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27:default:return _d .Errorf ("\u0025s\u003a\u0020\u006f\u0075t\u0020\u006f\u0066\u0020\u0072a\u006eg\u0065 \u0076\u0061\u006c\u0075\u0065\u0020\u0025d",path ,int (_bca ));
};return nil ;};func (_gcf ST_BorderType )MarshalXMLAttr (name _c .Name )(_c .Attr ,error ){_gdg :=_c .Attr {};_gdg .Name =name ;switch _gcf {case ST_BorderTypeUnset :_gdg .Value ="";case ST_BorderTypeNone :_gdg .Value ="\u006e\u006f\u006e\u0065";case ST_BorderTypeSingle :_gdg .Value ="\u0073\u0069\u006e\u0067\u006c\u0065";
case ST_BorderTypeThick :_gdg .Value ="\u0074\u0068\u0069c\u006b";case ST_BorderTypeDouble :_gdg .Value ="\u0064\u006f\u0075\u0062\u006c\u0065";case ST_BorderTypeHairline :_gdg .Value ="\u0068\u0061\u0069\u0072\u006c\u0069\u006e\u0065";case ST_BorderTypeDot :_gdg .Value ="\u0064\u006f\u0074";
case ST_BorderTypeDash :_gdg .Value ="\u0064\u0061\u0073\u0068";case ST_BorderTypeDotDash :_gdg .Value ="\u0064o\u0074\u0044\u0061\u0073\u0068";case ST_BorderTypeDashDotDot :_gdg .Value ="\u0064\u0061\u0073\u0068\u0044\u006f\u0074\u0044\u006f\u0074";case ST_BorderTypeTriple :_gdg .Value ="\u0074\u0072\u0069\u0070\u006c\u0065";
case ST_BorderTypeThinThickSmall :_gdg .Value ="\u0074\u0068\u0069\u006e\u0054\u0068\u0069\u0063\u006bS\u006d\u0061\u006c\u006c";case ST_BorderTypeThickThinSmall :_gdg .Value ="\u0074\u0068\u0069\u0063\u006b\u0054\u0068\u0069\u006eS\u006d\u0061\u006c\u006c";
case ST_BorderTypeThickBetweenThinSmall :_gdg .Value ="t\u0068\u0069\u0063\u006bBe\u0074w\u0065\u0065\u006e\u0054\u0068i\u006e\u0053\u006d\u0061\u006c\u006c";case ST_BorderTypeThinThick :_gdg .Value ="\u0074h\u0069\u006e\u0054\u0068\u0069\u0063k";case ST_BorderTypeThickThin :_gdg .Value ="\u0074h\u0069\u0063\u006b\u0054\u0068\u0069n";
case ST_BorderTypeThickBetweenThin :_gdg .Value ="\u0074\u0068i\u0063\u006b\u0042e\u0074\u0077\u0065\u0065\u006e\u0054\u0068\u0069\u006e";case ST_BorderTypeThinThickLarge :_gdg .Value ="\u0074\u0068\u0069\u006e\u0054\u0068\u0069\u0063\u006bL\u0061\u0072\u0067\u0065";
case ST_BorderTypeThickThinLarge :_gdg .Value ="\u0074\u0068\u0069\u0063\u006b\u0054\u0068\u0069\u006eL\u0061\u0072\u0067\u0065";case ST_BorderTypeThickBetweenThinLarge :_gdg .Value ="t\u0068\u0069\u0063\u006bBe\u0074w\u0065\u0065\u006e\u0054\u0068i\u006e\u004c\u0061\u0072\u0067\u0065";
case ST_BorderTypeWave :_gdg .Value ="\u0077\u0061\u0076\u0065";case ST_BorderTypeDoubleWave :_gdg .Value ="\u0064\u006f\u0075\u0062\u006c\u0065\u0057\u0061\u0076\u0065";case ST_BorderTypeDashedSmall :_gdg .Value ="d\u0061\u0073\u0068\u0065\u0064\u0053\u006d\u0061\u006c\u006c";
case ST_BorderTypeDashDotStroked :_gdg .Value ="\u0064\u0061\u0073\u0068\u0044\u006f\u0074\u0053\u0074r\u006f\u006b\u0065\u0064";case ST_BorderTypeThreeDEmboss :_gdg .Value ="\u0074\u0068\u0072e\u0065\u0044\u0045\u006d\u0062\u006f\u0073\u0073";case ST_BorderTypeThreeDEngrave :_gdg .Value ="\u0074\u0068\u0072\u0065\u0065\u0044\u0045\u006e\u0067\u0072\u0061\u0076\u0065";
case ST_BorderTypeHTMLOutset :_gdg .Value ="\u0048\u0054\u004d\u004c\u004f\u0075\u0074\u0073\u0065\u0074";case ST_BorderTypeHTMLInset :_gdg .Value ="\u0048T\u004d\u004c\u0049\u006e\u0073\u0065t";};return _gdg ,nil ;};type ST_BorderShadow byte ;type ST_WrapSide byte ;
func (_ddgf *ST_BorderShadow )UnmarshalXML (d *_c .Decoder ,start _c .StartElement )error {_aca ,_caaf :=d .Token ();if _caaf !=nil {return _caaf ;};if _bd ,_agc :=_aca .(_c .EndElement );_agc &&_bd .Name ==start .Name {*_ddgf =1;return nil ;};if _dde ,_aef :=_aca .(_c .CharData );
!_aef {return _d .Errorf ("\u0065\u0078\u0070\u0065\u0063\u0074\u0065\u0064\u0020\u0063\u0068a\u0072\u0020\u0064\u0061\u0074\u0061\u002c\u0020\u0067\u006ft\u0020\u0025\u0054",_aca );}else {switch string (_dde ){case "":*_ddgf =0;case "\u0074":*_ddgf =1;
case "\u0074\u0072\u0075\u0065":*_ddgf =2;case "\u0066":*_ddgf =3;case "\u0066\u0061\u006cs\u0065":*_ddgf =4;};};_aca ,_caaf =d .Token ();if _caaf !=nil {return _caaf ;};if _ddec ,_acg :=_aca .(_c .EndElement );_acg &&_ddec .Name ==start .Name {return nil ;
};return _d .Errorf ("\u0065\u0078\u0070\u0065c\u0074\u0065\u0064\u0020\u0065\u006e\u0064\u0020\u0065\u006ce\u006de\u006e\u0074\u002c\u0020\u0067\u006f\u0074 \u0025\u0076",_aca );};

// Validate validates the CT_Border and its children
func (_bee *CT_Border )Validate ()error {return _bee .ValidateWithPath ("\u0043T\u005f\u0042\u006f\u0072\u0064\u0065r");};

// ValidateWithPath validates the CT_AnchorLock and its children, prefixing error messages with path
func (_fgf *CT_AnchorLock )ValidateWithPath (path string )error {return nil };func (_eac ST_BorderType )MarshalXML (e *_c .Encoder ,start _c .StartElement )error {return e .EncodeElement (_eac .String (),start );};func (_ged ST_BorderType )Validate ()error {return _ged .ValidateWithPath ("")};


// Validate validates the CT_Wrap and its children
func (_dacc *CT_Wrap )Validate ()error {return _dacc .ValidateWithPath ("\u0043T\u005f\u0057\u0072\u0061\u0070");};

// Validate validates the Borderright and its children
func (_fac *Borderright )Validate ()error {return _fac .ValidateWithPath ("B\u006f\u0072\u0064\u0065\u0072\u0072\u0069\u0067\u0068\u0074");};const (ST_BorderTypeUnset ST_BorderType =0;ST_BorderTypeNone ST_BorderType =1;ST_BorderTypeSingle ST_BorderType =2;
ST_BorderTypeThick ST_BorderType =3;ST_BorderTypeDouble ST_BorderType =4;ST_BorderTypeHairline ST_BorderType =5;ST_BorderTypeDot ST_BorderType =6;ST_BorderTypeDash ST_BorderType =7;ST_BorderTypeDotDash ST_BorderType =8;ST_BorderTypeDashDotDot ST_BorderType =9;
ST_BorderTypeTriple ST_BorderType =10;ST_BorderTypeThinThickSmall ST_BorderType =11;ST_BorderTypeThickThinSmall ST_BorderType =12;ST_BorderTypeThickBetweenThinSmall ST_BorderType =13;ST_BorderTypeThinThick ST_BorderType =14;ST_BorderTypeThickThin ST_BorderType =15;
ST_BorderTypeThickBetweenThin ST_BorderType =16;ST_BorderTypeThinThickLarge ST_BorderType =17;ST_BorderTypeThickThinLarge ST_BorderType =18;ST_BorderTypeThickBetweenThinLarge ST_BorderType =19;ST_BorderTypeWave ST_BorderType =20;ST_BorderTypeDoubleWave ST_BorderType =21;
ST_BorderTypeDashedSmall ST_BorderType =22;ST_BorderTypeDashDotStroked ST_BorderType =23;ST_BorderTypeThreeDEmboss ST_BorderType =24;ST_BorderTypeThreeDEngrave ST_BorderType =25;ST_BorderTypeHTMLOutset ST_BorderType =26;ST_BorderTypeHTMLInset ST_BorderType =27;
);func (_aee *ST_WrapSide )UnmarshalXML (d *_c .Decoder ,start _c .StartElement )error {_dfbda ,_efgb :=d .Token ();if _efgb !=nil {return _efgb ;};if _abda ,_baee :=_dfbda .(_c .EndElement );_baee &&_abda .Name ==start .Name {*_aee =1;return nil ;};if _bgc ,_fae :=_dfbda .(_c .CharData );
!_fae {return _d .Errorf ("\u0065\u0078\u0070\u0065\u0063\u0074\u0065\u0064\u0020\u0063\u0068a\u0072\u0020\u0064\u0061\u0074\u0061\u002c\u0020\u0067\u006ft\u0020\u0025\u0054",_dfbda );}else {switch string (_bgc ){case "":*_aee =0;case "\u0062\u006f\u0074\u0068":*_aee =1;
case "\u006c\u0065\u0066\u0074":*_aee =2;case "\u0072\u0069\u0067h\u0074":*_aee =3;case "\u006ca\u0072\u0067\u0065\u0073\u0074":*_aee =4;};};_dfbda ,_efgb =d .Token ();if _efgb !=nil {return _efgb ;};if _gegd ,_fbcf :=_dfbda .(_c .EndElement );_fbcf &&_gegd .Name ==start .Name {return nil ;
};return _d .Errorf ("\u0065\u0078\u0070\u0065c\u0074\u0065\u0064\u0020\u0065\u006e\u0064\u0020\u0065\u006ce\u006de\u006e\u0074\u002c\u0020\u0067\u006f\u0074 \u0025\u0076",_dfbda );};const (ST_HorizontalAnchorUnset ST_HorizontalAnchor =0;ST_HorizontalAnchorMargin ST_HorizontalAnchor =1;
ST_HorizontalAnchorPage ST_HorizontalAnchor =2;ST_HorizontalAnchorText ST_HorizontalAnchor =3;ST_HorizontalAnchorChar ST_HorizontalAnchor =4;);

// ValidateWithPath validates the Anchorlock and its children, prefixing error messages with path
func (_ab *Anchorlock )ValidateWithPath (path string )error {if _ae :=_ab .CT_AnchorLock .ValidateWithPath (path );_ae !=nil {return _ae ;};return nil ;};func (_ccb ST_BorderType )String ()string {switch _ccb {case 0:return "";case 1:return "\u006e\u006f\u006e\u0065";
case 2:return "\u0073\u0069\u006e\u0067\u006c\u0065";case 3:return "\u0074\u0068\u0069c\u006b";case 4:return "\u0064\u006f\u0075\u0062\u006c\u0065";case 5:return "\u0068\u0061\u0069\u0072\u006c\u0069\u006e\u0065";case 6:return "\u0064\u006f\u0074";case 7:return "\u0064\u0061\u0073\u0068";
case 8:return "\u0064o\u0074\u0044\u0061\u0073\u0068";case 9:return "\u0064\u0061\u0073\u0068\u0044\u006f\u0074\u0044\u006f\u0074";case 10:return "\u0074\u0072\u0069\u0070\u006c\u0065";case 11:return "\u0074\u0068\u0069\u006e\u0054\u0068\u0069\u0063\u006bS\u006d\u0061\u006c\u006c";
case 12:return "\u0074\u0068\u0069\u0063\u006b\u0054\u0068\u0069\u006eS\u006d\u0061\u006c\u006c";case 13:return "t\u0068\u0069\u0063\u006bBe\u0074w\u0065\u0065\u006e\u0054\u0068i\u006e\u0053\u006d\u0061\u006c\u006c";case 14:return "\u0074h\u0069\u006e\u0054\u0068\u0069\u0063k";
case 15:return "\u0074h\u0069\u0063\u006b\u0054\u0068\u0069n";case 16:return "\u0074\u0068i\u0063\u006b\u0042e\u0074\u0077\u0065\u0065\u006e\u0054\u0068\u0069\u006e";case 17:return "\u0074\u0068\u0069\u006e\u0054\u0068\u0069\u0063\u006bL\u0061\u0072\u0067\u0065";
case 18:return "\u0074\u0068\u0069\u0063\u006b\u0054\u0068\u0069\u006eL\u0061\u0072\u0067\u0065";case 19:return "t\u0068\u0069\u0063\u006bBe\u0074w\u0065\u0065\u006e\u0054\u0068i\u006e\u004c\u0061\u0072\u0067\u0065";case 20:return "\u0077\u0061\u0076\u0065";
case 21:return "\u0064\u006f\u0075\u0062\u006c\u0065\u0057\u0061\u0076\u0065";case 22:return "d\u0061\u0073\u0068\u0065\u0064\u0053\u006d\u0061\u006c\u006c";case 23:return "\u0064\u0061\u0073\u0068\u0044\u006f\u0074\u0053\u0074r\u006f\u006b\u0065\u0064";
case 24:return "\u0074\u0068\u0072e\u0065\u0044\u0045\u006d\u0062\u006f\u0073\u0073";case 25:return "\u0074\u0068\u0072\u0065\u0065\u0044\u0045\u006e\u0067\u0072\u0061\u0076\u0065";case 26:return "\u0048\u0054\u004d\u004c\u004f\u0075\u0074\u0073\u0065\u0074";
case 27:return "\u0048T\u004d\u004c\u0049\u006e\u0073\u0065t";};return "";};func (_dgb *Bordertop )UnmarshalXML (d *_c .Decoder ,start _c .StartElement )error {_dgb .CT_Border =*NewCT_Border ();for _ ,_df :=range start .Attr {if _df .Name .Local =="\u0074\u0079\u0070\u0065"{_dgb .TypeAttr .UnmarshalXMLAttr (_df );
continue ;};if _df .Name .Local =="\u0077\u0069\u0064t\u0068"{_caa ,_bfe :=_a .ParseUint (_df .Value ,10,32);if _bfe !=nil {return _bfe ;};_dbd :=uint32 (_caa );_dgb .WidthAttr =&_dbd ;continue ;};if _df .Name .Local =="\u0073\u0068\u0061\u0064\u006f\u0077"{_dgb .ShadowAttr .UnmarshalXMLAttr (_df );
continue ;};};for {_cf ,_fbd :=d .Token ();if _fbd !=nil {return _d .Errorf ("p\u0061\u0072\u0073\u0069ng\u0020B\u006f\u0072\u0064\u0065\u0072t\u006f\u0070\u003a\u0020\u0025\u0073",_fbd );};if _fd ,_bc :=_cf .(_c .EndElement );_bc &&_fd .Name ==start .Name {break ;
};};return nil ;};type ST_BorderType byte ;func (_eeb *ST_HorizontalAnchor )UnmarshalXML (d *_c .Decoder ,start _c .StartElement )error {_acd ,_fcd :=d .Token ();if _fcd !=nil {return _fcd ;};if _adc ,_fef :=_acd .(_c .EndElement );_fef &&_adc .Name ==start .Name {*_eeb =1;
return nil ;};if _gaad ,_caf :=_acd .(_c .CharData );!_caf {return _d .Errorf ("\u0065\u0078\u0070\u0065\u0063\u0074\u0065\u0064\u0020\u0063\u0068a\u0072\u0020\u0064\u0061\u0074\u0061\u002c\u0020\u0067\u006ft\u0020\u0025\u0054",_acd );}else {switch string (_gaad ){case "":*_eeb =0;
case "\u006d\u0061\u0072\u0067\u0069\u006e":*_eeb =1;case "\u0070\u0061\u0067\u0065":*_eeb =2;case "\u0074\u0065\u0078\u0074":*_eeb =3;case "\u0063\u0068\u0061\u0072":*_eeb =4;};};_acd ,_fcd =d .Token ();if _fcd !=nil {return _fcd ;};if _ege ,_cfec :=_acd .(_c .EndElement );
_cfec &&_ege .Name ==start .Name {return nil ;};return _d .Errorf ("\u0065\u0078\u0070\u0065c\u0074\u0065\u0064\u0020\u0065\u006e\u0064\u0020\u0065\u006ce\u006de\u006e\u0074\u002c\u0020\u0067\u006f\u0074 \u0025\u0076",_acd );};func (_geda ST_HorizontalAnchor )MarshalXMLAttr (name _c .Name )(_c .Attr ,error ){_ddeg :=_c .Attr {};
_ddeg .Name =name ;switch _geda {case ST_HorizontalAnchorUnset :_ddeg .Value ="";case ST_HorizontalAnchorMargin :_ddeg .Value ="\u006d\u0061\u0072\u0067\u0069\u006e";case ST_HorizontalAnchorPage :_ddeg .Value ="\u0070\u0061\u0067\u0065";case ST_HorizontalAnchorText :_ddeg .Value ="\u0074\u0065\u0078\u0074";
case ST_HorizontalAnchorChar :_ddeg .Value ="\u0063\u0068\u0061\u0072";};return _ddeg ,nil ;};type Borderright struct{CT_Border };func (_fe *Anchorlock )UnmarshalXML (d *_c .Decoder ,start _c .StartElement )error {_fe .CT_AnchorLock =*NewCT_AnchorLock ();
for {_cb ,_cg :=d .Token ();if _cg !=nil {return _d .Errorf ("\u0070\u0061\u0072\u0073in\u0067\u0020\u0041\u006e\u0063\u0068\u006f\u0072\u006c\u006f\u0063\u006b\u003a\u0020%\u0073",_cg );};if _ff ,_gb :=_cb .(_c .EndElement );_gb &&_ff .Name ==start .Name {break ;
};};return nil ;};

// ValidateWithPath validates the Wrap and its children, prefixing error messages with path
func (_aega *Wrap )ValidateWithPath (path string )error {if _cfeg :=_aega .CT_Wrap .ValidateWithPath (path );_cfeg !=nil {return _cfeg ;};return nil ;};type CT_AnchorLock struct{};func (_bdc ST_BorderShadow )Validate ()error {return _bdc .ValidateWithPath ("")};
func (_eee ST_HorizontalAnchor )String ()string {switch _eee {case 0:return "";case 1:return "\u006d\u0061\u0072\u0067\u0069\u006e";case 2:return "\u0070\u0061\u0067\u0065";case 3:return "\u0074\u0065\u0078\u0074";case 4:return "\u0063\u0068\u0061\u0072";
};return "";};func (_fgb *ST_VerticalAnchor )UnmarshalXML (d *_c .Decoder ,start _c .StartElement )error {_ddfc ,_efda :=d .Token ();if _efda !=nil {return _efda ;};if _ecgb ,_gce :=_ddfc .(_c .EndElement );_gce &&_ecgb .Name ==start .Name {*_fgb =1;return nil ;
};if _gae ,_bdcd :=_ddfc .(_c .CharData );!_bdcd {return _d .Errorf ("\u0065\u0078\u0070\u0065\u0063\u0074\u0065\u0064\u0020\u0063\u0068a\u0072\u0020\u0064\u0061\u0074\u0061\u002c\u0020\u0067\u006ft\u0020\u0025\u0054",_ddfc );}else {switch string (_gae ){case "":*_fgb =0;
case "\u006d\u0061\u0072\u0067\u0069\u006e":*_fgb =1;case "\u0070\u0061\u0067\u0065":*_fgb =2;case "\u0074\u0065\u0078\u0074":*_fgb =3;case "\u006c\u0069\u006e\u0065":*_fgb =4;};};_ddfc ,_efda =d .Token ();if _efda !=nil {return _efda ;};if _bcd ,_agb :=_ddfc .(_c .EndElement );
_agb &&_bcd .Name ==start .Name {return nil ;};return _d .Errorf ("\u0065\u0078\u0070\u0065c\u0074\u0065\u0064\u0020\u0065\u006e\u0064\u0020\u0065\u006ce\u006de\u006e\u0074\u002c\u0020\u0067\u006f\u0074 \u0025\u0076",_ddfc );};func NewCT_AnchorLock ()*CT_AnchorLock {_ddg :=&CT_AnchorLock {};
return _ddg };func (_gcb ST_VerticalAnchor )ValidateWithPath (path string )error {switch _gcb {case 0,1,2,3,4:default:return _d .Errorf ("\u0025s\u003a\u0020\u006f\u0075t\u0020\u006f\u0066\u0020\u0072a\u006eg\u0065 \u0076\u0061\u006c\u0075\u0065\u0020\u0025d",path ,int (_gcb ));
};return nil ;};func (_ac *CT_AnchorLock )UnmarshalXML (d *_c .Decoder ,start _c .StartElement )error {for {_ecd ,_cfef :=d .Token ();if _cfef !=nil {return _d .Errorf ("\u0070a\u0072\u0073\u0069\u006eg\u0020\u0043\u0054\u005f\u0041n\u0063h\u006fr\u004c\u006f\u0063\u006b\u003a\u0020\u0025s",_cfef );
};if _cge ,_dgbg :=_ecd .(_c .EndElement );_dgbg &&_cge .Name ==start .Name {break ;};};return nil ;};func (_bgf ST_BorderShadow )String ()string {switch _bgf {case 0:return "";case 1:return "\u0074";case 2:return "\u0074\u0072\u0075\u0065";case 3:return "\u0066";
case 4:return "\u0066\u0061\u006cs\u0065";};return "";};func (_egf *CT_Border )MarshalXML (e *_c .Encoder ,start _c .StartElement )error {if _egf .TypeAttr !=ST_BorderTypeUnset {_cbd ,_cd :=_egf .TypeAttr .MarshalXMLAttr (_c .Name {Local :"\u0074\u0079\u0070\u0065"});
if _cd !=nil {return _cd ;};start .Attr =append (start .Attr ,_cbd );};if _egf .WidthAttr !=nil {start .Attr =append (start .Attr ,_c .Attr {Name :_c .Name {Local :"\u0077\u0069\u0064t\u0068"},Value :_d .Sprintf ("\u0025\u0076",*_egf .WidthAttr )});};if _egf .ShadowAttr !=ST_BorderShadowUnset {_bb ,_fcc :=_egf .ShadowAttr .MarshalXMLAttr (_c .Name {Local :"\u0073\u0068\u0061\u0064\u006f\u0077"});
if _fcc !=nil {return _fcc ;};start .Attr =append (start .Attr ,_bb );};e .EncodeToken (start );e .EncodeToken (_c .EndElement {Name :start .Name });return nil ;};type Borderbottom struct{CT_Border };func (_ece *Borderleft )MarshalXML (e *_c .Encoder ,start _c .StartElement )error {start .Attr =append (start .Attr ,_c .Attr {Name :_c .Name {Local :"\u0078\u006d\u006cn\u0073"},Value :"\u0075\u0072n\u003a\u0073\u0063\u0068e\u006d\u0061s\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006ff\u0074\u002d\u0063\u006f\u006d\u003a\u006f\u0066\u0066\u0069\u0063\u0065:\u0077\u006f\u0072\u0064"});
start .Attr =append (start .Attr ,_c .Attr {Name :_c .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0078\u006dl"},Value :"\u0068\u0074tp\u003a\u002f\u002fw\u0077\u0077\u002e\u00773.o\u0072g/\u0058\u004d\u004c\u002f\u0031\u0039\u00398/\u006e\u0061\u006d\u0065\u0073\u0070\u0061c\u0065"});
start .Name .Local ="\u0062\u006f\u0072\u0064\u0065\u0072\u006c\u0065\u0066\u0074";return _ece .CT_Border .MarshalXML (e ,start );};func (_edc *ST_BorderShadow )UnmarshalXMLAttr (attr _c .Attr )error {switch attr .Value {case "":*_edc =0;case "\u0074":*_edc =1;
case "\u0074\u0072\u0075\u0065":*_edc =2;case "\u0066":*_edc =3;case "\u0066\u0061\u006cs\u0065":*_edc =4;};return nil ;};func (_aag *Wrap )MarshalXML (e *_c .Encoder ,start _c .StartElement )error {start .Attr =append (start .Attr ,_c .Attr {Name :_c .Name {Local :"\u0078\u006d\u006cn\u0073"},Value :"\u0075\u0072n\u003a\u0073\u0063\u0068e\u006d\u0061s\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006ff\u0074\u002d\u0063\u006f\u006d\u003a\u006f\u0066\u0066\u0069\u0063\u0065:\u0077\u006f\u0072\u0064"});
start .Attr =append (start .Attr ,_c .Attr {Name :_c .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0078\u006dl"},Value :"\u0068\u0074tp\u003a\u002f\u002fw\u0077\u0077\u002e\u00773.o\u0072g/\u0058\u004d\u004c\u002f\u0031\u0039\u00398/\u006e\u0061\u006d\u0065\u0073\u0070\u0061c\u0065"});
start .Name .Local ="\u0077\u0072\u0061\u0070";return _aag .CT_Wrap .MarshalXML (e ,start );};

// ValidateWithPath validates the Bordertop and its children, prefixing error messages with path
func (_cfe *Bordertop )ValidateWithPath (path string )error {if _dda :=_cfe .CT_Border .ValidateWithPath (path );_dda !=nil {return _dda ;};return nil ;};func (_edd ST_WrapType )MarshalXMLAttr (name _c .Name )(_c .Attr ,error ){_baa :=_c .Attr {};_baa .Name =name ;
switch _edd {case ST_WrapTypeUnset :_baa .Value ="";case ST_WrapTypeTopAndBottom :_baa .Value ="\u0074\u006f\u0070A\u006e\u0064\u0042\u006f\u0074\u0074\u006f\u006d";case ST_WrapTypeSquare :_baa .Value ="\u0073\u0071\u0075\u0061\u0072\u0065";case ST_WrapTypeNone :_baa .Value ="\u006e\u006f\u006e\u0065";
case ST_WrapTypeTight :_baa .Value ="\u0074\u0069\u0067h\u0074";case ST_WrapTypeThrough :_baa .Value ="\u0074h\u0072\u006f\u0075\u0067\u0068";};return _baa ,nil ;};func (_dfg ST_BorderShadow )MarshalXMLAttr (name _c .Name )(_c .Attr ,error ){_cdgc :=_c .Attr {};
_cdgc .Name =name ;switch _dfg {case ST_BorderShadowUnset :_cdgc .Value ="";case ST_BorderShadowT :_cdgc .Value ="\u0074";case ST_BorderShadowTrue :_cdgc .Value ="\u0074\u0072\u0075\u0065";case ST_BorderShadowF :_cdgc .Value ="\u0066";case ST_BorderShadowFalse :_cdgc .Value ="\u0066\u0061\u006cs\u0065";
};return _cdgc ,nil ;};func (_ccfg *ST_BorderType )UnmarshalXML (d *_c .Decoder ,start _c .StartElement )error {_gdb ,_cee :=d .Token ();if _cee !=nil {return _cee ;};if _eda ,_feec :=_gdb .(_c .EndElement );_feec &&_eda .Name ==start .Name {*_ccfg =1;
return nil ;};if _ecae ,_ccd :=_gdb .(_c .CharData );!_ccd {return _d .Errorf ("\u0065\u0078\u0070\u0065\u0063\u0074\u0065\u0064\u0020\u0063\u0068a\u0072\u0020\u0064\u0061\u0074\u0061\u002c\u0020\u0067\u006ft\u0020\u0025\u0054",_gdb );}else {switch string (_ecae ){case "":*_ccfg =0;
case "\u006e\u006f\u006e\u0065":*_ccfg =1;case "\u0073\u0069\u006e\u0067\u006c\u0065":*_ccfg =2;case "\u0074\u0068\u0069c\u006b":*_ccfg =3;case "\u0064\u006f\u0075\u0062\u006c\u0065":*_ccfg =4;case "\u0068\u0061\u0069\u0072\u006c\u0069\u006e\u0065":*_ccfg =5;
case "\u0064\u006f\u0074":*_ccfg =6;case "\u0064\u0061\u0073\u0068":*_ccfg =7;case "\u0064o\u0074\u0044\u0061\u0073\u0068":*_ccfg =8;case "\u0064\u0061\u0073\u0068\u0044\u006f\u0074\u0044\u006f\u0074":*_ccfg =9;case "\u0074\u0072\u0069\u0070\u006c\u0065":*_ccfg =10;
case "\u0074\u0068\u0069\u006e\u0054\u0068\u0069\u0063\u006bS\u006d\u0061\u006c\u006c":*_ccfg =11;case "\u0074\u0068\u0069\u0063\u006b\u0054\u0068\u0069\u006eS\u006d\u0061\u006c\u006c":*_ccfg =12;case "t\u0068\u0069\u0063\u006bBe\u0074w\u0065\u0065\u006e\u0054\u0068i\u006e\u0053\u006d\u0061\u006c\u006c":*_ccfg =13;
case "\u0074h\u0069\u006e\u0054\u0068\u0069\u0063k":*_ccfg =14;case "\u0074h\u0069\u0063\u006b\u0054\u0068\u0069n":*_ccfg =15;case "\u0074\u0068i\u0063\u006b\u0042e\u0074\u0077\u0065\u0065\u006e\u0054\u0068\u0069\u006e":*_ccfg =16;case "\u0074\u0068\u0069\u006e\u0054\u0068\u0069\u0063\u006bL\u0061\u0072\u0067\u0065":*_ccfg =17;
case "\u0074\u0068\u0069\u0063\u006b\u0054\u0068\u0069\u006eL\u0061\u0072\u0067\u0065":*_ccfg =18;case "t\u0068\u0069\u0063\u006bBe\u0074w\u0065\u0065\u006e\u0054\u0068i\u006e\u004c\u0061\u0072\u0067\u0065":*_ccfg =19;case "\u0077\u0061\u0076\u0065":*_ccfg =20;
case "\u0064\u006f\u0075\u0062\u006c\u0065\u0057\u0061\u0076\u0065":*_ccfg =21;case "d\u0061\u0073\u0068\u0065\u0064\u0053\u006d\u0061\u006c\u006c":*_ccfg =22;case "\u0064\u0061\u0073\u0068\u0044\u006f\u0074\u0053\u0074r\u006f\u006b\u0065\u0064":*_ccfg =23;
case "\u0074\u0068\u0072e\u0065\u0044\u0045\u006d\u0062\u006f\u0073\u0073":*_ccfg =24;case "\u0074\u0068\u0072\u0065\u0065\u0044\u0045\u006e\u0067\u0072\u0061\u0076\u0065":*_ccfg =25;case "\u0048\u0054\u004d\u004c\u004f\u0075\u0074\u0073\u0065\u0074":*_ccfg =26;
case "\u0048T\u004d\u004c\u0049\u006e\u0073\u0065t":*_ccfg =27;};};_gdb ,_cee =d .Token ();if _cee !=nil {return _cee ;};if _fdd ,_cdg :=_gdb .(_c .EndElement );_cdg &&_fdd .Name ==start .Name {return nil ;};return _d .Errorf ("\u0065\u0078\u0070\u0065c\u0074\u0065\u0064\u0020\u0065\u006e\u0064\u0020\u0065\u006ce\u006de\u006e\u0074\u002c\u0020\u0067\u006f\u0074 \u0025\u0076",_gdb );
};func (_fdb *ST_VerticalAnchor )UnmarshalXMLAttr (attr _c .Attr )error {switch attr .Value {case "":*_fdb =0;case "\u006d\u0061\u0072\u0067\u0069\u006e":*_fdb =1;case "\u0070\u0061\u0067\u0065":*_fdb =2;case "\u0074\u0065\u0078\u0074":*_fdb =3;case "\u006c\u0069\u006e\u0065":*_fdb =4;
};return nil ;};func (_gc *CT_AnchorLock )MarshalXML (e *_c .Encoder ,start _c .StartElement )error {e .EncodeToken (start );e .EncodeToken (_c .EndElement {Name :start .Name });return nil ;};type Anchorlock struct{CT_AnchorLock };type CT_Border struct{

// Border Style
TypeAttr ST_BorderType ;

// Border Width
WidthAttr *uint32 ;

// Border shadow
ShadowAttr ST_BorderShadow ;};func (_gec ST_BorderShadow )MarshalXML (e *_c .Encoder ,start _c .StartElement )error {return e .EncodeElement (_gec .String (),start );};

// ValidateWithPath validates the Borderright and its children, prefixing error messages with path
func (_be *Borderright )ValidateWithPath (path string )error {if _de :=_be .CT_Border .ValidateWithPath (path );_de !=nil {return _de ;};return nil ;};func (_gdf *Wrap )UnmarshalXML (d *_c .Decoder ,start _c .StartElement )error {_gdf .CT_Wrap =*NewCT_Wrap ();
for _ ,_dgf :=range start .Attr {if _dgf .Name .Local =="\u0074\u0079\u0070\u0065"{_gdf .TypeAttr .UnmarshalXMLAttr (_dgf );continue ;};if _dgf .Name .Local =="\u0073\u0069\u0064\u0065"{_gdf .SideAttr .UnmarshalXMLAttr (_dgf );continue ;};if _dgf .Name .Local =="\u0061n\u0063\u0068\u006f\u0072\u0078"{_gdf .AnchorxAttr .UnmarshalXMLAttr (_dgf );
continue ;};if _dgf .Name .Local =="\u0061n\u0063\u0068\u006f\u0072\u0079"{_gdf .AnchoryAttr .UnmarshalXMLAttr (_dgf );continue ;};};for {_cfg ,_geb :=d .Token ();if _geb !=nil {return _d .Errorf ("\u0070\u0061r\u0073\u0069\u006eg\u0020\u0057\u0072\u0061\u0070\u003a\u0020\u0025\u0073",_geb );
};if _eaa ,_dec :=_cfg .(_c .EndElement );_dec &&_eaa .Name ==start .Name {break ;};};return nil ;};func (_ded *Bordertop )MarshalXML (e *_c .Encoder ,start _c .StartElement )error {start .Attr =append (start .Attr ,_c .Attr {Name :_c .Name {Local :"\u0078\u006d\u006cn\u0073"},Value :"\u0075\u0072n\u003a\u0073\u0063\u0068e\u006d\u0061s\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006ff\u0074\u002d\u0063\u006f\u006d\u003a\u006f\u0066\u0066\u0069\u0063\u0065:\u0077\u006f\u0072\u0064"});
start .Attr =append (start .Attr ,_c .Attr {Name :_c .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0078\u006dl"},Value :"\u0068\u0074tp\u003a\u002f\u002fw\u0077\u0077\u002e\u00773.o\u0072g/\u0058\u004d\u004c\u002f\u0031\u0039\u00398/\u006e\u0061\u006d\u0065\u0073\u0070\u0061c\u0065"});
start .Name .Local ="\u0062o\u0072\u0064\u0065\u0072\u0074\u006fp";return _ded .CT_Border .MarshalXML (e ,start );};func (_fge *ST_WrapType )UnmarshalXML (d *_c .Decoder ,start _c .StartElement )error {_efd ,_geg :=d .Token ();if _geg !=nil {return _geg ;
};if _dbdf ,_fbde :=_efd .(_c .EndElement );_fbde &&_dbdf .Name ==start .Name {*_fge =1;return nil ;};if _fbb ,_bda :=_efd .(_c .CharData );!_bda {return _d .Errorf ("\u0065\u0078\u0070\u0065\u0063\u0074\u0065\u0064\u0020\u0063\u0068a\u0072\u0020\u0064\u0061\u0074\u0061\u002c\u0020\u0067\u006ft\u0020\u0025\u0054",_efd );
}else {switch string (_fbb ){case "":*_fge =0;case "\u0074\u006f\u0070A\u006e\u0064\u0042\u006f\u0074\u0074\u006f\u006d":*_fge =1;case "\u0073\u0071\u0075\u0061\u0072\u0065":*_fge =2;case "\u006e\u006f\u006e\u0065":*_fge =3;case "\u0074\u0069\u0067h\u0074":*_fge =4;
case "\u0074h\u0072\u006f\u0075\u0067\u0068":*_fge =5;};};_efd ,_geg =d .Token ();if _geg !=nil {return _geg ;};if _dfb ,_aagb :=_efd .(_c .EndElement );_aagb &&_dfb .Name ==start .Name {return nil ;};return _d .Errorf ("\u0065\u0078\u0070\u0065c\u0074\u0065\u0064\u0020\u0065\u006e\u0064\u0020\u0065\u006ce\u006de\u006e\u0074\u002c\u0020\u0067\u006f\u0074 \u0025\u0076",_efd );
};type ST_HorizontalAnchor byte ;func NewCT_Border ()*CT_Border {_fcb :=&CT_Border {};return _fcb };func (_bcce *ST_WrapSide )UnmarshalXMLAttr (attr _c .Attr )error {switch attr .Value {case "":*_bcce =0;case "\u0062\u006f\u0074\u0068":*_bcce =1;case "\u006c\u0065\u0066\u0074":*_bcce =2;
case "\u0072\u0069\u0067h\u0074":*_bcce =3;case "\u006ca\u0072\u0067\u0065\u0073\u0074":*_bcce =4;};return nil ;};

// ValidateWithPath validates the CT_Wrap and its children, prefixing error messages with path
func (_bcc *CT_Wrap )ValidateWithPath (path string )error {if _afa :=_bcc .TypeAttr .ValidateWithPath (path +"\u002fT\u0079\u0070\u0065\u0041\u0074\u0074r");_afa !=nil {return _afa ;};if _dcg :=_bcc .SideAttr .ValidateWithPath (path +"\u002fS\u0069\u0064\u0065\u0041\u0074\u0074r");
_dcg !=nil {return _dcg ;};if _ccc :=_bcc .AnchorxAttr .ValidateWithPath (path +"\u002f\u0041\u006ec\u0068\u006f\u0072\u0078\u0041\u0074\u0074\u0072");_ccc !=nil {return _ccc ;};if _fcaa :=_bcc .AnchoryAttr .ValidateWithPath (path +"\u002f\u0041\u006ec\u0068\u006f\u0072\u0079\u0041\u0074\u0074\u0072");
_fcaa !=nil {return _fcaa ;};return nil ;};func (_fc *Borderleft )UnmarshalXML (d *_c .Decoder ,start _c .StartElement )error {_fc .CT_Border =*NewCT_Border ();for _ ,_bfg :=range start .Attr {if _bfg .Name .Local =="\u0074\u0079\u0070\u0065"{_fc .TypeAttr .UnmarshalXMLAttr (_bfg );
continue ;};if _bfg .Name .Local =="\u0077\u0069\u0064t\u0068"{_gf ,_ef :=_a .ParseUint (_bfg .Value ,10,32);if _ef !=nil {return _ef ;};_ca :=uint32 (_gf );_fc .WidthAttr =&_ca ;continue ;};if _bfg .Name .Local =="\u0073\u0068\u0061\u0064\u006f\u0077"{_fc .ShadowAttr .UnmarshalXMLAttr (_bfg );
continue ;};};for {_dd ,_ge :=d .Token ();if _ge !=nil {return _d .Errorf ("\u0070\u0061\u0072\u0073in\u0067\u0020\u0042\u006f\u0072\u0064\u0065\u0072\u006c\u0065\u0066\u0074\u003a\u0020%\u0073",_ge );};if _ega ,_ddc :=_dd .(_c .EndElement );_ddc &&_ega .Name ==start .Name {break ;
};};return nil ;};func (_gcg ST_WrapSide )MarshalXMLAttr (name _c .Name )(_c .Attr ,error ){_gfeb :=_c .Attr {};_gfeb .Name =name ;switch _gcg {case ST_WrapSideUnset :_gfeb .Value ="";case ST_WrapSideBoth :_gfeb .Value ="\u0062\u006f\u0074\u0068";case ST_WrapSideLeft :_gfeb .Value ="\u006c\u0065\u0066\u0074";
case ST_WrapSideRight :_gfeb .Value ="\u0072\u0069\u0067h\u0074";case ST_WrapSideLargest :_gfeb .Value ="\u006ca\u0072\u0067\u0065\u0073\u0074";};return _gfeb ,nil ;};func (_da *Borderbottom )UnmarshalXML (d *_c .Decoder ,start _c .StartElement )error {_da .CT_Border =*NewCT_Border ();
for _ ,_afc :=range start .Attr {if _afc .Name .Local =="\u0074\u0079\u0070\u0065"{_da .TypeAttr .UnmarshalXMLAttr (_afc );continue ;};if _afc .Name .Local =="\u0077\u0069\u0064t\u0068"{_eg ,_gd :=_a .ParseUint (_afc .Value ,10,32);if _gd !=nil {return _gd ;
};_ffb :=uint32 (_eg );_da .WidthAttr =&_ffb ;continue ;};if _afc .Name .Local =="\u0073\u0068\u0061\u0064\u006f\u0077"{_da .ShadowAttr .UnmarshalXMLAttr (_afc );continue ;};};for {_fb ,_bf :=d .Token ();if _bf !=nil {return _d .Errorf ("\u0070a\u0072\u0073\u0069\u006e\u0067\u0020\u0042\u006f\u0072\u0064\u0065r\u0062\u006f\u0074\u0074\u006f\u006d\u003a\u0020\u0025\u0073",_bf );
};if _aa ,_ba :=_fb .(_c .EndElement );_ba &&_aa .Name ==start .Name {break ;};};return nil ;};func (_fceg ST_HorizontalAnchor )MarshalXML (e *_c .Encoder ,start _c .StartElement )error {return e .EncodeElement (_fceg .String (),start );};func NewWrap ()*Wrap {_ffbd :=&Wrap {};
_ffbd .CT_Wrap =*NewCT_Wrap ();return _ffbd };

// Validate validates the Borderleft and its children
func (_cgg *Borderleft )Validate ()error {return _cgg .ValidateWithPath ("\u0042\u006f\u0072\u0064\u0065\u0072\u006c\u0065\u0066\u0074");};type ST_WrapType byte ;func (_edcd ST_WrapType )MarshalXML (e *_c .Encoder ,start _c .StartElement )error {return e .EncodeElement (_edcd .String (),start );
};func (_gbg ST_WrapType )Validate ()error {return _gbg .ValidateWithPath ("")};func (_fga ST_WrapSide )String ()string {switch _fga {case 0:return "";case 1:return "\u0062\u006f\u0074\u0068";case 2:return "\u006c\u0065\u0066\u0074";case 3:return "\u0072\u0069\u0067h\u0074";
case 4:return "\u006ca\u0072\u0067\u0065\u0073\u0074";};return "";};func (_gfd *CT_Border )UnmarshalXML (d *_c .Decoder ,start _c .StartElement )error {for _ ,_acc :=range start .Attr {if _acc .Name .Local =="\u0074\u0079\u0070\u0065"{_gfd .TypeAttr .UnmarshalXMLAttr (_acc );
continue ;};if _acc .Name .Local =="\u0077\u0069\u0064t\u0068"{_fce ,_cggc :=_a .ParseUint (_acc .Value ,10,32);if _cggc !=nil {return _cggc ;};_gfde :=uint32 (_fce );_gfd .WidthAttr =&_gfde ;continue ;};if _acc .Name .Local =="\u0073\u0068\u0061\u0064\u006f\u0077"{_gfd .ShadowAttr .UnmarshalXMLAttr (_acc );
continue ;};};for {_ee ,_cbf :=d .Token ();if _cbf !=nil {return _d .Errorf ("p\u0061\u0072\u0073\u0069ng\u0020C\u0054\u005f\u0042\u006f\u0072d\u0065\u0072\u003a\u0020\u0025\u0073",_cbf );};if _bfbf ,_fee :=_ee .(_c .EndElement );_fee &&_bfbf .Name ==start .Name {break ;
};};return nil ;};type CT_Wrap struct{

// Wrapping type
TypeAttr ST_WrapType ;

// Wrapping side
SideAttr ST_WrapSide ;

// Horizontal Positioning Base
AnchorxAttr ST_HorizontalAnchor ;

// Vertical Positioning Base
AnchoryAttr ST_VerticalAnchor ;};

// Validate validates the Wrap and its children
func (_eea *Wrap )Validate ()error {return _eea .ValidateWithPath ("\u0057\u0072\u0061\u0070")};func (_abf ST_WrapSide )Validate ()error {return _abf .ValidateWithPath ("")};const (ST_WrapTypeUnset ST_WrapType =0;ST_WrapTypeTopAndBottom ST_WrapType =1;
ST_WrapTypeSquare ST_WrapType =2;ST_WrapTypeNone ST_WrapType =3;ST_WrapTypeTight ST_WrapType =4;ST_WrapTypeThrough ST_WrapType =5;);func (_def ST_VerticalAnchor )String ()string {switch _def {case 0:return "";case 1:return "\u006d\u0061\u0072\u0067\u0069\u006e";
case 2:return "\u0070\u0061\u0067\u0065";case 3:return "\u0074\u0065\u0078\u0074";case 4:return "\u006c\u0069\u006e\u0065";};return "";};func (_ffg *Borderright )UnmarshalXML (d *_c .Decoder ,start _c .StartElement )error {_ffg .CT_Border =*NewCT_Border ();
for _ ,_ag :=range start .Attr {if _ag .Name .Local =="\u0074\u0079\u0070\u0065"{_ffg .TypeAttr .UnmarshalXMLAttr (_ag );continue ;};if _ag .Name .Local =="\u0077\u0069\u0064t\u0068"{_dg ,_ce :=_a .ParseUint (_ag .Value ,10,32);if _ce !=nil {return _ce ;
};_bg :=uint32 (_dg );_ffg .WidthAttr =&_bg ;continue ;};if _ag .Name .Local =="\u0073\u0068\u0061\u0064\u006f\u0077"{_ffg .ShadowAttr .UnmarshalXMLAttr (_ag );continue ;};};for {_aac ,_bfb :=d .Token ();if _bfb !=nil {return _d .Errorf ("\u0070\u0061\u0072si\u006e\u0067\u0020\u0042\u006f\u0072\u0064\u0065\u0072\u0072\u0069\u0067\u0068\u0074\u003a\u0020\u0025\u0073",_bfb );
};if _efa ,_ddf :=_aac .(_c .EndElement );_ddf &&_efa .Name ==start .Name {break ;};};return nil ;};const (ST_VerticalAnchorUnset ST_VerticalAnchor =0;ST_VerticalAnchorMargin ST_VerticalAnchor =1;ST_VerticalAnchorPage ST_VerticalAnchor =2;ST_VerticalAnchorText ST_VerticalAnchor =3;
ST_VerticalAnchorLine ST_VerticalAnchor =4;);type Bordertop struct{CT_Border };func (_dbdb ST_HorizontalAnchor )Validate ()error {return _dbdb .ValidateWithPath ("")};func (_dfbd ST_WrapSide )MarshalXML (e *_c .Encoder ,start _c .StartElement )error {return e .EncodeElement (_dfbd .String (),start );
};type Wrap struct{CT_Wrap };

// Validate validates the Anchorlock and its children
func (_e *Anchorlock )Validate ()error {return _e .ValidateWithPath ("\u0041\u006e\u0063\u0068\u006f\u0072\u006c\u006f\u0063\u006b");};const (ST_BorderShadowUnset ST_BorderShadow =0;ST_BorderShadowT ST_BorderShadow =1;ST_BorderShadowTrue ST_BorderShadow =2;
ST_BorderShadowF ST_BorderShadow =3;ST_BorderShadowFalse ST_BorderShadow =4;);type ST_VerticalAnchor byte ;

// ValidateWithPath validates the CT_Border and its children, prefixing error messages with path
func (_fbcc *CT_Border )ValidateWithPath (path string )error {if _ebd :=_fbcc .TypeAttr .ValidateWithPath (path +"\u002fT\u0079\u0070\u0065\u0041\u0074\u0074r");_ebd !=nil {return _ebd ;};if _bgd :=_fbcc .ShadowAttr .ValidateWithPath (path +"/\u0053\u0068\u0061\u0064\u006f\u0077\u0041\u0074\u0074\u0072");
_bgd !=nil {return _bgd ;};return nil ;};func (_gbb ST_VerticalAnchor )Validate ()error {return _gbb .ValidateWithPath ("")};func (_g *Anchorlock )MarshalXML (e *_c .Encoder ,start _c .StartElement )error {start .Attr =append (start .Attr ,_c .Attr {Name :_c .Name {Local :"\u0078\u006d\u006cn\u0073"},Value :"\u0075\u0072n\u003a\u0073\u0063\u0068e\u006d\u0061s\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006ff\u0074\u002d\u0063\u006f\u006d\u003a\u006f\u0066\u0066\u0069\u0063\u0065:\u0077\u006f\u0072\u0064"});
start .Attr =append (start .Attr ,_c .Attr {Name :_c .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0078\u006dl"},Value :"\u0068\u0074tp\u003a\u002f\u002fw\u0077\u0077\u002e\u00773.o\u0072g/\u0058\u004d\u004c\u002f\u0031\u0039\u00398/\u006e\u0061\u006d\u0065\u0073\u0070\u0061c\u0065"});
start .Name .Local ="\u0061\u006e\u0063\u0068\u006f\u0072\u006c\u006f\u0063\u006b";return _g .CT_AnchorLock .MarshalXML (e ,start );};func (_cfd ST_WrapType )String ()string {switch _cfd {case 0:return "";case 1:return "\u0074\u006f\u0070A\u006e\u0064\u0042\u006f\u0074\u0074\u006f\u006d";
case 2:return "\u0073\u0071\u0075\u0061\u0072\u0065";case 3:return "\u006e\u006f\u006e\u0065";case 4:return "\u0074\u0069\u0067h\u0074";case 5:return "\u0074h\u0072\u006f\u0075\u0067\u0068";};return "";};

// ValidateWithPath validates the Borderleft and its children, prefixing error messages with path
func (_cc *Borderleft )ValidateWithPath (path string )error {if _db :=_cc .CT_Border .ValidateWithPath (path );_db !=nil {return _db ;};return nil ;};func (_afg *ST_WrapType )UnmarshalXMLAttr (attr _c .Attr )error {switch attr .Value {case "":*_afg =0;
case "\u0074\u006f\u0070A\u006e\u0064\u0042\u006f\u0074\u0074\u006f\u006d":*_afg =1;case "\u0073\u0071\u0075\u0061\u0072\u0065":*_afg =2;case "\u006e\u006f\u006e\u0065":*_afg =3;case "\u0074\u0069\u0067h\u0074":*_afg =4;case "\u0074h\u0072\u006f\u0075\u0067\u0068":*_afg =5;
};return nil ;};func (_gaac ST_BorderShadow )ValidateWithPath (path string )error {switch _gaac {case 0,1,2,3,4:default:return _d .Errorf ("\u0025s\u003a\u0020\u006f\u0075t\u0020\u006f\u0066\u0020\u0072a\u006eg\u0065 \u0076\u0061\u006c\u0075\u0065\u0020\u0025d",path ,int (_gaac ));
};return nil ;};func (_egfe ST_VerticalAnchor )MarshalXML (e *_c .Encoder ,start _c .StartElement )error {return e .EncodeElement (_egfe .String (),start );};func NewBorderbottom ()*Borderbottom {_ga :=&Borderbottom {};_ga .CT_Border =*NewCT_Border ();
return _ga ;};func (_gba ST_HorizontalAnchor )ValidateWithPath (path string )error {switch _gba {case 0,1,2,3,4:default:return _d .Errorf ("\u0025s\u003a\u0020\u006f\u0075t\u0020\u006f\u0066\u0020\u0072a\u006eg\u0065 \u0076\u0061\u006c\u0075\u0065\u0020\u0025d",path ,int (_gba ));
};return nil ;};func NewBorderright ()*Borderright {_fbf :=&Borderright {};_fbf .CT_Border =*NewCT_Border ();return _fbf ;};const (ST_WrapSideUnset ST_WrapSide =0;ST_WrapSideBoth ST_WrapSide =1;ST_WrapSideLeft ST_WrapSide =2;ST_WrapSideRight ST_WrapSide =3;
ST_WrapSideLargest ST_WrapSide =4;);func (_gda *ST_HorizontalAnchor )UnmarshalXMLAttr (attr _c .Attr )error {switch attr .Value {case "":*_gda =0;case "\u006d\u0061\u0072\u0067\u0069\u006e":*_gda =1;case "\u0070\u0061\u0067\u0065":*_gda =2;case "\u0074\u0065\u0078\u0074":*_gda =3;
case "\u0063\u0068\u0061\u0072":*_gda =4;};return nil ;};

// ValidateWithPath validates the Borderbottom and its children, prefixing error messages with path
func (_dab *Borderbottom )ValidateWithPath (path string )error {if _ec :=_dab .CT_Border .ValidateWithPath (path );_ec !=nil {return _ec ;};return nil ;};func init (){_af .RegisterConstructor ("\u0075\u0072n\u003a\u0073\u0063\u0068e\u006d\u0061s\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006ff\u0074\u002d\u0063\u006f\u006d\u003a\u006f\u0066\u0066\u0069\u0063\u0065:\u0077\u006f\u0072\u0064","\u0043T\u005f\u0042\u006f\u0072\u0064\u0065r",NewCT_Border );
_af .RegisterConstructor ("\u0075\u0072n\u003a\u0073\u0063\u0068e\u006d\u0061s\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006ff\u0074\u002d\u0063\u006f\u006d\u003a\u006f\u0066\u0066\u0069\u0063\u0065:\u0077\u006f\u0072\u0064","\u0043T\u005f\u0057\u0072\u0061\u0070",NewCT_Wrap );
_af .RegisterConstructor ("\u0075\u0072n\u003a\u0073\u0063\u0068e\u006d\u0061s\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006ff\u0074\u002d\u0063\u006f\u006d\u003a\u006f\u0066\u0066\u0069\u0063\u0065:\u0077\u006f\u0072\u0064","\u0043\u0054\u005f\u0041\u006e\u0063\u0068\u006f\u0072\u004c\u006f\u0063\u006b",NewCT_AnchorLock );
_af .RegisterConstructor ("\u0075\u0072n\u003a\u0073\u0063\u0068e\u006d\u0061s\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006ff\u0074\u002d\u0063\u006f\u006d\u003a\u006f\u0066\u0066\u0069\u0063\u0065:\u0077\u006f\u0072\u0064","\u0062o\u0072\u0064\u0065\u0072\u0074\u006fp",NewBordertop );
_af .RegisterConstructor ("\u0075\u0072n\u003a\u0073\u0063\u0068e\u006d\u0061s\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006ff\u0074\u002d\u0063\u006f\u006d\u003a\u006f\u0066\u0066\u0069\u0063\u0065:\u0077\u006f\u0072\u0064","\u0062\u006f\u0072\u0064\u0065\u0072\u006c\u0065\u0066\u0074",NewBorderleft );
_af .RegisterConstructor ("\u0075\u0072n\u003a\u0073\u0063\u0068e\u006d\u0061s\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006ff\u0074\u002d\u0063\u006f\u006d\u003a\u006f\u0066\u0066\u0069\u0063\u0065:\u0077\u006f\u0072\u0064","b\u006f\u0072\u0064\u0065\u0072\u0072\u0069\u0067\u0068\u0074",NewBorderright );
_af .RegisterConstructor ("\u0075\u0072n\u003a\u0073\u0063\u0068e\u006d\u0061s\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006ff\u0074\u002d\u0063\u006f\u006d\u003a\u006f\u0066\u0066\u0069\u0063\u0065:\u0077\u006f\u0072\u0064","\u0062\u006f\u0072d\u0065\u0072\u0062\u006f\u0074\u0074\u006f\u006d",NewBorderbottom );
_af .RegisterConstructor ("\u0075\u0072n\u003a\u0073\u0063\u0068e\u006d\u0061s\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006ff\u0074\u002d\u0063\u006f\u006d\u003a\u006f\u0066\u0066\u0069\u0063\u0065:\u0077\u006f\u0072\u0064","\u0077\u0072\u0061\u0070",NewWrap );
_af .RegisterConstructor ("\u0075\u0072n\u003a\u0073\u0063\u0068e\u006d\u0061s\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006ff\u0074\u002d\u0063\u006f\u006d\u003a\u006f\u0066\u0066\u0069\u0063\u0065:\u0077\u006f\u0072\u0064","\u0061\u006e\u0063\u0068\u006f\u0072\u006c\u006f\u0063\u006b",NewAnchorlock );
};