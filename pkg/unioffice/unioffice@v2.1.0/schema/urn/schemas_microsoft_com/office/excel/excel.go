//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package excel ;import (_g "encoding/xml";_f "fmt";_b "github.com/unidoc/unioffice/v2";_c "github.com/unidoc/unioffice/v2/common/logger";_fe "github.com/unidoc/unioffice/v2/schema/soo/ofc/sharedTypes";);func NewCT_ClientDataChoice ()*CT_ClientDataChoice {_bdg :=&CT_ClientDataChoice {};
return _bdg };func NewCT_ClientData ()*CT_ClientData {_ae :=&CT_ClientData {};_ae .ObjectTypeAttr =ST_ObjectType (1);return _ae ;};func (_ga *CT_ClientData )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {_ga .ObjectTypeAttr =ST_ObjectType (1);
for _ ,_bd :=range start .Attr {if _bd .Name .Local =="\u004f\u0062\u006a\u0065\u0063\u0074\u0054\u0079\u0070\u0065"{_ga .ObjectTypeAttr .UnmarshalXMLAttr (_bd );continue ;};};_df :for {_cd ,_ce :=d .Token ();if _ce !=nil {return _ce ;};switch _fd :=_cd .(type ){case _g .StartElement :switch _fd .Name {case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u004d\u006f\u0076\u0065\u0057\u0069\u0074\u0068\u0043\u0065\u006c\u006c\u0073"}:_cc :=NewCT_ClientDataChoice ();
if _cee :=d .DecodeElement (&_cc .MoveWithCells ,&_fd );_cee !=nil {return _cee ;};_ga .ClientDataChoice =append (_ga .ClientDataChoice ,_cc );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0053\u0069\u007a\u0065\u0057\u0069\u0074\u0068\u0043\u0065\u006c\u006c\u0073"}:_ag :=NewCT_ClientDataChoice ();
if _dfg :=d .DecodeElement (&_ag .SizeWithCells ,&_fd );_dfg !=nil {return _dfg ;};_ga .ClientDataChoice =append (_ga .ClientDataChoice ,_ag );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0041\u006e\u0063\u0068\u006f\u0072"}:_bag :=NewCT_ClientDataChoice ();
if _fc :=d .DecodeElement (&_bag .Anchor ,&_fd );_fc !=nil {return _fc ;};_ga .ClientDataChoice =append (_ga .ClientDataChoice ,_bag );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u004c\u006f\u0063\u006b\u0065\u0064"}:_baa :=NewCT_ClientDataChoice ();
if _bc :=d .DecodeElement (&_baa .Locked ,&_fd );_bc !=nil {return _bc ;};_ga .ClientDataChoice =append (_ga .ClientDataChoice ,_baa );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"D\u0065\u0066\u0061\u0075\u006c\u0074\u0053\u0069\u007a\u0065"}:_cdf :=NewCT_ClientDataChoice ();
if _cdg :=d .DecodeElement (&_cdf .DefaultSize ,&_fd );_cdg !=nil {return _cdg ;};_ga .ClientDataChoice =append (_ga .ClientDataChoice ,_cdf );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"P\u0072\u0069\u006e\u0074\u004f\u0062\u006a\u0065\u0063\u0074"}:_gc :=NewCT_ClientDataChoice ();
if _ee :=d .DecodeElement (&_gc .PrintObject ,&_fd );_ee !=nil {return _ee ;};_ga .ClientDataChoice =append (_ga .ClientDataChoice ,_gc );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0044\u0069\u0073\u0061\u0062\u006c\u0065\u0064"}:_fdc :=NewCT_ClientDataChoice ();
if _ef :=d .DecodeElement (&_fdc .Disabled ,&_fd );_ef !=nil {return _ef ;};_ga .ClientDataChoice =append (_ga .ClientDataChoice ,_fdc );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0041\u0075\u0074\u006f\u0046\u0069\u006c\u006c"}:_ca :=NewCT_ClientDataChoice ();
if _ceea :=d .DecodeElement (&_ca .AutoFill ,&_fd );_ceea !=nil {return _ceea ;};_ga .ClientDataChoice =append (_ga .ClientDataChoice ,_ca );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0041\u0075\u0074\u006f\u004c\u0069\u006e\u0065"}:_bdb :=NewCT_ClientDataChoice ();
if _cda :=d .DecodeElement (&_bdb .AutoLine ,&_fd );_cda !=nil {return _cda ;};_ga .ClientDataChoice =append (_ga .ClientDataChoice ,_bdb );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0041\u0075\u0074\u006f\u0050\u0069\u0063\u0074"}:_dg :=NewCT_ClientDataChoice ();
if _aaf :=d .DecodeElement (&_dg .AutoPict ,&_fd );_aaf !=nil {return _aaf ;};_ga .ClientDataChoice =append (_ga .ClientDataChoice ,_dg );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0046m\u006c\u0061\u004d\u0061\u0063\u0072o"}:_bdf :=NewCT_ClientDataChoice ();
if _bda :=d .DecodeElement (&_bdf .FmlaMacro ,&_fd );_bda !=nil {return _bda ;};_ga .ClientDataChoice =append (_ga .ClientDataChoice ,_bdf );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0054\u0065\u0078\u0074\u0048\u0041\u006c\u0069\u0067\u006e"}:_bcg :=NewCT_ClientDataChoice ();
if _cg :=d .DecodeElement (&_bcg .TextHAlign ,&_fd );_cg !=nil {return _cg ;};_ga .ClientDataChoice =append (_ga .ClientDataChoice ,_bcg );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0054\u0065\u0078\u0074\u0056\u0041\u006c\u0069\u0067\u006e"}:_eb :=NewCT_ClientDataChoice ();
if _dd :=d .DecodeElement (&_eb .TextVAlign ,&_fd );_dd !=nil {return _dd ;};_ga .ClientDataChoice =append (_ga .ClientDataChoice ,_eb );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u004c\u006f\u0063\u006b\u0054\u0065\u0078\u0074"}:_dc :=NewCT_ClientDataChoice ();
if _gb :=d .DecodeElement (&_dc .LockText ,&_fd );_gb !=nil {return _gb ;};_ga .ClientDataChoice =append (_ga .ClientDataChoice ,_dc );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u004au\u0073\u0074\u004c\u0061\u0073\u0074X"}:_bad :=NewCT_ClientDataChoice ();
if _da :=d .DecodeElement (&_bad .JustLastX ,&_fd );_da !=nil {return _da ;};_ga .ClientDataChoice =append (_ga .ClientDataChoice ,_bad );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0053\u0065\u0063\u0072\u0065\u0074\u0045\u0064\u0069\u0074"}:_ccg :=NewCT_ClientDataChoice ();
if _cdgb :=d .DecodeElement (&_ccg .SecretEdit ,&_fd );_cdgb !=nil {return _cdgb ;};_ga .ClientDataChoice =append (_ga .ClientDataChoice ,_ccg );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0044e\u0066\u0061\u0075\u006c\u0074"}:_fde :=NewCT_ClientDataChoice ();
if _cgb :=d .DecodeElement (&_fde .Default ,&_fd );_cgb !=nil {return _cgb ;};_ga .ClientDataChoice =append (_ga .ClientDataChoice ,_fde );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0048\u0065\u006c\u0070"}:_ab :=NewCT_ClientDataChoice ();
if _abe :=d .DecodeElement (&_ab .Help ,&_fd );_abe !=nil {return _abe ;};_ga .ClientDataChoice =append (_ga .ClientDataChoice ,_ab );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0043\u0061\u006e\u0063\u0065\u006c"}:_ffc :=NewCT_ClientDataChoice ();
if _cca :=d .DecodeElement (&_ffc .Cancel ,&_fd );_cca !=nil {return _cca ;};_ga .ClientDataChoice =append (_ga .ClientDataChoice ,_ffc );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0044i\u0073\u006d\u0069\u0073\u0073"}:_fda :=NewCT_ClientDataChoice ();
if _ebe :=d .DecodeElement (&_fda .Dismiss ,&_fd );_ebe !=nil {return _ebe ;};_ga .ClientDataChoice =append (_ga .ClientDataChoice ,_fda );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0041\u0063\u0063e\u006c"}:_dfgd :=NewCT_ClientDataChoice ();
if _eg :=d .DecodeElement (&_dfgd .Accel ,&_fd );_eg !=nil {return _eg ;};_ga .ClientDataChoice =append (_ga .ClientDataChoice ,_dfgd );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0041\u0063\u0063\u0065\u006c\u0032"}:_be :=NewCT_ClientDataChoice ();
if _cf :=d .DecodeElement (&_be .Accel2 ,&_fd );_cf !=nil {return _cf ;};_ga .ClientDataChoice =append (_ga .ClientDataChoice ,_be );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0052\u006f\u0077"}:_fg :=NewCT_ClientDataChoice ();
if _aag :=d .DecodeElement (&_fg .Row ,&_fd );_aag !=nil {return _aag ;};_ga .ClientDataChoice =append (_ga .ClientDataChoice ,_fg );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0043\u006f\u006c\u0075\u006d\u006e"}:_fdeg :=NewCT_ClientDataChoice ();
if _de :=d .DecodeElement (&_fdeg .Column ,&_fd );_de !=nil {return _de ;};_ga .ClientDataChoice =append (_ga .ClientDataChoice ,_fdeg );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0056i\u0073\u0069\u0062\u006c\u0065"}:_bcc :=NewCT_ClientDataChoice ();
if _bef :=d .DecodeElement (&_bcc .Visible ,&_fd );_bef !=nil {return _bef ;};_ga .ClientDataChoice =append (_ga .ClientDataChoice ,_bcc );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0052o\u0077\u0048\u0069\u0064\u0064\u0065n"}:_daa :=NewCT_ClientDataChoice ();
if _gg :=d .DecodeElement (&_daa .RowHidden ,&_fd );_gg !=nil {return _gg ;};_ga .ClientDataChoice =append (_ga .ClientDataChoice ,_daa );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0043o\u006c\u0048\u0069\u0064\u0064\u0065n"}:_ggg :=NewCT_ClientDataChoice ();
if _bdag :=d .DecodeElement (&_ggg .ColHidden ,&_fd );_bdag !=nil {return _bdag ;};_ga .ClientDataChoice =append (_ga .ClientDataChoice ,_ggg );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0056\u0054\u0045\u0064\u0069\u0074"}:_efa :=NewCT_ClientDataChoice ();
if _bab :=d .DecodeElement (&_efa .VTEdit ,&_fd );_bab !=nil {return _bab ;};_ga .ClientDataChoice =append (_ga .ClientDataChoice ,_efa );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u004du\u006c\u0074\u0069\u004c\u0069\u006ee"}:_db :=NewCT_ClientDataChoice ();
if _gbb :=d .DecodeElement (&_db .MultiLine ,&_fd );_gbb !=nil {return _gbb ;};_ga .ClientDataChoice =append (_ga .ClientDataChoice ,_db );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0056S\u0063\u0072\u006f\u006c\u006c"}:_ced :=NewCT_ClientDataChoice ();
if _eeg :=d .DecodeElement (&_ced .VScroll ,&_fd );_eeg !=nil {return _eeg ;};_ga .ClientDataChoice =append (_ga .ClientDataChoice ,_ced );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0056\u0061\u006c\u0069\u0064\u0049\u0064\u0073"}:_ea :=NewCT_ClientDataChoice ();
if _gcf :=d .DecodeElement (&_ea .ValidIds ,&_fd );_gcf !=nil {return _gcf ;};_ga .ClientDataChoice =append (_ga .ClientDataChoice ,_ea );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0046m\u006c\u0061\u0052\u0061\u006e\u0067e"}:_ge :=NewCT_ClientDataChoice ();
if _fgg :=d .DecodeElement (&_ge .FmlaRange ,&_fd );_fgg !=nil {return _fgg ;};_ga .ClientDataChoice =append (_ga .ClientDataChoice ,_ge );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0057\u0069\u0064\u0074\u0068\u004d\u0069\u006e"}:_bf :=NewCT_ClientDataChoice ();
if _caf :=d .DecodeElement (&_bf .WidthMin ,&_fd );_caf !=nil {return _caf ;};_ga .ClientDataChoice =append (_ga .ClientDataChoice ,_bf );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0053\u0065\u006c"}:_fee :=NewCT_ClientDataChoice ();
if _dbg :=d .DecodeElement (&_fee .Sel ,&_fd );_dbg !=nil {return _dbg ;};_ga .ClientDataChoice =append (_ga .ClientDataChoice ,_fee );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u004eo\u0054\u0068\u0072\u0065\u0065\u00442"}:_gee :=NewCT_ClientDataChoice ();
if _dge :=d .DecodeElement (&_gee .NoThreeD2 ,&_fd );_dge !=nil {return _dge ;};_ga .ClientDataChoice =append (_ga .ClientDataChoice ,_gee );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0053e\u006c\u0054\u0079\u0070\u0065"}:_beb :=NewCT_ClientDataChoice ();
if _ggb :=d .DecodeElement (&_beb .SelType ,&_fd );_ggb !=nil {return _ggb ;};_ga .ClientDataChoice =append (_ga .ClientDataChoice ,_beb );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u004d\u0075\u006c\u0074\u0069\u0053\u0065\u006c"}:_cga :=NewCT_ClientDataChoice ();
if _aeff :=d .DecodeElement (&_cga .MultiSel ,&_fd );_aeff !=nil {return _aeff ;};_ga .ClientDataChoice =append (_ga .ClientDataChoice ,_cga );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u004c\u0043\u0054"}:_aaa :=NewCT_ClientDataChoice ();
if _gbe :=d .DecodeElement (&_aaa .LCT ,&_fd );_gbe !=nil {return _gbe ;};_ga .ClientDataChoice =append (_ga .ClientDataChoice ,_aaa );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u004c\u0069\u0073\u0074\u0049\u0074\u0065\u006d"}:_ad :=NewCT_ClientDataChoice ();
if _fa :=d .DecodeElement (&_ad .ListItem ,&_fd );_fa !=nil {return _fa ;};_ga .ClientDataChoice =append (_ga .ClientDataChoice ,_ad );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0044r\u006f\u0070\u0053\u0074\u0079\u006ce"}:_dca :=NewCT_ClientDataChoice ();
if _ac :=d .DecodeElement (&_dca .DropStyle ,&_fd );_ac !=nil {return _ac ;};_ga .ClientDataChoice =append (_ga .ClientDataChoice ,_dca );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0043o\u006c\u006f\u0072\u0065\u0064"}:_bfd :=NewCT_ClientDataChoice ();
if _dda :=d .DecodeElement (&_bfd .Colored ,&_fd );_dda !=nil {return _dda ;};_ga .ClientDataChoice =append (_ga .ClientDataChoice ,_bfd );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0044r\u006f\u0070\u004c\u0069\u006e\u0065s"}:_af :=NewCT_ClientDataChoice ();
if _gge :=d .DecodeElement (&_af .DropLines ,&_fd );_gge !=nil {return _gge ;};_ga .ClientDataChoice =append (_ga .ClientDataChoice ,_af );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0043h\u0065\u0063\u006b\u0065\u0064"}:_eba :=NewCT_ClientDataChoice ();
if _dgc :=d .DecodeElement (&_eba .Checked ,&_fd );_dgc !=nil {return _dgc ;};_ga .ClientDataChoice =append (_ga .ClientDataChoice ,_eba );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0046\u006d\u006c\u0061\u004c\u0069\u006e\u006b"}:_ffa :=NewCT_ClientDataChoice ();
if _bed :=d .DecodeElement (&_ffa .FmlaLink ,&_fd );_bed !=nil {return _bed ;};_ga .ClientDataChoice =append (_ga .ClientDataChoice ,_ffa );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0046\u006d\u006c\u0061\u0050\u0069\u0063\u0074"}:_cae :=NewCT_ClientDataChoice ();
if _gac :=d .DecodeElement (&_cae .FmlaPict ,&_fd );_gac !=nil {return _gac ;};_ga .ClientDataChoice =append (_ga .ClientDataChoice ,_cae );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u004e\u006f\u0054\u0068\u0072\u0065\u0065\u0044"}:_def :=NewCT_ClientDataChoice ();
if _ec :=d .DecodeElement (&_def .NoThreeD ,&_fd );_ec !=nil {return _ec ;};_ga .ClientDataChoice =append (_ga .ClientDataChoice ,_def );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"F\u0069\u0072\u0073\u0074\u0042\u0075\u0074\u0074\u006f\u006e"}:_bdagb :=NewCT_ClientDataChoice ();
if _bec :=d .DecodeElement (&_bdagb .FirstButton ,&_fd );_bec !=nil {return _bec ;};_ga .ClientDataChoice =append (_ga .ClientDataChoice ,_bdagb );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0046m\u006c\u0061\u0047\u0072\u006f\u0075p"}:_gd :=NewCT_ClientDataChoice ();
if _ecf :=d .DecodeElement (&_gd .FmlaGroup ,&_fd );_ecf !=nil {return _ecf ;};_ga .ClientDataChoice =append (_ga .ClientDataChoice ,_gd );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0056\u0061\u006c"}:_cea :=NewCT_ClientDataChoice ();
if _fcc :=d .DecodeElement (&_cea .Val ,&_fd );_fcc !=nil {return _fcc ;};_ga .ClientDataChoice =append (_ga .ClientDataChoice ,_cea );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u004d\u0069\u006e"}:_cfb :=NewCT_ClientDataChoice ();
if _acc :=d .DecodeElement (&_cfb .Min ,&_fd );_acc !=nil {return _acc ;};_ga .ClientDataChoice =append (_ga .ClientDataChoice ,_cfb );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u004d\u0061\u0078"}:_fcf :=NewCT_ClientDataChoice ();
if _cb :=d .DecodeElement (&_fcf .Max ,&_fd );_cb !=nil {return _cb ;};_ga .ClientDataChoice =append (_ga .ClientDataChoice ,_fcf );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0049\u006e\u0063"}:_bb :=NewCT_ClientDataChoice ();
if _aba :=d .DecodeElement (&_bb .Inc ,&_fd );_aba !=nil {return _aba ;};_ga .ClientDataChoice =append (_ga .ClientDataChoice ,_bb );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0050\u0061\u0067\u0065"}:_dbf :=NewCT_ClientDataChoice ();
if _aaae :=d .DecodeElement (&_dbf .Page ,&_fd );_aaae !=nil {return _aaae ;};_ga .ClientDataChoice =append (_ga .ClientDataChoice ,_dbf );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0048\u006f\u0072i\u007a"}:_ggd :=NewCT_ClientDataChoice ();
if _gf :=d .DecodeElement (&_ggd .Horiz ,&_fd );_gf !=nil {return _gf ;};_ga .ClientDataChoice =append (_ga .ClientDataChoice ,_ggd );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0044\u0078"}:_fdg :=NewCT_ClientDataChoice ();
if _ded :=d .DecodeElement (&_fdg .Dx ,&_fd );_ded !=nil {return _ded ;};_ga .ClientDataChoice =append (_ga .ClientDataChoice ,_fdg );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u004d\u0061\u0070\u004f\u0043\u0058"}:_cab :=NewCT_ClientDataChoice ();
if _baf :=d .DecodeElement (&_cab .MapOCX ,&_fd );_baf !=nil {return _baf ;};_ga .ClientDataChoice =append (_ga .ClientDataChoice ,_cab );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0043\u0046"}:_gbc :=NewCT_ClientDataChoice ();
if _gae :=d .DecodeElement (&_gbc .CF ,&_fd );_gae !=nil {return _gae ;};_ga .ClientDataChoice =append (_ga .ClientDataChoice ,_gbc );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0043\u0061\u006d\u0065\u0072\u0061"}:_bccf :=NewCT_ClientDataChoice ();
if _cac :=d .DecodeElement (&_bccf .Camera ,&_fd );_cac !=nil {return _cac ;};_ga .ClientDataChoice =append (_ga .ClientDataChoice ,_bccf );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0052\u0065\u0063a\u006c\u0063\u0041\u006c\u0077\u0061\u0079\u0073"}:_fgf :=NewCT_ClientDataChoice ();
if _gacg :=d .DecodeElement (&_fgf .RecalcAlways ,&_fd );_gacg !=nil {return _gacg ;};_ga .ClientDataChoice =append (_ga .ClientDataChoice ,_fgf );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0041u\u0074\u006f\u0053\u0063\u0061\u006ce"}:_fccb :=NewCT_ClientDataChoice ();
if _dde :=d .DecodeElement (&_fccb .AutoScale ,&_fd );_dde !=nil {return _dde ;};_ga .ClientDataChoice =append (_ga .ClientDataChoice ,_fccb );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0044\u0044\u0045"}:_dfgc :=NewCT_ClientDataChoice ();
if _adb :=d .DecodeElement (&_dfgc .DDE ,&_fd );_adb !=nil {return _adb ;};_ga .ClientDataChoice =append (_ga .ClientDataChoice ,_dfgc );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0055\u0049\u004fb\u006a"}:_agc :=NewCT_ClientDataChoice ();
if _gdd :=d .DecodeElement (&_agc .UIObj ,&_fd );_gdd !=nil {return _gdd ;};_ga .ClientDataChoice =append (_ga .ClientDataChoice ,_agc );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0053\u0063\u0072\u0069\u0070\u0074\u0054\u0065\u0078\u0074"}:_aea :=NewCT_ClientDataChoice ();
if _babg :=d .DecodeElement (&_aea .ScriptText ,&_fd );_babg !=nil {return _babg ;};_ga .ClientDataChoice =append (_ga .ClientDataChoice ,_aea );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0053\u0063\u0072\u0069\u0070\u0074\u0045\u0078\u0074e\u006e\u0064\u0065\u0064"}:_dfa :=NewCT_ClientDataChoice ();
if _cba :=d .DecodeElement (&_dfa .ScriptExtended ,&_fd );_cba !=nil {return _cba ;};_ga .ClientDataChoice =append (_ga .ClientDataChoice ,_dfa );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0053\u0063\u0072\u0069\u0070\u0074\u004c\u0061\u006eg\u0075\u0061\u0067\u0065"}:_ed :=NewCT_ClientDataChoice ();
if _ecg :=d .DecodeElement (&_ed .ScriptLanguage ,&_fd );_ecg !=nil {return _ecg ;};_ga .ClientDataChoice =append (_ga .ClientDataChoice ,_ed );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0053\u0063\u0072\u0069\u0070\u0074\u004c\u006f\u0063a\u0074\u0069\u006f\u006e"}:_afd :=NewCT_ClientDataChoice ();
if _bac :=d .DecodeElement (&_afd .ScriptLocation ,&_fd );_bac !=nil {return _bac ;};_ga .ClientDataChoice =append (_ga .ClientDataChoice ,_afd );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0046\u006d\u006c\u0061\u0054\u0078\u0062\u0078"}:_bcga :=NewCT_ClientDataChoice ();
if _ffe :=d .DecodeElement (&_bcga .FmlaTxbx ,&_fd );_ffe !=nil {return _ffe ;};_ga .ClientDataChoice =append (_ga .ClientDataChoice ,_bcga );default:_c .Log .Debug ("\u0073\u006b\u0069\u0070\u0070\u0069\u006e\u0067 \u0075\u006e\u0073up\u0070\u006f\u0072\u0074\u0065\u0064 \u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0043\u0054\u005f\u0043l\u0069\u0065\u006e\u0074\u0044\u0061\u0074\u0061 \u0025\u0076",_fd .Name );
if _dba :=d .Skip ();_dba !=nil {return _dba ;};};case _g .EndElement :break _df ;case _g .CharData :};};return nil ;};func (_dgeb *ST_ObjectType )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {_gaf ,_egb :=d .Token ();if _egb !=nil {return _egb ;
};if _dbde ,_cadc :=_gaf .(_g .EndElement );_cadc &&_dbde .Name ==start .Name {*_dgeb =1;return nil ;};if _gfb ,_dbb :=_gaf .(_g .CharData );!_dbb {return _f .Errorf ("\u0065\u0078\u0070\u0065\u0063\u0074\u0065\u0064\u0020\u0063\u0068a\u0072\u0020\u0064\u0061\u0074\u0061\u002c\u0020\u0067\u006ft\u0020\u0025\u0054",_gaf );
}else {switch string (_gfb ){case "":*_dgeb =0;case "\u0042\u0075\u0074\u0074\u006f\u006e":*_dgeb =1;case "\u0043\u0068\u0065\u0063\u006b\u0062\u006f\u0078":*_dgeb =2;case "\u0044\u0069\u0061\u006c\u006f\u0067":*_dgeb =3;case "\u0044\u0072\u006f\u0070":*_dgeb =4;
case "\u0045\u0064\u0069\u0074":*_dgeb =5;case "\u0047\u0042\u006f\u0078":*_dgeb =6;case "\u004c\u0061\u0062e\u006c":*_dgeb =7;case "\u004c\u0069\u006ee\u0041":*_dgeb =8;case "\u004c\u0069\u0073\u0074":*_dgeb =9;case "\u004d\u006f\u0076i\u0065":*_dgeb =10;
case "\u004e\u006f\u0074\u0065":*_dgeb =11;case "\u0050\u0069\u0063\u0074":*_dgeb =12;case "\u0052\u0061\u0064i\u006f":*_dgeb =13;case "\u0052\u0065\u0063t\u0041":*_dgeb =14;case "\u0053\u0063\u0072\u006f\u006c\u006c":*_dgeb =15;case "\u0053\u0070\u0069\u006e":*_dgeb =16;
case "\u0053\u0068\u0061p\u0065":*_dgeb =17;case "\u0047\u0072\u006fu\u0070":*_dgeb =18;case "\u0052\u0065\u0063\u0074":*_dgeb =19;};};_gaf ,_egb =d .Token ();if _egb !=nil {return _egb ;};if _baed ,_ffd :=_gaf .(_g .EndElement );_ffd &&_baed .Name ==start .Name {return nil ;
};return _f .Errorf ("\u0065\u0078\u0070\u0065c\u0074\u0065\u0064\u0020\u0065\u006e\u0064\u0020\u0065\u006ce\u006de\u006e\u0074\u002c\u0020\u0067\u006f\u0074 \u0025\u0076",_gaf );};func (_babd *ST_ObjectType )UnmarshalXMLAttr (attr _g .Attr )error {switch attr .Value {case "":*_babd =0;
case "\u0042\u0075\u0074\u0074\u006f\u006e":*_babd =1;case "\u0043\u0068\u0065\u0063\u006b\u0062\u006f\u0078":*_babd =2;case "\u0044\u0069\u0061\u006c\u006f\u0067":*_babd =3;case "\u0044\u0072\u006f\u0070":*_babd =4;case "\u0045\u0064\u0069\u0074":*_babd =5;
case "\u0047\u0042\u006f\u0078":*_babd =6;case "\u004c\u0061\u0062e\u006c":*_babd =7;case "\u004c\u0069\u006ee\u0041":*_babd =8;case "\u004c\u0069\u0073\u0074":*_babd =9;case "\u004d\u006f\u0076i\u0065":*_babd =10;case "\u004e\u006f\u0074\u0065":*_babd =11;
case "\u0050\u0069\u0063\u0074":*_babd =12;case "\u0052\u0061\u0064i\u006f":*_babd =13;case "\u0052\u0065\u0063t\u0041":*_babd =14;case "\u0053\u0063\u0072\u006f\u006c\u006c":*_babd =15;case "\u0053\u0070\u0069\u006e":*_babd =16;case "\u0053\u0068\u0061p\u0065":*_babd =17;
case "\u0047\u0072\u006fu\u0070":*_babd =18;case "\u0052\u0065\u0063\u0074":*_babd =19;};return nil ;};func (_bg *CT_ClientDataChoice )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {e .EncodeToken (start );if _bg .MoveWithCells !=_fe .ST_TrueFalseBlankUnset {_deg :=_g .StartElement {Name :_g .Name {Local :"\u0078:\u004do\u0076\u0065\u0057\u0069\u0074\u0068\u0043\u0065\u006c\u006c\u0073"}};
e .EncodeElement (_bg .MoveWithCells ,_deg );}else if _bg .SizeWithCells !=_fe .ST_TrueFalseBlankUnset {_dedc :=_g .StartElement {Name :_g .Name {Local :"\u0078:\u0053i\u007a\u0065\u0057\u0069\u0074\u0068\u0043\u0065\u006c\u006c\u0073"}};e .EncodeElement (_bg .SizeWithCells ,_dedc );
}else if _bg .Anchor !=nil {_efde :=_g .StartElement {Name :_g .Name {Local :"\u0078\u003a\u0041\u006e\u0063\u0068\u006f\u0072"}};_b .AddPreserveSpaceAttr (&_efde ,*_bg .Anchor );e .EncodeElement (_bg .Anchor ,_efde );}else if _bg .Locked !=_fe .ST_TrueFalseBlankUnset {_agf :=_g .StartElement {Name :_g .Name {Local :"\u0078\u003a\u004c\u006f\u0063\u006b\u0065\u0064"}};
e .EncodeElement (_bg .Locked ,_agf );}else if _bg .DefaultSize !=_fe .ST_TrueFalseBlankUnset {_cde :=_g .StartElement {Name :_g .Name {Local :"\u0078\u003a\u0044\u0065\u0066\u0061\u0075\u006c\u0074\u0053\u0069\u007a\u0065"}};e .EncodeElement (_bg .DefaultSize ,_cde );
}else if _bg .PrintObject !=_fe .ST_TrueFalseBlankUnset {_aad :=_g .StartElement {Name :_g .Name {Local :"\u0078\u003a\u0050\u0072\u0069\u006e\u0074\u004f\u0062\u006a\u0065\u0063\u0074"}};e .EncodeElement (_bg .PrintObject ,_aad );}else if _bg .Disabled !=_fe .ST_TrueFalseBlankUnset {_ccgg :=_g .StartElement {Name :_g .Name {Local :"\u0078\u003a\u0044\u0069\u0073\u0061\u0062\u006c\u0065\u0064"}};
e .EncodeElement (_bg .Disabled ,_ccgg );}else if _bg .AutoFill !=_fe .ST_TrueFalseBlankUnset {_fgc :=_g .StartElement {Name :_g .Name {Local :"\u0078\u003a\u0041\u0075\u0074\u006f\u0046\u0069\u006c\u006c"}};e .EncodeElement (_bg .AutoFill ,_fgc );}else if _bg .AutoLine !=_fe .ST_TrueFalseBlankUnset {_cbb :=_g .StartElement {Name :_g .Name {Local :"\u0078\u003a\u0041\u0075\u0074\u006f\u004c\u0069\u006e\u0065"}};
e .EncodeElement (_bg .AutoLine ,_cbb );}else if _bg .AutoPict !=_fe .ST_TrueFalseBlankUnset {_bcb :=_g .StartElement {Name :_g .Name {Local :"\u0078\u003a\u0041\u0075\u0074\u006f\u0050\u0069\u0063\u0074"}};e .EncodeElement (_bg .AutoPict ,_bcb );}else if _bg .FmlaMacro !=nil {_cacd :=_g .StartElement {Name :_g .Name {Local :"x\u003a\u0046\u006d\u006c\u0061\u004d\u0061\u0063\u0072\u006f"}};
_b .AddPreserveSpaceAttr (&_cacd ,*_bg .FmlaMacro );e .EncodeElement (_bg .FmlaMacro ,_cacd );}else if _bg .TextHAlign !=nil {_bcgb :=_g .StartElement {Name :_g .Name {Local :"\u0078\u003a\u0054e\u0078\u0074\u0048\u0041\u006c\u0069\u0067\u006e"}};_b .AddPreserveSpaceAttr (&_bcgb ,*_bg .TextHAlign );
e .EncodeElement (_bg .TextHAlign ,_bcgb );}else if _bg .TextVAlign !=nil {_daf :=_g .StartElement {Name :_g .Name {Local :"\u0078\u003a\u0054e\u0078\u0074\u0056\u0041\u006c\u0069\u0067\u006e"}};_b .AddPreserveSpaceAttr (&_daf ,*_bg .TextVAlign );e .EncodeElement (_bg .TextVAlign ,_daf );
}else if _bg .LockText !=_fe .ST_TrueFalseBlankUnset {_fgce :=_g .StartElement {Name :_g .Name {Local :"\u0078\u003a\u004c\u006f\u0063\u006b\u0054\u0065\u0078\u0074"}};e .EncodeElement (_bg .LockText ,_fgce );}else if _bg .JustLastX !=_fe .ST_TrueFalseBlankUnset {_cdd :=_g .StartElement {Name :_g .Name {Local :"x\u003a\u004a\u0075\u0073\u0074\u004c\u0061\u0073\u0074\u0058"}};
e .EncodeElement (_bg .JustLastX ,_cdd );}else if _bg .SecretEdit !=_fe .ST_TrueFalseBlankUnset {_fcd :=_g .StartElement {Name :_g .Name {Local :"\u0078\u003a\u0053e\u0063\u0072\u0065\u0074\u0045\u0064\u0069\u0074"}};e .EncodeElement (_bg .SecretEdit ,_fcd );
}else if _bg .Default !=_fe .ST_TrueFalseBlankUnset {_cdaa :=_g .StartElement {Name :_g .Name {Local :"\u0078:\u0044\u0065\u0066\u0061\u0075\u006ct"}};e .EncodeElement (_bg .Default ,_cdaa );}else if _bg .Help !=_fe .ST_TrueFalseBlankUnset {_facb :=_g .StartElement {Name :_g .Name {Local :"\u0078\u003a\u0048\u0065\u006c\u0070"}};
e .EncodeElement (_bg .Help ,_facb );}else if _bg .Cancel !=_fe .ST_TrueFalseBlankUnset {_ege :=_g .StartElement {Name :_g .Name {Local :"\u0078\u003a\u0043\u0061\u006e\u0063\u0065\u006c"}};e .EncodeElement (_bg .Cancel ,_ege );}else if _bg .Dismiss !=_fe .ST_TrueFalseBlankUnset {_fb :=_g .StartElement {Name :_g .Name {Local :"\u0078:\u0044\u0069\u0073\u006d\u0069\u0073s"}};
e .EncodeElement (_bg .Dismiss ,_fb );}else if _bg .Accel !=nil {_fag :=_g .StartElement {Name :_g .Name {Local :"\u0078:\u0041\u0063\u0063\u0065\u006c"}};e .EncodeElement (_bg .Accel ,_fag );}else if _bg .Accel2 !=nil {_fae :=_g .StartElement {Name :_g .Name {Local :"\u0078\u003a\u0041\u0063\u0063\u0065\u006c\u0032"}};
e .EncodeElement (_bg .Accel2 ,_fae );}else if _bg .Row !=nil {_ede :=_g .StartElement {Name :_g .Name {Local :"\u0078\u003a\u0052o\u0077"}};e .EncodeElement (_bg .Row ,_ede );}else if _bg .Column !=nil {_dcc :=_g .StartElement {Name :_g .Name {Local :"\u0078\u003a\u0043\u006f\u006c\u0075\u006d\u006e"}};
e .EncodeElement (_bg .Column ,_dcc );}else if _bg .Visible !=_fe .ST_TrueFalseBlankUnset {_acd :=_g .StartElement {Name :_g .Name {Local :"\u0078:\u0056\u0069\u0073\u0069\u0062\u006ce"}};e .EncodeElement (_bg .Visible ,_acd );}else if _bg .RowHidden !=_fe .ST_TrueFalseBlankUnset {_fcb :=_g .StartElement {Name :_g .Name {Local :"x\u003a\u0052\u006f\u0077\u0048\u0069\u0064\u0064\u0065\u006e"}};
e .EncodeElement (_bg .RowHidden ,_fcb );}else if _bg .ColHidden !=_fe .ST_TrueFalseBlankUnset {_bfb :=_g .StartElement {Name :_g .Name {Local :"x\u003a\u0043\u006f\u006c\u0048\u0069\u0064\u0064\u0065\u006e"}};e .EncodeElement (_bg .ColHidden ,_bfb );}else if _bg .VTEdit !=nil {_fcdb :=_g .StartElement {Name :_g .Name {Local :"\u0078\u003a\u0056\u0054\u0045\u0064\u0069\u0074"}};
e .EncodeElement (_bg .VTEdit ,_fcdb );}else if _bg .MultiLine !=_fe .ST_TrueFalseBlankUnset {_cfbf :=_g .StartElement {Name :_g .Name {Local :"x\u003a\u004d\u0075\u006c\u0074\u0069\u004c\u0069\u006e\u0065"}};e .EncodeElement (_bg .MultiLine ,_cfbf );}else if _bg .VScroll !=_fe .ST_TrueFalseBlankUnset {_bdad :=_g .StartElement {Name :_g .Name {Local :"\u0078:\u0056\u0053\u0063\u0072\u006f\u006cl"}};
e .EncodeElement (_bg .VScroll ,_bdad );}else if _bg .ValidIds !=_fe .ST_TrueFalseBlankUnset {_gaa :=_g .StartElement {Name :_g .Name {Local :"\u0078\u003a\u0056\u0061\u006c\u0069\u0064\u0049\u0064\u0073"}};e .EncodeElement (_bg .ValidIds ,_gaa );}else if _bg .FmlaRange !=nil {_edc :=_g .StartElement {Name :_g .Name {Local :"x\u003a\u0046\u006d\u006c\u0061\u0052\u0061\u006e\u0067\u0065"}};
_b .AddPreserveSpaceAttr (&_edc ,*_bg .FmlaRange );e .EncodeElement (_bg .FmlaRange ,_edc );}else if _bg .WidthMin !=nil {_feeb :=_g .StartElement {Name :_g .Name {Local :"\u0078\u003a\u0057\u0069\u0064\u0074\u0068\u004d\u0069\u006e"}};e .EncodeElement (_bg .WidthMin ,_feeb );
}else if _bg .Sel !=nil {_ecc :=_g .StartElement {Name :_g .Name {Local :"\u0078\u003a\u0053e\u006c"}};e .EncodeElement (_bg .Sel ,_ecc );}else if _bg .NoThreeD2 !=_fe .ST_TrueFalseBlankUnset {_gea :=_g .StartElement {Name :_g .Name {Local :"x\u003a\u004e\u006f\u0054\u0068\u0072\u0065\u0065\u0044\u0032"}};
e .EncodeElement (_bg .NoThreeD2 ,_gea );}else if _bg .SelType !=nil {_gba :=_g .StartElement {Name :_g .Name {Local :"\u0078:\u0053\u0065\u006c\u0054\u0079\u0070e"}};_b .AddPreserveSpaceAttr (&_gba ,*_bg .SelType );e .EncodeElement (_bg .SelType ,_gba );
}else if _bg .MultiSel !=nil {_bcce :=_g .StartElement {Name :_g .Name {Local :"\u0078\u003a\u004d\u0075\u006c\u0074\u0069\u0053\u0065\u006c"}};_b .AddPreserveSpaceAttr (&_bcce ,*_bg .MultiSel );e .EncodeElement (_bg .MultiSel ,_bcce );}else if _bg .LCT !=nil {_dafb :=_g .StartElement {Name :_g .Name {Local :"\u0078\u003a\u004cC\u0054"}};
_b .AddPreserveSpaceAttr (&_dafb ,*_bg .LCT );e .EncodeElement (_bg .LCT ,_dafb );}else if _bg .ListItem !=nil {_ebef :=_g .StartElement {Name :_g .Name {Local :"\u0078\u003a\u004c\u0069\u0073\u0074\u0049\u0074\u0065\u006d"}};_b .AddPreserveSpaceAttr (&_ebef ,*_bg .ListItem );
e .EncodeElement (_bg .ListItem ,_ebef );}else if _bg .DropStyle !=nil {_gaag :=_g .StartElement {Name :_g .Name {Local :"x\u003a\u0044\u0072\u006f\u0070\u0053\u0074\u0079\u006c\u0065"}};_b .AddPreserveSpaceAttr (&_gaag ,*_bg .DropStyle );e .EncodeElement (_bg .DropStyle ,_gaag );
}else if _bg .Colored !=_fe .ST_TrueFalseBlankUnset {_bff :=_g .StartElement {Name :_g .Name {Local :"\u0078:\u0043\u006f\u006c\u006f\u0072\u0065d"}};e .EncodeElement (_bg .Colored ,_bff );}else if _bg .DropLines !=nil {_faeb :=_g .StartElement {Name :_g .Name {Local :"x\u003a\u0044\u0072\u006f\u0070\u004c\u0069\u006e\u0065\u0073"}};
e .EncodeElement (_bg .DropLines ,_faeb );}else if _bg .Checked !=nil {_egea :=_g .StartElement {Name :_g .Name {Local :"\u0078:\u0043\u0068\u0065\u0063\u006b\u0065d"}};e .EncodeElement (_bg .Checked ,_egea );}else if _bg .FmlaLink !=nil {_eab :=_g .StartElement {Name :_g .Name {Local :"\u0078\u003a\u0046\u006d\u006c\u0061\u004c\u0069\u006e\u006b"}};
_b .AddPreserveSpaceAttr (&_eab ,*_bg .FmlaLink );e .EncodeElement (_bg .FmlaLink ,_eab );}else if _bg .FmlaPict !=nil {_acf :=_g .StartElement {Name :_g .Name {Local :"\u0078\u003a\u0046\u006d\u006c\u0061\u0050\u0069\u0063\u0074"}};_b .AddPreserveSpaceAttr (&_acf ,*_bg .FmlaPict );
e .EncodeElement (_bg .FmlaPict ,_acf );}else if _bg .NoThreeD !=_fe .ST_TrueFalseBlankUnset {_dafe :=_g .StartElement {Name :_g .Name {Local :"\u0078\u003a\u004e\u006f\u0054\u0068\u0072\u0065\u0065\u0044"}};e .EncodeElement (_bg .NoThreeD ,_dafe );}else if _bg .FirstButton !=_fe .ST_TrueFalseBlankUnset {_cff :=_g .StartElement {Name :_g .Name {Local :"\u0078\u003a\u0046\u0069\u0072\u0073\u0074\u0042\u0075\u0074\u0074\u006f\u006e"}};
e .EncodeElement (_bg .FirstButton ,_cff );}else if _bg .FmlaGroup !=nil {_gbaa :=_g .StartElement {Name :_g .Name {Local :"x\u003a\u0046\u006d\u006c\u0061\u0047\u0072\u006f\u0075\u0070"}};_b .AddPreserveSpaceAttr (&_gbaa ,*_bg .FmlaGroup );e .EncodeElement (_bg .FmlaGroup ,_gbaa );
}else if _bg .Val !=nil {_abed :=_g .StartElement {Name :_g .Name {Local :"\u0078\u003a\u0056a\u006c"}};e .EncodeElement (_bg .Val ,_abed );}else if _bg .Min !=nil {_agb :=_g .StartElement {Name :_g .Name {Local :"\u0078\u003a\u004di\u006e"}};e .EncodeElement (_bg .Min ,_agb );
}else if _bg .Max !=nil {_aeae :=_g .StartElement {Name :_g .Name {Local :"\u0078\u003a\u004da\u0078"}};e .EncodeElement (_bg .Max ,_aeae );}else if _bg .Inc !=nil {_dfd :=_g .StartElement {Name :_g .Name {Local :"\u0078\u003a\u0049n\u0063"}};e .EncodeElement (_bg .Inc ,_dfd );
}else if _bg .Page !=nil {_fdb :=_g .StartElement {Name :_g .Name {Local :"\u0078\u003a\u0050\u0061\u0067\u0065"}};e .EncodeElement (_bg .Page ,_fdb );}else if _bg .Horiz !=_fe .ST_TrueFalseBlankUnset {_ace :=_g .StartElement {Name :_g .Name {Local :"\u0078:\u0048\u006f\u0072\u0069\u007a"}};
e .EncodeElement (_bg .Horiz ,_ace );}else if _bg .Dx !=nil {_dab :=_g .StartElement {Name :_g .Name {Local :"\u0078\u003a\u0044\u0078"}};e .EncodeElement (_bg .Dx ,_dab );}else if _bg .MapOCX !=_fe .ST_TrueFalseBlankUnset {_cfa :=_g .StartElement {Name :_g .Name {Local :"\u0078\u003a\u004d\u0061\u0070\u004f\u0043\u0058"}};
e .EncodeElement (_bg .MapOCX ,_cfa );}else if _bg .CF !=nil {_abef :=_g .StartElement {Name :_g .Name {Local :"\u0078\u003a\u0043\u0046"}};_b .AddPreserveSpaceAttr (&_abef ,*_bg .CF );e .EncodeElement (_bg .CF ,_abef );}else if _bg .Camera !=_fe .ST_TrueFalseBlankUnset {_dga :=_g .StartElement {Name :_g .Name {Local :"\u0078\u003a\u0043\u0061\u006d\u0065\u0072\u0061"}};
e .EncodeElement (_bg .Camera ,_dga );}else if _bg .RecalcAlways !=_fe .ST_TrueFalseBlankUnset {_gdg :=_g .StartElement {Name :_g .Name {Local :"\u0078\u003a\u0052\u0065\u0063\u0061\u006c\u0063\u0041l\u0077\u0061\u0079\u0073"}};e .EncodeElement (_bg .RecalcAlways ,_gdg );
}else if _bg .AutoScale !=_fe .ST_TrueFalseBlankUnset {_efg :=_g .StartElement {Name :_g .Name {Local :"x\u003a\u0041\u0075\u0074\u006f\u0053\u0063\u0061\u006c\u0065"}};e .EncodeElement (_bg .AutoScale ,_efg );}else if _bg .DDE !=_fe .ST_TrueFalseBlankUnset {_aac :=_g .StartElement {Name :_g .Name {Local :"\u0078\u003a\u0044D\u0045"}};
e .EncodeElement (_bg .DDE ,_aac );}else if _bg .UIObj !=_fe .ST_TrueFalseBlankUnset {_eaf :=_g .StartElement {Name :_g .Name {Local :"\u0078:\u0055\u0049\u004f\u0062\u006a"}};e .EncodeElement (_bg .UIObj ,_eaf );}else if _bg .ScriptText !=nil {_aec :=_g .StartElement {Name :_g .Name {Local :"\u0078\u003a\u0053c\u0072\u0069\u0070\u0074\u0054\u0065\u0078\u0074"}};
_b .AddPreserveSpaceAttr (&_aec ,*_bg .ScriptText );e .EncodeElement (_bg .ScriptText ,_aec );}else if _bg .ScriptExtended !=nil {_fgfb :=_g .StartElement {Name :_g .Name {Local :"\u0078\u003aS\u0063\u0072\u0069p\u0074\u0045\u0078\u0074\u0065\u006e\u0064\u0065\u0064"}};
_b .AddPreserveSpaceAttr (&_fgfb ,*_bg .ScriptExtended );e .EncodeElement (_bg .ScriptExtended ,_fgfb );}else if _bg .ScriptLanguage !=nil {_fbc :=_g .StartElement {Name :_g .Name {Local :"\u0078\u003aS\u0063\u0072\u0069p\u0074\u004c\u0061\u006e\u0067\u0075\u0061\u0067\u0065"}};
e .EncodeElement (_bg .ScriptLanguage ,_fbc );}else if _bg .ScriptLocation !=nil {_afdd :=_g .StartElement {Name :_g .Name {Local :"\u0078\u003aS\u0063\u0072\u0069p\u0074\u004c\u006f\u0063\u0061\u0074\u0069\u006f\u006e"}};e .EncodeElement (_bg .ScriptLocation ,_afdd );
}else if _bg .FmlaTxbx !=nil {_bagb :=_g .StartElement {Name :_g .Name {Local :"\u0078\u003a\u0046\u006d\u006c\u0061\u0054\u0078\u0062\u0078"}};_b .AddPreserveSpaceAttr (&_bagb ,*_bg .FmlaTxbx );e .EncodeElement (_bg .FmlaTxbx ,_bagb );};e .EncodeToken (_g .EndElement {Name :start .Name });
return nil ;};type ClientData struct{CT_ClientData };func (_fba *CT_ClientDataChoice )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {_aeb :=start ;switch start .Name {case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u004d\u006f\u0076\u0065\u0057\u0069\u0074\u0068\u0043\u0065\u006c\u006c\u0073"}:_fba .MoveWithCells =_fe .ST_TrueFalseBlankUnset ;
if _dgd :=d .DecodeElement (&_fba .MoveWithCells ,&_aeb );_dgd !=nil {return _dgd ;};case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0053\u0069\u007a\u0065\u0057\u0069\u0074\u0068\u0043\u0065\u006c\u006c\u0073"}:_fba .SizeWithCells =_fe .ST_TrueFalseBlankUnset ;
if _ccf :=d .DecodeElement (&_fba .SizeWithCells ,&_aeb );_ccf !=nil {return _ccf ;};case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0041\u006e\u0063\u0068\u006f\u0072"}:_fba .Anchor =new (string );
if _bdc :=d .DecodeElement (_fba .Anchor ,&_aeb );_bdc !=nil {return _bdc ;};case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u004c\u006f\u0063\u006b\u0065\u0064"}:_fba .Locked =_fe .ST_TrueFalseBlankUnset ;
if _bbc :=d .DecodeElement (&_fba .Locked ,&_aeb );_bbc !=nil {return _bbc ;};case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"D\u0065\u0066\u0061\u0075\u006c\u0074\u0053\u0069\u007a\u0065"}:_fba .DefaultSize =_fe .ST_TrueFalseBlankUnset ;
if _dea :=d .DecodeElement (&_fba .DefaultSize ,&_aeb );_dea !=nil {return _dea ;};case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"P\u0072\u0069\u006e\u0074\u004f\u0062\u006a\u0065\u0063\u0074"}:_fba .PrintObject =_fe .ST_TrueFalseBlankUnset ;
if _bdga :=d .DecodeElement (&_fba .PrintObject ,&_aeb );_bdga !=nil {return _bdga ;};case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0044\u0069\u0073\u0061\u0062\u006c\u0065\u0064"}:_fba .Disabled =_fe .ST_TrueFalseBlankUnset ;
if _cgad :=d .DecodeElement (&_fba .Disabled ,&_aeb );_cgad !=nil {return _cgad ;};case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0041\u0075\u0074\u006f\u0046\u0069\u006c\u006c"}:_fba .AutoFill =_fe .ST_TrueFalseBlankUnset ;
if _fff :=d .DecodeElement (&_fba .AutoFill ,&_aeb );_fff !=nil {return _fff ;};case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0041\u0075\u0074\u006f\u004c\u0069\u006e\u0065"}:_fba .AutoLine =_fe .ST_TrueFalseBlankUnset ;
if _cbg :=d .DecodeElement (&_fba .AutoLine ,&_aeb );_cbg !=nil {return _cbg ;};case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0041\u0075\u0074\u006f\u0050\u0069\u0063\u0074"}:_fba .AutoPict =_fe .ST_TrueFalseBlankUnset ;
if _bfbe :=d .DecodeElement (&_fba .AutoPict ,&_aeb );_bfbe !=nil {return _bfbe ;};case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0046m\u006c\u0061\u004d\u0061\u0063\u0072o"}:_fba .FmlaMacro =new (string );
if _dgf :=d .DecodeElement (_fba .FmlaMacro ,&_aeb );_dgf !=nil {return _dgf ;};case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0054\u0065\u0078\u0074\u0048\u0041\u006c\u0069\u0067\u006e"}:_fba .TextHAlign =new (string );
if _bfg :=d .DecodeElement (_fba .TextHAlign ,&_aeb );_bfg !=nil {return _bfg ;};case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0054\u0065\u0078\u0074\u0056\u0041\u006c\u0069\u0067\u006e"}:_fba .TextVAlign =new (string );
if _dbc :=d .DecodeElement (_fba .TextVAlign ,&_aeb );_dbc !=nil {return _dbc ;};case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u004c\u006f\u0063\u006b\u0054\u0065\u0078\u0074"}:_fba .LockText =_fe .ST_TrueFalseBlankUnset ;
if _eac :=d .DecodeElement (&_fba .LockText ,&_aeb );_eac !=nil {return _eac ;};case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u004au\u0073\u0074\u004c\u0061\u0073\u0074X"}:_fba .JustLastX =_fe .ST_TrueFalseBlankUnset ;
if _eegg :=d .DecodeElement (&_fba .JustLastX ,&_aeb );_eegg !=nil {return _eegg ;};case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0053\u0065\u0063\u0072\u0065\u0074\u0045\u0064\u0069\u0074"}:_fba .SecretEdit =_fe .ST_TrueFalseBlankUnset ;
if _bgg :=d .DecodeElement (&_fba .SecretEdit ,&_aeb );_bgg !=nil {return _bgg ;};case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0044e\u0066\u0061\u0075\u006c\u0074"}:_fba .Default =_fe .ST_TrueFalseBlankUnset ;
if _bfe :=d .DecodeElement (&_fba .Default ,&_aeb );_bfe !=nil {return _bfe ;};case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0048\u0065\u006c\u0070"}:_fba .Help =_fe .ST_TrueFalseBlankUnset ;
if _dcf :=d .DecodeElement (&_fba .Help ,&_aeb );_dcf !=nil {return _dcf ;};case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0043\u0061\u006e\u0063\u0065\u006c"}:_fba .Cancel =_fe .ST_TrueFalseBlankUnset ;
if _bca :=d .DecodeElement (&_fba .Cancel ,&_aeb );_bca !=nil {return _bca ;};case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0044i\u0073\u006d\u0069\u0073\u0073"}:_fba .Dismiss =_fe .ST_TrueFalseBlankUnset ;
if _bcca :=d .DecodeElement (&_fba .Dismiss ,&_aeb );_bcca !=nil {return _bcca ;};case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0041\u0063\u0063e\u006c"}:_fba .Accel =new (int64 );
if _ece :=d .DecodeElement (_fba .Accel ,&_aeb );_ece !=nil {return _ece ;};case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0041\u0063\u0063\u0065\u006c\u0032"}:_fba .Accel2 =new (int64 );
if _cedf :=d .DecodeElement (_fba .Accel2 ,&_aeb );_cedf !=nil {return _cedf ;};case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0052\u006f\u0077"}:_fba .Row =new (int64 );
if _dag :=d .DecodeElement (_fba .Row ,&_aeb );_dag !=nil {return _dag ;};case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0043\u006f\u006c\u0075\u006d\u006e"}:_fba .Column =new (int64 );
if _cgc :=d .DecodeElement (_fba .Column ,&_aeb );_cgc !=nil {return _cgc ;};case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0056i\u0073\u0069\u0062\u006c\u0065"}:_fba .Visible =_fe .ST_TrueFalseBlankUnset ;
if _dac :=d .DecodeElement (&_fba .Visible ,&_aeb );_dac !=nil {return _dac ;};case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0052o\u0077\u0048\u0069\u0064\u0064\u0065n"}:_fba .RowHidden =_fe .ST_TrueFalseBlankUnset ;
if _aaaeg :=d .DecodeElement (&_fba .RowHidden ,&_aeb );_aaaeg !=nil {return _aaaeg ;};case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0043o\u006c\u0048\u0069\u0064\u0064\u0065n"}:_fba .ColHidden =_fe .ST_TrueFalseBlankUnset ;
if _ffg :=d .DecodeElement (&_fba .ColHidden ,&_aeb );_ffg !=nil {return _ffg ;};case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0056\u0054\u0045\u0064\u0069\u0074"}:_fba .VTEdit =new (int64 );
if _abad :=d .DecodeElement (_fba .VTEdit ,&_aeb );_abad !=nil {return _abad ;};case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u004du\u006c\u0074\u0069\u004c\u0069\u006ee"}:_fba .MultiLine =_fe .ST_TrueFalseBlankUnset ;
if _bfed :=d .DecodeElement (&_fba .MultiLine ,&_aeb );_bfed !=nil {return _bfed ;};case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0056S\u0063\u0072\u006f\u006c\u006c"}:_fba .VScroll =_fe .ST_TrueFalseBlankUnset ;
if _eda :=d .DecodeElement (&_fba .VScroll ,&_aeb );_eda !=nil {return _eda ;};case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0056\u0061\u006c\u0069\u0064\u0049\u0064\u0073"}:_fba .ValidIds =_fe .ST_TrueFalseBlankUnset ;
if _gaad :=d .DecodeElement (&_fba .ValidIds ,&_aeb );_gaad !=nil {return _gaad ;};case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0046m\u006c\u0061\u0052\u0061\u006e\u0067e"}:_fba .FmlaRange =new (string );
if _ddd :=d .DecodeElement (_fba .FmlaRange ,&_aeb );_ddd !=nil {return _ddd ;};case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0057\u0069\u0064\u0074\u0068\u004d\u0069\u006e"}:_fba .WidthMin =new (int64 );
if _abb :=d .DecodeElement (_fba .WidthMin ,&_aeb );_abb !=nil {return _abb ;};case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0053\u0065\u006c"}:_fba .Sel =new (int64 );
if _dae :=d .DecodeElement (_fba .Sel ,&_aeb );_dae !=nil {return _dae ;};case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u004eo\u0054\u0068\u0072\u0065\u0065\u00442"}:_fba .NoThreeD2 =_fe .ST_TrueFalseBlankUnset ;
if _eec :=d .DecodeElement (&_fba .NoThreeD2 ,&_aeb );_eec !=nil {return _eec ;};case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0053e\u006c\u0054\u0079\u0070\u0065"}:_fba .SelType =new (string );
if _dgdc :=d .DecodeElement (_fba .SelType ,&_aeb );_dgdc !=nil {return _dgdc ;};case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u004d\u0075\u006c\u0074\u0069\u0053\u0065\u006c"}:_fba .MultiSel =new (string );
if _ccga :=d .DecodeElement (_fba .MultiSel ,&_aeb );_ccga !=nil {return _ccga ;};case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u004c\u0043\u0054"}:_fba .LCT =new (string );
if _fffe :=d .DecodeElement (_fba .LCT ,&_aeb );_fffe !=nil {return _fffe ;};case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u004c\u0069\u0073\u0074\u0049\u0074\u0065\u006d"}:_fba .ListItem =new (string );
if _geaf :=d .DecodeElement (_fba .ListItem ,&_aeb );_geaf !=nil {return _geaf ;};case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0044r\u006f\u0070\u0053\u0074\u0079\u006ce"}:_fba .DropStyle =new (string );
if _fbb :=d .DecodeElement (_fba .DropStyle ,&_aeb );_fbb !=nil {return _fbb ;};case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0043o\u006c\u006f\u0072\u0065\u0064"}:_fba .Colored =_fe .ST_TrueFalseBlankUnset ;
if _geg :=d .DecodeElement (&_fba .Colored ,&_aeb );_geg !=nil {return _geg ;};case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0044r\u006f\u0070\u004c\u0069\u006e\u0065s"}:_fba .DropLines =new (int64 );
if _dgfg :=d .DecodeElement (_fba .DropLines ,&_aeb );_dgfg !=nil {return _dgfg ;};case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0043h\u0065\u0063\u006b\u0065\u0064"}:_fba .Checked =new (int64 );
if _eea :=d .DecodeElement (_fba .Checked ,&_aeb );_eea !=nil {return _eea ;};case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0046\u006d\u006c\u0061\u004c\u0069\u006e\u006b"}:_fba .FmlaLink =new (string );
if _bee :=d .DecodeElement (_fba .FmlaLink ,&_aeb );_bee !=nil {return _bee ;};case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0046\u006d\u006c\u0061\u0050\u0069\u0063\u0074"}:_fba .FmlaPict =new (string );
if _eegge :=d .DecodeElement (_fba .FmlaPict ,&_aeb );_eegge !=nil {return _eegge ;};case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u004e\u006f\u0054\u0068\u0072\u0065\u0065\u0044"}:_fba .NoThreeD =_fe .ST_TrueFalseBlankUnset ;
if _aee :=d .DecodeElement (&_fba .NoThreeD ,&_aeb );_aee !=nil {return _aee ;};case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"F\u0069\u0072\u0073\u0074\u0042\u0075\u0074\u0074\u006f\u006e"}:_fba .FirstButton =_fe .ST_TrueFalseBlankUnset ;
if _ega :=d .DecodeElement (&_fba .FirstButton ,&_aeb );_ega !=nil {return _ega ;};case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0046m\u006c\u0061\u0047\u0072\u006f\u0075p"}:_fba .FmlaGroup =new (string );
if _fcdc :=d .DecodeElement (_fba .FmlaGroup ,&_aeb );_fcdc !=nil {return _fcdc ;};case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0056\u0061\u006c"}:_fba .Val =new (int64 );
if _ccc :=d .DecodeElement (_fba .Val ,&_aeb );_ccc !=nil {return _ccc ;};case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u004d\u0069\u006e"}:_fba .Min =new (int64 );
if _cfd :=d .DecodeElement (_fba .Min ,&_aeb );_cfd !=nil {return _cfd ;};case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u004d\u0061\u0078"}:_fba .Max =new (int64 );
if _bce :=d .DecodeElement (_fba .Max ,&_aeb );_bce !=nil {return _bce ;};case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0049\u006e\u0063"}:_fba .Inc =new (int64 );
if _aeac :=d .DecodeElement (_fba .Inc ,&_aeb );_aeac !=nil {return _aeac ;};case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0050\u0061\u0067\u0065"}:_fba .Page =new (int64 );
if _gga :=d .DecodeElement (_fba .Page ,&_aeb );_gga !=nil {return _gga ;};case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0048\u006f\u0072i\u007a"}:_fba .Horiz =_fe .ST_TrueFalseBlankUnset ;
if _ffed :=d .DecodeElement (&_fba .Horiz ,&_aeb );_ffed !=nil {return _ffed ;};case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0044\u0078"}:_fba .Dx =new (int64 );
if _efb :=d .DecodeElement (_fba .Dx ,&_aeb );_efb !=nil {return _efb ;};case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u004d\u0061\u0070\u004f\u0043\u0058"}:_fba .MapOCX =_fe .ST_TrueFalseBlankUnset ;
if _cbf :=d .DecodeElement (&_fba .MapOCX ,&_aeb );_cbf !=nil {return _cbf ;};case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0043\u0046"}:_fba .CF =new (string );
if _ggbf :=d .DecodeElement (_fba .CF ,&_aeb );_ggbf !=nil {return _ggbf ;};case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0043\u0061\u006d\u0065\u0072\u0061"}:_fba .Camera =_fe .ST_TrueFalseBlankUnset ;
if _gfa :=d .DecodeElement (&_fba .Camera ,&_aeb );_gfa !=nil {return _gfa ;};case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0052\u0065\u0063a\u006c\u0063\u0041\u006c\u0077\u0061\u0079\u0073"}:_fba .RecalcAlways =_fe .ST_TrueFalseBlankUnset ;
if _fef :=d .DecodeElement (&_fba .RecalcAlways ,&_aeb );_fef !=nil {return _fef ;};case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0041u\u0074\u006f\u0053\u0063\u0061\u006ce"}:_fba .AutoScale =_fe .ST_TrueFalseBlankUnset ;
if _adf :=d .DecodeElement (&_fba .AutoScale ,&_aeb );_adf !=nil {return _adf ;};case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0044\u0044\u0045"}:_fba .DDE =_fe .ST_TrueFalseBlankUnset ;
if _bcd :=d .DecodeElement (&_fba .DDE ,&_aeb );_bcd !=nil {return _bcd ;};case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0055\u0049\u004fb\u006a"}:_fba .UIObj =_fe .ST_TrueFalseBlankUnset ;
if _dee :=d .DecodeElement (&_fba .UIObj ,&_aeb );_dee !=nil {return _dee ;};case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0053\u0063\u0072\u0069\u0070\u0074\u0054\u0065\u0078\u0074"}:_fba .ScriptText =new (string );
if _efba :=d .DecodeElement (_fba .ScriptText ,&_aeb );_efba !=nil {return _efba ;};case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0053\u0063\u0072\u0069\u0070\u0074\u0045\u0078\u0074e\u006e\u0064\u0065\u0064"}:_fba .ScriptExtended =new (string );
if _aecg :=d .DecodeElement (_fba .ScriptExtended ,&_aeb );_aecg !=nil {return _aecg ;};case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0053\u0063\u0072\u0069\u0070\u0074\u004c\u0061\u006eg\u0075\u0061\u0067\u0065"}:_fba .ScriptLanguage =new (uint32 );
if _bdbc :=d .DecodeElement (_fba .ScriptLanguage ,&_aeb );_bdbc !=nil {return _bdbc ;};case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0053\u0063\u0072\u0069\u0070\u0074\u004c\u006f\u0063a\u0074\u0069\u006f\u006e"}:_fba .ScriptLocation =new (uint32 );
if _fgca :=d .DecodeElement (_fba .ScriptLocation ,&_aeb );_fgca !=nil {return _fgca ;};case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0046\u006d\u006c\u0061\u0054\u0078\u0062\u0078"}:_fba .FmlaTxbx =new (string );
if _dgee :=d .DecodeElement (_fba .FmlaTxbx ,&_aeb );_dgee !=nil {return _dgee ;};default:_c .Log .Debug ("\u0073\u006b\u0069\u0070\u0070\u0069\u006e\u0067\u0020\u0075\u006e\u0073\u0075\u0070\u0070o\u0072\u0074\u0065\u0064\u0020\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020o\u006e\u0020\u0043\u0054\u005f\u0043\u006c\u0069\u0065\u006e\u0074\u0044at\u0061\u0043\u0068\u006f\u0069\u0063\u0065\u0020\u0025\u0076",_aeb .Name );
if _fdcd :=d .Skip ();_fdcd !=nil {return _fdcd ;};};return nil ;};func (_eabg ST_ObjectType )ValidateWithPath (path string )error {switch _eabg {case 0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19:default:return _f .Errorf ("\u0025s\u003a\u0020\u006f\u0075t\u0020\u006f\u0066\u0020\u0072a\u006eg\u0065 \u0076\u0061\u006c\u0075\u0065\u0020\u0025d",path ,int (_eabg ));
};return nil ;};type ST_ObjectType byte ;const (ST_ObjectTypeUnset ST_ObjectType =0;ST_ObjectTypeButton ST_ObjectType =1;ST_ObjectTypeCheckbox ST_ObjectType =2;ST_ObjectTypeDialog ST_ObjectType =3;ST_ObjectTypeDrop ST_ObjectType =4;ST_ObjectTypeEdit ST_ObjectType =5;
ST_ObjectTypeGBox ST_ObjectType =6;ST_ObjectTypeLabel ST_ObjectType =7;ST_ObjectTypeLineA ST_ObjectType =8;ST_ObjectTypeList ST_ObjectType =9;ST_ObjectTypeMovie ST_ObjectType =10;ST_ObjectTypeNote ST_ObjectType =11;ST_ObjectTypePict ST_ObjectType =12;ST_ObjectTypeRadio ST_ObjectType =13;
ST_ObjectTypeRectA ST_ObjectType =14;ST_ObjectTypeScroll ST_ObjectType =15;ST_ObjectTypeSpin ST_ObjectType =16;ST_ObjectTypeShape ST_ObjectType =17;ST_ObjectTypeGroup ST_ObjectType =18;ST_ObjectTypeRect ST_ObjectType =19;);func (_e *CT_ClientData )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {_ba ,_d :=_e .ObjectTypeAttr .MarshalXMLAttr (_g .Name {Local :"\u004f\u0062\u006a\u0065\u0063\u0074\u0054\u0079\u0070\u0065"});
if _d !=nil {return _d ;};start .Attr =append (start .Attr ,_ba );e .EncodeToken (start );if _e .ClientDataChoice !=nil {for _ ,_aef :=range _e .ClientDataChoice {_aef .MarshalXML (e ,_g .StartElement {});};};e .EncodeToken (_g .EndElement {Name :start .Name });
return nil ;};type CT_ClientData struct{

// Object type
ObjectTypeAttr ST_ObjectType ;ClientDataChoice []*CT_ClientDataChoice ;};func (_dgdg *ClientData )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {_dgdg .CT_ClientData =*NewCT_ClientData ();for _ ,_ggba :=range start .Attr {if _ggba .Name .Local =="\u004f\u0062\u006a\u0065\u0063\u0074\u0054\u0079\u0070\u0065"{_dgdg .ObjectTypeAttr .UnmarshalXMLAttr (_ggba );
continue ;};};_cgd :for {_gacb ,_dbdb :=d .Token ();if _dbdb !=nil {return _dbdb ;};switch _dbag :=_gacb .(type ){case _g .StartElement :switch _dbag .Name {case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u004d\u006f\u0076\u0065\u0057\u0069\u0074\u0068\u0043\u0065\u006c\u006c\u0073"}:_bgc :=NewCT_ClientDataChoice ();
if _cdab :=d .DecodeElement (&_bgc .MoveWithCells ,&_dbag );_cdab !=nil {return _cdab ;};_dgdg .ClientDataChoice =append (_dgdg .ClientDataChoice ,_bgc );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0053\u0069\u007a\u0065\u0057\u0069\u0074\u0068\u0043\u0065\u006c\u006c\u0073"}:_fccba :=NewCT_ClientDataChoice ();
if _ebfe :=d .DecodeElement (&_fccba .SizeWithCells ,&_dbag );_ebfe !=nil {return _ebfe ;};_dgdg .ClientDataChoice =append (_dgdg .ClientDataChoice ,_fccba );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0041\u006e\u0063\u0068\u006f\u0072"}:_ccaf :=NewCT_ClientDataChoice ();
if _agfb :=d .DecodeElement (&_ccaf .Anchor ,&_dbag );_agfb !=nil {return _agfb ;};_dgdg .ClientDataChoice =append (_dgdg .ClientDataChoice ,_ccaf );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u004c\u006f\u0063\u006b\u0065\u0064"}:_ggaf :=NewCT_ClientDataChoice ();
if _fdbe :=d .DecodeElement (&_ggaf .Locked ,&_dbag );_fdbe !=nil {return _fdbe ;};_dgdg .ClientDataChoice =append (_dgdg .ClientDataChoice ,_ggaf );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"D\u0065\u0066\u0061\u0075\u006c\u0074\u0053\u0069\u007a\u0065"}:_agba :=NewCT_ClientDataChoice ();
if _deb :=d .DecodeElement (&_agba .DefaultSize ,&_dbag );_deb !=nil {return _deb ;};_dgdg .ClientDataChoice =append (_dgdg .ClientDataChoice ,_agba );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"P\u0072\u0069\u006e\u0074\u004f\u0062\u006a\u0065\u0063\u0074"}:_edb :=NewCT_ClientDataChoice ();
if _dgb :=d .DecodeElement (&_edb .PrintObject ,&_dbag );_dgb !=nil {return _dgb ;};_dgdg .ClientDataChoice =append (_dgdg .ClientDataChoice ,_edb );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0044\u0069\u0073\u0061\u0062\u006c\u0065\u0064"}:_cdgbg :=NewCT_ClientDataChoice ();
if _ccfc :=d .DecodeElement (&_cdgbg .Disabled ,&_dbag );_ccfc !=nil {return _ccfc ;};_dgdg .ClientDataChoice =append (_dgdg .ClientDataChoice ,_cdgbg );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0041\u0075\u0074\u006f\u0046\u0069\u006c\u006c"}:_bdd :=NewCT_ClientDataChoice ();
if _ead :=d .DecodeElement (&_bdd .AutoFill ,&_dbag );_ead !=nil {return _ead ;};_dgdg .ClientDataChoice =append (_dgdg .ClientDataChoice ,_bdd );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0041\u0075\u0074\u006f\u004c\u0069\u006e\u0065"}:_cdb :=NewCT_ClientDataChoice ();
if _gfe :=d .DecodeElement (&_cdb .AutoLine ,&_dbag );_gfe !=nil {return _gfe ;};_dgdg .ClientDataChoice =append (_dgdg .ClientDataChoice ,_cdb );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0041\u0075\u0074\u006f\u0050\u0069\u0063\u0074"}:_abd :=NewCT_ClientDataChoice ();
if _bae :=d .DecodeElement (&_abd .AutoPict ,&_dbag );_bae !=nil {return _bae ;};_dgdg .ClientDataChoice =append (_dgdg .ClientDataChoice ,_abd );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0046m\u006c\u0061\u004d\u0061\u0063\u0072o"}:_adc :=NewCT_ClientDataChoice ();
if _cfae :=d .DecodeElement (&_adc .FmlaMacro ,&_dbag );_cfae !=nil {return _cfae ;};_dgdg .ClientDataChoice =append (_dgdg .ClientDataChoice ,_adc );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0054\u0065\u0078\u0074\u0048\u0041\u006c\u0069\u0067\u006e"}:_ecfc :=NewCT_ClientDataChoice ();
if _ebac :=d .DecodeElement (&_ecfc .TextHAlign ,&_dbag );_ebac !=nil {return _ebac ;};_dgdg .ClientDataChoice =append (_dgdg .ClientDataChoice ,_ecfc );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0054\u0065\u0078\u0074\u0056\u0041\u006c\u0069\u0067\u006e"}:_eaba :=NewCT_ClientDataChoice ();
if _bde :=d .DecodeElement (&_eaba .TextVAlign ,&_dbag );_bde !=nil {return _bde ;};_dgdg .ClientDataChoice =append (_dgdg .ClientDataChoice ,_eaba );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u004c\u006f\u0063\u006b\u0054\u0065\u0078\u0074"}:_bcf :=NewCT_ClientDataChoice ();
if _dfdd :=d .DecodeElement (&_bcf .LockText ,&_dbag );_dfdd !=nil {return _dfdd ;};_dgdg .ClientDataChoice =append (_dgdg .ClientDataChoice ,_bcf );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u004au\u0073\u0074\u004c\u0061\u0073\u0074X"}:_gde :=NewCT_ClientDataChoice ();
if _beda :=d .DecodeElement (&_gde .JustLastX ,&_dbag );_beda !=nil {return _beda ;};_dgdg .ClientDataChoice =append (_dgdg .ClientDataChoice ,_gde );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0053\u0065\u0063\u0072\u0065\u0074\u0045\u0064\u0069\u0074"}:_dcb :=NewCT_ClientDataChoice ();
if _fgd :=d .DecodeElement (&_dcb .SecretEdit ,&_dbag );_fgd !=nil {return _fgd ;};_dgdg .ClientDataChoice =append (_dgdg .ClientDataChoice ,_dcb );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0044e\u0066\u0061\u0075\u006c\u0074"}:_fdbec :=NewCT_ClientDataChoice ();
if _abbb :=d .DecodeElement (&_fdbec .Default ,&_dbag );_abbb !=nil {return _abbb ;};_dgdg .ClientDataChoice =append (_dgdg .ClientDataChoice ,_fdbec );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0048\u0065\u006c\u0070"}:_dege :=NewCT_ClientDataChoice ();
if _bcgac :=d .DecodeElement (&_dege .Help ,&_dbag );_bcgac !=nil {return _bcgac ;};_dgdg .ClientDataChoice =append (_dgdg .ClientDataChoice ,_dege );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0043\u0061\u006e\u0063\u0065\u006c"}:_bfbee :=NewCT_ClientDataChoice ();
if _ecca :=d .DecodeElement (&_bfbee .Cancel ,&_dbag );_ecca !=nil {return _ecca ;};_dgdg .ClientDataChoice =append (_dgdg .ClientDataChoice ,_bfbee );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0044i\u0073\u006d\u0069\u0073\u0073"}:_bdae :=NewCT_ClientDataChoice ();
if _deeg :=d .DecodeElement (&_bdae .Dismiss ,&_dbag );_deeg !=nil {return _deeg ;};_dgdg .ClientDataChoice =append (_dgdg .ClientDataChoice ,_bdae );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0041\u0063\u0063e\u006c"}:_dcbc :=NewCT_ClientDataChoice ();
if _bebc :=d .DecodeElement (&_dcbc .Accel ,&_dbag );_bebc !=nil {return _bebc ;};_dgdg .ClientDataChoice =append (_dgdg .ClientDataChoice ,_dcbc );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0041\u0063\u0063\u0065\u006c\u0032"}:_aed :=NewCT_ClientDataChoice ();
if _aced :=d .DecodeElement (&_aed .Accel2 ,&_dbag );_aced !=nil {return _aced ;};_dgdg .ClientDataChoice =append (_dgdg .ClientDataChoice ,_aed );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0052\u006f\u0077"}:_abea :=NewCT_ClientDataChoice ();
if _eff :=d .DecodeElement (&_abea .Row ,&_dbag );_eff !=nil {return _eff ;};_dgdg .ClientDataChoice =append (_dgdg .ClientDataChoice ,_abea );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0043\u006f\u006c\u0075\u006d\u006e"}:_dcfg :=NewCT_ClientDataChoice ();
if _bcgg :=d .DecodeElement (&_dcfg .Column ,&_dbag );_bcgg !=nil {return _bcgg ;};_dgdg .ClientDataChoice =append (_dgdg .ClientDataChoice ,_dcfg );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0056i\u0073\u0069\u0062\u006c\u0065"}:_agfg :=NewCT_ClientDataChoice ();
if _agbe :=d .DecodeElement (&_agfg .Visible ,&_dbag );_agbe !=nil {return _agbe ;};_dgdg .ClientDataChoice =append (_dgdg .ClientDataChoice ,_agfg );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0052o\u0077\u0048\u0069\u0064\u0064\u0065n"}:_ebg :=NewCT_ClientDataChoice ();
if _dgg :=d .DecodeElement (&_ebg .RowHidden ,&_dbag );_dgg !=nil {return _dgg ;};_dgdg .ClientDataChoice =append (_dgdg .ClientDataChoice ,_ebg );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0043o\u006c\u0048\u0069\u0064\u0064\u0065n"}:_dbfe :=NewCT_ClientDataChoice ();
if _dec :=d .DecodeElement (&_dbfe .ColHidden ,&_dbag );_dec !=nil {return _dec ;};_dgdg .ClientDataChoice =append (_dgdg .ClientDataChoice ,_dbfe );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0056\u0054\u0045\u0064\u0069\u0074"}:_eeaf :=NewCT_ClientDataChoice ();
if _bcdd :=d .DecodeElement (&_eeaf .VTEdit ,&_dbag );_bcdd !=nil {return _bcdd ;};_dgdg .ClientDataChoice =append (_dgdg .ClientDataChoice ,_eeaf );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u004du\u006c\u0074\u0069\u004c\u0069\u006ee"}:_geb :=NewCT_ClientDataChoice ();
if _fga :=d .DecodeElement (&_geb .MultiLine ,&_dbag );_fga !=nil {return _fga ;};_dgdg .ClientDataChoice =append (_dgdg .ClientDataChoice ,_geb );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0056S\u0063\u0072\u006f\u006c\u006c"}:_faa :=NewCT_ClientDataChoice ();
if _dgge :=d .DecodeElement (&_faa .VScroll ,&_dbag );_dgge !=nil {return _dgge ;};_dgdg .ClientDataChoice =append (_dgdg .ClientDataChoice ,_faa );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0056\u0061\u006c\u0069\u0064\u0049\u0064\u0073"}:_daae :=NewCT_ClientDataChoice ();
if _edcc :=d .DecodeElement (&_daae .ValidIds ,&_dbag );_edcc !=nil {return _edcc ;};_dgdg .ClientDataChoice =append (_dgdg .ClientDataChoice ,_daae );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0046m\u006c\u0061\u0052\u0061\u006e\u0067e"}:_bacc :=NewCT_ClientDataChoice ();
if _gbcd :=d .DecodeElement (&_bacc .FmlaRange ,&_dbag );_gbcd !=nil {return _gbcd ;};_dgdg .ClientDataChoice =append (_dgdg .ClientDataChoice ,_bacc );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0057\u0069\u0064\u0074\u0068\u004d\u0069\u006e"}:_aff :=NewCT_ClientDataChoice ();
if _egf :=d .DecodeElement (&_aff .WidthMin ,&_dbag );_egf !=nil {return _egf ;};_dgdg .ClientDataChoice =append (_dgdg .ClientDataChoice ,_aff );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0053\u0065\u006c"}:_efgf :=NewCT_ClientDataChoice ();
if _affa :=d .DecodeElement (&_efgf .Sel ,&_dbag );_affa !=nil {return _affa ;};_dgdg .ClientDataChoice =append (_dgdg .ClientDataChoice ,_efgf );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u004eo\u0054\u0068\u0072\u0065\u0065\u00442"}:_cfg :=NewCT_ClientDataChoice ();
if _edccc :=d .DecodeElement (&_cfg .NoThreeD2 ,&_dbag );_edccc !=nil {return _edccc ;};_dgdg .ClientDataChoice =append (_dgdg .ClientDataChoice ,_cfg );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0053e\u006c\u0054\u0079\u0070\u0065"}:_eca :=NewCT_ClientDataChoice ();
if _caba :=d .DecodeElement (&_eca .SelType ,&_dbag );_caba !=nil {return _caba ;};_dgdg .ClientDataChoice =append (_dgdg .ClientDataChoice ,_eca );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u004d\u0075\u006c\u0074\u0069\u0053\u0065\u006c"}:_dbfef :=NewCT_ClientDataChoice ();
if _faee :=d .DecodeElement (&_dbfef .MultiSel ,&_dbag );_faee !=nil {return _faee ;};_dgdg .ClientDataChoice =append (_dgdg .ClientDataChoice ,_dbfef );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u004c\u0043\u0054"}:_cceg :=NewCT_ClientDataChoice ();
if _dfba :=d .DecodeElement (&_cceg .LCT ,&_dbag );_dfba !=nil {return _dfba ;};_dgdg .ClientDataChoice =append (_dgdg .ClientDataChoice ,_cceg );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u004c\u0069\u0073\u0074\u0049\u0074\u0065\u006d"}:_beed :=NewCT_ClientDataChoice ();
if _bdfb :=d .DecodeElement (&_beed .ListItem ,&_dbag );_bdfb !=nil {return _bdfb ;};_dgdg .ClientDataChoice =append (_dgdg .ClientDataChoice ,_beed );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0044r\u006f\u0070\u0053\u0074\u0079\u006ce"}:_fgab :=NewCT_ClientDataChoice ();
if _cgg :=d .DecodeElement (&_fgab .DropStyle ,&_dbag );_cgg !=nil {return _cgg ;};_dgdg .ClientDataChoice =append (_dgdg .ClientDataChoice ,_fgab );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0043o\u006c\u006f\u0072\u0065\u0064"}:_egec :=NewCT_ClientDataChoice ();
if _bgd :=d .DecodeElement (&_egec .Colored ,&_dbag );_bgd !=nil {return _bgd ;};_dgdg .ClientDataChoice =append (_dgdg .ClientDataChoice ,_egec );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0044r\u006f\u0070\u004c\u0069\u006e\u0065s"}:_gaaa :=NewCT_ClientDataChoice ();
if _ffea :=d .DecodeElement (&_gaaa .DropLines ,&_dbag );_ffea !=nil {return _ffea ;};_dgdg .ClientDataChoice =append (_dgdg .ClientDataChoice ,_gaaa );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0043h\u0065\u0063\u006b\u0065\u0064"}:_gegg :=NewCT_ClientDataChoice ();
if _bdcb :=d .DecodeElement (&_gegg .Checked ,&_dbag );_bdcb !=nil {return _bdcb ;};_dgdg .ClientDataChoice =append (_dgdg .ClientDataChoice ,_gegg );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0046\u006d\u006c\u0061\u004c\u0069\u006e\u006b"}:_ecfb :=NewCT_ClientDataChoice ();
if _abee :=d .DecodeElement (&_ecfb .FmlaLink ,&_dbag );_abee !=nil {return _abee ;};_dgdg .ClientDataChoice =append (_dgdg .ClientDataChoice ,_ecfb );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0046\u006d\u006c\u0061\u0050\u0069\u0063\u0074"}:_aae :=NewCT_ClientDataChoice ();
if _dgbd :=d .DecodeElement (&_aae .FmlaPict ,&_dbag );_dgbd !=nil {return _dgbd ;};_dgdg .ClientDataChoice =append (_dgdg .ClientDataChoice ,_aae );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u004e\u006f\u0054\u0068\u0072\u0065\u0065\u0044"}:_gfee :=NewCT_ClientDataChoice ();
if _gag :=d .DecodeElement (&_gfee .NoThreeD ,&_dbag );_gag !=nil {return _gag ;};_dgdg .ClientDataChoice =append (_dgdg .ClientDataChoice ,_gfee );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"F\u0069\u0072\u0073\u0074\u0042\u0075\u0074\u0074\u006f\u006e"}:_egfa :=NewCT_ClientDataChoice ();
if _cfbg :=d .DecodeElement (&_egfa .FirstButton ,&_dbag );_cfbg !=nil {return _cfbg ;};_dgdg .ClientDataChoice =append (_dgdg .ClientDataChoice ,_egfa );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0046m\u006c\u0061\u0047\u0072\u006f\u0075p"}:_feg :=NewCT_ClientDataChoice ();
if _cgdf :=d .DecodeElement (&_feg .FmlaGroup ,&_dbag );_cgdf !=nil {return _cgdf ;};_dgdg .ClientDataChoice =append (_dgdg .ClientDataChoice ,_feg );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0056\u0061\u006c"}:_feb :=NewCT_ClientDataChoice ();
if _gbef :=d .DecodeElement (&_feb .Val ,&_dbag );_gbef !=nil {return _gbef ;};_dgdg .ClientDataChoice =append (_dgdg .ClientDataChoice ,_feb );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u004d\u0069\u006e"}:_fbf :=NewCT_ClientDataChoice ();
if _ccd :=d .DecodeElement (&_fbf .Min ,&_dbag );_ccd !=nil {return _ccd ;};_dgdg .ClientDataChoice =append (_dgdg .ClientDataChoice ,_fbf );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u004d\u0061\u0078"}:_abf :=NewCT_ClientDataChoice ();
if _eecc :=d .DecodeElement (&_abf .Max ,&_dbag );_eecc !=nil {return _eecc ;};_dgdg .ClientDataChoice =append (_dgdg .ClientDataChoice ,_abf );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0049\u006e\u0063"}:_ffcf :=NewCT_ClientDataChoice ();
if _edeb :=d .DecodeElement (&_ffcf .Inc ,&_dbag );_edeb !=nil {return _edeb ;};_dgdg .ClientDataChoice =append (_dgdg .ClientDataChoice ,_ffcf );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0050\u0061\u0067\u0065"}:_fbfg :=NewCT_ClientDataChoice ();
if _bcdb :=d .DecodeElement (&_fbfg .Page ,&_dbag );_bcdb !=nil {return _bcdb ;};_dgdg .ClientDataChoice =append (_dgdg .ClientDataChoice ,_fbfg );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0048\u006f\u0072i\u007a"}:_bccb :=NewCT_ClientDataChoice ();
if _egd :=d .DecodeElement (&_bccb .Horiz ,&_dbag );_egd !=nil {return _egd ;};_dgdg .ClientDataChoice =append (_dgdg .ClientDataChoice ,_bccb );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0044\u0078"}:_facbf :=NewCT_ClientDataChoice ();
if _ebed :=d .DecodeElement (&_facbf .Dx ,&_dbag );_ebed !=nil {return _ebed ;};_dgdg .ClientDataChoice =append (_dgdg .ClientDataChoice ,_facbf );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u004d\u0061\u0070\u004f\u0043\u0058"}:_badd :=NewCT_ClientDataChoice ();
if _gab :=d .DecodeElement (&_badd .MapOCX ,&_dbag );_gab !=nil {return _gab ;};_dgdg .ClientDataChoice =append (_dgdg .ClientDataChoice ,_badd );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0043\u0046"}:_agd :=NewCT_ClientDataChoice ();
if _gdgg :=d .DecodeElement (&_agd .CF ,&_dbag );_gdgg !=nil {return _gdgg ;};_dgdg .ClientDataChoice =append (_dgdg .ClientDataChoice ,_agd );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0043\u0061\u006d\u0065\u0072\u0061"}:_dfe :=NewCT_ClientDataChoice ();
if _ebd :=d .DecodeElement (&_dfe .Camera ,&_dbag );_ebd !=nil {return _ebd ;};_dgdg .ClientDataChoice =append (_dgdg .ClientDataChoice ,_dfe );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0052\u0065\u0063a\u006c\u0063\u0041\u006c\u0077\u0061\u0079\u0073"}:_decg :=NewCT_ClientDataChoice ();
if _gegd :=d .DecodeElement (&_decg .RecalcAlways ,&_dbag );_gegd !=nil {return _gegd ;};_dgdg .ClientDataChoice =append (_dgdg .ClientDataChoice ,_decg );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0041u\u0074\u006f\u0053\u0063\u0061\u006ce"}:_ggbgf :=NewCT_ClientDataChoice ();
if _aggc :=d .DecodeElement (&_ggbgf .AutoScale ,&_dbag );_aggc !=nil {return _aggc ;};_dgdg .ClientDataChoice =append (_dgdg .ClientDataChoice ,_ggbgf );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0044\u0044\u0045"}:_ccag :=NewCT_ClientDataChoice ();
if _daaf :=d .DecodeElement (&_ccag .DDE ,&_dbag );_daaf !=nil {return _daaf ;};_dgdg .ClientDataChoice =append (_dgdg .ClientDataChoice ,_ccag );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0055\u0049\u004fb\u006a"}:_bffg :=NewCT_ClientDataChoice ();
if _agfc :=d .DecodeElement (&_bffg .UIObj ,&_dbag );_agfc !=nil {return _agfc ;};_dgdg .ClientDataChoice =append (_dgdg .ClientDataChoice ,_bffg );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0053\u0063\u0072\u0069\u0070\u0074\u0054\u0065\u0078\u0074"}:_agdc :=NewCT_ClientDataChoice ();
if _fggb :=d .DecodeElement (&_agdc .ScriptText ,&_dbag );_fggb !=nil {return _fggb ;};_dgdg .ClientDataChoice =append (_dgdg .ClientDataChoice ,_agdc );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0053\u0063\u0072\u0069\u0070\u0074\u0045\u0078\u0074e\u006e\u0064\u0065\u0064"}:_aca :=NewCT_ClientDataChoice ();
if _ggc :=d .DecodeElement (&_aca .ScriptExtended ,&_dbag );_ggc !=nil {return _ggc ;};_dgdg .ClientDataChoice =append (_dgdg .ClientDataChoice ,_aca );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0053\u0063\u0072\u0069\u0070\u0074\u004c\u0061\u006eg\u0075\u0061\u0067\u0065"}:_eae :=NewCT_ClientDataChoice ();
if _gdf :=d .DecodeElement (&_eae .ScriptLanguage ,&_dbag );_gdf !=nil {return _gdf ;};_dgdg .ClientDataChoice =append (_dgdg .ClientDataChoice ,_eae );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0053\u0063\u0072\u0069\u0070\u0074\u004c\u006f\u0063a\u0074\u0069\u006f\u006e"}:_aeeb :=NewCT_ClientDataChoice ();
if _aeba :=d .DecodeElement (&_aeeb .ScriptLocation ,&_dbag );_aeba !=nil {return _aeba ;};_dgdg .ClientDataChoice =append (_dgdg .ClientDataChoice ,_aeeb );case _g .Name {Space :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c",Local :"\u0046\u006d\u006c\u0061\u0054\u0078\u0062\u0078"}:_egdf :=NewCT_ClientDataChoice ();
if _gggd :=d .DecodeElement (&_egdf .FmlaTxbx ,&_dbag );_gggd !=nil {return _gggd ;};_dgdg .ClientDataChoice =append (_dgdg .ClientDataChoice ,_egdf );default:_c .Log .Debug ("\u0073k\u0069\u0070p\u0069\u006e\u0067 \u0075\u006e\u0073\u0075\u0070\u0070\u006fr\u0074\u0065\u0064\u0020\u0065\u006ce\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0043\u006c\u0069e\u006e\u0074\u0044\u0061\u0074\u0061\u0020\u0025\u0076",_dbag .Name );
if _fdga :=d .Skip ();_fdga !=nil {return _fdga ;};};case _g .EndElement :break _cgd ;case _g .CharData :};};return nil ;};

// ValidateWithPath validates the CT_ClientDataChoice and its children, prefixing error messages with path
func (_bbce *CT_ClientDataChoice )ValidateWithPath (path string )error {if _fagc :=_bbce .MoveWithCells .ValidateWithPath (path +"\u002f\u004d\u006f\u0076\u0065\u0057\u0069\u0074\u0068C\u0065\u006c\u006c\u0073");_fagc !=nil {return _fagc ;};if _ddc :=_bbce .SizeWithCells .ValidateWithPath (path +"\u002f\u0053\u0069\u007a\u0065\u0057\u0069\u0074\u0068C\u0065\u006c\u006c\u0073");
_ddc !=nil {return _ddc ;};if _dbe :=_bbce .Locked .ValidateWithPath (path +"\u002fL\u006f\u0063\u006b\u0065\u0064");_dbe !=nil {return _dbe ;};if _aga :=_bbce .DefaultSize .ValidateWithPath (path +"\u002f\u0044\u0065f\u0061\u0075\u006c\u0074\u0053\u0069\u007a\u0065");
_aga !=nil {return _aga ;};if _cad :=_bbce .PrintObject .ValidateWithPath (path +"\u002f\u0050\u0072i\u006e\u0074\u004f\u0062\u006a\u0065\u0063\u0074");_cad !=nil {return _cad ;};if _bdcg :=_bbce .Disabled .ValidateWithPath (path +"\u002fD\u0069\u0073\u0061\u0062\u006c\u0065d");
_bdcg !=nil {return _bdcg ;};if _gcg :=_bbce .AutoFill .ValidateWithPath (path +"\u002fA\u0075\u0074\u006f\u0046\u0069\u006cl");_gcg !=nil {return _gcg ;};if _bedb :=_bbce .AutoLine .ValidateWithPath (path +"\u002fA\u0075\u0074\u006f\u004c\u0069\u006ee");
_bedb !=nil {return _bedb ;};if _bge :=_bbce .AutoPict .ValidateWithPath (path +"\u002fA\u0075\u0074\u006f\u0050\u0069\u0063t");_bge !=nil {return _bge ;};if _dbd :=_bbce .LockText .ValidateWithPath (path +"\u002fL\u006f\u0063\u006b\u0054\u0065\u0078t");
_dbd !=nil {return _dbd ;};if _dfaa :=_bbce .JustLastX .ValidateWithPath (path +"\u002f\u004a\u0075\u0073\u0074\u004c\u0061\u0073\u0074\u0058");_dfaa !=nil {return _dfaa ;};if _bea :=_bbce .SecretEdit .ValidateWithPath (path +"/\u0053\u0065\u0063\u0072\u0065\u0074\u0045\u0064\u0069\u0074");
_bea !=nil {return _bea ;};if _gbf :=_bbce .Default .ValidateWithPath (path +"\u002f\u0044\u0065\u0066\u0061\u0075\u006c\u0074");_gbf !=nil {return _gbf ;};if _bbe :=_bbce .Help .ValidateWithPath (path +"\u002f\u0048\u0065l\u0070");_bbe !=nil {return _bbe ;
};if _bagd :=_bbce .Cancel .ValidateWithPath (path +"\u002fC\u0061\u006e\u0063\u0065\u006c");_bagd !=nil {return _bagd ;};if _gggg :=_bbce .Dismiss .ValidateWithPath (path +"\u002f\u0044\u0069\u0073\u006d\u0069\u0073\u0073");_gggg !=nil {return _gggg ;
};if _fbd :=_bbce .Visible .ValidateWithPath (path +"\u002f\u0056\u0069\u0073\u0069\u0062\u006c\u0065");_fbd !=nil {return _fbd ;};if _bagc :=_bbce .RowHidden .ValidateWithPath (path +"\u002f\u0052\u006f\u0077\u0048\u0069\u0064\u0064\u0065\u006e");_bagc !=nil {return _bagc ;
};if _ccfa :=_bbce .ColHidden .ValidateWithPath (path +"\u002f\u0043\u006f\u006c\u0048\u0069\u0064\u0064\u0065\u006e");_ccfa !=nil {return _ccfa ;};if _edg :=_bbce .MultiLine .ValidateWithPath (path +"\u002f\u004d\u0075\u006c\u0074\u0069\u004c\u0069\u006e\u0065");
_edg !=nil {return _edg ;};if _agad :=_bbce .VScroll .ValidateWithPath (path +"\u002f\u0056\u0053\u0063\u0072\u006f\u006c\u006c");_agad !=nil {return _agad ;};if _dfb :=_bbce .ValidIds .ValidateWithPath (path +"\u002fV\u0061\u006c\u0069\u0064\u0049\u0064s");
_dfb !=nil {return _dfb ;};if _fbba :=_bbce .NoThreeD2 .ValidateWithPath (path +"\u002f\u004e\u006f\u0054\u0068\u0072\u0065\u0065\u0044\u0032");_fbba !=nil {return _fbba ;};if _egef :=_bbce .Colored .ValidateWithPath (path +"\u002f\u0043\u006f\u006c\u006f\u0072\u0065\u0064");
_egef !=nil {return _egef ;};if _gbcb :=_bbce .NoThreeD .ValidateWithPath (path +"\u002fN\u006f\u0054\u0068\u0072\u0065\u0065D");_gbcb !=nil {return _gbcb ;};if _ggbg :=_bbce .FirstButton .ValidateWithPath (path +"\u002f\u0046\u0069r\u0073\u0074\u0042\u0075\u0074\u0074\u006f\u006e");
_ggbg !=nil {return _ggbg ;};if _geeg :=_bbce .Horiz .ValidateWithPath (path +"\u002f\u0048\u006f\u0072\u0069\u007a");_geeg !=nil {return _geeg ;};if _ggf :=_bbce .MapOCX .ValidateWithPath (path +"\u002fM\u0061\u0070\u004f\u0043\u0058");_ggf !=nil {return _ggf ;
};if _bccfc :=_bbce .Camera .ValidateWithPath (path +"\u002fC\u0061\u006d\u0065\u0072\u0061");_bccfc !=nil {return _bccfc ;};if _bdcc :=_bbce .RecalcAlways .ValidateWithPath (path +"\u002f\u0052\u0065\u0063\u0061\u006c\u0063\u0041\u006c\u0077\u0061\u0079\u0073");
_bdcc !=nil {return _bdcc ;};if _fdd :=_bbce .AutoScale .ValidateWithPath (path +"\u002f\u0041\u0075\u0074\u006f\u0053\u0063\u0061\u006c\u0065");_fdd !=nil {return _fdd ;};if _ade :=_bbce .DDE .ValidateWithPath (path +"\u002f\u0044\u0044\u0045");_ade !=nil {return _ade ;
};if _bbb :=_bbce .UIObj .ValidateWithPath (path +"\u002f\u0055\u0049\u004f\u0062\u006a");_bbb !=nil {return _bbb ;};return nil ;};type CT_ClientDataChoice struct{

// Move with Cells
MoveWithCells _fe .ST_TrueFalseBlank ;

// Resize with Cells
SizeWithCells _fe .ST_TrueFalseBlank ;

// Anchor
Anchor *string ;

// Lock Toggle
Locked _fe .ST_TrueFalseBlank ;

// Default Size Toggle
DefaultSize _fe .ST_TrueFalseBlank ;

// Print Toggle
PrintObject _fe .ST_TrueFalseBlank ;

// Macro Disable Toggle
Disabled _fe .ST_TrueFalseBlank ;

// AutoFill
AutoFill _fe .ST_TrueFalseBlank ;

// AutoLine
AutoLine _fe .ST_TrueFalseBlank ;

// Automatically Size
AutoPict _fe .ST_TrueFalseBlank ;

// Reference to Custom Function
FmlaMacro *string ;

// Horizontal Text Alignment
TextHAlign *string ;

// Vertical Text Alignment
TextVAlign *string ;

// Text Lock
LockText _fe .ST_TrueFalseBlank ;

// Far East Alignment Toggle
JustLastX _fe .ST_TrueFalseBlank ;

// Password Edit
SecretEdit _fe .ST_TrueFalseBlank ;

// Default Button
Default _fe .ST_TrueFalseBlank ;

// Help Button
Help _fe .ST_TrueFalseBlank ;

// Cancel Button
Cancel _fe .ST_TrueFalseBlank ;

// Dismiss Button
Dismiss _fe .ST_TrueFalseBlank ;

// Primary Keyboard Accelerator
Accel *int64 ;

// Secondary Keyboard Accelerator
Accel2 *int64 ;

// Comment Row Target
Row *int64 ;

// Comment Column Target
Column *int64 ;

// Comment Visibility Toggle
Visible _fe .ST_TrueFalseBlank ;

// Comment's Row is Hidden
RowHidden _fe .ST_TrueFalseBlank ;

// Comment's Column is Hidden
ColHidden _fe .ST_TrueFalseBlank ;

// Validation Type
VTEdit *int64 ;

// Multi-line
MultiLine _fe .ST_TrueFalseBlank ;

// Vertical Scroll
VScroll _fe .ST_TrueFalseBlank ;

// Valid ID
ValidIds _fe .ST_TrueFalseBlank ;

// List Items Source Range
FmlaRange *string ;

// Minimum Width
WidthMin *int64 ;

// Selected Entry
Sel *int64 ;

// Disable 3D
NoThreeD2 _fe .ST_TrueFalseBlank ;

// Selection Type
SelType *string ;

// Multiple Selections
MultiSel *string ;

// Callback Type
LCT *string ;

// Non-linked List Item
ListItem *string ;

// Dropdown Style
DropStyle *string ;

// Dropdown Color Toggle
Colored _fe .ST_TrueFalseBlank ;

// Dropdown Maximum Lines
DropLines *int64 ;

// Checked
Checked *int64 ;

// Linked Formula
FmlaLink *string ;

// Camera Source Range
FmlaPict *string ;

// Disable 3D
NoThreeD _fe .ST_TrueFalseBlank ;

// First Radio Button
FirstButton _fe .ST_TrueFalseBlank ;

// Linked Formula - Group Box
FmlaGroup *string ;

// Scroll bar position
Val *int64 ;

// Scroll Bar Minimum
Min *int64 ;

// Scroll Bar Maximum
Max *int64 ;

// Scroll Bar Increment
Inc *int64 ;

// Scroll Bar Page Increment
Page *int64 ;

// Scroll Bar Orientation
Horiz _fe .ST_TrueFalseBlank ;

// Scroll Bar Width
Dx *int64 ;

// Embedded Control
MapOCX _fe .ST_TrueFalseBlank ;

// Clipboard Format
CF *string ;

// Camera Tool
Camera _fe .ST_TrueFalseBlank ;

// Recalculation Toggle
RecalcAlways _fe .ST_TrueFalseBlank ;

// Font AutoScale
AutoScale _fe .ST_TrueFalseBlank ;

// Dynamic Data Exchange
DDE _fe .ST_TrueFalseBlank ;

// UI Object Toggle
UIObj _fe .ST_TrueFalseBlank ;

// HTML Script Text
ScriptText *string ;

// HTML Script Attributes
ScriptExtended *string ;

// HTML Script Language
ScriptLanguage *uint32 ;

// HTML Script Location
ScriptLocation *uint32 ;

// Text Formula
FmlaTxbx *string ;};func (_dgeg ST_ObjectType )String ()string {switch _dgeg {case 0:return "";case 1:return "\u0042\u0075\u0074\u0074\u006f\u006e";case 2:return "\u0043\u0068\u0065\u0063\u006b\u0062\u006f\u0078";case 3:return "\u0044\u0069\u0061\u006c\u006f\u0067";
case 4:return "\u0044\u0072\u006f\u0070";case 5:return "\u0045\u0064\u0069\u0074";case 6:return "\u0047\u0042\u006f\u0078";case 7:return "\u004c\u0061\u0062e\u006c";case 8:return "\u004c\u0069\u006ee\u0041";case 9:return "\u004c\u0069\u0073\u0074";case 10:return "\u004d\u006f\u0076i\u0065";
case 11:return "\u004e\u006f\u0074\u0065";case 12:return "\u0050\u0069\u0063\u0074";case 13:return "\u0052\u0061\u0064i\u006f";case 14:return "\u0052\u0065\u0063t\u0041";case 15:return "\u0053\u0063\u0072\u006f\u006c\u006c";case 16:return "\u0053\u0070\u0069\u006e";
case 17:return "\u0053\u0068\u0061p\u0065";case 18:return "\u0047\u0072\u006fu\u0070";case 19:return "\u0052\u0065\u0063\u0074";};return "";};

// Validate validates the ClientData and its children
func (_gad *ClientData )Validate ()error {return _gad .ValidateWithPath ("\u0043\u006c\u0069\u0065\u006e\u0074\u0044\u0061\u0074\u0061");};func (_ffaa ST_ObjectType )MarshalXMLAttr (name _g .Name )(_g .Attr ,error ){_acfc :=_g .Attr {};_acfc .Name =name ;
switch _ffaa {case ST_ObjectTypeUnset :_acfc .Value ="";case ST_ObjectTypeButton :_acfc .Value ="\u0042\u0075\u0074\u0074\u006f\u006e";case ST_ObjectTypeCheckbox :_acfc .Value ="\u0043\u0068\u0065\u0063\u006b\u0062\u006f\u0078";case ST_ObjectTypeDialog :_acfc .Value ="\u0044\u0069\u0061\u006c\u006f\u0067";
case ST_ObjectTypeDrop :_acfc .Value ="\u0044\u0072\u006f\u0070";case ST_ObjectTypeEdit :_acfc .Value ="\u0045\u0064\u0069\u0074";case ST_ObjectTypeGBox :_acfc .Value ="\u0047\u0042\u006f\u0078";case ST_ObjectTypeLabel :_acfc .Value ="\u004c\u0061\u0062e\u006c";
case ST_ObjectTypeLineA :_acfc .Value ="\u004c\u0069\u006ee\u0041";case ST_ObjectTypeList :_acfc .Value ="\u004c\u0069\u0073\u0074";case ST_ObjectTypeMovie :_acfc .Value ="\u004d\u006f\u0076i\u0065";case ST_ObjectTypeNote :_acfc .Value ="\u004e\u006f\u0074\u0065";
case ST_ObjectTypePict :_acfc .Value ="\u0050\u0069\u0063\u0074";case ST_ObjectTypeRadio :_acfc .Value ="\u0052\u0061\u0064i\u006f";case ST_ObjectTypeRectA :_acfc .Value ="\u0052\u0065\u0063t\u0041";case ST_ObjectTypeScroll :_acfc .Value ="\u0053\u0063\u0072\u006f\u006c\u006c";
case ST_ObjectTypeSpin :_acfc .Value ="\u0053\u0070\u0069\u006e";case ST_ObjectTypeShape :_acfc .Value ="\u0053\u0068\u0061p\u0065";case ST_ObjectTypeGroup :_acfc .Value ="\u0047\u0072\u006fu\u0070";case ST_ObjectTypeRect :_acfc .Value ="\u0052\u0065\u0063\u0074";
};return _acfc ,nil ;};

// Validate validates the CT_ClientData and its children
func (_cabb *CT_ClientData )Validate ()error {return _cabb .ValidateWithPath ("\u0043\u0054\u005f\u0043\u006c\u0069\u0065\u006e\u0074\u0044\u0061\u0074\u0061");};

// ValidateWithPath validates the CT_ClientData and its children, prefixing error messages with path
func (_afc *CT_ClientData )ValidateWithPath (path string )error {if _afc .ObjectTypeAttr ==ST_ObjectTypeUnset {return _f .Errorf ("\u0025\u0073\u002f\u004f\u0062\u006a\u0065\u0063\u0074\u0054\u0079\u0070\u0065\u0041\u0074\u0074\u0072\u0020\u0069\u0073\u0020\u0061\u0020\u006da\u006e\u0064\u0061\u0074\u006fr\u0079\u0020f\u0069\u0065\u006c\u0064",path );
};if _efd :=_afc .ObjectTypeAttr .ValidateWithPath (path +"\u002fO\u0062j\u0065\u0063\u0074\u0054\u0079\u0070\u0065\u0041\u0074\u0074\u0072");_efd !=nil {return _efd ;};for _fac ,_ddec :=range _afc .ClientDataChoice {if _agg :=_ddec .ValidateWithPath (_f .Sprintf ("\u0025\u0073\u002fCl\u0069\u0065\u006e\u0074\u0044\u0061\u0074\u0061\u0043\u0068\u006f\u0069\u0063\u0065\u005b\u0025\u0064\u005d",path ,_fac ));
_agg !=nil {return _agg ;};};return nil ;};func (_egfb ST_ObjectType )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {return e .EncodeElement (_egfb .String (),start );};func (_bdgae *ClientData )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {start .Attr =append (start .Attr ,_g .Attr {Name :_g .Name {Local :"\u0078\u006d\u006cn\u0073"},Value :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c"});
start .Attr =append (start .Attr ,_g .Attr {Name :_g .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0078"},Value :"\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c"});
start .Attr =append (start .Attr ,_g .Attr {Name :_g .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0078\u006dl"},Value :"\u0068\u0074tp\u003a\u002f\u002fw\u0077\u0077\u002e\u00773.o\u0072g/\u0058\u004d\u004c\u002f\u0031\u0039\u00398/\u006e\u0061\u006d\u0065\u0073\u0070\u0061c\u0065"});
start .Name .Local ="\u0078\u003a\u0043l\u0069\u0065\u006e\u0074\u0044\u0061\u0074\u0061";return _bdgae .CT_ClientData .MarshalXML (e ,start );};func NewClientData ()*ClientData {_gbbd :=&ClientData {};_gbbd .CT_ClientData =*NewCT_ClientData ();return _gbbd ;
};func (_dff ST_ObjectType )Validate ()error {return _dff .ValidateWithPath ("")};

// Validate validates the CT_ClientDataChoice and its children
func (_ebf *CT_ClientDataChoice )Validate ()error {return _ebf .ValidateWithPath ("\u0043\u0054\u005f\u0043li\u0065\u006e\u0074\u0044\u0061\u0074\u0061\u0043\u0068\u006f\u0069\u0063\u0065");};

// ValidateWithPath validates the ClientData and its children, prefixing error messages with path
func (_dbgb *ClientData )ValidateWithPath (path string )error {if _dggd :=_dbgb .CT_ClientData .ValidateWithPath (path );_dggd !=nil {return _dggd ;};return nil ;};func init (){_b .RegisterConstructor ("\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c","\u0043\u0054\u005f\u0043\u006c\u0069\u0065\u006e\u0074\u0044\u0061\u0074\u0061",NewCT_ClientData );
_b .RegisterConstructor ("\u0075\u0072\u006e\u003a\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002d\u0063\u006fm\u003a\u006f\u0066\u0066\u0069c\u0065\u003ae\u0078\u0063\u0065\u006c","\u0043\u006c\u0069\u0065\u006e\u0074\u0044\u0061\u0074\u0061",NewClientData );
};