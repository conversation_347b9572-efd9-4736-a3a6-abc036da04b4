//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package activeX ;import (_f "encoding/xml";_g "fmt";_a "github.com/unidoc/unioffice/v2";_d "github.com/unidoc/unioffice/v2/common/logger";);

// Validate validates the CT_Ocx and its children
func (_ecb *CT_Ocx )Validate ()error {return _ecb .ValidateWithPath ("\u0043\u0054\u005f\u004f\u0063\u0078");};

// ValidateWithPath validates the CT_Ocx and its children, prefixing error messages with path
func (_ecc *CT_Ocx )ValidateWithPath (path string )error {if _ecc .PersistenceAttr ==ST_PersistenceUnset {return _g .Errorf ("\u0025\u0073\u002f\u0050\u0065\u0072\u0073\u0069\u0073\u0074\u0065\u006e\u0063e\u0041\u0074\u0074\u0072\u0020\u0069s\u0020\u0061\u0020\u006d\u0061\u006e\u0064\u0061\u0074\u006f\u0072\u0079\u0020f\u0069\u0065\u006c\u0064",path );
};if _ea :=_ecc .PersistenceAttr .ValidateWithPath (path +"\u002f\u0050e\u0072\u0073\u0069s\u0074\u0065\u006e\u0063\u0065\u0041\u0074\u0074\u0072");_ea !=nil {return _ea ;};for _fc ,_ddg :=range _ecc .OcxPr {if _cf :=_ddg .ValidateWithPath (_g .Sprintf ("\u0025\u0073\u002fO\u0063\u0078\u0050\u0072\u005b\u0025\u0064\u005d",path ,_fc ));
_cf !=nil {return _cf ;};};return nil ;};func (_cae *CT_Ocx )UnmarshalXML (d *_f .Decoder ,start _f .StartElement )error {_cae .PersistenceAttr =ST_Persistence (1);for _ ,_bf :=range start .Attr {if _bf .Name .Space =="\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069c\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002fr\u0065\u006c\u0061\u0074\u0069\u006f\u006e\u0073h\u0069\u0070\u0073"&&_bf .Name .Local =="\u0069\u0064"||_bf .Name .Space =="\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fof\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0072\u0065\u006c\u0061\u0074\u0069\u006f\u006e\u0073\u0068\u0069\u0070\u0073"&&_bf .Name .Local =="\u0069\u0064"{_eg :=_bf .Value ;
_cae .IdAttr =&_eg ;continue ;};if _bf .Name .Local =="\u0063l\u0061\u0073\u0073\u0069\u0064"{_ag :=_bf .Value ;_cae .ClassidAttr =_ag ;continue ;};if _bf .Name .Local =="\u006ci\u0063\u0065\u006e\u0073\u0065"{_adb :=_bf .Value ;_cae .LicenseAttr =&_adb ;
continue ;};if _bf .Name .Local =="p\u0065\u0072\u0073\u0069\u0073\u0074\u0065\u006e\u0063\u0065"{_cae .PersistenceAttr .UnmarshalXMLAttr (_bf );continue ;};};_fae :for {_gb ,_ff :=d .Token ();if _ff !=nil {return _ff ;};switch _aa :=_gb .(type ){case _f .StartElement :switch _aa .Name {case _f .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006das\u002e\u006d\u0069\u0063\u0072\u006fs\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f2\u0030\u0030\u0036\u002f\u0061\u0063\u0074\u0069v\u0065\u0058",Local :"\u006f\u0063\u0078P\u0072"}:_cg :=NewCT_OcxPr ();
if _gbb :=d .DecodeElement (_cg ,&_aa );_gbb !=nil {return _gbb ;};_cae .OcxPr =append (_cae .OcxPr ,_cg );default:_d .Log .Debug ("\u0073\u006b\u0069\u0070\u0070i\u006e\u0067\u0020\u0075\u006e\u0073\u0075\u0070\u0070\u006f\u0072\u0074\u0065d\u0020\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0043\u0054\u005f\u004f\u0063\u0078\u0020\u0025\u0076",_aa .Name );
if _feb :=d .Skip ();_feb !=nil {return _feb ;};};case _f .EndElement :break _fae ;case _f .CharData :};};return nil ;};type CT_Ocx struct{ClassidAttr string ;LicenseAttr *string ;IdAttr *string ;PersistenceAttr ST_Persistence ;OcxPr []*CT_OcxPr ;};

// Validate validates the CT_Font and its children
func (_bgg *CT_Font )Validate ()error {return _bgg .ValidateWithPath ("\u0043T\u005f\u0046\u006f\u006e\u0074");};

// ValidateWithPath validates the CT_OcxPr and its children, prefixing error messages with path
func (_acf *CT_OcxPr )ValidateWithPath (path string )error {if _fd :=_acf .OcxPrChoice .ValidateWithPath (path +"\u002f\u004f\u0063x\u0050\u0072\u0043\u0068\u006f\u0069\u0063\u0065");_fd !=nil {return _fd ;};return nil ;};func NewCT_OcxPr ()*CT_OcxPr {_gd :=&CT_OcxPr {};
_gd .OcxPrChoice =NewCT_OcxPrChoice ();return _gd };

// ValidateWithPath validates the CT_OcxPrChoice and its children, prefixing error messages with path
func (_ab *CT_OcxPrChoice )ValidateWithPath (path string )error {if _ab .Font !=nil {if _gfd :=_ab .Font .ValidateWithPath (path +"\u002f\u0046\u006fn\u0074");_gfd !=nil {return _gfd ;};};if _ab .Picture !=nil {if _febe :=_ab .Picture .ValidateWithPath (path +"\u002f\u0050\u0069\u0063\u0074\u0075\u0072\u0065");
_febe !=nil {return _febe ;};};return nil ;};func (_fa *CT_Font )MarshalXML (e *_f .Encoder ,start _f .StartElement )error {if _fa .PersistenceAttr !=ST_PersistenceUnset {_dd ,_c :=_fa .PersistenceAttr .MarshalXMLAttr (_f .Name {Local :"\u0061\u0078\u003a\u0070\u0065\u0072\u0073\u0069\u0073t\u0065\u006e\u0063\u0065"});
if _c !=nil {return _c ;};start .Attr =append (start .Attr ,_dd );};if _fa .IdAttr !=nil {start .Attr =append (start .Attr ,_f .Attr {Name :_f .Name {Local :"\u0072\u003a\u0069\u0064"},Value :_g .Sprintf ("\u0025\u0076",*_fa .IdAttr )});};e .EncodeToken (start );
if _fa .OcxPr !=nil {_fac :=_f .StartElement {Name :_f .Name {Local :"\u0061\u0078\u003a\u006f\u0063\u0078\u0050\u0072"}};for _ ,_ad :=range _fa .OcxPr {e .EncodeElement (_ad ,_fac );};};e .EncodeToken (_f .EndElement {Name :start .Name });return nil ;
};type CT_Font struct{PersistenceAttr ST_Persistence ;IdAttr *string ;OcxPr []*CT_OcxPr ;};

// Validate validates the CT_Picture and its children
func (_ge *CT_Picture )Validate ()error {return _ge .ValidateWithPath ("\u0043\u0054\u005f\u0050\u0069\u0063\u0074\u0075\u0072\u0065");};

// ValidateWithPath validates the CT_Picture and its children, prefixing error messages with path
func (_bcd *CT_Picture )ValidateWithPath (path string )error {return nil };func NewCT_Picture ()*CT_Picture {_cbf :=&CT_Picture {};return _cbf };func (_af *CT_OcxPr )UnmarshalXML (d *_f .Decoder ,start _f .StartElement )error {_af .OcxPrChoice =NewCT_OcxPrChoice ();
for _ ,_caeb :=range start .Attr {if _caeb .Name .Local =="\u006e\u0061\u006d\u0065"{_cgb :=_caeb .Value ;_af .NameAttr =_cgb ;continue ;};if _caeb .Name .Local =="\u0076\u0061\u006cu\u0065"{_ee :=_caeb .Value ;_af .ValueAttr =&_ee ;continue ;};};_eed :for {_eea ,_afc :=d .Token ();
if _afc !=nil {return _afc ;};switch _bbc :=_eea .(type ){case _f .StartElement :switch _bbc .Name {case _f .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006das\u002e\u006d\u0069\u0063\u0072\u006fs\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f2\u0030\u0030\u0036\u002f\u0061\u0063\u0074\u0069v\u0065\u0058",Local :"\u0066\u006f\u006e\u0074"}:_af .OcxPrChoice =NewCT_OcxPrChoice ();
if _cd :=d .DecodeElement (&_af .OcxPrChoice .Font ,&_bbc );_cd !=nil {return _cd ;};case _f .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006das\u002e\u006d\u0069\u0063\u0072\u006fs\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f2\u0030\u0030\u0036\u002f\u0061\u0063\u0074\u0069v\u0065\u0058",Local :"\u0070i\u0063\u0074\u0075\u0072\u0065"}:_af .OcxPrChoice =NewCT_OcxPrChoice ();
if _cdc :=d .DecodeElement (&_af .OcxPrChoice .Picture ,&_bbc );_cdc !=nil {return _cdc ;};default:_d .Log .Debug ("\u0073\u006b\u0069\u0070\u0070\u0069\u006eg\u0020\u0075\u006es\u0075\u0070\u0070\u006fr\u0074\u0065\u0064\u0020\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0043\u0054\u005f\u004f\u0063\u0078\u0050\u0072\u0020\u0025\u0076",_bbc .Name );
if _gg :=d .Skip ();_gg !=nil {return _gg ;};};case _f .EndElement :break _eed ;case _f .CharData :};};return nil ;};func (_afe *CT_OcxPrChoice )MarshalXML (e *_f .Encoder ,start _f .StartElement )error {if _afe .Font !=nil {_gfg :=_f .StartElement {Name :_f .Name {Local :"\u0061x\u003a\u0066\u006f\u006e\u0074"}};
e .EncodeElement (_afe .Font ,_gfg );}else if _afe .Picture !=nil {_ffd :=_f .StartElement {Name :_f .Name {Local :"\u0061\u0078\u003a\u0070\u0069\u0063\u0074\u0075\u0072\u0065"}};e .EncodeElement (_afe .Picture ,_ffd );};return nil ;};func (_ade ST_Persistence )ValidateWithPath (path string )error {switch _ade {case 0,1,2,3,4:default:return _g .Errorf ("\u0025s\u003a\u0020\u006f\u0075t\u0020\u006f\u0066\u0020\u0072a\u006eg\u0065 \u0076\u0061\u006c\u0075\u0065\u0020\u0025d",path ,int (_ade ));
};return nil ;};func (_gdd *ST_Persistence )UnmarshalXMLAttr (attr _f .Attr )error {switch attr .Value {case "":*_gdd =0;case "\u0070e\u0072s\u0069\u0073\u0074\u0050\u0072o\u0070\u0065r\u0074\u0079\u0042\u0061\u0067":*_gdd =1;case "\u0070\u0065\u0072\u0073\u0069\u0073\u0074\u0053\u0074\u0072\u0065\u0061\u006d":*_gdd =2;
case "\u0070\u0065\u0072\u0073\u0069\u0073\u0074\u0053\u0074\u0072\u0065\u0061m\u0049\u006e\u0069\u0074":*_gdd =3;case "\u0070\u0065\u0072\u0073\u0069\u0073\u0074\u0053\u0074o\u0072\u0061\u0067\u0065":*_gdd =4;};return nil ;};func (_ba *Ocx )MarshalXML (e *_f .Encoder ,start _f .StartElement )error {start .Attr =append (start .Attr ,_f .Attr {Name :_f .Name {Local :"\u0078\u006d\u006cn\u0073"},Value :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006das\u002e\u006d\u0069\u0063\u0072\u006fs\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f2\u0030\u0030\u0036\u002f\u0061\u0063\u0074\u0069v\u0065\u0058"});
start .Attr =append (start .Attr ,_f .Attr {Name :_f .Name {Local :"\u0078\u006d\u006c\u006e\u0073\u003a\u0061\u0078"},Value :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006das\u002e\u006d\u0069\u0063\u0072\u006fs\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f2\u0030\u0030\u0036\u002f\u0061\u0063\u0074\u0069v\u0065\u0058"});
start .Attr =append (start .Attr ,_f .Attr {Name :_f .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0072"},Value :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069c\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002fr\u0065\u006c\u0061\u0074\u0069\u006f\u006e\u0073h\u0069\u0070\u0073"});
start .Attr =append (start .Attr ,_f .Attr {Name :_f .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0078\u006dl"},Value :"\u0068\u0074tp\u003a\u002f\u002fw\u0077\u0077\u002e\u00773.o\u0072g/\u0058\u004d\u004c\u002f\u0031\u0039\u00398/\u006e\u0061\u006d\u0065\u0073\u0070\u0061c\u0065"});
start .Name .Local ="\u0061\u0078\u003a\u006f\u0063\u0078";return _ba .CT_Ocx .MarshalXML (e ,start );};func NewCT_Ocx ()*CT_Ocx {_bb :=&CT_Ocx {};_bb .PersistenceAttr =ST_Persistence (1);return _bb };func (_ffdd *CT_Picture )MarshalXML (e *_f .Encoder ,start _f .StartElement )error {if _ffdd .IdAttr !=nil {start .Attr =append (start .Attr ,_f .Attr {Name :_f .Name {Local :"\u0072\u003a\u0069\u0064"},Value :_g .Sprintf ("\u0025\u0076",*_ffdd .IdAttr )});
};e .EncodeToken (start );e .EncodeToken (_f .EndElement {Name :start .Name });return nil ;};func NewOcx ()*Ocx {_afa :=&Ocx {};_afa .CT_Ocx =*NewCT_Ocx ();return _afa };type CT_OcxPr struct{NameAttr string ;ValueAttr *string ;OcxPrChoice *CT_OcxPrChoice ;
};

// Validate validates the CT_OcxPrChoice and its children
func (_fad *CT_OcxPrChoice )Validate ()error {return _fad .ValidateWithPath ("\u0043\u0054\u005f\u004f\u0063\u0078\u0050\u0072\u0043h\u006f\u0069\u0063\u0065");};func (_fade *ST_Persistence )UnmarshalXML (d *_f .Decoder ,start _f .StartElement )error {_agf ,_gfc :=d .Token ();
if _gfc !=nil {return _gfc ;};if _egd ,_feg :=_agf .(_f .EndElement );_feg &&_egd .Name ==start .Name {*_fade =1;return nil ;};if _fbf ,_dba :=_agf .(_f .CharData );!_dba {return _g .Errorf ("\u0065\u0078\u0070\u0065\u0063\u0074\u0065\u0064\u0020\u0063\u0068a\u0072\u0020\u0064\u0061\u0074\u0061\u002c\u0020\u0067\u006ft\u0020\u0025\u0054",_agf );
}else {switch string (_fbf ){case "":*_fade =0;case "\u0070e\u0072s\u0069\u0073\u0074\u0050\u0072o\u0070\u0065r\u0074\u0079\u0042\u0061\u0067":*_fade =1;case "\u0070\u0065\u0072\u0073\u0069\u0073\u0074\u0053\u0074\u0072\u0065\u0061\u006d":*_fade =2;case "\u0070\u0065\u0072\u0073\u0069\u0073\u0074\u0053\u0074\u0072\u0065\u0061m\u0049\u006e\u0069\u0074":*_fade =3;
case "\u0070\u0065\u0072\u0073\u0069\u0073\u0074\u0053\u0074o\u0072\u0061\u0067\u0065":*_fade =4;};};_agf ,_gfc =d .Token ();if _gfc !=nil {return _gfc ;};if _gbe ,_dbd :=_agf .(_f .EndElement );_dbd &&_gbe .Name ==start .Name {return nil ;};return _g .Errorf ("\u0065\u0078\u0070\u0065c\u0074\u0065\u0064\u0020\u0065\u006e\u0064\u0020\u0065\u006ce\u006de\u006e\u0074\u002c\u0020\u0067\u006f\u0074 \u0025\u0076",_agf );
};func (_gcd *CT_OcxPrChoice )UnmarshalXML (d *_f .Decoder ,start _f .StartElement )error {_ddf :=start ;switch start .Name {case _f .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006das\u002e\u006d\u0069\u0063\u0072\u006fs\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f2\u0030\u0030\u0036\u002f\u0061\u0063\u0074\u0069v\u0065\u0058",Local :"\u0066\u006f\u006e\u0074"}:_gcd .Font =NewCT_Font ();
if _bggf :=d .DecodeElement (_gcd .Font ,&_ddf );_bggf !=nil {return _bggf ;};case _f .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006das\u002e\u006d\u0069\u0063\u0072\u006fs\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f2\u0030\u0030\u0036\u002f\u0061\u0063\u0074\u0069v\u0065\u0058",Local :"\u0070i\u0063\u0074\u0075\u0072\u0065"}:_gcd .Picture =NewCT_Picture ();
if _ace :=d .DecodeElement (_gcd .Picture ,&_ddf );_ace !=nil {return _ace ;};default:_d .Log .Debug ("\u0073\u006b\u0069\u0070\u0070\u0069n\u0067\u0020\u0075\u006e\u0073\u0075\u0070\u0070\u006f\u0072\u0074\u0065\u0064\u0020\u0065\u006c\u0065\u006d\u0065\u006et\u0020\u006f\u006e\u0020\u0043\u0054\u005f\u004f\u0063\u0078\u0050\u0072\u0043\u0068o\u0069c\u0065\u0020\u0025\u0076",_ddf .Name );
if _cb :=d .Skip ();_cb !=nil {return _cb ;};};return nil ;};

// Validate validates the Ocx and its children
func (_fbd *Ocx )Validate ()error {return _fbd .ValidateWithPath ("\u004f\u0063\u0078")};

// ValidateWithPath validates the CT_Font and its children, prefixing error messages with path
func (_eb *CT_Font )ValidateWithPath (path string )error {if _cc :=_eb .PersistenceAttr .ValidateWithPath (path +"\u002f\u0050e\u0072\u0073\u0069s\u0074\u0065\u006e\u0063\u0065\u0041\u0074\u0074\u0072");_cc !=nil {return _cc ;};for _ac ,_cac :=range _eb .OcxPr {if _fe :=_cac .ValidateWithPath (_g .Sprintf ("\u0025\u0073\u002fO\u0063\u0078\u0050\u0072\u005b\u0025\u0064\u005d",path ,_ac ));
_fe !=nil {return _fe ;};};return nil ;};

// Validate validates the CT_OcxPr and its children
func (_eac *CT_OcxPr )Validate ()error {return _eac .ValidateWithPath ("\u0043\u0054\u005f\u004f\u0063\u0078\u0050\u0072");};func (_fb *CT_Font )UnmarshalXML (d *_f .Decoder ,start _f .StartElement )error {for _ ,_e :=range start .Attr {if _e .Name .Space =="\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069c\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002fr\u0065\u006c\u0061\u0074\u0069\u006f\u006e\u0073h\u0069\u0070\u0073"&&_e .Name .Local =="\u0069\u0064"||_e .Name .Space =="\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fof\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0072\u0065\u006c\u0061\u0074\u0069\u006f\u006e\u0073\u0068\u0069\u0070\u0073"&&_e .Name .Local =="\u0069\u0064"{_ca :=_e .Value ;
_fb .IdAttr =&_ca ;continue ;};if _e .Name .Local =="p\u0065\u0072\u0073\u0069\u0073\u0074\u0065\u006e\u0063\u0065"{_fb .PersistenceAttr .UnmarshalXMLAttr (_e );continue ;};};_gc :for {_dg ,_bd :=d .Token ();if _bd !=nil {return _bd ;};switch _bg :=_dg .(type ){case _f .StartElement :switch _bg .Name {case _f .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006das\u002e\u006d\u0069\u0063\u0072\u006fs\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f2\u0030\u0030\u0036\u002f\u0061\u0063\u0074\u0069v\u0065\u0058",Local :"\u006f\u0063\u0078P\u0072"}:_fbe :=NewCT_OcxPr ();
if _bdd :=d .DecodeElement (_fbe ,&_bg );_bdd !=nil {return _bdd ;};_fb .OcxPr =append (_fb .OcxPr ,_fbe );default:_d .Log .Debug ("\u0073\u006b\u0069p\u0070\u0069\u006e\u0067\u0020\u0075\u006e\u0073\u0075\u0070\u0070\u006f\u0072\u0074\u0065\u0064\u0020\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0043T\u005f\u0046\u006f\u006e\u0074\u0020\u0025\u0076",_bg .Name );
if _de :=d .Skip ();_de !=nil {return _de ;};};case _f .EndElement :break _gc ;case _f .CharData :};};return nil ;};type CT_OcxPrChoice struct{Font *CT_Font ;Picture *CT_Picture ;};func (_fbed *Ocx )UnmarshalXML (d *_f .Decoder ,start _f .StartElement )error {_fbed .CT_Ocx =*NewCT_Ocx ();
for _ ,_ddd :=range start .Attr {if _ddd .Name .Space =="\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069c\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002fr\u0065\u006c\u0061\u0074\u0069\u006f\u006e\u0073h\u0069\u0070\u0073"&&_ddd .Name .Local =="\u0069\u0064"||_ddd .Name .Space =="\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fof\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0072\u0065\u006c\u0061\u0074\u0069\u006f\u006e\u0073\u0068\u0069\u0070\u0073"&&_ddd .Name .Local =="\u0069\u0064"{_fdg :=_ddd .Value ;
_fbed .IdAttr =&_fdg ;continue ;};if _ddd .Name .Local =="\u0063l\u0061\u0073\u0073\u0069\u0064"{_age :=_ddd .Value ;_fbed .ClassidAttr =_age ;continue ;};if _ddd .Name .Local =="\u006ci\u0063\u0065\u006e\u0073\u0065"{_bbb :=_ddd .Value ;_fbed .LicenseAttr =&_bbb ;
continue ;};if _ddd .Name .Local =="p\u0065\u0072\u0073\u0069\u0073\u0074\u0065\u006e\u0063\u0065"{_fbed .PersistenceAttr .UnmarshalXMLAttr (_ddd );continue ;};};_bba :for {_gfe ,_aba :=d .Token ();if _aba !=nil {return _aba ;};switch _fde :=_gfe .(type ){case _f .StartElement :switch _fde .Name {case _f .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006das\u002e\u006d\u0069\u0063\u0072\u006fs\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f2\u0030\u0030\u0036\u002f\u0061\u0063\u0074\u0069v\u0065\u0058",Local :"\u006f\u0063\u0078P\u0072"}:_bac :=NewCT_OcxPr ();
if _ddb :=d .DecodeElement (_bac ,&_fde );_ddb !=nil {return _ddb ;};_fbed .OcxPr =append (_fbed .OcxPr ,_bac );default:_d .Log .Debug ("\u0073\u006b\u0069\u0070\u0070\u0069\u006e\u0067\u0020\u0075\u006e\u0073\u0075\u0070\u0070\u006f\u0072\u0074\u0065\u0064\u0020\u0065\u006c\u0065m\u0065\u006e\u0074\u0020\u006fn\u0020\u004fc\u0078\u0020\u0025\u0076",_fde .Name );
if _gdb :=d .Skip ();_gdb !=nil {return _gdb ;};};case _f .EndElement :break _bba ;case _f .CharData :};};return nil ;};func NewCT_Font ()*CT_Font {_ae :=&CT_Font {};return _ae };

// ValidateWithPath validates the Ocx and its children, prefixing error messages with path
func (_dae *Ocx )ValidateWithPath (path string )error {if _bdb :=_dae .CT_Ocx .ValidateWithPath (path );_bdb !=nil {return _bdb ;};return nil ;};func (_ecd ST_Persistence )MarshalXMLAttr (name _f .Name )(_f .Attr ,error ){_cca :=_f .Attr {};_cca .Name =name ;
switch _ecd {case ST_PersistenceUnset :_cca .Value ="";case ST_PersistencePersistPropertyBag :_cca .Value ="\u0070e\u0072s\u0069\u0073\u0074\u0050\u0072o\u0070\u0065r\u0074\u0079\u0042\u0061\u0067";case ST_PersistencePersistStream :_cca .Value ="\u0070\u0065\u0072\u0073\u0069\u0073\u0074\u0053\u0074\u0072\u0065\u0061\u006d";
case ST_PersistencePersistStreamInit :_cca .Value ="\u0070\u0065\u0072\u0073\u0069\u0073\u0074\u0053\u0074\u0072\u0065\u0061m\u0049\u006e\u0069\u0074";case ST_PersistencePersistStorage :_cca .Value ="\u0070\u0065\u0072\u0073\u0069\u0073\u0074\u0053\u0074o\u0072\u0061\u0067\u0065";
};return _cca ,nil ;};func (_bfg *CT_Picture )UnmarshalXML (d *_f .Decoder ,start _f .StartElement )error {for _ ,_aegc :=range start .Attr {if _aegc .Name .Space =="\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069c\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002fr\u0065\u006c\u0061\u0074\u0069\u006f\u006e\u0073h\u0069\u0070\u0073"&&_aegc .Name .Local =="\u0069\u0064"||_aegc .Name .Space =="\u0068\u0074\u0074\u0070\u003a\u002f\u002fp\u0075\u0072\u006c.\u006f\u0063\u006cc\u002e\u006fr\u0067\u002f\u006f\u006f\u0078\u006dl\u002fof\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0072\u0065\u006c\u0061\u0074\u0069\u006f\u006e\u0073\u0068\u0069\u0070\u0073"&&_aegc .Name .Local =="\u0069\u0064"{_cda :=_aegc .Value ;
_bfg .IdAttr =&_cda ;continue ;};};for {_efd ,_bed :=d .Token ();if _bed !=nil {return _g .Errorf ("\u0070\u0061\u0072\u0073in\u0067\u0020\u0043\u0054\u005f\u0050\u0069\u0063\u0074\u0075\u0072\u0065\u003a\u0020%\u0073",_bed );};if _fcg ,_bbd :=_efd .(_f .EndElement );
_bbd &&_fcg .Name ==start .Name {break ;};};return nil ;};func (_ega ST_Persistence )String ()string {switch _ega {case 0:return "";case 1:return "\u0070e\u0072s\u0069\u0073\u0074\u0050\u0072o\u0070\u0065r\u0074\u0079\u0042\u0061\u0067";case 2:return "\u0070\u0065\u0072\u0073\u0069\u0073\u0074\u0053\u0074\u0072\u0065\u0061\u006d";
case 3:return "\u0070\u0065\u0072\u0073\u0069\u0073\u0074\u0053\u0074\u0072\u0065\u0061m\u0049\u006e\u0069\u0074";case 4:return "\u0070\u0065\u0072\u0073\u0069\u0073\u0074\u0053\u0074o\u0072\u0061\u0067\u0065";};return "";};func (_acb ST_Persistence )Validate ()error {return _acb .ValidateWithPath ("")};
func (_dc ST_Persistence )MarshalXML (e *_f .Encoder ,start _f .StartElement )error {return e .EncodeElement (_dc .String (),start );};const (ST_PersistenceUnset ST_Persistence =0;ST_PersistencePersistPropertyBag ST_Persistence =1;ST_PersistencePersistStream ST_Persistence =2;
ST_PersistencePersistStreamInit ST_Persistence =3;ST_PersistencePersistStorage ST_Persistence =4;);type ST_Persistence byte ;type CT_Picture struct{IdAttr *string ;};func NewCT_OcxPrChoice ()*CT_OcxPrChoice {_dgf :=&CT_OcxPrChoice {};return _dgf };type Ocx struct{CT_Ocx };
func (_bc *CT_Ocx )MarshalXML (e *_f .Encoder ,start _f .StartElement )error {start .Attr =append (start .Attr ,_f .Attr {Name :_f .Name {Local :"\u0061\u0078\u003a\u0063\u006c\u0061\u0073\u0073\u0069\u0064"},Value :_g .Sprintf ("\u0025\u0076",_bc .ClassidAttr )});
if _bc .LicenseAttr !=nil {start .Attr =append (start .Attr ,_f .Attr {Name :_f .Name {Local :"\u0061\u0078\u003a\u006c\u0069\u0063\u0065\u006e\u0073\u0065"},Value :_g .Sprintf ("\u0025\u0076",*_bc .LicenseAttr )});};if _bc .IdAttr !=nil {start .Attr =append (start .Attr ,_f .Attr {Name :_f .Name {Local :"\u0072\u003a\u0069\u0064"},Value :_g .Sprintf ("\u0025\u0076",*_bc .IdAttr )});
};_ga ,_dad :=_bc .PersistenceAttr .MarshalXMLAttr (_f .Name {Local :"\u0061\u0078\u003a\u0070\u0065\u0072\u0073\u0069\u0073t\u0065\u006e\u0063\u0065"});if _dad !=nil {return _dad ;};start .Attr =append (start .Attr ,_ga );e .EncodeToken (start );if _bc .OcxPr !=nil {_ef :=_f .StartElement {Name :_f .Name {Local :"\u0061\u0078\u003a\u006f\u0063\u0078\u0050\u0072"}};
for _ ,_gf :=range _bc .OcxPr {e .EncodeElement (_gf ,_ef );};};e .EncodeToken (_f .EndElement {Name :start .Name });return nil ;};func (_dac *CT_OcxPr )MarshalXML (e *_f .Encoder ,start _f .StartElement )error {start .Attr =append (start .Attr ,_f .Attr {Name :_f .Name {Local :"\u0061x\u003a\u006e\u0061\u006d\u0065"},Value :_g .Sprintf ("\u0025\u0076",_dac .NameAttr )});
if _dac .ValueAttr !=nil {start .Attr =append (start .Attr ,_f .Attr {Name :_f .Name {Local :"\u0061\u0078\u003a\u0076\u0061\u006c\u0075\u0065"},Value :_g .Sprintf ("\u0025\u0076",*_dac .ValueAttr )});};e .EncodeToken (start );_dac .OcxPrChoice .MarshalXML (e ,_f .StartElement {});
e .EncodeToken (_f .EndElement {Name :start .Name });return nil ;};func init (){_a .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006das\u002e\u006d\u0069\u0063\u0072\u006fs\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f2\u0030\u0030\u0036\u002f\u0061\u0063\u0074\u0069v\u0065\u0058","\u0043\u0054\u005f\u004f\u0063\u0078",NewCT_Ocx );
_a .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006das\u002e\u006d\u0069\u0063\u0072\u006fs\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f2\u0030\u0030\u0036\u002f\u0061\u0063\u0074\u0069v\u0065\u0058","\u0043\u0054\u005f\u004f\u0063\u0078\u0050\u0072",NewCT_OcxPr );
_a .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006das\u002e\u006d\u0069\u0063\u0072\u006fs\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f2\u0030\u0030\u0036\u002f\u0061\u0063\u0074\u0069v\u0065\u0058","\u0043T\u005f\u0046\u006f\u006e\u0074",NewCT_Font );
_a .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006das\u002e\u006d\u0069\u0063\u0072\u006fs\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f2\u0030\u0030\u0036\u002f\u0061\u0063\u0074\u0069v\u0065\u0058","\u0043\u0054\u005f\u0050\u0069\u0063\u0074\u0075\u0072\u0065",NewCT_Picture );
_a .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006das\u002e\u006d\u0069\u0063\u0072\u006fs\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f2\u0030\u0030\u0036\u002f\u0061\u0063\u0074\u0069v\u0065\u0058","\u006f\u0063\u0078",NewOcx );
};