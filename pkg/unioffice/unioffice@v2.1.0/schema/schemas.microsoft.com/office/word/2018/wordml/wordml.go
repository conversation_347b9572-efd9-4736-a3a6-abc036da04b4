//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package wordml ;import (_e "encoding/xml";_b "fmt";_c "github.com/unidoc/unioffice/v2";_f "github.com/unidoc/unioffice/v2/common/logger";_ac "github.com/unidoc/unioffice/v2/schema/soo/dml";_ba "github.com/unidoc/unioffice/v2/schema/soo/ofc/sharedTypes";
_eg "time";);func ParseUnionST_AnimationChartBuildType (s string )(_ac .ST_AnimationChartBuildType ,error ){return _ac .ParseUnionST_AnimationChartBuildType (s );};func (_ae *CT_ExtensionList )UnmarshalXML (d *_e .Decoder ,start _e .StartElement )error {_ga :for {_gf ,_ge :=d .Token ();
if _ge !=nil {return _ge ;};switch _ad :=_gf .(type ){case _e .StartElement :switch _ad .Name {case _e .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0038\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u0065\u0078\u0074"}:_bcg :=NewCT_Extension ();
if _ce :=d .DecodeElement (_bcg ,&_ad );_ce !=nil {return _ce ;};_ae .Ext =append (_ae .Ext ,_bcg );default:_f .Log .Debug ("\u0073\u006b\u0069\u0070\u0070i\u006e\u0067\u0020\u0075\u006e\u0073\u0075\u0070\u0070\u006f\u0072\u0074\u0065d\u0020\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0043\u0054\u005f\u0045\u0078\u0074\u0065\u006e\u0073\u0069\u006f\u006e\u004c\u0069\u0073\u0074\u0020\u0025v",_ad .Name );
if _cg :=d .Skip ();_cg !=nil {return _cg ;};};case _e .EndElement :break _ga ;case _e .CharData :};};return nil ;};type CT_ExtensionList struct{Ext []*CT_Extension ;};func ParseUnionST_FixedPercentage (s string )(_ac .ST_FixedPercentage ,error ){return _ac .ParseUnionST_FixedPercentage (s );
};func (_ag *CT_Extension )UnmarshalXML (d *_e .Decoder ,start _e .StartElement )error {for _ ,_agd :=range start .Attr {if _agd .Name .Local =="\u0075\u0072\u0069"{_d :=_agd .Value ;_ag .UriAttr =&_d ;continue ;};};_bae :for {_acg ,_acgd :=d .Token ();
if _acgd !=nil {return _acgd ;};switch _ab :=_acg .(type ){case _e .StartElement :switch _ab .Name {default:if _fc ,_dd :=_c .CreateElement (_ab );_dd !=nil {return _dd ;}else {if _bc :=d .DecodeElement (_fc ,&_ab );_bc !=nil {return _bc ;};_ag .Any =_fc ;
};};case _e .EndElement :break _bae ;case _e .CharData :};};return nil ;};func (_g *CT_ExtensionList )MarshalXML (e *_e .Encoder ,start _e .StartElement )error {start .Name .Local ="\u0077\u006f\u003a\u0043T_\u0045\u0078\u0074\u0065\u006e\u0073\u0069\u006f\u006e\u004c\u0069\u0073\u0074";
e .EncodeToken (start );if _g .Ext !=nil {_bcf :=_e .StartElement {Name :_e .Name {Local :"\u0077\u006f\u003a\u0065\u0078\u0074"}};for _ ,_ec :=range _g .Ext {e .EncodeElement (_ec ,_bcf );};};e .EncodeToken (_e .EndElement {Name :start .Name });return nil ;
};

// ValidateWithPath validates the CT_ExtensionList and its children, prefixing error messages with path
func (_be *CT_ExtensionList )ValidateWithPath (path string )error {for _fdc ,_fa :=range _be .Ext {if _ed :=_fa .ValidateWithPath (_b .Sprintf ("\u0025\u0073\u002f\u0045\u0078\u0074\u005b\u0025\u0064\u005d",path ,_fdc ));_ed !=nil {return _ed ;};};return nil ;
};func NewCT_Extension ()*CT_Extension {_fe :=&CT_Extension {};return _fe };func ParseUnionST_AdjCoordinate (s string )(_ac .ST_AdjCoordinate ,error ){return _ac .ParseUnionST_AdjCoordinate (s );};func ParseUnionST_AdjAngle (s string )(_ac .ST_AdjAngle ,error ){return _ac .ParseUnionST_AdjAngle (s )};


// ValidateWithPath validates the CT_Extension and its children, prefixing error messages with path
func (_fec *CT_Extension )ValidateWithPath (path string )error {return nil };func ParseUnionST_PositivePercentage (s string )(_ac .ST_PositivePercentage ,error ){return _ac .ParseUnionST_PositivePercentage (s );};type CT_Extension struct{UriAttr *string ;
Any _c .Any ;};func ParseUnionST_PositiveFixedPercentage (s string )(_ac .ST_PositiveFixedPercentage ,error ){return _ac .ParseUnionST_PositiveFixedPercentage (s );};func ParseUnionST_AnimationDgmBuildType (s string )(_ac .ST_AnimationDgmBuildType ,error ){return _ac .ParseUnionST_AnimationDgmBuildType (s );
};func ParseStdlibTime (s string )(_eg .Time ,error ){return _ba .ParseStdlibTime (s )};func ParseUnionST_Coordinate32 (s string )(_ac .ST_Coordinate32 ,error ){return _ac .ParseUnionST_Coordinate32 (s );};func _daa (_dag bool )uint8 {if _dag {return 1;
};return 0;};func ParseUnionST_OnOff (s string )(_ba .ST_OnOff ,error ){return _ba .ST_OnOff {},nil };

// Validate validates the CT_ExtensionList and its children
func (_da *CT_ExtensionList )Validate ()error {return _da .ValidateWithPath ("\u0043\u0054_\u0045\u0078\u0074e\u006e\u0073\u0069\u006f\u006e\u004c\u0069\u0073\u0074");};func ParseUnionST_Percentage (s string )(_ac .ST_Percentage ,error ){return _ac .ParseUnionST_Percentage (s );
};func NewCT_ExtensionList ()*CT_ExtensionList {_cf :=&CT_ExtensionList {};return _cf };

// Validate validates the CT_Extension and its children
func (_bg *CT_Extension )Validate ()error {return _bg .ValidateWithPath ("\u0043\u0054\u005fE\u0078\u0074\u0065\u006e\u0073\u0069\u006f\u006e");};func (_fd *CT_Extension )MarshalXML (e *_e .Encoder ,start _e .StartElement )error {if _fd .UriAttr !=nil {start .Attr =append (start .Attr ,_e .Attr {Name :_e .Name {Local :"\u0077\u006f\u003a\u0075\u0072\u0069"},Value :_b .Sprintf ("\u0025\u0076",*_fd .UriAttr )});
};e .EncodeToken (start );if _fd .Any !=nil {_fd .Any .MarshalXML (e ,_e .StartElement {});};e .EncodeToken (_e .EndElement {Name :start .Name });return nil ;};func ParseUnionST_TextSpacingPercentOrPercentString (s string )(_ac .ST_TextSpacingPercentOrPercentString ,error ){return _ac .ParseUnionST_TextSpacingPercentOrPercentString (s );
};type Any interface{MarshalXML (_ada *_e .Encoder ,_fed _e .StartElement )error ;UnmarshalXML (_bf *_e .Decoder ,_daf _e .StartElement )error ;};func ParseUnionST_Coordinate (s string )(_ac .ST_Coordinate ,error ){return _ac .ParseUnionST_Coordinate (s );
};func ParseUnionST_TextPoint (s string )(_ac .ST_TextPoint ,error ){return _ac .ParseUnionST_TextPoint (s )};func ParseUnionST_TextFontScalePercentOrPercentString (s string )(_ac .ST_TextFontScalePercentOrPercentString ,error ){return _ac .ParseUnionST_TextFontScalePercentOrPercentString (s );
};func init (){_c .RegisterConstructor ("\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0038\u002f\u0077\u006f\u0072\u0064\u006d\u006c","\u0043\u0054\u005fE\u0078\u0074\u0065\u006e\u0073\u0069\u006f\u006e",NewCT_Extension );
_c .RegisterConstructor ("\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0038\u002f\u0077\u006f\u0072\u0064\u006d\u006c","\u0043\u0054_\u0045\u0078\u0074e\u006e\u0073\u0069\u006f\u006e\u004c\u0069\u0073\u0074",NewCT_ExtensionList );
};