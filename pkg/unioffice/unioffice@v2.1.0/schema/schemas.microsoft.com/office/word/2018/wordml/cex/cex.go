//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package cex ;import (_ff "encoding/xml";_e "fmt";_g "github.com/unidoc/unioffice/v2";_fg "github.com/unidoc/unioffice/v2/common/logger";_ca "github.com/unidoc/unioffice/v2/schema/schemas.microsoft.com/office/word/2018/wordml";_d "github.com/unidoc/unioffice/v2/schema/soo/ofc/sharedTypes";
_c "time";);

// Validate validates the CT_CommentExtensible and its children
func (_cg *CT_CommentExtensible )Validate ()error {return _cg .ValidateWithPath ("C\u0054_\u0043\u006f\u006d\u006d\u0065\u006e\u0074\u0045x\u0074\u0065\u006e\u0073ib\u006c\u0065");};

// ValidateWithPath validates the CT_CommentsExtensible and its children, prefixing error messages with path
func (_be *CT_CommentsExtensible )ValidateWithPath (path string )error {for _gc ,_ag :=range _be .CommentExtensible {if _ade :=_ag .ValidateWithPath (_e .Sprintf ("\u0025s\u002f\u0043\u006f\u006d\u006d\u0065\u006e\u0074\u0045\u0078\u0074e\u006e\u0073\u0069\u0062\u006c\u0065\u005b\u0025\u0064\u005d",path ,_gc ));
_ade !=nil {return _ade ;};};if _be .ExtLst !=nil {if _cca :=_be .ExtLst .ValidateWithPath (path +"\u002fE\u0078\u0074\u004c\u0073\u0074");_cca !=nil {return _cca ;};};return nil ;};

// ValidateWithPath validates the CommentsExtensible and its children, prefixing error messages with path
func (_beb *CommentsExtensible )ValidateWithPath (path string )error {if _dcf :=_beb .CT_CommentsExtensible .ValidateWithPath (path );_dcf !=nil {return _dcf ;};return nil ;};func NewCT_CommentsExtensible ()*CT_CommentsExtensible {_gg :=&CT_CommentsExtensible {};
return _gg };type CT_CommentExtensible struct{DateUtcAttr *_c .Time ;IntelligentPlaceholderAttr *_d .ST_OnOff ;ExtLst *_ca .CT_ExtensionList ;};func NewCT_CommentExtensible ()*CT_CommentExtensible {_cb :=&CT_CommentExtensible {};return _cb };func (_ac *CT_CommentExtensible )UnmarshalXML (d *_ff .Decoder ,start _ff .StartElement )error {for _ ,_ad :=range start .Attr {if _ad .Name .Local =="\u0064a\u0074\u0065\u0055\u0074\u0063"{_aca ,_ge :=ParseStdlibTime (_ad .Value );
if _ge !=nil {return _ge ;};_ac .DateUtcAttr =&_aca ;continue ;};if _ad .Name .Local =="\u0069\u006e\u0074\u0065ll\u0069\u0067\u0065\u006e\u0074\u0050\u006c\u0061\u0063\u0065\u0068\u006f\u006c\u0064e\u0072"{_ae ,_ea :=ParseUnionST_OnOff (_ad .Value );if _ea !=nil {return _ea ;
};_ac .IntelligentPlaceholderAttr =&_ae ;continue ;};};_dc :for {_ga ,_abf :=d .Token ();if _abf !=nil {return _abf ;};switch _adf :=_ga .(type ){case _ff .StartElement :switch _adf .Name {case _ff .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068e\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002ec\u006f\u006d\u002f\u006f\u0066fi\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0038\u002f\u0077\u006f\u0072\u0064\u006d\u006c\u002f\u0063\u0065\u0078",Local :"\u0065\u0078\u0074\u004c\u0073\u0074"}:_ac .ExtLst =_ca .NewCT_ExtensionList ();
if _cf :=d .DecodeElement (_ac .ExtLst ,&_adf );_cf !=nil {return _cf ;};default:_fg .Log .Debug ("\u0073\u006b\u0069\u0070\u0070\u0069\u006eg\u0020\u0075\u006es\u0075\u0070\u0070o\u0072\u0074e\u0064\u0020\u0065\u006c\u0065\u006de\u006et \u006f\u006e\u0020\u0043\u0054\u005f\u0043\u006f\u006d\u006d\u0065\u006e\u0074\u0045\u0078\u0074\u0065\u006e\u0073\u0069\u0062\u006c\u0065\u0020\u0025\u0076",_adf .Name );
if _gae :=d .Skip ();_gae !=nil {return _gae ;};};case _ff .EndElement :break _dc ;case _ff .CharData :};};return nil ;};

// Validate validates the CT_CommentsExtensible and its children
func (_gf *CT_CommentsExtensible )Validate ()error {return _gf .ValidateWithPath ("C\u0054\u005f\u0043\u006fmm\u0065n\u0074\u0073\u0045\u0078\u0074e\u006e\u0073\u0069\u0062\u006c\u0065");};func (_adg *CT_CommentsExtensible )MarshalXML (e *_ff .Encoder ,start _ff .StartElement )error {e .EncodeToken (start );
if _adg .CommentExtensible !=nil {_b :=_ff .StartElement {Name :_ff .Name {Local :"c\u0065:\u0063\u006f\u006d\u006d\u0065\u006e\u0074\u0045x\u0074\u0065\u006e\u0073ib\u006c\u0065"}};for _ ,_fbf :=range _adg .CommentExtensible {e .EncodeElement (_fbf ,_b );
};};if _adg .ExtLst !=nil {_cfc :=_ff .StartElement {Name :_ff .Name {Local :"\u0063e\u003a\u0065\u0078\u0074\u004c\u0073t"}};e .EncodeElement (_adg .ExtLst ,_cfc );};e .EncodeToken (_ff .EndElement {Name :start .Name });return nil ;};func (_a *CT_CommentExtensible )MarshalXML (e *_ff .Encoder ,start _ff .StartElement )error {if _a .DateUtcAttr !=nil {start .Attr =append (start .Attr ,_ff .Attr {Name :_ff .Name {Local :"\u0063\u0065\u003a\u0064\u0061\u0074\u0065\u0055\u0074\u0063"},Value :_d .FormatDateTime (*_a .DateUtcAttr )});
};if _a .IntelligentPlaceholderAttr !=nil {start .Attr =append (start .Attr ,_ff .Attr {Name :_ff .Name {Local :"\u0063e\u003a\u0069\u006e\u0074e\u006c\u006c\u0069\u0067\u0065n\u0074P\u006ca\u0063\u0065\u0068\u006f\u006c\u0064\u0065r"},Value :_e .Sprintf ("\u0025\u0076",*_a .IntelligentPlaceholderAttr )});
};e .EncodeToken (start );if _a .ExtLst !=nil {_ab :=_ff .StartElement {Name :_ff .Name {Local :"\u0063e\u003a\u0065\u0078\u0074\u004c\u0073t"}};e .EncodeElement (_a .ExtLst ,_ab );};e .EncodeToken (_ff .EndElement {Name :start .Name });return nil ;};func (_de *CT_CommentsExtensible )UnmarshalXML (d *_ff .Decoder ,start _ff .StartElement )error {_ggc :for {_ed ,_fga :=d .Token ();
if _fga !=nil {return _fga ;};switch _fa :=_ed .(type ){case _ff .StartElement :switch _fa .Name {case _ff .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068e\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002ec\u006f\u006d\u002f\u006f\u0066fi\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0038\u002f\u0077\u006f\u0072\u0064\u006d\u006c\u002f\u0063\u0065\u0078",Local :"\u0063\u006f\u006d\u006d\u0065\u006e\u0074\u0045\u0078\u0074\u0065\u006es\u0069\u0062\u006c\u0065"}:_acc :=NewCT_CommentExtensible ();
if _fd :=d .DecodeElement (_acc ,&_fa );_fd !=nil {return _fd ;};_de .CommentExtensible =append (_de .CommentExtensible ,_acc );case _ff .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068e\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002ec\u006f\u006d\u002f\u006f\u0066fi\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0038\u002f\u0077\u006f\u0072\u0064\u006d\u006c\u002f\u0063\u0065\u0078",Local :"\u0065\u0078\u0074\u004c\u0073\u0074"}:_de .ExtLst =_ca .NewCT_ExtensionList ();
if _eg :=d .DecodeElement (_de .ExtLst ,&_fa );_eg !=nil {return _eg ;};default:_fg .Log .Debug ("\u0073\u006b\u0069\u0070\u0070\u0069n\u0067\u0020\u0075n\u0073\u0075\u0070p\u006f\u0072\u0074\u0065\u0064\u0020\u0065\u006c\u0065\u006de\u006e\u0074\u0020\u006f\u006e C\u0054\u005f\u0043\u006f\u006d\u006d\u0065\u006e\u0074\u0073\u0045\u0078\u0074\u0065\u006e\u0073\u0069\u0062\u006c\u0065\u0020\u0025\u0076",_fa .Name );
if _ffe :=d .Skip ();_ffe !=nil {return _ffe ;};};case _ff .EndElement :break _ggc ;case _ff .CharData :};};return nil ;};type CT_CommentsExtensible struct{CommentExtensible []*CT_CommentExtensible ;ExtLst *_ca .CT_ExtensionList ;};

// ValidateWithPath validates the CT_CommentExtensible and its children, prefixing error messages with path
func (_abb *CT_CommentExtensible )ValidateWithPath (path string )error {if _abb .IntelligentPlaceholderAttr !=nil {if _af :=_abb .IntelligentPlaceholderAttr .ValidateWithPath (path +"/\u0049\u006e\u0074\u0065\u006c\u006ci\u0067\u0065\u006e\u0074\u0050\u006c\u0061\u0063\u0065h\u006f\u006c\u0064e\u0072A\u0074\u0074\u0072");
_af !=nil {return _af ;};};if _abb .ExtLst !=nil {if _fb :=_abb .ExtLst .ValidateWithPath (path +"\u002fE\u0078\u0074\u004c\u0073\u0074");_fb !=nil {return _fb ;};};return nil ;};func NewCommentsExtensible ()*CommentsExtensible {_dcg :=&CommentsExtensible {};
_dcg .CT_CommentsExtensible =*NewCT_CommentsExtensible ();return _dcg ;};func ParseUnionST_OnOff (s string )(_d .ST_OnOff ,error ){return _d .ParseUnionST_OnOff (s )};func ParseStdlibTime (s string )(_c .Time ,error ){return _d .ParseStdlibTime (s )};type CommentsExtensible struct{CT_CommentsExtensible };
func (_ef *CommentsExtensible )UnmarshalXML (d *_ff .Decoder ,start _ff .StartElement )error {_ef .CT_CommentsExtensible =*NewCT_CommentsExtensible ();_gcf :for {_efc ,_ba :=d .Token ();if _ba !=nil {return _ba ;};switch _df :=_efc .(type ){case _ff .StartElement :switch _df .Name {case _ff .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068e\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002ec\u006f\u006d\u002f\u006f\u0066fi\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0038\u002f\u0077\u006f\u0072\u0064\u006d\u006c\u002f\u0063\u0065\u0078",Local :"\u0063\u006f\u006d\u006d\u0065\u006e\u0074\u0045\u0078\u0074\u0065\u006es\u0069\u0062\u006c\u0065"}:_ce :=NewCT_CommentExtensible ();
if _bg :=d .DecodeElement (_ce ,&_df );_bg !=nil {return _bg ;};_ef .CommentExtensible =append (_ef .CommentExtensible ,_ce );case _ff .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068e\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002ec\u006f\u006d\u002f\u006f\u0066fi\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0038\u002f\u0077\u006f\u0072\u0064\u006d\u006c\u002f\u0063\u0065\u0078",Local :"\u0065\u0078\u0074\u004c\u0073\u0074"}:_ef .ExtLst =_ca .NewCT_ExtensionList ();
if _gea :=d .DecodeElement (_ef .ExtLst ,&_df );_gea !=nil {return _gea ;};default:_fg .Log .Debug ("\u0073\u006b\u0069\u0070\u0070\u0069\u006e\u0067\u0020\u0075\u006es\u0075\u0070\u0070\u006f\u0072\u0074\u0065\u0064 \u0065l\u0065\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0043\u006f\u006d\u006d\u0065\u006e\u0074\u0073\u0045x\u0074\u0065\u006e\u0073\u0069\u0062\u006c\u0065\u0020\u0025\u0076",_df .Name );
if _cfe :=d .Skip ();_cfe !=nil {return _cfe ;};};case _ff .EndElement :break _gcf ;case _ff .CharData :};};return nil ;};func (_eb *CommentsExtensible )MarshalXML (e *_ff .Encoder ,start _ff .StartElement )error {start .Attr =append (start .Attr ,_ff .Attr {Name :_ff .Name {Local :"\u0078\u006d\u006cn\u0073"},Value :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068e\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002ec\u006f\u006d\u002f\u006f\u0066fi\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0038\u002f\u0077\u006f\u0072\u0064\u006d\u006c\u002f\u0063\u0065\u0078"});
start .Attr =append (start .Attr ,_ff .Attr {Name :_ff .Name {Local :"\u0078\u006d\u006c\u006e\u0073\u003a\u0063\u0065"},Value :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068e\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002ec\u006f\u006d\u002f\u006f\u0066fi\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0038\u002f\u0077\u006f\u0072\u0064\u006d\u006c\u002f\u0063\u0065\u0078"});
start .Attr =append (start .Attr ,_ff .Attr {Name :_ff .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0073"},Value :"\u0068\u0074\u0074\u0070\u003a/\u002f\u0073\u0063\u0068\u0065m\u0061s\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067/\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0068\u0061\u0072e\u0064\u0054\u0079\u0070\u0065\u0073"});
start .Attr =append (start .Attr ,_ff .Attr {Name :_ff .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0077"},Value :"ht\u0074\u0070:\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073.\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0077\u006f\u0072\u0064\u0070\u0072\u006f\u0063\u0065s\u0073i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u00306\u002fm\u0061\u0069n"});
start .Attr =append (start .Attr ,_ff .Attr {Name :_ff .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0078\u006dl"},Value :"\u0068\u0074tp\u003a\u002f\u002fw\u0077\u0077\u002e\u00773.o\u0072g/\u0058\u004d\u004c\u002f\u0031\u0039\u00398/\u006e\u0061\u006d\u0065\u0073\u0070\u0061c\u0065"});
start .Name .Local ="c\u0065\u003a\u0063\u006fmm\u0065n\u0074\u0073\u0045\u0078\u0074e\u006e\u0073\u0069\u0062\u006c\u0065";return _eb .CT_CommentsExtensible .MarshalXML (e ,start );};

// Validate validates the CommentsExtensible and its children
func (_db *CommentsExtensible )Validate ()error {return _db .ValidateWithPath ("\u0043o\u006dm\u0065\u006e\u0074\u0073\u0045x\u0074\u0065n\u0073\u0069\u0062\u006c\u0065");};func init (){_g .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068e\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002ec\u006f\u006d\u002f\u006f\u0066fi\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0038\u002f\u0077\u006f\u0072\u0064\u006d\u006c\u002f\u0063\u0065\u0078","C\u0054\u005f\u0043\u006fmm\u0065n\u0074\u0073\u0045\u0078\u0074e\u006e\u0073\u0069\u0062\u006c\u0065",NewCT_CommentsExtensible );
_g .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068e\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002ec\u006f\u006d\u002f\u006f\u0066fi\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0038\u002f\u0077\u006f\u0072\u0064\u006d\u006c\u002f\u0063\u0065\u0078","C\u0054_\u0043\u006f\u006d\u006d\u0065\u006e\u0074\u0045x\u0074\u0065\u006e\u0073ib\u006c\u0065",NewCT_CommentExtensible );
_g .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068e\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002ec\u006f\u006d\u002f\u006f\u0066fi\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0038\u002f\u0077\u006f\u0072\u0064\u006d\u006c\u002f\u0063\u0065\u0078","\u0063o\u006dm\u0065\u006e\u0074\u0073\u0045x\u0074\u0065n\u0073\u0069\u0062\u006c\u0065",NewCommentsExtensible );
};