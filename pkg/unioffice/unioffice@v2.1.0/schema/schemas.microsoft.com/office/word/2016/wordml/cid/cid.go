//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package cid ;import (_ge "encoding/xml";_d "fmt";_gf "github.com/unidoc/unioffice/v2";_e "github.com/unidoc/unioffice/v2/common/logger";_b "strconv";);func NewDecimaldurableId ()*DecimaldurableId {_dgf :=&DecimaldurableId {};return _dgf };func (_bab *CommentsIds )UnmarshalXML (d *_ge .Decoder ,start _ge .StartElement )error {_bab .CT_CommentsIds =*NewCT_CommentsIds ();
_gff :for {_cg ,_cbe :=d .Token ();if _cbe !=nil {return _cbe ;};switch _ddb :=_cg .(type ){case _ge .StartElement :switch _ddb .Name {case _ge .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068e\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002ec\u006f\u006d\u002f\u006f\u0066fi\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0036\u002f\u0077\u006f\u0072\u0064\u006d\u006c\u002f\u0063\u0069\u0064",Local :"\u0063o\u006d\u006d\u0065\u006e\u0074\u0049d"}:_ae :=NewCT_CommentId ();
if _ff :=d .DecodeElement (_ae ,&_ddb );_ff !=nil {return _ff ;};_bab .CommentId =append (_bab .CommentId ,_ae );default:_e .Log .Debug ("\u0073\u006bi\u0070\u0070\u0069\u006e\u0067\u0020\u0075\u006e\u0073\u0075\u0070\u0070\u006f\u0072\u0074\u0065\u0064\u0020\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0043\u006f\u006d\u006d\u0065\u006e\u0074\u0073\u0049\u0064\u0073\u0020\u0025\u0076",_ddb .Name );
if _da :=d .Skip ();_da !=nil {return _da ;};};case _ge .EndElement :break _gff ;case _ge .CharData :};};return nil ;};func (_f *CT_CommentId )MarshalXML (e *_ge .Encoder ,start _ge .StartElement )error {start .Attr =append (start .Attr ,_ge .Attr {Name :_ge .Name {Local :"\u0063i\u003a\u0070\u0061\u0072\u0061\u0049d"},Value :_d .Sprintf ("\u0025\u0076",_f .ParaIdAttr )});
start .Attr =append (start .Attr ,_ge .Attr {Name :_ge .Name {Local :"\u0063\u0069\u003ad\u0075\u0072\u0061\u0062\u006c\u0065\u0049\u0064"},Value :_d .Sprintf ("\u0025\u0076",_f .DurableIdAttr )});e .EncodeToken (start );e .EncodeToken (_ge .EndElement {Name :start .Name });
return nil ;};func NewCT_CommentsIds ()*CT_CommentsIds {_fa :=&CT_CommentsIds {};return _fa };

// Validate validates the CT_CommentId and its children
func (_eb *CT_CommentId )Validate ()error {return _eb .ValidateWithPath ("\u0043\u0054\u005fC\u006f\u006d\u006d\u0065\u006e\u0074\u0049\u0064");};

// ValidateWithPath validates the CT_CommentsIds and its children, prefixing error messages with path
func (_dd *CT_CommentsIds )ValidateWithPath (path string )error {for _ba ,_cf :=range _dd .CommentId {if _faf :=_cf .ValidateWithPath (_d .Sprintf ("\u0025\u0073/\u0043\u006f\u006dm\u0065\u006e\u0074\u0049\u0064\u005b\u0025\u0064\u005d",path ,_ba ));_faf !=nil {return _faf ;
};};return nil ;};func (_fe *CT_CommentsIds )MarshalXML (e *_ge .Encoder ,start _ge .StartElement )error {e .EncodeToken (start );if _fe .CommentId !=nil {_cce :=_ge .StartElement {Name :_ge .Name {Local :"\u0063\u0069\u003ac\u006f\u006d\u006d\u0065\u006e\u0074\u0049\u0064"}};
for _ ,_gd :=range _fe .CommentId {e .EncodeElement (_gd ,_cce );};};e .EncodeToken (_ge .EndElement {Name :start .Name });return nil ;};type CT_CommentsIds struct{CommentId []*CT_CommentId ;};

// Validate validates the CT_CommentsIds and its children
func (_cbb *CT_CommentsIds )Validate ()error {return _cbb .ValidateWithPath ("\u0043\u0054\u005f\u0043\u006f\u006d\u006d\u0065\u006et\u0073\u0049\u0064\u0073");};type CommentsIds struct{CT_CommentsIds };func (_ad *DecimaldurableId )MarshalXML (e *_ge .Encoder ,start _ge .StartElement )error {if _ad .DurableIdAttr !=nil {start .Attr =append (start .Attr ,_ge .Attr {Name :_ge .Name {Local :"\u0063\u0069\u003ad\u0075\u0072\u0061\u0062\u006c\u0065\u0049\u0064"},Value :_d .Sprintf ("\u0025\u0076",*_ad .DurableIdAttr )});
};start .Name .Local ="\u0063\u0069\u003a\u0064ec\u0069\u006d\u0061\u006c\u0064\u0075\u0072\u0061\u0062\u006c\u0065\u0049\u0064";return nil ;};

// Validate validates the CommentsIds and its children
func (_bd *CommentsIds )Validate ()error {return _bd .ValidateWithPath ("C\u006f\u006d\u006d\u0065\u006e\u0074\u0073\u0049\u0064\u0073");};type DecimaldurableId struct{DurableIdAttr *int64 ;};

// ValidateWithPath validates the CommentsIds and its children, prefixing error messages with path
func (_fdb *CommentsIds )ValidateWithPath (path string )error {if _ed :=_fdb .CT_CommentsIds .ValidateWithPath (path );_ed !=nil {return _ed ;};return nil ;};func NewCT_CommentId ()*CT_CommentId {_c :=&CT_CommentId {};return _c };func (_egg *CommentsIds )MarshalXML (e *_ge .Encoder ,start _ge .StartElement )error {start .Attr =append (start .Attr ,_ge .Attr {Name :_ge .Name {Local :"\u0078\u006d\u006cn\u0073"},Value :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068e\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002ec\u006f\u006d\u002f\u006f\u0066fi\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0036\u002f\u0077\u006f\u0072\u0064\u006d\u006c\u002f\u0063\u0069\u0064"});
start .Attr =append (start .Attr ,_ge .Attr {Name :_ge .Name {Local :"\u0078\u006d\u006c\u006e\u0073\u003a\u0063\u0069"},Value :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068e\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002ec\u006f\u006d\u002f\u006f\u0066fi\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0036\u002f\u0077\u006f\u0072\u0064\u006d\u006c\u002f\u0063\u0069\u0064"});
start .Attr =append (start .Attr ,_ge .Attr {Name :_ge .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0077"},Value :"ht\u0074\u0070:\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073.\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0077\u006f\u0072\u0064\u0070\u0072\u006f\u0063\u0065s\u0073i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u00306\u002fm\u0061\u0069n"});
start .Attr =append (start .Attr ,_ge .Attr {Name :_ge .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0078\u006dl"},Value :"\u0068\u0074tp\u003a\u002f\u002fw\u0077\u0077\u002e\u00773.o\u0072g/\u0058\u004d\u004c\u002f\u0031\u0039\u00398/\u006e\u0061\u006d\u0065\u0073\u0070\u0061c\u0065"});
start .Name .Local ="\u0063\u0069\u003a\u0063\u006f\u006d\u006d\u0065\u006et\u0073\u0049\u0064\u0073";return _egg .CT_CommentsIds .MarshalXML (e ,start );};type CT_CommentId struct{ParaIdAttr string ;DurableIdAttr string ;};func (_fge *DecimaldurableId )UnmarshalXML (d *_ge .Decoder ,start _ge .StartElement )error {for _ ,_be :=range start .Attr {if _be .Name .Local =="\u0064u\u0072\u0061\u0062\u006c\u0065\u0049d"{_gc ,_bf :=_b .ParseInt (_be .Value ,10,64);
if _bf !=nil {return _bf ;};_fge .DurableIdAttr =&_gc ;continue ;};};for {_gdf ,_gda :=d .Token ();if _gda !=nil {return _d .Errorf ("\u0070\u0061\u0072\u0073i\u006e\u0067\u0020\u0044\u0065\u0063\u0069\u006d\u0061\u006cd\u0075r\u0061\u0062\u006c\u0065\u0049\u0064\u003a \u0025\u0073",_gda );
};if _bfc ,_bfd :=_gdf .(_ge .EndElement );_bfd &&_bfc .Name ==start .Name {break ;};};return nil ;};

// Validate validates the DecimaldurableId and its children
func (_db *DecimaldurableId )Validate ()error {return _db .ValidateWithPath ("\u0044\u0065c\u0069\u006d\u0061l\u0064\u0075\u0072\u0061\u0062\u006c\u0065\u0049\u0064");};func NewCommentsIds ()*CommentsIds {_fd :=&CommentsIds {};_fd .CT_CommentsIds =*NewCT_CommentsIds ();
return _fd ;};func (_ec *CT_CommentId )UnmarshalXML (d *_ge .Decoder ,start _ge .StartElement )error {for _ ,_fg :=range start .Attr {if _fg .Name .Local =="\u0070\u0061\u0072\u0061\u0049\u0064"{_eg :=_fg .Value ;_ec .ParaIdAttr =_eg ;continue ;};if _fg .Name .Local =="\u0064u\u0072\u0061\u0062\u006c\u0065\u0049d"{_ce :=_fg .Value ;
_ec .DurableIdAttr =_ce ;continue ;};};for {_cc ,_fc :=d .Token ();if _fc !=nil {return _d .Errorf ("\u0070a\u0072\u0073\u0069\u006e\u0067\u0020\u0043\u0054\u005f\u0043\u006fm\u006d\u0065\u006e\u0074\u0049\u0064\u003a\u0020\u0025\u0073",_fc );};if _de ,_bc :=_cc .(_ge .EndElement );
_bc &&_de .Name ==start .Name {break ;};};return nil ;};

// ValidateWithPath validates the CT_CommentId and its children, prefixing error messages with path
func (_a *CT_CommentId )ValidateWithPath (path string )error {return nil };func (_aa *CT_CommentsIds )UnmarshalXML (d *_ge .Decoder ,start _ge .StartElement )error {_cb :for {_gef ,_ca :=d .Token ();if _ca !=nil {return _ca ;};switch _cad :=_gef .(type ){case _ge .StartElement :switch _cad .Name {case _ge .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068e\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002ec\u006f\u006d\u002f\u006f\u0066fi\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0036\u002f\u0077\u006f\u0072\u0064\u006d\u006c\u002f\u0063\u0069\u0064",Local :"\u0063o\u006d\u006d\u0065\u006e\u0074\u0049d"}:_dg :=NewCT_CommentId ();
if _ga :=d .DecodeElement (_dg ,&_cad );_ga !=nil {return _ga ;};_aa .CommentId =append (_aa .CommentId ,_dg );default:_e .Log .Debug ("\u0073\u006b\u0069\u0070\u0070\u0069n\u0067\u0020\u0075\u006e\u0073\u0075\u0070\u0070\u006f\u0072\u0074\u0065\u0064\u0020\u0065\u006c\u0065\u006d\u0065\u006et\u0020\u006f\u006e\u0020\u0043\u0054\u005f\u0043\u006f\u006d\u006d\u0065\u006e\u0074s\u0049d\u0073\u0020\u0025\u0076",_cad .Name );
if _feb :=d .Skip ();_feb !=nil {return _feb ;};};case _ge .EndElement :break _cb ;case _ge .CharData :};};return nil ;};

// ValidateWithPath validates the DecimaldurableId and its children, prefixing error messages with path
func (_gfa *DecimaldurableId )ValidateWithPath (path string )error {return nil };func init (){_gf .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068e\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002ec\u006f\u006d\u002f\u006f\u0066fi\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0036\u002f\u0077\u006f\u0072\u0064\u006d\u006c\u002f\u0063\u0069\u0064","\u0043\u0054\u005f\u0043\u006f\u006d\u006d\u0065\u006et\u0073\u0049\u0064\u0073",NewCT_CommentsIds );
_gf .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068e\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002ec\u006f\u006d\u002f\u006f\u0066fi\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0036\u002f\u0077\u006f\u0072\u0064\u006d\u006c\u002f\u0063\u0069\u0064","\u0043\u0054\u005fC\u006f\u006d\u006d\u0065\u006e\u0074\u0049\u0064",NewCT_CommentId );
_gf .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068e\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002ec\u006f\u006d\u002f\u006f\u0066fi\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0036\u002f\u0077\u006f\u0072\u0064\u006d\u006c\u002f\u0063\u0069\u0064","c\u006f\u006d\u006d\u0065\u006e\u0074\u0073\u0049\u0064\u0073",NewCommentsIds );
_gf .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068e\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002ec\u006f\u006d\u002f\u006f\u0066fi\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0036\u002f\u0077\u006f\u0072\u0064\u006d\u006c\u002f\u0063\u0069\u0064","\u0064\u0065c\u0069\u006d\u0061l\u0064\u0075\u0072\u0061\u0062\u006c\u0065\u0049\u0064",NewDecimaldurableId );
};