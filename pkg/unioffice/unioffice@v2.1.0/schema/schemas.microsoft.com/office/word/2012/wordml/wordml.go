//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package wordml ;import (_g "encoding/xml";_df "fmt";_f "github.com/unidoc/unioffice/v2";_dc "github.com/unidoc/unioffice/v2/common/logger";_e "github.com/unidoc/unioffice/v2/schema/soo/dml";_gg "github.com/unidoc/unioffice/v2/schema/soo/ofc/sharedTypes";
_dg "github.com/unidoc/unioffice/v2/schema/soo/wml";_gc "regexp";_b "time";);

// ValidateWithPath validates the CT_Guid and its children, prefixing error messages with path
func (_ea *CT_Guid )ValidateWithPath (path string )error {if _ea .ValAttr !=nil {if !ST_GuidPatternRe .MatchString (*_ea .ValAttr ){return _df .Errorf ("\u0025\u0073\u002f\u006d\u002e\u0056\u0061\u006c\u0041\u0074\u0074\u0072\u0020\u006d\u0075\u0073\u0074\u0020\u006d\u0061\u0074\u0063\u0068\u0020'\u0025\u0073\u0027\u0020\u0028h\u0061\u0076e\u0020\u0025\u0076\u0029",path ,ST_GuidPatternRe ,*_ea .ValAttr );
};};return nil ;};func NewCT_SdtRepeatedSection ()*CT_SdtRepeatedSection {_efae :=&CT_SdtRepeatedSection {};return _efae ;};func ParseUnionST_Percentage (s string )(_e .ST_Percentage ,error ){return _e .ParseUnionST_Percentage (s );};

// Validate validates the CT_CommentEx and its children
func (_dgc *CT_CommentEx )Validate ()error {return _dgc .ValidateWithPath ("\u0043\u0054\u005fC\u006f\u006d\u006d\u0065\u006e\u0074\u0045\u0078");};type FootnoteColumns struct{_dg .CT_DecimalNumber };func NewCT_Guid ()*CT_Guid {_ba :=&CT_Guid {};return _ba };
type CommentsEx struct{CT_CommentsEx };func NewCT_SdtAppearance ()*CT_SdtAppearance {_fcfc :=&CT_SdtAppearance {};return _fcfc };func NewPeople ()*People {_acf :=&People {};_acf .CT_People =*NewCT_People ();return _acf };func (_dgad *CT_SdtRepeatedSection )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {_ddcd :for {_ffb ,_aga :=d .Token ();
if _aga !=nil {return _aga ;};switch _dgb :=_ffb .(type ){case _g .StartElement :switch _dgb .Name {case _g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0032\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u0073\u0065\u0063t\u0069\u006f\u006e\u0054\u0069\u0074\u006c\u0065"}:_dgad .SectionTitle =_dg .NewCT_String ();
if _dcd :=d .DecodeElement (_dgad .SectionTitle ,&_dgb );_dcd !=nil {return _dcd ;};case _g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0032\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u0064\u006fN\u006f\u0074\u0041\u006c\u006c\u006f\u0077\u0049\u006e\u0073\u0065\u0072\u0074\u0044\u0065\u006c\u0065\u0074\u0065\u0053\u0065\u0063ti\u006f\u006e"}:_dgad .DoNotAllowInsertDeleteSection =_dg .NewCT_OnOff ();
if _gade :=d .DecodeElement (_dgad .DoNotAllowInsertDeleteSection ,&_dgb );_gade !=nil {return _gade ;};default:_dc .Log .Debug ("\u0073\u006b\u0069\u0070\u0070\u0069n\u0067\u0020\u0075n\u0073\u0075\u0070p\u006f\u0072\u0074\u0065\u0064\u0020\u0065\u006c\u0065\u006de\u006e\u0074\u0020\u006f\u006e C\u0054\u005f\u0053\u0064\u0074\u0052\u0065\u0070\u0065\u0061\u0074\u0065\u0064\u0053\u0065\u0063\u0074\u0069\u006f\u006e\u0020\u0025\u0076",_dgb .Name );
if _bce :=d .Skip ();_bce !=nil {return _bce ;};};case _g .EndElement :break _ddcd ;case _g .CharData :};};return nil ;};

// Validate validates the RepeatingSectionItem and its children
func (_fga *RepeatingSectionItem )Validate ()error {return _fga .ValidateWithPath ("R\u0065p\u0065\u0061\u0074\u0069\u006e\u0067\u0053\u0065c\u0074\u0069\u006f\u006eIt\u0065\u006d");};

// Validate validates the DocId and its children
func (_gdc *DocId )Validate ()error {return _gdc .ValidateWithPath ("\u0044\u006f\u0063I\u0064")};func ParseUnionST_Coordinate (s string )(_e .ST_Coordinate ,error ){return _e .ParseUnionST_Coordinate (s );};type CT_Guid struct{ValAttr *string ;};

// Validate validates the RepeatingSection and its children
func (_agecb *RepeatingSection )Validate ()error {return _agecb .ValidateWithPath ("\u0052\u0065p\u0065\u0061\u0074i\u006e\u0067\u0053\u0065\u0063\u0074\u0069\u006f\u006e");};func (_afee ST_SdtAppearance )MarshalXMLAttr (name _g .Name )(_g .Attr ,error ){_ggf :=_g .Attr {};
_ggf .Name =name ;switch _afee {case ST_SdtAppearanceUnset :_ggf .Value ="";case ST_SdtAppearanceBoundingBox :_ggf .Value ="b\u006f\u0075\u006e\u0064\u0069\u006e\u0067\u0042\u006f\u0078";case ST_SdtAppearanceTags :_ggf .Value ="\u0074\u0061\u0067\u0073";
case ST_SdtAppearanceHidden :_ggf .Value ="\u0068\u0069\u0064\u0064\u0065\u006e";};return _ggf ,nil ;};

// Validate validates the ChartTrackingRefBased and its children
func (_deb *ChartTrackingRefBased )Validate ()error {return _deb .ValidateWithPath ("C\u0068\u0061\u0072\u0074Tr\u0061c\u006b\u0069\u006e\u0067\u0052e\u0066\u0042\u0061\u0073\u0065\u0064");};

// ValidateWithPath validates the CT_SdtAppearance and its children, prefixing error messages with path
func (_ddc *CT_SdtAppearance )ValidateWithPath (path string )error {if _fdfa :=_ddc .ValAttr .ValidateWithPath (path +"\u002f\u0056\u0061\u006c\u0041\u0074\u0074\u0072");_fdfa !=nil {return _fdfa ;};return nil ;};func (_ebf *CT_Person )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {for _ ,_fbf :=range start .Attr {if _fbf .Name .Local =="\u0061\u0075\u0074\u0068\u006f\u0072"{_bac :=_fbf .Value ;
_ebf .AuthorAttr =_bac ;continue ;};};_ddf :for {_gcd ,_cf :=d .Token ();if _cf !=nil {return _cf ;};switch _bcb :=_gcd .(type ){case _g .StartElement :switch _bcb .Name {case _g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0032\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u0070\u0072\u0065s\u0065\u006e\u0063\u0065\u0049\u006e\u0066\u006f"}:_ebf .PresenceInfo =NewCT_PresenceInfo ();
if _aggf :=d .DecodeElement (_ebf .PresenceInfo ,&_bcb );_aggf !=nil {return _aggf ;};default:_dc .Log .Debug ("\u0073k\u0069\u0070p\u0069\u006e\u0067\u0020u\u006e\u0073\u0075p\u0070\u006f\u0072\u0074\u0065\u0064\u0020\u0065\u006cem\u0065\u006e\u0074 \u006f\u006e \u0043\u0054\u005f\u0050\u0065\u0072s\u006f\u006e \u0025\u0076",_bcb .Name );
if _cgg :=d .Skip ();_cgg !=nil {return _cgg ;};};case _g .EndElement :break _ddf ;case _g .CharData :};};return nil ;};

// Validate validates the CT_SdtAppearance and its children
func (_gcg *CT_SdtAppearance )Validate ()error {return _gcg .ValidateWithPath ("\u0043\u0054_\u0053\u0064\u0074A\u0070\u0070\u0065\u0061\u0072\u0061\u006e\u0063\u0065");};func NewCT_PresenceInfo ()*CT_PresenceInfo {_fbg :=&CT_PresenceInfo {};return _fbg };
type CT_SdtRepeatedSection struct{SectionTitle *_dg .CT_String ;DoNotAllowInsertDeleteSection *_dg .CT_OnOff ;};func (_ddg *CT_SdtAppearance )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {if _ddg .ValAttr !=ST_SdtAppearanceUnset {_ddgb ,_gb :=_ddg .ValAttr .MarshalXMLAttr (_g .Name {Local :"\u0077\u006f\u003a\u0076\u0061\u006c"});
if _gb !=nil {return _gb ;};start .Attr =append (start .Attr ,_ddgb );};e .EncodeToken (start );e .EncodeToken (_g .EndElement {Name :start .Name });return nil ;};func NewRepeatingSectionItem ()*RepeatingSectionItem {_ebg :=&RepeatingSectionItem {};_ebg .CT_Empty =*_dg .NewCT_Empty ();
return _ebg ;};const (ST_SdtAppearanceUnset ST_SdtAppearance =0;ST_SdtAppearanceBoundingBox ST_SdtAppearance =1;ST_SdtAppearanceTags ST_SdtAppearance =2;ST_SdtAppearanceHidden ST_SdtAppearance =3;);func NewChartTrackingRefBased ()*ChartTrackingRefBased {_bcc :=&ChartTrackingRefBased {};
_bcc .CT_OnOff =*_dg .NewCT_OnOff ();return _bcc ;};func (_fcef *People )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {_fcef .CT_People =*NewCT_People ();_dbb :for {_daa ,_gag :=d .Token ();if _gag !=nil {return _gag ;};switch _ddfe :=_daa .(type ){case _g .StartElement :switch _ddfe .Name {case _g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0032\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u0070\u0065\u0072\u0073\u006f\u006e"}:_efdd :=NewCT_Person ();
if _ffc :=d .DecodeElement (_efdd ,&_ddfe );_ffc !=nil {return _ffc ;};_fcef .Person =append (_fcef .Person ,_efdd );default:_dc .Log .Debug ("\u0073\u006b\u0069\u0070\u0070i\u006e\u0067\u0020\u0075\u006e\u0073\u0075\u0070\u0070\u006f\u0072\u0074\u0065d\u0020\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0050\u0065\u006f\u0070\u006c\u0065\u0020\u0025\u0076",_ddfe .Name );
if _dbgd :=d .Skip ();_dbgd !=nil {return _dbgd ;};};case _g .EndElement :break _dbb ;case _g .CharData :};};return nil ;};func (_fb *CT_CommentEx )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {for _ ,_fgb :=range start .Attr {if _fgb .Name .Local =="\u0070\u0061\u0072\u0061\u0049\u0064"{_ed :=_fgb .Value ;
_fb .ParaIdAttr =_ed ;continue ;};if _fgb .Name .Local =="\u0070\u0061\u0072a\u0049\u0064\u0050\u0061\u0072\u0065\u006e\u0074"{_dgg :=_fgb .Value ;_fb .ParaIdParentAttr =&_dgg ;continue ;};if _fgb .Name .Local =="\u0064\u006f\u006e\u0065"{_agg ,_be :=ParseUnionST_OnOff (_fgb .Value );
if _be !=nil {return _be ;};_fb .DoneAttr =&_agg ;continue ;};};for {_eca ,_af :=d .Token ();if _af !=nil {return _df .Errorf ("\u0070a\u0072\u0073\u0069\u006e\u0067\u0020\u0043\u0054\u005f\u0043\u006fm\u006d\u0065\u006e\u0074\u0045\u0078\u003a\u0020\u0025\u0073",_af );
};if _ad ,_ee :=_eca .(_g .EndElement );_ee &&_ad .Name ==start .Name {break ;};};return nil ;};

// ValidateWithPath validates the CommentsEx and its children, prefixing error messages with path
func (_bdg *CommentsEx )ValidateWithPath (path string )error {if _ab :=_bdg .CT_CommentsEx .ValidateWithPath (path );_ab !=nil {return _ab ;};return nil ;};func (_ca *CT_CommentEx )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {start .Attr =append (start .Attr ,_g .Attr {Name :_g .Name {Local :"\u0077o\u003a\u0070\u0061\u0072\u0061\u0049d"},Value :_df .Sprintf ("\u0025\u0076",_ca .ParaIdAttr )});
if _ca .ParaIdParentAttr !=nil {start .Attr =append (start .Attr ,_g .Attr {Name :_g .Name {Local :"\u0077o\u003ap\u0061\u0072\u0061\u0049\u0064\u0050\u0061\u0072\u0065\u006e\u0074"},Value :_df .Sprintf ("\u0025\u0076",*_ca .ParaIdParentAttr )});};if _ca .DoneAttr !=nil {start .Attr =append (start .Attr ,_g .Attr {Name :_g .Name {Local :"\u0077o\u003a\u0064\u006f\u006e\u0065"},Value :_df .Sprintf ("\u0025\u0076",*_ca .DoneAttr )});
};e .EncodeToken (start );e .EncodeToken (_g .EndElement {Name :start .Name });return nil ;};

// Validate validates the WebExtensionCreated and its children
func (_dfda *WebExtensionCreated )Validate ()error {return _dfda .ValidateWithPath ("\u0057\u0065\u0062\u0045xt\u0065\u006e\u0073\u0069\u006f\u006e\u0043\u0072\u0065\u0061\u0074\u0065\u0064");};func ParseUnionST_Coordinate32 (s string )(_e .ST_Coordinate32 ,error ){return _e .ParseUnionST_Coordinate32 (s );
};func (_ggg *Color )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {_ggg .CT_Color =*_dg .NewCT_Color ();for {_ebe ,_eeb :=d .Token ();if _eeb !=nil {return _df .Errorf ("\u0070\u0061\u0072\u0073\u0069\u006e\u0067\u0020\u0043\u006f\u006c\u006fr\u003a\u0020\u0025\u0073",_eeb );
};if _ebc ,_ffef :=_ebe .(_g .EndElement );_ffef &&_ebc .Name ==start .Name {break ;};};return nil ;};type People struct{CT_People };

// ValidateWithPath validates the ChartTrackingRefBased and its children, prefixing error messages with path
func (_ace *ChartTrackingRefBased )ValidateWithPath (path string )error {if _gac :=_ace .CT_OnOff .ValidateWithPath (path );_gac !=nil {return _gac ;};return nil ;};func (_aff *ChartTrackingRefBased )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {start .Attr =append (start .Attr ,_g .Attr {Name :_g .Name {Local :"\u0078\u006d\u006cn\u0073"},Value :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0032\u002f\u0077\u006f\u0072\u0064\u006d\u006c"});
start .Attr =append (start .Attr ,_g .Attr {Name :_g .Name {Local :"\u0078\u006d\u006c\u006e\u0073\u003a\u0073\u0068"},Value :"\u0068\u0074\u0074\u0070\u003a/\u002f\u0073\u0063\u0068\u0065m\u0061s\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067/\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0068\u0061\u0072e\u0064\u0054\u0079\u0070\u0065\u0073"});
start .Attr =append (start .Attr ,_g .Attr {Name :_g .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0077"},Value :"ht\u0074\u0070:\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073.\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0077\u006f\u0072\u0064\u0070\u0072\u006f\u0063\u0065s\u0073i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u00306\u002fm\u0061\u0069n"});
start .Attr =append (start .Attr ,_g .Attr {Name :_g .Name {Local :"\u0078\u006d\u006c\u006e\u0073\u003a\u0077\u006f"},Value :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0032\u002f\u0077\u006f\u0072\u0064\u006d\u006c"});
start .Attr =append (start .Attr ,_g .Attr {Name :_g .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0078\u006dl"},Value :"\u0068\u0074tp\u003a\u002f\u002fw\u0077\u0077\u002e\u00773.o\u0072g/\u0058\u004d\u004c\u002f\u0031\u0039\u00398/\u006e\u0061\u006d\u0065\u0073\u0070\u0061c\u0065"});
start .Name .Local ="\u0077o\u003a\u0063\u0068\u0061\u0072\u0074\u0054\u0072\u0061\u0063\u006bi\u006e\u0067\u0052\u0065\u0066\u0042\u0061\u0073\u0065\u0064";return _aff .CT_OnOff .MarshalXML (e ,start );};func NewCT_CommentsEx ()*CT_CommentsEx {_da :=&CT_CommentsEx {};
return _da };func (_ead *DataBinding )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {_ead .CT_DataBinding =*_dg .NewCT_DataBinding ();for {_abe ,_beb :=d .Token ();if _beb !=nil {return _df .Errorf ("\u0070\u0061\u0072si\u006e\u0067\u0020\u0044\u0061\u0074\u0061\u0042\u0069\u006e\u0064\u0069\u006e\u0067\u003a\u0020\u0025\u0073",_beb );
};if _bdf ,_gee :=_abe .(_g .EndElement );_gee &&_bdf .Name ==start .Name {break ;};};return nil ;};

// ValidateWithPath validates the CT_Person and its children, prefixing error messages with path
func (_eec *CT_Person )ValidateWithPath (path string )error {if _eec .PresenceInfo !=nil {if _fca :=_eec .PresenceInfo .ValidateWithPath (path +"\u002f\u0050\u0072\u0065\u0073\u0065\u006e\u0063\u0065\u0049\u006e\u0066\u006f");_fca !=nil {return _fca ;};
};return nil ;};

// ValidateWithPath validates the CT_People and its children, prefixing error messages with path
func (_gfbg *CT_People )ValidateWithPath (path string )error {for _gga ,_faf :=range _gfbg .Person {if _gca :=_faf .ValidateWithPath (_df .Sprintf ("\u0025\u0073\u002f\u0050\u0065\u0072\u0073\u006f\u006e\u005b\u0025\u0064\u005d",path ,_gga ));_gca !=nil {return _gca ;
};};return nil ;};

// ValidateWithPath validates the Collapsed and its children, prefixing error messages with path
func (_fgf *Collapsed )ValidateWithPath (path string )error {if _bd :=_fgf .CT_OnOff .ValidateWithPath (path );_bd !=nil {return _bd ;};return nil ;};func (_ddb *DocId )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {_ddb .CT_Guid =*NewCT_Guid ();
for _ ,_ebed :=range start .Attr {if _ebed .Name .Local =="\u0076\u0061\u006c"{_dab :=_ebed .Value ;_ddb .ValAttr =&_dab ;continue ;};};for {_bgg ,_acea :=d .Token ();if _acea !=nil {return _df .Errorf ("\u0070\u0061\u0072\u0073\u0069\u006e\u0067\u0020\u0044\u006f\u0063\u0049d\u003a\u0020\u0025\u0073",_acea );
};if _geg ,_dbf :=_bgg .(_g .EndElement );_dbf &&_geg .Name ==start .Name {break ;};};return nil ;};func NewRepeatingSection ()*RepeatingSection {_bfac :=&RepeatingSection {};_bfac .CT_SdtRepeatedSection =*NewCT_SdtRepeatedSection ();return _bfac ;};

// Validate validates the CT_Guid and its children
func (_bad *CT_Guid )Validate ()error {return _bad .ValidateWithPath ("\u0043T\u005f\u0047\u0075\u0069\u0064");};type Appearance struct{CT_SdtAppearance };

// ValidateWithPath validates the DocId and its children, prefixing error messages with path
func (_bade *DocId )ValidateWithPath (path string )error {if _ebd :=_bade .CT_Guid .ValidateWithPath (path );_ebd !=nil {return _ebd ;};return nil ;};

// Validate validates the DataBinding and its children
func (_gfde *DataBinding )Validate ()error {return _gfde .ValidateWithPath ("D\u0061\u0074\u0061\u0042\u0069\u006e\u0064\u0069\u006e\u0067");};func (_eda *CT_Person )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {start .Attr =append (start .Attr ,_g .Attr {Name :_g .Name {Local :"\u0077o\u003a\u0061\u0075\u0074\u0068\u006fr"},Value :_df .Sprintf ("\u0025\u0076",_eda .AuthorAttr )});
e .EncodeToken (start );if _eda .PresenceInfo !=nil {_fdfd :=_g .StartElement {Name :_g .Name {Local :"\u0077o\u003ap\u0072\u0065\u0073\u0065\u006e\u0063\u0065\u0049\u006e\u0066\u006f"}};e .EncodeElement (_eda .PresenceInfo ,_fdfd );};e .EncodeToken (_g .EndElement {Name :start .Name });
return nil ;};

// Validate validates the CommentsEx and its children
func (_ccc *CommentsEx )Validate ()error {return _ccc .ValidateWithPath ("\u0043\u006f\u006d\u006d\u0065\u006e\u0074\u0073\u0045\u0078");};func ParseUnionST_PositiveFixedPercentage (s string )(_e .ST_PositiveFixedPercentage ,error ){return _e .ParseUnionST_PositiveFixedPercentage (s );
};func (_fafc *RepeatingSectionItem )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {start .Attr =append (start .Attr ,_g .Attr {Name :_g .Name {Local :"\u0078\u006d\u006cn\u0073"},Value :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0032\u002f\u0077\u006f\u0072\u0064\u006d\u006c"});
start .Attr =append (start .Attr ,_g .Attr {Name :_g .Name {Local :"\u0078\u006d\u006c\u006e\u0073\u003a\u0073\u0068"},Value :"\u0068\u0074\u0074\u0070\u003a/\u002f\u0073\u0063\u0068\u0065m\u0061s\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067/\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0068\u0061\u0072e\u0064\u0054\u0079\u0070\u0065\u0073"});
start .Attr =append (start .Attr ,_g .Attr {Name :_g .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0077"},Value :"ht\u0074\u0070:\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073.\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0077\u006f\u0072\u0064\u0070\u0072\u006f\u0063\u0065s\u0073i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u00306\u002fm\u0061\u0069n"});
start .Attr =append (start .Attr ,_g .Attr {Name :_g .Name {Local :"\u0078\u006d\u006c\u006e\u0073\u003a\u0077\u006f"},Value :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0032\u002f\u0077\u006f\u0072\u0064\u006d\u006c"});
start .Attr =append (start .Attr ,_g .Attr {Name :_g .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0078\u006dl"},Value :"\u0068\u0074tp\u003a\u002f\u002fw\u0077\u0077\u002e\u00773.o\u0072g/\u0058\u004d\u004c\u002f\u0031\u0039\u00398/\u006e\u0061\u006d\u0065\u0073\u0070\u0061c\u0065"});
start .Name .Local ="\u0077\u006f\u003are\u0070\u0065\u0061\u0074\u0069\u006e\u0067\u0053\u0065\u0063\u0074\u0069\u006f\u006e\u0049\u0074\u0065\u006d";return _fafc .CT_Empty .MarshalXML (e ,start );};

// ValidateWithPath validates the RepeatingSectionItem and its children, prefixing error messages with path
func (_aaa *RepeatingSectionItem )ValidateWithPath (path string )error {if _beba :=_aaa .CT_Empty .ValidateWithPath (path );_beba !=nil {return _beba ;};return nil ;};var ST_GuidPatternRe =_gc .MustCompile (ST_GuidPattern );

// ValidateWithPath validates the CT_SdtRepeatedSection and its children, prefixing error messages with path
func (_eea *CT_SdtRepeatedSection )ValidateWithPath (path string )error {if _eea .SectionTitle !=nil {if _bbb :=_eea .SectionTitle .ValidateWithPath (path +"\u002f\u0053\u0065\u0063\u0074\u0069\u006f\u006e\u0054\u0069\u0074\u006c\u0065");_bbb !=nil {return _bbb ;
};};if _eea .DoNotAllowInsertDeleteSection !=nil {if _efe :=_eea .DoNotAllowInsertDeleteSection .ValidateWithPath (path +"\u002f\u0044\u006f\u004e\u006f\u0074\u0041\u006c\u006c\u006fw\u0049\u006e\u0073\u0065\u0072\u0074\u0044e\u006c\u0065\u0074\u0065\u0053\u0065\u0063\u0074\u0069\u006f\u006e");
_efe !=nil {return _efe ;};};return nil ;};func ParseUnionST_AdjAngle (s string )(_e .ST_AdjAngle ,error ){return _e .ParseUnionST_AdjAngle (s )};

// ValidateWithPath validates the CT_PresenceInfo and its children, prefixing error messages with path
func (_ecc *CT_PresenceInfo )ValidateWithPath (path string )error {return nil };func (_c *Appearance )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {_c .CT_SdtAppearance =*NewCT_SdtAppearance ();for _ ,_cc :=range start .Attr {if _cc .Name .Local =="\u0076\u0061\u006c"{_c .ValAttr .UnmarshalXMLAttr (_cc );
continue ;};};for {_ec ,_ggc :=d .Token ();if _ggc !=nil {return _df .Errorf ("\u0070\u0061\u0072\u0073in\u0067\u0020\u0041\u0070\u0070\u0065\u0061\u0072\u0061\u006e\u0063\u0065\u003a\u0020%\u0073",_ggc );};if _ae ,_cg :=_ec .(_g .EndElement );_cg &&_ae .Name ==start .Name {break ;
};};return nil ;};

// ValidateWithPath validates the DataBinding and its children, prefixing error messages with path
func (_dfa *DataBinding )ValidateWithPath (path string )error {if _fcaf :=_dfa .CT_DataBinding .ValidateWithPath (path );_fcaf !=nil {return _fcaf ;};return nil ;};func (_aaf *ST_SdtAppearance )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {_egg ,_cgc :=d .Token ();
if _cgc !=nil {return _cgc ;};if _dag ,_cgf :=_egg .(_g .EndElement );_cgf &&_dag .Name ==start .Name {*_aaf =1;return nil ;};if _ccd ,_gef :=_egg .(_g .CharData );!_gef {return _df .Errorf ("\u0065\u0078\u0070\u0065\u0063\u0074\u0065\u0064\u0020\u0063\u0068a\u0072\u0020\u0064\u0061\u0074\u0061\u002c\u0020\u0067\u006ft\u0020\u0025\u0054",_egg );
}else {switch string (_ccd ){case "":*_aaf =0;case "b\u006f\u0075\u006e\u0064\u0069\u006e\u0067\u0042\u006f\u0078":*_aaf =1;case "\u0074\u0061\u0067\u0073":*_aaf =2;case "\u0068\u0069\u0064\u0064\u0065\u006e":*_aaf =3;};};_egg ,_cgc =d .Token ();if _cgc !=nil {return _cgc ;
};if _dcf ,_deg :=_egg .(_g .EndElement );_deg &&_dcf .Name ==start .Name {return nil ;};return _df .Errorf ("\u0065\u0078\u0070\u0065c\u0074\u0065\u0064\u0020\u0065\u006e\u0064\u0020\u0065\u006ce\u006de\u006e\u0074\u002c\u0020\u0067\u006f\u0074 \u0025\u0076",_egg );
};

// Validate validates the CT_People and its children
func (_bg *CT_People )Validate ()error {return _bg .ValidateWithPath ("\u0043T\u005f\u0050\u0065\u006f\u0070\u006ce");};type Any interface{MarshalXML (_bedb *_g .Encoder ,_bab _g .StartElement )error ;UnmarshalXML (_faa *_g .Decoder ,_bbbc _g .StartElement )error ;
};func NewDocId ()*DocId {_gead :=&DocId {};_gead .CT_Guid =*NewCT_Guid ();return _gead };func NewDataBinding ()*DataBinding {_bacb :=&DataBinding {};_bacb .CT_DataBinding =*_dg .NewCT_DataBinding ();return _bacb ;};func (_fc *CT_CommentsEx )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {e .EncodeToken (start );
if _fc .CommentEx !=nil {_ecd :=_g .StartElement {Name :_g .Name {Local :"\u0077\u006f\u003ac\u006f\u006d\u006d\u0065\u006e\u0074\u0045\u0078"}};for _ ,_db :=range _fc .CommentEx {e .EncodeElement (_db ,_ecd );};};e .EncodeToken (_g .EndElement {Name :start .Name });
return nil ;};func ParseUnionST_AnimationChartBuildType (s string )(_e .ST_AnimationChartBuildType ,error ){return _e .ParseUnionST_AnimationChartBuildType (s );};func (_ddec *RepeatingSection )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {_ddec .CT_SdtRepeatedSection =*NewCT_SdtRepeatedSection ();
_bgc :for {_gdf ,_acfb :=d .Token ();if _acfb !=nil {return _acfb ;};switch _bcg :=_gdf .(type ){case _g .StartElement :switch _bcg .Name {case _g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0032\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u0073\u0065\u0063t\u0069\u006f\u006e\u0054\u0069\u0074\u006c\u0065"}:_ddec .SectionTitle =_dg .NewCT_String ();
if _aac :=d .DecodeElement (_ddec .SectionTitle ,&_bcg );_aac !=nil {return _aac ;};case _g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0032\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u0064\u006fN\u006f\u0074\u0041\u006c\u006c\u006f\u0077\u0049\u006e\u0073\u0065\u0072\u0074\u0044\u0065\u006c\u0065\u0074\u0065\u0053\u0065\u0063ti\u006f\u006e"}:_ddec .DoNotAllowInsertDeleteSection =_dg .NewCT_OnOff ();
if _bbe :=d .DecodeElement (_ddec .DoNotAllowInsertDeleteSection ,&_bcg );_bbe !=nil {return _bbe ;};default:_dc .Log .Debug ("\u0073\u006b\u0069\u0070\u0070i\u006e\u0067\u0020\u0075\u006e\u0073\u0075\u0070\u0070\u006f\u0072\u0074\u0065d\u0020\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0052\u0065\u0070\u0065\u0061\u0074\u0069\u006e\u0067\u0053\u0065\u0063\u0074\u0069\u006f\u006e\u0020\u0025v",_bcg .Name );
if _efec :=d .Skip ();_efec !=nil {return _efec ;};};case _g .EndElement :break _bgc ;case _g .CharData :};};return nil ;};func (_dfg ST_SdtAppearance )Validate ()error {return _dfg .ValidateWithPath ("")};type WebExtensionCreated struct{_dg .CT_OnOff };
func (_agad *RepeatingSection )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {start .Attr =append (start .Attr ,_g .Attr {Name :_g .Name {Local :"\u0078\u006d\u006cn\u0073"},Value :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0032\u002f\u0077\u006f\u0072\u0064\u006d\u006c"});
start .Attr =append (start .Attr ,_g .Attr {Name :_g .Name {Local :"\u0078\u006d\u006c\u006e\u0073\u003a\u0073\u0068"},Value :"\u0068\u0074\u0074\u0070\u003a/\u002f\u0073\u0063\u0068\u0065m\u0061s\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067/\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0068\u0061\u0072e\u0064\u0054\u0079\u0070\u0065\u0073"});
start .Attr =append (start .Attr ,_g .Attr {Name :_g .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0077"},Value :"ht\u0074\u0070:\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073.\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0077\u006f\u0072\u0064\u0070\u0072\u006f\u0063\u0065s\u0073i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u00306\u002fm\u0061\u0069n"});
start .Attr =append (start .Attr ,_g .Attr {Name :_g .Name {Local :"\u0078\u006d\u006c\u006e\u0073\u003a\u0077\u006f"},Value :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0032\u002f\u0077\u006f\u0072\u0064\u006d\u006c"});
start .Attr =append (start .Attr ,_g .Attr {Name :_g .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0078\u006dl"},Value :"\u0068\u0074tp\u003a\u002f\u002fw\u0077\u0077\u002e\u00773.o\u0072g/\u0058\u004d\u004c\u002f\u0031\u0039\u00398/\u006e\u0061\u006d\u0065\u0073\u0070\u0061c\u0065"});
start .Name .Local ="\u0077\u006f\u003a\u0072ep\u0065\u0061\u0074\u0069\u006e\u0067\u0053\u0065\u0063\u0074\u0069\u006f\u006e";return _agad .CT_SdtRepeatedSection .MarshalXML (e ,start );};func (_gfg *CommentsEx )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {_gfg .CT_CommentsEx =*NewCT_CommentsEx ();
_debg :for {_aa ,_bed :=d .Token ();if _bed !=nil {return _bed ;};switch _eaa :=_aa .(type ){case _g .StartElement :switch _eaa .Name {case _g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0032\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u0063o\u006d\u006d\u0065\u006e\u0074\u0045x"}:_cac :=NewCT_CommentEx ();
if _ggb :=d .DecodeElement (_cac ,&_eaa );_ggb !=nil {return _ggb ;};_gfg .CommentEx =append (_gfg .CommentEx ,_cac );default:_dc .Log .Debug ("\u0073k\u0069\u0070p\u0069\u006e\u0067 \u0075\u006e\u0073\u0075\u0070\u0070\u006fr\u0074\u0065\u0064\u0020\u0065\u006ce\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0043\u006f\u006dm\u0065\u006e\u0074\u0073\u0045\u0078\u0020\u0025\u0076",_eaa .Name );
if _agec :=d .Skip ();_agec !=nil {return _agec ;};};case _g .EndElement :break _debg ;case _g .CharData :};};return nil ;};

// ValidateWithPath validates the RepeatingSection and its children, prefixing error messages with path
func (_ebda *RepeatingSection )ValidateWithPath (path string )error {if _bcbd :=_ebda .CT_SdtRepeatedSection .ValidateWithPath (path );_bcbd !=nil {return _bcbd ;};return nil ;};func (_ceg *CT_PresenceInfo )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {start .Attr =append (start .Attr ,_g .Attr {Name :_g .Name {Local :"\u0077\u006f\u003a\u0070\u0072\u006f\u0076\u0069\u0064\u0065\u0072\u0049\u0064"},Value :_df .Sprintf ("\u0025\u0076",_ceg .ProviderIdAttr )});
start .Attr =append (start .Attr ,_g .Attr {Name :_g .Name {Local :"\u0077o\u003a\u0075\u0073\u0065\u0072\u0049d"},Value :_df .Sprintf ("\u0025\u0076",_ceg .UserIdAttr )});e .EncodeToken (start );e .EncodeToken (_g .EndElement {Name :start .Name });return nil ;
};

// ValidateWithPath validates the CT_CommentEx and its children, prefixing error messages with path
func (_ce *CT_CommentEx )ValidateWithPath (path string )error {if _ce .DoneAttr !=nil {if _fd :=_ce .DoneAttr .ValidateWithPath (path +"\u002fD\u006f\u006e\u0065\u0041\u0074\u0074r");_fd !=nil {return _fd ;};};return nil ;};func NewCollapsed ()*Collapsed {_ege :=&Collapsed {};
_ege .CT_OnOff =*_dg .NewCT_OnOff ();return _ege };func (_ecdc *CT_People )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {e .EncodeToken (start );if _ecdc .Person !=nil {_ef :=_g .StartElement {Name :_g .Name {Local :"\u0077o\u003a\u0070\u0065\u0072\u0073\u006fn"}};
for _ ,_eef :=range _ecdc .Person {e .EncodeElement (_eef ,_ef );};};e .EncodeToken (_g .EndElement {Name :start .Name });return nil ;};func (_bfa *People )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {start .Attr =append (start .Attr ,_g .Attr {Name :_g .Name {Local :"\u0078\u006d\u006cn\u0073"},Value :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0032\u002f\u0077\u006f\u0072\u0064\u006d\u006c"});
start .Attr =append (start .Attr ,_g .Attr {Name :_g .Name {Local :"\u0078\u006d\u006c\u006e\u0073\u003a\u0073\u0068"},Value :"\u0068\u0074\u0074\u0070\u003a/\u002f\u0073\u0063\u0068\u0065m\u0061s\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067/\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0068\u0061\u0072e\u0064\u0054\u0079\u0070\u0065\u0073"});
start .Attr =append (start .Attr ,_g .Attr {Name :_g .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0077"},Value :"ht\u0074\u0070:\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073.\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0077\u006f\u0072\u0064\u0070\u0072\u006f\u0063\u0065s\u0073i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u00306\u002fm\u0061\u0069n"});
start .Attr =append (start .Attr ,_g .Attr {Name :_g .Name {Local :"\u0078\u006d\u006c\u006e\u0073\u003a\u0077\u006f"},Value :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0032\u002f\u0077\u006f\u0072\u0064\u006d\u006c"});
start .Attr =append (start .Attr ,_g .Attr {Name :_g .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0078\u006dl"},Value :"\u0068\u0074tp\u003a\u002f\u002fw\u0077\u0077\u002e\u00773.o\u0072g/\u0058\u004d\u004c\u002f\u0031\u0039\u00398/\u006e\u0061\u006d\u0065\u0073\u0070\u0061c\u0065"});
start .Name .Local ="\u0077o\u003a\u0070\u0065\u006f\u0070\u006ce";return _bfa .CT_People .MarshalXML (e ,start );};func (_ffe *ChartTrackingRefBased )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {_ffe .CT_OnOff =*_dg .NewCT_OnOff ();for {_edc ,_dbe :=d .Token ();
if _dbe !=nil {return _df .Errorf ("\u0070\u0061\u0072\u0073\u0069\u006eg\u0020\u0043\u0068\u0061\u0072\u0074\u0054\u0072\u0061\u0063\u006b\u0069\u006eg\u0052\u0065\u0066\u0042\u0061\u0073\u0065d\u003a\u0020\u0025\u0073",_dbe );};if _eg ,_bcd :=_edc .(_g .EndElement );
_bcd &&_eg .Name ==start .Name {break ;};};return nil ;};func NewCT_Person ()*CT_Person {_ccg :=&CT_Person {};return _ccg };type WebExtensionLinked struct{_dg .CT_OnOff };func ParseStdlibTime (s string )(_b .Time ,error ){return _gg .ParseStdlibTime (s )};
func ParseUnionST_AnimationDgmBuildType (s string )(_e .ST_AnimationDgmBuildType ,error ){return _e .ParseUnionST_AnimationDgmBuildType (s );};

// ValidateWithPath validates the WebExtensionLinked and its children, prefixing error messages with path
func (_cggb *WebExtensionLinked )ValidateWithPath (path string )error {if _fge :=_cggb .CT_OnOff .ValidateWithPath (path );_fge !=nil {return _fge ;};return nil ;};func (_fbb *Collapsed )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {_fbb .CT_OnOff =*_dg .NewCT_OnOff ();
for {_age ,_geb :=d .Token ();if _geb !=nil {return _df .Errorf ("p\u0061\u0072\u0073\u0069ng\u0020C\u006f\u006c\u006c\u0061\u0070s\u0065\u0064\u003a\u0020\u0025\u0073",_geb );};if _ccgd ,_gce :=_age .(_g .EndElement );_gce &&_ccgd .Name ==start .Name {break ;
};};return nil ;};func NewCT_People ()*CT_People {_eee :=&CT_People {};return _eee };

// Validate validates the Appearance and its children
func (_ag *Appearance )Validate ()error {return _ag .ValidateWithPath ("\u0041\u0070\u0070\u0065\u0061\u0072\u0061\u006e\u0063\u0065");};

// Validate validates the People and its children
func (_fda *People )Validate ()error {return _fda .ValidateWithPath ("\u0050\u0065\u006f\u0070\u006c\u0065");};func NewWebExtensionLinked ()*WebExtensionLinked {_fac :=&WebExtensionLinked {};_fac .CT_OnOff =*_dg .NewCT_OnOff ();return _fac ;};

// ValidateWithPath validates the CT_CommentsEx and its children, prefixing error messages with path
func (_dffd *CT_CommentsEx )ValidateWithPath (path string )error {for _gge ,_ga :=range _dffd .CommentEx {if _fa :=_ga .ValidateWithPath (_df .Sprintf ("\u0025\u0073/\u0043\u006f\u006dm\u0065\u006e\u0074\u0045\u0078\u005b\u0025\u0064\u005d",path ,_gge ));
_fa !=nil {return _fa ;};};return nil ;};type Collapsed struct{_dg .CT_OnOff };type CT_PresenceInfo struct{ProviderIdAttr string ;UserIdAttr string ;};func (_ede *DocId )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {start .Attr =append (start .Attr ,_g .Attr {Name :_g .Name {Local :"\u0078\u006d\u006cn\u0073"},Value :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0032\u002f\u0077\u006f\u0072\u0064\u006d\u006c"});
start .Attr =append (start .Attr ,_g .Attr {Name :_g .Name {Local :"\u0078\u006d\u006c\u006e\u0073\u003a\u0073\u0068"},Value :"\u0068\u0074\u0074\u0070\u003a/\u002f\u0073\u0063\u0068\u0065m\u0061s\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067/\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0068\u0061\u0072e\u0064\u0054\u0079\u0070\u0065\u0073"});
start .Attr =append (start .Attr ,_g .Attr {Name :_g .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0077"},Value :"ht\u0074\u0070:\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073.\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0077\u006f\u0072\u0064\u0070\u0072\u006f\u0063\u0065s\u0073i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u00306\u002fm\u0061\u0069n"});
start .Attr =append (start .Attr ,_g .Attr {Name :_g .Name {Local :"\u0078\u006d\u006c\u006e\u0073\u003a\u0077\u006f"},Value :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0032\u002f\u0077\u006f\u0072\u0064\u006d\u006c"});
start .Attr =append (start .Attr ,_g .Attr {Name :_g .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0078\u006dl"},Value :"\u0068\u0074tp\u003a\u002f\u002fw\u0077\u0077\u002e\u00773.o\u0072g/\u0058\u004d\u004c\u002f\u0031\u0039\u00398/\u006e\u0061\u006d\u0065\u0073\u0070\u0061c\u0065"});
start .Name .Local ="\u0077\u006f\u003a\u0064\u006f\u0063\u0049\u0064";return _ede .CT_Guid .MarshalXML (e ,start );};type CT_CommentsEx struct{CommentEx []*CT_CommentEx ;};func ParseUnionST_PositivePercentage (s string )(_e .ST_PositivePercentage ,error ){return _e .ParseUnionST_PositivePercentage (s );
};func (_cacb ST_SdtAppearance )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {return e .EncodeElement (_cacb .String (),start );};type Color struct{_dg .CT_Color };func (_dggd *FootnoteColumns )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {_dggd .CT_DecimalNumber =*_dg .NewCT_DecimalNumber ();
for {_eaag ,_dde :=d .Token ();if _dde !=nil {return _df .Errorf ("p\u0061\u0072\u0073\u0069\u006e\u0067 \u0046\u006f\u006f\u0074\u006e\u006f\u0074\u0065\u0043o\u006c\u0075\u006dn\u0073:\u0020\u0025\u0073",_dde );};if _gebc ,_fef :=_eaag .(_g .EndElement );
_fef &&_gebc .Name ==start .Name {break ;};};return nil ;};type CT_SdtAppearance struct{ValAttr ST_SdtAppearance ;};func (_cgd *WebExtensionCreated )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {_cgd .CT_OnOff =*_dg .NewCT_OnOff ();for {_egf ,_gfbgb :=d .Token ();
if _gfbgb !=nil {return _df .Errorf ("\u0070\u0061\u0072s\u0069\u006e\u0067\u0020W\u0065\u0062\u0045\u0078\u0074\u0065\u006es\u0069\u006f\u006e\u0043\u0072\u0065\u0061\u0074\u0065\u0064\u003a\u0020\u0025\u0073",_gfbgb );};if _cbeg ,_cfga :=_egf .(_g .EndElement );
_cfga &&_cbeg .Name ==start .Name {break ;};};return nil ;};func (_ggab *WebExtensionLinked )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {start .Attr =append (start .Attr ,_g .Attr {Name :_g .Name {Local :"\u0078\u006d\u006cn\u0073"},Value :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0032\u002f\u0077\u006f\u0072\u0064\u006d\u006c"});
start .Attr =append (start .Attr ,_g .Attr {Name :_g .Name {Local :"\u0078\u006d\u006c\u006e\u0073\u003a\u0073\u0068"},Value :"\u0068\u0074\u0074\u0070\u003a/\u002f\u0073\u0063\u0068\u0065m\u0061s\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067/\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0068\u0061\u0072e\u0064\u0054\u0079\u0070\u0065\u0073"});
start .Attr =append (start .Attr ,_g .Attr {Name :_g .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0077"},Value :"ht\u0074\u0070:\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073.\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0077\u006f\u0072\u0064\u0070\u0072\u006f\u0063\u0065s\u0073i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u00306\u002fm\u0061\u0069n"});
start .Attr =append (start .Attr ,_g .Attr {Name :_g .Name {Local :"\u0078\u006d\u006c\u006e\u0073\u003a\u0077\u006f"},Value :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0032\u002f\u0077\u006f\u0072\u0064\u006d\u006c"});
start .Attr =append (start .Attr ,_g .Attr {Name :_g .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0078\u006dl"},Value :"\u0068\u0074tp\u003a\u002f\u002fw\u0077\u0077\u002e\u00773.o\u0072g/\u0058\u004d\u004c\u002f\u0031\u0039\u00398/\u006e\u0061\u006d\u0065\u0073\u0070\u0061c\u0065"});
start .Name .Local ="w\u006f\u003a\u0077\u0065bE\u0078t\u0065\u006e\u0073\u0069\u006fn\u004c\u0069\u006e\u006b\u0065\u0064";return _ggab .CT_OnOff .MarshalXML (e ,start );};func NewColor ()*Color {_ffd :=&Color {};_ffd .CT_Color =*_dg .NewCT_Color ();return _ffd };


// ValidateWithPath validates the Color and its children, prefixing error messages with path
func (_fcac *Color )ValidateWithPath (path string )error {if _gda :=_fcac .CT_Color .ValidateWithPath (path );_gda !=nil {return _gda ;};return nil ;};func (_acc *WebExtensionLinked )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {_acc .CT_OnOff =*_dg .NewCT_OnOff ();
for {_bge ,_ggd :=d .Token ();if _ggd !=nil {return _df .Errorf ("\u0070\u0061\u0072\u0073\u0069\u006e\u0067\u0020\u0057\u0065b\u0045\u0078\u0074\u0065\u006e\u0073\u0069o\u006e\u004c\u0069\u006e\u006b\u0065\u0064\u003a\u0020\u0025\u0073",_ggd );};if _aeb ,_efeb :=_bge .(_g .EndElement );
_efeb &&_aeb .Name ==start .Name {break ;};};return nil ;};

// ValidateWithPath validates the FootnoteColumns and its children, prefixing error messages with path
func (_fcg *FootnoteColumns )ValidateWithPath (path string )error {if _dac :=_fcg .CT_DecimalNumber .ValidateWithPath (path );_dac !=nil {return _dac ;};return nil ;};

// Validate validates the CT_PresenceInfo and its children
func (_gad *CT_PresenceInfo )Validate ()error {return _gad .ValidateWithPath ("\u0043T\u005fP\u0072\u0065\u0073\u0065\u006e\u0063\u0065\u0049\u006e\u0066\u006f");};func (_def *CT_SdtAppearance )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {for _ ,_bag :=range start .Attr {if _bag .Name .Local =="\u0076\u0061\u006c"{_def .ValAttr .UnmarshalXMLAttr (_bag );
continue ;};};for {_afe ,_dea :=d .Token ();if _dea !=nil {return _df .Errorf ("\u0070\u0061\u0072\u0073i\u006e\u0067\u0020\u0043\u0054\u005f\u0053\u0064\u0074\u0041p\u0070e\u0061\u0072\u0061\u006e\u0063\u0065\u003a \u0025\u0073",_dea );};if _gfe ,_fbd :=_afe .(_g .EndElement );
_fbd &&_gfe .Name ==start .Name {break ;};};return nil ;};func (_dcda ST_SdtAppearance )ValidateWithPath (path string )error {switch _dcda {case 0,1,2,3:default:return _df .Errorf ("\u0025s\u003a\u0020\u006f\u0075t\u0020\u006f\u0066\u0020\u0072a\u006eg\u0065 \u0076\u0061\u006c\u0075\u0065\u0020\u0025d",path ,int (_dcda ));
};return nil ;};type DocId struct{CT_Guid };const ST_GuidPattern ="\u005c\u007b\u005b\u0030\u002d\u0039\u0041\u002d\u0046\u005d\u007b\u0038\u007d\u002d\u005b\u0030\u002d9\u0041\u002d\u0046\u005d\u007b\u0034\u007d\u002d\u005b\u0030-\u0039\u0041\u002d\u0046\u005d\u007b\u0034\u007d\u002d\u005b\u0030\u002d\u0039\u0041\u002d\u0046\u005d\u007b4\u007d\u002d\u005b\u0030\u002d\u0039A\u002d\u0046]\u007b\u00312\u007d\\\u007d";
func (_dgf *RepeatingSectionItem )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {_dgf .CT_Empty =*_dg .NewCT_Empty ();for {_dae ,_fcb :=d .Token ();if _fcb !=nil {return _df .Errorf ("\u0070\u0061\u0072\u0073\u0069\u006e\u0067\u0020\u0052\u0065\u0070\u0065\u0061\u0074\u0069n\u0067S\u0065\u0063\u0074\u0069\u006f\u006e\u0049\u0074\u0065\u006d\u003a\u0020\u0025\u0073",_fcb );
};if _cfg ,_fag :=_dae .(_g .EndElement );_fag &&_cfg .Name ==start .Name {break ;};};return nil ;};func NewCommentsEx ()*CommentsEx {_geaa :=&CommentsEx {};_geaa .CT_CommentsEx =*NewCT_CommentsEx ();return _geaa ;};

// Validate validates the CT_Person and its children
func (_gd *CT_Person )Validate ()error {return _gd .ValidateWithPath ("\u0043T\u005f\u0050\u0065\u0072\u0073\u006fn");};

// Validate validates the Color and its children
func (_ecda *Color )Validate ()error {return _ecda .ValidateWithPath ("\u0043\u006f\u006co\u0072")};

// ValidateWithPath validates the People and its children, prefixing error messages with path
func (_gfdd *People )ValidateWithPath (path string )error {if _bfea :=_gfdd .CT_People .ValidateWithPath (path );_bfea !=nil {return _bfea ;};return nil ;};

// Validate validates the CT_SdtRepeatedSection and its children
func (_fab *CT_SdtRepeatedSection )Validate ()error {return _fab .ValidateWithPath ("C\u0054\u005f\u0053\u0064tR\u0065p\u0065\u0061\u0074\u0065\u0064S\u0065\u0063\u0074\u0069\u006f\u006e");};func (_fee *WebExtensionCreated )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {start .Attr =append (start .Attr ,_g .Attr {Name :_g .Name {Local :"\u0078\u006d\u006cn\u0073"},Value :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0032\u002f\u0077\u006f\u0072\u0064\u006d\u006c"});
start .Attr =append (start .Attr ,_g .Attr {Name :_g .Name {Local :"\u0078\u006d\u006c\u006e\u0073\u003a\u0073\u0068"},Value :"\u0068\u0074\u0074\u0070\u003a/\u002f\u0073\u0063\u0068\u0065m\u0061s\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067/\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0068\u0061\u0072e\u0064\u0054\u0079\u0070\u0065\u0073"});
start .Attr =append (start .Attr ,_g .Attr {Name :_g .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0077"},Value :"ht\u0074\u0070:\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073.\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0077\u006f\u0072\u0064\u0070\u0072\u006f\u0063\u0065s\u0073i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u00306\u002fm\u0061\u0069n"});
start .Attr =append (start .Attr ,_g .Attr {Name :_g .Name {Local :"\u0078\u006d\u006c\u006e\u0073\u003a\u0077\u006f"},Value :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0032\u002f\u0077\u006f\u0072\u0064\u006d\u006c"});
start .Attr =append (start .Attr ,_g .Attr {Name :_g .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0078\u006dl"},Value :"\u0068\u0074tp\u003a\u002f\u002fw\u0077\u0077\u002e\u00773.o\u0072g/\u0058\u004d\u004c\u002f\u0031\u0039\u00398/\u006e\u0061\u006d\u0065\u0073\u0070\u0061c\u0065"});
start .Name .Local ="\u0077\u006f\u003a\u0077eb\u0045\u0078\u0074\u0065\u006e\u0073\u0069\u006f\u006e\u0043\u0072\u0065\u0061\u0074e\u0064";return _fee .CT_OnOff .MarshalXML (e ,start );};func (_afa *FootnoteColumns )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {start .Attr =append (start .Attr ,_g .Attr {Name :_g .Name {Local :"\u0078\u006d\u006cn\u0073"},Value :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0032\u002f\u0077\u006f\u0072\u0064\u006d\u006c"});
start .Attr =append (start .Attr ,_g .Attr {Name :_g .Name {Local :"\u0078\u006d\u006c\u006e\u0073\u003a\u0073\u0068"},Value :"\u0068\u0074\u0074\u0070\u003a/\u002f\u0073\u0063\u0068\u0065m\u0061s\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067/\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0068\u0061\u0072e\u0064\u0054\u0079\u0070\u0065\u0073"});
start .Attr =append (start .Attr ,_g .Attr {Name :_g .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0077"},Value :"ht\u0074\u0070:\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073.\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0077\u006f\u0072\u0064\u0070\u0072\u006f\u0063\u0065s\u0073i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u00306\u002fm\u0061\u0069n"});
start .Attr =append (start .Attr ,_g .Attr {Name :_g .Name {Local :"\u0078\u006d\u006c\u006e\u0073\u003a\u0077\u006f"},Value :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0032\u002f\u0077\u006f\u0072\u0064\u006d\u006c"});
start .Attr =append (start .Attr ,_g .Attr {Name :_g .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0078\u006dl"},Value :"\u0068\u0074tp\u003a\u002f\u002fw\u0077\u0077\u002e\u00773.o\u0072g/\u0058\u004d\u004c\u002f\u0031\u0039\u00398/\u006e\u0061\u006d\u0065\u0073\u0070\u0061c\u0065"});
start .Name .Local ="\u0077o\u003af\u006f\u006f\u0074\u006e\u006ft\u0065\u0043o\u006c\u0075\u006d\u006e\u0073";return _afa .CT_DecimalNumber .MarshalXML (e ,start );};func ParseUnionST_TextFontScalePercentOrPercentString (s string )(_e .ST_TextFontScalePercentOrPercentString ,error ){return _e .ParseUnionST_TextFontScalePercentOrPercentString (s );
};func (_cbe *Color )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {start .Attr =append (start .Attr ,_g .Attr {Name :_g .Name {Local :"\u0078\u006d\u006cn\u0073"},Value :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0032\u002f\u0077\u006f\u0072\u0064\u006d\u006c"});
start .Attr =append (start .Attr ,_g .Attr {Name :_g .Name {Local :"\u0078\u006d\u006c\u006e\u0073\u003a\u0073\u0068"},Value :"\u0068\u0074\u0074\u0070\u003a/\u002f\u0073\u0063\u0068\u0065m\u0061s\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067/\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0068\u0061\u0072e\u0064\u0054\u0079\u0070\u0065\u0073"});
start .Attr =append (start .Attr ,_g .Attr {Name :_g .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0077"},Value :"ht\u0074\u0070:\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073.\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0077\u006f\u0072\u0064\u0070\u0072\u006f\u0063\u0065s\u0073i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u00306\u002fm\u0061\u0069n"});
start .Attr =append (start .Attr ,_g .Attr {Name :_g .Name {Local :"\u0078\u006d\u006c\u006e\u0073\u003a\u0077\u006f"},Value :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0032\u002f\u0077\u006f\u0072\u0064\u006d\u006c"});
start .Attr =append (start .Attr ,_g .Attr {Name :_g .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0078\u006dl"},Value :"\u0068\u0074tp\u003a\u002f\u002fw\u0077\u0077\u002e\u00773.o\u0072g/\u0058\u004d\u004c\u002f\u0031\u0039\u00398/\u006e\u0061\u006d\u0065\u0073\u0070\u0061c\u0065"});
start .Name .Local ="\u0077\u006f\u003a\u0063\u006f\u006c\u006f\u0072";return _cbe .CT_Color .MarshalXML (e ,start );};func (_fdf *CT_People )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {_gfd :for {_cee ,_dd :=d .Token ();if _dd !=nil {return _dd ;
};switch _gcb :=_cee .(type ){case _g .StartElement :switch _gcb .Name {case _g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0032\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u0070\u0065\u0072\u0073\u006f\u006e"}:_cce :=NewCT_Person ();
if _bf :=d .DecodeElement (_cce ,&_gcb );_bf !=nil {return _bf ;};_fdf .Person =append (_fdf .Person ,_cce );default:_dc .Log .Debug ("\u0073k\u0069\u0070p\u0069\u006e\u0067\u0020u\u006e\u0073\u0075p\u0070\u006f\u0072\u0074\u0065\u0064\u0020\u0065\u006cem\u0065\u006e\u0074 \u006f\u006e \u0043\u0054\u005f\u0050\u0065\u006fp\u006c\u0065 \u0025\u0076",_gcb .Name );
if _gaa :=d .Skip ();_gaa !=nil {return _gaa ;};};case _g .EndElement :break _gfd ;case _g .CharData :};};return nil ;};func ParseUnionST_OnOff (s string )(_gg .ST_OnOff ,error ){return _gg .ST_OnOff {},nil };type DataBinding struct{_dg .CT_DataBinding };
func (_acgg *CT_SdtRepeatedSection )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {e .EncodeToken (start );if _acgg .SectionTitle !=nil {_ecb :=_g .StartElement {Name :_g .Name {Local :"\u0077o\u003as\u0065\u0063\u0074\u0069\u006f\u006e\u0054\u0069\u0074\u006c\u0065"}};
e .EncodeElement (_acgg .SectionTitle ,_ecb );};if _acgg .DoNotAllowInsertDeleteSection !=nil {_ade :=_g .StartElement {Name :_g .Name {Local :"\u0077\u006f\u003a\u0064\u006f\u004e\u006f\u0074\u0041\u006c\u006c\u006f\u0077\u0049\u006es\u0065r\u0074\u0044\u0065\u006c\u0065\u0074\u0065\u0053\u0065\u0063\u0074\u0069\u006f\u006e"}};
e .EncodeElement (_acgg .DoNotAllowInsertDeleteSection ,_ade );};e .EncodeToken (_g .EndElement {Name :start .Name });return nil ;};func _aad (_bfd bool )uint8 {if _bfd {return 1;};return 0;};func NewFootnoteColumns ()*FootnoteColumns {_edd :=&FootnoteColumns {};
_edd .CT_DecimalNumber =*_dg .NewCT_DecimalNumber ();return _edd ;};func ParseUnionST_FixedPercentage (s string )(_e .ST_FixedPercentage ,error ){return _e .ParseUnionST_FixedPercentage (s );};func NewWebExtensionCreated ()*WebExtensionCreated {_feg :=&WebExtensionCreated {};
_feg .CT_OnOff =*_dg .NewCT_OnOff ();return _feg ;};func (_cad *DataBinding )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {start .Attr =append (start .Attr ,_g .Attr {Name :_g .Name {Local :"\u0078\u006d\u006cn\u0073"},Value :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0032\u002f\u0077\u006f\u0072\u0064\u006d\u006c"});
start .Attr =append (start .Attr ,_g .Attr {Name :_g .Name {Local :"\u0078\u006d\u006c\u006e\u0073\u003a\u0073\u0068"},Value :"\u0068\u0074\u0074\u0070\u003a/\u002f\u0073\u0063\u0068\u0065m\u0061s\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067/\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0068\u0061\u0072e\u0064\u0054\u0079\u0070\u0065\u0073"});
start .Attr =append (start .Attr ,_g .Attr {Name :_g .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0077"},Value :"ht\u0074\u0070:\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073.\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0077\u006f\u0072\u0064\u0070\u0072\u006f\u0063\u0065s\u0073i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u00306\u002fm\u0061\u0069n"});
start .Attr =append (start .Attr ,_g .Attr {Name :_g .Name {Local :"\u0078\u006d\u006c\u006e\u0073\u003a\u0077\u006f"},Value :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0032\u002f\u0077\u006f\u0072\u0064\u006d\u006c"});
start .Attr =append (start .Attr ,_g .Attr {Name :_g .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0078\u006dl"},Value :"\u0068\u0074tp\u003a\u002f\u002fw\u0077\u0077\u002e\u00773.o\u0072g/\u0058\u004d\u004c\u002f\u0031\u0039\u00398/\u006e\u0061\u006d\u0065\u0073\u0070\u0061c\u0065"});
start .Name .Local ="\u0077\u006f\u003a\u0064\u0061\u0074\u0061\u0042\u0069n\u0064\u0069\u006e\u0067";return _cad .CT_DataBinding .MarshalXML (e ,start );};type CT_Person struct{AuthorAttr string ;PresenceInfo *CT_PresenceInfo ;};type CT_CommentEx struct{ParaIdAttr string ;
ParaIdParentAttr *string ;DoneAttr *_gg .ST_OnOff ;};func (_fcf *CT_Guid )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {if _fcf .ValAttr !=nil {start .Attr =append (start .Attr ,_g .Attr {Name :_g .Name {Local :"\u0077\u006f\u003a\u0076\u0061\u006c"},Value :_df .Sprintf ("\u0025\u0076",*_fcf .ValAttr )});
};e .EncodeToken (start );e .EncodeToken (_g .EndElement {Name :start .Name });return nil ;};

// ValidateWithPath validates the Appearance and its children, prefixing error messages with path
func (_dcb *Appearance )ValidateWithPath (path string )error {if _gf :=_dcb .CT_SdtAppearance .ValidateWithPath (path );_gf !=nil {return _gf ;};return nil ;};type RepeatingSection struct{CT_SdtRepeatedSection };func ParseUnionST_TextPoint (s string )(_e .ST_TextPoint ,error ){return _e .ParseUnionST_TextPoint (s )};
func ParseUnionST_AdjCoordinate (s string )(_e .ST_AdjCoordinate ,error ){return _e .ParseUnionST_AdjCoordinate (s );};

// ValidateWithPath validates the WebExtensionCreated and its children, prefixing error messages with path
func (_cdd *WebExtensionCreated )ValidateWithPath (path string )error {if _ddef :=_cdd .CT_OnOff .ValidateWithPath (path );_ddef !=nil {return _ddef ;};return nil ;};func NewAppearance ()*Appearance {_a :=&Appearance {};_a .CT_SdtAppearance =*NewCT_SdtAppearance ();
return _a ;};func ParseUnionST_TextSpacingPercentOrPercentString (s string )(_e .ST_TextSpacingPercentOrPercentString ,error ){return _e .ParseUnionST_TextSpacingPercentOrPercentString (s );};

// Validate validates the WebExtensionLinked and its children
func (_gff *WebExtensionLinked )Validate ()error {return _gff .ValidateWithPath ("\u0057e\u0062E\u0078\u0074\u0065\u006e\u0073i\u006f\u006eL\u0069\u006e\u006b\u0065\u0064");};func (_bfg *CT_PresenceInfo )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {for _ ,_fce :=range start .Attr {if _fce .Name .Local =="\u0070\u0072\u006f\u0076\u0069\u0064\u0065\u0072\u0049\u0064"{_dce :=_fce .Value ;
_bfg .ProviderIdAttr =_dce ;continue ;};if _fce .Name .Local =="\u0075\u0073\u0065\u0072\u0049\u0064"{_efa :=_fce .Value ;_bfg .UserIdAttr =_efa ;continue ;};};for {_gaf ,_eeeb :=d .Token ();if _eeeb !=nil {return _df .Errorf ("p\u0061\u0072\u0073\u0069\u006e\u0067 \u0043\u0054\u005f\u0050\u0072\u0065\u0073\u0065\u006ec\u0065\u0049\u006ef\u006f:\u0020\u0025\u0073",_eeeb );
};if _bae ,_dga :=_gaf .(_g .EndElement );_dga &&_bae .Name ==start .Name {break ;};};return nil ;};func (_efd *Collapsed )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {start .Attr =append (start .Attr ,_g .Attr {Name :_g .Name {Local :"\u0078\u006d\u006cn\u0073"},Value :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0032\u002f\u0077\u006f\u0072\u0064\u006d\u006c"});
start .Attr =append (start .Attr ,_g .Attr {Name :_g .Name {Local :"\u0078\u006d\u006c\u006e\u0073\u003a\u0073\u0068"},Value :"\u0068\u0074\u0074\u0070\u003a/\u002f\u0073\u0063\u0068\u0065m\u0061s\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067/\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0068\u0061\u0072e\u0064\u0054\u0079\u0070\u0065\u0073"});
start .Attr =append (start .Attr ,_g .Attr {Name :_g .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0077"},Value :"ht\u0074\u0070:\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073.\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0077\u006f\u0072\u0064\u0070\u0072\u006f\u0063\u0065s\u0073i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u00306\u002fm\u0061\u0069n"});
start .Attr =append (start .Attr ,_g .Attr {Name :_g .Name {Local :"\u0078\u006d\u006c\u006e\u0073\u003a\u0077\u006f"},Value :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0032\u002f\u0077\u006f\u0072\u0064\u006d\u006c"});
start .Attr =append (start .Attr ,_g .Attr {Name :_g .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0078\u006dl"},Value :"\u0068\u0074tp\u003a\u002f\u002fw\u0077\u0077\u002e\u00773.o\u0072g/\u0058\u004d\u004c\u002f\u0031\u0039\u00398/\u006e\u0061\u006d\u0065\u0073\u0070\u0061c\u0065"});
start .Name .Local ="\u0077\u006f\u003ac\u006f\u006c\u006c\u0061\u0070\u0073\u0065\u0064";return _efd .CT_OnOff .MarshalXML (e ,start );};func (_gdg ST_SdtAppearance )String ()string {switch _gdg {case 0:return "";case 1:return "b\u006f\u0075\u006e\u0064\u0069\u006e\u0067\u0042\u006f\u0078";
case 2:return "\u0074\u0061\u0067\u0073";case 3:return "\u0068\u0069\u0064\u0064\u0065\u006e";};return "";};func (_dgd *ST_SdtAppearance )UnmarshalXMLAttr (attr _g .Attr )error {switch attr .Value {case "":*_dgd =0;case "b\u006f\u0075\u006e\u0064\u0069\u006e\u0067\u0042\u006f\u0078":*_dgd =1;
case "\u0074\u0061\u0067\u0073":*_dgd =2;case "\u0068\u0069\u0064\u0064\u0065\u006e":*_dgd =3;};return nil ;};type CT_People struct{Person []*CT_Person ;};func (_bc *CT_CommentsEx )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {_ac :for {_gea ,_fcd :=d .Token ();
if _fcd !=nil {return _fcd ;};switch _ecf :=_gea .(type ){case _g .StartElement :switch _ecf .Name {case _g .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0032\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u0063o\u006d\u006d\u0065\u006e\u0074\u0045x"}:_fe :=NewCT_CommentEx ();
if _dff :=d .DecodeElement (_fe ,&_ecf );_dff !=nil {return _dff ;};_bc .CommentEx =append (_bc .CommentEx ,_fe );default:_dc .Log .Debug ("\u0073\u006b\u0069\u0070\u0070\u0069\u006e\u0067 \u0075\u006e\u0073up\u0070\u006f\u0072\u0074\u0065\u0064 \u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0043\u0054\u005f\u0043o\u006d\u006d\u0065\u006e\u0074\u0073\u0045\u0078 \u0025\u0076",_ecf .Name );
if _cea :=d .Skip ();_cea !=nil {return _cea ;};};case _g .EndElement :break _ac ;case _g .CharData :};};return nil ;};

// Validate validates the FootnoteColumns and its children
func (_gbg *FootnoteColumns )Validate ()error {return _gbg .ValidateWithPath ("\u0046o\u006ft\u006e\u006f\u0074\u0065\u0043\u006f\u006c\u0075\u006d\u006e\u0073");};func (_fg *Appearance )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {start .Attr =append (start .Attr ,_g .Attr {Name :_g .Name {Local :"\u0078\u006d\u006cn\u0073"},Value :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0032\u002f\u0077\u006f\u0072\u0064\u006d\u006c"});
start .Attr =append (start .Attr ,_g .Attr {Name :_g .Name {Local :"\u0078\u006d\u006c\u006e\u0073\u003a\u0073\u0068"},Value :"\u0068\u0074\u0074\u0070\u003a/\u002f\u0073\u0063\u0068\u0065m\u0061s\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067/\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0068\u0061\u0072e\u0064\u0054\u0079\u0070\u0065\u0073"});
start .Attr =append (start .Attr ,_g .Attr {Name :_g .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0077"},Value :"ht\u0074\u0070:\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073.\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0077\u006f\u0072\u0064\u0070\u0072\u006f\u0063\u0065s\u0073i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u00306\u002fm\u0061\u0069n"});
start .Attr =append (start .Attr ,_g .Attr {Name :_g .Name {Local :"\u0078\u006d\u006c\u006e\u0073\u003a\u0077\u006f"},Value :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0032\u002f\u0077\u006f\u0072\u0064\u006d\u006c"});
start .Attr =append (start .Attr ,_g .Attr {Name :_g .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0078\u006dl"},Value :"\u0068\u0074tp\u003a\u002f\u002fw\u0077\u0077\u002e\u00773.o\u0072g/\u0058\u004d\u004c\u002f\u0031\u0039\u00398/\u006e\u0061\u006d\u0065\u0073\u0070\u0061c\u0065"});
start .Name .Local ="\u0077\u006f\u003a\u0061\u0070\u0070\u0065\u0061\u0072\u0061\u006e\u0063\u0065";return _fg .CT_SdtAppearance .MarshalXML (e ,start );};type RepeatingSectionItem struct{_dg .CT_Empty };

// Validate validates the CT_CommentsEx and its children
func (_eb *CT_CommentsEx )Validate ()error {return _eb .ValidateWithPath ("\u0043\u0054\u005f\u0043\u006f\u006d\u006d\u0065\u006e\u0074\u0073\u0045\u0078");};type ChartTrackingRefBased struct{_dg .CT_OnOff };func NewCT_CommentEx ()*CT_CommentEx {_cd :=&CT_CommentEx {};
return _cd };func (_acg *CT_Guid )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {for _ ,_baa :=range start .Attr {if _baa .Name .Local =="\u0076\u0061\u006c"{_gae :=_baa .Value ;_acg .ValAttr =&_gae ;continue ;};};for {_ced ,_dad :=d .Token ();
if _dad !=nil {return _df .Errorf ("\u0070\u0061\u0072\u0073in\u0067\u0020\u0043\u0054\u005f\u0047\u0075\u0069\u0064\u003a\u0020\u0025\u0073",_dad );};if _agf ,_cb :=_ced .(_g .EndElement );_cb &&_agf .Name ==start .Name {break ;};};return nil ;};type ST_SdtAppearance byte ;


// Validate validates the Collapsed and its children
func (_gcc *Collapsed )Validate ()error {return _gcc .ValidateWithPath ("\u0043o\u006c\u006c\u0061\u0070\u0073\u0065d");};func (_dbg *CommentsEx )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {start .Attr =append (start .Attr ,_g .Attr {Name :_g .Name {Local :"\u0078\u006d\u006cn\u0073"},Value :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0032\u002f\u0077\u006f\u0072\u0064\u006d\u006c"});
start .Attr =append (start .Attr ,_g .Attr {Name :_g .Name {Local :"\u0078\u006d\u006c\u006e\u0073\u003a\u0073\u0068"},Value :"\u0068\u0074\u0074\u0070\u003a/\u002f\u0073\u0063\u0068\u0065m\u0061s\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067/\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0068\u0061\u0072e\u0064\u0054\u0079\u0070\u0065\u0073"});
start .Attr =append (start .Attr ,_g .Attr {Name :_g .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0077"},Value :"ht\u0074\u0070:\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073.\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0077\u006f\u0072\u0064\u0070\u0072\u006f\u0063\u0065s\u0073i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u00306\u002fm\u0061\u0069n"});
start .Attr =append (start .Attr ,_g .Attr {Name :_g .Name {Local :"\u0078\u006d\u006c\u006e\u0073\u003a\u0077\u006f"},Value :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0032\u002f\u0077\u006f\u0072\u0064\u006d\u006c"});
start .Attr =append (start .Attr ,_g .Attr {Name :_g .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0078\u006dl"},Value :"\u0068\u0074tp\u003a\u002f\u002fw\u0077\u0077\u002e\u00773.o\u0072g/\u0058\u004d\u004c\u002f\u0031\u0039\u00398/\u006e\u0061\u006d\u0065\u0073\u0070\u0061c\u0065"});
start .Name .Local ="\u0077\u006f\u003a\u0063\u006f\u006d\u006d\u0065\u006e\u0074\u0073\u0045\u0078";return _dbg .CT_CommentsEx .MarshalXML (e ,start );};func init (){_f .RegisterConstructor ("\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0032\u002f\u0077\u006f\u0072\u0064\u006d\u006c","\u0043\u0054_\u0053\u0064\u0074A\u0070\u0070\u0065\u0061\u0072\u0061\u006e\u0063\u0065",NewCT_SdtAppearance );
_f .RegisterConstructor ("\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0032\u002f\u0077\u006f\u0072\u0064\u006d\u006c","\u0043\u0054\u005f\u0043\u006f\u006d\u006d\u0065\u006e\u0074\u0073\u0045\u0078",NewCT_CommentsEx );
_f .RegisterConstructor ("\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0032\u002f\u0077\u006f\u0072\u0064\u006d\u006c","\u0043\u0054\u005fC\u006f\u006d\u006d\u0065\u006e\u0074\u0045\u0078",NewCT_CommentEx );
_f .RegisterConstructor ("\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0032\u002f\u0077\u006f\u0072\u0064\u006d\u006c","\u0043T\u005f\u0050\u0065\u006f\u0070\u006ce",NewCT_People );
_f .RegisterConstructor ("\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0032\u002f\u0077\u006f\u0072\u0064\u006d\u006c","\u0043T\u005fP\u0072\u0065\u0073\u0065\u006e\u0063\u0065\u0049\u006e\u0066\u006f",NewCT_PresenceInfo );
_f .RegisterConstructor ("\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0032\u002f\u0077\u006f\u0072\u0064\u006d\u006c","\u0043T\u005f\u0050\u0065\u0072\u0073\u006fn",NewCT_Person );
_f .RegisterConstructor ("\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0032\u002f\u0077\u006f\u0072\u0064\u006d\u006c","C\u0054\u005f\u0053\u0064tR\u0065p\u0065\u0061\u0074\u0065\u0064S\u0065\u0063\u0074\u0069\u006f\u006e",NewCT_SdtRepeatedSection );
_f .RegisterConstructor ("\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0032\u002f\u0077\u006f\u0072\u0064\u006d\u006c","\u0043T\u005f\u0047\u0075\u0069\u0064",NewCT_Guid );
_f .RegisterConstructor ("\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0032\u002f\u0077\u006f\u0072\u0064\u006d\u006c","\u0063\u006f\u006co\u0072",NewColor );
_f .RegisterConstructor ("\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0032\u002f\u0077\u006f\u0072\u0064\u006d\u006c","d\u0061\u0074\u0061\u0042\u0069\u006e\u0064\u0069\u006e\u0067",NewDataBinding );
_f .RegisterConstructor ("\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0032\u002f\u0077\u006f\u0072\u0064\u006d\u006c","\u0061\u0070\u0070\u0065\u0061\u0072\u0061\u006e\u0063\u0065",NewAppearance );
_f .RegisterConstructor ("\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0032\u002f\u0077\u006f\u0072\u0064\u006d\u006c","\u0063\u006f\u006d\u006d\u0065\u006e\u0074\u0073\u0045\u0078",NewCommentsEx );
_f .RegisterConstructor ("\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0032\u002f\u0077\u006f\u0072\u0064\u006d\u006c","\u0070\u0065\u006f\u0070\u006c\u0065",NewPeople );
_f .RegisterConstructor ("\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0032\u002f\u0077\u006f\u0072\u0064\u006d\u006c","\u0072\u0065p\u0065\u0061\u0074i\u006e\u0067\u0053\u0065\u0063\u0074\u0069\u006f\u006e",NewRepeatingSection );
_f .RegisterConstructor ("\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0032\u002f\u0077\u006f\u0072\u0064\u006d\u006c","r\u0065p\u0065\u0061\u0074\u0069\u006e\u0067\u0053\u0065c\u0074\u0069\u006f\u006eIt\u0065\u006d",NewRepeatingSectionItem );
_f .RegisterConstructor ("\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0032\u002f\u0077\u006f\u0072\u0064\u006d\u006c","c\u0068\u0061\u0072\u0074Tr\u0061c\u006b\u0069\u006e\u0067\u0052e\u0066\u0042\u0061\u0073\u0065\u0064",NewChartTrackingRefBased );
_f .RegisterConstructor ("\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0032\u002f\u0077\u006f\u0072\u0064\u006d\u006c","\u0063o\u006c\u006c\u0061\u0070\u0073\u0065d",NewCollapsed );
_f .RegisterConstructor ("\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0032\u002f\u0077\u006f\u0072\u0064\u006d\u006c","\u0064\u006f\u0063I\u0064",NewDocId );
_f .RegisterConstructor ("\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0032\u002f\u0077\u006f\u0072\u0064\u006d\u006c","\u0066o\u006ft\u006e\u006f\u0074\u0065\u0043\u006f\u006c\u0075\u006d\u006e\u0073",NewFootnoteColumns );
_f .RegisterConstructor ("\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0032\u002f\u0077\u006f\u0072\u0064\u006d\u006c","\u0077e\u0062E\u0078\u0074\u0065\u006e\u0073i\u006f\u006eL\u0069\u006e\u006b\u0065\u0064",NewWebExtensionLinked );
_f .RegisterConstructor ("\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0032\u002f\u0077\u006f\u0072\u0064\u006d\u006c","\u0077\u0065\u0062\u0045xt\u0065\u006e\u0073\u0069\u006f\u006e\u0043\u0072\u0065\u0061\u0074\u0065\u0064",NewWebExtensionCreated );
};