//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package symex ;import (_f "encoding/xml";_e "fmt";_d "github.com/unidoc/unioffice/v2";);func (_b *CT_SymEx )UnmarshalXML (d *_f .Decoder ,start _f .StartElement )error {for _ ,_g :=range start .Attr {if _g .Name .Local =="\u0066\u006f\u006e\u0074"{_bd :=_g .Value ;
_b .FontAttr =&_bd ;continue ;};if _g .Name .Local =="\u0063\u0068\u0061\u0072"{_aa :=_g .Value ;_b .CharAttr =&_aa ;continue ;};};for {_ae ,_gd :=d .Token ();if _gd !=nil {return _e .Errorf ("p\u0061r\u0073\u0069\u006e\u0067\u0020\u0043\u0054\u005fS\u0079\u006d\u0045\u0078: \u0025\u0073",_gd );
};if _gf ,_gfa :=_ae .(_f .EndElement );_gfa &&_gf .Name ==start .Name {break ;};};return nil ;};func NewCT_SymEx ()*CT_SymEx {_cf :=&CT_SymEx {};return _cf };func (_aed *SymEx )UnmarshalXML (d *_f .Decoder ,start _f .StartElement )error {_aed .CT_SymEx =*NewCT_SymEx ();
for _ ,_fb :=range start .Attr {if _fb .Name .Local =="\u0066\u006f\u006e\u0074"{_ge :=_fb .Value ;_aed .FontAttr =&_ge ;continue ;};if _fb .Name .Local =="\u0063\u0068\u0061\u0072"{_dcb :=_fb .Value ;_aed .CharAttr =&_dcb ;continue ;};};for {_fe ,_dbf :=d .Token ();
if _dbf !=nil {return _e .Errorf ("\u0070\u0061\u0072\u0073\u0069\u006e\u0067\u0020\u0053\u0079\u006d\u0045x\u003a\u0020\u0025\u0073",_dbf );};if _be ,_gfd :=_fe .(_f .EndElement );_gfd &&_be .Name ==start .Name {break ;};};return nil ;};

// ValidateWithPath validates the CT_SymEx and its children, prefixing error messages with path
func (_cd *CT_SymEx )ValidateWithPath (path string )error {return nil };func NewSymEx ()*SymEx {_db :=&SymEx {};_db .CT_SymEx =*NewCT_SymEx ();return _db };func (_ad *SymEx )MarshalXML (e *_f .Encoder ,start _f .StartElement )error {start .Attr =append (start .Attr ,_f .Attr {Name :_f .Name {Local :"\u0078\u006d\u006cn\u0073"},Value :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006d\u0069\u0063\u0072\u006fs\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006ff\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u00315\u002f\u0077\u006f\u0072\u0064\u006dl\u002f\u0073\u0079m\u0065\u0078"});
start .Attr =append (start .Attr ,_f .Attr {Name :_f .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0073"},Value :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006d\u0069\u0063\u0072\u006fs\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006ff\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u00315\u002f\u0077\u006f\u0072\u0064\u006dl\u002f\u0073\u0079m\u0065\u0078"});
start .Attr =append (start .Attr ,_f .Attr {Name :_f .Name {Local :"\u0078\u006d\u006c\u006e\u0073\u003a\u0073\u0068"},Value :"\u0068\u0074\u0074\u0070\u003a/\u002f\u0073\u0063\u0068\u0065m\u0061s\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067/\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0068\u0061\u0072e\u0064\u0054\u0079\u0070\u0065\u0073"});
start .Attr =append (start .Attr ,_f .Attr {Name :_f .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0077"},Value :"ht\u0074\u0070:\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073.\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0077\u006f\u0072\u0064\u0070\u0072\u006f\u0063\u0065s\u0073i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u00306\u002fm\u0061\u0069n"});
start .Attr =append (start .Attr ,_f .Attr {Name :_f .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0078\u006dl"},Value :"\u0068\u0074tp\u003a\u002f\u002fw\u0077\u0077\u002e\u00773.o\u0072g/\u0058\u004d\u004c\u002f\u0031\u0039\u00398/\u006e\u0061\u006d\u0065\u0073\u0070\u0061c\u0065"});
start .Name .Local ="\u0073:\u0073\u0079\u006d\u0045\u0078";return _ad .CT_SymEx .MarshalXML (e ,start );};type CT_SymEx struct{FontAttr *string ;CharAttr *string ;};type SymEx struct{CT_SymEx };func (_dc *CT_SymEx )MarshalXML (e *_f .Encoder ,start _f .StartElement )error {if _dc .FontAttr !=nil {start .Attr =append (start .Attr ,_f .Attr {Name :_f .Name {Local :"\u0073\u003a\u0066\u006f\u006e\u0074"},Value :_e .Sprintf ("\u0025\u0076",*_dc .FontAttr )});
};if _dc .CharAttr !=nil {start .Attr =append (start .Attr ,_f .Attr {Name :_f .Name {Local :"\u0073\u003a\u0063\u0068\u0061\u0072"},Value :_e .Sprintf ("\u0025\u0076",*_dc .CharAttr )});};e .EncodeToken (start );e .EncodeToken (_f .EndElement {Name :start .Name });
return nil ;};

// Validate validates the SymEx and its children
func (_ff *SymEx )Validate ()error {return _ff .ValidateWithPath ("\u0053\u0079\u006dE\u0078")};

// ValidateWithPath validates the SymEx and its children, prefixing error messages with path
func (_ef *SymEx )ValidateWithPath (path string )error {if _gb :=_ef .CT_SymEx .ValidateWithPath (path );_gb !=nil {return _gb ;};return nil ;};

// Validate validates the CT_SymEx and its children
func (_bg *CT_SymEx )Validate ()error {return _bg .ValidateWithPath ("\u0043\u0054\u005f\u0053\u0079\u006d\u0045\u0078");};func init (){_d .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006d\u0069\u0063\u0072\u006fs\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006ff\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u00315\u002f\u0077\u006f\u0072\u0064\u006dl\u002f\u0073\u0079m\u0065\u0078","\u0043\u0054\u005f\u0053\u0079\u006d\u0045\u0078",NewCT_SymEx );
_d .RegisterConstructor ("\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006d\u0069\u0063\u0072\u006fs\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006ff\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u00315\u002f\u0077\u006f\u0072\u0064\u006dl\u002f\u0073\u0079m\u0065\u0078","\u0073\u0079\u006dE\u0078",NewSymEx );
};