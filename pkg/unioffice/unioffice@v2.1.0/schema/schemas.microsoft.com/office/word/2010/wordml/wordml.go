//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package wordml ;import (_fd "encoding/xml";_g "fmt";_ab "github.com/unidoc/unioffice/v2";_c "github.com/unidoc/unioffice/v2/common/logger";_abg "github.com/unidoc/unioffice/v2/schema/soo/dml";_fg "github.com/unidoc/unioffice/v2/schema/soo/ofc/sharedTypes";
_fe "github.com/unidoc/unioffice/v2/schema/soo/wml";_a "strconv";_e "time";);

// Validate validates the CT_Scene3D and its children
func (_bgbd *CT_Scene3D )Validate ()error {return _bgbd .ValidateWithPath ("\u0043\u0054\u005f\u0053\u0063\u0065\u006e\u0065\u0033\u0044");};

// Validate validates the CT_Percentage and its children
func (_fecd *CT_Percentage )Validate ()error {return _fecd .ValidateWithPath ("\u0043\u0054\u005f\u0050\u0065\u0072\u0063\u0065\u006e\u0074\u0061\u0067\u0065");};

// ValidateWithPath validates the Checkbox and its children, prefixing error messages with path
func (_abe *Checkbox )ValidateWithPath (path string )error {if _egdf :=_abe .CT_SdtCheckbox .ValidateWithPath (path );_egdf !=nil {return _egdf ;};return nil ;};func ParseUnionST_AdjAngle (s string )(_abg .ST_AdjAngle ,error ){return _abg .ParseUnionST_AdjAngle (s )};


// Validate validates the CT_DefaultImageDpi and its children
func (_bbb *CT_DefaultImageDpi )Validate ()error {return _bbb .ValidateWithPath ("\u0043T\u005fD\u0065\u0066\u0061\u0075\u006ct\u0049\u006da\u0067\u0065\u0044\u0070\u0069");};

// Validate validates the EG_FillProperties and its children
func (_eacf *EG_FillProperties )Validate ()error {return _eacf .ValidateWithPath ("\u0045\u0047\u005f\u0046\u0069\u006c\u006c\u0050\u0072\u006f\u0070\u0065r\u0074\u0069\u0065\u0073");};type EG_ShadePropertiesChoice struct{Lin *CT_LinearShadeProperties ;
Path *CT_PathShadeProperties ;};type ST_PresetMaterialType byte ;func (_aff *CT_GradientStopList )UnmarshalXML (d *_fd .Decoder ,start _fd .StartElement )error {_dadf :for {_acc ,_cgge :=d .Token ();if _cgge !=nil {return _cgge ;};switch _ddgc :=_acc .(type ){case _fd .StartElement :switch _ddgc .Name {case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u0067\u0073"}:_acac :=NewCT_GradientStop ();
if _bggd :=d .DecodeElement (_acac ,&_ddgc );_bggd !=nil {return _bggd ;};_aff .Gs =append (_aff .Gs ,_acac );default:_c .Log .Debug ("\u0073\u006b\u0069\u0070\u0070\u0069\u006e\u0067\u0020\u0075\u006e\u0073\u0075\u0070\u0070o\u0072\u0074\u0065\u0064\u0020\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020o\u006e\u0020\u0043\u0054\u005f\u0047\u0072\u0061\u0064\u0069\u0065\u006etS\u0074\u006f\u0070\u004c\u0069\u0073\u0074\u0020\u0025\u0076",_ddgc .Name );
if _fgd :=d .Skip ();_fgd !=nil {return _fgd ;};};case _fd .EndElement :break _dadf ;case _fd .CharData :};};return nil ;};func (_fefgg ST_NumSpacing )Validate ()error {return _fefgg .ValidateWithPath ("")};type CT_Scene3D struct{Camera *CT_Camera ;LightRig *CT_LightRig ;
};func (_bea *CT_Color )MarshalXML (e *_fd .Encoder ,start _fd .StartElement )error {e .EncodeToken (start );if _bea .SrgbClr !=nil {_ba :=_fd .StartElement {Name :_fd .Name {Local :"w\u006f\u0072\u003a\u0073\u0072\u0067\u0062\u0043\u006c\u0072"}};e .EncodeElement (_bea .SrgbClr ,_ba );
};if _bea .SchemeClr !=nil {_bbc :=_fd .StartElement {Name :_fd .Name {Local :"\u0077\u006f\u0072\u003a\u0073\u0063\u0068\u0065\u006d\u0065\u0043\u006c\u0072"}};e .EncodeElement (_bea .SchemeClr ,_bbc );};e .EncodeToken (_fd .EndElement {Name :start .Name });
return nil ;};

// ValidateWithPath validates the CT_SchemeColor and its children, prefixing error messages with path
func (_aad *CT_SchemeColor )ValidateWithPath (path string )error {if _aad .ValAttr ==ST_SchemeColorValUnset {return _g .Errorf ("\u0025\u0073\u002fV\u0061\u006c\u0041\u0074t\u0072\u0020\u0069\u0073\u0020\u0061\u0020m\u0061\u006e\u0064\u0061\u0074\u006f\u0072\u0079\u0020\u0066\u0069\u0065\u006c\u0064",path );
};if _gdac :=_aad .ValAttr .ValidateWithPath (path +"\u002f\u0056\u0061\u006c\u0041\u0074\u0074\u0072");_gdac !=nil {return _gdac ;};for _fcag ,_facc :=range _aad .EG_ColorTransform {if _gdc :=_facc .ValidateWithPath (_g .Sprintf ("\u0025s\u002f\u0045\u0047\u005f\u0043\u006f\u006c\u006f\u0072\u0054\u0072a\u006e\u0073\u0066\u006f\u0072\u006d\u005b\u0025\u0064\u005d",path ,_fcag ));
_gdc !=nil {return _gdc ;};};return nil ;};func (_ggafg ST_PresetMaterialType )ValidateWithPath (path string )error {switch _ggafg {case 0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16:default:return _g .Errorf ("\u0025s\u003a\u0020\u006f\u0075t\u0020\u006f\u0066\u0020\u0072a\u006eg\u0065 \u0076\u0061\u006c\u0075\u0065\u0020\u0025d",path ,int (_ggafg ));
};return nil ;};type CT_Color struct{SrgbClr *CT_SRgbColor ;SchemeClr *CT_SchemeColor ;};func NewAG_Parids ()*AG_Parids {_cf :=&AG_Parids {};return _cf };

// Validate validates the CT_Ligatures and its children
func (_egg *CT_Ligatures )Validate ()error {return _egg .ValidateWithPath ("\u0043\u0054\u005fL\u0069\u0067\u0061\u0074\u0075\u0072\u0065\u0073");};type EG_RPrTextEffects struct{Glow *CT_Glow ;Shadow *CT_Shadow ;Reflection *CT_Reflection ;TextOutline *CT_TextOutlineEffect ;
TextFill *CT_FillTextEffect ;Scene3d *CT_Scene3D ;Props3d *CT_Props3D ;};func (_aacfa *EG_ShadePropertiesChoice )MarshalXML (e *_fd .Encoder ,start _fd .StartElement )error {e .EncodeToken (start );if _aacfa .Lin !=nil {_aeffb :=_fd .StartElement {Name :_fd .Name {Local :"\u0077o\u0072\u003a\u006c\u0069\u006e"}};
e .EncodeElement (_aacfa .Lin ,_aeffb );}else if _aacfa .Path !=nil {_bbea :=_fd .StartElement {Name :_fd .Name {Local :"\u0077\u006f\u0072\u003a\u0070\u0061\u0074\u0068"}};e .EncodeElement (_aacfa .Path ,_bbea );};e .EncodeToken (_fd .EndElement {Name :start .Name });
return nil ;};

// Validate validates the DocId and its children
func (_baec *DocId )Validate ()error {return _baec .ValidateWithPath ("\u0044\u006f\u0063I\u0064")};

// Validate validates the EG_ConflictsChoice and its children
func (_abcg *EG_ConflictsChoice )Validate ()error {return _abcg .ValidateWithPath ("\u0045G\u005fC\u006f\u006e\u0066\u006c\u0069c\u0074\u0073C\u0068\u006f\u0069\u0063\u0065");};

// ValidateWithPath validates the EG_FillProperties and its children, prefixing error messages with path
func (_gdcc *EG_FillProperties )ValidateWithPath (path string )error {if _dfgb :=_gdcc .FillPropertiesChoice .ValidateWithPath (path +"/\u0046\u0069\u006c\u006cPr\u006fp\u0065\u0072\u0074\u0069\u0065s\u0043\u0068\u006f\u0069\u0063\u0065");_dfgb !=nil {return _dfgb ;
};return nil ;};type ST_PathShadeType byte ;

// Validate validates the CT_Bevel and its children
func (_dd *CT_Bevel )Validate ()error {return _dd .ValidateWithPath ("\u0043\u0054\u005f\u0042\u0065\u0076\u0065\u006c");};func NewCT_LongHexNumber ()*CT_LongHexNumber {_bfg :=&CT_LongHexNumber {};return _bfg };func (_cedee ST_OnOff )MarshalXMLAttr (name _fd .Name )(_fd .Attr ,error ){_bdbd :=_fd .Attr {};
_bdbd .Name =name ;switch _cedee {case ST_OnOffUnset :_bdbd .Value ="";case ST_OnOffTrue :_bdbd .Value ="\u0074\u0072\u0075\u0065";case ST_OnOffFalse :_bdbd .Value ="\u0066\u0061\u006cs\u0065";case ST_OnOff0 :_bdbd .Value ="\u0030";case ST_OnOff1 :_bdbd .Value ="\u0031";
};return _bdbd ,nil ;};type CT_NumForm struct{ValAttr ST_NumForm ;};func (_cgga *EG_LineJoinProperties )MarshalXML (e *_fd .Encoder ,start _fd .StartElement )error {_cgga .LineJoinPropertiesChoice .MarshalXML (e ,_fd .StartElement {});return nil ;};func NewEG_FillPropertiesChoice ()*EG_FillPropertiesChoice {_fecb :=&EG_FillPropertiesChoice {};
return _fecb ;};func (_bfbg *ST_PresetCameraType )UnmarshalXMLAttr (attr _fd .Attr )error {switch attr .Value {case "":*_bfbg =0;case "l\u0065g\u0061\u0063\u0079\u004f\u0062\u006c\u0069\u0071u\u0065\u0054\u006f\u0070Le\u0066\u0074":*_bfbg =1;case "\u006c\u0065g\u0061\u0063\u0079O\u0062\u006c\u0069\u0071\u0075\u0065\u0054\u006f\u0070":*_bfbg =2;
case "l\u0065\u0067\u0061\u0063yO\u0062l\u0069\u0071\u0075\u0065\u0054o\u0070\u0052\u0069\u0067\u0068\u0074":*_bfbg =3;case "\u006c\u0065\u0067\u0061\u0063\u0079\u004f\u0062\u006c\u0069\u0071\u0075e\u004c\u0065\u0066\u0074":*_bfbg =4;case "\u006ce\u0067a\u0063\u0079\u004f\u0062\u006ci\u0071\u0075e\u0046\u0072\u006f\u006e\u0074":*_bfbg =5;
case "\u006ce\u0067a\u0063\u0079\u004f\u0062\u006ci\u0071\u0075e\u0052\u0069\u0067\u0068\u0074":*_bfbg =6;case "\u006c\u0065\u0067ac\u0079\u004f\u0062\u006c\u0069\u0071\u0075\u0065\u0042\u006f\u0074\u0074\u006f\u006d\u004c\u0065\u0066\u0074":*_bfbg =7;
case "\u006c\u0065\u0067\u0061cy\u004f\u0062\u006c\u0069\u0071\u0075\u0065\u0042\u006f\u0074\u0074\u006f\u006d":*_bfbg =8;case "\u006ce\u0067\u0061\u0063\u0079\u004f\u0062\u006c\u0069\u0071\u0075\u0065B\u006f\u0074\u0074\u006f\u006d\u0052\u0069\u0067\u0068\u0074":*_bfbg =9;
case "\u006ce\u0067\u0061\u0063\u0079\u0050\u0065\u0072\u0073\u0070\u0065\u0063t\u0069\u0076\u0065\u0054\u006f\u0070\u004c\u0065\u0066\u0074":*_bfbg =10;case "l\u0065g\u0061\u0063\u0079\u0050\u0065\u0072\u0073\u0070e\u0063\u0074\u0069\u0076eT\u006f\u0070":*_bfbg =11;
case "\u006ce\u0067\u0061\u0063\u0079P\u0065\u0072\u0073\u0070\u0065c\u0074i\u0076e\u0054\u006f\u0070\u0052\u0069\u0067\u0068t":*_bfbg =12;case "l\u0065\u0067\u0061\u0063yP\u0065r\u0073\u0070\u0065\u0063\u0074i\u0076\u0065\u004c\u0065\u0066\u0074":*_bfbg =13;
case "\u006c\u0065\u0067\u0061cy\u0050\u0065\u0072\u0073\u0070\u0065\u0063\u0074\u0069\u0076\u0065\u0046\u0072\u006fn\u0074":*_bfbg =14;case "\u006c\u0065\u0067\u0061cy\u0050\u0065\u0072\u0073\u0070\u0065\u0063\u0074\u0069\u0076\u0065\u0052\u0069\u0067h\u0074":*_bfbg =15;
case "l\u0065\u0067\u0061\u0063\u0079\u0050e\u0072\u0073\u0070\u0065\u0063\u0074\u0069\u0076\u0065B\u006f\u0074\u0074o\u006dL\u0065\u0066\u0074":*_bfbg =16;case "\u006c\u0065\u0067ac\u0079\u0050\u0065\u0072\u0073\u0070\u0065\u0063\u0074\u0069\u0076\u0065\u0042\u006f\u0074\u0074\u006f\u006d":*_bfbg =17;
case "\u006c\u0065\u0067\u0061c\u0079\u0050\u0065\u0072\u0073\u0070\u0065\u0063\u0074\u0069v\u0065B\u006f\u0074\u0074\u006f\u006d\u0052\u0069g\u0068\u0074":*_bfbg =18;case "\u006f\u0072\u0074\u0068\u006f\u0067\u0072\u0061\u0070\u0068\u0069\u0063F\u0072\u006f\u006e\u0074":*_bfbg =19;
case "\u0069\u0073\u006f\u006d\u0065\u0074\u0072\u0069\u0063T\u006f\u0070\u0055\u0070":*_bfbg =20;case "\u0069\u0073o\u006d\u0065\u0074r\u0069\u0063\u0054\u006f\u0070\u0044\u006f\u0077\u006e":*_bfbg =21;case "\u0069\u0073\u006f\u006d\u0065\u0074\u0072\u0069\u0063\u0042\u006f\u0074t\u006f\u006d\u0055\u0070":*_bfbg =22;
case "\u0069\u0073\u006f\u006det\u0072\u0069\u0063\u0042\u006f\u0074\u0074\u006f\u006d\u0044\u006f\u0077\u006e":*_bfbg =23;case "\u0069s\u006fm\u0065\u0074\u0072\u0069\u0063\u004c\u0065\u0066\u0074\u0055\u0070":*_bfbg =24;case "\u0069\u0073\u006f\u006d\u0065\u0074\u0072\u0069\u0063\u004c\u0065\u0066t\u0044\u006f\u0077\u006e":*_bfbg =25;
case "\u0069\u0073o\u006d\u0065\u0074r\u0069\u0063\u0052\u0069\u0067\u0068\u0074\u0055\u0070":*_bfbg =26;case "\u0069s\u006fm\u0065\u0074\u0072\u0069\u0063R\u0069\u0067h\u0074\u0044\u006f\u0077\u006e":*_bfbg =27;case "i\u0073\u006f\u006d\u0065tr\u0069c\u004f\u0066\u0066\u0041\u0078i\u0073\u0031\u004c\u0065\u0066\u0074":*_bfbg =28;
case "\u0069\u0073\u006f\u006det\u0072\u0069\u0063\u004f\u0066\u0066\u0041\u0078\u0069\u0073\u0031\u0052\u0069\u0067h\u0074":*_bfbg =29;case "i\u0073o\u006d\u0065\u0074\u0072\u0069\u0063\u004f\u0066f\u0041\u0078\u0069\u00731T\u006f\u0070":*_bfbg =30;case "i\u0073\u006f\u006d\u0065tr\u0069c\u004f\u0066\u0066\u0041\u0078i\u0073\u0032\u004c\u0065\u0066\u0074":*_bfbg =31;
case "\u0069\u0073\u006f\u006det\u0072\u0069\u0063\u004f\u0066\u0066\u0041\u0078\u0069\u0073\u0032\u0052\u0069\u0067h\u0074":*_bfbg =32;case "i\u0073o\u006d\u0065\u0074\u0072\u0069\u0063\u004f\u0066f\u0041\u0078\u0069\u00732T\u006f\u0070":*_bfbg =33;case "i\u0073\u006f\u006d\u0065tr\u0069c\u004f\u0066\u0066\u0041\u0078i\u0073\u0033\u004c\u0065\u0066\u0074":*_bfbg =34;
case "\u0069\u0073\u006f\u006det\u0072\u0069\u0063\u004f\u0066\u0066\u0041\u0078\u0069\u0073\u0033\u0052\u0069\u0067h\u0074":*_bfbg =35;case "\u0069\u0073\u006fme\u0074\u0072\u0069\u0063\u004f\u0066\u0066\u0041\u0078\u0069\u0073\u0033\u0042\u006f\u0074\u0074\u006f\u006d":*_bfbg =36;
case "i\u0073\u006f\u006d\u0065tr\u0069c\u004f\u0066\u0066\u0041\u0078i\u0073\u0034\u004c\u0065\u0066\u0074":*_bfbg =37;case "\u0069\u0073\u006f\u006det\u0072\u0069\u0063\u004f\u0066\u0066\u0041\u0078\u0069\u0073\u0034\u0052\u0069\u0067h\u0074":*_bfbg =38;
case "\u0069\u0073\u006fme\u0074\u0072\u0069\u0063\u004f\u0066\u0066\u0041\u0078\u0069\u0073\u0034\u0042\u006f\u0074\u0074\u006f\u006d":*_bfbg =39;case "\u006f\u0062\u006c\u0069\u0071\u0075\u0065\u0054\u006fp\u004c\u0065\u0066\u0074":*_bfbg =40;case "\u006f\u0062\u006c\u0069\u0071\u0075\u0065\u0054\u006f\u0070":*_bfbg =41;
case "\u006fb\u006ci\u0071\u0075\u0065\u0054\u006f\u0070\u0052\u0069\u0067\u0068\u0074":*_bfbg =42;case "o\u0062\u006c\u0069\u0071\u0075\u0065\u004c\u0065\u0066\u0074":*_bfbg =43;case "\u006f\u0062\u006ci\u0071\u0075\u0065\u0052\u0069\u0067\u0068\u0074":*_bfbg =44;
case "\u006f\u0062\u006c\u0069\u0071\u0075\u0065\u0042\u006f\u0074\u0074\u006fm\u004c\u0065\u0066\u0074":*_bfbg =45;case "\u006f\u0062\u006c\u0069\u0071\u0075\u0065\u0042\u006f\u0074\u0074\u006f\u006d":*_bfbg =46;case "\u006fb\u006ci\u0071\u0075\u0065\u0042\u006ft\u0074\u006fm\u0052\u0069\u0067\u0068\u0074":*_bfbg =47;
case "\u0070\u0065r\u0073\u0070\u0065c\u0074\u0069\u0076\u0065\u0046\u0072\u006f\u006e\u0074":*_bfbg =48;case "\u0070e\u0072s\u0070\u0065\u0063\u0074\u0069\u0076\u0065\u004c\u0065\u0066\u0074":*_bfbg =49;case "\u0070\u0065r\u0073\u0070\u0065c\u0074\u0069\u0076\u0065\u0052\u0069\u0067\u0068\u0074":*_bfbg =50;
case "\u0070\u0065r\u0073\u0070\u0065c\u0074\u0069\u0076\u0065\u0041\u0062\u006f\u0076\u0065":*_bfbg =51;case "\u0070\u0065r\u0073\u0070\u0065c\u0074\u0069\u0076\u0065\u0042\u0065\u006c\u006f\u0077":*_bfbg =52;case "\u0070\u0065\u0072\u0073\u0070\u0065\u0063\u0074\u0069\u0076\u0065A\u0062\u006f\u0076\u0065\u004c\u0065\u0066\u0074\u0046\u0061c\u0069\u006e\u0067":*_bfbg =53;
case "p\u0065\u0072\u0073\u0070\u0065\u0063t\u0069\u0076\u0065\u0041\u0062\u006f\u0076\u0065\u0052i\u0067\u0068\u0074F\u0061c\u0069\u006e\u0067":*_bfbg =54;case "\u0070\u0065\u0072\u0073\u0070\u0065\u0063\u0074\u0069\u0076\u0065\u0043\u006f\u006e\u0074r\u0061s\u0074\u0069\u006e\u0067\u004c\u0065\u0066\u0074\u0046\u0061\u0063\u0069\u006e\u0067":*_bfbg =55;
case "\u0070\u0065\u0072\u0073\u0070\u0065c\u0074\u0069\u0076\u0065\u0043\u006f\u006e\u0074\u0072\u0061\u0073\u0074\u0069n\u0067\u0052\u0069\u0067\u0068\u0074\u0046a\u0063\u0069\u006e\u0067":*_bfbg =56;case "p\u0065\u0072\u0073\u0070\u0065\u0063t\u0069\u0076\u0065\u0048\u0065\u0072\u006f\u0069\u0063L\u0065\u0066\u0074F\u0061c\u0069\u006e\u0067":*_bfbg =57;
case "\u0070\u0065\u0072\u0073p\u0065\u0063\u0074\u0069\u0076\u0065\u0048\u0065\u0072\u006fi\u0063R\u0069\u0067\u0068\u0074\u0046\u0061\u0063i\u006e\u0067":*_bfbg =58;case "\u0070\u0065\u0072sp\u0065\u0063\u0074\u0069\u0076\u0065\u0048\u0065\u0072o\u0069c\u0045x\u0074r\u0065\u006d\u0065\u004c\u0065\u0066\u0074\u0046\u0061\u0063\u0069\u006e\u0067":*_bfbg =59;
case "p\u0065\u0072\u0073\u0070\u0065\u0063t\u0069\u0076\u0065\u0048\u0065\u0072o\u0069\u0063\u0045\u0078\u0074\u0072\u0065m\u0065\u0052\u0069\u0067\u0068\u0074\u0046\u0061\u0063\u0069n\u0067":*_bfbg =60;case "\u0070e\u0072s\u0070\u0065\u0063\u0074\u0069v\u0065\u0052e\u006c\u0061\u0078\u0065\u0064":*_bfbg =61;
case "\u0070\u0065\u0072\u0073p\u0065\u0063\u0074\u0069\u0076\u0065\u0052\u0065\u006c\u0061x\u0065d\u004d\u006f\u0064\u0065\u0072\u0061\u0074e\u006c\u0079":*_bfbg =62;};return nil ;};func (_gffa *ST_OnOff )UnmarshalXMLAttr (attr _fd .Attr )error {switch attr .Value {case "":*_gffa =0;
case "\u0074\u0072\u0075\u0065":*_gffa =1;case "\u0066\u0061\u006cs\u0065":*_gffa =2;case "\u0030":*_gffa =3;case "\u0031":*_gffa =4;};return nil ;};type CT_SdtCheckboxSymbol struct{FontAttr *string ;ValAttr *string ;};

// ValidateWithPath validates the CT_Percentage and its children, prefixing error messages with path
func (_bdgg *CT_Percentage )ValidateWithPath (path string )error {if _cdc :=_bdgg .ValAttr .ValidateWithPath (path +"\u002f\u0056\u0061\u006c\u0041\u0074\u0074\u0072");_cdc !=nil {return _cdc ;};return nil ;};

// ValidateWithPath validates the EG_LineJoinPropertiesChoice and its children, prefixing error messages with path
func (_ccfg *EG_LineJoinPropertiesChoice )ValidateWithPath (path string )error {if _ccfg .Round !=nil {if _fdcg :=_ccfg .Round .ValidateWithPath (path +"\u002f\u0052\u006f\u0075\u006e\u0064");_fdcg !=nil {return _fdcg ;};};if _ccfg .Bevel !=nil {if _fege :=_ccfg .Bevel .ValidateWithPath (path +"\u002f\u0042\u0065\u0076\u0065\u006c");
_fege !=nil {return _fege ;};};if _ccfg .Miter !=nil {if _cagc :=_ccfg .Miter .ValidateWithPath (path +"\u002f\u004d\u0069\u0074\u0065\u0072");_cagc !=nil {return _cagc ;};};return nil ;};func (_gfaa *CT_SolidColorFillProperties )MarshalXML (e *_fd .Encoder ,start _fd .StartElement )error {e .EncodeToken (start );
if _gfaa .SrgbClr !=nil {_babf :=_fd .StartElement {Name :_fd .Name {Local :"w\u006f\u0072\u003a\u0073\u0072\u0067\u0062\u0043\u006c\u0072"}};e .EncodeElement (_gfaa .SrgbClr ,_babf );};if _gfaa .SchemeClr !=nil {_caf :=_fd .StartElement {Name :_fd .Name {Local :"\u0077\u006f\u0072\u003a\u0073\u0063\u0068\u0065\u006d\u0065\u0043\u006c\u0072"}};
e .EncodeElement (_gfaa .SchemeClr ,_caf );};e .EncodeToken (_fd .EndElement {Name :start .Name });return nil ;};type CT_GradientStopList struct{Gs []*CT_GradientStop ;};func NewCT_SRgbColor ()*CT_SRgbColor {_gcfc :=&CT_SRgbColor {};return _gcfc };type CT_StyleSet struct{IdAttr uint64 ;
ValAttr ST_OnOff ;};func NewCT_LineJoinMiterProperties ()*CT_LineJoinMiterProperties {_dga :=&CT_LineJoinMiterProperties {};return _dga ;};func (_cgg *CT_DefaultImageDpi )UnmarshalXML (d *_fd .Decoder ,start _fd .StartElement )error {for _ ,_cbf :=range start .Attr {if _cbf .Name .Local =="\u0076\u0061\u006c"{_dbd ,_ddb :=_a .ParseInt (_cbf .Value ,10,64);
if _ddb !=nil {return _ddb ;};_cgg .ValAttr =_dbd ;continue ;};};for {_gcb ,_gda :=d .Token ();if _gda !=nil {return _g .Errorf ("\u0070\u0061\u0072\u0073\u0069\u006e\u0067\u0020\u0043\u0054_\u0044\u0065\u0066\u0061\u0075\u006c\u0074I\u006d\u0061\u0067\u0065\u0044\u0070\u0069\u003a\u0020\u0025\u0073",_gda );
};if _gbb ,_aba :=_gcb .(_fd .EndElement );_aba &&_gbb .Name ==start .Name {break ;};};return nil ;};func (_aca *CT_FillTextEffect )MarshalXML (e *_fd .Encoder ,start _fd .StartElement )error {e .EncodeToken (start );_aca .FillPropertiesChoice .MarshalXML (e ,_fd .StartElement {});
e .EncodeToken (_fd .EndElement {Name :start .Name });return nil ;};func ParseUnionST_AdjCoordinate (s string )(_abg .ST_AdjCoordinate ,error ){return _abg .ParseUnionST_AdjCoordinate (s );};func NewCT_Percentage ()*CT_Percentage {_cgc :=&CT_Percentage {};
return _cgc };func (_bfgd ST_CompoundLine )MarshalXML (e *_fd .Encoder ,start _fd .StartElement )error {return e .EncodeElement (_bfgd .String (),start );};type CT_LinearShadeProperties struct{AngAttr *int32 ;ScaledAttr ST_OnOff ;};func (_dcbb ST_SchemeColorVal )Validate ()error {return _dcbb .ValidateWithPath ("")};
func (_ecc *CT_GradientStopList )MarshalXML (e *_fd .Encoder ,start _fd .StartElement )error {e .EncodeToken (start );_ca :=_fd .StartElement {Name :_fd .Name {Local :"\u0077\u006f\u0072\u003a\u0067\u0073"}};for _ ,_bbad :=range _ecc .Gs {e .EncodeElement (_bbad ,_ca );
};e .EncodeToken (_fd .EndElement {Name :start .Name });return nil ;};func NewCT_LightRig ()*CT_LightRig {_bbe :=&CT_LightRig {};_bbe .RigAttr =ST_LightRigType (1);_bbe .DirAttr =ST_LightRigDirection (1);return _bbe ;};type EG_FillPropertiesChoice struct{NoFill *_fe .CT_Empty ;
SolidFill *CT_SolidColorFillProperties ;GradFill *CT_GradientFillProperties ;};func (_cgeb *CT_GradientStop )MarshalXML (e *_fd .Encoder ,start _fd .StartElement )error {start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0077o\u0072\u003a\u0070\u006f\u0073"},Value :_g .Sprintf ("\u0025\u0076",_cgeb .PosAttr )});
e .EncodeToken (start );if _cgeb .SrgbClr !=nil {_bgae :=_fd .StartElement {Name :_fd .Name {Local :"w\u006f\u0072\u003a\u0073\u0072\u0067\u0062\u0043\u006c\u0072"}};e .EncodeElement (_cgeb .SrgbClr ,_bgae );};if _cgeb .SchemeClr !=nil {_ebag :=_fd .StartElement {Name :_fd .Name {Local :"\u0077\u006f\u0072\u003a\u0073\u0063\u0068\u0065\u006d\u0065\u0043\u006c\u0072"}};
e .EncodeElement (_cgeb .SchemeClr ,_ebag );};e .EncodeToken (_fd .EndElement {Name :start .Name });return nil ;};const (ST_PathShadeTypeUnset ST_PathShadeType =0;ST_PathShadeTypeShape ST_PathShadeType =1;ST_PathShadeTypeCircle ST_PathShadeType =2;ST_PathShadeTypeRect ST_PathShadeType =3;
);type EG_ColorTransform struct{ColorTransformChoice *EG_ColorTransformChoice ;};func (_bega *CT_PositivePercentage )MarshalXML (e *_fd .Encoder ,start _fd .StartElement )error {start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0077o\u0072\u003a\u0076\u0061\u006c"},Value :_g .Sprintf ("\u0025\u0076",_bega .ValAttr )});
e .EncodeToken (start );e .EncodeToken (_fd .EndElement {Name :start .Name });return nil ;};func (_agdd *EG_ShadeProperties )MarshalXML (e *_fd .Encoder ,start _fd .StartElement )error {_agdd .ShadePropertiesChoice .MarshalXML (e ,_fd .StartElement {});
return nil ;};func (_afae ST_CompoundLine )MarshalXMLAttr (name _fd .Name )(_fd .Attr ,error ){_dfgfb :=_fd .Attr {};_dfgfb .Name =name ;switch _afae {case ST_CompoundLineUnset :_dfgfb .Value ="";case ST_CompoundLineSng :_dfgfb .Value ="\u0073\u006e\u0067";
case ST_CompoundLineDbl :_dfgfb .Value ="\u0064\u0062\u006c";case ST_CompoundLineThickThin :_dfgfb .Value ="\u0074h\u0069\u0063\u006b\u0054\u0068\u0069n";case ST_CompoundLineThinThick :_dfgfb .Value ="\u0074h\u0069\u006e\u0054\u0068\u0069\u0063k";case ST_CompoundLineTri :_dfgfb .Value ="\u0074\u0072\u0069";
};return _dfgfb ,nil ;};func (_fccg *EG_LineJoinProperties )UnmarshalXML (d *_fd .Decoder ,start _fd .StartElement )error {_fccg .LineJoinPropertiesChoice =NewEG_LineJoinPropertiesChoice ();_gebc :for {_eefb ,_fcea :=d .Token ();if _fcea !=nil {return _fcea ;
};switch _cdbd :=_eefb .(type ){case _fd .StartElement :switch _cdbd .Name {case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u0072\u006f\u0075n\u0064"}:_fccg .LineJoinPropertiesChoice =NewEG_LineJoinPropertiesChoice ();
if _egfb :=d .DecodeElement (&_fccg .LineJoinPropertiesChoice .Round ,&_cdbd );_egfb !=nil {return _egfb ;};case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u0062\u0065\u0076e\u006c"}:_fccg .LineJoinPropertiesChoice =NewEG_LineJoinPropertiesChoice ();
if _agcf :=d .DecodeElement (&_fccg .LineJoinPropertiesChoice .Bevel ,&_cdbd );_agcf !=nil {return _agcf ;};case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u006d\u0069\u0074e\u0072"}:_fccg .LineJoinPropertiesChoice =NewEG_LineJoinPropertiesChoice ();
if _dbggb :=d .DecodeElement (&_fccg .LineJoinPropertiesChoice .Miter ,&_cdbd );_dbggb !=nil {return _dbggb ;};default:_c .Log .Debug ("\u0073\u006b\u0069\u0070\u0070\u0069n\u0067\u0020\u0075n\u0073\u0075\u0070p\u006f\u0072\u0074\u0065\u0064\u0020\u0065\u006c\u0065\u006de\u006e\u0074\u0020\u006f\u006e E\u0047\u005f\u004c\u0069\u006e\u0065\u004a\u006f\u0069\u006e\u0050\u0072\u006f\u0070\u0065\u0072\u0074\u0069\u0065\u0073\u0020\u0025\u0076",_cdbd .Name );
if _abece :=d .Skip ();_abece !=nil {return _abece ;};};case _fd .EndElement :break _gebc ;case _fd .CharData :};};return nil ;};

// Validate validates the CT_SRgbColor and its children
func (_afbb *CT_SRgbColor )Validate ()error {return _afbb .ValidateWithPath ("\u0043\u0054\u005fS\u0052\u0067\u0062\u0043\u006f\u006c\u006f\u0072");};

// ValidateWithPath validates the CT_Ligatures and its children, prefixing error messages with path
func (_cda *CT_Ligatures )ValidateWithPath (path string )error {if _cda .ValAttr ==ST_LigaturesUnset {return _g .Errorf ("\u0025\u0073\u002fV\u0061\u006c\u0041\u0074t\u0072\u0020\u0069\u0073\u0020\u0061\u0020m\u0061\u006e\u0064\u0061\u0074\u006f\u0072\u0079\u0020\u0066\u0069\u0065\u006c\u0064",path );
};if _aae :=_cda .ValAttr .ValidateWithPath (path +"\u002f\u0056\u0061\u006c\u0041\u0074\u0074\u0072");_aae !=nil {return _aae ;};return nil ;};type ST_LightRigDirection byte ;

// Validate validates the ConflictMode and its children
func (_aaae *ConflictMode )Validate ()error {return _aaae .ValidateWithPath ("\u0043\u006f\u006ef\u006c\u0069\u0063\u0074\u004d\u006f\u0064\u0065");};func NewCustomXmlConflictInsRangeEnd ()*CustomXmlConflictInsRangeEnd {_dceg :=&CustomXmlConflictInsRangeEnd {};
_dceg .CT_Markup =*_fe .NewCT_Markup ();return _dceg ;};func NewDiscardImageEditingData ()*DiscardImageEditingData {_cfegdc :=&DiscardImageEditingData {};_cfegdc .CT_OnOff =*NewCT_OnOff ();return _cfegdc ;};func (_afgad *ST_NumForm )UnmarshalXML (d *_fd .Decoder ,start _fd .StartElement )error {_ebeaa ,_ggegb :=d .Token ();
if _ggegb !=nil {return _ggegb ;};if _dacb ,_ccba :=_ebeaa .(_fd .EndElement );_ccba &&_dacb .Name ==start .Name {*_afgad =1;return nil ;};if _abgg ,_fggab :=_ebeaa .(_fd .CharData );!_fggab {return _g .Errorf ("\u0065\u0078\u0070\u0065\u0063\u0074\u0065\u0064\u0020\u0063\u0068a\u0072\u0020\u0064\u0061\u0074\u0061\u002c\u0020\u0067\u006ft\u0020\u0025\u0054",_ebeaa );
}else {switch string (_abgg ){case "":*_afgad =0;case "\u0064e\u0066\u0061\u0075\u006c\u0074":*_afgad =1;case "\u006c\u0069\u006e\u0069\u006e\u0067":*_afgad =2;case "\u006f\u006c\u0064\u0053\u0074\u0079\u006c\u0065":*_afgad =3;};};_ebeaa ,_ggegb =d .Token ();
if _ggegb !=nil {return _ggegb ;};if _agbc ,_ceae :=_ebeaa .(_fd .EndElement );_ceae &&_agbc .Name ==start .Name {return nil ;};return _g .Errorf ("\u0065\u0078\u0070\u0065c\u0074\u0065\u0064\u0020\u0065\u006e\u0064\u0020\u0065\u006ce\u006de\u006e\u0074\u002c\u0020\u0067\u006f\u0074 \u0025\u0076",_ebeaa );
};func NewCT_Reflection ()*CT_Reflection {_eebg :=&CT_Reflection {};return _eebg };func (_ddca *CT_LinearShadeProperties )UnmarshalXML (d *_fd .Decoder ,start _fd .StartElement )error {for _ ,_ege :=range start .Attr {if _ege .Name .Local =="\u0061\u006e\u0067"{_dabg ,_edg :=_a .ParseInt (_ege .Value ,10,32);
if _edg !=nil {return _edg ;};_fea :=int32 (_dabg );_ddca .AngAttr =&_fea ;continue ;};if _ege .Name .Local =="\u0073\u0063\u0061\u006c\u0065\u0064"{_ddca .ScaledAttr .UnmarshalXMLAttr (_ege );continue ;};};for {_bdg ,_cceg :=d .Token ();if _cceg !=nil {return _g .Errorf ("\u0070\u0061rs\u0069\u006e\u0067 \u0043\u0054\u005f\u004cine\u0061rS\u0068\u0061\u0064\u0065\u0050\u0072\u006fpe\u0072\u0074\u0069\u0065\u0073\u003a\u0020%\u0073",_cceg );
};if _dba ,_ddd :=_bdg .(_fd .EndElement );_ddd &&_dba .Name ==start .Name {break ;};};return nil ;};

// ValidateWithPath validates the EG_RunLevelConflicts and its children, prefixing error messages with path
func (_fbfe *EG_RunLevelConflicts )ValidateWithPath (path string )error {if _fbfe .ConflictIns !=nil {if _ebea :=_fbfe .ConflictIns .ValidateWithPath (path +"\u002f\u0043\u006fn\u0066\u006c\u0069\u0063\u0074\u0049\u006e\u0073");_ebea !=nil {return _ebea ;
};};if _fbfe .ConflictDel !=nil {if _cccd :=_fbfe .ConflictDel .ValidateWithPath (path +"\u002f\u0043\u006fn\u0066\u006c\u0069\u0063\u0074\u0044\u0065\u006c");_cccd !=nil {return _cccd ;};};return nil ;};func (_gbgb *CT_Percentage )MarshalXML (e *_fd .Encoder ,start _fd .StartElement )error {start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0077o\u0072\u003a\u0076\u0061\u006c"},Value :_g .Sprintf ("\u0025\u0076",_gbgb .ValAttr )});
e .EncodeToken (start );e .EncodeToken (_fd .EndElement {Name :start .Name });return nil ;};

// ValidateWithPath validates the CT_PresetLineDashProperties and its children, prefixing error messages with path
func (_aea *CT_PresetLineDashProperties )ValidateWithPath (path string )error {if _gec :=_aea .ValAttr .ValidateWithPath (path +"\u002f\u0056\u0061\u006c\u0041\u0074\u0074\u0072");_gec !=nil {return _gec ;};return nil ;};func (_eaca ST_OnOff )String ()string {switch _eaca {case 0:return "";
case 1:return "\u0074\u0072\u0075\u0065";case 2:return "\u0066\u0061\u006cs\u0065";case 3:return "\u0030";case 4:return "\u0031";};return "";};func (_egbc *ST_PresetMaterialType )UnmarshalXMLAttr (attr _fd .Attr )error {switch attr .Value {case "":*_egbc =0;
case "l\u0065\u0067\u0061\u0063\u0079\u004d\u0061\u0074\u0074\u0065":*_egbc =1;case "\u006c\u0065\u0067\u0061\u0063\u0079\u0050\u006c\u0061\u0073\u0074\u0069\u0063":*_egbc =2;case "l\u0065\u0067\u0061\u0063\u0079\u004d\u0065\u0074\u0061\u006c":*_egbc =3;
case "\u006ce\u0067a\u0063\u0079\u0057\u0069\u0072\u0065\u0066\u0072\u0061\u006d\u0065":*_egbc =4;case "\u006d\u0061\u0074t\u0065":*_egbc =5;case "\u0070l\u0061\u0073\u0074\u0069\u0063":*_egbc =6;case "\u006d\u0065\u0074a\u006c":*_egbc =7;case "\u0077a\u0072\u006d\u004d\u0061\u0074\u0074e":*_egbc =8;
case "\u0074\u0072\u0061\u006e\u0073\u006c\u0075\u0063\u0065\u006e\u0074\u0050o\u0077\u0064\u0065\u0072":*_egbc =9;case "\u0070\u006f\u0077\u0064\u0065\u0072":*_egbc =10;case "\u0064\u006b\u0045\u0064\u0067\u0065":*_egbc =11;case "\u0073\u006f\u0066\u0074\u0045\u0064\u0067\u0065":*_egbc =12;
case "\u0063\u006c\u0065a\u0072":*_egbc =13;case "\u0066\u006c\u0061\u0074":*_egbc =14;case "\u0073o\u0066\u0074\u006d\u0065\u0074\u0061l":*_egbc =15;case "\u006e\u006f\u006e\u0065":*_egbc =16;};return nil ;};

// ValidateWithPath validates the CT_SphereCoords and its children, prefixing error messages with path
func (_efage *CT_SphereCoords )ValidateWithPath (path string )error {if _efage .LatAttr < 0{return _g .Errorf ("%\u0073\u002f\u006d\u002e\u004c\u0061t\u0041\u0074\u0074\u0072\u0020\u006du\u0073\u0074\u0020\u0062\u0065\u0020\u003e=\u0020\u0030\u0020\u0028\u0068\u0061\u0076\u0065\u0020\u0025v\u0029",path ,_efage .LatAttr );
};if _efage .LatAttr >=21600000{return _g .Errorf ("\u0025\u0073\u002f\u006d\u002eL\u0061\u0074\u0041\u0074\u0074\u0072\u0020\u006d\u0075\u0073\u0074\u0020\u0062e\u0020\u003c\u0020\u0032\u0031\u0036\u0030\u0030\u0030\u0030\u0030\u0020\u0028\u0068\u0061\u0076\u0065\u0020\u0025\u0076\u0029",path ,_efage .LatAttr );
};if _efage .LonAttr < 0{return _g .Errorf ("%\u0073\u002f\u006d\u002e\u004c\u006fn\u0041\u0074\u0074\u0072\u0020\u006du\u0073\u0074\u0020\u0062\u0065\u0020\u003e=\u0020\u0030\u0020\u0028\u0068\u0061\u0076\u0065\u0020\u0025v\u0029",path ,_efage .LonAttr );
};if _efage .LonAttr >=21600000{return _g .Errorf ("\u0025\u0073\u002f\u006d\u002eL\u006f\u006e\u0041\u0074\u0074\u0072\u0020\u006d\u0075\u0073\u0074\u0020\u0062e\u0020\u003c\u0020\u0032\u0031\u0036\u0030\u0030\u0030\u0030\u0030\u0020\u0028\u0068\u0061\u0076\u0065\u0020\u0025\u0076\u0029",path ,_efage .LonAttr );
};if _efage .RevAttr < 0{return _g .Errorf ("%\u0073\u002f\u006d\u002e\u0052\u0065v\u0041\u0074\u0074\u0072\u0020\u006du\u0073\u0074\u0020\u0062\u0065\u0020\u003e=\u0020\u0030\u0020\u0028\u0068\u0061\u0076\u0065\u0020\u0025v\u0029",path ,_efage .RevAttr );
};if _efage .RevAttr >=21600000{return _g .Errorf ("\u0025\u0073\u002f\u006d\u002eR\u0065\u0076\u0041\u0074\u0074\u0072\u0020\u006d\u0075\u0073\u0074\u0020\u0062e\u0020\u003c\u0020\u0032\u0031\u0036\u0030\u0030\u0030\u0030\u0030\u0020\u0028\u0068\u0061\u0076\u0065\u0020\u0025\u0076\u0029",path ,_efage .RevAttr );
};return nil ;};func (_eabfc ST_BevelPresetType )Validate ()error {return _eabfc .ValidateWithPath ("")};func NewDefaultImageDpi ()*DefaultImageDpi {_cfbb :=&DefaultImageDpi {};_cfbb .CT_DefaultImageDpi =*NewCT_DefaultImageDpi ();return _cfbb ;};

// ValidateWithPath validates the CT_LinearShadeProperties and its children, prefixing error messages with path
func (_fbba *CT_LinearShadeProperties )ValidateWithPath (path string )error {if _fbba .AngAttr !=nil {if *_fbba .AngAttr < 0{return _g .Errorf ("%\u0073\u002f\u006d\u002e\u0041\u006eg\u0041\u0074\u0074\u0072\u0020\u006du\u0073\u0074\u0020\u0062\u0065\u0020\u003e=\u0020\u0030\u0020\u0028\u0068\u0061\u0076\u0065\u0020\u0025v\u0029",path ,*_fbba .AngAttr );
};if *_fbba .AngAttr >=21600000{return _g .Errorf ("\u0025\u0073\u002f\u006d\u002eA\u006e\u0067\u0041\u0074\u0074\u0072\u0020\u006d\u0075\u0073\u0074\u0020\u0062e\u0020\u003c\u0020\u0032\u0031\u0036\u0030\u0030\u0030\u0030\u0030\u0020\u0028\u0068\u0061\u0076\u0065\u0020\u0025\u0076\u0029",path ,*_fbba .AngAttr );
};};if _fbf :=_fbba .ScaledAttr .ValidateWithPath (path +"/\u0053\u0063\u0061\u006c\u0065\u0064\u0041\u0074\u0074\u0072");_fbf !=nil {return _fbf ;};return nil ;};func (_geac ST_PathShadeType )Validate ()error {return _geac .ValidateWithPath ("")};func (_gefg ST_LineCap )ValidateWithPath (path string )error {switch _gefg {case 0,1,2,3:default:return _g .Errorf ("\u0025s\u003a\u0020\u006f\u0075t\u0020\u006f\u0066\u0020\u0072a\u006eg\u0065 \u0076\u0061\u006c\u0075\u0065\u0020\u0025d",path ,int (_gefg ));
};return nil ;};

// Validate validates the EG_LineDashProperties and its children
func (_daa *EG_LineDashProperties )Validate ()error {return _daa .ValidateWithPath ("E\u0047\u005f\u004c\u0069ne\u0044a\u0073\u0068\u0050\u0072\u006fp\u0065\u0072\u0074\u0069\u0065\u0073");};func (_gad *ConflictMode )UnmarshalXML (d *_fd .Decoder ,start _fd .StartElement )error {_gad .CT_OnOff =*NewCT_OnOff ();
for _ ,_bcbc :=range start .Attr {if _bcbc .Name .Local =="\u0076\u0061\u006c"{_gad .ValAttr .UnmarshalXMLAttr (_bcbc );continue ;};};for {_abbf ,_febe :=d .Token ();if _febe !=nil {return _g .Errorf ("\u0070a\u0072\u0073\u0069\u006e\u0067\u0020\u0043\u006f\u006e\u0066\u006ci\u0063\u0074\u004d\u006f\u0064\u0065\u003a\u0020\u0025\u0073",_febe );
};if _becb ,_fdce :=_abbf .(_fd .EndElement );_fdce &&_becb .Name ==start .Name {break ;};};return nil ;};

// Validate validates the CT_RelativeRect and its children
func (_cgdc *CT_RelativeRect )Validate ()error {return _cgdc .ValidateWithPath ("\u0043T\u005fR\u0065\u006c\u0061\u0074\u0069\u0076\u0065\u0052\u0065\u0063\u0074");};

// ValidateWithPath validates the CT_LineJoinMiterProperties and its children, prefixing error messages with path
func (_ace *CT_LineJoinMiterProperties )ValidateWithPath (path string )error {if _ace .LimAttr !=nil {if _gcf :=_ace .LimAttr .ValidateWithPath (path +"\u002f\u004c\u0069\u006d\u0041\u0074\u0074\u0072");_gcf !=nil {return _gcf ;};};return nil ;};func NewCT_NumSpacing ()*CT_NumSpacing {_ggc :=&CT_NumSpacing {};
_ggc .ValAttr =ST_NumSpacing (1);return _ggc ;};func (_fgfb ST_PresetLineDashVal )Validate ()error {return _fgfb .ValidateWithPath ("")};func NewCT_Scene3D ()*CT_Scene3D {_gabe :=&CT_Scene3D {};_gabe .Camera =NewCT_Camera ();_gabe .LightRig =NewCT_LightRig ();
return _gabe ;};type CT_SdtCheckbox struct{Checked *CT_OnOff ;CheckedState *CT_SdtCheckboxSymbol ;UncheckedState *CT_SdtCheckboxSymbol ;};func (_bcac *ST_Ligatures )UnmarshalXML (d *_fd .Decoder ,start _fd .StartElement )error {_eddb ,_eag :=d .Token ();
if _eag !=nil {return _eag ;};if _gcfcg ,_aecd :=_eddb .(_fd .EndElement );_aecd &&_gcfcg .Name ==start .Name {*_bcac =1;return nil ;};if _abed ,_bcdd :=_eddb .(_fd .CharData );!_bcdd {return _g .Errorf ("\u0065\u0078\u0070\u0065\u0063\u0074\u0065\u0064\u0020\u0063\u0068a\u0072\u0020\u0064\u0061\u0074\u0061\u002c\u0020\u0067\u006ft\u0020\u0025\u0054",_eddb );
}else {switch string (_abed ){case "":*_bcac =0;case "\u006e\u006f\u006e\u0065":*_bcac =1;case "\u0073\u0074\u0061\u006e\u0064\u0061\u0072\u0064":*_bcac =2;case "\u0063\u006f\u006e\u0074\u0065\u0078\u0074\u0075\u0061\u006c":*_bcac =3;case "\u0068\u0069\u0073\u0074\u006f\u0072\u0069\u0063\u0061\u006c":*_bcac =4;
case "\u0064\u0069\u0073c\u0072\u0065\u0074\u0069\u006f\u006e\u0061\u006c":*_bcac =5;case "\u0073t\u0061n\u0064\u0061\u0072\u0064\u0043o\u006e\u0074e\u0078\u0074\u0075\u0061\u006c":*_bcac =6;case "\u0073t\u0061n\u0064\u0061\u0072\u0064\u0048i\u0073\u0074o\u0072\u0069\u0063\u0061\u006c":*_bcac =7;
case "c\u006fn\u0074\u0065\u0078\u0074\u0075\u0061\u006c\u0048i\u0073\u0074\u006f\u0072ic\u0061\u006c":*_bcac =8;case "s\u0074a\u006e\u0064\u0061\u0072\u0064\u0044\u0069\u0073c\u0072\u0065\u0074\u0069on\u0061\u006c":*_bcac =9;case "\u0063\u006f\u006e\u0074ex\u0074\u0075\u0061\u006c\u0044\u0069\u0073\u0063\u0072\u0065\u0074\u0069\u006f\u006ea\u006c":*_bcac =10;
case "\u0068\u0069\u0073\u0074or\u0069\u0063\u0061\u006c\u0044\u0069\u0073\u0063\u0072\u0065\u0074\u0069\u006f\u006ea\u006c":*_bcac =11;case "\u0073\u0074\u0061\u006ed\u0061\u0072\u0064\u0043\u006f\u006e\u0074\u0065\u0078\u0074u\u0061l\u0048\u0069\u0073\u0074\u006f\u0072\u0069c\u0061\u006c":*_bcac =12;
case "\u0073\u0074\u0061\u006e\u0064\u0061\u0072\u0064\u0043\u006fn\u0074\u0065\u0078\u0074\u0075\u0061\u006cD\u0069\u0073\u0063\u0072\u0065\u0074\u0069\u006f\u006e\u0061\u006c":*_bcac =13;case "\u0073\u0074\u0061\u006e\u0064\u0061\u0072\u0064\u0048\u0069s\u0074\u006f\u0072\u0069\u0063\u0061\u006cD\u0069\u0073\u0063\u0072\u0065\u0074\u0069\u006f\u006e\u0061\u006c":*_bcac =14;
case "\u0063\u006f\u006e\u0074\u0065\u0078\u0074\u0075\u0061\u006c\u0048\u0069\u0073\u0074\u006fr\u0069c\u0061\u006c\u0044\u0069\u0073\u0063\u0072\u0065\u0074\u0069\u006f\u006e\u0061\u006c":*_bcac =15;case "\u0061\u006c\u006c":*_bcac =16;};};_eddb ,_eag =d .Token ();
if _eag !=nil {return _eag ;};if _gede ,_bcef :=_eddb .(_fd .EndElement );_bcef &&_gede .Name ==start .Name {return nil ;};return _g .Errorf ("\u0065\u0078\u0070\u0065c\u0074\u0065\u0064\u0020\u0065\u006e\u0064\u0020\u0065\u006ce\u006de\u006e\u0074\u002c\u0020\u0067\u006f\u0074 \u0025\u0076",_eddb );
};type ST_BevelPresetType byte ;func (_ggfa *CT_Reflection )MarshalXML (e *_fd .Encoder ,start _fd .StartElement )error {if _ggfa .BlurRadAttr !=nil {start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"w\u006f\u0072\u003a\u0062\u006c\u0075\u0072\u0052\u0061\u0064"},Value :_g .Sprintf ("\u0025\u0076",*_ggfa .BlurRadAttr )});
};if _ggfa .StAAttr !=nil {start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0077o\u0072\u003a\u0073\u0074\u0041"},Value :_g .Sprintf ("\u0025\u0076",*_ggfa .StAAttr )});};if _ggfa .StPosAttr !=nil {start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0077o\u0072\u003a\u0073\u0074\u0050\u006fs"},Value :_g .Sprintf ("\u0025\u0076",*_ggfa .StPosAttr )});
};if _ggfa .EndAAttr !=nil {start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0077\u006f\u0072\u003a\u0065\u006e\u0064\u0041"},Value :_g .Sprintf ("\u0025\u0076",*_ggfa .EndAAttr )});};if _ggfa .EndPosAttr !=nil {start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0077\u006f\u0072\u003a\u0065\u006e\u0064\u0050\u006f\u0073"},Value :_g .Sprintf ("\u0025\u0076",*_ggfa .EndPosAttr )});
};if _ggfa .DistAttr !=nil {start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0077\u006f\u0072\u003a\u0064\u0069\u0073\u0074"},Value :_g .Sprintf ("\u0025\u0076",*_ggfa .DistAttr )});};if _ggfa .DirAttr !=nil {start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0077o\u0072\u003a\u0064\u0069\u0072"},Value :_g .Sprintf ("\u0025\u0076",*_ggfa .DirAttr )});
};if _ggfa .FadeDirAttr !=nil {start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"w\u006f\u0072\u003a\u0066\u0061\u0064\u0065\u0044\u0069\u0072"},Value :_g .Sprintf ("\u0025\u0076",*_ggfa .FadeDirAttr )});};if _ggfa .SxAttr !=nil {start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0077\u006f\u0072\u003a\u0073\u0078"},Value :_g .Sprintf ("\u0025\u0076",*_ggfa .SxAttr )});
};if _ggfa .SyAttr !=nil {start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0077\u006f\u0072\u003a\u0073\u0079"},Value :_g .Sprintf ("\u0025\u0076",*_ggfa .SyAttr )});};if _ggfa .KxAttr !=nil {start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0077\u006f\u0072\u003a\u006b\u0078"},Value :_g .Sprintf ("\u0025\u0076",*_ggfa .KxAttr )});
};if _ggfa .KyAttr !=nil {start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0077\u006f\u0072\u003a\u006b\u0079"},Value :_g .Sprintf ("\u0025\u0076",*_ggfa .KyAttr )});};if _ggfa .AlgnAttr !=ST_RectAlignmentUnset {_ggce ,_aaf :=_ggfa .AlgnAttr .MarshalXMLAttr (_fd .Name {Local :"\u0077\u006f\u0072\u003a\u0061\u006c\u0067\u006e"});
if _aaf !=nil {return _aaf ;};start .Attr =append (start .Attr ,_ggce );};e .EncodeToken (start );e .EncodeToken (_fd .EndElement {Name :start .Name });return nil ;};func NewCustomXmlConflictDelRangeStart ()*CustomXmlConflictDelRangeStart {_faed :=&CustomXmlConflictDelRangeStart {};
_faed .CT_TrackChange =*_fe .NewCT_TrackChange ();return _faed ;};func (_aeaa *CT_SdtCheckboxSymbol )UnmarshalXML (d *_fd .Decoder ,start _fd .StartElement )error {for _ ,_ffbe :=range start .Attr {if _ffbe .Name .Local =="\u0066\u006f\u006e\u0074"{_gegc :=_ffbe .Value ;
_aeaa .FontAttr =&_gegc ;continue ;};if _ffbe .Name .Local =="\u0076\u0061\u006c"{_cdfb :=_ffbe .Value ;_aeaa .ValAttr =&_cdfb ;continue ;};};for {_eaedf ,_dge :=d .Token ();if _dge !=nil {return _g .Errorf ("\u0070\u0061\u0072\u0073\u0069\u006e\u0067\u0020\u0043\u0054\u005f\u0053\u0064\u0074\u0043h\u0065c\u006b\u0062\u006f\u0078\u0053\u0079\u006d\u0062\u006f\u006c\u003a\u0020\u0025\u0073",_dge );
};if _gggf ,_efb :=_eaedf .(_fd .EndElement );_efb &&_gggf .Name ==start .Name {break ;};};return nil ;};func (_dcfc *Checkbox )UnmarshalXML (d *_fd .Decoder ,start _fd .StartElement )error {_dcfc .CT_SdtCheckbox =*NewCT_SdtCheckbox ();_dagd :for {_gcfe ,_eabc :=d .Token ();
if _eabc !=nil {return _eabc ;};switch _gceb :=_gcfe .(type ){case _fd .StartElement :switch _gceb .Name {case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u0063h\u0065\u0063\u006b\u0065\u0064"}:_dcfc .Checked =NewCT_OnOff ();
if _egdb :=d .DecodeElement (_dcfc .Checked ,&_gceb );_egdb !=nil {return _egdb ;};case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u0063\u0068\u0065c\u006b\u0065\u0064\u0053\u0074\u0061\u0074\u0065"}:_dcfc .CheckedState =NewCT_SdtCheckboxSymbol ();
if _cgca :=d .DecodeElement (_dcfc .CheckedState ,&_gceb );_cgca !=nil {return _cgca ;};case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u0075\u006e\u0063\u0068\u0065\u0063\u006b\u0065\u0064S\u0074\u0061\u0074\u0065"}:_dcfc .UncheckedState =NewCT_SdtCheckboxSymbol ();
if _cdad :=d .DecodeElement (_dcfc .UncheckedState ,&_gceb );_cdad !=nil {return _cdad ;};default:_c .Log .Debug ("\u0073\u006b\u0069\u0070\u0070\u0069\u006eg\u0020\u0075\u006es\u0075\u0070\u0070\u006fr\u0074\u0065\u0064\u0020\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0043\u0068\u0065\u0063\u006b\u0062\u006f\u0078\u0020\u0025\u0076",_gceb .Name );
if _fbc :=d .Skip ();_fbc !=nil {return _fbc ;};};case _fd .EndElement :break _dagd ;case _fd .CharData :};};return nil ;};func (_gdgf *EG_ColorTransformChoice )MarshalXML (e *_fd .Encoder ,start _fd .StartElement )error {e .EncodeToken (start );if _gdgf .Tint !=nil {_bbdb :=_fd .StartElement {Name :_fd .Name {Local :"\u0077\u006f\u0072\u003a\u0074\u0069\u006e\u0074"}};
e .EncodeElement (_gdgf .Tint ,_bbdb );}else if _gdgf .Shade !=nil {_ffgf :=_fd .StartElement {Name :_fd .Name {Local :"\u0077o\u0072\u003a\u0073\u0068\u0061\u0064e"}};e .EncodeElement (_gdgf .Shade ,_ffgf );}else if _gdgf .Alpha !=nil {_bfef :=_fd .StartElement {Name :_fd .Name {Local :"\u0077o\u0072\u003a\u0061\u006c\u0070\u0068a"}};
e .EncodeElement (_gdgf .Alpha ,_bfef );}else if _gdgf .HueMod !=nil {_dfcb :=_fd .StartElement {Name :_fd .Name {Local :"\u0077\u006f\u0072\u003a\u0068\u0075\u0065\u004d\u006f\u0064"}};e .EncodeElement (_gdgf .HueMod ,_dfcb );}else if _gdgf .Sat !=nil {_baaf :=_fd .StartElement {Name :_fd .Name {Local :"\u0077o\u0072\u003a\u0073\u0061\u0074"}};
e .EncodeElement (_gdgf .Sat ,_baaf );}else if _gdgf .SatOff !=nil {_gca :=_fd .StartElement {Name :_fd .Name {Local :"\u0077\u006f\u0072\u003a\u0073\u0061\u0074\u004f\u0066\u0066"}};e .EncodeElement (_gdgf .SatOff ,_gca );}else if _gdgf .SatMod !=nil {_fdca :=_fd .StartElement {Name :_fd .Name {Local :"\u0077\u006f\u0072\u003a\u0073\u0061\u0074\u004d\u006f\u0064"}};
e .EncodeElement (_gdgf .SatMod ,_fdca );}else if _gdgf .Lum !=nil {_edbc :=_fd .StartElement {Name :_fd .Name {Local :"\u0077o\u0072\u003a\u006c\u0075\u006d"}};e .EncodeElement (_gdgf .Lum ,_edbc );}else if _gdgf .LumOff !=nil {_egec :=_fd .StartElement {Name :_fd .Name {Local :"\u0077\u006f\u0072\u003a\u006c\u0075\u006d\u004f\u0066\u0066"}};
e .EncodeElement (_gdgf .LumOff ,_egec );}else if _gdgf .LumMod !=nil {_dfcc :=_fd .StartElement {Name :_fd .Name {Local :"\u0077\u006f\u0072\u003a\u006c\u0075\u006d\u004d\u006f\u0064"}};e .EncodeElement (_gdgf .LumMod ,_dfcc );};e .EncodeToken (_fd .EndElement {Name :start .Name });
return nil ;};

// Validate validates the Checkbox and its children
func (_eaef *Checkbox )Validate ()error {return _eaef .ValidateWithPath ("\u0043\u0068\u0065\u0063\u006b\u0062\u006f\u0078");};type EG_LineJoinPropertiesChoice struct{Round *_fe .CT_Empty ;Bevel *_fe .CT_Empty ;Miter *CT_LineJoinMiterProperties ;};func (_edaa ST_LightRigDirection )String ()string {switch _edaa {case 0:return "";
case 1:return "\u0074\u006c";case 2:return "\u0074";case 3:return "\u0074\u0072";case 4:return "\u006c";case 5:return "\u0072";case 6:return "\u0062\u006c";case 7:return "\u0062";case 8:return "\u0062\u0072";};return "";};

// Validate validates the CT_LightRig and its children
func (_gaad *CT_LightRig )Validate ()error {return _gaad .ValidateWithPath ("C\u0054\u005f\u004c\u0069\u0067\u0068\u0074\u0052\u0069\u0067");};type CT_FillTextEffect struct{FillPropertiesChoice *EG_FillPropertiesChoice ;};

// ValidateWithPath validates the EG_ColorTransform and its children, prefixing error messages with path
func (_bgaa *EG_ColorTransform )ValidateWithPath (path string )error {if _deec :=_bgaa .ColorTransformChoice .ValidateWithPath (path +"/\u0043\u006f\u006c\u006frT\u0072a\u006e\u0073\u0066\u006f\u0072m\u0043\u0068\u006f\u0069\u0063\u0065");_deec !=nil {return _deec ;
};return nil ;};func (_deeeg *ST_CompoundLine )UnmarshalXML (d *_fd .Decoder ,start _fd .StartElement )error {_fbfg ,_ddef :=d .Token ();if _ddef !=nil {return _ddef ;};if _bcga ,_cdaa :=_fbfg .(_fd .EndElement );_cdaa &&_bcga .Name ==start .Name {*_deeeg =1;
return nil ;};if _gefc ,_bbbbd :=_fbfg .(_fd .CharData );!_bbbbd {return _g .Errorf ("\u0065\u0078\u0070\u0065\u0063\u0074\u0065\u0064\u0020\u0063\u0068a\u0072\u0020\u0064\u0061\u0074\u0061\u002c\u0020\u0067\u006ft\u0020\u0025\u0054",_fbfg );}else {switch string (_gefc ){case "":*_deeeg =0;
case "\u0073\u006e\u0067":*_deeeg =1;case "\u0064\u0062\u006c":*_deeeg =2;case "\u0074h\u0069\u0063\u006b\u0054\u0068\u0069n":*_deeeg =3;case "\u0074h\u0069\u006e\u0054\u0068\u0069\u0063k":*_deeeg =4;case "\u0074\u0072\u0069":*_deeeg =5;};};_fbfg ,_ddef =d .Token ();
if _ddef !=nil {return _ddef ;};if _gffe ,_cagg :=_fbfg .(_fd .EndElement );_cagg &&_gffe .Name ==start .Name {return nil ;};return _g .Errorf ("\u0065\u0078\u0070\u0065c\u0074\u0065\u0064\u0020\u0065\u006e\u0064\u0020\u0065\u006ce\u006de\u006e\u0074\u002c\u0020\u0067\u006f\u0074 \u0025\u0076",_fbfg );
};func (_dfdgd *EG_ColorTransform )UnmarshalXML (d *_fd .Decoder ,start _fd .StartElement )error {_dfdgd .ColorTransformChoice =NewEG_ColorTransformChoice ();_gbada :for {_acbfa ,_fdeb :=d .Token ();if _fdeb !=nil {return _fdeb ;};switch _dgdbb :=_acbfa .(type ){case _fd .StartElement :switch _dgdbb .Name {case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u0074\u0069\u006e\u0074"}:_dfdgd .ColorTransformChoice =NewEG_ColorTransformChoice ();
if _dcdc :=d .DecodeElement (&_dfdgd .ColorTransformChoice .Tint ,&_dgdbb );_dcdc !=nil {return _dcdc ;};case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u0073\u0068\u0061d\u0065"}:_dfdgd .ColorTransformChoice =NewEG_ColorTransformChoice ();
if _bbge :=d .DecodeElement (&_dfdgd .ColorTransformChoice .Shade ,&_dgdbb );_bbge !=nil {return _bbge ;};case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u0061\u006c\u0070h\u0061"}:_dfdgd .ColorTransformChoice =NewEG_ColorTransformChoice ();
if _cbeef :=d .DecodeElement (&_dfdgd .ColorTransformChoice .Alpha ,&_dgdbb );_cbeef !=nil {return _cbeef ;};case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u0068\u0075\u0065\u004d\u006f\u0064"}:_dfdgd .ColorTransformChoice =NewEG_ColorTransformChoice ();
if _cfcd :=d .DecodeElement (&_dfdgd .ColorTransformChoice .HueMod ,&_dgdbb );_cfcd !=nil {return _cfcd ;};case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u0073\u0061\u0074"}:_dfdgd .ColorTransformChoice =NewEG_ColorTransformChoice ();
if _gceac :=d .DecodeElement (&_dfdgd .ColorTransformChoice .Sat ,&_dgdbb );_gceac !=nil {return _gceac ;};case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u0073\u0061\u0074\u004f\u0066\u0066"}:_dfdgd .ColorTransformChoice =NewEG_ColorTransformChoice ();
if _gbbf :=d .DecodeElement (&_dfdgd .ColorTransformChoice .SatOff ,&_dgdbb );_gbbf !=nil {return _gbbf ;};case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u0073\u0061\u0074\u004d\u006f\u0064"}:_dfdgd .ColorTransformChoice =NewEG_ColorTransformChoice ();
if _fbbd :=d .DecodeElement (&_dfdgd .ColorTransformChoice .SatMod ,&_dgdbb );_fbbd !=nil {return _fbbd ;};case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u006c\u0075\u006d"}:_dfdgd .ColorTransformChoice =NewEG_ColorTransformChoice ();
if _bfa :=d .DecodeElement (&_dfdgd .ColorTransformChoice .Lum ,&_dgdbb );_bfa !=nil {return _bfa ;};case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u006c\u0075\u006d\u004f\u0066\u0066"}:_dfdgd .ColorTransformChoice =NewEG_ColorTransformChoice ();
if _gbbfe :=d .DecodeElement (&_dfdgd .ColorTransformChoice .LumOff ,&_dgdbb );_gbbfe !=nil {return _gbbfe ;};case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u006c\u0075\u006d\u004d\u006f\u0064"}:_dfdgd .ColorTransformChoice =NewEG_ColorTransformChoice ();
if _fgb :=d .DecodeElement (&_dfdgd .ColorTransformChoice .LumMod ,&_dgdbb );_fgb !=nil {return _fgb ;};default:_c .Log .Debug ("\u0073\u006bi\u0070\u0070\u0069\u006e\u0067 \u0075\u006e\u0073\u0075\u0070p\u006f\u0072\u0074\u0065\u0064\u0020\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0045\u0047\u005f\u0043\u006f\u006c\u006f\u0072\u0054\u0072\u0061\u006e\u0073\u0066\u006f\u0072\u006d\u0020\u0025\u0076",_dgdbb .Name );
if _geef :=d .Skip ();_geef !=nil {return _geef ;};};case _fd .EndElement :break _gbada ;case _fd .CharData :};};return nil ;};func (_ccf *ConflictMode )MarshalXML (e *_fd .Encoder ,start _fd .StartElement )error {start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0078\u006d\u006cn\u0073"},Value :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c"});
start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0061"},Value :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065m\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006cf\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077\u0069\u006e\u0067m\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u006d\u0061\u0069\u006e"});
start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0073"},Value :"\u0068\u0074\u0074\u0070\u003a/\u002f\u0073\u0063\u0068\u0065m\u0061s\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067/\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0068\u0061\u0072e\u0064\u0054\u0079\u0070\u0065\u0073"});
start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0077"},Value :"ht\u0074\u0070:\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073.\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0077\u006f\u0072\u0064\u0070\u0072\u006f\u0063\u0065s\u0073i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u00306\u002fm\u0061\u0069n"});
start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0077\u006fr"},Value :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c"});
start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0078\u006dl"},Value :"\u0068\u0074tp\u003a\u002f\u002fw\u0077\u0077\u002e\u00773.o\u0072g/\u0058\u004d\u004c\u002f\u0031\u0039\u00398/\u006e\u0061\u006d\u0065\u0073\u0070\u0061c\u0065"});
start .Name .Local ="\u0077\u006fr\u003a\u0063\u006fn\u0066\u006c\u0069\u0063\u0074\u004d\u006f\u0064\u0065";return _ccf .CT_OnOff .MarshalXML (e ,start );};

// Validate validates the CT_SchemeColor and its children
func (_eca *CT_SchemeColor )Validate ()error {return _eca .ValidateWithPath ("\u0043\u0054\u005f\u0053\u0063\u0068\u0065\u006d\u0065C\u006f\u006c\u006f\u0072");};

// Validate validates the CT_NumSpacing and its children
func (_ggcb *CT_NumSpacing )Validate ()error {return _ggcb .ValidateWithPath ("\u0043\u0054\u005f\u004e\u0075\u006d\u0053\u0070\u0061\u0063\u0069\u006e\u0067");};func (_ffd *CT_GradientStop )UnmarshalXML (d *_fd .Decoder ,start _fd .StartElement )error {for _ ,_cbb :=range start .Attr {if _cbb .Name .Local =="\u0070\u006f\u0073"{_cedd ,_gfe :=ParseUnionST_PositiveFixedPercentage (_cbb .Value );
if _gfe !=nil {return _gfe ;};_ffd .PosAttr =_cedd ;continue ;};};_ffdb :for {_ffb ,_efa :=d .Token ();if _efa !=nil {return _efa ;};switch _bde :=_ffb .(type ){case _fd .StartElement :switch _bde .Name {case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u0073r\u0067\u0062\u0043\u006c\u0072"}:_ffd .SrgbClr =NewCT_SRgbColor ();
if _gaff :=d .DecodeElement (_ffd .SrgbClr ,&_bde );_gaff !=nil {return _gaff ;};case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u0073c\u0068\u0065\u006d\u0065\u0043\u006cr"}:_ffd .SchemeClr =NewCT_SchemeColor ();
if _ee :=d .DecodeElement (_ffd .SchemeClr ,&_bde );_ee !=nil {return _ee ;};default:_c .Log .Debug ("\u0073\u006b\u0069\u0070\u0070\u0069\u006e\u0067\u0020\u0075\u006e\u0073\u0075\u0070\u0070\u006f\u0072\u0074e\u0064\u0020\u0065\u006c\u0065\u006d\u0065n\u0074\u0020\u006f\u006e\u0020\u0043\u0054\u005f\u0047\u0072\u0061d\u0069\u0065\u006e\u0074\u0053\u0074\u006f\u0070\u0020\u0025\u0076",_bde .Name );
if _abgc :=d .Skip ();_abgc !=nil {return _abgc ;};};case _fd .EndElement :break _ffdb ;case _fd .CharData :};};return nil ;};func (_abac *CT_NumForm )MarshalXML (e *_fd .Encoder ,start _fd .StartElement )error {_ecg ,_gabb :=_abac .ValAttr .MarshalXMLAttr (_fd .Name {Local :"\u0077o\u0072\u003a\u0076\u0061\u006c"});
if _gabb !=nil {return _gabb ;};start .Attr =append (start .Attr ,_ecg );e .EncodeToken (start );e .EncodeToken (_fd .EndElement {Name :start .Name });return nil ;};func NewEG_RPrTextEffects ()*EG_RPrTextEffects {_beebg :=&EG_RPrTextEffects {};return _beebg };
func (_gdcdc ST_LightRigType )ValidateWithPath (path string )error {switch _gdcdc {case 0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27:default:return _g .Errorf ("\u0025s\u003a\u0020\u006f\u0075t\u0020\u006f\u0066\u0020\u0072a\u006eg\u0065 \u0076\u0061\u006c\u0075\u0065\u0020\u0025d",path ,int (_gdcdc ));
};return nil ;};func (_agef ST_NumSpacing )String ()string {switch _agef {case 0:return "";case 1:return "\u0064e\u0066\u0061\u0075\u006c\u0074";case 2:return "\u0070\u0072\u006fp\u006f\u0072\u0074\u0069\u006f\u006e\u0061\u006c";case 3:return "\u0074a\u0062\u0075\u006c\u0061\u0072";
};return "";};func (_gcdb *CT_StyleSet )UnmarshalXML (d *_fd .Decoder ,start _fd .StartElement )error {for _ ,_begf :=range start .Attr {if _begf .Name .Local =="\u0069\u0064"{_cfdg ,_bce :=_a .ParseUint (_begf .Value ,10,64);if _bce !=nil {return _bce ;
};_gcdb .IdAttr =_cfdg ;continue ;};if _begf .Name .Local =="\u0076\u0061\u006c"{_gcdb .ValAttr .UnmarshalXMLAttr (_begf );continue ;};};for {_cege ,_ffcg :=d .Token ();if _ffcg !=nil {return _g .Errorf ("\u0070\u0061\u0072si\u006e\u0067\u0020\u0043\u0054\u005f\u0053\u0074\u0079\u006c\u0065\u0053\u0065\u0074\u003a\u0020\u0025\u0073",_ffcg );
};if _cdace ,_aaab :=_cege .(_fd .EndElement );_aaab &&_cdace .Name ==start .Name {break ;};};return nil ;};

// Validate validates the CT_Glow and its children
func (_cga *CT_Glow )Validate ()error {return _cga .ValidateWithPath ("\u0043T\u005f\u0047\u006c\u006f\u0077");};func (_efdf *ST_NumForm )UnmarshalXMLAttr (attr _fd .Attr )error {switch attr .Value {case "":*_efdf =0;case "\u0064e\u0066\u0061\u0075\u006c\u0074":*_efdf =1;
case "\u006c\u0069\u006e\u0069\u006e\u0067":*_efdf =2;case "\u006f\u006c\u0064\u0053\u0074\u0079\u006c\u0065":*_efdf =3;};return nil ;};type CT_PresetLineDashProperties struct{ValAttr ST_PresetLineDashVal ;};func (_afbeb ST_CompoundLine )ValidateWithPath (path string )error {switch _afbeb {case 0,1,2,3,4,5:default:return _g .Errorf ("\u0025s\u003a\u0020\u006f\u0075t\u0020\u006f\u0066\u0020\u0072a\u006eg\u0065 \u0076\u0061\u006c\u0075\u0065\u0020\u0025d",path ,int (_afbeb ));
};return nil ;};func NewCustomXmlConflictInsRangeStart ()*CustomXmlConflictInsRangeStart {_fdcf :=&CustomXmlConflictInsRangeStart {};_fdcf .CT_TrackChange =*_fe .NewCT_TrackChange ();return _fdcf ;};

// ValidateWithPath validates the CT_SdtCheckboxSymbol and its children, prefixing error messages with path
func (_acdg *CT_SdtCheckboxSymbol )ValidateWithPath (path string )error {return nil };func (_beeb *CustomXmlConflictDelRangeStart )UnmarshalXML (d *_fd .Decoder ,start _fd .StartElement )error {_beeb .CT_TrackChange =*_fe .NewCT_TrackChange ();for {_dacaa ,_fbge :=d .Token ();
if _fbge !=nil {return _g .Errorf ("\u0070\u0061\u0072s\u0069\u006e\u0067\u0020\u0043\u0075\u0073\u0074\u006f\u006d\u0058\u006d\u006c\u0043\u006f\u006e\u0066\u006c\u0069\u0063\u0074\u0044\u0065\u006c\u0052\u0061\u006e\u0067\u0065S\u0074\u0061\u0072\u0074\u003a\u0020\u0025\u0073",_fbge );
};if _accef ,_dfb :=_dacaa .(_fd .EndElement );_dfb &&_accef .Name ==start .Name {break ;};};return nil ;};func ParseUnionST_OnOff (s string )(_fg .ST_OnOff ,error ){return _fg .ST_OnOff {},nil };func NewEG_ShadePropertiesChoice ()*EG_ShadePropertiesChoice {_cbg :=&EG_ShadePropertiesChoice {};
return _cbg ;};func NewCT_DefaultImageDpi ()*CT_DefaultImageDpi {_cge :=&CT_DefaultImageDpi {};return _cge };

// ValidateWithPath validates the CT_OnOff and its children, prefixing error messages with path
func (_fbgb *CT_OnOff )ValidateWithPath (path string )error {if _abb :=_fbgb .ValAttr .ValidateWithPath (path +"\u002f\u0056\u0061\u006c\u0041\u0074\u0074\u0072");_abb !=nil {return _abb ;};return nil ;};type CT_PositiveFixedPercentage struct{ValAttr _abg .ST_PositiveFixedPercentage ;
};func (_bdga *ST_LineCap )UnmarshalXML (d *_fd .Decoder ,start _fd .StartElement )error {_gdccg ,_fbga :=d .Token ();if _fbga !=nil {return _fbga ;};if _ebbg ,_dabd :=_gdccg .(_fd .EndElement );_dabd &&_ebbg .Name ==start .Name {*_bdga =1;return nil ;
};if _gagg ,_ecgc :=_gdccg .(_fd .CharData );!_ecgc {return _g .Errorf ("\u0065\u0078\u0070\u0065\u0063\u0074\u0065\u0064\u0020\u0063\u0068a\u0072\u0020\u0064\u0061\u0074\u0061\u002c\u0020\u0067\u006ft\u0020\u0025\u0054",_gdccg );}else {switch string (_gagg ){case "":*_bdga =0;
case "\u0072\u006e\u0064":*_bdga =1;case "\u0073\u0071":*_bdga =2;case "\u0066\u006c\u0061\u0074":*_bdga =3;};};_gdccg ,_fbga =d .Token ();if _fbga !=nil {return _fbga ;};if _bcbfa ,_caefd :=_gdccg .(_fd .EndElement );_caefd &&_bcbfa .Name ==start .Name {return nil ;
};return _g .Errorf ("\u0065\u0078\u0070\u0065c\u0074\u0065\u0064\u0020\u0065\u006e\u0064\u0020\u0065\u006ce\u006de\u006e\u0074\u002c\u0020\u0067\u006f\u0074 \u0025\u0076",_gdccg );};func (_dffb *CustomXmlConflictDelRangeStart )MarshalXML (e *_fd .Encoder ,start _fd .StartElement )error {start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0078\u006d\u006cn\u0073"},Value :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c"});
start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0061"},Value :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065m\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006cf\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077\u0069\u006e\u0067m\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u006d\u0061\u0069\u006e"});
start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0073"},Value :"\u0068\u0074\u0074\u0070\u003a/\u002f\u0073\u0063\u0068\u0065m\u0061s\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067/\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0068\u0061\u0072e\u0064\u0054\u0079\u0070\u0065\u0073"});
start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0077"},Value :"ht\u0074\u0070:\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073.\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0077\u006f\u0072\u0064\u0070\u0072\u006f\u0063\u0065s\u0073i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u00306\u002fm\u0061\u0069n"});
start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0077\u006fr"},Value :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c"});
start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0078\u006dl"},Value :"\u0068\u0074tp\u003a\u002f\u002fw\u0077\u0077\u002e\u00773.o\u0072g/\u0058\u004d\u004c\u002f\u0031\u0039\u00398/\u006e\u0061\u006d\u0065\u0073\u0070\u0061c\u0065"});
start .Name .Local ="\u0077\u006f\u0072:c\u0075\u0073\u0074\u006f\u006d\u0058\u006d\u006c\u0043o\u006ef\u006ci\u0063t\u0044\u0065\u006c\u0052\u0061\u006e\u0067\u0065\u0053\u0074\u0061\u0072\u0074";return _dffb .CT_TrackChange .MarshalXML (e ,start );};


// ValidateWithPath validates the CT_NumSpacing and its children, prefixing error messages with path
func (_bec *CT_NumSpacing )ValidateWithPath (path string )error {if _bec .ValAttr ==ST_NumSpacingUnset {return _g .Errorf ("\u0025\u0073\u002fV\u0061\u006c\u0041\u0074t\u0072\u0020\u0069\u0073\u0020\u0061\u0020m\u0061\u006e\u0064\u0061\u0074\u006f\u0072\u0079\u0020\u0066\u0069\u0065\u006c\u0064",path );
};if _faa :=_bec .ValAttr .ValidateWithPath (path +"\u002f\u0056\u0061\u006c\u0041\u0074\u0074\u0072");_faa !=nil {return _faa ;};return nil ;};func (_dfga ST_OnOff )MarshalXML (e *_fd .Encoder ,start _fd .StartElement )error {return e .EncodeElement (_dfga .String (),start );
};

// Validate validates the EntityPicker and its children
func (_dfae *EntityPicker )Validate ()error {return _dfae .ValidateWithPath ("\u0045\u006e\u0074i\u0074\u0079\u0050\u0069\u0063\u006b\u0065\u0072");};func (_cade ST_SchemeColorVal )String ()string {switch _cade {case 0:return "";case 1:return "\u0062\u0067\u0031";
case 2:return "\u0074\u0078\u0031";case 3:return "\u0062\u0067\u0032";case 4:return "\u0074\u0078\u0032";case 5:return "\u0061c\u0063\u0065\u006e\u0074\u0031";case 6:return "\u0061c\u0063\u0065\u006e\u0074\u0032";case 7:return "\u0061c\u0063\u0065\u006e\u0074\u0033";
case 8:return "\u0061c\u0063\u0065\u006e\u0074\u0034";case 9:return "\u0061c\u0063\u0065\u006e\u0074\u0035";case 10:return "\u0061c\u0063\u0065\u006e\u0074\u0036";case 11:return "\u0068\u006c\u0069n\u006b";case 12:return "\u0066\u006f\u006c\u0048\u006c\u0069\u006e\u006b";
case 13:return "\u0064\u006b\u0031";case 14:return "\u006c\u0074\u0031";case 15:return "\u0064\u006b\u0032";case 16:return "\u006c\u0074\u0032";case 17:return "\u0070\u0068\u0043l\u0072";};return "";};func (_aabb *CT_OnOff )UnmarshalXML (d *_fd .Decoder ,start _fd .StartElement )error {for _ ,_gcea :=range start .Attr {if _gcea .Name .Local =="\u0076\u0061\u006c"{_aabb .ValAttr .UnmarshalXMLAttr (_gcea );
continue ;};};for {_cea ,_dccg :=d .Token ();if _dccg !=nil {return _g .Errorf ("p\u0061r\u0073\u0069\u006e\u0067\u0020\u0043\u0054\u005fO\u006e\u004f\u0066\u0066: \u0025\u0073",_dccg );};if _gae ,_cbd :=_cea .(_fd .EndElement );_cbd &&_gae .Name ==start .Name {break ;
};};return nil ;};func NewDocId ()*DocId {_ebff :=&DocId {};_ebff .CT_LongHexNumber =*NewCT_LongHexNumber ();return _ebff ;};func NewCT_LinearShadeProperties ()*CT_LinearShadeProperties {_adf :=&CT_LinearShadeProperties {};return _adf ;};func (_defea ST_PresetCameraType )MarshalXML (e *_fd .Encoder ,start _fd .StartElement )error {return e .EncodeElement (_defea .String (),start );
};func (_cbae *CT_LinearShadeProperties )MarshalXML (e *_fd .Encoder ,start _fd .StartElement )error {if _cbae .AngAttr !=nil {start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0077o\u0072\u003a\u0061\u006e\u0067"},Value :_g .Sprintf ("\u0025\u0076",*_cbae .AngAttr )});
};if _cbae .ScaledAttr !=ST_OnOffUnset {_cce ,_dce :=_cbae .ScaledAttr .MarshalXMLAttr (_fd .Name {Local :"\u0077\u006f\u0072\u003a\u0073\u0063\u0061\u006c\u0065\u0064"});if _dce !=nil {return _dce ;};start .Attr =append (start .Attr ,_cce );};e .EncodeToken (start );
e .EncodeToken (_fd .EndElement {Name :start .Name });return nil ;};

// ValidateWithPath validates the EG_RPrOpenType and its children, prefixing error messages with path
func (_gafcd *EG_RPrOpenType )ValidateWithPath (path string )error {if _gafcd .Ligatures !=nil {if _bfec :=_gafcd .Ligatures .ValidateWithPath (path +"\u002f\u004c\u0069\u0067\u0061\u0074\u0075\u0072\u0065\u0073");_bfec !=nil {return _bfec ;};};if _gafcd .NumForm !=nil {if _cbaec :=_gafcd .NumForm .ValidateWithPath (path +"\u002f\u004e\u0075\u006d\u0046\u006f\u0072\u006d");
_cbaec !=nil {return _cbaec ;};};if _gafcd .NumSpacing !=nil {if _dggd :=_gafcd .NumSpacing .ValidateWithPath (path +"/\u004e\u0075\u006d\u0053\u0070\u0061\u0063\u0069\u006e\u0067");_dggd !=nil {return _dggd ;};};if _gafcd .StylisticSets !=nil {if _fcce :=_gafcd .StylisticSets .ValidateWithPath (path +"\u002f\u0053\u0074\u0079\u006c\u0069\u0073\u0074\u0069c\u0053\u0065\u0074\u0073");
_fcce !=nil {return _fcce ;};};if _gafcd .CntxtAlts !=nil {if _aaff :=_gafcd .CntxtAlts .ValidateWithPath (path +"\u002f\u0043\u006e\u0074\u0078\u0074\u0041\u006c\u0074\u0073");_aaff !=nil {return _aaff ;};};return nil ;};

// ValidateWithPath validates the CT_LongHexNumber and its children, prefixing error messages with path
func (_dfd *CT_LongHexNumber )ValidateWithPath (path string )error {return nil };

// Validate validates the CT_GradientFillProperties and its children
func (_df *CT_GradientFillProperties )Validate ()error {return _df .ValidateWithPath ("\u0043T\u005f\u0047\u0072\u0061d\u0069\u0065\u006e\u0074\u0046i\u006cl\u0050r\u006f\u0070\u0065\u0072\u0074\u0069\u0065s");};const (ST_LineCapUnset ST_LineCap =0;ST_LineCapRnd ST_LineCap =1;
ST_LineCapSq ST_LineCap =2;ST_LineCapFlat ST_LineCap =3;);func (_gcge *ST_PresetLineDashVal )UnmarshalXMLAttr (attr _fd .Attr )error {switch attr .Value {case "":*_gcge =0;case "\u0073\u006f\u006ci\u0064":*_gcge =1;case "\u0064\u006f\u0074":*_gcge =2;case "\u0073\u0079\u0073\u0044\u006f\u0074":*_gcge =3;
case "\u0064\u0061\u0073\u0068":*_gcge =4;case "\u0073y\u0073\u0044\u0061\u0073\u0068":*_gcge =5;case "\u006c\u0067\u0044\u0061\u0073\u0068":*_gcge =6;case "\u0064a\u0073\u0068\u0044\u006f\u0074":*_gcge =7;case "\u0073\u0079\u0073\u0044\u0061\u0073\u0068\u0044\u006f\u0074":*_gcge =8;
case "\u006cg\u0044\u0061\u0073\u0068\u0044\u006ft":*_gcge =9;case "\u006c\u0067\u0044a\u0073\u0068\u0044\u006f\u0074\u0044\u006f\u0074":*_gcge =10;case "\u0073\u0079\u0073\u0044\u0061\u0073\u0068\u0044\u006f\u0074\u0044\u006f\u0074":*_gcge =11;};return nil ;
};func NewCT_Ligatures ()*CT_Ligatures {_aef :=&CT_Ligatures {};_aef .ValAttr =ST_Ligatures (1);return _aef ;};func (_dafe *EG_LineJoinPropertiesChoice )MarshalXML (e *_fd .Encoder ,start _fd .StartElement )error {e .EncodeToken (start );if _dafe .Round !=nil {_bffd :=_fd .StartElement {Name :_fd .Name {Local :"\u0077o\u0072\u003a\u0072\u006f\u0075\u006ed"}};
e .EncodeElement (_dafe .Round ,_bffd );}else if _dafe .Bevel !=nil {_gbag :=_fd .StartElement {Name :_fd .Name {Local :"\u0077o\u0072\u003a\u0062\u0065\u0076\u0065l"}};e .EncodeElement (_dafe .Bevel ,_gbag );}else if _dafe .Miter !=nil {_bccd :=_fd .StartElement {Name :_fd .Name {Local :"\u0077o\u0072\u003a\u006d\u0069\u0074\u0065r"}};
e .EncodeElement (_dafe .Miter ,_bccd );};e .EncodeToken (_fd .EndElement {Name :start .Name });return nil ;};func NewCT_GradientStop ()*CT_GradientStop {_ecf :=&CT_GradientStop {};return _ecf };

// ValidateWithPath validates the CT_PathShadeProperties and its children, prefixing error messages with path
func (_adee *CT_PathShadeProperties )ValidateWithPath (path string )error {if _eae :=_adee .PathAttr .ValidateWithPath (path +"\u002fP\u0061\u0074\u0068\u0041\u0074\u0074r");_eae !=nil {return _eae ;};if _adee .FillToRect !=nil {if _eab :=_adee .FillToRect .ValidateWithPath (path +"/\u0046\u0069\u006c\u006c\u0054\u006f\u0052\u0065\u0063\u0074");
_eab !=nil {return _eab ;};};return nil ;};func (_cbec *ST_SchemeColorVal )UnmarshalXMLAttr (attr _fd .Attr )error {switch attr .Value {case "":*_cbec =0;case "\u0062\u0067\u0031":*_cbec =1;case "\u0074\u0078\u0031":*_cbec =2;case "\u0062\u0067\u0032":*_cbec =3;
case "\u0074\u0078\u0032":*_cbec =4;case "\u0061c\u0063\u0065\u006e\u0074\u0031":*_cbec =5;case "\u0061c\u0063\u0065\u006e\u0074\u0032":*_cbec =6;case "\u0061c\u0063\u0065\u006e\u0074\u0033":*_cbec =7;case "\u0061c\u0063\u0065\u006e\u0074\u0034":*_cbec =8;
case "\u0061c\u0063\u0065\u006e\u0074\u0035":*_cbec =9;case "\u0061c\u0063\u0065\u006e\u0074\u0036":*_cbec =10;case "\u0068\u006c\u0069n\u006b":*_cbec =11;case "\u0066\u006f\u006c\u0048\u006c\u0069\u006e\u006b":*_cbec =12;case "\u0064\u006b\u0031":*_cbec =13;
case "\u006c\u0074\u0031":*_cbec =14;case "\u0064\u006b\u0032":*_cbec =15;case "\u006c\u0074\u0032":*_cbec =16;case "\u0070\u0068\u0043l\u0072":*_cbec =17;};return nil ;};func ParseStdlibTime (s string )(_e .Time ,error ){return _fg .ParseStdlibTime (s )};
func (_cfca ST_PresetLineDashVal )String ()string {switch _cfca {case 0:return "";case 1:return "\u0073\u006f\u006ci\u0064";case 2:return "\u0064\u006f\u0074";case 3:return "\u0073\u0079\u0073\u0044\u006f\u0074";case 4:return "\u0064\u0061\u0073\u0068";
case 5:return "\u0073y\u0073\u0044\u0061\u0073\u0068";case 6:return "\u006c\u0067\u0044\u0061\u0073\u0068";case 7:return "\u0064a\u0073\u0068\u0044\u006f\u0074";case 8:return "\u0073\u0079\u0073\u0044\u0061\u0073\u0068\u0044\u006f\u0074";case 9:return "\u006cg\u0044\u0061\u0073\u0068\u0044\u006ft";
case 10:return "\u006c\u0067\u0044a\u0073\u0068\u0044\u006f\u0074\u0044\u006f\u0074";case 11:return "\u0073\u0079\u0073\u0044\u0061\u0073\u0068\u0044\u006f\u0074\u0044\u006f\u0074";};return "";};

// ValidateWithPath validates the CustomXmlConflictDelRangeStart and its children, prefixing error messages with path
func (_dfff *CustomXmlConflictDelRangeStart )ValidateWithPath (path string )error {if _gcc :=_dfff .CT_TrackChange .ValidateWithPath (path );_gcc !=nil {return _gcc ;};return nil ;};func (_gege ST_BevelPresetType )MarshalXML (e *_fd .Encoder ,start _fd .StartElement )error {return e .EncodeElement (_gege .String (),start );
};func (_bbfe *CT_PositivePercentage )UnmarshalXML (d *_fd .Decoder ,start _fd .StartElement )error {for _ ,_bfc :=range start .Attr {if _bfc .Name .Local =="\u0076\u0061\u006c"{_fegd ,_gbc :=ParseUnionST_PositivePercentage (_bfc .Value );if _gbc !=nil {return _gbc ;
};_bbfe .ValAttr =_fegd ;continue ;};};for {_ffbb ,_eee :=d .Token ();if _eee !=nil {return _g .Errorf ("\u0070\u0061\u0072\u0073\u0069\u006eg\u0020\u0043\u0054\u005f\u0050\u006f\u0073\u0069\u0074\u0069\u0076\u0065\u0050e\u0072\u0063\u0065\u006e\u0074\u0061\u0067e\u003a\u0020\u0025\u0073",_eee );
};if _dbgg ,_eff :=_ffbb .(_fd .EndElement );_eff &&_dbgg .Name ==start .Name {break ;};};return nil ;};type CT_SolidColorFillProperties struct{SrgbClr *CT_SRgbColor ;SchemeClr *CT_SchemeColor ;};

// ValidateWithPath validates the CT_GradientFillProperties and its children, prefixing error messages with path
func (_gab *CT_GradientFillProperties )ValidateWithPath (path string )error {if _gab .GsLst !=nil {if _ded :=_gab .GsLst .ValidateWithPath (path +"\u002f\u0047\u0073\u004c\u0073\u0074");_ded !=nil {return _ded ;};};if _aeg :=_gab .ShadePropertiesChoice .ValidateWithPath (path +"\u002f\u0053\u0068\u0061de\u0050\u0072\u006f\u0070\u0065\u0072\u0074\u0069\u0065\u0073\u0043\u0068\u006f\u0069c\u0065");
_aeg !=nil {return _aeg ;};return nil ;};func ParseUnionST_Coordinate (s string )(_abg .ST_Coordinate ,error ){return _abg .ParseUnionST_Coordinate (s );};

// ValidateWithPath validates the EG_ShadeProperties and its children, prefixing error messages with path
func (_fbdc *EG_ShadeProperties )ValidateWithPath (path string )error {if _gfdd :=_fbdc .ShadePropertiesChoice .ValidateWithPath (path +"\u002f\u0053\u0068\u0061de\u0050\u0072\u006f\u0070\u0065\u0072\u0074\u0069\u0065\u0073\u0043\u0068\u006f\u0069c\u0065");
_gfdd !=nil {return _gfdd ;};return nil ;};type CT_SRgbColor struct{ValAttr string ;EG_ColorTransform []*EG_ColorTransform ;};type ConflictMode struct{CT_OnOff };func NewCT_PositivePercentage ()*CT_PositivePercentage {_ffdcc :=&CT_PositivePercentage {};
return _ffdcc ;};type Checkbox struct{CT_SdtCheckbox };func (_dfec ST_Ligatures )ValidateWithPath (path string )error {switch _dfec {case 0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16:default:return _g .Errorf ("\u0025s\u003a\u0020\u006f\u0075t\u0020\u006f\u0066\u0020\u0072a\u006eg\u0065 \u0076\u0061\u006c\u0075\u0065\u0020\u0025d",path ,int (_dfec ));
};return nil ;};type CustomXmlConflictDelRangeEnd struct{_fe .CT_Markup };

// ValidateWithPath validates the CT_PositiveFixedPercentage and its children, prefixing error messages with path
func (_eccd *CT_PositiveFixedPercentage )ValidateWithPath (path string )error {if _ceef :=_eccd .ValAttr .ValidateWithPath (path +"\u002f\u0056\u0061\u006c\u0041\u0074\u0074\u0072");_ceef !=nil {return _ceef ;};return nil ;};

// Validate validates the CT_StylisticSets and its children
func (_dbf *CT_StylisticSets )Validate ()error {return _dbf .ValidateWithPath ("\u0043\u0054_\u0053\u0074\u0079l\u0069\u0073\u0074\u0069\u0063\u0053\u0065\u0074\u0073");};type CT_DefaultImageDpi struct{ValAttr int64 ;};func (_afaea *ST_LightRigDirection )UnmarshalXML (d *_fd .Decoder ,start _fd .StartElement )error {_bfegb ,_gcgea :=d .Token ();
if _gcgea !=nil {return _gcgea ;};if _bafg ,_eedg :=_bfegb .(_fd .EndElement );_eedg &&_bafg .Name ==start .Name {*_afaea =1;return nil ;};if _fage ,_cddd :=_bfegb .(_fd .CharData );!_cddd {return _g .Errorf ("\u0065\u0078\u0070\u0065\u0063\u0074\u0065\u0064\u0020\u0063\u0068a\u0072\u0020\u0064\u0061\u0074\u0061\u002c\u0020\u0067\u006ft\u0020\u0025\u0054",_bfegb );
}else {switch string (_fage ){case "":*_afaea =0;case "\u0074\u006c":*_afaea =1;case "\u0074":*_afaea =2;case "\u0074\u0072":*_afaea =3;case "\u006c":*_afaea =4;case "\u0072":*_afaea =5;case "\u0062\u006c":*_afaea =6;case "\u0062":*_afaea =7;case "\u0062\u0072":*_afaea =8;
};};_bfegb ,_gcgea =d .Token ();if _gcgea !=nil {return _gcgea ;};if _agcc ,_aadcf :=_bfegb .(_fd .EndElement );_aadcf &&_agcc .Name ==start .Name {return nil ;};return _g .Errorf ("\u0065\u0078\u0070\u0065c\u0074\u0065\u0064\u0020\u0065\u006e\u0064\u0020\u0065\u006ce\u006de\u006e\u0074\u002c\u0020\u0067\u006f\u0074 \u0025\u0076",_bfegb );
};func (_dafb *EG_RunLevelConflicts )MarshalXML (e *_fd .Encoder ,start _fd .StartElement )error {start .Name .Local ="\u0077o\u0072\u003a\u0045\u0047\u005f\u0052\u0075\u006e\u004c\u0065\u0076e\u006c\u0043\u006f\u006e\u0066\u006c\u0069\u0063\u0074\u0073";
if _dafb .ConflictIns !=nil {_aecc :=_fd .StartElement {Name :_fd .Name {Local :"\u0077o\u0072:\u0063\u006f\u006e\u0066\u006c\u0069\u0063\u0074\u0049\u006e\u0073"}};e .EncodeElement (_dafb .ConflictIns ,_aecc );};if _dafb .ConflictDel !=nil {_aead :=_fd .StartElement {Name :_fd .Name {Local :"\u0077o\u0072:\u0063\u006f\u006e\u0066\u006c\u0069\u0063\u0074\u0044\u0065\u006c"}};
e .EncodeElement (_dafb .ConflictDel ,_aead );};return nil ;};func NewCT_Props3D ()*CT_Props3D {_edge :=&CT_Props3D {};return _edge };func (_gcec *CustomXmlConflictInsRangeStart )MarshalXML (e *_fd .Encoder ,start _fd .StartElement )error {start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0078\u006d\u006cn\u0073"},Value :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c"});
start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0061"},Value :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065m\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006cf\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077\u0069\u006e\u0067m\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u006d\u0061\u0069\u006e"});
start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0073"},Value :"\u0068\u0074\u0074\u0070\u003a/\u002f\u0073\u0063\u0068\u0065m\u0061s\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067/\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0068\u0061\u0072e\u0064\u0054\u0079\u0070\u0065\u0073"});
start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0077"},Value :"ht\u0074\u0070:\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073.\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0077\u006f\u0072\u0064\u0070\u0072\u006f\u0063\u0065s\u0073i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u00306\u002fm\u0061\u0069n"});
start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0077\u006fr"},Value :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c"});
start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0078\u006dl"},Value :"\u0068\u0074tp\u003a\u002f\u002fw\u0077\u0077\u002e\u00773.o\u0072g/\u0058\u004d\u004c\u002f\u0031\u0039\u00398/\u006e\u0061\u006d\u0065\u0073\u0070\u0061c\u0065"});
start .Name .Local ="\u0077\u006f\u0072:c\u0075\u0073\u0074\u006f\u006d\u0058\u006d\u006c\u0043o\u006ef\u006ci\u0063t\u0049\u006e\u0073\u0052\u0061\u006e\u0067\u0065\u0053\u0074\u0061\u0072\u0074";return _gcec .CT_TrackChange .MarshalXML (e ,start );};
func (_gdaaa ST_PenAlignment )MarshalXML (e *_fd .Encoder ,start _fd .StartElement )error {return e .EncodeElement (_gdaaa .String (),start );};func (_ebaa *EG_RPrOpenType )MarshalXML (e *_fd .Encoder ,start _fd .StartElement )error {start .Name .Local ="\u0077o\u0072:\u0045\u0047\u005f\u0052\u0050r\u004f\u0070e\u006e\u0054\u0079\u0070\u0065";
if _ebaa .Ligatures !=nil {_eaba :=_fd .StartElement {Name :_fd .Name {Local :"\u0077\u006f\u0072\u003a\u006c\u0069\u0067\u0061\u0074\u0075\u0072\u0065\u0073"}};e .EncodeElement (_ebaa .Ligatures ,_eaba );};if _ebaa .NumForm !=nil {_fdcab :=_fd .StartElement {Name :_fd .Name {Local :"w\u006f\u0072\u003a\u006e\u0075\u006d\u0046\u006f\u0072\u006d"}};
e .EncodeElement (_ebaa .NumForm ,_fdcab );};if _ebaa .NumSpacing !=nil {_cafb :=_fd .StartElement {Name :_fd .Name {Local :"\u0077\u006f\u0072\u003a\u006e\u0075\u006d\u0053\u0070a\u0063\u0069\u006e\u0067"}};e .EncodeElement (_ebaa .NumSpacing ,_cafb );
};if _ebaa .StylisticSets !=nil {_dbab :=_fd .StartElement {Name :_fd .Name {Local :"\u0077\u006f\u0072\u003a\u0073\u0074\u0079\u006c\u0069\u0073\u0074\u0069c\u0053\u0065\u0074\u0073"}};e .EncodeElement (_ebaa .StylisticSets ,_dbab );};if _ebaa .CntxtAlts !=nil {_cbac :=_fd .StartElement {Name :_fd .Name {Local :"\u0077\u006f\u0072\u003a\u0063\u006e\u0074\u0078\u0074\u0041\u006c\u0074\u0073"}};
e .EncodeElement (_ebaa .CntxtAlts ,_cbac );};return nil ;};func (_cbba *EG_ConflictsChoice )MarshalXML (e *_fd .Encoder ,start _fd .StartElement )error {e .EncodeToken (start );if _cbba .ConflictIns !=nil {_cgfed :=_fd .StartElement {Name :_fd .Name {Local :"\u0077o\u0072:\u0063\u006f\u006e\u0066\u006c\u0069\u0063\u0074\u0049\u006e\u0073"}};
e .EncodeElement (_cbba .ConflictIns ,_cgfed );}else if _cbba .ConflictDel !=nil {_degb :=_fd .StartElement {Name :_fd .Name {Local :"\u0077o\u0072:\u0063\u006f\u006e\u0066\u006c\u0069\u0063\u0074\u0044\u0065\u006c"}};e .EncodeElement (_cbba .ConflictDel ,_degb );
};e .EncodeToken (_fd .EndElement {Name :start .Name });return nil ;};func (_egc *CT_LineJoinMiterProperties )UnmarshalXML (d *_fd .Decoder ,start _fd .StartElement )error {for _ ,_efc :=range start .Attr {if _efc .Name .Local =="\u006c\u0069\u006d"{_eeb ,_aaa :=ParseUnionST_PositivePercentage (_efc .Value );
if _aaa !=nil {return _aaa ;};_egc .LimAttr =&_eeb ;continue ;};};for {_acce ,_afa :=d .Token ();if _afa !=nil {return _g .Errorf ("\u0070\u0061\u0072\u0073\u0069\u006e\u0067\u0020\u0043\u0054\u005f\u004c\u0069\u006e\u0065\u004a\u006f\u0069\u006e\u004d\u0069\u0074\u0065\u0072P\u0072\u006f\u0070\u0065\u0072t\u0069\u0065s\u003a\u0020\u0025\u0073",_afa );
};if _beaa ,_ggeb :=_acce .(_fd .EndElement );_ggeb &&_beaa .Name ==start .Name {break ;};};return nil ;};type EG_FillProperties struct{FillPropertiesChoice *EG_FillPropertiesChoice ;};func (_gggcb ST_LightRigType )String ()string {switch _gggcb {case 0:return "";
case 1:return "l\u0065\u0067\u0061\u0063\u0079\u0046\u006c\u0061\u0074\u0031";case 2:return "l\u0065\u0067\u0061\u0063\u0079\u0046\u006c\u0061\u0074\u0032";case 3:return "l\u0065\u0067\u0061\u0063\u0079\u0046\u006c\u0061\u0074\u0033";case 4:return "l\u0065\u0067\u0061\u0063\u0079\u0046\u006c\u0061\u0074\u0034";
case 5:return "\u006c\u0065\u0067\u0061\u0063\u0079\u004e\u006f\u0072\u006d\u0061\u006c\u0031";case 6:return "\u006c\u0065\u0067\u0061\u0063\u0079\u004e\u006f\u0072\u006d\u0061\u006c\u0032";case 7:return "\u006c\u0065\u0067\u0061\u0063\u0079\u004e\u006f\u0072\u006d\u0061\u006c\u0033";
case 8:return "\u006c\u0065\u0067\u0061\u0063\u0079\u004e\u006f\u0072\u006d\u0061\u006c\u0034";case 9:return "\u006c\u0065\u0067a\u0063\u0079\u0048\u0061\u0072\u0073\u0068\u0031";case 10:return "\u006c\u0065\u0067a\u0063\u0079\u0048\u0061\u0072\u0073\u0068\u0032";
case 11:return "\u006c\u0065\u0067a\u0063\u0079\u0048\u0061\u0072\u0073\u0068\u0033";case 12:return "\u006c\u0065\u0067a\u0063\u0079\u0048\u0061\u0072\u0073\u0068\u0034";case 13:return "\u0074h\u0072\u0065\u0065\u0050\u0074";case 14:return "\u0062\u0061\u006c\u0061\u006e\u0063\u0065\u0064";
case 15:return "\u0073\u006f\u0066\u0074";case 16:return "\u0068\u0061\u0072s\u0068";case 17:return "\u0066\u006c\u006fo\u0064";case 18:return "c\u006f\u006e\u0074\u0072\u0061\u0073\u0074\u0069\u006e\u0067";case 19:return "\u006do\u0072\u006e\u0069\u006e\u0067";
case 20:return "\u0073u\u006e\u0072\u0069\u0073\u0065";case 21:return "\u0073\u0075\u006e\u0073\u0065\u0074";case 22:return "\u0063\u0068\u0069\u006c\u006c\u0079";case 23:return "\u0066\u0072\u0065\u0065\u007a\u0069\u006e\u0067";case 24:return "\u0066\u006c\u0061\u0074";
case 25:return "\u0074\u0077\u006fP\u0074";case 26:return "\u0067\u006c\u006f\u0077";case 27:return "\u0062\u0072\u0069\u0067\u0068\u0074\u0052\u006f\u006f\u006d";};return "";};func (_bcfc *EntityPicker )UnmarshalXML (d *_fd .Decoder ,start _fd .StartElement )error {_bcfc .CT_Empty =*_fe .NewCT_Empty ();
for {_fbcd ,_fbaa :=d .Token ();if _fbaa !=nil {return _g .Errorf ("\u0070a\u0072\u0073\u0069\u006e\u0067\u0020\u0045\u006e\u0074\u0069\u0074y\u0050\u0069\u0063\u006b\u0065\u0072\u003a\u0020\u0025\u0073",_fbaa );};if _bgeg ,_aagb :=_fbcd .(_fd .EndElement );
_aagb &&_bgeg .Name ==start .Name {break ;};};return nil ;};

// Validate validates the CT_LongHexNumber and its children
func (_acfb *CT_LongHexNumber )Validate ()error {return _acfb .ValidateWithPath ("\u0043\u0054_\u004c\u006f\u006eg\u0048\u0065\u0078\u004e\u0075\u006d\u0062\u0065\u0072");};func NewCT_Shadow ()*CT_Shadow {_eeca :=&CT_Shadow {};return _eeca };func (_bcfe *CT_LightRig )MarshalXML (e *_fd .Encoder ,start _fd .StartElement )error {_ffdc ,_bbcd :=_bcfe .RigAttr .MarshalXMLAttr (_fd .Name {Local :"\u0077o\u0072\u003a\u0072\u0069\u0067"});
if _bbcd !=nil {return _bbcd ;};start .Attr =append (start .Attr ,_ffdc );_ffdc ,_bbcd =_bcfe .DirAttr .MarshalXMLAttr (_fd .Name {Local :"\u0077o\u0072\u003a\u0064\u0069\u0072"});if _bbcd !=nil {return _bbcd ;};start .Attr =append (start .Attr ,_ffdc );
e .EncodeToken (start );if _bcfe .Rot !=nil {_bgf :=_fd .StartElement {Name :_fd .Name {Local :"\u0077o\u0072\u003a\u0072\u006f\u0074"}};e .EncodeElement (_bcfe .Rot ,_bgf );};e .EncodeToken (_fd .EndElement {Name :start .Name });return nil ;};type ST_RectAlignment byte ;
func (_gf *CT_Color )UnmarshalXML (d *_fd .Decoder ,start _fd .StartElement )error {_ddg :for {_gb ,_dbb :=d .Token ();if _dbb !=nil {return _dbb ;};switch _fega :=_gb .(type ){case _fd .StartElement :switch _fega .Name {case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u0073r\u0067\u0062\u0043\u006c\u0072"}:_gf .SrgbClr =NewCT_SRgbColor ();
if _abd :=d .DecodeElement (_gf .SrgbClr ,&_fega );_abd !=nil {return _abd ;};case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u0073c\u0068\u0065\u006d\u0065\u0043\u006cr"}:_gf .SchemeClr =NewCT_SchemeColor ();
if _cd :=d .DecodeElement (_gf .SchemeClr ,&_fega );_cd !=nil {return _cd ;};default:_c .Log .Debug ("\u0073\u006b\u0069\u0070\u0070\u0069\u006eg\u0020\u0075\u006es\u0075\u0070\u0070\u006fr\u0074\u0065\u0064\u0020\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0043\u0054\u005f\u0043\u006f\u006c\u006f\u0072\u0020\u0025\u0076",_fega .Name );
if _beg :=d .Skip ();_beg !=nil {return _beg ;};};case _fd .EndElement :break _ddg ;case _fd .CharData :};};return nil ;};func (_cedef ST_CompoundLine )String ()string {switch _cedef {case 0:return "";case 1:return "\u0073\u006e\u0067";case 2:return "\u0064\u0062\u006c";
case 3:return "\u0074h\u0069\u0063\u006b\u0054\u0068\u0069n";case 4:return "\u0074h\u0069\u006e\u0054\u0068\u0069\u0063k";case 5:return "\u0074\u0072\u0069";};return "";};func (_fgca *CustomXmlConflictDelRangeEnd )MarshalXML (e *_fd .Encoder ,start _fd .StartElement )error {start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0078\u006d\u006cn\u0073"},Value :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c"});
start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0061"},Value :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065m\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006cf\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077\u0069\u006e\u0067m\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u006d\u0061\u0069\u006e"});
start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0073"},Value :"\u0068\u0074\u0074\u0070\u003a/\u002f\u0073\u0063\u0068\u0065m\u0061s\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067/\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0068\u0061\u0072e\u0064\u0054\u0079\u0070\u0065\u0073"});
start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0077"},Value :"ht\u0074\u0070:\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073.\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0077\u006f\u0072\u0064\u0070\u0072\u006f\u0063\u0065s\u0073i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u00306\u002fm\u0061\u0069n"});
start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0077\u006fr"},Value :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c"});
start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0078\u006dl"},Value :"\u0068\u0074tp\u003a\u002f\u002fw\u0077\u0077\u002e\u00773.o\u0072g/\u0058\u004d\u004c\u002f\u0031\u0039\u00398/\u006e\u0061\u006d\u0065\u0073\u0070\u0061c\u0065"});
start .Name .Local ="\u0077\u006f\u0072\u003a\u0063\u0075\u0073\u0074\u006f\u006d\u0058\u006d\u006c\u0043\u006fn\u0066l\u0069\u0063\u0074\u0044\u0065\u006c\u0052\u0061\u006e\u0067\u0065\u0045\u006e\u0064";return _fgca .CT_Markup .MarshalXML (e ,start );
};func (_bfad *ST_LightRigType )UnmarshalXMLAttr (attr _fd .Attr )error {switch attr .Value {case "":*_bfad =0;case "l\u0065\u0067\u0061\u0063\u0079\u0046\u006c\u0061\u0074\u0031":*_bfad =1;case "l\u0065\u0067\u0061\u0063\u0079\u0046\u006c\u0061\u0074\u0032":*_bfad =2;
case "l\u0065\u0067\u0061\u0063\u0079\u0046\u006c\u0061\u0074\u0033":*_bfad =3;case "l\u0065\u0067\u0061\u0063\u0079\u0046\u006c\u0061\u0074\u0034":*_bfad =4;case "\u006c\u0065\u0067\u0061\u0063\u0079\u004e\u006f\u0072\u006d\u0061\u006c\u0031":*_bfad =5;
case "\u006c\u0065\u0067\u0061\u0063\u0079\u004e\u006f\u0072\u006d\u0061\u006c\u0032":*_bfad =6;case "\u006c\u0065\u0067\u0061\u0063\u0079\u004e\u006f\u0072\u006d\u0061\u006c\u0033":*_bfad =7;case "\u006c\u0065\u0067\u0061\u0063\u0079\u004e\u006f\u0072\u006d\u0061\u006c\u0034":*_bfad =8;
case "\u006c\u0065\u0067a\u0063\u0079\u0048\u0061\u0072\u0073\u0068\u0031":*_bfad =9;case "\u006c\u0065\u0067a\u0063\u0079\u0048\u0061\u0072\u0073\u0068\u0032":*_bfad =10;case "\u006c\u0065\u0067a\u0063\u0079\u0048\u0061\u0072\u0073\u0068\u0033":*_bfad =11;
case "\u006c\u0065\u0067a\u0063\u0079\u0048\u0061\u0072\u0073\u0068\u0034":*_bfad =12;case "\u0074h\u0072\u0065\u0065\u0050\u0074":*_bfad =13;case "\u0062\u0061\u006c\u0061\u006e\u0063\u0065\u0064":*_bfad =14;case "\u0073\u006f\u0066\u0074":*_bfad =15;
case "\u0068\u0061\u0072s\u0068":*_bfad =16;case "\u0066\u006c\u006fo\u0064":*_bfad =17;case "c\u006f\u006e\u0074\u0072\u0061\u0073\u0074\u0069\u006e\u0067":*_bfad =18;case "\u006do\u0072\u006e\u0069\u006e\u0067":*_bfad =19;case "\u0073u\u006e\u0072\u0069\u0073\u0065":*_bfad =20;
case "\u0073\u0075\u006e\u0073\u0065\u0074":*_bfad =21;case "\u0063\u0068\u0069\u006c\u006c\u0079":*_bfad =22;case "\u0066\u0072\u0065\u0065\u007a\u0069\u006e\u0067":*_bfad =23;case "\u0066\u006c\u0061\u0074":*_bfad =24;case "\u0074\u0077\u006fP\u0074":*_bfad =25;
case "\u0067\u006c\u006f\u0077":*_bfad =26;case "\u0062\u0072\u0069\u0067\u0068\u0074\u0052\u006f\u006f\u006d":*_bfad =27;};return nil ;};func (_cfab ST_LineCap )MarshalXML (e *_fd .Encoder ,start _fd .StartElement )error {return e .EncodeElement (_cfab .String (),start );
};

// Validate validates the CT_Props3D and its children
func (_fbbe *CT_Props3D )Validate ()error {return _fbbe .ValidateWithPath ("\u0043\u0054\u005f\u0050\u0072\u006f\u0070\u0073\u0033\u0044");};func (_afac ST_PresetLineDashVal )MarshalXMLAttr (name _fd .Name )(_fd .Attr ,error ){_gdbg :=_fd .Attr {};_gdbg .Name =name ;
switch _afac {case ST_PresetLineDashValUnset :_gdbg .Value ="";case ST_PresetLineDashValSolid :_gdbg .Value ="\u0073\u006f\u006ci\u0064";case ST_PresetLineDashValDot :_gdbg .Value ="\u0064\u006f\u0074";case ST_PresetLineDashValSysDot :_gdbg .Value ="\u0073\u0079\u0073\u0044\u006f\u0074";
case ST_PresetLineDashValDash :_gdbg .Value ="\u0064\u0061\u0073\u0068";case ST_PresetLineDashValSysDash :_gdbg .Value ="\u0073y\u0073\u0044\u0061\u0073\u0068";case ST_PresetLineDashValLgDash :_gdbg .Value ="\u006c\u0067\u0044\u0061\u0073\u0068";case ST_PresetLineDashValDashDot :_gdbg .Value ="\u0064a\u0073\u0068\u0044\u006f\u0074";
case ST_PresetLineDashValSysDashDot :_gdbg .Value ="\u0073\u0079\u0073\u0044\u0061\u0073\u0068\u0044\u006f\u0074";case ST_PresetLineDashValLgDashDot :_gdbg .Value ="\u006cg\u0044\u0061\u0073\u0068\u0044\u006ft";case ST_PresetLineDashValLgDashDotDot :_gdbg .Value ="\u006c\u0067\u0044a\u0073\u0068\u0044\u006f\u0074\u0044\u006f\u0074";
case ST_PresetLineDashValSysDashDotDot :_gdbg .Value ="\u0073\u0079\u0073\u0044\u0061\u0073\u0068\u0044\u006f\u0074\u0044\u006f\u0074";};return _gdbg ,nil ;};func _cede (_ecfd bool )uint8 {if _ecfd {return 1;};return 0;};func ParseUnionST_AnimationDgmBuildType (s string )(_abg .ST_AnimationDgmBuildType ,error ){return _abg .ParseUnionST_AnimationDgmBuildType (s );
};

// ValidateWithPath validates the CT_Shadow and its children, prefixing error messages with path
func (_gabff *CT_Shadow )ValidateWithPath (path string )error {if _gabff .BlurRadAttr !=nil {if *_gabff .BlurRadAttr < 0{return _g .Errorf ("\u0025\u0073\u002f\u006d\u002e\u0042\u006c\u0075\u0072\u0052\u0061\u0064\u0041t\u0074\u0072\u0020\u006d\u0075\u0073t\u0020\u0062\u0065\u0020\u003e\u003d\u0020\u0030\u0020\u0028\u0068\u0061\u0076e\u0020\u0025\u0076\u0029",path ,*_gabff .BlurRadAttr );
};if *_gabff .BlurRadAttr > 27273042316900{return _g .Errorf ("\u0025\u0073/\u006d\u002e\u0042\u006c\u0075r\u0052\u0061\u0064\u0041\u0074t\u0072\u0020\u006d\u0075\u0073\u0074\u0020\u0062\u0065\u0020\u003c\u003d\u0020\u0032\u0037\u0032\u0037\u0033\u0030\u0034\u0032\u0033\u0031\u0036\u0039\u0030\u0030\u0020\u0028\u0068\u0061\u0076\u0065\u0020\u0025\u0076\u0029",path ,*_gabff .BlurRadAttr );
};};if _gabff .DistAttr !=nil {if *_gabff .DistAttr < 0{return _g .Errorf ("\u0025\u0073/m\u002e\u0044\u0069s\u0074\u0041\u0074\u0074r m\u0075st\u0020\u0062\u0065\u0020\u003e\u003d\u00200 \u0028\u0068\u0061\u0076\u0065\u0020\u0025v\u0029",path ,*_gabff .DistAttr );
};if *_gabff .DistAttr > 27273042316900{return _g .Errorf ("\u0025\u0073\u002f\u006d\u002e\u0044i\u0073\u0074\u0041\u0074\u0074\u0072\u0020\u006d\u0075\u0073\u0074\u0020\u0062\u0065\u0020\u003c\u003d\u0020\u0032\u00372\u0037\u0033\u0030\u0034\u0032\u0033\u0031\u0036\u0039\u0030\u0030\u0020\u0028\u0068a\u0076e\u0020\u0025\u0076\u0029",path ,*_gabff .DistAttr );
};};if _gabff .DirAttr !=nil {if *_gabff .DirAttr < 0{return _g .Errorf ("%\u0073\u002f\u006d\u002e\u0044\u0069r\u0041\u0074\u0074\u0072\u0020\u006du\u0073\u0074\u0020\u0062\u0065\u0020\u003e=\u0020\u0030\u0020\u0028\u0068\u0061\u0076\u0065\u0020\u0025v\u0029",path ,*_gabff .DirAttr );
};if *_gabff .DirAttr >=21600000{return _g .Errorf ("\u0025\u0073\u002f\u006d\u002eD\u0069\u0072\u0041\u0074\u0074\u0072\u0020\u006d\u0075\u0073\u0074\u0020\u0062e\u0020\u003c\u0020\u0032\u0031\u0036\u0030\u0030\u0030\u0030\u0030\u0020\u0028\u0068\u0061\u0076\u0065\u0020\u0025\u0076\u0029",path ,*_gabff .DirAttr );
};};if _gabff .SxAttr !=nil {if _bagb :=_gabff .SxAttr .ValidateWithPath (path +"\u002fS\u0078\u0041\u0074\u0074\u0072");_bagb !=nil {return _bagb ;};};if _gabff .SyAttr !=nil {if _fbfbeb :=_gabff .SyAttr .ValidateWithPath (path +"\u002fS\u0079\u0041\u0074\u0074\u0072");
_fbfbeb !=nil {return _fbfbeb ;};};if _gabff .KxAttr !=nil {if *_gabff .KxAttr <=-5400000{return _g .Errorf ("%\u0073\u002f\u006d\u002e\u004b\u0078\u0041\u0074\u0074\u0072\u0020\u006d\u0075\u0073\u0074\u0020\u0062\u0065 \u003e\u0020\u002d\u0035\u0034\u0030\u0030\u0030\u0030\u0030 (\u0068\u0061\u0076e\u0020%\u0076\u0029",path ,*_gabff .KxAttr );
};if *_gabff .KxAttr >=5400000{return _g .Errorf ("\u0025\u0073\u002f\u006d\u002e\u004b\u0078\u0041\u0074\u0074\u0072\u0020\u006du\u0073\u0074\u0020\u0062\u0065\u0020<\u0020\u0035\u0034\u0030\u0030\u0030\u0030\u0030\u0020\u0028\u0068\u0061\u0076e\u0020\u0025\u0076\u0029",path ,*_gabff .KxAttr );
};};if _gabff .KyAttr !=nil {if *_gabff .KyAttr <=-5400000{return _g .Errorf ("%\u0073\u002f\u006d\u002e\u004b\u0079\u0041\u0074\u0074\u0072\u0020\u006d\u0075\u0073\u0074\u0020\u0062\u0065 \u003e\u0020\u002d\u0035\u0034\u0030\u0030\u0030\u0030\u0030 (\u0068\u0061\u0076e\u0020%\u0076\u0029",path ,*_gabff .KyAttr );
};if *_gabff .KyAttr >=5400000{return _g .Errorf ("\u0025\u0073\u002f\u006d\u002e\u004b\u0079\u0041\u0074\u0074\u0072\u0020\u006du\u0073\u0074\u0020\u0062\u0065\u0020<\u0020\u0035\u0034\u0030\u0030\u0030\u0030\u0030\u0020\u0028\u0068\u0061\u0076e\u0020\u0025\u0076\u0029",path ,*_gabff .KyAttr );
};};if _gbfg :=_gabff .AlgnAttr .ValidateWithPath (path +"\u002fA\u006c\u0067\u006e\u0041\u0074\u0074r");_gbfg !=nil {return _gbfg ;};if _gabff .SrgbClr !=nil {if _ebfa :=_gabff .SrgbClr .ValidateWithPath (path +"\u002f\u0053\u0072\u0067\u0062\u0043\u006c\u0072");
_ebfa !=nil {return _ebfa ;};};if _gabff .SchemeClr !=nil {if _fad :=_gabff .SchemeClr .ValidateWithPath (path +"\u002f\u0053\u0063\u0068\u0065\u006d\u0065\u0043\u006c\u0072");_fad !=nil {return _fad ;};};return nil ;};

// ValidateWithPath validates the CT_TextOutlineEffect and its children, prefixing error messages with path
func (_gaea *CT_TextOutlineEffect )ValidateWithPath (path string )error {if _gaea .WAttr !=nil {if *_gaea .WAttr < 0{return _g .Errorf ("\u0025\u0073\u002f\u006d\u002e\u0057A\u0074\u0074\u0072\u0020\u006d\u0075\u0073\u0074\u0020\u0062\u0065\u0020\u003e=\u0020\u0030\u0020\u0028\u0068\u0061\u0076e\u0020\u0025\u0076\u0029",path ,*_gaea .WAttr );
};if *_gaea .WAttr > 20116800{return _g .Errorf ("%\u0073\u002f\u006d\u002e\u0057\u0041\u0074\u0074\u0072\u0020\u006d\u0075\u0073\u0074\u0020\u0062\u0065\u0020<\u003d\u0020\u0032\u0030\u0031\u0031\u0036\u0038\u0030\u0030 (\u0068\u0061\u0076e\u0020%\u0076\u0029",path ,*_gaea .WAttr );
};};if _abdaf :=_gaea .CapAttr .ValidateWithPath (path +"\u002f\u0043\u0061\u0070\u0041\u0074\u0074\u0072");_abdaf !=nil {return _abdaf ;};if _cbbd :=_gaea .CmpdAttr .ValidateWithPath (path +"\u002fC\u006d\u0070\u0064\u0041\u0074\u0074r");_cbbd !=nil {return _cbbd ;
};if _caa :=_gaea .AlgnAttr .ValidateWithPath (path +"\u002fA\u006c\u0067\u006e\u0041\u0074\u0074r");_caa !=nil {return _caa ;};if _bbdg :=_gaea .FillPropertiesChoice .ValidateWithPath (path +"/\u0046\u0069\u006c\u006cPr\u006fp\u0065\u0072\u0074\u0069\u0065s\u0043\u0068\u006f\u0069\u0063\u0065");
_bbdg !=nil {return _bbdg ;};if _gaea .PrstDash !=nil {if _cacb :=_gaea .PrstDash .ValidateWithPath (path +"\u002fP\u0072\u0073\u0074\u0044\u0061\u0073h");_cacb !=nil {return _cacb ;};};if _dbff :=_gaea .LineJoinPropertiesChoice .ValidateWithPath (path +"\u002fL\u0069\u006e\u0065\u004ao\u0069\u006e\u0050\u0072\u006fp\u0065r\u0074i\u0065\u0073\u0043\u0068\u006f\u0069\u0063e");
_dbff !=nil {return _dbff ;};return nil ;};const (ST_RectAlignmentUnset ST_RectAlignment =0;ST_RectAlignmentNone ST_RectAlignment =1;ST_RectAlignmentTl ST_RectAlignment =2;ST_RectAlignmentT ST_RectAlignment =3;ST_RectAlignmentTr ST_RectAlignment =4;ST_RectAlignmentL ST_RectAlignment =5;
ST_RectAlignmentCtr ST_RectAlignment =6;ST_RectAlignmentR ST_RectAlignment =7;ST_RectAlignmentBl ST_RectAlignment =8;ST_RectAlignmentB ST_RectAlignment =9;ST_RectAlignmentBr ST_RectAlignment =10;);

// Validate validates the EG_Conflicts and its children
func (_gbbcb *EG_Conflicts )Validate ()error {return _gbbcb .ValidateWithPath ("\u0045\u0047\u005fC\u006f\u006e\u0066\u006c\u0069\u0063\u0074\u0073");};func NewCT_StyleSet ()*CT_StyleSet {_cgaa :=&CT_StyleSet {};return _cgaa };func (_agde ST_CompoundLine )Validate ()error {return _agde .ValidateWithPath ("")};
func NewCT_PositiveFixedPercentage ()*CT_PositiveFixedPercentage {_dbdb :=&CT_PositiveFixedPercentage {};return _dbdb ;};func (_accdg ST_PresetCameraType )ValidateWithPath (path string )error {switch _accdg {case 0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62:default:return _g .Errorf ("\u0025s\u003a\u0020\u006f\u0075t\u0020\u006f\u0066\u0020\u0072a\u006eg\u0065 \u0076\u0061\u006c\u0075\u0065\u0020\u0025d",path ,int (_accdg ));
};return nil ;};const (ST_NumSpacingUnset ST_NumSpacing =0;ST_NumSpacingDefault ST_NumSpacing =1;ST_NumSpacingProportional ST_NumSpacing =2;ST_NumSpacingTabular ST_NumSpacing =3;);func (_fbaf ST_PenAlignment )ValidateWithPath (path string )error {switch _fbaf {case 0,1,2:default:return _g .Errorf ("\u0025s\u003a\u0020\u006f\u0075t\u0020\u006f\u0066\u0020\u0072a\u006eg\u0065 \u0076\u0061\u006c\u0075\u0065\u0020\u0025d",path ,int (_fbaf ));
};return nil ;};const (ST_PresetLineDashValUnset ST_PresetLineDashVal =0;ST_PresetLineDashValSolid ST_PresetLineDashVal =1;ST_PresetLineDashValDot ST_PresetLineDashVal =2;ST_PresetLineDashValSysDot ST_PresetLineDashVal =3;ST_PresetLineDashValDash ST_PresetLineDashVal =4;
ST_PresetLineDashValSysDash ST_PresetLineDashVal =5;ST_PresetLineDashValLgDash ST_PresetLineDashVal =6;ST_PresetLineDashValDashDot ST_PresetLineDashVal =7;ST_PresetLineDashValSysDashDot ST_PresetLineDashVal =8;ST_PresetLineDashValLgDashDot ST_PresetLineDashVal =9;
ST_PresetLineDashValLgDashDotDot ST_PresetLineDashVal =10;ST_PresetLineDashValSysDashDotDot ST_PresetLineDashVal =11;);func (_efabc ST_PresetLineDashVal )MarshalXML (e *_fd .Encoder ,start _fd .StartElement )error {return e .EncodeElement (_efabc .String (),start );
};func NewCT_Bevel ()*CT_Bevel {_bb :=&CT_Bevel {};return _bb };

// Validate validates the CT_LinearShadeProperties and its children
func (_fgce *CT_LinearShadeProperties )Validate ()error {return _fgce .ValidateWithPath ("\u0043T\u005f\u004c\u0069\u006e\u0065\u0061\u0072\u0053\u0068\u0061\u0064e\u0050\u0072\u006f\u0070\u0065\u0072\u0074\u0069\u0065\u0073");};type CT_StylisticSets struct{StyleSet []*CT_StyleSet ;
};type ST_PresetLineDashVal byte ;func (_eaf *CT_LongHexNumber )UnmarshalXML (d *_fd .Decoder ,start _fd .StartElement )error {for _ ,_fac :=range start .Attr {if _fac .Name .Local =="\u0076\u0061\u006c"{_baf :=_fac .Value ;_eaf .ValAttr =_baf ;continue ;
};};for {_fee ,_egeg :=d .Token ();if _egeg !=nil {return _g .Errorf ("\u0070\u0061\u0072\u0073i\u006e\u0067\u0020\u0043\u0054\u005f\u004c\u006f\u006e\u0067H\u0065x\u004e\u0075\u006d\u0062\u0065\u0072\u003a \u0025\u0073",_egeg );};if _dfc ,_dced :=_fee .(_fd .EndElement );
_dced &&_dfc .Name ==start .Name {break ;};};return nil ;};

// ValidateWithPath validates the CT_LightRig and its children, prefixing error messages with path
func (_cee *CT_LightRig )ValidateWithPath (path string )error {if _cee .RigAttr ==ST_LightRigTypeUnset {return _g .Errorf ("\u0025\u0073\u002fR\u0069\u0067\u0041\u0074t\u0072\u0020\u0069\u0073\u0020\u0061\u0020m\u0061\u006e\u0064\u0061\u0074\u006f\u0072\u0079\u0020\u0066\u0069\u0065\u006c\u0064",path );
};if _bfe :=_cee .RigAttr .ValidateWithPath (path +"\u002f\u0052\u0069\u0067\u0041\u0074\u0074\u0072");_bfe !=nil {return _bfe ;};if _cee .DirAttr ==ST_LightRigDirectionUnset {return _g .Errorf ("\u0025\u0073\u002fD\u0069\u0072\u0041\u0074t\u0072\u0020\u0069\u0073\u0020\u0061\u0020m\u0061\u006e\u0064\u0061\u0074\u006f\u0072\u0079\u0020\u0066\u0069\u0065\u006c\u0064",path );
};if _efg :=_cee .DirAttr .ValidateWithPath (path +"\u002f\u0044\u0069\u0072\u0041\u0074\u0074\u0072");_efg !=nil {return _efg ;};if _cee .Rot !=nil {if _ggad :=_cee .Rot .ValidateWithPath (path +"\u002f\u0052\u006f\u0074");_ggad !=nil {return _ggad ;};
};return nil ;};func ParseUnionST_PositivePercentage (s string )(_abg .ST_PositivePercentage ,error ){return _abg .ParseUnionST_PositivePercentage (s );};type CT_Bevel struct{WAttr *int64 ;HAttr *int64 ;PrstAttr ST_BevelPresetType ;};func (_eeeg *CT_Shadow )MarshalXML (e *_fd .Encoder ,start _fd .StartElement )error {if _eeeg .BlurRadAttr !=nil {start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"w\u006f\u0072\u003a\u0062\u006c\u0075\u0072\u0052\u0061\u0064"},Value :_g .Sprintf ("\u0025\u0076",*_eeeg .BlurRadAttr )});
};if _eeeg .DistAttr !=nil {start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0077\u006f\u0072\u003a\u0064\u0069\u0073\u0074"},Value :_g .Sprintf ("\u0025\u0076",*_eeeg .DistAttr )});};if _eeeg .DirAttr !=nil {start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0077o\u0072\u003a\u0064\u0069\u0072"},Value :_g .Sprintf ("\u0025\u0076",*_eeeg .DirAttr )});
};if _eeeg .SxAttr !=nil {start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0077\u006f\u0072\u003a\u0073\u0078"},Value :_g .Sprintf ("\u0025\u0076",*_eeeg .SxAttr )});};if _eeeg .SyAttr !=nil {start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0077\u006f\u0072\u003a\u0073\u0079"},Value :_g .Sprintf ("\u0025\u0076",*_eeeg .SyAttr )});
};if _eeeg .KxAttr !=nil {start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0077\u006f\u0072\u003a\u006b\u0078"},Value :_g .Sprintf ("\u0025\u0076",*_eeeg .KxAttr )});};if _eeeg .KyAttr !=nil {start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0077\u006f\u0072\u003a\u006b\u0079"},Value :_g .Sprintf ("\u0025\u0076",*_eeeg .KyAttr )});
};if _eeeg .AlgnAttr !=ST_RectAlignmentUnset {_bdae ,_dbbg :=_eeeg .AlgnAttr .MarshalXMLAttr (_fd .Name {Local :"\u0077\u006f\u0072\u003a\u0061\u006c\u0067\u006e"});if _dbbg !=nil {return _dbbg ;};start .Attr =append (start .Attr ,_bdae );};e .EncodeToken (start );
if _eeeg .SrgbClr !=nil {_cgdda :=_fd .StartElement {Name :_fd .Name {Local :"w\u006f\u0072\u003a\u0073\u0072\u0067\u0062\u0043\u006c\u0072"}};e .EncodeElement (_eeeg .SrgbClr ,_cgdda );};if _eeeg .SchemeClr !=nil {_bfeg :=_fd .StartElement {Name :_fd .Name {Local :"\u0077\u006f\u0072\u003a\u0073\u0063\u0068\u0065\u006d\u0065\u0043\u006c\u0072"}};
e .EncodeElement (_eeeg .SchemeClr ,_bfeg );};e .EncodeToken (_fd .EndElement {Name :start .Name });return nil ;};type EG_LineJoinProperties struct{LineJoinPropertiesChoice *EG_LineJoinPropertiesChoice ;};type ST_OnOff byte ;func NewCT_GradientStopList ()*CT_GradientStopList {_dab :=&CT_GradientStopList {};
return _dab };func NewCT_SolidColorFillProperties ()*CT_SolidColorFillProperties {_acfc :=&CT_SolidColorFillProperties {};return _acfc ;};

// ValidateWithPath validates the DocId and its children, prefixing error messages with path
func (_egff *DocId )ValidateWithPath (path string )error {if _geda :=_egff .CT_LongHexNumber .ValidateWithPath (path );_geda !=nil {return _geda ;};return nil ;};

// ValidateWithPath validates the CustomXmlConflictInsRangeEnd and its children, prefixing error messages with path
func (_ddbab *CustomXmlConflictInsRangeEnd )ValidateWithPath (path string )error {if _eefc :=_ddbab .CT_Markup .ValidateWithPath (path );_eefc !=nil {return _eefc ;};return nil ;};

// Validate validates the EG_RPrTextEffects and its children
func (_ccee *EG_RPrTextEffects )Validate ()error {return _ccee .ValidateWithPath ("\u0045\u0047\u005f\u0052\u0050\u0072\u0054\u0065\u0078\u0074\u0045\u0066f\u0065\u0063\u0074\u0073");};func NewCT_Glow ()*CT_Glow {_acf :=&CT_Glow {};return _acf };

// Validate validates the CT_SdtCheckbox and its children
func (_adgf *CT_SdtCheckbox )Validate ()error {return _adgf .ValidateWithPath ("\u0043\u0054\u005f\u0053\u0064\u0074\u0043\u0068\u0065c\u006b\u0062\u006f\u0078");};func (_cagd *CT_Reflection )UnmarshalXML (d *_fd .Decoder ,start _fd .StartElement )error {for _ ,_bdb :=range start .Attr {if _bdb .Name .Local =="\u0062l\u0075\u0072\u0052\u0061\u0064"{_deaf ,_afed :=_a .ParseInt (_bdb .Value ,10,64);
if _afed !=nil {return _afed ;};_cagd .BlurRadAttr =&_deaf ;continue ;};if _bdb .Name .Local =="\u0073\u0074\u0041"{_cfgb ,_ccec :=ParseUnionST_PositiveFixedPercentage (_bdb .Value );if _ccec !=nil {return _ccec ;};_cagd .StAAttr =&_cfgb ;continue ;};if _bdb .Name .Local =="\u0073\u0074\u0050o\u0073"{_eafd ,_bdgf :=ParseUnionST_PositiveFixedPercentage (_bdb .Value );
if _bdgf !=nil {return _bdgf ;};_cagd .StPosAttr =&_eafd ;continue ;};if _bdb .Name .Local =="\u0065\u006e\u0064\u0041"{_adg ,_febf :=ParseUnionST_PositiveFixedPercentage (_bdb .Value );if _febf !=nil {return _febf ;};_cagd .EndAAttr =&_adg ;continue ;
};if _bdb .Name .Local =="\u0065\u006e\u0064\u0050\u006f\u0073"{_ceec ,_bebd :=ParseUnionST_PositiveFixedPercentage (_bdb .Value );if _bebd !=nil {return _bebd ;};_cagd .EndPosAttr =&_ceec ;continue ;};if _bdb .Name .Local =="\u0064\u0069\u0073\u0074"{_gagf ,_fdc :=_a .ParseInt (_bdb .Value ,10,64);
if _fdc !=nil {return _fdc ;};_cagd .DistAttr =&_gagf ;continue ;};if _bdb .Name .Local =="\u0064\u0069\u0072"{_fda ,_abf :=_a .ParseInt (_bdb .Value ,10,32);if _abf !=nil {return _abf ;};_fced :=int32 (_fda );_cagd .DirAttr =&_fced ;continue ;};if _bdb .Name .Local =="\u0066a\u0064\u0065\u0044\u0069\u0072"{_dee ,_efcc :=_a .ParseInt (_bdb .Value ,10,32);
if _efcc !=nil {return _efcc ;};_bgad :=int32 (_dee );_cagd .FadeDirAttr =&_bgad ;continue ;};if _bdb .Name .Local =="\u0073\u0078"{_bbcdc ,_dae :=ParseUnionST_Percentage (_bdb .Value );if _dae !=nil {return _dae ;};_cagd .SxAttr =&_bbcdc ;continue ;};
if _bdb .Name .Local =="\u0073\u0079"{_acdd ,_dfe :=ParseUnionST_Percentage (_bdb .Value );if _dfe !=nil {return _dfe ;};_cagd .SyAttr =&_acdd ;continue ;};if _bdb .Name .Local =="\u006b\u0078"{_beba ,_acdda :=_a .ParseInt (_bdb .Value ,10,32);if _acdda !=nil {return _acdda ;
};_fff :=int32 (_beba );_cagd .KxAttr =&_fff ;continue ;};if _bdb .Name .Local =="\u006b\u0079"{_abff ,_gbf :=_a .ParseInt (_bdb .Value ,10,32);if _gbf !=nil {return _gbf ;};_gba :=int32 (_abff );_cagd .KyAttr =&_gba ;continue ;};if _bdb .Name .Local =="\u0061\u006c\u0067\u006e"{_cagd .AlgnAttr .UnmarshalXMLAttr (_bdb );
continue ;};};for {_edgd ,_adc :=d .Token ();if _adc !=nil {return _g .Errorf ("\u0070a\u0072\u0073\u0069\u006eg\u0020\u0043\u0054\u005f\u0052e\u0066l\u0065c\u0074\u0069\u006f\u006e\u003a\u0020\u0025s",_adc );};if _ebf ,_cgfe :=_edgd .(_fd .EndElement );
_cgfe &&_ebf .Name ==start .Name {break ;};};return nil ;};func (_badg ST_SchemeColorVal )ValidateWithPath (path string )error {switch _badg {case 0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17:default:return _g .Errorf ("\u0025s\u003a\u0020\u006f\u0075t\u0020\u006f\u0066\u0020\u0072a\u006eg\u0065 \u0076\u0061\u006c\u0075\u0065\u0020\u0025d",path ,int (_badg ));
};return nil ;};func (_eccdf *CustomXmlConflictInsRangeEnd )UnmarshalXML (d *_fd .Decoder ,start _fd .StartElement )error {_eccdf .CT_Markup =*_fe .NewCT_Markup ();for {_acbf ,_fgfg :=d .Token ();if _fgfg !=nil {return _g .Errorf ("p\u0061\u0072\u0073\u0069\u006e\u0067\u0020\u0043\u0075\u0073\u0074\u006f\u006d\u0058\u006d\u006c\u0043\u006fn\u0066\u006c\u0069\u0063\u0074\u0049\u006e\u0073\u0052\u0061ng\u0065\u0045\u006ed\u003a \u0025\u0073",_fgfg );
};if _gdcd ,_bacac :=_acbf .(_fd .EndElement );_bacac &&_gdcd .Name ==start .Name {break ;};};return nil ;};

// ValidateWithPath validates the DefaultImageDpi and its children, prefixing error messages with path
func (_dcag *DefaultImageDpi )ValidateWithPath (path string )error {if _adde :=_dcag .CT_DefaultImageDpi .ValidateWithPath (path );_adde !=nil {return _adde ;};return nil ;};func (_cgbg ST_Ligatures )String ()string {switch _cgbg {case 0:return "";case 1:return "\u006e\u006f\u006e\u0065";
case 2:return "\u0073\u0074\u0061\u006e\u0064\u0061\u0072\u0064";case 3:return "\u0063\u006f\u006e\u0074\u0065\u0078\u0074\u0075\u0061\u006c";case 4:return "\u0068\u0069\u0073\u0074\u006f\u0072\u0069\u0063\u0061\u006c";case 5:return "\u0064\u0069\u0073c\u0072\u0065\u0074\u0069\u006f\u006e\u0061\u006c";
case 6:return "\u0073t\u0061n\u0064\u0061\u0072\u0064\u0043o\u006e\u0074e\u0078\u0074\u0075\u0061\u006c";case 7:return "\u0073t\u0061n\u0064\u0061\u0072\u0064\u0048i\u0073\u0074o\u0072\u0069\u0063\u0061\u006c";case 8:return "c\u006fn\u0074\u0065\u0078\u0074\u0075\u0061\u006c\u0048i\u0073\u0074\u006f\u0072ic\u0061\u006c";
case 9:return "s\u0074a\u006e\u0064\u0061\u0072\u0064\u0044\u0069\u0073c\u0072\u0065\u0074\u0069on\u0061\u006c";case 10:return "\u0063\u006f\u006e\u0074ex\u0074\u0075\u0061\u006c\u0044\u0069\u0073\u0063\u0072\u0065\u0074\u0069\u006f\u006ea\u006c";case 11:return "\u0068\u0069\u0073\u0074or\u0069\u0063\u0061\u006c\u0044\u0069\u0073\u0063\u0072\u0065\u0074\u0069\u006f\u006ea\u006c";
case 12:return "\u0073\u0074\u0061\u006ed\u0061\u0072\u0064\u0043\u006f\u006e\u0074\u0065\u0078\u0074u\u0061l\u0048\u0069\u0073\u0074\u006f\u0072\u0069c\u0061\u006c";case 13:return "\u0073\u0074\u0061\u006e\u0064\u0061\u0072\u0064\u0043\u006fn\u0074\u0065\u0078\u0074\u0075\u0061\u006cD\u0069\u0073\u0063\u0072\u0065\u0074\u0069\u006f\u006e\u0061\u006c";
case 14:return "\u0073\u0074\u0061\u006e\u0064\u0061\u0072\u0064\u0048\u0069s\u0074\u006f\u0072\u0069\u0063\u0061\u006cD\u0069\u0073\u0063\u0072\u0065\u0074\u0069\u006f\u006e\u0061\u006c";case 15:return "\u0063\u006f\u006e\u0074\u0065\u0078\u0074\u0075\u0061\u006c\u0048\u0069\u0073\u0074\u006fr\u0069c\u0061\u006c\u0044\u0069\u0073\u0063\u0072\u0065\u0074\u0069\u006f\u006e\u0061\u006c";
case 16:return "\u0061\u006c\u006c";};return "";};

// ValidateWithPath validates the CT_Glow and its children, prefixing error messages with path
func (_feb *CT_Glow )ValidateWithPath (path string )error {if _feb .RadAttr !=nil {if *_feb .RadAttr < 0{return _g .Errorf ("%\u0073\u002f\u006d\u002e\u0052\u0061d\u0041\u0074\u0074\u0072\u0020\u006du\u0073\u0074\u0020\u0062\u0065\u0020\u003e=\u0020\u0030\u0020\u0028\u0068\u0061\u0076\u0065\u0020\u0025v\u0029",path ,*_feb .RadAttr );
};if *_feb .RadAttr > 27273042316900{return _g .Errorf ("\u0025\u0073\u002f\u006d\u002e\u0052\u0061\u0064A\u0074\u0074\u0072 m\u0075\u0073\u0074\u0020\u0062\u0065 \u003c\u003d\u0020\u0032\u0037\u0032\u0037\u0033\u0030\u0034\u0032\u0033\u0031\u0036\u00390\u0030\u0020\u0028\u0068\u0061\u0076\u0065\u0020%\u0076\u0029",path ,*_feb .RadAttr );
};};if _feb .SrgbClr !=nil {if _eba :=_feb .SrgbClr .ValidateWithPath (path +"\u002f\u0053\u0072\u0067\u0062\u0043\u006c\u0072");_eba !=nil {return _eba ;};};if _feb .SchemeClr !=nil {if _ef :=_feb .SchemeClr .ValidateWithPath (path +"\u002f\u0053\u0063\u0068\u0065\u006d\u0065\u0043\u006c\u0072");
_ef !=nil {return _ef ;};};return nil ;};func ParseUnionST_TextPoint (s string )(_abg .ST_TextPoint ,error ){return _abg .ParseUnionST_TextPoint (s );};type CT_LongHexNumber struct{ValAttr string ;};func (_fgad ST_RectAlignment )String ()string {switch _fgad {case 0:return "";
case 1:return "\u006e\u006f\u006e\u0065";case 2:return "\u0074\u006c";case 3:return "\u0074";case 4:return "\u0074\u0072";case 5:return "\u006c";case 6:return "\u0063\u0074\u0072";case 7:return "\u0072";case 8:return "\u0062\u006c";case 9:return "\u0062";
case 10:return "\u0062\u0072";};return "";};func NewCT_RelativeRect ()*CT_RelativeRect {_dcbc :=&CT_RelativeRect {};return _dcbc };

// ValidateWithPath validates the EntityPicker and its children, prefixing error messages with path
func (_begc *EntityPicker )ValidateWithPath (path string )error {if _eafe :=_begc .CT_Empty .ValidateWithPath (path );_eafe !=nil {return _eafe ;};return nil ;};

// ValidateWithPath validates the CT_NumForm and its children, prefixing error messages with path
func (_bdc *CT_NumForm )ValidateWithPath (path string )error {if _bdc .ValAttr ==ST_NumFormUnset {return _g .Errorf ("\u0025\u0073\u002fV\u0061\u006c\u0041\u0074t\u0072\u0020\u0069\u0073\u0020\u0061\u0020m\u0061\u006e\u0064\u0061\u0074\u006f\u0072\u0079\u0020\u0066\u0069\u0065\u006c\u0064",path );
};if _bdf :=_bdc .ValAttr .ValidateWithPath (path +"\u002f\u0056\u0061\u006c\u0041\u0074\u0074\u0072");_bdf !=nil {return _bdf ;};return nil ;};func (_ceg *CT_RelativeRect )MarshalXML (e *_fd .Encoder ,start _fd .StartElement )error {if _ceg .LAttr !=nil {start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0077\u006f\u0072:\u006c"},Value :_g .Sprintf ("\u0025\u0076",*_ceg .LAttr )});
};if _ceg .TAttr !=nil {start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0077\u006f\u0072:\u0074"},Value :_g .Sprintf ("\u0025\u0076",*_ceg .TAttr )});};if _ceg .RAttr !=nil {start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0077\u006f\u0072:\u0072"},Value :_g .Sprintf ("\u0025\u0076",*_ceg .RAttr )});
};if _ceg .BAttr !=nil {start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0077\u006f\u0072:\u0062"},Value :_g .Sprintf ("\u0025\u0076",*_ceg .BAttr )});};e .EncodeToken (start );e .EncodeToken (_fd .EndElement {Name :start .Name });
return nil ;};func (_gfgdb *ST_PenAlignment )UnmarshalXMLAttr (attr _fd .Attr )error {switch attr .Value {case "":*_gfgdb =0;case "\u0063\u0074\u0072":*_gfgdb =1;case "\u0069\u006e":*_gfgdb =2;};return nil ;};const (ST_PresetCameraTypeUnset ST_PresetCameraType =0;
ST_PresetCameraTypeLegacyObliqueTopLeft ST_PresetCameraType =1;ST_PresetCameraTypeLegacyObliqueTop ST_PresetCameraType =2;ST_PresetCameraTypeLegacyObliqueTopRight ST_PresetCameraType =3;ST_PresetCameraTypeLegacyObliqueLeft ST_PresetCameraType =4;ST_PresetCameraTypeLegacyObliqueFront ST_PresetCameraType =5;
ST_PresetCameraTypeLegacyObliqueRight ST_PresetCameraType =6;ST_PresetCameraTypeLegacyObliqueBottomLeft ST_PresetCameraType =7;ST_PresetCameraTypeLegacyObliqueBottom ST_PresetCameraType =8;ST_PresetCameraTypeLegacyObliqueBottomRight ST_PresetCameraType =9;
ST_PresetCameraTypeLegacyPerspectiveTopLeft ST_PresetCameraType =10;ST_PresetCameraTypeLegacyPerspectiveTop ST_PresetCameraType =11;ST_PresetCameraTypeLegacyPerspectiveTopRight ST_PresetCameraType =12;ST_PresetCameraTypeLegacyPerspectiveLeft ST_PresetCameraType =13;
ST_PresetCameraTypeLegacyPerspectiveFront ST_PresetCameraType =14;ST_PresetCameraTypeLegacyPerspectiveRight ST_PresetCameraType =15;ST_PresetCameraTypeLegacyPerspectiveBottomLeft ST_PresetCameraType =16;ST_PresetCameraTypeLegacyPerspectiveBottom ST_PresetCameraType =17;
ST_PresetCameraTypeLegacyPerspectiveBottomRight ST_PresetCameraType =18;ST_PresetCameraTypeOrthographicFront ST_PresetCameraType =19;ST_PresetCameraTypeIsometricTopUp ST_PresetCameraType =20;ST_PresetCameraTypeIsometricTopDown ST_PresetCameraType =21;ST_PresetCameraTypeIsometricBottomUp ST_PresetCameraType =22;
ST_PresetCameraTypeIsometricBottomDown ST_PresetCameraType =23;ST_PresetCameraTypeIsometricLeftUp ST_PresetCameraType =24;ST_PresetCameraTypeIsometricLeftDown ST_PresetCameraType =25;ST_PresetCameraTypeIsometricRightUp ST_PresetCameraType =26;ST_PresetCameraTypeIsometricRightDown ST_PresetCameraType =27;
ST_PresetCameraTypeIsometricOffAxis1Left ST_PresetCameraType =28;ST_PresetCameraTypeIsometricOffAxis1Right ST_PresetCameraType =29;ST_PresetCameraTypeIsometricOffAxis1Top ST_PresetCameraType =30;ST_PresetCameraTypeIsometricOffAxis2Left ST_PresetCameraType =31;
ST_PresetCameraTypeIsometricOffAxis2Right ST_PresetCameraType =32;ST_PresetCameraTypeIsometricOffAxis2Top ST_PresetCameraType =33;ST_PresetCameraTypeIsometricOffAxis3Left ST_PresetCameraType =34;ST_PresetCameraTypeIsometricOffAxis3Right ST_PresetCameraType =35;
ST_PresetCameraTypeIsometricOffAxis3Bottom ST_PresetCameraType =36;ST_PresetCameraTypeIsometricOffAxis4Left ST_PresetCameraType =37;ST_PresetCameraTypeIsometricOffAxis4Right ST_PresetCameraType =38;ST_PresetCameraTypeIsometricOffAxis4Bottom ST_PresetCameraType =39;
ST_PresetCameraTypeObliqueTopLeft ST_PresetCameraType =40;ST_PresetCameraTypeObliqueTop ST_PresetCameraType =41;ST_PresetCameraTypeObliqueTopRight ST_PresetCameraType =42;ST_PresetCameraTypeObliqueLeft ST_PresetCameraType =43;ST_PresetCameraTypeObliqueRight ST_PresetCameraType =44;
ST_PresetCameraTypeObliqueBottomLeft ST_PresetCameraType =45;ST_PresetCameraTypeObliqueBottom ST_PresetCameraType =46;ST_PresetCameraTypeObliqueBottomRight ST_PresetCameraType =47;ST_PresetCameraTypePerspectiveFront ST_PresetCameraType =48;ST_PresetCameraTypePerspectiveLeft ST_PresetCameraType =49;
ST_PresetCameraTypePerspectiveRight ST_PresetCameraType =50;ST_PresetCameraTypePerspectiveAbove ST_PresetCameraType =51;ST_PresetCameraTypePerspectiveBelow ST_PresetCameraType =52;ST_PresetCameraTypePerspectiveAboveLeftFacing ST_PresetCameraType =53;ST_PresetCameraTypePerspectiveAboveRightFacing ST_PresetCameraType =54;
ST_PresetCameraTypePerspectiveContrastingLeftFacing ST_PresetCameraType =55;ST_PresetCameraTypePerspectiveContrastingRightFacing ST_PresetCameraType =56;ST_PresetCameraTypePerspectiveHeroicLeftFacing ST_PresetCameraType =57;ST_PresetCameraTypePerspectiveHeroicRightFacing ST_PresetCameraType =58;
ST_PresetCameraTypePerspectiveHeroicExtremeLeftFacing ST_PresetCameraType =59;ST_PresetCameraTypePerspectiveHeroicExtremeRightFacing ST_PresetCameraType =60;ST_PresetCameraTypePerspectiveRelaxed ST_PresetCameraType =61;ST_PresetCameraTypePerspectiveRelaxedModerately ST_PresetCameraType =62;
);func NewEG_LineJoinPropertiesChoice ()*EG_LineJoinPropertiesChoice {_decd :=&EG_LineJoinPropertiesChoice {};return _decd ;};func (_cdgbeg *DefaultImageDpi )UnmarshalXML (d *_fd .Decoder ,start _fd .StartElement )error {_cdgbeg .CT_DefaultImageDpi =*NewCT_DefaultImageDpi ();
for _ ,_ccgg :=range start .Attr {if _ccgg .Name .Local =="\u0076\u0061\u006c"{_ggba ,_dgcc :=_a .ParseInt (_ccgg .Value ,10,64);if _dgcc !=nil {return _dgcc ;};_cdgbeg .ValAttr =_ggba ;continue ;};};for {_edba ,_gea :=d .Token ();if _gea !=nil {return _g .Errorf ("p\u0061\u0072\u0073\u0069\u006e\u0067 \u0044\u0065\u0066\u0061\u0075\u006c\u0074\u0049\u006da\u0067\u0065\u0044p\u0069:\u0020\u0025\u0073",_gea );
};if _fcc ,_dbcb :=_edba .(_fd .EndElement );_dbcb &&_fcc .Name ==start .Name {break ;};};return nil ;};

// Validate validates the CT_SolidColorFillProperties and its children
func (_fga *CT_SolidColorFillProperties )Validate ()error {return _fga .ValidateWithPath ("C\u0054\u005f\u0053\u006f\u006c\u0069d\u0043\u006f\u006c\u006f\u0072\u0046\u0069\u006c\u006cP\u0072\u006f\u0070e\u0072t\u0069\u0065\u0073");};func (_fbe *CT_Scene3D )UnmarshalXML (d *_fd .Decoder ,start _fd .StartElement )error {_fbe .Camera =NewCT_Camera ();
_fbe .LightRig =NewCT_LightRig ();_aefg :for {_fcdg ,_ddbf :=d .Token ();if _ddbf !=nil {return _ddbf ;};switch _decce :=_fcdg .(type ){case _fd .StartElement :switch _decce .Name {case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u0063\u0061\u006d\u0065\u0072\u0061"}:if _bfgb :=d .DecodeElement (_fbe .Camera ,&_decce );
_bfgb !=nil {return _bfgb ;};case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u006c\u0069\u0067\u0068\u0074\u0052\u0069\u0067"}:if _efge :=d .DecodeElement (_fbe .LightRig ,&_decce );
_efge !=nil {return _efge ;};default:_c .Log .Debug ("\u0073k\u0069\u0070p\u0069\u006e\u0067 \u0075\u006e\u0073\u0075\u0070\u0070\u006fr\u0074\u0065\u0064\u0020\u0065\u006ce\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0043\u0054\u005fS\u0063\u0065\u006e\u0065\u0033\u0044\u0020\u0025\u0076",_decce .Name );
if _bff :=d .Skip ();_bff !=nil {return _bff ;};};case _fd .EndElement :break _aefg ;case _fd .CharData :};};return nil ;};

// Validate validates the EG_ShadeProperties and its children
func (_beae *EG_ShadeProperties )Validate ()error {return _beae .ValidateWithPath ("\u0045G\u005fS\u0068\u0061\u0064\u0065\u0050r\u006f\u0070e\u0072\u0074\u0069\u0065\u0073");};func (_cced ST_NumForm )ValidateWithPath (path string )error {switch _cced {case 0,1,2,3:default:return _g .Errorf ("\u0025s\u003a\u0020\u006f\u0075t\u0020\u006f\u0066\u0020\u0072a\u006eg\u0065 \u0076\u0061\u006c\u0075\u0065\u0020\u0025d",path ,int (_cced ));
};return nil ;};type CT_TextOutlineEffect struct{WAttr *int32 ;CapAttr ST_LineCap ;CmpdAttr ST_CompoundLine ;AlgnAttr ST_PenAlignment ;FillPropertiesChoice *EG_FillPropertiesChoice ;PrstDash *CT_PresetLineDashProperties ;LineJoinPropertiesChoice *EG_LineJoinPropertiesChoice ;
};func (_dgab *EG_ShadeProperties )UnmarshalXML (d *_fd .Decoder ,start _fd .StartElement )error {_dgab .ShadePropertiesChoice =NewEG_ShadePropertiesChoice ();_becea :for {_fbabc ,_gbgf :=d .Token ();if _gbgf !=nil {return _gbgf ;};switch _ecfc :=_fbabc .(type ){case _fd .StartElement :switch _ecfc .Name {case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u006c\u0069\u006e"}:_dgab .ShadePropertiesChoice =NewEG_ShadePropertiesChoice ();
if _deba :=d .DecodeElement (&_dgab .ShadePropertiesChoice .Lin ,&_ecfc );_deba !=nil {return _deba ;};case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u0070\u0061\u0074\u0068"}:_dgab .ShadePropertiesChoice =NewEG_ShadePropertiesChoice ();
if _edbb :=d .DecodeElement (&_dgab .ShadePropertiesChoice .Path ,&_ecfc );_edbb !=nil {return _edbb ;};default:_c .Log .Debug ("\u0073\u006b\u0069\u0070\u0070\u0069\u006e\u0067\u0020\u0075\u006es\u0075\u0070\u0070\u006f\u0072\u0074\u0065\u0064 \u0065l\u0065\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0045\u0047\u005f\u0053\u0068\u0061\u0064\u0065\u0050r\u006f\u0070\u0065\u0072\u0074\u0069\u0065\u0073\u0020\u0025\u0076",_ecfc .Name );
if _gbcbf :=d .Skip ();_gbcbf !=nil {return _gbcbf ;};};case _fd .EndElement :break _becea ;case _fd .CharData :};};return nil ;};func (_gfbe *EG_ColorChoice )MarshalXML (e *_fd .Encoder ,start _fd .StartElement )error {if _gfbe .SrgbClr !=nil {_ebeb :=_fd .StartElement {Name :_fd .Name {Local :"w\u006f\u0072\u003a\u0073\u0072\u0067\u0062\u0043\u006c\u0072"}};
e .EncodeElement (_gfbe .SrgbClr ,_ebeb );}else if _gfbe .SchemeClr !=nil {_bcbf :=_fd .StartElement {Name :_fd .Name {Local :"\u0077\u006f\u0072\u003a\u0073\u0063\u0068\u0065\u006d\u0065\u0043\u006c\u0072"}};e .EncodeElement (_gfbe .SchemeClr ,_bcbf );
};return nil ;};func (_facec ST_NumForm )String ()string {switch _facec {case 0:return "";case 1:return "\u0064e\u0066\u0061\u0075\u006c\u0074";case 2:return "\u006c\u0069\u006e\u0069\u006e\u0067";case 3:return "\u006f\u006c\u0064\u0053\u0074\u0079\u006c\u0065";
};return "";};func (_dcfg ST_LightRigDirection )MarshalXML (e *_fd .Encoder ,start _fd .StartElement )error {return e .EncodeElement (_dcfg .String (),start );};

// ValidateWithPath validates the EG_Conflicts and its children, prefixing error messages with path
func (_aaag *EG_Conflicts )ValidateWithPath (path string )error {if _dbbe :=_aaag .ConflictsChoice .ValidateWithPath (path +"\u002f\u0043o\u006e\u0066\u006ci\u0063\u0074\u0073\u0043\u0068\u006f\u0069\u0063\u0065");_dbbe !=nil {return _dbbe ;};return nil ;
};func (_bfgdf *ST_PresetMaterialType )UnmarshalXML (d *_fd .Decoder ,start _fd .StartElement )error {_fdba ,_bdag :=d .Token ();if _bdag !=nil {return _bdag ;};if _cdcd ,_aace :=_fdba .(_fd .EndElement );_aace &&_cdcd .Name ==start .Name {*_bfgdf =1;return nil ;
};if _bgbe ,_daba :=_fdba .(_fd .CharData );!_daba {return _g .Errorf ("\u0065\u0078\u0070\u0065\u0063\u0074\u0065\u0064\u0020\u0063\u0068a\u0072\u0020\u0064\u0061\u0074\u0061\u002c\u0020\u0067\u006ft\u0020\u0025\u0054",_fdba );}else {switch string (_bgbe ){case "":*_bfgdf =0;
case "l\u0065\u0067\u0061\u0063\u0079\u004d\u0061\u0074\u0074\u0065":*_bfgdf =1;case "\u006c\u0065\u0067\u0061\u0063\u0079\u0050\u006c\u0061\u0073\u0074\u0069\u0063":*_bfgdf =2;case "l\u0065\u0067\u0061\u0063\u0079\u004d\u0065\u0074\u0061\u006c":*_bfgdf =3;
case "\u006ce\u0067a\u0063\u0079\u0057\u0069\u0072\u0065\u0066\u0072\u0061\u006d\u0065":*_bfgdf =4;case "\u006d\u0061\u0074t\u0065":*_bfgdf =5;case "\u0070l\u0061\u0073\u0074\u0069\u0063":*_bfgdf =6;case "\u006d\u0065\u0074a\u006c":*_bfgdf =7;case "\u0077a\u0072\u006d\u004d\u0061\u0074\u0074e":*_bfgdf =8;
case "\u0074\u0072\u0061\u006e\u0073\u006c\u0075\u0063\u0065\u006e\u0074\u0050o\u0077\u0064\u0065\u0072":*_bfgdf =9;case "\u0070\u006f\u0077\u0064\u0065\u0072":*_bfgdf =10;case "\u0064\u006b\u0045\u0064\u0067\u0065":*_bfgdf =11;case "\u0073\u006f\u0066\u0074\u0045\u0064\u0067\u0065":*_bfgdf =12;
case "\u0063\u006c\u0065a\u0072":*_bfgdf =13;case "\u0066\u006c\u0061\u0074":*_bfgdf =14;case "\u0073o\u0066\u0074\u006d\u0065\u0074\u0061l":*_bfgdf =15;case "\u006e\u006f\u006e\u0065":*_bfgdf =16;};};_fdba ,_bdag =d .Token ();if _bdag !=nil {return _bdag ;
};if _cdedb ,_bfdb :=_fdba .(_fd .EndElement );_bfdb &&_cdedb .Name ==start .Name {return nil ;};return _g .Errorf ("\u0065\u0078\u0070\u0065c\u0074\u0065\u0064\u0020\u0065\u006e\u0064\u0020\u0065\u006ce\u006de\u006e\u0074\u002c\u0020\u0067\u006f\u0074 \u0025\u0076",_fdba );
};func (_fcbg *EG_Conflicts )UnmarshalXML (d *_fd .Decoder ,start _fd .StartElement )error {_fcbg .ConflictsChoice =NewEG_ConflictsChoice ();_bgde :for {_efgb ,_dgcca :=d .Token ();if _dgcca !=nil {return _dgcca ;};switch _fcagg :=_efgb .(type ){case _fd .StartElement :switch _fcagg .Name {case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"c\u006f\u006e\u0066\u006c\u0069\u0063\u0074\u0049\u006e\u0073"}:_fcbg .ConflictsChoice =NewEG_ConflictsChoice ();
if _dcgg :=d .DecodeElement (&_fcbg .ConflictsChoice .ConflictIns ,&_fcagg );_dcgg !=nil {return _dcgg ;};case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"c\u006f\u006e\u0066\u006c\u0069\u0063\u0074\u0044\u0065\u006c"}:_fcbg .ConflictsChoice =NewEG_ConflictsChoice ();
if _ddcc :=d .DecodeElement (&_fcbg .ConflictsChoice .ConflictDel ,&_fcagg );_ddcc !=nil {return _ddcc ;};default:_c .Log .Debug ("s\u006b\u0069\u0070\u0070\u0069\u006e\u0067\u0020\u0075n\u0073\u0075\u0070\u0070\u006f\u0072\u0074ed\u0020\u0065\u006c\u0065m\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0045\u0047_C\u006f\u006ef\u006c\u0069\u0063\u0074\u0073\u0020\u0025\u0076",_fcagg .Name );
if _bad :=d .Skip ();_bad !=nil {return _bad ;};};case _fd .EndElement :break _bgde ;case _fd .CharData :};};return nil ;};const (ST_LigaturesUnset ST_Ligatures =0;ST_LigaturesNone ST_Ligatures =1;ST_LigaturesStandard ST_Ligatures =2;ST_LigaturesContextual ST_Ligatures =3;
ST_LigaturesHistorical ST_Ligatures =4;ST_LigaturesDiscretional ST_Ligatures =5;ST_LigaturesStandardContextual ST_Ligatures =6;ST_LigaturesStandardHistorical ST_Ligatures =7;ST_LigaturesContextualHistorical ST_Ligatures =8;ST_LigaturesStandardDiscretional ST_Ligatures =9;
ST_LigaturesContextualDiscretional ST_Ligatures =10;ST_LigaturesHistoricalDiscretional ST_Ligatures =11;ST_LigaturesStandardContextualHistorical ST_Ligatures =12;ST_LigaturesStandardContextualDiscretional ST_Ligatures =13;ST_LigaturesStandardHistoricalDiscretional ST_Ligatures =14;
ST_LigaturesContextualHistoricalDiscretional ST_Ligatures =15;ST_LigaturesAll ST_Ligatures =16;);type EG_ColorChoice struct{SrgbClr *CT_SRgbColor ;SchemeClr *CT_SchemeColor ;};func (_affa ST_PenAlignment )String ()string {switch _affa {case 0:return "";
case 1:return "\u0063\u0074\u0072";case 2:return "\u0069\u006e";};return "";};func (_cded ST_PathShadeType )MarshalXML (e *_fd .Encoder ,start _fd .StartElement )error {return e .EncodeElement (_cded .String (),start );};func (_abbb *CT_SphereCoords )UnmarshalXML (d *_fd .Decoder ,start _fd .StartElement )error {_abbb .LatAttr =0;
_abbb .LonAttr =0;_abbb .RevAttr =0;for _ ,_fgdgb :=range start .Attr {if _fgdgb .Name .Local =="\u006c\u0061\u0074"{_eda ,_gdfbf :=_a .ParseInt (_fgdgb .Value ,10,32);if _gdfbf !=nil {return _gdfbf ;};_abbb .LatAttr =int32 (_eda );continue ;};if _fgdgb .Name .Local =="\u006c\u006f\u006e"{_defe ,_fefg :=_a .ParseInt (_fgdgb .Value ,10,32);
if _fefg !=nil {return _fefg ;};_abbb .LonAttr =int32 (_defe );continue ;};if _fgdgb .Name .Local =="\u0072\u0065\u0076"{_cae ,_addd :=_a .ParseInt (_fgdgb .Value ,10,32);if _addd !=nil {return _addd ;};_abbb .RevAttr =int32 (_cae );continue ;};};for {_bafd ,_fdg :=d .Token ();
if _fdg !=nil {return _g .Errorf ("p\u0061\u0072\u0073\u0069\u006e\u0067 \u0043\u0054\u005f\u0053\u0070\u0068\u0065\u0072\u0065C\u006f\u006f\u0072d\u0073:\u0020\u0025\u0073",_fdg );};if _gfeca ,_gbcf :=_bafd .(_fd .EndElement );_gbcf &&_gfeca .Name ==start .Name {break ;
};};return nil ;};

// Validate validates the CT_Shadow and its children
func (_dgdb *CT_Shadow )Validate ()error {return _dgdb .ValidateWithPath ("\u0043T\u005f\u0053\u0068\u0061\u0064\u006fw");};type ST_SchemeColorVal byte ;type EG_RunLevelConflicts struct{ConflictIns *_fe .CT_RunTrackChange ;ConflictDel *_fe .CT_RunTrackChange ;
};func (_cbbde *DiscardImageEditingData )MarshalXML (e *_fd .Encoder ,start _fd .StartElement )error {start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0078\u006d\u006cn\u0073"},Value :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c"});
start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0061"},Value :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065m\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006cf\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077\u0069\u006e\u0067m\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u006d\u0061\u0069\u006e"});
start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0073"},Value :"\u0068\u0074\u0074\u0070\u003a/\u002f\u0073\u0063\u0068\u0065m\u0061s\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067/\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0068\u0061\u0072e\u0064\u0054\u0079\u0070\u0065\u0073"});
start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0077"},Value :"ht\u0074\u0070:\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073.\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0077\u006f\u0072\u0064\u0070\u0072\u006f\u0063\u0065s\u0073i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u00306\u002fm\u0061\u0069n"});
start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0077\u006fr"},Value :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c"});
start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0078\u006dl"},Value :"\u0068\u0074tp\u003a\u002f\u002fw\u0077\u0077\u002e\u00773.o\u0072g/\u0058\u004d\u004c\u002f\u0031\u0039\u00398/\u006e\u0061\u006d\u0065\u0073\u0070\u0061c\u0065"});
start .Name .Local ="w\u006f\u0072\u003a\u0064\u0069\u0073c\u0061\u0072\u0064\u0049\u006d\u0061\u0067\u0065\u0045d\u0069\u0074\u0069n\u0067D\u0061\u0074\u0061";return _cbbde .CT_OnOff .MarshalXML (e ,start );};

// Validate validates the CustomXmlConflictDelRangeStart and its children
func (_abec *CustomXmlConflictDelRangeStart )Validate ()error {return _abec .ValidateWithPath ("\u0043\u0075\u0073\u0074\u006f\u006d\u0058\u006d\u006c\u0043o\u006e\u0066\u006c\u0069\u0063\u0074\u0044e\u006c\u0052\u0061\u006e\u0067\u0065\u0053\u0074\u0061\u0072\u0074");
};func (_fdee *ST_PenAlignment )UnmarshalXML (d *_fd .Decoder ,start _fd .StartElement )error {_bbcb ,_ffbc :=d .Token ();if _ffbc !=nil {return _ffbc ;};if _geeg ,_fggb :=_bbcb .(_fd .EndElement );_fggb &&_geeg .Name ==start .Name {*_fdee =1;return nil ;
};if _ebfg ,_bfefc :=_bbcb .(_fd .CharData );!_bfefc {return _g .Errorf ("\u0065\u0078\u0070\u0065\u0063\u0074\u0065\u0064\u0020\u0063\u0068a\u0072\u0020\u0064\u0061\u0074\u0061\u002c\u0020\u0067\u006ft\u0020\u0025\u0054",_bbcb );}else {switch string (_ebfg ){case "":*_fdee =0;
case "\u0063\u0074\u0072":*_fdee =1;case "\u0069\u006e":*_fdee =2;};};_bbcb ,_ffbc =d .Token ();if _ffbc !=nil {return _ffbc ;};if _cbfgd ,_fgae :=_bbcb .(_fd .EndElement );_fgae &&_cbfgd .Name ==start .Name {return nil ;};return _g .Errorf ("\u0065\u0078\u0070\u0065c\u0074\u0065\u0064\u0020\u0065\u006e\u0064\u0020\u0065\u006ce\u006de\u006e\u0074\u002c\u0020\u0067\u006f\u0074 \u0025\u0076",_bbcb );
};func (_cbed ST_PathShadeType )MarshalXMLAttr (name _fd .Name )(_fd .Attr ,error ){_ccd :=_fd .Attr {};_ccd .Name =name ;switch _cbed {case ST_PathShadeTypeUnset :_ccd .Value ="";case ST_PathShadeTypeShape :_ccd .Value ="\u0073\u0068\u0061p\u0065";case ST_PathShadeTypeCircle :_ccd .Value ="\u0063\u0069\u0072\u0063\u006c\u0065";
case ST_PathShadeTypeRect :_ccd .Value ="\u0072\u0065\u0063\u0074";};return _ccd ,nil ;};func (_bbed *CT_PresetLineDashProperties )MarshalXML (e *_fd .Encoder ,start _fd .StartElement )error {if _bbed .ValAttr !=ST_PresetLineDashValUnset {_aga ,_eeed :=_bbed .ValAttr .MarshalXMLAttr (_fd .Name {Local :"\u0077o\u0072\u003a\u0076\u0061\u006c"});
if _eeed !=nil {return _eeed ;};start .Attr =append (start .Attr ,_aga );};e .EncodeToken (start );e .EncodeToken (_fd .EndElement {Name :start .Name });return nil ;};

// Validate validates the DefaultImageDpi and its children
func (_gfcd *DefaultImageDpi )Validate ()error {return _gfcd .ValidateWithPath ("\u0044e\u0066a\u0075\u006c\u0074\u0049\u006d\u0061\u0067\u0065\u0044\u0070\u0069");};func (_beab ST_BevelPresetType )MarshalXMLAttr (name _fd .Name )(_fd .Attr ,error ){_cbgb :=_fd .Attr {};
_cbgb .Name =name ;switch _beab {case ST_BevelPresetTypeUnset :_cbgb .Value ="";case ST_BevelPresetTypeRelaxedInset :_cbgb .Value ="\u0072\u0065\u006ca\u0078\u0065\u0064\u0049\u006e\u0073\u0065\u0074";case ST_BevelPresetTypeCircle :_cbgb .Value ="\u0063\u0069\u0072\u0063\u006c\u0065";
case ST_BevelPresetTypeSlope :_cbgb .Value ="\u0073\u006c\u006fp\u0065";case ST_BevelPresetTypeCross :_cbgb .Value ="\u0063\u0072\u006fs\u0073";case ST_BevelPresetTypeAngle :_cbgb .Value ="\u0061\u006e\u0067l\u0065";case ST_BevelPresetTypeSoftRound :_cbgb .Value ="\u0073o\u0066\u0074\u0052\u006f\u0075\u006ed";
case ST_BevelPresetTypeConvex :_cbgb .Value ="\u0063\u006f\u006e\u0076\u0065\u0078";case ST_BevelPresetTypeCoolSlant :_cbgb .Value ="\u0063o\u006f\u006c\u0053\u006c\u0061\u006et";case ST_BevelPresetTypeDivot :_cbgb .Value ="\u0064\u0069\u0076o\u0074";case ST_BevelPresetTypeRiblet :_cbgb .Value ="\u0072\u0069\u0062\u006c\u0065\u0074";
case ST_BevelPresetTypeHardEdge :_cbgb .Value ="\u0068\u0061\u0072\u0064\u0045\u0064\u0067\u0065";case ST_BevelPresetTypeArtDeco :_cbgb .Value ="\u0061r\u0074\u0044\u0065\u0063\u006f";};return _cbgb ,nil ;};

// ValidateWithPath validates the CT_StylisticSets and its children, prefixing error messages with path
func (_bdee *CT_StylisticSets )ValidateWithPath (path string )error {for _gbdg ,_bdbb :=range _bdee .StyleSet {if _ccca :=_bdbb .ValidateWithPath (_g .Sprintf ("\u0025s\u002fS\u0074\u0079\u006c\u0065\u0053\u0065\u0074\u005b\u0025\u0064\u005d",path ,_gbdg ));
_ccca !=nil {return _ccca ;};};return nil ;};func (_acecf *EG_FillProperties )MarshalXML (e *_fd .Encoder ,start _fd .StartElement )error {_acecf .FillPropertiesChoice .MarshalXML (e ,_fd .StartElement {});return nil ;};func (_dccf ST_LightRigType )MarshalXMLAttr (name _fd .Name )(_fd .Attr ,error ){_cgdce :=_fd .Attr {};
_cgdce .Name =name ;switch _dccf {case ST_LightRigTypeUnset :_cgdce .Value ="";case ST_LightRigTypeLegacyFlat1 :_cgdce .Value ="l\u0065\u0067\u0061\u0063\u0079\u0046\u006c\u0061\u0074\u0031";case ST_LightRigTypeLegacyFlat2 :_cgdce .Value ="l\u0065\u0067\u0061\u0063\u0079\u0046\u006c\u0061\u0074\u0032";
case ST_LightRigTypeLegacyFlat3 :_cgdce .Value ="l\u0065\u0067\u0061\u0063\u0079\u0046\u006c\u0061\u0074\u0033";case ST_LightRigTypeLegacyFlat4 :_cgdce .Value ="l\u0065\u0067\u0061\u0063\u0079\u0046\u006c\u0061\u0074\u0034";case ST_LightRigTypeLegacyNormal1 :_cgdce .Value ="\u006c\u0065\u0067\u0061\u0063\u0079\u004e\u006f\u0072\u006d\u0061\u006c\u0031";
case ST_LightRigTypeLegacyNormal2 :_cgdce .Value ="\u006c\u0065\u0067\u0061\u0063\u0079\u004e\u006f\u0072\u006d\u0061\u006c\u0032";case ST_LightRigTypeLegacyNormal3 :_cgdce .Value ="\u006c\u0065\u0067\u0061\u0063\u0079\u004e\u006f\u0072\u006d\u0061\u006c\u0033";
case ST_LightRigTypeLegacyNormal4 :_cgdce .Value ="\u006c\u0065\u0067\u0061\u0063\u0079\u004e\u006f\u0072\u006d\u0061\u006c\u0034";case ST_LightRigTypeLegacyHarsh1 :_cgdce .Value ="\u006c\u0065\u0067a\u0063\u0079\u0048\u0061\u0072\u0073\u0068\u0031";case ST_LightRigTypeLegacyHarsh2 :_cgdce .Value ="\u006c\u0065\u0067a\u0063\u0079\u0048\u0061\u0072\u0073\u0068\u0032";
case ST_LightRigTypeLegacyHarsh3 :_cgdce .Value ="\u006c\u0065\u0067a\u0063\u0079\u0048\u0061\u0072\u0073\u0068\u0033";case ST_LightRigTypeLegacyHarsh4 :_cgdce .Value ="\u006c\u0065\u0067a\u0063\u0079\u0048\u0061\u0072\u0073\u0068\u0034";case ST_LightRigTypeThreePt :_cgdce .Value ="\u0074h\u0072\u0065\u0065\u0050\u0074";
case ST_LightRigTypeBalanced :_cgdce .Value ="\u0062\u0061\u006c\u0061\u006e\u0063\u0065\u0064";case ST_LightRigTypeSoft :_cgdce .Value ="\u0073\u006f\u0066\u0074";case ST_LightRigTypeHarsh :_cgdce .Value ="\u0068\u0061\u0072s\u0068";case ST_LightRigTypeFlood :_cgdce .Value ="\u0066\u006c\u006fo\u0064";
case ST_LightRigTypeContrasting :_cgdce .Value ="c\u006f\u006e\u0074\u0072\u0061\u0073\u0074\u0069\u006e\u0067";case ST_LightRigTypeMorning :_cgdce .Value ="\u006do\u0072\u006e\u0069\u006e\u0067";case ST_LightRigTypeSunrise :_cgdce .Value ="\u0073u\u006e\u0072\u0069\u0073\u0065";
case ST_LightRigTypeSunset :_cgdce .Value ="\u0073\u0075\u006e\u0073\u0065\u0074";case ST_LightRigTypeChilly :_cgdce .Value ="\u0063\u0068\u0069\u006c\u006c\u0079";case ST_LightRigTypeFreezing :_cgdce .Value ="\u0066\u0072\u0065\u0065\u007a\u0069\u006e\u0067";
case ST_LightRigTypeFlat :_cgdce .Value ="\u0066\u006c\u0061\u0074";case ST_LightRigTypeTwoPt :_cgdce .Value ="\u0074\u0077\u006fP\u0074";case ST_LightRigTypeGlow :_cgdce .Value ="\u0067\u006c\u006f\u0077";case ST_LightRigTypeBrightRoom :_cgdce .Value ="\u0062\u0072\u0069\u0067\u0068\u0074\u0052\u006f\u006f\u006d";
};return _cgdce ,nil ;};func (_bdfa *CT_Percentage )UnmarshalXML (d *_fd .Decoder ,start _fd .StartElement )error {for _ ,_cdac :=range start .Attr {if _cdac .Name .Local =="\u0076\u0061\u006c"{_cfeg ,_dcg :=ParseUnionST_Percentage (_cdac .Value );if _dcg !=nil {return _dcg ;
};_bdfa .ValAttr =_cfeg ;continue ;};};for {_ffc ,_cbff :=d .Token ();if _cbff !=nil {return _g .Errorf ("\u0070a\u0072\u0073\u0069\u006eg\u0020\u0043\u0054\u005f\u0050e\u0072c\u0065n\u0074\u0061\u0067\u0065\u003a\u0020\u0025s",_cbff );};if _cag ,_aaeg :=_ffc .(_fd .EndElement );
_aaeg &&_cag .Name ==start .Name {break ;};};return nil ;};func (_acde ST_LightRigType )MarshalXML (e *_fd .Encoder ,start _fd .StartElement )error {return e .EncodeElement (_acde .String (),start );};func (_gced *ST_CompoundLine )UnmarshalXMLAttr (attr _fd .Attr )error {switch attr .Value {case "":*_gced =0;
case "\u0073\u006e\u0067":*_gced =1;case "\u0064\u0062\u006c":*_gced =2;case "\u0074h\u0069\u0063\u006b\u0054\u0068\u0069n":*_gced =3;case "\u0074h\u0069\u006e\u0054\u0068\u0069\u0063k":*_gced =4;case "\u0074\u0072\u0069":*_gced =5;};return nil ;};type ST_PresetCameraType byte ;


// ValidateWithPath validates the CT_FillTextEffect and its children, prefixing error messages with path
func (_ebc *CT_FillTextEffect )ValidateWithPath (path string )error {if _aec :=_ebc .FillPropertiesChoice .ValidateWithPath (path +"/\u0046\u0069\u006c\u006cPr\u006fp\u0065\u0072\u0074\u0069\u0065s\u0043\u0068\u006f\u0069\u0063\u0065");_aec !=nil {return _aec ;
};return nil ;};type CT_Glow struct{RadAttr *int64 ;SrgbClr *CT_SRgbColor ;SchemeClr *CT_SchemeColor ;};

// Validate validates the DiscardImageEditingData and its children
func (_dddg *DiscardImageEditingData )Validate ()error {return _dddg .ValidateWithPath ("\u0044\u0069\u0073ca\u0072\u0064\u0049\u006d\u0061\u0067\u0065\u0045\u0064\u0069\u0074\u0069\u006e\u0067\u0044\u0061\u0074\u0061");};func NewEntityPicker ()*EntityPicker {_cggge :=&EntityPicker {};
_cggge .CT_Empty =*_fe .NewCT_Empty ();return _cggge ;};func NewCT_SdtCheckboxSymbol ()*CT_SdtCheckboxSymbol {_face :=&CT_SdtCheckboxSymbol {};return _face };

// Validate validates the EG_LineJoinProperties and its children
func (_bbee *EG_LineJoinProperties )Validate ()error {return _bbee .ValidateWithPath ("E\u0047\u005f\u004c\u0069ne\u004ao\u0069\u006e\u0050\u0072\u006fp\u0065\u0072\u0074\u0069\u0065\u0073");};func (_dfgd *ST_BevelPresetType )UnmarshalXMLAttr (attr _fd .Attr )error {switch attr .Value {case "":*_dfgd =0;
case "\u0072\u0065\u006ca\u0078\u0065\u0064\u0049\u006e\u0073\u0065\u0074":*_dfgd =1;case "\u0063\u0069\u0072\u0063\u006c\u0065":*_dfgd =2;case "\u0073\u006c\u006fp\u0065":*_dfgd =3;case "\u0063\u0072\u006fs\u0073":*_dfgd =4;case "\u0061\u006e\u0067l\u0065":*_dfgd =5;
case "\u0073o\u0066\u0074\u0052\u006f\u0075\u006ed":*_dfgd =6;case "\u0063\u006f\u006e\u0076\u0065\u0078":*_dfgd =7;case "\u0063o\u006f\u006c\u0053\u006c\u0061\u006et":*_dfgd =8;case "\u0064\u0069\u0076o\u0074":*_dfgd =9;case "\u0072\u0069\u0062\u006c\u0065\u0074":*_dfgd =10;
case "\u0068\u0061\u0072\u0064\u0045\u0064\u0067\u0065":*_dfgd =11;case "\u0061r\u0074\u0044\u0065\u0063\u006f":*_dfgd =12;};return nil ;};func (_b *AG_Parids )MarshalXML (e *_fd .Encoder ,start _fd .StartElement )error {if _b .ParaIdAttr !=nil {start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0077\u006f\u0072\u003a\u0070\u0061\u0072\u0061\u0049\u0064"},Value :_g .Sprintf ("\u0025\u0076",*_b .ParaIdAttr )});
};if _b .TextIdAttr !=nil {start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0077\u006f\u0072\u003a\u0074\u0065\u0078\u0074\u0049\u0064"},Value :_g .Sprintf ("\u0025\u0076",*_b .TextIdAttr )});};start .Name .Local ="\u0077\u006f\u0072\u003a\u0041\u0047\u005f\u0050\u0061\u0072\u0069\u0064\u0073";
return nil ;};type CT_Camera struct{PrstAttr ST_PresetCameraType ;};func (_acd *CT_LightRig )UnmarshalXML (d *_fd .Decoder ,start _fd .StartElement )error {_acd .RigAttr =ST_LightRigType (1);_acd .DirAttr =ST_LightRigDirection (1);for _ ,_bag :=range start .Attr {if _bag .Name .Local =="\u0072\u0069\u0067"{_acd .RigAttr .UnmarshalXMLAttr (_bag );
continue ;};if _bag .Name .Local =="\u0064\u0069\u0072"{_acd .DirAttr .UnmarshalXMLAttr (_bag );continue ;};};_afb :for {_ebd ,_fbb :=d .Token ();if _fbb !=nil {return _fbb ;};switch _bbac :=_ebd .(type ){case _fd .StartElement :switch _bbac .Name {case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u0072\u006f\u0074"}:_acd .Rot =NewCT_SphereCoords ();
if _dgd :=d .DecodeElement (_acd .Rot ,&_bbac );_dgd !=nil {return _dgd ;};default:_c .Log .Debug ("\u0073\u006bi\u0070\u0070\u0069\u006e\u0067\u0020\u0075\u006e\u0073\u0075\u0070\u0070\u006f\u0072\u0074\u0065\u0064\u0020\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0043\u0054\u005f\u004c\u0069\u0067\u0068\u0074\u0052\u0069\u0067\u0020\u0025\u0076",_bbac .Name );
if _fed :=d .Skip ();_fed !=nil {return _fed ;};};case _fd .EndElement :break _afb ;case _fd .CharData :};};return nil ;};type CT_SchemeColor struct{ValAttr ST_SchemeColorVal ;EG_ColorTransform []*EG_ColorTransform ;};const (ST_OnOffUnset ST_OnOff =0;ST_OnOffTrue ST_OnOff =1;
ST_OnOffFalse ST_OnOff =2;ST_OnOff0 ST_OnOff =3;ST_OnOff1 ST_OnOff =4;);

// ValidateWithPath validates the ConflictMode and its children, prefixing error messages with path
func (_dfdg *ConflictMode )ValidateWithPath (path string )error {if _adgc :=_dfdg .CT_OnOff .ValidateWithPath (path );_adgc !=nil {return _adgc ;};return nil ;};func (_agbfg ST_Ligatures )MarshalXML (e *_fd .Encoder ,start _fd .StartElement )error {return e .EncodeElement (_agbfg .String (),start );
};func (_eeaa *CT_SphereCoords )MarshalXML (e *_fd .Encoder ,start _fd .StartElement )error {start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0077o\u0072\u003a\u006c\u0061\u0074"},Value :_g .Sprintf ("\u0025\u0076",_eeaa .LatAttr )});
start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0077o\u0072\u003a\u006c\u006f\u006e"},Value :_g .Sprintf ("\u0025\u0076",_eeaa .LonAttr )});start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0077o\u0072\u003a\u0072\u0065\u0076"},Value :_g .Sprintf ("\u0025\u0076",_eeaa .RevAttr )});
e .EncodeToken (start );e .EncodeToken (_fd .EndElement {Name :start .Name });return nil ;};type CT_LightRig struct{RigAttr ST_LightRigType ;DirAttr ST_LightRigDirection ;Rot *CT_SphereCoords ;};

// ValidateWithPath validates the CT_Props3D and its children, prefixing error messages with path
func (_dff *CT_Props3D )ValidateWithPath (path string )error {if _dff .ExtrusionHAttr !=nil {if *_dff .ExtrusionHAttr < 0{return _g .Errorf ("\u0025\u0073\u002fm\u002e\u0045\u0078\u0074\u0072\u0075\u0073\u0069\u006f\u006e\u0048\u0041\u0074\u0074\u0072\u0020\u006d\u0075\u0073\u0074\u0020\u0062\u0065\u0020\u003e\u003d\u0020\u0030\u0020(\u0068\u0061\u0076\u0065\u0020\u0025\u0076\u0029",path ,*_dff .ExtrusionHAttr );
};if *_dff .ExtrusionHAttr > 27273042316900{return _g .Errorf ("\u0025\u0073\u002f\u006d\u002e\u0045\u0078t\u0072\u0075\u0073i\u006f\u006e\u0048A\u0074\u0074r\u0020\u006d\u0075\u0073\u0074\u0020b\u0065 <\u003d\u0020\u0032\u0037\u0032\u0037\u0033\u0030\u0034\u0032\u0033\u0031\u0036\u0039\u0030\u0030\u0020\u0028\u0068\u0061\u0076\u0065\u0020\u0025\u0076\u0029",path ,*_dff .ExtrusionHAttr );
};};if _dff .ContourWAttr !=nil {if *_dff .ContourWAttr < 0{return _g .Errorf ("%\u0073\u002f\u006d\u002e\u0043\u006f\u006e\u0074\u006f\u0075\u0072\u0057\u0041\u0074\u0074\u0072\u0020\u006du\u0073\u0074\u0020\u0062\u0065\u0020\u003e\u003d\u0020\u0030 (\u0068\u0061\u0076e\u0020%\u0076\u0029",path ,*_dff .ContourWAttr );
};if *_dff .ContourWAttr > 27273042316900{return _g .Errorf ("\u0025\u0073\u002f\u006d\u002e\u0043\u006f\u006e\u0074\u006f\u0075r\u0057\u0041\u0074\u0074\u0072\u0020\u006d\u0075s\u0074 \u0062\u0065\u0020\u003c\u003d\u0020\u0032\u0037\u0032\u0037\u0033\u0030\u0034\u0032\u0033\u0031\u0036\u00390\u0030\u0020\u0028\u0068\u0061\u0076\u0065\u0020\u0025\u0076\u0029",path ,*_dff .ContourWAttr );
};};if _add :=_dff .PrstMaterialAttr .ValidateWithPath (path +"\u002f\u0050\u0072\u0073\u0074\u004d\u0061\u0074\u0065\u0072\u0069\u0061l\u0041\u0074\u0074\u0072");_add !=nil {return _add ;};if _dff .BevelT !=nil {if _daf :=_dff .BevelT .ValidateWithPath (path +"\u002fB\u0065\u0076\u0065\u006c\u0054");
_daf !=nil {return _daf ;};};if _dff .BevelB !=nil {if _gac :=_dff .BevelB .ValidateWithPath (path +"\u002fB\u0065\u0076\u0065\u006c\u0042");_gac !=nil {return _gac ;};};if _dff .ExtrusionClr !=nil {if _cbc :=_dff .ExtrusionClr .ValidateWithPath (path +"\u002f\u0045\u0078\u0074\u0072\u0075\u0073\u0069\u006f\u006e\u0043\u006c\u0072");
_cbc !=nil {return _cbc ;};};if _dff .ContourClr !=nil {if _gabf :=_dff .ContourClr .ValidateWithPath (path +"/\u0043\u006f\u006e\u0074\u006f\u0075\u0072\u0043\u006c\u0072");_gabf !=nil {return _gabf ;};};return nil ;};func (_gef *CT_PathShadeProperties )MarshalXML (e *_fd .Encoder ,start _fd .StartElement )error {if _gef .PathAttr !=ST_PathShadeTypeUnset {_age ,_dgdc :=_gef .PathAttr .MarshalXMLAttr (_fd .Name {Local :"\u0077\u006f\u0072\u003a\u0070\u0061\u0074\u0068"});
if _dgdc !=nil {return _dgdc ;};start .Attr =append (start .Attr ,_age );};e .EncodeToken (start );if _gef .FillToRect !=nil {_fgdc :=_fd .StartElement {Name :_fd .Name {Local :"\u0077\u006f\u0072\u003a\u0066\u0069\u006c\u006c\u0054o\u0052\u0065\u0063\u0074"}};
e .EncodeElement (_gef .FillToRect ,_fgdc );};e .EncodeToken (_fd .EndElement {Name :start .Name });return nil ;};

// ValidateWithPath validates the CustomXmlConflictDelRangeEnd and its children, prefixing error messages with path
func (_ebbe *CustomXmlConflictDelRangeEnd )ValidateWithPath (path string )error {if _aefcg :=_ebbe .CT_Markup .ValidateWithPath (path );_aefcg !=nil {return _aefcg ;};return nil ;};func NewEG_ColorTransform ()*EG_ColorTransform {_fbeg :=&EG_ColorTransform {};
_fbeg .ColorTransformChoice =NewEG_ColorTransformChoice ();return _fbeg ;};type CT_OnOff struct{ValAttr ST_OnOff ;};func NewCT_FillTextEffect ()*CT_FillTextEffect {_gfb :=&CT_FillTextEffect {};_gfb .FillPropertiesChoice =NewEG_FillPropertiesChoice ();return _gfb ;
};func (_edd *CT_SchemeColor )MarshalXML (e *_fd .Encoder ,start _fd .StartElement )error {_faf ,_cdgb :=_edd .ValAttr .MarshalXMLAttr (_fd .Name {Local :"\u0077o\u0072\u003a\u0076\u0061\u006c"});if _cdgb !=nil {return _cdgb ;};start .Attr =append (start .Attr ,_faf );
e .EncodeToken (start );if _edd .EG_ColorTransform !=nil {for _ ,_cage :=range _edd .EG_ColorTransform {_cage .MarshalXML (e ,_fd .StartElement {});};};e .EncodeToken (_fd .EndElement {Name :start .Name });return nil ;};func (_egce *CT_StylisticSets )UnmarshalXML (d *_fd .Decoder ,start _fd .StartElement )error {_efe :for {_afd ,_feda :=d .Token ();
if _feda !=nil {return _feda ;};switch _fcg :=_afd .(type ){case _fd .StartElement :switch _fcg .Name {case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u0073\u0074\u0079\u006c\u0065\u0053\u0065\u0074"}:_cfcf :=NewCT_StyleSet ();
if _cegg :=d .DecodeElement (_cfcf ,&_fcg );_cegg !=nil {return _cegg ;};_egce .StyleSet =append (_egce .StyleSet ,_cfcf );default:_c .Log .Debug ("\u0073\u006b\u0069\u0070\u0070i\u006e\u0067\u0020\u0075\u006e\u0073\u0075\u0070\u0070\u006f\u0072\u0074\u0065d\u0020\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0043\u0054\u005f\u0053\u0074\u0079\u006c\u0069\u0073\u0074\u0069\u0063\u0053\u0065\u0074\u0073\u0020\u0025v",_fcg .Name );
if _aagc :=d .Skip ();_aagc !=nil {return _aagc ;};};case _fd .EndElement :break _efe ;case _fd .CharData :};};return nil ;};func (_gfba ST_PenAlignment )MarshalXMLAttr (name _fd .Name )(_fd .Attr ,error ){_cafbe :=_fd .Attr {};_cafbe .Name =name ;switch _gfba {case ST_PenAlignmentUnset :_cafbe .Value ="";
case ST_PenAlignmentCtr :_cafbe .Value ="\u0063\u0074\u0072";case ST_PenAlignmentIn :_cafbe .Value ="\u0069\u006e";};return _cafbe ,nil ;};func (_bgfa *EG_ConflictsChoice )UnmarshalXML (d *_fd .Decoder ,start _fd .StartElement )error {_gdbc :=start ;switch start .Name {case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"c\u006f\u006e\u0066\u006c\u0069\u0063\u0074\u0049\u006e\u0073"}:_bgfa .ConflictIns =_fe .NewCT_TrackChange ();
if _degd :=d .DecodeElement (_bgfa .ConflictIns ,&_gdbc );_degd !=nil {return _degd ;};case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"c\u006f\u006e\u0066\u006c\u0069\u0063\u0074\u0044\u0065\u006c"}:_bgfa .ConflictDel =_fe .NewCT_TrackChange ();
if _dfafbe :=d .DecodeElement (_bgfa .ConflictDel ,&_gdbc );_dfafbe !=nil {return _dfafbe ;};default:_c .Log .Debug ("\u0073\u006b\u0069\u0070\u0070\u0069\u006e\u0067\u0020\u0075\u006es\u0075\u0070\u0070\u006f\u0072\u0074\u0065\u0064 \u0065l\u0065\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0045\u0047\u005f\u0043\u006f\u006e\u0066\u006c\u0069c\u0074\u0073\u0043\u0068\u006f\u0069\u0063\u0065\u0020\u0025\u0076",_gdbc .Name );
if _agd :=d .Skip ();_agd !=nil {return _agd ;};};return nil ;};func (_dgc *CT_NumSpacing )UnmarshalXML (d *_fd .Decoder ,start _fd .StartElement )error {_dgc .ValAttr =ST_NumSpacing (1);for _ ,_dfa :=range start .Attr {if _dfa .Name .Local =="\u0076\u0061\u006c"{_dgc .ValAttr .UnmarshalXMLAttr (_dfa );
continue ;};};for {_cfcc ,_fecc :=d .Token ();if _fecc !=nil {return _g .Errorf ("\u0070a\u0072\u0073\u0069\u006eg\u0020\u0043\u0054\u005f\u004eu\u006dS\u0070a\u0063\u0069\u006e\u0067\u003a\u0020\u0025s",_fecc );};if _bgag ,_dbbf :=_cfcc .(_fd .EndElement );
_dbbf &&_bgag .Name ==start .Name {break ;};};return nil ;};

// ValidateWithPath validates the EG_LineJoinProperties and its children, prefixing error messages with path
func (_ceb *EG_LineJoinProperties )ValidateWithPath (path string )error {if _eadfe :=_ceb .LineJoinPropertiesChoice .ValidateWithPath (path +"\u002fL\u0069\u006e\u0065\u004ao\u0069\u006e\u0050\u0072\u006fp\u0065r\u0074i\u0065\u0073\u0043\u0068\u006f\u0069\u0063e");
_eadfe !=nil {return _eadfe ;};return nil ;};func (_dbbd *ST_NumSpacing )UnmarshalXMLAttr (attr _fd .Attr )error {switch attr .Value {case "":*_dbbd =0;case "\u0064e\u0066\u0061\u0075\u006c\u0074":*_dbbd =1;case "\u0070\u0072\u006fp\u006f\u0072\u0074\u0069\u006f\u006e\u0061\u006c":*_dbbd =2;
case "\u0074a\u0062\u0075\u006c\u0061\u0072":*_dbbd =3;};return nil ;};func (_begd *EG_Conflicts )MarshalXML (e *_fd .Encoder ,start _fd .StartElement )error {start .Name .Local ="\u0077\u006fr\u003a\u0045\u0047_\u0043\u006f\u006e\u0066\u006c\u0069\u0063\u0074\u0073";
_begd .ConflictsChoice .MarshalXML (e ,_fd .StartElement {});return nil ;};func (_dde *CustomXmlConflictDelRangeEnd )UnmarshalXML (d *_fd .Decoder ,start _fd .StartElement )error {_dde .CT_Markup =*_fe .NewCT_Markup ();for {_faeg ,_gdbe :=d .Token ();if _gdbe !=nil {return _g .Errorf ("p\u0061\u0072\u0073\u0069\u006e\u0067\u0020\u0043\u0075\u0073\u0074\u006f\u006d\u0058\u006d\u006c\u0043\u006fn\u0066\u006c\u0069\u0063\u0074\u0044\u0065\u006c\u0052\u0061ng\u0065\u0045\u006ed\u003a \u0025\u0073",_gdbe );
};if _fcec ,_bfba :=_faeg .(_fd .EndElement );_bfba &&_fcec .Name ==start .Name {break ;};};return nil ;};func (_gddg ST_NumForm )MarshalXMLAttr (name _fd .Name )(_fd .Attr ,error ){_afef :=_fd .Attr {};_afef .Name =name ;switch _gddg {case ST_NumFormUnset :_afef .Value ="";
case ST_NumFormDefault :_afef .Value ="\u0064e\u0066\u0061\u0075\u006c\u0074";case ST_NumFormLining :_afef .Value ="\u006c\u0069\u006e\u0069\u006e\u0067";case ST_NumFormOldStyle :_afef .Value ="\u006f\u006c\u0064\u0053\u0074\u0079\u006c\u0065";};return _afef ,nil ;
};func (_bfcf *DocId )UnmarshalXML (d *_fd .Decoder ,start _fd .StartElement )error {_bfcf .CT_LongHexNumber =*NewCT_LongHexNumber ();for _ ,_egcb :=range start .Attr {if _egcb .Name .Local =="\u0076\u0061\u006c"{_ecag :=_egcb .Value ;_bfcf .ValAttr =_ecag ;
continue ;};};for {_agc ,_dfag :=d .Token ();if _dfag !=nil {return _g .Errorf ("\u0070\u0061\u0072\u0073\u0069\u006e\u0067\u0020\u0044\u006f\u0063\u0049d\u003a\u0020\u0025\u0073",_dfag );};if _gaca ,_faee :=_agc .(_fd .EndElement );_faee &&_gaca .Name ==start .Name {break ;
};};return nil ;};func (_gfgd ST_RectAlignment )Validate ()error {return _gfgd .ValidateWithPath ("")};

// ValidateWithPath validates the CT_DefaultImageDpi and its children, prefixing error messages with path
func (_eb *CT_DefaultImageDpi )ValidateWithPath (path string )error {return nil };type EG_Conflicts struct{ConflictsChoice *EG_ConflictsChoice ;};func (_dcdag *EntityPicker )MarshalXML (e *_fd .Encoder ,start _fd .StartElement )error {start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0078\u006d\u006cn\u0073"},Value :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c"});
start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0061"},Value :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065m\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006cf\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077\u0069\u006e\u0067m\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u006d\u0061\u0069\u006e"});
start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0073"},Value :"\u0068\u0074\u0074\u0070\u003a/\u002f\u0073\u0063\u0068\u0065m\u0061s\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067/\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0068\u0061\u0072e\u0064\u0054\u0079\u0070\u0065\u0073"});
start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0077"},Value :"ht\u0074\u0070:\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073.\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0077\u006f\u0072\u0064\u0070\u0072\u006f\u0063\u0065s\u0073i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u00306\u002fm\u0061\u0069n"});
start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0077\u006fr"},Value :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c"});
start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0078\u006dl"},Value :"\u0068\u0074tp\u003a\u002f\u002fw\u0077\u0077\u002e\u00773.o\u0072g/\u0058\u004d\u004c\u002f\u0031\u0039\u00398/\u006e\u0061\u006d\u0065\u0073\u0070\u0061c\u0065"});
start .Name .Local ="\u0077\u006fr\u003a\u0065\u006et\u0069\u0074\u0079\u0050\u0069\u0063\u006b\u0065\u0072";return _dcdag .CT_Empty .MarshalXML (e ,start );};func (_edc *CT_StylisticSets )MarshalXML (e *_fd .Encoder ,start _fd .StartElement )error {e .EncodeToken (start );
if _edc .StyleSet !=nil {_afc :=_fd .StartElement {Name :_fd .Name {Local :"\u0077\u006f\u0072:\u0073\u0074\u0079\u006c\u0065\u0053\u0065\u0074"}};for _ ,_cfdgd :=range _edc .StyleSet {e .EncodeElement (_cfdgd ,_afc );};};e .EncodeToken (_fd .EndElement {Name :start .Name });
return nil ;};func NewCT_PathShadeProperties ()*CT_PathShadeProperties {_geg :=&CT_PathShadeProperties {};return _geg ;};func (_abaf *EG_LineDashProperties )UnmarshalXML (d *_fd .Decoder ,start _fd .StartElement )error {_dccb :for {_ggee ,_cafa :=d .Token ();
if _cafa !=nil {return _cafa ;};switch _ecaf :=_ggee .(type ){case _fd .StartElement :switch _ecaf .Name {case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u0070\u0072\u0073\u0074\u0044\u0061\u0073\u0068"}:_abaf .PrstDash =NewCT_PresetLineDashProperties ();
if _dcdb :=d .DecodeElement (_abaf .PrstDash ,&_ecaf );_dcdb !=nil {return _dcdb ;};default:_c .Log .Debug ("\u0073\u006b\u0069\u0070\u0070\u0069n\u0067\u0020\u0075n\u0073\u0075\u0070p\u006f\u0072\u0074\u0065\u0064\u0020\u0065\u006c\u0065\u006de\u006e\u0074\u0020\u006f\u006e E\u0047\u005f\u004c\u0069\u006e\u0065\u0044\u0061\u0073\u0068\u0050\u0072\u006f\u0070\u0065\u0072\u0074\u0069\u0065\u0073\u0020\u0025\u0076",_ecaf .Name );
if _cbag :=d .Skip ();_cbag !=nil {return _cbag ;};};case _fd .EndElement :break _dccb ;case _fd .CharData :};};return nil ;};func (_gbbc *EG_ColorTransformChoice )UnmarshalXML (d *_fd .Decoder ,start _fd .StartElement )error {_bdcd :=start ;switch start .Name {case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u0074\u0069\u006e\u0074"}:_gbbc .Tint =NewCT_PositiveFixedPercentage ();
if _beag :=d .DecodeElement (_gbbc .Tint ,&_bdcd );_beag !=nil {return _beag ;};case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u0073\u0068\u0061d\u0065"}:_gbbc .Shade =NewCT_PositiveFixedPercentage ();
if _acad :=d .DecodeElement (_gbbc .Shade ,&_bdcd );_acad !=nil {return _acad ;};case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u0061\u006c\u0070h\u0061"}:_gbbc .Alpha =NewCT_PositiveFixedPercentage ();
if _fffd :=d .DecodeElement (_gbbc .Alpha ,&_bdcd );_fffd !=nil {return _fffd ;};case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u0068\u0075\u0065\u004d\u006f\u0064"}:_gbbc .HueMod =NewCT_PositivePercentage ();
if _eeac :=d .DecodeElement (_gbbc .HueMod ,&_bdcd );_eeac !=nil {return _eeac ;};case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u0073\u0061\u0074"}:_gbbc .Sat =NewCT_Percentage ();
if _gcaa :=d .DecodeElement (_gbbc .Sat ,&_bdcd );_gcaa !=nil {return _gcaa ;};case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u0073\u0061\u0074\u004f\u0066\u0066"}:_gbbc .SatOff =NewCT_Percentage ();
if _cggg :=d .DecodeElement (_gbbc .SatOff ,&_bdcd );_cggg !=nil {return _cggg ;};case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u0073\u0061\u0074\u004d\u006f\u0064"}:_gbbc .SatMod =NewCT_Percentage ();
if _fdgb :=d .DecodeElement (_gbbc .SatMod ,&_bdcd );_fdgb !=nil {return _fdgb ;};case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u006c\u0075\u006d"}:_gbbc .Lum =NewCT_Percentage ();
if _aeae :=d .DecodeElement (_gbbc .Lum ,&_bdcd );_aeae !=nil {return _aeae ;};case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u006c\u0075\u006d\u004f\u0066\u0066"}:_gbbc .LumOff =NewCT_Percentage ();
if _bafb :=d .DecodeElement (_gbbc .LumOff ,&_bdcd );_bafb !=nil {return _bafb ;};case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u006c\u0075\u006d\u004d\u006f\u0064"}:_gbbc .LumMod =NewCT_Percentage ();
if _aabd :=d .DecodeElement (_gbbc .LumMod ,&_bdcd );_aabd !=nil {return _aabd ;};default:_c .Log .Debug ("\u0073\u006b\u0069\u0070\u0070\u0069\u006e\u0067\u0020\u0075\u006e\u0073\u0075\u0070\u0070\u006f\u0072\u0074\u0065\u0064 \u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006fn\u0020\u0045\u0047\u005f\u0043\u006f\u006c\u006f\u0072\u0054\u0072\u0061\u006es\u0066\u006f\u0072\u006d\u0043\u0068o\u0069\u0063\u0065 \u0025\u0076",_bdcd .Name );
if _fffe :=d .Skip ();_fffe !=nil {return _fffe ;};};return nil ;};

// ValidateWithPath validates the CT_GradientStopList and its children, prefixing error messages with path
func (_aabc *CT_GradientStopList )ValidateWithPath (path string )error {for _fegb ,_acaa :=range _aabc .Gs {if _edb :=_acaa .ValidateWithPath (_g .Sprintf ("\u0025s\u002f\u0047\u0073\u005b\u0025\u0064]",path ,_fegb ));_edb !=nil {return _edb ;};};return nil ;
};

// Validate validates the CT_FillTextEffect and its children
func (_fgf *CT_FillTextEffect )Validate ()error {return _fgf .ValidateWithPath ("\u0043\u0054\u005f\u0046\u0069\u006c\u006c\u0054\u0065\u0078\u0074\u0045f\u0066\u0065\u0063\u0074");};func (_cddbb *ST_RectAlignment )UnmarshalXMLAttr (attr _fd .Attr )error {switch attr .Value {case "":*_cddbb =0;
case "\u006e\u006f\u006e\u0065":*_cddbb =1;case "\u0074\u006c":*_cddbb =2;case "\u0074":*_cddbb =3;case "\u0074\u0072":*_cddbb =4;case "\u006c":*_cddbb =5;case "\u0063\u0074\u0072":*_cddbb =6;case "\u0072":*_cddbb =7;case "\u0062\u006c":*_cddbb =8;case "\u0062":*_cddbb =9;
case "\u0062\u0072":*_cddbb =10;};return nil ;};func (_baef *ST_PathShadeType )UnmarshalXMLAttr (attr _fd .Attr )error {switch attr .Value {case "":*_baef =0;case "\u0073\u0068\u0061p\u0065":*_baef =1;case "\u0063\u0069\u0072\u0063\u006c\u0065":*_baef =2;
case "\u0072\u0065\u0063\u0074":*_baef =3;};return nil ;};func (_cffe *EG_ShadePropertiesChoice )UnmarshalXML (d *_fd .Decoder ,start _fd .StartElement )error {_dcgb :=start ;switch start .Name {case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u006c\u0069\u006e"}:_cffe .Lin =NewCT_LinearShadeProperties ();
if _efdc :=d .DecodeElement (_cffe .Lin ,&_dcgb );_efdc !=nil {return _efdc ;};case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u0070\u0061\u0074\u0068"}:_cffe .Path =NewCT_PathShadeProperties ();
if _dead :=d .DecodeElement (_cffe .Path ,&_dcgb );_dead !=nil {return _dead ;};default:_c .Log .Debug ("\u0073\u006b\u0069\u0070\u0070\u0069\u006e\u0067\u0020\u0075\u006e\u0073\u0075\u0070\u0070\u006f\u0072\u0074\u0065\u0064 \u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0045\u0047\u005f\u0053\u0068\u0061\u0064\u0065\u0050\u0072\u006f\u0070\u0065\u0072\u0074\u0069\u0065\u0073\u0043h\u006f\u0069\u0063\u0065\u0020%\u0076",_dcgb .Name );
if _aefde :=d .Skip ();_aefde !=nil {return _aefde ;};};return nil ;};func (_fdef *EG_RunLevelConflicts )UnmarshalXML (d *_fd .Decoder ,start _fd .StartElement )error {_cadf :for {_efae ,_gdcdb :=d .Token ();if _gdcdb !=nil {return _gdcdb ;};switch _bgfe :=_efae .(type ){case _fd .StartElement :switch _bgfe .Name {case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"c\u006f\u006e\u0066\u006c\u0069\u0063\u0074\u0049\u006e\u0073"}:_fdef .ConflictIns =_fe .NewCT_RunTrackChange ();
if _eggf :=d .DecodeElement (_fdef .ConflictIns ,&_bgfe );_eggf !=nil {return _eggf ;};case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"c\u006f\u006e\u0066\u006c\u0069\u0063\u0074\u0044\u0065\u006c"}:_fdef .ConflictDel =_fe .NewCT_RunTrackChange ();
if _dbcd :=d .DecodeElement (_fdef .ConflictDel ,&_bgfe );_dbcd !=nil {return _dbcd ;};default:_c .Log .Debug ("\u0073\u006b\u0069\u0070\u0070\u0069\u006eg\u0020\u0075\u006es\u0075\u0070\u0070o\u0072\u0074e\u0064\u0020\u0065\u006c\u0065\u006de\u006et \u006f\u006e\u0020\u0045\u0047\u005f\u0052\u0075\u006e\u004c\u0065\u0076\u0065\u006c\u0043\u006f\u006e\u0066\u006c\u0069\u0063\u0074\u0073\u0020\u0025\u0076",_bgfe .Name );
if _geeb :=d .Skip ();_geeb !=nil {return _geeb ;};};case _fd .EndElement :break _cadf ;case _fd .CharData :};};return nil ;};func (_efgag *ST_LineCap )UnmarshalXMLAttr (attr _fd .Attr )error {switch attr .Value {case "":*_efgag =0;case "\u0072\u006e\u0064":*_efgag =1;
case "\u0073\u0071":*_efgag =2;case "\u0066\u006c\u0061\u0074":*_efgag =3;};return nil ;};func NewConflictMode ()*ConflictMode {_cbfc :=&ConflictMode {};_cbfc .CT_OnOff =*NewCT_OnOff ();return _cbfc ;};func (_bdfee ST_OnOff )Validate ()error {return _bdfee .ValidateWithPath ("")};
func (_ddff ST_SchemeColorVal )MarshalXML (e *_fd .Encoder ,start _fd .StartElement )error {return e .EncodeElement (_ddff .String (),start );};func NewEG_ConflictsChoice ()*EG_ConflictsChoice {_cgeg :=&EG_ConflictsChoice {};return _cgeg };type CT_Percentage struct{ValAttr _abg .ST_Percentage ;
};type Any interface{MarshalXML (_feac *_fd .Encoder ,_acgb _fd .StartElement )error ;UnmarshalXML (_cfaa *_fd .Decoder ,_eeda _fd .StartElement )error ;};func (_dad *CT_GradientFillProperties )UnmarshalXML (d *_fd .Decoder ,start _fd .StartElement )error {_dad .ShadePropertiesChoice =NewEG_ShadePropertiesChoice ();
_beeg :for {_agb ,_baea :=d .Token ();if _baea !=nil {return _baea ;};switch _gga :=_agb .(type ){case _fd .StartElement :switch _gga .Name {case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u0067\u0073\u004cs\u0074"}:_dad .GsLst =NewCT_GradientStopList ();
if _bgdc :=d .DecodeElement (_dad .GsLst ,&_gga );_bgdc !=nil {return _bgdc ;};case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u006c\u0069\u006e"}:_dad .ShadePropertiesChoice =NewEG_ShadePropertiesChoice ();
if _fa :=d .DecodeElement (&_dad .ShadePropertiesChoice .Lin ,&_gga );_fa !=nil {return _fa ;};case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u0070\u0061\u0074\u0068"}:_dad .ShadePropertiesChoice =NewEG_ShadePropertiesChoice ();
if _gdaf :=d .DecodeElement (&_dad .ShadePropertiesChoice .Path ,&_gga );_gdaf !=nil {return _gdaf ;};default:_c .Log .Debug ("sk\u0069\u0070p\u0069\u006e\u0067\u0020\u0075\u006e\u0073\u0075\u0070p\u006f\u0072\u0074\u0065\u0064\u0020\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0043\u0054\u005f\u0047\u0072\u0061\u0064\u0069\u0065\u006e\u0074F\u0069l\u006c\u0050\u0072\u006f\u0070\u0065\u0072\u0074i\u0065s\u0020\u0025v",_gga .Name );
if _dcb :=d .Skip ();_dcb !=nil {return _dcb ;};};case _fd .EndElement :break _beeg ;case _fd .CharData :};};return nil ;};func (_dbed *EG_RPrTextEffects )UnmarshalXML (d *_fd .Decoder ,start _fd .StartElement )error {_cdeb :for {_fegba ,_fedc :=d .Token ();
if _fedc !=nil {return _fedc ;};switch _aaac :=_fegba .(type ){case _fd .StartElement :switch _aaac .Name {case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u0067\u006c\u006f\u0077"}:_dbed .Glow =NewCT_Glow ();
if _dgdf :=d .DecodeElement (_dbed .Glow ,&_aaac );_dgdf !=nil {return _dgdf ;};case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u0073\u0068\u0061\u0064\u006f\u0077"}:_dbed .Shadow =NewCT_Shadow ();
if _ggaf :=d .DecodeElement (_dbed .Shadow ,&_aaac );_ggaf !=nil {return _ggaf ;};case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u0072\u0065\u0066\u006c\u0065\u0063\u0074\u0069\u006f\u006e"}:_dbed .Reflection =NewCT_Reflection ();
if _fbgbe :=d .DecodeElement (_dbed .Reflection ,&_aaac );_fbgbe !=nil {return _fbgbe ;};case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"t\u0065\u0078\u0074\u004f\u0075\u0074\u006c\u0069\u006e\u0065"}:_dbed .TextOutline =NewCT_TextOutlineEffect ();
if _dbdf :=d .DecodeElement (_dbed .TextOutline ,&_aaac );_dbdf !=nil {return _dbdf ;};case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u0074\u0065\u0078\u0074\u0046\u0069\u006c\u006c"}:_dbed .TextFill =NewCT_FillTextEffect ();
if _dcfdf :=d .DecodeElement (_dbed .TextFill ,&_aaac );_dcfdf !=nil {return _dcfdf ;};case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u0073c\u0065\u006e\u0065\u0033\u0064"}:_dbed .Scene3d =NewCT_Scene3D ();
if _dcdg :=d .DecodeElement (_dbed .Scene3d ,&_aaac );_dcdg !=nil {return _dcdg ;};case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u0070r\u006f\u0070\u0073\u0033\u0064"}:_dbed .Props3d =NewCT_Props3D ();
if _ffeb :=d .DecodeElement (_dbed .Props3d ,&_aaac );_ffeb !=nil {return _ffeb ;};default:_c .Log .Debug ("\u0073\u006bi\u0070\u0070\u0069\u006e\u0067 \u0075\u006e\u0073\u0075\u0070p\u006f\u0072\u0074\u0065\u0064\u0020\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0045\u0047\u005f\u0052\u0050\u0072\u0054\u0065\u0078\u0074\u0045\u0066\u0066\u0065\u0063\u0074\u0073\u0020\u0025\u0076",_aaac .Name );
if _aafe :=d .Skip ();_aafe !=nil {return _aafe ;};};case _fd .EndElement :break _cdeb ;case _fd .CharData :};};return nil ;};type ST_Ligatures byte ;type EntityPicker struct{_fe .CT_Empty };func (_gfcg *ST_SchemeColorVal )UnmarshalXML (d *_fd .Decoder ,start _fd .StartElement )error {_bfcb ,_deeb :=d .Token ();
if _deeb !=nil {return _deeb ;};if _fcbe ,_beacc :=_bfcb .(_fd .EndElement );_beacc &&_fcbe .Name ==start .Name {*_gfcg =1;return nil ;};if _cddb ,_cfbcg :=_bfcb .(_fd .CharData );!_cfbcg {return _g .Errorf ("\u0065\u0078\u0070\u0065\u0063\u0074\u0065\u0064\u0020\u0063\u0068a\u0072\u0020\u0064\u0061\u0074\u0061\u002c\u0020\u0067\u006ft\u0020\u0025\u0054",_bfcb );
}else {switch string (_cddb ){case "":*_gfcg =0;case "\u0062\u0067\u0031":*_gfcg =1;case "\u0074\u0078\u0031":*_gfcg =2;case "\u0062\u0067\u0032":*_gfcg =3;case "\u0074\u0078\u0032":*_gfcg =4;case "\u0061c\u0063\u0065\u006e\u0074\u0031":*_gfcg =5;case "\u0061c\u0063\u0065\u006e\u0074\u0032":*_gfcg =6;
case "\u0061c\u0063\u0065\u006e\u0074\u0033":*_gfcg =7;case "\u0061c\u0063\u0065\u006e\u0074\u0034":*_gfcg =8;case "\u0061c\u0063\u0065\u006e\u0074\u0035":*_gfcg =9;case "\u0061c\u0063\u0065\u006e\u0074\u0036":*_gfcg =10;case "\u0068\u006c\u0069n\u006b":*_gfcg =11;
case "\u0066\u006f\u006c\u0048\u006c\u0069\u006e\u006b":*_gfcg =12;case "\u0064\u006b\u0031":*_gfcg =13;case "\u006c\u0074\u0031":*_gfcg =14;case "\u0064\u006b\u0032":*_gfcg =15;case "\u006c\u0074\u0032":*_gfcg =16;case "\u0070\u0068\u0043l\u0072":*_gfcg =17;
};};_bfcb ,_deeb =d .Token ();if _deeb !=nil {return _deeb ;};if _ecea ,_cebc :=_bfcb .(_fd .EndElement );_cebc &&_ecea .Name ==start .Name {return nil ;};return _g .Errorf ("\u0065\u0078\u0070\u0065c\u0074\u0065\u0064\u0020\u0065\u006e\u0064\u0020\u0065\u006ce\u006de\u006e\u0074\u002c\u0020\u0067\u006f\u0074 \u0025\u0076",_bfcb );
};func (_dabc *ST_RectAlignment )UnmarshalXML (d *_fd .Decoder ,start _fd .StartElement )error {_cec ,_cceb :=d .Token ();if _cceb !=nil {return _cceb ;};if _ecebe ,_bdd :=_cec .(_fd .EndElement );_bdd &&_ecebe .Name ==start .Name {*_dabc =1;return nil ;
};if _adfa ,_accec :=_cec .(_fd .CharData );!_accec {return _g .Errorf ("\u0065\u0078\u0070\u0065\u0063\u0074\u0065\u0064\u0020\u0063\u0068a\u0072\u0020\u0064\u0061\u0074\u0061\u002c\u0020\u0067\u006ft\u0020\u0025\u0054",_cec );}else {switch string (_adfa ){case "":*_dabc =0;
case "\u006e\u006f\u006e\u0065":*_dabc =1;case "\u0074\u006c":*_dabc =2;case "\u0074":*_dabc =3;case "\u0074\u0072":*_dabc =4;case "\u006c":*_dabc =5;case "\u0063\u0074\u0072":*_dabc =6;case "\u0072":*_dabc =7;case "\u0062\u006c":*_dabc =8;case "\u0062":*_dabc =9;
case "\u0062\u0072":*_dabc =10;};};_cec ,_cceb =d .Token ();if _cceb !=nil {return _cceb ;};if _bbce ,_ddcb :=_cec .(_fd .EndElement );_ddcb &&_bbce .Name ==start .Name {return nil ;};return _g .Errorf ("\u0065\u0078\u0070\u0065c\u0074\u0065\u0064\u0020\u0065\u006e\u0064\u0020\u0065\u006ce\u006de\u006e\u0074\u002c\u0020\u0067\u006f\u0074 \u0025\u0076",_cec );
};func (_efcda *ST_PresetCameraType )UnmarshalXML (d *_fd .Decoder ,start _fd .StartElement )error {_fegfg ,_cgdcg :=d .Token ();if _cgdcg !=nil {return _cgdcg ;};if _fefd ,_gace :=_fegfg .(_fd .EndElement );_gace &&_fefd .Name ==start .Name {*_efcda =1;
return nil ;};if _fdgdb ,_bebb :=_fegfg .(_fd .CharData );!_bebb {return _g .Errorf ("\u0065\u0078\u0070\u0065\u0063\u0074\u0065\u0064\u0020\u0063\u0068a\u0072\u0020\u0064\u0061\u0074\u0061\u002c\u0020\u0067\u006ft\u0020\u0025\u0054",_fegfg );}else {switch string (_fdgdb ){case "":*_efcda =0;
case "l\u0065g\u0061\u0063\u0079\u004f\u0062\u006c\u0069\u0071u\u0065\u0054\u006f\u0070Le\u0066\u0074":*_efcda =1;case "\u006c\u0065g\u0061\u0063\u0079O\u0062\u006c\u0069\u0071\u0075\u0065\u0054\u006f\u0070":*_efcda =2;case "l\u0065\u0067\u0061\u0063yO\u0062l\u0069\u0071\u0075\u0065\u0054o\u0070\u0052\u0069\u0067\u0068\u0074":*_efcda =3;
case "\u006c\u0065\u0067\u0061\u0063\u0079\u004f\u0062\u006c\u0069\u0071\u0075e\u004c\u0065\u0066\u0074":*_efcda =4;case "\u006ce\u0067a\u0063\u0079\u004f\u0062\u006ci\u0071\u0075e\u0046\u0072\u006f\u006e\u0074":*_efcda =5;case "\u006ce\u0067a\u0063\u0079\u004f\u0062\u006ci\u0071\u0075e\u0052\u0069\u0067\u0068\u0074":*_efcda =6;
case "\u006c\u0065\u0067ac\u0079\u004f\u0062\u006c\u0069\u0071\u0075\u0065\u0042\u006f\u0074\u0074\u006f\u006d\u004c\u0065\u0066\u0074":*_efcda =7;case "\u006c\u0065\u0067\u0061cy\u004f\u0062\u006c\u0069\u0071\u0075\u0065\u0042\u006f\u0074\u0074\u006f\u006d":*_efcda =8;
case "\u006ce\u0067\u0061\u0063\u0079\u004f\u0062\u006c\u0069\u0071\u0075\u0065B\u006f\u0074\u0074\u006f\u006d\u0052\u0069\u0067\u0068\u0074":*_efcda =9;case "\u006ce\u0067\u0061\u0063\u0079\u0050\u0065\u0072\u0073\u0070\u0065\u0063t\u0069\u0076\u0065\u0054\u006f\u0070\u004c\u0065\u0066\u0074":*_efcda =10;
case "l\u0065g\u0061\u0063\u0079\u0050\u0065\u0072\u0073\u0070e\u0063\u0074\u0069\u0076eT\u006f\u0070":*_efcda =11;case "\u006ce\u0067\u0061\u0063\u0079P\u0065\u0072\u0073\u0070\u0065c\u0074i\u0076e\u0054\u006f\u0070\u0052\u0069\u0067\u0068t":*_efcda =12;
case "l\u0065\u0067\u0061\u0063yP\u0065r\u0073\u0070\u0065\u0063\u0074i\u0076\u0065\u004c\u0065\u0066\u0074":*_efcda =13;case "\u006c\u0065\u0067\u0061cy\u0050\u0065\u0072\u0073\u0070\u0065\u0063\u0074\u0069\u0076\u0065\u0046\u0072\u006fn\u0074":*_efcda =14;
case "\u006c\u0065\u0067\u0061cy\u0050\u0065\u0072\u0073\u0070\u0065\u0063\u0074\u0069\u0076\u0065\u0052\u0069\u0067h\u0074":*_efcda =15;case "l\u0065\u0067\u0061\u0063\u0079\u0050e\u0072\u0073\u0070\u0065\u0063\u0074\u0069\u0076\u0065B\u006f\u0074\u0074o\u006dL\u0065\u0066\u0074":*_efcda =16;
case "\u006c\u0065\u0067ac\u0079\u0050\u0065\u0072\u0073\u0070\u0065\u0063\u0074\u0069\u0076\u0065\u0042\u006f\u0074\u0074\u006f\u006d":*_efcda =17;case "\u006c\u0065\u0067\u0061c\u0079\u0050\u0065\u0072\u0073\u0070\u0065\u0063\u0074\u0069v\u0065B\u006f\u0074\u0074\u006f\u006d\u0052\u0069g\u0068\u0074":*_efcda =18;
case "\u006f\u0072\u0074\u0068\u006f\u0067\u0072\u0061\u0070\u0068\u0069\u0063F\u0072\u006f\u006e\u0074":*_efcda =19;case "\u0069\u0073\u006f\u006d\u0065\u0074\u0072\u0069\u0063T\u006f\u0070\u0055\u0070":*_efcda =20;case "\u0069\u0073o\u006d\u0065\u0074r\u0069\u0063\u0054\u006f\u0070\u0044\u006f\u0077\u006e":*_efcda =21;
case "\u0069\u0073\u006f\u006d\u0065\u0074\u0072\u0069\u0063\u0042\u006f\u0074t\u006f\u006d\u0055\u0070":*_efcda =22;case "\u0069\u0073\u006f\u006det\u0072\u0069\u0063\u0042\u006f\u0074\u0074\u006f\u006d\u0044\u006f\u0077\u006e":*_efcda =23;case "\u0069s\u006fm\u0065\u0074\u0072\u0069\u0063\u004c\u0065\u0066\u0074\u0055\u0070":*_efcda =24;
case "\u0069\u0073\u006f\u006d\u0065\u0074\u0072\u0069\u0063\u004c\u0065\u0066t\u0044\u006f\u0077\u006e":*_efcda =25;case "\u0069\u0073o\u006d\u0065\u0074r\u0069\u0063\u0052\u0069\u0067\u0068\u0074\u0055\u0070":*_efcda =26;case "\u0069s\u006fm\u0065\u0074\u0072\u0069\u0063R\u0069\u0067h\u0074\u0044\u006f\u0077\u006e":*_efcda =27;
case "i\u0073\u006f\u006d\u0065tr\u0069c\u004f\u0066\u0066\u0041\u0078i\u0073\u0031\u004c\u0065\u0066\u0074":*_efcda =28;case "\u0069\u0073\u006f\u006det\u0072\u0069\u0063\u004f\u0066\u0066\u0041\u0078\u0069\u0073\u0031\u0052\u0069\u0067h\u0074":*_efcda =29;
case "i\u0073o\u006d\u0065\u0074\u0072\u0069\u0063\u004f\u0066f\u0041\u0078\u0069\u00731T\u006f\u0070":*_efcda =30;case "i\u0073\u006f\u006d\u0065tr\u0069c\u004f\u0066\u0066\u0041\u0078i\u0073\u0032\u004c\u0065\u0066\u0074":*_efcda =31;case "\u0069\u0073\u006f\u006det\u0072\u0069\u0063\u004f\u0066\u0066\u0041\u0078\u0069\u0073\u0032\u0052\u0069\u0067h\u0074":*_efcda =32;
case "i\u0073o\u006d\u0065\u0074\u0072\u0069\u0063\u004f\u0066f\u0041\u0078\u0069\u00732T\u006f\u0070":*_efcda =33;case "i\u0073\u006f\u006d\u0065tr\u0069c\u004f\u0066\u0066\u0041\u0078i\u0073\u0033\u004c\u0065\u0066\u0074":*_efcda =34;case "\u0069\u0073\u006f\u006det\u0072\u0069\u0063\u004f\u0066\u0066\u0041\u0078\u0069\u0073\u0033\u0052\u0069\u0067h\u0074":*_efcda =35;
case "\u0069\u0073\u006fme\u0074\u0072\u0069\u0063\u004f\u0066\u0066\u0041\u0078\u0069\u0073\u0033\u0042\u006f\u0074\u0074\u006f\u006d":*_efcda =36;case "i\u0073\u006f\u006d\u0065tr\u0069c\u004f\u0066\u0066\u0041\u0078i\u0073\u0034\u004c\u0065\u0066\u0074":*_efcda =37;
case "\u0069\u0073\u006f\u006det\u0072\u0069\u0063\u004f\u0066\u0066\u0041\u0078\u0069\u0073\u0034\u0052\u0069\u0067h\u0074":*_efcda =38;case "\u0069\u0073\u006fme\u0074\u0072\u0069\u0063\u004f\u0066\u0066\u0041\u0078\u0069\u0073\u0034\u0042\u006f\u0074\u0074\u006f\u006d":*_efcda =39;
case "\u006f\u0062\u006c\u0069\u0071\u0075\u0065\u0054\u006fp\u004c\u0065\u0066\u0074":*_efcda =40;case "\u006f\u0062\u006c\u0069\u0071\u0075\u0065\u0054\u006f\u0070":*_efcda =41;case "\u006fb\u006ci\u0071\u0075\u0065\u0054\u006f\u0070\u0052\u0069\u0067\u0068\u0074":*_efcda =42;
case "o\u0062\u006c\u0069\u0071\u0075\u0065\u004c\u0065\u0066\u0074":*_efcda =43;case "\u006f\u0062\u006ci\u0071\u0075\u0065\u0052\u0069\u0067\u0068\u0074":*_efcda =44;case "\u006f\u0062\u006c\u0069\u0071\u0075\u0065\u0042\u006f\u0074\u0074\u006fm\u004c\u0065\u0066\u0074":*_efcda =45;
case "\u006f\u0062\u006c\u0069\u0071\u0075\u0065\u0042\u006f\u0074\u0074\u006f\u006d":*_efcda =46;case "\u006fb\u006ci\u0071\u0075\u0065\u0042\u006ft\u0074\u006fm\u0052\u0069\u0067\u0068\u0074":*_efcda =47;case "\u0070\u0065r\u0073\u0070\u0065c\u0074\u0069\u0076\u0065\u0046\u0072\u006f\u006e\u0074":*_efcda =48;
case "\u0070e\u0072s\u0070\u0065\u0063\u0074\u0069\u0076\u0065\u004c\u0065\u0066\u0074":*_efcda =49;case "\u0070\u0065r\u0073\u0070\u0065c\u0074\u0069\u0076\u0065\u0052\u0069\u0067\u0068\u0074":*_efcda =50;case "\u0070\u0065r\u0073\u0070\u0065c\u0074\u0069\u0076\u0065\u0041\u0062\u006f\u0076\u0065":*_efcda =51;
case "\u0070\u0065r\u0073\u0070\u0065c\u0074\u0069\u0076\u0065\u0042\u0065\u006c\u006f\u0077":*_efcda =52;case "\u0070\u0065\u0072\u0073\u0070\u0065\u0063\u0074\u0069\u0076\u0065A\u0062\u006f\u0076\u0065\u004c\u0065\u0066\u0074\u0046\u0061c\u0069\u006e\u0067":*_efcda =53;
case "p\u0065\u0072\u0073\u0070\u0065\u0063t\u0069\u0076\u0065\u0041\u0062\u006f\u0076\u0065\u0052i\u0067\u0068\u0074F\u0061c\u0069\u006e\u0067":*_efcda =54;case "\u0070\u0065\u0072\u0073\u0070\u0065\u0063\u0074\u0069\u0076\u0065\u0043\u006f\u006e\u0074r\u0061s\u0074\u0069\u006e\u0067\u004c\u0065\u0066\u0074\u0046\u0061\u0063\u0069\u006e\u0067":*_efcda =55;
case "\u0070\u0065\u0072\u0073\u0070\u0065c\u0074\u0069\u0076\u0065\u0043\u006f\u006e\u0074\u0072\u0061\u0073\u0074\u0069n\u0067\u0052\u0069\u0067\u0068\u0074\u0046a\u0063\u0069\u006e\u0067":*_efcda =56;case "p\u0065\u0072\u0073\u0070\u0065\u0063t\u0069\u0076\u0065\u0048\u0065\u0072\u006f\u0069\u0063L\u0065\u0066\u0074F\u0061c\u0069\u006e\u0067":*_efcda =57;
case "\u0070\u0065\u0072\u0073p\u0065\u0063\u0074\u0069\u0076\u0065\u0048\u0065\u0072\u006fi\u0063R\u0069\u0067\u0068\u0074\u0046\u0061\u0063i\u006e\u0067":*_efcda =58;case "\u0070\u0065\u0072sp\u0065\u0063\u0074\u0069\u0076\u0065\u0048\u0065\u0072o\u0069c\u0045x\u0074r\u0065\u006d\u0065\u004c\u0065\u0066\u0074\u0046\u0061\u0063\u0069\u006e\u0067":*_efcda =59;
case "p\u0065\u0072\u0073\u0070\u0065\u0063t\u0069\u0076\u0065\u0048\u0065\u0072o\u0069\u0063\u0045\u0078\u0074\u0072\u0065m\u0065\u0052\u0069\u0067\u0068\u0074\u0046\u0061\u0063\u0069n\u0067":*_efcda =60;case "\u0070e\u0072s\u0070\u0065\u0063\u0074\u0069v\u0065\u0052e\u006c\u0061\u0078\u0065\u0064":*_efcda =61;
case "\u0070\u0065\u0072\u0073p\u0065\u0063\u0074\u0069\u0076\u0065\u0052\u0065\u006c\u0061x\u0065d\u004d\u006f\u0064\u0065\u0072\u0061\u0074e\u006c\u0079":*_efcda =62;};};_fegfg ,_cgdcg =d .Token ();if _cgdcg !=nil {return _cgdcg ;};if _geec ,_accg :=_fegfg .(_fd .EndElement );
_accg &&_geec .Name ==start .Name {return nil ;};return _g .Errorf ("\u0065\u0078\u0070\u0065c\u0074\u0065\u0064\u0020\u0065\u006e\u0064\u0020\u0065\u006ce\u006de\u006e\u0074\u002c\u0020\u0067\u006f\u0074 \u0025\u0076",_fegfg );};func (_gabfe *ST_LightRigType )UnmarshalXML (d *_fd .Decoder ,start _fd .StartElement )error {_gbaga ,_fdeec :=d .Token ();
if _fdeec !=nil {return _fdeec ;};if _fdead ,_dafd :=_gbaga .(_fd .EndElement );_dafd &&_fdead .Name ==start .Name {*_gabfe =1;return nil ;};if _ddea ,_efaad :=_gbaga .(_fd .CharData );!_efaad {return _g .Errorf ("\u0065\u0078\u0070\u0065\u0063\u0074\u0065\u0064\u0020\u0063\u0068a\u0072\u0020\u0064\u0061\u0074\u0061\u002c\u0020\u0067\u006ft\u0020\u0025\u0054",_gbaga );
}else {switch string (_ddea ){case "":*_gabfe =0;case "l\u0065\u0067\u0061\u0063\u0079\u0046\u006c\u0061\u0074\u0031":*_gabfe =1;case "l\u0065\u0067\u0061\u0063\u0079\u0046\u006c\u0061\u0074\u0032":*_gabfe =2;case "l\u0065\u0067\u0061\u0063\u0079\u0046\u006c\u0061\u0074\u0033":*_gabfe =3;
case "l\u0065\u0067\u0061\u0063\u0079\u0046\u006c\u0061\u0074\u0034":*_gabfe =4;case "\u006c\u0065\u0067\u0061\u0063\u0079\u004e\u006f\u0072\u006d\u0061\u006c\u0031":*_gabfe =5;case "\u006c\u0065\u0067\u0061\u0063\u0079\u004e\u006f\u0072\u006d\u0061\u006c\u0032":*_gabfe =6;
case "\u006c\u0065\u0067\u0061\u0063\u0079\u004e\u006f\u0072\u006d\u0061\u006c\u0033":*_gabfe =7;case "\u006c\u0065\u0067\u0061\u0063\u0079\u004e\u006f\u0072\u006d\u0061\u006c\u0034":*_gabfe =8;case "\u006c\u0065\u0067a\u0063\u0079\u0048\u0061\u0072\u0073\u0068\u0031":*_gabfe =9;
case "\u006c\u0065\u0067a\u0063\u0079\u0048\u0061\u0072\u0073\u0068\u0032":*_gabfe =10;case "\u006c\u0065\u0067a\u0063\u0079\u0048\u0061\u0072\u0073\u0068\u0033":*_gabfe =11;case "\u006c\u0065\u0067a\u0063\u0079\u0048\u0061\u0072\u0073\u0068\u0034":*_gabfe =12;
case "\u0074h\u0072\u0065\u0065\u0050\u0074":*_gabfe =13;case "\u0062\u0061\u006c\u0061\u006e\u0063\u0065\u0064":*_gabfe =14;case "\u0073\u006f\u0066\u0074":*_gabfe =15;case "\u0068\u0061\u0072s\u0068":*_gabfe =16;case "\u0066\u006c\u006fo\u0064":*_gabfe =17;
case "c\u006f\u006e\u0074\u0072\u0061\u0073\u0074\u0069\u006e\u0067":*_gabfe =18;case "\u006do\u0072\u006e\u0069\u006e\u0067":*_gabfe =19;case "\u0073u\u006e\u0072\u0069\u0073\u0065":*_gabfe =20;case "\u0073\u0075\u006e\u0073\u0065\u0074":*_gabfe =21;case "\u0063\u0068\u0069\u006c\u006c\u0079":*_gabfe =22;
case "\u0066\u0072\u0065\u0065\u007a\u0069\u006e\u0067":*_gabfe =23;case "\u0066\u006c\u0061\u0074":*_gabfe =24;case "\u0074\u0077\u006fP\u0074":*_gabfe =25;case "\u0067\u006c\u006f\u0077":*_gabfe =26;case "\u0062\u0072\u0069\u0067\u0068\u0074\u0052\u006f\u006f\u006d":*_gabfe =27;
};};_gbaga ,_fdeec =d .Token ();if _fdeec !=nil {return _fdeec ;};if _bfed ,_agae :=_gbaga .(_fd .EndElement );_agae &&_bfed .Name ==start .Name {return nil ;};return _g .Errorf ("\u0065\u0078\u0070\u0065c\u0074\u0065\u0064\u0020\u0065\u006e\u0064\u0020\u0065\u006ce\u006de\u006e\u0074\u002c\u0020\u0067\u006f\u0074 \u0025\u0076",_gbaga );
};type CT_Ligatures struct{ValAttr ST_Ligatures ;};func (_beca ST_NumForm )MarshalXML (e *_fd .Encoder ,start _fd .StartElement )error {return e .EncodeElement (_beca .String (),start );};func (_aag *CT_PathShadeProperties )UnmarshalXML (d *_fd .Decoder ,start _fd .StartElement )error {for _ ,_gde :=range start .Attr {if _gde .Name .Local =="\u0070\u0061\u0074\u0068"{_aag .PathAttr .UnmarshalXMLAttr (_gde );
continue ;};};_deb :for {_cgf ,_dgg :=d .Token ();if _dgg !=nil {return _dgg ;};switch _ddde :=_cgf .(type ){case _fd .StartElement :switch _ddde .Name {case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u0066\u0069\u006c\u006c\u0054\u006f\u0052\u0065\u0063\u0074"}:_aag .FillToRect =NewCT_RelativeRect ();
if _dac :=d .DecodeElement (_aag .FillToRect ,&_ddde );_dac !=nil {return _dac ;};default:_c .Log .Debug ("\u0073\u006b\u0069\u0070p\u0069\u006e\u0067\u0020\u0075\u006e\u0073\u0075\u0070p\u006f\u0072\u0074\u0065\u0064\u0020\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0043T\u005f\u0050\u0061\u0074\u0068S\u0068\u0061\u0064\u0065\u0050\u0072\u006f\u0070\u0065\u0072\u0074\u0069\u0065\u0073\u0020\u0025\u0076",_ddde .Name );
if _ggf :=d .Skip ();_ggf !=nil {return _ggf ;};};case _fd .EndElement :break _deb ;case _fd .CharData :};};return nil ;};

// Validate validates the EG_ColorChoice and its children
func (_gcg *EG_ColorChoice )Validate ()error {return _gcg .ValidateWithPath ("\u0045\u0047\u005f\u0043\u006f\u006c\u006f\u0072\u0043h\u006f\u0069\u0063\u0065");};func (_fbfb *CT_PositiveFixedPercentage )MarshalXML (e *_fd .Encoder ,start _fd .StartElement )error {start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0077o\u0072\u003a\u0076\u0061\u006c"},Value :_g .Sprintf ("\u0025\u0076",_fbfb .ValAttr )});
e .EncodeToken (start );e .EncodeToken (_fd .EndElement {Name :start .Name });return nil ;};

// ValidateWithPath validates the EG_ConflictsChoice and its children, prefixing error messages with path
func (_abcf *EG_ConflictsChoice )ValidateWithPath (path string )error {if _abcf .ConflictIns !=nil {if _dbae :=_abcf .ConflictIns .ValidateWithPath (path +"\u002f\u0043\u006fn\u0066\u006c\u0069\u0063\u0074\u0049\u006e\u0073");_dbae !=nil {return _dbae ;
};};if _abcf .ConflictDel !=nil {if _dgbg :=_abcf .ConflictDel .ValidateWithPath (path +"\u002f\u0043\u006fn\u0066\u006c\u0069\u0063\u0074\u0044\u0065\u006c");_dgbg !=nil {return _dgbg ;};};return nil ;};

// ValidateWithPath validates the CT_Bevel and its children, prefixing error messages with path
func (_gaa *CT_Bevel )ValidateWithPath (path string )error {if _gaa .WAttr !=nil {if *_gaa .WAttr < 0{return _g .Errorf ("\u0025\u0073\u002f\u006d\u002e\u0057A\u0074\u0074\u0072\u0020\u006d\u0075\u0073\u0074\u0020\u0062\u0065\u0020\u003e=\u0020\u0030\u0020\u0028\u0068\u0061\u0076e\u0020\u0025\u0076\u0029",path ,*_gaa .WAttr );
};if *_gaa .WAttr > 27273042316900{return _g .Errorf ("\u0025\u0073/\u006d\u002e\u0057\u0041\u0074\u0074\u0072\u0020\u006d\u0075\u0073\u0074\u0020\u0062\u0065\u0020\u003c\u003d\u0020\u0032\u0037\u0032\u0037\u0033\u0030\u0034\u0032\u0033\u0031\u0036\u0039\u0030\u0030\u0020\u0028\u0068\u0061\u0076\u0065\u0020\u0025\u0076\u0029",path ,*_gaa .WAttr );
};};if _gaa .HAttr !=nil {if *_gaa .HAttr < 0{return _g .Errorf ("\u0025\u0073\u002f\u006d\u002e\u0048A\u0074\u0074\u0072\u0020\u006d\u0075\u0073\u0074\u0020\u0062\u0065\u0020\u003e=\u0020\u0030\u0020\u0028\u0068\u0061\u0076e\u0020\u0025\u0076\u0029",path ,*_gaa .HAttr );
};if *_gaa .HAttr > 27273042316900{return _g .Errorf ("\u0025\u0073/\u006d\u002e\u0048\u0041\u0074\u0074\u0072\u0020\u006d\u0075\u0073\u0074\u0020\u0062\u0065\u0020\u003c\u003d\u0020\u0032\u0037\u0032\u0037\u0033\u0030\u0034\u0032\u0033\u0031\u0036\u0039\u0030\u0030\u0020\u0028\u0068\u0061\u0076\u0065\u0020\u0025\u0076\u0029",path ,*_gaa .HAttr );
};};if _cfc :=_gaa .PrstAttr .ValidateWithPath (path +"\u002fP\u0072\u0073\u0074\u0041\u0074\u0074r");_cfc !=nil {return _cfc ;};return nil ;};func (_cfccc *ST_PresetLineDashVal )UnmarshalXML (d *_fd .Decoder ,start _fd .StartElement )error {_ccbg ,_eadg :=d .Token ();
if _eadg !=nil {return _eadg ;};if _cfabb ,_ccag :=_ccbg .(_fd .EndElement );_ccag &&_cfabb .Name ==start .Name {*_cfccc =1;return nil ;};if _gade ,_afce :=_ccbg .(_fd .CharData );!_afce {return _g .Errorf ("\u0065\u0078\u0070\u0065\u0063\u0074\u0065\u0064\u0020\u0063\u0068a\u0072\u0020\u0064\u0061\u0074\u0061\u002c\u0020\u0067\u006ft\u0020\u0025\u0054",_ccbg );
}else {switch string (_gade ){case "":*_cfccc =0;case "\u0073\u006f\u006ci\u0064":*_cfccc =1;case "\u0064\u006f\u0074":*_cfccc =2;case "\u0073\u0079\u0073\u0044\u006f\u0074":*_cfccc =3;case "\u0064\u0061\u0073\u0068":*_cfccc =4;case "\u0073y\u0073\u0044\u0061\u0073\u0068":*_cfccc =5;
case "\u006c\u0067\u0044\u0061\u0073\u0068":*_cfccc =6;case "\u0064a\u0073\u0068\u0044\u006f\u0074":*_cfccc =7;case "\u0073\u0079\u0073\u0044\u0061\u0073\u0068\u0044\u006f\u0074":*_cfccc =8;case "\u006cg\u0044\u0061\u0073\u0068\u0044\u006ft":*_cfccc =9;
case "\u006c\u0067\u0044a\u0073\u0068\u0044\u006f\u0074\u0044\u006f\u0074":*_cfccc =10;case "\u0073\u0079\u0073\u0044\u0061\u0073\u0068\u0044\u006f\u0074\u0044\u006f\u0074":*_cfccc =11;};};_ccbg ,_eadg =d .Token ();if _eadg !=nil {return _eadg ;};if _gedf ,_cgfg :=_ccbg .(_fd .EndElement );
_cgfg &&_gedf .Name ==start .Name {return nil ;};return _g .Errorf ("\u0065\u0078\u0070\u0065c\u0074\u0065\u0064\u0020\u0065\u006e\u0064\u0020\u0065\u006ce\u006de\u006e\u0074\u002c\u0020\u0067\u006f\u0074 \u0025\u0076",_ccbg );};func (_efaa *EG_FillPropertiesChoice )MarshalXML (e *_fd .Encoder ,start _fd .StartElement )error {e .EncodeToken (start );
if _efaa .NoFill !=nil {_dfffe :=_fd .StartElement {Name :_fd .Name {Local :"\u0077\u006f\u0072\u003a\u006e\u006f\u0046\u0069\u006c\u006c"}};e .EncodeElement (_efaa .NoFill ,_dfffe );}else if _efaa .SolidFill !=nil {_gdbd :=_fd .StartElement {Name :_fd .Name {Local :"\u0077\u006f\u0072\u003a\u0073\u006f\u006c\u0069\u0064\u0046\u0069\u006c\u006c"}};
e .EncodeElement (_efaa .SolidFill ,_gdbd );}else if _efaa .GradFill !=nil {_dbbfd :=_fd .StartElement {Name :_fd .Name {Local :"\u0077\u006f\u0072:\u0067\u0072\u0061\u0064\u0046\u0069\u006c\u006c"}};e .EncodeElement (_efaa .GradFill ,_dbbfd );};e .EncodeToken (_fd .EndElement {Name :start .Name });
return nil ;};

// Validate validates the CT_SdtCheckboxSymbol and its children
func (_ebgd *CT_SdtCheckboxSymbol )Validate ()error {return _ebgd .ValidateWithPath ("C\u0054_\u0053\u0064\u0074\u0043\u0068\u0065\u0063\u006bb\u006f\u0078\u0053\u0079mb\u006f\u006c");};

// Validate validates the EG_LineJoinPropertiesChoice and its children
func (_feba *EG_LineJoinPropertiesChoice )Validate ()error {return _feba .ValidateWithPath ("E\u0047\u005f\u004c\u0069\u006e\u0065J\u006f\u0069\u006e\u0050\u0072\u006f\u0070\u0065\u0072t\u0069\u0065\u0073C\u0068o\u0069\u0063\u0065");};func (_ecb ST_NumSpacing )MarshalXMLAttr (name _fd .Name )(_fd .Attr ,error ){_dfbc :=_fd .Attr {};
_dfbc .Name =name ;switch _ecb {case ST_NumSpacingUnset :_dfbc .Value ="";case ST_NumSpacingDefault :_dfbc .Value ="\u0064e\u0066\u0061\u0075\u006c\u0074";case ST_NumSpacingProportional :_dfbc .Value ="\u0070\u0072\u006fp\u006f\u0072\u0074\u0069\u006f\u006e\u0061\u006c";
case ST_NumSpacingTabular :_dfbc .Value ="\u0074a\u0062\u0075\u006c\u0061\u0072";};return _dfbc ,nil ;};

// Validate validates the CT_SphereCoords and its children
func (_bbda *CT_SphereCoords )Validate ()error {return _bbda .ValidateWithPath ("\u0043T\u005fS\u0070\u0068\u0065\u0072\u0065\u0043\u006f\u006f\u0072\u0064\u0073");};

// ValidateWithPath validates the CT_Reflection and its children, prefixing error messages with path
func (_bcd *CT_Reflection )ValidateWithPath (path string )error {if _bcd .BlurRadAttr !=nil {if *_bcd .BlurRadAttr < 0{return _g .Errorf ("\u0025\u0073\u002f\u006d\u002e\u0042\u006c\u0075\u0072\u0052\u0061\u0064\u0041t\u0074\u0072\u0020\u006d\u0075\u0073t\u0020\u0062\u0065\u0020\u003e\u003d\u0020\u0030\u0020\u0028\u0068\u0061\u0076e\u0020\u0025\u0076\u0029",path ,*_bcd .BlurRadAttr );
};if *_bcd .BlurRadAttr > 27273042316900{return _g .Errorf ("\u0025\u0073/\u006d\u002e\u0042\u006c\u0075r\u0052\u0061\u0064\u0041\u0074t\u0072\u0020\u006d\u0075\u0073\u0074\u0020\u0062\u0065\u0020\u003c\u003d\u0020\u0032\u0037\u0032\u0037\u0033\u0030\u0034\u0032\u0033\u0031\u0036\u0039\u0030\u0030\u0020\u0028\u0068\u0061\u0076\u0065\u0020\u0025\u0076\u0029",path ,*_bcd .BlurRadAttr );
};};if _bcd .StAAttr !=nil {if _accd :=_bcd .StAAttr .ValidateWithPath (path +"\u002f\u0053\u0074\u0041\u0041\u0074\u0074\u0072");_accd !=nil {return _accd ;};};if _bcd .StPosAttr !=nil {if _bfga :=_bcd .StPosAttr .ValidateWithPath (path +"\u002f\u0053\u0074\u0050\u006f\u0073\u0041\u0074\u0074\u0072");
_bfga !=nil {return _bfga ;};};if _bcd .EndAAttr !=nil {if _ega :=_bcd .EndAAttr .ValidateWithPath (path +"\u002fE\u006e\u0064\u0041\u0041\u0074\u0074r");_ega !=nil {return _ega ;};};if _bcd .EndPosAttr !=nil {if _agbf :=_bcd .EndPosAttr .ValidateWithPath (path +"/\u0045\u006e\u0064\u0050\u006f\u0073\u0041\u0074\u0074\u0072");
_agbf !=nil {return _agbf ;};};if _bcd .DistAttr !=nil {if *_bcd .DistAttr < 0{return _g .Errorf ("\u0025\u0073/m\u002e\u0044\u0069s\u0074\u0041\u0074\u0074r m\u0075st\u0020\u0062\u0065\u0020\u003e\u003d\u00200 \u0028\u0068\u0061\u0076\u0065\u0020\u0025v\u0029",path ,*_bcd .DistAttr );
};if *_bcd .DistAttr > 27273042316900{return _g .Errorf ("\u0025\u0073\u002f\u006d\u002e\u0044i\u0073\u0074\u0041\u0074\u0074\u0072\u0020\u006d\u0075\u0073\u0074\u0020\u0062\u0065\u0020\u003c\u003d\u0020\u0032\u00372\u0037\u0033\u0030\u0034\u0032\u0033\u0031\u0036\u0039\u0030\u0030\u0020\u0028\u0068a\u0076e\u0020\u0025\u0076\u0029",path ,*_bcd .DistAttr );
};};if _bcd .DirAttr !=nil {if *_bcd .DirAttr < 0{return _g .Errorf ("%\u0073\u002f\u006d\u002e\u0044\u0069r\u0041\u0074\u0074\u0072\u0020\u006du\u0073\u0074\u0020\u0062\u0065\u0020\u003e=\u0020\u0030\u0020\u0028\u0068\u0061\u0076\u0065\u0020\u0025v\u0029",path ,*_bcd .DirAttr );
};if *_bcd .DirAttr >=21600000{return _g .Errorf ("\u0025\u0073\u002f\u006d\u002eD\u0069\u0072\u0041\u0074\u0074\u0072\u0020\u006d\u0075\u0073\u0074\u0020\u0062e\u0020\u003c\u0020\u0032\u0031\u0036\u0030\u0030\u0030\u0030\u0030\u0020\u0028\u0068\u0061\u0076\u0065\u0020\u0025\u0076\u0029",path ,*_bcd .DirAttr );
};};if _bcd .FadeDirAttr !=nil {if *_bcd .FadeDirAttr < 0{return _g .Errorf ("\u0025\u0073\u002f\u006d\u002e\u0046\u0061\u0064\u0065\u0044\u0069\u0072\u0041t\u0074\u0072\u0020\u006d\u0075\u0073t\u0020\u0062\u0065\u0020\u003e\u003d\u0020\u0030\u0020\u0028\u0068\u0061\u0076e\u0020\u0025\u0076\u0029",path ,*_bcd .FadeDirAttr );
};if *_bcd .FadeDirAttr >=21600000{return _g .Errorf ("\u0025s\u002f\u006d.\u0046\u0061\u0064e\u0044\u0069\u0072\u0041\u0074\u0074\u0072 \u006d\u0075\u0073\u0074\u0020\u0062e\u0020\u003c\u0020\u0032\u0031\u0036\u0030\u0030\u0030\u0030\u0030 \u0028\u0068\u0061\u0076\u0065\u0020\u0025\u0076\u0029",path ,*_bcd .FadeDirAttr );
};};if _bcd .SxAttr !=nil {if _gee :=_bcd .SxAttr .ValidateWithPath (path +"\u002fS\u0078\u0041\u0074\u0074\u0072");_gee !=nil {return _gee ;};};if _bcd .SyAttr !=nil {if _eec :=_bcd .SyAttr .ValidateWithPath (path +"\u002fS\u0079\u0041\u0074\u0074\u0072");
_eec !=nil {return _eec ;};};if _bcd .KxAttr !=nil {if *_bcd .KxAttr <=-5400000{return _g .Errorf ("%\u0073\u002f\u006d\u002e\u004b\u0078\u0041\u0074\u0074\u0072\u0020\u006d\u0075\u0073\u0074\u0020\u0062\u0065 \u003e\u0020\u002d\u0035\u0034\u0030\u0030\u0030\u0030\u0030 (\u0068\u0061\u0076e\u0020%\u0076\u0029",path ,*_bcd .KxAttr );
};if *_bcd .KxAttr >=5400000{return _g .Errorf ("\u0025\u0073\u002f\u006d\u002e\u004b\u0078\u0041\u0074\u0074\u0072\u0020\u006du\u0073\u0074\u0020\u0062\u0065\u0020<\u0020\u0035\u0034\u0030\u0030\u0030\u0030\u0030\u0020\u0028\u0068\u0061\u0076e\u0020\u0025\u0076\u0029",path ,*_bcd .KxAttr );
};};if _bcd .KyAttr !=nil {if *_bcd .KyAttr <=-5400000{return _g .Errorf ("%\u0073\u002f\u006d\u002e\u004b\u0079\u0041\u0074\u0074\u0072\u0020\u006d\u0075\u0073\u0074\u0020\u0062\u0065 \u003e\u0020\u002d\u0035\u0034\u0030\u0030\u0030\u0030\u0030 (\u0068\u0061\u0076e\u0020%\u0076\u0029",path ,*_bcd .KyAttr );
};if *_bcd .KyAttr >=5400000{return _g .Errorf ("\u0025\u0073\u002f\u006d\u002e\u004b\u0079\u0041\u0074\u0074\u0072\u0020\u006du\u0073\u0074\u0020\u0062\u0065\u0020<\u0020\u0035\u0034\u0030\u0030\u0030\u0030\u0030\u0020\u0028\u0068\u0061\u0076e\u0020\u0025\u0076\u0029",path ,*_bcd .KyAttr );
};};if _eac :=_bcd .AlgnAttr .ValidateWithPath (path +"\u002fA\u006c\u0067\u006e\u0041\u0074\u0074r");_eac !=nil {return _eac ;};return nil ;};func (_ffa *CT_Glow )UnmarshalXML (d *_fd .Decoder ,start _fd .StartElement )error {for _ ,_ed :=range start .Attr {if _ed .Name .Local =="\u0072\u0061\u0064"{_gbbg ,_cdg :=_a .ParseInt (_ed .Value ,10,64);
if _cdg !=nil {return _cdg ;};_ffa .RadAttr =&_gbbg ;continue ;};};_bgg :for {_geb ,_gbd :=d .Token ();if _gbd !=nil {return _gbd ;};switch _bac :=_geb .(type ){case _fd .StartElement :switch _bac .Name {case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u0073r\u0067\u0062\u0043\u006c\u0072"}:_ffa .SrgbClr =NewCT_SRgbColor ();
if _afe :=d .DecodeElement (_ffa .SrgbClr ,&_bac );_afe !=nil {return _afe ;};case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u0073c\u0068\u0065\u006d\u0065\u0043\u006cr"}:_ffa .SchemeClr =NewCT_SchemeColor ();
if _dc :=d .DecodeElement (_ffa .SchemeClr ,&_bac );_dc !=nil {return _dc ;};default:_c .Log .Debug ("\u0073\u006b\u0069p\u0070\u0069\u006e\u0067\u0020\u0075\u006e\u0073\u0075\u0070\u0070\u006f\u0072\u0074\u0065\u0064\u0020\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0043T\u005f\u0047\u006c\u006f\u0077\u0020\u0025\u0076",_bac .Name );
if _acg :=d .Skip ();_acg !=nil {return _acg ;};};case _fd .EndElement :break _bgg ;case _fd .CharData :};};return nil ;};func (_dgb *CT_Ligatures )UnmarshalXML (d *_fd .Decoder ,start _fd .StartElement )error {_dgb .ValAttr =ST_Ligatures (1);for _ ,_fag :=range start .Attr {if _fag .Name .Local =="\u0076\u0061\u006c"{_dgb .ValAttr .UnmarshalXMLAttr (_fag );
continue ;};};for {_gge ,_eeg :=d .Token ();if _eeg !=nil {return _g .Errorf ("\u0070a\u0072\u0073\u0069\u006e\u0067\u0020\u0043\u0054\u005f\u004c\u0069g\u0061\u0074\u0075\u0072\u0065\u0073\u003a\u0020\u0025\u0073",_eeg );};if _cff ,_cggea :=_gge .(_fd .EndElement );
_cggea &&_cff .Name ==start .Name {break ;};};return nil ;};

// Validate validates the CT_GradientStop and its children
func (_gfec *CT_GradientStop )Validate ()error {return _gfec .ValidateWithPath ("\u0043T\u005fG\u0072\u0061\u0064\u0069\u0065\u006e\u0074\u0053\u0074\u006f\u0070");};func (_ead *CT_RelativeRect )UnmarshalXML (d *_fd .Decoder ,start _fd .StartElement )error {for _ ,_fde :=range start .Attr {if _fde .Name .Local =="\u006c"{_acec ,_dggf :=ParseUnionST_Percentage (_fde .Value );
if _dggf !=nil {return _dggf ;};_ead .LAttr =&_acec ;continue ;};if _fde .Name .Local =="\u0074"{_eef ,_dfge :=ParseUnionST_Percentage (_fde .Value );if _dfge !=nil {return _dfge ;};_ead .TAttr =&_eef ;continue ;};if _fde .Name .Local =="\u0072"{_effa ,_ddfg :=ParseUnionST_Percentage (_fde .Value );
if _ddfg !=nil {return _ddfg ;};_ead .RAttr =&_effa ;continue ;};if _fde .Name .Local =="\u0062"{_feccd ,_gdb :=ParseUnionST_Percentage (_fde .Value );if _gdb !=nil {return _gdb ;};_ead .BAttr =&_feccd ;continue ;};};for {_bca ,_dgag :=d .Token ();if _dgag !=nil {return _g .Errorf ("p\u0061\u0072\u0073\u0069\u006e\u0067 \u0043\u0054\u005f\u0052\u0065\u006c\u0061\u0074\u0069v\u0065\u0052\u0065c\u0074:\u0020\u0025\u0073",_dgag );
};if _cfbg ,_gegd :=_bca .(_fd .EndElement );_gegd &&_cfbg .Name ==start .Name {break ;};};return nil ;};func (_addb *ST_LightRigDirection )UnmarshalXMLAttr (attr _fd .Attr )error {switch attr .Value {case "":*_addb =0;case "\u0074\u006c":*_addb =1;case "\u0074":*_addb =2;
case "\u0074\u0072":*_addb =3;case "\u006c":*_addb =4;case "\u0072":*_addb =5;case "\u0062\u006c":*_addb =6;case "\u0062":*_addb =7;case "\u0062\u0072":*_addb =8;};return nil ;};

// Validate validates the CT_GradientStopList and its children
func (_fegg *CT_GradientStopList )Validate ()error {return _fegg .ValidateWithPath ("\u0043\u0054\u005f\u0047ra\u0064\u0069\u0065\u006e\u0074\u0053\u0074\u006f\u0070\u004c\u0069\u0073\u0074");};func (_cfcdd ST_PresetMaterialType )MarshalXML (e *_fd .Encoder ,start _fd .StartElement )error {return e .EncodeElement (_cfcdd .String (),start );
};func NewCheckbox ()*Checkbox {_gbdf :=&Checkbox {};_gbdf .CT_SdtCheckbox =*NewCT_SdtCheckbox ();return _gbdf ;};func (_abbfb ST_BevelPresetType )ValidateWithPath (path string )error {switch _abbfb {case 0,1,2,3,4,5,6,7,8,9,10,11,12:default:return _g .Errorf ("\u0025s\u003a\u0020\u006f\u0075t\u0020\u006f\u0066\u0020\u0072a\u006eg\u0065 \u0076\u0061\u006c\u0075\u0065\u0020\u0025d",path ,int (_abbfb ));
};return nil ;};

// Validate validates the CT_PathShadeProperties and its children
func (_ffe *CT_PathShadeProperties )Validate ()error {return _ffe .ValidateWithPath ("\u0043\u0054\u005f\u0050at\u0068\u0053\u0068\u0061\u0064\u0065\u0050\u0072\u006f\u0070\u0065\u0072\u0074\u0069e\u0073");};func (_egcdf *DefaultImageDpi )MarshalXML (e *_fd .Encoder ,start _fd .StartElement )error {start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0078\u006d\u006cn\u0073"},Value :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c"});
start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0061"},Value :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065m\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006cf\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077\u0069\u006e\u0067m\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u006d\u0061\u0069\u006e"});
start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0073"},Value :"\u0068\u0074\u0074\u0070\u003a/\u002f\u0073\u0063\u0068\u0065m\u0061s\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067/\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0068\u0061\u0072e\u0064\u0054\u0079\u0070\u0065\u0073"});
start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0077"},Value :"ht\u0074\u0070:\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073.\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0077\u006f\u0072\u0064\u0070\u0072\u006f\u0063\u0065s\u0073i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u00306\u002fm\u0061\u0069n"});
start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0077\u006fr"},Value :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c"});
start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0078\u006dl"},Value :"\u0068\u0074tp\u003a\u002f\u002fw\u0077\u0077\u002e\u00773.o\u0072g/\u0058\u004d\u004c\u002f\u0031\u0039\u00398/\u006e\u0061\u006d\u0065\u0073\u0070\u0061c\u0065"});
start .Name .Local ="\u0077\u006f\u0072\u003ade\u0066\u0061\u0075\u006c\u0074\u0049\u006d\u0061\u0067\u0065\u0044\u0070\u0069";return _egcdf .CT_DefaultImageDpi .MarshalXML (e ,start );};type CT_GradientStop struct{PosAttr _abg .ST_PositiveFixedPercentage ;
SrgbClr *CT_SRgbColor ;SchemeClr *CT_SchemeColor ;};func ParseUnionST_Percentage (s string )(_abg .ST_Percentage ,error ){return _abg .ParseUnionST_Percentage (s );};func (_fffa *CT_TextOutlineEffect )MarshalXML (e *_fd .Encoder ,start _fd .StartElement )error {if _fffa .WAttr !=nil {start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0077\u006f\u0072:\u0077"},Value :_g .Sprintf ("\u0025\u0076",*_fffa .WAttr )});
};if _fffa .CapAttr !=ST_LineCapUnset {_ffgc ,_cac :=_fffa .CapAttr .MarshalXMLAttr (_fd .Name {Local :"\u0077o\u0072\u003a\u0063\u0061\u0070"});if _cac !=nil {return _cac ;};start .Attr =append (start .Attr ,_ffgc );};if _fffa .CmpdAttr !=ST_CompoundLineUnset {_cbbc ,_cgfeeg :=_fffa .CmpdAttr .MarshalXMLAttr (_fd .Name {Local :"\u0077\u006f\u0072\u003a\u0063\u006d\u0070\u0064"});
if _cgfeeg !=nil {return _cgfeeg ;};start .Attr =append (start .Attr ,_cbbc );};if _fffa .AlgnAttr !=ST_PenAlignmentUnset {_cbee ,_dcdd :=_fffa .AlgnAttr .MarshalXMLAttr (_fd .Name {Local :"\u0077\u006f\u0072\u003a\u0061\u006c\u0067\u006e"});if _dcdd !=nil {return _dcdd ;
};start .Attr =append (start .Attr ,_cbee );};e .EncodeToken (start );_fffa .FillPropertiesChoice .MarshalXML (e ,_fd .StartElement {});if _fffa .PrstDash !=nil {_cfbcc :=_fd .StartElement {Name :_fd .Name {Local :"\u0077\u006f\u0072:\u0070\u0072\u0073\u0074\u0044\u0061\u0073\u0068"}};
e .EncodeElement (_fffa .PrstDash ,_cfbcc );};_fffa .LineJoinPropertiesChoice .MarshalXML (e ,_fd .StartElement {});e .EncodeToken (_fd .EndElement {Name :start .Name });return nil ;};type CT_RelativeRect struct{LAttr *_abg .ST_Percentage ;TAttr *_abg .ST_Percentage ;
RAttr *_abg .ST_Percentage ;BAttr *_abg .ST_Percentage ;};func ParseUnionST_PositiveFixedPercentage (s string )(_abg .ST_PositiveFixedPercentage ,error ){return _abg .ParseUnionST_PositiveFixedPercentage (s );};func ParseUnionST_Coordinate32 (s string )(_abg .ST_Coordinate32 ,error ){return _abg .ParseUnionST_Coordinate32 (s );
};func (_cfga *Checkbox )MarshalXML (e *_fd .Encoder ,start _fd .StartElement )error {start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0078\u006d\u006cn\u0073"},Value :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c"});
start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0061"},Value :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065m\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006cf\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077\u0069\u006e\u0067m\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u006d\u0061\u0069\u006e"});
start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0073"},Value :"\u0068\u0074\u0074\u0070\u003a/\u002f\u0073\u0063\u0068\u0065m\u0061s\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067/\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0068\u0061\u0072e\u0064\u0054\u0079\u0070\u0065\u0073"});
start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0077"},Value :"ht\u0074\u0070:\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073.\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0077\u006f\u0072\u0064\u0070\u0072\u006f\u0063\u0065s\u0073i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u00306\u002fm\u0061\u0069n"});
start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0077\u006fr"},Value :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c"});
start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0078\u006dl"},Value :"\u0068\u0074tp\u003a\u002f\u002fw\u0077\u0077\u002e\u00773.o\u0072g/\u0058\u004d\u004c\u002f\u0031\u0039\u00398/\u006e\u0061\u006d\u0065\u0073\u0070\u0061c\u0065"});
start .Name .Local ="\u0077\u006f\u0072:\u0063\u0068\u0065\u0063\u006b\u0062\u006f\u0078";return _cfga .CT_SdtCheckbox .MarshalXML (e ,start );};func (_cgd *CT_Ligatures )MarshalXML (e *_fd .Encoder ,start _fd .StartElement )error {_gag ,_cedb :=_cgd .ValAttr .MarshalXMLAttr (_fd .Name {Local :"\u0077o\u0072\u003a\u0076\u0061\u006c"});
if _cedb !=nil {return _cedb ;};start .Attr =append (start .Attr ,_gag );e .EncodeToken (start );e .EncodeToken (_fd .EndElement {Name :start .Name });return nil ;};

// ValidateWithPath validates the CT_StyleSet and its children, prefixing error messages with path
func (_bdab *CT_StyleSet )ValidateWithPath (path string )error {if _cgfb :=_bdab .ValAttr .ValidateWithPath (path +"\u002f\u0056\u0061\u006c\u0041\u0074\u0074\u0072");_cgfb !=nil {return _cgfb ;};return nil ;};func NewCustomXmlConflictDelRangeEnd ()*CustomXmlConflictDelRangeEnd {_dace :=&CustomXmlConflictDelRangeEnd {};
_dace .CT_Markup =*_fe .NewCT_Markup ();return _dace ;};func (_gagfb ST_LineCap )String ()string {switch _gagfb {case 0:return "";case 1:return "\u0072\u006e\u0064";case 2:return "\u0073\u0071";case 3:return "\u0066\u006c\u0061\u0074";};return "";};type CT_Reflection struct{BlurRadAttr *int64 ;
StAAttr *_abg .ST_PositiveFixedPercentage ;StPosAttr *_abg .ST_PositiveFixedPercentage ;EndAAttr *_abg .ST_PositiveFixedPercentage ;EndPosAttr *_abg .ST_PositiveFixedPercentage ;DistAttr *int64 ;DirAttr *int32 ;FadeDirAttr *int32 ;SxAttr *_abg .ST_Percentage ;
SyAttr *_abg .ST_Percentage ;KxAttr *int32 ;KyAttr *int32 ;AlgnAttr ST_RectAlignment ;};

// ValidateWithPath validates the EG_FillPropertiesChoice and its children, prefixing error messages with path
func (_bbade *EG_FillPropertiesChoice )ValidateWithPath (path string )error {if _bbade .NoFill !=nil {if _efgg :=_bbade .NoFill .ValidateWithPath (path +"\u002fN\u006f\u0046\u0069\u006c\u006c");_efgg !=nil {return _efgg ;};};if _bbade .SolidFill !=nil {if _ddbdc :=_bbade .SolidFill .ValidateWithPath (path +"\u002f\u0053\u006f\u006c\u0069\u0064\u0046\u0069\u006c\u006c");
_ddbdc !=nil {return _ddbdc ;};};if _bbade .GradFill !=nil {if _dgaca :=_bbade .GradFill .ValidateWithPath (path +"\u002fG\u0072\u0061\u0064\u0046\u0069\u006cl");_dgaca !=nil {return _dgaca ;};};return nil ;};type EG_LineDashProperties struct{PrstDash *CT_PresetLineDashProperties ;
};func (_gacg ST_SchemeColorVal )MarshalXMLAttr (name _fd .Name )(_fd .Attr ,error ){_gebb :=_fd .Attr {};_gebb .Name =name ;switch _gacg {case ST_SchemeColorValUnset :_gebb .Value ="";case ST_SchemeColorValBg1 :_gebb .Value ="\u0062\u0067\u0031";case ST_SchemeColorValTx1 :_gebb .Value ="\u0074\u0078\u0031";
case ST_SchemeColorValBg2 :_gebb .Value ="\u0062\u0067\u0032";case ST_SchemeColorValTx2 :_gebb .Value ="\u0074\u0078\u0032";case ST_SchemeColorValAccent1 :_gebb .Value ="\u0061c\u0063\u0065\u006e\u0074\u0031";case ST_SchemeColorValAccent2 :_gebb .Value ="\u0061c\u0063\u0065\u006e\u0074\u0032";
case ST_SchemeColorValAccent3 :_gebb .Value ="\u0061c\u0063\u0065\u006e\u0074\u0033";case ST_SchemeColorValAccent4 :_gebb .Value ="\u0061c\u0063\u0065\u006e\u0074\u0034";case ST_SchemeColorValAccent5 :_gebb .Value ="\u0061c\u0063\u0065\u006e\u0074\u0035";
case ST_SchemeColorValAccent6 :_gebb .Value ="\u0061c\u0063\u0065\u006e\u0074\u0036";case ST_SchemeColorValHlink :_gebb .Value ="\u0068\u006c\u0069n\u006b";case ST_SchemeColorValFolHlink :_gebb .Value ="\u0066\u006f\u006c\u0048\u006c\u0069\u006e\u006b";
case ST_SchemeColorValDk1 :_gebb .Value ="\u0064\u006b\u0031";case ST_SchemeColorValLt1 :_gebb .Value ="\u006c\u0074\u0031";case ST_SchemeColorValDk2 :_gebb .Value ="\u0064\u006b\u0032";case ST_SchemeColorValLt2 :_gebb .Value ="\u006c\u0074\u0032";case ST_SchemeColorValPhClr :_gebb .Value ="\u0070\u0068\u0043l\u0072";
};return _gebb ,nil ;};func (_beagc ST_PresetMaterialType )Validate ()error {return _beagc .ValidateWithPath ("")};func NewEG_FillProperties ()*EG_FillProperties {_bdfg :=&EG_FillProperties {};_bdfg .FillPropertiesChoice =NewEG_FillPropertiesChoice ();
return _bdfg ;};func NewEG_LineJoinProperties ()*EG_LineJoinProperties {_gffg :=&EG_LineJoinProperties {};_gffg .LineJoinPropertiesChoice =NewEG_LineJoinPropertiesChoice ();return _gffg ;};

// ValidateWithPath validates the EG_LineDashProperties and its children, prefixing error messages with path
func (_cgb *EG_LineDashProperties )ValidateWithPath (path string )error {if _cgb .PrstDash !=nil {if _gaeb :=_cgb .PrstDash .ValidateWithPath (path +"\u002fP\u0072\u0073\u0074\u0044\u0061\u0073h");_gaeb !=nil {return _gaeb ;};};return nil ;};

// ValidateWithPath validates the CT_SdtCheckbox and its children, prefixing error messages with path
func (_bdeb *CT_SdtCheckbox )ValidateWithPath (path string )error {if _bdeb .Checked !=nil {if _egbd :=_bdeb .Checked .ValidateWithPath (path +"\u002f\u0043\u0068\u0065\u0063\u006b\u0065\u0064");_egbd !=nil {return _egbd ;};};if _bdeb .CheckedState !=nil {if _gdfb :=_bdeb .CheckedState .ValidateWithPath (path +"\u002f\u0043\u0068\u0065\u0063\u006b\u0065\u0064\u0053\u0074\u0061\u0074\u0065");
_gdfb !=nil {return _gdfb ;};};if _bdeb .UncheckedState !=nil {if _ede :=_bdeb .UncheckedState .ValidateWithPath (path +"\u002fU\u006ec\u0068\u0065\u0063\u006b\u0065\u0064\u0053\u0074\u0061\u0074\u0065");_ede !=nil {return _ede ;};};return nil ;};const (ST_SchemeColorValUnset ST_SchemeColorVal =0;
ST_SchemeColorValBg1 ST_SchemeColorVal =1;ST_SchemeColorValTx1 ST_SchemeColorVal =2;ST_SchemeColorValBg2 ST_SchemeColorVal =3;ST_SchemeColorValTx2 ST_SchemeColorVal =4;ST_SchemeColorValAccent1 ST_SchemeColorVal =5;ST_SchemeColorValAccent2 ST_SchemeColorVal =6;
ST_SchemeColorValAccent3 ST_SchemeColorVal =7;ST_SchemeColorValAccent4 ST_SchemeColorVal =8;ST_SchemeColorValAccent5 ST_SchemeColorVal =9;ST_SchemeColorValAccent6 ST_SchemeColorVal =10;ST_SchemeColorValHlink ST_SchemeColorVal =11;ST_SchemeColorValFolHlink ST_SchemeColorVal =12;
ST_SchemeColorValDk1 ST_SchemeColorVal =13;ST_SchemeColorValLt1 ST_SchemeColorVal =14;ST_SchemeColorValDk2 ST_SchemeColorVal =15;ST_SchemeColorValLt2 ST_SchemeColorVal =16;ST_SchemeColorValPhClr ST_SchemeColorVal =17;);

// Validate validates the AG_Parids and its children
func (_bf *AG_Parids )Validate ()error {return _bf .ValidateWithPath ("\u0041G\u005f\u0050\u0061\u0072\u0069\u0064s");};type EG_ColorTransformChoice struct{Tint *CT_PositiveFixedPercentage ;Shade *CT_PositiveFixedPercentage ;Alpha *CT_PositiveFixedPercentage ;
HueMod *CT_PositivePercentage ;Sat *CT_Percentage ;SatOff *CT_Percentage ;SatMod *CT_Percentage ;Lum *CT_Percentage ;LumOff *CT_Percentage ;LumMod *CT_Percentage ;};func (_egf *CT_SRgbColor )UnmarshalXML (d *_fd .Decoder ,start _fd .StartElement )error {for _ ,_bfdc :=range start .Attr {if _bfdc .Name .Local =="\u0076\u0061\u006c"{_dcda :=_bfdc .Value ;
_egf .ValAttr =_dcda ;continue ;};};_efcd :for {_fba ,_bacc :=d .Token ();if _bacc !=nil {return _bacc ;};switch _bfea :=_fba .(type ){case _fd .StartElement :switch _bfea .Name {case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u0074\u0069\u006e\u0074"}:_bge :=NewEG_ColorTransform ();
_bge .ColorTransformChoice =NewEG_ColorTransformChoice ();_egf .EG_ColorTransform =append (_egf .EG_ColorTransform ,_bge );if _dagc :=d .DecodeElement (&_bge .ColorTransformChoice .Tint ,&_bfea );_dagc !=nil {return _dagc ;};case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u0073\u0068\u0061d\u0065"}:_dabb :=NewEG_ColorTransform ();
_dabb .ColorTransformChoice =NewEG_ColorTransformChoice ();_egf .EG_ColorTransform =append (_egf .EG_ColorTransform ,_dabb );if _fbfbe :=d .DecodeElement (&_dabb .ColorTransformChoice .Shade ,&_bfea );_fbfbe !=nil {return _fbfbe ;};case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u0061\u006c\u0070h\u0061"}:_ggae :=NewEG_ColorTransform ();
_ggae .ColorTransformChoice =NewEG_ColorTransformChoice ();_egf .EG_ColorTransform =append (_egf .EG_ColorTransform ,_ggae );if _cbdc :=d .DecodeElement (&_ggae .ColorTransformChoice .Alpha ,&_bfea );_cbdc !=nil {return _cbdc ;};case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u0068\u0075\u0065\u004d\u006f\u0064"}:_bgfc :=NewEG_ColorTransform ();
_bgfc .ColorTransformChoice =NewEG_ColorTransformChoice ();_egf .EG_ColorTransform =append (_egf .EG_ColorTransform ,_bgfc );if _aeb :=d .DecodeElement (&_bgfc .ColorTransformChoice .HueMod ,&_bfea );_aeb !=nil {return _aeb ;};case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u0073\u0061\u0074"}:_fcedg :=NewEG_ColorTransform ();
_fcedg .ColorTransformChoice =NewEG_ColorTransformChoice ();_egf .EG_ColorTransform =append (_egf .EG_ColorTransform ,_fcedg );if _abae :=d .DecodeElement (&_fcedg .ColorTransformChoice .Sat ,&_bfea );_abae !=nil {return _abae ;};case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u0073\u0061\u0074\u004f\u0066\u0066"}:_cbdg :=NewEG_ColorTransform ();
_cbdg .ColorTransformChoice =NewEG_ColorTransformChoice ();_egf .EG_ColorTransform =append (_egf .EG_ColorTransform ,_cbdg );if _ggeg :=d .DecodeElement (&_cbdg .ColorTransformChoice .SatOff ,&_bfea );_ggeg !=nil {return _ggeg ;};case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u0073\u0061\u0074\u004d\u006f\u0064"}:_ffg :=NewEG_ColorTransform ();
_ffg .ColorTransformChoice =NewEG_ColorTransformChoice ();_egf .EG_ColorTransform =append (_egf .EG_ColorTransform ,_ffg );if _gfa :=d .DecodeElement (&_ffg .ColorTransformChoice .SatMod ,&_bfea );_gfa !=nil {return _gfa ;};case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u006c\u0075\u006d"}:_dgcf :=NewEG_ColorTransform ();
_dgcf .ColorTransformChoice =NewEG_ColorTransformChoice ();_egf .EG_ColorTransform =append (_egf .EG_ColorTransform ,_dgcf );if _dfca :=d .DecodeElement (&_dgcf .ColorTransformChoice .Lum ,&_bfea );_dfca !=nil {return _dfca ;};case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u006c\u0075\u006d\u004f\u0066\u0066"}:_bgc :=NewEG_ColorTransform ();
_bgc .ColorTransformChoice =NewEG_ColorTransformChoice ();_egf .EG_ColorTransform =append (_egf .EG_ColorTransform ,_bgc );if _decc :=d .DecodeElement (&_bgc .ColorTransformChoice .LumOff ,&_bfea );_decc !=nil {return _decc ;};case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u006c\u0075\u006d\u004d\u006f\u0064"}:_bgb :=NewEG_ColorTransform ();
_bgb .ColorTransformChoice =NewEG_ColorTransformChoice ();_egf .EG_ColorTransform =append (_egf .EG_ColorTransform ,_bgb );if _ccb :=d .DecodeElement (&_bgb .ColorTransformChoice .LumMod ,&_bfea );_ccb !=nil {return _ccb ;};default:_c .Log .Debug ("s\u006b\u0069\u0070\u0070\u0069\u006e\u0067\u0020\u0075n\u0073\u0075\u0070\u0070\u006f\u0072\u0074ed\u0020\u0065\u006c\u0065m\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0043\u0054_S\u0052\u0067b\u0043\u006f\u006c\u006f\u0072\u0020\u0025\u0076",_bfea .Name );
if _cgfee :=d .Skip ();_cgfee !=nil {return _cgfee ;};};case _fd .EndElement :break _efcd ;case _fd .CharData :};};return nil ;};func (_eagd ST_Ligatures )Validate ()error {return _eagd .ValidateWithPath ("")};func (_aeff *CT_Props3D )MarshalXML (e *_fd .Encoder ,start _fd .StartElement )error {if _aeff .ExtrusionHAttr !=nil {start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0077\u006f\u0072\u003a\u0065\u0078\u0074\u0072\u0075s\u0069\u006f\u006e\u0048"},Value :_g .Sprintf ("\u0025\u0076",*_aeff .ExtrusionHAttr )});
};if _aeff .ContourWAttr !=nil {start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0077\u006f\u0072:\u0063\u006f\u006e\u0074\u006f\u0075\u0072\u0057"},Value :_g .Sprintf ("\u0025\u0076",*_aeff .ContourWAttr )});};if _aeff .PrstMaterialAttr !=ST_PresetMaterialTypeUnset {_ebe ,_dea :=_aeff .PrstMaterialAttr .MarshalXMLAttr (_fd .Name {Local :"\u0077\u006fr\u003a\u0070\u0072s\u0074\u004d\u0061\u0074\u0065\u0072\u0069\u0061\u006c"});
if _dea !=nil {return _dea ;};start .Attr =append (start .Attr ,_ebe );};e .EncodeToken (start );if _aeff .BevelT !=nil {_acb :=_fd .StartElement {Name :_fd .Name {Local :"\u0077\u006f\u0072\u003a\u0062\u0065\u0076\u0065\u006c\u0054"}};e .EncodeElement (_aeff .BevelT ,_acb );
};if _aeff .BevelB !=nil {_fagd :=_fd .StartElement {Name :_fd .Name {Local :"\u0077\u006f\u0072\u003a\u0062\u0065\u0076\u0065\u006c\u0042"}};e .EncodeElement (_aeff .BevelB ,_fagd );};if _aeff .ExtrusionClr !=nil {_baa :=_fd .StartElement {Name :_fd .Name {Local :"\u0077\u006fr\u003a\u0065\u0078t\u0072\u0075\u0073\u0069\u006f\u006e\u0043\u006c\u0072"}};
e .EncodeElement (_aeff .ExtrusionClr ,_baa );};if _aeff .ContourClr !=nil {_eeedd :=_fd .StartElement {Name :_fd .Name {Local :"\u0077\u006f\u0072\u003a\u0063\u006f\u006e\u0074\u006fu\u0072\u0043\u006c\u0072"}};e .EncodeElement (_aeff .ContourClr ,_eeedd );
};e .EncodeToken (_fd .EndElement {Name :start .Name });return nil ;};func (_eeeed ST_LightRigDirection )MarshalXMLAttr (name _fd .Name )(_fd .Attr ,error ){_aeaf :=_fd .Attr {};_aeaf .Name =name ;switch _eeeed {case ST_LightRigDirectionUnset :_aeaf .Value ="";
case ST_LightRigDirectionTl :_aeaf .Value ="\u0074\u006c";case ST_LightRigDirectionT :_aeaf .Value ="\u0074";case ST_LightRigDirectionTr :_aeaf .Value ="\u0074\u0072";case ST_LightRigDirectionL :_aeaf .Value ="\u006c";case ST_LightRigDirectionR :_aeaf .Value ="\u0072";
case ST_LightRigDirectionBl :_aeaf .Value ="\u0062\u006c";case ST_LightRigDirectionB :_aeaf .Value ="\u0062";case ST_LightRigDirectionBr :_aeaf .Value ="\u0062\u0072";};return _aeaf ,nil ;};const (ST_LightRigTypeUnset ST_LightRigType =0;ST_LightRigTypeLegacyFlat1 ST_LightRigType =1;
ST_LightRigTypeLegacyFlat2 ST_LightRigType =2;ST_LightRigTypeLegacyFlat3 ST_LightRigType =3;ST_LightRigTypeLegacyFlat4 ST_LightRigType =4;ST_LightRigTypeLegacyNormal1 ST_LightRigType =5;ST_LightRigTypeLegacyNormal2 ST_LightRigType =6;ST_LightRigTypeLegacyNormal3 ST_LightRigType =7;
ST_LightRigTypeLegacyNormal4 ST_LightRigType =8;ST_LightRigTypeLegacyHarsh1 ST_LightRigType =9;ST_LightRigTypeLegacyHarsh2 ST_LightRigType =10;ST_LightRigTypeLegacyHarsh3 ST_LightRigType =11;ST_LightRigTypeLegacyHarsh4 ST_LightRigType =12;ST_LightRigTypeThreePt ST_LightRigType =13;
ST_LightRigTypeBalanced ST_LightRigType =14;ST_LightRigTypeSoft ST_LightRigType =15;ST_LightRigTypeHarsh ST_LightRigType =16;ST_LightRigTypeFlood ST_LightRigType =17;ST_LightRigTypeContrasting ST_LightRigType =18;ST_LightRigTypeMorning ST_LightRigType =19;
ST_LightRigTypeSunrise ST_LightRigType =20;ST_LightRigTypeSunset ST_LightRigType =21;ST_LightRigTypeChilly ST_LightRigType =22;ST_LightRigTypeFreezing ST_LightRigType =23;ST_LightRigTypeFlat ST_LightRigType =24;ST_LightRigTypeTwoPt ST_LightRigType =25;
ST_LightRigTypeGlow ST_LightRigType =26;ST_LightRigTypeBrightRoom ST_LightRigType =27;);func (_dcdcg ST_NumForm )Validate ()error {return _dcdcg .ValidateWithPath ("")};func (_gdg *CT_LongHexNumber )MarshalXML (e *_fd .Encoder ,start _fd .StartElement )error {start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0077o\u0072\u003a\u0076\u0061\u006c"},Value :_g .Sprintf ("\u0025\u0076",_gdg .ValAttr )});
e .EncodeToken (start );e .EncodeToken (_fd .EndElement {Name :start .Name });return nil ;};func (_abfg *EG_ColorTransform )MarshalXML (e *_fd .Encoder ,start _fd .StartElement )error {_abfg .ColorTransformChoice .MarshalXML (e ,_fd .StartElement {});return nil ;
};func (_gdfc *CustomXmlConflictInsRangeEnd )MarshalXML (e *_fd .Encoder ,start _fd .StartElement )error {start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0078\u006d\u006cn\u0073"},Value :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c"});
start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0061"},Value :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065m\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006cf\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077\u0069\u006e\u0067m\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u006d\u0061\u0069\u006e"});
start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0073"},Value :"\u0068\u0074\u0074\u0070\u003a/\u002f\u0073\u0063\u0068\u0065m\u0061s\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067/\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0068\u0061\u0072e\u0064\u0054\u0079\u0070\u0065\u0073"});
start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0077"},Value :"ht\u0074\u0070:\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073.\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0077\u006f\u0072\u0064\u0070\u0072\u006f\u0063\u0065s\u0073i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u00306\u002fm\u0061\u0069n"});
start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0077\u006fr"},Value :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c"});
start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0078\u006dl"},Value :"\u0068\u0074tp\u003a\u002f\u002fw\u0077\u0077\u002e\u00773.o\u0072g/\u0058\u004d\u004c\u002f\u0031\u0039\u00398/\u006e\u0061\u006d\u0065\u0073\u0070\u0061c\u0065"});
start .Name .Local ="\u0077\u006f\u0072\u003a\u0063\u0075\u0073\u0074\u006f\u006d\u0058\u006d\u006c\u0043\u006fn\u0066l\u0069\u0063\u0074\u0049\u006e\u0073\u0052\u0061\u006e\u0067\u0065\u0045\u006e\u0064";return _gdfc .CT_Markup .MarshalXML (e ,start );
};

// Validate validates the CustomXmlConflictInsRangeStart and its children
func (_cbdb *CustomXmlConflictInsRangeStart )Validate ()error {return _cbdb .ValidateWithPath ("\u0043\u0075\u0073\u0074\u006f\u006d\u0058\u006d\u006c\u0043o\u006e\u0066\u006c\u0069\u0063\u0074\u0049n\u0073\u0052\u0061\u006e\u0067\u0065\u0053\u0074\u0061\u0072\u0074");
};func (_cdgc *CT_NumSpacing )MarshalXML (e *_fd .Encoder ,start _fd .StartElement )error {_fcab ,_dcc :=_cdgc .ValAttr .MarshalXMLAttr (_fd .Name {Local :"\u0077o\u0072\u003a\u0076\u0061\u006c"});if _dcc !=nil {return _dcc ;};start .Attr =append (start .Attr ,_fcab );
e .EncodeToken (start );e .EncodeToken (_fd .EndElement {Name :start .Name });return nil ;};func (_efad *EG_RPrOpenType )UnmarshalXML (d *_fd .Decoder ,start _fd .StartElement )error {_eddg :for {_dffg ,_dgfc :=d .Token ();if _dgfc !=nil {return _dgfc ;
};switch _befb :=_dffg .(type ){case _fd .StartElement :switch _befb .Name {case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u006ci\u0067\u0061\u0074\u0075\u0072\u0065s"}:_efad .Ligatures =NewCT_Ligatures ();
if _aadc :=d .DecodeElement (_efad .Ligatures ,&_befb );_aadc !=nil {return _aadc ;};case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u006eu\u006d\u0046\u006f\u0072\u006d"}:_efad .NumForm =NewCT_NumForm ();
if _bdbga :=d .DecodeElement (_efad .NumForm ,&_befb );_bdbga !=nil {return _bdbga ;};case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u006e\u0075\u006d\u0053\u0070\u0061\u0063\u0069\u006e\u0067"}:_efad .NumSpacing =NewCT_NumSpacing ();
if _bgagd :=d .DecodeElement (_efad .NumSpacing ,&_befb );_bgagd !=nil {return _bgagd ;};case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u0073\u0074\u0079\u006c\u0069\u0073\u0074\u0069\u0063\u0053\u0065\u0074\u0073"}:_efad .StylisticSets =NewCT_StylisticSets ();
if _edea :=d .DecodeElement (_efad .StylisticSets ,&_befb );_edea !=nil {return _edea ;};case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u0063n\u0074\u0078\u0074\u0041\u006c\u0074s"}:_efad .CntxtAlts =NewCT_OnOff ();
if _fdgd :=d .DecodeElement (_efad .CntxtAlts ,&_befb );_fdgd !=nil {return _fdgd ;};default:_c .Log .Debug ("\u0073\u006b\u0069\u0070\u0070\u0069n\u0067\u0020\u0075\u006e\u0073\u0075\u0070\u0070\u006f\u0072\u0074\u0065\u0064\u0020\u0065\u006c\u0065\u006d\u0065\u006et\u0020\u006f\u006e\u0020\u0045\u0047\u005f\u0052\u0050\u0072\u004f\u0070\u0065\u006eT\u0079p\u0065\u0020\u0025\u0076",_befb .Name );
if _ggcf :=d .Skip ();_ggcf !=nil {return _ggcf ;};};case _fd .EndElement :break _eddg ;case _fd .CharData :};};return nil ;};func (_cgcb ST_PresetLineDashVal )ValidateWithPath (path string )error {switch _cgcb {case 0,1,2,3,4,5,6,7,8,9,10,11:default:return _g .Errorf ("\u0025s\u003a\u0020\u006f\u0075t\u0020\u006f\u0066\u0020\u0072a\u006eg\u0065 \u0076\u0061\u006c\u0075\u0065\u0020\u0025d",path ,int (_cgcb ));
};return nil ;};func (_gcfb ST_PresetCameraType )Validate ()error {return _gcfb .ValidateWithPath ("")};type ST_PenAlignment byte ;func NewCT_SdtCheckbox ()*CT_SdtCheckbox {_eadf :=&CT_SdtCheckbox {};return _eadf };func (_deed ST_RectAlignment )ValidateWithPath (path string )error {switch _deed {case 0,1,2,3,4,5,6,7,8,9,10:default:return _g .Errorf ("\u0025s\u003a\u0020\u006f\u0075t\u0020\u006f\u0066\u0020\u0072a\u006eg\u0065 \u0076\u0061\u006c\u0075\u0065\u0020\u0025d",path ,int (_deed ));
};return nil ;};func (_cbda *DocId )MarshalXML (e *_fd .Encoder ,start _fd .StartElement )error {start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0078\u006d\u006cn\u0073"},Value :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c"});
start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0061"},Value :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065m\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006cf\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077\u0069\u006e\u0067m\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u006d\u0061\u0069\u006e"});
start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0073"},Value :"\u0068\u0074\u0074\u0070\u003a/\u002f\u0073\u0063\u0068\u0065m\u0061s\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067/\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0068\u0061\u0072e\u0064\u0054\u0079\u0070\u0065\u0073"});
start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0077"},Value :"ht\u0074\u0070:\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073.\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0077\u006f\u0072\u0064\u0070\u0072\u006f\u0063\u0065s\u0073i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u00306\u002fm\u0061\u0069n"});
start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0077\u006fr"},Value :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c"});
start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0078\u006dl"},Value :"\u0068\u0074tp\u003a\u002f\u002fw\u0077\u0077\u002e\u00773.o\u0072g/\u0058\u004d\u004c\u002f\u0031\u0039\u00398/\u006e\u0061\u006d\u0065\u0073\u0070\u0061c\u0065"});
start .Name .Local ="\u0077o\u0072\u003a\u0064\u006f\u0063\u0049d";return _cbda .CT_LongHexNumber .MarshalXML (e ,start );};func (_dacc ST_RectAlignment )MarshalXMLAttr (name _fd .Name )(_fd .Attr ,error ){_acefd :=_fd .Attr {};_acefd .Name =name ;switch _dacc {case ST_RectAlignmentUnset :_acefd .Value ="";
case ST_RectAlignmentNone :_acefd .Value ="\u006e\u006f\u006e\u0065";case ST_RectAlignmentTl :_acefd .Value ="\u0074\u006c";case ST_RectAlignmentT :_acefd .Value ="\u0074";case ST_RectAlignmentTr :_acefd .Value ="\u0074\u0072";case ST_RectAlignmentL :_acefd .Value ="\u006c";
case ST_RectAlignmentCtr :_acefd .Value ="\u0063\u0074\u0072";case ST_RectAlignmentR :_acefd .Value ="\u0072";case ST_RectAlignmentBl :_acefd .Value ="\u0062\u006c";case ST_RectAlignmentB :_acefd .Value ="\u0062";case ST_RectAlignmentBr :_acefd .Value ="\u0062\u0072";
};return _acefd ,nil ;};func (_dedc *CT_PresetLineDashProperties )UnmarshalXML (d *_fd .Decoder ,start _fd .StartElement )error {for _ ,_dgac :=range start .Attr {if _dgac .Name .Local =="\u0076\u0061\u006c"{_dedc .ValAttr .UnmarshalXMLAttr (_dgac );continue ;
};};for {_bbeb ,_egca :=d .Token ();if _egca !=nil {return _g .Errorf ("\u0070\u0061\u0072\u0073\u0069\u006e\u0067\u0020\u0043\u0054\u005f\u0050\u0072e\u0073\u0065\u0074\u004c\u0069\u006ee\u0044\u0061\u0073\u0068\u0050\u0072\u006f\u0070\u0065\u0072\u0074\u0069\u0065s\u003a\u0020\u0025\u0073",_egca );
};if _bbg ,_cgag :=_bbeb .(_fd .EndElement );_cgag &&_bbg .Name ==start .Name {break ;};};return nil ;};type EG_ShadeProperties struct{ShadePropertiesChoice *EG_ShadePropertiesChoice ;};func (_agf *CT_Glow )MarshalXML (e *_fd .Encoder ,start _fd .StartElement )error {if _agf .RadAttr !=nil {start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0077o\u0072\u003a\u0072\u0061\u0064"},Value :_g .Sprintf ("\u0025\u0076",*_agf .RadAttr )});
};e .EncodeToken (start );if _agf .SrgbClr !=nil {_gce :=_fd .StartElement {Name :_fd .Name {Local :"w\u006f\u0072\u003a\u0073\u0072\u0067\u0062\u0043\u006c\u0072"}};e .EncodeElement (_agf .SrgbClr ,_gce );};if _agf .SchemeClr !=nil {_cba :=_fd .StartElement {Name :_fd .Name {Local :"\u0077\u006f\u0072\u003a\u0073\u0063\u0068\u0065\u006d\u0065\u0043\u006c\u0072"}};
e .EncodeElement (_agf .SchemeClr ,_cba );};e .EncodeToken (_fd .EndElement {Name :start .Name });return nil ;};func (_cged *CT_GradientFillProperties )MarshalXML (e *_fd .Encoder ,start _fd .StartElement )error {e .EncodeToken (start );if _cged .GsLst !=nil {_bfb :=_fd .StartElement {Name :_fd .Name {Local :"\u0077o\u0072\u003a\u0067\u0073\u004c\u0073t"}};
e .EncodeElement (_cged .GsLst ,_bfb );};_cged .ShadePropertiesChoice .MarshalXML (e ,_fd .StartElement {});e .EncodeToken (_fd .EndElement {Name :start .Name });return nil ;};func ParseUnionST_AnimationChartBuildType (s string )(_abg .ST_AnimationChartBuildType ,error ){return _abg .ParseUnionST_AnimationChartBuildType (s );
};func (_acgf ST_NumSpacing )MarshalXML (e *_fd .Encoder ,start _fd .StartElement )error {return e .EncodeElement (_acgf .String (),start );};func NewCT_StylisticSets ()*CT_StylisticSets {_ccbf :=&CT_StylisticSets {};return _ccbf };type ST_LightRigType byte ;
func (_gaag *CT_SdtCheckbox )UnmarshalXML (d *_fd .Decoder ,start _fd .StartElement )error {_fbdb :for {_cdgbe ,_cde :=d .Token ();if _cde !=nil {return _cde ;};switch _ddbd :=_cdgbe .(type ){case _fd .StartElement :switch _ddbd .Name {case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u0063h\u0065\u0063\u006b\u0065\u0064"}:_gaag .Checked =NewCT_OnOff ();
if _ebef :=d .DecodeElement (_gaag .Checked ,&_ddbd );_ebef !=nil {return _ebef ;};case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u0063\u0068\u0065c\u006b\u0065\u0064\u0053\u0074\u0061\u0074\u0065"}:_gaag .CheckedState =NewCT_SdtCheckboxSymbol ();
if _fcb :=d .DecodeElement (_gaag .CheckedState ,&_ddbd );_fcb !=nil {return _fcb ;};case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u0075\u006e\u0063\u0068\u0065\u0063\u006b\u0065\u0064S\u0074\u0061\u0074\u0065"}:_gaag .UncheckedState =NewCT_SdtCheckboxSymbol ();
if _bbbf :=d .DecodeElement (_gaag .UncheckedState ,&_ddbd );_bbbf !=nil {return _bbbf ;};default:_c .Log .Debug ("\u0073\u006b\u0069\u0070\u0070\u0069n\u0067\u0020\u0075\u006e\u0073\u0075\u0070\u0070\u006f\u0072\u0074\u0065\u0064\u0020\u0065\u006c\u0065\u006d\u0065\u006et\u0020\u006f\u006e\u0020\u0043\u0054\u005f\u0053\u0064\u0074\u0043\u0068\u0065\u0063k\u0062o\u0078\u0020\u0025\u0076",_ddbd .Name );
if _eaee :=d .Skip ();_eaee !=nil {return _eaee ;};};case _fd .EndElement :break _fbdb ;case _fd .CharData :};};return nil ;};

// Validate validates the CT_TextOutlineEffect and its children
func (_eedf *CT_TextOutlineEffect )Validate ()error {return _eedf .ValidateWithPath ("C\u0054_\u0054\u0065\u0078\u0074\u004f\u0075\u0074\u006ci\u006e\u0065\u0045\u0066fe\u0063\u0074");};type CT_PathShadeProperties struct{PathAttr ST_PathShadeType ;FillToRect *CT_RelativeRect ;
};func (_deg *CT_DefaultImageDpi )MarshalXML (e *_fd .Encoder ,start _fd .StartElement )error {start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0077o\u0072\u003a\u0076\u0061\u006c"},Value :_g .Sprintf ("\u0025\u0076",_deg .ValAttr )});
e .EncodeToken (start );e .EncodeToken (_fd .EndElement {Name :start .Name });return nil ;};func (_gfbbg ST_LightRigType )Validate ()error {return _gfbbg .ValidateWithPath ("")};

// Validate validates the CT_PositiveFixedPercentage and its children
func (_faab *CT_PositiveFixedPercentage )Validate ()error {return _faab .ValidateWithPath ("\u0043\u0054\u005f\u0050\u006f\u0073\u0069\u0074\u0069\u0076\u0065F\u0069\u0078\u0065\u0064\u0050\u0065\u0072\u0063\u0065\u006et\u0061\u0067\u0065");};func (_bfffg *DiscardImageEditingData )UnmarshalXML (d *_fd .Decoder ,start _fd .StartElement )error {_bfffg .CT_OnOff =*NewCT_OnOff ();
for _ ,_faga :=range start .Attr {if _faga .Name .Local =="\u0076\u0061\u006c"{_bfffg .ValAttr .UnmarshalXMLAttr (_faga );continue ;};};for {_edbe ,_ccbb :=d .Token ();if _ccbb !=nil {return _g .Errorf ("p\u0061\u0072\u0073\u0069\u006e\u0067 \u0044\u0069\u0073\u0063\u0061\u0072d\u0049\u006d\u0061\u0067\u0065\u0045\u0064i\u0074\u0069\u006e\u0067\u0044\u0061\u0074\u0061\u003a\u0020%\u0073",_ccbb );
};if _dega ,_fcf :=_edbe .(_fd .EndElement );_fcf &&_dega .Name ==start .Name {break ;};};return nil ;};func NewCT_SchemeColor ()*CT_SchemeColor {_daca :=&CT_SchemeColor {};_daca .ValAttr =ST_SchemeColorVal (1);return _daca ;};func (_ffbg ST_NumSpacing )ValidateWithPath (path string )error {switch _ffbg {case 0,1,2,3:default:return _g .Errorf ("\u0025s\u003a\u0020\u006f\u0075t\u0020\u006f\u0066\u0020\u0072a\u006eg\u0065 \u0076\u0061\u006c\u0075\u0065\u0020\u0025d",path ,int (_ffbg ));
};return nil ;};func (_beac *CT_PositiveFixedPercentage )UnmarshalXML (d *_fd .Decoder ,start _fd .StartElement )error {for _ ,_bbf :=range start .Attr {if _bbf .Name .Local =="\u0076\u0061\u006c"{_cbde ,_beb :=ParseUnionST_PositiveFixedPercentage (_bbf .Value );
if _beb !=nil {return _beb ;};_beac .ValAttr =_cbde ;continue ;};};for {_abab ,_dfaf :=d .Token ();if _dfaf !=nil {return _g .Errorf ("\u0070\u0061\u0072\u0073\u0069\u006e\u0067\u0020\u0043\u0054\u005f\u0050\u006f\u0073\u0069\u0074\u0069\u0076\u0065\u0046\u0069\u0078\u0065\u0064P\u0065\u0072\u0063\u0065\u006et\u0061\u0067e\u003a\u0020\u0025\u0073",_dfaf );
};if _ggg ,_bggdc :=_abab .(_fd .EndElement );_bggdc &&_ggg .Name ==start .Name {break ;};};return nil ;};

// ValidateWithPath validates the CT_GradientStop and its children, prefixing error messages with path
func (_dbg *CT_GradientStop )ValidateWithPath (path string )error {if _aab :=_dbg .PosAttr .ValidateWithPath (path +"\u002f\u0050\u006f\u0073\u0041\u0074\u0074\u0072");_aab !=nil {return _aab ;};if _dbg .SrgbClr !=nil {if _bba :=_dbg .SrgbClr .ValidateWithPath (path +"\u002f\u0053\u0072\u0067\u0062\u0043\u006c\u0072");
_bba !=nil {return _bba ;};};if _dbg .SchemeClr !=nil {if _ade :=_dbg .SchemeClr .ValidateWithPath (path +"\u002f\u0053\u0063\u0068\u0065\u006d\u0065\u0043\u006c\u0072");_ade !=nil {return _ade ;};};return nil ;};

// Validate validates the CT_NumForm and its children
func (_aefd *CT_NumForm )Validate ()error {return _aefd .ValidateWithPath ("\u0043\u0054\u005f\u004e\u0075\u006d\u0046\u006f\u0072\u006d");};func (_cgda ST_LightRigDirection )ValidateWithPath (path string )error {switch _cgda {case 0,1,2,3,4,5,6,7,8:default:return _g .Errorf ("\u0025s\u003a\u0020\u006f\u0075t\u0020\u006f\u0066\u0020\u0072a\u006eg\u0065 \u0076\u0061\u006c\u0075\u0065\u0020\u0025d",path ,int (_cgda ));
};return nil ;};

// ValidateWithPath validates the EG_RPrTextEffects and its children, prefixing error messages with path
func (_ecd *EG_RPrTextEffects )ValidateWithPath (path string )error {if _ecd .Glow !=nil {if _deee :=_ecd .Glow .ValidateWithPath (path +"\u002f\u0047\u006co\u0077");_deee !=nil {return _deee ;};};if _ecd .Shadow !=nil {if _aagf :=_ecd .Shadow .ValidateWithPath (path +"\u002fS\u0068\u0061\u0064\u006f\u0077");
_aagf !=nil {return _aagf ;};};if _ecd .Reflection !=nil {if _baag :=_ecd .Reflection .ValidateWithPath (path +"/\u0052\u0065\u0066\u006c\u0065\u0063\u0074\u0069\u006f\u006e");_baag !=nil {return _baag ;};};if _ecd .TextOutline !=nil {if _fbab :=_ecd .TextOutline .ValidateWithPath (path +"\u002f\u0054\u0065x\u0074\u004f\u0075\u0074\u006c\u0069\u006e\u0065");
_fbab !=nil {return _fbab ;};};if _ecd .TextFill !=nil {if _febg :=_ecd .TextFill .ValidateWithPath (path +"\u002fT\u0065\u0078\u0074\u0046\u0069\u006cl");_febg !=nil {return _febg ;};};if _ecd .Scene3d !=nil {if _gfed :=_ecd .Scene3d .ValidateWithPath (path +"\u002f\u0053\u0063\u0065\u006e\u0065\u0033\u0064");
_gfed !=nil {return _gfed ;};};if _ecd .Props3d !=nil {if _efaae :=_ecd .Props3d .ValidateWithPath (path +"\u002f\u0050\u0072\u006f\u0070\u0073\u0033\u0064");_efaae !=nil {return _efaae ;};};return nil ;};type ST_LineCap byte ;func (_eefa *CT_Shadow )UnmarshalXML (d *_fd .Decoder ,start _fd .StartElement )error {for _ ,_fgdg :=range start .Attr {if _fgdg .Name .Local =="\u0062l\u0075\u0072\u0052\u0061\u0064"{_ageg ,_gfcf :=_a .ParseInt (_fgdg .Value ,10,64);
if _gfcf !=nil {return _gfcf ;};_eefa .BlurRadAttr =&_ageg ;continue ;};if _fgdg .Name .Local =="\u0064\u0069\u0073\u0074"{_bdfab ,_cbfe :=_a .ParseInt (_fgdg .Value ,10,64);if _cbfe !=nil {return _cbfe ;};_eefa .DistAttr =&_bdfab ;continue ;};if _fgdg .Name .Local =="\u0064\u0069\u0072"{_cfdc ,_cdbb :=_a .ParseInt (_fgdg .Value ,10,32);
if _cdbb !=nil {return _cdbb ;};_eceb :=int32 (_cfdc );_eefa .DirAttr =&_eceb ;continue ;};if _fgdg .Name .Local =="\u0073\u0078"{_ffad ,_fdb :=ParseUnionST_Percentage (_fgdg .Value );if _fdb !=nil {return _fdb ;};_eefa .SxAttr =&_ffad ;continue ;};if _fgdg .Name .Local =="\u0073\u0079"{_gafc ,_bbcf :=ParseUnionST_Percentage (_fgdg .Value );
if _bbcf !=nil {return _bbcf ;};_eefa .SyAttr =&_gafc ;continue ;};if _fgdg .Name .Local =="\u006b\u0078"{_cdgbc ,_bbd :=_a .ParseInt (_fgdg .Value ,10,32);if _bbd !=nil {return _bbd ;};_gdbf :=int32 (_cdgbc );_eefa .KxAttr =&_gdbf ;continue ;};if _fgdg .Name .Local =="\u006b\u0079"{_dgbc ,_agac :=_a .ParseInt (_fgdg .Value ,10,32);
if _agac !=nil {return _agac ;};_bfff :=int32 (_dgbc );_eefa .KyAttr =&_bfff ;continue ;};if _fgdg .Name .Local =="\u0061\u006c\u0067\u006e"{_eefa .AlgnAttr .UnmarshalXMLAttr (_fgdg );continue ;};};_bab :for {_bcgf ,_ebee :=d .Token ();if _ebee !=nil {return _ebee ;
};switch _dbc :=_bcgf .(type ){case _fd .StartElement :switch _dbc .Name {case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u0073r\u0067\u0062\u0043\u006c\u0072"}:_eefa .SrgbClr =NewCT_SRgbColor ();
if _fafd :=d .DecodeElement (_eefa .SrgbClr ,&_dbc );_fafd !=nil {return _fafd ;};case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u0073c\u0068\u0065\u006d\u0065\u0043\u006cr"}:_eefa .SchemeClr =NewCT_SchemeColor ();
if _gbcb :=d .DecodeElement (_eefa .SchemeClr ,&_dbc );_gbcb !=nil {return _gbcb ;};default:_c .Log .Debug ("\u0073k\u0069\u0070p\u0069\u006e\u0067\u0020u\u006e\u0073\u0075p\u0070\u006f\u0072\u0074\u0065\u0064\u0020\u0065\u006cem\u0065\u006e\u0074 \u006f\u006e \u0043\u0054\u005f\u0053\u0068\u0061d\u006f\u0077 \u0025\u0076",_dbc .Name );
if _fdbf :=d .Skip ();_fdbf !=nil {return _fdbf ;};};case _fd .EndElement :break _bab ;case _fd .CharData :};};return nil ;};func ParseUnionST_FixedPercentage (s string )(_abg .ST_FixedPercentage ,error ){return _abg .ParseUnionST_FixedPercentage (s );
};func (_dacf *CT_Scene3D )MarshalXML (e *_fd .Encoder ,start _fd .StartElement )error {e .EncodeToken (start );_eeee :=_fd .StartElement {Name :_fd .Name {Local :"\u0077\u006f\u0072\u003a\u0063\u0061\u006d\u0065\u0072\u0061"}};e .EncodeElement (_dacf .Camera ,_eeee );
_aefc :=_fd .StartElement {Name :_fd .Name {Local :"\u0077\u006f\u0072:\u006c\u0069\u0067\u0068\u0074\u0052\u0069\u0067"}};e .EncodeElement (_dacf .LightRig ,_aefc );e .EncodeToken (_fd .EndElement {Name :start .Name });return nil ;};type CustomXmlConflictInsRangeStart struct{_fe .CT_TrackChange };
func (_acba *EG_ColorChoice )UnmarshalXML (d *_fd .Decoder ,start _fd .StartElement )error {_acef :=start ;switch start .Name {case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u0073r\u0067\u0062\u0043\u006c\u0072"}:_acba .SrgbClr =NewCT_SRgbColor ();
if _gdaa :=d .DecodeElement (_acba .SrgbClr ,&_acef );_gdaa !=nil {return _gdaa ;};case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u0073c\u0068\u0065\u006d\u0065\u0043\u006cr"}:_acba .SchemeClr =NewCT_SchemeColor ();
if _faad :=d .DecodeElement (_acba .SchemeClr ,&_acef );_faad !=nil {return _faad ;};default:_c .Log .Debug ("\u0073\u006b\u0069\u0070\u0070\u0069n\u0067\u0020\u0075\u006e\u0073\u0075\u0070\u0070\u006f\u0072\u0074\u0065\u0064\u0020\u0065\u006c\u0065\u006d\u0065\u006et\u0020\u006f\u006e\u0020\u0045\u0047\u005f\u0043\u006f\u006c\u006f\u0072\u0043\u0068o\u0069c\u0065\u0020\u0025\u0076",_acef .Name );
if _afaa :=d .Skip ();_afaa !=nil {return _afaa ;};};return nil ;};func (_agfd ST_PenAlignment )Validate ()error {return _agfd .ValidateWithPath ("")};func NewEG_ShadeProperties ()*EG_ShadeProperties {_ebdd :=&EG_ShadeProperties {};_ebdd .ShadePropertiesChoice =NewEG_ShadePropertiesChoice ();
return _ebdd ;};func (_gccc ST_OnOff )ValidateWithPath (path string )error {switch _gccc {case 0,1,2,3,4:default:return _g .Errorf ("\u0025s\u003a\u0020\u006f\u0075t\u0020\u006f\u0066\u0020\u0072a\u006eg\u0065 \u0076\u0061\u006c\u0075\u0065\u0020\u0025d",path ,int (_gccc ));
};return nil ;};func NewEG_Conflicts ()*EG_Conflicts {_gdfcg :=&EG_Conflicts {};_gdfcg .ConflictsChoice =NewEG_ConflictsChoice ();return _gdfcg ;};func (_gbg *CT_OnOff )MarshalXML (e *_fd .Encoder ,start _fd .StartElement )error {if _gbg .ValAttr !=ST_OnOffUnset {_ggd ,_eafc :=_gbg .ValAttr .MarshalXMLAttr (_fd .Name {Local :"\u0077o\u0072\u003a\u0076\u0061\u006c"});
if _eafc !=nil {return _eafc ;};start .Attr =append (start .Attr ,_ggd );};e .EncodeToken (start );e .EncodeToken (_fd .EndElement {Name :start .Name });return nil ;};

// Validate validates the EG_RPrOpenType and its children
func (_afga *EG_RPrOpenType )Validate ()error {return _afga .ValidateWithPath ("\u0045\u0047\u005f\u0052\u0050\u0072\u004f\u0070\u0065n\u0054\u0079\u0070\u0065");};

// ValidateWithPath validates the CT_Color and its children, prefixing error messages with path
func (_bee *CT_Color )ValidateWithPath (path string )error {if _bee .SrgbClr !=nil {if _fcd :=_bee .SrgbClr .ValidateWithPath (path +"\u002f\u0053\u0072\u0067\u0062\u0043\u006c\u0072");_fcd !=nil {return _fcd ;};};if _bee .SchemeClr !=nil {if _ced :=_bee .SchemeClr .ValidateWithPath (path +"\u002f\u0053\u0063\u0068\u0065\u006d\u0065\u0043\u006c\u0072");
_ced !=nil {return _ced ;};};return nil ;};type CT_LineJoinMiterProperties struct{LimAttr *_abg .ST_PositivePercentage ;};

// ValidateWithPath validates the CT_PositivePercentage and its children, prefixing error messages with path
func (_fbd *CT_PositivePercentage )ValidateWithPath (path string )error {if _egb :=_fbd .ValAttr .ValidateWithPath (path +"\u002f\u0056\u0061\u006c\u0041\u0074\u0074\u0072");_egb !=nil {return _egb ;};return nil ;};func (_gggc *EG_FillProperties )UnmarshalXML (d *_fd .Decoder ,start _fd .StartElement )error {_gggc .FillPropertiesChoice =NewEG_FillPropertiesChoice ();
_eacc :for {_bbdc ,_eebd :=d .Token ();if _eebd !=nil {return _eebd ;};switch _fdcec :=_bbdc .(type ){case _fd .StartElement :switch _fdcec .Name {case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u006e\u006f\u0046\u0069\u006c\u006c"}:_gggc .FillPropertiesChoice =NewEG_FillPropertiesChoice ();
if _agegd :=d .DecodeElement (&_gggc .FillPropertiesChoice .NoFill ,&_fdcec );_agegd !=nil {return _agegd ;};case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u0073o\u006c\u0069\u0064\u0046\u0069\u006cl"}:_gggc .FillPropertiesChoice =NewEG_FillPropertiesChoice ();
if _cggga :=d .DecodeElement (&_gggc .FillPropertiesChoice .SolidFill ,&_fdcec );_cggga !=nil {return _cggga ;};case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u0067\u0072\u0061\u0064\u0046\u0069\u006c\u006c"}:_gggc .FillPropertiesChoice =NewEG_FillPropertiesChoice ();
if _ebcg :=d .DecodeElement (&_gggc .FillPropertiesChoice .GradFill ,&_fdcec );_ebcg !=nil {return _ebcg ;};default:_c .Log .Debug ("\u0073\u006bi\u0070\u0070\u0069\u006e\u0067 \u0075\u006e\u0073\u0075\u0070p\u006f\u0072\u0074\u0065\u0064\u0020\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0045\u0047\u005f\u0046\u0069\u006c\u006c\u0050\u0072\u006f\u0070\u0065\u0072\u0074\u0069\u0065\u0073\u0020\u0025\u0076",_fdcec .Name );
if _cfcfa :=d .Skip ();_cfcfa !=nil {return _cfcfa ;};};case _fd .EndElement :break _eacc ;case _fd .CharData :};};return nil ;};type CT_NumSpacing struct{ValAttr ST_NumSpacing ;};func (_dade *CT_NumForm )UnmarshalXML (d *_fd .Decoder ,start _fd .StartElement )error {_dade .ValAttr =ST_NumForm (1);
for _ ,_agbg :=range start .Attr {if _agbg .Name .Local =="\u0076\u0061\u006c"{_dade .ValAttr .UnmarshalXMLAttr (_agbg );continue ;};};for {_cfg ,_ged :=d .Token ();if _ged !=nil {return _g .Errorf ("\u0070\u0061\u0072\u0073in\u0067\u0020\u0043\u0054\u005f\u004e\u0075\u006d\u0046\u006f\u0072\u006d\u003a\u0020%\u0073",_ged );
};if _gdae ,_aaedf :=_cfg .(_fd .EndElement );_aaedf &&_gdae .Name ==start .Name {break ;};};return nil ;};

// ValidateWithPath validates the CT_SolidColorFillProperties and its children, prefixing error messages with path
func (_eade *CT_SolidColorFillProperties )ValidateWithPath (path string )error {if _eade .SrgbClr !=nil {if _baca :=_eade .SrgbClr .ValidateWithPath (path +"\u002f\u0053\u0072\u0067\u0062\u0043\u006c\u0072");_baca !=nil {return _baca ;};};if _eade .SchemeClr !=nil {if _becf :=_eade .SchemeClr .ValidateWithPath (path +"\u002f\u0053\u0063\u0068\u0065\u006d\u0065\u0043\u006c\u0072");
_becf !=nil {return _becf ;};};return nil ;};

// Validate validates the CustomXmlConflictDelRangeEnd and its children
func (_fdbc *CustomXmlConflictDelRangeEnd )Validate ()error {return _fdbc .ValidateWithPath ("\u0043\u0075\u0073\u0074o\u006d\u0058\u006d\u006c\u0043\u006f\u006e\u0066\u006c\u0069c\u0074D\u0065\u006c\u0052\u0061\u006e\u0067\u0065E\u006e\u0064");};

// ValidateWithPath validates the CT_Camera and its children, prefixing error messages with path
func (_aac *CT_Camera )ValidateWithPath (path string )error {if _aac .PrstAttr ==ST_PresetCameraTypeUnset {return _g .Errorf ("\u0025\u0073\u002f\u0050\u0072\u0073\u0074\u0041\u0074\u0074\u0072\u0020\u0069\u0073\u0020a\u0020m\u0061\u006e\u0064\u0061\u0074\u006f\u0072\u0079\u0020\u0066\u0069\u0065\u006c\u0064",path );
};if _gc :=_aac .PrstAttr .ValidateWithPath (path +"\u002fP\u0072\u0073\u0074\u0041\u0074\u0074r");_gc !=nil {return _gc ;};return nil ;};func (_gbbgg ST_BevelPresetType )String ()string {switch _gbbgg {case 0:return "";case 1:return "\u0072\u0065\u006ca\u0078\u0065\u0064\u0049\u006e\u0073\u0065\u0074";
case 2:return "\u0063\u0069\u0072\u0063\u006c\u0065";case 3:return "\u0073\u006c\u006fp\u0065";case 4:return "\u0063\u0072\u006fs\u0073";case 5:return "\u0061\u006e\u0067l\u0065";case 6:return "\u0073o\u0066\u0074\u0052\u006f\u0075\u006ed";case 7:return "\u0063\u006f\u006e\u0076\u0065\u0078";
case 8:return "\u0063o\u006f\u006c\u0053\u006c\u0061\u006et";case 9:return "\u0064\u0069\u0076o\u0074";case 10:return "\u0072\u0069\u0062\u006c\u0065\u0074";case 11:return "\u0068\u0061\u0072\u0064\u0045\u0064\u0067\u0065";case 12:return "\u0061r\u0074\u0044\u0065\u0063\u006f";
};return "";};func NewEG_RPrOpenType ()*EG_RPrOpenType {_gebf :=&EG_RPrOpenType {};return _gebf };func (_bfdf *CT_SolidColorFillProperties )UnmarshalXML (d *_fd .Decoder ,start _fd .StartElement )error {_acag :for {_fbbf ,_dabfc :=d .Token ();if _dabfc !=nil {return _dabfc ;
};switch _gaaf :=_fbbf .(type ){case _fd .StartElement :switch _gaaf .Name {case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u0073r\u0067\u0062\u0043\u006c\u0072"}:_bfdf .SrgbClr =NewCT_SRgbColor ();
if _babd :=d .DecodeElement (_bfdf .SrgbClr ,&_gaaf );_babd !=nil {return _babd ;};case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u0073c\u0068\u0065\u006d\u0065\u0043\u006cr"}:_bfdf .SchemeClr =NewCT_SchemeColor ();
if _dbgd :=d .DecodeElement (_bfdf .SchemeClr ,&_gaaf );_dbgd !=nil {return _dbgd ;};default:_c .Log .Debug ("\u0073\u006b\u0069\u0070\u0070\u0069\u006e\u0067\u0020\u0075\u006e\u0073\u0075\u0070\u0070\u006f\u0072\u0074ed\u0020e\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0043\u0054_\u0053\u006f\u006c\u0069\u0064\u0043\u006f\u006c\u006f\u0072\u0046\u0069\u006c\u006c\u0050\u0072\u006fp\u0065\u0072\u0074\u0069\u0065\u0073\u0020\u0025\u0076",_gaaf .Name );
if _gabc :=d .Skip ();_gabc !=nil {return _gabc ;};};case _fd .EndElement :break _acag ;case _fd .CharData :};};return nil ;};const (ST_LightRigDirectionUnset ST_LightRigDirection =0;ST_LightRigDirectionTl ST_LightRigDirection =1;ST_LightRigDirectionT ST_LightRigDirection =2;
ST_LightRigDirectionTr ST_LightRigDirection =3;ST_LightRigDirectionL ST_LightRigDirection =4;ST_LightRigDirectionR ST_LightRigDirection =5;ST_LightRigDirectionBl ST_LightRigDirection =6;ST_LightRigDirectionB ST_LightRigDirection =7;ST_LightRigDirectionBr ST_LightRigDirection =8;
);func (_efab *CT_LineJoinMiterProperties )MarshalXML (e *_fd .Encoder ,start _fd .StartElement )error {if _efab .LimAttr !=nil {start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0077o\u0072\u003a\u006c\u0069\u006d"},Value :_g .Sprintf ("\u0025\u0076",*_efab .LimAttr )});
};e .EncodeToken (start );e .EncodeToken (_fd .EndElement {Name :start .Name });return nil ;};func NewCT_Color ()*CT_Color {_fce :=&CT_Color {};return _fce };func (_aeaee *ST_OnOff )UnmarshalXML (d *_fd .Decoder ,start _fd .StartElement )error {_fffg ,_dbdg :=d .Token ();
if _dbdg !=nil {return _dbdg ;};if _fcfc ,_caef :=_fffg .(_fd .EndElement );_caef &&_fcfc .Name ==start .Name {*_aeaee =1;return nil ;};if _fafc ,_cffab :=_fffg .(_fd .CharData );!_cffab {return _g .Errorf ("\u0065\u0078\u0070\u0065\u0063\u0074\u0065\u0064\u0020\u0063\u0068a\u0072\u0020\u0064\u0061\u0074\u0061\u002c\u0020\u0067\u006ft\u0020\u0025\u0054",_fffg );
}else {switch string (_fafc ){case "":*_aeaee =0;case "\u0074\u0072\u0075\u0065":*_aeaee =1;case "\u0066\u0061\u006cs\u0065":*_aeaee =2;case "\u0030":*_aeaee =3;case "\u0031":*_aeaee =4;};};_fffg ,_dbdg =d .Token ();if _dbdg !=nil {return _dbdg ;};if _edda ,_ebfe :=_fffg .(_fd .EndElement );
_ebfe &&_edda .Name ==start .Name {return nil ;};return _g .Errorf ("\u0065\u0078\u0070\u0065c\u0074\u0065\u0064\u0020\u0065\u006e\u0064\u0020\u0065\u006ce\u006de\u006e\u0074\u002c\u0020\u0067\u006f\u0074 \u0025\u0076",_fffg );};

// Validate validates the EG_FillPropertiesChoice and its children
func (_addc *EG_FillPropertiesChoice )Validate ()error {return _addc .ValidateWithPath ("\u0045\u0047\u005fFi\u006c\u006c\u0050\u0072\u006f\u0070\u0065\u0072\u0074\u0069\u0065\u0073\u0043\u0068\u006f\u0069\u0063\u0065");};func (_cfcg *ST_NumSpacing )UnmarshalXML (d *_fd .Decoder ,start _fd .StartElement )error {_bfbad ,_efgfc :=d .Token ();
if _efgfc !=nil {return _efgfc ;};if _gffb ,_bfac :=_bfbad .(_fd .EndElement );_bfac &&_gffb .Name ==start .Name {*_cfcg =1;return nil ;};if _cdee ,_efgba :=_bfbad .(_fd .CharData );!_efgba {return _g .Errorf ("\u0065\u0078\u0070\u0065\u0063\u0074\u0065\u0064\u0020\u0063\u0068a\u0072\u0020\u0064\u0061\u0074\u0061\u002c\u0020\u0067\u006ft\u0020\u0025\u0054",_bfbad );
}else {switch string (_cdee ){case "":*_cfcg =0;case "\u0064e\u0066\u0061\u0075\u006c\u0074":*_cfcg =1;case "\u0070\u0072\u006fp\u006f\u0072\u0074\u0069\u006f\u006e\u0061\u006c":*_cfcg =2;case "\u0074a\u0062\u0075\u006c\u0061\u0072":*_cfcg =3;};};_bfbad ,_efgfc =d .Token ();
if _efgfc !=nil {return _efgfc ;};if _addf ,_fbda :=_bfbad .(_fd .EndElement );_fbda &&_addf .Name ==start .Name {return nil ;};return _g .Errorf ("\u0065\u0078\u0070\u0065c\u0074\u0065\u0064\u0020\u0065\u006e\u0064\u0020\u0065\u006ce\u006de\u006e\u0074\u002c\u0020\u0067\u006f\u0074 \u0025\u0076",_bfbad );
};func NewCT_GradientFillProperties ()*CT_GradientFillProperties {_fgg :=&CT_GradientFillProperties {};_fgg .ShadePropertiesChoice =NewEG_ShadePropertiesChoice ();return _fgg ;};const (ST_NumFormUnset ST_NumForm =0;ST_NumFormDefault ST_NumForm =1;ST_NumFormLining ST_NumForm =2;
ST_NumFormOldStyle ST_NumForm =3;);

// Validate validates the EG_ShadePropertiesChoice and its children
func (_bagf *EG_ShadePropertiesChoice )Validate ()error {return _bagf .ValidateWithPath ("\u0045G\u005f\u0053\u0068\u0061\u0064\u0065\u0050\u0072\u006f\u0070\u0065r\u0074\u0069\u0065\u0073\u0043\u0068\u006f\u0069\u0063\u0065");};type DiscardImageEditingData struct{CT_OnOff };
func NewCT_PresetLineDashProperties ()*CT_PresetLineDashProperties {_cdae :=&CT_PresetLineDashProperties {};return _cdae ;};func NewEG_ColorChoice ()*EG_ColorChoice {_gbcc :=&EG_ColorChoice {};return _gbcc };

// Validate validates the CT_Reflection and its children
func (_eaed *CT_Reflection )Validate ()error {return _eaed .ValidateWithPath ("\u0043\u0054\u005f\u0052\u0065\u0066\u006c\u0065\u0063\u0074\u0069\u006f\u006e");};func NewCT_Camera ()*CT_Camera {_de :=&CT_Camera {};_de .PrstAttr =ST_PresetCameraType (1);
return _de ;};func (_fgde ST_RectAlignment )MarshalXML (e *_fd .Encoder ,start _fd .StartElement )error {return e .EncodeElement (_fgde .String (),start );};

// Validate validates the CustomXmlConflictInsRangeEnd and its children
func (_ccbe *CustomXmlConflictInsRangeEnd )Validate ()error {return _ccbe .ValidateWithPath ("\u0043\u0075\u0073\u0074o\u006d\u0058\u006d\u006c\u0043\u006f\u006e\u0066\u006c\u0069c\u0074I\u006e\u0073\u0052\u0061\u006e\u0067\u0065E\u006e\u0064");};func (_cfgbf ST_PresetMaterialType )String ()string {switch _cfgbf {case 0:return "";
case 1:return "l\u0065\u0067\u0061\u0063\u0079\u004d\u0061\u0074\u0074\u0065";case 2:return "\u006c\u0065\u0067\u0061\u0063\u0079\u0050\u006c\u0061\u0073\u0074\u0069\u0063";case 3:return "l\u0065\u0067\u0061\u0063\u0079\u004d\u0065\u0074\u0061\u006c";case 4:return "\u006ce\u0067a\u0063\u0079\u0057\u0069\u0072\u0065\u0066\u0072\u0061\u006d\u0065";
case 5:return "\u006d\u0061\u0074t\u0065";case 6:return "\u0070l\u0061\u0073\u0074\u0069\u0063";case 7:return "\u006d\u0065\u0074a\u006c";case 8:return "\u0077a\u0072\u006d\u004d\u0061\u0074\u0074e";case 9:return "\u0074\u0072\u0061\u006e\u0073\u006c\u0075\u0063\u0065\u006e\u0074\u0050o\u0077\u0064\u0065\u0072";
case 10:return "\u0070\u006f\u0077\u0064\u0065\u0072";case 11:return "\u0064\u006b\u0045\u0064\u0067\u0065";case 12:return "\u0073\u006f\u0066\u0074\u0045\u0064\u0067\u0065";case 13:return "\u0063\u006c\u0065a\u0072";case 14:return "\u0066\u006c\u0061\u0074";
case 15:return "\u0073o\u0066\u0074\u006d\u0065\u0074\u0061l";case 16:return "\u006e\u006f\u006e\u0065";};return "";};func NewCT_OnOff ()*CT_OnOff {_fagb :=&CT_OnOff {};return _fagb };type DefaultImageDpi struct{CT_DefaultImageDpi };func (_abda *CT_SRgbColor )MarshalXML (e *_fd .Encoder ,start _fd .StartElement )error {start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0077o\u0072\u003a\u0076\u0061\u006c"},Value :_g .Sprintf ("\u0025\u0076",_abda .ValAttr )});
e .EncodeToken (start );if _abda .EG_ColorTransform !=nil {for _ ,_dabf :=range _abda .EG_ColorTransform {_dabf .MarshalXML (e ,_fd .StartElement {});};};e .EncodeToken (_fd .EndElement {Name :start .Name });return nil ;};func (_cbfdf ST_PathShadeType )ValidateWithPath (path string )error {switch _cbfdf {case 0,1,2,3:default:return _g .Errorf ("\u0025s\u003a\u0020\u006f\u0075t\u0020\u006f\u0066\u0020\u0072a\u006eg\u0065 \u0076\u0061\u006c\u0075\u0065\u0020\u0025d",path ,int (_cbfdf ));
};return nil ;};func (_dcf *CT_SchemeColor )UnmarshalXML (d *_fd .Decoder ,start _fd .StartElement )error {_dcf .ValAttr =ST_SchemeColorVal (1);for _ ,_agfb :=range start .Attr {if _agfb .Name .Local =="\u0076\u0061\u006c"{_dcf .ValAttr .UnmarshalXMLAttr (_agfb );
continue ;};};_fceb :for {_bcg ,_eed :=d .Token ();if _eed !=nil {return _eed ;};switch _cffa :=_bcg .(type ){case _fd .StartElement :switch _cffa .Name {case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u0074\u0069\u006e\u0074"}:_bdac :=NewEG_ColorTransform ();
_bdac .ColorTransformChoice =NewEG_ColorTransformChoice ();_dcf .EG_ColorTransform =append (_dcf .EG_ColorTransform ,_bdac );if _ddfe :=d .DecodeElement (&_bdac .ColorTransformChoice .Tint ,&_cffa );_ddfe !=nil {return _ddfe ;};case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u0073\u0068\u0061d\u0065"}:_fded :=NewEG_ColorTransform ();
_fded .ColorTransformChoice =NewEG_ColorTransformChoice ();_dcf .EG_ColorTransform =append (_dcf .EG_ColorTransform ,_fded );if _eea :=d .DecodeElement (&_fded .ColorTransformChoice .Shade ,&_cffa );_eea !=nil {return _eea ;};case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u0061\u006c\u0070h\u0061"}:_bef :=NewEG_ColorTransform ();
_bef .ColorTransformChoice =NewEG_ColorTransformChoice ();_dcf .EG_ColorTransform =append (_dcf .EG_ColorTransform ,_bef );if _fae :=d .DecodeElement (&_bef .ColorTransformChoice .Alpha ,&_cffa );_fae !=nil {return _fae ;};case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u0068\u0075\u0065\u004d\u006f\u0064"}:_ebde :=NewEG_ColorTransform ();
_ebde .ColorTransformChoice =NewEG_ColorTransformChoice ();_dcf .EG_ColorTransform =append (_dcf .EG_ColorTransform ,_ebde );if _dbe :=d .DecodeElement (&_ebde .ColorTransformChoice .HueMod ,&_cffa );_dbe !=nil {return _dbe ;};case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u0073\u0061\u0074"}:_egd :=NewEG_ColorTransform ();
_egd .ColorTransformChoice =NewEG_ColorTransformChoice ();_dcf .EG_ColorTransform =append (_dcf .EG_ColorTransform ,_egd );if _cfa :=d .DecodeElement (&_egd .ColorTransformChoice .Sat ,&_cffa );_cfa !=nil {return _cfa ;};case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u0073\u0061\u0074\u004f\u0066\u0066"}:_gdf :=NewEG_ColorTransform ();
_gdf .ColorTransformChoice =NewEG_ColorTransformChoice ();_dcf .EG_ColorTransform =append (_dcf .EG_ColorTransform ,_gdf );if _gdafa :=d .DecodeElement (&_gdf .ColorTransformChoice .SatOff ,&_cffa );_gdafa !=nil {return _gdafa ;};case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u0073\u0061\u0074\u004d\u006f\u0064"}:_cggb :=NewEG_ColorTransform ();
_cggb .ColorTransformChoice =NewEG_ColorTransformChoice ();_dcf .EG_ColorTransform =append (_dcf .EG_ColorTransform ,_cggb );if _cfde :=d .DecodeElement (&_cggb .ColorTransformChoice .SatMod ,&_cffa );_cfde !=nil {return _cfde ;};case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u006c\u0075\u006d"}:_ccg :=NewEG_ColorTransform ();
_ccg .ColorTransformChoice =NewEG_ColorTransformChoice ();_dcf .EG_ColorTransform =append (_dcf .EG_ColorTransform ,_ccg );if _dcce :=d .DecodeElement (&_ccg .ColorTransformChoice .Lum ,&_cffa );_dcce !=nil {return _dcce ;};case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u006c\u0075\u006d\u004f\u0066\u0066"}:_dagf :=NewEG_ColorTransform ();
_dagf .ColorTransformChoice =NewEG_ColorTransformChoice ();_dcf .EG_ColorTransform =append (_dcf .EG_ColorTransform ,_dagf );if _dafa :=d .DecodeElement (&_dagf .ColorTransformChoice .LumOff ,&_cffa );_dafa !=nil {return _dafa ;};case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u006c\u0075\u006d\u004d\u006f\u0064"}:_bdfe :=NewEG_ColorTransform ();
_bdfe .ColorTransformChoice =NewEG_ColorTransformChoice ();_dcf .EG_ColorTransform =append (_dcf .EG_ColorTransform ,_bdfe );if _bcfa :=d .DecodeElement (&_bdfe .ColorTransformChoice .LumMod ,&_cffa );_bcfa !=nil {return _bcfa ;};default:_c .Log .Debug ("\u0073\u006b\u0069\u0070\u0070\u0069n\u0067\u0020\u0075\u006e\u0073\u0075\u0070\u0070\u006f\u0072\u0074\u0065\u0064\u0020\u0065\u006c\u0065\u006d\u0065\u006et\u0020\u006f\u006e\u0020\u0043\u0054\u005f\u0053\u0063\u0068\u0065\u006d\u0065\u0043o\u006co\u0072\u0020\u0025\u0076",_cffa .Name );
if _bbgc :=d .Skip ();_bbgc !=nil {return _bbgc ;};};case _fd .EndElement :break _fceb ;case _fd .CharData :};};return nil ;};type EG_ConflictsChoice struct{ConflictIns *_fe .CT_TrackChange ;ConflictDel *_fe .CT_TrackChange ;};func (_gg *CT_Bevel )UnmarshalXML (d *_fd .Decoder ,start _fd .StartElement )error {for _ ,_be :=range start .Attr {if _be .Name .Local =="\u0077"{_ae ,_cg :=_a .ParseInt (_be .Value ,10,64);
if _cg !=nil {return _cg ;};_gg .WAttr =&_ae ;continue ;};if _be .Name .Local =="\u0068"{_aa ,_bcf :=_a .ParseInt (_be .Value ,10,64);if _bcf !=nil {return _bcf ;};_gg .HAttr =&_aa ;continue ;};if _be .Name .Local =="\u0070\u0072\u0073\u0074"{_gg .PrstAttr .UnmarshalXMLAttr (_be );
continue ;};};for {_ff ,_bd :=d .Token ();if _bd !=nil {return _g .Errorf ("p\u0061r\u0073\u0069\u006e\u0067\u0020\u0043\u0054\u005fB\u0065\u0076\u0065\u006c: \u0025\u0073",_bd );};if _cca ,_fgc :=_ff .(_fd .EndElement );_fgc &&_cca .Name ==start .Name {break ;
};};return nil ;};type CT_SphereCoords struct{LatAttr int32 ;LonAttr int32 ;RevAttr int32 ;};

// Validate validates the CT_PositivePercentage and its children
func (_efd *CT_PositivePercentage )Validate ()error {return _efd .ValidateWithPath ("C\u0054\u005f\u0050\u006fsi\u0074i\u0076\u0065\u0050\u0065\u0072c\u0065\u006e\u0074\u0061\u0067\u0065");};type CT_PositivePercentage struct{ValAttr _abg .ST_PositivePercentage ;
};func (_dbcf ST_PresetMaterialType )MarshalXMLAttr (name _fd .Name )(_fd .Attr ,error ){_adef :=_fd .Attr {};_adef .Name =name ;switch _dbcf {case ST_PresetMaterialTypeUnset :_adef .Value ="";case ST_PresetMaterialTypeLegacyMatte :_adef .Value ="l\u0065\u0067\u0061\u0063\u0079\u004d\u0061\u0074\u0074\u0065";
case ST_PresetMaterialTypeLegacyPlastic :_adef .Value ="\u006c\u0065\u0067\u0061\u0063\u0079\u0050\u006c\u0061\u0073\u0074\u0069\u0063";case ST_PresetMaterialTypeLegacyMetal :_adef .Value ="l\u0065\u0067\u0061\u0063\u0079\u004d\u0065\u0074\u0061\u006c";
case ST_PresetMaterialTypeLegacyWireframe :_adef .Value ="\u006ce\u0067a\u0063\u0079\u0057\u0069\u0072\u0065\u0066\u0072\u0061\u006d\u0065";case ST_PresetMaterialTypeMatte :_adef .Value ="\u006d\u0061\u0074t\u0065";case ST_PresetMaterialTypePlastic :_adef .Value ="\u0070l\u0061\u0073\u0074\u0069\u0063";
case ST_PresetMaterialTypeMetal :_adef .Value ="\u006d\u0065\u0074a\u006c";case ST_PresetMaterialTypeWarmMatte :_adef .Value ="\u0077a\u0072\u006d\u004d\u0061\u0074\u0074e";case ST_PresetMaterialTypeTranslucentPowder :_adef .Value ="\u0074\u0072\u0061\u006e\u0073\u006c\u0075\u0063\u0065\u006e\u0074\u0050o\u0077\u0064\u0065\u0072";
case ST_PresetMaterialTypePowder :_adef .Value ="\u0070\u006f\u0077\u0064\u0065\u0072";case ST_PresetMaterialTypeDkEdge :_adef .Value ="\u0064\u006b\u0045\u0064\u0067\u0065";case ST_PresetMaterialTypeSoftEdge :_adef .Value ="\u0073\u006f\u0066\u0074\u0045\u0064\u0067\u0065";
case ST_PresetMaterialTypeClear :_adef .Value ="\u0063\u006c\u0065a\u0072";case ST_PresetMaterialTypeFlat :_adef .Value ="\u0066\u006c\u0061\u0074";case ST_PresetMaterialTypeSoftmetal :_adef .Value ="\u0073o\u0066\u0074\u006d\u0065\u0074\u0061l";case ST_PresetMaterialTypeNone :_adef .Value ="\u006e\u006f\u006e\u0065";
};return _adef ,nil ;};func (_gd *CT_Bevel )MarshalXML (e *_fd .Encoder ,start _fd .StartElement )error {if _gd .WAttr !=nil {start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0077\u006f\u0072:\u0077"},Value :_g .Sprintf ("\u0025\u0076",*_gd .WAttr )});
};if _gd .HAttr !=nil {start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0077\u006f\u0072:\u0068"},Value :_g .Sprintf ("\u0025\u0076",*_gd .HAttr )});};if _gd .PrstAttr !=ST_BevelPresetTypeUnset {_ea ,_feg :=_gd .PrstAttr .MarshalXMLAttr (_fd .Name {Local :"\u0077\u006f\u0072\u003a\u0070\u0072\u0073\u0074"});
if _feg !=nil {return _feg ;};start .Attr =append (start .Attr ,_ea );};e .EncodeToken (start );e .EncodeToken (_fd .EndElement {Name :start .Name });return nil ;};

// ValidateWithPath validates the CT_SRgbColor and its children, prefixing error messages with path
func (_ddfa *CT_SRgbColor )ValidateWithPath (path string )error {for _abfa ,_bda :=range _ddfa .EG_ColorTransform {if _ggb :=_bda .ValidateWithPath (_g .Sprintf ("\u0025s\u002f\u0045\u0047\u005f\u0043\u006f\u006c\u006f\u0072\u0054\u0072a\u006e\u0073\u0066\u006f\u0072\u006d\u005b\u0025\u0064\u005d",path ,_abfa ));
_ggb !=nil {return _ggb ;};};return nil ;};func NewEG_LineDashProperties ()*EG_LineDashProperties {_gddd :=&EG_LineDashProperties {};return _gddd ;};

// ValidateWithPath validates the EG_ShadePropertiesChoice and its children, prefixing error messages with path
func (_bcgfb *EG_ShadePropertiesChoice )ValidateWithPath (path string )error {if _bcgfb .Lin !=nil {if _cegb :=_bcgfb .Lin .ValidateWithPath (path +"\u002f\u004c\u0069\u006e");_cegb !=nil {return _cegb ;};};if _bcgfb .Path !=nil {if _fbdbf :=_bcgfb .Path .ValidateWithPath (path +"\u002f\u0050\u0061t\u0068");
_fbdbf !=nil {return _fbdbf ;};};return nil ;};

// ValidateWithPath validates the DiscardImageEditingData and its children, prefixing error messages with path
func (_bage *DiscardImageEditingData )ValidateWithPath (path string )error {if _bdbg :=_bage .CT_OnOff .ValidateWithPath (path );_bdbg !=nil {return _bdbg ;};return nil ;};func NewCT_SphereCoords ()*CT_SphereCoords {_aabf :=&CT_SphereCoords {};_aabf .LatAttr =0;
_aabf .LonAttr =0;_aabf .RevAttr =0;return _aabf ;};type DocId struct{CT_LongHexNumber };type AG_Parids struct{ParaIdAttr *string ;TextIdAttr *string ;};type CT_Shadow struct{BlurRadAttr *int64 ;DistAttr *int64 ;DirAttr *int32 ;SxAttr *_abg .ST_Percentage ;
SyAttr *_abg .ST_Percentage ;KxAttr *int32 ;KyAttr *int32 ;AlgnAttr ST_RectAlignment ;SrgbClr *CT_SRgbColor ;SchemeClr *CT_SchemeColor ;};

// ValidateWithPath validates the EG_ColorTransformChoice and its children, prefixing error messages with path
func (_agab *EG_ColorTransformChoice )ValidateWithPath (path string )error {if _agab .Tint !=nil {if _gfae :=_agab .Tint .ValidateWithPath (path +"\u002f\u0054\u0069n\u0074");_gfae !=nil {return _gfae ;};};if _agab .Shade !=nil {if _dfaa :=_agab .Shade .ValidateWithPath (path +"\u002f\u0053\u0068\u0061\u0064\u0065");
_dfaa !=nil {return _dfaa ;};};if _agab .Alpha !=nil {if _ebgc :=_agab .Alpha .ValidateWithPath (path +"\u002f\u0041\u006c\u0070\u0068\u0061");_ebgc !=nil {return _ebgc ;};};if _agab .HueMod !=nil {if _addg :=_agab .HueMod .ValidateWithPath (path +"\u002fH\u0075\u0065\u004d\u006f\u0064");
_addg !=nil {return _addg ;};};if _agab .Sat !=nil {if _adga :=_agab .Sat .ValidateWithPath (path +"\u002f\u0053\u0061\u0074");_adga !=nil {return _adga ;};};if _agab .SatOff !=nil {if _abc :=_agab .SatOff .ValidateWithPath (path +"\u002fS\u0061\u0074\u004f\u0066\u0066");
_abc !=nil {return _abc ;};};if _agab .SatMod !=nil {if _ebfac :=_agab .SatMod .ValidateWithPath (path +"\u002fS\u0061\u0074\u004d\u006f\u0064");_ebfac !=nil {return _ebfac ;};};if _agab .Lum !=nil {if _fdea :=_agab .Lum .ValidateWithPath (path +"\u002f\u004c\u0075\u006d");
_fdea !=nil {return _fdea ;};};if _agab .LumOff !=nil {if _gebg :=_agab .LumOff .ValidateWithPath (path +"\u002fL\u0075\u006d\u004f\u0066\u0066");_gebg !=nil {return _gebg ;};};if _agab .LumMod !=nil {if _gfgf :=_agab .LumMod .ValidateWithPath (path +"\u002fL\u0075\u006d\u004d\u006f\u0064");
_gfgf !=nil {return _gfgf ;};};return nil ;};func (_dfgf *EG_LineDashProperties )MarshalXML (e *_fd .Encoder ,start _fd .StartElement )error {if _dfgf .PrstDash !=nil {_bbec :=_fd .StartElement {Name :_fd .Name {Local :"\u0077\u006f\u0072:\u0070\u0072\u0073\u0074\u0044\u0061\u0073\u0068"}};
e .EncodeElement (_dfgf .PrstDash ,_bbec );};return nil ;};

// Validate validates the CT_OnOff and its children
func (_dbag *CT_OnOff )Validate ()error {return _dbag .ValidateWithPath ("\u0043\u0054\u005f\u004f\u006e\u004f\u0066\u0066");};const (ST_PresetMaterialTypeUnset ST_PresetMaterialType =0;ST_PresetMaterialTypeLegacyMatte ST_PresetMaterialType =1;ST_PresetMaterialTypeLegacyPlastic ST_PresetMaterialType =2;
ST_PresetMaterialTypeLegacyMetal ST_PresetMaterialType =3;ST_PresetMaterialTypeLegacyWireframe ST_PresetMaterialType =4;ST_PresetMaterialTypeMatte ST_PresetMaterialType =5;ST_PresetMaterialTypePlastic ST_PresetMaterialType =6;ST_PresetMaterialTypeMetal ST_PresetMaterialType =7;
ST_PresetMaterialTypeWarmMatte ST_PresetMaterialType =8;ST_PresetMaterialTypeTranslucentPowder ST_PresetMaterialType =9;ST_PresetMaterialTypePowder ST_PresetMaterialType =10;ST_PresetMaterialTypeDkEdge ST_PresetMaterialType =11;ST_PresetMaterialTypeSoftEdge ST_PresetMaterialType =12;
ST_PresetMaterialTypeClear ST_PresetMaterialType =13;ST_PresetMaterialTypeFlat ST_PresetMaterialType =14;ST_PresetMaterialTypeSoftmetal ST_PresetMaterialType =15;ST_PresetMaterialTypeNone ST_PresetMaterialType =16;);func NewEG_ColorTransformChoice ()*EG_ColorTransformChoice {_effg :=&EG_ColorTransformChoice {};
return _effg ;};func ParseUnionST_TextFontScalePercentOrPercentString (s string )(_abg .ST_TextFontScalePercentOrPercentString ,error ){return _abg .ParseUnionST_TextFontScalePercentOrPercentString (s );};

// Validate validates the CT_Color and its children
func (_cfb *CT_Color )Validate ()error {return _cfb .ValidateWithPath ("\u0043\u0054\u005f\u0043\u006f\u006c\u006f\u0072");};

// Validate validates the EG_ColorTransformChoice and its children
func (_edcg *EG_ColorTransformChoice )Validate ()error {return _edcg .ValidateWithPath ("\u0045\u0047\u005fCo\u006c\u006f\u0072\u0054\u0072\u0061\u006e\u0073\u0066\u006f\u0072\u006d\u0043\u0068\u006f\u0069\u0063\u0065");};func ParseUnionST_TextSpacingPercentOrPercentString (s string )(_abg .ST_TextSpacingPercentOrPercentString ,error ){return _abg .ParseUnionST_TextSpacingPercentOrPercentString (s );
};func (_fgga *CT_SdtCheckboxSymbol )MarshalXML (e *_fd .Encoder ,start _fd .StartElement )error {if _fgga .FontAttr !=nil {start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0077\u006f\u0072\u003a\u0066\u006f\u006e\u0074"},Value :_g .Sprintf ("\u0025\u0076",*_fgga .FontAttr )});
};if _fgga .ValAttr !=nil {start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0077o\u0072\u003a\u0076\u0061\u006c"},Value :_g .Sprintf ("\u0025\u0076",*_fgga .ValAttr )});};e .EncodeToken (start );e .EncodeToken (_fd .EndElement {Name :start .Name });
return nil ;};

// ValidateWithPath validates the CT_Scene3D and its children, prefixing error messages with path
func (_bcfee *CT_Scene3D )ValidateWithPath (path string )error {if _cdd :=_bcfee .Camera .ValidateWithPath (path +"\u002fC\u0061\u006d\u0065\u0072\u0061");_cdd !=nil {return _cdd ;};if _afbe :=_bcfee .LightRig .ValidateWithPath (path +"\u002fL\u0069\u0067\u0068\u0074\u0052\u0069g");
_afbe !=nil {return _afbe ;};return nil ;};type CT_Props3D struct{ExtrusionHAttr *int64 ;ContourWAttr *int64 ;PrstMaterialAttr ST_PresetMaterialType ;BevelT *CT_Bevel ;BevelB *CT_Bevel ;ExtrusionClr *CT_Color ;ContourClr *CT_Color ;};func (_eebc *ST_PathShadeType )UnmarshalXML (d *_fd .Decoder ,start _fd .StartElement )error {_fdbb ,_edf :=d .Token ();
if _edf !=nil {return _edf ;};if _dgbd ,_dfgfg :=_fdbb .(_fd .EndElement );_dfgfg &&_dgbd .Name ==start .Name {*_eebc =1;return nil ;};if _gfbf ,_gfff :=_fdbb .(_fd .CharData );!_gfff {return _g .Errorf ("\u0065\u0078\u0070\u0065\u0063\u0074\u0065\u0064\u0020\u0063\u0068a\u0072\u0020\u0064\u0061\u0074\u0061\u002c\u0020\u0067\u006ft\u0020\u0025\u0054",_fdbb );
}else {switch string (_gfbf ){case "":*_eebc =0;case "\u0073\u0068\u0061p\u0065":*_eebc =1;case "\u0063\u0069\u0072\u0063\u006c\u0065":*_eebc =2;case "\u0072\u0065\u0063\u0074":*_eebc =3;};};_fdbb ,_edf =d .Token ();if _edf !=nil {return _edf ;};if _dffbf ,_adgg :=_fdbb .(_fd .EndElement );
_adgg &&_dffbf .Name ==start .Name {return nil ;};return _g .Errorf ("\u0065\u0078\u0070\u0065c\u0074\u0065\u0064\u0020\u0065\u006e\u0064\u0020\u0065\u006ce\u006de\u006e\u0074\u002c\u0020\u0067\u006f\u0074 \u0025\u0076",_fdbb );};type ST_NumSpacing byte ;
const (ST_BevelPresetTypeUnset ST_BevelPresetType =0;ST_BevelPresetTypeRelaxedInset ST_BevelPresetType =1;ST_BevelPresetTypeCircle ST_BevelPresetType =2;ST_BevelPresetTypeSlope ST_BevelPresetType =3;ST_BevelPresetTypeCross ST_BevelPresetType =4;ST_BevelPresetTypeAngle ST_BevelPresetType =5;
ST_BevelPresetTypeSoftRound ST_BevelPresetType =6;ST_BevelPresetTypeConvex ST_BevelPresetType =7;ST_BevelPresetTypeCoolSlant ST_BevelPresetType =8;ST_BevelPresetTypeDivot ST_BevelPresetType =9;ST_BevelPresetTypeRiblet ST_BevelPresetType =10;ST_BevelPresetTypeHardEdge ST_BevelPresetType =11;
ST_BevelPresetTypeArtDeco ST_BevelPresetType =12;);

// ValidateWithPath validates the CustomXmlConflictInsRangeStart and its children, prefixing error messages with path
func (_eabf *CustomXmlConflictInsRangeStart )ValidateWithPath (path string )error {if _caeb :=_eabf .CT_TrackChange .ValidateWithPath (path );_caeb !=nil {return _caeb ;};return nil ;};const (ST_PenAlignmentUnset ST_PenAlignment =0;ST_PenAlignmentCtr ST_PenAlignment =1;
ST_PenAlignmentIn ST_PenAlignment =2;);type CT_GradientFillProperties struct{GsLst *CT_GradientStopList ;ShadePropertiesChoice *EG_ShadePropertiesChoice ;};func (_edgg *CT_StyleSet )MarshalXML (e *_fd .Encoder ,start _fd .StartElement )error {start .Attr =append (start .Attr ,_fd .Attr {Name :_fd .Name {Local :"\u0077\u006f\u0072\u003a\u0069\u0064"},Value :_g .Sprintf ("\u0025\u0076",_edgg .IdAttr )});
if _edgg .ValAttr !=ST_OnOffUnset {_effe ,_gbe :=_edgg .ValAttr .MarshalXMLAttr (_fd .Name {Local :"\u0077o\u0072\u003a\u0076\u0061\u006c"});if _gbe !=nil {return _gbe ;};start .Attr =append (start .Attr ,_effe );};e .EncodeToken (start );e .EncodeToken (_fd .EndElement {Name :start .Name });
return nil ;};func (_daac *ST_BevelPresetType )UnmarshalXML (d *_fd .Decoder ,start _fd .StartElement )error {_dgfe ,_fccf :=d .Token ();if _fccf !=nil {return _fccf ;};if _fffef ,_bdce :=_dgfe .(_fd .EndElement );_bdce &&_fffef .Name ==start .Name {*_daac =1;
return nil ;};if _baage ,_ffff :=_dgfe .(_fd .CharData );!_ffff {return _g .Errorf ("\u0065\u0078\u0070\u0065\u0063\u0074\u0065\u0064\u0020\u0063\u0068a\u0072\u0020\u0064\u0061\u0074\u0061\u002c\u0020\u0067\u006ft\u0020\u0025\u0054",_dgfe );}else {switch string (_baage ){case "":*_daac =0;
case "\u0072\u0065\u006ca\u0078\u0065\u0064\u0049\u006e\u0073\u0065\u0074":*_daac =1;case "\u0063\u0069\u0072\u0063\u006c\u0065":*_daac =2;case "\u0073\u006c\u006fp\u0065":*_daac =3;case "\u0063\u0072\u006fs\u0073":*_daac =4;case "\u0061\u006e\u0067l\u0065":*_daac =5;
case "\u0073o\u0066\u0074\u0052\u006f\u0075\u006ed":*_daac =6;case "\u0063\u006f\u006e\u0076\u0065\u0078":*_daac =7;case "\u0063o\u006f\u006c\u0053\u006c\u0061\u006et":*_daac =8;case "\u0064\u0069\u0076o\u0074":*_daac =9;case "\u0072\u0069\u0062\u006c\u0065\u0074":*_daac =10;
case "\u0068\u0061\u0072\u0064\u0045\u0064\u0067\u0065":*_daac =11;case "\u0061r\u0074\u0044\u0065\u0063\u006f":*_daac =12;};};_dgfe ,_fccf =d .Token ();if _fccf !=nil {return _fccf ;};if _eacae ,_dfgag :=_dgfe .(_fd .EndElement );_dfgag &&_eacae .Name ==start .Name {return nil ;
};return _g .Errorf ("\u0065\u0078\u0070\u0065c\u0074\u0065\u0064\u0020\u0065\u006e\u0064\u0020\u0065\u006ce\u006de\u006e\u0074\u002c\u0020\u0067\u006f\u0074 \u0025\u0076",_dgfe );};func (_dfdf *CT_TextOutlineEffect )UnmarshalXML (d *_fd .Decoder ,start _fd .StartElement )error {_dfdf .FillPropertiesChoice =NewEG_FillPropertiesChoice ();
_dfdf .LineJoinPropertiesChoice =NewEG_LineJoinPropertiesChoice ();for _ ,_cbfd :=range start .Attr {if _cbfd .Name .Local =="\u0077"{_egcd ,_gbad :=_a .ParseInt (_cbfd .Value ,10,32);if _gbad !=nil {return _gbad ;};_fbgf :=int32 (_egcd );_dfdf .WAttr =&_fbgf ;
continue ;};if _cbfd .Name .Local =="\u0063\u0061\u0070"{_dfdf .CapAttr .UnmarshalXMLAttr (_cbfd );continue ;};if _cbfd .Name .Local =="\u0063\u006d\u0070\u0064"{_dfdf .CmpdAttr .UnmarshalXMLAttr (_cbfd );continue ;};if _cbfd .Name .Local =="\u0061\u006c\u0067\u006e"{_dfdf .AlgnAttr .UnmarshalXMLAttr (_cbfd );
continue ;};};_gdd :for {_dca ,_dggc :=d .Token ();if _dggc !=nil {return _dggc ;};switch _dfafb :=_dca .(type ){case _fd .StartElement :switch _dfafb .Name {case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u006e\u006f\u0046\u0069\u006c\u006c"}:_dfdf .FillPropertiesChoice =NewEG_FillPropertiesChoice ();
if _debb :=d .DecodeElement (&_dfdf .FillPropertiesChoice .NoFill ,&_dfafb );_debb !=nil {return _debb ;};case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u0073o\u006c\u0069\u0064\u0046\u0069\u006cl"}:_dfdf .FillPropertiesChoice =NewEG_FillPropertiesChoice ();
if _eecc :=d .DecodeElement (&_dfdf .FillPropertiesChoice .SolidFill ,&_dfafb );_eecc !=nil {return _eecc ;};case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u0067\u0072\u0061\u0064\u0046\u0069\u006c\u006c"}:_dfdf .FillPropertiesChoice =NewEG_FillPropertiesChoice ();
if _bagd :=d .DecodeElement (&_dfdf .FillPropertiesChoice .GradFill ,&_dfafb );_bagd !=nil {return _bagd ;};case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u0070\u0072\u0073\u0074\u0044\u0061\u0073\u0068"}:_dfdf .PrstDash =NewCT_PresetLineDashProperties ();
if _gddc :=d .DecodeElement (_dfdf .PrstDash ,&_dfafb );_gddc !=nil {return _gddc ;};case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u0072\u006f\u0075n\u0064"}:_dfdf .LineJoinPropertiesChoice =NewEG_LineJoinPropertiesChoice ();
if _dgf :=d .DecodeElement (&_dfdf .LineJoinPropertiesChoice .Round ,&_dfafb );_dgf !=nil {return _dgf ;};case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u0062\u0065\u0076e\u006c"}:_dfdf .LineJoinPropertiesChoice =NewEG_LineJoinPropertiesChoice ();
if _bbbe :=d .DecodeElement (&_dfdf .LineJoinPropertiesChoice .Bevel ,&_dfafb );_bbbe !=nil {return _bbbe ;};case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u006d\u0069\u0074e\u0072"}:_dfdf .LineJoinPropertiesChoice =NewEG_LineJoinPropertiesChoice ();
if _ebge :=d .DecodeElement (&_dfdf .LineJoinPropertiesChoice .Miter ,&_dfafb );_ebge !=nil {return _ebge ;};default:_c .Log .Debug ("\u0073\u006b\u0069\u0070\u0070\u0069\u006eg\u0020\u0075\u006es\u0075\u0070\u0070o\u0072\u0074e\u0064\u0020\u0065\u006c\u0065\u006de\u006et \u006f\u006e\u0020\u0043\u0054\u005f\u0054\u0065\u0078\u0074\u004f\u0075\u0074\u006c\u0069\u006e\u0065\u0045\u0066\u0066\u0065\u0063\u0074\u0020\u0025\u0076",_dfafb .Name );
if _gdee :=d .Skip ();_gdee !=nil {return _gdee ;};};case _fd .EndElement :break _gdd ;case _fd .CharData :};};return nil ;};

// ValidateWithPath validates the EG_ColorChoice and its children, prefixing error messages with path
func (_ddce *EG_ColorChoice )ValidateWithPath (path string )error {if _ddce .SrgbClr !=nil {if _feae :=_ddce .SrgbClr .ValidateWithPath (path +"\u002f\u0053\u0072\u0067\u0062\u0043\u006c\u0072");_feae !=nil {return _feae ;};};if _ddce .SchemeClr !=nil {if _dddf :=_ddce .SchemeClr .ValidateWithPath (path +"\u002f\u0053\u0063\u0068\u0065\u006d\u0065\u0043\u006c\u0072");
_dddf !=nil {return _dddf ;};};return nil ;};func NewCT_TextOutlineEffect ()*CT_TextOutlineEffect {_aacf :=&CT_TextOutlineEffect {};_aacf .FillPropertiesChoice =NewEG_FillPropertiesChoice ();_aacf .LineJoinPropertiesChoice =NewEG_LineJoinPropertiesChoice ();
return _aacf ;};func (_gbdfb ST_Ligatures )MarshalXMLAttr (name _fd .Name )(_fd .Attr ,error ){_cdfc :=_fd .Attr {};_cdfc .Name =name ;switch _gbdfb {case ST_LigaturesUnset :_cdfc .Value ="";case ST_LigaturesNone :_cdfc .Value ="\u006e\u006f\u006e\u0065";
case ST_LigaturesStandard :_cdfc .Value ="\u0073\u0074\u0061\u006e\u0064\u0061\u0072\u0064";case ST_LigaturesContextual :_cdfc .Value ="\u0063\u006f\u006e\u0074\u0065\u0078\u0074\u0075\u0061\u006c";case ST_LigaturesHistorical :_cdfc .Value ="\u0068\u0069\u0073\u0074\u006f\u0072\u0069\u0063\u0061\u006c";
case ST_LigaturesDiscretional :_cdfc .Value ="\u0064\u0069\u0073c\u0072\u0065\u0074\u0069\u006f\u006e\u0061\u006c";case ST_LigaturesStandardContextual :_cdfc .Value ="\u0073t\u0061n\u0064\u0061\u0072\u0064\u0043o\u006e\u0074e\u0078\u0074\u0075\u0061\u006c";
case ST_LigaturesStandardHistorical :_cdfc .Value ="\u0073t\u0061n\u0064\u0061\u0072\u0064\u0048i\u0073\u0074o\u0072\u0069\u0063\u0061\u006c";case ST_LigaturesContextualHistorical :_cdfc .Value ="c\u006fn\u0074\u0065\u0078\u0074\u0075\u0061\u006c\u0048i\u0073\u0074\u006f\u0072ic\u0061\u006c";
case ST_LigaturesStandardDiscretional :_cdfc .Value ="s\u0074a\u006e\u0064\u0061\u0072\u0064\u0044\u0069\u0073c\u0072\u0065\u0074\u0069on\u0061\u006c";case ST_LigaturesContextualDiscretional :_cdfc .Value ="\u0063\u006f\u006e\u0074ex\u0074\u0075\u0061\u006c\u0044\u0069\u0073\u0063\u0072\u0065\u0074\u0069\u006f\u006ea\u006c";
case ST_LigaturesHistoricalDiscretional :_cdfc .Value ="\u0068\u0069\u0073\u0074or\u0069\u0063\u0061\u006c\u0044\u0069\u0073\u0063\u0072\u0065\u0074\u0069\u006f\u006ea\u006c";case ST_LigaturesStandardContextualHistorical :_cdfc .Value ="\u0073\u0074\u0061\u006ed\u0061\u0072\u0064\u0043\u006f\u006e\u0074\u0065\u0078\u0074u\u0061l\u0048\u0069\u0073\u0074\u006f\u0072\u0069c\u0061\u006c";
case ST_LigaturesStandardContextualDiscretional :_cdfc .Value ="\u0073\u0074\u0061\u006e\u0064\u0061\u0072\u0064\u0043\u006fn\u0074\u0065\u0078\u0074\u0075\u0061\u006cD\u0069\u0073\u0063\u0072\u0065\u0074\u0069\u006f\u006e\u0061\u006c";case ST_LigaturesStandardHistoricalDiscretional :_cdfc .Value ="\u0073\u0074\u0061\u006e\u0064\u0061\u0072\u0064\u0048\u0069s\u0074\u006f\u0072\u0069\u0063\u0061\u006cD\u0069\u0073\u0063\u0072\u0065\u0074\u0069\u006f\u006e\u0061\u006c";
case ST_LigaturesContextualHistoricalDiscretional :_cdfc .Value ="\u0063\u006f\u006e\u0074\u0065\u0078\u0074\u0075\u0061\u006c\u0048\u0069\u0073\u0074\u006fr\u0069c\u0061\u006c\u0044\u0069\u0073\u0063\u0072\u0065\u0074\u0069\u006f\u006e\u0061\u006c";case ST_LigaturesAll :_cdfc .Value ="\u0061\u006c\u006c";
};return _cdfc ,nil ;};

// Validate validates the CT_LineJoinMiterProperties and its children
func (_cfe *CT_LineJoinMiterProperties )Validate ()error {return _cfe .ValidateWithPath ("\u0043\u0054\u005f\u004c\u0069\u006e\u0065\u004a\u006f\u0069\u006eM\u0069\u0074\u0065\u0072\u0050\u0072\u006f\u0070\u0065\u0072t\u0069\u0065\u0073");};func (_ecgd ST_LineCap )Validate ()error {return _ecgd .ValidateWithPath ("")};
const (ST_CompoundLineUnset ST_CompoundLine =0;ST_CompoundLineSng ST_CompoundLine =1;ST_CompoundLineDbl ST_CompoundLine =2;ST_CompoundLineThickThin ST_CompoundLine =3;ST_CompoundLineThinThick ST_CompoundLine =4;ST_CompoundLineTri ST_CompoundLine =5;);

// Validate validates the EG_RunLevelConflicts and its children
func (_cfdf *EG_RunLevelConflicts )Validate ()error {return _cfdf .ValidateWithPath ("E\u0047_\u0052\u0075\u006e\u004c\u0065\u0076\u0065\u006cC\u006f\u006e\u0066\u006cic\u0074\u0073");};func (_agfa ST_PresetCameraType )String ()string {switch _agfa {case 0:return "";
case 1:return "l\u0065g\u0061\u0063\u0079\u004f\u0062\u006c\u0069\u0071u\u0065\u0054\u006f\u0070Le\u0066\u0074";case 2:return "\u006c\u0065g\u0061\u0063\u0079O\u0062\u006c\u0069\u0071\u0075\u0065\u0054\u006f\u0070";case 3:return "l\u0065\u0067\u0061\u0063yO\u0062l\u0069\u0071\u0075\u0065\u0054o\u0070\u0052\u0069\u0067\u0068\u0074";
case 4:return "\u006c\u0065\u0067\u0061\u0063\u0079\u004f\u0062\u006c\u0069\u0071\u0075e\u004c\u0065\u0066\u0074";case 5:return "\u006ce\u0067a\u0063\u0079\u004f\u0062\u006ci\u0071\u0075e\u0046\u0072\u006f\u006e\u0074";case 6:return "\u006ce\u0067a\u0063\u0079\u004f\u0062\u006ci\u0071\u0075e\u0052\u0069\u0067\u0068\u0074";
case 7:return "\u006c\u0065\u0067ac\u0079\u004f\u0062\u006c\u0069\u0071\u0075\u0065\u0042\u006f\u0074\u0074\u006f\u006d\u004c\u0065\u0066\u0074";case 8:return "\u006c\u0065\u0067\u0061cy\u004f\u0062\u006c\u0069\u0071\u0075\u0065\u0042\u006f\u0074\u0074\u006f\u006d";
case 9:return "\u006ce\u0067\u0061\u0063\u0079\u004f\u0062\u006c\u0069\u0071\u0075\u0065B\u006f\u0074\u0074\u006f\u006d\u0052\u0069\u0067\u0068\u0074";case 10:return "\u006ce\u0067\u0061\u0063\u0079\u0050\u0065\u0072\u0073\u0070\u0065\u0063t\u0069\u0076\u0065\u0054\u006f\u0070\u004c\u0065\u0066\u0074";
case 11:return "l\u0065g\u0061\u0063\u0079\u0050\u0065\u0072\u0073\u0070e\u0063\u0074\u0069\u0076eT\u006f\u0070";case 12:return "\u006ce\u0067\u0061\u0063\u0079P\u0065\u0072\u0073\u0070\u0065c\u0074i\u0076e\u0054\u006f\u0070\u0052\u0069\u0067\u0068t";case 13:return "l\u0065\u0067\u0061\u0063yP\u0065r\u0073\u0070\u0065\u0063\u0074i\u0076\u0065\u004c\u0065\u0066\u0074";
case 14:return "\u006c\u0065\u0067\u0061cy\u0050\u0065\u0072\u0073\u0070\u0065\u0063\u0074\u0069\u0076\u0065\u0046\u0072\u006fn\u0074";case 15:return "\u006c\u0065\u0067\u0061cy\u0050\u0065\u0072\u0073\u0070\u0065\u0063\u0074\u0069\u0076\u0065\u0052\u0069\u0067h\u0074";
case 16:return "l\u0065\u0067\u0061\u0063\u0079\u0050e\u0072\u0073\u0070\u0065\u0063\u0074\u0069\u0076\u0065B\u006f\u0074\u0074o\u006dL\u0065\u0066\u0074";case 17:return "\u006c\u0065\u0067ac\u0079\u0050\u0065\u0072\u0073\u0070\u0065\u0063\u0074\u0069\u0076\u0065\u0042\u006f\u0074\u0074\u006f\u006d";
case 18:return "\u006c\u0065\u0067\u0061c\u0079\u0050\u0065\u0072\u0073\u0070\u0065\u0063\u0074\u0069v\u0065B\u006f\u0074\u0074\u006f\u006d\u0052\u0069g\u0068\u0074";case 19:return "\u006f\u0072\u0074\u0068\u006f\u0067\u0072\u0061\u0070\u0068\u0069\u0063F\u0072\u006f\u006e\u0074";
case 20:return "\u0069\u0073\u006f\u006d\u0065\u0074\u0072\u0069\u0063T\u006f\u0070\u0055\u0070";case 21:return "\u0069\u0073o\u006d\u0065\u0074r\u0069\u0063\u0054\u006f\u0070\u0044\u006f\u0077\u006e";case 22:return "\u0069\u0073\u006f\u006d\u0065\u0074\u0072\u0069\u0063\u0042\u006f\u0074t\u006f\u006d\u0055\u0070";
case 23:return "\u0069\u0073\u006f\u006det\u0072\u0069\u0063\u0042\u006f\u0074\u0074\u006f\u006d\u0044\u006f\u0077\u006e";case 24:return "\u0069s\u006fm\u0065\u0074\u0072\u0069\u0063\u004c\u0065\u0066\u0074\u0055\u0070";case 25:return "\u0069\u0073\u006f\u006d\u0065\u0074\u0072\u0069\u0063\u004c\u0065\u0066t\u0044\u006f\u0077\u006e";
case 26:return "\u0069\u0073o\u006d\u0065\u0074r\u0069\u0063\u0052\u0069\u0067\u0068\u0074\u0055\u0070";case 27:return "\u0069s\u006fm\u0065\u0074\u0072\u0069\u0063R\u0069\u0067h\u0074\u0044\u006f\u0077\u006e";case 28:return "i\u0073\u006f\u006d\u0065tr\u0069c\u004f\u0066\u0066\u0041\u0078i\u0073\u0031\u004c\u0065\u0066\u0074";
case 29:return "\u0069\u0073\u006f\u006det\u0072\u0069\u0063\u004f\u0066\u0066\u0041\u0078\u0069\u0073\u0031\u0052\u0069\u0067h\u0074";case 30:return "i\u0073o\u006d\u0065\u0074\u0072\u0069\u0063\u004f\u0066f\u0041\u0078\u0069\u00731T\u006f\u0070";case 31:return "i\u0073\u006f\u006d\u0065tr\u0069c\u004f\u0066\u0066\u0041\u0078i\u0073\u0032\u004c\u0065\u0066\u0074";
case 32:return "\u0069\u0073\u006f\u006det\u0072\u0069\u0063\u004f\u0066\u0066\u0041\u0078\u0069\u0073\u0032\u0052\u0069\u0067h\u0074";case 33:return "i\u0073o\u006d\u0065\u0074\u0072\u0069\u0063\u004f\u0066f\u0041\u0078\u0069\u00732T\u006f\u0070";case 34:return "i\u0073\u006f\u006d\u0065tr\u0069c\u004f\u0066\u0066\u0041\u0078i\u0073\u0033\u004c\u0065\u0066\u0074";
case 35:return "\u0069\u0073\u006f\u006det\u0072\u0069\u0063\u004f\u0066\u0066\u0041\u0078\u0069\u0073\u0033\u0052\u0069\u0067h\u0074";case 36:return "\u0069\u0073\u006fme\u0074\u0072\u0069\u0063\u004f\u0066\u0066\u0041\u0078\u0069\u0073\u0033\u0042\u006f\u0074\u0074\u006f\u006d";
case 37:return "i\u0073\u006f\u006d\u0065tr\u0069c\u004f\u0066\u0066\u0041\u0078i\u0073\u0034\u004c\u0065\u0066\u0074";case 38:return "\u0069\u0073\u006f\u006det\u0072\u0069\u0063\u004f\u0066\u0066\u0041\u0078\u0069\u0073\u0034\u0052\u0069\u0067h\u0074";
case 39:return "\u0069\u0073\u006fme\u0074\u0072\u0069\u0063\u004f\u0066\u0066\u0041\u0078\u0069\u0073\u0034\u0042\u006f\u0074\u0074\u006f\u006d";case 40:return "\u006f\u0062\u006c\u0069\u0071\u0075\u0065\u0054\u006fp\u004c\u0065\u0066\u0074";case 41:return "\u006f\u0062\u006c\u0069\u0071\u0075\u0065\u0054\u006f\u0070";
case 42:return "\u006fb\u006ci\u0071\u0075\u0065\u0054\u006f\u0070\u0052\u0069\u0067\u0068\u0074";case 43:return "o\u0062\u006c\u0069\u0071\u0075\u0065\u004c\u0065\u0066\u0074";case 44:return "\u006f\u0062\u006ci\u0071\u0075\u0065\u0052\u0069\u0067\u0068\u0074";
case 45:return "\u006f\u0062\u006c\u0069\u0071\u0075\u0065\u0042\u006f\u0074\u0074\u006fm\u004c\u0065\u0066\u0074";case 46:return "\u006f\u0062\u006c\u0069\u0071\u0075\u0065\u0042\u006f\u0074\u0074\u006f\u006d";case 47:return "\u006fb\u006ci\u0071\u0075\u0065\u0042\u006ft\u0074\u006fm\u0052\u0069\u0067\u0068\u0074";
case 48:return "\u0070\u0065r\u0073\u0070\u0065c\u0074\u0069\u0076\u0065\u0046\u0072\u006f\u006e\u0074";case 49:return "\u0070e\u0072s\u0070\u0065\u0063\u0074\u0069\u0076\u0065\u004c\u0065\u0066\u0074";case 50:return "\u0070\u0065r\u0073\u0070\u0065c\u0074\u0069\u0076\u0065\u0052\u0069\u0067\u0068\u0074";
case 51:return "\u0070\u0065r\u0073\u0070\u0065c\u0074\u0069\u0076\u0065\u0041\u0062\u006f\u0076\u0065";case 52:return "\u0070\u0065r\u0073\u0070\u0065c\u0074\u0069\u0076\u0065\u0042\u0065\u006c\u006f\u0077";case 53:return "\u0070\u0065\u0072\u0073\u0070\u0065\u0063\u0074\u0069\u0076\u0065A\u0062\u006f\u0076\u0065\u004c\u0065\u0066\u0074\u0046\u0061c\u0069\u006e\u0067";
case 54:return "p\u0065\u0072\u0073\u0070\u0065\u0063t\u0069\u0076\u0065\u0041\u0062\u006f\u0076\u0065\u0052i\u0067\u0068\u0074F\u0061c\u0069\u006e\u0067";case 55:return "\u0070\u0065\u0072\u0073\u0070\u0065\u0063\u0074\u0069\u0076\u0065\u0043\u006f\u006e\u0074r\u0061s\u0074\u0069\u006e\u0067\u004c\u0065\u0066\u0074\u0046\u0061\u0063\u0069\u006e\u0067";
case 56:return "\u0070\u0065\u0072\u0073\u0070\u0065c\u0074\u0069\u0076\u0065\u0043\u006f\u006e\u0074\u0072\u0061\u0073\u0074\u0069n\u0067\u0052\u0069\u0067\u0068\u0074\u0046a\u0063\u0069\u006e\u0067";case 57:return "p\u0065\u0072\u0073\u0070\u0065\u0063t\u0069\u0076\u0065\u0048\u0065\u0072\u006f\u0069\u0063L\u0065\u0066\u0074F\u0061c\u0069\u006e\u0067";
case 58:return "\u0070\u0065\u0072\u0073p\u0065\u0063\u0074\u0069\u0076\u0065\u0048\u0065\u0072\u006fi\u0063R\u0069\u0067\u0068\u0074\u0046\u0061\u0063i\u006e\u0067";case 59:return "\u0070\u0065\u0072sp\u0065\u0063\u0074\u0069\u0076\u0065\u0048\u0065\u0072o\u0069c\u0045x\u0074r\u0065\u006d\u0065\u004c\u0065\u0066\u0074\u0046\u0061\u0063\u0069\u006e\u0067";
case 60:return "p\u0065\u0072\u0073\u0070\u0065\u0063t\u0069\u0076\u0065\u0048\u0065\u0072o\u0069\u0063\u0045\u0078\u0074\u0072\u0065m\u0065\u0052\u0069\u0067\u0068\u0074\u0046\u0061\u0063\u0069n\u0067";case 61:return "\u0070e\u0072s\u0070\u0065\u0063\u0074\u0069v\u0065\u0052e\u006c\u0061\u0078\u0065\u0064";
case 62:return "\u0070\u0065\u0072\u0073p\u0065\u0063\u0074\u0069\u0076\u0065\u0052\u0065\u006c\u0061x\u0065d\u004d\u006f\u0064\u0065\u0072\u0061\u0074e\u006c\u0079";};return "";};

// Validate validates the EG_ColorTransform and its children
func (_bfab *EG_ColorTransform )Validate ()error {return _bfab .ValidateWithPath ("\u0045\u0047\u005f\u0043\u006f\u006c\u006f\u0072\u0054\u0072\u0061\u006es\u0066\u006f\u0072\u006d");};func (_gffaf ST_PresetCameraType )MarshalXMLAttr (name _fd .Name )(_fd .Attr ,error ){_gbfga :=_fd .Attr {};
_gbfga .Name =name ;switch _gffaf {case ST_PresetCameraTypeUnset :_gbfga .Value ="";case ST_PresetCameraTypeLegacyObliqueTopLeft :_gbfga .Value ="l\u0065g\u0061\u0063\u0079\u004f\u0062\u006c\u0069\u0071u\u0065\u0054\u006f\u0070Le\u0066\u0074";case ST_PresetCameraTypeLegacyObliqueTop :_gbfga .Value ="\u006c\u0065g\u0061\u0063\u0079O\u0062\u006c\u0069\u0071\u0075\u0065\u0054\u006f\u0070";
case ST_PresetCameraTypeLegacyObliqueTopRight :_gbfga .Value ="l\u0065\u0067\u0061\u0063yO\u0062l\u0069\u0071\u0075\u0065\u0054o\u0070\u0052\u0069\u0067\u0068\u0074";case ST_PresetCameraTypeLegacyObliqueLeft :_gbfga .Value ="\u006c\u0065\u0067\u0061\u0063\u0079\u004f\u0062\u006c\u0069\u0071\u0075e\u004c\u0065\u0066\u0074";
case ST_PresetCameraTypeLegacyObliqueFront :_gbfga .Value ="\u006ce\u0067a\u0063\u0079\u004f\u0062\u006ci\u0071\u0075e\u0046\u0072\u006f\u006e\u0074";case ST_PresetCameraTypeLegacyObliqueRight :_gbfga .Value ="\u006ce\u0067a\u0063\u0079\u004f\u0062\u006ci\u0071\u0075e\u0052\u0069\u0067\u0068\u0074";
case ST_PresetCameraTypeLegacyObliqueBottomLeft :_gbfga .Value ="\u006c\u0065\u0067ac\u0079\u004f\u0062\u006c\u0069\u0071\u0075\u0065\u0042\u006f\u0074\u0074\u006f\u006d\u004c\u0065\u0066\u0074";case ST_PresetCameraTypeLegacyObliqueBottom :_gbfga .Value ="\u006c\u0065\u0067\u0061cy\u004f\u0062\u006c\u0069\u0071\u0075\u0065\u0042\u006f\u0074\u0074\u006f\u006d";
case ST_PresetCameraTypeLegacyObliqueBottomRight :_gbfga .Value ="\u006ce\u0067\u0061\u0063\u0079\u004f\u0062\u006c\u0069\u0071\u0075\u0065B\u006f\u0074\u0074\u006f\u006d\u0052\u0069\u0067\u0068\u0074";case ST_PresetCameraTypeLegacyPerspectiveTopLeft :_gbfga .Value ="\u006ce\u0067\u0061\u0063\u0079\u0050\u0065\u0072\u0073\u0070\u0065\u0063t\u0069\u0076\u0065\u0054\u006f\u0070\u004c\u0065\u0066\u0074";
case ST_PresetCameraTypeLegacyPerspectiveTop :_gbfga .Value ="l\u0065g\u0061\u0063\u0079\u0050\u0065\u0072\u0073\u0070e\u0063\u0074\u0069\u0076eT\u006f\u0070";case ST_PresetCameraTypeLegacyPerspectiveTopRight :_gbfga .Value ="\u006ce\u0067\u0061\u0063\u0079P\u0065\u0072\u0073\u0070\u0065c\u0074i\u0076e\u0054\u006f\u0070\u0052\u0069\u0067\u0068t";
case ST_PresetCameraTypeLegacyPerspectiveLeft :_gbfga .Value ="l\u0065\u0067\u0061\u0063yP\u0065r\u0073\u0070\u0065\u0063\u0074i\u0076\u0065\u004c\u0065\u0066\u0074";case ST_PresetCameraTypeLegacyPerspectiveFront :_gbfga .Value ="\u006c\u0065\u0067\u0061cy\u0050\u0065\u0072\u0073\u0070\u0065\u0063\u0074\u0069\u0076\u0065\u0046\u0072\u006fn\u0074";
case ST_PresetCameraTypeLegacyPerspectiveRight :_gbfga .Value ="\u006c\u0065\u0067\u0061cy\u0050\u0065\u0072\u0073\u0070\u0065\u0063\u0074\u0069\u0076\u0065\u0052\u0069\u0067h\u0074";case ST_PresetCameraTypeLegacyPerspectiveBottomLeft :_gbfga .Value ="l\u0065\u0067\u0061\u0063\u0079\u0050e\u0072\u0073\u0070\u0065\u0063\u0074\u0069\u0076\u0065B\u006f\u0074\u0074o\u006dL\u0065\u0066\u0074";
case ST_PresetCameraTypeLegacyPerspectiveBottom :_gbfga .Value ="\u006c\u0065\u0067ac\u0079\u0050\u0065\u0072\u0073\u0070\u0065\u0063\u0074\u0069\u0076\u0065\u0042\u006f\u0074\u0074\u006f\u006d";case ST_PresetCameraTypeLegacyPerspectiveBottomRight :_gbfga .Value ="\u006c\u0065\u0067\u0061c\u0079\u0050\u0065\u0072\u0073\u0070\u0065\u0063\u0074\u0069v\u0065B\u006f\u0074\u0074\u006f\u006d\u0052\u0069g\u0068\u0074";
case ST_PresetCameraTypeOrthographicFront :_gbfga .Value ="\u006f\u0072\u0074\u0068\u006f\u0067\u0072\u0061\u0070\u0068\u0069\u0063F\u0072\u006f\u006e\u0074";case ST_PresetCameraTypeIsometricTopUp :_gbfga .Value ="\u0069\u0073\u006f\u006d\u0065\u0074\u0072\u0069\u0063T\u006f\u0070\u0055\u0070";
case ST_PresetCameraTypeIsometricTopDown :_gbfga .Value ="\u0069\u0073o\u006d\u0065\u0074r\u0069\u0063\u0054\u006f\u0070\u0044\u006f\u0077\u006e";case ST_PresetCameraTypeIsometricBottomUp :_gbfga .Value ="\u0069\u0073\u006f\u006d\u0065\u0074\u0072\u0069\u0063\u0042\u006f\u0074t\u006f\u006d\u0055\u0070";
case ST_PresetCameraTypeIsometricBottomDown :_gbfga .Value ="\u0069\u0073\u006f\u006det\u0072\u0069\u0063\u0042\u006f\u0074\u0074\u006f\u006d\u0044\u006f\u0077\u006e";case ST_PresetCameraTypeIsometricLeftUp :_gbfga .Value ="\u0069s\u006fm\u0065\u0074\u0072\u0069\u0063\u004c\u0065\u0066\u0074\u0055\u0070";
case ST_PresetCameraTypeIsometricLeftDown :_gbfga .Value ="\u0069\u0073\u006f\u006d\u0065\u0074\u0072\u0069\u0063\u004c\u0065\u0066t\u0044\u006f\u0077\u006e";case ST_PresetCameraTypeIsometricRightUp :_gbfga .Value ="\u0069\u0073o\u006d\u0065\u0074r\u0069\u0063\u0052\u0069\u0067\u0068\u0074\u0055\u0070";
case ST_PresetCameraTypeIsometricRightDown :_gbfga .Value ="\u0069s\u006fm\u0065\u0074\u0072\u0069\u0063R\u0069\u0067h\u0074\u0044\u006f\u0077\u006e";case ST_PresetCameraTypeIsometricOffAxis1Left :_gbfga .Value ="i\u0073\u006f\u006d\u0065tr\u0069c\u004f\u0066\u0066\u0041\u0078i\u0073\u0031\u004c\u0065\u0066\u0074";
case ST_PresetCameraTypeIsometricOffAxis1Right :_gbfga .Value ="\u0069\u0073\u006f\u006det\u0072\u0069\u0063\u004f\u0066\u0066\u0041\u0078\u0069\u0073\u0031\u0052\u0069\u0067h\u0074";case ST_PresetCameraTypeIsometricOffAxis1Top :_gbfga .Value ="i\u0073o\u006d\u0065\u0074\u0072\u0069\u0063\u004f\u0066f\u0041\u0078\u0069\u00731T\u006f\u0070";
case ST_PresetCameraTypeIsometricOffAxis2Left :_gbfga .Value ="i\u0073\u006f\u006d\u0065tr\u0069c\u004f\u0066\u0066\u0041\u0078i\u0073\u0032\u004c\u0065\u0066\u0074";case ST_PresetCameraTypeIsometricOffAxis2Right :_gbfga .Value ="\u0069\u0073\u006f\u006det\u0072\u0069\u0063\u004f\u0066\u0066\u0041\u0078\u0069\u0073\u0032\u0052\u0069\u0067h\u0074";
case ST_PresetCameraTypeIsometricOffAxis2Top :_gbfga .Value ="i\u0073o\u006d\u0065\u0074\u0072\u0069\u0063\u004f\u0066f\u0041\u0078\u0069\u00732T\u006f\u0070";case ST_PresetCameraTypeIsometricOffAxis3Left :_gbfga .Value ="i\u0073\u006f\u006d\u0065tr\u0069c\u004f\u0066\u0066\u0041\u0078i\u0073\u0033\u004c\u0065\u0066\u0074";
case ST_PresetCameraTypeIsometricOffAxis3Right :_gbfga .Value ="\u0069\u0073\u006f\u006det\u0072\u0069\u0063\u004f\u0066\u0066\u0041\u0078\u0069\u0073\u0033\u0052\u0069\u0067h\u0074";case ST_PresetCameraTypeIsometricOffAxis3Bottom :_gbfga .Value ="\u0069\u0073\u006fme\u0074\u0072\u0069\u0063\u004f\u0066\u0066\u0041\u0078\u0069\u0073\u0033\u0042\u006f\u0074\u0074\u006f\u006d";
case ST_PresetCameraTypeIsometricOffAxis4Left :_gbfga .Value ="i\u0073\u006f\u006d\u0065tr\u0069c\u004f\u0066\u0066\u0041\u0078i\u0073\u0034\u004c\u0065\u0066\u0074";case ST_PresetCameraTypeIsometricOffAxis4Right :_gbfga .Value ="\u0069\u0073\u006f\u006det\u0072\u0069\u0063\u004f\u0066\u0066\u0041\u0078\u0069\u0073\u0034\u0052\u0069\u0067h\u0074";
case ST_PresetCameraTypeIsometricOffAxis4Bottom :_gbfga .Value ="\u0069\u0073\u006fme\u0074\u0072\u0069\u0063\u004f\u0066\u0066\u0041\u0078\u0069\u0073\u0034\u0042\u006f\u0074\u0074\u006f\u006d";case ST_PresetCameraTypeObliqueTopLeft :_gbfga .Value ="\u006f\u0062\u006c\u0069\u0071\u0075\u0065\u0054\u006fp\u004c\u0065\u0066\u0074";
case ST_PresetCameraTypeObliqueTop :_gbfga .Value ="\u006f\u0062\u006c\u0069\u0071\u0075\u0065\u0054\u006f\u0070";case ST_PresetCameraTypeObliqueTopRight :_gbfga .Value ="\u006fb\u006ci\u0071\u0075\u0065\u0054\u006f\u0070\u0052\u0069\u0067\u0068\u0074";
case ST_PresetCameraTypeObliqueLeft :_gbfga .Value ="o\u0062\u006c\u0069\u0071\u0075\u0065\u004c\u0065\u0066\u0074";case ST_PresetCameraTypeObliqueRight :_gbfga .Value ="\u006f\u0062\u006ci\u0071\u0075\u0065\u0052\u0069\u0067\u0068\u0074";case ST_PresetCameraTypeObliqueBottomLeft :_gbfga .Value ="\u006f\u0062\u006c\u0069\u0071\u0075\u0065\u0042\u006f\u0074\u0074\u006fm\u004c\u0065\u0066\u0074";
case ST_PresetCameraTypeObliqueBottom :_gbfga .Value ="\u006f\u0062\u006c\u0069\u0071\u0075\u0065\u0042\u006f\u0074\u0074\u006f\u006d";case ST_PresetCameraTypeObliqueBottomRight :_gbfga .Value ="\u006fb\u006ci\u0071\u0075\u0065\u0042\u006ft\u0074\u006fm\u0052\u0069\u0067\u0068\u0074";
case ST_PresetCameraTypePerspectiveFront :_gbfga .Value ="\u0070\u0065r\u0073\u0070\u0065c\u0074\u0069\u0076\u0065\u0046\u0072\u006f\u006e\u0074";case ST_PresetCameraTypePerspectiveLeft :_gbfga .Value ="\u0070e\u0072s\u0070\u0065\u0063\u0074\u0069\u0076\u0065\u004c\u0065\u0066\u0074";
case ST_PresetCameraTypePerspectiveRight :_gbfga .Value ="\u0070\u0065r\u0073\u0070\u0065c\u0074\u0069\u0076\u0065\u0052\u0069\u0067\u0068\u0074";case ST_PresetCameraTypePerspectiveAbove :_gbfga .Value ="\u0070\u0065r\u0073\u0070\u0065c\u0074\u0069\u0076\u0065\u0041\u0062\u006f\u0076\u0065";
case ST_PresetCameraTypePerspectiveBelow :_gbfga .Value ="\u0070\u0065r\u0073\u0070\u0065c\u0074\u0069\u0076\u0065\u0042\u0065\u006c\u006f\u0077";case ST_PresetCameraTypePerspectiveAboveLeftFacing :_gbfga .Value ="\u0070\u0065\u0072\u0073\u0070\u0065\u0063\u0074\u0069\u0076\u0065A\u0062\u006f\u0076\u0065\u004c\u0065\u0066\u0074\u0046\u0061c\u0069\u006e\u0067";
case ST_PresetCameraTypePerspectiveAboveRightFacing :_gbfga .Value ="p\u0065\u0072\u0073\u0070\u0065\u0063t\u0069\u0076\u0065\u0041\u0062\u006f\u0076\u0065\u0052i\u0067\u0068\u0074F\u0061c\u0069\u006e\u0067";case ST_PresetCameraTypePerspectiveContrastingLeftFacing :_gbfga .Value ="\u0070\u0065\u0072\u0073\u0070\u0065\u0063\u0074\u0069\u0076\u0065\u0043\u006f\u006e\u0074r\u0061s\u0074\u0069\u006e\u0067\u004c\u0065\u0066\u0074\u0046\u0061\u0063\u0069\u006e\u0067";
case ST_PresetCameraTypePerspectiveContrastingRightFacing :_gbfga .Value ="\u0070\u0065\u0072\u0073\u0070\u0065c\u0074\u0069\u0076\u0065\u0043\u006f\u006e\u0074\u0072\u0061\u0073\u0074\u0069n\u0067\u0052\u0069\u0067\u0068\u0074\u0046a\u0063\u0069\u006e\u0067";
case ST_PresetCameraTypePerspectiveHeroicLeftFacing :_gbfga .Value ="p\u0065\u0072\u0073\u0070\u0065\u0063t\u0069\u0076\u0065\u0048\u0065\u0072\u006f\u0069\u0063L\u0065\u0066\u0074F\u0061c\u0069\u006e\u0067";case ST_PresetCameraTypePerspectiveHeroicRightFacing :_gbfga .Value ="\u0070\u0065\u0072\u0073p\u0065\u0063\u0074\u0069\u0076\u0065\u0048\u0065\u0072\u006fi\u0063R\u0069\u0067\u0068\u0074\u0046\u0061\u0063i\u006e\u0067";
case ST_PresetCameraTypePerspectiveHeroicExtremeLeftFacing :_gbfga .Value ="\u0070\u0065\u0072sp\u0065\u0063\u0074\u0069\u0076\u0065\u0048\u0065\u0072o\u0069c\u0045x\u0074r\u0065\u006d\u0065\u004c\u0065\u0066\u0074\u0046\u0061\u0063\u0069\u006e\u0067";
case ST_PresetCameraTypePerspectiveHeroicExtremeRightFacing :_gbfga .Value ="p\u0065\u0072\u0073\u0070\u0065\u0063t\u0069\u0076\u0065\u0048\u0065\u0072o\u0069\u0063\u0045\u0078\u0074\u0072\u0065m\u0065\u0052\u0069\u0067\u0068\u0074\u0046\u0061\u0063\u0069n\u0067";
case ST_PresetCameraTypePerspectiveRelaxed :_gbfga .Value ="\u0070e\u0072s\u0070\u0065\u0063\u0074\u0069v\u0065\u0052e\u006c\u0061\u0078\u0065\u0064";case ST_PresetCameraTypePerspectiveRelaxedModerately :_gbfga .Value ="\u0070\u0065\u0072\u0073p\u0065\u0063\u0074\u0069\u0076\u0065\u0052\u0065\u006c\u0061x\u0065d\u004d\u006f\u0064\u0065\u0072\u0061\u0074e\u006c\u0079";
};return _gbfga ,nil ;};func (_ggfe ST_LightRigDirection )Validate ()error {return _ggfe .ValidateWithPath ("")};func NewCT_NumForm ()*CT_NumForm {_bgaed :=&CT_NumForm {};_bgaed .ValAttr =ST_NumForm (1);return _bgaed ;};func (_dag *CT_Camera )UnmarshalXML (d *_fd .Decoder ,start _fd .StartElement )error {_dag .PrstAttr =ST_PresetCameraType (1);
for _ ,_bfd :=range start .Attr {if _bfd .Name .Local =="\u0070\u0072\u0073\u0074"{_dag .PrstAttr .UnmarshalXMLAttr (_bfd );continue ;};};for {_fc ,_dg :=d .Token ();if _dg !=nil {return _g .Errorf ("p\u0061\u0072\u0073\u0069ng\u0020C\u0054\u005f\u0043\u0061\u006de\u0072\u0061\u003a\u0020\u0025\u0073",_dg );
};if _ddc ,_ac :=_fc .(_fd .EndElement );_ac &&_ddc .Name ==start .Name {break ;};};return nil ;};func (_facb ST_PathShadeType )String ()string {switch _facb {case 0:return "";case 1:return "\u0073\u0068\u0061p\u0065";case 2:return "\u0063\u0069\u0072\u0063\u006c\u0065";
case 3:return "\u0072\u0065\u0063\u0074";};return "";};func (_bada *ST_Ligatures )UnmarshalXMLAttr (attr _fd .Attr )error {switch attr .Value {case "":*_bada =0;case "\u006e\u006f\u006e\u0065":*_bada =1;case "\u0073\u0074\u0061\u006e\u0064\u0061\u0072\u0064":*_bada =2;
case "\u0063\u006f\u006e\u0074\u0065\u0078\u0074\u0075\u0061\u006c":*_bada =3;case "\u0068\u0069\u0073\u0074\u006f\u0072\u0069\u0063\u0061\u006c":*_bada =4;case "\u0064\u0069\u0073c\u0072\u0065\u0074\u0069\u006f\u006e\u0061\u006c":*_bada =5;case "\u0073t\u0061n\u0064\u0061\u0072\u0064\u0043o\u006e\u0074e\u0078\u0074\u0075\u0061\u006c":*_bada =6;
case "\u0073t\u0061n\u0064\u0061\u0072\u0064\u0048i\u0073\u0074o\u0072\u0069\u0063\u0061\u006c":*_bada =7;case "c\u006fn\u0074\u0065\u0078\u0074\u0075\u0061\u006c\u0048i\u0073\u0074\u006f\u0072ic\u0061\u006c":*_bada =8;case "s\u0074a\u006e\u0064\u0061\u0072\u0064\u0044\u0069\u0073c\u0072\u0065\u0074\u0069on\u0061\u006c":*_bada =9;
case "\u0063\u006f\u006e\u0074ex\u0074\u0075\u0061\u006c\u0044\u0069\u0073\u0063\u0072\u0065\u0074\u0069\u006f\u006ea\u006c":*_bada =10;case "\u0068\u0069\u0073\u0074or\u0069\u0063\u0061\u006c\u0044\u0069\u0073\u0063\u0072\u0065\u0074\u0069\u006f\u006ea\u006c":*_bada =11;
case "\u0073\u0074\u0061\u006ed\u0061\u0072\u0064\u0043\u006f\u006e\u0074\u0065\u0078\u0074u\u0061l\u0048\u0069\u0073\u0074\u006f\u0072\u0069c\u0061\u006c":*_bada =12;case "\u0073\u0074\u0061\u006e\u0064\u0061\u0072\u0064\u0043\u006fn\u0074\u0065\u0078\u0074\u0075\u0061\u006cD\u0069\u0073\u0063\u0072\u0065\u0074\u0069\u006f\u006e\u0061\u006c":*_bada =13;
case "\u0073\u0074\u0061\u006e\u0064\u0061\u0072\u0064\u0048\u0069s\u0074\u006f\u0072\u0069\u0063\u0061\u006cD\u0069\u0073\u0063\u0072\u0065\u0074\u0069\u006f\u006e\u0061\u006c":*_bada =14;case "\u0063\u006f\u006e\u0074\u0065\u0078\u0074\u0075\u0061\u006c\u0048\u0069\u0073\u0074\u006fr\u0069c\u0061\u006c\u0044\u0069\u0073\u0063\u0072\u0065\u0074\u0069\u006f\u006e\u0061\u006c":*_bada =15;
case "\u0061\u006c\u006c":*_bada =16;};return nil ;};type ST_NumForm byte ;

// Validate validates the CT_StyleSet and its children
func (_geff *CT_StyleSet )Validate ()error {return _geff .ValidateWithPath ("C\u0054\u005f\u0053\u0074\u0079\u006c\u0065\u0053\u0065\u0074");};func (_ga *AG_Parids )UnmarshalXML (d *_fd .Decoder ,start _fd .StartElement )error {for _ ,_af :=range start .Attr {if _af .Name .Local =="\u0070\u0061\u0072\u0061\u0049\u0064"{_d :=_af .Value ;
_ga .ParaIdAttr =&_d ;continue ;};if _af .Name .Local =="\u0074\u0065\u0078\u0074\u0049\u0064"{_ce :=_af .Value ;_ga .TextIdAttr =&_ce ;continue ;};};for {_cb ,_db :=d .Token ();if _db !=nil {return _g .Errorf ("p\u0061\u0072\u0073\u0069ng\u0020A\u0047\u005f\u0050\u0061\u0072i\u0064\u0073\u003a\u0020\u0025\u0073",_db );
};if _bc ,_ge :=_cb .(_fd .EndElement );_ge &&_bc .Name ==start .Name {break ;};};return nil ;};func (_afec *CT_SdtCheckbox )MarshalXML (e *_fd .Encoder ,start _fd .StartElement )error {e .EncodeToken (start );if _afec .Checked !=nil {_ece :=_fd .StartElement {Name :_fd .Name {Local :"w\u006f\u0072\u003a\u0063\u0068\u0065\u0063\u006b\u0065\u0064"}};
e .EncodeElement (_afec .Checked ,_ece );};if _afec .CheckedState !=nil {_cbe :=_fd .StartElement {Name :_fd .Name {Local :"\u0077\u006fr\u003a\u0063\u0068e\u0063\u006b\u0065\u0064\u0053\u0074\u0061\u0074\u0065"}};e .EncodeElement (_afec .CheckedState ,_cbe );
};if _afec .UncheckedState !=nil {_ggecf :=_fd .StartElement {Name :_fd .Name {Local :"\u0077o\u0072:\u0075\u006e\u0063\u0068\u0065c\u006b\u0065d\u0053\u0074\u0061\u0074\u0065"}};e .EncodeElement (_afec .UncheckedState ,_ggecf );};e .EncodeToken (_fd .EndElement {Name :start .Name });
return nil ;};func NewEG_RunLevelConflicts ()*EG_RunLevelConflicts {_cbdgb :=&EG_RunLevelConflicts {};return _cbdgb ;};func (_ffac ST_LineCap )MarshalXMLAttr (name _fd .Name )(_fd .Attr ,error ){_dfbe :=_fd .Attr {};_dfbe .Name =name ;switch _ffac {case ST_LineCapUnset :_dfbe .Value ="";
case ST_LineCapRnd :_dfbe .Value ="\u0072\u006e\u0064";case ST_LineCapSq :_dfbe .Value ="\u0073\u0071";case ST_LineCapFlat :_dfbe .Value ="\u0066\u006c\u0061\u0074";};return _dfbe ,nil ;};type EG_RPrOpenType struct{Ligatures *CT_Ligatures ;NumForm *CT_NumForm ;
NumSpacing *CT_NumSpacing ;StylisticSets *CT_StylisticSets ;CntxtAlts *CT_OnOff ;};type CustomXmlConflictDelRangeStart struct{_fe .CT_TrackChange };func (_aed *EG_FillPropertiesChoice )UnmarshalXML (d *_fd .Decoder ,start _fd .StartElement )error {_faegb :=start ;
switch start .Name {case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u006e\u006f\u0046\u0069\u006c\u006c"}:_aed .NoFill =_fe .NewCT_Empty ();
if _cfgc :=d .DecodeElement (_aed .NoFill ,&_faegb );_cfgc !=nil {return _cfgc ;};case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u0073o\u006c\u0069\u0064\u0046\u0069\u006cl"}:_aed .SolidFill =NewCT_SolidColorFillProperties ();
if _adgad :=d .DecodeElement (_aed .SolidFill ,&_faegb );_adgad !=nil {return _adgad ;};case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u0067\u0072\u0061\u0064\u0046\u0069\u006c\u006c"}:_aed .GradFill =NewCT_GradientFillProperties ();
if _cagda :=d .DecodeElement (_aed .GradFill ,&_faegb );_cagda !=nil {return _cagda ;};default:_c .Log .Debug ("\u0073\u006b\u0069\u0070\u0070\u0069\u006e\u0067\u0020\u0075\u006e\u0073\u0075\u0070\u0070\u006f\u0072\u0074\u0065\u0064 \u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006fn\u0020\u0045\u0047\u005f\u0046\u0069\u006c\u006c\u0050\u0072\u006f\u0070\u0065r\u0074\u0069\u0065\u0073\u0043\u0068o\u0069\u0063\u0065 \u0025\u0076",_faegb .Name );
if _cad :=d .Skip ();_cad !=nil {return _cad ;};};return nil ;};type CustomXmlConflictInsRangeEnd struct{_fe .CT_Markup };func (_afg *CT_FillTextEffect )UnmarshalXML (d *_fd .Decoder ,start _fd .StartElement )error {_afg .FillPropertiesChoice =NewEG_FillPropertiesChoice ();
_bg :for {_gfd ,_fec :=d .Token ();if _fec !=nil {return _fec ;};switch _bgd :=_gfd .(type ){case _fd .StartElement :switch _bgd .Name {case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u006e\u006f\u0046\u0069\u006c\u006c"}:_afg .FillPropertiesChoice =NewEG_FillPropertiesChoice ();
if _gfc :=d .DecodeElement (&_afg .FillPropertiesChoice .NoFill ,&_bgd );_gfc !=nil {return _gfc ;};case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u0073o\u006c\u0069\u0064\u0046\u0069\u006cl"}:_afg .FillPropertiesChoice =NewEG_FillPropertiesChoice ();
if _bga :=d .DecodeElement (&_afg .FillPropertiesChoice .SolidFill ,&_bgd );_bga !=nil {return _bga ;};case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u0067\u0072\u0061\u0064\u0046\u0069\u006c\u006c"}:_afg .FillPropertiesChoice =NewEG_FillPropertiesChoice ();
if _gfbb :=d .DecodeElement (&_afg .FillPropertiesChoice .GradFill ,&_bgd );_gfbb !=nil {return _gfbb ;};default:_c .Log .Debug ("\u0073\u006bi\u0070\u0070\u0069\u006e\u0067 \u0075\u006e\u0073\u0075\u0070p\u006f\u0072\u0074\u0065\u0064\u0020\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0043\u0054\u005f\u0046\u0069\u006c\u006c\u0054\u0065\u0078\u0074\u0045\u0066\u0066\u0065\u0063\u0074\u0020\u0025\u0076",_bgd .Name );
if _fbg :=d .Skip ();_fbg !=nil {return _fbg ;};};case _fd .EndElement :break _bg ;case _fd .CharData :};};return nil ;};

// Validate validates the CT_Camera and its children
func (_ec *CT_Camera )Validate ()error {return _ec .ValidateWithPath ("\u0043T\u005f\u0043\u0061\u006d\u0065\u0072a");};func (_da *CT_Camera )MarshalXML (e *_fd .Encoder ,start _fd .StartElement )error {_fb ,_ddf :=_da .PrstAttr .MarshalXMLAttr (_fd .Name {Local :"\u0077\u006f\u0072\u003a\u0070\u0072\u0073\u0074"});
if _ddf !=nil {return _ddf ;};start .Attr =append (start .Attr ,_fb );e .EncodeToken (start );e .EncodeToken (_fd .EndElement {Name :start .Name });return nil ;};func (_efga *CT_Props3D )UnmarshalXML (d *_fd .Decoder ,start _fd .StartElement )error {for _ ,_gebd :=range start .Attr {if _gebd .Name .Local =="\u0065\u0078\u0074\u0072\u0075\u0073\u0069\u006f\u006e\u0048"{_baff ,_ccc :=_a .ParseInt (_gebd .Value ,10,64);
if _ccc !=nil {return _ccc ;};_efga .ExtrusionHAttr =&_baff ;continue ;};if _gebd .Name .Local =="\u0063\u006f\u006e\u0074\u006f\u0075\u0072\u0057"{_ggec ,_cef :=_a .ParseInt (_gebd .Value ,10,64);if _cef !=nil {return _cef ;};_efga .ContourWAttr =&_ggec ;
continue ;};if _gebd .Name .Local =="\u0070\u0072\u0073t\u004d\u0061\u0074\u0065\u0072\u0069\u0061\u006c"{_efga .PrstMaterialAttr .UnmarshalXMLAttr (_gebd );continue ;};};_ebb :for {_afbg ,_dcd :=d .Token ();if _dcd !=nil {return _dcd ;};switch _gfg :=_afbg .(type ){case _fd .StartElement :switch _gfg .Name {case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u0062\u0065\u0076\u0065\u006c\u0054"}:_efga .BevelT =NewCT_Bevel ();
if _bgaf :=d .DecodeElement (_efga .BevelT ,&_gfg );_bgaf !=nil {return _bgaf ;};case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u0062\u0065\u0076\u0065\u006c\u0042"}:_efga .BevelB =NewCT_Bevel ();
if _cgdd :=d .DecodeElement (_efga .BevelB ,&_gfg );_cgdd !=nil {return _cgdd ;};case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u0065\u0078\u0074r\u0075\u0073\u0069\u006f\u006e\u0043\u006c\u0072"}:_efga .ExtrusionClr =NewCT_Color ();
if _eebb :=d .DecodeElement (_efga .ExtrusionClr ,&_gfg );_eebb !=nil {return _eebb ;};case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u0063\u006f\u006e\u0074\u006f\u0075\u0072\u0043\u006c\u0072"}:_efga .ContourClr =NewCT_Color ();
if _agaa :=d .DecodeElement (_efga .ContourClr ,&_gfg );_agaa !=nil {return _agaa ;};default:_c .Log .Debug ("\u0073k\u0069\u0070p\u0069\u006e\u0067 \u0075\u006e\u0073\u0075\u0070\u0070\u006fr\u0074\u0065\u0064\u0020\u0065\u006ce\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0043\u0054\u005fP\u0072\u006f\u0070\u0073\u0033\u0044\u0020\u0025\u0076",_gfg .Name );
if _cfd :=d .Skip ();_cfd !=nil {return _cfd ;};};case _fd .EndElement :break _ebb ;case _fd .CharData :};};return nil ;};type ST_CompoundLine byte ;func (_ddbfc *EG_RPrTextEffects )MarshalXML (e *_fd .Encoder ,start _fd .StartElement )error {start .Name .Local ="w\u006f\u0072\u003a\u0045G_\u0052P\u0072\u0054\u0065\u0078\u0074E\u0066\u0066\u0065\u0063\u0074\u0073";
if _ddbfc .Glow !=nil {_dacd :=_fd .StartElement {Name :_fd .Name {Local :"\u0077\u006f\u0072\u003a\u0067\u006c\u006f\u0077"}};e .EncodeElement (_ddbfc .Glow ,_dacd );};if _ddbfc .Shadow !=nil {_gcff :=_fd .StartElement {Name :_fd .Name {Local :"\u0077\u006f\u0072\u003a\u0073\u0068\u0061\u0064\u006f\u0077"}};
e .EncodeElement (_ddbfc .Shadow ,_gcff );};if _ddbfc .Reflection !=nil {_gbca :=_fd .StartElement {Name :_fd .Name {Local :"\u0077\u006f\u0072\u003a\u0072\u0065\u0066\u006c\u0065c\u0074\u0069\u006f\u006e"}};e .EncodeElement (_ddbfc .Reflection ,_gbca );
};if _ddbfc .TextOutline !=nil {_ebbd :=_fd .StartElement {Name :_fd .Name {Local :"\u0077o\u0072:\u0074\u0065\u0078\u0074\u004f\u0075\u0074\u006c\u0069\u006e\u0065"}};e .EncodeElement (_ddbfc .TextOutline ,_ebbd );};if _ddbfc .TextFill !=nil {_fbbef :=_fd .StartElement {Name :_fd .Name {Local :"\u0077\u006f\u0072:\u0074\u0065\u0078\u0074\u0046\u0069\u006c\u006c"}};
e .EncodeElement (_ddbfc .TextFill ,_fbbef );};if _ddbfc .Scene3d !=nil {_agaf :=_fd .StartElement {Name :_fd .Name {Local :"w\u006f\u0072\u003a\u0073\u0063\u0065\u006e\u0065\u0033\u0064"}};e .EncodeElement (_ddbfc .Scene3d ,_agaf );};if _ddbfc .Props3d !=nil {_fdd :=_fd .StartElement {Name :_fd .Name {Local :"w\u006f\u0072\u003a\u0070\u0072\u006f\u0070\u0073\u0033\u0064"}};
e .EncodeElement (_ddbfc .Props3d ,_fdd );};return nil ;};

// ValidateWithPath validates the AG_Parids and its children, prefixing error messages with path
func (_bcb *AG_Parids )ValidateWithPath (path string )error {return nil };func (_fffb *CustomXmlConflictInsRangeStart )UnmarshalXML (d *_fd .Decoder ,start _fd .StartElement )error {_fffb .CT_TrackChange =*_fe .NewCT_TrackChange ();for {_eadb ,_gdde :=d .Token ();
if _gdde !=nil {return _g .Errorf ("\u0070\u0061\u0072s\u0069\u006e\u0067\u0020\u0043\u0075\u0073\u0074\u006f\u006d\u0058\u006d\u006c\u0043\u006f\u006e\u0066\u006c\u0069\u0063\u0074\u0049\u006e\u0073\u0052\u0061\u006e\u0067\u0065S\u0074\u0061\u0072\u0074\u003a\u0020\u0025\u0073",_gdde );
};if _cffaf ,_bcde :=_eadb .(_fd .EndElement );_bcde &&_cffaf .Name ==start .Name {break ;};};return nil ;};func (_ebeee *EG_LineJoinPropertiesChoice )UnmarshalXML (d *_fd .Decoder ,start _fd .StartElement )error {_bbbb :=start ;switch start .Name {case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u0072\u006f\u0075n\u0064"}:_ebeee .Round =_fe .NewCT_Empty ();
if _ada :=d .DecodeElement (_ebeee .Round ,&_bbbb );_ada !=nil {return _ada ;};case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u0062\u0065\u0076e\u006c"}:_ebeee .Bevel =_fe .NewCT_Empty ();
if _gcag :=d .DecodeElement (_ebeee .Bevel ,&_bbbb );_gcag !=nil {return _gcag ;};case _fd .Name {Space :"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c",Local :"\u006d\u0069\u0074e\u0072"}:_ebeee .Miter =NewCT_LineJoinMiterProperties ();
if _eaad :=d .DecodeElement (_ebeee .Miter ,&_bbbb );_eaad !=nil {return _eaad ;};default:_c .Log .Debug ("\u0073\u006b\u0069\u0070\u0070\u0069\u006e\u0067\u0020\u0075\u006e\u0073\u0075\u0070\u0070\u006f\u0072\u0074ed\u0020e\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0045\u0047_\u004c\u0069\u006e\u0065\u004a\u006f\u0069\u006e\u0050\u0072\u006f\u0070\u0065\u0072\u0074\u0069\u0065s\u0043\u0068\u006f\u0069\u0063\u0065\u0020\u0025\u0076",_bbbb .Name );
if _ddbg :=d .Skip ();_ddbg !=nil {return _ddbg ;};};return nil ;};

// Validate validates the CT_PresetLineDashProperties and its children
func (_dec *CT_PresetLineDashProperties )Validate ()error {return _dec .ValidateWithPath ("C\u0054\u005f\u0050\u0072\u0065\u0073e\u0074\u004c\u0069\u006e\u0065\u0044\u0061\u0073\u0068P\u0072\u006f\u0070e\u0072t\u0069\u0065\u0073");};

// ValidateWithPath validates the CT_RelativeRect and its children, prefixing error messages with path
func (_bece *CT_RelativeRect )ValidateWithPath (path string )error {if _bece .LAttr !=nil {if _edgc :=_bece .LAttr .ValidateWithPath (path +"\u002f\u004c\u0041\u0074\u0074\u0072");_edgc !=nil {return _edgc ;};};if _bece .TAttr !=nil {if _gfgc :=_bece .TAttr .ValidateWithPath (path +"\u002f\u0054\u0041\u0074\u0074\u0072");
_gfgc !=nil {return _gfgc ;};};if _bece .RAttr !=nil {if _efag :=_bece .RAttr .ValidateWithPath (path +"\u002f\u0052\u0041\u0074\u0074\u0072");_efag !=nil {return _efag ;};};if _bece .BAttr !=nil {if _fef :=_bece .BAttr .ValidateWithPath (path +"\u002f\u0042\u0041\u0074\u0074\u0072");
_fef !=nil {return _fef ;};};return nil ;};func init (){_ab .RegisterConstructor ("\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c","\u0043\u0054_\u004c\u006f\u006eg\u0048\u0065\u0078\u004e\u0075\u006d\u0062\u0065\u0072",NewCT_LongHexNumber );
_ab .RegisterConstructor ("\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c","\u0043\u0054\u005f\u004f\u006e\u004f\u0066\u0066",NewCT_OnOff );
_ab .RegisterConstructor ("\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c","\u0043\u0054\u005f\u0050\u0065\u0072\u0063\u0065\u006e\u0074\u0061\u0067\u0065",NewCT_Percentage );
_ab .RegisterConstructor ("\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c","\u0043\u0054\u005f\u0050\u006f\u0073\u0069\u0074\u0069\u0076\u0065F\u0069\u0078\u0065\u0064\u0050\u0065\u0072\u0063\u0065\u006et\u0061\u0067\u0065",NewCT_PositiveFixedPercentage );
_ab .RegisterConstructor ("\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c","C\u0054\u005f\u0050\u006fsi\u0074i\u0076\u0065\u0050\u0065\u0072c\u0065\u006e\u0074\u0061\u0067\u0065",NewCT_PositivePercentage );
_ab .RegisterConstructor ("\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c","\u0043T\u005fR\u0065\u006c\u0061\u0074\u0069\u0076\u0065\u0052\u0065\u0063\u0074",NewCT_RelativeRect );
_ab .RegisterConstructor ("\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c","\u0043\u0054\u005fS\u0052\u0067\u0062\u0043\u006f\u006c\u006f\u0072",NewCT_SRgbColor );
_ab .RegisterConstructor ("\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c","\u0043\u0054\u005f\u0053\u0063\u0068\u0065\u006d\u0065C\u006f\u006c\u006f\u0072",NewCT_SchemeColor );
_ab .RegisterConstructor ("\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c","\u0043\u0054\u005f\u0043\u006f\u006c\u006f\u0072",NewCT_Color );
_ab .RegisterConstructor ("\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c","\u0043T\u005fG\u0072\u0061\u0064\u0069\u0065\u006e\u0074\u0053\u0074\u006f\u0070",NewCT_GradientStop );
_ab .RegisterConstructor ("\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c","\u0043\u0054\u005f\u0047ra\u0064\u0069\u0065\u006e\u0074\u0053\u0074\u006f\u0070\u004c\u0069\u0073\u0074",NewCT_GradientStopList );
_ab .RegisterConstructor ("\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c","\u0043T\u005f\u004c\u0069\u006e\u0065\u0061\u0072\u0053\u0068\u0061\u0064e\u0050\u0072\u006f\u0070\u0065\u0072\u0074\u0069\u0065\u0073",NewCT_LinearShadeProperties );
_ab .RegisterConstructor ("\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c","\u0043\u0054\u005f\u0050at\u0068\u0053\u0068\u0061\u0064\u0065\u0050\u0072\u006f\u0070\u0065\u0072\u0074\u0069e\u0073",NewCT_PathShadeProperties );
_ab .RegisterConstructor ("\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c","C\u0054\u005f\u0053\u006f\u006c\u0069d\u0043\u006f\u006c\u006f\u0072\u0046\u0069\u006c\u006cP\u0072\u006f\u0070e\u0072t\u0069\u0065\u0073",NewCT_SolidColorFillProperties );
_ab .RegisterConstructor ("\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c","\u0043T\u005f\u0047\u0072\u0061d\u0069\u0065\u006e\u0074\u0046i\u006cl\u0050r\u006f\u0070\u0065\u0072\u0074\u0069\u0065s",NewCT_GradientFillProperties );
_ab .RegisterConstructor ("\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c","C\u0054\u005f\u0050\u0072\u0065\u0073e\u0074\u004c\u0069\u006e\u0065\u0044\u0061\u0073\u0068P\u0072\u006f\u0070e\u0072t\u0069\u0065\u0073",NewCT_PresetLineDashProperties );
_ab .RegisterConstructor ("\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c","\u0043\u0054\u005f\u004c\u0069\u006e\u0065\u004a\u006f\u0069\u006eM\u0069\u0074\u0065\u0072\u0050\u0072\u006f\u0070\u0065\u0072t\u0069\u0065\u0073",NewCT_LineJoinMiterProperties );
_ab .RegisterConstructor ("\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c","\u0043T\u005f\u0043\u0061\u006d\u0065\u0072a",NewCT_Camera );
_ab .RegisterConstructor ("\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c","\u0043T\u005fS\u0070\u0068\u0065\u0072\u0065\u0043\u006f\u006f\u0072\u0064\u0073",NewCT_SphereCoords );
_ab .RegisterConstructor ("\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c","C\u0054\u005f\u004c\u0069\u0067\u0068\u0074\u0052\u0069\u0067",NewCT_LightRig );
_ab .RegisterConstructor ("\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c","\u0043\u0054\u005f\u0042\u0065\u0076\u0065\u006c",NewCT_Bevel );
_ab .RegisterConstructor ("\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c","\u0043T\u005f\u0047\u006c\u006f\u0077",NewCT_Glow );
_ab .RegisterConstructor ("\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c","\u0043T\u005f\u0053\u0068\u0061\u0064\u006fw",NewCT_Shadow );
_ab .RegisterConstructor ("\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c","\u0043\u0054\u005f\u0052\u0065\u0066\u006c\u0065\u0063\u0074\u0069\u006f\u006e",NewCT_Reflection );
_ab .RegisterConstructor ("\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c","\u0043\u0054\u005f\u0046\u0069\u006c\u006c\u0054\u0065\u0078\u0074\u0045f\u0066\u0065\u0063\u0074",NewCT_FillTextEffect );
_ab .RegisterConstructor ("\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c","C\u0054_\u0054\u0065\u0078\u0074\u004f\u0075\u0074\u006ci\u006e\u0065\u0045\u0066fe\u0063\u0074",NewCT_TextOutlineEffect );
_ab .RegisterConstructor ("\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c","\u0043\u0054\u005f\u0053\u0063\u0065\u006e\u0065\u0033\u0044",NewCT_Scene3D );
_ab .RegisterConstructor ("\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c","\u0043\u0054\u005f\u0050\u0072\u006f\u0070\u0073\u0033\u0044",NewCT_Props3D );
_ab .RegisterConstructor ("\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c","\u0043\u0054\u005fL\u0069\u0067\u0061\u0074\u0075\u0072\u0065\u0073",NewCT_Ligatures );
_ab .RegisterConstructor ("\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c","\u0043\u0054\u005f\u004e\u0075\u006d\u0046\u006f\u0072\u006d",NewCT_NumForm );
_ab .RegisterConstructor ("\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c","\u0043\u0054\u005f\u004e\u0075\u006d\u0053\u0070\u0061\u0063\u0069\u006e\u0067",NewCT_NumSpacing );
_ab .RegisterConstructor ("\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c","C\u0054\u005f\u0053\u0074\u0079\u006c\u0065\u0053\u0065\u0074",NewCT_StyleSet );
_ab .RegisterConstructor ("\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c","\u0043\u0054_\u0053\u0074\u0079l\u0069\u0073\u0074\u0069\u0063\u0053\u0065\u0074\u0073",NewCT_StylisticSets );
_ab .RegisterConstructor ("\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c","\u0043T\u005fD\u0065\u0066\u0061\u0075\u006ct\u0049\u006da\u0067\u0065\u0044\u0070\u0069",NewCT_DefaultImageDpi );
_ab .RegisterConstructor ("\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c","C\u0054_\u0053\u0064\u0074\u0043\u0068\u0065\u0063\u006bb\u006f\u0078\u0053\u0079mb\u006f\u006c",NewCT_SdtCheckboxSymbol );
_ab .RegisterConstructor ("\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c","\u0043\u0054\u005f\u0053\u0064\u0074\u0043\u0068\u0065c\u006b\u0062\u006f\u0078",NewCT_SdtCheckbox );
_ab .RegisterConstructor ("\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c","\u0064\u006f\u0063I\u0064",NewDocId );
_ab .RegisterConstructor ("\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c","\u0063\u006f\u006ef\u006c\u0069\u0063\u0074\u004d\u006f\u0064\u0065",NewConflictMode );
_ab .RegisterConstructor ("\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c","\u0063\u0075\u0073\u0074\u006f\u006d\u0058\u006d\u006c\u0043o\u006e\u0066\u006c\u0069\u0063\u0074\u0049n\u0073\u0052\u0061\u006e\u0067\u0065\u0053\u0074\u0061\u0072\u0074",NewCustomXmlConflictInsRangeStart );
_ab .RegisterConstructor ("\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c","\u0063\u0075\u0073\u0074o\u006d\u0058\u006d\u006c\u0043\u006f\u006e\u0066\u006c\u0069c\u0074I\u006e\u0073\u0052\u0061\u006e\u0067\u0065E\u006e\u0064",NewCustomXmlConflictInsRangeEnd );
_ab .RegisterConstructor ("\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c","\u0063\u0075\u0073\u0074\u006f\u006d\u0058\u006d\u006c\u0043o\u006e\u0066\u006c\u0069\u0063\u0074\u0044e\u006c\u0052\u0061\u006e\u0067\u0065\u0053\u0074\u0061\u0072\u0074",NewCustomXmlConflictDelRangeStart );
_ab .RegisterConstructor ("\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c","\u0063\u0075\u0073\u0074o\u006d\u0058\u006d\u006c\u0043\u006f\u006e\u0066\u006c\u0069c\u0074D\u0065\u006c\u0052\u0061\u006e\u0067\u0065E\u006e\u0064",NewCustomXmlConflictDelRangeEnd );
_ab .RegisterConstructor ("\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c","\u0064\u0069\u0073ca\u0072\u0064\u0049\u006d\u0061\u0067\u0065\u0045\u0064\u0069\u0074\u0069\u006e\u0067\u0044\u0061\u0074\u0061",NewDiscardImageEditingData );
_ab .RegisterConstructor ("\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c","\u0064e\u0066a\u0075\u006c\u0074\u0049\u006d\u0061\u0067\u0065\u0044\u0070\u0069",NewDefaultImageDpi );
_ab .RegisterConstructor ("\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c","\u0065\u006e\u0074i\u0074\u0079\u0050\u0069\u0063\u006b\u0065\u0072",NewEntityPicker );
_ab .RegisterConstructor ("\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c","\u0063\u0068\u0065\u0063\u006b\u0062\u006f\u0078",NewCheckbox );
_ab .RegisterConstructor ("\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c","E\u0047_\u0052\u0075\u006e\u004c\u0065\u0076\u0065\u006cC\u006f\u006e\u0066\u006cic\u0074\u0073",NewEG_RunLevelConflicts );
_ab .RegisterConstructor ("\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c","\u0045\u0047\u005fC\u006f\u006e\u0066\u006c\u0069\u0063\u0074\u0073",NewEG_Conflicts );
_ab .RegisterConstructor ("\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c","\u0045\u0047\u005f\u0043\u006f\u006c\u006f\u0072\u0054\u0072\u0061\u006es\u0066\u006f\u0072\u006d",NewEG_ColorTransform );
_ab .RegisterConstructor ("\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c","\u0045\u0047\u005f\u0043\u006f\u006c\u006f\u0072\u0043h\u006f\u0069\u0063\u0065",NewEG_ColorChoice );
_ab .RegisterConstructor ("\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c","\u0045G\u005fS\u0068\u0061\u0064\u0065\u0050r\u006f\u0070e\u0072\u0074\u0069\u0065\u0073",NewEG_ShadeProperties );
_ab .RegisterConstructor ("\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c","\u0045\u0047\u005f\u0046\u0069\u006c\u006c\u0050\u0072\u006f\u0070\u0065r\u0074\u0069\u0065\u0073",NewEG_FillProperties );
_ab .RegisterConstructor ("\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c","E\u0047\u005f\u004c\u0069ne\u0044a\u0073\u0068\u0050\u0072\u006fp\u0065\u0072\u0074\u0069\u0065\u0073",NewEG_LineDashProperties );
_ab .RegisterConstructor ("\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c","E\u0047\u005f\u004c\u0069ne\u004ao\u0069\u006e\u0050\u0072\u006fp\u0065\u0072\u0074\u0069\u0065\u0073",NewEG_LineJoinProperties );
_ab .RegisterConstructor ("\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c","\u0045\u0047\u005f\u0052\u0050\u0072\u0054\u0065\u0078\u0074\u0045\u0066f\u0065\u0063\u0074\u0073",NewEG_RPrTextEffects );
_ab .RegisterConstructor ("\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c","\u0045\u0047\u005f\u0052\u0050\u0072\u004f\u0070\u0065n\u0054\u0079\u0070\u0065",NewEG_RPrOpenType );
_ab .RegisterConstructor ("\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c","\u0041G\u005f\u0050\u0061\u0072\u0069\u0064s",NewAG_Parids );
};