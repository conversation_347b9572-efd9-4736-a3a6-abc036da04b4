//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package terms ;import (_g "encoding/xml";_a "fmt";_c "github.com/unidoc/unioffice/v2";_b "github.com/unidoc/unioffice/v2/common/logger";_f "github.com/unidoc/unioffice/v2/schema/purl.org/dc/elements";);

// ValidateWithPath validates the W3CDTF and its children, prefixing error messages with path
func (_fcf *W3CDTF )ValidateWithPath (path string )error {return nil };type ElementOrRefinementContainer struct{Any *_f .Any ;};

// Validate validates the UDC and its children
func (_dfa *UDC )Validate ()error {return _dfa .ValidateWithPath ("\u0055\u0044\u0043")};func (_fgc *ElementsAndRefinementsGroup )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {if _fgc .Any !=nil {_fcd :=_g .StartElement {Name :_g .Name {Local :"\u0064\u0063\u003a\u0061\u006e\u0079"}};
e .EncodeElement (_fgc .Any ,_fcd );};return nil ;};func NewURI ()*URI {_dda :=&URI {};return _dda };type IMT struct{};

// Validate validates the ElementsAndRefinementsGroup and its children
func (_ea *ElementsAndRefinementsGroup )Validate ()error {return _ea .ValidateWithPath ("E\u006c\u0065\u006d\u0065\u006e\u0074s\u0041\u006e\u0064\u0052\u0065\u0066\u0069\u006e\u0065m\u0065\u006e\u0074s\u0047r\u006f\u0075\u0070");};func NewDCMIType ()*DCMIType {_bd :=&DCMIType {};
return _bd };

// Validate validates the LCC and its children
func (_gfd *LCC )Validate ()error {return _gfd .ValidateWithPath ("\u004c\u0043\u0043")};func NewISO3166 ()*ISO3166 {_fbf :=&ISO3166 {};return _fbf };type DDC struct{};

// ValidateWithPath validates the ElementOrRefinementContainer and its children, prefixing error messages with path
func (_dg *ElementOrRefinementContainer )ValidateWithPath (path string )error {if _dg .Any !=nil {if _egc :=_dg .Any .ValidateWithPath (path +"\u002f\u0041\u006e\u0079");_egc !=nil {return _egc ;};};return nil ;};

// ValidateWithPath validates the DDC and its children, prefixing error messages with path
func (_fee *DDC )ValidateWithPath (path string )error {return nil };type W3CDTF struct{};func NewPeriod ()*Period {_fae :=&Period {};return _fae };

// Validate validates the ISO3166 and its children
func (_aeb *ISO3166 )Validate ()error {return _aeb .ValidateWithPath ("\u0049S\u004f\u0033\u0031\u0036\u0036");};

// ValidateWithPath validates the Point and its children, prefixing error messages with path
func (_cfg *Point )ValidateWithPath (path string )error {return nil };

// ValidateWithPath validates the MESH and its children, prefixing error messages with path
func (_eff *MESH )ValidateWithPath (path string )error {return nil };func (_eaa *ISO3166 )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {start .Name .Local ="\u0049S\u004f\u0033\u0031\u0036\u0036";e .EncodeToken (start );e .EncodeToken (_g .EndElement {Name :start .Name });
return nil ;};func (_ddfd *Point )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {start .Name .Local ="\u0050\u006f\u0069n\u0074";e .EncodeToken (start );e .EncodeToken (_g .EndElement {Name :start .Name });return nil ;};

// Validate validates the IMT and its children
func (_bb *IMT )Validate ()error {return _bb .ValidateWithPath ("\u0049\u004d\u0054")};func NewRFC3066 ()*RFC3066 {_ccb :=&RFC3066 {};return _ccb };func (_ggg *Period )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {for {_fcgb ,_fbg :=d .Token ();
if _fbg !=nil {return _a .Errorf ("\u0070a\u0072s\u0069\u006e\u0067\u0020\u0050e\u0072\u0069o\u0064\u003a\u0020\u0025\u0073",_fbg );};if _cdd ,_dfbf :=_fcgb .(_g .EndElement );_dfbf &&_cdd .Name ==start .Name {break ;};};return nil ;};

// Validate validates the RFC1766 and its children
func (_afe *RFC1766 )Validate ()error {return _afe .ValidateWithPath ("\u0052F\u0043\u0031\u0037\u0036\u0036");};func NewMESH ()*MESH {_fab :=&MESH {};return _fab };func (_ff *DCMIType )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {start .Name .Local ="\u0044\u0043\u004d\u0049\u0054\u0079\u0070\u0065";
e .EncodeToken (start );e .EncodeToken (_g .EndElement {Name :start .Name });return nil ;};func (_ab *URI )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {start .Name .Local ="\u0055\u0052\u0049";e .EncodeToken (start );e .EncodeToken (_g .EndElement {Name :start .Name });
return nil ;};func (_afb *LCSH )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {start .Name .Local ="\u004c\u0043\u0053\u0048";e .EncodeToken (start );e .EncodeToken (_g .EndElement {Name :start .Name });return nil ;};func (_bcgc *LCC )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {for {_bfc ,_dge :=d .Token ();
if _dge !=nil {return _a .Errorf ("\u0070a\u0072s\u0069\u006e\u0067\u0020\u004c\u0043\u0043\u003a\u0020\u0025\u0073",_dge );};if _aad ,_fcdg :=_bfc .(_g .EndElement );_fcdg &&_aad .Name ==start .Name {break ;};};return nil ;};type RFC3066 struct{};func (_caf *TGN )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {start .Name .Local ="\u0054\u0047\u004e";
e .EncodeToken (start );e .EncodeToken (_g .EndElement {Name :start .Name });return nil ;};

// Validate validates the DDC and its children
func (_egg *DDC )Validate ()error {return _egg .ValidateWithPath ("\u0044\u0044\u0043")};type MESH struct{};func NewElementsAndRefinementsGroup ()*ElementsAndRefinementsGroup {_fc :=&ElementsAndRefinementsGroup {};return _fc ;};type Period struct{};func (_cgd *RFC1766 )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {for {_fdc ,_ccd :=d .Token ();
if _ccd !=nil {return _a .Errorf ("\u0070\u0061\u0072\u0073in\u0067\u0020\u0052\u0046\u0043\u0031\u0037\u0036\u0036\u003a\u0020\u0025\u0073",_ccd );};if _ad ,_gcc :=_fdc .(_g .EndElement );_gcc &&_ad .Name ==start .Name {break ;};};return nil ;};

// Validate validates the TGN and its children
func (_cgdd *TGN )Validate ()error {return _cgdd .ValidateWithPath ("\u0054\u0047\u004e")};type UDC struct{};

// ValidateWithPath validates the Box and its children, prefixing error messages with path
func (_ac *Box )ValidateWithPath (path string )error {return nil };func (_dbc *ISO639_2 )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {start .Name .Local ="\u0049\u0053\u004f\u0036\u0033\u0039\u002d\u0032";e .EncodeToken (start );e .EncodeToken (_g .EndElement {Name :start .Name });
return nil ;};type ISO639_2 struct{};func NewTGN ()*TGN {_aadg :=&TGN {};return _aadg };

// Validate validates the LCSH and its children
func (_fac *LCSH )Validate ()error {return _fac .ValidateWithPath ("\u004c\u0043\u0053\u0048")};

// Validate validates the MESH and its children
func (_dba *MESH )Validate ()error {return _dba .ValidateWithPath ("\u004d\u0045\u0053\u0048")};func (_gcd *TGN )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {for {_dde ,_adb :=d .Token ();if _adb !=nil {return _a .Errorf ("\u0070a\u0072s\u0069\u006e\u0067\u0020\u0054\u0047\u004e\u003a\u0020\u0025\u0073",_adb );
};if _egce ,_deb :=_dde .(_g .EndElement );_deb &&_egce .Name ==start .Name {break ;};};return nil ;};func (_efee *URI )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {for {_acb ,_cdag :=d .Token ();if _cdag !=nil {return _a .Errorf ("\u0070a\u0072s\u0069\u006e\u0067\u0020\u0055\u0052\u0049\u003a\u0020\u0025\u0073",_cdag );
};if _fga ,_ddd :=_acb .(_g .EndElement );_ddd &&_fga .Name ==start .Name {break ;};};return nil ;};

// Validate validates the W3CDTF and its children
func (_eab *W3CDTF )Validate ()error {return _eab .ValidateWithPath ("\u0057\u0033\u0043\u0044\u0054\u0046");};func (_bde *ElementOrRefinementContainer )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {_fbcb :for {_cc ,_ed :=d .Token ();if _ed !=nil {return _ed ;
};switch _gd :=_cc .(type ){case _g .StartElement :switch _gd .Name {case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0072\u0067/\u0064c\u002f\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0073\u002f\u0031\u002e\u0031\u002f",Local :"\u0061\u006e\u0079"}:_bde .Any =_f .NewAny ();
if _dbe :=d .DecodeElement (_bde .Any ,&_gd );_dbe !=nil {return _dbe ;};default:_b .Log .Debug ("\u0073k\u0069\u0070\u0070\u0069\u006e\u0067\u0020un\u0073\u0075\u0070\u0070\u006f\u0072\u0074\u0065\u0064\u0020\u0065\u006c\u0065\u006de\u006e\u0074 \u006f\u006e\u0020E\u006c\u0065\u006d\u0065\u006e\u0074\u004f\u0072\u0052\u0065\u0066\u0069\u006e\u0065\u006d\u0065n\u0074\u0043on\u0074\u0061\u0069n\u0065\u0072\u0020\u0025\u0076",_gd .Name );
if _gf :=d .Skip ();_gf !=nil {return _gf ;};};case _g .EndElement :break _fbcb ;case _g .CharData :};};return nil ;};

// ValidateWithPath validates the TGN and its children, prefixing error messages with path
func (_cda *TGN )ValidateWithPath (path string )error {return nil };type RFC1766 struct{};

// Validate validates the RFC3066 and its children
func (_dee *RFC3066 )Validate ()error {return _dee .ValidateWithPath ("\u0052F\u0043\u0033\u0030\u0036\u0036");};func NewBox ()*Box {_ga :=&Box {};return _ga };func (_eeg *MESH )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {start .Name .Local ="\u004d\u0045\u0053\u0048";
e .EncodeToken (start );e .EncodeToken (_g .EndElement {Name :start .Name });return nil ;};

// ValidateWithPath validates the Period and its children, prefixing error messages with path
func (_fec *Period )ValidateWithPath (path string )error {return nil };func NewIMT ()*IMT {_gag :=&IMT {};return _gag };func (_ecg *W3CDTF )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {for {_dbga ,_adg :=d .Token ();if _adg !=nil {return _a .Errorf ("\u0070a\u0072s\u0069\u006e\u0067\u0020\u00573\u0043\u0044T\u0046\u003a\u0020\u0025\u0073",_adg );
};if _debb ,_dgf :=_dbga .(_g .EndElement );_dgf &&_debb .Name ==start .Name {break ;};};return nil ;};

// ValidateWithPath validates the ISO639_2 and its children, prefixing error messages with path
func (_fag *ISO639_2 )ValidateWithPath (path string )error {return nil };func NewRFC1766 ()*RFC1766 {_age :=&RFC1766 {};return _age };

// ValidateWithPath validates the DCMIType and its children, prefixing error messages with path
func (_gb *DCMIType )ValidateWithPath (path string )error {return nil };

// ValidateWithPath validates the LCSH and its children, prefixing error messages with path
func (_be *LCSH )ValidateWithPath (path string )error {return nil };type URI struct{};type DCMIType struct{};

// ValidateWithPath validates the RFC3066 and its children, prefixing error messages with path
func (_fagf *RFC3066 )ValidateWithPath (path string )error {return nil };

// Validate validates the URI and its children
func (_dfee *URI )Validate ()error {return _dfee .ValidateWithPath ("\u0055\u0052\u0049")};

// ValidateWithPath validates the IMT and its children, prefixing error messages with path
func (_ebe *IMT )ValidateWithPath (path string )error {return nil };func NewLCSH ()*LCSH {_eae :=&LCSH {};return _eae };

// ValidateWithPath validates the LCC and its children, prefixing error messages with path
func (_gae *LCC )ValidateWithPath (path string )error {return nil };func NewElementOrRefinementContainer ()*ElementOrRefinementContainer {_eb :=&ElementOrRefinementContainer {};return _eb ;};type TGN struct{};

// ValidateWithPath validates the ISO3166 and its children, prefixing error messages with path
func (_fd *ISO3166 )ValidateWithPath (path string )error {return nil };func (_gg *Box )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {for {_d ,_fe :=d .Token ();if _fe !=nil {return _a .Errorf ("\u0070a\u0072s\u0069\u006e\u0067\u0020\u0042\u006f\u0078\u003a\u0020\u0025\u0073",_fe );
};if _fg ,_fb :=_d .(_g .EndElement );_fb &&_fg .Name ==start .Name {break ;};};return nil ;};func (_gff *IMT )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {start .Name .Local ="\u0049\u004d\u0054";e .EncodeToken (start );e .EncodeToken (_g .EndElement {Name :start .Name });
return nil ;};func (_db *ElementOrRefinementContainer )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {start .Name .Local ="\u0065\u006c\u0065\u006de\u006e\u0074\u004f\u0072\u0052\u0065\u0066\u0069\u006e\u0065m\u0065n\u0074\u0043\u006f\u006e\u0074\u0061\u0069n\u0065\u0072";
e .EncodeToken (start );if _db .Any !=nil {_afg :=_g .StartElement {Name :_g .Name {Local :"\u0064\u0063\u003a\u0061\u006e\u0079"}};e .EncodeElement (_db .Any ,_afg );};e .EncodeToken (_g .EndElement {Name :start .Name });return nil ;};func (_aa *DDC )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {start .Name .Local ="\u0044\u0044\u0043";
e .EncodeToken (start );e .EncodeToken (_g .EndElement {Name :start .Name });return nil ;};

// Validate validates the DCMIType and its children
func (_dc *DCMIType )Validate ()error {return _dc .ValidateWithPath ("\u0044\u0043\u004d\u0049\u0054\u0079\u0070\u0065");};

// Validate validates the Box and its children
func (_dd *Box )Validate ()error {return _dd .ValidateWithPath ("\u0042\u006f\u0078")};func NewW3CDTF ()*W3CDTF {_gcca :=&W3CDTF {};return _gcca };func (_ead *Point )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {for {_cf ,_beb :=d .Token ();
if _beb !=nil {return _a .Errorf ("\u0070\u0061\u0072\u0073\u0069\u006e\u0067\u0020\u0050\u006f\u0069\u006et\u003a\u0020\u0025\u0073",_beb );};if _bdec ,_acff :=_cf .(_g .EndElement );_acff &&_bdec .Name ==start .Name {break ;};};return nil ;};func (_ggc *RFC3066 )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {start .Name .Local ="\u0052F\u0043\u0033\u0030\u0036\u0036";
e .EncodeToken (start );e .EncodeToken (_g .EndElement {Name :start .Name });return nil ;};func (_dab *RFC1766 )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {start .Name .Local ="\u0052F\u0043\u0031\u0037\u0036\u0036";e .EncodeToken (start );
e .EncodeToken (_g .EndElement {Name :start .Name });return nil ;};func (_cac *RFC3066 )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {for {_dbgb ,_eef :=d .Token ();if _eef !=nil {return _a .Errorf ("\u0070\u0061\u0072\u0073in\u0067\u0020\u0052\u0046\u0043\u0033\u0030\u0036\u0036\u003a\u0020\u0025\u0073",_eef );
};if _eea ,_ba :=_dbgb .(_g .EndElement );_ba &&_eea .Name ==start .Name {break ;};};return nil ;};type ISO3166 struct{};func (_bca *LCC )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {start .Name .Local ="\u004c\u0043\u0043";e .EncodeToken (start );
e .EncodeToken (_g .EndElement {Name :start .Name });return nil ;};

// Validate validates the Period and its children
func (_afge *Period )Validate ()error {return _afge .ValidateWithPath ("\u0050\u0065\u0072\u0069\u006f\u0064");};

// ValidateWithPath validates the UDC and its children, prefixing error messages with path
func (_bfa *UDC )ValidateWithPath (path string )error {return nil };type ElementsAndRefinementsGroup struct{Any *_f .Any ;};func (_cee *MESH )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {for {_bcba ,_de :=d .Token ();if _de !=nil {return _a .Errorf ("\u0070\u0061r\u0073\u0069\u006eg\u0020\u004d\u0045\u0053\u0048\u003a\u0020\u0025\u0073",_de );
};if _bbc ,_eed :=_bcba .(_g .EndElement );_eed &&_bbc .Name ==start .Name {break ;};};return nil ;};type LCSH struct{};type LCC struct{};

// ValidateWithPath validates the RFC1766 and its children, prefixing error messages with path
func (_fcc *RFC1766 )ValidateWithPath (path string )error {return nil };func (_agg *Period )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {start .Name .Local ="\u0050\u0065\u0072\u0069\u006f\u0064";e .EncodeToken (start );e .EncodeToken (_g .EndElement {Name :start .Name });
return nil ;};func (_ddf *DCMIType )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {for {_bg ,_ee :=d .Token ();if _ee !=nil {return _a .Errorf ("p\u0061r\u0073\u0069\u006e\u0067\u0020\u0044\u0043\u004dI\u0054\u0079\u0070\u0065: \u0025\u0073",_ee );
};if _df ,_ag :=_bg .(_g .EndElement );_ag &&_df .Name ==start .Name {break ;};};return nil ;};type Box struct{};func (_gc *DDC )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {for {_fbc ,_fa :=d .Token ();if _fa !=nil {return _a .Errorf ("\u0070a\u0072s\u0069\u006e\u0067\u0020\u0044\u0044\u0043\u003a\u0020\u0025\u0073",_fa );
};if _af ,_bc :=_fbc .(_g .EndElement );_bc &&_af .Name ==start .Name {break ;};};return nil ;};func (_bcgg *UDC )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {start .Name .Local ="\u0055\u0044\u0043";e .EncodeToken (start );e .EncodeToken (_g .EndElement {Name :start .Name });
return nil ;};

// Validate validates the ISO639_2 and its children
func (_ecf *ISO639_2 )Validate ()error {return _ecf .ValidateWithPath ("\u0049\u0053\u004f\u0036\u0033\u0039\u005f\u0032");};func NewISO639_2 ()*ISO639_2 {_caa :=&ISO639_2 {};return _caa };

// Validate validates the ElementOrRefinementContainer and its children
func (_dfb *ElementOrRefinementContainer )Validate ()error {return _dfb .ValidateWithPath ("\u0045\u006c\u0065\u006de\u006e\u0074\u004f\u0072\u0052\u0065\u0066\u0069\u006e\u0065m\u0065n\u0074\u0043\u006f\u006e\u0074\u0061\u0069n\u0065\u0072");};

// ValidateWithPath validates the URI and its children, prefixing error messages with path
func (_bce *URI )ValidateWithPath (path string )error {return nil };func (_ef *Box )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {start .Name .Local ="\u0042\u006f\u0078";e .EncodeToken (start );e .EncodeToken (_g .EndElement {Name :start .Name });
return nil ;};func NewDDC ()*DDC {_eg :=&DDC {};return _eg };func (_ceg *UDC )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {for {_ecfa ,_acc :=d .Token ();if _acc !=nil {return _a .Errorf ("\u0070a\u0072s\u0069\u006e\u0067\u0020\u0055\u0044\u0043\u003a\u0020\u0025\u0073",_acc );
};if _dbcb ,_cba :=_ecfa .(_g .EndElement );_cba &&_dbcb .Name ==start .Name {break ;};};return nil ;};type Point struct{};func (_cd *IMT )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {for {_bcg ,_efe :=d .Token ();if _efe !=nil {return _a .Errorf ("\u0070a\u0072s\u0069\u006e\u0067\u0020\u0049\u004d\u0054\u003a\u0020\u0025\u0073",_efe );
};if _ega ,_bcgf :=_bcg .(_g .EndElement );_bcgf &&_ega .Name ==start .Name {break ;};};return nil ;};

// ValidateWithPath validates the ElementsAndRefinementsGroup and its children, prefixing error messages with path
func (_dfg *ElementsAndRefinementsGroup )ValidateWithPath (path string )error {if _dfg .Any !=nil {if _da :=_dfg .Any .ValidateWithPath (path +"\u002f\u0041\u006e\u0079");_da !=nil {return _da ;};};return nil ;};func NewPoint ()*Point {_cca :=&Point {};
return _cca };func (_ae *ISO3166 )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {for {_ec ,_dfe :=d .Token ();if _dfe !=nil {return _a .Errorf ("\u0070\u0061\u0072\u0073in\u0067\u0020\u0049\u0053\u004f\u0033\u0031\u0036\u0036\u003a\u0020\u0025\u0073",_dfe );
};if _acf ,_bf :=_ec .(_g .EndElement );_bf &&_acf .Name ==start .Name {break ;};};return nil ;};func (_gde *W3CDTF )MarshalXML (e *_g .Encoder ,start _g .StartElement )error {start .Name .Local ="\u0057\u0033\u0043\u0044\u0054\u0046";e .EncodeToken (start );
e .EncodeToken (_g .EndElement {Name :start .Name });return nil ;};func (_fce *ISO639_2 )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {for {_fcg ,_bcb :=d .Token ();if _bcb !=nil {return _a .Errorf ("p\u0061r\u0073\u0069\u006e\u0067\u0020\u0049\u0053\u004f6\u0033\u0039\u005f\u0032: \u0025\u0073",_bcb );
};if _dbg ,_aed :=_fcg .(_g .EndElement );_aed &&_dbg .Name ==start .Name {break ;};};return nil ;};

// Validate validates the Point and its children
func (_gab *Point )Validate ()error {return _gab .ValidateWithPath ("\u0050\u006f\u0069n\u0074")};func NewUDC ()*UDC {_fbcd :=&UDC {};return _fbcd };func (_ffg *LCSH )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {for {_cg ,_dbb :=d .Token ();
if _dbb !=nil {return _a .Errorf ("\u0070\u0061r\u0073\u0069\u006eg\u0020\u004c\u0043\u0053\u0048\u003a\u0020\u0025\u0073",_dbb );};if _ge ,_cgc :=_cg .(_g .EndElement );_cgc &&_ge .Name ==start .Name {break ;};};return nil ;};func NewLCC ()*LCC {_ce :=&LCC {};
return _ce };func (_gfa *ElementsAndRefinementsGroup )UnmarshalXML (d *_g .Decoder ,start _g .StartElement )error {_ddb :for {_dcb ,_ca :=d .Token ();if _ca !=nil {return _ca ;};switch _dcg :=_dcb .(type ){case _g .StartElement :switch _dcg .Name {case _g .Name {Space :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0072\u0067/\u0064c\u002f\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0073\u002f\u0031\u002e\u0031\u002f",Local :"\u0061\u006e\u0079"}:_gfa .Any =_f .NewAny ();
if _eec :=d .DecodeElement (_gfa .Any ,&_dcg );_eec !=nil {return _eec ;};default:_b .Log .Debug ("\u0073\u006b\u0069\u0070\u0070\u0069\u006e\u0067\u0020\u0075\u006e\u0073\u0075\u0070\u0070\u006f\u0072\u0074ed\u0020e\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006f\u006e\u0020\u0045\u006ce\u006d\u0065\u006e\u0074\u0073\u0041\u006e\u0064\u0052\u0065\u0066\u0069\u006e\u0065\u006d\u0065\u006et\u0073\u0047\u0072\u006f\u0075\u0070\u0020\u0025\u0076",_dcg .Name );
if _cb :=d .Skip ();_cb !=nil {return _cb ;};};case _g .EndElement :break _ddb ;case _g .CharData :};};return nil ;};func init (){_c .RegisterConstructor ("\u0068t\u0074\u0070\u003a\u002f/\u0070\u0075\u0072\u006c\u002eo\u0072g\u002fd\u0063\u002f\u0074\u0065\u0072\u006d\u0073/","\u004c\u0043\u0053\u0048",NewLCSH );
_c .RegisterConstructor ("\u0068t\u0074\u0070\u003a\u002f/\u0070\u0075\u0072\u006c\u002eo\u0072g\u002fd\u0063\u002f\u0074\u0065\u0072\u006d\u0073/","\u004d\u0045\u0053\u0048",NewMESH );_c .RegisterConstructor ("\u0068t\u0074\u0070\u003a\u002f/\u0070\u0075\u0072\u006c\u002eo\u0072g\u002fd\u0063\u002f\u0074\u0065\u0072\u006d\u0073/","\u0044\u0044\u0043",NewDDC );
_c .RegisterConstructor ("\u0068t\u0074\u0070\u003a\u002f/\u0070\u0075\u0072\u006c\u002eo\u0072g\u002fd\u0063\u002f\u0074\u0065\u0072\u006d\u0073/","\u004c\u0043\u0043",NewLCC );_c .RegisterConstructor ("\u0068t\u0074\u0070\u003a\u002f/\u0070\u0075\u0072\u006c\u002eo\u0072g\u002fd\u0063\u002f\u0074\u0065\u0072\u006d\u0073/","\u0055\u0044\u0043",NewUDC );
_c .RegisterConstructor ("\u0068t\u0074\u0070\u003a\u002f/\u0070\u0075\u0072\u006c\u002eo\u0072g\u002fd\u0063\u002f\u0074\u0065\u0072\u006d\u0073/","\u0050\u0065\u0072\u0069\u006f\u0064",NewPeriod );_c .RegisterConstructor ("\u0068t\u0074\u0070\u003a\u002f/\u0070\u0075\u0072\u006c\u002eo\u0072g\u002fd\u0063\u002f\u0074\u0065\u0072\u006d\u0073/","\u0057\u0033\u0043\u0044\u0054\u0046",NewW3CDTF );
_c .RegisterConstructor ("\u0068t\u0074\u0070\u003a\u002f/\u0070\u0075\u0072\u006c\u002eo\u0072g\u002fd\u0063\u002f\u0074\u0065\u0072\u006d\u0073/","\u0044\u0043\u004d\u0049\u0054\u0079\u0070\u0065",NewDCMIType );_c .RegisterConstructor ("\u0068t\u0074\u0070\u003a\u002f/\u0070\u0075\u0072\u006c\u002eo\u0072g\u002fd\u0063\u002f\u0074\u0065\u0072\u006d\u0073/","\u0049\u004d\u0054",NewIMT );
_c .RegisterConstructor ("\u0068t\u0074\u0070\u003a\u002f/\u0070\u0075\u0072\u006c\u002eo\u0072g\u002fd\u0063\u002f\u0074\u0065\u0072\u006d\u0073/","\u0055\u0052\u0049",NewURI );_c .RegisterConstructor ("\u0068t\u0074\u0070\u003a\u002f/\u0070\u0075\u0072\u006c\u002eo\u0072g\u002fd\u0063\u002f\u0074\u0065\u0072\u006d\u0073/","\u0049\u0053\u004f\u0036\u0033\u0039\u002d\u0032",NewISO639_2 );
_c .RegisterConstructor ("\u0068t\u0074\u0070\u003a\u002f/\u0070\u0075\u0072\u006c\u002eo\u0072g\u002fd\u0063\u002f\u0074\u0065\u0072\u006d\u0073/","\u0052F\u0043\u0031\u0037\u0036\u0036",NewRFC1766 );_c .RegisterConstructor ("\u0068t\u0074\u0070\u003a\u002f/\u0070\u0075\u0072\u006c\u002eo\u0072g\u002fd\u0063\u002f\u0074\u0065\u0072\u006d\u0073/","\u0052F\u0043\u0033\u0030\u0036\u0036",NewRFC3066 );
_c .RegisterConstructor ("\u0068t\u0074\u0070\u003a\u002f/\u0070\u0075\u0072\u006c\u002eo\u0072g\u002fd\u0063\u002f\u0074\u0065\u0072\u006d\u0073/","\u0050\u006f\u0069n\u0074",NewPoint );_c .RegisterConstructor ("\u0068t\u0074\u0070\u003a\u002f/\u0070\u0075\u0072\u006c\u002eo\u0072g\u002fd\u0063\u002f\u0074\u0065\u0072\u006d\u0073/","\u0049S\u004f\u0033\u0031\u0036\u0036",NewISO3166 );
_c .RegisterConstructor ("\u0068t\u0074\u0070\u003a\u002f/\u0070\u0075\u0072\u006c\u002eo\u0072g\u002fd\u0063\u002f\u0074\u0065\u0072\u006d\u0073/","\u0042\u006f\u0078",NewBox );_c .RegisterConstructor ("\u0068t\u0074\u0070\u003a\u002f/\u0070\u0075\u0072\u006c\u002eo\u0072g\u002fd\u0063\u002f\u0074\u0065\u0072\u006d\u0073/","\u0054\u0047\u004e",NewTGN );
_c .RegisterConstructor ("\u0068t\u0074\u0070\u003a\u002f/\u0070\u0075\u0072\u006c\u002eo\u0072g\u002fd\u0063\u002f\u0074\u0065\u0072\u006d\u0073/","\u0065\u006c\u0065\u006de\u006e\u0074\u004f\u0072\u0052\u0065\u0066\u0069\u006e\u0065m\u0065n\u0074\u0043\u006f\u006e\u0074\u0061\u0069n\u0065\u0072",NewElementOrRefinementContainer );
_c .RegisterConstructor ("\u0068t\u0074\u0070\u003a\u002f/\u0070\u0075\u0072\u006c\u002eo\u0072g\u002fd\u0063\u002f\u0074\u0065\u0072\u006d\u0073/","e\u006c\u0065\u006d\u0065\u006e\u0074s\u0041\u006e\u0064\u0052\u0065\u0066\u0069\u006e\u0065m\u0065\u006e\u0074s\u0047r\u006f\u0075\u0070",NewElementsAndRefinementsGroup );
};