module github.com/unidoc/unioffice/v2

go 1.19

require (
	github.com/richardlehane/msoleps v1.0.3
	github.com/stretchr/testify v1.9.0
	github.com/unidoc/emf v0.1.0
	github.com/unidoc/unichart v0.3.0
	github.com/unidoc/unipdf/v3 v3.66.0
	github.com/unidoc/unitype v0.4.0
	golang.org/x/image v0.18.0
)

require (
	github.com/adrg/strutil v0.3.1 // indirect
	github.com/adrg/sysfont v0.1.2 // indirect
	github.com/adrg/xdg v0.5.0 // indirect
	github.com/davecgh/go-spew v1.1.1 // indirect
	github.com/disintegration/imaging v1.6.2 // indirect
	github.com/gabriel-vasile/mimetype v1.4.3 // indirect
	github.com/golang/freetype v0.0.0-20170609003504-e2365dfdc4a0 // indirect
	github.com/gorilla/i18n v0.0.0-20150820051429-8b358169da46 // indirect
	github.com/llgcode/draw2d v0.0.0-20231212091825-f55e0c776b44 // indirect
	github.com/pmezard/go-difflib v1.0.0 // indirect
	github.com/sirupsen/logrus v1.9.3 // indirect
	github.com/unidoc/freetype v0.2.3 // indirect
	github.com/unidoc/garabic v0.0.0-20220702200334-8c7cb25baa11 // indirect
	github.com/unidoc/pkcs7 v0.2.0 // indirect
	github.com/unidoc/timestamp v0.0.0-20200412005513-91597fd3793a // indirect
	golang.org/x/crypto v0.31.0 // indirect
	golang.org/x/net v0.33.0 // indirect
	golang.org/x/sys v0.28.0 // indirect
	golang.org/x/text v0.21.0 // indirect
	golang.org/x/xerrors v0.0.0-20240716161551-93cc26a95ae9 // indirect
	gopkg.in/yaml.v3 v3.0.1 // indirect
)
