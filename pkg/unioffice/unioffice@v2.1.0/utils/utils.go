//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package utils ;import (_dg "bytes";_g "github.com/unidoc/unioffice/v2/document";_ee "github.com/unidoc/unioffice/v2/document/convert";_e "github.com/unidoc/unipdf/v3/model";);

// GetNumPages will try to get actual document page count by converting the document to a PDF first
// and then get the actual page count from the PDF result.
//
// WARNING: This method is currently in experimental state as the PDF result might have incorrect page count.
func GetNumPages (d *_g .Document )(int ,error ){var _ga _dg .Buffer ;_c :=_ee .ConvertToPdf (d );if _da :=_c .Write (&_ga );_da !=nil {return 0,_da ;};_b ,_bg :=_e .NewPdfReader (_dg .NewReader (_ga .Bytes ()));if _bg !=nil {return 0,_bg ;};_dc ,_bg :=_b .GetNumPages ();
if _bg !=nil {return 0,_bg ;};return _dc ,nil ;};