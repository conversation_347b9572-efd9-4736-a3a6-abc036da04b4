//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package presentation ;import (_ge "archive/zip";_dga "bytes";_acb "encoding/xml";_db "errors";_eff "fmt";_geb "github.com/unidoc/unioffice/v2";_df "github.com/unidoc/unioffice/v2/common";_ca "github.com/unidoc/unioffice/v2/common/logger";_ace "github.com/unidoc/unioffice/v2/common/tempstorage";
_cb "github.com/unidoc/unioffice/v2/drawing";_ef "github.com/unidoc/unioffice/v2/internal/formatutils";_ac "github.com/unidoc/unioffice/v2/internal/license";_a "github.com/unidoc/unioffice/v2/measurement";_bd "github.com/unidoc/unioffice/v2/schema/soo/dml";
_b "github.com/unidoc/unioffice/v2/schema/soo/dml/chart";_c "github.com/unidoc/unioffice/v2/schema/soo/ofc/sharedTypes";_e "github.com/unidoc/unioffice/v2/schema/soo/pkg/relationships";_eg "github.com/unidoc/unioffice/v2/schema/soo/pml";_be "github.com/unidoc/unioffice/v2/zippkg";
_ba "image";_bgf "image/jpeg";_g "io";_fd "math";_fa "os";_ag "path";_dg "sort";_bg "strconv";_d "strings";);

// ExtractText returns text from a presentation as a PresentationText object.
func (_bga *Presentation )ExtractText ()*PresentationText {_cg :=[]*SlideText {};for _ ,_cd :=range _bga .Slides (){_bea :=_cd .ExtractText ();if _bea !=nil {_cg =append (_cg ,_bea );};};return &PresentationText {Slides :_cg };};

// ClrMru returns the ClrMru property.
func (_gg PresentationProperties )ClrMru ()*_bd .CT_ColorMRU {return _gg ._fag .ClrMru };func _dgag (_cfgf []*_eg .CT_GroupShapeChoice )[]*_eg .CT_GroupShapeChoice {var _gee []*_eg .CT_GroupShapeChoice ;for _ ,_gbba :=range _cfgf {if _gbba .Pic ==nil {_gee =append (_gee ,_gbba );
};};return _gee ;};

// AddImageToRels adds an image relationship to a slide without putting image on the slide.
func (_bgeb Slide )AddImageToRels (img _df .ImageRef )string {_ggad :=0;for _eceb ,_gfbf :=range _bgeb ._acbf .Images {if _gfbf ==img {_ggad =_eceb +1;break ;};};var _efef string ;for _faf ,_afbc :=range _bgeb ._acbf .Slides (){if _afbc ._gbabe ==_bgeb ._gbabe {_gbde :=_eff .Sprintf ("\u002e\u002e\u002f\u006ded\u0069\u0061\u002f\u0069\u006d\u0061\u0067\u0065\u0025\u0064\u002e\u0025\u0073",_ggad ,img .Format ());
_geg :=_bgeb ._acbf ._adg [_faf ].AddRelationship (_gbde ,_geb .ImageType );_efef =_geg .ID ();};};return _efef ;};

// AddParagraph adds a paragraph to the text box
func (_aga TextBox )AddParagraph ()_cb .Paragraph {_dbcde :=_bd .NewCT_TextParagraph ();_aga ._dfea .TxBody .P =append (_aga ._dfea .TxBody .P ,_dbcde );return _cb .MakeParagraph (_dbcde );};

// SetHeight sets height of slide screen size with given value in EMU units.
func (_aae *SlideScreenSize )SetHeight (val int32 ){_aae [1]=val };

// RemoveSlide removes a slide from a presentation.
func (_ebd *Presentation )RemoveSlide (s Slide )error {_gbd :=false ;_bdfg :=0;for _fcga ,_cbdd :=range _ebd ._bac {if _cbdd ==s ._gbabe {if _ebd ._ad .SldIdLst .SldId [_fcga ]!=s ._fceg {return _db .New ("i\u006e\u0063\u006f\u006e\u0073\u0069s\u0074\u0065\u006e\u0063\u0079\u0020i\u006e\u0020\u0073\u006c\u0069\u0064\u0065s\u0020\u0061\u006e\u0064\u0020\u0049\u0044\u0020\u006c\u0069s\u0074");
};copy (_ebd ._bac [_fcga :],_ebd ._bac [_fcga +1:]);_ebd ._bac =_ebd ._bac [0:len (_ebd ._bac )-1];copy (_ebd ._adg [_fcga :],_ebd ._adg [_fcga +1:]);_ebd ._adg =_ebd ._adg [0:len (_ebd ._adg )-1];copy (_ebd ._ad .SldIdLst .SldId [_fcga :],_ebd ._ad .SldIdLst .SldId [_fcga +1:]);
_ebd ._ad .SldIdLst .SldId =_ebd ._ad .SldIdLst .SldId [0:len (_ebd ._ad .SldIdLst .SldId )-1];copy (_ebd ._ccd [_fcga :],_ebd ._ccd [_fcga +1:]);_ebd ._ccd =_ebd ._ccd [0:len (_ebd ._ccd )-1];_gbd =true ;_bdfg =_fcga ;};};if !_gbd {return _db .New ("u\u006ea\u0062\u006c\u0065\u0020\u0074\u006f\u0020\u0066i\u006e\u0064\u0020\u0073li\u0064\u0065");
};_bgfc :=_geb .AbsoluteFilename (_geb .DocTypePresentation ,_geb .SlideType ,0);return _ebd .ContentTypes .RemoveOverrideByIndex (_bgfc ,_bdfg );};

// Height returns slide screen size height in EMU units.
func (_agee *SlideScreenSize )Height ()int32 {return _agee [1]};

// SlideSize represents a slide size of a presentation.
type SlideSize struct{_bfe *_eg .CT_SlideSize ;_geaa *Presentation ;};

// ClearAll completely clears a placeholder. To be useable, at least one
// paragraph must be added after ClearAll via AddParagraph.
func (_dgaf PlaceHolder )ClearAll (){_dgaf ._bdb .SpPr =_bd .NewCT_ShapeProperties ();_dgaf ._bdb .TxBody =_bd .NewCT_TextBody ();_dgaf ._bdb .TxBody .LstStyle =_bd .NewCT_TextListStyle ();};

// GetImageByTarget returns an ImageRef with the given target in the
// document.
func (_cfec *Presentation )GetImageByTarget (target string )(_df .ImageRef ,bool ){for _ ,_baea :=range _cfec .Images {if _baea .Target ()==target {return _baea ,true ;};};return _df .ImageRef {},false ;};

// AddDefaultSlideWithLayout tries to replicate what PowerPoint does when
// inserting a slide with a new style by clearing placeholder content and removing
// some placeholders.  Use AddSlideWithLayout if you need more control.
func (_bda *Presentation )AddDefaultSlideWithLayout (l SlideLayout )(Slide ,error ){_cea ,_gcb :=_bda .AddSlideWithLayout (l );for _ ,_cbab :=range _cea .PlaceHolders (){_cbab .Clear ();switch _cbab .Type (){case _eg .ST_PlaceholderTypeFtr ,_eg .ST_PlaceholderTypeDt ,_eg .ST_PlaceholderTypeSldNum :_cbab .Remove ();
};};return _cea ,_gcb ;};

// AddTable adds a new table to a placeholder.
func (_aeca PlaceHolder )AddTable ()*_df .Table {_aeca .Clear ();_cgc :=_eg .NewCT_GroupShapeChoice ();_aeca ._cdf .CSld .SpTree .GroupShapeChoice =append (_aeca ._cdf .CSld .SpTree .GroupShapeChoice ,_cgc );_cgcf :=_eg .NewCT_GraphicalObjectFrame ();_cgc .GraphicFrame =_cgcf ;
_cgcf .Xfrm .Off =_bd .NewCT_Point2D ();_affe :=int64 (1);_cgcf .Xfrm .Off .XAttr =_bd .ST_Coordinate {ST_CoordinateUnqualified :&_affe };_cgcf .Xfrm .Off .YAttr =_bd .ST_Coordinate {ST_CoordinateUnqualified :&_affe };_agg :=_cgcf .Graphic .CT_GraphicalObject .GraphicData ;
_agg .UriAttr ="\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002eo\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073.\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077\u0069\u006e\u0067\u006dl/\u0032\u0030\u0030\u0036\u002f\u0074\u0061\u0062\u006c\u0065";
_bec :=_df .NewTableWithXfrm (_cgcf .Xfrm );_agg .Any =append (_agg .Any ,_bec .X ());return _bec ;};

// ExtractText returns text from a slide as a SlideText object.
func (_gb *Slide )ExtractText ()*SlideText {_fab :=_cag (_gb ._acbf ,_gb ._gbabe .CSld .SpTree .GroupShapeChoice ,[]rectangle {},[]*TextItem {});_dg .Sort (sort2d (_fab ));return &SlideText {Items :_fab };};

// Save writes the presentation out to a writer in the Zip package format
func (_edd *Presentation )Save (w _g .Writer )error {return _edd .save (w ,false )};

// CopySlide copies existing slide from another presentation and inserts it as a last one.
func (_aeg *Presentation )CopySlide (s Slide )(Slide ,error ){_baca :=_eg .NewCT_SlideIdListEntry ();_baca .IdAttr =_aeg .nextSlideID ();_aeg ._ad .SldIdLst .SldId =append (_aeg ._ad .SldIdLst .SldId ,_baca );_gga :=s .GetSlideLayout ();_efe :=true ;for _ ,_eac :=range _aeg ._bdbg {if _eac ==_gga {_efe =false ;
break ;};};if _efe {_aeg .addLayoutFromSlide (s );};_aeg ._bac =append (_aeg ._bac ,s ._gbabe );_aeg ._ccd =append (_aeg ._ccd ,len (_aeg ._bac ));_aecb :=_aeg ._aad .AddAutoRelationship (_geb .DocTypePresentation ,_geb .OfficeDocumentType ,len (_aeg ._bac ),_geb .SlideType );
_baca .RIdAttr =_aecb .ID ();_dac :=_geb .AbsoluteFilename (_geb .DocTypePresentation ,_geb .SlideType ,len (_aeg ._bac ));_aeg .ContentTypes .AddOverride (_dac ,_geb .SlideContentType );_feb :=_df .NewRelationships ();_aeg ._adg =append (_aeg ._adg ,_feb );
_bcb :=len (_aeg ._adg )-1;for _adf ,_febb :=range _aeg ._bdbg {if _febb ==_gga {_fbbe :=_aeg ._bebc [_adf ];for _ ,_eda :=range _fbbe .X ().Relationship {if _eda .TypeAttr !=_geb .SlideMasterType {_aeg ._adg [_bcb ].X ().Relationship =append (_aeg ._adg [_bcb ].X ().Relationship ,_eda );
};};_feb .AddAutoRelationship (_geb .DocTypePresentation ,_geb .SlideType ,_adf +1,_geb .SlideLayoutType );};};_gddd :=Slide {_baca ,s ._gbabe ,_aeg ,nil };return _gddd ,nil ;};

// Clear clears the placeholder contents and adds a single empty paragraph.  The
// empty paragrah is required by PowerPoint or it will report the file as being
// invalid.
func (_cef PlaceHolder )Clear (){_cef .ClearAll ();_cfc :=_bd .NewCT_TextParagraph ();_cef ._bdb .TxBody .P =[]*_bd .CT_TextParagraph {_cfc };_cfc .EndParaRPr =_bd .NewCT_TextCharacterProperties ();_cfc .EndParaRPr .LangAttr =_geb .String ("\u0065\u006e\u002dU\u0053");
};

// ValidateWithPath validates the slide passing path informaton for a better
// error message.
func (_accg Slide )ValidateWithPath (path string )error {if _fcba :=_accg ._gbabe .ValidateWithPath (path );_fcba !=nil {return _fcba ;};for _ ,_dfg :=range _accg ._gbabe .CSld .SpTree .GroupShapeChoice {if _dfg .Sp !=nil &&_dfg .Sp .TxBody !=nil {if len (_dfg .Sp .TxBody .P )==0{return _db .New (path +"\u0020\u003a \u0073\u006c\u0069\u0064\u0065 \u0073\u0068\u0061\u0070\u0065 \u0077\u0069\u0074\u0068\u0020\u0061\u0020\u0074\u0078\u0062\u006f\u0064\u0079\u0020\u006d\u0075\u0073\u0074\u0020\u0063\u006f\u006e\u0074\u0061\u0069\u006e\u0020\u0070\u0061\u0072\u0061\u0067\u0072\u0061\u0070\u0068\u0073");
};};};return nil ;};func (_baf *Presentation )Validate ()error {if _bbe :=_baf ._ad .Validate ();_bbe !=nil {return _bbe ;};for _ece ,_debc :=range _baf .Slides (){if _edf :=_debc .ValidateWithPath (_eff .Sprintf ("\u0053l\u0069\u0064\u0065\u005b\u0025\u0064]",_ece ));
_edf !=nil {return _edf ;};};for _fec ,_ebcd :=range _baf ._gbc {if _ccf :=_ebcd .ValidateWithPath (_eff .Sprintf ("\u0053l\u0069d\u0065\u004d\u0061\u0073\u0074\u0065\u0072\u005b\u0025\u0064\u005d",_fec ));_ccf !=nil {return _ccf ;};};for _afd ,_ebae :=range _baf ._bdbg {if _acfgb :=_ebae .ValidateWithPath (_eff .Sprintf ("\u0053l\u0069d\u0065\u004c\u0061\u0079\u006f\u0075\u0074\u005b\u0025\u0064\u005d",_afd ));
_acfgb !=nil {return _acfgb ;};};return nil ;};type sort2d []*TextItem ;func (_ddecf *Presentation )nextSlideID ()uint32 {_aggf :=uint32 (256);for _ ,_eadf :=range _ddecf ._ad .SldIdLst .SldId {if _eadf .IdAttr >=_aggf {_aggf =_eadf .IdAttr +1;};};return _aggf ;
};

// PresentationProperties contains document specific properties.
type PresentationProperties struct{_fag *_eg .PresentationPr };func (_bdfd *Presentation )addLayoutFromSlide (_fbf Slide )error {_fcc :=1;for _ ,_gfa :=range _bdfd .Images {if _ggfa ,_dcb :=_ef .StringToNumbers (_gfa .Target ());_dcb &&_ggfa >=_fcc {_fcc =_ggfa +1;
};};_bdfd ._bdbg =append (_bdfd ._bdbg ,_fbf .GetSlideLayout ());_bbdg :=_fbf .getSlideLayoutRels ();for _ ,_dffa :=range _bbdg .X ().Relationship {if _dffa .TypeAttr ==_geb .ImageType {_fcf ,_cfcb :=_fbf ._acbf .GetImageByTarget (_dffa .TargetAttr );if _cfcb {if _agba ,_fbgc :=_ef .StringToNumbers (_fcf .Target ());
_fbgc &&_agba < _fcc {_fcf .SetTarget (_d .ReplaceAll (_fcf .Target (),_eff .Sprint (_agba ),_eff .Sprint (_fcc )));_dffa .TargetAttr =_fcf .Target ();_fcc =_fcc +1;};_bdfd .Images =append (_bdfd .Images ,_fcf );};};};_bdfd ._bebc =append (_bdfd ._bebc ,_bbdg );
if len (_bdfd ._gbc )> 0&&len (_bdfd ._dgc )> 0{_beg :=_bdfd ._dgc [0].AddAutoRelationship (_geb .DocTypePresentation ,_geb .SlideMasterType ,len (_bdfd ._bdbg ),_geb .SlideLayoutType );_cgdd :=_eg .NewCT_SlideLayoutIdListEntry ();_cgdd .IdAttr =_geb .Uint32 (2147483649);
_cgdd .RIdAttr =_beg .ID ();for _ ,_gadc :=range _bdfd ._gbc [0].SldLayoutIdLst .SldLayoutId {if *_gadc .IdAttr >=*_cgdd .IdAttr {_cgdd .IdAttr =_geb .Uint32 ((*_gadc .IdAttr +1));};};_bdfd ._gbc [0].SldLayoutIdLst .SldLayoutId =append (_bdfd ._gbc [0].SldLayoutIdLst .SldLayoutId ,_cgdd );
};_fdf :=_geb .AbsoluteFilename (_geb .DocTypePresentation ,_geb .SlideLayoutType ,len (_bdfd ._bdbg ));_bdfd .ContentTypes .AddOverride (_fdf ,_geb .SlideLayoutContentType );return nil ;};

// SetTextAnchor controls the text anchoring
func (_beaa TextBox )SetTextAnchor (a _bd .ST_TextAnchoringType ){_beaa ._dfea .TxBody .BodyPr =_bd .NewCT_TextBodyProperties ();_beaa ._dfea .TxBody .BodyPr .AnchorAttr =a ;};

// SetText sets the text of a placeholder for the initial paragraph. This is a
// shortcut method that is useful for things like titles which only contain a
// single paragraph.
func (_aag PlaceHolder )SetText (text string ){_aag .Clear ();_aec :=_bd .NewEG_TextRun ();_aec .TextRunChoice .R =_bd .NewCT_RegularTextRun ();_aec .TextRunChoice .R .T =text ;if len (_aag ._bdb .TxBody .P )==0{_aag ._bdb .TxBody .P =append (_aag ._bdb .TxBody .P ,_bd .NewCT_TextParagraph ());
};_aag ._bdb .TxBody .P [0].EG_TextRun =nil ;_aag ._bdb .TxBody .P [0].EG_TextRun =append (_aag ._bdb .TxBody .P [0].EG_TextRun ,_aec );};

// Properties returns the properties of the TextBox.
func (_gcag TextBox )Properties ()_cb .ShapeProperties {if _gcag ._dfea .SpPr ==nil {_gcag ._dfea .SpPr =_bd .NewCT_ShapeProperties ();};return _cb .MakeShapeProperties (_gcag ._dfea .SpPr );};func _gd (_gfd *Presentation ,_bfa *_eg .CT_Shape ,_gc *_eg .CT_GraphicalObjectFrame ,_bee *TableInfo ,_ebb *_bd .CT_Transform2D ,_fgg int ,_fb []rectangle ,_cf []*_bd .CT_TextParagraph )[]*TextItem {_de :=[]*TextItem {};
var _dbgd ,_ec ,_efdg ,_ebg ,_aaf ,_fe int64 ;_gfgf :=_ebb ==nil ;_aca :=0;for _ ,_dfe :=range _cf {for _ ,_cc :=range _dfe .EG_TextRun {if _fff :=_cc .TextRunChoice .R ;_fff !=nil {if !_gfgf {if _ebb .Off !=nil {if _ddb :=_ebb .Ext ;_ddb !=nil {_aaf ,_fe =_ddb .CxAttr ,_ddb .CyAttr ;
};if _fba :=_ebb .Off .XAttr .ST_CoordinateUnqualified ;_fba !=nil {_dbgd =*_fba ;_ec =_dbgd +_aaf ;_gfgf =true ;};if _ea :=_ebb .Off .YAttr .ST_CoordinateUnqualified ;_ea !=nil {_efdg =*_ea ;_ebg =_efdg +_fe ;_gfgf =true ;};};};_acfg :=append ([]rectangle {},_fb ...);
_acfg =append (_acfg ,rectangle {_acc :_dbgd ,_ffb :_ec ,_gfg :_efdg ,_aa :_ebg });_de =append (_de ,&TextItem {Presentation :_gfd ,Shape :_bfa ,GraphicFrame :_gc ,TableInfo :_bee ,Paragraph :_dfe ,Run :_fff ,Text :_fff .T ,_af :_acfg ,_cbe :_fgg ,_ae :_aca });
_aca ++;};};};return _de ;};

// Remove removes a placeholder from a presentation.
func (_gcd PlaceHolder )Remove ()error {for _gfdb ,_debg :=range _gcd ._cdf .CSld .SpTree .GroupShapeChoice {if _debg .Sp !=nil &&_debg .Sp ==_gcd ._bdb {copy (_gcd ._cdf .CSld .SpTree .GroupShapeChoice [_gfdb :],_gcd ._cdf .CSld .SpTree .GroupShapeChoice [_gfdb +1:]);
_gcd ._cdf .CSld .SpTree .GroupShapeChoice =_gcd ._cdf .CSld .SpTree .GroupShapeChoice [0:len (_gcd ._cdf .CSld .SpTree .GroupShapeChoice )-1];return nil ;};};return _db .New ("\u0070\u006c\u0061\u0063\u0065\u0068\u006f\u006c\u0064\u0065r\u0020\u006e\u006f\u0074\u0020\u0066\u006fu\u006e\u0064\u0020\u0069\u006e\u0020\u0073\u006c\u0069\u0064\u0065");
};

// ShowPr returns the ShowPr property.
func (_egdb PresentationProperties )ShowPr ()*_eg .CT_ShowProperties {return _egdb ._fag .ShowPr };

// Type returns the type of the slide layout.
func (_bbfc SlideLayout )Type ()_eg .ST_SlideLayoutType {return _bbfc ._gced .TypeAttr };

// Presentation returns a slide's presentation.
func (_bbg Slide )Presentation ()*Presentation {return _bbg ._acbf };

// PlaceHolder is a place holder from a slide.
type PlaceHolder struct{_bdb *_eg .CT_Shape ;_cdf *_eg .Sld ;};

// SetOffsetY sets vertical offset of text box in distance units (see measurement package).
func (_gcge TextBox )SetOffsetY (offY float64 ){_eaab :=_gcge .getOff ();_fece :=_a .ToEMU (offY );_eaab .YAttr =_bd .ST_Coordinate {ST_CoordinateUnqualified :&_fece };};

// SaveAsTemplate writes the presentation out to a writer in the Zip package format as a template
func (_cefe *Presentation )SaveAsTemplate (w _g .Writer )error {return _cefe .save (w ,true )};

// ExtLst returns the ExtLst property.
func (_agb PresentationProperties )ExtLst ()*_eg .CT_ExtensionList {return _agb ._fag .ExtLst };

// TextBox is a text box within a slide.
type TextBox struct{_dfea *_eg .CT_Shape };

// SlideMaster is the slide master for a presentation.
type SlideMaster struct{_fbbc *Presentation ;_dffc _df .Relationships ;_aaff *_eg .SldMaster ;};

// HtmlPubPr returns the HtmlPubPr property.
func (_bfc PresentationProperties )HtmlPubPr ()*_eg .CT_HtmlPublishProperties {return _bfc ._fag .HtmlPubPr ;};

// SetWidth sets width of slide screen size with given value in EMU units.
func (_fega *SlideScreenSize )SetWidth (val int32 ){_fega [0]=val };

// SlideLayouts returns the slide layouts defined in the presentation.
func (_abae *Presentation )SlideLayouts ()[]SlideLayout {_gfce :=[]SlideLayout {};for _ ,_caa :=range _abae ._bdbg {if _caa ==nil {continue ;};if _caa .CSld .NameAttr ==nil {continue ;};_gfce =append (_gfce ,SlideLayout {_caa });};return _gfce ;};

// Text returns text from a presentation as one string separated with line breaks.
func (_fffd *PresentationText )Text ()string {_fgf :=_dga .NewBuffer ([]byte {});for _ ,_fge :=range _fffd .Slides {_fgf .WriteString (_fge .Text ());};return _fgf .String ();};

// Sid returns the sid of slide
func (_bfae Slide )Sid ()*_eg .CT_SlideIdListEntry {return _bfae ._fceg };

// GetPlaceholder returns a placeholder given its type.  If there are multiplace
// placeholders of the same type, this method returns the first one.  You must use the
// PlaceHolders() method to access the others.
func (_ebcg Slide )GetPlaceholder (t _eg .ST_PlaceholderType )(PlaceHolder ,error ){for _ ,_ebbg :=range _ebcg ._gbabe .CSld .SpTree .GroupShapeChoice {if _ebbg .Sp !=nil &&_ebbg .Sp .NvSpPr !=nil &&_ebbg .Sp .NvSpPr .NvPr !=nil &&_ebbg .Sp .NvSpPr .NvPr .Ph !=nil {if _ebbg .Sp .NvSpPr .NvPr .Ph .TypeAttr ==t {return PlaceHolder {_ebbg .Sp ,_ebcg ._gbabe },nil ;
};};};return PlaceHolder {},_db .New ("\u0075\u006e\u0061\u0062\u006c\u0065\u0020\u0074\u006f\u0020\u0066i\u006e\u0064\u0020\u0070\u006c\u0061\u0063\u0065\u0068\u006fl\u0064\u0065\u0072");};

// X returns TextBox's underlying *pml.CT_Shape.
func (_bbbc TextBox )X ()*_eg .CT_Shape {return _bbbc ._dfea };

// OutlineViewPr returns the OutlineViewPr property.
func (_fgeb ViewProperties )OutlineViewPr ()*_eg .CT_OutlineViewProperties {return _fgeb ._ede .OutlineViewPr ;};

// WebPr returns the WebPr property.
func (_dff PresentationProperties )WebPr ()*_eg .CT_WebProperties {return _dff ._fag .WebPr };

// AddSlide adds a new slide to the presentation.
func (_agc *Presentation )AddSlide ()Slide {_ege :=_eg .NewCT_SlideIdListEntry ();_ege .IdAttr =_agc .nextSlideID ();_agc ._ad .SldIdLst .SldId =append (_agc ._ad .SldIdLst .SldId ,_ege );_bebe :=_eg .NewSld ();_bebe .CSld .SpTree .NvGrpSpPr .CNvPr .IdAttr =1;
_bebe .CSld .SpTree .GrpSpPr .Xfrm =_bd .NewCT_GroupTransform2D ();_bebe .CSld .SpTree .GrpSpPr .Xfrm .Off =_bd .NewCT_Point2D ();_bebe .CSld .SpTree .GrpSpPr .Xfrm .Off .XAttr .ST_CoordinateUnqualified =_geb .Int64 (0);_bebe .CSld .SpTree .GrpSpPr .Xfrm .Off .YAttr .ST_CoordinateUnqualified =_geb .Int64 (0);
_bebe .CSld .SpTree .GrpSpPr .Xfrm .Ext =_bd .NewCT_PositiveSize2D ();_bebe .CSld .SpTree .GrpSpPr .Xfrm .Ext .CxAttr =int64 (0*_a .Point );_bebe .CSld .SpTree .GrpSpPr .Xfrm .Ext .CyAttr =int64 (0*_a .Point );_bebe .CSld .SpTree .GrpSpPr .Xfrm .ChOff =_bebe .CSld .SpTree .GrpSpPr .Xfrm .Off ;
_bebe .CSld .SpTree .GrpSpPr .Xfrm .ChExt =_bebe .CSld .SpTree .GrpSpPr .Xfrm .Ext ;_agc ._bac =append (_agc ._bac ,_bebe );_agc ._ccd =append (_agc ._ccd ,len (_agc ._bac ));_daa :=_agc ._aad .AddAutoRelationship (_geb .DocTypePresentation ,_geb .OfficeDocumentType ,len (_agc ._bac ),_geb .SlideType );
_ege .RIdAttr =_daa .ID ();_egb :=_geb .AbsoluteFilename (_geb .DocTypePresentation ,_geb .SlideType ,len (_agc ._bac ));_agc .ContentTypes .AddOverride (_egb ,_geb .SlideContentType );_gfba :=_df .NewRelationships ();_agc ._adg =append (_agc ._adg ,_gfba );
_gfba .AddAutoRelationship (_geb .DocTypePresentation ,_geb .SlideType ,len (_agc ._bdbg ),_geb .SlideLayoutType );return Slide {_ege ,_bebe ,_agc ,nil };};

// Text returns text from a slide as one string separated with line breaks.
func (_gdd *SlideText )Text ()string {_bgd :=_dga .NewBuffer ([]byte {});for _ ,_ceb :=range _gdd .Items {if _ceb .Text !=""{_bgd .WriteString (_ceb .Text );_bgd .WriteString ("\u000a");};};return _bgd .String ();};func (_ffd sort2d )Len ()int {return len (_ffd )};
func (_bgg *chart )RelId ()string {return _bgg ._eb };

// NewSlideScreenSizeWithValue returns slide screen size with given width and height.
// Width and Height value is in EMU units, use our measurement.ToEMU to convert the -
// width and height value.
func NewSlideScreenSizeWithValue (width ,height int32 )SlideScreenSize {return SlideScreenSize {width ,height };};

// GetTableStyleById returns *dml.CT_TableStyle by its style id.
func (_cdaf *Presentation )GetTableStyleById (id string )*_bd .CT_TableStyle {_bcea :=_cdaf ._bge .TblStyle ();for _ ,_gef :=range _bcea {if _gef .StyleIdAttr ==id {return _gef ;};};return nil ;};

// Themes returns an array of presentation themes.
func (_afbe *Presentation )Themes ()[]*_bd .Theme {return _afbe ._bed };

// AddTextBox adds an empty textbox to a slide.
func (_bcee Slide )AddTextBox ()TextBox {_aaab :=_eg .NewCT_GroupShapeChoice ();_bcee ._gbabe .CSld .SpTree .GroupShapeChoice =append (_bcee ._gbabe .CSld .SpTree .GroupShapeChoice ,_aaab );_gaf :=_eg .NewCT_Shape ();_aaab .Sp =_gaf ;_gaf .SpPr =_bd .NewCT_ShapeProperties ();
_gaf .SpPr .Xfrm =_bd .NewCT_Transform2D ();_gaf .SpPr .GeometryChoice .PrstGeom =_bd .NewCT_PresetGeometry2D ();_gaf .SpPr .GeometryChoice .PrstGeom .PrstAttr =_bd .ST_ShapeTypeRect ;_gaf .NvSpPr =_eg .NewCT_ShapeNonVisual ();_gaf .NvSpPr .CNvSpPr =_bd .NewCT_NonVisualDrawingShapeProps ();
_ccdb :=true ;_gaf .NvSpPr .CNvSpPr .TxBoxAttr =&_ccdb ;_gaf .TxBody =_bd .NewCT_TextBody ();_gaf .TxBody .BodyPr =_bd .NewCT_TextBodyProperties ();_gaf .TxBody .BodyPr .WrapAttr =_bd .ST_TextWrappingTypeSquare ;_gaf .TxBody .BodyPr .TextAutofitChoice .SpAutoFit =_bd .NewCT_TextShapeAutofit ();
_fefd :=TextBox {_gaf };_fefd .Properties ().SetWidth (3*_a .Inch );_fefd .Properties ().SetHeight (1*_a .Inch );_fefd .Properties ().SetPosition (0,0);return _fefd ;};

// Slide represents a slide of a presentation.
type Slide struct{_fceg *_eg .CT_SlideIdListEntry ;_gbabe *_eg .Sld ;_acbf *Presentation ;_fagb *_bd .CT_ColorMapping ;};

// Presentation is the a presentation base document.
type Presentation struct{_df .DocBase ;_ad *_eg .Presentation ;_aad _df .Relationships ;_bac []*_eg .Sld ;_adg []_df .Relationships ;_ccd []int ;_gbc []*_eg .SldMaster ;_dgc []_df .Relationships ;_bbb []int ;_bdbg []*_eg .SldLayout ;_bebc []_df .Relationships ;
_bed []*_bd .Theme ;_fea []_df .Relationships ;_gbb []int ;_bge _df .TableStyles ;_ebba PresentationProperties ;_aba ViewProperties ;_gaaa []*_bd .CT_Hyperlink ;_eeb []*chart ;_cbee []*_eg .HandoutMaster ;_bab []*_eg .NotesMaster ;_ggf []int ;_effd []*_geb .XSDAny ;
_ed []int ;_acec map[string ]string ;_bbcd string ;};func (_dgcg *Slide )ensureClrMap (){if len (_dgcg ._acbf ._gbc )==0||len (_dgcg ._acbf ._bed )==0{return ;};_efag :=_dgcg ._acbf ._gbc [0];_fbfg :=_efag .ClrMap ;if _fdd :=_dgcg ._gbabe .ClrMapOvr ;_fdd !=nil {if _dbcd :=_fdd .ColorMappingOverrideChoice ;
_dbcd !=nil {if _dbcd .MasterClrMapping ==nil {if _efcfd :=_dbcd .OverrideClrMapping ;_efcfd !=nil {if _efcfd .Bg1Attr !=_bd .ST_ColorSchemeIndexUnset {_fbfg .Bg1Attr =_efcfd .Bg1Attr ;};if _efcfd .Tx1Attr !=_bd .ST_ColorSchemeIndexUnset {_fbfg .Tx1Attr =_efcfd .Tx1Attr ;
};if _efcfd .Bg2Attr !=_bd .ST_ColorSchemeIndexUnset {_fbfg .Bg2Attr =_efcfd .Bg2Attr ;};if _efcfd .Tx2Attr !=_bd .ST_ColorSchemeIndexUnset {_fbfg .Tx2Attr =_efcfd .Tx2Attr ;};if _efcfd .Accent1Attr !=_bd .ST_ColorSchemeIndexUnset {_fbfg .Accent1Attr =_efcfd .Accent1Attr ;
};if _efcfd .Accent2Attr !=_bd .ST_ColorSchemeIndexUnset {_fbfg .Accent2Attr =_efcfd .Accent2Attr ;};if _efcfd .Accent3Attr !=_bd .ST_ColorSchemeIndexUnset {_fbfg .Accent3Attr =_efcfd .Accent3Attr ;};if _efcfd .Accent4Attr !=_bd .ST_ColorSchemeIndexUnset {_fbfg .Accent4Attr =_efcfd .Accent4Attr ;
};if _efcfd .Accent5Attr !=_bd .ST_ColorSchemeIndexUnset {_fbfg .Accent5Attr =_efcfd .Accent5Attr ;};if _efcfd .Accent6Attr !=_bd .ST_ColorSchemeIndexUnset {_fbfg .Accent6Attr =_efcfd .Accent6Attr ;};if _efcfd .HlinkAttr !=_bd .ST_ColorSchemeIndexUnset {_fbfg .HlinkAttr =_efcfd .HlinkAttr ;
};if _efcfd .FolHlinkAttr !=_bd .ST_ColorSchemeIndexUnset {_fbfg .FolHlinkAttr =_efcfd .FolHlinkAttr ;};};};};};_dgcg ._fagb =_fbfg ;};type rectangle struct{_acc int64 ;_gfg int64 ;_ffb int64 ;_aa int64 ;};

// Size returns slide size value as SlideScreenSize.
func (_fecg *SlideSize )Size ()SlideScreenSize {return SlideScreenSize {_fecg ._bfe .CxAttr ,_fecg ._bfe .CyAttr };};

// Read reads a document from an io.Reader.
func Read (r _g .ReaderAt ,size int64 )(*Presentation ,error ){const _cfb ="\u0070\u0072\u0065\u0073\u0065\u006e\u0074\u0061\u0074\u0069\u006f\u006e:\u0052\u0065\u0061\u0064";if !_ac .GetLicenseKey ().IsLicensed ()&&!_gfaf {_eff .Println ("\u0055\u006e\u006ci\u0063\u0065\u006e\u0073e\u0064\u0020\u0076\u0065\u0072\u0073\u0069o\u006e\u0020\u006f\u0066\u0020\u0055\u006e\u0069\u004f\u0066\u0066\u0069\u0063\u0065");
_eff .Println ("\u002d\u0020\u0047e\u0074\u0020\u0061\u0020\u0074\u0072\u0069\u0061\u006c\u0020\u006c\u0069\u0063\u0065\u006e\u0073\u0065\u0020\u006f\u006e\u0020\u0068\u0074\u0074\u0070\u0073\u003a\u002f\u002fu\u006e\u0069\u0064\u006f\u0063\u002e\u0069\u006f");
return nil ,_db .New ("\u0075\u006e\u0069\u006f\u0066\u0066\u0069\u0063\u0065\u0020\u006ci\u0063\u0065\u006e\u0073\u0065\u0020\u0072\u0065\u0071\u0075i\u0072\u0065\u0064");};_ffdaf :="\u0075n\u006b\u006e\u006f\u0077\u006e";if _edae ,_gccg :=r .(*_fa .File );
_gccg {_ffdaf =_edae .Name ();};_dcaa :=_bdf ();_aea ,_bgec :=_ac .GenRefId ("\u0070\u0072");if _bgec !=nil {_ca .Log .Error ("\u0045R\u0052\u004f\u0052\u003a\u0020\u0025v",_bgec );return nil ,_bgec ;};_dcaa ._bbcd =_aea ;if _eacg :=_ac .Track (_dcaa ._bbcd ,_cfb ,_ffdaf );
_eacg !=nil {_ca .Log .Error ("\u0045R\u0052\u004f\u0052\u003a\u0020\u0025v",_eacg );return nil ,_eacg ;};_ggbdf ,_bgec :=_ace .TempDir ("\u0075\u006e\u0069\u006f\u0066\u0066\u0069\u0063\u0065-\u0070\u0070\u0074\u0078");if _bgec !=nil {return nil ,_bgec ;
};_dcaa .TmpPath =_ggbdf ;_cfef ,_bgec :=_ge .NewReader (r ,size );if _bgec !=nil {return nil ,_eff .Errorf ("\u0070a\u0072s\u0069\u006e\u0067\u0020\u007a\u0069\u0070\u003a\u0020\u0025\u0073",_bgec );};_cbdf :=[]*_ge .File {};_cbdf =append (_cbdf ,_cfef .File ...);
_cgfa :=false ;for _ ,_cbecg :=range _cbdf {if _cbecg .FileHeader .Name =="\u0064\u006f\u0063\u0050ro\u0070\u0073\u002f\u0063\u0075\u0073\u0074\u006f\u006d\u002e\u0078\u006d\u006c"{_cgfa =true ;break ;};};if _cgfa {_dcaa .CreateCustomProperties ();};_bafc :=_be .DecodeMap {};
_bafc .SetOnNewRelationshipFunc (_dcaa .onNewRelationship );_bafc .AddTarget (_geb .ContentTypesFilename ,_dcaa .ContentTypes .X (),"",0);_bafc .AddTarget (_geb .BaseRelsFilename ,_dcaa .Rels .X (),"",0);if _acgc :=_bafc .Decode (_cbdf );_acgc !=nil {return nil ,_acgc ;
};for _ ,_cad :=range _cbdf {if _cad ==nil {continue ;};if _edcg :=_dcaa .AddExtraFileFromZip (_cad );_edcg !=nil {return nil ,_edcg ;};};if _cgfa {_aded :=false ;for _ ,_dfcf :=range _dcaa .Rels .X ().Relationship {if _dfcf .TargetAttr =="\u0064\u006f\u0063\u0050ro\u0070\u0073\u002f\u0063\u0075\u0073\u0074\u006f\u006d\u002e\u0078\u006d\u006c"{_aded =true ;
break ;};};if !_aded {_dcaa .AddCustomRelationships ();};};return _dcaa ,nil ;};

// GetPlaceholderByIndex returns a placeholder given its index.  If there are multiplace
// placeholders of the same index, this method returns the first one.  You must use the
// PlaceHolders() method to access the others.
func (_dgf Slide )GetPlaceholderByIndex (idx uint32 )(PlaceHolder ,error ){for _ ,_bgce :=range _dgf ._gbabe .CSld .SpTree .GroupShapeChoice {if _bgce .Sp !=nil &&_bgce .Sp .NvSpPr !=nil &&_bgce .Sp .NvSpPr .NvPr !=nil &&_bgce .Sp .NvSpPr .NvPr .Ph !=nil {if (idx ==0&&_bgce .Sp .NvSpPr .NvPr .Ph .IdxAttr ==nil )||(_bgce .Sp .NvSpPr .NvPr .Ph .IdxAttr !=nil &&*_bgce .Sp .NvSpPr .NvPr .Ph .IdxAttr ==idx ){return PlaceHolder {_bgce .Sp ,_dgf ._gbabe },nil ;
};};};return PlaceHolder {},_db .New ("\u0075\u006e\u0061\u0062\u006c\u0065\u0020\u0074\u006f\u0020\u0066i\u006e\u0064\u0020\u0070\u006c\u0061\u0063\u0065\u0068\u006fl\u0064\u0065\u0072");};func (_afb *Presentation )saveToFile (_efcf string ,_gbabc bool )error {_fgdg ,_acga :=_fa .Create (_efcf );
if _acga !=nil {return _acga ;};defer _fgdg .Close ();return _afb .save (_fgdg ,_gbabc );};func _bdf ()*Presentation {_ade :=&Presentation {_ad :_eg .NewPresentation ()};_ade ._ad .SldIdLst =_eg .NewCT_SlideIdList ();_ade ._ad .ConformanceAttr =_c .ST_ConformanceClassTransitional ;
_ade .AppProperties =_df .NewAppProperties ();_ade .CoreProperties =_df .NewCoreProperties ();_ade ._bge =_df .NewTableStyles ();_ade .ContentTypes =_df .NewContentTypes ();_ade .Rels =_df .NewRelationships ();_ade ._aad =_df .NewRelationships ();_ade ._ebba =NewPresentationProperties ();
_ade ._aba =NewViewProperties ();_ade ._acec =map[string ]string {};return _ade ;};func (_ccdc *Presentation )save (_aegf _g .Writer ,_dcbf bool )error {const _cdbb ="\u0050\u0072\u0065\u0073en\u0074\u0061\u0074\u0069\u006f\u006e\u003a\u0070\u002e\u0053\u0061\u0076\u0065";
if _cgcc :=_ccdc ._ad .Validate ();_cgcc !=nil {_ca .Log .Debug ("\u0076\u0061\u006c\u0069\u0064\u0061\u0074\u0069\u006f\u006e\u0020\u0065\u0072\u0072\u006fr\u0020i\u006e\u0020\u0064\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u003a\u0020\u0025\u0073",_cgcc );
};if !_ac .GetLicenseKey ().IsLicensed ()&&!_gfaf {_eff .Println ("\u0055\u006e\u006ci\u0063\u0065\u006e\u0073e\u0064\u0020\u0076\u0065\u0072\u0073\u0069o\u006e\u0020\u006f\u0066\u0020\u0055\u006e\u0069\u004f\u0066\u0066\u0069\u0063\u0065");_eff .Println ("\u002d\u0020\u0047e\u0074\u0020\u0061\u0020\u0074\u0072\u0069\u0061\u006c\u0020\u006c\u0069\u0063\u0065\u006e\u0073\u0065\u0020\u006f\u006e\u0020\u0068\u0074\u0074\u0070\u0073\u003a\u002f\u002fu\u006e\u0069\u0064\u006f\u0063\u002e\u0069\u006f");
return _db .New ("\u0075\u006e\u0069\u006f\u0066\u0066\u0069\u0063\u0065\u0020\u006ci\u0063\u0065\u006e\u0073\u0065\u0020\u0072\u0065\u0071\u0075i\u0072\u0065\u0064");};_efa :="\u0075n\u006b\u006e\u006f\u0077\u006e";if _afg ,_fga :=_aegf .(*_fa .File );
_fga {_efa =_afg .Name ();};if len (_ccdc ._bbcd )==0{_fgd ,_fdbg :=_ac .GenRefId ("\u0070\u0077");if _fdbg !=nil {_ca .Log .Error ("\u0045R\u0052\u004f\u0052\u003a\u0020\u0025v",_fdbg );return _fdbg ;};_ccdc ._bbcd =_fgd ;};if _dddg :=_ac .Track (_ccdc ._bbcd ,_cdbb ,_efa );
_dddg !=nil {_ca .Log .Error ("\u0045R\u0052\u004f\u0052\u003a\u0020\u0025v",_dddg );return _dddg ;};if _dcbf {_ccdc .ContentTypes .RemoveOverride ("\u0061\u0070\u0070\u006c\u0069\u0063\u0061t\u0069\u006f\u006e\u002f\u0076\u006e\u0064\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002d\u006ff\u0066\u0069\u0063\u0065\u0064\u006f\u0063\u0075\u006de\u006e\u0074\u002e\u0070\u0072\u0065\u0073\u0065\u006e\u0074\u0061\u0074\u0069\u006f\u006e\u006d\u006c\u002e\u0070\u0072\u0065\u0073\u0065\u006e\u0074\u0061\u0074\u0069\u006f\u006e\u002e\u006d\u0061\u0069\u006e\u002b\u0078\u006d\u006c");
_ccdc .ContentTypes .EnsureOverride ("/\u0070\u0070\u0074\u002fpr\u0065s\u0065\u006e\u0074\u0061\u0074i\u006f\u006e\u002e\u0078\u006d\u006c","\u0061\u0070pl\u0069\u0063\u0061\u0074\u0069\u006f\u006e\u002f\u0076\u006e\u0064\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066o\u0072\u006d\u0061\u0074s\u002d\u006f\u0066\u0066ic\u0065\u0064o\u0063u\u006d\u0065\u006e\u0074\u002e\u0070r\u0065\u0073\u0065n\u0074\u0061t\u0069\u006f\u006e\u006d\u006c\u002e\u0074\u0065\u006d\u0070\u006c\u0061\u0074\u0065.\u006d\u0061\u0069\u006e\u002b\u0078\u006d\u006c");
}else {_ccdc .ContentTypes .RemoveOverride ("\u0061\u0070pl\u0069\u0063\u0061\u0074\u0069\u006f\u006e\u002f\u0076\u006e\u0064\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066o\u0072\u006d\u0061\u0074s\u002d\u006f\u0066\u0066ic\u0065\u0064o\u0063u\u006d\u0065\u006e\u0074\u002e\u0070r\u0065\u0073\u0065n\u0074\u0061t\u0069\u006f\u006e\u006d\u006c\u002e\u0074\u0065\u006d\u0070\u006c\u0061\u0074\u0065.\u006d\u0061\u0069\u006e\u002b\u0078\u006d\u006c");
_ccdc .ContentTypes .EnsureOverride ("/\u0070\u0070\u0074\u002fpr\u0065s\u0065\u006e\u0074\u0061\u0074i\u006f\u006e\u002e\u0078\u006d\u006c","\u0061\u0070\u0070\u006c\u0069\u0063\u0061t\u0069\u006f\u006e\u002f\u0076\u006e\u0064\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002d\u006ff\u0066\u0069\u0063\u0065\u0064\u006f\u0063\u0075\u006de\u006e\u0074\u002e\u0070\u0072\u0065\u0073\u0065\u006e\u0074\u0061\u0074\u0069\u006f\u006e\u006d\u006c\u002e\u0070\u0072\u0065\u0073\u0065\u006e\u0074\u0061\u0074\u0069\u006f\u006e\u002e\u006d\u0061\u0069\u006e\u002b\u0078\u006d\u006c");
};_gfde :=_geb .DocTypePresentation ;_fca :=_ge .NewWriter (_aegf );defer _fca .Close ();if _ebc :=_be .MarshalXML (_fca ,_geb .BaseRelsFilename ,_ccdc .Rels .X ());_ebc !=nil {return _ebc ;};if _efb :=_be .MarshalXMLByType (_fca ,_gfde ,_geb .ExtendedPropertiesType ,_ccdc .AppProperties .X ());
_efb !=nil {return _efb ;};if _cead :=_be .MarshalXMLByType (_fca ,_gfde ,_geb .CorePropertiesType ,_ccdc .CoreProperties .X ());_cead !=nil {return _cead ;};if _dae :=_be .MarshalXMLByType (_fca ,_gfde ,_geb .PresentationPropertiesType ,_ccdc ._ebba .X ());
_dae !=nil {return _dae ;};if _dea :=_be .MarshalXMLByType (_fca ,_gfde ,_geb .ViewPropertiesType ,_ccdc ._aba .X ());_dea !=nil {return _dea ;};if _fce :=_be .MarshalXMLByType (_fca ,_gfde ,_geb .TableStylesType ,_ccdc ._bge .X ());_fce !=nil {return _fce ;
};if len (_ccdc ._effd )> 0{for _befg ,_ccb :=range _ccdc ._effd {_gab :=_geb .AbsoluteFilename (_geb .DocTypePresentation ,_geb .CustomXMLType ,_ccdc ._ed [_befg ]);if _efc :=_be .MarshalXML (_fca ,_gab ,_ccb );_efc !=nil {return _efc ;};};};if _ccdc .CustomProperties .X ()!=nil {if _dbgg :=_be .MarshalXMLByType (_fca ,_gfde ,_geb .CustomPropertiesType ,_ccdc .CustomProperties .X ());
_dbgg !=nil {return _dbgg ;};};if _ccdc .Thumbnail !=nil {_adef ,_cbc :=_fca .Create ("\u0064\u006f\u0063Pr\u006f\u0070\u0073\u002f\u0074\u0068\u0075\u006d\u0062\u006e\u0061\u0069\u006c\u002e\u006a\u0070\u0065\u0067");if _cbc !=nil {return _cbc ;};if _aafb :=_bgf .Encode (_adef ,_ccdc .Thumbnail ,nil );
_aafb !=nil {return _aafb ;};};_cdeb :=_geb .AbsoluteFilename (_gfde ,_geb .OfficeDocumentType ,0);if _bgfe :=_be .MarshalXML (_fca ,_cdeb ,_ccdc ._ad );_bgfe !=nil {return _bgfe ;};if _fbff :=_be .MarshalXML (_fca ,_be .RelationsPathFor (_cdeb ),_ccdc ._aad .X ());
_fbff !=nil {return _fbff ;};for _gca ,_cab :=range _ccdc ._bac {if _cab ==nil {continue ;};_edc :=_geb .AbsoluteFilename (_geb .DocTypePresentation ,_geb .SlideType ,_ccdc ._ccd [_gca ]);_be .MarshalXML (_fca ,_edc ,_cab );if !_ccdc ._adg [_gca ].IsEmpty (){_cfed :=_be .RelationsPathFor (_edc );
_be .MarshalXML (_fca ,_cfed ,_ccdc ._adg [_gca ].X ());};};for _dgg ,_add :=range _ccdc ._gbc {if _add ==nil {continue ;};_aaa :=_geb .AbsoluteFilename (_geb .DocTypePresentation ,_geb .SlideMasterType ,_ccdc ._bbb [_dgg ]);_be .MarshalXML (_fca ,_aaa ,_add );
if !_ccdc ._dgc [_dgg ].IsEmpty (){_ggaf :=_be .RelationsPathFor (_aaa );_be .MarshalXML (_fca ,_ggaf ,_ccdc ._dgc [_dgg ].X ());};};for _abb ,_gge :=range _ccdc ._bdbg {if _gge ==nil {continue ;};_geda :=_geb .AbsoluteFilename (_geb .DocTypePresentation ,_geb .SlideLayoutType ,_abb +1);
_be .MarshalXML (_fca ,_geda ,_gge );if !_ccdc ._bebc [_abb ].IsEmpty (){_fbfe :=_be .RelationsPathFor (_geda );_be .MarshalXML (_fca ,_fbfe ,_ccdc ._bebc [_abb ].X ());};};for _fcg ,_begf :=range _ccdc ._bed {if _begf ==nil {continue ;};_dab :=_geb .AbsoluteFilename (_geb .DocTypePresentation ,_geb .ThemeType ,_ccdc ._gbb [_fcg ]);
_be .MarshalXML (_fca ,_dab ,_begf );if !_ccdc ._fea [_fcg ].IsEmpty (){_agcf :=_be .RelationsPathFor (_dab );_be .MarshalXML (_fca ,_agcf ,_ccdc ._fea [_fcg ].X ());};};for _cegc ,_gcbf :=range _ccdc ._eeb {_dec :=_geb .AbsoluteFilename (_gfde ,_geb .ChartType ,_cegc +1);
_be .MarshalXML (_fca ,_dec ,_gcbf );};for _bag ,_deaf :=range _ccdc ._cbee {_eca :=_geb .AbsoluteFilename (_gfde ,_geb .HandoutMasterType ,_bag +1);_be .MarshalXML (_fca ,_eca ,_deaf );};for _bedc ,_ddbc :=range _ccdc ._bab {if _ddbc ==nil {continue ;
};_gce :=_geb .AbsoluteFilename (_gfde ,_geb .NotesMasterType ,_ccdc ._ggf [_bedc ]);_be .MarshalXML (_fca ,_gce ,_ddbc );};for _bff ,_decg :=range _ccdc .Images {_dfc :=_bff +1;if _fcd ,_affb :=_ef .StringToNumbers (_decg .Target ());_affb &&_dfc !=_fcd {_dfc =_fcd ;
};if _fedb :=_df .AddImageToZip (_fca ,_decg ,_dfc ,_geb .DocTypePresentation );_fedb !=nil {return _fedb ;};};_ccdc .ContentTypes .EnsureDefault ("\u0070\u006e\u0067","\u0069m\u0061\u0067\u0065\u002f\u0070\u006eg");_ccdc .ContentTypes .EnsureDefault ("\u006a\u0070\u0065\u0067","\u0069\u006d\u0061\u0067\u0065\u002f\u006a\u0070\u0065\u0067");
_ccdc .ContentTypes .EnsureDefault ("\u006a\u0070\u0067","\u0069\u006d\u0061\u0067\u0065\u002f\u006a\u0070\u0065\u0067");_ccdc .ContentTypes .EnsureDefault ("\u0077\u006d\u0066","i\u006d\u0061\u0067\u0065\u002f\u0078\u002d\u0077\u006d\u0066");if _cbgf :=_be .MarshalXML (_fca ,_geb .ContentTypesFilename ,_ccdc .ContentTypes .X ());
_cbgf !=nil {return _cbgf ;};if _cgb :=_ccdc .WriteExtraFiles (_fca );_cgb !=nil {return _cgb ;};return nil ;};

// X returns the inner wrapped XML type.
func (_aeae SlideMaster )X ()*_eg .SldMaster {return _aeae ._aaff };

// SetOffsetX sets horizontal offset of text box in distance units (see measurement package).
func (_cdcc TextBox )SetOffsetX (offX float64 ){_bgcc :=_cdcc .getOff ();_gbegf :=_a .ToEMU (offX );_bgcc .XAttr =_bd .ST_Coordinate {ST_CoordinateUnqualified :&_gbegf };};

// GetSlideLayout returns a slide layout related to the slide.
func (_cbgg *Slide )GetSlideLayout ()*_eg .SldLayout {_bbcf :=_cbgg .getSlideRels ();for _ ,_fege :=range _bbcf .Relationships (){if _fege .Type ()==_geb .SlideLayoutType {if _eege ,_edg :=_ef .StringToNumbers (_fege .Target ());_edg {return _cbgg ._acbf ._bdbg [_eege -1];
};return nil ;};};return nil ;};

// Type returns the placeholder type
func (_dfa PlaceHolder )Type ()_eg .ST_PlaceholderType {return _dfa ._bdb .NvSpPr .NvPr .Ph .TypeAttr };

// MoveSlide moves a slide with given number to position targetPos within a presentation.
func (_eddg *Presentation )MoveSlide (slideNumber ,targetPos int )error {if slideNumber < 0||slideNumber >=len (_eddg ._bac ){return _db .New ("\u0073\u006c\u0069\u0064eN\u0075\u006d\u0062\u0065\u0072\u0020\u0069\u0073\u0020\u0069\u006e\u0076\u0061\u006ci\u0064");
};if targetPos < 0||targetPos >=len (_eddg ._bac ){return _db .New ("t\u0061r\u0067\u0065\u0074\u0050\u006f\u0073\u0020\u0069s\u0020\u0069\u006e\u0076al\u0069\u0064");};if slideNumber ==targetPos {return nil ;};_gcdd :=_eddg ._bac [slideNumber ];_fbc :=_eddg ._adg [slideNumber ];
if slideNumber < targetPos {copy (_eddg ._bac [slideNumber :targetPos ],_eddg ._bac [slideNumber +1:targetPos +1]);copy (_eddg ._adg [slideNumber :targetPos ],_eddg ._adg [slideNumber +1:targetPos +1]);copy (_eddg ._ccd [slideNumber :targetPos ],_eddg ._ccd [slideNumber +1:targetPos +1]);
}else {copy (_eddg ._bac [targetPos +1:slideNumber +1],_eddg ._bac [targetPos :slideNumber ]);copy (_eddg ._adg [targetPos +1:slideNumber +1],_eddg ._adg [targetPos :slideNumber ]);copy (_eddg ._ccd [targetPos +1:slideNumber +1],_eddg ._ccd [targetPos :slideNumber ]);
};_eddg ._bac [targetPos ]=_gcdd ;_eddg ._adg [targetPos ]=_fbc ;return nil ;};

// AddImage adds an image to the document package, returning a reference that
// can be used to add the image to a run and place it in the document contents.
func (_dfac *Presentation )AddImage (i _df .Image )(_df .ImageRef ,error ){_fef :=_df .MakeImageRef (i ,&_dfac .DocBase ,_dfac ._aad );if i .Data ==nil &&i .Path ==""{return _fef ,_db .New ("\u0069\u006d\u0061\u0067\u0065\u0020\u006d\u0075\u0073\u0074 \u0068\u0061\u0076\u0065\u0020\u0064\u0061t\u0061\u0020\u006f\u0072\u0020\u0061\u0020\u0070\u0061\u0074\u0068");
};if i .Format ==""{return _fef ,_db .New ("\u0069\u006d\u0061\u0067\u0065\u0020\u006d\u0075\u0073\u0074 \u0068\u0061\u0076\u0065\u0020\u0061\u0020v\u0061\u006c\u0069\u0064\u0020\u0066\u006f\u0072\u006d\u0061\u0074");};if i .Size .X ==0||i .Size .Y ==0{return _fef ,_db .New ("\u0069\u006d\u0061\u0067e\u0020\u006d\u0075\u0073\u0074\u0020\u0068\u0061\u0076\u0065 \u0061 \u0076\u0061\u006c\u0069\u0064\u0020\u0073i\u007a\u0065");
};if i .Path !=""{_ebaed :=_ace .Add (i .Path );if _ebaed !=nil {return _fef ,_ebaed ;};};_dfac .Images =append (_dfac .Images ,_fef );_dfac .ContentTypes .EnsureDefault ("\u0070\u006e\u0067","\u0069m\u0061\u0067\u0065\u002f\u0070\u006eg");_dfac .ContentTypes .EnsureDefault ("\u006a\u0070\u0065\u0067","\u0069\u006d\u0061\u0067\u0065\u002f\u006a\u0070\u0065\u0067");
_dfac .ContentTypes .EnsureDefault ("\u006a\u0070\u0067","\u0069\u006d\u0061\u0067\u0065\u002f\u006a\u0070\u0065\u0067");_dfac .ContentTypes .EnsureDefault ("\u0077\u006d\u0066","i\u006d\u0061\u0067\u0065\u002f\u0078\u002d\u0077\u006d\u0066");_dfac .ContentTypes .EnsureDefault (i .Format ,"\u0069\u006d\u0061\u0067\u0065\u002f"+i .Format );
return _fef ,nil ;};

// GetLayoutImageByRelID returns an ImageRef with the associated relation ID in the
// slide layout.
func (_bcfd *Slide )GetLayoutImageByRelID (relID string )(_df .ImageRef ,bool ){_efcd :=_bcfd .getSlideLayoutRels ();if (_efcd ==_df .Relationships {}){return _df .ImageRef {},false ;};_gbgc :=_efcd .GetTargetByRelId (relID );for _ ,_fgcf :=range _bcfd ._acbf .Images {if _fgcf .Target ()==_gbgc {return _fgcf ,true ;
};};return _df .ImageRef {},false ;};

// AddImage adds an image textbox to a slide.
func (_feg Slide )AddImage (img _df .ImageRef )Image {_dfbg :=_eg .NewCT_GroupShapeChoice ();_feg ._gbabe .CSld .SpTree .GroupShapeChoice =append (_feg ._gbabe .CSld .SpTree .GroupShapeChoice ,_dfbg );_bcf :=_eg .NewCT_Picture ();_dfbg .Pic =_bcf ;_bcf .NvPicPr .CNvPicPr =_bd .NewCT_NonVisualPictureProperties ();
_bcf .NvPicPr .CNvPicPr .PicLocks =_bd .NewCT_PictureLocking ();_bcf .NvPicPr .CNvPicPr .PicLocks .NoChangeAspectAttr =_geb .Bool (true );_bcf .BlipFill =_bd .NewCT_BlipFillProperties ();_bcf .BlipFill .Blip =_bd .NewCT_Blip ();_fdag :=_feg .AddImageToRels (img );
_bcf .BlipFill .Blip .EmbedAttr =_geb .String (_fdag );_bcf .BlipFill .FillModePropertiesChoice .Stretch =_bd .NewCT_StretchInfoProperties ();_bcf .BlipFill .FillModePropertiesChoice .Stretch .FillRect =_bd .NewCT_RelativeRect ();_bcf .SpPr =_bd .NewCT_ShapeProperties ();
_bcf .SpPr .GeometryChoice .PrstGeom =_bd .NewCT_PresetGeometry2D ();_bcf .SpPr .GeometryChoice .PrstGeom .PrstAttr =_bd .ST_ShapeTypeRect ;_adab :=Image {_bcf };_aaba :=img .Size ();_adab .Properties ().SetWidth (_a .Distance (_aaba .X )*_a .Pixel72 );
_adab .Properties ().SetHeight (_a .Distance (_aaba .Y )*_a .Pixel72 );_adab .Properties ().SetPosition (0,0);return _adab ;};

// PlaceHolders returns all of the content place holders within a given slide.
func (_gede Slide )PlaceHolders ()[]PlaceHolder {_dge :=[]PlaceHolder {};for _ ,_acdc :=range _gede ._gbabe .CSld .SpTree .GroupShapeChoice {if _acdc .Sp !=nil &&_acdc .Sp .NvSpPr !=nil &&_acdc .Sp .NvSpPr .NvPr !=nil &&_acdc .Sp .NvSpPr .NvPr .Ph !=nil {_dge =append (_dge ,PlaceHolder {_acdc .Sp ,_gede ._gbabe });
};};return _dge ;};var (SlideScreenSize16x9 =SlideScreenSize {12192000,6858000};SlideScreenSize4x3 =SlideScreenSize {9144000,6858000};SlideScreenSizeA4 =SlideScreenSize {9906000,6858000};);

// AddSlideWithLayout adds a new slide with content copied from a layout.  Normally you should
// use AddDefaultSlideWithLayout as it will do some post processing similar to PowerPoint to
// clear place holder text, etc.
func (_eef *Presentation )AddSlideWithLayout (l SlideLayout )(Slide ,error ){_fbb :=_eg .NewCT_SlideIdListEntry ();_fbb .IdAttr =_eef .nextSlideID ();_eef ._ad .SldIdLst .SldId =append (_eef ._ad .SldIdLst .SldId ,_fbb );_afca :=_eg .NewSld ();_fbd :=_dga .Buffer {};
_cbec :=_acb .NewEncoder (&_fbd );_cgd :=_acb .StartElement {Name :_acb .Name {Local :"\u0073\u006c\u0069d\u0065"}};_cgd .Attr =append (_cgd .Attr ,_acb .Attr {Name :_acb .Name {Local :"\u0078\u006d\u006cn\u0073"},Value :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u0070\u0072\u0065\u0073\u0065\u006e\u0074\u0061\u0074\u0069o\u006e\u006d\u006c\u002f\u0032\u00300\u0036\u002f\u006da\u0069\u006e"});
_cgd .Attr =append (_cgd .Attr ,_acb .Attr {Name :_acb .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0061"},Value :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065m\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006cf\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077\u0069\u006e\u0067m\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u006d\u0061\u0069\u006e"});
_cgd .Attr =append (_cgd .Attr ,_acb .Attr {Name :_acb .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0070"},Value :"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002f\u0070\u0072\u0065\u0073\u0065\u006e\u0074\u0061\u0074\u0069o\u006e\u006d\u006c\u002f\u0032\u00300\u0036\u002f\u006da\u0069\u006e"});
_cgd .Attr =append (_cgd .Attr ,_acb .Attr {Name :_acb .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0072"},Value :"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069c\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002fr\u0065\u006c\u0061\u0074\u0069\u006f\u006e\u0073h\u0069\u0070\u0073"});
_cgd .Attr =append (_cgd .Attr ,_acb .Attr {Name :_acb .Name {Local :"\u0078\u006d\u006c\u006e\u0073\u003a\u0073\u0068"},Value :"\u0068\u0074\u0074\u0070\u003a/\u002f\u0073\u0063\u0068\u0065m\u0061s\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067/\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0073\u0068\u0061\u0072e\u0064\u0054\u0079\u0070\u0065\u0073"});
_cgd .Attr =append (_cgd .Attr ,_acb .Attr {Name :_acb .Name {Local :"\u0078m\u006c\u006e\u0073\u003a\u0078\u006dl"},Value :"\u0068\u0074tp\u003a\u002f\u002fw\u0077\u0077\u002e\u00773.o\u0072g/\u0058\u004d\u004c\u002f\u0031\u0039\u00398/\u006e\u0061\u006d\u0065\u0073\u0070\u0061c\u0065"});
if _acfa :=l ._gced .CSld .MarshalXML (_cbec ,_cgd );_acfa !=nil {return Slide {},_acfa ;};_cbec .Flush ();_eaf :=_acb .NewDecoder (&_fbd );_afca .CSld =_eg .NewCT_CommonSlideData ();if _cfe :=_eaf .Decode (_afca .CSld );_cfe !=nil {return Slide {},_cfe ;
};_afca .CSld .NameAttr =nil ;_afca .CSld .SpTree .GroupShapeChoice =_dgag (_afca .CSld .SpTree .GroupShapeChoice );_eef ._bac =append (_eef ._bac ,_afca );_eef ._ccd =append (_eef ._ccd ,len (_eef ._bac ));_bfd :=_eef ._aad .AddAutoRelationship (_geb .DocTypePresentation ,_geb .OfficeDocumentType ,len (_eef ._bac ),_geb .SlideType );
_fbb .RIdAttr =_bfd .ID ();_ffg :=_geb .AbsoluteFilename (_geb .DocTypePresentation ,_geb .SlideType ,len (_eef ._bac ));_eef .ContentTypes .AddOverride (_ffg ,_geb .SlideContentType );_acg :=_df .NewRelationships ();_eef ._adg =append (_eef ._adg ,_acg );
_caf :=len (_eef ._adg )-1;for _geag ,_dfaf :=range _eef ._bdbg {if _dfaf ==l .X (){_eba :=_eef ._bebc [_geag ];for _ ,_ddc :=range _eba .X ().Relationship {if _ddc .TypeAttr !=_geb .SlideMasterType {_eef ._adg [_caf ].X ().Relationship =append (_eef ._adg [_caf ].X ().Relationship ,_ddc );
};};_acg .AddAutoRelationship (_geb .DocTypePresentation ,_geb .SlideType ,_geag +1,_geb .SlideLayoutType );};};_gaad :=Slide {_fbb ,_afca ,_eef ,nil };return _gaad ,nil ;};

// NotesViewPr returns the NotesViewPr property.
func (_fbe ViewProperties )NotesViewPr ()*_eg .CT_NotesViewProperties {return _fbe ._ede .NotesViewPr };

// Image is an image within a slide.
type Image struct{_eea *_eg .CT_Picture };

// ExtLst returns the ExtLst property.
func (_cedcc ViewProperties )ExtLst ()*_eg .CT_ExtensionList {return _cedcc ._ede .ExtLst };

// Close closes the presentation, removing any temporary files that might have been
// created when opening a document.
func (_efbef *Presentation )Close ()error {if _efbef .TmpPath !=""{return _ace .RemoveAll (_efbef .TmpPath );};return nil ;};

// SlideMasters returns the slide masters defined in the presentation.
func (_ecee *Presentation )SlideMasters ()[]SlideMaster {_cee :=[]SlideMaster {};for _bffb ,_bca :=range _ecee ._gbc {if _bffb < len (_ecee ._dgc ){_cee =append (_cee ,SlideMaster {_ecee ,_ecee ._dgc [_bffb ],_bca });};};return _cee ;};

// Less is for implementing sorting of two locations. Symbols share the same location if they are in the same paragraph or table. One location is 'less' than another first by y coordinate, if y coordinates are equal or differ by less than yEpsilon, then x coordinates are compared, then if they are also equal, indexes of locations in the table are compared, then positions of locations in a paragraph.
func (_gaa sort2d )Less (i ,j int )bool {_bdd ,_abg :=_gaa [i ],_gaa [j ];_dda ,_abe :=_bdd ._af ,_abg ._af ;_ecd ,_cde :=len (_dda )-1,len (_abe )-1;_ffda ,_ffab :=0,0;for {_dbf ,_ee ,_dc ,_cbd ,_bbd ,_gad ,_egc ,_deb :=_dda [_ffda ]._gfg ,_abe [_ffab ]._gfg ,_dda [_ffda ]._aa ,_abe [_ffab ]._aa ,_dda [_ffda ]._acc ,_abe [_ffab ]._acc ,_dda [_ffda ]._ffb ,_abe [_ffab ]._ffb ;
if _dbf ==_ee ||((_fd .Abs (float64 (_dbf )-float64 (_ee ))< _gfc )&&((_dbf >=_ee &&_dbf <=_cbd )||(_ee >=_dbf &&_ee <=_dc ))&&(_egc < _gad ||_bbd > _deb )){if _bbd ==_gad {if _ffda < _ecd &&_ffab < _cde {_ffda ++;_ffab ++;continue ;};if _ffda >=_ecd &&_ffab >=_cde {break ;
};return _ffda >=_ecd ;}else {return _bbd < _gad ;};}else {return _dbf < _ee ;};};_aeb ,_gaag ,_cfg ,_eab :=_bdd ._cbe ,_abg ._cbe ,_bdd ._ae ,_abg ._ae ;if _aeb ==_gaag {return _cfg <=_eab ;};return _aeb < _gaag ;};

// SaveToFile writes the Presentation out to a file.
func (_cege *Presentation )SaveToFile (path string )error {return _cege .saveToFile (path ,false )};

// GridSpacing returns the GridSpacing property.
func (_aef ViewProperties )GridSpacing ()*_bd .CT_PositiveSize2D {return _aef ._ede .GridSpacing };func (_bfg sort2d )Swap (i ,j int ){_bfg [i ],_bfg [j ]=_bfg [j ],_bfg [i ]};

// GetImageByRelID returns an ImageRef with the associated relation ID in the
// document.
func (_fcbg *Presentation )GetImageByRelID (relID string )(_df .ImageRef ,bool ){for _ ,_gedd :=range _fcbg .Images {if _gedd .RelID ()==relID {return _gedd ,true ;};};return _df .ImageRef {},false ;};

// NewViewProperties constructs a new ViewProperties.
func NewViewProperties ()ViewProperties {return ViewProperties {_ede :_eg .NewViewPr ()}};

// GetColorBySchemeColor returns *dml.CT_Color mapped to scheme colors like dk1, lt1 etc. depending on what theme is used in the presentation.
func (_gbeg *Presentation )GetColorBySchemeColor (schClr _bd .ST_SchemeColorVal )*_bd .CT_Color {if len (_gbeg ._gbc )==0||len (_gbeg ._bed )==0{return nil ;};var _cedc _bd .ST_ColorSchemeIndex ;_feca :=_gbeg ._gbc [0];_ddg :=_feca .ClrMap ;switch schClr .String (){case "\u0062\u0067\u0031":_cedc =_ddg .Bg1Attr ;
case "\u0062\u0067\u0032":_cedc =_ddg .Bg2Attr ;case "\u0074\u0078\u0031":_cedc =_ddg .Tx1Attr ;case "\u0074\u0078\u0032":_cedc =_ddg .Tx2Attr ;case "\u0061c\u0063\u0065\u006e\u0074\u0031":_cedc =_ddg .Accent1Attr ;case "\u0061c\u0063\u0065\u006e\u0074\u0032":_cedc =_ddg .Accent2Attr ;
case "\u0061c\u0063\u0065\u006e\u0074\u0033":_cedc =_ddg .Accent3Attr ;case "\u0061c\u0063\u0065\u006e\u0074\u0034":_cedc =_ddg .Accent4Attr ;case "\u0061c\u0063\u0065\u006e\u0074\u0035":_cedc =_ddg .Accent5Attr ;case "\u0061c\u0063\u0065\u006e\u0074\u0036":_cedc =_ddg .Accent6Attr ;
case "\u0068\u006c\u0069n\u006b":_cedc =_ddg .HlinkAttr ;case "\u0066\u006f\u006c\u0048\u006c\u0069\u006e\u006b":_cedc =_ddg .FolHlinkAttr ;case "\u0064\u006b\u0031":_cedc =_bd .ST_ColorSchemeIndexDk1 ;case "\u0064\u006b\u0032":_cedc =_bd .ST_ColorSchemeIndexDk2 ;
case "\u006c\u0074\u0031":_cedc =_bd .ST_ColorSchemeIndexLt1 ;case "\u006c\u0074\u0032":_cedc =_bd .ST_ColorSchemeIndexLt2 ;default:_cedc =_bd .ST_ColorSchemeIndexUnset ;};_ebaf :=_gbeg ._bed [0];_abgd :=_ebaf .ThemeElements ;if _abgd ==nil {return nil ;
};var _acea *_bd .CT_Color ;_dag :=_abgd .ClrScheme ;switch _cedc .String (){case "\u0064\u006b\u0031":_acea =_dag .Dk1 ;case "\u0064\u006b\u0032":_acea =_dag .Dk2 ;case "\u006c\u0074\u0031":_acea =_dag .Lt1 ;case "\u006c\u0074\u0032":_acea =_dag .Lt2 ;
case "\u0061c\u0063\u0065\u006e\u0074\u0031":_acea =_dag .Accent1 ;case "\u0061c\u0063\u0065\u006e\u0074\u0032":_acea =_dag .Accent2 ;case "\u0061c\u0063\u0065\u006e\u0074\u0033":_acea =_dag .Accent3 ;case "\u0061c\u0063\u0065\u006e\u0074\u0034":_acea =_dag .Accent4 ;
case "\u0061c\u0063\u0065\u006e\u0074\u0035":_acea =_dag .Accent5 ;case "\u0061c\u0063\u0065\u006e\u0074\u0036":_acea =_dag .Accent6 ;case "\u0068\u006c\u0069n\u006b":_acea =_dag .Hlink ;case "\u0066\u006f\u006c\u0048\u006c\u0069\u006e\u006b":_acea =_dag .FolHlink ;
default:return nil ;};return _acea ;};

// SetSize sets the slide size, take argument of SlideScreenSize.
func (_dee *SlideSize )SetSize (sz SlideScreenSize ){_dee ._bfe .CxAttr =sz [0];_dee ._bfe .CyAttr =sz [1];};

// NewSlideScreenSize returns slide screen size with default MS PowerPoint slide screen size 16x9.
func NewSlideScreenSize ()SlideScreenSize {return NewSlideScreenSizeWithValue (SlideScreenSize16x9 [0],SlideScreenSize16x9 [1]);};

// GetTextBoxes returns a list of all text boxes from a slide.
func (_bdag Slide )GetTextBoxes ()[]*TextBox {_fbbg :=[]*TextBox {};_cdc :=_bdag ._gbabe .CSld .SpTree .GroupShapeChoice ;for _ ,_abc :=range _cdc {if _abc .Sp !=nil &&_abc .Sp .NvSpPr .CNvSpPr .TxBoxAttr !=nil &&*_abc .Sp .NvSpPr .CNvSpPr .TxBoxAttr {_fbbg =append (_fbbg ,&TextBox {_abc .Sp });
};};return _fbbg ;};

// AddTable adds an empty table to a slide.
func (_gbad Slide )AddTable ()*_df .Table {_gag :=_eg .NewCT_GroupShapeChoice ();_gbad ._gbabe .CSld .SpTree .GroupShapeChoice =append (_gbad ._gbabe .CSld .SpTree .GroupShapeChoice ,_gag );_def :=_eg .NewCT_GraphicalObjectFrame ();_gag .GraphicFrame =_def ;
_def .Xfrm .Off =_bd .NewCT_Point2D ();_dgfe :=int64 (1);_def .Xfrm .Off .XAttr =_bd .ST_Coordinate {ST_CoordinateUnqualified :&_dgfe };_def .Xfrm .Off .YAttr =_bd .ST_Coordinate {ST_CoordinateUnqualified :&_dgfe };_cgfac :=_def .Graphic .CT_GraphicalObject .GraphicData ;
_cgfac .UriAttr ="\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002eo\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073.\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077\u0069\u006e\u0067\u006dl/\u0032\u0030\u0030\u0036\u002f\u0074\u0061\u0062\u006c\u0065";
_effa :=_df .NewTableWithXfrm (_def .Xfrm );_cgfac .Any =append (_cgfac .Any ,_effa .X ());return _effa ;};

// LastViewAttr returns the LastViewAttr property.
func (_fee ViewProperties )LastViewAttr ()_eg .ST_ViewType {return _fee ._ede .LastViewAttr };

// SlideLayout is a layout from which slides can be created.
type SlideLayout struct{_gced *_eg .SldLayout };const _gfc float64 =500000;

// Open opens and reads a document from a file (.pptx).
func Open (filename string )(*Presentation ,error ){_bgcf ,_acd :=_fa .Open (filename );if _acd !=nil {return nil ,_eff .Errorf ("e\u0072r\u006f\u0072\u0020\u006f\u0070\u0065\u006e\u0069n\u0067\u0020\u0025\u0073: \u0025\u0073",filename ,_acd );};defer _bgcf .Close ();
_cdb ,_acd :=_fa .Stat (filename );if _acd !=nil {return nil ,_eff .Errorf ("e\u0072r\u006f\u0072\u0020\u006f\u0070\u0065\u006e\u0069n\u0067\u0020\u0025\u0073: \u0025\u0073",filename ,_acd );};_ =_cdb ;return Read (_bgcf ,_cdb .Size ());};

// PresentationText is an array of extracted text items which has some methods for representing extracted text.
type PresentationText struct{Slides []*SlideText ;};

// NormalViewPr returns the NormalViewPr property.
func (_ebad ViewProperties )NormalViewPr ()*_eg .CT_NormalViewProperties {return _ebad ._ede .NormalViewPr ;};

// Index returns the placeholder index
func (_gcf PlaceHolder )Index ()uint32 {if _gcf ._bdb .NvSpPr .NvPr .Ph .IdxAttr ==nil {return 0;};return *_gcf ._bdb .NvSpPr .NvPr .Ph .IdxAttr ;};

// GetLayoutByName retrieves a slide layout given a layout name.
func (_eeeg *Presentation )GetLayoutByName (name string )(SlideLayout ,error ){for _ ,_cge :=range _eeeg ._bdbg {if _cge .CSld .NameAttr !=nil &&name ==*_cge .CSld .NameAttr {return SlideLayout {_cge },nil ;};};return SlideLayout {},_db .New ("\u0075\u006eab\u006c\u0065\u0020t\u006f\u0020\u0066\u0069nd \u006cay\u006f\u0075\u0074\u0020\u0077\u0069\u0074h \u0074\u0068\u0061\u0074\u0020\u006e\u0061m\u0065");
};

// PrnPr returns the PrnPr property.
func (_gbe PresentationProperties )PrnPr ()*_eg .CT_PrintProperties {return _gbe ._fag .PrnPr };

// Name returns the name of the slide layout.
func (_abbf SlideLayout )Name ()string {if _abbf ._gced .CSld !=nil &&_abbf ._gced .CSld .NameAttr !=nil {return *_abbf ._gced .CSld .NameAttr ;};return "";};

// SorterViewPr returns the SorterViewPr property.
func (_dabd ViewProperties )SorterViewPr ()*_eg .CT_SlideSorterViewProperties {return _dabd ._ede .SorterViewPr ;};

// Slides returns the slides in the presentation.
func (_gda *Presentation )Slides ()[]Slide {_ddcd :=[]Slide {};for _bage ,_cgg :=range _gda ._bac {_ddcd =append (_ddcd ,Slide {_gda ._ad .SldIdLst .SldId [_bage ],_cgg ,_gda ,nil });};return _ddcd ;};

// SlideSize returns presentation slide size.
func (_efdd *Presentation )SlideSize ()SlideSize {if _efdd ._ad .SldSz ==nil {_efdd ._ad .SldSz =_eg .NewCT_SlideSize ();};return SlideSize {_efdd ._ad .SldSz ,_efdd };};

// GetColorBySchemeColor returns *dml.CT_Color mapped to scheme colors like dk1, lt1 etc. depending on what theme is used in the presentation.
func (_cefb *Slide )GetColorBySchemeColor (schClr _bd .ST_SchemeColorVal )*_bd .CT_Color {_cefb .ensureClrMap ();_ecf :=_cefb ._fagb ;if _ecf ==nil {return nil ;};var _bdac _bd .ST_ColorSchemeIndex ;switch schClr .String (){case "\u0062\u0067\u0031":_bdac =_ecf .Bg1Attr ;
case "\u0062\u0067\u0032":_bdac =_ecf .Bg2Attr ;case "\u0074\u0078\u0031":_bdac =_ecf .Tx1Attr ;case "\u0074\u0078\u0032":_bdac =_ecf .Tx2Attr ;case "\u0061c\u0063\u0065\u006e\u0074\u0031":_bdac =_ecf .Accent1Attr ;case "\u0061c\u0063\u0065\u006e\u0074\u0032":_bdac =_ecf .Accent2Attr ;
case "\u0061c\u0063\u0065\u006e\u0074\u0033":_bdac =_ecf .Accent3Attr ;case "\u0061c\u0063\u0065\u006e\u0074\u0034":_bdac =_ecf .Accent4Attr ;case "\u0061c\u0063\u0065\u006e\u0074\u0035":_bdac =_ecf .Accent5Attr ;case "\u0061c\u0063\u0065\u006e\u0074\u0036":_bdac =_ecf .Accent6Attr ;
case "\u0068\u006c\u0069n\u006b":_bdac =_ecf .HlinkAttr ;case "\u0066\u006f\u006c\u0048\u006c\u0069\u006e\u006b":_bdac =_ecf .FolHlinkAttr ;case "\u0064\u006b\u0031":_bdac =_bd .ST_ColorSchemeIndexDk1 ;case "\u0064\u006b\u0032":_bdac =_bd .ST_ColorSchemeIndexDk2 ;
case "\u006c\u0074\u0031":_bdac =_bd .ST_ColorSchemeIndexLt1 ;case "\u006c\u0074\u0032":_bdac =_bd .ST_ColorSchemeIndexLt2 ;default:_bdac =_bd .ST_ColorSchemeIndexUnset ;};_gcbd :=_cefb ._acbf ._bed [0];_ccg :=_gcbd .ThemeElements ;if _ccg ==nil {return nil ;
};var _gcg *_bd .CT_Color ;_bbfd :=_ccg .ClrScheme ;switch _bdac .String (){case "\u0064\u006b\u0031":_gcg =_bbfd .Dk1 ;case "\u0064\u006b\u0032":_gcg =_bbfd .Dk2 ;case "\u006c\u0074\u0031":_gcg =_bbfd .Lt1 ;case "\u006c\u0074\u0032":_gcg =_bbfd .Lt2 ;
case "\u0061c\u0063\u0065\u006e\u0074\u0031":_gcg =_bbfd .Accent1 ;case "\u0061c\u0063\u0065\u006e\u0074\u0032":_gcg =_bbfd .Accent2 ;case "\u0061c\u0063\u0065\u006e\u0074\u0033":_gcg =_bbfd .Accent3 ;case "\u0061c\u0063\u0065\u006e\u0074\u0034":_gcg =_bbfd .Accent4 ;
case "\u0061c\u0063\u0065\u006e\u0074\u0035":_gcg =_bbfd .Accent5 ;case "\u0061c\u0063\u0065\u006e\u0074\u0036":_gcg =_bbfd .Accent6 ;case "\u0068\u006c\u0069n\u006b":_gcg =_bbfd .Hlink ;case "\u0066\u006f\u006c\u0048\u006c\u0069\u006e\u006b":_gcg =_bbfd .FolHlink ;
default:return nil ;};return _gcg ;};

// NotesTextViewPr returns the NotesTextViewPr property.
func (_bfda ViewProperties )NotesTextViewPr ()*_eg .CT_NotesTextViewProperties {return _bfda ._ede .NotesTextViewPr ;};func (_fdc *Slide )getSlideLayoutRels ()_df .Relationships {_aafd :=_fdc ._acbf ;for _egg ,_cbac :=range _aafd .SlideLayouts (){if *_fdc .GetSlideLayout ().CSld ==*_cbac ._gced .CSld {return _aafd ._bebc [_egg ];
};};return _df .Relationships {};};

// X returns the inner wrapped XML type.
func (_bae PresentationProperties )X ()*_eg .PresentationPr {return _bae ._fag };

// Width returns slide screen size width in EMU units.
func (_ggc *SlideScreenSize )Width ()int32 {return _ggc [0]};

// X returns the inner wrapped XML type.
func (_cfgb *Presentation )X ()*_eg .Presentation {return _cfgb ._ad };

// SlideLayouts returns a slice of all layouts in SlideMaster.
func (_bfca SlideMaster )SlideLayouts ()[]SlideLayout {_geef :=map[string ]int {};_gegg :=[]SlideLayout {};for _ ,_dgb :=range _bfca ._dffc .Relationships (){_aeab :=_d .Replace (_dgb .Target (),".\u002e\u002f\u0073\u006c\u0069\u0064e\u004c\u0061\u0079\u006f\u0075\u0074\u0073\u002f\u0073l\u0069\u0064\u0065L\u0061y\u006f\u0075\u0074","",-1);
_aeab =_d .Replace (_aeab ,"\u002e\u0078\u006d\u006c","",-1);if _ceae ,_bacab :=_bg .ParseInt (_aeab ,10,32);_bacab ==nil {_geef [_dgb .ID ()]=int (_ceae );};};for _ ,_eebg :=range _bfca ._aaff .SldLayoutIdLst .SldLayoutId {if _cbacf ,_cfab :=_geef [_eebg .RIdAttr ];
_cfab {_eaa :=_bfca ._fbbc ._bdbg [_cbacf -1];_gegg =append (_gegg ,SlideLayout {_eaa });};};return _gegg ;};

// SaveToFileAsTemplate writes the Presentation out to a file as a template.
func (_bgeg *Presentation )SaveToFileAsTemplate (path string )error {return _bgeg .saveToFile (path ,true );};

// X returns the inner wrapped XML type.
func (_dbce ViewProperties )X ()*_eg .ViewPr {return _dbce ._ede };

// ViewProperties contains presentation specific properties.
type ViewProperties struct{_ede *_eg .ViewPr };

// New initializes and returns a new presentation
func New ()*Presentation {_cgf :=_bdf ();_cgf .ContentTypes .AddOverride ("/\u0070\u0070\u0074\u002fpr\u0065s\u0065\u006e\u0074\u0061\u0074i\u006f\u006e\u002e\u0078\u006d\u006c","\u0061\u0070\u0070\u006c\u0069\u0063\u0061t\u0069\u006f\u006e\u002f\u0076\u006e\u0064\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002d\u006ff\u0066\u0069\u0063\u0065\u0064\u006f\u0063\u0075\u006de\u006e\u0074\u002e\u0070\u0072\u0065\u0073\u0065\u006e\u0074\u0061\u0074\u0069\u006f\u006e\u006d\u006c\u002e\u0070\u0072\u0065\u0073\u0065\u006e\u0074\u0061\u0074\u0069\u006f\u006e\u002e\u006d\u0061\u0069\u006e\u002b\u0078\u006d\u006c");
_cgf .ContentTypes .AddOverride ("\u0070\u0070\u0074\u002f\u0070\u0072\u0065\u0073\u0050\u0072\u006f\u0070s\u002e\u0078\u006d\u006c","\u0061\u0070\u0070\u006c\u0069c\u0061\u0074\u0069\u006f\u006e\u002fv\u006e\u0064\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066o\u0072\u006d\u0061\u0074s\u002d\u006f\u0066\u0066\u0069\u0063e\u0064\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002e\u0070\u0072\u0065\u0073\u0065\u006e\u0074\u0061t\u0069\u006f\u006e\u006d\u006c\u002e\u0070\u0072\u0065\u0073\u0050\u0072\u006f\u0070\u0073\u002b\u0078\u006d\u006c");
_cgf .ContentTypes .AddOverride ("\u0070\u0070\u0074\u002f\u0076\u0069\u0065\u0077\u0050\u0072\u006f\u0070s\u002e\u0078\u006d\u006c","\u0061\u0070\u0070\u006c\u0069c\u0061\u0074\u0069\u006f\u006e\u002fv\u006e\u0064\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066o\u0072\u006d\u0061\u0074s\u002d\u006f\u0066\u0066\u0069\u0063e\u0064\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002e\u0070\u0072\u0065\u0073\u0065\u006e\u0074\u0061t\u0069\u006f\u006e\u006d\u006c\u002e\u0076\u0069\u0065\u0077\u0050\u0072\u006f\u0070\u0073\u002b\u0078\u006d\u006c");
_cgf .ContentTypes .AddOverride ("\u0070\u0070\u0074\u002fta\u0062\u006c\u0065\u0053\u0074\u0079\u006c\u0065\u0073\u002e\u0078\u006d\u006c","\u0061\u0070\u0070\u006c\u0069c\u0061\u0074\u0069\u006f\u006e\u002f\u0076n\u0064\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002d\u006f\u0066\u0066\u0069\u0063\u0065\u0064\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002e\u0070\u0072\u0065\u0073\u0065\u006e\u0074\u0061t\u0069\u006f\u006e\u006d\u006c\u002e\u0074\u0061\u0062\u006c\u0065\u0053t\u0079\u006c\u0065\u0073\u002b\u0078m\u006c");
_cgf .Rels .AddRelationship ("\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u002f\u0063\u006f\u0072e\u002e\u0078\u006d\u006c","\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061s\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066o\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0070\u0061\u0063\u006ba\u0067\u0065\u002f\u0032\u0030\u0030\u0036\u002f\u0072\u0065\u006c\u0061\u0074\u0069\u006f\u006e\u0073\u0068\u0069\u0070\u0073\u002f\u006d\u0065\u0074\u0061\u0064\u0061\u0074\u0061/\u0063\u006f\u0072\u0065\u002d\u0070\u0072\u006f\u0070e\u0072\u0074i\u0065\u0073");
_cgf .Rels .AddRelationship ("\u0064\u006fc\u0050\u0072\u006fp\u0073\u002f\u0061\u0070\u0070\u002e\u0078\u006d\u006c","\u0068t\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002eo\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072\u006da\u0074\u0073.\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0072\u0065\u006c\u0061\u0074i\u006f\u006e\u0073\u0068\u0069p\u0073\u002f\u0065x\u0074\u0065\u006e\u0064\u0065d\u002d\u0070\u0072\u006f\u0070\u0065\u0072\u0074\u0069\u0065\u0073");
_cgf .Rels .AddRelationship ("p\u0070t\u002f\u0070\u0072\u0065\u0073\u0065\u006e\u0074a\u0074\u0069\u006f\u006e.x\u006d\u006c","\u0068\u0074\u0074\u0070\u003a\u002f\u002fs\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006da\u0074\u0073\u002e\u006f\u0072g\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0072\u0065\u006c\u0061\u0074\u0069\u006f\u006e\u0073\u0068\u0069\u0070\u0073\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074");
_cgf ._ad .SldMasterIdLst =_eg .NewCT_SlideMasterIdList ();_ecg :=_eg .NewSldMaster ();_ecg .ClrMap .Bg1Attr =_bd .ST_ColorSchemeIndexLt1 ;_ecg .ClrMap .Bg2Attr =_bd .ST_ColorSchemeIndexLt2 ;_ecg .ClrMap .Tx1Attr =_bd .ST_ColorSchemeIndexDk1 ;_ecg .ClrMap .Tx2Attr =_bd .ST_ColorSchemeIndexDk2 ;
_ecg .ClrMap .Accent1Attr =_bd .ST_ColorSchemeIndexAccent1 ;_ecg .ClrMap .Accent2Attr =_bd .ST_ColorSchemeIndexAccent2 ;_ecg .ClrMap .Accent3Attr =_bd .ST_ColorSchemeIndexAccent3 ;_ecg .ClrMap .Accent4Attr =_bd .ST_ColorSchemeIndexAccent4 ;_ecg .ClrMap .Accent5Attr =_bd .ST_ColorSchemeIndexAccent5 ;
_ecg .ClrMap .Accent6Attr =_bd .ST_ColorSchemeIndexAccent6 ;_ecg .ClrMap .HlinkAttr =_bd .ST_ColorSchemeIndexHlink ;_ecg .ClrMap .FolHlinkAttr =_bd .ST_ColorSchemeIndexFolHlink ;_cgf ._gbc =append (_cgf ._gbc ,_ecg );_cgf ._bbb =append (_cgf ._bbb ,len (_cgf ._gbc ));
_bef :=_geb .AbsoluteFilename (_geb .DocTypePresentation ,_geb .SlideMasterType ,1);_cgf .ContentTypes .AddOverride (_bef ,_geb .SlideMasterContentType );_gcc :=_cgf ._aad .AddAutoRelationship (_geb .DocTypePresentation ,_geb .OfficeDocumentType ,1,_geb .SlideMasterType );
_fed :=_eg .NewCT_SlideMasterIdListEntry ();_fed .IdAttr =_geb .Uint32 (2147483648);_fed .RIdAttr =_gcc .ID ();_cgf ._ad .SldMasterIdLst .SldMasterId =append (_cgf ._ad .SldMasterIdLst .SldMasterId ,_fed );_afce :=_df .NewRelationships ();_cgf ._dgc =append (_cgf ._dgc ,_afce );
_baa :=_eg .NewSldLayout ();_ddec :=_afce .AddAutoRelationship (_geb .DocTypePresentation ,_geb .SlideMasterType ,1,_geb .SlideLayoutType );_ecgc :=_geb .AbsoluteFilename (_geb .DocTypePresentation ,_geb .SlideLayoutType ,1);_cgf .ContentTypes .AddOverride (_ecgc ,_geb .SlideLayoutContentType );
_afce .AddAutoRelationship (_geb .DocTypePresentation ,_geb .SlideMasterType ,1,_geb .ThemeType );_cgf ._bdbg =append (_cgf ._bdbg ,_baa );_ecg .SldLayoutIdLst =_eg .NewCT_SlideLayoutIdList ();_gcdf :=_eg .NewCT_SlideLayoutIdListEntry ();_gcdf .IdAttr =_geb .Uint32 (2147483649);
_gcdf .RIdAttr =_ddec .ID ();_ecg .SldLayoutIdLst .SldLayoutId =append (_ecg .SldLayoutIdLst .SldLayoutId ,_gcdf );_cda :=_df .NewRelationships ();_cgf ._bebc =append (_cgf ._bebc ,_cda );_cda .AddAutoRelationship (_geb .DocTypePresentation ,_geb .SlideType ,1,_geb .SlideMasterType );
_cgf ._ad .NotesSz .CxAttr =6858000;_cgf ._ad .NotesSz .CyAttr =9144000;_baeg :=_bd .NewTheme ();_baeg .NameAttr =_geb .String ("\u0075n\u0069o\u0066\u0066\u0069\u0063\u0065\u0020\u0054\u0068\u0065\u006d\u0065");_baeg .ThemeElements .ClrScheme .NameAttr ="\u004f\u0066\u0066\u0069\u0063\u0065";
_baeg .ThemeElements .ClrScheme .Dk1 .SysClr =_bd .NewCT_SystemColor ();_baeg .ThemeElements .ClrScheme .Dk1 .SysClr .LastClrAttr =_geb .String ("\u0030\u0030\u0030\u0030\u0030\u0030");_baeg .ThemeElements .ClrScheme .Dk1 .SysClr .ValAttr =_bd .ST_SystemColorValWindowText ;
_baeg .ThemeElements .ClrScheme .Lt1 .SysClr =_bd .NewCT_SystemColor ();_baeg .ThemeElements .ClrScheme .Lt1 .SysClr .LastClrAttr =_geb .String ("\u0066\u0066\u0066\u0066\u0066\u0066");_baeg .ThemeElements .ClrScheme .Lt1 .SysClr .ValAttr =_bd .ST_SystemColorValWindow ;
_baeg .ThemeElements .ClrScheme .Dk2 .SrgbClr =_bd .NewCT_SRgbColor ();_baeg .ThemeElements .ClrScheme .Dk2 .SrgbClr .ValAttr ="\u0034\u0034\u0035\u0034\u0036\u0061";_baeg .ThemeElements .ClrScheme .Lt2 .SrgbClr =_bd .NewCT_SRgbColor ();_baeg .ThemeElements .ClrScheme .Lt2 .SrgbClr .ValAttr ="\u0065\u0037\u0065\u0037\u0065\u0036";
_baeg .ThemeElements .ClrScheme .Accent1 .SrgbClr =_bd .NewCT_SRgbColor ();_baeg .ThemeElements .ClrScheme .Accent1 .SrgbClr .ValAttr ="\u0034\u0034\u0037\u0032\u0063\u0034";_baeg .ThemeElements .ClrScheme .Accent2 .SrgbClr =_bd .NewCT_SRgbColor ();_baeg .ThemeElements .ClrScheme .Accent2 .SrgbClr .ValAttr ="\u0065\u0064\u0037\u0064\u0033\u0031";
_baeg .ThemeElements .ClrScheme .Accent3 .SrgbClr =_bd .NewCT_SRgbColor ();_baeg .ThemeElements .ClrScheme .Accent3 .SrgbClr .ValAttr ="\u0061\u0035\u0061\u0035\u0061\u0035";_baeg .ThemeElements .ClrScheme .Accent4 .SrgbClr =_bd .NewCT_SRgbColor ();_baeg .ThemeElements .ClrScheme .Accent4 .SrgbClr .ValAttr ="\u0066\u0066\u0063\u0030\u0030\u0030";
_baeg .ThemeElements .ClrScheme .Accent5 .SrgbClr =_bd .NewCT_SRgbColor ();_baeg .ThemeElements .ClrScheme .Accent5 .SrgbClr .ValAttr ="\u0035\u0062\u0039\u0062\u0064\u0035";_baeg .ThemeElements .ClrScheme .Accent6 .SrgbClr =_bd .NewCT_SRgbColor ();_baeg .ThemeElements .ClrScheme .Accent6 .SrgbClr .ValAttr ="\u0037\u0030\u0061\u0064\u0034\u0037";
_baeg .ThemeElements .ClrScheme .Hlink .SrgbClr =_bd .NewCT_SRgbColor ();_baeg .ThemeElements .ClrScheme .Hlink .SrgbClr .ValAttr ="\u0030\u0035\u0036\u0033\u0063\u0031";_baeg .ThemeElements .ClrScheme .FolHlink .SrgbClr =_bd .NewCT_SRgbColor ();_baeg .ThemeElements .ClrScheme .FolHlink .SrgbClr .ValAttr ="\u0039\u0035\u0034\u0066\u0037\u0032";
_baeg .ThemeElements .FontScheme .NameAttr ="\u004f\u0066\u0066\u0069\u0063\u0065";_baeg .ThemeElements .FontScheme .MajorFont .Latin .TypefaceAttr ="\u0043\u0061\u006c\u0069\u0062\u0072\u0069\u0020\u004c\u0069\u0067\u0068\u0074";_baeg .ThemeElements .FontScheme .MinorFont .Latin .TypefaceAttr ="\u0043a\u006c\u0069\u0062\u0072\u0069";
_baeg .ThemeElements .FmtScheme .NameAttr =_geb .String ("\u004f\u0066\u0066\u0069\u0063\u0065");_ged :=_bd .NewEG_FillProperties ();_baeg .ThemeElements .FmtScheme .FillStyleLst .EG_FillProperties =append (_baeg .ThemeElements .FmtScheme .FillStyleLst .EG_FillProperties ,_ged );
_ged .FillPropertiesChoice .SolidFill =&_bd .CT_SolidColorFillProperties {SchemeClr :&_bd .CT_SchemeColor {ValAttr :_bd .ST_SchemeColorValPhClr }};_ged =_bd .NewEG_FillProperties ();_baeg .ThemeElements .FmtScheme .FillStyleLst .EG_FillProperties =append (_baeg .ThemeElements .FmtScheme .FillStyleLst .EG_FillProperties ,_ged );
_baeg .ThemeElements .FmtScheme .FillStyleLst .EG_FillProperties =append (_baeg .ThemeElements .FmtScheme .FillStyleLst .EG_FillProperties ,_ged );_ged .FillPropertiesChoice .GradFill =&_bd .CT_GradientFillProperties {RotWithShapeAttr :_geb .Bool (true ),GsLst :&_bd .CT_GradientStopList {},ShadePropertiesChoice :&_bd .EG_ShadePropertiesChoice {Lin :&_bd .CT_LinearShadeProperties {}}};
_ged .FillPropertiesChoice .GradFill .ShadePropertiesChoice .Lin .AngAttr =_geb .Int32 (5400000);_ged .FillPropertiesChoice .GradFill .ShadePropertiesChoice .Lin .ScaledAttr =_geb .Bool (false );_dgcd :=_bd .NewCT_GradientStop ();_dgcd .PosAttr .ST_PositiveFixedPercentageDecimal =_geb .Int32 (0);
_dgcd .SchemeClr =&_bd .CT_SchemeColor {ValAttr :_bd .ST_SchemeColorValPhClr };_ged .FillPropertiesChoice .GradFill .GsLst .Gs =append (_ged .FillPropertiesChoice .GradFill .GsLst .Gs ,_dgcd );_dgcd =_bd .NewCT_GradientStop ();_dgcd .PosAttr .ST_PositiveFixedPercentageDecimal =_geb .Int32 (50000);
_dgcd .SchemeClr =&_bd .CT_SchemeColor {ValAttr :_bd .ST_SchemeColorValPhClr };_ged .FillPropertiesChoice .GradFill .GsLst .Gs =append (_ged .FillPropertiesChoice .GradFill .GsLst .Gs ,_dgcd );_baeg .ThemeElements .FmtScheme .LnStyleLst =_bd .NewCT_LineStyleList ();
for _aeba :=0;_aeba < 3;_aeba ++{_bebcg :=_bd .NewCT_LineProperties ();_bebcg .WAttr =_geb .Int32 (int32 (6350*(_aeba +1)));_bebcg .CapAttr =_bd .ST_LineCapFlat ;_bebcg .CmpdAttr =_bd .ST_CompoundLineSng ;_bebcg .AlgnAttr =_bd .ST_PenAlignmentCtr ;_baeg .ThemeElements .FmtScheme .LnStyleLst .Ln =append (_baeg .ThemeElements .FmtScheme .LnStyleLst .Ln ,_bebcg );
};_baeg .ThemeElements .FmtScheme .EffectStyleLst =_bd .NewCT_EffectStyleList ();for _gec :=0;_gec < 3;_gec ++{_bgaf :=_bd .NewCT_EffectStyleItem ();_bgaf .EffectPropertiesChoice .EffectLst =_bd .NewCT_EffectList ();_baeg .ThemeElements .FmtScheme .EffectStyleLst .EffectStyle =append (_baeg .ThemeElements .FmtScheme .EffectStyleLst .EffectStyle ,_bgaf );
};_ced :=_bd .NewEG_FillProperties ();_ced .FillPropertiesChoice .SolidFill =&_bd .CT_SolidColorFillProperties {SchemeClr :&_bd .CT_SchemeColor {ValAttr :_bd .ST_SchemeColorValPhClr }};_baeg .ThemeElements .FmtScheme .BgFillStyleLst .EG_FillProperties =append (_baeg .ThemeElements .FmtScheme .BgFillStyleLst .EG_FillProperties ,_ced );
_baeg .ThemeElements .FmtScheme .BgFillStyleLst .EG_FillProperties =append (_baeg .ThemeElements .FmtScheme .BgFillStyleLst .EG_FillProperties ,_ced );_baeg .ThemeElements .FmtScheme .BgFillStyleLst .EG_FillProperties =append (_baeg .ThemeElements .FmtScheme .BgFillStyleLst .EG_FillProperties ,_ged );
_cgf ._bed =append (_cgf ._bed ,_baeg );_ead :=_geb .AbsoluteFilename (_geb .DocTypePresentation ,_geb .ThemeType ,1);_cgf .ContentTypes .AddOverride (_ead ,_geb .ThemeContentType );_cgf ._aad .AddAutoRelationship (_geb .DocTypePresentation ,_geb .OfficeDocumentType ,1,_geb .ThemeType );
_gde :=_df .NewRelationships ();_cgf ._fea =append (_cgf ._fea ,_gde );_cgf ._gbb =append (_cgf ._gbb ,len (_cgf ._bed ));return _cgf ;};

// GetImageByRelID returns an ImageRef with the associated relation ID in the
// slide.
func (_fac *Slide )GetImageByRelID (relID string )(_df .ImageRef ,bool ){_ebcc :=_fac .getSlideRels ();if (_ebcc ==_df .Relationships {}){return _df .ImageRef {},false ;};_gabe :=_ebcc .GetTargetByRelId (relID );for _ ,_ceac :=range _fac ._acbf .Images {if _ceac .Target ()==_gabe {return _ceac ,true ;
};};return _df .ImageRef {},false ;};

// GetChartSpaceByRelId returns a *crt.ChartSpace with the associated relation ID in the
// slide.
func (_bcbe *Slide )GetChartSpaceByRelId (relId string )*_b .ChartSpace {_gdea :=_bcbe .getSlideRels ();if (_gdea ==_df .Relationships {}){return nil ;};_cfag :=_gdea .GetTargetByRelId (relId );for _ ,_gaaf :=range _bcbe ._acbf ._eeb {if _cfag ==_gaaf .Target (){return _gaaf ._gf ;
};};return nil ;};

// NewPresentationProperties constructs a new PresentationProperties.
func NewPresentationProperties ()PresentationProperties {return PresentationProperties {_fag :_eg .NewPresentationPr ()};};

// Paragraphs returns the paragraphs defined in the placeholder.
func (_gea PlaceHolder )Paragraphs ()[]_cb .Paragraph {_cefd :=[]_cb .Paragraph {};for _ ,_fabb :=range _gea ._bdb .TxBody .P {_cefd =append (_cefd ,_cb .MakeParagraph (_fabb ));};return _cefd ;};func (_fg *chart )Target ()string {return _fg ._ff };

// X returns the inner wrapped XML type.
func (_dcc *SlideSize )X ()*_eg .CT_SlideSize {return _dcc ._bfe };

// SlideScreenSize represents the slide screen size as a 2 element array
// representing the width and height in EMU units.
type SlideScreenSize [2]int32 ;

// TextItem is used for keeping text with references to a paragraph and run, a shape or a table, a row and a cell where it is located.
type TextItem struct{Text string ;Presentation *Presentation ;Shape *_eg .CT_Shape ;GraphicFrame *_eg .CT_GraphicalObjectFrame ;Paragraph *_bd .CT_TextParagraph ;Run *_bd .CT_RegularTextRun ;TableInfo *TableInfo ;_af []rectangle ;_cbe int ;_ae int ;};

// Properties returns the properties of the TextBox.
func (_fdb Image )Properties ()_cb .ShapeProperties {if _fdb ._eea .SpPr ==nil {_fdb ._eea .SpPr =_bd .NewCT_ShapeProperties ();};return _cb .MakeShapeProperties (_fdb ._eea .SpPr );};

// OpenTemplate opens a template file.
func OpenTemplate (fn string )(*Presentation ,error ){_abf ,_cbeg :=Open (fn );if _cbeg !=nil {return nil ,_cbeg ;};return _abf ,nil ;};

// TableInfo is used for keep information about a table, a row and a cell where the text is located.
type TableInfo struct{Table *_bd .CT_Table ;Row *_bd .CT_TableRow ;Cell *_bd .CT_TableCell ;RowIndex int ;ColIndex int ;};func (_dfae *Slide )getSlideRels ()_df .Relationships {_bceg :=_dfae ._acbf ;for _dfgg ,_fbag :=range _bceg .Slides (){if *_dfae ._gbabe ==*_fbag ._gbabe {return _bceg ._adg [_dfgg ];
};};return _df .Relationships {};};

// X returns the inner wrapped XML type.
func (_cba PlaceHolder )X ()*_eg .CT_Shape {return _cba ._bdb };var _gfaf =false ;

// SlideViewPr returns the SlideViewPr property.
func (_fdaf ViewProperties )SlideViewPr ()*_eg .CT_SlideViewProperties {return _fdaf ._ede .SlideViewPr };type chart struct{_gf *_b .ChartSpace ;_eb string ;_ff string ;};

// X returns the inner wrapped XML type.
func (_cegb Slide )X ()*_eg .Sld {return _cegb ._gbabe };func (_dcf *Presentation )onNewRelationship (_fcfg *_be .DecodeMap ,_abbc ,_acff string ,_bead []*_ge .File ,_ggb *_e .Relationship ,_ggab _be .Target )error {_adfg :=_geb .DocTypePresentation ;switch _acff {case _geb .OfficeDocumentType :_dcf ._ad =_eg .NewPresentation ();
_fcfg .AddTarget (_abbc ,_dcf ._ad ,_acff ,0);_fcfg .AddTarget (_be .RelationsPathFor (_abbc ),_dcf ._aad .X (),_acff ,0);_ggb .TargetAttr =_geb .RelativeFilename (_adfg ,_ggab .Typ ,_acff ,0);case _geb .CorePropertiesType :_fcfg .AddTarget (_abbc ,_dcf .CoreProperties .X (),_acff ,0);
_ggb .TargetAttr =_geb .RelativeFilename (_adfg ,_ggab .Typ ,_acff ,0);case _geb .CustomPropertiesType :_fcfg .AddTarget (_abbc ,_dcf .CustomProperties .X (),_acff ,0);_ggb .TargetAttr =_geb .RelativeFilename (_adfg ,_ggab .Typ ,_acff ,0);case _geb .PresentationPropertiesType :_fcfg .AddTarget (_abbc ,_dcf ._ebba .X (),_acff ,0);
_ggb .TargetAttr =_geb .RelativeFilename (_adfg ,_ggab .Typ ,_acff ,0);case _geb .ViewPropertiesType :_fcfg .AddTarget (_abbc ,_dcf ._aba .X (),_acff ,0);_ggb .TargetAttr =_geb .RelativeFilename (_adfg ,_ggab .Typ ,_acff ,0);case _geb .TableStylesType :_fcfg .AddTarget (_abbc ,_dcf ._bge .X (),_acff ,0);
_ggb .TargetAttr =_geb .RelativeFilename (_adfg ,_ggab .Typ ,_acff ,0);case _geb .HyperLinkType :_bfgf :=_bd .NewCT_Hyperlink ();_ccfe :=uint32 (len (_dcf ._gaaa ));_fcfg .AddTarget (_abbc ,_bfgf ,_acff ,_ccfe );_dcf ._gaaa =append (_dcf ._gaaa ,_bfgf );
case _geb .CustomXMLType :if _aegc ,_afe :=_ef .StringToNumbers (_abbc );_afe {if len (_dcf ._effd )< _aegc {_efbe :=&_geb .XSDAny {};_fcfg .AddTarget (_abbc ,_efbe ,_acff ,uint32 (_aegc ));_dcf ._effd =append (_dcf ._effd ,_efbe );_dcf ._ed =append (_dcf ._ed ,_aegc );
_ggb .TargetAttr =_geb .RelativeFilename (_adfg ,_ggab .Typ ,_acff ,_aegc );};};case _geb .ChartType :_cbdg :=chart {_gf :_b .NewChartSpace ()};_ecag :=uint32 (len (_dcf ._eeb ));_fcfg .AddTarget (_abbc ,_cbdg ._gf ,_acff ,_ecag );_dcf ._eeb =append (_dcf ._eeb ,&_cbdg );
_ggb .TargetAttr =_geb .RelativeFilename (_adfg ,_ggab .Typ ,_acff ,len (_dcf ._eeb ));_cbdg ._ff =_ggb .TargetAttr ;case _geb .HandoutMasterType :_ffe :=_eg .NewHandoutMaster ();_cded :=uint32 (len (_dcf ._cbee ));_fcfg .AddTarget (_abbc ,_ffe ,_acff ,_cded );
_dcf ._cbee =append (_dcf ._cbee ,_ffe );_ggb .TargetAttr =_geb .RelativeFilename (_adfg ,_ggab .Typ ,_acff ,len (_dcf ._cbee ));case _geb .NotesMasterType :if _dbd ,_ebab :=_ef .StringToNumbers (_abbc );_ebab {if len (_dcf ._bab )< _dbd {_ddf :=_eg .NewNotesMaster ();
_dcf ._bab =append (_dcf ._bab ,_ddf );_dcf ._ggf =append (_dcf ._ggf ,_dbd );_fcfg .AddTarget (_abbc ,_ddf ,_acff ,uint32 (_dbd ));_ggb .TargetAttr =_geb .RelativeFilename (_adfg ,_ggab .Typ ,_acff ,_dbd );};};case _geb .ExtendedPropertiesType :_fcfg .AddTarget (_abbc ,_dcf .AppProperties .X (),_acff ,0);
_ggb .TargetAttr =_geb .RelativeFilename (_adfg ,_ggab .Typ ,_acff ,0);case _geb .SlideType :if _fadf ,_dgab :=_ef .StringToNumbers (_abbc );_dgab {if len (_dcf ._bac )< _fadf {_aab :=_eg .NewSld ();_dcf ._bac =append (_dcf ._bac ,_aab );_dcf ._ccd =append (_dcf ._ccd ,_fadf );
_fcfg .AddTarget (_abbc ,_aab ,_acff ,uint32 (_fadf ));_ggb .TargetAttr =_geb .RelativeFilename (_adfg ,_ggab .Typ ,_acff ,_fadf );_fgb :=_df .NewRelationships ();_fcfg .AddTarget (_be .RelationsPathFor (_abbc ),_fgb .X (),_acff ,0);if len (_dcf ._adg )>=_fadf {_dcf ._adg [_fadf -1]=_fgb ;
}else {_dcf ._adg =append (_dcf ._adg ,_fgb );};};};case _geb .SlideMasterType :if _feba ,_gbca :=_ef .StringToNumbers (_abbc );_gbca {if len (_dcf ._gbc )< _feba {_dca :=_eg .NewSldMaster ();if !_fcfg .AddTarget (_abbc ,_dca ,_acff ,uint32 (_feba )){return nil ;
};_dcf ._gbc =append (_dcf ._gbc ,_dca );_dcf ._bbb =append (_dcf ._bbb ,_feba );_ggb .TargetAttr =_geb .RelativeFilename (_adfg ,_ggab .Typ ,_acff ,_feba );_baaf :=_df .NewRelationships ();_fcfg .AddTarget (_be .RelationsPathFor (_abbc ),_baaf .X (),_acff ,0);
if len (_dcf ._dgc )>=_feba {_dcf ._dgc [_feba -1]=_baaf ;}else {_dcf ._dgc =append (_dcf ._adg ,_baaf );};};};case _geb .SlideLayoutType :if _fda ,_eabb :=_ef .StringToNumbers (_abbc );_eabb {_bcg :=_eg .NewSldLayout ();if !_fcfg .AddTarget (_abbc ,_bcg ,_acff ,uint32 (_fda )){return nil ;
};for _dgae :=len (_dcf ._bdbg );_dgae < _fda ;_dgae ++{_dcf ._bdbg =append (_dcf ._bdbg ,nil );};_dcf ._bdbg [_fda -1]=_bcg ;_ggb .TargetAttr =_geb .RelativeFilename (_adfg ,_ggab .Typ ,_acff ,_fda );for _bgb :=len (_dcf ._bebc );_bgb < _fda ;_bgb ++{_dcf ._bebc =append (_dcf ._bebc ,_df .NewRelationships ());
};_ada :=_df .NewRelationships ();_fcfg .AddTarget (_be .RelationsPathFor (_abbc ),_ada .X (),_acff ,0);_dcf ._bebc [_fda -1]=_ada ;};case _geb .ThumbnailType :for _ggd ,_gfe :=range _bead {if _gfe ==nil {continue ;};if _gfe .Name ==_abbc {_gbg ,_gfed :=_gfe .Open ();
if _gfed !=nil {return _eff .Errorf ("e\u0072\u0072\u006f\u0072\u0020\u0072e\u0061\u0064\u0069\u006e\u0067\u0020\u0074\u0068\u0075m\u0062\u006e\u0061i\u006c:\u0020\u0025\u0073",_gfed );};_dcf .Thumbnail ,_ ,_gfed =_ba .Decode (_gbg );_gbg .Close ();if _gfed !=nil {return _eff .Errorf ("\u0065\u0072\u0072\u006fr\u0020\u0064\u0065\u0063\u006f\u0064\u0069\u006e\u0067\u0020t\u0068u\u006d\u0062\u006e\u0061\u0069\u006c\u003a \u0025\u0073",_gfed );
};_bead [_ggd ]=nil ;};};case _geb .ThemeType :if _fcff ,_eec :=_ef .StringToNumbers (_abbc );_eec {if len (_dcf ._bed )< _fcff {_eeg :=_bd .NewTheme ();if !_fcfg .AddTarget (_abbc ,_eeg ,_acff ,uint32 (_fcff )){return nil ;};_dcf ._bed =append (_dcf ._bed ,_eeg );
_dcf ._gbb =append (_dcf ._gbb ,_fcff );_ggb .TargetAttr =_geb .RelativeFilename (_adfg ,_ggab .Typ ,_acff ,_fcff );_eeba :=_df .NewRelationships ();_fcfg .AddTarget (_be .RelationsPathFor (_abbc ),_eeba .X (),_acff ,0);if len (_dcf ._fea )>=_fcff {_dcf ._fea [_fcff -1]=_eeba ;
}else {_dcf ._fea =append (_dcf ._fea ,_eeba );};};};case _geb .ImageType :_abbc =_ag .Clean (_abbc );if _abge ,_gdg :=_dcf ._acec [_abbc ];_gdg {_ggb .TargetAttr =_abge ;return nil ;};_fgef :="";for _ggfg ,_fgdc :=range _bead {if _fgdc ==nil {continue ;
};if _fgdc .Name ==_abbc {_bgdg ,_fdfb :=_be .ExtractToDiskTmp (_fgdc ,_dcf .TmpPath );if _fdfb !=nil {return _fdfb ;};_gabb ,_fdfb :=_df .ImageFromStorage (_bgdg );if _fdfb !=nil {return _fdfb ;};_fgef =_gabb .Format ;if _gabb .Format =="\u006a\u0070\u0065\u0067"&&_d .HasSuffix (_abbc ,"\u006a\u0070\u0067"){_fgef ="\u006a\u0070\u0067";
};_bgdd :=_df .MakeImageRef (_gabb ,&_dcf .DocBase ,_dcf ._aad );_bgdd .SetTarget ("\u002e\u002e\u002f"+_abbc [4:]);_dcf .Images =append (_dcf .Images ,_bgdd );_bead [_ggfg ]=nil ;_adb :=len (_dcf .Images );if _ffaf ,_fgee :=_ef .StringToNumbers (_abbc );
_fgee {_adb =_ffaf ;};_fcfg .RecordIndex (_abbc ,_adb );break ;};};_ggbd :=_fcfg .IndexFor (_abbc );_ggb .TargetAttr =_geb .RelativeImageFilename (_adfg ,_ggab .Typ ,_acff ,_ggbd ,_fgef );_dcf ._acec [_abbc ]=_ggb .TargetAttr ;default:_ca .Log .Debug ("\u0075\u006e\u0073\u0075\u0070p\u006f\u0072\u0074\u0065\u0064\u0020\u0072\u0065\u006c\u0061\u0074\u0069\u006fn\u0073\u0068\u0069\u0070\u0020\u0074\u0079\u0070\u0065\u003a\u0020\u0025\u0073\u0020\u0074\u0067\u0074\u003a\u0020\u0025\u0073",_acff ,_abbc );
};return nil ;};

// AddParagraph adds a new paragraph to a placeholder.
func (_egd PlaceHolder )AddParagraph ()_cb .Paragraph {_dfeb :=_cb .MakeParagraph (_bd .NewCT_TextParagraph ());_egd ._bdb .TxBody .P =append (_egd ._bdb .TxBody .P ,_dfeb .X ());return _dfeb ;};

// ShowCommentsAttr returns the WebPr property.
func (_dbe ViewProperties )ShowCommentsAttr ()*bool {return _dbe ._ede .ShowCommentsAttr };

// SlideText is an array of extracted text items which has some methods for representing extracted text from a slide.
type SlideText struct{Items []*TextItem ;};func (_dd *chart )X ()*_b .ChartSpace {return _dd ._gf };func (_bcdd TextBox )getOff ()*_bd .CT_Point2D {if _bcdd ._dfea .SpPr ==nil {_bcdd ._dfea .SpPr =_bd .NewCT_ShapeProperties ();};if _bcdd ._dfea .SpPr .Xfrm ==nil {_bcdd ._dfea .SpPr .Xfrm =_bd .NewCT_Transform2D ();
};if _bcdd ._dfea .SpPr .Xfrm .Off ==nil {_bcdd ._dfea .SpPr .Xfrm .Off =_bd .NewCT_Point2D ();};return _bcdd ._dfea .SpPr .Xfrm .Off ;};

// X returns the inner wrapped XML type.
func (_gafg SlideLayout )X ()*_eg .SldLayout {return _gafg ._gced };func _cag (_ab *Presentation ,_acf []*_eg .CT_GroupShapeChoice ,_ddd []rectangle ,_bb []*TextItem )[]*TextItem {for _ ,_ce :=range _acf {_bgc :=append ([]rectangle {},_ddd ...);if _ce .Sp !=nil {_bb =append (_bb ,_gd (_ab ,_ce .Sp ,nil ,nil ,_ce .Sp .SpPr .Xfrm ,0,_ddd ,_ce .Sp .TxBody .P )...);
};if _ce .GraphicFrame !=nil &&_ce .GraphicFrame .Graphic !=nil &&_ce .GraphicFrame .Graphic .GraphicData !=nil {_aac :=_ce .GraphicFrame .Xfrm ;for _ ,_da :=range _ce .GraphicFrame .Graphic .GraphicData .Any {if _dfb ,_afc :=_da .(*_bd .Tbl );_afc {_aff :=&_dfb .CT_Table ;
_efd :=0;for _bc ,_fc :=range _dfb .Tr {for _ga ,_cbf :=range _fc .Tc {_gfb :=&TableInfo {Table :_aff ,Row :_fc ,Cell :_cbf ,RowIndex :_bc ,ColIndex :_ga };_bb =append (_bb ,_gd (_ab ,nil ,_ce .GraphicFrame ,_gfb ,_aac ,_efd ,_ddd ,_cbf .TxBody .P )...);
_efd ++;};};};};};if _ce .GrpSp !=nil {if _ce .GrpSp .GrpSpPr !=nil {_gbab :=_ce .GrpSp .GrpSpPr .Xfrm ;var _dbg ,_afa int64 ;if _gbab .Off !=nil {_cbg ,_fcb :=_gbab .Off .XAttr .ST_CoordinateUnqualified ,_gbab .Off .YAttr .ST_CoordinateUnqualified ;if _cbg !=nil &&_fcb !=nil {if _bf :=_gbab .Ext ;
_bf !=nil {_dbg ,_afa =_bf .CxAttr ,_bf .CyAttr ;};_bgc =append (_bgc ,rectangle {_acc :*_cbg ,_gfg :*_fcb ,_ffb :*_cbg +_dbg ,_aa :*_fcb +_afa });};};};_bb =_cag (_ab ,_ce .GrpSp .GroupShapeChoice ,_bgc ,_bb );};};return _bb ;};