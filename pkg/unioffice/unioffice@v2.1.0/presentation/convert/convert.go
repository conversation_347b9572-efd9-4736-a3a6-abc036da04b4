//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package convert ;import (_f "bytes";_c "errors";_ef "github.com/unidoc/unioffice/v2/common";_eb "github.com/unidoc/unioffice/v2/common/logger";_dg "github.com/unidoc/unioffice/v2/common/tempstorage";_dd "github.com/unidoc/unioffice/v2/internal/convertutils";
_ac "github.com/unidoc/unioffice/v2/measurement";_cf "github.com/unidoc/unioffice/v2/presentation";_bc "github.com/unidoc/unioffice/v2/schema/soo/dml";_cg "github.com/unidoc/unioffice/v2/schema/soo/dml/chart";_ce "github.com/unidoc/unioffice/v2/schema/soo/pml";
_db "github.com/unidoc/unipdf/v3/contentstream/draw";_bf "github.com/unidoc/unipdf/v3/core";_cd "github.com/unidoc/unipdf/v3/creator";_ace "github.com/unidoc/unipdf/v3/model";_bgg "github.com/unidoc/unipdf/v3/render";_bg "image";_g "image/color";_bd "image/draw";
_d "math";_e "strconv";_a "strings";);func (_eaf *convertContext )extractDefaultProperties (){_ebd :=_eaf ._eec .X ();_eg :=_ebd .DefaultTextStyle ;var _cdd ,_dgd ,_cfe ,_daf ,_fbf ,_ae ,_agc ,_dc ,_bac ,_dae *_bc .CT_TextParagraphProperties ;if _eg !=nil {_cdd =_eg .DefPPr ;
_dgd =_bceg (_eg .Lvl1pPr ,_cdd );_cfe =_bceg (_eg .Lvl2pPr ,_cdd );_daf =_bceg (_eg .Lvl3pPr ,_cdd );_fbf =_bceg (_eg .Lvl4pPr ,_cdd );_ae =_bceg (_eg .Lvl5pPr ,_cdd );_agc =_bceg (_eg .Lvl6pPr ,_cdd );_dc =_bceg (_eg .Lvl7pPr ,_cdd );_bac =_bceg (_eg .Lvl8pPr ,_cdd );
_dae =_bceg (_eg .Lvl9pPr ,_cdd );_eaf ._baad =_cdd ;_eaf ._bgga =_cdd .DefRPr ;};_eaf ._dcga =make ([]*_bc .CT_TextParagraphProperties ,9);_eaf ._dcga [0]=_dgd ;_eaf ._dcga [1]=_cfe ;_eaf ._dcga [2]=_daf ;_eaf ._dcga [3]=_fbf ;_eaf ._dcga [4]=_ae ;_eaf ._dcga [5]=_agc ;
_eaf ._dcga [6]=_dc ;_eaf ._dcga [7]=_bac ;_eaf ._dcga [8]=_dae ;_fd :=_eaf ._eec .SlideMasters ()[0].X ();_fg :=_fd .TxStyles ;_bda :=_fg .TitleStyle ;_eaf ._gfg =_bceg (_bda .DefPPr ,_cdd );_eaf ._bfac =_eaf ._gfg .DefRPr ;_eaf ._dgaa =make ([]*_bc .CT_TextParagraphProperties ,9);
_eaf ._dgaa [0]=_bceg (_bda .Lvl1pPr ,_dgd );_eaf ._dgaa [1]=_bceg (_bda .Lvl2pPr ,_cfe );_eaf ._dgaa [2]=_bceg (_bda .Lvl3pPr ,_daf );_eaf ._dgaa [3]=_bceg (_bda .Lvl4pPr ,_fbf );_eaf ._dgaa [4]=_bceg (_bda .Lvl5pPr ,_ae );_eaf ._dgaa [5]=_bceg (_bda .Lvl6pPr ,_agc );
_eaf ._dgaa [6]=_bceg (_bda .Lvl7pPr ,_dc );_eaf ._dgaa [7]=_bceg (_bda .Lvl8pPr ,_bac );_eaf ._dgaa [8]=_bceg (_bda .Lvl9pPr ,_dae );_af :=_fg .BodyStyle ;_eaf ._fbbc =_bceg (_af .DefPPr ,_cdd );_eaf ._dcc =_eaf ._fbbc .DefRPr ;_eaf ._cdg =make ([]*_bc .CT_TextParagraphProperties ,9);
_eaf ._cdg [0]=_bceg (_af .Lvl1pPr ,_dgd );_eaf ._cdg [1]=_bceg (_af .Lvl2pPr ,_cfe );_eaf ._cdg [2]=_bceg (_af .Lvl3pPr ,_daf );_eaf ._cdg [3]=_bceg (_af .Lvl4pPr ,_fbf );_eaf ._cdg [4]=_bceg (_af .Lvl5pPr ,_ae );_eaf ._cdg [5]=_bceg (_af .Lvl6pPr ,_agc );
_eaf ._cdg [6]=_bceg (_af .Lvl7pPr ,_dc );_eaf ._cdg [7]=_bceg (_af .Lvl8pPr ,_bac );_eaf ._cdg [8]=_bceg (_af .Lvl9pPr ,_dae );};func _dfggg (_cgeab int ,_dbef bool )string {_feeg :=_f .NewBuffer ([]byte {});for _ ,_gac :=range _defa {for {if _cgeab < _gac ._acea {break ;
};_feeg .WriteString (_gac ._bgcf );_cgeab -=_gac ._acea ;};};_eac :=_feeg .String ();if _dbef {_eac =_a .ToUpper (_eac );};return _eac ;};type convertContext struct{_abag *_cd .Creator ;_efca *_dd .Rectangle ;_eec *_cf .Presentation ;_fea *_cf .Slide ;
_dcaff *_ce .SldMaster ;_cfaa *_ce .SldLayout ;_feg float64 ;_bgc float64 ;_dec []_cd .Drawable ;_bgdb *background ;_baad *_bc .CT_TextParagraphProperties ;_bgga *_bc .CT_TextCharacterProperties ;_gfg *_bc .CT_TextParagraphProperties ;_bfac *_bc .CT_TextCharacterProperties ;
_fbbc *_bc .CT_TextParagraphProperties ;_dcc *_bc .CT_TextCharacterProperties ;_dcga []*_bc .CT_TextParagraphProperties ;_dgaa []*_bc .CT_TextParagraphProperties ;_cdg []*_bc .CT_TextParagraphProperties ;_cbgd *_bc .Theme ;_gce *_bc .CT_ColorMappingOverride ;
};func _bbbg (_adbg int ,_caed bool )string {_fdab :=(_adbg -1)/26+1;_bdc :=byte ((_adbg -1)%26);if _caed {_bdc +=byte (65);}else {_bdc +=byte (97);};_cbgg :=_f .NewBuffer ([]byte {});for _fgd :=0;_fgd < _fdab ;_fgd ++{_cbgg .Write ([]byte {_bdc });};return _cbgg .String ();
};func (_faf *convertContext )makePdfDivisionFromTxBody (_gdga *_bc .CT_TextBody ,_bbaf *_bc .CT_TableStyleTextStyle )*_cd .Division {_dce :=_faf ._abag .NewDivision ();_aagf :=_faf ._baad ;_gda :=_bc .ST_TextAnchoringTypeT ;if _ccea :=_gdga .BodyPr ;_ccea !=nil {if _bfdc :=_ccea .AnchorAttr ;
_bfdc !=_bc .ST_TextAnchoringTypeUnset {_gda =_ccea .AnchorAttr ;};};if _fbbd :=_gdga .LstStyle ;_fbbd !=nil {var _cddd *_bc .CT_TextParagraphProperties ;if _fbbd .Lvl1pPr !=nil {_cddd =_fbbd .Lvl1pPr ;}else {_cddd =_faf ._dcga [0];};_aagf =_bceg (_cddd ,_bceg (_fbbd .DefPPr ,_aagf ));
};for _ ,_ffbd :=range _gdga .P {if _ffbd !=nil {_cbc :=_faf ._abag .NewStyledParagraph ();_dffe :=_bceg (_ffbd .PPr ,_aagf );_ddc :=_eggge (_ffbd .EndParaRPr ,_dffe .DefRPr );switch _dffe .AlgnAttr {case _bc .ST_TextAlignTypeR :_cbc .SetTextAlignment (_cd .TextAlignmentRight );
case _bc .ST_TextAlignTypeCtr :_cbc .SetTextAlignment (_cd .TextAlignmentCenter );case _bc .ST_TextAlignTypeJust :_cbc .SetTextAlignment (_cd .TextAlignmentJustify );};if len (_ffbd .EG_TextRun )==0{_cbc .Append ("\u000a");_dce .Add (_cbc );continue ;};
for _ ,_dbg :=range _ffbd .EG_TextRun {if _ebg :=_dbg .TextRunChoice .Br ;_ebg !=nil {_cbc .Append ("\u000a");}else if _dfa :=_dbg .TextRunChoice .R ;_dfa !=nil {_edb :=_agebd (_dfa .RPr ,_bbaf );_edb =_eggge (_edb ,_ddc );var _cbg _cd .Color ;if _edb .FillPropertiesChoice .SolidFill !=nil {_cbg ,_ =_faf .getColorFromSolidFill (_edb .FillPropertiesChoice .SolidFill );
}else {_cbg =_cd .ColorBlack ;};_fcd ,_aagef ,_cbb ,_ :=_faf .makeStyleFromRPr (_edb );_fcd .Color =_cbg ;if _aagef {_fcd .TextRise =0.5;}else if _cbb {_fcd .TextRise =-0.5;};_fcgf :=_dfa .T ;if _edb .CapAttr ==_bc .ST_TextCapsTypeAll {for _ ,_aggd :=range _fcgf {_aggd =[]rune (_a .ToUpper (string (_aggd )))[0];
};};_dbee :=_cbc .Append (_fcgf );_dbee .Style =*_fcd ;};};_ =_gda ;_dce .Add (_cbc );};};return _dce ;};var _acc =_eggg (1.9);func (_bed *convertContext )addShapes (_cda *_ce .CT_CommonSlideData ,_ab bool ){if _cda ==nil {return ;};_bfd :=&background {};
if _baa :=_cda .Bg ;_baa !=nil {if _fc :=_baa .BackgroundChoice .BgPr ;_fc !=nil {if _fc .FillPropertiesChoice .NoFill ==nil {if _dca :=_fc .FillPropertiesChoice .SolidFill ;_dca !=nil {_fgc ,_dac :=_bed .getColorFromSolidFill (_dca );if _fgc !=nil {_bfd ._cdae =_fgc ;
_bfd ._dedb =_dac ;};}else if _aa :=_fc .FillPropertiesChoice .BlipFill ;_aa !=nil {_bfd ._bfc =_aa ;};};};};_bed ._bgdb =_bfd ;if _afg :=_cda .SpTree ;_afg !=nil {for _ ,_gd :=range _afg .GroupShapeChoice {if _gd !=nil {if _gd .Sp !=nil {_bag :=_bed .getShapes (_gd .Sp ,_ab ,false );
_bed ._dec =append (_bed ._dec ,_bag ...);};if _gd .GraphicFrame !=nil {var _accg ,_ad ,_fce ,_cec float64 ;if _fde :=_gd .GraphicFrame .Xfrm ;_fde !=nil {_accg ,_ad ,_fce ,_cec =_dd .GetDataFromXfrm (_fde );};if _fce ==0&&_cec ==0{_fce =_bed ._bgc ;_cec =_bed ._feg ;
};if _aae :=_gd .GraphicFrame .Graphic ;_aae !=nil {if _ga :=_aae .GraphicData ;_ga !=nil {for _ ,_bce :=range _ga .Any {if _cc ,_gf :=_bce .(*_cg .Chart );_gf {_beg ,_ebdb :=_bed .makePdfBlockFromChart (_cc ,_fce ,_cec );if _ebdb !=nil {_eb .Log .Debug ("C\u0061\u006e\u006e\u006ft \u0072e\u0061\u0064\u0020\u0062\u006co\u0063\u006b\u003a\u0020\u0025\u0073",_ebdb );
};if _beg !=nil {_beg .SetPos (_accg ,_ad );_bed ._dec =append (_bed ._dec ,_beg );};}else if _bb ,_bgge :=_bce .(*_bc .Tbl );_bgge {_ada :=_bed .makePdfBlockFromTable (_bb );if _ada !=nil {_df :=_cd .NewBlock (_fce ,_cec );_df .SetPos (_accg ,_ad );_afe :=_df .Draw (_ada );
if _afe !=nil {_eb .Log .Debug ("C\u0061\u006e\u006e\u006ft \u0064r\u0061\u0077\u0020\u0074\u0061b\u006c\u0065\u003a\u0020\u0025\u0073",_afe );if _afe ==_cd .ErrContentNotFit {_df =_cd .NewBlock (_bed ._bgc -1.5*_accg ,_bed ._feg -1.5*_ad );_df .SetPos (_accg ,_ad );
_afe =_df .Draw (_ada );};};if _afe ==nil {_bed ._dec =append (_bed ._dec ,_df );};};};};};};};if _gd .CxnSp !=nil {_gcc :=_bed .getConnectors (_gd .CxnSp );_bed ._dec =append (_bed ._dec ,_gcc ...);};if _gd .GrpSp !=nil {_fe :=0.0;_gdg :=0.0;if _bcc :=_gd .GrpSp .GrpSpPr .Xfrm ;
_bcc !=nil {_fe ,_gdg =_dd .GetGroupOffsetFromXfrm (_bcc );};for _ ,_cca :=range _gd .GrpSp .GroupShapeChoice {if _cca .CxnSp !=nil {_ec :=_bed .getGroupConnectors (_cca .CxnSp ,_fe ,_gdg );_bed ._dec =append (_bed ._dec ,_ec ...);};};};if _gd .Pic !=nil {_bfdb :=false ;
var _ecf ,_dfg ,_bfe ,_dab float64 ;if _afa :=_gd .Pic .SpPr ;_afa !=nil {if _dcf :=_afa .Xfrm ;_dcf !=nil {_ecf ,_dfg ,_bfe ,_dab =_dd .GetDataFromXfrm (_dcf );_bfdb =true ;};};var _ed _ce .ST_PlaceholderType ;var _bbf *uint32 ;if _ecff :=_gd .Pic .NvPicPr ;
_ecff !=nil {if _dde :=_ecff .NvPr ;_dde !=nil {if _gbb :=_dde .Ph ;_gbb !=nil {_ed =_gbb .TypeAttr ;_bbf =_gbb .IdxAttr ;};};};_dcff ,_ ,_ ,_ ,_ :=_ecge (_bed ._dcaff .CSld ,_ed ,_bbf );_fda ,_ ,_ ,_ ,_ :=_ecge (_bed ._cfaa .CSld ,_ed ,_bbf );if _fda ==nil {_fda =_dcff ;
};if _fda !=nil &&!_bfdb {_ecf ,_dfg ,_bfe ,_dab =_dd .GetDataFromXfrm (_fda );};if _gdgd :=_gd .Pic .BlipFill ;_gdgd !=nil {_dcaf :=_bed .getShapeFromBlipFill (_gdgd ,_ecf ,_dfg ,_bfe ,_dab ,_ab );_bed ._dec =append (_bed ._dec ,_dcaf );};};};};};};func (_bba *convertContext )getConnectors (_cecb *_ce .CT_Connector )[]_cd .Drawable {_egdb ,_ ,_ ,_ ,_ ,_ ,_ :=_bba .getShapesFromSpPr (_cecb .SpPr ,_cecb .Style ,false ,0.0,0.0);
return _egdb ;};func (_cecf *convertContext )getShapes (_bge *_ce .CT_Shape ,_ddaa bool ,_eda bool )[]_cd .Drawable {_aeeb :=[]_cd .Drawable {};_cce :=_bge .SpPr ;if _cce ==nil {return _aeeb ;};var _cfd bool ;if _bgeg :=_bge .UseBgFillAttr ;_bgeg !=nil {_cfd =*_bgeg ;
};_gde ,_aabbd ,_dacc ,_adg ,_gge ,_bbeg ,_cfg :=_cecf .getShapesFromSpPr (_cce ,_bge .Style ,_cfd ,0.0,0.0);if !_eda {_aeeb =append (_aeeb ,_gde ...);};if _cdc :=_bge .TxBody ;_cdc !=nil &&!_ddaa {_egfb ,_bacf ,_afeg ,_dbcc ,_eae ,_bdfb :=_cecf .getPhData (_bge );
if _eda &&!(_dbcc ||_eae ){return _aeeb ;};if _egfb !=nil &&!_cfg {_aabbd ,_dacc ,_adg ,_gge =_dd .GetDataFromXfrm (_egfb );};_ecg ,_fa :=_cecf .makePdfBlockFromTxBody (_cdc ,_bacf ,_afeg ,_adg ,_gge ,_bbeg ,_dbcc ,_bdfb );if _fa !=nil {_eb .Log .Debug ("\u0043\u0061\u006e\u006e\u006f\u0074\u0020\u006d\u0061\u006b\u0065\u0020\u0050\u0044\u0046\u0020\u0062\u006c\u006f\u0063\u006b\u0020\u0066\u0072o\u006d\u0020\u0074\u0065\u0078t\u0062\u006fx\u003a\u0020\u0025\u0073",_fa );
}else if _ecg !=nil {_ecg .SetPos (_aabbd ,_dacc );_aeeb =append (_aeeb ,_ecg );};};return _aeeb ;};func (_ebfb *convertContext )makePdfImageFromBlipFill (_adac *_bc .CT_BlipFillProperties ,_ggcg bool )(*_cd .Image ,[]*_bc .CT_BlipChoice ,error ){if _gccb :=_adac .Blip ;
_gccb !=nil {if _dccg :=_gccb .EmbedAttr ;_dccg !=nil {var _acbfd _ef .ImageRef ;var _bdac bool ;if _ggcg {_acbfd ,_bdac =_ebfb ._fea .GetLayoutImageByRelID (*_dccg );}else {_acbfd ,_bdac =_ebfb ._fea .GetImageByRelID (*_dccg );};if _bdac {_dffb ,_dbfe :=_dg .Open (_acbfd .Path ());
if _dbfe !=nil {_eb .Log .Debug ("\u0046\u0069\u006c\u0065 o\u0070\u0065\u006e\u0020\u0065\u0072\u0072\u006f\u0072\u003a\u0020\u0025\u0073",_dbfe );return nil ,nil ,_dbfe ;};defer _dffb .Close ();_geee ,_ ,_dbfe :=_bg .Decode (_dffb );if _dbfe !=nil {_eb .Log .Debug ("\u0044\u0065\u0063\u006fde\u0020\u0069\u006d\u0061\u0067\u0065\u0020\u0065\u0072\u0072\u006f\u0072\u003a\u0020%\u0073",_dbfe );
return nil ,nil ,_dbfe ;};if _bbdg :=_adac .SrcRect ;_bbdg !=nil {_eaac :=_geee .Bounds ().Size ();_ebef :=_eaac .X ;_eage :=_eaac .Y ;var _acbfc ,_fbe ,_fac ,_dgbdg int ;var _egggd bool ;if _ggdc :=_bbdg .LAttr ;_ggdc !=nil {_acbfc =int (float64 (_ebef )*_dd .FromSTPercentage (_ggdc ));
_egggd =true ;}else {_acbfc =0;};if _acga :=_bbdg .TAttr ;_acga !=nil {_fac =int (float64 (_eage )*_dd .FromSTPercentage (_acga ));_egggd =true ;}else {_fac =0;};if _cecc :=_bbdg .RAttr ;_cecc !=nil {_fbe =int (float64 (_ebef )*(1-_dd .FromSTPercentage (_cecc )));
_egggd =true ;}else {_fbe =_ebef ;};if _gfb :=_bbdg .BAttr ;_gfb !=nil {_dgbdg =int (float64 (_eage )*(1-_dd .FromSTPercentage (_gfb )));_egggd =true ;}else {_dgbdg =_eage ;};if _egggd {_geee =_dd .CropImageByRect (_geee ,_bg .Rect (_acbfc ,_fac ,_fbe +1,_dgbdg +1));
};};_geca ,_dbfe :=_ebfb ._abag .NewImageFromGoImage (_geee );_geca .SetEncoder (_bf .NewFlateEncoder ());if _a .ToLower (_acbfd .Format ())=="\u006a\u0070\u0067"||_a .ToLower (_acbfd .Format ())=="\u006a\u0070\u0065\u0067"{_geca .SetEncoder (_bf .NewDCTEncoder ());
};if _dbfe !=nil {_eb .Log .Debug ("\u0043\u0061\u006e\u006e\u006ft\u0020\u0063\u0072\u0065\u0061\u0074\u0065\u0020\u0050\u0044\u0046\u0020\u0069m\u0061\u0067\u0065\u0020\u0066\u0072\u006f\u006d\u0020\u0047\u006f\u0020\u0069\u006d\u0061\u0067\u0065\u003a\u0020\u0025\u0073",_dbfe );
return nil ,nil ,_dbfe ;};return _geca ,_gccb .BlipChoice ,nil ;};};};return nil ,nil ,nil ;};func (_gcf *convertContext )drawSlide (){_gcf ._abag .NewPage ();for _ ,_ee :=range _gcf ._dec {if _ee !=nil {_gcf ._abag .MoveTo (0,0);_gcf ._abag .Draw (_ee );
};};};func _eggg (_acgf float64 )float64 {return _acgf *_ac .Millimeter };func (_fgff *convertContext )getColorFromFontReference (_gfda *_bc .CT_FontReference )_cd .Color {var _dcfg _cd .Color ;var _fdca string ;if _afdd :=_gfda .SrgbClr ;_afdd !=nil {_fdca =_afdd .ValAttr ;
}else if _aaab :=_gfda .SchemeClr ;_aaab !=nil {_fdca =_dd .GetColorStringFromDmlColor (_fgff ._eec .GetColorBySchemeColor (_aaab .ValAttr ));_fdca =_dd .AdjustColor (_fdca ,_aaab .EG_ColorTransform );};if _fdca !=""{_dcfg =_cd .ColorRGBFromHex ("\u0023"+_fdca );
};return _dcfg ;};func (_cggde *textboxContext )drawParagraphs (){_cggde ._ccc .NewPage ();for _ ,_egag :=range _cggde ._cac {for _ ,_cede :=range _egag ._dadf {for _ ,_agedd :=range _cede ._dbfc {for _ ,_eebd :=range _agedd ._eedb {_afdf :=_cggde ._ccc .NewStyledParagraph ();
if _eebd ._fae {_eebd ._gdda =0;}else if _eebd ._ddg {_eebd ._gdda =1.2*_cede ._fege -_eebd ._bgdf ;};_cceef :=_agedd ._bgef +_eebd ._fbbg ;_afege :=_egag ._bdd +_cede ._bcfe +_eebd ._gdda ;_afdf .SetPos (_cceef ,_afege );_gbffd :=_afdf .Append (_eebd ._cfgb );
if _eebd ._def !=nil {_gbffd .Style =*_eebd ._def ;};_cggde ._ccc .Draw (_afdf );if _eebd ._dfd {_adab :=_afege +_eebd ._bgdf +2;_dd .DrawLine (_cggde ._ccc ,_cceef ,_adab ,_cceef +_eebd ._fcda ,_adab ,1,_eebd ._def .Color );};};};};};};func (_edd *convertContext )makeSlide (){_aag :=_edd ._fea .GetSlideLayout ().CSld ;
_edd .addShapes (_aag ,true );_aag =_edd ._fea .X ().CSld ;_edd .addShapes (_aag ,false );};func _cea (_cefc *_bc .CT_TableCellProperties ,_edbg *_bc .CT_TableStyleCellStyle ,_bddf ,_bbgg ,_acad ,_fdcf bool )*_bc .CT_TableCellProperties {_ddae :=_bc .NewCT_TableCellProperties ();
if _cefc !=nil {*_ddae =*_cefc ;};if _edbg ==nil {return _ddae ;};if _afbe :=_edbg .ThemeableFillStyleChoice .FillRef ;_afbe !=nil {_efg :=_bc .NewCT_SolidColorFillProperties ();_efg .ScrgbClr =_afbe .ScrgbClr ;_efg .SrgbClr =_afbe .SrgbClr ;_efg .HslClr =_afbe .HslClr ;
_efg .SysClr =_afbe .SysClr ;_efg .SchemeClr =_afbe .SchemeClr ;_efg .PrstClr =_afbe .PrstClr ;_ddae .FillPropertiesChoice .SolidFill =_efg ;};if _ddae .FillPropertiesChoice .NoFill ==nil &&_ddae .FillPropertiesChoice .SolidFill ==nil {if _gdae :=_edbg .ThemeableFillStyleChoice .Fill ;
_gdae !=nil {if _ddae .FillPropertiesChoice .NoFill ==nil {_ddae .FillPropertiesChoice .NoFill =_gdae .FillPropertiesChoice .NoFill ;};if _ddae .FillPropertiesChoice .SolidFill ==nil {_ddae .FillPropertiesChoice .SolidFill =_gdae .FillPropertiesChoice .SolidFill ;
};};};if _fead :=_edbg .TcBdr ;_fead !=nil {if _ddae .LnL ==nil {var _faa *_bc .CT_ThemeableLineStyle ;if _acad {_faa =_fead .Left ;}else {_faa =_fead .InsideV ;};if _faa !=nil {_ddae .LnL =_faa .ThemeableLineStyleChoice .Ln ;};};if _ddae .LnR ==nil {var _fgef *_bc .CT_ThemeableLineStyle ;
if _fdcf {_fgef =_fead .Right ;}else {_fgef =_fead .InsideV ;};if _fgef !=nil {_ddae .LnR =_fgef .ThemeableLineStyleChoice .Ln ;};};if _ddae .LnT ==nil {var _eabfa *_bc .CT_ThemeableLineStyle ;if _bddf {_eabfa =_fead .Top ;}else {_eabfa =_fead .InsideH ;
};if _eabfa !=nil {_ddae .LnT =_eabfa .ThemeableLineStyleChoice .Ln ;};};if _ddae .LnB ==nil {var _adgc *_bc .CT_ThemeableLineStyle ;if _bbgg {_adgc =_fead .Bottom ;}else {_adgc =_fead .InsideH ;};if _adgc !=nil {_ddae .LnB =_adgc .ThemeableLineStyleChoice .Ln ;
};};};return _ddae ;};func (_fad *convertContext )getPhData (_dgc *_ce .CT_Shape )(*_bc .CT_Transform2D ,*_bc .CT_TextBodyProperties ,*_bc .CT_TextListStyle ,bool ,bool ,bool ){_egggf ,_dfc :=_fcfa (_dgc );_bgfd ,_dbec ,_cdaf ,_cgag ,_fdag :=_ecge (_fad ._dcaff .CSld ,_egggf ,_dfc );
_cgfa ,_bccb ,_edgd ,_baed ,_cebe :=_ecge (_fad ._cfaa .CSld ,_egggf ,_dfc );if _cgfa ==nil {_cgfa =_bgfd ;};_dcbcd ,_fdea :=_efeag (_bccb ,_dbec );var _ffbc ,_fcf ,_gegf bool ;if _edgd ==nil {if _cdaf !=nil {_ffbc =*_cdaf ;};}else {_ffbc =*_edgd ;};if _cebe ==nil {if _fdag !=nil {_gegf =*_fdag ;
};}else {_gegf =*_cebe ;};if _baed ==nil {if _cgag !=nil {_fcf =*_cgag ;};}else {_fcf =*_baed ;};return _cgfa ,_dcbcd ,_fdea ,_ffbc ,_fcf ,_gegf ;};func _agebd (_gdgdg *_bc .CT_TextCharacterProperties ,_debb *_bc .CT_TableStyleTextStyle )*_bc .CT_TextCharacterProperties {_ebfga :=_bc .NewCT_TextCharacterProperties ();
if _gdgdg !=nil {*_ebfga =*_gdgdg ;};if _debb ==nil {return _ebfga ;};if _ebfga .BAttr ==nil &&_debb .BAttr !=_bc .ST_OnOffStyleTypeUnset {_ebgg :=_debb .BAttr ==_bc .ST_OnOffStyleTypeOn ;_ebfga .BAttr =&_ebgg ;};if _ebfga .IAttr ==nil &&_debb .IAttr !=_bc .ST_OnOffStyleTypeUnset {_bea :=_debb .IAttr ==_bc .ST_OnOffStyleTypeOn ;
_ebfga .IAttr =&_bea ;};if _ebfga .FillPropertiesChoice .NoFill ==nil &&_ebfga .FillPropertiesChoice .SolidFill ==nil {_ebfga .FillPropertiesChoice .SolidFill =_bc .NewCT_SolidColorFillProperties ();_ebfga .FillPropertiesChoice .SolidFill .ScrgbClr =_debb .ScrgbClr ;
_ebfga .FillPropertiesChoice .SolidFill .SrgbClr =_debb .SrgbClr ;_ebfga .FillPropertiesChoice .SolidFill .HslClr =_debb .HslClr ;_ebfga .FillPropertiesChoice .SolidFill .SysClr =_debb .SysClr ;_ebfga .FillPropertiesChoice .SolidFill .SchemeClr =_debb .SchemeClr ;
_ebfga .FillPropertiesChoice .SolidFill .PrstClr =_debb .PrstClr ;};if _dccf :=_debb .ThemeableFontStylesChoice .Font ;_dccf !=nil &&_ebfga .Latin ==nil &&_ebfga .Ea ==nil &&_ebfga .Cs ==nil {_ebfga .Latin =_dccf .Latin ;_ebfga .Ea =_dccf .Ea ;_ebfga .Cs =_dccf .Cs ;
};return _ebfga ;};func _abc (_fcef *bool )bool {return _fcef !=nil &&*_fcef };func (_afgg *convertContext )getShapesFromSpPr (_bbfc *_bc .CT_ShapeProperties ,_gee *_bc .CT_ShapeStyle ,_fga bool ,_acd float64 ,_gef float64 )([]_cd .Drawable ,float64 ,float64 ,float64 ,float64 ,_cd .Color ,bool ){_afd :=[]_cd .Drawable {};
var _dff ,_cbe ,_bbcc ,_ddf ,_egdc float64 ;var _cceb ,_adf ,_ege ,_gbf _cd .Color ;var _bbg *_bc .CT_BlipFillProperties ;_gfa ,_efcf :=1.0,1.0;if _gee !=nil {_cceb ,_adf ,_gbf =_afgg .getStyleColors (_gee );};if _bcaa :=_bbfc .Ln ;_bcaa !=nil {if _bcaa .LineFillPropertiesChoice .NoFill !=nil {_ege ,_egdc =nil ,0;
}else {_ege ,_egdc ,_gfa =_afgg .getInfoFromLn (_bcaa );if _ege ==nil {_ege =_gbf ;};};};if _bbfc .FillPropertiesChoice .NoFill !=nil {_adf ,_efcf =nil ,0;}else if _fga {_adf =_afgg ._bgdb ._cdae ;_efcf =_afgg ._bgdb ._dedb ;_bbg =_afgg ._bgdb ._bfc ;}else if _ffba :=_bbfc .FillPropertiesChoice .SolidFill ;
_ffba !=nil {_adf ,_efcf =_afgg .getColorFromSolidFill (_ffba );};var _cag bool ;if _efcb :=_bbfc .Xfrm ;_efcb !=nil {_dff ,_cbe ,_bbcc ,_ddf =_dd .GetDataFromXfrm (_efcb );_dff +=_acd ;_cbe +=_gef ;_cag =true ;};if _bfec :=_bbfc .GeometryChoice .CustGeom ;
_bfec !=nil {_ega :=[]_db .Point {};_bff ,_efb :=1.0,1.0;if _cae :=_bfec .PathLst ;_cae !=nil {for _ ,_afga :=range _cae .Path {if _afga !=nil {if _ebe :=_afga .WAttr ;_ebe !=nil {_bff =_bbcc /_ac .FromEMU (*_ebe );};if _ged :=_afga .HAttr ;_ged !=nil {_efb =_ddf /_ac .FromEMU (*_ged );
};for _ ,_bcf :=range _afga .Path2DChoice {if _bcf .Close !=nil {};if _fcg :=_bcf .MoveTo ;_bcf .MoveTo !=nil {if _fcg !=nil &&_fcg .Pt !=nil {_eaff ,_egae :=_cfdbd (_fcg .Pt );_ega =append (_ega ,_db .Point {X :_eaff *_bff +_dff ,Y :_egae *_efb +_cbe });
};};if _egc :=_bcf .LnTo ;_egc !=nil &&_egc .Pt !=nil {_bgee ,_dga :=_cfdbd (_egc .Pt );_ega =append (_ega ,_db .Point {X :_bgee *_bff +_dff ,Y :_dga *_efb +_cbe });};};_egg :=_afgg ._abag .NewPolygon ([][]_db .Point {_ega });_egg .SetFillColor (_adf );
_egg .SetFillOpacity (_efcf );_egg .SetBorderWidth (_egdc );if _ege !=nil {_egg .SetBorderColor (_ege );_egg .SetBorderOpacity (_gfa );};_afd =append (_afd ,_egg );};};};};if _deb :=_bbfc .GeometryChoice .PrstGeom ;_deb !=nil {switch _deb .PrstAttr {case _bc .ST_ShapeTypeRect :if _bbg !=nil {_fdc :=_afgg .getShapeFromBlipFill (_bbg ,_dff ,_cbe ,_bbcc ,_ddf ,false );
_afd =append (_afd ,_fdc );}else {_gbfd :=_afgg ._abag .NewRectangle (_dff ,_cbe ,_bbcc ,_ddf );_ggda :=_adf !=nil &&_efcf > 0;if _ggda {_gbfd .SetFillColor (_adf );_gbfd .SetFillOpacity (_efcf );};_dbb :=_ege !=nil &&_gfa > 0&&_egdc > 0;if _dbb {_gbfd .SetBorderWidth (_egdc );
_gbfd .SetBorderColor (_ege );_gbfd .SetBorderOpacity (_gfa );};if _ggda ||_dbb {_afd =append (_afd ,_gbfd );};};case _bc .ST_ShapeTypeLine :_gdcd :=_afgg ._abag .NewLine (_dff ,_cbe ,_dff +_bbcc ,_cbe +_ddf );_gdcd .SetLineWidth (_egdc );if _ege !=nil {_gdcd .SetColor (_ege );
};_afd =append (_afd ,_gdcd );};};return _afd ,_dff ,_cbe ,_bbcc ,_ddf ,_cceb ,_cag ;};func (_ageb *convertContext )getGroupConnectors (_cga *_ce .CT_Connector ,_ddab float64 ,_dfb float64 )[]_cd .Drawable {_ggd ,_ ,_ ,_ ,_ ,_ ,_ :=_ageb .getShapesFromSpPr (_cga .SpPr ,_cga .Style ,false ,_ddab ,_dfb );
return _ggd ;};func (_dedd *convertContext )makePdfBlockFromTable (_cafa *_bc .Tbl )*_cd .Table {_aggf :=_cafa .TblGrid ;if _aggf ==nil {return nil ;};_egbc :=len (_aggf .GridCol );if _egbc ==0{return nil ;};_ade :=[]float64 {};_gag :=0.0;for _ ,_bgdg :=range _aggf .GridCol {_dfae :=_ac .FromEMU (_dd .FromSTCoordinate (_bgdg .WAttr ));
_ade =append (_ade ,_dfae );_gag +=_dfae ;};_fbbb :=[]float64 {};for _bdb :=0;_bdb < _egbc ;_bdb ++{_fbbb =append (_fbbb ,_ade [_bdb ]/_gag );};_dceb :=_dedd ._abag .NewTable (_egbc );_dceb .SetColumnWidths (_fbbb ...);_cgac :=_cafa .TblPr ;var _aed *_bc .CT_TableStyle ;
if _cedg :=_cgac .TablePropertiesChoice ;_cedg !=nil {if _cedg .TableStyle !=nil {_aed =_cedg .TableStyle ;}else if _cedg .TableStyleId !=nil {_aed =_dedd ._eec .GetTableStyleById (*_cedg .TableStyleId );};};_dgf :=_bc .NewCT_TablePartStyle ();_dgf .TcStyle =_bc .NewCT_TableStyleCellStyle ();
_dgf .TcTxStyle =_bc .NewCT_TableStyleTextStyle ();if _aed !=nil {if _aed .WholeTbl !=nil {*_dgf =*_aed .WholeTbl ;};if _aed .TblBg !=nil {if _dgf .TcStyle .ThemeableFillStyleChoice .Fill ==nil {_dgf .TcStyle .ThemeableFillStyleChoice .Fill =_aed .TblBg .ThemeableFillStyleChoice .Fill ;
};};};if _dgf .TcStyle .ThemeableFillStyleChoice .Fill ==nil {_dgf .TcStyle .ThemeableFillStyleChoice .Fill =_bc .NewCT_FillProperties ();_dgf .TcStyle .ThemeableFillStyleChoice .Fill .FillPropertiesChoice .NoFill =_cgac .FillPropertiesChoice .NoFill ;
_dgf .TcStyle .ThemeableFillStyleChoice .Fill .FillPropertiesChoice .SolidFill =_cgac .FillPropertiesChoice .SolidFill ;};_aff :=len (_cafa .Tr );_eba :=_cafa .TblPr .BandRowAttr !=nil &&*_cafa .TblPr .BandRowAttr ;_dace :=_cafa .TblPr .FirstRowAttr !=nil &&*_cafa .TblPr .FirstRowAttr ;
_geeb :=_cafa .TblPr .LastRowAttr !=nil &&*_cafa .TblPr .LastRowAttr ;_fgag :=_cafa .TblPr .BandColAttr !=nil &&*_cafa .TblPr .BandColAttr ;_afdc :=_cafa .TblPr .FirstColAttr !=nil &&*_cafa .TblPr .FirstColAttr ;_aaf :=_cafa .TblPr .LastColAttr !=nil &&*_cafa .TblPr .LastColAttr ;
for _edaf ,_egda :=range _cafa .Tr {_ddfe :=_edaf ==0;_eee :=_edaf ==_aff -1;_agbg :=_edaf %2==0;_fcec :=len (_egda .Tc );var _ffc *_bc .CT_TablePartStyle ;if _aed !=nil {if _ddfe &&_dace {_ffc =_aed .FirstRow ;}else if _eee &&_geeb {_ffc =_aed .LastRow ;
}else if _agbg &&_eba {_ffc =_aed .Band2H ;}else if _eba {_ffc =_aed .Band1H ;};};var _gfac float64 ;for _ceb ,_cgdg :=range _egda .Tc {_dcd :=_ceb ==0;_cfea :=_ceb ==_fcec -1;_bfgc :=_ceb %2==0;var _efe *_bc .CT_TablePartStyle ;if _aed !=nil {if _dcd &&_afdc {_efe =_aed .FirstCol ;
}else if _cfea &&_aaf {_efe =_aed .LastCol ;}else if _bfgc &&_fgag {_efe =_aed .Band2V ;}else if _fgag {_efe =_aed .Band1V ;};};_egdbb :=_dbcbc (_dbcbc (_efe ,_ffc ),_dgf );_eagg :=_dedd .addCellToTable (_dceb ,_cgdg ,_egdbb ,_ddfe ,_eee ,_dcd ,_cfea );
if _eagg > _gfac {_gfac =_eagg ;};};_eef :=_ac .FromEMU (_dd .FromSTCoordinate (_egda .HAttr ));if _eef < _gfac {_eef =_gfac ;};if _eef < _eggg (4){_eef =_eggg (4);};_dceb .SetRowHeight (_dceb .CurRow (),_eef );};return _dceb ;};func (_bgbb *convertContext )addCellToTable (_cee *_cd .Table ,_gagg *_bc .CT_TableCell ,_cdb *_bc .CT_TablePartStyle ,_gbff ,_dge ,_deeg ,_ccbc bool )float64 {var _cde *_cd .TableCell ;
_ebgf :=1;if _gagg .GridSpanAttr !=nil {_ebgf =int (*_gagg .GridSpanAttr );};_cde =_cee .MultiColCell (_ebgf );_bgdc :=_gagg .TcPr ;var _cbbf *_bc .CT_TableStyleTextStyle ;if _cdb !=nil {_bgdc =_cea (_bgdc ,_cdb .TcStyle ,_gbff ,_dge ,_deeg ,_ccbc );_cbbf =_cdb .TcTxStyle ;
};_dcgg :=_acc ;_cddc :=_cd .CellVerticalAlignmentMiddle ;_bcde :=_eceb (0.05);_afab :=_eceb (0.05);if _bgdc !=nil {if _bgegf :=_bgdc .LnL ;_bgegf !=nil {_bgbg ,_cbce ,_cggd :=_bgbb .getBorderStyle (_bgegf );_cde .SetBorder (_cd .CellBorderSideLeft ,_bgbg ,_cggd );
if _cbce !=nil &&*_cbce !=nil {_cde .SetSideBorderColor (_cd .CellBorderSideLeft ,*_cbce );};};if _beb :=_bgdc .LnT ;_beb !=nil {_afde ,_eff ,_aba :=_bgbb .getBorderStyle (_beb );_cde .SetBorder (_cd .CellBorderSideTop ,_afde ,_aba );if _eff !=nil &&*_eff !=nil {_cde .SetSideBorderColor (_cd .CellBorderSideTop ,*_eff );
};};if _dbea :=_bgdc .LnR ;_dbea !=nil {_afdg ,_fceba ,_cafb :=_bgbb .getBorderStyle (_dbea );_cde .SetBorder (_cd .CellBorderSideRight ,_afdg ,_cafb );if _fceba !=nil &&*_fceba !=nil {_cde .SetSideBorderColor (_cd .CellBorderSideRight ,*_fceba );};};if _gdb :=_bgdc .LnB ;
_gdb !=nil {_acdg ,_dgdeg ,_ffbed :=_bgbb .getBorderStyle (_gdb );_cde .SetBorder (_cd .CellBorderSideBottom ,_acdg ,_ffbed );if _dgdeg !=nil &&*_dgdeg !=nil {_cde .SetSideBorderColor (_cd .CellBorderSideBottom ,*_dgdeg );};};if _beca :=_bgdc .MarLAttr ;
_beca !=nil {_dcgg =float64 (_dd .FromSTCoordinate32 (*_beca ));};switch _bgdc .AnchorAttr {case _bc .ST_TextAnchoringTypeT :_cddc =_cd .CellVerticalAlignmentTop ;case _bc .ST_TextAnchoringTypeB :_cddc =_cd .CellVerticalAlignmentBottom ;};if _bgdc .FillPropertiesChoice .NoFill ==nil {if _bafd :=_bgdc .FillPropertiesChoice .SolidFill ;
_bafd !=nil {_gbc ,_ :=_bgbb .getColorFromSolidFill (_bafd );_cde .SetBackgroundColor (_gbc );};};if _feee :=_bgdc .MarBAttr ;_feee !=nil {_bcde =float64 (_dd .FromSTCoordinate32 (*_feee ));};if _aaag :=_bgdc .MarTAttr ;_aaag !=nil {_afab =float64 (_dd .FromSTCoordinate32 (*_aaag ));
};};_cde .SetVerticalAlignment (_cddc );_cde .SetIndent (_dcgg );var _aafc float64 ;if _dcbc :=_gagg .TxBody ;_dcbc !=nil {_adge :=_bgbb .makePdfDivisionFromTxBody (_dcbc ,_cbbf );_aafc =_adge .Height ()+_afab +_bcde ;_cde .SetContent (_adge );};return _aafc ;
};func (_cgbc *textboxContext )addCurrentParagraph (){_cgbc ._gga =_cgbc ._egaf ._bdd +_cgbc ._egaf ._ccdf +_cgbc ._egaf ._ecc +_cgbc ._egaf ._cfc ;_cgbc ._cac =append (_cgbc ._cac ,_cgbc ._egaf );_cgbc .alignParagraph ();};func (_fegf *textboxContext )assignPropsToCurrentParagraph (_gafg *_bc .CT_TextParagraphProperties ){_bee :=12.4;
if _gafg ==nil {_fegf ._egaf ._bgde =_bee ;return ;};if _dcdc :=_gafg .DefRPr ;_dcdc !=nil {_cdfb :=_dcdc .SzAttr ;if _cdfb !=nil {_fec :=float64 (*_cdfb )/1200;if _bee <=_fec {_bee =_fec ;};};};if _cfcf :=_gafg .MarLAttr ;_cfcf !=nil {_fegf ._egaf ._gbee =_ac .FromEMU (int64 (*_cfcf ));
};_fegf ._egaf ._fffe =_fegf ._dgfd ;if _fgca :=_gafg .MarRAttr ;_fgca !=nil {_fegf ._egaf ._fffe -=_ac .FromEMU (int64 (*_fgca ));};if _ccfg :=_gafg .IndentAttr ;_ccfg !=nil {_fegf ._egaf ._adee =_ac .FromEMU (int64 (*_ccfg ));};if _dgbd :=_gafg .LatinLnBrkAttr ;
_dgbd !=nil {_fegf ._egaf ._bfff =*_dgbd ;};if _dafa :=_gafg .LnSpc ;_dafa !=nil &&_dafa .TextSpacingChoice !=nil {if _ddac :=_dafa .TextSpacingChoice .SpcPct ;_ddac !=nil {if _ead :=_ddac .ValAttr .ST_TextSpacingPercent ;_ead !=nil {_bee =float64 (*_ead )/5000;
};};};var _gfc float64 ;if _bcaad :=_gafg .SpcBef ;_bcaad !=nil &&_bcaad .TextSpacingChoice !=nil {if _fged :=_bcaad .TextSpacingChoice .SpcPts ;_fged !=nil {_gfc =float64 (_fged .ValAttr )/100;};};_adcd :=_fegf ._cac ;if len (_adcd )> 0{_gfc -=_adcd [len (_adcd )-1]._cfc ;
if _gfc < 0{_gfc =0;};};_fegf ._egaf ._ccdf =_gfc ;if _bfed :=_gafg .SpcAft ;_bfed !=nil &&_bfed .TextSpacingChoice !=nil {if _bgdgf :=_bfed .TextSpacingChoice .SpcPts ;_bgdgf !=nil {_fegf ._egaf ._cfc =float64 (_bgdgf .ValAttr )/100;};};_fegf ._egaf ._bgde =_bee ;
_fegf ._egaf ._fcb =_gafg .AlgnAttr ;};type paragraph struct{_dfac float64 ;_ccdf float64 ;_cfc float64 ;_adee float64 ;_gbee float64 ;_fffe float64 ;_bdd float64 ;_ecc float64 ;_fcb _bc .ST_TextAlignType ;_bgde float64 ;_bfff bool ;_dadf []*line ;};func (_afc *convertContext )getColorFromSolidFill (_egea *_bc .CT_SolidColorFillProperties )(_cd .Color ,float64 ){if _egea ==nil {return nil ,1;
};var _adff string ;_aabbg :=1.0;if _dcee :=_egea .SrgbClr ;_dcee !=nil {_adff =_dcee .ValAttr ;_aabbg =_dd .GetOpacityFromColorTransform (_dcee .EG_ColorTransform );}else if _cedeg :=_egea .SchemeClr ;_cedeg !=nil {_adff =_dd .GetColorStringFromDmlColor (_afc ._fea .GetColorBySchemeColor (_cedeg .ValAttr ));
_adff =_dd .AdjustColor (_adff ,_cedeg .EG_ColorTransform );_aabbg =_dd .GetOpacityFromColorTransform (_cedeg .EG_ColorTransform );};if _adff !=""{_eaa :=_cd .ColorRGBFromHex ("\u0023"+_adff );return _eaa ,_aabbg ;};return nil ,1;};func (_eagga *textboxContext )newLine (){if _eagga ._egaf ==nil {_eagga .newParagraph ();
};_fdd :=_eagga ._egaf ._ecc +_eagga ._egaf ._ccdf ;_dfgb :=&line {};_dfgb ._dgbe =_eagga ._egaf ._gbee ;if len (_eagga ._egaf ._dadf )==0{_dfgb ._dgbe +=_eagga ._egaf ._adee ;};_dfgb ._ecb =_eagga ._egaf ._fffe ;_dfgb ._cdga =_dfgb ._dgbe ;_dfgb ._bcfe =_fdd ;
_eagga ._egaf ._dadf =append (_eagga ._egaf ._dadf ,_dfgb );_eagga ._gcfa =_dfgb ;};type line struct{_bcfe float64 ;_dgbe float64 ;_ecb float64 ;_cdga float64 ;_fege float64 ;_dbfc []*word ;};func _fcfa (_gbcaa *_ce .CT_Shape )(_ce .ST_PlaceholderType ,*uint32 ){if _bfbe :=_gbcaa .NvSpPr ;
_bfbe !=nil {if _dafaa :=_bfbe .NvPr ;_dafaa !=nil {if _dgbg :=_dafaa .Ph ;_dgbg !=nil {return _dgbg .TypeAttr ,_dgbg .IdxAttr ;};};};return _ce .ST_PlaceholderTypeUnset ,nil ;};func (_egge *textboxContext )alignParagraph (){_gcg :=_egge ._egaf ;if _gcg ._fcb ==_bc .ST_TextAlignTypeL {return ;
};_dddg :=len (_gcg ._dadf )-1;for _gcge ,_gddac :=range _gcg ._dadf {_fgad :=true ;_aagb :=len (_gddac ._dbfc );_gecg :=0.0;for _gffc :=len (_gddac ._dbfc )-1;_gffc >=0;_gffc --{_aca :=_gddac ._dbfc [_gffc ];if _fgad &&_aca ._bgdgg {_aagb =_gffc ;}else {_fgad =false ;
for _ ,_efbd :=range _aca ._eedb {_gecg +=_efbd ._fcda ;};};};_gddac ._dbfc =_gddac ._dbfc [:_aagb ];_eggc :=_gddac ._ecb -_gddac ._dgbe -_gecg ;switch _gcg ._fcb {case _bc .ST_TextAlignTypeR :_gddac .moveRight (_eggc );case _bc .ST_TextAlignTypeCtr :_gddac .moveRight (_eggc /2);
case _bc .ST_TextAlignTypeJust :if _gcge !=_dddg {_bcfd :=[]*word {};for _ ,_bgf :=range _gddac ._dbfc {if _bgf ._bgdgg {_bcfd =append (_bcfd ,_bgf );};};_ecbc :=_eggc /float64 (len (_bcfd ));for _ ,_ggc :=range _bcfd {_ggc ._begf +=_ecbc ;};var _cgfc *word ;
for _ ,_fcbf :=range _gddac ._dbfc {if _cgfc !=nil {_fcbf ._bgef =_cgfc ._bgef +_cgfc ._begf ;};_cgfc =_fcbf ;};};};};};func _ccegd (_agge ,_cdad *_bc .CT_TableStyleCellStyle )*_bc .CT_TableStyleCellStyle {_dgfac :=_bc .NewCT_TableStyleCellStyle ();if _agge !=nil {*_dgfac =*_agge ;
};if _cdad ==nil {return _dgfac ;};if _dgfac .TcBdr ==nil {_dgfac .TcBdr =_cdad .TcBdr ;}else {if _dgfac .TcBdr .Left ==nil {_dgfac .TcBdr .Left =_cdad .TcBdr .Left ;};if _dgfac .TcBdr .Right ==nil {_dgfac .TcBdr .Right =_cdad .TcBdr .Right ;};if _dgfac .TcBdr .Top ==nil {_dgfac .TcBdr .Top =_cdad .TcBdr .Top ;
};if _dgfac .TcBdr .Bottom ==nil {_dgfac .TcBdr .Bottom =_cdad .TcBdr .Bottom ;};if _dgfac .TcBdr .InsideH ==nil {_dgfac .TcBdr .InsideH =_cdad .TcBdr .InsideH ;};if _dgfac .TcBdr .InsideV ==nil {_dgfac .TcBdr .InsideV =_cdad .TcBdr .InsideV ;};};if _dgfac .ThemeableFillStyleChoice .Fill ==nil {_dgfac .ThemeableFillStyleChoice .Fill =_cdad .ThemeableFillStyleChoice .Fill ;
};if _dgfac .ThemeableFillStyleChoice .FillRef ==nil {_dgfac .ThemeableFillStyleChoice .FillRef =_cdad .ThemeableFillStyleChoice .FillRef ;};return _dgfac ;};

// ConvertToPdfWithOptions convert a presentation to PDF with given options.
func ConvertToPdfWithOptions (pr *_cf .Presentation ,opts *Options )*_cd .Creator {_da :=pr .X ().SldSz ;_bdf :=_ac .FromEMU (int64 (_da .CxAttr ));_be :=_ac .FromEMU (int64 (_da .CyAttr ));_gb :=_cd .PageSize {_bdf ,_be };if (_gb ==_cd .PageSize {}){_gb =_dd .GetDefaultPageSize ();
if opts !=nil &&opts .DefaultPageSize !=_dd .DefaultPageSize {_gb =_dd .GetPageDimensions (opts .DefaultPageSize );};};_efa :=_cd .New ();_efa .SetPageSize (_gb );var _fb *_bc .Theme ;if len (pr .Themes ())> 0{_fb =pr .Themes ()[0];};for _ ,_ba :=range pr .Slides (){if _ba .X ()==nil {continue ;
};_fbb :=&convertContext {_abag :_efa ,_fea :&_ba ,_cfaa :_ba .GetSlideLayout (),_dcaff :pr .SlideMasters ()[0].X (),_eec :pr ,_cbgd :_fb ,_gce :_ba .X ().ClrMapOvr ,_feg :_gb [1],_bgc :_gb [0]};_fbb .extractDefaultProperties ();_fbb .makeSlide ();_fbb .drawSlide ();
};return _efa ;};func (_abb *convertContext )stretchImage (_ffb *_cd .Image ,_abbb *_bc .CT_StretchInfoProperties ,_bfg ,_aee ,_dcg ,_dbf float64 )(*_cd .Image ,float64 ,float64 ){_bagc :=_abbb .FillRect ;if _bagc ==nil {_dda :=_ffb .Width ()/_ffb .Height ();
_bgd :=_dcg /_dbf ;var _cgde ,_eca float64 ;if _dda > _bgd {_eca =_dbf ;_cgde =_dbf *_dda ;}else {_cgde =_dcg ;_eca =_dcg /_dda ;};_ffb .Scale (_cgde /_ffb .Width (),_eca /_ffb .Height ());return _ffb ,_bfg ,_aee ;};var _dcb ,_efac ,_fff ,_fdbb float64 ;
if _age :=_bagc .LAttr ;_age !=nil {_fff =_dd .FromSTPercentage (_age );};if _bbe :=_bagc .TAttr ;_bbe !=nil {_dcb =_dd .FromSTPercentage (_bbe );};if _acb :=_bagc .RAttr ;_acb !=nil {_fdbb =_dd .FromSTPercentage (_acb );};if _ccag :=_bagc .BAttr ;_ccag !=nil {_efac =_dd .FromSTPercentage (_ccag );
};_aabb :=_dcg *(1-_fff -_fdbb );_bgb :=_dbf *(1-_dcb -_efac );_ffb .Scale (_aabb /_ffb .Width (),_bgb /_ffb .Height ());return _ffb ,_bfg +_fff *_dcg ,_aee +_dcb *_dbf ;};func (_edac *textboxContext )adjustHeights (_fded float64 ){if _edac ._gcfa ._fege < _fded {_edac ._egaf ._ecc +=(_fded -_edac ._gcfa ._fege );
_edac ._gcfa ._fege =_fded ;};};func (_dacbf *textboxContext )addTextRun (_beeb *_bc .EG_TextRun ,_bcba *_bc .CT_TextCharacterProperties ,_dcge _cd .Color ,_cdcc *prefixData ){if _abf :=_beeb .TextRunChoice .Br ;_abf !=nil {_dacbf .addCurrentWordToParagraph ();
_dacbf .newLine ();_dacbf .newWord ();}else if _dfaee :=_beeb .TextRunChoice .R ;_dfaee !=nil {var _fbdcf _cd .Color ;if _dfaee .RPr !=nil &&_dfaee .RPr .FillPropertiesChoice .SolidFill !=nil {_fbdcf ,_ =_dacbf ._cgba .getColorFromSolidFill (_dfaee .RPr .FillPropertiesChoice .SolidFill );
}else if _dcge !=nil {_fbdcf =_dcge ;}else if _bcba .FillPropertiesChoice .SolidFill !=nil {_fbdcf ,_ =_dacbf ._cgba .getColorFromSolidFill (_bcba .FillPropertiesChoice .SolidFill );}else {_fbdcf =_cd .ColorBlack ;};_geg :=_eggge (_dfaee .RPr ,_bcba );
_aac ,_adfe ,_cgf ,_decf :=_dacbf ._cgba .makeStyleFromRPr (_geg );_aac .Color =_fbdcf ;if _cdcc !=nil {_dacbf .addPrefix (_cdcc ,_aac );};_gcca :=_aea (_dfaee .T );for _ ,_eabf :=range _gcca {_eabf ._def =_aac ;_eabf ._fae =_adfe ;_eabf ._ddg =_cgf ;_eabf ._dfd =_decf ;
if _geg .CapAttr ==_bc .ST_TextCapsTypeAll {_eabf ._cfgb =_a .ToUpper (_eabf ._cfgb );};_dacbf .addTextSymbol (_eabf );};};};func _fdg (_bgda ,_acbe *_bc .CT_TextListStyle )*_bc .CT_TextListStyle {_abd :=_bc .NewCT_TextListStyle ();if _bgda !=nil {*_abd =*_bgda ;
};if _acbe ==nil {return _abd ;};_abd .DefPPr =_bceg (_abd .DefPPr ,_acbe .DefPPr );_abd .Lvl1pPr =_bceg (_abd .Lvl1pPr ,_acbe .Lvl1pPr );_abd .Lvl2pPr =_bceg (_abd .Lvl2pPr ,_acbe .Lvl2pPr );_abd .Lvl3pPr =_bceg (_abd .Lvl3pPr ,_acbe .Lvl3pPr );_abd .Lvl4pPr =_bceg (_abd .Lvl4pPr ,_acbe .Lvl4pPr );
_abd .Lvl5pPr =_bceg (_abd .Lvl5pPr ,_acbe .Lvl5pPr );_abd .Lvl6pPr =_bceg (_abd .Lvl6pPr ,_acbe .Lvl6pPr );_abd .Lvl7pPr =_bceg (_abd .Lvl7pPr ,_acbe .Lvl7pPr );_abd .Lvl8pPr =_bceg (_abd .Lvl8pPr ,_acbe .Lvl8pPr );_abd .Lvl9pPr =_bceg (_abd .Lvl9pPr ,_acbe .Lvl9pPr );
return _abd ;};func (_bgec *convertContext )getStyleColors (_cfae *_bc .CT_ShapeStyle )(_cd .Color ,_cd .Color ,_cd .Color ){var _eadd ,_eggd ,_cacb _cd .Color ;if _dbfg :=_cfae .LnRef ;_dbfg !=nil {_eggd =_bgec .getColorFromMatrixReference (_dbfg );};
if _daab :=_cfae .FillRef ;_daab !=nil {_cacb =_bgec .getColorFromMatrixReference (_daab );};if _edf :=_cfae .FontRef ;_edf !=nil {_eadd =_bgec .getColorFromFontReference (_edf );};return _eadd ,_cacb ,_eggd ;};

// FontStyle represents a kind of font styling. It can be FontStyle_Regular, FontStyle_Bold, FontStyle_Italic and FontStyle_BoldItalic.
type FontStyle =_dd .FontStyle ;

// ConvertToPdf converts a presentation to a PDF file. This package is beta, breaking changes can take place.
func ConvertToPdf (pr *_cf .Presentation )*_cd .Creator {return ConvertToPdfWithOptions (pr ,nil )};func _ecge (_bbfb *_ce .CT_CommonSlideData ,_edbf _ce .ST_PlaceholderType ,_aaba *uint32 )(*_bc .CT_Transform2D ,*_bc .CT_TextBody ,*bool ,*bool ,*bool ){_efcc :=_aaba ==nil ;
if _bbfb !=nil &&(_edbf !=_ce .ST_PlaceholderTypeUnset ||!_efcc ){if _cacd :=_bbfb .SpTree ;_cacd !=nil {for _ ,_dfdbb :=range _cacd .GroupShapeChoice {if _dfdbb !=nil {if _dfdbb .Sp !=nil {_cdcbb ,_ccdb :=_fcfa (_dfdbb .Sp );if _edbf ==_cdcbb {if (_efcc &&_ccdb ==nil )||(!_efcc &&_ccdb !=nil &&*_ccdb ==*_aaba ){var _afb *_bc .CT_Transform2D ;
if _dfdbb .Sp .SpPr !=nil {_afb =_dfdbb .Sp .SpPr .Xfrm ;};_dafd :=_edbf ==_ce .ST_PlaceholderTypeTitle ||_edbf ==_ce .ST_PlaceholderTypeCtrTitle ;_bebf :=_edbf ==_ce .ST_PlaceholderTypeSubTitle ;_fbde :=!_dafd &&_edbf !=_ce .ST_PlaceholderTypeUnset ;return _afb ,_dfdbb .Sp .TxBody ,&_dafd ,&_bebf ,&_fbde ;
};};};};};};};return nil ,nil ,nil ,nil ,nil ;};func (_aad *convertContext )tileImage (_bdg *_cd .Image ,_bca *_bc .CT_TileInfoProperties ,_cge ,_gaa float64 )*_cd .Image {_bbc ,_aef :=1.0,1.0;if _cdf :=_bca .SxAttr ;_cdf !=nil {_bbc =_dd .FromSTPercentage (_cdf );
};if _fgf :=_bca .SyAttr ;_fgf !=nil {_aef =_dd .FromSTPercentage (_fgf );};_dgg :=_dd .MakeTempCreator (_cge ,_gaa );_bdg .Scale (_bbc ,_aef );_bae ,_fdb :=_bdg .Width (),_bdg .Height ();var _dbe ,_bec float64 ;if _cgd :=_bca .TxAttr ;_cgd !=nil {_dbe =_ac .FromEMU (_dd .FromSTCoordinate (*_cgd ));
};if _ecd :=_bca .TyAttr ;_ecd !=nil {_bec =_ac .FromEMU (_dd .FromSTCoordinate (*_ecd ));};if _dbe > 0{_dbe -=_bae ;};if _bec > 0{_bec -=_fdb ;};_agg :=_aad ._bgc /_bae +1;_ccf :=_aad ._feg /_fdb +1;for _bccf :=0.0;_bccf <=_agg ;_bccf ++{_dacb :=_bccf *_bae ;
for _gcfc :=0.0;_gcfc <=_ccf ;_gcfc ++{_de :=_gcfc *_fdb ;_bdg .SetPos (_dacb +_dbe ,_de +_bec );_dgg .Draw (_bdg );};};_gfd ,_bde :=_dd .GetPageFromCreator (_dgg );if _bde !=nil {_eb .Log .Debug ("\u0043\u0061\u006e\u006e\u006f\u0074 \u0067\u0065\u0074\u0020\u0069\u006d\u0061\u0067\u0065\u0020\u0066\u0072\u006fm\u0020\u0063\u0072\u0065\u0061\u0074\u006fr\u003a\u0020\u0025\u0073",_bde );
return nil ;};_aage ,_bde :=_bgg .NewImageDevice ().Render (_gfd );if _bde !=nil {_eb .Log .Debug ("\u0043\u0061\u006eno\u0074\u0020\u0072\u0065\u006e\u0064\u0065\u0072\u0020\u0069\u006d\u0061\u0067\u0065\u003a\u0020\u0025\u0073",_bde );return nil ;};_eab ,_bde :=_aad ._abag .NewImageFromGoImage (_aage );
if _bde !=nil {_eb .Log .Debug ("\u0043\u0061nn\u006f\u0074\u0020c\u0072\u0065\u0061\u0074e i\u006dag\u0065\u0020\u0066\u0072\u006f\u006d\u0020Go\u0049\u006d\u0061\u0067\u0065\u003a\u0020%\u0073",_bde );return nil ;};return _eab ;};func (_cgdc *line )moveRight (_bagab float64 ){for _ ,_bfad :=range _cgdc ._dbfc {_bfad ._bgef +=_bagab ;
};};type prefixData struct{_dfdb string ;_fbdb bool ;_ebeb float64 ;_cceg float64 ;};func (_ceg *textboxContext )addPrefix (_dedbd *prefixData ,_gbca *_cd .TextStyle ){_gaac :=_aea (_dedbd ._dfdb );_bdcc :=*_gbca ;if _dedbd ._fbdb {_bdcc .Font =_dd .AssignStdFontByName (_bdcc ,"\u0053\u0079\u006d\u0062\u006f\u006c");
};for _ ,_gdbe :=range _gaac {_gdbe ._def =&_bdcc ;_ceg .addTextSymbol (_gdbe );};_ecca :=-(_dedbd ._cceg +_ceg ._eega ._begf );if _ecca < 0{_ecca =0;};_ffa :=&symbol {_cfgb :"\u0020",_fcda :_ecca };_ceg .addTextSymbol (_ffa );_ceg ._eega ._bgef +=(_dedbd ._cceg +_dedbd ._ebeb );
};func (_dea *convertContext )renderPageWithDrawableToGoImage (_edc _cd .Drawable )(_bg .Image ,error ){_cgfag :=_dd .MakeTempCreator (_dea ._bgc ,_dea ._feg );_cgfag .NewPage ();_cgfag .Draw (_edc );_edgf ,_acef :=_dd .GetPageFromCreator (_cgfag );if _acef !=nil {return nil ,_acef ;
};return _bgg .NewImageDevice ().Render (_edgf );};func (_bcd *convertContext )makePdfBlockFromChart (_ea *_cg .Chart ,_efc ,_ag float64 )(*_cd .Block ,error ){_gc :=_ea .CT_RelId .IdAttr ;_dbc :=_bcd ._fea .GetChartSpaceByRelId (_gc );if _dbc ==nil {return nil ,_c .New ("\u004e\u006f\u0020\u0063\u0068\u0061\u0072\u0074\u0073\u0070\u0061\u0063\u0065");
};var _cgb *_bc .Theme ;_gg :=_bcd ._eec .Themes ();if len (_gg )> 0{_cgb =_gg [0];};return _dd .MakeBlockFromChartSpace (_dbc ,_efc ,_ag ,_cgb );};func (_bafb *textboxContext )alignParagraphsVertically (_addc _bc .ST_TextAnchoringType ){if _addc ==_bc .ST_TextAnchoringTypeT {return ;
};_bdde :=0.0;for _ ,_ccg :=range _bafb ._cac {_bdde +=_ccg ._ccdf +_ccg ._ecc +_ccg ._cfc ;};var _adad float64 ;switch _addc {case _bc .ST_TextAnchoringTypeCtr :_adad =(_bafb ._bdad -_bdde )/2;case _bc .ST_TextAnchoringTypeB :_adad =_bafb ._bdad -_bdde ;
};for _ ,_ffd :=range _bafb ._cac {_ffd ._bdd +=_adad ;};};func (_adabf *convertContext )renderCurrentStateToGoImage ()(_bg .Image ,error ){_egdf :=_dd .MakeTempCreator (_adabf ._bgc ,_adabf ._feg );_egdf .NewPage ();for _ ,_baab :=range _adabf ._dec {if _baab !=nil {_egdf .MoveTo (0,0);
_egdf .Draw (_baab );};};_acdd ,_eaae :=_dd .GetPageFromCreator (_egdf );if _eaae !=nil {return nil ,_eaae ;};return _bgg .NewImageDevice ().Render (_acdd );};

// RegisterFontsFromDirectory registers all fonts from the given directory automatically detecting font families and styles.
func RegisterFontsFromDirectory (dirName string )error {return _dd .RegisterFontsFromDirectory (dirName )};func (_fgcg *convertContext )getColorFromMatrixReference (_ecce *_bc .CT_StyleMatrixReference )_cd .Color {if _ecce ==nil {return nil ;};var _abac _cd .Color ;
var _agd string ;if _aebg :=_ecce .SrgbClr ;_aebg !=nil {_agd =_aebg .ValAttr ;}else if _fag :=_ecce .SchemeClr ;_fag !=nil {_agd =_dd .GetColorStringFromDmlColor (_fgcg ._eec .GetColorBySchemeColor (_fag .ValAttr ));_agd =_dd .AdjustColor (_agd ,_fag .EG_ColorTransform );
};if _agd !=""{_abac =_cd .ColorRGBFromHex ("\u0023"+_agd );};return _abac ;};func (_eefc *convertContext )getShapeFromBlipFill (_fdf *_bc .CT_BlipFillProperties ,_bbcd ,_dgbb ,_fcge ,_aadf float64 ,_afegd bool )_cd .Drawable {_ffde ,_egcg ,_gab :=_eefc .makePdfImageFromBlipFill (_fdf ,_afegd );
if _gab !=nil {_eb .Log .Debug ("\u0043\u0061\u006e\u006e\u006f\u0074\u0020\u006d\u0061\u006b\u0065\u0020\u0050D\u0046\u0020\u0069\u006d\u0061\u0067e\u0020\u0066\u0072\u006f\u006d\u0020\u0042\u006c\u0069\u0070\u0046\u0069\u006cl\u003a\u0020\u0025\u0073",_gab );
return nil ;};if _ffde ==nil {return nil ;};if _aeeg :=_fdf .FillModePropertiesChoice .Tile ;_aeeg !=nil {_ffde =_eefc .tileImage (_ffde ,_fdf .FillModePropertiesChoice .Tile ,_fcge ,_aadf );};if _aga :=_fdf .FillModePropertiesChoice .Stretch ;_aga !=nil {_ffde ,_bbcd ,_dgbb =_eefc .stretchImage (_ffde ,_fdf .FillModePropertiesChoice .Stretch ,_bbcd ,_dgbb ,_fcge ,_aadf );
};if _ffde ==nil {return nil ;};if len (_egcg )==0{_ffde .SetPos (_bbcd ,_dgbb );return _ffde ;};_ffde =_eefc .applyBlipEffectsOnImg (_ffde ,_bbcd ,_dgbb ,_egcg );_ffde .SetPos (_bbcd ,_dgbb );return _ffde ;};func (_gba *textboxContext )newWord (){_gba ._eega =&word {_bgdgg :true ,_bgef :_gba ._gcfa ._cdga }};
func _efeag (_gacg ,_dfba *_bc .CT_TextBody )(*_bc .CT_TextBodyProperties ,*_bc .CT_TextListStyle ){if _gacg ==nil &&_dfba ==nil {return nil ,nil ;};if _gacg ==nil {return _dfba .BodyPr ,_dfba .LstStyle ;};if _dfba ==nil {return _gacg .BodyPr ,_gacg .LstStyle ;
};_egga ,_eecc :=_gacg .BodyPr ,_gacg .LstStyle ;_cef ,_afgc :=_dfba .BodyPr ,_dfba .LstStyle ;_dcbb :=_aeed (_egga ,_cef );_dceef :=_fdg (_eecc ,_afgc );return _dcbb ,_dceef ;};func _dbcbc (_dfde ,_ebee *_bc .CT_TablePartStyle )*_bc .CT_TablePartStyle {_cgdeb :=_bc .NewCT_TablePartStyle ();
if _dfde !=nil {*_cgdeb =*_dfde ;};if _ebee ==nil {return _cgdeb ;};if _cgdeb .TcTxStyle ==nil {_cgdeb .TcTxStyle =_ebee .TcTxStyle ;}else {_cgdeb .TcTxStyle =_ccgd (_cgdeb .TcTxStyle ,_ebee .TcTxStyle );};if _cgdeb .TcStyle ==nil {_cgdeb .TcStyle =_ebee .TcStyle ;
}else {_cgdeb .TcStyle =_ccegd (_cgdeb .TcStyle ,_ebee .TcStyle );};return _cgdeb ;};func _eggge (_egba ,_gadd *_bc .CT_TextCharacterProperties )*_bc .CT_TextCharacterProperties {_ecgc :=_bc .NewCT_TextCharacterProperties ();if _egba !=nil {*_ecgc =*_egba ;
};if _gadd ==nil {return _ecgc ;};if _ecgc .KumimojiAttr ==nil {_ecgc .KumimojiAttr =_gadd .KumimojiAttr ;};if _ecgc .LangAttr ==nil {_ecgc .LangAttr =_gadd .LangAttr ;};if _ecgc .AltLangAttr ==nil {_ecgc .AltLangAttr =_gadd .AltLangAttr ;};if _ecgc .SzAttr ==nil {_ecgc .SzAttr =_gadd .SzAttr ;
};if _ecgc .BAttr ==nil {_ecgc .BAttr =_gadd .BAttr ;};if _ecgc .IAttr ==nil {_ecgc .IAttr =_gadd .IAttr ;};if _ecgc .UAttr ==_bc .ST_TextUnderlineTypeUnset {_ecgc .UAttr =_gadd .UAttr ;};if _ecgc .StrikeAttr ==_bc .ST_TextStrikeTypeUnset {_ecgc .StrikeAttr =_gadd .StrikeAttr ;
};if _ecgc .KernAttr ==nil {_ecgc .KernAttr =_gadd .KernAttr ;};if _ecgc .CapAttr ==_bc .ST_TextCapsTypeUnset {_ecgc .CapAttr =_gadd .CapAttr ;};if _ecgc .SpcAttr ==nil {_ecgc .SpcAttr =_gadd .SpcAttr ;};if _ecgc .NormalizeHAttr ==nil {_ecgc .NormalizeHAttr =_gadd .NormalizeHAttr ;
};if _ecgc .BaselineAttr ==nil {_ecgc .BaselineAttr =_gadd .BaselineAttr ;};if _ecgc .NoProofAttr ==nil {_ecgc .NoProofAttr =_gadd .NoProofAttr ;};if _ecgc .DirtyAttr ==nil {_ecgc .DirtyAttr =_gadd .DirtyAttr ;};if _ecgc .ErrAttr ==nil {_ecgc .ErrAttr =_gadd .ErrAttr ;
};if _ecgc .SmtCleanAttr ==nil {_ecgc .SmtCleanAttr =_gadd .SmtCleanAttr ;};if _ecgc .SmtIdAttr ==nil {_ecgc .SmtIdAttr =_gadd .SmtIdAttr ;};if _ecgc .BmkAttr ==nil {_ecgc .BmkAttr =_gadd .BmkAttr ;};if _ecgc .Ln ==nil {_ecgc .Ln =_gadd .Ln ;};if _ecgc .FillPropertiesChoice .NoFill ==nil {_ecgc .FillPropertiesChoice .NoFill =_gadd .FillPropertiesChoice .NoFill ;
};if _ecgc .FillPropertiesChoice .SolidFill ==nil {_ecgc .FillPropertiesChoice .SolidFill =_gadd .FillPropertiesChoice .SolidFill ;};if _ecgc .FillPropertiesChoice .BlipFill ==nil {_ecgc .FillPropertiesChoice .BlipFill =_gadd .FillPropertiesChoice .BlipFill ;
};if _ecgc .EffectPropertiesChoice .EffectLst ==nil {_ecgc .EffectPropertiesChoice .EffectLst =_gadd .EffectPropertiesChoice .EffectLst ;};if _ecgc .EffectPropertiesChoice .EffectDag ==nil {_ecgc .EffectPropertiesChoice .EffectDag =_gadd .EffectPropertiesChoice .EffectDag ;
};if _ecgc .Highlight ==nil {_ecgc .Highlight =_gadd .Highlight ;};if _ecgc .TextUnderlineLineChoice .ULnTx ==nil {_ecgc .TextUnderlineLineChoice .ULnTx =_gadd .TextUnderlineLineChoice .ULnTx ;};if _ecgc .TextUnderlineLineChoice .ULn ==nil {_ecgc .TextUnderlineLineChoice .ULn =_gadd .TextUnderlineLineChoice .ULn ;
};if _ecgc .TextUnderlineFillChoice .UFillTx ==nil {_ecgc .TextUnderlineFillChoice .UFillTx =_gadd .TextUnderlineFillChoice .UFillTx ;};if _ecgc .TextUnderlineFillChoice .UFill ==nil {_ecgc .TextUnderlineFillChoice .UFill =_gadd .TextUnderlineFillChoice .UFill ;
};if _ecgc .Latin ==nil {_ecgc .Latin =_gadd .Latin ;};if _ecgc .Ea ==nil {_ecgc .Ea =_gadd .Ea ;};if _ecgc .Cs ==nil {_ecgc .Cs =_gadd .Cs ;};if _ecgc .Sym ==nil {_ecgc .Sym =_gadd .Sym ;};if _ecgc .Rtl ==nil {_ecgc .Rtl =_gadd .Rtl ;};return _ecgc ;};
func _cfdbd (_dgfa *_bc .CT_AdjPoint2D )(float64 ,float64 ){var _ccae ,_fgcad float64 ;_gedd ,_agcd :=_dgfa .XAttr ,_dgfa .YAttr ;if _dcdd :=_gedd .ST_Coordinate ;_dcdd !=nil {_ccae =_ac .FromEMU (_dd .FromSTCoordinate (*_dcdd ));};if _bab :=_agcd .ST_Coordinate ;
_bab !=nil {_fgcad =_ac .FromEMU (_dd .FromSTCoordinate (*_bab ));};return _ccae ,_fgcad ;};func _eceb (_cddfa float64 )float64 {return _cddfa *_ac .Inch };func _ccgd (_efd ,_fddg *_bc .CT_TableStyleTextStyle )*_bc .CT_TableStyleTextStyle {_gdec :=_bc .NewCT_TableStyleTextStyle ();
if _efd !=nil {*_gdec =*_efd ;};if _fddg ==nil {return _gdec ;};if _gdec .BAttr ==_bc .ST_OnOffStyleTypeUnset {_gdec .BAttr =_fddg .BAttr ;};if _gdec .IAttr ==_bc .ST_OnOffStyleTypeUnset {_gdec .IAttr =_fddg .IAttr ;};if _gdec .ThemeableFontStylesChoice .Font ==nil {_gdec .ThemeableFontStylesChoice .Font =_fddg .ThemeableFontStylesChoice .Font ;
};if _gdec .ThemeableFontStylesChoice .FontRef ==nil {_gdec .ThemeableFontStylesChoice .FontRef =_fddg .ThemeableFontStylesChoice .FontRef ;};if _gdec .ScrgbClr ==nil {_gdec .ScrgbClr =_fddg .ScrgbClr ;};if _gdec .SrgbClr ==nil {_gdec .SrgbClr =_fddg .SrgbClr ;
};if _gdec .HslClr ==nil {_gdec .HslClr =_fddg .HslClr ;};if _gdec .SysClr ==nil {_gdec .SysClr =_fddg .SysClr ;};if _gdec .SchemeClr ==nil {_gdec .SchemeClr =_fddg .SchemeClr ;};if _gdec .PrstClr ==nil {_gdec .PrstClr =_fddg .PrstClr ;};return _gdec ;
};type textboxContext struct{_cgba *convertContext ;_dgfd float64 ;_bdad float64 ;_ccc *_cd .Creator ;_gga float64 ;_cac []*paragraph ;_egaf *paragraph ;_gcfa *line ;_eega *word ;_ede bool ;};func (_ecgb *convertContext )getInfoFromLn (_gfdf *_bc .CT_LineProperties )(_cd .Color ,float64 ,float64 ){if _gfdf ==nil ||_gfdf .LineFillPropertiesChoice .NoFill !=nil {return nil ,0,0;
};var _ddba float64 ;_bafc ,_fgdg :=_ecgb .getColorFromSolidFill (_gfdf .LineFillPropertiesChoice .SolidFill );if _bgcc :=_gfdf .WAttr ;_bgcc !=nil {_ddba =_ac .FromEMU (int64 (*_bgcc ));}else {_ddba =1;};return _bafc ,_ddba ,_fgdg ;};func _bceg (_bcdg ,_baca *_bc .CT_TextParagraphProperties )*_bc .CT_TextParagraphProperties {_dbd :=_bc .NewCT_TextParagraphProperties ();
if _bcdg !=nil {*_dbd =*_bcdg ;};if _baca ==nil {return _dbd ;};if _dbd .MarLAttr ==nil {_dbd .MarLAttr =_baca .MarLAttr ;};if _dbd .MarRAttr ==nil {_dbd .MarRAttr =_baca .MarRAttr ;};if _dbd .LvlAttr ==nil {_dbd .LvlAttr =_baca .LvlAttr ;};if _dbd .IndentAttr ==nil {_dbd .IndentAttr =_baca .IndentAttr ;
};if _dbd .AlgnAttr ==_bc .ST_TextAlignTypeUnset {_dbd .AlgnAttr =_baca .AlgnAttr ;};if _dbd .DefTabSzAttr ==nil {_dbd .DefTabSzAttr =_baca .DefTabSzAttr ;};if _dbd .RtlAttr ==nil {_dbd .RtlAttr =_baca .RtlAttr ;};if _dbd .EaLnBrkAttr ==nil {_dbd .EaLnBrkAttr =_baca .EaLnBrkAttr ;
};if _dbd .FontAlgnAttr ==_bc .ST_TextFontAlignTypeUnset {_dbd .FontAlgnAttr =_baca .FontAlgnAttr ;};if _dbd .LatinLnBrkAttr ==nil {_dbd .LatinLnBrkAttr =_baca .LatinLnBrkAttr ;};if _dbd .HangingPunctAttr ==nil {_dbd .HangingPunctAttr =_baca .HangingPunctAttr ;
};if _dbd .LnSpc ==nil {_dbd .LnSpc =_baca .LnSpc ;};if _dbd .SpcBef ==nil {_dbd .SpcBef =_baca .SpcBef ;};if _dbd .SpcAft ==nil {_dbd .SpcAft =_baca .SpcAft ;};if _dbd .TextBulletColorChoice .BuClrTx ==nil {_dbd .TextBulletColorChoice .BuClrTx =_baca .TextBulletColorChoice .BuClrTx ;
};if _dbd .TextBulletColorChoice .BuClr ==nil {_dbd .TextBulletColorChoice .BuClr =_baca .TextBulletColorChoice .BuClr ;};if _dbd .TextBulletSizeChoice .BuSzTx ==nil {_dbd .TextBulletSizeChoice .BuSzTx =_baca .TextBulletSizeChoice .BuSzTx ;};if _dbd .TextBulletSizeChoice .BuSzPct ==nil {_dbd .TextBulletSizeChoice .BuSzPct =_baca .TextBulletSizeChoice .BuSzPct ;
};if _dbd .TextBulletSizeChoice .BuSzPts ==nil {_dbd .TextBulletSizeChoice .BuSzPts =_baca .TextBulletSizeChoice .BuSzPts ;};if _dbd .TextBulletTypefaceChoice .BuFontTx ==nil {_dbd .TextBulletTypefaceChoice .BuFontTx =_baca .TextBulletTypefaceChoice .BuFontTx ;
};if _dbd .TextBulletTypefaceChoice .BuFont ==nil {_dbd .TextBulletTypefaceChoice .BuFont =_baca .TextBulletTypefaceChoice .BuFont ;};if _dbd .TextBulletChoice .BuNone ==nil {_dbd .TextBulletChoice .BuNone =_baca .TextBulletChoice .BuNone ;};if _dbd .TextBulletChoice .BuAutoNum ==nil {_dbd .TextBulletChoice .BuAutoNum =_baca .TextBulletChoice .BuAutoNum ;
};if _dbd .TextBulletChoice .BuChar ==nil {_dbd .TextBulletChoice .BuChar =_baca .TextBulletChoice .BuChar ;};if _dbd .TextBulletChoice .BuBlip ==nil {_dbd .TextBulletChoice .BuBlip =_baca .TextBulletChoice .BuBlip ;};if _dbd .TabLst ==nil {_dbd .TabLst =_baca .TabLst ;
};if _dbd .ExtLst ==nil {_dbd .ExtLst =_baca .ExtLst ;};_dbd .DefRPr =_eggge (_dbd .DefRPr ,_baca .DefRPr );return _dbd ;};type symbolStyle struct{_befc *string ;_cfce *float64 ;_caae *string ;_aged *bool ;_dbcb *bool ;_egafe *bool ;_edda *bool ;_fed *bool ;
};func (_ddb *textboxContext )addCurrentWordToParagraph (){if _ddb ._ede &&_ddb ._gcfa ._cdga +_ddb ._eega ._begf > _ddb ._gcfa ._ecb {_ddb .newLine ();};if !_ddb ._eega ._bgdgg ||len (_ddb ._gcfa ._dbfc )> 0{_ddb ._eega ._bgef =_ddb ._gcfa ._cdga ;_ddb ._gcfa ._dbfc =append (_ddb ._gcfa ._dbfc ,_ddb ._eega );
_ddb ._gcfa ._cdga +=_ddb ._eega ._begf ;for _ ,_gbfa :=range _ddb ._eega ._eedb {_ddb .adjustHeights (_gbfa ._edga );};};};type romanMatch struct{_acea int ;_bgcf string ;};type background struct{_cdae _cd .Color ;_dedb float64 ;_bfc *_bc .CT_BlipFillProperties ;
};type symbol struct{_cfgb string ;_fbbg float64 ;_edga float64 ;_bgdf float64 ;_gdda float64 ;_fcda float64 ;_def *_cd .TextStyle ;_dgdg string ;_fae bool ;_ddg bool ;_dfd bool ;};func (_ece *textboxContext )alignSymbolsVertically (){for _ ,_accf :=range _ece ._cac {for _ ,_effd :=range _accf ._dadf {_gebf :=0.0;
for _ ,_dadb :=range _effd ._dbfc {for _ ,_ggdd :=range _dadb ._eedb {if _ggdd ._edga > _gebf {_gebf =_ggdd ._edga ;};};};for _ ,_gcae :=range _effd ._dbfc {for _ ,_bded :=range _gcae ._eedb {if _bded ._bgdf < _gebf {_bded ._gdda =_gebf -_bded ._bgdf ;
};};};};};};func (_eedd *textboxContext )alignVertically (_cdec _bc .ST_TextAnchoringType ){_eedd .alignParagraphsVertically (_cdec );_eedd .alignSymbolsVertically ();};func (_caf *convertContext )makeStyleFromRPr (_fdba *_bc .CT_TextCharacterProperties )(*_cd .TextStyle ,bool ,bool ,bool ){var _abgg ,_accgg ,_ddeb bool ;
_gaaa :=_caf ._abag .NewTextStyle ();if _fdba !=nil {_agb :=_dd .FontStyle_Regular ;_cbef :=_abc (_fdba .BAttr );_eeb :=_abc (_fdba .IAttr );if _cbef &&_eeb {_agb =_dd .FontStyle_BoldItalic ;}else if _cbef {_agb =_dd .FontStyle_Bold ;}else if _eeb {_agb =_dd .FontStyle_Italic ;
};_ddeb =_fdba .UAttr !=_bc .ST_TextUnderlineTypeUnset &&_fdba .UAttr !=_bc .ST_TextUnderlineTypeNone ;_aeg :="\u0064e\u0066\u0061\u0075\u006c\u0074";if _eag :=_fdba .Latin ;_eag !=nil {_aeg =_eag .TypefaceAttr ;}else if _afgb :=_fdba .Ea ;_afgb !=nil {_aeg =_afgb .TypefaceAttr ;
}else if _ebec :=_fdba .Cs ;_ebec !=nil {_aeg =_ebec .TypefaceAttr ;}else if _efcd :=_fdba .Sym ;_efcd !=nil {_aeg =_efcd .TypefaceAttr ;};if _cff ,_ebdd :=_dd .StdFontsMap [_aeg ];_ebdd {_gaaa .Font =_dd .AssignStdFontByName (_gaaa ,_cff [_agb ]);}else if _ecdg :=_dd .GetRegisteredFont (_aeg ,_agb );
_ecdg !=nil {_gaaa .Font =_ecdg ;}else {_eb .Log .Debug ("\u0046\u006f\u006e\u0074\u0020\u0025\u0073\u0020\u0077\u0069\u0074h\u0020\u0073\u0074\u0079\u006c\u0065\u0020\u0025s\u0020i\u0073\u0020\u006e\u006f\u0074\u0020\u0066\u006f\u0075\u006e\u0064\u002c\u0020\u0072\u0065\u0073\u0065\u0074 \u0074\u006f\u0020\u0064\u0065\u0066\u0061\u0075\u006c\u0074\u002e",_aeg ,_agb );
_gaaa .Font =_dd .AssignStdFontByName (_gaaa ,_dd .StdFontsMap ["\u0064e\u0066\u0061\u0075\u006c\u0074"][_agb ]);};var _eeg float64 ;if _gae :=_fdba .SzAttr ;_gae !=nil {_eeg =_d .Round (float64 (*_gae )/100)-0.5;}else {_eeg =_dd .DefaultFontSize ;};if _cba :=_fdba .BaselineAttr ;
_cba !=nil {if _baf :=_cba .ST_PercentageDecimal ;_baf !=nil {if *_baf > 0{_abgg =true ;}else if *_baf < 0{_accgg =true ;};};};if _abgg ||_accgg {_eeg =_d .Round (_eeg *0.64);};_gaaa .FontSize =_eeg ;_gbd :=0.0;if _fge :=_fdba .SpcAttr ;_fge !=nil {if _dfab :=_fge .ST_TextPointUnqualified ;
_dfab !=nil &&*_dfab > 0{_gbd =float64 (*_dfab )/100;};};_gaaa .CharSpacing =_gbd ;};return &_gaaa ,_abgg ,_accgg ,_ddeb ;};type word struct{_eedb []*symbol ;_bgef float64 ;_begf float64 ;_bgdgg bool ;};func (_gec *textboxContext )newParagraph (){_dbgf :=&paragraph {};
_dbgf ._bdd =_gec ._gga ;_gec ._egaf =_dbgf ;};func _aeed (_feed ,_beda *_bc .CT_TextBodyProperties )*_bc .CT_TextBodyProperties {_aeaf :=_bc .NewCT_TextBodyProperties ();if _feed !=nil {*_aeaf =*_feed ;};if _beda ==nil {return _aeaf ;};if _aeaf .RotAttr ==nil {_aeaf .RotAttr =_beda .RotAttr ;
};if _aeaf .SpcFirstLastParaAttr ==nil {_aeaf .SpcFirstLastParaAttr =_beda .SpcFirstLastParaAttr ;};if _aeaf .VertOverflowAttr ==_bc .ST_TextVertOverflowTypeUnset {_aeaf .VertOverflowAttr =_beda .VertOverflowAttr ;};if _aeaf .HorzOverflowAttr ==_bc .ST_TextHorzOverflowTypeUnset {_aeaf .HorzOverflowAttr =_beda .HorzOverflowAttr ;
};if _aeaf .VertAttr ==_bc .ST_TextVerticalTypeUnset {_aeaf .VertAttr =_beda .VertAttr ;};if _aeaf .WrapAttr ==_bc .ST_TextWrappingTypeUnset {_aeaf .WrapAttr =_beda .WrapAttr ;};if _aeaf .LInsAttr ==nil {_aeaf .LInsAttr =_beda .LInsAttr ;};if _aeaf .TInsAttr ==nil {_aeaf .TInsAttr =_beda .TInsAttr ;
};if _aeaf .RInsAttr ==nil {_aeaf .RInsAttr =_beda .RInsAttr ;};if _aeaf .BInsAttr ==nil {_aeaf .BInsAttr =_beda .BInsAttr ;};if _aeaf .NumColAttr ==nil {_aeaf .NumColAttr =_beda .NumColAttr ;};if _aeaf .SpcColAttr ==nil {_aeaf .SpcColAttr =_beda .SpcColAttr ;
};if _aeaf .RtlColAttr ==nil {_aeaf .RtlColAttr =_beda .RtlColAttr ;};if _aeaf .AnchorAttr ==_bc .ST_TextAnchoringTypeUnset {_aeaf .AnchorAttr =_beda .AnchorAttr ;};if _aeaf .AnchorCtrAttr ==nil {_aeaf .AnchorCtrAttr =_beda .AnchorCtrAttr ;};if _aeaf .ForceAAAttr ==nil {_aeaf .ForceAAAttr =_beda .ForceAAAttr ;
};if _aeaf .UprightAttr ==nil {_aeaf .UprightAttr =_beda .UprightAttr ;};if _aeaf .CompatLnSpcAttr ==nil {_aeaf .CompatLnSpcAttr =_beda .CompatLnSpcAttr ;};if _aeaf .PrstTxWarp ==nil {_aeaf .PrstTxWarp =_beda .PrstTxWarp ;};if _aeaf .TextAutofitChoice .NoAutofit ==nil {_aeaf .TextAutofitChoice .NoAutofit =_beda .TextAutofitChoice .NoAutofit ;
};if _aeaf .TextAutofitChoice .NormAutofit ==nil {_aeaf .TextAutofitChoice .NormAutofit =_beda .TextAutofitChoice .NormAutofit ;};if _aeaf .TextAutofitChoice .SpAutoFit ==nil {_aeaf .TextAutofitChoice .SpAutoFit =_beda .TextAutofitChoice .SpAutoFit ;};
if _aeaf .Scene3d ==nil {_aeaf .Scene3d =_beda .Scene3d ;};if _aeaf .Text3DChoice .Sp3d ==nil {_aeaf .Text3DChoice .Sp3d =_beda .Text3DChoice .Sp3d ;};if _aeaf .Text3DChoice .FlatTx ==nil {_aeaf .Text3DChoice .FlatTx =_beda .Text3DChoice .FlatTx ;};if _aeaf .ExtLst ==nil {_aeaf .ExtLst =_beda .ExtLst ;
};return _aeaf ;};const (FontStyle_Regular FontStyle =0;FontStyle_Bold FontStyle =1;FontStyle_Italic FontStyle =2;FontStyle_BoldItalic FontStyle =3;);func (_eafb *convertContext )applyBlipEffectsOnImg (_bcg *_cd .Image ,_ffbf ,_bbga float64 ,_fcdd []*_bc .CT_BlipChoice )*_cd .Image {if len (_fcdd )==0{return _bcg ;
};_bcg .SetPos (_ffbf ,_bbga );_dba ,_acec :=_eafb .renderPageWithDrawableToGoImage (_bcg );if _acec !=nil {_eb .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u0072\u0065\u006e\u0064\u0065\u0072\u0020a\u006e\u0020\u0069\u006d\u0061\u0067e\u0020\u0074\u006f\u0020\u0061\u0020\u0047\u006f\u0020\u0069\u006d\u0061\u0067e\u003a\u0020\u0025\u0073",_acec );
return _bcg ;};_gebb ,_acec :=_eafb .renderCurrentStateToGoImage ();if _acec !=nil {_eb .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u0072\u0065n\u0064\u0065\u0072 t\u0068\u0065\u0020\u0063\u0075\u0072r\u0065\u006e\u0074\u0020\u0073\u0074\u0061\u0074\u0065\u0020\u0074\u006f\u0020\u0061\u0020G\u006f\u0020\u0069\u006d\u0061\u0067\u0065\u003a \u0025\u0073",_acec );
return _bcg ;};_bebfe :=_dba .Bounds ();_gafa :=_bg .NewRGBA (_bebfe );_ccfb ,_aedd :=_bcg .Width (),_bcg .Height ();for _ ,_beaa :=range _fcdd {if _abacd :=_beaa .AlphaModFix ;_abacd ==nil {continue ;};if _cafad :=_beaa .AlphaModFix .AmtAttr ;_cafad !=nil {if _ebgc :=_cafad .ST_PositivePercentageDecimal ;
_ebgc !=nil {_ebab :=uint8 (255*(*_ebgc )/100000);_bbcda :=_bg .NewUniform (_g .Alpha {_ebab });_bd .Draw (_gafa ,_bebfe ,_gebb ,_bg .Point {0,0},_bd .Src );_bd .DrawMask (_gafa ,_bebfe ,_dba ,_bg .Point {0,0},_bbcda ,_bg .Point {0,0},_bd .Over );};};};
_ecffa :=_bg .Rect (int (_ffbf ),int (_bbga ),int (_ffbf +_ccfb )+1,int (_bbga +_aedd )+1);_cgagb :=_dd .CropImageByRect (_gafa ,_ecffa );_agfb ,_acec :=_eafb ._abag .NewImageFromGoImage (_cgagb );if _acec !=nil {_eb .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u0061\u006e\u0020\u0069\u006d\u0061\u0067\u0065\u0020t\u006f \u0061\u0020\u0047\u006f\u0020\u0069\u006d\u0061\u0067\u0065\u003a\u0020\u0025\u0073",_acec );
return _bcg ;};return _agfb ;};func (_afdb *convertContext )getBorderStyle (_caaa *_bc .CT_LineProperties )(_cd .CellBorderStyle ,*_cd .Color ,float64 ){if _caaa ==nil ||_caaa .LineFillPropertiesChoice .NoFill !=nil {return _cd .CellBorderStyleNone ,nil ,0;
};var _ccdg _cd .Color ;if _fbdc :=_caaa .LineFillPropertiesChoice .SolidFill ;_fbdc !=nil {_ccdg ,_ =_afdb .getColorFromSolidFill (_fbdc );};_gfff :=0.0;if _cdddb :=_caaa .WAttr ;_cdddb !=nil {_gfff =_ac .FromEMU (int64 (*_cdddb ));};return _cd .CellBorderStyleSingle ,&_ccdg ,_gfff ;
};

// RegisterFont makes a PdfFont accessible for using in converting to PDF.
func RegisterFont (name string ,style FontStyle ,font *_ace .PdfFont ){_dd .RegisterFont (name ,style ,font );};func (_bbd *textboxContext )addTextSymbol (_aefc *symbol ){_fbdd :=_cd .New ();_dcef :=_fbdd .NewStyledParagraph ();_dcef .SetMargins (0,0,0,0);
_fgde :=_dcef .Append (_aefc ._cfgb );_fcbc :=0.0;if _aefc ._def !=nil {_fgde .Style =*_aefc ._def ;if _aefc ._def .CharSpacing !=0{_fcbc =_aefc ._def .CharSpacing ;};};_aefc ._bgdf =_dcef .Height ();_aefc ._edga =_dcef .Height ()*1.2;if _aefc ._fcda ==0{_aefc ._fcda =_dcef .Width ()+_fcbc ;
};if len (_bbd ._eega ._eedb )> 0{_ebdc :=_bbd ._eega ._eedb [len (_bbd ._eega ._eedb )-1]._cfgb ;if _bbd ._egaf ._bfff ||_dd .IsNoSpaceLanguage (_ebdc )||(_ebdc =="\u0020")!=(_aefc ._cfgb =="\u0020"){_bbd .addCurrentWordToParagraph ();_bbd .newWord ();
};};_bbd ._eega ._eedb =append (_bbd ._eega ._eedb ,_aefc );_aefc ._fbbg =_bbd ._eega ._begf ;_bbd ._eega ._begf +=_aefc ._fcda ;if _aefc ._cfgb !="\u0020"{_bbd ._eega ._bgdgg =false ;};if _bbd ._eega ._begf >=_bbd ._egaf ._fffe -_bbd ._egaf ._gbee {_bbd .addCurrentWordToParagraph ();
_bbd .newLine ();_bbd .newWord ();};};func (_ddag *convertContext )makePdfBlockFromTxBody (_egee *_bc .CT_TextBody ,_bbb *_bc .CT_TextBodyProperties ,_baga *_bc .CT_TextListStyle ,_dee ,_cddf float64 ,_dfbd _cd .Color ,_bfa ,_adc bool )(*_cd .Block ,error ){var _fceb *_bc .CT_TextParagraphProperties ;
if _egb :=_egee .LstStyle ;_egb !=nil {var _acbb *_bc .CT_TextParagraphProperties ;if _egb .Lvl1pPr !=nil {_acbb =_egb .Lvl1pPr ;};_fceb =_bceg (_acbb ,_egb .DefPPr );};var _ddd *_bc .CT_TextParagraphProperties ;if _fceb !=nil {if _bfa {_ddd =_ddag ._dgaa [0];
}else if _adc {_ddd =_ddag ._cdg [0];}else {_ddd =_ddag ._dcga [0];};if _baga !=nil {_ddd =_bceg (_bceg (_baga .Lvl1pPr ,_baga .DefPPr ),_ddd );};_ddd =_bceg (_fceb ,_ddd );}else {if _bfa {_ddd =_ddag ._gfg ;}else if _adc {_ddd =_ddag ._fbbc ;}else {_ddd =_ddag ._baad ;
};};_gcaf ,_ded :=_eggg (2.5),_eggg (2.5);_adb ,_dfgg :=_eggg (1.3),_eggg (1.3);_gefd :=true ;_aeb :=_bc .ST_TextAnchoringTypeT ;if _bbb !=nil {if _daa :=_bbb .AnchorAttr ;_daa !=_bc .ST_TextAnchoringTypeUnset {_aeb =_daa ;};};if _cgbg :=_egee .BodyPr ;
_cgbg !=nil {if _eed :=_cgbg .LInsAttr ;_eed !=nil {_gcaf =_ac .FromEMU (_dd .FromSTCoordinate32 (*_eed ));};if _bfea :=_cgbg .TInsAttr ;_bfea !=nil {_adb =_ac .FromEMU (_dd .FromSTCoordinate32 (*_bfea ));};if _dcbf :=_cgbg .RInsAttr ;_dcbf !=nil {_ded =_ac .FromEMU (_dd .FromSTCoordinate32 (*_dcbf ));
};if _ced :=_cgbg .BInsAttr ;_ced !=nil {_dfgg =_ac .FromEMU (_dd .FromSTCoordinate32 (*_ced ));};_gefd =_cgbg .WrapAttr !=_bc .ST_TextWrappingTypeNone ;if _caa :=_cgbg .AnchorAttr ;_caa !=_bc .ST_TextAnchoringTypeUnset {_aeb =_cgbg .AnchorAttr ;};};_ecagc :=_dd .MakeTempCreator (_dee ,_cddf );
_ecagc .SetPageMargins (_gcaf ,_ded ,_adb ,_dfgg );_bbae :=&textboxContext {_cgba :_ddag ,_ede :_gefd ,_dgfd :_dee -_gcaf -_ded ,_bdad :_cddf -_adb -_dfgg ,_ccc :_ecagc ,_cac :[]*paragraph {}};_dgb :=1;for _ ,_adcf :=range _egee .P {if _adcf !=nil {_ccee :=_adcf .PPr ;
var _gbe *prefixData ;if _ccee !=nil &&_ccee .TextBulletChoice .BuNone ==nil {var _acg string ;var _fee bool ;if _gad :=_ccee .TextBulletChoice .BuAutoNum ;_gad !=nil {var _cgea string ;if _dfbc :=_gad .StartAtAttr ;_dfbc !=nil {_dgb =int (*_dfbc );};var _dgde string ;
switch _gad .TypeAttr {case _bc .ST_TextAutonumberSchemeAlphaUcParenBoth ,_bc .ST_TextAutonumberSchemeAlphaUcParenR ,_bc .ST_TextAutonumberSchemeAlphaUcPeriod :_dgde =_bbbg (_dgb ,true );case _bc .ST_TextAutonumberSchemeAlphaLcParenBoth ,_bc .ST_TextAutonumberSchemeAlphaLcParenR ,_bc .ST_TextAutonumberSchemeAlphaLcPeriod :_dgde =_bbbg (_dgb ,false );
case _bc .ST_TextAutonumberSchemeRomanUcParenBoth ,_bc .ST_TextAutonumberSchemeRomanUcParenR ,_bc .ST_TextAutonumberSchemeRomanUcPeriod :_dgde =_dfggg (_dgb ,true );case _bc .ST_TextAutonumberSchemeRomanLcParenBoth ,_bc .ST_TextAutonumberSchemeRomanLcParenR ,_bc .ST_TextAutonumberSchemeRomanLcPeriod :_dgde =_dfggg (_dgb ,false );
default:_dgde =_e .Itoa (_dgb );};switch _gad .TypeAttr {case _bc .ST_TextAutonumberSchemeAlphaLcPeriod ,_bc .ST_TextAutonumberSchemeAlphaUcPeriod ,_bc .ST_TextAutonumberSchemeArabicPeriod ,_bc .ST_TextAutonumberSchemeRomanLcPeriod ,_bc .ST_TextAutonumberSchemeRomanUcPeriod ,_bc .ST_TextAutonumberSchemeArabicDbPeriod ,_bc .ST_TextAutonumberSchemeEa1ChsPeriod ,_bc .ST_TextAutonumberSchemeEa1ChtPeriod ,_bc .ST_TextAutonumberSchemeEa1JpnChsDbPeriod ,_bc .ST_TextAutonumberSchemeEa1JpnKorPeriod ,_bc .ST_TextAutonumberSchemeThaiAlphaPeriod ,_bc .ST_TextAutonumberSchemeThaiNumPeriod ,_bc .ST_TextAutonumberSchemeHindiAlphaPeriod ,_bc .ST_TextAutonumberSchemeHindiNumPeriod ,_bc .ST_TextAutonumberSchemeHindiAlpha1Period :_cgea ="\u002e";
case _bc .ST_TextAutonumberSchemeAlphaLcParenR ,_bc .ST_TextAutonumberSchemeAlphaUcParenR ,_bc .ST_TextAutonumberSchemeArabicParenR ,_bc .ST_TextAutonumberSchemeRomanLcParenR ,_bc .ST_TextAutonumberSchemeRomanUcParenR ,_bc .ST_TextAutonumberSchemeThaiAlphaParenR ,_bc .ST_TextAutonumberSchemeThaiNumParenR ,_bc .ST_TextAutonumberSchemeHindiNumParenR :_cgea ="\u0029";
};_acg =_dgde +_cgea ;_dgb ++;}else if _dbgg :=_ccee .TextBulletChoice .BuChar ;_dbgg !=nil {_gbg :=_dbgg .CharAttr ;if _fgg ,_cbf :=_fcgc [_gbg ];_cbf {_gbg =string (rune (_fgg ));}else {_gbg ="\u2022";};_acg =_gbg ;_fee =true ;};if _acg !=""{var _bbab ,_gff float64 ;
if _ccee .MarLAttr !=nil {_bbab =_ac .FromEMU (int64 (*_ccee .MarLAttr ));};if _ccee .IndentAttr !=nil {_gff =_ac .FromEMU (int64 (*_ccee .IndentAttr ));};_gbe =&prefixData {_dfdb :_acg ,_fbdb :_fee ,_ebeb :_bbab ,_cceg :_gff };};};_ccee =_bceg (_ccee ,_ddd );
_cggc :=_eggge (_adcf .EndParaRPr ,_ccee .DefRPr );_bbae .newParagraph ();_bbae .assignPropsToCurrentParagraph (_ccee );_bbae .newLine ();_bbae .newWord ();for _gaf ,_dcafc :=range _adcf .EG_TextRun {_bbae .addTextRun (_dcafc ,_cggc ,_dfbd ,_gbe );if _gaf > 0{_gbe =nil ;
};};_bbae .addCurrentWordToParagraph ();};_bbae .addCurrentParagraph ();};_bbae .alignVertically (_aeb );_bbae .drawParagraphs ();return _dd .MakeBlockFromCreator (_ecagc );};var _fcgc =map[string ]int32 {"\u0076":9830,"\u00d8":8594,"\u00fc":8730};

// Options contains the options for convert process
type Options struct{

// DefaultPageSize is applied when there is no page size explicitly set in the document.
// A4 is the default option.
DefaultPageSize _dd .PageSize ;};var _defa =[]romanMatch {romanMatch {1000,"\u006d"},romanMatch {900,"\u0063\u006d"},romanMatch {500,"\u0064"},romanMatch {400,"\u0063\u0064"},romanMatch {100,"\u0063"},romanMatch {90,"\u0078\u0063"},romanMatch {50,"\u006c"},romanMatch {40,"\u0078\u006c"},romanMatch {10,"\u0078"},romanMatch {9,"\u0069\u0078"},romanMatch {5,"\u0076"},romanMatch {4,"\u0069\u0076"},romanMatch {1,"\u0069"}};
func _aea (_bcb string )[]*symbol {_cbcd :=[]*symbol {};for _ ,_cfa :=range _bcb {_cbcd =append (_cbcd ,&symbol {_cfgb :string (_cfa )});};return _cbcd ;};