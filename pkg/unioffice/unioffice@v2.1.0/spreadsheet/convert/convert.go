//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package convert ;import (_c "github.com/unidoc/unioffice/v2/common/logger";_db "github.com/unidoc/unioffice/v2/common/tempstorage";_ec "github.com/unidoc/unioffice/v2/internal/convertutils";_ed "github.com/unidoc/unioffice/v2/measurement";_cf "github.com/unidoc/unioffice/v2/schema/soo/dml";
_cb "github.com/unidoc/unioffice/v2/schema/soo/dml/chart";_ef "github.com/unidoc/unioffice/v2/schema/soo/ofc/sharedTypes";_ebb "github.com/unidoc/unioffice/v2/schema/soo/sml";_fe "github.com/unidoc/unioffice/v2/spreadsheet";_a "github.com/unidoc/unioffice/v2/spreadsheet/formula";
_bf "github.com/unidoc/unioffice/v2/spreadsheet/reference";_eb "github.com/unidoc/unipdf/v3/creator";_fb "github.com/unidoc/unipdf/v3/model";_d "image";_e "math";_b "sort";_f "strconv";);

// Options contains the options for convert process
type Options struct{

// DefaultPageSize is applied when there is no page size explicitly set in the document.
// A4 is the default option.
DefaultPageSize _ec .PageSize ;};func (_dfec *convertContext )fillPages (){for _daaf ,_ddc :=range _dfec ._fgcg {_fga :=_dfec ._acce [_ddc ._cbcc :_ddc ._fca ];for _fbefg ,_dda :=range _fga {_bbg :=0;_aac :=0.0;_bce :=[]*cell {};if _dda ._dgeb {for _ ,_fggg :=range _dda ._baba {_add :=_dfec ._ebf [_bbg ];
_dfec ._bbgd =_add ._bdgdd [_daaf ];_dfec ._bbgd ._bcdd =true ;_gcag :=_fggg ._bfcb ;if _aac +_gcag > _add ._fbgbd {_dfec .addRowToPage (_bce ,_fbefg );_bce =[]*cell {_fggg };_aac =_gcag ;_bbg ++;}else {_fggg ._adgde =_aac ;_bce =append (_bce ,_fggg );
_aac +=_gcag ;};};if len (_bce )> 0{_eed :=_dfec ._ebf [_bbg ];_dfec ._bbgd =_eed ._bdgdd [_daaf ];_dfec ._bbgd ._bcdd =true ;_dfec .addRowToPage (_bce ,_fbefg );};};};};};type rowInfo struct{_gdc float64 ;_dgeb bool ;_eecd float64 ;_bcbb *style ;_baba []*cell ;
_ddff float64 ;};func (_bbf *convertContext )imageFromAnchor (_daga *anchor ,_agg ,_bgab float64 )_d .Image {if _daga ._deae !=nil {return _daga ._deae ;};if _daga ._fddc !=nil {_efef ,_baa :=_ec .MakeImageFromChartSpace (_daga ._fddc ,_agg ,_bgab ,_bbf ._deeg ,_bbf ._gagb );
if _baa !=nil {_c .Log .Debug ("C\u0061\u006e\u006e\u006f\u0074\u0020\u006d\u0061\u006b\u0065\u0020\u0061\u006e\u0020\u0069\u006d\u0061\u0067e\u0020\u0066\u0072\u006f\u006d\u0020\u0063\u0068\u0061\u0072tS\u0070\u0061\u0063e\u003a \u0025\u0073",_baa );return nil ;
};return _efef ;};return nil ;};func (_gecb *convertContext )makePagespans (){_gecb ._ebf =[]*pagespan {};_adgd :=0.0;_feda :=0;for _ecd ,_ggc :=range _gecb ._bag {_bcf :=_ggc ._fef ;if _adgd +_bcf <=_gecb ._beee {_ggc ._eefd =_adgd ;_adgd +=_bcf ;}else {_ggc ._eefd =0;
_gecb ._ebf =append (_gecb ._ebf ,&pagespan {_fbgbd :_adgd ,_ebfe :_feda ,_gdee :_ecd });_adgd =_bcf ;_feda =_ecd ;};};_gecb ._ebf =append (_gecb ._ebf ,&pagespan {_fbgbd :_adgd ,_ebfe :_feda ,_gdee :len (_gecb ._bag )});};const _df =2;

// ConvertToPdfWithOptions convert a sheet to PDF with given options.
func ConvertToPdfWithOptions (s *_fe .Sheet ,opts *Options )*_eb .Creator {_dfc :=s .X ();if _dfc ==nil {return nil ;};var _ce _eb .PageSize ;_ea :=true ;_fbf :=false ;if _dfc .SheetPr !=nil &&_dfc .SheetPr .PageSetUpPr !=nil &&_dfc .SheetPr .PageSetUpPr .FitToPageAttr !=nil &&*_dfc .SheetPr .PageSetUpPr .FitToPageAttr {_fbf =true ;
};if _cbc :=_dfc .PageSetup ;_cbc !=nil {_ea =_cbc .OrientationAttr ==_ebb .ST_OrientationLandscape ;if _eda :=_cbc .PaperSizeAttr ;_eda !=nil {_ce =_fgbd [*_eda ];};};if (_ce ==_eb .PageSize {}){_ce =_ec .GetDefaultPageSize ();if opts !=nil &&opts .DefaultPageSize !=_ec .DefaultPageSize {_ce =_ec .GetPageDimensions (opts .DefaultPageSize );
};};if _ea {_ce [0],_ce [1]=_ce [1],_ce [0];};_abf :=_eb .New ();_abf .SetPageSize (_ce );var _eg ,_aeb ,_fba ,_ee float64 ;if _ge :=_dfc .PageMargins ;_ge !=nil {_fba =_ge .LeftAttr ;_ee =_ge .RightAttr ;_eg =_ge .TopAttr ;_aeb =_ge .BottomAttr ;};if _fba < _dgg {_fba =_dgg ;
};if _ee < _dgg {_ee =_dgg ;};if _eg < _da {_eg =_da ;};if _aeb < _da {_aeb =_da ;};_eg *=_ed .Inch ;_aeb *=_ed .Inch ;_fba *=_ed .Inch ;_ee *=_ed .Inch ;_abf .SetPageMargins (_fba ,_ee ,_eg ,_aeb );_eaf :=s .Workbook ();var _dd *_cf .Theme ;if len (_eaf .Themes ())> 0{_dd =_eaf .Themes ()[0];
};var _gg ,_dc ,_gd ,_fbe int ;for _ ,_dcd :=range _eaf .DefinedNames (){if _dcd .Name ()=="\u005f\u0078l\u006e\u006d\u002eP\u0072\u0069\u006e\u0074\u005f\u0041\u0072\u0065\u0061"{_bc ,_be ,_gfg ,_ebc :=_ec .ParseExcelRange (_dcd .Content ());if _ebc ==nil &&s .Name ()==_bc {_gg =int (_be .ColumnIdx );
_dc =int (_gfg .ColumnIdx );_gd =int (_be .RowIdx );_fbe =int (_gfg .RowIdx );};};};_eaa :=[]_fe .Table {};if _dfc .TableParts !=nil &&_dfc .TableParts .TablePart !=nil {_ba :=0;_ca :=s .Workbook ().Tables ();_b .Slice (_ca [:],func (_bd ,_bdc int )bool {return _ca [_bd ].X ().IdAttr < _ca [_bdc ].X ().IdAttr });
for _ ,_dddf :=range s .Workbook ().Sheets (){if _dddf .Name ()==s .Name (){break ;}else {if _dddf .X ().TableParts !=nil &&_dddf .X ().TableParts .TablePart !=nil {_ba +=len (_dddf .X ().TableParts .TablePart );};};};if len (_ca )>=_ba +len (_dfc .TableParts .TablePart ){_eaa =append (_eaa ,_ca [_ba :_ba +len (_dfc .TableParts .TablePart )]...);
};};_bdf :=&convertContext {_gdb :_abf ,_bcdc :s ,_gagb :s .Workbook (),_deeg :_dd ,_bgc :&s .Workbook ().StyleSheet ,_daag :_eg ,_eeaaa :_fba ,_ddfc :_ce [1]-_aeb -_eg ,_beee :_ce [0]-_ee -_fba ,_cgf :_gg ,_abgc :_dc ,_fccd :_gd ,_gbd :_fbe ,_dea :_fbf ,_ggdd :_eaa };
_bdf .makeAnchors ();_bdf .determineMaxIndexes ();if _bdf ._dded ==0&&_bdf ._bfea ==0{_abf .NewPage ();return _abf ;};_bdf .makeCols ();_bdf .makeRows ();_bdf .makeMergedCells ();_bdf .makeCells ();_bdf .makePagespans ();_bdf .makeRowspans ();_bdf .makePages ();
_bdf .fillPages ();_bdf .distributeAnchors ();_bdf .drawSheet ();return _abf ;};func (_eccd *convertContext )addRowToPage (_aec []*cell ,_fbc int ){_efdf :=0.0;_ccaa :=_eccd ._beee ;for _ ,_eafg :=range _aec {if len (_eafg ._gbca )!=0{_eafg ._caae =_efdf ;
_efdf =_eafg ._adgde +_eafg ._fcge ;};};for _ggf :=len (_aec )-1;_ggf >=0;_ggf --{_agb :=_aec [_ggf ];if len (_agb ._gbca )!=0{_agb ._bbdfe =_ccaa ;_ccaa =_agb ._adgde ;};};_eccd ._bbgd ._bcbea =append (_eccd ._bbgd ._bcbea ,&pageRow {_egab :_fbc ,_ded :_aec });
};const _ae =15.0;func (_fab *convertContext )getSymbolsFromR (_edae []*_ebb .CT_RElt ,_adba *style )[]*symbol {_babe :=[]*symbol {};for _ ,_bgee :=range _edae {_gdf :=_fab .combineCellStyleWithRPrElt (_adba ,_bgee .RPr );for _ ,_ggdc :=range _bgee .T {_babe =append (_babe ,&symbol {_ggcb :string (_ggdc ),_bacc :_fab .makeTextStyleFromCellStyle (_gdf )});
};};return _babe ;};type line struct{_eac float64 ;_eaec []*symbol ;_fedf float64 ;};func (_dgc *convertContext )makeCells (){_fed :=_dgc ._bcdc ;_cae :=_fed .Rows ();_gbe :=0;for _cbac ,_affg :=range _dgc ._acce {if _cbac < _dgc ._fccd ||(_cbac > _dgc ._gbd &&_dgc ._gbd > 0){continue ;
};_affg ._baba =[]*cell {};_eef :=0.0;_bge :=_affg ._bcbb ;if _affg ._dgeb {_cbf :=_cae [_gbe ];_gbe ++;_aga :=_affg ._eecd ;for _ ,_adb :=range _cbf .Cells (){_fc ,_dgb :=_bf .ParseCellReference (_adb .Reference ());if _dgb !=nil {_c .Log .Debug ("\u0043\u0061\u006e\u006eo\u0074\u0020\u0070\u0061\u0072\u0073\u0065\u0020\u0061\u0020r\u0065f\u0065\u0072\u0065\u006e\u0063\u0065\u003a \u0025\u0073",_dgb );
continue ;};if int (_fc .ColumnIdx )< _dgc ._cgf ||(int (_fc .ColumnIdx )> _dgc ._abgc &&_dgc ._abgc > 0){continue ;};_gca :=_dgc ._bag [_fc .ColumnIdx ];_edb :=_gca ._fef ;_bfac :=_edb ;_bfag :=_gca ._ddga ;var _acb ,_ffa ,_fbef ,_deff bool ;for _ ,_abc :=range _dgc ._cab {if _fc .RowIdx >=_abc ._abde &&_fc .RowIdx <=_abc ._ccdf &&_fc .ColumnIdx >=_abc ._dcaf &&_fc .ColumnIdx <=_abc ._acfaa {if _fc .ColumnIdx ==_abc ._dcaf &&_fc .RowIdx ==_abc ._abde {_edb =_abc ._fgbc ;
_aga =_abc ._baag ;};_acb =_fc .RowIdx !=_abc ._abde ;_ffa =_fc .RowIdx !=_abc ._ccdf ;_fbef =_fc .ColumnIdx !=_abc ._dcaf ;_deff =_fc .ColumnIdx !=_abc ._acfaa ;};};var _deg *style ;for _ ,_gefa :=range _dgc ._ggdd {_fcg ,_adg ,_aee :=_bf .ParseRangeReference (_gefa .Reference ());
if _aee !=nil ||_fcg .RowIdx > _fc .RowIdx ||_fcg .ColumnIdx > _fc .ColumnIdx ||_adg .RowIdx < _fc .RowIdx ||_adg .ColumnIdx < _fc .ColumnIdx ||_dgc ._gagb .StyleSheet .X ().TableStyles ==nil {continue ;};_bea :=_fc .RowIdx ==_fcg .RowIdx ;for _ ,_deb :=range _dgc ._gagb .StyleSheet .X ().TableStyles .TableStyle {if _gefa .X ().TableStyleInfo .NameAttr !=nil &&_deb .NameAttr ==*_gefa .X ().TableStyleInfo .NameAttr {for _ ,_edad :=range _deb .TableStyleElement {if !_bea &&_edad .TypeAttr ==_ebb .ST_TableStyleTypeWholeTable {_deg =_dgc .getDxfStyle (_edad .DxfIdAttr );
};if _bea &&_edad .TypeAttr ==_ebb .ST_TableStyleTypeHeaderRow {_deg =_dgc .getDxfStyle (_edad .DxfIdAttr );};};};};};_geg :=_dgc .getStyleFromCell (_adb ,_bge ,_bfag ,_deg );var _cagg ,_cfa ,_agd ,_ega bool ;var _cd ,_dgba ,_gfc ,_dgd *border ;var _ffc _ebb .ST_VerticalAlignment ;
var _dggf _ebb .ST_HorizontalAlignment ;if _geg !=nil {if !_acb {_cd =_geg ._fdad ;};if !_ffa {_dgba =_geg ._ebef ;};if !_fbef {_gfc =_geg ._eegg ;};if !_deff {_dgd =_geg ._adbe ;};if _dgba !=nil &&_dgba ._bbed > _eef {_eef =_dgba ._bbed ;};_ffc =_geg ._ecbf ;
_dggf =_geg ._badd ;if _geg ._gfcb !=nil {_cagg =*_geg ._gfcb ;};if _geg ._ecg !=nil {_cfa =*_geg ._ecg ;};_agd =_geg ._dcf ;_ega =_geg ._dfb ;};var _fa _eb .Color ;if _geg !=nil &&_geg ._fcfe !=nil {_fa =_eb .ColorRGBFromHex (*_geg ._fcfe );};_bcd ,_eebf :=_dgc .getContentFromCell (_fed ,_adb ,_geg ,_edb ,_agd ,_ega );
_deed :=&cell {_gdbc :_eebf ,_fcge :_edb ,_bfcb :_bfac ,_bfcef :_aga ,_gbca :_bcd ,_aggf :_cd ,_bddg :_dgba ,_becf :_gfc ,_gea :_dgd ,_cfgg :_cagg ,_eeeca :_cfa ,_dcgbb :_fa };_dgc .alignSymbolsHorizontally (_deed ,_dggf );_dgc .alignSymbolsVertically (_deed ,_ffc );
_affg ._baba =append (_affg ._baba ,_deed );};};_affg ._ddff =_eef ;};};func (_fcgg *convertContext )makeTextStyleFromCellStyle (_gagc *style )*_eb .TextStyle {_fcd :=_fcgg ._gdb .NewTextStyle ();if _gagc ==nil {_fcd .FontSize =_ec .DefaultFontSize ;_fcd .Font =_ec .AssignStdFontByName (_fcd ,_ec .StdFontsMap ["\u0064e\u0066\u0061\u0075\u006c\u0074"][FontStyle_Regular ]);
return &_fcd ;};if _acda (_gagc ._egd ){_fcd .Underline =true ;_fcd .UnderlineStyle =_eb .TextDecorationLineStyle {Offset :0.5,Thickness :_daad (1/32)};};var _gfea FontStyle ;if _acda (_gagc ._fgac )&&_acda (_gagc ._dbba ){_gfea =FontStyle_BoldItalic ;
}else if _acda (_gagc ._fgac ){_gfea =FontStyle_Bold ;}else if _acda (_gagc ._dbba ){_gfea =FontStyle_Italic ;}else {_gfea =FontStyle_Regular ;};_gfbe :="\u0064e\u0066\u0061\u0075\u006c\u0074";if _gagc ._fggdg !=nil {_gfbe =*_gagc ._fggdg ;};if _efb ,_ccbg :=_ec .StdFontsMap [_gfbe ];
_ccbg {_fcd .Font =_ec .AssignStdFontByName (_fcd ,_efb [_gfea ]);}else if _deede :=_ec .GetRegisteredFont (_gfbe ,_gfea );_deede !=nil {_fcd .Font =_deede ;}else {_c .Log .Debug ("\u0046\u006f\u006e\u0074\u0020\u0025\u0073\u0020\u0077\u0069\u0074h\u0020\u0073\u0074\u0079\u006c\u0065\u0020\u0025s\u0020i\u0073\u0020\u006e\u006f\u0074\u0020\u0066\u006f\u0075\u006e\u0064\u002c\u0020\u0072\u0065\u0073\u0065\u0074 \u0074\u006f\u0020\u0064\u0065\u0066\u0061\u0075\u006c\u0074\u002e",_gfbe ,_gfea );
_fcd .Font =_ec .AssignStdFontByName (_fcd ,_ec .StdFontsMap ["\u0064e\u0066\u0061\u0075\u006c\u0074"][_gfea ]);};if _gagc ._bacb !=nil {_fcd .FontSize =_e .Round (*_gagc ._bacb *_fcgg ._bda );};if _gagc ._agga !=nil {_fcd .Color =_eb .ColorRGBFromHex (*_gagc ._agga );
};if _gagc ._gfcb !=nil &&*_gagc ._gfcb {_fcd .FontSize *=_dg ;}else if _gagc ._ecg !=nil &&*_gagc ._ecg {_fcd .FontSize *=_dg ;};return &_fcd ;};const _dg =0.64;func (_gad *convertContext )combineCellStyleWithRPrElt (_fefd *style ,_ecfc *_ebb .CT_RPrElt )*style {_cbfg :=*_fefd ;
_dace :=_gad .getStyleFromRPrElt (_ecfc );if _dace ==nil {return &_cbfg ;};if _dace ._agga !=nil {_cbfg ._agga =_dace ._agga ;};if _dace ._bacb !=nil {_cbfg ._bacb =_dace ._bacb ;};if _dace ._fggdg !=nil {_cbfg ._fggdg =_dace ._fggdg ;};if _dace ._fgac !=nil {_cbfg ._fgac =_dace ._fgac ;
};if _dace ._dbba !=nil {_cbfg ._dbba =_dace ._dbba ;};if _dace ._egd !=nil {_cbfg ._egd =_dace ._egd ;};if _dace ._gfcb !=nil {_cbfg ._gfcb =_dace ._gfcb ;};if _dace ._ecg !=nil {_cbfg ._ecg =_dace ._ecg ;};return &_cbfg ;};var _fg =_daad (1);func (_ddac *convertContext )drawPage (_dde *page ){_fcf :=_ddac ._daag ;
_gag :=_ddac ._eeaaa ;for _ ,_acd :=range _dde ._bcbea {_bab :=_ddac ._acce [_acd ._egab ];for _ ,_agdb :=range _acd ._ded {var _faf float64 ;if _acd ._egab > 1{_faf =_ddac ._acce [_acd ._egab -1]._ddff ;};var _cac ,_aaf float64 ;if _aacd :=_agdb ._aggf ;
_aacd !=nil {_cac =_aacd ._bbed ;};if _gaf :=_agdb ._bddg ;_gaf !=nil {_aaf =_gaf ._bbed ;};_cbg :=_fcf +_bab ._gdc -0.5*(_faf -_cac );_ade :=_fcf +_bab ._gdc +_bab ._eecd +0.5*(_bab ._ddff +_aaf );_bbe :=_gag +_agdb ._adgde ;_feca :=_bbe +_agdb ._bfcb ;
if _agdb ._dcgbb !=nil &&_agdb ._dcgbb !=_eb .ColorBlack {_ec .FillRectangle (_ddac ._gdb ,_bbe ,_cbg ,_feca -_bbe ,_ade -_cbg ,_agdb ._dcgbb );};};};for _ ,_dfede :=range _dde ._bcbea {_cgdc :=_ddac ._acce [_dfede ._egab ];for _ ,_acfa :=range _dfede ._ded {_gagf :=_acfa ._caae < _acfa ._adgde ;
_fbec :=_acfa ._bbdfe > _acfa ._adgde +_acfa ._fcge ;var _bec ,_gdga bool ;for _ ,_abdf :=range _acfa ._gbca {for _ ,_ddee :=range _abdf ._eaec {if _gagf &&!_bec {_bec =_ddee ._fdb < 0;};if _fbec &&!_gdga {_gdga =_acfa ._fcge < _ddee ._fdb +_ddee ._becd ;
};if _acfa ._adgde +_ddee ._fdb >=_acfa ._caae &&_acfa ._adgde +_ddee ._fdb +_ddee ._becd <=_acfa ._bbdfe {_bddf :=_ddac ._gdb .NewStyledParagraph ();_cbab :=_gag +_acfa ._adgde +_ddee ._fdb ;_afbe :=_fcf +_cgdc ._gdc +_abdf ._eac -_ddee ._edaa -_daad (0.5);
_bddf .SetPos (_cbab ,_afbe );var _bdfd *_eb .TextChunk ;if _ddee ._gecd !=""{_bdfd =_bddf .AddExternalLink (_ddee ._ggcb ,_ddee ._gecd );}else {_bdfd =_bddf .Append (_ddee ._ggcb );};if _ddee ._bacc !=nil {_bdfd .Style =*_ddee ._bacc ;};_ddac ._gdb .Draw (_bddf );
};};};var _eba ,_fedc ,_dbd ,_bdggb ,_dbaa ,_egb float64 ;var _afd ,_beab ,_cfea ,_bgf _eb .Color ;if _ffd :=_acfa ._aggf ;_ffd !=nil {_eba =_ffd ._bbed ;_afd =_ffd ._ecbe ;};if _feb :=_acfa ._bddg ;_feb !=nil {_fedc =_feb ._bbed ;_beab =_feb ._ecbe ;};
if _eff :=_acfa ._becf ;_eff !=nil {_dbd =_eff ._bbed ;_dbaa =_dbd /2;_cfea =_eff ._ecbe ;};if _fcc :=_acfa ._gea ;_fcc !=nil {_bdggb =_fcc ._bbed ;_egb =_bdggb /2;_bgf =_fcc ._ecbe ;};var _aagg float64 ;if _dfede ._egab > 1{_aagg =_ddac ._acce [_dfede ._egab -1]._ddff ;
};_cdg :=_fcf +_cgdc ._gdc -0.5*(_aagg -_eba );_egcg :=_fcf +_cgdc ._gdc +_cgdc ._eecd +0.5*(_cgdc ._ddff +_fedc );_dcg :=_gag +_acfa ._adgde ;_gfcd :=_dcg +_acfa ._bfcb ;_ec .DrawLine (_ddac ._gdb ,_dcg ,_cdg ,_gfcd ,_cdg ,_eba ,_afd );_ec .DrawLine (_ddac ._gdb ,_dcg ,_egcg ,_gfcd ,_egcg ,_fedc ,_beab );
if !_bec {_ec .DrawLine (_ddac ._gdb ,_dcg -_dbaa ,_cdg ,_dcg -_dbaa ,_egcg ,_dbd ,_cfea );};if !_gdga {_ec .DrawLine (_ddac ._gdb ,_gfcd -_egb ,_cdg ,_gfcd -_egb ,_egcg ,_bdggb ,_bgf );};};};for _ ,_cfg :=range _dde ._abfd {if _cfg !=nil {_ddac ._gdb .Draw (_cfg );
};};};func _bfe (_becc *symbol ){_acaf :=_eb .New ();_bbb :=_acaf .NewStyledParagraph ();_bbb .SetMargins (0,0,0,0);_bga :=_bbb .Append (_becc ._ggcb );if _becc ._bacc !=nil {_bga .Style =*_becc ._bacc ;};_becc ._edaa =_bbb .Height ();if _becc ._becd ==0{_becc ._becd =_bbb .Width ();
};};func (_gef *convertContext )makeRows (){_ag :=[]*rowInfo {};_fdc :=_gef ._bcdc .Rows ();_daa :=0;_gefg :=0.0;for _ebgg ,_edf :=range _fdc {if _ebgg < _gef ._fccd ||(_ebgg > _gef ._gbd &&_gef ._gbd > 0){continue ;};_daa ++;_gfb :=int (_edf .RowNumber ());
if _gfb > _daa {for _dag :=_daa ;_dag < _gfb ;_dag ++{_ag =append (_ag ,&rowInfo {_eecd :_ae /_ff });_gefg +=_ae /_ff ;};_daa =_gfb ;};var _bbd float64 ;if _edf .X ().HtAttr ==nil {_bbd =_ae ;}else {_bbd =*_edf .X ().HtAttr ;};_ag =append (_ag ,&rowInfo {_eecd :_bbd /_ff ,_dgeb :true ,_bcbb :_gef .getStyle (_edf .X ().SAttr )});
_gefg +=_bbd /_ff ;};for _cc :=len (_ag );_cc < _gef ._dded ;_cc ++{_ag =append (_ag ,&rowInfo {_eecd :_ae /_ff });_gefg +=_ae /_ff ;};if _gef ._dea ||_gefg >=_gef ._ddfc {_fgc :=_gef ._bda ;if _gef ._ddfc /_gefg < _gef ._bda {_fgc =_gef ._ddfc /_gefg ;
};for _ ,_ad :=range _ag {_ad ._eecd *=_fgc ;};};_gef ._acce =_ag ;};func _cfdf (_daafb []*symbol )float64 {_edba :=0.0;for _ ,_dbbf :=range _daafb {_edba +=_dbbf ._becd ;};return _edba ;};func _acfc (_ebd ,_cbdde *style ){if _cbdde ==nil {return ;};if _ebd ==nil {_ebd =_cbdde ;
return ;};if _ebd ._fcfe ==nil {_ebd ._fcfe =_cbdde ._fcfe ;};if _ebd ._fggdg ==nil {_ebd ._fggdg =_cbdde ._fggdg ;};if _ebd ._agga ==nil {_ebd ._agga =_cbdde ._agga ;};if _ebd ._bacb ==nil {_ebd ._bacb =_cbdde ._bacb ;};if _ebd ._fgac ==nil {_ebd ._fgac =_cbdde ._fgac ;
};if _ebd ._dbba ==nil {_ebd ._dbba =_cbdde ._dbba ;};if _ebd ._egd ==nil {_ebd ._egd =_cbdde ._egd ;};if _ebd ._gfcb ==nil {_ebd ._gfcb =_cbdde ._gfcb ;};if _ebd ._ecg ==nil {_ebd ._ecg =_cbdde ._ecg ;};if _ebd ._fdad ==nil {_ebd ._fdad =_cbdde ._fdad ;
};if _ebd ._ebef ==nil {_ebd ._ebef =_cbdde ._ebef ;};if _ebd ._eegg ==nil {_ebd ._eegg =_cbdde ._eegg ;};if _ebd ._adbe ==nil {_ebd ._adbe =_cbdde ._adbe ;};if _ebd ._ecbf ==_ebb .ST_VerticalAlignmentUnset {_ebd ._ecbf =_cbdde ._ecbf ;};if _ebd ._badd ==_ebb .ST_HorizontalAlignmentUnset {_ebd ._badd =_cbdde ._badd ;
};};const _dgg =0.25;var _cdf =[]string {"\u0030\u0030\u0030\u0030\u0030\u0030","\u0066\u0066\u0066\u0066\u0066\u0066","\u0066\u0066\u0030\u0030\u0030\u0030","\u0030\u0030\u0066\u0066\u0030\u0030","\u0030\u0030\u0030\u0030\u0066\u0066","\u0066\u0066\u0066\u0066\u0030\u0030","\u0066\u0066\u0030\u0030\u0066\u0066","\u0030\u0030\u0066\u0066\u0066\u0066","\u0030\u0030\u0030\u0030\u0030\u0030","\u0066\u0066\u0066\u0066\u0066\u0066","\u0066\u0066\u0030\u0030\u0030\u0030","\u0030\u0030\u0066\u0066\u0030\u0030","\u0030\u0030\u0030\u0030\u0066\u0066","\u0066\u0066\u0066\u0066\u0030\u0030","\u0066\u0066\u0030\u0030\u0066\u0066","\u0030\u0030\u0066\u0066\u0066\u0066","\u0038\u0030\u0030\u0030\u0030\u0030","\u0030\u0030\u0038\u0030\u0030\u0030","\u0030\u0030\u0030\u0030\u0038\u0030","\u0038\u0030\u0038\u0030\u0030\u0030","\u0038\u0030\u0030\u0030\u0038\u0030","\u0030\u0030\u0038\u0030\u0038\u0030","\u0063\u0030\u0063\u0030\u0063\u0030","\u0038\u0030\u0038\u0030\u0038\u0030","\u0039\u0039\u0039\u0039\u0066\u0066","\u0039\u0039\u0033\u0033\u0036\u0036","\u0066\u0066\u0066\u0066\u0063\u0063","\u0063\u0063\u0066\u0066\u0066\u0066","\u0036\u0036\u0030\u0030\u0036\u0036","\u0066\u0066\u0038\u0030\u0038\u0030","\u0030\u0030\u0036\u0036\u0063\u0063","\u0063\u0063\u0063\u0063\u0066\u0066","\u0030\u0030\u0030\u0030\u0038\u0030","\u0066\u0066\u0030\u0030\u0066\u0066","\u0066\u0066\u0066\u0066\u0030\u0030","\u0030\u0030\u0066\u0066\u0066\u0066","\u0038\u0030\u0030\u0030\u0038\u0030","\u0038\u0030\u0030\u0030\u0030\u0030","\u0030\u0030\u0038\u0030\u0038\u0030","\u0030\u0030\u0030\u0030\u0066\u0066","\u0030\u0030\u0063\u0063\u0066\u0066","\u0063\u0063\u0066\u0066\u0066\u0066","\u0063\u0063\u0066\u0066\u0063\u0063","\u0066\u0066\u0066\u0066\u0039\u0039","\u0039\u0039\u0063\u0063\u0066\u0066","\u0066\u0066\u0039\u0039\u0063\u0063","\u0063\u0063\u0039\u0039\u0066\u0066","\u0066\u0066\u0063\u0063\u0039\u0039","\u0033\u0033\u0036\u0036\u0066\u0066","\u0033\u0033\u0063\u0063\u0063\u0063","\u0039\u0039\u0063\u0063\u0030\u0030","\u0066\u0066\u0063\u0063\u0030\u0030","\u0066\u0066\u0039\u0039\u0030\u0030","\u0066\u0066\u0036\u0036\u0030\u0030","\u0036\u0036\u0036\u0036\u0039\u0039","\u0039\u0036\u0039\u0036\u0039\u0036","\u0030\u0030\u0033\u0033\u0036\u0036","\u0033\u0033\u0039\u0039\u0036\u0036","\u0030\u0030\u0033\u0033\u0030\u0030","\u0033\u0033\u0033\u0033\u0030\u0030","\u0039\u0039\u0033\u0033\u0030\u0030","\u0039\u0039\u0033\u0033\u0036\u0036","\u0033\u0033\u0033\u0033\u0039\u0039","\u0033\u0033\u0033\u0033\u0033\u0033"};
func (_gec *convertContext )makeMergedCells (){_fgg :=[]*mergedCell {};for _ ,_eea :=range _gec ._bcdc .MergedCells (){_eag ,_eaaf ,_age :=_bf .ParseRangeReference (_eea .Reference ());if _age !=nil {_c .Log .Debug ("\u0065\u0072r\u006f\u0072\u0020\u0070\u0061\u0072\u0073\u0069\u006e\u0067\u0020\u006d\u0065\u0072\u0067\u0065\u0064\u0020\u0063\u0065\u006c\u006c: \u0025\u0073",_age );
continue ;};_bac :=mergedCell {_abde :_eag .RowIdx ,_dcaf :_eag .ColumnIdx ,_ccdf :_eaaf .RowIdx ,_acfaa :_eaaf .ColumnIdx };for _aab :=_bac ._dcaf -1;_aab < _bac ._acfaa ;_aab ++{_bac ._fgbc +=_gec ._bag [_aab ]._fef ;};for _bfg :=_bac ._abde -1;_bfg < _bac ._ccdf ;
_bfg ++{_bac ._baag +=_gec ._acce [_bfg ]._eecd ;};_fgg =append (_fgg ,&_bac );};_gec ._cab =_fgg ;};func _acda (_babf *bool )bool {return _babf !=nil &&*_babf };func (_babb *convertContext )getDxfStyle (_gfd *uint32 )*style {_ecf :=&style {};_bfacd :=false ;
if _gfd !=nil {var _acba *_ebb .CT_Dxf ;for _bgae ,_bfga :=range _babb ._bgc .X ().Dxfs .Dxf {if uint32 (_bgae )==*_gfd {_acba =_bfga ;};};if _acba ==nil {return nil ;};if _acba .Fill !=nil &&_acba .Fill .FillChoice !=nil &&_acba .Fill .FillChoice .PatternFill !=nil {if _acba .Fill .FillChoice .PatternFill .FgColor !=nil {_ecf ._fcfe =_babb .getColorStringFromSmlColor (_acba .Fill .FillChoice .PatternFill .FgColor );
}else if _acba .Fill .FillChoice .PatternFill .BgColor !=nil {_ecf ._fcfe =_babb .getColorStringFromSmlColor (_acba .Fill .FillChoice .PatternFill .BgColor );};};_bafc :=_acba .Font ;for _ ,_fffg :=range _bafc .FontChoice {if _fffg .Name !=nil {_ecf ._fggdg =&_fffg .Name .ValAttr ;
_bfacd =true ;}else if _fffg .B !=nil {_fdee :=_fffg .B .ValAttr ==nil ||*_fffg .B .ValAttr ;_ecf ._fgac =&_fdee ;_bfacd =true ;}else if _fffg .I !=nil {_dfee :=_fffg .I .ValAttr ==nil ||*_fffg .I .ValAttr ;_ecf ._dbba =&_dfee ;_bfacd =true ;}else if _fffg .U !=nil {_gfdc :=_fffg .U .ValAttr ==_ebb .ST_UnderlineValuesSingle ||_fffg .U .ValAttr ==_ebb .ST_UnderlineValuesUnset ;
_ecf ._egd =&_gfdc ;_bfacd =true ;}else if _fffg .Sz !=nil {_fgdc :=_fffg .Sz .ValAttr ;_ecf ._bacb =&_fgdc ;_bfacd =true ;}else if _fffg .VertAlign !=nil {_dac :=_fffg .VertAlign .ValAttr ==_ef .ST_VerticalAlignRunSuperscript ;_ecf ._gfcb =&_dac ;_aacf :=_fffg .VertAlign .ValAttr ==_ef .ST_VerticalAlignRunSubscript ;
_ecf ._ecg =&_aacf ;_bfacd =true ;}else if _fffg .Color !=nil {_ecf ._agga =_babb .getColorStringFromSmlColor (_fffg .Color );_bfacd =true ;};};_bfgf :=_acba .Border ;if _bfgf !=nil {if _bfgf .Top !=nil {_ecf ._fdad =_babb .getBorder (_bfgf .Top );_bfacd =true ;
};if _bfgf .Bottom !=nil {_ecf ._ebef =_babb .getBorder (_bfgf .Bottom );_bfacd =true ;};if _bfgf .Left !=nil {_ecf ._eegg =_babb .getBorder (_bfgf .Left );_bfacd =true ;};if _bfgf .Right !=nil {_ecf ._adbe =_babb .getBorder (_bfgf .Right );_bfacd =true ;
};};if _acba .Alignment !=nil {if _bef :=_acba .Alignment .VerticalAttr ;_bef !=_ebb .ST_VerticalAlignmentUnset {_ecf ._ecbf =_bef ;_bfacd =true ;};if _bgfd :=_acba .Alignment .HorizontalAttr ;_bgfd !=_ebb .ST_HorizontalAlignmentUnset {_ecf ._badd =_bgfd ;
_bfacd =true ;};};};if _bfacd {return _ecf ;};return nil ;};type style struct{_fcfe *string ;_agga *string ;_bacb *float64 ;_fggdg *string ;_fgac *bool ;_dbba *bool ;_egd *bool ;_gfcb *bool ;_ecg *bool ;_fdad *border ;_ebef *border ;_eegg *border ;_adbe *border ;
_dcf bool ;_ecbf _ebb .ST_VerticalAlignment ;_badd _ebb .ST_HorizontalAlignment ;_dfb bool ;};type rowspan struct{_ccc float64 ;_cbcc int ;_fca int ;};type mergedCell struct{_abde uint32 ;_dcaf uint32 ;_ccdf uint32 ;_acfaa uint32 ;_fgbc float64 ;_baag float64 ;
};func (_caf *convertContext )makeCols (){_cgd :=_caf ._bcdc ;_dfd :=_cgd .X ();_afa :=[]*colInfo {};_acf :=[]colWidthRange {};if _gcb :=_dfd .Cols ;len (_gcb )> 0{for _ ,_cad :=range _gcb [0].Col {_dfe :=_dgga ;if _dbc :=_cad .WidthAttr ;_dbc !=nil {if *_dbc > 0.83{*_dbc -=0.83;
};if *_dbc <=1{_dfe =*_dbc *11;}else {_dfe =5+*_dbc *6;};};_bg :=int (_cad .MinAttr -1);_cag :=int (_cad .MaxAttr -1);_acf =append (_acf ,colWidthRange {_geag :_bg ,_gdgc :_cag ,_eece :_dfe ,_geb :_caf .getStyle (_cad .StyleAttr )});};};_efd :=0;for _bcb :=0;
_bcb <=_caf ._bfea ;_bcb ++{if _bcb < _caf ._cgf ||(_bcb > _caf ._abgc &&_caf ._abgc > 0){continue ;};var _ffe float64 ;var _fd *style ;if len (_acf )==0{_ffe =_dgga ;}else if _efd < len (_acf ){_def :=_acf [_efd ];if _bcb >=_def ._geag &&_bcb <=_def ._gdgc {_ffe =_def ._eece ;
_fd =_def ._geb ;if _bcb ==_def ._gdgc {_efd ++;};}else {continue ;};};_afa =append (_afa ,&colInfo {_fef :_ffe ,_ddga :_fd });};_bdgg :=0.0;for _ ,_bca :=range _afa {_bdgg +=_bca ._fef ;};_caf ._bda =1.0;if _bdgg > _caf ._beee {_caf ._bda =_caf ._beee /_bdgg ;
for _ ,_aa :=range _afa {_aa ._fef *=_caf ._bda ;};};_caf ._bag =_afa ;};type cell struct{_gdbc _ebb .ST_CellType ;_cbgf int ;_adgde float64 ;_gbca []*line ;_fcge float64 ;_bfcb float64 ;_bfcef float64 ;_caae float64 ;_bbdfe float64 ;_baea *_eb .TextStyle ;
_aggf *border ;_bddg *border ;_becf *border ;_gea *border ;_cfgg bool ;_eeeca bool ;_dcgbb _eb .Color ;};func (_fdff *convertContext )alignSymbolsVertically (_fce *cell ,_bdgd _ebb .ST_VerticalAlignment ){var _cbfa float64 ;switch _bdgd {case _ebb .ST_VerticalAlignmentTop :_cbfa =_df ;
if _fce ._cfgg {_cbfa -=_ab ;}else if _fce ._eeeca {_cbfa +=4*_ab ;};for _ ,_acbg :=range _fce ._gbca {_cbfa +=_acbg ._fedf ;_acbg ._eac =_cbfa ;_cbfa +=_fg ;};case _ebb .ST_VerticalAlignmentCenter :_bade :=0.0;for _ ,_acbe :=range _fce ._gbca {_bade +=_acbe ._fedf +_daad (1);
};_cbfa =0.5*(_fce ._bfcef -_bade );if _fce ._cfgg {_cbfa -=2*_ab ;}else if _fce ._eeeca {_cbfa +=2*_ab ;};for _ ,_afc :=range _fce ._gbca {_cbfa +=_afc ._fedf +0.5*_fg ;_afc ._eac =_cbfa ;_cbfa +=0.5*_fg ;};default:_cbfa =_fce ._bfcef -_df ;if _fce ._cfgg {_cbfa -=4*_ab ;
}else if _fce ._eeeca {_cbfa +=_ab ;};for _fgaf :=len (_fce ._gbca )-1;_fgaf >=0;_fgaf --{_fce ._gbca [_fgaf ]._eac =_cbfa ;_cbfa -=_fce ._gbca [_fgaf ]._fedf ;_cbfa -=_fg ;};};};func (_eeca *convertContext )makePages (){for _ ,_edff :=range _eeca ._ebf {for _ ,_fea :=range _eeca ._fgcg {_edff ._bdgdd =append (_edff ._bdgdd ,&page {_bcbea :[]*pageRow {},_afac :_edff ,_bdda :_fea });
};};};const _af =3;func (_ebe *convertContext )makeRowspans (){var _baef float64 ;_beg :=0;for _feg ,_eec :=range _ebe ._acce {_dcbcd :=_eec ._eecd +_eec ._ddff ;if _baef +_dcbcd <=_ebe ._ddfc {_eec ._gdc =_baef ;_baef +=_dcbcd ;}else {_ebe ._fgcg =append (_ebe ._fgcg ,&rowspan {_ccc :_baef ,_cbcc :_beg ,_fca :_feg });
_beg =_feg ;_eec ._gdc =0;_baef =_dcbcd ;};};_ebe ._fgcg =append (_ebe ._fgcg ,&rowspan {_ccc :_baef ,_cbcc :_beg ,_fca :len (_ebe ._acce )});};type colInfo struct{_eefd float64 ;_fef float64 ;_ddga *style ;};func (_abbe *convertContext )getContentFromCell (_gfcc *_fe .Sheet ,_fgab _fe .Cell ,_ddea *style ,_gfba float64 ,_fad ,_dge bool )([]*line ,_ebb .ST_CellType ){if _dge {return []*line {},_ebb .ST_CellTypeS ;
};_gde :=_fgab .X ();var _gcf []*symbol ;switch _gde .TAttr {case _ebb .ST_CellTypeS :_fbg :=_gde .V ;if _fbg !=nil {_efe ,_dfa :=_f .Atoi (*_fbg );if _dfa ==nil {_egf :=_abbe ._gagb .SharedStrings .X ().Si [_efe ];if _egf .T !=nil {_gcf =_abbe .getSymbolsFromString (*_egf .T ,_ddea );
}else if _egf .R !=nil {_gcf =_abbe .getSymbolsFromR (_egf .R ,_ddea );};};};case _ebb .ST_CellTypeB :_gcff :=_gde .V ;if _gcff !=nil {if *_gcff =="\u0030"{_gcf =_abbe .getSymbolsFromString ("\u0046\u0041\u004cS\u0045",_ddea );}else {_gcf =_abbe .getSymbolsFromString ("\u0054\u0052\u0055\u0045",_ddea );
};};case _ebb .ST_CellTypeStr :if _gde .F !=nil {_dbg :=_a .NewEvaluator ();_fcca :=_gfcc .FormulaContext ().Cell (_fgab .Reference (),_dbg );_gcf =_abbe .getSymbolsFromString (_fcca .Value (),_ddea );};default:_gcf =_abbe .getSymbolsFromString (_fgab .GetFormattedValue (),_ddea );
};_bee :=0.0;_dcag :=0.0;var _adc []*line ;var _afg bool ;if _ddea !=nil {if _ddea ._gfcb !=nil {if *_ddea ._gfcb {_afg =true ;};};if _ddea ._ecg !=nil {if *_ddea ._ecg {_afg =true ;};};};if _fad {_adc =[]*line {};_bfce :=_gfba -2*_af ;_gdea :=[]*symbol {};
for _ ,_fde :=range _gcf {_bfe (_fde );if _bee +_fde ._becd >=_bfce {_ggd :=_bgd (_gdea );if _afg {_ggd /=_dg ;};_adc =append (_adc ,&line {_eac :_dcag ,_eaec :_gdea ,_fedf :_ggd });_gdea =[]*symbol {_fde };_bee =_fde ._becd ;_dcag +=_ggd ;}else {_fde ._fdb =_bee ;
_bee +=_fde ._becd ;_gdea =append (_gdea ,_fde );};};_ccd :=_bgd (_gdea );if _afg {_ccd /=_dg ;};if len (_gdea )> 0{_adc =append (_adc ,&line {_eac :_dcag ,_eaec :_gdea ,_fedf :_ccd });};}else {for _ ,_cgdcf :=range _gcf {_bfe (_cgdcf );_cgdcf ._fdb =_bee ;
_bee +=_cgdcf ._becd ;};if len (_gcf )> 0{_adc =[]*line {&line {_eaec :_gcf ,_fedf :_bgd (_gcf )}};};};_aafa :=_gde .TAttr ;if _aafa ==_ebb .ST_CellTypeUnset {_aafa =_ebb .ST_CellTypeN ;};return _adc ,_aafa ;};func (_eega *convertContext )getStyleFromRPrElt (_bgb *_ebb .CT_RPrElt )*style {if _bgb ==nil ||_bgb .RPrEltChoice ==nil ||len (_bgb .RPrEltChoice )==0{return nil ;
};_cbdd :=&style {};for _ ,_cce :=range _bgb .RPrEltChoice {if _cce .RFont !=nil {_cbdd ._fggdg =&_cce .RFont .ValAttr ;};if _ffaa :=_cce .B ;_ffaa !=nil {_baae :=_ffaa .ValAttr ==nil ||*_ffaa .ValAttr ;_cbdd ._fgac =&_baae ;};if _becdc :=_cce .I ;_becdc !=nil {_egabc :=_becdc .ValAttr ==nil ||*_becdc .ValAttr ;
_cbdd ._dbba =&_egabc ;};if _ccef :=_cce .U ;_ccef !=nil {_abaa :=_ccef .ValAttr ==_ebb .ST_UnderlineValuesSingle ||_ccef .ValAttr ==_ebb .ST_UnderlineValuesUnset ;_cbdd ._egd =&_abaa ;};if _afe :=_cce .VertAlign ;_afe !=nil {_ecae :=_afe .ValAttr ==_ef .ST_VerticalAlignRunSuperscript ;
_cbdd ._gfcb =&_ecae ;_abac :=_afe .ValAttr ==_ef .ST_VerticalAlignRunSubscript ;_cbdd ._ecg =&_abac ;};if _fbee :=_cce .Sz ;_fbee !=nil {_dgcf :=_fbee .ValAttr ;_cbdd ._bacb =&_dgcf ;};if _ecdd :=_cce .Color ;_ecdd !=nil {_cbdd ._agga =_eega .getColorStringFromSmlColor (_ecdd );
};};return _cbdd ;};func (_eeb *convertContext )makeAnchors (){_de ,_bb :=_eeb ._bcdc .GetDrawing ();if _de !=nil {for _ ,_ac :=range _de .EG_Anchor {_eab :=&anchor {};if _cg :=_ac .AnchorChoice .TwoCellAnchor ;_cg !=nil {_bdfc ,_afb :=_cg .From ,_cg .To ;
if _bdfc ==nil ||_afb ==nil {return ;};_eab ._cfdc =int (_bdfc .Row );_eab ._gfe =_ec .FromSTCoordinate (_bdfc .RowOff );_eab ._dff =int (_bdfc .Col );_eab ._cgc =_ec .FromSTCoordinate (_bdfc .ColOff );_eab ._cfff =int (_afb .Row );_eab ._fcea =_ec .FromSTCoordinate (_afb .RowOff );
_eab ._aeg =int (_afb .Col );_eab ._eege =_ec .FromSTCoordinate (_afb .ColOff );if _fec :=_cg .ObjectChoicesChoice ;_fec !=nil {if _eae :=_fec .Pic ;_eae !=nil {if _bdg :=_eae .BlipFill ;_bdg !=nil {if _fgd :=_bdg .Blip ;_fgd !=nil {if _eeg :=_fgd .EmbedAttr ;
_eeg !=nil {for _ ,_bfa :=range _bb .X ().Relationship {if _bfa .IdAttr ==*_eeg {for _ ,_ga :=range _eeb ._gagb .Images {if _ga .Target ()==_bfa .TargetAttr {_affa ,_efg :=_db .Open (_ga .Path ());if _efg !=nil {_c .Log .Debug ("\u004fp\u0065\u006e\u0020\u0069m\u0061\u0067\u0065\u0020\u0066i\u006ce\u0020e\u0072\u0072\u006f\u0072\u003a\u0020\u0025s",_efg );
continue ;};_baf ,_ ,_efg :=_d .Decode (_affa );if _efg !=nil {_c .Log .Debug ("\u0044\u0065\u0063\u006fde\u0020\u0069\u006d\u0061\u0067\u0065\u0020\u0065\u0072\u0072\u006f\u0072\u003a\u0020%\u0073",_efg );continue ;};_eab ._deae =_baf ;};};};};};};};}else if _cba :=_fec .GraphicFrame ;
_cba !=nil {if _aebf :=_cba .Graphic ;_aebf !=nil {if _cgb :=_aebf .GraphicData ;_cgb !=nil {for _ ,_gc :=range _cgb .Any {if _bad ,_beb :=_gc .(*_cb .Chart );_beb {for _ ,_gb :=range _bb .X ().Relationship {if _gb .IdAttr ==_bad .IdAttr {_dbe :=_eeb ._gagb .GetChartByTargetId (_gb .TargetAttr );
if _dbe !=nil {_eab ._fddc =_dbe ;};};};};};};};};};};if _eab ._deae !=nil ||_eab ._fddc !=nil {_eeb ._dgaa =append (_eeb ._dgaa ,_eab );};};};};func (_fcef *convertContext )getStyleFromCell (_fgdb _fe .Cell ,_cea ,_gdgcb ,_feba *style )*style {if _feba !=nil {_acfc (_feba ,_cea );
_acfc (_feba ,_gdgcb );return _feba ;};_dbfc :=_fgdb .X ();_aaag :=_fcef .getStyle (_dbfc .SAttr );_acfc (_aaag ,_cea );_acfc (_aaag ,_gdgcb );return _aaag ;};func (_abca *convertContext )drawSheet (){for _bfcf ,_cbcf :=range _abca ._ebf {_fff :=len (_cbcf ._bdgdd );
if _bfcf ==len (_abca ._ebf )-1{for _aaa :=len (_cbcf ._bdgdd )-1;_aaa >=0;_aaa --{if !_cbcf ._bdgdd [_aaa ]._bcdd {_fff =_aaa ;};};};_dbea :=_cbcf ._bdgdd [:_fff ];for _ ,_beba :=range _dbea {_abca ._gdb .NewPage ();_abca .drawPage (_beba );};};};type symbol struct{_ggcb string ;
_fdb float64 ;_edaa float64 ;_becd float64 ;_bacc *_eb .TextStyle ;_gecd string ;};type convertContext struct{_gdb *_eb .Creator ;_gagb *_fe .Workbook ;_deeg *_cf .Theme ;_bcdc *_fe .Sheet ;_bgc *_fe .StyleSheet ;_dded int ;_bfea int ;_ebf []*pagespan ;
_bbgd *page ;_bag []*colInfo ;_acce []*rowInfo ;_fgcg []*rowspan ;_daag float64 ;_eeaaa float64 ;_ddfc float64 ;_beee float64 ;_cab []*mergedCell ;_dgaa []*anchor ;_bda float64 ;_cgf int ;_abgc int ;_fccd int ;_gbd int ;_dea bool ;_ggdd []_fe .Table ;};
type colWidthRange struct{_geag int ;_gdgc int ;_eece float64 ;_geb *style ;};

// RegisterFontsFromDirectory registers all fonts from the given directory automatically detecting font families and styles.
func RegisterFontsFromDirectory (dirName string )error {return _ec .RegisterFontsFromDirectory (dirName )};func (_dgab *convertContext )alignSymbolsHorizontally (_ecaa *cell ,_egca _ebb .ST_HorizontalAlignment ){if _egca ==_ebb .ST_HorizontalAlignmentUnset {switch _ecaa ._gdbc {case _ebb .ST_CellTypeB :_egca =_ebb .ST_HorizontalAlignmentCenter ;
case _ebb .ST_CellTypeN :_egca =_ebb .ST_HorizontalAlignmentRight ;default:_egca =_ebb .ST_HorizontalAlignmentLeft ;};};var _gfbf float64 ;for _ ,_abba :=range _ecaa ._gbca {switch _egca {case _ebb .ST_HorizontalAlignmentLeft :_gfbf =_af ;case _ebb .ST_HorizontalAlignmentRight :_afcd :=_cfdf (_abba ._eaec );
_gfbf =_ecaa ._fcge -_af -_afcd ;case _ebb .ST_HorizontalAlignmentCenter :_dcgb :=_cfdf (_abba ._eaec );_gfbf =(_ecaa ._fcge -_dcgb )/2;};for _ ,_bdgda :=range _abba ._eaec {_bdgda ._fdb +=_gfbf ;};};};const _da =0.0;func (_fgb *convertContext )getImage (_fdd _d .Image ,_agdf ,_gffa ,_dfaa ,_ged ,_ddeea ,_agbd float64 ,_fda _ec .ImgPart )*_eb .Image {_ged +=_fgb ._daag ;
_dfaa +=_fgb ._eeaaa ;_bdff ,_bbdf :=_ec .GetImage (_fgb ._gdb ,_fdd ,_agdf ,_gffa ,_dfaa ,_ged ,_ddeea ,_agbd ,_fda );if _bbdf !=nil {_c .Log .Debug ("\u0043\u0061\u006eno\u0074\u0020\u0067\u0065\u0074\u0020\u0061\u006e\u0020\u0069\u006d\u0061\u0067\u0065\u003a\u0020\u0025\u0073",_bbdf );
return nil ;};return _bdff ;};var _fgbd =map[uint32 ]_eb .PageSize {1:_eb .PageSize {8.5*_ed .Inch ,11*_ed .Inch },2:_eb .PageSize {8.5*_ed .Inch ,11*_ed .Inch },3:_eb .PageSize {11*_ed .Inch ,17*_ed .Inch },4:_eb .PageSize {17*_ed .Inch ,11*_ed .Inch },5:_eb .PageSize {8.5*_ed .Inch ,14*_ed .Inch },6:_eb .PageSize {5.5*_ed .Inch ,8.5*_ed .Inch },7:_eb .PageSize {7.5*_ed .Inch ,10*_ed .Inch },8:_eb .PageSize {_daad (297),_daad (420)},9:_eb .PageSize {_daad (210),_daad (297)},10:_eb .PageSize {_daad (210),_daad (297)},11:_eb .PageSize {_daad (148),_daad (210)},70:_eb .PageSize {_daad (105),_daad (148)},12:_eb .PageSize {_daad (250),_daad (354)},13:_eb .PageSize {_daad (182),_daad (257)},14:_eb .PageSize {8.5*_ed .Inch ,13*_ed .Inch },20:_eb .PageSize {4.125*_ed .Inch ,9.5*_ed .Inch },27:_eb .PageSize {_daad (110),_daad (220)},28:_eb .PageSize {_daad (162),_daad (229)},34:_eb .PageSize {_daad (250),_daad (176)},29:_eb .PageSize {_daad (324),_daad (458)},30:_eb .PageSize {_daad (229),_daad (324)},31:_eb .PageSize {_daad (114),_daad (162)},37:_eb .PageSize {3.88*_ed .Inch ,7.5*_ed .Inch },43:_eb .PageSize {_daad (100),_daad (148)},69:_eb .PageSize {_daad (200),_daad (148)}};
func _bgd (_dce []*symbol )float64 {_bcba :=0.0;for _ ,_eeeb :=range _dce {if _eeeb ._edaa > _bcba {_bcba =_eeeb ._edaa ;};};return _bcba ;};const (FontStyle_Regular FontStyle =0;FontStyle_Bold FontStyle =1;FontStyle_Italic FontStyle =2;FontStyle_BoldItalic FontStyle =3;
);

// ConvertToPdf converts a sheet to a PDF file. This package is beta, breaking changes can take place.
func ConvertToPdf (s *_fe .Sheet )*_eb .Creator {return ConvertToPdfWithOptions (s ,nil )};type pageRow struct{_egab int ;_ded []*cell ;};func (_acdd *convertContext )getStyle (_bfef *uint32 )*style {_fagg :=&style {};_ecgc :=false ;if _bfef !=nil {_gfag :=_acdd ._bgc .GetCellStyle (*_bfef );
if _acdd ._bgc .GetNumberFormat (_gfag .NumberFormat ()).GetFormat ()=="\u003b\u003b\u003b"{_fagg ._dfb =true ;};_gdeg :=_gfag .GetFill ();if _gdeg !=nil &&_gdeg .FillChoice !=nil &&_gdeg .FillChoice .PatternFill !=nil {if _gdeg .FillChoice .PatternFill .FgColor !=nil {_fagg ._fcfe =_acdd .getColorStringFromSmlColor (_gdeg .FillChoice .PatternFill .FgColor );
}else if _gdeg .FillChoice .PatternFill .BgColor !=nil {_fagg ._fcfe =_acdd .getColorStringFromSmlColor (_gdeg .FillChoice .PatternFill .BgColor );};};_abbd :=_gfag .GetFont ();for _ ,_dedg :=range _abbd .FontChoice {if _dedg .Name !=nil {_fagg ._fggdg =&_dedg .Name .ValAttr ;
_ecgc =true ;}else if _dedg .B !=nil {_abbeb :=_dedg .B .ValAttr ==nil ||*_dedg .B .ValAttr ;_fagg ._fgac =&_abbeb ;_ecgc =true ;}else if _dedg .I !=nil {_ccg :=_dedg .I .ValAttr ==nil ||*_dedg .I .ValAttr ;_fagg ._dbba =&_ccg ;_ecgc =true ;}else if _dedg .U !=nil {_bcbf :=_dedg .U .ValAttr ==_ebb .ST_UnderlineValuesSingle ||_dedg .U .ValAttr ==_ebb .ST_UnderlineValuesUnset ;
_fagg ._egd =&_bcbf ;_ecgc =true ;}else if _dedg .Sz !=nil {_dbef :=_dedg .Sz .ValAttr ;_fagg ._bacb =&_dbef ;_ecgc =true ;}else if _dedg .VertAlign !=nil {_eedf :=_dedg .VertAlign .ValAttr ==_ef .ST_VerticalAlignRunSuperscript ;_fagg ._gfcb =&_eedf ;_fbgba :=_dedg .VertAlign .ValAttr ==_ef .ST_VerticalAlignRunSubscript ;
_fagg ._ecg =&_fbgba ;_ecgc =true ;}else if _dedg .Color !=nil {_fagg ._agga =_acdd .getColorStringFromSmlColor (_dedg .Color );_ecgc =true ;};};_aae :=_gfag .GetBorder ();if _aae .Top !=nil {_fagg ._fdad =_acdd .getBorder (_aae .Top );_ecgc =true ;};if _aae .Bottom !=nil {_fagg ._ebef =_acdd .getBorder (_aae .Bottom );
_ecgc =true ;};if _aae .Left !=nil {_fagg ._eegg =_acdd .getBorder (_aae .Left );_ecgc =true ;};if _aae .Right !=nil {_fagg ._adbe =_acdd .getBorder (_aae .Right );_ecgc =true ;};if _gfag .Wrapped (){_fagg ._dcf =true ;_ecgc =true ;};if _ebfa :=_gfag .GetVerticalAlignment ();
_ebfa !=_ebb .ST_VerticalAlignmentUnset {_fagg ._ecbf =_ebfa ;_ecgc =true ;};if _gaa :=_gfag .GetHorizontalAlignment ();_gaa !=_ebb .ST_HorizontalAlignmentUnset {_fagg ._badd =_gaa ;_ecgc =true ;};};if _ecgc {return _fagg ;};return nil ;};type anchor struct{_deae _d .Image ;
_fddc *_cb .ChartSpace ;_cfdc int ;_gfe int64 ;_dff int ;_cgc int64 ;_cfff int ;_fcea int64 ;_aeg int ;_eege int64 ;};func (_fcgef *convertContext )getColorFromTheme (_cfge uint32 )string {_ggg :=_fcgef ._gagb .Themes ();if len (_ggg )!=0{_ceec :=_ggg [0];
if _bbedd :=_ceec .ThemeElements ;_bbedd !=nil {if _dbefa :=_bbedd .ClrScheme ;_dbefa !=nil {switch _cfge {case 0:return _ec .GetColorStringFromDmlColor (_dbefa .Lt1 );case 1:return _ec .GetColorStringFromDmlColor (_dbefa .Dk1 );case 2:return _ec .GetColorStringFromDmlColor (_dbefa .Lt2 );
case 3:return _ec .GetColorStringFromDmlColor (_dbefa .Dk2 );case 4:return _ec .GetColorStringFromDmlColor (_dbefa .Accent1 );case 5:return _ec .GetColorStringFromDmlColor (_dbefa .Accent2 );case 6:return _ec .GetColorStringFromDmlColor (_dbefa .Accent3 );
case 7:return _ec .GetColorStringFromDmlColor (_dbefa .Accent4 );case 8:return _ec .GetColorStringFromDmlColor (_dbefa .Accent5 );case 9:return _ec .GetColorStringFromDmlColor (_dbefa .Accent6 );};};};};return "";};func (_cgbc *convertContext )getSymbolsFromString (_agag string ,_fffd *style )[]*symbol {_eeec :=[]*symbol {};
_dga :=_cgbc .makeTextStyleFromCellStyle (_fffd );for _ ,_acac :=range _agag {_eeec =append (_eeec ,&symbol {_ggcb :string (_acac ),_bacc :_dga });};return _eeec ;};type pagespan struct{_fbgbd float64 ;_bdgdd []*page ;_ebfe int ;_gdee int ;};

// FontStyle represents a kind of font styling. It can be FontStyle_Regular, FontStyle_Bold, FontStyle_Italic and FontStyle_BoldItalic.
type FontStyle =_ec .FontStyle ;type border struct{_bbed float64 ;_ecbe _eb .Color ;};var _gf =_daad (0.0625);const _ab =1.5;func (_aca *convertContext )determineMaxIndexes (){var _dec ,_ebg int ;_dec =int (_aca ._bcdc .MaxColumnIdx ());_ecb :=_aca ._bcdc .Rows ();
if len (_ecb )> 0{_ebg =int (_ecb [len (_ecb )-1].RowNumber ());};for _ ,_caa :=range _aca ._dgaa {if _caa ._cfff >=_ebg {_ebg =_caa ._cfff +1;};if _caa ._aeg >=_dec {_dec =_caa ._aeg +1;};};_aca ._dded =_ebg ;_aca ._bfea =_dec ;};func (_dcfb *convertContext )getColorStringFromSmlColor (_edbe *_ebb .CT_Color )*string {var _fdef string ;
if _edbe .RgbAttr !=nil {_fdef =*_edbe .RgbAttr ;}else if _edbe .IndexedAttr !=nil &&*_edbe .IndexedAttr < 64{_fdef =_cdf [*_edbe .IndexedAttr ];}else if _edbe .ThemeAttr !=nil {_dbda :=*_edbe .ThemeAttr ;_fdef =_dcfb .getColorFromTheme (_dbda );};if _fdef ==""{return nil ;
};if len (_fdef )> 6{_fdef =_fdef [(len (_fdef )-6):];};if _edbe .TintAttr !=nil {_cacf :=*_edbe .TintAttr ;_fdef =_ec .AdjustColorByTint (_fdef ,_cacf );};_fdef ="\u0023"+_fdef ;return &_fdef ;};func (_fgdbe *convertContext )getBorder (_cgbf *_ebb .CT_BorderPr )*border {_gagfa :=&border {};
switch _cgbf .StyleAttr {case _ebb .ST_BorderStyleHair :_gagfa ._bbed =_gf /2;case _ebb .ST_BorderStyleThin :_gagfa ._bbed =_gf ;case _ebb .ST_BorderStyleMedium :_gagfa ._bbed =_gf *2;case _ebb .ST_BorderStyleThick :_gagfa ._bbed =_gf *4;};if _gagfa ._bbed ==0.0{return nil ;
};if _aabb :=_cgbf .Color ;_aabb !=nil {_fbd :=_fgdbe .getColorStringFromSmlColor (_aabb );if _fbd !=nil {_gagfa ._ecbe =_eb .ColorRGBFromHex (*_fbd );}else {_gagfa ._ecbe =_eb .ColorBlack ;};};return _gagfa ;};func _daad (_gdbe float64 )float64 {return _gdbe *_ed .Millimeter };
var _ff =3.025/_daad (1);type page struct{_bcbea []*pageRow ;_bcdd bool ;_abfd []*_eb .Image ;_afac *pagespan ;_bdda *rowspan ;};func (_cbe *convertContext )distributeAnchors (){for _ ,_dca :=range _cbe ._dgaa {_eefb ,_ecc :=_dca ._cfdc ,_dca ._gfe ;_fgde ,_dba :=_dca ._dff ,_dca ._cgc ;
_fece ,_fag :=_dca ._cfff ,_dca ._fcea ;_bfb ,_aba :=_dca ._aeg ,_dca ._eege ;if _eefb < _cbe ._fccd ||(_fece > _cbe ._gbd &&_cbe ._gbd > 0){continue ;};if _fgde < _cbe ._cgf ||(_bfb > _cbe ._abgc &&_cbe ._abgc > 0){continue ;};var _cec ,_acc ,_egg ,_fdf *page ;
for _ ,_ddce :=range _cbe ._ebf {for _ ,_baeb :=range _ddce ._bdgdd {if _eefb >=_baeb ._bdda ._cbcc &&_eefb < _baeb ._bdda ._fca {if _fgde >=_baeb ._afac ._ebfe &&_fgde < _baeb ._afac ._gdee {_baeb ._bcdd =true ;_cec =_baeb ;};if _bfb >=_baeb ._afac ._ebfe &&_bfb < _baeb ._afac ._gdee {_baeb ._bcdd =true ;
_acc =_baeb ;};};if _fece >=_baeb ._bdda ._cbcc &&_fece < _baeb ._bdda ._fca {if _fgde >=_baeb ._afac ._ebfe &&_fgde < _baeb ._afac ._gdee {_baeb ._bcdd =true ;_fdf =_baeb ;};if _bfb >=_baeb ._afac ._ebfe &&_bfb < _baeb ._afac ._gdee {_baeb ._bcdd =true ;
_egg =_baeb ;};};};};_abff :=_cec !=_acc ;_eee :=_cec !=_fdf ;if _abff &&_eee {_ffee :=_cbe ._bag [_fgde ]._eefd +_ed .FromEMU (_dba );_ace :=_cec ._afac ._fbgbd ;_eafa :=_cbe ._bag [_bfb ]._eefd +_ed .FromEMU (_aba );_cfe :=_cbe ._acce [_eefb ]._gdc +_ed .FromEMU (_ecc );
_fac :=float64 (_cec ._bdda ._ccc );_ddb :=_cbe ._acce [_fece ]._gdc +_ed .FromEMU (_fag );_cfd :=_eafa +_ace -_ffee ;_aaca :=_ddb +_fac -_cfe ;_gff :=_cbe .imageFromAnchor (_dca ,_cfd ,_aaca );_cec ._abfd =append (_cec ._abfd ,_cbe .getImage (_gff ,_aaca ,_cfd ,_ffee ,_cfe ,_ace -_ffee ,_fac -_cfe ,_ec .ImgPart_lt ));
_acc ._abfd =append (_acc ._abfd ,_cbe .getImage (_gff ,_aaca ,_cfd ,0,_cfe ,_ace -_ffee ,_fac -_cfe ,_ec .ImgPart_rt ));_fdf ._abfd =append (_fdf ._abfd ,_cbe .getImage (_gff ,_aaca ,_cfd ,_ffee ,0,_ace -_ffee ,_fac -_cfe ,_ec .ImgPart_lb ));_egg ._abfd =append (_egg ._abfd ,_cbe .getImage (_gff ,_aaca ,_cfd ,0,0,_ace -_ffee ,_fac -_cfe ,_ec .ImgPart_rb ));
}else if _abff {_acee :=_cbe ._acce [_eefb ]._gdc +_ed .FromEMU (_ecc );_fggd :=_cbe ._acce [_fece ]._gdc +_ed .FromEMU (_fag );_ceea :=_cbe ._bag [_fgde ]._eefd +_ed .FromEMU (_dba );_gcaga :=_cec ._afac ._fbgbd ;_bff :=_cbe ._bag [_bfb ]._eefd +_ed .FromEMU (_aba );
_ceeac :=_bff +_gcaga -_ceea ;_eecb :=_fggd -_acee ;_bfc :=_cbe .imageFromAnchor (_dca ,_ceeac ,_eecb );_cec ._abfd =append (_cec ._abfd ,_cbe .getImage (_bfc ,_eecb ,_ceeac ,_ceea ,_acee ,_gcaga -_ceea ,0,_ec .ImgPart_l ));_acc ._abfd =append (_acc ._abfd ,_cbe .getImage (_bfc ,_eecb ,_ceeac ,0,_acee ,_gcaga -_ceea ,0,_ec .ImgPart_r ));
}else if _eee {_ccb :=_cbe ._bag [_fgde ]._eefd +_ed .FromEMU (_dba );_bfaea :=_cbe ._bag [_bfb ]._eefd +_ed .FromEMU (_aba );_gegc :=_cbe ._acce [_eefb ]._gdc +_ed .FromEMU (_ecc );_cbd :=float64 (_cec ._bdda ._ccc );_ddg :=_cbe ._acce [_fece ]._gdc +_ed .FromEMU (_fag );
_dbb :=_bfaea -_ccb ;_bba :=_ddg +_cbd -_gegc ;_deba :=_cbe .imageFromAnchor (_dca ,_dbb ,_bba );_cec ._abfd =append (_cec ._abfd ,_cbe .getImage (_deba ,_bba ,_dbb ,_ccb ,_gegc ,0,_cbd -_gegc ,_ec .ImgPart_t ));_fdf ._abfd =append (_fdf ._abfd ,_cbe .getImage (_deba ,_bba ,_dbb ,_ccb ,0,0,_cbd -_gegc ,_ec .ImgPart_b ));
}else {_dae :=_cbe ._bag [_fgde ]._eefd +_ed .FromEMU (_dba );_ebcc :=_cbe ._bag [_bfb ]._eefd +_ed .FromEMU (_aba );_bffg :=_cbe ._acce [_eefb ]._gdc +_ed .FromEMU (_ecc );_aed :=_cbe ._acce [_fece ]._gdc +_ed .FromEMU (_fag );_gcd :=_ebcc -_dae ;_ead :=_aed -_bffg ;
_bdd :=_cbe .imageFromAnchor (_dca ,_gcd ,_ead );_cec ._abfd =append (_cec ._abfd ,_cbe .getImage (_bdd ,_ead ,_gcd ,_dae ,_bffg ,0,0,_ec .ImgPart_whole ));};};};const _dgga =64.0;

// RegisterFont makes a PdfFont accessible for using in converting to PDF.
func RegisterFont (name string ,style FontStyle ,font *_fb .PdfFont ){_ec .RegisterFont (name ,style ,font );};