//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

// Package format provides support for parsing and evaluating
// spreadsheetml/Excel number formats.
//
// Internally spreadsheets store numbers and dates values as a text
// representation of a floating point number (e.g. 1.2345).  This number is then
// displayed in Excel or another spreadsheet viewer differently depending on the
// number fornat of the cell style applied to the cell.
//
// As an example, the same value of 1.2345 can be displayed as:
// - "1" with format "0"
// - "1.2" with format "0.0"
// - "1.23" with format "0.00"
// - "1.235" with format "0.000"
// - "123%" with format "0%"
// - "1 23/100" with fornat "0 0/100"
// - "1.23E+00" with format "0.00E+00"
// - "29:37:41s" with format `[h]:mm:ss"s"`
package format ;import (_a "bytes";_e "fmt";_b "github.com/unidoc/unioffice/v2/common/logger";_eb "io";_g "math";_ed "strconv";_dd "strings";_f "time";);

// Token is a format token in the Excel format string.
type Token struct{Type FmtType ;Literal byte ;DateTime string ;};func _bae (_gfaf _f .Time )_f .Time {_gfaf =_gfaf .UTC ();return _f .Date (_gfaf .Year (),_gfaf .Month (),_gfaf .Day (),_gfaf .Hour (),_gfaf .Minute (),_gfaf .Second (),_gfaf .Nanosecond (),_f .Local );
};func _gd (_ffe ,_cgg float64 ,_fca Format )[]byte {if len (_fca .Whole )==0{return nil ;};_acd :=_f .Date (1899,12,30,0,0,0,0,_f .UTC );_gee :=_acd .Add (_f .Duration (_cgg *float64 (24*_f .Hour )));_gee =_bae (_gee );_ddc :=_ed .AppendFloat (nil ,_ffe ,'f',-1,64);
_agf :=make ([]byte ,0,len (_ddc ));_ebf :=0;_gf :=1;_dg :for _cbg :=len (_fca .Whole )-1;_cbg >=0;_cbg --{_daga :=len (_ddc )-1-_ebf ;_fe :=_fca .Whole [_cbg ];switch _fe .Type {case FmtTypeDigit :if _daga >=0{_agf =append (_agf ,_ddc [_daga ]);_ebf ++;
_gf =_cbg ;}else {_agf =append (_agf ,'0');};case FmtTypeDigitOpt :if _daga >=0{_agf =append (_agf ,_ddc [_daga ]);_ebf ++;_gf =_cbg ;}else {for _fda :=_cbg ;_fda >=0;_fda --{_ae :=_fca .Whole [_fda ];if _ae .Type ==FmtTypeLiteral {_agf =append (_agf ,_ae .Literal );
};};break _dg ;};case FmtTypeDollar :for _aag :=_ebf ;_aag < len (_ddc );_aag ++{_agf =append (_agf ,_ddc [len (_ddc )-1-_aag ]);_ebf ++;};_agf =append (_agf ,'$');case FmtTypeComma :if !_fca ._cb {_agf =append (_agf ,',');};case FmtTypeLiteral :_agf =append (_agf ,_fe .Literal );
case FmtTypeDate :_agf =append (_agf ,_fbd (_bad (_gee ,_fe .DateTime ))...);case FmtTypeTime :_agf =append (_agf ,_fbd (_cedf (_gee ,_cgg ,_fe .DateTime ))...);default:_b .Log .Debug ("\u0075\u006e\u0073\u0075p\u0070\u006f\u0072\u0074\u0065\u0064\u0020\u0074\u0079\u0070e\u0020i\u006e\u0020\u0077\u0068\u006f\u006c\u0065 \u0025\u0076",_fe );
};};_aec :=_fbd (_agf );if _ebf < len (_ddc )&&(_ebf !=0||_fca ._bc ){_dea :=len (_ddc )-_ebf ;_gfa :=make ([]byte ,len (_aec )+_dea );copy (_gfa ,_aec [0:_gf ]);copy (_gfa [_gf :],_ddc [0:]);copy (_gfa [_gf +_dea :],_aec [_gf :]);_aec =_gfa ;};if _fca ._cb {_dcd :=_a .Buffer {};
_fgb :=0;for _fbb :=len (_aec )-1;_fbb >=0;_fbb --{if !(_aec [_fbb ]>='0'&&_aec [_fbb ]<='9'){_fgb ++;}else {break ;};};for _cf :=0;_cf < len (_aec );_cf ++{_bcc :=(len (_aec )-_cf -_fgb );if _bcc %3==0&&_bcc !=0&&_cf !=0{_dcd .WriteByte (',');};_dcd .WriteByte (_aec [_cf ]);
};_aec =_dcd .Bytes ();};return _aec ;};func _fbc (_ggf float64 ,_de Format ,_be bool )string {if _de ._ac {return NumberGeneric (_ggf );};_dag :=make ([]byte ,0,20);_af :=_g .Signbit (_ggf );_ebe :=_g .Abs (_ggf );_fcf :=int64 (0);_db :=int64 (0);if _de .IsExponential {for _ebe >=10{_db ++;
_ebe /=10;};for _ebe < 1{_db --;_ebe *=10;};}else if _de ._cd {_ebe *=100;}else if _de ._gg {if _de ._ge ==0{_ab :=_g .Pow (10,float64 (_de ._fg ));_dc ,_ag :=1.0,1.0;_ =_dc ;for _ecg :=1.0;_ecg < _ab ;_ecg ++{_ ,_ade :=_g .Modf (_ebe *float64 (_ecg ));
if _ade < _ag {_ag =_ade ;_dc =_ecg ;if _ade ==0{break ;};};};_de ._ge =int64 (_dc );};_fcf =int64 (_ebe *float64 (_de ._ge )+0.5);if len (_de .Whole )> 0&&_fcf > _de ._ge {_fcf =int64 (_ebe *float64 (_de ._ge ))%_de ._ge ;_ebe -=float64 (_fcf )/float64 (_de ._ge );
}else {_ebe -=float64 (_fcf )/float64 (_de ._ge );if _g .Abs (_ebe )< 1{_dddd :=true ;for _ ,_bf :=range _de .Whole {if _bf .Type ==FmtTypeDigitOpt {continue ;};if _bf .Type ==FmtTypeLiteral &&_bf .Literal ==' '{continue ;};_dddd =false ;};if _dddd {_de .Whole =nil ;
};};};};_gb :=1;for _ ,_ced :=range _de .Fractional {if _ced .Type ==FmtTypeDigit ||_ced .Type ==FmtTypeDigitOpt {_gb ++;};};_ebe +=5*_g .Pow10 (-_gb );_dcf ,_ebea :=_g .Modf (_ebe );_dag =append (_dag ,_gd (_dcf ,_ggf ,_de )...);_dag =append (_dag ,_bg (_ebea ,_ggf ,_de )...);
_dag =append (_dag ,_acegd (_db ,_de )...);if _de ._gg {_dag =_ed .AppendInt (_dag ,_fcf ,10);_dag =append (_dag ,'/');_dag =_ed .AppendInt (_dag ,_de ._ge ,10);};if !_be &&_af {return "\u002d"+string (_dag );};return string (_dag );};const _cggf int =0;
const _egbf int =34;

// NumberGeneric formats the number with the generic format which attemps to
// mimic Excel's general formatting.
func NumberGeneric (v float64 )string {if _g .Abs (v )>=_c ||_g .Abs (v )<=_ga &&v !=0{return _eag (v );};_ecf :=make ([]byte ,0,15);_ecf =_ed .AppendFloat (_ecf ,v ,'f',-1,64);if len (_ecf )> 11{_ddb :=_ecf [11]-'0';if _ddb >=5&&_ddb <=9{_ecf [10]++;_ecf =_ecf [0:11];
_ecf =_dce (_ecf );};_ecf =_ecf [0:11];}else if len (_ecf )==11{if _ecf [len (_ecf )-1]=='9'{_ecf [len (_ecf )-1]++;_ecf =_dce (_ecf );};};_ecf =_bd (_ecf );return string (_ecf );};func (_gc FmtType )String ()string {if _gc >=FmtType (len (_ad )-1){return _e .Sprintf ("F\u006d\u0074\u0054\u0079\u0070\u0065\u0028\u0025\u0064\u0029",_gc );
};return _aa [_ad [_gc ]:_ad [_gc +1]];};func IsNumber (data string )(_dab bool ){_afe ,_bgae ,_age :=0,0,len (data );_gae :=len (data );_fga ,_gdc ,_bgc :=0,0,0;_ =_gdc ;_ =_bgc ;_ =_fga ;{_afe =_dfc ;_fga =0;_gdc =0;_bgc =0;};{if _bgae ==_age {goto _gge ;
};switch _afe {case 0:goto _fgag ;case 1:goto _fdg ;case 2:goto _cbb ;case 3:goto _aafc ;case 4:goto _bag ;case 5:goto _dba ;case 6:goto _gac ;case 7:goto _feac ;};goto _ede ;_fea :_gdc =_bgae ;_bgae --;{_dab =false ;};goto _acef ;_dae :_gdc =_bgae ;_bgae --;
{_dab =_gdc ==len (data );};goto _acef ;_def :_gdc =_bgae ;_bgae --;{_dab =_gdc ==len (data );};goto _acef ;_dagaf :switch _bgc {case 2:{_bgae =(_gdc )-1;_dab =_gdc ==len (data );};case 3:{_bgae =(_gdc )-1;_dab =false ;};};goto _acef ;_acef :_fga =0;if _bgae ++;
_bgae ==_age {goto _cce ;};_fgag :_fga =_bgae ;switch data [_bgae ]{case 43:goto _fffb ;case 45:goto _fffb ;};if 48<=data [_bgae ]&&data [_bgae ]<=57{goto _dgc ;};goto _cba ;_cba :if _bgae ++;_bgae ==_age {goto _cad ;};_fdg :goto _cba ;_fffb :if _bgae ++;
_bgae ==_age {goto _gca ;};_cbb :if 48<=data [_bgae ]&&data [_bgae ]<=57{goto _dgc ;};goto _cba ;_dgc :if _bgae ++;_bgae ==_age {goto _egb ;};_aafc :if data [_bgae ]==46{goto _deae ;};if 48<=data [_bgae ]&&data [_bgae ]<=57{goto _dgc ;};goto _cba ;_deae :if _bgae ++;
_bgae ==_age {goto _ddcb ;};_bag :if 48<=data [_bgae ]&&data [_bgae ]<=57{goto _cga ;};goto _cba ;_cga :if _bgae ++;_bgae ==_age {goto _adc ;};_dba :if data [_bgae ]==69{goto _gbf ;};if 48<=data [_bgae ]&&data [_bgae ]<=57{goto _cga ;};goto _cba ;_gbf :if _bgae ++;
_bgae ==_age {goto _ddg ;};_gac :switch data [_bgae ]{case 43:goto _efb ;case 45:goto _efb ;};goto _cba ;_efb :_gdc =_bgae +1;_bgc =3;goto _dbf ;_dddf :_gdc =_bgae +1;_bgc =2;goto _dbf ;_dbf :if _bgae ++;_bgae ==_age {goto _cac ;};_feac :if 48<=data [_bgae ]&&data [_bgae ]<=57{goto _dddf ;
};goto _cba ;_ede :_cce :_afe =0;goto _gge ;_cad :_afe =1;goto _gge ;_gca :_afe =2;goto _gge ;_egb :_afe =3;goto _gge ;_ddcb :_afe =4;goto _gge ;_adc :_afe =5;goto _gge ;_ddg :_afe =6;goto _gge ;_cac :_afe =7;goto _gge ;_gge :{};if _bgae ==_gae {switch _afe {case 1:goto _fea ;
case 2:goto _fea ;case 3:goto _dae ;case 4:goto _fea ;case 5:goto _def ;case 6:goto _fea ;case 7:goto _dagaf ;};};};if _afe ==_efc {return false ;};return ;};

// AddToken adds a format token to the format.
func (_fc *Format )AddToken (t FmtType ,l []byte ){if _fc ._ec {_fc ._ec =false ;return ;};switch t {case FmtTypeDecimal :_fc ._bc =true ;case FmtTypeUnderscore :_fc ._ec =true ;case FmtTypeText :_fc .Whole =append (_fc .Whole ,Token {Type :t });case FmtTypeDate ,FmtTypeTime :_fc .Whole =append (_fc .Whole ,Token {Type :t ,DateTime :string (l )});
case FmtTypePercent :_fc ._cd =true ;t =FmtTypeLiteral ;l =[]byte {'%'};fallthrough;case FmtTypeDigitOpt :fallthrough;case FmtTypeLiteral ,FmtTypeDigit ,FmtTypeDollar ,FmtTypeComma :if l ==nil {l =[]byte {0};};for _ ,_ce :=range l {if _fc .IsExponential {_fc .Exponent =append (_fc .Exponent ,Token {Type :t ,Literal :_ce });
}else if !_fc ._bc {_fc .Whole =append (_fc .Whole ,Token {Type :t ,Literal :_ce });}else {_fc .Fractional =append (_fc .Fractional ,Token {Type :t ,Literal :_ce });};};case FmtTypeDigitOptThousands :_fc ._cb =true ;case FmtTypeFraction :_bb :=_dd .Split (string (l ),"\u002f");
if len (_bb )==2{_fc ._gg =true ;_fc ._ge ,_ =_ed .ParseInt (_bb [1],10,64);for _ ,_bcd :=range _bb [1]{if _bcd =='?'||_bcd =='0'{_fc ._fg ++;};};};default:_b .Log .Debug ("\u0075\u006e\u0073u\u0070\u0070\u006f\u0072t\u0065\u0064\u0020\u0070\u0068\u0020\u0074y\u0070\u0065\u0020\u0069\u006e\u0020\u0070\u0061\u0072\u0073\u0065\u0020\u0025\u0076",t );
};};func Parse (s string )[]Format {_eca :=Lexer {};_eca .Lex (_dd .NewReader (s ));_eca ._bfg =append (_eca ._bfg ,_eca ._fcc );return _eca ._bfg ;};const _c =1e11;func _acegd (_bfe int64 ,_ee Format )[]byte {if !_ee .IsExponential ||len (_ee .Exponent )==0{return nil ;
};_ggfa :=_ed .AppendInt (nil ,_edb (_bfe ),10);_ba :=make ([]byte ,0,len (_ggfa )+2);_ba =append (_ba ,'E');if _bfe >=0{_ba =append (_ba ,'+');}else {_ba =append (_ba ,'-');_bfe *=-1;};_bgb :=0;_gbc :for _ege :=len (_ee .Exponent )-1;_ege >=0;_ege --{_cc :=len (_ggfa )-1-_bgb ;
_gaf :=_ee .Exponent [_ege ];switch _gaf .Type {case FmtTypeDigit :if _cc >=0{_ba =append (_ba ,_ggfa [_cc ]);_bgb ++;}else {_ba =append (_ba ,'0');};case FmtTypeDigitOpt :if _cc >=0{_ba =append (_ba ,_ggfa [_cc ]);_bgb ++;}else {for _gbed :=_ege ;_gbed >=0;
_gbed --{_ddca :=_ee .Exponent [_gbed ];if _ddca .Type ==FmtTypeLiteral {_ba =append (_ba ,_ddca .Literal );};};break _gbc ;};case FmtTypeLiteral :_ba =append (_ba ,_gaf .Literal );default:_b .Log .Debug ("\u0075\u006e\u0073\u0075\u0070\u0070\u006f\u0072\u0074\u0065\u0064 \u0074\u0079\u0070\u0065\u0020\u0069\u006e\u0020\u0065\u0078p\u0020\u0025\u0076",_gaf );
};};if _bgb < len (_ggfa ){_ba =append (_ba ,_ggfa [len (_ggfa )-_bgb -1:_bgb -1]...);};_fbd (_ba [2:]);return _ba ;};

// Format is a parsed number format.
type Format struct{Whole []Token ;Fractional []Token ;Exponent []Token ;IsExponential bool ;_gg bool ;_cd bool ;_ac bool ;_cb bool ;_ec bool ;_bc bool ;_ge int64 ;_fg int ;};func _fbd (_egg []byte )[]byte {for _ff :=0;_ff < len (_egg )/2;_ff ++{_da :=len (_egg )-1-_ff ;
_egg [_ff ],_egg [_da ]=_egg [_da ],_egg [_ff ];};return _egg ;};func _bg (_fff ,_ea float64 ,_egd Format )[]byte {if len (_egd .Fractional )==0{return nil ;};_bga :=_ed .AppendFloat (nil ,_fff ,'f',-1,64);if len (_bga )> 2{_bga =_bga [2:];}else {_bga =nil ;
};_bbd :=make ([]byte ,0,len (_bga ));_bbd =append (_bbd ,'.');_geg :=0;_dbc :for _acf :=0;_acf < len (_egd .Fractional );_acf ++{_bgg :=_acf ;_gbef :=_egd .Fractional [_acf ];switch _gbef .Type {case FmtTypeDigit :if _bgg < len (_bga ){_bbd =append (_bbd ,_bga [_bgg ]);
_geg ++;}else {_bbd =append (_bbd ,'0');};case FmtTypeDigitOpt :if _bgg >=0{_bbd =append (_bbd ,_bga [_bgg ]);_geg ++;}else {break _dbc ;};case FmtTypeLiteral :_bbd =append (_bbd ,_gbef .Literal );default:_b .Log .Debug ("\u0075\u006e\u0073\u0075\u0070\u0070o\u0072\u0074\u0065\u0064\u0020\u0074\u0079\u0070\u0065\u0020\u0069\u006e\u0020f\u0072\u0061\u0063\u0074\u0069\u006f\u006ea\u006c\u0020\u0025\u0076",_gbef );
};};return _bbd ;};func _bd (_dff []byte )[]byte {_bbe :=len (_dff );_gad :=false ;_edg :=false ;for _bec :=len (_dff )-1;_bec >=0;_bec --{if _dff [_bec ]=='0'&&!_edg &&!_gad {_bbe =_bec ;}else if _dff [_bec ]=='.'{_gad =true ;}else {_edg =true ;};};if _gad &&_edg {if _dff [_bbe -1]=='.'{_bbe --;
};return _dff [0:_bbe ];};return _dff ;};const _ga =1e-10;const _aa ="\u0046\u006d\u0074\u0054\u0079\u0070\u0065\u004c\u0069\u0074\u0065\u0072a\u006c\u0046\u006d\u0074\u0054\u0079\u0070\u0065\u0044\u0069\u0067\u0069\u0074\u0046\u006d\u0074\u0054y\u0070\u0065\u0044i\u0067\u0069\u0074\u004f\u0070\u0074\u0046\u006d\u0074\u0054\u0079\u0070\u0065\u0043o\u006d\u006d\u0061\u0046\u006d\u0074\u0054\u0079\u0070\u0065\u0044\u0065\u0063\u0069\u006da\u006c\u0046\u006d\u0074\u0054\u0079\u0070\u0065Pe\u0072\u0063e\u006e\u0074\u0046\u006d\u0074\u0054\u0079\u0070e\u0044\u006f\u006c\u006c\u0061\u0072\u0046\u006d\u0074Ty\u0070\u0065\u0044i\u0067\u0069\u0074\u004f\u0070\u0074\u0054\u0068\u006f\u0075\u0073\u0061n\u0064\u0073\u0046\u006d\u0074\u0054\u0079\u0070\u0065\u0055n\u0064\u0065\u0072\u0073c\u006f\u0072\u0065\u0046\u006d\u0074T\u0079\u0070\u0065\u0044\u0061\u0074\u0065\u0046\u006d\u0074\u0054y\u0070e\u0054\u0069\u006d\u0065\u0046\u006d\u0074\u0054\u0079\u0070\u0065\u0046\u0072\u0061\u0063t\u0069\u006f\u006e\u0046\u006dt\u0054\u0079\u0070\u0065\u0054e\u0078\u0074";
func _bad (_ggd _f .Time ,_gag string )[]byte {_fad :=[]byte {};_bed :=0;for _cdd :=0;_cdd < len (_gag );_cdd ++{var _bbdc string ;if _gag [_cdd ]=='/'{_bbdc =string (_gag [_bed :_cdd ]);_bed =_cdd +1;}else if _cdd ==len (_gag )-1{_bbdc =string (_gag [_bed :_cdd +1]);
}else {continue ;};switch _bbdc {case "\u0079\u0079":_fad =_ggd .AppendFormat (_fad ,"\u0030\u0036");case "\u0079\u0079\u0079\u0079":_fad =_ggd .AppendFormat (_fad ,"\u0032\u0030\u0030\u0036");case "\u006d":_fad =_ggd .AppendFormat (_fad ,"\u0031");case "\u006d\u006d":_fad =_ggd .AppendFormat (_fad ,"\u0030\u0031");
case "\u006d\u006d\u006d":_fad =_ggd .AppendFormat (_fad ,"\u004a\u0061\u006e");case "\u006d\u006d\u006d\u006d":_fad =_ggd .AppendFormat (_fad ,"\u004aa\u006e\u0075\u0061\u0072\u0079");case "\u006d\u006d\u006dm\u006d":switch _ggd .Month (){case _f .January ,_f .July ,_f .June :_fad =append (_fad ,'J');
case _f .February :_fad =append (_fad ,'M');case _f .March ,_f .May :_fad =append (_fad ,'M');case _f .April ,_f .August :_fad =append (_fad ,'A');case _f .September :_fad =append (_fad ,'S');case _f .October :_fad =append (_fad ,'O');case _f .November :_fad =append (_fad ,'N');
case _f .December :_fad =append (_fad ,'D');};case "\u0064":_fad =_ggd .AppendFormat (_fad ,"\u0032");case "\u0064\u0064":_fad =_ggd .AppendFormat (_fad ,"\u0030\u0032");case "\u0064\u0064\u0064":_fad =_ggd .AppendFormat (_fad ,"\u004d\u006f\u006e");case "\u0064\u0064\u0064\u0064":_fad =_ggd .AppendFormat (_fad ,"\u004d\u006f\u006e\u0064\u0061\u0079");
default:_b .Log .Debug ("\u0075\u006e\u0073\u0075\u0070\u0070\u006f\u0072\u0074\u0065\u0064 \u0064\u0061\u0074\u0065\u0020\u0066\u006f\u0072\u006d\u0061t\u0020\u0025\u0073",_bbdc );};if _gag [_cdd ]=='/'{_fad =append (_fad ,'/');};};return _fad ;};const _dfc int =0;
type Lexer struct{_fcc Format ;_bfg []Format ;};const _ecfg int =34;

// Number is used to format a number with a format string.  If the format
// string is empty, then General number formatting is used which attempts to mimic
// Excel's general formatting.
func Number (v float64 ,f string )string {if f ==""||f =="\u0047e\u006e\u0065\u0072\u0061\u006c"||f =="\u0040"{return NumberGeneric (v );};_gga :=Parse (f );if len (_gga )==1{return _fbc (v ,_gga [0],false );}else if len (_gga )> 1&&v < 0{return _fbc (v ,_gga [1],true );
}else if len (_gga )> 2&&v ==0{return _fbc (v ,_gga [2],false );};return _fbc (v ,_gga [0],false );};const _gaa int =-1;func _dce (_fec []byte )[]byte {for _ceb :=len (_fec )-1;_ceb > 0;_ceb --{if _fec [_ceb ]=='9'+1{_fec [_ceb ]='0';if _fec [_ceb -1]=='.'{_ceb --;
};_fec [_ceb -1]++;};};if _fec [0]=='9'+1{_fec [0]='0';copy (_fec [1:],_fec [0:]);_fec [0]='1';};return _fec ;};func (_cge *Lexer )nextFmt (){_cge ._bfg =append (_cge ._bfg ,_cge ._fcc );_cge ._fcc =Format {}};func (_aca *Lexer )Lex (r _eb .Reader ){_bgbd ,_gbd ,_abg :=0,0,0;
_gda :=-1;_acfc ,_acc ,_acda :=0,0,0;_ =_acc ;_ =_acda ;_fgc :=1;_ =_fgc ;_dgcb :=make ([]byte ,4096);_dgg :=false ;for !_dgg {_bgfg :=0;if _acfc > 0{_bgfg =_gbd -_acfc ;};_gbd =0;_dgf ,_ffb :=r .Read (_dgcb [_bgfg :]);if _dgf ==0||_ffb !=nil {_dgg =true ;
};_abg =_dgf +_bgfg ;if _abg < len (_dgcb ){_gda =_abg ;};{_bgbd =_gec ;_acfc =0;_acc =0;_acda =0;};{if _gbd ==_abg {goto _ccga ;};switch _bgbd {case 34:goto _ggef ;case 35:goto _gdg ;case 0:goto _egca ;case 36:goto _edd ;case 37:goto _dad ;case 1:goto _dcg ;
case 2:goto _bbee ;case 38:goto _ega ;case 3:goto _cfd ;case 4:goto _eagg ;case 39:goto _fge ;case 5:goto _cggc ;case 6:goto _gff ;case 7:goto _add ;case 8:goto _edda ;case 40:goto _gbb ;case 9:goto _bfd ;case 41:goto _fcaf ;case 10:goto _adca ;case 42:goto _fef ;
case 11:goto _bea ;case 43:goto _dffc ;case 44:goto _fffe ;case 45:goto _cca ;case 12:goto _bfa ;case 46:goto _afg ;case 13:goto _aeag ;case 14:goto _afa ;case 15:goto _beb ;case 16:goto _gabdb ;case 47:goto _gaab ;case 17:goto _dfg ;case 48:goto _dcgc ;
case 18:goto _ggdf ;case 19:goto _adg ;case 20:goto _efef ;case 49:goto _agb ;case 50:goto _ccc ;case 21:goto _gfd ;case 22:goto _gdd ;case 23:goto _gdaa ;case 24:goto _edc ;case 25:goto _dda ;case 51:goto _dcc ;case 26:goto _gabg ;case 52:goto _acag ;
case 53:goto _ccd ;case 54:goto _gdb ;case 55:goto _gfg ;case 56:goto _fbeb ;case 57:goto _agd ;case 27:goto _acfcb ;case 28:goto _acg ;case 29:goto _aeb ;case 30:goto _bbae ;case 31:goto _cbdc ;case 58:goto _gfc ;case 32:goto _bdbf ;case 59:goto _fbbg ;
case 33:goto _gffb ;case 60:goto _dgea ;case 61:goto _dddg ;case 62:goto _aaaa ;};goto _caac ;_fbe :switch _acda {case 2:{_gbd =(_acc )-1;_aca ._fcc .AddToken (FmtTypeDigit ,nil );};case 3:{_gbd =(_acc )-1;_aca ._fcc .AddToken (FmtTypeDigitOpt ,nil );};
case 5:{_gbd =(_acc )-1;};case 8:{_gbd =(_acc )-1;_aca ._fcc .AddToken (FmtTypePercent ,nil );};case 13:{_gbd =(_acc )-1;_aca ._fcc .AddToken (FmtTypeFraction ,_dgcb [_acfc :_acc ]);};case 14:{_gbd =(_acc )-1;_aca ._fcc .AddToken (FmtTypeDate ,_dgcb [_acfc :_acc ]);
};case 15:{_gbd =(_acc )-1;_aca ._fcc .AddToken (FmtTypeTime ,_dgcb [_acfc :_acc ]);};case 16:{_gbd =(_acc )-1;_aca ._fcc .AddToken (FmtTypeTime ,_dgcb [_acfc :_acc ]);};case 18:{_gbd =(_acc )-1;};case 20:{_gbd =(_acc )-1;_aca ._fcc .AddToken (FmtTypeLiteral ,_dgcb [_acfc :_acc ]);
};case 21:{_gbd =(_acc )-1;_aca ._fcc .AddToken (FmtTypeLiteral ,_dgcb [_acfc +1:_acc -1]);};};goto _bda ;_dbb :_gbd =(_acc )-1;{_aca ._fcc .AddToken (FmtTypeFraction ,_dgcb [_acfc :_acc ]);};goto _bda ;_fce :_gbd =(_acc )-1;{_aca ._fcc .AddToken (FmtTypeDigitOpt ,nil );
};goto _bda ;_deg :_acc =_gbd +1;{_aca ._fcc .AddToken (FmtTypeDigitOptThousands ,nil );};goto _bda ;_bgfc :_gbd =(_acc )-1;{_aca ._fcc .AddToken (FmtTypePercent ,nil );};goto _bda ;_agfa :_gbd =(_acc )-1;{_aca ._fcc .AddToken (FmtTypeDate ,_dgcb [_acfc :_acc ]);
};goto _bda ;_eee :_gbd =(_acc )-1;{_aca ._fcc .AddToken (FmtTypeDigit ,nil );};goto _bda ;_bde :_gbd =(_acc )-1;{_aca ._fcc .AddToken (FmtTypeTime ,_dgcb [_acfc :_acc ]);};goto _bda ;_ebd :_gbd =(_acc )-1;{_aca ._fcc .AddToken (FmtTypeLiteral ,_dgcb [_acfc :_acc ]);
};goto _bda ;_fae :_acc =_gbd +1;{_aca ._fcc ._ac =true ;};goto _bda ;_aef :_acc =_gbd +1;{_aca ._fcc .AddToken (FmtTypeLiteral ,_dgcb [_acfc :_acc ]);};goto _bda ;_bbdd :_acc =_gbd +1;{_aca ._fcc .AddToken (FmtTypeDollar ,nil );};goto _bda ;_efe :_acc =_gbd +1;
{_aca ._fcc .AddToken (FmtTypeComma ,nil );};goto _bda ;_ddf :_acc =_gbd +1;{_aca ._fcc .AddToken (FmtTypeDecimal ,nil );};goto _bda ;_cfa :_acc =_gbd +1;{_aca .nextFmt ();};goto _bda ;_efd :_acc =_gbd +1;{_aca ._fcc .AddToken (FmtTypeText ,nil );};goto _bda ;
_abb :_acc =_gbd +1;{_aca ._fcc .AddToken (FmtTypeUnderscore ,nil );};goto _bda ;_cdc :_acc =_gbd ;_gbd --;{_aca ._fcc .AddToken (FmtTypeLiteral ,_dgcb [_acfc :_acc ]);};goto _bda ;_dbg :_acc =_gbd ;_gbd --;{_aca ._fcc .AddToken (FmtTypeLiteral ,_dgcb [_acfc +1:_acc -1]);
};goto _bda ;_dge :_acc =_gbd ;_gbd --;{_aca ._fcc .AddToken (FmtTypeDigitOpt ,nil );};goto _bda ;_feg :_acc =_gbd ;_gbd --;{_aca ._fcc .AddToken (FmtTypeFraction ,_dgcb [_acfc :_acc ]);};goto _bda ;_dfcg :_acc =_gbd ;_gbd --;{_aca ._fcc .AddToken (FmtTypePercent ,nil );
};goto _bda ;_dbe :_acc =_gbd ;_gbd --;{_aca ._fcc .AddToken (FmtTypeDate ,_dgcb [_acfc :_acc ]);};goto _bda ;_bgbc :_acc =_gbd ;_gbd --;{_aca ._fcc .AddToken (FmtTypeDigit ,nil );};goto _bda ;_beg :_acc =_gbd ;_gbd --;{_aca ._fcc .AddToken (FmtTypeTime ,_dgcb [_acfc :_acc ]);
};goto _bda ;_fcea :_acc =_gbd ;_gbd --;{};goto _bda ;_cda :_acc =_gbd +1;{_aca ._fcc .IsExponential =true ;};goto _bda ;_cbd :_acc =_gbd +1;{_aca ._fcc .AddToken (FmtTypeLiteral ,_dgcb [_acfc +1:_acc ]);};goto _bda ;_bda :_acfc =0;if _gbd ++;_gbd ==_abg {goto _ebad ;
};_ggef :_acfc =_gbd ;switch _dgcb [_gbd ]{case 34:goto _fcde ;case 35:goto _bdac ;case 36:goto _bbdd ;case 37:goto _ecga ;case 44:goto _efe ;case 46:goto _ddf ;case 47:goto _eba ;case 48:goto _beda ;case 58:goto _fgbf ;case 59:goto _cfa ;case 63:goto _aee ;
case 64:goto _efd ;case 65:goto _egad ;case 69:goto _fbdb ;case 71:goto _efca ;case 91:goto _ddddd ;case 92:goto _acfg ;case 95:goto _abb ;case 100:goto _eba ;case 104:goto _fgbf ;case 109:goto _fbf ;case 115:goto _bdaa ;case 121:goto _fde ;};if 49<=_dgcb [_gbd ]&&_dgcb [_gbd ]<=57{goto _dcgb ;
};goto _aef ;_fcde :_acc =_gbd +1;_acda =20;goto _egc ;_egc :if _gbd ++;_gbd ==_abg {goto _gadd ;};_gdg :if _dgcb [_gbd ]==34{goto _aecd ;};goto _ccg ;_ccg :if _gbd ++;_gbd ==_abg {goto _bcb ;};_egca :if _dgcb [_gbd ]==34{goto _aecd ;};goto _ccg ;_aecd :_acc =_gbd +1;
_acda =21;goto _feb ;_feb :if _gbd ++;_gbd ==_abg {goto _afb ;};_edd :if _dgcb [_gbd ]==34{goto _ccg ;};goto _dbg ;_bdac :_acc =_gbd +1;_acda =3;goto _ggcf ;_ggcf :if _gbd ++;_gbd ==_abg {goto _bedb ;};_dad :switch _dgcb [_gbd ]{case 35:goto _ddbg ;case 37:goto _ddbg ;
case 44:goto _gbdb ;case 47:goto _ead ;case 48:goto _ddbg ;case 63:goto _ddbg ;};goto _dge ;_ddbg :if _gbd ++;_gbd ==_abg {goto _fdb ;};_dcg :switch _dgcb [_gbd ]{case 35:goto _ddbg ;case 37:goto _ddbg ;case 47:goto _ead ;case 48:goto _ddbg ;case 63:goto _ddbg ;
};goto _fbe ;_ead :if _gbd ++;_gbd ==_abg {goto _agee ;};_bbee :switch _dgcb [_gbd ]{case 35:goto _edeb ;case 37:goto _aaa ;case 48:goto _dbgc ;case 63:goto _edeb ;};if 49<=_dgcb [_gbd ]&&_dgcb [_gbd ]<=57{goto _bdec ;};goto _fbe ;_edeb :_acc =_gbd +1;
goto _gagd ;_gagd :if _gbd ++;_gbd ==_abg {goto _gcd ;};_ega :switch _dgcb [_gbd ]{case 35:goto _edeb ;case 37:goto _edeb ;case 44:goto _edeb ;case 46:goto _edeb ;case 48:goto _edeb ;case 63:goto _edeb ;case 65:goto _cec ;};goto _feg ;_cec :if _gbd ++;
_gbd ==_abg {goto _aab ;};_cfd :switch _dgcb [_gbd ]{case 47:goto _gbce ;case 77:goto _bdb ;};goto _dbb ;_gbce :if _gbd ++;_gbd ==_abg {goto _cbc ;};_eagg :if _dgcb [_gbd ]==80{goto _abc ;};goto _dbb ;_abc :_acc =_gbd +1;goto _bgbe ;_bgbe :if _gbd ++;_gbd ==_abg {goto _gcf ;
};_fge :if _dgcb [_gbd ]==65{goto _cec ;};goto _feg ;_bdb :if _gbd ++;_gbd ==_abg {goto _ccdb ;};_cggc :if _dgcb [_gbd ]==47{goto _bba ;};goto _dbb ;_bba :if _gbd ++;_gbd ==_abg {goto _eeea ;};_gff :if _dgcb [_gbd ]==80{goto _dgec ;};goto _dbb ;_dgec :if _gbd ++;
_gbd ==_abg {goto _fefc ;};_add :if _dgcb [_gbd ]==77{goto _abc ;};goto _dbb ;_aaa :if _gbd ++;_gbd ==_abg {goto _ggeb ;};_edda :switch _dgcb [_gbd ]{case 35:goto _cdac ;case 37:goto _aff ;case 63:goto _cdac ;};if 48<=_dgcb [_gbd ]&&_dgcb [_gbd ]<=57{goto _bdd ;
};goto _fbe ;_cdac :_acc =_gbd +1;goto _abge ;_abge :if _gbd ++;_gbd ==_abg {goto _ecbc ;};_gbb :switch _dgcb [_gbd ]{case 35:goto _edeb ;case 37:goto _gecg ;case 44:goto _edeb ;case 46:goto _edeb ;case 48:goto _edeb ;case 63:goto _edeb ;case 65:goto _cec ;
};goto _feg ;_gecg :if _gbd ++;_gbd ==_abg {goto _dcb ;};_bfd :switch _dgcb [_gbd ]{case 35:goto _gde ;case 44:goto _gde ;case 46:goto _gde ;case 48:goto _gde ;case 63:goto _gde ;};goto _dbb ;_gde :_acc =_gbd +1;goto _gage ;_gage :if _gbd ++;_gbd ==_abg {goto _fbbc ;
};_fcaf :switch _dgcb [_gbd ]{case 35:goto _gde ;case 44:goto _gde ;case 46:goto _gde ;case 48:goto _gde ;case 63:goto _gde ;case 65:goto _cec ;};goto _feg ;_aff :if _gbd ++;_gbd ==_abg {goto _afef ;};_adca :if _dgcb [_gbd ]==37{goto _aff ;};if 48<=_dgcb [_gbd ]&&_dgcb [_gbd ]<=57{goto _bdd ;
};goto _fbe ;_bdd :_acc =_gbd +1;_acda =13;goto _dfa ;_dfa :if _gbd ++;_gbd ==_abg {goto _fba ;};_fef :switch _dgcb [_gbd ]{case 35:goto _edeb ;case 37:goto _cgb ;case 44:goto _edeb ;case 46:goto _edeb ;case 48:goto _dbea ;case 63:goto _edeb ;case 65:goto _cec ;
};if 49<=_dgcb [_gbd ]&&_dgcb [_gbd ]<=57{goto _bdd ;};goto _feg ;_cgb :if _gbd ++;_gbd ==_abg {goto _bff ;};_bea :switch _dgcb [_gbd ]{case 35:goto _gde ;case 37:goto _aff ;case 44:goto _gde ;case 46:goto _gde ;case 63:goto _gde ;};if 48<=_dgcb [_gbd ]&&_dgcb [_gbd ]<=57{goto _bdd ;
};goto _dbb ;_dbea :_acc =_gbd +1;goto _acae ;_acae :if _gbd ++;_gbd ==_abg {goto _agdg ;};_dffc :switch _dgcb [_gbd ]{case 35:goto _edeb ;case 37:goto _dbea ;case 44:goto _edeb ;case 46:goto _edeb ;case 48:goto _dbea ;case 63:goto _edeb ;case 65:goto _cec ;
};if 49<=_dgcb [_gbd ]&&_dgcb [_gbd ]<=57{goto _bdd ;};goto _feg ;_dbgc :_acc =_gbd +1;goto _ebb ;_ebb :if _gbd ++;_gbd ==_abg {goto _cfe ;};_fffe :switch _dgcb [_gbd ]{case 35:goto _edeb ;case 37:goto _dbea ;case 44:goto _edeb ;case 46:goto _edeb ;case 48:goto _dbgc ;
case 63:goto _edeb ;case 65:goto _cec ;};if 49<=_dgcb [_gbd ]&&_dgcb [_gbd ]<=57{goto _bdec ;};goto _feg ;_bdec :_acc =_gbd +1;goto _ded ;_ded :if _gbd ++;_gbd ==_abg {goto _defd ;};_cca :switch _dgcb [_gbd ]{case 35:goto _edeb ;case 37:goto _bdd ;case 44:goto _edeb ;
case 46:goto _edeb ;case 48:goto _dbgc ;case 63:goto _edeb ;case 65:goto _cec ;};if 49<=_dgcb [_gbd ]&&_dgcb [_gbd ]<=57{goto _bdec ;};goto _feg ;_gbdb :if _gbd ++;_gbd ==_abg {goto _dggd ;};_bfa :if _dgcb [_gbd ]==35{goto _deg ;};goto _fce ;_ecga :_acc =_gbd +1;
_acda =8;goto _fagd ;_fagd :if _gbd ++;_gbd ==_abg {goto _caee ;};_afg :switch _dgcb [_gbd ]{case 35:goto _gfe ;case 37:goto _bggc ;case 48:goto _bdaf ;case 63:goto _gfe ;};if 49<=_dgcb [_gbd ]&&_dgcb [_gbd ]<=57{goto _aecb ;};goto _dfcg ;_gfe :if _gbd ++;
_gbd ==_abg {goto _dgd ;};_aeag :switch _dgcb [_gbd ]{case 35:goto _gfe ;case 47:goto _ead ;case 48:goto _gfe ;case 63:goto _gfe ;};goto _bgfc ;_bggc :if _gbd ++;_gbd ==_abg {goto _fbfg ;};_afa :if _dgcb [_gbd ]==37{goto _bggc ;};if 48<=_dgcb [_gbd ]&&_dgcb [_gbd ]<=57{goto _aecb ;
};goto _fbe ;_aecb :if _gbd ++;_gbd ==_abg {goto _gbbc ;};_beb :switch _dgcb [_gbd ]{case 37:goto _bggc ;case 47:goto _ead ;};if 48<=_dgcb [_gbd ]&&_dgcb [_gbd ]<=57{goto _aecb ;};goto _fbe ;_bdaf :if _gbd ++;_gbd ==_abg {goto _ecgf ;};_gabdb :switch _dgcb [_gbd ]{case 35:goto _gfe ;
case 37:goto _bggc ;case 47:goto _ead ;case 48:goto _bdaf ;case 63:goto _gfe ;};if 49<=_dgcb [_gbd ]&&_dgcb [_gbd ]<=57{goto _aecb ;};goto _bgfc ;_eba :_acc =_gbd +1;goto _dbge ;_dbge :if _gbd ++;_gbd ==_abg {goto _ggdb ;};_gaab :switch _dgcb [_gbd ]{case 47:goto _eba ;
case 100:goto _eba ;case 109:goto _eba ;case 121:goto _fagg ;};goto _dbe ;_fagg :if _gbd ++;_gbd ==_abg {goto _bce ;};_dfg :if _dgcb [_gbd ]==121{goto _eba ;};goto _agfa ;_beda :_acc =_gbd +1;_acda =2;goto _gbfd ;_gbfd :if _gbd ++;_gbd ==_abg {goto _aae ;
};_dcgc :switch _dgcb [_gbd ]{case 35:goto _ddbg ;case 37:goto _ecb ;case 47:goto _ead ;case 48:goto _dgfc ;case 63:goto _ddbg ;};if 49<=_dgcb [_gbd ]&&_dgcb [_gbd ]<=57{goto _fcg ;};goto _bgbc ;_ecb :if _gbd ++;_gbd ==_abg {goto _cee ;};_ggdf :switch _dgcb [_gbd ]{case 35:goto _ddbg ;
case 37:goto _ecb ;case 47:goto _ead ;case 48:goto _ecb ;case 63:goto _ddbg ;};if 49<=_dgcb [_gbd ]&&_dgcb [_gbd ]<=57{goto _aecb ;};goto _eee ;_dgfc :if _gbd ++;_gbd ==_abg {goto _fagga ;};_adg :switch _dgcb [_gbd ]{case 35:goto _ddbg ;case 37:goto _ecb ;
case 47:goto _ead ;case 48:goto _dgfc ;case 63:goto _ddbg ;};if 49<=_dgcb [_gbd ]&&_dgcb [_gbd ]<=57{goto _fcg ;};goto _eee ;_fcg :if _gbd ++;_gbd ==_abg {goto _gcfa ;};_efef :switch _dgcb [_gbd ]{case 37:goto _aecb ;case 47:goto _ead ;};if 48<=_dgcb [_gbd ]&&_dgcb [_gbd ]<=57{goto _fcg ;
};goto _fbe ;_dcgb :_acc =_gbd +1;_acda =20;goto _dcff ;_dcff :if _gbd ++;_gbd ==_abg {goto _feag ;};_agb :switch _dgcb [_gbd ]{case 37:goto _aecb ;case 47:goto _ead ;};if 48<=_dgcb [_gbd ]&&_dgcb [_gbd ]<=57{goto _fcg ;};goto _cdc ;_fgbf :_acc =_gbd +1;
_acda =15;goto _gef ;_gef :if _gbd ++;_gbd ==_abg {goto _bfad ;};_ccc :switch _dgcb [_gbd ]{case 58:goto _fgbf ;case 65:goto _cae ;case 104:goto _fgbf ;case 109:goto _fgbf ;case 115:goto _bdaa ;};goto _beg ;_cae :if _gbd ++;_gbd ==_abg {goto _fdba ;};_gfd :switch _dgcb [_gbd ]{case 47:goto _bcgb ;
case 77:goto _ebee ;};goto _fbe ;_bcgb :if _gbd ++;_gbd ==_abg {goto _bdab ;};_gdd :if _dgcb [_gbd ]==80{goto _fgbf ;};goto _fbe ;_ebee :if _gbd ++;_gbd ==_abg {goto _eddg ;};_gdaa :if _dgcb [_gbd ]==47{goto _gfb ;};goto _fbe ;_gfb :if _gbd ++;_gbd ==_abg {goto _cdda ;
};_edc :if _dgcb [_gbd ]==80{goto _fbdf ;};goto _fbe ;_fbdf :if _gbd ++;_gbd ==_abg {goto _ffa ;};_dda :if _dgcb [_gbd ]==77{goto _fgbf ;};goto _fbe ;_bdaa :_acc =_gbd +1;_acda =15;goto _fcb ;_fcb :if _gbd ++;_gbd ==_abg {goto _bebf ;};_dcc :switch _dgcb [_gbd ]{case 46:goto _eec ;
case 58:goto _fgbf ;case 65:goto _cae ;case 104:goto _fgbf ;case 109:goto _fgbf ;case 115:goto _bdaa ;};goto _beg ;_eec :if _gbd ++;_gbd ==_abg {goto _dcde ;};_gabg :if _dgcb [_gbd ]==48{goto _fged ;};goto _bde ;_fged :_acc =_gbd +1;_acda =15;goto _ebfa ;
_ebfa :if _gbd ++;_gbd ==_abg {goto _dccd ;};_acag :switch _dgcb [_gbd ]{case 48:goto _dccf ;case 58:goto _fgbf ;case 65:goto _cae ;case 104:goto _fgbf ;case 109:goto _fgbf ;case 115:goto _bdaa ;};goto _beg ;_dccf :_acc =_gbd +1;_acda =15;goto _ebfc ;_ebfc :if _gbd ++;
_gbd ==_abg {goto _adeb ;};_ccd :switch _dgcb [_gbd ]{case 48:goto _fgbf ;case 58:goto _fgbf ;case 65:goto _cae ;case 104:goto _fgbf ;case 109:goto _fgbf ;case 115:goto _bdaa ;};goto _beg ;_aee :_acc =_gbd +1;_acda =5;goto _gfeb ;_gfeb :if _gbd ++;_gbd ==_abg {goto _fccb ;
};_gdb :switch _dgcb [_gbd ]{case 35:goto _ddbg ;case 37:goto _ddbg ;case 47:goto _ead ;case 48:goto _ddbg ;case 63:goto _ddbg ;};goto _fcea ;_egad :_acc =_gbd +1;_acda =20;goto _edcg ;_edcg :if _gbd ++;_gbd ==_abg {goto _bceg ;};_gfg :switch _dgcb [_gbd ]{case 47:goto _bcgb ;
case 77:goto _ebee ;};goto _cdc ;_fbdb :if _gbd ++;_gbd ==_abg {goto _ceee ;};_fbeb :switch _dgcb [_gbd ]{case 43:goto _cda ;case 45:goto _cda ;};goto _cdc ;_efca :_acc =_gbd +1;goto _dgca ;_dgca :if _gbd ++;_gbd ==_abg {goto _dggg ;};_agd :if _dgcb [_gbd ]==101{goto _gabga ;
};goto _cdc ;_gabga :if _gbd ++;_gbd ==_abg {goto _bee ;};_acfcb :if _dgcb [_gbd ]==110{goto _cfdb ;};goto _ebd ;_cfdb :if _gbd ++;_gbd ==_abg {goto _aeab ;};_acg :if _dgcb [_gbd ]==101{goto _gddf ;};goto _ebd ;_gddf :if _gbd ++;_gbd ==_abg {goto _fgbc ;
};_aeb :if _dgcb [_gbd ]==114{goto _acaa ;};goto _ebd ;_acaa :if _gbd ++;_gbd ==_abg {goto _gged ;};_bbae :if _dgcb [_gbd ]==97{goto _badg ;};goto _ebd ;_badg :if _gbd ++;_gbd ==_abg {goto _bbef ;};_cbdc :if _dgcb [_gbd ]==108{goto _fae ;};goto _ebd ;_ddddd :_acc =_gbd +1;
_acda =20;goto _abcc ;_abcc :if _gbd ++;_gbd ==_abg {goto _cdg ;};_gfc :switch _dgcb [_gbd ]{case 104:goto _fdd ;case 109:goto _fdd ;case 115:goto _fdd ;};goto _bcgbg ;_bcgbg :if _gbd ++;_gbd ==_abg {goto _dbed ;};_bdbf :if _dgcb [_gbd ]==93{goto _fgg ;
};goto _bcgbg ;_fgg :_acc =_gbd +1;_acda =18;goto _bade ;_abe :_acc =_gbd +1;_acda =16;goto _bade ;_bade :if _gbd ++;_gbd ==_abg {goto _fdgb ;};_fbbg :if _dgcb [_gbd ]==93{goto _fgg ;};goto _bcgbg ;_fdd :if _gbd ++;_gbd ==_abg {goto _feae ;};_gffb :if _dgcb [_gbd ]==93{goto _abe ;
};goto _bcgbg ;_acfg :if _gbd ++;_gbd ==_abg {goto _bbec ;};_dgea :goto _cbd ;_fbf :_acc =_gbd +1;_acda =14;goto _gagea ;_gagea :if _gbd ++;_gbd ==_abg {goto _dbfd ;};_dddg :switch _dgcb [_gbd ]{case 47:goto _eba ;case 58:goto _fgbf ;case 65:goto _cae ;
case 100:goto _eba ;case 104:goto _fgbf ;case 109:goto _fbf ;case 115:goto _bdaa ;case 121:goto _fagg ;};goto _dbe ;_fde :if _gbd ++;_gbd ==_abg {goto _dca ;};_aaaa :if _dgcb [_gbd ]==121{goto _eba ;};goto _cdc ;_caac :_ebad :_bgbd =34;goto _ccga ;_gadd :_bgbd =35;
goto _ccga ;_bcb :_bgbd =0;goto _ccga ;_afb :_bgbd =36;goto _ccga ;_bedb :_bgbd =37;goto _ccga ;_fdb :_bgbd =1;goto _ccga ;_agee :_bgbd =2;goto _ccga ;_gcd :_bgbd =38;goto _ccga ;_aab :_bgbd =3;goto _ccga ;_cbc :_bgbd =4;goto _ccga ;_gcf :_bgbd =39;goto _ccga ;
_ccdb :_bgbd =5;goto _ccga ;_eeea :_bgbd =6;goto _ccga ;_fefc :_bgbd =7;goto _ccga ;_ggeb :_bgbd =8;goto _ccga ;_ecbc :_bgbd =40;goto _ccga ;_dcb :_bgbd =9;goto _ccga ;_fbbc :_bgbd =41;goto _ccga ;_afef :_bgbd =10;goto _ccga ;_fba :_bgbd =42;goto _ccga ;
_bff :_bgbd =11;goto _ccga ;_agdg :_bgbd =43;goto _ccga ;_cfe :_bgbd =44;goto _ccga ;_defd :_bgbd =45;goto _ccga ;_dggd :_bgbd =12;goto _ccga ;_caee :_bgbd =46;goto _ccga ;_dgd :_bgbd =13;goto _ccga ;_fbfg :_bgbd =14;goto _ccga ;_gbbc :_bgbd =15;goto _ccga ;
_ecgf :_bgbd =16;goto _ccga ;_ggdb :_bgbd =47;goto _ccga ;_bce :_bgbd =17;goto _ccga ;_aae :_bgbd =48;goto _ccga ;_cee :_bgbd =18;goto _ccga ;_fagga :_bgbd =19;goto _ccga ;_gcfa :_bgbd =20;goto _ccga ;_feag :_bgbd =49;goto _ccga ;_bfad :_bgbd =50;goto _ccga ;
_fdba :_bgbd =21;goto _ccga ;_bdab :_bgbd =22;goto _ccga ;_eddg :_bgbd =23;goto _ccga ;_cdda :_bgbd =24;goto _ccga ;_ffa :_bgbd =25;goto _ccga ;_bebf :_bgbd =51;goto _ccga ;_dcde :_bgbd =26;goto _ccga ;_dccd :_bgbd =52;goto _ccga ;_adeb :_bgbd =53;goto _ccga ;
_fccb :_bgbd =54;goto _ccga ;_bceg :_bgbd =55;goto _ccga ;_ceee :_bgbd =56;goto _ccga ;_dggg :_bgbd =57;goto _ccga ;_bee :_bgbd =27;goto _ccga ;_aeab :_bgbd =28;goto _ccga ;_fgbc :_bgbd =29;goto _ccga ;_gged :_bgbd =30;goto _ccga ;_bbef :_bgbd =31;goto _ccga ;
_cdg :_bgbd =58;goto _ccga ;_dbed :_bgbd =32;goto _ccga ;_fdgb :_bgbd =59;goto _ccga ;_feae :_bgbd =33;goto _ccga ;_bbec :_bgbd =60;goto _ccga ;_dbfd :_bgbd =61;goto _ccga ;_dca :_bgbd =62;goto _ccga ;_ccga :{};if _gbd ==_gda {switch _bgbd {case 35:goto _cdc ;
case 0:goto _fbe ;case 36:goto _dbg ;case 37:goto _dge ;case 1:goto _fbe ;case 2:goto _fbe ;case 38:goto _feg ;case 3:goto _dbb ;case 4:goto _dbb ;case 39:goto _feg ;case 5:goto _dbb ;case 6:goto _dbb ;case 7:goto _dbb ;case 8:goto _fbe ;case 40:goto _feg ;
case 9:goto _dbb ;case 41:goto _feg ;case 10:goto _fbe ;case 42:goto _feg ;case 11:goto _dbb ;case 43:goto _feg ;case 44:goto _feg ;case 45:goto _feg ;case 12:goto _fce ;case 46:goto _dfcg ;case 13:goto _bgfc ;case 14:goto _fbe ;case 15:goto _fbe ;case 16:goto _bgfc ;
case 47:goto _dbe ;case 17:goto _agfa ;case 48:goto _bgbc ;case 18:goto _eee ;case 19:goto _eee ;case 20:goto _fbe ;case 49:goto _cdc ;case 50:goto _beg ;case 21:goto _fbe ;case 22:goto _fbe ;case 23:goto _fbe ;case 24:goto _fbe ;case 25:goto _fbe ;case 51:goto _beg ;
case 26:goto _bde ;case 52:goto _beg ;case 53:goto _beg ;case 54:goto _fcea ;case 55:goto _cdc ;case 56:goto _cdc ;case 57:goto _cdc ;case 27:goto _ebd ;case 28:goto _ebd ;case 29:goto _ebd ;case 30:goto _ebd ;case 31:goto _ebd ;case 58:goto _cdc ;case 32:goto _fbe ;
case 59:goto _fbe ;case 33:goto _ebd ;case 60:goto _cdc ;case 61:goto _dbe ;case 62:goto _cdc ;};};};if _acfc > 0{copy (_dgcb [0:],_dgcb [_acfc :]);};};_ =_gda ;if _bgbd ==_efc {_b .Log .Debug ("\u0066o\u0072m\u0061\u0074\u0020\u0070\u0061r\u0073\u0065 \u0065\u0072\u0072\u006f\u0072");
};};

// Value formats a value as a number or string depending on  if it appears to be
// a number or string.
func Value (v string ,f string )string {if IsNumber (v ){_eg ,_ :=_ed .ParseFloat (v ,64);return Number (_eg ,f );};return String (v ,f );};func _edb (_aceg int64 )int64 {if _aceg < 0{return -_aceg ;};return _aceg ;};const _gec int =34;const _efc int =-1;
const _fbg int =0;

// String returns the string formatted according to the type.  In format strings
// this is the fourth item, where '@' is used as a placeholder for text.
func String (v string ,f string )string {_ggc :=Parse (f );var _bcg Format ;if len (_ggc )==1{_bcg =_ggc [0];}else if len (_ggc )==4{_bcg =_ggc [3];};_df :=false ;for _ ,_fb :=range _bcg .Whole {if _fb .Type ==FmtTypeText {_df =true ;};};if !_df {return v ;
};_cg :=_a .Buffer {};for _ ,_fd :=range _bcg .Whole {switch _fd .Type {case FmtTypeLiteral :_cg .WriteByte (_fd .Literal );case FmtTypeText :_cg .WriteString (v );};};return _cg .String ();};var _ad =[...]uint8 {0,14,26,41,53,67,81,94,118,135,146,157,172,183};
const (FmtTypeLiteral FmtType =iota ;FmtTypeDigit ;FmtTypeDigitOpt ;FmtTypeComma ;FmtTypeDecimal ;FmtTypePercent ;FmtTypeDollar ;FmtTypeDigitOptThousands ;FmtTypeUnderscore ;FmtTypeDate ;FmtTypeTime ;FmtTypeFraction ;FmtTypeText ;);func _eag (_fac float64 )string {_ggb :=_ed .FormatFloat (_fac ,'E',-1,64);
_aecg :=_ed .FormatFloat (_fac ,'E',5,64);if len (_ggb )< len (_aecg ){return _ed .FormatFloat (_fac ,'E',2,64);};return _aecg ;};

// FmtType is the type of a format token.
//
//go:generate stringer -type=FmtType
type FmtType byte ;func _cedf (_eae _f .Time ,_cfc float64 ,_fag string )[]byte {_ceg :=[]byte {};_gabd :=0;for _gcg :=0;_gcg < len (_fag );_gcg ++{var _bgf string ;if _fag [_gcg ]==':'{_bgf =string (_fag [_gabd :_gcg ]);_gabd =_gcg +1;}else if _gcg ==len (_fag )-1{_bgf =string (_fag [_gabd :_gcg +1]);
}else {continue ;};switch _bgf {case "\u0064":_ceg =_eae .AppendFormat (_ceg ,"\u0032");case "\u0068":_ceg =_eae .AppendFormat (_ceg ,"\u0033");case "\u0068\u0068":_ceg =_eae .AppendFormat (_ceg ,"\u0031\u0035");case "\u006d":_ceg =_eae .AppendFormat (_ceg ,"\u0034");
case "\u006d\u006d":_ceg =_eae .AppendFormat (_ceg ,"\u0030\u0034");case "\u0073":_ceg =_eae .Round (_f .Second ).AppendFormat (_ceg ,"\u0035");case "\u0073\u002e\u0030":_ceg =_eae .Round (_f .Second /10).AppendFormat (_ceg ,"\u0035\u002e\u0030");case "\u0073\u002e\u0030\u0030":_ceg =_eae .Round (_f .Second /100).AppendFormat (_ceg ,"\u0035\u002e\u0030\u0030");
case "\u0073\u002e\u00300\u0030":_ceg =_eae .Round (_f .Second /1000).AppendFormat (_ceg ,"\u0035\u002e\u00300\u0030");case "\u0073\u0073":_ceg =_eae .Round (_f .Second ).AppendFormat (_ceg ,"\u0030\u0035");case "\u0073\u0073\u002e\u0030":_ceg =_eae .Round (_f .Second /10).AppendFormat (_ceg ,"\u0030\u0035\u002e\u0030");
case "\u0073\u0073\u002e0\u0030":_ceg =_eae .Round (_f .Second /100).AppendFormat (_ceg ,"\u0030\u0035\u002e0\u0030");case "\u0073\u0073\u002e\u0030\u0030\u0030":_ceg =_eae .Round (_f .Second /1000).AppendFormat (_ceg ,"\u0030\u0035\u002e\u0030\u0030\u0030");
case "\u0041\u004d\u002fP\u004d":_ceg =_eae .AppendFormat (_ceg ,"\u0050\u004d");case "\u005b\u0068\u005d":_ceg =_ed .AppendInt (_ceg ,int64 (_cfc *24),10);case "\u005b\u006d\u005d":_ceg =_ed .AppendInt (_ceg ,int64 (_cfc *24*60),10);case "\u005b\u0073\u005d":_ceg =_ed .AppendInt (_ceg ,int64 (_cfc *24*60*60),10);
case "":default:_b .Log .Debug ("\u0075\u006e\u0073\u0075\u0070\u0070\u006f\u0072\u0074\u0065\u0064 \u0074\u0069\u006d\u0065\u0020\u0066\u006f\u0072\u006d\u0061t\u0020\u0025\u0073",_bgf );};if _fag [_gcg ]==':'{_ceg =append (_ceg ,':');};};return _ceg ;
};