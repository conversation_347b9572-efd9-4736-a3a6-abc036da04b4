//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package zippkg ;import (_g "archive/zip";_gc "bytes";_e "encoding/xml";_dg "fmt";_cf "github.com/unidoc/unioffice/v2";_dd "github.com/unidoc/unioffice/v2/algo";_ab "github.com/unidoc/unioffice/v2/common/tempstorage";_b "github.com/unidoc/unioffice/v2/schema/soo/pkg/relationships";
_ce "io";_cee "path";_d "sort";_a "strings";_f "time";);func (_ef SelfClosingWriter )Write (b []byte )(int ,error ){_fdba :=0;_adg :=0;for _ffg :=0;_ffg < len (b )-2;_ffg ++{if b [_ffg ]=='>'&&b [_ffg +1]=='<'&&b [_ffg +2]=='/'{_cbad :=[]byte {};_de :=_ffg ;
for _gdc :=_ffg ;_gdc >=0;_gdc --{if b [_gdc ]==' '{_de =_gdc ;}else if b [_gdc ]=='<'{_cbad =b [_gdc +1:_de ];break ;};};_aaf :=[]byte {};for _fdf :=_ffg +3;_fdf < len (b );_fdf ++{if b [_fdf ]=='>'{_aaf =b [_ffg +3:_fdf ];break ;};};if !_gc .Equal (_cbad ,_aaf ){continue ;
};_afc ,_aeab :=_ef .W .Write (b [_fdba :_ffg ]);if _aeab !=nil {return _adg +_afc ,_aeab ;};_adg +=_afc ;_ ,_aeab =_ef .W .Write (_cce );if _aeab !=nil {return _adg ,_aeab ;};_adg +=3;for _bgde :=_ffg +2;_bgde < len (b )&&b [_bgde ]!='>';_bgde ++{_adg ++;
_fdba =_bgde +2;_ffg =_fdba ;};};};_gf ,_dfd :=_ef .W .Write (b [_fdba :]);return _gf +_adg ,_dfd ;};

// MarshalXML creates a file inside of a zip and marshals an object as xml, prefixing it
// with a standard XML header.
func MarshalXML (z *_g .Writer ,filename string ,v interface{})error {_cba :=&_g .FileHeader {};_cba .Method =_g .Deflate ;_cba .Name =filename ;_cba .SetModTime (_f .Now ());_aeg ,_ecf :=z .CreateHeader (_cba );if _ecf !=nil {return _dg .Errorf ("\u0063\u0072\u0065\u0061ti\u006e\u0067\u0020\u0025\u0073\u0020\u0069\u006e\u0020\u007a\u0069\u0070\u003a\u0020%\u0073",filename ,_ecf );
};_ ,_ecf =_aeg .Write ([]byte (XMLHeader ));if _ecf !=nil {return _dg .Errorf ("\u0063\u0072e\u0061\u0074\u0069\u006e\u0067\u0020\u0078\u006d\u006c\u0020\u0068\u0065\u0061\u0064\u0065\u0072\u0020\u0074\u006f\u0020\u0025\u0073: \u0025\u0073",filename ,_ecf );
};if _ecf =_e .NewEncoder (SelfClosingWriter {_aeg }).Encode (v );_ecf !=nil {return _dg .Errorf ("\u006d\u0061\u0072\u0073\u0068\u0061\u006c\u0069\u006e\u0067\u0020\u0025s\u003a\u0020\u0025\u0073",filename ,_ecf );};_ ,_ecf =_aeg .Write (_gcg );return _ecf ;
};

// SelfClosingWriter wraps a writer and replaces XML tags of the
// type <foo></foo> with <foo/>
type SelfClosingWriter struct{W _ce .Writer ;};

// AddFileFromStorage reads a file from internal storage and adds it at a given path to a zip file.
// NOTE: If disk storage cannot be used, memory storage can be used instead by calling memstore.SetAsStorage().
func AddFileFromStorage (z *_g .Writer ,zipPath ,storagePath string )error {_af ,_bdf :=z .Create (zipPath );if _bdf !=nil {return _dg .Errorf ("e\u0072\u0072\u006f\u0072 c\u0072e\u0061\u0074\u0069\u006e\u0067 \u0025\u0073\u003a\u0020\u0025\u0073",zipPath ,_bdf );
};_gab ,_bdf :=_ab .Open (storagePath );if _bdf !=nil {return _dg .Errorf ("e\u0072r\u006f\u0072\u0020\u006f\u0070\u0065\u006e\u0069n\u0067\u0020\u0025\u0073: \u0025\u0073",storagePath ,_bdf );};defer _gab .Close ();_ ,_bdf =_ce .Copy (_af ,_gab );return _bdf ;
};

// DecodeMap is used to walk a tree of relationships, decoding files and passing
// control back to the document.
type DecodeMap struct{_da map[string ]Target ;_ddc map[*_b .Relationships ]string ;_dac []Target ;_ddb OnNewRelationshipFunc ;_ec map[string ]struct{};_ge map[string ]int ;};

// Decode unmarshals the content of a *zip.File as XML to a given destination.
func Decode (f *_g .File ,dest interface{})error {_gd ,_fdc :=f .Open ();if _fdc !=nil {return _dg .Errorf ("e\u0072r\u006f\u0072\u0020\u0072\u0065\u0061\u0064\u0069n\u0067\u0020\u0025\u0073: \u0025\u0073",f .Name ,_fdc );};defer _gd .Close ();_abd :=_e .NewDecoder (_gd );
if _ged :=_abd .Decode (dest );_ged !=nil {return _dg .Errorf ("e\u0072\u0072\u006f\u0072 d\u0065c\u006f\u0064\u0069\u006e\u0067 \u0025\u0073\u003a\u0020\u0025\u0073",f .Name ,_ged );};if _bfb ,_dfe :=dest .(*_b .Relationships );_dfe {for _fg ,_aead :=range _bfb .Relationship {switch _aead .TypeAttr {case _cf .OfficeDocumentTypeStrict :_bfb .Relationship [_fg ].TypeAttr =_cf .OfficeDocumentType ;
case _cf .StylesTypeStrict :_bfb .Relationship [_fg ].TypeAttr =_cf .StylesType ;case _cf .ThemeTypeStrict :_bfb .Relationship [_fg ].TypeAttr =_cf .ThemeType ;case _cf .ControlTypeStrict :_bfb .Relationship [_fg ].TypeAttr =_cf .ControlType ;case _cf .SettingsTypeStrict :_bfb .Relationship [_fg ].TypeAttr =_cf .SettingsType ;
case _cf .ImageTypeStrict :_bfb .Relationship [_fg ].TypeAttr =_cf .ImageType ;case _cf .CommentsTypeStrict :_bfb .Relationship [_fg ].TypeAttr =_cf .CommentsType ;case _cf .ThumbnailTypeStrict :_bfb .Relationship [_fg ].TypeAttr =_cf .ThumbnailType ;case _cf .DrawingTypeStrict :_bfb .Relationship [_fg ].TypeAttr =_cf .DrawingType ;
case _cf .ChartTypeStrict :_bfb .Relationship [_fg ].TypeAttr =_cf .ChartType ;case _cf .ExtendedPropertiesTypeStrict :_bfb .Relationship [_fg ].TypeAttr =_cf .ExtendedPropertiesType ;case _cf .CustomXMLTypeStrict :_bfb .Relationship [_fg ].TypeAttr =_cf .CustomXMLType ;
case _cf .WorksheetTypeStrict :_bfb .Relationship [_fg ].TypeAttr =_cf .WorksheetType ;case _cf .SharedStringsTypeStrict :_bfb .Relationship [_fg ].TypeAttr =_cf .SharedStringsType ;case _cf .TableTypeStrict :_bfb .Relationship [_fg ].TypeAttr =_cf .TableType ;
case _cf .HeaderTypeStrict :_bfb .Relationship [_fg ].TypeAttr =_cf .HeaderType ;case _cf .FooterTypeStrict :_bfb .Relationship [_fg ].TypeAttr =_cf .FooterType ;case _cf .NumberingTypeStrict :_bfb .Relationship [_fg ].TypeAttr =_cf .NumberingType ;case _cf .FontTableTypeStrict :_bfb .Relationship [_fg ].TypeAttr =_cf .FontTableType ;
case _cf .WebSettingsTypeStrict :_bfb .Relationship [_fg ].TypeAttr =_cf .WebSettingsType ;case _cf .FootNotesTypeStrict :_bfb .Relationship [_fg ].TypeAttr =_cf .FootNotesType ;case _cf .EndNotesTypeStrict :_bfb .Relationship [_fg ].TypeAttr =_cf .EndNotesType ;
case _cf .SlideTypeStrict :_bfb .Relationship [_fg ].TypeAttr =_cf .SlideType ;case _cf .VMLDrawingTypeStrict :_bfb .Relationship [_fg ].TypeAttr =_cf .VMLDrawingType ;};};_d .Slice (_bfb .Relationship ,func (_aeb ,_cfg int )bool {_cab :=_bfb .Relationship [_aeb ];
_eg :=_bfb .Relationship [_cfg ];return _dd .NaturalLess (_cab .IdAttr ,_eg .IdAttr );});};return nil ;};var _gcg =[]byte {'\r','\n'};

// OnNewRelationshipFunc is called when a new relationship has been discovered.
//
// target is a resolved path that takes into account the location of the
// relationships file source and should be the path in the zip file.
//
// files are passed so non-XML files that can't be handled by AddTarget can be
// decoded directly (e.g. images)
//
// rel is the actual relationship so its target can be modified if the source
// target doesn't match where unioffice will write the file (e.g. read in
// 'xl/worksheets/MyWorksheet.xml' and we'll write out
// 'xl/worksheets/sheet1.xml')
type OnNewRelationshipFunc func (_cg *DecodeMap ,_ca ,_ad string ,_cd []*_g .File ,_cdc *_b .Relationship ,_bg Target )error ;var _cce =[]byte {'/','>'};const XMLHeader ="\u003c\u003f\u0078\u006d\u006c\u0020\u0076e\u0072\u0073\u0069o\u006e\u003d\u00221\u002e\u0030\"\u0020\u0065\u006e\u0063\u006f\u0064i\u006eg=\u0022\u0055\u0054\u0046\u002d\u0038\u0022\u0020\u0073\u0074\u0061\u006e\u0064\u0061\u006c\u006f\u006e\u0065\u003d\u0022\u0079\u0065\u0073\u0022\u003f\u003e"+"\u000a";
func (_fc *DecodeMap )IndexFor (path string )int {return _fc ._ge [path ]};

// RelationsPathFor returns the relations path for a given filename.
func RelationsPathFor (path string )string {_df :=_a .Split (path ,"\u002f");_eb :=_a .Join (_df [0:len (_df )-1],"\u002f");_bef :=_df [len (_df )-1];_eb +="\u002f_\u0072\u0065\u006c\u0073\u002f";_bef +="\u002e\u0072\u0065l\u0073";return _eb +_bef ;};type Target struct{Path string ;
Typ string ;Ifc interface{};Index uint32 ;};

// AddTarget allows documents to register decode targets. Path is a path that
// will be found in the zip file and ifc is an XML element that the file will be
// unmarshaled to.  filePath is the absolute path to the target, ifc is the
// object to decode into, sourceFileType is the type of file that the reference
// was discovered in, and index is the index of the source file type.
func (_fd *DecodeMap )AddTarget (filePath string ,ifc interface{},sourceFileType string ,idx uint32 )bool {if _fd ._da ==nil {_fd ._da =make (map[string ]Target );_fd ._ddc =make (map[*_b .Relationships ]string );_fd ._ec =make (map[string ]struct{});_fd ._ge =make (map[string ]int );
};if _cee .IsAbs (filePath ){filePath =_a .TrimPrefix (filePath ,"\u002f");};_fa :=_cee .Clean (filePath );if _ ,_cfd :=_fd ._ec [_fa ];_cfd {return false ;};_fd ._ec [_fa ]=struct{}{};_fd ._da [_fa ]=Target {Path :_fa ,Typ :sourceFileType ,Ifc :ifc ,Index :idx };
return true ;};

// SetOnNewRelationshipFunc sets the function to be called when a new
// relationship has been discovered.
func (_gb *DecodeMap )SetOnNewRelationshipFunc (fn OnNewRelationshipFunc ){_gb ._ddb =fn };

// Decode loops decoding targets registered with AddTarget and calling th
func (_cc *DecodeMap )Decode (files []*_g .File )error {_be :=1;for _be > 0{for len (_cc ._dac )> 0{_abb :=_cc ._dac [0];_cc ._dac =_cc ._dac [1:];_fdb :=_abb .Ifc .(*_b .Relationships );for _ ,_ff :=range _fdb .Relationship {_ea :=_cc ._ddc [_fdb ];_ed :=_a .TrimPrefix (_ff .TargetAttr ,"\u002f");
if _a .HasPrefix (_ff .TargetAttr ,"\u002f"){_ =_cc ._ddb (_cc ,_ed ,_ff .TypeAttr ,files ,_ff ,_abb );}else {if _a .IndexByte (_ea ,'/')> -1{_bd :=_ea [:_a .IndexByte (_ea ,'/')+1];if _a .HasPrefix (_ed ,_bd ){_ea ="";};};if _a .HasPrefix (_ed ,_ea ){_ea ="";
};_ =_cc ._ddb (_cc ,_ea +_ed ,_ff .TypeAttr ,files ,_ff ,_abb );};};};for _ae ,_ffeb :=range files {if _ffeb ==nil {continue ;};if _fe ,_cb :=_cc ._da [_ffeb .Name ];_cb {delete (_cc ._da ,_ffeb .Name );if _fef :=Decode (_ffeb ,_fe .Ifc );_fef !=nil {return _fef ;
};files [_ae ]=nil ;if _aa ,_ga :=_fe .Ifc .(*_b .Relationships );_ga {_cc ._dac =append (_cc ._dac ,_fe );_bf ,_ :=_cee .Split (_cee .Clean (_ffeb .Name +"\u002f\u002e\u002e\u002f"));_cc ._ddc [_aa ]=_bf ;_be ++;};};};_be --;};return nil ;};func (_cff *DecodeMap )RecordIndex (path string ,idx int ){_cff ._ge [path ]=idx };


// ExtractToDiskTmp extracts a zip file to a temporary file in a given path,
// returning the name of the file.
func ExtractToDiskTmp (f *_g .File ,path string )(string ,error ){_edd ,_aeaf :=_ab .TempFile (path ,"\u007a\u007a");if _aeaf !=nil {return "",_aeaf ;};defer _edd .Close ();_bgd ,_aeaf :=f .Open ();if _aeaf !=nil {return "",_aeaf ;};defer _bgd .Close ();
_ ,_aeaf =_ce .Copy (_edd ,_bgd );if _aeaf !=nil {return "",_aeaf ;};return _edd .Name (),nil ;};func MarshalXMLByType (z *_g .Writer ,dt _cf .DocType ,typ string ,v interface{})error {_dggdd :=_cf .AbsoluteFilename (dt ,typ ,0);return MarshalXML (z ,_dggdd ,v );
};func MarshalXMLByTypeIndex (z *_g .Writer ,dt _cf .DocType ,typ string ,idx int ,v interface{})error {_gcc :=_cf .AbsoluteFilename (dt ,typ ,idx );return MarshalXML (z ,_gcc ,v );};

// AddFileFromBytes takes a byte array and adds it at a given path to a zip file.
func AddFileFromBytes (z *_g .Writer ,zipPath string ,data []byte )error {_afd ,_dggd :=z .Create (zipPath );if _dggd !=nil {return _dg .Errorf ("e\u0072\u0072\u006f\u0072 c\u0072e\u0061\u0074\u0069\u006e\u0067 \u0025\u0073\u003a\u0020\u0025\u0073",zipPath ,_dggd );
};_ ,_dggd =_ce .Copy (_afd ,_gc .NewReader (data ));return _dggd ;};