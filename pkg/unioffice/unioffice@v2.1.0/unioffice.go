//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

/*
Package unioffice provides creation, reading, and writing of ECMA 376 Office Open
XML documents, spreadsheets and presentations.  It is still early in
development, but is progressing quickly.  This library takes a slightly
different approach from others, in that it starts by trying to support all of
the ECMA-376 standard when marshaling/unmarshaling XML documents.  From there it
adds wrappers around the ECMA-376 derived types that provide a more convenient
interface.

The raw XML based types reside in the `schema/“ directory. These types are
always accessible from the wrapper types via a `X() method that returns the
raw type.  Except for the base documents (document.Document,
spreadsheet.Workbook and presentation.Presentation), the other wrapper types are
value types with non-pointer methods.  They exist solely to modify and return
data from one or more XML types.

The packages of interest are github.com/unidoc/unioffice/v2/document,
unidoc/unioffice/v2/spreadsheet and github.com/unidoc/unioffice/v2/presentation.
*/
package unioffice ;import (_b "encoding/xml";_c "errors";_e "fmt";_cc "github.com/unidoc/unioffice/v2/algo";_ag "github.com/unidoc/unioffice/v2/common/logger";_df "log";_ab "reflect";_g "strings";_a "unicode";);

// Int32 returns a copy of v as a pointer.
func Int32 (v int32 )*int32 {_gc :=v ;return &_gc };

// AbsoluteImageFilename returns the full path to an image from the root of the
// zip container.
func AbsoluteImageFilename (dt DocType ,index int ,fileExtension string )string {_bb :=AbsoluteFilename (dt ,ImageType ,index );return _bb [0:len (_bb )-3]+fileExtension ;};

// Stringf formats according to a format specifier and returns a pointer to the
// resulting string.
func Stringf (f string ,args ...interface{})*string {_cg :=_e .Sprintf (f ,args ...);return &_cg };

// RelativeFilename returns a filename relative to the source file referenced
// from a relationships file. Index is used in some cases for files which there
// may be more than one of (e.g. worksheets/drawings/charts)
func RelativeFilename (dt DocType ,relToTyp ,typ string ,index int )string {_fg :=AbsoluteFilename (dt ,typ ,index );if relToTyp ==""{return _fg ;};_dff :=AbsoluteFilename (dt ,relToTyp ,index );_dg :=_g .Split (_dff ,"\u002f");_fd :=_g .Split (_fg ,"\u002f");
_ff :=0;for _ad :=0;_ad < len (_dg );_ad ++{if _dg [_ad ]==_fd [_ad ]{_ff ++;};if _ad +1==len (_fd ){break ;};};_dg =_dg [_ff :];_fd =_fd [_ff :];_gf :=len (_dg )-1;if _gf > 0{return _cc .RepeatString ("\u002e\u002e\u002f",_gf )+_g .Join (_fd ,"\u002f");
};return _g .Join (_fd ,"\u002f");};

// Uint8 returns a copy of v as a pointer.
func Uint8 (v uint8 )*uint8 {_aad :=v ;return &_aad };

// Uint64 returns a copy of v as a pointer.
func Uint64 (v uint64 )*uint64 {_aac :=v ;return &_aac };var _eec =func ()map[string ]string {_eed :=map[string ]string {};for _db ,_ce :=range _gfa {_eed [_ce ]=_db ;};return _eed ;}();const (ContentTypesFilename ="\u005b\u0043\u006f\u006ete\u006e\u0074\u005f\u0054\u0079\u0070\u0065\u0073\u005d\u002e\u0078\u006d\u006c";
BaseRelsFilename ="_\u0072\u0065\u006c\u0073\u002f\u002e\u0072\u0065\u006c\u0073";);const MinGoVersion =_ga ;const (OfficeDocumentTypeStrict ="\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072g\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063u\u006d\u0065\u006e\u0074\u002f\u0072\u0065\u006c\u0061\u0074\u0069\u006f\u006e\u0073\u0068\u0069\u0070\u0073\u002f\u006f\u0066\u0066\u0069\u0063e\u0044\u006f\u0063\u0075\u006de\u006e\u0074";
StylesTypeStrict ="\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006frg\u002fo\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044o\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0072\u0065\u006c\u0061\u0074\u0069\u006f\u006e\u0073\u0068i\u0070\u0073\u002f\u0073\u0074\u0079\u006c\u0065\u0073";
ThemeTypeStrict ="h\u0074\u0074\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002fo\u0066f\u0069\u0063\u0065\u0044o\u0063\u0075m\u0065\u006e\u0074\u002f\u0072\u0065\u006c\u0061\u0074\u0069\u006f\u006e\u0073\u0068\u0069\u0070\u0073\u002f\u0074\u0068\u0065\u006d\u0065";
ControlTypeStrict ="\u0068t\u0074\u0070\u003a\u002f\u002f\u0070\u0075rl\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006ff\u0066\u0069\u0063e\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0072\u0065\u006c\u0061\u0074\u0069\u006fn\u0073\u0068ip\u0073\u002f\u0063o\u006e\u0074\u0072\u006f\u006c";
SettingsTypeStrict ="\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002eo\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006ff\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0072\u0065\u006c\u0061\u0074i\u006f\u006e\u0073\u0068\u0069\u0070s\u002f\u0073e\u0074\u0074i\u006eg\u0073";
ImageTypeStrict ="h\u0074\u0074\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002fo\u0066f\u0069\u0063\u0065\u0044o\u0063\u0075m\u0065\u006e\u0074\u002f\u0072\u0065\u006c\u0061\u0074\u0069\u006f\u006e\u0073\u0068\u0069\u0070\u0073\u002f\u0069\u006d\u0061\u0067\u0065";
CommentsTypeStrict ="\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002eo\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006ff\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0072\u0065\u006c\u0061\u0074i\u006f\u006e\u0073\u0068\u0069\u0070s\u002f\u0063o\u006d\u006de\u006et\u0073";
ThumbnailTypeStrict ="\u0068\u0074\u0074\u0070\u003a/\u002f\u0070\u0075\u0072\u006c\u002eo\u0063\u006c\u0063\u002e\u006f\u0072\u0067/\u006f\u006f\u0078m\u006c\u002f\u006f\u0066f\u0069\u0063\u0065\u0044\u006f\u0063u\u006d\u0065\u006e\u0074\u002f\u0072\u0065\u006c\u0061\u0074\u0069\u006f\u006e\u0073\u0068\u0069\u0070s\u002f\u006d\u0065\u0074\u0061\u0064\u0061\u0074\u0061\u002f\u0074\u0068\u0075\u006d\u0062\u006e\u0061\u0069\u006c";
DrawingTypeStrict ="\u0068t\u0074\u0070\u003a\u002f\u002f\u0070\u0075rl\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006dl\u002f\u006ff\u0066\u0069\u0063e\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0072\u0065\u006c\u0061\u0074\u0069\u006fn\u0073\u0068ip\u0073\u002f\u0064r\u0061\u0077\u0069\u006e\u0067";
ChartTypeStrict ="h\u0074\u0074\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002fo\u0066f\u0069\u0063\u0065\u0044o\u0063\u0075m\u0065\u006e\u0074\u002f\u0072\u0065\u006c\u0061\u0074\u0069\u006f\u006e\u0073\u0068\u0069\u0070\u0073\u002f\u0063\u0068\u0061\u0072\u0074";
ExtendedPropertiesTypeStrict ="\u0068\u0074\u0074\u0070\u003a/\u002f\u0070\u0075\u0072\u006c\u002eo\u0063\u006c\u0063\u002e\u006f\u0072\u0067/\u006f\u006f\u0078m\u006c\u002f\u006f\u0066f\u0069\u0063\u0065\u0044\u006f\u0063u\u006d\u0065\u006e\u0074\u002f\u0072\u0065\u006c\u0061\u0074\u0069\u006f\u006e\u0073\u0068\u0069\u0070s\u002f\u0065\u0078\u0074\u0065\u006e\u0064\u0065\u0064\u0050\u0072\u006f\u0070\u0065\u0072\u0074\u0069\u0065\u0073";
CustomXMLTypeStrict ="\u0068\u0074\u0074\u0070\u003a/\u002f\u0070\u0075\u0072\u006c.\u006fc\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065D\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0072\u0065\u006c\u0061\u0074\u0069\u006f\u006e\u0073\u0068\u0069\u0070\u0073\u002f\u0063\u0075s\u0074\u006f\u006d\u0058\u006d\u006c";
WorksheetTypeStrict ="\u0068\u0074\u0074\u0070\u003a/\u002f\u0070\u0075\u0072\u006c.\u006fc\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065D\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0072\u0065\u006c\u0061\u0074\u0069\u006f\u006e\u0073\u0068\u0069\u0070\u0073\u002f\u0077\u006fr\u006b\u0073\u0068\u0065\u0065\u0074";
SharedStringsTypeStrict ="h\u0074\u0074p\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078m\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074/\u0072\u0065\u006c\u0061\u0074\u0069\u006f\u006e\u0073\u0068\u0069\u0070\u0073/\u0073\u0068\u0061\u0072\u0065\u0064\u0053\u0074\u0072\u0069\u006eg\u0073";
TableTypeStrict ="h\u0074\u0074\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002fo\u0066f\u0069\u0063\u0065\u0044o\u0063\u0075m\u0065\u006e\u0074\u002f\u0072\u0065\u006c\u0061\u0074\u0069\u006f\u006e\u0073\u0068\u0069\u0070\u0073\u002f\u0074\u0061\u0062\u006c\u0065";
HeaderTypeStrict ="\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006frg\u002fo\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044o\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0072\u0065\u006c\u0061\u0074\u0069\u006f\u006e\u0073\u0068i\u0070\u0073\u002f\u0068\u0065\u0061\u0064\u0065\u0072";
FooterTypeStrict ="\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006frg\u002fo\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044o\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0072\u0065\u006c\u0061\u0074\u0069\u006f\u006e\u0073\u0068i\u0070\u0073\u002f\u0066\u006f\u006f\u0074\u0065\u0072";
NumberingTypeStrict ="\u0068\u0074\u0074\u0070\u003a/\u002f\u0070\u0075\u0072\u006c.\u006fc\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065D\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0072\u0065\u006c\u0061\u0074\u0069\u006f\u006e\u0073\u0068\u0069\u0070\u0073\u002f\u006e\u0075m\u0062\u0065\u0072\u0069\u006e\u0067";
FontTableTypeStrict ="\u0068\u0074\u0074\u0070\u003a/\u002f\u0070\u0075\u0072\u006c.\u006fc\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065D\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0072\u0065\u006c\u0061\u0074\u0069\u006f\u006e\u0073\u0068\u0069\u0070\u0073\u002f\u0066\u006fn\u0074\u0054\u0061\u0062\u006c\u0065";
WebSettingsTypeStrict ="\u0068\u0074\u0074\u0070\u003a\u002f/\u0070\u0075\u0072\u006c\u002eo\u0063\u006c\u0063\u002e\u006f\u0072g\u002f\u006f\u006f\u0078\u006dl\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006de\u006e\u0074\u002f\u0072\u0065\u006c\u0061\u0074\u0069\u006f\u006e\u0073\u0068\u0069\u0070s\u002f\u0077\u0065\u0062\u0053\u0065\u0074\u0074i\u006e\u0067\u0073";
FootNotesTypeStrict ="\u0068\u0074\u0074\u0070\u003a/\u002f\u0070\u0075\u0072\u006c.\u006fc\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006f\u0066\u0066\u0069\u0063\u0065D\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0072\u0065\u006c\u0061\u0074\u0069\u006f\u006e\u0073\u0068\u0069\u0070\u0073\u002f\u0066\u006fo\u0074\u006e\u006f\u0074\u0065\u0073";
EndNotesTypeStrict ="\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002eo\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002f\u006ff\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0072\u0065\u006c\u0061\u0074i\u006f\u006e\u0073\u0068\u0069\u0070s\u002f\u0065n\u0064\u006eo\u0074e\u0073";
SlideTypeStrict ="h\u0074\u0074\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006f\u006f\u0078\u006d\u006c\u002fo\u0066f\u0069\u0063\u0065\u0044o\u0063\u0075m\u0065\u006e\u0074\u002f\u0072\u0065\u006c\u0061\u0074\u0069\u006f\u006e\u0073\u0068\u0069\u0070\u0073\u002f\u0073\u006c\u0069\u0064\u0065";
VMLDrawingTypeStrict ="\u0068\u0074t\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0063\u006c\u0063\u002e\u006f\u0072\u0067\u002f\u006fo\u0078\u006d\u006c\u002f\u006f\u0066f\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0072\u0065l\u0061\u0074i\u006f\u006e\u0073\u0068i\u0070\u0073\u002f\u0076\u006dl\u0044\u0072\u0061\u0077\u0069\u006e\u0067";
OfficeDocumentType ="\u0068\u0074\u0074\u0070\u003a\u002f\u002fs\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006da\u0074\u0073\u002e\u006f\u0072g\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006fc\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0072\u0065\u006c\u0061\u0074\u0069\u006f\u006e\u0073\u0068\u0069\u0070\u0073\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074";
StylesType ="\u0068\u0074\u0074\u0070\u003a/\u002f\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006fr\u006d\u0061\u0074\u0073.\u006f\u0072\u0067\u002f\u006f\u0066f\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002fr\u0065\u006c\u0061\u0074\u0069\u006f\u006e\u0073\u0068\u0069\u0070\u0073\u002f\u0073\u0074\u0079\u006c\u0065\u0073";
ThemeType ="\u0068t\u0074p\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002eo\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006da\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0072\u0065\u006c\u0061t\u0069\u006f\u006e\u0073\u0068\u0069\u0070\u0073/\u0074\u0068\u0065\u006d\u0065";
ThemeContentType ="\u0061\u0070\u0070\u006c\u0069\u0063\u0061t\u0069\u006f\u006e/\u0076\u006e\u0064.\u006f\u0070e\u006e\u0078\u006d\u006c\u0066\u006fr\u006dat\u0073\u002d\u006f\u0066\u0066\u0069\u0063\u0065\u0064\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002e\u0074\u0068\u0065\u006d\u0065\u002b\u0078\u006d\u006c";
SettingsType ="\u0068\u0074\u0074\u0070\u003a/\u002f\u0073\u0063\u0068\u0065\u006d\u0061s\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002fr\u0065\u006c\u0061\u0074\u0069\u006f\u006e\u0073\u0068\u0069\u0070\u0073/\u0073\u0065\u0074\u0074\u0069\u006eg\u0073";
ImageType ="\u0068t\u0074p\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002eo\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006da\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0072\u0065\u006c\u0061t\u0069\u006f\u006e\u0073\u0068\u0069\u0070\u0073/\u0069\u006d\u0061\u0067\u0065";
ControlType ="\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063h\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006cf\u006f\u0072\u006d\u0061t\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0072\u0065\u006c\u0061t\u0069\u006f\u006es\u0068\u0069\u0070\u0073\u002f\u0063\u006f\u006e\u0074\u0072\u006f\u006c";
CommentsType ="\u0068\u0074\u0074\u0070\u003a/\u002f\u0073\u0063\u0068\u0065\u006d\u0061s\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002fr\u0065\u006c\u0061\u0074\u0069\u006f\u006e\u0073\u0068\u0069\u0070\u0073/\u0063\u006f\u006d\u006d\u0065\u006et\u0073";
CommentsContentType ="a\u0070pl\u0069c\u0061t\u0069\u006f\u006e\u002f\u0076n\u0064\u002e\u006fp\u0065\u006e\u0078\u006d\u006cf\u006f\u0072\u006da\u0074\u0073\u002d\u006f\u0066\u0066\u0069\u0063\u0065\u0064\u006fc\u0075\u006d\u0065nt.\u0073\u0070\u0072\u0065\u0061\u0064s\u0068\u0065\u0065\u0074\u006d\u006c\u002e\u0063\u006f\u006d\u006d\u0065n\u0074s\u002b\u0078\u006d\u006c";
ThumbnailType ="\u0068\u0074\u0074\u0070\u003a\u002f\u002fs\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006cf\u006fr\u006d\u0061\u0074\u0073\u002e\u006fr\u0067\u002f\u0070\u0061\u0063\u006b\u0061g\u0065\u002f\u0032\u0030\u0030\u0036\u002f\u0072\u0065\u006c\u0061\u0074\u0069\u006f\u006e\u0073\u0068\u0069\u0070\u0073\u002f\u006d\u0065t\u0061\u0064\u0061\u0074\u0061\u002f\u0074\u0068\u0075\u006d\u0062\u006e\u0061i\u006c";
DrawingType ="\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063h\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006cf\u006f\u0072\u006d\u0061t\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0072\u0065\u006c\u0061t\u0069\u006f\u006es\u0068\u0069\u0070\u0073\u002f\u0064\u0072\u0061\u0077\u0069\u006e\u0067";
DrawingContentType ="\u0061\u0070\u0070\u006ci\u0063\u0061\u0074\u0069\u006f\u006e\u002f\u0076\u006ed\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002d\u006f\u0066f\u0069\u0063\u0065\u0064\u006fc\u0075\u006d\u0065\u006e\u0074\u002e\u0064\u0072\u0061\u0077\u0069\u006e\u0067\u002b\u0078\u006d\u006c";
ChartType ="\u0068t\u0074p\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002eo\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006da\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0072\u0065\u006c\u0061t\u0069\u006f\u006e\u0073\u0068\u0069\u0070\u0073/\u0063\u0068\u0061\u0072\u0074";
ChartContentType ="\u0061\u0070\u0070\u006c\u0069c\u0061\u0074\u0069\u006f\u006e/\u0076n\u0064\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002d\u006f\u0066f\u0069\u0063\u0065\u0064\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002e\u0064\u0072\u0061\u0077\u0069\u006e\u0067\u006d\u006c\u002e\u0063\u0068a\u0072\u0074\u002b\u0078\u006d\u006c";
HyperLinkType ="ht\u0074\u0070\u003a\u002f\u002f\u0073\u0063he\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006da\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006et\u002f\u0032\u0030\u0030\u0036\u002fr\u0065\u006c\u0061\u0074\u0069\u006f\u006e\u0073\u0068i\u0070s\u002f\u0068\u0079\u0070\u0065\u0072\u006c\u0069\u006e\u006b";
ExtendedPropertiesType ="\u0068t\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002eo\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072\u006da\u0074\u0073.\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0072\u0065\u006c\u0061\u0074i\u006f\u006e\u0073\u0068\u0069p\u0073\u002f\u0065x\u0074\u0065\u006e\u0064\u0065d\u002d\u0070\u0072\u006f\u0070\u0065\u0072\u0074\u0069\u0065\u0073";
CorePropertiesType ="\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061s\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066o\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0070\u0061\u0063\u006ba\u0067\u0065\u002f\u0032\u0030\u0030\u0036\u002f\u0072\u0065\u006c\u0061\u0074\u0069\u006f\u006e\u0073\u0068\u0069\u0070\u0073\u002f\u006d\u0065\u0074\u0061\u0064\u0061\u0074\u0061/\u0063\u006f\u0072\u0065\u002d\u0070\u0072\u006f\u0070e\u0072\u0074i\u0065\u0073";
CorePropertiesAltType ="h\u0074\u0074\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070e\u006e\u0078\u006dl\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006ff\u0066\u0069c\u0065\u0064\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u00300\u0036/\u0072\u0065\u006c\u0061\u0074\u0069\u006f\u006e\u0073\u0068\u0069\u0070\u0073\u002f\u006d\u0065t\u0061\u0064\u0061\u0074\u0061\u002f\u0063\u006f\u0072\u0065\u002d\u0070\u0072\u006f\u0070\u0065\u0072\u0074\u0069\u0065\u0073";
CustomPropertiesType ="\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061s\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066o\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069c\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0072\u0065\u006c\u0061\u0074\u0069\u006f\u006e\u0073\u0068\u0069\u0070\u0073\u002f\u0063u\u0073\u0074\u006f\u006d\u002d\u0070\u0072\u006f\u0070e\u0072\u0074i\u0065\u0073";
CustomXMLType ="ht\u0074\u0070\u003a\u002f\u002f\u0073\u0063he\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006da\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006et\u002f\u0032\u0030\u0030\u0036\u002fr\u0065\u006c\u0061\u0074\u0069\u006f\u006e\u0073\u0068i\u0070s\u002f\u0063\u0075\u0073\u0074\u006f\u006d\u0058\u006d\u006c";
TableStylesType ="\u0068\u0074\u0074\u0070\u003a\u002f\u002fs\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006cf\u006fr\u006d\u0061\u0074\u0073\u002e\u006fr\u0067\u002f\u006f\u0066\u0066\u0069\u0063e\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0072\u0065\u006c\u0061\u0074\u0069\u006f\u006e\u0073h\u0069\u0070\u0073\u002f\u0074\u0061\u0062\u006c\u0065\u0053\u0074\u0079\u006ce\u0073";
ViewPropertiesType ="ht\u0074\u0070\u003a\u002f\u002f\u0073\u0063he\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006da\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006et\u002f\u0032\u0030\u0030\u0036\u002fr\u0065\u006c\u0061\u0074\u0069\u006f\u006e\u0073\u0068i\u0070s\u002f\u0076\u0069\u0065\u0077\u0050\u0072\u006f\u0070\u0073";
FontEmbeddingType ="h\u0074tp\u003a/\u002fs\u0063\u0068\u0065\u006d\u0061s\u002e\u006f\u0070e\u006e\u0078\u006d\u006c\u0066o\u0072\u006d\u0061t\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063e\u0044\u006f\u0063ume\u006e\u0074\u002f\u0032\u0030\u00306\u002f\u0072\u0065\u006c\u0061\u0074\u0069\u006f\u006e\u0073\u0068\u0069p\u0073/\u0066\u006f\u006e\u0074";
WorksheetType ="ht\u0074\u0070\u003a\u002f\u002f\u0073\u0063he\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006da\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006et\u002f\u0032\u0030\u0030\u0036\u002fr\u0065\u006c\u0061\u0074\u0069\u006f\u006e\u0073\u0068i\u0070s\u002f\u0077\u006f\u0072\u006b\u0073\u0068\u0065\u0065\u0074";
WorksheetContentType ="\u0061p\u0070l\u0069\u0063\u0061\u0074\u0069\u006f\u006e\u002f\u0076\u006e\u0064.\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002d\u006f\u0066\u0066\u0069\u0063\u0065\u0064\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002e\u0073\u0070\u0072\u0065\u0061\u0064\u0073\u0068\u0065\u0065\u0074\u006dl\u002e\u0077\u006f\u0072\u006b\u0073\u0068\u0065e\u0074\u002b\u0078\u006d\u006c";
SharedStringsType ="h\u0074\u0074\u0070:\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002fo\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074/\u0032\u0030\u0030\u0036/\u0072\u0065\u006c\u0061t\u0069\u006f\u006e\u0073\u0068\u0069\u0070\u0073\u002f\u0073\u0068\u0061\u0072\u0065\u0064\u0053\u0074r\u0069\u006e\u0067\u0073";
SharedStringsContentType ="ap\u0070\u006c\u0069\u0063\u0061\u0074\u0069on\u002f\u0076\u006e\u0064\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072m\u0061\u0074\u0073\u002d\u006f\u0066\u0066\u0069\u0063\u0065\u0064\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002e\u0073p\u0072\u0065\u0061\u0064\u0073\u0068e\u0065\u0074\u006d\u006c\u002e\u0073\u0068\u0061\u0072e\u0064S\u0074\u0072\u0069\u006e\u0067\u0073\u002b\u0078\u006d\u006c";
SMLStyleSheetContentType ="\u0061\u0070\u0070\u006c\u0069\u0063\u0061\u0074\u0069\u006f\u006e\u002f\u0076\u006e\u0064\u002e\u006f\u0070\u0065n\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002d\u006f\u0066\u0066\u0069\u0063e\u0064\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002e\u0073\u0070\u0072\u0065\u0061\u0064\u0073\u0068\u0065\u0065\u0074\u006d\u006c\u002e\u0073t\u0079\u006c\u0065\u0073\u002bx\u006d\u006c";
TableType ="\u0068t\u0074p\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002eo\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006da\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0072\u0065\u006c\u0061t\u0069\u006f\u006e\u0073\u0068\u0069\u0070\u0073/\u0074\u0061\u0062\u006c\u0065";
TableContentType ="a\u0070\u0070l\u0069\u0063\u0061\u0074\u0069\u006f\u006e\u002f\u0076\u006e\u0064\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066o\u0072\u006d\u0061\u0074\u0073\u002d\u006f\u0066\u0066\u0069\u0063\u0065\u0064\u006f\u0063\u0075m\u0065\u006e\u0074\u002e\u0073\u0070\u0072\u0065\u0061\u0064\u0073\u0068\u0065e\u0074\u006d\u006c\u002e\u0074\u0061\u0062\u006c\u0065\u002b\u0078m\u006c";
HeaderType ="\u0068\u0074\u0074\u0070\u003a/\u002f\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006fr\u006d\u0061\u0074\u0073.\u006f\u0072\u0067\u002f\u006f\u0066f\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002fr\u0065\u006c\u0061\u0074\u0069\u006f\u006e\u0073\u0068\u0069\u0070\u0073\u002f\u0068\u0065\u0061\u0064\u0065\u0072";
FooterType ="\u0068\u0074\u0074\u0070\u003a/\u002f\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006fr\u006d\u0061\u0074\u0073.\u006f\u0072\u0067\u002f\u006f\u0066f\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002fr\u0065\u006c\u0061\u0074\u0069\u006f\u006e\u0073\u0068\u0069\u0070\u0073\u002f\u0066\u006f\u006f\u0074\u0065\u0072";
NumberingType ="ht\u0074\u0070\u003a\u002f\u002f\u0073\u0063he\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006da\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006et\u002f\u0032\u0030\u0030\u0036\u002fr\u0065\u006c\u0061\u0074\u0069\u006f\u006e\u0073\u0068i\u0070s\u002f\u006e\u0075\u006d\u0062\u0065\u0072\u0069\u006e\u0067";
FontTableType ="ht\u0074\u0070\u003a\u002f\u002f\u0073\u0063he\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006da\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006et\u002f\u0032\u0030\u0030\u0036\u002fr\u0065\u006c\u0061\u0074\u0069\u006f\u006e\u0073\u0068i\u0070s\u002f\u0066\u006f\u006e\u0074\u0054\u0061\u0062\u006c\u0065";
WebSettingsType ="\u0068\u0074\u0074\u0070\u003a\u002f\u002fs\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006cf\u006fr\u006d\u0061\u0074\u0073\u002e\u006fr\u0067\u002f\u006f\u0066\u0066\u0069\u0063e\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0072\u0065\u006c\u0061\u0074\u0069\u006f\u006e\u0073h\u0069\u0070\u0073\u002f\u0077\u0065\u0062\u0053\u0065\u0074\u0074\u0069\u006eg\u0073";
FootNotesType ="ht\u0074\u0070\u003a\u002f\u002f\u0073\u0063he\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006da\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006et\u002f\u0032\u0030\u0030\u0036\u002fr\u0065\u006c\u0061\u0074\u0069\u006f\u006e\u0073\u0068i\u0070s\u002f\u0066\u006f\u006f\u0074\u006e\u006f\u0074\u0065\u0073";
EndNotesType ="\u0068\u0074\u0074\u0070\u003a/\u002f\u0073\u0063\u0068\u0065\u006d\u0061s\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002fr\u0065\u006c\u0061\u0074\u0069\u006f\u006e\u0073\u0068\u0069\u0070\u0073/\u0065\u006e\u0064\u006e\u006f\u0074e\u0073";
SlideType ="\u0068t\u0074p\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002eo\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006da\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0072\u0065\u006c\u0061t\u0069\u006f\u006e\u0073\u0068\u0069\u0070\u0073/\u0073\u006c\u0069\u0064\u0065";
SlideContentType ="\u0061\u0070\u0070\u006c\u0069\u0063\u0061\u0074\u0069\u006f\u006e\u002f\u0076\u006e\u0064\u002e\u006f\u0070\u0065n\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002d\u006f\u0066\u0066\u0069\u0063e\u0064\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002e\u0070\u0072\u0065\u0073\u0065\u006e\u0074\u0061\u0074\u0069\u006f\u006e\u006d\u006c\u002es\u006c\u0069\u0064\u0065\u002bx\u006d\u006c";
SlideMasterType ="\u0068\u0074\u0074\u0070\u003a\u002f\u002fs\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006cf\u006fr\u006d\u0061\u0074\u0073\u002e\u006fr\u0067\u002f\u006f\u0066\u0066\u0069\u0063e\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0072\u0065\u006c\u0061\u0074\u0069\u006f\u006e\u0073h\u0069\u0070\u0073\u002f\u0073\u006c\u0069\u0064\u0065\u004d\u0061\u0073\u0074e\u0072";
SlideMasterContentType ="\u0061\u0070\u0070\u006c\u0069c\u0061\u0074\u0069\u006f\u006e\u002f\u0076n\u0064\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002d\u006f\u0066\u0066\u0069\u0063\u0065\u0064\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002e\u0070\u0072\u0065\u0073\u0065\u006e\u0074\u0061t\u0069\u006f\u006e\u006d\u006c\u002e\u0073\u006c\u0069\u0064\u0065\u004da\u0073\u0074\u0065\u0072\u002b\u0078m\u006c";
SlideLayoutType ="\u0068\u0074\u0074\u0070\u003a\u002f\u002fs\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006cf\u006fr\u006d\u0061\u0074\u0073\u002e\u006fr\u0067\u002f\u006f\u0066\u0066\u0069\u0063e\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0072\u0065\u006c\u0061\u0074\u0069\u006f\u006e\u0073h\u0069\u0070\u0073\u002f\u0073\u006c\u0069\u0064\u0065\u004c\u0061\u0079\u006fu\u0074";
SlideLayoutContentType ="\u0061\u0070\u0070\u006c\u0069c\u0061\u0074\u0069\u006f\u006e\u002f\u0076n\u0064\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002d\u006f\u0066\u0066\u0069\u0063\u0065\u0064\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002e\u0070\u0072\u0065\u0073\u0065\u006e\u0074\u0061t\u0069\u006f\u006e\u006d\u006c\u002e\u0073\u006c\u0069\u0064\u0065\u004ca\u0079\u006f\u0075\u0074\u002b\u0078m\u006c";
PresentationPropertiesType ="ht\u0074\u0070\u003a\u002f\u002f\u0073\u0063he\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006da\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006et\u002f\u0032\u0030\u0030\u0036\u002fr\u0065\u006c\u0061\u0074\u0069\u006f\u006e\u0073\u0068i\u0070s\u002f\u0070\u0072\u0065\u0073\u0050\u0072\u006f\u0070\u0073";
HandoutMasterType ="h\u0074\u0074\u0070:\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002eo\u0072\u0067\u002fo\u0066\u0066\u0069\u0063\u0065\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074/\u0032\u0030\u0030\u0036/\u0072\u0065\u006c\u0061t\u0069\u006f\u006e\u0073\u0068\u0069\u0070\u0073\u002f\u0068\u0061\u006e\u0064\u006f\u0075\u0074\u004da\u0073\u0074\u0065\u0072";
NotesMasterType ="\u0068\u0074\u0074\u0070\u003a\u002f\u002fs\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006cf\u006fr\u006d\u0061\u0074\u0073\u002e\u006fr\u0067\u002f\u006f\u0066\u0066\u0069\u0063e\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002f\u0032\u0030\u0030\u0036\u002f\u0072\u0065\u006c\u0061\u0074\u0069\u006f\u006e\u0073h\u0069\u0070\u0073\u002f\u006e\u006f\u0074\u0065\u0073\u004d\u0061\u0073\u0074e\u0072";
VMLDrawingType ="\u0068\u0074tp\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006fr\u006d\u0061\u0074\u0073.\u006f\u0072\u0067\u002fof\u0066\u0069c\u0065D\u006f\u0063\u0075\u006d\u0065\u006et\u002f\u0032\u00300\u0036\u002fr\u0065\u006c\u0061\u0074\u0069\u006f\u006e\u0073\u0068\u0069\u0070\u0073\u002f\u0076m\u006c\u0044\u0072\u0061\u0077\u0069\u006e\u0067";
VMLDrawingContentType ="\u0061\u0070\u0070\u006c\u0069\u0063a\u0074\u0069\u006fn\u002f\u0076\u006ed\u002e\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006fr\u006d\u0061\u0074\u0073\u002dof\u0066\u0069\u0063\u0065\u0064\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002e\u0076\u006d\u006c\u0044\u0072\u0061\u0077\u0069\u006e\u0067";
);

// Int64 returns a copy of v as a pointer.
func Int64 (v int64 )*int64 {_ee :=v ;return &_ee };var _af =map[string ]interface{}{};func _afa (_fa []*XSDAny )[]*any {_abdg :=[]*any {};for _ ,_cdb :=range _fa {_ddb :=&any {};_ddb .XMLName =_cdb .XMLName ;_bfc :=[]_b .Attr {};for _ ,_ccf :=range _cdb .Attrs {if _ccf .Name .Local !="\u0078\u006d\u006cn\u0073"{_bfc =append (_bfc ,_ccf );
};};_ddb .Attrs =_bfc ;_ddb .Data =_cdb .Data ;_ddb .Nodes =_afa (_cdb .Nodes );_abdg =append (_abdg ,_ddb );};return _abdg ;};

// MarshalXML implements the xml.Marshaler interface.
func (_bbc *XSDAny )MarshalXML (e *_b .Encoder ,start _b .StartElement )error {start .Name =_bbc .XMLName ;start .Attr =_bbc .Attrs ;_fda :=any {};_fda .XMLName =_bbc .XMLName ;_fda .Attrs =_bbc .Attrs ;_fda .Data =_bbc .Data ;_fda .Nodes =_afa (_bbc .Nodes );
_aaa :=[]string {};_fga :=false ;_gg :=nsSet {_ae :map[string ]string {},_bd :map[string ]string {}};_bbc .collectNS (&_gg );_gg .applyToNode (&_fda );for _ ,_bf :=range _gg ._dde {if _ ,_gbg :=_age [_bf ];_gbg {_aaa =append (_aaa ,_bf );};_fb :=_gg ._bd [_bf ];
_fda .Attrs =append (_fda .Attrs ,_b .Attr {Name :_b .Name {Local :"\u0078\u006d\u006c\u006e\u0073\u003a"+_bf },Value :_fb });if _bf =="\u006d\u0063"{_fga =true ;};};for _ ,_ge :=range _fda .Attrs {if _ge .Name .Local =="\u006d\u0063\u003aI\u0067\u006e\u006f\u0072\u0061\u0062\u006c\u0065"{_fga =false ;
break ;};};if _fga &&len (_aaa )> 0{_fda .Attrs =append (_fda .Attrs ,_b .Attr {Name :_b .Name {Local :"\u006d\u0063\u003aI\u0067\u006e\u006f\u0072\u0061\u0062\u006c\u0065"},Value :_g .Join (_aaa ,"\u0020")});};return e .Encode (&_fda );};const _ga =true ;


// Any is the interface used for marshaling/unmarshaling xsd:any
type Any interface{MarshalXML (_aba *_b .Encoder ,_agc _b .StartElement )error ;UnmarshalXML (_aa *_b .Decoder ,_ba _b .StartElement )error ;};func (_da *nsSet )getPrefix (_ccd string )string {if _aca ,_ec :=_eec [_ccd ];_ec {if _ ,_dc :=_da ._bd [_aca ];
!_dc {_da ._bd [_aca ]=_ccd ;_da ._ae [_ccd ]=_aca ;_da ._dde =append (_da ._dde ,_aca );};return _aca ;};_ccd =_g .TrimFunc (_ccd ,func (_ea rune )bool {return !_a .IsLetter (_ea )});if _eb ,_bg :=_da ._ae [_ccd ];_bg {return _eb ;};_cddc :=_g .Split (_ccd ,"\u002f");
_cddc =_g .Split (_cddc [len (_cddc )-1],"\u003a");_abg :=_cddc [len (_cddc )-1];_aec :=0;_aadg :=[]byte {};for {if _aec < len (_abg ){_aadg =append (_aadg ,_abg [_aec ]);}else {_aadg =append (_aadg ,'_');};_aec ++;if _ ,_gda :=_da ._bd [string (_aadg )];
!_gda {_da ._bd [string (_aadg )]=_ccd ;_da ._ae [_ccd ]=string (_aadg );_da ._dde =append (_da ._dde ,string (_aadg ));return string (_aadg );};};};const (Unknown DocType =iota ;DocTypeSpreadsheet ;DocTypeDocument ;DocTypePresentation ;);

// Float32 returns a copy of v as a pointer.
func Float32 (v float32 )*float32 {_fgd :=v ;return &_fgd };func (_dge *XSDAny )collectNS (_de *nsSet ){if _dge .XMLName .Space !=""{_de .getPrefix (_dge .XMLName .Space );};for _ ,_eaf :=range _dge .Attrs {if _eaf .Name .Space !=""&&_eaf .Name .Space !="\u0078\u006d\u006cn\u0073"{_de .getPrefix (_eaf .Name .Space );
};};for _ ,_ecf :=range _dge .Nodes {_ecf .collectNS (_de );};};var Log =_df .Printf ;

// Uint16 returns a copy of v as a pointer.
func Uint16 (v uint16 )*uint16 {_gd :=v ;return &_gd };

// Uint32 returns a copy of v as a pointer.
func Uint32 (v uint32 )*uint32 {_cb :=v ;return &_cb };

// Float64 returns a copy of v as a pointer.
func Float64 (v float64 )*float64 {_gb :=v ;return &_gb };

// String returns a copy of v as a pointer.
func String (v string )*string {_cdd :=v ;return &_cdd };var _gfa =map[string ]string {"\u0061":"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065m\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006cf\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072\u0061\u0077\u0069\u006e\u0067m\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u006d\u0061\u0069\u006e","\u0064\u0063":"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0070\u0075\u0072\u006c\u002e\u006f\u0072\u0067/\u0064c\u002f\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0073\u002f\u0031\u002e\u0031\u002f","\u0064c\u0074\u0065\u0072\u006d\u0073":"\u0068t\u0074\u0070\u003a\u002f/\u0070\u0075\u0072\u006c\u002eo\u0072g\u002fd\u0063\u002f\u0074\u0065\u0072\u006d\u0073/","\u006d\u0063":"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006e\u0078m\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u006d\u0061\u0072\u006b\u0075\u0070\u002d\u0063\u006f\u006d\u0070\u0061\u0074\u0069\u0062\u0069\u006ci\u0074\u0079\u002f\u0032\u00300\u0036","\u006d\u006f":"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068e\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002ec\u006f\u006d\u002f\u006f\u0066fi\u0063\u0065\u002f\u006d\u0061\u0063\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0032\u0030\u0030\u0038\u002f\u006d\u0061\u0069\u006e","\u0077":"ht\u0074\u0070:\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073.\u006f\u0070\u0065\u006e\u0078\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0077\u006f\u0072\u0064\u0070\u0072\u006f\u0063\u0065s\u0073i\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u00306\u002fm\u0061\u0069n","\u0077\u0031\u0030":"\u0075\u0072n\u003a\u0073\u0063\u0068e\u006d\u0061s\u002d\u006d\u0069\u0063\u0072\u006f\u0073\u006ff\u0074\u002d\u0063\u006f\u006d\u003a\u006f\u0066\u0066\u0069\u0063\u0065:\u0077\u006f\u0072\u0064","\u0077\u0031\u0034":"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u006d\u006c","\u0077\u0031\u0035":"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0032\u002f\u0077\u006f\u0072\u0064\u006d\u006c","\u0077\u006e\u0065":"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0030\u0036\u002f\u0077\u006f\u0072\u0064\u006d\u006c","\u0077\u0070":"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006f\u0070\u0065\u006ex\u006d\u006c\u0066\u006f\u0072\u006d\u0061\u0074\u0073\u002e\u006f\u0072\u0067\u002f\u0064\u0072a\u0077\u0069\u006e\u0067\u006d\u006c\u002f\u0032\u0030\u0030\u0036\u002f\u0077\u006f\u0072\u0064\u0070\u0072\u006f\u0063\u0065\u0073\u0073\u0069n\u0067\u0044\u0072\u0061\u0077i\u006e\u0067","\u0077\u0070\u0031\u0034":"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068\u0065\u006da\u0073\u002e\u006d\u0069\u0063\u0072o\u0073\u006f\u0066\u0074\u002ec\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006fr\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u0070\u0072\u006f\u0063e\u0073\u0073\u0069\u006e\u0067\u0044\u0072\u0061w\u0069\u006e\u0067","\u0077\u0070\u0063":"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006d\u0069\u0063\u0072\u006f\u0073\u006ff\u0074\u002e\u0063\u006f\u006d\u002fo\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u00310\u002f\u0077o\u0072\u0064\u0070\u0072o\u0063\u0065\u0073\u0073\u0069n\u0067\u0043\u0061\u006e\u0076\u0061\u0073","\u0077\u0070\u0067":"\u0068\u0074\u0074\u0070\u003a/\u002f\u0073\u0063\u0068\u0065m\u0061s\u002e\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069c\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u0070\u0072\u006f\u0063\u0065\u0073\u0073\u0069n\u0067\u0047\u0072\u006f\u0075\u0070","\u0077\u0070\u0069":"\u0068t\u0074\u0070\u003a\u002f\u002f\u0073\u0063he\u006d\u0061\u0073\u002e\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002ec\u006f\u006d/\u006f\u0066\u0066i\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072d\u0070\u0072oc\u0065\u0073\u0073i\u006e\u0067\u0049\u006e\u006b","\u0077\u0070\u0073":"\u0068\u0074\u0074\u0070\u003a/\u002f\u0073\u0063\u0068\u0065m\u0061s\u002e\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069c\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0030\u002f\u0077\u006f\u0072\u0064\u0070\u0072\u006f\u0063\u0065\u0073\u0073\u0069n\u0067\u0053\u0068\u0061\u0070\u0065","\u0078\u0073\u0069":"\u0068\u0074\u0074\u0070\u003a/\u002f\u0077\u0077\u0077\u002e\u0077\u0033\u002e\u006f\u0072\u0067\u002f\u00320\u0030\u0031\u002f\u0058\u004d\u004c\u0053\u0063\u0068\u0065\u006d\u0061\u002d\u0069\u006e\u0073\u0074\u0061\u006e\u0063\u0065","\u0078\u0031\u0035a\u0063":"ht\u0074\u0070:\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0073\u0070\u0072\u0065\u0061\u0064\u0073h\u0065e\u0074\u006d\u006c\u002f\u0032\u0030\u0031\u0030/\u00311\u002f\u0061c","\u0077\u0031\u0036s\u0065":"\u0068\u0074\u0074\u0070\u003a\u002f\u002f\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u002e\u006d\u0069\u0063\u0072\u006fs\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006ff\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u00315\u002f\u0077\u006f\u0072\u0064\u006dl\u002f\u0073\u0079m\u0065\u0078","\u0077\u0031\u0036\u0063\u0069\u0064":"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068e\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002ec\u006f\u006d\u002f\u006f\u0066fi\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0036\u002f\u0077\u006f\u0072\u0064\u006d\u006c\u002f\u0063\u0069\u0064","\u0077\u0031\u0036":"\u0068\u0074t\u0070\u003a\u002f\u002f\u0073c\u0068\u0065\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002e\u0063\u006f\u006d\u002f\u006f\u0066\u0066\u0069\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0038\u002f\u0077\u006f\u0072\u0064\u006d\u006c","\u0077\u0031\u0036\u0063\u0065\u0078":"\u0068\u0074\u0074\u0070\u003a\u002f/\u0073\u0063\u0068e\u006d\u0061\u0073.\u006d\u0069\u0063\u0072\u006f\u0073\u006f\u0066\u0074\u002ec\u006f\u006d\u002f\u006f\u0066fi\u0063\u0065\u002f\u0077\u006f\u0072\u0064\u002f\u0032\u0030\u0031\u0038\u002f\u0077\u006f\u0072\u0064\u006d\u006c\u002f\u0063\u0065\u0078","\u0078\u006d\u006c":"\u0068\u0074tp\u003a\u002f\u002fw\u0077\u0077\u002e\u00773.o\u0072g/\u0058\u004d\u004c\u002f\u0031\u0039\u00398/\u006e\u0061\u006d\u0065\u0073\u0070\u0061c\u0065"};
type any struct{XMLName _b .Name ;Attrs []_b .Attr `xml:",any,attr"`;Nodes []*any `xml:",any"`;Data []byte `xml:",chardata"`;};

// UnmarshalXML implements the xml.Unmarshaler interface.
func (_fff *XSDAny )UnmarshalXML (d *_b .Decoder ,start _b .StartElement )error {_ef :=any {};if _aaf :=d .DecodeElement (&_ef ,&start );_aaf !=nil {return _aaf ;};_ed (&_ef );_fff .XMLName =_ef .XMLName ;_fff .Attrs =_ef .Attrs ;_fff .Data =_ef .Data ;
_fff .Nodes =_aea (_ef .Nodes );return nil ;};

// CreateElement creates an element with the given namespace and name. It is
// used to unmarshal some xsd:any elements to the appropriate concrete type.
func CreateElement (start _b .StartElement )(Any ,error ){_f ,_ac :=_af [start .Name .Space +"\u002f"+start .Name .Local ];if !_ac {_dd :=&XSDAny {};return _dd ,nil ;};_bc :=_ab .ValueOf (_f );_cf :=_bc .Call (nil );if len (_cf )!=1{return nil ,_e .Errorf ("\u0063\u006fn\u0073\u0074\u0072\u0075\u0063t\u006f\u0072\u0020\u0066\u0075n\u0063\u0074\u0069\u006f\u006e\u0020\u0073\u0068\u006f\u0075\u006c\u0064\u0020\u0072\u0065\u0074\u0075\u0072\u006e\u0020\u006f\u006e\u0065\u0020\u0076\u0061\u006c\u0075\u0065\u002c\u0020\u0067\u006f\u0074\u0020\u0025\u0064",len (_cf ));
};_cd ,_ac :=_cf [0].Interface ().(Any );if !_ac {return nil ,_c .New ("\u0063o\u006e\u0073t\u0072\u0075\u0063\u0074o\u0072\u0020\u0066u\u006e\u0063\u0074\u0069\u006f\u006e\u0020\u0073\u0068ou\u006c\u0064\u0020r\u0065\u0074u\u0072\u006e\u0020\u0061\u006e\u0079 \u0027\u0041n\u0079\u0027");
};return _cd ,nil ;};func (_dba nsSet )applyToNode (_cec *any ){if _cec .XMLName .Space ==""{return ;};_egg :=_dba .getPrefix (_cec .XMLName .Space );_cec .XMLName .Space ="";_cec .XMLName .Local =_egg +"\u003a"+_cec .XMLName .Local ;_aadc :=_cec .Attrs ;
_cec .Attrs =nil ;for _ ,_abe :=range _aadc {if _abe .Name .Space =="\u0078\u006d\u006cn\u0073"{continue ;};if _abe .Name .Space !=""{_ccdc :=_dba .getPrefix (_abe .Name .Space );_abe .Name .Space ="";_abe .Name .Local =_ccdc +"\u003a"+_abe .Name .Local ;
};_cec .Attrs =append (_cec .Attrs ,_abe );};for _ ,_cgb :=range _cec .Nodes {_dba .applyToNode (_cgb );};};var _age =map[string ]bool {"\u0077\u0031\u0030":true ,"\u0077\u0031\u0034":true ,"\u0077\u0070\u0031\u0034":true ,"\u0077\u0031\u0035":true ,"\u0078\u0031\u0035a\u0063":true ,"\u0077\u0031\u0036s\u0065":true ,"\u0077\u0031\u0036\u0063\u0069\u0064":true ,"\u0077\u0031\u0036":true ,"\u0077\u0031\u0036\u0063\u0065\u0078":true };


// Int8 returns a copy of v as a pointer.
func Int8 (v int8 )*int8 {_dfe :=v ;return &_dfe };

// MarshalXML implements the xml.Marshaler interface.
func (_ffe *any )MarshalXML (e *_b .Encoder ,start _b .StartElement )error {start .Name =_ffe .XMLName ;start .Attr =_ffe .Attrs ;if _eg :=e .EncodeToken (start );_eg !=nil {return _eg ;};if len (_ffe .Nodes )> 0{for _ ,_fce :=range _ffe .Nodes {if _eeca :=e .Encode (_fce );
_eeca !=nil {return _eeca ;};};};if len (_ffe .Data )> 0{if _cgc :=e .EncodeToken (_b .CharData (_ffe .Data ));_cgc !=nil {return _cgc ;};};return e .EncodeToken (start .End ());};

// NeedsSpacePreserve returns true if the string has leading or trailing space.
func NeedsSpacePreserve (s string )bool {if len (s )==0{return false ;};switch s [0]{case '\t','\n','\v','\f','\r',' ',0x85,0xA0:return true ;};switch s [len (s )-1]{case '\t','\n','\v','\f','\r',' ',0x85,0xA0:return true ;};return false ;};

// XSDAny  is used to marshal/unmarshal xsd:any types in the OOXML schema.
type XSDAny struct{XMLName _b .Name ;Attrs []_b .Attr ;Data []byte ;Nodes []*XSDAny ;};

// Bool returns a copy of v as a pointer.
func Bool (v bool )*bool {_abd :=v ;return &_abd };

// RegisterConstructor registers a constructor function used for unmarshaling
// xsd:any elements.
func RegisterConstructor (ns ,name string ,fn interface{}){_af [ns +"\u002f"+name ]=fn };func _aea (_ebd []*any )[]*XSDAny {_agf :=[]*XSDAny {};for _ ,_cbg :=range _ebd {_fbb :=&XSDAny {};_fbb .XMLName =_cbg .XMLName ;_fbb .Attrs =_cbg .Attrs ;_fbb .Data =_cbg .Data ;
_fbb .Nodes =_aea (_cbg .Nodes );_agf =append (_agf ,_fbb );};return _agf ;};func _ed (_be *any ){for _ ,_aag :=range _be .Nodes {_ed (_aag );};};

// DocType represents one of the three document types supported (docx/xlsx/pptx)
type DocType byte ;

// AbsoluteFilename returns the full path to a file from the root of the zip
// container. Index is used in some cases for files which there may be more than
// one of (e.g. worksheets/drawings/charts)
func AbsoluteFilename (dt DocType ,typ string ,index int )string {switch typ {case CorePropertiesType :return "\u0064\u006f\u0063\u0050\u0072\u006f\u0070\u0073\u002f\u0063\u006f\u0072e\u002e\u0078\u006d\u006c";case CustomPropertiesType :return "\u0064\u006f\u0063\u0050ro\u0070\u0073\u002f\u0063\u0075\u0073\u0074\u006f\u006d\u002e\u0078\u006d\u006c";
case ExtendedPropertiesType ,ExtendedPropertiesTypeStrict :return "\u0064\u006fc\u0050\u0072\u006fp\u0073\u002f\u0061\u0070\u0070\u002e\u0078\u006d\u006c";case ThumbnailType ,ThumbnailTypeStrict :return "\u0064\u006f\u0063Pr\u006f\u0070\u0073\u002f\u0074\u0068\u0075\u006d\u0062\u006e\u0061\u0069\u006c\u002e\u006a\u0070\u0065\u0067";
case CustomXMLType :return _e .Sprintf ("c\u0075s\u0074\u006f\u006d\u0058\u006d\u006c\u002f\u0069t\u0065\u006d\u0025\u0064.x\u006d\u006c",index );case PresentationPropertiesType :return "\u0070\u0070\u0074\u002f\u0070\u0072\u0065\u0073\u0050\u0072\u006f\u0070s\u002e\u0078\u006d\u006c";
case ViewPropertiesType :switch dt {case DocTypePresentation :return "\u0070\u0070\u0074\u002f\u0076\u0069\u0065\u0077\u0050\u0072\u006f\u0070s\u002e\u0078\u006d\u006c";case DocTypeSpreadsheet :return "\u0078\u006c/\u0076\u0069\u0065w\u0050\u0072\u006f\u0070\u0073\u002e\u0078\u006d\u006c";
case DocTypeDocument :return "\u0077o\u0072d\u002f\u0076\u0069\u0065\u0077P\u0072\u006fp\u0073\u002e\u0078\u006d\u006c";};case TableStylesType :switch dt {case DocTypePresentation :return "\u0070\u0070\u0074\u002fta\u0062\u006c\u0065\u0053\u0074\u0079\u006c\u0065\u0073\u002e\u0078\u006d\u006c";
case DocTypeSpreadsheet :return "\u0078l\u002ft\u0061\u0062\u006c\u0065\u0053t\u0079\u006ce\u0073\u002e\u0078\u006d\u006c";case DocTypeDocument :return "w\u006fr\u0064\u002f\u0074\u0061\u0062\u006c\u0065\u0053t\u0079\u006c\u0065\u0073.x\u006d\u006c";};
case HyperLinkType :return "";case OfficeDocumentType ,OfficeDocumentTypeStrict :switch dt {case DocTypeSpreadsheet :return "\u0078l\u002fw\u006f\u0072\u006b\u0062\u006f\u006f\u006b\u002e\u0078\u006d\u006c";case DocTypeDocument :return "\u0077\u006f\u0072\u0064\u002f\u0064\u006f\u0063\u0075\u006d\u0065\u006et\u002e\u0078\u006d\u006c";
case DocTypePresentation :return "p\u0070t\u002f\u0070\u0072\u0065\u0073\u0065\u006e\u0074a\u0074\u0069\u006f\u006e.x\u006d\u006c";default:_ag .Log .Debug ("\u0075\u006e\u0073u\u0070\u0070\u006f\u0072t\u0065\u0064\u0020\u0074\u0079\u0070\u0065 \u0025\u0073\u0020\u0070\u0061\u0069\u0072\u0020\u0061\u006e\u0064\u0020\u0025\u0076",typ ,dt );
};case ThemeType ,ThemeTypeStrict ,ThemeContentType :switch dt {case DocTypeSpreadsheet :return _e .Sprintf ("x\u006c/\u0074\u0068\u0065\u006d\u0065\u002f\u0074\u0068e\u006d\u0065\u0025\u0064.x\u006d\u006c",index );case DocTypeDocument :return _e .Sprintf ("\u0077\u006f\u0072\u0064/t\u0068\u0065\u006d\u0065\u002f\u0074\u0068\u0065\u006d\u0065\u0025\u0064\u002e\u0078m\u006c",index );
case DocTypePresentation :return _e .Sprintf ("p\u0070\u0074\u002f\u0074he\u006de\u002f\u0074\u0068\u0065\u006de\u0025\u0064\u002e\u0078\u006d\u006c",index );default:_ag .Log .Debug ("\u0075\u006e\u0073u\u0070\u0070\u006f\u0072t\u0065\u0064\u0020\u0074\u0079\u0070\u0065 \u0025\u0073\u0020\u0070\u0061\u0069\u0072\u0020\u0061\u006e\u0064\u0020\u0025\u0076",typ ,dt );
};case StylesType ,StylesTypeStrict :switch dt {case DocTypeSpreadsheet :return "\u0078\u006c\u002f\u0073\u0074\u0079\u006c\u0065\u0073\u002e\u0078\u006d\u006c";case DocTypeDocument :return "\u0077o\u0072d\u002f\u0073\u0074\u0079\u006c\u0065\u0073\u002e\u0078\u006d\u006c";
case DocTypePresentation :return "\u0070\u0070\u0074\u002f\u0073\u0074\u0079\u006c\u0065s\u002e\u0078\u006d\u006c";default:_ag .Log .Debug ("\u0075\u006e\u0073u\u0070\u0070\u006f\u0072t\u0065\u0064\u0020\u0074\u0079\u0070\u0065 \u0025\u0073\u0020\u0070\u0061\u0069\u0072\u0020\u0061\u006e\u0064\u0020\u0025\u0076",typ ,dt );
};case ChartType ,ChartTypeStrict ,ChartContentType :switch dt {case DocTypeSpreadsheet :return _e .Sprintf ("x\u006c\u002f\u0063\u0068ar\u0074s\u002f\u0063\u0068\u0061\u0072t\u0025\u0064\u002e\u0078\u006d\u006c",index );case DocTypeDocument :return _e .Sprintf ("\u0077\u006f\u0072d/\u0063\u0068\u0061\u0072\u0074\u0073\u002f\u0063\u0068\u0061\u0072\u0074\u0025\u0064\u002e\u0078\u006d\u006c",index );
case DocTypePresentation :return _e .Sprintf ("\u0070\u0070\u0074\u002fch\u0061\u0072\u0074\u0073\u002f\u0063\u0068\u0061\u0072\u0074\u0025\u0064\u002e\u0078m\u006c",index );default:_ag .Log .Debug ("\u0075\u006e\u0073u\u0070\u0070\u006f\u0072t\u0065\u0064\u0020\u0074\u0079\u0070\u0065 \u0025\u0073\u0020\u0070\u0061\u0069\u0072\u0020\u0061\u006e\u0064\u0020\u0025\u0076",typ ,dt );
};case TableType ,TableTypeStrict ,TableContentType :return _e .Sprintf ("x\u006c\u002f\u0074\u0061bl\u0065s\u002f\u0074\u0061\u0062\u006ce\u0025\u0064\u002e\u0078\u006d\u006c",index );case DrawingType ,DrawingTypeStrict ,DrawingContentType :switch dt {case DocTypeSpreadsheet :return _e .Sprintf ("\u0078l\u002f\u0064\u0072\u0061w\u0069\u006e\u0067\u0073\u002fd\u0072a\u0077i\u006e\u0067\u0025\u0064\u002e\u0078\u006dl",index );
default:_ag .Log .Debug ("\u0075\u006e\u0073u\u0070\u0070\u006f\u0072t\u0065\u0064\u0020\u0074\u0079\u0070\u0065 \u0025\u0073\u0020\u0070\u0061\u0069\u0072\u0020\u0061\u006e\u0064\u0020\u0025\u0076",typ ,dt );};case CommentsType ,CommentsTypeStrict ,CommentsContentType :switch dt {case DocTypeSpreadsheet :return _e .Sprintf ("\u0078\u006c\u002f\u0063\u006f\u006d\u006d\u0065\u006e\u0074\u0073\u0025d\u002e\u0078\u006d\u006c",index );
case DocTypeDocument :return "\u0077\u006f\u0072\u0064\u002f\u0063\u006f\u006d\u006d\u0065\u006e\u0074s\u002e\u0078\u006d\u006c";default:_ag .Log .Debug ("\u0075\u006e\u0073u\u0070\u0070\u006f\u0072t\u0065\u0064\u0020\u0074\u0079\u0070\u0065 \u0025\u0073\u0020\u0070\u0061\u0069\u0072\u0020\u0061\u006e\u0064\u0020\u0025\u0076",typ ,dt );
};case VMLDrawingType ,VMLDrawingTypeStrict ,VMLDrawingContentType :switch dt {case DocTypeSpreadsheet :return _e .Sprintf ("\u0078\u006c\u002f\u0064r\u0061\u0077\u0069\u006e\u0067\u0073\u002f\u0076\u006d\u006cD\u0072a\u0077\u0069\u006e\u0067\u0025\u0064\u002ev\u006d\u006c",index );
default:_ag .Log .Debug ("\u0075\u006e\u0073u\u0070\u0070\u006f\u0072t\u0065\u0064\u0020\u0074\u0079\u0070\u0065 \u0025\u0073\u0020\u0070\u0061\u0069\u0072\u0020\u0061\u006e\u0064\u0020\u0025\u0076",typ ,dt );};case ImageType ,ImageTypeStrict :switch dt {case DocTypeDocument :return _e .Sprintf ("\u0077\u006f\u0072\u0064/m\u0065\u0064\u0069\u0061\u002f\u0069\u006d\u0061\u0067\u0065\u0025\u0064\u002e\u0070n\u0067",index );
case DocTypeSpreadsheet :return _e .Sprintf ("x\u006c/\u006d\u0065\u0064\u0069\u0061\u002f\u0069\u006da\u0067\u0065\u0025\u0064.p\u006e\u0067",index );case DocTypePresentation :return _e .Sprintf ("p\u0070\u0074\u002f\u006ded\u0069a\u002f\u0069\u006d\u0061\u0067e\u0025\u0064\u002e\u0070\u006e\u0067",index );
default:_ag .Log .Debug ("\u0075\u006e\u0073u\u0070\u0070\u006f\u0072t\u0065\u0064\u0020\u0074\u0079\u0070\u0065 \u0025\u0073\u0020\u0070\u0061\u0069\u0072\u0020\u0061\u006e\u0064\u0020\u0025\u0076",typ ,dt );};case WorksheetType ,WorksheetTypeStrict ,WorksheetContentType :return _e .Sprintf ("\u0078l\u002f\u0077\u006f\u0072k\u0073\u0068\u0065\u0065\u0074s\u002fs\u0068e\u0065\u0074\u0025\u0064\u002e\u0078\u006dl",index );
case SharedStringsType ,SharedStringsTypeStrict ,SharedStringsContentType :return "x\u006c/\u0073\u0068\u0061\u0072\u0065\u0064\u0053\u0074r\u0069\u006e\u0067\u0073.x\u006d\u006c";case FontTableType ,FontTableTypeStrict :return "\u0077o\u0072d\u002f\u0066\u006f\u006e\u0074T\u0061\u0062l\u0065\u002e\u0078\u006d\u006c";
case EndNotesType ,EndNotesTypeStrict :return "\u0077\u006f\u0072\u0064\u002f\u0065\u006e\u0064\u006e\u006f\u0074\u0065s\u002e\u0078\u006d\u006c";case FootNotesType ,FootNotesTypeStrict :return "\u0077o\u0072d\u002f\u0066\u006f\u006f\u0074n\u006f\u0074e\u0073\u002e\u0078\u006d\u006c";
case NumberingType ,NumberingTypeStrict :return "\u0077o\u0072d\u002f\u006e\u0075\u006d\u0062e\u0072\u0069n\u0067\u002e\u0078\u006d\u006c";case WebSettingsType ,WebSettingsTypeStrict :return "w\u006fr\u0064\u002f\u0077\u0065\u0062\u0053\u0065\u0074t\u0069\u006e\u0067\u0073.x\u006d\u006c";
case SettingsType ,SettingsTypeStrict :return "\u0077\u006f\u0072\u0064\u002f\u0073\u0065\u0074\u0074\u0069\u006e\u0067s\u002e\u0078\u006d\u006c";case HeaderType ,HeaderTypeStrict :return _e .Sprintf ("\u0077\u006f\u0072\u0064\u002f\u0068\u0065\u0061\u0064\u0065\u0072\u0025d\u002e\u0078\u006d\u006c",index );
case FooterType ,FooterTypeStrict :return _e .Sprintf ("\u0077\u006f\u0072\u0064\u002f\u0066\u006f\u006f\u0074\u0065\u0072\u0025d\u002e\u0078\u006d\u006c",index );case ControlType ,ControlTypeStrict :switch dt {case DocTypeSpreadsheet :return _e .Sprintf ("\u0078l\u002f\u0061\u0063\u0074\u0069\u0076\u0065\u0058\u002f\u0061\u0063t\u0069\u0076\u0065\u0058\u0025\u0064\u002e\u0078\u006d\u006c",index );
case DocTypeDocument :return _e .Sprintf ("\u0077\u006f\u0072\u0064\u002f\u0061\u0063\u0074\u0069\u0076\u0065X\u002f\u0061\u0063\u0074\u0069\u0076\u0065\u0058\u0025\u0064.\u0078\u006d\u006c",index );case DocTypePresentation :return _e .Sprintf ("\u0070p\u0074\u002f\u0061\u0063t\u0069\u0076\u0065\u0058\u002fa\u0063t\u0069v\u0065\u0058\u0025\u0064\u002e\u0078\u006dl",index );
default:_ag .Log .Debug ("\u0075\u006e\u0073u\u0070\u0070\u006f\u0072t\u0065\u0064\u0020\u0074\u0079\u0070\u0065 \u0025\u0073\u0020\u0070\u0061\u0069\u0072\u0020\u0061\u006e\u0064\u0020\u0025\u0076",typ ,dt );};case SlideType ,SlideTypeStrict :return _e .Sprintf ("\u0070\u0070\u0074\u002fsl\u0069\u0064\u0065\u0073\u002f\u0073\u006c\u0069\u0064\u0065\u0025\u0064\u002e\u0078m\u006c",index );
case SlideLayoutType :return _e .Sprintf ("\u0070\u0070\u0074/s\u006c\u0069\u0064\u0065\u004c\u0061\u0079\u006f\u0075t\u0073/\u0073l\u0069d\u0065\u004c\u0061\u0079\u006f\u0075\u0074\u0025\u0064\u002e\u0078\u006d\u006c",index );case SlideMasterType :return _e .Sprintf ("\u0070\u0070\u0074/s\u006c\u0069\u0064\u0065\u004d\u0061\u0073\u0074\u0065r\u0073/\u0073l\u0069d\u0065\u004d\u0061\u0073\u0074\u0065\u0072\u0025\u0064\u002e\u0078\u006d\u006c",index );
case HandoutMasterType :return _e .Sprintf ("\u0070\u0070\u0074\u002f\u0068\u0061\u006e\u0064\u006f\u0075\u0074\u004d\u0061\u0073\u0074\u0065\u0072\u0073\u002f\u0068\u0061\u006e\u0064\u006fu\u0074\u004d\u0061\u0073\u0074e\u0072\u0025d\u002e\u0078\u006d\u006c",index );
case NotesMasterType :return _e .Sprintf ("\u0070\u0070\u0074/n\u006f\u0074\u0065\u0073\u004d\u0061\u0073\u0074\u0065r\u0073/\u006eo\u0074e\u0073\u004d\u0061\u0073\u0074\u0065\u0072\u0025\u0064\u002e\u0078\u006d\u006c",index );default:_ag .Log .Debug ("\u0075\u006e\u0073\u0075pp\u006f\u0072\u0074\u0065\u0064\u0020\u0074\u0079\u0070\u0065\u0020\u0025\u0073",typ );
};return "";};

// RelativeImageFilename returns an image filename relative to the source file referenced
// from a relationships file. It is identical to RelativeFilename but is used particularly for images
// in order to handle different image formats.
func RelativeImageFilename (dt DocType ,relToTyp ,typ string ,index int ,fileExtension string )string {_acb :=RelativeFilename (dt ,relToTyp ,typ ,index );return _acb [0:len (_acb )-3]+fileExtension ;};type nsSet struct{_ae map[string ]string ;_bd map[string ]string ;
_dde []string ;};

// DisableLogging sets the Log function to a no-op so that any log messages are
// silently discarded.
func DisableLogging (){Log =func (string ,...interface{}){}};

// AddPreserveSpaceAttr adds an xml:space="preserve" attribute to a start
// element if it is required for the string s.
func AddPreserveSpaceAttr (se *_b .StartElement ,s string ){if NeedsSpacePreserve (s ){se .Attr =append (se .Attr ,_b .Attr {Name :_b .Name {Local :"\u0078m\u006c\u003a\u0073\u0070\u0061\u0063e"},Value :"\u0070\u0072\u0065\u0073\u0065\u0072\u0076\u0065"});
};};