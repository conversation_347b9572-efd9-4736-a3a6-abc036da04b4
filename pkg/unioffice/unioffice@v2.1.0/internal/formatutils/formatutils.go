//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package formatutils ;import (_ac "fmt";_f "github.com/unidoc/unioffice/v2/schema/soo/wml";_g "strconv";_e "strings";);func Initials (text string )string {if text ==""{return "";};_caa :=_e .Split (text ,"\u0020");_bc :=_e .Builder {};for _ ,_aab :=range _caa {_bc .WriteByte (_aab [0]);
};return _bc .String ();};func _gc (_ee string )(_b []string ){for _eeb :=0;_eeb < len (_ee )-2;_eeb ++{if string (_ee [_eeb ])=="\u0025"{if !_e .Contains (string (_ee [_eeb +2:]),"\u0025"){if _eeb ==0{_b =append (_b ,_ac .Sprintf ("\u0025\u0073\u0025\u0073\u0025\u0073",string (_ee [_eeb ]),string (_ee [_eeb +1]),string (_ee [_eeb +2:])));
}else {_b =append (_b ,_ac .Sprintf ("\u0025\u0073\u0025\u0073\u0025\u0073\u0025\u0073",string (_ee [_eeb -1]),string (_ee [_eeb ]),string (_ee [_eeb +1]),string (_ee [_eeb +2:])));};}else {_b =append (_b ,_ac .Sprintf ("\u0025\u0073\u0025\u0073\u0025\u0073",string (_ee [_eeb ]),string (_ee [_eeb +1]),string (_ee [_eeb +2])));
};};};return ;};func StringToNumbers (str string )(int ,bool ){_gb :=0;_cfg :=false ;for _ ,_eff :=range []byte (str ){_eff -='0';if _eff > 9{continue ;};_gb =_gb *10+int (_eff );_cfg =true ;};return _gb ,_cfg ;};func FormatNumberingText (currentNumber int64 ,ilvl int64 ,lvlText string ,numFmt *_f .CT_NumFmt ,levelNumbers map[int64 ]int64 )string {_c :=_gc (lvlText );
_ad :=_bfe (currentNumber ,numFmt );_fa :=int64 (0);for _gg ,_d :=range _c {_ef :=_ac .Sprintf ("\u0025\u0025\u0025\u0064",_gg +1);if len (_c )==1{_ef =_ac .Sprintf ("\u0025\u0025\u0025\u0064",ilvl +1);_c [_gg ]=_e .Replace (_d ,_ef ,_ad ,1);break ;};if ilvl > 0&&ilvl > _fa &&_gg < (len (_c )-1){_da :=_bfe (levelNumbers [_fa ],numFmt );
_c [_gg ]=_e .Replace (_d ,_ef ,_da ,1);_fa ++;}else {_c [_gg ]=_e .Replace (_d ,_ef ,_ad ,1);};};return _e .Join (_c ,"");};var (_gf =[]string {"","\u0049","\u0049\u0049","\u0049\u0049\u0049","\u0049\u0056","\u0056","\u0056\u0049","\u0056\u0049\u0049","\u0056\u0049\u0049\u0049","\u0049\u0058"};
_gd =[]string {"","\u0058","\u0058\u0058","\u0058\u0058\u0058","\u0058\u004c","\u004c","\u004c\u0058","\u004c\u0058\u0058","\u004c\u0058\u0058\u0058","\u0058\u0043"};_bf =[]string {"","\u0043","\u0043\u0043","\u0043\u0043\u0043","\u0043\u0044","\u0044","\u0044\u0043","\u0044\u0043\u0043","\u0044\u0043\u0043\u0043","\u0043\u004d","\u004d"};
_cb =[]string {"","\u004d","\u004d\u004d","\u004d\u004d\u004d","\u004d\u004d\u004d\u004d","\u004d\u004d\u004dM\u004d","\u004d\u004d\u004d\u004d\u004d\u004d","\u004dM\u004d\u004d\u004d\u004d\u004d","\u004d\u004d\u004d\u004d\u004d\u004d\u004d\u004d","\u004dM\u004d\u004d\u004d\u004d\u004d\u004dM","\u004d\u004d\u004d\u004d\u004d\u004d\u004d\u004d\u004d\u004d"};
_aa =[]string {"\u006f\u006e\u0065","\u0074\u0077\u006f","\u0074\u0068\u0072e\u0065","\u0066\u006f\u0075\u0072","\u0066\u0069\u0076\u0065","\u0073\u0069\u0078","\u0073\u0065\u0076e\u006e","\u0065\u0069\u0067h\u0074","\u006e\u0069\u006e\u0065","\u0074\u0065\u006e","\u0065\u006c\u0065\u0076\u0065\u006e","\u0074\u0077\u0065\u006c\u0076\u0065","\u0074\u0068\u0069\u0072\u0074\u0065\u0065\u006e","\u0066\u006f\u0075\u0072\u0074\u0065\u0065\u006e","\u0066i\u0066\u0074\u0065\u0065\u006e","\u0073i\u0078\u0074\u0065\u0065\u006e","\u0073e\u0076\u0065\u006e\u0074\u0065\u0065n","\u0065\u0069\u0067\u0068\u0074\u0065\u0065\u006e","\u006e\u0069\u006e\u0065\u0074\u0065\u0065\u006e"};
_gcb =[]string {"\u0074\u0065\u006e","\u0074\u0077\u0065\u006e\u0074\u0079","\u0074\u0068\u0069\u0072\u0074\u0079","\u0066\u006f\u0072t\u0079","\u0066\u0069\u0066t\u0079","\u0073\u0069\u0078t\u0079","\u0073e\u0076\u0065\u006e\u0074\u0079","\u0065\u0069\u0067\u0068\u0074\u0079","\u006e\u0069\u006e\u0065\u0074\u0079"};
_cba =[]string {"\u0066\u0069\u0072s\u0074","\u0073\u0065\u0063\u006f\u006e\u0064","\u0074\u0068\u0069r\u0064","\u0066\u006f\u0075\u0072\u0074\u0068","\u0066\u0069\u0066t\u0068","\u0073\u0069\u0078t\u0068","\u0073e\u0076\u0065\u006e\u0074\u0068","\u0065\u0069\u0067\u0068\u0074\u0068","\u006e\u0069\u006et\u0068","\u0074\u0065\u006et\u0068","\u0065\u006c\u0065\u0076\u0065\u006e\u0074\u0068","\u0074w\u0065\u006c\u0066\u0074\u0068","\u0074\u0068\u0069\u0072\u0074\u0065\u0065\u006e\u0074\u0068","\u0066\u006f\u0075\u0072\u0074\u0065\u0065\u006e\u0074\u0068","\u0066i\u0066\u0074\u0065\u0065\u006e\u0074h","\u0073i\u0078\u0074\u0065\u0065\u006e\u0074h","s\u0065\u0076\u0065\u006e\u0074\u0065\u0065\u006e\u0074\u0068","\u0065\u0069\u0067\u0068\u0074\u0065\u0065\u006e\u0074\u0068","\u006e\u0069\u006e\u0065\u0074\u0065\u0065\u006e\u0074\u0068"};
_fae =[]string {"\u0074\u0065\u006et\u0068","\u0074w\u0065\u006e\u0074\u0069\u0065\u0074h","\u0074h\u0069\u0072\u0074\u0069\u0065\u0074h","\u0066\u006f\u0072\u0074\u0069\u0065\u0074\u0068","\u0066\u0069\u0066\u0074\u0069\u0065\u0074\u0068","\u0073\u0069\u0078\u0074\u0069\u0065\u0074\u0068","\u0073\u0065\u0076\u0065\u006e\u0074\u0069\u0065\u0074\u0068","\u0065i\u0067\u0068\u0074\u0069\u0065\u0074h","\u006ei\u006e\u0065\u0074\u0069\u0065\u0074h"};
_dg ="\u0041\u0042\u0043\u0044\u0045\u0046\u0047\u0048\u0049\u004a\u004bL\u004d\u004e\u004f\u0050\u0051\u0052\u0053\u0054\u0055\u0056W\u0058\u0059\u005a";);func _bfe (_de int64 ,_fc *_f .CT_NumFmt )(_ec string ){if _fc ==nil {return ;};_bg :=_fc .ValAttr ;
switch _bg {case _f .ST_NumberFormatNone :_ec ="";case _f .ST_NumberFormatDecimal :_ec =_g .Itoa (int (_de ));case _f .ST_NumberFormatDecimalZero :_ec =_g .Itoa (int (_de ));if _de < 10{_ec ="\u0030"+_ec ;};case _f .ST_NumberFormatUpperRoman :var (_cf =_de %10;
_ff =(_de %100)/10;_acc =(_de %1000)/100;_eef =_de /1000;);_ec =_cb [_eef ]+_bf [_acc ]+_gd [_ff ]+_gf [_cf ];case _f .ST_NumberFormatLowerRoman :var (_df =_de %10;_ce =(_de %100)/10;_ba =(_de %1000)/100;_efg =_de /1000;);_ec =_cb [_efg ]+_bf [_ba ]+_gd [_ce ]+_gf [_df ];
_ec =_e .ToLower (_ec );case _f .ST_NumberFormatUpperLetter :_cbf :=_de %780;if _cbf ==0{_cbf =780;};_ggf :=(_cbf -1)/26;_fd :=(_cbf -1)%26;_ca :=_dg [_ggf +_fd ];_ec =string (_ca );case _f .ST_NumberFormatLowerLetter :_dff :=_de %780;if _dff ==0{_dff =780;
};_bgf :=(_dff -1)/26;_fg :=(_dff -1)%26;_bd :=_dg [_bgf +_fg ];_ec =_e .ToLower (string (_bd ));default:_ec ="";};return ;};