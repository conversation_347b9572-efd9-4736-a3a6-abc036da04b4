//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package mscfb ;import (_c "bytes";_g "encoding/binary";_fe "fmt";_d "github.com/richardlehane/msoleps/types";_ga "github.com/unidoc/unioffice/v2/internal/mscfb/rw";_fa "io";_fd "os";_e "strconv";_bc "time";_a "unicode";_b "unicode/utf16";);func _bgc (_abg [][2]int64 )[][2]int64 {_beg :=len (_abg );
for _aeg ,_gfb :=0,0;_aeg < _beg &&_gfb +1< len (_abg );_aeg ++{if _abg [_gfb ][0]+_abg [_gfb ][1]==_abg [_gfb +1][0]{_abg [_gfb ][1]=_abg [_gfb ][1]+_abg [_gfb +1][1];for _fcd :=range _abg [_gfb +1:len (_abg )-1]{_abg [_gfb +1+_fcd ]=_abg [_fcd +_gfb +2];
};_abg =_abg [:len (_abg )-1];}else {_gfb +=1;};};return _abg ;};func (_fba *Reader )setMiniStream ()error {if _fba ._caaa [0]._ccd ==_ebf ||_fba ._edd ._eefg ==_ebf ||_fba ._edd ._ceeg ==0{return nil ;};_cgb :=int (_fba ._edd ._ceeg );_fba ._edd ._gaa =make ([]uint32 ,_cgb );
_fba ._edd ._gaa [0]=_fba ._edd ._eefg ;for _adag :=1;_adag < _cgb ;_adag ++{_beda ,_gcd :=_fba .findNext (_fba ._edd ._gaa [_adag -1],false );if _gcd !=nil {return Error {ErrFormat ,"s\u0065\u0074\u0074\u0069ng\u0020m\u0069\u006e\u0069\u0020\u0073t\u0072\u0065\u0061\u006d\u0020\u0028"+_gcd .Error ()+"\u0029",int64 (_fba ._edd ._gaa [_adag -1])};
};_fba ._edd ._gaa [_adag ]=_beda ;};_cgb =int (_fba ._fcg /4*_fba ._edd ._ceeg );_fba ._edd ._abgb =make ([]uint32 ,0,_cgb );_abcb :=_fba ._caaa [0]._ccd ;var _afcc error ;for _abcb !=_ebf {_fba ._edd ._abgb =append (_fba ._edd ._abgb ,_abcb );_abcb ,_afcc =_fba .findNext (_abcb ,false );
if _afcc !=nil {return Error {ErrFormat ,"s\u0065\u0074\u0074\u0069ng\u0020m\u0069\u006e\u0069\u0020\u0073t\u0072\u0065\u0061\u006d\u0020\u0028"+_afcc .Error ()+"\u0029",int64 (_abcb )};};};return nil ;};func (_acg *File )changeSize (_cd int64 )error {if _cd ==_acg .Size {return nil ;
};var _eebc *File ;for _ ,_bfa :=range _acg ._ddf ._caaa {if _bfa .Name ==_acg .Name {_eebc =_bfa ;break ;};};if _eebc ==nil {return _fe .Errorf ("\u004e\u006f\u0020\u0064\u0069\u0072e\u0063\u0074\u006f\u0072\u0079\u0020\u0065\u006e\u0074\u0072\u0079\u0020\u0066o\u0072\u0020\u0061\u0020\u0066\u0069\u006ce\u003a\u0020\u0025\u0073",_acg .Name );
};_cf :=_c .NewBuffer ([]byte {});if _eegb :=_g .Write (_cf ,_g .LittleEndian ,_cd );_eegb !=nil {return _eegb ;};for _aed ,_cgef :=range _cf .Bytes (){_eebc ._ccb [_aed ]=_cgef ;};var _eec int64 ;var _ada bool ;if _acg .Size < _eef {_ada =true ;_eec =int64 (_ccaa );
}else {_eec =int64 (_acg ._ddf ._fcg );};_ege :=int ((_acg .Size -1)/_eec )+1;_cbg :=int ((_cd -1)/_eec )+1;if _ege < _cbg {_dcb ,_dca :=_acg .findLast (_ada );if _dca !=nil {return _dca ;};_bca ,_dca :=_acg ._ddf .findNextFreeSector (_ada );if _dca !=nil {return _dca ;
};for _bfg :=_cbg -_ege ;_bfg > 0;_bfg --{if _ddd :=_acg ._ddf .saveToFatLocs (_dcb ,_bca ,_ada );_ddd !=nil {return _ddd ;};if _bfg > 1{_dcb =_bca ;_bca ++;}else if _ddb :=_acg ._ddf .saveToFatLocs (_bca ,_ebf ,_ada );_ddb !=nil {return _ddb ;};};}else if _ege > _cbg {_ggaa :=_acg ._ccd ;
var _agfd error ;for _egc :=0;_egc < _cbg -1;_egc ++{_ggaa ,_agfd =_acg ._ddf .findNext (_ggaa ,_ada );if _agfd !=nil {return _agfd ;};};if _fgg :=_acg ._ddf .saveToFatLocs (_ggaa ,_ebf ,_ada );_fgg !=nil {return _fgg ;};};_acg .Size =_cd ;return nil ;
};func _ge (_bb []byte )*directoryEntryFields {_ggf :=&directoryEntryFields {};for _adf :=range _ggf ._bcg {_ggf ._bcg [_adf ]=_g .LittleEndian .Uint16 (_bb [_adf *2:_adf *2+2]);};_ggf ._ec =_g .LittleEndian .Uint16 (_bb [64:66]);_ggf ._gac =uint8 (_bb [66]);
_ggf ._ac =uint8 (_bb [67]);_ggf ._gc =_g .LittleEndian .Uint32 (_bb [68:72]);_ggf ._gd =_g .LittleEndian .Uint32 (_bb [72:76]);_ggf ._eg =_g .LittleEndian .Uint32 (_bb [76:80]);_ggf ._fc =_d .MustGuid (_bb [80:96]);copy (_ggf ._gf [:],_bb [96:100]);_ggf ._db =_d .MustFileTime (_bb [100:108]);
_ggf ._cc =_d .MustFileTime (_bb [108:116]);_ggf ._ccd =_g .LittleEndian .Uint32 (_bb [116:120]);copy (_ggf ._ccb [:],_bb [120:128]);return _ggf ;};type headerFields struct{_fgcb uint64 ;_ [16]byte ;_dcf uint16 ;_bcf uint16 ;_ [2]byte ;_cbb uint16 ;_ [2]byte ;
_ [6]byte ;_dae uint32 ;_bcb uint32 ;_cgee uint32 ;_ [4]byte ;_ [4]byte ;_eefg uint32 ;_ceeg uint32 ;_bag uint32 ;_effb uint32 ;_dfc [109]uint32 ;};type File struct{Name string ;Initial uint16 ;Path []string ;Size int64 ;_gda int64 ;_ef uint32 ;_fcc int64 ;
*directoryEntryFields ;_ddf *Reader ;};func (_ca *Reader )traverse ()error {_ca .File =make ([]*File ,0,len (_ca ._caaa ));var (_cg func (int ,[]string );_fg error ;_ed int ;);_cg =func (_agf int ,_bd []string ){_ed ++;if _ed > len (_ca ._caaa ){_fg =Error {ErrTraverse ,"\u0074\u0072\u0061\u0076\u0065\u0072\u0073\u0061\u006c\u0020\u0063o\u0075\u006e\u0074\u0065\u0072\u0020\u006f\u0076\u0065\u0072f\u006c\u006f\u0077",int64 (_agf )};
return ;};if _agf < 0||_agf >=len (_ca ._caaa ){_fg =Error {ErrTraverse ,"\u0069\u006c\u006ceg\u0061\u006c\u0020\u0074\u0072\u0061\u0076\u0065\u0072\u0073\u0061\u006c\u0020\u0069\u006e\u0064\u0065\u0078",int64 (_agf )};return ;};_cb :=_ca ._caaa [_agf ];
if _cb ._gc !=_fcb {_cg (int (_cb ._gc ),_bd );};_ca .File =append (_ca .File ,_cb );_cb .Path =_bd ;if _cb ._eg !=_fcb {if _agf > 0{_cg (int (_cb ._eg ),append (_bd ,_cb .Name ));}else {_cg (int (_cb ._eg ),_bd );};};if _cb ._gd !=_fcb {_cg (int (_cb ._gd ),_bd );
};return ;};_cg (0,[]string {});return _fg ;};func (_dfe *File )WriteAt (p []byte ,off int64 )(_ab int ,_cda error ){_gbb ,_fed ,_ceb :=_dfe ._gda ,_dfe ._fcc ,_dfe ._ef ;_ ,_cda =_dfe .Seek (off ,0);if _cda ==nil {_ab ,_cda =_dfe .Write (p );};_dfe ._gda ,_dfe ._fcc ,_dfe ._ef =_gbb ,_fed ,_ceb ;
return _ab ,_cda ;};func (_bcac *Reader )readAt (_gbeb int64 ,_cbac int )([]byte ,error ){if _bcac ._gccf {_fec ,_bga :=_bcac ._efa .(slicer ).Slice (_gbeb ,_cbac );if _bga !=nil {return nil ,Error {ErrRead ,"\u0073\u006c\u0069\u0063er\u0020\u0072\u0065\u0061\u0064\u0020\u0065\u0072\u0072\u006f\u0072\u0020\u0028"+_bga .Error ()+"\u0029",_gbeb };
};return _fec ,nil ;};if _cbac > len (_bcac ._bfe ){return nil ,Error {ErrRead ,"\u0072\u0065ad\u0020\u006c\u0065n\u0067\u0074\u0068\u0020gre\u0061te\u0072\u0020\u0074\u0068\u0061\u006e\u0020re\u0061\u0064\u0020\u0062\u0075\u0066\u0066e\u0072",int64 (_cbac )};
};if _ ,_dab :=_bcac ._efa .ReadAt (_bcac ._bfe [:_cbac ],_gbeb );_dab !=nil {return nil ,Error {ErrRead ,_dab .Error (),_gbeb };};return _bcac ._bfe [:_cbac ],nil ;};const (ErrFormat =iota ;ErrRead ;ErrSeek ;ErrWrite ;ErrTraverse ;);const _fdf int =64+4*4+16+4+8*2+4+8;
type Reader struct{_gccf bool ;_fcg uint32 ;_bfe []byte ;_edd *header ;File []*File ;_caaa []*File ;_gcgd int ;_efa _fa .ReaderAt ;_eba _fa .WriterAt ;};func (_fab fileInfo )IsDir ()bool {return _fab .mode ().IsDir ()};func (_agb *File )Seek (offset int64 ,whence int )(int64 ,error ){var _acd int64 ;
switch whence {default:return 0,Error {ErrSeek ,"\u0069\u006e\u0076\u0061\u006c\u0069\u0064\u0020\u0077h\u0065\u006e\u0063\u0065",int64 (whence )};case 0:_acd =offset ;case 1:_acd =_agb ._gda +offset ;case 2:_acd =_agb .Size -offset ;};switch {case _acd < 0:return _agb ._gda ,Error {ErrSeek ,"\u0063\u0061\u006e'\u0074\u0020\u0073\u0065e\u006b\u0020\u0062\u0065\u0066\u006f\u0072e\u0020\u0073\u0074\u0061\u0072\u0074\u0020\u006f\u0066\u0020\u0046\u0069\u006c\u0065",_acd };
case _acd >=_agb .Size :return _agb ._gda ,Error {ErrSeek ,"c\u0061\u006e\u0027\u0074\u0020\u0073e\u0065\u006b\u0020\u0070\u0061\u0073\u0074\u0020\u0046i\u006c\u0065\u0020l\u0065n\u0067\u0074\u0068",_acd };case _acd ==_agb ._gda :return _acd ,nil ;case _acd > _agb ._gda :_ebd :=_agb ._gda ;
_agb ._gda =_acd ;return _agb ._gda ,_agb .seek (_acd -_ebd );};if _agb ._fcc >=_agb ._gda -_acd {_agb ._fcc =_agb ._fcc -(_agb ._gda -_acd );_agb ._gda =_acd ;return _agb ._gda ,nil ;};_agb ._fcc =0;_agb ._ef =_agb ._ccd ;_agb ._gda =_acd ;return _agb ._gda ,_agb .seek (_acd );
};type slicer interface{Slice (_beb int64 ,_aff int )([]byte ,error );};func (_fgc *File )ensureWriterAt ()error {if _fgc ._ddf ._eba ==nil {_cac ,_eda :=_fgc ._ddf ._efa .(_fa .WriterAt );if !_eda {return Error {ErrWrite ,"\u006d\u0073\u0063\u0066\u0062\u002e\u004ee\u0077\u0020\u006d\u0075\u0073\u0074\u0020\u0062\u0065\u0020\u0067\u0069\u0076\u0065n\u0020R\u0065\u0061\u0064\u0065\u0072\u0041t\u0020\u0063\u006f\u006e\u0076\u0065\u0072t\u0069\u0062\u006c\u0065\u0020\u0074\u006f\u0020\u0061\u0020\u0069\u006f\u002e\u0057\u0072\u0069\u0074\u0065\u0072\u0041\u0074\u0020\u0069n\u0020\u006f\u0072\u0064\u0065\u0072\u0020\u0074\u006f\u0020\u0077\u0072\u0069t\u0065",0};
};_fgc ._ddf ._eba =_cac ;};return nil ;};func (_dga *File )ID ()string {return _dga ._fc .String ()};func (_eeg *File )SetEntryContent (b []byte )error {if _bde :=_eeg .ensureWriterAt ();_bde !=nil {return _bde ;};_eeg .reset ();if _edaf :=_eeg .changeSize (int64 (len (b )));
_edaf !=nil {return _edaf ;};_ ,_dbeb :=_eeg .Write (b );return _dbeb ;};func (_bbf *File )ReadAt (p []byte ,off int64 )(_de int ,_gbc error ){_ffc ,_fcf ,_cca :=_bbf ._gda ,_bbf ._fcc ,_bbf ._ef ;_ ,_gbc =_bbf .Seek (off ,0);if _gbc ==nil {_de ,_gbc =_bbf .Read (p );
};_bbf ._gda ,_bbf ._fcc ,_bbf ._ef =_ffc ,_fcf ,_cca ;return _de ,_gbc ;};const (_ad uint8 =0x0;_dg uint8 =0x1;);func (_ccg *Reader )findFatLocsOffset (_eea bool )int64 {var _cgf uint32 ;if _eea {_cgf =_ccg ._edd ._gaa [0];}else {_cgf =_ccg ._edd ._ace [0];
};return _aga (_ccg ._fcg ,_cgf );};func (_geb Error )Error ()string {return "\u006ds\u0063\u0066\u0062\u003a\u0020"+_geb ._ebge +"\u003b\u0020"+_e .FormatInt (_geb ._bba ,10);};func (_fea *File )Created ()_bc .Time {return _fea ._db .Time ()};func (_dgff *Reader )setDifats ()error {_dgff ._edd ._ace =_dgff ._edd ._dfc [:];
if _dgff ._edd ._effb ==0{return nil ;};_bdb :=(_dgff ._fcg /4)-1;_bbed :=make ([]uint32 ,109,_dgff ._edd ._effb *_bdb +109);copy (_bbed ,_dgff ._edd ._ace );_dgff ._edd ._ace =_bbed ;_fbc :=_dgff ._edd ._bag ;for _bcc :=0;_bcc < int (_dgff ._edd ._effb );
_bcc ++{_gde ,_aaf :=_dgff .readAt (_aga (_dgff ._fcg ,_fbc ),int (_dgff ._fcg ));if _aaf !=nil {return Error {ErrFormat ,"e\u0072r\u006f\u0072\u0020\u0073\u0065\u0074\u0074\u0069n\u0067\u0020\u0044\u0049FA\u0054\u0028"+_aaf .Error ()+"\u0029",int64 (_fbc )};
};for _ffe :=0;_ffe < int (_bdb );_ffe ++{_dgff ._edd ._ace =append (_dgff ._edd ._ace ,_g .LittleEndian .Uint32 (_gde [_ffe *4:_ffe *4+4]));};_fbc =_g .LittleEndian .Uint32 (_gde [len (_gde )-4:]);};return nil ;};func (_bcagg *Reader )Created ()_bc .Time {return _bcagg .File [0].Created ()};
func (_af *File )Write (b []byte )(int ,error ){if _af .Size < 1||_af ._gda >=_af .Size {return 0,_fa .EOF ;};if _be :=_af .ensureWriterAt ();_be !=nil {return 0,_be ;};_bed :=len (b );if int64 (_bed )> _af .Size -_af ._gda {_bed =int (_af .Size -_af ._gda );
};_cee ,_ff :=_af .stream (_bed );if _ff !=nil {return 0,_ff ;};var _age ,_gdg int ;for _ ,_bgf :=range _cee {_fad :=_age +int (_bgf [1]);if _fad < _age ||_fad > _bed {return 0,Error {ErrWrite ,"\u0062\u0061d\u0020\u0077\u0072i\u0074\u0065\u0020\u006c\u0065\u006e\u0067\u0074\u0068",int64 (_fad )};
};_gb ,_gbe :=_af ._ddf ._eba .WriteAt (b [_age :_fad ],_bgf [0]);_gdg =_gdg +_gb ;if _gbe !=nil {_af ._gda +=int64 (_gdg );return _gdg ,Error {ErrWrite ,"\u0075n\u0064\u0065\u0072\u006c\u0079\u0069\u006e\u0067\u0020\u0077\u0072i\u0074\u0065\u0072\u0020\u0066\u0061\u0069\u006c\u0020\u0028"+_gbe .Error ()+"\u0029",int64 (_age )};
};_age =_fad ;};_af ._gda +=int64 (_gdg );if _gdg !=_bed {_ff =Error {ErrWrite ,"\u0062\u0079t\u0065\u0073\u0020\u0077\u0072\u0069\u0074\u0074\u0065\u006e\u0020\u0064\u006f\u0020\u006e\u006f\u0074\u0020\u006d\u0061\u0074\u0063\u0068\u0020\u0065\u0078\u0070\u0065\u0063\u0074\u0065\u0064\u0020\u0077\u0072\u0069\u0074\u0065\u0020\u0073\u0069\u007a\u0065",int64 (_gdg )};
}else if _gdg < len (b ){_ff =_fa .EOF ;};return _gdg ,_ff ;};func (_bdg *Reader )exportDirEntries (_cbe *_ga .Writer )error {if int64 (_cbe .Len ())!=_aga (_bdg ._fcg ,_bdg ._edd ._cgee ){return Error {ErrWrite ,_fe .Sprintf ("I\u006e\u0063\u006f\u0072\u0072\u0065c\u0074\u0020\u0077\u0072\u0069\u0074\u0065\u0072\u0020l\u0065\u006e\u0067t\u0068:\u0020\u0025\u0076",_cbe .Len ()),0};
};for _ ,_ebcc :=range _bdg ._caaa {_ega ,_afc :=_acc (_ebcc .directoryEntryFields );if _afc !=nil {return _afc ;};if _ ,_defc :=_fa .Copy (_cbe ,_ega );_defc !=nil {return _defc ;};};return nil ;};func _ggdc (_aab []byte )*headerFields {_bef :=&headerFields {};
_bef ._fgcb =_g .LittleEndian .Uint64 (_aab [:8]);_bef ._dcf =_g .LittleEndian .Uint16 (_aab [24:26]);_bef ._bcf =_g .LittleEndian .Uint16 (_aab [26:28]);_bef ._cbb =_g .LittleEndian .Uint16 (_aab [30:32]);_bef ._dae =_g .LittleEndian .Uint32 (_aab [40:44]);
_bef ._bcb =_g .LittleEndian .Uint32 (_aab [44:48]);_bef ._cgee =_g .LittleEndian .Uint32 (_aab [48:52]);_bef ._eefg =_g .LittleEndian .Uint32 (_aab [60:64]);_bef ._ceeg =_g .LittleEndian .Uint32 (_aab [64:68]);_bef ._bag =_g .LittleEndian .Uint32 (_aab [68:72]);
_bef ._effb =_g .LittleEndian .Uint32 (_aab [72:76]);var _bbe int ;for _bcag :=76;_bcag < 512;_bcag =_bcag +4{_bef ._dfc [_bbe ]=_g .LittleEndian .Uint32 (_aab [_bcag :_bcag +4]);_bbe ++;};return _bef ;};func (_gaac *Reader )exportMiniStream ()(*_ga .Writer ,*_ga .Writer ,error ){_fbaeb ,_bdf :=_ga .NewWriter (),_ga .NewWriter ();
_daaa :=uint32 (0);for _ ,_cdcf :=range _gaac .File {if _cdcf .Size ==0{continue ;};_cdcf .reset ();_cdcf ._ccd =_daaa ;_ccbc :=int (_cdcf .Size )/int (_ccaa );if int (_cdcf .Size )%int (_ccaa )!=0{_ccbc ++;};for _cfec :=1;_cfec < _ccbc ;_cfec ++{_daaa ++;
if _abfd :=_g .Write (_fbaeb ,_g .LittleEndian ,_daaa );_abfd !=nil {return nil ,nil ,_abfd ;};};if _cafg :=_g .Write (_fbaeb ,_g .LittleEndian ,_ebf );_cafg !=nil {return nil ,nil ,_cafg ;};_daaa ++;if _ ,_dcad :=_fa .Copy (_bdf ,_cdcf );_dcad !=nil {return nil ,nil ,_dcad ;
};if _dbeg :=_bdf .AlignLength (64);_dbeg !=nil {return nil ,nil ,_dbeg ;};};if _eecb :=_fbaeb .FillWithByte (int (_gaac ._fcg )-_fbaeb .Len (),_ddac );_eecb !=nil {return nil ,nil ,_eecb ;};if _geg :=_bdf .AlignLength (int (_gaac ._fcg ));_geg !=nil {return nil ,nil ,_geg ;
};return _fbaeb ,_bdf ,nil ;};func (_bcaf *File )stream (_bgff int )([][2]int64 ,error ){var _bcad bool ;var _egeg int ;var _ggb int64 ;if _bcaf .Size < _eef {_bcad =true ;_egeg =_bgff /int (_ccaa )+2;_ggb =int64 (_ccaa );}else {_egeg =_bgff /int (_bcaf ._ddf ._fcg )+2;
_ggb =int64 (_bcaf ._ddf ._fcg );};_agfb :=make ([][2]int64 ,0,_egeg );var _dac ,_ccf int ;if _bcaf ._fcc > 0{_aede ,_egcf :=_bcaf ._ddf .getOffset (_bcaf ._ef ,_bcad );if _egcf !=nil {return nil ,_egcf ;};if _ggb -_bcaf ._fcc >=int64 (_bgff ){_agfb =append (_agfb ,[2]int64 {_aede +_bcaf ._fcc ,int64 (_bgff )});
}else {_agfb =append (_agfb ,[2]int64 {_aede +_bcaf ._fcc ,_ggb -_bcaf ._fcc });};if _ggb -_bcaf ._fcc <=int64 (_bgff ){_bcaf ._ef ,_egcf =_bcaf ._ddf .findNext (_bcaf ._ef ,_bcad );if _egcf !=nil {return nil ,_egcf ;};_ccf +=int (_ggb -_bcaf ._fcc );_bcaf ._fcc =0;
}else {_bcaf ._fcc +=int64 (_bgff );};if _agfb [0][1]==int64 (_bgff ){return _agfb ,nil ;};if _bcaf ._ef ==_ebf {return nil ,Error {ErrRead ,"\u0075\u006ee\u0078\u0070\u0065\u0063\u0074\u0065\u0064\u0020\u0065\u0061\u0072\u006c\u0079\u0020\u0065\u006e\u0064\u0020\u006f\u0066\u0020\u0063ha\u0069\u006e",int64 (_bcaf ._ef )};
};_dac ++;};for {if _dac >=cap (_agfb ){return nil ,Error {ErrRead ,"\u0069\u006e\u0064\u0065x\u0020\u006f\u0076\u0065\u0072\u0072\u0075\u006e\u0073\u0020s\u0065c\u0074\u006f\u0072\u0020\u006c\u0065\u006eg\u0074\u0068",int64 (_dac )};};_agg ,_abf :=_bcaf ._ddf .getOffset (_bcaf ._ef ,_bcad );
if _abf !=nil {return nil ,_abf ;};if _bgff -_ccf < int (_ggb ){_agfb =append (_agfb ,[2]int64 {_agg ,int64 (_bgff -_ccf )});_bcaf ._fcc =int64 (_bgff -_ccf );return _bgc (_agfb ),nil ;}else {_agfb =append (_agfb ,[2]int64 {_agg ,_ggb });_ccf +=int (_ggb );
_bcaf ._ef ,_abf =_bcaf ._ddf .findNext (_bcaf ._ef ,_bcad );if _abf !=nil {return nil ,_abf ;};if _ccf ==_bgff {return _bgc (_agfb ),nil ;};};_dac ++;};};func (_fgad Error )Typ ()int {return _fgad ._bce };func (_dbc fileInfo )Sys ()interface{}{return nil };
func (_dcg *Reader )findNext (_bea uint32 ,_bbc bool )(uint32 ,error ){_cdg :=_dcg ._fcg /4;_egfb :=int (_bea /_cdg );var _bab uint32 ;if _bbc {if _egfb < 0||_egfb >=len (_dcg ._edd ._gaa ){return 0,Error {ErrRead ,"\u006d\u0069\u006e\u0069\u0073e\u0063\u0074\u006f\u0072\u0020\u0069\u006e\u0064\u0065\u0078\u0020\u0069\u0073 \u006f\u0075\u0074\u0073\u0069\u0064\u0065\u0020\u006d\u0069\u006e\u0069\u0046\u0041\u0054\u0020\u0072\u0061\u006e\u0067\u0065",int64 (_egfb )};
};_bab =_dcg ._edd ._gaa [_egfb ];}else {if _egfb < 0||_egfb >=len (_dcg ._edd ._ace ){return 0,Error {ErrRead ,"\u0046\u0041\u0054\u0020\u0069\u006e\u0064\u0065\u0078\u0020\u0069\u0073\u0020\u006f\u0075t\u0073i\u0064\u0065\u0020\u0044\u0049\u0046\u0041\u0054\u0020\u0072\u0061\u006e\u0067\u0065",int64 (_egfb )};
};_bab =_dcg ._edd ._ace [_egfb ];};_ecga :=_bea %_cdg ;_fge :=_aga (_dcg ._fcg ,_bab )+int64 (_ecga *4);_faf ,_bbga :=_dcg .readAt (_fge ,4);if _bbga !=nil {return 0,Error {ErrRead ,"\u0062\u0061\u0064\u0020\u0072\u0065\u0061\u0064\u0020\u0066i\u006e\u0064\u0069\u006e\u0067\u0020\u006ee\u0078\u0074\u0020\u0073\u0065\u0063\u0074\u006f\u0072\u0020\u0028"+_bbga .Error ()+"\u0029",_fge };
};_fbe :=_g .LittleEndian .Uint32 (_faf );return _fbe ,nil ;};func (_fff *Reader )Modified ()_bc .Time {return _fff .File [0].Modified ()};func _aga (_fb ,_cdb uint32 )int64 {return int64 ((_cdb +1)*_fb )};func (_cfed *Reader )exportHeader (_efc *_ga .Writer )error {if _eee :=_g .Write (_efc ,_g .LittleEndian ,&_cfed ._edd ._fgcb );
_eee !=nil {return _eee ;};if _cde :=_efc .Skip (16);_cde !=nil {return _cde ;};if _ceea :=_g .Write (_efc ,_g .LittleEndian ,&_cfed ._edd ._dcf );_ceea !=nil {return _ceea ;};if _bee :=_g .Write (_efc ,_g .LittleEndian ,&_cfed ._edd ._bcf );_bee !=nil {return _bee ;
};if _fcfg :=_g .Write (_efc ,_g .LittleEndian ,uint16 (0xfffe));_fcfg !=nil {return _fcfg ;};if _bbcc :=_g .Write (_efc ,_g .LittleEndian ,&_cfed ._edd ._cbb );_bbcc !=nil {return _bbcc ;};if _bbae :=_g .Write (_efc ,_g .LittleEndian ,uint16 (0x0006));
_bbae !=nil {return _bbae ;};if _gbd :=_efc .Skip (6);_gbd !=nil {return _gbd ;};if _bdbb :=_g .Write (_efc ,_g .LittleEndian ,&_cfed ._edd ._dae );_bdbb !=nil {return _bdbb ;};if _afec :=_g .Write (_efc ,_g .LittleEndian ,&_cfed ._edd ._bcb );_afec !=nil {return _afec ;
};if _ffa :=_g .Write (_efc ,_g .LittleEndian ,&_cfed ._edd ._cgee );_ffa !=nil {return _ffa ;};if _eebb :=_efc .Skip (4);_eebb !=nil {return _eebb ;};if _dbb :=_g .Write (_efc ,_g .LittleEndian ,uint32 (0x00001000));_dbb !=nil {return _dbb ;};if _dea :=_g .Write (_efc ,_g .LittleEndian ,&_cfed ._edd ._eefg );
_dea !=nil {return _dea ;};if _gbee :=_g .Write (_efc ,_g .LittleEndian ,&_cfed ._edd ._ceeg );_gbee !=nil {return _gbee ;};if _fbb :=_g .Write (_efc ,_g .LittleEndian ,&_cfed ._edd ._bag );_fbb !=nil {return _fbb ;};if _caad :=_g .Write (_efc ,_g .LittleEndian ,&_cfed ._edd ._effb );
_caad !=nil {return _caad ;};for _cafb :=0;_cafb < 109;_cafb ++{if _gdd :=_g .Write (_efc ,_g .LittleEndian ,&_cfed ._edd ._dfc [_cafb ]);_gdd !=nil {return _gdd ;};};return nil ;};func (_dc *File )Read (b []byte )(int ,error ){if _dc .Size < 1||_dc ._gda >=_dc .Size {return 0,_fa .EOF ;
};_eff :=len (b );if int64 (_eff )> _dc .Size -_dc ._gda {_eff =int (_dc .Size -_dc ._gda );};_dad ,_ba :=_dc .stream (_eff );if _ba !=nil {return 0,_ba ;};var _efb ,_df int ;for _ ,_aa :=range _dad {_dbe :=_efb +int (_aa [1]);if _dbe < _efb ||_dbe > _eff {return 0,Error {ErrRead ,"\u0062a\u0064 \u0072\u0065\u0061\u0064\u0020\u006c\u0065\u006e\u0067\u0074\u0068",int64 (_dbe )};
};_fdfg ,_gcg :=_dc ._ddf ._efa .ReadAt (b [_efb :_dbe ],_aa [0]);_df =_df +_fdfg ;if _gcg !=nil {_dc ._gda +=int64 (_df );return _df ,Error {ErrRead ,"\u0075n\u0064\u0065\u0072\u006c\u0079\u0069\u006e\u0067\u0020\u0072\u0065a\u0064\u0065\u0072\u0020\u0066\u0061\u0069\u006c\u0020\u0028"+_gcg .Error ()+"\u0029",int64 (_efb )};
};_efb =_dbe ;};_dc ._gda +=int64 (_df );if _df !=_eff {_ba =Error {ErrRead ,"\u0062\u0079\u0074e\u0073\u0020\u0072\u0065\u0061\u0064\u0020\u0064\u006f\u0020\u006e\u006f\u0074\u0020\u006d\u0061\u0074\u0063\u0068\u0020\u0065\u0078\u0070\u0065\u0063\u0074\u0065\u0064\u0020r\u0065\u0061\u0064\u0020\u0073\u0069\u007a\u0065",int64 (_df )};
}else if _df < len (b ){_ba =_fa .EOF ;};return _df ,_ba ;};func (_cafc *Reader )getOffset (_gfc uint32 ,_acgc bool )(int64 ,error ){if _acgc {_edafd :=_cafc ._fcg /64;_dfa :=int (_gfc /_edafd );if _dfa >=len (_cafc ._edd ._abgb ){return 0,Error {ErrRead ,"\u006di\u006e\u0069s\u0065\u0063\u0074o\u0072\u0020\u006e\u0075\u006d\u0062\u0065r\u0020\u0069\u0073\u0020\u006f\u0075t\u0073\u0069\u0064\u0065\u0020\u006d\u0069\u006e\u0069\u0073\u0065c\u0074\u006f\u0072\u0020\u0072\u0061\u006e\u0067\u0065",int64 (_dfa )};
};_ecg :=_gfc %_edafd ;return int64 ((_cafc ._edd ._abgb [_dfa ]+1)*_cafc ._fcg +_ecg *64),nil ;};return _aga (_cafc ._fcg ,_gfc ),nil ;};func (_dbf fileInfo )ModTime ()_bc .Time {return _dbf .Modified ()};type directoryEntryFields struct{_bcg [32]uint16 ;
_ec uint16 ;_gac uint8 ;_ac uint8 ;_gc uint32 ;_gd uint32 ;_eg uint32 ;_fc _d .Guid ;_gf [4]byte ;_db _d .FileTime ;_cc _d .FileTime ;_ccd uint32 ;_ccb [8]byte ;};func (_gfa *File )mode ()_fd .FileMode {if _gfa ._gac !=_gg {return _fd .ModeDir |0777;};
return 0666;};func (_dde fileInfo )Size ()int64 {if _dde ._gac !=_gg {return 0;};return _dde .File .Size ;};func (_fag *Reader )exportDifats (_bbb *_ga .Writer )error {if _fag ._edd ._effb ==0{return nil ;};return nil ;};type header struct{*headerFields ;
_ace []uint32 ;_gaa []uint32 ;_abgb []uint32 ;};func (_gga fileInfo )Name ()string {return _gga .File .Name };func (_cdd *Reader )Export ()([]byte ,error ){_dbcg :=_ga .NewWriter ();if _cbfb :=_cdd .exportHeader (_dbcg );_cbfb !=nil {return nil ,_cbfb ;
};if _ffg :=_dbcg .FillWithByte (512,_ddac );_ffg !=nil {return nil ,_ffg ;};_ecb :=[]uint32 {};if _aafg :=_cdd .exportDifats (_dbcg );_aafg !=nil {return nil ,_aafg ;};_begb ,_eddb ,_ead :=_cdd .exportMiniStream ();if _ead !=nil {return nil ,_ead ;};_ecb =append (_ecb ,uint32 (_dbcg .Len ())/_cdd ._fcg );
if _dec :=_cdd .exportDirEntries (_dbcg );_dec !=nil {return nil ,_dec ;};_ecb =append (_ecb ,uint32 (_dbcg .Len ())/_cdd ._fcg );if _ ,_ddg :=_begb .WriteTo (_dbcg );_ddg !=nil {return nil ,_ddg ;};_ecb =append (_ecb ,uint32 (_dbcg .Len ())/_cdd ._fcg );
if _ ,_gebe :=_eddb .WriteTo (_dbcg );_gebe !=nil {return nil ,_gebe ;};_ecb =append (_ecb ,uint32 (_dbcg .Len ())/_cdd ._fcg );if _fcfc :=_cdd .exportFAT (_dbcg ,_ecb );_fcfc !=nil {return nil ,_fcfc ;};return _dbcg .Bytes (),nil ;};const (_ggd uint64 =0xE11AB1A1E011CFD0;
_ccaa uint32 =64;_eef int64 =4096;_dgf uint32 =128;);const _dgb int =8+16+10+6+12+8+16+109*4;func (_bbd *Reader )setHeader ()error {_gcgb ,_fcbe :=_bbd .readAt (0,_dgb );if _fcbe !=nil {return _fcbe ;};_bbd ._edd =&header {headerFields :_ggdc (_gcgb )};
if _bbd ._edd ._fgcb !=_ggd {return Error {ErrFormat ,"\u0062\u0061\u0064\u0020\u0073\u0069\u0067\u006e\u0061\u0074\u0075\u0072\u0065",int64 (_bbd ._edd ._fgcb )};};if _bbd ._edd ._cbb ==0x0009||_bbd ._edd ._cbb ==0x000c{_bbd ._fcg =uint32 (1<<_bbd ._edd ._cbb );
}else {return Error {ErrFormat ,"\u0069\u006c\u006c\u0065ga\u006c\u0020\u0073\u0065\u0063\u0074\u006f\u0072\u0020\u0073\u0069\u007a\u0065",int64 (_bbd ._edd ._cbb )};};if _bbd ._edd ._effb > 0{_daed :=(_bbd ._fcg /4)-1;if int (_bbd ._edd ._effb *_daed +109)< 0{return Error {ErrFormat ,"\u0044I\u0046A\u0054\u0020\u0069\u006e\u0074 \u006f\u0076e\u0072\u0066\u006c\u006f\u0077",int64 (_bbd ._edd ._effb )};
};if _bbd ._edd ._effb *_daed +109> _bbd ._edd ._bcb +_daed {return Error {ErrFormat ,"\u006e\u0075\u006d\u0020\u0044\u0049\u0046\u0041\u0054\u0073 \u0065\u0078\u0063\u0065\u0065\u0064\u0073 \u0046\u0041\u0054\u0020\u0073\u0065\u0063\u0074\u006f\u0072\u0073",int64 (_bbd ._edd ._effb )};
};};if _bbd ._edd ._ceeg > 0{if int (_bbd ._fcg /4*_bbd ._edd ._ceeg )< 0{return Error {ErrFormat ,"m\u0069\u006e\u0069\u0020FA\u0054 \u0069\u006e\u0074\u0020\u006fv\u0065\u0072\u0066\u006c\u006f\u0077",int64 (_bbd ._edd ._ceeg )};};if _bbd ._edd ._ceeg > _bbd ._edd ._bcb *(_bbd ._fcg /_ccaa ){return Error {ErrFormat ,"\u006e\u0075\u006d\u0020\u006d\u0069n\u0069\u0020\u0046\u0041\u0054\u0073\u0020\u0065\u0078\u0063\u0065\u0065\u0064s\u0020\u0046\u0041\u0054\u0020\u0073\u0065c\u0074\u006f\u0072\u0073",int64 (_bbd ._edd ._bcb )};
};};return nil ;};const (_ag uint8 =0x0;_eb uint8 =0x1;_gg uint8 =0x2;_bg uint8 =0x5;);type fileInfo struct{*File };func (_aaa *File )findLast (_baf bool )(uint32 ,error ){_gfe :=_aaa ._ccd ;for {_cfe ,_fggb :=_aaa ._ddf .findNext (_gfe ,_baf );if _fggb !=nil {return 0,Error {ErrRead ,"\u0062\u0061\u0064\u0020\u0072\u0065\u0061\u0064\u0020\u0066i\u006e\u0064\u0069\u006e\u0067\u0020\u006ee\u0078\u0074\u0020\u0073\u0065\u0063\u0074\u006f\u0072\u0020\u0028"+_fggb .Error ()+"\u0029",0};
};if _cfe ==_ebf {break ;};_gfe =_cfe ;};return _gfe ,nil ;};func New (ra _fa .ReaderAt )(*Reader ,error ){_abfg :=&Reader {_efa :ra };if _ ,_befc :=ra .(slicer );_befc {_abfg ._gccf =true ;}else {_abfg ._bfe =make ([]byte ,_dgb );};if _dcd :=_abfg .setHeader ();
_dcd !=nil {return nil ,_dcd ;};if !_abfg ._gccf &&int (_abfg ._fcg )> len (_abfg ._bfe ){_abfg ._bfe =make ([]byte ,_abfg ._fcg );};if _afdc :=_abfg .setDifats ();_afdc !=nil {return nil ,_afdc ;};if _fgba :=_abfg .setDirEntries ();_fgba !=nil {return nil ,_fgba ;
};if _eeag :=_abfg .setMiniStream ();_eeag !=nil {return nil ,_eeag ;};if _afe :=_abfg .traverse ();_afe !=nil {return nil ,_afe ;};return _abfg ,nil ;};func (_egf *Reader )GetHeader ()*header {return _egf ._edd };func (_dabb *Reader )ID ()string {return _dabb .File [0].ID ()};
func (_fee *Reader )GetEntry (entryName string )(*File ,error ){for _cccb ,_cbf :=_fee .Next ();_cbf ==nil ;_cccb ,_cbf =_fee .Next (){if _cccb .Name ==entryName {return _cccb ,nil ;};};return nil ,Error {ErrTraverse ,"\u004e\u006f\u0020\u0065\u006e\u0074\u0072\u0079\u0020\u0066o\u0075\u006e\u0064\u0020\u0066\u006f\u0072 \u0067\u0069\u0076\u0065\u006e\u0020\u006e\u0061\u006d\u0065\u002e",0};
};func (_ce *Reader )setDirEntries ()error {_ae :=20;if _ce ._edd ._dae > 0{_ae =int (_ce ._edd ._dae );};_aca :=make ([]*File ,0,_ae );_ebc :=make (map[uint32 ]bool );_agc :=int (_ce ._fcg /_dgf );_gcc :=_ce ._edd ._cgee ;for _gcc !=_ebf {_fdb ,_da :=_ce .readAt (_aga (_ce ._fcg ,_gcc ),int (_ce ._fcg ));
if _da !=nil {return Error {ErrRead ,"\u0064\u0069\u0072\u0065\u0063\u0074\u006f\u0072\u0079\u0020e\u006e\u0074\u0072\u0069\u0065\u0073\u0020r\u0065\u0061\u0064\u0020\u0065\u0072\u0072\u006f\u0072\u0020\u0028"+_da .Error ()+"\u0029",_aga (_ce ._fcg ,_gcc )};
};for _adff :=0;_adff < _agc ;_adff ++{_aee :=&File {_ddf :_ce };_aee .directoryEntryFields =_ge (_fdb [_adff *int (_dgf ):]);_gfg (_ce ._edd ._bcf ,_aee );_aee ._ef =_aee ._ccd ;_aca =append (_aca ,_aee );};_gae ,_da :=_ce .findNext (_gcc ,false );if _da !=nil {return Error {ErrRead ,"d\u0069\u0072\u0065\u0063\u0074\u006f\u0072\u0079\u0020\u0065\u006e\u0074\u0072\u0069\u0065\u0073\u0020\u0065r\u0072\u006f\u0072\u0020\u0066\u0069\u006e\u0064\u0069\u006eg \u0073\u0065\u0063t\u006fr\u0020\u0028"+_da .Error ()+"\u0029",int64 (_gae )};
};if _gae <=_gcc {if _gae ==_gcc ||_ebc [_gae ]{return Error {ErrRead ,"\u0064\u0069\u0072\u0065\u0063\u0074\u006f\u0072\u0079\u0020e\u006e\u0074\u0072\u0069\u0065\u0073\u0020s\u0065\u0063\u0074\u006f\u0072\u0020\u0063\u0079\u0063\u006c\u0065",int64 (_gae )};
};_ebc [_gae ]=true ;};_gcc =_gae ;};_ce ._caaa =_aca ;return nil ;};func (_cab *File )seek (_aba int64 )error {var _def bool ;var _acb int64 ;if _cab .Size < _eef {_def =true ;_acb =64;}else {_acb =int64 (_cab ._ddf ._fcg );};var _dda int64 ;var _fcca error ;
if _cab ._fcc > 0{if _acb -_cab ._fcc <=_aba {_cab ._ef ,_fcca =_cab ._ddf .findNext (_cab ._ef ,_def );if _fcca !=nil {return _fcca ;};_dda +=_acb -_cab ._fcc ;_cab ._fcc =0;if _dda ==_aba {return nil ;};}else {_cab ._fcc +=_aba ;return nil ;};if _cab ._ef ==_ebf {return Error {ErrRead ,"\u0075\u006ee\u0078\u0070\u0065\u0063\u0074\u0065\u0064\u0020\u0065\u0061\u0072\u006c\u0079\u0020\u0065\u006e\u0064\u0020\u006f\u0066\u0020\u0063ha\u0069\u006e",int64 (_cab ._ef )};
};};for {if _aba -_dda < _acb {_cab ._fcc =_aba -_dda ;return nil ;}else {_dda +=_acb ;_cab ._ef ,_fcca =_cab ._ddf .findNext (_cab ._ef ,_def );if _fcca !=nil {return _fcca ;};if _dda ==_aba {return nil ;};};};};func (_dfea *Reader )exportFAT (_eafg *_ga .Writer ,_gded []uint32 )error {if _dfea ._edd ._bcb ==0{return nil ;
};_babe :=_c .NewBuffer ([]byte {});if _gec :=_g .Write (_babe ,_g .LittleEndian ,_daa );_gec !=nil {return _gec ;};for _bdc :=0;_bdc < len (_gded )-1;_bdc ++{for _ebce :=_gded [_bdc ];_ebce < _gded [_bdc +1]-1;_ebce ++{if _ebff :=_g .Write (_babe ,_g .LittleEndian ,_ebce );
_ebff !=nil {return _ebff ;};};if _dbg :=_g .Write (_babe ,_g .LittleEndian ,_ebf );_dbg !=nil {return _dbg ;};};_deea :=512;for _ ,_gbf :=range _babe .Bytes (){if _bdbg :=_eafg .WriteByteAt (_gbf ,_deea );_bdbg !=nil {return _bdbg ;};_deea ++;};return nil ;
};func (_dcff *Reader )Next ()(*File ,error ){_dcff ._gcgd ++;if _dcff ._gcgd >=len (_dcff .File ){return nil ,_fa .EOF ;};return _dcff .File [_dcff ._gcgd ],nil ;};type Error struct{_bce int ;_ebge string ;_bba int64 ;};func _acc (_aedee *directoryEntryFields )(*_c .Buffer ,error ){_caa :=_c .NewBuffer ([]byte {});
for _ ,_ggff :=range _aedee ._bcg {if _fgb :=_g .Write (_caa ,_g .LittleEndian ,&_ggff );_fgb !=nil {return nil ,_fgb ;};};if _egcc :=_g .Write (_caa ,_g .LittleEndian ,&_aedee ._ec );_egcc !=nil {return nil ,_egcc ;};if _bcd :=_g .Write (_caa ,_g .LittleEndian ,&_aedee ._gac );
_bcd !=nil {return nil ,_bcd ;};if _cba :=_g .Write (_caa ,_g .LittleEndian ,&_aedee ._ac );_cba !=nil {return nil ,_cba ;};if _fgfc :=_g .Write (_caa ,_g .LittleEndian ,&_aedee ._gc );_fgfc !=nil {return nil ,_fgfc ;};if _abd :=_g .Write (_caa ,_g .LittleEndian ,&_aedee ._gd );
_abd !=nil {return nil ,_abd ;};if _gfgd :=_g .Write (_caa ,_g .LittleEndian ,&_aedee ._eg );_gfgd !=nil {return nil ,_gfgd ;};if _fga :=_g .Write (_caa ,_g .LittleEndian ,&_aedee ._fc .DataA );_fga !=nil {return nil ,_fga ;};if _ced :=_g .Write (_caa ,_g .LittleEndian ,&_aedee ._fc .DataB );
_ced !=nil {return nil ,_ced ;};if _efe :=_g .Write (_caa ,_g .LittleEndian ,&_aedee ._fc .DataC );_efe !=nil {return nil ,_efe ;};if _ ,_ebb :=_caa .Write (_aedee ._fc .DataD [:]);_ebb !=nil {return nil ,_ebb ;};if _ ,_eeba :=_caa .Write (_aedee ._gf [:]);
_eeba !=nil {return nil ,_eeba ;};if _acgg :=_g .Write (_caa ,_g .LittleEndian ,&_aedee ._db .Low );_acgg !=nil {return nil ,_acgg ;};if _cacd :=_g .Write (_caa ,_g .LittleEndian ,&_aedee ._db .High );_cacd !=nil {return nil ,_cacd ;};if _bede :=_g .Write (_caa ,_g .LittleEndian ,&_aedee ._cc .Low );
_bede !=nil {return nil ,_bede ;};if _cdc :=_g .Write (_caa ,_g .LittleEndian ,&_aedee ._cc .High );_cdc !=nil {return nil ,_cdc ;};if _dbd :=_g .Write (_caa ,_g .LittleEndian ,&_aedee ._ccd );_dbd !=nil {return nil ,_dbd ;};if _ ,_gcgf :=_caa .Write (_aedee ._ccb [:]);
_gcgf !=nil {return nil ,_gcgf ;};return _caa ,nil ;};const (_fbf uint32 =0xFFFFFFFA;_gfad uint32 =0xFFFFFFFC;_daa uint32 =0xFFFFFFFD;_ebf uint32 =0xFFFFFFFE;_gba uint32 =0xFFFFFFFF;_ddac byte =0xFF;_bfae uint32 =0xFFFFFFFA;_fcb uint32 =0xFFFFFFFF;);func (_cge *File )Modified ()_bc .Time {return _cge ._cc .Time ()};
func (_acde *Reader )Read (b []byte )(_gcdb int ,_ebbd error ){if _acde ._gcgd >=len (_acde .File ){return 0,_fa .EOF ;};return _acde .File [_acde ._gcgd ].Read (b );};func (_bdgc *Reader )Debug ()map[string ][]uint32 {_ddfc :=map[string ][]uint32 {"s\u0065\u0063\u0074\u006f\u0072\u0020\u0073\u0069\u007a\u0065":[]uint32 {_bdgc ._fcg },"\u006d\u0069\u006e\u0069\u0020\u0066\u0061\u0074\u0020\u006c\u006f\u0063\u0073":_bdgc ._edd ._gaa ,"\u006d\u0069n\u0069\u0020\u0073t\u0072\u0065\u0061\u006d\u0020\u006c\u006f\u0063\u0073":_bdgc ._edd ._abgb ,"\u0064\u0069r\u0065\u0063\u0074o\u0072\u0079\u0020\u0073\u0065\u0063\u0074\u006f\u0072":[]uint32 {_bdgc ._edd ._cgee },"\u006d\u0069\u006e\u0069 s\u0074\u0072\u0065\u0061\u006d\u0020\u0073\u0074\u0061\u0072\u0074\u002f\u0073\u0069z\u0065":[]uint32 {_bdgc .File [0]._ccd ,_g .LittleEndian .Uint32 (_bdgc .File [0]._ccb [:])}};
for _ded ,_fbae :=_bdgc .Next ();_fbae ==nil ;_ded ,_fbae =_bdgc .Next (){_ddfc [_ded .Name +" \u0073\u0074\u0061\u0072\u0074\u002f\u0073\u0069\u007a\u0065"]=[]uint32 {_ded ._ccd ,_g .LittleEndian .Uint32 (_ded ._ccb [:])};};return _ddfc ;};func (_gaeg fileInfo )Mode ()_fd .FileMode {return _gaeg .File .mode ()};
func (_fgf *File )reset (){_fgf ._gda =0;_fgf ._fcc =0;_fgf ._ef =_fgf ._ccd };func _gfg (_dd uint16 ,_ebg *File ){_ea (_ebg );if _ebg ._gac !=_gg {return ;};if _dd > 3{_ebg .Size =int64 (_g .LittleEndian .Uint64 (_ebg ._ccb [:]));}else {_ebg .Size =int64 (_g .LittleEndian .Uint32 (_ebg ._ccb [:4]));
};};func (_edb *File )FileInfo ()_fd .FileInfo {return fileInfo {_edb }};func _ea (_ee *File ){if _ee ._ec < 4||_ee ._ec > 64{return ;};_eeb :=int (_ee ._ec /2-1);_ee .Initial =_ee ._bcg [0];var _adc int ;if !_a .IsPrint (rune (_ee .Initial )){_adc =1;
};_ee .Name =string (_b .Decode (_ee ._bcg [_adc :_eeb ]));};func (_gbeg *Reader )findNextFreeSector (_dbec bool )(uint32 ,error ){_add :=_gbeg .findFatLocsOffset (_dbec );_cfg :=uint32 (0);_dgae :=_gbeg ._fcg /4;for {_bega ,_gge :=_gbeg .readAt (_add ,4);
if _gge !=nil {return 0,Error {ErrRead ,"\u0062\u0061\u0064\u0020\u0072\u0065\u0061\u0064\u0020\u0066i\u006e\u0064\u0069\u006e\u0067\u0020\u006ee\u0078\u0074\u0020\u0073\u0065\u0063\u0074\u006f\u0072\u0020\u0028"+_gge .Error ()+"\u0029",_add };};_bfb :=_g .LittleEndian .Uint32 (_bega );
if _bfb ==_gba {break ;};if _cfg >=_dgae {return 0,Error {ErrRead ,"\u0065\u006e\u0064\u0020of\u0020\u006d\u0069\u006e\u0069\u0046\u0061\u0074\u0020\u0072\u0065\u0061\u0063\u0068e\u0064",_add };};_cfg ++;_add +=4;};return _cfg ,nil ;};func (_eaa *Reader )saveToFatLocs (_ceec uint32 ,_daf interface{},_abb bool )error {_cebf :=_c .NewBuffer ([]byte {});
if _edgf :=_g .Write (_cebf ,_g .LittleEndian ,_daf );_edgf !=nil {return _edgf ;};_fdfa :=_eaa .findFatLocsOffset (_abb )+int64 (_ceec *4);_ ,_acba :=_eaa ._eba .WriteAt (_cebf .Bytes (),_fdfa );return _acba ;};