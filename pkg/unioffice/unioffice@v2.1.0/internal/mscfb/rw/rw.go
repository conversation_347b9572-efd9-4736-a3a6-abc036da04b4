//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package rw ;import (_e "bytes";_ad "encoding/binary";_a "errors";_bf "fmt";_ec "io";_bg "io/ioutil";_b "reflect";);func (_cf *Reader )ReadProperty (a interface{})error {_ag :=_b .ValueOf (a );for _ag .Kind ()==_b .Ptr {_ag =_ag .Elem ();};if !_ag .IsValid (){return _bf .Errorf ("\u0076a\u006cu\u0065\u0020\u0069\u0073\u0020n\u006f\u0074 \u0076\u0061\u006c\u0069\u0064");
};if _cb :=_cf .align (int (_ag .Type ().Size ()));_cb !=nil {return _cb ;};if _df :=_ad .Read (_cf ,_ad .LittleEndian ,a );_df !=nil {return _df ;};return nil ;};func (_dd *Writer )WritePropertyNoAlign (a interface{})error {if _de :=_ad .Write (_dd ,_ad .LittleEndian ,a );
_de !=nil {return _de ;};return nil ;};func (_gb *Writer )AlignLength (alignTo int )error {_ab :=_gb .Len ()%alignTo ;if _ab > 0{_ ,_ecd :=_gb .Write (make ([]byte ,alignTo -_ab ));if _ecd !=nil {return _ecd ;};};return nil ;};const _be =int (^uint (0)>>1);
var _ee =_a .New ("r\u0077.\u0057\u0072\u0069\u0074\u0065\u0072\u003a\u0020t\u006f\u006f\u0020\u006car\u0067\u0065");func _dgg (_bd int )[]byte {defer func (){if recover ()!=nil {panic (_ee );};}();return make ([]byte ,_bd );};func (_cbf *Writer )align (_fcg int )error {return _cbf .Skip ((_fcg -(_cbf .Len ())%_fcg )%_fcg )};
type Reader struct{*_e .Reader };func PushLeftUI32 (v uint32 ,flag bool )uint32 {v >>=1;if flag {v |=1<<31;};return v ;};func (_da *Writer )Cap ()int {return cap (_da ._ceb )};func PushLeftUI64 (v uint64 ,flag bool )uint64 {v >>=1;if flag {v |=1<<63;};
return v ;};type Writer struct{_ceb []byte ;_fc int ;};func (_dcg *Writer )WriteTo (wTo _ec .Writer )(_agg int64 ,_cd error ){if _bbg :=_dcg .Len ();_bbg > 0{_ea ,_dbc :=wTo .Write (_dcg ._ceb [_dcg ._fc :]);if _ea > _bbg {return 0,_a .New ("\u0072\u0077\u002e\u0057\u0072\u0069\u0074\u0065\u0072\u002e\u0057\u0072\u0069\u0074\u0065\u0054\u006f\u003a\u0020\u0069\u006e\u0076\u0061\u006ci\u0064\u0020\u0057\u0072\u0069t\u0065\u0020c\u006f\u0075\u006e\u0074");
};_dcg ._fc +=_ea ;_agg =int64 (_ea );if _dbc !=nil {return _agg ,_dbc ;};if _ea !=_bbg {return _agg ,_ec .ErrShortWrite ;};};_dcg .reset ();return _agg ,nil ;};const _baa =64;func (_db *Writer )WriteStringProperty (s string )error {_db .align (4);_dc :=[]byte (s );
if _aab :=_ad .Write (_db ,_ad .LittleEndian ,&_dc );_aab !=nil {return _aab ;};return nil ;};func NewWriter ()*Writer {return &Writer {_ceb :[]byte {}}};func (_fe *Writer )curPos ()int {return int (_fe .Cap ())-_fe .Len ()};func (_cbd *Writer )WriteProperty (a interface{})error {if _ed :=_cbd .align (int (_b .TypeOf (a ).Size ()));
_ed !=nil {return _ed ;};return _cbd .WritePropertyNoAlign (a );};func PopRightUI32 (v uint32 )(bool ,uint32 ){return (v &uint32 (1))==1,v >>1};func (_cbb *Writer )FillWithByte (fillSize int ,b byte )error {for _ebe :=0;_ebe < fillSize ;_ebe ++{if _geg :=_cbb .WritePropertyNoAlign (b );
_geg !=nil {return _geg ;};};return nil ;};func (_fd *Reader )ReadStringProperty (n uint32 )(string ,error ){if _ce :=_fd .align (4);_ce !=nil {return "",_ce ;};_gf :=make ([]byte ,n );if _aga :=_ad .Read (_fd ,_ad .LittleEndian ,&_gf );_aga !=nil {return "",_aga ;
};return string (_gf ),nil ;};func (_c *Reader )skip (_ba int )error {_ ,_ae :=_ec .CopyN (_bg .Discard ,_c ,int64 (_ba ));if _ae !=nil {return _ae ;};return nil ;};func NewReader (b []byte )(*Reader ,error ){return &Reader {_e .NewReader (b )},nil };func (_ac *Reader )ReadPairProperty (p interface{})error {if _aa :=_ac .align (4);
_aa !=nil {return _aa ;};_aac :=_b .ValueOf (p );for _aac .Kind ()==_b .Ptr {_aac =_aac .Elem ();};if !_aac .IsValid (){return _bf .Errorf ("\u0076a\u006cu\u0065\u0020\u0069\u0073\u0020n\u006f\u0074 \u0076\u0061\u006c\u0069\u0064");};if _cba :=_ad .Read (_ac ,_ad .LittleEndian ,p );
_cba !=nil {return _cba ;};return nil ;};func (_ddc *Writer )Len ()int {return len (_ddc ._ceb )-_ddc ._fc };func (_bef *Writer )Write (p []byte )(_bb int ,_gec error ){_cg ,_dg :=_bef .tryGrowByReslice (len (p ));if !_dg {var _agb error ;_cg ,_agb =_bef .grow (len (p ));
if _agb !=nil {return 0,_agb ;};};return copy (_bef ._ceb [_cg :],p ),nil ;};func (_aaa *Writer )reset (){_aaa ._ceb =_aaa ._ceb [:0];_aaa ._fc =0};func (_bga *Writer )grow (_ggf int )(int ,error ){_ead :=_bga .Len ();if _ead ==0&&_bga ._fc !=0{_bga .reset ();
};if _dbd ,_fb :=_bga .tryGrowByReslice (_ggf );_fb {return _dbd ,nil ;};if _bga ._ceb ==nil &&_ggf <=_baa {_bga ._ceb =make ([]byte ,_ggf ,_baa );return 0,nil ;};_fed :=cap (_bga ._ceb );if _ggf <=_fed /2-_ead {copy (_bga ._ceb ,_bga ._ceb [_bga ._fc :]);
}else if _fed > _be -_fed -_ggf {return 0,_ee ;}else {_ef :=_dgg (2*_fed +_ggf );copy (_ef ,_bga ._ceb [_bga ._fc :]);_bga ._ceb =_ef ;};_bga ._fc =0;_bga ._ceb =_bga ._ceb [:_ead +_ggf ];return _ead ,nil ;};func (_fdf *Writer )Skip (n int )error {if n ==0{return nil ;
};_ ,_ga :=_fdf .Write (make ([]byte ,n ));return _ga ;};func (_dac *Writer )tryGrowByReslice (_ff int )(int ,bool ){if _gg :=len (_dac ._ceb );_ff <=cap (_dac ._ceb )-_gg {_dac ._ceb =_dac ._ceb [:_gg +_ff ];return _gg ,true ;};return 0,false ;};func (_ecf *Reader )align (_ge int )error {return _ecf .skip ((_ge -_ecf .curPos ()%_ge )%_ge )};
func PopRightUI64 (v uint64 )(bool ,uint64 ){return (v &uint64 (1))==1,v >>1};func (_gff *Writer )Bytes ()[]byte {return _gff ._ceb };func (_g *Reader )curPos ()int {return int (_g .Size ())-_g .Len ()};func (_eb *Writer )WriteByteAt (b byte ,off int )error {if off >=len (_eb ._ceb ){return _a .New ("\u004f\u0075\u0074\u0020\u006f\u0066\u0020\u0062\u006f\u0075\u006e\u0064\u0073");
};_eb ._ceb [off ]=b ;return nil ;};