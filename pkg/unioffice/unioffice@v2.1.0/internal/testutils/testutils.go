//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package testutils ;import (_ed "crypto/md5";_fc "encoding/hex";_ae "encoding/json";_b "errors";_aa "fmt";_gc "github.com/stretchr/testify/require";_eg "golang.org/x/image/font";_ab "golang.org/x/image/font/opentype";_bc "golang.org/x/image/math/fixed";
_db "image";_de "image/color";_f "image/draw";_ac "image/png";_ce "io";_e "io/ioutil";_aee "log";_d "math";_acg "os";_c "os/exec";_ea "path/filepath";_ga "strings";_fe "sync";_g "testing";_edf "time";);func (_cd *ReferenceFile )Update (currentMap *ReferenceMap )error {if _cd ._ad ==""{return nil ;
};_aad :=_cd .updateMap (currentMap );if _aad ==0{return nil ;};_bcb ,_aeb :=_acg .OpenFile (_cd ._ad ,_acg .O_CREATE |_acg .O_TRUNC |_acg .O_WRONLY ,0664);if _aeb !=nil {return _aeb ;};defer _bcb .Close ();_cd .Timestamp =_edf .Now ().UTC ();_aaa :=_ae .NewEncoder (_bcb );
_aaa .SetIndent ("","\u0009");return _aaa .Encode (_cd );};func CreatePNGDiff (src ,dst string )(bool ,string ,float64 ,float64 ,error ){_ggd ,_efd :=ReadPNG (src );if _efd !=nil {return false ,"",0,0,_efd ;};_gdg ,_efd :=ReadPNG (dst );if _efd !=nil {return false ,"",0,0,_efd ;
};_fdgb :=_ggd .Bounds ();_fee :=_gdg .Bounds ();if !_cgf (_fdgb ,_fee ){if _d .Abs (float64 (_fdgb .Max .X )-float64 (_fee .Max .X ))> 1{_aee .Printf ("S\u006f\u0075\u0072\u0063\u0065\u0020b\u006f\u0075\u006e\u0064\u0073\u003a \u0025\u0076\u003b\u0020\u0044\u0065\u0073t\u0020\u0062\u006f\u0075\u006e\u0064\u0073\u003a\u0020\u0025v\u000a",_fdgb ,_fee );
return false ,"",0,0,ErrImageSizeNotMatch ;};};_dccc :=_db .NewRGBA (_db .Rect (0,0,_fdgb .Max .X ,_fdgb .Max .Y ));var (_dfg float64 ;_fba float64 ;);for _edg :=_fdgb .Min .Y ;_edg < _fdgb .Max .Y ;_edg ++{for _ebf :=_fdgb .Min .X ;_ebf < _fdgb .Max .X ;
_ebf ++{_acf ,_fcg ,_cce ,_gf :=_ggd .At (_ebf ,_edg ).RGBA ();_aff ,_dddf ,_fab ,_ebb :=_gdg .At (_ebf ,_edg ).RGBA ();_dccc .Set (_ebf ,_edg ,_de .RGBA {uint8 (_aff ),uint8 (_dddf ),uint8 (_fab ),64});_bed :=_gf ==0x00&&_acf ==0x00&&_fcg ==0x00&&_cce ==0x00&&_aff ==0xFFFF&&_dddf ==0xFFFF&&_fab ==0xFFFF;
if !_bed &&!_dg (_ggd .At (_ebf ,_edg ),_gdg .At (_ebf ,_edg )){_dccc .Set (_ebf ,_edg ,_de .RGBA {uint8 (_acf ),uint8 (_fcg ),uint8 (_cce ),uint8 (_gf )});_deg :=float64 (_acf )+float64 (_fcg )+float64 (_cce )+float64 (_gf )-float64 (_aff )+float64 (_dddf )+float64 (_fab )+float64 (_ebb );
_gba :=_d .Sqrt (_d .Pow (_deg /float64 (_fdgb .Dx ()*_fdgb .Dy ()),2));_fba +=_gba ;_dfg ++;};};};_adb :=_dfg /float64 (_fdgb .Dx ()*_fdgb .Dy ())*100;_abd :=_ea .Dir (src );_afg :=_ga .TrimSuffix (_ea .Base (src ),_ea .Ext (src ));_aag ,_efd :=_acg .Create (_abd +"\u002f"+_afg +"\u002dd\u0069\u0066\u0066\u002e\u0070\u006eg");
if _efd !=nil {return false ,"",0,0,_efd ;};defer _aag .Close ();_gdb :=_ga .Replace (_abd ,"\u0072\u0065\u006e\u0064\u0065\u0072","\u0066\u006f\u006et\u0073",1)+"\u002f\u0043\u0061l\u0069\u0062\u0072\u0069\u002e\u0074\u0074\u0066";_bbb :=_aa .Sprintf ("\u0044\u0069f\u0066\u0065\u0072e\u006e\u0063\u0065\u003a\u0020\u0025\u0066\u0025\u0025",_adb );
_efd =_ebcf (_dccc ,_gdb ,_bbb ,15,22);if _efd !=nil {return false ,"",0,0,_efd ;};_bbb =_aa .Sprintf ("T\u006ft\u0061\u006c\u0020\u0044\u0069\u0066\u0066\u0065r\u0065\u006e\u0063\u0065: \u0025\u0066",_fba );_efd =_ebcf (_dccc ,_gdb ,_bbb ,15,44);if _efd !=nil {return false ,"",0,0,_efd ;
};if _dbg :=_ac .Encode (_aag ,_dccc );_dbg !=nil {return false ,"",0,0,_dbg ;};return true ,_aag .Name (),_adb ,_fba ,nil ;};type ReferenceMap struct{_fe .Mutex ;_fed map[string ]ReferenceEntry ;};func _afgg (_dea ,_acfa string )error {_eea ,_dbc :=_acg .Open (_dea );
if _dbc !=nil {return _dbc ;};defer _eea .Close ();_aed ,_ ,_dbc :=_db .DecodeConfig (_eea );if _dbc !=nil {panic (_dbc );};_dac :=_db .NewRGBA (_db .Rect (0,0,_aed .Width ,_aed .Height ));_gfb ,_dbc :=_acg .Create (_acfa );if _dbc !=nil {return _dbc ;
};defer _gfb .Close ();_dbc =_ac .Encode (_gfb ,_dac );if _dbc !=nil {return _dbc ;};return nil ;};func _efc (_ecg *_g .T ,_eda string )int64 {_fcf ,_ebfe :=_acg .Stat (_eda );_gc .NoError (_ecg ,_ebfe );return _fcf .Size ();};func (_fag *ReferenceMap )Read (key string )(ReferenceEntry ,bool ){_fag .Lock ();
defer _fag .Unlock ();if _fag ._fed ==nil {return ReferenceEntry {},false ;};_df ,_bd :=_fag ._fed [key ];return _df ,_bd ;};func (_ff *ReferenceMap )Len ()int {return len (_ff ._fed )};func (_ddd *ReferenceMap )MarshalJSON ()([]byte ,error ){return _ae .Marshal (_ddd ._fed )};
func HashFile (file string )(string ,error ){_bg ,_ef :=_acg .Open (file );if _ef !=nil {return "",_ef ;};defer _bg .Close ();_ca :=_ed .New ();if _ ,_ef =_ce .Copy (_ca ,_bg );_ef !=nil {return "",_ef ;};return _fc .EncodeToString (_ca .Sum (nil )),nil ;
};func (_cfg *ReferenceMap )Copy ()*ReferenceMap {_gab :=ReferenceMap {_fed :make (map[string ]ReferenceEntry ,len (_cfg ._fed ))};for _fd ,_eef :=range _cfg ._fed {_gab ._fed [_fd ]=_eef ;};return &_gab ;};func _ebcf (_ged *_db .RGBA ,_gbag string ,_fca string ,_bbd ,_bee int )error {_deb ,_eed :=_e .ReadFile (_gbag );
if _eed !=nil {return _eed ;};_gdc ,_eed :=_ab .Parse (_deb );if _eed !=nil {return _eed ;};_dae ,_eed :=_ab .NewFace (_gdc ,&_ab .FaceOptions {Size :18,DPI :72,Hinting :_eg .HintingNone });if _eed !=nil {return _eed ;};_dbe :=&_eg .Drawer {Dst :_ged ,Src :_db .NewUniform (_de .RGBA {200,100,0,255}),Face :_dae ,Dot :_bc .P (_bbd ,_bee )};
_dbe .DrawString (_fca );return nil ;};func (_ee *ReferenceFile )updateMap (_bcbe *ReferenceMap )int {var _cf int ;if _ee .Map ._fed ==nil {_ee .Map ._fed =map[string ]ReferenceEntry {};};for _af ,_gd :=range _bcbe ._fed {_fa ,_fcc :=_ee .Map ._fed [_af ];
if !_fcc {_ee .Map ._fed [_af ]=_gd ;_cf ++;continue ;};if string (_fa .Value )!=string (_gd .Value ){_ee .Map ._fed [_af ]=_gd ;_cf ++;};};for _ge :=range _ee .Map ._fed {if _ ,_bb :=_bcbe ._fed [_ge ];!_bb {delete (_ee .Map ._fed ,_ge );_cf ++;};};return _cf ;
};func ReadFile (dirPath ,testName string ,createEmpty bool )(*ReferenceFile ,error ){if dirPath ==""&&createEmpty {return &ReferenceFile {Map :&ReferenceMap {}},nil ;};if dirPath ==""{return nil ,_acg .ErrNotExist ;};_gde :=_ea .Join (dirPath ,testName +"\u005fr\u0065f\u0065\u0072\u0065\u006e\u0063\u0065\u002e\u006a\u0073\u006f\u006e");
_dbf :=&ReferenceFile {_ad :_gde };_eb ,_cfd :=_acg .Open (_gde );if _b .Is (_cfd ,_acg .ErrNotExist )&&createEmpty {_dbf .Timestamp =_edf .Now ().UTC ();_dbf .Map =&ReferenceMap {};return _dbf ,nil ;};if _cfd !=nil {return nil ,_cfd ;};defer _eb .Close ();
if _gcb :=_ae .NewDecoder (_eb ).Decode (_dbf );_gcb !=nil {if _gcb .Error ()=="i\u006c\u006c\u0065\u0067\u0061\u006c \u0062\u0061\u0073\u0065\u0036\u0034 \u0064\u0061\u0074\u0061\u0020\u0061\u0074 \u0069\u006e\u0070\u0075\u0074\u0020\u0062\u0079\u0074\u0065 \u0030"&&createEmpty {return _dbf ,nil ;
};return nil ,_gcb ;};return _dbf ,nil ;};func CompareImages (img1 ,img2 _db .Image )(bool ,error ){_be :=img1 .Bounds ();_cg :=0;for _ceb :=0;_ceb < _be .Size ().X ;_ceb ++{for _ebc :=0;_ebc < _be .Size ().Y ;_ebc ++{_dad ,_cgc ,_eee ,_ :=img1 .At (_ceb ,_ebc ).RGBA ();
_eec ,_bga ,_fdg ,_ :=img2 .At (_ceb ,_ebc ).RGBA ();if _dad !=_eec ||_cgc !=_bga ||_eee !=_fdg {_cg ++;};};};_cb :=float64 (_cg )/float64 (_be .Dx ()*_be .Dy ());if _cb > 0.0001{_aa .Printf ("\u0064\u0069\u0066f \u0066\u0072\u0061\u0063\u0074\u0069\u006f\u006e\u003a\u0020\u0025\u0076\u0020\u0028\u0025\u0064\u0029\u000a",_cb ,_cg );
return false ,nil ;};return true ,nil ;};func ReadPNG (file string )(_db .Image ,error ){_dcc ,_da :=_acg .Open (file );if _da !=nil {return nil ,_da ;};defer _dcc .Close ();return _ac .Decode (_dcc );};func (_cc *ReferenceMap )UnmarshalJSON (data []byte )error {return _ae .Unmarshal (data ,&_cc ._fed )};
func _dg (_dfga ,_bdf _de .Color )bool {_dga ,_faa ,_dba ,_cac :=_dfga .RGBA ();_fabg ,_bfb ,_bfe ,_fge :=_bdf .RGBA ();return _dga ==_fabg &&_faa ==_bfb &&_dba ==_bfe &&_cac ==_fge ;};func RunRenderTest (t *_g .T ,pdfPath ,outputDir ,baselineRenderPath string ,saveBaseline bool ,currentHashMap *ReferenceMap ,refFile *ReferenceFile ){RunRenderOfficeTest (t ,pdfPath ,outputDir ,baselineRenderPath ,saveBaseline ,currentHashMap ,refFile ,"\u002em\u0073\u0077\u006f\u0072\u0064");
};type ReferenceFile struct{Timestamp _edf .Time `json:"timestamp"`;Map *ReferenceMap `json:"map,omitempty"`;_ad string ;};func RunRenderOfficeTest (t *_g .T ,pdfPath ,outputDir ,baselineRenderPath string ,saveBaseline bool ,currentHashMap *ReferenceMap ,refFile *ReferenceFile ,postfix string ){_cdg :=_ga .TrimSuffix (_ea .Base (pdfPath ),_ea .Ext (pdfPath ));
t .Run ("\u0072\u0065\u006e\u0064\u0065\u0072",func (_ccd *_g .T ){_fdf :=_ea .Join (outputDir ,_cdg );_aec :=_fdf +"\u002d%\u0064\u002e\u0070\u006e\u0067";if _ec :=RenderPDFToPNGs (pdfPath ,0,_aec );_ec !=nil {_ccd .Skip (_ec );};_gbc :=_cdg +postfix ;
_acb :=_ea .Join (outputDir ,_gbc );_gfe :=_acb +"\u002d%\u0064\u002e\u0070\u006e\u0067";_eaa :=false ;if saveBaseline {_ade :=_ea .Dir (pdfPath );_bbbg :=_ea .Join (_ade ,_gbc +"\u002e\u0070\u0064\u0066");if _ ,_dfd :=_acg .Stat (_bbbg );_dfd ==nil {_ccd .Logf ("\u0052e\u006e\u0064\u0065\u0072\u0020\u004d\u0053\u0020\u004f\u0066\u0066i\u0063\u0065\u0020\u0050\u0044\u0046\u003a\u0020\u0025\u0076",_bbbg );
if _egf :=RenderPDFToPNGs (_bbbg ,0,_gfe );_egf !=nil {_ccd .Skip (_egf );};_eaa =true ;};};for _aac :=1;true ;_aac ++{_eba :=_aa .Sprintf ("\u0025s\u002d\u0025\u0064\u002e\u0070\u006eg",_fdf ,_aac );_acgc :=_ea .Join (baselineRenderPath ,_aa .Sprintf ("\u0025\u0073\u002d\u0025\u0064\u005f\u0065\u0078\u0070\u002e\u0070\u006e\u0067",_cdg ,_aac ));
if _ ,_dbb :=_acg .Stat (_eba );_dbb !=nil {break ;};_ccd .Logf ("\u0025\u0073",_acgc );if saveBaseline {_ccd .Logf ("\u0043\u006fp\u0079\u0069\u006eg\u0020\u0025\u0073\u0020\u002d\u003e\u0020\u0025\u0073",_eba ,_acgc );_beb :=CopyFile (_eba ,_acgc );if _beb !=nil {_ccd .Fatalf ("\u0045\u0052\u0052OR\u0020\u0063\u006f\u0070\u0079\u0069\u006e\u0067\u0020\u0074\u006f\u0020\u0025\u0073\u003a\u0020\u0025\u0076",_acgc ,_beb );
};if _eaa {_aga :=_aa .Sprintf ("\u0025s\u002d\u0025\u0064\u002e\u0070\u006eg",_acb ,_aac );_ggda :=_ea .Join (baselineRenderPath ,_aa .Sprintf ("\u0025\u0073\u002d\u0025\u0064\u005f\u0065\u0078\u0070\u002e\u0070\u006e\u0067",_gbc ,_aac ));_ccd .Logf ("\u0043\u006f\u0070\u0079\u0069\u006e\u0067\u0020\u004d\u0053\u0020\u0057\u006f\u0072\u0064 \u0072e\u0073\u0075\u006c\u0074\u0073\u0020\u0025\u0073\u0020\u002d\u003e\u0020\u0025\u0073",_aga ,_ggda );
_fef :=CopyFile (_aga ,_ggda );if _fef !=nil {_ccd .Logf ("\u0045\u0052\u0052\u004f\u0052\u0020\u0063\u006f\u0070\u0079\u0069\u006eg\u0020\u0074\u006f \u0025\u0073\u003a\u0020\u0025\u0076\u002c\u0020\u0068\u0061\u0076i\u006e\u0067\u0020d\u0069\u0066\u0066\u0065r\u0065\u006e\u0074\u0020\u0070\u0061\u0067\u0065\u0020\u0073\u0069\u007a\u0065\u0020\u0072\u0065\u0073\u0075\u006c\u0074\u0073\u002c\u0020\u0072\u0065\u0070\u006c\u0061\u0063\u0069\u006eg\u0020\u0069\u0074\u0020\u0077\u0069\u0074\u0068\u0020\u0062\u006ca\u006e\u006b\u0020\u0069\u006d\u0061\u0067\u0065\u0020\u0069\u006e\u0073\u0074\u0065\u0061\u0064",_ggda ,_fef );
if _aeba :=_afgg (_acgc ,_ggda );_aeba !=nil {_ccd .Fatalf ("\u0045\u0052\u0052\u004f\u0052\u0020c\u0072\u0065\u0061\u0074\u0069\u006e\u0067\u0020\u0065\u006d\u0070\u0074\u0079 \u0069\u006d\u0061\u0067\u0065\u0020\u0025s\u003a\u0020\u0025\u0076",_ggda ,_aeba );
};};_ccd .Logf ("\u0043\u006fm\u0062\u0069\u006e\u0069\u006eg\u0020\u0055\u006e\u0069\u004ff\u0066\u0069\u0063\u0065\u0020\u0072\u0065\u0073\u0075\u006c\u0074\u0073\u0020\u0077\u0069\u0074\u0068\u0020\u004d\u0053\u0020\u004f\u0066\u0066\u0069\u0063\u0065\u0020\u0025\u0073\u0020\u0061\u006e\u0064\u0020\u0025\u0073",_acgc ,_ggda );
_faaa ,_fef :=CombinePNGFiles (_acgc ,_ggda );if _acg .IsNotExist (_fef ){_ccd .Fatal ("\u0069m\u0061g\u0065\u0020\u0066\u0069\u006ce\u0020\u006di\u0073\u0073\u0069\u006e\u0067");}else if !_faaa {_ccd .Fatal ("\u0055n\u0061\u0062\u006c\u0065\u0020\u0074\u006f\u0020\u0063\u006f\u006db\u0069\u006e\u0065\u0020\u0069\u006d\u0061\u0067\u0065\u0073");
};_ccd .Logf ("\u0043\u0072\u0065\u0061\u0074\u0069\u006e\u0067\u0020\u0069\u006d\u0061\u0067\u0065\u0020\u0064\u0069\u0066f \u0055n\u0069\u004f\u0066\u0066\u0069\u0063\u0065\u0020\u0072\u0065\u0073\u0075l\u0074\u0073\u0020\u0077\u0069\u0074\u0068\u0020\u004d\u0053\u0020\u004f\u0066\u0066\u0069\u0063\u0065 \u0025\u0073\u0020\u0061\u006e\u0064\u0020\u0025\u0073",_acgc ,_ggda );
_faaa ,_dde ,_aca ,_ega ,_fef :=CreatePNGDiff (_acgc ,_ggda );if _fef !=nil &&_b .Is (_fef ,ErrImageSizeNotMatch ){_ccd .Fatalf ("\u0045\u0072\u0072\u006fr\u0020\u006f\u006e\u0020\u0063\u0072\u0065\u0061\u0074\u0065 \u0050N\u0047\u0020\u0044\u0069\u0066\u0066\u003a \u0025\u0076",_fef );
};if _faaa {_ccd .Logf ("\u0049\u006d\u0061\u0067\u0065\u003a\u0020\u0025\u0073\u000a",_dde );_ccd .Logf ("D\u0069\u0066\u0066\u0020Pe\u0072c\u0065\u006e\u0074\u003a\u0020%\u0032\u002e\u0066\u0025\u0025\u000a",_aca );_ccd .Logf ("\u0044i\u0066f\u0020\u0054\u006f\u0074\u0061\u006c\u003a\u0020\u0025\u0066\u000a",_ega );
_fcb :=_ea .Base (_dde );_gdbf ,_cag :=currentHashMap .Read (_fcb );if _cag &&(_gdbf .DiffPercent < _aca ||_gdbf .DiffTotal < _ega ){_ccd .Logf ("\u004e\u0065\u0077\u0020\u0072\u0065\u0073\u0075\u006c\u0074\u0073\u0020\u0068\u0061\u0076\u0069\u006e\u0067 h\u0069g\u0068\u0065\u0072\u0020\u0064\u0069\u0066\u0066\u0065\u0072\u0065\u006ec\u0065\u0020\u0070\u0065\u0072\u0063\u0065\u006e\u0074\u003a\u0020\u0025\u0066\u0020\u0061\u006e\u0064 \u0074\u006f\u0074\u0061\u006c\u0020\u0025\u0066\u000a",_aca ,_ega );
};_bcbf ,_gga :=HashFile (_dde );_gc .NoError (_ccd ,_gga );_bfc :=_efc (_ccd ,_dde );if !_cag ||_gdbf .Value !=_bcbf ||_gdbf .ResultSize !=_bfc ||_gdbf .DiffPercent !=_aca ||_gdbf .DiffTotal !=_ega {_caga :=ReferenceEntry {Timestamp :_edf .Now ().UTC ().Unix (),Value :_bcbf ,ResultSize :_bfc ,DiffPercent :_aca ,DiffTotal :_ega };
currentHashMap .Write (_fcb ,_caga );if _gga =refFile .Update (currentHashMap );_gga !=nil {_ccd .Logf ("\u0055\u0070\u0064\u0061\u0074\u0065\u0020\u0072\u0065\u0066\u0065\u0072\u0065\u006e\u0063e\u0020f\u0069\u006c\u0065\u0020\u0066\u0061\u0069\u006c\u0065\u0064\u003a\u0020\u0025\u0076",_gga );
};};};};continue ;};_ccd .Run (_aa .Sprintf ("\u0070\u0061\u0067\u0065\u0025\u0064",_aac ),func (_cdgc *_g .T ){_cdgc .Logf ("\u0043o\u006dp\u0061\u0072\u0069\u006e\u0067 \u0025\u0073 \u0076\u0073\u0020\u0025\u0073",_eba ,_acgc );_cdc ,_dgg :=ComparePNGFiles (_eba ,_acgc );
if _acg .IsNotExist (_dgg ){_cdgc .Fatal ("\u0069m\u0061g\u0065\u0020\u0066\u0069\u006ce\u0020\u006di\u0073\u0073\u0069\u006e\u0067");}else if !_cdc {_cdgc .Fatal ("\u0077\u0072\u006f\u006eg \u0070\u0061\u0067\u0065\u0020\u0072\u0065\u006e\u0064\u0065\u0072\u0065\u0064");
};});};});};type ReferenceEntry struct{Timestamp int64 `json:"timestamp"`;Value string `json:"value"`;ResultSize int64 `json:"resultSize,omitempty"`;DiffPercent float64 `json:"diffPercent,omitempty"`;DiffTotal float64 `json:"diffValue,omitempty"`;Invalid bool `json:"markedInvalid,omitempty"`;
};var (ErrRenderNotSupported =_b .New ("\u0072\u0065\u006e\u0064\u0065r\u0069\u006e\u0067\u0020\u0050\u0044\u0046\u0020\u0066\u0069\u006c\u0065\u0073 \u0069\u0073\u0020\u006e\u006f\u0074\u0020\u0073\u0075\u0070\u0070\u006f\u0072\u0074\u0065\u0064\u0020\u006f\u006e\u0020\u0074\u0068\u0069\u0073\u0020\u0073\u0079\u0073\u0074\u0065m");
ErrImageSizeNotMatch =_b .New ("\u0069\u006d\u0061ge\u0020\u0073\u0069\u007a\u0065\u0073\u0020\u0064\u006f\u006e\u0027\u0074\u0020\u006d\u0061\u0074\u0063\u0068"););func RenderPDFToPNGs (pdfPath string ,dpi int ,outpathTpl string )error {if dpi <=0{dpi =100;
};if _ ,_adbe :=_c .LookPath ("\u0067\u0073");_adbe !=nil {return ErrRenderNotSupported ;};return _c .Command ("\u0067\u0073","\u002d\u0073\u0044\u0045\u0056\u0049\u0043\u0045\u003d\u0070\u006e\u0067a\u006c\u0070\u0068\u0061","\u002d\u006f",outpathTpl ,_aa .Sprintf ("\u002d\u0072\u0025\u0064",dpi ),pdfPath ).Run ();
};func _cgf (_aeg ,_gea _db .Rectangle )bool {return _aeg .Min .X ==_gea .Min .X &&_aeg .Min .Y ==_gea .Min .Y &&_aeg .Max .X ==_gea .Max .X &&_aeg .Max .Y ==_gea .Max .Y ;};func (_fae *ReferenceMap )Write (key string ,entry ReferenceEntry ){_fae .Lock ();
defer _fae .Unlock ();if _fae ._fed ==nil {_fae ._fed =map[string ]ReferenceEntry {};};_fae ._fed [key ]=entry ;};func CombinePNGFiles (file1 ,file2 string )(bool ,error ){_abc ,_dfc :=ReadPNG (file1 );if _dfc !=nil {return false ,_dfc ;};_bgc ,_dfc :=ReadPNG (file2 );
if _dfc !=nil {return false ,_dfc ;};_ffc :=_db .Point {_abc .Bounds ().Dx (),0};_fac :=_db .Rectangle {_ffc ,_ffc .Add (_bgc .Bounds ().Size ())};_bea :=_db .Rectangle {_db .Point {0,0},_fac .Max };_dce :=_db .NewRGBA (_bea );_f .Draw (_dce ,_abc .Bounds (),_abc ,_db .Point {0,0},_f .Src );
_f .Draw (_dce ,_fac ,_bgc ,_db .Point {0,0},_f .Src );_agb :=_ea .Dir (file1 );_fg :=_ga .TrimSuffix (_ea .Base (file1 ),_ea .Ext (file1 ));_abcf ,_dfc :=_acg .Create (_agb +"\u002f"+_fg +"\u002d\u0063\u006f\u006d\u0062\u0069\u006e\u0065\u0064\u002e\u0070\u006e\u0067");
if _dfc !=nil {return false ,_dfc ;};defer _abcf .Close ();if _fb :=_ac .Encode (_abcf ,_dce );_fb !=nil {return false ,_fb ;};return true ,nil ;};func ComparePNGFiles (file1 ,file2 string )(bool ,error ){_ba ,_defe :=HashFile (file1 );if _defe !=nil {return false ,_defe ;
};_feg ,_defe :=HashFile (file2 );if _defe !=nil {return false ,_defe ;};if _ba ==_feg {return true ,nil ;};_eecd ,_defe :=ReadPNG (file1 );if _defe !=nil {return false ,_defe ;};_adg ,_defe :=ReadPNG (file2 );if _defe !=nil {return false ,_defe ;};if _eecd .Bounds ()!=_adg .Bounds (){return false ,nil ;
};return CompareImages (_eecd ,_adg );};func CopyFile (src ,dst string )error {_fad ,_dc :=_acg .Open (src );if _dc !=nil {return _dc ;};defer _fad .Close ();_eae ,_dc :=_acg .Create (dst );if _dc !=nil {return _dc ;};defer _eae .Close ();_ ,_dc =_ce .Copy (_eae ,_fad );
return _dc ;};func (_cec *ReferenceMap )Keys ()(_bf []string ){_bf =make ([]string ,len (_cec ._fed ));var _dd int ;for _gg :=range _cec ._fed {_bf [_dd ]=_gg ;_dd ++;};return _bf ;};