//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package mergesort ;func MergeSort (array []float64 )[]float64 {if len (array )<=1{_a :=make ([]float64 ,len (array ));copy (_a ,array );return _a ;};_bd :=len (array )/2;_e :=MergeSort (array [:_bd ]);_bb :=MergeSort (array [_bd :]);_ef :=make ([]float64 ,len (array ));
_c :=0;_f :=0;_efd :=0;for _f < len (_e )&&_efd < len (_bb ){if _e [_f ]<=_bb [_efd ]{_ef [_c ]=_e [_f ];_f ++;}else {_ef [_c ]=_bb [_efd ];_efd ++;};_c ++;};for _f < len (_e ){_ef [_c ]=_e [_f ];_f ++;_c ++;};for _efd < len (_bb ){_ef [_c ]=_bb [_efd ];
_efd ++;_c ++;};return _ef ;};