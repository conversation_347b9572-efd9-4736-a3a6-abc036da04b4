//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package license ;import (_gb "bytes";_g "compress/gzip";_f "crypto";_cg "crypto/aes";_gad "crypto/cipher";_cd "crypto/hmac";_eb "crypto/rand";_bc "crypto/rsa";_af "crypto/sha256";_dfg "crypto/sha512";_bag "crypto/x509";_de "encoding/base64";_bb "encoding/binary";
_gbb "encoding/hex";_acf "encoding/json";_df "encoding/pem";_ba "errors";_ac "fmt";_ce "github.com/unidoc/unioffice/v2/common";_gf "github.com/unidoc/unioffice/v2/common/logger";_ff "io";_e "io/ioutil";_gag "log";_fd "net";_ga "net/http";_c "os";_ag "path/filepath";
_a "sort";_ea "strings";_d "sync";_fe "time";);const (LicenseTierUnlicensed ="\u0075\u006e\u006c\u0069\u0063\u0065\u006e\u0073\u0065\u0064";LicenseTierCommunity ="\u0063o\u006d\u006d\u0075\u006e\u0069\u0074y";LicenseTierIndividual ="\u0069\u006e\u0064\u0069\u0076\u0069\u0064\u0075\u0061\u006c";
LicenseTierBusiness ="\u0062\u0075\u0073\u0069\u006e\u0065\u0073\u0073";);type LegacyLicense struct{Name string ;Signature string `json:",omitempty"`;Expiration _fe .Time ;LicenseType LegacyLicenseType ;};func Track (docKey string ,useKey string ,docName string )error {return _bace (docKey ,useKey ,docName ,!_cdbec ._deg );
};func _cabf ()(_fd .IP ,error ){_abf ,_bbg :=_fd .Dial ("\u0075\u0064\u0070","\u0038\u002e\u0038\u002e\u0038\u002e\u0038\u003a\u0038\u0030");if _bbg !=nil {return nil ,_bbg ;};defer _abf .Close ();_cabc :=_abf .LocalAddr ().(*_fd .UDPAddr );return _cabc .IP ,nil ;
};func SetMeteredKeyUsageLogVerboseMode (val bool ){_cdbec ._dea =val };const _agf ="\u0055N\u0049D\u004f\u0043\u005f\u004c\u0049C\u0045\u004eS\u0045\u005f\u0044\u0049\u0052";func (_aca defaultStateHolder )updateState (_fbe ,_ddg ,_eggg string ,_dba int ,_bdg bool ,_afe int ,_dae int ,_ffd _fe .Time ,_ebd map[string ]int ,_fefa ...interface{})error {_fbab ,_abcc :=_aea ();
if _abcc !=nil {return _abcc ;};_abcc =_c .MkdirAll (_fbab ,0777);if _abcc !=nil {return _abcc ;};if len (_fbe )< 20{return _ba .New ("i\u006e\u0076\u0061\u006c\u0069\u0064\u0020\u006b\u0065\u0079");};_egc :=[]byte (_fbe );_daf :=_dfg .Sum512_256 (_egc [:20]);
_ccc :=_gbb .EncodeToString (_daf [:]);_eeed :=_ag .Join (_fbab ,_ccc );var _agd reportState ;_agd .Docs =int64 (_dba );_agd .NumErrors =int64 (_dae );_agd .LimitDocs =_bdg ;_agd .RemainingDocs =int64 (_afe );_agd .LastWritten =_fe .Now ().UTC ();_agd .LastReported =_ffd ;
_agd .Instance =_ddg ;_agd .Next =_eggg ;_agd .Usage =_ebd ;_fdeb ,_abcc :=_acf .Marshal (_agd );if _abcc !=nil {return _abcc ;};const _fee ="\u0068\u00619\u004e\u004b\u0038]\u0052\u0062\u004c\u002a\u006d\u0034\u004c\u004b\u0057";_fdeb ,_abcc =_acg ([]byte (_fee ),_fdeb );
if _abcc !=nil {return _abcc ;};_abcc =_e .WriteFile (_eeed ,_fdeb ,0600);if _abcc !=nil {return _abcc ;};return nil ;};func (_gaf defaultStateHolder )loadState (_fbad string )(reportState ,error ){_cfa ,_fgc :=_aea ();if _fgc !=nil {return reportState {},_fgc ;
};_fgc =_c .MkdirAll (_cfa ,0777);if _fgc !=nil {return reportState {},_fgc ;};if len (_fbad )< 20{return reportState {},_ba .New ("i\u006e\u0076\u0061\u006c\u0069\u0064\u0020\u006b\u0065\u0079");};_eeba :=[]byte (_fbad );_cadc :=_dfg .Sum512_256 (_eeba [:20]);
_ebdf :=_gbb .EncodeToString (_cadc [:]);_bef :=_ag .Join (_cfa ,_ebdf );_bdgf ,_fgc :=_e .ReadFile (_bef );if _fgc !=nil {if _c .IsNotExist (_fgc ){return reportState {},nil ;};_gf .Log .Error ("\u0045R\u0052\u004f\u0052\u003a\u0020\u0025v",_fgc );return reportState {},_ba .New ("\u0069\u006e\u0076a\u006c\u0069\u0064\u0020\u0064\u0061\u0074\u0061");
};const _ece ="\u0068\u00619\u004e\u004b\u0038]\u0052\u0062\u004c\u002a\u006d\u0034\u004c\u004b\u0057";_bdgf ,_fgc =_aacb ([]byte (_ece ),_bdgf );if _fgc !=nil {return reportState {},_fgc ;};var _fad reportState ;_fgc =_acf .Unmarshal (_bdgf ,&_fad );if _fgc !=nil {_gf .Log .Error ("\u0045\u0052\u0052OR\u003a\u0020\u0049\u006e\u0076\u0061\u006c\u0069\u0064\u0020\u0064\u0061\u0074\u0061\u003a\u0020\u0025\u0076",_fgc );
return reportState {},_ba .New ("\u0069\u006e\u0076a\u006c\u0069\u0064\u0020\u0064\u0061\u0074\u0061");};return _fad ,nil ;};func (_gfg *meteredClient )checkinUsage (_adcf meteredUsageCheckinForm )(meteredUsageCheckinResp ,error ){_adcf .Package ="\u0075n\u0069\u006f\u0066\u0066\u0069\u0063e";
_adcf .PackageVersion =_ce .Version ;var _ggdd meteredUsageCheckinResp ;_gcg :=_gfg ._acd +"\u002f\u006d\u0065\u0074er\u0065\u0064\u002f\u0075\u0073\u0061\u0067\u0065\u005f\u0063\u0068\u0065\u0063\u006bi\u006e";_fa ,_bcfc :=_acf .Marshal (_adcf );if _bcfc !=nil {return _ggdd ,_bcfc ;
};_ge ,_bcfc :=_efb (_fa );if _bcfc !=nil {return _ggdd ,_bcfc ;};_abcb ,_bcfc :=_ga .NewRequest ("\u0050\u004f\u0053\u0054",_gcg ,_ge );if _bcfc !=nil {return _ggdd ,_bcfc ;};_abcb .Header .Add ("\u0043\u006f\u006et\u0065\u006e\u0074\u002d\u0054\u0079\u0070\u0065","\u0061\u0070p\u006c\u0069\u0063a\u0074\u0069\u006f\u006e\u002f\u006a\u0073\u006f\u006e");
_abcb .Header .Add ("\u0043\u006fn\u0074\u0065\u006et\u002d\u0045\u006e\u0063\u006f\u0064\u0069\u006e\u0067","\u0067\u007a\u0069\u0070");_abcb .Header .Add ("\u0041c\u0063e\u0070\u0074\u002d\u0045\u006e\u0063\u006f\u0064\u0069\u006e\u0067","\u0067\u007a\u0069\u0070");
_abcb .Header .Add ("\u0058-\u0041\u0050\u0049\u002d\u004b\u0045Y",_gfg ._edd );_fcc ,_bcfc :=_gfg ._eed .Do (_abcb );if _bcfc !=nil {_gf .Log .Error ("\u0049n\u0076\u0061\u006c\u0069d\u0020\u0068\u0074\u0074\u0070 \u0072e\u0073p\u006f\u006e\u0073\u0065\u003a\u0020\u0025v",_bcfc );
return _ggdd ,_bcfc ;};defer _fcc .Body .Close ();if _fcc .StatusCode !=200{_dcg ,_bdbb :=_fcbd (_fcc );if _bdbb !=nil {return _ggdd ,_bdbb ;};_bdbb =_acf .Unmarshal (_dcg ,&_ggdd );if _bdbb !=nil {return _ggdd ,_bdbb ;};return _ggdd ,_ac .Errorf ("\u0066\u0061i\u006c\u0065\u0064\u0020t\u006f\u0020c\u0068\u0065\u0063\u006b\u0069\u006e\u002c\u0020s\u0074\u0061\u0074\u0075\u0073\u0020\u0063\u006f\u0064\u0065\u0020\u0069s\u003a\u0020\u0025\u0064",_fcc .StatusCode );
};_eea :=_fcc .Header .Get ("\u0058\u002d\u0055\u0043\u002d\u0053\u0069\u0067\u006ea\u0074\u0075\u0072\u0065");_gd :=_bae (_adcf .MacAddress ,string (_fa ));if _gd !=_eea {_gf .Log .Error ("I\u006e\u0076\u0061l\u0069\u0064\u0020\u0072\u0065\u0073\u0070\u006f\u006e\u0073\u0065\u0020\u0073\u0069\u0067\u006e\u0061\u0074\u0075\u0072\u0065\u002c\u0020\u0073\u0065t\u0020\u0074\u0068e\u0020\u006c\u0069\u0063\u0065\u006e\u0073\u0065\u0020\u0073\u0065\u0072\u0076e\u0072\u0020\u0074\u006f \u0068\u0074\u0074\u0070s\u003a\u002f\u002f\u0063\u006c\u006f\u0075\u0064\u002e\u0075\u006e\u0069\u0064\u006f\u0063\u002e\u0069o\u002f\u0061\u0070\u0069");
return _ggdd ,_ba .New ("\u0066\u0061\u0069l\u0065\u0064\u0020\u0074\u006f\u0020\u0063\u0068\u0065\u0063\u006b\u0069\u006e\u002c\u0020\u0069\u006e\u0076\u0061\u006c\u0069\u0064\u0020\u0073\u0065\u0072\u0076\u0065\u0072 \u0072\u0065\u0073\u0070\u006f\u006e\u0073\u0065");
};_aab ,_bcfc :=_fcbd (_fcc );if _bcfc !=nil {return _ggdd ,_bcfc ;};_bcfc =_acf .Unmarshal (_aab ,&_ggdd );if _bcfc !=nil {return _ggdd ,_bcfc ;};return _ggdd ,nil ;};func _ggg (_fce string ,_dd string ,_ffe string )(string ,error ){_cf :=_ea .Index (_ffe ,_fce );
if _cf ==-1{return "",_ac .Errorf ("\u0068\u0065a\u0064\u0065\u0072 \u006e\u006f\u0074\u0020\u0066\u006f\u0075\u006e\u0064");};_fba :=_ea .Index (_ffe ,_dd );if _fba ==-1{return "",_ac .Errorf ("\u0066\u006fo\u0074\u0065\u0072 \u006e\u006f\u0074\u0020\u0066\u006f\u0075\u006e\u0064");
};_db :=_cf +len (_fce )+1;return _ffe [_db :_fba -1],nil ;};var _cdb =_fe .Date (2010,1,1,0,0,0,0,_fe .UTC );type reportState struct{Instance string `json:"inst"`;Next string `json:"n"`;Docs int64 `json:"d"`;NumErrors int64 `json:"e"`;LimitDocs bool `json:"ld"`;
RemainingDocs int64 `json:"rd"`;LastReported _fe .Time `json:"lr"`;LastWritten _fe .Time `json:"lw"`;Usage map[string ]int `json:"u"`;UsageLogs []interface{}`json:"ul,omitempty"`;};type defaultStateHolder struct{};func _bae (_baa ,_gfb string )string {_ggb :=[]byte (_baa );
_bcbe :=_cd .New (_af .New ,_ggb );_bcbe .Write ([]byte (_gfb ));return _de .StdEncoding .EncodeToString (_bcbe .Sum (nil ));};func MakeUnlicensedKey ()*LicenseKey {_bgg :=LicenseKey {};_bgg .CustomerName ="\u0055\u006e\u006c\u0069\u0063\u0065\u006e\u0073\u0065\u0064";
_bgg .Tier =LicenseTierUnlicensed ;_bgg .CreatedAt =_fe .Now ().UTC ();_bgg .CreatedAtInt =_bgg .CreatedAt .Unix ();return &_bgg ;};func (_ffc *LicenseKey )getExpiryDateToCompare ()_fe .Time {if _ffc .Trial {return _fe .Now ().UTC ();};return _ce .ReleasedAt ;
};func _acg (_ccbd ,_bce []byte )([]byte ,error ){_ded ,_cabb :=_cg .NewCipher (_ccbd );if _cabb !=nil {return nil ,_cabb ;};_bagd :=make ([]byte ,_cg .BlockSize +len (_bce ));_caga :=_bagd [:_cg .BlockSize ];if _ ,_bbc :=_ff .ReadFull (_eb .Reader ,_caga );
_bbc !=nil {return nil ,_bbc ;};_gcfb :=_gad .NewCFBEncrypter (_ded ,_caga );_gcfb .XORKeyStream (_bagd [_cg .BlockSize :],_bce );_aed :=make ([]byte ,_de .URLEncoding .EncodedLen (len (_bagd )));_de .URLEncoding .Encode (_aed ,_bagd );return _aed ,nil ;
};func _ecd (_bcd string )(LicenseKey ,error ){var _ffg LicenseKey ;_fcg ,_bf :=_ggg (_ca ,_cc ,_bcd );if _bf !=nil {return _ffg ,_bf ;};_eaa ,_bf :=_bec (_fgf ,_fcg );if _bf !=nil {return _ffg ,_bf ;};_bf =_acf .Unmarshal (_eaa ,&_ffg );if _bf !=nil {return _ffg ,_bf ;
};_ffg .CreatedAt =_fe .Unix (_ffg .CreatedAtInt ,0);if _ffg .ExpiresAtInt > 0{_cgf :=_fe .Unix (_ffg .ExpiresAtInt ,0);_ffg .ExpiresAt =_cgf ;};return _ffg ,nil ;};const (_ca ="\u002d\u002d\u002d--\u0042\u0045\u0047\u0049\u004e\u0020\u0055\u004e\u0049D\u004fC\u0020L\u0049C\u0045\u004e\u0053\u0045\u0020\u004b\u0045\u0059\u002d\u002d\u002d\u002d\u002d";
_cc ="\u002d\u002d\u002d\u002d\u002d\u0045\u004e\u0044\u0020\u0055\u004e\u0049\u0044\u004f\u0043 \u004cI\u0043\u0045\u004e\u0053\u0045\u0020\u004b\u0045\u0059\u002d\u002d\u002d\u002d\u002d";);func (_fec LegacyLicense )Verify (pubKey *_bc .PublicKey )error {_fcea :=_fec ;
_fcea .Signature ="";_fde :=_gb .Buffer {};_dg :=_acf .NewEncoder (&_fde );if _dgd :=_dg .Encode (_fcea );_dgd !=nil {return _dgd ;};_gge ,_cad :=_gbb .DecodeString (_fec .Signature );if _cad !=nil {return _cad ;};_fdf :=_af .Sum256 (_fde .Bytes ());_cad =_bc .VerifyPKCS1v15 (pubKey ,_f .SHA256 ,_fdf [:],_gge );
return _cad ;};func init (){_edb ,_ggd :=_gbb .DecodeString (_bgc );if _ggd !=nil {_gag .Fatalf ("e\u0072\u0072\u006f\u0072 r\u0065a\u0064\u0069\u006e\u0067\u0020k\u0065\u0079\u003a\u0020\u0025\u0073",_ggd );};_dfb ,_ggd :=_bag .ParsePKIXPublicKey (_edb );
if _ggd !=nil {_gag .Fatalf ("e\u0072\u0072\u006f\u0072 r\u0065a\u0064\u0069\u006e\u0067\u0020k\u0065\u0079\u003a\u0020\u0025\u0073",_ggd );};_gaa =_dfb .(*_bc .PublicKey );};const _fgf ="\u000a\u002d\u002d\u002d\u002d\u002d\u0042\u0045\u0047\u0049\u004e \u0050\u0055\u0042\u004c\u0049\u0043\u0020\u004b\u0045Y\u002d\u002d\u002d\u002d\u002d\u000a\u004d\u0049I\u0042\u0049\u006a\u0041NB\u0067\u006b\u0071\u0068\u006b\u0069G\u0039\u0077\u0030\u0042\u0041\u0051\u0045\u0046A\u0041\u004f\u0043\u0041\u0051\u0038\u0041\u004d\u0049\u0049\u0042\u0043\u0067\u004b\u0043\u0041\u0051\u0045A\u006dF\u0055\u0069\u0079\u0064\u0037\u0062\u0035\u0058\u006a\u0070\u006b\u0050\u0035\u0052\u0061\u0070\u0034\u0077\u000a\u0044\u0063\u0031d\u0079\u007a\u0049\u0051\u0034\u004c\u0065\u006b\u0078\u0072\u0076\u0079\u0074\u006e\u0045\u004d\u0070\u004e\u0055\u0062\u006f\u0036i\u0041\u0037\u0034\u0056\u0038\u0072\u0075\u005a\u004f\u0076\u0072\u0053\u0063\u0073\u0066\u0032\u0051\u0065\u004e9\u002f\u0071r\u0055\u0047\u0038\u0071\u0045\u0062\u0055\u0057\u0064\u006f\u0045\u0059\u0071+\u000a\u006f\u0074\u0046\u004e\u0041\u0046N\u0078\u006c\u0047\u0062\u0078\u0062\u0044\u0048\u0063\u0064\u0047\u0056\u0061\u004d\u0030\u004f\u0058\u0064\u0058g\u0044y\u004c5\u0061\u0049\u0045\u0061\u0067\u004c\u0030\u0063\u0035\u0070\u0077\u006a\u0049\u0064\u0050G\u0049\u006e\u0034\u0036\u0066\u0037\u0038\u0065\u004d\u004a\u002b\u004a\u006b\u0064\u0063\u0070\u0044\n\u0044\u004a\u0061\u0071\u0059\u0058d\u0072\u007a5\u004b\u0065\u0073\u0068\u006aS\u0069\u0049\u0061\u0061\u0037\u006d\u0065\u006e\u0042\u0049\u0041\u0058\u0053\u0034\u0055\u0046\u0078N\u0066H\u0068\u004e\u0030\u0048\u0043\u0059\u005a\u0059\u0071\u0051\u0047\u0037\u0062K+\u0073\u0035\u0072R\u0048\u006f\u006e\u0079\u0064\u004eW\u0045\u0047\u000a\u0048\u0038M\u0079\u0076\u00722\u0070\u0079\u0061\u0032K\u0072\u004d\u0075m\u0066\u006d\u0041\u0078\u0055\u0042\u0036\u0066\u0065\u006e\u0043\u002f4\u004f\u0030\u0057\u00728\u0067\u0066\u0050\u004f\u0055\u0038R\u0069\u0074\u006d\u0062\u0044\u0076\u0051\u0050\u0049\u0052\u0058\u004fL\u0034\u0076\u0054B\u0072\u0042\u0064\u0062a\u0041\u000a9\u006e\u0077\u004e\u0050\u002b\u0069\u002f\u002f\u0032\u0030\u004d\u00542\u0062\u0078\u006d\u0065\u0057\u0042\u002b\u0067\u0070\u0063\u0045\u0068G\u0070\u0058\u005a7\u0033\u0033\u0061\u007a\u0051\u0078\u0072\u0043\u0033\u004a\u0034\u0076\u0033C\u005a\u006d\u0045\u004eS\u0074\u0044\u004b\u002f\u004b\u0044\u0053\u0050\u004b\u0055\u0047\u0066\u00756\u000a\u0066\u0077I\u0044\u0041\u0051\u0041\u0042\u000a\u002d\u002d\u002d\u002d\u002dE\u004e\u0044\u0020\u0050\u0055\u0042\u004c\u0049\u0043 \u004b\u0045Y\u002d\u002d\u002d\u002d\u002d\n";
func (_bfb *meteredClient )getStatus ()(meteredStatusResp ,error ){var _cge meteredStatusResp ;_dbb :=_bfb ._acd +"\u002fm\u0065t\u0065\u0072\u0065\u0064\u002f\u0073\u0074\u0061\u0074\u0075\u0073";var _dcc meteredStatusForm ;_abc ,_fbf :=_acf .Marshal (_dcc );
if _fbf !=nil {return _cge ,_fbf ;};_cdd ,_fbf :=_efb (_abc );if _fbf !=nil {return _cge ,_fbf ;};_gggg ,_fbf :=_ga .NewRequest ("\u0050\u004f\u0053\u0054",_dbb ,_cdd );if _fbf !=nil {return _cge ,_fbf ;};_gggg .Header .Add ("\u0043\u006f\u006et\u0065\u006e\u0074\u002d\u0054\u0079\u0070\u0065","\u0061\u0070p\u006c\u0069\u0063a\u0074\u0069\u006f\u006e\u002f\u006a\u0073\u006f\u006e");
_gggg .Header .Add ("\u0043\u006fn\u0074\u0065\u006et\u002d\u0045\u006e\u0063\u006f\u0064\u0069\u006e\u0067","\u0067\u007a\u0069\u0070");_gggg .Header .Add ("\u0041c\u0063e\u0070\u0074\u002d\u0045\u006e\u0063\u006f\u0064\u0069\u006e\u0067","\u0067\u007a\u0069\u0070");
_gggg .Header .Add ("\u0058-\u0041\u0050\u0049\u002d\u004b\u0045Y",_bfb ._edd );_age ,_fbf :=_bfb ._eed .Do (_gggg );if _fbf !=nil {return _cge ,_fbf ;};defer _age .Body .Close ();if _age .StatusCode !=200{return _cge ,_ac .Errorf ("\u0066\u0061i\u006c\u0065\u0064\u0020t\u006f\u0020c\u0068\u0065\u0063\u006b\u0069\u006e\u002c\u0020s\u0074\u0061\u0074\u0075\u0073\u0020\u0063\u006f\u0064\u0065\u0020\u0069s\u003a\u0020\u0025\u0064",_age .StatusCode );
};_aece ,_fbf :=_fcbd (_age );if _fbf !=nil {return _cge ,_fbf ;};_fbf =_acf .Unmarshal (_aece ,&_cge );if _fbf !=nil {return _cge ,_fbf ;};return _cge ,nil ;};type LicenseKey struct{LicenseId string `json:"license_id"`;CustomerId string `json:"customer_id"`;
CustomerName string `json:"customer_name"`;Tier string `json:"tier"`;CreatedAt _fe .Time `json:"-"`;CreatedAtInt int64 `json:"created_at"`;ExpiresAt _fe .Time `json:"-"`;ExpiresAtInt int64 `json:"expires_at"`;CreatedBy string `json:"created_by"`;CreatorName string `json:"creator_name"`;
CreatorEmail string `json:"creator_email"`;UniPDF bool `json:"unipdf"`;UniOffice bool `json:"unioffice"`;UniHTML bool `json:"unihtml"`;Trial bool `json:"trial"`;_aec bool ;_feb string ;_deg bool ;_dea bool ;};func _efb (_cde []byte )(_ff .Reader ,error ){_cdec :=new (_gb .Buffer );
_dcd :=_g .NewWriter (_cdec );_dcd .Write (_cde );_cbf :=_dcd .Close ();if _cbf !=nil {return nil ,_cbf ;};return _cdec ,nil ;};func _da ()*meteredClient {_aa :=meteredClient {_acd :"h\u0074\u0074\u0070\u0073\u003a\u002f/\u0063\u006c\u006f\u0075\u0064\u002e\u0075\u006e\u0069d\u006f\u0063\u002ei\u006f/\u0061\u0070\u0069",_eed :&_ga .Client {Timeout :30*_fe .Second }};
if _eee :=_c .Getenv ("\u0055N\u0049\u0044\u004f\u0043_\u004c\u0049\u0043\u0045\u004eS\u0045_\u0053E\u0052\u0056\u0045\u0052\u005f\u0055\u0052L");_ea .HasPrefix (_eee ,"\u0068\u0074\u0074\u0070"){_aa ._acd =_eee ;};return &_aa ;};const _gagd ="\u0055\u004e\u0049OF\u0046\u0049\u0043\u0045\u005f\u0043\u0055\u0053\u0054\u004f\u004d\u0045\u0052\u005f\u004e\u0041\u004d\u0045";
var _dbf =&_d .Mutex {};type LegacyLicenseType byte ;var _ee =_fe .Date (2020,1,1,0,0,0,0,_fe .UTC );func (_bcf *LicenseKey )ToString ()string {if _bcf ._aec {return "M\u0065t\u0065\u0072\u0065\u0064\u0020\u0073\u0075\u0062s\u0063\u0072\u0069\u0070ti\u006f\u006e";
};_dce :=_ac .Sprintf ("\u004ci\u0063e\u006e\u0073\u0065\u0020\u0049\u0064\u003a\u0020\u0025\u0073\u000a",_bcf .LicenseId );_dce +=_ac .Sprintf ("\u0043\u0075s\u0074\u006f\u006de\u0072\u0020\u0049\u0064\u003a\u0020\u0025\u0073\u000a",_bcf .CustomerId );
_dce +=_ac .Sprintf ("\u0043u\u0073t\u006f\u006d\u0065\u0072\u0020N\u0061\u006de\u003a\u0020\u0025\u0073\u000a",_bcf .CustomerName );_dce +=_ac .Sprintf ("\u0054i\u0065\u0072\u003a\u0020\u0025\u0073\n",_bcf .Tier );_dce +=_ac .Sprintf ("\u0043r\u0065a\u0074\u0065\u0064\u0020\u0041\u0074\u003a\u0020\u0025\u0073\u000a",_ce .UtcTimeFormat (_bcf .CreatedAt ));
if _bcf .ExpiresAt .IsZero (){_dce +="\u0045x\u0070i\u0072\u0065\u0073\u0020\u0041t\u003a\u0020N\u0065\u0076\u0065\u0072\u000a";}else {_dce +=_ac .Sprintf ("\u0045x\u0070i\u0072\u0065\u0073\u0020\u0041\u0074\u003a\u0020\u0025\u0073\u000a",_ce .UtcTimeFormat (_bcf .ExpiresAt ));
};_dce +=_ac .Sprintf ("\u0043\u0072\u0065\u0061\u0074\u006f\u0072\u003a\u0020\u0025\u0073\u0020<\u0025\u0073\u003e\u000a",_bcf .CreatorName ,_bcf .CreatorEmail );return _dce ;};func _gadd (_aaga *_ga .Response )(_ff .ReadCloser ,error ){var _cbc error ;
var _aafb _ff .ReadCloser ;switch _ea .ToLower (_aaga .Header .Get ("\u0043\u006fn\u0074\u0065\u006et\u002d\u0045\u006e\u0063\u006f\u0064\u0069\u006e\u0067")){case "\u0067\u007a\u0069\u0070":_aafb ,_cbc =_g .NewReader (_aaga .Body );if _cbc !=nil {return _aafb ,_cbc ;
};defer _aafb .Close ();default:_aafb =_aaga .Body ;};return _aafb ,nil ;};func SetLicenseKey (content string ,customerName string )error {if _adg {return nil ;};_aaf ,_dfc :=_ecd (content );if _dfc !=nil {_gf .Log .Error ("\u004c\u0069c\u0065\u006e\u0073\u0065\u0020\u0063\u006f\u0064\u0065\u0020\u0064\u0065\u0063\u006f\u0064\u0065\u0020\u0065\u0072\u0072\u006f\u0072: \u0025\u0076",_dfc );
return _dfc ;};if !_ea .EqualFold (_aaf .CustomerName ,customerName ){_gf .Log .Error ("L\u0069ce\u006es\u0065 \u0063\u006f\u0064\u0065\u0020i\u0073\u0073\u0075e\u0020\u002d\u0020\u0043\u0075s\u0074\u006f\u006de\u0072\u0020\u006e\u0061\u006d\u0065\u0020\u006d\u0069\u0073\u006da\u0074\u0063\u0068, e\u0078\u0070\u0065\u0063\u0074\u0065d\u0020\u0027\u0025\u0073\u0027\u002c\u0020\u0062\u0075\u0074\u0020\u0067o\u0074 \u0027\u0025\u0073\u0027",_aaf .CustomerName ,customerName );
return _ac .Errorf ("\u0063\u0075\u0073\u0074\u006fm\u0065\u0072\u0020\u006e\u0061\u006d\u0065\u0020\u006d\u0069\u0073\u006d\u0061t\u0063\u0068\u002c\u0020\u0065\u0078\u0070\u0065\u0063\u0074\u0065\u0064\u0020\u0027\u0025\u0073\u0027\u002c\u0020\u0062\u0075\u0074\u0020\u0067\u006f\u0074\u0020\u0027\u0025\u0073'",_aaf .CustomerName ,customerName );
};_dfc =_aaf .Validate ();if _dfc !=nil {_gf .Log .Error ("\u004c\u0069\u0063\u0065\u006e\u0073e\u0020\u0063\u006f\u0064\u0065\u0020\u0076\u0061\u006c\u0069\u0064\u0061\u0074i\u006f\u006e\u0020\u0065\u0072\u0072\u006fr\u003a\u0020\u0025\u0076",_dfc );return _dfc ;
};_cdbec =&_aaf ;return nil ;};func GetLicenseKey ()*LicenseKey {if _cdbec ==nil {return nil ;};_add :=*_cdbec ;return &_add ;};func (_gbe *LicenseKey )TypeToString ()string {if _gbe ._aec {return "M\u0065t\u0065\u0072\u0065\u0064\u0020\u0073\u0075\u0062s\u0063\u0072\u0069\u0070ti\u006f\u006e";
};if _gbe .Tier ==LicenseTierUnlicensed {return "\u0055\u006e\u006c\u0069\u0063\u0065\u006e\u0073\u0065\u0064";};if _gbe .Tier ==LicenseTierCommunity {return "\u0041\u0047PL\u0076\u0033\u0020O\u0070\u0065\u006e\u0020Sou\u0072ce\u0020\u0043\u006f\u006d\u006d\u0075\u006eit\u0079\u0020\u004c\u0069\u0063\u0065\u006es\u0065";
};if _gbe .Tier ==LicenseTierIndividual ||_gbe .Tier =="\u0069\u006e\u0064i\u0065"{return "\u0043\u006f\u006dm\u0065\u0072\u0063\u0069a\u006c\u0020\u004c\u0069\u0063\u0065\u006es\u0065\u0020\u002d\u0020\u0049\u006e\u0064\u0069\u0076\u0069\u0064\u0075\u0061\u006c";
};return "\u0043\u006fm\u006d\u0065\u0072\u0063\u0069\u0061\u006c\u0020\u004c\u0069\u0063\u0065\u006e\u0073\u0065\u0020\u002d\u0020\u0042\u0075\u0073\u0069ne\u0073\u0073";};var _fbg map[string ]struct{};var _gabc stateLoader =defaultStateHolder {};const _ged ="\u0055\u004e\u0049\u004fFF\u0049\u0043\u0045\u005f\u004c\u0049\u0043\u0045\u004e\u0053\u0045\u005f\u0050\u0041T\u0048";
func (_ab *LicenseKey )Validate ()error {if _ab ._aec {return nil ;};if len (_ab .LicenseId )< 10{return _ac .Errorf ("i\u006e\u0076\u0061\u006c\u0069\u0064 \u006c\u0069\u0063\u0065\u006e\u0073\u0065\u003a\u0020L\u0069\u0063\u0065n\u0073e\u0020\u0049\u0064");
};if len (_ab .CustomerId )< 10{return _ac .Errorf ("\u0069\u006e\u0076\u0061l\u0069\u0064\u0020\u006c\u0069\u0063\u0065\u006e\u0073\u0065:\u0020C\u0075\u0073\u0074\u006f\u006d\u0065\u0072 \u0049\u0064");};if len (_ab .CustomerName )< 1{return _ac .Errorf ("\u0069\u006e\u0076\u0061\u006c\u0069\u0064\u0020\u006c\u0069c\u0065\u006e\u0073\u0065\u003a\u0020\u0043u\u0073\u0074\u006f\u006d\u0065\u0072\u0020\u004e\u0061\u006d\u0065");
};if _cdb .After (_ab .CreatedAt ){return _ac .Errorf ("\u0069\u006e\u0076\u0061\u006c\u0069\u0064\u0020\u006c\u0069\u0063\u0065\u006e\u0073\u0065\u003a\u0020\u0043\u0072\u0065\u0061\u0074\u0065\u0064 \u0041\u0074\u0020\u0069\u0073 \u0069\u006ev\u0061\u006c\u0069\u0064");
};if _ab .ExpiresAt .IsZero (){_gcb :=_ab .CreatedAt .AddDate (1,0,0);if _ee .After (_gcb ){_gcb =_ee ;};_ab .ExpiresAt =_gcb ;};if _ab .CreatedAt .After (_ab .ExpiresAt ){return _ac .Errorf ("i\u006e\u0076\u0061\u006c\u0069\u0064\u0020\u006c\u0069\u0063\u0065\u006e\u0073\u0065\u003a\u0020\u0043\u0072\u0065\u0061\u0074\u0065\u0064\u0020\u0041\u0074 \u0063a\u006e\u006e\u006f\u0074 \u0062\u0065 \u0047\u0072\u0065\u0061\u0074\u0065\u0072\u0020\u0074\u0068\u0061\u006e\u0020\u0045\u0078\u0070\u0069\u0072\u0065\u0073\u0020\u0041\u0074");
};if _ab .isExpired (){_eff :="\u0054\u0068\u0065\u0020\u006c\u0069c\u0065\u006e\u0073\u0065\u0020\u0068\u0061\u0073\u0020\u0061\u006c\u0072\u0065a\u0064\u0079\u0020\u0065\u0078\u0070\u0069r\u0065\u0064\u002e\u000a"+"\u0059o\u0075\u0020\u006d\u0061y\u0020n\u0065\u0065\u0064\u0020\u0074\u006f\u0020\u0075\u0070d\u0061\u0074\u0065\u0020\u0074\u0068\u0065\u0020l\u0069\u0063\u0065\u006e\u0073\u0065\u0020\u006b\u0065\u0079\u0020t\u006f\u0020\u0074\u0068\u0065\u0020\u006e\u0065\u0077\u0065s\u0074\u0020\u006c\u0069\u0063\u0065\u006e\u0073\u0065\u0020\u006b\u0065\u0079\u0020\u0066\u006f\u0072\u0020\u0079o\u0075\u0072\u0020\u006f\u0072\u0067\u0061\u006e\u0069\u007a\u0061\u0074i\u006fn\u002e\u000a"+"\u0054o\u0020\u0066\u0069\u006ed y\u006f\u0075\u0072\u0020n\u0065\u0077\u0065\u0073\u0074\u0020\u006c\u0069\u0063\u0065n\u0073\u0065\u0020\u006b\u0065\u0079\u002c\u0020\u0067\u006f\u0020\u0074\u006f\u0020\u0068\u0074\u0074\u0070\u0073\u003a\u002f\u002f\u0063l\u006f\u0075\u0064\u002e\u0075\u006e\u0069\u0064oc\u002e\u0069\u006f \u0061\u006e\u0064\u0020\u0067o\u0020t\u006f\u0020\u0074\u0068\u0065\u0020\u006c\u0069\u0063e\u006e\u0073\u0065\u0020\u006d\u0065\u006e\u0075\u002e";
return _ac .Errorf ("\u0069\u006e\u0076\u0061li\u0064\u0020\u006c\u0069\u0063\u0065\u006e\u0073\u0065\u003a\u0020\u0025\u0073",_eff );};if len (_ab .CreatorName )< 1{return _ac .Errorf ("\u0069\u006ev\u0061\u006c\u0069\u0064\u0020\u006c\u0069\u0063\u0065\u006e\u0073\u0065\u003a\u0020\u0043\u0072\u0065\u0061\u0074\u006f\u0072\u0020na\u006d\u0065");
};if len (_ab .CreatorEmail )< 1{return _ac .Errorf ("\u0069\u006e\u0076\u0061\u006c\u0069\u0064\u0020\u006c\u0069c\u0065\u006e\u0073\u0065\u003a\u0020\u0043r\u0065\u0061\u0074\u006f\u0072\u0020\u0065\u006d\u0061\u0069\u006c");};if _ab .CreatedAt .After (_bdb ){if !_ab .UniOffice {return _ac .Errorf ("\u0069\u006e\u0076\u0061l\u0069\u0064\u0020\u006c\u0069\u0063\u0065\u006e\u0073e\u003a\u0020\u0054\u0068\u0069\u0073\u0020\u0055\u006e\u0069\u0044\u006f\u0063\u0020\u006b\u0065\u0079\u0020i\u0073\u0020\u0069\u006e\u0076a\u006c\u0069\u0064\u0020\u0066\u006f\u0072\u0020\u0055\u006e\u0069\u004f\u0066\u0066\u0069\u0063\u0065");
};};return nil ;};var _cdbec =MakeUnlicensedKey ();type meteredClient struct{_acd string ;_edd string ;_eed *_ga .Client ;};func _def (_ceg string ,_bg []byte )(string ,error ){_be ,_ :=_df .Decode ([]byte (_ceg ));if _be ==nil {return "",_ac .Errorf ("\u0050\u0072\u0069\u0076\u004b\u0065\u0079\u0020\u0066a\u0069\u006c\u0065\u0064");
};_ef ,_ed :=_bag .ParsePKCS1PrivateKey (_be .Bytes );if _ed !=nil {return "",_ed ;};_ec :=_dfg .New ();_ec .Write (_bg );_ccb :=_ec .Sum (nil );_ae ,_ed :=_bc .SignPKCS1v15 (_eb .Reader ,_ef ,_f .SHA512 ,_ccb );if _ed !=nil {return "",_ed ;};_dc :=_de .StdEncoding .EncodeToString (_bg );
_dc +="\u000a\u002b\u000a";_dc +=_de .StdEncoding .EncodeToString (_ae );return _dc ,nil ;};func _aea ()(string ,error ){_abbd :=_ea .TrimSpace (_c .Getenv (_agf ));if _abbd ==""{_gf .Log .Debug ("\u0024\u0025\u0073\u0020e\u006e\u0076\u0069\u0072\u006f\u006e\u006d\u0065\u006e\u0074\u0020\u0076\u0061\u0072\u0069\u0061\u0062l\u0065\u0020\u006e\u006f\u0074\u0020\u0066\u006f\u0075\u006e\u0064\u002e\u0020\u0057\u0069\u006c\u006c\u0020\u0075\u0073\u0065\u0020\u0068\u006f\u006d\u0065\u0020\u0064\u0069\u0072\u0065\u0063\u0074\u006f\u0072\u0079\u0020\u0074\u006f\u0020s\u0074\u006f\u0072\u0065\u0020\u006c\u0069\u0063\u0065\u006e\u0073\u0065\u0020in\u0066o\u0072\u006d\u0061\u0074\u0069\u006f\u006e\u002e",_agf );
_caa :=_eebb ();if len (_caa )==0{return "",_ac .Errorf ("r\u0065\u0071\u0075\u0069\u0072\u0065\u0064\u0020\u0024\u0025\u0073\u0020\u0065\u006e\u0076\u0069\u0072\u006f\u006e\u006d\u0065\u006e\u0074\u0020\u0076\u0061r\u0069a\u0062\u006c\u0065\u0020o\u0072\u0020h\u006f\u006d\u0065\u0020\u0064\u0069\u0072\u0065\u0063\u0074\u006f\u0072\u0079\u0020\u006e\u006f\u0074\u0020\u0066\u006f\u0075\u006e\u0064",_agf );
};_abbd =_ag .Join (_caa ,"\u002eu\u006e\u0069\u0064\u006f\u0063");};_feca :=_c .MkdirAll (_abbd ,0777);if _feca !=nil {return "",_feca ;};return _abbd ,nil ;};var _eeeg []interface{};func _fcbd (_ebdb *_ga .Response )([]byte ,error ){var _eaf []byte ;
_fbfc ,_fcf :=_gadd (_ebdb );if _fcf !=nil {return _eaf ,_fcf ;};return _e .ReadAll (_fbfc );};func (_cda *LicenseKey )isExpired ()bool {return _cda .getExpiryDateToCompare ().After (_cda .ExpiresAt )};func (_efd *LicenseKey )IsLicensed ()bool {return true};var _gaa *_bc .PublicKey ;type meteredUsageCheckinResp struct{Instance string `json:"inst"`;Next string `json:"next"`;Success bool `json:"success"`;Message string `json:"message"`;RemainingDocs int `json:"rd"`;
LimitDocs bool `json:"ld"`;};var _adg =false ;type meteredStatusResp struct{Valid bool `json:"valid"`;OrgCredits int64 `json:"org_credits"`;OrgUsed int64 `json:"org_used"`;OrgRemaining int64 `json:"org_remaining"`;};func _aacb (_dfgb ,_cade []byte )([]byte ,error ){_cegb :=make ([]byte ,_de .URLEncoding .DecodedLen (len (_cade )));
_cgbg ,_dca :=_de .URLEncoding .Decode (_cegb ,_cade );if _dca !=nil {return nil ,_dca ;};_cegb =_cegb [:_cgbg ];_ffcg ,_dca :=_cg .NewCipher (_dfgb );if _dca !=nil {return nil ,_dca ;};if len (_cegb )< _cg .BlockSize {return nil ,_ba .New ("c\u0069p\u0068\u0065\u0072\u0074\u0065\u0078\u0074\u0020t\u006f\u006f\u0020\u0073ho\u0072\u0074");
};_bca :=_cegb [:_cg .BlockSize ];_cegb =_cegb [_cg .BlockSize :];_ace :=_gad .NewCFBDecrypter (_ffcg ,_bca );_ace .XORKeyStream (_cegb ,_cegb );return _cegb ,nil ;};func _eebb ()string {_cbef :=_c .Getenv ("\u0048\u004f\u004d\u0045");if len (_cbef )==0{_cbef ,_ =_c .UserHomeDir ();
};return _cbef ;};func GenRefId (prefix string )(string ,error ){var _abe _gb .Buffer ;_abe .WriteString (prefix );_gbc :=make ([]byte ,8+16);_eegg :=_fe .Now ().UTC ().UnixNano ();_bb .BigEndian .PutUint64 (_gbc ,uint64 (_eegg ));_ ,_eae :=_eb .Read (_gbc [8:]);
if _eae !=nil {return "",_eae ;};_abe .WriteString (_gbb .EncodeToString (_gbc ));return _abe .String (),nil ;};type meteredUsageCheckinForm struct{Instance string `json:"inst"`;Next string `json:"next"`;UsageNumber int `json:"usage_number"`;NumFailed int64 `json:"num_failed"`;
Hostname string `json:"hostname"`;LocalIP string `json:"local_ip"`;MacAddress string `json:"mac_address"`;Package string `json:"package"`;PackageVersion string `json:"package_version"`;Usage map[string ]int `json:"u"`;IsPersistentCache bool `json:"is_persistent_cache"`;
Timestamp int64 `json:"timestamp"`;UsageLogs []interface{}`json:"ul,omitempty"`;};type stateLoader interface{loadState (_gfd string )(reportState ,error );updateState (_aeb ,_egg ,_eeb string ,_fge int ,_gab bool ,_dfgf int ,_accd int ,_cb _fe .Time ,_ccbgb map[string ]int ,_dga ...interface{})error ;
};var _gff map[string ]int ;type meteredStatusForm struct{};func init (){_ebf :=_c .Getenv (_ged );_fcb :=_c .Getenv (_gagd );if len (_ebf )==0||len (_fcb )==0{return ;};_dcb ,_gga :=_e .ReadFile (_ebf );if _gga !=nil {_gf .Log .Error ("\u0055\u006eab\u006c\u0065\u0020t\u006f\u0020\u0072\u0065ad \u006cic\u0065\u006e\u0073\u0065\u0020\u0063\u006fde\u0020\u0066\u0069\u006c\u0065\u003a\u0020%\u0076",_gga );
return ;};_gga =SetLicenseKey (string (_dcb ),_fcb );if _gga !=nil {_gf .Log .Error ("\u0055\u006e\u0061b\u006c\u0065\u0020\u0074o\u0020\u006c\u006f\u0061\u0064\u0020\u006ci\u0063\u0065\u006e\u0073\u0065\u0020\u0063\u006f\u0064\u0065\u003a\u0020\u0025\u0076",_gga );
return ;};};func GetMeteredState ()(MeteredStatus ,error ){if _cdbec ==nil {return MeteredStatus {},_ba .New ("\u006c\u0069\u0063\u0065ns\u0065\u0020\u006b\u0065\u0079\u0020\u006e\u006f\u0074\u0020\u0073\u0065\u0074");};if !_cdbec ._aec ||len (_cdbec ._feb )==0{return MeteredStatus {},_ba .New ("\u0061p\u0069 \u006b\u0065\u0079\u0020\u006e\u006f\u0074\u0020\u0073\u0065\u0074");
};_egb ,_fg :=_gabc .loadState (_cdbec ._feb );if _fg !=nil {_gf .Log .Error ("\u0045R\u0052\u004f\u0052\u003a\u0020\u0025v",_fg );return MeteredStatus {},_fg ;};if _egb .Docs > 0{_deb :=_bace ("","","",true );if _deb !=nil {return MeteredStatus {},_deb ;
};};_dbf .Lock ();defer _dbf .Unlock ();_ead :=_da ();_ead ._edd =_cdbec ._feb ;_eeg ,_fg :=_ead .getStatus ();if _fg !=nil {return MeteredStatus {},_fg ;};if !_eeg .Valid {return MeteredStatus {},_ba .New ("\u006b\u0065\u0079\u0020\u006e\u006f\u0074\u0020\u0076\u0061\u006c\u0069\u0064");
};_cag :=MeteredStatus {OK :true ,Credits :_eeg .OrgCredits ,Used :_eeg .OrgUsed };return _cag ,nil ;};const _bgc ="\u0033\u0030\u0035\u0063\u0033\u0030\u0030\u00640\u0036\u0030\u0039\u0032\u0061\u0038\u00364\u0038\u0038\u0036\u0066\u0037\u0030d\u0030\u0031\u0030\u0031\u0030\u00310\u0035\u0030\u0030\u0030\u0033\u0034\u0062\u0030\u0030\u0033\u0030\u00348\u0030\u0032\u0034\u0031\u0030\u0030\u0062\u0038\u0037\u0065\u0061\u0066\u0062\u0036\u0063\u0030\u0037\u0034\u0039\u0039\u0065\u0062\u00397\u0063\u0063\u0039\u0064\u0033\u0035\u0036\u0035\u0065\u0063\u00663\u0031\u0036\u0038\u0031\u0039\u0036\u0033\u0030\u0031\u0039\u0030\u0037c\u0038\u0034\u0031\u0061\u0064\u0064c6\u0036\u0035\u0030\u0038\u0036\u0062\u0062\u0033\u0065\u0064\u0038\u0065\u0062\u0031\u0032\u0064\u0039\u0064\u0061\u0032\u0036\u0063\u0061\u0066\u0061\u0039\u0036\u00345\u0030\u00314\u0036\u0064\u0061\u0038\u0062\u0064\u0030\u0063c\u0066\u0031\u0035\u0035\u0066\u0063a\u0063\u0063\u00368\u0036\u0039\u0035\u0035\u0065\u0066\u0030\u0033\u0030\u0032\u0066\u0061\u0034\u0034\u0061\u0061\u0033\u0065\u0063\u0038\u0039\u0034\u0031\u0037\u0062\u0030\u0032\u0030\u0033\u0030\u0031\u0030\u0030\u0030\u0031";
func SetMeteredKey (apiKey string )error {if len (apiKey )==0{_gf .Log .Error ("\u004d\u0065\u0074\u0065\u0072e\u0064\u0020\u004c\u0069\u0063\u0065\u006e\u0073\u0065\u0020\u0041\u0050\u0049 \u004b\u0065\u0079\u0020\u006d\u0075\u0073\u0074\u0020\u006e\u006f\u0074\u0020\u0062\u0065\u0020\u0065\u006d\u0070\u0074\u0079");
_gf .Log .Error ("\u002d\u0020\u0047\u0072\u0061\u0062\u0020\u006f\u006e\u0065\u0020\u0069\u006e\u0020\u0074h\u0065\u0020\u0046\u0072\u0065\u0065\u0020\u0054\u0069\u0065\u0072\u0020\u0061t\u0020\u0068\u0074\u0074\u0070\u0073\u003a\u002f\u002f\u0063\u006c\u006fud\u002e\u0075\u006e\u0069\u0064\u006f\u0063\u002e\u0069\u006f");
return _ac .Errorf ("\u006de\u0074\u0065\u0072e\u0064\u0020\u006ci\u0063en\u0073\u0065\u0020\u0061\u0070\u0069\u0020k\u0065\u0079\u0020\u006d\u0075\u0073\u0074\u0020\u006e\u006f\u0074\u0020\u0062\u0065\u0020\u0065\u006d\u0070\u0074\u0079\u003a\u0020\u0063\u0072\u0065\u0061\u0074\u0065 o\u006ee\u0020\u0061\u0074\u0020\u0068\u0074t\u0070\u0073\u003a\u002f\u002fc\u006c\u006f\u0075\u0064\u002e\u0075\u006e\u0069\u0064\u006f\u0063.\u0069\u006f");
};if _cdbec !=nil &&(_cdbec ._aec ||_cdbec .Tier !=LicenseTierUnlicensed ){_gf .Log .Error ("\u0045\u0052\u0052\u004f\u0052:\u0020\u0043\u0061\u006e\u006eo\u0074 \u0073\u0065\u0074\u0020\u006c\u0069\u0063\u0065\u006e\u0073\u0065\u0020\u006b\u0065\u0079\u0020\u0074\u0077\u0069c\u0065\u0020\u002d\u0020\u0053\u0068\u006f\u0075\u006c\u0064\u0020\u006a\u0075\u0073\u0074\u0020\u0069\u006e\u0069\u0074\u0069\u0061\u006c\u0069z\u0065\u0020\u006f\u006e\u0063\u0065");
return _ba .New ("\u006c\u0069\u0063en\u0073\u0065\u0020\u006b\u0065\u0079\u0020\u0061\u006c\u0072\u0065\u0061\u0064\u0079\u0020\u0073\u0065\u0074");};_cdbe :=_da ();_cdbe ._edd =apiKey ;_bdc ,_afc :=_cdbe .getStatus ();if _afc !=nil {return _afc ;};if !_bdc .Valid {return _ba .New ("\u006b\u0065\u0079\u0020\u006e\u006f\u0074\u0020\u0076\u0061\u006c\u0069\u0064");
};_acb :=&LicenseKey {_aec :true ,_feb :apiKey ,_deg :true };_cdbec =_acb ;return nil ;};func TrackUse (useKey string ){if _cdbec ==nil {return ;};if !_cdbec ._aec ||len (_cdbec ._feb )==0{return ;};if len (useKey )==0{return ;};_dbf .Lock ();defer _dbf .Unlock ();
if _gff ==nil {_gff =map[string ]int {};};_gff [useKey ]++;};type MeteredStatus struct{OK bool ;Credits int64 ;Used int64 ;};func _bec (_fc string ,_eda string )([]byte ,error ){var (_fb int ;_ad string ;);for _ ,_ad =range []string {"\u000a\u002b\u000a","\u000d\u000a\u002b\r\u000a","\u0020\u002b\u0020"}{if _fb =_ea .Index (_eda ,_ad );
_fb !=-1{break ;};};if _fb ==-1{return nil ,_ac .Errorf ("\u0069\u006e\u0076al\u0069\u0064\u0020\u0069\u006e\u0070\u0075\u0074\u002c \u0073i\u0067n\u0061t\u0075\u0072\u0065\u0020\u0073\u0065\u0070\u0061\u0072\u0061\u0074\u006f\u0072");};_ccbg :=_eda [:_fb ];
_eg :=_fb +len (_ad );_cab :=_eda [_eg :];if _ccbg ==""||_cab ==""{return nil ,_ac .Errorf ("\u0069n\u0076\u0061l\u0069\u0064\u0020\u0069n\u0070\u0075\u0074,\u0020\u006d\u0069\u0073\u0073\u0069\u006e\u0067\u0020or\u0069\u0067\u0069n\u0061\u006c \u006f\u0072\u0020\u0073\u0069\u0067n\u0061\u0074u\u0072\u0065");
};_acc ,_gce :=_de .StdEncoding .DecodeString (_ccbg );if _gce !=nil {return nil ,_ac .Errorf ("\u0069\u006e\u0076\u0061li\u0064\u0020\u0069\u006e\u0070\u0075\u0074\u0020\u006f\u0072\u0069\u0067\u0069\u006ea\u006c");};_bd ,_gce :=_de .StdEncoding .DecodeString (_cab );
if _gce !=nil {return nil ,_ac .Errorf ("\u0069\u006e\u0076al\u0069\u0064\u0020\u0069\u006e\u0070\u0075\u0074\u0020\u0073\u0069\u0067\u006e\u0061\u0074\u0075\u0072\u0065");};_gg ,_ :=_df .Decode ([]byte (_fc ));if _gg ==nil {return nil ,_ac .Errorf ("\u0050\u0075\u0062\u004b\u0065\u0079\u0020\u0066\u0061\u0069\u006c\u0065\u0064");
};_fef ,_gce :=_bag .ParsePKIXPublicKey (_gg .Bytes );if _gce !=nil {return nil ,_gce ;};_adb :=_fef .(*_bc .PublicKey );if _adb ==nil {return nil ,_ac .Errorf ("\u0050u\u0062\u004b\u0065\u0079\u0020\u0063\u006f\u006e\u0076\u0065\u0072s\u0069\u006f\u006e\u0020\u0066\u0061\u0069\u006c\u0065\u0064");
};_gcf :=_dfg .New ();_gcf .Write (_acc );_adc :=_gcf .Sum (nil );_gce =_bc .VerifyPKCS1v15 (_adb ,_f .SHA512 ,_adc ,_bd );if _gce !=nil {return nil ,_gce ;};return _acc ,nil ;};func SetMeteredKeyPersistentCache (val bool ){_cdbec ._deg =val };func _bace (_bcfa string ,_fcd string ,_bcbd string ,_abb bool )error {if _cdbec ==nil {return _ba .New ("\u006e\u006f\u0020\u006c\u0069\u0063\u0065\u006e\u0073e\u0020\u006b\u0065\u0079");
};if !_cdbec ._aec ||len (_cdbec ._feb )==0{return nil ;};if len (_bcfa )==0&&!_abb {return _ba .New ("\u0064\u006f\u0063\u004b\u0065\u0079\u0020\u006e\u006ft\u0020\u0073\u0065\u0074");};_dbf .Lock ();defer _dbf .Unlock ();if _fbg ==nil {_fbg =map[string ]struct{}{};
};if _gff ==nil {_gff =map[string ]int {};};_agg :=0;if len (_bcfa )> 0{_ ,_fbc :=_fbg [_bcfa ];if !_fbc {_fbg [_bcfa ]=struct{}{};_agg ++;};if _cdbec ._dea {_eeeg =append (_eeeg ,map[string ]interface{}{"\u0074\u0069\u006d\u0065":_fe .Now ().String (),"\u0066\u0075\u006e\u0063":_fcd ,"\u0072\u0065\u0066":_bcfa [:8],"\u0066\u0069\u006c\u0065":_bcbd ,"\u0063\u006f\u0073\u0074":_agg });
if _fbc &&_agg ==0{_gf .Log .Info ("\u0025\u0073\u0020\u0052\u0065\u0066\u003a\u0020\u0025\u0073\u0020\u007c\u0020\u0025\u0073 \u007c \u004e\u006f\u0020\u0063\u0072\u0065\u0064\u0069\u0074\u0020\u0075\u0073\u0065\u0064",_fe .Now ().String (),_bcfa [:8],_fcd );
};};};if _agg ==0&&!_abb {return nil ;};_gff [_fcd ]++;_gac :=_fe .Now ();_cfd ,_ceb :=_gabc .loadState (_cdbec ._feb );if _ceb !=nil {_gf .Log .Error ("\u0045R\u0052\u004f\u0052\u003a\u0020\u0025v",_ceb );return _ceb ;};_cfd .UsageLogs =append (_cfd .UsageLogs ,_eeeg ...);
if _cfd .Usage ==nil {_cfd .Usage =map[string ]int {};};for _cdba ,_aecf :=range _gff {if _cdba !=""{_cfd .Usage [_cdba ]+=_aecf ;};};_gff =nil ;const _cgb =24*_fe .Hour ;const _efa =3*24*_fe .Hour ;if len (_cfd .Instance )==0||_gac .Sub (_cfd .LastReported )> _cgb ||(_cfd .LimitDocs &&_cfd .RemainingDocs <=_cfd .Docs +int64 (_agg ))||_abb {_afgc ,_gafg :=_c .Hostname ();
if _gafg !=nil {return _gafg ;};_bfbf :=_cfd .Docs ;_fag ,_gcbf ,_gafg :=_dfe ();if _gafg !=nil {_gf .Log .Debug ("\u0055\u006e\u0061b\u006c\u0065\u0020\u0074o\u0020\u0067\u0065\u0074\u0020\u006c\u006fc\u0061\u006c\u0020\u0061\u0064\u0064\u0072\u0065\u0073\u0073\u003a\u0020\u0025\u0073",_gafg .Error ());
_fag =append (_fag ,"\u0069n\u0066\u006f\u0072\u006da\u0074\u0069\u006f\u006e\u0020n\u006ft\u0020a\u0076\u0061\u0069\u006c\u0061\u0062\u006ce");_gcbf =append (_gcbf ,"\u0069n\u0066\u006f\u0072\u006da\u0074\u0069\u006f\u006e\u0020n\u006ft\u0020a\u0076\u0061\u0069\u006c\u0061\u0062\u006ce");
}else {_a .Strings (_gcbf );_a .Strings (_fag );_aag ,_fbgd :=_cabf ();if _fbgd !=nil {return _fbgd ;};_cbe :=false ;for _ ,_bfd :=range _gcbf {if _bfd ==_aag .String (){_cbe =true ;};};if !_cbe {_gcbf =append (_gcbf ,_aag .String ());};};_dbc :=_da ();
_dbc ._edd =_cdbec ._feb ;_bfbf +=int64 (_agg );_dafe :=meteredUsageCheckinForm {Instance :_cfd .Instance ,Next :_cfd .Next ,UsageNumber :int (_bfbf ),NumFailed :_cfd .NumErrors ,Hostname :_afgc ,LocalIP :_ea .Join (_gcbf ,"\u002c\u0020"),MacAddress :_ea .Join (_fag ,"\u002c\u0020"),Package :"\u0075n\u0069\u006f\u0066\u0066\u0069\u0063e",PackageVersion :_ce .Version ,Usage :_cfd .Usage ,IsPersistentCache :_cdbec ._deg ,Timestamp :_gac .Unix ()};
if len (_fag )==0{_dafe .MacAddress ="\u006e\u006f\u006e\u0065";};if _cdbec ._dea {_dafe .UsageLogs =_cfd .UsageLogs ;};_cdc :=int64 (0);_bad :=_cfd .NumErrors ;_dad :=_gac ;_dfa :=0;_gfdf :=_cfd .LimitDocs ;_cdbb ,_gafg :=_dbc .checkinUsage (_dafe );if _gafg !=nil {if _gac .Sub (_cfd .LastReported )> _efa {if !_cdbb .Success {return _ba .New (_cdbb .Message );
};return _ba .New ("\u0074\u006f\u006f\u0020\u006c\u006f\u006e\u0067\u0020\u0073\u0069\u006e\u0063\u0065\u0020\u006c\u0061\u0073\u0074\u0020\u0073\u0075\u0063\u0063e\u0073\u0073\u0066\u0075\u006c \u0063\u0068e\u0063\u006b\u0069\u006e");};_cdc =_bfbf ;_bad ++;
_dad =_cfd .LastReported ;}else {_gfdf =_cdbb .LimitDocs ;_dfa =_cdbb .RemainingDocs ;_bad =0;};if len (_cdbb .Instance )==0{_cdbb .Instance =_dafe .Instance ;};if len (_cdbb .Next )==0{_cdbb .Next =_dafe .Next ;};_gafg =_gabc .updateState (_dbc ._edd ,_cdbb .Instance ,_cdbb .Next ,int (_cdc ),_gfdf ,_dfa ,int (_bad ),_dad ,nil );
if _gafg !=nil {return _gafg ;};if !_cdbb .Success {return _ac .Errorf ("\u0065r\u0072\u006f\u0072\u003a\u0020\u0025s",_cdbb .Message );};}else {_ceb =_gabc .updateState (_cdbec ._feb ,_cfd .Instance ,_cfd .Next ,int (_cfd .Docs )+_agg ,_cfd .LimitDocs ,int (_cfd .RemainingDocs ),int (_cfd .NumErrors ),_cfd .LastReported ,_cfd .Usage ,_cfd .UsageLogs ...);
if _ceb !=nil {return _ceb ;};};if _cdbec ._dea &&len (_bcfa )> 0{_ffb :="";if _bcbd !=""{_ffb =_ac .Sprintf ("\u0046i\u006c\u0065\u0020\u0025\u0073\u0020|",_bcbd );};_gf .Log .Info ("%\u0073\u0020\u007c\u0020\u0025\u0073\u0020\u0052\u0065\u0066\u003a\u0020\u0025\u0073\u0020\u007c\u0020\u0025s\u0020\u007c\u0020\u0025\u0064\u0020\u0063\u0072\u0065\u0064it\u0028\u0073\u0029 \u0075s\u0065\u0064",_gac .String (),_ffb ,_bcfa [:8],_fcd ,_agg );
};return nil ;};func _dfe ()([]string ,[]string ,error ){_afee ,_gda :=_fd .Interfaces ();if _gda !=nil {return nil ,nil ,_gda ;};var _ege []string ;var _edf []string ;for _ ,_fgce :=range _afee {if _fgce .Flags &_fd .FlagUp ==0||_gb .Equal (_fgce .HardwareAddr ,nil ){continue ;
};_aac ,_edg :=_fgce .Addrs ();if _edg !=nil {return nil ,nil ,_edg ;};_bcc :=0;for _ ,_caf :=range _aac {var _bgb _fd .IP ;switch _bcfcb :=_caf .(type ){case *_fd .IPNet :_bgb =_bcfcb .IP ;case *_fd .IPAddr :_bgb =_bcfcb .IP ;};if _bgb .IsLoopback (){continue ;
};if _bgb .To4 ()==nil {continue ;};_edf =append (_edf ,_bgb .String ());_bcc ++;};_eec :=_fgce .HardwareAddr .String ();if _eec !=""&&_bcc > 0{_ege =append (_ege ,_eec );};};return _ege ,_edf ,nil ;};var _bdb =_fe .Date (2019,6,6,0,0,0,0,_fe .UTC );