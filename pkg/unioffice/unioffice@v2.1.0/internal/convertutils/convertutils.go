//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package convertutils ;import (_c "bytes";_b "errors";_dfg "fmt";_ed "github.com/unidoc/unichart";_adc "github.com/unidoc/unichart/dataset";_eda "github.com/unidoc/unichart/render";_fgg "github.com/unidoc/unioffice/v2/common/logger";_fg "github.com/unidoc/unioffice/v2/document";
_bb "github.com/unidoc/unioffice/v2/measurement";_dfb "github.com/unidoc/unioffice/v2/schema/soo/dml";_cf "github.com/unidoc/unioffice/v2/schema/soo/dml/chart";_ad "github.com/unidoc/unioffice/v2/spreadsheet";_ab "github.com/unidoc/unioffice/v2/spreadsheet/format";
_fe "github.com/unidoc/unioffice/v2/spreadsheet/formula";_bbb "github.com/unidoc/unioffice/v2/spreadsheet/reference";_be "github.com/unidoc/unipdf/v3/core";_bg "github.com/unidoc/unipdf/v3/creator";_eeg "github.com/unidoc/unipdf/v3/model";_ee "github.com/unidoc/unipdf/v3/render";
_gc "github.com/unidoc/unitype";_db "image";_g "image/color";_e "math";_fb "os";_f "regexp";_ge "sort";_bd "strconv";_fc "strings";_df "sync";_d "unicode";);var StdFontsMap =map[string ][]string {"\u0048e\u006c\u0076\u0065\u0074\u0069\u0063a":[]string {"\u0048e\u006c\u0076\u0065\u0074\u0069\u0063a","\u0048\u0065\u006c\u0076\u0065\u0074\u0069\u0063\u0061-\u0042\u006f\u006c\u0064","\u0048\u0065\u006c\u0076\u0065\u0074\u0069\u0063\u0061\u002d\u004f\u0062l\u0069\u0071\u0075\u0065","H\u0065\u006c\u0076\u0065ti\u0063a\u002d\u0042\u006f\u006c\u0064O\u0062\u006c\u0069\u0071\u0075\u0065"},"\u0043o\u0075\u0072\u0069\u0065\u0072":[]string {"\u0043o\u0075\u0072\u0069\u0065\u0072","\u0043\u006f\u0075r\u0069\u0065\u0072\u002d\u0042\u006f\u006c\u0064","\u0043o\u0075r\u0069\u0065\u0072\u002d\u004f\u0062\u006c\u0069\u0071\u0075\u0065","\u0043\u006f\u0075\u0072ie\u0072\u002d\u0042\u006f\u006c\u0064\u004f\u0062\u006c\u0069\u0071\u0075\u0065"},"\u0054i\u006de\u0073\u0020\u004e\u0065\u0077\u0020\u0052\u006f\u006d\u0061\u006e":[]string {"T\u0069\u006d\u0065\u0073\u002d\u0052\u006f\u006d\u0061\u006e","\u0054\u0069\u006d\u0065\u0073\u002d\u0042\u006f\u006c\u0064","\u0054\u0069\u006de\u0073\u002d\u0049\u0074\u0061\u006c\u0069\u0063","\u0054\u0069m\u0065\u0073\u002dB\u006f\u006c\u0064\u0049\u0074\u0061\u006c\u0069\u0063"},"\u0064e\u0066\u0061\u0075\u006c\u0074":[]string {"\u0048e\u006c\u0076\u0065\u0074\u0069\u0063a","\u0048\u0065\u006c\u0076\u0065\u0074\u0069\u0063\u0061-\u0042\u006f\u006c\u0064","\u0048\u0065\u006c\u0076\u0065\u0074\u0069\u0063\u0061\u002d\u004f\u0062l\u0069\u0071\u0075\u0065","H\u0065\u006c\u0076\u0065ti\u0063a\u002d\u0042\u006f\u006c\u0064O\u0062\u006c\u0069\u0071\u0075\u0065"}};
type FontStyle byte ;func _cba (_bcac *_cf .CT_SerAx )(uint32 ,_cf .ST_AxPos ,_cf .ST_TickMark ,_cf .ST_TickLblPos ,*_cf .CT_ChartLines ,uint32 ,*_dfb .CT_ShapeProperties ,error ){var _afb ,_gcaf uint32 ;var _bgba _cf .ST_AxPos ;var _edad _cf .ST_TickMark ;
var _cag *_cf .CT_ChartLines ;var _gfag _cf .ST_TickLblPos ;if _bcac .AxId ==nil {return _afb ,_bgba ,_edad ,_gfag ,_cag ,_gcaf ,_bcac .SpPr ,_b .New ("\u004e\u006f\u0020x\u0020\u0061\u0078\u0069\u0073\u0020\u0049\u0044");}else {_afb =_bcac .AxId .ValAttr ;
};if _bcac .AxPos ==nil {return _afb ,_bgba ,_edad ,_gfag ,_cag ,_gcaf ,_bcac .SpPr ,_b .New ("\u004eo\u0020x\u0020\u0061\u0078\u0069\u0073 \u0070\u006fs\u0069\u0074\u0069\u006f\u006e");}else {_bgba =_bcac .AxPos .ValAttr ;};if _bcac .MajorTickMark !=nil {_edad =_bcac .MajorTickMark .ValAttr ;
};if _bcac .TickLblPos !=nil {_gfag =_bcac .TickLblPos .ValAttr ;};if _bcac .CrossAx ==nil {return _afb ,_bgba ,_edad ,_gfag ,_cag ,_gcaf ,_bcac .SpPr ,_b .New ("\u004e\u006f \u0063\u0072\u006fs\u0073\u0020\u0061\u0078\u0069\u0073\u0020\u0049\u0044");}else {_gcaf =_bcac .CrossAx .ValAttr ;
};_cag =_bcac .MajorGridlines ;return _afb ,_bgba ,_edad ,_gfag ,_cag ,_gcaf ,_bcac .SpPr ,nil ;};func _daeed (_ecde *_cf .CT_DateAx )(uint32 ,_cf .ST_AxPos ,_cf .ST_TickMark ,_cf .ST_TickLblPos ,*_cf .CT_ChartLines ,uint32 ,*_dfb .CT_ShapeProperties ,error ){var _cgbe ,_beab uint32 ;
var _dcdf _cf .ST_AxPos ;var _egf _cf .ST_TickMark ;var _gada *_cf .CT_ChartLines ;var _gdbb _cf .ST_TickLblPos ;if _ecde .AxId ==nil {return _cgbe ,_dcdf ,_egf ,_gdbb ,_gada ,_beab ,_ecde .SpPr ,_b .New ("\u004e\u006f\u0020x\u0020\u0061\u0078\u0069\u0073\u0020\u0049\u0044");
}else {_cgbe =_ecde .AxId .ValAttr ;};if _ecde .AxPos ==nil {return _cgbe ,_dcdf ,_egf ,_gdbb ,_gada ,_beab ,_ecde .SpPr ,_b .New ("\u004eo\u0020x\u0020\u0061\u0078\u0069\u0073 \u0070\u006fs\u0069\u0074\u0069\u006f\u006e");}else {_dcdf =_ecde .AxPos .ValAttr ;
};if _ecde .MajorTickMark !=nil {_egf =_ecde .MajorTickMark .ValAttr ;};if _ecde .TickLblPos !=nil {_gdbb =_ecde .TickLblPos .ValAttr ;};if _ecde .CrossAx ==nil {return _cgbe ,_dcdf ,_egf ,_gdbb ,_gada ,_beab ,_ecde .SpPr ,_b .New ("\u004e\u006f \u0063\u0072\u006fs\u0073\u0020\u0061\u0078\u0069\u0073\u0020\u0049\u0044");
}else {_beab =_ecde .CrossAx .ValAttr ;};_gada =_ecde .MajorGridlines ;return _cgbe ,_dcdf ,_egf ,_gdbb ,_gada ,_beab ,_ecde .SpPr ,nil ;};func (_cdec *creatorContext )drawLineChart (_fcbc *_cf .CT_LineChart ,_bfgc *Rectangle ,_aaca []*_cf .CT_PlotAreaChoice1 ,_ffaf float64 )([]*legendItem ,error ){_ddcc :=[]*legendItem {};
_eee :=_fcbc .Ser ;_gfgb :=make (map[int ][]float64 );_gbdc :=make (map[int ][]float64 );_eggfg :=make (map[int ]_bg .Color );_aeca :=make (map[int ]float64 );for _cfcb ,_aaga :=range _eee {var _bad string ;if _ggdg :=_aaga .Tx ;_ggdg !=nil {if _gcdag :=_ggdg .SerTxChoice ;
_gcdag !=nil {if _gcdag .V !=nil {_bad =*_gcdag .V ;}else if _cda :=_gcdag .StrRef ;_cda !=nil {if _fdb :=_cda .StrCache ;_fdb !=nil {for _ ,_feab :=range _fdb .Pt {_bad =_feab .V ;};};};};};_eggfg [_cfcb ]=_bg .ColorWhite ;if _dgg :=_aaga .SpPr ;_dgg !=nil {if _ccac :=_dgg .Ln ;
_ccac !=nil {if _cdba :=_ccac .WAttr ;_cdba !=nil {_aeca [_cfcb ]=_bb .FromEMU (int64 (*_cdba ));if _bcgc :=_ccac .LineFillPropertiesChoice .SolidFill ;_bcgc !=nil {_fecc :=_cdec .getPdfColorFromSolidFill (_bcgc );if _fecc !=nil {_eggfg [_cfcb ]=_fecc ;
};};};};};if _fbeeg :=_aaga .Val ;_fbeeg !=nil {if _bcga :=_fbeeg .NumDataSourceChoice ;_bcga !=nil {if _deed :=_bcga .NumRef ;_deed !=nil {if _bfdg :=_deed .NumCache ;_bfdg !=nil {for _ ,_ecc :=range _bfdg .Pt {_gfgb [_cfcb ]=append (_gfgb [_cfcb ],float64 (_ecc .IdxAttr ));
_faac ,_ :=_bd .ParseFloat (_fc .TrimSpace (_ecc .V ),64);_gbdc [_cfcb ]=append (_gbdc [_cfcb ],_faac );};};};};};_ddcc =append (_ddcc ,&legendItem {_ace :_bad ,_cabe :_aaga .SpPr });};_gagc :=true ;_eccg :=true ;_efdb :="";_gbg :="";if len (_aaca )> 0{for _ ,_aae :=range _aaca {if _aae .CatAx !=nil {if _aae .CatAx .Delete !=nil {if !*(*_aae .CatAx .Delete ).ValAttr {_gagc =false ;
};};_efdb =_cdec .getTitle (_aae .CatAx .Title );}else if _aae .ValAx !=nil {if _aae .ValAx .Delete !=nil {if !*(*_aae .ValAx .Delete ).ValAttr {_eccg =false ;};};_gbg =_cdec .getTitle (_aae .ValAx .Title );};};};_bbba :=_eda .Style {Hidden :_gagc ,FontSize :8*_ffaf };
_cfa :=_eda .Style {Hidden :_eccg ,FontSize :8*_ffaf };_gagcd :=&_ed .Chart {Series :[]_adc .Series {},YAxisSecondary :_ed .YAxis {Style :_cfa ,Name :_gbg ,NameStyle :_cfa },XAxis :_ed .XAxis {Style :_bbba ,Name :_efdb ,NameStyle :_bbba },YAxis :_ed .YAxis {Style :_eda .Style {Hidden :true }}};
for _faeg :=0;_faeg < len (_eggfg );_faeg ++{_dfce ,_gdgf ,_gebg :=_eggfg [_faeg ].ToRGB ();_gecg :=_adc .ContinuousSeries {XValues :_gfgb [_faeg ],YValues :_gbdc [_faeg ],Style :_eda .Style {StrokeColor :_g .RGBA {uint8 (_dfce *255),uint8 (_gdgf *255),uint8 (_gebg *255),255},StrokeWidth :_aeca [_faeg ]}};
_gagcd .Series =append (_gagcd .Series ,_gecg );};_gagcd .SetHeight (int (_e .Abs (_bfgc .Top -_bfgc .Bottom )));_gagcd .SetWidth (int (_e .Abs (_bfgc .Right -_bfgc .Left )));_gbgd :=_cdec ._gafe ;_dfec :=_bg .NewChart (_gagcd );_dfec .SetPos (_bfgc .Left ,_bfgc .Top );
_dadg :=_gbgd .Draw (_dfec );if _dadg !=nil {return nil ,_dadg ;};return _ddcc ,nil ;};func GetImage (c *_bg .Creator ,goImg _db .Image ,imgHeight ,imgWidth ,left ,top ,dividerX ,dividerY float64 ,part ImgPart )(*_bg .Image ,error ){if goImg ==nil {return nil ,nil ;
};_ggdd :=goImg .Bounds ().Size ();_geed :=_ggdd .X ;_bdad :=_ggdd .Y ;if dividerX !=0{dividerX =dividerX /imgWidth *float64 (_geed );};if dividerY !=0{dividerY =dividerY /imgHeight *float64 (_bdad );};var _fgge _db .Rectangle ;switch part {case ImgPart_t :_fgge =_db .Rect (0,0,_geed ,int (dividerY ));
case ImgPart_b :_fgge =_db .Rect (0,int (dividerY ),_geed ,_bdad );case ImgPart_l :_fgge =_db .Rect (0,0,int (dividerX ),_bdad );case ImgPart_r :_fgge =_db .Rect (int (dividerX ),0,_geed ,_bdad );case ImgPart_lt :_fgge =_db .Rect (0,0,int (dividerX ),int (dividerY ));
case ImgPart_rt :_fgge =_db .Rect (int (dividerX ),0,_geed ,int (dividerY ));case ImgPart_lb :_fgge =_db .Rect (0,int (dividerY ),int (dividerX ),_bdad );case ImgPart_rb :_fgge =_db .Rect (int (dividerX ),int (dividerY ),_geed ,_bdad );default:_fgge =_db .Rect (0,0,_geed ,_bdad );
};_bga :=CropImageByRect (goImg ,_fgge );_daag ,_dbec :=c .NewImageFromGoImage (_bga );if _dbec !=nil {return nil ,_dbec ;};_daag .Scale (imgWidth /float64 (_geed ),imgHeight /float64 (_bdad ));_daag .SetPos (left ,top );return _daag ,nil ;};func (_feaa *creatorContext )drawLineWithProps (_cgba *_dfb .CT_ShapeProperties ,_befd ,_aagd ,_aaf ,_aga float64 ,_bgfb bool ){if _cgba !=nil {if _bbea :=_cgba .Ln ;
_bbea !=nil {_ggae :=_feaa .getPdfColorFromSolidFill (_bbea .LineFillPropertiesChoice .SolidFill );if _ggae ==nil &&_bgfb {_ggae =_bg .ColorBlack ;};if _ggae !=nil {var _cebd float64 ;if _ecfb :=_bbea .WAttr ;_ecfb !=nil {_cebd =_bb .FromEMU (int64 (*_ecfb ));
}else {_cebd =_bgc ;};DrawLine (_feaa ._gafe ,_befd ,_aagd ,_aaf ,_aga ,_cebd ,_ggae );};};};};type legendItem struct{_ace string ;_cabe *_dfb .CT_ShapeProperties ;};var _gaf =_fgd (1);func _ccbg (_aacf _dfb .ST_SchemeColorVal ,_abf *_dfb .Theme )string {if _cbad :=_abf .ThemeElements ;
_cbad !=nil {if _afda :=_cbad .ClrScheme ;_afda !=nil {switch _aacf {case _dfb .ST_SchemeColorValLt1 :return GetColorStringFromDmlColor (_afda .Lt1 );case _dfb .ST_SchemeColorValDk1 ,_dfb .ST_SchemeColorValTx1 :return GetColorStringFromDmlColor (_afda .Dk1 );
case _dfb .ST_SchemeColorValLt2 :return GetColorStringFromDmlColor (_afda .Lt2 );case _dfb .ST_SchemeColorValDk2 :return GetColorStringFromDmlColor (_afda .Dk2 );case _dfb .ST_SchemeColorValAccent1 :return GetColorStringFromDmlColor (_afda .Accent1 );case _dfb .ST_SchemeColorValAccent2 :return GetColorStringFromDmlColor (_afda .Accent2 );
case _dfb .ST_SchemeColorValAccent3 :return GetColorStringFromDmlColor (_afda .Accent3 );case _dfb .ST_SchemeColorValAccent4 :return GetColorStringFromDmlColor (_afda .Accent4 );case _dfb .ST_SchemeColorValAccent5 :return GetColorStringFromDmlColor (_afda .Accent5 );
case _dfb .ST_SchemeColorValAccent6 :return GetColorStringFromDmlColor (_afda .Accent6 );};};};return "";};func _dcbf (_aaaf *_cf .CT_ValAx )(uint32 ,_cf .ST_AxPos ,_cf .ST_TickMark ,_cf .ST_TickLblPos ,*_cf .CT_ChartLines ,uint32 ,*_dfb .CT_ShapeProperties ,error ){var _bbf ,_eebc uint32 ;
var _edb _cf .ST_AxPos ;var _baeg _cf .ST_TickMark ;var _agg *_cf .CT_ChartLines ;var _ebb _cf .ST_TickLblPos ;if _aaaf .AxId ==nil {return _bbf ,_edb ,_baeg ,_ebb ,_agg ,_eebc ,_aaaf .SpPr ,_b .New ("\u004e\u006f\u0020x\u0020\u0061\u0078\u0069\u0073\u0020\u0049\u0044");
}else {_bbf =_aaaf .AxId .ValAttr ;};if _aaaf .AxPos ==nil {return _bbf ,_edb ,_baeg ,_ebb ,_agg ,_eebc ,_aaaf .SpPr ,_b .New ("\u004eo\u0020x\u0020\u0061\u0078\u0069\u0073 \u0070\u006fs\u0069\u0074\u0069\u006f\u006e");}else {_edb =_aaaf .AxPos .ValAttr ;
};if _aaaf .MajorTickMark !=nil {_baeg =_aaaf .MajorTickMark .ValAttr ;};if _aaaf .TickLblPos !=nil {_ebb =_aaaf .TickLblPos .ValAttr ;};if _aaaf .CrossAx ==nil {return _bbf ,_edb ,_baeg ,_ebb ,_agg ,_eebc ,_aaaf .SpPr ,_b .New ("\u004e\u006f \u0063\u0072\u006fs\u0073\u0020\u0061\u0078\u0069\u0073\u0020\u0049\u0044");
}else {_eebc =_aaaf .CrossAx .ValAttr ;};_agg =_aaaf .MajorGridlines ;return _bbf ,_edb ,_baeg ,_ebb ,_agg ,_eebc ,_aaaf .SpPr ,nil ;};func _ccgc (_fdcg uint8 ,_decb float64 )string {_fbdf :=float64 (_fdcg );return _dfg .Sprintf ("\u0025\u0030\u0032\u0078",int (_fbdf *_decb ));
};type BorderPosition byte ;func (_faf FontStyle )String ()string {return []string {"\u0052e\u0067\u0075\u006c\u0061\u0072","\u0042\u006f\u006c\u0064","\u0049\u0074\u0061\u006c\u0069\u0063","\u0042\u006f\u006c\u0064\u0049\u0074\u0061\u006c\u0069\u0063"}[int (_faf )];
};func AdjustColorByShade (colorStr string ,shade float64 )string {var _aab ,_fbag ,_caac uint8 ;_egfd ,_ :=_dfg .Sscanf (colorStr ,"\u0025\u0030\u0032x\u0025\u0030\u0032\u0078\u0025\u0030\u0032\u0078",&_aab ,&_fbag ,&_caac );if _egfd !=3{return "";};return _ccgc (_aab ,shade )+_ccgc (_fbag ,shade )+_ccgc (_caac ,shade );
};func (_agf *creatorContext )drawLegend (_ccag *Rectangle ,_bca []*legendItem ,_gfcba bool ){_efb :=_agf ._gccb ;_cbg :=_fgd (2.5)*_efb ;_eeff :=_cbb *_efb ;_fff :=(_cbg -_eeff )/2;_gacc :=float64 (len (_bca ));if _gfcba {_feac :=&Rectangle {Top :_ccag .Top +_fgd (1)*_efb ,Bottom :_ccag .Bottom -_fgd (1)*_efb ,Left :_ccag .Left +_fgd (2.5)*_efb ,Right :_ccag .Right -_fgd (2.5)*_efb };
var _fce float64 ;if _gacc > 1{_fce =(_feac .Right -_feac .Left )/_gacc ;};_dgc :=_feac .Left ;_fag :=_feac .Top ;for _ ,_dec :=range _bca {if _acg :=_dec ._cabe ;_acg !=nil {_agf .drawRectangleWithProps (_acg ,_dgc ,_fag +_fff ,_eeff ,_eeff ,false );_accb :=_dgc +_eeff *2;
_bae :=_agf ._gafe .NewStyledParagraph ();_bae .SetPos (_accb ,_fag );_af :=_bae .Append (_dec ._ace );_aeed ,_fef :=_eeg .NewStandard14Font (_eeg .HelveticaName );if _fef ==nil {_gcgb :=_agf ._gafe .NewTextStyle ();_gcgb .Font =_aeed ;_gcgb .FontSize =_cbg ;
_gcgb .TextRise =0.4;_af .Style =_gcgb ;_agf ._gafe .Draw (_bae );};};_dgc +=_fce ;};}else {_bdee :=&Rectangle {Top :_ccag .Top +_fgd (2.5)*_efb ,Bottom :_ccag .Bottom -_fgd (2.5)*_efb ,Left :_ccag .Left +_fgd (2.5)*_efb ,Right :_ccag .Right -_fgd (2.5)*_efb };
var _gdg float64 ;if _gacc > 1{_gdg =(_bdee .Bottom -_bdee .Top -_cbg )/(_gacc -1);if _gdg < _cbg {_gdg =_cbg ;};};_fegg :=_bdee .Top ;_ggac :=_bdee .Left ;_gceg :=_ggac +_eeff *2;for _ ,_deb :=range _bca {if _eabd :=_deb ._cabe ;_eabd !=nil {_agf .drawRectangleWithProps (_eabd ,_ggac ,_fegg +_fff ,_eeff ,_eeff ,false );
_bdf :=_agf ._gafe .NewStyledParagraph ();_bdf .SetPos (_gceg ,_fegg );_bdf .SetWidth (_bdee .Right -_bdee .Left );_abe :=_bdf .Append (_deb ._ace );_eefa ,_eebd :=_eeg .NewStandard14Font (_eeg .HelveticaName );if _eebd ==nil {_gca :=_agf ._gafe .NewTextStyle ();
_gca .Font =_eefa ;_gca .FontSize =_cbg ;_gca .TextRise =0.4;_abe .Style =_gca ;_agf ._gafe .Draw (_bdf );};};_fegg +=_gdg ;};};};func MakeImageFromChartSpace (cs *_cf .ChartSpace ,width ,height float64 ,theme *_dfb .Theme ,workbook *_ad .Workbook )(_db .Image ,error ){_begg ,_gbdd :=_cae (cs ,width ,height ,theme ,true ,workbook );
if _gbdd !=nil {return nil ,_gbdd ;};_cebg ,_gbdd :=GetPageFromCreator (_begg );if _gbdd !=nil {return nil ,_gbdd ;};return _ee .NewImageDevice ().Render (_cebg );};func DrawRectangle (c *_bg .Creator ,r *Rectangle ,w float64 ,color _bg .Color ){if color ==nil {return ;
};DrawLine (c ,r .Left ,r .Top ,r .Right ,r .Top ,w ,color );DrawLine (c ,r .Left ,r .Top ,r .Left ,r .Bottom ,w ,color );DrawLine (c ,r .Left ,r .Bottom ,r .Right ,r .Bottom ,w ,color );DrawLine (c ,r .Right ,r .Top ,r .Right ,r .Bottom ,w ,color );};
func AdjustColorByTint (colorStr string ,tint float64 )string {var _bagd ,_gdag ,_fde uint8 ;_dab ,_ :=_dfg .Sscanf (colorStr ,"\u0025\u0030\u0032x\u0025\u0030\u0032\u0078\u0025\u0030\u0032\u0078",&_bagd ,&_gdag ,&_fde );if _dab !=3{return "";};return _baea (_bagd ,tint )+_baea (_gdag ,tint )+_baea (_fde ,tint );
};type creatorContext struct{_gafe *_bg .Creator ;_cegc *_dfb .Theme ;_gccb float64 ;};func RegisterEmbeddedFonts (d *_fg .Document )error {_cbaa :=d .FontTable ();if _cbaa ==nil {return nil ;};for _ ,_beb :=range _cbaa .Font {if _beb .EmbedRegular !=nil {return _cfegd (d ,_beb .NameAttr ,_beb .EmbedRegular .IdAttr ,_beb .EmbedRegular .FontKeyAttr );
};if _beb .EmbedBold !=nil {return _cfegd (d ,_beb .NameAttr ,_beb .EmbedBold .IdAttr ,_beb .EmbedBold .FontKeyAttr );};if _beb .EmbedItalic !=nil {return _cfegd (d ,_beb .NameAttr ,_beb .EmbedItalic .IdAttr ,_beb .EmbedItalic .FontKeyAttr );};if _beb .EmbedBoldItalic !=nil {return _cfegd (d ,_beb .NameAttr ,_beb .EmbedBoldItalic .IdAttr ,_beb .EmbedBoldItalic .FontKeyAttr );
};};return nil ;};func _cfeg (_bfdb ,_cbc ,_efg float64 )(uint8 ,uint8 ,uint8 ){var _adbe float64 ;if _efg < 0.5{_adbe =_efg *(1+_cbc );}else {_adbe =_efg +_cbc -_efg *_cbc ;};_egbe :=_efg *2-_adbe ;_bfdb /=360.0;_fface :=_gedb (_bfdb +1.0/3.0);_geaf :=_gedb (_bfdb );
_dfea :=_gedb (_bfdb -1.0/3.0);_bdgf :=_eabg (_fface ,_adbe ,_egbe );_bed :=_eabg (_geaf ,_adbe ,_egbe );_efee :=_eabg (_dfea ,_adbe ,_egbe );return uint8 (255*_bdgf ),uint8 (255*_bed ),uint8 (255*_efee );};func (_ffdba barSerByOrder )Swap (i ,j int ){_ffdba [i ],_ffdba [j ]=_ffdba [j ],_ffdba [i ]};
type Rectangle struct{Top float64 ;Bottom float64 ;Left float64 ;Right float64 ;};func (_gcda *creatorContext )getTitle (_aca *_cf .CT_Title )string {_bgb :="";if _aca !=nil &&_aca .Tx !=nil &&_aca .Tx .TxChoice !=nil &&_aca .Tx .TxChoice .Rich !=nil {if len (_aca .Tx .TxChoice .Rich .P )> 0{for _ ,_gbb :=range _aca .Tx .TxChoice .Rich .P {if len (_gbb .EG_TextRun )> 0{_bgb +="\u000a"+_gbb .EG_TextRun [0].TextRunChoice .R .T ;
};};};};return _bgb ;};var _eggb =map[string ]FontStyle {"\u0052e\u0067\u0075\u006c\u0061\u0072":FontStyle_Regular ,"\u0042\u006f\u006c\u0064":FontStyle_Bold ,"\u0049\u0074\u0061\u006c\u0069\u0063":FontStyle_Italic ,"B\u006f\u006c\u0064\u0020\u0049\u0074\u0061\u006c\u0069\u0063":FontStyle_BoldItalic };
func GetColorStringFromDmlColor (dmlColor *_dfb .CT_Color )string {var _cde string ;if _gagg :=dmlColor .SrgbClr ;_gagg !=nil {_cde =_gagg .ValAttr ;}else if _fbca :=dmlColor .SysClr ;_fbca !=nil {if _fbca .LastClrAttr !=nil {return *_fbca .LastClrAttr ;
};return "\u0030\u0030\u0030\u0030\u0030\u0030";};return _cde ;};func AdjustColor (colorStr string ,EG_ColorTransform []*_dfb .EG_ColorTransform )string {for _ ,_fced :=range EG_ColorTransform {if _gfbd :=_fced .ColorTransformChoice .Tint ;_gfbd !=nil {if _gefb :=_gfbd .ValAttr .ST_PositiveFixedPercentageDecimal ;
_gefb !=nil {colorStr =AdjustColorByTint (colorStr ,1.0-float64 (*_gefb )/Percent100 );};};if _bdd :=_fced .ColorTransformChoice .Shade ;_bdd !=nil {if _def :=_bdd .ValAttr .ST_PositiveFixedPercentageDecimal ;_def !=nil {colorStr =AdjustColorByShade (colorStr ,float64 (*_def )/Percent100 );
};};if _bcbb :=_fced .ColorTransformChoice .LumMod ;_bcbb !=nil {if _ecdeg :=_bcbb .ValAttr .ST_PercentageDecimal ;_ecdeg !=nil {colorStr =AdjustColorByLumMod (colorStr ,float64 (*_ecdeg )/Percent100 );};};if _dbc :=_fced .ColorTransformChoice .LumOff ;
_dbc !=nil {if _dfd :=_dbc .ValAttr .ST_PercentageDecimal ;_dfd !=nil {colorStr =AdjustColorByLumOff (colorStr ,float64 (*_dfd )/Percent100 );};};};return colorStr ;};func _gedb (_gaade float64 )float64 {if _gaade < 0{_gaade +=float64 (-int (_gaade )+1);
}else if _gaade > 1{_gaade -=float64 (int (_gaade ));};return _gaade ;};type fontsMap struct{_cce *_df .Mutex ;_cafga map[string ]map[FontStyle ]*_eeg .PdfFont ;};var (DefaultFontSize =12.0;DefaultImageEncoder _be .StreamEncoder ;DefaultPageSize =PageSizeA4 ;
_bcce =map[PageSize ]_bg .PageSize {PageSizeA3 :_bg .PageSizeA3 ,PageSizeA4 :_bg .PageSizeA4 ,PageSizeA5 :_bg .PageSizeA5 ,PageSizeLetter :_bg .PageSizeLetter ,PageSizeLegal :_bg .PageSizeLegal };);func MakeTempCreator (width ,height float64 )*_bg .Creator {_gfee :=_bg .New ();
_gfee .SetPageSize (_bg .PageSize {width ,height });_gfee .SetPageMargins (0,0,0,0);return _gfee ;};func _cae (_ffad *_cf .ChartSpace ,_gac ,_aeg float64 ,_gcg *_dfb .Theme ,_fdf bool ,_dcb *_ad .Workbook )(*_bg .Creator ,error ){_egb :=1.0;if _fdf {_egb =8.0;
};_fbbb :=&Rectangle {};_bafe :=&Rectangle {Top :_fbbb .Top ,Bottom :_aeg -_fbbb .Bottom ,Left :_fbbb .Left ,Right :_gac -_fbbb .Right };_aaa :=MakeTempCreator (_gac *_egb +1,_aeg *_egb +1);_daa :=&creatorContext {_gafe :_aaa ,_cegc :_gcg ,_gccb :_egb };
var _bgcf bool ;if _egc :=_ffad .Chart ;_egc !=nil {_cgb :=_egc .PlotArea ;if _cgb ==nil {return nil ,_b .New ("\u004e\u006f\u0020p\u006c\u006f\u0074\u0020\u0061\u0072\u0065\u0061");};_ccd :=0.0;if _egc .AutoTitleDeleted !=nil &&_egc .AutoTitleDeleted .ValAttr !=nil &&!*_egc .AutoTitleDeleted .ValAttr {if _egc .Title !=nil &&_egc .Title .Overlay !=nil &&_egc .Title .Overlay .ValAttr !=nil &&!*_egc .Title .Overlay .ValAttr {_ccd =_fgd (10);
};_bggb :=_daa .getTitle (_egc .Title );if _bggb ==""{_bggb ="C\u0068\u0061\u0072\u0074\u0020\u0054\u0069\u0074\u006c\u0065";};_daa .drawTitle (_bggb ,_bafe .Left +_bafe .Right /2,_fgd (5));};_ccb :=&Rectangle {Top :_fgd (10)+_ccd ,Bottom :_bafe .Bottom -_fgd (15),Left :_fgd (10),Right :_bafe .Right -_fgd (10)};
var _ebe *Rectangle ;_daee :=_egc .Legend ;if _daee !=nil {_fdc :=_daee .Overlay !=nil &&_daee .Overlay .ValAttr !=nil &&*_daee .Overlay .ValAttr ;if _dca :=_daee .LegendPos ;_dca !=nil {switch _dca .ValAttr {case _cf .ST_LegendPosTr :if !_fdc {_ccb =&Rectangle {Top :_fgd (25)+_ccd ,Bottom :_bafe .Bottom -_fgd (10),Left :_fgd (10),Right :_bafe .Right -_fgd (25)};
};_ebe =&Rectangle {Top :_fgd (2.5)+_ccd ,Bottom :_fgd (22.5),Left :_bafe .Right -_fgd (22.5),Right :_bafe .Right -_fgd (2.5)};case _cf .ST_LegendPosT :_ebe =&Rectangle {Top :_fgd (2.5)+_ccd ,Bottom :_fgd (7.5),Left :(_bafe .Right -_bafe .Left )*0.25,Right :(_bafe .Right -_bafe .Left )*0.75};
if !_fdc {_ccb =&Rectangle {Top :_fgd (12.5)+_ccd ,Bottom :_bafe .Bottom -_fgd (15),Left :_fgd (10),Right :_bafe .Right -_fgd (5)};};_bgcf =true ;case _cf .ST_LegendPosB :_ebe =&Rectangle {Top :_bafe .Bottom -_fgd (7.5)+_ccd ,Bottom :_bafe .Bottom -_fgd (2.5),Left :(_bafe .Right -_bafe .Left )*0.25,Right :(_bafe .Right -_bafe .Left )*0.75};
if !_fdc {_ccb =&Rectangle {Top :_fgd (5)+_ccd ,Bottom :_bafe .Bottom -_fgd (15),Left :_fgd (10),Right :_bafe .Right -_fgd (5)};};_bgcf =true ;case _cf .ST_LegendPosR :_ebe =&Rectangle {Top :(_bafe .Bottom -_bafe .Top )/2-_fgd (10)+_ccd ,Bottom :(_bafe .Bottom -_bafe .Top )/2+_fgd (10),Left :_bafe .Right -_fgd (22.5),Right :_bafe .Right -_fgd (2.5)};
if !_fdc {_ccb =&Rectangle {Top :_fgd (5)+_ccd ,Bottom :_bafe .Bottom -_fgd (12.5),Left :_fgd (10),Right :_bafe .Right -_fgd (25)};};case _cf .ST_LegendPosL :_ebe =&Rectangle {Top :(_bafe .Bottom -_bafe .Top )/2-_fgd (10)+_ccd ,Bottom :(_bafe .Bottom -_bafe .Top )/2+_fgd (10),Left :_fgd (2.5),Right :_fgd (22.5)};
if !_fdc {_ccb =&Rectangle {Top :_fgd (5)+_ccd ,Bottom :_bafe .Bottom -_fgd (12.5),Left :_fgd (30),Right :_bafe .Right -_fgd (5)};};default:_ebe =&Rectangle {Top :(_bafe .Bottom -_bafe .Top )/2-_fgd (10)+_ccd ,Bottom :(_bafe .Bottom -_bafe .Top )/2+_fgd (10),Left :_bafe .Right -_fgd (25),Right :_bafe .Right -_fgd (5)};
if !_fdc {_ccb =&Rectangle {Top :_fgd (5)+_ccd ,Bottom :_bafe .Bottom -_fgd (12.5),Left :_fgd (100),Right :_bafe .Right -_fgd (25)};};};};};_ccb .scale (_egb );_daa .drawBorderWithProps (_cgb .SpPr ,_ccb ,_bgc );_bce :=[]*legendItem {};var _bafea error ;
_cbf :=_cgb .PlotAreaChoice1 ;for _ ,_bdc :=range _cgb .PlotAreaChoice {if _ede :=_bdc .BarChart ;_ede !=nil {_bce ,_bafea =_daa .drawBarChart (_ede ,_ccb ,_cbf ,_dcb );if _bafea !=nil {return nil ,_bafea ;};break ;};if _dcd :=_bdc .PieChart ;_dcd !=nil {_bce ,_bafea =_daa .drawPieChart (_dcd ,_ccb ,_dcb ,_egb );
if _bafea !=nil {return nil ,_bafea ;};break ;};if _acf :=_bdc .ScatterChart ;_acf !=nil {_bce ,_bafea =_daa .drawScatterChart (_acf ,_ccb ,_cbf ,_egb );if _bafea !=nil {return nil ,_bafea ;};break ;};if _fbd :=_bdc .LineChart ;_fbd !=nil {_bce ,_bafea =_daa .drawLineChart (_fbd ,_ccb ,_cbf ,_egb );
if _bafea !=nil {return nil ,_bafea ;};break ;};if _edd :=_bdc .AreaChart ;_edd !=nil {_bce ,_bafea =_daa .drawAreaChart (_edd ,_ccb ,_cbf ,_egb );if _bafea !=nil {return nil ,_bafea ;};break ;};};if _daee !=nil {_ebe .scale (_egb );_daa .drawBorderWithProps (_daee .SpPr ,_ebe ,_bgc );
if len (_bce )!=0{_daa .drawLegend (_ebe ,_bce ,_bgcf );};};};_bafe .scale (_egb );_daa .drawBorderWithProps (_ffad .SpPr ,_bafe ,_bgc );return _aaa ,nil ;};func _agb (_abge string )error {if !_fc .HasSuffix (_abge ,"\u002e\u0074\u0074\u0066"){_fgg .Log .Debug ("\u0055\u006es\u0075\u0070\u0070\u006f\u0072\u0074\u0065\u0064\u0020\u0066\u006f\u006e\u0074\u0020\u0066\u0069\u006c\u0065\u0020\u0066\u006f\u0072ma\u0074\u002e");
return _dfg .Errorf ("\u0055\u006e\u0073\u0075\u0070\u0070o\u0072\u0074\u0065\u0064\u0020f\u006f\u006e\u0074\u0020\u0066\u0069l\u0065\u0020\u0066\u006f\u0072m\u0061\u0074\u002c\u0020\u0063\u0075\u0072\u0072\u0065\u006e\u0074\u006cy\u0020\u006f\u006e\u006c\u0079\u0020\u0073\u0075\u0070\u0070\u006f\u0072\u0074\u0020\u0054T\u0046\u0020\u0066\u006f\u006e\u0074\u0020\u0066i\u006c\u0065\u002e");
};_gggd ,_bcaa :=_gc .ParseFile (_abge );if _bcaa !=nil {_fgg .Log .Debug ("\u0043a\u006e\u006e\u006f\u0074\u0020\u0070\u0061\u0072\u0073\u0065\u0020T\u0054\u0046\u0020\u0066\u0069\u006c\u0065\u0020\u0025\u0073",_bcaa );return _bcaa ;};_ccge ,_bcaa :=_eeg .NewCompositePdfFontFromTTFFile (_abge );
if _bcaa !=nil {return _bcaa ;};_fbbe :=_gggd .GetNameRecords ();for _ ,_bacg :=range _fbbe {_aeeba :=_bacg [1];if _aeeba ==""{continue ;};_ceba :=make ([]byte ,0);for _fabe :=0;_fabe < len (_aeeba );_fabe ++{if _aeeba [_fabe ]==39||_aeeba [_fabe ]==92{continue ;
};_dgee :=4;if _fabe +_dgee < len (_aeeba ){if _aeeba [_fabe :_fabe +_dgee ]=="\u0000"{_fabe =_fabe +_dgee +1;continue ;};};_ceba =append (_ceba ,_aeeba [_fabe ]);};_aeeba =_fc .Replace (string (_ceba ),"\u0078\u0030\u0030","",-1);_geag :=_bacg [2];if _geag ==""{return _dfg .Errorf ("N\u006f\u0020\u0073\u0074\u0079\u006ce\u0020\u0069\u006e\u0066\u006f\u0072m\u0061\u0074\u0069\u006f\u006e\u0020\u0069n\u0020\u0074\u0068\u0065\u0020\u0066\u0069\u006c\u0065\u0020%\u0073",_abge );
};_ceba =make ([]byte ,0);for _ccgb :=0;_ccgb < len (_geag );_ccgb ++{if _geag [_ccgb ]==39||_geag [_ccgb ]==92{continue ;};_ccdf :=4;if _ccgb +_ccdf < len (_geag ){if _geag [_ccgb :_ccgb +_ccdf ]=="\u0000"{_ccgb =_ccgb +_ccdf +1;continue ;};};_ceba =append (_ceba ,_geag [_ccgb ]);
};_geag =_fc .Replace (string (_ceba ),"\u0078\u0030\u0030","",-1);RegisterFont (_aeeba ,_eggb [_geag ],_ccge );};return nil ;};func RegisterFontsFromDirectory (dirName string )error {_abcd ,_ebc :=_fb .Open (dirName );if _ebc !=nil {return _ebc ;};defer _abcd .Close ();
_ggea ,_ebc :=_abcd .Readdirnames (0);if _ebc !=nil {return _ebc ;};for _ ,_gabf :=range _ggea {if _fc .HasSuffix (_gabf ,"\u002e\u0074\u0074\u0066"){_befge :=dirName +"\u002f"+_gabf ;_gadd :=_agb (_befge );if _gadd !=nil {_fgg .Log .Debug ("\u0075\u006ea\u0062\u006c\u0065\u0020\u0074o\u0020\u0070\u0072\u006f\u0063e\u0073\u0073\u0020\u0061\u006e\u0064\u0020\u0072\u0065\u0067\u0069\u0073\u0074\u0065\u0072\u0020\u0066\u006f\u006e\u0074\u0020\u0066\u0072\u006f\u006d\u0020\u0054\u0054\u0046\u0020\u0066\u0069\u006c\u0065\u0020\u0025\u0073",_gadd );
continue ;};};};return nil ;};func _geff (_dfbd *_cf .CT_CatAx )(uint32 ,_cf .ST_AxPos ,_cf .ST_TickMark ,_cf .ST_TickLblPos ,*_cf .CT_ChartLines ,uint32 ,*_dfb .CT_ShapeProperties ,error ){var _efc ,_deg uint32 ;var _ebec _cf .ST_AxPos ;var _fbgg _cf .ST_TickMark ;
var _dge *_cf .CT_ChartLines ;var _gge _cf .ST_TickLblPos ;if _dfbd .AxId ==nil {return _efc ,_ebec ,_fbgg ,_gge ,_dge ,_deg ,_dfbd .SpPr ,_b .New ("\u004e\u006f\u0020x\u0020\u0061\u0078\u0069\u0073\u0020\u0049\u0044");}else {_efc =_dfbd .AxId .ValAttr ;
};if _dfbd .AxPos ==nil {return _efc ,_ebec ,_fbgg ,_gge ,_dge ,_deg ,_dfbd .SpPr ,_b .New ("\u004eo\u0020x\u0020\u0061\u0078\u0069\u0073 \u0070\u006fs\u0069\u0074\u0069\u006f\u006e");}else {_ebec =_dfbd .AxPos .ValAttr ;};if _dfbd .MajorTickMark !=nil {_fbgg =_dfbd .MajorTickMark .ValAttr ;
};if _dfbd .TickLblPos !=nil {_gge =_dfbd .TickLblPos .ValAttr ;};if _dfbd .CrossAx ==nil {return _efc ,_ebec ,_fbgg ,_gge ,_dge ,_deg ,_dfbd .SpPr ,_b .New ("\u004e\u006f \u0063\u0072\u006fs\u0073\u0020\u0061\u0078\u0069\u0073\u0020\u0049\u0044");}else {_deg =_dfbd .CrossAx .ValAttr ;
};_dge =_dfbd .MajorGridlines ;return _efc ,_ebec ,_fbgg ,_gge ,_dge ,_deg ,_dfbd .SpPr ,nil ;};var _cbb =_fgd (1.5);func (_fbb *creatorContext )addCatValue (_ecdf string ,_dgba map[string ]serCategory ,_aeba []string ,_cec int ,_gfcb ,_dfeg *float64 ,_bcbea string ,_bcg *_dfb .CT_ShapeProperties ){_fcb ,_dbaf :=_bd .ParseFloat (_ecdf ,64);
if _dbaf !=nil {_fcb =0;_fgg .Log .Debug ("\u0070a\u0072s\u0065\u0020\u0065\u0072\u0072\u006f\u0072\u003a\u0020\u0025\u0073",_dbaf );};if _fcb > *_dfeg {*_dfeg =_fcb ;};if _fcb < *_gfcb {*_gfcb =_fcb ;};_gdd :=_dgba [_aeba [_cec ]];_gdd ._fcg =append (_gdd ._fcg ,serValue {_bfc :_bcbea ,_dae :_fcb ,_ccg :_bcg });
_dgba [_aeba [_cec ]]=_gdd ;};func DrawLine (c *_bg .Creator ,x0 ,y0 ,x1 ,y1 ,width float64 ,color _bg .Color ){if color ==nil {return ;};_eeed :=c .NewLine (x0 ,y0 ,x1 ,y1 );_eeed .SetLineWidth (width );_eeed .SetColor (color );c .Draw (_eeed );};func _cfegd (_geafa *_fg .Document ,_fdg ,_cbba ,_aag string )error {_agfg ,_beaa :=_geafa .GetFontBytesByRelId (_cbba );
if _beaa !=nil {return _beaa ;};_acbe ,_beaa :=_dged (_aag );if _beaa !=nil {return _beaa ;};for _ebgg :=0;_ebgg < 32;_ebgg ++{_bbcg :=_ebgg %len (_acbe );_agfg [_ebgg ]=_agfg [_ebgg ]^_acbe [_bbcg ];};_dgfc :=_geafa .TmpPath +"\u002f"+_fdg +"\u002e\u0074\u0074\u0066";
_beaa =_fb .WriteFile (_dgfc ,_agfg ,0644);if _beaa !=nil {return _beaa ;};_agb (_dgfc );return nil ;};func PointsFromTwips (twips int64 )float64 {return float64 (int64 (float64 (twips )*_bb .Twips *10+0.5))/10;};func MakeBlockFromChartSpace (cs *_cf .ChartSpace ,width ,height float64 ,theme *_dfb .Theme )(*_bg .Block ,error ){_ceg ,_gga :=_cae (cs ,width ,height ,theme ,false ,nil );
if _gga !=nil {return nil ,_gga ;};_dee ,_gga :=GetPageFromCreator (_ceg );if _gga !=nil {return nil ,_gga ;};_dea ,_gga :=_bg .NewBlockFromPage (_dee );if _gga !=nil {return nil ,_gga ;};return _dea ,nil ;};func IsNoSpaceLanguage (symbol string )bool {for _ ,_cagb :=range symbol {if _d .Is (_d .Han ,_cagb ){return true ;
};};return false ;};func _efd (_aeeed ,_dcfa ,_ead uint8 )(float64 ,float64 ,float64 ){_dedf ,_gcaca ,_dcbg :=float64 (_aeeed )/255,float64 (_dcfa )/255,float64 (_ead )/255;_ggbg :=_dedf ;if _gcaca < _ggbg {_ggbg =_gcaca ;};if _dcbg < _ggbg {_ggbg =_dcbg ;
};var _ccf ,_dcdd bool ;_bdbc :=_dedf ;if _gcaca > _bdbc {_bdbc =_gcaca ;_ccf =true ;};if _dcbg > _bdbc {_bdbc =_dcbg ;_ccf =false ;_dcdd =true ;};_fae :=(_ggbg +_bdbc )/2;var _afbg float64 ;if _ggbg !=_bdbc {if _fae <=0.5{_afbg =(_bdbc -_ggbg )/(_bdbc +_ggbg );
}else {_afbg =(_bdbc -_ggbg )/(2.0-_bdbc -_ggbg );};};var _feggd float64 ;if _ggbg !=_bdbc {if _ccf {_feggd =2.0+(_dcbg -_dedf )/(_bdbc -_ggbg );}else if _dcdd {_feggd =4.0+(_dedf -_gcaca )/(_bdbc -_ggbg );}else {_feggd =(_gcaca -_dcbg )/(_bdbc -_ggbg );
};_feggd *=60;if _feggd < 0{_feggd +=360;};};return _feggd ,_afbg ,_fae ;};func AdjustColorByLumMod (colorStr string ,lum float64 )string {var _edda ,_addd ,_edeb uint8 ;_ddba ,_ :=_dfg .Sscanf (colorStr ,"\u0025\u0030\u0032x\u0025\u0030\u0032\u0078\u0025\u0030\u0032\u0078",&_edda ,&_addd ,&_edeb );
if _ddba !=3{return "";};_dcbfb ,_dffag ,_fcfc :=_efd (_edda ,_addd ,_edeb );_fcfc =lum *_fcfc ;_edda ,_addd ,_edeb =_cfeg (_dcbfb ,_dffag ,_fcfc );return _dfg .Sprintf ("\u0025\u0030\u0032x\u0025\u0030\u0032\u0078\u0025\u0030\u0032\u0078",_edda ,_addd ,_edeb );
};func ClearRegisteredFonts (){_cefc ._cce .Lock ();_cefc ._cafga =map[string ]map[FontStyle ]*_eeg .PdfFont {};_cefc ._cce .Unlock ();};func FromSTCoordinate32 (st _dfb .ST_Coordinate32 )int64 {if _ebf :=st .ST_Coordinate32Unqualified ;_ebf !=nil {return int64 (*_ebf );
};return 0;};var _edae =_fgd (1.5);func AssignStdFontByName (style _bg .TextStyle ,fontName string )*_eeg .PdfFont {_ffe :=_eeg .StdFontName (fontName );return _eeg .NewStandard14FontMustCompile (_ffe );};const (BorderPositionTop BorderPosition =0;BorderPositionLeft BorderPosition =1;
BorderPositionBottom BorderPosition =2;BorderPositionRight BorderPosition =3;);func MakeBlockFromCreator (c *_bg .Creator )(*_bg .Block ,error ){_fecf ,_ggdc :=GetPageFromCreator (c );if _ggdc !=nil {return nil ,_ggdc ;};_effaa ,_ggdc :=_bg .NewBlockFromPage (_fecf );
if _ggdc !=nil {return nil ,_ggdc ;};return _effaa ,nil ;};func (_aabb *creatorContext )drawRectangleWithProps (_edef *_dfb .CT_ShapeProperties ,_ggagc ,_caaf ,_dbe ,_aacb float64 ,_debb bool ){_faab :=_aabb ._gafe .NewRectangle (_ggagc ,_caaf ,_dbe ,_aacb );
if _edef ==nil {if _debb {_faab .SetBorderWidth (_bgc );}else {return ;};}else {_cbe :=_aabb .getPdfColorFromSolidFill (_edef .FillPropertiesChoice .SolidFill );if _cbe !=nil {_faab .SetFillColor (_cbe );};if _gdagb :=_edef .Ln ;_gdagb !=nil {if _geagg :=_gdagb .WAttr ;
_geagg !=nil {_acad :=_bb .FromEMU (int64 (*_geagg ));_faab .SetBorderWidth (_acad );if _aaace :=_gdagb .LineFillPropertiesChoice .SolidFill ;_aaace !=nil {_deef :=_aabb .getPdfColorFromSolidFill (_aaace );if _deef !=nil {_faab .SetBorderColor (_deef );
};};}else {_faab .SetBorderWidth (0);};};};_aabb ._gafe .Draw (_faab );};var _fgfg =_fgd (1.5);var _bag =_fgd (5);type serCategory struct{_ded string ;_fcg []serValue ;};func LoadFontFromFile (filename string )(*_eeg .PdfFont ,error ){if !_fc .HasSuffix (filename ,"\u002e\u0074\u0074\u0066"){_fgg .Log .Debug ("\u0055\u006es\u0075\u0070\u0070\u006f\u0072\u0074\u0065\u0064\u0020\u0066\u006f\u006e\u0074\u0020\u0066\u0069\u006c\u0065\u0020\u0066\u006f\u0072ma\u0074\u002e");
return nil ,_dfg .Errorf ("\u0055\u006e\u0073\u0075\u0070\u0070o\u0072\u0074\u0065\u0064\u0020f\u006f\u006e\u0074\u0020\u0066\u0069l\u0065\u0020\u0066\u006f\u0072m\u0061\u0074\u002c\u0020\u0063\u0075\u0072\u0072\u0065\u006e\u0074\u006cy\u0020\u006f\u006e\u006c\u0079\u0020\u0073\u0075\u0070\u0070\u006f\u0072\u0074\u0020\u0054T\u0046\u0020\u0066\u006f\u006e\u0074\u0020\u0066i\u006c\u0065\u002e");
};_gafg ,_bfa :=_eeg .NewCompositePdfFontFromTTFFile (filename );if _bfa !=nil {return nil ,_bfa ;};return _gafg ,nil ;};type barSerByOrder []*_cf .CT_BarSer ;func CropImageByRect (sourceImg _db .Image ,rect _db .Rectangle )_db .Image {_acgd ,_aeedd ,_bee ,_ggef :=rect .Min .X ,rect .Min .Y ,rect .Max .X ,rect .Max .Y ;
_fdbd :=_db .NewNRGBA (_db .Rect (0,0,_bee -_acgd ,_ggef -_aeedd ));for _dbcg :=_acgd ;_dbcg < _bee ;_dbcg ++{for _gace :=_aeedd ;_gace < _ggef ;_gace ++{_fdbd .Set (_dbcg -_acgd ,_gace -_aeedd ,sourceImg .At (_dbcg ,_gace ));};};return _fdbd ;};var _ddf =_fgd (2);
func FillRectangle (c *_bg .Creator ,x ,y ,width ,height float64 ,color _bg .Color ){if color ==nil {return ;};_aagee :=c .NewRectangle (x ,y ,width ,height );_aagee .SetFillColor (color );_aagee .SetBorderWidth (0);c .Draw (_aagee );};const (ImgPart_whole ImgPart =0;
ImgPart_t ImgPart =1;ImgPart_b ImgPart =2;ImgPart_l ImgPart =3;ImgPart_r ImgPart =4;ImgPart_lt ImgPart =5;ImgPart_rt ImgPart =6;ImgPart_lb ImgPart =7;ImgPart_rb ImgPart =8;);func (_dgbc *creatorContext )drawPieChart (_ccab *_cf .CT_PieChart ,_cdad *Rectangle ,_fed *_ad .Workbook ,_dfcg float64 )([]*legendItem ,error ){_dbga :=[]*legendItem {};
_ccbd :=map[string ]serCategory {};_gaff :=[]string {};_efgb :=_e .Inf (1);_aedg :=_e .Inf (-1);_fcda :=_ccab .Ser ;for _ ,_fdbe :=range _fcda {var _dfab string ;if _beac :=_fdbe .Tx ;_beac !=nil {if _cgc :=_beac .SerTxChoice ;_cgc !=nil {if _cgc .V !=nil {_dfab =*_cgc .V ;
}else if _ece :=_cgc .StrRef ;_ece !=nil {if _ebgb :=_ece .StrCache ;_ebgb !=nil {for _ ,_dcef :=range _ebgb .Pt {_dfab =_dcef .V ;};};};};};if _agba :=_fdbe .Cat ;_agba !=nil {if _cecb :=_agba .AxDataSourceChoice ;_cecb !=nil {if _gfcf :=_cecb .StrRef ;
_gfcf !=nil {if _abd :=_gfcf .F ;_abd !=""&&_fed !=nil {_ccfa ,_cfga ,_age ,_gcae :=ParseExcelRange (_abd );if _gcae ==nil {for _ ,_cdd :=range _fed .Sheets (){if _cdd .Name ()==_fc .Trim (_ccfa ,"\u0027"){_ecec :=_cfga .String ();for _defb :=_cfga .RowIdx ;
_defb <=_age .RowIdx ;_defb +=1{_feee :=_fc .ReplaceAll (_ecec ,_dfg .Sprint (_cfga .RowIdx ),_dfg .Sprint (_defb ));_dgbc .addCatName (_cdd .Cell (_feee ).GetString (),_ccbd ,&_gaff );};break ;};};};};if _eefe :=_gfcf .StrCache ;_eefe !=nil {for _ ,_fggc :=range _eefe .Pt {_dgbc .addCatName (_fggc .V ,_ccbd ,&_gaff );
};};}else if _adbb :=_cecb .NumRef ;_adbb !=nil {if _ebga :=_adbb .NumCache ;_ebga !=nil {var _ddg string ;if _ebga .FormatCode !=nil {_ddg =*_ebga .FormatCode ;};for _ ,_fgfa :=range _ebga .Pt {var _gfac string ;if _fgfa .FormatCodeAttr ==nil {_gfac =_ddg ;
}else {_gfac =*_fgfa .FormatCodeAttr ;};var _ccgcb string ;_bba ,_dffe :=_bd .ParseFloat (_fgfa .V ,64);if _dffe !=nil {_ccgcb =_fgfa .V ;}else {_ccgcb =_ab .Number (_bba ,_gfac );};_dgbc .addCatName (_ccgcb ,_ccbd ,&_gaff );};};};};};if _agbc :=_fdbe .Val ;
_agbc !=nil {if _dgdb :=_agbc .NumDataSourceChoice ;_dgdb !=nil {if _ccga :=_dgdb .NumRef ;_ccga !=nil {if _bcaf :=_ccga .F ;_bcaf !=""&&_fed !=nil {_efa ,_ffec ,_dgfd ,_addf :=ParseExcelRange (_bcaf );if _addf ==nil {for _ ,_fafe :=range _fed .Sheets (){if _fafe .Name ()==_fc .Trim (_efa ,"\u0027"){_fbdg :=_ffec .String ();
for _cebe :=_ffec .RowIdx ;_cebe <=_dgfd .RowIdx ;_cebe +=1{_acac :=_fc .ReplaceAll (_fbdg ,_dfg .Sprint (_ffec .RowIdx ),_dfg .Sprint (_cebe ));if _fafe .FormulaContext ().HasFormula (_acac ){_bdce :=_fe .NewEvaluator ();_ecf :=_fafe .FormulaContext ().Cell (_acac ,_bdce );
_ffab :=_ecf .Value ();_dgbc .addCatValue (_ffab ,_ccbd ,_gaff ,(int )(_cebe -_ffec .RowIdx ),&_efgb ,&_aedg ,_dfab ,_fdbe .SpPr );}else {_eebda :=_fafe .Cell (_acac ).GetString ();_dgbc .addCatValue (_eebda ,_ccbd ,_gaff ,(int )(_cebe -_ffec .RowIdx ),&_efgb ,&_aedg ,_dfab ,_fdbe .SpPr );
};};break ;};};};};if _abce :=_ccga .NumCache ;_abce !=nil {for _efcd ,_fbdgg :=range _abce .Pt {_dgbc .addCatValue (_fbdgg .V ,_ccbd ,_gaff ,_efcd ,&_efgb ,&_aedg ,_dfab ,_fdbe .SpPr );};};};};};};_eegg :=&_ed .PieChart {Values :[]_adc .Value {}};for _ ,_feggg :=range _gaff {_adbf :=_ccbd [_feggg ];
if len (_adbf ._fcg )> 0{_eegg .Values =append (_eegg .Values ,_adc .Value {Value :_adbf ._fcg [0]._dae ,Label :_feggg ,Style :_eda .Style {FontSize :12*_dfcg }});};};_eegg .SetHeight (int (_cdad .Top -_cdad .Bottom ));_eegg .SetWidth (int (_cdad .Right -_cdad .Left ));
_ebca :=_dgbc ._gafe ;_bcbbg :=_bg .NewChart (_eegg );_bcbbg .SetPos (_cdad .Left ,_cdad .Top );_ccad :=_ebca .Draw (_bcbbg );if _ccad !=nil {return nil ,_ccad ;};return _dbga ,nil ;};type ImgPart byte ;var _ged =_fgd (0.5);func (_fda *creatorContext )drawTitle (_ggaa string ,_bab ,_ffg float64 ){_cca :=_fda ._gccb ;
_cac :=_fda ._gafe .NewStyledParagraph ();_dfeb :=_cac .SetText (_ggaa );_bdb ,_fcbg :=_eeg .NewStandard14Font (_eeg .HelveticaName );if _fcbg ==nil {_bdbe :=_fda ._gafe .NewTextStyle ();_bdbe .Font =_bdb ;_bdbe .FontSize =14;_bdbe .TextRise =0.4;_dfeb .Style =_bdbe ;
_cac .SetPos (_bab -_cac .Width ()*_cca /2,_ffg );_fda ._gafe .Draw (_cac );};};func (_dff *creatorContext )drawAreaChart (_ba *_cf .CT_AreaChart ,_ae *Rectangle ,_fa []*_cf .CT_PlotAreaChoice1 ,_bf float64 )([]*legendItem ,error ){_fbg :=[]*legendItem {};
_gf :=_ba .Ser ;_ff :=make (map[int ][]float64 );_ga :=make (map[int ][]float64 );_gd :=make (map[int ]_bg .Color );for _cfg ,_bbc :=range _gf {_gd [_cfg ]=_bg .ColorWhite ;if _ce :=_bbc .SpPr ;_ce !=nil {_fcc :=_dff .getPdfColorFromSolidFill (_ce .FillPropertiesChoice .SolidFill );
if _fcc !=nil {_gd [_cfg ]=_fcc ;};};if _aa :=_bbc .Val ;_aa !=nil {if _aad :=_aa .NumDataSourceChoice ;_aad !=nil {if _feb :=_aad .NumRef ;_feb !=nil {if _eed :=_feb .NumCache ;_eed !=nil {for _ ,_bef :=range _eed .Pt {_ff [_cfg ]=append (_ff [_cfg ],float64 (_bef .IdxAttr ));
_dg ,_ :=_bd .ParseFloat (_fc .TrimSpace (_bef .V ),64);_ga [_cfg ]=append (_ga [_cfg ],_dg );};};};};};};_fgb :=true ;_ca :="";_da :="";if len (_fa )> 1{if _fa [0].ValAx !=nil &&_fa [1].ValAx !=nil {if _fa [0].ValAx .Delete !=nil &&_fa [1].ValAx .Delete !=nil {if !*(*_fa [0].ValAx .Delete ).ValAttr &&!*(*_fa [1].ValAx .Delete ).ValAttr {_fgb =false ;
};};_ca =_dff .getTitle (_fa [0].ValAx .Title );_da =_dff .getTitle (_fa [1].ValAx .Title );};};_bc :=_eda .Style {Hidden :_fgb ,FontSize :8*_bf };_fga :=&_ed .Chart {Series :[]_adc .Series {},YAxisSecondary :_ed .YAxis {Style :_bc ,Name :_da ,NameStyle :_bc },XAxis :_ed .XAxis {Style :_bc ,Name :_ca ,NameStyle :_bc },YAxis :_ed .YAxis {Style :_eda .Style {Hidden :true }}};
for _gfg :=0;_gfg < len (_gd );_gfg ++{_fbf ,_dbf ,_dfe :=_gd [_gfg ].ToRGB ();_ec :=_adc .ContinuousSeries {XValues :_ff [_gfg ],YValues :_ga [_gfg ],Style :_eda .Style {StrokeWidth :1,FillColor :_g .RGBA {uint8 (_fbf *255),uint8 (_dbf *255),uint8 (_dfe *255),255}}};
_fga .Series =append (_fga .Series ,_ec );};_fga .SetHeight (int (_e .Abs (_ae .Top -_ae .Bottom )));_fga .SetWidth (int (_e .Abs (_ae .Right -_ae .Left )));_gg :=_dff ._gafe ;_ffd :=_bg .NewChart (_fga );_ffd .SetPos (_ae .Left ,_ae .Top );_fbe :=_gg .Draw (_ffd );
if _fbe !=nil {return nil ,_fbe ;};return _fbg ,nil ;};func _fgd (_fcfa float64 )float64 {return _fcfa *_bb .Millimeter };func (_baac *creatorContext )drawScatterChart (_gaea *_cf .CT_ScatterChart ,_dcec *Rectangle ,_bgga []*_cf .CT_PlotAreaChoice1 ,_abec float64 )([]*legendItem ,error ){_gdbc :=[]*legendItem {};
_cecf :=_gaea .Ser ;_ddga :=make (map[string ]float64 );_dbda :=make (map[string ][]float64 );_cdf :=make (map[string ][]float64 );_bedg :=[]string {};for _ ,_adbba :=range _cecf {var _bbad string ;if _ddbc :=_adbba .Tx ;_ddbc !=nil {if _ddbe :=_ddbc .SerTxChoice ;
_ddbe !=nil {if _ddbe .V !=nil {_bbad =*_ddbe .V ;}else if _ffbc :=_ddbe .StrRef ;_ffbc !=nil {if _ddef :=_ffbc .StrCache ;_ddef !=nil {for _ ,_bbbac :=range _ddef .Pt {_bbad =_bbbac .V ;};};};_bedg =append (_bedg ,_bbad );};};if _adbba .Marker !=nil &&_adbba .Marker .Size !=nil {_ddga [_bbad ]=float64 (*_adbba .Marker .Size .ValAttr );
}else {_ddga [_bbad ]=-1.0;};if _cfbf :=_adbba .XVal ;_cfbf !=nil {if _fedd :=_cfbf .AxDataSourceChoice ;_fedd !=nil {if _effa :=_fedd .NumRef ;_effa !=nil {if _efac :=_effa .NumCache ;_efac !=nil {for _ ,_cegb :=range _efac .Pt {_dagd ,_ :=_bd .ParseFloat (_fc .TrimSpace (_cegb .V ),64);
_dbda [_bbad ]=append (_dbda [_bbad ],_dagd );};};};};};if _adea :=_adbba .YVal ;_adea !=nil {if _eefg :=_adea .NumDataSourceChoice ;_eefg !=nil {if _bcafa :=_eefg .NumRef ;_bcafa !=nil {if _eacb :=_bcafa .NumCache ;_eacb !=nil {for _ ,_aedd :=range _eacb .Pt {_bebb ,_ :=_bd .ParseFloat (_fc .TrimSpace (_aedd .V ),64);
_cdf [_bbad ]=append (_cdf [_bbad ],_bebb );};};};};};};_eabff :=true ;_abb :="";_adcc :="";if len (_bgga )> 1{if _bgga [0].ValAx .Delete !=nil &&_bgga [1].ValAx .Delete !=nil {if !*(*_bgga [0].ValAx .Delete ).ValAttr &&!*(*_bgga [1].ValAx .Delete ).ValAttr {_eabff =false ;
};};_abb =_baac .getTitle (_bgga [0].ValAx .Title );_adcc =_baac .getTitle (_bgga [1].ValAx .Title );};_ecfd :=_eda .Style {Hidden :_eabff ,FontSize :8*_abec };_bbcd :=&_ed .Chart {Series :[]_adc .Series {},YAxisSecondary :_ed .YAxis {Style :_ecfd ,Name :_adcc ,NameStyle :_ecfd },XAxis :_ed .XAxis {Style :_ecfd ,Name :_abb ,NameStyle :_ecfd },YAxis :_ed .YAxis {Style :_eda .Style {Hidden :true }}};
for _ ,_bbg :=range _bedg {_gece :=_adc .ContinuousSeries {XValues :_dbda [_bbg ],YValues :_cdf [_bbg ],Style :_eda .Style {StrokeWidth :-1,DotWidth :_ddga [_bbg ]}};_bbcd .Series =append (_bbcd .Series ,_gece );};_bbcd .SetHeight (int (_e .Abs (_dcec .Top -_dcec .Bottom )));
_bbcd .SetWidth (int (_e .Abs (_dcec .Right -_dcec .Left )));_bdda :=_baac ._gafe ;_eag :=_bg .NewChart (_bbcd );_eag .SetPos (_dcec .Left ,_dcec .Top );_dcbb :=_bdda .Draw (_eag );if _dcbb !=nil {return nil ,_dcbb ;};return _gdbc ,nil ;};func Lighten (clr float64 )float64 {return 0.6+0.4*clr };
func FromSTCoordinate (st _dfb .ST_Coordinate )int64 {if _cdfd :=st .ST_CoordinateUnqualified ;_cdfd !=nil {return *_cdfd ;};return 0;};func FromSTPercentage (st *_dfb .ST_Percentage )float64 {if _fdee :=st .ST_PercentageDecimal ;_fdee !=nil {return float64 (*_fdee )/Percent100 ;
};return 0;};func GetDefaultPageSize ()_bg .PageSize {return _bcce [DefaultPageSize ]};func _dged (_bddd string )([]byte ,error ){_bddf :=_f .MustCompile ("\u005b\u005e\u0061\u002d\u007a\u0041\u002d\u005a\u0030\u002d\u0039\u005d\u002b");_bddd =_bddf .ReplaceAllString (_bddd ,"");
_geg :=[]rune (_bddd );_gcc :=[]byte {};for _cbac :=len (_geg )-2;_cbac >=0;_cbac -=2{_dbgb ,_aacc :=_bd .ParseUint (string (_geg [_cbac ])+string (_geg [_cbac +1]),16,8);if _aacc !=nil {return nil ,_aacc ;};_gcc =append (_gcc ,byte (_dbgb ));};return _gcc ,nil ;
};func (_fca barSerByOrder )Less (i ,j int )bool {return _fca [i ].Order .ValAttr < _fca [j ].Order .ValAttr };func ParseExcelRange (s string )(string ,_bbb .CellReference ,_bbb .CellReference ,error ){_fbab :=_fc .FieldsFunc (s ,func (_dgac rune )bool {return _dgac ==':'||_dgac =='!'});
if len (_fbab )==3{_cffc ,_cga :=_bbb .ParseCellReference (_fbab [1]);if _cga !=nil {return "",_bbb .CellReference {},_bbb .CellReference {},_cga ;};_febe ,_cga :=_bbb .ParseCellReference (_fbab [2]);if _cga !=nil {return "",_bbb .CellReference {},_bbb .CellReference {},_cga ;
};return _fbab [0],_cffc ,_febe ,nil ;};return "",_bbb .CellReference {},_bbb .CellReference {},_b .New ("\u0065r\u0072\u006f\u0072\u0020p\u0061\u0072\u0073\u0069\u006eg\u0020e\u0078c\u0065\u006c\u0020\u0072\u0061\u006e\u0067e");};func RegisterFont (name string ,style FontStyle ,font *_eeg .PdfFont ){_cefc ._cce .Lock ();
if _cefc ._cafga [name ]==nil {_cefc ._cafga [name ]=map[FontStyle ]*_eeg .PdfFont {};};_cefc ._cafga [name ][style ]=font ;_cefc ._cce .Unlock ();};func MakeTempCreatorMaxHeight (width float64 )*_bg .Creator {return MakeTempCreator (width ,_e .MaxFloat64 );
};func (_aea *creatorContext )drawBarChart (_ffdb *_cf .CT_BarChart ,_fbc *Rectangle ,_fab []*_cf .CT_PlotAreaChoice1 ,_fba *_ad .Workbook )([]*legendItem ,error ){var _ggc bool ;if _fbfg :=_ffdb .BarDir ;_fbfg !=nil {_ggc =_fbfg .ValAttr ==_cf .ST_BarDirBar ;
};_gb :=_ffdb .Ser ;_ge .Sort (barSerByOrder (_gb ));_fbfa :=map[string ]serCategory {};_dgf :=[]string {};_ac :=[]*legendItem {};_eef :=_e .Inf (1);_bfg :=_e .Inf (-1);for _ ,_fgf :=range _gb {var _dffa string ;if _dc :=_fgf .Tx ;_dc !=nil {if _dgfa :=_dc .SerTxChoice ;
_dgfa !=nil {if _dgfa .V !=nil {_dffa =*_dgfa .V ;}else if _aee :=_dgfa .StrRef ;_aee !=nil {if _fcd :=_aee .StrCache ;_fcd !=nil {for _ ,_fge :=range _fcd .Pt {_dffa =_fge .V ;};};};};};if _ag :=_fgf .Cat ;_ag !=nil {if _dfbb :=_ag .AxDataSourceChoice ;
_dfbb !=nil {if _bgd :=_dfbb .StrRef ;_bgd !=nil {if _bbd :=_bgd .F ;_bbd !=""&&_fba !=nil {_dcc ,_gad ,_bde ,_cc :=ParseExcelRange (_bbd );if _cc ==nil {for _ ,_fd :=range _fba .Sheets (){if _fd .Name ()==_fc .Trim (_dcc ,"\u0027"){_bgg :=_gad .String ();
for _aeee :=_gad .RowIdx ;_aeee <=_bde .RowIdx ;_aeee +=1{_cg :=_fc .ReplaceAll (_bgg ,_dfg .Sprint (_gad .RowIdx ),_dfg .Sprint (_aeee ));_aea .addCatName (_fd .Cell (_cg ).GetString (),_fbfa ,&_dgf );};break ;};};};};if _gdb :=_bgd .StrCache ;_gdb !=nil {for _ ,_ef :=range _gdb .Pt {_aea .addCatName (_ef .V ,_fbfa ,&_dgf );
};};}else if _eg :=_dfbb .NumRef ;_eg !=nil {if _bff :=_eg .NumCache ;_bff !=nil {var _aada string ;if _bff .FormatCode !=nil {_aada =*_bff .FormatCode ;};for _ ,_dce :=range _bff .Pt {var _fec string ;if _dce .FormatCodeAttr ==nil {_fec =_aada ;}else {_fec =*_dce .FormatCodeAttr ;
};var _acc string ;_dgb ,_bge :=_bd .ParseFloat (_dce .V ,64);if _bge !=nil {_acc =_dce .V ;}else {_acc =_ab .Number (_dgb ,_fec );};_aea .addCatName (_acc ,_fbfa ,&_dgf );};};};};};if _eeb :=_fgf .Val ;_eeb !=nil {if _fea :=_eeb .NumDataSourceChoice ;
_fea !=nil {if _bbcb :=_fea .NumRef ;_bbcb !=nil {if _dd :=_bbcb .F ;_dd !=""&&_fba !=nil {_cab ,_ade ,_gde ,_fcdb :=ParseExcelRange (_dd );if _fcdb ==nil {for _ ,_eec :=range _fba .Sheets (){if _eec .Name ()==_fc .Trim (_cab ,"\u0027"){_ggb :=_ade .String ();
for _adb :=_ade .RowIdx ;_adb <=_gde .RowIdx ;_adb +=1{_feg :=_fc .ReplaceAll (_ggb ,_dfg .Sprint (_ade .RowIdx ),_dfg .Sprint (_adb ));if _eec .FormulaContext ().HasFormula (_feg ){_adef :=_fe .NewEvaluator ();_gaa :=_eec .FormulaContext ().Cell (_feg ,_adef );
_gcd :=_gaa .Value ();_aea .addCatValue (_gcd ,_fbfa ,_dgf ,(int )(_adb -_ade .RowIdx ),&_eef ,&_bfg ,_dffa ,_fgf .SpPr );}else {_bdg :=_eec .Cell (_feg ).GetString ();_aea .addCatValue (_bdg ,_fbfa ,_dgf ,(int )(_adb -_ade .RowIdx ),&_eef ,&_bfg ,_dffa ,_fgf .SpPr );
};};break ;};};};};if _bgf :=_bbcb .NumCache ;_bgf !=nil {for _gce ,_egd :=range _bgf .Pt {_aea .addCatValue (_egd .V ,_fbfa ,_dgf ,_gce ,&_eef ,&_bfg ,_dffa ,_fgf .SpPr );};};};};};_ac =append (_ac ,&legendItem {_ace :_dffa ,_cabe :_fgf .SpPr });};var _ggbb float64 ;
var _egg ,_dcf float64 ;if _bfg ==0&&_eef ==0{_ggbb =0.2;_dcf =0;_egg =1;}else {var _caf float64 ;if _gcf :=_e .Abs (_eef );_bfg < _gcf {_caf =_gcf ;}else {_caf =_bfg ;};_fac :=_e .Pow (10,_e .Floor (_e .Log10 (_caf )));_gab :=_caf /_fac ;if _gab < 4.29{_ggbb =0.5;
}else if _gab >=4.29&&_gab < 8.58{_ggbb =1;}else {_ggbb =2;};_ggbb *=_fac ;if _bfg <=0{_egg =0;}else {_egg =(_e .Ceil (_bfg /_ggbb )+1)*_ggbb ;};if _eef >=0{_dcf =0;}else {_dcf =(_e .Floor (_eef /_ggbb )-1)*_ggbb ;};};_gda :=_aea .drawAxes (_fab ,_dcf ,_egg ,_ggbb ,_dgf ,_fbc ,_ggc );
if _gda !=nil {return nil ,_gda ;};_gfe :=0.0;if _ffdb .GapWidth !=nil {if _ceb :=_ffdb .GapWidth .ValAttr ;_ceb !=nil {if _ffa :=_ceb .ST_GapAmountUShort ;_ffa !=nil {_gfe =float64 (*_ffa )/100.0;};};};_edc :=_fbc .Right -_fbc .Left ;_fbeb :=_fbc .Bottom -_fbc .Top ;
_gbd :=float64 (len (_dgf ));if _ggc {_fcf :=_egg /(_egg -_dcf )*_edc ;_aeb :=-_dcf /(_egg -_dcf )*_edc ;_bdgd :=_fbc .Left +_aeb ;_ebd :=_fbeb /_gbd ;for _fggf ,_gade :=range _dgf {_dad :=_fbfa [_gade ];_beg :=float64 (len (_dad ._fcg ))+_gfe ;_baa :=_ebd /_beg ;
_aeeb :=_baa *_gfe ;_de :=_fbc .Bottom -float64 (_fggf )*_ebd -_aeeb /2-_baa ;for _ ,_abg :=range _dad ._fcg {if _abg ._dae ==0{continue ;};var _caa ,_eab float64 ;if _abg ._dae > 0{_eab =_abg ._dae /_egg *_fcf ;_aea .drawRectangleWithProps (_abg ._ccg ,_bdgd ,_de ,_eab ,_baa ,false );
}else {_eab =_abg ._dae /_dcf *_aeb ;_caa =_bdgd -_eab ;_aea .drawRectangleWithProps (_abg ._ccg ,_caa ,_de ,_eab ,_baa ,false );};_de -=_baa ;};};}else {_gfc :=_egg /(_egg -_dcf )*_fbeb ;_gfb :=-_dcf /(_egg -_dcf )*_fbeb ;_bcb :=_fbc .Top +_gfc ;_acd :=_edc /_gbd ;
for _baf ,_acb :=range _dgf {_cge :=_fbfa [_acb ];_cebf :=float64 (len (_cge ._fcg ))+_gfe ;_adf :=_acd /_cebf ;_dbd :=_adf *_gfe ;_eff :=_fbc .Left +float64 (_baf )*_acd +_dbd /2;for _ ,_dde :=range _cge ._fcg {var _dgd ,_cgd float64 ;if _dde ._dae > 0{_cgd =_dde ._dae /_egg *_gfc ;
_dgd =_bcb -_cgd ;_aea .drawRectangleWithProps (_dde ._ccg ,_eff ,_dgd ,_adf ,_cgd ,false );}else {_cgd =_dde ._dae /_dcf *_gfb ;_aea .drawRectangleWithProps (_dde ._ccg ,_eff ,_bcb ,_adf ,_cgd ,false );};_eff +=_adf ;};};};return _ac ,nil ;};var _bgc =_fgd (0.125);
func (_eea *Rectangle )Translate (x ,y float64 ){_eea .Left +=x ;_eea .Right +=x ;_eea .Top +=y ;_eea .Bottom +=y ;};func GetPageFromCreator (c *_bg .Creator )(*_eeg .PdfPage ,error ){_abga :=_c .NewBuffer ([]byte {});_fdgd :=c .Write (_abga );if _fdgd !=nil {return nil ,_fdgd ;
};_bgbe :=_c .NewReader (_abga .Bytes ());_ffea ,_fdgd :=_eeg .NewPdfReader (_bgbe );if _fdgd !=nil {return nil ,_fdgd ;};return _ffea .GetPage (1);};func (_bcgf *Rectangle )scale (_eaa float64 ){_bcgf .Top *=_eaa ;_bcgf .Bottom *=_eaa ;_bcgf .Left *=_eaa ;
_bcgf .Right *=_eaa ;};type PageSize int ;func (_bebc *creatorContext )getPdfColorFromSolidFill (_eeag *_dfb .CT_SolidColorFillProperties )_bg .Color {if _eeag ==nil {return nil ;};_gbbb :="";if _gbc :=_eeag .SrgbClr ;_gbc !=nil {_gbbb =_gbc .ValAttr ;
}else if _fcce :=_eeag .SchemeClr ;_fcce !=nil {_gbbb =_ccbg (_fcce .ValAttr ,_bebc ._cegc );_gbbb =AdjustColor (_gbbb ,_fcce .EG_ColorTransform );};if _gbbb ==""{return nil ;};return _bg .ColorRGBFromHex ("\u0023"+_gbbb );};const _fad =6.0;var RtlFontFile *_eeg .PdfFont ;
const (FontStyle_Regular FontStyle =0;FontStyle_Bold FontStyle =1;FontStyle_Italic FontStyle =2;FontStyle_BoldItalic FontStyle =3;);func GetRegisteredFont (name string ,style FontStyle )*_eeg .PdfFont {_cefc ._cce .Lock ();defer _cefc ._cce .Unlock ();
if _caed ,_aed :=_cefc ._cafga [name ];_aed {if _fccd ,_bfeb :=_caed [style ];_bfeb {return _fccd ;};};return nil ;};func GetGroupOffsetFromXfrm (xfrm *_dfb .CT_GroupTransform2D )(float64 ,float64 ){var _deefe ,_agcf float64 ;if xfrm .Off !=nil &&xfrm .ChOff !=nil {_deefe =_bb .FromEMU (FromSTCoordinate (xfrm .Off .XAttr ))-_bb .FromEMU (FromSTCoordinate (xfrm .ChOff .XAttr ));
_agcf =_bb .FromEMU (FromSTCoordinate (xfrm .Off .YAttr ))-_bb .FromEMU (FromSTCoordinate (xfrm .ChOff .YAttr ));};return _deefe ,_agcf ;};func _bcc (_bec _dfb .ST_SchemeColorVal ,_gee *_dfb .Theme )string {if _bbff :=_gee .ThemeElements ;_bbff !=nil {if _gdae :=_bbff .ClrScheme ;
_gdae !=nil {switch _bec {case _dfb .ST_SchemeColorValLt1 :return GetColorStringFromDmlColor (_gdae .Lt1 );case _dfb .ST_SchemeColorValDk1 ,_dfb .ST_SchemeColorValTx1 :return GetColorStringFromDmlColor (_gdae .Dk1 );case _dfb .ST_SchemeColorValLt2 :return GetColorStringFromDmlColor (_gdae .Lt2 );
case _dfb .ST_SchemeColorValDk2 :return GetColorStringFromDmlColor (_gdae .Dk2 );case _dfb .ST_SchemeColorValAccent1 :return GetColorStringFromDmlColor (_gdae .Accent1 );case _dfb .ST_SchemeColorValAccent2 :return GetColorStringFromDmlColor (_gdae .Accent2 );
case _dfb .ST_SchemeColorValAccent3 :return GetColorStringFromDmlColor (_gdae .Accent3 );case _dfb .ST_SchemeColorValAccent4 :return GetColorStringFromDmlColor (_gdae .Accent4 );case _dfb .ST_SchemeColorValAccent5 :return GetColorStringFromDmlColor (_gdae .Accent5 );
case _dfb .ST_SchemeColorValAccent6 :return GetColorStringFromDmlColor (_gdae .Accent6 );};};};return "";};func AdjustColorByLumOff (colorStr string ,lumOff float64 )string {var _baca ,_cbbb ,_aec uint8 ;_dcee ,_ :=_dfg .Sscanf (colorStr ,"\u0025\u0030\u0032x\u0025\u0030\u0032\u0078\u0025\u0030\u0032\u0078",&_baca ,&_cbbb ,&_aec );
if _dcee !=3{return "";};_ggag ,_geb ,_daac :=_efd (_baca ,_cbbb ,_aec );_daac =_daac +lumOff ;_baca ,_cbbb ,_aec =_cfeg (_ggag ,_geb ,_daac );return _dfg .Sprintf ("\u0025\u0030\u0032x\u0025\u0030\u0032\u0078\u0025\u0030\u0032\u0078",_baca ,_cbbb ,_aec );
};func (_gbbga *creatorContext )drawBorderWithProps (_aef *_dfb .CT_ShapeProperties ,_cdee *Rectangle ,_cfd float64 ){if _cdee !=nil &&_aef !=nil &&_aef .Ln !=nil &&_aef .Ln .LineFillPropertiesChoice .SolidFill !=nil {_gdgfc :=_gbbga .getPdfColorFromSolidFill (_aef .Ln .LineFillPropertiesChoice .SolidFill );
DrawRectangle (_gbbga ._gafe ,_cdee ,_cfd ,_gdgfc );};};var _cefc =fontsMap {_cce :&_df .Mutex {},_cafga :map[string ]map[FontStyle ]*_eeg .PdfFont {}};func (_cb *creatorContext )addCatName (_ffaa string ,_bcbe map[string ]serCategory ,_ecd *[]string ){if _ ,_eabf :=_bcbe [_ffaa ];
!_eabf {_bcbe [_ffaa ]=serCategory {_ded :_ffaa ,_fcg :[]serValue {}};*_ecd =append (*_ecd ,_ffaa );};};const (PageSizeA4 PageSize =iota ;PageSizeA3 ;PageSizeA5 ;PageSizeLetter ;PageSizeLegal ;Percent100 =100000.0;);func _eabg (_gdbf ,_gbbfd ,_ebg float64 )float64 {if _gdbf *6< 1{return _ebg +(_gbbfd -_ebg )*6*_gdbf ;
}else if _gdbf *2< 1{return _gbbfd ;}else if _gdbf *3< 2{return _ebg +(_gbbfd -_ebg )*(2.0/3.0-_gdbf )*6;}else {return _ebg ;};};func (_fbga *creatorContext )drawAxes (_cafd []*_cf .CT_PlotAreaChoice1 ,_cgeb ,_dfba ,_eggf float64 ,_gbbg []string ,_eac *Rectangle ,_fcff bool )error {_dcfe :=_fbga ._gafe ;
_fabc :=_fbga ._gccb ;if _cafd ==nil {return _b .New ("\u004e\u006f\u0020\u0061xi\u0073\u0020\u0069\u006e\u0066\u006f\u0072\u006d\u0061\u0074\u0069\u006f\u006e");};var (_fcfd *_cf .CT_ValAx ;_dbag *_cf .CT_CatAx ;_ggg *_cf .CT_DateAx ;_eabe *_cf .CT_SerAx ;
);for _ ,_cdb :=range _cafd {if _cdb .ValAx !=nil {_fcfd =_cdb .ValAx ;}else if _cdb .CatAx !=nil {_dbag =_cdb .CatAx ;}else if _cdb .DateAx !=nil {_ggg =_cdb .DateAx ;}else if _cdb .SerAx !=nil {_eabe =_cdb .SerAx ;};};if _fcfd ==nil ||(_dbag ==nil &&_ggg ==nil &&_eabe ==nil ){return _b .New ("\u004e\u006f\u0020\u0078\u0020\u006f\u0072\u0020\u0079 \u0061\u0078\u0069\u0073");
};var _dgce ,_cgg ,_cbgc ,_abc uint32 ;var _bded ,_gfcbg _cf .ST_AxPos ;var _cfgd ,_gdc _cf .ST_TickMark ;var _fee ,_bdge *_cf .CT_ChartLines ;var _fagb ,_efe _cf .ST_TickLblPos ;var _fcbd ,_bbe *_dfb .CT_ShapeProperties ;var _gag error ;_eebb :=_eac .Right -_eac .Left ;
_fbee :=_eac .Bottom -_eac .Top ;if _fcfd !=nil {_cgg ,_gfcbg ,_gdc ,_efe ,_bdge ,_abc ,_bbe ,_gag =_dcbf (_fcfd );};if _gfcbg !=_cf .ST_AxPosL &&_gfcbg !=_cf .ST_AxPosB {return _b .New ("\u004f\u006e\u006c\u0079\u0020l\u0065\u0066\u0074\u0020\u006f\u0072\u0020\u0062\u006f\u0074\u0074\u006f\u006d \u0079\u0020\u0061\u0078\u0069\u0073\u0020\u0073\u0075\u0070\u0070\u006f\u0072\u0074\u0020\u0073\u006f\u0020\u0066\u0061\u0072");
};_fcge :=_dfba -_cgeb ;_fabf :=int (_fcge /_eggf )+1;var _fdad ,_bea float64 ;switch _gdc {case _cf .ST_TickMarkIn :_fdad ,_bea =_gaf ,0;case _cf .ST_TickMarkOut :_fdad ,_bea =0,_gaf ;case _cf .ST_TickMarkCross :_fdad ,_bea =_gaf ,_gaf ;};_fdad =_fdad *_fabc ;
_bea =_bea *_fabc ;var _dgfg *_dfb .CT_ShapeProperties ;if _bdge !=nil {_dgfg =_bdge .SpPr ;};_gef ,_bfd :=_fagb !=_cf .ST_TickLblPosNone ,_efe !=_cf .ST_TickLblPosNone ;_eae :=_cgeb ;if _dbag !=nil {_dgce ,_bded ,_cfgd ,_fagb ,_fee ,_cbgc ,_fcbd ,_gag =_geff (_dbag );
}else if _ggg !=nil {_dgce ,_bded ,_cfgd ,_fagb ,_fee ,_cbgc ,_fcbd ,_gag =_daeed (_ggg );}else if _eabe !=nil {_dgce ,_bded ,_cfgd ,_fagb ,_fee ,_cbgc ,_fcbd ,_gag =_cba (_eabe );};if _gag !=nil {return _gag ;};if _bded !=_cf .ST_AxPosL &&_bded !=_cf .ST_AxPosB {return _b .New ("\u004f\u006e\u006c\u0079\u0020l\u0065\u0066\u0074\u0020\u006f\u0072\u0020\u0062\u006f\u0074\u0074\u006f\u006d \u0078\u0020\u0061\u0078\u0069\u0073\u0020\u0073\u0075\u0070\u0070\u006f\u0072\u0074\u0020\u0073\u006f\u0020\u0066\u0061\u0072");
};if _dgce !=_abc ||_cgg !=_cbgc {return _b .New ("a\u0078i\u0073\u0020\u0069\u0064\u0073\u0020\u0064\u006fn\u0027\u0074\u0020\u006dat\u0063\u0068");};_afd :=len (_gbbg )+1;var _dadd ,_ddb float64 ;switch _cfgd {case _cf .ST_TickMarkIn :_dadd ,_ddb =_gaf ,0;
case _cf .ST_TickMarkOut :_dadd ,_ddb =0,_gaf ;case _cf .ST_TickMarkCross :_dadd ,_ddb =_gaf ,_gaf ;};_dadd =_dadd *_fabc ;_ddb =_ddb *_fabc ;var _gea *_dfb .CT_ShapeProperties ;if _fee !=nil {_gea =_fee .SpPr ;};if _fcff {_befg :=_fbee /float64 (len (_gbbg ));
_edg :=_eac .Left -_cgeb *_eebb /_fcge ;_agc :=_edg -_aeeef *_fabc ;if _gef {var _fdff float64 ;for _cafg :=0;_cafg < _afd ;_cafg ++{_gabg :=_eac .Bottom -float64 (_cafg )*_befg ;if _cafg < _afd -1{_cdc :=_dcfe .NewParagraph (_gbbg [_cafg ]);_cdc .SetFontSize (_fad *_fabc );
_cdc .SetPos (_agc ,_gabg -_befg /2-_fgfg *_fabc );_dcfe .Draw (_cdc );_egbg :=(_cdc .Width ()/1000-_aeeef )*_fabc ;if _egbg > 0&&_egbg > _fdff {_fdff =_egbg ;};};};if _fdff > 0{_eac .Left +=_fdff +_ddf ;_edg =_eac .Left -_cgeb *_eebb /_fcge ;_eebb =_eac .Right -_eac .Left ;
};};_fagd :=_edg -_ddb ;_cfb :=_edg +_dadd ;_ccaa :=_eac .Left ;_gae :=_eac .Right ;for _fggd :=0;_fggd < _afd ;_fggd ++{_bged :=_eac .Bottom -float64 (_fggd )*_befg ;_fbga .drawLineWithProps (_fcbd ,_fagd ,_bged ,_cfb ,_bged ,true );_fbga .drawLineWithProps (_gea ,_ccaa ,_bged ,_gae ,_bged ,true );
};_ecdfe :=_eebb /_fcge ;_gba :=_eac .Bottom -_fdad ;_ddfg :=_eac .Bottom +_bea ;_gbbf :=_eac .Top ;_ffb :=_eac .Bottom ;for _gaed :=0;_gaed < _fabf ;_gaed ++{_edgd :=_eac .Left +(_eae -_cgeb )*_ecdfe ;_fbga .drawLineWithProps (_bbe ,_edgd ,_gba ,_edgd ,_ddfg ,true );
_fbga .drawLineWithProps (_dgfg ,_edgd ,_gbbf ,_edgd ,_ffb ,true );if _bfd {_acgg :=_dcfe .NewParagraph (_bd .FormatFloat (_eae ,'g',-1,64));_acgg .SetFontSize (_fad *_fabc );_acgg .SetPos (_edgd -_ged *_fabc ,_ffb +_edae *_fabc );_dcfe .Draw (_acgg );
};_eae +=_eggf ;};}else {_bac :=_fbee /_fcge ;_faa :=_eac .Left ;if _bfd {var _cad float64 ;for _gcga :=0;_gcga < _fabf ;_gcga ++{_addc :=_eac .Bottom -(_eae -_cgeb )*_bac ;_feaf :=_dcfe .NewParagraph (_bd .FormatFloat (_eae ,'g',-1,64));_feaf .SetFontSize (_fad *_fabc );
_feaf .SetPos (_faa -_aeeef *_fabc ,_addc -_fgfg *_fabc );_dcfe .Draw (_feaf );_cfc :=(_feaf .Width ()/1000-_aeeef )*_fabc ;if _cfc > 0&&_cfc > _cad {_cad =_cfc ;};_eae +=_eggf ;};if _cad > 0{_eac .Left +=_cad +_ddf ;_eebb =_eac .Right -_eac .Left ;};};
_eae =_cgeb ;_dgdg :=_eac .Left -_bea ;_bagc :=_eac .Left +_fdad ;_faa =_eac .Left ;_dga :=_eac .Right ;for _fadd :=0;_fadd < _fabf ;_fadd ++{_bcbg :=_eac .Bottom -(_eae -_cgeb )*_bac ;_fbga .drawLineWithProps (_bbe ,_dgdg ,_bcbg ,_bagc ,_bcbg ,true );
_fbga .drawLineWithProps (_dgfg ,_faa ,_bcbg ,_dga ,_bcbg ,true );_eae +=_eggf ;};_cbd :=_eebb /float64 (len (_gbbg ));_gfbb :=_eac .Bottom +_cgeb *_fbee /_fcge ;_fege :=_gfbb -_dadd ;_dfa :=_gfbb +_ddb ;_fdfb :=_eac .Top ;_bbcbe :=_eac .Bottom ;_cfce :=_gfbb +_edae *_fabc ;
for _fbbd :=0;_fbbd < _afd ;_fbbd ++{_eebf :=_eac .Left +float64 (_fbbd )*_cbd ;_fbga .drawLineWithProps (_fcbd ,_eebf ,_fege ,_eebf ,_dfa ,true );_fbga .drawLineWithProps (_gea ,_eebf ,_fdfb ,_eebf ,_bbcbe ,true );if _gef &&_fbbd < _afd -1{_fdd :=_dcfe .NewParagraph (_gbbg [_fbbd ]);
_fdd .SetFontSize (_fad *_fabc );_fdd .SetPos (_eebf +_bag *_fabc ,_cfce );_dcfe .Draw (_fdd );};};};return nil ;};func GetOpacityFromColorTransform (trs []*_dfb .EG_ColorTransform )float64 {for _ ,_acge :=range trs {if _acge !=nil {if _gaad :=_acge .ColorTransformChoice .Alpha ;
_gaad !=nil {if _bda :=_gaad .ValAttr .ST_PositiveFixedPercentageDecimal ;_bda !=nil {return float64 (*_bda )/Percent100 ;};};};};return 1.0;};func GetDataFromXfrm (xfrm *_dfb .CT_Transform2D )(float64 ,float64 ,float64 ,float64 ){var _cee ,_acfe ,_gafa ,_ffdc float64 ;
if _cdac :=xfrm .Off ;_cdac !=nil {_cee =_bb .FromEMU (FromSTCoordinate (_cdac .XAttr ));_acfe =_bb .FromEMU (FromSTCoordinate (_cdac .YAttr ));};if _dcecc :=xfrm .Ext ;_dcecc !=nil {_gafa =_bb .FromEMU (_dcecc .CxAttr );_ffdc =_bb .FromEMU (_dcecc .CyAttr );
};return _cee ,_acfe ,_gafa ,_ffdc ;};var _aeeef =_fgd (7.5);type serValue struct{_bfc string ;_dae float64 ;_ccg *_dfb .CT_ShapeProperties ;};func (_dfc barSerByOrder )Len ()int {return len (_dfc )};func TwipsFromPoints (points float64 )float64 {return points /_bb .Twips };
func _baea (_eddc uint8 ,_gcac float64 )string {_cadc :=float64 (_eddc );var _cdg float64 ;if _gcac < 0{_cdg =_cadc *(1+_gcac );}else {_cdg =_cadc +(255-_cadc )*_gcac ;};return _dfg .Sprintf ("\u0025\u0030\u0032\u0078",int (_cdg ));};func GetPageDimensions (size PageSize )_bg .PageSize {return _bcce [size ]};
func RegisterFontsFromFiles (files []string )error {for _ ,_dbfe :=range files {if _fc .HasSuffix (_dbfe ,"\u002e\u0074\u0074\u0066"){_ggbe :=_agb (_dbfe );if _ggbe !=nil {_fgg .Log .Debug ("\u0075\u006ea\u0062\u006c\u0065\u0020\u0074o\u0020\u0070\u0072\u006f\u0063e\u0073\u0073\u0020\u0061\u006e\u0064\u0020\u0072\u0065\u0067\u0069\u0073\u0074\u0065\u0072\u0020\u0066\u006f\u006e\u0074\u0020\u0066\u0072\u006f\u006d\u0020\u0054\u0054\u0046\u0020\u0066\u0069\u006c\u0065\u0020\u0025\u0073",_ggbe );
continue ;};};};return nil ;};