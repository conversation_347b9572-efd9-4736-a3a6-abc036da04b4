//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package wildcard ;func MatchSimple (pattern ,name string )bool {if pattern ==""{return name ==pattern ;};if pattern =="\u002a"{return true ;};_ed :=make ([]rune ,0,len (name ));_d :=make ([]rune ,0,len (pattern ));for _ ,_eg :=range name {_ed =append (_ed ,_eg );
};for _ ,_edd :=range pattern {_d =append (_d ,_edd );};_ec :=true ;return _ecc (_ed ,_d ,_ec );};func Index (pattern ,name string )(_ce int ){if pattern ==""||pattern =="\u002a"{return 0;};_f :=make ([]rune ,0,len (name ));_bc :=make ([]rune ,0,len (pattern ));
for _ ,_cg :=range name {_f =append (_f ,_cg );};for _ ,_ddb :=range pattern {_bc =append (_bc ,_ddb );};return _fe (_f ,_bc ,0);};func _fe (_aad ,_ab []rune ,_efb int )int {for len (_ab )> 0{switch _ab [0]{default:if len (_aad )==0{return -1;};if _aad [0]!=_ab [0]{return _fe (_aad [1:],_ab ,_efb +1);
};case '?':if len (_aad )==0{return -1;};case '*':if len (_aad )==0{return -1;};_bf :=_fe (_aad ,_ab [1:],_efb );if _bf !=-1{return _efb ;}else {_bf =_fe (_aad [1:],_ab ,_efb );if _bf !=-1{return _efb ;}else {return -1;};};};_aad =_aad [1:];_ab =_ab [1:];
};return _efb ;};func _ecc (_adb ,_ecf []rune ,_efc bool )bool {for len (_ecf )> 0{switch _ecf [0]{default:if len (_adb )==0||_adb [0]!=_ecf [0]{return false ;};case '?':if len (_adb )==0&&!_efc {return false ;};case '*':return _ecc (_adb ,_ecf [1:],_efc )||(len (_adb )> 0&&_ecc (_adb [1:],_ecf ,_efc ));
};_adb =_adb [1:];_ecf =_ecf [1:];};return len (_adb )==0&&len (_ecf )==0;};func Match (pattern ,name string )(_c bool ){if pattern ==""{return name ==pattern ;};if pattern =="\u002a"{return true ;};_dc :=make ([]rune ,0,len (name ));_g :=make ([]rune ,0,len (pattern ));
for _ ,_b :=range name {_dc =append (_dc ,_b );};for _ ,_dd :=range pattern {_g =append (_g ,_dd );};_aa :=false ;return _ecc (_dc ,_g ,_aa );};