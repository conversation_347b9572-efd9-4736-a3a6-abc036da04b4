//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package chart ;import (_g "fmt";_fa "github.com/unidoc/unioffice/v2";_gg "github.com/unidoc/unioffice/v2/color";_a "github.com/unidoc/unioffice/v2/drawing";_fc "github.com/unidoc/unioffice/v2/measurement";_e "github.com/unidoc/unioffice/v2/schema/soo/dml";
_f "github.com/unidoc/unioffice/v2/schema/soo/dml/chart";_gb "math/rand";);

// InitializeDefaults the bar chart to its defaults
func (_cgad BarChart )InitializeDefaults (){_cgad .SetDirection (_f .ST_BarDirCol )};func (_bba Legend )Properties ()_a .ShapeProperties {if _bba ._ceg .SpPr ==nil {_bba ._ceg .SpPr =_e .NewCT_ShapeProperties ();};return _a .MakeShapeProperties (_bba ._ceg .SpPr );
};

// AddSeries adds a default series to an Pie chart.
func (_acg PieChart )AddSeries ()PieChartSeries {_eee :=_f .NewCT_PieSer ();_acg ._gbe .Ser =append (_acg ._gbe .Ser ,_eee );_eee .Idx .ValAttr =uint32 (len (_acg ._gbe .Ser )-1);_eee .Order .ValAttr =uint32 (len (_acg ._gbe .Ser )-1);_bcde :=PieChartSeries {_eee };
_bcde .InitializeDefaults ();return _bcde ;};

// Properties returns the bar chart series shape properties.
func (_gfag RadarChartSeries )Properties ()_a .ShapeProperties {if _gfag ._gae .SpPr ==nil {_gfag ._gae .SpPr =_e .NewCT_ShapeProperties ();};return _a .MakeShapeProperties (_gfag ._gae .SpPr );};func (_faca SeriesAxis )InitializeDefaults (){};

// InitializeDefaults initializes an Pie series to the default values.
func (_baa PieChartSeries )InitializeDefaults (){};

// X returns the inner wrapped XML type.
func (_dgb DateAxis )X ()*_f .CT_DateAx {return _dgb ._aca };func (_dbf DataLabels )SetShowPercent (b bool ){_dbf .ensureChoice ();_dbf ._ecc .DLblsChoice .ShowPercent =_f .NewCT_Boolean ();_dbf ._ecc .DLblsChoice .ShowPercent .ValAttr =_fa .Bool (b );
};

// X returns the inner wrapped XML type.
func (_dce BubbleChartSeries )X ()*_f .CT_BubbleSer {return _dce ._efc };

// AddSeries adds a default series to a Surface chart.
func (_bea SurfaceChart )AddSeries ()SurfaceChartSeries {_fbde :=_bea .nextColor (len (_bea ._ege .Ser ));_fccb :=_f .NewCT_SurfaceSer ();_bea ._ege .Ser =append (_bea ._ege .Ser ,_fccb );_fccb .Idx .ValAttr =uint32 (len (_bea ._ege .Ser )-1);_fccb .Order .ValAttr =uint32 (len (_bea ._ege .Ser )-1);
_dbbb :=SurfaceChartSeries {_fccb };_dbbb .InitializeDefaults ();_dbbb .Properties ().LineProperties ().SetSolidFill (_fbde );return _dbbb ;};type Legend struct{_ceg *_f .CT_Legend };

// ScatterChartSeries is the data series for a scatter chart.
type ScatterChartSeries struct{_bbbb *_f .CT_ScatterSer };func MakeCategoryAxis (x *_f .CT_CatAx )CategoryAxis {return CategoryAxis {x }};func MakeLegend (l *_f .CT_Legend )Legend {return Legend {l }};

// X returns the inner wrapped XML type.
func (_ee BubbleChart )X ()*_f .CT_BubbleChart {return _ee ._add };

// AddArea3DChart adds a new area chart to a chart.
func (_edf Chart )AddArea3DChart ()Area3DChart {_ded (_edf ._edc .Chart );_fbe :=_f .NewCT_PlotAreaChoice ();_edf ._edc .Chart .PlotArea .PlotAreaChoice =append (_edf ._edc .Chart .PlotArea .PlotAreaChoice ,_fbe );_fbe .Area3DChart =_f .NewCT_Area3DChart ();
_dab :=Area3DChart {_d :_fbe .Area3DChart };_dab .InitializeDefaults ();return _dab ;};type nullAxis byte ;

// DoughnutChart is a Doughnut chart.
type DoughnutChart struct{chartBase ;_gbd *_f .CT_DoughnutChart ;};

// RadarChart is an Radar chart that has a shaded Radar underneath a curve.
type RadarChart struct{chartBase ;_ecg *_f .CT_RadarChart ;};

// CategoryAxis returns the category data source.
func (_be AreaChartSeries )CategoryAxis ()CategoryAxisDataSource {if _be ._fbg .Cat ==nil {_be ._fbg .Cat =_f .NewCT_AxDataSource ();};return MakeAxisDataSource (_be ._fbg .Cat );};

// AddLineChart adds a new line chart to a chart.
func (_agg Chart )AddLineChart ()LineChart {_bf :=_f .NewCT_PlotAreaChoice ();_agg ._edc .Chart .PlotArea .PlotAreaChoice =append (_agg ._edc .Chart .PlotArea .PlotAreaChoice ,_bf );_bf .LineChart =_f .NewCT_LineChart ();_bf .LineChart .Grouping =_f .NewCT_Grouping ();
_bf .LineChart .Grouping .ValAttr =_f .ST_GroupingStandard ;return LineChart {_gff :_bf .LineChart };};

// AddSeries adds a default series to a line chart.
func (_fgg Line3DChart )AddSeries ()LineChartSeries {_bdga :=_fgg .nextColor (len (_fgg ._aaf .Ser ));_gcg :=_f .NewCT_LineSer ();_fgg ._aaf .Ser =append (_fgg ._aaf .Ser ,_gcg );_gcg .Idx .ValAttr =uint32 (len (_fgg ._aaf .Ser )-1);_gcg .Order .ValAttr =uint32 (len (_fgg ._aaf .Ser )-1);
_dea :=LineChartSeries {_gcg };_dea .InitializeDefaults ();_dea .Properties ().LineProperties ().SetSolidFill (_bdga );_dea .Properties ().SetSolidFill (_bdga );return _dea ;};func (_bfb DateAxis )SetTickLabelPosition (p _f .ST_TickLblPos ){if p ==_f .ST_TickLblPosUnset {_bfb ._aca .TickLblPos =nil ;
}else {_bfb ._aca .TickLblPos =_f .NewCT_TickLblPos ();_bfb ._aca .TickLblPos .ValAttr =p ;};};

// InitializeDefaults the bar chart to its defaults
func (_cae Pie3DChart )InitializeDefaults (){_cae ._bgg .VaryColors =_f .NewCT_Boolean ();_cae ._bgg .VaryColors .ValAttr =_fa .Bool (true );};

// AddBarChart adds a new bar chart to a chart.
func (_bcc Chart )AddBarChart ()BarChart {_fd :=_f .NewCT_PlotAreaChoice ();_bcc ._edc .Chart .PlotArea .PlotAreaChoice =append (_bcc ._edc .Chart .PlotArea .PlotAreaChoice ,_fd );_fd .BarChart =_f .NewCT_BarChart ();_fd .BarChart .Grouping =_f .NewCT_BarGrouping ();
_fd .BarChart .Grouping .ValAttr =_f .ST_BarGroupingStandard ;_facd :=BarChart {_bbe :_fd .BarChart };_facd .InitializeDefaults ();return _facd ;};

// X returns the inner wrapped XML type.
func (_ef Area3DChart )X ()*_f .CT_Area3DChart {return _ef ._d };

// Chart is a generic chart.
type Chart struct{_edc *_f .ChartSpace };

// BarChartSeries is a series to be used on a bar chart.
type BarChartSeries struct{_cee *_f .CT_BarSer };

// SetText sets the series text.
func (_dbe PieChartSeries )SetText (s string ){_dbe ._dcc .Tx =_f .NewCT_SerTx ();_dbe ._dcc .Tx .SerTxChoice .V =&s ;};

// LineChartSeries is the data series for a line chart.
type LineChartSeries struct{_aeb *_f .CT_LineSer };func (_fef DateAxis )MajorGridLines ()GridLines {if _fef ._aca .MajorGridlines ==nil {_fef ._aca .MajorGridlines =_f .NewCT_ChartLines ();};return GridLines {_fef ._aca .MajorGridlines };};func (_gba BarChart )AddAxis (axis Axis ){_egf :=_f .NewCT_UnsignedInt ();
_egf .ValAttr =axis .AxisID ();_gba ._bbe .AxId =append (_gba ._bbe .AxId ,_egf );};

// Marker returns the marker properties.
func (_bag ScatterChartSeries )Marker ()Marker {if _bag ._bbbb .Marker ==nil {_bag ._bbbb .Marker =_f .NewCT_Marker ();};return MakeMarker (_bag ._bbbb .Marker );};

// AddAreaChart adds a new area chart to a chart.
func (_dg Chart )AddAreaChart ()AreaChart {_gadf :=_f .NewCT_PlotAreaChoice ();_dg ._edc .Chart .PlotArea .PlotAreaChoice =append (_dg ._edc .Chart .PlotArea .PlotAreaChoice ,_gadf );_gadf .AreaChart =_f .NewCT_AreaChart ();_gdg :=AreaChart {_fb :_gadf .AreaChart };
_gdg .InitializeDefaults ();return _gdg ;};

// InitializeDefaults initializes a bar chart series to the default values.
func (_cd BarChartSeries )InitializeDefaults (){};

// X returns the inner wrapped XML type.
func (_ffc ScatterChartSeries )X ()*_f .CT_ScatterSer {return _ffc ._bbbb };

// AddValueAxis adds a value axis to the chart.
func (_fgf Chart )AddValueAxis ()ValueAxis {_bcb :=_f .NewCT_ValAx ();if _fgf ._edc .Chart .PlotArea .PlotAreaChoice1 ==nil {_fgf ._edc .Chart .PlotArea .PlotAreaChoice1 =[]*_f .CT_PlotAreaChoice1 {};};_bcb .AxId =_f .NewCT_UnsignedInt ();_bcb .AxId .ValAttr =0x7FFFFFFF&_gb .Uint32 ();
_fgf ._edc .Chart .PlotArea .PlotAreaChoice1 =append (_fgf ._edc .Chart .PlotArea .PlotAreaChoice1 ,&_f .CT_PlotAreaChoice1 {ValAx :_bcb });_bcb .Delete =_f .NewCT_Boolean ();_bcb .Delete .ValAttr =_fa .Bool (false );_bcb .Scaling =_f .NewCT_Scaling ();
_bcb .Scaling .Orientation =_f .NewCT_Orientation ();_bcb .Scaling .Orientation .ValAttr =_f .ST_OrientationMinMax ;_bcb .AxSharedChoice =&_f .EG_AxSharedChoice {};_bcb .AxSharedChoice .Crosses =_f .NewCT_Crosses ();_bcb .AxSharedChoice .Crosses .ValAttr =_f .ST_CrossesAutoZero ;
_bcb .CrossBetween =_f .NewCT_CrossBetween ();_bcb .CrossBetween .ValAttr =_f .ST_CrossBetweenBetween ;_adb :=MakeValueAxis (_bcb );_adb .MajorGridLines ().Properties ().LineProperties ().SetSolidFill (_gg .LightGray );_adb .SetMajorTickMark (_f .ST_TickMarkOut );
_adb .SetMinorTickMark (_f .ST_TickMarkIn );_adb .SetTickLabelPosition (_f .ST_TickLblPosNextTo );_adb .Properties ().LineProperties ().SetSolidFill (_gg .Black );_adb .SetPosition (_f .ST_AxPosL );return _adb ;};type Marker struct{_gdbd *_f .CT_Marker };


// X returns the inner wrapped XML type.
func (_ffa Line3DChart )X ()*_f .CT_Line3DChart {return _ffa ._aaf };

// PieOfPieChart is a Pie chart with an extra Pie chart.
type PieOfPieChart struct{chartBase ;_dadcg *_f .CT_OfPieChart ;};

// BarChart is a 2D bar chart.
type BarChart struct{chartBase ;_bbe *_f .CT_BarChart ;};func (_ea CategoryAxis )AxisID ()uint32 {return _ea ._gf .AxId .ValAttr };func (_cda DataLabels )ensureChoice (){if _cda ._ecc .DLblsChoice ==nil {_cda ._ecc .DLblsChoice =_f .NewCT_DLblsChoice ();
};};

// AreaChart is an area chart that has a shaded area underneath a curve.
type AreaChart struct{chartBase ;_fb *_f .CT_AreaChart ;};

// X returns the inner wrapped XML type.
func (_gbb Marker )X ()*_f .CT_Marker {return _gbb ._gdbd };func (_dd CategoryAxis )SetTickLabelPosition (p _f .ST_TickLblPos ){if p ==_f .ST_TickLblPosUnset {_dd ._gf .TickLblPos =nil ;}else {_dd ._gf .TickLblPos =_f .NewCT_TickLblPos ();_dd ._gf .TickLblPos .ValAttr =p ;
};};func (_fggb SeriesAxis )SetCrosses (axis Axis ){_fggb ._abd .CrossAx .ValAttr =axis .AxisID ()};func (_ccb CategoryAxis )InitializeDefaults (){_ccb .SetPosition (_f .ST_AxPosB );_ccb .SetMajorTickMark (_f .ST_TickMarkOut );_ccb .SetMinorTickMark (_f .ST_TickMarkIn );
_ccb .SetTickLabelPosition (_f .ST_TickLblPosNextTo );_ccb .MajorGridLines ().Properties ().LineProperties ().SetSolidFill (_gg .LightGray );_ccb .Properties ().LineProperties ().SetSolidFill (_gg .Black );};

// AddPie3DChart adds a new pie chart to a chart.
func (_fcc Chart )AddPie3DChart ()Pie3DChart {_ded (_fcc ._edc .Chart );_fdd :=_f .NewCT_PlotAreaChoice ();_fcc ._edc .Chart .PlotArea .PlotAreaChoice =append (_fcc ._edc .Chart .PlotArea .PlotAreaChoice ,_fdd );_fdd .Pie3DChart =_f .NewCT_Pie3DChart ();
_dabf :=Pie3DChart {_bgg :_fdd .Pie3DChart };_dabf .InitializeDefaults ();return _dabf ;};

// Pie3DChart is a Pie3D chart.
type Pie3DChart struct{chartBase ;_bgg *_f .CT_Pie3DChart ;};

// Properties returns the Bubble chart series shape properties.
func (_gc BubbleChartSeries )Properties ()_a .ShapeProperties {if _gc ._efc .SpPr ==nil {_gc ._efc .SpPr =_e .NewCT_ShapeProperties ();};return _a .MakeShapeProperties (_gc ._efc .SpPr );};func (_ccd DateAxis )SetMinorTickMark (m _f .ST_TickMark ){if m ==_f .ST_TickMarkUnset {_ccd ._aca .MinorTickMark =nil ;
}else {_ccd ._aca .MinorTickMark =_f .NewCT_TickMark ();_ccd ._aca .MinorTickMark .ValAttr =m ;};};func (_eecf GridLines )Properties ()_a .ShapeProperties {if _eecf ._edgb .SpPr ==nil {_eecf ._edgb .SpPr =_e .NewCT_ShapeProperties ();};return _a .MakeShapeProperties (_eecf ._edgb .SpPr );
};func (_gafe ScatterChartSeries )InitializeDefaults (){_gafe .Properties ().LineProperties ().SetNoFill ();_gafe .Marker ().SetSymbol (_f .ST_MarkerStyleAuto );_gafe .Labels ().SetShowLegendKey (false );_gafe .Labels ().SetShowValue (true );_gafe .Labels ().SetShowPercent (false );
_gafe .Labels ().SetShowCategoryName (false );_gafe .Labels ().SetShowSeriesName (false );_gafe .Labels ().SetShowLeaderLines (false );};

// SetDirection changes the direction of the bar chart (bar or column).
func (_aa Bar3DChart )SetDirection (d _f .ST_BarDir ){_aa ._db .BarDir .ValAttr =d };func (_efg ValueAxis )SetTickLabelPosition (p _f .ST_TickLblPos ){if p ==_f .ST_TickLblPosUnset {_efg ._gea .TickLblPos =nil ;}else {_efg ._gea .TickLblPos =_f .NewCT_TickLblPos ();
_efg ._gea .TickLblPos .ValAttr =p ;};};type Title struct{_febe *_f .CT_Title };

// InitializeDefaults initializes an Radar series to the default values.
func (_ceec RadarChartSeries )InitializeDefaults (){};

// SetDirection changes the direction of the bar chart (bar or column).
func (_gad BarChart )SetDirection (d _f .ST_BarDir ){_gad ._bbe .BarDir .ValAttr =d };

// CategoryAxisDataSource specifies the data for an axis.  It's commonly used with
// SetReference to set the axis data to a range of cells.
type CategoryAxisDataSource struct{_ffb *_f .CT_AxDataSource };

// X returns the inner wrapped XML type.
func (_cbf Bar3DChart )X ()*_f .CT_Bar3DChart {return _cbf ._db };

// Order returns the order of the series
func (_dcdd LineChartSeries )Order ()uint32 {return _dcdd ._aeb .Order .ValAttr };

// AddTitle sets a new title on the chart.
func (_fdb Chart )AddTitle ()Title {_fdb ._edc .Chart .Title =_f .NewCT_Title ();_fdb ._edc .Chart .Title .Overlay =_f .NewCT_Boolean ();_fdb ._edc .Chart .Title .Overlay .ValAttr =_fa .Bool (false );_fdb ._edc .Chart .AutoTitleDeleted =_f .NewCT_Boolean ();
_fdb ._edc .Chart .AutoTitleDeleted .ValAttr =_fa .Bool (false );_dag :=MakeTitle (_fdb ._edc .Chart .Title );_dag .InitializeDefaults ();return _dag ;};

// SetText sets the series text.
func (_cga AreaChartSeries )SetText (s string ){_cga ._fbg .Tx =_f .NewCT_SerTx ();_cga ._fbg .Tx .SerTxChoice .V =&s ;};

// AddSurface3DChart adds a new 3D surface chart to a chart.
func (_fdc Chart )AddSurface3DChart ()Surface3DChart {_ab :=_f .NewCT_PlotAreaChoice ();_fdc ._edc .Chart .PlotArea .PlotAreaChoice =append (_fdc ._edc .Chart .PlotArea .PlotAreaChoice ,_ab );_ab .Surface3DChart =_f .NewCT_Surface3DChart ();_ded (_fdc ._edc .Chart );
_cfeb :=Surface3DChart {_edcf :_ab .Surface3DChart };_cfeb .InitializeDefaults ();return _cfeb ;};

// X returns the inner wrapped XML type.
func (_ddg LineChart )X ()*_f .CT_LineChart {return _ddg ._gff };

// Index returns the index of the series
func (_dfc SurfaceChartSeries )Index ()uint32 {return _dfc ._fecg .Idx .ValAttr };

// InitializeDefaults initializes an area series to the default values.
func (_fcd AreaChartSeries )InitializeDefaults (){};func (_acaf DateAxis )SetCrosses (axis Axis ){_acaf ._aca .CrossAx .ValAttr =axis .AxisID ()};

// Bar3DChart is a 3D bar chart.
type Bar3DChart struct{chartBase ;_db *_f .CT_Bar3DChart ;};

// BubbleChart is a 2D Bubble chart.
type BubbleChart struct{chartBase ;_add *_f .CT_BubbleChart ;};func (_gbaf DataLabels )SetShowCategoryName (b bool ){_gbaf .ensureChoice ();_gbaf ._ecc .DLblsChoice .ShowCatName =_f .NewCT_Boolean ();_gbaf ._ecc .DLblsChoice .ShowCatName .ValAttr =_fa .Bool (b );
};

// AddDoughnutChart adds a new doughnut (pie with a hole in the center) chart to a chart.
func (_bfd Chart )AddDoughnutChart ()DoughnutChart {_dbg :=_f .NewCT_PlotAreaChoice ();_bfd ._edc .Chart .PlotArea .PlotAreaChoice =append (_bfd ._edc .Chart .PlotArea .PlotAreaChoice ,_dbg );_dbg .DoughnutChart =_f .NewCT_DoughnutChart ();_cgc :=DoughnutChart {_gbd :_dbg .DoughnutChart };
_cgc .InitializeDefaults ();return _cgc ;};

// Axis is the interface implemented by different axes when assigning to a
// chart.
type Axis interface{AxisID ()uint32 ;};func (_caa Title )RunProperties ()_a .RunProperties {if _caa ._febe .Tx ==nil {_caa .SetText ("");};if _caa ._febe .Tx .TxChoice .Rich .P [0].EG_TextRun [0].TextRunChoice .R .RPr ==nil {_caa ._febe .Tx .TxChoice .Rich .P [0].EG_TextRun [0].TextRunChoice .R .RPr =_e .NewCT_TextCharacterProperties ();
};return _a .MakeRunProperties (_caa ._febe .Tx .TxChoice .Rich .P [0].EG_TextRun [0].TextRunChoice .R .RPr );};

// Index returns the index of the series
func (_efcf LineChartSeries )Index ()uint32 {return _efcf ._aeb .Idx .ValAttr };

// AddSeries adds a default series to an Pie chart.
func (_gdca PieOfPieChart )AddSeries ()PieChartSeries {_adbc :=_f .NewCT_PieSer ();_gdca ._dadcg .Ser =append (_gdca ._dadcg .Ser ,_adbc );_adbc .Idx .ValAttr =uint32 (len (_gdca ._dadcg .Ser )-1);_adbc .Order .ValAttr =uint32 (len (_gdca ._dadcg .Ser )-1);
_ccc :=PieChartSeries {_adbc };_ccc .InitializeDefaults ();return _ccc ;};

// SetExplosion sets the value that the segements of the pie are 'exploded' by
func (_dadc PieChartSeries )SetExplosion (v uint32 ){_dadc ._dcc .Explosion =_f .NewCT_UnsignedInt ();_dadc ._dcc .Explosion .ValAttr =v ;};

// SetValues sets values directly on a source.
func (_abe NumberDataSource )SetValues (v []float64 ){_abe .ensureChoice ();_abe ._dda .NumDataSourceChoice .NumRef =nil ;_abe ._dda .NumDataSourceChoice .NumLit =_f .NewCT_NumData ();_abe ._dda .NumDataSourceChoice .NumLit .PtCount =_f .NewCT_UnsignedInt ();
_abe ._dda .NumDataSourceChoice .NumLit .PtCount .ValAttr =uint32 (len (v ));for _bae ,_bca :=range v {_abe ._dda .NumDataSourceChoice .NumLit .Pt =append (_abe ._dda .NumDataSourceChoice .NumLit .Pt ,&_f .CT_NumVal {IdxAttr :uint32 (_bae ),V :_g .Sprintf ("\u0025\u0067",_bca )});
};};func (_aad CategoryAxis )MajorGridLines ()GridLines {if _aad ._gf .MajorGridlines ==nil {_aad ._gf .MajorGridlines =_f .NewCT_ChartLines ();};return GridLines {_aad ._gf .MajorGridlines };};type SurfaceChartSeries struct{_fecg *_f .CT_SurfaceSer };
type DateAxis struct{_aca *_f .CT_DateAx };

// AddSeries adds a default series to a Stock chart.
func (_eed StockChart )AddSeries ()LineChartSeries {_acab :=_f .NewCT_LineSer ();_eed ._gbef .Ser =append (_eed ._gbef .Ser ,_acab );_acab .Idx .ValAttr =uint32 (len (_eed ._gbef .Ser )-1);_acab .Order .ValAttr =uint32 (len (_eed ._gbef .Ser )-1);_cacf :=LineChartSeries {_acab };
_cacf .Values ().CreateEmptyNumberCache ();_cacf .Properties ().LineProperties ().SetNoFill ();return _cacf ;};func (_cbcg ValueAxis )MajorGridLines ()GridLines {if _cbcg ._gea .MajorGridlines ==nil {_cbcg ._gea .MajorGridlines =_f .NewCT_ChartLines ();
};return GridLines {_cbcg ._gea .MajorGridlines };};func (_cfg Marker )SetSymbol (s _f .ST_MarkerStyle ){if s ==_f .ST_MarkerStyleUnset {_cfg ._gdbd .Symbol =nil ;}else {_cfg ._gdbd .Symbol =_f .NewCT_MarkerStyle ();_cfg ._gdbd .Symbol .ValAttr =s ;};};


// InitializeDefaults the bar chart to its defaults
func (_fbdg DoughnutChart )InitializeDefaults (){_fbdg ._gbd .VaryColors =_f .NewCT_Boolean ();_fbdg ._gbd .VaryColors .ValAttr =_fa .Bool (true );_fbdg ._gbd .HoleSize =_f .NewCT_HoleSize ();_fbdg ._gbd .HoleSize .ValAttr =&_f .ST_HoleSize {};_fbdg ._gbd .HoleSize .ValAttr .ST_HoleSizeUByte =_fa .Uint8 (50);
};func MakeTitle (x *_f .CT_Title )Title {return Title {x }};

// AddSeries adds a default series to a bar chart.
func (_agb Bar3DChart )AddSeries ()BarChartSeries {_fac :=_agb .nextColor (len (_agb ._db .Ser ));_ffe :=_f .NewCT_BarSer ();_agb ._db .Ser =append (_agb ._db .Ser ,_ffe );_ffe .Idx .ValAttr =uint32 (len (_agb ._db .Ser )-1);_ffe .Order .ValAttr =uint32 (len (_agb ._db .Ser )-1);
_ed :=BarChartSeries {_ffe };_ed .InitializeDefaults ();_ed .Properties ().SetSolidFill (_fac );return _ed ;};type ValueAxis struct{_gea *_f .CT_ValAx };

// Area3DChart is an area chart that has a shaded area underneath a curve.
type Area3DChart struct{chartBase ;_d *_f .CT_Area3DChart ;};

// SurfaceChart is a 3D surface chart, viewed from the top-down.
type SurfaceChart struct{chartBase ;_ege *_f .CT_SurfaceChart ;};func MakeNumberDataSource (x *_f .CT_NumDataSource )NumberDataSource {return NumberDataSource {x }};

// X returns the inner wrapped XML type.
func (_cggg SeriesAxis )X ()*_f .CT_SerAx {return _cggg ._abd };

// AddStockChart adds a new stock chart.
func (_eec Chart )AddStockChart ()StockChart {_bcda :=_f .NewCT_PlotAreaChoice ();_eec ._edc .Chart .PlotArea .PlotAreaChoice =append (_eec ._edc .Chart .PlotArea .PlotAreaChoice ,_bcda );_bcda .StockChart =_f .NewCT_StockChart ();_bfe :=StockChart {_gbef :_bcda .StockChart };
_bfe .InitializeDefaults ();return _bfe ;};

// SetOrder sets the order of the series
func (_beea SurfaceChartSeries )SetOrder (idx uint32 ){_beea ._fecg .Order .ValAttr =idx };

// AddSurfaceChart adds a new surface chart to a chart.
func (_cac Chart )AddSurfaceChart ()SurfaceChart {_ebba :=_f .NewCT_PlotAreaChoice ();_cac ._edc .Chart .PlotArea .PlotAreaChoice =append (_cac ._edc .Chart .PlotArea .PlotAreaChoice ,_ebba );_ebba .SurfaceChart =_f .NewCT_SurfaceChart ();_ded (_cac ._edc .Chart );
_cac ._edc .Chart .View3D .RotX .ValAttr =_fa .Int8 (90);_cac ._edc .Chart .View3D .RotY .ValAttr =_fa .Uint16 (0);_cac ._edc .Chart .View3D .Perspective =_f .NewCT_Perspective ();_cac ._edc .Chart .View3D .Perspective .ValAttr =_fa .Uint8 (0);_dabfd :=SurfaceChart {_ege :_ebba .SurfaceChart };
_dabfd .InitializeDefaults ();return _dabfd ;};func (_aaa DateAxis )SetPosition (p _f .ST_AxPos ){_aaa ._aca .AxPos =_f .NewCT_AxPos ();_aaa ._aca .AxPos .ValAttr =p ;};func (_bfa DataLabels )SetShowSeriesName (b bool ){_bfa .ensureChoice ();_bfa ._ecc .DLblsChoice .ShowSerName =_f .NewCT_Boolean ();
_bfa ._ecc .DLblsChoice .ShowSerName .ValAttr =_fa .Bool (b );};

// X returns the inner wrapped XML type.
func (_ffab LineChartSeries )X ()*_f .CT_LineSer {return _ffab ._aeb };

// Values returns the value data source.
func (_dbbc PieChartSeries )Values ()NumberDataSource {if _dbbc ._dcc .Val ==nil {_dbbc ._dcc .Val =_f .NewCT_NumDataSource ();};return MakeNumberDataSource (_dbbc ._dcc .Val );};

// AddAxis adds an axis to a line chart.
func (_fba LineChart )AddAxis (axis Axis ){_bbf :=_f .NewCT_UnsignedInt ();_bbf .ValAttr =axis .AxisID ();_fba ._gff .AxId =append (_fba ._gff .AxId ,_bbf );};

// AddAxis adds an axis to a Surface chart.
func (_abc SurfaceChart )AddAxis (axis Axis ){_bfg :=_f .NewCT_UnsignedInt ();_bfg .ValAttr =axis .AxisID ();_abc ._ege .AxId =append (_abc ._ege .AxId ,_bfg );};

// CategoryAxis returns the category data source.
func (_da BubbleChartSeries )CategoryAxis ()CategoryAxisDataSource {if _da ._efc .XVal ==nil {_da ._efc .XVal =_f .NewCT_AxDataSource ();};return MakeAxisDataSource (_da ._efc .XVal );};func (_acb BubbleChart )AddAxis (axis Axis ){_cdf :=_f .NewCT_UnsignedInt ();
_cdf .ValAttr =axis .AxisID ();_acb ._add .AxId =append (_acb ._add .AxId ,_cdf );};

// InitializeDefaults the bar chart to its defaults
func (_ge AreaChart )InitializeDefaults (){};

// PieChartSeries is a series to be used on an Pie chart.
type PieChartSeries struct{_dcc *_f .CT_PieSer };func (_fec Marker )SetSize (sz uint8 ){_fec ._gdbd .Size =_f .NewCT_MarkerSize ();_fec ._gdbd .Size .ValAttr =&sz ;};var _eae =[]_gg .Color {_gg .RGB (0x33,0x66,0xcc),_gg .RGB (0xDC,0x39,0x12),_gg .RGB (0xFF,0x99,0x00),_gg .RGB (0x10,0x96,0x18),_gg .RGB (0x99,0x00,0x99),_gg .RGB (0x3B,0x3E,0xAC),_gg .RGB (0x00,0x99,0xC6),_gg .RGB (0xDD,0x44,0x77),_gg .RGB (0x66,0xAA,0x00),_gg .RGB (0xB8,0x2E,0x2E),_gg .RGB (0x31,0x63,0x95),_gg .RGB (0x99,0x44,0x99),_gg .RGB (0x22,0xAA,0x99),_gg .RGB (0xAA,0xAA,0x11),_gg .RGB (0x66,0x33,0xCC),_gg .RGB (0xE6,0x73,0x00),_gg .RGB (0x8B,0x07,0x07),_gg .RGB (0x32,0x92,0x62),_gg .RGB (0x55,0x74,0xA6),_gg .RGB (0x3B,0x3E,0xAC)};


// X returns the inner wrapped XML type.
func (_adc PieOfPieChart )X ()*_f .CT_OfPieChart {return _adc ._dadcg };func (_fadb SurfaceChartSeries )CategoryAxis ()CategoryAxisDataSource {if _fadb ._fecg .Cat ==nil {_fadb ._fecg .Cat =_f .NewCT_AxDataSource ();};return MakeAxisDataSource (_fadb ._fecg .Cat );
};

// X returns the inner wrapped XML type.
func (_eeee ScatterChart )X ()*_f .CT_ScatterChart {return _eeee ._cfae };func MakeMarker (x *_f .CT_Marker )Marker {return Marker {x }};

// SetIndex sets the index of the series
func (_cfaef SurfaceChartSeries )SetIndex (idx uint32 ){_cfaef ._fecg .Idx .ValAttr =idx };func (_cfbb ValueAxis )SetMinorTickMark (m _f .ST_TickMark ){if m ==_f .ST_TickMarkUnset {_cfbb ._gea .MinorTickMark =nil ;}else {_cfbb ._gea .MinorTickMark =_f .NewCT_TickMark ();
_cfbb ._gea .MinorTickMark .ValAttr =m ;};};

// CreateEmptyNumberCache creates an empty number cache, which is used sometimes
// to increase file format compatibility.  It should actually contain the
// computed cell data, but just creating an empty one is good enough.
func (_efae NumberDataSource )CreateEmptyNumberCache (){_efae .ensureChoice ();if _efae ._dda .NumDataSourceChoice .NumRef ==nil {_efae ._dda .NumDataSourceChoice .NumRef =_f .NewCT_NumRef ();};_efae ._dda .NumDataSourceChoice .NumLit =nil ;_efae ._dda .NumDataSourceChoice .NumRef .NumCache =_f .NewCT_NumData ();
_efae ._dda .NumDataSourceChoice .NumRef .NumCache .PtCount =_f .NewCT_UnsignedInt ();_efae ._dda .NumDataSourceChoice .NumRef .NumCache .PtCount .ValAttr =0;};

// AddSeries adds a default series to an area chart.
func (_af AreaChart )AddSeries ()AreaChartSeries {_cb :=_af .nextColor (len (_af ._fb .Ser ));_cg :=_f .NewCT_AreaSer ();_af ._fb .Ser =append (_af ._fb .Ser ,_cg );_cg .Idx .ValAttr =uint32 (len (_af ._fb .Ser )-1);_cg .Order .ValAttr =uint32 (len (_af ._fb .Ser )-1);
_cbc :=AreaChartSeries {_cg };_cbc .InitializeDefaults ();_cbc .Properties ().SetSolidFill (_cb );return _cbc ;};

// CategoryAxis returns the category data source.
func (_bce BarChartSeries )CategoryAxis ()CategoryAxisDataSource {if _bce ._cee .Cat ==nil {_bce ._cee .Cat =_f .NewCT_AxDataSource ();};return MakeAxisDataSource (_bce ._cee .Cat );};func (_gadb DateAxis )AxisID ()uint32 {return _gadb ._aca .AxId .ValAttr };
func (_ebda DataLabels )SetPosition (p _f .ST_DLblPos ){_ebda .ensureChoice ();_ebda ._ecc .DLblsChoice .DLblPos =_f .NewCT_DLblPos ();_ebda ._ecc .DLblsChoice .DLblPos .ValAttr =p ;};

// X returns the inner wrapped XML type.
func (_ad BarChartSeries )X ()*_f .CT_BarSer {return _ad ._cee };type GridLines struct{_edgb *_f .CT_ChartLines };

// AreaChartSeries is a series to be used on an area chart.
type AreaChartSeries struct{_fbg *_f .CT_AreaSer };

// InitializeDefaults the bar chart to its defaults
func (_efa Area3DChart )InitializeDefaults (){};func _ded (_cfa *_f .CT_Chart ){_cfa .View3D =_f .NewCT_View3D ();_cfa .View3D .RotX =_f .NewCT_RotX ();_cfa .View3D .RotX .ValAttr =_fa .Int8 (15);_cfa .View3D .RotY =_f .NewCT_RotY ();_cfa .View3D .RotY .ValAttr =_fa .Uint16 (20);
_cfa .View3D .RAngAx =_f .NewCT_Boolean ();_cfa .View3D .RAngAx .ValAttr =_fa .Bool (false );_cfa .Floor =_f .NewCT_Surface ();_cfa .Floor .Thickness =_f .NewCT_Thickness ();_cfa .Floor .Thickness .ValAttr .Uint32 =_fa .Uint32 (0);_cfa .SideWall =_f .NewCT_Surface ();
_cfa .SideWall .Thickness =_f .NewCT_Thickness ();_cfa .SideWall .Thickness .ValAttr .Uint32 =_fa .Uint32 (0);_cfa .BackWall =_f .NewCT_Surface ();_cfa .BackWall .Thickness =_f .NewCT_Thickness ();_cfa .BackWall .Thickness .ValAttr .Uint32 =_fa .Uint32 (0);
};func (_dgg ScatterChart )InitializeDefaults (){_dgg ._cfae .ScatterStyle .ValAttr =_f .ST_ScatterStyleMarker ;};

// Properties returns the line chart series shape properties.
func (_feb ScatterChartSeries )Properties ()_a .ShapeProperties {if _feb ._bbbb .SpPr ==nil {_feb ._bbbb .SpPr =_e .NewCT_ShapeProperties ();};return _a .MakeShapeProperties (_feb ._bbbb .SpPr );};

// AddSeries adds a default series to an Radar chart.
func (_gfdbc RadarChart )AddSeries ()RadarChartSeries {_gfeg :=_gfdbc .nextColor (len (_gfdbc ._ecg .Ser ));_gbf :=_f .NewCT_RadarSer ();_gfdbc ._ecg .Ser =append (_gfdbc ._ecg .Ser ,_gbf );_gbf .Idx .ValAttr =uint32 (len (_gfdbc ._ecg .Ser )-1);_gbf .Order .ValAttr =uint32 (len (_gfdbc ._ecg .Ser )-1);
_afa :=RadarChartSeries {_gbf };_afa .InitializeDefaults ();_afa .Properties ().SetSolidFill (_gfeg );return _afa ;};func (_cfeba Surface3DChart )InitializeDefaults (){_cfeba ._edcf .Wireframe =_f .NewCT_Boolean ();_cfeba ._edcf .Wireframe .ValAttr =_fa .Bool (false );
_cfeba ._edcf .BandFmts =_f .NewCT_BandFmts ();for _fcde :=0;_fcde < 15;_fcde ++{_gda :=_f .NewCT_BandFmt ();_gda .Idx .ValAttr =uint32 (_fcde );_gda .SpPr =_e .NewCT_ShapeProperties ();_dee :=_a .MakeShapeProperties (_gda .SpPr );_dee .SetSolidFill (_cfeba .nextColor (_fcde ));
_cfeba ._edcf .BandFmts .BandFmt =append (_cfeba ._edcf .BandFmts .BandFmt ,_gda );};};

// Properties returns the bar chart series shape properties.
func (_bb AreaChartSeries )Properties ()_a .ShapeProperties {if _bb ._fbg .SpPr ==nil {_bb ._fbg .SpPr =_e .NewCT_ShapeProperties ();};return _a .MakeShapeProperties (_bb ._fbg .SpPr );};

// InitializeDefaults the bar chart to its defaults
func (_dae PieChart )InitializeDefaults (){_dae ._gbe .VaryColors =_f .NewCT_Boolean ();_dae ._gbe .VaryColors .ValAttr =_fa .Bool (true );};

// SetType sets the type the secone pie to either pie or bar
func (_gfda PieOfPieChart )SetType (t _f .ST_OfPieType ){_gfda ._dadcg .OfPieType .ValAttr =t };func (_fg Area3DChart )AddAxis (axis Axis ){_eg :=_f .NewCT_UnsignedInt ();_eg .ValAttr =axis .AxisID ();_fg ._d .AxId =append (_fg ._d .AxId ,_eg );};func (_agc RadarChart )AddAxis (axis Axis ){_gec :=_f .NewCT_UnsignedInt ();
_gec .ValAttr =axis .AxisID ();_agc ._ecg .AxId =append (_agc ._ecg .AxId ,_gec );};

// SetLabelReference is used to set the source data to a range of cells
// containing strings.
func (_eab CategoryAxisDataSource )SetLabelReference (s string ){_eab ._ffb .AxDataSourceChoice =_f .NewCT_AxDataSourceChoice ();_eab ._ffb .AxDataSourceChoice .StrRef =_f .NewCT_StrRef ();_eab ._ffb .AxDataSourceChoice .StrRef .F =s ;};func (_dace LineChartSeries )CategoryAxis ()CategoryAxisDataSource {if _dace ._aeb .Cat ==nil {_dace ._aeb .Cat =_f .NewCT_AxDataSource ();
};return MakeAxisDataSource (_dace ._aeb .Cat );};

// PieChart is a Pie chart.
type PieChart struct{chartBase ;_gbe *_f .CT_PieChart ;};

// SetDisplayBlanksAs controls how missing values are displayed.
func (_gfc Chart )SetDisplayBlanksAs (v _f .ST_DispBlanksAs ){_gfc ._edc .Chart .DispBlanksAs =_f .NewCT_DispBlanksAs ();_gfc ._edc .Chart .DispBlanksAs .ValAttr =v ;};

// AddSeries adds a default series to a line chart.
func (_gdb LineChart )AddSeries ()LineChartSeries {_faa :=_gdb .nextColor (len (_gdb ._gff .Ser ));_adf :=_f .NewCT_LineSer ();_gdb ._gff .Ser =append (_gdb ._gff .Ser ,_adf );_adf .Idx .ValAttr =uint32 (len (_gdb ._gff .Ser )-1);_adf .Order .ValAttr =uint32 (len (_gdb ._gff .Ser )-1);
_bdf :=LineChartSeries {_adf };_bdf .InitializeDefaults ();_bdf .Properties ().LineProperties ().SetSolidFill (_faa );return _bdf ;};func (_gfd DataLabels )SetShowLeaderLines (b bool ){_gfd .ensureChoice ();_gfd ._ecc .DLblsChoice .ShowLeaderLines =_f .NewCT_Boolean ();
_gfd ._ecc .DLblsChoice .ShowLeaderLines .ValAttr =_fa .Bool (b );};func (_ga nullAxis )AxisID ()uint32 {return 0};type CategoryAxis struct{_gf *_f .CT_CatAx };func (_gfg SurfaceChart )InitializeDefaults (){_gfg ._ege .Wireframe =_f .NewCT_Boolean ();_gfg ._ege .Wireframe .ValAttr =_fa .Bool (false );
_gfg ._ege .BandFmts =_f .NewCT_BandFmts ();for _bbda :=0;_bbda < 15;_bbda ++{_ade :=_f .NewCT_BandFmt ();_ade .Idx .ValAttr =uint32 (_bbda );_ade .SpPr =_e .NewCT_ShapeProperties ();_cddb :=_a .MakeShapeProperties (_ade .SpPr );_cddb .SetSolidFill (_gfg .nextColor (_bbda ));
_gfg ._ege .BandFmts .BandFmt =append (_gfg ._ege .BandFmts .BandFmt ,_ade );};};

// Values returns the value data source.
func (_efd RadarChartSeries )Values ()NumberDataSource {if _efd ._gae .Val ==nil {_efd ._gae .Val =_f .NewCT_NumDataSource ();};return MakeNumberDataSource (_efd ._gae .Val );};func (_gafea StockChart )AddAxis (axis Axis ){_fbge :=_f .NewCT_UnsignedInt ();
_fbge .ValAttr =axis .AxisID ();_gafea ._gbef .AxId =append (_gafea ._gbef .AxId ,_fbge );};func (_gdd ValueAxis )SetPosition (p _f .ST_AxPos ){_gdd ._gea .AxPos =_f .NewCT_AxPos ();_gdd ._gea .AxPos .ValAttr =p ;};

// SetText sets the series text
func (_fcdc LineChartSeries )SetText (s string ){_fcdc ._aeb .Tx =_f .NewCT_SerTx ();_fcdc ._aeb .Tx .SerTxChoice .V =&s ;};

// Marker returns the marker properties.
func (_dbb LineChartSeries )Marker ()Marker {if _dbb ._aeb .Marker ==nil {_dbb ._aeb .Marker =_f .NewCT_Marker ();};return MakeMarker (_dbb ._aeb .Marker );};func (_bdgab ValueAxis )SetMajorTickMark (m _f .ST_TickMark ){if m ==_f .ST_TickMarkUnset {_bdgab ._gea .MajorTickMark =nil ;
}else {_bdgab ._gea .MajorTickMark =_f .NewCT_TickMark ();_bdgab ._gea .MajorTickMark .ValAttr =m ;};};

// X returns the inner wrapped XML type.
func (_bdgd ValueAxis )X ()*_f .CT_ValAx {return _bdgd ._gea };

// X returns the inner wrapped XML type.
func (_bdg BarChart )X ()*_f .CT_BarChart {return _bdg ._bbe };

// Order returns the order of the series
func (_adcb ScatterChartSeries )Order ()uint32 {return _adcb ._bbbb .Order .ValAttr };

// Order returns the order of the series
func (_addd SurfaceChartSeries )Order ()uint32 {return _addd ._fecg .Order .ValAttr };func (_caca Legend )SetOverlay (b bool ){_caca ._ceg .Overlay =_f .NewCT_Boolean ();_caca ._ceg .Overlay .ValAttr =_fa .Bool (b );};

// AddLegend adds a legend to a chart, replacing any existing legend.
func (_fad Chart )AddLegend ()Legend {_fad ._edc .Chart .Legend =_f .NewCT_Legend ();_bef :=MakeLegend (_fad ._edc .Chart .Legend );_bef .InitializeDefaults ();return _bef ;};

// SetNumberReference is used to set the source data to a range of cells containing
// numbers.
func (_dac CategoryAxisDataSource )SetNumberReference (s string ){_dac ._ffb .AxDataSourceChoice =_f .NewCT_AxDataSourceChoice ();_dac ._ffb .AxDataSourceChoice .NumRef =_f .NewCT_NumRef ();_dac ._ffb .AxDataSourceChoice .NumRef .F =s ;};type SeriesAxis struct{_abd *_f .CT_SerAx };


// AddSeries adds a default series to a bar chart.
func (_cgg BarChart )AddSeries ()BarChartSeries {_bbd :=_cgg .nextColor (len (_cgg ._bbe .Ser ));_cc :=_f .NewCT_BarSer ();_cgg ._bbe .Ser =append (_cgg ._bbe .Ser ,_cc );_cc .Idx .ValAttr =uint32 (len (_cgg ._bbe .Ser )-1);_cc .Order .ValAttr =uint32 (len (_cgg ._bbe .Ser )-1);
_dbd :=BarChartSeries {_cc };_dbd .InitializeDefaults ();_dbd .Properties ().SetSolidFill (_bbd );return _dbd ;};func (_bc Bar3DChart )AddAxis (axis Axis ){_ega :=_f .NewCT_UnsignedInt ();_ega .ValAttr =axis .AxisID ();_bc ._db .AxId =append (_bc ._db .AxId ,_ega );
};func (_ae Legend )InitializeDefaults (){_ae .SetPosition (_f .ST_LegendPosR );_ae .SetOverlay (false );_ae .Properties ().SetNoFill ();_ae .Properties ().LineProperties ().SetNoFill ();};func (_ead ValueAxis )Properties ()_a .ShapeProperties {if _ead ._gea .SpPr ==nil {_ead ._gea .SpPr =_e .NewCT_ShapeProperties ();
};return _a .MakeShapeProperties (_ead ._gea .SpPr );};

// MakeAxisDataSource constructs an AxisDataSource wrapper.
func MakeAxisDataSource (x *_f .CT_AxDataSource )CategoryAxisDataSource {return CategoryAxisDataSource {x };};func (_gebf Marker )Properties ()_a .ShapeProperties {if _gebf ._gdbd .SpPr ==nil {_gebf ._gdbd .SpPr =_e .NewCT_ShapeProperties ();};return _a .MakeShapeProperties (_gebf ._gdbd .SpPr );
};

// X returns the inner wrapped XML type.
func (_fge DoughnutChart )X ()*_f .CT_DoughnutChart {return _fge ._gbd };

// X returns the inner wrapped XML type.
func (_ag AreaChart )X ()*_f .CT_AreaChart {return _ag ._fb };

// SetValues is used to set the source data to a set of values.
func (_fag CategoryAxisDataSource )SetValues (v []string ){_fag ._ffb .AxDataSourceChoice =_f .NewCT_AxDataSourceChoice ();_fag ._ffb .AxDataSourceChoice .StrLit =_f .NewCT_StrData ();_fag ._ffb .AxDataSourceChoice .StrLit .PtCount =_f .NewCT_UnsignedInt ();
_fag ._ffb .AxDataSourceChoice .StrLit .PtCount .ValAttr =uint32 (len (v ));for _bdgc ,_gfe :=range v {_fag ._ffb .AxDataSourceChoice .StrLit .Pt =append (_fag ._ffb .AxDataSourceChoice .StrLit .Pt ,&_f .CT_StrVal {IdxAttr :uint32 (_bdgc ),V :_gfe });};
};func (_ddc Title )SetText (s string ){if _ddc ._febe .Tx ==nil {_ddc ._febe .Tx =_f .NewCT_Tx ();};if _ddc ._febe .Tx .TxChoice .Rich ==nil {_ddc ._febe .Tx .TxChoice .Rich =_e .NewCT_TextBody ();};var _fbba *_e .CT_TextParagraph ;if len (_ddc ._febe .Tx .TxChoice .Rich .P )==0{_fbba =_e .NewCT_TextParagraph ();
_ddc ._febe .Tx .TxChoice .Rich .P =[]*_e .CT_TextParagraph {_fbba };}else {_fbba =_ddc ._febe .Tx .TxChoice .Rich .P [0];};var _dff *_e .EG_TextRun ;if len (_fbba .EG_TextRun )==0{_dff =_e .NewEG_TextRun ();_fbba .EG_TextRun =[]*_e .EG_TextRun {_dff };
}else {_dff =_fbba .EG_TextRun [0];};if _dff .TextRunChoice .R ==nil {_dff .TextRunChoice .R =_e .NewCT_RegularTextRun ();};_dff .TextRunChoice .R .T =s ;};var NullAxis Axis =nullAxis (0);

// SetIndex sets the index of the series
func (_cfcb LineChartSeries )SetIndex (idx uint32 ){_cfcb ._aeb .Idx .ValAttr =idx };

// InitializeDefaults initializes a Bubble chart series to the default values.
func (_ggd BubbleChartSeries )InitializeDefaults (){};

// AddAxis adds an axis to a Surface chart.
func (_bgdf Surface3DChart )AddAxis (axis Axis ){_bcg :=_f .NewCT_UnsignedInt ();_bcg .ValAttr =axis .AxisID ();_bgdf ._edcf .AxId =append (_bgdf ._edcf .AxId ,_bcg );};

// AddSeries adds a default series to a Scatter chart.
func (_dabfc ScatterChart )AddSeries ()ScatterChartSeries {_gab :=_dabfc .nextColor (len (_dabfc ._cfae .Ser ));_egaa :=_f .NewCT_ScatterSer ();_dabfc ._cfae .Ser =append (_dabfc ._cfae .Ser ,_egaa );_egaa .Idx .ValAttr =uint32 (len (_dabfc ._cfae .Ser )-1);
_egaa .Order .ValAttr =uint32 (len (_dabfc ._cfae .Ser )-1);_dgbd :=ScatterChartSeries {_egaa };_dgbd .InitializeDefaults ();_dgbd .Marker ().Properties ().LineProperties ().SetSolidFill (_gab );_dgbd .Marker ().Properties ().SetSolidFill (_gab );return _dgbd ;
};func (_cgcb Chart )AddSeriesAxis ()SeriesAxis {_eaa :=_f .NewCT_SerAx ();if _cgcb ._edc .Chart .PlotArea .PlotAreaChoice1 ==nil {_cgcb ._edc .Chart .PlotArea .PlotAreaChoice1 =[]*_f .CT_PlotAreaChoice1 {};};_eaa .AxId =_f .NewCT_UnsignedInt ();_eaa .AxId .ValAttr =0x7FFFFFFF&_gb .Uint32 ();
_cgcb ._edc .Chart .PlotArea .PlotAreaChoice1 =append (_cgcb ._edc .Chart .PlotArea .PlotAreaChoice1 ,&_f .CT_PlotAreaChoice1 {SerAx :_eaa });_eaa .Delete =_f .NewCT_Boolean ();_eaa .Delete .ValAttr =_fa .Bool (false );_bcdf :=MakeSeriesAxis (_eaa );_bcdf .InitializeDefaults ();
return _bcdf ;};

// AddAxis adds an axis to a Scatter chart.
func (_bega ScatterChart )AddAxis (axis Axis ){_ecd :=_f .NewCT_UnsignedInt ();_ecd .ValAttr =axis .AxisID ();_bega ._cfae .AxId =append (_bega ._cfae .AxId ,_ecd );};

// CategoryAxis returns the category data source.
func (_cdd PieChartSeries )CategoryAxis ()CategoryAxisDataSource {if _cdd ._dcc .Cat ==nil {_cdd ._dcc .Cat =_f .NewCT_AxDataSource ();};return MakeAxisDataSource (_cdd ._dcc .Cat );};func (_bbed DataLabels )SetShowValue (b bool ){_bbed .ensureChoice ();
_bbed ._ecc .DLblsChoice .ShowVal =_f .NewCT_Boolean ();_bbed ._ecc .DLblsChoice .ShowVal .ValAttr =_fa .Bool (b );};func (_dc AreaChart )AddAxis (axis Axis ){_ged :=_f .NewCT_UnsignedInt ();_ged .ValAttr =axis .AxisID ();_dc ._fb .AxId =append (_dc ._fb .AxId ,_ged );
};

// AddBubbleChart adds a new bubble chart.
func (_beb Chart )AddBubbleChart ()BubbleChart {_cde :=_f .NewCT_PlotAreaChoice ();_beb ._edc .Chart .PlotArea .PlotAreaChoice =append (_beb ._edc .Chart .PlotArea .PlotAreaChoice ,_cde );_cde .BubbleChart =_f .NewCT_BubbleChart ();_dfa :=BubbleChart {_add :_cde .BubbleChart };
_dfa .InitializeDefaults ();return _dfa ;};

// X returns the inner wrapped XML type.
func (_cdg Title )X ()*_f .CT_Title {return _cdg ._febe };func MakeValueAxis (x *_f .CT_ValAx )ValueAxis {return ValueAxis {x }};

// Values returns the value data source.
func (_ec BarChartSeries )Values ()NumberDataSource {if _ec ._cee .Val ==nil {_ec ._cee .Val =_f .NewCT_NumDataSource ();};return MakeNumberDataSource (_ec ._cee .Val );};

// AddSeries adds a default series to a Surface chart.
func (_bcdff Surface3DChart )AddSeries ()SurfaceChartSeries {_fbc :=_bcdff .nextColor (len (_bcdff ._edcf .Ser ));_cgb :=_f .NewCT_SurfaceSer ();_bcdff ._edcf .Ser =append (_bcdff ._edcf .Ser ,_cgb );_cgb .Idx .ValAttr =uint32 (len (_bcdff ._edcf .Ser )-1);
_cgb .Order .ValAttr =uint32 (len (_bcdff ._edcf .Ser )-1);_fdab :=SurfaceChartSeries {_cgb };_fdab .InitializeDefaults ();_fdab .Properties ().LineProperties ().SetSolidFill (_fbc );return _fdab ;};

// InitializeDefaults the Bubble chart to its defaults
func (_de BubbleChart )InitializeDefaults (){};

// X returns the inner wrapped XML type.
func (_fff RadarChartSeries )X ()*_f .CT_RadarSer {return _fff ._gae };type LineChart struct{chartBase ;_gff *_f .CT_LineChart ;};func (_dacg ScatterChartSeries )CategoryAxis ()CategoryAxisDataSource {if _dacg ._bbbb .XVal ==nil {_dacg ._bbbb .XVal =_f .NewCT_AxDataSource ();
};return MakeAxisDataSource (_dacg ._bbbb .XVal );};

// Properties returns the line chart series shape properties.
func (_gfb LineChartSeries )Properties ()_a .ShapeProperties {if _gfb ._aeb .SpPr ==nil {_gfb ._aeb .SpPr =_e .NewCT_ShapeProperties ();};return _a .MakeShapeProperties (_gfb ._aeb .SpPr );};

// SetText sets the series text.
func (_bcd BarChartSeries )SetText (s string ){_bcd ._cee .Tx =_f .NewCT_SerTx ();_bcd ._cee .Tx .SerTxChoice .V =&s ;};

// AddSeries adds a default series to an Pie3D chart.
func (_cab Pie3DChart )AddSeries ()PieChartSeries {_geba :=_f .NewCT_PieSer ();_cab ._bgg .Ser =append (_cab ._bgg .Ser ,_geba );_geba .Idx .ValAttr =uint32 (len (_cab ._bgg .Ser )-1);_geba .Order .ValAttr =uint32 (len (_cab ._bgg .Ser )-1);_beed :=PieChartSeries {_geba };
_beed .InitializeDefaults ();return _beed ;};

// X returns the inner wrapped XML type.
func (_beeg PieChartSeries )X ()*_f .CT_PieSer {return _beeg ._dcc };

// Labels returns the data label properties.
func (_bccf ScatterChartSeries )Labels ()DataLabels {if _bccf ._bbbb .DLbls ==nil {_bccf ._bbbb .DLbls =_f .NewCT_DLbls ();};return MakeDataLabels (_bccf ._bbbb .DLbls );};func (_gdc NumberDataSource )SetReference (s string ){_gdc .ensureChoice ();if _gdc ._dda .NumDataSourceChoice .NumRef ==nil {_gdc ._dda .NumDataSourceChoice .NumRef =_f .NewCT_NumRef ();
};_gdc ._dda .NumDataSourceChoice .NumRef .F =s ;};

// X returns the inner wrapped XML type.
func (_fcg GridLines )X ()*_f .CT_ChartLines {return _fcg ._edgb };

// Properties returns the line chart series shape properties.
func (_deb SurfaceChartSeries )Properties ()_a .ShapeProperties {if _deb ._fecg .SpPr ==nil {_deb ._fecg .SpPr =_e .NewCT_ShapeProperties ();};return _a .MakeShapeProperties (_deb ._fecg .SpPr );};

// SetText sets the series text
func (_dcb ScatterChartSeries )SetText (s string ){_dcb ._bbbb .Tx =_f .NewCT_SerTx ();_dcb ._bbbb .Tx .SerTxChoice .V =&s ;};

// Properties returns the bar chart series shape properties.
func (_egfd PieChartSeries )Properties ()_a .ShapeProperties {if _egfd ._dcc .SpPr ==nil {_egfd ._dcc .SpPr =_e .NewCT_ShapeProperties ();};return _a .MakeShapeProperties (_egfd ._dcc .SpPr );};

// X returns the inner wrapped XML type.
func (_bg Legend )X ()*_f .CT_Legend {return _bg ._ceg };

// X returns the inner wrapped XML type.
func (_dbbbe SurfaceChartSeries )X ()*_f .CT_SurfaceSer {return _dbbbe ._fecg };

// SetOrder sets the order of the series
func (_aadg LineChartSeries )SetOrder (idx uint32 ){_aadg ._aeb .Order .ValAttr =idx };func (_gd CategoryAxis )SetMinorTickMark (m _f .ST_TickMark ){if m ==_f .ST_TickMarkUnset {_gd ._gf .MinorTickMark =nil ;}else {_gd ._gf .MinorTickMark =_f .NewCT_TickMark ();
_gd ._gf .MinorTickMark .ValAttr =m ;};};

// AddCategoryAxis adds a category axis.
func (_gaf Chart )AddCategoryAxis ()CategoryAxis {_cfc :=_f .NewCT_CatAx ();if _gaf ._edc .Chart .PlotArea .PlotAreaChoice1 ==nil {_gaf ._edc .Chart .PlotArea .PlotAreaChoice1 =[]*_f .CT_PlotAreaChoice1 {};};_cfc .AxId =_f .NewCT_UnsignedInt ();_cfc .AxId .ValAttr =0x7FFFFFFF&_gb .Uint32 ();
_gaf ._edc .Chart .PlotArea .PlotAreaChoice1 =append (_gaf ._edc .Chart .PlotArea .PlotAreaChoice1 ,&_f .CT_PlotAreaChoice1 {CatAx :_cfc });_cfc .Auto =_f .NewCT_Boolean ();_cfc .Auto .ValAttr =_fa .Bool (true );_cfc .Delete =_f .NewCT_Boolean ();_cfc .Delete .ValAttr =_fa .Bool (false );
_gcc :=MakeCategoryAxis (_cfc );_gcc .InitializeDefaults ();return _gcc ;};func (_abb Title )InitializeDefaults (){_abb .SetText ("\u0054\u0069\u0074l\u0065");_abb .RunProperties ().SetSize (16*_fc .Point );_abb .RunProperties ().SetSolidFill (_gg .Black );
_abb .RunProperties ().SetFont ("\u0043\u0061\u006c\u0069\u0062\u0020\u0072\u0069");_abb .RunProperties ().SetBold (false );};

// AddLine3DChart adds a new 3D line chart to a chart.
func (_gadc Chart )AddLine3DChart ()Line3DChart {_ded (_gadc ._edc .Chart );_ccg :=_f .NewCT_PlotAreaChoice ();_gadc ._edc .Chart .PlotArea .PlotAreaChoice =append (_gadc ._edc .Chart .PlotArea .PlotAreaChoice ,_ccg );_ccg .Line3DChart =_f .NewCT_Line3DChart ();
_ccg .Line3DChart .Grouping =_f .NewCT_Grouping ();_ccg .Line3DChart .Grouping .ValAttr =_f .ST_GroupingStandard ;return Line3DChart {_aaf :_ccg .Line3DChart };};

// X returns the inner wrapped XML type.
func (_dcd AreaChartSeries )X ()*_f .CT_AreaSer {return _dcd ._fbg };func (_ddac SeriesAxis )AxisID ()uint32 {return _ddac ._abd .AxId .ValAttr };func (_cef Title )ParagraphProperties ()_a .ParagraphProperties {if _cef ._febe .Tx ==nil {_cef .SetText ("");
};if _cef ._febe .Tx .TxChoice .Rich .P [0].PPr ==nil {_cef ._febe .Tx .TxChoice .Rich .P [0].PPr =_e .NewCT_TextParagraphProperties ();};return _a .MakeParagraphProperties (_cef ._febe .Tx .TxChoice .Rich .P [0].PPr );};

// AddPieChart adds a new pie chart to a chart.
func (_fagd Chart )AddPieChart ()PieChart {_acba :=_f .NewCT_PlotAreaChoice ();_fagd ._edc .Chart .PlotArea .PlotAreaChoice =append (_fagd ._edc .Chart .PlotArea .PlotAreaChoice ,_acba );_acba .PieChart =_f .NewCT_PieChart ();_fe :=PieChart {_gbe :_acba .PieChart };
_fe .InitializeDefaults ();return _fe ;};

// RadarChartSeries is a series to be used on an Radar chart.
type RadarChartSeries struct{_gae *_f .CT_RadarSer };

// SetIndex sets the index of the series
func (_gfea ScatterChartSeries )SetIndex (idx uint32 ){_gfea ._bbbb .Idx .ValAttr =idx };func (_aac DataLabels )SetShowLegendKey (b bool ){_aac .ensureChoice ();_aac ._ecc .DLblsChoice .ShowLegendKey =_f .NewCT_Boolean ();_aac ._ecc .DLblsChoice .ShowLegendKey .ValAttr =_fa .Bool (b );
};

// X returns the inner wrapped XML type.
func (_aea SurfaceChart )X ()*_f .CT_SurfaceChart {return _aea ._ege };

// Values returns the value data source.
func (_ca BubbleChartSeries )Values ()NumberDataSource {if _ca ._efc .YVal ==nil {_ca ._efc .YVal =_f .NewCT_NumDataSource ();};return MakeNumberDataSource (_ca ._efc .YVal );};type ScatterChart struct{chartBase ;_cfae *_f .CT_ScatterChart ;};

// AddRadarChart adds a new radar chart to a chart.
func (_fafg Chart )AddRadarChart ()RadarChart {_cea :=_f .NewCT_PlotAreaChoice ();_fafg ._edc .Chart .PlotArea .PlotAreaChoice =append (_fafg ._edc .Chart .PlotArea .PlotAreaChoice ,_cea );_cea .RadarChart =_f .NewCT_RadarChart ();_fgb :=RadarChart {_ecg :_cea .RadarChart };
_fgb .InitializeDefaults ();return _fgb ;};

// AddSeries adds a default series to an area chart.
func (_eb Area3DChart )AddSeries ()AreaChartSeries {_c :=_eb .nextColor (len (_eb ._d .Ser ));_bd :=_f .NewCT_AreaSer ();_eb ._d .Ser =append (_eb ._d .Ser ,_bd );_bd .Idx .ValAttr =uint32 (len (_eb ._d .Ser )-1);_bd .Order .ValAttr =uint32 (len (_eb ._d .Ser )-1);
_ff :=AreaChartSeries {_bd };_ff .InitializeDefaults ();_ff .Properties ().SetSolidFill (_c );return _ff ;};

// AddScatterChart adds a scatter (X/Y) chart.
func (_fbd Chart )AddScatterChart ()ScatterChart {_eda :=_f .NewCT_PlotAreaChoice ();_fbd ._edc .Chart .PlotArea .PlotAreaChoice =append (_fbd ._edc .Chart .PlotArea .PlotAreaChoice ,_eda );_eda .ScatterChart =_f .NewCT_ScatterChart ();_geb :=ScatterChart {_cfae :_eda .ScatterChart };
_geb .InitializeDefaults ();return _geb ;};func (_dad CategoryAxis )Properties ()_a .ShapeProperties {if _dad ._gf .SpPr ==nil {_dad ._gf .SpPr =_e .NewCT_ShapeProperties ();};return _a .MakeShapeProperties (_dad ._gf .SpPr );};func (_ebe ScatterChartSeries )SetSmooth (b bool ){_ebe ._bbbb .Smooth =_f .NewCT_Boolean ();
_ebe ._bbbb .Smooth .ValAttr =&b ;};func (_fda ScatterChartSeries )Values ()NumberDataSource {if _fda ._bbbb .YVal ==nil {_fda ._bbbb .YVal =_f .NewCT_NumDataSource ();};return MakeNumberDataSource (_fda ._bbbb .YVal );};

// AddPieOfPieChart adds a new pie chart to a chart.
func (_edg Chart )AddPieOfPieChart ()PieOfPieChart {_feg :=_f .NewCT_PlotAreaChoice ();_edg ._edc .Chart .PlotArea .PlotAreaChoice =append (_edg ._edc .Chart .PlotArea .PlotAreaChoice ,_feg );_feg .OfPieChart =_f .NewCT_OfPieChart ();_bee :=PieOfPieChart {_dadcg :_feg .OfPieChart };
_bee .InitializeDefaults ();return _bee ;};

// Index returns the index of the series
func (_bccb ScatterChartSeries )Index ()uint32 {return _bccb ._bbbb .Idx .ValAttr };

// X returns the inner wrapped XML type.
func (_faf Chart )X ()*_f .ChartSpace {return _faf ._edc };

// Properties returns the chart's shape properties.
func (_dbc Chart )Properties ()_a .ShapeProperties {if _dbc ._edc .SpPr ==nil {_dbc ._edc .SpPr =_e .NewCT_ShapeProperties ();};return _a .MakeShapeProperties (_dbc ._edc .SpPr );};

// AddDateAxis adds a value axis to the chart.
func (_bfec Chart )AddDateAxis ()DateAxis {_ebd :=_f .NewCT_DateAx ();if _bfec ._edc .Chart .PlotArea .PlotAreaChoice1 ==nil {_bfec ._edc .Chart .PlotArea .PlotAreaChoice1 =[]*_f .CT_PlotAreaChoice1 {};};_ebd .AxId =_f .NewCT_UnsignedInt ();_ebd .AxId .ValAttr =0x7FFFFFFF&_gb .Uint32 ();
_bfec ._edc .Chart .PlotArea .PlotAreaChoice1 =append (_bfec ._edc .Chart .PlotArea .PlotAreaChoice1 ,&_f .CT_PlotAreaChoice1 {DateAx :_ebd });_ebd .Delete =_f .NewCT_Boolean ();_ebd .Delete .ValAttr =_fa .Bool (false );_ebd .Scaling =_f .NewCT_Scaling ();
_ebd .Scaling .Orientation =_f .NewCT_Orientation ();_ebd .Scaling .Orientation .ValAttr =_f .ST_OrientationMinMax ;_ebd .AxSharedChoice =&_f .EG_AxSharedChoice {};_ebd .AxSharedChoice .Crosses =_f .NewCT_Crosses ();_ebd .AxSharedChoice .Crosses .ValAttr =_f .ST_CrossesAutoZero ;
_bfdb :=DateAxis {_ebd };_bfdb .MajorGridLines ().Properties ().LineProperties ().SetSolidFill (_gg .LightGray );_bfdb .SetMajorTickMark (_f .ST_TickMarkOut );_bfdb .SetMinorTickMark (_f .ST_TickMarkIn );_bfdb .SetTickLabelPosition (_f .ST_TickLblPosNextTo );
_bfdb .Properties ().LineProperties ().SetSolidFill (_gg .Black );_bfdb .SetPosition (_f .ST_AxPosL );return _bfdb ;};

// SetText sets the series text
func (_bead SurfaceChartSeries )SetText (s string ){_bead ._fecg .Tx =_f .NewCT_SerTx ();_bead ._fecg .Tx .SerTxChoice .V =&s ;};

// Values returns the bubble size data source.
func (_ebb BubbleChartSeries )BubbleSizes ()NumberDataSource {if _ebb ._efc .BubbleSize ==nil {_ebb ._efc .BubbleSize =_f .NewCT_NumDataSource ();};return MakeNumberDataSource (_ebb ._efc .BubbleSize );};

// X returns the inner wrapped XML type.
func (_ece Surface3DChart )X ()*_f .CT_Surface3DChart {return _ece ._edcf };

// InitializeDefaults the bar chart to its defaults
func (_cbdd RadarChart )InitializeDefaults (){_cbdd ._ecg .RadarStyle .ValAttr =_f .ST_RadarStyleMarker };

// AddAxis adds an axis to a line chart.
func (_edac Line3DChart )AddAxis (axis Axis ){_ffbc :=_f .NewCT_UnsignedInt ();_ffbc .ValAttr =axis .AxisID ();_edac ._aaf .AxId =append (_edac ._aaf .AxId ,_ffbc );};func (_bbbd SurfaceChartSeries )Values ()NumberDataSource {if _bbbd ._fecg .Val ==nil {_bbbd ._fecg .Val =_f .NewCT_NumDataSource ();
};_aae :=MakeNumberDataSource (_bbbd ._fecg .Val );_aae .CreateEmptyNumberCache ();return _aae ;};

// SetHoleSize controls the hole size in the pie chart and is measured in percent.
func (_fage DoughnutChart )SetHoleSize (pct uint8 ){if _fage ._gbd .HoleSize ==nil {_fage ._gbd .HoleSize =_f .NewCT_HoleSize ();};if _fage ._gbd .HoleSize .ValAttr ==nil {_fage ._gbd .HoleSize .ValAttr =&_f .ST_HoleSize {};};_fage ._gbd .HoleSize .ValAttr .ST_HoleSizeUByte =&pct ;
};func (_fcf ValueAxis )SetCrosses (axis Axis ){_fcf ._gea .CrossAx .ValAttr =axis .AxisID ()};

// InitializeDefaults the Stock chart to its defaults
func (_bgd StockChart )InitializeDefaults (){_bgd ._gbef .HiLowLines =_f .NewCT_ChartLines ();_bgd ._gbef .UpDownBars =_f .NewCT_UpDownBars ();_bgd ._gbef .UpDownBars .GapWidth =_f .NewCT_GapAmount ();_bgd ._gbef .UpDownBars .GapWidth .ValAttr =&_f .ST_GapAmount {};
_bgd ._gbef .UpDownBars .GapWidth .ValAttr .ST_GapAmountUShort =_fa .Uint16 (150);_bgd ._gbef .UpDownBars .UpBars =_f .NewCT_UpDownBar ();_bgd ._gbef .UpDownBars .DownBars =_f .NewCT_UpDownBar ();};func (_age DateAxis )SetMajorTickMark (m _f .ST_TickMark ){if m ==_f .ST_TickMarkUnset {_age ._aca .MajorTickMark =nil ;
}else {_age ._aca .MajorTickMark =_f .NewCT_TickMark ();_age ._aca .MajorTickMark .ValAttr =m ;};};

// AddSeries adds a default series to an Doughnut chart.
func (_cad DoughnutChart )AddSeries ()PieChartSeries {_dgf :=_f .NewCT_PieSer ();_cad ._gbd .Ser =append (_cad ._gbd .Ser ,_dgf );_dgf .Idx .ValAttr =uint32 (len (_cad ._gbd .Ser )-1);_dgf .Order .ValAttr =uint32 (len (_cad ._gbd .Ser )-1);_fbf :=PieChartSeries {_dgf };
_fbf .InitializeDefaults ();return _fbf ;};func MakeSeriesAxis (x *_f .CT_SerAx )SeriesAxis {return SeriesAxis {x }};type DataLabels struct{_ecc *_f .CT_DLbls };

// BubbleChartSeries is a series to be used on a Bubble chart.
type BubbleChartSeries struct{_efc *_f .CT_BubbleSer };

// Properties returns the bar chart series shape properties.
func (_bbb BarChartSeries )Properties ()_a .ShapeProperties {if _bbb ._cee .SpPr ==nil {_bbb ._cee .SpPr =_e .NewCT_ShapeProperties ();};return _a .MakeShapeProperties (_bbb ._cee .SpPr );};

// CategoryAxis returns the category data source.
func (_gfa RadarChartSeries )CategoryAxis ()CategoryAxisDataSource {if _gfa ._gae .Cat ==nil {_gfa ._gae .Cat =_f .NewCT_AxDataSource ();};return MakeAxisDataSource (_gfa ._gae .Cat );};func (_fbb Legend )SetPosition (p _f .ST_LegendPos ){if p ==_f .ST_LegendPosUnset {_fbb ._ceg .LegendPos =nil ;
}else {_fbb ._ceg .LegendPos =_f .NewCT_LegendPos ();_fbb ._ceg .LegendPos .ValAttr =p ;};};func (_fdde chartBase )nextColor (_aga int )_gg .Color {return _eae [_aga %len (_eae )]};func (_gcd LineChartSeries )Values ()NumberDataSource {if _gcd ._aeb .Val ==nil {_gcd ._aeb .Val =_f .NewCT_NumDataSource ();
};return MakeNumberDataSource (_gcd ._aeb .Val );};type Line3DChart struct{chartBase ;_aaf *_f .CT_Line3DChart ;};

// X returns the inner wrapped XML type.
func (_bfc PieChart )X ()*_f .CT_PieChart {return _bfc ._gbe };func (_ecb SurfaceChartSeries )InitializeDefaults (){_ecb .Properties ().LineProperties ().SetWidth (1*_fc .Point );_ecb .Properties ().LineProperties ().SetSolidFill (_gg .Black );_ecb .Properties ().LineProperties ().SetJoin (_a .LineJoinRound );
};func MakeChart (x *_f .ChartSpace )Chart {return Chart {x }};func (_fccc DateAxis )Properties ()_a .ShapeProperties {if _fccc ._aca .SpPr ==nil {_fccc ._aca .SpPr =_e .NewCT_ShapeProperties ();};return _a .MakeShapeProperties (_fccc ._aca .SpPr );};

// X returns the inner wrapped XML type.
func (_gfca StockChart )X ()*_f .CT_StockChart {return _gfca ._gbef };

// AddSeries adds a default series to a Bubble chart.
func (_df BubbleChart )AddSeries ()BubbleChartSeries {_cf :=_df .nextColor (len (_df ._add .Ser ));_cfe :=_f .NewCT_BubbleSer ();_df ._add .Ser =append (_df ._add .Ser ,_cfe );_cfe .Idx .ValAttr =uint32 (len (_df ._add .Ser )-1);_cfe .Order .ValAttr =uint32 (len (_df ._add .Ser )-1);
_bdb :=BubbleChartSeries {_cfe };_bdb .InitializeDefaults ();_bdb .Properties ().SetSolidFill (_cf );return _bdb ;};

// Labels returns the data label properties.
func (_agaf LineChartSeries )Labels ()DataLabels {if _agaf ._aeb .DLbls ==nil {_agaf ._aeb .DLbls =_f .NewCT_DLbls ();};return MakeDataLabels (_agaf ._aeb .DLbls );};

// InitializeDefaults the bar chart to its defaults
func (_edga PieOfPieChart )InitializeDefaults (){_edga ._dadcg .VaryColors =_f .NewCT_Boolean ();_edga ._dadcg .VaryColors .ValAttr =_fa .Bool (true );_edga .SetType (_f .ST_OfPieTypePie );_edga ._dadcg .SecondPieSize =_f .NewCT_SecondPieSize ();_edga ._dadcg .SecondPieSize .ValAttr =&_f .ST_SecondPieSize {};
_edga ._dadcg .SecondPieSize .ValAttr .ST_SecondPieSizeUShort =_fa .Uint16 (75);_bbg :=_f .NewCT_ChartLines ();_bbg .SpPr =_e .NewCT_ShapeProperties ();_dde :=_a .MakeShapeProperties (_bbg .SpPr );_dde .LineProperties ().SetSolidFill (_gg .Auto );_edga ._dadcg .SerLines =append (_edga ._dadcg .SerLines ,_bbg );
};

// InitializeDefaults the bar chart to its defaults
func (_ac Bar3DChart )InitializeDefaults (){_ac .SetDirection (_f .ST_BarDirCol )};

// SetOrder sets the order of the series
func (_bcdee ScatterChartSeries )SetOrder (idx uint32 ){_bcdee ._bbbb .Order .ValAttr =idx };

// SetText sets the series text.
func (_cfb BubbleChartSeries )SetText (s string ){_cfb ._efc .Tx =_f .NewCT_SerTx ();_cfb ._efc .Tx .SerTxChoice .V =&s ;};func MakeDataLabels (x *_f .CT_DLbls )DataLabels {return DataLabels {x }};func (_faac LineChartSeries )SetSmooth (b bool ){_faac ._aeb .Smooth =_f .NewCT_Boolean ();
_faac ._aeb .Smooth .ValAttr =&b ;};

// RemoveLegend removes the legend if the chart has one.
func (_efag Chart )RemoveLegend (){_efag ._edc .Chart .Legend =nil };

// AddBar3DChart adds a new 3D bar chart to a chart.
func (_agd Chart )AddBar3DChart ()Bar3DChart {_ded (_agd ._edc .Chart );_efb :=_f .NewCT_PlotAreaChoice ();_agd ._edc .Chart .PlotArea .PlotAreaChoice =append (_agd ._edc .Chart .PlotArea .PlotAreaChoice ,_efb );_efb .Bar3DChart =_f .NewCT_Bar3DChart ();
_efb .Bar3DChart .Grouping =_f .NewCT_BarGrouping ();_efb .Bar3DChart .Grouping .ValAttr =_f .ST_BarGroupingStandard ;_beg :=Bar3DChart {_db :_efb .Bar3DChart };_beg .InitializeDefaults ();return _beg ;};func (_gadcb ValueAxis )AxisID ()uint32 {return _gadcb ._gea .AxId .ValAttr };
func (_gfec NumberDataSource )ensureChoice (){if _gfec ._dda .NumDataSourceChoice ==nil {_gfec ._dda .NumDataSourceChoice =_f .NewCT_NumDataSourceChoice ();};};func (_cbd CategoryAxis )SetCrosses (axis Axis ){_cbd ._gf .AxSharedChoice =_f .NewEG_AxSharedChoice ();
_cbd ._gf .AxSharedChoice .Crosses =_f .NewCT_Crosses ();_cbd ._gf .AxSharedChoice .Crosses .ValAttr =_f .ST_CrossesAutoZero ;_cbd ._gf .CrossAx .ValAttr =axis .AxisID ();};func (_ccf CategoryAxis )SetPosition (p _f .ST_AxPos ){_ccf ._gf .AxPos =_f .NewCT_AxPos ();
_ccf ._gf .AxPos .ValAttr =p ;};type chartBase struct{};

// X returns the inner wrapped XML type.
func (_gfdb RadarChart )X ()*_f .CT_RadarChart {return _gfdb ._ecg };type NumberDataSource struct{_dda *_f .CT_NumDataSource };

// RemoveTitle removes any existing title from the chart.
func (_ba Chart )RemoveTitle (){_ba ._edc .Chart .Title =nil ;_ba ._edc .Chart .AutoTitleDeleted =_f .NewCT_Boolean ();_ba ._edc .Chart .AutoTitleDeleted .ValAttr =_fa .Bool (true );};func (_ada CategoryAxis )SetMajorTickMark (m _f .ST_TickMark ){if m ==_f .ST_TickMarkUnset {_ada ._gf .MajorTickMark =nil ;
}else {_ada ._gf .MajorTickMark =_f .NewCT_TickMark ();_ada ._gf .MajorTickMark .ValAttr =m ;};};func (_aba LineChartSeries )InitializeDefaults (){_aba .Properties ().LineProperties ().SetWidth (1*_fc .Point );_aba .Properties ().LineProperties ().SetSolidFill (_gg .Black );
_aba .Properties ().LineProperties ().SetJoin (_a .LineJoinRound );_aba .Marker ().SetSymbol (_f .ST_MarkerStyleNone );_aba .Labels ().SetShowLegendKey (false );_aba .Labels ().SetShowValue (false );_aba .Labels ().SetShowPercent (false );_aba .Labels ().SetShowCategoryName (false );
_aba .Labels ().SetShowSeriesName (false );_aba .Labels ().SetShowLeaderLines (false );};

// Values returns the value data source.
func (_ce AreaChartSeries )Values ()NumberDataSource {if _ce ._fbg .Val ==nil {_ce ._fbg .Val =_f .NewCT_NumDataSource ();};return MakeNumberDataSource (_ce ._fbg .Val );};

// Surface3DChart is a 3D view of a surface chart.
type Surface3DChart struct{chartBase ;_edcf *_f .CT_Surface3DChart ;};

// StockChart is a 2D Stock chart.
type StockChart struct{chartBase ;_gbef *_f .CT_StockChart ;};

// SetText sets the series text.
func (_gee RadarChartSeries )SetText (s string ){_gee ._gae .Tx =_f .NewCT_SerTx ();_gee ._gae .Tx .SerTxChoice .V =&s ;};

// X returns the inner wrapped XML type.
func (_bfaa Pie3DChart )X ()*_f .CT_Pie3DChart {return _bfaa ._bgg };