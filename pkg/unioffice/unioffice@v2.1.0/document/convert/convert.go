//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package convert ;import (_a "bytes";_c "errors";_g "fmt";_fc "github.com/unidoc/emf";_e "github.com/unidoc/unioffice/v2/color";_eg "github.com/unidoc/unioffice/v2/common/logger";_dac "github.com/unidoc/unioffice/v2/common/tempstorage";_fca "github.com/unidoc/unioffice/v2/document";
_ef "github.com/unidoc/unioffice/v2/internal/convertutils";_af "github.com/unidoc/unioffice/v2/internal/formatutils";_ag "github.com/unidoc/unioffice/v2/measurement";_bf "github.com/unidoc/unioffice/v2/schema/soo/dml";_db "github.com/unidoc/unioffice/v2/schema/soo/dml/chart";
_ffd "github.com/unidoc/unioffice/v2/schema/soo/dml/picture";_bc "github.com/unidoc/unioffice/v2/schema/soo/ofc/sharedTypes";_bg "github.com/unidoc/unioffice/v2/schema/soo/pkg/relationships";_bfc "github.com/unidoc/unioffice/v2/schema/soo/wml";_ff "github.com/unidoc/unioffice/v2/schema/urn/schemas_microsoft_com/vml";
_eb "github.com/unidoc/unioffice/v2/vmldrawing";_fb "github.com/unidoc/unipdf/v3/core";_da "github.com/unidoc/unipdf/v3/creator";_ae "github.com/unidoc/unipdf/v3/model";_ee "github.com/unidoc/unipdf/v3/textshaping";_dd "image/png";_d "io/ioutil";_be "regexp";
_b "strconv";_ga "strings";);type zoneToSkip struct{_dc *_ef .Rectangle ;_dda *_bfc .WdEG_WrapTypeChoice ;_ba uint32 ;};func (_dea *convertContext )drawPages (){for _ ,_bca :=range _dea ._cbaaa {_dea ._affc .NewPage ();_dea .drawPage (_bca );};};func (_bfge *convertContext )currentParagraphOverflowsCurrentPage ()bool {_cfcf :=_bfge ._cfeb ._bdd +_bfge ._cfeb ._agb .Top +_bfge ._cfeb ._agb .Bottom ;
_dgdf :=_bfge ._fadc ._ebc .Bottom -_bfge ._cfeb ._aeg ;if len (_bfge ._fadc ._fe )==0&&len (_bfge ._cfeb ._edg )> 0{_dgdf -=_dae ;};return _cfcf +_bfge ._cfeb ._aee > _dgdf ||_cfcf +_bfge ._cfeb ._ce > _dgdf ;};var _faad =_ggef (2.5);func _acbd (_bbedg *_fca .Document ,_egga string ,_cddgg *_bfc .CT_TblPr ,_adcf *_bfc .CT_PPrGeneral ,_dbaf *_bfc .CT_RPr )(*_bfc .CT_TblPr ,*_bfc .CT_PPrGeneral ,*_bfc .CT_RPr ){if _cddgg .TblStyle !=nil {_ccaf :=_bbedg .GetStyleByID (_egga );
if _dcbf :=_ccaf .X ();_dcbf !=nil {if _ebgf :=_dcbf .TblPr ;_ebgf !=nil {_cabbg :=_cddgg .TblBorders ;var _cffef *_bfc .CT_TblBorders ;if _ebgf .TblBorders !=nil {_cffef =_ebgf .TblBorders ;};if _cabbg ==nil {_cabbg =_cffef ;}else {if _cffef !=nil {if _cabbg .Top ==nil {_cabbg .Top =_cffef .Top ;
};if _cabbg .Bottom ==nil {_cabbg .Bottom =_cffef .Bottom ;};if _cabbg .Left ==nil {_cabbg .Left =_cffef .Left ;};if _cabbg .Right ==nil {_cabbg .Right =_cffef .Right ;};if _cabbg .InsideH ==nil {_cabbg .InsideH =_cffef .InsideH ;};if _cabbg .InsideV ==nil {_cabbg .InsideV =_cffef .InsideV ;
};};};_cddgg .TblBorders =_cabbg ;_ccdd :=_cddgg .Shd ;_feacb :=_ebgf .Shd ;if _ccdd ==nil {_ccdd =_feacb ;}else {if _feacb !=nil &&_ccdd .FillAttr ==nil {_ccdd .FillAttr =_feacb .FillAttr ;};};_cddgg .Shd =_ccdd ;_bdced :=_cddgg .TblCellMar ;_fdbgc :=_ebgf .TblCellMar ;
if _bdced ==nil {_bdced =_fdbgc ;}else {if _fdbgc !=nil &&_bdced .Left ==nil {_bdced .Left =_fdbgc .Left ;};};_cddgg .TblCellMar =_bdced ;if _cddgg .TblInd ==nil {_cddgg .TblInd =_ebgf .TblInd ;};if _cddgg .Jc ==nil {_cddgg .Jc =_ebgf .Jc ;};};if _dcbf .PPr !=nil {_adcf =_dbbf (_dcbf .PPr ,_adcf );
};if _dcbf .RPr !=nil {_dbaf =_bcag (_dcbf .RPr ,_dbaf );};if _ffaff :=_dcbf .BasedOn ;_ffaff !=nil {return _acbd (_bbedg ,_ffaff .ValAttr ,_cddgg ,_adcf ,_dbaf );};};};return _cddgg ,_adcf ,_dbaf ;};func _fccae (_begf *_bfc .CT_PPrGeneral ,_ffdf ,_febb *int64 ){if _begf !=nil &&_begf .Spacing !=nil {if _begf .Spacing .BeforeAttr !=nil {*_ffdf =int64 (*_begf .Spacing .BeforeAttr .ST_UnsignedDecimalNumber );
};if _begf .Spacing .AfterAttr !=nil {*_febb =int64 (*_begf .Spacing .AfterAttr .ST_UnsignedDecimalNumber );};};};func (_bdbce *convertContext )assignPropsToRelativeParagraph (_eddd *_bfc .CT_PPr ,_cfac *_da .StyledParagraph )(float64 ,float64 ){_eddd =_cfgfd (_eddd ,_bdbce ._aefeg ,_bdbce ._ggbe );
_fgcd :=1.1;if _eddd ==nil {_cfac .SetLineHeight (_fgcd );return 0,0;};var _eddc _da .TextAlignment ;if _eddd .Jc !=nil {switch _eddd .Jc .ValAttr {case _bfc .ST_JcRight :_eddc =_da .TextAlignmentRight ;case _bfc .ST_JcCenter :_eddc =_da .TextAlignmentCenter ;
case _bfc .ST_JcBoth :_eddc =_da .TextAlignmentJustify ;case _bfc .ST_JcEnd :_eddc =_da .TextAlignmentRight ;default:_eddc =_da .TextAlignmentLeft ;};_cfac .SetTextAlignment (_eddc );};var _bdefa ,_cddc ,_dabe ,_ecce float64 ;if _cfbe :=_eddd .Spacing ;
_cfbe !=nil {if _bagf :=_cfbe .BeforeAttr ;_bagf !=nil {if _bagf .ST_UnsignedDecimalNumber !=nil {_bdefa =_ef .PointsFromTwips (int64 (*_bagf .ST_UnsignedDecimalNumber ));};};if _acce :=_cfbe .AfterAttr ;_acce !=nil {if _acce .ST_UnsignedDecimalNumber !=nil {_cddc =_ef .PointsFromTwips (int64 (*_acce .ST_UnsignedDecimalNumber ));
};};if _fdefd :=_cfbe .LineAttr ;_fdefd !=nil {if _fdefd .Int64 !=nil {_fgcd =float64 (*_fdefd .Int64 )/240.0;};};};if _eddd .ContextualSpacing !=nil &&_dgba (_eddd .ContextualSpacing ){_bdefa =0;_cddc =0;};if _bgee :=_eddd .TextAlignment ;_bgee !=nil {switch _bgee .ValAttr {case _bfc .ST_TextAlignmentTop :_cfac .SetTextVerticalAlignment (_da .TextVerticalAlignmentTop );
case _bfc .ST_TextAlignmentBottom :_cfac .SetTextVerticalAlignment (_da .TextVerticalAlignmentBottom );case _bfc .ST_TextAlignmentBaseline :_cfac .SetTextVerticalAlignment (_da .TextVerticalAlignmentBaseline );case _bfc .ST_TextAlignmentCenter :_cfac .SetTextVerticalAlignment (_da .TextVerticalAlignmentCenter );
};};if _eddc ==_da .TextAlignmentRight &&_ecce <=0{_ecce +=5;};if _bdefa > 0{_bdefa =_bdefa -_fgcd /2;};if _cddc > 0{_cddc =_cddc -_fgcd /2;};_cfac .SetLineHeight (_fgcd );if _caed :=_eddd .Ind ;_caed !=nil {if _fedc :=_caed .LeftAttr ;_fedc !=nil {if _fedc .Int64 !=nil {_dabe =_ef .PointsFromTwips (*_fedc .Int64 );
};};if _egce :=_caed .RightAttr ;_egce !=nil {if _egce .Int64 !=nil {_ecce =_ef .PointsFromTwips (*_egce .Int64 );};};};_cfac .SetMargins (_dabe ,_ecce ,_bdefa ,_cddc );return _bdefa ,_dabe ;};func (_gded *convertContext )processRtlLine (_aga *line ){_bddg :=_aga ._fbg ;
for _ ,_ebcb :=range _aga ._dgbf {for _ ,_aaef :=range _ebcb ._egf {_aaef ._gcd =_bddg -_aaef ._afg ;_bbbd :=_aaef ._afg ;for _ ,_ffag :=range _aaef ._edf {_ffag ._cd =_bbbd -_ffag ._df ;_bbbd -=_ffag ._df ;};_bddg =_aaef ._gcd ;};};};func (_gbb *convertContext )newSpan (){_gafa :=&span {_adg :_gbb ._gfcba ._ffg ,_dca :_gbb ._gfcba ._fbg };
_gbb ._dcged =_gafa ;_gbb ._gfcba ._dgbf =append (_gbb ._gfcba ._dgbf ,_gafa );};func (_gccc *convertContext )renderTableRow (_dcedd *_bfc .CT_Row ,_cdd *_bfc .CT_Row ,_ddef float64 ,_bbdg map[int ][]tableCellProperties ,_ebbd int ,_cdcb *_bfc .CT_TblPr ,_dbbd int ,_cec int ,_dbfc int ,_ddg []*_bfc .CT_TblStylePr ,_efcb *_bfc .CT_PPrGeneral ,_adbf *_bfc .CT_RPr ,_baae *_da .Table ,_ebdf []float64 ,_gcdg int ,_cace float64 ,_dggda bool ,_daba []float64 ,_fbdf *_bfc .CT_Tbl ,_bffc float64 ,_bgbd int ,_ffage float64 )(float64 ,int ,*_da .Table ,int ,float64 ,int ,*_bfc .CT_Row ,bool ){_adfa :=_dcedd .TblPrEx ;
_geg :=false ;var _dded float64 ;_fabc :=_bfc .ST_HeightRuleAuto ;if _caec :=_dcedd .TrPr ;_caec !=nil {if len (_caec .TrPrBaseChoice )!=0{for _ ,_ccbb :=range _caec .TrPrBaseChoice {if _ccbb .TblHeader !=nil &&_cdd ==nil {_cdd =_dcedd ;};if _ccbb .TrHeight !=nil {_efd :=_ccbb .TrHeight ;
_fabc =_efd .HRuleAttr ;if _dfb :=_efd .ValAttr ;_dfb !=nil {if _dfb .ST_UnsignedDecimalNumber !=nil {_dded =_ef .PointsFromTwips (int64 (*_dfb .ST_UnsignedDecimalNumber ));_geg =true ;};};};};};};if _ddef ==0||_ddef > _gccc ._fadc ._ebc .Right -_gccc ._fadc ._ebc .Left {_ddef =_gccc ._fadc ._ebc .Right -_gccc ._fadc ._ebc .Left ;
};_fdbb :=map[int ]int {};_gcecg :=0;_efca :=-1;for {_babgbg :=false ;for _deaf ,_bgef :=range _dcedd .EG_ContentCellContent {if _dcb :=_bgef .ContentCellContentChoice .Tc ;len (_dcb )> 0{if _cefg :=_dcb [0];_cefg !=nil {_eebaa :=-1;if _faaff ,_dbag :=_fdbb [_deaf ];
_dbag {_eebaa =_faaff ;};_dgbd :=_deaf !=len (_dcedd .EG_ContentCellContent )-1;_dbbcge :=_gcecg > 0&&_eebaa ==-1&&_deaf < _efca ;_bbdg [_ebbd ]=append (_bbdg [_ebbd ],tableCellProperties {_cefg ,_cdcb ,_adfa ,_dbbd ,_deaf ,_cec ,_dbfc ,_ddg ,_efcb ,_adbf ,_dgbd ,_eebaa ,_dbbcge ,_geg ,_dded });
if _dbbcge {_gccc .addEmptyCellToTable (_baae ,_cefg ,_cdcb ,_adfa ,_dbbd ,_deaf ,_cec ,_dbfc ,_ddg ,_adbf );}else {_fcbd :=_gccc .addCellToTable (_baae ,_cefg ,_cdcb ,_adfa ,_dbbd ,_deaf ,_cec ,_dbfc ,_ddg ,_efcb ,_adbf ,_dgbd ,_eebaa ,_ebdf );if !_babgbg &&_fcbd > -1{_babgbg =true ;
_efca =_deaf ;};_fdbb [_deaf ]=_fcbd ;};};};};if !_babgbg {break ;};_gcecg ++;_gcdg =_ebbd ;_ebbd ++;};if _geg {if _fabc ==_bfc .ST_HeightRuleAtLeast {_edadd ,_caeg :=_baae .GetRowHeight (_baae .CurRow ());if _caeg !=nil {_eg .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u0055\u006e\u0061\u0062\u006c\u0065\u0020\u0070a\u0072\u0073\u0065\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u006f\u0066\u0020v\u003a\u006c\u0069\u006e\u0065\u0020\u0073\u0074\u0072\u006f\u006b\u0065 w\u0065\u0069\u0067\u0068\u0074\u0020\u0028\u0025\u0073\u0029",_caeg .Error ());
};if _edadd < _dded {_caeg =_baae .SetRowHeight (_baae .CurRow (),_dded );if _caeg !=nil {_eg .Log .Debug ("E\u0052\u0052\u004f\u0052\u003a\u0020\u0055\u006e\u0061b\u006c\u0065\u0020\u0074\u006f\u0020\u0073et\u0020\u0072\u006f\u0077 \u0068\u0065\u0069\u0067\u0068\u0074\u0073\u0020\u0066or\u0020\u0074a\u0062\u006c\u0065\u0020\u0028\u0025\u0073\u0029",_caeg .Error ());
};};}else {_adbc :=_baae .SetRowHeight (_baae .CurRow (),_dded );if _adbc !=nil {_eg .Log .Debug ("E\u0052\u0052\u004f\u0052\u003a\u0020\u0055\u006e\u0061b\u006c\u0065\u0020\u0074\u006f\u0020\u0073et\u0020\u0072\u006f\u0077 \u0068\u0065\u0069\u0067\u0068\u0074\u0073\u0020\u0066or\u0020\u0074a\u0062\u006c\u0065\u0020\u0028\u0025\u0073\u0029",_adbc .Error ());
};};};_cacf :=_ef .MakeTempCreatorMaxHeight (_ddef );_bafed :=_cacf .Draw (_baae );if _bafed !=nil {_eg .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u0055\u006e\u0061\u0062\u006c\u0065\u0020\u0074o\u0020d\u0072\u0061\u0077\u0020\u0074\u0061\u0062\u006c\u0065\u0020\u0028\u0025\u0073\u0029",_bafed .Error ());
};if _baae .Height ()> _cace {if _dggda {_gccc .autofitColumns (_baae ,_ddef ,_daba ,_ebdf );_gccc .renderTableRows (_fbdf ,_dbfc ,false ,_daba ,_cdcb ,_ddg ,_efcb ,_adbf ,_bffc ,_cdd );return _ddef ,_ebbd ,_baae ,_bgbd ,_cace ,_gcdg ,_cdd ,true ;};if _gcdg !=-1{_gccc .addTableWithDataRange (_bbdg ,_bgbd +1,_gcdg ,_dbfc ,_ddef ,_ffage ,_daba ,_ebdf ,false );
_bgbd =_gcdg ;}else {_gccc .addTableWithDataRange (_bbdg ,_bgbd +1,_ebbd -1,_dbfc ,_ddef ,_ffage ,_daba ,_ebdf ,false );_bgbd =_ebbd -1;};_gcdg =-1;_gccc .newPage ();_cace =_gccc .getPageAccessiblePart ();_baae =_gccc ._affc .NewTable (_dbfc );_bafed =_baae .SetColumnWidths (_daba ...);
if _bafed !=nil {_eg .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a \u0055\u006e\u0061\u0062\u006c\u0065\u0020\u0074\u006f\u0020\u0073\u0065\u0074\u0020\u0063\u006f\u006c\u0075\u006d\u006e \u0077\u0069\u0064\u0074\u0068\u0073\u0020\u0066\u006f\u0072\u0020\u0074\u0061\u0062l\u0065 \u0028\u0025\u0073\u0029",_bafed .Error ());
};if _cdd !=nil {_ddef ,_ ,_baae ,_ ,_cace ,_gcdg ,_cdd ,_ =_gccc .renderTableRow (_cdd ,_cdd ,_ddef ,_bbdg ,_ebbd -1,_cdcb ,0,_cec ,_dbfc ,_ddg ,_efcb ,_adbf ,_baae ,_ebdf ,_gcdg ,_cace ,_dggda ,_daba ,_fbdf ,_bffc ,_bgbd ,_ffage );};for _acf :=_bgbd +1;
_acf <=_ebbd ;_acf ++{_gacc :=_bbdg [_acf ];_cbfd :=_gacc [0]._fda ;_bgag :=_gacc [0]._ceg ;for _ ,_ffged :=range _gacc {if _ffged ._edb {_gccc .addEmptyCellToTable (_baae ,_ffged ._dgb ,_ffged ._cgd ,_ffged ._fde ,_ffged ._cbc ,_ffged ._eda ,_ffged ._fgg ,_ffged ._gda ,_ffged ._aab ,_ffged ._fgf );
}else {_gccc .addCellToTable (_baae ,_ffged ._dgb ,_ffged ._cgd ,_ffged ._fde ,_ffged ._cbc ,_ffged ._eda ,_ffged ._fgg ,_ffged ._gda ,_ffged ._aab ,_ffged ._eec ,_ffged ._fgf ,_ffged ._fad ,_ffged ._dacg ,_ebdf );};};if _cbfd {_bafed =_baae .SetRowHeight (_baae .CurRow (),_bgag );
if _bafed !=nil {_eg .Log .Debug ("E\u0052\u0052\u004f\u0052\u003a\u0020\u0055\u006e\u0061b\u006c\u0065\u0020\u0074\u006f\u0020\u0073et\u0020\u0072\u006f\u0077 \u0068\u0065\u0069\u0067\u0068\u0074\u0073\u0020\u0066or\u0020\u0074a\u0062\u006c\u0065\u0020\u0028\u0025\u0073\u0029",_bafed .Error ());
};};};};_ebbd ++;return _ddef ,_ebbd ,_baae ,_bgbd ,_cace ,_gcdg ,_cdd ,false ;};func (_gagb *convertContext )addAbsoluteHeaderFooterTable (_egdc *_bfc .CT_Tbl ){_daae :=_egdc .TblGrid ;if _daae ==nil {return ;};_dbgg :=len (_daae .GridCol );if _dbgg ==0{return ;
};_aceg :=[]float64 {};_dbbe :=[]float64 {};_adgcf :=0.0;for _ ,_degfc :=range _daae .GridCol {_bgda :=0.0;if _degfc .WAttr .ST_UnsignedDecimalNumber !=nil {_bgda =_ef .PointsFromTwips (int64 (*_degfc .WAttr .ST_UnsignedDecimalNumber ));};_aceg =append (_aceg ,_bgda );
_adgcf +=_bgda ;};for _ffaed :=0;_ffaed < _dbgg ;_ffaed ++{_dbbe =append (_dbbe ,_aceg [_ffaed ]/_adgcf );};_acbfd :=_gagb ._affc .NewTable (_dbgg );_acbfd .SetColumnWidths (_dbbe ...);_cfage :=_gagb ._affc .NewTable (_dbgg );_cfage .SetColumnWidths (_dbbe ...);
_dfad ,_gdedf ,_dgea :=_ddga (_gagb ._edega ,_egdc .TblPr );var _bggc []*_bfc .CT_TblStylePr ;if _dfad .TblStyle !=nil {_bggc =_cdfbg (_gagb ._edega ,_dfad .TblStyle .ValAttr );};_gdefa :=_fdfa (_dfad .TblW ,_gagb ._fadc ._ebc .Right -_gagb ._fadc ._ebc .Left ,0);
_becde :=_fdfa (_dfad .TblInd ,_gagb ._fadc ._ebc .Right -_gagb ._fadc ._ebc .Left ,0);_cbcc :=_gagb ._fadc ._ebc .Bottom -_gagb ._fadc ._ebc .Top ;_cbde :=len (_egdc .EG_ContentRowContent );_efadf :=make ([]float64 ,_dbgg );for _dacae ,_bfbce :=range _egdc .EG_ContentRowContent {if _bfbce ==nil {continue ;
};_eeab :=_gagb ._affc .NewTable (_dbgg );_eeab .SetColumnWidths (_dbbe ...);if _decb :=_bfbce .ContentRowContentChoice .Tr ;len (_decb )> 0{_fccb :=_decb [0];_bccg :=_fccb .TblPrEx ;for _geb ,_fbbb :=range _fccb .EG_ContentCellContent {if _ddcf :=_fbbb .ContentCellContentChoice .Tc ;
len (_ddcf )> 0{if _cefa :=_ddcf [0];_cefa !=nil {_gagb .addCellToTable (_cfage ,_cefa ,_dfad ,_bccg ,_dacae ,_geb ,_cbde ,_dbgg ,_bggc ,_gdedf ,_dgea ,false ,-1,_efadf );_gagb .addCellToTable (_eeab ,_cefa ,_dfad ,_bccg ,_dacae ,_geb ,_cbde ,_dbgg ,_bggc ,_gdedf ,_dgea ,false ,-1,_efadf );
};};};var _fabde float64 ;if _cccd :=_fccb .TrPr ;_cccd !=nil {if len (_cccd .TrPrBaseChoice )!=0{_gceb :=_cccd .TrPrBaseChoice [0];if _gceb .TrHeight ==nil {continue ;};_cegd :=_gceb .TrHeight ;if _gabd :=_cegd .ValAttr ;_gabd !=nil {if _gabd .ST_UnsignedDecimalNumber !=nil {_fabde =_ef .PointsFromTwips (int64 (*_gabd .ST_UnsignedDecimalNumber ));
};};};};if _fabde < _eeab .Height (){_fabde =_eeab .Height ();};if _fabde < _ggef (4){_fabde =_ggef (4);};_cfage .SetRowHeight (_cfage .CurRow (),_fabde );_eeab .SetRowHeight (_eeab .CurRow (),_fabde );if _gdefa ==0||_gdefa > _gagb ._fadc ._ebc .Right -_gagb ._fadc ._ebc .Left {_gdefa =_gagb ._fadc ._ebc .Right -_gagb ._fadc ._ebc .Left ;
};_abfdg :=_ef .MakeTempCreatorMaxHeight (_gdefa );_abfdg .Draw (_cfage );if _cfage .Height ()>=_cbcc {_gagb .addParagraphWithTable (*_acbfd ,_gdefa ,_becde );_gagb .newPage ();*_cfage =*_eeab ;_cfage .SetRowHeight (_cfage .CurRow (),_fabde );_cbcc =_gagb ._fadc ._ebc .Bottom -_gagb ._fadc ._ebc .Top ;
_acbfd =nil ;}else {if _acbfd ==nil {_acbfd =_gagb ._affc .NewTable (_dbgg );_acbfd .SetColumnWidths (_dbbe ...);};*_acbfd =*_eeab ;};};};if _acbfd !=nil {_gagb .addParagraphWithTableToHeaderFooter (*_acbfd ,_gdefa ,_becde );};};func (_ceabd *convertContext )addBorderLine (_acge *_bfc .CT_Border ,_cgde *paragraph ,_efced _ef .BorderPosition ){_gcge :=0.0;
if _bdga :=_acge .SzAttr ;_bdga !=nil {_gcge =float64 (*_bdga )/8;};_ggeg :=0.0;if _cfdb :=_acge .SpaceAttr ;_cfdb !=nil {_ggeg =float64 (*_cfdb )*_ag .Pixel72 ;};var _cdee _da .Color ;if _dgff :=_acge .ColorAttr ;_dgff !=nil {if _fcdf :=_dgff .ST_HexColorAuto ;
_fcdf ==_bfc .ST_HexColorAutoAuto {_cdee =_da .ColorBlack ;};if _cbda :=_dgff .ST_HexColorRGB ;_cbda !=nil {_cdee =_da .ColorRGBFromHex ("\u0023"+*_cbda );};};if _efced ==_ef .BorderPositionBottom ||_efced ==_ef .BorderPositionTop {_fada :=&borderLine {_gcdd :_efced ,_daa :_ceabd ._fadc ._ebc .Right -_ceabd ._fadc ._ebc .Left ,_gbg :_gcge ,_aef :_cdee ,_ea :_ggeg };
_cgde ._fd =append (_cgde ._fd ,_fada );}else {_egcf :=&borderLine {_gcdd :_efced ,_daa :_gcge ,_gbg :_ceabd ._fadc ._ebc .Top -_ceabd ._fadc ._ebc .Bottom ,_aef :_cdee ,_ea :_ggeg };_cgde ._fd =append (_cgde ._fd ,_egcf );};};func (_bdb *convertContext )alignParagraph (){_dff :=_bdb ._cfeb ;
if _dff ._gfc ==_da .TextAlignmentLeft {return ;};_aabd :=len (_dff ._ed )-1;for _ace ,_bfef :=range _dff ._ed {_cgc :=len (_bfef ._dgbf )-1;for _afe ,_adbe :=range _bfef ._dgbf {_dga :=true ;_bed :=len (_adbe ._egf );_agf :=0.0;for _ead :=len (_adbe ._egf )-1;
_ead >=0;_ead --{_dacdf :=_adbe ._egf [_ead ];if _dga &&_dacdf ._ffb {_bed =_ead ;}else {_dga =false ;for _ ,_gcde :=range _dacdf ._edf {_agf +=_gcde ._df ;};};};_adbe ._egf =_adbe ._egf [:_bed ];_dce :=_adbe ._dca -_adbe ._adg -_agf ;switch _dff ._gfc {case _da .TextAlignmentRight :_adbe .moveRight (_dce );
case _da .TextAlignmentCenter :_adbe .moveRight (_dce /2);case _da .TextAlignmentJustify :if _ace !=_aabd ||_afe !=_cgc {_ffba :=[]*word {};for _ ,_dfe :=range _adbe ._egf {if _dfe ._ffb {_ffba =append (_ffba ,_dfe );};};_aaf :=_dce /float64 (len (_ffba ));
for _ ,_efc :=range _ffba {_efc ._afg +=_aaf ;};var _cgdc *word ;for _ ,_gga :=range _adbe ._egf {if _cgdc !=nil {_gga ._gcd =_cgdc ._gcd +_cgdc ._afg ;};_cgdc =_gga ;};};};};};};func _cfgfd (_acead *_bfc .CT_PPr ,_ddca *_bfc .CT_PPrGeneral ,_bgbdd *_bfc .CT_RPr )*_bfc .CT_PPr {if _acead ==nil {_acead =_bfc .NewCT_PPr ();
};if _ddca !=nil {if _acead .Jc ==nil &&_ddca .Jc !=nil {_acead .Jc =_ddca .Jc ;};if _acead .Spacing ==nil {_acead .Spacing =_ddca .Spacing ;}else if _dggc :=_ddca .Spacing ;_dggc !=nil {if _acead .Spacing .BeforeAttr ==nil {_acead .Spacing .BeforeAttr =_dggc .BeforeAttr ;
};if _acead .Spacing .AfterAttr ==nil {_acead .Spacing .AfterAttr =_dggc .AfterAttr ;};if _acead .Spacing .LineAttr ==nil {_acead .Spacing .LineAttr =_dggc .LineAttr ;};};if _ddca .ContextualSpacing !=nil {_acead .ContextualSpacing =_ddca .ContextualSpacing ;
};if _ddca .Ind !=nil {if _acead .Ind ==nil {_acead .Ind =_ddca .Ind ;}else {_afaa ,_aaec :=_acead .Ind .FirstLineAttr ==nil ,_acead .Ind .HangingAttr ==nil ;if _afaa &&_aaec &&_ddca .Ind .FirstLineAttr !=nil {_acead .Ind .FirstLineAttr =_ddca .Ind .FirstLineAttr ;
_afaa =false ;};if _afaa &&_aaec &&_ddca .Ind .HangingAttr !=nil {_acead .Ind .HangingAttr =_ddca .Ind .HangingAttr ;};if _acead .Ind .LeftAttr ==nil {_acead .Ind .LeftAttr =_ddca .Ind .LeftAttr ;};if _acead .Ind .RightAttr ==nil {_acead .Ind .RightAttr =_ddca .Ind .RightAttr ;
};};};if _acead .Tabs ==nil &&_ddca .Tabs !=nil {_acead .Tabs =_ddca .Tabs ;};if _ddca .PBdr !=nil {_acead .PBdr =_ddca .PBdr ;};if _acead .NumPr ==nil &&_ddca .NumPr !=nil {_acead .NumPr =_ddca .NumPr ;};};if _bgbdd !=nil {var _bdgdg _bfc .CT_ParaRPr ;
if _acead .RPr ==nil {_bdgdg =*_bfc .NewCT_ParaRPr ();}else {_bdgdg =*_acead .RPr ;};if _bdgdg .Color ==nil &&_bgbdd .Color !=nil {_bdgdg .Color =_bgbdd .Color ;};if _bdgdg .Spacing ==nil &&_bgbdd .Spacing !=nil {_bdgdg .Spacing =_bgbdd .Spacing ;};if _bdgdg .Sz ==nil &&_bgbdd .Sz !=nil {_bdgdg .Sz =_bgbdd .Sz ;
};if _bdgdg .SzCs ==nil &&_bgbdd .SzCs !=nil {_bdgdg .SzCs =_bgbdd .SzCs ;};if _bdgdg .B ==nil &&_bgbdd .B !=nil {_bdgdg .B =_bgbdd .B ;};if _bdgdg .I ==nil &&_bgbdd .I !=nil {_bdgdg .I =_bgbdd .I ;};if _bdgdg .RFonts ==nil &&_bgbdd .RFonts !=nil {_bdgdg .RFonts =_bgbdd .RFonts ;
};if _bdgdg .VertAlign ==nil &&_bgbdd .VertAlign !=nil {_bdgdg .VertAlign =_bgbdd .VertAlign ;};if _bdgdg .Bdr ==nil &&_bgbdd .Bdr !=nil {_bdgdg .Bdr =_bgbdd .Bdr ;};if _bdgdg .Caps ==nil &&_bgbdd .Caps !=nil {_bdgdg .Caps =_bgbdd .Caps ;};if _bdgdg .SmallCaps ==nil &&_bgbdd .SmallCaps !=nil {_bdgdg .SmallCaps =_bgbdd .SmallCaps ;
};_acead .RPr =&_bdgdg ;};return _acead ;};const (FontStyle_Regular FontStyle =0;FontStyle_Bold FontStyle =1;FontStyle_Italic FontStyle =2;FontStyle_BoldItalic FontStyle =3;);func (_aggde *convertContext )autofitColumns (_gadg *_da .Table ,_dccf float64 ,_fgga []float64 ,_dbffg []float64 ){_egd :=0.0;
for _ ,_acac :=range _fgga {_egd +=_acac ;};if _egd <=0||_dccf <=0||len (_fgga )!=len (_dbffg ){return ;};_cdgb :=map[int ]float64 {};_fdcc :=map[int ]float64 {};for _cgbad ,_edgg :=range _dbffg {if _edgg *_egd /_dccf > _fgga [_cgbad ]{_cdgb [_cgbad ]=_edgg *_egd /_dccf -_fgga [_cgbad ];
}else {_aeaf :=1.5;if _fgga [_cgbad ]-_edgg *_aeaf *_egd /_dccf > 0{_fdcc [_cgbad ]=_fgga [_cgbad ]-_edgg *_aeaf *_egd /_dccf ;};};};if len (_cdgb )==0||len (_fdcc )==0{return ;};for _fgad ,_feed :=range _cdgb {for _dddb ,_dbea :=range _fdcc {if _feed < _dbea {_cdgb [_fgad ]=0;
_fdcc [_dddb ]-=_feed ;_fgga [_fgad ]+=_dbea -_feed ;_fgga [_dddb ]-=_dbea -_feed ;break ;}else {_fdcc [_dddb ]=0;_cdgb [_fgad ]-=_dbea ;_fgga [_fgad ]+=_dbea ;_fgga [_dddb ]-=_dbea ;};};};_gdde :=_gadg .SetColumnWidths (_fgga ...);if _gdde !=nil {_eg .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a \u0055\u006e\u0061\u0062\u006c\u0065\u0020\u0074\u006f\u0020\u0073\u0065\u0074\u0020\u0063\u006f\u006c\u0075\u006d\u006e \u0077\u0069\u0064\u0074\u0068\u0073\u0020\u0066\u006f\u0072\u0020\u0074\u0061\u0062l\u0065 \u0028\u0025\u0073\u0029",_gdde .Error ());
};};type block struct{_de *_da .Block ;_bfd float64 ;_ccd float64 ;_aae bool ;_fgcf float64 ;_abf _da .Color ;};func (_fbf *convertContext )addAnchorBlocks (_dggd []*_bfc .EG_PContent ){for _ ,_dbbc :=range _dggd {for _ ,_egbe :=range _dbbc .PContentChoice .EG_ContentRunContent {if _ged :=_egbe .ContentRunContentChoice .R ;
_ged !=nil {for _ ,_cafg :=range _ged .EG_RunInnerContent {if _cfg :=_cafg .RunInnerContentChoice .Drawing ;_cfg !=nil {for _ ,_faf :=range _cfg .DrawingChoice {if _faf .Anchor ==nil {continue ;};_fbba :=_faf .Anchor ;var _gafc ,_gff ,_eeba ,_cfb float64 ;
if _fbba .DistLAttr !=nil {_gafc =_ag .FromEMU (int64 (*_fbba .DistLAttr ));};if _fbba .DistRAttr !=nil {_gff =_ag .FromEMU (int64 (*_fbba .DistRAttr ));};if _fbba .DistTAttr !=nil {_eeba =_ag .FromEMU (int64 (*_fbba .DistTAttr ));};if _fbba .DistBAttr !=nil {_cfb =_ag .FromEMU (int64 (*_fbba .DistBAttr ));
};var _fga ,_gbc ,_ebf ,_babg float64 ;if _dgee :=_fbba .Extent ;_dgee !=nil {_babg =_ag .FromEMU (_dgee .CxAttr );_ebf =_ag .FromEMU (_dgee .CyAttr );};_gag :=0.0;_cegb :=0.0;_ebge ,_bceg :=_fbba .PositionH ,_fbba .PositionV ;switch _ebge .RelativeFromAttr {case _bfc .WdST_RelFromHPage :_cegb =0;
default:_cegb =_fbf ._fadc ._ebc .Left ;};if _gab :=_ebge .PosHChoice ;_gab !=nil {if _gab .PosOffset !=nil {_fga =_ag .FromEMU (int64 (*_gab .PosOffset ));};switch _gab .Align {case _bfc .WdST_AlignHRight :_fga +=_fbf ._cfeb ._dcg -_fbf ._cfeb ._ddd -_babg ;
case _bfc .WdST_AlignHCenter :_fga +=(_fbf ._cfeb ._dcg -_fbf ._cfeb ._ddd -_babg )/2;};};switch _bceg .RelativeFromAttr {case _bfc .WdST_RelFromVPage :_gag =0;case _bfc .WdST_RelFromVParagraph :_gag =_fbf ._cfeb ._bdd ;case _bfc .WdST_RelFromVTopMargin :_gag =0;
default:_gag =_fbf ._fadc ._ebc .Top ;};if _bdf :=_bceg .PosVChoice ;_bdf !=nil {if _bdf .PosOffset !=nil {_gbc =_ag .FromEMU (int64 (*_bdf .PosOffset ));};};_gag +=_gbc ;_cegb +=_fga ;_aeca :=_gag +_ebf ;_adac :=_cegb +_babg ;if _gag < _fbf ._fadc ._cfe {_fbf .moveCurrentParagraphToNewPage ();
};_cbaf :=_gbc +_ebf ;if _cbaf > _fbf ._cfeb ._ce {_fbf ._cfeb ._ce =_cbaf ;};if !_fbba .AllowOverlapAttr {_fbf ._cfeb ._aee =_ebf ;};if _fbba .WrapTypeChoice !=nil &&_fbba .WrapTypeChoice .WrapNone ==nil {_fbf ._cfeb ._aeb =append (_fbf ._cfeb ._aeb ,&zoneToSkip {_dc :&_ef .Rectangle {Top :_gag -_eeba ,Bottom :_aeca +_cfb ,Left :_cegb -_gafc ,Right :_adac +_gff },_dda :_fbba .WrapTypeChoice ,_ba :_fbba .RelativeHeightAttr });
};if _gccd :=_fbba .Graphic ;_gccd !=nil {if _ffgd :=_gccd .GraphicData ;_ffgd !=nil {for _ ,_fdb :=range _ffgd .Any {if _gfa ,_acg :=_fdb .(*_ffd .Pic );_acg {_gbeg ,_aafa :=_fbf .makePdfImageFromGraphics (_gfa );if _aafa !=nil {_eg .Log .Debug ("C\u0061\u006e\u006e\u006ft \u0072e\u0061\u0064\u0020\u0069\u006da\u0067\u0065\u003a\u0020\u0025\u0073",_aafa );
};_ddde :=false ;_bgb :=0.0;if _gfa .SpPr !=nil &&_gfa .SpPr .Xfrm !=nil {if _gfa .SpPr .Xfrm .RotAttr !=nil {_bgb =_ag .DegreeFromSTAngle (*_gfa .SpPr .Xfrm .RotAttr );};if _gfe :=_gfa .SpPr .Xfrm .Ext ;_gfe !=nil {_ddde =true ;};};if _gbeg !=nil {if !_ddde {_gbeg .Scale (_babg /_gbeg .Width (),_ebf /_gbeg .Height ());
}else {_gbeg .ScaleToWidth (_babg );};_gbeg .SetAngle (_bgb );_babgg :=&image {_dag :_gbeg ,_bac :_cegb ,_eca :_gag };if _fbba .BehindDocAttr {_fbf ._cfeb ._egb =append (_fbf ._cfeb ._egb ,_babgg );}else {_fbf ._cfeb ._bdda =append (_fbf ._cfeb ._bdda ,_babgg );
};};}else if _fcab ,_gafb :=_fdb .(*_db .Chart );_gafb {_egff ,_ebd :=_fbf .makePdfBlockFromChart (_fcab ,_babg ,_ebf );if _ebd !=nil {_eg .Log .Debug ("C\u0061\u006e\u006e\u006ft \u0072e\u0061\u0064\u0020\u0062\u006co\u0063\u006b\u003a\u0020\u0025\u0073",_ebd );
};if _egff !=nil {_cee :=&block {_de :_egff ,_bfd :_cegb ,_ccd :_gag };if _fbba .BehindDocAttr {_fbf ._cfeb ._gd =append (_fbf ._cfeb ._gd ,_cee );}else {_fbf ._cfeb ._ad =append (_fbf ._cfeb ._ad ,_cee );};};};};};};};};};};};};};func (_ebcbe *convertContext )assignPropsToAbsoluteParagraph (_ffea *_bfc .CT_PPr ,_feaeb *paragraph )(float64 ,float64 ){_ebcbe ._bfeaf =_ffea ;
_ffea =_cfgfd (_ffea ,_ebcbe ._aefeg ,_ebcbe ._ggbe );_eecd :=12.4;if _ffea ==nil {return 0,0;};_feaeb ._ab =0.0;if _degf :=_ffea .RPr ;_degf !=nil {_agga :=_bfcd (_degf .Sz ,_degf .SzCs );if _agga > _eecd {_eecd =_agga ;}else {_eecd =_agga *_dba ;};_feaeb ._cfed =_eecd ;
};if _ffea .Jc !=nil {switch _ffea .Jc .ValAttr {case _bfc .ST_JcRight :_feaeb ._gfc =_da .TextAlignmentRight ;case _bfc .ST_JcCenter :_feaeb ._gfc =_da .TextAlignmentCenter ;case _bfc .ST_JcBoth :_feaeb ._gfc =_da .TextAlignmentJustify ;case _bfc .ST_JcEnd :_feaeb ._gfc =_da .TextAlignmentRight ;
default:_feaeb ._gfc =_da .TextAlignmentLeft ;};};var _ecec ,_cgg ,_afff ,_fgdga ,_dagac float64 ;if _bgca :=_ffea .Spacing ;_bgca !=nil {if _egbb :=_bgca .BeforeAttr ;_egbb !=nil {if _egbb .ST_UnsignedDecimalNumber !=nil {_ecec =_ef .PointsFromTwips (int64 (*_egbb .ST_UnsignedDecimalNumber ));
};};if _fegf :=_bgca .AfterAttr ;_fegf !=nil {if _fegf .ST_UnsignedDecimalNumber !=nil {_cgg =_ef .PointsFromTwips (int64 (*_fegf .ST_UnsignedDecimalNumber ));};};if _egbba :=_bgca .LineAttr ;_egbba !=nil {if _egbba .Int64 !=nil &&*_egbba .Int64 !=0{if _bgca .LineRuleAttr ==_bfc .ST_LineSpacingRuleAuto {_eeaf :=float64 (*_egbba .Int64 )/240;
if _eeaf > 1{_eecd =_eecd *_eeaf ;};}else {if _aaac :=float64 (*_egbba .Int64 )/20;_aaac > _eecd {_eecd =_aaac ;};};};};};_bfea :=_ebcbe ._fadc ._gc ;if len (_bfea )> 0{_fcddf :=_bfea [len (_bfea )-1]._agb .Bottom ;if _ebcbe ._aadb !=nil &&_ebcbe ._aadb .Spacing !=nil &&_ffea .Spacing !=nil {if _ebcbe .shouldApplyContextualSpacing (_ffea ){_ecdf :=_ecec -_fcddf ;
if _fcddf > _ecec {_ecec =_ecdf ;}else {_ecec =-_fcddf +_ecdf ;};}else if _fcddf > _ecec {_ecec =0.0;}else {_ecec -=_fcddf ;};};}else {_ecec -=_bd ;};_feaeb ._ab =_eecd -_feaeb ._cfed ;_feaeb ._cfed =_eecd ;if _cbd :=_ffea .Ind ;_cbd !=nil {if _cdec :=_cbd .FirstLineAttr ;
_cdec !=nil {if _cdec .ST_UnsignedDecimalNumber !=nil {_dagac =_ef .PointsFromTwips (int64 (*_cdec .ST_UnsignedDecimalNumber ));};};if _ggfgg :=_cbd .HangingAttr ;_ggfgg !=nil {if _ggfgg .ST_UnsignedDecimalNumber !=nil {_dagac -=_ef .PointsFromTwips (int64 (*_ggfgg .ST_UnsignedDecimalNumber ));
};};if _gabf :=_cbd .LeftAttr ;_gabf !=nil {if _gabf .Int64 !=nil {_afff =_ef .PointsFromTwips (*_gabf .Int64 );};};if _aecg :=_cbd .RightAttr ;_aecg !=nil {if _aecg .Int64 !=nil {_fgdga =_ef .PointsFromTwips (*_aecg .Int64 );};};};if _ffea .PBdr !=nil {if _gdce :=_ffea .PBdr .Top ;
_gdce !=nil {_ebcbe .addBorderLine (_gdce ,_feaeb ,_ef .BorderPositionTop );};if _edbd :=_ffea .PBdr .Left ;_edbd !=nil {_ebcbe .addBorderLine (_edbd ,_feaeb ,_ef .BorderPositionLeft );};if _eaegg :=_ffea .PBdr .Right ;_eaegg !=nil {_ebcbe .addBorderLine (_eaegg ,_feaeb ,_ef .BorderPositionRight );
};if _eafe :=_ffea .PBdr .Bottom ;_eafe !=nil {_ebcbe .addBorderLine (_eafe ,_feaeb ,_ef .BorderPositionBottom );};};_feaeb ._agb =&_ef .Rectangle {Top :_ecec ,Bottom :_cgg ,Left :_afff ,Right :_fgdga };_feaeb ._fbc =_dagac ;_ebcbe ._aadb =_ffea ;return _ecec ,_afff ;
};var _ecfb ,_dgdeb ,_gacba *_be .Regexp ;type image struct{_dag *_da .Image ;_bac float64 ;_eca float64 ;};func _cabb (_cfgd int64 ,_aecdd _bfc .ST_NumberFormat )string {_dfca :=int (_cfgd );switch _aecdd {case _bfc .ST_NumberFormatDecimal :return _b .Itoa (_dfca );
case _bfc .ST_NumberFormatUpperRoman :return _daeg (_dfca ,true );case _bfc .ST_NumberFormatLowerRoman :return _daeg (_dfca ,false );case _bfc .ST_NumberFormatUpperLetter :return _bebe (_dfca ,true );case _bfc .ST_NumberFormatLowerLetter :return _bebe (_dfca ,false );
default:return _b .Itoa (_dfca );};};func (_gaaa *convertContext )addTextSymbol (_gdc *symbol ){_cbafe :=_da .New ();_cacae :=_cbafe .NewStyledParagraph ();_cacae .SetMargins (0,0,0,0);_ccge :=_gdc ._aaa ;if _gdc ._dgda {_ccge ="";};_bead :=_cacae .Append (_ccge );
_agbb :=0.0;if _gdc ._dab !=nil {_bead .Style =*_gdc ._dab ;if _gdc ._dab .CharSpacing !=0{_agbb =_gdc ._dab .CharSpacing ;};};if _gdc ._bfb ==nil &&_gdc ._fggc ==nil {_gdc ._dfc =_cacae .Height ()*_dba ;_gdc ._abg =_cacae .Height ();};if _gdc ._df ==0&&!_gdc ._dgda {_gdc ._df =_cacae .Width ()+_agbb ;
};if _gdc ._dfc < _gaaa ._cfeb ._cfed {_gdc ._dfc =_gaaa ._cfeb ._cfed ;};if len (_gaaa ._efdb ._edf )> 0{_efadg :=_gaaa ._efdb ._edf [len (_gaaa ._efdb ._edf )-1]._aaa ;if _ef .IsNoSpaceLanguage (_efadg )||(_efadg =="\u0020")!=(_gdc ._aaa =="\u0020"){_gaaa .addCurrentWordToParagraph ();
_gaaa .newWord ();};};_gaaa ._efdb ._edf =append (_gaaa ._efdb ._edf ,_gdc );_gdc ._cd =_gaaa ._efdb ._afg ;_gaaa ._efdb ._afg +=_gdc ._df ;if _gdc ._aaa !="\u0020"{_gaaa ._efdb ._ffb =false ;};if _gdc ._aaa =="\u000d"{_gaaa .adjustHeights (_gdc ._dfc *1.13);
_gaaa .adjustHeights (_gdc ._dfc );};};func (_fgdba *convertContext )newPage (){_efec :=&page {};_efec ._ebc =_fgdba ._gefe ;_efec ._fa =_efec ._ebc .Top ;if _fgdba ._cgdcd {_efec ._eea =true ;_efec ._fa +=_dae ;};_fgdba ._cbaaa =append (_fgdba ._cbaaa ,_efec );
_fgdba ._fadc =_efec ;};func (_fae *convertContext )newLine (){if _fae ._cfeb ==nil {_fae .newParagraph ();};_edde :=_fae ._cfeb ._aee +_fae ._cfeb ._agb .Top ;_fbdd :=&line {};if len (_fae ._cfeb ._ed )==0{_fbdd ._cbca =_fae ._cfeb ._cc ;}else {_fbdd ._cbca =_fae ._cfeb ._ddd ;
};_fbdd ._fbg =_fae ._cfeb ._dcg ;_fbdd ._ffg =_fbdd ._cbca ;_fbdd ._bab =_edde ;_fae ._cfeb ._ed =append (_fae ._cfeb ._ed ,_fbdd );_fae ._gfcba =_fbdd ;_fae .newSpan ();};type prefix struct{_bdee string ;_bfgb []float64 ;_fgdbb bool ;_cffd bool ;};func _bfae (_adda *_bfc .EG_RunInnerContent )bool {if _dbee :=_adda .RunInnerContentChoice .Br ;
_dbee !=nil {return _dbee .TypeAttr ==_bfc .ST_BrTypePage ;};return false ;};type paragraph struct{_fbc float64 ;_agb *_ef .Rectangle ;_cc float64 ;_ddd float64 ;_dcg float64 ;_bdd float64 ;_aee float64 ;_gfc _da .TextAlignment ;_cfed float64 ;_ab float64 ;
_ed []*line ;_aa *tableWrapper ;_bdda []*image ;_egb []*image ;_ad []*block ;_gd []*block ;_edg []*note ;_aeg float64 ;_aeb []*zoneToSkip ;_ce float64 ;_fcg bool ;_fd []*borderLine ;_fg bool ;};func (_dbdac *convertContext )moveCurrentParagraphToNewPage (){_dbdac .newPage ();
_aebd :=_dbdac ._cfeb ._bdd -_dbdac ._fadc ._fa ;_dbdac ._cfeb ._bdd -=_aebd ;for _ ,_abgg :=range _dbdac ._cfeb ._aeb {_abgg ._dc .Translate (0,-_aebd );};for _ ,_dfcdf :=range _dbdac ._cfeb ._ad {_dfcdf ._ccd -=_aebd ;};for _ ,_fdbf :=range _dbdac ._cfeb ._gd {_fdbf ._ccd -=_aebd ;
};for _ ,_abbg :=range _dbdac ._cfeb ._bdda {_abbg ._eca -=_aebd ;};for _ ,_eddab :=range _dbdac ._cfeb ._egb {_eddab ._eca -=_aebd ;};};type tableWrapper struct{_cef *_da .Table ;_ec float64 ;_ca _da .HorizontalAlignment ;};func _gccb (_dcad string )(string ,string ){_gdaf :=_dgdeb .FindStringSubmatch (_dcad );
if len (_gdaf )< 3{return "","";};return _gdaf [1],_gdaf [2];};func _bebe (_feee int ,_cgab bool )string {_gabfb :=(_feee -1)/26+1;_cebae :=byte ((_feee -1)%26);if _cgab {_cebae +=byte (65);}else {_cebae +=byte (97);};_beffa :=_a .NewBuffer ([]byte {});
for _fecb :=0;_fecb < _gabfb ;_fecb ++{_beffa .Write ([]byte {_cebae });};return _beffa .String ();};func _bcag (_gffd ,_ddgfd *_bfc .CT_RPr )*_bfc .CT_RPr {if _gffd ==nil {return _ddgfd ;};if _ddgfd ==nil {return _gffd ;};if _gffd .RStyle ==nil {_gffd .RStyle =_ddgfd .RStyle ;
};if _gffd .RFonts ==nil {_gffd .RFonts =_ddgfd .RFonts ;};if _gffd .B ==nil {_gffd .B =_ddgfd .B ;};if _gffd .BCs ==nil {_gffd .BCs =_ddgfd .BCs ;};if _gffd .I ==nil {_gffd .I =_ddgfd .I ;};if _gffd .ICs ==nil {_gffd .ICs =_ddgfd .ICs ;};if _gffd .Caps ==nil {_gffd .Caps =_ddgfd .Caps ;
};if _gffd .SmallCaps ==nil {_gffd .SmallCaps =_ddgfd .SmallCaps ;};if _gffd .Strike ==nil {_gffd .Strike =_ddgfd .Strike ;};if _gffd .Dstrike ==nil {_gffd .Dstrike =_ddgfd .Dstrike ;};if _gffd .Outline ==nil {_gffd .Outline =_ddgfd .Outline ;};if _gffd .Shadow ==nil {_gffd .Shadow =_ddgfd .Shadow ;
};if _gffd .Emboss ==nil {_gffd .Emboss =_ddgfd .Emboss ;};if _gffd .Imprint ==nil {_gffd .Imprint =_ddgfd .Imprint ;};if _gffd .NoProof ==nil {_gffd .NoProof =_ddgfd .NoProof ;};if _gffd .SnapToGrid ==nil {_gffd .SnapToGrid =_ddgfd .SnapToGrid ;};if _gffd .Vanish ==nil {_gffd .Vanish =_ddgfd .Vanish ;
};if _gffd .WebHidden ==nil {_gffd .WebHidden =_ddgfd .WebHidden ;};if _gffd .Color ==nil {_gffd .Color =_ddgfd .Color ;};if _gffd .Spacing ==nil {_gffd .Spacing =_ddgfd .Spacing ;};if _gffd .W ==nil {_gffd .W =_ddgfd .W ;};if _gffd .Kern ==nil {_gffd .Kern =_ddgfd .Kern ;
};if _gffd .Position ==nil {_gffd .Position =_ddgfd .Position ;};if _gffd .Sz ==nil {_gffd .Sz =_ddgfd .Sz ;};if _gffd .SzCs ==nil {_gffd .SzCs =_ddgfd .SzCs ;};if _gffd .Highlight ==nil {_gffd .Highlight =_ddgfd .Highlight ;};if _gffd .U ==nil {_gffd .U =_ddgfd .U ;
};if _gffd .Effect ==nil {_gffd .Effect =_ddgfd .Effect ;};if _gffd .Bdr ==nil {_gffd .Bdr =_ddgfd .Bdr ;};if _gffd .Shd ==nil {_gffd .Shd =_ddgfd .Shd ;};if _gffd .FitText ==nil {_gffd .FitText =_ddgfd .FitText ;};if _gffd .VertAlign ==nil {_gffd .VertAlign =_ddgfd .VertAlign ;
};if _gffd .Rtl ==nil {_gffd .Rtl =_ddgfd .Rtl ;};if _gffd .Cs ==nil {_gffd .Cs =_ddgfd .Cs ;};if _gffd .Em ==nil {_gffd .Em =_ddgfd .Em ;};if _gffd .Lang ==nil {_gffd .Lang =_ddgfd .Lang ;};if _gffd .EastAsianLayout ==nil {_gffd .EastAsianLayout =_ddgfd .EastAsianLayout ;
};if _gffd .SpecVanish ==nil {_gffd .SpecVanish =_ddgfd .SpecVanish ;};if _gffd .OMath ==nil {_gffd .OMath =_ddgfd .OMath ;};if _gffd .RPrChange ==nil {_gffd .RPrChange =_ddgfd .RPrChange ;};return _gffd ;};func (_ecbg *convertContext )adjustRightBoundOfLastSpan (){_gad :=_ecbg ._dcged ._dca ;
_bdef :=_ecbg ._gfcba ._bab +_ecbg ._cfeb ._bdd ;_bdaa :=_bdef +_ecbg ._gfcba ._fgc ;for _ ,_egfa :=range _ecbg ._fadc ._cfc {if ((_bdef > _egfa ._dc .Top &&_bdef < _egfa ._dc .Bottom )||(_bdaa > _egfa ._dc .Top &&_bdef < _egfa ._dc .Bottom ))&&(_gad > _egfa ._dc .Left ){_gad =_egfa ._dc .Left ;
};};_ecbg ._dcged ._dca =_gad ;};func (_dee *convertContext )determineParagraphBounds (){_dee ._cfeb ._ddd =_dee ._fadc ._ebc .Left +_dee ._cfeb ._agb .Left ;_dee ._cfeb ._cc =_dee ._cfeb ._ddd +_dee ._cfeb ._fbc ;_dee ._cfeb ._dcg =_dee ._fadc ._ebc .Right -_dee ._cfeb ._agb .Right ;
};func (_fba *convertContext )processCtr (_efag *_bfc .CT_R ,_fbec *_bfc .CT_PPr ,_dcgf bool ,_bcb *link ,_dfge *_da .StyledParagraph ,_afgf bool ,_babcb int ,_ggda int ,_efae int ,_baab *_da .Division ,_adbd bool )(bool ,int ,bool ,_da .TextStyle ){var _gba _da .TextStyle ;
_dcae :=_ccgd (_fba ._edega ,_efag .RPr ,_fbec );for _ ,_gadd :=range _efag .EG_RunInnerContent {var _dgaa *_da .TextChunk ;if _gadd .RunInnerContentChoice .T !=nil {_beaaa :=_gadd .RunInnerContentChoice .T .Content ;if _dcae !=nil &&_dgba (_dcae .Caps ){_beaaa =_ga .ToUpper (_beaaa );
};if _beaaa ==""{_beaaa ="\u0020";};_dcgf =true ;if _bcb ._fbb !=""{if _bcb ._affd ==_bg .ST_TargetModeExternal {_dgaa =_dfge .AddExternalLink (_beaaa ,_bcb ._fbb );};}else {_dgaa =_dfge .Append (_beaaa );};if _dcae .Highlight !=nil {_dgaa .Highlight (_e .HighlightColorToCreatorColorMap [_dcae .Highlight .ValAttr ],1.0);
};_gba ,_ ,_ ,_ =_fba .makeRunStyle (_dcae ,false ,false ,false ,false ,false );_dgaa .Style =_gba ;}else if _gadd .RunInnerContentChoice .LastRenderedPageBreak !=nil &&!_afgf &&_babcb !=_ggda {_efae =_ggda ;break ;}else if _gadd .RunInnerContentChoice .Br !=nil {_dfge .Append ("\u000a\u0020");
_dcgf =true ;}else if _gadd .RunInnerContentChoice .Drawing !=nil {for _ ,_ccef :=range _gadd .RunInnerContentChoice .Drawing .DrawingChoice {if _ccef .Inline ==nil {continue ;};_fgeg :=_ccef .Inline ;if _bbabb :=_fgeg .Graphic ;_bbabb !=nil {if _gbbf :=_bbabb .GraphicData ;
_gbbf !=nil {_cegf :=_fgeg .Extent ;if _cegf ==nil {continue ;};_fdg :=_ag .FromEMU (_cegf .CxAttr );_cfag :=_ag .FromEMU (_cegf .CyAttr );for _ ,_ceab :=range _gbbf .Any {if _cabf ,_ffed :=_ceab .(*_ffd .Pic );_ffed {if _cabf .BlipFill !=nil {_cfec ,_dcga :=_fba .makePdfImageFromGraphics (_cabf );
if _dcga !=nil {_eg .Log .Debug ("C\u0061\u006e\u006e\u006ft \u006da\u006b\u0065\u0020\u0069\u006da\u0067\u0065\u003a\u0020\u0025\u0073",_dcga );};if _cfec !=nil {_cfec .Scale (_fdg /_cfec .Width (),_cfag /_cfec .Height ());_bggb :=_baab .Add (_cfec );
if _bggb !=nil {_eg .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020U\u006e\u0061\u0062l\u0065\u0020\u0074\u006f \u0061\u0064\u0064\u0020\u0069\u006d\u0061\u0067\u0065\u0020\u0074\u006f\u0020\u0064\u0069\u0076\u0069\u0073\u0069\u006f\u006e\u0020\u0028\u0025\u0073\u0029",_bggb .Error ());
};_dcgf =true ;_adbd =false ;};};};};};};};};};return _dcgf ,_efae ,_adbd ,_gba ;};func (_cbdg *convertContext )makePdfBlockFromChart (_bcfg *_db .Chart ,_gfac ,_deecg float64 )(*_da .Block ,error ){_eedb :=_bcfg .CT_RelId .IdAttr ;_fbbe :=_cbdg ._edega .GetChartSpaceByRelId (_eedb );
if _fbbe ==nil {return nil ,_c .New ("\u004e\u006f\u0020\u0063\u0068\u0061\u0072\u0074\u0073\u0070\u0061\u0063\u0065");};var _gfcf *_bf .Theme ;_bfde :=_cbdg ._edega .Themes ();if len (_bfde )> 0{_gfcf =_bfde [0];};return _ef .MakeBlockFromChartSpace (_fbbe ,_gfac ,_deecg ,_gfcf );
};const (_ge =0.67;_dba =1.15;_bd =2.5;);func (_abad *convertContext )makeBlockFromTextboxContent (_dceg *_bfc .TxbxContent ,_deea ,_deff float64 ,_bgge *_ef .Rectangle )(*block ,error ){if _bgge ==nil {_bgge =&_ef .Rectangle {};};for _ ,_ceaba :=range _dceg .EG_BlockLevelElts {if _facf :=_ceaba .BlockLevelEltsChoice .EG_ContentBlockContent ;
len (_facf )> 0{_abgd ,_gca :=_abad .makePdfBlockFromCBCs ([][]*_bfc .EG_ContentBlockContent {_facf },_deea ,_deff ,_bgge ,false ,nil );if _gca !=nil {return nil ,_gca ;};_gccca :=&block {_de :_abgd ,_aae :false ,_fgcf :0,_abf :_da .ColorBlack };return _gccca ,nil ;
};};return nil ,nil ;};func (_adcg *convertContext )renderTableRows (_fgaag *_bfc .CT_Tbl ,_dbdc int ,_efaa bool ,_fdac []float64 ,_cagd *_bfc .CT_TblPr ,_fge []*_bfc .CT_TblStylePr ,_dege *_bfc .CT_PPrGeneral ,_gaae *_bfc .CT_RPr ,_abeg float64 ,_dgeb *_bfc .CT_Row ){_faeg :=_adcg ._affc .NewTable (_dbdc );
_ddeg :=_faeg .SetColumnWidths (_fdac ...);if _ddeg !=nil {_eg .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a \u0055\u006e\u0061\u0062\u006c\u0065\u0020\u0074\u006f\u0020\u0073\u0065\u0074\u0020\u0063\u006f\u006c\u0075\u006d\u006e \u0077\u0069\u0064\u0074\u0068\u0073\u0020\u0066\u006f\u0072\u0020\u0074\u0061\u0062l\u0065 \u0028\u0025\u0073\u0029",_ddeg .Error ());
};_aaed :=_fdfa (_cagd .TblW ,_adcg ._fadc ._ebc .Right -_adcg ._fadc ._ebc .Left ,_abeg );_cbgf :=_fdfa (_cagd .TblInd ,_adcg ._fadc ._ebc .Right -_adcg ._fadc ._ebc .Left ,0);_fgca :=_adcg .getPageAccessiblePart ();_fcfde :=len (_fgaag .EG_ContentRowContent );
_facc :=map[int ][]tableCellProperties {};_afeb :=0;_cbcb :=-1;_eddg :=-1;_dcc :=make ([]float64 ,_dbdc );if _dgeb !=nil {_aaed ,_afeb ,_faeg ,_eddg ,_fgca ,_cbcb ,_dgeb ,_ =_adcg .renderTableRow (_dgeb ,_dgeb ,_aaed ,_facc ,_afeb ,_cagd ,0,_fcfde ,_dbdc ,_fge ,_dege ,_gaae ,_faeg ,_dcc ,_cbcb ,_fgca ,_efaa ,_fdac ,_fgaag ,_abeg ,_eddg ,_cbgf );
};for _bbef ,_gdb :=range _fgaag .EG_ContentRowContent {if _gdb ==nil {continue ;};if _cgeb :=_gdb .ContentRowContentChoice .Tr ;len (_cgeb )> 0{_fecc :=_cgeb [0];var _faaf bool ;_aaed ,_afeb ,_faeg ,_eddg ,_fgca ,_cbcb ,_dgeb ,_faaf =_adcg .renderTableRow (_fecc ,_dgeb ,_aaed ,_facc ,_afeb ,_cagd ,_bbef ,_fcfde ,_dbdc ,_fge ,_dege ,_gaae ,_faeg ,_dcc ,_cbcb ,_fgca ,_efaa ,_fdac ,_fgaag ,_abeg ,_eddg ,_cbgf );
if _faaf {return ;};};};if _efaa {_adcg .autofitColumns (_faeg ,_aaed ,_fdac ,_dcc );};if _eddg < _afeb -1{_adcg .addTableWithDataRange (_facc ,_eddg +1,_afeb -1,_dbdc ,_aaed ,_cbgf ,_fdac ,_dcc ,_dgeb !=nil &&_eddg !=-1);};if _cagd .Jc !=nil &&_adcg ._cfeb ._aa !=nil {switch _cagd .Jc .ValAttr {case _bfc .ST_JcTableCenter :_adcg ._cfeb ._aa ._ca =_da .HorizontalAlignmentCenter ;
case _bfc .ST_JcTableRight ,_bfc .ST_JcTableEnd :_adcg ._cfeb ._aa ._ca =_da .HorizontalAlignmentRight ;};};};type convertContext struct{_affc *_da .Creator ;_edega *_fca .Document ;_aefeg *_bfc .CT_PPrGeneral ;_ggbe *_bfc .CT_RPr ;_cffe *_da .StyledParagraph ;
_cbaaa []*page ;_fadc *page ;_gefe *_ef .Rectangle ;_cfeb *paragraph ;_gfcba *line ;_dcged *span ;_efdb *word ;_edgdg *_bfc .CT_Hyperlink ;_bfcf *_bfc .CT_ParaRPr ;_bfeaf *_bfc .CT_PPr ;_aegfc []note ;_cead *prefix ;_fdgf bool ;_dadc bool ;_fcgc bool ;
_gfbg float64 ;_fgbb float64 ;_gbfg float64 ;_gfaca float64 ;_cgdcd bool ;_fdfg map[int64 ]map[int64 ]int64 ;_gegc map[string ]string ;_dagg *Options ;_ebeg []*headerFooterRef ;_cbafb []*headerFooterRef ;_fbee []*paragraph ;_bgdb []*paragraph ;_agcd map[string ]map[int64 ]*_bfc .CT_Ind ;
_fgggc float64 ;_afcg float64 ;_cdga []float64 ;_dgeda *_ef .Rectangle ;_aadb *_bfc .CT_PPr ;_aded []*paragraph ;};type span struct{_adg float64 ;_dca float64 ;_egf []*word ;};func _gcdea (_dcgac uint16 )string {switch _dcgac {case 0x429,0x401,0x801,0xc01,0x1001,0x1401,0x1801,0x1c01,0x2001,0x2401,0x2801,0x2c01,0x3001,0x3401,0x3801,0x3c01,0x4001,0x420,0x846,0x859,0x45f,0x460,0x463,0x48c:return "\u0041\u0072\u0061\u0062";
case 0x42b:return "\u0041\u0072\u006d\u006e";case 0x445,0x845,0x44d,0x458:return "\u0042\u0065\u006e\u0067";case 0x45d:return "\u0043\u0061\u006e\u0073";case 0x45c:return "\u0043\u0068\u0065\u0072";case 0x419,0x402,0x281a,0x422,0x819,0xc1a,0x1c1a,0x201a,0x301a,0x423,0x428,0x82c,0x42f,0x43f,0x440,0x843,0x444,0x450,0x46d,0x485:return "\u0043\u0072\u0079\u006c";
case 0x439,0x44e,0x44f,0x457,0x459,0x860,0x461,0x861:return "\u0044\u0065\u0076\u0061";case 0x45e,0x473,0x873:return "\u0045\u0074\u0068\u0069";case 0x437:return "\u0047\u0065\u006f\u0072";case 0x408:return "\u0047\u0072\u0065\u006b";case 0x447:return "\u0047\u0075\u006a\u0072";
case 0x446:return "\u0047\u0075\u0072\u0075";case 0x412:return "\u0048\u0061\u006e\u0067";case 0x804,0x1004:return "\u0048\u0061\u006e\u0073";case 0x404,0xc04,0x1404:return "\u0048\u0061\u006e\u0074";case 0x40d,0x43d:return "\u0048\u0065\u0062\u0072";case 0x411:return "\u004a\u0070\u0061\u006e";
case 0x453:return "\u004b\u0068\u006d\u0072";case 0x44b:return "\u004b\u006e\u0064\u0061";case 0x454:return "\u004c\u0061\u006f\u006f";case 0x409,0xc09,0x809,0x1009,0x403,0x406,0x413,0x813,0x479,0x40b,0x40c,0xc0c,0x407,0x807,0xc07,0x1007,0x1407,0x410,0x414,0x814,0x416,0x816,0x40a,0x41d,0x405,0x40e,0x415,0x41f,0x42d,0x424,0x426,0x427,0x418,0x818,0x241a,0x41a,0x491,0x83c,0x430,0x431,0x432,0x433,0x434,0x435,0x436,0x425,0x456,0x41b,0x1409,0x1809,0x1c09,0x2009,0x2409,0x2809,0x2c09,0x3009,0x3409,0x3809,0x3c09,0x4009,0x4409,0x4809,0x80a,0xc0a,0x100a,0x140a,0x180a,0x1c0a,0x200a,0x240a,0x280a,0x2c0a,0x300a,0x340a,0x380a,0x3c0a,0x400a,0x440a,0x480a,0x4c0a,0x500a,0x540a,0x80c,0x100c,0x140c,0x180c,0x1c0c,0x200c,0x240c,0x280c,0x2c0c,0x300c,0x340c,0x3c0c,0x380c,0x40f,0x810,0x417,0x81a,0x101a,0x141a,0x181a,0x2c1a,0x41c,0x81d,0x421,0x42c,0x42e,0x82e,0x438,0x43a,0x43b,0x83b,0xc3b,0x103b,0x143b,0x183b,0x1c3b,0x203b,0x243b,0x43e,0x83e,0x441,0x442,0x443,0x452,0x85d,0x85f,0x462,0x464,0x466,0x467,0x468,0x469,0x46a,0x46b,0x86b,0xc6b,0x46c,0x46e,0x46f,0x470,0x471,0x472,0x474,0x475,0x476,0x477,0x47a,0x47c,0x47e,0x481,0x482,0x483,0x484,0x486,0x487,0x488:return "\u004c\u0061\u0074\u006e";
case 0x44c:return "\u004d\u006c\u0079\u006d";case 0x850:return "\u004d\u006f\u006e\u0067";case 0x455:return "\u004d\u0079\u006d\u0072";case 0x448:return "\u004f\u0072\u0079\u0061";case 0x45b:return "\u0053\u0069\u006e\u0068";case 0x45a:return "\u0053\u0079\u0072\u0063";
case 0x449:return "\u0054\u0061\u006d\u006c";case 0x44a:return "\u0054\u0065\u006c\u0075";case 0x465:return "\u0054\u0068\u0061\u0061";case 0x41e:return "\u0054\u0068\u0061\u0069";case 0x851,0x451:return "\u0054\u0069\u0062\u0074";case 0x480:return "\u0055\u0069\u0067\u0068";
case 0x42a:return "\u0056\u0069\u0065\u0074";case 0x478:return "\u0059\u0069\u0069\u0069";};return "";};func (_abbfc *convertContext )makeBlockFromWdWsp (_ecgf *_bfc .WdWsp )(*block ,error ){if _eef :=_ecgf .WordprocessingShapeChoice1 ;_eef !=nil {if _bdad :=_eef .Txbx ;
_bdad !=nil {if _fcbdf :=_bdad .TxbxContent ;_fcbdf !=nil {for _ ,_cgdcb :=range _fcbdf .EG_BlockLevelElts {if _gdab :=_cgdcb .BlockLevelEltsChoice .EG_ContentBlockContent ;len (_gdab )> 0{if _gbebb :=_ecgf .SpPr ;_gbebb !=nil {if _badg :=_gbebb .Xfrm ;
_badg !=nil {if _cbcd :=_badg .Ext ;_cbcd !=nil {_dcaaf :=_ag .FromEMU (_cbcd .CxAttr );_dabef :=_ag .FromEMU (_cbcd .CyAttr );_dfeea :=&_ef .Rectangle {Top :_faad ,Bottom :_faad ,Left :_faad ,Right :_faad };_eabea ,_gbcc :=_abbfc .makePdfBlockFromCBCs ([][]*_bfc .EG_ContentBlockContent {_gdab },_dcaaf ,_dabef ,_dfeea ,false ,nil );
if _gbcc !=nil {return nil ,_gbcc ;};var _aebbe bool ;var _beef float64 ;var _aegga _da .Color ;if _fgge :=_gbebb .GeometryChoice .PrstGeom ;_fgge !=nil {if _fgge .PrstAttr ==_bf .ST_ShapeTypeRect {if _cfad :=_gbebb .Ln ;_cfad !=nil {if _ddegf :=_cfad .WAttr ;
_ddegf !=nil {_aebbe =true ;_beef =_ag .FromEMU (int64 (*_ddegf ));_aegga =_da .ColorBlack ;if _gbaac :=_cfad .LineFillPropertiesChoice .SolidFill ;_gbaac !=nil {if _feedg :=_gbaac .SrgbClr ;_feedg !=nil {_aegga =_da .ColorRGBFromHex ("\u0023"+_feedg .ValAttr );
};};};};};};_dfgdf :=&block {_de :_eabea ,_aae :_aebbe ,_fgcf :_beef ,_abf :_aegga };return _dfgdf ,nil ;};};};};};};};};return nil ,nil ;};func (_bfga *convertContext )addAbsoluteHeaderFooterCBCs (_cbea []*_bfc .EG_ContentBlockContent ){for _ ,_abegc :=range _cbea {for _ ,_efgf :=range _abegc .ContentBlockContentChoice .P {_bfga .newParagraph ();
if _efgf .PPr !=nil &&_efgf .PPr .PStyle ==nil {_gegef :=_bfga ._edega .Styles .ParagraphStyles ();for _ ,_abde :=range _gegef {if _gbecf :=_abde .X ().DefaultAttr ;_gbecf !=nil {if _cdeca :=_gbecf .Bool ;_cdeca !=nil &&*_cdeca {_efgf .PPr =_cfgfd (_efgf .PPr ,_abde .X ().PPr ,_abde .X ().RPr );
};if _fbbg :=_gbecf .ST_OnOff1 ;_fbbg ==_bc .ST_OnOff1On {_efgf .PPr =_cfgfd (_efgf .PPr ,_abde .X ().PPr ,_abde .X ().RPr );};break ;};};};_ecaa ,_dece :=_bfga .combinePPrWithStyles (_efgf .PPr );if _dece !=nil {_bfga ._cead =_dece ;};_bfga .assignPropsToAbsoluteParagraph (_ecaa ,_bfga ._cfeb );
_bfga .determineParagraphBounds ();_bfga .newLine ();_bfga .newWord ();_aecd :=_efgf .EG_PContent ;if len (_aecd )==0{_bfga .addEmptyLine ();}else {_bfga .addAnchorBlocks (_aecd );_bfga .addAnchorExtra (_aecd );_bfga .addAbsoluteEGPC (_aecd ,_ecaa );_bfga .addCurrentWordToParagraph ();
};if _bfga ._dadc {_bfga .addCurrentParagraphHeaderToCurrentPage ();}else {_bfga .addCurrentParagraphFooterToCurrentPage ();};};for _ ,_ggff :=range _abegc .ContentBlockContentChoice .Tbl {if _bfga ._cfeb ==nil {_bfga .newParagraph ();_bfga .determineParagraphBounds ();
_bfga .newLine ();_bfga .newWord ();};_bfga .addAbsoluteHeaderFooterTable (_ggff );};};};func (_dbba *convertContext )newWord (){_dbba ._efdb =&word {_ffb :true ,_gcd :_dbba ._gfcba ._ffg }};

// RegisterFontsFromDirectory registers all fonts from the given directory automatically detecting font families and styles. For composite fonts use RegisterCompositeFontsFromDirectory.
func RegisterFontsFromDirectory (dirName string )error {return _ef .RegisterFontsFromDirectory (dirName )};func (_edeg *convertContext )adjustHeights (_fgfd float64 ){if _edeg ._gfcba ._fgc < _fgfd {_edeg ._cfeb ._aee +=(_fgfd -_edeg ._gfcba ._fgc );_edeg ._gfcba ._fgc =_fgfd ;
};};func (_ecca *convertContext )newParagraph (){if _ecca ._fadc ==nil {_ecca .newPage ();};_bebc :=&paragraph {};_bebc ._agb =&_ef .Rectangle {};_bebc ._bdd =_ecca ._fadc ._fa ;_ecca ._cfeb =_bebc ;};func (_cddg *convertContext )getTableCellProperties (_aaefe *_da .Table ,_bdbc *_bfc .CT_TblPr ,_cgdce *_bfc .CT_TblPrEx ,_eebag []*_bfc .CT_TblStylePr ,_fabd int ,_bgebc *_bfc .CT_TcPr ,_dfcd *_bfc .CT_RPr ,_cecg int ,_dacde int ,_aeaa int )(*_bfc .CT_RPr ,_da .CellVerticalAlignment ,float64 ,float64 ,float64 ,float64 ,*_da .TableCell ){var _eafd *_da .TableCell ;
_bfcg :=1;_eaeg :=_bfc .NewCT_RPr ();var _fcgd ,_adegc int64 ;for _ ,_abede :=range _eebag {if _fabd ==0&&_abede .TypeAttr ==_bfc .ST_TblStyleOverrideTypeFirstRow {_fccae (_abede .PPr ,&_fcgd ,&_adegc );_bgebc =_bdcd (_bgebc ,_abede .TcPr );_dfcd =_bcag (_eaeg ,_abede .RPr );
break ;};if _cecg ==0&&_abede .TypeAttr ==_bfc .ST_TblStyleOverrideTypeFirstCol {_fccae (_abede .PPr ,&_fcgd ,&_adegc );_bgebc =_bdcd (_bgebc ,_abede .TcPr );_dfcd =_bcag (_eaeg ,_abede .RPr );};if _fabd ==_dacde -1&&_abede .TypeAttr ==_bfc .ST_TblStyleOverrideTypeLastRow {_fccae (_abede .PPr ,&_fcgd ,&_adegc );
_bgebc =_bdcd (_bgebc ,_abede .TcPr );_dfcd =_bcag (_eaeg ,_abede .RPr );};if _cecg ==_aeaa -1&&_abede .TypeAttr ==_bfc .ST_TblStyleOverrideTypeLastCol {_fccae (_abede .PPr ,&_fcgd ,&_adegc );_bgebc =_bdcd (_bgebc ,_abede .TcPr );_dfcd =_bcag (_eaeg ,_abede .RPr );
};if _fabd %2!=0&&_abede .TypeAttr ==_bfc .ST_TblStyleOverrideTypeBand1Horz {_fccae (_abede .PPr ,&_fcgd ,&_adegc );_bgebc =_bdcd (_bgebc ,_abede .TcPr );if _cecg ==0{_dfcd =_bcag (_eaeg ,_abede .RPr );}else {_dfcd =_cgdcg (_dfcd ,_abede .RPr );};};if _cecg %2!=0&&_abede .TypeAttr ==_bfc .ST_TblStyleOverrideTypeBand1Vert {_fccae (_abede .PPr ,&_fcgd ,&_adegc );
_bgebc =_bdcd (_bgebc ,_abede .TcPr );if _fabd ==0{_dfcd =_bcag (_eaeg ,_abede .RPr );}else {_dfcd =_cgdcg (_eaeg ,_abede .RPr );};};if _fabd %2==0&&_abede .TypeAttr ==_bfc .ST_TblStyleOverrideTypeBand2Horz {_fccae (_abede .PPr ,&_fcgd ,&_adegc );_bgebc =_bdcd (_bgebc ,_abede .TcPr );
if _cecg ==_aeaa -1{_dfcd =_bcag (_eaeg ,_abede .RPr );}else {_dfcd =_cgdcg (_eaeg ,_abede .RPr );};};if _cecg %2==0&&_abede .TypeAttr ==_bfc .ST_TblStyleOverrideTypeBand2Vert {_fccae (_abede .PPr ,&_fcgd ,&_adegc );_bgebc =_bdcd (_bgebc ,_abede .TcPr );
if _fabd ==_dacde -1{_dfcd =_bcag (_eaeg ,_abede .RPr );}else {_dfcd =_cgdcg (_eaeg ,_abede .RPr );};};};_babgd :=_gbed (_bdbc ,_cgdce ,_bgebc ,_fabd ,_cecg ,_dacde ,_aeaa );_cfeg :=_da .CellVerticalAlignmentTop ;_efge ,_cdbe ,_bdbd ,_ecea :=0.0,0.0,0.0,0.0;
if _babgd !=nil {if _babgd .GridSpan !=nil {_bfcg =int (_babgd .GridSpan .ValAttr );};_eafd =_aaefe .MultiColCell (_bfcg );if _bbec :=_babgd .TcBorders ;_bbec !=nil {if _fcdd :=_bbec .Left ;_fcdd !=nil {_baca ,_fbdae ,_cdfb :=_eeed (_fcdd );_eafd .SetBorder (_da .CellBorderSideLeft ,_baca ,_cdfb );
if _fbdae !=nil &&*_fbdae !=nil {_eafd .SetSideBorderColor (_da .CellBorderSideLeft ,*_fbdae );};};if _bfeb :=_bbec .Top ;_bfeb !=nil {_face ,_ggec ,_fggg :=_eeed (_bfeb );_eafd .SetBorder (_da .CellBorderSideTop ,_face ,_fggg );if _ggec !=nil &&*_ggec !=nil {_eafd .SetSideBorderColor (_da .CellBorderSideTop ,*_ggec );
};};if _cfedb :=_bbec .Right ;_cfedb !=nil {_egfc ,_fgbc ,_agcc :=_eeed (_cfedb );_eafd .SetBorder (_da .CellBorderSideRight ,_egfc ,_agcc );if _fgbc !=nil &&*_fgbc !=nil {_eafd .SetSideBorderColor (_da .CellBorderSideRight ,*_fgbc );};};if _fabab :=_bbec .Bottom ;
_fabab !=nil {_fcgef ,_agdc ,_adfbb :=_eeed (_fabab );_eafd .SetBorder (_da .CellBorderSideBottom ,_fcgef ,_adfbb );if _agdc !=nil &&*_agdc !=nil {_eafd .SetSideBorderColor (_da .CellBorderSideBottom ,*_agdc );};};}else {_eafd .SetBorder (_da .CellBorderSideAll ,_da .CellBorderStyleSingle ,_ggef (0.125));
_eafd .SetBorderColor (_da .ColorBlack );};_cddg .setCellBackgroundColor (_eafd ,_babgd .Shd );if _gbac :=_babgd .TcMar ;_gbac !=nil {_efge =_ggaa (_gbac .Left ,0);_cdbe =_ggaa (_gbac .Right ,0);_bdbd =_ggaa (_gbac .Top ,_fcgd );_ecea =_ggaa (_gbac .Bottom ,_adegc );
};if _bcgf :=_babgd .VAlign ;_bcgf !=nil {switch _bcgf .ValAttr {case _bfc .ST_VerticalJcCenter :_cfeg =_da .CellVerticalAlignmentMiddle ;case _bfc .ST_VerticalJcBottom :_cfeg =_da .CellVerticalAlignmentBottom ;};};};if _eafd ==nil {_eafd =_aaefe .NewCell ();
};return _dfcd ,_cfeg ,_efge ,_cdbe ,_bdbd ,_ecea ,_eafd ;};func _eabe (_bfce *_fca .Document )map[string ]string {_adbaa :=[]_fca .Paragraph {};_dccc :=map[string ]string {};for _ ,_gbcf :=range _bfce .Tables (){for _ ,_cede :=range _gbcf .Rows (){for _ ,_cbgd :=range _cede .Cells (){_adbaa =append (_adbaa ,_cbgd .Paragraphs ()...);
};};};_adbaa =append (_adbaa ,_bfce .Paragraphs ()...);for _ ,_bebb :=range _bfce .Headers (){_adbaa =append (_adbaa ,_bebb .Paragraphs ()...);for _ ,_faegc :=range _bebb .Tables (){for _ ,_bggbc :=range _faegc .Rows (){for _ ,_aadd :=range _bggbc .Cells (){_adbaa =append (_adbaa ,_aadd .Paragraphs ()...);
};};};};for _ ,_bbcba :=range _bfce .Footers (){_adbaa =append (_adbaa ,_bbcba .Paragraphs ()...);for _ ,_aebg :=range _bbcba .Tables (){for _ ,_cabd :=range _aebg .Rows (){for _ ,_fdcb :=range _cabd .Cells (){_adbaa =append (_adbaa ,_fdcb .Paragraphs ()...);
};};};};for _ ,_ecbgc :=range _adbaa {for _ ,_faec :=range _ecbgc .Runs (){for _ ,_debf :=range _faec .X ().EG_RunInnerContent {if _fgfb :=_debf .RunInnerContentChoice .InstrText ;_fgfb !=nil {_fade ,_afca :=_gccb (_fgfb .Content );if _fade !=""&&_afca !=""{_dccc [_fade ]=_afca ;
};};};};};return _dccc ;};func _eeed (_edag *_bfc .CT_Border )(_da .CellBorderStyle ,*_da .Color ,float64 ){if _edag ==nil {return _da .CellBorderStyleNone ,nil ,0;};var _cdfba _da .CellBorderStyle ;switch _edag .ValAttr {case _bfc .ST_BorderSingle :_cdfba =_da .CellBorderStyleSingle ;
case _bfc .ST_BorderDouble :_cdfba =_da .CellBorderStyleDouble ;default:_cdfba =_da .CellBorderStyleNone ;};_babfa :=0.0;if _fcfb :=_edag .SzAttr ;_fcfb !=nil {_babfa =float64 (*_fcfb )/8;};var _deeac _da .Color ;if _gabde :=_edag .ColorAttr ;_gabde !=nil {if _dcedfc :=_gabde .ST_HexColorRGB ;
_dcedfc !=nil {_deeac =_da .ColorRGBFromHex ("\u0023"+*_dcedfc );}else if _bcgfd :=_gabde .ST_HexColorAuto ;_bcgfd ==_bfc .ST_HexColorAutoAuto {_deeac =_da .ColorBlack ;if _babfa ==0{_babfa =2.0/8.0;};};};return _cdfba ,&_deeac ,_babfa ;};type link struct{_fbb string ;
_affd _bg .ST_TargetMode ;};func (_bdg *convertContext )addEmptyLine (){_bdg .addTextSymbol (&symbol {_aaa :"\u000d",_df :0,_dfc :_bdg ._cfeb ._cfed });};func (_adc *convertContext )addAnchorExtra (_gabg []*_bfc .EG_PContent ){for _ ,_dcea :=range _gabg {for _ ,_ddb :=range _dcea .PContentChoice .EG_ContentRunContent {if _dfff :=_ddb .ContentRunContentChoice .R ;
_dfff !=nil {for _ ,_gec :=range _dfff .Extra {if _deab ,_cff :=_gec .(*_bfc .AlternateContentRun );_cff {if _adae :=_deab .Choice ;_adae !=nil {if _dced :=_adae .Drawing ;_dced !=nil {for _ ,_agce :=range _dced .DrawingChoice {if _agce ==nil ||_agce .Anchor ==nil {continue ;
};_afa :=_agce .Anchor ;var _abd ,_ggg ,_afc ,_bcf float64 ;_fcgg ,_ebec :=_afa .PositionH ,_afa .PositionV ;if _dde :=_fcgg .PosHChoice ;_dde !=nil {if _dde .PosOffset !=nil {_abd =_ag .FromEMU (int64 (*_dde .PosOffset ));};};if _ecg :=_ebec .PosVChoice ;
_ecg !=nil {if _ecg .PosOffset !=nil {_ggg =_ag .FromEMU (int64 (*_ecg .PosOffset ));};};if _gea :=_afa .Extent ;_gea !=nil {_bcf =_ag .FromEMU (_gea .CxAttr );_afc =_ag .FromEMU (_gea .CyAttr );};_gfff :=_adc ._cfeb ._bdd +_ggg ;_cafb :=_gfff +_afc ;_cgcg :=_adc ._cfeb ._ddd +_abd ;
_egc :=_cgcg +_bcf ;_efea :=_ggg +_afc ;if _efea > _adc ._cfeb ._ce {_adc ._cfeb ._ce =_efea ;};if _afa .WrapTypeChoice !=nil &&_afa .WrapTypeChoice .WrapNone ==nil {_adc ._cfeb ._aeb =append (_adc ._cfeb ._aeb ,&zoneToSkip {_dc :&_ef .Rectangle {Top :_gfff ,Bottom :_cafb ,Left :_cgcg ,Right :_egc },_dda :_afa .WrapTypeChoice ,_ba :_afa .RelativeHeightAttr });
};if _bcgbf :=_afa .Graphic ;_bcgbf !=nil {if _bddge :=_bcgbf .GraphicData ;_bddge !=nil {for _ ,_bda :=range _bddge .Any {if _adce ,_dbe :=_bda .(*_bfc .WdWsp );_dbe {_abgb ,_fac :=_adc .makeBlockFromWdWsp (_adce );if _fac !=nil {_eg .Log .Debug ("C\u0061\u006e\u006e\u006ft \u0072e\u0061\u0064\u0020\u0062\u006co\u0063\u006b\u003a\u0020\u0025\u0073",_fac );
};if _abgb !=nil {_abgb ._de .Scale (_bcf /_abgb ._de .Width (),_afc /_abgb ._de .Height ());_abgb ._bfd =_cgcg ;_abgb ._ccd =_gfff ;if _afa .BehindDocAttr {_adc ._cfeb ._gd =append (_adc ._cfeb ._gd ,_abgb );}else {_adc ._cfeb ._ad =append (_adc ._cfeb ._ad ,_abgb );
};};};};};};};};};};};};};};};func _fdfa (_fgdd *_bfc .CT_TblWidth ,_fbbga ,_dbbaf float64 )float64 {if _fgdd !=nil {if _gdee :=_fgdd .WAttr ;_gdee !=nil {if _dbdg :=_gdee .ST_DecimalNumberOrPercent ;_dbdg !=nil {if _dcde :=_dbdg .ST_UnqualifiedPercentage ;
_dcde !=nil {switch _fgdd .TypeAttr {case _bfc .ST_TblWidthDxa :return float64 (*_dcde )/20;case _bfc .ST_TblWidthPct :return float64 (*_dcde )/100/50*_fbbga ;default:return _dbbaf ;};};};};};return _dbbaf ;};func (_ecb *convertContext )alignSymbolsVertically (){for _ ,_feg :=range _ecb ._cbaaa {for _ ,_dbd :=range _feg ._gc {for _ ,_gbec :=range _dbd ._ed {_cba :=0.0;
for _ ,_bbf :=range _gbec ._dgbf {for _ ,_egbg :=range _bbf ._egf {for _ ,_edd :=range _egbg ._edf {if _edd ._dfc > _cba {_cba =_edd ._dfc ;};};};};for _ ,_affa :=range _gbec ._dgbf {for _ ,_fdd :=range _affa ._egf {for _ ,_aea :=range _fdd ._edf {if _aea ._abg < _cba -_dbd ._ab {_aea ._cae =_cba -_dbd ._ab -_aea ._abg ;
};};};};};};};};var (_dae =_ggef (6);_ffa =_ggef (0.25);_cg =_ggef (1.9););func _dgbe (_dad string )[]*symbol {return []*symbol {{_aaa :_dad ,_dgda :true }}};func (_ggfg *convertContext )addAbsoluteCRC (_aec []*_bfc .EG_ContentRunContent ,_aba *_bfc .CT_PPr )bool {for _ ,_ddcc :=range _aec {if _ebgd :=_ddcc .ContentRunContentChoice .R ;
_ebgd !=nil {if _aba !=nil &&_aba .PStyle !=nil {_cadf :=_ggfg ._edega .GetStyleByID (_aba .PStyle .ValAttr );if _bbcb :=_cadf .X ();_bbcb !=nil {if _bbcb .QFormat !=nil &&_dgba (_bbcb .QFormat ){if _bbcb .RPr !=nil &&_aba .RPr !=nil {_aba .RPr =_adfd (_aba .RPr ,_bbcb .RPr );
};};if _bbcb .RPr !=nil {if _bbcb .UiPriority !=nil &&_bbcb .UiPriority .ValAttr > 0&&_ebgd .RPr ==nil {_aba .RPr =_adfd (_aba .RPr ,_bbcb .RPr );};_ebgd .RPr =_bcag (_ebgd .RPr ,_bbcb .RPr );};if _ggfg ._cead !=nil {_decd ,_dgdc :=_ggfg .getStyleProps (_aba .PStyle .ValAttr ,_cadf );
_aba =_cfgfd (_aba ,_decd ,_dgdc );_ebgd .RPr =_bcag (_ebgd .RPr ,_dgdc );};};};_bfdd :=_aba !=nil ||_ebgd .RPr !=nil ;if len (_ebgd .EG_RunInnerContent )==0&&_bfdd {_ggfg .addEmptyLine ();};_bfba :=_ccgd (_ggfg ._edega ,_ebgd .RPr ,_aba );if _ggfg ._cead !=nil {_ggfg .addAbsoluteRIC (nil ,_bfba ,_aba );
_ggfg ._cead =nil ;_ggfg ._cfeb ._fg =true ;};for _ ,_ccb :=range _ebgd .EG_RunInnerContent {if _ggfg .addAbsoluteRIC (_ccb ,_bfba ,_aba ){return true ;};_ggfg ._cfeb ._fg =false ;};for _ ,_dbf :=range _ebgd .Extra {if _adba ,_aeec :=_dbf .(*_bfc .AlternateContentRun );
_aeec {if _gfd :=_adba .Choice ;_gfd !=nil {if _agfd :=_gfd .Drawing ;_agfd !=nil {for _ ,_ffae :=range _agfd .DrawingChoice {if _ffae .Inline ==nil {continue ;};_edad :=_ffae .Inline ;_bcd :=_edad .Extent ;if _bcd ==nil {return false ;};_ccbc :=_ag .FromEMU (_bcd .CxAttr );
_fcf :=_ag .FromEMU (_bcd .CyAttr );if _dgf :=_edad .Graphic ;_dgf !=nil {if _bceb :=_dgf .GraphicData ;_bceb !=nil {for _ ,_ccc :=range _bceb .Any {if _fec ,_aac :=_ccc .(*_bfc .WdWsp );_aac {_aebc ,_cea :=_ggfg .makeBlockFromWdWsp (_fec );if _cea !=nil {_eg .Log .Debug ("C\u0061\u006e\u006e\u006ft \u0072e\u0061\u0064\u0020\u0062\u006co\u0063\u006b\u003a\u0020\u0025\u0073",_cea );
};if _aebc ==nil {continue ;};_aebc ._de .Scale (_ccbc /_aebc ._de .Width (),_fcf /_aebc ._de .Height ());_ggfg .addInlineSymbol (&symbol {_dfc :_fcf ,_df :_ccbc ,_bfb :_aebc });};};};};};};};};};};};return false ;};func (_eaff *convertContext )getSectPrHeaderAndFooterRef (_bfbg *_bfc .CT_SectPr ,_cdgaa int )([]*headerFooterRef ,[]*headerFooterRef ){if len (_bfbg .EG_HdrFtrReferences )< 1&&_bfbg .PgSz ==nil {_fedg :=&headerFooterRef {_bbed :false ,_gbgd :false ,_cddga :_cdgaa };
return []*headerFooterRef {_fedg },[]*headerFooterRef {_fedg };};var (_acad []*headerFooterRef ;_cdce []*headerFooterRef ;);for _ ,_gcfdf :=range _bfbg .EG_HdrFtrReferences {if _cecb :=_gcfdf .HdrFtrReferencesChoice .HeaderReference ;_cecb !=nil {_ccdf :=&headerFooterRef {_gbgd :true ,_ddag :_cecb .IdAttr ,_cadc :_cecb .TypeAttr ,_beff :_eaff ._ebeg [len (_eaff ._ebeg )-1]._cddga ,_cddga :_cdgaa };
_acad =append (_acad ,_ccdf );}else {_eaa :=&headerFooterRef {_gbgd :false ,_bbed :false ,_cddga :_cdgaa };_acad =append (_acad ,_eaa );};if _eaca :=_gcfdf .HdrFtrReferencesChoice .FooterReference ;_eaca !=nil {_gefbb :=&headerFooterRef {_bbed :true ,_ddag :_eaca .IdAttr ,_cadc :_eaca .TypeAttr ,_beff :_eaff ._cbafb [len (_eaff ._cbafb )-1]._cddga ,_cddga :_cdgaa };
_cdce =append (_cdce ,_gefbb );}else {_fegd :=&headerFooterRef {_bbed :false ,_gbgd :false ,_cddga :_cdgaa };_cdce =append (_cdce ,_fegd );};};return _acad ,_cdce ;};type note struct{_cf string ;_bfcb []*_bfc .EG_BlockLevelElts ;_bce *_da .Block ;};func (_dcbg *convertContext )addCurrentParagraphFooterToCurrentPage (){_dcbg .alignParagraph ();
_dcbg ._fadc ._gf =append (_dcbg ._fadc ._gf ,_dcbg ._cfeb );};func _ddddb (_dfafea ,_daegg *_bfc .CT_Border ,_gdec bool )*_bfc .CT_Border {if _gdec {return _dfafea ;};return _daegg ;};

// Options contains the options for convert process -
// e.g ProcessFields is when document contains fields
// and the value need to be processed also.
type Options struct{

// ProcessFields process the document fields if set to true, default is `false`.
ProcessFields bool ;

// EnableFontSubsetting process document with subsetting font to reduce size result.
// Default value is `true`.
EnableFontSubsetting bool ;

// FontFiles location of fonts for convert process.
FontFiles []string ;

// FontDirectory location of font directory for convert process.
// This will load all font files inside directory if set
// we recommend to use FontFiles for better performance.
FontDirectory string ;

// DefaultPageSize is applied when there is no page size explicitly set in the document.
// A4 is the default option.
DefaultPageSize _ef .PageSize ;

// DefaultFontSize is applied when there is no font size explicitly set in the document
// Possible values compatible with MS Word are 11 or 12, by default it is 12.
DefaultFontSize int ;

// RtlFontFile is applied for RTL paragraphs. Only one font is currently supported
// for RTL paragraphs within one document.
RtlFontFile string ;

// DefaultImageEncoder sets the default image encoder for the convert process.
// Default value is nil, which will use the best suitable encoder based on image format.
// If image is `jpg` or `jpeg` will use `DCTEncoder` if image is `png` or in other format will use `FlateEncoder`.
// Available options are `FlateEncoder`, `DCTEncoder`, `LZWEncoder`, `JBIG2Encoder`, `CCITTFaxEncoder`, and `RawEncoder`.
DefaultImageEncoder _fb .StreamEncoder ;};func _daeg (_egffb int ,_dfebb bool )string {_bgefa :=_a .NewBuffer ([]byte {});for _ ,_ggea :=range _cegdg {for {if _egffb < _ggea ._gcdb {break ;};_bgefa .WriteString (_ggea ._ffaf );_egffb -=_ggea ._gcdb ;};
};_affaa :=_bgefa .String ();if _dfebb {_affaa =_ga .ToUpper (_affaa );};return _affaa ;};func (_ggbd *convertContext )drawHeaderFooter (){_ggbd .setPagesHeaderFooterRefs ();_ggbd ._affc .PageFinalize (func (_bbgb _da .PageFinalizeFunctionArgs )error {_fded :=_ggbd ._cbaaa [_bbgb .PageNum -1];
_ggbd ._fadc =_fded ;_ggbd ._fadc ._fcb =nil ;_ggbd ._fadc ._gf =nil ;_ggbd .assignHeaderFooterToPage (_fded );_gafcc :=_da .NewBlock (_ggbd ._cdga [0],_ggbd ._fgggc );_gafcc .SetPos (0,0);_gafcc .SetMargins (0,0,0,0);_gfcgd :=_bacac (_ggbd ._affc ,_gafcc ,_ggbd ._fadc ._fcb ,_ggbd ._fgbb ,_bbgb );
_ggbd ._fgggc =_gfcgd ;_gfada :=_da .NewBlock (_ggbd ._cdga [0],_ggbd ._afcg );_gfada .SetPos (0,0);_gfada .SetMargins (0,0,0,0);_gfcgd =_bacac (_ggbd ._affc ,_gfada ,_ggbd ._fadc ._gf ,_ggbd ._gbfg ,_bbgb );_ggbd ._afcg =_gfcgd ;_ggbd ._affc .Draw (_gafcc );
_ggbd ._affc .Draw (_gfada );return nil ;});};func _adcfg (_fdeba string )(float64 ,float64 ){_abba :=_ga .SplitN (_fdeba ,"\u002c",2);_bacab :=_ga .ReplaceAll (_abba [0],"\u0070\u0074","");_beca :=_ga .ReplaceAll (_abba [1],"\u0070\u0074","");_adea ,_eacc :=_b .ParseFloat (_bacab ,64);
if _eacc !=nil {_eg .Log .Debug ("\u0045\u0052RO\u0052\u003a\u0020U\u006e\u0061\u0062\u006ce p\u0061rs\u0065\u0020\u0078\u003a\u0020\u0025\u0076 t\u006f\u0020\u0066\u006c\u006f\u0061\u00746\u0034",_eacc .Error ());};_eeecd ,_eacc :=_b .ParseFloat (_beca ,64);
if _eacc !=nil {_eg .Log .Debug ("\u0045\u0052RO\u0052\u003a\u0020U\u006e\u0061\u0062\u006ce p\u0061rs\u0065\u0020\u0079\u003a\u0020\u0025\u0076 t\u006f\u0020\u0066\u006c\u006f\u0061\u00746\u0034",_eacc .Error ());};return _adea ,_eeecd ;};func _dbbf (_dbbfa ,_bfab *_bfc .CT_PPrGeneral )*_bfc .CT_PPrGeneral {if _dbbfa ==nil {return _dbbfa ;
};if _bfab ==nil {return _dbbfa ;};if _dbbfa .PStyle ==nil {_dbbfa .PStyle =_bfab .PStyle ;};if _dbbfa .KeepNext ==nil {_dbbfa .KeepNext =_bfab .KeepNext ;};if _dbbfa .KeepLines ==nil {_dbbfa .KeepLines =_bfab .KeepLines ;};if _dbbfa .PageBreakBefore ==nil {_dbbfa .PageBreakBefore =_bfab .PageBreakBefore ;
};if _dbbfa .FramePr ==nil {_dbbfa .FramePr =_bfab .FramePr ;};if _dbbfa .WidowControl ==nil {_dbbfa .WidowControl =_bfab .WidowControl ;};if _dbbfa .NumPr ==nil {_dbbfa .NumPr =_bfab .NumPr ;};if _dbbfa .SuppressLineNumbers ==nil {_dbbfa .SuppressLineNumbers =_bfab .SuppressLineNumbers ;
};if _dbbfa .PBdr ==nil {_dbbfa .PBdr =_bfab .PBdr ;};if _dbbfa .Shd ==nil {_dbbfa .Shd =_bfab .Shd ;};if _dbbfa .Tabs ==nil {_dbbfa .Tabs =_bfab .Tabs ;};if _dbbfa .SuppressAutoHyphens ==nil {_dbbfa .SuppressAutoHyphens =_bfab .SuppressAutoHyphens ;};
if _dbbfa .Kinsoku ==nil {_dbbfa .Kinsoku =_bfab .Kinsoku ;};if _dbbfa .WordWrap ==nil {_dbbfa .WordWrap =_bfab .WordWrap ;};if _dbbfa .OverflowPunct ==nil {_dbbfa .OverflowPunct =_bfab .OverflowPunct ;};if _dbbfa .TopLinePunct ==nil {_dbbfa .TopLinePunct =_bfab .TopLinePunct ;
};if _dbbfa .AutoSpaceDE ==nil {_dbbfa .AutoSpaceDE =_bfab .AutoSpaceDE ;};if _dbbfa .AutoSpaceDN ==nil {_dbbfa .AutoSpaceDN =_bfab .AutoSpaceDN ;};if _dbbfa .Bidi ==nil {_dbbfa .Bidi =_bfab .Bidi ;};if _dbbfa .AdjustRightInd ==nil {_dbbfa .AdjustRightInd =_bfab .AdjustRightInd ;
};if _dbbfa .SnapToGrid ==nil {_dbbfa .SnapToGrid =_bfab .SnapToGrid ;};if _dbbfa .Spacing ==nil {_dbbfa .Spacing =_bfab .Spacing ;};if _dbbfa .Ind ==nil {_dbbfa .Ind =_bfab .Ind ;};if _dbbfa .ContextualSpacing ==nil {_dbbfa .ContextualSpacing =_bfab .ContextualSpacing ;
};if _dbbfa .MirrorIndents ==nil {_dbbfa .MirrorIndents =_bfab .MirrorIndents ;};if _dbbfa .SuppressOverlap ==nil {_dbbfa .SuppressOverlap =_bfab .SuppressOverlap ;};if _dbbfa .Jc ==nil {_dbbfa .Jc =_bfab .Jc ;};if _dbbfa .TextDirection ==nil {_dbbfa .TextDirection =_bfab .TextDirection ;
};if _dbbfa .TextAlignment ==nil {_dbbfa .TextAlignment =_bfab .TextAlignment ;};if _dbbfa .TextboxTightWrap ==nil {_dbbfa .TextboxTightWrap =_bfab .TextboxTightWrap ;};if _dbbfa .OutlineLvl ==nil {_dbbfa .OutlineLvl =_bfab .OutlineLvl ;};if _dbbfa .DivId ==nil {_dbbfa .DivId =_bfab .DivId ;
};if _dbbfa .CnfStyle ==nil {_dbbfa .CnfStyle =_bfab .CnfStyle ;};if _dbbfa .PPrChange ==nil {_dbbfa .PPrChange =_bfab .PPrChange ;};return _dbbfa ;};func (_ffgc *convertContext )addParagraphWithTableToHeaderFooter (_aebb _da .Table ,_fddd ,_cecgc float64 ){_ffgc .newParagraph ();
_ffgc ._cfeb ._agb =&_ef .Rectangle {Top :_ggef (2),Bottom :_ggef (2),Left :0,Right :0};_ffgc ._cfeb ._aa =&tableWrapper {_cef :&_aebb ,_ec :_fddd };_ffgc ._cfeb ._fbc =_cecgc ;_ffgc ._cfeb ._aee =_aebb .Height ();_ffgc .determineParagraphBounds ();if _ffgc ._dadc {_ffgc .addCurrentParagraphHeaderToCurrentPage ();
}else if _ffgc ._fcgc {_ffgc .addCurrentParagraphFooterToCurrentPage ();};};type line struct{_bab float64 ;_cbca float64 ;_fbg float64 ;_ffg float64 ;_fgc float64 ;_dgbf []*span ;_fgd bool ;};type tableCellProperties struct{_dgb *_bfc .CT_Tc ;_cgd *_bfc .CT_TblPr ;
_fde *_bfc .CT_TblPrEx ;_cbc int ;_eda int ;_fgg int ;_gda int ;_aab []*_bfc .CT_TblStylePr ;_eec *_bfc .CT_PPrGeneral ;_fgf *_bfc .CT_RPr ;_fad bool ;_dacg int ;_edb bool ;_fda bool ;_ceg float64 ;};func _ggef (_cgdeg float64 )float64 {return _cgdeg *_ag .Millimeter };
func _ddga (_dcedfb *_fca .Document ,_gddff *_bfc .CT_TblPr )(*_bfc .CT_TblPr ,*_bfc .CT_PPrGeneral ,*_bfc .CT_RPr ){_cdfc :=_bfc .NewCT_PPrGeneral ();_bbfcg :=_bfc .NewCT_RPr ();if _gddff ==nil {_gddff =_bfc .NewCT_TblPr ();}else {if _gddff .TblStyle !=nil {_gddff ,_cdfc ,_bbfcg =_acbd (_dcedfb ,_gddff .TblStyle .ValAttr ,_gddff ,_cdfc ,_bbfcg );
};};return _gddff ,_cdfc ,_bbfcg ;};func (_cacg *convertContext )makePdfBlockFromCBCs (_fbcd [][]*_bfc .EG_ContentBlockContent ,_febg ,_beae float64 ,_dbcg *_ef .Rectangle ,_afdac bool ,_bcfgd *prefix )(*_da .Block ,error ){if _dbcg ==nil {_dbcg =&_ef .Rectangle {};
};_fege :=&_ef .Rectangle {Top :_dbcg .Top ,Bottom :_beae -_dbcg .Bottom ,Left :_dbcg .Left ,Right :_febg -_dbcg .Right };_cgbb :=_ef .MakeTempCreator (_febg ,_beae );_fbbfb :=&convertContext {_affc :_cgbb ,_edega :_cacg ._edega ,_gefe :_fege ,_cead :_bcfgd ,_dagg :_cacg ._dagg };
for _ ,_agbf :=range _fbcd {_fbbfb .addAbsoluteCBCs (_agbf ,nil );};if _afdac {_ffafa :=0.0;for _ ,_bccga :=range _fbbfb ._cbaaa {for _ ,_eecf :=range _bccga ._gc {_ffafa +=(_eecf ._aee +_eecf ._agb .Top +_eecf ._agb .Bottom );};};_fege .Bottom =_ffafa -_dbcg .Bottom ;
_cgbb =_ef .MakeTempCreator (_febg ,_ffafa );_fbbfb =&convertContext {_affc :_cgbb ,_edega :_cacg ._edega ,_gefe :_fege ,_cead :_bcfgd ,_dagg :_cacg ._dagg };for _ ,_dfab :=range _fbcd {_fbbfb .addAbsoluteCBCs (_dfab ,nil );};};_fbbfb .alignSymbolsVertically ();
_fbbfb ._affc .NewPage ();_fbbfb .drawPage (_fbbfb ._cbaaa [len (_fbbfb ._cbaaa )-1]);return _ef .MakeBlockFromCreator (_cgbb );};func init (){_dgdeb =_be .MustCompile ("\u0053E\u0054 \u0028\u002e\u002b\u0029\u0020\u0022\u0028\u002e\u002b\u0029\u0022");
_gacba =_be .MustCompile ("\u0052\u0045\u0046\u0020\u0028\u002e\u002b\u003f\u0029\u0020");};type headerFooterRef struct{_gbgd bool ;_bbed bool ;_ddag string ;_cadc _bfc .ST_HdrFtr ;_beff int ;_cddga int ;};func (_eeaa *convertContext )addSeparator (){_eeaa .newParagraph ();
_eeaa ._cfeb ._fcg =true ;_eeaa ._cfeb ._aee =_dae ;if _eeaa .currentParagraphOverflowsCurrentPage (){_eeaa .moveCurrentParagraphToNewPage ();};_eeaa .addCurrentParagraphToCurrentPage ();};func (_ggfb *convertContext )addCurrentParagraphHeaderToCurrentPage (){_ggfb .alignParagraph ();
_ggfb ._fadc ._fcb =append (_ggfb ._fadc ._fcb ,_ggfb ._cfeb );};func _gcega (_dagb string )uint16 {_egbf ,_acdgg :=_gdae [_dagb ];if !_acdgg {return 0;};return _egbf ;};func _adfd (_gfdb *_bfc .CT_ParaRPr ,_febe *_bfc .CT_RPr )*_bfc .CT_ParaRPr {if _febe ==nil {return _gfdb ;
};if _gfdb ==nil {_gfdb =_bfc .NewCT_ParaRPr ();if _febe .B !=nil {_gfdb .B =_febe .B ;};if _febe .BCs !=nil {_gfdb .BCs =_febe .BCs ;};if _febe .I !=nil {_gfdb .I =_febe .I ;};if _febe .ICs !=nil {_gfdb .ICs =_febe .ICs ;};if _febe .U !=nil {_gfdb .U =_febe .U ;
};if _febe .Color !=nil {_gfdb .Color =_febe .Color ;};return _gfdb ;};if _gfdb .B !=_febe .B {_gfdb .B =_febe .B ;};if _gfdb .BCs !=_febe .BCs {_gfdb .BCs =_febe .BCs ;};if _gfdb .I !=_febe .I {_gfdb .I =_febe .I ;};if _gfdb .ICs !=_febe .ICs {_gfdb .ICs =_febe .ICs ;
};if _gfdb .U !=_febe .U {_gfdb .U =_febe .U ;};if _gfdb .Color !=_febe .Color {_gfdb .Color =_febe .Color ;};return _gfdb ;};func _dafa (_abdg *_bfc .EG_RunInnerContent )bool {if _cabea :=_abdg .RunInnerContentChoice .Br ;_cabea !=nil {return _cabea .TypeAttr ==_bfc .ST_BrTypeTextWrapping ||_cabea .TypeAttr ==_bfc .ST_BrTypeUnset ;
};return false ;};func (_caggg *convertContext )makePdfImageFromRelId (_abc *string )(*_da .Image ,error ){if _abc !=nil {_adgg ,_cfebc :=_caggg ._edega .GetHeaderFooterImageObjByRelId (*_abc ,_caggg ._dadc ,_caggg ._fcgc );if _cfebc !=nil {return nil ,_cfebc ;
};_bbdcb ,_cfebc :=_dac .Open (_adgg .Path );if _cfebc !=nil {return nil ,_cfebc ;};_edbga ,_cfebc :=_d .ReadAll (_bbdcb );if _cfebc !=nil {return nil ,_cfebc ;};_fgef ,_cfebc :=_caggg ._affc .NewImageFromData (_edbga );if _cfebc !=nil {return nil ,_cfebc ;
};if _ef .DefaultImageEncoder !=nil {_fgef .SetEncoder (_ef .DefaultImageEncoder );}else {_fgef .SetEncoder (_fb .NewFlateEncoder ());if _ga .ToLower (_adgg .Format )=="\u006a\u0070\u0067"||_ga .ToLower (_adgg .Format )=="\u006a\u0070\u0065\u0067"{_fgef .SetEncoder (_fb .NewDCTEncoder ());
};};return _fgef ,nil ;};return nil ,nil ;};type borderLine struct{_aef _da .Color ;_gcdd _ef .BorderPosition ;_daa float64 ;_ea float64 ;_gbg float64 ;};func (_ddbe *convertContext )addCellToTable (_fdf *_da .Table ,_ffbad *_bfc .CT_Tc ,_eaee *_bfc .CT_TblPr ,_cdb *_bfc .CT_TblPrEx ,_ccfa ,_gege ,_dcdd ,_edggf int ,_gffe []*_bfc .CT_TblStylePr ,_ageaa *_bfc .CT_PPrGeneral ,_gddf *_bfc .CT_RPr ,_dcbc bool ,_caga int ,_ccgec []float64 )int {_gddf ,_gaaaa ,_bbab ,_accf ,_ecdd ,_edgd ,_acgb :=_ddbe .getTableCellProperties (_fdf ,_eaee ,_cdb ,_gffe ,_ccfa ,_ffbad .TcPr ,_gddf ,_gege ,_dcdd ,_edggf );
_acgb .SetVerticalAlignment (_gaaaa );_acgb .SetIndent (_bbab );var _adfb *_da .StyledParagraph ;_ddcd :=_ffbad .EG_BlockLevelElts ;_daeb :=_ddbe ._affc .NewDivision ();_dadga :=_ddbe ._affc .NewList ();_dfaf :=true ;_daeb .SetMargins (0.0,_accf ,_ecdd ,_edgd );
_aedec :=false ;_agd :=-1;_bag :=false ;for _ ,_eeda :=range _ddcd {for _ ,_gcg :=range _eeda .BlockLevelEltsChoice .EG_ContentBlockContent {for _ ,_gdefb :=range _gcg .ContentBlockContentChoice .P {_bada :=_ddbe ._affc .NewStyledParagraph ();if _aedec {_ggca :=_bada .Append ("\u000a");
_fgaf :=_ddbe ._affc .NewTextStyle ();_fgaf .FontSize =0;_ggca .Style =_fgaf ;};_eecb :=false ;_acdc ,_dedd :=_ddbe .combinePPrWithStyles (_gdefb .PPr );if _acdc !=nil &&_acdc .PStyle !=nil {_bedf :=_ddbe ._edega .GetStyleByID (_acdc .PStyle .ValAttr );
if _bbbdg :=_bedf .X ();_bbbdg !=nil {if _bbbdg .QFormat !=nil &&_dgba (_bbbdg .QFormat ){if _acdc .RPr !=nil &&_bbbdg .RPr !=nil {_acdc .RPr =_adfd (_acdc .RPr ,_bbbdg .RPr );};_acdc =_cfgfd (_gdefb .PPr ,_bbbdg .PPr ,_bbbdg .RPr );_eecb =true ;}else {_fgdg ,_adag :=_ddbe .getStyleProps (_acdc .PStyle .ValAttr ,_bedf );
_acdc =_cfgfd (_gdefb .PPr ,_fgdg ,_adag );};};}else {if _bag {_ccfb :=_daeb .Add (_dadga );if _ccfb !=nil {_eg .Log .Debug ("\u0045\u0052\u0052O\u0052\u003a\u0020\u0055\u006e\u0061\u0062\u006c\u0065\u0020\u0074\u006f\u0020\u0061\u0064\u0064\u0020\u006c\u0069\u0073\u0074\u0020\u0074\u006f\u0020\u0064\u0069\u0076\u0069s\u0069\u006f\u006e\u0020\u0028\u0025\u0073\u0029",_ccfb .Error ());
};_dadga =_ddbe ._affc .NewList ();_bag =false ;_dfaf =false ;};_ddec :=_ddbe ._edega .Styles .ParagraphStyles ();for _ ,_dbad :=range _ddec {if _bbge :=_dbad .X ().DefaultAttr ;_bbge !=nil {if _acgg :=_bbge .Bool ;_acgg !=nil &&*_acgg {_acdc =_cfgfd (_gdefb .PPr ,_dbad .X ().PPr ,_dbad .X ().RPr );
};if _bgdf :=_bbge .ST_OnOff1 ;_bgdf ==_bc .ST_OnOff1On {_acdc =_cfgfd (_gdefb .PPr ,_dbad .X ().PPr ,_dbad .X ().RPr );};break ;};};};if !_eecb {_acdc =_cfgfd (_gdefb .PPr ,_ageaa ,_gddf );};var _dddd *_da .TextStyle ;if _dedd !=nil &&_dedd ._fgdbb {_bag =true ;
};if _gdefb .EG_PContent ==nil {_gegee :="\u0020";_aedec =true ;_bada .Append (_gegee );}else {for _acfd ,_gfde :=range _gdefb .EG_PContent {if _caga !=-1&&_acfd < _caga {continue ;};_cfgf :=_gfde .PContentChoice .EG_ContentRunContent ;_affb :=&link {};
if _gfde .PContentChoice .Hyperlink !=nil {_cfgf =_gfde .PContentChoice .Hyperlink .PContentChoice .EG_ContentRunContent ;_gfcce :=_ddbe ._edega .DocRels ().GetByRelId (*_gfde .PContentChoice .Hyperlink .IdAttr );_affb ._fbb =_gfcce .X ().CT_Relationship .TargetAttr ;
_affb ._affd =_gfcce .X ().CT_Relationship .TargetModeAttr ;};_efceb :=_da .TextStyle {};for _ ,_dbda :=range _cfgf {if _gfae :=_dbda .ContentRunContentChoice .Sdt ;_gfae !=nil {if _gfae .SdtContent !=nil {for _ ,_aafg :=range _gfae .SdtContent .EG_PContent {for _ ,_ffab :=range _aafg .PContentChoice .EG_ContentRunContent {if _abbf :=_ffab .ContentRunContentChoice .R ;
_abbf !=nil {_aedec ,_agd ,_dfaf ,_efceb =_ddbe .processCtr (_abbf ,_acdc ,_aedec ,_affb ,_bada ,_dcbc ,_caga ,_acfd ,_agd ,_daeb ,_dfaf );if _dddd ==nil {_dddd =&_efceb ;};};if _agd > -1{break ;};};};};};if _ddff :=_dbda .ContentRunContentChoice .R ;_ddff !=nil {_aedec ,_agd ,_dfaf ,_efceb =_ddbe .processCtr (_ddff ,_acdc ,_aedec ,_affb ,_bada ,_dcbc ,_caga ,_acfd ,_agd ,_daeb ,_dfaf );
if _dddd ==nil {_dddd =&_efceb ;};};};};};if !_aedec {_dgbc :=_ccgd (_ddbe ._edega ,_bfc .NewCT_RPr (),_acdc );_eff :=_bada .Append ("\u0020");_eff .Style ,_ ,_ ,_ =_ddbe .makeRunStyle (_dgbc ,false ,false ,false ,false ,false );};if _bada !=nil {if _gaaaa ==_da .CellVerticalAlignmentTop {_acdc .TextAlignment =_bfc .NewCT_TextAlignment ();
_acdc .TextAlignment .ValAttr =_bfc .ST_TextAlignmentTop ;};_ddbe .assignPropsToRelativeParagraph (_acdc ,_bada );if _bag {_ffac :=_dadga .Marker ();for _ ,_aafb :=range _dedd ._bdee {if _cdeb ,_afda :=_afgbd [_aafb ];_afda {_ffac .Text =string (rune (_cdeb ));
if _dddd !=nil {_ffac .Style =*_dddd ;};};};_ ,_cdba :=_dadga .Add (_bada );if _cdba !=nil {_eg .Log .Debug ("\u0045\u0052\u0052\u004f\u0052:\u0020\u0055\u006e\u0061\u0062\u006c\u0065\u0020\u0074\u006f\u0020\u0061\u0064d\u0020\u0063\u006f\u006e\u0074\u0065\u006e\u0074\u0020\u0074\u006f\u0020\u006c\u0069\u0073\u0074\u0020\u0028\u0025\u0073\u0029",_cdba .Error ());
};}else if _dfaf &&_adfb ==nil {_adfb =_bada ;}else {if _dfaf {_aefc :=_daeb .Add (_adfb );if _aefc !=nil {_eg .Log .Debug ("\u0045R\u0052\u004fR\u003a\u0020\u0055n\u0061\u0062\u006c\u0065\u0020\u0074\u006f \u0061\u0064\u0064\u0020\u0063\u006fn\u0074\u0065\u006e\u0074\u0020\u0074\u006f\u0020\u0064\u0069\u0076i\u0073\u0069\u006f\u006e\u0020\u0028\u0025\u0073\u0029",_aefc .Error ());
};_dfaf =false ;};_cacaf :=_daeb .Add (_bada );if _cacaf !=nil {_eg .Log .Debug ("\u0045R\u0052\u004fR\u003a\u0020\u0055n\u0061\u0062\u006c\u0065\u0020\u0074\u006f \u0061\u0064\u0064\u0020\u0063\u006fn\u0074\u0065\u006e\u0074\u0020\u0074\u006f\u0020\u0064\u0069\u0076i\u0073\u0069\u006f\u006e\u0020\u0028\u0025\u0073\u0029",_cacaf .Error ());
};};if _ccfa > 0&&_ccgec [_gege ]< _bada .Width (){_ccgec [_gege ]=_bada .Width ();};};};};};if _bag {_ggge :=_daeb .Add (_dadga );if _ggge !=nil {_eg .Log .Debug ("\u0045\u0052\u0052O\u0052\u003a\u0020\u0055\u006e\u0061\u0062\u006c\u0065\u0020\u0074\u006f\u0020\u0061\u0064\u0064\u0020\u006c\u0069\u0073\u0074\u0020\u0074\u006f\u0020\u0064\u0069\u0076\u0069s\u0069\u006f\u006e\u0020\u0028\u0025\u0073\u0029",_ggge .Error ());
};};if _dfaf {_fdcf ,_cded ,_ecee ,_efgg :=_adfb .GetMargins ();_adfb .SetMargins (_fdcf ,_accf +_cded ,_ecdd +_ecee ,_edgd +_efgg );_dffb :=_acgb .SetContent (_adfb );if _dffb !=nil {_eg .Log .Debug ("\u0045\u0052\u0052\u004f\u0052:\u0020\u0055\u006e\u0061\u0062\u006c\u0065\u0020\u0074\u006f\u0020\u0073\u0065t\u0020\u0063\u006f\u006e\u0074\u0065\u006e\u0074\u0020\u0074\u006f\u0020\u0063\u0065\u006c\u006c\u0020\u0028\u0025\u0073\u0029",_dffb .Error ());
};}else {_eadc :=_acgb .SetContent (_daeb );if _eadc !=nil {_eg .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u0055\u006e\u0061\u0062\u006c\u0065\u0020\u0074\u006f\u0020\u0073e\u0074\u0020\u0064\u0069\u0076\u0069\u0073i\u006f\u006e\u0020\u0063\u006f\u006e\u0074\u0065\u006e\u0074\u0020t\u006f\u0020\u0063\u0065\u006c\u006c\u0020\u0028\u0025\u0073\u0029",_eadc .Error ());
};};return _agd ;};func (_bcgd *convertContext )makeRunStyle (_bcfga *_bfc .CT_RPr ,_addb ,_edfa ,_efcd ,_fcddae ,_gacg bool )(_da .TextStyle ,bool ,bool ,*_da .Color ){var _dbde *_da .Color ;_baeb :=_bcgd ._affc .NewTextStyle ();if _bcfga !=nil {_ffefa :=_ef .FontStyle_Regular ;
_fdea :=_dgba (_bcfga .B );_dbbcf :=_dgba (_bcfga .I );if _fdea &&_dbbcf {_ffefa =_ef .FontStyle_BoldItalic ;}else if _fdea {_ffefa =_ef .FontStyle_Bold ;}else if _dbbcf {_ffefa =_ef .FontStyle_Italic ;};if _fcddae {_baeb .Font =_ef .AssignStdFontByName (_baeb ,"\u0053\u0079\u006d\u0062\u006f\u006c");
}else {_ecdcc :="\u0064e\u0066\u0061\u0075\u006c\u0074";if _gefa :=_bcfga .RFonts ;_gefa !=nil {if _gccbc :=_gefa .AsciiAttr ;_gccbc !=nil {_ecdcc =*_gccbc ;}else if _baeg :=_gefa .HAnsiAttr ;_baeg !=nil {_ecdcc =*_baeg ;}else if _cefdd :=_gefa .CsAttr ;
_cefdd !=nil {_ecdcc =*_cefdd ;}else {_ccdff :=_bcgd ._ggbe ;if _ccdff !=nil {_edfe :="";if _dbccc :=_ccdff .RFonts ;_dbccc !=nil {if _fcfda :=_gefa .HintAttr ;_fcfda ==_bfc .ST_HintEastAsia {if _dbccc .EastAsiaAttr !=nil {_ecdcc =*_ccdff .RFonts .EastAsiaAttr ;
}else {if _dbccc .EastAsiaThemeAttr ==_bfc .ST_ThemeMajorEastAsia {_edfe =_dbcc ;};if _dbccc .EastAsiaThemeAttr ==_bfc .ST_ThemeMinorEastAsia {_edfe =_egba ;};};}else {if _cacff :=_dbccc .AsciiAttr ;_cacff !=nil {_ecdcc =*_cacff ;}else if _cfadg :=_dbccc .HAnsiAttr ;
_cfadg !=nil {_ecdcc =*_cfadg ;};};if _ecdcc =="\u0064e\u0066\u0061\u0075\u006c\u0074"{if _edfe ==""{if _dbccc .EastAsiaThemeAttr ==_bfc .ST_ThemeMajorEastAsia {_edfe =_dbcc ;}else if _dbccc .EastAsiaThemeAttr ==_bfc .ST_ThemeMinorEastAsia {_edfe =_egba ;
}else if _eecbc :=_dbccc .AsciiThemeAttr ;_eecbc ==_bfc .ST_ThemeMajorAscii ||_eecbc ==_bfc .ST_ThemeMajorHAnsi {_edfe =_egfce ;}else if _fdde :=_dbccc .AsciiThemeAttr ;_fdde ==_bfc .ST_ThemeMinorAscii ||_fdde ==_bfc .ST_ThemeMinorHAnsi {_edfe =_bgdg ;
}else {if _cgea :=_dbccc .HAnsiThemeAttr ;_cgea ==_bfc .ST_ThemeMajorAscii ||_cgea ==_bfc .ST_ThemeMajorHAnsi {_edfe =_egfce ;}else if _adeb :=_dbccc .HAnsiThemeAttr ;_adeb ==_bfc .ST_ThemeMinorAscii ||_adeb ==_bfc .ST_ThemeMinorHAnsi {_edfe =_bgdg ;};
};};_ecgc :="";if _ecdcc =="\u0064e\u0066\u0061\u0075\u006c\u0074"{if _bcfa :=_bcgd ._edega .Settings .X ();_bcfa !=nil {_agfb :="";if _ebgef :=_bcfa .ThemeFontLang ;_ebgef !=nil {if _ebgef .ValAttr !=nil {_agfb =*_ebgef .ValAttr ;};if _ebgef .EastAsiaAttr !=nil {_agfb =*_ebgef .EastAsiaAttr ;
};if _ebgef .BidiAttr !=nil {_agfb =*_ebgef .BidiAttr ;};};_ecgc =_gcdea (_gcega (_agfb ));};};_gdba :=_bcgd ._edega .Themes ();for _ ,_ggae :=range _gdba {if _ggae .ThemeElements !=nil {if _faadb :=_ggae .ThemeElements .FontScheme ;_faadb !=nil {if _faadb .MajorFont !=nil &&_edfe ==_dbcc {if _faadb .MajorFont .Ea !=nil {_ecdcc =_faadb .MajorFont .Ea .TypefaceAttr ;
if _ecdcc ==""&&_ecgc !=""{for _ ,_ecbb :=range _faadb .MajorFont .Font {if _ecbb .ScriptAttr ==_ecgc {_ecdcc =_ecbb .TypefaceAttr ;break ;};};};break ;};}else if _faadb .MinorFont !=nil &&_edfe ==_egba {if _faadb .MinorFont .Ea !=nil {_ecdcc =_faadb .MinorFont .Ea .TypefaceAttr ;
if _ecdcc ==""&&_ecgc !=""{for _ ,_agff :=range _faadb .MinorFont .Font {if _agff .ScriptAttr ==_ecgc {_ecdcc =_agff .TypefaceAttr ;break ;};};};break ;};}else if _faadb .MajorFont !=nil &&_edfe ==_egfce {if _faadb .MajorFont .Latin !=nil {_ecdcc =_faadb .MajorFont .Latin .TypefaceAttr ;
break ;};}else if _faadb .MinorFont !=nil &&_edfe ==_bgdg {if _faadb .MinorFont .Latin !=nil {_ecdcc =_faadb .MinorFont .Latin .TypefaceAttr ;break ;};};};};};};};};};};if _ecdcc !="\u0064e\u0066\u0061\u0075\u006c\u0074"&&!_dfef (_ecdcc ){if _gfdbg :=_bcgd ._edega .FontTable ();
_gfdbg !=nil {for _ ,_aeaac :=range _gfdbg .Font {if _aeaac .NameAttr ==_ecdcc &&_aeaac .AltName !=nil &&_dfef (_aeaac .AltName .ValAttr ){_ecdcc =_aeaac .AltName .ValAttr ;break ;};if _aeaac .AltName !=nil &&!_dfef (_aeaac .AltName .ValAttr )&&_aeaac .AltName .ValAttr ==_ecdcc {_ecdcc =_aeaac .NameAttr ;
break ;};};};};if _dgba (_bcfga .Rtl )&&_ef .RtlFontFile !=nil {_baeb .Font =_ef .RtlFontFile ;}else if _bgadf ,_adab :=_ef .StdFontsMap [_ecdcc ];_adab {_baeb .Font =_ef .AssignStdFontByName (_baeb ,_bgadf [_ffefa ]);}else if _cbab :=_ef .GetRegisteredFont (_ecdcc ,_ffefa );
_cbab !=nil {_baeb .Font =_cbab ;}else {_eg .Log .Debug ("\u0046\u006f\u006e\u0074\u0020\u0025\u0073\u0020\u0077\u0069\u0074h\u0020\u0073\u0074\u0079\u006c\u0065\u0020\u0025s\u0020i\u0073\u0020\u006e\u006f\u0074\u0020\u0066\u006f\u0075\u006e\u0064\u002c\u0020\u0072\u0065\u0073\u0065\u0074 \u0074\u006f\u0020\u0064\u0065\u0066\u0061\u0075\u006c\u0074\u002e",_ecdcc ,_ffefa );
_baeb .Font =_ef .AssignStdFontByName (_baeb ,_ef .StdFontsMap ["\u0064e\u0066\u0061\u0075\u006c\u0074"][_ffefa ]);};};_fgfbe :=_bfcd (_bcfga .Sz ,_bcfga .SzCs );if _abff :=_bcfga .VertAlign ;_abff !=nil {_eabc :=_abff .ValAttr ;_addb =_eabc ==_bc .ST_VerticalAlignRunSuperscript ;
_edfa =_eabc ==_bc .ST_VerticalAlignRunSubscript ;};if _fgfbe > _bcgd ._gfaca {_bcgd ._gfaca =_fgfbe ;};if _addb ||_edfa {_fgfbe *=0.64;};if _efcd {if _addb {_baeb .TextRise =1.5;};if _edfa {_baeb .TextRise =-1.5;};};_baeb .FontSize =_fgfbe ;_bccc :=0.0;
if _efegg :=_bcfga .Spacing ;_efegg !=nil {_bccc =_ef .PointsFromTwips (*_efegg .ValAttr .Int64 );if _bccc < 0.0{_bccc =0.0;};};_baeb .CharSpacing =_bccc ;_efbc :=0.0;if _ceag :=_bcfga .Position ;_ceag !=nil {_efbc =float64 (*_ceag .ValAttr .Int64 )/24*_fgfbe ;
};_baeb .TextRise =_efbc ;_gfgf :=_da .ColorBlack ;if _bcfga .Color !=nil {_gdefg :=_bcfga .Color .ValAttr .ST_HexColorRGB ;if _gdefg !=nil {_gfgf =_da .ColorRGBFromHex ("\u0023"+*_gdefg );};};if _efcd {_bfdff ,_fggb ,_ggbg :=_gfgf .ToRGB ();_bfdff ,_fggb ,_ggbg =_ef .Lighten (_bfdff ),_ef .Lighten (_fggb ),_ef .Lighten (_ggbg );
_gfgf =_da .ColorRGBFromArithmetic (_bfdff ,_fggb ,_ggbg );};_baeb .Color =_gfgf ;if _gacg {_dbde =&_gfgf ;};if _bcfga .U !=nil &&_bcfga .U .ValAttr !=_bfc .ST_UnderlineNone &&!_fcddae {_dbde =&_gfgf ;if _cdeed :=_bcfga .U .ColorAttr ;_cdeed !=nil {if _cdafc :=_cdeed .ST_HexColorRGB ;
_cdafc !=nil {_fddg :=_da .ColorRGBFromHex ("\u0023"+*_cdafc );_dbde =&_fddg ;};};};};return _baeb ,_addb ,_edfa ,_dbde ;};type symbol struct{_aaa string ;_cd float64 ;_cae float64 ;_df float64 ;_dfc float64 ;_abg float64 ;_dab *_da .TextStyle ;_fggc *_da .Image ;
_bfb *block ;_gcb string ;_bga bool ;_fdeb bool ;_dgda bool ;_fag *_da .Color ;_aff bool ;_gdg bool ;_cca *_da .Color ;};func (_abbc *convertContext )calculateHdrFtrContentHeight (){_bdce :=_da .New ();_bdce .SetPageSize (_da .PageSize {_abbc ._affc .Width (),_abbc ._affc .Height ()});
_bdce .SetPageMargins (_abbc ._dgeda .Left ,_abbc ._dgeda .Right ,_abbc ._dgeda .Top ,_abbc ._dgeda .Bottom );_dcaa :=*_abbc ;_dcaa ._affc =_bdce ;_dcaa .newPage ();_dcaa .drawPages ();_dcaa .drawHeaderFooter ();_bbbg :=_bdce .Finalize ();if _bbbg !=nil {_eg .Log .Error ("\u0045R\u0052\u004f\u0052\u003a\u0020\u0025v",_bbbg );
};if _dcaa ._fgggc >=_abbc ._dgeda .Top {_abbc ._gefe .Top =_dcaa ._fgggc +_abbc ._fgbb ;_abbc ._dgeda .Top =_dcaa ._fgggc +_abbc ._fgbb ;_abbc ._fgggc =_dcaa ._fgggc ;};if _dcaa ._afcg < _abbc ._dgeda .Bottom {_dbfbd :=(_dcaa ._afcg /_abbc ._dgeda .Bottom )*(_dcaa ._afcg *_dba );
_abbc ._gbfg -=_dbfbd ;}else {_abbc ._gbfg -=_dcaa ._afcg ;_abbc ._gefe .Bottom =_abbc ._gbfg ;};_abbc ._affc .SetPageMargins (_abbc ._dgeda .Left ,_abbc ._dgeda .Right ,_abbc ._dgeda .Top ,_abbc ._dgeda .Bottom );};type page struct{_ebc *_ef .Rectangle ;
_gc []*paragraph ;_fa float64 ;_cfe float64 ;_cfc []*zoneToSkip ;_efe []*image ;_cb []*image ;_dg []*block ;_dgd []*block ;_fe []*note ;_eea bool ;_gb []*headerFooterRef ;_bb []*headerFooterRef ;_fcb []*paragraph ;_gf []*paragraph ;};func _eag (_adgc *_da .Creator ,_gg *image ){_gg ._dag .SetPos (_gg ._bac ,_gg ._eca );
_aegf :=_adgc .Draw (_gg ._dag );if _aegf !=nil {_eg .Log .Debug ("\u0045\u0072\u0072or\u0020\u0064\u0072\u0061\u0077\u0069\u006e\u0067\u0020\u0069\u006d\u0061\u0067\u0065\u003a\u0020\u0025\u0073",_aegf );};};type romanMatch struct{_gcdb int ;_ffaf string ;
};func (_ebcd *convertContext )setCellBackgroundColor (_aafd *_da .TableCell ,_dcddd *_bfc .CT_Shd ){if _dcddd ==nil {return ;};if _dcddd .ValAttr ==_bfc .ST_ShdSolid {if _cebc :=_dcddd .ColorAttr ;_cebc !=nil {if _debe :=_cebc .ST_HexColorRGB ;_debe !=nil {_gdca :=_da .ColorRGBFromHex ("\u0023"+*_debe );
_aafd .SetBackgroundColor (_gdca );return ;};};}else {if _acdg :=_dcddd .FillAttr ;_acdg !=nil {if _deec :=_acdg .ST_HexColorRGB ;_deec !=nil {_eedd :=_da .ColorRGBFromHex ("\u0023"+*_deec );_aafd .SetBackgroundColor (_eedd );_aagg :=0;_cfecf :=_dcddd .ValAttr .String ();
if _ga .HasPrefix (_cfecf ,"\u0070\u0063\u0074"){_agac ,_aegd :=_b .Atoi (_cfecf [3:]);if _aegd !=nil {return ;};_aagg =_agac ;}else if _dcddd .ValAttr > _bfc .ST_ShdSolid &&_dcddd .ValAttr <=_bfc .ST_ShdThinDiagCross {_aagg =25;};_gaag :=float64 (_aagg )/100.0;
if _ggee :=_dcddd .ColorAttr ;_ggee !=nil {if _gaee :=_ggee .ST_HexColorRGB ;_gaee !=nil {_cgca :=_da .ColorRGBFromHex ("\u0023"+*_gaee );_dcge ,_eedc ,_cegfe :=_eedd .ToRGB ();_ebab ,_fegg ,_dged :=_cgca .ToRGB ();_eedd =_da .ColorRGBFromArithmetic ((1-_gaag )*_dcge +_gaag *_ebab ,(1-_gaag )*_eedc +_gaag *_fegg ,(1-_gaag )*_cegfe +_gaag *_dged );
_aafd .SetBackgroundColor (_eedd );};};};};};};func _ccgg (_caaa string )string {_egda :=_gacba .FindStringSubmatch (_caaa );if len (_egda )< 2{return "";};return _egda [1];};type word struct{_edf []*symbol ;_gcd float64 ;_afg float64 ;_ffb bool ;};func _dgba (_becg *_bfc .CT_OnOff )bool {if _becg !=nil {if _cfce :=_becg .ValAttr ;
_cfce !=nil {if _bfbbb :=_cfce .Bool ;_bfbbb !=nil {return *_bfbbb ;};return _cfce .ST_OnOff1 ==_bc .ST_OnOff1On ;};return true ;};return false ;};var _cegdg =[]romanMatch {romanMatch {1000,"\u006d"},romanMatch {900,"\u0063\u006d"},romanMatch {500,"\u0064"},romanMatch {400,"\u0063\u0064"},romanMatch {100,"\u0063"},romanMatch {90,"\u0078\u0063"},romanMatch {50,"\u006c"},romanMatch {40,"\u0078\u006c"},romanMatch {10,"\u0078"},romanMatch {9,"\u0069\u0078"},romanMatch {5,"\u0076"},romanMatch {4,"\u0069\u0076"},romanMatch {1,"\u0069"}};
func (_dec *convertContext )addAbsoluteCBCs (_aegg []*_bfc .EG_ContentBlockContent ,_gce []*_bfc .EG_ContentBlockContent ){_fdeg :="";_bcab :=false ;for _ ,_egbd :=range _gce {if len (_egbd .ContentBlockContentChoice .P )< 1{_bcab =true ;break ;};for _ ,_fab :=range _egbd .ContentBlockContentChoice .P {if len (_fab .EG_PContent )==0{break ;
};if _fab .PPr !=nil &&_fab .PPr .PStyle !=nil {_fdeg =_fab .PPr .PStyle .ValAttr ;break ;};};};for _ ,_bgd :=range _aegg {for _ ,_bbc :=range _bgd .ContentBlockContentChoice .P {_dec .newParagraph ();if len (_dec ._aded )> 1{_gaf :=0.0;for _ebg ,_ggd :=range _dec ._aded {if _ebg ==0{_gaf =_ggd ._bdd +_ggd ._aee ;
}else if _ebg ==len (_dec ._aded )-1{_ggd ._bdd =_gaf ;}else {_ggd ._bdd =_gaf ;_gaf =_ggd ._bdd +_ggd ._aee ;};};};_dec ._aded =[]*paragraph {};if _bbc .PPr !=nil &&_bbc .PPr .PStyle ==nil {_fbe :=_dec ._edega .Styles .ParagraphStyles ();for _ ,_fdeec :=range _fbe {if _ecff :=_fdeec .X ().DefaultAttr ;
_ecff !=nil {if _deca :=_ecff .Bool ;_deca !=nil &&*_deca {_bbc .PPr =_cfgfd (_bbc .PPr ,_fdeec .X ().PPr ,_fdeec .X ().RPr );};if _ecag :=_ecff .ST_OnOff1 ;_ecag ==_bc .ST_OnOff1On {_bbc .PPr =_cfgfd (_bbc .PPr ,_fdeec .X ().PPr ,_fdeec .X ().RPr );};
break ;};};};_dfg ,_ffee :=_dec .combinePPrWithStyles (_bbc .PPr );if _ffee !=nil {_dec ._cead =_ffee ;};if _bbc .PPr !=nil &&_bbc .PPr .PStyle !=nil {if _bbc .PPr .PStyle .ValAttr !=_fdeg {_bbc .PPr .ContextualSpacing =nil ;};};if _dfg !=nil &&_dfg .SectPr !=nil {_ccf ,_faga :=_dec .getSectPrHeaderAndFooterRef (_dfg .SectPr ,len (_dec ._cbaaa )-1);
_dec ._fadc ._gb =append (_dec ._fadc ._gb ,_ccf ...);_dec ._fadc ._bb =append (_dec ._fadc ._bb ,_faga ...);_dec ._ebeg =append (_dec ._ebeg ,_ccf ...);_dec ._cbafb =append (_dec ._cbafb ,_faga ...);if !_bcab &&(_dfg .SectPr .Type ==nil ||(_dfg .SectPr .Type !=nil &&_dfg .SectPr .Type .ValAttr !=_bfc .ST_SectionMarkContinuous ))&&_ffee ==nil &&!_dgba (_dfg .WidowControl ){_dec .newPage ();
continue ;};if len (_bbc .EG_PContent )< 1{continue ;};};_dec .assignPropsToAbsoluteParagraph (_dfg ,_dec ._cfeb );_dec .determineParagraphBounds ();_dec .newLine ();_dec .newWord ();_dacd :=_bbc .EG_PContent ;if len (_dacd )==0{_dec .addEmptyLine ();}else {if _dec .addAbsoluteEGPC (_dacd ,_dfg ){_dec .addCurrentWordToParagraph ();
_dec .addCurrentParagraphToCurrentPage ();_dec .newPage ();continue ;};if _dec .currentParagraphOverflowsCurrentPage (){_dec .moveCurrentParagraphToNewPage ();};_dec .addAnchorBlocks (_dacd );_dec .addAnchorExtra (_dacd );_dec .addCurrentWordToParagraph ();
};_dec .addCurrentParagraphToCurrentPage ();};for _ ,_eaea :=range _bgd .ContentBlockContentChoice .Tbl {if _dec ._cfeb ==nil {_dec .newParagraph ();_dec .determineParagraphBounds ();_dec .newLine ();_dec .newWord ();};_dec .addAbsoluteTable (_eaea );_dec ._aded =append (_dec ._aded ,_dec ._cfeb );
};};_dec ._cfeb =nil ;};func (_bbdcd *convertContext )makePdfImageFromGraphics (_fgdc *_ffd .Pic )(*_da .Image ,error ){if _gdbc :=_fgdc .BlipFill ;_gdbc !=nil {if _cbbg :=_gdbc .Blip ;_cbbg !=nil {if _ggaf :=_cbbg .EmbedAttr ;_ggaf !=nil {_eeca ,_ddac :=_bbdcd ._edega .GetHeaderFooterImageObjByRelId (*_ggaf ,_bbdcd ._dadc ,_bbdcd ._fcgc );
if _ddac !=nil {return nil ,_ddac ;};_gcba ,_ddac :=_dac .Open (_eeca .Path );if _ddac !=nil {return nil ,_ddac ;};_afebd ,_ddac :=_d .ReadAll (_gcba );if _ddac !=nil {return nil ,_ddac ;};if _ga .ToLower (_eeca .Format )=="\u0065\u006d\u0066"{_cbbd ,_acaf :=_fc .ReadFile (_afebd );
if _acaf !=nil {return nil ,_acaf ;};_bccgb :=new (_a .Buffer );_bfefe :=_cbbd .Draw ();if _aabcg :=_dd .Encode (_bccgb ,_bfefe );_aabcg !=nil {return nil ,_aabcg ;};_afebd =_bccgb .Bytes ();};_cabdc ,_ddac :=_bbdcd ._affc .NewImageFromData (_afebd );if _ddac !=nil {return nil ,_ddac ;
};if _ef .DefaultImageEncoder !=nil {_cabdc .SetEncoder (_ef .DefaultImageEncoder );}else {_cabdc .SetEncoder (_fb .NewFlateEncoder ());if _ga .ToLower (_eeca .Format )=="\u006a\u0070\u0067"||_ga .ToLower (_eeca .Format )=="\u006a\u0070\u0065\u0067"{_cabdc .SetEncoder (_fb .NewDCTEncoder ());
};};return _cabdc ,nil ;};};};return nil ,nil ;};func (_acc *convertContext )addTableWithDataRange (_gacb map[int ][]tableCellProperties ,_gcfd ,_cdaf ,_dbdf int ,_ddcg ,_cegab float64 ,_adef []float64 ,_gfbe []float64 ,_cedf bool ){_gfcg :=_acc ._affc .NewTable (_dbdf );
_cdfg :=_gfcg .SetColumnWidths (_adef ...);if _cdfg !=nil {_eg .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a \u0055\u006e\u0061\u0062\u006c\u0065\u0020\u0074\u006f\u0020\u0073\u0065\u0074\u0020\u0063\u006f\u006c\u0075\u006d\u006e \u0077\u0069\u0064\u0074\u0068\u0073\u0020\u0066\u006f\u0072\u0020\u0074\u0061\u0062l\u0065 \u0028\u0025\u0073\u0029",_cdfg .Error ());
};if _cedf {_dgde :=_acc .addRowToTable (_gacb ,0,_gfcg ,_gfbe );if _dgde !=nil {_eg .Log .Debug ("\u0045\u0052\u0052\u004f\u0052:\u0020\u0055\u006e\u0061\u0062\u006c\u0065\u0020\u0074\u006f\u0020\u0061\u0064d\u0020\u0068\u0065\u0061\u0064\u0065\u0072\u0020\u0074\u006f\u0020\u0074\u0061\u0062\u006c\u0065\u0020\u0028\u0025\u0073\u0029",_dgde .Error ());
};};for _bcgc :=_gcfd ;_bcgc <=_cdaf ;_bcgc ++{_gabe :=_acc .addRowToTable (_gacb ,_bcgc ,_gfcg ,_gfbe );if _gabe !=nil {_eg .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u0055\u006e\u0061\u0062\u006c\u0065\u0020\u0074\u006f\u0020\u0061\u0064\u0064\u0020\u0072\u006f\u0077 \u0074\u006f\u0020\u0074\u0061b\u006c\u0065 \u0028\u0025\u0073\u0029",_gabe .Error ());
};};_affg :=_ef .MakeTempCreatorMaxHeight (_ddcg );_cdfg =_affg .Draw (_gfcg );if _cdfg !=nil {_eg .Log .Debug ("\u0045\u0052RO\u0052\u003a\u0020U\u006e\u0061\u0062\u006ce t\u006f s\u0065\u0074\u0020\u0064\u0072\u0061\u0077 t\u0061\u0062\u006c\u0065\u0020\u0028\u0025s\u0029",_cdfg .Error ());
};_acc .addParagraphWithTable (*_gfcg ,_ddcg ,_cegab );};func (_aaca *convertContext )addRect (_dbec *_ff .Rect ){_gagg :=_eb .NewShapeStyle ("");_cbafa :=false ;if _dbec .StyleAttr !=nil {_gagg =_eb .NewShapeStyle (*_dbec .StyleAttr );_cbafa =_ga .Contains (*_dbec .StyleAttr ,"\u0070\u0074");
};_afadb :=_da .ColorWhite ;if _dbec .FillcolorAttr !=nil {_afadb =_da .ColorRGBFromHex (*_dbec .FillcolorAttr );};_eede :=_gagg .Width ();_cfdg :=_gagg .Height ();_geae :=_gagg .Left ()-_gagg .Right ();_dbfb :=_gagg .Top ()-_gagg .Bottom ();if !_cbafa {_eede =_ef .PointsFromTwips (int64 (_eede ));
_cfdg =_ef .PointsFromTwips (int64 (_cfdg ));_geae =_ef .PointsFromTwips (int64 (_geae ));_dbfb =_ef .PointsFromTwips (int64 (_dbfb ));};_bbdf :=&borderLine {_gcdd :_ef .BorderPositionBottom ,_daa :_eede ,_gbg :_cfdg ,_aef :_afadb };_aaca ._cfeb ._fd =append (_aaca ._cfeb ._fd ,_bbdf );
if _gagg .Position ()==_eb .ShapeStylePositionAbsolute {_aaca ._gfcba ._ffg =_aaca ._cfeb ._ddd +_geae ;_aaca ._gfcba ._bab =_dbfb ;};};func (_fecd *convertContext )addInlineSymbol (_ffce *symbol ){if len (_fecd ._efdb ._edf )> 0{_dbc :=_fecd ._efdb ._edf [len (_fecd ._efdb ._edf )-1]._aaa ;
if _dbc =="\u0020"{_fecd .addCurrentWordToParagraph ();_fecd .newWord ();};};_fecd ._efdb ._edf =append (_fecd ._efdb ._edf ,_ffce );_ffce ._cd =_fecd ._efdb ._afg ;_fecd ._efdb ._afg +=_ffce ._df ;_fecd ._efdb ._ffb =false ;_fecd .adjustHeights (_ffce ._dfc );
};func (_bfdde *convertContext )getStyleProps (_faadd string ,_adcb _fca .Style )(*_bfc .CT_PPrGeneral ,*_bfc .CT_RPr ){var _fadf *_bfc .CT_PPrGeneral ;var _cbecb *_bfc .CT_RPr ;_bfbcg :=_bfdde ._edega .GetStyleByID (_faadd );_ddbf :=int64 (0);_dgfe :=true ;
if _fbaa :=_bfbcg .X ();_fbaa !=nil {_fadf =_fbaa .PPr ;_cbecb =_fbaa .RPr ;if _fbaa .UiPriority !=nil {_ddbf =_fbaa .UiPriority .ValAttr ;};if _fefg :=_fbaa .BasedOn ;_fefg !=nil {_bafa ,_ceae :=_bfdde .getStyleProps (_fefg .ValAttr ,_bfbcg );if _cdea :=_adcb .X ();
_cdea !=nil {if _cdea .UiPriority !=nil &&_ddbf > 0{if _fbaa .UiPriority .ValAttr > _ddbf {_dgfe =false ;};};if _cdea .QFormat !=nil &&_fbaa .QFormat !=nil &&_dgba (_cdea .QFormat )&&_dgba (_fbaa .QFormat ){_dgfe =false ;};};if _dgfe {_fadf =_dbbf (_fadf ,_bafa );
_cbecb =_bcag (_cbecb ,_ceae );};};};return _fadf ,_cbecb ;};func _cgdcg (_bbfcd ,_addd *_bfc .CT_RPr )*_bfc .CT_RPr {if _bbfcd ==nil {return _addd ;};if _addd ==nil {if _bbfcd .B !=nil {_bbfcd .B =nil ;};if _bbfcd .BCs !=nil {_bbfcd .BCs =nil ;};if _bbfcd .I !=nil {_bbfcd .I =nil ;
};if _bbfcd .ICs !=nil {_bbfcd .ICs =nil ;};return _bbfcd ;};if _bbfcd .RStyle ==nil {_bbfcd .RStyle =_addd .RStyle ;};if _bbfcd .RFonts ==nil {_bbfcd .RFonts =_addd .RFonts ;};if _bbfcd .B ==nil {_bbfcd .B =_addd .B ;};if _bbfcd .BCs ==nil {_bbfcd .BCs =_addd .BCs ;
};if _bbfcd .I ==nil {_bbfcd .I =_addd .I ;};if _bbfcd .ICs ==nil {_bbfcd .ICs =_addd .ICs ;};if _bbfcd .Caps ==nil {_bbfcd .Caps =_addd .Caps ;};if _bbfcd .SmallCaps ==nil {_bbfcd .SmallCaps =_addd .SmallCaps ;};if _bbfcd .Strike ==nil {_bbfcd .Strike =_addd .Strike ;
};if _bbfcd .Dstrike ==nil {_bbfcd .Dstrike =_addd .Dstrike ;};if _bbfcd .Outline ==nil {_bbfcd .Outline =_addd .Outline ;};if _bbfcd .Shadow ==nil {_bbfcd .Shadow =_addd .Shadow ;};if _bbfcd .Emboss ==nil {_bbfcd .Emboss =_addd .Emboss ;};if _bbfcd .Imprint ==nil {_bbfcd .Imprint =_addd .Imprint ;
};if _bbfcd .NoProof ==nil {_bbfcd .NoProof =_addd .NoProof ;};if _bbfcd .SnapToGrid ==nil {_bbfcd .SnapToGrid =_addd .SnapToGrid ;};if _bbfcd .Vanish ==nil {_bbfcd .Vanish =_addd .Vanish ;};if _bbfcd .WebHidden ==nil {_bbfcd .WebHidden =_addd .WebHidden ;
};if _bbfcd .Color ==nil {_bbfcd .Color =_addd .Color ;};if _bbfcd .Spacing ==nil {_bbfcd .Spacing =_addd .Spacing ;};if _bbfcd .W ==nil {_bbfcd .W =_addd .W ;};if _bbfcd .Kern ==nil {_bbfcd .Kern =_addd .Kern ;};if _bbfcd .Position ==nil {_bbfcd .Position =_addd .Position ;
};if _bbfcd .Sz ==nil {_bbfcd .Sz =_addd .Sz ;};if _bbfcd .SzCs ==nil {_bbfcd .SzCs =_addd .SzCs ;};if _bbfcd .Highlight ==nil {_bbfcd .Highlight =_addd .Highlight ;};if _bbfcd .U ==nil {_bbfcd .U =_addd .U ;};if _bbfcd .Effect ==nil {_bbfcd .Effect =_addd .Effect ;
};if _bbfcd .Bdr ==nil {_bbfcd .Bdr =_addd .Bdr ;};if _bbfcd .Shd ==nil {_bbfcd .Shd =_addd .Shd ;};if _bbfcd .FitText ==nil {_bbfcd .FitText =_addd .FitText ;};if _bbfcd .VertAlign ==nil {_bbfcd .VertAlign =_addd .VertAlign ;};if _bbfcd .Rtl ==nil {_bbfcd .Rtl =_addd .Rtl ;
};if _bbfcd .Cs ==nil {_bbfcd .Cs =_addd .Cs ;};if _bbfcd .Em ==nil {_bbfcd .Em =_addd .Em ;};if _bbfcd .Lang ==nil {_bbfcd .Lang =_addd .Lang ;};if _bbfcd .EastAsianLayout ==nil {_bbfcd .EastAsianLayout =_addd .EastAsianLayout ;};if _bbfcd .SpecVanish ==nil {_bbfcd .SpecVanish =_addd .SpecVanish ;
};if _bbfcd .OMath ==nil {_bbfcd .OMath =_addd .OMath ;};if _bbfcd .RPrChange ==nil {_bbfcd .RPrChange =_addd .RPrChange ;};return _bbfcd ;};func _ebcc (_fgdb ,_eaf string ,_efa ,_cada ,_gcc bool )[]*symbol {_gcea :=[]*symbol {};for _ ,_cebd :=range _fgdb {_cag :=&symbol {_aaa :string (_cebd ),_bga :_efa ,_fdeb :_cada ,_gcb :_eaf ,_aff :_gcc };
_gcea =append (_gcea ,_cag );};return _gcea ;};func (_dfebd *convertContext )shouldApplyContextualSpacing (Ppr *_bfc .CT_PPr )bool {_aedg :=_dfebd ._aadb .PStyle ;return Ppr !=nil &&Ppr .ContextualSpacing !=nil &&_dgba (Ppr .ContextualSpacing )&&_dfebd ._aadb .ContextualSpacing !=nil &&_dgba (_dfebd ._aadb .ContextualSpacing )&&Ppr .PStyle !=nil &&_aedg !=nil &&Ppr .PStyle .ValAttr ==_aedg .ValAttr ;
};func (_bea *span )moveRight (_gdd float64 ){for _ ,_cac :=range _bea ._egf {_cac ._gcd +=_gdd ;};};func _gbed (_caaad *_bfc .CT_TblPr ,_afgg *_bfc .CT_TblPrEx ,_acdcb *_bfc .CT_TcPr ,_bbdcg ,_abec ,_cebb ,_eabf int )*_bfc .CT_TcPr {if _acdcb ==nil {_acdcb =_bfc .NewCT_TcPr ();
};if _afgg ==nil {_afgg =_bfc .NewCT_TblPrEx ();};if _caaad ==nil {_caaad =_bfc .NewCT_TblPr ();};if _acdcb .TcBorders ==nil {_acdcb .TcBorders =_bfc .NewCT_TcBorders ();};if _afgg .TblBorders ==nil {_afgg .TblBorders =_bfc .NewCT_TblBorders ();};if _caaad .TblBorders ==nil {_caaad .TblBorders =_bfc .NewCT_TblBorders ();
};if _acdcb .TcBorders .Top ==nil {if _afgg .TblBorders .Top ==nil {_acdcb .TcBorders .Top =_ddddb (_caaad .TblBorders .Top ,_caaad .TblBorders .InsideH ,_bbdcg ==0);}else {_acdcb .TcBorders .Top =_ddddb (_afgg .TblBorders .Top ,_afgg .TblBorders .InsideH ,_bbdcg ==0);
};};if _acdcb .TcBorders .Bottom ==nil {if _afgg .TblBorders .Bottom ==nil {_acdcb .TcBorders .Bottom =_ddddb (_caaad .TblBorders .Bottom ,_caaad .TblBorders .InsideH ,_bbdcg ==_cebb -1);}else {_acdcb .TcBorders .Bottom =_ddddb (_afgg .TblBorders .Bottom ,_afgg .TblBorders .InsideH ,_bbdcg ==_cebb -1);
};};if _acdcb .TcBorders .Left ==nil {if _afgg .TblBorders .Left ==nil {_acdcb .TcBorders .Left =_ddddb (_caaad .TblBorders .Left ,_caaad .TblBorders .InsideV ,_abec ==0);}else {_acdcb .TcBorders .Left =_ddddb (_afgg .TblBorders .Left ,_afgg .TblBorders .InsideV ,_abec ==0);
};};if _acdcb .TcBorders .Right ==nil {if _afgg .TblBorders .Right ==nil {_acdcb .TcBorders .Right =_ddddb (_caaad .TblBorders .Right ,_caaad .TblBorders .InsideV ,_abec ==_eabf -1);}else {_acdcb .TcBorders .Right =_ddddb (_afgg .TblBorders .Right ,_afgg .TblBorders .InsideV ,_abec ==_eabf -1);
};};if _acdcb .Shd ==nil {if _ddded :=_caaad .Shd ;_ddded !=nil {_acdcb .Shd =_ddded ;};}else {if _caaad .Shd !=nil &&_acdcb .Shd .FillAttr ==nil {_acdcb .Shd .FillAttr =_caaad .Shd .FillAttr ;};};if _afgg .TblCellMar !=nil {_caaad .TblCellMar =_afgg .TblCellMar ;
};if _addc :=_caaad .TblCellMar ;_addc !=nil {if _acdcb .TcMar ==nil {_acdcb .TcMar =_bfc .NewCT_TcMar ();};if _acdcb .TcMar .Left ==nil {_acdcb .TcMar .Left =_addc .Left ;};if _acdcb .TcMar .Top ==nil {_acdcb .TcMar .Top =_addc .Top ;};if _acdcb .TcMar .Right ==nil {_acdcb .TcMar .Right =_addc .Right ;
};if _acdcb .TcMar .Bottom ==nil {_acdcb .TcMar .Bottom =_addc .Bottom ;};};return _acdcb ;};var _cabfe float64 ;

// RegisterFont makes a PdfFont accessible for using in converting to PDF.
func RegisterFont (name string ,style FontStyle ,font *_ae .PdfFont ){_ef .RegisterFont (name ,style ,font );};func (_cdcca *convertContext )setPagesHeaderFooterRefs (){var _cbgge int ;for _ ,_cgee :=range _cdcca ._edega .X ().Body .EG_BlockLevelElts {for _ ,_dadb :=range _cgee .BlockLevelEltsChoice .EG_ContentBlockContent {for _ ,_ecfbe :=range _dadb .ContentBlockContentChoice .P {if _ecfbe .PPr ==nil ||(_ecfbe .PPr !=nil &&_ecfbe .PPr .SectPr ==nil ){continue ;
};_cbgge ++;};};};for _agcb ,_bbdgg :=range _cdcca ._cbaaa {_gbbb :=_agcb > 0&&_agcb +1< len (_cdcca ._cbaaa )&&_cbgge > 0;if len (_bbdgg ._gb )> 0||len (_bbdgg ._bb )> 0{_cbgge --;};if len (_bbdgg ._gb )< 1{if len (_cdcca ._ebeg )> 0&&!_gbbb {_ebeb :=false ;
_fbdb :=false ;_aedfb :=_agcb ==0;for !_ebeb {for _ ,_cagg :=range _cdcca ._ebeg {if _cagg ._cadc ==_bfc .ST_HdrFtrFirst &&_agcb <=_cagg ._cddga &&_aedfb {_bbdgg ._gb =append (_bbdgg ._gb ,_cagg );break ;};if _cagg ._cadc ==_bfc .ST_HdrFtrDefault &&_agcb >=_cagg ._beff &&_agcb <=_cagg ._cddga &&!_aedfb &&!_fbdb {_bbdgg ._gb =append (_bbdgg ._gb ,_cagg );
break ;};if _cagg ._cadc ==_bfc .ST_HdrFtrDefault &&_cagg ._cddga < 0&&_fbdb {_bbdgg ._gb =append (_bbdgg ._gb ,_cagg );break ;};};if _fbdb ||len (_bbdgg ._gb )> 0{_ebeb =true ;};_fbdb =len (_bbdgg ._gb )< 1&&!_fbdb &&!_aedfb ;_aedfb =false ;};}else if _agcb > 0{if _gdea :=_cdcca ._cbaaa [_agcb -1];
len (_gdea ._gb )> 0{_bbdgg ._gb =_gdea ._gb ;};};_cdcca ._cbaaa [_agcb ]._gb =_bbdgg ._gb ;};if len (_bbdgg ._bb )< 1{if len (_cdcca ._cbafb )> 0&&!_gbbb {_bdfca :=false ;_fcggf :=false ;_dfae :=_agcb ==0;for !_bdfca {for _ ,_bgfbf :=range _cdcca ._cbafb {if _bgfbf ._cadc ==_bfc .ST_HdrFtrFirst &&_agcb <=_bgfbf ._cddga &&_dfae {_bbdgg ._bb =append (_bbdgg ._bb ,_bgfbf );
break ;};if _bgfbf ._cadc ==_bfc .ST_HdrFtrDefault &&_agcb >=_bgfbf ._beff &&_agcb <=_bgfbf ._cddga &&!_dfae &&!_fcggf {_bbdgg ._bb =append (_bbdgg ._bb ,_bgfbf );break ;};if _bgfbf ._cadc ==_bfc .ST_HdrFtrDefault &&_fcggf {_bbdgg ._bb =append (_bbdgg ._bb ,_bgfbf );
break ;};};if _fcggf ||len (_bbdgg ._bb )> 0{_bdfca =true ;};_fcggf =len (_bbdgg ._bb )< 1&&!_fcggf &&!_dfae ;_dfae =false ;};}else if _agcb > 0{if _gebc :=_cdcca ._cbaaa [_agcb -1];len (_gebc ._bb )> 0{_bbdgg ._bb =_gebc ._bb ;};};_cdcca ._cbaaa [_agcb ]._bb =_bbdgg ._bb ;
};};};

// ConvertToPdf converts document to PDF file. This package is beta, breaking changes can take place.
func ConvertToPdf (d *_fca .Document )*_da .Creator {return ConvertToPdfWithOptions (d ,nil )};

// ConvertToPdfWithOptions convert the document to PDF with given options.
func ConvertToPdfWithOptions (d *_fca .Document ,opts *Options )*_da .Creator {var _cbafc map[string ]string ;_ef .DefaultFontSize =12;if opts !=nil {if opts .ProcessFields {_cbafc =_eabe (d );};if len (opts .FontFiles )> 0{_dbfa :=_ef .RegisterFontsFromFiles (opts .FontFiles );
if _dbfa !=nil {_eg .Log .Debug ("\u0046\u0061\u0069\u006c t\u006f\u0020\u006c\u006f\u0061\u0064\u0020\u0066\u006f\u006e\u0074\u0073\u003a\u0020%\u0076",opts .FontDirectory );};};if opts .FontDirectory !=""{_egcc :=_ef .RegisterFontsFromDirectory (opts .FontDirectory );
if _egcc !=nil {_eg .Log .Debug ("\u0046\u0061\u0069l\u0020\u0074\u006f\u0020l\u006f\u0061\u0064\u0020\u0066\u006f\u006et\u0020\u0064\u0069\u0072\u0065\u0063\u0074\u006f\u0072\u0079\u003a\u0020\u0025\u0076",_egcc .Error ());};};if opts .DefaultFontSize > 0{_ef .DefaultFontSize =float64 (opts .DefaultFontSize );
};if len (opts .RtlFontFile )> 0{_ef .RtlFontFile ,_ =_ef .LoadFontFromFile (opts .RtlFontFile );};if opts .DefaultImageEncoder !=nil {_ef .DefaultImageEncoder =opts .DefaultImageEncoder ;};};_bbbdc :=_ef .RegisterEmbeddedFonts (d );if _bbbdc !=nil {_eg .Log .Debug ("\u0046\u0061\u0069l\u0020\u0074\u006f\u0020l\u006f\u0061\u0064\u0020\u0065\u006d\u0062e\u0064\u0064\u0065\u0064\u0020\u0066\u006f\u006e\u0074\u0073\u003a\u0020\u0025\u0076",_bbbdc .Error ());
};var (_bffb *_bfc .CT_PPrGeneral ;_cggc *_bfc .CT_RPr ;);if _caa :=d .Styles .X ().DocDefaults ;_caa !=nil {if _afaf :=_caa .PPrDefault ;_afaf !=nil {_bffb =_afaf .PPr ;};if _fgae :=_caa .RPrDefault ;_fgae !=nil {_cggc =_fgae .RPr ;};};_fceg :=_ef .GetDefaultPageSize ();
if opts !=nil &&opts .DefaultPageSize !=_ef .DefaultPageSize {_fceg =_ef .GetPageDimensions (opts .DefaultPageSize );};_ddgc :=_fceg [0];_ecbf :=_fceg [1];_ebbdb :=_ag .Inch *1.0;_ebdbe :=_ag .Inch *0.5;_ffga ,_acfg ,_bedc ,_gecf :=_ebbdb ,_ebbdb ,_ebbdb ,_ebbdb ;
_aeaba ,_aega :=_ebdbe ,_ebdbe ;var (_bacd []*headerFooterRef ;_cegbb []*headerFooterRef ;);if _dfcg :=d .BodySection ().X ();_dfcg !=nil {if _cdgg :=_dfcg .PgMar ;_cdgg !=nil {if _cdgg .LeftAttr .ST_UnsignedDecimalNumber !=nil {_ffga =_ef .PointsFromTwips (int64 (*_cdgg .LeftAttr .ST_UnsignedDecimalNumber ));
};if _cdgg .LeftAttr .ST_UnsignedDecimalNumber !=nil {_acfg =_ef .PointsFromTwips (int64 (*_cdgg .RightAttr .ST_UnsignedDecimalNumber ));};if _cdgg .TopAttr .Int64 !=nil {_bedc =_ef .PointsFromTwips (*_cdgg .TopAttr .Int64 );};if _cdgg .BottomAttr .Int64 !=nil {_gecf =_ef .PointsFromTwips (*_cdgg .BottomAttr .Int64 );
};if _cdgg .HeaderAttr .ST_UnsignedDecimalNumber !=nil {_aeaba =_ef .PointsFromTwips (int64 (*_cdgg .HeaderAttr .ST_UnsignedDecimalNumber ));};if _cdgg .FooterAttr .ST_UnsignedDecimalNumber !=nil {_aega =_ef .PointsFromTwips (int64 (*_cdgg .FooterAttr .ST_UnsignedDecimalNumber ));
};};if _cbb :=_dfcg .PgSz ;_cbb !=nil {if _cbb .WAttr !=nil {_ddgc =_ef .PointsFromTwips (int64 (*_cbb .WAttr .ST_UnsignedDecimalNumber ));};if _cbb .HAttr !=nil {_ecbf =_ef .PointsFromTwips (int64 (*_cbb .HAttr .ST_UnsignedDecimalNumber ));};};for _ ,_dacc :=range _dfcg .EG_HdrFtrReferences {if _fcfc :=_dacc .HdrFtrReferencesChoice .HeaderReference ;
_fcfc !=nil {_bbfec :=&headerFooterRef {_gbgd :true ,_ddag :_fcfc .IdAttr ,_cadc :_fcfc .TypeAttr ,_cddga :-1};_bacd =append (_bacd ,_bbfec );};if _cbed :=_dacc .HdrFtrReferencesChoice .FooterReference ;_cbed !=nil {_ggag :=&headerFooterRef {_bbed :true ,_ddag :_cbed .IdAttr ,_cadc :_cbed .TypeAttr ,_cddga :-1};
_cegbb =append (_cegbb ,_ggag );};};if len (_dfcg .EG_HdrFtrReferences )< 1{_bfee :=&headerFooterRef {_bbed :false ,_gbgd :false ,_cddga :-1};_bacd =append (_bacd ,_bfee );_cegbb =append (_cegbb ,_bfee );};};if d .Settings .X ().DefaultTabStop ==nil {_cabfe =_ggef (12.7);
}else {_cabfe =_ef .PointsFromTwips (int64 (*d .Settings .X ().DefaultTabStop .ValAttr .ST_UnsignedDecimalNumber ));};_ddgf :=_da .New ();_ddgf .SetPageSize (_da .PageSize {_ddgc ,_ecbf });_ddgf .SetPageMargins (_ffga ,_acfg ,_bedc ,_gecf );_cggg :=&convertContext {_affc :_ddgf ,_edega :d ,_aefeg :_bffb ,_ggbe :_cggc ,_gefe :&_ef .Rectangle {Top :_bedc ,Bottom :_ecbf -_gecf ,Left :_ffga ,Right :_ddgc -_acfg },_dgeda :&_ef .Rectangle {Top :_bedc ,Bottom :_gecf ,Left :_ffga ,Right :_acfg },_aegfc :[]note {},_fdfg :map[int64 ]map[int64 ]int64 {},_gegc :_cbafc ,_dagg :opts ,_ebeg :_bacd ,_cbafb :_cegbb ,_fgbb :_aeaba ,_fgggc :_bedc ,_gbfg :_ecbf -_aega ,_afcg :_gecf ,_gfbg :_ffga ,_agcd :map[string ]map[int64 ]*_bfc .CT_Ind {},_cdga :[]float64 {_ddgc ,_ecbf },_aded :[]*paragraph {}};
_cggg .calculateHdrFtrContentHeight ();_abbgc :=d .X ().Body .EG_BlockLevelElts ;_edfb :=len (_abbgc );_cggg ._aadb =nil ;for _agge ,_cbfa :=range _abbgc {var _gfdd []*_bfc .EG_ContentBlockContent ;if _agge < _edfb -1{_fefd :=_abbgc [_agge +1];_gfdd =_fefd .BlockLevelEltsChoice .EG_ContentBlockContent ;
};_cggg .addAbsoluteCBCs (_cbfa .BlockLevelEltsChoice .EG_ContentBlockContent ,_gfdd );};_cggg ._aadb =nil ;_cggg .addEndnotes ();_cggg .alignSymbolsVertically ();_cggg .drawPages ();_cggg .drawHeaderFooter ();return _ddgf ;};func _bfcd (_gbde ,_dfga *_bfc .CT_HpsMeasure )float64 {var _cfee float64 ;
_gebd :=_ef .DefaultFontSize ;if _gbde !=nil {_cfee =float64 (*_gbde .ValAttr .ST_UnsignedDecimalNumber );}else if _dfga !=nil {_cfee =float64 (*_dfga .ValAttr .ST_UnsignedDecimalNumber );};if _cfee !=0{_gebd =_cfee /2;};return _gebd ;};func _cdfbg (_efbd *_fca .Document ,_agece string )[]*_bfc .CT_TblStylePr {_gggd :=_efbd .GetStyleByID (_agece );
var _adcgf []*_bfc .CT_TblStylePr ;if _fdaae :=_gggd .X ();_fdaae !=nil {if _gcgff :=_fdaae .BasedOn ;_gcgff !=nil {_cdfbg (_efbd ,_gcgff .ValAttr );};if len (_fdaae .TblStylePr )> 0{_adcgf =_fdaae .TblStylePr ;};};return _adcgf ;};func _cfa (_ecf *_da .Creator ,_eeb *block ){_eeb ._de .SetPos (_eeb ._bfd ,_eeb ._ccd );
_fdc :=_ecf .Draw (_eeb ._de );if _fdc !=nil {_eg .Log .Debug ("\u0045\u0072\u0072or\u0020\u0064\u0072\u0061\u0077\u0069\u006e\u0067\u0020\u0062\u006c\u006f\u0063\u006b\u003a\u0020\u0025\u0073",_fdc );};if _eeb ._aae {_ef .DrawRectangle (_ecf ,&_ef .Rectangle {Top :_eeb ._ccd ,Bottom :_eeb ._ccd +_eeb ._de .Height (),Left :_eeb ._bfd ,Right :_eeb ._bfd +_eeb ._de .Width ()},_eeb ._fgcf ,_eeb ._abf );
};};func _bacac (_cccf *_da .Creator ,_egdcc *_da .Block ,_dacgc []*paragraph ,_dddf float64 ,_ceaf _da .PageFinalizeFunctionArgs )float64 {_fcac :=0.0;_baaee :=0.0;for _ ,_bgac :=range _dacgc {for _ ,_dfcf :=range _bgac ._ed {for _ ,_abag :=range _dfcf ._dgbf {for _ ,_caac :=range _abag ._egf {for _ ,_caecd :=range _caac ._edf {if _caecd ._fggc !=nil {_caecd ._fggc .SetPos (_caac ._gcd +_caecd ._cd ,_dddf );
_egdcc .Draw (_caecd ._fggc );}else if _caecd ._bfb !=nil {if _caecd ._bfb ._bfd ==0{_caecd ._bfb ._bfd =_caac ._gcd +_caecd ._cd ;};if _caecd ._bfb ._ccd ==0{_caecd ._bfb ._ccd =_bgac ._bdd +_dfcf ._bab ;};_cfa (_cccf ,_caecd ._bfb );}else {_gfaf :=_cccf .NewStyledParagraph ();
if _caecd ._bga {_caecd ._cae =0;}else if _caecd ._fdeb {_caecd ._cae =1.2*_dfcf ._fgc -_caecd ._dfc ;};_cecec :=_caac ._gcd +_caecd ._cd +_baaee ;_ceef :=_dddf +_dfcf ._bab +_caecd ._cae +_fcac ;_gfaf .SetPos (_cecec ,_ceef );_eggf :=false ;if _caecd ._aaa =="\u005b\u0046\u0049E\u004c\u0044\u005f\u0050\u0041\u0047\u0045\u005d"{_caecd ._aaa =_b .Itoa (_ceaf .PageNum );
_eggf =true ;};if _caecd ._aaa =="\u005b\u0046I\u0045\u004c\u0044_\u004e\u0055\u004d\u0050\u0041\u0047\u0045\u0053\u005d"{_caecd ._aaa =_b .Itoa (_ceaf .TotalPages );_eggf =true ;};var _ceec *_da .TextChunk ;if _caecd ._gcb !=""{_ceec =_gfaf .AddExternalLink (_caecd ._aaa ,_caecd ._gcb );
}else {_ceec =_gfaf .Append (_caecd ._aaa );};if _caecd ._dab !=nil {_ceec .Style =*_caecd ._dab ;};if _eggf {_baaee +=_gfaf .Width ();};_egdcc .Draw (_gfaf );if _caecd ._fag !=nil {_ffbg :=_ceef +_caecd ._dfc ;_ef .DrawLine (_cccf ,_cecec ,_ffbg ,_cecec +_caecd ._df ,_ffbg ,1,*_caecd ._fag );
};};};};};};if _bgac ._aa !=nil {_daef :=_da .NewBlock (_bgac ._aa ._ec ,_ceaf .PageHeight );_daef .SetPos (_bgac ._cc ,_dddf );_daef .Draw (_bgac ._aa ._cef );_egdcc .Draw (_daef );_bgac ._aee =_bgac ._aa ._cef .Height ();};_fcac +=_bgac ._aee ;};return _fcac ;
};func _bdcd (_ccda ,_decf *_bfc .CT_TcPr )*_bfc .CT_TcPr {if _ccda ==nil {return _decf ;};if _decf ==nil {return _ccda ;};if _ccda .CnfStyle ==nil {_ccda .CnfStyle =_decf .CnfStyle ;};if _ccda .TcW ==nil {_ccda .TcW =_decf .TcW ;};if _ccda .GridSpan ==nil {_ccda .GridSpan =_decf .GridSpan ;
};if _ccda .HMerge ==nil {_ccda .HMerge =_decf .HMerge ;};if _ccda .VMerge ==nil {_ccda .VMerge =_decf .VMerge ;};if _ccda .TcBorders ==nil {_ccda .TcBorders =_decf .TcBorders ;};if _ccda .Shd ==nil {_ccda .Shd =_decf .Shd ;};if _ccda .NoWrap ==nil {_ccda .NoWrap =_decf .NoWrap ;
};if _ccda .TcMar ==nil {_ccda .TcMar =_decf .TcMar ;};if _ccda .TextDirection ==nil {_ccda .TextDirection =_decf .TextDirection ;};if _ccda .TcFitText ==nil {_ccda .TcFitText =_decf .TcFitText ;};if _ccda .VAlign ==nil {_ccda .VAlign =_decf .VAlign ;};
if _ccda .HideMark ==nil {_ccda .HideMark =_decf .HideMark ;};if _ccda .Headers ==nil {_ccda .Headers =_decf .Headers ;};if _ccda .CellMarkupElementsChoice .CellIns ==nil {_ccda .CellMarkupElementsChoice .CellIns =_decf .CellMarkupElementsChoice .CellIns ;
};if _ccda .CellMarkupElementsChoice .CellDel ==nil {_ccda .CellMarkupElementsChoice .CellDel =_decf .CellMarkupElementsChoice .CellDel ;};if _ccda .CellMarkupElementsChoice .CellMerge ==nil {_ccda .CellMarkupElementsChoice .CellMerge =_decf .CellMarkupElementsChoice .CellMerge ;
};if _ccda .TcPrChange ==nil {_ccda .TcPrChange =_decf .TcPrChange ;};return _ccda ;};func (_ffef *convertContext )addAbsoluteEGPC (_aed []*_bfc .EG_PContent ,_dbaa *_bfc .CT_PPr )bool {_bfbb :=len (_aed );for _ ,_bbdc :=range _aed {for _ ,_cfedf :=range _bbdc .PContentChoice .FldSimple {if _cfedf !=nil {_ffef .addAbsoluteEGPC (_cfedf .EG_PContent ,_dbaa );
};};if _bge :=_bbdc .PContentChoice .Hyperlink ;_bge !=nil {_ffef ._edgdg =_bge ;_ffef .addAbsoluteCRC (_bge .PContentChoice .EG_ContentRunContent ,_dbaa );};_ffef ._edgdg =nil ;if _ffef .addAbsoluteCRC (_bbdc .PContentChoice .EG_ContentRunContent ,_dbaa ){if _bfbb > 1{_ffef .moveCurrentParagraphToNewPage ();
continue ;}else {return true ;};};_bfbb --;};return false ;};type styleAttributes struct{};

// FontStyle represents a kind of font styling. It can be FontStyle_Regular, FontStyle_Bold, FontStyle_Italic and FontStyle_BoldItalic.
type FontStyle =_ef .FontStyle ;func (_aeea *convertContext )addRowToTable (_fdcac map[int ][]tableCellProperties ,_fdbg int ,_ece *_da .Table ,_caff []float64 )error {_eebg :=_fdcac [_fdbg ];_gabee :=_eebg [0]._fda ;_fcga :=_eebg [0]._ceg ;for _ ,_ecbge :=range _eebg {if _ecbge ._edb {_aeea .addEmptyCellToTable (_ece ,_ecbge ._dgb ,_ecbge ._cgd ,_ecbge ._fde ,_ecbge ._cbc ,_ecbge ._eda ,_ecbge ._fgg ,_ecbge ._gda ,_ecbge ._aab ,_ecbge ._fgf );
}else {_aeea .addCellToTable (_ece ,_ecbge ._dgb ,_ecbge ._cgd ,_ecbge ._fde ,_ecbge ._cbc ,_ecbge ._eda ,_ecbge ._fgg ,_ecbge ._gda ,_ecbge ._aab ,_ecbge ._eec ,_ecbge ._fgf ,_ecbge ._fad ,_ecbge ._dacg ,_caff );};};if _gabee {_adde :=_ece .SetRowHeight (_ece .CurRow (),_fcga );
if _adde !=nil {return _g .Errorf ("E\u0052\u0052\u004f\u0052\u003a\u0020\u0055\u006e\u0061b\u006c\u0065\u0020\u0074\u006f\u0020\u0073et\u0020\u0072\u006f\u0077 \u0068\u0065\u0069\u0067\u0068\u0074\u0073\u0020\u0066or\u0020\u0074a\u0062\u006c\u0065\u0020\u0028\u0025\u0073\u0029",_adde .Error ());
};};return nil ;};func (_deabd *convertContext )addParagraphWithTable (_bcca _da .Table ,_cdg ,_fdcd float64 ){_deabd .newParagraph ();_deabd ._cfeb ._agb =&_ef .Rectangle {Top :_ggef (2),Bottom :_ggef (2),Left :0,Right :0};_deabd ._cfeb ._aa =&tableWrapper {_cef :&_bcca ,_ec :_cdg };
_deabd ._cfeb ._fbc =_fdcd ;_deabd ._cfeb ._aee =_bcca .Height ();_deabd .determineParagraphBounds ();_deabd .addCurrentParagraphToCurrentPage ();_deabd ._fadc ._cfe =_deabd ._fadc ._fa ;};func (_eae *convertContext )drawPage (_babf *page ){if _babf ._eea {_ffe :=_babf ._ebc .Top +_dae *_ge ;
_gfcb :=_babf ._ebc .Left ;_ggb :=_babf ._ebc .Right ;_ef .DrawLine (_eae ._affc ,_gfcb ,_ffe ,_ggb ,_ffe ,_ffa ,_da .ColorBlack );};for _ ,_agc :=range _babf ._cb {_eag (_eae ._affc ,_agc );};for _ ,_agg :=range _babf ._dgd {_cfa (_eae ._affc ,_agg );
};for _ ,_gde :=range _babf ._gc {if _gde ._fcg {_bbd :=_gde ._bdd +_dae *_ge ;_fcgf :=_babf ._ebc .Left ;_daga :=_fcgf +_ggef (50);_ef .DrawLine (_eae ._affc ,_fcgf ,_bbd ,_daga ,_bbd ,_ffa ,_da .ColorBlack );}else {for _ ,_bcgb :=range _gde ._ed {if _bcgb ._fgd {_eae .processRtlLine (_bcgb );
};for _ ,_eecc :=range _bcgb ._dgbf {for _ ,_fea :=range _eecc ._egf {for _ ,_dddg :=range _fea ._edf {if _dddg ._fggc !=nil {_dddg ._fggc .SetPos (_fea ._gcd +_dddg ._cd ,_gde ._bdd +_bcgb ._bab );_bgad :=_eae ._affc .Draw (_dddg ._fggc );if _bgad !=nil {_eg .Log .Debug ("\u0045\u0072\u0072or\u0020\u0064\u0072\u0061\u0077\u0069\u006e\u0067\u0020\u0069\u006d\u0061\u0067\u0065\u003a\u0020\u0025\u0073",_bgad );
};}else if _dddg ._bfb !=nil {_dddg ._bfb ._bfd =_fea ._gcd +_dddg ._cd ;_dddg ._bfb ._ccd =_gde ._bdd +_bcgb ._bab ;_cfa (_eae ._affc ,_dddg ._bfb );}else {_feac :=_eae ._affc .NewStyledParagraph ();if _dddg ._bga {_dddg ._cae =0;}else if _dddg ._fdeb {_dddg ._cae =1.2*_bcgb ._fgc -_dddg ._dfc ;
};_gbf :=_fea ._gcd +_dddg ._cd ;_gbfc :=_gde ._bdd +_bcgb ._bab +_dddg ._cae ;_feac .SetPos (_gbf ,_gbfc );var _bcgg *_da .TextChunk ;if _dddg ._gcb !=""{_bcgg =_feac .AddExternalLink (_dddg ._aaa ,_dddg ._gcb );}else {_bcgg =_feac .Append (_dddg ._aaa );
};if _dddg ._dab !=nil {_bcgg .Style =*_dddg ._dab ;};if _dddg ._cca !=nil {_bcgg .Highlight (*_dddg ._cca ,1.0);};_dcd :=_eae ._affc .Draw (_feac );if _dcd !=nil {_eg .Log .Debug ("\u0045\u0072\u0072\u006fr \u0064\u0072\u0061\u0077\u0069\u006e\u0067\u0020\u0074\u0065\u0078\u0074\u003a\u0020%\u0073",_dcd );
};if _dddg ._fag !=nil {_dcdc :=_gbfc +_dddg ._abg +2.0;_ef .DrawLine (_eae ._affc ,_gbf ,_dcdc ,_gbf +_dddg ._df ,_dcdc ,1,*_dddg ._fag );};};};};};};if _gde ._aa !=nil {switch _gde ._aa ._ca {case _da .HorizontalAlignmentCenter :_gde ._aa ._cef .SetPos (_gde ._cc +(_babf ._ebc .Right -_babf ._ebc .Left -_gde ._aa ._ec )/2,_gde ._bdd +_gde ._agb .Top );
case _da .HorizontalAlignmentRight :_gde ._aa ._cef .SetPos (_babf ._ebc .Right -_gde ._aa ._ec -_gde ._agb .Right ,_gde ._bdd +_gde ._agb .Top );default:_gde ._aa ._cef .SetPos (_gde ._cc ,_gde ._bdd +_gde ._agb .Top );};_bbg :=_da .NewBlock (_gde ._aa ._ec ,_eae ._affc .Height ());
_bbg .SetPos (0,0);_ =_bbg .Draw (_gde ._aa ._cef );_ =_eae ._affc .Draw (_bbg );};if _gde ._fd !=nil {_fdee :=(_babf ._ebc .Left /_ef .DefaultFontSize -1);_cad :=1.5;for _ ,_bec :=range _gde ._fd {_ddf :=_gde ._ddd +_bec ._daa +_fdee ;if _ddf > _gde ._dcg +_fdee {_ddf =_gde ._dcg +_fdee ;
};switch _bec ._gcdd {case _ef .BorderPositionTop :_caf :=_gde ._bdd +_bec ._ea ;_ef .DrawLine (_eae ._affc ,_gde ._ddd -_fdee ,_caf ,_ddf ,_caf ,_bec ._gbg ,_bec ._aef );case _ef .BorderPositionLeft :_bef :=_gde ._bdd +_gde ._aee -_gde ._agb .Top -_gde ._agb .Bottom -_bec ._ea -_cad ;
_bcae :=_bef +_gde ._aee +_gde ._agb .Top +_gde ._agb .Bottom ;_dge :=_gde ._ddd -_fdee ;_ef .DrawLine (_eae ._affc ,_dge ,_bef ,_dge ,_bcae ,_bec ._daa ,_bec ._aef );case _ef .BorderPositionBottom :_fbde :=_gde ._bdd +_bec ._ea +_gde ._agb .Top +_gde ._aee +_gde ._agb .Bottom ;
_ef .DrawLine (_eae ._affc ,_gde ._ddd -_fdee ,_fbde ,_ddf ,_fbde ,_bec ._gbg ,_bec ._aef );case _ef .BorderPositionRight :_ceb :=_gde ._bdd +_gde ._aee -_gde ._agb .Top -_gde ._agb .Bottom -_bec ._ea -_cad ;_cgb :=_ceb +_gde ._aee +_gde ._agb .Top +_gde ._agb .Bottom ;
_bfe :=_gde ._dcg +_fdee ;_ef .DrawLine (_eae ._affc ,_bfe ,_ceb ,_bfe ,_cgb ,_bec ._daa ,_bec ._aef );};};};};};for _ ,_cabg :=range _babf ._efe {_eag (_eae ._affc ,_cabg );};for _ ,_aaea :=range _babf ._dg {_cfa (_eae ._affc ,_aaea );};if len (_babf ._fe )> 0{_bddd :=_babf ._ebc .Bottom +_dae *_ge ;
_becd :=_babf ._ebc .Left ;_fcae :=_becd +_ggef (50);_ef .DrawLine (_eae ._affc ,_becd ,_bddd ,_fcae ,_bddd ,_ffa ,_da .ColorBlack );_bde :=_babf ._ebc .Bottom +_dae ;for _ ,_efb :=range _babf ._fe {_efb ._bce .SetPos (_babf ._ebc .Left ,_bde );_cda :=_eae ._affc .Draw (_efb ._bce );
if _cda !=nil {_eg .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u0064\u0072\u0061\u0077\u0069n\u0067\u0020\u0066\u006f\u006f\u0074\u006e\u006f\u0074\u0065:\u0020\u0025\u0073",_cda );};_bde +=_efb ._bce .Height ();};};};var _afgbd =map[int32 ]int32 {61623:8226,61607:8226,61558:9830,61656:8594,61692:8730};
func _ggaa (_cbac *_bfc .CT_TblWidth ,_bfabd int64 )float64 {if _cbac !=nil {if _cbac .TypeAttr ==_bfc .ST_TblWidthUnset ||_cbac .TypeAttr ==_bfc .ST_TblWidthDxa {if _gfdde :=_cbac .WAttr ;_gfdde !=nil {if _fedd :=_gfdde .ST_DecimalNumberOrPercent ;_fedd !=nil {if _ddad :=_fedd .ST_UnqualifiedPercentage ;
_ddad !=nil {return _ef .PointsFromTwips (*_ddad +_bfabd );};};};};};return 0.0;};func (_gcgf *convertContext )addEndnotes (){for _efgb ,_fdgg :=range _gcgf ._aegfc {if _efgb ==0{_gcgf .addSeparator ();};_gcgf ._cead =&prefix {_bdee :_fdgg ._cf };for _fbga ,_afaef :=range _fdgg ._bfcb {if _efgb !=0||_fbga !=0{_gcgf ._cgdcd =true ;
};_gcgf .addAbsoluteCBCs (_afaef .BlockLevelEltsChoice .EG_ContentBlockContent ,nil );};};_gcgf ._cgdcd =false ;};func (_aagb *convertContext )addEmptyCellToTable (_edge *_da .Table ,_feeda *_bfc .CT_Tc ,_aeed *_bfc .CT_TblPr ,_gaaea *_bfc .CT_TblPrEx ,_daf ,_bcdg ,_gacdc ,_cacaec int ,_bee []*_bfc .CT_TblStylePr ,_ceba *_bfc .CT_RPr ){_aagb .getTableCellProperties (_edge ,_aeed ,_gaaea ,_bee ,_daf ,_feeda .TcPr ,_ceba ,_bcdg ,_gacdc ,_cacaec );
};func _dace (_ffedc string )bool {for _ ,_bdgb :=range _ffedc {if _bdgb >=0x0600&&_bdgb <=0x06E0{return true ;};};return false ;};func (_cdcf *convertContext )assignHeaderFooterToPage (_gcce *page ){_dcedc :=_cdcf ._edega .DocRels ();_fcdb :=_be .MustCompile ("\u005b\u0030\u002d\u0039\u005d\u002b");
for _ ,_eecg :=range _gcce ._gb {if _eecg !=nil &&_eecg ._gbgd {if _bfegg :=_dcedc .GetTargetByRelId (_eecg ._ddag );_bfegg !=""{_fdccf ,_ :=_b .Atoi (_fcdb .FindString (_bfegg ));for _bfdge ,_cdfge :=range _cdcf ._edega .Headers (){if _fdccf ==(_bfdge +1){_cdcf ._dadc =true ;
_cdcf ._fcgc =false ;for _ ,_effc :=range _cdfge .X ().EG_BlockLevelElts {_cdcf .addAbsoluteHeaderFooterCBCs (_effc .BlockLevelEltsChoice .EG_ContentBlockContent );};};};};};};for _ ,_dggf :=range _gcce ._bb {if _dggf !=nil &&_dggf ._bbed {if _eacf :=_dcedc .GetTargetByRelId (_dggf ._ddag );
_eacf !=""{_cggca ,_ :=_b .Atoi (_fcdb .FindString (_eacf ));for _gagc ,_cddb :=range _cdcf ._edega .Footers (){if _cggca ==(_gagc +1){_cdcf ._dadc =false ;_cdcf ._fcgc =true ;for _ ,_def :=range _cddb .X ().EG_BlockLevelElts {_cdcf .addAbsoluteHeaderFooterCBCs (_def .BlockLevelEltsChoice .EG_ContentBlockContent );
};};};};};};};func (_gccda *convertContext )addAbsoluteRIC (_egg *_bfc .EG_RunInnerContent ,_aede *_bfc .CT_RPr ,_fcfd *_bfc .CT_PPr )bool {var _gac ,_aedef bool ;_aefe :=[]*symbol {};_fef :=false ;if _egg ==nil {if _gccda ._cead !=nil {_gdfa :=true ;for _ ,_bcfc :=range _gccda ._cead ._bdee {if _beg ,_bfdg :=_afgbd [_bcfc ];
_bfdg {_aedef =_gccda ._cead ._cffd ;_gccda ._cead ._bdee =string (rune (_beg ));_gdfa =false ;};};_aefe =_ebcc (_gccda ._cead ._bdee ,"",true ,false ,_gdfa );};}else {if _bfae (_egg ){return true ;}else if _egg .RunInnerContentChoice .T !=nil {_gbeb :=_egg .RunInnerContentChoice .T .Content ;
_adcec :=_fcfd ==nil ||_fcfd .Bidi ==nil ||_dgba (_fcfd .Bidi );if _aede !=nil &&_dgba (_aede .Rtl )&&_adcec {_gccda ._gfcba ._fgd =true ;if _dace (_gbeb ){_adcd ,_eebc :=_ee .ArabicShape (_gbeb );if _eebc ==nil {_gbeb =_adcd ;};};};if _aede !=nil &&_dgba (_aede .Caps ){_gbeb =_ga .ToUpper (_gbeb );
};if _gbeb ==""{_gbeb ="\u0020";};_eeec ,_ :=_be .MatchString ("\u00ab\u002e\u002a\u00bb",_gbeb );if len (_gccda ._efdb ._edf )> 0&&_gccda ._efdb ._edf [len (_gccda ._efdb ._edf )-1]._dgda &&_gccda ._efdb ._edf [len (_gccda ._efdb ._edf )-1]._aaa ==""&&!_eeec {return false ;
};if _ccg :=_gccda ._edgdg ;_ccg !=nil &&_ccg .IdAttr !=nil {_fef =true ;_aefe =_ebcc (_gbeb ,_gccda ._edega .GetTargetByRelId (*_ccg .IdAttr ),false ,false ,false );}else {_aefe =_ebcc (_gbeb ,"",false ,false ,false );};if _aede .Highlight !=nil {_egfd :=_e .HighlightColorToCreatorColorMap [_aede .Highlight .ValAttr ];
for _ ,_bdgc :=range _aefe {_bdgc ._cca =&_egfd ;};};}else if _beaa :=_egg .RunInnerContentChoice .EndnoteReference ;_beaa !=nil {_bbcd :=_gccda ._edega .BodySection ().X ();_fead :=_beaa .IdAttr ;_fbfb :=_fead ;_cdac :=_bfc .ST_NumberFormatLowerRoman ;
if _fgaa :=_bbcd .EndnotePr ;_fgaa !=nil {if _efg :=_fgaa .NumFmt ;_efg !=nil {_cdac =_efg .ValAttr ;};if _dcedf :=_fgaa .NumStart ;_dcedf !=nil {_fbfb +=_dcedf .ValAttr -1;};};_dfgb :=_cabb (_fbfb ,_cdac );_cbec :=_gccda ._edega .Endnote (_fead ).X ();
if _cbec !=nil {_gccda ._aegfc =append (_gccda ._aegfc ,note {_cf :_dfgb ,_bfcb :_cbec .EG_BlockLevelElts });_aefe =_ebcc (_dfgb ,"",true ,false ,false );};}else if _aca :=_egg .RunInnerContentChoice .FootnoteReference ;_aca !=nil {_afad :=_gccda ._edega .BodySection ().X ();
_afee :=_aca .IdAttr ;_gffg :=_afee ;_gaeg :=_bfc .ST_NumberFormatDecimal ;if _bgc :=_afad .FootnotePr ;_bgc !=nil {if _fce :=_bgc .NumFmt ;_fce !=nil {_gaeg =_fce .ValAttr ;};if _fabg :=_bgc .NumStart ;_fabg !=nil {_gffg +=_fabg .ValAttr -1;};};_gbd :=_cabb (_gffg ,_gaeg );
_baf :=_gccda ._edega .Footnote (_afee ).X ();if _baf !=nil {_ggba :=&note {_cf :_gbd ,_bfcb :_baf .EG_BlockLevelElts };_fcge :=[][]*_bfc .EG_ContentBlockContent {};for _ ,_fgda :=range _baf .EG_BlockLevelElts {_fcge =append (_fcge ,_fgda .BlockLevelEltsChoice .EG_ContentBlockContent );
};_ffdg :=&prefix {_bdee :_gbd };_ggde ,_efad :=_gccda .makePdfBlockFromCBCs (_fcge ,_gccda ._fadc ._ebc .Right -_gccda ._fadc ._ebc .Left ,_ggef (1000),nil ,true ,_ffdg );if _efad !=nil {_eg .Log .Debug ("C\u0061\u006e\u006e\u006f\u0074\u0020c\u006f\u006e\u0076\u0065\u0072\u0074\u0020\u0066\u006fo\u0074\u006e\u006ft\u0065:\u0020\u0025\u0073",_efad );
return false ;};_ggba ._bce =_ggde ;_gccda ._cfeb ._edg =append (_gccda ._cfeb ._edg ,_ggba );_gccda ._cfeb ._aeg +=_ggba ._bce .Height ();_aefe =_ebcc (_gbd ,"",true ,false ,false );};}else if _baa :=_egg .RunInnerContentChoice .InstrText ;_baa !=nil {_dadf :=_ccgg (_baa .Content );
if _dadf !=""{_aefe =_ebcc (_gccda ._gegc [_dadf ],"",false ,false ,false );};if _baa .Content ==_fca .FieldCurrentPage {_aefe =_dgbe ("\u005b\u0046\u0049E\u004c\u0044\u005f\u0050\u0041\u0047\u0045\u005d");};if _baa .Content ==_fca .FieldNumberOfPages {_aefe =_dgbe ("\u005b\u0046I\u0045\u004c\u0044_\u004e\u0055\u004d\u0050\u0041\u0047\u0045\u0053\u005d");
};}else if _cbg :=_egg .RunInnerContentChoice .Drawing ;_cbg !=nil {for _ ,_dcf :=range _cbg .DrawingChoice {if _dcf .Inline ==nil {continue ;};_dedg :=_dcf .Inline ;if _ffc :=_dedg .Graphic ;_ffc !=nil {if _gaa :=_ffc .GraphicData ;_gaa !=nil {_bfg :=_dedg .Extent ;
if _bfg ==nil {return false ;};_ebba :=_ag .FromEMU (_bfg .CxAttr );_bdgcg :=_ag .FromEMU (_bfg .CyAttr );if _gaca :=_dedg .EffectExtent ;_gaca !=nil {if _gaca .LAttr .ST_CoordinateUnqualified !=nil {_ebba +=_ag .FromEMU (*_gaca .LAttr .ST_CoordinateUnqualified );
};if _gaca .RAttr .ST_CoordinateUnqualified !=nil {_ebba +=_ag .FromEMU (*_gaca .RAttr .ST_CoordinateUnqualified );};if _gaca .TAttr .ST_CoordinateUnqualified !=nil {_bdgcg +=_ag .FromEMU (*_gaca .TAttr .ST_CoordinateUnqualified );};if _gaca .BAttr .ST_CoordinateUnqualified !=nil {_bdgcg +=_ag .FromEMU (*_gaca .BAttr .ST_CoordinateUnqualified );
};};for _ ,_adbb :=range _gaa .Any {if _cfd ,_bega :=_adbb .(*_ffd .Pic );_bega {_ebdb :=&symbol {_dfc :_bdgcg ,_df :_ebba };_deg ,_gead :=_gccda .makePdfImageFromGraphics (_cfd );if _gead !=nil {_eg .Log .Debug ("C\u0061\u006e\u006e\u006ft \u0072e\u0061\u0064\u0020\u0069\u006da\u0067\u0065\u003a\u0020\u0025\u0073",_gead );
};if _deg ==nil {_ebdb ._aaa ="\u0020";}else {_abfb :=_cfd .BlipFill ;if _abfb .SrcRect !=nil {var _babgb ,_bbee ,_gfad ,_bgf float64 ;_dadfb :=_abfb .SrcRect ;if _dadfb .LAttr !=nil {_babgb =float64 (*_dadfb .LAttr .ST_PercentageDecimal )/1000.0;};if _dadfb .RAttr !=nil {_gfad =float64 (*_dadfb .RAttr .ST_PercentageDecimal )/1000.0;
};if _dadfb .TAttr !=nil {_bbee =float64 (*_dadfb .TAttr .ST_PercentageDecimal )/1000.0;};if _dadfb .BAttr !=nil {_bgf =float64 (*_abfb .SrcRect .BAttr .ST_PercentageDecimal )/1000.0;};_bcc :=_deg .Width ();_faba :=_deg .Height ();_deg .Crop (int (_babgb /100.0*_bcc ),int (_bbee /100.0*_faba ),int (_bcc -(_gfad /100.0*_bcc )),int (_faba -(_bgf /100.0*_faba )));
};_gfdg :=false ;if _cfd .SpPr !=nil &&_cfd .SpPr .Xfrm !=nil {if _cfd .SpPr .Xfrm .RotAttr !=nil {_adf :=_ag .DegreeFromSTAngle (*_cfd .SpPr .Xfrm .RotAttr );_deg .SetAngle (_adf );};if _cfd .SpPr .Xfrm .Ext !=nil {_gfdg =true ;};};if _gfdg {_deg .ScaleToWidth (_ebba );
}else {_deg .Scale (_ebba /_deg .Width (),_ebba /_deg .Width ());};_ebdb ._fggc =_deg ;_gac =true ;};_aefe =[]*symbol {_ebdb };}else if _abfbe ,_bgfb :=_adbb .(*_db .Chart );_bgfb {_afdd :=&symbol {_dfc :_bdgcg ,_df :_ebba };_dfdc ,_gef :=_gccda .makePdfBlockFromChart (_abfbe ,_ebba ,_bdgcg );
if _gef !=nil {_eg .Log .Debug ("C\u0061\u006e\u006e\u006ft \u0072e\u0061\u0064\u0020\u0062\u006co\u0063\u006b\u003a\u0020\u0025\u0073",_gef );};if _dfdc ==nil {_afdd ._aaa ="\u0020";}else {_afdd ._bfb =&block {_de :_dfdc };_gac =true ;};_aefe =[]*symbol {_afdd };
};};};};};}else if _fcd :=_egg .RunInnerContentChoice .Pict ;_fcd !=nil {for _ ,_aggg :=range _fcd .Any {if _afgc ,_gcbb :=_aggg .(*_ff .Group );_gcbb {for _ ,_ebgc :=range _afgc .GroupChoice {if _ebgc .Rect !=nil {_gccda .addRect (_ebgc .Rect );}else if _ebgc .Shape !=nil {_cde :=_ebgc .Shape ;
_ede :=_eb .NewShapeStyle ("");if _cde .StyleAttr !=nil {_ede =_eb .NewShapeStyle (*_cde .StyleAttr );};_fcec :=_ef .PointsFromTwips (int64 (_ede .Width ()));_dgfa :=_ef .PointsFromTwips (int64 (_ede .Height ()));_dcda :=_ef .PointsFromTwips (int64 (_ede .Left ()-_ede .Right ()));
_ggc :=_ef .PointsFromTwips (int64 (_ede .Top ()-_ede .Bottom ()));for _ ,_ccfc :=range _cde .ShapeChoice {if _ccfc .ShapeElementsChoice !=nil {_bgeb :=_ccfc .ShapeElementsChoice ;if _bgeb .Imagedata !=nil {_edgf :=&symbol {_dfc :_fcec ,_df :_dgfa };_ggdf ,_beb :=_gccda .makePdfImageFromRelId (_bgeb .Imagedata .IdAttr );
if _beb !=nil {_eg .Log .Debug ("C\u0061\u006e\u006e\u006ft \u0072e\u0061\u0064\u0020\u0069\u006da\u0067\u0065\u003a\u0020\u0025\u0073",_beb );};if _ggdf ==nil {_edgf ._aaa ="\u0020";}else {_ggdf .Scale (_fcec /_ggdf .Width (),_dgfa /_ggdf .Height ());
_ggdf .SetPos (_dcda ,_ggc );_edgf ._fggc =_ggdf ;_gac =true ;};_aefe =[]*symbol {_edgf };if _ede .Position ()==_eb .ShapeStylePositionAbsolute {_gccda ._gfcba ._ffg =_gccda ._cfeb ._ddd +_dcda ;_gccda ._gfcba ._bab =_ggc ;};};};};};};};if _feae ,_aeda :=_aggg .(*_ff .Shape );
_aeda {_ccca :=_eb .NewShapeStyle ("");if _feae .StyleAttr !=nil {_ccca =_eb .NewShapeStyle (*_feae .StyleAttr );};_bbfe :=_da .ColorWhite ;if _feae .StrokecolorAttr !=nil {_bbfe =_da .ColorRGBFromHex (*_feae .StrokecolorAttr );};if _feae .FillcolorAttr !=nil {_bbfe =_da .ColorRGBFromHex (*_feae .FillcolorAttr );
};_bfbc :=_ccca .Width ();_fdba :=_ccca .Height ();_bdgd :=_ef .PointsFromTwips (int64 (_ccca .Left ()-_ccca .Right ()));_aaee :=_ef .PointsFromTwips (int64 (_ccca .Top ()-_ccca .Bottom ()));_baff ,_cebg ,_bgfc ,_eeecb :=_ccca .Margins ();_eage :=&borderLine {_gcdd :_ef .BorderPositionBottom ,_daa :_bfbc ,_gbg :_fdba ,_aef :_bbfe };
_gccda ._cfeb ._fd =append (_gccda ._cfeb ._fd ,_eage );_gccda ._cfeb ._agb =&_ef .Rectangle {Top :float64 (_baff ),Left :float64 (_cebg ),Bottom :float64 (_bgfc ),Right :float64 (_eeecb )};if _ccca .Position ()==_eb .ShapeStylePositionAbsolute {_gccda ._gfcba ._ffg =_gccda ._cfeb ._ddd +_bdgd +float64 (_ccca .Left ());
_gccda ._gfcba ._bab =_aaee ;};var _efef []*symbol ;for _ ,_gfeb :=range _feae .ShapeChoice {if _gfeb .ShapeElementsChoice ==nil {continue ;};_gefb :=_gfeb .ShapeElementsChoice ;if _gefb .Textbox !=nil &&_gefb .Textbox .TxbxContent !=nil {_aag ,_ :=_gccda .makeBlockFromTextboxContent (_gefb .Textbox .TxbxContent ,_bfbc ,_fdba ,nil );
if _aag !=nil {_afb :=&symbol {_dfc :_fdba ,_df :_bfbc };if _ccca .MSOPositionVerticalRelative ()=="\u0070\u0061\u0067\u0065"{_aag ._ccd =_baff ;};if _ccca .MSOPositionHorizontalRelative ()=="\u0070\u0061\u0067\u0065"{_aag ._bfd =_cebg ;};_afb ._bfb =_aag ;
_afb ._aaa ="\u0020";_efef =append (_efef ,_afb );};};};if len (_efef )> 0{_aefe =_efef ;};};if _deb ,_babc :=_aggg .(*_ff .Line );_babc {_fcc :=_eb .NewShapeStyle ("");if _deb .StyleAttr !=nil {_fcc =_eb .NewShapeStyle (*_deb .StyleAttr );};_edbg ,_agba :=0.0,0.0;
if _deb .FromAttr !=nil {_edbg ,_agba =_adcfg (*_deb .FromAttr );};_ggcf ,_gedg :=_edbg ,_agba ;if _deb .ToAttr !=nil {_ggcf ,_gedg =_adcfg (*_deb .ToAttr );};_abfd :=_da .ColorWhite ;if _deb .StrokecolorAttr !=nil {_abfd =_da .ColorRGBFromHex (*_deb .StrokecolorAttr );
};_eagc :=_gedg -_agba ;if _deb .StrokeweightAttr !=nil {_bdfc ,_bfda :=_b .ParseFloat (_ga .ReplaceAll (*_deb .StrokeweightAttr ,"\u0070\u0074",""),64);if _bfda !=nil {_eg .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u0055\u006e\u0061\u0062\u006c\u0065\u0020\u0070a\u0072\u0073\u0065\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u006f\u0066\u0020v\u003a\u006c\u0069\u006e\u0065\u0020\u0073\u0074\u0072\u006f\u006b\u0065 w\u0065\u0069\u0067\u0068\u0074\u0020\u0028\u0025\u0073\u0029",_bfda .Error ());
};_eagc =_bdfc ;};_dgfd :=&borderLine {_gcdd :_ef .BorderPositionBottom ,_daa :_ggcf -_edbg ,_gbg :_eagc ,_aef :_abfd };_gccda ._cfeb ._fd =append (_gccda ._cfeb ._fd ,_dgfd );if _fcc .Position ()==_eb .ShapeStylePositionAbsolute {_gccda ._gfcba ._ffg =_gccda ._cfeb ._ddd +_edbg ;
_gccda ._gfcba ._bab =_agba ;};};if _bfeg ,_ecc :=_aggg .(*_ff .Rect );_ecc {_gccda .addRect (_bfeg );};};}else if _feb :=_egg .RunInnerContentChoice .Tab ;_feb !=nil {_afae :=_cabfe ;if _gccda ._bfeaf ==nil {_gccda ._bfeaf =_bfc .NewCT_PPr ();};if _eba :=_gccda ._bfeaf .Tabs ;
_eba !=nil {_aad :=_eba .Tab [0];_afec :=_ef .PointsFromTwips (*_aad .PosAttr .Int64 );if _aad .ValAttr !=_bfc .ST_TabJcEnd &&_aad .ValAttr !=_bfc .ST_TabJcRight {_afec +=_cabfe ;};_afae =_afec -_gccda ._cfeb ._fbc -_gccda ._cfeb ._agb .Left -_gccda ._cfeb ._agb .Right ;
_gggc :=0.0;for _ ,_addg :=range _gccda ._cfeb ._ed {for _ ,_acb :=range _addg ._dgbf {for _ ,_faa :=range _acb ._egf {for _ ,_dadg :=range _faa ._edf {_gggc +=_dadg ._df ;};};};};_afae =_afae -_gggc -_gccda ._cfeb ._ddd ;if _afae < _cabfe {_afae =_cabfe ;
};};if _bba :=_gccda ._bfeaf .Ind ;_bba !=nil {if _bba .HangingAttr !=nil {_bff :=_ef .PointsFromTwips (int64 (*_bba .HangingAttr .ST_UnsignedDecimalNumber ));_afae -=_gccda ._gfcba ._ffg -_bff ;};};_aefe =_ebcc ("\u0009","",false ,false ,false );_gacd :=_aefe [len (_aefe )-1];
_gacd ._df =_afae ;}else if _edda :=_egg .RunInnerContentChoice .Ptab ;_edda !=nil {_fefb :=_gccda ._cfeb ._fbc +_gccda ._cfeb ._agb .Left ;if _edda .RelativeToAttr ==_bfc .ST_PTabRelativeToIndent {_fefb =_gccda ._cfeb ._fbc ;};_aagd :=0.0;for _ ,_fggcf :=range _gccda ._cfeb ._ed {for _ ,_cbf :=range _fggcf ._dgbf {for _ ,_beba :=range _cbf ._egf {for _ ,_daca :=range _beba ._edf {_aagd +=_daca ._df ;
};};};};if _edda .AlignmentAttr ==_bfc .ST_PTabAlignmentCenter {_fefb +=(_gccda ._cfeb ._dcg -(_gccda ._cfeb ._ddd +_gccda ._cfeb ._agb .Left +_gccda ._cfeb ._agb .Right ))/2;}else if _edda .AlignmentAttr ==_bfc .ST_PTabAlignmentRight {_fefb +=_gccda ._cfeb ._ddd +_gccda ._cfeb ._agb .Left +_gccda ._cfeb ._agb .Right +_aagd ;
};_aefe =_ebcc ("\u0009","",false ,false ,false );_edac :=_aefe [len (_aefe )-1];_edac ._df =_fefb ;}else if _egg .RunInnerContentChoice .LastRenderedPageBreak !=nil &&!_gccda ._cfeb ._fg {_aefe =append (_aefe ,&symbol {_gdg :true });}else if _dafa (_egg ){_gccda .addCurrentWordToParagraph ();
_gccda .newLine ();_gccda .newWord ();_gccda .addEmptyLine ();}else if _egg .RunInnerContentChoice .FldChar !=nil {_bcce :=_egg .RunInnerContentChoice .FldChar .FldCharTypeAttr ;switch _bcce {case _bfc .ST_FldCharTypeBegin ,_bfc .ST_FldCharTypeSeparate :_gccda ._efdb ._edf =append (_gccda ._efdb ._edf ,&symbol {_dgda :true });
case _bfc .ST_FldCharTypeEnd :_gccda ._efdb ._edf =append (_gccda ._efdb ._edf ,&symbol {_dgda :false });};return false ;};};var _cga _da .TextStyle ;var _ecd ,_afbf bool ;var _afbc *_da .Color ;if !_gac {_cga ,_ecd ,_afbf ,_afbc =_gccda .makeRunStyle (_aede ,false ,false ,false ,_aedef ,_fef );
if _cga .Font !=nil &&(_gccda ._dagg ==nil ||(_gccda ._dagg !=nil &&_gccda ._dagg .EnableFontSubsetting )){_gccda ._affc .EnableFontSubsetting (_cga .Font );};};for _ ,_dgae :=range _aefe {if _dgae ._gdg &&_gccda ._fadc ._fa > _gccda ._gefe .Top {_gccda .addCurrentParagraphToCurrentPage ();
_gccda .newPage ();_gccda .newParagraph ();_gccda .determineParagraphBounds ();_gccda .newLine ();_gccda .newWord ();continue ;};if _dgae ._fggc !=nil ||_dgae ._bfb !=nil {_gccda .addInlineSymbol (_dgae );}else {_dgae ._dab =&_cga ;_dgae ._bga =_ecd ;_dgae ._fdeb =_afbf ;
_dgae ._fag =_afbc ;if _dgae ._aff {_gfcc :=*_aede ;_gfcc .B =nil ;_gfcc .U =nil ;_edaa ,_ ,_ ,_ :=_gccda .makeRunStyle (&_gfcc ,false ,false ,false ,_aedef ,_fef );_dgae ._dab =&_edaa ;_dgae ._fag =nil ;};_gccda .addTextSymbol (_dgae );};};if _gccda ._cead !=nil &&_gccda ._cead ._fgdbb {var _cfca ,_cccb float64 ;
for _ ,_cfaa :=range _aefe {_cfca +=_cfaa ._df ;};_acbf :=0;_gbce :=_gccda ._fadc ._ebc .Left ;_cce :=len (_gccda ._cead ._bfgb );if _cce > 1&&_gccda ._cead ._fgdbb {_cce =len (_gccda ._cead ._bfgb )-1;};_cacb :=_gccda ._cfeb ._fbc < _cfca ;_dfeb :=_gccda ._gfcba ._cbca +_cfca ;
for {var _cceg float64 ;if _cacb ||_acbf >=_cce {_cceg =_cabfe ;}else {_cceg =_gccda ._cead ._bfgb [_acbf ];_acbf ++;};_gbce +=_cceg ;if _gbce > _dfeb {_cccb =_gbce -_dfeb ;break ;};};_gccda .addTextSymbol (&symbol {_aaa :"\u0020",_df :_cccb });};return false ;
};func (_adeg *convertContext )addCurrentWordToParagraph (){for {_acd :=_adeg ._gfcba ._ffg ;_gfg :=_acd +_adeg ._efdb ._afg ;if _gfg > _adeg ._gfcba ._fbg {if len (_adeg ._efdb ._edf )==1&&_adeg ._efdb ._edf [0]._fggc !=nil {break ;};_adeg .newLine ();
};_fccg :=_adeg ._cfeb ._bdd +_adeg ._gfcba ._bab ;_bgg :=_fccg +_adeg ._gfcba ._fgc ;_eabd :=false ;_aggd :=append (_adeg ._fadc ._cfc ,_adeg ._cfeb ._aeb ...);for _ ,_aaae :=range _aggd {_bdfd :=_aaae ._dc ;if ((_fccg > _bdfd .Top &&_fccg < _bdfd .Bottom )||(_bgg > _bdfd .Top &&_bgg < _bdfd .Bottom ))&&((_acd > _bdfd .Left &&_acd < _bdfd .Right )||(_gfg > _bdfd .Left &&_gfg < _bdfd .Right )){_eabd =true ;
if _adeg ._gfcba ._ffg < _bdfd .Right {_adeg ._dcged ._dca =_bdfd .Left ;_adeg ._gfcba ._ffg =_bdfd .Right ;_adeg .newSpan ();};};};if !_eabd {break ;};};if !_adeg ._efdb ._ffb ||len (_adeg ._dcged ._egf )> 0{_adeg ._efdb ._gcd =_adeg ._gfcba ._ffg ;_adeg ._dcged ._egf =append (_adeg ._dcged ._egf ,_adeg ._efdb );
_adeg ._gfcba ._ffg +=_adeg ._efdb ._afg ;for _ ,_cefd :=range _adeg ._efdb ._edf {if _adeg .shouldApplyContextualSpacing (_adeg ._bfeaf ){_adeg .adjustHeights (_adeg ._cfeb ._agb .Top );}else {_adeg .adjustHeights (_cefd ._dfc );};};};};var _gdae =map[string ]uint16 {"\u0061\u0061":0x1404,"\u0061\u0061\u002dD\u004a":0x1000,"\u0061\u0061\u002dE\u0052":0x1000,"\u0061\u0061\u002dE\u0054":0x1000,"\u0061\u0066":0x0036,"\u0061\u0066\u002dN\u0041":0x1000,"\u0061\u0066\u002dZ\u0041":0x0436,"\u0061\u0067\u0071":0x1000,"\u0061\u0067\u0071\u002d\u0043\u004d":0x1000,"\u0061\u006b":0x1000,"\u0061\u006b\u002dG\u0048":0x1000,"\u0073\u0071":0x001C,"\u0073\u0071\u002dA\u004c":0x041C,"\u0073\u0071\u002dM\u004b":0x1000,"\u0067\u0073\u0077":0x0084,"\u0067\u0073\u0077\u002d\u0046\u0052":0x0484,"\u0067\u0073\u0077\u002d\u004c\u0049":0x1000,"\u0067\u0073\u0077\u002d\u0043\u0048":0x1000,"\u0061\u006d":0x005E,"\u0061\u006d\u002dE\u0054":0x045E,"\u0061\u0072":0x0001,"\u0061\u0072\u002dD\u005a":0x1401,"\u0061\u0072\u002dT\u0044":0x1000,"\u0061\u0072\u002dK\u004d":0x1000,"\u0061\u0072\u002dD\u004a":0x1000,"\u0061\u0072\u002dE\u0047":0x0c01,"\u0061\u0072\u002dE\u0052":0x1000,"\u0061\u0072\u002dI\u0051":0x0801,"\u0061\u0072\u002dI\u004c":0x1000,"\u0061\u0072\u002dJ\u004f":0x2C01,"\u0061\u0072\u002dK\u0057":0x3401,"\u0061\u0072\u002dL\u0042":0x3001,"\u0061\u0072\u002dL\u0059":0x1001,"\u0061\u0072\u002dM\u0052":0x1000,"\u0061\u0072\u002dM\u0041":0x1801,"\u0061\u0072\u002dO\u004d":0x2001,"\u0061\u0072\u002dP\u0053":0x1000,"\u0061\u0072\u002dQ\u0041":0x4001,"\u0061\u0072\u002dS\u0041":0x0401,"\u0061\u0072\u002dS\u004f":0x1000,"\u0061\u0072\u002dS\u0053":0x1000,"\u0061\u0072\u002dS\u0044":0x1000,"\u0061\u0072\u002dS\u0059":0x2801,"\u0061\u0072\u002dT\u004e":0x1C01,"\u0061\u0072\u002dA\u0045":0x3801,"\u0061\u0072\u002d\u0030\u0030\u0031":0x1000,"\u0061\u0072\u002dY\u0045":0x2401,"\u0068\u0079":0x002B,"\u0068\u0079\u002dA\u004d":0x042B,"\u0061\u0073":0x004D,"\u0061\u0073\u002dI\u004e":0x044D,"\u0061\u0073\u0074":0x1000,"\u0061\u0073\u0074\u002d\u0045\u0053":0x1000,"\u0061\u0073\u0061":0x1000,"\u0061\u0073\u0061\u002d\u0054\u005a":0x1000,"\u0061z\u002d\u0043\u0079\u0072\u006c":0x742C,"\u0061\u007a\u002d\u0043\u0079\u0072\u006c\u002d\u0041\u005a":0x082C,"\u0061\u007a":0x002C,"\u0061z\u002d\u004c\u0061\u0074\u006e":0x782C,"\u0061\u007a\u002d\u004c\u0061\u0074\u006e\u002d\u0041\u005a":0x042C,"\u006b\u0073\u0066":0x1000,"\u006b\u0073\u0066\u002d\u0043\u004d":0x1000,"\u0062\u006d":0x1000,"\u0062\u006d\u002d\u004c\u0061\u0074\u006e\u002d\u004d\u004c":0x1000,"\u0062\u006e":0x0045,"\u0062\u006e\u002dB\u0044":0x0845,"\u0062\u006e\u002dI\u004e":0x0445,"\u0062\u0061\u0073":0x1000,"\u0062\u0061\u0073\u002d\u0043\u004d":0x1000,"\u0062\u0061":0x006D,"\u0062\u0061\u002dR\u0055":0x046D,"\u0065\u0075":0x002D,"\u0065\u0075\u002dE\u0053":0x042D,"\u0062\u0065":0x0023,"\u0062\u0065\u002dB\u0059":0x0423,"\u0062\u0065\u006d":0x1000,"\u0062\u0065\u006d\u002d\u005a\u004d":0x1000,"\u0062\u0065\u007a":0x1000,"\u0062\u0065\u007a\u002d\u0054\u005a":0x1000,"\u0062\u0079\u006e":0x1000,"\u0062\u0079\u006e\u002d\u0045\u0052":0x1000,"\u0062\u0072\u0078":0x1000,"\u0062\u0072\u0078\u002d\u0049\u004e":0x1000,"\u0062s\u002d\u0043\u0072\u0079\u006c":0x6414,"\u0062\u0073\u002d\u0043\u0079\u0072\u006c\u002d\u0042\u0041":0x201A,"\u0062s\u002d\u004c\u0061\u0074\u006e":0x681A,"\u0062\u0073":0x7814,"\u0062\u0073\u002d\u004c\u0061\u0074\u006e\u002d\u0042\u0041":0x141A,"\u0062\u0072":0x007E,"\u0062\u0072\u002dF\u0052":0x047E,"\u0062\u0067":0x0002,"\u0062\u0067\u002dB\u0047":0x0402,"\u006d\u0079":0x0055,"\u006d\u0079\u002dM\u004d":0x0455,"\u0063\u0061":0x0003,"\u0063\u0061\u002dA\u0044":0x1000,"\u0063\u0061\u002dF\u0052":0x1000,"\u0063\u0061\u002dI\u0054":0x1000,"\u0063\u0061\u002dE\u0053":0x0403,"\u0063\u0065\u0062":0x1000,"\u0063\u0065\u0062\u002d\u004c\u0061\u0074\u006e":0x1000,"c\u0065\u0062\u002d\u004c\u0061\u0074\u006e\u002d\u0050\u0048":0x1000,"t\u007a\u006d\u002d\u0041\u0072\u0061\u0062\u002d\u004d\u0041":0x045F,"t\u006d\u007a\u002d\u004c\u0061\u0074\u006e\u002d\u004d\u0041":0x1000,"\u006b\u0075":0x0092,"\u006bu\u002d\u0041\u0072\u0061\u0062":0x7c92,"\u006b\u0075\u002d\u0041\u0072\u0061\u0062\u002d\u0049\u0051":0x0492,"\u0063\u0063\u0070":0x1000,"\u0063\u0063\u0070\u002d\u0043\u0061\u006b\u006d":0x1000,"c\u0063\u0070\u002d\u0043\u0061\u006b\u006d\u002d\u0049\u004e":0x1000,"\u0063\u0065\u002dR\u0055":0x1000,"\u0063\u0068\u0072":0x005C,"\u0063\u0068\u0072\u002d\u0043\u0068\u0065\u0072":0x7c5c,"c\u0068\u0072\u002d\u0043\u0068\u0065\u0072\u002d\u0055\u0053":0x045C,"\u0063\u0067\u0067":0x1000,"\u0063\u0067\u0067\u002d\u0055\u0047":0x1000,"\u007ah\u002d\u0048\u0061\u006e\u0073":0x0004,"\u007a\u0068":0x7804,"\u007a\u0068\u002dC\u004e":0x0804,"\u007a\u0068\u002dS\u0047":0x1004,"\u007ah\u002d\u0048\u0061\u006e\u0074":0x7C04,"\u007a\u0068\u002dH\u004b":0x0C04,"\u007a\u0068\u002dM\u004f":0x1404,"\u007a\u0068\u002dT\u0057":0x0404,"\u0063\u0075\u002dR\u0055":0x1000,"\u0073\u0077\u0063":0x1000,"\u0073\u0077\u0063\u002d\u0043\u0044":0x1000,"\u006b\u0077":0x1000,"\u006b\u0077\u002dG\u0042":0x1000,"\u0063\u006f":0x0083,"\u0063\u006f\u002dF\u0052":0x0483,"\u0068\u0072":0x001A,"\u0068\u0072\u002dH\u0052":0x041A,"\u0068\u0072\u002dB\u0041":0x101A,"\u0063\u0073":0x0005,"\u0063\u0073\u002dC\u005a":0x0405,"\u0064\u0061":0x0006,"\u0064\u0061\u002dD\u004b":0x0406,"\u0064\u0061\u002dG\u004c":0x1000,"\u0070\u0072\u0073":0x008C,"\u0070\u0072\u0073\u002d\u0041\u0046":0x048C,"\u0064\u0076":0x0065,"\u0064\u0076\u002dM\u0056":0x0465,"\u0064\u0075\u0061":0x1000,"\u0064\u0075\u0061\u002d\u0043\u004d":0x1000,"\u006e\u006c":0x0013,"\u006e\u006c\u002dA\u0057":0x1000,"\u006e\u006c\u002dB\u0045":0x0813,"\u006e\u006c\u002dB\u0051":0x1000,"\u006e\u006c\u002dC\u0057":0x1000,"\u006e\u006c\u002dN\u004c":0x0413,"\u006e\u006c\u002dS\u0058":0x1000,"\u006e\u006c\u002dS\u0052":0x1000,"\u0064\u007a":0x1000,"\u0064\u007a\u002dB\u0054":0x0C51,"\u0065\u0062\u0075":0x1000,"\u0065\u0062\u0075\u002d\u004b\u0045":0x1000,"\u0065\u006e\u002dA\u0053":0x1000,"\u0065\u006e\u002dA\u0049":0x1000,"\u0065\u006e\u002dA\u0047":0x1000,"\u0065\u006e\u002dA\u0055":0x0C09,"\u0065\u006e\u002dA\u0054":0x1000,"\u0065\u006e\u002dB\u0053":0x1000,"\u0065\u006e\u002dB\u0042":0x1000,"\u0065\u006e\u002dB\u0045":0x1000,"\u0065\u006e\u002dB\u005a":0x2809,"\u0065\u006e\u002dB\u004d":0x1000,"\u0065\u006e\u002dB\u0057":0x1000,"\u0065\u006e\u002dI\u004f":0x1000,"\u0065\u006e\u002dV\u0047":0x1000,"\u0065\u006e\u002dB\u0049":0x1000,"\u0065\u006e\u002dC\u004d":0x1000,"\u0065\u006e\u002dC\u0041":0x1009,"\u0065\u006e\u002d\u0030\u0032\u0039":0x2409,"\u0065\u006e\u002dK\u0059":0x1000,"\u0065\u006e\u002dC\u0058":0x1000,"\u0065\u006e\u002dC\u0043":0x1000,"\u0065\u006e\u002dC\u004b":0x1000,"\u0065\u006e\u002dC\u0059":0x1000,"\u0065\u006e\u002dD\u004b":0x1000,"\u0065\u006e\u002dD\u004d":0x1000,"\u0065\u006e\u002dE\u0052":0x1000,"\u0065\u006e\u002d\u0031\u0035\u0030":0x1000,"\u0065\u006e\u002dF\u004b":0x1000,"\u0065\u006e\u002dF\u0049":0x1000,"\u0065\u006e\u002dF\u004a":0x1000,"\u0065\u006e\u002dG\u004d":0x1000,"\u0065\u006e\u002dD\u0045":0x1000,"\u0065\u006e\u002dG\u0048":0x1000,"\u0065\u006e\u002dG\u0049":0x1000,"\u0065\u006e\u002dG\u0044":0x1000,"\u0065\u006e\u002dG\u0055":0x1000,"\u0065\u006e\u002dG\u0047":0x1000,"\u0065\u006e\u002dG\u0059":0x1000,"\u0065\u006e\u002dH\u004b":0x3C09,"\u0065\u006e\u002dI\u004e":0x4009,"\u0065\u006e\u002dI\u004d":0x1000,"\u0065\u006e\u002dI\u004c":0x1000,"\u0065\u006e\u002dJ\u004d":0x2009,"\u0065\u006e\u002dJ\u0045":0x1000,"\u0065\u006e\u002dK\u0045":0x1000,"\u0065\u006e\u002dK\u0049":0x1000,"\u0065\u006e\u002dL\u0053":0x1000,"\u0065\u006e\u002dL\u0052":0x1000,"\u0065\u006e\u002dM\u004f":0x1000,"\u0065\u006e\u002dM\u0047":0x1000,"\u0065\u006e\u002dM\u0057":0x1000,"\u0065\u006e\u002dM\u0059":0x4409,"\u0065\u006e\u002dM\u0054":0x1000,"\u0065\u006e\u002dM\u0048":0x1000,"\u0065\u006e\u002dM\u0055":0x1000,"\u0065\u006e\u002dF\u004d":0x1000,"\u0065\u006e\u002dM\u0053":0x1000,"\u0065\u006e\u002dN\u0041":0x1000,"\u0065\u006e\u002dN\u0052":0x1000,"\u0065\u006e\u002dN\u004c":0x1000,"\u0065\u006e\u002dN\u005a":0x1409,"\u0065\u006e\u002dN\u0047":0x1000,"\u0065\u006e\u002dN\u0055":0x1000,"\u0065\u006e\u002dN\u0046":0x1000,"\u0065\u006e\u002dM\u0050":0x1000,"\u0065\u006e\u002dP\u004b":0x1000,"\u0065\u006e\u002dP\u0057":0x1000,"\u0065\u006e\u002dP\u0047":0x1000,"\u0065\u006e\u002dP\u004e":0x1000,"\u0065\u006e\u002dP\u0052":0x1000,"\u0065\u006e\u002dP\u0048":0x3409,"\u0065\u006e\u002dR\u0057":0x1000,"\u0065\u006e\u002dK\u004e":0x1000,"\u0065\u006e\u002dL\u0043":0x1000,"\u0065\u006e\u002dV\u0043":0x1000,"\u0065\u006e\u002dW\u0053":0x1000,"\u0065\u006e\u002dS\u0043":0x1000,"\u0065\u006e\u002dS\u004c":0x1000,"\u0065\u006e\u002dS\u0047":0x4809,"\u0065\u006e\u002dS\u0058":0x1000,"\u0065\u006e\u002dS\u0049":0x1000,"\u0065\u006e\u002dS\u0042":0x1000,"\u0065\u006e\u002dZ\u0041":0x1C09,"\u0065\u006e\u002dS\u0053":0x1000,"\u0065\u006e\u002dS\u0048":0x1000,"\u0065\u006e\u002dS\u0044":0x1000,"\u0065\u006e\u002dS\u005a":0x1000,"\u0065\u006e\u002dS\u0045":0x1000,"\u0065\u006e\u002dC\u0048":0x1000,"\u0065\u006e\u002dT\u005a":0x1000,"\u0065\u006e\u002dT\u004b":0x1000,"\u0065\u006e\u002dT\u004f":0x1000,"\u0065\u006e\u002dT\u0054":0x2c09,"\u0065\u006e\u002dT\u0043":0x1000,"\u0065\u006e\u002dT\u0056":0x1000,"\u0065\u006e\u002dU\u0047":0x1000,"\u0065\u006e\u002dA\u0045":0x4C09,"\u0065\u006e\u002dG\u0042":0x0809,"\u0065\u006e\u002dU\u0053":0x0409,"\u0065\u006e\u002dU\u004d":0x1000,"\u0065\u006e\u002dV\u0049":0x1000,"\u0065\u006e\u002dV\u0055":0x1000,"\u0065\u006e\u002d\u0030\u0030\u0031":0x1000,"\u0065\u006e\u002dZ\u004d":0x1000,"\u0065\u006e\u002dZ\u0057":0x3009,"\u0065\u006f":0x1000,"\u0065\u006f\u002d\u0030\u0030\u0031":0x1000,"\u0065\u0074":0x0025,"\u0065\u0074\u002dE\u0045":0x0425,"\u0065\u0065":0x1000,"\u0065\u0065\u002dG\u0048":0x1000,"\u0065\u0065\u002dT\u0047":0x1000,"\u0065\u0077\u006f":0x1000,"\u0065\u0077\u006f\u002d\u0043\u004d":0x1000,"\u0066\u006f":0x0038,"\u0066\u006f\u002dD\u004b":0x1000,"\u0066\u006f\u002dF\u004f":0x0438,"\u0066\u0069\u006c":0x0064,"\u0066\u0069\u006c\u002d\u0050\u0048":0x0464,"\u0066\u0069":0x000B,"\u0066\u0069\u002dF\u0049":0x040B,"\u0066\u0072":0x000C,"\u0066\u0072\u002dD\u005a":0x1000,"\u0066\u0072\u002dB\u0045":0x080C,"\u0066\u0072\u002dB\u004a":0x1000,"\u0066\u0072\u002dB\u0046":0x1000,"\u0066\u0072\u002dB\u0049":0x1000,"\u0066\u0072\u002dC\u004d":0x2c0C,"\u0066\u0072\u002dC\u0041":0x0c0C,"\u0066\u0072\u002d\u0030\u0032\u0039":0x1C0C,"\u0066\u0072\u002dC\u0046":0x1000,"\u0066\u0072\u002dT\u0044":0x1000,"\u0066\u0072\u002dK\u004d":0x1000,"\u0066\u0072\u002dC\u0047":0x1000,"\u0066\u0072\u002dC\u0044":0x240C,"\u0066\u0072\u002dC\u0049":0x300C,"\u0066\u0072\u002dD\u004a":0x1000,"\u0066\u0072\u002dG\u0051":0x1000,"\u0066\u0072\u002dF\u0052":0x040C,"\u0066\u0072\u002dG\u0046":0x1000,"\u0066\u0072\u002dP\u0046":0x1000,"\u0066\u0072\u002dG\u0041":0x1000,"\u0066\u0072\u002dG\u0050":0x1000,"\u0066\u0072\u002dG\u004e":0x1000,"\u0066\u0072\u002dH\u0054":0x3c0C,"\u0066\u0072\u002dL\u0055":0x140C,"\u0066\u0072\u002dM\u0047":0x1000,"\u0066\u0072\u002dM\u004c":0x340C,"\u0066\u0072\u002dM\u0051":0x1000,"\u0066\u0072\u002dM\u0052":0x1000,"\u0066\u0072\u002dM\u0055":0x1000,"\u0066\u0072\u002dY\u0054":0x1000,"\u0066\u0072\u002dM\u0041":0x380C,"\u0066\u0072\u002dN\u0043":0x1000,"\u0066\u0072\u002dN\u0045":0x1000,"\u0066\u0072\u002dM\u0043":0x180C,"\u0066\u0072\u002dR\u0045":0x200C,"\u0066\u0072\u002dR\u0057":0x1000,"\u0066\u0072\u002dB\u004c":0x1000,"\u0066\u0072\u002dM\u0046":0x1000,"\u0066\u0072\u002dP\u004d":0x1000,"\u0066\u0072\u002dS\u004e":0x280C,"\u0066\u0072\u002dS\u0043":0x1000,"\u0066\u0072\u002dC\u0048":0x100C,"\u0066\u0072\u002dS\u0059":0x1000,"\u0066\u0072\u002dT\u0047":0x1000,"\u0066\u0072\u002dT\u004e":0x1000,"\u0066\u0072\u002dV\u0055":0x1000,"\u0066\u0072\u002dW\u0046":0x1000,"\u0066\u0079":0x0062,"\u0066\u0079\u002dN\u004c":0x0462,"\u0066\u0075\u0072":0x1000,"\u0066\u0075\u0072\u002d\u0049\u0054":0x1000,"\u0066\u0066":0x0067,"\u0066f\u002d\u004c\u0061\u0074\u006e":0x7C67,"\u0066\u0066\u002d\u004c\u0061\u0074\u006e\u002d\u0042\u0046":0x1000,"\u0066\u0066\u002dC\u004d":0x1000,"\u0066\u0066\u002d\u004c\u0061\u0074\u006e\u002d\u0043\u004d":0x1000,"\u0066\u0066\u002d\u004c\u0061\u0074\u006e\u002d\u0047\u004d":0x1000,"\u0066\u0066\u002d\u004c\u0061\u0074\u006e\u002d\u0047\u0048":0x1000,"\u0066\u0066\u002dG\u004e":0x1000,"\u0066\u0066\u002d\u004c\u0061\u0074\u006e\u002d\u0047\u004e":0x1000,"\u0066\u0066\u002d\u004c\u0061\u0074\u006e\u002d\u0047\u0057":0x1000,"\u0066\u0066\u002d\u004c\u0061\u0074\u006e\u002d\u004c\u0052":0x1000,"\u0066\u0066\u002dM\u0052":0x1000,"\u0066\u0066\u002d\u004c\u0061\u0074\u006e\u002d\u004d\u0052":0x1000,"\u0066\u0066\u002d\u004c\u0061\u0074\u006e\u002d\u004e\u0045":0x1000,"\u0066\u0066\u002dN\u0047":0x0467,"\u0066\u0066\u002d\u004c\u0061\u0074\u006e\u002d\u004e\u0047":0x0467,"\u0066\u0066\u002d\u004c\u0061\u0074\u006e\u002d\u0053\u004e":0x0867,"\u0066\u0066\u002d\u004c\u0061\u0074\u006e\u002d\u0053\u004c":0x1000,"\u0067\u006c":0x0056,"\u0067\u006c\u002dE\u0053":0x0456,"\u006c\u0067":0x1000,"\u006c\u0067\u002dU\u0047":0x1000,"\u006b\u0061":0x0037,"\u006b\u0061\u002dG\u0045":0x0437,"\u0064\u0065":0x0007,"\u0064\u0065\u002dA\u0054":0x0C07,"\u0064\u0065\u002dB\u0045":0x1000,"\u0064\u0065\u002dD\u0045":0x0407,"\u0064\u0065\u002dI\u0054":0x1000,"\u0064\u0065\u002dL\u0049":0x1407,"\u0064\u0065\u002dL\u0055":0x1007,"\u0064\u0065\u002dC\u0048":0x0807,"\u0065\u006c":0x0008,"\u0065\u006c\u002dC\u0059":0x1000,"\u0065\u006c\u002dG\u0052":0x0408,"\u006b\u006c":0x006F,"\u006b\u006c\u002dG\u004c":0x046F,"\u0067\u006e":0x0074,"\u0067\u006e\u002dP\u0059":0x0474,"\u0067\u0075":0x0047,"\u0067\u0075\u002dI\u004e":0x0447,"\u0067\u0075\u007a":0x1000,"\u0067\u0075\u007a\u002d\u004b\u0045":0x1000,"\u0068\u0061":0x0068,"\u0068a\u002d\u004c\u0061\u0074\u006e":0x7C68,"\u0068\u0061\u002d\u004c\u0061\u0074\u006e\u002d\u0047\u0048":0x1000,"\u0068\u0061\u002d\u004c\u0061\u0074\u006e\u002d\u004e\u0045":0x1000,"\u0068\u0061\u002d\u004c\u0061\u0074\u006e\u002d\u004e\u0047":0x0468,"\u0068\u0061\u0077":0x0075,"\u0068\u0061\u0077\u002d\u0055\u0053":0x0475,"\u0068\u0065":0x000D,"\u0068\u0065\u002dI\u004c":0x040D,"\u0068\u0069":0x0039,"\u0068\u0069\u002dI\u004e":0x0439,"\u0068\u0075":0x000E,"\u0068\u0075\u002dH\u0055":0x040E,"\u0069\u0073":0x000F,"\u0069\u0073\u002dI\u0053":0x040F,"\u0069\u0067":0x0070,"\u0069\u0067\u002dN\u0047":0x0470,"\u0069\u0064":0x0021,"\u0069\u0064\u002dI\u0044":0x0421,"\u0069\u0061":0x1000,"\u0069\u0061\u002dF\u0052":0x1000,"\u0069\u0061\u002d\u0030\u0030\u0031":0x1000,"\u0069\u0075":0x005D,"\u0069u\u002d\u004c\u0061\u0074\u006e":0x7C5D,"\u0069\u0075\u002d\u004c\u0061\u0074\u006e\u002d\u0043\u0041":0x085D,"\u0069u\u002d\u0043\u0061\u006e\u0073":0x785D,"\u0067\u0061":0x003C,"\u0067\u0061\u002dI\u0045":0x083C,"\u0069\u0074":0x0010,"\u0069\u0074\u002dI\u0054":0x0410,"\u0069\u0074\u002dS\u004d":0x1000,"\u0069\u0074\u002dC\u0048":0x0810,"\u0069\u0074\u002dV\u0041":0x1000,"\u006a\u0061":0x0011,"\u006a\u0061\u002dJ\u0050":0x0411,"\u006a\u0076":0x1000,"\u006av\u002d\u004c\u0061\u0074\u006e":0x1000,"\u006a\u0076\u002d\u004c\u0061\u0074\u006e\u002d\u0049\u0044":0x1000,"\u0064\u0079\u006f":0x1000,"\u0064\u0079\u006f\u002d\u0053\u004e":0x1000,"\u006b\u0065\u0061":0x1000,"\u006b\u0065\u0061\u002d\u0043\u0056":0x1000,"\u006b\u0061\u0062":0x1000,"\u006b\u0061\u0062\u002d\u0044\u005a":0x1000,"\u006b\u006b\u006a":0x1000,"\u006b\u006b\u006a\u002d\u0043\u004d":0x1000,"\u006b\u006c\u006e":0x1000,"\u006b\u006c\u006e\u002d\u004b\u0045":0x1000,"\u006b\u0061\u006d":0x1000,"\u006b\u0061\u006d\u002d\u004b\u0045":0x1000,"\u006b\u006e":0x004B,"\u006b\u006e\u002dI\u004e":0x044B,"\u006b\u0072\u002d\u004c\u0061\u0074\u006e\u002d\u004e\u0047":0x0471,"\u006b\u0073":0x0060,"\u006bs\u002d\u0041\u0072\u0061\u0062":0x0460,"\u006b\u0073\u002d\u0041\u0072\u0061\u0062\u002d\u0049\u004e":0x1000,"\u006b\u0073\u002d\u0044\u0065\u0076\u0061\u002d\u0049\u004e":0x0860,"\u006b\u006b":0x003F,"\u006b\u006b\u002dK\u005a":0x043F,"\u006b\u006d":0x0053,"\u006b\u006d\u002dK\u0048":0x0453,"\u0071\u0075\u0063":0x0086,"q\u0075\u0063\u002d\u004c\u0061\u0074\u006e\u002d\u0047\u0054":0x0486,"\u006b\u0069":0x1000,"\u006b\u0069\u002dK\u0045":0x1000,"\u0072\u0077":0x0087,"\u0072\u0077\u002dR\u0057":0x0487,"\u0073\u0077\u002dK\u0045":0x0441,"\u0073\u0077\u002dT\u005a":0x1000,"\u0073\u0077\u002dU\u0047":0x1000,"\u006b\u006f\u006b":0x0057,"\u006b\u006f\u006b\u002d\u0049\u004e":0x0457,"\u006b\u006f":0x0012,"\u006b\u006f\u002dK\u0052":0x0412,"\u006b\u006f\u002dK\u0050":0x1000,"\u006b\u0068\u0071":0x1000,"\u006b\u0068\u0071\u002d\u004d\u004c":0x1000,"\u0073\u0065\u0073":0x1000,"\u0073\u0065\u0073\u002d\u004d\u004c":0x1000,"\u006e\u006d\u0067":0x1000,"\u006e\u006d\u0067\u002d\u0043\u004d":0x1000,"\u006b\u0079":0x0040,"\u006b\u0079\u002dK\u0047":0x0440,"\u006b\u0075\u002d\u0041\u0072\u0061\u0062\u002d\u0049\u0052":0x1000,"\u006c\u006b\u0074":0x1000,"\u006c\u006b\u0074\u002d\u0055\u0053":0x1000,"\u006c\u0061\u0067":0x1000,"\u006c\u0061\u0067\u002d\u0054\u005a":0x1000,"\u006c\u006f":0x0054,"\u006c\u006f\u002dL\u0041":0x0454,"\u006c\u0061\u002dV\u0041":0x0476,"\u006c\u0076":0x0026,"\u006c\u0076\u002dL\u0056":0x0426,"\u006c\u006e":0x1000,"\u006c\u006e\u002dA\u004f":0x1000,"\u006c\u006e\u002dC\u0046":0x1000,"\u006c\u006e\u002dC\u0047":0x1000,"\u006c\u006e\u002dC\u0044":0x1000,"\u006c\u0074":0x0027,"\u006c\u0074\u002dL\u0054":0x0427,"\u006e\u0064\u0073":0x1000,"\u006e\u0064\u0073\u002d\u0044\u0045":0x1000,"\u006e\u0064\u0073\u002d\u004e\u004c":0x1000,"\u0064\u0073\u0062":0x7C2E,"\u0064\u0073\u0062\u002d\u0044\u0045":0x082E,"\u006c\u0075":0x1000,"\u006c\u0075\u002dC\u0044":0x1000,"\u006c\u0075\u006f":0x1000,"\u006c\u0075\u006f\u002d\u004b\u0045":0x1000,"\u006c\u0062":0x006E,"\u006c\u0062\u002dL\u0055":0x046E,"\u006c\u0075\u0079":0x1000,"\u006c\u0075\u0079\u002d\u004b\u0045":0x1000,"\u006d\u006b":0x002F,"\u006d\u006b\u002dM\u004b":0x042F,"\u006a\u006d\u0063":0x1000,"\u006a\u006d\u0063\u002d\u0054\u005a":0x1000,"\u006d\u0067\u0068":0x1000,"\u006d\u0067\u0068\u002d\u004d\u005a":0x1000,"\u006b\u0064\u0065":0x1000,"\u006b\u0064\u0065\u002d\u0054\u005a":0x1000,"\u006d\u0067":0x1000,"\u006d\u0067\u002dM\u0047":0x1000,"\u006d\u0073":0x003E,"\u006d\u0073\u002dB\u004e":0x083E,"\u006d\u0073\u002dM\u0059":0x043E,"\u006d\u006c":0x004C,"\u006d\u006c\u002dI\u004e":0x044C,"\u006d\u0074":0x003A,"\u006d\u0074\u002dM\u0054":0x043A,"\u0067\u0076":0x1000,"\u0067\u0076\u002dI\u004d":0x1000,"\u006d\u0069":0x0081,"\u006d\u0069\u002dN\u005a":0x0481,"\u0061\u0072\u006e":0x007A,"\u0061\u0072\u006e\u002d\u0043\u004c":0x047A,"\u006d\u0072":0x004E,"\u006d\u0072\u002dI\u004e":0x044E,"\u006d\u0061\u0073":0x1000,"\u006d\u0061\u0073\u002d\u004b\u0045":0x1000,"\u006d\u0061\u0073\u002d\u0054\u005a":0x1000,"\u006d\u007a\u006e\u002d\u0049\u0052":0x1000,"\u006d\u0065\u0072":0x1000,"\u006d\u0065\u0072\u002d\u004b\u0045":0x1000,"\u006d\u0067\u006f":0x1000,"\u006d\u0067\u006f\u002d\u0043\u004d":0x1000,"\u006d\u006f\u0068":0x007C,"\u006d\u006f\u0068\u002d\u0043\u0041":0x047C,"\u006d\u006e":0x0050,"\u006dn\u002d\u0043\u0079\u0072\u006c":0x7850,"\u006d\u006e\u002dM\u004e":0x0450,"\u006dn\u002d\u004d\u006f\u006e\u0067":0x7C50,"\u006d\u006e\u002d\u004d\u006f\u006e\u0067\u002d\u0043\u004e":0x0850,"\u006d\u006e\u002d\u004d\u006f\u006e\u0067\u002d\u004d\u004e":0x0C50,"\u006d\u0066\u0065":0x1000,"\u006d\u0066\u0065\u002d\u004d\u0055":0x1000,"\u006d\u0075\u0061":0x1000,"\u006d\u0075\u0061\u002d\u0043\u004d":0x1000,"\u006e\u0071\u006f":0x1000,"\u006e\u0071\u006f\u002d\u0047\u004e":0x1000,"\u006e\u0061\u0071":0x1000,"\u006e\u0061\u0071\u002d\u004e\u0041":0x1000,"\u006e\u0065":0x0061,"\u006e\u0065\u002dI\u004e":0x0861,"\u006e\u0065\u002dN\u0050":0x0461,"\u006e\u006e\u0068":0x1000,"\u006e\u006e\u0068\u002d\u0043\u004d":0x1000,"\u006a\u0067\u006f":0x1000,"\u006a\u0067\u006f\u002d\u0043\u004d":0x1000,"\u006c\u0072\u0063\u002d\u0049\u0051":0x1000,"\u006c\u0072\u0063\u002d\u0049\u0052":0x1000,"\u006e\u0064":0x1000,"\u006e\u0064\u002dZ\u0057":0x1000,"\u006e\u006f":0x0014,"\u006e\u0062":0x7C14,"\u006e\u0062\u002dN\u004f":0x0414,"\u006e\u006e":0x7814,"\u006e\u006e\u002dN\u004f":0x0814,"\u006e\u0062\u002dS\u004a":0x1000,"\u006e\u0075\u0073":0x1000,"\u006e\u0075\u0073\u002d\u0053\u0044":0x1000,"\u006e\u0075\u0073\u002d\u0053\u0053":0x1000,"\u006e\u0079\u006e":0x1000,"\u006e\u0079\u006e\u002d\u0055\u0047":0x1000,"\u006f\u0063":0x0082,"\u006f\u0063\u002dF\u0052":0x0482,"\u006f\u0072":0x0048,"\u006f\u0072\u002dI\u004e":0x0448,"\u006f\u006d":0x0072,"\u006f\u006d\u002dE\u0054":0x0472,"\u006f\u006d\u002dK\u0045":0x1000,"\u006f\u0073":0x1000,"\u006f\u0073\u002dG\u0045":0x1000,"\u006f\u0073\u002dR\u0055":0x1000,"\u0070\u0073":0x0063,"\u0070\u0073\u002dA\u0046":0x0463,"\u0070\u0073\u002dP\u004b":0x1000,"\u0066\u0061":0x0029,"\u0066\u0061\u002dA\u0046":0x1000,"\u0066\u0061\u002dI\u0052":0x0429,"\u0070\u006c":0x0015,"\u0070\u006c\u002dP\u004c":0x0415,"\u0070\u0074":0x0016,"\u0070\u0074\u002dA\u004f":0x1000,"\u0070\u0074\u002dB\u0052":0x0416,"\u0070\u0074\u002dC\u0056":0x1000,"\u0070\u0074\u002dG\u0051":0x1000,"\u0070\u0074\u002dG\u0057":0x1000,"\u0070\u0074\u002dL\u0055":0x1000,"\u0070\u0074\u002dM\u004f":0x1000,"\u0070\u0074\u002dM\u005a":0x1000,"\u0070\u0074\u002dP\u0054":0x0816,"\u0070\u0074\u002dS\u0054":0x1000,"\u0070\u0074\u002dC\u0048":0x1000,"\u0070\u0074\u002dT\u004c":0x1000,"\u0070r\u0067\u002d\u0030\u0030\u0031":0x1000,"\u0071p\u0073\u002d\u0070\u006c\u006f\u0063a":0x05FE,"\u0071\u0070\u0073\u002d\u0070\u006c\u006f\u0063":0x0501,"\u0071p\u0073\u002d\u0070\u006c\u006f\u0063m":0x09FF,"\u0070\u0061":0x0046,"\u0070a\u002d\u0041\u0072\u0061\u0062":0x7C46,"\u0070\u0061\u002dI\u004e":0x0446,"\u0070\u0061\u002d\u0041\u0072\u0061\u0062\u002d\u0050\u004b":0x0846,"\u0071\u0075\u007a":0x006B,"\u0071\u0075\u007a\u002d\u0042\u004f":0x046B,"\u0071\u0075\u007a\u002d\u0045\u0043":0x086B,"\u0071\u0075\u007a\u002d\u0050\u0045":0x0C6B,"\u006b\u0073\u0068":0x1000,"\u006b\u0073\u0068\u002d\u0044\u0045":0x1000,"\u0072\u006f":0x0018,"\u0072\u006f\u002dM\u0044":0x0818,"\u0072\u006f\u002dR\u004f":0x0418,"\u0072\u006d":0x0017,"\u0072\u006d\u002dC\u0048":0x0417,"\u0072\u006f\u0066":0x1000,"\u0072\u006f\u0066\u002d\u0054\u005a":0x1000,"\u0072\u006e":0x1000,"\u0072\u006e\u002dB\u0049":0x1000,"\u0072\u0075\u002dB\u0059":0x1000,"\u0072\u0075\u002dK\u005a":0x1000,"\u0072\u0075\u002dK\u0047":0x1000,"\u0072\u0075\u002dM\u0044":0x0819,"\u0072\u0075\u002dR\u0055":0x0419,"\u0072\u0075\u002dU\u0041":0x1000,"\u0072\u0077\u006b":0x1000,"\u0072\u0077\u006b\u002d\u0054\u005a":0x1000,"\u0073\u0073\u0079":0x1000,"\u0073\u0073\u0079\u002d\u0045\u0052":0x1000,"\u0073\u0061\u0068":0x0085,"\u0073\u0061\u0068\u002d\u0052\u0055":0x0485,"\u0073\u0061\u0071":0x1000,"\u0073\u0061\u0071\u002d\u004b\u0045":0x1000,"\u0073\u006d\u006e":0x703B,"\u0073\u006d\u006e\u002d\u0046\u0049":0x243B,"\u0073\u006d\u006a":0x7C3B,"\u0073\u006d\u006a\u002d\u004e\u004f":0x103B,"\u0073\u0065":0x003B,"\u0073\u0065\u002dF\u0049":0x0C3B,"\u0073\u0065\u002dN\u004f":0x043B,"\u0073\u0065\u002dS\u0045":0x083B,"\u0073\u006d\u0073":0x743B,"\u0073\u006d\u0073\u002d\u0046\u0049":0x203B,"\u0073\u006d\u0061":0x783B,"\u0073\u006d\u0061\u002d\u004e\u004f":0x183B,"\u0073\u006d\u0061\u002d\u0053\u0045":0x1C3B,"\u0073\u0067":0x1000,"\u0073\u0067\u002dC\u0046":0x1000,"\u0073\u0062\u0070":0x1000,"\u0073\u0062\u0070\u002d\u0054\u005a":0x1000,"\u0073\u0061":0x004F,"\u0073\u0061\u002dI\u004e":0x044F,"\u0067\u0064":0x0091,"\u0067\u0064\u002dG\u0042":0x0491,"\u0073\u0065\u0068":0x1000,"\u0073\u0065\u0068\u002d\u004d\u005a":0x1000,"\u0073r\u002d\u0043\u0079\u0072\u006c":0x6C1A,"\u0073\u0072\u002d\u0043\u0079\u0072\u006c\u002d\u0042\u0041":0x1C1A,"\u0073\u0072\u002d\u0043\u0079\u0072\u006c\u002d\u004d\u0045":0x301A,"\u0073\u0072\u002d\u0043\u0079\u0072\u006c\u002d\u0052\u0053":0x281A,"\u0073\u0072\u002d\u0043\u0079\u0072\u006c\u002d\u0043\u0053":0x0C1A,"\u0073r\u002d\u004c\u0061\u0074\u006e":0x701A,"\u0073\u0072":0x7C1A,"\u0073\u0072\u002d\u004c\u0061\u0074\u006e\u002d\u0042\u0041":0x181A,"\u0073\u0072\u002d\u004c\u0061\u0074\u006e\u002d\u004d\u0045":0x2c1A,"\u0073\u0072\u002d\u004c\u0061\u0074\u006e\u002d\u0052\u0053":0x241A,"\u0073\u0072\u002d\u004c\u0061\u0074\u006e\u002d\u0043\u0053":0x081A,"\u006e\u0073\u006f":0x006C,"\u006e\u0073\u006f\u002d\u005a\u0041":0x046C,"\u0074\u006e":0x0032,"\u0074\u006e\u002dB\u0057":0x0832,"\u0074\u006e\u002dZ\u0041":0x0432,"\u006b\u0073\u0062":0x1000,"\u006b\u0073\u0062\u002d\u0054\u005a":0x1000,"\u0073\u006e":0x1000,"\u0073n\u002d\u004c\u0061\u0074\u006e":0x1000,"\u0073\u006e\u002d\u004c\u0061\u0074\u006e\u002d\u005a\u0057":0x1000,"\u0073\u0064":0x0059,"\u0073d\u002d\u0041\u0072\u0061\u0062":0x7C59,"\u0073\u0064\u002d\u0041\u0072\u0061\u0062\u002d\u0050\u004b":0x0859,"\u0073\u0069":0x005B,"\u0073\u0069\u002dL\u004b":0x045B,"\u0073\u006b":0x001B,"\u0073\u006b\u002dS\u004b":0x041B,"\u0073\u006c":0x0024,"\u0073\u006c\u002dS\u0049":0x0424,"\u0078\u006f\u0067":0x1000,"\u0078\u006f\u0067\u002d\u0055\u0047":0x1000,"\u0073\u006f":0x0077,"\u0073\u006f\u002dD\u004a":0x1000,"\u0073\u006f\u002dE\u0054":0x1000,"\u0073\u006f\u002dK\u0045":0x1000,"\u0073\u006f\u002dS\u004f":0x0477,"\u0073\u0074":0x0030,"\u0073\u0074\u002dZ\u0041":0x0430,"\u006e\u0072":0x1000,"\u006e\u0072\u002dZ\u0041":0x1000,"\u0073\u0074\u002dL\u0053":0x1000,"\u0065\u0073":0x000A,"\u0065\u0073\u002dA\u0052":0x2C0A,"\u0065\u0073\u002dB\u005a":0x1000,"\u0065\u0073\u002dV\u0045":0x200A,"\u0065\u0073\u002dB\u004f":0x400A,"\u0065\u0073\u002dB\u0052":0x1000,"\u0065\u0073\u002dC\u004c":0x340A,"\u0065\u0073\u002dC\u004f":0x240A,"\u0065\u0073\u002dC\u0052":0x140A,"\u0065\u0073\u002dC\u0055":0x5c0A,"\u0065\u0073\u002dD\u004f":0x1c0A,"\u0065\u0073\u002dE\u0043":0x300A,"\u0065\u0073\u002dS\u0056":0x440A,"\u0065\u0073\u002dG\u0051":0x1000,"\u0065\u0073\u002dG\u0054":0x100A,"\u0065\u0073\u002dH\u004e":0x480A,"\u0065\u0073\u002d\u0034\u0031\u0039":0x580A,"\u0065\u0073\u002dM\u0058":0x080A,"\u0065\u0073\u002dN\u0049":0x4C0A,"\u0065\u0073\u002dP\u0041":0x180A,"\u0065\u0073\u002dP\u0059":0x3C0A,"\u0065\u0073\u002dP\u0045":0x280A,"\u0065\u0073\u002dP\u0048":0x1000,"\u0065\u0073\u002dP\u0052":0x500A,"\u0065\u0073\u002dE\u0053\u005f\u0074\u0072\u0061\u0064\u006e\u006c":0x040A,"\u0065\u0073\u002dE\u0053":0x0c0A,"\u0065\u0073\u002dU\u0053":0x540A,"\u0065\u0073\u002dU\u0059":0x390A,"\u007a\u0067\u0068":0x1000,"z\u0067\u0068\u002d\u0054\u0066\u006e\u0067\u002d\u004d\u0041":0x1000,"\u007a\u0067\u0068\u002d\u0054\u0066\u006e\u0067":0x1000,"\u0073\u0073":0x1000,"\u0073\u0073\u002dZ\u0041":0x1000,"\u0073\u0073\u002dS\u005a":0x1000,"\u0073\u0076":0x001D,"\u0073\u0076\u002dA\u0058":0x1000,"\u0073\u0076\u002dF\u0049":0x081D,"\u0073\u0076\u002dS\u0045":0x041D,"\u0073\u0079\u0072":0x005A,"\u0073\u0079\u0072\u002d\u0053\u0059":0x045A,"\u0073\u0068\u0069":0x1000,"\u0073\u0068\u0069\u002d\u0054\u0066\u006e\u0067":0x1000,"s\u0068\u0069\u002d\u0054\u0066\u006e\u0067\u002d\u004d\u0041":0x1000,"\u0073\u0068\u0069\u002d\u004c\u0061\u0074\u006e":0x1000,"s\u0068\u0069\u002d\u004c\u0061\u0074\u006e\u002d\u004d\u0041":0x1000,"\u0064\u0061\u0076":0x1000,"\u0064\u0061\u0076\u002d\u004b\u0045":0x1000,"\u0074\u0067":0x0028,"\u0074g\u002d\u0043\u0079\u0072\u006c":0x7C28,"\u0074\u0067\u002d\u0043\u0079\u0072\u006c\u002d\u0054\u006a":0x0428,"\u0074\u007a\u006d":0x005F,"\u0074\u007a\u006d\u002d\u004c\u0061\u0074\u006e":0x7C5F,"t\u007a\u006d\u002d\u004c\u0061\u0074\u006e\u002d\u0044\u005a":0x085F,"\u0074\u0061":0x0049,"\u0074\u0061\u002dI\u004e":0x0449,"\u0074\u0061\u002dM\u0059":0x1000,"\u0074\u0061\u002dS\u0047":0x1000,"\u0074\u0061\u002dL\u004b":0x0849,"\u0074\u0077\u0071":0x1000,"\u0074\u0077\u0071\u002d\u004e\u0045":0x1000,"\u0074\u0074":0x0044,"\u0074\u0074\u002dR\u0055":0x0444,"\u0074\u0065":0x004A,"\u0074\u0065\u002dI\u004e":0x044A,"\u0074\u0065\u006f":0x1000,"\u0074\u0065\u006f\u002d\u004b\u0045":0x1000,"\u0074\u0065\u006f\u002d\u0055\u0047":0x1000,"\u0074\u0068":0x001E,"\u0074\u0068\u002dT\u0048":0x041E,"\u0062\u006f":0x0051,"\u0062\u006f\u002dI\u004e":0x1000,"\u0062\u006f\u002dC\u004e":0x0451,"\u0074\u0069\u0067":0x1000,"\u0074\u0069\u0067\u002d\u0045\u0052":0x1000,"\u0074\u0069":0x0073,"\u0074\u0069\u002dE\u0052":0x0873,"\u0074\u0069\u002dE\u0054":0x0473,"\u0074\u006f":0x1000,"\u0074\u006f\u002dT\u004f":0x1000,"\u0074\u0073":0x0031,"\u0074\u0073\u002dZ\u0041":0x0431,"\u0074\u0072":0x001F,"\u0074\u0072\u002dC\u0059":0x1000,"\u0074\u0072\u002dT\u0052":0x041F,"\u0074\u006b":0x0042,"\u0074\u006b\u002dT\u004d":0x0442,"\u0075\u006b":0x0022,"\u0075\u006b\u002dU\u0041":0x0422,"\u0068\u0073\u0062":0x002E,"\u0068\u0073\u0062\u002d\u0044\u0045":0x042E,"\u0075\u0072":0x0020,"\u0075\u0072\u002dI\u004e":0x0820,"\u0075\u0067":0x0080,"\u0075\u0067\u002dC\u004e":0x0480,"\u0075z\u002d\u0041\u0072\u0061\u0062":0x1000,"\u0075\u007a\u002d\u0041\u0072\u0061\u0062\u002d\u0041\u0046":0x1000,"\u0075z\u002d\u0043\u0079\u0072\u006c":0x7843,"\u0075\u007a\u002d\u0043\u0079\u0072\u006c\u002d\u0055\u005a":0x0843,"\u0075\u007a":0x0043,"\u0075z\u002d\u004c\u0061\u0074\u006e":0x7C43,"\u0075\u007a\u002d\u004c\u0061\u0074\u006e\u002d\u0055\u005a":0x0443,"\u0076\u0061\u0069":0x1000,"\u0076\u0061\u0069\u002d\u0056\u0061\u0069\u0069":0x1000,"v\u0061\u0069\u002d\u0056\u0061\u0069\u0069\u002d\u004c\u0052":0x1000,"v\u0061\u0069\u002d\u004c\u0061\u0074\u006e\u002d\u004c\u0052":0x1000,"\u0076\u0061\u0069\u002d\u004c\u0061\u0074\u006e":0x1000,"\u0063\u0061\u002d\u0045\u0053\u002d\u0076\u0061\u006ce\u006e\u0063\u0069\u0061":0x0803,"\u0076\u0065":0x0033,"\u0076\u0065\u002dZ\u0041":0x0433,"\u0076\u0069":0x002A,"\u0076\u0069\u002dV\u004e":0x042A,"\u0076\u006f":0x1000,"\u0076\u006f\u002d\u0030\u0030\u0031":0x1000,"\u0076\u0075\u006e":0x1000,"\u0076\u0075\u006e\u002d\u0054\u005a":0x1000,"\u0077\u0061\u0065":0x1000,"\u0077\u0061\u0065\u002d\u0043\u0048":0x1000,"\u0063\u0079":0x0052,"\u0063\u0079\u002dG\u0042":0x0452,"\u0077\u0061\u006c":0x1000,"\u0077\u0061\u006c\u002d\u0045\u0054":0x1000,"\u0077\u006f":0x0088,"\u0077\u006f\u002dS\u004e":0x0488,"\u0078\u0068":0x0034,"\u0078\u0068\u002dZ\u0041":0x0434,"\u0079\u0061\u0076":0x1000,"\u0079\u0061\u0076\u002d\u0043\u004d":0x1000,"\u0069\u0069":0x0078,"\u0069\u0069\u002dC\u004e":0x0478,"\u0079\u0069\u002d\u0030\u0030\u0031":0x043D,"\u0079\u006f":0x006A,"\u0079\u006f\u002dB\u004a":0x1000,"\u0079\u006f\u002dN\u0047":0x046A,"\u0064\u006a\u0065":0x1000,"\u0064\u006a\u0065\u002d\u004e\u0045":0x1000,"\u007a\u0075":0x0035,"\u007a\u0075\u006c\u0075":0x0435};
const (_bgdg ="\u006di\u006e\u006f\u0072\u0046\u006f\u006et";_egfce ="\u006da\u006a\u006f\u0072\u0046\u006f\u006et";_dbcc ="\u006d\u0061\u006a\u006f\u0072\u0045\u0061\u0073\u0074\u0041\u0073\u0069a\u0046\u006f\u006e\u0074";_egba ="\u006d\u0069\u006e\u006f\u0072\u0045\u0061\u0073\u0074\u0041\u0073\u0069a\u0046\u006f\u006e\u0074";
);func (_cccg *convertContext )combinePPrWithStyles (_aedb *_bfc .CT_PPr )(*_bfc .CT_PPr ,*prefix ){if _aedb ==nil {return nil ,nil ;};if _aedb !=nil &&_aedb .PStyle !=nil {_egeg ,_egbgf :=_cccg .getStyleProps (_aedb .PStyle .ValAttr ,_fca .Style {});_aedb =_cfgfd (_aedb ,_egeg ,_egbgf );
};var _eaag *prefix ;if _aedb !=nil &&_aedb .NumPr !=nil {if _dabaf ,_eeg :=_aedb .NumPr .Ilvl ,_aedb .NumPr .NumId ;_eeg !=nil {var _baac int64 ;if _dabaf !=nil {_baac =_dabaf .ValAttr ;};if _baaa :=_cccg ._edega .GetNumberingLevelByIds (_eeg .ValAttr ,_baac ).X ();
_baaa !=nil {_aedb =_cfgfd (_aedb ,_baaa .PPr ,_baaa .RPr );if _aedb .PStyle !=nil &&_aedb .NumPr .Ilvl !=nil {if _ ,_ceabdb :=_cccg ._agcd [_aedb .PStyle .ValAttr ];!_ceabdb {_cccg ._agcd [_aedb .PStyle .ValAttr ]=map[int64 ]*_bfc .CT_Ind {};};if _dcagb ,_gfga :=_cccg ._agcd [_aedb .PStyle .ValAttr ][_baac ];
_gfga {_aedb .Ind =_dcagb ;}else {_cccg ._agcd [_aedb .PStyle .ValAttr ][_baac ]=_aedb .Ind ;};};if _agec :=_baaa .NumFmt ;_agec !=nil {if _dbfd :=_agec .ValAttr ;_dbfd !=_bfc .ST_NumberFormatNone &&_dbfd !=_bfc .ST_NumberFormatCustom {var _fbbf []float64 ;
if _efagb :=_aedb .Tabs ;_efagb !=nil &&len (_efagb .Tab )!=0{for _ ,_aabc :=range _efagb .Tab {_fbbf =append (_fbbf ,_ef .PointsFromTwips (*_aabc .PosAttr .Int64 ));};};_eaag =&prefix {_bfgb :_fbbf ,_fgdbb :true };if _dbfd ==_bfc .ST_NumberFormatBullet {if _dfdf :=_baaa .LvlText ;
_dfdf !=nil {if _efeg :=_dfdf .ValAttr ;_efeg !=nil &&*_efeg !=""{_eaag ._bdee =*_efeg ;_eaag ._cffd =true ;};};}else {_cfcd ,_cfece :=_eeg .ValAttr ,_dabaf .ValAttr ;if _ ,_gfadg :=_cccg ._fdfg [_cfcd ];!_gfadg {_cccg ._fdfg [_cfcd ]=map[int64 ]int64 {};
};if _ ,_fdbaf :=_cccg ._fdfg [_cfcd ][_cfece ];!_fdbaf {_cccg ._fdfg [_cfcd ][_cfece ]=1;if _gdgf :=_baaa .Start ;_gdgf !=nil {_cccg ._fdfg [_cfcd ][_cfece ]=_gdgf .ValAttr ;};};if _ ,_fgde :=_cccg ._fdfg [_cfcd ][_cfece +1];_fgde {_cccg ._fdfg [_cfcd ][_cfece +1]=1;
};_bcac :=_cccg ._fdfg [_cfcd ][_cfece ];_gfgac :=_af .FormatNumberingText (int64 (_bcac ),_baaa .IlvlAttr ,*_baaa .LvlText .ValAttr ,_baaa .NumFmt ,_cccg ._fdfg [_cfcd ]);_cccg ._fdfg [_cfcd ][_cfece ]++;_eaag ._bdee =_gfgac ;};};};};};};return _aedb ,_eaag ;
};func (_bgfd *convertContext )getPageAccessiblePart ()float64 {_ebag :=_bgfd ._fadc ._ebc .Bottom -_bgfd ._fadc ._fa -_bgfd ._cfeb ._agb .Top -_bgfd ._cfeb ._agb .Bottom ;return _ebag ;};func _dfef (_fcad string )bool {for _ ,_cgdg :=range _fcad {if _cgdg > 255{return false ;
};};return true ;};func (_bdec *convertContext )addAbsoluteTable (_ageb *_bfc .CT_Tbl ){_dcag :=_ageb .TblGrid ;if _dcag ==nil {return ;};_bdc :=len (_dcag .GridCol );_cbafee :=false ;if _bdc ==0{if len (_ageb .EG_ContentRowContent )==0{return ;};_eg .Log .Debug ("\u004d\u0069\u0073\u0073\u0069\u006e\u0067\u0020\u0067\u0072\u0069\u0064\u0043\u006f\u006c\u0020\u0065\u006ce\u006d\u0065\u006e\u0074\u002c\u0020\u0063r\u0065\u0061\u0074\u0069\u006e\u0067\u0020\u0067\u0072\u0069\u0064C\u006f\u006c\u0020\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u002e");
_cfab :=_ageb .EG_ContentRowContent [0];if len (_cfab .ContentRowContentChoice .Tr )< 1{return ;};_ffge :=_cfab .ContentRowContentChoice .Tr [0];if len (_ffge .EG_ContentCellContent )< 1{return ;};_fdab :=0;if _ageb .TblPr !=nil &&_ageb .TblPr .TblW !=nil {if _cge :=_ageb .TblPr .TblW .WAttr ;
_cge !=nil {switch _ageb .TblPr .TblW .TypeAttr {case _bfc .ST_TblWidthPct ,_bfc .ST_TblWidthDxa :if _cge .ST_DecimalNumberOrPercent !=nil {if _cge .ST_DecimalNumberOrPercent .ST_UnqualifiedPercentage !=nil {_fdab =int (*_cge .ST_DecimalNumberOrPercent .ST_UnqualifiedPercentage );
};};};};};var _bede []*_bfc .CT_TblGridCol ;for _ ,_cfgg :=range _ffge .EG_ContentCellContent {if _bade :=_cfgg .ContentCellContentChoice .Tc ;len (_bade )> 0{if _fdaa :=_bade [0];_fdaa !=nil {_feadd :=_bfc .NewCT_TblGridCol ();if _fdaa .TcPr !=nil {if _bafd :=_fdaa .TcPr .TcW ;
_bafd !=nil {if _bafd .WAttr !=nil {if _dfgd :=_bafd .WAttr .ST_DecimalNumberOrPercent ;_dfgd !=nil {if _aacd :=_dfgd .ST_UnqualifiedPercentage ;_aacd !=nil {_fffe :=uint64 (*_aacd );_fdaac :=&_bc .ST_TwipsMeasure {};_fdaac .ST_UnsignedDecimalNumber =&_fffe ;
_feadd .WAttr =_fdaac ;};};};};_bede =append (_bede ,_feadd );if _fdaa .TcPr .GridSpan !=nil {for _dbff :=int (_fdaa .TcPr .GridSpan .ValAttr )-1;_dbff > 0;_dbff --{_ecfd :=_bfc .NewCT_TblGridCol ();_bede =append (_bede ,_ecfd );};};}else {_bede =append (_bede ,_feadd );
};};};};_ebaa :=uint64 (_fdab /len (_bede ));for _ ,_ffad :=range _bede {if _ffad .WAttr ==nil {_bafe :=&_bc .ST_TwipsMeasure {};_bafe .ST_UnsignedDecimalNumber =&_ebaa ;_ffad .WAttr =_bafe ;_cbafee =true ;};};_dcag .GridCol =_bede ;_bdc =len (_bede );
};_bged :=[]float64 {};_gbdb :=[]float64 {};_gcee :=0.0;for _ ,_agea :=range _dcag .GridCol {_cebda :=0.0;if _agea .WAttr .ST_UnsignedDecimalNumber !=nil {_cebda =_ef .PointsFromTwips (int64 (*_agea .WAttr .ST_UnsignedDecimalNumber ));};_bged =append (_bged ,_cebda );
_gcee +=_cebda ;};for _bfgf :=0;_bfgf < _bdc ;_bfgf ++{_gbdb =append (_gbdb ,_bged [_bfgf ]/_gcee );};_efgc ,_cdc ,_fdca :=_ddga (_bdec ._edega ,_ageb .TblPr );var _fgfa []*_bfc .CT_TblStylePr ;if _efgc .TblStyle !=nil {_fgfa =_cdfbg (_bdec ._edega ,_efgc .TblStyle .ValAttr );
};if _efgc .TblLayout !=nil &&_efgc .TblLayout .TypeAttr ==_bfc .ST_TblLayoutTypeFixed {_cbafee =false ;};_bdec .renderTableRows (_ageb ,_bdc ,_cbafee ,_gbdb ,_efgc ,_fgfa ,_cdc ,_fdca ,_gcee ,nil );};func _ccgd (_ecafe *_fca .Document ,_dcaeb *_bfc .CT_RPr ,_eaad *_bfc .CT_PPr )*_bfc .CT_RPr {var _bdfa ,_gede *_bfc .CT_RPr ;
if _dcaeb ==nil {_dcaeb =_bfc .NewCT_RPr ();};var _bcff *_bfc .CT_ParaRPr ;if _eaad !=nil &&_eaad .RPr !=nil {_bcff =_eaad .RPr ;};if _bcff ==nil {_bcff =_bfc .NewCT_ParaRPr ();};if _dcaeb .RStyle !=nil {_egea :=_ecafe .GetStyleByID (_dcaeb .RStyle .ValAttr );
if _aeff :=_egea .X ();_aeff !=nil {_bdfa =_aeff .RPr ;};};if _bdfa ==nil {_bdfa =_bfc .NewCT_RPr ();};if _bcff .RStyle !=nil {_aagf :=_ecafe .GetStyleByID (_bcff .RStyle .ValAttr );if _fcdda :=_aagf .X ();_fcdda !=nil {_gede =_fcdda .RPr ;if _fcdda .QFormat !=nil &&_dgba (_fcdda .QFormat ){return _gede ;
};};};if _gede ==nil {_gede =_bfc .NewCT_RPr ();};if _dcaeb .Color ==nil {if _bdfa .Color !=nil {_dcaeb .Color =_bdfa .Color ;}else if _bcff .Color !=nil {_dcaeb .Color =_bcff .Color ;}else if _gede .Color !=nil {_dcaeb .Color =_gede .Color ;};};if _dcaeb .Spacing ==nil {if _bdfa .Spacing !=nil {_dcaeb .Spacing =_bdfa .Spacing ;
}else if _bcff .Spacing !=nil {_dcaeb .Spacing =_bcff .Spacing ;}else if _gede .Spacing !=nil {_dcaeb .Spacing =_gede .Spacing ;};};if _dcaeb .Sz ==nil {if _bdfa .Sz !=nil {_dcaeb .Sz =_bdfa .Sz ;}else if _bcff .Sz !=nil {_dcaeb .Sz =_bcff .Sz ;}else if _gede .Sz !=nil {_dcaeb .Sz =_gede .Sz ;
};};if _dcaeb .SzCs ==nil {if _bdfa .SzCs !=nil {_dcaeb .SzCs =_bdfa .SzCs ;}else if _bcff .SzCs !=nil {_dcaeb .SzCs =_bcff .SzCs ;}else if _gede .SzCs !=nil {_dcaeb .SzCs =_gede .SzCs ;};};if _dcaeb .B ==nil {if _bdfa .B !=nil {_dcaeb .B =_bdfa .B ;}else if _bcff .B !=nil {_dcaeb .B =_bcff .B ;
}else if _gede .B !=nil {_dcaeb .B =_gede .B ;};};if _dcaeb .I ==nil {if _bdfa .I !=nil {_dcaeb .I =_bdfa .I ;}else if _bcff .I !=nil {_dcaeb .I =_bcff .I ;}else if _gede .I !=nil {_dcaeb .I =_gede .I ;};};if _dcaeb .U ==nil {if _bdfa .U !=nil {_dcaeb .U =_bdfa .U ;
}else if _bcff .U !=nil {_dcaeb .U =_bcff .U ;}else if _gede .U !=nil {_dcaeb .U =_gede .U ;};};if _dcaeb .RFonts ==nil {if _bdfa .RFonts !=nil {_dcaeb .RFonts =_bdfa .RFonts ;}else if _bcff .RFonts !=nil {_dcaeb .RFonts =_bcff .RFonts ;}else if _gede .RFonts !=nil {_dcaeb .RFonts =_gede .RFonts ;
};};if _dcaeb .VertAlign ==nil {if _bdfa .VertAlign !=nil {_dcaeb .VertAlign =_bdfa .VertAlign ;}else if _bcff .VertAlign !=nil {_dcaeb .VertAlign =_bcff .VertAlign ;}else if _gede .VertAlign !=nil {_dcaeb .VertAlign =_gede .VertAlign ;};};if _dcaeb .Caps ==nil {if _bdfa .Caps !=nil {_dcaeb .Caps =_bdfa .Caps ;
}else if _bcff .Caps !=nil {_dcaeb .Caps =_bcff .Caps ;}else if _gede .Caps !=nil {_dcaeb .Caps =_gede .Caps ;};};if _dcaeb .SmallCaps ==nil {if _bdfa .SmallCaps !=nil {_dcaeb .SmallCaps =_bdfa .SmallCaps ;}else if _bcff .SmallCaps !=nil {_dcaeb .SmallCaps =_bcff .SmallCaps ;
}else if _gede .SmallCaps !=nil {_dcaeb .SmallCaps =_gede .SmallCaps ;};};if _dcaeb .Bdr ==nil {if _bdfa .Bdr !=nil {_dcaeb .Bdr =_bdfa .Bdr ;}else if _bcff .Bdr !=nil {_dcaeb .Bdr =_bcff .Bdr ;}else if _gede .Bdr !=nil {_dcaeb .Bdr =_gede .Bdr ;};};if _dcaeb .Shd ==nil {if _bdfa .Shd !=nil {_dcaeb .Shd =_bdfa .Shd ;
}else if _bcff .Shd !=nil {_dcaeb .Shd =_bcff .Shd ;}else if _gede .Shd !=nil {_dcaeb .Shd =_gede .Shd ;};};return _dcaeb ;};func (_cdf *convertContext )addCurrentParagraphToCurrentPage (){_dbbcg :=_cdf ._cfeb ._bdd +_cdf ._cfeb ._agb .Top +_cdf ._cfeb ._aee +_cdf ._cfeb ._agb .Bottom ;
_gcf :=0.0;for _ ,_bbde :=range _cdf ._cfeb ._aeb {if _bbde ._ba ==0&&_bbde ._dc .Bottom > _gcf &&_bbde ._dc .Top >=_cdf ._cfeb ._bdd {_gcf =_bbde ._dc .Bottom ;};};if _gcf > _dbbcg {_dbbcg =_gcf ;};_cdf ._fadc ._fa =_dbbcg ;_cdf ._fadc ._efe =append (_cdf ._fadc ._efe ,_cdf ._cfeb ._bdda ...);
_cdf ._fadc ._cb =append (_cdf ._fadc ._cb ,_cdf ._cfeb ._egb ...);_cdf ._fadc ._dg =append (_cdf ._fadc ._dg ,_cdf ._cfeb ._ad ...);_cdf ._fadc ._dgd =append (_cdf ._fadc ._dgd ,_cdf ._cfeb ._gd ...);_cdf ._fadc ._cfc =append (_cdf ._fadc ._cfc ,_cdf ._cfeb ._aeb ...);
_cdf ._fadc ._gc =append (_cdf ._fadc ._gc ,_cdf ._cfeb );_cdf .adjustRightBoundOfLastSpan ();_cdf .alignParagraph ();if len (_cdf ._fadc ._fe )==0&&len (_cdf ._cfeb ._edg )> 0{_cdf ._fadc ._ebc .Bottom -=_dae ;};_cdf ._fadc ._fe =append (_cdf ._fadc ._fe ,_cdf ._cfeb ._edg ...);
_cdf ._fadc ._ebc .Bottom -=_cdf ._cfeb ._aeg ;};