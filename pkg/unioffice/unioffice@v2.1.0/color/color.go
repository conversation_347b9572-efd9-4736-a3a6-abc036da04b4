//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

// Package color provides color handling structures and functions for use across
// all of the document types.
package color ;import (_cb "fmt";_d "github.com/unidoc/unioffice/v2";_c "github.com/unidoc/unioffice/v2/schema/soo/wml";_dg "github.com/unidoc/unipdf/v3/creator";);var Orange =Color {0xFF,0xA5,0x00,255,false };var DarkSlateBlue =Color {0x48,0x3D,0x8B,255,false };
var NavajoWhite =Color {0xFF,0xDE,0xAD,255,false };var DarkSeaGreen =Color {0x8F,0xBC,0x8F,255,false };var DarkTurquoise =Color {0x00,0xCE,0xD1,255,false };var MistyRose =Color {0xFF,0xE4,0xE1,255,false };var LemonChiffon =Color {0xFF,0xFA,0xCD,255,false };
var SkyBlue =Color {0x87,0xCE,0xEB,255,false };var Ivory =Color {0xFF,0xFF,0xF0,255,false };var PeachPuff =Color {0xFF,0xDA,0xB9,255,false };var MediumAquaMarine =Color {0x66,0xCD,0xAA,255,false };var LightCyan =Color {0xE0,0xFF,0xFF,255,false };var Thistle =Color {0xD8,0xBF,0xD8,255,false };
var MidnightBlue =Color {0x19,0x19,0x70,255,false };var SeaGreen =Color {0x2E,0x8B,0x57,255,false };var MediumSeaGreen =Color {0x3C,0xB3,0x71,255,false };var RebeccaPurple =Color {0x66,0x33,0x99,255,false };var Olive =Color {0x80,0x80,0x00,255,false };
var RosyBrown =Color {0xBC,0x8F,0x8F,255,false };var Fuchsia =Color {0xFF,0x00,0xFF,255,false };var DarkRed =Color {0x8B,0x00,0x00,255,false };var Chocolate =Color {0xD2,0x69,0x1E,255,false };var LightSlateGrey =Color {0x77,0x88,0x99,255,false };var MediumSlateBlue =Color {0x7B,0x68,0xEE,255,false };
var Magenta =Color {0xFF,0x00,0xFF,255,false };var WhiteSmoke =Color {0xF5,0xF5,0xF5,255,false };var Beige =Color {0xF5,0xF5,0xDC,255,false };var DarkOrange =Color {0xFF,0x8C,0x00,255,false };var BurlyWood =Color {0xDE,0xB8,0x87,255,false };var Silver =Color {0xC0,0xC0,0xC0,255,false };
var ColorMap =map[string ]Color {"\u0061l\u0069\u0063\u0065\u0062\u006c\u0075e":AliceBlue ,"\u0061\u006e\u0074i\u0071\u0075\u0065\u0077\u0068\u0069\u0074\u0065":AntiqueWhite ,"\u0061\u0071\u0075\u0061":Aqua ,"\u0061\u0071\u0075\u0061\u006d\u0061\u0072\u0069\u006e\u0065":Aquamarine ,"\u0061\u007a\u0075r\u0065":Azure ,"\u0062\u0065\u0069g\u0065":Beige ,"\u0062\u0069\u0073\u0071\u0075\u0065":Bisque ,"\u0062\u006c\u0061c\u006b":Black ,"\u0062\u006c\u0061\u006e\u0063\u0068\u0065\u0064\u0061l\u006d\u006f\u006e\u0064":BlanchedAlmond ,"\u0062\u006c\u0075\u0065":Blue ,"\u0062\u006c\u0075\u0065\u0076\u0069\u006f\u006c\u0065\u0074":BlueViolet ,"\u0062\u0072\u006fw\u006e":Brown ,"\u0062u\u0072\u006c\u0079\u0077\u006f\u006fd":BurlyWood ,"\u0063a\u0064\u0065\u0074\u0062\u006c\u0075e":CadetBlue ,"\u0063\u0068\u0061\u0072\u0074\u0072\u0065\u0075\u0073\u0065":Chartreuse ,"\u0063h\u006f\u0063\u006f\u006c\u0061\u0074e":Chocolate ,"\u0063\u006f\u0072a\u006c":Coral ,"\u0063\u006f\u0072\u006e\u0066\u006c\u006f\u0077\u0065r\u0062\u006c\u0075\u0065":CornflowerBlue ,"\u0063\u006f\u0072\u006e\u0073\u0069\u006c\u006b":Cornsilk ,"\u0063r\u0069\u006d\u0073\u006f\u006e":Crimson ,"\u0063\u0079\u0061\u006e":Cyan ,"\u0064\u0061\u0072\u006b\u0062\u006c\u0075\u0065":DarkBlue ,"\u0064\u0061\u0072\u006b\u0063\u0079\u0061\u006e":DarkCyan ,"\u0064\u0061\u0072\u006b\u0067\u006f\u006c\u0064\u0065\u006e\u0072\u006f\u0064":DarkGoldenRod ,"\u0064\u0061\u0072\u006b\u0067\u0072\u0061\u0079":DarkGray ,"\u0064\u0061\u0072\u006b\u0067\u0072\u0065\u0079":DarkGrey ,"\u0064a\u0072\u006b\u0067\u0072\u0065\u0065n":DarkGreen ,"\u0064a\u0072\u006b\u006b\u0068\u0061\u006bi":DarkKhaki ,"d\u0061\u0072\u006b\u006d\u0061\u0067\u0065\u006e\u0074\u0061":DarkMagenta ,"\u0064\u0061\u0072\u006b\u006f\u006c\u0069\u0076\u0065g\u0072\u0065\u0065\u006e":DarkOliveGreen ,"\u0064\u0061\u0072\u006b\u006f\u0072\u0061\u006e\u0067\u0065":DarkOrange ,"\u0064\u0061\u0072\u006b\u006f\u0072\u0063\u0068\u0069\u0064":DarkOrchid ,"\u0064a\u0072\u006b\u0072\u0065\u0064":DarkRed ,"\u0064\u0061\u0072\u006b\u0073\u0061\u006c\u006d\u006f\u006e":DarkSalmon ,"\u0064\u0061\u0072k\u0073\u0065\u0061\u0067\u0072\u0065\u0065\u006e":DarkSeaGreen ,"\u0064\u0061\u0072\u006b\u0073\u006c\u0061\u0074\u0065\u0062\u006c\u0075\u0065":DarkSlateBlue ,"\u0064\u0061\u0072\u006b\u0073\u006c\u0061\u0074\u0065\u0067\u0072\u0061\u0079":DarkSlateGray ,"\u0064\u0061\u0072\u006b\u0073\u006c\u0061\u0074\u0065\u0067\u0072\u0065\u0079":DarkSlateGrey ,"\u0064\u0061\u0072\u006b\u0074\u0075\u0072\u0071\u0075\u006f\u0069\u0073\u0065":DarkTurquoise ,"\u0064\u0061\u0072\u006b\u0076\u0069\u006f\u006c\u0065\u0074":DarkViolet ,"\u0064\u0065\u0065\u0070\u0070\u0069\u006e\u006b":DeepPink ,"d\u0065\u0065\u0070\u0073\u006b\u0079\u0062\u006c\u0075\u0065":DeepSkyBlue ,"\u0064i\u006d\u0067\u0072\u0061\u0079":DimGray ,"\u0064i\u006d\u0067\u0072\u0065\u0079":DimGrey ,"\u0064\u006f\u0064\u0067\u0065\u0072\u0062\u006c\u0075\u0065":DodgerBlue ,"\u0066i\u0072\u0065\u0062\u0072\u0069\u0063k":FireBrick ,"f\u006c\u006f\u0072\u0061\u006c\u0077\u0068\u0069\u0074\u0065":FloralWhite ,"f\u006f\u0072\u0065\u0073\u0074\u0067\u0072\u0065\u0065\u006e":ForestGreen ,"\u0066u\u0063\u0068\u0073\u0069\u0061":Fuchsia ,"\u0067a\u0069\u006e\u0073\u0062\u006f\u0072o":Gainsboro ,"\u0067\u0068\u006f\u0073\u0074\u0077\u0068\u0069\u0074\u0065":GhostWhite ,"\u0067\u006f\u006c\u0064":Gold ,"\u0067o\u006c\u0064\u0065\u006e\u0072\u006fd":GoldenRod ,"\u0067\u0072\u0061\u0079":Gray ,"\u0067\u0072\u0065e\u006e":Green ,"g\u0072\u0065\u0065\u006e\u0079\u0065\u006c\u006c\u006f\u0077":GreenYellow ,"\u0068\u006f\u006e\u0065\u0079\u0064\u0065\u0077":HoneyDew ,"\u0068o\u0074\u0070\u0069\u006e\u006b":HotPink ,"\u0069n\u0064\u0069\u0061\u006e\u0072\u0065d":IndianRed ,"\u0069\u006e\u0064\u0069\u0067\u006f":Indigo ,"\u0069\u0076\u006fr\u0079":Ivory ,"\u006b\u0068\u0061k\u0069":Khaki ,"\u006c\u0061\u0076\u0065\u006e\u0064\u0065\u0072":Lavender ,"\u006c\u0061\u0076\u0065\u006e\u0064\u0065\u0072\u0062\u006c\u0075\u0073\u0068":LavenderBlush ,"\u006ca\u0077\u006e\u0067\u0072\u0065\u0065n":LawnGreen ,"\u006c\u0065\u006do\u006e\u0063\u0068\u0069\u0066\u0066\u006f\u006e":LemonChiffon ,"\u006ci\u0067\u0068\u0074\u0062\u006c\u0075e":LightBlue ,"\u006c\u0069\u0067\u0068\u0074\u0063\u006f\u0072\u0061\u006c":LightCoral ,"\u006ci\u0067\u0068\u0074\u0063\u0079\u0061n":LightCyan ,"l\u0069g\u0068\u0074\u0067\u006f\u006c\u0064\u0065\u006er\u006f\u0064\u0079\u0065ll\u006f\u0077":LightGoldenRodYellow ,"\u006ci\u0067\u0068\u0074\u0067\u0072\u0061y":LightGray ,"\u006ci\u0067\u0068\u0074\u0067\u0072\u0065y":LightGrey ,"\u006c\u0069\u0067\u0068\u0074\u0067\u0072\u0065\u0065\u006e":LightGreen ,"\u006ci\u0067\u0068\u0074\u0070\u0069\u006ek":LightPink ,"l\u0069\u0067\u0068\u0074\u0073\u0061\u006c\u006d\u006f\u006e":LightSalmon ,"\u006c\u0069\u0067\u0068\u0074\u0073\u0065\u0061\u0067\u0072\u0065\u0065\u006e":LightSeaGreen ,"\u006c\u0069\u0067h\u0074\u0073\u006b\u0079\u0062\u006c\u0075\u0065":LightSkyBlue ,"\u006c\u0069\u0067\u0068\u0074\u0073\u006c\u0061\u0074e\u0067\u0072\u0061\u0079":LightSlateGray ,"\u006c\u0069\u0067\u0068\u0074\u0073\u006c\u0061\u0074e\u0067\u0072\u0065\u0079":LightSlateGrey ,"\u006c\u0069\u0067\u0068\u0074\u0073\u0074\u0065\u0065l\u0062\u006c\u0075\u0065":LightSteelBlue ,"l\u0069\u0067\u0068\u0074\u0079\u0065\u006c\u006c\u006f\u0077":LightYellow ,"\u006c\u0069\u006d\u0065":Lime ,"\u006ci\u006d\u0065\u0067\u0072\u0065\u0065n":LimeGreen ,"\u006c\u0069\u006ee\u006e":Linen ,"\u006da\u0067\u0065\u006e\u0074\u0061":Magenta ,"\u006d\u0061\u0072\u006f\u006f\u006e":Maroon ,"\u006d\u0065d\u0069\u0075\u006da\u0071\u0075\u0061\u006d\u0061\u0072\u0069\u006e\u0065":MediumAquaMarine ,"\u006d\u0065\u0064\u0069\u0075\u006d\u0062\u006c\u0075\u0065":MediumBlue ,"\u006d\u0065\u0064i\u0075\u006d\u006f\u0072\u0063\u0068\u0069\u0064":MediumOrchid ,"\u006d\u0065\u0064i\u0075\u006d\u0070\u0075\u0072\u0070\u006c\u0065":MediumPurple ,"\u006d\u0065\u0064\u0069\u0075\u006d\u0073\u0065\u0061g\u0072\u0065\u0065\u006e":MediumSeaGreen ,"\u006de\u0064i\u0075\u006d\u0073\u006c\u0061\u0074\u0065\u0062\u006c\u0075\u0065":MediumSlateBlue ,"\u006d\u0065\u0064\u0069\u0075\u006d\u0073\u0070\u0072\u0069\u006e\u0067g\u0072\u0065\u0065\u006e":MediumSpringGreen ,"\u006de\u0064i\u0075\u006d\u0074\u0075\u0072\u0071\u0075\u006f\u0069\u0073\u0065":MediumTurquoise ,"\u006de\u0064i\u0075\u006d\u0076\u0069\u006f\u006c\u0065\u0074\u0072\u0065\u0064":MediumVioletRed ,"\u006d\u0069\u0064n\u0069\u0067\u0068\u0074\u0062\u006c\u0075\u0065":MidnightBlue ,"\u006di\u006e\u0074\u0063\u0072\u0065\u0061m":MintCream ,"\u006di\u0073\u0074\u0079\u0072\u006f\u0073e":MistyRose ,"\u006d\u006f\u0063\u0063\u0061\u0073\u0069\u006e":Moccasin ,"n\u0061\u0076\u0061\u006a\u006f\u0077\u0068\u0069\u0074\u0065":NavajoWhite ,"\u006e\u0061\u0076\u0079":Navy ,"\u006fl\u0064\u006c\u0061\u0063\u0065":OldLace ,"\u006f\u006c\u0069v\u0065":Olive ,"\u006fl\u0069\u0076\u0065\u0064\u0072\u0061b":OliveDrab ,"\u006f\u0072\u0061\u006e\u0067\u0065":Orange ,"\u006fr\u0061\u006e\u0067\u0065\u0072\u0065d":OrangeRed ,"\u006f\u0072\u0063\u0068\u0069\u0064":Orchid ,"\u0070\u0061\u006c\u0065\u0067\u006f\u006c\u0064\u0065\u006e\u0072\u006f\u0064":PaleGoldenRod ,"\u0070a\u006c\u0065\u0067\u0072\u0065\u0065n":PaleGreen ,"\u0070\u0061\u006c\u0065\u0074\u0075\u0072\u0071\u0075\u006f\u0069\u0073\u0065":PaleTurquoise ,"\u0070\u0061\u006c\u0065\u0076\u0069\u006f\u006c\u0065\u0074\u0072\u0065\u0064":PaleVioletRed ,"\u0070\u0061\u0070\u0061\u0079\u0061\u0077\u0068\u0069\u0070":PapayaWhip ,"\u0070e\u0061\u0063\u0068\u0070\u0075\u0066f":PeachPuff ,"\u0070\u0065\u0072\u0075":Peru ,"\u0070\u0069\u006e\u006b":Pink ,"\u0070\u006c\u0075\u006d":Plum ,"\u0070\u006f\u0077\u0064\u0065\u0072\u0062\u006c\u0075\u0065":PowderBlue ,"\u0070\u0075\u0072\u0070\u006c\u0065":Purple ,"\u0072\u0065\u0062\u0065\u0063\u0063\u0061\u0070\u0075\u0072\u0070\u006c\u0065":RebeccaPurple ,"\u0072\u0065\u0064":Red ,"\u0072o\u0073\u0079\u0062\u0072\u006f\u0077n":RosyBrown ,"\u0072o\u0079\u0061\u006c\u0062\u006c\u0075e":RoyalBlue ,"s\u0061\u0064\u0064\u006c\u0065\u0062\u0072\u006f\u0077\u006e":SaddleBrown ,"\u0073\u0061\u006c\u006d\u006f\u006e":Salmon ,"\u0073\u0061\u006e\u0064\u0079\u0062\u0072\u006f\u0077\u006e":SandyBrown ,"\u0073\u0075\u0063c\u0065\u0073\u0073\u0067\u0072\u0065\u0065\u006e":SuccessGreen ,"\u0073\u0065\u0061\u0067\u0072\u0065\u0065\u006e":SeaGreen ,"\u0073\u0065\u0061\u0073\u0068\u0065\u006c\u006c":SeaShell ,"\u0073\u0069\u0065\u006e\u006e\u0061":Sienna ,"\u0073\u0069\u006c\u0076\u0065\u0072":Silver ,"\u0073k\u0079\u0062\u006c\u0075\u0065":SkyBlue ,"\u0073l\u0061\u0074\u0065\u0062\u006c\u0075e":SlateBlue ,"\u0073l\u0061\u0074\u0065\u0067\u0072\u0061y":SlateGray ,"\u0073l\u0061\u0074\u0065\u0067\u0072\u0065y":SlateGrey ,"\u0073\u006e\u006f\u0077":Snow ,"s\u0070\u0072\u0069\u006e\u0067\u0067\u0072\u0065\u0065\u006e":SpringGreen ,"\u0073t\u0065\u0065\u006c\u0062\u006c\u0075e":SteelBlue ,"\u0074\u0061\u006e":Tan ,"\u0074\u0065\u0061\u006c":Teal ,"\u0074h\u0069\u0073\u0074\u006c\u0065":Thistle ,"\u0074\u006f\u006d\u0061\u0074\u006f":Tomato ,"\u0074u\u0072\u0071\u0075\u006f\u0069\u0073e":Turquoise ,"\u0076\u0069\u006f\u006c\u0065\u0074":Violet ,"\u0077\u0068\u0065a\u0074":Wheat ,"\u0077\u0068\u0069t\u0065":White ,"\u0077\u0068\u0069\u0074\u0065\u0073\u006d\u006f\u006b\u0065":WhiteSmoke ,"\u0079\u0065\u006c\u006c\u006f\u0077":Yellow ,"y\u0065\u006c\u006c\u006f\u0077\u0067\u0072\u0065\u0065\u006e":YellowGreen };
var HighlightColorMap =map[string ]_c .ST_HighlightColor {"\u0062\u006c\u0061c\u006b":_c .ST_HighlightColorBlack ,"\u0062\u006c\u0075\u0065":_c .ST_HighlightColorBlue ,"\u0063\u0079\u0061\u006e":_c .ST_HighlightColorCyan ,"\u0067\u0072\u0065e\u006e":_c .ST_HighlightColorGreen ,"\u006da\u0067\u0065\u006e\u0074\u0061":_c .ST_HighlightColorMagenta ,"\u0072\u0065\u0064":_c .ST_HighlightColorRed ,"\u0079\u0065\u006c\u006c\u006f\u0077":_c .ST_HighlightColorYellow ,"\u0077\u0068\u0069t\u0065":_c .ST_HighlightColorWhite ,"\u0064\u0061\u0072\u006b\u0042\u006c\u0075\u0065":_c .ST_HighlightColorDarkBlue ,"\u0064\u0061\u0072\u006b\u0043\u0079\u0061\u006e":_c .ST_HighlightColorDarkCyan ,"\u0064a\u0072\u006b\u0047\u0072\u0065\u0065n":_c .ST_HighlightColorDarkGreen ,"d\u0061\u0072\u006b\u004d\u0061\u0067\u0065\u006e\u0074\u0061":_c .ST_HighlightColorDarkMagenta ,"\u0064a\u0072\u006b\u0052\u0065\u0064":_c .ST_HighlightColorDarkRed ,"\u0064\u0061\u0072\u006b\u0059\u0065\u006c\u006c\u006f\u0077":_c .ST_HighlightColorDarkYellow ,"\u006ci\u0067\u0068\u0074\u0047\u0072\u0061y":_c .ST_HighlightColorLightGray ,"\u0064\u0061\u0072\u006b\u0047\u0072\u0061\u0079":_c .ST_HighlightColorDarkGray };
var DarkGray =Color {0xA9,0xA9,0xA9,255,false };var DodgerBlue =Color {0x1E,0x90,0xFF,255,false };var DarkViolet =Color {0x94,0x00,0xD3,255,false };var ForestGreen =Color {0x22,0x8B,0x22,255,false };var Navy =Color {0x00,0x00,0x80,255,false };var IndianRed =Color {0xCD,0x5C,0x5C,255,false };
var MediumVioletRed =Color {0xC7,0x15,0x85,255,false };var Bisque =Color {0xFF,0xE4,0xC4,255,false };var DeepSkyBlue =Color {0x00,0xBF,0xFF,255,false };var GoldenRod =Color {0xDA,0xA5,0x20,255,false };var DarkGreen =Color {0x00,0x64,0x00,255,false };var LightSalmon =Color {0xFF,0xA0,0x7A,255,false };
var Black =Color {0x00,0x00,0x00,255,false };var HoneyDew =Color {0xF0,0xFF,0xF0,255,false };var Aqua =Color {0x00,0xFF,0xFF,255,false };var LightSeaGreen =Color {0x20,0xB2,0xAA,255,false };var DarkSlateGray =Color {0x2F,0x4F,0x4F,255,false };var Moccasin =Color {0xFF,0xE4,0xB5,255,false };
var LightCoral =Color {0xF0,0x80,0x80,255,false };var LightSkyBlue =Color {0x87,0xCE,0xFA,255,false };var DarkBlue =Color {0x00,0x00,0x8B,255,false };var SteelBlue =Color {0x46,0x82,0xB4,255,false };var Khaki =Color {0xF0,0xE6,0x8C,255,false };var FireBrick =Color {0xB2,0x22,0x22,255,false };
var PowderBlue =Color {0xB0,0xE0,0xE6,255,false };var DarkGoldenRod =Color {0xB8,0x86,0x0B,255,false };var DeepPink =Color {0xFF,0x14,0x93,255,false };var MediumBlue =Color {0x00,0x00,0xCD,255,false };var PaleVioletRed =Color {0xDB,0x70,0x93,255,false };
var SandyBrown =Color {0xF4,0xA4,0x60,255,false };

// RGBA constructs a new RGBA color with a given red, green, blue and alpha
// value.
func RGBA (r ,g ,b ,a uint8 )Color {return Color {r ,g ,b ,a ,false }};var LawnGreen =Color {0x7C,0xFC,0x00,255,false };var Violet =Color {0xEE,0x82,0xEE,255,false };var Lavender =Color {0xE6,0xE6,0xFA,255,false };var LightPink =Color {0xFF,0xB6,0xC1,255,false };
var Cornsilk =Color {0xFF,0xF8,0xDC,255,false };var YellowGreen =Color {0x9A,0xCD,0x32,255,false };var Orchid =Color {0xDA,0x70,0xD6,255,false };var DarkMagenta =Color {0x8B,0x00,0x8B,255,false };var Aquamarine =Color {0x7F,0xFF,0xD4,255,false };

// IsAuto returns true if the color is the 'Auto' type.  If the
// field doesn't support an Auto color, then black is used.
func (_df Color )IsAuto ()bool {return _df ._cbe };var OrangeRed =Color {0xFF,0x45,0x00,255,false };var PapayaWhip =Color {0xFF,0xEF,0xD5,255,false };var Auto =Color {0,0,0,255,true };var Gray =Color {0x80,0x80,0x80,255,false };

// AsRGBAString is used by the various wrappers to return a pointer
// to a string containing a six digit hex RGB value.
func (_b Color )AsRGBAString ()*string {return _d .Stringf ("\u0025\u00302\u0078\u0025\u00302\u0078\u0025\u0030\u0032\u0078\u0025\u0030\u0032\u0078",_b ._ag ,_b ._e ,_b ._ab ,_b ._dd );};var LightGreen =Color {0x90,0xEE,0x90,255,false };var Pink =Color {0xFF,0xC0,0xCB,255,false };
var Snow =Color {0xFF,0xFA,0xFA,255,false };var LightGrey =Color {0xD3,0xD3,0xD3,255,false };var MintCream =Color {0xF5,0xFF,0xFA,255,false };var SuccessGreen =Color {0x00,0xCC,0x00,255,false };var DarkKhaki =Color {0xBD,0xB7,0x6B,255,false };var MediumTurquoise =Color {0x48,0xD1,0xCC,255,false };
var Green =Color {0x00,0x80,0x00,255,false };var DimGray =Color {0x69,0x69,0x69,255,false };var LavenderBlush =Color {0xFF,0xF0,0xF5,255,false };var Teal =Color {0x00,0x80,0x80,255,false };var PaleGreen =Color {0x98,0xFB,0x98,255,false };var Purple =Color {0x80,0x00,0x80,255,false };
var SaddleBrown =Color {0x8B,0x45,0x13,255,false };

// AsRGBString is used by the various wrappers to return a pointer
// to a string containing a six digit hex RGB value.
func (_eb Color )AsRGBString ()*string {return _d .Stringf ("\u0025\u0030\u0032x\u0025\u0030\u0032\u0078\u0025\u0030\u0032\u0078",_eb ._e ,_eb ._ab ,_eb ._dd );};var Indigo =Color {0x4B,0x00,0x82,255,false };var Maroon =Color {0x80,0x00,0x00,255,false };
var LightSlateGray =Color {0x77,0x88,0x99,255,false };var MediumSpringGreen =Color {0x00,0xFA,0x9A,255,false };var MediumPurple =Color {0x93,0x70,0xDB,255,false };var PaleGoldenRod =Color {0xEE,0xE8,0xAA,255,false };var LightGray =Color {0xD3,0xD3,0xD3,255,false };
var Chartreuse =Color {0x7F,0xFF,0x00,255,false };var SlateGrey =Color {0x70,0x80,0x90,255,false };var AliceBlue =Color {0xF0,0xF8,0xFF,255,false };var Turquoise =Color {0x40,0xE0,0xD0,255,false };var PaleTurquoise =Color {0xAF,0xEE,0xEE,255,false };var Gainsboro =Color {0xDC,0xDC,0xDC,255,false };
var Yellow =Color {0xFF,0xFF,0x00,255,false };var Red =Color {0xFF,0x00,0x00,255,false };var GreenYellow =Color {0xAD,0xFF,0x2F,255,false };var LightBlue =Color {0xAD,0xD8,0xE6,255,false };var OliveDrab =Color {0x6B,0x8E,0x23,255,false };var DarkOliveGreen =Color {0x55,0x6B,0x2F,255,false };
var DarkCyan =Color {0x00,0x8B,0x8B,255,false };var Crimson =Color {0xDC,0x14,0x3C,255,false };var Plum =Color {0xDD,0xA0,0xDD,255,false };var Salmon =Color {0xFA,0x80,0x72,255,false };var MediumOrchid =Color {0xBA,0x55,0xD3,255,false };var Tan =Color {0xD2,0xB4,0x8C,255,false };
var OldLace =Color {0xFD,0xF5,0xE6,255,false };var Brown =Color {0xA5,0x2A,0x2A,255,false };var SlateBlue =Color {0x6A,0x5A,0xCD,255,false };var Linen =Color {0xFA,0xF0,0xE6,255,false };var HotPink =Color {0xFF,0x69,0xB4,255,false };var DarkSalmon =Color {0xE9,0x96,0x7A,255,false };
var Cyan =Color {0x00,0xFF,0xFF,255,false };var LimeGreen =Color {0x32,0xCD,0x32,255,false };var Tomato =Color {0xFF,0x63,0x47,255,false };var Peru =Color {0xCD,0x85,0x3F,255,false };var LightSteelBlue =Color {0xB0,0xC4,0xDE,255,false };var SeaShell =Color {0xFF,0xF5,0xEE,255,false };
var DimGrey =Color {0x69,0x69,0x69,255,false };var Azure =Color {0xF0,0xFF,0xFF,255,false };var Wheat =Color {0xF5,0xDE,0xB3,255,false };var DarkGrey =Color {0xA9,0xA9,0xA9,255,false };var GhostWhite =Color {0xF8,0xF8,0xFF,255,false };func FromHex (s string )Color {if len (s )==0{return Auto ;
};if s [0]=='#'{s =s [1:];};var _dff ,_cd ,_ca uint8 ;_ad ,_ :=_cb .Sscanf (s ,"\u0025\u0030\u0032x\u0025\u0030\u0032\u0078\u0025\u0030\u0032\u0078",&_dff ,&_cd ,&_ca );if _ad ==3{return RGB (_dff ,_cd ,_ca );};return Auto ;};var DarkOrchid =Color {0x99,0x32,0xCC,255,false };
var White =Color {0xFF,0xFF,0xFF,255,false };

// Color is a 24 bit color that can be converted to
// internal ECMA-376 formats as needed.
type Color struct{_e ,_ab ,_dd ,_ag uint8 ;_cbe bool ;};var Blue =Color {0x00,0x00,0xFF,255,false };var SpringGreen =Color {0x00,0xFF,0x7F,255,false };var Gold =Color {0xFF,0xD7,0x00,255,false };var SlateGray =Color {0x70,0x80,0x90,255,false };var BlueViolet =Color {0x8A,0x2B,0xE2,255,false };
var CadetBlue =Color {0x5F,0x9E,0xA0,255,false };var Coral =Color {0xFF,0x7F,0x50,255,false };var CornflowerBlue =Color {0x64,0x95,0xED,255,false };var Lime =Color {0x00,0xFF,0x00,255,false };var HighlightColorToCreatorColorMap =map[_c .ST_HighlightColor ]_dg .Color {_c .ST_HighlightColorBlack :_dg .ColorBlack ,_c .ST_HighlightColorBlue :_dg .ColorBlue ,_c .ST_HighlightColorCyan :_dg .ColorRGBFrom8bit (0,255,255),_c .ST_HighlightColorGreen :_dg .ColorGreen ,_c .ST_HighlightColorMagenta :_dg .ColorRGBFrom8bit (255,0,255),_c .ST_HighlightColorRed :_dg .ColorRed ,_c .ST_HighlightColorYellow :_dg .ColorYellow ,_c .ST_HighlightColorWhite :_dg .ColorWhite ,_c .ST_HighlightColorDarkBlue :_dg .ColorRGBFrom8bit (0,0,128),_c .ST_HighlightColorDarkCyan :_dg .ColorRGBFrom8bit (0,128,128),_c .ST_HighlightColorDarkGreen :_dg .ColorRGBFrom8bit (0,128,0),_c .ST_HighlightColorDarkMagenta :_dg .ColorRGBFrom8bit (128,0,128),_c .ST_HighlightColorDarkRed :_dg .ColorRGBFrom8bit (128,0,0),_c .ST_HighlightColorDarkYellow :_dg .ColorRGBFrom8bit (255,215,0),_c .ST_HighlightColorLightGray :_dg .ColorRGBFrom8bit (211,211,211),_c .ST_HighlightColorDarkGray :_dg .ColorRGBFrom8bit (169,169,169)};
var Sienna =Color {0xA0,0x52,0x2D,255,false };var LightGoldenRodYellow =Color {0xFA,0xFA,0xD2,255,false };var LightYellow =Color {0xFF,0xFF,0xE0,255,false };

// RGB constructs a new RGB color with a given red, green and blue value.
func RGB (r ,g ,b uint8 )Color {return Color {r ,g ,b ,255,false }};var FloralWhite =Color {0xFF,0xFA,0xF0,255,false };var AntiqueWhite =Color {0xFA,0xEB,0xD7,255,false };var DarkSlateGrey =Color {0x2F,0x4F,0x4F,255,false };var BlanchedAlmond =Color {0xFF,0xEB,0xCD,255,false };
var RoyalBlue =Color {0x41,0x69,0xE1,255,false };