//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package textshaping ;import (_c "github.com/unidoc/garabic";_a "golang.org/x/text/unicode/bidi";_g "strings";);

// ArabicShape returns shaped arabic glyphs string.
func ArabicShape (text string )(string ,error ){_e :=_a .Paragraph {};_e .SetString (text );_ac ,_gd :=_e .Order ();if _gd !=nil {return "",_gd ;};for _f :=0;_f < _ac .NumRuns ();_f ++{_fd :=_ac .Run (_f );_ec :=_fd .String ();if _fd .Direction ()==_a .RightToLeft {var (_gf =_c .Shape (_ec );
_ga =[]rune (_gf );_gc =make ([]rune ,len (_ga )););_d :=0;for _dg :=len (_ga )-1;_dg >=0;_dg --{_gc [_d ]=_ga [_dg ];_d ++;};_ec =string (_gc );text =_g .Replace (text ,_g .TrimSpace (_fd .String ()),_ec ,1);};};return text ,nil ;};