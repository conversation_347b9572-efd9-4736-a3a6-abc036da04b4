//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package security ;import (_acf "bytes";_eg "crypto/aes";_b "crypto/cipher";_f "crypto/md5";_bc "crypto/rand";_ac "crypto/rc4";_ag "crypto/sha256";_e "crypto/sha512";_egg "encoding/binary";_g "errors";_age "fmt";_de "github.com/unidoc/unipdf/v3/common";
_c "hash";_d "io";_aa "math";);func (_ab *ecbDecrypter )CryptBlocks (dst ,src []byte ){if len (src )%_ab ._eb !=0{_de .Log .Error ("\u0045\u0052\u0052\u004f\u0052:\u0020\u0045\u0043\u0042\u0020\u0064\u0065\u0063\u0072\u0079\u0070\u0074\u003a \u0069\u006e\u0070\u0075\u0074\u0020\u006e\u006f\u0074\u0020\u0066\u0075\u006c\u006c\u0020\u0062\u006c\u006f\u0063\u006b\u0073");
return ;};if len (dst )< len (src ){_de .Log .Error ("\u0045R\u0052\u004fR\u003a\u0020\u0045C\u0042\u0020\u0064\u0065\u0063\u0072\u0079p\u0074\u003a\u0020\u006f\u0075\u0074p\u0075\u0074\u0020\u0073\u006d\u0061\u006c\u006c\u0065\u0072\u0020t\u0068\u0061\u006e\u0020\u0069\u006e\u0070\u0075\u0074");
return ;};for len (src )> 0{_ab ._fe .Decrypt (dst ,src [:_ab ._eb ]);src =src [_ab ._eb :];dst =dst [_ab ._eb :];};};

// StdHandler is an interface for standard security handlers.
type StdHandler interface{

// GenerateParams uses owner and user passwords to set encryption parameters and generate an encryption key.
// It assumes that R, P and EncryptMetadata are already set.
GenerateParams (_bf *StdEncryptDict ,_ad ,_dg []byte )([]byte ,error );

// Authenticate uses encryption dictionary parameters and the password to calculate
// the document encryption key. It also returns permissions that should be granted to a user.
// In case of failed authentication, it returns empty key and zero permissions with no error.
Authenticate (_cg *StdEncryptDict ,_ge []byte )([]byte ,Permissions ,error );};

// GenerateParams generates and sets O and U parameters for the encryption dictionary.
// It expects R, P and EncryptMetadata fields to be set.
func (_bbb stdHandlerR4 )GenerateParams (d *StdEncryptDict ,opass ,upass []byte )([]byte ,error ){O ,_dga :=_bbb .alg3 (d .R ,upass ,opass );if _dga !=nil {_de .Log .Debug ("\u0045R\u0052\u004fR\u003a\u0020\u0045r\u0072\u006f\u0072\u0020\u0067\u0065\u006ee\u0072\u0061\u0074\u0069\u006e\u0067 \u004f\u0020\u0066\u006f\u0072\u0020\u0065\u006e\u0063\u0072\u0079p\u0074\u0069\u006f\u006e\u0020\u0028\u0025\u0073\u0029",_dga );
return nil ,_dga ;};d .O =O ;_de .Log .Trace ("\u0067\u0065\u006e\u0020\u004f\u003a\u0020\u0025\u0020\u0078",O );_aacg :=_bbb .alg2 (d ,upass );U ,_dga :=_bbb .alg5 (_aacg ,upass );if _dga !=nil {_de .Log .Debug ("\u0045R\u0052\u004fR\u003a\u0020\u0045r\u0072\u006f\u0072\u0020\u0067\u0065\u006ee\u0072\u0061\u0074\u0069\u006e\u0067 \u004f\u0020\u0066\u006f\u0072\u0020\u0065\u006e\u0063\u0072\u0079p\u0074\u0069\u006f\u006e\u0020\u0028\u0025\u0073\u0029",_dga );
return nil ,_dga ;};d .U =U ;_de .Log .Trace ("\u0067\u0065\u006e\u0020\u0055\u003a\u0020\u0025\u0020\u0078",U );return _aacg ,nil ;};var _ StdHandler =stdHandlerR4 {};func (_bfg stdHandlerR4 )alg5 (_eda []byte ,_fecf []byte )([]byte ,error ){_cge :=_f .New ();
_cge .Write ([]byte (_gag ));_cge .Write ([]byte (_bfg .ID0 ));_fg :=_cge .Sum (nil );_de .Log .Trace ("\u0061\u006c\u0067\u0035");_de .Log .Trace ("\u0065k\u0065\u0079\u003a\u0020\u0025\u0020x",_eda );_de .Log .Trace ("\u0049D\u003a\u0020\u0025\u0020\u0078",_bfg .ID0 );
if len (_fg )!=16{return nil ,_g .New ("\u0068a\u0073\u0068\u0020\u006c\u0065\u006e\u0067\u0074\u0068\u0020\u006eo\u0074\u0020\u0031\u0036\u0020\u0062\u0079\u0074\u0065\u0073");};_ebg ,_cbf :=_ac .NewCipher (_eda );if _cbf !=nil {return nil ,_g .New ("\u0066a\u0069l\u0065\u0064\u0020\u0072\u0063\u0034\u0020\u0063\u0069\u0070\u0068");
};_gff :=make ([]byte ,16);_ebg .XORKeyStream (_gff ,_fg );_dba :=make ([]byte ,len (_eda ));for _bae :=0;_bae < 19;_bae ++{for _gcf :=0;_gcf < len (_eda );_gcf ++{_dba [_gcf ]=_eda [_gcf ]^byte (_bae +1);};_ebg ,_cbf =_ac .NewCipher (_dba );if _cbf !=nil {return nil ,_g .New ("\u0066a\u0069l\u0065\u0064\u0020\u0072\u0063\u0034\u0020\u0063\u0069\u0070\u0068");
};_ebg .XORKeyStream (_gff ,_gff );_de .Log .Trace ("\u0069\u0020\u003d\u0020\u0025\u0064\u002c\u0020\u0065\u006b\u0065\u0079:\u0020\u0025\u0020\u0078",_bae ,_dba );_de .Log .Trace ("\u0069\u0020\u003d\u0020\u0025\u0064\u0020\u002d\u003e\u0020\u0025\u0020\u0078",_bae ,_gff );
};_ceg :=make ([]byte ,32);for _aac :=0;_aac < 16;_aac ++{_ceg [_aac ]=_gff [_aac ];};_ ,_cbf =_bc .Read (_ceg [16:32]);if _cbf !=nil {return nil ,_g .New ("\u0066a\u0069\u006c\u0065\u0064 \u0074\u006f\u0020\u0067\u0065n\u0020r\u0061n\u0064\u0020\u006e\u0075\u006d\u0062\u0065r");
};return _ceg ,nil ;};func _cagc (_acg []byte ,_bcdg int ){_gfe :=_bcdg ;for _gfe < len (_acg ){copy (_acg [_gfe :],_acg [:_gfe ]);_gfe *=2;};};const (EventDocOpen =AuthEvent ("\u0044o\u0063\u004f\u0070\u0065\u006e");EventEFOpen =AuthEvent ("\u0045\u0046\u004f\u0070\u0065\u006e");
);const (PermOwner =Permissions (_aa .MaxUint32 );PermPrinting =Permissions (1<<2);PermModify =Permissions (1<<3);PermExtractGraphics =Permissions (1<<4);PermAnnotate =Permissions (1<<5);PermFillForms =Permissions (1<<8);PermDisabilityExtract =Permissions (1<<9);
PermRotateInsert =Permissions (1<<10);PermFullPrintQuality =Permissions (1<<11););func (_cb *ecbDecrypter )BlockSize ()int {return _cb ._eb };func (_eccc stdHandlerR6 )alg13 (_ebbg *StdEncryptDict ,_afa []byte )error {if _abb :=_df ("\u0061\u006c\u00671\u0033","\u004b\u0065\u0079",32,_afa );
_abb !=nil {return _abb ;};if _gad :=_df ("\u0061\u006c\u00671\u0033","\u0050\u0065\u0072m\u0073",16,_ebbg .Perms );_gad !=nil {return _gad ;};_dfgb :=make ([]byte ,16);copy (_dfgb ,_ebbg .Perms [:16]);_ccb ,_gaa :=_eg .NewCipher (_afa [:32]);if _gaa !=nil {return _gaa ;
};_efb :=_cd (_ccb );_efb .CryptBlocks (_dfgb ,_dfgb );if !_acf .Equal (_dfgb [9:12],[]byte ("\u0061\u0064\u0062")){return _g .New ("\u0064\u0065\u0063o\u0064\u0065\u0064\u0020p\u0065\u0072\u006d\u0069\u0073\u0073\u0069o\u006e\u0073\u0020\u0061\u0072\u0065\u0020\u0069\u006e\u0076\u0061\u006c\u0069\u0064");
};_ccc :=Permissions (_egg .LittleEndian .Uint32 (_dfgb [0:4]));if _ccc !=_ebbg .P {return _g .New ("\u0070\u0065r\u006d\u0069\u0073\u0073\u0069\u006f\u006e\u0073\u0020\u0076\u0061\u006c\u0069\u0064\u0061\u0074\u0069\u006f\u006e\u0020\u0066\u0061il\u0065\u0064");
};var _gfc bool ;if _dfgb [8]=='T'{_gfc =true ;}else if _dfgb [8]=='F'{_gfc =false ;}else {return _g .New ("\u0064\u0065\u0063\u006f\u0064\u0065\u0064 \u006d\u0065\u0074a\u0064\u0061\u0074\u0061 \u0065\u006e\u0063\u0072\u0079\u0070\u0074\u0069\u006f\u006e\u0020\u0066\u006c\u0061\u0067\u0020\u0069\u0073\u0020\u0069\u006e\u0076\u0061\u006c\u0069\u0064");
};if _gfc !=_ebbg .EncryptMetadata {return _g .New ("\u006d\u0065t\u0061\u0064\u0061\u0074a\u0020\u0065n\u0063\u0072\u0079\u0070\u0074\u0069\u006f\u006e \u0076\u0061\u006c\u0069\u0064\u0061\u0074\u0069\u006f\u006e\u0020\u0066a\u0069\u006c\u0065\u0064");
};return nil ;};func (_fge stdHandlerR6 )alg12 (_gedee *StdEncryptDict ,_cfg []byte )([]byte ,error ){if _fabf :=_df ("\u0061\u006c\u00671\u0032","\u0055",48,_gedee .U );_fabf !=nil {return nil ,_fabf ;};if _badb :=_df ("\u0061\u006c\u00671\u0032","\u004f",48,_gedee .O );
_badb !=nil {return nil ,_badb ;};_egdc :=make ([]byte ,len (_cfg )+8+48);_ade :=copy (_egdc ,_cfg );_ade +=copy (_egdc [_ade :],_gedee .O [32:40]);_ade +=copy (_egdc [_ade :],_gedee .U [0:48]);_dgd ,_eca :=_fge .alg2b (_gedee .R ,_egdc ,_cfg ,_gedee .U [0:48]);
if _eca !=nil {return nil ,_eca ;};_dgd =_dgd [:32];if !_acf .Equal (_dgd ,_gedee .O [:32]){return nil ,nil ;};return _dgd ,nil ;};

// StdEncryptDict is a set of additional fields used in standard encryption dictionary.
type StdEncryptDict struct{R int ;P Permissions ;EncryptMetadata bool ;O ,U []byte ;OE ,UE []byte ;Perms []byte ;};type ecbEncrypter ecb ;func _df (_bg ,_gf string ,_geb int ,_gdb []byte )error {if len (_gdb )< _geb {return errInvalidField {Func :_bg ,Field :_gf ,Exp :_geb ,Got :len (_gdb )};
};return nil ;};func (_fb stdHandlerR4 )alg6 (_efc *StdEncryptDict ,_ecc []byte )([]byte ,error ){var (_fecfe []byte ;_caf error ;);_cagb :=_fb .alg2 (_efc ,_ecc );if _efc .R ==2{_fecfe ,_caf =_fb .alg4 (_cagb ,_ecc );}else if _efc .R >=3{_fecfe ,_caf =_fb .alg5 (_cagb ,_ecc );
}else {return nil ,_g .New ("\u0069n\u0076\u0061\u006c\u0069\u0064\u0020R");};if _caf !=nil {return nil ,_caf ;};_de .Log .Trace ("\u0063\u0068\u0065\u0063k:\u0020\u0025\u0020\u0078\u0020\u003d\u003d\u0020\u0025\u0020\u0078\u0020\u003f",string (_fecfe ),string (_efc .U ));
_gaf :=_fecfe ;_gcfa :=_efc .U ;if _efc .R >=3{if len (_gaf )> 16{_gaf =_gaf [0:16];};if len (_gcfa )> 16{_gcfa =_gcfa [0:16];};};if !_acf .Equal (_gaf ,_gcfa ){return nil ,nil ;};return _cagb ,nil ;};func _bba (_dbe []byte )([]byte ,error ){_cbd :=_ag .New ();
_cbd .Write (_dbe );return _cbd .Sum (nil ),nil ;};func (_ga *ecbEncrypter )CryptBlocks (dst ,src []byte ){if len (src )%_ga ._eb !=0{_de .Log .Error ("\u0045\u0052\u0052\u004f\u0052:\u0020\u0045\u0043\u0042\u0020\u0065\u006e\u0063\u0072\u0079\u0070\u0074\u003a \u0069\u006e\u0070\u0075\u0074\u0020\u006e\u006f\u0074\u0020\u0066\u0075\u006c\u006c\u0020\u0062\u006c\u006f\u0063\u006b\u0073");
return ;};if len (dst )< len (src ){_de .Log .Error ("\u0045R\u0052\u004fR\u003a\u0020\u0045C\u0042\u0020\u0065\u006e\u0063\u0072\u0079p\u0074\u003a\u0020\u006f\u0075\u0074p\u0075\u0074\u0020\u0073\u006d\u0061\u006c\u006c\u0065\u0072\u0020t\u0068\u0061\u006e\u0020\u0069\u006e\u0070\u0075\u0074");
return ;};for len (src )> 0{_ga ._fe .Encrypt (dst ,src [:_ga ._eb ]);src =src [_ga ._eb :];dst =dst [_ga ._eb :];};};

// Authenticate implements StdHandler interface.
func (_bfd stdHandlerR4 )Authenticate (d *StdEncryptDict ,pass []byte )([]byte ,Permissions ,error ){_de .Log .Trace ("\u0044\u0065b\u0075\u0067\u0067\u0069n\u0067\u0020a\u0075\u0074\u0068\u0065\u006e\u0074\u0069\u0063a\u0074\u0069\u006f\u006e\u0020\u002d\u0020\u006f\u0077\u006e\u0065\u0072 \u0070\u0061\u0073\u0073");
_fa ,_adc :=_bfd .alg7 (d ,pass );if _adc !=nil {return nil ,0,_adc ;};if _fa !=nil {_de .Log .Trace ("\u0074h\u0069\u0073\u002e\u0061u\u0074\u0068\u0065\u006e\u0074i\u0063a\u0074e\u0064\u0020\u003d\u0020\u0054\u0072\u0075e");return _fa ,PermOwner ,nil ;
};_de .Log .Trace ("\u0044\u0065bu\u0067\u0067\u0069n\u0067\u0020\u0061\u0075the\u006eti\u0063\u0061\u0074\u0069\u006f\u006e\u0020- \u0075\u0073\u0065\u0072\u0020\u0070\u0061s\u0073");_fa ,_adc =_bfd .alg6 (d ,pass );if _adc !=nil {return nil ,0,_adc ;
};if _fa !=nil {_de .Log .Trace ("\u0074h\u0069\u0073\u002e\u0061u\u0074\u0068\u0065\u006e\u0074i\u0063a\u0074e\u0064\u0020\u003d\u0020\u0054\u0072\u0075e");return _fa ,d .P ,nil ;};return nil ,0,nil ;};func (_cfbc stdHandlerR6 )alg11 (_dgg *StdEncryptDict ,_dee []byte )([]byte ,error ){if _bfga :=_df ("\u0061\u006c\u00671\u0031","\u0055",48,_dgg .U );
_bfga !=nil {return nil ,_bfga ;};_gac :=make ([]byte ,len (_dee )+8);_bgcf :=copy (_gac ,_dee );_bgcf +=copy (_gac [_bgcf :],_dgg .U [32:40]);_dab ,_cbg :=_cfbc .alg2b (_dgg .R ,_gac ,_dee ,nil );if _cbg !=nil {return nil ,_cbg ;};_dab =_dab [:32];if !_acf .Equal (_dab ,_dgg .U [:32]){return nil ,nil ;
};return _dab ,nil ;};type stdHandlerR4 struct{Length int ;ID0 string ;};func (_eec stdHandlerR6 )alg2a (_ggc *StdEncryptDict ,_dag []byte )([]byte ,Permissions ,error ){if _eggg :=_df ("\u0061\u006c\u00672\u0061","\u004f",48,_ggc .O );_eggg !=nil {return nil ,0,_eggg ;
};if _aaa :=_df ("\u0061\u006c\u00672\u0061","\u0055",48,_ggc .U );_aaa !=nil {return nil ,0,_aaa ;};if len (_dag )> 127{_dag =_dag [:127];};_ffe ,_be :=_eec .alg12 (_ggc ,_dag );if _be !=nil {return nil ,0,_be ;};var (_gea []byte ;_egab []byte ;_egb []byte ;
);var _gage Permissions ;if len (_ffe )!=0{_gage =PermOwner ;_dbg :=make ([]byte ,len (_dag )+8+48);_cbb :=copy (_dbg ,_dag );_cbb +=copy (_dbg [_cbb :],_ggc .O [40:48]);copy (_dbg [_cbb :],_ggc .U [0:48]);_gea =_dbg ;_egab =_ggc .OE ;_egb =_ggc .U [0:48];
}else {_ffe ,_be =_eec .alg11 (_ggc ,_dag );if _be ==nil &&len (_ffe )==0{_ffe ,_be =_eec .alg11 (_ggc ,[]byte (""));};if _be !=nil {return nil ,0,_be ;}else if len (_ffe )==0{return nil ,0,nil ;};_gage =_ggc .P ;_bab :=make ([]byte ,len (_dag )+8);_bde :=copy (_bab ,_dag );
copy (_bab [_bde :],_ggc .U [40:48]);_gea =_bab ;_egab =_ggc .UE ;_egb =nil ;};if _daf :=_df ("\u0061\u006c\u00672\u0061","\u004b\u0065\u0079",32,_egab );_daf !=nil {return nil ,0,_daf ;};_egab =_egab [:32];_aacb ,_be :=_eec .alg2b (_ggc .R ,_gea ,_dag ,_egb );
if _be !=nil {return nil ,0,_be ;};_fde ,_be :=_eg .NewCipher (_aacb [:32]);if _be !=nil {return nil ,0,_be ;};_dac :=make ([]byte ,_eg .BlockSize );_dca :=_b .NewCBCDecrypter (_fde ,_dac );_fdf :=make ([]byte ,32);_dca .CryptBlocks (_fdf ,_egab );if _ggc .R ==5{return _fdf ,_gage ,nil ;
};_be =_eec .alg13 (_ggc ,_fdf );if _be !=nil {return nil ,0,_be ;};return _fdf ,_gage ,nil ;};func (_ea stdHandlerR4 )alg3 (R int ,_gdd ,_bca []byte )([]byte ,error ){var _fd []byte ;if len (_bca )> 0{_fd =_ea .alg3Key (R ,_bca );}else {_fd =_ea .alg3Key (R ,_gdd );
};_cab ,_fdg :=_ac .NewCipher (_fd );if _fdg !=nil {return nil ,_g .New ("\u0066a\u0069l\u0065\u0064\u0020\u0072\u0063\u0034\u0020\u0063\u0069\u0070\u0068");};_dff :=_ea .paddedPass (_gdd );_gb :=make ([]byte ,len (_dff ));_cab .XORKeyStream (_gb ,_dff );
if R >=3{_bff :=make ([]byte ,len (_fd ));for _ead :=0;_ead < 19;_ead ++{for _ba :=0;_ba < len (_fd );_ba ++{_bff [_ba ]=_fd [_ba ]^byte (_ead +1);};_ec ,_cag :=_ac .NewCipher (_bff );if _cag !=nil {return nil ,_g .New ("\u0066a\u0069l\u0065\u0064\u0020\u0072\u0063\u0034\u0020\u0063\u0069\u0070\u0068");
};_ec .XORKeyStream (_gb ,_gb );};};return _gb ,nil ;};func (_eed stdHandlerR4 )alg7 (_fgf *StdEncryptDict ,_ff []byte )([]byte ,error ){_fdb :=_eed .alg3Key (_fgf .R ,_ff );_ceb :=make ([]byte ,len (_fgf .O ));if _fgf .R ==2{_bgc ,_bffa :=_ac .NewCipher (_fdb );
if _bffa !=nil {return nil ,_g .New ("\u0066\u0061\u0069\u006c\u0065\u0064\u0020\u0063\u0069\u0070\u0068\u0065\u0072");};_bgc .XORKeyStream (_ceb ,_fgf .O );}else if _fgf .R >=3{_dda :=append ([]byte {},_fgf .O ...);for _fff :=0;_fff < 20;_fff ++{_gebb :=append ([]byte {},_fdb ...);
for _dge :=0;_dge < len (_fdb );_dge ++{_gebb [_dge ]^=byte (19-_fff );};_eggd ,_cf :=_ac .NewCipher (_gebb );if _cf !=nil {return nil ,_g .New ("\u0066\u0061\u0069\u006c\u0065\u0064\u0020\u0063\u0069\u0070\u0068\u0065\u0072");};_eggd .XORKeyStream (_ceb ,_dda );
_dda =append ([]byte {},_ceb ...);};}else {return nil ,_g .New ("\u0069n\u0076\u0061\u006c\u0069\u0064\u0020R");};_ega ,_cda :=_eed .alg6 (_fgf ,_ceb );if _cda !=nil {return nil ,nil ;};return _ega ,nil ;};func _egd (_dd _b .Block )_b .BlockMode {return (*ecbEncrypter )(_fec (_dd ))};
func (_bgd stdHandlerR4 )alg2 (_bb *StdEncryptDict ,_ce []byte )[]byte {_de .Log .Trace ("\u0061\u006c\u0067\u0032");_abc :=_bgd .paddedPass (_ce );_ee :=_f .New ();_ee .Write (_abc );_ee .Write (_bb .O );var _dfe [4]byte ;_egg .LittleEndian .PutUint32 (_dfe [:],uint32 (_bb .P ));
_ee .Write (_dfe [:]);_de .Log .Trace ("\u0067o\u0020\u0050\u003a\u0020\u0025\u0020x",_dfe );_ee .Write ([]byte (_bgd .ID0 ));_de .Log .Trace ("\u0074\u0068\u0069\u0073\u002e\u0052\u0020\u003d\u0020\u0025d\u0020\u0065\u006e\u0063\u0072\u0079\u0070t\u004d\u0065\u0074\u0061\u0064\u0061\u0074\u0061\u0020\u0025\u0076",_bb .R ,_bb .EncryptMetadata );
if (_bb .R >=4)&&!_bb .EncryptMetadata {_ee .Write ([]byte {0xff,0xff,0xff,0xff});};_dc :=_ee .Sum (nil );if _bb .R >=3{_ee =_f .New ();for _fc :=0;_fc < 50;_fc ++{_ee .Reset ();_ee .Write (_dc [0:_bgd .Length /8]);_dc =_ee .Sum (nil );};};if _bb .R >=3{return _dc [0:_bgd .Length /8];
};return _dc [0:5];};type errInvalidField struct{Func string ;Field string ;Exp int ;Got int ;};func (_gdg errInvalidField )Error ()string {return _age .Sprintf ("\u0025s\u003a\u0020e\u0078\u0070\u0065\u0063t\u0065\u0064\u0020%\u0073\u0020\u0066\u0069\u0065\u006c\u0064\u0020\u0074o \u0062\u0065\u0020%\u0064\u0020b\u0079\u0074\u0065\u0073\u002c\u0020g\u006f\u0074 \u0025\u0064",_gdg .Func ,_gdg .Field ,_gdg .Exp ,_gdg .Got );
};func (_dcg stdHandlerR6 )alg10 (_bce *StdEncryptDict ,_gede []byte )error {if _bad :=_df ("\u0061\u006c\u00671\u0030","\u004b\u0065\u0079",32,_gede );_bad !=nil {return _bad ;};_cee :=uint64 (uint32 (_bce .P ))|(_aa .MaxUint32 <<32);Perms :=make ([]byte ,16);
_egg .LittleEndian .PutUint64 (Perms [:8],_cee );if _bce .EncryptMetadata {Perms [8]='T';}else {Perms [8]='F';};copy (Perms [9:12],"\u0061\u0064\u0062");if _ ,_ace :=_d .ReadFull (_bc .Reader ,Perms [12:16]);_ace !=nil {return _ace ;};_aea ,_cgb :=_abd (_gede [:32]);
if _cgb !=nil {return _cgb ;};_cfc :=_egd (_aea );_cfc .CryptBlocks (Perms ,Perms );_bce .Perms =Perms [:16];return nil ;};

// Allowed checks if a set of permissions can be granted.
func (_bcd Permissions )Allowed (p2 Permissions )bool {return _bcd &p2 ==p2 };func (_ca *ecbEncrypter )BlockSize ()int {return _ca ._eb };func _cd (_gd _b .Block )_b .BlockMode {return (*ecbDecrypter )(_fec (_gd ))};

// AuthEvent is an event type that triggers authentication.
type AuthEvent string ;var _ StdHandler =stdHandlerR6 {};

// Authenticate implements StdHandler interface.
func (_ggce stdHandlerR6 )Authenticate (d *StdEncryptDict ,pass []byte )([]byte ,Permissions ,error ){return _ggce .alg2a (d ,pass );};func _fec (_gg _b .Block )*ecb {return &ecb {_fe :_gg ,_eb :_gg .BlockSize ()}};type stdHandlerR6 struct{};func _babd (_bga ,_adcg ,_bbd []byte )([]byte ,error ){var (_dbaf ,_ebb ,_gfeb _c .Hash ;
);_dbaf =_ag .New ();_ggae :=make ([]byte ,64);_bcdgc :=_dbaf ;_bcdgc .Write (_bga );K :=_bcdgc .Sum (_ggae [:0]);_aag :=make ([]byte ,64*(127+64+48));_bgae :=func (_gffa int )([]byte ,error ){_cff :=len (_adcg )+len (K )+len (_bbd );_bag :=_aag [:_cff ];
_bfe :=copy (_bag ,_adcg );_bfe +=copy (_bag [_bfe :],K [:]);_bfe +=copy (_bag [_bfe :],_bbd );if _bfe !=_cff {_de .Log .Error ("E\u0052\u0052\u004f\u0052\u003a\u0020u\u006e\u0065\u0078\u0070\u0065\u0063t\u0065\u0064\u0020\u0072\u006f\u0075\u006ed\u0020\u0069\u006e\u0070\u0075\u0074\u0020\u0073\u0069\u007ae\u002e");
return nil ,_g .New ("\u0077\u0072\u006f\u006e\u0067\u0020\u0073\u0069\u007a\u0065");};K1 :=_aag [:_cff *64];_cagc (K1 ,_cff );_ecf ,_acd :=_abd (K [0:16]);if _acd !=nil {return nil ,_acd ;};_bfa :=_b .NewCBCEncrypter (_ecf ,K [16:32]);_bfa .CryptBlocks (K1 ,K1 );
E :=K1 ;_dfd :=0;for _gee :=0;_gee < 16;_gee ++{_dfd +=int (E [_gee ]%3);};var _dfg _c .Hash ;switch _dfd %3{case 0:_dfg =_dbaf ;case 1:if _ebb ==nil {_ebb =_e .New384 ();};_dfg =_ebb ;case 2:if _gfeb ==nil {_gfeb =_e .New ();};_dfg =_gfeb ;};_dfg .Reset ();
_dfg .Write (E );K =_dfg .Sum (_ggae [:0]);return E ,nil ;};for _fgb :=0;;{E ,_dgc :=_bgae (_fgb );if _dgc !=nil {return nil ,_dgc ;};_agb :=E [len (E )-1];_fgb ++;if _fgb >=64&&_agb <=uint8 (_fgb -32){break ;};};return K [:32],nil ;};func (_bd stdHandlerR4 )alg3Key (R int ,_gce []byte )[]byte {_deg :=_f .New ();
_ed :=_bd .paddedPass (_gce );_deg .Write (_ed );if R >=3{for _gga :=0;_gga < 50;_gga ++{_ged :=_deg .Sum (nil );_deg =_f .New ();_deg .Write (_ged );};};_bda :=_deg .Sum (nil );if R ==2{_bda =_bda [0:5];}else {_bda =_bda [0:_bd .Length /8];};return _bda ;
};type ecb struct{_fe _b .Block ;_eb int ;};type ecbDecrypter ecb ;func (_ggcb stdHandlerR6 )alg8 (_agg *StdEncryptDict ,_ae []byte ,_bcdf []byte )error {if _cbe :=_df ("\u0061\u006c\u0067\u0038","\u004b\u0065\u0079",32,_ae );_cbe !=nil {return _cbe ;};
var _cfb [16]byte ;if _ ,_egf :=_d .ReadFull (_bc .Reader ,_cfb [:]);_egf !=nil {return _egf ;};_dec :=_cfb [0:8];_cac :=_cfb [8:16];_ebf :=make ([]byte ,len (_bcdf )+len (_dec ));_fece :=copy (_ebf ,_bcdf );copy (_ebf [_fece :],_dec );_afb ,_ebge :=_ggcb .alg2b (_agg .R ,_ebf ,_bcdf ,nil );
if _ebge !=nil {return _ebge ;};U :=make ([]byte ,len (_afb )+len (_dec )+len (_cac ));_fece =copy (U ,_afb [:32]);_fece +=copy (U [_fece :],_dec );copy (U [_fece :],_cac );_agg .U =U ;_fece =len (_bcdf );copy (_ebf [_fece :],_cac );_afb ,_ebge =_ggcb .alg2b (_agg .R ,_ebf ,_bcdf ,nil );
if _ebge !=nil {return _ebge ;};_beg ,_ebge :=_abd (_afb [:32]);if _ebge !=nil {return _ebge ;};_bdb :=make ([]byte ,_eg .BlockSize );_afe :=_b .NewCBCEncrypter (_beg ,_bdb );UE :=make ([]byte ,32);_afe .CryptBlocks (UE ,_ae [:32]);_agg .UE =UE ;return nil ;
};func (_af stdHandlerR6 )alg2b (R int ,_cgc ,_fbe ,_gbb []byte )([]byte ,error ){if R ==5{return _bba (_cgc );};return _babd (_cgc ,_fbe ,_gbb );};

// GenerateParams is the algorithm opposite to alg2a (R>=5).
// It generates U,O,UE,OE,Perms fields using AESv3 encryption.
// There is no algorithm number assigned to this function in the spec.
// It expects R, P and EncryptMetadata fields to be set.
func (_ece stdHandlerR6 )GenerateParams (d *StdEncryptDict ,opass ,upass []byte )([]byte ,error ){_afg :=make ([]byte ,32);if _ ,_acc :=_d .ReadFull (_bc .Reader ,_afg );_acc !=nil {return nil ,_acc ;};d .U =nil ;d .O =nil ;d .UE =nil ;d .OE =nil ;d .Perms =nil ;
if len (upass )> 127{upass =upass [:127];};if len (opass )> 127{opass =opass [:127];};if _eceb :=_ece .alg8 (d ,_afg ,upass );_eceb !=nil {return nil ,_eceb ;};if _dgaf :=_ece .alg9 (d ,_afg ,opass );_dgaf !=nil {return nil ,_dgaf ;};if d .R ==5{return _afg ,nil ;
};if _aacf :=_ece .alg10 (d ,_afg );_aacf !=nil {return nil ,_aacf ;};return _afg ,nil ;};func (stdHandlerR4 )paddedPass (_gc []byte )[]byte {_db :=make ([]byte ,32);_ef :=copy (_db ,_gc );for ;_ef < 32;_ef ++{_db [_ef ]=_gag [_ef -len (_gc )];};return _db ;
};func (_gca stdHandlerR4 )alg4 (_agd []byte ,_fcd []byte )([]byte ,error ){_da ,_gfa :=_ac .NewCipher (_agd );if _gfa !=nil {return nil ,_g .New ("\u0066a\u0069l\u0065\u0064\u0020\u0072\u0063\u0034\u0020\u0063\u0069\u0070\u0068");};_cdf :=[]byte (_gag );
_cc :=make ([]byte ,len (_cdf ));_da .XORKeyStream (_cc ,_cdf );return _cc ,nil ;};func (_beb stdHandlerR6 )alg9 (_cagbg *StdEncryptDict ,_decc []byte ,_eea []byte )error {if _eef :=_df ("\u0061\u006c\u0067\u0039","\u004b\u0065\u0079",32,_decc );_eef !=nil {return _eef ;
};if _dfa :=_df ("\u0061\u006c\u0067\u0039","\u0055",48,_cagbg .U );_dfa !=nil {return _dfa ;};var _abf [16]byte ;if _ ,_ege :=_d .ReadFull (_bc .Reader ,_abf [:]);_ege !=nil {return _ege ;};_gdc :=_abf [0:8];_bcc :=_abf [8:16];_bee :=_cagbg .U [:48];_fbc :=make ([]byte ,len (_eea )+len (_gdc )+len (_bee ));
_dbc :=copy (_fbc ,_eea );_dbc +=copy (_fbc [_dbc :],_gdc );_dbc +=copy (_fbc [_dbc :],_bee );_ecb ,_ggg :=_beb .alg2b (_cagbg .R ,_fbc ,_eea ,_bee );if _ggg !=nil {return _ggg ;};O :=make ([]byte ,len (_ecb )+len (_gdc )+len (_bcc ));_dbc =copy (O ,_ecb [:32]);
_dbc +=copy (O [_dbc :],_gdc );_dbc +=copy (O [_dbc :],_bcc );_cagbg .O =O ;_dbc =len (_eea );_dbc +=copy (_fbc [_dbc :],_bcc );_ecb ,_ggg =_beb .alg2b (_cagbg .R ,_fbc ,_eea ,_bee );if _ggg !=nil {return _ggg ;};_cabd ,_ggg :=_abd (_ecb [:32]);if _ggg !=nil {return _ggg ;
};_ebba :=make ([]byte ,_eg .BlockSize );_fae :=_b .NewCBCEncrypter (_cabd ,_ebba );OE :=make ([]byte ,32);_fae .CryptBlocks (OE ,_decc [:32]);_cagbg .OE =OE ;return nil ;};

// Permissions is a bitmask of access permissions for a PDF file.
type Permissions uint32 ;

// NewHandlerR6 creates a new standard security handler for R=5 and R=6.
func NewHandlerR6 ()StdHandler {return stdHandlerR6 {}};const _gag ="\x28\277\116\136\x4e\x75\x8a\x41\x64\000\x4e\x56\377"+"\xfa\001\010\056\x2e\x00\xb6\xd0\x68\076\x80\x2f\014"+"\251\xfe\x64\x53\x69\172";

// NewHandlerR4 creates a new standard security handler for R<=4.
func NewHandlerR4 (id0 string ,length int )StdHandler {return stdHandlerR4 {ID0 :id0 ,Length :length }};func _abd (_aab []byte )(_b .Block ,error ){_daa ,_eae :=_eg .NewCipher (_aab );if _eae !=nil {_de .Log .Error ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u0063\u006f\u0075\u006c\u0064\u0020\u006e\u006f\u0074\u0020\u0063\u0072\u0065\u0061\u0074\u0065\u0020A\u0045\u0053\u0020\u0063\u0069p\u0068\u0065r\u003a\u0020\u0025\u0076",_eae );
return nil ,_eae ;};return _daa ,nil ;};