//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

// Package extractor is used for quickly extracting PDF content through a simple interface.
// Currently offers functionality for extracting textual content.
package extractor ;import (_cd "bytes";_df "errors";_ge "fmt";_f "github.com/unidoc/unipdf/v3/common";_fb "github.com/unidoc/unipdf/v3/contentstream";_ea "github.com/unidoc/unipdf/v3/core";_g "github.com/unidoc/unipdf/v3/internal/license";_eee "github.com/unidoc/unipdf/v3/internal/textencoding";
_ce "github.com/unidoc/unipdf/v3/internal/transform";_cc "github.com/unidoc/unipdf/v3/model";_d "golang.org/x/image/draw";_fc "golang.org/x/text/unicode/norm";_ee "image";_ca "image/color";_b "io";_da "math";_a "reflect";_ec "regexp";_be "sort";_ag "strings";
_e "unicode";_fa "unicode/utf8";);func (_geac *stateStack )top ()*textState {if _geac .empty (){return nil ;};return (*_geac )[_geac .size ()-1];};func (_deea *wordBag )absorb (_dcfg *wordBag ){_dbfa :=_dcfg .makeRemovals ();for _fbgee ,_aagg :=range _dcfg ._fggf {for _ ,_bedg :=range _aagg {_deea .pullWord (_bedg ,_fbgee ,_dbfa );
};};_dcfg .applyRemovals (_dbfa );};func (_fbfb *textObject )setTextRenderMode (_cbca int ){if _fbfb ==nil {return ;};_fbfb ._fea ._cggbec =RenderMode (_cbca );};func (_eaafc *shapesState )cubicTo (_bbae ,_abag ,_fced ,_fbba ,_fecg ,_deba float64 ){if _bfgf {_f .Log .Info ("\u0063\u0075\u0062\u0069\u0063\u0054\u006f\u003a");
};_eaafc .addPoint (_fecg ,_deba );};func (_edea rectRuling )checkWidth (_eeceg ,_fcbd float64 )(float64 ,bool ){_ddeb :=_fcbd -_eeceg ;_gcea :=_ddeb <=_accd ;return _ddeb ,_gcea ;};func _cfddc (_ccdf []*wordBag )[]*wordBag {if len (_ccdf )<=1{return _ccdf ;
};if _cfbd {_f .Log .Info ("\u006d\u0065\u0072\u0067\u0065\u0057\u006f\u0072\u0064B\u0061\u0067\u0073\u003a");};_be .Slice (_ccdf ,func (_dfeg ,_cdeaa int )bool {_bafa ,_efbbb :=_ccdf [_dfeg ],_ccdf [_cdeaa ];_bbfb :=_bafa .Width ()*_bafa .Height ();_eebeg :=_efbbb .Width ()*_efbbb .Height ();
if _bbfb !=_eebeg {return _bbfb > _eebeg ;};if _bafa .Height ()!=_efbbb .Height (){return _bafa .Height ()> _efbbb .Height ();};return _dfeg < _cdeaa ;});var _adcba []*wordBag ;_gdaa :=make (intSet );for _gfag :=0;_gfag < len (_ccdf );_gfag ++{if _gdaa .has (_gfag ){continue ;
};_dged :=_ccdf [_gfag ];for _dfde :=_gfag +1;_dfde < len (_ccdf );_dfde ++{if _gdaa .has (_gfag ){continue ;};_cgcc :=_ccdf [_dfde ];_agea :=_dged .PdfRectangle ;_agea .Llx -=_dged ._aafec ;if _dbbe (_agea ,_cgcc .PdfRectangle ){_dged .absorb (_cgcc );
_gdaa .add (_dfde );};};_adcba =append (_adcba ,_dged );};if len (_ccdf )!=len (_adcba )+len (_gdaa ){_f .Log .Error ("\u006d\u0065\u0072ge\u0057\u006f\u0072\u0064\u0042\u0061\u0067\u0073\u003a \u0025d\u2192%\u0064 \u0061\u0062\u0073\u006f\u0072\u0062\u0065\u0064\u003d\u0025\u0064",len (_ccdf ),len (_adcba ),len (_gdaa ));
};return _adcba ;};func (_gaf *TextMarkArray )getTextMarkAtOffset (_gdc int )*TextMark {for _ ,_bagf :=range _gaf ._aecad {if _bagf .Offset ==_gdc {return &_bagf ;};};return nil ;};func (_bfcbc *textWord )addDiacritic (_fdagag string ){_afcbg :=_bfcbc ._bacg [len (_bfcbc ._bacg )-1];
_afcbg ._fecec +=_fdagag ;_afcbg ._fecec =_fc .NFKC .String (_afcbg ._fecec );};func (_gcfag *textWord )bbox ()_cc .PdfRectangle {return _gcfag .PdfRectangle };func (_dff *imageExtractContext )extractXObjectImage (_dag *_ea .PdfObjectName ,_gfd _fb .GraphicsState ,_cef *_cc .PdfPageResources )error {_ggb ,_ :=_cef .GetXObjectByName (*_dag );
if _ggb ==nil {return nil ;};_aag ,_edgb :=_dff ._bdb [_ggb ];if !_edgb {_gge ,_bdfe :=_cef .GetXObjectImageByName (*_dag );if _bdfe !=nil {return _bdfe ;};if _gge ==nil {return nil ;};_eeb ,_bdfe :=_gge .ToImage ();if _bdfe !=nil {return _bdfe ;};var _bba _ee .Image ;
if _gge .Mask !=nil {if _bba ,_bdfe =_gegg (_gge .Mask ,_ca .Opaque );_bdfe !=nil {_f .Log .Debug ("\u0057\u0041\u0052\u004e\u003a \u0063\u006f\u0075\u006c\u0064 \u006eo\u0074\u0020\u0067\u0065\u0074\u0020\u0065\u0078\u0070\u006c\u0069\u0063\u0069\u0074\u0020\u0069\u006d\u0061\u0067e\u0020\u006d\u0061\u0073\u006b\u002e\u0020\u004f\u0075\u0074\u0070\u0075\u0074\u0020\u006d\u0061\u0079\u0020\u0062\u0065\u0020\u0069\u006e\u0063o\u0072\u0072\u0065\u0063\u0074\u002e");
};}else if _gge .SMask !=nil {_bba ,_bdfe =_cdfaa (_gge .SMask ,_ca .Opaque );if _bdfe !=nil {_f .Log .Debug ("W\u0041\u0052\u004e\u003a\u0020\u0063\u006f\u0075\u006c\u0064\u0020\u006e\u006f\u0074\u0020\u0067\u0065\u0074\u0020\u0073\u006f\u0066\u0074\u0020\u0069\u006da\u0067e\u0020\u006d\u0061\u0073k\u002e\u0020O\u0075\u0074\u0070\u0075\u0074\u0020\u006d\u0061\u0079\u0020\u0062\u0065\u0020\u0069\u006e\u0063\u006f\u0072\u0072\u0065\u0063\u0074\u002e");
};};if _bba !=nil {_bce ,_eaa :=_eeb .ToGoImage ();if _eaa !=nil {return _eaa ;};_bce =_gffa (_bce ,_bba );switch _gge .ColorSpace .String (){case "\u0044\u0065\u0076\u0069\u0063\u0065\u0047\u0072\u0061\u0079","\u0049n\u0064\u0065\u0078\u0065\u0064":_eeb ,_eaa =_cc .ImageHandling .NewGrayImageFromGoImage (_bce );
if _eaa !=nil {return _eaa ;};default:_eeb ,_eaa =_cc .ImageHandling .NewImageFromGoImage (_bce );if _eaa !=nil {return _eaa ;};};};_aag =&cachedImage {_cge :_eeb ,_bfa :_gge .ColorSpace };_dff ._bdb [_ggb ]=_aag ;};_geg :=_aag ._cge ;_fca :=_aag ._bfa ;
_cdgc ,_dgfd :=_fca .ImageToRGB (*_geg );if _dgfd !=nil {return _dgfd ;};_f .Log .Debug ("@\u0044\u006f\u0020\u0043\u0054\u004d\u003a\u0020\u0025\u0073",_gfd .CTM .String ());_aba :=ImageMark {Image :&_cdgc ,Width :_gfd .CTM .ScalingFactorX (),Height :_gfd .CTM .ScalingFactorY (),Angle :_gfd .CTM .Angle ()};
_aba .X ,_aba .Y =_gfd .CTM .Translation ();_dff ._gbg =append (_dff ._gbg ,_aba );_dff ._gbc ++;return nil ;};

// ExtractTextWithStats works like ExtractText but returns the number of characters in the output
// (`numChars`) and the number of characters that were not decoded (`numMisses`).
func (_dcgb *Extractor )ExtractTextWithStats ()(_gbfe string ,_eeagg int ,_effa int ,_cbac error ){_fcgc ,_eeagg ,_effa ,_cbac :=_dcgb .ExtractPageText ();if _cbac !=nil {return "",_eeagg ,_effa ,_cbac ;};return _fcgc .Text (),_eeagg ,_effa ,nil ;};func (_bfea *wordBag )getDepthIdx (_gefb float64 )int {_ccdcf :=_bfea .depthIndexes ();
_caeg :=_bbgdd (_gefb );if _caeg < _ccdcf [0]{return _ccdcf [0];};if _caeg > _ccdcf [len (_ccdcf )-1]{return _ccdcf [len (_ccdcf )-1];};return _caeg ;};func _egfc (_acdfc *textLine )bool {_ddcc :=true ;_fceba :=-1;for _ ,_eadac :=range _acdfc ._ebgf {for _ ,_ecbc :=range _eadac ._bacg {_bcga :=_ecbc ._adgc ;
if _fceba ==-1{_fceba =_bcga ;}else {if _fceba !=_bcga {_ddcc =false ;break ;};};};};return _ddcc ;};func (_gde *PageText )computeViews (){if _gde ._ceef ._gbdd {_gde ._dabb =_gde .getText ();return ;};_fbge :=_gde .getParagraphs ();_bbbf :=new (_cd .Buffer );
_fbge .writeText (_bbbf );_gde ._dabb =_bbbf .String ();_gde ._baag =_fbge .toTextMarks ();_gde ._gcaca =_fbge .tables ();if _ababc {_f .Log .Info ("\u0063\u006f\u006dpu\u0074\u0065\u0056\u0069\u0065\u0077\u0073\u003a\u0020\u0074\u0061\u0062\u006c\u0065\u0073\u003d\u0025\u0064",len (_gde ._gcaca ));
};};func _adcbb (_efgf []*textLine ,_agad ,_gfed float64 )[]*textLine {var _ccebg []*textLine ;for _ ,_dgebb :=range _efgf {if _agad ==-1{if _dgebb ._gdca > _gfed {_ccebg =append (_ccebg ,_dgebb );};}else {if _dgebb ._gdca > _gfed &&_dgebb ._gdca < _agad {_ccebg =append (_ccebg ,_dgebb );
};};};return _ccebg ;};func _aegc (_geddd _ce .Point )_ce .Matrix {return _ce .TranslationMatrix (_geddd .X ,_geddd .Y )};func _fcdd (_cdfb byte )bool {for _ ,_cebd :=range _gbgd {if []byte (_cebd )[0]==_cdfb {return true ;};};return false ;};func _abgg (_dcgc *textLine )float64 {return _dcgc ._ebgf [0].Llx };
func (_aeccf *wordBag )blocked (_adcc *textWord )bool {if _adcc .Urx < _aeccf .Llx {_bfed :=_bceag (_adcc .PdfRectangle );_bgdf :=_fdedd (_aeccf .PdfRectangle );if _aeccf ._eccg .blocks (_bfed ,_bgdf ){if _aefce {_f .Log .Info ("\u0062\u006c\u006f\u0063ke\u0064\u0020\u2190\u0078\u003a\u0020\u0025\u0073\u0020\u0025\u0073",_adcc ,_aeccf );
};return true ;};}else if _aeccf .Urx < _adcc .Llx {_fcca :=_bceag (_aeccf .PdfRectangle );_fddd :=_fdedd (_adcc .PdfRectangle );if _aeccf ._eccg .blocks (_fcca ,_fddd ){if _aefce {_f .Log .Info ("b\u006co\u0063\u006b\u0065\u0064\u0020\u0078\u2192\u0020:\u0020\u0025\u0073\u0020%s",_adcc ,_aeccf );
};return true ;};};if _adcc .Ury < _aeccf .Lly {_ccfgf :=_gdce (_adcc .PdfRectangle );_cfbga :=_eeggg (_aeccf .PdfRectangle );if _aeccf ._dbca .blocks (_ccfgf ,_cfbga ){if _aefce {_f .Log .Info ("\u0062\u006c\u006f\u0063ke\u0064\u0020\u2190\u0079\u003a\u0020\u0025\u0073\u0020\u0025\u0073",_adcc ,_aeccf );
};return true ;};}else if _aeccf .Ury < _adcc .Lly {_debag :=_gdce (_aeccf .PdfRectangle );_ccdce :=_eeggg (_adcc .PdfRectangle );if _aeccf ._dbca .blocks (_debag ,_ccdce ){if _aefce {_f .Log .Info ("b\u006co\u0063\u006b\u0065\u0064\u0020\u0079\u2192\u0020:\u0020\u0025\u0073\u0020%s",_adcc ,_aeccf );
};return true ;};};return false ;};func _bfefe (_gddf ,_dgebe _ce .Point )rulingKind {_faffb :=_da .Abs (_gddf .X -_dgebe .X );_ebff :=_da .Abs (_gddf .Y -_dgebe .Y );return _dbafe (_faffb ,_ebff ,_ecgcc );};func (_fdadf lineRuling )asRuling ()(*ruling ,bool ){_fcbf :=ruling {_befeb :_fdadf ._eead ,Color :_fdadf .Color ,_ceab :_fgdbf };
switch _fdadf ._eead {case _degg :_fcbf ._gdaaf =_fdadf .xMean ();_fcbf ._bfbdb =_da .Min (_fdadf ._gadd .Y ,_fdadf ._beac .Y );_fcbf ._bedbg =_da .Max (_fdadf ._gadd .Y ,_fdadf ._beac .Y );case _gfbdf :_fcbf ._gdaaf =_fdadf .yMean ();_fcbf ._bfbdb =_da .Min (_fdadf ._gadd .X ,_fdadf ._beac .X );
_fcbf ._bedbg =_da .Max (_fdadf ._gadd .X ,_fdadf ._beac .X );default:_f .Log .Error ("\u0062\u0061\u0064\u0020pr\u0069\u006d\u0061\u0072\u0079\u0020\u006b\u0069\u006e\u0064\u003d\u0025\u0064",_fdadf ._eead );return nil ,false ;};return &_fcbf ,true ;};
func (_cggb *imageExtractContext )extractContentStreamImages (_eae string ,_egb *_cc .PdfPageResources )error {_gaa :=_fb .NewContentStreamParser (_eae );_gbf ,_bcd :=_gaa .Parse ();if _bcd !=nil {return _bcd ;};if _cggb ._bdb ==nil {_cggb ._bdb =map[*_ea .PdfObjectStream ]*cachedImage {};
};if _cggb ._dec ==nil {_cggb ._dec =&ImageExtractOptions {};};_gae :=_fb .NewContentStreamProcessor (*_gbf );_gae .AddHandler (_fb .HandlerConditionEnumAllOperands ,"",_cggb .processOperand );return _gae .Process (_egb );};func _feedf (_bbgf map[int ][]float64 ){if len (_bbgf )<=1{return ;
};_gggdd :=_gfff (_bbgf );if _ababc {_f .Log .Info ("\u0066i\u0078C\u0065\u006c\u006c\u0073\u003a \u006b\u0065y\u0073\u003d\u0025\u002b\u0076",_gggdd );};var _cgdae ,_ecfad int ;for _cgdae ,_ecfad =range _gggdd {if _bbgf [_ecfad ]!=nil {break ;};};for _fbcge ,_cacg :=range _gggdd [_cgdae :]{_egdga :=_bbgf [_cacg ];
if _egdga ==nil {continue ;};if _ababc {_ge .Printf ("\u0025\u0034\u0064\u003a\u0020\u006b\u0030\u003d\u0025\u0064\u0020\u006b1\u003d\u0025\u0064\u000a",_cgdae +_fbcge ,_ecfad ,_cacg );};_adffa :=_bbgf [_cacg ];if _adffa [len (_adffa )-1]> _egdga [0]{_adffa [len (_adffa )-1]=_egdga [0];
_bbgf [_ecfad ]=_adffa ;};_ecfad =_cacg ;};};

// TextMark represents extracted text on a page with information regarding both textual content,
// formatting (font and size) and positioning.
// It is the smallest unit of text on a PDF page, typically a single character.
//
// getBBox() in test_text.go shows how to compute bounding boxes of substrings of extracted text.
// The following code extracts the text on PDF page `page` into `text` then finds the bounding box
// `bbox` of substring `term` in `text`.
//
//	ex, _ := New(page)
//	// handle errors
//	pageText, _, _, err := ex.ExtractPageText()
//	// handle errors
//	text := pageText.Text()
//	textMarks := pageText.Marks()
//
//		start := strings.Index(text, term)
//	 end := start + len(term)
//	 spanMarks, err := textMarks.RangeOffset(start, end)
//	 // handle errors
//	 bbox, ok := spanMarks.BBox()
//	 // handle errors
type TextMark struct{

// Text is the extracted text.
Text string ;

// Original is the text in the PDF. It has not been decoded like `Text`.
Original string ;

// BBox is the bounding box of the text.
BBox _cc .PdfRectangle ;

// Font is the font the text was drawn with.
Font *_cc .PdfFont ;

// FontSize is the font size the text was drawn with.
FontSize float64 ;

// Offset is the offset of the start of TextMark.Text in the extracted text. If you do this
//   text, textMarks := pageText.Text(), pageText.Marks()
//   marks := textMarks.Elements()
// then marks[i].Offset is the offset of marks[i].Text in text.
Offset int ;

// Meta is set true for spaces and line breaks that we insert in the extracted text. We insert
// spaces (line breaks) when we see characters that are over a threshold horizontal (vertical)
//  distance  apart. See wordJoiner (lineJoiner) in PageText.computeViews().
Meta bool ;

// FillColor is the fill color of the text.
// The color is nil for spaces and line breaks (i.e. the Meta field is true).
FillColor _ca .Color ;

// StrokeColor is the stroke color of the text.
// The color is nil for spaces and line breaks (i.e. the Meta field is true).
StrokeColor _ca .Color ;

// Orientation is the text orientation
Orientation int ;

// DirectObject is the underlying PdfObject (Text Object) that represents the visible texts. This is introduced to get
// a simple access to the TextObject in case editing or replacment of some text is needed. E.g during redaction.
DirectObject _ea .PdfObject ;

// ObjString is a decoded string operand of a text-showing operator. It has the same value as `Text` attribute except
// when many glyphs are represented with the same Text Object that contains multiple length string operand in which case
// ObjString spans more than one character string that falls in different TextMark objects.
ObjString []string ;Tw float64 ;Th float64 ;Tc float64 ;Index int ;_eaee bool ;_cbfb *TextTable ;};func _efgd (_bcac *wordBag ,_dgdcf float64 ,_bgag ,_fcgec rulingList ,_ccgf bool )[]*wordBag {var _dbgdb []*wordBag ;for _ ,_eabb :=range _bcac .depthIndexes (){_aeaeb :=false ;
for !_bcac .empty (_eabb ){_ecdae :=_bcac .firstReadingIndex (_eabb );_fefa :=_bcac .firstWord (_ecdae );_dcgafc :=_ccaa (_fefa ,_dgdcf ,_bgag ,_fcgec );_bcac .removeWord (_fefa ,_ecdae );if _gafg {_f .Log .Info ("\u0066\u0069\u0072\u0073\u0074\u0057\u006f\u0072\u0064\u0020\u005e\u005e^\u005e\u0020\u0025\u0073",_fefa .String ());
};for _ggccb :=true ;_ggccb ;_ggccb =_aeaeb {_aeaeb =false ;_fffaa :=_gade *_dcgafc ._aafec ;_abca :=_dcbe *_dcgafc ._aafec ;if _ccgf {_abca =_da .MaxFloat64 ;};_bfcg :=_bdgd *_dcgafc ._aafec ;if _gafg {_f .Log .Info ("\u0070a\u0072a\u0057\u006f\u0072\u0064\u0073\u0020\u0064\u0065\u0070\u0074\u0068 \u0025\u002e\u0032\u0066 \u002d\u0020\u0025\u002e\u0032f\u0020\u006d\u0061\u0078\u0049\u006e\u0074\u0072\u0061\u0044\u0065\u0070\u0074\u0068\u0047\u0061\u0070\u003d\u0025\u002e\u0032\u0066\u0020\u006d\u0061\u0078\u0049\u006e\u0074\u0072\u0061R\u0065\u0061\u0064\u0069\u006e\u0067\u0047\u0061p\u003d\u0025\u002e\u0032\u0066",_dcgafc .minDepth (),_dcgafc .maxDepth (),_bfcg ,_abca );
};if _bcac .scanBand ("\u0076\u0065\u0072\u0074\u0069\u0063\u0061\u006c",_dcgafc ,_geeea (_ebeg ,0),_dcgafc .minDepth ()-_bfcg ,_dcgafc .maxDepth ()+_bfcg ,_bfacf ,false ,false )> 0{_aeaeb =true ;};if _bcac .scanBand ("\u0068\u006f\u0072\u0069\u007a\u006f\u006e\u0074\u0061\u006c",_dcgafc ,_geeea (_ebeg ,_abca ),_dcgafc .minDepth (),_dcgafc .maxDepth (),_fcab ,false ,false )> 0{_aeaeb =true ;
};if _aeaeb {continue ;};_cebc :=_bcac .scanBand ("",_dcgafc ,_geeea (_bcgc ,_fffaa ),_dcgafc .minDepth (),_dcgafc .maxDepth (),_agfe ,true ,false );if _cebc > 0{_ebdeb :=(_dcgafc .maxDepth ()-_dcgafc .minDepth ())/_dcgafc ._aafec ;if (_cebc > 1&&float64 (_cebc )> 0.3*_ebdeb )||_cebc <=10{if _bcac .scanBand ("\u006f\u0074\u0068e\u0072",_dcgafc ,_geeea (_bcgc ,_fffaa ),_dcgafc .minDepth (),_dcgafc .maxDepth (),_agfe ,false ,true )> 0{_aeaeb =true ;
};};};};_dbgdb =append (_dbgdb ,_dcgafc );};};return _dbgdb ;};type textState struct{_eegf float64 ;_eeef float64 ;_bbgg float64 ;_egdf float64 ;_fcba float64 ;_cggbec RenderMode ;_daca float64 ;_dbcb *_cc .PdfFont ;_aeef _cc .PdfRectangle ;_fgcd int ;
_baad int ;};

// TableInfo gets table information of the textmark `tm`.
func (_gcde *TextMark )TableInfo ()(*TextTable ,[][]int ){if !_gcde ._eaee {return nil ,nil ;};_dffg :=_gcde ._cbfb ;_edbe :=_dffg .getCellInfo (*_gcde );return _dffg ,_edbe ;};func _dbbde (_ecfg []*textLine ,_bcefa string )string {var _defgd _ag .Builder ;
_fagaef :=0.0;for _gafb ,_ffcba :=range _ecfg {_ffefc :=_ffcba .text ();_cdbgd :=_ffcba ._gdca ;if _gafb < len (_ecfg )-1{_fagaef =_ecfg [_gafb +1]._gdca ;}else {_fagaef =0.0;};_defgd .WriteString (_bcefa );_defgd .WriteString (_ffefc );if _fagaef !=_cdbgd {_defgd .WriteString ("\u000a");
}else {_defgd .WriteString ("\u0020");};};return _defgd .String ();};func (_bfgcf *textTable )compositeColCorridors ()map[int ][]float64 {_bfdf :=make (map[int ][]float64 ,_bfgcf ._ebed );if _ababc {_f .Log .Info ("\u0063\u006f\u006d\u0070o\u0073\u0069\u0074\u0065\u0043\u006f\u006c\u0043\u006f\u0072r\u0069d\u006f\u0072\u0073\u003a\u0020\u0077\u003d%\u0064\u0020",_bfgcf ._ebed );
};for _eaabd :=0;_eaabd < _bfgcf ._ebed ;_eaabd ++{_bfdf [_eaabd ]=nil ;};return _bfdf ;};func (_cdfae gridTile )complete ()bool {return _cdfae .numBorders ()==4};type fontEntry struct{_aedf *_cc .PdfFont ;_ggea int64 ;};func _ceea (_cdgd *TextMarkArray ,_aecg int ,_ggc int )int {_eece :=_cdgd .Elements ();
_gfef :=_aecg -1;_fac :=_aecg +1;_gccc :=-1;if _gfef >=0{_eaf :=_eece [_gfef ];_bde :=_eaf .ObjString ;_gbd :=len (_bde );_faee :=_eaf .Index ;if _faee +1< _gbd {return _gfef ;};};if _fac < len (_eece ){_edc :=_eece [_fac ];_eaad :=_edc .ObjString ;if _eaad [0]!=_edc .Text {return _fac ;
};};if _gccc ==-1&&_eece [_aecg ].Text =="\u0020"{return _gfef ;};return _gccc ;};func (_gfccc compositeCell )split (_babc ,_cfafd []float64 )*textTable {_defb :=len (_babc )+1;_dfbda :=len (_cfafd )+1;if _ababc {_f .Log .Info ("\u0063\u006f\u006d\u0070\u006f\u0073\u0069t\u0065\u0043\u0065l\u006c\u002e\u0073\u0070l\u0069\u0074\u003a\u0020\u0025\u0064\u0020\u0078\u0020\u0025\u0064\u000a\u0009\u0063\u006f\u006d\u0070\u006f\u0073\u0069\u0074\u0065\u003d\u0025\u0073\u000a"+"\u0009\u0072\u006f\u0077\u0043\u006f\u0072\u0072\u0069\u0064\u006f\u0072\u0073=\u0025\u0036\u002e\u0032\u0066\u000a\t\u0063\u006f\u006c\u0043\u006f\u0072\u0072\u0069\u0064\u006f\u0072\u0073\u003d%\u0036\u002e\u0032\u0066",_dfbda ,_defb ,_gfccc ,_babc ,_cfafd );
_ge .Printf ("\u0020\u0020\u0020\u0020\u0025\u0064\u0020\u0070\u0061\u0072\u0061\u0073\u000a",len (_gfccc .paraList ));for _ccec ,_eadfg :=range _gfccc .paraList {_ge .Printf ("\u0025\u0034\u0064\u003a\u0020\u0025\u0073\u000a",_ccec ,_eadfg .String ());
};_ge .Printf ("\u0020\u0020\u0020\u0020\u0025\u0064\u0020\u006c\u0069\u006e\u0065\u0073\u000a",len (_gfccc .lines ()));for _gfbfe ,_gfdcg :=range _gfccc .lines (){_ge .Printf ("\u0025\u0034\u0064\u003a\u0020\u0025\u0073\u000a",_gfbfe ,_gfdcg );};};_babc =_abeee (_babc ,_gfccc .Ury ,_gfccc .Lly );
_cfafd =_abeee (_cfafd ,_gfccc .Llx ,_gfccc .Urx );_bdgb :=make (map[uint64 ]*textPara ,_dfbda *_defb );_eaae :=textTable {_ebed :_dfbda ,_fcec :_defb ,_dcdb :_bdgb };_egbad :=_gfccc .paraList ;_be .Slice (_egbad ,func (_fedf ,_ceaee int )bool {_fbgec ,_aacec :=_egbad [_fedf ],_egbad [_ceaee ];
_ggec ,_abgd :=_fbgec .Lly ,_aacec .Lly ;if _ggec !=_abgd {return _ggec < _abgd ;};return _fbgec .Llx < _aacec .Llx ;});_cgbd :=make (map[uint64 ]_cc .PdfRectangle ,_dfbda *_defb );for _accf ,_gbfg :=range _babc [1:]{_dfgg :=_babc [_accf ];for _cdeg ,_facg :=range _cfafd [1:]{_cebe :=_cfafd [_cdeg ];
_cgbd [_dfgde (_cdeg ,_accf )]=_cc .PdfRectangle {Llx :_cebe ,Urx :_facg ,Lly :_gbfg ,Ury :_dfgg };};};if _ababc {_f .Log .Info ("\u0063\u006f\u006d\u0070\u006f\u0073\u0069\u0074\u0065\u0043\u0065l\u006c\u002e\u0073\u0070\u006c\u0069\u0074\u003a\u0020\u0072e\u0063\u0074\u0073");
_ge .Printf ("\u0020\u0020\u0020\u0020");for _cbef :=0;_cbef < _dfbda ;_cbef ++{_ge .Printf ("\u0025\u0033\u0030\u0064\u002c\u0020",_cbef );};_ge .Println ();for _faaef :=0;_faaef < _defb ;_faaef ++{_ge .Printf ("\u0020\u0020\u0025\u0032\u0064\u003a",_faaef );
for _bfbec :=0;_bfbec < _dfbda ;_bfbec ++{_ge .Printf ("\u00256\u002e\u0032\u0066\u002c\u0020",_cgbd [_dfgde (_bfbec ,_faaef )]);};_ge .Println ();};};_dafc :=func (_bfcc *textLine )(int ,int ){for _ccgfe :=0;_ccgfe < _defb ;_ccgfe ++{for _bebgd :=0;_bebgd < _dfbda ;
_bebgd ++{if _dbbe (_cgbd [_dfgde (_bebgd ,_ccgfe )],_bfcc .PdfRectangle ){return _bebgd ,_ccgfe ;};};};return -1,-1;};_ceefb :=make (map[uint64 ][]*textLine ,_dfbda *_defb );for _ ,_fdfb :=range _egbad .lines (){_gafbg ,_dffca :=_dafc (_fdfb );if _gafbg < 0{continue ;
};_ceefb [_dfgde (_gafbg ,_dffca )]=append (_ceefb [_dfgde (_gafbg ,_dffca )],_fdfb );};for _bacc :=0;_bacc < len (_babc )-1;_bacc ++{_aedad :=_babc [_bacc ];_eebg :=_babc [_bacc +1];for _gcba :=0;_gcba < len (_cfafd )-1;_gcba ++{_afgcg :=_cfafd [_gcba ];
_eadfgd :=_cfafd [_gcba +1];_edfa :=_cc .PdfRectangle {Llx :_afgcg ,Urx :_eadfgd ,Lly :_eebg ,Ury :_aedad };_ffge :=_ceefb [_dfgde (_gcba ,_bacc )];if len (_ffge )==0{continue ;};_agcf :=_bfge (_edfa ,_ffge );_eaae .put (_gcba ,_bacc ,_agcf );};};return &_eaae ;
};func _cfee (_fged []compositeCell )[]float64 {var _dgba []*textLine ;_adgeb :=0;for _ ,_aagb :=range _fged {_adgeb +=len (_aagb .paraList );_dgba =append (_dgba ,_aagb .lines ()...);};_be .Slice (_dgba ,func (_afaee ,_ecgac int )bool {_caee ,_bdaea :=_dgba [_afaee ],_dgba [_ecgac ];
_bgdg ,_fbac :=_caee ._gdca ,_bdaea ._gdca ;if !_bfee (_bgdg -_fbac ){return _bgdg < _fbac ;};return _caee .Llx < _bdaea .Llx ;});if _ababc {_ge .Printf ("\u0020\u0020\u0020 r\u006f\u0077\u0042\u006f\u0072\u0064\u0065\u0072\u0073:\u0020%\u0064 \u0070a\u0072\u0061\u0073\u0020\u0025\u0064\u0020\u006c\u0069\u006e\u0065\u0073\u000a",_adgeb ,len (_dgba ));
for _edbbd ,_gedf :=range _dgba {_ge .Printf ("\u0025\u0038\u0064\u003a\u0020\u0025\u0073\u000a",_edbbd ,_gedf );};};var _ebbde []float64 ;_cacbf :=_dgba [0];var _cgdbg [][]*textLine ;_eabbcg :=[]*textLine {_cacbf };for _ceeaf ,_egga :=range _dgba [1:]{if _egga .Ury < _cacbf .Lly {_gfefb :=0.5*(_egga .Ury +_cacbf .Lly );
if _ababc {_ge .Printf ("\u0025\u0034\u0064\u003a\u0020\u0025\u0036\u002e\u0032\u0066\u0020\u003c\u0020\u0025\u0036.\u0032f\u0020\u0062\u006f\u0072\u0064\u0065\u0072\u003d\u0025\u0036\u002e\u0032\u0066\u000a"+"\u0009\u0020\u0071\u003d\u0025\u0073\u000a\u0009\u0020p\u003d\u0025\u0073\u000a",_ceeaf ,_egga .Ury ,_cacbf .Lly ,_gfefb ,_cacbf ,_egga );
};_ebbde =append (_ebbde ,_gfefb );_cgdbg =append (_cgdbg ,_eabbcg );_eabbcg =nil ;};_eabbcg =append (_eabbcg ,_egga );if _egga .Lly < _cacbf .Lly {_cacbf =_egga ;};};if len (_eabbcg )> 0{_cgdbg =append (_cgdbg ,_eabbcg );};if _ababc {_ge .Printf (" \u0020\u0020\u0020\u0020\u0020\u0020 \u0072\u006f\u0077\u0043\u006f\u0072\u0072\u0069\u0064o\u0072\u0073\u003d%\u0036.\u0032\u0066\u000a",_ebbde );
};if _ababc {_f .Log .Info ("\u0072\u006f\u0077\u003d\u0025\u0064",len (_fged ));for _cadgc ,_dcacc :=range _fged {_ge .Printf ("\u0025\u0034\u0064\u003a\u0020\u0025\u0073\u000a",_cadgc ,_dcacc );};_f .Log .Info ("\u0067r\u006f\u0075\u0070\u0073\u003d\u0025d",len (_cgdbg ));
for _ecbbc ,_edaa :=range _cgdbg {_ge .Printf ("\u0025\u0034\u0064\u003a\u0020\u0025\u0064\u000a",_ecbbc ,len (_edaa ));for _dfdf ,_babee :=range _edaa {_ge .Printf ("\u0025\u0038\u0064\u003a\u0020\u0025\u0073\u000a",_dfdf ,_babee );};};};_eedcb :=true ;
for _bfaf ,_ddggd :=range _cgdbg {_fdgc :=true ;for _gefad ,_agaad :=range _fged {if _ababc {_ge .Printf ("\u0020\u0020\u0020\u007e\u007e\u007e\u0067\u0072\u006f\u0075\u0070\u0020\u0025\u0064\u0020\u006f\u0066\u0020\u0025\u0064\u0020\u0063\u0065\u006cl\u0020\u0025\u0064\u0020\u006ff\u0020\u0025d\u0020\u0025\u0073\u000a",_bfaf ,len (_cgdbg ),_gefad ,len (_fged ),_agaad );
};if !_agaad .hasLines (_ddggd ){if _ababc {_ge .Printf ("\u0020\u0020\u0020\u0021\u0021\u0021\u0067\u0072\u006f\u0075\u0070\u0020\u0025d\u0020\u006f\u0066\u0020\u0025\u0064 \u0063\u0065\u006c\u006c\u0020\u0025\u0064\u0020\u006f\u0066\u0020\u0025\u0064 \u004f\u0055\u0054\u000a",_bfaf ,len (_cgdbg ),_gefad ,len (_fged ));
};_fdgc =false ;break ;};};if !_fdgc {_eedcb =false ;break ;};};if !_eedcb {if _ababc {_f .Log .Info ("\u0072\u006f\u0077\u0020\u0063o\u0072\u0072\u0069\u0064\u006f\u0072\u0073\u0020\u0064\u006f\u006e\u0027\u0074 \u0073\u0070\u0061\u006e\u0020\u0061\u006c\u006c\u0020\u0063\u0065\u006c\u006c\u0073\u0020\u0069\u006e\u0020\u0072\u006f\u0077\u002e\u0020\u0069\u0067\u006e\u006f\u0072\u0069\u006eg");
};_ebbde =nil ;};if _ababc &&_ebbde !=nil {_ge .Printf ("\u0020\u0020\u0020\u0020\u0020\u0020\u0020\u0020\u002a\u002a*\u0072\u006f\u0077\u0043\u006f\u0072\u0072i\u0064\u006f\u0072\u0073\u003d\u0025\u0036\u002e\u0032\u0066\u000a",_ebbde );};return _ebbde ;
};const _cga =1.0/1000.0;func (_gcec *shapesState )clearPath (){_gcec ._edde =nil ;_gcec ._ffdgg =false ;if _bfgf {_f .Log .Info ("\u0043\u004c\u0045A\u0052\u003a\u0020\u0073\u0073\u003d\u0025\u0073",_gcec );};};

// GetContentStreamOps returns the contentStreamOps field of `pt`.
func (_fcge *PageText )GetContentStreamOps ()*_fb .ContentStreamOperations {return _fcge ._edeb };func _egag (_bddda []int )[]int {_ecgeb :=make ([]int ,len (_bddda ));for _dgfac ,_eagg :=range _bddda {_ecgeb [len (_bddda )-1-_dgfac ]=_eagg ;};return _ecgeb ;
};func (_cdfc paraList )findTables (_fgef []gridTiling )[]*textTable {_cdfc .addNeighbours ();_be .Slice (_cdfc ,func (_cbbec ,_faagb int )bool {return _gaeb (_cdfc [_cbbec ],_cdfc [_faagb ])< 0});var _cabf []*textTable ;if _fdfcb {_gbge :=_cdfc .findGridTables (_fgef );
_cabf =append (_cabf ,_gbge ...);};if _gbaa {_ceafc :=_cdfc .findTextTables ();_cabf =append (_cabf ,_ceafc ...);};return _cabf ;};func _egbc (_beae ,_cgfbg _ce .Point )rulingKind {_daag :=_da .Abs (_beae .X -_cgfbg .X );_dbad :=_da .Abs (_beae .Y -_cgfbg .Y );
return _dbafe (_daag ,_dbad ,_ebdc );};func _ccaa (_abcb *textWord ,_bdbb float64 ,_gedg ,_baef rulingList )*wordBag {_dcbf :=_bbgdd (_abcb ._bfff );_acab :=[]*textWord {_abcb };_cacf :=wordBag {_fggf :map[int ][]*textWord {_dcbf :_acab },PdfRectangle :_abcb .PdfRectangle ,_aafec :_abcb ._dcca ,_afbd :_bdbb ,_eccg :_gedg ,_dbca :_baef };
return &_cacf ;};func (_aeaebb *textWord )absorb (_adgcf *textWord ){_aeaebb .PdfRectangle =_cbba (_aeaebb .PdfRectangle ,_adgcf .PdfRectangle );_aeaebb ._bacg =append (_aeaebb ._bacg ,_adgcf ._bacg ...);};func _fdedd (_dffgb _cc .PdfRectangle )*ruling {return &ruling {_befeb :_degg ,_gdaaf :_dffgb .Llx ,_bfbdb :_dffgb .Lly ,_bedbg :_dffgb .Ury };
};func (_bfbad paraList )sortReadingOrder (){_f .Log .Trace ("\u0073\u006fr\u0074\u0052\u0065\u0061\u0064i\u006e\u0067\u004f\u0072\u0064e\u0072\u003a\u0020\u0070\u0061\u0072\u0061\u0073\u003d\u0025\u0064\u0020\u003d\u003d\u003d\u003d\u003d\u003d\u003d\u003d\u003d\u003d\u003d\u0078\u003d\u003d\u003d\u003d\u003d\u003d\u003d\u003d\u003d\u003d\u003d\u003d\u003d",len (_bfbad ));
if len (_bfbad )<=1{return ;};_bfbad .computeEBBoxes ();_be .Slice (_bfbad ,func (_fgfba ,_baee int )bool {return _acfg (_bfbad [_fgfba ],_bfbad [_baee ])<=0});};func (_gfdd *textPara )bbox ()_cc .PdfRectangle {return _gfdd .PdfRectangle };func _eeggg (_dffga _cc .PdfRectangle )*ruling {return &ruling {_befeb :_gfbdf ,_gdaaf :_dffga .Lly ,_bfbdb :_dffga .Llx ,_bedbg :_dffga .Urx };
};func (_ggfe *shapesState )stroke (_bdab *[]pathSection ){_cfad :=pathSection {_gdeb :_ggfe ._edde ,Color :_ggfe ._abef .getStrokeColor ()};*_bdab =append (*_bdab ,_cfad );if _bggcg {_ge .Printf ("\u0020 \u0020\u0020S\u0054\u0052\u004fK\u0045\u003a\u0020\u0025\u0064\u0020\u0073t\u0072\u006f\u006b\u0065\u0073\u0020s\u0073\u003d\u0025\u0073\u0020\u0063\u006f\u006c\u006f\u0072\u003d%\u002b\u0076\u0020\u0025\u0036\u002e\u0032\u0066\u000a",len (*_bdab ),_ggfe ,_ggfe ._abef .getStrokeColor (),_cfad .bbox ());
if _dedc {for _fdbe ,_feef :=range _ggfe ._edde {_ge .Printf ("\u0025\u0038\u0064\u003a\u0020\u0025\u0073\u000a",_fdbe ,_feef );if _fdbe ==10{break ;};};};};};

// BidiText represents a bidi text organized in its visual order
// with base direction of the text.
type BidiText struct{_eec string ;_cdg string ;};func (_fabb paraList )sortTopoOrder (){_geacd :=_fabb .topoOrder ();_fabb .reorder (_geacd )};func (_ebae paraList )topoOrder ()[]int {if _fgfdc {_f .Log .Info ("\u0074\u006f\u0070\u006f\u004f\u0072\u0064\u0065\u0072\u003a");
};_cbffc :=len (_ebae );_fcagdg :=make ([]bool ,_cbffc );_gcceg :=make ([]int ,0,_cbffc );_dgfgg :=_ebae .llyOrdering ();var _dffad func (_geaad int );_dffad =func (_abfd int ){_fcagdg [_abfd ]=true ;for _edbaa :=0;_edbaa < _cbffc ;_edbaa ++{if !_fcagdg [_edbaa ]{if _ebae .readBefore (_dgfgg ,_abfd ,_edbaa ){_dffad (_edbaa );
};};};_gcceg =append (_gcceg ,_abfd );};for _fcda :=0;_fcda < _cbffc ;_fcda ++{if !_fcagdg [_fcda ]{_dffad (_fcda );};};return _egag (_gcceg );};func (_ggdd paraList )computeEBBoxes (){if _ebfec {_f .Log .Info ("\u0063o\u006dp\u0075\u0074\u0065\u0045\u0042\u0042\u006f\u0078\u0065\u0073\u003a");
};for _ ,_agba :=range _ggdd {_agba ._abfg =_agba .PdfRectangle ;};_acfb :=_ggdd .yNeighbours (0);for _cdbc ,_egcbc :=range _ggdd {_bdda :=_egcbc ._abfg ;_gada ,_gggggb :=-1.0e9,+1.0e9;for _ ,_baff :=range _acfb [_egcbc ]{_cgddf :=_ggdd [_baff ]._abfg ;
if _cgddf .Urx < _bdda .Llx {_gada =_da .Max (_gada ,_cgddf .Urx );}else if _bdda .Urx < _cgddf .Llx {_gggggb =_da .Min (_gggggb ,_cgddf .Llx );};};for _gdgbd ,_fgdeb :=range _ggdd {_abega :=_fgdeb ._abfg ;if _cdbc ==_gdgbd ||_abega .Ury > _bdda .Lly {continue ;
};if _gada <=_abega .Llx &&_abega .Llx < _bdda .Llx {_bdda .Llx =_abega .Llx ;}else if _abega .Urx <=_gggggb &&_bdda .Urx < _abega .Urx {_bdda .Urx =_abega .Urx ;};};if _ebfec {_ge .Printf ("\u0025\u0034\u0064\u003a %\u0036\u002e\u0032\u0066\u2192\u0025\u0036\u002e\u0032\u0066\u0020\u0025\u0071\u000a",_cdbc ,_egcbc ._abfg ,_bdda ,_bcfdgc (_egcbc .text (),50));
};_egcbc ._abfg =_bdda ;};if _dabf {for _ ,_bcada :=range _ggdd {_bcada .PdfRectangle =_bcada ._abfg ;};};};func _gffa (_abga ,_bcfbb _ee .Image )_ee .Image {_agegd ,_cggc :=_bcfbb .Bounds ().Size (),_abga .Bounds ().Size ();_cgae ,_acgef :=_agegd .X ,_agegd .Y ;
if _cggc .X > _cgae {_cgae =_cggc .X ;};if _cggc .Y > _acgef {_acgef =_cggc .Y ;};_dcddc :=_ee .Rect (0,0,_cgae ,_acgef );if _agegd .X !=_cgae ||_agegd .Y !=_acgef {_ffgfg :=_ee .NewRGBA (_dcddc );_d .BiLinear .Scale (_ffgfg ,_dcddc ,_abga ,_bcfbb .Bounds (),_d .Over ,nil );
_bcfbb =_ffgfg ;};if _cggc .X !=_cgae ||_cggc .Y !=_acgef {_cgceb :=_ee .NewRGBA (_dcddc );_d .BiLinear .Scale (_cgceb ,_dcddc ,_abga ,_abga .Bounds (),_d .Over ,nil );_abga =_cgceb ;};_fdede :=_ee .NewRGBA (_dcddc );_d .DrawMask (_fdede ,_dcddc ,_abga ,_ee .Point {},_bcfbb ,_ee .Point {},_d .Over );
return _fdede ;};func _daeg (_cfeggf *PageText )error {_egfb :=_g .GetLicenseKey ();if _egfb !=nil &&_egfb .IsLicensed ()||_ddd {return nil ;};_ge .Printf ("\u0055\u006e\u006c\u0069\u0063\u0065\u006e\u0073\u0065\u0064\u0020c\u006f\u0070\u0079\u0020\u006f\u0066\u0020\u0055\u006e\u0069P\u0044\u0046\u000a");
_ge .Println ("-\u0020\u0047\u0065\u0074\u0020\u0061\u0020\u0066\u0072e\u0065\u0020\u0074\u0072\u0069\u0061\u006c l\u0069\u0063\u0065\u006es\u0065\u0020\u006f\u006e\u0020\u0068\u0074\u0074\u0070s:\u002f\u002fu\u006e\u0069\u0064\u006f\u0063\u002e\u0069\u006f");
return _df .New ("\u0075\u006e\u0069\u0070d\u0066\u0020\u006c\u0069\u0063\u0065\u006e\u0073\u0065\u0020c\u006fd\u0065\u0020\u0072\u0065\u0071\u0075\u0069r\u0065\u0064");};func _acedb (_fefaf ,_bfdce _ce .Point )bool {_affec :=_da .Abs (_fefaf .X -_bfdce .X );
_agfdc :=_da .Abs (_fefaf .Y -_bfdce .Y );return _aggdf (_agfdc ,_affec );};type textResult struct{_aebg PageText ;_bbgc int ;_dbgb int ;};func _aecd (_eadcg structElement )[]structElement {_eegfc :=[]structElement {};for _ ,_dcdf :=range _eadcg ._gedge {for _ ,_aaaa :=range _dcdf ._gedge {for _ ,_ebad :=range _aaaa ._gedge {if _ebad ._eeagb =="\u004c"{_eegfc =append (_eegfc ,_ebad );
};};};};return _eegfc ;};type intSet map[int ]struct{};func (_eaddd *ruling )encloses (_fceg ,_fccbc float64 )bool {return _eaddd ._bfbdb -_cefge <=_fceg &&_fccbc <=_eaddd ._bedbg +_cefge ;};func (_acggg rulingList )bbox ()_cc .PdfRectangle {var _cddd _cc .PdfRectangle ;
if len (_acggg )==0{_f .Log .Error ("r\u0075\u006c\u0069\u006e\u0067\u004ci\u0073\u0074\u002e\u0062\u0062\u006f\u0078\u003a\u0020n\u006f\u0020\u0072u\u006ci\u006e\u0067\u0073");return _cc .PdfRectangle {};};if _acggg [0]._befeb ==_gfbdf {_cddd .Llx ,_cddd .Urx =_acggg .secMinMax ();
_cddd .Lly ,_cddd .Ury =_acggg .primMinMax ();}else {_cddd .Llx ,_cddd .Urx =_acggg .primMinMax ();_cddd .Lly ,_cddd .Ury =_acggg .secMinMax ();};return _cddd ;};var _ddd =false ;func _dcdda (_gbeb int ,_bffcd map[int ][]float64 )([]int ,int ){_dggg :=make ([]int ,_gbeb );
_eeefg :=0;for _becfe :=0;_becfe < _gbeb ;_becfe ++{_dggg [_becfe ]=_eeefg ;_eeefg +=len (_bffcd [_becfe ])+1;};return _dggg ,_eeefg ;};func (_cbee *textTable )bbox ()_cc .PdfRectangle {return _cbee .PdfRectangle };func (_baefe paraList )list ()[]*list {var _fbdgg []*textLine ;
var _ebbbe []*textLine ;for _ ,_ggccc :=range _baefe {_fbda :=_ggccc .getListLines ();_fbdgg =append (_fbdgg ,_fbda ...);_ebbbe =append (_ebbbe ,_ggccc ._geffa ...);};_cbaf :=_ggbdb (_fbdgg );_edbeba :=_deaa (_ebbbe ,_cbaf );return _edbeba ;};const (_gdda =1.0e-6;
_fafg =1.0e-4;_fdba =10;_fbadd =6;_bafg =0.5;_dbdf =0.12;_cadf =0.19;_dfbbca =0.04;_caffdc =0.04;_bdgd =1.0;_bfacf =0.04;_cdgab =12;_dcbe =0.4;_fcab =0.7;_gade =1.0;_agfe =0.1;_fedd =1.4;_cgcge =0.46;_dgad =0.02;_ccfc =0.2;_fbec =0.5;_bebeb =4;_bbd =4.0;
_adgg =6;_adfg =0.3;_afgf =0.01;_acca =0.02;_cdbg =2;_dcbfb =2;_bedb =500;_ecgcc =4.0;_acde =4.0;_ebdc =0.05;_aeeda =0.1;_cefge =2.0;_accd =2.0;_cfceg =1.5;_ebee =3.0;_edeba =0.25;);

// ExtractFonts returns all font information from the page extractor, including
// font name, font type, the raw data of the embedded font file (if embedded), font descriptor and more.
//
// The argument `previousPageFonts` is used when trying to build a complete font catalog for multiple pages or the entire document.
// The entries from `previousPageFonts` are added to the returned result unless already included in the page, i.e. no duplicate entries.
//
// NOTE: If previousPageFonts is nil, all fonts from the page will be returned. Use it when building up a full list of fonts for a document or page range.
func (_gcf *Extractor )ExtractFonts (previousPageFonts *PageFonts )(*PageFonts ,error ){_ded :=PageFonts {};_gbaf :=_ded .extractPageResourcesToFont (_gcf ._gf );if _gbaf !=nil {return nil ,_gbaf ;};if previousPageFonts !=nil {for _ ,_cdga :=range previousPageFonts .Fonts {if !_gdg (_ded .Fonts ,_cdga .FontName ){_ded .Fonts =append (_ded .Fonts ,_cdga );
};};};return &PageFonts {Fonts :_ded .Fonts },nil ;};func (_bgf *PageFonts )extractPageResourcesToFont (_aaf *_cc .PdfPageResources )error {if _aaf .Font ==nil {return _df .New (_faef );};_daa ,_dbg :=_ea .GetDict (_aaf .Font );if !_dbg {return _df .New (_bcf );
};for _ ,_dgfa :=range _daa .Keys (){var (_cfa =true ;_gfc []byte ;_ddc string ;);_aeg ,_dfd :=_aaf .GetFontByName (_dgfa );if !_dfd {return _df .New (_gca );};_caa ,_edfc :=_cc .NewPdfFontFromPdfObject (_aeg );if _edfc !=nil {return _edfc ;};_bgg :=_caa .FontDescriptor ();
_gcc :=_caa .FontDescriptor ().FontName .String ();_eea :=_caa .Subtype ();if _gdg (_bgf .Fonts ,_gcc ){continue ;};if len (_caa .ToUnicode ())==0{_cfa =false ;};if _bgg .FontFile !=nil {if _ef ,_dgdc :=_ea .GetStream (_bgg .FontFile );_dgdc {_gfc ,_edfc =_ea .DecodeStream (_ef );
if _edfc !=nil {return _edfc ;};_ddc =_gcc +"\u002e\u0070\u0066\u0062";};}else if _bgg .FontFile2 !=nil {if _gbe ,_deg :=_ea .GetStream (_bgg .FontFile2 );_deg {_gfc ,_edfc =_ea .DecodeStream (_gbe );if _edfc !=nil {return _edfc ;};_ddc =_gcc +"\u002e\u0074\u0074\u0066";
};}else if _bgg .FontFile3 !=nil {if _gggd ,_bec :=_ea .GetStream (_bgg .FontFile3 );_bec {_gfc ,_edfc =_ea .DecodeStream (_gggd );if _edfc !=nil {return _edfc ;};_ddc =_gcc +"\u002e\u0063\u0066\u0066";};};if len (_ddc )< 1{_f .Log .Debug (_bdff );};_bad :=Font {FontName :_gcc ,PdfFont :_caa ,IsCID :_caa .IsCID (),IsSimple :_caa .IsSimple (),ToUnicode :_cfa ,FontType :_eea ,FontData :_gfc ,FontFileName :_ddc ,FontDescriptor :_bgg };
_bgf .Fonts =append (_bgf .Fonts ,_bad );};return nil ;};func (_fbeca *textTable )computeBbox ()_cc .PdfRectangle {var _aeba _cc .PdfRectangle ;_beeef :=false ;for _bbdg :=0;_bbdg < _fbeca ._fcec ;_bbdg ++{for _gfbg :=0;_gfbg < _fbeca ._ebed ;_gfbg ++{_gegb :=_fbeca .get (_gfbg ,_bbdg );
if _gegb ==nil {continue ;};if !_beeef {_aeba =_gegb .PdfRectangle ;_beeef =true ;}else {_aeba =_cbba (_aeba ,_gegb .PdfRectangle );};};};return _aeba ;};func (_cadfe *textPara )taken ()bool {return _cadfe ==nil ||_cadfe ._dddg };func _cca (_eg []string ,_bc int ,_dae string )int {_fe :=_bc ;
for ;_fe < len (_eg );_fe ++{if _eg [_fe ]!=_dae {return _fe ;};};return _fe ;};func (_ebea *textMark )bbox ()_cc .PdfRectangle {return _ebea .PdfRectangle };

// Editor represents a document editor object
type Editor struct{_cff *_cc .PdfReader };func _gc (_dac int )bool {return (_dac &1)==0};func _eacd (_ecdga map[float64 ]map[float64 ]gridTile )[]float64 {_befd :=make ([]float64 ,0,len (_ecdga ));for _affa :=range _ecdga {_befd =append (_befd ,_affa );
};_be .Float64s (_befd );_gfaf :=len (_befd );for _gecd :=0;_gecd < _gfaf /2;_gecd ++{_befd [_gecd ],_befd [_gfaf -1-_gecd ]=_befd [_gfaf -1-_gecd ],_befd [_gecd ];};return _befd ;};func (_gdfgg *textLine )text ()string {var _ddf []string ;for _ ,_gefba :=range _gdfgg ._ebgf {if _gefba ._fabaa {_ddf =append (_ddf ,"\u0020");
};_ddf =append (_ddf ,_gefba ._ggeag );};_bga :=_ag .Join (_ddf ,"");_bcfc :=_afe ([]rune (_bga ));return _bcfc ._eec ;};func (_gggg *shapesState )drawRectangle (_geaa ,_bbce ,_feac ,_cadd float64 ){if _bfgf {_cebb :=_gggg .devicePoint (_geaa ,_bbce );
_bddd :=_gggg .devicePoint (_geaa +_feac ,_bbce +_cadd );_dgcd :=_cc .PdfRectangle {Llx :_cebb .X ,Lly :_cebb .Y ,Urx :_bddd .X ,Ury :_bddd .Y };_f .Log .Info ("d\u0072a\u0077\u0052\u0065\u0063\u0074\u0061\u006e\u0067l\u0065\u003a\u0020\u00256.\u0032\u0066",_dgcd );
};_gggg .newSubPath ();_gggg .moveTo (_geaa ,_bbce );_gggg .lineTo (_geaa +_feac ,_bbce );_gggg .lineTo (_geaa +_feac ,_bbce +_cadd );_gggg .lineTo (_geaa ,_bbce +_cadd );_gggg .closePath ();};func (_eded *ruling )equals (_dacca *ruling )bool {return _eded ._befeb ==_dacca ._befeb &&_cgbe (_eded ._gdaaf ,_dacca ._gdaaf )&&_cgbe (_eded ._bfbdb ,_dacca ._bfbdb )&&_cgbe (_eded ._bedbg ,_dacca ._bedbg );
};func _dagce (_aefd *list )[]*textLine {for _ ,_edeg :=range _aefd ._bafe {switch _edeg ._eddg {case "\u004c\u0042\u006fd\u0079":if len (_edeg ._faeg )!=0{return _edeg ._faeg ;};return _dagce (_edeg );case "\u0053\u0070\u0061\u006e":return _edeg ._faeg ;
case "I\u006e\u006c\u0069\u006e\u0065\u0053\u0068\u0061\u0070\u0065":return _edeg ._faeg ;};};return nil ;};func _gfff (_eaead map[int ][]float64 )[]int {_cbae :=make ([]int ,len (_eaead ));_bcaa :=0;for _cebce :=range _eaead {_cbae [_bcaa ]=_cebce ;_bcaa ++;
};_be .Ints (_cbae );return _cbae ;};

// ToTextMark returns the public view of `tm`.
func (_eccde *textMark )ToTextMark ()TextMark {return TextMark {Text :_eccde ._fecec ,Original :_eccde ._bdea ,BBox :_eccde ._ebge ,Font :_eccde ._dagf ,FontSize :_eccde ._ggde ,FillColor :_eccde ._dbgd ,StrokeColor :_eccde ._eeeb ,Orientation :_eccde ._ggbe ,DirectObject :_eccde ._fbce ,ObjString :_eccde ._fedc ,Tw :_eccde .Tw ,Th :_eccde .Th ,Tc :_eccde ._bccbd ,Index :_eccde ._gbecb };
};func _feddf (_gcge ,_gdbe _ce .Point )bool {_gegf :=_da .Abs (_gcge .X -_gdbe .X );_cfgf :=_da .Abs (_gcge .Y -_gdbe .Y );return _aggdf (_gegf ,_cfgf );};func (_cecb paraList )findTableGrid (_defc gridTiling )(*textTable ,map[*textPara ]struct{}){_eabg :=len (_defc ._gdec );
_gedgf :=len (_defc ._daeaf );_fdce :=textTable {_fggcd :true ,_ebed :_eabg ,_fcec :_gedgf ,_dcdb :make (map[uint64 ]*textPara ,_eabg *_gedgf ),_dccb :make (map[uint64 ]compositeCell ,_eabg *_gedgf )};_fdce .PdfRectangle =_defc .PdfRectangle ;_bbagg :=make (map[*textPara ]struct{});
_fbbfc :=int ((1.0-_adfg )*float64 (_eabg *_gedgf ));_geafe :=0;if _ffbg {_f .Log .Info ("\u0066\u0069\u006e\u0064Ta\u0062\u006c\u0065\u0047\u0072\u0069\u0064\u003a\u0020\u0025\u0064\u0020\u0078\u0020%\u0064",_eabg ,_gedgf );};for _bafac ,_gaaf :=range _defc ._daeaf {_edcc ,_feecc :=_defc ._bceeb [_gaaf ];
if !_feecc {continue ;};for _bgfac ,_cdgaf :=range _defc ._gdec {_bacff ,_gadag :=_edcc [_cdgaf ];if !_gadag {continue ;};_bdcdd :=_cecb .inTile (_bacff );if len (_bdcdd )==0{_geafe ++;if _geafe > _fbbfc {if _ffbg {_f .Log .Info ("\u0021\u006e\u0075m\u0045\u006d\u0070\u0074\u0079\u003d\u0025\u0064",_geafe );
};return nil ,nil ;};}else {_fdce .putComposite (_bgfac ,_bafac ,_bdcdd ,_bacff .PdfRectangle );for _ ,_agga :=range _bdcdd {_bbagg [_agga ]=struct{}{};};};};};_agaaf :=0;for _efcb :=0;_efcb < _eabg ;_efcb ++{_bgfcb :=_fdce .get (_efcb ,0);if _bgfcb ==nil ||!_bgfcb ._gaebg {_agaaf ++;
};};if _agaaf ==0{if _ffbg {_f .Log .Info ("\u0021\u006e\u0075m\u0048\u0065\u0061\u0064\u0065\u0072\u003d\u0030");};return nil ,nil ;};_ccgfecb :=_fdce .reduceTiling (_defc ,_ebee );_ccgfecb =_ccgfecb .subdivide ();return _ccgfecb ,_bbagg ;};

// String returns a description of `k`.
func (_egcf markKind )String ()string {_abdce ,_ddfgd :=_ebdf [_egcf ];if !_ddfgd {return _ge .Sprintf ("\u004e\u006f\u0074\u0020\u0061\u0020\u006d\u0061\u0072k\u003a\u0020\u0025\u0064",_egcf );};return _abdce ;};func _egae (_gagc *paraList )map[int ][]*textLine {_aeeb :=map[int ][]*textLine {};
for _ ,_ecdg :=range *_gagc {for _ ,_ggcb :=range _ecdg ._geffa {if !_egfc (_ggcb ){_f .Log .Debug ("g\u0072\u006f\u0075p\u004c\u0069\u006e\u0065\u0073\u003a\u0020\u0054\u0068\u0065\u0020\u0074\u0065\u0078\u0074\u0020\u006c\u0069\u006e\u0065\u0020\u0063\u006f\u006e\u0074a\u0069\u006e\u0073 \u006d\u006f\u0072\u0065\u0020\u0074\u0068\u0061\u006e\u0020\u006f\u006e\u0065 \u006d\u0063\u0069\u0064 \u006e\u0075\u006d\u0062e\u0072\u002e\u0020\u0049\u0074\u0020\u0073\u0068\u006f\u0075\u006c\u0064\u0020\u0062\u0065\u0020\u0073p\u006c\u0069\u0074\u002e");
continue ;};_aaef :=_ggcb ._ebgf [0]._bacg [0]._adgc ;_aeeb [_aaef ]=append (_aeeb [_aaef ],_ggcb );};if _ecdg ._dcde !=nil {_fbea :=_ecdg ._dcde ._dcdb ;for _ ,_aaff :=range _fbea {for _ ,_gbad :=range _aaff ._geffa {if !_egfc (_gbad ){_f .Log .Debug ("g\u0072\u006f\u0075p\u004c\u0069\u006e\u0065\u0073\u003a\u0020\u0054\u0068\u0065\u0020\u0074\u0065\u0078\u0074\u0020\u006c\u0069\u006e\u0065\u0020\u0063\u006f\u006e\u0074a\u0069\u006e\u0073 \u006d\u006f\u0072\u0065\u0020\u0074\u0068\u0061\u006e\u0020\u006f\u006e\u0065 \u006d\u0063\u0069\u0064 \u006e\u0075\u006d\u0062e\u0072\u002e\u0020\u0049\u0074\u0020\u0073\u0068\u006f\u0075\u006c\u0064\u0020\u0062\u0065\u0020\u0073p\u006c\u0069\u0074\u002e");
continue ;};_aeae :=_gbad ._ebgf [0]._bacg [0]._adgc ;_aeeb [_aeae ]=append (_aeeb [_aeae ],_gbad );};};};};return _aeeb ;};func (_ecdd *PageText )getText ()string {_acae :="";_dgfg :=len (_ecdd ._fagg );for _cceb :=0;_cceb < 360&&_dgfg > 0;_cceb +=90{_geffe :=make ([]*textMark ,0,len (_ecdd ._fagg )-_dgfg );
for _ ,_afgd :=range _ecdd ._fagg {if _afgd ._ggbe ==_cceb {_geffe =append (_geffe ,_afgd );};};if len (_geffe )> 0{_acae +=_aefda (_geffe ,_ecdd ._fccb );_dgfg -=len (_geffe );};};return _acae ;};func (_badg *shapesState )closePath (){if _badg ._ffdgg {_badg ._edde =append (_badg ._edde ,_bgdc (_badg ._ffefa ));
_badg ._ffdgg =false ;}else if len (_badg ._edde )==0{if _bfgf {_f .Log .Debug ("\u0063\u006c\u006f\u0073eP\u0061\u0074\u0068\u0020\u0077\u0069\u0074\u0068\u0020\u006e\u006f\u0020\u0070\u0061t\u0068");};_badg ._ffdgg =false ;return ;};_badg ._edde [len (_badg ._edde )-1].close ();
if _bfgf {_f .Log .Info ("\u0063\u006c\u006f\u0073\u0065\u0050\u0061\u0074\u0068\u003a\u0020\u0025\u0073",_badg );};};func (_ceaf *stateStack )size ()int {return len (*_ceaf )};func (_aabfe *textObject )reset (){_aabfe ._gfdfa =_ce .IdentityMatrix ();_aabfe ._bdc =_ce .IdentityMatrix ();
_aabfe ._dbge =nil ;};func (_aea *wordBag )firstReadingIndex (_dfgb int )int {_aggbe :=_aea .firstWord (_dfgb )._dcca ;_fbfcc :=float64 (_dfgb +1)*_fbadd ;_dffc :=_fbfcc +_bbd *_aggbe ;_fagae :=_dfgb ;for _ ,_gfbf :=range _aea .depthBand (_fbfcc ,_dffc ){if _bbade (_aea .firstWord (_gfbf ),_aea .firstWord (_fagae ))< 0{_fagae =_gfbf ;
};};return _fagae ;};

// PageText represents the layout of text on a device page.
type PageText struct{_fagg []*textMark ;_dabb string ;_baag []TextMark ;_gcaca []TextTable ;_fccb _cc .PdfRectangle ;_affb []pathSection ;_abae []pathSection ;_ebeb *_ea .PdfObject ;_fbcg _ea .PdfObject ;_edeb *_fb .ContentStreamOperations ;_ceef PageTextOptions ;
};

// String returns a human readable description of `path`.
func (_gdae *subpath )String ()string {_gbac :=_gdae ._cggf ;_cdd :=len (_gbac );if _cdd <=5{return _ge .Sprintf ("\u0025d\u003a\u0020\u0025\u0036\u002e\u0032f",_cdd ,_gbac );};return _ge .Sprintf ("\u0025d\u003a\u0020\u0025\u0036.\u0032\u0066\u0020\u0025\u0036.\u0032f\u0020.\u002e\u002e\u0020\u0025\u0036\u002e\u0032f",_cdd ,_gbac [0],_gbac [1],_gbac [_cdd -1]);
};

// ApplyArea processes the page text only within the specified area `bbox`.
// Each time ApplyArea is called, it updates the result set in `pt`.
// Can be called multiple times in a row with different bounding boxes.
func (_fbcd *PageText )ApplyArea (bbox _cc .PdfRectangle ){_aecga :=make ([]*textMark ,0,len (_fbcd ._fagg ));for _ ,_cab :=range _fbcd ._fagg {if _aadgd (_cab .bbox (),bbox ){_aecga =append (_aecga ,_cab );};};var _edfd paraList ;_cdea :="";_cedf :=len (_aecga );
for _bedd :=0;_bedd < 360&&_cedf > 0;_bedd +=90{_def :=make ([]*textMark ,0,len (_aecga )-_cedf );for _ ,_abdc :=range _aecga {if _abdc ._ggbe ==_bedd {_def =append (_def ,_abdc );};};if len (_def )> 0{if _fbcd ._ceef ._gbdd {_cdea +=_aefda (_def ,_fbcd ._fccb );
}else {_fgda :=_ggbb (_def ,_fbcd ._fccb ,nil ,nil ,_fbcd ._ceef ._fcdcf );_edfd =append (_edfd ,_fgda ...);};_cedf -=len (_def );};};if _fbcd ._ceef ._gbdd {_fbcd ._dabb =_cdea ;}else {_fbaf :=new (_cd .Buffer );_edfd .writeText (_fbaf );_fbcd ._dabb =_fbaf .String ();
_fbcd ._baag =_edfd .toTextMarks ();_fbcd ._gcaca =_edfd .tables ();};};type rulingList []*ruling ;

// String returns a description of `l`.
func (_ggdc *textLine )String ()string {return _ge .Sprintf ("\u0025\u002e2\u0066\u0020\u0025\u0036\u002e\u0032\u0066\u0020\u0066\u006f\u006e\u0074\u0073\u0069\u007a\u0065\u003d\u0025\u002e\u0032\u0066\u0020\"%\u0073\u0022",_ggdc ._gdca ,_ggdc .PdfRectangle ,_ggdc ._fcgeg ,_ggdc .text ());
};func (_abdf compositeCell )String ()string {_aedfea :="";if len (_abdf .paraList )> 0{_aedfea =_bcfdgc (_abdf .paraList .merge ().text (),50);};return _ge .Sprintf ("\u0025\u0036\u002e\u0032\u0066\u0020\u0025\u0064\u0020\u0070\u0061\u0072a\u0073\u0020\u0025\u0071",_abdf .PdfRectangle ,len (_abdf .paraList ),_aedfea );
};func (_bfdbc rulingList )toTilings ()(rulingList ,[]gridTiling ){_bfdbc .log ("\u0074o\u0054\u0069\u006c\u0069\u006e\u0067s");if len (_bfdbc )==0{return nil ,nil ;};_bfdbc =_bfdbc .tidied ("\u0061\u006c\u006c");_bfdbc .log ("\u0074\u0069\u0064\u0069\u0065\u0064");
_bbbba :=_bfdbc .toGrids ();_dcfgb :=make ([]gridTiling ,len (_bbbba ));for _defd ,_cdfe :=range _bbbba {_dcfgb [_defd ]=_cdfe .asTiling ();};return _bfdbc ,_dcfgb ;};

// Extractor stores and offers functionality for extracting content from PDF pages.
type Extractor struct{_dddf string ;_gf *_cc .PdfPageResources ;_cgg _cc .PdfRectangle ;_afc *_cc .PdfRectangle ;_cfb map[string ]fontEntry ;_cfd map[string ]textResult ;_ggg map[string ]textResult ;_gfe int64 ;_cgca int ;_acb *Options ;_fbe *_ea .PdfObject ;
_aa _ea .PdfObject ;_ccf []*_cc .PdfAnnotation ;};func (_dbeaf *textObject )getFontDict (_gbda string )(_afgc _ea .PdfObject ,_aafde error ){_cage :=_dbeaf ._baadd ;if _cage ==nil {_f .Log .Debug ("g\u0065\u0074\u0046\u006f\u006e\u0074D\u0069\u0063\u0074\u002e\u0020\u004eo\u0020\u0072\u0065\u0073\u006f\u0075\u0072c\u0065\u0073\u002e\u0020\u006e\u0061\u006d\u0065\u003d\u0025#\u0071",_gbda );
return nil ,nil ;};_afgc ,_ecbb :=_cage .GetFontByName (_ea .PdfObjectName (_gbda ));if !_ecbb {_f .Log .Debug ("\u0045R\u0052\u004fR\u003a\u0020\u0067\u0065t\u0046\u006f\u006et\u0044\u0069\u0063\u0074\u003a\u0020\u0046\u006f\u006et \u006e\u006f\u0074 \u0066\u006fu\u006e\u0064\u003a\u0020\u006e\u0061m\u0065\u003d%\u0023\u0071",_gbda );
return nil ,_df .New ("f\u006f\u006e\u0074\u0020no\u0074 \u0069\u006e\u0020\u0072\u0065s\u006f\u0075\u0072\u0063\u0065\u0073");};return _afgc ,nil ;};func _eebgg (_fgeac ,_dcfgc int )int {if _fgeac > _dcfgc {return _fgeac ;};return _dcfgc ;};var _defg string ="\u0028\u003f\u0069\u0029\u005e\u0028\u004d\u007b\u0030\u002c\u0033\u007d\u0029\u0028\u0043\u0028?\u003a\u0044\u007cM\u0029\u007c\u0044\u003f\u0043{\u0030\u002c\u0033\u007d\u0029\u0028\u0058\u0028\u003f\u003a\u004c\u007c\u0043\u0029\u007cL\u003f\u0058\u007b\u0030\u002c\u0033}\u0029\u0028\u0049\u0028\u003f\u003a\u0056\u007c\u0058\u0029\u007c\u0056\u003f\u0049\u007b\u0030\u002c\u0033\u007d\u0029\u0028\u005c\u0029\u007c\u005c\u002e\u0029\u007c\u005e\u005c\u0028\u0028\u004d\u007b\u0030\u002c\u0033\u007d\u0029\u0028\u0043\u0028\u003f\u003aD\u007cM\u0029\u007c\u0044\u003f\u0043\u007b\u0030\u002c\u0033\u007d\u0029\u0028\u0058\u0028?\u003a\u004c\u007c\u0043\u0029\u007c\u004c?\u0058\u007b0\u002c\u0033\u007d\u0029(\u0049\u0028\u003f\u003a\u0056|\u0058\u0029\u007c\u0056\u003f\u0049\u007b\u0030\u002c\u0033\u007d\u0029\u005c\u0029";


// Font represents the font properties on a PDF page.
type Font struct{PdfFont *_cc .PdfFont ;

// FontName represents Font Name from font properties.
FontName string ;

// FontType represents Font Subtype entry in the font dictionary inside page resources.
// Examples : type0, Type1, MMType1, Type3, TrueType, CIDFont.
FontType string ;

// ToUnicode is true if font provides a `ToUnicode` mapping.
ToUnicode bool ;

// IsCID is true if underlying font is a composite font.
// Composite font is represented by a font dictionary whose Subtype is `Type0`
IsCID bool ;

// IsSimple is true if font is simple font.
// A simple font is limited to only 8 bit (255) character codes.
IsSimple bool ;

// FontData represents the raw data of the embedded font file.
// It can have format TrueType (TTF), PostScript Font (PFB) or Compact Font Format (CCF).
// FontData value can be indicates from `FontFile`, `FontFile2` or `FontFile3` inside Font Descriptor.
// At most, only one of `FontFile`, `FontFile2` or `FontFile3` will be FontData value.
FontData []byte ;

// FontFileName is a name representing the font. it has format:
// (Font Name) + (Font Type Extension), example: helvetica.ttf.
FontFileName string ;

// FontDescriptor represents metrics and other attributes inside font properties from PDF Structure (Font Descriptor).
FontDescriptor *_cc .PdfFontDescriptor ;};func _ggef (_fegc _cc .PdfRectangle )rulingKind {_dbcbc :=_fegc .Width ();_acagd :=_fegc .Height ();if _dbcbc > _acagd {if _dbcbc >=_ecgcc {return _gfbdf ;};}else {if _acagd >=_ecgcc {return _degg ;};};return _dcbd ;
};func (_cbcaa *subpath )makeRectRuling (_cfdce _ca .Color )(*ruling ,bool ){if _effbc {_f .Log .Info ("\u006d\u0061\u006beR\u0065\u0063\u0074\u0052\u0075\u006c\u0069\u006e\u0067\u003a\u0020\u0070\u0061\u0074\u0068\u003d\u0025\u0076",_cbcaa );};_caegc :=_cbcaa ._cggf [:4];
_gbefa :=make (map[int ]rulingKind ,len (_caegc ));for _bdbbc ,_dced :=range _caegc {_fgaf :=_cbcaa ._cggf [(_bdbbc +1)%4];_gbefa [_bdbbc ]=_egbc (_dced ,_fgaf );if _effbc {_ge .Printf ("\u0025\u0034\u0064: \u0025\u0073\u0020\u003d\u0020\u0025\u0036\u002e\u0032\u0066\u0020\u002d\u0020\u0025\u0036\u002e\u0032\u0066",_bdbbc ,_gbefa [_bdbbc ],_dced ,_fgaf );
};};if _effbc {_ge .Printf ("\u0020\u0020\u0020\u006b\u0069\u006e\u0064\u0073\u003d\u0025\u002b\u0076\u000a",_gbefa );};var _gggb ,_cdegf []int ;for _cgad ,_caacf :=range _gbefa {switch _caacf {case _gfbdf :_cdegf =append (_cdegf ,_cgad );case _degg :_gggb =append (_gggb ,_cgad );
};};if _effbc {_ge .Printf ("\u0020\u0020 \u0068\u006f\u0072z\u0073\u003d\u0025\u0064\u0020\u0025\u002b\u0076\u000a",len (_cdegf ),_cdegf );_ge .Printf ("\u0020\u0020 \u0076\u0065\u0072t\u0073\u003d\u0025\u0064\u0020\u0025\u002b\u0076\u000a",len (_gggb ),_gggb );
};_gbbg :=(len (_cdegf )==2&&len (_gggb )==2)||(len (_cdegf )==2&&len (_gggb )==0&&_acedb (_caegc [_cdegf [0]],_caegc [_cdegf [1]]))||(len (_gggb )==2&&len (_cdegf )==0&&_feddf (_caegc [_gggb [0]],_caegc [_gggb [1]]));if _effbc {_ge .Printf (" \u0020\u0020\u0068\u006f\u0072\u007as\u003d\u0025\u0064\u0020\u0076\u0065\u0072\u0074\u0073=\u0025\u0064\u0020o\u006b=\u0025\u0074\u000a",len (_cdegf ),len (_gggb ),_gbbg );
};if !_gbbg {if _effbc {_f .Log .Error ("\u0021!\u006d\u0061\u006b\u0065R\u0065\u0063\u0074\u0052\u0075l\u0069n\u0067:\u0020\u0070\u0061\u0074\u0068\u003d\u0025v",_cbcaa );_ge .Printf (" \u0020\u0020\u0068\u006f\u0072\u007as\u003d\u0025\u0064\u0020\u0076\u0065\u0072\u0074\u0073=\u0025\u0064\u0020o\u006b=\u0025\u0074\u000a",len (_cdegf ),len (_gggb ),_gbbg );
};return &ruling {},false ;};if len (_gggb )==0{for _gafgd ,_abea :=range _gbefa {if _abea !=_gfbdf {_gggb =append (_gggb ,_gafgd );};};};if len (_cdegf )==0{for _faeb ,_fcce :=range _gbefa {if _fcce !=_degg {_cdegf =append (_cdegf ,_faeb );};};};if _effbc {_f .Log .Info ("\u006da\u006b\u0065R\u0065\u0063\u0074\u0052u\u006c\u0069\u006eg\u003a\u0020\u0068\u006f\u0072\u007a\u0073\u003d\u0025d \u0076\u0065\u0072t\u0073\u003d%\u0064\u0020\u0070\u006f\u0069\u006et\u0073\u003d%\u0064\u000a"+"\u0009\u0020\u0068o\u0072\u007a\u0073\u003d\u0025\u002b\u0076\u000a"+"\u0009\u0020\u0076e\u0072\u0074\u0073\u003d\u0025\u002b\u0076\u000a"+"\t\u0070\u006f\u0069\u006e\u0074\u0073\u003d\u0025\u002b\u0076",len (_cdegf ),len (_gggb ),len (_caegc ),_cdegf ,_gggb ,_caegc );
};var _fdffd ,_feee ,_caed ,_fgfaa _ce .Point ;if _caegc [_cdegf [0]].Y > _caegc [_cdegf [1]].Y {_caed ,_fgfaa =_caegc [_cdegf [0]],_caegc [_cdegf [1]];}else {_caed ,_fgfaa =_caegc [_cdegf [1]],_caegc [_cdegf [0]];};if _caegc [_gggb [0]].X > _caegc [_gggb [1]].X {_fdffd ,_feee =_caegc [_gggb [0]],_caegc [_gggb [1]];
}else {_fdffd ,_feee =_caegc [_gggb [1]],_caegc [_gggb [0]];};_aade :=_cc .PdfRectangle {Llx :_fdffd .X ,Urx :_feee .X ,Lly :_fgfaa .Y ,Ury :_caed .Y };if _aade .Llx > _aade .Urx {_aade .Llx ,_aade .Urx =_aade .Urx ,_aade .Llx ;};if _aade .Lly > _aade .Ury {_aade .Lly ,_aade .Ury =_aade .Ury ,_aade .Lly ;
};_decf :=rectRuling {PdfRectangle :_aade ,_bbcga :_ggef (_aade ),Color :_cfdce };if _decf ._bbcga ==_dcbd {if _effbc {_f .Log .Error ("\u006da\u006b\u0065\u0052\u0065\u0063\u0074\u0052\u0075\u006c\u0069\u006eg\u003a\u0020\u006b\u0069\u006e\u0064\u003d\u006e\u0069\u006c");
};return nil ,false ;};_fedda ,_adbca :=_decf .asRuling ();if !_adbca {if _effbc {_f .Log .Error ("\u006da\u006b\u0065\u0052\u0065c\u0074\u0052\u0075\u006c\u0069n\u0067:\u0020!\u0069\u0073\u0052\u0075\u006c\u0069\u006eg");};return nil ,false ;};if _bggcg {_ge .Printf ("\u0020\u0020\u0020\u0072\u003d\u0025\u0073\u000a",_fedda .String ());
};return _fedda ,true ;};

// Len returns the number of TextMarks in `ma`.
func (_dfg *TextMarkArray )Len ()int {if _dfg ==nil {return 0;};return len (_dfg ._aecad );};func (_dgaf rulingList )splitSec ()[]rulingList {_be .Slice (_dgaf ,func (_accb ,_fagad int )bool {_eade ,_gded :=_dgaf [_accb ],_dgaf [_fagad ];if _eade ._bfbdb !=_gded ._bfbdb {return _eade ._bfbdb < _gded ._bfbdb ;
};return _eade ._bedbg < _gded ._bedbg ;});_dgca :=make (map[*ruling ]struct{},len (_dgaf ));_cdbe :=func (_ecdb *ruling )rulingList {_gefc :=rulingList {_ecdb };_dgca [_ecdb ]=struct{}{};for _ ,_edcb :=range _dgaf {if _ ,_adbb :=_dgca [_edcb ];_adbb {continue ;
};for _ ,_accfad :=range _gefc {if _edcb .alignsSec (_accfad ){_gefc =append (_gefc ,_edcb );_dgca [_edcb ]=struct{}{};break ;};};};return _gefc ;};_adcg :=[]rulingList {_cdbe (_dgaf [0])};for _ ,_ccfec :=range _dgaf [1:]{if _ ,_afed :=_dgca [_ccfec ];
_afed {continue ;};_adcg =append (_adcg ,_cdbe (_ccfec ));};return _adcg ;};func (_bcbb *PageText )getParagraphs ()paraList {var _ddgf rulingList ;if _gefae {_bdagg :=_dafe (_bcbb ._affb );_ddgf =append (_ddgf ,_bdagg ...);};if _cdac {_bbag :=_cafa (_bcbb ._abae );
_ddgf =append (_ddgf ,_bbag ...);};_ddgf ,_faggd :=_ddgf .toTilings ();var _bed paraList ;_dfcc :=len (_bcbb ._fagg );for _bfdgf :=0;_bfdgf < 360&&_dfcc > 0;_bfdgf +=90{_bfb :=make ([]*textMark ,0,len (_bcbb ._fagg )-_dfcc );for _ ,_ffg :=range _bcbb ._fagg {if _ffg ._ggbe ==_bfdgf {_bfb =append (_bfb ,_ffg );
};};if len (_bfb )> 0{_ebb :=_ggbb (_bfb ,_bcbb ._fccb ,_ddgf ,_faggd ,_bcbb ._ceef ._fcdcf );_bed =append (_bed ,_ebb ...);_dfcc -=len (_bfb );};};return _bed ;};

// PageFonts represents extracted fonts on a PDF page.
type PageFonts struct{Fonts []Font ;};func _eebb (_eged *list )[]*list {var _dgeb []*list ;for _ ,_bddeb :=range _eged ._bafe {switch _bddeb ._eddg {case "\u004c\u0049":_ebfc :=_dagce (_bddeb );_feca :=_eebb (_bddeb );_ebcg :=_fdfcc (_ebfc ,"\u0062\u0075\u006c\u006c\u0065\u0074",_feca );
_gacdd :=_dbbde (_ebfc ,"");_ebcg ._eeff =_gacdd ;_dgeb =append (_dgeb ,_ebcg );case "\u004c\u0042\u006fd\u0079":return _eebb (_bddeb );case "\u004c":_gcfa :=_eebb (_bddeb );_dgeb =append (_dgeb ,_gcfa ...);return _dgeb ;};};return _dgeb ;};

// TableCell is a cell in a TextTable.
type TableCell struct{_cc .PdfRectangle ;

// Text is the extracted text.
Text string ;

// Marks returns the TextMarks corresponding to the text in Text.
Marks TextMarkArray ;};func _cefa (_bcae map[float64 ]map[float64 ]gridTile )[]float64 {_dccc :=make ([]float64 ,0,len (_bcae ));_cefe :=make (map[float64 ]struct{},len (_bcae ));for _ ,_bbff :=range _bcae {for _ebbc :=range _bbff {if _ ,_cdcdb :=_cefe [_ebbc ];
_cdcdb {continue ;};_dccc =append (_dccc ,_ebbc );_cefe [_ebbc ]=struct{}{};};};_be .Float64s (_dccc );return _dccc ;};func _ebeg (_abefg *wordBag ,_gecac *textWord ,_cbbf float64 )bool {return _gecac .Llx < _abefg .Urx +_cbbf &&_abefg .Llx -_cbbf < _gecac .Urx ;
};func (_bge *Editor )getMatches (_bab string ,_decd []int )(map[int ]Match ,map[int ][]*TextMarkArray ,error ){_ecd :=map[int ]Match {};_ecff :=map[int ][]*TextMarkArray {};for _ ,_fceb :=range _decd {_acc ,_dcdgg :=_bge ._cff .GetPage (_fceb );if _dcdgg !=nil {return nil ,nil ,_dcdgg ;
};_gfgc ,_dcdgg :=New (_acc );if _dcdgg !=nil {return nil ,nil ,_dcdgg ;};_ffe ,_ ,_ ,_dcdgg :=_gfgc .ExtractPageText ();if _dcdgg !=nil {return nil ,nil ,_dcdgg ;};_efc :=_ffe .Text ();_afbg ,_dcdgg :=_gdbf (_bab ,_efc );if _dcdgg !=nil {return nil ,nil ,_dcdgg ;
};if len (_afbg )==0{_f .Log .Info ("\u004e\u006f\u0020\u006d\u0061\u0074\u0063\u0068\u0020\u0066\u006f\u0075\u006e\u0064\u0020f\u006fr\u0020\u0025\u0073\u0020\u006f\u006e\u0020\u0070\u0061\u0067\u0065\u0020\u0025\u0064",_bab ,_fceb );};_egcb :=_ffe .Marks ();
_ggfc :=[]Box {};for _ ,_egef :=range _afbg {_ ,_fdff ,_ged :=_gcb (_egef ,_egcb ,_bab );if _ged !=nil {return nil ,nil ,_ged ;};_ggfc =append (_ggfc ,_fdff );};_fdcf :=Match {Pattern :_bab ,Indexes :_afbg ,Locations :_ggfc };_ecd [_fceb ]=_fdcf ;};return _ecd ,_ecff ,nil ;
};func _bfcgg (_agcg []*textMark ,_cbed _cc .PdfRectangle )[]*textWord {var _bcgd []*textWord ;var _dcgdg *textWord ;if _affbb {_f .Log .Info ("\u006d\u0061\u006beT\u0065\u0078\u0074\u0057\u006f\u0072\u0064\u0073\u003a\u0020\u0025\u0064\u0020\u006d\u0061\u0072\u006b\u0073",len (_agcg ));
};_ccgc :=func (){if _dcgdg !=nil {_bgdde :=_dcgdg .computeText ();if !_cffdg (_bgdde ){_dcgdg ._ggeag =_bgdde ;_bcgd =append (_bcgd ,_dcgdg );if _affbb {_f .Log .Info ("\u0061\u0064\u0064Ne\u0077\u0057\u006f\u0072\u0064\u003a\u0020\u0025\u0064\u003a\u0020\u0077\u006f\u0072\u0064\u003d\u0025\u0073",len (_bcgd )-1,_dcgdg .String ());
for _fcece ,_befda :=range _dcgdg ._bacg {_ge .Printf ("\u0025\u0034\u0064\u003a\u0020\u0025\u0073\u000a",_fcece ,_befda .String ());};};};_dcgdg =nil ;};};for _ ,_cebcf :=range _agcg {if _fcfda &&_dcgdg !=nil &&len (_dcgdg ._bacg )> 0{_cabb :=_dcgdg ._bacg [len (_dcgdg ._bacg )-1];
_dagg ,_gabfb :=_cfebf (_cebcf ._fecec );_bbgef ,_gadg :=_cfebf (_cabb ._fecec );if _gabfb &&!_gadg &&_cabb .inDiacriticArea (_cebcf ){_dcgdg .addDiacritic (_dagg );continue ;};if _gadg &&!_gabfb &&_cebcf .inDiacriticArea (_cabb ){_dcgdg ._bacg =_dcgdg ._bacg [:len (_dcgdg ._bacg )-1];
_dcgdg .appendMark (_cebcf ,_cbed );_dcgdg .addDiacritic (_bbgef );continue ;};};_cbda :=_cffdg (_cebcf ._fecec );if _cbda {_ccgc ();continue ;};if _dcgdg ==nil &&!_cbda {_dcgdg =_ebdfd ([]*textMark {_cebcf },_cbed );continue ;};_edeaa :=_dcgdg ._dcca ;
_fdge :=_da .Abs (_bgcgf (_cbed ,_cebcf )-_dcgdg ._bfff )/_edeaa ;_agcdf :=_eecd (_cebcf ,_dcgdg )/_edeaa ;if _agcdf >=_dbdf ||!(-_cadf <=_agcdf &&_fdge <=_dfbbca ){_ccgc ();_dcgdg =_ebdfd ([]*textMark {_cebcf },_cbed );continue ;};_dcgdg .appendMark (_cebcf ,_cbed );
};_ccgc ();return _bcgd ;};func _fcfc (_ggd []*textWord ,_eegg float64 ,_gbag ,_afff rulingList )*wordBag {_fbee :=_ccaa (_ggd [0],_eegg ,_gbag ,_afff );for _ ,_adbg :=range _ggd [1:]{_dceg :=_bbgdd (_adbg ._bfff );_fbee ._fggf [_dceg ]=append (_fbee ._fggf [_dceg ],_adbg );
_fbee .PdfRectangle =_cbba (_fbee .PdfRectangle ,_adbg .PdfRectangle );};_fbee .sort ();return _fbee ;};func (_abfa *textTable )emptyCompositeRow (_adbbf int )bool {for _adca :=0;_adca < _abfa ._ebed ;_adca ++{if _gfbc ,_fdfee :=_abfa ._dccb [_dfgde (_adca ,_adbbf )];
_fdfee {if len (_gfbc .paraList )> 0{return false ;};};};return true ;};func (_cdaf *TextMarkArray )exists (_dfca TextMark )bool {for _ ,_dbeb :=range _cdaf .Elements (){if _a .DeepEqual (_dfca .DirectObject ,_dbeb .DirectObject )&&_a .DeepEqual (_dfca .BBox ,_dbeb .BBox )&&_dbeb .Text ==_dfca .Text {return true ;
};};return false ;};func (_efge rulingList )snapToGroupsDirection ()rulingList {_efge .sortStrict ();_befee :=make (map[*ruling ]rulingList ,len (_efge ));_gdbae :=_efge [0];_gaeca :=func (_gegd *ruling ){_gdbae =_gegd ;_befee [_gdbae ]=rulingList {_gegd }};
_gaeca (_efge [0]);for _ ,_aaed :=range _efge [1:]{if _aaed ._gdaaf < _gdbae ._gdaaf -_gdda {_f .Log .Error ("\u0073\u006e\u0061\u0070T\u006f\u0047\u0072\u006f\u0075\u0070\u0073\u0044\u0069r\u0065\u0063\u0074\u0069\u006f\u006e\u003a\u0020\u0057\u0072\u006f\u006e\u0067\u0020\u0070\u0072\u0069\u006da\u0072\u0079\u0020\u006f\u0072d\u0065\u0072\u002e\u000a\u0009\u0076\u0030\u003d\u0025\u0073\u000a\u0009\u0020\u0076\u003d\u0025\u0073",_gdbae ,_aaed );
};if _aaed ._gdaaf > _gdbae ._gdaaf +_accd {_gaeca (_aaed );}else {_befee [_gdbae ]=append (_befee [_gdbae ],_aaed );};};_eefc :=make (map[*ruling ]float64 ,len (_befee ));_faadc :=make (map[*ruling ]*ruling ,len (_efge ));for _gdaac ,_bccd :=range _befee {_eefc [_gdaac ]=_bccd .mergePrimary ();
for _ ,_aeab :=range _bccd {_faadc [_aeab ]=_gdaac ;};};for _ ,_dcfa :=range _efge {_dcfa ._gdaaf =_eefc [_faadc [_dcfa ]];};_acbef :=make (rulingList ,0,len (_efge ));for _ ,_fdaf :=range _befee {_dbga :=_fdaf .splitSec ();for _efgfc ,_ecbad :=range _dbga {_cabeg :=_ecbad .merge ();
if len (_acbef )> 0{_abagb :=_acbef [len (_acbef )-1];if _abagb .alignsPrimary (_cabeg )&&_abagb .alignsSec (_cabeg ){_f .Log .Error ("\u0073\u006e\u0061\u0070\u0054\u006fG\u0072\u006f\u0075\u0070\u0073\u0044\u0069\u0072\u0065\u0063\u0074\u0069\u006f\u006e\u003a\u0020\u0044\u0075\u0070\u006ci\u0063\u0061\u0074\u0065\u0020\u0069\u003d\u0025\u0064\u000a\u0009\u0077\u003d\u0025s\u000a\t\u0076\u003d\u0025\u0073",_efgfc ,_abagb ,_cabeg );
continue ;};};_acbef =append (_acbef ,_cabeg );};};_acbef .sortStrict ();return _acbef ;};func (_gcbg *shapesState )lastpointEstablished ()(_ce .Point ,bool ){if _gcbg ._ffdgg {return _gcbg ._ffefa ,false ;};_fcfdd :=len (_gcbg ._edde );if _fcfdd > 0&&_gcbg ._edde [_fcfdd -1]._cafc {return _gcbg ._edde [_fcfdd -1].last (),false ;
};return _ce .Point {},true ;};func (_cebdd paraList )xNeighbours (_eedge float64 )map[*textPara ][]int {_gbadb :=make ([]event ,2*len (_cebdd ));if _eedge ==0{for _gccde ,_eceag :=range _cebdd {_gbadb [2*_gccde ]=event {_eceag .Llx ,true ,_gccde };_gbadb [2*_gccde +1]=event {_eceag .Urx ,false ,_gccde };
};}else {for _adac ,_cece :=range _cebdd {_gbadb [2*_adac ]=event {_cece .Llx -_eedge *_cece .fontsize (),true ,_adac };_gbadb [2*_adac +1]=event {_cece .Urx +_eedge *_cece .fontsize (),false ,_adac };};};return _cebdd .eventNeighbours (_gbadb );};func (_dbede *textPara )fontsize ()float64 {return _dbede ._geffa [0]._fcgeg };
type structElement struct{_eeagb string ;_gedge []structElement ;_daaaa int64 ;_abefd _ea .PdfObject ;};func _cfebf (_dabe string )(string ,bool ){_ffadc :=[]rune (_dabe );if len (_ffadc )!=1{return "",false ;};_ecgce ,_ecgd :=_defde [_ffadc [0]];return _ecgce ,_ecgd ;
};func _baegc (_ddfg []TextMark ,_cdbf *int ,_ecdaf string )[]TextMark {_acaba :=_dbaf ;_acaba .Text =_ecdaf ;return _fffb (_ddfg ,_cdbf ,_acaba );};func (_ggab *textLine )toTextMarks (_cgfg *int )[]TextMark {var _adbd []TextMark ;for _ ,_baba :=range _ggab ._ebgf {if _baba ._fabaa {_adbd =_baegc (_adbd ,_cgfg ,"\u0020");
};_begc :=_baba .toTextMarks (_cgfg );_adbd =append (_adbd ,_begc ...);};return _adbd ;};

// PageImages represents extracted images on a PDF page with spatial information:
// display position and size.
type PageImages struct{Images []ImageMark ;};const (_ebfec =false ;_affbb =false ;_bgcgb =false ;_gcdaf =false ;_bfgf =false ;_bcdg =false ;_gafg =false ;_fgfdc =false ;_cfbd =false ;_ggaeg =_cfbd &&true ;_afea =_ggaeg &&false ;_caffd =_cfbd &&true ;_ababc =false ;
_gfab =_ababc &&false ;_eaebd =_ababc &&true ;_bggcg =false ;_dedc =_bggcg &&false ;_ffdf =_bggcg &&false ;_ffbg =_bggcg &&true ;_effbc =_bggcg &&false ;_aefce =_bggcg &&false ;);func _ebef (_bfagd *_cc .Image ,_cebfd _ca .Color )_ee .Image {_eaba ,_gdgbe :=int (_bfagd .Width ),int (_bfagd .Height );
_dfaf :=_ee .NewRGBA (_ee .Rect (0,0,_eaba ,_gdgbe ));for _ffcca :=0;_ffcca < _gdgbe ;_ffcca ++{for _geafg :=0;_geafg < _eaba ;_geafg ++{_adad ,_afec :=_bfagd .ColorAt (_geafg ,_ffcca );if _afec !=nil {_f .Log .Debug ("\u0057\u0041\u0052\u004e\u003a\u0020\u0063o\u0075\u006c\u0064\u0020\u006e\u006f\u0074\u0020\u0072\u0065\u0074\u0072\u0069\u0065v\u0065 \u0069\u006d\u0061\u0067\u0065\u0020m\u0061\u0073\u006b\u0020\u0076\u0061\u006cu\u0065\u0020\u0061\u0074\u0020\u0028\u0025\u0064\u002c\u0020\u0025\u0064\u0029\u002e\u0020\u004f\u0075\u0074\u0070\u0075\u0074\u0020\u006da\u0079\u0020\u0062\u0065\u0020\u0069\u006e\u0063\u006f\u0072\u0072\u0065\u0063t\u002e",_geafg ,_ffcca );
continue ;};_acdea ,_fedaa ,_bgbb ,_ :=_adad .RGBA ();var _fgee _ca .Color ;if _acdea +_fedaa +_bgbb ==0{_fgee =_cebfd ;}else {_fgee =_ca .Transparent ;};_dfaf .Set (_geafg ,_ffcca ,_fgee );};};return _dfaf ;};func (_begg *stateStack )pop ()*textState {if _begg .empty (){return nil ;
};_gdfg :=*(*_begg )[len (*_begg )-1];*_begg =(*_begg )[:len (*_begg )-1];return &_gdfg ;};type textMark struct{_cc .PdfRectangle ;_ggbe int ;_fecec string ;_bdea string ;_dagf *_cc .PdfFont ;_ggde float64 ;_bccbd float64 ;_deff _ce .Matrix ;_ffdfd _ce .Point ;
_ebge _cc .PdfRectangle ;_dbgd _ca .Color ;_eeeb _ca .Color ;_fbce _ea .PdfObject ;_fedc []string ;Tw float64 ;Th float64 ;_adgc int ;_gbecb int ;};type bounded interface{bbox ()_cc .PdfRectangle };func _dgfce (_ecgea ,_fcfe _cc .PdfRectangle )bool {return _fcfe .Llx <=_ecgea .Urx &&_ecgea .Llx <=_fcfe .Urx ;
};var _dbaf =TextMark {Text :"\u005b\u0058\u005d",Original :"\u0020",Meta :true ,FillColor :_ca .White ,StrokeColor :_ca .White };func _bcbg (_eedf ,_bgccf *textPara )bool {return _dgfce (_eedf ._abfg ,_bgccf ._abfg )};

// Box represents the bounding box of a given textMark on pdf page.
// This might be used for different kinds of high lighting after doing the search
type Box struct{BBox _cc .PdfRectangle ;};func (_dfec *wordBag )makeRemovals ()map[int ]map[*textWord ]struct{}{_accc :=make (map[int ]map[*textWord ]struct{},len (_dfec ._fggf ));for _gdfb :=range _dfec ._fggf {_accc [_gdfb ]=make (map[*textWord ]struct{});
};return _accc ;};func (_fbada *wordBag )pullWord (_fbdd *textWord ,_aaba int ,_fece map[int ]map[*textWord ]struct{}){_fbada .PdfRectangle =_cbba (_fbada .PdfRectangle ,_fbdd .PdfRectangle );if _fbdd ._dcca > _fbada ._aafec {_fbada ._aafec =_fbdd ._dcca ;
};_fbada ._fggf [_aaba ]=append (_fbada ._fggf [_aaba ],_fbdd );_fece [_aaba ][_fbdd ]=struct{}{};};func (_bceff paraList )findTextTables ()[]*textTable {var _abfb []*textTable ;for _ ,_deaae :=range _bceff {if _deaae .taken ()||_deaae .Width ()==0{continue ;
};_bcaef :=_deaae .isAtom ();if _bcaef ==nil {continue ;};_bcaef .growTable ();if _bcaef ._ebed *_bcaef ._fcec < _adgg {continue ;};_bcaef .markCells ();_bcaef .log ("\u0067\u0072\u006fw\u006e");_abfb =append (_abfb ,_bcaef );};return _abfb ;};

// NewEditor returns a new Editor object
func NewEditor (reader *_cc .PdfReader )*Editor {return &Editor {_cff :reader }};func (_fggg *textLine )markWordBoundaries (){_fgfac :=_dgad *_fggg ._fcgeg ;for _efbc ,_ffedb :=range _fggg ._ebgf [1:]{if _eecd (_ffedb ,_fggg ._ebgf [_efbc ])>=_fgfac {_ffedb ._fabaa =true ;
};};};func _fdfcc (_aafea []*textLine ,_gfdg string ,_bafd []*list )*list {return &list {_faeg :_aafea ,_eddg :_gfdg ,_bafe :_bafd };};func (_dbddc gridTiling )log (_becd string ){if !_ffbg {return ;};_f .Log .Info ("\u0074i\u006ci\u006e\u0067\u003a\u0020\u0025d\u0020\u0078 \u0025\u0064\u0020\u0025\u0071",len (_dbddc ._gdec ),len (_dbddc ._daeaf ),_becd );
_ge .Printf ("\u0020\u0020\u0020l\u006c\u0078\u003d\u0025\u002e\u0032\u0066\u000a",_dbddc ._gdec );_ge .Printf ("\u0020\u0020\u0020l\u006c\u0079\u003d\u0025\u002e\u0032\u0066\u000a",_dbddc ._daeaf );for _cadc ,_ebdff :=range _dbddc ._daeaf {_eecg ,_bfaaf :=_dbddc ._bceeb [_ebdff ];
if !_bfaaf {continue ;};_ge .Printf ("%\u0034\u0064\u003a\u0020\u0025\u0036\u002e\u0032\u0066\u000a",_cadc ,_ebdff );for _fabab ,_aabab :=range _dbddc ._gdec {_dgfgc ,_faccg :=_eecg [_aabab ];if !_faccg {continue ;};_ge .Printf ("\u0025\u0038\u0064\u003a\u0020\u0025\u0073\u000a",_fabab ,_dgfgc .String ());
};};};

// PageTextOptions holds various options available in extraction process.
type PageTextOptions struct{_cgdc bool ;_gbdd bool ;_fcdcf bool ;};

// TextMarkArray is a collection of TextMarks.
type TextMarkArray struct{_aecad []TextMark };func _affg (_cba *_ea .PdfObjectString ,_cbg string ,_gcdd *_cc .PdfFont ){_fcdf ,_aed :=_gcdd .StringToCharcodeBytes (_cbg );if _aed !=0{_f .Log .Debug ("\u0057\u0041\u0052\u004e\u003a\u0020\u0073\u006fm\u0065\u0020\u0072un\u0065\u0073\u0020\u0063\u006f\u0075l\u0064\u0020\u006e\u006f\u0074\u0020\u0062\u0065\u0020\u0065\u006e\u0063\u006f\u0064\u0065d\u002e\u000a\u0009\u0025\u0073\u0020\u002d\u003e \u0025\u0076",_cbg ,_fcdf );
};_caf :=_ea .MakeStringFromBytes (_fcdf );*_cba =*_caf ;};const (_ddeeag markKind =iota ;_fgdbf ;_agbd ;_ecdaa ;);func (_dbeba gridTile )numBorders ()int {_eecb :=0;if _dbeba ._egeg {_eecb ++;};if _dbeba ._gbfec {_eecb ++;};if _dbeba ._gfgcaf {_eecb ++;
};if _dbeba ._fbfd {_eecb ++;};return _eecb ;};type gridTile struct{_cc .PdfRectangle ;_fbfd ,_egeg ,_gfgcaf ,_gbfec bool ;};func (_gceed *textPara )isAtom ()*textTable {_bdcf :=_gceed ;_fcga :=_gceed ._dgadb ;_beed :=_gceed ._cada ;if _fcga .taken ()||_beed .taken (){return nil ;
};_gcfb :=_fcga ._cada ;if _gcfb .taken ()||_gcfb !=_beed ._dgadb {return nil ;};return _cfcbc (_bdcf ,_fcga ,_beed ,_gcfb );};func (_bcfd paraList )extractTables (_afde []gridTiling )paraList {if _ababc {_f .Log .Debug ("\u0065\u0078\u0074r\u0061\u0063\u0074\u0054\u0061\u0062\u006c\u0065\u0073\u003d\u0025\u0064\u0020\u003d\u003d\u003d\u003d\u003d\u003d\u003d\u003d\u003d\u003d\u003d\u0078\u003d\u003d\u003d\u003d=\u003d\u003d\u003d\u003d\u003d\u003d\u003d\u003d",len (_bcfd ));
};if len (_bcfd )< _adgg {return _bcfd ;};_bgccb :=_bcfd .findTables (_afde );if _ababc {_f .Log .Info ("c\u006f\u006d\u0062\u0069\u006e\u0065d\u0020\u0074\u0061\u0062\u006c\u0065s\u0020\u0025\u0064\u0020\u003d\u003d\u003d=\u003d\u003d\u003d\u003d\u003d\u003d\u003d\u003d\u003d\u003d=\u003d",len (_bgccb ));
for _ceag ,_eabbc :=range _bgccb {_eabbc .log (_ge .Sprintf ("c\u006f\u006d\u0062\u0069\u006e\u0065\u0064\u0020\u0025\u0064",_ceag ));};};return _bcfd .applyTables (_bgccb );};

// List returns all the list objects detected on the page.
// It detects all the bullet point Lists from a given pdf page and builds a slice of bullet list objects.
// A given bullet list object has a tree structure.
// Each bullet point list is extracted with the text content it contains and all the sub lists found under it as children in the tree.
// The rest content of the pdf is ignored and only text in the bullet point lists are extracted.
// The list extraction is done in two ways.
// 1. If the document is tagged then the lists are extracted using the tags provided in the document.
// 2. Otherwise the bullet lists are extracted from the raw text using regex matching.
// By default the document tag is used if available.
// However this can be disabled using `DisableDocumentTags` in the `Options` object.
// Sometimes disabling document tags option might give a better bullet list extraction if the document was tagged incorrectly.
//
//	    options := &Options{
//		     DisableDocumentTags: false, // this means use document tag if available
//	    }
//	    ex, err := NewWithOptions(page, options)
//	    // handle error
//	    pageText, _, _, err := ex.ExtractPageText()
//	    // handle error
//	    lists := pageText.List()
//	    txt := lists.Text()
func (_cecg PageText )List ()lists {_cgbca :=!_cecg ._ceef ._cgdc ;_eebcb :=_cecg .getParagraphs ();_bgdd :=true ;if _cecg ._ebeb ==nil ||*_cecg ._ebeb ==nil {_bgdd =false ;};_aabc :=_eebcb .list ();if _bgdd &&_cgbca {_dagc :=_egae (&_eebcb );_gdbc :=&structTreeRoot {};
_gdbc .parseStructTreeRoot (*_cecg ._ebeb );if _gdbc ._ffedd ==nil {_f .Log .Debug ("\u004c\u0069\u0073\u0074\u003a\u0020\u0073t\u0072\u0075\u0063\u0074\u0054\u0072\u0065\u0065\u0052\u006f\u006f\u0074\u0020\u0064\u006f\u0065\u0073\u006e'\u0074\u0020\u0068\u0061\u0076e\u0020\u0061\u006e\u0079\u0020\u0063\u006f\u006e\u0074e\u006e\u0074\u002c\u0020\u0075\u0073\u0069\u006e\u0067\u0020\u0074\u0065\u0078\u0074\u0020\u006d\u0061\u0074\u0063\u0068\u0069\u006e\u0067\u0020\u006d\u0065\u0074\u0068\u006f\u0064\u0020\u0069\u006e\u0073\u0074\u0065\u0061\u0064\u002e");
return _aabc ;};_aabc =_gdbc .buildList (_dagc ,_cecg ._fbcg );};return _aabc ;};type wordBag struct{_cc .PdfRectangle ;_aafec float64 ;_eccg ,_dbca rulingList ;_afbd float64 ;_fggf map[int ][]*textWord ;};func (_dcgbg *wordBag )allWords ()[]*textWord {var _gede []*textWord ;
for _ ,_cebf :=range _dcgbg ._fggf {_gede =append (_gede ,_cebf ...);};return _gede ;};type list struct{_faeg []*textLine ;_eddg string ;_bafe []*list ;_eeff string ;};func _dfgde (_agfba ,_ddbg int )uint64 {return uint64 (_agfba )*0x1000000+uint64 (_ddbg )};
func _bgcgf (_eeedd _cc .PdfRectangle ,_eefa bounded )float64 {return _eeedd .Ury -_eefa .bbox ().Lly };func (_dgda *textTable )compositeRowCorridors ()map[int ][]float64 {_feagb :=make (map[int ][]float64 ,_dgda ._fcec );if _ababc {_f .Log .Info ("c\u006f\u006d\u0070\u006f\u0073\u0069t\u0065\u0052\u006f\u0077\u0043\u006f\u0072\u0072\u0069d\u006f\u0072\u0073:\u0020h\u003d\u0025\u0064",_dgda ._fcec );
};for _dbbefb :=1;_dbbefb < _dgda ._fcec ;_dbbefb ++{var _egage []compositeCell ;for _bagb :=0;_bagb < _dgda ._ebed ;_bagb ++{if _eccga ,_aacag :=_dgda ._dccb [_dfgde (_bagb ,_dbbefb )];_aacag {_egage =append (_egage ,_eccga );};};if len (_egage )==0{continue ;
};_ecdca :=_cfee (_egage );_feagb [_dbbefb ]=_ecdca ;if _ababc {_ge .Printf ("\u0020\u0020\u0020\u0025\u0032\u0064\u003a\u0020\u00256\u002e\u0032\u0066\u000a",_dbbefb ,_ecdca );};};return _feagb ;};func (_cgeca rulingList )findPrimSec (_ecce ,_bbbga float64 )*ruling {for _ ,_bbdf :=range _cgeca {if _bfee (_bbdf ._gdaaf -_ecce )&&_bbdf ._bfbdb -_cefge <=_bbbga &&_bbbga <=_bbdf ._bedbg +_cefge {return _bbdf ;
};};return nil ;};const _cdfd =20;func (_bafab *textWord )appendMark (_fdagf *textMark ,_ebdg _cc .PdfRectangle ){_bafab ._bacg =append (_bafab ._bacg ,_fdagf );_bafab .PdfRectangle =_cbba (_bafab .PdfRectangle ,_fdagf .PdfRectangle );if _fdagf ._ggde > _bafab ._dcca {_bafab ._dcca =_fdagf ._ggde ;
};_bafab ._bfff =_ebdg .Ury -_bafab .PdfRectangle .Lly ;};func _eedfa (_bfaae ,_cdgeb float64 )string {_eafd :=!_bfee (_bfaae -_cdgeb );if _eafd {return "\u000a";};return "\u0020";};func (_dagb *textPara )depth ()float64 {if _dagb ._gaebg {return -1.0;
};if len (_dagb ._geffa )> 0{return _dagb ._geffa [0]._gdca ;};return _dagb ._dcde .depth ();};func (_abab *textObject )getStrokeColor ()_ca .Color {return _eefcg (_abab ._fbgf .ColorspaceStroking ,_abab ._fbgf .ColorStroking );};func _cfcbc (_gdeega ,_gccb ,_ffeb ,_bfca *textPara )*textTable {_cbdc :=&textTable {_ebed :2,_fcec :2,_dcdb :make (map[uint64 ]*textPara ,4)};
_cbdc .put (0,0,_gdeega );_cbdc .put (1,0,_gccb );_cbdc .put (0,1,_ffeb );_cbdc .put (1,1,_bfca );return _cbdc ;};

// ExtractPageImages returns the image contents of the page extractor, including data
// and position, size information for each image.
// A set of options to control page image extraction can be passed in. The options
// parameter can be nil for the default options. By default, inline stencil masks
// are not extracted.
func (_gcac *Extractor )ExtractPageImages (options *ImageExtractOptions )(*PageImages ,error ){_bcb :=&imageExtractContext {_dec :options };_fdg :=_bcb .extractContentStreamImages (_gcac ._dddf ,_gcac ._gf );if _fdg !=nil {return nil ,_fdg ;};return &PageImages {Images :_bcb ._gbg },nil ;
};func (_dffe *textPara )toCellTextMarks (_fdfgd *int )[]TextMark {var _adcbg []TextMark ;for _egba ,_fgab :=range _dffe ._geffa {_fgca :=_fgab .toTextMarks (_fdfgd );_dcbef :=_egg &&_fgab .endsInHyphen ()&&_egba !=len (_dffe ._geffa )-1;if _dcbef {_fgca =_gccdf (_fgca ,_fdfgd );
};_adcbg =append (_adcbg ,_fgca ...);if !(_dcbef ||_egba ==len (_dffe ._geffa )-1){_adcbg =_baegc (_adcbg ,_fdfgd ,_eedfa (_fgab ._gdca ,_dffe ._geffa [_egba +1]._gdca ));};};return _adcbg ;};func (_ffgg *textLine )bbox ()_cc .PdfRectangle {return _ffgg .PdfRectangle };
func (_cbfe intSet )del (_dadg int ){delete (_cbfe ,_dadg )};func (_cfdg *wordBag )depthBand (_bgcg ,_cedfe float64 )[]int {if len (_cfdg ._fggf )==0{return nil ;};return _cfdg .depthRange (_cfdg .getDepthIdx (_bgcg ),_cfdg .getDepthIdx (_cedfe ));};var (_defde =map[rune ]string {0x0060:"\u0300",0x02CB:"\u0300",0x0027:"\u0301",0x00B4:"\u0301",0x02B9:"\u0301",0x02CA:"\u0301",0x005E:"\u0302",0x02C6:"\u0302",0x007E:"\u0303",0x02DC:"\u0303",0x00AF:"\u0304",0x02C9:"\u0304",0x02D8:"\u0306",0x02D9:"\u0307",0x00A8:"\u0308",0x00B0:"\u030a",0x02DA:"\u030a",0x02BA:"\u030b",0x02DD:"\u030b",0x02C7:"\u030c",0x02C8:"\u030d",0x0022:"\u030e",0x02BB:"\u0312",0x02BC:"\u0313",0x0486:"\u0313",0x055A:"\u0313",0x02BD:"\u0314",0x0485:"\u0314",0x0559:"\u0314",0x02D4:"\u031d",0x02D5:"\u031e",0x02D6:"\u031f",0x02D7:"\u0320",0x02B2:"\u0321",0x00B8:"\u0327",0x02CC:"\u0329",0x02B7:"\u032b",0x02CD:"\u0331",0x005F:"\u0332",0x204E:"\u0359"};
);func _afe (_cec []rune )BidiText {_agg :=-1;_eab :=false ;_dgd :=true ;_cecf :=len (_cec );_gee :=make ([]string ,_cecf );_de :=make ([]string ,_cecf );if _cecf ==0||_eab {return _cgc (string (_cec ),_dgd ,_eab );};_cag :=0;for _egc ,_faf :=range _cec {_gee [_egc ]=string (_faf );
_cce :="\u004c";if _faf <=0x00ff{_cce =_gef [_faf ];}else if 0x0590<=_faf &&_faf <=0x05f4{_cce ="\u0052";}else if 0x0600<=_faf &&_faf <=0x06ff{_fd :=_faf &0xff;if int (_fd )>=len (_af ){_f .Log .Debug ("\u0042\u0069\u0064\u0069\u003a\u0020\u0069\u006e\u0076\u0061\u006c\u0069\u0064\u0020\u0055n\u0069c\u006f\u0064\u0065\u0020\u0063\u0068\u0061\u0072\u0061\u0063\u0074\u0065\u0072\u0020"+string (_faf ));
};_cce =_af [_faf &0xff];}else if (0x0700<=_faf &&_faf <=0x08ac)||(0xfb50<=_faf &&_faf <=0xfdff)||(0xfe70<=_faf &&_faf <=0xfeff){_cce ="\u0041\u004c";};if _cce =="\u0052"||_cce =="\u0041\u004c"||_cce =="\u0041\u004e"{_cag ++;};_de [_egc ]=_cce ;};if _cag ==0{_dgd =true ;
return _cgc (string (_cec ),_dgd ,false );};if _agg ==-1{if float64 (_cag )/float64 (_cecf )< 0.3&&_cecf > 4{_dgd =true ;_agg =0;}else {_dgd =false ;_agg =1;};};var _ecf []int ;for range _cec {_ecf =append (_ecf ,_agg );};_ab :="\u004c";if _ad (_agg ){_ab ="\u0052";
};_ead :=_ab ;_dcd :=_ead ;_ba :=_ead ;for _ac :=range _cec {if _de [_ac ]=="\u004e\u0053\u004d"{_de [_ac ]=_ba ;}else {_ba =_de [_ac ];};};_ba =_ead ;var _fbad string ;for _dfa :=range _cec {_fbad =_de [_dfa ];if _fbad =="\u0045\u004e"{if _ba =="\u0041\u004c"{_de [_dfa ]="\u0041\u004e";
}else {_de [_dfa ]="\u0045\u004e";};}else if _fbad =="\u0052"||_fbad =="\u004c"||_fbad =="\u0041\u004c"{_ba =_fbad ;};};for _dfe :=range _cec {_fg :=_de [_dfe ];if _fg =="\u0041\u004c"{_de [_dfe ]="\u0052";};};for _bee :=1;_bee < (len (_cec )-1);_bee ++{if _de [_bee ]=="\u0045\u0053"&&_de [_bee -1]=="\u0045\u004e"&&_de [_bee +1]=="\u0045\u004e"{_de [_bee ]="\u0045\u004e";
};if _de [_bee ]=="\u0043\u0053"&&(_de [_bee -1]=="\u0045\u004e"||_de [_bee -1]=="\u0041\u004e")&&_de [_bee +1]==_de [_bee -1]{_de [_bee ]=_de [_bee -1];};};for _dcdd :=range _cec {if _de [_dcdd ]=="\u0045\u004e"{for _bf :=_dcdd -1;_bf >=0;_bf --{if _de [_bf ]!="\u0045\u0054"{break ;
};_de [_bf ]="\u0045\u004e";};for _bd :=_dcdd +1;_bd < _cecf ;_bd ++{if _de [_bd ]!="\u0045\u0054"{break ;};_de [_bd ]="\u0045\u004e";};};};for _cb :=range _cec {_db :=_de [_cb ];if _db =="\u0057\u0053"||_db =="\u0045\u0053"||_db =="\u0045\u0054"||_db =="\u0043\u0053"{_de [_cb ]="\u004f\u004e";
};};_ba ="\u0073\u006f\u0072";for _fag :=range _cec {_eabf :=_de [_fag ];if _eabf =="\u0045\u004e"{if _ba =="\u004c"{_de [_fag ]="\u004c";}else {_de [_fag ]="\u0045\u004e";};}else if _eabf =="\u0052"||_eabf =="\u004c"{_ba =_eabf ;};};for _fgg :=0;_fgg < len (_cec );
_fgg ++{if _de [_fgg ]=="\u004f\u004e"{_fdc :=_cca (_de ,_fgg +1,"\u004f\u004e");_bdf :=_dcd ;if _fgg > 0{_bdf =_de [_fgg -1];};_fae :=_dcd ;if _fdc +1< _cecf {_fae =_de [_fdc +1];};if _bdf !="\u004c"{_bdf ="\u0052";};if _fae !="\u004c"{_fae ="\u0052";
};if _bdf ==_fae {_cg (_de ,_fgg ,_fdc ,_bdf );};_fgg =_fdc -1;};};for _bfc :=range _cec {if _de [_bfc ]=="\u004f\u004e"{_de [_bfc ]=_ab ;};};for _dd :=range _cec {_cdb :=_de [_dd ];if _gc (_ecf [_dd ]){if _cdb =="\u0052"{_ecf [_dd ]++;}else if _cdb =="\u0041\u004e"||_cdb =="\u0045\u004e"{_ecf [_dd ]+=2;
};}else if _cdb =="\u004c"||_cdb =="\u0041\u004e"||_cdb =="\u0045\u004e"{_ecf [_dd ]++;};};_gce :=-1;_adf :=99;var _gd int ;for _fbg :=0;_fbg < len (_ecf );_fbg ++{_gd =_ecf [_fbg ];if _gce < _gd {_gce =_gd ;};if _adf > _gd &&_ad (_gd ){_adf =_gd ;};};
for _cf :=_gce ;_cf >=_adf ;_cf --{_eadc :=-1;for _cac :=0;_cac < len (_ecf );_cac ++{if _ecf [_cac ]< _cf {if _eadc >=0{_eeg (_gee ,_eadc ,_cac );_eadc =-1;};}else if _eadc < 0{_eadc =_cac ;};};if _eadc >=0{_eeg (_gee ,_eadc ,len (_ecf ));};};for _gcg :=0;
_gcg < len (_gee );_gcg ++{_fbb :=_gee [_gcg ];if _fbb =="\u003c"||_fbb =="\u003e"{_gee [_gcg ]="";};};return _cgc (_ag .Join (_gee ,""),_dgd ,false );};func _cfca (_bbcb []structElement ,_dfed map[int ][]*textLine ,_fde _ea .PdfObject )[]*list {_egdg :=[]*list {};
for _ ,_aebc :=range _bbcb {_bagc :=_aebc ._gedge ;_cdffg :=int (_aebc ._daaaa );_bagg :=_aebc ._eeagb ;_gfea :=[]*textLine {};_aeccc :=[]*list {};_bdde :=_aebc ._abefd ;_dbbdb ,_cggd :=(_bdde .(*_ea .PdfObjectReference ));if !_cggd {_f .Log .Debug ("\u0066\u0061\u0069l\u0065\u0064\u0020\u006f\u0074\u0020\u0063\u0061\u0073\u0074\u0020\u0074\u006f\u0020\u002a\u0063\u006f\u0072\u0065\u002e\u0050\u0064\u0066\u004f\u0062\u006a\u0065\u0063\u0074R\u0065\u0066\u0065\u0072\u0065\u006e\u0063\u0065");
};if _cdffg !=-1&&_dbbdb !=nil {if _fggceb ,_dgade :=_dfed [_cdffg ];_dgade {if _ccge ,_eegfe :=_fde .(*_ea .PdfIndirectObject );_eegfe {_ggdb :=_ccge .PdfObjectReference ;if _a .DeepEqual (*_dbbdb ,_ggdb ){_gfea =_fggceb ;};};};};if _bagc !=nil {_aeccc =_cfca (_bagc ,_dfed ,_fde );
};_cbcf :=_fdfcc (_gfea ,_bagg ,_aeccc );_egdg =append (_egdg ,_cbcf );};return _egdg ;};func _dgfaaf (_efbce *textLine ,_dbdbd []*textLine ,_debe []float64 ,_deeb ,_efcfd float64 )[]*textLine {_fcagd :=[]*textLine {};for _ ,_cbacc :=range _dbdbd {if _cbacc ._gdca >=_deeb {if _efcfd !=-1&&_cbacc ._gdca < _efcfd {if _cbacc .text ()!=_efbce .text (){if _da .Round (_cbacc .Llx )< _da .Round (_efbce .Llx ){break ;
};_fcagd =append (_fcagd ,_cbacc );};}else if _efcfd ==-1{if _cbacc ._gdca ==_efbce ._gdca {if _cbacc .text ()!=_efbce .text (){_fcagd =append (_fcagd ,_cbacc );};continue ;};_cfg :=_fedef (_efbce ,_dbdbd ,_debe );if _cfg !=-1&&_cbacc ._gdca <=_cfg {_fcagd =append (_fcagd ,_cbacc );
};};};};return _fcagd ;};

// Replace takes a pattern and replaces all the texts that much the pattern with `replacement`.
func (_gbdg *Editor )Replace (pattern string ,replacement string ,pages []int )error {_cgea :=map[int ]Match {};for _ ,_eag :=range pages {_ecaf ,_gaga :=_gbdg ._cff .GetPage (_eag );if _gaga !=nil {return _gaga ;};_aab ,_gaga :=New (_ecaf );if _gaga !=nil {return _gaga ;
};_adg ,_ ,_ ,_gaga :=_aab .ExtractPageText ();if _gaga !=nil {return _gaga ;};_baf :="";_dcg :=_adg .Text ();_fad ,_gaga :=_gdbf (pattern ,_dcg );if _gaga !=nil {return _gaga ;};_fbadb :=_adg .Marks ();_eda :=[]Box {};for _ ,_dfda :=range _fad {_bdbf ,_eeag ,_afcb :=_gcb (_dfda ,_fbadb ,pattern );
if _afcb !=nil {return _afcb ;};_cad :=_fgb (_bdbf );if _afcb !=nil {return _afcb ;};_eda =append (_eda ,_eeag );replacement ,_afcb =_fbgb (_cad ,replacement ,pattern );if _afcb !=nil {return _afcb ;};};_baf =_adg .GetContentStreamOps ().String ();_fgfb :=Match {Pattern :pattern ,Indexes :_fad ,Locations :_eda };
_ecaf .SetContentStreams ([]string {_baf },_ea .NewFlateEncoder ());_cgea [_eag ]=_fgfb ;};return nil ;};type textLine struct{_cc .PdfRectangle ;_gdca float64 ;_ebgf []*textWord ;_fcgeg float64 ;};func _cbfff (_cagfc map[float64 ][]*textLine )[]float64 {_fgccd :=[]float64 {};
for _bddf :=range _cagfc {_fgccd =append (_fgccd ,_bddf );};_be .Float64s (_fgccd );return _fgccd ;};func (_feeb *shapesState )establishSubpath ()*subpath {_dea ,_fafd :=_feeb .lastpointEstablished ();if !_fafd {_feeb ._edde =append (_feeb ._edde ,_bgdc (_dea ));
};if len (_feeb ._edde )==0{return nil ;};_feeb ._ffdgg =false ;return _feeb ._edde [len (_feeb ._edde )-1];};var _ebdf =map[markKind ]string {_fgdbf :"\u0073\u0074\u0072\u006f\u006b\u0065",_agbd :"\u0066\u0069\u006c\u006c",_ecdaa :"\u0061u\u0067\u006d\u0065\u006e\u0074"};


// Tables returns the tables extracted from the page.
func (_agf PageText )Tables ()[]TextTable {if _ababc {_f .Log .Info ("\u0054\u0061\u0062\u006c\u0065\u0073\u003a\u0020\u0025\u0064",len (_agf ._gcaca ));};return _agf ._gcaca ;};

// String returns a description of `k`.
func (_cacd rulingKind )String ()string {_cbbe ,_gagcc :=_befec [_cacd ];if !_gagcc {return _ge .Sprintf ("\u004e\u006ft\u0020\u0061\u0020r\u0075\u006c\u0069\u006e\u0067\u003a\u0020\u0025\u0064",_cacd );};return _cbbe ;};func (_ecba TextTable )getCellInfo (_cdff TextMark )[][]int {for _edd ,_ccgb :=range _ecba .Cells {for _agcde :=range _ccgb {_egbfb :=&_ccgb [_agcde ].Marks ;
if _egbfb .exists (_cdff ){return [][]int {{_edd },{_agcde }};};};};return nil ;};

// RangeOffset returns the TextMarks in `ma` that overlap text[start:end] in the extracted text.
// These are tm: `start` <= tm.Offset + len(tm.Text) && tm.Offset < `end` where
// `start` and `end` are offsets in the extracted text.
// NOTE: TextMarks can contain multiple characters. e.g. "ffi" for the ﬃ ligature so the first and
// last elements of the returned TextMarkArray may only partially overlap text[start:end].
func (_egbf *TextMarkArray )RangeOffset (start ,end int )(*TextMarkArray ,error ){if _egbf ==nil {return nil ,_df .New ("\u006da\u003d\u003d\u006e\u0069\u006c");};if end < start {return nil ,_ge .Errorf ("\u0065\u006e\u0064\u0020\u003c\u0020\u0073\u0074\u0061\u0072\u0074\u002e\u0020\u0052\u0061n\u0067\u0065\u004f\u0066\u0066\u0073\u0065\u0074\u0020\u006e\u006f\u0074\u0020d\u0065\u0066\u0069\u006e\u0065\u0064\u002e\u0020\u0073\u0074\u0061\u0072t=\u0025\u0064\u0020\u0065\u006e\u0064\u003d\u0025\u0064\u0020",start ,end );
};_ceg :=len (_egbf ._aecad );if _ceg ==0{return _egbf ,nil ;};if start < _egbf ._aecad [0].Offset {start =_egbf ._aecad [0].Offset ;};if end > _egbf ._aecad [_ceg -1].Offset +1{end =_egbf ._aecad [_ceg -1].Offset +1;};_ggeb :=_be .Search (_ceg ,func (_adbe int )bool {return _egbf ._aecad [_adbe ].Offset +len (_egbf ._aecad [_adbe ].Text )-1>=start });
if !(0<=_ggeb &&_ggeb < _ceg ){_fcfd :=_ge .Errorf ("\u004f\u0075\u0074\u0020\u006f\u0066\u0020\u0072\u0061\u006e\u0067\u0065\u002e\u0020\u0073\u0074\u0061\u0072\u0074\u003d%\u0064\u0020\u0069\u0053\u0074\u0061\u0072\u0074\u003d\u0025\u0064\u0020\u006c\u0065\u006e\u003d\u0025\u0064\u000a\u0009\u0066\u0069\u0072\u0073\u0074\u003d\u0025\u0076\u000a\u0009 \u006c\u0061\u0073\u0074\u003d%\u0076",start ,_ggeb ,_ceg ,_egbf ._aecad [0],_egbf ._aecad [_ceg -1]);
return nil ,_fcfd ;};_gdaf :=_be .Search (_ceg ,func (_aaa int )bool {return _egbf ._aecad [_aaa ].Offset > end -1});if !(0<=_gdaf &&_gdaf < _ceg ){_cagg :=_ge .Errorf ("\u004f\u0075\u0074\u0020\u006f\u0066\u0020r\u0061\u006e\u0067e\u002e\u0020\u0065n\u0064\u003d%\u0064\u0020\u0069\u0045\u006e\u0064=\u0025d \u006c\u0065\u006e\u003d\u0025\u0064\u000a\u0009\u0066\u0069\u0072\u0073\u0074\u003d\u0025\u0076\u000a\u0009\u0020\u006c\u0061\u0073\u0074\u003d\u0025\u0076",end ,_gdaf ,_ceg ,_egbf ._aecad [0],_egbf ._aecad [_ceg -1]);
return nil ,_cagg ;};if _gdaf <=_ggeb {return nil ,_ge .Errorf ("\u0069\u0045\u006e\u0064\u0020\u003c=\u0020\u0069\u0053\u0074\u0061\u0072\u0074\u003a\u0020\u0073\u0074\u0061\u0072\u0074\u003d\u0025\u0064\u0020\u0065\u006ed\u003d\u0025\u0064\u0020\u0069\u0053\u0074\u0061\u0072\u0074\u003d\u0025\u0064\u0020i\u0045n\u0064\u003d\u0025\u0064",start ,end ,_ggeb ,_gdaf );
};return &TextMarkArray {_aecad :_egbf ._aecad [_ggeb :_gdaf ]},nil ;};func _bcfdgc (_ebfecc string ,_bdddc int )string {if len (_ebfecc )< _bdddc {return _ebfecc ;};return _ebfecc [:_bdddc ];};func _ggbdb (_cgab []*textLine )map[float64 ][]*textLine {_be .Slice (_cgab ,func (_gebd ,_cdgb int )bool {return _cgab [_gebd ]._gdca < _cgab [_cdgb ]._gdca });
_bfbe :=map[float64 ][]*textLine {};for _ ,_bfef :=range _cgab {_bccg :=_abgg (_bfef );_bccg =_da .Round (_bccg );_bfbe [_bccg ]=append (_bfbe [_bccg ],_bfef );};return _bfbe ;};func (_fcdc *textObject )nextLine (){_fcdc .moveLP (0,-_fcdc ._fea ._egdf )};
func (_fcdea paraList )llyOrdering ()[]int {_aagge :=make ([]int ,len (_fcdea ));for _faad :=range _fcdea {_aagge [_faad ]=_faad ;};_be .SliceStable (_aagge ,func (_cgdef ,_eggb int )bool {_fcef ,_beca :=_aagge [_cgdef ],_aagge [_eggb ];return _fcdea [_fcef ].Lly < _fcdea [_beca ].Lly ;
});return _aagge ;};func (_cdgg *textPara )writeCellText (_cbcab _b .Writer ){for _caacaa ,_bcbba :=range _cdgg ._geffa {_bfdeb :=_bcbba .text ();_cgff :=_egg &&_bcbba .endsInHyphen ()&&_caacaa !=len (_cdgg ._geffa )-1;if _cgff {_bfdeb =_ffbb (_bfdeb );
};_cbcab .Write ([]byte (_bfdeb ));if !(_cgff ||_caacaa ==len (_cdgg ._geffa )-1){_cbcab .Write ([]byte (_eedfa (_bcbba ._gdca ,_cdgg ._geffa [_caacaa +1]._gdca )));};};};func (_caggd *wordBag )scanBand (_abdde string ,_cabe *wordBag ,_fcfg func (_cefg *wordBag ,_eebd *textWord )bool ,_ffbd ,_ega ,_ggffd float64 ,_dgbc ,_bdeg bool )int {_bcec :=_cabe ._aafec ;
var _eedd map[int ]map[*textWord ]struct{};if !_dgbc {_eedd =_caggd .makeRemovals ();};_feae :=_bafg *_bcec ;_aacc :=0;for _ ,_ebab :=range _caggd .depthBand (_ffbd -_feae ,_ega +_feae ){if len (_caggd ._fggf [_ebab ])==0{continue ;};for _ ,_gebe :=range _caggd ._fggf [_ebab ]{if !(_ffbd -_feae <=_gebe ._bfff &&_gebe ._bfff <=_ega +_feae ){continue ;
};if !_fcfg (_cabe ,_gebe ){continue ;};_cfdf :=2.0*_da .Abs (_gebe ._dcca -_cabe ._aafec )/(_gebe ._dcca +_cabe ._aafec );_agd :=_da .Max (_gebe ._dcca /_cabe ._aafec ,_cabe ._aafec /_gebe ._dcca );_dcf :=_da .Min (_cfdf ,_agd );if _ggffd > 0&&_dcf > _ggffd {continue ;
};if _cabe .blocked (_gebe ){continue ;};if !_dgbc {_cabe .pullWord (_gebe ,_ebab ,_eedd );};_aacc ++;if !_bdeg {if _gebe ._bfff < _ffbd {_ffbd =_gebe ._bfff ;};if _gebe ._bfff > _ega {_ega =_gebe ._bfff ;};};if _dgbc {break ;};};};if !_dgbc {_caggd .applyRemovals (_eedd );
};return _aacc ;};

// ToText returns the page text as a single string.
// Deprecated: This function is deprecated and will be removed in a future major version. Please use
// Text() instead.
func (_eada PageText )ToText ()string {return _eada .Text ()};func _cccb (_gefg bounded )float64 {return -_gefg .bbox ().Lly };func (_adfa paraList )tables ()[]TextTable {var _eadf []TextTable ;if _ababc {_f .Log .Info ("\u0070\u0061\u0072\u0061\u0073\u002e\u0074\u0061\u0062\u006c\u0065\u0073\u003a");
};for _ ,_decda :=range _adfa {_bcce :=_decda ._dcde ;if _bcce !=nil &&_bcce .isExportable (){_eadf =append (_eadf ,_bcce .toTextTable ());};};return _eadf ;};func (_ddefc *compositeCell )updateBBox (){for _ ,_abbf :=range _ddefc .paraList {_ddefc .PdfRectangle =_cbba (_ddefc .PdfRectangle ,_abbf .PdfRectangle );
};};func (_aeedcc *wordBag )highestWord (_ggbd int ,_ceec ,_geege float64 )*textWord {for _ ,_feda :=range _aeedcc ._fggf [_ggbd ]{if _ceec <=_feda ._bfff &&_feda ._bfff <=_geege {return _feda ;};};return nil ;};func (_dfdab rulingList )tidied (_dacac string )rulingList {_ebeaf :=_dfdab .removeDuplicates ();
_ebeaf .log ("\u0075n\u0069\u0071\u0075\u0065\u0073");_bgebf :=_ebeaf .snapToGroups ();if _bgebf ==nil {return nil ;};_bgebf .sort ();if _bggcg {_f .Log .Info ("\u0074\u0069\u0064i\u0065\u0064\u003a\u0020\u0025\u0071\u0020\u0076\u0065\u0063\u0073\u003d\u0025\u0064\u0020\u0075\u006e\u0069\u0071\u0075\u0065\u0073\u003d\u0025\u0064\u0020\u0063\u006f\u0061l\u0065\u0073\u0063\u0065\u0064\u003d\u0025\u0064",_dacac ,len (_dfdab ),len (_ebeaf ),len (_bgebf ));
};_bgebf .log ("\u0063o\u0061\u006c\u0065\u0073\u0063\u0065d");return _bgebf ;};func (_abaa *stateStack )empty ()bool {return len (*_abaa )==0};func _cdfaa (_abba _ea .PdfObject ,_bdbd _ca .Color )(_ee .Image ,error ){_eagc ,_cegd :=_ea .GetStream (_abba );
if !_cegd {return nil ,nil ;};_bdfef ,_feffd :=_cc .NewXObjectImageFromStream (_eagc );if _feffd !=nil {return nil ,_feffd ;};_acegd ,_feffd :=_bdfef .ToImage ();if _feffd !=nil {return nil ,_feffd ;};return _fgge (_acegd ,_bdbd ),nil ;};func (_bbf *imageExtractContext )extractFormImages (_ceb *_ea .PdfObjectName ,_efa _fb .GraphicsState ,_efb *_cc .PdfPageResources )error {_ccae ,_dcdg :=_efb .GetXObjectFormByName (*_ceb );
if _dcdg !=nil {return _dcdg ;};if _ccae ==nil {return nil ;};_dfea ,_dcdg :=_ccae .GetContentStream ();if _dcdg !=nil {return _dcdg ;};_dfef :=_ccae .Resources ;if _dfef ==nil {_dfef =_efb ;};_dcdg =_bbf .extractContentStreamImages (string (_dfea ),_dfef );
if _dcdg !=nil {return _dcdg ;};_bbf ._ffd ++;return nil ;};func _cceg (_fccga int ,_agfde func (int ,int )bool )[]int {_abfcf :=make ([]int ,_fccga );for _aeaef :=range _abfcf {_abfcf [_aeaef ]=_aeaef ;};_be .Slice (_abfcf ,func (_abff ,_dcgcg int )bool {return _agfde (_abfcf [_abff ],_abfcf [_dcgcg ])});
return _abfcf ;};const _edba =10;func _bfee (_gbccff float64 )bool {return _da .Abs (_gbccff )< _gdda };

// RenderMode specifies the text rendering mode (Tmode), which determines whether showing text shall cause
// glyph outlines to be  stroked, filled, used as a clipping boundary, or some combination of the three.
// Stroking, filling, and clipping shall have the same effects for a text object as they do for a path object
// (see 8.5.3, "Path-Painting Operators" and 8.5.4, "Clipping Path Operators").
type RenderMode int ;func (_dbfac *structTreeRoot )buildList (_aacd map[int ][]*textLine ,_cfcc _ea .PdfObject )[]*list {if _dbfac ==nil {_f .Log .Debug ("\u0062\u0075\u0069\u006c\u0064\u004c\u0069\u0073\u0074\u003a\u0020t\u0072\u0065\u0065\u0052\u006f\u006f\u0074\u0020\u0069\u0073 \u006e\u0069\u006c");
return nil ;};var _fbfgc *structElement ;_fbdcd :=[]structElement {};if len (_dbfac ._ffedd )==1{_bfde :=_dbfac ._ffedd [0]._eeagb ;if _bfde =="\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074"||_bfde =="\u0053\u0065\u0063\u0074"||_bfde =="\u0050\u0061\u0072\u0074"||_bfde =="\u0044\u0069\u0076"||_bfde =="\u0041\u0072\u0074"{_fbfgc =&_dbfac ._ffedd [0];
};}else {_fbfgc =&structElement {_gedge :_dbfac ._ffedd ,_eeagb :_dbfac ._cafcf };};if _fbfgc ==nil {_f .Log .Debug ("\u0062\u0075\u0069\u006cd\u004c\u0069\u0073\u0074\u003a\u0020\u0074\u006f\u0070\u0045l\u0065m\u0065\u006e\u0074\u0020\u0069\u0073\u0020n\u0069\u006c");
return nil ;};for _ ,_abdb :=range _fbfgc ._gedge {if _abdb ._eeagb =="\u004c"{_fbdcd =append (_fbdcd ,_abdb );}else if _abdb ._eeagb =="\u0054\u0061\u0062l\u0065"{_edbef :=_aecd (_abdb );_fbdcd =append (_fbdcd ,_edbef ...);};};_dbgge :=_cfca (_fbdcd ,_aacd ,_cfcc );
var _dcc []*list ;for _ ,_gcce :=range _dbgge {_bgfc :=_eebb (_gcce );_dcc =append (_dcc ,_bgfc ...);};return _dcc ;};type subpath struct{_cggf []_ce .Point ;_cafc bool ;};func (_ecbac rulingList )blocks (_geed ,_fgdfb *ruling )bool {if _geed ._bfbdb > _fgdfb ._bedbg ||_fgdfb ._bfbdb > _geed ._bedbg {return false ;
};_adge :=_da .Max (_geed ._bfbdb ,_fgdfb ._bfbdb );_bddae :=_da .Min (_geed ._bedbg ,_fgdfb ._bedbg );if _geed ._gdaaf > _fgdfb ._gdaaf {_geed ,_fgdfb =_fgdfb ,_geed ;};for _ ,_gdafd :=range _ecbac {if _geed ._gdaaf <=_gdafd ._gdaaf +_accd &&_gdafd ._gdaaf <=_fgdfb ._gdaaf +_accd &&_gdafd ._bfbdb <=_bddae &&_adge <=_gdafd ._bedbg {return true ;
};};return false ;};func (_fcfdde *textMark )inDiacriticArea (_bbcg *textMark )bool {_ggfa :=_fcfdde .Llx -_bbcg .Llx ;_ddbf :=_fcfdde .Urx -_bbcg .Urx ;_cgde :=_fcfdde .Lly -_bbcg .Lly ;return _da .Abs (_ggfa +_ddbf )< _fcfdde .Width ()*_fbec &&_da .Abs (_cgde )< _fcfdde .Height ()*_fbec ;
};

// String returns a description of `state`.
func (_cgeg *textState )String ()string {_ade :="\u005bN\u004f\u0054\u0020\u0053\u0045\u0054]";if _cgeg ._dbcb !=nil {_ade =_cgeg ._dbcb .BaseFont ();};return _ge .Sprintf ("\u0074\u0063\u003d\u0025\u002e\u0032\u0066\u0020\u0074\u0077\u003d\u0025\u002e\u0032\u0066 \u0074f\u0073\u003d\u0025\u002e\u0032\u0066\u0020\u0066\u006f\u006e\u0074\u003d\u0025\u0071",_cgeg ._eegf ,_cgeg ._eeef ,_cgeg ._fcba ,_ade );
};func (_eceg *textLine )endsInHyphen ()bool {_gcga :=_eceg ._ebgf [len (_eceg ._ebgf )-1];_gced :=_gcga ._ggeag ;_acaad ,_gfcc :=_fa .DecodeLastRuneInString (_gced );if _gfcc <=0||!_e .Is (_e .Hyphen ,_acaad ){return false ;};if _gcga ._fabaa &&_ceee (_gced ){return true ;
};return _ceee (_eceg .text ());};func _bceag (_gege _cc .PdfRectangle )*ruling {return &ruling {_befeb :_degg ,_gdaaf :_gege .Urx ,_bfbdb :_gege .Lly ,_bedbg :_gege .Ury };};func _gcb (_efbe []int ,_cfce *TextMarkArray ,_ecag string )(*TextMarkArray ,Box ,error ){_bffc :=Box {};
_bbe :=_efbe [0];_ebc :=_efbe [1];_afbb :=len (_ecag )-len (_ag .TrimLeft (_ecag ,"\u0020"));_dbgc :=len (_ecag )-len (_ag .TrimRight (_ecag ,"\u0020\u000a"));_bbe =_bbe +_afbb ;_ebc =_ebc -_dbgc ;_dcda ,_aad :=_cfce .RangeOffset (_bbe ,_ebc );if _aad !=nil {return nil ,_bffc ,_aad ;
};_dee ,_eeda :=_dcda .BBox ();if !_eeda {return nil ,_bffc ,_ge .Errorf ("\u0073\u0070\u0061\u006e\u004d\u0061\u0072\u006bs\u002e\u0042\u0042ox\u0020\u0068\u0061\u0073\u0020\u006eo\u0020\u0062\u006f\u0075\u006e\u0064\u0069\u006e\u0067\u0020\u0062\u006f\u0078\u002e\u0020s\u0070\u0061\u006e\u004d\u0061\u0072\u006b\u0073=\u0025\u0073",_dcda );
};_bffc =Box {BBox :_dee };return _dcda ,_bffc ,nil ;};type lineRuling struct{_eead rulingKind ;_gbgde markKind ;_ca .Color ;_gadd ,_beac _ce .Point ;};func (_aeeg *wordBag )minDepth ()float64 {return _aeeg ._afbd -(_aeeg .Ury -_aeeg ._aafec )};func (_dbgbc *textObject )showText (_ecgc _ea .PdfObject ,_fafc []byte ,_cfda int ,_afdd string )error {return _dbgbc .renderText (_ecgc ,_fafc ,_cfda ,_afdd );
};func (_dfee rulingList )primMinMax ()(float64 ,float64 ){_gfbbc ,_eceaf :=_dfee [0]._gdaaf ,_dfee [0]._gdaaf ;for _ ,_aaea :=range _dfee [1:]{if _aaea ._gdaaf < _gfbbc {_gfbbc =_aaea ._gdaaf ;}else if _aaea ._gdaaf > _eceaf {_eceaf =_aaea ._gdaaf ;};
};return _gfbbc ,_eceaf ;};var _befec =map[rulingKind ]string {_dcbd :"\u006e\u006f\u006e\u0065",_gfbdf :"\u0068\u006f\u0072\u0069\u007a\u006f\u006e\u0074\u0061\u006c",_degg :"\u0076\u0065\u0072\u0074\u0069\u0063\u0061\u006c"};var _gfggb *_ec .Regexp =_ec .MustCompile (_defg +"\u007c"+_aceg );


// Append appends `mark` to the mark array.
func (_dga *TextMarkArray )Append (mark TextMark ){_dga ._aecad =append (_dga ._aecad ,mark )};func (_agfg *shapesState )addPoint (_adga ,_gebc float64 ){_cdcb :=_agfg .establishSubpath ();_bcg :=_agfg .devicePoint (_adga ,_gebc );if _cdcb ==nil {_agfg ._ffdgg =true ;
_agfg ._ffefa =_bcg ;}else {_cdcb .add (_bcg );};};func (_cdgaa *ruling )gridIntersecting (_bggdb *ruling )bool {return _cgbe (_cdgaa ._bfbdb ,_bggdb ._bfbdb )&&_cgbe (_cdgaa ._bedbg ,_bggdb ._bedbg );};func _edbab (_beggf *list ,_cffg *_ag .Builder ,_geefe *string ){_fga :=_bebc (_beggf ,_geefe );
_cffg .WriteString (_fga );for _ ,_gbeee :=range _beggf ._bafe {_cebg :=*_geefe +"\u0020\u0020\u0020";_edbab (_gbeee ,_cffg ,&_cebg );};};func (_eafde rulingList )secMinMax ()(float64 ,float64 ){_bgebb ,_ffcc :=_eafde [0]._bfbdb ,_eafde [0]._bedbg ;for _ ,_fdcbc :=range _eafde [1:]{if _fdcbc ._bfbdb < _bgebb {_bgebb =_fdcbc ._bfbdb ;
};if _fdcbc ._bedbg > _ffcc {_ffcc =_fdcbc ._bedbg ;};};return _bgebb ,_ffcc ;};func _ggggg (_fgff []*textLine )[]*textLine {_dcgaf :=[]*textLine {};for _ ,_gead :=range _fgff {_bcee :=_gead .text ();_cfeg :=_gfggb .Find ([]byte (_bcee ));if _cfeg !=nil {_dcgaf =append (_dcgaf ,_gead );
};};return _dcgaf ;};func (_aagc *subpath )removeDuplicates (){if len (_aagc ._cggf )==0{return ;};_gcfg :=[]_ce .Point {_aagc ._cggf [0]};for _ ,_babg :=range _aagc ._cggf [1:]{if !_dgcc (_babg ,_gcfg [len (_gcfg )-1]){_gcfg =append (_gcfg ,_babg );};
};_aagc ._cggf =_gcfg ;};func (_abcf paraList )toTextMarks ()[]TextMark {_ffdc :=0;var _bcfe []TextMark ;for _ebba ,_gbbe :=range _abcf {if _gbbe ._gaebg {continue ;};_bagff :=_gbbe .toTextMarks (&_ffdc );_bcfe =append (_bcfe ,_bagff ...);if _ebba !=len (_abcf )-1{if _egdc (_gbbe ,_abcf [_ebba +1]){_bcfe =_baegc (_bcfe ,&_ffdc ,"\u0020");
}else {_bcfe =_baegc (_bcfe ,&_ffdc ,"\u000a");_bcfe =_baegc (_bcfe ,&_ffdc ,"\u000a");};};};_bcfe =_baegc (_bcfe ,&_ffdc ,"\u000a");_bcfe =_baegc (_bcfe ,&_ffdc ,"\u000a");return _bcfe ;};func (_egbd *wordBag )applyRemovals (_gfa map[int ]map[*textWord ]struct{}){for _gbee ,_eadb :=range _gfa {if len (_eadb )==0{continue ;
};_ggbag :=_egbd ._fggf [_gbee ];_gggc :=len (_ggbag )-len (_eadb );if _gggc ==0{delete (_egbd ._fggf ,_gbee );continue ;};_gccdg :=make ([]*textWord ,_gggc );_cagbd :=0;for _ ,_dfead :=range _ggbag {if _ ,_geee :=_eadb [_dfead ];!_geee {_gccdg [_cagbd ]=_dfead ;
_cagbd ++;};};_egbd ._fggf [_gbee ]=_gccdg ;};};

// BBox returns the smallest axis-aligned rectangle that encloses all the TextMarks in `ma`.
func (_fdfg *TextMarkArray )BBox ()(_cc .PdfRectangle ,bool ){var _fffa _cc .PdfRectangle ;_cdag :=false ;for _ ,_abeg :=range _fdfg ._aecad {if _abeg .Meta ||_cffdg (_abeg .Text ){continue ;};if _cdag {_fffa =_cbba (_fffa ,_abeg .BBox );}else {_fffa =_abeg .BBox ;
_cdag =true ;};};return _fffa ,_cdag ;};const (_egg =true ;_adfd =true ;_fcfda =true ;_dabf =false ;_cgfb =false ;_bffgc =6;_abbe =3.0;_baeg =200;_fdfcb =true ;_gbaa =true ;_gefae =true ;_cdac =true ;_gcbge =false ;);func (_ceac *shapesState )fill (_efccd *[]pathSection ){_agcb :=pathSection {_gdeb :_ceac ._edde ,Color :_ceac ._abef .getFillColor ()};
*_efccd =append (*_efccd ,_agcb );if _bggcg {_dabg :=_agcb .bbox ();_ge .Printf ("\u0020 \u0020\u0020\u0046\u0049\u004c\u004c\u003a %\u0032\u0064\u0020\u0066\u0069\u006c\u006c\u0073\u0020\u0028\u0025\u0064\u0020\u006ee\u0077\u0029 \u0073\u0073\u003d%\u0073\u0020\u0063\u006f\u006c\u006f\u0072\u003d\u0025\u0033\u0076\u0020\u0025\u0036\u002e\u0032f\u003d\u00256.\u0032\u0066\u0078%\u0036\u002e\u0032\u0066\u000a",len (*_efccd ),len (_agcb ._gdeb ),_ceac ,_agcb .Color ,_dabg ,_dabg .Width (),_dabg .Height ());
if _dedc {for _dbee ,_fdga :=range _agcb ._gdeb {_ge .Printf ("\u0025\u0038\u0064\u003a\u0020\u0025\u0073\u000a",_dbee ,_fdga );if _dbee ==10{break ;};};};};};func (_cacc paraList )writeText (_cdeb _b .Writer ){for _bfgc ,_aadge :=range _cacc {if _aadge ._gaebg {continue ;
};_aadge .writeText (_cdeb );if _bfgc !=len (_cacc )-1{if _egdc (_aadge ,_cacc [_bfgc +1]){_cdeb .Write ([]byte ("\u0020"));}else {_cdeb .Write ([]byte ("\u000a"));_cdeb .Write ([]byte ("\u000a"));};};};_cdeb .Write ([]byte ("\u000a"));_cdeb .Write ([]byte ("\u000a"));
};func (_ggga rulingList )toGrids ()[]rulingList {if _bggcg {_f .Log .Info ("t\u006f\u0047\u0072\u0069\u0064\u0073\u003a\u0020\u0025\u0073",_ggga );};_deafd :=_ggga .intersections ();if _bggcg {_f .Log .Info ("\u0074\u006f\u0047r\u0069\u0064\u0073\u003a \u0076\u0065\u0063\u0073\u003d\u0025\u0064 \u0069\u006e\u0074\u0065\u0072\u0073\u0065\u0063\u0074\u0073\u003d\u0025\u0064\u0020",len (_ggga ),len (_deafd ));
for _ ,_befag :=range _gdfbe (_deafd ){_ge .Printf ("\u00254\u0064\u003a\u0020\u0025\u002b\u0076\n",_befag ,_deafd [_befag ]);};};_ffgb :=make (map[int ]intSet ,len (_ggga ));for _eeadf :=range _ggga {_efgg :=_ggga .connections (_deafd ,_eeadf );if len (_efgg )> 0{_ffgb [_eeadf ]=_efgg ;
};};if _bggcg {_f .Log .Info ("t\u006fG\u0072\u0069\u0064\u0073\u003a\u0020\u0063\u006fn\u006e\u0065\u0063\u0074s=\u0025\u0064",len (_ffgb ));for _ ,_feec :=range _gdfbe (_ffgb ){_ge .Printf ("\u00254\u0064\u003a\u0020\u0025\u002b\u0076\n",_feec ,_ffgb [_feec ]);
};};_cedfb :=_cceg (len (_ggga ),func (_abac ,_bcgaf int )bool {_ggcfe ,_decff :=len (_ffgb [_abac ]),len (_ffgb [_bcgaf ]);if _ggcfe !=_decff {return _ggcfe > _decff ;};return _ggga .comp (_abac ,_bcgaf );});if _bggcg {_f .Log .Info ("t\u006fG\u0072\u0069\u0064\u0073\u003a\u0020\u006f\u0072d\u0065\u0072\u0069\u006eg=\u0025\u0076",_cedfb );
};_gfadd :=[][]int {{_cedfb [0]}};_fega :for _ ,_bfaac :=range _cedfb [1:]{for _eabfg ,_afadb :=range _gfadd {for _ ,_acga :=range _afadb {if _ffgb [_acga ].has (_bfaac ){_gfadd [_eabfg ]=append (_afadb ,_bfaac );continue _fega ;};};};_gfadd =append (_gfadd ,[]int {_bfaac });
};if _bggcg {_f .Log .Info ("\u0074o\u0047r\u0069\u0064\u0073\u003a\u0020i\u0067\u0072i\u0064\u0073\u003d\u0025\u0076",_gfadd );};_be .SliceStable (_gfadd ,func (_fabd ,_abce int )bool {return len (_gfadd [_fabd ])> len (_gfadd [_abce ])});for _ ,_abbd :=range _gfadd {_be .Slice (_abbd ,func (_eafg ,_gccdc int )bool {return _ggga .comp (_abbd [_eafg ],_abbd [_gccdc ])});
};_gcfdf :=make ([]rulingList ,len (_gfadd ));for _febd ,_gceea :=range _gfadd {_bacfd :=make (rulingList ,len (_gceea ));for _abcdf ,_dcfeg :=range _gceea {_bacfd [_abcdf ]=_ggga [_dcfeg ];};_gcfdf [_febd ]=_bacfd ;};if _bggcg {_f .Log .Info ("\u0074o\u0047r\u0069\u0064\u0073\u003a\u0020g\u0072\u0069d\u0073\u003d\u0025\u002b\u0076",_gcfdf );
};var _cbbb []rulingList ;for _ ,_gcbga :=range _gcfdf {if _afae ,_aecbb :=_gcbga .isActualGrid ();_aecbb {_gcbga =_afae ;_gcbga =_gcbga .snapToGroups ();_cbbb =append (_cbbb ,_gcbga );};};if _bggcg {_gaaa ("t\u006fG\u0072\u0069\u0064\u0073\u003a\u0020\u0061\u0063t\u0075\u0061\u006c\u0047ri\u0064\u0073",_cbbb );
_f .Log .Info ("\u0074\u006f\u0047\u0072\u0069\u0064\u0073\u003a\u0020\u0067\u0072\u0069\u0064\u0073\u003d%\u0064 \u0061\u0063\u0074\u0075\u0061\u006c\u0047\u0072\u0069\u0064\u0073\u003d\u0025\u0064",len (_gcfdf ),len (_cbbb ));};return _cbbb ;};func (_ggffa rulingList )vertsHorzs ()(rulingList ,rulingList ){var _cedd ,_bgde rulingList ;
for _ ,_adcef :=range _ggffa {switch _adcef ._befeb {case _degg :_cedd =append (_cedd ,_adcef );case _gfbdf :_bgde =append (_bgde ,_adcef );};};return _cedd ,_bgde ;};func _ffbb (_ffbbc string )string {_ebdbe :=[]rune (_ffbbc );return string (_ebdbe [:len (_ebdbe )-1])};
type event struct{_fbaddc float64 ;_ecfdg bool ;_efdb int ;};func (_gfdaf *wordBag )maxDepth ()float64 {return _gfdaf ._afbd -_gfdaf .Lly };

// String returns a human readable description of `s`.
func (_ddda intSet )String ()string {var _gacde []int ;for _dcbgg :=range _ddda {if _ddda .has (_dcbgg ){_gacde =append (_gacde ,_dcbgg );};};_be .Ints (_gacde );return _ge .Sprintf ("\u0025\u002b\u0076",_gacde );};func _bgdc (_decdc _ce .Point )*subpath {return &subpath {_cggf :[]_ce .Point {_decdc }}};
func (_cfbc *wordBag )depthRange (_edca ,_afcec int )[]int {var _dgdcc []int ;for _bbaa :=range _cfbc ._fggf {if _edca <=_bbaa &&_bbaa <=_afcec {_dgdcc =append (_dgdcc ,_bbaa );};};if len (_dgdcc )==0{return nil ;};_be .Ints (_dgdcc );return _dgdcc ;};
func (_fcecd paraList )findGridTables (_aeefc []gridTiling )[]*textTable {if _ababc {_f .Log .Info ("\u0066i\u006e\u0064\u0047\u0072\u0069\u0064\u0054\u0061\u0062\u006c\u0065s\u003a\u0020\u0025\u0064\u0020\u0070\u0061\u0072\u0061\u0073",len (_fcecd ));
for _efbba ,_cgfc :=range _fcecd {_ge .Printf ("\u0025\u0034\u0064\u003a\u0020\u0025\u0073\u000a",_efbba ,_cgfc );};};var _adef []*textTable ;for _gdff ,_fabc :=range _aeefc {_baffb ,_dfcea :=_fcecd .findTableGrid (_fabc );if _baffb !=nil {_baffb .log (_ge .Sprintf ("\u0066\u0069\u006e\u0064Ta\u0062\u006c\u0065\u0057\u0069\u0074\u0068\u0047\u0072\u0069\u0064\u0073\u003a\u0020%\u0064",_gdff ));
_adef =append (_adef ,_baffb );_baffb .markCells ();};for _gaad :=range _dfcea {_gaad ._dddg =true ;};};if _ababc {_f .Log .Info ("\u0066i\u006e\u0064\u0047\u0072i\u0064\u0054\u0061\u0062\u006ce\u0073:\u0020%\u0064\u0020\u0074\u0061\u0062\u006c\u0065s",len (_adef ));
};return _adef ;};func (_effe compositeCell )hasLines (_edeea []*textLine )bool {for _gbefc ,_ffaf :=range _edeea {_dbgcf :=_aadgd (_effe .PdfRectangle ,_ffaf .PdfRectangle );if _ababc {_ge .Printf ("\u0020\u0020\u0020\u0020\u0020\u0020\u005e\u005e\u005e\u0069\u006e\u0074\u0065\u0072\u0073e\u0063t\u0073\u003d\u0025\u0074\u0020\u0025\u0064\u0020\u006f\u0066\u0020\u0025\u0064\u000a",_dbgcf ,_gbefc ,len (_edeea ));
_ge .Printf ("\u0020\u0020\u0020\u0020  \u005e\u005e\u005e\u0063\u006f\u006d\u0070\u006f\u0073\u0069\u0074\u0065\u003d\u0025s\u000a",_effe );_ge .Printf ("\u0020 \u0020 \u0020\u0020\u0020\u006c\u0069\u006e\u0065\u003d\u0025\u0073\u000a",_ffaf );};if _dbgcf {return true ;
};};return false ;};func (_adec *shapesState )lineTo (_cgbc ,_aggb float64 ){if _bfgf {_f .Log .Info ("\u006c\u0069\u006eeT\u006f\u0028\u0025\u002e\u0032\u0066\u002c\u0025\u002e\u0032\u0066\u0020\u0070\u003d\u0025\u002e\u0032\u0066",_cgbc ,_aggb ,_adec .devicePoint (_cgbc ,_aggb ));
};_adec .addPoint (_cgbc ,_aggb );};func _cafd (_aeac float64 ,_gagce int )int {if _gagce ==0{_gagce =1;};_fccbf :=float64 (_gagce );return int (_da .Round (_aeac /_fccbf )*_fccbf );};func (_dfbd *textObject )getFontDirect (_ecfb string )(*_cc .PdfFont ,error ){_dgbg ,_adaa :=_dfbd .getFontDict (_ecfb );
if _adaa !=nil {return nil ,_adaa ;};_eccd ,_adaa :=_cc .NewPdfFontFromPdfObject (_dgbg );if _adaa !=nil {_f .Log .Debug ("\u0067\u0065\u0074\u0046\u006f\u006e\u0074\u0044\u0069\u0072\u0065\u0063\u0074\u003a\u0020\u004e\u0065\u0077Pd\u0066F\u006f\u006e\u0074\u0046\u0072\u006f\u006d\u0050\u0064\u0066\u004f\u0062j\u0065\u0063\u0074\u0020\u0066\u0061\u0069\u006c\u0065\u0064\u002e\u0020\u006e\u0061\u006d\u0065\u003d%\u0023\u0071\u0020\u0065\u0072\u0072\u003d\u0025\u0076",_ecfb ,_adaa );
};return _eccd ,_adaa ;};

// Text returns the text content of the `bulletLists`.
func (_bbgbg *lists )Text ()string {_afcdb :=&_ag .Builder {};for _ ,_bfbd :=range *_bbgbg {_cade :=_bfbd .Text ();_afcdb .WriteString (_cade );};return _afcdb .String ();};

// String returns a string describing the current state of the textState stack.
func (_beag *stateStack )String ()string {_aeeab :=[]string {_ge .Sprintf ("\u002d\u002d\u002d\u002d f\u006f\u006e\u0074\u0020\u0073\u0074\u0061\u0063\u006b\u003a\u0020\u0025\u0064",len (*_beag ))};for _ggcc ,_cfdad :=range *_beag {_fagf :="\u003c\u006e\u0069l\u003e";
if _cfdad !=nil {_fagf =_cfdad .String ();};_aeeab =append (_aeeab ,_ge .Sprintf ("\u0009\u0025\u0032\u0064\u003a\u0020\u0025\u0073",_ggcc ,_fagf ));};return _ag .Join (_aeeab ,"\u000a");};func (_bgeea rulingList )log (_dgaae string ){if !_bggcg {return ;
};_f .Log .Info ("\u0023\u0023\u0023\u0020\u0025\u0031\u0030\u0073\u003a\u0020\u0076\u0065c\u0073\u003d\u0025\u0073",_dgaae ,_bgeea .String ());for _afffg ,_ffbf :=range _bgeea {_ge .Printf ("\u0025\u0034\u0064\u003a\u0020\u0025\u0073\u000a",_afffg ,_ffbf .String ());
};};func _cgbce (_cdfda []*textWord ,_cgfee *textWord )[]*textWord {for _deca ,_adfef :=range _cdfda {if _adfef ==_cgfee {return _bfacfd (_cdfda ,_deca );};};_f .Log .Error ("\u0072\u0065\u006d\u006f\u0076e\u0057\u006f\u0072\u0064\u003a\u0020\u0077\u006f\u0072\u0064\u0073\u0020\u0064o\u0065\u0073\u006e\u0027\u0074\u0020\u0063\u006f\u006e\u0074\u0061\u0069\u006e\u0020\u0077\u006f\u0072\u0064\u003d\u0025\u0073",_cgfee );
return nil ;};type cachedImage struct{_cge *_cc .Image ;_bfa _cc .PdfColorspace ;};func (_gfcd rulingList )isActualGrid ()(rulingList ,bool ){_agdg ,_edbb :=_gfcd .augmentGrid ();if !(len (_agdg )>=_cdbg +1&&len (_edbb )>=_dcbfb +1){if _bggcg {_f .Log .Info ("\u0069s\u0041\u0063t\u0075\u0061\u006c\u0047r\u0069\u0064\u003a \u004e\u006f\u0074\u0020\u0061\u006c\u0069\u0067\u006eed\u002e\u0020\u0025d\u0020\u0078 \u0025\u0064\u0020\u003c\u0020\u0025d\u0020\u0078 \u0025\u0064",len (_agdg ),len (_edbb ),_cdbg +1,_dcbfb +1);
};return nil ,false ;};if _bggcg {_f .Log .Info ("\u0069\u0073\u0041\u0063\u0074\u0075a\u006c\u0047\u0072\u0069\u0064\u003a\u0020\u0025\u0073\u0020\u003a\u0020\u0025t\u0020\u0026\u0020\u0025\u0074\u0020\u2192 \u0025\u0074",_gfcd ,len (_agdg )>=2,len (_edbb )>=2,len (_agdg )>=2&&len (_edbb )>=2);
for _addae ,_gecc :=range _gfcd {_ge .Printf ("\u0025\u0034\u0064\u003a\u0020\u0025\u0076\u000a",_addae ,_gecc );};};if _gcbge {_eaag ,_gbcef :=_agdg [0],_agdg [len (_agdg )-1];_cfbff ,_badag :=_edbb [0],_edbb [len (_edbb )-1];if !(_cdce (_eaag ._gdaaf -_cfbff ._bfbdb )&&_cdce (_gbcef ._gdaaf -_cfbff ._bedbg )&&_cdce (_cfbff ._gdaaf -_eaag ._bedbg )&&_cdce (_badag ._gdaaf -_eaag ._bfbdb )){if _bggcg {_f .Log .Info ("\u0069\u0073\u0041\u0063\u0074\u0075\u0061l\u0047\u0072\u0069d\u003a\u0020\u0020N\u006f\u0074 \u0061\u006c\u0069\u0067\u006e\u0065d\u002e\n\t\u0076\u0030\u003d\u0025\u0073\u000a\u0009\u0076\u0031\u003d\u0025\u0073\u000a\u0009\u0068\u0030\u003d\u0025\u0073\u000a\u0009\u0068\u0031\u003d\u0025\u0073",_eaag ,_gbcef ,_cfbff ,_badag );
};return nil ,false ;};}else {if !_agdg .aligned (){if _ffdf {_f .Log .Info ("i\u0073\u0041\u0063\u0074\u0075\u0061l\u0047\u0072\u0069\u0064\u003a\u0020N\u006f\u0074\u0020\u0061\u006c\u0069\u0067n\u0065\u0064\u0020\u0076\u0065\u0072\u0074\u0073\u002e\u0020%\u0064",len (_agdg ));
};return nil ,false ;};if !_edbb .aligned (){if _bggcg {_f .Log .Info ("i\u0073\u0041\u0063\u0074\u0075\u0061l\u0047\u0072\u0069\u0064\u003a\u0020N\u006f\u0074\u0020\u0061\u006c\u0069\u0067n\u0065\u0064\u0020\u0068\u006f\u0072\u007a\u0073\u002e\u0020%\u0064",len (_edbb ));
};return nil ,false ;};};_gbccf :=append (_agdg ,_edbb ...);return _gbccf ,true ;};func _cdbd (_fbafb map[float64 ]gridTile )[]float64 {_cebga :=make ([]float64 ,0,len (_fbafb ));for _ebbad :=range _fbafb {_cebga =append (_cebga ,_ebbad );};_be .Float64s (_cebga );
return _cebga ;};

// Match defines the structure for each match, including pattern, indexes, and locations.
type Match struct{Pattern string ;Indexes [][]int ;Locations []Box ;};func (_gccf *textObject )setHorizScaling (_fbfg float64 ){if _gccf ==nil {return ;};_gccf ._fea ._bbgg =_fbfg ;};func _acee (_befc *TextMarkArray ,_acdf *string ,_cgb *int ,_aef string )error {_eef :=_befc .Elements ()[0].DirectObject ;
_acag ,_fggb :=_ea .GetString (_eef );if !_fggb {return _ge .Errorf ("\u0075n\u0061\u0062l\u0065\u0020\u0074\u006f \u0067\u0065\u0074 \u0073\u0074\u0072\u0069\u006e\u0067\u0020\u0042\u0079te\u0073\u0020\u0066r\u006f\u006d \u0064\u0069\u0072\u0065\u0063\u0074O\u0062\u006ae\u0063\u0074");
};_bccf :=_ccfe (_befc );_fgfc ,_fggb :=_ea .GetStringBytes (_eef );if !_fggb {return _ea .ErrTypeError ;};_fef :=_befc .Elements ()[0].Font ;_fcgb :=_dbc (_fgfc ,_fef );_cfdd :="";_gcfe :=*_acdf ;if len (_gcfe )> *_cgb {_cfdd =_gcfe [*_cgb :*_cgb +len (_bccf )];
}else if *_cgb ==len (_aef )-1&&len (_gcfe )> *_cgb {_cfdd =_gcfe [*_cgb :];};_ebf :="";_fda :=_ag .Split (_fcgb ,"\u0020");_fddc :=_fda [len (_fda )-1];if _fddc ==_bccf &&*_cgb ==0{_ccd :=_ag .LastIndex (_fcgb ,_bccf );_ebf =_dece (_fcgb ,_ccd ,len (_bccf )+_ccd ,_cfdd );
}else if *_cgb ==len (_aef )-1&&len (_gcfe )> *_cgb {_ebf =_ag .Replace (_fcgb ,_bccf ,_gcfe [*_cgb :],-1);}else {_ebf =_ag .Replace (_fcgb ,_bccf ,_cfdd ,1);};_affg (_acag ,_ebf ,_fef );*_cgb +=len (_bccf );return nil ;};func (_bfac *subpath )add (_egbfa ..._ce .Point ){_bfac ._cggf =append (_bfac ._cggf ,_egbfa ...)};
func _eefcg (_cbfec _cc .PdfColorspace ,_bagfc _cc .PdfColor )_ca .Color {if _cbfec ==nil ||_bagfc ==nil {return _ca .Black ;};_dagfb ,_bgeee :=_cbfec .ColorToRGB (_bagfc );if _bgeee !=nil {_f .Log .Debug ("\u0057\u0041\u0052\u004e\u003a\u0020\u0063\u006fu\u006c\u0064\u0020no\u0074\u0020\u0063\u006f\u006e\u0076e\u0072\u0074\u0020\u0063\u006f\u006c\u006f\u0072\u0020\u0025\u0076\u0020\u0028\u0025\u0076)\u0020\u0074\u006f\u0020\u0052\u0047\u0042\u003a \u0025\u0073",_bagfc ,_cbfec ,_bgeee );
return _ca .Black ;};_fffec ,_cced :=_dagfb .(*_cc .PdfColorDeviceRGB );if !_cced {_f .Log .Debug ("\u0057\u0041\u0052\u004e\u003a\u0020\u0063\u006f\u006e\u0076\u0065\u0072\u0074\u0065\u0064 \u0063\u006f\u006c\u006f\u0072\u0020\u0069\u0073\u0020\u006e\u006f\u0074\u0020i\u006e\u0020\u0074\u0068\u0065\u0020\u0052\u0047\u0042\u0020\u0063\u006flo\u0072\u0073\u0070\u0061\u0063\u0065\u003a\u0020\u0025\u0076",_dagfb );
return _ca .Black ;};return _ca .NRGBA {R :uint8 (_fffec .R ()*255),G :uint8 (_fffec .G ()*255),B :uint8 (_fffec .B ()*255),A :uint8 (255)};};func (_dcga *textObject )setTextRise (_faa float64 ){if _dcga ==nil {return ;};_dcga ._fea ._daca =_faa ;};func _fbecd (_acgfa ,_ebgd _ce .Point ,_aggdc _ca .Color )(*ruling ,bool ){_gdebg :=lineRuling {_gadd :_acgfa ,_beac :_ebgd ,_eead :_bfefe (_acgfa ,_ebgd ),Color :_aggdc };
if _gdebg ._eead ==_dcbd {return nil ,false ;};return _gdebg .asRuling ();};func (_bdgbf rulingList )asTiling ()gridTiling {if _ffbg {_f .Log .Info ("r\u0075\u006ci\u006e\u0067\u004c\u0069\u0073\u0074\u002e\u0061\u0073\u0054\u0069\u006c\u0069\u006e\u0067\u003a\u0020\u0076\u0065\u0063s\u003d\u0025\u0064\u0020\u003d\u003d\u003d\u003d\u003d\u003d\u003d\u003d\u003d\u003d\u003d\u003d=\u003d\u003d\u003d\u003d\u003d\u002b\u002b\u002b\u0020\u003d\u003d\u003d\u003d=\u003d\u003d\u003d\u003d\u003d\u003d\u003d\u003d\u003d\u003d\u003d=\u003d",len (_bdgbf ));
};for _aaeb ,_baccf :=range _bdgbf [1:]{_acccc :=_bdgbf [_aaeb ];if _acccc .alignsPrimary (_baccf )&&_acccc .alignsSec (_baccf ){_f .Log .Error ("a\u0073\u0054\u0069\u006c\u0069\u006e\u0067\u003a\u0020\u0044\u0075\u0070\u006c\u0069\u0063\u0061\u0074\u0065 \u0072\u0075\u006c\u0069\u006e\u0067\u0073\u002e\u000a\u0009v=\u0025\u0073\u000a\t\u0077=\u0025\u0073",_baccf ,_acccc );
};};_bdgbf .sortStrict ();_bdgbf .log ("\u0073n\u0061\u0070\u0070\u0065\u0064");_egbbe ,_badab :=_bdgbf .vertsHorzs ();_cdae :=_egbbe .primaries ();_befg :=_badab .primaries ();_afba :=len (_cdae )-1;_defa :=len (_befg )-1;if _afba ==0||_defa ==0{return gridTiling {};
};_bbeg :=_cc .PdfRectangle {Llx :_cdae [0],Urx :_cdae [_afba ],Lly :_befg [0],Ury :_befg [_defa ]};if _ffbg {_f .Log .Info ("\u0072\u0075l\u0069\u006e\u0067\u004c\u0069\u0073\u0074\u002e\u0061\u0073\u0054\u0069\u006c\u0069\u006e\u0067\u003a\u0020\u0076\u0065\u0072\u0074s=\u0025\u0064",len (_egbbe ));
for _egca ,_badc :=range _egbbe {_ge .Printf ("\u0025\u0034\u0064\u003a\u0020\u0025\u0073\u000a",_egca ,_badc );};_f .Log .Info ("\u0072\u0075l\u0069\u006e\u0067\u004c\u0069\u0073\u0074\u002e\u0061\u0073\u0054\u0069\u006c\u0069\u006e\u0067\u003a\u0020\u0068\u006f\u0072\u007as=\u0025\u0064",len (_badab ));
for _dagba ,_ggdbc :=range _badab {_ge .Printf ("\u0025\u0034\u0064\u003a\u0020\u0025\u0073\u000a",_dagba ,_ggdbc );};_f .Log .Info ("\u0072\u0075\u006c\u0069\u006eg\u004c\u0069\u0073\u0074\u002e\u0061\u0073\u0054\u0069\u006c\u0069\u006e\u0067:\u0020\u0020\u0077\u0078\u0068\u003d\u0025\u0064\u0078\u0025\u0064\u000a\u0009\u006c\u006c\u0078\u003d\u0025\u002e\u0032\u0066\u000a\u0009\u006c\u006c\u0079\u003d\u0025\u002e\u0032f",_afba ,_defa ,_cdae ,_befg );
};_cabec :=make ([]gridTile ,_afba *_defa );for _ddcf :=_defa -1;_ddcf >=0;_ddcf --{_abge :=_befg [_ddcf ];_bdffe :=_befg [_ddcf +1];for _cdbce :=0;_cdbce < _afba ;_cdbce ++{_agcfe :=_cdae [_cdbce ];_daccf :=_cdae [_cdbce +1];_dada :=_egbbe .findPrimSec (_agcfe ,_abge );
_fbfee :=_egbbe .findPrimSec (_daccf ,_abge );_abage :=_badab .findPrimSec (_abge ,_agcfe );_bafdc :=_badab .findPrimSec (_bdffe ,_agcfe );_bgfa :=_cc .PdfRectangle {Llx :_agcfe ,Urx :_daccf ,Lly :_abge ,Ury :_bdffe };_afffc :=_ggddc (_bgfa ,_dada ,_fbfee ,_abage ,_bafdc );
_cabec [_ddcf *_afba +_cdbce ]=_afffc ;if _ffbg {_ge .Printf ("\u0020\u0020\u0078\u003d\u0025\u0032\u0064\u0020\u0079\u003d\u0025\u0032\u0064\u003a\u0020%\u0073 \u0025\u0036\u002e\u0032\u0066\u0020\u0078\u0020\u0025\u0036\u002e\u0032\u0066\u000a",_cdbce ,_ddcf ,_afffc .String (),_afffc .Width (),_afffc .Height ());
};};};if _ffbg {_f .Log .Info ("r\u0075\u006c\u0069\u006e\u0067\u004c\u0069\u0073\u0074.\u0061\u0073\u0054\u0069\u006c\u0069\u006eg:\u0020\u0063\u006f\u0061l\u0065\u0073\u0063\u0065\u0020\u0068\u006f\u0072\u0069zo\u006e\u0074a\u006c\u002e\u0020\u0025\u0036\u002e\u0032\u0066",_bbeg );
};_egfa :=make ([]map[float64 ]gridTile ,_defa );for _bcadc :=_defa -1;_bcadc >=0;_bcadc --{if _ffbg {_ge .Printf ("\u0020\u0020\u0079\u003d\u0025\u0032\u0064\u000a",_bcadc );};_egfa [_bcadc ]=make (map[float64 ]gridTile ,_afba );for _fcee :=0;_fcee < _afba ;
_fcee ++{_dcafc :=_cabec [_bcadc *_afba +_fcee ];if _ffbg {_ge .Printf ("\u0020\u0020\u0025\u0034\u0064\u003a\u0020\u0025\u0073\u000a",_fcee ,_dcafc );};if !_dcafc ._egeg {continue ;};_cgcf :=_fcee ;for _accfa :=_fcee +1;!_dcafc ._gbfec &&_accfa < _afba ;
_accfa ++{_afgdb :=_cabec [_bcadc *_afba +_accfa ];_dcafc .Urx =_afgdb .Urx ;_dcafc ._fbfd =_dcafc ._fbfd ||_afgdb ._fbfd ;_dcafc ._gfgcaf =_dcafc ._gfgcaf ||_afgdb ._gfgcaf ;_dcafc ._gbfec =_afgdb ._gbfec ;if _ffbg {_ge .Printf ("\u0020 \u0020%\u0034\u0064\u003a\u0020\u0025s\u0020\u2192 \u0025\u0073\u000a",_accfa ,_afgdb ,_dcafc );
};_cgcf =_accfa ;};if _ffbg {_ge .Printf (" \u0020 \u0025\u0032\u0064\u0020\u002d\u0020\u0025\u0032d\u0020\u2192\u0020\u0025s\n",_fcee ,_cgcf ,_dcafc );};_fcee =_cgcf ;_egfa [_bcadc ][_dcafc .Llx ]=_dcafc ;};};_dddfa :=make (map[float64 ]map[float64 ]gridTile ,_defa );
_cdacg :=make (map[float64 ]map[float64 ]struct{},_defa );for _fabeg :=_defa -1;_fabeg >=0;_fabeg --{_cbgc :=_cabec [_fabeg *_afba ].Lly ;_dddfa [_cbgc ]=make (map[float64 ]gridTile ,_afba );_cdacg [_cbgc ]=make (map[float64 ]struct{},_afba );};if _ffbg {_f .Log .Info ("\u0072u\u006c\u0069n\u0067\u004c\u0069s\u0074\u002e\u0061\u0073\u0054\u0069\u006ci\u006e\u0067\u003a\u0020\u0063\u006fa\u006c\u0065\u0073\u0063\u0065\u0020\u0076\u0065\u0072\u0074\u0069c\u0061\u006c\u002e\u0020\u0025\u0036\u002e\u0032\u0066",_bbeg );
};for _gffc :=_defa -1;_gffc >=0;_gffc --{_aafa :=_cabec [_gffc *_afba ].Lly ;_eefe :=_egfa [_gffc ];if _ffbg {_ge .Printf ("\u0020\u0020\u0079\u003d\u0025\u0032\u0064\u000a",_gffc );};for _ ,_efce :=range _cdbd (_eefe ){if _ ,_fecad :=_cdacg [_aafa ][_efce ];
_fecad {continue ;};_bbed :=_eefe [_efce ];if _ffbg {_ge .Printf (" \u0020\u0020\u0020\u0020\u0076\u0030\u003d\u0025\u0073\u000a",_bbed .String ());};for _beada :=_gffc -1;_beada >=0;_beada --{if _bbed ._gfgcaf {break ;};_fgga :=_egfa [_beada ];_ccbdd ,_bggb :=_fgga [_efce ];
if !_bggb {break ;};if _ccbdd .Urx !=_bbed .Urx {break ;};_bbed ._gfgcaf =_ccbdd ._gfgcaf ;_bbed .Lly =_ccbdd .Lly ;if _ffbg {_ge .Printf ("\u0020\u0020\u0020\u0020  \u0020\u0020\u0076\u003d\u0025\u0073\u0020\u0076\u0030\u003d\u0025\u0073\u000a",_ccbdd .String (),_bbed .String ());
};_cdacg [_ccbdd .Lly ][_ccbdd .Llx ]=struct{}{};};if _gffc ==0{_bbed ._gfgcaf =true ;};if _bbed .complete (){_dddfa [_aafa ][_efce ]=_bbed ;};};};_ffgbg :=gridTiling {PdfRectangle :_bbeg ,_gdec :_cefa (_dddfa ),_daeaf :_eacd (_dddfa ),_bceeb :_dddfa };
_ffgbg .log ("\u0043r\u0065\u0061\u0074\u0065\u0064");return _ffgbg ;};func (_fcabg paraList )inTile (_ccgab gridTile )paraList {var _bbeb paraList ;for _ ,_dbgbf :=range _fcabg {if _ccgab .contains (_dbgbf .PdfRectangle ){_bbeb =append (_bbeb ,_dbgbf );
};};if _ababc {_ge .Printf ("\u0020 \u0020\u0069\u006e\u0054i\u006c\u0065\u003a\u0020\u0020%\u0073 \u0069n\u0073\u0069\u0064\u0065\u003d\u0025\u0064\n",_ccgab ,len (_bbeb ));for _ecega ,_fbfed :=range _bbeb {_ge .Printf ("\u0025\u0034\u0064\u003a\u0020\u0025\u0073\u000a",_ecega ,_fbfed );
};_ge .Println ("");};return _bbeb ;};func (_ggfcc *ruling )intersects (_adbee *ruling )bool {_gcbe :=(_ggfcc ._befeb ==_degg &&_adbee ._befeb ==_gfbdf )||(_adbee ._befeb ==_degg &&_ggfcc ._befeb ==_gfbdf );_fcfaca :=func (_bbbc ,_ggaf *ruling )bool {return _bbbc ._bfbdb -_cefge <=_ggaf ._gdaaf &&_ggaf ._gdaaf <=_bbbc ._bedbg +_cefge ;
};_feag :=_fcfaca (_ggfcc ,_adbee );_dfbc :=_fcfaca (_adbee ,_ggfcc );if _bggcg {_ge .Printf ("\u0020\u0020\u0020\u0020\u0069\u006e\u0074\u0065\u0072\u0073\u0065\u0063\u0074\u0073\u003a\u0020\u0020\u006fr\u0074\u0068\u006f\u0067\u006f\u006e\u0061l\u003d\u0025\u0074\u0020\u006f\u0031\u003d\u0025\u0074\u0020\u006f2\u003d\u0025\u0074\u0020\u2192\u0020\u0025\u0074\u000a"+"\u0020\u0020\u0020 \u0020\u0020\u0020\u0076\u003d\u0025\u0073\u000a"+" \u0020\u0020\u0020\u0020\u0020\u0077\u003d\u0025\u0073\u000a",_gcbe ,_feag ,_dfbc ,_gcbe &&_feag &&_dfbc ,_ggfcc ,_adbee );
};return _gcbe &&_feag &&_dfbc ;};func _dafe (_bggcc []pathSection )rulingList {_acge (_bggcc );if _bggcg {_f .Log .Info ("\u006d\u0061k\u0065\u0053\u0074\u0072\u006f\u006b\u0065\u0052\u0075\u006c\u0069\u006e\u0067\u0073\u003a\u0020\u0025\u0064\u0020\u0073\u0074\u0072ok\u0065\u0073",len (_bggcc ));
};var _abcd rulingList ;for _ ,_cbfbc :=range _bggcc {for _ ,_fagd :=range _cbfbc ._gdeb {if len (_fagd ._cggf )< 2{continue ;};_ggfaa :=_fagd ._cggf [0];for _ ,_fcaf :=range _fagd ._cggf [1:]{if _agcda ,_fffaf :=_fbecd (_ggfaa ,_fcaf ,_cbfbc .Color );
_fffaf {_abcd =append (_abcd ,_agcda );};_ggfaa =_fcaf ;};};};if _bggcg {_f .Log .Info ("m\u0061\u006b\u0065\u0053tr\u006fk\u0065\u0052\u0075\u006c\u0069n\u0067\u0073\u003a\u0020\u0025\u0073",_abcd );};return _abcd ;};func _dbafe (_ggece ,_bffe ,_gdgf float64 )rulingKind {if _ggece >=_gdgf &&_aggdf (_bffe ,_ggece ){return _gfbdf ;
};if _bffe >=_gdgf &&_aggdf (_ggece ,_bffe ){return _degg ;};return _dcbd ;};func _dcee (_caea ,_aegb bounded )float64 {return _cccb (_caea )-_cccb (_aegb )};func (_cddg rulingList )merge ()*ruling {_cgefb :=_cddg [0]._gdaaf ;_fdgg :=_cddg [0]._bfbdb ;
_addc :=_cddg [0]._bedbg ;for _ ,_edgba :=range _cddg [1:]{_cgefb +=_edgba ._gdaaf ;if _edgba ._bfbdb < _fdgg {_fdgg =_edgba ._bfbdb ;};if _edgba ._bedbg > _addc {_addc =_edgba ._bedbg ;};};_cebdf :=&ruling {_befeb :_cddg [0]._befeb ,_ceab :_cddg [0]._ceab ,Color :_cddg [0].Color ,_gdaaf :_cgefb /float64 (len (_cddg )),_bfbdb :_fdgg ,_bedbg :_addc };
if _ffdf {_f .Log .Info ("\u006de\u0072g\u0065\u003a\u0020\u0025\u0032d\u0020\u0076e\u0063\u0073\u0020\u0025\u0073",len (_cddg ),_cebdf );for _fccgb ,_dgeba :=range _cddg {_ge .Printf ("\u0025\u0034\u0064\u003a\u0020\u0025\u0073\u000a",_fccgb ,_dgeba );
};};return _cebdf ;};

// String returns a string descibing `i`.
func (_baabd gridTile )String ()string {_cfcbf :=func (_ggfda bool ,_ggede string )string {if _ggfda {return _ggede ;};return "\u005f";};return _ge .Sprintf ("\u00256\u002e2\u0066\u0020\u0025\u0031\u0073%\u0031\u0073%\u0031\u0073\u0025\u0031\u0073",_baabd .PdfRectangle ,_cfcbf (_baabd ._egeg ,"\u004c"),_cfcbf (_baabd ._gbfec ,"\u0052"),_cfcbf (_baabd ._gfgcaf ,"\u0042"),_cfcbf (_baabd ._fbfd ,"\u0054"));
};func _gdbeg (_gdgbg float64 )float64 {return _fafg *_da .Round (_gdgbg /_fafg )};func (_ebbac *textTable )toTextTable ()TextTable {if _ababc {_f .Log .Info ("t\u006fT\u0065\u0078\u0074\u0054\u0061\u0062\u006c\u0065:\u0020\u0025\u0064\u0020x \u0025\u0064",_ebbac ._ebed ,_ebbac ._fcec );
};_gdag :=make ([][]TableCell ,_ebbac ._fcec );for _fefb :=0;_fefb < _ebbac ._fcec ;_fefb ++{_gdag [_fefb ]=make ([]TableCell ,_ebbac ._ebed );for _fbca :=0;_fbca < _ebbac ._ebed ;_fbca ++{_gabgc :=_ebbac .get (_fbca ,_fefb );if _gabgc ==nil {continue ;
};_efgeb (_gabgc ._geffa );if _ababc {_ge .Printf ("\u0025\u0034\u0064 \u0025\u0032\u0064\u003a\u0020\u0025\u0073\u000a",_fbca ,_fefb ,_gabgc );};_gdag [_fefb ][_fbca ].Text =_gabgc .text ();_adgf :=0;_gdag [_fefb ][_fbca ].Marks ._aecad =_gabgc .toTextMarks (&_adgf );
};};_eaafa :=TextTable {W :_ebbac ._ebed ,H :_ebbac ._fcec ,Cells :_gdag };_eaafa .PdfRectangle =_ebbac .bbox ();return _eaafa ;};func _dcdae (_bccfa ,_aaae int )int {if _bccfa < _aaae {return _bccfa ;};return _aaae ;};

// ImageExtractOptions contains options for controlling image extraction from
// PDF pages.
type ImageExtractOptions struct{IncludeInlineStencilMasks bool ;};var _gbgd =[]string {"\u2756","\u27a2","\u2713","\u2022","\uf0a7","\u25a1","\u2212","\u25a0","\u25aa","\u006f"};func _bebc (_bcdf *list ,_fbfe *string )string {_agac :=_ag .Split (_bcdf ._eeff ,"\u000a");
_eagee :=&_ag .Builder {};for _ ,_dgadc :=range _agac {if _dgadc !=""{_eagee .WriteString (*_fbfe );_eagee .WriteString (_dgadc );_eagee .WriteString ("\u000a");};};return _eagee .String ();};func _ebdfd (_dfgcd []*textMark ,_dcgf _cc .PdfRectangle )*textWord {_aeagd :=_dfgcd [0].PdfRectangle ;
_ggaa :=_dfgcd [0]._ggde ;for _ ,_cccba :=range _dfgcd [1:]{_aeagd =_cbba (_aeagd ,_cccba .PdfRectangle );if _cccba ._ggde > _ggaa {_ggaa =_cccba ._ggde ;};};return &textWord {PdfRectangle :_aeagd ,_bacg :_dfgcd ,_bfff :_dcgf .Ury -_aeagd .Lly ,_dcca :_ggaa };
};func (_ccfbe *textTable )putComposite (_gdfcc ,_dgbfe int ,_edae paraList ,_edab _cc .PdfRectangle ){if len (_edae )==0{_f .Log .Error ("\u0074\u0065xt\u0054\u0061\u0062l\u0065\u0029\u0020\u0070utC\u006fmp\u006f\u0073\u0069\u0074\u0065\u003a\u0020em\u0070\u0074\u0079\u0020\u0070\u0061\u0072a\u0073");
return ;};_bcdgge :=compositeCell {PdfRectangle :_edab ,paraList :_edae };if _ababc {_ge .Printf ("\u0020\u0020\u0020\u0020\u0020\u0020\u0020\u0020\u0070\u0075\u0074\u0043\u006f\u006d\u0070o\u0073i\u0074\u0065\u0028\u0025\u0064\u002c\u0025\u0064\u0029\u003c\u002d\u0025\u0073\u000a",_gdfcc ,_dgbfe ,_bcdgge .String ());
};_bcdgge .updateBBox ();_ccfbe ._dccb [_dfgde (_gdfcc ,_dgbfe )]=_bcdgge ;};func _geeea (_dacd func (*wordBag ,*textWord ,float64 )bool ,_gbea float64 )func (*wordBag ,*textWord )bool {return func (_ecead *wordBag ,_ggeaf *textWord )bool {return _dacd (_ecead ,_ggeaf ,_gbea )};
};func _ffcd (_cdde []*textLine ,_eggc map[float64 ][]*textLine ,_gfdaa []float64 ,_acdb int ,_dfad ,_gfdc float64 )[]*list {_gdba :=[]*list {};_fafe :=_acdb ;_acdb =_acdb +1;_aebe :=_gfdaa [_fafe ];_fafgg :=_eggc [_aebe ];_deag :=_adcbb (_fafgg ,_gfdc ,_dfad );
for _cefc ,_deacf :=range _deag {var _ebabf float64 ;_abcg :=[]*list {};_gecb :=_deacf ._gdca ;_caca :=_gfdc ;if _cefc < len (_deag )-1{_caca =_deag [_cefc +1]._gdca ;};if _acdb < len (_gfdaa ){_abcg =_ffcd (_cdde ,_eggc ,_gfdaa ,_acdb ,_gecb ,_caca );
};_ebabf =_caca ;if len (_abcg )> 0{_ecec :=_abcg [0];if len (_ecec ._faeg )> 0{_ebabf =_ecec ._faeg [0]._gdca ;};};_egce :=[]*textLine {_deacf };_cgbcaf :=_dgfaaf (_deacf ,_cdde ,_gfdaa ,_gecb ,_ebabf );_egce =append (_egce ,_cgbcaf ...);_ggfff :=_fdfcc (_egce ,"\u0062\u0075\u006c\u006c\u0065\u0074",_abcg );
_ggfff ._eeff =_dbbde (_egce ,"");_gdba =append (_gdba ,_ggfff );};return _gdba ;};func _cg (_fba []string ,_agc int ,_ecb int ,_aff string ){for _gb :=_agc ;_gb < _ecb ;_gb ++{_fba [_gb ]=_aff ;};};

// String returns a description of `p`.
func (_bgfd *textPara )String ()string {if _bgfd ._gaebg {return _ge .Sprintf ("\u0025\u0036\u002e\u0032\u0066\u0020\u005b\u0045\u004d\u0050\u0054\u0059\u005d",_bgfd .PdfRectangle );};_gedc :="";if _bgfd ._dcde !=nil {_gedc =_ge .Sprintf ("\u005b\u0025\u0064\u0078\u0025\u0064\u005d\u0020",_bgfd ._dcde ._ebed ,_bgfd ._dcde ._fcec );
};return _ge .Sprintf ("\u0025\u0036\u002e\u0032f \u0025\u0073\u0025\u0064\u0020\u006c\u0069\u006e\u0065\u0073\u0020\u0025\u0071",_bgfd .PdfRectangle ,_gedc ,len (_bgfd ._geffa ),_bcfdgc (_bgfd .text (),50));};func (_ddaa *structTreeRoot )parseStructTreeRoot (_ebcb _ea .PdfObject ){if _ebcb !=nil {_dgfaa ,_cfag :=_ea .GetDict (_ebcb );
if !_cfag {_f .Log .Debug ("\u0070\u0061\u0072s\u0065\u0053\u0074\u0072\u0075\u0063\u0074\u0054\u0072\u0065\u0065\u0052\u006f\u006f\u0074\u003a\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u0020\u006eo\u0074\u0020\u0066\u006f\u0075\u006e\u0064\u002e");
};K :=_dgfaa .Get ("\u004b");_ddb :=_dgfaa .Get ("\u0054\u0079\u0070\u0065").String ();var _bccb *_ea .PdfObjectArray ;switch _bebf :=K .(type ){case *_ea .PdfObjectArray :_bccb =_bebf ;case *_ea .PdfObjectReference :_bccb =_ea .MakeArray (K );};_dbbg :=[]structElement {};
for _ ,_befa :=range _bccb .Elements (){_dfgc :=&structElement {};_dfgc .parseStructElement (_befa );_dbbg =append (_dbbg ,*_dfgc );};_ddaa ._ffedd =_dbbg ;_ddaa ._cafcf =_ddb ;};};func (_cbgf *subpath )last ()_ce .Point {return _cbgf ._cggf [len (_cbgf ._cggf )-1]};


// String returns a description of `b`.
func (_ageg *wordBag )String ()string {var _ggfg []string ;for _ ,_gabb :=range _ageg .depthIndexes (){_eeac :=_ageg ._fggf [_gabb ];for _ ,_fdfc :=range _eeac {_ggfg =append (_ggfg ,_fdfc ._ggeag );};};return _ge .Sprintf ("\u0025.\u0032\u0066\u0020\u0066\u006f\u006e\u0074\u0073\u0069\u007a\u0065=\u0025\u002e\u0032\u0066\u0020\u0025\u0064\u0020\u0025\u0071",_ageg .PdfRectangle ,_ageg ._aafec ,len (_ggfg ),_ggfg );
};func (_gaec *textObject )moveText (_aedb ,_fgde float64 ){_gaec .moveLP (_aedb ,_fgde )};func (_dgaa paraList )log (_fdfd string ){if !_fgfdc {return ;};_f .Log .Info ("%\u0038\u0073\u003a\u0020\u0025\u0064 \u0070\u0061\u0072\u0061\u0073\u0020=\u003d\u003d\u003d\u003d\u003d\u003d\u002d-\u002d\u002d\u002d\u002d\u002d\u003d\u003d\u003d\u003d\u003d=\u003d",_fdfd ,len (_dgaa ));
for _fdcfa ,_aadbfb :=range _dgaa {if _aadbfb ==nil {continue ;};_eagf :=_aadbfb .text ();_acgg :="\u0020\u0020";if _aadbfb ._dcde !=nil {_acgg =_ge .Sprintf ("\u005b%\u0064\u0078\u0025\u0064\u005d",_aadbfb ._dcde ._ebed ,_aadbfb ._dcde ._fcec );};_ge .Printf ("\u0025\u0034\u0064\u003a\u0020\u0025\u0036\u002e\u0032\u0066\u0020\u0025s\u0020\u0025\u0071\u000a",_fdcfa ,_aadbfb .PdfRectangle ,_acgg ,_bcfdgc (_eagf ,50));
};};func _bbade (_dfdd ,_badgb bounded )float64 {return _dfdd .bbox ().Llx -_badgb .bbox ().Llx };type structTreeRoot struct{_ffedd []structElement ;_cafcf string ;};func (_gafgdc *textTable )depth ()float64 {_egdcg :=1e10;for _dcac :=0;_dcac < _gafgdc ._ebed ;
_dcac ++{_dgeaf :=_gafgdc .get (_dcac ,0);if _dgeaf ==nil ||_dgeaf ._gaebg {continue ;};_egdcg =_da .Min (_egdcg ,_dgeaf .depth ());};return _egdcg ;};func (_fabg *shapesState )devicePoint (_cfaf ,_abgc float64 )_ce .Point {_eaeb :=_fabg ._fbcb .Mult (_fabg ._badf );
_cfaf ,_abgc =_eaeb .Transform (_cfaf ,_abgc );return _ce .NewPoint (_cfaf ,_abgc );};func _abeee (_cgfe []float64 ,_dgef ,_aggg float64 )[]float64 {_cgdad ,_ffdcg :=_dgef ,_aggg ;if _ffdcg < _cgdad {_cgdad ,_ffdcg =_ffdcg ,_cgdad ;};_fgefe :=make ([]float64 ,0,len (_cgfe )+2);
_fgefe =append (_fgefe ,_dgef );for _ ,_fcfea :=range _cgfe {if _fcfea <=_cgdad {continue ;}else if _fcfea >=_ffdcg {break ;};_fgefe =append (_fgefe ,_fcfea );};_fgefe =append (_fgefe ,_aggg );return _fgefe ;};type rulingKind int ;func _gaaa (_aece string ,_eadag []rulingList ){_f .Log .Info ("\u0024\u0024 \u0025\u0064\u0020g\u0072\u0069\u0064\u0073\u0020\u002d\u0020\u0025\u0073",len (_eadag ),_aece );
for _gagabe ,_eadca :=range _eadag {_ge .Printf ("\u0025\u0034\u0064\u003a\u0020\u0025\u0073\u000a",_gagabe ,_eadca .String ());};};func (_ecbd rulingList )primaries ()[]float64 {_eeacb :=make (map[float64 ]struct{},len (_ecbd ));for _ ,_fbfccb :=range _ecbd {_eeacb [_fbfccb ._gdaaf ]=struct{}{};
};_eggg :=make ([]float64 ,len (_eeacb ));_dedgd :=0;for _aegd :=range _eeacb {_eggg [_dedgd ]=_aegd ;_dedgd ++;};_be .Float64s (_eggg );return _eggg ;};func _daaga (_dfbcb []_ea .PdfObject )(_eaed ,_agfbc float64 ,_aadca error ){if len (_dfbcb )!=2{return 0,0,_ge .Errorf ("\u0069\u006e\u0076\u0061l\u0069\u0064\u0020\u006e\u0075\u006d\u0062\u0065\u0072\u0020o\u0066 \u0070\u0061\u0072\u0061\u006d\u0073\u003a \u0025\u0064",len (_dfbcb ));
};_abbb ,_aadca :=_ea .GetNumbersAsFloat (_dfbcb );if _aadca !=nil {return 0,0,_aadca ;};return _abbb [0],_abbb [1],nil ;};type compositeCell struct{_cc .PdfRectangle ;paraList ;};

// String returns a string describing `pt`.
func (_ggbab PageText )String ()string {_dcec :=_ge .Sprintf ("P\u0061\u0067\u0065\u0054ex\u0074:\u0020\u0025\u0064\u0020\u0065l\u0065\u006d\u0065\u006e\u0074\u0073",len (_ggbab ._fagg ));_eba :=[]string {"\u002d"+_dcec };for _ ,_bacf :=range _ggbab ._fagg {_eba =append (_eba ,_bacf .String ());
};_eba =append (_eba ,"\u002b"+_dcec );return _ag .Join (_eba ,"\u000a");};func (_baab *textObject )renderText (_ccdcg _ea .PdfObject ,_facc []byte ,_dce int ,_dba string )error {if _baab ._aafd {_f .Log .Debug ("\u0072\u0065\u006e\u0064\u0065r\u0054\u0065\u0078\u0074\u003a\u0020\u0049\u006e\u0076\u0061\u006c\u0069\u0064 \u0066\u006f\u006e\u0074\u002e\u0020\u004e\u006f\u0074\u0020\u0070\u0072\u006f\u0063\u0065\u0073\u0073\u0069\u006e\u0067\u002e");
return nil ;};_ceca :=_baab .getCurrentFont ();_adee :=_ceca .BytesToCharcodes (_facc );_geddc ,_bfcb ,_bdge :=_ceca .CharcodesToStrings (_adee ,_dba );if _bdge > 0{_f .Log .Debug ("\u0072\u0065nd\u0065\u0072\u0054e\u0078\u0074\u003a\u0020num\u0043ha\u0072\u0073\u003d\u0025\u0064\u0020\u006eum\u004d\u0069\u0073\u0073\u0065\u0073\u003d%\u0064",_bfcb ,_bdge );
};_baab ._fea ._fgcd +=_bfcb ;_baab ._fea ._baad +=_bdge ;_abe :=_baab ._fea ;_baed :=_abe ._fcba ;_cagb :=_abe ._bbgg /100.0;_egf :=_cga ;if _ceca .Subtype ()=="\u0054\u0079\u0070e\u0033"{_egf =1;};_gcad ,_aae :=_ceca .GetRuneMetrics (' ');if !_aae {_gcad ,_aae =_ceca .GetCharMetrics (32);
};if !_aae {_gcad ,_ =_cc .DefaultFont ().GetRuneMetrics (' ');};_ede :=_gcad .Wx *_egf ;_f .Log .Trace ("\u0073p\u0061\u0063e\u0057\u0069\u0064t\u0068\u003d\u0025\u002e\u0032\u0066\u0020t\u0065\u0078\u0074\u003d\u0025\u0071 \u0066\u006f\u006e\u0074\u003d\u0025\u0073\u0020\u0066\u006f\u006et\u0053\u0069\u007a\u0065\u003d\u0025\u002e\u0032\u0066",_ede ,_geddc ,_ceca ,_baed );
_gda :=_ce .NewMatrix (_baed *_cagb ,0,0,_baed ,0,_abe ._daca );if _bcdg {_f .Log .Info ("\u0072\u0065\u006e\u0064\u0065\u0072T\u0065\u0078\u0074\u003a\u0020\u0025\u0064\u0020\u0063\u006f\u0064\u0065\u0073=\u0025\u002b\u0076\u0020\u0074\u0065\u0078t\u0073\u003d\u0025\u0071",len (_adee ),_adee ,_geddc );
};_f .Log .Trace ("\u0072\u0065\u006e\u0064\u0065\u0072T\u0065\u0078\u0074\u003a\u0020\u0025\u0064\u0020\u0063\u006f\u0064\u0065\u0073=\u0025\u002b\u0076\u0020\u0072\u0075\u006ee\u0073\u003d\u0025\u0071",len (_adee ),_adee ,len (_geddc ));_caff :=_baab .getFillColor ();
_ebg :=_baab .getStrokeColor ();for _bbbd ,_ffb :=range _geddc {_dffa :=[]rune (_ffb );if len (_dffa )==1&&_dffa [0]=='\x00'{continue ;};_fada :=_adee [_bbbd ];_efcf :=_baab ._fbgf .CTM .Mult (_baab ._gfdfa ).Mult (_gda );_fggc :=0.0;if len (_dffa )==1&&_dffa [0]==32{_fggc =_abe ._eeef ;
};_deee ,_bffg :=_ceca .GetCharMetrics (_fada );if !_bffg {_f .Log .Debug ("\u0045R\u0052\u004fR\u003a\u0020\u004e\u006f \u006d\u0065\u0074r\u0069\u0063\u0020\u0066\u006f\u0072\u0020\u0063\u006fde\u003d\u0025\u0064 \u0072\u003d0\u0078\u0025\u0030\u0034\u0078\u003d%\u002b\u0071 \u0025\u0073",_fada ,_dffa ,_dffa ,_ceca );
return _ge .Errorf ("\u006e\u006f\u0020\u0063\u0068\u0061\u0072\u0020\u006d\u0065\u0074\u0072\u0069\u0063\u0073:\u0020f\u006f\u006e\u0074\u003d\u0025\u0073\u0020\u0063\u006f\u0064\u0065\u003d\u0025\u0064",_ceca .String (),_fada );};_fege :=_ce .Point {X :_deee .Wx *_egf ,Y :_deee .Wy *_egf };
_gabc :=_ce .Point {X :(_fege .X *_baed +_fggc )*_cagb };_effac :=_ce .Point {X :(_fege .X *_baed +_abe ._eegf +_fggc )*_cagb };if _bcdg {_f .Log .Info ("\u0074\u0066\u0073\u003d\u0025\u002e\u0032\u0066\u0020\u0074\u0063\u003d\u0025\u002e\u0032f\u0020t\u0077\u003d\u0025\u002e\u0032\u0066\u0020\u0074\u0068\u003d\u0025\u002e\u0032\u0066",_baed ,_abe ._eegf ,_abe ._eeef ,_cagb );
_f .Log .Info ("\u0064x\u002c\u0064\u0079\u003d%\u002e\u0033\u0066\u0020\u00740\u003d%\u002e3\u0066\u0020\u0074\u003d\u0025\u002e\u0033f",_fege ,_gabc ,_effac );};_bag :=_aegc (_gabc );_fcae :=_aegc (_effac );_fed :=_baab ._fbgf .CTM .Mult (_baab ._gfdfa ).Mult (_bag );
if _gcdaf {_f .Log .Info ("e\u006e\u0064\u003a\u000a\tC\u0054M\u003d\u0025\u0073\u000a\u0009 \u0074\u006d\u003d\u0025\u0073\u000a"+"\u0009\u0020t\u0064\u003d\u0025s\u0020\u0078\u006c\u0061\u0074\u003d\u0025\u0073\u000a"+"\u0009t\u0064\u0030\u003d\u0025s\u000a\u0009\u0020\u0020\u2192 \u0025s\u0020x\u006c\u0061\u0074\u003d\u0025\u0073",_baab ._fbgf .CTM ,_baab ._gfdfa ,_fcae ,_ffac (_baab ._fbgf .CTM .Mult (_baab ._gfdfa ).Mult (_fcae )),_bag ,_fed ,_ffac (_fed ));
};_dafb ,_aadb :=_baab .newTextMark (_eee .ExpandLigatures (_dffa ),_efcf ,_ffac (_fed ),_da .Abs (_ede *_efcf .ScalingFactorX ()),_ceca ,_baab ._fea ._eegf ,_caff ,_ebg ,_ccdcg ,_geddc ,_bbbd ,_dce );if !_aadb {_f .Log .Debug ("\u0054\u0065\u0078\u0074\u0020\u006d\u0061\u0072\u006b\u0020\u006f\u0075\u0074\u0073\u0069d\u0065 \u0070\u0061\u0067\u0065\u002e\u0020\u0053\u006b\u0069\u0070\u0070\u0069\u006e\u0067");
continue ;};if _ceca ==nil {_f .Log .Debug ("\u0045R\u0052O\u0052\u003a\u0020\u004e\u006f\u0020\u0066\u006f\u006e\u0074\u002e");}else if _ceca .Encoder ()==nil {_f .Log .Debug ("E\u0052\u0052\u004f\u0052\u003a\u0020N\u006f\u0020\u0065\u006e\u0063\u006f\u0064\u0069\u006eg\u002e\u0020\u0066o\u006et\u003d\u0025\u0073",_ceca );
}else {if _aggd ,_ddeea :=_ceca .Encoder ().CharcodeToRune (_fada );_ddeea {_dafb ._bdea =string (_aggd );};};_f .Log .Trace ("i\u003d\u0025\u0064\u0020\u0063\u006fd\u0065\u003d\u0025\u0064\u0020\u006d\u0061\u0072\u006b=\u0025\u0073\u0020t\u0072m\u003d\u0025\u0073",_bbbd ,_fada ,_dafb ,_efcf );
_baab ._dbge =append (_baab ._dbge ,&_dafb );_baab ._gfdfa .Concat (_fcae );};return nil ;};func (_addd rectRuling )asRuling ()(*ruling ,bool ){_gbfa :=ruling {_befeb :_addd ._bbcga ,Color :_addd .Color ,_ceab :_agbd };switch _addd ._bbcga {case _degg :_gbfa ._gdaaf =0.5*(_addd .Llx +_addd .Urx );
_gbfa ._bfbdb =_addd .Lly ;_gbfa ._bedbg =_addd .Ury ;_fecbd ,_gdgc :=_addd .checkWidth (_addd .Llx ,_addd .Urx );if !_gdgc {if _effbc {_f .Log .Error ("\u0072\u0065\u0063\u0074\u0052\u0075l\u0069\u006e\u0067\u002e\u0061\u0073\u0052\u0075\u006c\u0069\u006e\u0067\u003a\u0020\u0072\u0075\u006c\u0069\u006e\u0067V\u0065\u0072\u0074\u0020\u0021\u0063\u0068\u0065\u0063\u006b\u0057\u0069\u0064\u0074h\u0020v\u003d\u0025\u002b\u0076",_addd );
};return nil ,false ;};_gbfa ._bfdc =_fecbd ;case _gfbdf :_gbfa ._gdaaf =0.5*(_addd .Lly +_addd .Ury );_gbfa ._bfbdb =_addd .Llx ;_gbfa ._bedbg =_addd .Urx ;_bbec ,_bdga :=_addd .checkWidth (_addd .Lly ,_addd .Ury );if !_bdga {if _effbc {_f .Log .Error ("\u0072\u0065\u0063\u0074\u0052\u0075l\u0069\u006e\u0067\u002e\u0061\u0073\u0052\u0075\u006c\u0069\u006e\u0067\u003a\u0020\u0072\u0075\u006c\u0069\u006e\u0067H\u006f\u0072\u007a\u0020\u0021\u0063\u0068\u0065\u0063\u006b\u0057\u0069\u0064\u0074h\u0020v\u003d\u0025\u002b\u0076",_addd );
};return nil ,false ;};_gbfa ._bfdc =_bbec ;default:_f .Log .Error ("\u0062\u0061\u0064\u0020pr\u0069\u006d\u0061\u0072\u0079\u0020\u006b\u0069\u006e\u0064\u003d\u0025\u0064",_addd ._bbcga );return nil ,false ;};return &_gbfa ,true ;};func (_fefcf rulingList )comp (_bcdgg ,_cfccc int )bool {_eabc ,_egaeg :=_fefcf [_bcdgg ],_fefcf [_cfccc ];
_cegba ,_edcg :=_eabc ._befeb ,_egaeg ._befeb ;if _cegba !=_edcg {return _cegba > _edcg ;};if _cegba ==_dcbd {return false ;};_fcdb :=func (_cgcbb bool )bool {if _cegba ==_gfbdf {return _cgcbb ;};return !_cgcbb ;};_fdbb ,_gcdde :=_eabc ._gdaaf ,_egaeg ._gdaaf ;
if _fdbb !=_gcdde {return _fcdb (_fdbb > _gcdde );};_fdbb ,_gcdde =_eabc ._bfbdb ,_egaeg ._bfbdb ;if _fdbb !=_gcdde {return _fcdb (_fdbb < _gcdde );};return _fcdb (_eabc ._bedbg < _egaeg ._bedbg );};

// String returns a description of `tm`.
func (_dgea *textMark )String ()string {return _ge .Sprintf ("\u0025\u002e\u0032f \u0066\u006f\u006e\u0074\u0073\u0069\u007a\u0065\u003d\u0025\u002e\u0032\u0066\u0020\u0022\u0025\u0073\u0022",_dgea .PdfRectangle ,_dgea ._ggde ,_dgea ._fecec );};func (_gdad *wordBag )sort (){for _ ,_bdca :=range _gdad ._fggf {_be .Slice (_bdca ,func (_gbgf ,_dead int )bool {return _bbade (_bdca [_gbgf ],_bdca [_dead ])< 0});
};};func (_cgefa rulingList )connections (_gbcb map[int ]intSet ,_dbbef int )intSet {_eeada :=make (intSet );_ebbae :=make (intSet );var _cgec func (int );_cgec =func (_dfdge int ){if !_ebbae .has (_dfdge ){_ebbae .add (_dfdge );for _daae :=range _cgefa {if _gbcb [_daae ].has (_dfdge ){_eeada .add (_daae );
};};for _aabg :=range _cgefa {if _eeada .has (_aabg ){_cgec (_aabg );};};};};_cgec (_dbbef );return _eeada ;};func _fgb (_cccd *TextMarkArray )[]*TextMarkArray {_feb :=_cccd .Elements ();_cdc :=len (_feb );var _ggf _ea .PdfObject ;_ace :=[]*TextMarkArray {};
_gdb :=&TextMarkArray {};_bfag :=-1;for _gcd ,_gfb :=range _feb {_dfaa :=_gfb .DirectObject ;_bfag =_gfb .Index ;if _dfaa ==nil {_gbce :=_ceea (_cccd ,_gcd ,_bfag );if _ggf !=nil {if _gbce ==-1||_gbce > _gcd {_ace =append (_ace ,_gdb );_gdb =&TextMarkArray {};
};};}else if _dfaa !=nil &&_ggf ==nil {if _bfag ==0&&_gcd > 0{_ace =append (_ace ,_gdb );_gdb =&TextMarkArray {};};}else if _dfaa !=nil &&_ggf !=nil {if _dfaa !=_ggf {_ace =append (_ace ,_gdb );_gdb =&TextMarkArray {};};};_ggf =_dfaa ;_gdb .Append (_gfb );
if _gcd ==(_cdc -1){_ace =append (_ace ,_gdb );};};return _ace ;};

// String returns a description of `t`.
func (_fdfdc *textTable )String ()string {return _ge .Sprintf ("\u0025\u0064\u0020\u0078\u0020\u0025\u0064\u0020\u0025\u0074",_fdfdc ._ebed ,_fdfdc ._fcec ,_fdfdc ._fggcd );};func _fgge (_cabfb *_cc .Image ,_efgc _ca .Color )_ee .Image {_geea ,_ddfc :=int (_cabfb .Width ),int (_cabfb .Height );
_gdab :=_ee .NewRGBA (_ee .Rect (0,0,_geea ,_ddfc ));for _gdecd :=0;_gdecd < _ddfc ;_gdecd ++{for _cgfbgd :=0;_cgfbgd < _geea ;_cgfbgd ++{_bfadc ,_bgage :=_cabfb .ColorAt (_cgfbgd ,_gdecd );if _bgage !=nil {_f .Log .Debug ("\u0057\u0041\u0052\u004e\u003a\u0020\u0063o\u0075\u006c\u0064\u0020\u006e\u006f\u0074\u0020\u0072\u0065\u0074\u0072\u0069\u0065v\u0065 \u0069\u006d\u0061\u0067\u0065\u0020m\u0061\u0073\u006b\u0020\u0076\u0061\u006cu\u0065\u0020\u0061\u0074\u0020\u0028\u0025\u0064\u002c\u0020\u0025\u0064\u0029\u002e\u0020\u004f\u0075\u0074\u0070\u0075\u0074\u0020\u006da\u0079\u0020\u0062\u0065\u0020\u0069\u006e\u0063\u006f\u0072\u0072\u0065\u0063t\u002e",_cgfbgd ,_gdecd );
continue ;};_eacg ,_feage ,_bcdac ,_ :=_bfadc .RGBA ();var _ffgge _ca .Color ;if _eacg +_feage +_bcdac ==0{_ffgge =_ca .Transparent ;}else {_ffgge =_efgc ;};_gdab .Set (_cgfbgd ,_gdecd ,_ffgge );};};return _gdab ;};const (_dcbd rulingKind =iota ;_gfbdf ;
_degg ;);type stateStack []*textState ;func _acfg (_fgdf ,_cddc bounded )float64 {_adbc :=_dcee (_fgdf ,_cddc );if !_bfee (_adbc ){return _adbc ;};return _bbade (_fgdf ,_cddc );};func (_dcacb *textTable )subdivide ()*textTable {_dcacb .logComposite ("\u0073u\u0062\u0064\u0069\u0076\u0069\u0064e");
_eggdf :=_dcacb .compositeRowCorridors ();_ffdcb :=_dcacb .compositeColCorridors ();if _ababc {_f .Log .Info ("\u0073u\u0062\u0064i\u0076\u0069\u0064\u0065:\u000a\u0009\u0072o\u0077\u0043\u006f\u0072\u0072\u0069\u0064\u006f\u0072s=\u0025\u0073\u000a\t\u0063\u006fl\u0043\u006f\u0072\u0072\u0069\u0064o\u0072\u0073=\u0025\u0073",_cfdab (_eggdf ),_cfdab (_ffdcb ));
};if len (_eggdf )==0||len (_ffdcb )==0{return _dcacb ;};_feedf (_eggdf );_feedf (_ffdcb );if _ababc {_f .Log .Info ("\u0073\u0075\u0062\u0064\u0069\u0076\u0069\u0064\u0065\u0020\u0066\u0069\u0078\u0065\u0064\u003a\u000a\u0009r\u006f\u0077\u0043\u006f\u0072\u0072\u0069d\u006f\u0072\u0073\u003d\u0025\u0073\u000a\u0009\u0063\u006f\u006cC\u006f\u0072\u0072\u0069\u0064\u006f\u0072\u0073\u003d\u0025\u0073",_cfdab (_eggdf ),_cfdab (_ffdcb ));
};_gfeae ,_gfbe :=_dcdda (_dcacb ._fcec ,_eggdf );_cggdc ,_dcbeff :=_dcdda (_dcacb ._ebed ,_ffdcb );_acacb :=make (map[uint64 ]*textPara ,_dcbeff *_gfbe );_cagff :=&textTable {PdfRectangle :_dcacb .PdfRectangle ,_fggcd :_dcacb ._fggcd ,_fcec :_gfbe ,_ebed :_dcbeff ,_dcdb :_acacb };
if _ababc {_f .Log .Info ("\u0073\u0075b\u0064\u0069\u0076\u0069\u0064\u0065\u003a\u0020\u0063\u006f\u006d\u0070\u006f\u0073\u0069\u0074\u0065\u0020\u003d\u0020\u0025\u0064\u0020\u0078\u0020\u0025\u0064\u0020\u0063\u0065\u006c\u006c\u0073\u003d\u0020\u0025\u0064\u0020\u0078\u0020\u0025\u0064\u000a"+"\u0009\u0072\u006f\u0077\u0043\u006f\u0072\u0072\u0069\u0064\u006f\u0072s\u003d\u0025\u0073\u000a"+"\u0009\u0063\u006f\u006c\u0043\u006f\u0072\u0072\u0069\u0064\u006f\u0072s\u003d\u0025\u0073\u000a"+"\u0009\u0079\u004f\u0066\u0066\u0073\u0065\u0074\u0073=\u0025\u002b\u0076\u000a"+"\u0009\u0078\u004f\u0066\u0066\u0073\u0065\u0074\u0073\u003d\u0025\u002b\u0076",_dcacb ._ebed ,_dcacb ._fcec ,_dcbeff ,_gfbe ,_cfdab (_eggdf ),_cfdab (_ffdcb ),_gfeae ,_cggdc );
};for _cdgfd :=0;_cdgfd < _dcacb ._fcec ;_cdgfd ++{_accae :=_gfeae [_cdgfd ];for _eddag :=0;_eddag < _dcacb ._ebed ;_eddag ++{_fgbg :=_cggdc [_eddag ];if _ababc {_ge .Printf ("\u0025\u0036\u0064\u002c %\u0032\u0064\u003a\u0020\u0078\u0030\u003d\u0025\u0064\u0020\u0079\u0030\u003d\u0025d\u000a",_eddag ,_cdgfd ,_fgbg ,_accae );
};_dbgbca ,_dbeeb :=_dcacb ._dccb [_dfgde (_eddag ,_cdgfd )];if !_dbeeb {continue ;};_aecbf :=_dbgbca .split (_eggdf [_cdgfd ],_ffdcb [_eddag ]);for _adgaf :=0;_adgaf < _aecbf ._fcec ;_adgaf ++{for _cbfd :=0;_cbfd < _aecbf ._ebed ;_cbfd ++{_febg :=_aecbf .get (_cbfd ,_adgaf );
_cagff .put (_fgbg +_cbfd ,_accae +_adgaf ,_febg );if _ababc {_ge .Printf ("\u0025\u0038\u0064\u002c\u0020\u0025\u0032\u0064\u003a\u0020\u0025\u0073\u000a",_fgbg +_cbfd ,_accae +_adgaf ,_febg );};};};};};return _cagff ;};

// String returns a string describing `ma`.
func (_dfdg TextMarkArray )String ()string {_ddea :=len (_dfdg ._aecad );if _ddea ==0{return "\u0045\u004d\u0050T\u0059";};_bdbc :=_dfdg ._aecad [0];_gbec :=_dfdg ._aecad [_ddea -1];return _ge .Sprintf ("\u007b\u0054\u0045\u0058\u0054\u004d\u0041\u0052K\u0041\u0052\u0052AY\u003a\u0020\u0025\u0064\u0020\u0065l\u0065\u006d\u0065\u006e\u0074\u0073\u000a\u0009\u0066\u0069\u0072\u0073\u0074\u003d\u0025s\u000a\u0009\u0020\u006c\u0061\u0073\u0074\u003d%\u0073\u007d",_ddea ,_bdbc ,_gbec );
};func (_efbfb paraList )eventNeighbours (_cffc []event )map[*textPara ][]int {_be .Slice (_cffc ,func (_dafef ,_fgae int )bool {_afaf ,_afgde :=_cffc [_dafef ],_cffc [_fgae ];_fgaff ,_aeeabe :=_afaf ._fbaddc ,_afgde ._fbaddc ;if _fgaff !=_aeeabe {return _fgaff < _aeeabe ;
};if _afaf ._ecfdg !=_afgde ._ecfdg {return _afaf ._ecfdg ;};return _dafef < _fgae ;});_gfec :=make (map[int ]intSet );_ecee :=make (intSet );for _ ,_fdaga :=range _cffc {if _fdaga ._ecfdg {_gfec [_fdaga ._efdb ]=make (intSet );for _gabff :=range _ecee {if _gabff !=_fdaga ._efdb {_gfec [_fdaga ._efdb ].add (_gabff );
_gfec [_gabff ].add (_fdaga ._efdb );};};_ecee .add (_fdaga ._efdb );}else {_ecee .del (_fdaga ._efdb );};};_adfdd :=map[*textPara ][]int {};for _efag ,_bcceb :=range _gfec {_eedde :=_efbfb [_efag ];if len (_bcceb )==0{_adfdd [_eedde ]=nil ;continue ;};
_ecdbe :=make ([]int ,len (_bcceb ));_feeccg :=0;for _gaacf :=range _bcceb {_ecdbe [_feeccg ]=_gaacf ;_feeccg ++;};_adfdd [_eedde ]=_ecdbe ;};return _adfdd ;};func (_bdcd rulingList )removeDuplicates ()rulingList {if len (_bdcd )==0{return nil ;};_bdcd .sort ();
_cefba :=rulingList {_bdcd [0]};for _ ,_aefe :=range _bdcd [1:]{if _aefe .equals (_cefba [len (_cefba )-1]){continue ;};_cefba =append (_cefba ,_aefe );};return _cefba ;};func _cfdab (_cddb map[int ][]float64 )string {_fbcef :=_gfff (_cddb );_gecbb :=make ([]string ,len (_cddb ));
for _dbba ,_defge :=range _fbcef {_gecbb [_dbba ]=_ge .Sprintf ("\u0025\u0064\u003a\u0020\u0025\u002e\u0032\u0066",_defge ,_cddb [_defge ]);};return _ge .Sprintf ("\u007b\u0025\u0073\u007d",_ag .Join (_gecbb ,"\u002c\u0020"));};func (_affd paraList )lines ()[]*textLine {var _facd []*textLine ;
for _ ,_acabe :=range _affd {_facd =append (_facd ,_acabe ._geffa ...);};return _facd ;};func (_edda *structElement )parseStructElement (_cgee _ea .PdfObject ){_aagcc ,_gbb :=_ea .GetDict (_cgee );if !_gbb {_f .Log .Debug ("\u0070\u0061\u0072\u0073\u0065\u0053\u0074\u0072u\u0063\u0074\u0045le\u006d\u0065\u006e\u0074\u003a\u0020d\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u0020\u006f\u0062\u006a\u0065\u0063t\u0020\u006e\u006f\u0074\u0020\u0066\u006f\u0075n\u0064\u002e");
return ;};_edbeb :=_aagcc .Get ("\u0053");_bffb :=_aagcc .Get ("\u0050\u0067");_ecffg :="";if _edbeb !=nil {_ecffg =_edbeb .String ();};_gfce :=_aagcc .Get ("\u004b");_edda ._eeagb =_ecffg ;_edda ._abefd =_bffb ;switch _dacdc :=_gfce .(type ){case *_ea .PdfObjectInteger :_edda ._eeagb =_ecffg ;
_edda ._daaaa =int64 (*_dacdc );_edda ._abefd =_bffb ;case *_ea .PdfObjectReference :_faggc :=*_ea .MakeArray (_dacdc );var _efeb int64 =-1;_edda ._daaaa =_efeb ;if _faggc .Len ()==1{_agab :=_faggc .Elements ()[0];_gagab ,_eedc :=_agab .(*_ea .PdfObjectInteger );
if _eedc {_efeb =int64 (*_gagab );_edda ._daaaa =_efeb ;_edda ._eeagb =_ecffg ;_edda ._abefd =_bffb ;return ;};};_ceff :=[]structElement {};for _ ,_daaae :=range _faggc .Elements (){_egec ,_bcef :=_daaae .(*_ea .PdfObjectInteger );if _bcef {_efeb =int64 (*_egec );
_edda ._daaaa =_efeb ;_edda ._eeagb =_ecffg ;}else {_ebeeb :=&structElement {};_ebeeb .parseStructElement (_daaae );_ceff =append (_ceff ,*_ebeeb );};_efeb =-1;};_edda ._gedge =_ceff ;case *_ea .PdfObjectArray :_bggg :=_gfce .(*_ea .PdfObjectArray );var _fccad int64 =-1;
_edda ._daaaa =_fccad ;if _bggg .Len ()==1{_bbbe :=_bggg .Elements ()[0];_aadc ,_agabf :=_bbbe .(*_ea .PdfObjectInteger );if _agabf {_fccad =int64 (*_aadc );_edda ._daaaa =_fccad ;_edda ._eeagb =_ecffg ;_edda ._abefd =_bffb ;return ;};};_egcg :=[]structElement {};
for _ ,_geef :=range _bggg .Elements (){_dbda ,_cfeb :=_geef .(*_ea .PdfObjectInteger );if _cfeb {_fccad =int64 (*_dbda );_edda ._daaaa =_fccad ;_edda ._eeagb =_ecffg ;_edda ._abefd =_bffb ;}else {_dcfgf :=&structElement {};_dcfgf .parseStructElement (_geef );
_egcg =append (_egcg ,*_dcfgf );};_fccad =-1;};_edda ._gedge =_egcg ;};};func (_aefg *textObject )setWordSpacing (_caaef float64 ){if _aefg ==nil {return ;};_aefg ._fea ._eeef =_caaef ;};func _cbba (_ffed ,_efe _cc .PdfRectangle )_cc .PdfRectangle {return _cc .PdfRectangle {Llx :_da .Min (_ffed .Llx ,_efe .Llx ),Lly :_da .Min (_ffed .Lly ,_efe .Lly ),Urx :_da .Max (_ffed .Urx ,_efe .Urx ),Ury :_da .Max (_ffed .Ury ,_efe .Ury )};
};func _cffdg (_afedg string )bool {for _ ,_fcgg :=range _afedg {if !_e .IsSpace (_fcgg ){return false ;};};return true ;};

// Search searches the pages specified by `pages`.
func (_acdc *Editor )Search (pattern string ,pages []int )(map[int ]Match ,error ){_bged ,_ ,_eff :=_acdc .getMatches (pattern ,pages );return _bged ,_eff ;};func (_bbge rulingList )aligned ()bool {if len (_bbge )< 2{return false ;};_dcbdf :=make (map[*ruling ]int );
_dcbdf [_bbge [0]]=0;for _ ,_deagc :=range _bbge [1:]{_gabfd :=false ;for _fedb :=range _dcbdf {if _deagc .gridIntersecting (_fedb ){_dcbdf [_fedb ]++;_gabfd =true ;break ;};};if !_gabfd {_dcbdf [_deagc ]=0;};};_abgce :=0;for _ ,_gdcb :=range _dcbdf {if _gdcb ==0{_abgce ++;
};};_bfeb :=float64 (_abgce )/float64 (len (_bbge ));_babe :=_bfeb <=1.0-_edeba ;if _bggcg {_f .Log .Info ("\u0061\u006c\u0069\u0067\u006e\u0065\u0064\u003d\u0025\u0074\u0020\u0075\u006em\u0061\u0074\u0063\u0068\u0065\u0064=\u0025\u002e\u0032\u0066\u003d\u0025\u0064\u002f\u0025\u0064\u0020\u0076\u0065c\u0073\u003d\u0025\u0073",_babe ,_bfeb ,_abgce ,len (_bbge ),_bbge .String ());
};return _babe ;};

// Elements returns the TextMarks in `ma`.
func (_cfe *TextMarkArray )Elements ()[]TextMark {return _cfe ._aecad };func _fffb (_bgcc []TextMark ,_gbgfg *int ,_acgbc TextMark )[]TextMark {_acgbc .Offset =*_gbgfg ;_bgcc =append (_bgcc ,_acgbc );*_gbgfg +=len (_acgbc .Text );return _bgcc ;};func _gdfbe (_egfd map[int ]intSet )[]int {_bedc :=make ([]int ,0,len (_egfd ));
for _ffafe :=range _egfd {_bedc =append (_bedc ,_ffafe );};_be .Ints (_bedc );return _bedc ;};

// String returns a human readable description of `ss`.
func (_gabd *shapesState )String ()string {return _ge .Sprintf ("\u007b\u0025\u0064\u0020su\u0062\u0070\u0061\u0074\u0068\u0073\u0020\u0066\u0072\u0065\u0073\u0068\u003d\u0025t\u007d",len (_gabd ._edde ),_gabd ._ffdgg );};func _dbc (_beg []byte ,_gdgb *_cc .PdfFont )string {_dad :=_gdgb .BytesToCharcodes (_beg );
_faga ,_gac ,_acbe :=_gdgb .CharcodesToStrings (_dad ,"");if _acbe > 0{_f .Log .Debug ("\u0072\u0065nd\u0065\u0072\u0054e\u0078\u0074\u003a\u0020num\u0043ha\u0072\u0073\u003d\u0025\u0064\u0020\u006eum\u004d\u0069\u0073\u0073\u0065\u0073\u003d%\u0064",_gac ,_acbe );
};_ada :=_ag .Join (_faga ,"");return _ada ;};func (_fbcdd *textLine )appendWord (_aeee *textWord ){_fbcdd ._ebgf =append (_fbcdd ._ebgf ,_aeee );_fbcdd .PdfRectangle =_cbba (_fbcdd .PdfRectangle ,_aeee .PdfRectangle );if _aeee ._dcca > _fbcdd ._fcgeg {_fbcdd ._fcgeg =_aeee ._dcca ;
};if _aeee ._bfff > _fbcdd ._gdca {_fbcdd ._gdca =_aeee ._bfff ;};};func _cafa (_acdg []pathSection )rulingList {_acge (_acdg );if _bggcg {_f .Log .Info ("\u006da\u006b\u0065\u0046\u0069l\u006c\u0052\u0075\u006c\u0069n\u0067s\u003a \u0025\u0064\u0020\u0066\u0069\u006c\u006cs",len (_acdg ));
};var _ggca rulingList ;for _ ,_decg :=range _acdg {for _ ,_egbb :=range _decg ._gdeb {if !_egbb .isQuadrilateral (){if _bggcg {_f .Log .Error ("!\u0069s\u0051\u0075\u0061\u0064\u0072\u0069\u006c\u0061t\u0065\u0072\u0061\u006c: \u0025\u0073",_egbb );};
continue ;};if _gcbaa ,_cdgca :=_egbb .makeRectRuling (_decg .Color );_cdgca {_ggca =append (_ggca ,_gcbaa );}else {if _effbc {_f .Log .Error ("\u0021\u006d\u0061\u006beR\u0065\u0063\u0074\u0052\u0075\u006c\u0069\u006e\u0067\u003a\u0020\u0025\u0073",_egbb );
};};};};if _bggcg {_f .Log .Info ("\u006d\u0061\u006b\u0065Fi\u006c\u006c\u0052\u0075\u006c\u0069\u006e\u0067\u0073\u003a\u0020\u0025\u0073",_ggca .String ());};return _ggca ;};func (_aeacd *textTable )markCells (){for _gcbeb :=0;_gcbeb < _aeacd ._fcec ;
_gcbeb ++{for _ceeg :=0;_ceeg < _aeacd ._ebed ;_ceeg ++{_caggdc :=_aeacd .get (_ceeg ,_gcbeb );if _caggdc !=nil {_caggdc ._dddg =true ;};};};};func (_gfde paraList )yNeighbours (_begab float64 )map[*textPara ][]int {_fbbe :=make ([]event ,2*len (_gfde ));
if _begab ==0{for _ebce ,_becab :=range _gfde {_fbbe [2*_ebce ]=event {_becab .Lly ,true ,_ebce };_fbbe [2*_ebce +1]=event {_becab .Ury ,false ,_ebce };};}else {for _bgbgg ,_dbbgcc :=range _gfde {_fbbe [2*_bgbgg ]=event {_dbbgcc .Lly -_begab *_dbbgcc .fontsize (),true ,_bgbgg };
_fbbe [2*_bgbgg +1]=event {_dbbgcc .Ury +_begab *_dbbgcc .fontsize (),false ,_bgbgg };};};return _gfde .eventNeighbours (_fbbe );};func (_ebega *wordBag )arrangeText (_bege bool )*textPara {_ebega .sort ();if _adfd {_ebega .removeDuplicates ();};var _ccgg []*textLine ;
for _ ,_dgfgb :=range _ebega .depthIndexes (){for !_ebega .empty (_dgfgb ){_eaeeb :=_ebega .firstReadingIndex (_dgfgb );_gebf :=_ebega .firstWord (_eaeeb );_gcfga :=_aegf (_ebega ,_eaeeb );_cacb :=_gebf ._dcca ;if _cacb < _cdgab {_cacb =_cdgab ;};_acgc :=_gebf ._bfff -_bafg *_cacb ;
_bdbg :=_gebf ._bfff +_bafg *_cacb ;_gfbd :=_fedd *_cacb ;_aafef :=_cgcge *_cacb ;_gdbcb :for {var _feed *textWord ;_cdee :=0;for _ ,_dagbe :=range _ebega .depthBand (_acgc ,_bdbg ){_bebd :=_ebega .highestWord (_dagbe ,_acgc ,_bdbg );if _bebd ==nil {continue ;
};_edgbff :=_eecd (_bebd ,_gcfga ._ebgf [len (_gcfga ._ebgf )-1]);if _edgbff < -_aafef {break _gdbcb ;};if !_bege &&_edgbff > _gfbd {continue ;};if _feed !=nil &&_bbade (_bebd ,_feed )>=0{continue ;};_feed =_bebd ;_cdee =_dagbe ;};if _feed ==nil {break ;
};_gcfga .pullWord (_ebega ,_feed ,_cdee );};_gcfga .markWordBoundaries ();_ccgg =append (_ccgg ,_gcfga );};};if len (_ccgg )==0{return nil ;};_be .Slice (_ccgg ,func (_fbgbc ,_daea int )bool {return _acfg (_ccgg [_fbgbc ],_ccgg [_daea ])< 0});_dbcd :=_bfge (_ebega .PdfRectangle ,_ccgg );
if _cfbd {_f .Log .Info ("\u0061\u0072\u0072an\u0067\u0065\u0054\u0065\u0078\u0074\u0020\u0021\u0021\u0021\u0020\u0070\u0061\u0072\u0061\u003d\u0025\u0073",_dbcd .String ());if _ggaeg {for _gbcf ,_gcecg :=range _dbcd ._geffa {_ge .Printf ("\u0025\u0034\u0064\u003a\u0020\u0025\u0073\u000a",_gbcf ,_gcecg .String ());
if _afea {for _ggggd ,_geacf :=range _gcecg ._ebgf {_ge .Printf ("\u0025\u0038\u0064\u003a\u0020\u0025\u0073\u000a",_ggggd ,_geacf .String ());for _cccc ,_fadaf :=range _geacf ._bacg {_ge .Printf ("\u00251\u0032\u0064\u003a\u0020\u0025\u0073\n",_cccc ,_fadaf .String ());
};};};};};};return _dbcd ;};

// ImageMark represents an image drawn on a page and its position in device coordinates.
// All coordinates are in device coordinates.
type ImageMark struct{Image *_cc .Image ;

// Dimensions of the image as displayed in the PDF.
Width float64 ;Height float64 ;

// Position of the image in PDF coordinates (lower left corner).
X float64 ;Y float64 ;

// Angle in degrees, if rotated.
Angle float64 ;};func _acge (_fcbb []pathSection ){if _fafg < 0.0{return ;};if _bggcg {_f .Log .Info ("\u0067\u0072\u0061\u006e\u0075\u006c\u0061\u0072\u0069\u007a\u0065\u003a\u0020\u0025\u0064 \u0073u\u0062\u0070\u0061\u0074\u0068\u0020\u0073\u0065\u0063\u0074\u0069\u006f\u006e\u0073",len (_fcbb ));
};for _dgdag ,_cdcea :=range _fcbb {for _baec ,_fadg :=range _cdcea ._gdeb {for _aabac ,_cbfbe :=range _fadg ._cggf {_fadg ._cggf [_aabac ]=_ce .Point {X :_gdbeg (_cbfbe .X ),Y :_gdbeg (_cbfbe .Y )};if _bggcg {_eaadb :=_fadg ._cggf [_aabac ];if !_dgcc (_cbfbe ,_eaadb ){_ccdgd :=_ce .Point {X :_eaadb .X -_cbfbe .X ,Y :_eaadb .Y -_cbfbe .Y };
_ge .Printf ("\u0025\u0034d \u002d\u0020\u00254\u0064\u0020\u002d\u0020%4d\u003a %\u002e\u0032\u0066\u0020\u2192\u0020\u0025.2\u0066\u0020\u0028\u0025\u0067\u0029\u000a",_dgdag ,_baec ,_aabac ,_cbfbe ,_eaadb ,_ccdgd );};};};};};};

// Text returns the extracted page text.
func (_bbggf PageText )Text ()string {return _bbggf ._dabb };func (_aabda *shapesState )newSubPath (){_aabda .clearPath ();if _bfgf {_f .Log .Info ("\u006e\u0065\u0077\u0053\u0075\u0062\u0050\u0061\u0074h\u003a\u0020\u0025\u0073",_aabda );};};func (_aeda *textPara )toTextMarks (_aefa *int )[]TextMark {if _aeda ._dcde ==nil {return _aeda .toCellTextMarks (_aefa );
};var _dgcge []TextMark ;for _cbfba :=0;_cbfba < _aeda ._dcde ._fcec ;_cbfba ++{for _gefbe :=0;_gefbe < _aeda ._dcde ._ebed ;_gefbe ++{_gbfea :=_aeda ._dcde .get (_gefbe ,_cbfba );if _gbfea ==nil {_dgcge =_baegc (_dgcge ,_aefa ,"\u0009");}else {_gabda :=_gbfea .toCellTextMarks (_aefa );
_dgcge =append (_dgcge ,_gabda ...);};_dgcge =_baegc (_dgcge ,_aefa ,"\u0020");};if _cbfba < _aeda ._dcde ._fcec -1{_dgcge =_baegc (_dgcge ,_aefa ,"\u000a");};};_fbbb :=_aeda ._dcde ;if _fbbb .isExportable (){_cefb :=_fbbb .toTextTable ();_dgcge =_aaca (_dgcge ,&_cefb );
};return _dgcge ;};

// New returns an Extractor instance for extracting content from the input PDF page.
func New (page *_cc .PdfPage )(*Extractor ,error ){return NewWithOptions (page ,nil )};func _efgeb (_bbgga []*textLine ){_be .Slice (_bbgga ,func (_adffdb ,_aafbc int )bool {_fagdb ,_gcgab :=_bbgga [_adffdb ],_bbgga [_aafbc ];return _fagdb ._gdca < _gcgab ._gdca ;
});};func _dece (_bbad string ,_fgc ,_ccfg int ,_fbc string )string {if _ccfg > len (_bbad )-1{return _bbad [:_fgc ]+_fbc ;};return _bbad [:_fgc ]+_fbc +_bbad [_ccfg :];};func (_fcaa rulingList )intersections ()map[int ]intSet {var _ccgfec ,_cacac []int ;
for _bfgfg ,_bgbed :=range _fcaa {switch _bgbed ._befeb {case _degg :_ccgfec =append (_ccgfec ,_bfgfg );case _gfbdf :_cacac =append (_cacac ,_bfgfg );};};if len (_ccgfec )< _cdbg +1||len (_cacac )< _dcbfb +1{return nil ;};if len (_ccgfec )+len (_cacac )> _bedb {_f .Log .Debug ("\u0069\u006e\u0074\u0065\u0072\u0073e\u0063\u0074\u0069\u006f\u006e\u0073\u003a\u0020\u0054\u004f\u004f\u0020\u004d\u0041\u004e\u0059\u0020\u0072\u0075\u006ci\u006e\u0067\u0073\u0020\u0076\u0065\u0063\u0073\u003d\u0025\u0064\u0020\u003d\u0020%\u0064 \u0078\u0020\u0025\u0064",len (_fcaa ),len (_ccgfec ),len (_cacac ));
return nil ;};_fccbg :=make (map[int ]intSet ,len (_ccgfec )+len (_cacac ));for _ ,_eaef :=range _ccgfec {for _ ,_gddaa :=range _cacac {if _fcaa [_eaef ].intersects (_fcaa [_gddaa ]){if _ ,_gddd :=_fccbg [_eaef ];!_gddd {_fccbg [_eaef ]=make (intSet );
};if _ ,_fccc :=_fccbg [_gddaa ];!_fccc {_fccbg [_gddaa ]=make (intSet );};_fccbg [_eaef ].add (_gddaa );_fccbg [_gddaa ].add (_eaef );};};};return _fccbg ;};func _ggbb (_afbc []*textMark ,_gefbf _cc .PdfRectangle ,_dbbc rulingList ,_fbef []gridTiling ,_faae bool )paraList {_f .Log .Trace ("\u006d\u0061\u006b\u0065\u0054\u0065\u0078\u0074\u0050\u0061\u0067\u0065\u003a \u0025\u0064\u0020\u0065\u006c\u0065m\u0065\u006e\u0074\u0073\u0020\u0070\u0061\u0067\u0065\u0053\u0069\u007a\u0065=\u0025\u002e\u0032\u0066",len (_afbc ),_gefbf );
if len (_afbc )==0{return nil ;};_agfb :=_bfcgg (_afbc ,_gefbf );if len (_agfb )==0{return nil ;};_dbbc .log ("\u006d\u0061\u006be\u0054\u0065\u0078\u0074\u0050\u0061\u0067\u0065");_aadbf ,_cfbgb :=_dbbc .vertsHorzs ();_gbbc :=_fcfc (_agfb ,_gefbf .Ury ,_aadbf ,_cfbgb );
_abcc :=_efgd (_gbbc ,_gefbf .Ury ,_aadbf ,_cfbgb ,_faae );_abcc =_cfddc (_abcc );_dcbg :=make (paraList ,0,len (_abcc ));for _ ,_eacb :=range _abcc {_bgbg :=_eacb .arrangeText (_faae );if _bgbg !=nil {_dcbg =append (_dcbg ,_bgbg );};};if len (_dcbg )>=_adgg {_dcbg =_dcbg .extractTables (_fbef );
};_dcbg .sortReadingOrder ();_dcbg .sortTopoOrder ();_dcbg .log ("\u0073\u006f\u0072te\u0064\u0020\u0069\u006e\u0020\u0072\u0065\u0061\u0064\u0069\u006e\u0067\u0020\u006f\u0072\u0064\u0065\u0072");return _dcbg ;};func (_bdd *textObject )setTextMatrix (_bbb []float64 ){if len (_bbb )!=6{_f .Log .Debug ("\u0045\u0052\u0052OR\u003a\u0020\u006c\u0065\u006e\u0028\u0066\u0029\u0020\u0021\u003d\u0020\u0036\u0020\u0028\u0025\u0064\u0029",len (_bbb ));
return ;};_cagf ,_ddeg ,_fdgd ,_affe ,_afad ,_cgce :=_bbb [0],_bbb [1],_bbb [2],_bbb [3],_bbb [4],_bbb [5];_bdd ._gfdfa =_ce .NewMatrix (_cagf ,_ddeg ,_fdgd ,_affe ,_afad ,_cgce );_bdd ._bdc =_bdd ._gfdfa ;};func _cbff (_dbdg *Extractor ,_afbbg *_cc .PdfPageResources ,_ddee _fb .GraphicsState ,_dbbd *textState ,_fecb *stateStack )*textObject {return &textObject {_gccd :_dbdg ,_baadd :_afbbg ,_fbgf :_ddee ,_dfcb :_fecb ,_fea :_dbbd ,_gfdfa :_ce .IdentityMatrix (),_bdc :_ce .IdentityMatrix ()};
};func (_bdgeg *ruling )alignsPrimary (_gbae *ruling )bool {return _bdgeg ._befeb ==_gbae ._befeb &&_da .Abs (_bdgeg ._gdaaf -_gbae ._gdaaf )< _accd *0.5;};func (_ccca *textTable )getRight ()paraList {_febdd :=make (paraList ,_ccca ._fcec );for _gbga :=0;
_gbga < _ccca ._fcec ;_gbga ++{_ddca :=_ccca .get (_ccca ._ebed -1,_gbga )._dgadb ;if _ddca .taken (){return nil ;};_febdd [_gbga ]=_ddca ;};for _ceda :=0;_ceda < _ccca ._fcec -1;_ceda ++{if _febdd [_ceda ]._cada !=_febdd [_ceda +1]{return nil ;};};return _febdd ;
};func _eecd (_bgb ,_aefc bounded )float64 {return _bgb .bbox ().Llx -_aefc .bbox ().Urx };func _fbgb (_bef []*TextMarkArray ,_eaaf ,_fcad string )(string ,error ){_acgd :=0;for _ ,_beeg :=range _bef {_gea :=_beeg ._aecad [0].DirectObject ;if _gea ==nil {continue ;
};_fcg :=_gea .String ();if len (_fcg )> 1{_ffcb :=_acee (_beeg ,&_eaaf ,&_acgd ,_fcad );if _ffcb !=nil {return _eaaf ,_ffcb ;};}else if len (_fcg )==1{_bfe :=_cfc (_beeg ,&_eaaf ,&_acgd ,_fcad );if _bfe !=nil {return _eaaf ,_bfe ;};};};return _eaaf ,nil ;
};func _egdc (_ecbg ,_fded *textPara )bool {if _ecbg ._gaebg ||_fded ._gaebg {return true ;};return _bfee (_ecbg .depth ()-_fded .depth ());};func (_bebe *textObject )setTextLeading (_eafb float64 ){if _bebe ==nil {return ;};_bebe ._fea ._egdf =_eafb ;
};func (_dbbdba paraList )addNeighbours (){_bfda :=func (_gcbc []int ,_dbef *textPara )([]*textPara ,[]*textPara ){_eeacd :=make ([]*textPara ,0,len (_gcbc )-1);_facb :=make ([]*textPara ,0,len (_gcbc )-1);for _ ,_agcc :=range _gcbc {_bcbc :=_dbbdba [_agcc ];
if _bcbc .Urx <=_dbef .Llx {_eeacd =append (_eeacd ,_bcbc );}else if _bcbc .Llx >=_dbef .Urx {_facb =append (_facb ,_bcbc );};};return _eeacd ,_facb ;};_bffcbe :=func (_afefe []int ,_ebdfc *textPara )([]*textPara ,[]*textPara ){_bfadd :=make ([]*textPara ,0,len (_afefe )-1);
_eeafe :=make ([]*textPara ,0,len (_afefe )-1);for _ ,_fadga :=range _afefe {_cbcb :=_dbbdba [_fadga ];if _cbcb .Ury <=_ebdfc .Lly {_eeafe =append (_eeafe ,_cbcb );}else if _cbcb .Lly >=_ebdfc .Ury {_bfadd =append (_bfadd ,_cbcb );};};return _bfadd ,_eeafe ;
};_cfada :=_dbbdba .yNeighbours (_acca );for _ ,_efbf :=range _dbbdba {_aecf :=_cfada [_efbf ];if len (_aecf )==0{continue ;};_cbfbf ,_caggg :=_bfda (_aecf ,_efbf );if len (_cbfbf )==0&&len (_caggg )==0{continue ;};if len (_cbfbf )> 0{_ccea :=_cbfbf [0];
for _ ,_badgbe :=range _cbfbf [1:]{if _badgbe .Urx >=_ccea .Urx {_ccea =_badgbe ;};};for _ ,_bgbgb :=range _cbfbf {if _bgbgb !=_ccea &&_bgbgb .Urx > _ccea .Llx {_ccea =nil ;break ;};};if _ccea !=nil &&_aagf (_efbf .PdfRectangle ,_ccea .PdfRectangle ){_efbf ._afeb =_ccea ;
};};if len (_caggg )> 0{_cbcdf :=_caggg [0];for _ ,_aecadf :=range _caggg [1:]{if _aecadf .Llx <=_cbcdf .Llx {_cbcdf =_aecadf ;};};for _ ,_gffcc :=range _caggg {if _gffcc !=_cbcdf &&_gffcc .Llx < _cbcdf .Urx {_cbcdf =nil ;break ;};};if _cbcdf !=nil &&_aagf (_efbf .PdfRectangle ,_cbcdf .PdfRectangle ){_efbf ._dgadb =_cbcdf ;
};};};_cfada =_dbbdba .xNeighbours (_afgf );for _ ,_feceb :=range _dbbdba {_bbcgf :=_cfada [_feceb ];if len (_bbcgf )==0{continue ;};_cegbf ,_fgdaa :=_bffcbe (_bbcgf ,_feceb );if len (_cegbf )==0&&len (_fgdaa )==0{continue ;};if len (_fgdaa )> 0{_dfaag :=_fgdaa [0];
for _ ,_fgce :=range _fgdaa [1:]{if _fgce .Ury >=_dfaag .Ury {_dfaag =_fgce ;};};for _ ,_gbbaf :=range _fgdaa {if _gbbaf !=_dfaag &&_gbbaf .Ury > _dfaag .Lly {_dfaag =nil ;break ;};};if _dfaag !=nil &&_dgfce (_feceb .PdfRectangle ,_dfaag .PdfRectangle ){_feceb ._cada =_dfaag ;
};};if len (_cegbf )> 0{_cbdfg :=_cegbf [0];for _ ,_ccef :=range _cegbf [1:]{if _ccef .Lly <=_cbdfg .Lly {_cbdfg =_ccef ;};};for _ ,_abcdfc :=range _cegbf {if _abcdfc !=_cbdfg &&_abcdfc .Lly < _cbdfg .Ury {_cbdfg =nil ;break ;};};if _cbdfg !=nil &&_dgfce (_feceb .PdfRectangle ,_cbdfg .PdfRectangle ){_feceb ._eagea =_cbdfg ;
};};};for _ ,_cafec :=range _dbbdba {if _cafec ._afeb !=nil &&_cafec ._afeb ._dgadb !=_cafec {_cafec ._afeb =nil ;};if _cafec ._eagea !=nil &&_cafec ._eagea ._cada !=_cafec {_cafec ._eagea =nil ;};if _cafec ._dgadb !=nil &&_cafec ._dgadb ._afeb !=_cafec {_cafec ._dgadb =nil ;
};if _cafec ._cada !=nil &&_cafec ._cada ._eagea !=_cafec {_cafec ._cada =nil ;};};};func (_befe paraList )llyRange (_gabf []int ,_bdba ,_eggd float64 )[]int {_eeegg :=len (_befe );if _eggd < _befe [_gabf [0]].Lly ||_bdba > _befe [_gabf [_eeegg -1]].Lly {return nil ;
};_cfgd :=_be .Search (_eeegg ,func (_aeag int )bool {return _befe [_gabf [_aeag ]].Lly >=_bdba });_ggbc :=_be .Search (_eeegg ,func (_cafe int )bool {return _befe [_gabf [_cafe ]].Lly > _eggd });return _gabf [_cfgd :_ggbc ];};func _gaeb (_fbdc ,_aedfe bounded )float64 {_cdge :=_bbade (_fbdc ,_aedfe );
if !_bfee (_cdge ){return _cdge ;};return _dcee (_fbdc ,_aedfe );};func (_dfaad *subpath )clear (){*_dfaad =subpath {}};func _gegg (_bgbbd _ea .PdfObject ,_dbgac _ca .Color )(_ee .Image ,error ){_edegd ,_ebgfd :=_ea .GetStream (_bgbbd );if !_ebgfd {return nil ,nil ;
};_debec ,_ccaec :=_cc .NewXObjectImageFromStream (_edegd );if _ccaec !=nil {return nil ,_ccaec ;};_bdgcc ,_ccaec :=_debec .ToImage ();if _ccaec !=nil {return nil ,_ccaec ;};return _ebef (_bdgcc ,_dbgac ),nil ;};func _bfge (_bfeab _cc .PdfRectangle ,_bgab []*textLine )*textPara {return &textPara {PdfRectangle :_bfeab ,_geffa :_bgab };
};func (_ffceb *textTable )get (_ffgf ,_gfdff int )*textPara {return _ffceb ._dcdb [_dfgde (_ffgf ,_gfdff )]};func _afddg (_fdca []rulingList )(rulingList ,rulingList ){var _cege rulingList ;for _ ,_bdgbg :=range _fdca {_cege =append (_cege ,_bdgbg ...);
};return _cege .vertsHorzs ();};func (_ddfa *textLine )pullWord (_eaeg *wordBag ,_ebgg *textWord ,_agfc int ){_ddfa .appendWord (_ebgg );_eaeg .removeWord (_ebgg ,_agfc );};func (_fdcb *wordBag )empty (_fcag int )bool {_ ,_bbbb :=_fdcb ._fggf [_fcag ];
return !_bbbb };func (_acac *textPara )text ()string {_fcgea :=new (_cd .Buffer );_acac .writeText (_fcgea );return _fcgea .String ();};func (_adcb *textObject )checkOp (_fbeb *_fb .ContentStreamOperation ,_cdfa int ,_bbc bool )(_begf bool ,_bcab error ){if _adcb ==nil {var _dfce []_ea .PdfObject ;
if _cdfa > 0{_dfce =_fbeb .Params ;if len (_dfce )> _cdfa {_dfce =_dfce [:_cdfa ];};};_f .Log .Debug ("\u0025\u0023q \u006f\u0070\u0065r\u0061\u006e\u0064\u0020out\u0073id\u0065\u0020\u0074\u0065\u0078\u0074\u002e p\u0061\u0072\u0061\u006d\u0073\u003d\u0025+\u0076",_fbeb .Operand ,_dfce );
};if _cdfa >=0{if len (_fbeb .Params )!=_cdfa {if _bbc {_bcab =_df .New ("\u0069n\u0063\u006f\u0072\u0072e\u0063\u0074\u0020\u0070\u0061r\u0061m\u0065t\u0065\u0072\u0020\u0063\u006f\u0075\u006et");};_f .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u0025\u0023\u0071\u0020\u0073\u0068\u006f\u0075\u006c\u0064\u0020h\u0061\u0076\u0065\u0020\u0025\u0064\u0020i\u006e\u0070\u0075\u0074\u0020\u0070\u0061\u0072\u0061\u006d\u0073,\u0020\u0067\u006f\u0074\u0020\u0025\u0064\u0020\u0025\u002b\u0076",_fbeb .Operand ,_cdfa ,len (_fbeb .Params ),_fbeb .Params );
return false ,_bcab ;};};return true ,nil ;};func (_ebgag *textTable )log (_gfefd string ){if !_ababc {return ;};_f .Log .Info ("~\u007e\u007e\u0020\u0025\u0073\u003a \u0025\u0064\u0020\u0078\u0020\u0025d\u0020\u0067\u0072\u0069\u0064\u003d\u0025t\u000a\u0020\u0020\u0020\u0020\u0020\u0020\u0025\u0036\u002e2\u0066",_gfefd ,_ebgag ._ebed ,_ebgag ._fcec ,_ebgag ._fggcd ,_ebgag .PdfRectangle );
for _caege :=0;_caege < _ebgag ._fcec ;_caege ++{for _eabdbd :=0;_eabdbd < _ebgag ._ebed ;_eabdbd ++{_gdead :=_ebgag .get (_eabdbd ,_caege );if _gdead ==nil {continue ;};_ge .Printf ("%\u0034\u0064\u0020\u00252d\u003a \u0025\u0036\u002e\u0032\u0066 \u0025\u0071\u0020\u0025\u0064\u000a",_eabdbd ,_caege ,_gdead .PdfRectangle ,_bcfdgc (_gdead .text (),50),_fa .RuneCountInString (_gdead .text ()));
};};};func (_eebbaf *textTable )isExportable ()bool {if _eebbaf ._fggcd {return true ;};_acdfd :=func (_gcddb int )bool {_fcbad :=_eebbaf .get (0,_gcddb );if _fcbad ==nil {return false ;};_gedcd :=_fcbad .text ();_agfgf :=_fa .RuneCountInString (_gedcd );
_dfdbg :=_bgcf .MatchString (_gedcd );return _agfgf <=1||_dfdbg ;};for _cgba :=0;_cgba < _eebbaf ._fcec ;_cgba ++{if !_acdfd (_cgba ){return true ;};};return false ;};func _aaca (_fcbg []TextMark ,_bdgcg *TextTable )[]TextMark {var _fdeb []TextMark ;for _ ,_dfgd :=range _fcbg {_dfgd ._eaee =true ;
_dfgd ._cbfb =_bdgcg ;_fdeb =append (_fdeb ,_dfgd );};return _fdeb ;};func (_dggce *textTable )getComposite (_bfbb ,_bagd int )(paraList ,_cc .PdfRectangle ){_bbdff ,_dbagc :=_dggce ._dccb [_dfgde (_bfbb ,_bagd )];if _ababc {_ge .Printf ("\u0020\u0020\u0020\u0020\u0020\u0020\u0020\u0020\u0067\u0065\u0074\u0043\u006f\u006d\u0070o\u0073i\u0074\u0065\u0028\u0025\u0064\u002c\u0025\u0064\u0029\u002d\u003e\u0025\u0073\u000a",_bfbb ,_bagd ,_bbdff .String ());
};if !_dbagc {return nil ,_cc .PdfRectangle {};};return _bbdff .parasBBox ();};func (_fbbc *wordBag )text ()string {_dgfge :=_fbbc .allWords ();_cabd :=make ([]string ,len (_dgfge ));for _efcg ,_bgff :=range _dgfge {_cabd [_efcg ]=_bgff ._ggeag ;};return _ag .Join (_cabd ,"\u0020");
};type pathSection struct{_gdeb []*subpath ;_ca .Color ;};

// String returns a description of `v`.
func (_bbbbg *ruling )String ()string {if _bbbbg ._befeb ==_dcbd {return "\u004e\u004f\u0054\u0020\u0052\u0055\u004c\u0049\u004e\u0047";};_affbg ,_bace :="\u0078","\u0079";if _bbbbg ._befeb ==_gfbdf {_affbg ,_bace ="\u0079","\u0078";};_gdga :="";if _bbbbg ._bfdc !=0.0{_gdga =_ge .Sprintf (" \u0077\u0069\u0064\u0074\u0068\u003d\u0025\u002e\u0032\u0066",_bbbbg ._bfdc );
};return _ge .Sprintf ("\u0025\u00310\u0073\u0020\u0025\u0073\u003d\u0025\u0036\u002e\u0032\u0066\u0020\u0025\u0073\u003d\u0025\u0036\u002e\u0032\u0066\u0020\u002d\u0020\u0025\u0036\u002e\u0032\u0066\u0020\u0028\u0025\u0036\u002e\u0032\u0066\u0029\u0020\u0025\u0073\u0020\u0025\u0076\u0025\u0073",_bbbbg ._befeb ,_affbg ,_bbbbg ._gdaaf ,_bace ,_bbbbg ._bfbdb ,_bbbbg ._bedbg ,_bbbbg ._bedbg -_bbbbg ._bfbdb ,_bbbbg ._ceab ,_bbbbg .Color ,_gdga );
};func (_dcfe paraList )merge ()*textPara {_f .Log .Trace ("\u006d\u0065\u0072\u0067\u0065:\u0020\u0070\u0061\u0072\u0061\u0073\u003d\u0025\u0064\u0020\u003d\u003d\u003d=\u003d\u003d\u003d\u003d\u003d\u003d\u003d\u0078\u003d\u003d\u003d\u003d\u003d\u003d\u003d\u003d\u003d\u003d\u003d\u003d\u003d",len (_dcfe ));
if len (_dcfe )==0{return nil ;};_dcfe .sortReadingOrder ();_gdgd :=_dcfe [0].PdfRectangle ;_eabdb :=_dcfe [0]._geffa ;for _ ,_caddb :=range _dcfe [1:]{_gdgd =_cbba (_gdgd ,_caddb .PdfRectangle );_eabdb =append (_eabdb ,_caddb ._geffa ...);};return _bfge (_gdgd ,_eabdb );
};

// ExtractPageText returns the text contents of `e` (an Extractor for a page) as a PageText.
// TODO(peterwilliams97): The stats complicate this function signature and aren't very useful.
//
//	Replace with a function like Extract() (*PageText, error)
func (_gbcc *Extractor )ExtractPageText ()(*PageText ,int ,int ,error ){_aceed ,_fegf ,_cbcd ,_cbb :=_gbcc .extractPageText (_gbcc ._dddf ,_gbcc ._gf ,_ce .IdentityMatrix (),0,false );if _cbb !=nil &&_cbb !=_cc .ErrColorOutOfRange {return nil ,0,0,_cbb ;
};if _gbcc ._acb !=nil {_aceed ._ceef ._gbdd =_gbcc ._acb .UseSimplerExtractionProcess ;_aceed ._ceef ._fcdcf =_gbcc ._acb .DisableMultiColumns ;};_aceed .computeViews ();_cbb =_daeg (_aceed );if _cbb !=nil {return nil ,0,0,_cbb ;};if _gbcc ._acb !=nil {if _gbcc ._acb .ApplyCropBox &&_gbcc ._afc !=nil {_aceed .ApplyArea (*_gbcc ._afc );
};_aceed ._ceef ._cgdc =_gbcc ._acb .DisableDocumentTags ;};return _aceed ,_fegf ,_cbcd ,nil ;};func _gdbf (_geeg string ,_aac string )([][]int ,error ){_fcc ,_ggcf :=_ec .Compile (_geeg );if _ggcf !=nil {return nil ,_ge .Errorf ("\u0065\u0072\u0072\u006f\u0072\u0020c\u006f\u006d\u0070\u0069\u006c\u0069\u006e\u0067\u0020\u0072\u0065\u0067\u0065x\u0020\u0070\u0061\u0074\u0074\u0065\u0072n\u003a\u0020\u0025\u0077",_ggcf );
};_bda :=_fcc .FindAllStringIndex (_aac ,-1);return _bda ,nil ;};func (_dedcd *textTable )getDown ()paraList {_cgdbb :=make (paraList ,_dedcd ._ebed );for _bfbdg :=0;_bfbdg < _dedcd ._ebed ;_bfbdg ++{_gbaaf :=_dedcd .get (_bfbdg ,_dedcd ._fcec -1)._cada ;
if _gbaaf .taken (){return nil ;};_cgdbb [_bfbdg ]=_gbaaf ;};for _cdagg :=0;_cdagg < _dedcd ._ebed -1;_cdagg ++{if _cgdbb [_cdagg ]._dgadb !=_cgdbb [_cdagg +1]{return nil ;};};return _cgdbb ;};func (_efgb gridTiling )complete ()bool {for _ ,_fagb :=range _efgb ._bceeb {for _ ,_gacg :=range _fagb {if !_gacg .complete (){return false ;
};};};return true ;};func _cgc (_edb string ,_gg bool ,_ff bool )BidiText {_bea :="\u006c\u0074\u0072";if _ff {_bea ="\u0074\u0074\u0062";}else if !_gg {_bea ="\u0072\u0074\u006c";};return BidiText {_eec :_edb ,_cdg :_bea };};func _ggddc (_cdcg _cc .PdfRectangle ,_gaeed ,_eefd ,_abee ,_ebffb *ruling )gridTile {_gbgff :=_cdcg .Llx ;
_caffc :=_cdcg .Urx ;_ggce :=_cdcg .Lly ;_fbed :=_cdcg .Ury ;return gridTile {PdfRectangle :_cdcg ,_egeg :_gaeed !=nil &&_gaeed .encloses (_ggce ,_fbed ),_gbfec :_eefd !=nil &&_eefd .encloses (_ggce ,_fbed ),_gfgcaf :_abee !=nil &&_abee .encloses (_gbgff ,_caffc ),_fbfd :_ebffb !=nil &&_ebffb .encloses (_gbgff ,_caffc )};
};func (_cgcb *imageExtractContext )extractInlineImage (_fce *_fb .ContentStreamInlineImage ,_fgfa _fb .GraphicsState ,_efg *_cc .PdfPageResources )error {_ffa ,_acf :=_fce .ToImage (_efg );if _acf !=nil {return _acf ;};_eca ,_acf :=_fce .GetColorSpace (_efg );
if _acf !=nil {return _acf ;};if _eca ==nil {_eca =_cc .NewPdfColorspaceDeviceGray ();};_afa ,_acf :=_eca .ImageToRGB (*_ffa );if _acf !=nil {return _acf ;};_bcc :=ImageMark {Image :&_afa ,Width :_fgfa .CTM .ScalingFactorX (),Height :_fgfa .CTM .ScalingFactorY (),Angle :_fgfa .CTM .Angle ()};
_bcc .X ,_bcc .Y =_fgfa .CTM .Translation ();_cgcb ._gbg =append (_cgcb ._gbg ,_bcc );_cgcb ._afcd ++;return nil ;};var _af =[]string {"\u0041\u004e","\u0041\u004e","\u0041\u004e","\u0041\u004e","\u0041\u004e","\u0041\u004e","\u004f\u004e","\u004f\u004e","\u0041\u004c","\u0045\u0054","\u0045\u0054","\u0041\u004c","\u0043\u0053","\u0041\u004c","\u004f\u004e","\u004f\u004e","\u004e\u0053\u004d","\u004e\u0053\u004d","\u004e\u0053\u004d","\u004e\u0053\u004d","\u004e\u0053\u004d","\u004e\u0053\u004d","\u004e\u0053\u004d","\u004e\u0053\u004d","\u004e\u0053\u004d","\u004e\u0053\u004d","\u004e\u0053\u004d","\u0041\u004c","\u0041\u004c","","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u004e\u0053\u004d","\u004e\u0053\u004d","\u004e\u0053\u004d","\u004e\u0053\u004d","\u004e\u0053\u004d","\u004e\u0053\u004d","\u004e\u0053\u004d","\u004e\u0053\u004d","\u004e\u0053\u004d","\u004e\u0053\u004d","\u004e\u0053\u004d","\u004e\u0053\u004d","\u004e\u0053\u004d","\u004e\u0053\u004d","\u004e\u0053\u004d","\u004e\u0053\u004d","\u004e\u0053\u004d","\u004e\u0053\u004d","\u004e\u0053\u004d","\u004e\u0053\u004d","\u004e\u0053\u004d","\u0041\u004e","\u0041\u004e","\u0041\u004e","\u0041\u004e","\u0041\u004e","\u0041\u004e","\u0041\u004e","\u0041\u004e","\u0041\u004e","\u0041\u004e","\u0045\u0054","\u0041\u004e","\u0041\u004e","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u004e\u0053\u004d","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u004e\u0053\u004d","\u004e\u0053\u004d","\u004e\u0053\u004d","\u004e\u0053\u004d","\u004e\u0053\u004d","\u004e\u0053\u004d","\u004e\u0053\u004d","\u0041\u004e","\u004f\u004e","\u004e\u0053\u004d","\u004e\u0053\u004d","\u004e\u0053\u004d","\u004e\u0053\u004d","\u004e\u0053\u004d","\u004e\u0053\u004d","\u0041\u004c","\u0041\u004c","\u004e\u0053\u004d","\u004e\u0053\u004d","\u004f\u004e","\u004e\u0053\u004d","\u004e\u0053\u004d","\u004e\u0053\u004d","\u004e\u0053\u004d","\u0041\u004c","\u0041\u004c","\u0045\u004e","\u0045\u004e","\u0045\u004e","\u0045\u004e","\u0045\u004e","\u0045\u004e","\u0045\u004e","\u0045\u004e","\u0045\u004e","\u0045\u004e","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c","\u0041\u004c"};
type textObject struct{_gccd *Extractor ;_baadd *_cc .PdfPageResources ;_fbgf _fb .GraphicsState ;_fea *textState ;_dfcb *stateStack ;_gfdfa _ce .Matrix ;_bdc _ce .Matrix ;_dbge []*textMark ;_aafd bool ;};func (_eadbg *textTable )growTable (){_cadca :=func (_deecc paraList ){_eadbg ._fcec ++;
for _bfacfa :=0;_bfacfa < _eadbg ._ebed ;_bfacfa ++{_deceg :=_deecc [_bfacfa ];_eadbg .put (_bfacfa ,_eadbg ._fcec -1,_deceg );};};_fabcb :=func (_deeg paraList ){_eadbg ._ebed ++;for _fbgdc :=0;_fbgdc < _eadbg ._fcec ;_fbgdc ++{_geaf :=_deeg [_fbgdc ];
_eadbg .put (_eadbg ._ebed -1,_fbgdc ,_geaf );};};if _gfab {_eadbg .log ("\u0067r\u006f\u0077\u0054\u0061\u0062\u006ce");};for _gaeba :=0;;_gaeba ++{_ggfcd :=false ;_ecdbd :=_eadbg .getDown ();_gdaee :=_eadbg .getRight ();if _gfab {_ge .Printf ("\u0025\u0034\u0064\u003a\u0020\u0025\u0073\u000a",_gaeba ,_eadbg );
_ge .Printf ("\u0020\u0020 \u0020\u0020\u0020 \u0020\u0064\u006f\u0077\u006e\u003d\u0025\u0073\u000a",_ecdbd );_ge .Printf ("\u0020\u0020 \u0020\u0020\u0020 \u0072\u0069\u0067\u0068\u0074\u003d\u0025\u0073\u000a",_gdaee );};if _ecdbd !=nil &&_gdaee !=nil {_gbbd :=_ecdbd [len (_ecdbd )-1];
if !_gbbd .taken ()&&_gbbd ==_gdaee [len (_gdaee )-1]{_cadca (_ecdbd );if _gdaee =_eadbg .getRight ();_gdaee !=nil {_fabcb (_gdaee );_eadbg .put (_eadbg ._ebed -1,_eadbg ._fcec -1,_gbbd );};_ggfcd =true ;};};if !_ggfcd &&_ecdbd !=nil {_cadca (_ecdbd );
_ggfcd =true ;};if !_ggfcd &&_gdaee !=nil {_fabcb (_gdaee );_ggfcd =true ;};if !_ggfcd {break ;};};};func _gdg (_bfg []Font ,_feg string )bool {for _ ,_ddg :=range _bfg {if _ddg .FontName ==_feg {return true ;};};return false ;};func (_ecdfd *textTable )logComposite (_eddb string ){if !_ababc {return ;
};_f .Log .Info ("\u007e~\u007eP\u0061\u0072\u0061\u0020\u0025d\u0020\u0078 \u0025\u0064\u0020\u0025\u0073",_ecdfd ._ebed ,_ecdfd ._fcec ,_eddb );_ge .Printf ("\u0025\u0035\u0073 \u007c","");for _dbdba :=0;_dbdba < _ecdfd ._ebed ;_dbdba ++{_ge .Printf ("\u0025\u0033\u0064 \u007c",_dbdba );
};_ge .Println ("");_ge .Printf ("\u0025\u0035\u0073 \u002b","");for _eedg :=0;_eedg < _ecdfd ._ebed ;_eedg ++{_ge .Printf ("\u0025\u0033\u0073 \u002b","\u002d\u002d\u002d");};_ge .Println ("");for _cfadd :=0;_cfadd < _ecdfd ._fcec ;_cfadd ++{_ge .Printf ("\u0025\u0035\u0064 \u007c",_cfadd );
for _bdbgb :=0;_bdbgb < _ecdfd ._ebed ;_bdbgb ++{_fbbcd ,_ :=_ecdfd ._dccb [_dfgde (_bdbgb ,_cfadd )].parasBBox ();_ge .Printf ("\u0025\u0033\u0064 \u007c",len (_fbbcd ));};_ge .Println ("");};_f .Log .Info ("\u007e~\u007eT\u0065\u0078\u0074\u0020\u0025d\u0020\u0078 \u0025\u0064\u0020\u0025\u0073",_ecdfd ._ebed ,_ecdfd ._fcec ,_eddb );
_ge .Printf ("\u0025\u0035\u0073 \u007c","");for _bafff :=0;_bafff < _ecdfd ._ebed ;_bafff ++{_ge .Printf ("\u0025\u0031\u0032\u0064\u0020\u007c",_bafff );};_ge .Println ("");_ge .Printf ("\u0025\u0035\u0073 \u002b","");for _babb :=0;_babb < _ecdfd ._ebed ;
_babb ++{_ge .Print ("\u002d\u002d\u002d\u002d\u002d\u002d\u002d\u002d\u002d-\u002d\u002d\u002d\u002b");};_ge .Println ("");for _gcgb :=0;_gcgb < _ecdfd ._fcec ;_gcgb ++{_ge .Printf ("\u0025\u0035\u0064 \u007c",_gcgb );for _fcabb :=0;_fcabb < _ecdfd ._ebed ;
_fcabb ++{_ccfd ,_ :=_ecdfd ._dccb [_dfgde (_fcabb ,_gcgb )].parasBBox ();_cfegg :="";_febdc :=_ccfd .merge ();if _febdc !=nil {_cfegg =_febdc .text ();};_cfegg =_ge .Sprintf ("\u0025\u0071",_bcfdgc (_cfegg ,12));_cfegg =_cfegg [1:len (_cfegg )-1];_ge .Printf ("\u0025\u0031\u0032\u0073\u0020\u007c",_cfegg );
};_ge .Println ("");};};func (_eddbg *textWord )computeText ()string {_baefd :=make ([]string ,len (_eddbg ._bacg ));for _eeddb ,_accca :=range _eddbg ._bacg {_baefd [_eeddb ]=_accca ._fecec ;};return _ag .Join (_baefd ,"");};type textTable struct{_cc .PdfRectangle ;
_ebed ,_fcec int ;_fggcd bool ;_dcdb map[uint64 ]*textPara ;_dccb map[uint64 ]compositeCell ;};func (_fdffc *subpath )close (){if !_dgcc (_fdffc ._cggf [0],_fdffc .last ()){_fdffc .add (_fdffc ._cggf [0]);};_fdffc ._cafc =true ;_fdffc .removeDuplicates ();
};func _aagf (_egda ,_cbe _cc .PdfRectangle )bool {return _egda .Lly <=_cbe .Ury &&_cbe .Lly <=_egda .Ury };func (_cbgfg *wordBag )removeDuplicates (){if _caffd {_f .Log .Info ("r\u0065m\u006f\u0076\u0065\u0044\u0075\u0070\u006c\u0069c\u0061\u0074\u0065\u0073: \u0025\u0071",_cbgfg .text ());
};for _ ,_ffbbd :=range _cbgfg .depthIndexes (){if len (_cbgfg ._fggf [_ffbbd ])==0{continue ;};_acfc :=_cbgfg ._fggf [_ffbbd ][0];_aedfeb :=_ccfc *_acfc ._dcca ;_dbde :=_acfc ._bfff ;for _ ,_dbag :=range _cbgfg .depthBand (_dbde ,_dbde +_aedfeb ){_gbfd :=map[*textWord ]struct{}{};
_dedg :=_cbgfg ._fggf [_dbag ];for _ ,_bceca :=range _dedg {if _ ,_cace :=_gbfd [_bceca ];_cace {continue ;};for _ ,_aage :=range _dedg {if _ ,_cgfa :=_gbfd [_aage ];_cgfa {continue ;};if _aage !=_bceca &&_aage ._ggeag ==_bceca ._ggeag &&_da .Abs (_aage .Llx -_bceca .Llx )< _aedfeb &&_da .Abs (_aage .Urx -_bceca .Urx )< _aedfeb &&_da .Abs (_aage .Lly -_bceca .Lly )< _aedfeb &&_da .Abs (_aage .Ury -_bceca .Ury )< _aedfeb {_gbfd [_aage ]=struct{}{};
};};};if len (_gbfd )> 0{_gabg :=0;for _ ,_gfcb :=range _dedg {if _ ,_bfbg :=_gbfd [_gfcb ];!_bfbg {_dedg [_gabg ]=_gfcb ;_gabg ++;};};_cbgfg ._fggf [_dbag ]=_dedg [:len (_dedg )-len (_gbfd )];if len (_cbgfg ._fggf [_dbag ])==0{delete (_cbgfg ._fggf ,_dbag );
};};};};};func _cfc (_bgedg *TextMarkArray ,_gfgca *string ,_cde *int ,_cggbe string )error {var _fdd TextMark ;for _ ,_eed :=range _bgedg .Elements (){_dfc :=_eed .Text ;_fge :=_eed .Font ;_fccg :="";_fcf :=*_gfgca ;if len (_fcf )> *_cde {_fccg =_fcf [*_cde :*_cde +len (_dfc )];
}else if *_cde ==len (_cggbe )-1&&len (_fcf )> *_cde {_fccg =_fcf [*_cde :];};_cea :=_eed .DirectObject ;if _cea ==nil &&_eed .Text =="\u0020"{_gaee :=_fdd .ObjString ;_ecab :=_gaee [len (_gaee )-1];if _ecab !=_eed .Text {_cea =_fdd .DirectObject ;_fge =_fdd .Font ;
_fbd ,_ggba :=_ea .GetString (_cea );if !_ggba {return _ge .Errorf ("\u0075n\u0061\u0062l\u0065\u0020\u0074\u006f \u0067\u0065\u0074 \u0073\u0074\u0072\u0069\u006e\u0067\u0020\u0042\u0079te\u0073\u0020\u0066r\u006f\u006d \u0064\u0069\u0072\u0065\u0063\u0074O\u0062\u006ae\u0063\u0074");
};_bdag ,_ggba :=_ea .GetStringBytes (_cea );if !_ggba {return _ea .ErrTypeError ;};_afef :=_dbc (_bdag ,_fge );_fccg =_afef +_fccg ;_affg (_fbd ,_fccg ,_fge );*_cde +=len (_dfc );continue ;};};_fcd ,_acgf :=_ea .GetString (_cea );if !_acgf {return _ge .Errorf ("\u0075n\u0061\u0062l\u0065\u0020\u0074\u006f \u0067\u0065\u0074 \u0073\u0074\u0072\u0069\u006e\u0067\u0020\u0042\u0079te\u0073\u0020\u0066r\u006f\u006d \u0064\u0069\u0072\u0065\u0063\u0074O\u0062\u006ae\u0063\u0074");
};_fab :="";_adfe ,_acgf :=_ea .GetStringBytes (_cea );if !_acgf {return _ea .ErrTypeError ;};_fffe :=_dbc (_adfe ,_fge );_fab =_ag .Replace (_fffe ,_dfc ,_fccg ,1);_affg (_fcd ,_fab ,_fge );*_cde +=len (_dfc );_fdd =_eed ;};return nil ;};func (_ffab *wordBag )depthIndexes ()[]int {if len (_ffab ._fggf )==0{return nil ;
};_bbfe :=make ([]int ,len (_ffab ._fggf ));_gfda :=0;for _ffdd :=range _ffab ._fggf {_bbfe [_gfda ]=_ffdd ;_gfda ++;};_be .Ints (_bbfe );return _bbfe ;};func _deaa (_ggggc []*textLine ,_edffg map[float64 ][]*textLine )[]*list {_bbcd :=_cbfff (_edffg );
_eegfa :=[]*list {};if len (_bbcd )==0{return _eegfa ;};_dbdb :=_bbcd [0];_febf :=1;_caaea :=_edffg [_dbdb ];for _eageg ,_ccfb :=range _caaea {var _fede float64 ;_bcaf :=[]*list {};_fgfe :=_ccfb ._gdca ;_effga :=-1.0;if _eageg < len (_caaea )-1{_effga =_caaea [_eageg +1]._gdca ;
};if _febf < len (_bbcd ){_bcaf =_ffcd (_ggggc ,_edffg ,_bbcd ,_febf ,_fgfe ,_effga );};_fede =_effga ;if len (_bcaf )> 0{_gddb :=_bcaf [0];if len (_gddb ._faeg )> 0{_fede =_gddb ._faeg [0]._gdca ;};};_cbga :=[]*textLine {_ccfb };_cbacg :=_dgfaaf (_ccfb ,_ggggc ,_bbcd ,_fgfe ,_fede );
_cbga =append (_cbga ,_cbacg ...);_fgbc :=_fdfcc (_cbga ,"\u0062\u0075\u006c\u006c\u0065\u0074",_bcaf );_fgbc ._eeff =_dbbde (_cbga ,"");_eegfa =append (_eegfa ,_fgbc );};return _eegfa ;};

// NewFromContents creates a new extractor from contents and page resources.
func NewFromContents (contents string ,resources *_cc .PdfPageResources )(*Extractor ,error ){const _gba ="\u0065x\u0074\u0072\u0061\u0063t\u006f\u0072\u002e\u004e\u0065w\u0046r\u006fm\u0043\u006f\u006e\u0074\u0065\u006e\u0074s";_dgf :=&Extractor {_dddf :contents ,_gf :resources ,_cfb :map[string ]fontEntry {},_cfd :map[string ]textResult {}};
_g .TrackUse (_gba );return _dgf ,nil ;};func (_dfge *textTable )put (_efaf ,_eagd int ,_bgac *textPara ){_dfge ._dcdb [_dfgde (_efaf ,_eagd )]=_bgac ;};

// ExtractText processes and extracts all text data in content streams and returns as a string.
// It takes into account character encodings in the PDF file, which are decoded by
// CharcodeBytesToUnicode.
// Characters that can't be decoded are replaced with MissingCodeRune ('\ufffd' = �).
func (_dab *Extractor )ExtractText ()(string ,error ){_facf ,_ ,_ ,_bega :=_dab .ExtractTextWithStats ();return _facf ,_bega ;};

// NewWithOptions an Extractor instance for extracting content from the input PDF page with options.
func NewWithOptions (page *_cc .PdfPage ,options *Options )(*Extractor ,error ){const _fdf ="\u0065x\u0074\u0072\u0061\u0063\u0074\u006f\u0072\u002e\u004e\u0065\u0077W\u0069\u0074\u0068\u004f\u0070\u0074\u0069\u006f\u006e\u0073";_cdf ,_fdcg :=page .GetAllContentStreams ();
if _fdcg !=nil {return nil ,_fdcg ;};_ege ,_aga :=page .GetStructTreeRoot ();if !_aga {_f .Log .Debug ("T\u0068\u0065\u0020\u0070\u0064\u0066\u0020\u0064\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u0020\u0069\u0073\u0020\u006e\u006f\u0074\u0020\u0074\u0061\u0067g\u0065d\u002e\u0020\u0053\u0074r\u0075\u0063t\u0054\u0072\u0065\u0065\u0052\u006f\u006f\u0074\u0020\u0064\u006f\u0065\u0073\u006e\u0027\u0074\u0020\u0065\u0078\u0069\u0073\u0074\u002e");
};_edg :=page .GetContainingPdfObject ();_dge ,_fdcg :=page .GetMediaBox ();if _fdcg !=nil {return nil ,_ge .Errorf ("\u0065\u0078\u0074r\u0061\u0063\u0074\u006fr\u0020\u0072\u0065\u0071\u0075\u0069\u0072e\u0073\u0020\u006d\u0065\u0064\u0069\u0061\u0042\u006f\u0078\u002e\u0020\u0025\u0076",_fdcg );
};_aca :=&Extractor {_dddf :_cdf ,_gf :page .Resources ,_cgg :*_dge ,_afc :page .CropBox ,_cfb :map[string ]fontEntry {},_cfd :map[string ]textResult {},_ggg :map[string ]textResult {},_acb :options ,_fbe :_ege ,_aa :_edg };if _aca ._cgg .Llx > _aca ._cgg .Urx {_f .Log .Info ("\u004d\u0065\u0064\u0069\u0061\u0042o\u0078\u0020\u0068\u0061\u0073\u0020\u0058\u0020\u0063\u006f\u006f\u0072\u0064\u0069\u006e\u0061\u0074\u0065\u0073\u0020r\u0065\u0076\u0065\u0072\u0073\u0065\u0064\u002e\u0020\u0025\u002e\u0032\u0066\u0020F\u0069x\u0069\u006e\u0067\u002e",_aca ._cgg );
_aca ._cgg .Llx ,_aca ._cgg .Urx =_aca ._cgg .Urx ,_aca ._cgg .Llx ;};if _aca ._cgg .Lly > _aca ._cgg .Ury {_f .Log .Info ("\u004d\u0065\u0064\u0069\u0061\u0042o\u0078\u0020\u0068\u0061\u0073\u0020\u0059\u0020\u0063\u006f\u006f\u0072\u0064\u0069\u006e\u0061\u0074\u0065\u0073\u0020r\u0065\u0076\u0065\u0072\u0073\u0065\u0064\u002e\u0020\u0025\u002e\u0032\u0066\u0020F\u0069x\u0069\u006e\u0067\u002e",_aca ._cgg );
_aca ._cgg .Lly ,_aca ._cgg .Ury =_aca ._cgg .Ury ,_aca ._cgg .Lly ;};if _aca ._acb !=nil {if _aca ._acb .IncludeAnnotations {_aca ._ccf ,_fdcg =page .GetAnnotations ();if _fdcg !=nil {_f .Log .Debug ("\u0045\u0072r\u006f\u0072\u0020\u0067\u0065\u0074\u0074\u0069\u006e\u0067\u0020\u0061\u006e\u006e\u006f\u0074\u0061\u0074\u0069\u006f\u006e\u0073: \u0025\u0076",_fdcg );
};};};_g .TrackUse (_fdf );return _aca ,nil ;};func (_cgdg *textPara )getListLines ()[]*textLine {var _dddfb []*textLine ;_fbgeb :=_ggggg (_cgdg ._geffa );for _ ,_bbbg :=range _cgdg ._geffa {_cebfe :=_bbbg ._ebgf [0]._ggeag [0];if _fcdd (_cebfe ){_dddfb =append (_dddfb ,_bbbg );
};};_dddfb =append (_dddfb ,_fbgeb ...);return _dddfb ;};func _ccfe (_aggf *TextMarkArray )string {_effb :="";for _ ,_bac :=range _aggf .Elements (){_effb +=_bac .Text ;};return _effb ;};func _eeg (_dg []string ,_daf int ,_ed int ){for _dc ,_cee :=_daf ,_ed -1;
_dc < _cee ;_dc ,_cee =_dc +1,_cee -1{_ae :=_dg [_dc ];_dg [_dc ]=_dg [_cee ];_dg [_cee ]=_ae ;};};func (_fbbbf compositeCell )parasBBox ()(paraList ,_cc .PdfRectangle ){return _fbbbf .paraList ,_fbbbf .PdfRectangle ;};func (_dda *Extractor )extractPageText (_bfd string ,_acgbb *_cc .PdfPageResources ,_efbb _ce .Matrix ,_fgd int ,_dbgg bool )(*PageText ,int ,int ,error ){_f .Log .Trace ("\u0065x\u0074\u0072\u0061\u0063t\u0050\u0061\u0067\u0065\u0054e\u0078t\u003a \u006c\u0065\u0076\u0065\u006c\u003d\u0025d",_fgd );
_ccdc :=&PageText {_fccb :_dda ._cgg ,_ebeb :_dda ._fbe ,_fbcg :_dda ._aa };_edgf :=_bgeda (_dda ._cgg );var _gec stateStack ;_gaef :=_cbff (_dda ,_acgbb ,_fb .GraphicsState {},&_edgf ,&_gec );_abf :=shapesState {_fbcb :_efbb ,_badf :_ce .IdentityMatrix (),_abef :_gaef };
var _bcea bool ;_ccdg :=-1;_bcda :="";if _fgd > _cdfd {_bggc :=_df .New ("\u0066\u006f\u0072\u006d s\u0074\u0061\u0063\u006b\u0020\u006f\u0076\u0065\u0072\u0066\u006c\u006f\u0077");_f .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a \u0065\u0078\u0074\u0072\u0061\u0063\u0074\u0050\u0061\u0067\u0065\u0054\u0065\u0078\u0074\u002e\u0020\u0072\u0065\u0063u\u0072\u0073\u0069\u006f\u006e\u0020\u006c\u0065\u0076\u0065\u006c\u003d\u0025\u0064 \u0065r\u0072\u003d\u0025\u0076",_fgd ,_bggc );
return _ccdc ,_edgf ._fgcd ,_edgf ._baad ,_bggc ;};_adb :=_fb .NewContentStreamParser (_bfd );_ebe ,_bgc :=_adb .Parse ();if _bgc !=nil {_f .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020e\u0078\u0074\u0072a\u0063\u0074\u0050\u0061g\u0065\u0054\u0065\u0078\u0074\u0020\u0070\u0061\u0072\u0073\u0065\u0020\u0066\u0061\u0069\u006c\u0065\u0064\u002e\u0020\u0065\u0072\u0072\u003d\u0025\u0076",_bgc );
return _ccdc ,_edgf ._fgcd ,_edgf ._baad ,_bgc ;};_ccdc ._edeb =_ebe ;_aee :=_fb .NewContentStreamProcessor (*_ebe );if _dda ._acb !=nil {_aee .SetRelaxedMode (_dda ._acb .RelaxedMode );};_aee .AddHandler (_fb .HandlerConditionEnumAllOperands ,"",func (_dgg *_fb .ContentStreamOperation ,_aafe _fb .GraphicsState ,_aaga *_cc .PdfPageResources )error {_cgef :=_dgg .Operand ;
if _bgcgb {_f .Log .Info ("\u0026&\u0026\u0020\u006f\u0070\u003d\u0025s",_dgg );};switch _cgef {case "\u0071":if _bfgf {_f .Log .Info ("\u0063\u0074\u006d\u003d\u0025\u0073",_abf ._badf );};_gec .push (&_edgf );case "\u0051":if !_gec .empty (){_edgf =*_gec .pop ();
};_abf ._badf =_aafe .CTM ;if _bfgf {_f .Log .Info ("\u0063\u0074\u006d\u003d\u0025\u0073",_abf ._badf );};case "\u0042\u0044\u0043":_ccb ,_fbag :=_ea .GetDict (_dgg .Params [1]);if !_fbag {_f .Log .Debug ("\u0045\u0052\u0052O\u0052\u003a\u0020\u0042D\u0043\u0020\u006f\u0070\u003d\u0025\u0073 \u0047\u0065\u0074\u0044\u0069\u0063\u0074\u0020\u0066\u0061\u0069\u006c\u0065\u0064",_dgg );
return _bgc ;};_ffef :=_ccb .Get ("\u004d\u0043\u0049\u0044");if _ffef !=nil {_agca ,_bca :=_ea .GetIntVal (_ffef );if !_bca {_f .Log .Debug ("\u0045R\u0052\u004fR\u003a\u0020\u0042\u0044C\u0020\u006f\u0070=\u0025\u0073\u002e\u0020\u0042\u0061\u0064\u0020\u006eum\u0065\u0072\u0069c\u0061\u006c \u006f\u0062\u006a\u0065\u0063\u0074.\u0020\u006f=\u0025\u0073",_dgg ,_ffef );
};_ccdg =_agca ;}else {_ccdg =-1;};_bbea :=_ccb .Get ("\u0041\u0063\u0074\u0075\u0061\u006c\u0054\u0065\u0078\u0074");if _bbea !=nil {_bcda =_bbea .String ();};case "\u0045\u004d\u0043":_ccdg =-1;_bcda ="";case "\u0042\u0054":if _bcea {_f .Log .Debug ("\u0042\u0054\u0020\u0063\u0061\u006c\u006c\u0065\u0064\u0020\u0077\u0068\u0069\u006c\u0065 \u0069n\u0020\u0061\u0020\u0074\u0065\u0078\u0074\u0020\u006f\u0062\u006a\u0065\u0063\u0074");
_ccdc ._fagg =append (_ccdc ._fagg ,_gaef ._dbge ...);};_bcea =true ;_aeca :=_aafe ;if _dbgg {_aeca =_fb .GraphicsState {};_aeca .CTM =_abf ._badf ;};_aeca .CTM =_efbb .Mult (_aeca .CTM );_gaef =_cbff (_dda ,_aaga ,_aeca ,&_edgf ,&_gec );_abf ._abef =_gaef ;
case "\u0045\u0054":if !_bcea {_f .Log .Debug ("\u0045\u0054\u0020ca\u006c\u006c\u0065\u0064\u0020\u006f\u0075\u0074\u0073i\u0064e\u0020o\u0066 \u0061\u0020\u0074\u0065\u0078\u0074\u0020\u006f\u0062\u006a\u0065\u0063\u0074");};_bcea =false ;_ccdc ._fagg =append (_ccdc ._fagg ,_gaef ._dbge ...);
_gaef .reset ();case "\u0054\u002a":_gaef .nextLine ();case "\u0054\u0064":if _cfbg ,_fec :=_gaef .checkOp (_dgg ,2,true );!_cfbg {_f .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u0065\u0072\u0072\u003d\u0025\u0076",_fec );return _fec ;};_dbe ,_bbg ,_dgdce :=_daaga (_dgg .Params );
if _dgdce !=nil {return _dgdce ;};_gaef .moveText (_dbe ,_bbg );case "\u0054\u0044":if _dbb ,_bdee :=_gaef .checkOp (_dgg ,2,true );!_dbb {_f .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u0065\u0072\u0072\u003d\u0025\u0076",_bdee );return _bdee ;
};_eebe ,_fdcd ,_afg :=_daaga (_dgg .Params );if _afg !=nil {_f .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u0065\u0072\u0072\u003d\u0025\u0076",_afg );return _afg ;};_gaef .moveTextSetLeading (_eebe ,_fdcd );case "\u0054\u006a":if _faff ,_fbbf :=_gaef .checkOp (_dgg ,1,true );
!_faff {_f .Log .Debug ("\u0045\u0052\u0052\u004fR:\u0020\u0054\u006a\u0020\u006f\u0070\u003d\u0025\u0073\u0020\u0065\u0072\u0072\u003d%\u0076",_dgg ,_fbbf );return _fbbf ;};_fdad :=_ea .TraceToDirectObject (_dgg .Params [0]);_effg ,_eebc :=_ea .GetStringBytes (_fdad );
if !_eebc {_f .Log .Debug ("\u0045\u0052R\u004f\u0052\u003a\u0020T\u006a\u0020o\u0070\u003d\u0025\u0073\u0020\u0047\u0065\u0074S\u0074\u0072\u0069\u006e\u0067\u0042\u0079\u0074\u0065\u0073\u0020\u0066a\u0069\u006c\u0065\u0064",_dgg );return _ea .ErrTypeError ;
};return _gaef .showText (_fdad ,_effg ,_ccdg ,_bcda );case "\u0054\u004a":if _bffcb ,_ccbc :=_gaef .checkOp (_dgg ,1,true );!_bffcb {_f .Log .Debug ("\u0045\u0052R\u004f\u0052\u003a \u0054\u004a\u0020\u0065\u0072\u0072\u003d\u0025\u0076",_ccbc );return _ccbc ;
};_dgfc ,_cgcg :=_ea .GetArray (_dgg .Params [0]);if !_cgcg {_f .Log .Debug ("\u0045\u0052\u0052OR\u003a\u0020\u0054\u004a\u0020\u006f\u0070\u003d\u0025s\u0020G\u0065t\u0041r\u0072\u0061\u0079\u0056\u0061\u006c\u0020\u0066\u0061\u0069\u006c\u0065\u0064",_dgg );
return _bgc ;};return _gaef .showTextAdjusted (_dgfc ,_ccdg ,_bcda );case "\u0027":if _cffd ,_aabf :=_gaef .checkOp (_dgg ,1,true );!_cffd {_f .Log .Debug ("\u0045R\u0052O\u0052\u003a\u0020\u0027\u0020\u0065\u0072\u0072\u003d\u0025\u0076",_aabf );return _aabf ;
};_add :=_ea .TraceToDirectObject (_dgg .Params [0]);_bae ,_bgee :=_ea .GetStringBytes (_add );if !_bgee {_f .Log .Debug ("\u0045\u0052RO\u0052\u003a\u0020'\u0020\u006f\u0070\u003d%s \u0047et\u0053\u0074\u0072\u0069\u006e\u0067\u0042yt\u0065\u0073\u0020\u0066\u0061\u0069\u006ce\u0064",_dgg );
return _ea .ErrTypeError ;};_gaef .nextLine ();return _gaef .showText (_add ,_bae ,_ccdg ,_bcda );case "\u0022":if _fadc ,_ecc :=_gaef .checkOp (_dgg ,3,true );!_fadc {_f .Log .Debug ("\u0045R\u0052O\u0052\u003a\u0020\u0022\u0020\u0065\u0072\u0072\u003d\u0025\u0076",_ecc );
return _ecc ;};_bdfa ,_gdf ,_afce :=_daaga (_dgg .Params [:2]);if _afce !=nil {return _afce ;};_cbf :=_ea .TraceToDirectObject (_dgg .Params [2]);_abd ,_fgcc :=_ea .GetStringBytes (_cbf );if !_fgcc {_f .Log .Debug ("\u0045\u0052RO\u0052\u003a\u0020\"\u0020\u006f\u0070\u003d%s \u0047et\u0053\u0074\u0072\u0069\u006e\u0067\u0042yt\u0065\u0073\u0020\u0066\u0061\u0069\u006ce\u0064",_dgg );
return _ea .ErrTypeError ;};_gaef .setCharSpacing (_bdfa );_gaef .setWordSpacing (_gdf );_gaef .nextLine ();return _gaef .showText (_cbf ,_abd ,_ccdg ,_bcda );case "\u0054\u004c":_edcf ,_fbf :=_eadd (_dgg );if _fbf !=nil {_f .Log .Debug ("\u0045\u0052R\u004f\u0052\u003a \u0054\u004c\u0020\u0065\u0072\u0072\u003d\u0025\u0076",_fbf );
return _fbf ;};_gaef .setTextLeading (_edcf );case "\u0054\u0063":_dgc ,_fgfd :=_eadd (_dgg );if _fgfd !=nil {_f .Log .Debug ("\u0045\u0052R\u004f\u0052\u003a \u0054\u0063\u0020\u0065\u0072\u0072\u003d\u0025\u0076",_fgfd );return _fgfd ;};_gaef .setCharSpacing (_dgc );
case "\u0054\u0066":if _baa ,_dbd :=_gaef .checkOp (_dgg ,2,true );!_baa {_f .Log .Debug ("\u0045\u0052R\u004f\u0052\u003a \u0054\u0066\u0020\u0065\u0072\u0072\u003d\u0025\u0076",_dbd );return _dbd ;};_aabe ,_dbea :=_ea .GetNameVal (_dgg .Params [0]);if !_dbea {_f .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a \u0054\u0066\u0020\u006f\u0070\u003d\u0025\u0073\u0020\u0047\u0065\u0074\u004ea\u006d\u0065\u0056\u0061\u006c\u0020\u0066a\u0069\u006c\u0065\u0064",_dgg );
return _ea .ErrTypeError ;};_aeed ,_agcd :=_ea .GetNumberAsFloat (_dgg .Params [1]);if !_dbea {_f .Log .Debug ("\u0045\u0052\u0052O\u0052\u003a\u0020\u0054\u0066\u0020\u006f\u0070\u003d\u0025\u0073\u0020\u0047\u0065\u0074\u0046\u006c\u006f\u0061\u0074\u0056\u0061\u006c\u0020\u0066\u0061\u0069\u006c\u0065d\u002e\u0020\u0065\u0072\u0072\u003d\u0025\u0076",_dgg ,_agcd );
return _agcd ;};_agcd =_gaef .setFont (_aabe ,_aeed );_gaef ._aafd =_df .Is (_agcd ,_ea .ErrNotSupported );if _agcd !=nil &&!_gaef ._aafd {return _agcd ;};case "\u0054\u006d":if _dgdb ,_aacg :=_gaef .checkOp (_dgg ,6,true );!_dgdb {_f .Log .Debug ("\u0045\u0052R\u004f\u0052\u003a \u0054\u006d\u0020\u0065\u0072\u0072\u003d\u0025\u0076",_aacg );
return _aacg ;};_bfad ,_ccbd :=_ea .GetNumbersAsFloat (_dgg .Params );if _ccbd !=nil {_f .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u0065\u0072\u0072\u003d\u0025\u0076",_ccbd );return _ccbd ;};_gaef .setTextMatrix (_bfad );case "\u0054\u0072":if _gace ,_fbdf :=_gaef .checkOp (_dgg ,1,true );
!_gace {_f .Log .Debug ("\u0045\u0052R\u004f\u0052\u003a \u0054\u0072\u0020\u0065\u0072\u0072\u003d\u0025\u0076",_fbdf );return _fbdf ;};_dfbb ,_ccac :=_ea .GetIntVal (_dgg .Params [0]);if !_ccac {_f .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u0054\u0072\u0020\u006f\u0070\u003d\u0025\u0073 \u0047e\u0074\u0049\u006e\u0074\u0056\u0061\u006c\u0020\u0066\u0061\u0069\u006c\u0065\u0064",_dgg );
return _ea .ErrTypeError ;};_gaef .setTextRenderMode (_dfbb );case "\u0054\u0073":if _abg ,_bgd :=_gaef .checkOp (_dgg ,1,true );!_abg {_f .Log .Debug ("\u0045\u0052R\u004f\u0052\u003a \u0054\u0073\u0020\u0065\u0072\u0072\u003d\u0025\u0076",_bgd );return _bgd ;
};_becf ,_cdgcd :=_ea .GetNumberAsFloat (_dgg .Params [0]);if _cdgcd !=nil {_f .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u0065\u0072\u0072\u003d\u0025\u0076",_cdgcd );return _cdgcd ;};_gaef .setTextRise (_becf );case "\u0054\u0077":if _beee ,_faba :=_gaef .checkOp (_dgg ,1,true );
!_beee {_f .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u0065\u0072\u0072\u003d\u0025\u0076",_faba );return _faba ;};_geca ,_deb :=_ea .GetNumberAsFloat (_dgg .Params [0]);if _deb !=nil {_f .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u0065\u0072\u0072\u003d\u0025\u0076",_deb );
return _deb ;};_gaef .setWordSpacing (_geca );case "\u0054\u007a":if _bdaa ,_cda :=_gaef .checkOp (_dgg ,1,true );!_bdaa {_f .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u0065\u0072\u0072\u003d\u0025\u0076",_cda );return _cda ;};_bbaf ,_ffdg :=_ea .GetNumberAsFloat (_dgg .Params [0]);
if _ffdg !=nil {_f .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u0065\u0072\u0072\u003d\u0025\u0076",_ffdg );return _ffdg ;};_gaef .setHorizScaling (_bbaf );case "\u0063\u006d":if !_dbgg {_abf ._badf =_aafe .CTM ;};if _abf ._badf .Singular (){_gagg :=_ce .IdentityMatrix ().Translate (_abf ._badf .Translation ());
_f .Log .Debug ("S\u0069n\u0067\u0075\u006c\u0061\u0072\u0020\u0063\u0074m\u003d\u0025\u0073\u2192%s",_abf ._badf ,_gagg );_abf ._badf =_gagg ;};if _bfgf {_f .Log .Info ("\u0063\u0074\u006d\u003d\u0025\u0073",_abf ._badf );};case "\u006d":if len (_dgg .Params )!=2{_f .Log .Debug ("\u0057\u0041\u0052\u004e\u003a\u0020\u0065\u0072\u0072o\u0072\u0020\u0077\u0068\u0069\u006c\u0065\u0020\u0070\u0072\u006f\u0063\u0065\u0073\u0073\u0069\u006e\u0067\u0020\u0060\u006d\u0060\u0020o\u0070\u0065r\u0061\u0074o\u0072\u003a\u0020\u0025\u0076\u002e\u0020\u004f\u0075\u0074\u0070\u0075\u0074 m\u0061\u0079\u0020\u0062\u0065\u0020\u0069\u006e\u0063o\u0072\u0072\u0065\u0063\u0074\u002e",_edf );
return nil ;};_ebd ,_cgd :=_ea .GetNumbersAsFloat (_dgg .Params );if _cgd !=nil {return _cgd ;};_abf .moveTo (_ebd [0],_ebd [1]);case "\u006c":if len (_dgg .Params )!=2{_f .Log .Debug ("\u0057\u0041\u0052\u004e\u003a\u0020\u0065\u0072\u0072o\u0072\u0020\u0077\u0068\u0069\u006c\u0065\u0020\u0070\u0072\u006f\u0063\u0065\u0073\u0073\u0069\u006e\u0067\u0020\u0060\u006c\u0060\u0020o\u0070\u0065r\u0061\u0074o\u0072\u003a\u0020\u0025\u0076\u002e\u0020\u004f\u0075\u0074\u0070\u0075\u0074 m\u0061\u0079\u0020\u0062\u0065\u0020\u0069\u006e\u0063o\u0072\u0072\u0065\u0063\u0074\u002e",_edf );
return nil ;};_fefc ,_dfbbc :=_ea .GetNumbersAsFloat (_dgg .Params );if _dfbbc !=nil {return _dfbbc ;};_abf .lineTo (_fefc [0],_fefc [1]);case "\u0063":if len (_dgg .Params )!=6{return _edf ;};_gab ,_dbf :=_ea .GetNumbersAsFloat (_dgg .Params );if _dbf !=nil {return _dbf ;
};_f .Log .Debug ("\u0043u\u0062\u0069\u0063\u0020b\u0065\u007a\u0069\u0065\u0072 \u0070a\u0072a\u006d\u0073\u003a\u0020\u0025\u002e\u0032f",_gab );_abf .cubicTo (_gab [0],_gab [1],_gab [2],_gab [3],_gab [4],_gab [5]);case "\u0076","\u0079":if len (_dgg .Params )!=4{return _edf ;
};_feff ,_acbea :=_ea .GetNumbersAsFloat (_dgg .Params );if _acbea !=nil {return _acbea ;};_f .Log .Debug ("\u0043u\u0062\u0069\u0063\u0020b\u0065\u007a\u0069\u0065\u0072 \u0070a\u0072a\u006d\u0073\u003a\u0020\u0025\u002e\u0032f",_feff );_abf .quadraticTo (_feff [0],_feff [1],_feff [2],_feff [3]);
case "\u0068":_abf .closePath ();case "\u0072\u0065":if len (_dgg .Params )!=4{return _edf ;};_fgdb ,_adffd :=_ea .GetNumbersAsFloat (_dgg .Params );if _adffd !=nil {return _adffd ;};_abf .drawRectangle (_fgdb [0],_fgdb [1],_fgdb [2],_fgdb [3]);_abf .closePath ();
case "\u0053":_abf .stroke (&_ccdc ._affb );_abf .clearPath ();case "\u0073":_abf .closePath ();_abf .stroke (&_ccdc ._affb );_abf .clearPath ();case "\u0046":_abf .fill (&_ccdc ._abae );_abf .clearPath ();case "\u0066","\u0066\u002a":_abf .closePath ();
_abf .fill (&_ccdc ._abae );_abf .clearPath ();case "\u0042","\u0042\u002a":_abf .fill (&_ccdc ._abae );_abf .stroke (&_ccdc ._affb );_abf .clearPath ();case "\u0062","\u0062\u002a":_abf .closePath ();_abf .fill (&_ccdc ._abae );_abf .stroke (&_ccdc ._affb );
_abf .clearPath ();case "\u006e":_abf .clearPath ();case "\u0044\u006f":if len (_dgg .Params )==0{_f .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u0065\u0078\u0070\u0065\u0063\u0074\u0065\u0064\u0020\u0058\u004fbj\u0065c\u0074\u0020\u006e\u0061\u006d\u0065\u0020\u006f\u0070\u0065\u0072\u0061n\u0064\u0020\u0066\u006f\u0072\u0020\u0044\u006f\u0020\u006f\u0070\u0065\u0072\u0061\u0074\u006f\u0072.\u0020\u0047\u006f\u0074\u0020\u0025\u002b\u0076\u002e",_dgg .Params );
return _ea .ErrRangeError ;};_agaa ,_dggc :=_ea .GetName (_dgg .Params [0]);if !_dggc {_f .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u0069\u006e\u0076\u0061l\u0069\u0064\u0020\u0044\u006f\u0020\u006f\u0070e\u0072a\u0074\u006f\u0072\u0020\u0058\u004f\u0062\u006a\u0065\u0063\u0074\u0020\u006e\u0061\u006d\u0065\u0020\u006fp\u0065\u0072\u0061\u006e\u0064\u003a\u0020\u0025\u002b\u0076\u002e",_dgg .Params [0]);
return _ea .ErrTypeError ;};_ ,_cbce :=_aaga .GetXObjectByName (*_agaa );if _cbce !=_cc .XObjectTypeForm {break ;};_cbge ,_dggc :=_dda ._cfd [_agaa .String ()];if !_dggc {_aeedc ,_egcc :=_aaga .GetXObjectFormByName (*_agaa );if _egcc !=nil {_f .Log .Debug ("\u0045R\u0052\u004f\u0052\u003a\u0020\u0025v",_egcc );
return _egcc ;};_dde ,_egcc :=_aeedc .GetContentStream ();if _egcc !=nil {_f .Log .Debug ("\u0045R\u0052\u004f\u0052\u003a\u0020\u0025v",_egcc );return _egcc ;};_acfa :=_aeedc .Resources ;if _acfa ==nil {_acfa =_aaga ;};_fccd :=_aafe .CTM ;if _bead ,_egd :=_ea .GetArray (_aeedc .Matrix );
_egd {_ece ,_acaa :=_bead .GetAsFloat64Slice ();if _acaa !=nil {return _acaa ;};if len (_ece )!=6{return _edf ;};_gdfd :=_ce .NewMatrix (_ece [0],_ece [1],_ece [2],_ece [3],_ece [4],_ece [5]);_fccd =_aafe .CTM .Mult (_gdfd );};_aeb ,_fcfa ,_faffc ,_egcc :=_dda .extractPageText (string (_dde ),_acfa ,_efbb .Mult (_fccd ),_fgd +1,false );
if _egcc !=nil {_f .Log .Debug ("\u0045R\u0052\u004f\u0052\u003a\u0020\u0025v",_egcc );return _egcc ;};_cbge =textResult {*_aeb ,_fcfa ,_faffc };_dda ._cfd [_agaa .String ()]=_cbge ;};_abf ._badf =_aafe .CTM ;if _bfgf {_f .Log .Info ("\u0063\u0074\u006d\u003d\u0025\u0073",_abf ._badf );
};_ccdc ._fagg =append (_ccdc ._fagg ,_cbge ._aebg ._fagg ...);_ccdc ._affb =append (_ccdc ._affb ,_cbge ._aebg ._affb ...);_ccdc ._abae =append (_ccdc ._abae ,_cbge ._aebg ._abae ...);_edgf ._fgcd +=_cbge ._bbgc ;_edgf ._baad +=_cbge ._dbgb ;case "\u0072\u0067","\u0067","\u006b","\u0063\u0073","\u0073\u0063","\u0073\u0063\u006e":_gaef ._fbgf .ColorspaceNonStroking =_aafe .ColorspaceNonStroking ;
_gaef ._fbgf .ColorNonStroking =_aafe .ColorNonStroking ;case "\u0052\u0047","\u0047","\u004b","\u0043\u0053","\u0053\u0043","\u0053\u0043\u004e":_gaef ._fbgf .ColorspaceStroking =_aafe .ColorspaceStroking ;_gaef ._fbgf .ColorStroking =_aafe .ColorStroking ;
};return nil ;});_bgc =_aee .Process (_acgbb );if _dda ._acb !=nil &&_dda ._acb .IncludeAnnotations &&!_dbgg {for _ ,_ecfd :=range _dda ._ccf {_eaff ,_gaac :=_ea .GetDict (_ecfd .AP );if !_gaac {continue ;};_fbfc ,_gaac :=_eaff .Get ("\u004e").(*_ea .PdfObjectStream );
if !_gaac {continue ;};_geff ,_dca :=_ea .DecodeStream (_fbfc );if _dca !=nil {_f .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u006f\u006e\u0020\u0064\u0065c\u006f\u0064\u0065\u0020\u0073\u0074\u0072\u0065\u0061\u006d:\u0020\u0025\u0076",_dca );continue ;
};_cgdd :=_fbfc .PdfObjectDictionary .Get ("\u0052e\u0073\u006f\u0075\u0072\u0063\u0065s");_beef ,_dca :=_cc .NewPdfPageResourcesFromDict (_cgdd .(*_ea .PdfObjectDictionary ));if _dca !=nil {_f .Log .Debug ("\u0045\u0072\u0072\u006f\u0072 \u006f\u006e\u0020\u0067\u0065\u0074\u0074\u0069\u006e\u0067\u0020\u0061\u006en\u006f\u0074\u0061\u0074\u0069\u006f\u006e\u0020\u0072\u0065\u0073\u006f\u0075\u0072\u0063\u0065\u0073\u003a\u0020\u0025\u0076",_dca );
continue ;};_ecg :=_ce .IdentityMatrix ();_bdg ,_gaac :=_fbfc .PdfObjectDictionary .Get ("\u004d\u0061\u0074\u0072\u0069\u0078").(*_ea .PdfObjectArray );if _gaac {_efcc ,_cgf :=_bdg .GetAsFloat64Slice ();if _cgf !=nil {_f .Log .Debug ("\u0045\u0072\u0072or\u0020\u006f\u006e\u0020\u0067\u0065\u0074\u0074\u0069n\u0067 \u0066l\u006fa\u0074\u0036\u0034\u0020\u0073\u006c\u0069\u0063\u0065\u003a\u0020\u0025\u0076",_cgf );
continue ;};if len (_efcc )!=6{_f .Log .Debug ("I\u006e\u0076\u0061\u006c\u0069\u0064 \u006d\u0061\u0074\u0072\u0069\u0078\u0020\u0073\u006ci\u0063\u0065\u0020l\u0065n\u0067\u0074\u0068");continue ;};_ecg =_ce .NewMatrix (_efcc [0],_efcc [1],_efcc [2],_efcc [3],_efcc [4],_efcc [5]);
};_gfdf ,_gaac :=_dda ._ggg [_fbfc .String ()];if !_gaac {_faea ,_ccg ,_ddce ,_fee :=_dda .extractPageText (string (_geff ),_beef ,_ecg ,_fgd +1,true );if _fee !=nil {_f .Log .Debug ("\u0045\u0052R\u004f\u0052\u0020\u0065x\u0074\u0072a\u0063\u0074\u0069\u006e\u0067\u0020\u0061\u006en\u006f\u0074\u0061\u0074\u0069\u006f\u006e\u0020\u0074\u0065\u0078\u0074s\u003a\u0020\u0025\u0076",_fee );
continue ;};_gfdf =textResult {*_faea ,_ccg ,_ddce };_dda ._ggg [_fbfc .String ()]=_gfdf ;};_ccdc ._fagg =append (_ccdc ._fagg ,_gfdf ._aebg ._fagg ...);_ccdc ._affb =append (_ccdc ._affb ,_gfdf ._aebg ._affb ...);_ccdc ._abae =append (_ccdc ._abae ,_gfdf ._aebg ._abae ...);
_edgf ._fgcd +=_gfdf ._bbgc ;_edgf ._baad +=_gfdf ._dbgb ;};};return _ccdc ,_edgf ._fgcd ,_edgf ._baad ,_bgc ;};func _bgeda (_eabd _cc .PdfRectangle )textState {return textState {_bbgg :100,_cggbec :RenderModeFill ,_aeef :_eabd };};func (_dgaac *textTable )emptyCompositeColumn (_deab int )bool {for _eagef :=0;
_eagef < _dgaac ._fcec ;_eagef ++{if _gffg ,_ffgee :=_dgaac ._dccb [_dfgde (_deab ,_eagef )];_ffgee {if len (_gffg .paraList )> 0{return false ;};};};return true ;};func (_affgd *textObject )moveTextSetLeading (_gcee ,_adc float64 ){_affgd ._fea ._egdf =-_adc ;
_affgd .moveLP (_gcee ,_adc );};func (_eeed *textObject )setCharSpacing (_ggff float64 ){if _eeed ==nil {return ;};_eeed ._fea ._eegf =_ggff ;if _bcdg {_f .Log .Info ("\u0073\u0065t\u0043\u0068\u0061\u0072\u0053\u0070\u0061\u0063\u0069\u006e\u0067\u003a\u0020\u0025\u002e\u0032\u0066\u0020\u0073\u0074\u0061\u0074e=\u0025\u0073",_ggff ,_eeed ._fea .String ());
};};func _aegf (_eaea *wordBag ,_cfcg int )*textLine {_dfdb :=_eaea .firstWord (_cfcg );_ddgg :=textLine {PdfRectangle :_dfdb .PdfRectangle ,_fcgeg :_dfdb ._dcca ,_gdca :_dfdb ._bfff };_ddgg .pullWord (_eaea ,_dfdb ,_cfcg );return &_ddgg ;};func _bfacfd (_effag []*textWord ,_ccagd int )[]*textWord {_fbafa :=len (_effag );
copy (_effag [_ccagd :],_effag [_ccagd +1:]);return _effag [:_fbafa -1];};func (_bggde rulingList )sortStrict (){_be .Slice (_bggde ,func (_egge ,_ffadf int )bool {_cede ,_caggf :=_bggde [_egge ],_bggde [_ffadf ];_eafeb ,_bfcd :=_cede ._befeb ,_caggf ._befeb ;
if _eafeb !=_bfcd {return _eafeb > _bfcd ;};_gfeb ,_bedf :=_cede ._gdaaf ,_caggf ._gdaaf ;if !_bfee (_gfeb -_bedf ){return _gfeb < _bedf ;};_gfeb ,_bedf =_cede ._bfbdb ,_caggf ._bfbdb ;if _gfeb !=_bedf {return _gfeb < _bedf ;};return _cede ._bedbg < _caggf ._bedbg ;
});};var (_ga =_df .New ("\u0074\u0079p\u0065\u0020\u0063h\u0065\u0063\u006b\u0020\u0065\u0072\u0072\u006f\u0072");_edf =_df .New ("\u0072\u0061\u006e\u0067\u0065\u0020\u0063\u0068\u0065\u0063\u006b\u0020e\u0072\u0072\u006f\u0072"););const (RenderModeStroke RenderMode =1<<iota ;
RenderModeFill ;RenderModeClip ;);type textWord struct{_cc .PdfRectangle ;_bfff float64 ;_ggeag string ;_bacg []*textMark ;_dcca float64 ;_fabaa bool ;};func (_eaab *textObject )getCurrentFont ()*_cc .PdfFont {_fdac :=_eaab ._fea ._dbcb ;if _fdac ==nil {_f .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u004e\u006f\u0020\u0066\u006f\u006e\u0074\u0020\u0064\u0065\u0066\u0069\u006e\u0065\u0064\u002e\u0020U\u0073\u0069\u006e\u0067\u0020d\u0065\u0066a\u0075\u006c\u0074\u002e");
return _cc .DefaultFont ();};return _fdac ;};type textPara struct{_cc .PdfRectangle ;_abfg _cc .PdfRectangle ;_geffa []*textLine ;_dcde *textTable ;_dddg bool ;_gaebg bool ;_afeb *textPara ;_dgadb *textPara ;_eagea *textPara ;_cada *textPara ;_bcbge []list ;
};func _bbgdd (_bgdb float64 )int {var _egea int ;if _bgdb >=0{_egea =int (_bgdb /_fbadd );}else {_egea =int (_bgdb /_fbadd )-1;};return _egea ;};type imageExtractContext struct{_gbg []ImageMark ;_afcd int ;_gbc int ;_ffd int ;_bdb map[*_ea .PdfObjectStream ]*cachedImage ;
_dec *ImageExtractOptions ;_daaa bool ;};func (_dgb *textObject )getFillColor ()_ca .Color {return _eefcg (_dgb ._fbgf .ColorspaceNonStroking ,_dgb ._fbgf .ColorNonStroking );};func (_dgeag rulingList )mergePrimary ()float64 {_dbgba :=_dgeag [0]._gdaaf ;
for _ ,_cgcfd :=range _dgeag [1:]{_dbgba +=_cgcfd ._gdaaf ;};return _dbgba /float64 (len (_dgeag ));};func _eadd (_cfcb *_fb .ContentStreamOperation )(float64 ,error ){if len (_cfcb .Params )!=1{_afcg :=_df .New ("\u0069n\u0063\u006f\u0072\u0072e\u0063\u0074\u0020\u0070\u0061r\u0061m\u0065t\u0065\u0072\u0020\u0063\u006f\u0075\u006et");
_f .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u0025\u0023\u0071\u0020\u0073\u0068\u006f\u0075\u006c\u0064\u0020h\u0061\u0076\u0065\u0020\u0025\u0064\u0020i\u006e\u0070\u0075\u0074\u0020\u0070\u0061\u0072\u0061\u006d\u0073,\u0020\u0067\u006f\u0074\u0020\u0025\u0064\u0020\u0025\u002b\u0076",_cfcb .Operand ,1,len (_cfcb .Params ),_cfcb .Params );
return 0.0,_afcg ;};return _ea .GetNumberAsFloat (_cfcb .Params [0]);};func _dgcc (_gcccf ,_aadf _ce .Point )bool {return _gcccf .X ==_aadf .X &&_gcccf .Y ==_aadf .Y };func (_geb *shapesState )quadraticTo (_effc ,_cfdc ,_gdd ,_bbgb float64 ){if _bfgf {_f .Log .Info ("\u0071\u0075\u0061d\u0072\u0061\u0074\u0069\u0063\u0054\u006f\u003a");
};_geb .addPoint (_gdd ,_bbgb );};func (_ecdf pathSection )bbox ()_cc .PdfRectangle {_ebbb :=_ecdf ._gdeb [0]._cggf [0];_bebg :=_cc .PdfRectangle {Llx :_ebbb .X ,Urx :_ebbb .X ,Lly :_ebbb .Y ,Ury :_ebbb .Y };_caaee :=func (_agbf _ce .Point ){if _agbf .X < _bebg .Llx {_bebg .Llx =_agbf .X ;
}else if _agbf .X > _bebg .Urx {_bebg .Urx =_agbf .X ;};if _agbf .Y < _bebg .Lly {_bebg .Lly =_agbf .Y ;}else if _agbf .Y > _bebg .Ury {_bebg .Ury =_agbf .Y ;};};for _ ,_bceb :=range _ecdf ._gdeb [0]._cggf [1:]{_caaee (_bceb );};for _ ,_fgbb :=range _ecdf ._gdeb [1:]{for _ ,_cefd :=range _fgbb ._cggf {_caaee (_cefd );
};};return _bebg ;};func (_gfbb paraList )reorder (_gdbb []int ){_aecaf :=make (paraList ,len (_gfbb ));for _dcad ,_ccgff :=range _gdbb {_aecaf [_dcad ]=_gfbb [_ccgff ];};copy (_gfbb ,_aecaf );};func _gdce (_abfc _cc .PdfRectangle )*ruling {return &ruling {_befeb :_gfbdf ,_gdaaf :_abfc .Ury ,_bfbdb :_abfc .Llx ,_bedbg :_abfc .Urx };
};type paraList []*textPara ;var _bgcf =_ec .MustCompile ("\u005e\u005c\u0073\u002a\u0028\u005c\u0064\u002b\u005c\u002e\u003f|\u005b\u0049\u0069\u0076\u005d\u002b\u0029\u005c\u0073\u002a\\\u0029\u003f\u0024");type shapesState struct{_badf _ce .Matrix ;
_fbcb _ce .Matrix ;_edde []*subpath ;_ffdgg bool ;_ffefa _ce .Point ;_abef *textObject ;};func (_eafa rulingList )snapToGroups ()rulingList {_caabb ,_dgeed :=_eafa .vertsHorzs ();if len (_caabb )> 0{_caabb =_caabb .snapToGroupsDirection ();};if len (_dgeed )> 0{_dgeed =_dgeed .snapToGroupsDirection ();
};_fcfae :=append (_caabb ,_dgeed ...);_fcfae .log ("\u0073\u006e\u0061p\u0054\u006f\u0047\u0072\u006f\u0075\u0070\u0073");return _fcfae ;};const (_bcf ="\u0045\u0052R\u004f\u0052\u003a\u0020\u0043\u0061\u006e\u0027\u0074\u0020\u0063\u006f\u006e\u0076\u0065\u0072\u0074\u0020\u0066\u006f\u006e\u0074\u0020\u006f\u0062\u006a\u0065\u0063\u0074\u002c\u0020\u0069\u006e\u0076\u0061\u006c\u0069\u0064\u0020\u0074\u0079\u0070\u0065";
_gca ="\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u0043a\u006e\u0027\u0074 g\u0065\u0074\u0020\u0066\u006f\u006et\u0020\u0070\u0072\u006f\u0070\u0065\u0072\u0074\u0069\u0065\u0073\u002c\u0020\u0066\u006fn\u0074\u0020\u006e\u006f\u0074\u0020\u0066\u006fu\u006e\u0064";
_bdff ="\u0045\u0052\u0052O\u0052\u003a\u0020\u0043\u0061\u006e\u0027\u0074\u0020\u0067\u0065\u0074\u0020\u0066\u006f\u006e\u0074\u0020\u0073\u0074\u0072\u0065\u0061\u006d\u002c\u0020\u0069\u006e\u0076a\u006c\u0069\u0064\u0020\u0074\u0079\u0070\u0065";
_faef ="E\u0052R\u004f\u0052\u003a\u0020\u004e\u006f\u0020\u0066o\u006e\u0074\u0020\u0066ou\u006e\u0064";);func (_ebga *textObject )getFont (_aabfb string )(*_cc .PdfFont ,error ){if _ebga ._gccd ._cfb !=nil {_gdcg ,_edge :=_ebga .getFontDict (_aabfb );
if _edge !=nil {_f .Log .Debug ("\u0045\u0052\u0052OR\u003a\u0020\u0067\u0065\u0074\u0046\u006f\u006e\u0074:\u0020n\u0061m\u0065=\u0025\u0073\u002c\u0020\u0065\u0072\u0072\u006f\u0072\u003a\u0020\u0025\u0073",_aabfb ,_edge .Error ());return nil ,_edge ;
};_ebga ._gccd ._gfe ++;_dcb ,_abdd :=_ebga ._gccd ._cfb [_gdcg .String ()];if _abdd {_dcb ._ggea =_ebga ._gccd ._gfe ;return _dcb ._aedf ,nil ;};};_bfdb ,_dcgag :=_ebga .getFontDict (_aabfb );if _dcgag !=nil {return nil ,_dcgag ;};_gaag ,_dcgag :=_ebga .getFontDirect (_aabfb );
if _dcgag !=nil {return nil ,_dcgag ;};if _ebga ._gccd ._cfb !=nil {_aada :=fontEntry {_gaag ,_ebga ._gccd ._gfe };if len (_ebga ._gccd ._cfb )>=_edba {var _bbgd []string ;for _fdag :=range _ebga ._gccd ._cfb {_bbgd =append (_bbgd ,_fdag );};_be .Slice (_bbgd ,func (_acbg ,_ffae int )bool {return _ebga ._gccd ._cfb [_bbgd [_acbg ]]._ggea < _ebga ._gccd ._cfb [_bbgd [_ffae ]]._ggea ;
});delete (_ebga ._gccd ._cfb ,_bbgd [0]);};_ebga ._gccd ._cfb [_bfdb .String ()]=_aada ;};return _gaag ,nil ;};func (_cded intSet )has (_agggg int )bool {_ ,_afag :=_cded [_agggg ];return _afag };

// Text gets the extracted text contained in `l`.
func (_efde *list )Text ()string {_cfbf :=&_ag .Builder {};_ffcef :="";_edbab (_efde ,_cfbf ,&_ffcef );return _cfbf .String ();};func (_fagfd *ruling )alignsSec (_aefba *ruling )bool {const _bbbdg =_accd +1.0;return _fagfd ._bfbdb -_bbbdg <=_aefba ._bedbg &&_aefba ._bfbdb -_bbbdg <=_fagfd ._bedbg ;
};func _bcgc (_eeaf *wordBag ,_daga *textWord ,_baagg float64 )bool {return _eeaf .Urx <=_daga .Llx &&_daga .Llx < _eeaf .Urx +_baagg ;};func (_bfba *textObject )newTextMark (_daff string ,_ccfa _ce .Matrix ,_deec _ce .Point ,_bbdc float64 ,_ebde *_cc .PdfFont ,_efebc float64 ,_geab ,_fgea _ca .Color ,_adde _ea .PdfObject ,_beeff []string ,_cegb int ,_cebfb int )(textMark ,bool ){_bdffd :=_ccfa .Angle ();
_eaeaf :=_cafd (_bdffd ,_fdba );var _eebba float64 ;if _eaeaf %180!=90{_eebba =_ccfa .ScalingFactorY ();}else {_eebba =_ccfa .ScalingFactorX ();};_dadf :=_ffac (_ccfa );_fccbe :=_cc .PdfRectangle {Llx :_dadf .X ,Lly :_dadf .Y ,Urx :_deec .X ,Ury :_deec .Y };
switch _eaeaf %360{case 90:_fccbe .Urx -=_eebba ;case 180:_fccbe .Ury -=_eebba ;case 270:_fccbe .Urx +=_eebba ;case 0:_fccbe .Ury +=_eebba ;default:_eaeaf =0;_fccbe .Ury +=_eebba ;};if _fccbe .Llx > _fccbe .Urx {_fccbe .Llx ,_fccbe .Urx =_fccbe .Urx ,_fccbe .Llx ;
};if _fccbe .Lly > _fccbe .Ury {_fccbe .Lly ,_fccbe .Ury =_fccbe .Ury ,_fccbe .Lly ;};_cbfg :=true ;if _bfba ._gccd ._cgg .Width ()> 0{_fcde ,_bcdgf :=_gfad (_fccbe ,_bfba ._gccd ._cgg );if !_bcdgf {_cbfg =false ;_f .Log .Debug ("\u0054\u0065\u0078\u0074\u0020m\u0061\u0072\u006b\u0020\u006f\u0075\u0074\u0073\u0069\u0064\u0065\u0020\u0070a\u0067\u0065\u002e\u0020\u0062\u0062\u006f\u0078\u003d\u0025\u0067\u0020\u006d\u0065\u0064\u0069\u0061\u0042\u006f\u0078\u003d\u0025\u0067\u0020\u0074\u0065\u0078\u0074\u003d\u0025q",_fccbe ,_bfba ._gccd ._cgg ,_daff );
};_fccbe =_fcde ;};_eccfd :=_fccbe ;_bcbe :=_bfba ._gccd ._cgg ;switch _eaeaf %360{case 90:_bcbe .Urx ,_bcbe .Ury =_bcbe .Ury ,_bcbe .Urx ;_eccfd =_cc .PdfRectangle {Llx :_bcbe .Urx -_fccbe .Ury ,Urx :_bcbe .Urx -_fccbe .Lly ,Lly :_fccbe .Llx ,Ury :_fccbe .Urx };
case 180:_eccfd =_cc .PdfRectangle {Llx :_bcbe .Urx -_fccbe .Llx ,Urx :_bcbe .Urx -_fccbe .Urx ,Lly :_bcbe .Ury -_fccbe .Lly ,Ury :_bcbe .Ury -_fccbe .Ury };case 270:_bcbe .Urx ,_bcbe .Ury =_bcbe .Ury ,_bcbe .Urx ;_eccfd =_cc .PdfRectangle {Llx :_fccbe .Ury ,Urx :_fccbe .Lly ,Lly :_bcbe .Ury -_fccbe .Llx ,Ury :_bcbe .Ury -_fccbe .Urx };
};if _eccfd .Llx > _eccfd .Urx {_eccfd .Llx ,_eccfd .Urx =_eccfd .Urx ,_eccfd .Llx ;};if _eccfd .Lly > _eccfd .Ury {_eccfd .Lly ,_eccfd .Ury =_eccfd .Ury ,_eccfd .Lly ;};_abeb :=textMark {_fecec :_daff ,PdfRectangle :_eccfd ,_ebge :_fccbe ,_dagf :_ebde ,_ggde :_eebba ,_bccbd :_efebc ,_deff :_ccfa ,_ffdfd :_deec ,_ggbe :_eaeaf ,_dbgd :_geab ,_eeeb :_fgea ,_fbce :_adde ,_fedc :_beeff ,Th :_bfba ._fea ._bbgg ,Tw :_bfba ._fea ._eeef ,_adgc :_cebfb ,_gbecb :_cegb };
if _affbb {_f .Log .Info ("n\u0065\u0077\u0054\u0065\u0078\u0074M\u0061\u0072\u006b\u003a\u0020\u0073t\u0061\u0072\u0074\u003d\u0025\u002e\u0032f\u0020\u0065\u006e\u0064\u003d\u0025\u002e\u0032\u0066\u0020%\u0073",_dadf ,_deec ,_abeb .String ());};return _abeb ,_cbfg ;
};func _dbbe (_adce ,_ccbg _cc .PdfRectangle )bool {return _adce .Llx <=_ccbg .Llx &&_ccbg .Urx <=_adce .Urx &&_adce .Lly <=_ccbg .Lly &&_ccbg .Ury <=_adce .Ury ;};func _fedef (_ecbe *textLine ,_gggca []*textLine ,_edgbf []float64 )float64 {var _gcbb float64 =-1;
for _ ,_eac :=range _gggca {if _eac ._gdca > _ecbe ._gdca {if _da .Round (_eac .Llx )>=_da .Round (_ecbe .Llx ){_gcbb =_eac ._gdca ;}else {break ;};};};return _gcbb ;};func _aadgd (_ccag ,_effad _cc .PdfRectangle )bool {return _dgfce (_ccag ,_effad )&&_aagf (_ccag ,_effad );
};

// WriteToFile writes the edited content to `outputPath`.
func (_acgb *Editor )WriteToFile (outputPath string )error {_ffce ,_gefa :=_acgb ._cff .ToWriter (nil );if _gefa !=nil {return _ge .Errorf ("\u0066\u0061\u0069\u006c\u0065\u0064\u0020\u0074\u006f\u0020c\u006f\u006e\u0076\u0065\u0072\u0074\u0020t\u006f\u0020\u0077\u0072\u0069\u0074\u0065\u0072\u0020\u0025\u0076",_gefa );
};_ffce .WriteToFile (outputPath );return nil ;};type lists []*list ;

// String returns a description of `w`.
func (_gcaf *textWord )String ()string {return _ge .Sprintf ("\u0025\u002e2\u0066\u0020\u0025\u0036\u002e\u0032\u0066\u0020\u0066\u006f\u006e\u0074\u0073\u0069\u007a\u0065\u003d\u0025\u002e\u0032\u0066\u0020\"%\u0073\u0022",_gcaf ._bfff ,_gcaf .PdfRectangle ,_gcaf ._dcca ,_gcaf ._ggeag );
};func (_fdb *imageExtractContext )processOperand (_aec *_fb .ContentStreamOperation ,_fgf _fb .GraphicsState ,_aecb *_cc .PdfPageResources )error {if _aec .Operand =="\u0042\u0049"&&len (_aec .Params )==1{_dfb ,_bff :=_aec .Params [0].(*_fb .ContentStreamInlineImage );
if !_bff {return nil ;};if _afb ,_fegb :=_ea .GetBoolVal (_dfb .ImageMask );_fegb {if _afb &&!_fdb ._dec .IncludeInlineStencilMasks {return nil ;};};return _fdb .extractInlineImage (_dfb ,_fgf ,_aecb );}else if _aec .Operand =="\u0044\u006f"&&len (_aec .Params )==1{_gad ,_bb :=_ea .GetName (_aec .Params [0]);
if !_bb {_f .Log .Debug ("E\u0052\u0052\u004f\u0052\u003a\u0020\u0054\u0079\u0070\u0065");return _ga ;};_ ,_acd :=_aecb .GetXObjectByName (*_gad );switch _acd {case _cc .XObjectTypeImage :return _fdb .extractXObjectImage (_gad ,_fgf ,_aecb );case _cc .XObjectTypeForm :return _fdb .extractFormImages (_gad ,_fgf ,_aecb );
};}else if _fdb ._daaa &&(_aec .Operand =="\u0073\u0063\u006e"||_aec .Operand =="\u0053\u0043\u004e")&&len (_aec .Params )==1{_fff ,_bfaa :=_ea .GetName (_aec .Params [0]);if !_bfaa {_f .Log .Debug ("E\u0052\u0052\u004f\u0052\u003a\u0020\u0054\u0079\u0070\u0065");
return _ga ;};_ccc ,_bfaa :=_aecb .GetPatternByName (*_fff );if !_bfaa {_f .Log .Debug ("\u0045R\u0052\u004f\u0052\u003a\u0020\u0050\u0061\u0074\u0074\u0065\u0072n\u0020\u006e\u006f\u0074\u0020\u0066\u006f\u0075\u006e\u0064");return nil ;};if _ccc .IsTiling (){_gfg :=_ccc .GetAsTilingPattern ();
_abb ,_eabfc :=_gfg .GetContentStream ();if _eabfc !=nil {return _eabfc ;};_eabfc =_fdb .extractContentStreamImages (string (_abb ),_gfg .Resources );if _eabfc !=nil {return _eabfc ;};};}else if (_aec .Operand =="\u0063\u0073"||_aec .Operand =="\u0043\u0053")&&len (_aec .Params )>=1{_fdb ._daaa =_aec .Params [0].String ()=="\u0050a\u0074\u0074\u0065\u0072\u006e";
};return nil ;};type rectRuling struct{_bbcga rulingKind ;_fffae markKind ;_ca .Color ;_cc .PdfRectangle ;};func _cgbe (_fafggd ,_egbe float64 )bool {return _da .Abs (_fafggd -_egbe )<=_cefge };func (_acggge *subpath )isQuadrilateral ()bool {if len (_acggge ._cggf )< 4||len (_acggge ._cggf )> 5{return false ;
};if len (_acggge ._cggf )==5{_eaggf :=_acggge ._cggf [0];_dbgga :=_acggge ._cggf [4];if _eaggf .X !=_dbgga .X ||_eaggf .Y !=_dbgga .Y {return false ;};};return true ;};func (_bcff *stateStack )push (_gcag *textState ){_fcfb :=*_gcag ;*_bcff =append (*_bcff ,&_fcfb )};
func (_effeb *textTable )reduceTiling (_bbdb gridTiling ,_aacf float64 )*textTable {_afee :=make ([]int ,0,_effeb ._fcec );_cbdcd :=make ([]int ,0,_effeb ._ebed );_bcbgg :=_bbdb ._gdec ;_agcaa :=_bbdb ._daeaf ;for _ebeag :=0;_ebeag < _effeb ._fcec ;_ebeag ++{_feba :=_ebeag > 0&&_da .Abs (_agcaa [_ebeag -1]-_agcaa [_ebeag ])< _aacf &&_effeb .emptyCompositeRow (_ebeag );
if !_feba {_afee =append (_afee ,_ebeag );};};for _aegbf :=0;_aegbf < _effeb ._ebed ;_aegbf ++{_gaacg :=_aegbf < _effeb ._ebed -1&&_da .Abs (_bcbgg [_aegbf +1]-_bcbgg [_aegbf ])< _aacf &&_effeb .emptyCompositeColumn (_aegbf );if !_gaacg {_cbdcd =append (_cbdcd ,_aegbf );
};};if len (_afee )==_effeb ._fcec &&len (_cbdcd )==_effeb ._ebed {return _effeb ;};_bdfcc :=textTable {_fggcd :_effeb ._fggcd ,_ebed :len (_cbdcd ),_fcec :len (_afee ),_dccb :make (map[uint64 ]compositeCell ,len (_cbdcd )*len (_afee ))};if _ababc {_f .Log .Info ("\u0072\u0065\u0064\u0075c\u0065\u0054\u0069\u006c\u0069\u006e\u0067\u003a\u0020\u0025d\u0078%\u0064\u0020\u002d\u003e\u0020\u0025\u0064x\u0025\u0064",_effeb ._ebed ,_effeb ._fcec ,len (_cbdcd ),len (_afee ));
_f .Log .Info ("\u0072\u0065d\u0075\u0063\u0065d\u0043\u006f\u006c\u0073\u003a\u0020\u0025\u002b\u0076",_cbdcd );_f .Log .Info ("\u0072\u0065d\u0075\u0063\u0065d\u0052\u006f\u0077\u0073\u003a\u0020\u0025\u002b\u0076",_afee );};for _bggf ,_dgafd :=range _afee {for _ggccd ,_gcdc :=range _cbdcd {_bcfdg ,_fdada :=_effeb .getComposite (_gcdc ,_dgafd );
if len (_bcfdg )==0{continue ;};if _ababc {_ge .Printf ("\u0020 \u0025\u0032\u0064\u002c \u0025\u0032\u0064\u0020\u0028%\u0032d\u002c \u0025\u0032\u0064\u0029\u0020\u0025\u0071\n",_ggccd ,_bggf ,_gcdc ,_dgafd ,_bcfdgc (_bcfdg .merge ().text (),50));};_bdfcc .putComposite (_ggccd ,_bggf ,_bcfdg ,_fdada );
};};return &_bdfcc ;};func _ffac (_gcda _ce .Matrix )_ce .Point {_edee ,_gcacb :=_gcda .Translation ();return _ce .Point {X :_edee ,Y :_gcacb };};func (_babd *textObject )moveLP (_ced ,_dbfd float64 ){_babd ._bdc .Concat (_ce .NewMatrix (1,0,0,1,_ced ,_dbfd ));
_babd ._gfdfa =_babd ._bdc ;};func (_gdgcb rulingList )sort (){_be .Slice (_gdgcb ,_gdgcb .comp )};func (_ddgc lineRuling )xMean ()float64 {return 0.5*(_ddgc ._gadd .X +_ddgc ._beac .X )};func (_cgga *shapesState )moveTo (_dbeaa ,_gfgd float64 ){_cgga ._ffdgg =true ;
_cgga ._ffefa =_cgga .devicePoint (_dbeaa ,_gfgd );if _bfgf {_f .Log .Info ("\u006d\u006fv\u0065\u0054\u006f\u003a\u0020\u0025\u002e\u0032\u0066\u002c\u0025\u002e\u0032\u0066\u0020\u0064\u0065\u0076\u0069\u0063\u0065\u003d%.\u0032\u0066",_dbeaa ,_gfgd ,_cgga ._ffefa );
};};func (_dadfa intSet )add (_bceg int ){_dadfa [_bceg ]=struct{}{}};func _ad (_afd int )bool {return (_afd &1)!=0};func (_bdbae paraList )applyTables (_gega []*textTable )paraList {var _gdea paraList ;for _ ,_ggccg :=range _gega {_gdea =append (_gdea ,_ggccg .newTablePara ());
};for _ ,_fddb :=range _bdbae {if _fddb ._dddg {continue ;};_gdea =append (_gdea ,_fddb );};return _gdea ;};var _aceg string ="\u005e\u005b\u0061\u002d\u007a\u0041\u002dZ\u005d\u0028\u005c)\u007c\u005c\u002e)\u007c\u005e[\u005c\u0064\u005d\u002b\u0028\u005c)\u007c\\.\u0029\u007c\u005e\u005c\u0028\u005b\u0061\u002d\u007a\u0041\u002d\u005a\u005d\u005c\u0029\u007c\u005e\u005c\u0028\u005b\u005c\u0064\u005d\u002b\u005c\u0029";
func _aefda (_eaeac []*textMark ,_ggedc _cc .PdfRectangle )string {_f .Log .Trace ("\u006d\u0061\u006b\u0065\u0053i\u006d\u0070\u006c\u0065\u0054\u0065\u0078\u0074\u003a\u0020\u0025\u0064\u0020e\u006c\u0065\u006d\u0065\u006e\u0074\u0073\u0020\u0070\u0061\u0067\u0065\u0053\u0069\u007a\u0065\u003d\u0025\u002e\u0032\u0066",len (_eaeac ),_ggedc );
_dfeaa :="";if len (_eaeac )==0{return _dfeaa ;};_bggd :=_bfcgg (_eaeac ,_ggedc );if len (_bggd )==0{return _dfeaa ;};_gff :=0.0;_feeg :=true ;_bcad :="";for _ ,_afgg :=range _bggd {_aced :=_afgg ._dcca ;if _aced > _cdgab {_aced =_cdgab ;};if (_afgg ._bfff -_gff > _bafg *_aced &&_gff !=0.0)||(_gff -_afgg ._bfff > _aced *10){_deaf :=_afe ([]rune (_bcad ));
_bcad =_deaf ._eec ;_bcad +="\u000a";_dfeaa +=_bcad ;_bcad ="";}else {if !_feeg {_bcad +="\u0020";};};_bcad +=_afgg ._ggeag ;_feeg =false ;_gff =_afgg ._bfff ;};if _bcad !=""{_dcce :=_afe ([]rune (_bcad ));_bcad =_dcce ._eec ;_bcad +="\u000a";_dfeaa +=_bcad ;
};return _dfeaa ;};type ruling struct{_befeb rulingKind ;_ceab markKind ;_ca .Color ;_gdaaf float64 ;_bfbdb float64 ;_bedbg float64 ;_bfdc float64 ;};func (_fcdg gridTile )contains (_cbfc _cc .PdfRectangle )bool {if _fcdg .numBorders ()< 3{return false ;
};if _fcdg ._egeg &&_cbfc .Llx < _fcdg .Llx -_cfceg {return false ;};if _fcdg ._gbfec &&_cbfc .Urx > _fcdg .Urx +_cfceg {return false ;};if _fcdg ._gfgcaf &&_cbfc .Lly < _fcdg .Lly -_cfceg {return false ;};if _fcdg ._fbfd &&_cbfc .Ury > _fcdg .Ury +_cfceg {return false ;
};return true ;};func (_gbef *textObject )showTextAdjusted (_agb *_ea .PdfObjectArray ,_gggf int ,_beaa string )error {_beb :=false ;for _ ,_fbdg :=range _agb .Elements (){switch _fbdg .(type ){case *_ea .PdfObjectFloat ,*_ea .PdfObjectInteger :_caab ,_cdcd :=_ea .GetNumberAsFloat (_fbdg );
if _cdcd !=nil {_f .Log .Debug ("\u0045\u0052\u0052\u004fR\u003a\u0020\u0073\u0068\u006f\u0077\u0054\u0065\u0078t\u0041\u0064\u006a\u0075\u0073\u0074\u0065\u0064\u002e\u0020\u0042\u0061\u0064\u0020\u006e\u0075\u006d\u0065r\u0069\u0063\u0061\u006c\u0020a\u0072\u0067\u002e\u0020\u006f\u003d\u0025\u0073\u0020\u0061\u0072\u0067\u0073\u003d\u0025\u002b\u0076",_fbdg ,_agb );
return _cdcd ;};_bada ,_geeb :=-_caab *0.001*_gbef ._fea ._fcba ,0.0;if _beb {_geeb ,_bada =_bada ,_geeb ;};_aeea :=_aegc (_ce .Point {X :_bada ,Y :_geeb });_gbef ._gfdfa .Concat (_aeea );case *_ea .PdfObjectString :_ecfa :=_ea .TraceToDirectObject (_fbdg );
_eccf ,_ggae :=_ea .GetStringBytes (_ecfa );if !_ggae {_f .Log .Trace ("s\u0068\u006f\u0077\u0054\u0065\u0078\u0074\u0041\u0064j\u0075\u0073\u0074\u0065\u0064\u003a\u0020Ba\u0064\u0020\u0073\u0074r\u0069\u006e\u0067\u0020\u0061\u0072\u0067\u002e\u0020o=\u0025\u0073 \u0061\u0072\u0067\u0073\u003d\u0025\u002b\u0076",_fbdg ,_agb );
return _ea .ErrTypeError ;};_gbef .renderText (_ecfa ,_eccf ,_gggf ,_beaa );default:_f .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u0073\u0068\u006f\u0077\u0054\u0065\u0078\u0074A\u0064\u006a\u0075\u0073\u0074\u0065\u0064\u002e\u0020\u0055\u006e\u0065\u0078p\u0065\u0063\u0074\u0065\u0064\u0020\u0074\u0079\u0070\u0065\u0020\u0028%T\u0029\u0020\u0061\u0072\u0067\u0073\u003d\u0025\u002b\u0076",_fbdg ,_agb );
return _ea .ErrTypeError ;};};return nil ;};func _gccdf (_dbfc []TextMark ,_cdgf *int )[]TextMark {_cefda :=_dbfc [len (_dbfc )-1];_gccca :=[]rune (_cefda .Text );if len (_gccca )==1{_dbfc =_dbfc [:len (_dbfc )-1];_cfbb :=_dbfc [len (_dbfc )-1];*_cdgf =_cfbb .Offset +len (_cfbb .Text );
}else {_bdae :=_ffbb (_cefda .Text );*_cdgf +=len (_bdae )-len (_cefda .Text );_cefda .Text =_bdae ;};return _dbfc ;};func _gfad (_fcfac ,_dfbf _cc .PdfRectangle )(_cc .PdfRectangle ,bool ){if !_aadgd (_fcfac ,_dfbf ){return _cc .PdfRectangle {},false ;
};return _cc .PdfRectangle {Llx :_da .Max (_fcfac .Llx ,_dfbf .Llx ),Urx :_da .Min (_fcfac .Urx ,_dfbf .Urx ),Lly :_da .Max (_fcfac .Lly ,_dfbf .Lly ),Ury :_da .Min (_fcfac .Ury ,_dfbf .Ury )},true ;};func (_adgga *textWord )toTextMarks (_gcafd *int )[]TextMark {var _eeafg []TextMark ;
for _ ,_dfgdb :=range _adgga ._bacg {_eeafg =_fffb (_eeafg ,_gcafd ,_dfgdb .ToTextMark ());};return _eeafg ;};func (_fccf *textTable )reduce ()*textTable {_eeefgg :=make ([]int ,0,_fccf ._fcec );_fcbc :=make ([]int ,0,_fccf ._ebed );for _afbbd :=0;_afbbd < _fccf ._fcec ;
_afbbd ++{if !_fccf .emptyCompositeRow (_afbbd ){_eeefgg =append (_eeefgg ,_afbbd );};};for _dbab :=0;_dbab < _fccf ._ebed ;_dbab ++{if !_fccf .emptyCompositeColumn (_dbab ){_fcbc =append (_fcbc ,_dbab );};};if len (_eeefgg )==_fccf ._fcec &&len (_fcbc )==_fccf ._ebed {return _fccf ;
};_dcab :=textTable {_fggcd :_fccf ._fggcd ,_ebed :len (_fcbc ),_fcec :len (_eeefgg ),_dcdb :make (map[uint64 ]*textPara ,len (_fcbc )*len (_eeefgg ))};if _ababc {_f .Log .Info ("\u0072\u0065\u0064\u0075ce\u003a\u0020\u0025\u0064\u0078\u0025\u0064\u0020\u002d\u003e\u0020\u0025\u0064\u0078%\u0064",_fccf ._ebed ,_fccf ._fcec ,len (_fcbc ),len (_eeefgg ));
_f .Log .Info ("\u0072\u0065d\u0075\u0063\u0065d\u0043\u006f\u006c\u0073\u003a\u0020\u0025\u002b\u0076",_fcbc );_f .Log .Info ("\u0072\u0065d\u0075\u0063\u0065d\u0052\u006f\u0077\u0073\u003a\u0020\u0025\u002b\u0076",_eeefgg );};for _cefeg ,_dgfe :=range _eeefgg {for _aged ,_fdgb :=range _fcbc {_fbecb ,_cdab :=_fccf .getComposite (_fdgb ,_dgfe );
if _fbecb ==nil {continue ;};if _ababc {_ge .Printf ("\u0020 \u0025\u0032\u0064\u002c \u0025\u0032\u0064\u0020\u0028%\u0032d\u002c \u0025\u0032\u0064\u0029\u0020\u0025\u0071\n",_aged ,_cefeg ,_fdgb ,_dgfe ,_bcfdgc (_fbecb .merge ().text (),50));};_dcab .putComposite (_aged ,_cefeg ,_fbecb ,_cdab );
};};return &_dcab ;};func (_caaca *textPara )writeText (_dcgd _b .Writer ){if _caaca ._dcde ==nil {_caaca .writeCellText (_dcgd );return ;};for _ebcgb :=0;_ebcgb < _caaca ._dcde ._fcec ;_ebcgb ++{for _bccc :=0;_bccc < _caaca ._dcde ._ebed ;_bccc ++{_aggbg :=_caaca ._dcde .get (_bccc ,_ebcgb );
if _aggbg ==nil {_dcgd .Write ([]byte ("\u0009"));}else {_efgeb (_aggbg ._geffa );_aggbg .writeCellText (_dcgd );};_dcgd .Write ([]byte ("\u0020"));};if _ebcgb < _caaca ._dcde ._fcec -1{_dcgd .Write ([]byte ("\u000a"));};};};var _gef =[]string {"\u0042\u004e","\u0042\u004e","\u0042\u004e","\u0042\u004e","\u0042\u004e","\u0042\u004e","\u0042\u004e","\u0042\u004e","\u0042\u004e","\u0053","\u0042","\u0053","\u0057\u0053","\u0042","\u0042\u004e","\u0042\u004e","\u0042\u004e","\u0042\u004e","\u0042\u004e","\u0042\u004e","\u0042\u004e","\u0042\u004e","\u0042\u004e","\u0042\u004e","\u0042\u004e","\u0042\u004e","\u0042\u004e","\u0042\u004e","\u0042","\u0042","\u0042","\u0053","\u0057\u0053","\u004f\u004e","\u004f\u004e","\u0045\u0054","\u0045\u0054","\u0045\u0054","\u004f\u004e","\u004f\u004e","\u004f\u004e","\u004f\u004e","\u004f\u004e","\u0045\u0053","\u0043\u0053","\u0045\u0053","\u0043\u0053","\u0043\u0053","\u0045\u004e","\u0045\u004e","\u0045\u004e","\u0045\u004e","\u0045\u004e","\u0045\u004e","\u0045\u004e","\u0045\u004e","\u0045\u004e","\u0045\u004e","\u0043\u0053","\u004f\u004e","\u004f\u004e","\u004f\u004e","\u004f\u004e","\u004f\u004e","\u004f\u004e","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004f\u004e","\u004f\u004e","\u004f\u004e","\u004f\u004e","\u004f\u004e","\u004f\u004e","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004f\u004e","\u004f\u004e","\u004f\u004e","\u004f\u004e","\u0042\u004e","\u0042\u004e","\u0042\u004e","\u0042\u004e","\u0042\u004e","\u0042\u004e","\u0042","\u0042\u004e","\u0042\u004e","\u0042\u004e","\u0042\u004e","\u0042\u004e","\u0042\u004e","\u0042\u004e","\u0042\u004e","\u0042\u004e","\u0042\u004e","\u0042\u004e","\u0042\u004e","\u0042\u004e","\u0042\u004e","\u0042\u004e","\u0042\u004e","\u0042\u004e","\u0042\u004e","\u0042\u004e","\u0042\u004e","\u0042\u004e","\u0042\u004e","\u0042\u004e","\u0042\u004e","\u0042\u004e","\u0042\u004e","\u0043\u0053","\u004f\u004e","\u0045\u0054","\u0045\u0054","\u0045\u0054","\u0045\u0054","\u004f\u004e","\u004f\u004e","\u004f\u004e","\u004f\u004e","\u004c","\u004f\u004e","\u004f\u004e","\u0042\u004e","\u004f\u004e","\u004f\u004e","\u0045\u0054","\u0045\u0054","\u0045\u004e","\u0045\u004e","\u004f\u004e","\u004c","\u004f\u004e","\u004f\u004e","\u004f\u004e","\u0045\u004e","\u004c","\u004f\u004e","\u004f\u004e","\u004f\u004e","\u004f\u004e","\u004f\u004e","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004f\u004e","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004f\u004e","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c","\u004c"};


// Marks returns the TextMark collection for a page. It represents all the text on the page.
func (_eafe PageText )Marks ()*TextMarkArray {return &TextMarkArray {_aecad :_eafe ._baag }};func (_bcfb *textObject )setFont (_bfdg string ,_gedd float64 )error {if _bcfb ==nil {return nil ;};_bcfb ._fea ._fcba =_gedd ;_aabd ,_gfgg :=_bcfb .getFont (_bfdg );
if _gfgg !=nil {return _gfgg ;};_bcfb ._fea ._dbcb =_aabd ;return nil ;};

// TextTable represents a table.
// Cells are ordered top-to-bottom, left-to-right.
// Cells[y] is the (0-offset) y'th row in the table.
// Cells[y][x] is the (0-offset) x'th column in the table.
type TextTable struct{_cc .PdfRectangle ;W ,H int ;Cells [][]TableCell ;};func (_gbcea *textTable )newTablePara ()*textPara {_cbdd :=_gbcea .computeBbox ();_bceffd :=&textPara {PdfRectangle :_cbdd ,_abfg :_cbdd ,_dcde :_gbcea };if _ababc {_f .Log .Info ("\u006e\u0065w\u0054\u0061\u0062l\u0065\u0050\u0061\u0072\u0061\u003a\u0020\u0025\u0073",_bceffd );
};return _bceffd ;};func (_decb paraList )readBefore (_cgbcd []int ,_ggdcf ,_gdfe int )bool {_fdfe ,_bbcc :=_decb [_ggdcf ],_decb [_gdfe ];if _bcbg (_fdfe ,_bbcc )&&_fdfe .Lly > _bbcc .Lly {return true ;};if !(_fdfe ._abfg .Urx < _bbcc ._abfg .Llx ){return false ;
};_eeeg ,_cgda :=_fdfe .Lly ,_bbcc .Lly ;if _eeeg > _cgda {_cgda ,_eeeg =_eeeg ,_cgda ;};_bbgba :=_da .Max (_fdfe ._abfg .Llx ,_bbcc ._abfg .Llx );_cead :=_da .Min (_fdfe ._abfg .Urx ,_bbcc ._abfg .Urx );_gceee :=_decb .llyRange (_cgbcd ,_eeeg ,_cgda );
for _ ,_gcbd :=range _gceee {if _gcbd ==_ggdcf ||_gcbd ==_gdfe {continue ;};_gceeb :=_decb [_gcbd ];if _gceeb ._abfg .Llx <=_cead &&_bbgba <=_gceeb ._abfg .Urx {return false ;};};return true ;};func (_ecga lineRuling )yMean ()float64 {return 0.5*(_ecga ._gadd .Y +_ecga ._beac .Y )};
type gridTiling struct{_cc .PdfRectangle ;_gdec []float64 ;_daeaf []float64 ;_bceeb map[float64 ]map[float64 ]gridTile ;};func (_edgfd rulingList )augmentGrid ()(rulingList ,rulingList ){_ecgaf ,_bcbf :=_edgfd .vertsHorzs ();if len (_ecgaf )==0||len (_bcbf )==0{return _ecgaf ,_bcbf ;
};_ecfdd ,_dgbf :=_ecgaf ,_bcbf ;_bfcba :=_ecgaf .bbox ();_gcgg :=_bcbf .bbox ();if _bggcg {_f .Log .Info ("\u0061u\u0067\u006d\u0065\u006e\u0074\u0047\u0072\u0069\u0064\u003a\u0020b\u0062\u006f\u0078\u0056\u003d\u0025\u0036\u002e\u0032\u0066",_bfcba );
_f .Log .Info ("\u0061u\u0067\u006d\u0065\u006e\u0074\u0047\u0072\u0069\u0064\u003a\u0020b\u0062\u006f\u0078\u0048\u003d\u0025\u0036\u002e\u0032\u0066",_gcgg );};var _faag ,_adda ,_efee ,_acagc *ruling ;if _gcgg .Llx < _bfcba .Llx -_cefge {_faag =&ruling {_ceab :_ecdaa ,_befeb :_degg ,_gdaaf :_gcgg .Llx ,_bfbdb :_bfcba .Lly ,_bedbg :_bfcba .Ury };
_ecgaf =append (rulingList {_faag },_ecgaf ...);};if _gcgg .Urx > _bfcba .Urx +_cefge {_adda =&ruling {_ceab :_ecdaa ,_befeb :_degg ,_gdaaf :_gcgg .Urx ,_bfbdb :_bfcba .Lly ,_bedbg :_bfcba .Ury };_ecgaf =append (_ecgaf ,_adda );};if _bfcba .Lly < _gcgg .Lly -_cefge {_efee =&ruling {_ceab :_ecdaa ,_befeb :_gfbdf ,_gdaaf :_bfcba .Lly ,_bfbdb :_gcgg .Llx ,_bedbg :_gcgg .Urx };
_bcbf =append (rulingList {_efee },_bcbf ...);};if _bfcba .Ury > _gcgg .Ury +_cefge {_acagc =&ruling {_ceab :_ecdaa ,_befeb :_gfbdf ,_gdaaf :_bfcba .Ury ,_bfbdb :_gcgg .Llx ,_bedbg :_gcgg .Urx };_bcbf =append (_bcbf ,_acagc );};if len (_ecgaf )+len (_bcbf )==len (_edgfd ){return _ecfdd ,_dgbf ;
};_caagc :=append (_ecgaf ,_bcbf ...);_edgfd .log ("u\u006e\u0061\u0075\u0067\u006d\u0065\u006e\u0074\u0065\u0064");_caagc .log ("\u0061u\u0067\u006d\u0065\u006e\u0074\u0065d");return _ecgaf ,_bcbf ;};type markKind int ;func _cdce (_adcbe float64 )bool {return _da .Abs (_adcbe )< _accd };
func (_ecge *wordBag )removeWord (_dbdd *textWord ,_ecdc int ){_bfec :=_ecge ._fggf [_ecdc ];_bfec =_cgbce (_bfec ,_dbdd );if len (_bfec )==0{delete (_ecge ._fggf ,_ecdc );}else {_ecge ._fggf [_ecdc ]=_bfec ;};};func _aggdf (_gcecb ,_ggfd float64 )bool {return _gcecb /_da .Max (_aeeda ,_ggfd )< _ebdc };
func _ceee (_aeedaa string )bool {if _fa .RuneCountInString (_aeedaa )< _bebeb {return false ;};_afga ,_agbe :=_fa .DecodeLastRuneInString (_aeedaa );if _agbe <=0||!_e .Is (_e .Hyphen ,_afga ){return false ;};_afga ,_agbe =_fa .DecodeLastRuneInString (_aeedaa [:len (_aeedaa )-_agbe ]);
return _agbe > 0&&!_e .IsSpace (_afga );};

// String returns a string describing `tm`.
func (_efd TextMark )String ()string {_caac :=_efd .BBox ;var _gged string ;if _efd .Font !=nil {_gged =_efd .Font .String ();if len (_gged )> 50{_gged =_gged [:50]+"\u002e\u002e\u002e";};};var _ffad string ;if _efd .Meta {_ffad ="\u0020\u002a\u004d\u002a";
};return _ge .Sprintf ("\u007b\u0054\u0065\u0078t\u004d\u0061\u0072\u006b\u003a\u0020\u0025\u0064\u0020%\u0071\u003d\u0025\u0030\u0032\u0078\u0020\u0028\u0025\u0036\u002e\u0032\u0066\u002c\u0020\u0025\u0036\u002e2\u0066\u0029\u0020\u0028\u00256\u002e\u0032\u0066\u002c\u0020\u0025\u0036\u002e\u0032\u0066\u0029\u0020\u0025\u0073\u0025\u0073\u007d",_efd .Offset ,_efd .Text ,[]rune (_efd .Text ),_caac .Llx ,_caac .Lly ,_caac .Urx ,_caac .Ury ,_gged ,_ffad );
};func (_bdfc *wordBag )firstWord (_ebfe int )*textWord {return _bdfc ._fggf [_ebfe ][0]};

// Options extractor options.
type Options struct{

// DisableDocumentTags specifies whether to use the document tags during list extraction.
DisableDocumentTags bool ;

// ApplyCropBox will extract page text based on page cropbox if set to `true`.
ApplyCropBox bool ;

// UseSimplerExtractionProcess will skip topological text ordering and table processing.
//
// NOTE: While normally the extra processing is beneficial, it can also lead to problems when it does not work.
// Thus it is a flag to allow the user to control this process.
//
// Skipping some extraction processes would also lead to the reduced processing time.
UseSimplerExtractionProcess bool ;

// IncludeAnnotations specifies whether to include annotations in the extraction process, default value is `false`.
IncludeAnnotations bool ;

// If enabled, this option prevents splitting of one line into several lines.
DisableMultiColumns bool ;

// RelaxedMode specifies whether to use relaxed mode for processing the objects,
// If enabled UniPDF will automatically try to fix invalid parameters length and value.
// Default is `false`.
RelaxedMode bool ;};

// String returns a human readable description of `vecs`.
func (_agbg rulingList )String ()string {if len (_agbg )==0{return "\u007b \u0045\u004d\u0050\u0054\u0059\u0020}";};_gdfba ,_fdgdg :=_agbg .vertsHorzs ();_cadfd :=len (_gdfba );_edcfe :=len (_fdgdg );if _cadfd ==0||_edcfe ==0{return _ge .Sprintf ("\u007b%\u0064\u0020\u0078\u0020\u0025\u0064}",_cadfd ,_edcfe );
};_bgeead :=_cc .PdfRectangle {Llx :_gdfba [0]._gdaaf ,Urx :_gdfba [_cadfd -1]._gdaaf ,Lly :_fdgdg [_edcfe -1]._gdaaf ,Ury :_fdgdg [0]._gdaaf };return _ge .Sprintf ("\u007b\u0025d\u0020\u0078\u0020%\u0064\u003a\u0020\u0025\u0036\u002e\u0032\u0066\u007d",_cadfd ,_edcfe ,_bgeead );
};