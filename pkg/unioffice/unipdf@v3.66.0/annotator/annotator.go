//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

// Package annotator provides an interface for creating annotations with appearance
// streams.  It goes beyond the models package which includes definitions of basic annotation models, in that it
// can create the appearance streams which specify the exact appearance as needed by many pdf viewers for consistent
// appearance of the annotations.
// It also contains methods for generating appearance streams for fields via widget annotations.
package annotator ;import (_fa "bytes";_ceb "errors";_fg "fmt";_f "github.com/unidoc/unipdf/v3/common";_c "github.com/unidoc/unipdf/v3/contentstream";_ba "github.com/unidoc/unipdf/v3/contentstream/draw";_cd "github.com/unidoc/unipdf/v3/core";_d "github.com/unidoc/unipdf/v3/creator";
_dg "github.com/unidoc/unipdf/v3/internal/textencoding";_ceg "github.com/unidoc/unipdf/v3/model";_g "image";_fc "math";_ce "strings";_e "time";_b "unicode";);

// NewSignatureField returns a new signature field with a visible appearance
// containing the specified signature lines and styled according to the
// specified options.
func NewSignatureField (signature *_ceg .PdfSignature ,lines []*SignatureLine ,opts *SignatureFieldOpts )(*_ceg .PdfFieldSignature ,error ){if signature ==nil {return nil ,_ceb .New ("\u0073\u0069\u0067na\u0074\u0075\u0072\u0065\u0020\u0063\u0061\u006e\u006e\u006f\u0074\u0020\u0062\u0065\u0020\u006e\u0069\u006c");
};_fgdea ,_cbgg :=_affe (lines ,opts );if _cbgg !=nil {return nil ,_cbgg ;};_efbgc :=_ceg .NewPdfFieldSignature (signature );_efbgc .Rect =_cd .MakeArrayFromFloats (opts .Rect );_efbgc .AP =_fgdea ;return _efbgc ,nil ;};

// CheckboxFieldOptions defines optional parameters for a checkbox field a form.
type CheckboxFieldOptions struct{Checked bool ;};

// NewCheckboxField generates a new checkbox field with partial name `name` at location `rect`
// on specified `page` and with field specific options `opt`.
func NewCheckboxField (page *_ceg .PdfPage ,name string ,rect []float64 ,opt CheckboxFieldOptions )(*_ceg .PdfFieldButton ,error ){if page ==nil {return nil ,_ceb .New ("\u0070a\u0067e\u0020\u006e\u006f\u0074\u0020s\u0070\u0065c\u0069\u0066\u0069\u0065\u0064");
};if len (name )<=0{return nil ,_ceb .New ("\u0072\u0065\u0071\u0075\u0069\u0072\u0065\u0064\u0020\u0061\u0074\u0074\u0072\u0069\u0062u\u0074e\u0020\u006e\u006f\u0074\u0020\u0073\u0070\u0065\u0063\u0069\u0066\u0069\u0065\u0064");};if len (rect )!=4{return nil ,_ceb .New ("\u0069\u006e\u0076\u0061\u006c\u0069\u0064\u0020\u0072\u0061\u006e\u0067\u0065");
};_eaf ,_eda :=_ceg .NewStandard14Font (_ceg .ZapfDingbatsName );if _eda !=nil {return nil ,_eda ;};_ebec :=_ceg .NewPdfField ();_fgae :=&_ceg .PdfFieldButton {};_ebec .SetContext (_fgae );_fgae .PdfField =_ebec ;_fgae .T =_cd .MakeString (name );_fgae .SetType (_ceg .ButtonTypeCheckbox );
_bfge :="\u004f\u0066\u0066";if opt .Checked {_bfge ="\u0059\u0065\u0073";};_fgae .V =_cd .MakeName (_bfge );_dafg :=_ceg .NewPdfAnnotationWidget ();_dafg .Rect =_cd .MakeArrayFromFloats (rect );_dafg .P =page .ToPdfObject ();_dafg .F =_cd .MakeInteger (4);
_dafg .Parent =_fgae .ToPdfObject ();_gec :=rect [2]-rect [0];_cddbg :=rect [3]-rect [1];var _fdb _fa .Buffer ;_fdb .WriteString ("\u0071\u000a");_fdb .WriteString ("\u0030 \u0030\u0020\u0031\u0020\u0072\u0067\n");_fdb .WriteString ("\u0042\u0054\u000a");
_fdb .WriteString ("\u002f\u005a\u0061D\u0062\u0020\u0031\u0032\u0020\u0054\u0066\u000a");_fdb .WriteString ("\u0045\u0054\u000a");_fdb .WriteString ("\u0051\u000a");_eaa :=_c .NewContentCreator ();_eaa .Add_q ();_eaa .Add_rg (0,0,1);_eaa .Add_BT ();_eaa .Add_Tf (*_cd .MakeName ("\u005a\u0061\u0044\u0062"),12);
_eaa .Add_Td (0,0);_eaa .Add_ET ();_eaa .Add_Q ();_fgde :=_ceg .NewXObjectForm ();_fgde .SetContentStream (_eaa .Bytes (),_cd .NewRawEncoder ());_fgde .BBox =_cd .MakeArrayFromFloats ([]float64 {0,0,_gec ,_cddbg });_fgde .Resources =_ceg .NewPdfPageResources ();
_fgde .Resources .SetFontByName ("\u005a\u0061\u0044\u0062",_eaf .ToPdfObject ());_eaa =_c .NewContentCreator ();_eaa .Add_q ();_eaa .Add_re (0,0,_gec ,_cddbg );_eaa .Add_W ().Add_n ();_eaa .Add_rg (0,0,1);_eaa .Translate (0,3.0);_eaa .Add_BT ();_eaa .Add_Tf (*_cd .MakeName ("\u005a\u0061\u0044\u0062"),12);
_eaa .Add_Td (0,0);_eaa .Add_Tj (*_cd .MakeString ("\u0034"));_eaa .Add_ET ();_eaa .Add_Q ();_faad :=_ceg .NewXObjectForm ();_faad .SetContentStream (_eaa .Bytes (),_cd .NewRawEncoder ());_faad .BBox =_cd .MakeArrayFromFloats ([]float64 {0,0,_gec ,_cddbg });
_faad .Resources =_ceg .NewPdfPageResources ();_faad .Resources .SetFontByName ("\u005a\u0061\u0044\u0062",_eaf .ToPdfObject ());_fbec :=_cd .MakeDict ();_fbec .Set ("\u004f\u0066\u0066",_fgde .ToPdfObject ());_fbec .Set ("\u0059\u0065\u0073",_faad .ToPdfObject ());
_gebg :=_cd .MakeDict ();_gebg .Set ("\u004e",_fbec );_dafg .AP =_gebg ;_dafg .AS =_cd .MakeName (_bfge );_fgae .Annotations =append (_fgae .Annotations ,_dafg );return _fgae ,nil ;};

// CreateInkAnnotation creates an ink annotation object that can be added to the annotation list of a PDF page.
func CreateInkAnnotation (inkDef InkAnnotationDef )(*_ceg .PdfAnnotation ,error ){_aae :=_ceg .NewPdfAnnotationInk ();_egdb :=_cd .MakeArray ();for _ ,_dggc :=range inkDef .Paths {if _dggc .Length ()==0{continue ;};_gfad :=[]float64 {};for _ ,_fabc :=range _dggc .Points {_gfad =append (_gfad ,_fabc .X ,_fabc .Y );
};_egdb .Append (_cd .MakeArrayFromFloats (_gfad ));};_aae .InkList =_egdb ;if inkDef .Color ==nil {inkDef .Color =_ceg .NewPdfColorDeviceRGB (0.0,0.0,0.0);};_aae .C =_cd .MakeArrayFromFloats ([]float64 {inkDef .Color .R (),inkDef .Color .G (),inkDef .Color .B ()});
_cddf ,_dfb ,_gegb :=_fcbb (&inkDef );if _gegb !=nil {return nil ,_gegb ;};_aae .AP =_cddf ;_aae .Rect =_cd .MakeArrayFromFloats ([]float64 {_dfb .Llx ,_dfb .Lly ,_dfb .Urx ,_dfb .Ury });return _aae .PdfAnnotation ,nil ;};func _ccdb (_dagb [][]_ba .CubicBezierCurve ,_fea *_ceg .PdfColorDeviceRGB ,_eeaf float64 )([]byte ,*_ceg .PdfRectangle ,error ){_gbfe :=_c .NewContentCreator ();
_gbfe .Add_q ().SetStrokingColor (_fea ).Add_w (_eeaf );_gced :=_ba .NewCubicBezierPath ();for _ ,_ecafe :=range _dagb {_gced .Curves =append (_gced .Curves ,_ecafe ...);for _defb ,_fgbd :=range _ecafe {if _defb ==0{_gbfe .Add_m (_fgbd .P0 .X ,_fgbd .P0 .Y );
}else {_gbfe .Add_l (_fgbd .P0 .X ,_fgbd .P0 .Y );};_gbfe .Add_c (_fgbd .P1 .X ,_fgbd .P1 .Y ,_fgbd .P2 .X ,_fgbd .P2 .Y ,_fgbd .P3 .X ,_fgbd .P3 .Y );};};_gbfe .Add_S ().Add_Q ();return _gbfe .Bytes (),_gced .GetBoundingBox ().ToPdfRectangle (),nil ;};


// AppearanceStyle defines style parameters for appearance stream generation.
type AppearanceStyle struct{

// How much of Rect height to fill when autosizing text.
AutoFontSizeFraction float64 ;

// CheckmarkRune is a rune used for check mark in checkboxes (for ZapfDingbats font).
CheckmarkRune rune ;BorderSize float64 ;BorderColor _ceg .PdfColor ;FillColor _ceg .PdfColor ;

// Multiplier for lineheight for multi line text.
MultilineLineHeight float64 ;MultilineVAlignMiddle bool ;

// Visual guide checking alignment of field contents (debugging).
DrawAlignmentReticle bool ;

// Allow field MK appearance characteristics to override style settings.
AllowMK bool ;

// Fonts holds appearance styles for fonts.
Fonts *AppearanceFontStyle ;

// MarginLeft represents the amount of space to leave on the left side of
// the form field bounding box when generating appearances (default: 2.0).
MarginLeft *float64 ;TextColor _ceg .PdfColor ;

// FieldColors holds separate set of appearance styles for fields.
FieldColors map[string ]_ceg .PdfColor ;};type quadding int ;func _eab (_gba *_ceg .PdfAcroForm ,_afae *_ceg .PdfAnnotationWidget ,_fbd *_ceg .PdfFieldChoice ,_dadf AppearanceStyle )(*_cd .PdfObjectDictionary ,error ){_dfab ,_eefc :=_cd .GetArray (_afae .Rect );
if !_eefc {return nil ,_ceb .New ("\u0069\u006e\u0076a\u006c\u0069\u0064\u0020\u0052\u0065\u0063\u0074");};_bcgb ,_gggg :=_ceg .NewPdfRectangle (*_dfab );if _gggg !=nil {return nil ,_gggg ;};_aff ,_ecad :=_bcgb .Width (),_bcgb .Height ();_f .Log .Debug ("\u0043\u0068\u006f\u0069\u0063\u0065\u002c\u0020\u0077\u0061\u0020\u0042S\u003a\u0020\u0025\u0076",_afae .BS );
_fbe ,_gggg :=_c .NewContentStreamParser (_cccf (_fbd .PdfField )).Parse ();if _gggg !=nil {return nil ,_gggg ;};_fgge ,_bafe :=_cd .GetDict (_afae .MK );if _bafe {_dage ,_ :=_cd .GetDict (_afae .BS );_beaa :=_dadf .applyAppearanceCharacteristics (_fgge ,_dage ,nil );
if _beaa !=nil {return nil ,_beaa ;};};_bada :=_cd .MakeDict ();for _ ,_dgg :=range _fbd .Opt .Elements (){if _dcdb ,_fgd :=_cd .GetArray (_dgg );_fgd &&_dcdb .Len ()==2{_dgg =_dcdb .Get (1);};var _deg string ;if _dgage ,_ace :=_cd .GetString (_dgg );_ace {_deg =_dgage .Decoded ();
}else if _bda ,_bcgg :=_cd .GetName (_dgg );_bcgg {_deg =_bda .String ();}else {_f .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a \u004f\u0070\u0074\u0020\u006e\u006f\u0074\u0020\u0061\u0020\u006e\u0061\u006de\u002f\u0073\u0074\u0072\u0069\u006e\u0067 \u002d\u0020\u0025\u0054",_dgg );
return nil ,_ceb .New ("\u006e\u006f\u0074\u0020\u0061\u0020\u006e\u0061\u006d\u0065\u002f\u0073t\u0072\u0069\u006e\u0067");};if len (_deg )> 0{_afbf ,_dfca :=_ggb (_fbd .PdfField ,_aff ,_ecad ,_deg ,_dadf ,_fbe ,_gba .DR ,_fgge );if _dfca !=nil {return nil ,_dfca ;
};_bada .Set (*_cd .MakeName (_deg ),_afbf .ToPdfObject ());};};_badag :=_cd .MakeDict ();_badag .Set ("\u004e",_bada );return _badag ,nil ;};

// NewImageField generates a new image field with partial name `name` at location `rect`
// on specified `page` and with field specific options `opt`.
func NewImageField (page *_ceg .PdfPage ,name string ,rect []float64 ,opt ImageFieldOptions )(*_ceg .PdfFieldButton ,error ){if page ==nil {return nil ,_ceb .New ("\u0070a\u0067e\u0020\u006e\u006f\u0074\u0020s\u0070\u0065c\u0069\u0066\u0069\u0065\u0064");
};if len (name )<=0{return nil ,_ceb .New ("\u0072\u0065\u0071\u0075\u0069\u0072\u0065\u0064\u0020\u0061\u0074\u0074\u0072\u0069\u0062u\u0074e\u0020\u006e\u006f\u0074\u0020\u0073\u0070\u0065\u0063\u0069\u0066\u0069\u0065\u0064");};if len (rect )!=4{return nil ,_ceb .New ("\u0069\u006e\u0076\u0061\u006c\u0069\u0064\u0020\u0072\u0061\u006e\u0067\u0065");
};_gdad :=_ceg .NewPdfField ();_aege :=&_ceg .PdfFieldButton {};_aege .PdfField =_gdad ;_gdad .SetContext (_aege );_aege .SetType (_ceg .ButtonTypePush );_aege .T =_cd .MakeString (name );_ecdbd :=_ceg .NewPdfAnnotationWidget ();_ecdbd .Rect =_cd .MakeArrayFromFloats (rect );
_ecdbd .P =page .ToPdfObject ();_ecdbd .F =_cd .MakeInteger (4);_ecdbd .Parent =_aege .ToPdfObject ();_ccgf :=rect [2]-rect [0];_gggbf :=rect [3]-rect [1];_aegb :=opt ._aefd ;_fabg :=_c .NewContentCreator ();if _aegb .BorderSize > 0{_dfcd (_fabg ,_aegb ,_ccgf ,_gggbf );
};if _aegb .DrawAlignmentReticle {_efbe :=_aegb ;_efbe .BorderSize =0.2;_fcb (_fabg ,_efbe ,_ccgf ,_gggbf );};_eafb ,_geae :=_becce (_ccgf ,_gggbf ,opt .Image ,_aegb );if _geae !=nil {return nil ,_geae ;};_bcb ,_gfe :=_cd .GetDict (_ecdbd .MK );if _gfe {_bcb .Set ("\u006c",_eafb .ToPdfObject ());
};_cbeab :=_cd .MakeDict ();_cbeab .Set ("\u0046\u0052\u004d",_eafb .ToPdfObject ());_fefb :=_ceg .NewPdfPageResources ();_fefb .ProcSet =_cd .MakeArray (_cd .MakeName ("\u0050\u0044\u0046"));_fefb .XObject =_cbeab ;_fddf :=_ccgf -2;_ebef :=_gggbf -2;_fabg .Add_q ();
_fabg .Add_re (1,1,_fddf ,_ebef );_fabg .Add_W ();_fabg .Add_n ();_fddf -=2;_ebef -=2;_fabg .Add_q ();_fabg .Add_re (2,2,_fddf ,_ebef );_fabg .Add_W ();_fabg .Add_n ();_ffd :=_fc .Min (_fddf /float64 (opt .Image .Width ),_ebef /float64 (opt .Image .Height ));
_fabg .Add_cm (_ffd ,0,0,_ffd ,(_ccgf /2)-(float64 (opt .Image .Width )*_ffd /2)+2,2);_fabg .Add_Do ("\u0046\u0052\u004d");_fabg .Add_Q ();_fabg .Add_Q ();_deabb :=_ceg .NewXObjectForm ();_deabb .FormType =_cd .MakeInteger (1);_deabb .Resources =_fefb ;
_deabb .BBox =_cd .MakeArrayFromFloats ([]float64 {0,0,_ccgf ,_gggbf });_deabb .Matrix =_cd .MakeArrayFromFloats ([]float64 {1.0,0.0,0.0,1.0,0.0,0.0});_deabb .SetContentStream (_fabg .Bytes (),_adfb ());_dffb :=_cd .MakeDict ();_dffb .Set ("\u004e",_deabb .ToPdfObject ());
_ecdbd .AP =_dffb ;_aege .Annotations =append (_aege .Annotations ,_ecdbd );return _aege ,nil ;};func (_bfcf *AppearanceStyle )applyAppearanceCharacteristics (_fab *_cd .PdfObjectDictionary ,_bgd *_cd .PdfObjectDictionary ,_gea *_ceg .PdfFont )error {if !_bfcf .AllowMK {return nil ;
};if CA ,_gfbb :=_cd .GetString (_fab .Get ("\u0043\u0041"));_gfbb &&_gea !=nil {_decd :=CA .Bytes ();if len (_decd )!=0{_eba :=[]rune (_gea .Encoder ().Decode (_decd ));if len (_eba )==1{_bfcf .CheckmarkRune =_eba [0];};};};if BC ,_gggb :=_cd .GetArray (_fab .Get ("\u0042\u0043"));
_gggb {_abb ,_dgfg :=BC .ToFloat64Array ();if _dgfg !=nil {return _dgfg ;};switch len (_abb ){case 1:_bfcf .BorderColor =_ceg .NewPdfColorDeviceGray (_abb [0]);case 3:_bfcf .BorderColor =_ceg .NewPdfColorDeviceRGB (_abb [0],_abb [1],_abb [2]);case 4:_bfcf .BorderColor =_ceg .NewPdfColorDeviceCMYK (_abb [0],_abb [1],_abb [2],_abb [3]);
default:_f .Log .Debug ("\u0045\u0052\u0052\u004f\u0052:\u0020\u0042\u0043\u0020\u002d\u0020\u0049\u006e\u0076\u0061\u006c\u0069\u0064 \u006e\u0075\u006d\u0062\u0065\u0072\u0020\u006f\u0066\u0020\u0063\u006f\u006c\u006f\u0072\u0020\u0063\u006f\u006d\u0070\u006f\u006e\u0065\u006e\u0074\u0073\u0020\u0028\u0025\u0064)",len (_abb ));
};if _bgd !=nil {if _gge ,_afg :=_cd .GetNumberAsFloat (_bgd .Get ("\u0057"));_afg ==nil {_bfcf .BorderSize =_gge ;};};};if BG ,_ded :=_cd .GetArray (_fab .Get ("\u0042\u0047"));_ded {_fgb ,_affd :=BG .ToFloat64Array ();if _affd !=nil {return _affd ;};
switch len (_fgb ){case 1:_bfcf .FillColor =_ceg .NewPdfColorDeviceGray (_fgb [0]);case 3:_bfcf .FillColor =_ceg .NewPdfColorDeviceRGB (_fgb [0],_fgb [1],_fgb [2]);case 4:_bfcf .FillColor =_ceg .NewPdfColorDeviceCMYK (_fgb [0],_fgb [1],_fgb [2],_fgb [3]);
default:_f .Log .Debug ("\u0045\u0052\u0052\u004f\u0052:\u0020\u0042\u0047\u0020\u002d\u0020\u0049\u006e\u0076\u0061\u006c\u0069\u0064 \u006e\u0075\u006d\u0062\u0065\u0072\u0020\u006f\u0066\u0020\u0063\u006f\u006c\u006f\u0072\u0020\u0063\u006f\u006d\u0070\u006f\u006e\u0065\u006e\u0074\u0073\u0020\u0028\u0025\u0064)",len (_fgb ));
};};return nil ;};func _fcbb (_aegg *InkAnnotationDef )(*_cd .PdfObjectDictionary ,*_ceg .PdfRectangle ,error ){_bffed :=_ceg .NewXObjectForm ();_egee ,_edefc ,_gccde :=_aba (_aegg );if _gccde !=nil {return nil ,nil ,_gccde ;};_gccde =_bffed .SetContentStream (_egee ,nil );
if _gccde !=nil {return nil ,nil ,_gccde ;};_bffed .BBox =_edefc .ToPdfObject ();_bffed .Resources =_ceg .NewPdfPageResources ();_bffed .Resources .ProcSet =_cd .MakeArray (_cd .MakeName ("\u0050\u0044\u0046"));_aaga :=_cd .MakeDict ();_aaga .Set ("\u004e",_bffed .ToPdfObject ());
return _aaga ,_edefc ,nil ;};

// ImageFieldAppearance implements interface model.FieldAppearanceGenerator and generates appearance streams
// for attaching an image to a button field.
type ImageFieldAppearance struct{OnlyIfMissing bool ;_gdfg *AppearanceStyle ;};

// Style returns the appearance style of `fa`. If not specified, returns default style.
func (_eege ImageFieldAppearance )Style ()AppearanceStyle {if _eege ._gdfg !=nil {return *_eege ._gdfg ;};return AppearanceStyle {BorderSize :0.0,BorderColor :_ceg .NewPdfColorDeviceGray (0),FillColor :_ceg .NewPdfColorDeviceGray (1),DrawAlignmentReticle :false };
};const (_eeeg =1;_abgf =2;_eebag =4;_eaba =8;_bfe =16;_fed =32;_afgg =64;_ccdc =128;_fdcd =256;_ebc =512;_fda =1024;_ffbg =2048;_fcbf =4096;);func _cccf (_dafe *_ceg .PdfField )string {if _dafe ==nil {return "";};_fbef ,_dcec :=_dafe .GetContext ().(*_ceg .PdfFieldText );
if !_dcec {return _cccf (_dafe .Parent );};if _fbef .DA !=nil {return _fbef .DA .Str ();};return _cccf (_fbef .Parent );};const (_cfa quadding =0;_dc quadding =1;_fe quadding =2;_cad float64 =2.0;);func _dce (_feg *_ceg .PdfFieldText ,_aed _ceg .PdfColor )(*_cd .PdfObjectString ,error ){_bff :=_c .ContentStreamOperations {};
_fad :=_cccf (_feg .PdfField );_debb ,_gad :=_c .NewContentStreamParser (_fad ).Parse ();if _gad !=nil {return nil ,_gad ;};for _ ,_fdg :=range *_debb {if _fdg .Operand =="\u0067"||_fdg .Operand =="\u0072\u0067"{continue ;};_bff =append (_bff ,_fdg );};
_cc ,_efb :=_aed .(*_ceg .PdfColorDeviceRGB );if !_efb {return nil ,_gad ;};_dgfb ,_dff ,_gag :=_cd .MakeFloat (_cc [0]),_cd .MakeFloat (_cc [1]),_cd .MakeFloat (_cc [2]);_fegd :=&_c .ContentStreamOperation {Params :[]_cd .PdfObject {_dgfb ,_dff ,_gag },Operand :"\u0072\u0067"};
_bff =append (_bff ,_fegd );_cb :=_bff .String ();_cb =_ce .Replace (_cb ,"\u000a","\u0020",-1);_cb =_ce .Trim (_cb ,"\u0020");return _cd .MakeHexString (_cb ),nil ;};

// GenerateAppearanceDict generates an appearance dictionary for widget annotation `wa` for the `field` in `form`.
// Implements interface model.FieldAppearanceGenerator.
func (_aca FieldAppearance )GenerateAppearanceDict (form *_ceg .PdfAcroForm ,field *_ceg .PdfField ,wa *_ceg .PdfAnnotationWidget )(*_cd .PdfObjectDictionary ,error ){_f .Log .Trace ("\u0047\u0065n\u0065\u0072\u0061\u0074e\u0041\u0070p\u0065\u0061\u0072\u0061\u006e\u0063\u0065\u0044i\u0063\u0074\u0020\u0066\u006f\u0072\u0020\u0025\u0076\u0020\u0020\u0056:\u0020\u0025\u002b\u0076",field .PartialName (),field .V );
_ ,_cdd :=field .GetContext ().(*_ceg .PdfFieldText );_fae ,_dd :=_cd .GetDict (wa .AP );if _dd &&_aca .OnlyIfMissing &&(!_cdd ||!_aca .RegenerateTextFields ){_f .Log .Trace ("\u0041\u006c\u0072\u0065a\u0064\u0079\u0020\u0070\u006f\u0070\u0075\u006c\u0061\u0074e\u0064 \u002d\u0020\u0069\u0067\u006e\u006f\u0072i\u006e\u0067");
return _fae ,nil ;};if form .DR ==nil {form .DR =_ceg .NewPdfPageResources ();};switch _gb :=field .GetContext ().(type ){case *_ceg .PdfFieldText :_egb :=_gb ;if _ea :=_cccf (_egb .PdfField );_ea ==""{_egb .DA =form .DA ;};if _aca ._cab !=nil {if _aca ._cab .FieldColors !=nil &&_aca ._cab .FieldColors [field .PartialName ()]!=nil {_aef :=_aca ._cab .FieldColors [field .PartialName ()];
_dad ,_fd :=_dce (_egb ,_aef );if _fd !=nil {return nil ,_fd ;};_egb .DA =_dad ;}else if _aca ._cab .TextColor !=nil {_bb ,_gd :=_dce (_egb ,_aca ._cab .TextColor );if _gd !=nil {return nil ,_gd ;};_egb .DA =_bb ;};};switch {case _egb .Flags ().Has (_ceg .FieldFlagPassword ):return nil ,nil ;
case _egb .Flags ().Has (_ceg .FieldFlagFileSelect ):return nil ,nil ;case _egb .Flags ().Has (_ceg .FieldFlagComb ):if _egb .MaxLen !=nil {_dde ,_dcb :=_beac (wa ,_egb ,form .DR ,_aca .Style ());if _dcb !=nil {return nil ,_dcb ;};return _dde ,nil ;};};
_daf ,_cea :=_gagf (wa ,_egb ,form .DR ,_aca .Style ());if _cea !=nil {return nil ,_cea ;};return _daf ,nil ;case *_ceg .PdfFieldButton :_dac :=_gb ;if _dac .IsCheckbox (){_baf ,_bdd :=_dgd (wa ,_dac ,form .DR ,_aca .Style ());if _bdd !=nil {return nil ,_bdd ;
};return _baf ,nil ;};_f .Log .Debug ("\u0054\u004f\u0044\u004f\u003a\u0020\u0055\u004e\u0048\u0041\u004e\u0044\u004c\u0045\u0044 \u0062u\u0074\u0074\u006f\u006e\u0020\u0074\u0079\u0070\u0065\u003a\u0020\u0025\u002b\u0076",_dac .GetType ());case *_ceg .PdfFieldChoice :_df :=_gb ;
switch {case _df .Flags ().Has (_ceg .FieldFlagCombo ):_def ,_efe :=_eab (form ,wa ,_df ,_aca .Style ());if _efe !=nil {return nil ,_efe ;};return _def ,nil ;default:_f .Log .Debug ("\u0054\u004f\u0044\u004f\u003a\u0020\u0055N\u0048\u0041\u004eD\u004c\u0045\u0044\u0020c\u0068\u006f\u0069\u0063\u0065\u0020\u0066\u0069\u0065\u006c\u0064\u0020\u0077\u0069\u0074\u0068\u0020\u0066\u006c\u0061\u0067\u0073\u003a\u0020\u0025\u0073",_df .Flags ().String ());
};default:_f .Log .Debug ("\u0054\u004f\u0044\u004f\u003a\u0020\u0055\u004e\u0048\u0041N\u0044\u004c\u0045\u0044\u0020\u0066\u0069e\u006c\u0064\u0020\u0074\u0079\u0070\u0065\u003a\u0020\u0025\u0054",_gb );};return nil ,nil ;};

// FormResetActionOptions holds options for creating a form reset button.
type FormResetActionOptions struct{

// Rectangle holds the button position, size, and color.
Rectangle _ba .Rectangle ;

// Label specifies the text that would be displayed on the button.
Label string ;

// LabelColor specifies the button label color.
LabelColor _ceg .PdfColor ;

// Font specifies a font used for rendering the button label.
// When omitted it will fallback to use a Helvetica font.
Font *_ceg .PdfFont ;

// FontSize specifies the font size used in rendering the button label.
// The default font size is 12pt.
FontSize *float64 ;

// Fields specifies list of fields that could be resetted.
// This list may contain indirect object to fields or field names.
Fields *_cd .PdfObjectArray ;

// IsExclusionList specifies that the fields in the `Fields` array would be excluded form reset process.
IsExclusionList bool ;};const (SignatureImageLeft SignatureImagePosition =iota ;SignatureImageRight ;SignatureImageTop ;SignatureImageBottom ;);

// WrapContentStream ensures that the entire content stream for a `page` is wrapped within q ... Q operands.
// Ensures that following operands that are added are not affected by additional operands that are added.
// Implements interface model.ContentStreamWrapper.
func (_egcb ImageFieldAppearance )WrapContentStream (page *_ceg .PdfPage )error {_bgg ,_fffe :=page .GetAllContentStreams ();if _fffe !=nil {return _fffe ;};_cdac :=_c .NewContentStreamParser (_bgg );_gfeg ,_fffe :=_cdac .Parse ();if _fffe !=nil {return _fffe ;
};_gfeg .WrapIfNeeded ();_eeega :=[]string {_gfeg .String ()};return page .SetContentStreams (_eeega ,_adfb ());};func _dfcd (_fga *_c .ContentCreator ,_gbc AppearanceStyle ,_aggg ,_fege float64 ){_fga .Add_q ().Add_re (0,0,_aggg ,_fege ).Add_w (_gbc .BorderSize ).SetStrokingColor (_gbc .BorderColor ).SetNonStrokingColor (_gbc .FillColor ).Add_B ().Add_Q ();
};

// SetStyle applies appearance `style` to `fa`.
func (_deb *FieldAppearance )SetStyle (style AppearanceStyle ){_deb ._cab =&style };

// NewComboboxField generates a new combobox form field with partial name `name` at location `rect`
// on specified `page` and with field specific options `opt`.
func NewComboboxField (page *_ceg .PdfPage ,name string ,rect []float64 ,opt ComboboxFieldOptions )(*_ceg .PdfFieldChoice ,error ){if page ==nil {return nil ,_ceb .New ("\u0070a\u0067e\u0020\u006e\u006f\u0074\u0020s\u0070\u0065c\u0069\u0066\u0069\u0065\u0064");
};if len (name )<=0{return nil ,_ceb .New ("\u0072\u0065\u0071\u0075\u0069\u0072\u0065\u0064\u0020\u0061\u0074\u0074\u0072\u0069\u0062u\u0074e\u0020\u006e\u006f\u0074\u0020\u0073\u0070\u0065\u0063\u0069\u0066\u0069\u0065\u0064");};if len (rect )!=4{return nil ,_ceb .New ("\u0069\u006e\u0076\u0061\u006c\u0069\u0064\u0020\u0072\u0061\u006e\u0067\u0065");
};_bacb :=_ceg .NewPdfField ();_dcgg :=&_ceg .PdfFieldChoice {};_bacb .SetContext (_dcgg );_dcgg .PdfField =_bacb ;_dcgg .T =_cd .MakeString (name );_dcgg .Opt =_cd .MakeArray ();for _ ,_faba :=range opt .Choices {_dcgg .Opt .Append (_cd .MakeString (_faba ));
};_dcgg .SetFlag (_ceg .FieldFlagCombo );_fefc :=_ceg .NewPdfAnnotationWidget ();_fefc .Rect =_cd .MakeArrayFromFloats (rect );_fefc .P =page .ToPdfObject ();_fefc .F =_cd .MakeInteger (4);_fefc .Parent =_dcgg .ToPdfObject ();_dcgg .Annotations =append (_dcgg .Annotations ,_fefc );
return _dcgg ,nil ;};func _dbaf (_ccbc *_ceg .PdfFieldButton ,_bagad *_ceg .PdfAnnotationWidget ,_cdea AppearanceStyle )(*_cd .PdfObjectDictionary ,error ){_afgd ,_cgae :=_cd .GetArray (_bagad .Rect );if !_cgae {return nil ,_ceb .New ("\u0069\u006e\u0076a\u006c\u0069\u0064\u0020\u0052\u0065\u0063\u0074");
};_eebg ,_gbdge :=_ceg .NewPdfRectangle (*_afgd );if _gbdge !=nil {return nil ,_gbdge ;};_gcee ,_aafc :=_eebg .Width (),_eebg .Height ();_cgdc :=_c .NewContentCreator ();if _cdea .BorderSize > 0{_dfcd (_cgdc ,_cdea ,_gcee ,_aafc );};if _cdea .DrawAlignmentReticle {_eggc :=_cdea ;
_eggc .BorderSize =0.2;_fcb (_cgdc ,_eggc ,_gcee ,_aafc );};_fgbe :=_ccbc .GetFillImage ();_fgga ,_gbdge :=_becce (_gcee ,_aafc ,_fgbe ,_cdea );if _gbdge !=nil {return nil ,_gbdge ;};_eccb ,_beaec :=_cd .GetDict (_bagad .MK );if _beaec {_eccb .Set ("\u006c",_fgga .ToPdfObject ());
};_efg :=_cd .MakeDict ();_efg .Set ("\u0046\u0052\u004d",_fgga .ToPdfObject ());_edef :=_ceg .NewPdfPageResources ();_edef .ProcSet =_cd .MakeArray (_cd .MakeName ("\u0050\u0044\u0046"));_edef .XObject =_efg ;_gffe :=_gcee -2;_dbga :=_aafc -2;_cgdc .Add_q ();
_cgdc .Add_re (1,1,_gffe ,_dbga );_cgdc .Add_W ();_cgdc .Add_n ();_gffe -=2;_dbga -=2;_cgdc .Add_q ();_cgdc .Add_re (2,2,_gffe ,_dbga );_cgdc .Add_W ();_cgdc .Add_n ();_bcf :=_fc .Min (_gffe /float64 (_fgbe .Width ),_dbga /float64 (_fgbe .Height ));_cgdc .Add_cm (_bcf ,0,0,_bcf ,(_gcee /2)-(float64 (_fgbe .Width )*_bcf /2)+2,2);
_cgdc .Add_Do ("\u0046\u0052\u004d");_cgdc .Add_Q ();_cgdc .Add_Q ();_egfg :=_ceg .NewXObjectForm ();_egfg .FormType =_cd .MakeInteger (1);_egfg .Resources =_edef ;_egfg .BBox =_cd .MakeArrayFromFloats ([]float64 {0,0,_gcee ,_aafc });_egfg .Matrix =_cd .MakeArrayFromFloats ([]float64 {1.0,0.0,0.0,1.0,0.0,0.0});
_egfg .SetContentStream (_cgdc .Bytes (),_adfb ());_afga :=_cd .MakeDict ();_afga .Set ("\u004e",_egfg .ToPdfObject ());return _afga ,nil ;};

// SetStyle applies appearance `style` to `fa`.
func (_dacc *ImageFieldAppearance )SetStyle (style AppearanceStyle ){_dacc ._gdfg =&style };func (_efde *AppearanceStyle )processDA (_acc *_ceg .PdfField ,_caa *_c .ContentStreamOperations ,_dagg ,_bbbe *_ceg .PdfPageResources ,_fbb *_c .ContentCreator )(*AppearanceFont ,bool ,error ){var _fcfc *AppearanceFont ;
var _babc bool ;if _efde .Fonts !=nil {if _efde .Fonts .Fallback !=nil {_fcfc =_efde .Fonts .Fallback ;};if _fcaa :=_efde .Fonts .FieldFallbacks ;_fcaa !=nil {if _febg ,_geab :=_fcaa [_acc .PartialName ()];_geab {_fcfc =_febg ;}else if _fgdf ,_degf :=_acc .FullName ();
_degf ==nil {if _gffb ,_gggf :=_fcaa [_fgdf ];_gggf {_fcfc =_gffb ;};};};if _fcfc !=nil {_fcfc .fillName ();};_babc =_efde .Fonts .ForceReplace ;};var _ceab string ;var _daag float64 ;var _dggf bool ;if _caa !=nil {for _ ,_afad :=range *_caa {if _afad .Operand =="\u0054\u0066"&&len (_afad .Params )==2{if _ecdb ,_affa :=_cd .GetNameVal (_afad .Params [0]);
_affa {_ceab =_ecdb ;};if _cdff ,_cfgf :=_cd .GetNumberAsFloat (_afad .Params [1]);_cfgf ==nil {_daag =_cdff ;};_dggf =true ;continue ;};_fbb .AddOperand (*_afad );};};var _gcd *AppearanceFont ;var _bbe _cd .PdfObject ;if _babc &&_fcfc !=nil {_gcd =_fcfc ;
}else {if _dagg !=nil &&_ceab !=""{if _abe ,_aceb :=_dagg .GetFontByName (*_cd .MakeName (_ceab ));_aceb {if _dccf ,_cfaab :=_ceg .NewPdfFontFromPdfObject (_abe );_cfaab ==nil {_bbe =_abe ;_gcd =&AppearanceFont {Name :_ceab ,Font :_dccf ,Size :_daag };
}else {_f .Log .Debug ("\u0045\u0052\u0052\u004f\u0052:\u0020\u0063\u006f\u0075\u006c\u0064\u0020\u006e\u006f\u0074\u0020\u006c\u006fa\u0064\u0020\u0061\u0070\u0070\u0065\u0061\u0072\u0061\u006e\u0063\u0065\u0020\u0066\u006f\u006e\u0074\u003a\u0020\u0025\u0076",_cfaab );
};};};if _gcd ==nil &&_fcfc !=nil {_gcd =_fcfc ;};if _gcd ==nil {_ecc ,_cca :=_ceg .NewStandard14Font ("\u0048e\u006c\u0076\u0065\u0074\u0069\u0063a");if _cca !=nil {return nil ,false ,_cca ;};_gcd =&AppearanceFont {Name :"\u0048\u0065\u006c\u0076",Font :_ecc ,Size :_daag };
};};if _gcd .Size <=0&&_efde .Fonts !=nil &&_efde .Fonts .FallbackSize > 0{_gcd .Size =_efde .Fonts .FallbackSize ;};_bgc :=*_cd .MakeName (_gcd .Name );if _bbe ==nil {_bbe =_gcd .Font .ToPdfObject ();};if _dagg !=nil &&!_dagg .HasFontByName (_bgc ){_dagg .SetFontByName (_bgc ,_bbe );
};if _bbbe !=nil &&!_bbbe .HasFontByName (_bgc ){_bbbe .SetFontByName (_bgc ,_bbe );};return _gcd ,_dggf ,nil ;};func _becce (_beba ,_ddb float64 ,_ebbd *_ceg .Image ,_eccd AppearanceStyle )(*_ceg .XObjectForm ,error ){_caae ,_dggff :=_ceg .NewXObjectImageFromImage (_ebbd ,nil ,_cd .NewFlateEncoder ());
if _dggff !=nil {return nil ,_dggff ;};_caae .Decode =_cd .MakeArrayFromFloats ([]float64 {0.0,1.0,0.0,1.0,0.0,1.0});_faag :=_ceg .NewPdfPageResources ();_faag .ProcSet =_cd .MakeArray (_cd .MakeName ("\u0050\u0044\u0046"),_cd .MakeName ("\u0049\u006d\u0061\u0067\u0065\u0043"));
_faag .SetXObjectImageByName (_cd .PdfObjectName ("\u0049\u006d\u0030"),_caae );_bgca :=_c .NewContentCreator ();_bgca .Add_q ();_bgca .Add_cm (float64 (_ebbd .Width ),0,0,float64 (_ebbd .Height ),0,0);_bgca .Add_Do ("\u0049\u006d\u0030");_bgca .Add_Q ();
_ggaa :=_ceg .NewXObjectForm ();_ggaa .FormType =_cd .MakeInteger (1);_ggaa .BBox =_cd .MakeArrayFromFloats ([]float64 {0,0,float64 (_ebbd .Width ),float64 (_ebbd .Height )});_ggaa .Resources =_faag ;_ggaa .SetContentStream (_bgca .Bytes (),_adfb ());return _ggaa ,nil ;
};

// CreateCircleAnnotation creates a circle/ellipse annotation object with appearance stream that can be added to
// page PDF annotations.
func CreateCircleAnnotation (circDef CircleAnnotationDef )(*_ceg .PdfAnnotation ,error ){_cg :=_ceg .NewPdfAnnotationCircle ();if circDef .BorderEnabled {_ga ,_bac ,_ec :=circDef .BorderColor .R (),circDef .BorderColor .G (),circDef .BorderColor .B ();
_cg .C =_cd .MakeArrayFromFloats ([]float64 {_ga ,_bac ,_ec });_ee :=_ceg .NewBorderStyle ();_ee .SetBorderWidth (circDef .BorderWidth );_cg .BS =_ee .ToPdfObject ();};if circDef .FillEnabled {_ae ,_gaa ,_dgf :=circDef .FillColor .R (),circDef .FillColor .G (),circDef .FillColor .B ();
_cg .IC =_cd .MakeArrayFromFloats ([]float64 {_ae ,_gaa ,_dgf });}else {_cg .IC =_cd .MakeArrayFromIntegers ([]int {});};if circDef .Opacity < 1.0{_cg .CA =_cd .MakeFloat (circDef .Opacity );};_ac ,_ca ,_eg :=_ag (circDef );if _eg !=nil {return nil ,_eg ;
};_cg .AP =_ac ;_cg .Rect =_cd .MakeArrayFromFloats ([]float64 {_ca .Llx ,_ca .Lly ,_ca .Urx ,_ca .Ury });return _cg .PdfAnnotation ,nil ;};

// CreateLineAnnotation creates a line annotation object that can be added to page PDF annotations.
func CreateLineAnnotation (lineDef LineAnnotationDef )(*_ceg .PdfAnnotation ,error ){_adga :=_ceg .NewPdfAnnotationLine ();_adga .L =_cd .MakeArrayFromFloats ([]float64 {lineDef .X1 ,lineDef .Y1 ,lineDef .X2 ,lineDef .Y2 });_cgdf :=_cd .MakeName ("\u004e\u006f\u006e\u0065");
if lineDef .LineEndingStyle1 ==_ba .LineEndingStyleArrow {_cgdf =_cd .MakeName ("C\u006c\u006f\u0073\u0065\u0064\u0041\u0072\u0072\u006f\u0077");};_cbfg :=_cd .MakeName ("\u004e\u006f\u006e\u0065");if lineDef .LineEndingStyle2 ==_ba .LineEndingStyleArrow {_cbfg =_cd .MakeName ("C\u006c\u006f\u0073\u0065\u0064\u0041\u0072\u0072\u006f\u0077");
};_adga .LE =_cd .MakeArray (_cgdf ,_cbfg );if lineDef .Opacity < 1.0{_adga .CA =_cd .MakeFloat (lineDef .Opacity );};_agfd ,_cccce ,_defg :=lineDef .LineColor .R (),lineDef .LineColor .G (),lineDef .LineColor .B ();_adga .IC =_cd .MakeArrayFromFloats ([]float64 {_agfd ,_cccce ,_defg });
_adga .C =_cd .MakeArrayFromFloats ([]float64 {_agfd ,_cccce ,_defg });_dcee :=_ceg .NewBorderStyle ();_dcee .SetBorderWidth (lineDef .LineWidth );_adga .BS =_dcee .ToPdfObject ();_bfgf ,_eaca ,_gee :=_fcba (lineDef );if _gee !=nil {return nil ,_gee ;};
_adga .AP =_bfgf ;_adga .Rect =_cd .MakeArrayFromFloats ([]float64 {_eaca .Llx ,_eaca .Lly ,_eaca .Urx ,_eaca .Ury });return _adga .PdfAnnotation ,nil ;};func _beac (_afde *_ceg .PdfAnnotationWidget ,_bca *_ceg .PdfFieldText ,_dgag *_ceg .PdfPageResources ,_daa AppearanceStyle )(*_cd .PdfObjectDictionary ,error ){_adc :=_ceg .NewPdfPageResources ();
_acf ,_fcc :=_cd .GetArray (_afde .Rect );if !_fcc {return nil ,_ceb .New ("\u0069\u006e\u0076a\u006c\u0069\u0064\u0020\u0052\u0065\u0063\u0074");};_fef ,_ecag :=_ceg .NewPdfRectangle (*_acf );if _ecag !=nil {return nil ,_ecag ;};_dda ,_gbd :=_fef .Width (),_fef .Height ();
_ade ,_bbcd :=_dda ,_gbd ;_gga ,_decg :=_cd .GetDict (_afde .MK );if _decg {_ccd ,_ :=_cd .GetDict (_afde .BS );_dfa :=_daa .applyAppearanceCharacteristics (_gga ,_ccd ,nil );if _dfa !=nil {return nil ,_dfa ;};};_cfd ,_decg :=_cd .GetIntVal (_bca .MaxLen );
if !_decg {return nil ,_ceb .New ("\u006d\u0061\u0078\u006c\u0065\u006e\u0020\u006e\u006ft\u0020\u0073\u0065\u0074");};if _cfd <=0{return nil ,_ceb .New ("\u006d\u0061\u0078\u004c\u0065\u006e\u0020\u0069\u006ev\u0061\u006c\u0069\u0064");};_bag :=_dda /float64 (_cfd );
_cgf ,_ecag :=_c .NewContentStreamParser (_cccf (_bca .PdfField )).Parse ();if _ecag !=nil {return nil ,_ecag ;};_cfe :=_c .NewContentCreator ();if _daa .BorderSize > 0{_dfcd (_cfe ,_daa ,_dda ,_gbd );};if _daa .DrawAlignmentReticle {_ffe :=_daa ;_ffe .BorderSize =0.2;
_fcb (_cfe ,_ffe ,_dda ,_gbd );};_cfe .Add_BMC ("\u0054\u0078");_cfe .Add_q ();_ ,_gbd =_daa .applyRotation (_gga ,_dda ,_gbd ,_cfe );_cfe .Add_BT ();_aec ,_add ,_ecag :=_daa .processDA (_bca .PdfField ,_cgf ,_dgag ,_adc ,_cfe );if _ecag !=nil {return nil ,_ecag ;
};_beae :=_aec .Font ;_dfc :=_cd .MakeName (_aec .Name );_caf :=_aec .Size ;_gbg :=_caf ==0;if _gbg &&_add {_caf =_gbd *_daa .AutoFontSizeFraction ;};_dfe :=_beae .Encoder ();if _dfe ==nil {_f .Log .Debug ("\u0057\u0041RN\u003a\u0020\u0066\u006f\u006e\u0074\u0020\u0065\u006e\u0063\u006f\u0064\u0065\u0072\u0020\u0069\u0073\u0020\u006e\u0069l\u002e\u0020\u0041\u0073s\u0075\u006d\u0069\u006eg \u0069\u0064e\u006et\u0069\u0074\u0079\u0020\u0065\u006ec\u006f\u0064\u0065r\u002e\u0020O\u0075\u0074\u0070\u0075\u0074\u0020\u006d\u0061\u0079\u0020\u0062\u0065\u0020\u0069n\u0063\u006f\u0072\u0072\u0065\u0063\u0074\u002e");
_dfe =_dg .NewIdentityTextEncoder ("\u0049\u0064\u0065\u006e\u0074\u0069\u0074\u0079\u002d\u0048");};var _dbgg string ;if _geb ,_daca :=_cd .GetString (_bca .V );_daca {_dbgg =_geb .Decoded ();};_cfe .Add_Tf (*_dfc ,_caf );var _dcbe float64 ;for _ ,_cfc :=range _dbgg {_eeg ,_aeb :=_beae .GetRuneMetrics (_cfc );
if !_aeb {_f .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a \u0052\u0075\u006e\u0065\u0020\u006e\u006f\u0074\u0020\u0066\u006f\u0075\u006e\u0064\u0020\u0069\u006e\u0020\u0066\u006fn\u0074\u003a\u0020\u0025\u0076\u0020\u002d\u0020\u0073\u006b\u0069\u0070\u0070\u0069n\u0067 \u006f\u0076\u0065\u0072",_cfc );
continue ;};_eed :=_eeg .Wy ;if int (_eed )<=0{_eed =_eeg .Wx ;};if _eed > _dcbe {_dcbe =_eed ;};};if int (_dcbe )==0{_f .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u0055\u006e\u0061\u0062\u006c\u0065\u0020\u0074o\u0020\u0064\u0065\u0074\u0065\u0072\u006d\u0069\u006e\u0065\u0020\u006d\u0061x\u0020\u0067\u006c\u0079\u0070\u0068\u0020\u0073\u0069\u007a\u0065\u0020- \u0075\u0073\u0069\u006e\u0067\u0020\u0031\u0030\u0030\u0030");
_dcbe =1000;};_eeba ,_ecag :=_beae .GetFontDescriptor ();if _ecag !=nil {_f .Log .Debug ("\u0045\u0072ro\u0072\u003a\u0020U\u006e\u0061\u0062\u006ce t\u006f g\u0065\u0074\u0020\u0066\u006f\u006e\u0074 d\u0065\u0073\u0063\u0072\u0069\u0070\u0074o\u0072");
};var _cfb float64 ;if _eeba !=nil {_cfb ,_ecag =_eeba .GetCapHeight ();if _ecag !=nil {_f .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u0055\u006e\u0061\u0062\u006c\u0065 \u0074\u006f\u0020\u0067\u0065\u0074 \u0066\u006f\u006e\u0074\u0020\u0043\u0061\u0070\u0048\u0065\u0069\u0067\u0068t\u003a\u0020\u0025\u0076",_ecag );
};};if int (_cfb )<=0{_f .Log .Debug ("W\u0041\u0052\u004e\u003a\u0020\u0043\u0061\u0070\u0048e\u0069\u0067\u0068\u0074\u0020\u006e\u006ft \u0061\u0076\u0061\u0069l\u0061\u0062\u006c\u0065\u0020\u002d\u0020\u0073\u0065tt\u0069\u006eg\u0020\u0074\u006f\u0020\u0031\u0030\u0030\u0030");
_cfb =1000.0;};_dccc :=_cfb /1000.0*_caf ;_ecd :=0.0;_gbb :=1.0*_caf *(_dcbe /1000.0);{_dfaf :=_gbb ;if _gbg &&_ecd +_dfaf > _gbd {_caf =0.95*(_gbd -_ecd );_dccc =_cfb /1000.0*_caf ;};if _gbd > _dccc {_ecd =(_gbd -_dccc )/2.0;};};_cfe .Add_Td (0,_ecd );
if _ede ,_dffg :=_cd .GetIntVal (_bca .Q );_dffg {switch _ede {case 2:if len (_dbgg )< _cfd {_bdc :=float64 (_cfd -len (_dbgg ))*_bag ;_cfe .Add_Td (_bdc ,0);};};};for _fgg ,_cee :=range _dbgg {_adg :=_cad ;if _daa .MarginLeft !=nil {_adg =*_daa .MarginLeft ;
};_ddf :=string (_cee );if _dfe !=nil {_gaf ,_beag :=_beae .GetRuneMetrics (_cee );if !_beag {_f .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a \u0052\u0075\u006e\u0065\u0020\u006e\u006f\u0074\u0020\u0066\u006f\u0075\u006e\u0064\u0020\u0069\u006e\u0020\u0066\u006fn\u0074\u003a\u0020\u0025\u0076\u0020\u002d\u0020\u0073\u006b\u0069\u0070\u0070\u0069n\u0067 \u006f\u0076\u0065\u0072",_cee );
continue ;};_ddf =string (_dfe .Encode (_ddf ));_cfdc :=_caf *_gaf .Wx /1000.0;_feb :=(_bag -_cfdc )/2;_adg =_feb ;};_cfe .Add_Td (_adg ,0);_cfe .Add_Tj (*_cd .MakeString (_ddf ));if _fgg !=len (_dbgg )-1{_cfe .Add_Td (_bag -_adg ,0);};};_cfe .Add_ET ();
_cfe .Add_Q ();_cfe .Add_EMC ();_bae :=_ceg .NewXObjectForm ();_bae .Resources =_adc ;_bae .BBox =_cd .MakeArrayFromFloats ([]float64 {0,0,_ade ,_bbcd });_bae .SetContentStream (_cfe .Bytes (),_adfb ());_dccd :=_cd .MakeDict ();_dccd .Set ("\u004e",_bae .ToPdfObject ());
return _dccd ,nil ;};

// ImageFieldOptions defines optional parameters for a push button with image attach capability form field.
type ImageFieldOptions struct{Image *_ceg .Image ;_aefd AppearanceStyle ;};

// CreateFileAttachmentAnnotation creates a file attachment annotation object that can be added to the annotation list of a PDF page.
func CreateFileAttachmentAnnotation (fileDef FileAnnotationDef )(*_ceg .PdfAnnotation ,error ){_gbdg :=_ceg .NewPdfFileSpecFromEmbeddedFile (fileDef .EmbeddedFile );if fileDef .Color ==nil {fileDef .Color =_ceg .NewPdfColorDeviceRGB (0.0,0.0,0.0);};if fileDef .Description ==""{fileDef .Description =fileDef .EmbeddedFile .Name ;
};if fileDef .CreationDate ==nil {_acfg :=_e .Now ();fileDef .CreationDate =&_acfg ;};if fileDef .IconName ==""{fileDef .IconName ="\u0050u\u0073\u0068\u0050\u0069\u006e";};_aabe ,_aagg :=_ceg .NewPdfDateFromTime (*fileDef .CreationDate );if _aagg !=nil {return nil ,_aagg ;
};_dgbe :=_ceg .NewPdfAnnotationFileAttachment ();_dgbe .FS =_gbdg .ToPdfObject ();_dgbe .C =_cd .MakeArrayFromFloats ([]float64 {fileDef .Color .R (),fileDef .Color .G (),fileDef .Color .B ()});_dgbe .Contents =_cd .MakeString (fileDef .Description );
_dgbe .CreationDate =_aabe .ToPdfObject ();_dgbe .M =_aabe .ToPdfObject ();_dgbe .Name =_cd .MakeName (fileDef .IconName );_dgbe .Rect =_cd .MakeArrayFromFloats ([]float64 {fileDef .X ,fileDef .Y ,fileDef .X +fileDef .Width ,fileDef .Y +fileDef .Height });
_dgbe .T =_cd .MakeString (fileDef .Author );_dgbe .Subj =_cd .MakeString (fileDef .Subject );return _dgbe .PdfAnnotation ,nil ;};

// LineAnnotationDef defines a line between point 1 (X1,Y1) and point 2 (X2,Y2).  The line ending styles can be none
// (regular line), or arrows at either end.  The line also has a specified width, color and opacity.
type LineAnnotationDef struct{X1 float64 ;Y1 float64 ;X2 float64 ;Y2 float64 ;LineColor *_ceg .PdfColorDeviceRGB ;Opacity float64 ;LineWidth float64 ;LineEndingStyle1 _ba .LineEndingStyle ;LineEndingStyle2 _ba .LineEndingStyle ;};func _dgdf (_bfb _g .Image ,_gdba string ,_dca *SignatureFieldOpts ,_fafb []float64 ,_gbfc *_c .ContentCreator )(*_cd .PdfObjectName ,*_ceg .XObjectImage ,error ){_gcfb ,_cafg :=_ceg .DefaultImageHandler {}.NewImageFromGoImage (_bfb );
if _cafg !=nil {return nil ,nil ,_cafg ;};_dgdff ,_cafg :=_ceg .NewXObjectImageFromImage (_gcfb ,nil ,_dca .Encoder );if _cafg !=nil {return nil ,nil ,_cafg ;};_gafg ,_gccd :=float64 (*_dgdff .Width ),float64 (*_dgdff .Height );_cafb :=_fafb [2]-_fafb [0];
_gdaf :=_fafb [3]-_fafb [1];if _dca .AutoSize {_gae :=_fc .Min (_cafb /_gafg ,_gdaf /_gccd );_gafg *=_gae ;_gccd *=_gae ;_fafb [0]=_fafb [0]+(_cafb /2)-(_gafg /2);_fafb [1]=_fafb [1]+(_gdaf /2)-(_gccd /2);};var _agc *_cd .PdfObjectName ;if _fdf ,_fgdd :=_cd .GetName (_dgdff .Name );
_fgdd {_agc =_fdf ;}else {_agc =_cd .MakeName (_gdba );};if _gbfc !=nil {_gbfc .Add_q ().Translate (_fafb [0],_fafb [1]).Scale (_gafg ,_gccd ).Add_Do (*_agc ).Add_Q ();}else {return nil ,nil ,_ceb .New ("\u0043\u006f\u006e\u0074en\u0074\u0043\u0072\u0065\u0061\u0074\u006f\u0072\u0020\u0069\u0073\u0020\u006e\u0075l\u006c");
};return _agc ,_dgdff ,nil ;};func _adfb ()_cd .StreamEncoder {return _cd .NewFlateEncoder ()};func _aba (_fabad *InkAnnotationDef )([]byte ,*_ceg .PdfRectangle ,error ){_fdcg :=[][]_ba .CubicBezierCurve {};for _ ,_ddg :=range _fabad .Paths {if _ddg .Length ()==0{continue ;
};_cdb :=_ddg .Points ;_cdgbd ,_cgdb ,_ebcc :=_agef (_cdb );if _ebcc !=nil {return nil ,nil ,_ebcc ;};if len (_cdgbd )!=len (_cgdb ){return nil ,nil ,_ceb .New ("\u0049\u006e\u0065\u0071\u0075\u0061\u006c\u0020\u006e\u0075\u006d\u0062\u0065\u0072\u0020\u006f\u0066\u0020\u0063\u0061l\u0063\u0075\u006c\u0061\u0074\u0065\u0064\u0020\u0066\u0069\u0072\u0073\u0074\u0020\u0061\u006e\u0064\u0020\u0073\u0065\u0063\u006f\u006e\u0064\u0020\u0063\u006f\u006e\u0074\u0072o\u006c\u0020\u0070\u006f\u0069n\u0074");
};_ddeg :=[]_ba .CubicBezierCurve {};for _fgee :=0;_fgee < len (_cdgbd );_fgee ++{_ddeg =append (_ddeg ,_ba .CubicBezierCurve {P0 :_cdb [_fgee ],P1 :_cdgbd [_fgee ],P2 :_cgdb [_fgee ],P3 :_cdb [_fgee +1]});};if len (_ddeg )> 0{_fdcg =append (_fdcg ,_ddeg );
};};_aefe ,_edfd ,_badd :=_ccdb (_fdcg ,_fabad .Color ,_fabad .LineWidth );if _badd !=nil {return nil ,nil ,_badd ;};return _aefe ,_edfd ,nil ;};func (_cf *AppearanceFont )fillName (){if _cf .Font ==nil ||_cf .Name !=""{return ;};_fac :=_cf .Font .FontDescriptor ();
if _fac ==nil ||_fac .FontName ==nil {return ;};_cf .Name =_fac .FontName .String ();};

// NewFormResetButtonField would create a reset button in specified page according to the parameter in `FormResetActionOptions`.
func NewFormResetButtonField (page *_ceg .PdfPage ,opt FormResetActionOptions )(*_ceg .PdfFieldButton ,error ){_fddd :=_ceg .NewPdfActionResetForm ();_fddd .Fields =opt .Fields ;_fddd .Flags =_cd .MakeInteger (0);if opt .IsExclusionList {_fddd .Flags =_cd .MakeInteger (1);
};_gaef ,_cge :=_aabf (page ,opt .Rectangle ,"\u0062\u0074\u006e\u0052\u0065\u0073\u0065\u0074",opt .Label ,opt .LabelColor ,opt .Font ,opt .FontSize ,_fddd .ToPdfObject ());if _cge !=nil {return nil ,_cge ;};return _gaef ,nil ;};func _ag (_gg CircleAnnotationDef )(*_cd .PdfObjectDictionary ,*_ceg .PdfRectangle ,error ){_aga :=_ceg .NewXObjectForm ();
_aga .Resources =_ceg .NewPdfPageResources ();_dgb :="";if _gg .Opacity < 1.0{_age :=_cd .MakeDict ();_age .Set ("\u0063\u0061",_cd .MakeFloat (_gg .Opacity ));_age .Set ("\u0043\u0041",_cd .MakeFloat (_gg .Opacity ));_gf :=_aga .Resources .AddExtGState ("\u0067\u0073\u0031",_age );
if _gf !=nil {_f .Log .Debug ("U\u006e\u0061\u0062\u006c\u0065\u0020t\u006f\u0020\u0061\u0064\u0064\u0020\u0065\u0078\u0074g\u0073\u0074\u0061t\u0065 \u0067\u0073\u0031");return nil ,nil ,_gf ;};_dgb ="\u0067\u0073\u0031";};_ge ,_bf ,_agee ,_dga :=_eb (_gg ,_dgb );
if _dga !=nil {return nil ,nil ,_dga ;};_dga =_aga .SetContentStream (_ge ,nil );if _dga !=nil {return nil ,nil ,_dga ;};_aga .BBox =_bf .ToPdfObject ();_ef :=_cd .MakeDict ();_ef .Set ("\u004e",_aga .ToPdfObject ());return _ef ,_agee ,nil ;};func _dgd (_ccg *_ceg .PdfAnnotationWidget ,_gbdb *_ceg .PdfFieldButton ,_cgbg *_ceg .PdfPageResources ,_dece AppearanceStyle )(*_cd .PdfObjectDictionary ,error ){_dcd ,_dee :=_cd .GetArray (_ccg .Rect );
if !_dee {return nil ,_ceb .New ("\u0069\u006e\u0076a\u006c\u0069\u0064\u0020\u0052\u0065\u0063\u0074");};_bcac ,_ddac :=_ceg .NewPdfRectangle (*_dcd );if _ddac !=nil {return nil ,_ddac ;};_dgfd ,_ebd :=_bcac .Width (),_bcac .Height ();_cce ,_dea :=_dgfd ,_ebd ;
_f .Log .Debug ("\u0043\u0068\u0065\u0063kb\u006f\u0078\u002c\u0020\u0077\u0061\u0020\u0042\u0053\u003a\u0020\u0025\u0076",_ccg .BS );_baga ,_ddac :=_ceg .NewStandard14Font ("\u005a\u0061\u0070f\u0044\u0069\u006e\u0067\u0062\u0061\u0074\u0073");if _ddac !=nil {return nil ,_ddac ;
};_fcf ,_afc :=_cd .GetDict (_ccg .MK );if _afc {_eac ,_ :=_cd .GetDict (_ccg .BS );_eded :=_dece .applyAppearanceCharacteristics (_fcf ,_eac ,_baga );if _eded !=nil {return nil ,_eded ;};};_aab :=_ceg .NewXObjectForm ();{_cdf :=_c .NewContentCreator ();
if _dece .BorderSize > 0{_dfcd (_cdf ,_dece ,_dgfd ,_ebd );};if _dece .DrawAlignmentReticle {_cfda :=_dece ;_cfda .BorderSize =0.2;_fcb (_cdf ,_cfda ,_dgfd ,_ebd );};_dgfd ,_ebd =_dece .applyRotation (_fcf ,_dgfd ,_ebd ,_cdf );_dcbb :=_dece .AutoFontSizeFraction *_ebd ;
_acd ,_eef :=_baga .GetRuneMetrics (_dece .CheckmarkRune );if !_eef {return nil ,_ceb .New ("\u0067l\u0079p\u0068\u0020\u006e\u006f\u0074\u0020\u0066\u006f\u0075\u006e\u0064");};_dbd :=_baga .Encoder ();_abgb :=_dbd .Encode (string (_dece .CheckmarkRune ));
_gaac :=_acd .Wx *_dcbb /1000.0;_cbd :=705.0;_dbec :=_cbd /1000.0*_dcbb ;_fccf :=_cad ;if _dece .MarginLeft !=nil {_fccf =*_dece .MarginLeft ;};_dceb :=1.0;if _gaac < _dgfd {_fccf =(_dgfd -_gaac )/2.0;};if _dbec < _ebd {_dceb =(_ebd -_dbec )/2.0;};_cdf .Add_q ().Add_g (0).Add_BT ().Add_Tf ("\u005a\u0061\u0044\u0062",_dcbb ).Add_Td (_fccf ,_dceb ).Add_Tj (*_cd .MakeStringFromBytes (_abgb )).Add_ET ().Add_Q ();
_aab .Resources =_ceg .NewPdfPageResources ();_aab .Resources .SetFontByName ("\u005a\u0061\u0044\u0062",_baga .ToPdfObject ());_aab .BBox =_cd .MakeArrayFromFloats ([]float64 {0,0,_cce ,_dea });_aab .SetContentStream (_cdf .Bytes (),_adfb ());};_aabg :=_ceg .NewXObjectForm ();
{_ggd :=_c .NewContentCreator ();if _dece .BorderSize > 0{_dfcd (_ggd ,_dece ,_dgfd ,_ebd );};_aabg .BBox =_cd .MakeArrayFromFloats ([]float64 {0,0,_cce ,_dea });_aabg .SetContentStream (_ggd .Bytes (),_adfb ());};_egc :=_cd .PdfObjectName ("\u0059\u0065\u0073");
_ggf ,_afc :=_cd .GetDict (_ccg .PdfAnnotation .AP );if _afc &&_ggf !=nil {_ecgd :=_cd .TraceToDirectObject (_ggf .Get ("\u004e"));switch _ebf :=_ecgd .(type ){case *_cd .PdfObjectDictionary :_eace :=_ebf .Keys ();for _ ,_deab :=range _eace {if _deab !="\u004f\u0066\u0066"{_egc =_deab ;
};};};};_ccce :=_cd .MakeDict ();_ccce .Set ("\u004f\u0066\u0066",_aabg .ToPdfObject ());_ccce .Set (_egc ,_aab .ToPdfObject ());_cabe :=_cd .MakeDict ();_cabe .Set ("\u004e",_ccce );return _cabe ,nil ;};

// ComboboxFieldOptions defines optional parameters for a combobox form field.
type ComboboxFieldOptions struct{

// Choices is the list of string values that can be selected.
Choices []string ;};func _gafc (_cdacd LineAnnotationDef ,_ceec string )([]byte ,*_ceg .PdfRectangle ,*_ceg .PdfRectangle ,error ){_bcec :=_ba .Line {X1 :0,Y1 :0,X2 :_cdacd .X2 -_cdacd .X1 ,Y2 :_cdacd .Y2 -_cdacd .Y1 ,LineColor :_cdacd .LineColor ,Opacity :_cdacd .Opacity ,LineWidth :_cdacd .LineWidth ,LineEndingStyle1 :_cdacd .LineEndingStyle1 ,LineEndingStyle2 :_cdacd .LineEndingStyle2 };
_febb ,_effc ,_gbgc :=_bcec .Draw (_ceec );if _gbgc !=nil {return nil ,nil ,nil ,_gbgc ;};_ecb :=&_ceg .PdfRectangle {};_ecb .Llx =_cdacd .X1 +_effc .Llx ;_ecb .Lly =_cdacd .Y1 +_effc .Lly ;_ecb .Urx =_cdacd .X1 +_effc .Urx ;_ecb .Ury =_cdacd .Y1 +_effc .Ury ;
return _febb ,_effc ,_ecb ,nil ;};func _fcb (_egbdf *_c .ContentCreator ,_cddg AppearanceStyle ,_dfee ,_acb float64 ){_egbdf .Add_q ().Add_re (0,0,_dfee ,_acb ).Add_re (0,_acb /2,_dfee ,_acb /2).Add_re (0,0,_dfee ,_acb ).Add_re (_dfee /2,0,_dfee /2,_acb ).Add_w (_cddg .BorderSize ).SetStrokingColor (_cddg .BorderColor ).SetNonStrokingColor (_cddg .FillColor ).Add_B ().Add_Q ();
};

// NewSignatureLine returns a new signature line displayed as a part of the
// signature field appearance.
func NewSignatureLine (desc ,text string )*SignatureLine {return &SignatureLine {Desc :desc ,Text :text };};func _aabf (_agf *_ceg .PdfPage ,_gffa _ba .Rectangle ,_bfda string ,_eefe string ,_cgbc _ceg .PdfColor ,_ddd *_ceg .PdfFont ,_bceg *float64 ,_cagb _cd .PdfObject )(*_ceg .PdfFieldButton ,error ){_abef ,_gdec :=_gffa .X ,_gffa .Y ;
_bede :=_gffa .Width ;_edf :=_gffa .Height ;if _gffa .FillColor ==nil {_gffa .FillColor =_ceg .NewPdfColorDeviceGray (0.7);};if _cgbc ==nil {_cgbc =_ceg .NewPdfColorDeviceGray (0);};if _ddd ==nil {_beb ,_gdbag :=_ceg .NewStandard14Font ("\u0048e\u006c\u0076\u0065\u0074\u0069\u0063a");
if _gdbag !=nil {return nil ,_gdbag ;};_ddd =_beb ;};_edeb :=_ceg .NewPdfField ();_aaff :=&_ceg .PdfFieldButton {};_edeb .SetContext (_aaff );_aaff .PdfField =_edeb ;_aaff .T =_cd .MakeString (_bfda );_aaff .SetType (_ceg .ButtonTypePush );_aaff .V =_cd .MakeName ("\u004f\u0066\u0066");
_aaff .Ff =_cd .MakeInteger (4);_edb :=_cd .MakeDict ();_edb .Set (*_cd .MakeName ("\u0043\u0041"),_cd .MakeString (_eefe ));_bbdf ,_bece :=_ddd .GetFontDescriptor ();if _bece !=nil {return nil ,_bece ;};_gdbca :=_cd .MakeName ("\u0048e\u006c\u0076\u0065\u0074\u0069\u0063a");
_ecaf :=12.0;if _bbdf !=nil &&_bbdf .FontName !=nil {_gdbca ,_ =_cd .GetName (_bbdf .FontName );};if _bceg !=nil {_ecaf =*_bceg ;};_dcgf :=_c .NewContentCreator ();_dcgf .Add_q ();_dcgf .SetNonStrokingColor (_gffa .FillColor );_dcgf .Add_re (0,0,_bede ,_edf );
_dcgf .Add_f ();_dcgf .Add_Q ();_dcgf .Add_q ();_dcgf .Add_BT ();_cagg :=0.0;for _ ,_bfbf :=range _eefe {_ddcb ,_eegg :=_ddd .GetRuneMetrics (_bfbf );if !_eegg {_f .Log .Debug ("\u0046\u006f\u006e\u0074\u0020\u0064o\u0065\u0073\u0020\u006e\u006f\u0074\u0020\u0068\u0061\u0076\u0065\u0020\u0072\u0075\u006e\u0065\u0020\u006d\u0065\u0074r\u0069\u0063\u0073\u0020\u0066\u006f\u0072\u0020\u0025\u0076\u0020\u002d\u0020\u0073k\u0069p\u0070\u0069\u006e\u0067",_bfbf );
continue ;};_cagg +=_ddcb .Wx ;};_cagg =_cagg /1000.0*_ecaf ;var _eae float64 ;if _bbdf !=nil {_eae ,_bece =_bbdf .GetCapHeight ();if _bece !=nil {_f .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u0055\u006e\u0061\u0062\u006c\u0065 \u0074\u006f\u0020\u0067\u0065\u0074 \u0066\u006f\u006e\u0074\u0020\u0043\u0061\u0070\u0048\u0065\u0069\u0067\u0068t\u003a\u0020\u0025\u0076",_bece );
};};if int (_eae )<=0{_f .Log .Debug ("W\u0041\u0052\u004e\u003a\u0020\u0043\u0061\u0070\u0048e\u0069\u0067\u0068\u0074\u0020\u006e\u006ft \u0061\u0076\u0061\u0069l\u0061\u0062\u006c\u0065\u0020\u002d\u0020\u0073\u0065tt\u0069\u006eg\u0020\u0074\u006f\u0020\u0031\u0030\u0030\u0030");
_eae =1000;};_fage :=_eae /1000.0*_ecaf ;_bega :=(_edf -_fage )/2.0;_gbea :=(_bede -_cagg )/2.0;_dcgf .Add_Tf (*_gdbca ,_ecaf );_dcgf .SetNonStrokingColor (_cgbc );_dcgf .Add_Td (_gbea ,_bega );_dcgf .Add_Tj (*_cd .MakeString (_eefe ));_dcgf .Add_ET ();
_dcgf .Add_Q ();_dbfg :=_ceg .NewXObjectForm ();_dbfg .SetContentStream (_dcgf .Bytes (),_cd .NewRawEncoder ());_dbfg .BBox =_cd .MakeArrayFromFloats ([]float64 {0,0,_bede ,_edf });_dbfg .Resources =_ceg .NewPdfPageResources ();_dbfg .Resources .SetFontByName (*_gdbca ,_ddd .ToPdfObject ());
_aad :=_cd .MakeDict ();_aad .Set ("\u004e",_dbfg .ToPdfObject ());_gccf :=_ceg .NewPdfAnnotationWidget ();_gccf .Rect =_cd .MakeArrayFromFloats ([]float64 {_abef ,_gdec ,_abef +_bede ,_gdec +_edf });_gccf .P =_agf .ToPdfObject ();_gccf .F =_cd .MakeInteger (4);
_gccf .Parent =_aaff .ToPdfObject ();_gccf .A =_cagb ;_gccf .MK =_edb ;_gccf .AP =_aad ;_aaff .Annotations =append (_aaff .Annotations ,_gccf );return _aaff ,nil ;};

// TextFieldOptions defines optional parameter for a text field in a form.
type TextFieldOptions struct{MaxLen int ;Value string ;

// TextColor defines the color of the text in hex format. e.g #43fd23.
// If it has an invalid value a #000000 (black) color is taken as default
TextColor string ;

// FontName defines the font of the text. Helvetica font is the default one.
// It is recommended to use one of 14 standard PDF fonts.
FontName string ;

// FontSize defines the font size of the text, 12 is used by default.
FontSize int ;};

// WrapContentStream ensures that the entire content stream for a `page` is wrapped within q ... Q operands.
// Ensures that following operands that are added are not affected by additional operands that are added.
// Implements interface model.ContentStreamWrapper.
func (_gdef FieldAppearance )WrapContentStream (page *_ceg .PdfPage )error {_febf ,_gdefe :=page .GetAllContentStreams ();if _gdefe !=nil {return _gdefe ;};_edg :=_c .NewContentStreamParser (_febf );_bfd ,_gdefe :=_edg .Parse ();if _gdefe !=nil {return _gdefe ;
};_bfd .WrapIfNeeded ();_acg :=[]string {_bfd .String ()};return page .SetContentStreams (_acg ,_adfb ());};

// NewFormSubmitButtonField would create a submit button in specified page according to the parameter in `FormSubmitActionOptions`.
func NewFormSubmitButtonField (page *_ceg .PdfPage ,opt FormSubmitActionOptions )(*_ceg .PdfFieldButton ,error ){_bed :=int64 (_eebag );if opt .IsExclusionList {_bed |=_eeeg ;};if opt .IncludeEmptyFields {_bed |=_abgf ;};if opt .SubmitAsPDF {_bed |=_fdcd ;
};_fbea :=_ceg .NewPdfActionSubmitForm ();_fbea .Flags =_cd .MakeInteger (_bed );_fbea .F =_ceg .NewPdfFilespec ();if opt .Fields !=nil {_fbea .Fields =opt .Fields ;};_fbea .F .F =_cd .MakeString (opt .Url );_fbea .F .FS =_cd .MakeName ("\u0055\u0052\u004c");
_cdc ,_ddfe :=_aabf (page ,opt .Rectangle ,"\u0062t\u006e\u0053\u0075\u0062\u006d\u0069t",opt .Label ,opt .LabelColor ,opt .Font ,opt .FontSize ,_fbea .ToPdfObject ());if _ddfe !=nil {return nil ,_ddfe ;};return _cdc ,nil ;};

// InkAnnotationDef holds base information for constructing an ink annotation.
type InkAnnotationDef struct{

// Paths is the array of stroked paths which compose the annotation.
Paths []_ba .Path ;

// Color is the color of the line. Default to black.
Color *_ceg .PdfColorDeviceRGB ;

// LineWidth is the width of the line.
LineWidth float64 ;};func _affe (_ecgdb []*SignatureLine ,_fbee *SignatureFieldOpts )(*_cd .PdfObjectDictionary ,error ){if _fbee ==nil {_fbee =NewSignatureFieldOpts ();};var _bec error ;var _aggc *_cd .PdfObjectName ;_fcff :=_fbee .Font ;if _fcff !=nil {_agdg ,_ :=_fcff .GetFontDescriptor ();
if _agdg !=nil {if _fdgc ,_baa :=_agdg .FontName .(*_cd .PdfObjectName );_baa {_aggc =_fdgc ;};};if _aggc ==nil {_aggc =_cd .MakeName ("\u0046\u006f\u006et\u0031");};}else {if _fcff ,_bec =_ceg .NewStandard14Font ("\u0048e\u006c\u0076\u0065\u0074\u0069\u0063a");
_bec !=nil {return nil ,_bec ;};_aggc =_cd .MakeName ("\u0048\u0065\u006c\u0076");};_dcbeb :=_fbee .FontSize ;if _dcbeb <=0{_dcbeb =10;};if _fbee .LineHeight <=0{_fbee .LineHeight =1;};_efa :=_fbee .LineHeight *_dcbeb ;_ccgg ,_adea :=_fcff .GetRuneMetrics (' ');
if !_adea {return nil ,_ceb .New ("\u0074\u0068e \u0066\u006f\u006et\u0020\u0064\u006f\u0065s n\u006ft \u0068\u0061\u0076\u0065\u0020\u0061\u0020sp\u0061\u0063\u0065\u0020\u0067\u006c\u0079p\u0068");};_cbea :=_ccgg .Wx ;var _fcbd float64 ;var _cfff []string ;
for _ ,_fgfe :=range _ecgdb {if _fgfe .Text ==""{continue ;};_bbcc :=_fgfe .Text ;if _fgfe .Desc !=""{_bbcc =_fgfe .Desc +"\u003a\u0020"+_bbcc ;};_cfff =append (_cfff ,_bbcc );var _bacg float64 ;for _ ,_ebff :=range _bbcc {_baed ,_gaga :=_fcff .GetRuneMetrics (_ebff );
if !_gaga {continue ;};_bacg +=_baed .Wx ;};if _bacg > _fcbd {_fcbd =_bacg ;};};_fcbd =_fcbd *_dcbeb /1000.0;_cgdd :=float64 (len (_cfff ))*_efa ;_beee :=_fbee .Image !=nil ;_gbee :=_fbee .Rect ;if _gbee ==nil {_gbee =[]float64 {0,0,_fcbd ,_cgdd };if _beee {_gbee [2]=_fcbd *2;
_gbee [3]=_cgdd *2;};_fbee .Rect =_gbee ;};_cccc :=_gbee [2]-_gbee [0];_abff :=_gbee [3]-_gbee [1];_cdfb ,_dfcag :=_gbee ,_gbee ;var _bcga ,_adgd float64 ;if _beee &&len (_cfff )> 0{if _fbee .ImagePosition <=SignatureImageRight {_cec :=[]float64 {_gbee [0],_gbee [1],_gbee [0]+(_cccc /2),_gbee [3]};
_faa :=[]float64 {_gbee [0]+(_cccc /2),_gbee [1],_gbee [2],_gbee [3]};if _fbee .ImagePosition ==SignatureImageLeft {_cdfb ,_dfcag =_cec ,_faa ;}else {_cdfb ,_dfcag =_faa ,_cec ;};}else {_dfda :=[]float64 {_gbee [0],_gbee [1],_gbee [2],_gbee [1]+(_abff /2)};
_degg :=[]float64 {_gbee [0],_gbee [1]+(_abff /2),_gbee [2],_gbee [3]};if _fbee .ImagePosition ==SignatureImageTop {_cdfb ,_dfcag =_degg ,_dfda ;}else {_cdfb ,_dfcag =_dfda ,_degg ;};};};_bcga =_dfcag [2]-_dfcag [0];_adgd =_dfcag [3]-_dfcag [1];var _fff float64 ;
if _fbee .AutoSize {if _fcbd > _bcga ||_cgdd > _adgd {_acgf :=_fc .Min (_bcga /_fcbd ,_adgd /_cgdd );_dcbeb *=_acgf ;};_efa =_fbee .LineHeight *_dcbeb ;_fff +=(_adgd -float64 (len (_cfff ))*_efa )/2;};_fegc :=_c .NewContentCreator ();_caag :=_ceg .NewPdfPageResources ();
_caag .SetFontByName (*_aggc ,_fcff .ToPdfObject ());if _fbee .BorderSize <=0{_fbee .BorderSize =0;_fbee .BorderColor =_ceg .NewPdfColorDeviceGray (1);};_fegc .Add_q ();if _fbee .FillColor !=nil {_fegc .SetNonStrokingColor (_fbee .FillColor );};if _fbee .BorderColor !=nil {_fegc .SetStrokingColor (_fbee .BorderColor );
};_fegc .Add_w (_fbee .BorderSize ).Add_re (_gbee [0],_gbee [1],_cccc ,_abff );if _fbee .FillColor !=nil &&_fbee .BorderColor !=nil {_fegc .Add_B ();}else if _fbee .FillColor !=nil {_fegc .Add_f ();}else if _fbee .BorderColor !=nil {_fegc .Add_S ();};_fegc .Add_Q ();
if _fbee .WatermarkImage !=nil {_debbe :=[]float64 {_gbee [0],_gbee [1],_gbee [2],_gbee [3]};_acdd ,_gda ,_afdef :=_dgdf (_fbee .WatermarkImage ,"\u0049\u006d\u0061\u0067\u0065\u0057\u0061\u0074\u0065r\u006d\u0061\u0072\u006b",_fbee ,_debbe ,_fegc );if _afdef !=nil {return nil ,_afdef ;
};_caag .SetXObjectImageByName (*_acdd ,_gda );};_fegc .Add_q ();_fegc .Translate (_dfcag [0],_dfcag [3]-_efa -_fff );_fegc .Add_BT ();_cfgg :=_fcff .Encoder ();for _ ,_eabfg :=range _cfff {var _eabfc []byte ;for _ ,_ebbb :=range _eabfg {if _b .IsSpace (_ebbb ){if len (_eabfc )> 0{_fegc .SetNonStrokingColor (_fbee .TextColor ).Add_Tf (*_aggc ,_dcbeb ).Add_TL (_efa ).Add_TJ ([]_cd .PdfObject {_cd .MakeStringFromBytes (_eabfc )}...);
_eabfc =nil ;};_fegc .Add_Tf (*_aggc ,_dcbeb ).Add_TL (_efa ).Add_TJ ([]_cd .PdfObject {_cd .MakeFloat (-_cbea )}...);}else {_eabfc =append (_eabfc ,_cfgg .Encode (string (_ebbb ))...);};};if len (_eabfc )> 0{_fegc .SetNonStrokingColor (_fbee .TextColor ).Add_Tf (*_aggc ,_dcbeb ).Add_TL (_efa ).Add_TJ ([]_cd .PdfObject {_cd .MakeStringFromBytes (_eabfc )}...);
};_fegc .Add_Td (0,-_efa );};_fegc .Add_ET ();_fegc .Add_Q ();if _beee {_bdcf ,_aefa ,_cddb :=_dgdf (_fbee .Image ,"\u0049\u006d\u0061\u0067\u0065\u0053\u0069\u0067\u006ea\u0074\u0075\u0072\u0065",_fbee ,_cdfb ,_fegc );if _cddb !=nil {return nil ,_cddb ;
};_caag .SetXObjectImageByName (*_bdcf ,_aefa );};_ggc :=_ceg .NewXObjectForm ();_ggc .Resources =_caag ;_ggc .BBox =_cd .MakeArrayFromFloats (_gbee );_ggc .SetContentStream (_fegc .Bytes (),_adfb ());_fegcd :=_cd .MakeDict ();_fegcd .Set ("\u004e",_ggc .ToPdfObject ());
return _fegcd ,nil ;};

// NewSignatureFieldOpts returns a new initialized instance of options
// used to generate a signature appearance.
func NewSignatureFieldOpts ()*SignatureFieldOpts {return &SignatureFieldOpts {Font :_ceg .DefaultFont (),FontSize :10,LineHeight :1,AutoSize :true ,TextColor :_ceg .NewPdfColorDeviceGray (0),BorderColor :_ceg .NewPdfColorDeviceGray (0),FillColor :_ceg .NewPdfColorDeviceGray (1),Encoder :_cd .NewFlateEncoder (),ImagePosition :SignatureImageLeft };
};

// FieldAppearance implements interface model.FieldAppearanceGenerator and generates appearance streams
// for fields taking into account what value is in the field. A common use case is for generating the
// appearance stream prior to flattening fields.
//
// If `OnlyIfMissing` is true, the field appearance is generated only for fields that do not have an
// appearance stream specified.
// If `RegenerateTextFields` is true, all text fields are regenerated (even if OnlyIfMissing is true).
type FieldAppearance struct{OnlyIfMissing bool ;RegenerateTextFields bool ;_cab *AppearanceStyle ;};

// AppearanceFont represents a font used for generating the appearance of a
// field in the filling/flattening process.
type AppearanceFont struct{

// Name represents the name of the font which will be added to the
// AcroForm resources (DR).
Name string ;

// Font represents the actual font used for the field appearance.
Font *_ceg .PdfFont ;

// Size represents the size of the font used for the field appearance.
// If the font size is 0, the value of the FallbackSize field of the
// AppearanceFontStyle is used, if set. Otherwise, the font size is
// calculated based on the available annotation height and on the
// AutoFontSizeFraction field of the AppearanceStyle.
Size float64 ;};func _fcba (_dgbc LineAnnotationDef )(*_cd .PdfObjectDictionary ,*_ceg .PdfRectangle ,error ){_efaf :=_ceg .NewXObjectForm ();_efaf .Resources =_ceg .NewPdfPageResources ();_gdeg :="";if _dgbc .Opacity < 1.0{_bdg :=_cd .MakeDict ();_bdg .Set ("\u0063\u0061",_cd .MakeFloat (_dgbc .Opacity ));
_cfea :=_efaf .Resources .AddExtGState ("\u0067\u0073\u0031",_bdg );if _cfea !=nil {_f .Log .Debug ("U\u006e\u0061\u0062\u006c\u0065\u0020t\u006f\u0020\u0061\u0064\u0064\u0020\u0065\u0078\u0074g\u0073\u0074\u0061t\u0065 \u0067\u0073\u0031");return nil ,nil ,_cfea ;
};_gdeg ="\u0067\u0073\u0031";};_fgfd ,_bgcb ,_ebfd ,_faga :=_gafc (_dgbc ,_gdeg );if _faga !=nil {return nil ,nil ,_faga ;};_faga =_efaf .SetContentStream (_fgfd ,nil );if _faga !=nil {return nil ,nil ,_faga ;};_efaf .BBox =_bgcb .ToPdfObject ();_acfb :=_cd .MakeDict ();
_acfb .Set ("\u004e",_efaf .ToPdfObject ());return _acfb ,_ebfd ,nil ;};

// AppearanceFontStyle defines font style characteristics for form fields,
// used in the filling/flattening process.
type AppearanceFontStyle struct{

// Fallback represents a global font fallback, used for fields which do
// not specify a font in their default appearance (DA). The fallback is
// also used if there is a font specified in the DA, but it is not
// found in the AcroForm resources (DR).
Fallback *AppearanceFont ;

// FallbackSize represents a global font size fallback used for fields
// which do not specify a font size in their default appearance (DA).
// The fallback size is applied only if its value is larger than zero.
FallbackSize float64 ;

// FieldFallbacks defines font fallbacks for specific fields. The map keys
// represent the names of the fields (which can be specified by their
// partial or full names). Specific field fallback fonts take precedence
// over the global font fallback.
FieldFallbacks map[string ]*AppearanceFont ;

// ForceReplace forces the replacement of fonts in the filling/flattening
// process, even if the default appearance (DA) specifies a valid font.
// If no fallback font is provided, setting this field has no effect.
ForceReplace bool ;};

// SignatureImagePosition specifies the image signature location relative to the text signature.
// If text signature is not defined, this position will be ignored.
type SignatureImagePosition int ;func (_abf *AppearanceStyle )applyRotation (_bab *_cd .PdfObjectDictionary ,_gfa ,_adf float64 ,_ddcf *_c .ContentCreator )(float64 ,float64 ){if !_abf .AllowMK {return _gfa ,_adf ;};if _bab ==nil {return _gfa ,_adf ;};
_dccb ,_ :=_cd .GetNumberAsFloat (_bab .Get ("\u0052"));if _dccb ==0{return _gfa ,_adf ;};_feec :=-_dccb ;_ebbg :=_ba .Path {Points :[]_ba .Point {_ba .NewPoint (0,0).Rotate (_feec ),_ba .NewPoint (_gfa ,0).Rotate (_feec ),_ba .NewPoint (0,_adf ).Rotate (_feec ),_ba .NewPoint (_gfa ,_adf ).Rotate (_feec )}}.GetBoundingBox ();
_ddcf .RotateDeg (_dccb );_ddcf .Translate (_ebbg .X ,_ebbg .Y );return _ebbg .Width ,_ebbg .Height ;};

// CircleAnnotationDef defines a circle annotation or ellipse at position (X, Y) and Width and Height.
// The annotation has various style parameters including Fill and Border options and Opacity.
type CircleAnnotationDef struct{X float64 ;Y float64 ;Width float64 ;Height float64 ;FillEnabled bool ;FillColor *_ceg .PdfColorDeviceRGB ;BorderEnabled bool ;BorderWidth float64 ;BorderColor *_ceg .PdfColorDeviceRGB ;Opacity float64 ;};

// CreateRectangleAnnotation creates a rectangle annotation object that can be added to page PDF annotations.
func CreateRectangleAnnotation (rectDef RectangleAnnotationDef )(*_ceg .PdfAnnotation ,error ){_fefce :=_ceg .NewPdfAnnotationSquare ();if rectDef .BorderEnabled {_cgee ,_bcff ,_defd :=rectDef .BorderColor .R (),rectDef .BorderColor .G (),rectDef .BorderColor .B ();
_fefce .C =_cd .MakeArrayFromFloats ([]float64 {_cgee ,_bcff ,_defd });_cabec :=_ceg .NewBorderStyle ();_cabec .SetBorderWidth (rectDef .BorderWidth );_fefce .BS =_cabec .ToPdfObject ();};if rectDef .FillEnabled {_eccdc ,_gbcd ,_baad :=rectDef .FillColor .R (),rectDef .FillColor .G (),rectDef .FillColor .B ();
_fefce .IC =_cd .MakeArrayFromFloats ([]float64 {_eccdc ,_gbcd ,_baad });}else {_fefce .IC =_cd .MakeArrayFromIntegers ([]int {});};if rectDef .Opacity < 1.0{_fefce .CA =_cd .MakeFloat (rectDef .Opacity );};_ddbb ,_cef ,_cbge :=_eebb (rectDef );if _cbge !=nil {return nil ,_cbge ;
};_fefce .AP =_ddbb ;_fefce .Rect =_cd .MakeArrayFromFloats ([]float64 {_cef .Llx ,_cef .Lly ,_cef .Urx ,_cef .Ury });return _fefce .PdfAnnotation ,nil ;};

// RectangleAnnotationDef is a rectangle defined with a specified Width and Height and a lower left corner at (X,Y).
// The rectangle can optionally have a border and a filling color.
// The Width/Height includes the border (if any specified).
type RectangleAnnotationDef struct{X float64 ;Y float64 ;Width float64 ;Height float64 ;FillEnabled bool ;FillColor *_ceg .PdfColorDeviceRGB ;BorderEnabled bool ;BorderWidth float64 ;BorderColor *_ceg .PdfColorDeviceRGB ;Opacity float64 ;};

// FileAnnotationDef holds base information for constructing an file attachment annotation.
type FileAnnotationDef struct{

// Bounding box of the annotation.
X float64 ;Y float64 ;Width float64 ;Height float64 ;

// EmbeddedFile is the file information to be attached.
EmbeddedFile *_ceg .EmbeddedFile ;

// Author is the author of the attachment file.
Author string ;

// Subject is the subject of the attachment file.
Subject string ;

// Description of the file attachment that will be displayed as a comment on the PDF reader.
Description string ;

// IconName is The name of an icon that shall be used in displaying the annotation.
// Conforming readers shall provide predefined icon appearances for at least the following standard names:
//
// - Graph
// - PushPin
// - Paperclip
// - Tag
//
// Additional names may be supported as well. Default value: "PushPin".
IconName string ;

// Color is the color of the annotation.
Color *_ceg .PdfColorDeviceRGB ;

// CreationDate is the date and time when the file attachment was created.
// If not set, the current time is used.
CreationDate *_e .Time ;};func _aac (_agggg _cd .PdfObject ,_efec *_ceg .PdfPageResources )(*_cd .PdfObjectName ,float64 ,bool ){var (_ddaf *_cd .PdfObjectName ;_babb float64 ;_dacg bool ;);if _bffd ,_cgad :=_cd .GetDict (_agggg );_cgad &&_bffd !=nil {_aaba :=_cd .TraceToDirectObject (_bffd .Get ("\u004e"));
switch _gccb :=_aaba .(type ){case *_cd .PdfObjectStream :_beg ,_fbc :=_cd .DecodeStream (_gccb );if _fbc !=nil {_f .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u0020\u0075\u006e\u0061\u0062\u006c\u0065\u0020\u0064\u0065\u0063\u006f\u0064\u0065\u0020\u0063\u006f\u006e\u0074e\u006e\u0074\u0020\u0073\u0074r\u0065\u0061m\u003a\u0020\u0025\u0076",_fbc .Error ());
return nil ,0,false ;};_egda ,_fbc :=_c .NewContentStreamParser (string (_beg )).Parse ();if _fbc !=nil {_f .Log .Debug ("\u0045\u0052R\u004f\u0052\u0020\u0075n\u0061\u0062l\u0065\u0020\u0070\u0061\u0072\u0073\u0065\u0020c\u006f\u006e\u0074\u0065\u006e\u0074\u0020\u0073\u0074\u0072\u0065\u0061m\u003a\u0020\u0025\u0076",_fbc .Error ());
return nil ,0,false ;};_dgdffd :=_c .NewContentStreamProcessor (*_egda );_dgdffd .AddHandler (_c .HandlerConditionEnumOperand ,"\u0054\u0066",func (_baca *_c .ContentStreamOperation ,_gfg _c .GraphicsState ,_ddag *_ceg .PdfPageResources )error {if len (_baca .Params )==2{if _ffb ,_fbbc :=_cd .GetName (_baca .Params [0]);
_fbbc {_ddaf =_ffb ;};if _aggb ,_eeac :=_cd .GetNumberAsFloat (_baca .Params [1]);_eeac ==nil {_babb =_aggb ;};_dacg =true ;return _c .ErrEarlyExit ;};return nil ;});_dgdffd .Process (_efec );return _ddaf ,_babb ,_dacg ;};};return nil ,0,false ;};func _bfbe (_ffa RectangleAnnotationDef ,_efecd string )([]byte ,*_ceg .PdfRectangle ,*_ceg .PdfRectangle ,error ){_bbca :=_ba .Rectangle {X :0,Y :0,Width :_ffa .Width ,Height :_ffa .Height ,FillEnabled :_ffa .FillEnabled ,FillColor :_ffa .FillColor ,BorderEnabled :_ffa .BorderEnabled ,BorderWidth :2*_ffa .BorderWidth ,BorderColor :_ffa .BorderColor ,Opacity :_ffa .Opacity };
_ddebd ,_gadb ,_dcbg :=_bbca .Draw (_efecd );if _dcbg !=nil {return nil ,nil ,nil ,_dcbg ;};_cfbba :=&_ceg .PdfRectangle {};_cfbba .Llx =_ffa .X +_gadb .Llx ;_cfbba .Lly =_ffa .Y +_gadb .Lly ;_cfbba .Urx =_ffa .X +_gadb .Urx ;_cfbba .Ury =_ffa .Y +_gadb .Ury ;
return _ddebd ,_gadb ,_cfbba ,nil ;};func _fcgf (_efag []float64 )[]float64 {var (_eeacf =len (_efag );_bbg =make ([]float64 ,_eeacf );_fbaa =make ([]float64 ,_eeacf ););_eccde :=2.0;_bbg [0]=_efag [0]/_eccde ;for _eefa :=1;_eefa < _eeacf ;_eefa ++{_fbaa [_eefa ]=1/_eccde ;
if _eefa < _eeacf -1{_eccde =4.0;}else {_eccde =3.5;};_eccde -=_fbaa [_eefa ];_bbg [_eefa ]=(_efag [_eefa ]-_bbg [_eefa -1])/_eccde ;};for _ddga :=1;_ddga < _eeacf ;_ddga ++{_bbg [_eeacf -_ddga -1]-=_fbaa [_eeacf -_ddga ]*_bbg [_eeacf -_ddga ];};return _bbg ;
};

// SignatureLine represents a line of information in the signature field appearance.
type SignatureLine struct{Desc string ;Text string ;};func _eebb (_ggeb RectangleAnnotationDef )(*_cd .PdfObjectDictionary ,*_ceg .PdfRectangle ,error ){_geabg :=_ceg .NewXObjectForm ();_geabg .Resources =_ceg .NewPdfPageResources ();_dfdg :="";if _ggeb .Opacity < 1.0{_fgaed :=_cd .MakeDict ();
_fgaed .Set ("\u0063\u0061",_cd .MakeFloat (_ggeb .Opacity ));_fgaed .Set ("\u0043\u0041",_cd .MakeFloat (_ggeb .Opacity ));_ffec :=_geabg .Resources .AddExtGState ("\u0067\u0073\u0031",_fgaed );if _ffec !=nil {_f .Log .Debug ("U\u006e\u0061\u0062\u006c\u0065\u0020t\u006f\u0020\u0061\u0064\u0064\u0020\u0065\u0078\u0074g\u0073\u0074\u0061t\u0065 \u0067\u0073\u0031");
return nil ,nil ,_ffec ;};_dfdg ="\u0067\u0073\u0031";};_aegbf ,_bfee ,_fcag ,_ccdg :=_bfbe (_ggeb ,_dfdg );if _ccdg !=nil {return nil ,nil ,_ccdg ;};_ccdg =_geabg .SetContentStream (_aegbf ,nil );if _ccdg !=nil {return nil ,nil ,_ccdg ;};_geabg .BBox =_bfee .ToPdfObject ();
_fegde :=_cd .MakeDict ();_fegde .Set ("\u004e",_geabg .ToPdfObject ());return _fegde ,_fcag ,nil ;};func _ggb (_fba *_ceg .PdfField ,_cgbe ,_fbac float64 ,_acea string ,_edd AppearanceStyle ,_dfdc *_c .ContentStreamOperations ,_fcg *_ceg .PdfPageResources ,_gfc *_cd .PdfObjectDictionary )(*_ceg .XObjectForm ,error ){_eeed :=_ceg .NewPdfPageResources ();
_daaf ,_bg :=_cgbe ,_fbac ;_gdd :=_c .NewContentCreator ();if _edd .BorderSize > 0{_dfcd (_gdd ,_edd ,_cgbe ,_fbac );};if _edd .DrawAlignmentReticle {_dfdf :=_edd ;_dfdf .BorderSize =0.2;_fcb (_gdd ,_dfdf ,_cgbe ,_fbac );};_gdd .Add_BMC ("\u0054\u0078");
_gdd .Add_q ();_gdd .Add_BT ();_cgbe ,_fbac =_edd .applyRotation (_gfc ,_cgbe ,_fbac ,_gdd );_bgf ,_dcg ,_gfd :=_edd .processDA (_fba ,_dfdc ,_fcg ,_eeed ,_gdd );if _gfd !=nil {return nil ,_gfd ;};_gfbg :=_bgf .Font ;_eabf :=_bgf .Size ;_dcf :=_cd .MakeName (_bgf .Name );
_feff :=_eabf ==0;if _feff &&_dcg {_eabf =_fbac *_edd .AutoFontSizeFraction ;};_bdac :=_gfbg .Encoder ();if _bdac ==nil {_f .Log .Debug ("\u0057\u0041RN\u003a\u0020\u0066\u006f\u006e\u0074\u0020\u0065\u006e\u0063\u006f\u0064\u0065\u0072\u0020\u0069\u0073\u0020\u006e\u0069l\u002e\u0020\u0041\u0073s\u0075\u006d\u0069\u006eg \u0069\u0064e\u006et\u0069\u0074\u0079\u0020\u0065\u006ec\u006f\u0064\u0065r\u002e\u0020O\u0075\u0074\u0070\u0075\u0074\u0020\u006d\u0061\u0079\u0020\u0062\u0065\u0020\u0069n\u0063\u006f\u0072\u0072\u0065\u0063\u0074\u002e");
_bdac =_dg .NewIdentityTextEncoder ("\u0049\u0064\u0065\u006e\u0074\u0069\u0074\u0079\u002d\u0048");};if len (_acea )==0{return nil ,nil ;};_gdf :=_cad ;if _edd .MarginLeft !=nil {_gdf =*_edd .MarginLeft ;};_adac :=0.0;if _bdac !=nil {for _ ,_dfg :=range _acea {_aaf ,_gcf :=_gfbg .GetRuneMetrics (_dfg );
if !_gcf {_f .Log .Debug ("\u0046\u006f\u006e\u0074\u0020\u0064o\u0065\u0073\u0020\u006e\u006f\u0074\u0020\u0068\u0061\u0076\u0065\u0020\u0072\u0075\u006e\u0065\u0020\u006d\u0065\u0074r\u0069\u0063\u0073\u0020\u0066\u006f\u0072\u0020\u0025\u0076\u0020\u002d\u0020\u0073k\u0069p\u0070\u0069\u006e\u0067",_dfg );
continue ;};_adac +=_aaf .Wx ;};_acea =string (_bdac .Encode (_acea ));};if _eabf ==0||_feff &&_adac > 0&&_gdf +_adac *_eabf /1000.0> _cgbe {_eabf =0.95*1000.0*(_cgbe -_gdf )/_adac ;};_bbd :=1.0*_eabf ;_fca :=2.0;{_aeg :=_bbd ;if _feff &&_fca +_aeg > _fbac {_eabf =0.95*(_fbac -_fca );
_bbd =1.0*_eabf ;_aeg =_bbd ;};if _fbac > _aeg {_fca =(_fbac -_aeg )/2.0;_fca +=1.50;};};_gdd .Add_Tf (*_dcf ,_eabf );_gdd .Add_Td (_gdf ,_fca );_gdd .Add_Tj (*_cd .MakeString (_acea ));_gdd .Add_ET ();_gdd .Add_Q ();_gdd .Add_EMC ();_cfaa :=_ceg .NewXObjectForm ();
_cfaa .Resources =_eeed ;_cfaa .BBox =_cd .MakeArrayFromFloats ([]float64 {0,0,_daaf ,_bg });_cfaa .SetContentStream (_gdd .Bytes (),_adfb ());return _cfaa ,nil ;};

// FormSubmitActionOptions holds options for creating a form submit button.
type FormSubmitActionOptions struct{

// Rectangle holds the button position, size, and color.
Rectangle _ba .Rectangle ;

// Url specifies the URL where the fieds will be submitted.
Url string ;

// Label specifies the text that would be displayed on the button.
Label string ;

// LabelColor specifies the button label color.
LabelColor _ceg .PdfColor ;

// Font specifies a font used for rendering the button label.
// When omitted it will fallback to use a Helvetica font.
Font *_ceg .PdfFont ;

// FontSize specifies the font size used in rendering the button label.
// The default font size is 12pt.
FontSize *float64 ;

// Fields specifies list of fields that could be submitted.
// This list may contain indirect object to fields or field names.
Fields *_cd .PdfObjectArray ;

// IsExclusionList specifies that the fields contain in `Fields` array would not be submitted.
IsExclusionList bool ;

// IncludeEmptyFields specifies if all fields would be submitted even though it's value is empty.
IncludeEmptyFields bool ;

// SubmitAsPDF specifies that the document shall be submitted as PDF.
// If set then all the other flags shall be ignored.
SubmitAsPDF bool ;};func _gagf (_bde *_ceg .PdfAnnotationWidget ,_dag *_ceg .PdfFieldText ,_db *_ceg .PdfPageResources ,_be AppearanceStyle )(*_cd .PdfObjectDictionary ,error ){_gde :=_ceg .NewPdfPageResources ();_agd ,_cfg :=_cd .GetArray (_bde .Rect );
if !_cfg {return nil ,_ceb .New ("\u0069\u006e\u0076a\u006c\u0069\u0064\u0020\u0052\u0065\u0063\u0074");};_bdde ,_bc :=_ceg .NewPdfRectangle (*_agd );if _bc !=nil {return nil ,_bc ;};_ad ,_ab :=_bdde .Width (),_bdde .Height ();_gc ,_faf :=_ad ,_ab ;_agg :=true ;
_egf :=_ceg .NewXObjectForm ();_egf .BBox =_cd .MakeArrayFromFloats ([]float64 {0,0,_gc ,_faf });if _bde .AP !=nil {if _efd ,_dcba :=_cd .GetDict (_bde .AP );_dcba &&_efd !=nil {_bdeb :=_cd .TraceToDirectObject (_efd .Get ("\u004e"));switch _cda :=_bdeb .(type ){case *_cd .PdfObjectStream :_bce ,_cag :=_cd .DecodeStream (_cda );
if _cag !=nil {_f .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u0020\u0075\u006e\u0061\u0062\u006c\u0065\u0020\u0064\u0065\u0063\u006f\u0064\u0065\u0020\u0063\u006f\u006e\u0074e\u006e\u0074\u0020\u0073\u0074r\u0065\u0061m\u003a\u0020\u0025\u0076",_cag .Error ());
break ;};_eea ,_cag :=_c .NewContentStreamParser (string (_bce )).Parse ();if _cag !=nil {_f .Log .Debug ("\u0045\u0052R\u004f\u0052\u0020\u0075n\u0061\u0062l\u0065\u0020\u0070\u0061\u0072\u0073\u0065\u0020c\u006f\u006e\u0074\u0065\u006e\u0074\u0020\u0073\u0074\u0072\u0065\u0061m\u003a\u0020\u0025\u0076",_cag .Error ());
break ;};_ddc :=_c .NewContentStreamProcessor (*_eea );_ddc .AddHandler (_c .HandlerConditionEnumAllOperands ,"",func (_dfd *_c .ContentStreamOperation ,_ceaf _c .GraphicsState ,_dadd *_ceg .PdfPageResources )error {if _dfd .Operand =="\u0054\u006a"||_dfd .Operand =="\u0054\u004a"{if len (_dfd .Params )==1{if _aa ,_dgfc :=_cd .GetString (_dfd .Params [0]);
_dgfc {_agg =_ce .TrimSpace (_aa .Str ())=="";};return _c .ErrEarlyExit ;};return nil ;};return nil ;});_ddc .Process (_gde );if !_agg {if _gbe ,_gcc :=_cd .GetDict (_cda .Get ("\u0052e\u0073\u006f\u0075\u0072\u0063\u0065s"));_gcc {_gde ,_cag =_ceg .NewPdfPageResourcesFromDict (_gbe );
if _cag !=nil {return nil ,_cag ;};};if _abg ,_eee :=_cd .GetArray (_cda .Get ("\u004d\u0061\u0074\u0072\u0069\u0078"));_eee {_egf .Matrix =_abg ;};_egf .SetContentStream (_bce ,_adfb ());};};};};if _agg {_egg ,_aefc :=_cd .GetDict (_bde .MK );if _aefc {_ed ,_ :=_cd .GetDict (_bde .BS );
_fgf :=_be .applyAppearanceCharacteristics (_egg ,_ed ,nil );if _fgf !=nil {return nil ,_fgf ;};};_bea ,_bbc :=_c .NewContentStreamParser (_cccf (_dag .PdfField )).Parse ();if _bbc !=nil {return nil ,_bbc ;};_egbd :=_c .NewContentCreator ();if _be .BorderSize > 0{_dfcd (_egbd ,_be ,_ad ,_ab );
};if _be .DrawAlignmentReticle {_cac :=_be ;_cac .BorderSize =0.2;_fcb (_egbd ,_cac ,_ad ,_ab );};_egbd .Add_BMC ("\u0054\u0078");_egbd .Add_q ();_ad ,_ab =_be .applyRotation (_egg ,_ad ,_ab ,_egbd );_egbd .Add_BT ();_ccb ,_cde ,_bbc :=_be .processDA (_dag .PdfField ,_bea ,_db ,_gde ,_egbd );
if _bbc !=nil {return nil ,_bbc ;};_abc :=_ccb .Font ;_fbf :=_ccb .Size ;_af :=_cd .MakeName (_ccb .Name );if _dag .Flags ().Has (_ceg .FieldFlagMultiline )&&_dag .MaxLen !=nil {_f .Log .Debug ("\u004c\u006f\u006f\u006b\u0020\u0066\u006f\u0072\u0020\u0041\u0050\u0020\u0064\u0069\u0063\u0074\u0069\u006fn\u0061\u0072\u0079\u0020\u0066\u006f\u0072 \u004e\u0020\u006f\u0062\u006a\u0065\u0063\u0074\u0020\u0063\u006fn\u0074\u0065\u006e\u0074\u0020\u0073\u0074\u0072\u0065\u0061\u006d");
if _afa ,_fag ,_aag :=_aac (_bde .PdfAnnotation .AP ,_db );_aag {_af =_afa ;_fbf =_fag ;_cde =true ;};};_fdd :=_fbf ==0;if _fdd &&_cde {_fbf =_ab *_be .AutoFontSizeFraction ;};_gce :=_abc .Encoder ();if _gce ==nil {_f .Log .Debug ("\u0057\u0041RN\u003a\u0020\u0066\u006f\u006e\u0074\u0020\u0065\u006e\u0063\u006f\u0064\u0065\u0072\u0020\u0069\u0073\u0020\u006e\u0069l\u002e\u0020\u0041\u0073s\u0075\u006d\u0069\u006eg \u0069\u0064e\u006et\u0069\u0074\u0079\u0020\u0065\u006ec\u006f\u0064\u0065r\u002e\u0020O\u0075\u0074\u0070\u0075\u0074\u0020\u006d\u0061\u0079\u0020\u0062\u0065\u0020\u0069n\u0063\u006f\u0072\u0072\u0065\u0063\u0074\u002e");
_gce =_dg .NewIdentityTextEncoder ("\u0049\u0064\u0065\u006e\u0074\u0069\u0074\u0079\u002d\u0048");};_gdb ,_bbc :=_abc .GetFontDescriptor ();if _bbc !=nil {_f .Log .Debug ("\u0045\u0072ro\u0072\u003a\u0020U\u006e\u0061\u0062\u006ce t\u006f g\u0065\u0074\u0020\u0066\u006f\u006e\u0074 d\u0065\u0073\u0063\u0072\u0069\u0070\u0074o\u0072");
};var _dbb string ;if _agea ,_agb :=_cd .GetString (_dag .V );_agb {_dbb =_agea .Decoded ();};if len (_dbb )==0{return nil ,nil ;};_bcg :=[]string {_dbb };_cdgb :=false ;if _dag .Flags ().Has (_ceg .FieldFlagMultiline ){_cdgb =true ;_dbb =_ce .Replace (_dbb ,"\u000d\u000a","\u000a",-1);
_dbb =_ce .Replace (_dbb ,"\u000d","\u000a",-1);_bcg =_ce .Split (_dbb ,"\u000a");};_gef :=make ([]string ,len (_bcg ));copy (_gef ,_bcg );_dbf :=_be .MultilineLineHeight ;_dec :=0.0;_cgd :=0;if _gce !=nil {for _fbf >=0{_fge :=make ([]string ,len (_bcg ));
copy (_fge ,_bcg );_gbf :=make ([]string ,len (_gef ));copy (_gbf ,_gef );_dec =0.0;_cgd =0;_ccc :=len (_fge );_bdec :=0;for _bdec < _ccc {var _eeb float64 ;_bad :=-1;_bcc :=_cad ;if _be .MarginLeft !=nil {_bcc =*_be .MarginLeft ;};for _fafa ,_gab :=range _fge [_bdec ]{if _gab ==' '{_bad =_fafa ;
};_cgda ,_abd :=_abc .GetRuneMetrics (_gab );if !_abd {_f .Log .Debug ("\u0046\u006f\u006e\u0074\u0020\u0064o\u0065\u0073\u0020\u006e\u006f\u0074\u0020\u0068\u0061\u0076\u0065\u0020\u0072\u0075\u006e\u0065\u0020\u006d\u0065\u0074r\u0069\u0063\u0073\u0020\u0066\u006f\u0072\u0020\u0025\u0076\u0020\u002d\u0020\u0073k\u0069p\u0070\u0069\u006e\u0067",_gab );
continue ;};_eeb =_bcc ;_bcc +=_cgda .Wx ;if _cdgb &&!_fdd &&_fbf *_bcc /1000.0> _ad {_bffe :=_fafa ;_badc :=_fafa ;if _bad > 0{_bffe =_bad +1;_badc =_bad ;};_afb :=_fge [_bdec ][_bffe :];_ebe :=_gbf [_bdec ][_bffe :];if _bdec < len (_fge )-1{_fge =append (_fge [:_bdec +1],_fge [_bdec :]...);
_fge [_bdec +1]=_afb ;_gbf =append (_gbf [:_bdec +1],_gbf [_bdec :]...);_gbf [_bdec +1]=_ebe ;}else {_fge =append (_fge ,_afb );_gbf =append (_gbf ,_ebe );};_fge [_bdec ]=_fge [_bdec ][0:_badc ];_gbf [_bdec ]=_gbf [_bdec ][0:_badc ];_ccc ++;_bcc =_eeb ;
break ;};};if _bcc > _dec {_dec =_bcc ;};_fge [_bdec ]=string (_gce .Encode (_fge [_bdec ]));if len (_fge [_bdec ])> 0{_cgd ++;};_bdec ++;};_geg :=_fbf ;if _cgd > 1{_geg *=_dbf ;};_dgfbb :=float64 (_cgd )*_geg ;if _fdd ||_dgfbb <=_ab {_bcg =_fge ;_gef =_gbf ;
break ;};_fbf --;};};_bace :=_cad ;if _be .MarginLeft !=nil {_bace =*_be .MarginLeft ;};if _fbf ==0||_fdd &&_dec > 0&&_bace +_dec *_fbf /1000.0> _ad {_fbf =0.95*1000.0*(_ad -_bace )/_dec ;};_aee :=_cfa ;{if _bee ,_ff :=_cd .GetIntVal (_dag .Q );_ff {switch _bee {case 0:_aee =_cfa ;
case 1:_aee =_dc ;case 2:_aee =_fe ;default:_f .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u0055\u006e\u0073\u0075\u0070\u0070\u006f\u0072t\u0065\u0064\u0020\u0071\u0075\u0061\u0064\u0064\u0069\u006e\u0067\u003a\u0020%\u0064\u0020\u002d\u0020\u0075\u0073\u0069\u006e\u0067\u0020\u006c\u0065ft\u0020\u0061\u006c\u0069\u0067\u006e\u006d\u0065\u006e\u0074",_bee );
};};};_bbb :=_fbf ;if _cdgb &&_cgd > 1{_bbb =_dbf *_fbf ;};var _eca float64 ;if _gdb !=nil {_eca ,_bbc =_gdb .GetCapHeight ();if _bbc !=nil {_f .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u0055\u006e\u0061\u0062\u006c\u0065 \u0074\u006f\u0020\u0067\u0065\u0074 \u0066\u006f\u006e\u0074\u0020\u0043\u0061\u0070\u0048\u0065\u0069\u0067\u0068t\u003a\u0020\u0025\u0076",_bbc );
};};if int (_eca )<=0{_f .Log .Debug ("W\u0041\u0052\u004e\u003a\u0020\u0043\u0061\u0070\u0048e\u0069\u0067\u0068\u0074\u0020\u006e\u006ft \u0061\u0076\u0061\u0069l\u0061\u0062\u006c\u0065\u0020\u002d\u0020\u0073\u0065tt\u0069\u006eg\u0020\u0074\u006f\u0020\u0031\u0030\u0030\u0030");
_eca =1000;};_cga :=_eca /1000.0*_fbf ;_egd :=0.0;{_cebe :=float64 (_cgd )*_bbb ;if _fdd &&_egd +_cebe > _ab {_fbf =0.95*(_ab -_egd )/float64 (_cgd );_bbb =_fbf ;if _cdgb &&_cgd > 1{_bbb =_dbf *_fbf ;};_cga =_eca /1000.0*_fbf ;_cebe =float64 (_cgd )*_bbb ;
};if _ab > _cebe {if _cdgb {if _be .MultilineVAlignMiddle {_afd :=(_ab -(_cebe +_cga ))/2.0;_efc :=_afd +_cebe +_cga -_bbb ;_egd =_efc ;if _cgd > 1{_egd =_egd +(_cebe /_fbf *float64 (_cgd ))-_bbb -_cga ;};if _egd < _cebe {_egd =(_ab -_cga )/2.0;};}else {_egd =_ab -_bbb ;
if _egd > _fbf {_ege :=0.0;if _cdgb &&_be .MultilineLineHeight > 1&&_cgd > 1{_ege =_be .MultilineLineHeight -1;};_egd -=_fbf *(0.5-_ege );};};}else {_egd =(_ab -_cga )/2.0;};};};if _be .TextColor !=nil {_ebb :=_be .TextColor ;_eggb ,_ecg :=_ebb .(*_ceg .PdfColorDeviceRGB );
if !_ecg {_eggb =_ceg .NewPdfColorDeviceRGB (0,0,0);};_egbd .Add_rg (_eggb .R (),_eggb .G (),_eggb .B ());}else {for _ ,_ebea :=range *_bea {if _ebea .Operand =="\u0072\u0067"||_ebea .Operand =="\u0067"{_egbd .AddOperand (*_ebea );};};};_egbd .Add_Tf (*_af ,_fbf );
_egbd .Add_Td (_bace ,_egd );_ada :=_bace ;_cgga :=_bace ;for _cabg ,_gfb :=range _bcg {_edc :=0.0;for _ ,_dbe :=range _gef [_cabg ]{_bced ,_ddeb :=_abc .GetRuneMetrics (_dbe );if !_ddeb {continue ;};_edc +=_bced .Wx ;};_cbe :=_edc /1000.0*_fbf ;_fcd :=_ad -_cbe ;
var _cffb float64 ;switch _aee {case _cfa :_cffb =_ada ;case _dc :_cffb =_fcd /2;case _fe :_cffb =_fcd ;};_bace =_cffb -_cgga ;if _bace > 0.0{_egbd .Add_Td (_bace ,0);};_cgga =_cffb ;_egbd .Add_Tj (*_cd .MakeString (_gfb ));if _cabg < len (_bcg )-1{_egbd .Add_Td (0,-_fbf *_dbf );
};};_egbd .Add_ET ();_egbd .Add_Q ();_egbd .Add_EMC ();_egf .SetContentStream (_egbd .Bytes (),_adfb ());};_egf .Resources =_gde ;_dbg :=_cd .MakeDict ();_dbg .Set ("\u004e",_egf .ToPdfObject ());return _dbg ,nil ;};

// SignatureFieldOpts represents a set of options used to configure
// an appearance widget dictionary.
type SignatureFieldOpts struct{

// Rect represents the area the signature annotation is displayed on.
Rect []float64 ;

// AutoSize specifies if the content of the appearance should be
// scaled to fit in the annotation rectangle.
AutoSize bool ;

// Font specifies the font of the text content.
Font *_ceg .PdfFont ;

// FontSize specifies the size of the text content.
FontSize float64 ;

// LineHeight specifies the height of a line of text in the appearance annotation.
LineHeight float64 ;

// TextColor represents the color of the text content displayed.
TextColor _ceg .PdfColor ;

// FillColor represents the background color of the appearance annotation area.
FillColor _ceg .PdfColor ;

// BorderSize represents border size of the appearance annotation area.
BorderSize float64 ;

// BorderColor represents the border color of the appearance annotation area.
BorderColor _ceg .PdfColor ;

// WatermarkImage specifies the image used as a watermark that will be rendered
// behind the signature.
WatermarkImage _g .Image ;

// Image represents the image used for the signature appearance.
Image _g .Image ;

// Encoder specifies the image encoder used for image signature. Defaults to flate encoder.
Encoder _cd .StreamEncoder ;

// ImagePosition specifies the image location relative to the text signature.
ImagePosition SignatureImagePosition ;};func _eb (_cgg CircleAnnotationDef ,_ggg string )([]byte ,*_ceg .PdfRectangle ,*_ceg .PdfRectangle ,error ){_cdg :=_ba .Circle {X :_cgg .X ,Y :_cgg .Y ,Width :_cgg .Width ,Height :_cgg .Height ,FillEnabled :_cgg .FillEnabled ,FillColor :_cgg .FillColor ,BorderEnabled :_cgg .BorderEnabled ,BorderWidth :_cgg .BorderWidth ,BorderColor :_cgg .BorderColor ,Opacity :_cgg .Opacity };
_fb ,_bd ,_de :=_cdg .Draw (_ggg );if _de !=nil {return nil ,nil ,nil ,_de ;};_da :=&_ceg .PdfRectangle {};_da .Llx =_cgg .X +_bd .Llx ;_da .Lly =_cgg .Y +_bd .Lly ;_da .Urx =_cgg .X +_bd .Urx ;_da .Ury =_cgg .Y +_bd .Ury ;return _fb ,_bd ,_da ,nil ;};


// GenerateAppearanceDict generates an appearance dictionary for widget annotation `wa` for the `field` in `form`.
// Implements interface model.FieldAppearanceGenerator.
func (_cgec ImageFieldAppearance )GenerateAppearanceDict (form *_ceg .PdfAcroForm ,field *_ceg .PdfField ,wa *_ceg .PdfAnnotationWidget )(*_cd .PdfObjectDictionary ,error ){_ ,_gdbaa :=field .GetContext ().(*_ceg .PdfFieldButton );if !_gdbaa {_f .Log .Trace ("C\u006f\u0075\u006c\u0064\u0020\u006fn\u006c\u0079\u0020\u0068\u0061\u006ed\u006c\u0065\u0020\u0062\u0075\u0074\u0074o\u006e\u0020\u002d\u0020\u0069\u0067\u006e\u006f\u0072\u0069n\u0067");
return nil ,nil ;};_cebef ,_eaed :=_cd .GetDict (wa .AP );if _eaed &&_cgec .OnlyIfMissing {_f .Log .Trace ("\u0041\u006c\u0072\u0065a\u0064\u0079\u0020\u0070\u006f\u0070\u0075\u006c\u0061\u0074e\u0064 \u002d\u0020\u0069\u0067\u006e\u006f\u0072i\u006e\u0067");
return _cebef ,nil ;};if form .DR ==nil {form .DR =_ceg .NewPdfPageResources ();};switch _dcgd :=field .GetContext ().(type ){case *_ceg .PdfFieldButton :if _dcgd .IsPush (){_agfc ,_cdaa :=_dbaf (_dcgd ,wa ,_cgec .Style ());if _cdaa !=nil {return nil ,_cdaa ;
};return _agfc ,nil ;};};return nil ,nil ;};

// NewTextField generates a new text field with partial name `name` at location
// specified by `rect` on given `page` and with field specific options `opt`.
func NewTextField (page *_ceg .PdfPage ,name string ,rect []float64 ,opt TextFieldOptions )(*_ceg .PdfFieldText ,error ){if page ==nil {return nil ,_ceb .New ("\u0070a\u0067e\u0020\u006e\u006f\u0074\u0020s\u0070\u0065c\u0069\u0066\u0069\u0065\u0064");};
if len (name )<=0{return nil ,_ceb .New ("\u0072\u0065\u0071\u0075\u0069\u0072\u0065\u0064\u0020\u0061\u0074\u0074\u0072\u0069\u0062u\u0074e\u0020\u006e\u006f\u0074\u0020\u0073\u0070\u0065\u0063\u0069\u0066\u0069\u0065\u0064");};if len (rect )!=4{return nil ,_ceb .New ("\u0069\u006e\u0076\u0061\u006c\u0069\u0064\u0020\u0072\u0061\u006e\u0067\u0065");
};_fbdb :=_ceg .NewPdfField ();_ccgb :=&_ceg .PdfFieldText {};_fbdb .SetContext (_ccgb );_ccgb .PdfField =_fbdb ;_ccgb .T =_cd .MakeString (name );if opt .MaxLen > 0{_ccgb .MaxLen =_cd .MakeInteger (int64 (opt .MaxLen ));};if len (opt .Value )> 0{_ccgb .V =_cd .MakeString (opt .Value );
};if opt .TextColor !=""{_ccbb :=_d .ColorRGBFromHex (opt .TextColor );_ebfb ,_babbe ,_dba :=_ccbb .ToRGB ();_becc :=12;if opt .FontSize > 0{_becc =opt .FontSize ;};_fec :="\u0048e\u006c\u0076\u0065\u0074\u0069\u0063a";if opt .FontName !=""{_fec =opt .FontName ;
};_dege :=_fg .Sprintf ("/\u0025\u0073\u0020\u0025\u0064\u0020T\u0066\u0020\u0025\u002e\u0033\u0066\u0020\u0025\u002e3\u0066\u0020\u0025.\u0033f\u0020\u0072\u0067",_fec ,_becc ,_ebfb ,_babbe ,_dba );_ccgb .DA =_cd .MakeString (_dege );};_fbdb .SetContext (_ccgb );
_aafa :=_ceg .NewPdfAnnotationWidget ();_aafa .Rect =_cd .MakeArrayFromFloats (rect );_aafa .P =page .ToPdfObject ();_aafa .F =_cd .MakeInteger (4);_aafa .Parent =_ccgb .ToPdfObject ();_ccgb .Annotations =append (_ccgb .Annotations ,_aafa );return _ccgb ,nil ;
};

// Style returns the appearance style of `fa`. If not specified, returns default style.
func (_bfa FieldAppearance )Style ()AppearanceStyle {if _bfa ._cab !=nil {return *_bfa ._cab ;};_cgb :=_cad ;return AppearanceStyle {AutoFontSizeFraction :0.65,CheckmarkRune :'✔',BorderSize :0.0,BorderColor :_ceg .NewPdfColorDeviceGray (0),FillColor :_ceg .NewPdfColorDeviceGray (1),MultilineLineHeight :1.2,MultilineVAlignMiddle :false ,DrawAlignmentReticle :false ,AllowMK :true ,MarginLeft :&_cgb };
};func _agef (_dgdd []_ba .Point )(_cbg []_ba .Point ,_begd []_ba .Point ,_fbdg error ){_dadg :=len (_dgdd )-1;if len (_dgdd )< 1{return nil ,nil ,_ceb .New ("\u0041\u0074\u0020\u006c\u0065\u0061\u0073\u0074\u0020\u0074\u0077\u006f\u0020\u0070\u006f\u0069\u006e\u0074s \u0072e\u0071\u0075\u0069\u0072\u0065\u0064\u0020\u0074\u006f\u0020\u0063\u0061l\u0063\u0075\u006c\u0061\u0074\u0065\u0020\u0063\u0075\u0072\u0076\u0065\u0020\u0063\u006f\u006e\u0074r\u006f\u006c\u0020\u0070\u006f\u0069\u006e\u0074\u0073");
};if _dadg ==1{_bcgd :=_ba .Point {X :(2*_dgdd [0].X +_dgdd [1].X )/3,Y :(2*_dgdd [0].Y +_dgdd [1].Y )/3};_cbg =append (_cbg ,_bcgd );_begd =append (_begd ,_ba .Point {X :2*_bcgd .X -_dgdd [0].X ,Y :2*_bcgd .Y -_dgdd [0].Y });return _cbg ,_begd ,nil ;};
_bacgg :=make ([]float64 ,_dadg );for _aggbd :=1;_aggbd < _dadg -1;_aggbd ++{_bacgg [_aggbd ]=4*_dgdd [_aggbd ].X +2*_dgdd [_aggbd +1].X ;};_bacgg [0]=_dgdd [0].X +2*_dgdd [1].X ;_bacgg [_dadg -1]=(8*_dgdd [_dadg -1].X +_dgdd [_dadg ].X )/2.0;_gccff :=_fcgf (_bacgg );
for _gggc :=1;_gggc < _dadg -1;_gggc ++{_bacgg [_gggc ]=4*_dgdd [_gggc ].Y +2*_dgdd [_gggc +1].Y ;};_bacgg [0]=_dgdd [0].Y +2*_dgdd [1].Y ;_bacgg [_dadg -1]=(8*_dgdd [_dadg -1].Y +_dgdd [_dadg ].Y )/2.0;_fffg :=_fcgf (_bacgg );_cbg =make ([]_ba .Point ,_dadg );
_begd =make ([]_ba .Point ,_dadg );for _bage :=0;_bage < _dadg ;_bage ++{_cbg [_bage ]=_ba .Point {X :_gccff [_bage ],Y :_fffg [_bage ]};if _bage < _dadg -1{_begd [_bage ]=_ba .Point {X :2*_dgdd [_bage +1].X -_gccff [_bage +1],Y :2*_dgdd [_bage +1].Y -_fffg [_bage +1]};
}else {_begd [_bage ]=_ba .Point {X :(_dgdd [_dadg ].X +_gccff [_dadg -1])/2,Y :(_dgdd [_dadg ].Y +_fffg [_dadg -1])/2};};};return _cbg ,_begd ,nil ;};