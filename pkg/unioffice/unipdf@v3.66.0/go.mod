module github.com/unidoc/unipdf/v3

go 1.19

require (
	github.com/adrg/sysfont v0.1.2
	github.com/boombuler/barcode v1.0.1
	github.com/gabriel-vasile/mimetype v1.4.3
	github.com/gorilla/i18n v0.0.0-20150820051429-8b358169da46
	github.com/stretchr/testify v1.9.0
	github.com/trimmer-io/go-xmp v1.0.0
	github.com/unidoc/freetype v0.2.3
	github.com/unidoc/garabic v0.0.0-20220702200334-8c7cb25baa11
	github.com/unidoc/pkcs7 v0.2.0
	github.com/unidoc/timestamp v0.0.0-20200412005513-91597fd3793a
	github.com/unidoc/unichart v0.3.0
	github.com/unidoc/unitype v0.4.0
	golang.org/x/crypto v0.31.0
	golang.org/x/image v0.18.0
	golang.org/x/net v0.33.0
	golang.org/x/text v0.21.0
	golang.org/x/xerrors v0.0.0-20231012003039-104605ab7028
)

require (
	github.com/adrg/strutil v0.3.1 // indirect
	github.com/adrg/xdg v0.4.0 // indirect
	github.com/davecgh/go-spew v1.1.1 // indirect
	github.com/pmezard/go-difflib v1.0.0 // indirect
	github.com/sirupsen/logrus v1.9.3 // indirect
	golang.org/x/sys v0.28.0 // indirect
	gopkg.in/yaml.v3 v3.0.1 // indirect
)
