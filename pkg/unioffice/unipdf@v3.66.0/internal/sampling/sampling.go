//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package sampling ;import (_b "github.com/unidoc/unipdf/v3/internal/bitwise";_c "github.com/unidoc/unipdf/v3/internal/imageutil";_a "io";);type Reader struct{_ga _c .ImageBase ;_f *_b .Reader ;_bd ,_e ,_gac int ;_db bool ;};func (_cd *Reader )ReadSample ()(uint32 ,error ){if _cd ._e ==_cd ._ga .Height {return 0,_a .EOF ;
};_ed ,_de :=_cd ._f .ReadBits (byte (_cd ._ga .BitsPerComponent ));if _de !=nil {return 0,_de ;};_cd ._gac --;if _cd ._gac ==0{_cd ._gac =_cd ._ga .ColorComponents ;_cd ._bd ++;};if _cd ._bd ==_cd ._ga .Width {if _cd ._db {_cd ._f .ConsumeRemainingBits ();
};_cd ._bd =0;_cd ._e ++;};return uint32 (_ed ),nil ;};func (_fg *Writer )WriteSample (sample uint32 )error {if _ ,_da :=_fg ._gee .WriteBits (uint64 (sample ),_fg ._dcg .BitsPerComponent );_da !=nil {return _da ;};_fg ._agc --;if _fg ._agc ==0{_fg ._agc =_fg ._dcg .ColorComponents ;
_fg ._fag ++;};if _fg ._fag ==_fg ._dcg .Width {if _fg ._aa {_fg ._gee .FinishByte ();};_fg ._fag =0;};return nil ;};type SampleWriter interface{WriteSample (_fe uint32 )error ;WriteSamples (_eg []uint32 )error ;};func ResampleBytes (data []byte ,bitsPerSample int )[]uint32 {var _fa []uint32 ;
_ac :=bitsPerSample ;var _ef uint32 ;var _ce byte ;_geb :=0;_gaa :=0;_ee :=0;for _ee < len (data ){if _geb > 0{_bdc :=_geb ;if _ac < _bdc {_bdc =_ac ;};_ef =(_ef <<uint (_bdc ))|uint32 (_ce >>uint (8-_bdc ));_geb -=_bdc ;if _geb > 0{_ce =_ce <<uint (_bdc );
}else {_ce =0;};_ac -=_bdc ;if _ac ==0{_fa =append (_fa ,_ef );_ac =bitsPerSample ;_ef =0;_gaa ++;};}else {_acb :=data [_ee ];_ee ++;_cf :=8;if _ac < _cf {_cf =_ac ;};_geb =8-_cf ;_ef =(_ef <<uint (_cf ))|uint32 (_acb >>uint (_geb ));if _cf < 8{_ce =_acb <<uint (_cf );
};_ac -=_cf ;if _ac ==0{_fa =append (_fa ,_ef );_ac =bitsPerSample ;_ef =0;_gaa ++;};};};for _geb >=bitsPerSample {_ec :=_geb ;if _ac < _ec {_ec =_ac ;};_ef =(_ef <<uint (_ec ))|uint32 (_ce >>uint (8-_ec ));_geb -=_ec ;if _geb > 0{_ce =_ce <<uint (_ec );
}else {_ce =0;};_ac -=_ec ;if _ac ==0{_fa =append (_fa ,_ef );_ac =bitsPerSample ;_ef =0;_gaa ++;};};return _fa ;};func NewReader (img _c .ImageBase )*Reader {return &Reader {_f :_b .NewReader (img .Data ),_ga :img ,_gac :img .ColorComponents ,_db :img .BytesPerLine *8!=img .ColorComponents *img .BitsPerComponent *img .Width };
};func NewWriter (img _c .ImageBase )*Writer {return &Writer {_gee :_b .NewWriterMSB (img .Data ),_dcg :img ,_agc :img .ColorComponents ,_aa :img .BytesPerLine *8!=img .ColorComponents *img .BitsPerComponent *img .Width };};type SampleReader interface{ReadSample ()(uint32 ,error );
ReadSamples (_g []uint32 )error ;};func (_ag *Reader )ReadSamples (samples []uint32 )(_fd error ){for _ge :=0;_ge < len (samples );_ge ++{samples [_ge ],_fd =_ag .ReadSample ();if _fd !=nil {return _fd ;};};return nil ;};func ResampleUint32 (data []uint32 ,bitsPerInputSample int ,bitsPerOutputSample int )[]uint32 {var _gc []uint32 ;
_cfb :=bitsPerOutputSample ;var _eb uint32 ;var _dd uint32 ;_cdf :=0;_cb :=0;_fdc :=0;for _fdc < len (data ){if _cdf > 0{_dc :=_cdf ;if _cfb < _dc {_dc =_cfb ;};_eb =(_eb <<uint (_dc ))|(_dd >>uint (bitsPerInputSample -_dc ));_cdf -=_dc ;if _cdf > 0{_dd =_dd <<uint (_dc );
}else {_dd =0;};_cfb -=_dc ;if _cfb ==0{_gc =append (_gc ,_eb );_cfb =bitsPerOutputSample ;_eb =0;_cb ++;};}else {_ae :=data [_fdc ];_fdc ++;_ged :=bitsPerInputSample ;if _cfb < _ged {_ged =_cfb ;};_cdf =bitsPerInputSample -_ged ;_eb =(_eb <<uint (_ged ))|(_ae >>uint (_cdf ));
if _ged < bitsPerInputSample {_dd =_ae <<uint (_ged );};_cfb -=_ged ;if _cfb ==0{_gc =append (_gc ,_eb );_cfb =bitsPerOutputSample ;_eb =0;_cb ++;};};};for _cdf >=bitsPerOutputSample {_ad :=_cdf ;if _cfb < _ad {_ad =_cfb ;};_eb =(_eb <<uint (_ad ))|(_dd >>uint (bitsPerInputSample -_ad ));
_cdf -=_ad ;if _cdf > 0{_dd =_dd <<uint (_ad );}else {_dd =0;};_cfb -=_ad ;if _cfb ==0{_gc =append (_gc ,_eb );_cfb =bitsPerOutputSample ;_eb =0;_cb ++;};};if _cfb > 0&&_cfb < bitsPerOutputSample {_eb <<=uint (_cfb );_gc =append (_gc ,_eb );};return _gc ;
};func (_bg *Writer )WriteSamples (samples []uint32 )error {for _fde :=0;_fde < len (samples );_fde ++{if _fgc :=_bg .WriteSample (samples [_fde ]);_fgc !=nil {return _fgc ;};};return nil ;};type Writer struct{_dcg _c .ImageBase ;_gee *_b .Writer ;_fag ,_agc int ;
_aa bool ;};