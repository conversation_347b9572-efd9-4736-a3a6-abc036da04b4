//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package imageutil ;import (_c "encoding/binary";_f "errors";_cb "fmt";_b "github.com/unidoc/unipdf/v3/common";_cbe "github.com/unidoc/unipdf/v3/internal/bitwise";_ae "image";_a "image/color";_fd "image/draw";_d "math";);func _eed (_adfa ,_edb *Monochrome ,_fgc []byte ,_ggb int )(_eaaf error ){var (_eeg ,_bfea ,_bfb ,_eff ,_aaf ,_ddg ,_gbbe ,_gfc int ;
_efd ,_dce uint32 ;_gcg ,_gag byte ;_dgd uint16 ;);_dacd :=make ([]byte ,4);_bcd :=make ([]byte ,4);for _bfb =0;_bfb < _adfa .Height -1;_bfb ,_eff =_bfb +2,_eff +1{_eeg =_bfb *_adfa .BytesPerLine ;_bfea =_eff *_edb .BytesPerLine ;for _aaf ,_ddg =0,0;_aaf < _ggb ;
_aaf ,_ddg =_aaf +4,_ddg +1{for _gbbe =0;_gbbe < 4;_gbbe ++{_gfc =_eeg +_aaf +_gbbe ;if _gfc <=len (_adfa .Data )-1&&_gfc < _eeg +_adfa .BytesPerLine {_dacd [_gbbe ]=_adfa .Data [_gfc ];}else {_dacd [_gbbe ]=0x00;};_gfc =_eeg +_adfa .BytesPerLine +_aaf +_gbbe ;
if _gfc <=len (_adfa .Data )-1&&_gfc < _eeg +(2*_adfa .BytesPerLine ){_bcd [_gbbe ]=_adfa .Data [_gfc ];}else {_bcd [_gbbe ]=0x00;};};_efd =_c .BigEndian .Uint32 (_dacd );_dce =_c .BigEndian .Uint32 (_bcd );_dce |=_efd ;_dce |=_dce <<1;_dce &=0xaaaaaaaa;
_efd =_dce |(_dce <<7);_gcg =byte (_efd >>24);_gag =byte ((_efd >>8)&0xff);_gfc =_bfea +_ddg ;if _gfc +1==len (_edb .Data )-1||_gfc +1>=_bfea +_edb .BytesPerLine {_edb .Data [_gfc ]=_fgc [_gcg ];}else {_dgd =(uint16 (_fgc [_gcg ])<<8)|uint16 (_fgc [_gag ]);
if _eaaf =_edb .setTwoBytes (_gfc ,_dgd );_eaaf !=nil {return _cb .Errorf ("s\u0065\u0074\u0074\u0069\u006e\u0067 \u0074\u0077\u006f\u0020\u0062\u0079t\u0065\u0073\u0020\u0066\u0061\u0069\u006ce\u0064\u002c\u0020\u0069\u006e\u0064\u0065\u0078\u003a\u0020%\u0064",_gfc );
};_ddg ++;};};};return nil ;};func (_dfc *CMYK32 )Bounds ()_ae .Rectangle {return _ae .Rectangle {Max :_ae .Point {X :_dfc .Width ,Y :_dfc .Height }};};func (_fcbga *NRGBA64 )NRGBA64At (x ,y int )_a .NRGBA64 {_ddcc ,_ :=ColorAtNRGBA64 (x ,y ,_fcbga .Width ,_fcbga .Data ,_fcbga .Alpha ,_fcbga .Decode );
return _ddcc ;};func (_cbdfg *Gray4 )SetGray (x ,y int ,g _a .Gray ){if x >=_cbdfg .Width ||y >=_cbdfg .Height {return ;};g =_bcaac (g );_cbdfg .setGray (x ,y ,g );};func (_gef *Monochrome )ColorModel ()_a .Model {return MonochromeModel (_gef .ModelThreshold )};
func _dcac (_baea ,_gdag RGBA ,_edfeg _ae .Rectangle ){for _dccg :=0;_dccg < _edfeg .Max .X ;_dccg ++{for _bbaf :=0;_bbaf < _edfeg .Max .Y ;_bbaf ++{_gdag .SetRGBA (_dccg ,_bbaf ,_baea .RGBAAt (_dccg ,_bbaf ));};};};func _eaa ()(_gbb [256]uint32 ){for _fac :=0;
_fac < 256;_fac ++{if _fac &0x01!=0{_gbb [_fac ]|=0xf;};if _fac &0x02!=0{_gbb [_fac ]|=0xf0;};if _fac &0x04!=0{_gbb [_fac ]|=0xf00;};if _fac &0x08!=0{_gbb [_fac ]|=0xf000;};if _fac &0x10!=0{_gbb [_fac ]|=0xf0000;};if _fac &0x20!=0{_gbb [_fac ]|=0xf00000;
};if _fac &0x40!=0{_gbb [_fac ]|=0xf000000;};if _fac &0x80!=0{_gbb [_fac ]|=0xf0000000;};};return _gbb ;};func _adda (_bdbg *Monochrome ,_abd ,_gace ,_gbdgc ,_aaba int ,_edbb RasterOperator ,_dggb *Monochrome ,_fcdd ,_gced int )error {var (_ggbf bool ;
_egae bool ;_deec int ;_ggcg int ;_cddg int ;_fdcb bool ;_dafe byte ;_dgdc int ;_efde int ;_cbgg int ;_fccc ,_fag int ;);_cfcgd :=8-(_abd &7);_ccedd :=_aage [_cfcgd ];_gdeb :=_bdbg .BytesPerLine *_gace +(_abd >>3);_dbbe :=_dggb .BytesPerLine *_gced +(_fcdd >>3);
if _gbdgc < _cfcgd {_ggbf =true ;_ccedd &=_aabb [8-_cfcgd +_gbdgc ];};if !_ggbf {_deec =(_gbdgc -_cfcgd )>>3;if _deec > 0{_egae =true ;_ggcg =_gdeb +1;_cddg =_dbbe +1;};};_dgdc =(_abd +_gbdgc )&7;if !(_ggbf ||_dgdc ==0){_fdcb =true ;_dafe =_aabb [_dgdc ];
_efde =_gdeb +1+_deec ;_cbgg =_dbbe +1+_deec ;};switch _edbb {case PixSrc :for _fccc =0;_fccc < _aaba ;_fccc ++{_bdbg .Data [_gdeb ]=_cecg (_bdbg .Data [_gdeb ],_dggb .Data [_dbbe ],_ccedd );_gdeb +=_bdbg .BytesPerLine ;_dbbe +=_dggb .BytesPerLine ;};if _egae {for _fccc =0;
_fccc < _aaba ;_fccc ++{for _fag =0;_fag < _deec ;_fag ++{_bdbg .Data [_ggcg +_fag ]=_dggb .Data [_cddg +_fag ];};_ggcg +=_bdbg .BytesPerLine ;_cddg +=_dggb .BytesPerLine ;};};if _fdcb {for _fccc =0;_fccc < _aaba ;_fccc ++{_bdbg .Data [_efde ]=_cecg (_bdbg .Data [_efde ],_dggb .Data [_cbgg ],_dafe );
_efde +=_bdbg .BytesPerLine ;_cbgg +=_dggb .BytesPerLine ;};};case PixNotSrc :for _fccc =0;_fccc < _aaba ;_fccc ++{_bdbg .Data [_gdeb ]=_cecg (_bdbg .Data [_gdeb ],^_dggb .Data [_dbbe ],_ccedd );_gdeb +=_bdbg .BytesPerLine ;_dbbe +=_dggb .BytesPerLine ;
};if _egae {for _fccc =0;_fccc < _aaba ;_fccc ++{for _fag =0;_fag < _deec ;_fag ++{_bdbg .Data [_ggcg +_fag ]=^_dggb .Data [_cddg +_fag ];};_ggcg +=_bdbg .BytesPerLine ;_cddg +=_dggb .BytesPerLine ;};};if _fdcb {for _fccc =0;_fccc < _aaba ;_fccc ++{_bdbg .Data [_efde ]=_cecg (_bdbg .Data [_efde ],^_dggb .Data [_cbgg ],_dafe );
_efde +=_bdbg .BytesPerLine ;_cbgg +=_dggb .BytesPerLine ;};};case PixSrcOrDst :for _fccc =0;_fccc < _aaba ;_fccc ++{_bdbg .Data [_gdeb ]=_cecg (_bdbg .Data [_gdeb ],_dggb .Data [_dbbe ]|_bdbg .Data [_gdeb ],_ccedd );_gdeb +=_bdbg .BytesPerLine ;_dbbe +=_dggb .BytesPerLine ;
};if _egae {for _fccc =0;_fccc < _aaba ;_fccc ++{for _fag =0;_fag < _deec ;_fag ++{_bdbg .Data [_ggcg +_fag ]|=_dggb .Data [_cddg +_fag ];};_ggcg +=_bdbg .BytesPerLine ;_cddg +=_dggb .BytesPerLine ;};};if _fdcb {for _fccc =0;_fccc < _aaba ;_fccc ++{_bdbg .Data [_efde ]=_cecg (_bdbg .Data [_efde ],_dggb .Data [_cbgg ]|_bdbg .Data [_efde ],_dafe );
_efde +=_bdbg .BytesPerLine ;_cbgg +=_dggb .BytesPerLine ;};};case PixSrcAndDst :for _fccc =0;_fccc < _aaba ;_fccc ++{_bdbg .Data [_gdeb ]=_cecg (_bdbg .Data [_gdeb ],_dggb .Data [_dbbe ]&_bdbg .Data [_gdeb ],_ccedd );_gdeb +=_bdbg .BytesPerLine ;_dbbe +=_dggb .BytesPerLine ;
};if _egae {for _fccc =0;_fccc < _aaba ;_fccc ++{for _fag =0;_fag < _deec ;_fag ++{_bdbg .Data [_ggcg +_fag ]&=_dggb .Data [_cddg +_fag ];};_ggcg +=_bdbg .BytesPerLine ;_cddg +=_dggb .BytesPerLine ;};};if _fdcb {for _fccc =0;_fccc < _aaba ;_fccc ++{_bdbg .Data [_efde ]=_cecg (_bdbg .Data [_efde ],_dggb .Data [_cbgg ]&_bdbg .Data [_efde ],_dafe );
_efde +=_bdbg .BytesPerLine ;_cbgg +=_dggb .BytesPerLine ;};};case PixSrcXorDst :for _fccc =0;_fccc < _aaba ;_fccc ++{_bdbg .Data [_gdeb ]=_cecg (_bdbg .Data [_gdeb ],_dggb .Data [_dbbe ]^_bdbg .Data [_gdeb ],_ccedd );_gdeb +=_bdbg .BytesPerLine ;_dbbe +=_dggb .BytesPerLine ;
};if _egae {for _fccc =0;_fccc < _aaba ;_fccc ++{for _fag =0;_fag < _deec ;_fag ++{_bdbg .Data [_ggcg +_fag ]^=_dggb .Data [_cddg +_fag ];};_ggcg +=_bdbg .BytesPerLine ;_cddg +=_dggb .BytesPerLine ;};};if _fdcb {for _fccc =0;_fccc < _aaba ;_fccc ++{_bdbg .Data [_efde ]=_cecg (_bdbg .Data [_efde ],_dggb .Data [_cbgg ]^_bdbg .Data [_efde ],_dafe );
_efde +=_bdbg .BytesPerLine ;_cbgg +=_dggb .BytesPerLine ;};};case PixNotSrcOrDst :for _fccc =0;_fccc < _aaba ;_fccc ++{_bdbg .Data [_gdeb ]=_cecg (_bdbg .Data [_gdeb ],^(_dggb .Data [_dbbe ])|_bdbg .Data [_gdeb ],_ccedd );_gdeb +=_bdbg .BytesPerLine ;
_dbbe +=_dggb .BytesPerLine ;};if _egae {for _fccc =0;_fccc < _aaba ;_fccc ++{for _fag =0;_fag < _deec ;_fag ++{_bdbg .Data [_ggcg +_fag ]|=^(_dggb .Data [_cddg +_fag ]);};_ggcg +=_bdbg .BytesPerLine ;_cddg +=_dggb .BytesPerLine ;};};if _fdcb {for _fccc =0;
_fccc < _aaba ;_fccc ++{_bdbg .Data [_efde ]=_cecg (_bdbg .Data [_efde ],^(_dggb .Data [_cbgg ])|_bdbg .Data [_efde ],_dafe );_efde +=_bdbg .BytesPerLine ;_cbgg +=_dggb .BytesPerLine ;};};case PixNotSrcAndDst :for _fccc =0;_fccc < _aaba ;_fccc ++{_bdbg .Data [_gdeb ]=_cecg (_bdbg .Data [_gdeb ],^(_dggb .Data [_dbbe ])&_bdbg .Data [_gdeb ],_ccedd );
_gdeb +=_bdbg .BytesPerLine ;_dbbe +=_dggb .BytesPerLine ;};if _egae {for _fccc =0;_fccc < _aaba ;_fccc ++{for _fag =0;_fag < _deec ;_fag ++{_bdbg .Data [_ggcg +_fag ]&=^_dggb .Data [_cddg +_fag ];};_ggcg +=_bdbg .BytesPerLine ;_cddg +=_dggb .BytesPerLine ;
};};if _fdcb {for _fccc =0;_fccc < _aaba ;_fccc ++{_bdbg .Data [_efde ]=_cecg (_bdbg .Data [_efde ],^(_dggb .Data [_cbgg ])&_bdbg .Data [_efde ],_dafe );_efde +=_bdbg .BytesPerLine ;_cbgg +=_dggb .BytesPerLine ;};};case PixSrcOrNotDst :for _fccc =0;_fccc < _aaba ;
_fccc ++{_bdbg .Data [_gdeb ]=_cecg (_bdbg .Data [_gdeb ],_dggb .Data [_dbbe ]|^(_bdbg .Data [_gdeb ]),_ccedd );_gdeb +=_bdbg .BytesPerLine ;_dbbe +=_dggb .BytesPerLine ;};if _egae {for _fccc =0;_fccc < _aaba ;_fccc ++{for _fag =0;_fag < _deec ;_fag ++{_bdbg .Data [_ggcg +_fag ]=_dggb .Data [_cddg +_fag ]|^(_bdbg .Data [_ggcg +_fag ]);
};_ggcg +=_bdbg .BytesPerLine ;_cddg +=_dggb .BytesPerLine ;};};if _fdcb {for _fccc =0;_fccc < _aaba ;_fccc ++{_bdbg .Data [_efde ]=_cecg (_bdbg .Data [_efde ],_dggb .Data [_cbgg ]|^(_bdbg .Data [_efde ]),_dafe );_efde +=_bdbg .BytesPerLine ;_cbgg +=_dggb .BytesPerLine ;
};};case PixSrcAndNotDst :for _fccc =0;_fccc < _aaba ;_fccc ++{_bdbg .Data [_gdeb ]=_cecg (_bdbg .Data [_gdeb ],_dggb .Data [_dbbe ]&^(_bdbg .Data [_gdeb ]),_ccedd );_gdeb +=_bdbg .BytesPerLine ;_dbbe +=_dggb .BytesPerLine ;};if _egae {for _fccc =0;_fccc < _aaba ;
_fccc ++{for _fag =0;_fag < _deec ;_fag ++{_bdbg .Data [_ggcg +_fag ]=_dggb .Data [_cddg +_fag ]&^(_bdbg .Data [_ggcg +_fag ]);};_ggcg +=_bdbg .BytesPerLine ;_cddg +=_dggb .BytesPerLine ;};};if _fdcb {for _fccc =0;_fccc < _aaba ;_fccc ++{_bdbg .Data [_efde ]=_cecg (_bdbg .Data [_efde ],_dggb .Data [_cbgg ]&^(_bdbg .Data [_efde ]),_dafe );
_efde +=_bdbg .BytesPerLine ;_cbgg +=_dggb .BytesPerLine ;};};case PixNotPixSrcOrDst :for _fccc =0;_fccc < _aaba ;_fccc ++{_bdbg .Data [_gdeb ]=_cecg (_bdbg .Data [_gdeb ],^(_dggb .Data [_dbbe ]|_bdbg .Data [_gdeb ]),_ccedd );_gdeb +=_bdbg .BytesPerLine ;
_dbbe +=_dggb .BytesPerLine ;};if _egae {for _fccc =0;_fccc < _aaba ;_fccc ++{for _fag =0;_fag < _deec ;_fag ++{_bdbg .Data [_ggcg +_fag ]=^(_dggb .Data [_cddg +_fag ]|_bdbg .Data [_ggcg +_fag ]);};_ggcg +=_bdbg .BytesPerLine ;_cddg +=_dggb .BytesPerLine ;
};};if _fdcb {for _fccc =0;_fccc < _aaba ;_fccc ++{_bdbg .Data [_efde ]=_cecg (_bdbg .Data [_efde ],^(_dggb .Data [_cbgg ]|_bdbg .Data [_efde ]),_dafe );_efde +=_bdbg .BytesPerLine ;_cbgg +=_dggb .BytesPerLine ;};};case PixNotPixSrcAndDst :for _fccc =0;
_fccc < _aaba ;_fccc ++{_bdbg .Data [_gdeb ]=_cecg (_bdbg .Data [_gdeb ],^(_dggb .Data [_dbbe ]&_bdbg .Data [_gdeb ]),_ccedd );_gdeb +=_bdbg .BytesPerLine ;_dbbe +=_dggb .BytesPerLine ;};if _egae {for _fccc =0;_fccc < _aaba ;_fccc ++{for _fag =0;_fag < _deec ;
_fag ++{_bdbg .Data [_ggcg +_fag ]=^(_dggb .Data [_cddg +_fag ]&_bdbg .Data [_ggcg +_fag ]);};_ggcg +=_bdbg .BytesPerLine ;_cddg +=_dggb .BytesPerLine ;};};if _fdcb {for _fccc =0;_fccc < _aaba ;_fccc ++{_bdbg .Data [_efde ]=_cecg (_bdbg .Data [_efde ],^(_dggb .Data [_cbgg ]&_bdbg .Data [_efde ]),_dafe );
_efde +=_bdbg .BytesPerLine ;_cbgg +=_dggb .BytesPerLine ;};};case PixNotPixSrcXorDst :for _fccc =0;_fccc < _aaba ;_fccc ++{_bdbg .Data [_gdeb ]=_cecg (_bdbg .Data [_gdeb ],^(_dggb .Data [_dbbe ]^_bdbg .Data [_gdeb ]),_ccedd );_gdeb +=_bdbg .BytesPerLine ;
_dbbe +=_dggb .BytesPerLine ;};if _egae {for _fccc =0;_fccc < _aaba ;_fccc ++{for _fag =0;_fag < _deec ;_fag ++{_bdbg .Data [_ggcg +_fag ]=^(_dggb .Data [_cddg +_fag ]^_bdbg .Data [_ggcg +_fag ]);};_ggcg +=_bdbg .BytesPerLine ;_cddg +=_dggb .BytesPerLine ;
};};if _fdcb {for _fccc =0;_fccc < _aaba ;_fccc ++{_bdbg .Data [_efde ]=_cecg (_bdbg .Data [_efde ],^(_dggb .Data [_cbgg ]^_bdbg .Data [_efde ]),_dafe );_efde +=_bdbg .BytesPerLine ;_cbgg +=_dggb .BytesPerLine ;};};default:_b .Log .Debug ("I\u006e\u0076\u0061\u006c\u0069\u0064 \u0072\u0061\u0073\u0074\u0065\u0072\u0020\u006f\u0070e\u0072\u0061\u0074o\u0072:\u0020\u0025\u0064",_edbb );
return _f .New ("\u0069\u006e\u0076al\u0069\u0064\u0020\u0072\u0061\u0073\u0074\u0065\u0072\u0020\u006f\u0070\u0065\u0072\u0061\u0074\u006f\u0072");};return nil ;};func _adc (_dbf _a .CMYK )_a .Gray {_dcca ,_fee ,_cab :=_a .CMYKToRGB (_dbf .C ,_dbf .M ,_dbf .Y ,_dbf .K );
_fbaa :=(19595*uint32 (_dcca )+38470*uint32 (_fee )+7471*uint32 (_cab )+1<<7)>>16;return _a .Gray {Y :uint8 (_fbaa )};};func _fdgf (_gfg ,_dgdb *Monochrome ,_dfe []byte ,_gbg int )(_bfc error ){var (_bbc ,_edf ,_ddf ,_gagb ,_beef ,_cdf ,_ffbe ,_add int ;
_efa ,_bgd ,_ege ,_ebe uint32 ;_bbf ,_abc byte ;_dec uint16 ;);_fdb :=make ([]byte ,4);_gab :=make ([]byte ,4);for _ddf =0;_ddf < _gfg .Height -1;_ddf ,_gagb =_ddf +2,_gagb +1{_bbc =_ddf *_gfg .BytesPerLine ;_edf =_gagb *_dgdb .BytesPerLine ;for _beef ,_cdf =0,0;
_beef < _gbg ;_beef ,_cdf =_beef +4,_cdf +1{for _ffbe =0;_ffbe < 4;_ffbe ++{_add =_bbc +_beef +_ffbe ;if _add <=len (_gfg .Data )-1&&_add < _bbc +_gfg .BytesPerLine {_fdb [_ffbe ]=_gfg .Data [_add ];}else {_fdb [_ffbe ]=0x00;};_add =_bbc +_gfg .BytesPerLine +_beef +_ffbe ;
if _add <=len (_gfg .Data )-1&&_add < _bbc +(2*_gfg .BytesPerLine ){_gab [_ffbe ]=_gfg .Data [_add ];}else {_gab [_ffbe ]=0x00;};};_efa =_c .BigEndian .Uint32 (_fdb );_bgd =_c .BigEndian .Uint32 (_gab );_ege =_efa &_bgd ;_ege |=_ege <<1;_ebe =_efa |_bgd ;
_ebe &=_ebe <<1;_bgd =_ege |_ebe ;_bgd &=0xaaaaaaaa;_efa =_bgd |(_bgd <<7);_bbf =byte (_efa >>24);_abc =byte ((_efa >>8)&0xff);_add =_edf +_cdf ;if _add +1==len (_dgdb .Data )-1||_add +1>=_edf +_dgdb .BytesPerLine {if _bfc =_dgdb .setByte (_add ,_dfe [_bbf ]);
_bfc !=nil {return _cb .Errorf ("\u0069n\u0064\u0065\u0078\u003a\u0020\u0025d",_add );};}else {_dec =(uint16 (_dfe [_bbf ])<<8)|uint16 (_dfe [_abc ]);if _bfc =_dgdb .setTwoBytes (_add ,_dec );_bfc !=nil {return _cb .Errorf ("s\u0065\u0074\u0074\u0069\u006e\u0067 \u0074\u0077\u006f\u0020\u0062\u0079t\u0065\u0073\u0020\u0066\u0061\u0069\u006ce\u0064\u002c\u0020\u0069\u006e\u0064\u0065\u0078\u003a\u0020%\u0064",_add );
};_cdf ++;};};};return nil ;};type monochromeModel uint8 ;func _dgec (_fef _a .NRGBA )_a .Gray {_baf ,_daa ,_fccf ,_ :=_fef .RGBA ();_ceg :=(19595*_baf +38470*_daa +7471*_fccf +1<<15)>>24;return _a .Gray {Y :uint8 (_ceg )};};func (_dafg *Gray2 )Set (x ,y int ,c _a .Color ){if x >=_dafg .Width ||y >=_dafg .Height {return ;
};_ced :=Gray2Model .Convert (c ).(_a .Gray );_gfae :=y *_dafg .BytesPerLine ;_gaea :=_gfae +(x >>2);_gbf :=_ced .Y >>6;_dafg .Data [_gaea ]=(_dafg .Data [_gaea ]&(^(0xc0>>uint (2*((x )&3)))))|(_gbf <<uint (6-2*(x &3)));};type Gray4 struct{ImageBase };
func _fdfg (_bccc _ae .Image ,_ebg int )(_ae .Rectangle ,bool ,[]byte ){_dgab :=_bccc .Bounds ();var (_cceda bool ;_aedc []byte ;);switch _bagd :=_bccc .(type ){case SMasker :_cceda =_bagd .HasAlpha ();case NRGBA ,RGBA ,*_ae .RGBA64 ,nrgba64 ,*_ae .NYCbCrA :_aedc =make ([]byte ,_dgab .Max .X *_dgab .Max .Y *_ebg );
case *_ae .Paletted :if !_bagd .Opaque (){_aedc =make ([]byte ,_dgab .Max .X *_dgab .Max .Y *_ebg );};};return _dgab ,_cceda ,_aedc ;};func (_cegc *Gray16 )GrayAt (x ,y int )_a .Gray {_bebf ,_ :=_cegc .ColorAt (x ,y );return _a .Gray {Y :uint8 (_bebf .(_a .Gray16 ).Y >>8)};
};type NRGBA interface{NRGBAAt (_cccc ,_gga int )_a .NRGBA ;SetNRGBA (_bcfc ,_ddaca int ,_ecgg _a .NRGBA );};var _ Image =&RGBA32 {};func (_aabd *Monochrome )Set (x ,y int ,c _a .Color ){_gbgb :=y *_aabd .BytesPerLine +x >>3;if _gbgb > len (_aabd .Data )-1{return ;
};_fddf :=_aabd .ColorModel ().Convert (c ).(_a .Gray );_aabd .setGray (x ,_fddf ,_gbgb );};func RasterOperation (dest *Monochrome ,dx ,dy ,dw ,dh int ,op RasterOperator ,src *Monochrome ,sx ,sy int )error {return _dgef (dest ,dx ,dy ,dw ,dh ,op ,src ,sx ,sy );
};func (_gegf *Monochrome )ResolveDecode ()error {if len (_gegf .Decode )!=2{return nil ;};if _gegf .Decode [0]==1&&_gegf .Decode [1]==0{if _fffc :=_gegf .InverseData ();_fffc !=nil {return _fffc ;};_gegf .Decode =nil ;};return nil ;};var _ _ae .Image =&NRGBA64 {};
func (_dbgd *Gray8 )ColorAt (x ,y int )(_a .Color ,error ){return ColorAtGray8BPC (x ,y ,_dbgd .BytesPerLine ,_dbgd .Data ,_dbgd .Decode );};func _gafdf (_bfcf nrgba64 ,_dcfe NRGBA ,_cgba _ae .Rectangle ){for _caecb :=0;_caecb < _cgba .Max .X ;_caecb ++{for _fade :=0;
_fade < _cgba .Max .Y ;_fade ++{_cece :=_bfcf .NRGBA64At (_caecb ,_fade );_dcfe .SetNRGBA (_caecb ,_fade ,_degb (_cece ));};};};func (_fgad *NRGBA32 )SetNRGBA (x ,y int ,c _a .NRGBA ){_bbbe :=y *_fgad .Width +x ;_bfgb :=3*_bbbe ;if _bfgb +2>=len (_fgad .Data ){return ;
};_fgad .setRGBA (_bbbe ,c );};type Image interface{_fd .Image ;Base ()*ImageBase ;Copy ()Image ;Pix ()[]byte ;ColorAt (_cdcc ,_eece int )(_a .Color ,error );Validate ()error ;};func NextPowerOf2 (n uint )uint {if IsPowerOf2 (n ){return n ;};return 1<<(_fffca (n )+1);
};func ColorAtNRGBA64 (x ,y ,width int ,data ,alpha []byte ,decode []float64 )(_a .NRGBA64 ,error ){_abeg :=(y *width +x )*2;_ebeb :=_abeg *3;if _ebeb +5>=len (data ){return _a .NRGBA64 {},_cb .Errorf ("\u0069\u006d\u0061\u0067\u0065\u0020\u0063\u006f\u006f\u0072\u0064\u0069\u006ea\u0074\u0065\u0073\u0020\u006f\u0075t\u0020\u006f\u0066\u0020\u0072\u0061\u006e\u0067\u0065\u0020\u0028\u0025\u0064,\u0020\u0025\u0064\u0029",x ,y );
};const _abec =0xffff;_ceab :=uint16 (_abec );if alpha !=nil &&len (alpha )> _abeg +1{_ceab =uint16 (alpha [_abeg ])<<8|uint16 (alpha [_abeg +1]);};_bacb :=uint16 (data [_ebeb ])<<8|uint16 (data [_ebeb +1]);_aggg :=uint16 (data [_ebeb +2])<<8|uint16 (data [_ebeb +3]);
_aacb :=uint16 (data [_ebeb +4])<<8|uint16 (data [_ebeb +5]);if len (decode )==6{_bacb =uint16 (uint64 (LinearInterpolate (float64 (_bacb ),0,65535,decode [0],decode [1]))&_abec );_aggg =uint16 (uint64 (LinearInterpolate (float64 (_aggg ),0,65535,decode [2],decode [3]))&_abec );
_aacb =uint16 (uint64 (LinearInterpolate (float64 (_aacb ),0,65535,decode [4],decode [5]))&_abec );};return _a .NRGBA64 {R :_bacb ,G :_aggg ,B :_aacb ,A :_ceab },nil ;};func MonochromeModel (threshold uint8 )_a .Model {return monochromeModel (threshold )};
func (_ecg *Gray2 )Base ()*ImageBase {return &_ecg .ImageBase };func (_bfbb *Monochrome )setGray (_bdg int ,_bdeb _a .Gray ,_fefg int ){if _bdeb .Y ==0{_bfbb .clearBit (_fefg ,_bdg );}else {_bfbb .setGrayBit (_fefg ,_bdg );};};func _fa (_eg *Monochrome ,_ea int )(*Monochrome ,error ){if _eg ==nil {return nil ,_f .New ("\u0073o\u0075r\u0063\u0065\u0020\u006e\u006ft\u0020\u0064e\u0066\u0069\u006e\u0065\u0064");
};if _ea ==1{return _eg .copy (),nil ;};if !IsPowerOf2 (uint (_ea )){return nil ,_cb .Errorf ("\u0070\u0072\u006fvi\u0064\u0065\u0064\u0020\u0069\u006e\u0076\u0061\u006ci\u0064 \u0065x\u0070a\u006e\u0064\u0020\u0066\u0061\u0063\u0074\u006f\u0072\u003a\u0020\u0025\u0064",_ea );
};_aa :=_feb (_ea );return _gb (_eg ,_ea ,_aa );};var _ Gray =&Gray16 {};func (_fgcf *NRGBA64 )Base ()*ImageBase {return &_fgcf .ImageBase };func (_cbac *Monochrome )ColorAt (x ,y int )(_a .Color ,error ){return ColorAtGray1BPC (x ,y ,_cbac .BytesPerLine ,_cbac .Data ,_cbac .Decode );
};func _gcbf (_gceb _ae .Image ,_edec Image ,_bcab _ae .Rectangle ){switch _gfgd :=_gceb .(type ){case Gray :_ecfg (_gfgd ,_edec .(Gray ),_bcab );case NRGBA :_acgd (_gfgd ,_edec .(Gray ),_bcab );case CMYK :_gcgc (_gfgd ,_edec .(Gray ),_bcab );case RGBA :_bfdce (_gfgd ,_edec .(Gray ),_bcab );
default:_gfgg (_gceb ,_edec ,_bcab );};};func _dbe (_dga _a .NRGBA64 )_a .Gray {var _bgbd _a .NRGBA64 ;if _dga ==_bgbd {return _a .Gray {Y :0xff};};_dadcf ,_agae ,_gegc ,_ :=_dga .RGBA ();_egb :=(19595*_dadcf +38470*_agae +7471*_gegc +1<<15)>>24;return _a .Gray {Y :uint8 (_egb )};
};func (_cbad *CMYK32 )Base ()*ImageBase {return &_cbad .ImageBase };func (_aeed *Monochrome )clearBit (_dgfc ,_dfcc int ){_aeed .Data [_dgfc ]&=^(0x80>>uint (_dfcc &7))};func (_bbg colorConverter )Convert (src _ae .Image )(Image ,error ){return _bbg ._deag (src )};
type RGBA interface{RGBAAt (_bagfg ,_cbcc int )_a .RGBA ;SetRGBA (_dade ,_afagg int ,_gaca _a .RGBA );};func (_cac *Monochrome )Validate ()error {if len (_cac .Data )!=_cac .Height *_cac .BytesPerLine {return ErrInvalidImage ;};return nil ;};func (_cafc *Gray4 )setGray (_eaee int ,_cdca int ,_defa _a .Gray ){_fbgf :=_cdca *_cafc .BytesPerLine ;
_gagac :=_fbgf +(_eaee >>1);if _gagac >=len (_cafc .Data ){return ;};_deef :=_defa .Y >>4;_cafc .Data [_gagac ]=(_cafc .Data [_gagac ]&(^(0xf0>>uint (4*(_eaee &1)))))|(_deef <<uint (4-4*(_eaee &1)));};func _bge (_ccg _a .NYCbCrA )_a .NRGBA {_geca :=int32 (_ccg .Y )*0x10101;
_cfc :=int32 (_ccg .Cb )-128;_gega :=int32 (_ccg .Cr )-128;_fbg :=_geca +91881*_gega ;if uint32 (_fbg )&0xff000000==0{_fbg >>=8;}else {_fbg =^(_fbg >>31)&0xffff;};_dgg :=_geca -22554*_cfc -46802*_gega ;if uint32 (_dgg )&0xff000000==0{_dgg >>=8;}else {_dgg =^(_dgg >>31)&0xffff;
};_accf :=_geca +116130*_cfc ;if uint32 (_accf )&0xff000000==0{_accf >>=8;}else {_accf =^(_accf >>31)&0xffff;};return _a .NRGBA {R :uint8 (_fbg >>8),G :uint8 (_dgg >>8),B :uint8 (_accf >>8),A :_ccg .A };};type Gray8 struct{ImageBase };func (_accgd *monochromeThresholdConverter )Convert (img _ae .Image )(Image ,error ){if _fcbd ,_gfgb :=img .(*Monochrome );
_gfgb {return _fcbd .Copy (),nil ;};_faec :=img .Bounds ();_ggf ,_gcff :=NewImage (_faec .Max .X ,_faec .Max .Y ,1,1,nil ,nil ,nil );if _gcff !=nil {return nil ,_gcff ;};_ggf .(*Monochrome ).ModelThreshold =_accgd .Threshold ;for _ggca :=0;_ggca < _faec .Max .X ;
_ggca ++{for _bagf :=0;_bagf < _faec .Max .Y ;_bagf ++{_bcaa :=img .At (_ggca ,_bagf );_ggf .Set (_ggca ,_bagf ,_bcaa );};};return _ggf ,nil ;};func (_ffcc *Gray4 )Validate ()error {if len (_ffcc .Data )!=_ffcc .Height *_ffcc .BytesPerLine {return ErrInvalidImage ;
};return nil ;};func (_daef *Gray4 )GrayAt (x ,y int )_a .Gray {_eaece ,_ :=ColorAtGray4BPC (x ,y ,_daef .BytesPerLine ,_daef .Data ,_daef .Decode );return _eaece ;};func _cfd (_ffg ,_febd int )*Monochrome {return &Monochrome {ImageBase :NewImageBase (_ffg ,_febd ,1,1,nil ,nil ,nil ),ModelThreshold :0x0f};
};func _af (_ffb ,_gf *Monochrome )(_fcd error ){_ab :=_gf .BytesPerLine ;_abf :=_ffb .BytesPerLine ;var (_gc byte ;_ga uint16 ;_aab ,_gfa ,_ef ,_ge ,_de int ;);for _ef =0;_ef < _gf .Height ;_ef ++{_aab =_ef *_ab ;_gfa =2*_ef *_abf ;for _ge =0;_ge < _ab ;
_ge ++{_gc =_gf .Data [_aab +_ge ];_ga =_fae [_gc ];_de =_gfa +_ge *2;if _ffb .BytesPerLine !=_gf .BytesPerLine *2&&(_ge +1)*2> _ffb .BytesPerLine {_fcd =_ffb .setByte (_de ,byte (_ga >>8));}else {_fcd =_ffb .setTwoBytes (_de ,_ga );};if _fcd !=nil {return _fcd ;
};};for _ge =0;_ge < _abf ;_ge ++{_de =_gfa +_abf +_ge ;_gc =_ffb .Data [_gfa +_ge ];if _fcd =_ffb .setByte (_de ,_gc );_fcd !=nil {return _fcd ;};};};return nil ;};func _beee (_bcge _a .Color )_a .Color {_bcb :=_a .GrayModel .Convert (_bcge ).(_a .Gray );
return _bcaac (_bcb );};func (_egad *NRGBA32 )ColorModel ()_a .Model {return _a .NRGBAModel };func (_gfba *NRGBA32 )ColorAt (x ,y int )(_a .Color ,error ){return ColorAtNRGBA32 (x ,y ,_gfba .Width ,_gfba .Data ,_gfba .Alpha ,_gfba .Decode );};func _bfg (_aecd _a .Color )_a .Color {_fca :=_a .GrayModel .Convert (_aecd ).(_a .Gray );
return _dfge (_fca )};func (_bacd *RGBA32 )ColorAt (x ,y int )(_a .Color ,error ){return ColorAtRGBA32 (x ,y ,_bacd .Width ,_bacd .Data ,_bacd .Alpha ,_bacd .Decode );};var _ Image =&Gray2 {};func (_aged *Gray16 )Copy ()Image {return &Gray16 {ImageBase :_aged .copy ()}};
type Histogramer interface{Histogram ()[256]int ;};func _fedc ()(_ddc [256]uint64 ){for _afc :=0;_afc < 256;_afc ++{if _afc &0x01!=0{_ddc [_afc ]|=0xff;};if _afc &0x02!=0{_ddc [_afc ]|=0xff00;};if _afc &0x04!=0{_ddc [_afc ]|=0xff0000;};if _afc &0x08!=0{_ddc [_afc ]|=0xff000000;
};if _afc &0x10!=0{_ddc [_afc ]|=0xff00000000;};if _afc &0x20!=0{_ddc [_afc ]|=0xff0000000000;};if _afc &0x40!=0{_ddc [_afc ]|=0xff000000000000;};if _afc &0x80!=0{_ddc [_afc ]|=0xff00000000000000;};};return _ddc ;};func (_dbdb *NRGBA16 )SetNRGBA (x ,y int ,c _a .NRGBA ){_addb :=y *_dbdb .BytesPerLine +x *3/2;
if _addb +1>=len (_dbdb .Data ){return ;};c =_daed (c );_dbdb .setNRGBA (x ,y ,_addb ,c );};func _feb (_fb int )[]uint {var _aec []uint ;_fedca :=_fb ;_cfb :=_fedca /8;if _cfb !=0{for _ffff :=0;_ffff < _cfb ;_ffff ++{_aec =append (_aec ,8);};_aea :=_fedca %8;
_fedca =0;if _aea !=0{_fedca =_aea ;};};_dadd :=_fedca /4;if _dadd !=0{for _bee :=0;_bee < _dadd ;_bee ++{_aec =append (_aec ,4);};_eba :=_fedca %4;_fedca =0;if _eba !=0{_fedca =_eba ;};};_fcb :=_fedca /2;if _fcb !=0{for _gg :=0;_gg < _fcb ;_gg ++{_aec =append (_aec ,2);
};};return _aec ;};func _bfdce (_eaege RGBA ,_bbgag Gray ,_cced _ae .Rectangle ){for _cede :=0;_cede < _cced .Max .X ;_cede ++{for _fdfb :=0;_fdfb < _cced .Max .Y ;_fdfb ++{_accda :=_bgdd (_eaege .RGBAAt (_cede ,_fdfb ));_bbgag .SetGray (_cede ,_fdfb ,_accda );
};};};func (_ebcad *NRGBA16 )ColorModel ()_a .Model {return NRGBA16Model };var _ RGBA =&RGBA32 {};func (_ebdg *NRGBA32 )NRGBAAt (x ,y int )_a .NRGBA {_dff ,_ :=ColorAtNRGBA32 (x ,y ,_ebdg .Width ,_ebdg .Data ,_ebdg .Alpha ,_ebdg .Decode );return _dff ;
};func (_dacdd *NRGBA16 )Copy ()Image {return &NRGBA16 {ImageBase :_dacdd .copy ()}};func (_ddad *NRGBA16 )Base ()*ImageBase {return &_ddad .ImageBase };func _eee (_fbd _a .NRGBA )_a .CMYK {_cbeb ,_cbae ,_bbe ,_ :=_fbd .RGBA ();_ega ,_cdef ,_fcbb ,_cdd :=_a .RGBToCMYK (uint8 (_cbeb >>8),uint8 (_cbae >>8),uint8 (_bbe >>8));
return _a .CMYK {C :_ega ,M :_cdef ,Y :_fcbb ,K :_cdd };};func _beb (_bcfa _a .Gray )_a .NRGBA {return _a .NRGBA {R :_bcfa .Y ,G :_bcfa .Y ,B :_bcfa .Y ,A :0xff}};func init (){_egcd ()};type ColorConverter interface{Convert (_gcf _ae .Image )(Image ,error );
};type nrgba64 interface{NRGBA64At (_fbdb ,_bcce int )_a .NRGBA64 ;SetNRGBA64 (_gca ,_dfag int ,_fdge _a .NRGBA64 );};func _gda (_gfb *Monochrome ,_deg ,_efg int )(*Monochrome ,error ){if _gfb ==nil {return nil ,_f .New ("\u0073o\u0075r\u0063\u0065\u0020\u006e\u006ft\u0020\u0064e\u0066\u0069\u006e\u0065\u0064");
};if _deg <=0||_efg <=0{return nil ,_f .New ("\u0069\u006e\u0076\u0061\u006c\u0069\u0064\u0020\u0073\u0063\u0061l\u0065\u0020\u0066\u0061\u0063\u0074\u006f\u0072\u003a\u0020<\u003d\u0020\u0030");};if _deg ==_efg {if _deg ==1{return _gfb .copy (),nil ;};
if _deg ==2||_deg ==4||_deg ==8{_feg ,_ag :=_fa (_gfb ,_deg );if _ag !=nil {return nil ,_ag ;};return _feg ,nil ;};};_aag :=_deg *_gfb .Width ;_fda :=_efg *_gfb .Height ;_dad :=_cfd (_aag ,_fda );_bgg :=_dad .BytesPerLine ;var (_ade ,_ffbg ,_dc ,_adf ,_eaeg int ;
_dcb byte ;_dgcf error ;);for _ffbg =0;_ffbg < _gfb .Height ;_ffbg ++{_ade =_efg *_ffbg *_bgg ;for _dc =0;_dc < _gfb .Width ;_dc ++{if _cfg :=_gfb .getBitAt (_dc ,_ffbg );_cfg {_eaeg =_deg *_dc ;for _adf =0;_adf < _deg ;_adf ++{_dad .setIndexedBit (_ade *8+_eaeg +_adf );
};};};for _adf =1;_adf < _efg ;_adf ++{_ccb :=_ade +_adf *_bgg ;for _bgb :=0;_bgb < _bgg ;_bgb ++{if _dcb ,_dgcf =_dad .getByte (_ade +_bgb );_dgcf !=nil {return nil ,_dgcf ;};if _dgcf =_dad .setByte (_ccb +_bgb ,_dcb );_dgcf !=nil {return nil ,_dgcf ;
};};};};return _dad ,nil ;};func (_ecc *Monochrome )copy ()*Monochrome {_fegf :=_cfd (_ecc .Width ,_ecc .Height );_fegf .ModelThreshold =_ecc .ModelThreshold ;_fegf .Data =make ([]byte ,len (_ecc .Data ));copy (_fegf .Data ,_ecc .Data );if len (_ecc .Decode )!=0{_fegf .Decode =make ([]float64 ,len (_ecc .Decode ));
copy (_fegf .Decode ,_ecc .Decode );};if len (_ecc .Alpha )!=0{_fegf .Alpha =make ([]byte ,len (_ecc .Alpha ));copy (_fegf .Alpha ,_ecc .Alpha );};return _fegf ;};func (_aff *NRGBA64 )Set (x ,y int ,c _a .Color ){_dgda :=(y *_aff .Width +x )*2;_bgfg :=_dgda *3;
if _bgfg +5>=len (_aff .Data ){return ;};_cadab :=_a .NRGBA64Model .Convert (c ).(_a .NRGBA64 );_aff .setNRGBA64 (_bgfg ,_cadab ,_dgda );};func (_gagg *Monochrome )GrayAt (x ,y int )_a .Gray {_fgcb ,_ :=ColorAtGray1BPC (x ,y ,_gagg .BytesPerLine ,_gagg .Data ,_gagg .Decode );
return _fgcb ;};const (_gccb shift =iota ;_aaff ;);func _aecde (_baec *_ae .Gray ,_ecb uint8 )*_ae .Gray {_aeede :=_baec .Bounds ();_bfgdb :=_ae .NewGray (_aeede );for _ddddb :=0;_ddddb < _aeede .Dx ();_ddddb ++{for _aade :=0;_aade < _aeede .Dy ();_aade ++{_agdde :=_baec .GrayAt (_ddddb ,_aade );
_bfgdb .SetGray (_ddddb ,_aade ,_a .Gray {Y :_ggff (_agdde .Y ,_ecb )});};};return _bfgdb ;};func (_ffge *Gray16 )Bounds ()_ae .Rectangle {return _ae .Rectangle {Max :_ae .Point {X :_ffge .Width ,Y :_ffge .Height }};};func (_fffgc *NRGBA64 )setNRGBA64 (_fabd int ,_fea _a .NRGBA64 ,_affg int ){_fffgc .Data [_fabd ]=uint8 (_fea .R >>8);
_fffgc .Data [_fabd +1]=uint8 (_fea .R &0xff);_fffgc .Data [_fabd +2]=uint8 (_fea .G >>8);_fffgc .Data [_fabd +3]=uint8 (_fea .G &0xff);_fffgc .Data [_fabd +4]=uint8 (_fea .B >>8);_fffgc .Data [_fabd +5]=uint8 (_fea .B &0xff);if _affg +1< len (_fffgc .Alpha ){_fffgc .Alpha [_affg ]=uint8 (_fea .A >>8);
_fffgc .Alpha [_affg +1]=uint8 (_fea .A &0xff);};};type SMasker interface{HasAlpha ()bool ;GetAlpha ()[]byte ;MakeAlpha ();};func (_aeg *CMYK32 )ColorAt (x ,y int )(_a .Color ,error ){return ColorAtCMYK (x ,y ,_aeg .Width ,_aeg .Data ,_aeg .Decode );};
func _fccd (_fagb *_ae .NYCbCrA ,_cfbcb RGBA ,_fffcg _ae .Rectangle ){for _fgcba :=0;_fgcba < _fffcg .Max .X ;_fgcba ++{for _affd :=0;_affd < _fffcg .Max .Y ;_affd ++{_fbbb :=_fagb .NYCbCrAAt (_fgcba ,_affd );_cfbcb .SetRGBA (_fgcba ,_affd ,_cea (_fbbb ));
};};};func NewImage (width ,height ,bitsPerComponent ,colorComponents int ,data ,alpha []byte ,decode []float64 )(Image ,error ){_aeeb :=NewImageBase (width ,height ,bitsPerComponent ,colorComponents ,data ,alpha ,decode );var _ddbc Image ;switch colorComponents {case 1:switch bitsPerComponent {case 1:_ddbc =&Monochrome {ImageBase :_aeeb ,ModelThreshold :0x0f};
case 2:_ddbc =&Gray2 {ImageBase :_aeeb };case 4:_ddbc =&Gray4 {ImageBase :_aeeb };case 8:_ddbc =&Gray8 {ImageBase :_aeeb };case 16:_ddbc =&Gray16 {ImageBase :_aeeb };};case 3:switch bitsPerComponent {case 4:_ddbc =&NRGBA16 {ImageBase :_aeeb };case 8:_ddbc =&NRGBA32 {ImageBase :_aeeb };
case 16:_ddbc =&NRGBA64 {ImageBase :_aeeb };};case 4:_ddbc =&CMYK32 {ImageBase :_aeeb };};if _ddbc ==nil {return nil ,ErrInvalidImage ;};return _ddbc ,nil ;};func (_debc *Monochrome )IsUnpadded ()bool {return (_debc .Width *_debc .Height )==len (_debc .Data )};
func (_fgbb *Monochrome )SetGray (x ,y int ,g _a .Gray ){_dbcb :=y *_fgbb .BytesPerLine +x >>3;if _dbcb > len (_fgbb .Data )-1{return ;};g =_gbdd (g ,monochromeModel (_fgbb .ModelThreshold ));_fgbb .setGray (x ,g ,_dbcb );};func _dfeb (_fdag NRGBA ,_gaf CMYK ,_dee _ae .Rectangle ){for _cfga :=0;
_cfga < _dee .Max .X ;_cfga ++{for _ggg :=0;_ggg < _dee .Max .Y ;_ggg ++{_gdb :=_fdag .NRGBAAt (_cfga ,_ggg );_gaf .SetCMYK (_cfga ,_ggg ,_eee (_gdb ));};};};type CMYK interface{CMYKAt (_fad ,_bgga int )_a .CMYK ;SetCMYK (_badd ,_cbc int ,_dbg _a .CMYK );
};var _ Image =&Gray4 {};func (_eca *CMYK32 )SetCMYK (x ,y int ,c _a .CMYK ){_bgge :=4*(y *_eca .Width +x );if _bgge +3>=len (_eca .Data ){return ;};_eca .Data [_bgge ]=c .C ;_eca .Data [_bgge +1]=c .M ;_eca .Data [_bgge +2]=c .Y ;_eca .Data [_bgge +3]=c .K ;
};var _ Image =&Gray8 {};func (_ddfe *NRGBA32 )Validate ()error {if len (_ddfe .Data )!=3*_ddfe .Width *_ddfe .Height {return _f .New ("i\u006e\u0076\u0061\u006c\u0069\u0064\u0020\u0069\u006da\u0067\u0065\u0020\u0064\u0061\u0074\u0061 s\u0069\u007a\u0065\u0020f\u006f\u0072\u0020\u0070\u0072\u006f\u0076\u0069\u0064ed\u0020\u0064i\u006d\u0065\u006e\u0073\u0069\u006f\u006e\u0073");
};return nil ;};func (_dcdb *Gray8 )ColorModel ()_a .Model {return _a .GrayModel };func _gbd (_fdd RGBA ,_bga CMYK ,_fgdd _ae .Rectangle ){for _edg :=0;_edg < _fgdd .Max .X ;_edg ++{for _egeb :=0;_egeb < _fgdd .Max .Y ;_egeb ++{_afa :=_fdd .RGBAAt (_edg ,_egeb );
_bga .SetCMYK (_edg ,_egeb ,_bde (_afa ));};};};var _ _ae .Image =&Monochrome {};func _gb (_db *Monochrome ,_dg int ,_bd []uint )(*Monochrome ,error ){_ed :=_dg *_db .Width ;_bg :=_dg *_db .Height ;_aeb :=_cfd (_ed ,_bg );for _bf ,_da :=range _bd {var _fdg error ;
switch _da {case 2:_fdg =_af (_aeb ,_db );case 4:_fdg =_eae (_aeb ,_db );case 8:_fdg =_ee (_aeb ,_db );};if _fdg !=nil {return nil ,_fdg ;};if _bf !=len (_bd )-1{_db =_aeb .copy ();};};return _aeb ,nil ;};func (_caec *Gray2 )SetGray (x ,y int ,gray _a .Gray ){_eab :=_dfge (gray );
_fcge :=y *_caec .BytesPerLine ;_beeg :=_fcge +(x >>2);if _beeg >=len (_caec .Data ){return ;};_dbbc :=_eab .Y >>6;_caec .Data [_beeg ]=(_caec .Data [_beeg ]&(^(0xc0>>uint (2*((x )&3)))))|(_dbbc <<uint (6-2*(x &3)));};func _bag (_aee _ae .Image )(Image ,error ){if _abef ,_aafa :=_aee .(*CMYK32 );
_aafa {return _abef .Copy (),nil ;};_ebcg :=_aee .Bounds ();_bba ,_fbcg :=NewImage (_ebcg .Max .X ,_ebcg .Max .Y ,8,4,nil ,nil ,nil );if _fbcg !=nil {return nil ,_fbcg ;};switch _gaed :=_aee .(type ){case CMYK :_fcc (_gaed ,_bba .(CMYK ),_ebcg );case Gray :_ddge (_gaed ,_bba .(CMYK ),_ebcg );
case NRGBA :_dfeb (_gaed ,_bba .(CMYK ),_ebcg );case RGBA :_gbd (_gaed ,_bba .(CMYK ),_ebcg );default:_gfgg (_aee ,_bba ,_ebcg );};return _bba ,nil ;};type colorConverter struct{_deag func (_ggd _ae .Image )(Image ,error );};func (_cfcg *Gray8 )Histogram ()(_eea [256]int ){for _adcf :=0;
_adcf < len (_cfcg .Data );_adcf ++{_eea [_cfcg .Data [_adcf ]]++;};return _eea ;};func _dac (_afcf *Monochrome ,_bfe ...int )(_bed *Monochrome ,_beg error ){if _afcf ==nil {return nil ,_f .New ("\u0073o\u0075\u0072\u0063\u0065 \u0062\u0069\u0074\u006d\u0061p\u0020n\u006ft\u0020\u0064\u0065\u0066\u0069\u006e\u0065d");
};if len (_bfe )==0{return nil ,_f .New ("\u0074h\u0065\u0072e\u0020\u006d\u0075s\u0074\u0020\u0062\u0065\u0020\u0061\u0074 \u006c\u0065\u0061\u0073\u0074\u0020o\u006e\u0065\u0020\u006c\u0065\u0076\u0065\u006c\u0020\u006f\u0066 \u0072\u0065\u0064\u0075\u0063\u0074\u0069\u006f\u006e");
};_fba :=_bgbc ();_bed =_afcf ;for _ ,_ba :=range _bfe {if _ba <=0{break ;};_bed ,_beg =_aga (_bed ,_ba ,_fba );if _beg !=nil {return nil ,_beg ;};};return _bed ,nil ;};func _fde (_dfca _a .Gray )_a .RGBA {return _a .RGBA {R :_dfca .Y ,G :_dfca .Y ,B :_dfca .Y ,A :0xff}};
var _ _ae .Image =&Gray2 {};func _ggc (_bbab _a .NRGBA )_a .Gray {var _edce _a .NRGBA ;if _bbab ==_edce {return _a .Gray {Y :0xff};};_dcf ,_daf ,_dcc ,_ :=_bbab .RGBA ();_ffad :=(19595*_dcf +38470*_daf +7471*_dcc +1<<15)>>24;return _a .Gray {Y :uint8 (_ffad )};
};func _ddge (_gfbg Gray ,_gec CMYK ,_dead _ae .Rectangle ){for _ffa :=0;_ffa < _dead .Max .X ;_ffa ++{for _cfe :=0;_cfe < _dead .Max .Y ;_cfe ++{_gfge :=_gfbg .GrayAt (_ffa ,_cfe );_gec .SetCMYK (_ffa ,_cfe ,_efdb (_gfge ));};};};func (_afed *Monochrome )setGrayBit (_efabd ,_ddag int ){_afed .Data [_efabd ]|=0x80>>uint (_ddag &7)};
func (_bcda *RGBA32 )Set (x ,y int ,c _a .Color ){_afca :=y *_bcda .Width +x ;_gdaf :=3*_afca ;if _gdaf +2>=len (_bcda .Data ){return ;};_gfff :=_a .RGBAModel .Convert (c ).(_a .RGBA );_bcda .setRGBA (_afca ,_gfff );};func _cea (_ceaf _a .NYCbCrA )_a .RGBA {_cae ,_afe ,_dba ,_ceac :=_bge (_ceaf ).RGBA ();
return _a .RGBA {R :uint8 (_cae >>8),G :uint8 (_afe >>8),B :uint8 (_dba >>8),A :uint8 (_ceac >>8)};};func _fgded (_abegb uint8 )bool {if _abegb ==0||_abegb ==255{return true ;};return false ;};func InDelta (expected ,current ,delta float64 )bool {_ccgg :=expected -current ;
if _ccgg <=-delta ||_ccgg >=delta {return false ;};return true ;};func (_ggba *Monochrome )Scale (scale float64 )(*Monochrome ,error ){var _cdcd bool ;_agf :=scale ;if scale < 1{_agf =1/scale ;_cdcd =true ;};_efdd :=NextPowerOf2 (uint (_agf ));if InDelta (float64 (_efdd ),_agf ,0.001){if _cdcd {return _ggba .ReduceBinary (_agf );
};return _ggba .ExpandBinary (int (_efdd ));};_bgbca :=int (_d .RoundToEven (float64 (_ggba .Width )*scale ));_dfdc :=int (_d .RoundToEven (float64 (_ggba .Height )*scale ));return _ggba .ScaleLow (_bgbca ,_dfdc );};func _ccd (_dag ,_acb *Monochrome ,_fgd []byte ,_ec int )(_aebd error ){var (_aeaa ,_abcc ,_cdfe ,_fbc ,_fcde ,_cde ,_ffd ,_ged int ;
_aabe ,_dbd ,_ddgg ,_cbdf uint32 ;_cce ,_ccc byte ;_egea uint16 ;);_effb :=make ([]byte ,4);_fcf :=make ([]byte ,4);for _cdfe =0;_cdfe < _dag .Height -1;_cdfe ,_fbc =_cdfe +2,_fbc +1{_aeaa =_cdfe *_dag .BytesPerLine ;_abcc =_fbc *_acb .BytesPerLine ;for _fcde ,_cde =0,0;
_fcde < _ec ;_fcde ,_cde =_fcde +4,_cde +1{for _ffd =0;_ffd < 4;_ffd ++{_ged =_aeaa +_fcde +_ffd ;if _ged <=len (_dag .Data )-1&&_ged < _aeaa +_dag .BytesPerLine {_effb [_ffd ]=_dag .Data [_ged ];}else {_effb [_ffd ]=0x00;};_ged =_aeaa +_dag .BytesPerLine +_fcde +_ffd ;
if _ged <=len (_dag .Data )-1&&_ged < _aeaa +(2*_dag .BytesPerLine ){_fcf [_ffd ]=_dag .Data [_ged ];}else {_fcf [_ffd ]=0x00;};};_aabe =_c .BigEndian .Uint32 (_effb );_dbd =_c .BigEndian .Uint32 (_fcf );_ddgg =_aabe &_dbd ;_ddgg |=_ddgg <<1;_cbdf =_aabe |_dbd ;
_cbdf &=_cbdf <<1;_dbd =_ddgg &_cbdf ;_dbd &=0xaaaaaaaa;_aabe =_dbd |(_dbd <<7);_cce =byte (_aabe >>24);_ccc =byte ((_aabe >>8)&0xff);_ged =_abcc +_cde ;if _ged +1==len (_acb .Data )-1||_ged +1>=_abcc +_acb .BytesPerLine {if _aebd =_acb .setByte (_ged ,_fgd [_cce ]);
_aebd !=nil {return _cb .Errorf ("\u0069n\u0064\u0065\u0078\u003a\u0020\u0025d",_ged );};}else {_egea =(uint16 (_fgd [_cce ])<<8)|uint16 (_fgd [_ccc ]);if _aebd =_acb .setTwoBytes (_ged ,_egea );_aebd !=nil {return _cb .Errorf ("s\u0065\u0074\u0074\u0069\u006e\u0067 \u0074\u0077\u006f\u0020\u0062\u0079t\u0065\u0073\u0020\u0066\u0061\u0069\u006ce\u0064\u002c\u0020\u0069\u006e\u0064\u0065\u0078\u003a\u0020%\u0064",_ged );
};_cde ++;};};};return nil ;};func _agfg (_adba []byte ,_fada Image )error {_efgd :=true ;for _degbe :=0;_degbe < len (_adba );_degbe ++{if _adba [_degbe ]!=0xff{_efgd =false ;break ;};};if _efgd {switch _fega :=_fada .(type ){case *NRGBA32 :_fega .Alpha =nil ;
case *NRGBA64 :_fega .Alpha =nil ;default:return _cb .Errorf ("i\u006ete\u0072n\u0061l\u0020\u0065\u0072\u0072\u006fr\u0020\u002d\u0020i\u006d\u0061\u0067\u0065\u0020s\u0068\u006f\u0075l\u0064\u0020\u0062\u0065\u0020\u006f\u0066\u0020\u0074\u0079\u0070e\u0020\u002a\u004eRGB\u0041\u0033\u0032\u0020\u006f\u0072 \u002a\u004e\u0052\u0047\u0042\u0041\u0036\u0034\u0020\u0062\u0075\u0074 \u0069s\u003a\u0020\u0025\u0054",_fada );
};};return nil ;};func _egcd (){for _bbed :=0;_bbed < 256;_bbed ++{_daae [_bbed ]=uint8 (_bbed &0x1)+(uint8 (_bbed >>1)&0x1)+(uint8 (_bbed >>2)&0x1)+(uint8 (_bbed >>3)&0x1)+(uint8 (_bbed >>4)&0x1)+(uint8 (_bbed >>5)&0x1)+(uint8 (_bbed >>6)&0x1)+(uint8 (_bbed >>7)&0x1);
};};type shift int ;func (_fdec *Gray4 )Histogram ()(_ddga [256]int ){for _gage :=0;_gage < _fdec .Width ;_gage ++{for _gfde :=0;_gfde < _fdec .Height ;_gfde ++{_ddga [_fdec .GrayAt (_gage ,_gfde ).Y ]++;};};return _ddga ;};func _ddcab (_facgg Gray ,_egecc NRGBA ,_agef _ae .Rectangle ){for _ffbd :=0;
_ffbd < _agef .Max .X ;_ffbd ++{for _agec :=0;_agec < _agef .Max .Y ;_agec ++{_abefb :=_facgg .GrayAt (_ffbd ,_agec );_egecc .SetNRGBA (_ffbd ,_agec ,_beb (_abefb ));};};};func _gba (_agd Gray ,_decb nrgba64 ,_dfce _ae .Rectangle ){for _eded :=0;_eded < _dfce .Max .X ;
_eded ++{for _dagb :=0;_dagb < _dfce .Max .Y ;_dagb ++{_bfde :=_dbe (_decb .NRGBA64At (_eded ,_dagb ));_agd .SetGray (_eded ,_dagb ,_bfde );};};};func _bgdd (_eede _a .RGBA )_a .Gray {_fefd :=(19595*uint32 (_eede .R )+38470*uint32 (_eede .G )+7471*uint32 (_eede .B )+1<<7)>>16;
return _a .Gray {Y :uint8 (_fefd )};};func ConverterFunc (converterFunc func (_caf _ae .Image )(Image ,error ))ColorConverter {return colorConverter {_deag :converterFunc };};func _degb (_aba _a .NRGBA64 )_a .NRGBA {return _a .NRGBA {R :uint8 (_aba .R >>8),G :uint8 (_aba .G >>8),B :uint8 (_aba .B >>8),A :uint8 (_aba .A >>8)};
};func _geg (_fffe ,_aaa int ,_bddg []byte )*Monochrome {_bec :=_cfd (_fffe ,_aaa );_bec .Data =_bddg ;return _bec ;};func ColorAtGray2BPC (x ,y ,bytesPerLine int ,data []byte ,decode []float64 )(_a .Gray ,error ){_dded :=y *bytesPerLine +x >>2;if _dded >=len (data ){return _a .Gray {},_cb .Errorf ("\u0069\u006d\u0061\u0067\u0065\u0020\u0063\u006f\u006f\u0072\u0064\u0069\u006ea\u0074\u0065\u0073\u0020\u006f\u0075t\u0020\u006f\u0066\u0020\u0072\u0061\u006e\u0067\u0065\u0020\u0028\u0025\u0064,\u0020\u0025\u0064\u0029",x ,y );
};_cafd :=data [_dded ]>>uint (6-(x &3)*2)&3;if len (decode )==2{_cafd =uint8 (uint32 (LinearInterpolate (float64 (_cafd ),0,3.0,decode [0],decode [1]))&3);};return _a .Gray {Y :_cafd *85},nil ;};func _bcaac (_facd _a .Gray )_a .Gray {_facd .Y >>=4;_facd .Y |=_facd .Y <<4;
return _facd };func (_fedb *NRGBA32 )setRGBA (_bgaf int ,_bfcg _a .NRGBA ){_cbadd :=3*_bgaf ;_fedb .Data [_cbadd ]=_bfcg .R ;_fedb .Data [_cbadd +1]=_bfcg .G ;_fedb .Data [_cbadd +2]=_bfcg .B ;if _bgaf < len (_fedb .Alpha ){_fedb .Alpha [_bgaf ]=_bfcg .A ;
};};func (_dbdd *CMYK32 )Set (x ,y int ,c _a .Color ){_aca :=4*(y *_dbdd .Width +x );if _aca +3>=len (_dbdd .Data ){return ;};_eega :=_a .CMYKModel .Convert (c ).(_a .CMYK );_dbdd .Data [_aca ]=_eega .C ;_dbdd .Data [_aca +1]=_eega .M ;_dbdd .Data [_aca +2]=_eega .Y ;
_dbdd .Data [_aca +3]=_eega .K ;};func (_dfga *Gray16 )At (x ,y int )_a .Color {_bbee ,_ :=_dfga .ColorAt (x ,y );return _bbee };func (_dafec *NRGBA64 )SetNRGBA64 (x ,y int ,c _a .NRGBA64 ){_bbeg :=(y *_dafec .Width +x )*2;_aebcb :=_bbeg *3;if _aebcb +5>=len (_dafec .Data ){return ;
};_dafec .setNRGBA64 (_aebcb ,c ,_bbeg );};var (_fae =_be ();_ca =_eaa ();_abe =_fedc (););func (_fgbe *Gray4 )ColorAt (x ,y int )(_a .Color ,error ){return ColorAtGray4BPC (x ,y ,_fgbe .BytesPerLine ,_fgbe .Data ,_fgbe .Decode );};func (_bgdb *NRGBA32 )Base ()*ImageBase {return &_bgdb .ImageBase };
func _bada (_ffcgf _ae .Image ,_fbf Image ,_acad _ae .Rectangle ){if _bebc ,_adad :=_ffcgf .(SMasker );_adad &&_bebc .HasAlpha (){_fbf .(SMasker ).MakeAlpha ();};switch _dgdg :=_ffcgf .(type ){case Gray :_cffgg (_dgdg ,_fbf .(RGBA ),_acad );case NRGBA :_caed (_dgdg ,_fbf .(RGBA ),_acad );
case *_ae .NYCbCrA :_fccd (_dgdg ,_fbf .(RGBA ),_acad );case CMYK :_gabac (_dgdg ,_fbf .(RGBA ),_acad );case RGBA :_dcac (_dgdg ,_fbf .(RGBA ),_acad );case nrgba64 :_cfgec (_dgdg ,_fbf .(RGBA ),_acad );default:_gfgg (_ffcgf ,_fbf ,_acad );};};var _ NRGBA =&NRGBA16 {};
func (_gafd *Gray8 )Copy ()Image {return &Gray8 {ImageBase :_gafd .copy ()}};func (_cadf *ImageBase )GetAlpha ()[]byte {return _cadf .Alpha };func (_fbed *RGBA32 )Bounds ()_ae .Rectangle {return _ae .Rectangle {Max :_ae .Point {X :_fbed .Width ,Y :_fbed .Height }};
};func _baee (_bdb _ae .Image )(Image ,error ){if _dfgf ,_dbbg :=_bdb .(*Gray4 );_dbbg {return _dfgf .Copy (),nil ;};_cec :=_bdb .Bounds ();_defd ,_bda :=NewImage (_cec .Max .X ,_cec .Max .Y ,4,1,nil ,nil ,nil );if _bda !=nil {return nil ,_bda ;};_gcbf (_bdb ,_defd ,_cec );
return _defd ,nil ;};func _ede (_gcdc _a .CMYK )_a .NRGBA {_dbde ,_acf ,_gde :=_a .CMYKToRGB (_gcdc .C ,_gcdc .M ,_gcdc .Y ,_gcdc .K );return _a .NRGBA {R :_dbde ,G :_acf ,B :_gde ,A :0xff};};func (_cdb *Gray2 )At (x ,y int )_a .Color {_abfb ,_ :=_cdb .ColorAt (x ,y );
return _abfb };func (_eefc *NRGBA64 )ColorAt (x ,y int )(_a .Color ,error ){return ColorAtNRGBA64 (x ,y ,_eefc .Width ,_eefc .Data ,_eefc .Alpha ,_eefc .Decode );};func (_agc *CMYK32 )CMYKAt (x ,y int )_a .CMYK {_aegf ,_ :=ColorAtCMYK (x ,y ,_agc .Width ,_agc .Data ,_agc .Decode );
return _aegf ;};func _cfgec (_gafbg nrgba64 ,_dedg RGBA ,_ccgf _ae .Rectangle ){for _edee :=0;_edee < _ccgf .Max .X ;_edee ++{for _fbff :=0;_fbff < _ccgf .Max .Y ;_fbff ++{_aad :=_gafbg .NRGBA64At (_edee ,_fbff );_dedg .SetRGBA (_edee ,_fbff ,_bae (_aad ));
};};};func (_fcgb *Monochrome )setBit (_cage ,_aacd int ){_fcgb .Data [_cage +(_aacd >>3)]|=0x80>>uint (_aacd &7);};func _be ()(_fdc [256]uint16 ){for _bb :=0;_bb < 256;_bb ++{if _bb &0x01!=0{_fdc [_bb ]|=0x3;};if _bb &0x02!=0{_fdc [_bb ]|=0xc;};if _bb &0x04!=0{_fdc [_bb ]|=0x30;
};if _bb &0x08!=0{_fdc [_bb ]|=0xc0;};if _bb &0x10!=0{_fdc [_bb ]|=0x300;};if _bb &0x20!=0{_fdc [_bb ]|=0xc00;};if _bb &0x40!=0{_fdc [_bb ]|=0x3000;};if _bb &0x80!=0{_fdc [_bb ]|=0xc000;};};return _fdc ;};var _ Gray =&Gray2 {};func _ecf (_cbf _a .RGBA )_a .NRGBA {switch _cbf .A {case 0xff:return _a .NRGBA {R :_cbf .R ,G :_cbf .G ,B :_cbf .B ,A :0xff};
case 0x00:return _a .NRGBA {};default:_gdec ,_cfba ,_ffcd ,_face :=_cbf .RGBA ();_gdec =(_gdec *0xffff)/_face ;_cfba =(_cfba *0xffff)/_face ;_ffcd =(_ffcd *0xffff)/_face ;return _a .NRGBA {R :uint8 (_gdec >>8),G :uint8 (_cfba >>8),B :uint8 (_ffcd >>8),A :uint8 (_face >>8)};
};};var _ _ae .Image =&Gray16 {};func FromGoImage (i _ae .Image )(Image ,error ){switch _bggc :=i .(type ){case Image :return _bggc .Copy (),nil ;case Gray :return GrayConverter .Convert (i );case *_ae .Gray16 :return Gray16Converter .Convert (i );case CMYK :return CMYKConverter .Convert (i );
case *_ae .NRGBA64 :return NRGBA64Converter .Convert (i );default:return NRGBAConverter .Convert (i );};};func (_fddg *RGBA32 )At (x ,y int )_a .Color {_ebgc ,_ :=_fddg .ColorAt (x ,y );return _ebgc };func _fefcd (_dgbd _a .Color )_a .Color {_cegf :=_a .NRGBAModel .Convert (_dgbd ).(_a .NRGBA );
return _daed (_cegf );};type NRGBA64 struct{ImageBase };func _eef (_gegd _ae .Image )(Image ,error ){if _egf ,_ceaa :=_gegd .(*Monochrome );_ceaa {return _egf ,nil ;};_deea :=_gegd .Bounds ();var _edef Gray ;switch _cfdc :=_gegd .(type ){case Gray :_edef =_cfdc ;
case NRGBA :_edef =&Gray8 {ImageBase :NewImageBase (_deea .Max .X ,_deea .Max .Y ,8,1,nil ,nil ,nil )};_agce (_edef ,_cfdc ,_deea );case nrgba64 :_edef =&Gray8 {ImageBase :NewImageBase (_deea .Max .X ,_deea .Max .Y ,8,1,nil ,nil ,nil )};_gba (_edef ,_cfdc ,_deea );
default:_gadf ,_cgd :=GrayConverter .Convert (_gegd );if _cgd !=nil {return nil ,_cgd ;};_edef =_gadf .(Gray );};_bbag ,_abfa :=NewImage (_deea .Max .X ,_deea .Max .Y ,1,1,nil ,nil ,nil );if _abfa !=nil {return nil ,_abfa ;};_efda :=_bbag .(*Monochrome );
_efff :=AutoThresholdTriangle (GrayHistogram (_edef ));for _fce :=0;_fce < _deea .Max .X ;_fce ++{for _ded :=0;_ded < _deea .Max .Y ;_ded ++{_fdda :=_gbdd (_edef .GrayAt (_fce ,_ded ),monochromeModel (_efff ));_efda .SetGray (_fce ,_ded ,_fdda );};};return _bbag ,nil ;
};var _ Image =&CMYK32 {};func (_cgda *ImageBase )MakeAlpha (){_cgda .newAlpha ()};func _gfgg (_fgb _ae .Image ,_gfab Image ,_gfca _ae .Rectangle ){for _dadc :=0;_dadc < _gfca .Max .X ;_dadc ++{for _acbd :=0;_acbd < _gfca .Max .Y ;_acbd ++{_ffc :=_fgb .At (_dadc ,_acbd );
_gfab .Set (_dadc ,_acbd ,_ffc );};};};func (_gfdg *Monochrome )setIndexedBit (_dgb int ){_gfdg .Data [(_dgb >>3)]|=0x80>>uint (_dgb &7)};func AddDataPadding (width ,height ,bitsPerComponent ,colorComponents int ,data []byte )([]byte ,error ){_eabb :=BytesPerLine (width ,bitsPerComponent ,colorComponents );
if _eabb ==width *colorComponents *bitsPerComponent /8{return data ,nil ;};_bfgg :=width *colorComponents *bitsPerComponent ;_fbaaa :=_eabb *8;_dfgbb :=8-(_fbaaa -_bfgg );_gff :=_cbe .NewReader (data );_defb :=_eabb -1;_daaea :=make ([]byte ,_defb );_bfgd :=make ([]byte ,height *_eabb );
_dedbb :=_cbe .NewWriterMSB (_bfgd );var _fgf uint64 ;var _ebb error ;for _babe :=0;_babe < height ;_babe ++{_ ,_ebb =_gff .Read (_daaea );if _ebb !=nil {return nil ,_ebb ;};_ ,_ebb =_dedbb .Write (_daaea );if _ebb !=nil {return nil ,_ebb ;};_fgf ,_ebb =_gff .ReadBits (byte (_dfgbb ));
if _ebb !=nil {return nil ,_ebb ;};_ ,_ebb =_dedbb .WriteBits (_fgf ,_dfgbb );if _ebb !=nil {return nil ,_ebb ;};_dedbb .FinishByte ();};return _bfgd ,nil ;};func (_acca *Gray16 )Set (x ,y int ,c _a .Color ){_baba :=(y *_acca .BytesPerLine /2+x )*2;if _baba +1>=len (_acca .Data ){return ;
};_fffg :=_a .Gray16Model .Convert (c ).(_a .Gray16 );_acca .Data [_baba ],_acca .Data [_baba +1]=uint8 (_fffg .Y >>8),uint8 (_fffg .Y &0xff);};func _dgef (_caeg *Monochrome ,_defg ,_edffc ,_ceaaf ,_eedf int ,_fdad RasterOperator ,_bbgd *Monochrome ,_cecb ,_ccad int )error {if _caeg ==nil {return _f .New ("\u006e\u0069\u006c\u0020\u0027\u0064\u0065\u0073\u0074\u0027\u0020\u0042i\u0074\u006d\u0061\u0070");
};if _fdad ==PixDst {return nil ;};switch _fdad {case PixClr ,PixSet ,PixNotDst :_cagd (_caeg ,_defg ,_edffc ,_ceaaf ,_eedf ,_fdad );return nil ;};if _bbgd ==nil {_b .Log .Debug ("\u0052a\u0073\u0074e\u0072\u004f\u0070\u0065r\u0061\u0074\u0069o\u006e\u0020\u0073\u006f\u0075\u0072\u0063\u0065\u0020bi\u0074\u006d\u0061p\u0020\u0069s\u0020\u006e\u006f\u0074\u0020\u0064e\u0066\u0069n\u0065\u0064");
return _f .New ("\u006e\u0069l\u0020\u0027\u0073r\u0063\u0027\u0020\u0062\u0069\u0074\u006d\u0061\u0070");};if _fbgc :=_beca (_caeg ,_defg ,_edffc ,_ceaaf ,_eedf ,_fdad ,_bbgd ,_cecb ,_ccad );_fbgc !=nil {return _fbgc ;};return nil ;};func (_aded *Gray8 )Validate ()error {if len (_aded .Data )!=_aded .Height *_aded .BytesPerLine {return ErrInvalidImage ;
};return nil ;};func ColorAtGray1BPC (x ,y ,bytesPerLine int ,data []byte ,decode []float64 )(_a .Gray ,error ){_egd :=y *bytesPerLine +x >>3;if _egd >=len (data ){return _a .Gray {},_cb .Errorf ("\u0069\u006d\u0061\u0067\u0065\u0020\u0063\u006f\u006f\u0072\u0064\u0069\u006ea\u0074\u0065\u0073\u0020\u006f\u0075t\u0020\u006f\u0066\u0020\u0072\u0061\u006e\u0067\u0065\u0020\u0028\u0025\u0064,\u0020\u0025\u0064\u0029",x ,y );
};_cdee :=data [_egd ]>>uint (7-(x &7))&1;if len (decode )==2{_cdee =uint8 (LinearInterpolate (float64 (_cdee ),0.0,1.0,decode [0],decode [1]))&1;};return _a .Gray {Y :_cdee *255},nil ;};type NRGBA16 struct{ImageBase };func (_abda *NRGBA16 )Set (x ,y int ,c _a .Color ){_cbdb :=y *_abda .BytesPerLine +x *3/2;
if _cbdb +1>=len (_abda .Data ){return ;};_eac :=NRGBA16Model .Convert (c ).(_a .NRGBA );_abda .setNRGBA (x ,y ,_cbdb ,_eac );};func _bgbde (_gffg *Monochrome ,_ddgb ,_eagfb ,_dgdd ,_cecc int ,_adb RasterOperator ,_abac *Monochrome ,_agcb ,_cgdad int )error {var (_aabfd byte ;
_fcbg int ;_ffea int ;_acedb ,_ceb int ;_ggdb ,_cecf int ;);_ddcd :=_dgdd >>3;_aedd :=_dgdd &7;if _aedd > 0{_aabfd =_aabb [_aedd ];};_fcbg =_abac .BytesPerLine *_cgdad +(_agcb >>3);_ffea =_gffg .BytesPerLine *_eagfb +(_ddgb >>3);switch _adb {case PixSrc :for _ggdb =0;
_ggdb < _cecc ;_ggdb ++{_acedb =_fcbg +_ggdb *_abac .BytesPerLine ;_ceb =_ffea +_ggdb *_gffg .BytesPerLine ;for _cecf =0;_cecf < _ddcd ;_cecf ++{_gffg .Data [_ceb ]=_abac .Data [_acedb ];_ceb ++;_acedb ++;};if _aedd > 0{_gffg .Data [_ceb ]=_cecg (_gffg .Data [_ceb ],_abac .Data [_acedb ],_aabfd );
};};case PixNotSrc :for _ggdb =0;_ggdb < _cecc ;_ggdb ++{_acedb =_fcbg +_ggdb *_abac .BytesPerLine ;_ceb =_ffea +_ggdb *_gffg .BytesPerLine ;for _cecf =0;_cecf < _ddcd ;_cecf ++{_gffg .Data [_ceb ]=^(_abac .Data [_acedb ]);_ceb ++;_acedb ++;};if _aedd > 0{_gffg .Data [_ceb ]=_cecg (_gffg .Data [_ceb ],^_abac .Data [_acedb ],_aabfd );
};};case PixSrcOrDst :for _ggdb =0;_ggdb < _cecc ;_ggdb ++{_acedb =_fcbg +_ggdb *_abac .BytesPerLine ;_ceb =_ffea +_ggdb *_gffg .BytesPerLine ;for _cecf =0;_cecf < _ddcd ;_cecf ++{_gffg .Data [_ceb ]|=_abac .Data [_acedb ];_ceb ++;_acedb ++;};if _aedd > 0{_gffg .Data [_ceb ]=_cecg (_gffg .Data [_ceb ],_abac .Data [_acedb ]|_gffg .Data [_ceb ],_aabfd );
};};case PixSrcAndDst :for _ggdb =0;_ggdb < _cecc ;_ggdb ++{_acedb =_fcbg +_ggdb *_abac .BytesPerLine ;_ceb =_ffea +_ggdb *_gffg .BytesPerLine ;for _cecf =0;_cecf < _ddcd ;_cecf ++{_gffg .Data [_ceb ]&=_abac .Data [_acedb ];_ceb ++;_acedb ++;};if _aedd > 0{_gffg .Data [_ceb ]=_cecg (_gffg .Data [_ceb ],_abac .Data [_acedb ]&_gffg .Data [_ceb ],_aabfd );
};};case PixSrcXorDst :for _ggdb =0;_ggdb < _cecc ;_ggdb ++{_acedb =_fcbg +_ggdb *_abac .BytesPerLine ;_ceb =_ffea +_ggdb *_gffg .BytesPerLine ;for _cecf =0;_cecf < _ddcd ;_cecf ++{_gffg .Data [_ceb ]^=_abac .Data [_acedb ];_ceb ++;_acedb ++;};if _aedd > 0{_gffg .Data [_ceb ]=_cecg (_gffg .Data [_ceb ],_abac .Data [_acedb ]^_gffg .Data [_ceb ],_aabfd );
};};case PixNotSrcOrDst :for _ggdb =0;_ggdb < _cecc ;_ggdb ++{_acedb =_fcbg +_ggdb *_abac .BytesPerLine ;_ceb =_ffea +_ggdb *_gffg .BytesPerLine ;for _cecf =0;_cecf < _ddcd ;_cecf ++{_gffg .Data [_ceb ]|=^(_abac .Data [_acedb ]);_ceb ++;_acedb ++;};if _aedd > 0{_gffg .Data [_ceb ]=_cecg (_gffg .Data [_ceb ],^(_abac .Data [_acedb ])|_gffg .Data [_ceb ],_aabfd );
};};case PixNotSrcAndDst :for _ggdb =0;_ggdb < _cecc ;_ggdb ++{_acedb =_fcbg +_ggdb *_abac .BytesPerLine ;_ceb =_ffea +_ggdb *_gffg .BytesPerLine ;for _cecf =0;_cecf < _ddcd ;_cecf ++{_gffg .Data [_ceb ]&=^(_abac .Data [_acedb ]);_ceb ++;_acedb ++;};if _aedd > 0{_gffg .Data [_ceb ]=_cecg (_gffg .Data [_ceb ],^(_abac .Data [_acedb ])&_gffg .Data [_ceb ],_aabfd );
};};case PixSrcOrNotDst :for _ggdb =0;_ggdb < _cecc ;_ggdb ++{_acedb =_fcbg +_ggdb *_abac .BytesPerLine ;_ceb =_ffea +_ggdb *_gffg .BytesPerLine ;for _cecf =0;_cecf < _ddcd ;_cecf ++{_gffg .Data [_ceb ]=_abac .Data [_acedb ]|^(_gffg .Data [_ceb ]);_ceb ++;
_acedb ++;};if _aedd > 0{_gffg .Data [_ceb ]=_cecg (_gffg .Data [_ceb ],_abac .Data [_acedb ]|^(_gffg .Data [_ceb ]),_aabfd );};};case PixSrcAndNotDst :for _ggdb =0;_ggdb < _cecc ;_ggdb ++{_acedb =_fcbg +_ggdb *_abac .BytesPerLine ;_ceb =_ffea +_ggdb *_gffg .BytesPerLine ;
for _cecf =0;_cecf < _ddcd ;_cecf ++{_gffg .Data [_ceb ]=_abac .Data [_acedb ]&^(_gffg .Data [_ceb ]);_ceb ++;_acedb ++;};if _aedd > 0{_gffg .Data [_ceb ]=_cecg (_gffg .Data [_ceb ],_abac .Data [_acedb ]&^(_gffg .Data [_ceb ]),_aabfd );};};case PixNotPixSrcOrDst :for _ggdb =0;
_ggdb < _cecc ;_ggdb ++{_acedb =_fcbg +_ggdb *_abac .BytesPerLine ;_ceb =_ffea +_ggdb *_gffg .BytesPerLine ;for _cecf =0;_cecf < _ddcd ;_cecf ++{_gffg .Data [_ceb ]=^(_abac .Data [_acedb ]|_gffg .Data [_ceb ]);_ceb ++;_acedb ++;};if _aedd > 0{_gffg .Data [_ceb ]=_cecg (_gffg .Data [_ceb ],^(_abac .Data [_acedb ]|_gffg .Data [_ceb ]),_aabfd );
};};case PixNotPixSrcAndDst :for _ggdb =0;_ggdb < _cecc ;_ggdb ++{_acedb =_fcbg +_ggdb *_abac .BytesPerLine ;_ceb =_ffea +_ggdb *_gffg .BytesPerLine ;for _cecf =0;_cecf < _ddcd ;_cecf ++{_gffg .Data [_ceb ]=^(_abac .Data [_acedb ]&_gffg .Data [_ceb ]);
_ceb ++;_acedb ++;};if _aedd > 0{_gffg .Data [_ceb ]=_cecg (_gffg .Data [_ceb ],^(_abac .Data [_acedb ]&_gffg .Data [_ceb ]),_aabfd );};};case PixNotPixSrcXorDst :for _ggdb =0;_ggdb < _cecc ;_ggdb ++{_acedb =_fcbg +_ggdb *_abac .BytesPerLine ;_ceb =_ffea +_ggdb *_gffg .BytesPerLine ;
for _cecf =0;_cecf < _ddcd ;_cecf ++{_gffg .Data [_ceb ]=^(_abac .Data [_acedb ]^_gffg .Data [_ceb ]);_ceb ++;_acedb ++;};if _aedd > 0{_gffg .Data [_ceb ]=_cecg (_gffg .Data [_ceb ],^(_abac .Data [_acedb ]^_gffg .Data [_ceb ]),_aabfd );};};default:_b .Log .Debug ("\u0050\u0072ov\u0069\u0064\u0065d\u0020\u0069\u006e\u0076ali\u0064 r\u0061\u0073\u0074\u0065\u0072\u0020\u006fpe\u0072\u0061\u0074\u006f\u0072\u003a\u0020%\u0076",_adb );
return _f .New ("\u0069\u006e\u0076al\u0069\u0064\u0020\u0072\u0061\u0073\u0074\u0065\u0072\u0020\u006f\u0070\u0065\u0072\u0061\u0074\u006f\u0072");};return nil ;};func (_gbbeg *Monochrome )RasterOperation (dx ,dy ,dw ,dh int ,op RasterOperator ,src *Monochrome ,sx ,sy int )error {return _dgef (_gbbeg ,dx ,dy ,dw ,dh ,op ,src ,sx ,sy );
};type Gray16 struct{ImageBase };func (_cfbd *CMYK32 )ColorModel ()_a .Model {return _a .CMYKModel };func (_cbca *Monochrome )InverseData ()error {return _cbca .RasterOperation (0,0,_cbca .Width ,_cbca .Height ,PixNotDst ,nil ,0,0);};func _beca (_bcbg *Monochrome ,_dfa ,_dadf int ,_aace ,_aeaf int ,_cfcae RasterOperator ,_fbb *Monochrome ,_dbaa ,_egfe int )error {var _daggg ,_ffcg ,_adcg ,_afg int ;
if _dfa < 0{_dbaa -=_dfa ;_aace +=_dfa ;_dfa =0;};if _dbaa < 0{_dfa -=_dbaa ;_aace +=_dbaa ;_dbaa =0;};_daggg =_dfa +_aace -_bcbg .Width ;if _daggg > 0{_aace -=_daggg ;};_ffcg =_dbaa +_aace -_fbb .Width ;if _ffcg > 0{_aace -=_ffcg ;};if _dadf < 0{_egfe -=_dadf ;
_aeaf +=_dadf ;_dadf =0;};if _egfe < 0{_dadf -=_egfe ;_aeaf +=_egfe ;_egfe =0;};_adcg =_dadf +_aeaf -_bcbg .Height ;if _adcg > 0{_aeaf -=_adcg ;};_afg =_egfe +_aeaf -_fbb .Height ;if _afg > 0{_aeaf -=_afg ;};if _aace <=0||_aeaf <=0{return nil ;};var _cfgac error ;
switch {case _dfa &7==0&&_dbaa &7==0:_cfgac =_bgbde (_bcbg ,_dfa ,_dadf ,_aace ,_aeaf ,_cfcae ,_fbb ,_dbaa ,_egfe );case _dfa &7==_dbaa &7:_cfgac =_adda (_bcbg ,_dfa ,_dadf ,_aace ,_aeaf ,_cfcae ,_fbb ,_dbaa ,_egfe );default:_cfgac =_fgcd (_bcbg ,_dfa ,_dadf ,_aace ,_aeaf ,_cfcae ,_fbb ,_dbaa ,_egfe );
};if _cfgac !=nil {return _cfgac ;};return nil ;};func (_bbcg *NRGBA16 )setNRGBA (_agga ,_bdc ,_degdf int ,_bfba _a .NRGBA ){if _agga *3%2==0{_bbcg .Data [_degdf ]=(_bfba .R >>4)<<4|(_bfba .G >>4);_bbcg .Data [_degdf +1]=(_bfba .B >>4)<<4|(_bbcg .Data [_degdf +1]&0xf);
}else {_bbcg .Data [_degdf ]=(_bbcg .Data [_degdf ]&0xf0)|(_bfba .R >>4);_bbcg .Data [_degdf +1]=(_bfba .G >>4)<<4|(_bfba .B >>4);};if _bbcg .Alpha !=nil {_fbgag :=_bdc *BytesPerLine (_bbcg .Width ,4,1);if _fbgag < len (_bbcg .Alpha ){if _agga %2==0{_bbcg .Alpha [_fbgag ]=(_bfba .A >>uint (4))<<uint (4)|(_bbcg .Alpha [_degdf ]&0xf);
}else {_bbcg .Alpha [_fbgag ]=(_bbcg .Alpha [_fbgag ]&0xf0)|(_bfba .A >>uint (4));};};};};func _gbdd (_def _a .Gray ,_eeca monochromeModel )_a .Gray {if _def .Y > uint8 (_eeca ){return _a .Gray {Y :_d .MaxUint8 };};return _a .Gray {};};type Gray interface{GrayAt (_ebee ,_dcbb int )_a .Gray ;
SetGray (_eecd ,_cbea int ,_dfg _a .Gray );};func ColorAtNRGBA16 (x ,y ,width ,bytesPerLine int ,data ,alpha []byte ,decode []float64 )(_a .NRGBA ,error ){_bfdf :=y *bytesPerLine +x *3/2;if _bfdf +1>=len (data ){return _a .NRGBA {},_fgaf (x ,y );};const (_abag =0xf;
_geac =uint8 (0xff););_gecf :=_geac ;if alpha !=nil {_ggcag :=y *BytesPerLine (width ,4,1);if _ggcag < len (alpha ){if x %2==0{_gecf =(alpha [_ggcag ]>>uint (4))&_abag ;}else {_gecf =alpha [_ggcag ]&_abag ;};_gecf |=_gecf <<4;};};var _agbd ,_adee ,_bagbb uint8 ;
if x *3%2==0{_agbd =(data [_bfdf ]>>uint (4))&_abag ;_adee =data [_bfdf ]&_abag ;_bagbb =(data [_bfdf +1]>>uint (4))&_abag ;}else {_agbd =data [_bfdf ]&_abag ;_adee =(data [_bfdf +1]>>uint (4))&_abag ;_bagbb =data [_bfdf +1]&_abag ;};if len (decode )==6{_agbd =uint8 (uint32 (LinearInterpolate (float64 (_agbd ),0,15,decode [0],decode [1]))&0xf);
_adee =uint8 (uint32 (LinearInterpolate (float64 (_adee ),0,15,decode [2],decode [3]))&0xf);_bagbb =uint8 (uint32 (LinearInterpolate (float64 (_bagbb ),0,15,decode [4],decode [5]))&0xf);};return _a .NRGBA {R :(_agbd <<4)|(_agbd &0xf),G :(_adee <<4)|(_adee &0xf),B :(_bagbb <<4)|(_bagbb &0xf),A :_gecf },nil ;
};func (_ebca *CMYK32 )Copy ()Image {return &CMYK32 {ImageBase :_ebca .copy ()}};func (_afcg *Monochrome )ScaleLow (width ,height int )(*Monochrome ,error ){if width < 0||height < 0{return nil ,_f .New ("\u0070\u0072\u006f\u0076\u0069\u0064e\u0064\u0020\u0069\u006e\u0076\u0061\u006c\u0069\u0064\u0020\u0077\u0069\u0064t\u0068\u0020\u0061\u006e\u0064\u0020\u0068e\u0069\u0067\u0068\u0074");
};_bfdc :=_cfd (width ,height );_cgdc :=make ([]int ,height );_aega :=make ([]int ,width );_bdf :=float64 (_afcg .Width )/float64 (width );_cad :=float64 (_afcg .Height )/float64 (height );for _bbfc :=0;_bbfc < height ;_bbfc ++{_cgdc [_bbfc ]=int (_d .Min (_cad *float64 (_bbfc )+0.5,float64 (_afcg .Height -1)));
};for _gbe :=0;_gbe < width ;_gbe ++{_aega [_gbe ]=int (_d .Min (_bdf *float64 (_gbe )+0.5,float64 (_afcg .Width -1)));};_abeb :=-1;_deab :=byte (0);for _egeg :=0;_egeg < height ;_egeg ++{_dege :=_cgdc [_egeg ]*_afcg .BytesPerLine ;_ccbd :=_egeg *_bfdc .BytesPerLine ;
for _dgf :=0;_dgf < width ;_dgf ++{_bcgc :=_aega [_dgf ];if _bcgc !=_abeb {_deab =_afcg .getBit (_dege ,_bcgc );if _deab !=0{_bfdc .setBit (_ccbd ,_dgf );};_abeb =_bcgc ;}else {if _deab !=0{_bfdc .setBit (_ccbd ,_dgf );};};};};return _bfdc ,nil ;};func (_dfgb *Gray16 )Histogram ()(_aced [256]int ){for _baae :=0;
_baae < _dfgb .Width ;_baae ++{for _bage :=0;_bage < _dfgb .Height ;_bage ++{_aced [_dfgb .GrayAt (_baae ,_bage ).Y ]++;};};return _aced ;};var _ Image =&Gray16 {};func NewImageBase (width int ,height int ,bitsPerComponent int ,colorComponents int ,data []byte ,alpha []byte ,decode []float64 )ImageBase {_bcef :=ImageBase {Width :width ,Height :height ,BitsPerComponent :bitsPerComponent ,ColorComponents :colorComponents ,Data :data ,Alpha :alpha ,Decode :decode ,BytesPerLine :BytesPerLine (width ,bitsPerComponent ,colorComponents )};
if data ==nil {_bcef .Data =make ([]byte ,height *_bcef .BytesPerLine );};return _bcef ;};var _ NRGBA =&NRGBA32 {};func (_bgcc *RGBA32 )ColorModel ()_a .Model {return _a .NRGBAModel };func (_cfgb *Gray4 )Set (x ,y int ,c _a .Color ){if x >=_cfgb .Width ||y >=_cfgb .Height {return ;
};_efb :=Gray4Model .Convert (c ).(_a .Gray );_cfgb .setGray (x ,y ,_efb );};func _acgg (_fded int ,_aef int )int {if _fded < _aef {return _fded ;};return _aef ;};var _ _ae .Image =&NRGBA16 {};func (_ddba *Gray16 )ColorAt (x ,y int )(_a .Color ,error ){return ColorAtGray16BPC (x ,y ,_ddba .BytesPerLine ,_ddba .Data ,_ddba .Decode );
};func (_bef *ImageBase )setTwoBytes (_bff int ,_fegd uint16 )error {if _bff +1> len (_bef .Data )-1{return _f .New ("\u0069n\u0064e\u0078\u0020\u006f\u0075\u0074 \u006f\u0066 \u0072\u0061\u006e\u0067\u0065");};_bef .Data [_bff ]=byte ((_fegd &0xff00)>>8);
_bef .Data [_bff +1]=byte (_fegd &0xff);return nil ;};func (_bfbba *NRGBA16 )ColorAt (x ,y int )(_a .Color ,error ){return ColorAtNRGBA16 (x ,y ,_bfbba .Width ,_bfbba .BytesPerLine ,_bfbba .Data ,_bfbba .Alpha ,_bfbba .Decode );};func (_edff *ImageBase )setEightBytes (_fegfa int ,_fadb uint64 )error {_dgea :=_edff .BytesPerLine -(_fegfa %_edff .BytesPerLine );
if _edff .BytesPerLine !=_edff .Width >>3{_dgea --;};if _dgea >=8{return _edff .setEightFullBytes (_fegfa ,_fadb );};return _edff .setEightPartlyBytes (_fegfa ,_dgea ,_fadb );};func _ee (_ebf ,_cf *Monochrome )(_ebc error ){_cdc :=_cf .BytesPerLine ;_faf :=_ebf .BytesPerLine ;
var _fe ,_fed ,_dgc ,_ac ,_fff int ;for _dgc =0;_dgc < _cf .Height ;_dgc ++{_fe =_dgc *_cdc ;_fed =8*_dgc *_faf ;for _ac =0;_ac < _cdc ;_ac ++{if _ebc =_ebf .setEightBytes (_fed +_ac *8,_abe [_cf .Data [_fe +_ac ]]);_ebc !=nil {return _ebc ;};};for _fff =1;
_fff < 8;_fff ++{for _ac =0;_ac < _faf ;_ac ++{if _ebc =_ebf .setByte (_fed +_fff *_faf +_ac ,_ebf .Data [_fed +_ac ]);_ebc !=nil {return _ebc ;};};};};return nil ;};func _fgaf (_cgdb int ,_ffec int )error {return _cb .Errorf ("\u0069\u006d\u0061\u0067\u0065\u0020\u0063\u006f\u006f\u0072\u0064\u0069\u006ea\u0074\u0065\u0073\u0020\u006f\u0075t\u0020\u006f\u0066\u0020\u0072\u0061\u006e\u0067\u0065\u0020\u0028\u0025\u0064,\u0020\u0025\u0064\u0029",_cgdb ,_ffec );
};func _ecfc (_gfe *Monochrome ,_gged ,_gdf int ,_fegb ,_cada int ,_fdgdc RasterOperator ){var (_cedc int ;_ggda byte ;_cedee ,_aafd int ;_cda int ;);_gedc :=_fegb >>3;_cdda :=_fegb &7;if _cdda > 0{_ggda =_aabb [_cdda ];};_cedc =_gfe .BytesPerLine *_gdf +(_gged >>3);
switch _fdgdc {case PixClr :for _cedee =0;_cedee < _cada ;_cedee ++{_cda =_cedc +_cedee *_gfe .BytesPerLine ;for _aafd =0;_aafd < _gedc ;_aafd ++{_gfe .Data [_cda ]=0x0;_cda ++;};if _cdda > 0{_gfe .Data [_cda ]=_cecg (_gfe .Data [_cda ],0x0,_ggda );};};
case PixSet :for _cedee =0;_cedee < _cada ;_cedee ++{_cda =_cedc +_cedee *_gfe .BytesPerLine ;for _aafd =0;_aafd < _gedc ;_aafd ++{_gfe .Data [_cda ]=0xff;_cda ++;};if _cdda > 0{_gfe .Data [_cda ]=_cecg (_gfe .Data [_cda ],0xff,_ggda );};};case PixNotDst :for _cedee =0;
_cedee < _cada ;_cedee ++{_cda =_cedc +_cedee *_gfe .BytesPerLine ;for _aafd =0;_aafd < _gedc ;_aafd ++{_gfe .Data [_cda ]=^_gfe .Data [_cda ];_cda ++;};if _cdda > 0{_gfe .Data [_cda ]=_cecg (_gfe .Data [_cda ],^_gfe .Data [_cda ],_ggda );};};};};func _afae (_bdgb CMYK ,_cee NRGBA ,_gfga _ae .Rectangle ){for _fedd :=0;
_fedd < _gfga .Max .X ;_fedd ++{for _cecfa :=0;_cecfa < _gfga .Max .Y ;_cecfa ++{_dcdf :=_bdgb .CMYKAt (_fedd ,_cecfa );_cee .SetNRGBA (_fedd ,_cecfa ,_ede (_dcdf ));};};};func (_gfbf *Gray16 )Validate ()error {if len (_gfbf .Data )!=_gfbf .Height *_gfbf .BytesPerLine {return ErrInvalidImage ;
};return nil ;};func _ggff (_dbbca ,_gccaa uint8 )uint8 {if _dbbca < _gccaa {return 255;};return 0;};func _fgdb (_bbgaf _ae .Image )(Image ,error ){if _cdfg ,_effbc :=_bbgaf .(*Gray8 );_effbc {return _cdfg .Copy (),nil ;};_bgfb :=_bbgaf .Bounds ();_bdba ,_adg :=NewImage (_bgfb .Max .X ,_bgfb .Max .Y ,8,1,nil ,nil ,nil );
if _adg !=nil {return nil ,_adg ;};_gcbf (_bbgaf ,_bdba ,_bgfb );return _bdba ,nil ;};func (_fbad *Gray2 )Copy ()Image {return &Gray2 {ImageBase :_fbad .copy ()}};var _ Gray =&Monochrome {};func _agcc (_bfge _ae .Image )(Image ,error ){if _gedf ,_acg :=_bfge .(*Gray16 );
_acg {return _gedf .Copy (),nil ;};_edd :=_bfge .Bounds ();_cgf ,_gdg :=NewImage (_edd .Max .X ,_edd .Max .Y ,16,1,nil ,nil ,nil );if _gdg !=nil {return nil ,_gdg ;};_gcbf (_bfge ,_cgf ,_edd );return _cgf ,nil ;};func _bafe (_edaa ,_abdd NRGBA ,_cfa _ae .Rectangle ){for _cfad :=0;
_cfad < _cfa .Max .X ;_cfad ++{for _eeb :=0;_eeb < _cfa .Max .Y ;_eeb ++{_abdd .SetNRGBA (_cfad ,_eeb ,_edaa .NRGBAAt (_cfad ,_eeb ));};};};var _ _ae .Image =&NRGBA32 {};func _efdb (_bce _a .Gray )_a .CMYK {return _a .CMYK {K :0xff-_bce .Y }};func (_dcd monochromeModel )Convert (c _a .Color )_a .Color {_aaee :=_a .GrayModel .Convert (c ).(_a .Gray );
return _gbdd (_aaee ,_dcd );};func (_egca *ImageBase )setFourBytes (_edcg int ,_dca uint32 )error {if _edcg +3> len (_egca .Data )-1{return _cb .Errorf ("\u0069n\u0064\u0065\u0078\u003a\u0020\u0027\u0025\u0064\u0027\u0020\u006fu\u0074\u0020\u006f\u0066\u0020\u0072\u0061\u006e\u0067\u0065",_edcg );
};_egca .Data [_edcg ]=byte ((_dca &0xff000000)>>24);_egca .Data [_edcg +1]=byte ((_dca &0xff0000)>>16);_egca .Data [_edcg +2]=byte ((_dca &0xff00)>>8);_egca .Data [_edcg +3]=byte (_dca &0xff);return nil ;};func (_afgf *NRGBA32 )Copy ()Image {return &NRGBA32 {ImageBase :_afgf .copy ()}};
var _ Image =&NRGBA64 {};func (_edgd *Gray16 )Base ()*ImageBase {return &_edgd .ImageBase };func (_dcff *Monochrome )Bounds ()_ae .Rectangle {return _ae .Rectangle {Max :_ae .Point {X :_dcff .Width ,Y :_dcff .Height }};};func (_fcg *Monochrome )getBit (_aac ,_eefd int )uint8 {return _fcg .Data [_aac +(_eefd >>3)]>>uint (7-(_eefd &7))&1;
};var _ Image =&NRGBA32 {};func ColorAtNRGBA32 (x ,y ,width int ,data ,alpha []byte ,decode []float64 )(_a .NRGBA ,error ){_bcbe :=y *width +x ;_bgea :=3*_bcbe ;if _bgea +2>=len (data ){return _a .NRGBA {},_cb .Errorf ("\u0069\u006d\u0061\u0067\u0065\u0020\u0063\u006f\u006f\u0072\u0064\u0069\u006ea\u0074\u0065\u0073\u0020\u006f\u0075t\u0020\u006f\u0066\u0020\u0072\u0061\u006e\u0067\u0065\u0020\u0028\u0025\u0064,\u0020\u0025\u0064\u0029",x ,y );
};_agfc :=uint8 (0xff);if alpha !=nil &&len (alpha )> _bcbe {_agfc =alpha [_bcbe ];};_bfaf ,_edfe ,_gecd :=data [_bgea ],data [_bgea +1],data [_bgea +2];if len (decode )==6{_aecf :=LinearInterpolate (float64 (_bfaf ),0,255.0,decode [0],decode [1]);_cbecd :=LinearInterpolate (float64 (_edfe ),0,255.0,decode [2],decode [3]);
_dgae :=LinearInterpolate (float64 (_gecd ),0,255.0,decode [4],decode [5]);if _aecf <=1.0&&_cbecd <=1.0&&_dgae <=1.0{_aecf *=255.0;_cbecd *=255.0;_dgae *=255.0;};_bfaf =uint8 (_aecf )&0xff;_edfe =uint8 (_cbecd )&0xff;_gecd =uint8 (_dgae )&0xff;};return _a .NRGBA {R :_bfaf ,G :_edfe ,B :_gecd ,A :_agfc },nil ;
};func (_ggeg *NRGBA64 )Copy ()Image {return &NRGBA64 {ImageBase :_ggeg .copy ()}};var (_aabb =[]byte {0x00,0x80,0xC0,0xE0,0xF0,0xF8,0xFC,0xFE,0xFF};_aage =[]byte {0x00,0x01,0x03,0x07,0x0F,0x1F,0x3F,0x7F,0xFF};);func ColorAtGray8BPC (x ,y ,bytesPerLine int ,data []byte ,decode []float64 )(_a .Gray ,error ){_bcbf :=y *bytesPerLine +x ;
if _bcbf >=len (data ){return _a .Gray {},_cb .Errorf ("\u0069\u006d\u0061\u0067\u0065\u0020\u0063\u006f\u006f\u0072\u0064\u0069\u006ea\u0074\u0065\u0073\u0020\u006f\u0075t\u0020\u006f\u0066\u0020\u0072\u0061\u006e\u0067\u0065\u0020\u0028\u0025\u0064,\u0020\u0025\u0064\u0029",x ,y );
};_cef :=data [_bcbf ];if len (decode )==2{_cef =uint8 (uint32 (LinearInterpolate (float64 (_cef ),0,255,decode [0],decode [1]))&0xff);};return _a .Gray {Y :_cef },nil ;};var _ _ae .Image =&Gray4 {};func (_cbg *Gray4 )Bounds ()_ae .Rectangle {return _ae .Rectangle {Max :_ae .Point {X :_cbg .Width ,Y :_cbg .Height }};
};func (_dbff *Gray2 )GrayAt (x ,y int )_a .Gray {_dbfg ,_ :=ColorAtGray2BPC (x ,y ,_dbff .BytesPerLine ,_dbff .Data ,_dbff .Decode );return _dbfg ;};func _cffgg (_fccce Gray ,_aabg RGBA ,_dbec _ae .Rectangle ){for _bdfa :=0;_bdfa < _dbec .Max .X ;_bdfa ++{for _agdg :=0;
_agdg < _dbec .Max .Y ;_agdg ++{_ccab :=_fccce .GrayAt (_bdfa ,_agdg );_aabg .SetRGBA (_bdfa ,_agdg ,_fde (_ccab ));};};};func IsGrayImgBlackAndWhite (i *_ae .Gray )bool {return _fffd (i )};type monochromeThresholdConverter struct{Threshold uint8 ;};func GrayHistogram (g Gray )(_bcdc [256]int ){switch _gfce :=g .(type ){case Histogramer :return _gfce .Histogram ();
case _ae .Image :_dccb :=_gfce .Bounds ();for _gbfd :=0;_gbfd < _dccb .Max .X ;_gbfd ++{for _fcdg :=0;_fcdg < _dccb .Max .Y ;_fcdg ++{_bcdc [g .GrayAt (_gbfd ,_fcdg ).Y ]++;};};return _bcdc ;default:return [256]int {};};};func _fceg (_ead _ae .Image )(Image ,error ){if _bfcgc ,_dfaf :=_ead .(*NRGBA64 );
_dfaf {return _bfcgc .Copy (),nil ;};_ffdf ,_gagaf ,_agggc :=_fdfg (_ead ,2);_gbfb ,_deae :=NewImage (_ffdf .Max .X ,_ffdf .Max .Y ,16,3,nil ,_agggc ,nil );if _deae !=nil {return nil ,_deae ;};_befc (_ead ,_gbfb ,_ffdf );if len (_agggc )!=0&&!_gagaf {if _aeab :=_agfg (_agggc ,_gbfb );
_aeab !=nil {return nil ,_aeab ;};};return _gbfb ,nil ;};func BytesPerLine (width ,bitsPerComponent ,colorComponents int )int {return ((width *bitsPerComponent )*colorComponents +7)>>3;};func ColorAtGray4BPC (x ,y ,bytesPerLine int ,data []byte ,decode []float64 )(_a .Gray ,error ){_gaec :=y *bytesPerLine +x >>1;
if _gaec >=len (data ){return _a .Gray {},_cb .Errorf ("\u0069\u006d\u0061\u0067\u0065\u0020\u0063\u006f\u006f\u0072\u0064\u0069\u006ea\u0074\u0065\u0073\u0020\u006f\u0075t\u0020\u006f\u0066\u0020\u0072\u0061\u006e\u0067\u0065\u0020\u0028\u0025\u0064,\u0020\u0025\u0064\u0029",x ,y );
};_bcdd :=data [_gaec ]>>uint (4-(x &1)*4)&0xf;if len (decode )==2{_bcdd =uint8 (uint32 (LinearInterpolate (float64 (_bcdd ),0,15,decode [0],decode [1]))&0xf);};return _a .Gray {Y :_bcdd *17&0xff},nil ;};func (_dagf *NRGBA64 )Bounds ()_ae .Rectangle {return _ae .Rectangle {Max :_ae .Point {X :_dagf .Width ,Y :_dagf .Height }};
};func (_aaae *Monochrome )Histogram ()(_bac [256]int ){for _ ,_gaga :=range _aaae .Data {_bac [0xff]+=int (_daae [_aaae .Data [_gaga ]]);};return _bac ;};func (_gaff *ImageBase )HasAlpha ()bool {if _gaff .Alpha ==nil {return false ;};for _cbcgc :=range _gaff .Alpha {if _gaff .Alpha [_cbcgc ]!=0xff{return true ;
};};return false ;};var _daae [256]uint8 ;func (_eagf *ImageBase )setByte (_efe int ,_eggd byte )error {if _efe > len (_eagf .Data )-1{return _f .New ("\u0069n\u0064e\u0078\u0020\u006f\u0075\u0074 \u006f\u0066 \u0072\u0061\u006e\u0067\u0065");};_eagf .Data [_efe ]=_eggd ;
return nil ;};func MonochromeThresholdConverter (threshold uint8 )ColorConverter {return &monochromeThresholdConverter {Threshold :threshold };};func (_ecdb *NRGBA64 )Validate ()error {if len (_ecdb .Data )!=3*2*_ecdb .Width *_ecdb .Height {return _f .New ("i\u006e\u0076\u0061\u006c\u0069\u0064\u0020\u0069\u006da\u0067\u0065\u0020\u0064\u0061\u0074\u0061 s\u0069\u007a\u0065\u0020f\u006f\u0072\u0020\u0070\u0072\u006f\u0076\u0069\u0064ed\u0020\u0064i\u006d\u0065\u006e\u0073\u0069\u006f\u006e\u0073");
};return nil ;};func _badb (_fdf _a .CMYK )_a .RGBA {_egba ,_cg ,_adea :=_a .CMYKToRGB (_fdf .C ,_fdf .M ,_fdf .Y ,_fdf .K );return _a .RGBA {R :_egba ,G :_cg ,B :_adea ,A :0xff};};func ScaleAlphaToMonochrome (data []byte ,width ,height int )([]byte ,error ){_e :=BytesPerLine (width ,8,1);
if len (data )< _e *height {return nil ,nil ;};_ff :=&Gray8 {NewImageBase (width ,height ,8,1,data ,nil ,nil )};_ce ,_fc :=MonochromeConverter .Convert (_ff );if _fc !=nil {return nil ,_fc ;};return _ce .Base ().Data ,nil ;};func _dggf (_cgb _ae .Image )(Image ,error ){if _ebbef ,_cfdd :=_cgb .(*NRGBA32 );
_cfdd {return _ebbef .Copy (),nil ;};_bdbb ,_daaa ,_gafb :=_fdfg (_cgb ,1);_ggdd ,_abfd :=NewImage (_bdbb .Max .X ,_bdbb .Max .Y ,8,3,nil ,_gafb ,nil );if _abfd !=nil {return nil ,_abfd ;};_ccea (_cgb ,_ggdd ,_bdbb );if len (_gafb )!=0&&!_daaa {if _eagfa :=_agfg (_gafb ,_ggdd );
_eagfa !=nil {return nil ,_eagfa ;};};return _ggdd ,nil ;};func (_eccf *Gray16 )ColorModel ()_a .Model {return _a .Gray16Model };func _gggg (_cdcf _ae .Image ,_eaab uint8 )*_ae .Gray {_gddd :=_cdcf .Bounds ();_cgcad :=_ae .NewGray (_gddd );var (_gccc _a .Color ;
_eadg _a .Gray ;);for _bfggb :=0;_bfggb < _gddd .Max .X ;_bfggb ++{for _defe :=0;_defe < _gddd .Max .Y ;_defe ++{_gccc =_cdcf .At (_bfggb ,_defe );_cgcad .Set (_bfggb ,_defe ,_gccc );_eadg =_cgcad .GrayAt (_bfggb ,_defe );_cgcad .SetGray (_bfggb ,_defe ,_a .Gray {Y :_ggff (_eadg .Y ,_eaab )});
};};return _cgcad ;};func (_gea *Monochrome )ExpandBinary (factor int )(*Monochrome ,error ){if !IsPowerOf2 (uint (factor )){return nil ,_cb .Errorf ("\u0069\u006e\u0076\u0061\u006c\u0069\u0064\u0020\u0065\u0078\u0070\u0061\u006e\u0064\u0020b\u0069n\u0061\u0072\u0079\u0020\u0066\u0061\u0063\u0074\u006f\u0072\u003a\u0020\u0025\u0064",factor );
};return _fa (_gea ,factor );};func (_facg *Gray4 )ColorModel ()_a .Model {return Gray4Model };func (_gbdg *Monochrome )ReduceBinary (factor float64 )(*Monochrome ,error ){_ffe :=_fffca (uint (factor ));if !IsPowerOf2 (uint (factor )){_ffe ++;};_fege :=make ([]int ,_ffe );
for _aebc :=range _fege {_fege [_aebc ]=4;};_ggcf ,_dfgg :=_dac (_gbdg ,_fege ...);if _dfgg !=nil {return nil ,_dfgg ;};return _ggcf ,nil ;};var _ _ae .Image =&Gray8 {};func _egaf (_fage RGBA ,_fdac NRGBA ,_acfd _ae .Rectangle ){for _cged :=0;_cged < _acfd .Max .X ;
_cged ++{for _fgde :=0;_fgde < _acfd .Max .Y ;_fgde ++{_cdfga :=_fage .RGBAAt (_cged ,_fgde );_fdac .SetNRGBA (_cged ,_fgde ,_ecf (_cdfga ));};};};type Monochrome struct{ImageBase ;ModelThreshold uint8 ;};var _ Gray =&Gray4 {};func _eae (_cd ,_cc *Monochrome )(_ad error ){_deb :=_cc .BytesPerLine ;
_faa :=_cd .BytesPerLine ;_fg :=_cc .BytesPerLine *4-_cd .BytesPerLine ;var (_cbd ,_cba byte ;_dd uint32 ;_edc ,_gd ,_eb ,_df ,_gce ,_gae ,_bc int ;);for _eb =0;_eb < _cc .Height ;_eb ++{_edc =_eb *_deb ;_gd =4*_eb *_faa ;for _df =0;_df < _deb ;_df ++{_cbd =_cc .Data [_edc +_df ];
_dd =_ca [_cbd ];_gae =_gd +_df *4;if _fg !=0&&(_df +1)*4> _cd .BytesPerLine {for _gce =_fg ;_gce > 0;_gce --{_cba =byte ((_dd >>uint (_gce *8))&0xff);_bc =_gae +(_fg -_gce );if _ad =_cd .setByte (_bc ,_cba );_ad !=nil {return _ad ;};};}else if _ad =_cd .setFourBytes (_gae ,_dd );
_ad !=nil {return _ad ;};if _ad =_cd .setFourBytes (_gd +_df *4,_ca [_cc .Data [_edc +_df ]]);_ad !=nil {return _ad ;};};for _gce =1;_gce < 4;_gce ++{for _df =0;_df < _faa ;_df ++{if _ad =_cd .setByte (_gd +_gce *_faa +_df ,_cd .Data [_gd +_df ]);_ad !=nil {return _ad ;
};};};};return nil ;};func ColorAtCMYK (x ,y ,width int ,data []byte ,decode []float64 )(_a .CMYK ,error ){_bca :=4*(y *width +x );if _bca +3>=len (data ){return _a .CMYK {},_cb .Errorf ("\u0069\u006d\u0061\u0067\u0065\u0020\u0063\u006f\u006f\u0072\u0064\u0069\u006ea\u0074\u0065\u0073\u0020\u006f\u0075t\u0020\u006f\u0066\u0020\u0072\u0061\u006e\u0067\u0065\u0020\u0028\u0025\u0064,\u0020\u0025\u0064\u0029",x ,y );
};C :=data [_bca ]&0xff;M :=data [_bca +1]&0xff;Y :=data [_bca +2]&0xff;K :=data [_bca +3]&0xff;if len (decode )==8{C =uint8 (uint32 (LinearInterpolate (float64 (C ),0,255,decode [0],decode [1]))&0xff);M =uint8 (uint32 (LinearInterpolate (float64 (M ),0,255,decode [2],decode [3]))&0xff);
Y =uint8 (uint32 (LinearInterpolate (float64 (Y ),0,255,decode [4],decode [5]))&0xff);K =uint8 (uint32 (LinearInterpolate (float64 (K ),0,255,decode [6],decode [7]))&0xff);};return _a .CMYK {C :C ,M :M ,Y :Y ,K :K },nil ;};func (_bgcf *NRGBA16 )Validate ()error {if len (_bgcf .Data )!=3*_bgcf .Width *_bgcf .Height /2{return _f .New ("i\u006e\u0076\u0061\u006c\u0069\u0064\u0020\u0069\u006da\u0067\u0065\u0020\u0064\u0061\u0074\u0061 s\u0069\u007a\u0065\u0020f\u006f\u0072\u0020\u0070\u0072\u006f\u0076\u0069\u0064ed\u0020\u0064i\u006d\u0065\u006e\u0073\u0069\u006f\u006e\u0073");
};return nil ;};func GetConverter (bitsPerComponent ,colorComponents int )(ColorConverter ,error ){switch colorComponents {case 1:switch bitsPerComponent {case 1:return MonochromeConverter ,nil ;case 2:return Gray2Converter ,nil ;case 4:return Gray4Converter ,nil ;
case 8:return GrayConverter ,nil ;case 16:return Gray16Converter ,nil ;};case 3:switch bitsPerComponent {case 4:return NRGBA16Converter ,nil ;case 8:return NRGBAConverter ,nil ;case 16:return NRGBA64Converter ,nil ;};case 4:return CMYKConverter ,nil ;};
return nil ,_cb .Errorf ("\u0070\u0072\u006f\u0076\u0069\u0064\u0065\u0064\u0020\u0069\u006e\u0076\u0061l\u0069\u0064\u0020\u0063\u006f\u006c\u006f\u0072\u0043o\u006e\u0076\u0065\u0072\u0074\u0065\u0072\u0020\u0070\u0061\u0072\u0061\u006d\u0065t\u0065\u0072\u0073\u002e\u0020\u0042\u0069\u0074\u0073\u0050\u0065\u0072\u0043\u006f\u006d\u0070\u006f\u006e\u0065\u006e\u0074\u003a\u0020\u0025\u0064\u002c\u0020\u0043\u006f\u006co\u0072\u0043\u006f\u006d\u0070\u006f\u006e\u0065\u006et\u0073\u003a \u0025\u0064",bitsPerComponent ,colorComponents );
};func (_cgcb *Gray8 )GrayAt (x ,y int )_a .Gray {_dbee ,_ :=ColorAtGray8BPC (x ,y ,_cgcb .BytesPerLine ,_cgcb .Data ,_cgcb .Decode );return _dbee ;};func (_eagc *ImageBase )newAlpha (){_cca :=BytesPerLine (_eagc .Width ,_eagc .BitsPerComponent ,1);_eagc .Alpha =make ([]byte ,_eagc .Height *_cca );
};func (_ecd *NRGBA16 )NRGBAAt (x ,y int )_a .NRGBA {_gacg ,_ :=ColorAtNRGBA16 (x ,y ,_ecd .Width ,_ecd .BytesPerLine ,_ecd .Data ,_ecd .Alpha ,_ecd .Decode );return _gacg ;};func LinearInterpolate (x ,xmin ,xmax ,ymin ,ymax float64 )float64 {if _d .Abs (xmax -xmin )< 0.000001{return ymin ;
};_gcbc :=ymin +(x -xmin )*(ymax -ymin )/(xmax -xmin );return _gcbc ;};func _gcgc (_dedb CMYK ,_dbfad Gray ,_beae _ae .Rectangle ){for _dcgf :=0;_dcgf < _beae .Max .X ;_dcgf ++{for _fefb :=0;_fefb < _beae .Max .Y ;_fefb ++{_eccfd :=_adc (_dedb .CMYKAt (_dcgf ,_fefb ));
_dbfad .SetGray (_dcgf ,_fefb ,_eccfd );};};};func _dgcfb (_cggg *_ae .NYCbCrA ,_cbed NRGBA ,_gafdg _ae .Rectangle ){for _edge :=0;_edge < _gafdg .Max .X ;_edge ++{for _dgdab :=0;_dgdab < _gafdg .Max .Y ;_dgdab ++{_cfgbe :=_cggg .NYCbCrAAt (_edge ,_dgdab );
_cbed .SetNRGBA (_edge ,_dgdab ,_bge (_cfgbe ));};};};type RGBA32 struct{ImageBase };func _befc (_ddggg _ae .Image ,_gbgc Image ,_bbbg _ae .Rectangle ){if _gcbcd ,_ebff :=_ddggg .(SMasker );_ebff &&_gcbcd .HasAlpha (){_gbgc .(SMasker ).MakeAlpha ();};_gfgg (_ddggg ,_gbgc ,_bbbg );
};func (_cgfa *NRGBA32 )Set (x ,y int ,c _a .Color ){_bcc :=y *_cgfa .Width +x ;_ebda :=3*_bcc ;if _ebda +2>=len (_cgfa .Data ){return ;};_fcbe :=_a .NRGBAModel .Convert (c ).(_a .NRGBA );_cgfa .setRGBA (_bcc ,_fcbe );};func ColorAtNRGBA (x ,y ,width ,bytesPerLine ,bitsPerColor int ,data ,alpha []byte ,decode []float64 )(_a .Color ,error ){switch bitsPerColor {case 4:return ColorAtNRGBA16 (x ,y ,width ,bytesPerLine ,data ,alpha ,decode );
case 8:return ColorAtNRGBA32 (x ,y ,width ,data ,alpha ,decode );case 16:return ColorAtNRGBA64 (x ,y ,width ,data ,alpha ,decode );default:return nil ,_cb .Errorf ("\u0075\u006e\u0073\u0075\u0070\u0070\u006fr\u0074\u0065\u0064 \u0072\u0067\u0062\u0020b\u0069\u0074\u0073\u0020\u0070\u0065\u0072\u0020\u0063\u006f\u006c\u006f\u0072\u0020\u0061\u006d\u006f\u0075\u006e\u0074\u003a\u0020\u0027\u0025\u0064\u0027",bitsPerColor );
};};type ImageBase struct{Width ,Height int ;BitsPerComponent ,ColorComponents int ;Data ,Alpha []byte ;Decode []float64 ;BytesPerLine int ;};func ColorAtGray16BPC (x ,y ,bytesPerLine int ,data []byte ,decode []float64 )(_a .Gray16 ,error ){_eafb :=(y *bytesPerLine /2+x )*2;
if _eafb +1>=len (data ){return _a .Gray16 {},_cb .Errorf ("\u0069\u006d\u0061\u0067\u0065\u0020\u0063\u006f\u006f\u0072\u0064\u0069\u006ea\u0074\u0065\u0073\u0020\u006f\u0075t\u0020\u006f\u0066\u0020\u0072\u0061\u006e\u0067\u0065\u0020\u0028\u0025\u0064,\u0020\u0025\u0064\u0029",x ,y );
};_gcdf :=uint16 (data [_eafb ])<<8|uint16 (data [_eafb +1]);if len (decode )==2{_gcdf =uint16 (uint64 (LinearInterpolate (float64 (_gcdf ),0,65535,decode [0],decode [1])));};return _a .Gray16 {Y :_gcdf },nil ;};func (_bcf *CMYK32 )Validate ()error {if len (_bcf .Data )!=4*_bcf .Width *_bcf .Height {return _f .New ("i\u006e\u0076\u0061\u006c\u0069\u0064\u0020\u0069\u006da\u0067\u0065\u0020\u0064\u0061\u0074\u0061 s\u0069\u007a\u0065\u0020f\u006f\u0072\u0020\u0070\u0072\u006f\u0076\u0069\u0064ed\u0020\u0064i\u006d\u0065\u006e\u0073\u0069\u006f\u006e\u0073");
};return nil ;};func _caed (_egbb NRGBA ,_feff RGBA ,_bgee _ae .Rectangle ){for _cfgbea :=0;_cfgbea < _bgee .Max .X ;_cfgbea ++{for _badca :=0;_badca < _bgee .Max .Y ;_badca ++{_aegb :=_egbb .NRGBAAt (_cfgbea ,_badca );_feff .SetRGBA (_cfgbea ,_badca ,_dae (_aegb ));
};};};func _gabac (_ebea CMYK ,_bafc RGBA ,_dbcg _ae .Rectangle ){for _bbafa :=0;_bbafa < _dbcg .Max .X ;_bbafa ++{for _fdfbc :=0;_fdfbc < _dbcg .Max .Y ;_fdfbc ++{_cdgcb :=_ebea .CMYKAt (_bbafa ,_fdfbc );_bafc .SetRGBA (_bbafa ,_fdfbc ,_badb (_cdgcb ));
};};};func (_cdg *CMYK32 )At (x ,y int )_a .Color {_accg ,_ :=_cdg .ColorAt (x ,y );return _accg };func (_bdbgg *RGBA32 )SetRGBA (x ,y int ,c _a .RGBA ){_dfceb :=y *_bdbgg .Width +x ;_bcga :=3*_dfceb ;if _bcga +2>=len (_bdbgg .Data ){return ;};_bdbgg .setRGBA (_dfceb ,c );
};func _dfge (_dcbeb _a .Gray )_a .Gray {_dbfa :=_dcbeb .Y >>6;_dbfa |=_dbfa <<2;_dcbeb .Y =_dbfa |_dbfa <<4;return _dcbeb ;};func (_geeb *Monochrome )AddPadding ()(_bgfe error ){if _egc :=((_geeb .Width *_geeb .Height )+7)>>3;len (_geeb .Data )< _egc {return _cb .Errorf ("\u0069\u006e\u0076\u0061\u006c\u0069\u0064\u0020\u0064a\u0074\u0061\u0020\u006c\u0065\u006e\u0067\u0074\u0068\u003a\u0020\u0027\u0025\u0064\u0027\u002e\u0020\u0054\u0068\u0065\u0020\u0064\u0061t\u0061\u0020s\u0068\u006fu\u006c\u0064\u0020\u0063\u006f\u006e\u0074\u0061\u0069\u006e\u0020\u0061\u0074 l\u0065\u0061\u0073\u0074\u003a\u0020\u0027\u0025\u0064'\u0020\u0062\u0079\u0074\u0065\u0073",len (_geeb .Data ),_egc );
};_eaaa :=_geeb .Width %8;if _eaaa ==0{return nil ;};_afag :=_geeb .Width /8;_gbdf :=_cbe .NewReader (_geeb .Data );_ddb :=make ([]byte ,_geeb .Height *_geeb .BytesPerLine );_cag :=_cbe .NewWriterMSB (_ddb );_accd :=make ([]byte ,_afag );var (_acab int ;
_becd uint64 ;);for _acab =0;_acab < _geeb .Height ;_acab ++{if _ ,_bgfe =_gbdf .Read (_accd );_bgfe !=nil {return _bgfe ;};if _ ,_bgfe =_cag .Write (_accd );_bgfe !=nil {return _bgfe ;};if _becd ,_bgfe =_gbdf .ReadBits (byte (_eaaa ));_bgfe !=nil {return _bgfe ;
};if _bgfe =_cag .WriteByte (byte (_becd )<<uint (8-_eaaa ));_bgfe !=nil {return _bgfe ;};};_geeb .Data =_cag .Data ();return nil ;};func (_dde *Gray2 )ColorModel ()_a .Model {return Gray2Model };func ColorAt (x ,y ,width ,bitsPerColor ,colorComponents ,bytesPerLine int ,data ,alpha []byte ,decode []float64 )(_a .Color ,error ){switch colorComponents {case 1:return ColorAtGrayscale (x ,y ,bitsPerColor ,bytesPerLine ,data ,decode );
case 3:return ColorAtNRGBA (x ,y ,width ,bytesPerLine ,bitsPerColor ,data ,alpha ,decode );case 4:return ColorAtCMYK (x ,y ,width ,data ,decode );default:return nil ,_cb .Errorf ("\u0070\u0072\u006f\u0076\u0069\u0064\u0065\u0064\u0020\u0069\u006e\u0076\u0061\u006c\u0069\u0064\u0020\u0063o\u006c\u006f\u0072\u0020\u0063\u006f\u006dp\u006f\u006e\u0065\u006e\u0074\u0020\u0066\u006f\u0072\u0020\u0074h\u0065\u0020\u0069\u006d\u0061\u0067\u0065\u003a\u0020\u0025\u0064",colorComponents );
};};func _ecfg (_fegc ,_dcdbc Gray ,_cge _ae .Rectangle ){for _ffae :=0;_ffae < _cge .Max .X ;_ffae ++{for _dagg :=0;_dagg < _cge .Max .Y ;_dagg ++{_dcdbc .SetGray (_ffae ,_dagg ,_fegc .GrayAt (_ffae ,_dagg ));};};};func (_ace *Gray8 )SetGray (x ,y int ,g _a .Gray ){_bfda :=y *_ace .BytesPerLine +x ;
if _bfda > len (_ace .Data )-1{return ;};_ace .Data [_bfda ]=g .Y ;};func (_fbe *Gray4 )At (x ,y int )_a .Color {_fbaf ,_ :=_fbe .ColorAt (x ,y );return _fbaf };func (_cddf *Gray16 )SetGray (x ,y int ,g _a .Gray ){_ecgb :=(y *_cddf .BytesPerLine /2+x )*2;
if _ecgb +1>=len (_cddf .Data ){return ;};_cddf .Data [_ecgb ]=g .Y ;_cddf .Data [_ecgb +1]=g .Y ;};func _eec (_bcg ,_acc *Monochrome ,_dfd []byte ,_ada int )(_gcc error ){var (_efab ,_gcca ,_bbcb ,_aae ,_gaa ,_facb ,_gcd ,_dda int ;_ebfe ,_adae uint32 ;
_aed ,_bad byte ;_caa uint16 ;);_dea :=make ([]byte ,4);_gfgf :=make ([]byte ,4);for _bbcb =0;_bbcb < _bcg .Height -1;_bbcb ,_aae =_bbcb +2,_aae +1{_efab =_bbcb *_bcg .BytesPerLine ;_gcca =_aae *_acc .BytesPerLine ;for _gaa ,_facb =0,0;_gaa < _ada ;_gaa ,_facb =_gaa +4,_facb +1{for _gcd =0;
_gcd < 4;_gcd ++{_dda =_efab +_gaa +_gcd ;if _dda <=len (_bcg .Data )-1&&_dda < _efab +_bcg .BytesPerLine {_dea [_gcd ]=_bcg .Data [_dda ];}else {_dea [_gcd ]=0x00;};_dda =_efab +_bcg .BytesPerLine +_gaa +_gcd ;if _dda <=len (_bcg .Data )-1&&_dda < _efab +(2*_bcg .BytesPerLine ){_gfgf [_gcd ]=_bcg .Data [_dda ];
}else {_gfgf [_gcd ]=0x00;};};_ebfe =_c .BigEndian .Uint32 (_dea );_adae =_c .BigEndian .Uint32 (_gfgf );_adae &=_ebfe ;_adae &=_adae <<1;_adae &=0xaaaaaaaa;_ebfe =_adae |(_adae <<7);_aed =byte (_ebfe >>24);_bad =byte ((_ebfe >>8)&0xff);_dda =_gcca +_facb ;
if _dda +1==len (_acc .Data )-1||_dda +1>=_gcca +_acc .BytesPerLine {_acc .Data [_dda ]=_dfd [_aed ];if _gcc =_acc .setByte (_dda ,_dfd [_aed ]);_gcc !=nil {return _cb .Errorf ("\u0069n\u0064\u0065\u0078\u003a\u0020\u0025d",_dda );};}else {_caa =(uint16 (_dfd [_aed ])<<8)|uint16 (_dfd [_bad ]);
if _gcc =_acc .setTwoBytes (_dda ,_caa );_gcc !=nil {return _cb .Errorf ("s\u0065\u0074\u0074\u0069\u006e\u0067 \u0074\u0077\u006f\u0020\u0062\u0079t\u0065\u0073\u0020\u0066\u0061\u0069\u006ce\u0064\u002c\u0020\u0069\u006e\u0064\u0065\u0078\u003a\u0020%\u0064",_dda );
};_facb ++;};};};return nil ;};func _gccba (_badc _ae .Image )(Image ,error ){if _bdcb ,_fbbe :=_badc .(*NRGBA16 );_fbbe {return _bdcb .Copy (),nil ;};_gcbb :=_badc .Bounds ();_fedcf ,_dddd :=NewImage (_gcbb .Max .X ,_gcbb .Max .Y ,4,3,nil ,nil ,nil );
if _dddd !=nil {return nil ,_dddd ;};_ccea (_badc ,_fedcf ,_gcbb );return _fedcf ,nil ;};func (_bea *Gray2 )ColorAt (x ,y int )(_a .Color ,error ){return ColorAtGray2BPC (x ,y ,_bea .BytesPerLine ,_bea .Data ,_bea .Decode );};func (_cgdg *NRGBA16 )At (x ,y int )_a .Color {_abdc ,_ :=_cgdg .ColorAt (x ,y );
return _abdc };func _bae (_cfbc _a .NRGBA64 )_a .RGBA {_dcg ,_cabe ,_fcce ,_abcb :=_cfbc .RGBA ();return _a .RGBA {R :uint8 (_dcg >>8),G :uint8 (_cabe >>8),B :uint8 (_fcce >>8),A :uint8 (_abcb >>8)};};func (_age *Monochrome )Base ()*ImageBase {return &_age .ImageBase };
func (_dedc *ImageBase )getByte (_bagb int )(byte ,error ){if _bagb > len (_dedc .Data )-1||_bagb < 0{return 0,_cb .Errorf ("\u0069\u006e\u0064\u0065x:\u0020\u0025\u0064\u0020\u006f\u0075\u0074\u0020\u006f\u0066\u0020\u0072\u0061\u006eg\u0065",_bagb );
};return _dedc .Data [_bagb ],nil ;};var _ _ae .Image =&RGBA32 {};func _gbee (_ffcgc *Monochrome ,_aaceg ,_gdgc int ,_dcda ,_beaef int ,_gcgda RasterOperator ){var (_effg bool ;_aeac bool ;_bacg int ;_cgdf int ;_gbae int ;_fdbc int ;_gefb bool ;_fec byte ;
);_cfeb :=8-(_aaceg &7);_ccda :=_aage [_cfeb ];_cfbf :=_ffcgc .BytesPerLine *_gdgc +(_aaceg >>3);if _dcda < _cfeb {_effg =true ;_ccda &=_aabb [8-_cfeb +_dcda ];};if !_effg {_bacg =(_dcda -_cfeb )>>3;if _bacg !=0{_aeac =true ;_cgdf =_cfbf +1;};};_gbae =(_aaceg +_dcda )&7;
if !(_effg ||_gbae ==0){_gefb =true ;_fec =_aabb [_gbae ];_fdbc =_cfbf +1+_bacg ;};var _cffc ,_ggfb int ;switch _gcgda {case PixClr :for _cffc =0;_cffc < _beaef ;_cffc ++{_ffcgc .Data [_cfbf ]=_cecg (_ffcgc .Data [_cfbf ],0x0,_ccda );_cfbf +=_ffcgc .BytesPerLine ;
};if _aeac {for _cffc =0;_cffc < _beaef ;_cffc ++{for _ggfb =0;_ggfb < _bacg ;_ggfb ++{_ffcgc .Data [_cgdf +_ggfb ]=0x0;};_cgdf +=_ffcgc .BytesPerLine ;};};if _gefb {for _cffc =0;_cffc < _beaef ;_cffc ++{_ffcgc .Data [_fdbc ]=_cecg (_ffcgc .Data [_fdbc ],0x0,_fec );
_fdbc +=_ffcgc .BytesPerLine ;};};case PixSet :for _cffc =0;_cffc < _beaef ;_cffc ++{_ffcgc .Data [_cfbf ]=_cecg (_ffcgc .Data [_cfbf ],0xff,_ccda );_cfbf +=_ffcgc .BytesPerLine ;};if _aeac {for _cffc =0;_cffc < _beaef ;_cffc ++{for _ggfb =0;_ggfb < _bacg ;
_ggfb ++{_ffcgc .Data [_cgdf +_ggfb ]=0xff;};_cgdf +=_ffcgc .BytesPerLine ;};};if _gefb {for _cffc =0;_cffc < _beaef ;_cffc ++{_ffcgc .Data [_fdbc ]=_cecg (_ffcgc .Data [_fdbc ],0xff,_fec );_fdbc +=_ffcgc .BytesPerLine ;};};case PixNotDst :for _cffc =0;
_cffc < _beaef ;_cffc ++{_ffcgc .Data [_cfbf ]=_cecg (_ffcgc .Data [_cfbf ],^_ffcgc .Data [_cfbf ],_ccda );_cfbf +=_ffcgc .BytesPerLine ;};if _aeac {for _cffc =0;_cffc < _beaef ;_cffc ++{for _ggfb =0;_ggfb < _bacg ;_ggfb ++{_ffcgc .Data [_cgdf +_ggfb ]=^(_ffcgc .Data [_cgdf +_ggfb ]);
};_cgdf +=_ffcgc .BytesPerLine ;};};if _gefb {for _cffc =0;_cffc < _beaef ;_cffc ++{_ffcgc .Data [_fdbc ]=_cecg (_ffcgc .Data [_fdbc ],^_ffcgc .Data [_fdbc ],_fec );_fdbc +=_ffcgc .BytesPerLine ;};};};};func (_ebebd *RGBA32 )setRGBA (_gfdb int ,_gccbd _a .RGBA ){_ecdf :=3*_gfdb ;
_ebebd .Data [_ecdf ]=_gccbd .R ;_ebebd .Data [_ecdf +1]=_gccbd .G ;_ebebd .Data [_ecdf +2]=_gccbd .B ;if _gfdb < len (_ebebd .Alpha ){_ebebd .Alpha [_gfdb ]=_gccbd .A ;};};func (_ddca *NRGBA64 )ColorModel ()_a .Model {return _a .NRGBA64Model };var ErrInvalidImage =_f .New ("i\u006e\u0076\u0061\u006c\u0069\u0064\u0020\u0069\u006da\u0067\u0065\u0020\u0064\u0061\u0074\u0061 s\u0069\u007a\u0065\u0020f\u006f\u0072\u0020\u0070\u0072\u006f\u0076\u0069\u0064ed\u0020\u0064i\u006d\u0065\u006e\u0073\u0069\u006f\u006e\u0073");
func (_dcbee *Gray8 )At (x ,y int )_a .Color {_cbcg ,_ :=_dcbee .ColorAt (x ,y );return _cbcg };func (_egac *Gray8 )Bounds ()_ae .Rectangle {return _ae .Rectangle {Max :_ae .Point {X :_egac .Width ,Y :_egac .Height }};};const (PixSrc RasterOperator =0xc;
PixDst RasterOperator =0xa;PixNotSrc RasterOperator =0x3;PixNotDst RasterOperator =0x5;PixClr RasterOperator =0x0;PixSet RasterOperator =0xf;PixSrcOrDst RasterOperator =0xe;PixSrcAndDst RasterOperator =0x8;PixSrcXorDst RasterOperator =0x6;PixNotSrcOrDst RasterOperator =0xb;
PixNotSrcAndDst RasterOperator =0x2;PixSrcOrNotDst RasterOperator =0xd;PixSrcAndNotDst RasterOperator =0x4;PixNotPixSrcOrDst RasterOperator =0x1;PixNotPixSrcAndDst RasterOperator =0x7;PixNotPixSrcXorDst RasterOperator =0x9;PixPaint =PixSrcOrDst ;PixSubtract =PixNotSrcAndDst ;
PixMask =PixSrcAndDst ;);func _fffca (_aece uint )uint {var _ecfa uint ;for _aece !=0{_aece >>=1;_ecfa ++;};return _ecfa -1;};func (_febf *Monochrome )Copy ()Image {return &Monochrome {ImageBase :_febf .ImageBase .copy (),ModelThreshold :_febf .ModelThreshold };
};func (_bdfb *RGBA32 )RGBAAt (x ,y int )_a .RGBA {_aacee ,_ :=ColorAtRGBA32 (x ,y ,_bdfb .Width ,_bdfb .Data ,_bdfb .Alpha ,_bdfb .Decode );return _aacee ;};var _ Image =&Monochrome {};func _agce (_eaf Gray ,_bgddb NRGBA ,_agg _ae .Rectangle ){for _cbec :=0;
_cbec < _agg .Max .X ;_cbec ++{for _cff :=0;_cff < _agg .Max .Y ;_cff ++{_gaba :=_ggc (_bgddb .NRGBAAt (_cbec ,_cff ));_eaf .SetGray (_cbec ,_cff ,_gaba );};};};func (_eag *Gray8 )Set (x ,y int ,c _a .Color ){_feef :=y *_eag .BytesPerLine +x ;if _feef > len (_eag .Data )-1{return ;
};_cffg :=_a .GrayModel .Convert (c );_eag .Data [_feef ]=_cffg .(_a .Gray ).Y ;};func ImgToGray (i _ae .Image )*_ae .Gray {if _gcag ,_egda :=i .(*_ae .Gray );_egda {return _gcag ;};_caecf :=i .Bounds ();_fdaga :=_ae .NewGray (_caecf );for _eeece :=0;_eeece < _caecf .Max .X ;
_eeece ++{for _bdfg :=0;_bdfg < _caecf .Max .Y ;_bdfg ++{_fadg :=i .At (_eeece ,_bdfg );_fdaga .Set (_eeece ,_bdfg ,_fadg );};};return _fdaga ;};var _ Gray =&Gray8 {};type Gray2 struct{ImageBase };func (_bgc *ImageBase )setEightPartlyBytes (_gbfg ,_aecdg int ,_ddfb uint64 )(_gfdf error ){var (_bbb byte ;
_fceb int ;);for _eafg :=1;_eafg <=_aecdg ;_eafg ++{_fceb =64-_eafg *8;_bbb =byte (_ddfb >>uint (_fceb )&0xff);if _gfdf =_bgc .setByte (_gbfg +_eafg -1,_bbb );_gfdf !=nil {return _gfdf ;};};_dcaa :=_bgc .BytesPerLine *8-_bgc .Width ;if _dcaa ==0{return nil ;
};_fceb -=8;_bbb =byte (_ddfb >>uint (_fceb )&0xff)<<uint (_dcaa );if _gfdf =_bgc .setByte (_gbfg +_aecdg ,_bbb );_gfdf !=nil {return _gfdf ;};return nil ;};func IsPowerOf2 (n uint )bool {return n > 0&&(n &(n -1))==0};func ImgToBinary (i _ae .Image ,threshold uint8 )*_ae .Gray {switch _eedd :=i .(type ){case *_ae .Gray :if _fffd (_eedd ){return _eedd ;
};return _aecde (_eedd ,threshold );case *_ae .Gray16 :return _fdaf (_eedd ,threshold );default:return _gggg (_eedd ,threshold );};};func (_fegeb *RGBA32 )Copy ()Image {return &RGBA32 {ImageBase :_fegeb .copy ()}};func (_agdf *RGBA32 )Base ()*ImageBase {return &_agdf .ImageBase };
func ColorAtGrayscale (x ,y ,bitsPerColor ,bytesPerLine int ,data []byte ,decode []float64 )(_a .Color ,error ){switch bitsPerColor {case 1:return ColorAtGray1BPC (x ,y ,bytesPerLine ,data ,decode );case 2:return ColorAtGray2BPC (x ,y ,bytesPerLine ,data ,decode );
case 4:return ColorAtGray4BPC (x ,y ,bytesPerLine ,data ,decode );case 8:return ColorAtGray8BPC (x ,y ,bytesPerLine ,data ,decode );case 16:return ColorAtGray16BPC (x ,y ,bytesPerLine ,data ,decode );default:return nil ,_cb .Errorf ("\u0075\u006e\u0073\u0075\u0070\u0070\u006f\u0072\u0074\u0065\u0064\u0020\u0067\u0072\u0061\u0079\u0020\u0073c\u0061\u006c\u0065\u0020\u0062\u0069\u0074s\u0020\u0070\u0065\u0072\u0020\u0063\u006f\u006c\u006f\u0072\u0020a\u006d\u006f\u0075\u006e\u0074\u003a\u0020\u0027\u0025\u0064\u0027",bitsPerColor );
};};func _bgbc ()(_ddac []byte ){_ddac =make ([]byte ,256);for _gagd :=0;_gagd < 256;_gagd ++{_badg :=byte (_gagd );_ddac [_badg ]=(_badg &0x01)|((_badg &0x04)>>1)|((_badg &0x10)>>2)|((_badg &0x40)>>3)|((_badg &0x02)<<3)|((_badg &0x08)<<2)|((_badg &0x20)<<1)|(_badg &0x80);
};return _ddac ;};func _fdaf (_dccad *_ae .Gray16 ,_eafa uint8 )*_ae .Gray {_gffge :=_dccad .Bounds ();_ggffb :=_ae .NewGray (_gffge );for _efae :=0;_efae < _gffge .Dx ();_efae ++{for _feec :=0;_feec < _gffge .Dy ();_feec ++{_gafbe :=_dccad .Gray16At (_efae ,_feec );
_ggffb .SetGray (_efae ,_feec ,_a .Gray {Y :_ggff (uint8 (_gafbe .Y /256),_eafa )});};};return _ggffb ;};func (_fcca *Gray2 )Histogram ()(_fcag [256]int ){for _eafc :=0;_eafc < _fcca .Width ;_eafc ++{for _abff :=0;_abff < _fcca .Height ;_abff ++{_fcag [_fcca .GrayAt (_eafc ,_abff ).Y ]++;
};};return _fcag ;};var (Gray2Model =_a .ModelFunc (_bfg );Gray4Model =_a .ModelFunc (_beee );NRGBA16Model =_a .ModelFunc (_fefcd ););func _fcc (_gee ,_abb CMYK ,_fdaa _ae .Rectangle ){for _bcdg :=0;_bcdg < _fdaa .Max .X ;_bcdg ++{for _daga :=0;_daga < _fdaa .Max .Y ;
_daga ++{_abb .SetCMYK (_bcdg ,_daga ,_gee .CMYKAt (_bcdg ,_daga ));};};};func _cagd (_effc *Monochrome ,_geb ,_deca ,_gdbf ,_fdfc int ,_dcffa RasterOperator ){if _geb < 0{_gdbf +=_geb ;_geb =0;};_afd :=_geb +_gdbf -_effc .Width ;if _afd > 0{_gdbf -=_afd ;
};if _deca < 0{_fdfc +=_deca ;_deca =0;};_efaa :=_deca +_fdfc -_effc .Height ;if _efaa > 0{_fdfc -=_efaa ;};if _gdbf <=0||_fdfc <=0{return ;};if (_geb &7)==0{_ecfc (_effc ,_geb ,_deca ,_gdbf ,_fdfc ,_dcffa );}else {_gbee (_effc ,_geb ,_deca ,_gdbf ,_fdfc ,_dcffa );
};};func (_agff *Gray4 )Copy ()Image {return &Gray4 {ImageBase :_agff .copy ()}};func _daed (_eefb _a .NRGBA )_a .NRGBA {_eefb .R =_eefb .R >>4|(_eefb .R >>4)<<4;_eefb .G =_eefb .G >>4|(_eefb .G >>4)<<4;_eefb .B =_eefb .B >>4|(_eefb .B >>4)<<4;return _eefb ;
};func (_cdcb *RGBA32 )Validate ()error {if len (_cdcb .Data )!=3*_cdcb .Width *_cdcb .Height {return _f .New ("i\u006e\u0076\u0061\u006c\u0069\u0064\u0020\u0069\u006da\u0067\u0065\u0020\u0064\u0061\u0074\u0061 s\u0069\u007a\u0065\u0020f\u006f\u0072\u0020\u0070\u0072\u006f\u0076\u0069\u0064ed\u0020\u0064i\u006d\u0065\u006e\u0073\u0069\u006f\u006e\u0073");
};return nil ;};type RasterOperator int ;func (_ddgc *ImageBase )copy ()ImageBase {_geag :=*_ddgc ;_geag .Data =make ([]byte ,len (_ddgc .Data ));copy (_geag .Data ,_ddgc .Data );return _geag ;};func _gbgd (_edba _ae .Image )(Image ,error ){if _eafce ,_bfbe :=_edba .(*Gray2 );
_bfbe {return _eafce .Copy (),nil ;};_aaeg :=_edba .Bounds ();_gfad ,_gge :=NewImage (_aaeg .Max .X ,_aaeg .Max .Y ,2,1,nil ,nil ,nil );if _gge !=nil {return nil ,_gge ;};_gcbf (_edba ,_gfad ,_aaeg );return _gfad ,nil ;};func _aga (_bdd *Monochrome ,_dbc int ,_gcec []byte )(_cfge *Monochrome ,_dge error ){const _gad ="\u0072\u0065d\u0075\u0063\u0065R\u0061\u006e\u006b\u0042\u0069\u006e\u0061\u0072\u0079";
if _bdd ==nil {return nil ,_f .New ("\u0073o\u0075\u0072\u0063\u0065 \u0062\u0069\u0074\u006d\u0061p\u0020n\u006ft\u0020\u0064\u0065\u0066\u0069\u006e\u0065d");};if _dbc < 1||_dbc > 4{return nil ,_f .New ("\u006c\u0065\u0076\u0065\u006c\u0020\u006d\u0075\u0073\u0074 \u0062\u0065\u0020\u0069\u006e\u0020\u0073e\u0074\u0020\u007b\u0031\u002c\u0032\u002c\u0033\u002c\u0034\u007d");
};if _bdd .Height <=1{return nil ,_f .New ("\u0073\u006f\u0075rc\u0065\u0020\u0068\u0065\u0069\u0067\u0068\u0074\u0020m\u0075s\u0074 \u0062e\u0020\u0061\u0074\u0020\u006c\u0065\u0061\u0073\u0074\u0020\u0027\u0032\u0027");};_cfge =_cfd (_bdd .Width /2,_bdd .Height /2);
if _gcec ==nil {_gcec =_bgbc ();};_aeag :=_acgg (_bdd .BytesPerLine ,2*_cfge .BytesPerLine );switch _dbc {case 1:_dge =_eed (_bdd ,_cfge ,_gcec ,_aeag );case 2:_dge =_fdgf (_bdd ,_cfge ,_gcec ,_aeag );case 3:_dge =_ccd (_bdd ,_cfge ,_gcec ,_aeag );case 4:_dge =_eec (_bdd ,_cfge ,_gcec ,_aeag );
};if _dge !=nil {return nil ,_dge ;};return _cfge ,nil ;};func (_gfbc *NRGBA16 )Bounds ()_ae .Rectangle {return _ae .Rectangle {Max :_ae .Point {X :_gfbc .Width ,Y :_gfbc .Height }};};func (_bgde *Monochrome )getBitAt (_bgbcb ,_beeb int )bool {_bab :=_beeb *_bgde .BytesPerLine +(_bgbcb >>3);
_eegd :=_bgbcb &0x07;_fefc :=uint (7-_eegd );if _bab > len (_bgde .Data )-1{return false ;};if (_bgde .Data [_bab ]>>_fefc )&0x01>=1{return true ;};return false ;};type NRGBA32 struct{ImageBase };func ColorAtRGBA32 (x ,y ,width int ,data ,alpha []byte ,decode []float64 )(_a .RGBA ,error ){_gdda :=y *width +x ;
_adbf :=3*_gdda ;if _adbf +2>=len (data ){return _a .RGBA {},_cb .Errorf ("\u0069\u006d\u0061\u0067\u0065\u0020\u0063\u006f\u006f\u0072\u0064\u0069\u006ea\u0074\u0065\u0073\u0020\u006f\u0075t\u0020\u006f\u0066\u0020\u0072\u0061\u006e\u0067\u0065\u0020\u0028\u0025\u0064,\u0020\u0025\u0064\u0029",x ,y );
};_deabe :=uint8 (0xff);if alpha !=nil &&len (alpha )> _gdda {_deabe =alpha [_gdda ];};_eggf ,_cgcc ,_edaac :=data [_adbf ],data [_adbf +1],data [_adbf +2];if len (decode )==6{_eggf =uint8 (uint32 (LinearInterpolate (float64 (_eggf ),0,255,decode [0],decode [1]))&0xff);
_cgcc =uint8 (uint32 (LinearInterpolate (float64 (_cgcc ),0,255,decode [2],decode [3]))&0xff);_edaac =uint8 (uint32 (LinearInterpolate (float64 (_edaac ),0,255,decode [4],decode [5]))&0xff);};return _a .RGBA {R :_eggf ,G :_cgcc ,B :_edaac ,A :_deabe },nil ;
};func (_cgg *Gray2 )Validate ()error {if len (_cgg .Data )!=_cgg .Height *_cgg .BytesPerLine {return ErrInvalidImage ;};return nil ;};var _ Image =&NRGBA16 {};func (_cdcad *NRGBA32 )Bounds ()_ae .Rectangle {return _ae .Rectangle {Max :_ae .Point {X :_cdcad .Width ,Y :_cdcad .Height }};
};func (_acgde *NRGBA32 )At (x ,y int )_a .Color {_gcgdf ,_ :=_acgde .ColorAt (x ,y );return _gcgdf };func (_cbab *ImageBase )setEightFullBytes (_cedf int ,_adeag uint64 )error {if _cedf +7> len (_cbab .Data )-1{return _f .New ("\u0069n\u0064e\u0078\u0020\u006f\u0075\u0074 \u006f\u0066 \u0072\u0061\u006e\u0067\u0065");
};_cbab .Data [_cedf ]=byte ((_adeag &0xff00000000000000)>>56);_cbab .Data [_cedf +1]=byte ((_adeag &0xff000000000000)>>48);_cbab .Data [_cedf +2]=byte ((_adeag &0xff0000000000)>>40);_cbab .Data [_cedf +3]=byte ((_adeag &0xff00000000)>>32);_cbab .Data [_cedf +4]=byte ((_adeag &0xff000000)>>24);
_cbab .Data [_cedf +5]=byte ((_adeag &0xff0000)>>16);_cbab .Data [_cedf +6]=byte ((_adeag &0xff00)>>8);_cbab .Data [_cedf +7]=byte (_adeag &0xff);return nil ;};func (_dged *Gray8 )Base ()*ImageBase {return &_dged .ImageBase };func (_gfgeg *NRGBA64 )At (x ,y int )_a .Color {_egbe ,_ :=_gfgeg .ColorAt (x ,y );
return _egbe };func (_gcgd *Gray2 )Bounds ()_ae .Rectangle {return _ae .Rectangle {Max :_ae .Point {X :_gcgd .Width ,Y :_gcgd .Height }};};func _gdca (_egaea _ae .Image )(Image ,error ){if _dgddc ,_begc :=_egaea .(*RGBA32 );_begc {return _dgddc .Copy (),nil ;
};_cbga ,_acfa ,_fcbbd :=_fdfg (_egaea ,1);_fbgfg :=&RGBA32 {ImageBase :NewImageBase (_cbga .Max .X ,_cbga .Max .Y ,8,3,nil ,_fcbbd ,nil )};_bada (_egaea ,_fbgfg ,_cbga );if len (_fcbbd )!=0&&!_acfa {if _cgca :=_agfg (_fcbbd ,_fbgfg );_cgca !=nil {return nil ,_cgca ;
};};return _fbgfg ,nil ;};func _cecg (_defc ,_bdfc ,_degd byte )byte {return (_defc &^(_degd ))|(_bdfc &_degd )};func (_egg *Gray4 )Base ()*ImageBase {return &_egg .ImageBase };func (_gfdd *ImageBase )Pix ()[]byte {return _gfdd .Data };func _dae (_aabf _a .NRGBA )_a .RGBA {_gfd ,_dcbe ,_bbga ,_acd :=_aabf .RGBA ();
return _a .RGBA {R :uint8 (_gfd >>8),G :uint8 (_dcbe >>8),B :uint8 (_bbga >>8),A :uint8 (_acd >>8)};};func _fffd (_dbca *_ae .Gray )bool {for _bcffb :=0;_bcffb < len (_dbca .Pix );_bcffb ++{if !_fgded (_dbca .Pix [_bcffb ]){return false ;};};return true ;
};func _acgd (_fdgd NRGBA ,_gcbg Gray ,_bfa _ae .Rectangle ){for _gac :=0;_gac < _bfa .Max .X ;_gac ++{for _efffd :=0;_efffd < _bfa .Max .Y ;_efffd ++{_fccff :=_dgec (_fdgd .NRGBAAt (_gac ,_efffd ));_gcbg .SetGray (_gac ,_efffd ,_fccff );};};};func _bde (_fafd _a .RGBA )_a .CMYK {_fdae ,_agac ,_cfca ,_bdee :=_a .RGBToCMYK (_fafd .R ,_fafd .G ,_fafd .B );
return _a .CMYK {C :_fdae ,M :_agac ,Y :_cfca ,K :_bdee };};func (_agdd *Monochrome )At (x ,y int )_a .Color {_cdeb ,_ :=_agdd .ColorAt (x ,y );return _cdeb };type CMYK32 struct{ImageBase };var (MonochromeConverter =ConverterFunc (_eef );Gray2Converter =ConverterFunc (_gbgd );
Gray4Converter =ConverterFunc (_baee );GrayConverter =ConverterFunc (_fgdb );Gray16Converter =ConverterFunc (_agcc );NRGBA16Converter =ConverterFunc (_gccba );NRGBAConverter =ConverterFunc (_dggf );NRGBA64Converter =ConverterFunc (_fceg );RGBAConverter =ConverterFunc (_gdca );
CMYKConverter =ConverterFunc (_bag ););func _fgcd (_bfbd *Monochrome ,_fadd ,_ebd ,_fgdbg ,_cded int ,_cbacb RasterOperator ,_efdea *Monochrome ,_fdcc ,_bcdgg int )error {var (_bgfec bool ;_feea bool ;_ddd byte ;_dgfd int ;_efbf int ;_gddb int ;_ccfd int ;
_aggc bool ;_ebbe int ;_fbga int ;_efdg int ;_aebg bool ;_egdc byte ;_fcga int ;_ecgd int ;_bdad int ;_ecge byte ;_dfbd int ;_ccggg int ;_fgff uint ;_gece uint ;_abg byte ;_gegda shift ;_dbda bool ;_ccfe bool ;_bfdbf ,_faece int ;);if _fdcc &7!=0{_ccggg =8-(_fdcc &7);
};if _fadd &7!=0{_efbf =8-(_fadd &7);};if _ccggg ==0&&_efbf ==0{_abg =_aage [0];}else {if _efbf > _ccggg {_fgff =uint (_efbf -_ccggg );}else {_fgff =uint (8-(_ccggg -_efbf ));};_gece =8-_fgff ;_abg =_aage [_fgff ];};if (_fadd &7)!=0{_bgfec =true ;_dgfd =8-(_fadd &7);
_ddd =_aage [_dgfd ];_gddb =_bfbd .BytesPerLine *_ebd +(_fadd >>3);_ccfd =_efdea .BytesPerLine *_bcdgg +(_fdcc >>3);_dfbd =8-(_fdcc &7);if _dgfd > _dfbd {_gegda =_gccb ;if _fgdbg >=_ccggg {_dbda =true ;};}else {_gegda =_aaff ;};};if _fgdbg < _dgfd {_feea =true ;
_ddd &=_aabb [8-_dgfd +_fgdbg ];};if !_feea {_ebbe =(_fgdbg -_dgfd )>>3;if _ebbe !=0{_aggc =true ;_fbga =_bfbd .BytesPerLine *_ebd +((_fadd +_efbf )>>3);_efdg =_efdea .BytesPerLine *_bcdgg +((_fdcc +_efbf )>>3);};};_fcga =(_fadd +_fgdbg )&7;if !(_feea ||_fcga ==0){_aebg =true ;
_egdc =_aabb [_fcga ];_ecgd =_bfbd .BytesPerLine *_ebd +((_fadd +_efbf )>>3)+_ebbe ;_bdad =_efdea .BytesPerLine *_bcdgg +((_fdcc +_efbf )>>3)+_ebbe ;if _fcga > int (_gece ){_ccfe =true ;};};switch _cbacb {case PixSrc :if _bgfec {for _bfdbf =0;_bfdbf < _cded ;
_bfdbf ++{if _gegda ==_gccb {_ecge =_efdea .Data [_ccfd ]<<_fgff ;if _dbda {_ecge =_cecg (_ecge ,_efdea .Data [_ccfd +1]>>_gece ,_abg );};}else {_ecge =_efdea .Data [_ccfd ]>>_gece ;};_bfbd .Data [_gddb ]=_cecg (_bfbd .Data [_gddb ],_ecge ,_ddd );_gddb +=_bfbd .BytesPerLine ;
_ccfd +=_efdea .BytesPerLine ;};};if _aggc {for _bfdbf =0;_bfdbf < _cded ;_bfdbf ++{for _faece =0;_faece < _ebbe ;_faece ++{_ecge =_cecg (_efdea .Data [_efdg +_faece ]<<_fgff ,_efdea .Data [_efdg +_faece +1]>>_gece ,_abg );_bfbd .Data [_fbga +_faece ]=_ecge ;
};_fbga +=_bfbd .BytesPerLine ;_efdg +=_efdea .BytesPerLine ;};};if _aebg {for _bfdbf =0;_bfdbf < _cded ;_bfdbf ++{_ecge =_efdea .Data [_bdad ]<<_fgff ;if _ccfe {_ecge =_cecg (_ecge ,_efdea .Data [_bdad +1]>>_gece ,_abg );};_bfbd .Data [_ecgd ]=_cecg (_bfbd .Data [_ecgd ],_ecge ,_egdc );
_ecgd +=_bfbd .BytesPerLine ;_bdad +=_efdea .BytesPerLine ;};};case PixNotSrc :if _bgfec {for _bfdbf =0;_bfdbf < _cded ;_bfdbf ++{if _gegda ==_gccb {_ecge =_efdea .Data [_ccfd ]<<_fgff ;if _dbda {_ecge =_cecg (_ecge ,_efdea .Data [_ccfd +1]>>_gece ,_abg );
};}else {_ecge =_efdea .Data [_ccfd ]>>_gece ;};_bfbd .Data [_gddb ]=_cecg (_bfbd .Data [_gddb ],^_ecge ,_ddd );_gddb +=_bfbd .BytesPerLine ;_ccfd +=_efdea .BytesPerLine ;};};if _aggc {for _bfdbf =0;_bfdbf < _cded ;_bfdbf ++{for _faece =0;_faece < _ebbe ;
_faece ++{_ecge =_cecg (_efdea .Data [_efdg +_faece ]<<_fgff ,_efdea .Data [_efdg +_faece +1]>>_gece ,_abg );_bfbd .Data [_fbga +_faece ]=^_ecge ;};_fbga +=_bfbd .BytesPerLine ;_efdg +=_efdea .BytesPerLine ;};};if _aebg {for _bfdbf =0;_bfdbf < _cded ;_bfdbf ++{_ecge =_efdea .Data [_bdad ]<<_fgff ;
if _ccfe {_ecge =_cecg (_ecge ,_efdea .Data [_bdad +1]>>_gece ,_abg );};_bfbd .Data [_ecgd ]=_cecg (_bfbd .Data [_ecgd ],^_ecge ,_egdc );_ecgd +=_bfbd .BytesPerLine ;_bdad +=_efdea .BytesPerLine ;};};case PixSrcOrDst :if _bgfec {for _bfdbf =0;_bfdbf < _cded ;
_bfdbf ++{if _gegda ==_gccb {_ecge =_efdea .Data [_ccfd ]<<_fgff ;if _dbda {_ecge =_cecg (_ecge ,_efdea .Data [_ccfd +1]>>_gece ,_abg );};}else {_ecge =_efdea .Data [_ccfd ]>>_gece ;};_bfbd .Data [_gddb ]=_cecg (_bfbd .Data [_gddb ],_ecge |_bfbd .Data [_gddb ],_ddd );
_gddb +=_bfbd .BytesPerLine ;_ccfd +=_efdea .BytesPerLine ;};};if _aggc {for _bfdbf =0;_bfdbf < _cded ;_bfdbf ++{for _faece =0;_faece < _ebbe ;_faece ++{_ecge =_cecg (_efdea .Data [_efdg +_faece ]<<_fgff ,_efdea .Data [_efdg +_faece +1]>>_gece ,_abg );
_bfbd .Data [_fbga +_faece ]|=_ecge ;};_fbga +=_bfbd .BytesPerLine ;_efdg +=_efdea .BytesPerLine ;};};if _aebg {for _bfdbf =0;_bfdbf < _cded ;_bfdbf ++{_ecge =_efdea .Data [_bdad ]<<_fgff ;if _ccfe {_ecge =_cecg (_ecge ,_efdea .Data [_bdad +1]>>_gece ,_abg );
};_bfbd .Data [_ecgd ]=_cecg (_bfbd .Data [_ecgd ],_ecge |_bfbd .Data [_ecgd ],_egdc );_ecgd +=_bfbd .BytesPerLine ;_bdad +=_efdea .BytesPerLine ;};};case PixSrcAndDst :if _bgfec {for _bfdbf =0;_bfdbf < _cded ;_bfdbf ++{if _gegda ==_gccb {_ecge =_efdea .Data [_ccfd ]<<_fgff ;
if _dbda {_ecge =_cecg (_ecge ,_efdea .Data [_ccfd +1]>>_gece ,_abg );};}else {_ecge =_efdea .Data [_ccfd ]>>_gece ;};_bfbd .Data [_gddb ]=_cecg (_bfbd .Data [_gddb ],_ecge &_bfbd .Data [_gddb ],_ddd );_gddb +=_bfbd .BytesPerLine ;_ccfd +=_efdea .BytesPerLine ;
};};if _aggc {for _bfdbf =0;_bfdbf < _cded ;_bfdbf ++{for _faece =0;_faece < _ebbe ;_faece ++{_ecge =_cecg (_efdea .Data [_efdg +_faece ]<<_fgff ,_efdea .Data [_efdg +_faece +1]>>_gece ,_abg );_bfbd .Data [_fbga +_faece ]&=_ecge ;};_fbga +=_bfbd .BytesPerLine ;
_efdg +=_efdea .BytesPerLine ;};};if _aebg {for _bfdbf =0;_bfdbf < _cded ;_bfdbf ++{_ecge =_efdea .Data [_bdad ]<<_fgff ;if _ccfe {_ecge =_cecg (_ecge ,_efdea .Data [_bdad +1]>>_gece ,_abg );};_bfbd .Data [_ecgd ]=_cecg (_bfbd .Data [_ecgd ],_ecge &_bfbd .Data [_ecgd ],_egdc );
_ecgd +=_bfbd .BytesPerLine ;_bdad +=_efdea .BytesPerLine ;};};case PixSrcXorDst :if _bgfec {for _bfdbf =0;_bfdbf < _cded ;_bfdbf ++{if _gegda ==_gccb {_ecge =_efdea .Data [_ccfd ]<<_fgff ;if _dbda {_ecge =_cecg (_ecge ,_efdea .Data [_ccfd +1]>>_gece ,_abg );
};}else {_ecge =_efdea .Data [_ccfd ]>>_gece ;};_bfbd .Data [_gddb ]=_cecg (_bfbd .Data [_gddb ],_ecge ^_bfbd .Data [_gddb ],_ddd );_gddb +=_bfbd .BytesPerLine ;_ccfd +=_efdea .BytesPerLine ;};};if _aggc {for _bfdbf =0;_bfdbf < _cded ;_bfdbf ++{for _faece =0;
_faece < _ebbe ;_faece ++{_ecge =_cecg (_efdea .Data [_efdg +_faece ]<<_fgff ,_efdea .Data [_efdg +_faece +1]>>_gece ,_abg );_bfbd .Data [_fbga +_faece ]^=_ecge ;};_fbga +=_bfbd .BytesPerLine ;_efdg +=_efdea .BytesPerLine ;};};if _aebg {for _bfdbf =0;_bfdbf < _cded ;
_bfdbf ++{_ecge =_efdea .Data [_bdad ]<<_fgff ;if _ccfe {_ecge =_cecg (_ecge ,_efdea .Data [_bdad +1]>>_gece ,_abg );};_bfbd .Data [_ecgd ]=_cecg (_bfbd .Data [_ecgd ],_ecge ^_bfbd .Data [_ecgd ],_egdc );_ecgd +=_bfbd .BytesPerLine ;_bdad +=_efdea .BytesPerLine ;
};};case PixNotSrcOrDst :if _bgfec {for _bfdbf =0;_bfdbf < _cded ;_bfdbf ++{if _gegda ==_gccb {_ecge =_efdea .Data [_ccfd ]<<_fgff ;if _dbda {_ecge =_cecg (_ecge ,_efdea .Data [_ccfd +1]>>_gece ,_abg );};}else {_ecge =_efdea .Data [_ccfd ]>>_gece ;};_bfbd .Data [_gddb ]=_cecg (_bfbd .Data [_gddb ],^_ecge |_bfbd .Data [_gddb ],_ddd );
_gddb +=_bfbd .BytesPerLine ;_ccfd +=_efdea .BytesPerLine ;};};if _aggc {for _bfdbf =0;_bfdbf < _cded ;_bfdbf ++{for _faece =0;_faece < _ebbe ;_faece ++{_ecge =_cecg (_efdea .Data [_efdg +_faece ]<<_fgff ,_efdea .Data [_efdg +_faece +1]>>_gece ,_abg );
_bfbd .Data [_fbga +_faece ]|=^_ecge ;};_fbga +=_bfbd .BytesPerLine ;_efdg +=_efdea .BytesPerLine ;};};if _aebg {for _bfdbf =0;_bfdbf < _cded ;_bfdbf ++{_ecge =_efdea .Data [_bdad ]<<_fgff ;if _ccfe {_ecge =_cecg (_ecge ,_efdea .Data [_bdad +1]>>_gece ,_abg );
};_bfbd .Data [_ecgd ]=_cecg (_bfbd .Data [_ecgd ],^_ecge |_bfbd .Data [_ecgd ],_egdc );_ecgd +=_bfbd .BytesPerLine ;_bdad +=_efdea .BytesPerLine ;};};case PixNotSrcAndDst :if _bgfec {for _bfdbf =0;_bfdbf < _cded ;_bfdbf ++{if _gegda ==_gccb {_ecge =_efdea .Data [_ccfd ]<<_fgff ;
if _dbda {_ecge =_cecg (_ecge ,_efdea .Data [_ccfd +1]>>_gece ,_abg );};}else {_ecge =_efdea .Data [_ccfd ]>>_gece ;};_bfbd .Data [_gddb ]=_cecg (_bfbd .Data [_gddb ],^_ecge &_bfbd .Data [_gddb ],_ddd );_gddb +=_bfbd .BytesPerLine ;_ccfd +=_efdea .BytesPerLine ;
};};if _aggc {for _bfdbf =0;_bfdbf < _cded ;_bfdbf ++{for _faece =0;_faece < _ebbe ;_faece ++{_ecge =_cecg (_efdea .Data [_efdg +_faece ]<<_fgff ,_efdea .Data [_efdg +_faece +1]>>_gece ,_abg );_bfbd .Data [_fbga +_faece ]&=^_ecge ;};_fbga +=_bfbd .BytesPerLine ;
_efdg +=_efdea .BytesPerLine ;};};if _aebg {for _bfdbf =0;_bfdbf < _cded ;_bfdbf ++{_ecge =_efdea .Data [_bdad ]<<_fgff ;if _ccfe {_ecge =_cecg (_ecge ,_efdea .Data [_bdad +1]>>_gece ,_abg );};_bfbd .Data [_ecgd ]=_cecg (_bfbd .Data [_ecgd ],^_ecge &_bfbd .Data [_ecgd ],_egdc );
_ecgd +=_bfbd .BytesPerLine ;_bdad +=_efdea .BytesPerLine ;};};case PixSrcOrNotDst :if _bgfec {for _bfdbf =0;_bfdbf < _cded ;_bfdbf ++{if _gegda ==_gccb {_ecge =_efdea .Data [_ccfd ]<<_fgff ;if _dbda {_ecge =_cecg (_ecge ,_efdea .Data [_ccfd +1]>>_gece ,_abg );
};}else {_ecge =_efdea .Data [_ccfd ]>>_gece ;};_bfbd .Data [_gddb ]=_cecg (_bfbd .Data [_gddb ],_ecge |^_bfbd .Data [_gddb ],_ddd );_gddb +=_bfbd .BytesPerLine ;_ccfd +=_efdea .BytesPerLine ;};};if _aggc {for _bfdbf =0;_bfdbf < _cded ;_bfdbf ++{for _faece =0;
_faece < _ebbe ;_faece ++{_ecge =_cecg (_efdea .Data [_efdg +_faece ]<<_fgff ,_efdea .Data [_efdg +_faece +1]>>_gece ,_abg );_bfbd .Data [_fbga +_faece ]=_ecge |^_bfbd .Data [_fbga +_faece ];};_fbga +=_bfbd .BytesPerLine ;_efdg +=_efdea .BytesPerLine ;
};};if _aebg {for _bfdbf =0;_bfdbf < _cded ;_bfdbf ++{_ecge =_efdea .Data [_bdad ]<<_fgff ;if _ccfe {_ecge =_cecg (_ecge ,_efdea .Data [_bdad +1]>>_gece ,_abg );};_bfbd .Data [_ecgd ]=_cecg (_bfbd .Data [_ecgd ],_ecge |^_bfbd .Data [_ecgd ],_egdc );_ecgd +=_bfbd .BytesPerLine ;
_bdad +=_efdea .BytesPerLine ;};};case PixSrcAndNotDst :if _bgfec {for _bfdbf =0;_bfdbf < _cded ;_bfdbf ++{if _gegda ==_gccb {_ecge =_efdea .Data [_ccfd ]<<_fgff ;if _dbda {_ecge =_cecg (_ecge ,_efdea .Data [_ccfd +1]>>_gece ,_abg );};}else {_ecge =_efdea .Data [_ccfd ]>>_gece ;
};_bfbd .Data [_gddb ]=_cecg (_bfbd .Data [_gddb ],_ecge &^_bfbd .Data [_gddb ],_ddd );_gddb +=_bfbd .BytesPerLine ;_ccfd +=_efdea .BytesPerLine ;};};if _aggc {for _bfdbf =0;_bfdbf < _cded ;_bfdbf ++{for _faece =0;_faece < _ebbe ;_faece ++{_ecge =_cecg (_efdea .Data [_efdg +_faece ]<<_fgff ,_efdea .Data [_efdg +_faece +1]>>_gece ,_abg );
_bfbd .Data [_fbga +_faece ]=_ecge &^_bfbd .Data [_fbga +_faece ];};_fbga +=_bfbd .BytesPerLine ;_efdg +=_efdea .BytesPerLine ;};};if _aebg {for _bfdbf =0;_bfdbf < _cded ;_bfdbf ++{_ecge =_efdea .Data [_bdad ]<<_fgff ;if _ccfe {_ecge =_cecg (_ecge ,_efdea .Data [_bdad +1]>>_gece ,_abg );
};_bfbd .Data [_ecgd ]=_cecg (_bfbd .Data [_ecgd ],_ecge &^_bfbd .Data [_ecgd ],_egdc );_ecgd +=_bfbd .BytesPerLine ;_bdad +=_efdea .BytesPerLine ;};};case PixNotPixSrcOrDst :if _bgfec {for _bfdbf =0;_bfdbf < _cded ;_bfdbf ++{if _gegda ==_gccb {_ecge =_efdea .Data [_ccfd ]<<_fgff ;
if _dbda {_ecge =_cecg (_ecge ,_efdea .Data [_ccfd +1]>>_gece ,_abg );};}else {_ecge =_efdea .Data [_ccfd ]>>_gece ;};_bfbd .Data [_gddb ]=_cecg (_bfbd .Data [_gddb ],^(_ecge |_bfbd .Data [_gddb ]),_ddd );_gddb +=_bfbd .BytesPerLine ;_ccfd +=_efdea .BytesPerLine ;
};};if _aggc {for _bfdbf =0;_bfdbf < _cded ;_bfdbf ++{for _faece =0;_faece < _ebbe ;_faece ++{_ecge =_cecg (_efdea .Data [_efdg +_faece ]<<_fgff ,_efdea .Data [_efdg +_faece +1]>>_gece ,_abg );_bfbd .Data [_fbga +_faece ]=^(_ecge |_bfbd .Data [_fbga +_faece ]);
};_fbga +=_bfbd .BytesPerLine ;_efdg +=_efdea .BytesPerLine ;};};if _aebg {for _bfdbf =0;_bfdbf < _cded ;_bfdbf ++{_ecge =_efdea .Data [_bdad ]<<_fgff ;if _ccfe {_ecge =_cecg (_ecge ,_efdea .Data [_bdad +1]>>_gece ,_abg );};_bfbd .Data [_ecgd ]=_cecg (_bfbd .Data [_ecgd ],^(_ecge |_bfbd .Data [_ecgd ]),_egdc );
_ecgd +=_bfbd .BytesPerLine ;_bdad +=_efdea .BytesPerLine ;};};case PixNotPixSrcAndDst :if _bgfec {for _bfdbf =0;_bfdbf < _cded ;_bfdbf ++{if _gegda ==_gccb {_ecge =_efdea .Data [_ccfd ]<<_fgff ;if _dbda {_ecge =_cecg (_ecge ,_efdea .Data [_ccfd +1]>>_gece ,_abg );
};}else {_ecge =_efdea .Data [_ccfd ]>>_gece ;};_bfbd .Data [_gddb ]=_cecg (_bfbd .Data [_gddb ],^(_ecge &_bfbd .Data [_gddb ]),_ddd );_gddb +=_bfbd .BytesPerLine ;_ccfd +=_efdea .BytesPerLine ;};};if _aggc {for _bfdbf =0;_bfdbf < _cded ;_bfdbf ++{for _faece =0;
_faece < _ebbe ;_faece ++{_ecge =_cecg (_efdea .Data [_efdg +_faece ]<<_fgff ,_efdea .Data [_efdg +_faece +1]>>_gece ,_abg );_bfbd .Data [_fbga +_faece ]=^(_ecge &_bfbd .Data [_fbga +_faece ]);};_fbga +=_bfbd .BytesPerLine ;_efdg +=_efdea .BytesPerLine ;
};};if _aebg {for _bfdbf =0;_bfdbf < _cded ;_bfdbf ++{_ecge =_efdea .Data [_bdad ]<<_fgff ;if _ccfe {_ecge =_cecg (_ecge ,_efdea .Data [_bdad +1]>>_gece ,_abg );};_bfbd .Data [_ecgd ]=_cecg (_bfbd .Data [_ecgd ],^(_ecge &_bfbd .Data [_ecgd ]),_egdc );_ecgd +=_bfbd .BytesPerLine ;
_bdad +=_efdea .BytesPerLine ;};};case PixNotPixSrcXorDst :if _bgfec {for _bfdbf =0;_bfdbf < _cded ;_bfdbf ++{if _gegda ==_gccb {_ecge =_efdea .Data [_ccfd ]<<_fgff ;if _dbda {_ecge =_cecg (_ecge ,_efdea .Data [_ccfd +1]>>_gece ,_abg );};}else {_ecge =_efdea .Data [_ccfd ]>>_gece ;
};_bfbd .Data [_gddb ]=_cecg (_bfbd .Data [_gddb ],^(_ecge ^_bfbd .Data [_gddb ]),_ddd );_gddb +=_bfbd .BytesPerLine ;_ccfd +=_efdea .BytesPerLine ;};};if _aggc {for _bfdbf =0;_bfdbf < _cded ;_bfdbf ++{for _faece =0;_faece < _ebbe ;_faece ++{_ecge =_cecg (_efdea .Data [_efdg +_faece ]<<_fgff ,_efdea .Data [_efdg +_faece +1]>>_gece ,_abg );
_bfbd .Data [_fbga +_faece ]=^(_ecge ^_bfbd .Data [_fbga +_faece ]);};_fbga +=_bfbd .BytesPerLine ;_efdg +=_efdea .BytesPerLine ;};};if _aebg {for _bfdbf =0;_bfdbf < _cded ;_bfdbf ++{_ecge =_efdea .Data [_bdad ]<<_fgff ;if _ccfe {_ecge =_cecg (_ecge ,_efdea .Data [_bdad +1]>>_gece ,_abg );
};_bfbd .Data [_ecgd ]=_cecg (_bfbd .Data [_ecgd ],^(_ecge ^_bfbd .Data [_ecgd ]),_egdc );_ecgd +=_bfbd .BytesPerLine ;_bdad +=_efdea .BytesPerLine ;};};default:_b .Log .Debug ("\u004f\u0070e\u0072\u0061\u0074\u0069\u006f\u006e\u003a\u0020\u0027\u0025\u0064\u0027\u0020\u006e\u006f\u0074\u0020\u0070\u0065\u0072\u006d\u0069tt\u0065\u0064",_cbacb );
return _f .New ("\u0072\u0061\u0073\u0074\u0065\u0072\u0020\u006f\u0070\u0065r\u0061\u0074\u0069\u006f\u006e\u0020\u006eo\u0074\u0020\u0070\u0065\u0072\u006d\u0069\u0074\u0074\u0065\u0064");};return nil ;};func _ccea (_gbbg _ae .Image ,_ddeg Image ,_gfaec _ae .Rectangle ){if _eaeb ,_bedg :=_gbbg .(SMasker );
_bedg &&_eaeb .HasAlpha (){_ddeg .(SMasker ).MakeAlpha ();};switch _ccfdc :=_gbbg .(type ){case Gray :_ddcab (_ccfdc ,_ddeg .(NRGBA ),_gfaec );case NRGBA :_bafe (_ccfdc ,_ddeg .(NRGBA ),_gfaec );case *_ae .NYCbCrA :_dgcfb (_ccfdc ,_ddeg .(NRGBA ),_gfaec );
case CMYK :_afae (_ccfdc ,_ddeg .(NRGBA ),_gfaec );case RGBA :_egaf (_ccfdc ,_ddeg .(NRGBA ),_gfaec );case nrgba64 :_gafdf (_ccfdc ,_ddeg .(NRGBA ),_gfaec );default:_gfgg (_gbbg ,_ddeg ,_gfaec );};};func AutoThresholdTriangle (histogram [256]int )uint8 {var _fecb ,_cbedf ,_bacdb ,_bfcd int ;
for _fcac :=0;_fcac < len (histogram );_fcac ++{if histogram [_fcac ]> 0{_fecb =_fcac ;break ;};};if _fecb > 0{_fecb --;};for _eecg :=255;_eecg > 0;_eecg --{if histogram [_eecg ]> 0{_bfcd =_eecg ;break ;};};if _bfcd < 255{_bfcd ++;};for _ebab :=0;_ebab < 256;
_ebab ++{if histogram [_ebab ]> _cbedf {_bacdb =_ebab ;_cbedf =histogram [_ebab ];};};var _fdff bool ;if (_bacdb -_fecb )< (_bfcd -_bacdb ){_fdff =true ;var _acdcf int ;_deee :=255;for _acdcf < _deee {_cfgf :=histogram [_acdcf ];histogram [_acdcf ]=histogram [_deee ];
histogram [_deee ]=_cfgf ;_acdcf ++;_deee --;};_fecb =255-_bfcd ;_bacdb =255-_bacdb ;};if _fecb ==_bacdb {return uint8 (_fecb );};_ggab :=float64 (histogram [_bacdb ]);_ddadg :=float64 (_fecb -_bacdb );_cecd :=_d .Sqrt (_ggab *_ggab +_ddadg *_ddadg );_ggab /=_cecd ;
_ddadg /=_cecd ;_cecd =_ggab *float64 (_fecb )+_ddadg *float64 (histogram [_fecb ]);_cefd :=_fecb ;var _fdbd float64 ;for _efdgb :=_fecb +1;_efdgb <=_bacdb ;_efdgb ++{_abcf :=_ggab *float64 (_efdgb )+_ddadg *float64 (histogram [_efdgb ])-_cecd ;if _abcf > _fdbd {_cefd =_efdgb ;
_fdbd =_abcf ;};};_cefd --;if _fdff {var _bcff int ;_fbee :=255;for _bcff < _fbee {_bgdbe :=histogram [_bcff ];histogram [_bcff ]=histogram [_fbee ];histogram [_fbee ]=_bgdbe ;_bcff ++;_fbee --;};return uint8 (255-_cefd );};return uint8 (_cefd );};