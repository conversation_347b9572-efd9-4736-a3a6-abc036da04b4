//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package testutils ;import (_g "crypto/md5";_df "encoding/hex";_ca "errors";_ff "fmt";_a "github.com/unidoc/unipdf/v3/common";_dc "github.com/unidoc/unipdf/v3/core";_b "image";_d "image/png";_f "io";_ece "os";_ed "os/exec";_c "path/filepath";_gg "strings";
_ec "testing";);func _dcd (_gd _dc .PdfObject ,_aae map[int64 ]_dc .PdfObject )error {switch _ffbd :=_gd .(type ){case *_dc .PdfIndirectObject :_egd :=_ffbd ;_dcd (_egd .PdfObject ,_aae );case *_dc .PdfObjectDictionary :_gde :=_ffbd ;for _ ,_gecg :=range _gde .Keys (){_ffg :=_gde .Get (_gecg );
if _dg ,_fgg :=_ffg .(*_dc .PdfObjectReference );_fgg {_bde ,_cbb :=_aae [_dg .ObjectNumber ];if !_cbb {return _ca .New ("r\u0065\u0066\u0065\u0072\u0065\u006ec\u0065\u0020\u0074\u006f\u0020\u006f\u0075\u0074\u0073i\u0064\u0065\u0020o\u0062j\u0065\u0063\u0074");
};_gde .Set (_gecg ,_bde );}else {_dcd (_ffg ,_aae );};};case *_dc .PdfObjectArray :_bee :=_ffbd ;for _fec ,_fbf :=range _bee .Elements (){if _geb ,_ccd :=_fbf .(*_dc .PdfObjectReference );_ccd {_fef ,_aba :=_aae [_geb .ObjectNumber ];if !_aba {return _ca .New ("r\u0065\u0066\u0065\u0072\u0065\u006ec\u0065\u0020\u0074\u006f\u0020\u006f\u0075\u0074\u0073i\u0064\u0065\u0020o\u0062j\u0065\u0063\u0074");
};_bee .Set (_fec ,_fef );}else {_dcd (_fbf ,_aae );};};};return nil ;};func CompareImages (img1 ,img2 _b .Image )(bool ,error ){_dcg :=img1 .Bounds ();_bf :=0;for _af :=0;_af < _dcg .Size ().X ;_af ++{for _cf :=0;_cf < _dcg .Size ().Y ;_cf ++{_bb ,_ffd ,_gee ,_ :=img1 .At (_af ,_cf ).RGBA ();
_ee ,_ggb ,_cc ,_ :=img2 .At (_af ,_cf ).RGBA ();if _bb !=_ee ||_ffd !=_ggb ||_gee !=_cc {_bf ++;};};};_ea :=float64 (_bf )/float64 (_dcg .Dx ()*_dcg .Dy ());if _ea > 0.0001{_ff .Printf ("\u0064\u0069\u0066f \u0066\u0072\u0061\u0063\u0074\u0069\u006f\u006e\u003a\u0020\u0025\u0076\u0020\u0028\u0025\u0064\u0029\u000a",_ea ,_bf );
return false ,nil ;};return true ,nil ;};func ReadPNG (file string )(_b .Image ,error ){_fe ,_fg :=_ece .Open (file );if _fg !=nil {return nil ,_fg ;};defer _fe .Close ();return _d .Decode (_fe );};func ComparePNGFiles (file1 ,file2 string )(bool ,error ){_cb ,_cff :=HashFile (file1 );
if _cff !=nil {return false ,_cff ;};_bd ,_cff :=HashFile (file2 );if _cff !=nil {return false ,_cff ;};if _cb ==_bd {return true ,nil ;};_eeb ,_cff :=ReadPNG (file1 );if _cff !=nil {return false ,_cff ;};_ab ,_cff :=ReadPNG (file2 );if _cff !=nil {return false ,_cff ;
};if _eeb .Bounds ()!=_ab .Bounds (){return false ,nil ;};return CompareImages (_eeb ,_ab );};func CopyFile (src ,dst string )error {_ge ,_fb :=_ece .Open (src );if _fb !=nil {return _fb ;};defer _ge .Close ();_ffb ,_fb :=_ece .Create (dst );if _fb !=nil {return _fb ;
};defer _ffb .Close ();_ ,_fb =_f .Copy (_ffb ,_ge );return _fb ;};func RenderPDFToPNGs (pdfPath string ,dpi int ,outpathTpl string )error {if dpi <=0{dpi =100;};if _ ,_cg :=_ed .LookPath ("\u0067\u0073");_cg !=nil {return ErrRenderNotSupported ;};return _ed .Command ("\u0067\u0073","\u002d\u0073\u0044\u0045\u0056\u0049\u0043\u0045\u003d\u0070\u006e\u0067a\u006c\u0070\u0068\u0061","\u002d\u006f",outpathTpl ,_ff .Sprintf ("\u002d\u0072\u0025\u0064",dpi ),pdfPath ).Run ();
};var (ErrRenderNotSupported =_ca .New ("\u0072\u0065\u006e\u0064\u0065r\u0069\u006e\u0067\u0020\u0050\u0044\u0046\u0020\u0066\u0069\u006c\u0065\u0073 \u0069\u0073\u0020\u006e\u006f\u0074\u0020\u0073\u0075\u0070\u0070\u006f\u0072\u0074\u0065\u0064\u0020\u006f\u006e\u0020\u0074\u0068\u0069\u0073\u0020\u0073\u0079\u0073\u0074\u0065m");
);func CompareDictionariesDeep (d1 ,d2 *_dc .PdfObjectDictionary )bool {if len (d1 .Keys ())!=len (d2 .Keys ()){_a .Log .Debug ("\u0044\u0069\u0063\u0074\u0020\u0065\u006e\u0074\u0072\u0069\u0065\u0073\u0020\u006d\u0069s\u006da\u0074\u0063\u0068\u0020\u0028\u0025\u0064\u0020\u0021\u003d\u0020\u0025\u0064\u0029",len (d1 .Keys ()),len (d2 .Keys ()));
_a .Log .Debug ("\u0057\u0061s\u0020\u0027\u0025s\u0027\u0020\u0076\u0073\u0020\u0027\u0025\u0073\u0027",d1 .WriteString (),d2 .WriteString ());return false ;};for _ ,_bc :=range d1 .Keys (){if _bc =="\u0050\u0061\u0072\u0065\u006e\u0074"{continue ;};_dcb :=_dc .TraceToDirectObject (d1 .Get (_bc ));
_cfd :=_dc .TraceToDirectObject (d2 .Get (_bc ));if _dcb ==nil {_a .Log .Debug ("\u00761\u0020\u0069\u0073\u0020\u006e\u0069l");return false ;};if _cfd ==nil {_a .Log .Debug ("\u00762\u0020\u0069\u0073\u0020\u006e\u0069l");return false ;};switch _dcbe :=_dcb .(type ){case *_dc .PdfObjectDictionary :_eed ,_fd :=_cfd .(*_dc .PdfObjectDictionary );
if !_fd {_a .Log .Debug ("\u0054\u0079\u0070\u0065 m\u0069\u0073\u006d\u0061\u0074\u0063\u0068\u0020\u0025\u0054\u0020\u0076\u0073\u0020%\u0054",_dcb ,_cfd );return false ;};if !CompareDictionariesDeep (_dcbe ,_eed ){return false ;};continue ;case *_dc .PdfObjectArray :_agc ,_dgf :=_cfd .(*_dc .PdfObjectArray );
if !_dgf {_a .Log .Debug ("\u00762\u0020n\u006f\u0074\u0020\u0061\u006e\u0020\u0061\u0072\u0072\u0061\u0079");return false ;};if _dcbe .Len ()!=_agc .Len (){_a .Log .Debug ("\u0061\u0072\u0072\u0061\u0079\u0020\u006c\u0065\u006e\u0067\u0074\u0068\u0020\u006d\u0069s\u006da\u0074\u0063\u0068\u0020\u0028\u0025\u0064\u0020\u0021\u003d\u0020\u0025\u0064\u0029",_dcbe .Len (),_agc .Len ());
return false ;};for _de :=0;_de < _dcbe .Len ();_de ++{_agg :=_dc .TraceToDirectObject (_dcbe .Get (_de ));_cbc :=_dc .TraceToDirectObject (_agc .Get (_de ));if _bfd ,_gge :=_agg .(*_dc .PdfObjectDictionary );_gge {_ga ,_gcc :=_cbc .(*_dc .PdfObjectDictionary );
if !_gcc {return false ;};if !CompareDictionariesDeep (_bfd ,_ga ){return false ;};}else {if _agg .WriteString ()!=_cbc .WriteString (){_a .Log .Debug ("M\u0069\u0073\u006d\u0061tc\u0068 \u0027\u0025\u0073\u0027\u0020!\u003d\u0020\u0027\u0025\u0073\u0027",_agg .WriteString (),_cbc .WriteString ());
return false ;};};};continue ;};if _dcb .String ()!=_cfd .String (){_a .Log .Debug ("\u006b\u0065y\u003d\u0025\u0073\u0020\u004d\u0069\u0073\u006d\u0061\u0074\u0063\u0068\u0021\u0020\u0027\u0025\u0073\u0027\u0020\u0021\u003d\u0020'%\u0073\u0027",_bc ,_dcb .String (),_cfd .String ());
_a .Log .Debug ("\u0046o\u0072 \u0027\u0025\u0054\u0027\u0020\u002d\u0020\u0027\u0025\u0054\u0027",_dcb ,_cfd );_a .Log .Debug ("\u0046\u006f\u0072\u0020\u0027\u0025\u002b\u0076\u0027\u0020\u002d\u0020'\u0025\u002b\u0076\u0027",_dcb ,_cfd );return false ;
};};return true ;};func HashFile (file string )(string ,error ){_aa ,_eg :=_ece .Open (file );if _eg !=nil {return "",_eg ;};defer _aa .Close ();_ce :=_g .New ();if _ ,_eg =_f .Copy (_ce ,_aa );_eg !=nil {return "",_eg ;};return _df .EncodeToString (_ce .Sum (nil )),nil ;
};func RunRenderTest (t *_ec .T ,pdfPath ,outputDir ,baselineRenderPath string ,saveBaseline bool ){_eaf :=_gg .TrimSuffix (_c .Base (pdfPath ),_c .Ext (pdfPath ));t .Run ("\u0072\u0065\u006e\u0064\u0065\u0072",func (_fcd *_ec .T ){_gea :=_c .Join (outputDir ,_eaf );
_dfb :=_gea +"\u002d%\u0064\u002e\u0070\u006e\u0067";if _fa :=RenderPDFToPNGs (pdfPath ,0,_dfb );_fa !=nil {_fcd .Skip (_fa );};for _bg :=1;true ;_bg ++{_da :=_ff .Sprintf ("\u0025s\u002d\u0025\u0064\u002e\u0070\u006eg",_gea ,_bg );_gc :=_c .Join (baselineRenderPath ,_ff .Sprintf ("\u0025\u0073\u002d\u0025\u0064\u005f\u0065\u0078\u0070\u002e\u0070\u006e\u0067",_eaf ,_bg ));
if _ ,_gec :=_ece .Stat (_da );_gec !=nil {break ;};_fcd .Logf ("\u0025\u0073",_gc );if saveBaseline {_fcd .Logf ("\u0043\u006fp\u0079\u0069\u006eg\u0020\u0025\u0073\u0020\u002d\u003e\u0020\u0025\u0073",_da ,_gc );_cd :=CopyFile (_da ,_gc );if _cd !=nil {_fcd .Fatalf ("\u0045\u0052\u0052OR\u0020\u0063\u006f\u0070\u0079\u0069\u006e\u0067\u0020\u0074\u006f\u0020\u0025\u0073\u003a\u0020\u0025\u0076",_gc ,_cd );
};continue ;};_fcd .Run (_ff .Sprintf ("\u0070\u0061\u0067\u0065\u0025\u0064",_bg ),func (_ad *_ec .T ){_ad .Logf ("\u0043o\u006dp\u0061\u0072\u0069\u006e\u0067 \u0025\u0073 \u0076\u0073\u0020\u0025\u0073",_da ,_gc );_gb ,_fbg :=ComparePNGFiles (_da ,_gc );
if _ece .IsNotExist (_fbg ){_ad .Fatal ("\u0069m\u0061g\u0065\u0020\u0066\u0069\u006ce\u0020\u006di\u0073\u0073\u0069\u006e\u0067");}else if !_gb {_ad .Fatal ("\u0077\u0072\u006f\u006eg \u0070\u0061\u0067\u0065\u0020\u0072\u0065\u006e\u0064\u0065\u0072\u0065\u0064");
};});};});};func ParseIndirectObjects (rawpdf string )(map[int64 ]_dc .PdfObject ,error ){_ag :=_dc .NewParserFromString (rawpdf );_bge :=map[int64 ]_dc .PdfObject {};for {_be ,_dbf :=_ag .ParseIndirectObject ();if _dbf !=nil {if _dbf ==_f .EOF {break ;
};return nil ,_dbf ;};switch _eag :=_be .(type ){case *_dc .PdfIndirectObject :_bge [_eag .ObjectNumber ]=_be ;case *_dc .PdfObjectStream :_bge [_eag .ObjectNumber ]=_be ;};};for _ ,_eb :=range _bge {_dcd (_eb ,_bge );};return _bge ,nil ;};