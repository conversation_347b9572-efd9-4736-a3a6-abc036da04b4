//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package license ;import (_ec "bytes";_a "compress/gzip";_c "crypto";_gc "crypto/aes";_bg "crypto/cipher";_g "crypto/hmac";_ecc "crypto/rand";_df "crypto/rsa";_ed "crypto/sha256";_fac "crypto/sha512";_cc "crypto/x509";_bag "encoding/base64";_cf "encoding/hex";
_eeg "encoding/json";_dc "encoding/pem";_fg "errors";_ee "fmt";_fgd "github.com/unidoc/unipdf/v3/common";_b "io";_fd "net";_da "net/http";_e "os";_fa "path/filepath";_d "sort";_ecb "strings";_ba "sync";_ag "time";);func _ecd (_gdf string )(LicenseKey ,error ){var _de LicenseKey ;
_agcf ,_fe :=_gf (_ce ,_fae ,_gdf );if _fe !=nil {return _de ,_fe ;};_ad ,_fe :=_dac (_befe ,_agcf );if _fe !=nil {return _de ,_fe ;};_fe =_eeg .Unmarshal (_ad ,&_de );if _fe !=nil {return _de ,_fe ;};_de .CreatedAt =_ag .Unix (_de .CreatedAtInt ,0);if _de .ExpiresAtInt > 0{_af :=_ag .Unix (_de .ExpiresAtInt ,0);
_de .ExpiresAt =&_af ;};return _de ,nil ;};func Track (docKey string ,useKey string ,docName string )error {return _aab (docKey ,useKey ,docName ,!_gab ._gff );};var _gfc =_ag .Date (2019,6,6,0,0,0,0,_ag .UTC );type LicenseKey struct{LicenseId string `json:"license_id"`;
CustomerId string `json:"customer_id"`;CustomerName string `json:"customer_name"`;Tier string `json:"tier"`;CreatedAt _ag .Time `json:"-"`;CreatedAtInt int64 `json:"created_at"`;ExpiresAt *_ag .Time `json:"-"`;ExpiresAtInt int64 `json:"expires_at"`;CreatedBy string `json:"created_by"`;
CreatorName string `json:"creator_name"`;CreatorEmail string `json:"creator_email"`;UniPDF bool `json:"unipdf"`;UniOffice bool `json:"unioffice"`;UniHTML bool `json:"unihtml"`;Trial bool `json:"trial"`;_fbe bool ;_adc string ;_gff bool ;_eg bool ;};func _fc (_ga string ,_ab []byte )(string ,error ){_faa ,_ :=_dc .Decode ([]byte (_ga ));
if _faa ==nil {return "",_ee .Errorf ("\u0050\u0072\u0069\u0076\u004b\u0065\u0079\u0020\u0066a\u0069\u006c\u0065\u0064");};_cg ,_ca :=_cc .ParsePKCS1PrivateKey (_faa .Bytes );if _ca !=nil {return "",_ca ;};_dfb :=_fac .New ();_dfb .Write (_ab );_fb :=_dfb .Sum (nil );
_baf ,_ca :=_df .SignPKCS1v15 (_ecc .Reader ,_cg ,_c .SHA512 ,_fb );if _ca !=nil {return "",_ca ;};_gcg :=_bag .StdEncoding .EncodeToString (_ab );_gcg +="\u000a\u002b\u000a";_gcg +=_bag .StdEncoding .EncodeToString (_baf );return _gcg ,nil ;};var _gga map[string ]struct{};
func _gbf (_gef *_da .Response )(_b .ReadCloser ,error ){var _ffa error ;var _bce _b .ReadCloser ;switch _ecb .ToLower (_gef .Header .Get ("\u0043\u006fn\u0074\u0065\u006et\u002d\u0045\u006e\u0063\u006f\u0064\u0069\u006e\u0067")){case "\u0067\u007a\u0069\u0070":_bce ,_ffa =_a .NewReader (_gef .Body );
if _ffa !=nil {return _bce ,_ffa ;};defer _bce .Close ();default:_bce =_gef .Body ;};return _bce ,nil ;};func (_gfe defaultStateHolder )loadState (_aff string )(reportState ,error ){_faed ,_abg :=_def ();if _abg !=nil {return reportState {},_abg ;};_abg =_e .MkdirAll (_faed ,0777);
if _abg !=nil {return reportState {},_abg ;};if len (_aff )< 20{return reportState {},_fg .New ("i\u006e\u0076\u0061\u006c\u0069\u0064\u0020\u006b\u0065\u0079");};_fda :=[]byte (_aff );_ddd :=_fac .Sum512_256 (_fda [:20]);_gg :=_cf .EncodeToString (_ddd [:]);
_gcgc :=_fa .Join (_faed ,_gg );_abb ,_abg :=_e .ReadFile (_gcgc );if _abg !=nil {if _e .IsNotExist (_abg ){return reportState {},nil ;};_fgd .Log .Debug ("\u0045R\u0052\u004f\u0052\u003a\u0020\u0025v",_abg );return reportState {},_fg .New ("\u0069\u006e\u0076a\u006c\u0069\u0064\u0020\u0064\u0061\u0074\u0061");
};const _bgbd ="\u0068\u00619\u004e\u004b\u0038]\u0052\u0062\u004c\u002a\u006d\u0034\u004c\u004b\u0057";_abb ,_abg =_bfe ([]byte (_bgbd ),_abb );if _abg !=nil {return reportState {},_abg ;};var _fba reportState ;_abg =_eeg .Unmarshal (_abb ,&_fba );if _abg !=nil {_fgd .Log .Debug ("\u0045\u0052\u0052OR\u003a\u0020\u0049\u006e\u0076\u0061\u006c\u0069\u0064\u0020\u0064\u0061\u0074\u0061\u003a\u0020\u0025\u0076",_abg );
return reportState {},_fg .New ("\u0069\u006e\u0076a\u006c\u0069\u0064\u0020\u0064\u0061\u0074\u0061");};return _fba ,nil ;};func (_bbe *LicenseKey )getExpiryDateToCompare ()_ag .Time {if _bbe .Trial {return _ag .Now ().UTC ();};return _fgd .ReleasedAt ;
};func (_fgdf *LicenseKey )isExpired ()bool {return _fgdf .getExpiryDateToCompare ().After (*_fgdf .ExpiresAt );};type defaultStateHolder struct{};const (LicenseTierUnlicensed ="\u0075\u006e\u006c\u0069\u0063\u0065\u006e\u0073\u0065\u0064";LicenseTierCommunity ="\u0063o\u006d\u006d\u0075\u006e\u0069\u0074y";
LicenseTierIndividual ="\u0069\u006e\u0064\u0069\u0076\u0069\u0064\u0075\u0061\u006c";LicenseTierBusiness ="\u0062\u0075\u0073\u0069\u006e\u0065\u0073\u0073";);func GetMeteredState ()(MeteredStatus ,error ){if _gab ==nil {return MeteredStatus {},_fg .New ("\u006c\u0069\u0063\u0065ns\u0065\u0020\u006b\u0065\u0079\u0020\u006e\u006f\u0074\u0020\u0073\u0065\u0074");
};if !_gab ._fbe ||len (_gab ._adc )==0{return MeteredStatus {},_fg .New ("\u0061p\u0069 \u006b\u0065\u0079\u0020\u006e\u006f\u0074\u0020\u0073\u0065\u0074");};_bgb ,_dea :=_bga .loadState (_gab ._adc );if _dea !=nil {_fgd .Log .Debug ("\u0045R\u0052\u004f\u0052\u003a\u0020\u0025v",_dea );
return MeteredStatus {},_dea ;};if _bgb .Docs > 0{_gcc :=_aab ("","","",true );if _gcc !=nil {return MeteredStatus {},_gcc ;};};_gfcf .Lock ();defer _gfcf .Unlock ();_gcd :=_ede ();_gcd ._gda =_gab ._adc ;_eab ,_dea :=_gcd .getStatus ();if _dea !=nil {return MeteredStatus {},_dea ;
};if !_eab .Valid {return MeteredStatus {},_fg .New ("\u006b\u0065\u0079\u0020\u006e\u006f\u0074\u0020\u0076\u0061\u006c\u0069\u0064");};_eege :=MeteredStatus {OK :true ,Credits :_eab .OrgCredits ,Used :_eab .OrgUsed };return _eege ,nil ;};func (_bef *meteredClient )getStatus ()(meteredStatusResp ,error ){var _cca meteredStatusResp ;
_fcd :=_bef ._ebc +"\u002fm\u0065t\u0065\u0072\u0065\u0064\u002f\u0073\u0074\u0061\u0074\u0075\u0073";var _dce meteredStatusForm ;_ecda ,_fcb :=_eeg .Marshal (_dce );if _fcb !=nil {return _cca ,_fcb ;};_facb ,_fcb :=_fbf (_ecda );if _fcb !=nil {return _cca ,_fcb ;
};_cd ,_fcb :=_da .NewRequest ("\u0050\u004f\u0053\u0054",_fcd ,_facb );if _fcb !=nil {return _cca ,_fcb ;};_cd .Header .Add ("\u0043\u006f\u006et\u0065\u006e\u0074\u002d\u0054\u0079\u0070\u0065","\u0061\u0070p\u006c\u0069\u0063a\u0074\u0069\u006f\u006e\u002f\u006a\u0073\u006f\u006e");
_cd .Header .Add ("\u0043\u006fn\u0074\u0065\u006et\u002d\u0045\u006e\u0063\u006f\u0064\u0069\u006e\u0067","\u0067\u007a\u0069\u0070");_cd .Header .Add ("\u0041c\u0063e\u0070\u0074\u002d\u0045\u006e\u0063\u006f\u0064\u0069\u006e\u0067","\u0067\u007a\u0069\u0070");
_cd .Header .Add ("\u0058-\u0041\u0050\u0049\u002d\u004b\u0045Y",_bef ._gda );_cagf ,_fcb :=_bef ._abaa .Do (_cd );if _fcb !=nil {return _cca ,_fcb ;};defer _cagf .Body .Close ();if _cagf .StatusCode !=200{return _cca ,_ee .Errorf ("\u0066\u0061i\u006c\u0065\u0064\u0020t\u006f\u0020c\u0068\u0065\u0063\u006b\u0069\u006e\u002c\u0020s\u0074\u0061\u0074\u0075\u0073\u0020\u0063\u006f\u0064\u0065\u0020\u0069s\u003a\u0020\u0025\u0064",_cagf .StatusCode );
};_fgg ,_fcb :=_eggc (_cagf );if _fcb !=nil {return _cca ,_fcb ;};_fcb =_eeg .Unmarshal (_fgg ,&_cca );if _fcb !=nil {return _cca ,_fcb ;};return _cca ,nil ;};func _gf (_ff string ,_beb string ,_cga string )(string ,error ){_edb :=_ecb .Index (_cga ,_ff );
if _edb ==-1{return "",_ee .Errorf ("\u0068\u0065a\u0064\u0065\u0072 \u006e\u006f\u0074\u0020\u0066\u006f\u0075\u006e\u0064");};_gd :=_ecb .Index (_cga ,_beb );if _gd ==-1{return "",_ee .Errorf ("\u0066\u006fo\u0074\u0065\u0072 \u006e\u006f\u0074\u0020\u0066\u006f\u0075\u006e\u0064");
};_cag :=_edb +len (_ff )+1;return _cga [_cag :_gd -1],nil ;};var _gfcf =&_ba .Mutex {};var _aba =_ag .Date (2010,1,1,0,0,0,0,_ag .UTC );type MeteredStatus struct{OK bool ;Credits int64 ;Used int64 ;};func (_bgd *LicenseKey )TypeToString ()string {if _bgd ._fbe {return "M\u0065t\u0065\u0072\u0065\u0064\u0020\u0073\u0075\u0062s\u0063\u0072\u0069\u0070ti\u006f\u006e";
};if _bgd .Tier ==LicenseTierUnlicensed {return "\u0055\u006e\u006c\u0069\u0063\u0065\u006e\u0073\u0065\u0064";};if _bgd .Tier ==LicenseTierCommunity {return "\u0041\u0047PL\u0076\u0033\u0020O\u0070\u0065\u006e\u0020Sou\u0072ce\u0020\u0043\u006f\u006d\u006d\u0075\u006eit\u0079\u0020\u004c\u0069\u0063\u0065\u006es\u0065";
};if _bgd .Tier ==LicenseTierIndividual ||_bgd .Tier =="\u0069\u006e\u0064i\u0065"{return "\u0043\u006f\u006dm\u0065\u0072\u0063\u0069a\u006c\u0020\u004c\u0069\u0063\u0065\u006es\u0065\u0020\u002d\u0020\u0049\u006e\u0064\u0069\u0076\u0069\u0064\u0075\u0061\u006c";
};return "\u0043\u006fm\u006d\u0065\u0072\u0063\u0069\u0061\u006c\u0020\u004c\u0069\u0063\u0065\u006e\u0073\u0065\u0020\u002d\u0020\u0042\u0075\u0073\u0069ne\u0073\u0073";};func SetLicenseKey (content string ,customerName string )error {_cbd ,_gee :=_ecd (content );
if _gee !=nil {_fgd .Log .Error ("\u004c\u0069c\u0065\u006e\u0073\u0065\u0020\u0063\u006f\u0064\u0065\u0020\u0064\u0065\u0063\u006f\u0064\u0065\u0020\u0065\u0072\u0072\u006f\u0072: \u0025\u0076",_gee );return _gee ;};if !_ecb .EqualFold (_cbd .CustomerName ,customerName ){_fgd .Log .Error ("L\u0069ce\u006es\u0065 \u0063\u006f\u0064\u0065\u0020i\u0073\u0073\u0075e\u0020\u002d\u0020\u0043\u0075s\u0074\u006f\u006de\u0072\u0020\u006e\u0061\u006d\u0065\u0020\u006d\u0069\u0073\u006da\u0074\u0063\u0068, e\u0078\u0070\u0065\u0063\u0074\u0065d\u0020\u0027\u0025\u0073\u0027\u002c\u0020\u0062\u0075\u0074\u0020\u0067o\u0074 \u0027\u0025\u0073\u0027",_cbd .CustomerName ,customerName );
return _ee .Errorf ("\u0063\u0075\u0073\u0074\u006fm\u0065\u0072\u0020\u006e\u0061\u006d\u0065\u0020\u006d\u0069\u0073\u006d\u0061t\u0063\u0068\u002c\u0020\u0065\u0078\u0070\u0065\u0063\u0074\u0065\u0064\u0020\u0027\u0025\u0073\u0027\u002c\u0020\u0062\u0075\u0074\u0020\u0067\u006f\u0074\u0020\u0027\u0025\u0073'",_cbd .CustomerName ,customerName );
};_gee =_cbd .Validate ();if _gee !=nil {_fgd .Log .Error ("\u004c\u0069\u0063\u0065\u006e\u0073e\u0020\u0063\u006f\u0064\u0065\u0020\u0076\u0061\u006c\u0069\u0064\u0061\u0074i\u006f\u006e\u0020\u0065\u0072\u0072\u006fr\u003a\u0020\u0025\u0076",_gee );
return _gee ;};_gab =&_cbd ;return nil ;};func SetMeteredKeyUsageLogVerboseMode (val bool ){_gab ._eg =val };func _eace ()([]string ,[]string ,error ){_fee ,_aaa :=_fd .Interfaces ();if _aaa !=nil {return nil ,nil ,_aaa ;};var _fbd []string ;var _dfe []string ;
for _ ,_dafa :=range _fee {if _dafa .Flags &_fd .FlagUp ==0||_ec .Equal (_dafa .HardwareAddr ,nil ){continue ;};_gfef ,_dgb :=_dafa .Addrs ();if _dgb !=nil {return nil ,nil ,_dgb ;};_dceb :=0;for _ ,_gccd :=range _gfef {var _ceab _fd .IP ;switch _agd :=_gccd .(type ){case *_fd .IPNet :_ceab =_agd .IP ;
case *_fd .IPAddr :_ceab =_agd .IP ;};if _ceab .IsLoopback (){continue ;};if _ceab .To4 ()==nil {continue ;};_dfe =append (_dfe ,_ceab .String ());_dceb ++;};_cdg :=_dafa .HardwareAddr .String ();if _cdg !=""&&_dceb > 0{_fbd =append (_fbd ,_cdg );};};return _fbd ,_dfe ,nil ;
};func (_aee *LicenseKey )Validate ()error {if _aee ._fbe {return nil ;};if len (_aee .LicenseId )< 10{return _ee .Errorf ("i\u006e\u0076\u0061\u006c\u0069\u0064 \u006c\u0069\u0063\u0065\u006e\u0073\u0065\u003a\u0020L\u0069\u0063\u0065n\u0073e\u0020\u0049\u0064");
};if len (_aee .CustomerId )< 10{return _ee .Errorf ("\u0069\u006e\u0076\u0061l\u0069\u0064\u0020\u006c\u0069\u0063\u0065\u006e\u0073\u0065:\u0020C\u0075\u0073\u0074\u006f\u006d\u0065\u0072 \u0049\u0064");};if len (_aee .CustomerName )< 1{return _ee .Errorf ("\u0069\u006e\u0076\u0061\u006c\u0069\u0064\u0020\u006c\u0069c\u0065\u006e\u0073\u0065\u003a\u0020\u0043u\u0073\u0074\u006f\u006d\u0065\u0072\u0020\u004e\u0061\u006d\u0065");
};if _aba .After (_aee .CreatedAt ){return _ee .Errorf ("\u0069\u006e\u0076\u0061\u006c\u0069\u0064\u0020\u006c\u0069\u0063\u0065\u006e\u0073\u0065\u003a\u0020\u0043\u0072\u0065\u0061\u0074\u0065\u0064 \u0041\u0074\u0020\u0069\u0073 \u0069\u006ev\u0061\u006c\u0069\u0064");
};if _aee .ExpiresAt ==nil {_ef :=_aee .CreatedAt .AddDate (1,0,0);if _bf .After (_ef ){_ef =_bf ;};_aee .ExpiresAt =&_ef ;};if _aee .CreatedAt .After (*_aee .ExpiresAt ){return _ee .Errorf ("i\u006e\u0076\u0061\u006c\u0069\u0064\u0020\u006c\u0069\u0063\u0065\u006e\u0073\u0065\u003a\u0020\u0043\u0072\u0065\u0061\u0074\u0065\u0064\u0020\u0041\u0074 \u0063a\u006e\u006e\u006f\u0074 \u0062\u0065 \u0047\u0072\u0065\u0061\u0074\u0065\u0072\u0020\u0074\u0068\u0061\u006e\u0020\u0045\u0078\u0070\u0069\u0072\u0065\u0073\u0020\u0041\u0074");
};if _aee .isExpired (){_dec :="\u0054\u0068\u0065\u0020\u006c\u0069c\u0065\u006e\u0073\u0065\u0020\u0068\u0061\u0073\u0020\u0061\u006c\u0072\u0065a\u0064\u0079\u0020\u0065\u0078\u0070\u0069r\u0065\u0064\u002e\u000a"+"\u0059o\u0075\u0020\u006d\u0061y\u0020n\u0065\u0065\u0064\u0020\u0074\u006f\u0020\u0075\u0070d\u0061\u0074\u0065\u0020\u0074\u0068\u0065\u0020l\u0069\u0063\u0065\u006e\u0073\u0065\u0020\u006b\u0065\u0079\u0020t\u006f\u0020\u0074\u0068\u0065\u0020\u006e\u0065\u0077\u0065s\u0074\u0020\u006c\u0069\u0063\u0065\u006e\u0073\u0065\u0020\u006b\u0065\u0079\u0020\u0066\u006f\u0072\u0020\u0079o\u0075\u0072\u0020\u006f\u0072\u0067\u0061\u006e\u0069\u007a\u0061\u0074i\u006fn\u002e\u000a"+"\u0054o\u0020\u0066\u0069\u006ed y\u006f\u0075\u0072\u0020n\u0065\u0077\u0065\u0073\u0074\u0020\u006c\u0069\u0063\u0065n\u0073\u0065\u0020\u006b\u0065\u0079\u002c\u0020\u0067\u006f\u0020\u0074\u006f\u0020\u0068\u0074\u0074\u0070\u0073\u003a\u002f\u002f\u0063l\u006f\u0075\u0064\u002e\u0075\u006e\u0069\u0064oc\u002e\u0069\u006f \u0061\u006e\u0064\u0020\u0067o\u0020t\u006f\u0020\u0074\u0068\u0065\u0020\u006c\u0069\u0063e\u006e\u0073\u0065\u0020\u006d\u0065\u006e\u0075\u002e";
return _ee .Errorf ("\u0069\u006e\u0076\u0061li\u0064\u0020\u006c\u0069\u0063\u0065\u006e\u0073\u0065\u003a\u0020\u0025\u0073",_dec );};if len (_aee .CreatorName )< 1{return _ee .Errorf ("\u0069\u006ev\u0061\u006c\u0069\u0064\u0020\u006c\u0069\u0063\u0065\u006e\u0073\u0065\u003a\u0020\u0043\u0072\u0065\u0061\u0074\u006f\u0072\u0020na\u006d\u0065");
};if len (_aee .CreatorEmail )< 1{return _ee .Errorf ("\u0069\u006e\u0076\u0061\u006c\u0069\u0064\u0020\u006c\u0069c\u0065\u006e\u0073\u0065\u003a\u0020\u0043r\u0065\u0061\u0074\u006f\u0072\u0020\u0065\u006d\u0061\u0069\u006c");};if _aee .CreatedAt .After (_gfc ){if !_aee .UniPDF {return _ee .Errorf ("\u0069\u006e\u0076\u0061\u006c\u0069\u0064\u0020\u006c\u0069\u0063\u0065\u006e\u0073\u0065:\u0020\u0054\u0068\u0069\u0073\u0020\u0055\u006e\u0069\u0044\u006f\u0063\u0020k\u0065\u0079\u0020\u0069\u0073\u0020\u0069\u006e\u0076\u0061\u006c\u0069d \u0066\u006f\u0072\u0020\u0055\u006e\u0069\u0050\u0044\u0046");
};};return nil ;};var _egf map[string ]int ;type meteredClient struct{_ebc string ;_gda string ;_abaa *_da .Client ;};func _dac (_cee string ,_bge string )([]byte ,error ){var (_bb int ;_agc string ;);for _ ,_agc =range []string {"\u000a\u002b\u000a","\u000d\u000a\u002b\r\u000a","\u0020\u002b\u0020"}{if _bb =_ecb .Index (_bge ,_agc );
_bb !=-1{break ;};};if _bb ==-1{return nil ,_ee .Errorf ("\u0069\u006e\u0076al\u0069\u0064\u0020\u0069\u006e\u0070\u0075\u0074\u002c \u0073i\u0067n\u0061t\u0075\u0072\u0065\u0020\u0073\u0065\u0070\u0061\u0072\u0061\u0074\u006f\u0072");};_db :=_bge [:_bb ];
_dd :=_bb +len (_agc );_be :=_bge [_dd :];if _db ==""||_be ==""{return nil ,_ee .Errorf ("\u0069n\u0076\u0061l\u0069\u0064\u0020\u0069n\u0070\u0075\u0074,\u0020\u006d\u0069\u0073\u0073\u0069\u006e\u0067\u0020or\u0069\u0067\u0069n\u0061\u006c \u006f\u0072\u0020\u0073\u0069\u0067n\u0061\u0074u\u0072\u0065");
};_ea ,_fgb :=_bag .StdEncoding .DecodeString (_db );if _fgb !=nil {return nil ,_ee .Errorf ("\u0069\u006e\u0076\u0061li\u0064\u0020\u0069\u006e\u0070\u0075\u0074\u0020\u006f\u0072\u0069\u0067\u0069\u006ea\u006c");};_dae ,_fgb :=_bag .StdEncoding .DecodeString (_be );
if _fgb !=nil {return nil ,_ee .Errorf ("\u0069\u006e\u0076al\u0069\u0064\u0020\u0069\u006e\u0070\u0075\u0074\u0020\u0073\u0069\u0067\u006e\u0061\u0074\u0075\u0072\u0065");};_dbb ,_ :=_dc .Decode ([]byte (_cee ));if _dbb ==nil {return nil ,_ee .Errorf ("\u0050\u0075\u0062\u004b\u0065\u0079\u0020\u0066\u0061\u0069\u006c\u0065\u0064");
};_ae ,_fgb :=_cc .ParsePKIXPublicKey (_dbb .Bytes );if _fgb !=nil {return nil ,_fgb ;};_ebb :=_ae .(*_df .PublicKey );if _ebb ==nil {return nil ,_ee .Errorf ("\u0050u\u0062\u004b\u0065\u0079\u0020\u0063\u006f\u006e\u0076\u0065\u0072s\u0069\u006f\u006e\u0020\u0066\u0061\u0069\u006c\u0065\u0064");
};_gcb :=_fac .New ();_gcb .Write (_ea );_eac :=_gcb .Sum (nil );_fgb =_df .VerifyPKCS1v15 (_ebb ,_c .SHA512 ,_eac ,_dae );if _fgb !=nil {return nil ,_fgb ;};return _ea ,nil ;};const _aefa ="\u0055\u004e\u0049\u0050DF\u005f\u004c\u0049\u0043\u0045\u004e\u0053\u0045\u005f\u0050\u0041\u0054\u0048";
type reportState struct{Instance string `json:"inst"`;Next string `json:"n"`;Docs int64 `json:"d"`;NumErrors int64 `json:"e"`;LimitDocs bool `json:"ld"`;RemainingDocs int64 `json:"rd"`;LastReported _ag .Time `json:"lr"`;LastWritten _ag .Time `json:"lw"`;
Usage map[string ]int `json:"u"`;UsageLogs []interface{}`json:"ul,omitempty"`;};type stateLoader interface{loadState (_ddaa string )(reportState ,error );updateState (_ecbg ,_bac ,_aef string ,_dde int ,_aga bool ,_ffed int ,_efe int ,_cda _ag .Time ,_gdb map[string ]int ,_bd ...interface{})error ;
};var _gab =MakeUnlicensedKey ();func (_bbd defaultStateHolder )updateState (_cea ,_bgbc ,_fcf string ,_dbf int ,_ecg bool ,_aaf int ,_fdc int ,_bcb _ag .Time ,_ebbd map[string ]int ,_gcbb ...interface{})error {_ddfc ,_eea :=_def ();if _eea !=nil {return _eea ;
};if len (_cea )< 20{return _fg .New ("i\u006e\u0076\u0061\u006c\u0069\u0064\u0020\u006b\u0065\u0079");};_dg :=[]byte (_cea );_dcef :=_fac .Sum512_256 (_dg [:20]);_bad :=_cf .EncodeToString (_dcef [:]);_facbf :=_fa .Join (_ddfc ,_bad );var _feb reportState ;
_feb .Docs =int64 (_dbf );_feb .NumErrors =int64 (_fdc );_feb .LimitDocs =_ecg ;_feb .RemainingDocs =int64 (_aaf );_feb .LastWritten =_ag .Now ().UTC ();_feb .LastReported =_bcb ;_feb .Instance =_bgbc ;_feb .Next =_fcf ;_feb .Usage =_ebbd ;_feb .UsageLogs =_gcbb ;
_bbf ,_eea :=_eeg .Marshal (_feb );if _eea !=nil {return _eea ;};const _eee ="\u0068\u00619\u004e\u004b\u0038]\u0052\u0062\u004c\u002a\u006d\u0034\u004c\u004b\u0057";_bbf ,_eea =_ffg ([]byte (_eee ),_bbf );if _eea !=nil {return _eea ;};_eea =_e .WriteFile (_facbf ,_bbf ,0600);
if _eea !=nil {return _eea ;};return nil ;};func (_ac *LicenseKey )IsLicensed ()bool {return true };type meteredUsageCheckinResp struct{Instance string `json:"inst"`;Next string `json:"next"`;Success bool `json:"success"`;
Message string `json:"message"`;RemainingDocs int `json:"rd"`;LimitDocs bool `json:"ld"`;};func _fbf (_cfb []byte )(_b .Reader ,error ){_cdgg :=new (_ec .Buffer );_cadb :=_a .NewWriter (_cdgg );_cadb .Write (_cfb );_gabg :=_cadb .Close ();if _gabg !=nil {return nil ,_gabg ;
};return _cdgg ,nil ;};func SetMeteredKey (apiKey string )error {if len (apiKey )==0{_fgd .Log .Error ("\u004d\u0065\u0074\u0065\u0072e\u0064\u0020\u004c\u0069\u0063\u0065\u006e\u0073\u0065\u0020\u0041\u0050\u0049 \u004b\u0065\u0079\u0020\u006d\u0075\u0073\u0074\u0020\u006e\u006f\u0074\u0020\u0062\u0065\u0020\u0065\u006d\u0070\u0074\u0079");
_fgd .Log .Error ("\u002d\u0020\u0047\u0072\u0061\u0062\u0020\u006f\u006e\u0065\u0020\u0069\u006e\u0020\u0074h\u0065\u0020\u0046\u0072\u0065\u0065\u0020\u0054\u0069\u0065\u0072\u0020\u0061t\u0020\u0068\u0074\u0074\u0070\u0073\u003a\u002f\u002f\u0063\u006c\u006fud\u002e\u0075\u006e\u0069\u0064\u006f\u0063\u002e\u0069\u006f");
return _ee .Errorf ("\u006de\u0074\u0065\u0072e\u0064\u0020\u006ci\u0063en\u0073\u0065\u0020\u0061\u0070\u0069\u0020k\u0065\u0079\u0020\u006d\u0075\u0073\u0074\u0020\u006e\u006f\u0074\u0020\u0062\u0065\u0020\u0065\u006d\u0070\u0074\u0079\u003a\u0020\u0063\u0072\u0065\u0061\u0074\u0065 o\u006ee\u0020\u0061\u0074\u0020\u0068\u0074t\u0070\u0073\u003a\u002f\u002fc\u006c\u006f\u0075\u0064\u002e\u0075\u006e\u0069\u0064\u006f\u0063.\u0069\u006f");
};if _gab !=nil &&(_gab ._fbe ||_gab .Tier !=LicenseTierUnlicensed ){_fgd .Log .Error ("\u0045\u0052\u0052\u004f\u0052:\u0020\u0043\u0061\u006e\u006eo\u0074 \u0073\u0065\u0074\u0020\u006c\u0069\u0063\u0065\u006e\u0073\u0065\u0020\u006b\u0065\u0079\u0020\u0074\u0077\u0069c\u0065\u0020\u002d\u0020\u0053\u0068\u006f\u0075\u006c\u0064\u0020\u006a\u0075\u0073\u0074\u0020\u0069\u006e\u0069\u0074\u0069\u0061\u006c\u0069z\u0065\u0020\u006f\u006e\u0063\u0065");
return _fg .New ("\u006c\u0069\u0063en\u0073\u0065\u0020\u006b\u0065\u0079\u0020\u0061\u006c\u0072\u0065\u0061\u0064\u0079\u0020\u0073\u0065\u0074");};_edef :=_ede ();_edef ._gda =apiKey ;_acc ,_age :=_edef .getStatus ();if _age !=nil {return _age ;};if !_acc .Valid {return _fg .New ("\u006b\u0065\u0079\u0020\u006e\u006f\u0074\u0020\u0076\u0061\u006c\u0069\u0064");
};_fbg :=&LicenseKey {_fbe :true ,_adc :apiKey ,_gff :true };_gab =_fbg ;return nil ;};func _ede ()*meteredClient {_cb :=meteredClient {_ebc :"h\u0074\u0074\u0070\u0073\u003a\u002f/\u0063\u006c\u006f\u0075\u0064\u002e\u0075\u006e\u0069d\u006f\u0063\u002ei\u006f/\u0061\u0070\u0069",_abaa :&_da .Client {Timeout :30*_ag .Second }};
if _aea :=_e .Getenv ("\u0055N\u0049\u0044\u004f\u0043_\u004c\u0049\u0043\u0045\u004eS\u0045_\u0053E\u0052\u0056\u0045\u0052\u005f\u0055\u0052L");_ecb .HasPrefix (_aea ,"\u0068\u0074\u0074\u0070"){_cb ._ebc =_aea ;};return &_cb ;};func _afgg (_ceeec ,_caee string )string {_agag :=[]byte (_ceeec );
_ccb :=_g .New (_ed .New ,_agag );_ccb .Write ([]byte (_caee ));return _bag .StdEncoding .EncodeToString (_ccb .Sum (nil ));};func _ffg (_cdad ,_adff []byte )([]byte ,error ){_adg ,_geb :=_gc .NewCipher (_cdad );if _geb !=nil {return nil ,_geb ;};_gea :=make ([]byte ,_gc .BlockSize +len (_adff ));
_cfde :=_gea [:_gc .BlockSize ];if _ ,_cff :=_b .ReadFull (_ecc .Reader ,_cfde );_cff !=nil {return nil ,_cff ;};_abc :=_bg .NewCFBEncrypter (_adg ,_cfde );_abc .XORKeyStream (_gea [_gc .BlockSize :],_adff );_aca :=make ([]byte ,_bag .URLEncoding .EncodedLen (len (_gea )));
_bag .URLEncoding .Encode (_aca ,_gea );return _aca ,nil ;};func (_ebbc *LicenseKey )ToString ()string {if _ebbc ._fbe {return "M\u0065t\u0065\u0072\u0065\u0064\u0020\u0073\u0075\u0062s\u0063\u0072\u0069\u0070ti\u006f\u006e";};_bgde :=_ee .Sprintf ("\u004ci\u0063e\u006e\u0073\u0065\u0020\u0049\u0064\u003a\u0020\u0025\u0073\u000a",_ebbc .LicenseId );
_bgde +=_ee .Sprintf ("\u0043\u0075s\u0074\u006f\u006de\u0072\u0020\u0049\u0064\u003a\u0020\u0025\u0073\u000a",_ebbc .CustomerId );_bgde +=_ee .Sprintf ("\u0043u\u0073t\u006f\u006d\u0065\u0072\u0020N\u0061\u006de\u003a\u0020\u0025\u0073\u000a",_ebbc .CustomerName );
_bgde +=_ee .Sprintf ("\u0054i\u0065\u0072\u003a\u0020\u0025\u0073\n",_ebbc .Tier );_bgde +=_ee .Sprintf ("\u0043r\u0065a\u0074\u0065\u0064\u0020\u0041\u0074\u003a\u0020\u0025\u0073\u000a",_fgd .UtcTimeFormat (_ebbc .CreatedAt ));if _ebbc .ExpiresAt ==nil {_bgde +="\u0045x\u0070i\u0072\u0065\u0073\u0020\u0041t\u003a\u0020N\u0065\u0076\u0065\u0072\u000a";
}else {_bgde +=_ee .Sprintf ("\u0045x\u0070i\u0072\u0065\u0073\u0020\u0041\u0074\u003a\u0020\u0025\u0073\u000a",_fgd .UtcTimeFormat (*_ebbc .ExpiresAt ));};_bgde +=_ee .Sprintf ("\u0043\u0072\u0065\u0061\u0074\u006f\u0072\u003a\u0020\u0025\u0073\u0020<\u0025\u0073\u003e\u000a",_ebbc .CreatorName ,_ebbc .CreatorEmail );
return _bgde ;};const _befe ="\u000a\u002d\u002d\u002d\u002d\u002d\u0042\u0045\u0047\u0049\u004e \u0050\u0055\u0042\u004c\u0049\u0043\u0020\u004b\u0045Y\u002d\u002d\u002d\u002d\u002d\u000a\u004d\u0049I\u0042\u0049\u006a\u0041NB\u0067\u006b\u0071\u0068\u006b\u0069G\u0039\u0077\u0030\u0042\u0041\u0051\u0045\u0046A\u0041\u004f\u0043\u0041\u0051\u0038\u0041\u004d\u0049\u0049\u0042\u0043\u0067\u004b\u0043\u0041\u0051\u0045A\u006dF\u0055\u0069\u0079\u0064\u0037\u0062\u0035\u0058\u006a\u0070\u006b\u0050\u0035\u0052\u0061\u0070\u0034\u0077\u000a\u0044\u0063\u0031d\u0079\u007a\u0049\u0051\u0034\u004c\u0065\u006b\u0078\u0072\u0076\u0079\u0074\u006e\u0045\u004d\u0070\u004e\u0055\u0062\u006f\u0036i\u0041\u0037\u0034\u0056\u0038\u0072\u0075\u005a\u004f\u0076\u0072\u0053\u0063\u0073\u0066\u0032\u0051\u0065\u004e9\u002f\u0071r\u0055\u0047\u0038\u0071\u0045\u0062\u0055\u0057\u0064\u006f\u0045\u0059\u0071+\u000a\u006f\u0074\u0046\u004e\u0041\u0046N\u0078\u006c\u0047\u0062\u0078\u0062\u0044\u0048\u0063\u0064\u0047\u0056\u0061\u004d\u0030\u004f\u0058\u0064\u0058g\u0044y\u004c5\u0061\u0049\u0045\u0061\u0067\u004c\u0030\u0063\u0035\u0070\u0077\u006a\u0049\u0064\u0050G\u0049\u006e\u0034\u0036\u0066\u0037\u0038\u0065\u004d\u004a\u002b\u004a\u006b\u0064\u0063\u0070\u0044\n\u0044\u004a\u0061\u0071\u0059\u0058d\u0072\u007a5\u004b\u0065\u0073\u0068\u006aS\u0069\u0049\u0061\u0061\u0037\u006d\u0065\u006e\u0042\u0049\u0041\u0058\u0053\u0034\u0055\u0046\u0078N\u0066H\u0068\u004e\u0030\u0048\u0043\u0059\u005a\u0059\u0071\u0051\u0047\u0037\u0062K+\u0073\u0035\u0072R\u0048\u006f\u006e\u0079\u0064\u004eW\u0045\u0047\u000a\u0048\u0038M\u0079\u0076\u00722\u0070\u0079\u0061\u0032K\u0072\u004d\u0075m\u0066\u006d\u0041\u0078\u0055\u0042\u0036\u0066\u0065\u006e\u0043\u002f4\u004f\u0030\u0057\u00728\u0067\u0066\u0050\u004f\u0055\u0038R\u0069\u0074\u006d\u0062\u0044\u0076\u0051\u0050\u0049\u0052\u0058\u004fL\u0034\u0076\u0054B\u0072\u0042\u0064\u0062a\u0041\u000a9\u006e\u0077\u004e\u0050\u002b\u0069\u002f\u002f\u0032\u0030\u004d\u00542\u0062\u0078\u006d\u0065\u0057\u0042\u002b\u0067\u0070\u0063\u0045\u0068G\u0070\u0058\u005a7\u0033\u0033\u0061\u007a\u0051\u0078\u0072\u0043\u0033\u004a\u0034\u0076\u0033C\u005a\u006d\u0045\u004eS\u0074\u0044\u004b\u002f\u004b\u0044\u0053\u0050\u004b\u0055\u0047\u0066\u00756\u000a\u0066\u0077I\u0044\u0041\u0051\u0041\u0042\u000a\u002d\u002d\u002d\u002d\u002dE\u004e\u0044\u0020\u0050\u0055\u0042\u004c\u0049\u0043 \u004b\u0045Y\u002d\u002d\u002d\u002d\u002d\n";
func (_edee *meteredClient )checkinUsage (_dee meteredUsageCheckinForm )(meteredUsageCheckinResp ,error ){_dee .Package ="\u0075\u006e\u0069\u0070\u0064\u0066";_dee .PackageVersion =_fgd .Version ;var _ffe meteredUsageCheckinResp ;_faad :=_edee ._ebc +"\u002f\u006d\u0065\u0074er\u0065\u0064\u002f\u0075\u0073\u0061\u0067\u0065\u005f\u0063\u0068\u0065\u0063\u006bi\u006e";
_ebe ,_bc :=_eeg .Marshal (_dee );if _bc !=nil {return _ffe ,_bc ;};_afd ,_bc :=_fbf (_ebe );if _bc !=nil {return _ffe ,_bc ;};_aa ,_bc :=_da .NewRequest ("\u0050\u004f\u0053\u0054",_faad ,_afd );if _bc !=nil {return _ffe ,_bc ;};_aa .Header .Add ("\u0043\u006f\u006et\u0065\u006e\u0074\u002d\u0054\u0079\u0070\u0065","\u0061\u0070p\u006c\u0069\u0063a\u0074\u0069\u006f\u006e\u002f\u006a\u0073\u006f\u006e");
_aa .Header .Add ("\u0043\u006fn\u0074\u0065\u006et\u002d\u0045\u006e\u0063\u006f\u0064\u0069\u006e\u0067","\u0067\u007a\u0069\u0070");_aa .Header .Add ("\u0041c\u0063e\u0070\u0074\u002d\u0045\u006e\u0063\u006f\u0064\u0069\u006e\u0067","\u0067\u007a\u0069\u0070");
_aa .Header .Add ("\u0058-\u0041\u0050\u0049\u002d\u004b\u0045Y",_edee ._gda );_ceee ,_bc :=_edee ._abaa .Do (_aa );if _bc !=nil {return _ffe ,_bc ;};defer _ceee .Body .Close ();if _ceee .StatusCode !=200{_eec ,_ddf :=_eggc (_ceee );if _ddf !=nil {return _ffe ,_ddf ;
};_ddf =_eeg .Unmarshal (_eec ,&_ffe );if _ddf !=nil {return _ffe ,_ddf ;};return _ffe ,_ee .Errorf ("\u0066\u0061i\u006c\u0065\u0064\u0020t\u006f\u0020c\u0068\u0065\u0063\u006b\u0069\u006e\u002c\u0020s\u0074\u0061\u0074\u0075\u0073\u0020\u0063\u006f\u0064\u0065\u0020\u0069s\u003a\u0020\u0025\u0064",_ceee .StatusCode );
};_dag :=_ceee .Header .Get ("\u0058\u002d\u0055\u0043\u002d\u0053\u0069\u0067\u006ea\u0074\u0075\u0072\u0065");_dda :=_afgg (_dee .MacAddress ,string (_ebe ));if _dda !=_dag {_fgd .Log .Error ("I\u006e\u0076\u0061l\u0069\u0064\u0020\u0072\u0065\u0073\u0070\u006f\u006e\u0073\u0065\u0020\u0073\u0069\u0067\u006e\u0061\u0074\u0075\u0072\u0065\u002c\u0020\u0073\u0065t\u0020\u0074\u0068e\u0020\u006c\u0069\u0063\u0065\u006e\u0073\u0065\u0020\u0073\u0065\u0072\u0076e\u0072\u0020\u0074\u006f \u0068\u0074\u0074\u0070s\u003a\u002f\u002f\u0063\u006c\u006f\u0075\u0064\u002e\u0075\u006e\u0069\u0064\u006f\u0063\u002e\u0069o\u002f\u0061\u0070\u0069");
return _ffe ,_fg .New ("\u0066\u0061\u0069l\u0065\u0064\u0020\u0074\u006f\u0020\u0063\u0068\u0065\u0063\u006b\u0069\u006e\u002c\u0020\u0069\u006e\u0076\u0061\u006c\u0069\u0064\u0020\u0073\u0065\u0072\u0076\u0065\u0072 \u0072\u0065\u0073\u0070\u006f\u006e\u0073\u0065");
};_eecg ,_bc :=_eggc (_ceee );if _bc !=nil {return _ffe ,_bc ;};_bc =_eeg .Unmarshal (_eecg ,&_ffe );if _bc !=nil {return _ffe ,_bc ;};return _ffe ,nil ;};func SetMeteredKeyPersistentCache (val bool ){_gab ._gff =val };func _baa ()string {_ceb :=_e .Getenv ("\u0048\u004f\u004d\u0045");
if len (_ceb )==0{_ceb ,_ =_e .UserHomeDir ();};return _ceb ;};type meteredStatusForm struct{};var _cfd []interface{};func _beg ()(_fd .IP ,error ){_bed ,_ddb :=_fd .Dial ("\u0075\u0064\u0070","\u0038\u002e\u0038\u002e\u0038\u002e\u0038\u003a\u0038\u0030");
if _ddb !=nil {return nil ,_ddb ;};defer _bed .Close ();_ecbd :=_bed .LocalAddr ().(*_fd .UDPAddr );return _ecbd .IP ,nil ;};var _bga stateLoader =defaultStateHolder {};func _aab (_ebcg string ,_cge string ,_gbc string ,_abgc bool )error {if _gab ==nil {return _fg .New ("\u006e\u006f\u0020\u006c\u0069\u0063\u0065\u006e\u0073e\u0020\u006b\u0065\u0079");
};if !_gab ._fbe ||len (_gab ._adc )==0{return nil ;};if len (_ebcg )==0&&!_abgc {return _fg .New ("\u0064\u006f\u0063\u004b\u0065\u0079\u0020\u006e\u006ft\u0020\u0073\u0065\u0074");};_gfcf .Lock ();defer _gfcf .Unlock ();if _gga ==nil {_gga =map[string ]struct{}{};
};if _egf ==nil {_egf =map[string ]int {};};_afc :=0;if len (_ebcg )> 0{_ ,_afcb :=_gga [_ebcg ];if !_afcb {_gga [_ebcg ]=struct{}{};_afc ++;};if _gab ._eg {_cfd =append (_cfd ,map[string ]interface{}{"\u0074\u0069\u006d\u0065":_ag .Now ().String (),"\u0066\u0075\u006e\u0063":_cge ,"\u0072\u0065\u0066":_ebcg [:8],"\u0066\u0069\u006c\u0065":_gbc ,"\u0063\u006f\u0073\u0074":_afc });
if _afcb &&_afc ==0{_fgd .Log .Info ("\u0025\u0073\u0020\u0052\u0065\u0066\u003a\u0020\u0025\u0073\u0020\u007c\u0020\u0025\u0073 \u007c \u004e\u006f\u0020\u0063\u0072\u0065\u0064\u0069\u0074\u0020\u0075\u0073\u0065\u0064",_ag .Now ().String (),_ebcg [:8],_cge );
};};};if _afc ==0&&!_abgc {return nil ;};_egf [_cge ]++;_acg :=_ag .Now ();_efg ,_aeaa :=_bga .loadState (_gab ._adc );if _aeaa !=nil {_fgd .Log .Debug ("\u0045R\u0052\u004f\u0052\u003a\u0020\u0025v",_aeaa );return _aeaa ;};_efg .UsageLogs =append (_efg .UsageLogs ,_cfd ...);
if _efg .Usage ==nil {_efg .Usage =map[string ]int {};};for _agef ,_gac :=range _egf {if _agef !=""{_efg .Usage [_agef ]+=_gac ;};};_egf =nil ;const _eabb =24*_ag .Hour ;const _daf =3*24*_ag .Hour ;if len (_efg .Instance )==0||_acg .Sub (_efg .LastReported )> _eabb ||(_efg .LimitDocs &&_efg .RemainingDocs <=_efg .Docs +int64 (_afc ))||_abgc {_ccd ,_ge :=_e .Hostname ();
if _ge !=nil {return _ge ;};_befc :=_efg .Docs ;_edd ,_egg ,_ge :=_eace ();if _ge !=nil {_fgd .Log .Debug ("\u0055\u006e\u0061b\u006c\u0065\u0020\u0074o\u0020\u0067\u0065\u0074\u0020\u006c\u006fc\u0061\u006c\u0020\u0061\u0064\u0064\u0072\u0065\u0073\u0073\u003a\u0020\u0025\u0073",_ge .Error ());
_edd =append (_edd ,"\u0069n\u0066\u006f\u0072\u006da\u0074\u0069\u006f\u006e\u0020n\u006ft\u0020a\u0076\u0061\u0069\u006c\u0061\u0062\u006ce");_egg =append (_egg ,"\u0069n\u0066\u006f\u0072\u006da\u0074\u0069\u006f\u006e\u0020n\u006ft\u0020a\u0076\u0061\u0069\u006c\u0061\u0062\u006ce");
}else {_d .Strings (_egg );_d .Strings (_edd );_caa ,_cbf :=_beg ();if _cbf !=nil {return _cbf ;};_cce :=false ;for _ ,_bebe :=range _egg {if _bebe ==_caa .String (){_cce =true ;};};if !_cce {_egg =append (_egg ,_caa .String ());};};_ecf :=_ede ();_ecf ._gda =_gab ._adc ;
_befc +=int64 (_afc );_ecbb :=meteredUsageCheckinForm {Instance :_efg .Instance ,Next :_efg .Next ,UsageNumber :int (_befc ),NumFailed :_efg .NumErrors ,Hostname :_ccd ,LocalIP :_ecb .Join (_egg ,"\u002c\u0020"),MacAddress :_ecb .Join (_edd ,"\u002c\u0020"),Package :"\u0075\u006e\u0069\u0070\u0064\u0066",PackageVersion :_fgd .Version ,Usage :_efg .Usage ,IsPersistentCache :_gab ._gff ,Timestamp :_acg .Unix ()};
if len (_edd )==0{_ecbb .MacAddress ="\u006e\u006f\u006e\u0065";};if _gab ._eg {_ecbb .UsageLogs =_efg .UsageLogs ;};_eegc :=int64 (0);_bae :=_efg .NumErrors ;_afg :=_acg ;_cae :=0;_dfcg :=_efg .LimitDocs ;_eda ,_ge :=_ecf .checkinUsage (_ecbb );if _ge !=nil {if _acg .Sub (_efg .LastReported )> _daf {if !_eda .Success {return _fg .New (_eda .Message );
};return _fg .New ("\u0074\u006f\u006f\u0020\u006c\u006f\u006e\u0067\u0020\u0073\u0069\u006e\u0063\u0065\u0020\u006c\u0061\u0073\u0074\u0020\u0073\u0075\u0063\u0063e\u0073\u0073\u0066\u0075\u006c \u0063\u0068e\u0063\u006b\u0069\u006e");};_eegc =_befc ;
_bae ++;_afg =_efg .LastReported ;}else {_dfcg =_eda .LimitDocs ;_cae =_eda .RemainingDocs ;_bae =0;};if len (_eda .Instance )==0{_eda .Instance =_ecbb .Instance ;};if len (_eda .Next )==0{_eda .Next =_ecbb .Next ;};_ge =_bga .updateState (_ecf ._gda ,_eda .Instance ,_eda .Next ,int (_eegc ),_dfcg ,_cae ,int (_bae ),_afg ,nil );
if _ge !=nil {return _ge ;};if !_eda .Success {return _ee .Errorf ("\u0065r\u0072\u006f\u0072\u003a\u0020\u0025s",_eda .Message );};}else {_aeaa =_bga .updateState (_gab ._adc ,_efg .Instance ,_efg .Next ,int (_efg .Docs )+_afc ,_efg .LimitDocs ,int (_efg .RemainingDocs ),int (_efg .NumErrors ),_efg .LastReported ,_efg .Usage ,_efg .UsageLogs ...);
if _aeaa !=nil {return _aeaa ;};};if _gab ._eg &&len (_ebcg )> 0{_dbc :="";if _gbc !=""{_dbc =_ee .Sprintf ("\u0046i\u006c\u0065\u0020\u0025\u0073\u0020|",_gbc );};_fgd .Log .Info ("%\u0073\u0020\u007c\u0020\u0025\u0073\u0020\u0052\u0065\u0066\u003a\u0020\u0025\u0073\u0020\u007c\u0020\u0025s\u0020\u007c\u0020\u0025\u0064\u0020\u0063\u0072\u0065\u0064it\u0028\u0073\u0029 \u0075s\u0065\u0064",_acg .String (),_dbc ,_ebcg [:8],_cge ,_afc );
};return nil ;};const _fdd ="\u0055N\u0049D\u004f\u0043\u005f\u004c\u0049C\u0045\u004eS\u0045\u005f\u0044\u0049\u0052";func _def ()(string ,error ){_decf :=_ecb .TrimSpace (_e .Getenv (_fdd ));if _decf ==""{_fgd .Log .Debug ("\u0024\u0025\u0073\u0020e\u006e\u0076\u0069\u0072\u006f\u006e\u006d\u0065\u006e\u0074\u0020\u0076\u0061\u0072\u0069\u0061\u0062l\u0065\u0020\u006e\u006f\u0074\u0020\u0066\u006f\u0075\u006e\u0064\u002e\u0020\u0057\u0069\u006c\u006c\u0020\u0075\u0073\u0065\u0020\u0068\u006f\u006d\u0065\u0020\u0064\u0069\u0072\u0065\u0063\u0074\u006f\u0072\u0079\u0020\u0074\u006f\u0020s\u0074\u006f\u0072\u0065\u0020\u006c\u0069\u0063\u0065\u006e\u0073\u0065\u0020in\u0066o\u0072\u006d\u0061\u0074\u0069\u006f\u006e\u002e",_fdd );
_dfd :=_baa ();if len (_dfd )==0{return "",_ee .Errorf ("r\u0065\u0071\u0075\u0069\u0072\u0065\u0064\u0020\u0024\u0025\u0073\u0020\u0065\u006e\u0076\u0069\u0072\u006f\u006e\u006d\u0065\u006e\u0074\u0020\u0076\u0061r\u0069a\u0062\u006c\u0065\u0020o\u0072\u0020h\u006f\u006d\u0065\u0020\u0064\u0069\u0072\u0065\u0063\u0074\u006f\u0072\u0079\u0020\u006e\u006f\u0074\u0020\u0066\u006f\u0075\u006e\u0064",_fdd );
};_decf =_fa .Join (_dfd ,"\u002eu\u006e\u0069\u0064\u006f\u0063");};_dcd :=_e .MkdirAll (_decf ,0777);if _dcd !=nil {return "",_dcd ;};return _decf ,nil ;};func _eggc (_fbdc *_da .Response )([]byte ,error ){var _fge []byte ;_fgbb ,_fcc :=_gbf (_fbdc );
if _fcc !=nil {return _fge ,_fcc ;};return _b .ReadAll (_fgbb );};func init (){_dgf :=_e .Getenv (_aefa );_ded :=_e .Getenv (_bcc );if len (_dgf )==0||len (_ded )==0{return ;};_ada ,_gbb :=_e .ReadFile (_dgf );if _gbb !=nil {_fgd .Log .Error ("\u0055\u006eab\u006c\u0065\u0020t\u006f\u0020\u0072\u0065ad \u006cic\u0065\u006e\u0073\u0065\u0020\u0063\u006fde\u0020\u0066\u0069\u006c\u0065\u003a\u0020%\u0076",_gbb );
return ;};_gbb =SetLicenseKey (string (_ada ),_ded );if _gbb !=nil {_fgd .Log .Error ("\u0055\u006e\u0061b\u006c\u0065\u0020\u0074o\u0020\u006c\u006f\u0061\u0064\u0020\u006ci\u0063\u0065\u006e\u0073\u0065\u0020\u0063\u006f\u0064\u0065\u003a\u0020\u0025\u0076",_gbb );
return ;};};const (_ce ="\u002d\u002d\u002d--\u0042\u0045\u0047\u0049\u004e\u0020\u0055\u004e\u0049D\u004fC\u0020L\u0049C\u0045\u004e\u0053\u0045\u0020\u004b\u0045\u0059\u002d\u002d\u002d\u002d\u002d";_fae ="\u002d\u002d\u002d\u002d\u002d\u0045\u004e\u0044\u0020\u0055\u004e\u0049\u0044\u004f\u0043 \u004cI\u0043\u0045\u004e\u0053\u0045\u0020\u004b\u0045\u0059\u002d\u002d\u002d\u002d\u002d";
);var _bf =_ag .Date (2020,1,1,0,0,0,0,_ag .UTC );func MakeUnlicensedKey ()*LicenseKey {_dfc :=LicenseKey {};_dfc .CustomerName ="\u0055\u006e\u006c\u0069\u0063\u0065\u006e\u0073\u0065\u0064";_dfc .Tier =LicenseTierUnlicensed ;_dfc .CreatedAt =_ag .Now ().UTC ();
_dfc .CreatedAtInt =_dfc .CreatedAt .Unix ();return &_dfc ;};type meteredUsageCheckinForm struct{Instance string `json:"inst"`;Next string `json:"next"`;UsageNumber int `json:"usage_number"`;NumFailed int64 `json:"num_failed"`;Hostname string `json:"hostname"`;
LocalIP string `json:"local_ip"`;MacAddress string `json:"mac_address"`;Package string `json:"package"`;PackageVersion string `json:"package_version"`;Usage map[string ]int `json:"u"`;IsPersistentCache bool `json:"is_persistent_cache"`;Timestamp int64 `json:"timestamp"`;
UsageLogs []interface{}`json:"ul,omitempty"`;};func _bfe (_cgg ,_ggaf []byte )([]byte ,error ){_cde :=make ([]byte ,_bag .URLEncoding .DecodedLen (len (_ggaf )));_feg ,_dfeb :=_bag .URLEncoding .Decode (_cde ,_ggaf );if _dfeb !=nil {return nil ,_dfeb ;
};_cde =_cde [:_feg ];_fad ,_dfeb :=_gc .NewCipher (_cgg );if _dfeb !=nil {return nil ,_dfeb ;};if len (_cde )< _gc .BlockSize {return nil ,_fg .New ("c\u0069p\u0068\u0065\u0072\u0074\u0065\u0078\u0074\u0020t\u006f\u006f\u0020\u0073ho\u0072\u0074");};_gfcg :=_cde [:_gc .BlockSize ];
_cde =_cde [_gc .BlockSize :];_dcf :=_bg .NewCFBDecrypter (_fad ,_gfcg );_dcf .XORKeyStream (_cde ,_cde );return _cde ,nil ;};const _bcc ="U\u004eI\u0050\u0044\u0046\u005f\u0043\u0055\u0053\u0054O\u004d\u0045\u0052\u005fNA\u004d\u0045";func TrackUse (useKey string ){if _gab ==nil {return ;
};if !_gab ._fbe ||len (_gab ._adc )==0{return ;};if len (useKey )==0{return ;};_gfcf .Lock ();defer _gfcf .Unlock ();if _egf ==nil {_egf =map[string ]int {};};_egf [useKey ]++;};func GetLicenseKey ()*LicenseKey {if _gab ==nil {return nil ;};_fcfb :=*_gab ;
return &_fcfb ;};type meteredStatusResp struct{Valid bool `json:"valid"`;OrgCredits int64 `json:"org_credits"`;OrgUsed int64 `json:"org_used"`;OrgRemaining int64 `json:"org_remaining"`;};