//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package endian ;import (_e "encoding/binary";_d "unsafe";);func IsBig ()bool {return _c };func init (){const _ea =int (_d .Sizeof (0));_dc :=1;_ec :=(*[_ea ]byte )(_d .Pointer (&_dc ));if _ec [0]==0{_c =true ;ByteOrder =_e .<PERSON>ndian ;}else {ByteOrder =_e .LittleEndian ;
};};func IsLittle ()bool {return !_c };var (ByteOrder _e .ByteOrder ;_c bool ;);