//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package basic ;import _g "github.com/unidoc/unipdf/v3/internal/jbig2/errors";func (_fd *NumSlice )AddInt (v int ){*_fd =append (*_fd ,float32 (v ))};type NumSlice []float32 ;func (_fca NumSlice )Get (i int )(float32 ,error ){if i < 0||i > len (_fca )-1{return 0,_g .Errorf ("\u004e\u0075\u006dS\u006c\u0069\u0063\u0065\u002e\u0047\u0065\u0074","\u0069n\u0064\u0065\u0078\u003a\u0020\u0027\u0025\u0064\u0027\u0020\u006fu\u0074\u0020\u006f\u0066\u0020\u0072\u0061\u006e\u0067\u0065",i );
};return _fca [i ],nil ;};func (_aac IntSlice )Get (index int )(int ,error ){if index > len (_aac )-1{return 0,_g .Errorf ("\u0049\u006e\u0074S\u006c\u0069\u0063\u0065\u002e\u0047\u0065\u0074","\u0069\u006e\u0064\u0065x:\u0020\u0025\u0064\u0020\u006f\u0075\u0074\u0020\u006f\u0066\u0020\u0072\u0061\u006eg\u0065",index );
};return _aac [index ],nil ;};type Stack struct{Data []interface{};Aux *Stack ;};func Ceil (numerator ,denominator int )int {if numerator %denominator ==0{return numerator /denominator ;};return (numerator /denominator )+1;};func (_fg *Stack )top ()int {return len (_fg .Data )-1};
func (_a IntsMap )Add (key uint64 ,value int ){_a [key ]=append (_a [key ],value )};func (_aa *IntSlice )Add (v int )error {if _aa ==nil {return _g .Error ("\u0049\u006e\u0074S\u006c\u0069\u0063\u0065\u002e\u0041\u0064\u0064","\u0073\u006c\u0069\u0063\u0065\u0020\u006e\u006f\u0074\u0020\u0064\u0065f\u0069\u006e\u0065\u0064");
};*_aa =append (*_aa ,v );return nil ;};func Max (x ,y int )int {if x > y {return x ;};return y ;};func (_fc IntsMap )GetSlice (key uint64 )([]int ,bool ){_ec ,_ea :=_fc [key ];if !_ea {return nil ,false ;};return _ec ,true ;};func (_ed *Stack )Pop ()(_dd interface{},_gb bool ){_dd ,_gb =_ed .peek ();
if !_gb {return nil ,_gb ;};_ed .Data =_ed .Data [:_ed .top ()];return _dd ,true ;};type IntsMap map[uint64 ][]int ;func (_d *NumSlice )Add (v float32 ){*_d =append (*_d ,v )};func (_fe *Stack )Push (v interface{}){_fe .Data =append (_fe .Data ,v )};func (_ee NumSlice )GetIntSlice ()[]int {_ecd :=make ([]int ,len (_ee ));
for _ba ,_fb :=range _ee {_ecd [_ba ]=int (_fb );};return _ecd ;};func NewIntSlice (i int )*IntSlice {_gg :=IntSlice (make ([]int ,i ));return &_gg };func (_bb NumSlice )GetInt (i int )(int ,error ){const _fdc ="\u0047\u0065\u0074\u0049\u006e\u0074";if i < 0||i > len (_bb )-1{return 0,_g .Errorf (_fdc ,"\u0069n\u0064\u0065\u0078\u003a\u0020\u0027\u0025\u0064\u0027\u0020\u006fu\u0074\u0020\u006f\u0066\u0020\u0072\u0061\u006e\u0067\u0065",i );
};_aae :=_bb [i ];return int (_aae +Sign (_aae )*0.5),nil ;};func NewNumSlice (i int )*NumSlice {_ad :=NumSlice (make ([]float32 ,i ));return &_ad };func (_bf *Stack )Len ()int {return len (_bf .Data )};func Sign (v float32 )float32 {if v >=0.0{return 1.0;
};return -1.0;};func (_ece *IntSlice )Copy ()*IntSlice {_bd :=IntSlice (make ([]int ,len (*_ece )));copy (_bd ,*_ece );return &_bd ;};func (_aaef *Stack )peek ()(interface{},bool ){_dg :=_aaef .top ();if _dg ==-1{return nil ,false ;};return _aaef .Data [_dg ],true ;
};func (_f IntsMap )Get (key uint64 )(int ,bool ){_e ,_b :=_f [key ];if !_b {return 0,false ;};if len (_e )==0{return 0,false ;};return _e [0],true ;};func Min (x ,y int )int {if x < y {return x ;};return y ;};func Abs (v int )int {if v > 0{return v ;};
return -v ;};func (_gf IntsMap )Delete (key uint64 ){delete (_gf ,key )};func (_eaf IntSlice )Size ()int {return len (_eaf )};type IntSlice []int ;func (_gc *Stack )Peek ()(_fa interface{},_bg bool ){return _gc .peek ()};