//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package errors ;import (_b "fmt";_e "golang.org/x/xerrors";);type processError struct{_a string ;_c string ;_ecc string ;_ce error ;};func (_eg *processError )Error ()string {var _eb string ;if _eg ._a !=""{_eb =_eg ._a ;};_eb +="\u0050r\u006f\u0063\u0065\u0073\u0073\u003a "+_eg ._c ;
if _eg ._ecc !=""{_eb +="\u0020\u004d\u0065\u0073\u0073\u0061\u0067\u0065\u003a\u0020"+_eg ._ecc ;};if _eg ._ce !=nil {_eb +="\u002e\u0020"+_eg ._ce .Error ();};return _eb ;};var _ _e .Wrapper =(*processError )(nil );func Errorf (processName ,message string ,arguments ...interface{})error {return _d (_b .Sprintf (message ,arguments ...),processName );
};func Wrapf (err error ,processName ,message string ,arguments ...interface{})error {if _bee ,_acf :=err .(*processError );_acf {_bee ._a ="";};_bf :=_d (_b .Sprintf (message ,arguments ...),processName );_bf ._ce =err ;return _bf ;};func Error (processName ,message string )error {return _d (message ,processName )};
func (_aa *processError )Unwrap ()error {return _aa ._ce };func _d (_ac ,_be string )*processError {return &processError {_a :"\u005b\u0055\u006e\u0069\u0050\u0044\u0046\u005d",_ecc :_ac ,_c :_be };};func Wrap (err error ,processName ,message string )error {if _ag ,_egg :=err .(*processError );
_egg {_ag ._a ="";};_ed :=_d (message ,processName );_ed ._ce =err ;return _ed ;};