//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package arithmetic ;import (_b "bytes";_gf "github.com/unidoc/unipdf/v3/common";_bd "github.com/unidoc/unipdf/v3/internal/jbig2/bitmap";_c "github.com/unidoc/unipdf/v3/internal/jbig2/errors";_a "io";);func (_cafb *Encoder )codeMPS (_fbd *codingContext ,_gcb uint32 ,_faf uint16 ,_cde byte ){_cafb ._ce -=_faf ;
if _cafb ._ce &0x8000!=0{_cafb ._ef +=uint32 (_faf );return ;};if _cafb ._ce < _faf {_cafb ._ce =_faf ;}else {_cafb ._ef +=uint32 (_faf );};_fbd ._bf [_gcb ]=_bac [_cde ]._efd ;_cafb .renormalize ();};type intEncRangeS struct{_ag ,_gg int ;_bg ,_ba uint8 ;
_e uint16 ;_gge uint8 ;};func (_cdb *Encoder )dataSize ()int {return _efab *len (_cdb ._df )+_cdb ._dbc };func (_afc *Encoder )Flush (){_afc ._dbc =0;_afc ._df =nil ;_afc ._cd =-1};func (_cf Class )String ()string {switch _cf {case IAAI :return "\u0049\u0041\u0041\u0049";
case IADH :return "\u0049\u0041\u0044\u0048";case IADS :return "\u0049\u0041\u0044\u0053";case IADT :return "\u0049\u0041\u0044\u0054";case IADW :return "\u0049\u0041\u0044\u0057";case IAEX :return "\u0049\u0041\u0045\u0058";case IAFS :return "\u0049\u0041\u0046\u0053";
case IAIT :return "\u0049\u0041\u0049\u0054";case IARDH :return "\u0049\u0041\u0052D\u0048";case IARDW :return "\u0049\u0041\u0052D\u0057";case IARDX :return "\u0049\u0041\u0052D\u0058";case IARDY :return "\u0049\u0041\u0052D\u0059";case IARI :return "\u0049\u0041\u0052\u0049";
default:return "\u0055N\u004b\u004e\u004f\u0057\u004e";};};func (_gff *Encoder )emit (){if _gff ._dbc ==_efab {_gff ._df =append (_gff ._df ,_gff ._ca );_gff ._ca =make ([]byte ,_efab );_gff ._dbc =0;};_gff ._ca [_gff ._dbc ]=_gff ._db ;_gff ._dbc ++;};
func (_dc *codingContext )flipMps (_ge uint32 ){_dc ._d [_ge ]=1-_dc ._d [_ge ]};type Class int ;func (_bbb *Encoder )WriteTo (w _a .Writer )(int64 ,error ){const _fcdc ="\u0045n\u0063o\u0064\u0065\u0072\u002e\u0057\u0072\u0069\u0074\u0065\u0054\u006f";
var _gcdg int64 ;for _aca ,_agc :=range _bbb ._df {_gbe ,_bage :=w .Write (_agc );if _bage !=nil {return 0,_c .Wrapf (_bage ,_fcdc ,"\u0066\u0061\u0069\u006c\u0065\u0064\u0020\u0061\u0074\u0020\u0069'\u0074\u0068\u003a\u0020\u0027\u0025\u0064\u0027\u0020\u0063h\u0075\u006e\u006b",_aca );
};_gcdg +=int64 (_gbe );};_bbb ._ca =_bbb ._ca [:_bbb ._dbc ];_aaf ,_cea :=w .Write (_bbb ._ca );if _cea !=nil {return 0,_c .Wrap (_cea ,_fcdc ,"\u0062u\u0066f\u0065\u0072\u0065\u0064\u0020\u0063\u0068\u0075\u006e\u006b\u0073");};_gcdg +=int64 (_aaf );
return _gcdg ,nil ;};type state struct{_faa uint16 ;_efd ,_eba uint8 ;_cg uint8 ;};func (_bgd *Encoder )EncodeBitmap (bm *_bd .Bitmap ,duplicateLineRemoval bool )error {_gf .Log .Trace ("\u0045n\u0063\u006f\u0064\u0065 \u0042\u0069\u0074\u006d\u0061p\u0020[\u0025d\u0078\u0025\u0064\u005d\u002c\u0020\u0025s",bm .Width ,bm .Height ,bm );
var (_aa ,_dbb uint8 ;_cee ,_ed ,_gd uint16 ;_ac ,_ddd ,_gc byte ;_gb ,_ggf ,_fe int ;_gbf ,_af []byte ;);for _bdg :=0;_bdg < bm .Height ;_bdg ++{_ac ,_ddd =0,0;if _bdg >=2{_ac =bm .Data [(_bdg -2)*bm .RowStride ];};if _bdg >=1{_ddd =bm .Data [(_bdg -1)*bm .RowStride ];
if duplicateLineRemoval {_ggf =_bdg *bm .RowStride ;_gbf =bm .Data [_ggf :_ggf +bm .RowStride ];_fe =(_bdg -1)*bm .RowStride ;_af =bm .Data [_fe :_fe +bm .RowStride ];if _b .Equal (_gbf ,_af ){_dbb =_aa ^1;_aa =1;}else {_dbb =_aa ;_aa =0;};};};if duplicateLineRemoval {if _cc :=_bgd .encodeBit (_bgd ._dbg ,_bga ,_dbb );
_cc !=nil {return _cc ;};if _aa !=0{continue ;};};_gc =bm .Data [_bdg *bm .RowStride ];_cee =uint16 (_ac >>5);_ed =uint16 (_ddd >>4);_ac <<=3;_ddd <<=4;_gd =0;for _gb =0;_gb < bm .Width ;_gb ++{_afa :=uint32 (_cee <<11|_ed <<4|_gd );_bag :=(_gc &0x80)>>7;
_gcd :=_bgd .encodeBit (_bgd ._dbg ,_afa ,_bag );if _gcd !=nil {return _gcd ;};_cee <<=1;_ed <<=1;_gd <<=1;_cee |=uint16 ((_ac &0x80)>>7);_ed |=uint16 ((_ddd &0x80)>>7);_gd |=uint16 (_bag );_baa :=_gb %8;_bae :=_gb /8+1;if _baa ==4&&_bdg >=2{_ac =0;if _bae < bm .RowStride {_ac =bm .Data [(_bdg -2)*bm .RowStride +_bae ];
};}else {_ac <<=1;};if _baa ==3&&_bdg >=1{_ddd =0;if _bae < bm .RowStride {_ddd =bm .Data [(_bdg -1)*bm .RowStride +_bae ];};}else {_ddd <<=1;};if _baa ==7{_gc =0;if _bae < bm .RowStride {_gc =bm .Data [_bdg *bm .RowStride +_bae ];};}else {_gc <<=1;};_cee &=31;
_ed &=127;_gd &=15;};};return nil ;};func (_dfc *Encoder )lBlock (){if _dfc ._cd >=0{_dfc .emit ();};_dfc ._cd ++;_dfc ._db =uint8 (_dfc ._ef >>19);_dfc ._ef &=0x7ffff;_dfc ._gga =8;};const (_eg =65536;_efab =20*1024;);func (_ggb *Encoder )Refine (iTemp ,iTarget *_bd .Bitmap ,ox ,oy int )error {for _bbe :=0;
_bbe < iTarget .Height ;_bbe ++{var _fb int ;_bde :=_bbe +oy ;var (_dca ,_fa ,_fc ,_gec ,_fcd uint16 ;_bgc ,_ae ,_ga ,_afd ,_bbd byte ;);if _bde >=1&&(_bde -1)< iTemp .Height {_bgc =iTemp .Data [(_bde -1)*iTemp .RowStride ];};if _bde >=0&&_bde < iTemp .Height {_ae =iTemp .Data [_bde *iTemp .RowStride ];
};if _bde >=-1&&_bde +1< iTemp .Height {_ga =iTemp .Data [(_bde +1)*iTemp .RowStride ];};if _bbe >=1{_afd =iTarget .Data [(_bbe -1)*iTarget .RowStride ];};_bbd =iTarget .Data [_bbe *iTarget .RowStride ];_gea :=uint (6+ox );_dca =uint16 (_bgc >>_gea );_fa =uint16 (_ae >>_gea );
_fc =uint16 (_ga >>_gea );_gec =uint16 (_afd >>6);_fec :=uint (2-ox );_bgc <<=_fec ;_ae <<=_fec ;_ga <<=_fec ;_afd <<=2;for _fb =0;_fb < iTarget .Width ;_fb ++{_aad :=(_dca <<10)|(_fa <<7)|(_fc <<4)|(_gec <<1)|_fcd ;_fd :=_bbd >>7;_dcb :=_ggb .encodeBit (_ggb ._dbg ,uint32 (_aad ),_fd );
if _dcb !=nil {return _dcb ;};_dca <<=1;_fa <<=1;_fc <<=1;_gec <<=1;_dca |=uint16 (_bgc >>7);_fa |=uint16 (_ae >>7);_fc |=uint16 (_ga >>7);_gec |=uint16 (_afd >>7);_fcd =uint16 (_fd );_caf :=_fb %8;_ccc :=_fb /8+1;if _caf ==5+ox {_bgc ,_ae ,_ga =0,0,0;
if _ccc < iTemp .RowStride &&_bde >=1&&(_bde -1)< iTemp .Height {_bgc =iTemp .Data [(_bde -1)*iTemp .RowStride +_ccc ];};if _ccc < iTemp .RowStride &&_bde >=0&&_bde < iTemp .Height {_ae =iTemp .Data [_bde *iTemp .RowStride +_ccc ];};if _ccc < iTemp .RowStride &&_bde >=-1&&(_bde +1)< iTemp .Height {_ga =iTemp .Data [(_bde +1)*iTemp .RowStride +_ccc ];
};}else {_bgc <<=1;_ae <<=1;_ga <<=1;};if _caf ==5&&_bbe >=1{_afd =0;if _ccc < iTarget .RowStride {_afd =iTarget .Data [(_bbe -1)*iTarget .RowStride +_ccc ];};}else {_afd <<=1;};if _caf ==7{_bbd =0;if _ccc < iTarget .RowStride {_bbd =iTarget .Data [_bbe *iTarget .RowStride +_ccc ];
};}else {_bbd <<=1;};_dca &=7;_fa &=7;_fc &=7;_gec &=7;};};return nil ;};func (_ffg *Encoder )rBlock (){if _ffg ._cd >=0{_ffg .emit ();};_ffg ._cd ++;_ffg ._db =uint8 (_ffg ._ef >>20);_ffg ._ef &=0xfffff;_ffg ._gga =7;};func (_feg *Encoder )flush (){_feg .setBits ();
_feg ._ef <<=_feg ._gga ;_feg .byteOut ();_feg ._ef <<=_feg ._gga ;_feg .byteOut ();_feg .emit ();if _feg ._db !=0xff{_feg ._cd ++;_feg ._db =0xff;_feg .emit ();};_feg ._cd ++;_feg ._db =0xac;_feg ._cd ++;_feg .emit ();};const _bga =0x9b25;var _bac =[]state {{0x5601,1,1,1},{0x3401,2,6,0},{0x1801,3,9,0},{0x0AC1,4,12,0},{0x0521,5,29,0},{0x0221,38,33,0},{0x5601,7,6,1},{0x5401,8,14,0},{0x4801,9,14,0},{0x3801,10,14,0},{0x3001,11,17,0},{0x2401,12,18,0},{0x1C01,13,20,0},{0x1601,29,21,0},{0x5601,15,14,1},{0x5401,16,14,0},{0x5101,17,15,0},{0x4801,18,16,0},{0x3801,19,17,0},{0x3401,20,18,0},{0x3001,21,19,0},{0x2801,22,19,0},{0x2401,23,20,0},{0x2201,24,21,0},{0x1C01,25,22,0},{0x1801,26,23,0},{0x1601,27,24,0},{0x1401,28,25,0},{0x1201,29,26,0},{0x1101,30,27,0},{0x0AC1,31,28,0},{0x09C1,32,29,0},{0x08A1,33,30,0},{0x0521,34,31,0},{0x0441,35,32,0},{0x02A1,36,33,0},{0x0221,37,34,0},{0x0141,38,35,0},{0x0111,39,36,0},{0x0085,40,37,0},{0x0049,41,38,0},{0x0025,42,39,0},{0x0015,43,40,0},{0x0009,44,41,0},{0x0005,45,42,0},{0x0001,45,43,0},{0x5601,46,46,0}};
func (_gdd *Encoder )EncodeIAID (symbolCodeLength ,value int )(_cfd error ){_gf .Log .Trace ("\u0045\u006e\u0063\u006f\u0064\u0065\u0020\u0049A\u0049\u0044\u002e S\u0079\u006d\u0062\u006f\u006c\u0043o\u0064\u0065\u004c\u0065\u006e\u0067\u0074\u0068\u003a\u0020\u0027\u0025\u0064\u0027\u002c \u0056\u0061\u006c\u0075\u0065\u003a\u0020\u0027%\u0064\u0027",symbolCodeLength ,value );
if _cfd =_gdd .encodeIAID (symbolCodeLength ,value );_cfd !=nil {return _c .Wrap (_cfd ,"\u0045\u006e\u0063\u006f\u0064\u0065\u0049\u0041\u0049\u0044","");};return nil ;};func (_bdc *Encoder )encodeIAID (_bggb ,_gac int )error {if _bdc ._eb ==nil {_bdc ._eb =_bgg (1<<uint (_bggb ));
};_bba :=uint32 (1<<uint32 (_bggb +1))-1;_gac <<=uint (32-_bggb );_aeb :=uint32 (1);for _edb :=0;_edb < _bggb ;_edb ++{_ede :=_aeb &_bba ;_baga :=uint8 ((uint32 (_gac )&0x80000000)>>31);if _fcb :=_bdc .encodeBit (_bdc ._eb ,_ede ,_baga );_fcb !=nil {return _fcb ;
};_aeb =(_aeb <<1)|uint32 (_baga );_gac <<=1;};return nil ;};const (IAAI Class =iota ;IADH ;IADS ;IADT ;IADW ;IAEX ;IAFS ;IAIT ;IARDH ;IARDW ;IARDX ;IARDY ;IARI ;);var _ _a .WriterTo =&Encoder {};func (_bfg *Encoder )Init (){_bfg ._dbg =_bgg (_eg );_bfg ._ce =0x8000;
_bfg ._ef =0;_bfg ._gga =12;_bfg ._cd =-1;_bfg ._db =0;_bfg ._dbc =0;_bfg ._ca =make ([]byte ,_efab );for _dfd :=0;_dfd < len (_bfg ._dd );_dfd ++{_bfg ._dd [_dfd ]=_bgg (512);};_bfg ._eb =nil ;};func (_cfgg *Encoder )encodeOOB (_geb Class )error {_dbd :=_cfgg ._dd [_geb ];
_gbfe :=_cfgg .encodeBit (_dbd ,1,1);if _gbfe !=nil {return _gbfe ;};_gbfe =_cfgg .encodeBit (_dbd ,3,0);if _gbfe !=nil {return _gbfe ;};_gbfe =_cfgg .encodeBit (_dbd ,6,0);if _gbfe !=nil {return _gbfe ;};_gbfe =_cfgg .encodeBit (_dbd ,12,0);if _gbfe !=nil {return _gbfe ;
};return nil ;};func (_ffd *Encoder )byteOut (){if _ffd ._db ==0xff{_ffd .rBlock ();return ;};if _ffd ._ef < 0x8000000{_ffd .lBlock ();return ;};_ffd ._db ++;if _ffd ._db !=0xff{_ffd .lBlock ();return ;};_ffd ._ef &=0x7ffffff;_ffd .rBlock ();};func _bgg (_ec int )*codingContext {return &codingContext {_bf :make ([]byte ,_ec ),_d :make ([]byte ,_ec )};
};func (_fg *codingContext )mps (_bb uint32 )int {return int (_fg ._d [_bb ])};func (_bdd *Encoder )encodeBit (_deb *codingContext ,_dda uint32 ,_afdc uint8 )error {const _ceg ="\u0045\u006e\u0063\u006f\u0064\u0065\u0072\u002e\u0065\u006e\u0063\u006fd\u0065\u0042\u0069\u0074";
_bdd ._ff ++;if _dda >=uint32 (len (_deb ._bf )){return _c .Errorf (_ceg ,"\u0061r\u0069\u0074h\u006d\u0065\u0074i\u0063\u0020\u0065\u006e\u0063\u006f\u0064e\u0072\u0020\u002d\u0020\u0069\u006ev\u0061\u006c\u0069\u0064\u0020\u0063\u0074\u0078\u0020\u006e\u0075m\u0062\u0065\u0072\u003a\u0020\u0027\u0025\u0064\u0027",_dda );
};_aae :=_deb ._bf [_dda ];_gad :=_deb .mps (_dda );_ded :=_bac [_aae ]._faa ;_gf .Log .Trace ("\u0045\u0043\u003a\u0020\u0025d\u0009\u0020D\u003a\u0020\u0025d\u0009\u0020\u0049\u003a\u0020\u0025d\u0009\u0020\u004dPS\u003a \u0025\u0064\u0009\u0020\u0051\u0045\u003a \u0025\u0030\u0034\u0058\u0009\u0020\u0020\u0041\u003a\u0020\u0025\u0030\u0034\u0058\u0009\u0020\u0043\u003a %\u0030\u0038\u0058\u0009\u0020\u0043\u0054\u003a\u0020\u0025\u0064\u0009\u0020\u0042\u003a\u0020\u0025\u0030\u0032\u0058\u0009\u0020\u0042\u0050\u003a\u0020\u0025\u0064",_bdd ._ff ,_afdc ,_aae ,_gad ,_ded ,_bdd ._ce ,_bdd ._ef ,_bdd ._gga ,_bdd ._db ,_bdd ._cd );
if _afdc ==0{_bdd .code0 (_deb ,_dda ,_ded ,_aae );}else {_bdd .code1 (_deb ,_dda ,_ded ,_aae );};return nil ;};func (_ffb *Encoder )Reset (){_ffb ._ce =0x8000;_ffb ._ef =0;_ffb ._gga =12;_ffb ._cd =-1;_ffb ._db =0;_ffb ._eb =nil ;_ffb ._dbg =_bgg (_eg );
};type codingContext struct{_bf []byte ;_d []byte ;};func (_bc *Encoder )EncodeOOB (proc Class )(_efa error ){_gf .Log .Trace ("E\u006e\u0063\u006f\u0064\u0065\u0020O\u004f\u0042\u0020\u0077\u0069\u0074\u0068\u0020\u0043l\u0061\u0073\u0073:\u0020'\u0025\u0073\u0027",proc );
if _efa =_bc .encodeOOB (proc );_efa !=nil {return _c .Wrap (_efa ,"\u0045n\u0063\u006f\u0064\u0065\u004f\u004fB","");};return nil ;};func (_efe *Encoder )DataSize ()int {return _efe .dataSize ()};var _f =[]intEncRangeS {{0,3,0,2,0,2},{-1,-1,9,4,0,0},{-3,-2,5,3,2,1},{4,19,2,3,4,4},{-19,-4,3,3,4,4},{20,83,6,4,20,6},{-83,-20,7,4,20,6},{84,339,14,5,84,8},{-339,-84,15,5,84,8},{340,4435,30,6,340,12},{-4435,-340,31,6,340,12},{4436,2000000000,62,6,4436,32},{-2000000000,-4436,63,6,4436,32}};
type Encoder struct{_ef uint32 ;_ce uint16 ;_gga ,_db uint8 ;_cd int ;_ff int ;_df [][]byte ;_ca []byte ;_dbc int ;_dbg *codingContext ;_dd [13]*codingContext ;_eb *codingContext ;};func (_fge *Encoder )Final (){_fge .flush ()};func New ()*Encoder {_cdc :=&Encoder {};
_cdc .Init ();return _cdc };func (_fcg *Encoder )setBits (){_bda :=_fcg ._ef +uint32 (_fcg ._ce );_fcg ._ef |=0xffff;if _fcg ._ef >=_bda {_fcg ._ef -=0x8000;};};func (_bbbd *Encoder )code1 (_de *codingContext ,_bgca uint32 ,_bfa uint16 ,_fdf byte ){if _de .mps (_bgca )==1{_bbbd .codeMPS (_de ,_bgca ,_bfa ,_fdf );
}else {_bbbd .codeLPS (_de ,_bgca ,_bfa ,_fdf );};};func (_ea *Encoder )encodeInteger (_ecd Class ,_ddag int )error {const _fff ="E\u006e\u0063\u006f\u0064er\u002ee\u006e\u0063\u006f\u0064\u0065I\u006e\u0074\u0065\u0067\u0065\u0072";if _ddag > 2000000000||_ddag < -2000000000{return _c .Errorf (_fff ,"\u0061\u0072\u0069\u0074\u0068\u006d\u0065\u0074i\u0063\u0020\u0065nc\u006f\u0064\u0065\u0072\u0020\u002d \u0069\u006e\u0076\u0061\u006c\u0069\u0064\u0020\u0069\u006e\u0074\u0065\u0067\u0065\u0072 \u0076\u0061\u006c\u0075\u0065\u003a\u0020\u0027%\u0064\u0027",_ddag );
};_cfg :=_ea ._dd [_ecd ];_dg :=uint32 (1);var _dee int ;for ;;_dee ++{if _f [_dee ]._ag <=_ddag &&_f [_dee ]._gg >=_ddag {break ;};};if _ddag < 0{_ddag =-_ddag ;};_ddag -=int (_f [_dee ]._e );_aaa :=_f [_dee ]._bg ;for _gce :=uint8 (0);_gce < _f [_dee ]._ba ;
_gce ++{_fbdf :=_aaa &1;if _gag :=_ea .encodeBit (_cfg ,_dg ,_fbdf );_gag !=nil {return _c .Wrap (_gag ,_fff ,"");};_aaa >>=1;if _dg &0x100> 0{_dg =(((_dg <<1)|uint32 (_fbdf ))&0x1ff)|0x100;}else {_dg =(_dg <<1)|uint32 (_fbdf );};};_ddag <<=32-_f [_dee ]._gge ;
for _bbda :=uint8 (0);_bbda < _f [_dee ]._gge ;_bbda ++{_ad :=uint8 ((uint32 (_ddag )&0x80000000)>>31);if _fee :=_ea .encodeBit (_cfg ,_dg ,_ad );_fee !=nil {return _c .Wrap (_fee ,_fff ,"\u006d\u006f\u0076\u0065 \u0064\u0061\u0074\u0061\u0020\u0074\u006f\u0020\u0074\u0068e\u0020t\u006f\u0070\u0020\u006f\u0066\u0020\u0077o\u0072\u0064");
};_ddag <<=1;if _dg &0x100!=0{_dg =(((_dg <<1)|uint32 (_ad ))&0x1ff)|0x100;}else {_dg =(_dg <<1)|uint32 (_ad );};};return nil ;};func (_cce *Encoder )EncodeInteger (proc Class ,value int )(_cab error ){_gf .Log .Trace ("\u0045\u006eco\u0064\u0065\u0020I\u006e\u0074\u0065\u0067er:\u0027%d\u0027\u0020\u0077\u0069\u0074\u0068\u0020Cl\u0061\u0073\u0073\u003a\u0020\u0027\u0025s\u0027",value ,proc );
if _cab =_cce .encodeInteger (proc ,value );_cab !=nil {return _c .Wrap (_cab ,"\u0045\u006e\u0063\u006f\u0064\u0065\u0049\u006e\u0074\u0065\u0067\u0065\u0072","");};return nil ;};func (_geg *Encoder )renormalize (){for {_geg ._ce <<=1;_geg ._ef <<=1;_geg ._gga --;
if _geg ._gga ==0{_geg .byteOut ();};if (_geg ._ce &0x8000)!=0{break ;};};};func (_age *Encoder )code0 (_cceg *codingContext ,_ee uint32 ,_bgcb uint16 ,_ece byte ){if _cceg .mps (_ee )==0{_age .codeMPS (_cceg ,_ee ,_bgcb ,_ece );}else {_age .codeLPS (_cceg ,_ee ,_bgcb ,_ece );
};};func (_gab *Encoder )codeLPS (_gdf *codingContext ,_be uint32 ,_gef uint16 ,_cag byte ){_gab ._ce -=_gef ;if _gab ._ce < _gef {_gab ._ef +=uint32 (_gef );}else {_gab ._ce =_gef ;};if _bac [_cag ]._cg ==1{_gdf .flipMps (_be );};_gdf ._bf [_be ]=_bac [_cag ]._eba ;
_gab .renormalize ();};