//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package classer ;import (_bd "github.com/unidoc/unipdf/v3/common";_f "github.com/unidoc/unipdf/v3/internal/jbig2/basic";_d "github.com/unidoc/unipdf/v3/internal/jbig2/bitmap";_ad "github.com/unidoc/unipdf/v3/internal/jbig2/errors";_c "image";_a "math";
);const JbAddedPixels =6;func (_df *Classer )ComputeLLCorners ()(_gb error ){const _fe ="\u0043l\u0061\u0073\u0073\u0065\u0072\u002e\u0043\u006f\u006d\u0070\u0075t\u0065\u004c\u004c\u0043\u006f\u0072\u006e\u0065\u0072\u0073";if _df .PtaUL ==nil {return _ad .Error (_fe ,"\u0055\u004c\u0020\u0043or\u006e\u0065\u0072\u0073\u0020\u006e\u006f\u0074\u0020\u0064\u0065\u0066\u0069\u006ee\u0064");
};_fc :=len (*_df .PtaUL );_df .PtaLL =&_d .Points {};var (_cd ,_bc float32 ;_dg ,_be int ;_cdf *_d .Bitmap ;);for _ca :=0;_ca < _fc ;_ca ++{_cd ,_bc ,_gb =_df .PtaUL .GetGeometry (_ca );if _gb !=nil {_bd .Log .Debug ("\u0047e\u0074\u0074\u0069\u006e\u0067\u0020\u0050\u0074\u0061\u0055\u004c \u0066\u0061\u0069\u006c\u0065\u0064\u003a\u0020\u0025\u0076",_gb );
return _ad .Wrap (_gb ,_fe ,"\u0050\u0074\u0061\u0055\u004c\u0020\u0047\u0065\u006fm\u0065\u0074\u0072\u0079");};_dg ,_gb =_df .ClassIDs .Get (_ca );if _gb !=nil {_bd .Log .Debug ("\u0047\u0065\u0074\u0074\u0069\u006e\u0067\u0020\u0043\u006c\u0061s\u0073\u0049\u0044\u0020\u0066\u0061\u0069\u006c\u0065\u0064:\u0020\u0025\u0076",_gb );
return _ad .Wrap (_gb ,_fe ,"\u0043l\u0061\u0073\u0073\u0049\u0044");};_cdf ,_gb =_df .UndilatedTemplates .GetBitmap (_dg );if _gb !=nil {_bd .Log .Debug ("\u0047\u0065t\u0074\u0069\u006e\u0067 \u0055\u006ed\u0069\u006c\u0061\u0074\u0065\u0064\u0054\u0065m\u0070\u006c\u0061\u0074\u0065\u0073\u0020\u0066\u0061\u0069\u006c\u0065d\u003a\u0020\u0025\u0076",_gb );
return _ad .Wrap (_gb ,_fe ,"\u0055\u006e\u0064\u0069la\u0074\u0065\u0064\u0020\u0054\u0065\u006d\u0070\u006c\u0061\u0074\u0065\u0073");};_be =_cdf .Height ;_df .PtaLL .AddPoint (_cd ,_bc +float32 (_be ));};return nil ;};func (_bcdc Settings )Validate ()error {const _gcf ="\u0053\u0065\u0074\u0074\u0069\u006e\u0067\u0073\u002e\u0056\u0061\u006ci\u0064\u0061\u0074\u0065";
if _bcdc .Thresh < 0.4||_bcdc .Thresh > 0.98{return _ad .Error (_gcf ,"\u006a\u0062i\u0067\u0032\u0020\u0065\u006e\u0063\u006f\u0064\u0065\u0072\u0020\u0074\u0068\u0072\u0065\u0073\u0068\u0020\u006e\u006f\u0074\u0020\u0069\u006e\u0020\u0072\u0061\u006e\u0067\u0065\u0020\u005b\u0030\u002e\u0034\u0020\u002d\u0020\u0030\u002e\u0039\u0038\u005d");
};if _bcdc .WeightFactor < 0.0||_bcdc .WeightFactor > 1.0{return _ad .Error (_gcf ,"\u006a\u0062i\u0067\u0032\u0020\u0065\u006ec\u006f\u0064\u0065\u0072\u0020w\u0065\u0069\u0067\u0068\u0074\u0020\u0066\u0061\u0063\u0074\u006f\u0072\u0020\u006e\u006f\u0074\u0020\u0069\u006e\u0020\u0072\u0061\u006e\u0067\u0065\u0020\u005b\u0030\u002e\u0030\u0020\u002d\u0020\u0031\u002e\u0030\u005d");
};if _bcdc .RankHaus < 0.5||_bcdc .RankHaus > 1.0{return _ad .Error (_gcf ,"\u006a\u0062\u0069\u0067\u0032\u0020\u0065\u006e\u0063\u006f\u0064\u0065\u0072\u0020\u0072a\u006e\u006b\u0020\u0068\u0061\u0075\u0073\u0020\u0076\u0061\u006c\u0075\u0065 \u006e\u006f\u0074\u0020\u0069\u006e\u0020\u0072\u0061\u006e\u0067\u0065 [\u0030\u002e\u0035\u0020\u002d\u0020\u0031\u002e\u0030\u005d");
};if _bcdc .SizeHaus < 1||_bcdc .SizeHaus > 10{return _ad .Error (_gcf ,"\u006a\u0062\u0069\u0067\u0032 \u0065\u006e\u0063\u006f\u0064\u0065\u0072\u0020\u0073\u0069\u007a\u0065\u0020h\u0061\u0075\u0073\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u006e\u006f\u0074\u0020\u0069\u006e\u0020\u0072\u0061\u006e\u0067\u0065\u0020\u005b\u0031\u0020\u002d\u0020\u0031\u0030]");
};switch _bcdc .Components {case _d .ComponentConn ,_d .ComponentCharacters ,_d .ComponentWords :default:return _ad .Error (_gcf ,"\u0069n\u0076\u0061\u006c\u0069d\u0020\u0063\u006c\u0061\u0073s\u0065r\u0020c\u006f\u006d\u0070\u006f\u006e\u0065\u006et");
};return nil ;};func (_adba *Classer )classifyRankHouseOne (_dcbf *_d .Boxes ,_cebc ,_cce ,_aage *_d .Bitmaps ,_ebf *_d .Points ,_eec int )(_ecfd error ){const _faa ="\u0043\u006c\u0061\u0073s\u0065\u0072\u002e\u0063\u006c\u0061\u0073\u0073\u0069\u0066y\u0052a\u006e\u006b\u0048\u006f\u0075\u0073\u0065O\u006e\u0065";
var (_bgcf ,_cbed ,_beg ,_aaa float32 ;_efb int ;_bdff ,_ddga ,_edg ,_dbd ,_gdf *_d .Bitmap ;_facg ,_acg bool ;);for _bdd :=0;_bdd < len (_cebc .Values );_bdd ++{_ddga =_cce .Values [_bdd ];_edg =_aage .Values [_bdd ];_bgcf ,_cbed ,_ecfd =_ebf .GetGeometry (_bdd );
if _ecfd !=nil {return _ad .Wrapf (_ecfd ,_faa ,"\u0066\u0069\u0072\u0073\u0074\u0020\u0067\u0065\u006fm\u0065\u0074\u0072\u0079");};_fcf :=len (_adba .UndilatedTemplates .Values );_facg =false ;_fbc :=_eee (_adba ,_ddga );for _efb =_fbc .Next ();_efb > -1;
{_dbd ,_ecfd =_adba .UndilatedTemplates .GetBitmap (_efb );if _ecfd !=nil {return _ad .Wrap (_ecfd ,_faa ,"\u0062\u006d\u0033");};_gdf ,_ecfd =_adba .DilatedTemplates .GetBitmap (_efb );if _ecfd !=nil {return _ad .Wrap (_ecfd ,_faa ,"\u0062\u006d\u0034");
};_beg ,_aaa ,_ecfd =_adba .CentroidPointsTemplates .GetGeometry (_efb );if _ecfd !=nil {return _ad .Wrap (_ecfd ,_faa ,"\u0043\u0065\u006e\u0074\u0072\u006f\u0069\u0064\u0054\u0065\u006d\u0070l\u0061\u0074\u0065\u0073");};_acg ,_ecfd =_d .HausTest (_ddga ,_edg ,_dbd ,_gdf ,_bgcf -_beg ,_cbed -_aaa ,MaxDiffWidth ,MaxDiffHeight );
if _ecfd !=nil {return _ad .Wrap (_ecfd ,_faa ,"");};if _acg {_facg =true ;if _ecfd =_adba .ClassIDs .Add (_efb );_ecfd !=nil {return _ad .Wrap (_ecfd ,_faa ,"");};if _ecfd =_adba .ComponentPageNumbers .Add (_eec );_ecfd !=nil {return _ad .Wrap (_ecfd ,_faa ,"");
};if _adba .Settings .KeepClassInstances {_efef ,_bdgc :=_adba .ClassInstances .GetBitmaps (_efb );if _bdgc !=nil {return _ad .Wrap (_bdgc ,_faa ,"\u004be\u0065\u0070\u0050\u0069\u0078\u0061a");};_bdff ,_bdgc =_cebc .GetBitmap (_bdd );if _bdgc !=nil {return _ad .Wrap (_bdgc ,_faa ,"\u004be\u0065\u0070\u0050\u0069\u0078\u0061a");
};_efef .AddBitmap (_bdff );_efed ,_bdgc :=_dcbf .Get (_bdd );if _bdgc !=nil {return _ad .Wrap (_bdgc ,_faa ,"\u004be\u0065\u0070\u0050\u0069\u0078\u0061a");};_efef .AddBox (_efed );};break ;};};if !_facg {if _ecfd =_adba .ClassIDs .Add (_fcf );_ecfd !=nil {return _ad .Wrap (_ecfd ,_faa ,"");
};if _ecfd =_adba .ComponentPageNumbers .Add (_eec );_ecfd !=nil {return _ad .Wrap (_ecfd ,_faa ,"");};_dca :=&_d .Bitmaps {};_bdff ,_ecfd =_cebc .GetBitmap (_bdd );if _ecfd !=nil {return _ad .Wrap (_ecfd ,_faa ,"\u0021\u0066\u006f\u0075\u006e\u0064");
};_dca .Values =append (_dca .Values ,_bdff );_fdc ,_ggd :=_bdff .Width ,_bdff .Height ;_adba .TemplatesSize .Add (uint64 (_ggd )*uint64 (_fdc ),_fcf );_feb ,_aafe :=_dcbf .Get (_bdd );if _aafe !=nil {return _ad .Wrap (_aafe ,_faa ,"\u0021\u0066\u006f\u0075\u006e\u0064");
};_dca .AddBox (_feb );_adba .ClassInstances .AddBitmaps (_dca );_adba .CentroidPointsTemplates .AddPoint (_bgcf ,_cbed );_adba .UndilatedTemplates .AddBitmap (_ddga );_adba .DilatedTemplates .AddBitmap (_edg );};};return nil ;};func (_eg *Classer )classifyRankHaus (_fefg *_d .Boxes ,_efa *_d .Bitmaps ,_ccf int )error {const _agd ="\u0063\u006ca\u0073\u0073\u0069f\u0079\u0052\u0061\u006e\u006b\u0048\u0061\u0075\u0073";
if _fefg ==nil {return _ad .Error (_agd ,"\u0062\u006fx\u0061\u0020\u006eo\u0074\u0020\u0064\u0065\u0066\u0069\u006e\u0065\u0064");};if _efa ==nil {return _ad .Error (_agd ,"\u0070\u0069x\u0061\u0020\u006eo\u0074\u0020\u0064\u0065\u0066\u0069\u006e\u0065\u0064");
};_bae :=len (_efa .Values );if _bae ==0{return _ad .Error (_agd ,"e\u006dp\u0074\u0079\u0020\u006e\u0065\u0077\u0020\u0063o\u006d\u0070\u006f\u006een\u0074\u0073");};_aeg :=_efa .CountPixels ();_ceb :=_eg .Settings .SizeHaus ;_faf :=_d .SelCreateBrick (_ceb ,_ceb ,_ceb /2,_ceb /2,_d .SelHit );
_bca :=&_d .Bitmaps {Values :make ([]*_d .Bitmap ,_bae )};_ffb :=&_d .Bitmaps {Values :make ([]*_d .Bitmap ,_bae )};var (_cab ,_efd ,_ffe *_d .Bitmap ;_de error ;);for _cbf :=0;_cbf < _bae ;_cbf ++{_cab ,_de =_efa .GetBitmap (_cbf );if _de !=nil {return _ad .Wrap (_de ,_agd ,"");
};_efd ,_de =_cab .AddBorderGeneral (JbAddedPixels ,JbAddedPixels ,JbAddedPixels ,JbAddedPixels ,0);if _de !=nil {return _ad .Wrap (_de ,_agd ,"");};_ffe ,_de =_d .Dilate (nil ,_efd ,_faf );if _de !=nil {return _ad .Wrap (_de ,_agd ,"");};_bca .Values [_bae ]=_efd ;
_ffb .Values [_bae ]=_ffe ;};_aaf ,_de :=_d .Centroids (_bca .Values );if _de !=nil {return _ad .Wrap (_de ,_agd ,"");};if _de =_aaf .Add (_eg .CentroidPoints );_de !=nil {_bd .Log .Trace ("\u004e\u006f\u0020\u0063en\u0074\u0072\u006f\u0069\u0064\u0073\u0020\u0074\u006f\u0020\u0061\u0064\u0064");
};if _eg .Settings .RankHaus ==1.0{_de =_eg .classifyRankHouseOne (_fefg ,_efa ,_bca ,_ffb ,_aaf ,_ccf );}else {_de =_eg .classifyRankHouseNonOne (_fefg ,_efa ,_bca ,_ffb ,_aaf ,_aeg ,_ccf );};if _de !=nil {return _ad .Wrap (_de ,_agd ,"");};return nil ;
};func (_g *Classer )AddPage (inputPage *_d .Bitmap ,pageNumber int ,method Method )(_gc error ){const _fd ="\u0043l\u0061s\u0073\u0065\u0072\u002e\u0041\u0064\u0064\u0050\u0061\u0067\u0065";_g .Widths [pageNumber ]=inputPage .Width ;_g .Heights [pageNumber ]=inputPage .Height ;
if _gc =_g .verifyMethod (method );_gc !=nil {return _ad .Wrap (_gc ,_fd ,"");};_fa ,_fab ,_gc :=inputPage .GetComponents (_g .Settings .Components ,_g .Settings .MaxCompWidth ,_g .Settings .MaxCompHeight );if _gc !=nil {return _ad .Wrap (_gc ,_fd ,"");
};_bd .Log .Debug ("\u0043\u006f\u006d\u0070\u006f\u006e\u0065\u006e\u0074s\u003a\u0020\u0025\u0076",_fa );if _gc =_g .addPageComponents (inputPage ,_fab ,_fa ,pageNumber ,method );_gc !=nil {return _ad .Wrap (_gc ,_fd ,"");};return nil ;};const (RankHaus Method =iota ;
Correlation ;);type Classer struct{BaseIndex int ;Settings Settings ;ComponentsNumber *_f .IntSlice ;TemplateAreas *_f .IntSlice ;Widths map[int ]int ;Heights map[int ]int ;NumberOfClasses int ;ClassInstances *_d .BitmapsArray ;UndilatedTemplates *_d .Bitmaps ;
DilatedTemplates *_d .Bitmaps ;TemplatesSize _f .IntsMap ;FgTemplates *_f .NumSlice ;CentroidPoints *_d .Points ;CentroidPointsTemplates *_d .Points ;ClassIDs *_f .IntSlice ;ComponentPageNumbers *_f .IntSlice ;PtaUL *_d .Points ;PtaLL *_d .Points ;};var TwoByTwoWalk =[]int {0,0,0,1,-1,0,0,-1,1,0,-1,1,1,1,-1,-1,1,-1,0,-2,2,0,0,2,-2,0,-1,-2,1,-2,2,-1,2,1,1,2,-1,2,-2,1,-2,-1,-2,-2,2,-2,2,2,-2,2};
func _cde (_dga *_d .Bitmap ,_fbe ,_fae ,_dff ,_bb int ,_ga *_d .Bitmap )(_cded _c .Point ,_dcb error ){const _ed ="\u0066i\u006e\u0061\u006c\u0041l\u0069\u0067\u006e\u006d\u0065n\u0074P\u006fs\u0069\u0074\u0069\u006f\u006e\u0069\u006eg";if _dga ==nil {return _cded ,_ad .Error (_ed ,"\u0073\u006f\u0075\u0072ce\u0020\u006e\u006f\u0074\u0020\u0070\u0072\u006f\u0076\u0069\u0064\u0065\u0064");
};if _ga ==nil {return _cded ,_ad .Error (_ed ,"t\u0065\u006d\u0070\u006cat\u0065 \u006e\u006f\u0074\u0020\u0070r\u006f\u0076\u0069\u0064\u0065\u0064");};_bde ,_bceb :=_ga .Width ,_ga .Height ;_geg ,_ea :=_fbe -_dff -JbAddedPixels ,_fae -_bb -JbAddedPixels ;
_bd .Log .Trace ("\u0078\u003a\u0020\u0027\u0025\u0064\u0027\u002c\u0020\u0079\u003a\u0020\u0027\u0025\u0064'\u002c\u0020\u0077\u003a\u0020\u0027\u0025\u0064\u0027\u002c\u0020\u0068\u003a \u0027\u0025\u0064\u0027\u002c\u0020\u0062\u0078\u003a\u0020\u0027\u0025d'\u002c\u0020\u0062\u0079\u003a\u0020\u0027\u0025\u0064\u0027",_fbe ,_fae ,_bde ,_bceb ,_geg ,_ea );
_geb ,_dcb :=_d .Rect (_geg ,_ea ,_bde ,_bceb );if _dcb !=nil {return _cded ,_ad .Wrap (_dcb ,_ed ,"");};_ebe ,_ ,_dcb :=_dga .ClipRectangle (_geb );if _dcb !=nil {_bd .Log .Error ("\u0043a\u006e\u0027\u0074\u0020\u0063\u006c\u0069\u0070\u0020\u0072\u0065c\u0074\u0061\u006e\u0067\u006c\u0065\u003a\u0020\u0025\u0076",_geb );
return _cded ,_ad .Wrap (_dcb ,_ed ,"");};_aeb :=_d .New (_ebe .Width ,_ebe .Height );_eff :=_a .MaxInt32 ;var _dcf ,_bdg ,_aa ,_gg ,_gcd int ;for _dcf =-1;_dcf <=1;_dcf ++{for _bdg =-1;_bdg <=1;_bdg ++{if _ ,_dcb =_d .Copy (_aeb ,_ebe );_dcb !=nil {return _cded ,_ad .Wrap (_dcb ,_ed ,"");
};if _dcb =_aeb .RasterOperation (_bdg ,_dcf ,_bde ,_bceb ,_d .PixSrcXorDst ,_ga ,0,0);_dcb !=nil {return _cded ,_ad .Wrap (_dcb ,_ed ,"");};_aa =_aeb .CountPixels ();if _aa < _eff {_gg =_bdg ;_gcd =_dcf ;_eff =_aa ;};};};_cded .X =_gg ;_cded .Y =_gcd ;
return _cded ,nil ;};func (_ab *Classer )verifyMethod (_dd Method )error {if _dd !=RankHaus &&_dd !=Correlation {return _ad .Error ("\u0076\u0065\u0072i\u0066\u0079\u004d\u0065\u0074\u0068\u006f\u0064","\u0069\u006e\u0076\u0061li\u0064\u0020\u0063\u006c\u0061\u0073\u0073\u0065\u0072\u0020\u006d\u0065\u0074\u0068o\u0064");
};return nil ;};type similarTemplatesFinder struct{Classer *Classer ;Width int ;Height int ;Index int ;CurrentNumbers []int ;N int ;};func _eee (_efc *Classer ,_cdg *_d .Bitmap )*similarTemplatesFinder {return &similarTemplatesFinder {Width :_cdg .Width ,Height :_cdg .Height ,Classer :_efc };
};func (_fcc *Classer )getULCorners (_fge *_d .Bitmap ,_ce *_d .Boxes )error {const _eb ="\u0067\u0065\u0074U\u004c\u0043\u006f\u0072\u006e\u0065\u0072\u0073";if _fge ==nil {return _ad .Error (_eb ,"\u006e\u0069l\u0020\u0069\u006da\u0067\u0065\u0020\u0062\u0069\u0074\u006d\u0061\u0070");
};if _ce ==nil {return _ad .Error (_eb ,"\u006e\u0069\u006c\u0020\u0062\u006f\u0075\u006e\u0064\u0073");};if _fcc .PtaUL ==nil {_fcc .PtaUL =&_d .Points {};};_ag :=len (*_ce );var (_bcc ,_fec ,_ef ,_gf int ;_cge ,_ge ,_bcd ,_dc float32 ;_fde error ;_cf *_c .Rectangle ;
_ff *_d .Bitmap ;_efe _c .Point ;);for _cgeg :=0;_cgeg < _ag ;_cgeg ++{_bcc =_fcc .BaseIndex +_cgeg ;if _cge ,_ge ,_fde =_fcc .CentroidPoints .GetGeometry (_bcc );_fde !=nil {return _ad .Wrap (_fde ,_eb ,"\u0043\u0065\u006e\u0074\u0072\u006f\u0069\u0064\u0050o\u0069\u006e\u0074\u0073");
};if _fec ,_fde =_fcc .ClassIDs .Get (_bcc );_fde !=nil {return _ad .Wrap (_fde ,_eb ,"\u0043\u006c\u0061s\u0073\u0049\u0044\u0073\u002e\u0047\u0065\u0074");};if _bcd ,_dc ,_fde =_fcc .CentroidPointsTemplates .GetGeometry (_fec );_fde !=nil {return _ad .Wrap (_fde ,_eb ,"\u0043\u0065\u006etr\u006f\u0069\u0064\u0050\u006f\u0069\u006e\u0074\u0073\u0054\u0065\u006d\u0070\u006c\u0061\u0074\u0065\u0073");
};_cef :=_bcd -_cge ;_adb :=_dc -_ge ;if _cef >=0{_ef =int (_cef +0.5);}else {_ef =int (_cef -0.5);};if _adb >=0{_gf =int (_adb +0.5);}else {_gf =int (_adb -0.5);};if _cf ,_fde =_ce .Get (_cgeg );_fde !=nil {return _ad .Wrap (_fde ,_eb ,"");};_ffc ,_feg :=_cf .Min .X ,_cf .Min .Y ;
_ff ,_fde =_fcc .UndilatedTemplates .GetBitmap (_fec );if _fde !=nil {return _ad .Wrap (_fde ,_eb ,"\u0055\u006e\u0064\u0069\u006c\u0061\u0074\u0065\u0064\u0054e\u006d\u0070\u006c\u0061\u0074\u0065\u0073.\u0047\u0065\u0074\u0028\u0069\u0043\u006c\u0061\u0073\u0073\u0029");
};_efe ,_fde =_cde (_fge ,_ffc ,_feg ,_ef ,_gf ,_ff );if _fde !=nil {return _ad .Wrap (_fde ,_eb ,"");};_fcc .PtaUL .AddPoint (float32 (_ffc -_ef +_efe .X ),float32 (_feg -_gf +_efe .Y ));};return nil ;};func Init (settings Settings )(*Classer ,error ){const _cc ="\u0063\u006c\u0061s\u0073\u0065\u0072\u002e\u0049\u006e\u0069\u0074";
_cb :=&Classer {Settings :settings ,Widths :map[int ]int {},Heights :map[int ]int {},TemplatesSize :_f .IntsMap {},TemplateAreas :&_f .IntSlice {},ComponentPageNumbers :&_f .IntSlice {},ClassIDs :&_f .IntSlice {},ComponentsNumber :&_f .IntSlice {},CentroidPoints :&_d .Points {},CentroidPointsTemplates :&_d .Points {},UndilatedTemplates :&_d .Bitmaps {},DilatedTemplates :&_d .Bitmaps {},ClassInstances :&_d .BitmapsArray {},FgTemplates :&_f .NumSlice {}};
if _cg :=_cb .Settings .Validate ();_cg !=nil {return nil ,_ad .Wrap (_cg ,_cc ,"");};return _cb ,nil ;};func (_agf *Classer )classifyRankHouseNonOne (_cee *_d .Boxes ,_gde ,_cdd ,_efg *_d .Bitmaps ,_abfe *_d .Points ,_bga *_f .NumSlice ,_gfa int )(_cbeg error ){const _gac ="\u0043\u006c\u0061\u0073s\u0065\u0072\u002e\u0063\u006c\u0061\u0073\u0073\u0069\u0066y\u0052a\u006e\u006b\u0048\u006f\u0075\u0073\u0065O\u006e\u0065";
var (_gfd ,_abd ,_aaca ,_ddfa float32 ;_dbc ,_dgaf ,_ecfdg int ;_bec ,_ffec ,_dgae ,_bda ,_gce *_d .Bitmap ;_fag ,_cbb bool ;);_ecd :=_d .MakePixelSumTab8 ();for _efgg :=0;_efgg < len (_gde .Values );_efgg ++{if _ffec ,_cbeg =_cdd .GetBitmap (_efgg );_cbeg !=nil {return _ad .Wrap (_cbeg ,_gac ,"b\u006d\u0073\u0031\u002e\u0047\u0065\u0074\u0028\u0069\u0029");
};if _dbc ,_cbeg =_bga .GetInt (_efgg );_cbeg !=nil {_bd .Log .Trace ("\u0047\u0065t\u0074\u0069\u006e\u0067 \u0046\u0047T\u0065\u006d\u0070\u006c\u0061\u0074\u0065\u0073 \u0061\u0074\u003a\u0020\u0025\u0064\u0020\u0066\u0061\u0069\u006c\u0065d\u003a\u0020\u0025\u0076",_efgg ,_cbeg );
};if _dgae ,_cbeg =_efg .GetBitmap (_efgg );_cbeg !=nil {return _ad .Wrap (_cbeg ,_gac ,"b\u006d\u0073\u0032\u002e\u0047\u0065\u0074\u0028\u0069\u0029");};if _gfd ,_abd ,_cbeg =_abfe .GetGeometry (_efgg );_cbeg !=nil {return _ad .Wrapf (_cbeg ,_gac ,"\u0070t\u0061[\u0069\u005d\u002e\u0047\u0065\u006f\u006d\u0065\u0074\u0072\u0079");
};_cdag :=len (_agf .UndilatedTemplates .Values );_fag =false ;_fce :=_eee (_agf ,_ffec );for _ecfdg =_fce .Next ();_ecfdg > -1;{if _bda ,_cbeg =_agf .UndilatedTemplates .GetBitmap (_ecfdg );_cbeg !=nil {return _ad .Wrap (_cbeg ,_gac ,"\u0070\u0069\u0078\u0061\u0074\u002e\u005b\u0069\u0043l\u0061\u0073\u0073\u005d");
};if _dgaf ,_cbeg =_agf .FgTemplates .GetInt (_ecfdg );_cbeg !=nil {_bd .Log .Trace ("\u0047\u0065\u0074\u0074\u0069\u006eg\u0020\u0046\u0047\u0054\u0065\u006d\u0070\u006c\u0061\u0074\u0065\u005b\u0025d\u005d\u0020\u0066\u0061\u0069\u006c\u0065d\u003a\u0020\u0025\u0076",_ecfdg ,_cbeg );
};if _gce ,_cbeg =_agf .DilatedTemplates .GetBitmap (_ecfdg );_cbeg !=nil {return _ad .Wrap (_cbeg ,_gac ,"\u0070\u0069\u0078\u0061\u0074\u0064\u005b\u0069\u0043l\u0061\u0073\u0073\u005d");};if _aaca ,_ddfa ,_cbeg =_agf .CentroidPointsTemplates .GetGeometry (_ecfdg );
_cbeg !=nil {return _ad .Wrap (_cbeg ,_gac ,"\u0043\u0065\u006et\u0072\u006f\u0069\u0064P\u006f\u0069\u006e\u0074\u0073\u0054\u0065m\u0070\u006c\u0061\u0074\u0065\u0073\u005b\u0069\u0043\u006c\u0061\u0073\u0073\u005d");};_cbb ,_cbeg =_d .RankHausTest (_ffec ,_dgae ,_bda ,_gce ,_gfd -_aaca ,_abd -_ddfa ,MaxDiffWidth ,MaxDiffHeight ,_dbc ,_dgaf ,float32 (_agf .Settings .RankHaus ),_ecd );
if _cbeg !=nil {return _ad .Wrap (_cbeg ,_gac ,"");};if _cbb {_fag =true ;if _cbeg =_agf .ClassIDs .Add (_ecfdg );_cbeg !=nil {return _ad .Wrap (_cbeg ,_gac ,"");};if _cbeg =_agf .ComponentPageNumbers .Add (_gfa );_cbeg !=nil {return _ad .Wrap (_cbeg ,_gac ,"");
};if _agf .Settings .KeepClassInstances {_ced ,_bbbg :=_agf .ClassInstances .GetBitmaps (_ecfdg );if _bbbg !=nil {return _ad .Wrap (_bbbg ,_gac ,"\u0063\u002e\u0050\u0069\u0078\u0061\u0061\u002e\u0047\u0065\u0074B\u0069\u0074\u006d\u0061\u0070\u0073\u0028\u0069\u0043\u006ca\u0073\u0073\u0029");
};if _bec ,_bbbg =_gde .GetBitmap (_efgg );_bbbg !=nil {return _ad .Wrap (_bbbg ,_gac ,"\u0070i\u0078\u0061\u005b\u0069\u005d");};_ced .Values =append (_ced .Values ,_bec );_cbfc ,_bbbg :=_cee .Get (_efgg );if _bbbg !=nil {return _ad .Wrap (_bbbg ,_gac ,"b\u006f\u0078\u0061\u002e\u0047\u0065\u0074\u0028\u0069\u0029");
};_ced .Boxes =append (_ced .Boxes ,_cbfc );};break ;};};if !_fag {if _cbeg =_agf .ClassIDs .Add (_cdag );_cbeg !=nil {return _ad .Wrap (_cbeg ,_gac ,"\u0021\u0066\u006f\u0075\u006e\u0064");};if _cbeg =_agf .ComponentPageNumbers .Add (_gfa );_cbeg !=nil {return _ad .Wrap (_cbeg ,_gac ,"\u0021\u0066\u006f\u0075\u006e\u0064");
};_ede :=&_d .Bitmaps {};_bec =_gde .Values [_efgg ];_ede .AddBitmap (_bec );_eeb ,_fcad :=_bec .Width ,_bec .Height ;_agf .TemplatesSize .Add (uint64 (_eeb )*uint64 (_fcad ),_cdag );_agfa ,_fgf :=_cee .Get (_efgg );if _fgf !=nil {return _ad .Wrap (_fgf ,_gac ,"\u0021\u0066\u006f\u0075\u006e\u0064");
};_ede .AddBox (_agfa );_agf .ClassInstances .AddBitmaps (_ede );_agf .CentroidPointsTemplates .AddPoint (_gfd ,_abd );_agf .UndilatedTemplates .AddBitmap (_ffec );_agf .DilatedTemplates .AddBitmap (_dgae );_agf .FgTemplates .AddInt (_dbc );};};_agf .NumberOfClasses =len (_agf .UndilatedTemplates .Values );
return nil ;};const (MaxDiffWidth =2;MaxDiffHeight =2;);func (_bce *Classer )addPageComponents (_gd *_d .Bitmap ,_ae *_d .Boxes ,_fef *_d .Bitmaps ,_bg int ,_fg Method )error {const _e ="\u0043l\u0061\u0073\u0073\u0065r\u002e\u0041\u0064\u0064\u0050a\u0067e\u0043o\u006d\u0070\u006f\u006e\u0065\u006e\u0074s";
if _gd ==nil {return _ad .Error (_e ,"\u006e\u0069\u006c\u0020\u0069\u006e\u0070\u0075\u0074 \u0070\u0061\u0067\u0065");};if _ae ==nil ||_fef ==nil ||len (*_ae )==0{_bd .Log .Trace ("\u0041\u0064\u0064P\u0061\u0067\u0065\u0043\u006f\u006d\u0070\u006f\u006e\u0065\u006e\u0074\u0073\u003a\u0020\u0025\u0073\u002e\u0020\u004e\u006f\u0020\u0063\u006f\u006d\u0070\u006f\u006e\u0065n\u0074\u0073\u0020\u0066\u006f\u0075\u006e\u0064",_gd );
return nil ;};var _fb error ;switch _fg {case RankHaus :_fb =_bce .classifyRankHaus (_ae ,_fef ,_bg );case Correlation :_fb =_bce .classifyCorrelation (_ae ,_fef ,_bg );default:_bd .Log .Debug ("\u0055\u006ek\u006e\u006f\u0077\u006e\u0020\u0063\u006c\u0061\u0073\u0073\u0069\u0066\u0079\u0020\u006d\u0065\u0074\u0068\u006f\u0064\u003a\u0020'%\u0076\u0027",_fg );
return _ad .Error (_e ,"\u0075\u006e\u006bno\u0077\u006e\u0020\u0063\u006c\u0061\u0073\u0073\u0069\u0066\u0079\u0020\u006d\u0065\u0074\u0068\u006f\u0064");};if _fb !=nil {return _ad .Wrap (_fb ,_e ,"");};if _fb =_bce .getULCorners (_gd ,_ae );_fb !=nil {return _ad .Wrap (_fb ,_e ,"");
};_da :=len (*_ae );_bce .BaseIndex +=_da ;if _fb =_bce .ComponentsNumber .Add (_da );_fb !=nil {return _ad .Wrap (_fb ,_e ,"");};return nil ;};func (_afgc *Settings )SetDefault (){if _afgc .MaxCompWidth ==0{switch _afgc .Components {case _d .ComponentConn :_afgc .MaxCompWidth =MaxConnCompWidth ;
case _d .ComponentCharacters :_afgc .MaxCompWidth =MaxCharCompWidth ;case _d .ComponentWords :_afgc .MaxCompWidth =MaxWordCompWidth ;};};if _afgc .MaxCompHeight ==0{_afgc .MaxCompHeight =MaxCompHeight ;};if _afgc .Thresh ==0.0{_afgc .Thresh =0.9;};if _afgc .WeightFactor ==0.0{_afgc .WeightFactor =0.75;
};if _afgc .RankHaus ==0.0{_afgc .RankHaus =0.97;};if _afgc .SizeHaus ==0{_afgc .SizeHaus =2;};};func (_fff *similarTemplatesFinder )Next ()int {var (_ffd ,_egg ,_bac ,_eea int ;_fgeb bool ;_acf *_d .Bitmap ;_ccb error ;);for {if _fff .Index >=25{return -1;
};_egg =_fff .Width +TwoByTwoWalk [2*_fff .Index ];_ffd =_fff .Height +TwoByTwoWalk [2*_fff .Index +1];if _ffd < 1||_egg < 1{_fff .Index ++;continue ;};if len (_fff .CurrentNumbers )==0{_fff .CurrentNumbers ,_fgeb =_fff .Classer .TemplatesSize .GetSlice (uint64 (_egg )*uint64 (_ffd ));
if !_fgeb {_fff .Index ++;continue ;};_fff .N =0;};_bac =len (_fff .CurrentNumbers );for ;_fff .N < _bac ;_fff .N ++{_eea =_fff .CurrentNumbers [_fff .N ];_acf ,_ccb =_fff .Classer .DilatedTemplates .GetBitmap (_eea );if _ccb !=nil {_bd .Log .Debug ("\u0046\u0069\u006e\u0064\u004e\u0065\u0078\u0074\u0054\u0065\u006d\u0070\u006c\u0061\u0074\u0065\u003a\u0020\u0074\u0065\u006d\u0070\u006c\u0061t\u0065\u0020\u006e\u006f\u0074 \u0066\u006fu\u006e\u0064\u003a\u0020");
return 0;};if _acf .Width -2*JbAddedPixels ==_egg &&_acf .Height -2*JbAddedPixels ==_ffd {return _eea ;};};_fff .Index ++;_fff .CurrentNumbers =nil ;};};func DefaultSettings ()Settings {_aec :=&Settings {};_aec .SetDefault ();return *_aec };type Settings struct{MaxCompWidth int ;
MaxCompHeight int ;SizeHaus int ;RankHaus float64 ;Thresh float64 ;WeightFactor float64 ;KeepClassInstances bool ;Components _d .Component ;Method Method ;};var _bbd bool ;type Method int ;const (MaxConnCompWidth =350;MaxCharCompWidth =350;MaxWordCompWidth =1000;
MaxCompHeight =120;);func (_fccb *Classer )classifyCorrelation (_cga *_d .Boxes ,_cff *_d .Bitmaps ,_gaa int )error {const _ec ="\u0063\u006c\u0061\u0073si\u0066\u0079\u0043\u006f\u0072\u0072\u0065\u006c\u0061\u0074\u0069\u006f\u006e";if _cga ==nil {return _ad .Error (_ec ,"\u006e\u0065\u0077\u0043\u006f\u006d\u0070\u006f\u006e\u0065\u006e\u0074\u0073\u0020\u0062\u006f\u0075\u006e\u0064\u0069\u006e\u0067\u0020\u0062o\u0078\u0065\u0073\u0020\u006eo\u0074\u0020f\u006f\u0075\u006e\u0064");
};if _cff ==nil {return _ad .Error (_ec ,"\u006e\u0065wC\u006f\u006d\u0070o\u006e\u0065\u006e\u0074s b\u0069tm\u0061\u0070\u0020\u0061\u0072\u0072\u0061y \u006e\u006f\u0074\u0020\u0066\u006f\u0075n\u0064");};_daa :=len (_cff .Values );if _daa ==0{_bd .Log .Debug ("\u0063l\u0061\u0073s\u0069\u0066\u0079C\u006f\u0072\u0072\u0065\u006c\u0061\u0074i\u006f\u006e\u0020\u002d\u0020\u0070r\u006f\u0076\u0069\u0064\u0065\u0064\u0020\u0070\u0069\u0078\u0061s\u0020\u0069\u0073\u0020\u0065\u006d\u0070\u0074\u0079");
return nil ;};var (_bf ,_cda *_d .Bitmap ;_aag error ;);_gbf :=&_d .Bitmaps {Values :make ([]*_d .Bitmap ,_daa )};for _gef ,_gag :=range _cff .Values {_cda ,_aag =_gag .AddBorderGeneral (JbAddedPixels ,JbAddedPixels ,JbAddedPixels ,JbAddedPixels ,0);if _aag !=nil {return _ad .Wrap (_aag ,_ec ,"");
};_gbf .Values [_gef ]=_cda ;};_aab :=_fccb .FgTemplates ;_ac :=_d .MakePixelSumTab8 ();_adg :=_d .MakePixelCentroidTab8 ();_cbe :=make ([]int ,_daa );_fdec :=make ([][]int ,_daa );_bcf :=_d .Points (make ([]_d .Point ,_daa ));_fefb :=&_bcf ;var (_afg ,_fbd int ;
_fac ,_ba ,_abf int ;_bdf ,_cag int ;_aca byte ;);for _ece ,_dde :=range _gbf .Values {_fdec [_ece ]=make ([]int ,_dde .Height );_afg =0;_fbd =0;_ba =(_dde .Height -1)*_dde .RowStride ;_fac =0;for _cag =_dde .Height -1;_cag >=0;_cag ,_ba =_cag -1,_ba -_dde .RowStride {_fdec [_ece ][_cag ]=_fac ;
_abf =0;for _bdf =0;_bdf < _dde .RowStride ;_bdf ++{_aca =_dde .Data [_ba +_bdf ];_abf +=_ac [_aca ];_afg +=_adg [_aca ]+_bdf *8*_ac [_aca ];};_fac +=_abf ;_fbd +=_abf *_cag ;};_cbe [_ece ]=_fac ;if _fac > 0{(*_fefb )[_ece ]=_d .Point {X :float32 (_afg )/float32 (_fac ),Y :float32 (_fbd )/float32 (_fac )};
}else {(*_fefb )[_ece ]=_d .Point {X :float32 (_dde .Width )/float32 (2),Y :float32 (_dde .Height )/float32 (2)};};};if _aag =_fccb .CentroidPoints .Add (_fefb );_aag !=nil {return _ad .Wrap (_aag ,_ec ,"\u0063\u0065\u006et\u0072\u006f\u0069\u0064\u0020\u0061\u0064\u0064");
};var (_cefa ,_db ,_bcfa int ;_ddf float64 ;_bag ,_gae ,_ddg ,_fdf float32 ;_dba ,_gefd _d .Point ;_acag bool ;_ggc *similarTemplatesFinder ;_fca int ;_bbg *_d .Bitmap ;_ecf *_c .Rectangle ;_aac *_d .Bitmaps ;);for _fca ,_cda =range _gbf .Values {_db =_cbe [_fca ];
if _bag ,_gae ,_aag =_fefb .GetGeometry (_fca );_aag !=nil {return _ad .Wrap (_aag ,_ec ,"\u0070t\u0061\u0020\u002d\u0020\u0069");};_acag =false ;_fcd :=len (_fccb .UndilatedTemplates .Values );_ggc =_eee (_fccb ,_cda );for _bbb :=_ggc .Next ();_bbb > -1;
{if _bbg ,_aag =_fccb .UndilatedTemplates .GetBitmap (_bbb );_aag !=nil {return _ad .Wrap (_aag ,_ec ,"\u0075\u006e\u0069dl\u0061\u0074\u0065\u0064\u005b\u0069\u0063\u006c\u0061\u0073\u0073\u005d\u0020\u003d\u0020\u0062\u006d\u0032");};if _bcfa ,_aag =_aab .GetInt (_bbb );
_aag !=nil {_bd .Log .Trace ("\u0046\u0047\u0020T\u0065\u006d\u0070\u006ca\u0074\u0065\u0020\u005b\u0069\u0063\u006ca\u0073\u0073\u005d\u0020\u0066\u0061\u0069\u006c\u0065\u0064\u003a\u0020\u0025\u0076",_aag );};if _ddg ,_fdf ,_aag =_fccb .CentroidPointsTemplates .GetGeometry (_bbb );
_aag !=nil {return _ad .Wrap (_aag ,_ec ,"\u0043\u0065\u006e\u0074\u0072\u006f\u0069\u0064\u0050\u006f\u0069\u006e\u0074T\u0065\u006d\u0070\u006c\u0061\u0074e\u0073\u005b\u0069\u0063\u006c\u0061\u0073\u0073\u005d\u0020\u003d\u0020\u00782\u002c\u0079\u0032\u0020");
};if _fccb .Settings .WeightFactor > 0.0{if _cefa ,_aag =_fccb .TemplateAreas .Get (_bbb );_aag !=nil {_bd .Log .Trace ("\u0054\u0065\u006dp\u006c\u0061\u0074\u0065A\u0072\u0065\u0061\u0073\u005b\u0069\u0063l\u0061\u0073\u0073\u005d\u0020\u003d\u0020\u0061\u0072\u0065\u0061\u0020\u0025\u0076",_aag );
};_ddf =_fccb .Settings .Thresh +(1.0-_fccb .Settings .Thresh )*_fccb .Settings .WeightFactor *float64 (_bcfa )/float64 (_cefa );}else {_ddf =_fccb .Settings .Thresh ;};_dgf ,_bgc :=_d .CorrelationScoreThresholded (_cda ,_bbg ,_db ,_bcfa ,_dba .X -_gefd .X ,_dba .Y -_gefd .Y ,MaxDiffWidth ,MaxDiffHeight ,_ac ,_fdec [_fca ],float32 (_ddf ));
if _bgc !=nil {return _ad .Wrap (_bgc ,_ec ,"");};if _bbd {var (_cdfd ,_ee float64 ;_gab ,_gcc int ;);_cdfd ,_bgc =_d .CorrelationScore (_cda ,_bbg ,_db ,_bcfa ,_bag -_ddg ,_gae -_fdf ,MaxDiffWidth ,MaxDiffHeight ,_ac );if _bgc !=nil {return _ad .Wrap (_bgc ,_ec ,"d\u0065\u0062\u0075\u0067Co\u0072r\u0065\u006c\u0061\u0074\u0069o\u006e\u0053\u0063\u006f\u0072\u0065");
};_ee ,_bgc =_d .CorrelationScoreSimple (_cda ,_bbg ,_db ,_bcfa ,_bag -_ddg ,_gae -_fdf ,MaxDiffWidth ,MaxDiffHeight ,_ac );if _bgc !=nil {return _ad .Wrap (_bgc ,_ec ,"d\u0065\u0062\u0075\u0067Co\u0072r\u0065\u006c\u0061\u0074\u0069o\u006e\u0053\u0063\u006f\u0072\u0065");
};_gab =int (_a .Sqrt (_cdfd *float64 (_db )*float64 (_bcfa )));_gcc =int (_a .Sqrt (_ee *float64 (_db )*float64 (_bcfa )));if (_cdfd >=_ddf )!=(_ee >=_ddf ){return _ad .Errorf (_ec ,"\u0064\u0065\u0062\u0075\u0067\u0020\u0043\u006f\u0072r\u0065\u006c\u0061\u0074\u0069\u006f\u006e\u0020\u0073\u0063\u006f\u0072\u0065\u0020\u006d\u0069\u0073\u006d\u0061\u0074\u0063\u0068\u0020-\u0020\u0025d\u0028\u00250\u002e\u0034\u0066\u002c\u0020\u0025\u0076\u0029\u0020\u0076\u0073\u0020\u0025d(\u0025\u0030\u002e\u0034\u0066\u002c\u0020\u0025\u0076)\u0020\u0025\u0030\u002e\u0034\u0066",_gab ,_cdfd ,_cdfd >=_ddf ,_gcc ,_ee ,_ee >=_ddf ,_cdfd -_ee );
};if _cdfd >=_ddf !=_dgf {return _ad .Errorf (_ec ,"\u0064\u0065\u0062\u0075\u0067\u0020\u0043o\u0072\u0072\u0065\u006c\u0061\u0074\u0069\u006f\u006e \u0073\u0063\u006f\u0072\u0065 \u004d\u0069\u0073\u006d\u0061t\u0063\u0068 \u0062\u0065\u0074w\u0065\u0065\u006e\u0020\u0063\u006frr\u0065\u006c\u0061\u0074\u0069\u006f\u006e\u0020/\u0020\u0074\u0068\u0072\u0065s\u0068\u006f\u006c\u0064\u002e\u0020\u0043\u006f\u006dpa\u0072\u0069\u0073\u006f\u006e:\u0020\u0025\u0030\u002e\u0034\u0066\u0028\u0025\u0030\u002e\u0034\u0066\u002c\u0020\u0025\u0064\u0029\u0020\u003e\u003d\u0020\u00250\u002e\u0034\u0066\u0028\u0025\u0030\u002e\u0034\u0066\u0029\u0020\u0076\u0073\u0020\u0025\u0076",_cdfd ,_cdfd *float64 (_db )*float64 (_bcfa ),_gab ,_ddf ,float32 (_ddf )*float32 (_db )*float32 (_bcfa ),_dgf );
};};if _dgf {_acag =true ;if _bgc =_fccb .ClassIDs .Add (_bbb );_bgc !=nil {return _ad .Wrap (_bgc ,_ec ,"\u006f\u0076\u0065\u0072\u0054\u0068\u0072\u0065\u0073\u0068\u006f\u006c\u0064");};if _bgc =_fccb .ComponentPageNumbers .Add (_gaa );_bgc !=nil {return _ad .Wrap (_bgc ,_ec ,"\u006f\u0076\u0065\u0072\u0054\u0068\u0072\u0065\u0073\u0068\u006f\u006c\u0064");
};if _fccb .Settings .KeepClassInstances {if _bf ,_bgc =_cff .GetBitmap (_fca );_bgc !=nil {return _ad .Wrap (_bgc ,_ec ,"\u004b\u0065\u0065\u0070Cl\u0061\u0073\u0073\u0049\u006e\u0073\u0074\u0061\u006e\u0063\u0065\u0073\u0020\u002d \u0069");};if _aac ,_bgc =_fccb .ClassInstances .GetBitmaps (_bbb );
_bgc !=nil {return _ad .Wrap (_bgc ,_ec ,"K\u0065\u0065\u0070\u0043\u006c\u0061s\u0073\u0049\u006e\u0073\u0074\u0061\u006e\u0063\u0065s\u0020\u002d\u0020i\u0043l\u0061\u0073\u0073");};_aac .AddBitmap (_bf );if _ecf ,_bgc =_cga .Get (_fca );_bgc !=nil {return _ad .Wrap (_bgc ,_ec ,"\u004be\u0065p\u0043\u006c\u0061\u0073\u0073I\u006e\u0073t\u0061\u006e\u0063\u0065\u0073");
};_aac .AddBox (_ecf );};break ;};};if !_acag {if _aag =_fccb .ClassIDs .Add (_fcd );_aag !=nil {return _ad .Wrap (_aag ,_ec ,"\u0021\u0066\u006f\u0075\u006e\u0064");};if _aag =_fccb .ComponentPageNumbers .Add (_gaa );_aag !=nil {return _ad .Wrap (_aag ,_ec ,"\u0021\u0066\u006f\u0075\u006e\u0064");
};_aac =&_d .Bitmaps {};if _bf ,_aag =_cff .GetBitmap (_fca );_aag !=nil {return _ad .Wrap (_aag ,_ec ,"\u0021\u0066\u006f\u0075\u006e\u0064");};_aac .AddBitmap (_bf );_aae ,_bcec :=_bf .Width ,_bf .Height ;_dgc :=uint64 (_bcec )*uint64 (_aae );_fccb .TemplatesSize .Add (_dgc ,_fcd );
if _ecf ,_aag =_cga .Get (_fca );_aag !=nil {return _ad .Wrap (_aag ,_ec ,"\u0021\u0066\u006f\u0075\u006e\u0064");};_aac .AddBox (_ecf );_fccb .ClassInstances .AddBitmaps (_aac );_fccb .CentroidPointsTemplates .AddPoint (_bag ,_gae );_fccb .FgTemplates .AddInt (_db );
_fccb .UndilatedTemplates .AddBitmap (_bf );_cefa =(_cda .Width -2*JbAddedPixels )*(_cda .Height -2*JbAddedPixels );if _aag =_fccb .TemplateAreas .Add (_cefa );_aag !=nil {return _ad .Wrap (_aag ,_ec ,"\u0021\u0066\u006f\u0075\u006e\u0064");};};};_fccb .NumberOfClasses =len (_fccb .UndilatedTemplates .Values );
return nil ;};