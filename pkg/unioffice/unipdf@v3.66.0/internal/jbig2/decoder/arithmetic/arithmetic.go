//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package arithmetic ;import (_ge "fmt";_a "github.com/unidoc/unipdf/v3/common";_ga "github.com/unidoc/unipdf/v3/internal/bitwise";_ce "github.com/unidoc/unipdf/v3/internal/jbig2/internal";_g "io";_c "strings";);func (_dfc *DecoderStats )String ()string {_ffe :=&_c .Builder {};
_ffe .WriteString (_ge .Sprintf ("S\u0074\u0061\u0074\u0073\u003a\u0020\u0020\u0025\u0064\u000a",len (_dfc ._ef )));for _aaf ,_cba :=range _dfc ._ef {if _cba !=0{_ffe .WriteString (_ge .Sprintf ("N\u006f\u0074\u0020\u007aer\u006f \u0061\u0074\u003a\u0020\u0025d\u0020\u002d\u0020\u0025\u0064\u000a",_aaf ,_cba ));
};};return _ffe .String ();};func (_cegb *Decoder )DecodeIAID (codeLen uint64 ,stats *DecoderStats )(int64 ,error ){_cegb ._caf =1;var _bf uint64 ;for _bf =0;_bf < codeLen ;_bf ++{stats .SetIndex (int32 (_cegb ._caf ));_cee ,_ab :=_cegb .DecodeBit (stats );
if _ab !=nil {return 0,_ab ;};_cegb ._caf =(_cegb ._caf <<1)|int64 (_cee );};_cef :=_cegb ._caf -(1<<codeLen );return _cef ,nil ;};var (_gd =[][4]uint32 {{0x5601,1,1,1},{0x3401,2,6,0},{0x1801,3,9,0},{0x0AC1,4,12,0},{0x0521,5,29,0},{0x0221,38,33,0},{0x5601,7,6,1},{0x5401,8,14,0},{0x4801,9,14,0},{0x3801,10,14,0},{0x3001,11,17,0},{0x2401,12,18,0},{0x1C01,13,20,0},{0x1601,29,21,0},{0x5601,15,14,1},{0x5401,16,14,0},{0x5101,17,15,0},{0x4801,18,16,0},{0x3801,19,17,0},{0x3401,20,18,0},{0x3001,21,19,0},{0x2801,22,19,0},{0x2401,23,20,0},{0x2201,24,21,0},{0x1C01,25,22,0},{0x1801,26,23,0},{0x1601,27,24,0},{0x1401,28,25,0},{0x1201,29,26,0},{0x1101,30,27,0},{0x0AC1,31,28,0},{0x09C1,32,29,0},{0x08A1,33,30,0},{0x0521,34,31,0},{0x0441,35,32,0},{0x02A1,36,33,0},{0x0221,37,34,0},{0x0141,38,35,0},{0x0111,39,36,0},{0x0085,40,37,0},{0x0049,41,38,0},{0x0025,42,39,0},{0x0015,43,40,0},{0x0009,44,41,0},{0x0005,45,42,0},{0x0001,45,43,0},{0x5601,46,46,0}};
);func (_bgf *Decoder )lpsExchange (_gg *DecoderStats ,_ac int32 ,_afa uint32 )int {_bgb :=_gg .getMps ();if _bgf ._ca < _afa {_gg .setEntry (int (_gd [_ac ][1]));_bgf ._ca =_afa ;return int (_bgb );};if _gd [_ac ][3]==1{_gg .toggleMps ();};_gg .setEntry (int (_gd [_ac ][2]));
_bgf ._ca =_afa ;return int (1-_bgb );};func (_abc *DecoderStats )setEntry (_bac int ){_afb :=byte (_bac &0x7f);_abc ._ef [_abc ._egd ]=_afb };func (_dc *Decoder )readByte ()error {if _dc ._b .AbsolutePosition ()> _dc ._cf {if _ ,_ffd :=_dc ._b .Seek (-1,_g .SeekCurrent );
_ffd !=nil {return _ffd ;};};_fd ,_fde :=_dc ._b .ReadByte ();if _fde !=nil {return _fde ;};_dc ._gc =_fd ;if _dc ._gc ==0xFF{_bb ,_bc :=_dc ._b .ReadByte ();if _bc !=nil {return _bc ;};if _bb > 0x8F{_dc ._e +=0xFF00;_dc ._de =8;if _ ,_ebg :=_dc ._b .Seek (-2,_g .SeekCurrent );
_ebg !=nil {return _ebg ;};}else {_dc ._e +=uint64 (_bb )<<9;_dc ._de =7;};}else {_fd ,_fde =_dc ._b .ReadByte ();if _fde !=nil {return _fde ;};_dc ._gc =_fd ;_dc ._e +=uint64 (_dc ._gc )<<8;_dc ._de =8;};_dc ._e &=0xFFFFFFFFFF;return nil ;};func (_gdg *Decoder )init ()error {_gdg ._cf =_gdg ._b .AbsolutePosition ();
_dg ,_cfa :=_gdg ._b .ReadByte ();if _cfa !=nil {_a .Log .Debug ("B\u0075\u0066\u0066\u0065\u0072\u0030 \u0072\u0065\u0061\u0064\u0042\u0079\u0074\u0065\u0020f\u0061\u0069\u006ce\u0064.\u0020\u0025\u0076",_cfa );return _cfa ;};_gdg ._gc =_dg ;_gdg ._e =uint64 (_dg )<<16;
if _cfa =_gdg .readByte ();_cfa !=nil {return _cfa ;};_gdg ._e <<=7;_gdg ._de -=7;_gdg ._ca =0x8000;_gdg ._cb ++;return nil ;};func (_cead *DecoderStats )SetIndex (index int32 ){_cead ._egd =index };func NewStats (contextSize int32 ,index int32 )*DecoderStats {return &DecoderStats {_egd :index ,_ecc :contextSize ,_ef :make ([]byte ,contextSize ),_gb :make ([]byte ,contextSize )};
};func (_gcb *DecoderStats )Overwrite (dNew *DecoderStats ){for _cfaa :=0;_cfaa < len (_gcb ._ef );_cfaa ++{_gcb ._ef [_cfaa ]=dNew ._ef [_cfaa ];_gcb ._gb [_cfaa ]=dNew ._gb [_cfaa ];};};func (_fe *DecoderStats )Copy ()*DecoderStats {_be :=&DecoderStats {_ecc :_fe ._ecc ,_ef :make ([]byte ,_fe ._ecc )};
copy (_be ._ef ,_fe ._ef );return _be ;};func (_dgd *DecoderStats )Reset (){for _gef :=0;_gef < len (_dgd ._ef );_gef ++{_dgd ._ef [_gef ]=0;_dgd ._gb [_gef ]=0;};};type DecoderStats struct{_egd int32 ;_ecc int32 ;_ef []byte ;_gb []byte ;};func (_abe *Decoder )decodeIntBit (_ag *DecoderStats )(int ,error ){_ag .SetIndex (int32 (_abe ._caf ));
_gce ,_cea :=_abe .DecodeBit (_ag );if _cea !=nil {_a .Log .Debug ("\u0041\u0072\u0069\u0074\u0068\u006d\u0065t\u0069\u0063\u0044e\u0063\u006f\u0064e\u0072\u0020'\u0064\u0065\u0063\u006f\u0064\u0065I\u006etB\u0069\u0074\u0027\u002d\u003e\u0020\u0044\u0065\u0063\u006f\u0064\u0065\u0042\u0069\u0074\u0020\u0066\u0061\u0069\u006c\u0065\u0064\u002e\u0020\u0025\u0076",_cea );
return _gce ,_cea ;};if _abe ._caf < 256{_abe ._caf =((_abe ._caf <<uint64 (1))|int64 (_gce ))&0x1ff;}else {_abe ._caf =(((_abe ._caf <<uint64 (1)|int64 (_gce ))&511)|256)&0x1ff;};return _gce ,nil ;};type Decoder struct{ContextSize []uint32 ;ReferedToContextSize []uint32 ;
_b *_ga .Reader ;_gc uint8 ;_e uint64 ;_ca uint32 ;_caf int64 ;_de int32 ;_cb int32 ;_cf int64 ;};func (_bg *Decoder )mpsExchange (_gf *DecoderStats ,_db int32 )int {_gaaa :=_gf ._gb [_gf ._egd ];if _bg ._ca < _gd [_db ][0]{if _gd [_db ][3]==1{_gf .toggleMps ();
};_gf .setEntry (int (_gd [_db ][2]));return int (1-_gaaa );};_gf .setEntry (int (_gd [_db ][1]));return int (_gaaa );};func (_fg *Decoder )renormalize ()error {for {if _fg ._de ==0{if _fdb :=_fg .readByte ();_fdb !=nil {return _fdb ;};};_fg ._ca <<=1;
_fg ._e <<=1;_fg ._de --;if (_fg ._ca &0x8000)!=0{break ;};};_fg ._e &=0xffffffff;return nil ;};func (_cge *DecoderStats )getMps ()byte {return _cge ._gb [_cge ._egd ]};func (_feg *DecoderStats )cx ()byte {return _feg ._ef [_feg ._egd ]};func New (r *_ga .Reader )(*Decoder ,error ){_aa :=&Decoder {_b :r ,ContextSize :[]uint32 {16,13,10,10},ReferedToContextSize :[]uint32 {13,10}};
if _ba :=_aa .init ();_ba !=nil {return nil ,_ba ;};return _aa ,nil ;};func (_eg *Decoder )DecodeBit (stats *DecoderStats )(int ,error ){var (_ceg int ;_ee =_gd [stats .cx ()][0];_ec =int32 (stats .cx ()););defer func (){_eg ._cb ++}();_eg ._ca -=_ee ;
if (_eg ._e >>16)< uint64 (_ee ){_ceg =_eg .lpsExchange (stats ,_ec ,_ee );if _da :=_eg .renormalize ();_da !=nil {return 0,_da ;};}else {_eg ._e -=uint64 (_ee )<<16;if (_eg ._ca &0x8000)==0{_ceg =_eg .mpsExchange (stats ,_ec );if _cfg :=_eg .renormalize ();
_cfg !=nil {return 0,_cfg ;};}else {_ceg =int (stats .getMps ());};};return _ceg ,nil ;};func (_f *Decoder )DecodeInt (stats *DecoderStats )(int32 ,error ){var (_cg ,_fa int32 ;_af ,_ff ,_gaa int ;_fb error ;);if stats ==nil {stats =NewStats (512,1);};
_f ._caf =1;_ff ,_fb =_f .decodeIntBit (stats );if _fb !=nil {return 0,_fb ;};_af ,_fb =_f .decodeIntBit (stats );if _fb !=nil {return 0,_fb ;};if _af ==1{_af ,_fb =_f .decodeIntBit (stats );if _fb !=nil {return 0,_fb ;};if _af ==1{_af ,_fb =_f .decodeIntBit (stats );
if _fb !=nil {return 0,_fb ;};if _af ==1{_af ,_fb =_f .decodeIntBit (stats );if _fb !=nil {return 0,_fb ;};if _af ==1{_af ,_fb =_f .decodeIntBit (stats );if _fb !=nil {return 0,_fb ;};if _af ==1{_gaa =32;_fa =4436;}else {_gaa =12;_fa =340;};}else {_gaa =8;
_fa =84;};}else {_gaa =6;_fa =20;};}else {_gaa =4;_fa =4;};}else {_gaa =2;_fa =0;};for _df :=0;_df < _gaa ;_df ++{_af ,_fb =_f .decodeIntBit (stats );if _fb !=nil {return 0,_fb ;};_cg =(_cg <<1)|int32 (_af );};_cg +=_fa ;if _ff ==0{return _cg ,nil ;}else if _ff ==1&&_cg > 0{return -_cg ,nil ;
};return 0,_ce .ErrOOB ;};func (_ebge *DecoderStats )toggleMps (){_ebge ._gb [_ebge ._egd ]^=1};