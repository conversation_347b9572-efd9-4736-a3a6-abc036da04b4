//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package mmr ;import (_ef "errors";_f "fmt";_fa "github.com/unidoc/unipdf/v3/common";_b "github.com/unidoc/unipdf/v3/internal/bitwise";_fe "github.com/unidoc/unipdf/v3/internal/jbig2/bitmap";_e "io";);const (_ed mmrCode =iota ;_eg ;_ag ;_bg ;_bc ;_bga ;
_g ;_fg ;_ffg ;_fga ;_df ;);func (_fac *Decoder )fillBitmap (_dae *_fe .Bitmap ,_ba int ,_ga []int ,_dea int )error {var _dbde byte ;_ab :=0;_aeaa :=_dae .GetByteIndex (_ab ,_ba );for _eggf :=0;_eggf < _dea ;_eggf ++{_ce :=byte (1);_gbb :=_ga [_eggf ];
if (_eggf &1)==0{_ce =0;};for _ab < _gbb {_dbde =(_dbde <<1)|_ce ;_ab ++;if (_ab &7)==0{if _gbf :=_dae .SetByte (_aeaa ,_dbde );_gbf !=nil {return _gbf ;};_aeaa ++;_dbde =0;};};};if (_ab &7)!=0{_dbde <<=uint (8-(_ab &7));if _eea :=_dae .SetByte (_aeaa ,_dbde );
_eea !=nil {return _eea ;};};return nil ;};func (_dfe *Decoder )uncompress2d (_egb *runData ,_gdce []int ,_eee int ,_agf []int ,_ge int )(int ,error ){var (_beg int ;_cc int ;_bgb int ;_ebg =true ;_eab error ;_afa *code ;);_gdce [_eee ]=_ge ;_gdce [_eee +1]=_ge ;
_gdce [_eee +2]=_ge +1;_gdce [_eee +3]=_ge +1;_bgd :for _bgb < _ge {_afa ,_eab =_egb .uncompressGetCode (_dfe ._ae );if _eab !=nil {return EOL ,nil ;};if _afa ==nil {_egb ._ccc ++;break _bgd ;};_egb ._ccc +=_afa ._a ;switch mmrCode (_afa ._fc ){case _ag :_bgb =_gdce [_beg ];
case _bg :_bgb =_gdce [_beg ]+1;case _g :_bgb =_gdce [_beg ]-1;case _eg :for {var _gdb []*code ;if _ebg {_gdb =_dfe ._gc ;}else {_gdb =_dfe ._cfa ;};_afa ,_eab =_egb .uncompressGetCode (_gdb );if _eab !=nil {return 0,_eab ;};if _afa ==nil {break _bgd ;
};_egb ._ccc +=_afa ._a ;if _afa ._fc < 64{if _afa ._fc < 0{_agf [_cc ]=_bgb ;_cc ++;_afa =nil ;break _bgd ;};_bgb +=_afa ._fc ;_agf [_cc ]=_bgb ;_cc ++;break ;};_bgb +=_afa ._fc ;};_gfca :=_bgb ;_eeag :for {var _bcc []*code ;if !_ebg {_bcc =_dfe ._gc ;
}else {_bcc =_dfe ._cfa ;};_afa ,_eab =_egb .uncompressGetCode (_bcc );if _eab !=nil {return 0,_eab ;};if _afa ==nil {break _bgd ;};_egb ._ccc +=_afa ._a ;if _afa ._fc < 64{if _afa ._fc < 0{_agf [_cc ]=_bgb ;_cc ++;break _bgd ;};_bgb +=_afa ._fc ;if _bgb < _ge ||_bgb !=_gfca {_agf [_cc ]=_bgb ;
_cc ++;};break _eeag ;};_bgb +=_afa ._fc ;};for _bgb < _ge &&_gdce [_beg ]<=_bgb {_beg +=2;};continue _bgd ;case _ed :_beg ++;_bgb =_gdce [_beg ];_beg ++;continue _bgd ;case _bc :_bgb =_gdce [_beg ]+2;case _fg :_bgb =_gdce [_beg ]-2;case _bga :_bgb =_gdce [_beg ]+3;
case _ffg :_bgb =_gdce [_beg ]-3;default:if _egb ._ccc ==12&&_afa ._fc ==EOL {_egb ._ccc =0;if _ ,_eab =_dfe .uncompress1d (_egb ,_gdce ,_ge );_eab !=nil {return 0,_eab ;};_egb ._ccc ++;if _ ,_eab =_dfe .uncompress1d (_egb ,_agf ,_ge );_eab !=nil {return 0,_eab ;
};_gga ,_ffe :=_dfe .uncompress1d (_egb ,_gdce ,_ge );if _ffe !=nil {return EOF ,_ffe ;};_egb ._ccc ++;return _gga ,nil ;};_bgb =_ge ;continue _bgd ;};if _bgb <=_ge {_ebg =!_ebg ;_agf [_cc ]=_bgb ;_cc ++;if _beg > 0{_beg --;}else {_beg ++;};for _bgb < _ge &&_gdce [_beg ]<=_bgb {_beg +=2;
};};};if _agf [_cc ]!=_ge {_agf [_cc ]=_ge ;};if _afa ==nil {return EOL ,nil ;};return _cc ,nil ;};func New (r *_b .Reader ,width ,height int ,dataOffset ,dataLength int64 )(*Decoder ,error ){_gf :=&Decoder {_gb :width ,_ege :height };_aea ,_fae :=r .NewPartialReader (int (dataOffset ),int (dataLength ),false );
if _fae !=nil {return nil ,_fae ;};_fab ,_fae :=_cfc (_aea );if _fae !=nil {return nil ,_fae ;};_ ,_fae =r .Seek (_aea .RelativePosition (),_e .SeekCurrent );if _fae !=nil {return nil ,_fae ;};_gf ._dbd =_fab ;if _dfd :=_gf .initTables ();_dfd !=nil {return nil ,_dfd ;
};return _gf ,nil ;};func (_bbd *Decoder )UncompressMMR ()(_ea *_fe .Bitmap ,_fcb error ){_ea =_fe .New (_bbd ._gb ,_bbd ._ege );_ad :=make ([]int ,_ea .Width +5);_cb :=make ([]int ,_ea .Width +5);_cb [0]=_ea .Width ;_egg :=1;var _bcb int ;for _gfc :=0;
_gfc < _ea .Height ;_gfc ++{_bcb ,_fcb =_bbd .uncompress2d (_bbd ._dbd ,_cb ,_egg ,_ad ,_ea .Width );if _fcb !=nil {return nil ,_fcb ;};if _bcb ==EOF {break ;};if _bcb > 0{_fcb =_bbd .fillBitmap (_ea ,_gfc ,_ad ,_bcb );if _fcb !=nil {return nil ,_fcb ;
};};_cb ,_ad =_ad ,_cb ;_egg =_bcb ;};if _fcb =_bbd .detectAndSkipEOL ();_fcb !=nil {return nil ,_fcb ;};_bbd ._dbd .align ();return _ea ,nil ;};func (_af *Decoder )detectAndSkipEOL ()error {for {_ecb ,_cdb :=_af ._dbd .uncompressGetCode (_af ._ae );if _cdb !=nil {return _cdb ;
};if _ecb !=nil &&_ecb ._fc ==EOL {_af ._dbd ._ccc +=_ecb ._a ;}else {return nil ;};};};func _ac (_db ,_ee int )int {if _db < _ee {return _ee ;};return _db ;};func _cfc (_bac *_b .Reader )(*runData ,error ){_fdd :=&runData {_ggab :_bac ,_ccc :0,_dcg :1};
_dcb :=_de (_ac (_dfg ,int (_bac .Length ())),_gcd );_fdd ._bca =make ([]byte ,_dcb );if _aef :=_fdd .fillBuffer (0);_aef !=nil {if _aef ==_e .EOF {_fdd ._bca =make ([]byte ,10);_fa .Log .Debug ("F\u0069\u006c\u006c\u0042uf\u0066e\u0072\u0020\u0066\u0061\u0069l\u0065\u0064\u003a\u0020\u0025\u0076",_aef );
}else {return nil ,_aef ;};};return _fdd ,nil ;};func (_eeac *Decoder )initTables ()(_bbf error ){if _eeac ._gc ==nil {_eeac ._gc ,_bbf =_eeac .createLittleEndianTable (_cf );if _bbf !=nil {return ;};_eeac ._cfa ,_bbf =_eeac .createLittleEndianTable (_ace );
if _bbf !=nil {return ;};_eeac ._ae ,_bbf =_eeac .createLittleEndianTable (_bcf );if _bbf !=nil {return ;};};return nil ;};type runData struct{_ggab *_b .Reader ;_ccc int ;_dcg int ;_agfc int ;_bca []byte ;_ecd int ;_fea int ;};func (_bf *code )String ()string {return _f .Sprintf ("\u0025\u0064\u002f\u0025\u0064\u002f\u0025\u0064",_bf ._a ,_bf ._fd ,_bf ._fc );
};func (_gef *runData )uncompressGetCode (_ecbb []*code )(*code ,error ){return _gef .uncompressGetCodeLittleEndian (_ecbb );};type Decoder struct{_gb ,_ege int ;_dbd *runData ;_gc []*code ;_cfa []*code ;_ae []*code ;};type mmrCode int ;const (EOF =-3;
_gd =-2;EOL =-1;_fcd =8;_dg =(1<<_fcd )-1;_dfa =5;_cd =(1<<_dfa )-1;);var (_bcf =[][3]int {{4,0x1,int (_ed )},{3,0x1,int (_eg )},{1,0x1,int (_ag )},{3,0x3,int (_bg )},{6,0x3,int (_bc )},{7,0x3,int (_bga )},{3,0x2,int (_g )},{6,0x2,int (_fg )},{7,0x2,int (_ffg )},{10,0xf,int (_fga )},{12,0xf,int (_df )},{12,0x1,int (EOL )}};
_cf =[][3]int {{4,0x07,2},{4,0x08,3},{4,0x0B,4},{4,0x0C,5},{4,0x0E,6},{4,0x0F,7},{5,0x12,128},{5,0x13,8},{5,0x14,9},{5,0x1B,64},{5,0x07,10},{5,0x08,11},{6,0x17,192},{6,0x18,1664},{6,0x2A,16},{6,0x2B,17},{6,0x03,13},{6,0x34,14},{6,0x35,15},{6,0x07,1},{6,0x08,12},{7,0x13,26},{7,0x17,21},{7,0x18,28},{7,0x24,27},{7,0x27,18},{7,0x28,24},{7,0x2B,25},{7,0x03,22},{7,0x37,256},{7,0x04,23},{7,0x08,20},{7,0xC,19},{8,0x12,33},{8,0x13,34},{8,0x14,35},{8,0x15,36},{8,0x16,37},{8,0x17,38},{8,0x1A,31},{8,0x1B,32},{8,0x02,29},{8,0x24,53},{8,0x25,54},{8,0x28,39},{8,0x29,40},{8,0x2A,41},{8,0x2B,42},{8,0x2C,43},{8,0x2D,44},{8,0x03,30},{8,0x32,61},{8,0x33,62},{8,0x34,63},{8,0x35,0},{8,0x36,320},{8,0x37,384},{8,0x04,45},{8,0x4A,59},{8,0x4B,60},{8,0x5,46},{8,0x52,49},{8,0x53,50},{8,0x54,51},{8,0x55,52},{8,0x58,55},{8,0x59,56},{8,0x5A,57},{8,0x5B,58},{8,0x64,448},{8,0x65,512},{8,0x67,640},{8,0x68,576},{8,0x0A,47},{8,0x0B,48},{9,0x01,_gd },{9,0x98,1472},{9,0x99,1536},{9,0x9A,1600},{9,0x9B,1728},{9,0xCC,704},{9,0xCD,768},{9,0xD2,832},{9,0xD3,896},{9,0xD4,960},{9,0xD5,1024},{9,0xD6,1088},{9,0xD7,1152},{9,0xD8,1216},{9,0xD9,1280},{9,0xDA,1344},{9,0xDB,1408},{10,0x01,_gd },{11,0x01,_gd },{11,0x08,1792},{11,0x0C,1856},{11,0x0D,1920},{12,0x00,EOF },{12,0x01,EOL },{12,0x12,1984},{12,0x13,2048},{12,0x14,2112},{12,0x15,2176},{12,0x16,2240},{12,0x17,2304},{12,0x1C,2368},{12,0x1D,2432},{12,0x1E,2496},{12,0x1F,2560}};
_ace =[][3]int {{2,0x02,3},{2,0x03,2},{3,0x02,1},{3,0x03,4},{4,0x02,6},{4,0x03,5},{5,0x03,7},{6,0x04,9},{6,0x05,8},{7,0x04,10},{7,0x05,11},{7,0x07,12},{8,0x04,13},{8,0x07,14},{9,0x01,_gd },{9,0x18,15},{10,0x01,_gd },{10,0x17,16},{10,0x18,17},{10,0x37,0},{10,0x08,18},{10,0x0F,64},{11,0x01,_gd },{11,0x17,24},{11,0x18,25},{11,0x28,23},{11,0x37,22},{11,0x67,19},{11,0x68,20},{11,0x6C,21},{11,0x08,1792},{11,0x0C,1856},{11,0x0D,1920},{12,0x00,EOF },{12,0x01,EOL },{12,0x12,1984},{12,0x13,2048},{12,0x14,2112},{12,0x15,2176},{12,0x16,2240},{12,0x17,2304},{12,0x1C,2368},{12,0x1D,2432},{12,0x1E,2496},{12,0x1F,2560},{12,0x24,52},{12,0x27,55},{12,0x28,56},{12,0x2B,59},{12,0x2C,60},{12,0x33,320},{12,0x34,384},{12,0x35,448},{12,0x37,53},{12,0x38,54},{12,0x52,50},{12,0x53,51},{12,0x54,44},{12,0x55,45},{12,0x56,46},{12,0x57,47},{12,0x58,57},{12,0x59,58},{12,0x5A,61},{12,0x5B,256},{12,0x64,48},{12,0x65,49},{12,0x66,62},{12,0x67,63},{12,0x68,30},{12,0x69,31},{12,0x6A,32},{12,0x6B,33},{12,0x6C,40},{12,0x6D,41},{12,0xC8,128},{12,0xC9,192},{12,0xCA,26},{12,0xCB,27},{12,0xCC,28},{12,0xCD,29},{12,0xD2,34},{12,0xD3,35},{12,0xD4,36},{12,0xD5,37},{12,0xD6,38},{12,0xD7,39},{12,0xDA,42},{12,0xDB,43},{13,0x4A,640},{13,0x4B,704},{13,0x4C,768},{13,0x4D,832},{13,0x52,1280},{13,0x53,1344},{13,0x54,1408},{13,0x55,1472},{13,0x5A,1536},{13,0x5B,1600},{13,0x64,1664},{13,0x65,1728},{13,0x6C,512},{13,0x6D,576},{13,0x72,896},{13,0x73,960},{13,0x74,1024},{13,0x75,1088},{13,0x76,1152},{13,0x77,1216}};
);func (_gdf *runData )fillBuffer (_gbd int )error {_gdf ._ecd =_gbd ;_ ,_ccd :=_gdf ._ggab .Seek (int64 (_gbd ),_e .SeekStart );if _ccd !=nil {if _ccd ==_e .EOF {_fa .Log .Debug ("\u0053\u0065\u0061\u006b\u0020\u0045\u004f\u0046");_gdf ._fea =-1;}else {return _ccd ;
};};if _ccd ==nil {_gdf ._fea ,_ccd =_gdf ._ggab .Read (_gdf ._bca );if _ccd !=nil {if _ccd ==_e .EOF {_fa .Log .Trace ("\u0052\u0065\u0061\u0064\u0020\u0045\u004f\u0046");_gdf ._fea =-1;}else {return _ccd ;};};};if _gdf ._fea > -1&&_gdf ._fea < 3{for _gdf ._fea < 3{_dgfe ,_ead :=_gdf ._ggab .ReadByte ();
if _ead !=nil {if _ead ==_e .EOF {_gdf ._bca [_gdf ._fea ]=0;}else {return _ead ;};}else {_gdf ._bca [_gdf ._fea ]=_dgfe &0xFF;};_gdf ._fea ++;};};_gdf ._fea -=3;if _gdf ._fea < 0{_gdf ._bca =make ([]byte ,len (_gdf ._bca ));_gdf ._fea =len (_gdf ._bca )-3;
};return nil ;};func (_dcc *Decoder )createLittleEndianTable (_eef [][3]int )([]*code ,error ){_gdc :=make ([]*code ,_dg +1);for _be :=0;_be < len (_eef );_be ++{_dccd :=_ff (_eef [_be ]);if _dccd ._a <=_fcd {_bed :=_fcd -_dccd ._a ;_gbe :=_dccd ._fd <<uint (_bed );
for _da :=(1<<uint (_bed ))-1;_da >=0;_da --{_ega :=_gbe |_da ;_gdc [_ega ]=_dccd ;};}else {_ec :=_dccd ._fd >>uint (_dccd ._a -_fcd );if _gdc [_ec ]==nil {var _eggc =_ff ([3]int {});_eggc ._eb =make ([]*code ,_cd +1);_gdc [_ec ]=_eggc ;};if _dccd ._a <=_fcd +_dfa {_gca :=_fcd +_dfa -_dccd ._a ;
_dfdf :=(_dccd ._fd <<uint (_gca ))&_cd ;_gdc [_ec ]._dc =true ;for _bgad :=(1<<uint (_gca ))-1;_bgad >=0;_bgad --{_gdc [_ec ]._eb [_dfdf |_bgad ]=_dccd ;};}else {return nil ,_ef .New ("\u0043\u006f\u0064\u0065\u0020\u0074a\u0062\u006c\u0065\u0020\u006f\u0076\u0065\u0072\u0066\u006c\u006f\u0077\u0020i\u006e\u0020\u004d\u004d\u0052\u0044\u0065c\u006f\u0064\u0065\u0072");
};};};return _gdc ,nil ;};func (_aec *runData )uncompressGetNextCodeLittleEndian ()(int ,error ){_gac :=_aec ._ccc -_aec ._dcg ;if _gac < 0||_gac > 24{_bgg :=(_aec ._ccc >>3)-_aec ._ecd ;if _bgg >=_aec ._fea {_bgg +=_aec ._ecd ;if _cg :=_aec .fillBuffer (_bgg );
_cg !=nil {return 0,_cg ;};_bgg -=_aec ._ecd ;};_ade :=(uint32 (_aec ._bca [_bgg ]&0xFF)<<16)|(uint32 (_aec ._bca [_bgg +1]&0xFF)<<8)|(uint32 (_aec ._bca [_bgg +2]&0xFF));_aeg :=uint32 (_aec ._ccc &7);_ade <<=_aeg ;_aec ._agfc =int (_ade );}else {_bcae :=_aec ._dcg &7;
_dbf :=7-_bcae ;if _gac <=_dbf {_aec ._agfc <<=uint (_gac );}else {_eead :=(_aec ._dcg >>3)+3-_aec ._ecd ;if _eead >=_aec ._fea {_eead +=_aec ._ecd ;if _ecbc :=_aec .fillBuffer (_eead );_ecbc !=nil {return 0,_ecbc ;};_eead -=_aec ._ecd ;};_bcae =8-_bcae ;
for {_aec ._agfc <<=uint (_bcae );_aec ._agfc |=int (uint (_aec ._bca [_eead ])&0xFF);_gac -=_bcae ;_eead ++;_bcae =8;if !(_gac >=8){break ;};};_aec ._agfc <<=uint (_gac );};};_aec ._dcg =_aec ._ccc ;return _aec ._agfc ,nil ;};func (_bedc *Decoder )uncompress1d (_gcf *runData ,_ebe []int ,_cef int )(int ,error ){var (_egf =true ;
_gaa int ;_dgf *code ;_gba int ;_eag error ;);_gbfe :for _gaa < _cef {_gfe :for {if _egf {_dgf ,_eag =_gcf .uncompressGetCode (_bedc ._gc );if _eag !=nil {return 0,_eag ;};}else {_dgf ,_eag =_gcf .uncompressGetCode (_bedc ._cfa );if _eag !=nil {return 0,_eag ;
};};_gcf ._ccc +=_dgf ._a ;if _dgf ._fc < 0{break _gbfe ;};_gaa +=_dgf ._fc ;if _dgf ._fc < 64{_egf =!_egf ;_ebe [_gba ]=_gaa ;_gba ++;break _gfe ;};};};if _ebe [_gba ]!=_cef {_ebe [_gba ]=_cef ;};_eec :=EOL ;if _dgf !=nil &&_dgf ._fc !=EOL {_eec =_gba ;
};return _eec ,nil ;};func (_bcad *runData )uncompressGetCodeLittleEndian (_cdc []*code )(*code ,error ){_bgc ,_aefg :=_bcad .uncompressGetNextCodeLittleEndian ();if _aefg !=nil {_fa .Log .Debug ("\u0055n\u0063\u006fm\u0070\u0072\u0065\u0073s\u0047\u0065\u0074N\u0065\u0078\u0074\u0043\u006f\u0064\u0065\u004c\u0069tt\u006c\u0065\u0045n\u0064\u0069a\u006e\u0020\u0066\u0061\u0069\u006ce\u0064\u003a \u0025\u0076",_aefg );
return nil ,_aefg ;};_bgc &=0xffffff;_daf :=_bgc >>(_geb -_fcd );_bff :=_cdc [_daf ];if _bff !=nil &&_bff ._dc {_daf =(_bgc >>(_geb -_fcd -_dfa ))&_cd ;_bff =_bff ._eb [_daf ];};return _bff ,nil ;};const (_gcd int =1024<<7;_dfg int =3;_geb uint =24;);func (_fddc *runData )align (){_fddc ._ccc =((_fddc ._ccc +7)>>3)<<3};
func _de (_c ,_dd int )int {if _c > _dd {return _dd ;};return _c ;};func _ff (_bb [3]int )*code {return &code {_a :_bb [0],_fd :_bb [1],_fc :_bb [2]}};type code struct{_a int ;_fd int ;_fc int ;_eb []*code ;_dc bool ;};