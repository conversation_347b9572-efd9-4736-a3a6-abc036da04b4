//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package decoder ;import (_cb "github.com/unidoc/unipdf/v3/internal/bitwise";_g "github.com/unidoc/unipdf/v3/internal/jbig2/bitmap";_cg "github.com/unidoc/unipdf/v3/internal/jbig2/document";_e "github.com/unidoc/unipdf/v3/internal/jbig2/errors";_f "image";
);func (_gc *Decoder )PageNumber ()(int ,error ){const _fda ="\u0044e\u0063o\u0064\u0065\u0072\u002e\u0050a\u0067\u0065N\u0075\u006d\u0062\u0065\u0072";if _gc ._b ==nil {return 0,_e .Error (_fda ,"d\u0065\u0063\u006f\u0064\u0065\u0072 \u006e\u006f\u0074\u0020\u0069\u006e\u0069\u0074\u0069a\u006c\u0069\u007ae\u0064 \u0079\u0065\u0074");
};return int (_gc ._b .NumberOfPages ),nil ;};func (_gf *Decoder )DecodePageImage (pageNumber int )(_f .Image ,error ){const _fb ="\u0064\u0065\u0063od\u0065\u0072\u002e\u0044\u0065\u0063\u006f\u0064\u0065\u0050\u0061\u0067\u0065\u0049\u006d\u0061\u0067\u0065";
_fd ,_ec :=_gf .decodePageImage (pageNumber );if _ec !=nil {return nil ,_e .Wrap (_ec ,_fb ,"");};return _fd ,nil ;};func (_ff *Decoder )DecodeNextPage ()([]byte ,error ){_ff ._d ++;_dg :=_ff ._d ;return _ff .decodePage (_dg );};func (_gga *Decoder )decodePageImage (_ea int )(_f .Image ,error ){const _ce ="\u0064e\u0063o\u0064\u0065\u0050\u0061\u0067\u0065\u0049\u006d\u0061\u0067\u0065";
if _ea < 0{return nil ,_e .Errorf (_ce ,"\u0069n\u0076\u0061\u006c\u0069d\u0020\u0070\u0061\u0067\u0065 \u006eu\u006db\u0065\u0072\u003a\u0020\u0027\u0025\u0064'",_ea );};if _ea > int (_gga ._b .NumberOfPages ){return nil ,_e .Errorf (_ce ,"p\u0061\u0067\u0065\u003a\u0020\u0027%\u0064\u0027\u0020\u006e\u006f\u0074 \u0066\u006f\u0075\u006e\u0064\u0020\u0069n\u0020\u0074\u0068\u0065\u0020\u0064\u0065\u0063\u006f\u0064e\u0072",_ea );
};_bcc ,_gfd :=_gga ._b .GetPage (_ea );if _gfd !=nil {return nil ,_e .Wrap (_gfd ,_ce ,"");};_cf ,_gfd :=_bcc .GetBitmap ();if _gfd !=nil {return nil ,_e .Wrap (_gfd ,_ce ,"");};_cf .InverseData ();return _cf .ToImage (),nil ;};type Parameters struct{UnpaddedData bool ;
Color _g .Color ;};func Decode (input []byte ,parameters Parameters ,globals *_cg .Globals )(*Decoder ,error ){_ag :=_cb .NewReader (input );_gb ,_df :=_cg .DecodeDocument (_ag ,globals );if _df !=nil {return nil ,_df ;};return &Decoder {_ed :_ag ,_b :_gb ,_fc :parameters },nil ;
};type Decoder struct{_ed *_cb .Reader ;_b *_cg .Document ;_d int ;_fc Parameters ;};func (_db *Decoder )DecodePage (pageNumber int )([]byte ,error ){return _db .decodePage (pageNumber )};func (_a *Decoder )decodePage (_gg int )([]byte ,error ){const _ab ="\u0064\u0065\u0063\u006f\u0064\u0065\u0050\u0061\u0067\u0065";
if _gg < 0{return nil ,_e .Errorf (_ab ,"\u0069n\u0076\u0061\u006c\u0069d\u0020\u0070\u0061\u0067\u0065 \u006eu\u006db\u0065\u0072\u003a\u0020\u0027\u0025\u0064'",_gg );};if _gg > int (_a ._b .NumberOfPages ){return nil ,_e .Errorf (_ab ,"p\u0061\u0067\u0065\u003a\u0020\u0027%\u0064\u0027\u0020\u006e\u006f\u0074 \u0066\u006f\u0075\u006e\u0064\u0020\u0069n\u0020\u0074\u0068\u0065\u0020\u0064\u0065\u0063\u006f\u0064e\u0072",_gg );
};_dgb ,_bc :=_a ._b .GetPage (_gg );if _bc !=nil {return nil ,_e .Wrap (_bc ,_ab ,"");};_bd ,_bc :=_dgb .GetBitmap ();if _bc !=nil {return nil ,_e .Wrap (_bc ,_ab ,"");};_bd .InverseData ();if !_a ._fc .UnpaddedData {return _bd .Data ,nil ;};return _bd .GetUnpaddedData ();
};