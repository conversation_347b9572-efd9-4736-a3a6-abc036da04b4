//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package huffman ;import (_da "errors";_ad "fmt";_g "github.com/unidoc/unipdf/v3/internal/bitwise";_e "github.com/unidoc/unipdf/v3/internal/jbig2/internal";_gc "math";_d "strings";);type Code struct{_efb int32 ;_dbg int32 ;_ffe int32 ;_acg bool ;_bfd int32 ;
};func (_c *EncodedTable )RootNode ()*InternalNode {return _c ._db };func _ggc (_af *Code )*ValueNode {return &ValueNode {_fa :_af ._dbg ,_bdf :_af ._ffe ,_aab :_af ._acg }};func NewFixedSizeTable (codeTable []*Code )(*FixedSizeTable ,error ){_eg :=&FixedSizeTable {_dc :&InternalNode {}};
if _ac :=_eg .InitTree (codeTable );_ac !=nil {return nil ,_ac ;};return _eg ,nil ;};var _ Node =&InternalNode {};func (_gd *InternalNode )pad (_gde *_d .Builder ){for _cef :=int32 (0);_cef < _gd ._cd ;_cef ++{_gde .WriteString ("\u0020\u0020\u0020");};
};func (_cee *StandardTable )String ()string {return _cee ._faf .String ()+"\u000a"};func (_cag *ValueNode )Decode (r *_g .Reader )(int64 ,error ){_ec ,_egb :=r .ReadBits (byte (_cag ._fa ));if _egb !=nil {return 0,_egb ;};if _cag ._aab {_ec =-_ec ;};return int64 (_cag ._bdf )+int64 (_ec ),nil ;
};func (_cfg *FixedSizeTable )RootNode ()*InternalNode {return _cfg ._dc };func _fg (_gcag []*Code ){var _gfg int32 ;for _ ,_fad :=range _gcag {_gfg =_ggg (_gfg ,_fad ._efb );};_ecd :=make ([]int32 ,_gfg +1);for _ ,_fdc :=range _gcag {_ecd [_fdc ._efb ]++;
};var _aaa int32 ;_geae :=make ([]int32 ,len (_ecd )+1);_ecd [0]=0;for _bbe :=int32 (1);_bbe <=int32 (len (_ecd ));_bbe ++{_geae [_bbe ]=(_geae [_bbe -1]+(_ecd [_bbe -1]))<<1;_aaa =_geae [_bbe ];for _ ,_eaa :=range _gcag {if _eaa ._efb ==_bbe {_eaa ._bfd =_aaa ;
_aaa ++;};};};};type BasicTabler interface{HtHigh ()int32 ;HtLow ()int32 ;StreamReader ()*_g .Reader ;HtPS ()int32 ;HtRS ()int32 ;HtOOB ()int32 ;};func (_dg *InternalNode )Decode (r *_g .Reader )(int64 ,error ){_gea ,_cg :=r .ReadBit ();if _cg !=nil {return 0,_cg ;
};if _gea ==0{return _dg ._ee .Decode (r );};return _dg ._bg .Decode (r );};func (_dd *InternalNode )String ()string {_fbc :=&_d .Builder {};_fbc .WriteString ("\u000a");_dd .pad (_fbc );_fbc .WriteString ("\u0030\u003a\u0020");_fbc .WriteString (_dd ._ee .String ()+"\u000a");
_dd .pad (_fbc );_fbc .WriteString ("\u0031\u003a\u0020");_fbc .WriteString (_dd ._bg .String ()+"\u000a");return _fbc .String ();};func (_ff *ValueNode )String ()string {return _ad .Sprintf ("\u0025\u0064\u002f%\u0064",_ff ._fa ,_ff ._bdf );};type ValueNode struct{_fa int32 ;
_bdf int32 ;_aab bool ;};var _fda =[][][]int32 {{{1,4,0},{2,8,16},{3,16,272},{3,32,65808}},{{1,0,0},{2,0,1},{3,0,2},{4,3,3},{5,6,11},{6,32,75},{6,-1,0}},{{8,8,-256},{1,0,0},{2,0,1},{3,0,2},{4,3,3},{5,6,11},{8,32,-257,999},{7,32,75},{6,-1,0}},{{1,0,1},{2,0,2},{3,0,3},{4,3,4},{5,6,12},{5,32,76}},{{7,8,-255},{1,0,1},{2,0,2},{3,0,3},{4,3,4},{5,6,12},{7,32,-256,999},{6,32,76}},{{5,10,-2048},{4,9,-1024},{4,8,-512},{4,7,-256},{5,6,-128},{5,5,-64},{4,5,-32},{2,7,0},{3,7,128},{3,8,256},{4,9,512},{4,10,1024},{6,32,-2049,999},{6,32,2048}},{{4,9,-1024},{3,8,-512},{4,7,-256},{5,6,-128},{5,5,-64},{4,5,-32},{4,5,0},{5,5,32},{5,6,64},{4,7,128},{3,8,256},{3,9,512},{3,10,1024},{5,32,-1025,999},{5,32,2048}},{{8,3,-15},{9,1,-7},{8,1,-5},{9,0,-3},{7,0,-2},{4,0,-1},{2,1,0},{5,0,2},{6,0,3},{3,4,4},{6,1,20},{4,4,22},{4,5,38},{5,6,70},{5,7,134},{6,7,262},{7,8,390},{6,10,646},{9,32,-16,999},{9,32,1670},{2,-1,0}},{{8,4,-31},{9,2,-15},{8,2,-11},{9,1,-7},{7,1,-5},{4,1,-3},{3,1,-1},{3,1,1},{5,1,3},{6,1,5},{3,5,7},{6,2,39},{4,5,43},{4,6,75},{5,7,139},{5,8,267},{6,8,523},{7,9,779},{6,11,1291},{9,32,-32,999},{9,32,3339},{2,-1,0}},{{7,4,-21},{8,0,-5},{7,0,-4},{5,0,-3},{2,2,-2},{5,0,2},{6,0,3},{7,0,4},{8,0,5},{2,6,6},{5,5,70},{6,5,102},{6,6,134},{6,7,198},{6,8,326},{6,9,582},{6,10,1094},{7,11,2118},{8,32,-22,999},{8,32,4166},{2,-1,0}},{{1,0,1},{2,1,2},{4,0,4},{4,1,5},{5,1,7},{5,2,9},{6,2,13},{7,2,17},{7,3,21},{7,4,29},{7,5,45},{7,6,77},{7,32,141}},{{1,0,1},{2,0,2},{3,1,3},{5,0,5},{5,1,6},{6,1,8},{7,0,10},{7,1,11},{7,2,13},{7,3,17},{7,4,25},{8,5,41},{8,32,73}},{{1,0,1},{3,0,2},{4,0,3},{5,0,4},{4,1,5},{3,3,7},{6,1,15},{6,2,17},{6,3,21},{6,4,29},{6,5,45},{7,6,77},{7,32,141}},{{3,0,-2},{3,0,-1},{1,0,0},{3,0,1},{3,0,2}},{{7,4,-24},{6,2,-8},{5,1,-4},{4,0,-2},{3,0,-1},{1,0,0},{3,0,1},{4,0,2},{5,1,3},{6,2,5},{7,4,9},{7,32,-25,999},{7,32,25}}};
func (_ce *OutOfBandNode )String ()string {return _ad .Sprintf ("\u0025\u0030\u00364\u0062",int64 (_gc .MaxInt64 ));};func (_ggd *StandardTable )InitTree (codeTable []*Code )error {_fg (codeTable );for _ ,_dbc :=range codeTable {if _bc :=_ggd ._faf .append (_dbc );
_bc !=nil {return _bc ;};};return nil ;};func (_acf *OutOfBandNode )Decode (r *_g .Reader )(int64 ,error ){return 0,_e .ErrOOB };var _ Node =&ValueNode {};type EncodedTable struct{BasicTabler ;_db *InternalNode ;};type OutOfBandNode struct{};func (_dde *InternalNode )append (_cce *Code )(_fe error ){if _cce ._efb ==0{return nil ;
};_ba :=_cce ._efb -1-_dde ._cd ;if _ba < 0{return _da .New ("\u004e\u0065\u0067\u0061\u0074\u0069\u0076\u0065\u0020\u0073\u0068\u0069\u0066\u0074\u0069n\u0067 \u0069\u0073\u0020\u006e\u006f\u0074\u0020\u0061\u006c\u006c\u006f\u0077\u0065\u0064");};_ffd :=(_cce ._bfd >>uint (_ba ))&0x1;
if _ba ==0{if _cce ._dbg ==-1{if _ffd ==1{if _dde ._bg !=nil {return _ad .Errorf ("O\u004f\u0042\u0020\u0061\u006c\u0072e\u0061\u0064\u0079\u0020\u0073\u0065\u0074\u0020\u0066o\u0072\u0020\u0063o\u0064e\u0020\u0025\u0073",_cce );};_dde ._bg =_gac (_cce );
}else {if _dde ._ee !=nil {return _ad .Errorf ("O\u004f\u0042\u0020\u0061\u006c\u0072e\u0061\u0064\u0079\u0020\u0073\u0065\u0074\u0020\u0066o\u0072\u0020\u0063o\u0064e\u0020\u0025\u0073",_cce );};_dde ._ee =_gac (_cce );};}else {if _ffd ==1{if _dde ._bg !=nil {return _ad .Errorf ("\u0056\u0061\u006cue\u0020\u004e\u006f\u0064\u0065\u0020\u0061\u006c\u0072e\u0061d\u0079 \u0073e\u0074\u0020\u0066\u006f\u0072\u0020\u0063\u006f\u0064\u0065\u0020\u0025\u0073",_cce );
};_dde ._bg =_ggc (_cce );}else {if _dde ._ee !=nil {return _ad .Errorf ("\u0056\u0061\u006cue\u0020\u004e\u006f\u0064\u0065\u0020\u0061\u006c\u0072e\u0061d\u0079 \u0073e\u0074\u0020\u0066\u006f\u0072\u0020\u0063\u006f\u0064\u0065\u0020\u0025\u0073",_cce );
};_dde ._ee =_ggc (_cce );};};}else {if _ffd ==1{if _dde ._bg ==nil {_dde ._bg =_cb (_dde ._cd +1);};if _fe =_dde ._bg .(*InternalNode ).append (_cce );_fe !=nil {return _fe ;};}else {if _dde ._ee ==nil {_dde ._ee =_cb (_dde ._cd +1);};if _fe =_dde ._ee .(*InternalNode ).append (_cce );
_fe !=nil {return _fe ;};};};return nil ;};func (_bff *FixedSizeTable )String ()string {return _bff ._dc .String ()+"\u000a"};func (_dca *StandardTable )Decode (r *_g .Reader )(int64 ,error ){return _dca ._faf .Decode (r )};func (_fc *EncodedTable )InitTree (codeTable []*Code )error {_fg (codeTable );
for _ ,_aa :=range codeTable {if _b :=_fc ._db .append (_aa );_b !=nil {return _b ;};};return nil ;};func (_gg *EncodedTable )parseTable ()error {var (_eb []*Code ;_gb ,_gga ,_bb int32 ;_gf uint64 ;_bfa error ;);_be :=_gg .StreamReader ();_ca :=_gg .HtLow ();
for _ca < _gg .HtHigh (){_gf ,_bfa =_be .ReadBits (byte (_gg .HtPS ()));if _bfa !=nil {return _bfa ;};_gb =int32 (_gf );_gf ,_bfa =_be .ReadBits (byte (_gg .HtRS ()));if _bfa !=nil {return _bfa ;};_gga =int32 (_gf );_eb =append (_eb ,NewCode (_gb ,_gga ,_bb ,false ));
_ca +=1<<uint (_gga );};_gf ,_bfa =_be .ReadBits (byte (_gg .HtPS ()));if _bfa !=nil {return _bfa ;};_gb =int32 (_gf );_gga =32;_bb =_gg .HtLow ()-1;_eb =append (_eb ,NewCode (_gb ,_gga ,_bb ,true ));_gf ,_bfa =_be .ReadBits (byte (_gg .HtPS ()));if _bfa !=nil {return _bfa ;
};_gb =int32 (_gf );_gga =32;_bb =_gg .HtHigh ();_eb =append (_eb ,NewCode (_gb ,_gga ,_bb ,false ));if _gg .HtOOB ()==1{_gf ,_bfa =_be .ReadBits (byte (_gg .HtPS ()));if _bfa !=nil {return _bfa ;};_gb =int32 (_gf );_eb =append (_eb ,NewCode (_gb ,-1,-1,false ));
};if _bfa =_gg .InitTree (_eb );_bfa !=nil {return _bfa ;};return nil ;};func _gac (_fb *Code )*OutOfBandNode {return &OutOfBandNode {}};func (_baf *Code )String ()string {var _fcf string ;if _baf ._bfd !=-1{_fcf =_ddg (_baf ._bfd ,_baf ._efb );}else {_fcf ="\u003f";
};return _ad .Sprintf ("%\u0073\u002f\u0025\u0064\u002f\u0025\u0064\u002f\u0025\u0064",_fcf ,_baf ._efb ,_baf ._dbg ,_baf ._ffe );};func (_bf *EncodedTable )String ()string {return _bf ._db .String ()+"\u000a"};var _cea =make ([]Tabler ,len (_fda ));type Tabler interface{Decode (_baa *_g .Reader )(int64 ,error );
InitTree (_df []*Code )error ;String ()string ;RootNode ()*InternalNode ;};var _ Tabler =&EncodedTable {};func (_fae *StandardTable )RootNode ()*InternalNode {return _fae ._faf };type FixedSizeTable struct{_dc *InternalNode };type InternalNode struct{_cd int32 ;
_ee Node ;_bg Node ;};func _ggg (_gcf ,_dbcb int32 )int32 {if _gcf > _dbcb {return _gcf ;};return _dbcb ;};func (_ada *EncodedTable )Decode (r *_g .Reader )(int64 ,error ){return _ada ._db .Decode (r )};func NewCode (prefixLength ,rangeLength ,rangeLow int32 ,isLowerRange bool )*Code {return &Code {_efb :prefixLength ,_dbg :rangeLength ,_ffe :rangeLow ,_acg :isLowerRange ,_bfd :-1};
};func _dac (_egg [][]int32 )(*StandardTable ,error ){var _bef []*Code ;for _geg :=0;_geg < len (_egg );_geg ++{_bda :=_egg [_geg ][0];_dcd :=_egg [_geg ][1];_gff :=_egg [_geg ][2];var _gba bool ;if len (_egg [_geg ])> 3{_gba =true ;};_bef =append (_bef ,NewCode (_bda ,_dcd ,_gff ,_gba ));
};_ef :=&StandardTable {_faf :_cb (0)};if _ecc :=_ef .InitTree (_bef );_ecc !=nil {return nil ,_ecc ;};return _ef ,nil ;};func GetStandardTable (number int )(Tabler ,error ){if number <=0||number > len (_cea ){return nil ,_da .New ("\u0049n\u0064e\u0078\u0020\u006f\u0075\u0074 \u006f\u0066 \u0072\u0061\u006e\u0067\u0065");
};_agd :=_cea [number -1];if _agd ==nil {var _ea error ;_agd ,_ea =_dac (_fda [number -1]);if _ea !=nil {return nil ,_ea ;};_cea [number -1]=_agd ;};return _agd ,nil ;};type StandardTable struct{_faf *InternalNode };func NewEncodedTable (table BasicTabler )(*EncodedTable ,error ){_fd :=&EncodedTable {_db :&InternalNode {},BasicTabler :table };
if _gcd :=_fd .parseTable ();_gcd !=nil {return nil ,_gcd ;};return _fd ,nil ;};func (_bd *FixedSizeTable )Decode (r *_g .Reader )(int64 ,error ){return _bd ._dc .Decode (r )};var _ Node =&OutOfBandNode {};func _cb (_dae int32 )*InternalNode {return &InternalNode {_cd :_dae }};
func _ddg (_cga ,_ed int32 )string {var _fab int32 ;_beg :=make ([]rune ,_ed );for _gca :=int32 (1);_gca <=_ed ;_gca ++{_fab =_cga >>uint (_ed -_gca )&1;if _fab !=0{_beg [_gca -1]='1';}else {_beg [_gca -1]='0';};};return string (_beg );};type Node interface{Decode (_cac *_g .Reader )(int64 ,error );
String ()string ;};func (_ga *FixedSizeTable )InitTree (codeTable []*Code )error {_fg (codeTable );for _ ,_cf :=range codeTable {_bfb :=_ga ._dc .append (_cf );if _bfb !=nil {return _bfb ;};};return nil ;};