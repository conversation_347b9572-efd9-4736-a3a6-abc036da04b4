//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package segments ;import (_da "encoding/binary";_ad "errors";_b "fmt";_ac "github.com/unidoc/unipdf/v3/common";_af "github.com/unidoc/unipdf/v3/internal/bitwise";_ae "github.com/unidoc/unipdf/v3/internal/jbig2/basic";_f "github.com/unidoc/unipdf/v3/internal/jbig2/bitmap";
_afd "github.com/unidoc/unipdf/v3/internal/jbig2/decoder/arithmetic";_eb "github.com/unidoc/unipdf/v3/internal/jbig2/decoder/huffman";_c "github.com/unidoc/unipdf/v3/internal/jbig2/decoder/mmr";_gd "github.com/unidoc/unipdf/v3/internal/jbig2/encoder/arithmetic";
_gf "github.com/unidoc/unipdf/v3/internal/jbig2/errors";_gb "github.com/unidoc/unipdf/v3/internal/jbig2/internal";_e "image";_ge "io";_g "math";_a "strings";_eg "time";);func (_bbfa *SymbolDictionary )getToExportFlags ()([]int ,error ){var (_ggab int ;
_acdag int32 ;_dbge error ;_cedb =int32 (_bbfa ._gagg +_bbfa .NumberOfNewSymbols );_adcg =make ([]int ,_cedb ););for _bbff :=int32 (0);_bbff < _cedb ;_bbff +=_acdag {if _bbfa .IsHuffmanEncoded {_ddag ,_ggbd :=_eb .GetStandardTable (1);if _ggbd !=nil {return nil ,_ggbd ;
};_cddb ,_ggbd :=_ddag .Decode (_bbfa ._ecdbb );if _ggbd !=nil {return nil ,_ggbd ;};_acdag =int32 (_cddb );}else {_acdag ,_dbge =_bbfa ._edffg .DecodeInt (_bbfa ._fgde );if _dbge !=nil {return nil ,_dbge ;};};if _acdag !=0{if _bbff +_acdag > _cedb {return nil ,_gf .Error ("\u0053\u0079\u006d\u0062\u006f\u006cD\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u002e\u0067\u0065\u0074T\u006f\u0045\u0078\u0070\u006f\u0072\u0074F\u006c\u0061\u0067\u0073","\u006d\u0061\u006c\u0066\u006f\u0072m\u0065\u0064\u0020\u0069\u006e\u0070\u0075\u0074\u0020\u0064\u0061\u0074\u0061\u0020\u0070\u0072\u006f\u0076\u0069\u0064e\u0064\u002e\u0020\u0069\u006e\u0064\u0065\u0078\u0020\u006f\u0075\u0074\u0020\u006ff\u0020r\u0061\u006e\u0067\u0065");
};for _acbc :=_bbff ;_acbc < _bbff +_acdag ;_acbc ++{_adcg [_acbc ]=_ggab ;};};if _ggab ==0{_ggab =1;}else {_ggab =0;};};return _adcg ,nil ;};func (_gffb *GenericRefinementRegion )setParameters (_eaf *_afd .DecoderStats ,_bed *_afd .Decoder ,_cdbf int8 ,_eebe ,_age uint32 ,_egf *_f .Bitmap ,_gdd ,_gec int32 ,_fdaf bool ,_ace []int8 ,_bbdc []int8 ){_ac .Log .Trace ("\u005b\u0047\u0045NE\u0052\u0049\u0043\u002d\u0052\u0045\u0046\u002d\u0052E\u0047I\u004fN\u005d \u0073\u0065\u0074\u0050\u0061\u0072\u0061\u006d\u0065\u0074\u0065\u0072\u0073");
if _eaf !=nil {_gffb ._ceb =_eaf ;};if _bed !=nil {_gffb ._ec =_bed ;};_gffb .TemplateID =_cdbf ;_gffb .RegionInfo .BitmapWidth =_eebe ;_gffb .RegionInfo .BitmapHeight =_age ;_gffb .ReferenceBitmap =_egf ;_gffb .ReferenceDX =_gdd ;_gffb .ReferenceDY =_gec ;
_gffb .IsTPGROn =_fdaf ;_gffb .GrAtX =_ace ;_gffb .GrAtY =_bbdc ;_gffb .RegionBitmap =nil ;_ac .Log .Trace ("[\u0047\u0045\u004e\u0045\u0052\u0049\u0043\u002d\u0052E\u0046\u002d\u0052\u0045\u0047\u0049\u004fN]\u0020\u0073\u0065\u0074P\u0061\u0072\u0061\u006d\u0065\u0074\u0065\u0072\u0073 f\u0069\u006ei\u0073\u0068\u0065\u0064\u002e\u0020\u0025\u0073",_gffb );
};func (_daca *SymbolDictionary )decodeDifferenceWidth ()(int64 ,error ){if _daca .IsHuffmanEncoded {switch _daca .SdHuffDecodeWidthSelection {case 0:_cefe ,_edad :=_eb .GetStandardTable (2);if _edad !=nil {return 0,_edad ;};return _cefe .Decode (_daca ._ecdbb );
case 1:_fcdg ,_accc :=_eb .GetStandardTable (3);if _accc !=nil {return 0,_accc ;};return _fcdg .Decode (_daca ._ecdbb );case 3:if _daca ._dcdd ==nil {var _fbgf int ;if _daca .SdHuffDecodeHeightSelection ==3{_fbgf ++;};_gcfe ,_caea :=_daca .getUserTable (_fbgf );
if _caea !=nil {return 0,_caea ;};_daca ._dcdd =_gcfe ;};return _daca ._dcdd .Decode (_daca ._ecdbb );};}else {_ffbg ,_fbe :=_daca ._edffg .DecodeInt (_daca ._ebdd );if _fbe !=nil {return 0,_fbe ;};return int64 (_ffbg ),nil ;};return 0,nil ;};func (_cffb *GenericRegion )overrideAtTemplate0a (_aee ,_dcgg ,_cbde ,_edca ,_eda ,_gaee int )int {if _cffb .GBAtOverride [0]{_aee &=0xFFEF;
if _cffb .GBAtY [0]==0&&_cffb .GBAtX [0]>=-int8 (_eda ){_aee |=(_edca >>uint (int8 (_gaee )-_cffb .GBAtX [0]&0x1))<<4;}else {_aee |=int (_cffb .getPixel (_dcgg +int (_cffb .GBAtX [0]),_cbde +int (_cffb .GBAtY [0])))<<4;};};if _cffb .GBAtOverride [1]{_aee &=0xFBFF;
if _cffb .GBAtY [1]==0&&_cffb .GBAtX [1]>=-int8 (_eda ){_aee |=(_edca >>uint (int8 (_gaee )-_cffb .GBAtX [1]&0x1))<<10;}else {_aee |=int (_cffb .getPixel (_dcgg +int (_cffb .GBAtX [1]),_cbde +int (_cffb .GBAtY [1])))<<10;};};if _cffb .GBAtOverride [2]{_aee &=0xF7FF;
if _cffb .GBAtY [2]==0&&_cffb .GBAtX [2]>=-int8 (_eda ){_aee |=(_edca >>uint (int8 (_gaee )-_cffb .GBAtX [2]&0x1))<<11;}else {_aee |=int (_cffb .getPixel (_dcgg +int (_cffb .GBAtX [2]),_cbde +int (_cffb .GBAtY [2])))<<11;};};if _cffb .GBAtOverride [3]{_aee &=0x7FFF;
if _cffb .GBAtY [3]==0&&_cffb .GBAtX [3]>=-int8 (_eda ){_aee |=(_edca >>uint (int8 (_gaee )-_cffb .GBAtX [3]&0x1))<<15;}else {_aee |=int (_cffb .getPixel (_dcgg +int (_cffb .GBAtX [3]),_cbde +int (_cffb .GBAtY [3])))<<15;};};return _aee ;};func (_dcfgf *TextRegion )setContexts (_fbce *_afd .DecoderStats ,_afec *_afd .DecoderStats ,_aeefa *_afd .DecoderStats ,_gefac *_afd .DecoderStats ,_efaaf *_afd .DecoderStats ,_acab *_afd .DecoderStats ,_fcgd *_afd .DecoderStats ,_bgcd *_afd .DecoderStats ,_ddfb *_afd .DecoderStats ,_agcd *_afd .DecoderStats ){_dcfgf ._bace =_afec ;
_dcfgf ._deggc =_aeefa ;_dcfgf ._bageg =_gefac ;_dcfgf ._adge =_efaaf ;_dcfgf ._cdfe =_fcgd ;_dcfgf ._egcg =_bgcd ;_dcfgf ._bea =_acab ;_dcfgf ._gffge =_ddfb ;_dcfgf ._gfae =_agcd ;_dcfgf ._ceed =_fbce ;};func (_dcge *TextRegion )symbolIDCodeLengths ()error {var (_cadfc []*_eb .Code ;
_fcac uint64 ;_ebbf _eb .Tabler ;_aaaa error ;);for _bbcbf :=0;_bbcbf < 35;_bbcbf ++{_fcac ,_aaaa =_dcge ._bbbd .ReadBits (4);if _aaaa !=nil {return _aaaa ;};_bgce :=int (_fcac &0xf);if _bgce > 0{_cadfc =append (_cadfc ,_eb .NewCode (int32 (_bgce ),0,int32 (_bbcbf ),false ));
};};_ebbf ,_aaaa =_eb .NewFixedSizeTable (_cadfc );if _aaaa !=nil {return _aaaa ;};var (_ffad int64 ;_bcffc uint32 ;_fbae []*_eb .Code ;_cgfg int64 ;);for _bcffc < _dcge .NumberOfSymbols {_cgfg ,_aaaa =_ebbf .Decode (_dcge ._bbbd );if _aaaa !=nil {return _aaaa ;
};if _cgfg < 32{if _cgfg > 0{_fbae =append (_fbae ,_eb .NewCode (int32 (_cgfg ),0,int32 (_bcffc ),false ));};_ffad =_cgfg ;_bcffc ++;}else {var _bccb ,_dbad int64 ;switch _cgfg {case 32:_fcac ,_aaaa =_dcge ._bbbd .ReadBits (2);if _aaaa !=nil {return _aaaa ;
};_bccb =3+int64 (_fcac );if _bcffc > 0{_dbad =_ffad ;};case 33:_fcac ,_aaaa =_dcge ._bbbd .ReadBits (3);if _aaaa !=nil {return _aaaa ;};_bccb =3+int64 (_fcac );case 34:_fcac ,_aaaa =_dcge ._bbbd .ReadBits (7);if _aaaa !=nil {return _aaaa ;};_bccb =11+int64 (_fcac );
};for _gfeb :=0;_gfeb < int (_bccb );_gfeb ++{if _dbad > 0{_fbae =append (_fbae ,_eb .NewCode (int32 (_dbad ),0,int32 (_bcffc ),false ));};_bcffc ++;};};};_dcge ._bbbd .Align ();_dcge ._bdea ,_aaaa =_eb .NewFixedSizeTable (_fbae );return _aaaa ;};func (_gcb *HalftoneRegion )computeGrayScalePlanes (_bcbg []*_f .Bitmap ,_degf int )([][]int ,error ){_dcaa :=make ([][]int ,_gcb .HGridHeight );
for _bbee :=0;_bbee < len (_dcaa );_bbee ++{_dcaa [_bbee ]=make ([]int ,_gcb .HGridWidth );};for _affe :=0;_affe < int (_gcb .HGridHeight );_affe ++{for _dad :=0;_dad < int (_gcb .HGridWidth );_dad +=8{var _cfac int ;if _eegd :=int (_gcb .HGridWidth )-_dad ;
_eegd > 8{_cfac =8;}else {_cfac =_eegd ;};_bcddf :=_bcbg [0].GetByteIndex (_dad ,_affe );for _fgf :=0;_fgf < _cfac ;_fgf ++{_ffca :=_fgf +_dad ;_dcaa [_affe ][_ffca ]=0;for _ceabf :=0;_ceabf < _degf ;_ceabf ++{_eec ,_cfff :=_bcbg [_ceabf ].GetByte (_bcddf );
if _cfff !=nil {return nil ,_cfff ;};_egd :=_eec >>uint (7-_ffca &7);_abcf :=_egd &1;_gaad :=1<<uint (_ceabf );_cgg :=int (_abcf )*_gaad ;_dcaa [_affe ][_ffca ]+=_cgg ;};};};};return _dcaa ,nil ;};func (_cgec *PageInformationSegment )readRequiresAuxiliaryBuffer ()error {_bdf ,_ebgc :=_cgec ._fccb .ReadBit ();
if _ebgc !=nil {return _ebgc ;};if _bdf ==1{_cgec ._bgca =true ;};return nil ;};func (_edb *SymbolDictionary )Encode (w _af .BinaryWriter )(_dfed int ,_feae error ){const _afdf ="\u0053\u0079\u006dbo\u006c\u0044\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u002e\u0045\u006e\u0063\u006f\u0064\u0065";
if _edb ==nil {return 0,_gf .Error (_afdf ,"\u0073\u0079m\u0062\u006f\u006c\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u0020\u006e\u006f\u0074\u0020\u0064\u0065\u0066in\u0065\u0064");};if _dfed ,_feae =_edb .encodeFlags (w );_feae !=nil {return _dfed ,_gf .Wrap (_feae ,_afdf ,"");
};_adaa ,_feae :=_edb .encodeATFlags (w );if _feae !=nil {return _dfed ,_gf .Wrap (_feae ,_afdf ,"");};_dfed +=_adaa ;if _adaa ,_feae =_edb .encodeRefinementATFlags (w );_feae !=nil {return _dfed ,_gf .Wrap (_feae ,_afdf ,"");};_dfed +=_adaa ;if _adaa ,_feae =_edb .encodeNumSyms (w );
_feae !=nil {return _dfed ,_gf .Wrap (_feae ,_afdf ,"");};_dfed +=_adaa ;if _adaa ,_feae =_edb .encodeSymbols (w );_feae !=nil {return _dfed ,_gf .Wrap (_feae ,_afdf ,"");};_dfed +=_adaa ;return _dfed ,nil ;};func (_debe *template1 )setIndex (_dbc *_afd .DecoderStats ){_dbc .SetIndex (0x080)};
func (_beef *GenericRegion )writeGBAtPixels (_ffec _af .BinaryWriter )(_abb int ,_aaagc error ){const _gce ="\u0077r\u0069t\u0065\u0047\u0042\u0041\u0074\u0050\u0069\u0078\u0065\u006c\u0073";if _beef .UseMMR {return 0,nil ;};_feef :=1;if _beef .GBTemplate ==0{_feef =4;
}else if _beef .UseExtTemplates {_feef =12;};if len (_beef .GBAtX )!=_feef {return 0,_gf .Errorf (_gce ,"\u0067\u0062\u0020\u0061\u0074\u0020\u0070\u0061\u0069\u0072\u0020\u006e\u0075\u006d\u0062\u0065\u0072\u0020d\u006f\u0065\u0073\u006e\u0027\u0074\u0020m\u0061\u0074\u0063\u0068\u0020\u0074\u006f\u0020\u0047\u0042\u0041t\u0058\u0020\u0073\u006c\u0069\u0063\u0065\u0020\u006c\u0065\u006e");
};if len (_beef .GBAtY )!=_feef {return 0,_gf .Errorf (_gce ,"\u0067\u0062\u0020\u0061\u0074\u0020\u0070\u0061\u0069\u0072\u0020\u006e\u0075\u006d\u0062\u0065\u0072\u0020d\u006f\u0065\u0073\u006e\u0027\u0074\u0020m\u0061\u0074\u0063\u0068\u0020\u0074\u006f\u0020\u0047\u0042\u0041t\u0059\u0020\u0073\u006c\u0069\u0063\u0065\u0020\u006c\u0065\u006e");
};for _fdec :=0;_fdec < _feef ;_fdec ++{if _aaagc =_ffec .WriteByte (byte (_beef .GBAtX [_fdec ]));_aaagc !=nil {return _abb ,_gf .Wrap (_aaagc ,_gce ,"w\u0072\u0069\u0074\u0065\u0020\u0047\u0042\u0041\u0074\u0058");};_abb ++;if _aaagc =_ffec .WriteByte (byte (_beef .GBAtY [_fdec ]));
_aaagc !=nil {return _abb ,_gf .Wrap (_aaagc ,_gce ,"w\u0072\u0069\u0074\u0065\u0020\u0047\u0042\u0041\u0074\u0059");};_abb ++;};return _abb ,nil ;};func (_agbg *Header )readReferredToSegmentNumbers (_bbdcf *_af .Reader ,_gcf int )([]int ,error ){const _fdda ="\u0072\u0065\u0061\u0064R\u0065\u0066\u0065\u0072\u0072\u0065\u0064\u0054\u006f\u0053e\u0067m\u0065\u006e\u0074\u004e\u0075\u006d\u0062e\u0072\u0073";
_dcfg :=make ([]int ,_gcf );if _gcf > 0{_agbg .RTSegments =make ([]*Header ,_gcf );var (_ceae uint64 ;_agdb error ;);for _aeacc :=0;_aeacc < _gcf ;_aeacc ++{_ceae ,_agdb =_bbdcf .ReadBits (byte (_agbg .referenceSize ())<<3);if _agdb !=nil {return nil ,_gf .Wrapf (_agdb ,_fdda ,"\u0027\u0025\u0064\u0027 \u0072\u0065\u0066\u0065\u0072\u0072\u0065\u0064\u0020\u0073e\u0067m\u0065\u006e\u0074\u0020\u006e\u0075\u006db\u0065\u0072",_aeacc );
};_dcfg [_aeacc ]=int (_ceae &_g .MaxInt32 );};};return _dcfg ,nil ;};func (_fceaf *TextRegion )decodeSymbolInstances ()error {_aedf ,_abdef :=_fceaf .decodeStripT ();if _abdef !=nil {return _abdef ;};var (_agfbg int64 ;_dfaa uint32 ;);for _dfaa < _fceaf .NumberOfSymbolInstances {_fdef ,_fegb :=_fceaf .decodeDT ();
if _fegb !=nil {return _fegb ;};_aedf +=_fdef ;var _agaa int64 ;_cgaf :=true ;_fceaf ._abdc =0;for {if _cgaf {_agaa ,_fegb =_fceaf .decodeDfs ();if _fegb !=nil {return _fegb ;};_agfbg +=_agaa ;_fceaf ._abdc =_agfbg ;_cgaf =false ;}else {_bdac ,_bgdb :=_fceaf .decodeIds ();
if _ad .Is (_bgdb ,_gb .ErrOOB ){break ;};if _bgdb !=nil {return _bgdb ;};if _dfaa >=_fceaf .NumberOfSymbolInstances {break ;};_fceaf ._abdc +=_bdac +int64 (_fceaf .SbDsOffset );};_bbed ,_efde :=_fceaf .decodeCurrentT ();if _efde !=nil {return _efde ;};
_afac :=_aedf +_bbed ;_ffda ,_efde :=_fceaf .decodeID ();if _efde !=nil {return _efde ;};_fbcb ,_efde :=_fceaf .decodeRI ();if _efde !=nil {return _efde ;};_bafg ,_efde :=_fceaf .decodeIb (_fbcb ,_ffda );if _efde !=nil {return _efde ;};if _efde =_fceaf .blit (_bafg ,_afac );
_efde !=nil {return _efde ;};_dfaa ++;};};return nil ;};func (_adbg *Header )writeSegmentDataLength (_cafa _af .BinaryWriter )(_gggd int ,_aba error ){_egbg :=make ([]byte ,4);_da .BigEndian .PutUint32 (_egbg ,uint32 (_adbg .SegmentDataLength ));if _gggd ,_aba =_cafa .Write (_egbg );
_aba !=nil {return 0,_gf .Wrap (_aba ,"\u0048\u0065a\u0064\u0065\u0072\u002e\u0077\u0072\u0069\u0074\u0065\u0053\u0065\u0067\u006d\u0065\u006e\u0074\u0044\u0061\u0074\u0061\u004c\u0065ng\u0074\u0068","");};return _gggd ,nil ;};func (_dbcg *TableSegment )HtRS ()int32 {return _dbcg ._ffdd };
func (_aga *GenericRegion )Encode (w _af .BinaryWriter )(_geaf int ,_ebf error ){const _eccb ="G\u0065n\u0065\u0072\u0069\u0063\u0052\u0065\u0067\u0069o\u006e\u002e\u0045\u006eco\u0064\u0065";if _aga .Bitmap ==nil {return 0,_gf .Error (_eccb ,"\u0070\u0072\u006f\u0076id\u0065\u0064\u0020\u006e\u0069\u006c\u0020\u0062\u0069\u0074\u006d\u0061\u0070");
};_eeg ,_ebf :=_aga .RegionSegment .Encode (w );if _ebf !=nil {return 0,_gf .Wrap (_ebf ,_eccb ,"\u0052\u0065\u0067\u0069\u006f\u006e\u0053\u0065\u0067\u006d\u0065\u006e\u0074");};_geaf +=_eeg ;if _ebf =w .SkipBits (4);_ebf !=nil {return _geaf ,_gf .Wrap (_ebf ,_eccb ,"\u0073k\u0069p\u0020\u0072\u0065\u0073\u0065r\u0076\u0065d\u0020\u0062\u0069\u0074\u0073");
};var _ddg int ;if _aga .IsTPGDon {_ddg =1;};if _ebf =w .WriteBit (_ddg );_ebf !=nil {return _geaf ,_gf .Wrap (_ebf ,_eccb ,"\u0074\u0070\u0067\u0064\u006f\u006e");};_ddg =0;if _ebf =w .WriteBit (int (_aga .GBTemplate >>1)&0x01);_ebf !=nil {return _geaf ,_gf .Wrap (_ebf ,_eccb ,"f\u0069r\u0073\u0074\u0020\u0067\u0062\u0074\u0065\u006dp\u006c\u0061\u0074\u0065 b\u0069\u0074");
};if _ebf =w .WriteBit (int (_aga .GBTemplate )&0x01);_ebf !=nil {return _geaf ,_gf .Wrap (_ebf ,_eccb ,"s\u0065\u0063\u006f\u006ed \u0067b\u0074\u0065\u006d\u0070\u006ca\u0074\u0065\u0020\u0062\u0069\u0074");};if _aga .UseMMR {_ddg =1;};if _ebf =w .WriteBit (_ddg );
_ebf !=nil {return _geaf ,_gf .Wrap (_ebf ,_eccb ,"u\u0073\u0065\u0020\u004d\u004d\u0052\u0020\u0062\u0069\u0074");};_geaf ++;if _eeg ,_ebf =_aga .writeGBAtPixels (w );_ebf !=nil {return _geaf ,_gf .Wrap (_ebf ,_eccb ,"");};_geaf +=_eeg ;_ddef :=_gd .New ();
if _ebf =_ddef .EncodeBitmap (_aga .Bitmap ,_aga .IsTPGDon );_ebf !=nil {return _geaf ,_gf .Wrap (_ebf ,_eccb ,"");};_ddef .Final ();var _bda int64 ;if _bda ,_ebf =_ddef .WriteTo (w );_ebf !=nil {return _geaf ,_gf .Wrap (_ebf ,_eccb ,"");};_geaf +=int (_bda );
return _geaf ,nil ;};func (_cgfc *HalftoneRegion )Init (hd *Header ,r *_af .Reader )error {_cgfc ._efbc =r ;_cgfc ._bgac =hd ;_cgfc .RegionSegment =NewRegionSegment (r );return _cgfc .parseHeader ();};func (_ggfde *SymbolDictionary )retrieveImportSymbols ()error {for _ ,_gebb :=range _ggfde .Header .RTSegments {if _gebb .Type ==0{_eea ,_ggdb :=_gebb .GetSegmentData ();
if _ggdb !=nil {return _ggdb ;};_cbc ,_dcab :=_eea .(*SymbolDictionary );if !_dcab {return _b .Errorf ("\u0070\u0072\u006f\u0076\u0069\u0064\u0065\u0064\u0020\u0053\u0065\u0067\u006d\u0065\u006e\u0074\u0020\u0044\u0061\u0074a\u0020\u0069\u0073\u0020\u006e\u006f\u0074\u0020\u0061\u0020\u0053\u0079\u006d\u0062\u006f\u006c\u0044\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u0020\u0053\u0065\u0067m\u0065\u006e\u0074\u003a\u0020%\u0054",_eea );
};_cced ,_ggdb :=_cbc .GetDictionary ();if _ggdb !=nil {return _b .Errorf ("\u0072\u0065\u006c\u0061\u0074\u0065\u0064 \u0073\u0065\u0067m\u0065\u006e\u0074 \u0077\u0069t\u0068\u0020\u0069\u006e\u0064\u0065x\u003a %\u0064\u0020\u0067\u0065\u0074\u0044\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u0020\u0066\u0061\u0069\u006c\u0065\u0064\u002e\u0020\u0025\u0073",_gebb .SegmentNumber ,_ggdb .Error ());
};_ggfde ._agc =append (_ggfde ._agc ,_cced ...);_ggfde ._gagg +=_cbc .NumberOfExportedSymbols ;};};return nil ;};func (_afffe *PageInformationSegment )checkInput ()error {if _afffe .PageBMHeight ==_g .MaxInt32 {if !_afffe .IsStripe {_ac .Log .Debug ("P\u0061\u0067\u0065\u0049\u006e\u0066\u006f\u0072\u006da\u0074\u0069\u006f\u006e\u0053\u0065\u0067me\u006e\u0074\u002e\u0049s\u0053\u0074\u0072\u0069\u0070\u0065\u0020\u0073\u0068ou\u006c\u0064 \u0062\u0065\u0020\u0074\u0072\u0075\u0065\u002e");
};};return nil ;};func (_dcba *GenericRegion )setParametersMMR (_dbaa bool ,_bcdd ,_ecdb int64 ,_agg ,_bdcg uint32 ,_fgdb byte ,_becf ,_feff bool ,_abfa ,_fab []int8 ){_dcba .DataOffset =_bcdd ;_dcba .DataLength =_ecdb ;_dcba .RegionSegment =&RegionSegment {};
_dcba .RegionSegment .BitmapHeight =_agg ;_dcba .RegionSegment .BitmapWidth =_bdcg ;_dcba .GBTemplate =_fgdb ;_dcba .IsMMREncoded =_dbaa ;_dcba .IsTPGDon =_becf ;_dcba .GBAtX =_abfa ;_dcba .GBAtY =_fab ;};var _ templater =&template0 {};func (_bdfa *TextRegion )decodeRdy ()(int64 ,error ){const _fgge ="\u0064e\u0063\u006f\u0064\u0065\u0052\u0064y";
if _bdfa .IsHuffmanEncoded {if _bdfa .SbHuffRDY ==3{if _bdfa ._cgea ==nil {var (_fabg int ;_gdde error ;);if _bdfa .SbHuffFS ==3{_fabg ++;};if _bdfa .SbHuffDS ==3{_fabg ++;};if _bdfa .SbHuffDT ==3{_fabg ++;};if _bdfa .SbHuffRDWidth ==3{_fabg ++;};if _bdfa .SbHuffRDHeight ==3{_fabg ++;
};if _bdfa .SbHuffRDX ==3{_fabg ++;};_bdfa ._cgea ,_gdde =_bdfa .getUserTable (_fabg );if _gdde !=nil {return 0,_gf .Wrap (_gdde ,_fgge ,"");};};return _bdfa ._cgea .Decode (_bdfa ._bbbd );};_eagd ,_aggf :=_eb .GetStandardTable (14+int (_bdfa .SbHuffRDY ));
if _aggf !=nil {return 0,_aggf ;};return _eagd .Decode (_bdfa ._bbbd );};_dgfd ,_gefdc :=_bdfa ._dbf .DecodeInt (_bdfa ._gfae );if _gefdc !=nil {return 0,_gf .Wrap (_gefdc ,_fgge ,"");};return int64 (_dgfd ),nil ;};func (_ggga *SymbolDictionary )addSymbol (_acdd Regioner )error {_fbbg ,_gbdf :=_acdd .GetRegionBitmap ();
if _gbdf !=nil {return _gbdf ;};_ggga ._fgga [_ggga ._aagc ]=_fbbg ;_ggga ._gggg =append (_ggga ._gggg ,_fbbg );_ac .Log .Trace ("\u005b\u0053YM\u0042\u004f\u004c \u0044\u0049\u0043\u0054ION\u0041RY\u005d\u0020\u0041\u0064\u0064\u0065\u0064 s\u0079\u006d\u0062\u006f\u006c\u003a\u0020%\u0073",_fbbg );
return nil ;};func (_gdce *SymbolDictionary )readAtPixels (_bdff int )error {_gdce .SdATX =make ([]int8 ,_bdff );_gdce .SdATY =make ([]int8 ,_bdff );var (_bdfd byte ;_feffc error ;);for _ebdb :=0;_ebdb < _bdff ;_ebdb ++{_bdfd ,_feffc =_gdce ._ecdbb .ReadByte ();
if _feffc !=nil {return _feffc ;};_gdce .SdATX [_ebdb ]=int8 (_bdfd );_bdfd ,_feffc =_gdce ._ecdbb .ReadByte ();if _feffc !=nil {return _feffc ;};_gdce .SdATY [_ebdb ]=int8 (_bdfd );};return nil ;};func (_geac *Header )readDataStartOffset (_cacg *_af .Reader ,_eebec OrganizationType ){if _eebec ==OSequential {_geac .SegmentDataStartOffset =uint64 (_cacg .AbsolutePosition ());
};};func (_egefc *PageInformationSegment )CombinationOperator ()_f .CombinationOperator {return _egefc ._egeb ;};var (_ Regioner =&TextRegion {};_ Segmenter =&TextRegion {};);func (_cfed *Header )writeSegmentNumber (_fdb _af .BinaryWriter )(_gfcf int ,_bcfb error ){_cbgf :=make ([]byte ,4);
_da .BigEndian .PutUint32 (_cbgf ,_cfed .SegmentNumber );if _gfcf ,_bcfb =_fdb .Write (_cbgf );_bcfb !=nil {return 0,_gf .Wrap (_bcfb ,"\u0048e\u0061\u0064\u0065\u0072.\u0077\u0072\u0069\u0074\u0065S\u0065g\u006de\u006e\u0074\u004e\u0075\u006d\u0062\u0065r","");
};return _gfcf ,nil ;};func (_egefb *HalftoneRegion )computeY (_fceb ,_dcda int )int {return _egefb .shiftAndFill (int (_egefb .HGridY )+_fceb *int (_egefb .HRegionX )-_dcda *int (_egefb .HRegionY ));};type Pager interface{GetSegment (int )(*Header ,error );
GetBitmap ()(*_f .Bitmap ,error );};func (_fde *GenericRegion )parseHeader ()(_ebc error ){_ac .Log .Trace ("\u005b\u0047\u0045\u004e\u0045\u0052I\u0043\u002d\u0052\u0045\u0047\u0049\u004f\u004e\u005d\u0020\u0050\u0061\u0072s\u0069\u006e\u0067\u0048\u0065\u0061\u0064e\u0072\u002e\u002e\u002e");
defer func (){if _ebc !=nil {_ac .Log .Trace ("\u005b\u0047\u0045\u004e\u0045\u0052\u0049\u0043\u002d\u0052\u0045\u0047\u0049\u004f\u004e]\u0020\u0050\u0061\u0072\u0073\u0069\u006e\u0067\u0048\u0065\u0061\u0064\u0065r\u0020\u0046\u0069\u006e\u0069\u0073\u0068\u0065\u0064\u0020\u0077\u0069th\u0020\u0065\u0072\u0072\u006f\u0072\u002e\u0020\u0025\u0076",_ebc );
}else {_ac .Log .Trace ("\u005b\u0047\u0045\u004e\u0045\u0052\u0049C\u002d\u0052\u0045G\u0049\u004f\u004e]\u0020\u0050a\u0072\u0073\u0069\u006e\u0067\u0048e\u0061de\u0072\u0020\u0046\u0069\u006e\u0069\u0073\u0068\u0065\u0064\u0020\u0053\u0075\u0063\u0063\u0065\u0073\u0073\u0066\u0075\u006c\u006c\u0079\u002e\u002e\u002e");
};}();var (_fcde int ;_cff uint64 ;);if _ebc =_fde .RegionSegment .parseHeader ();_ebc !=nil {return _ebc ;};if _ ,_ebc =_fde ._cfag .ReadBits (3);_ebc !=nil {return _ebc ;};_fcde ,_ebc =_fde ._cfag .ReadBit ();if _ebc !=nil {return _ebc ;};if _fcde ==1{_fde .UseExtTemplates =true ;
};_fcde ,_ebc =_fde ._cfag .ReadBit ();if _ebc !=nil {return _ebc ;};if _fcde ==1{_fde .IsTPGDon =true ;};_cff ,_ebc =_fde ._cfag .ReadBits (2);if _ebc !=nil {return _ebc ;};_fde .GBTemplate =byte (_cff &0xf);_fcde ,_ebc =_fde ._cfag .ReadBit ();if _ebc !=nil {return _ebc ;
};if _fcde ==1{_fde .IsMMREncoded =true ;};if !_fde .IsMMREncoded {_bcgd :=1;if _fde .GBTemplate ==0{_bcgd =4;if _fde .UseExtTemplates {_bcgd =12;};};if _ebc =_fde .readGBAtPixels (_bcgd );_ebc !=nil {return _ebc ;};};if _ebc =_fde .computeSegmentDataStructure ();
_ebc !=nil {return _ebc ;};_ac .Log .Trace ("\u0025\u0073",_fde );return nil ;};func (_bdca *Header )writeFlags (_gcbbd _af .BinaryWriter )(_agbc error ){const _fdff ="\u0048\u0065\u0061\u0064\u0065\u0072\u002e\u0077\u0072\u0069\u0074\u0065F\u006c\u0061\u0067\u0073";
_dcgd :=byte (_bdca .Type );if _agbc =_gcbbd .WriteByte (_dcgd );_agbc !=nil {return _gf .Wrap (_agbc ,_fdff ,"\u0077\u0072\u0069ti\u006e\u0067\u0020\u0073\u0065\u0067\u006d\u0065\u006et\u0020t\u0079p\u0065 \u006e\u0075\u006d\u0062\u0065\u0072\u0020\u0066\u0061\u0069\u006c\u0065\u0064");
};if !_bdca .RetainFlag &&!_bdca .PageAssociationFieldSize {return nil ;};if _agbc =_gcbbd .SkipBits (-8);_agbc !=nil {return _gf .Wrap (_agbc ,_fdff ,"\u0073\u006bi\u0070\u0070\u0069\u006e\u0067\u0020\u0062\u0061\u0063\u006b\u0020\u0074\u0068\u0065\u0020\u0062\u0069\u0074\u0073\u0020\u0066\u0061il\u0065\u0064");
};var _caff int ;if _bdca .RetainFlag {_caff =1;};if _agbc =_gcbbd .WriteBit (_caff );_agbc !=nil {return _gf .Wrap (_agbc ,_fdff ,"\u0072\u0065\u0074\u0061in\u0020\u0072\u0065\u0074\u0061\u0069\u006e\u0020\u0066\u006c\u0061\u0067\u0073");};_caff =0;if _bdca .PageAssociationFieldSize {_caff =1;
};if _agbc =_gcbbd .WriteBit (_caff );_agbc !=nil {return _gf .Wrap (_agbc ,_fdff ,"p\u0061\u0067\u0065\u0020as\u0073o\u0063\u0069\u0061\u0074\u0069o\u006e\u0020\u0066\u006c\u0061\u0067");};_gcbbd .FinishByte ();return nil ;};func (_fgcd *GenericRegion )String ()string {_cedc :=&_a .Builder {};
_cedc .WriteString ("\u000a[\u0047E\u004e\u0045\u0052\u0049\u0043 \u0052\u0045G\u0049\u004f\u004e\u005d\u000a");_cedc .WriteString (_fgcd .RegionSegment .String ()+"\u000a");_cedc .WriteString (_b .Sprintf ("\u0009\u002d\u0020Us\u0065\u0045\u0078\u0074\u0054\u0065\u006d\u0070\u006c\u0061\u0074\u0065\u0073\u003a\u0020\u0025\u0076\u000a",_fgcd .UseExtTemplates ));
_cedc .WriteString (_b .Sprintf ("\u0009\u002d \u0049\u0073\u0054P\u0047\u0044\u006f\u006e\u003a\u0020\u0025\u0076\u000a",_fgcd .IsTPGDon ));_cedc .WriteString (_b .Sprintf ("\u0009-\u0020G\u0042\u0054\u0065\u006d\u0070l\u0061\u0074e\u003a\u0020\u0025\u0064\u000a",_fgcd .GBTemplate ));
_cedc .WriteString (_b .Sprintf ("\t\u002d \u0049\u0073\u004d\u004d\u0052\u0045\u006e\u0063o\u0064\u0065\u0064\u003a %\u0076\u000a",_fgcd .IsMMREncoded ));_cedc .WriteString (_b .Sprintf ("\u0009\u002d\u0020\u0047\u0042\u0041\u0074\u0058\u003a\u0020\u0025\u0076\u000a",_fgcd .GBAtX ));
_cedc .WriteString (_b .Sprintf ("\u0009\u002d\u0020\u0047\u0042\u0041\u0074\u0059\u003a\u0020\u0025\u0076\u000a",_fgcd .GBAtY ));_cedc .WriteString (_b .Sprintf ("\t\u002d \u0047\u0042\u0041\u0074\u004f\u0076\u0065\u0072r\u0069\u0064\u0065\u003a %\u0076\u000a",_fgcd .GBAtOverride ));
return _cedc .String ();};func (_ccf *Header )parse (_gcdd Documenter ,_ebda *_af .Reader ,_dega int64 ,_ceec OrganizationType )(_faa error ){const _gcbb ="\u0070\u0061\u0072s\u0065";_ac .Log .Trace ("\u005b\u0053\u0045\u0047\u004d\u0045\u004e\u0054\u002d\u0048E\u0041\u0044\u0045\u0052\u005d\u005b\u0050A\u0052\u0053\u0045\u005d\u0020\u0042\u0065\u0067\u0069\u006e\u0073");
defer func (){if _faa !=nil {_ac .Log .Trace ("\u005b\u0053\u0045GM\u0045\u004e\u0054\u002d\u0048\u0045\u0041\u0044\u0045R\u005d[\u0050A\u0052S\u0045\u005d\u0020\u0046\u0061\u0069\u006c\u0065\u0064\u002e\u0020\u0025\u0076",_faa );}else {_ac .Log .Trace ("\u005b\u0053\u0045\u0047\u004d\u0045\u004e\u0054\u002d\u0048\u0045\u0041\u0044\u0045\u0052]\u005bP\u0041\u0052\u0053\u0045\u005d\u0020\u0046\u0069\u006e\u0069\u0073\u0068\u0065\u0064");
};}();_ ,_faa =_ebda .Seek (_dega ,_ge .SeekStart );if _faa !=nil {return _gf .Wrap (_faa ,_gcbb ,"\u0073\u0065\u0065\u006b\u0020\u0073\u0074\u0061\u0072\u0074");};if _faa =_ccf .readSegmentNumber (_ebda );_faa !=nil {return _gf .Wrap (_faa ,_gcbb ,"");
};if _faa =_ccf .readHeaderFlags ();_faa !=nil {return _gf .Wrap (_faa ,_gcbb ,"");};var _acaf uint64 ;_acaf ,_faa =_ccf .readNumberOfReferredToSegments (_ebda );if _faa !=nil {return _gf .Wrap (_faa ,_gcbb ,"");};_ccf .RTSNumbers ,_faa =_ccf .readReferredToSegmentNumbers (_ebda ,int (_acaf ));
if _faa !=nil {return _gf .Wrap (_faa ,_gcbb ,"");};_faa =_ccf .readSegmentPageAssociation (_gcdd ,_ebda ,_acaf ,_ccf .RTSNumbers ...);if _faa !=nil {return _gf .Wrap (_faa ,_gcbb ,"");};if _ccf .Type !=TEndOfFile {if _faa =_ccf .readSegmentDataLength (_ebda );
_faa !=nil {return _gf .Wrap (_faa ,_gcbb ,"");};};_ccf .readDataStartOffset (_ebda ,_ceec );_ccf .readHeaderLength (_ebda ,_dega );_ac .Log .Trace ("\u0025\u0073",_ccf );return nil ;};func (_dae *GenericRegion )overrideAtTemplate1 (_becg ,_fec ,_ddd ,_aaag ,_ageg int )int {_becg &=0x1FF7;
if _dae .GBAtY [0]==0&&_dae .GBAtX [0]>=-int8 (_ageg ){_becg |=(_aaag >>uint (7-(int8 (_ageg )+_dae .GBAtX [0]))&0x1)<<3;}else {_becg |=int (_dae .getPixel (_fec +int (_dae .GBAtX [0]),_ddd +int (_dae .GBAtY [0])))<<3;};return _becg ;};func (_dfdd *HalftoneRegion )GetPatterns ()([]*_f .Bitmap ,error ){var (_ccde []*_f .Bitmap ;
_cfaa error ;);for _ ,_bcfd :=range _dfdd ._bgac .RTSegments {var _bfcbd Segmenter ;_bfcbd ,_cfaa =_bcfd .GetSegmentData ();if _cfaa !=nil {_ac .Log .Debug ("\u0047e\u0074\u0053\u0065\u0067m\u0065\u006e\u0074\u0044\u0061t\u0061 \u0066a\u0069\u006c\u0065\u0064\u003a\u0020\u0025v",_cfaa );
return nil ,_cfaa ;};_egec ,_ccdc :=_bfcbd .(*PatternDictionary );if !_ccdc {_cfaa =_b .Errorf ("\u0072e\u006c\u0061t\u0065\u0064\u0020\u0073e\u0067\u006d\u0065n\u0074\u0020\u006e\u006f\u0074\u0020\u0061\u0020\u0070at\u0074\u0065\u0072n\u0020\u0064i\u0063\u0074\u0069\u006f\u006e\u0061r\u0079\u003a \u0025\u0054",_bfcbd );
return nil ,_cfaa ;};var _abdg []*_f .Bitmap ;_abdg ,_cfaa =_egec .GetDictionary ();if _cfaa !=nil {_ac .Log .Debug ("\u0070\u0061\u0074\u0074\u0065\u0072\u006e\u0020\u0047\u0065\u0074\u0044\u0069\u0063\u0074i\u006fn\u0061\u0072\u0079\u0020\u0066\u0061\u0069\u006c\u0065\u0064\u003a\u0020\u0025\u0076",_cfaa );
return nil ,_cfaa ;};_ccde =append (_ccde ,_abdg ...);};return _ccde ,nil ;};func (_edcb *HalftoneRegion )parseHeader ()error {if _aab :=_edcb .RegionSegment .parseHeader ();_aab !=nil {return _aab ;};_ggfb ,_bbcg :=_edcb ._efbc .ReadBit ();if _bbcg !=nil {return _bbcg ;
};_edcb .HDefaultPixel =int8 (_ggfb );_fbc ,_bbcg :=_edcb ._efbc .ReadBits (3);if _bbcg !=nil {return _bbcg ;};_edcb .CombinationOperator =_f .CombinationOperator (_fbc &0xf);_ggfb ,_bbcg =_edcb ._efbc .ReadBit ();if _bbcg !=nil {return _bbcg ;};if _ggfb ==1{_edcb .HSkipEnabled =true ;
};_fbc ,_bbcg =_edcb ._efbc .ReadBits (2);if _bbcg !=nil {return _bbcg ;};_edcb .HTemplate =byte (_fbc &0xf);_ggfb ,_bbcg =_edcb ._efbc .ReadBit ();if _bbcg !=nil {return _bbcg ;};if _ggfb ==1{_edcb .IsMMREncoded =true ;};_fbc ,_bbcg =_edcb ._efbc .ReadBits (32);
if _bbcg !=nil {return _bbcg ;};_edcb .HGridWidth =uint32 (_fbc &_g .MaxUint32 );_fbc ,_bbcg =_edcb ._efbc .ReadBits (32);if _bbcg !=nil {return _bbcg ;};_edcb .HGridHeight =uint32 (_fbc &_g .MaxUint32 );_fbc ,_bbcg =_edcb ._efbc .ReadBits (32);if _bbcg !=nil {return _bbcg ;
};_edcb .HGridX =int32 (_fbc &_g .MaxInt32 );_fbc ,_bbcg =_edcb ._efbc .ReadBits (32);if _bbcg !=nil {return _bbcg ;};_edcb .HGridY =int32 (_fbc &_g .MaxInt32 );_fbc ,_bbcg =_edcb ._efbc .ReadBits (16);if _bbcg !=nil {return _bbcg ;};_edcb .HRegionX =uint16 (_fbc &_g .MaxUint16 );
_fbc ,_bbcg =_edcb ._efbc .ReadBits (16);if _bbcg !=nil {return _bbcg ;};_edcb .HRegionY =uint16 (_fbc &_g .MaxUint16 );if _bbcg =_edcb .computeSegmentDataStructure ();_bbcg !=nil {return _bbcg ;};return _edcb .checkInput ();};func (_bdad *SymbolDictionary )setRetainedCodingContexts (_geec *SymbolDictionary ){_bdad ._edffg =_geec ._edffg ;
_bdad .IsHuffmanEncoded =_geec .IsHuffmanEncoded ;_bdad .UseRefinementAggregation =_geec .UseRefinementAggregation ;_bdad .SdTemplate =_geec .SdTemplate ;_bdad .SdrTemplate =_geec .SdrTemplate ;_bdad .SdATX =_geec .SdATX ;_bdad .SdATY =_geec .SdATY ;_bdad .SdrATX =_geec .SdrATX ;
_bdad .SdrATY =_geec .SdrATY ;_bdad ._gabd =_geec ._gabd ;};func (_abcg *TableSegment )parseHeader ()error {var (_fdca int ;_bcde uint64 ;_cbcd error ;);_fdca ,_cbcd =_abcg ._fcae .ReadBit ();if _cbcd !=nil {return _cbcd ;};if _fdca ==1{return _b .Errorf ("\u0069\u006e\u0076\u0061\u006c\u0069\u0064\u0020\u0074\u0061\u0062\u006c\u0065 \u0073\u0065\u0067\u006d\u0065\u006e\u0074\u0020\u0064e\u0066\u0069\u006e\u0069\u0074\u0069\u006f\u006e\u002e\u0020\u0042\u002e\u0032\u002e1\u0020\u0043\u006f\u0064\u0065\u0020\u0054\u0061\u0062\u006c\u0065\u0020\u0066\u006c\u0061\u0067\u0073\u003a\u0020\u0042\u0069\u0074\u0020\u0037\u0020\u006d\u0075\u0073\u0074\u0020b\u0065\u0020\u007a\u0065\u0072\u006f\u002e\u0020\u0057a\u0073\u003a \u0025\u0064",_fdca );
};if _bcde ,_cbcd =_abcg ._fcae .ReadBits (3);_cbcd !=nil {return _cbcd ;};_abcg ._ffdd =(int32 (_bcde )+1)&0xf;if _bcde ,_cbcd =_abcg ._fcae .ReadBits (3);_cbcd !=nil {return _cbcd ;};_abcg ._dabe =(int32 (_bcde )+1)&0xf;if _bcde ,_cbcd =_abcg ._fcae .ReadBits (32);
_cbcd !=nil {return _cbcd ;};_abcg ._ecge =int32 (_bcde &_g .MaxInt32 );if _bcde ,_cbcd =_abcg ._fcae .ReadBits (32);_cbcd !=nil {return _cbcd ;};_abcg ._eag =int32 (_bcde &_g .MaxInt32 );return nil ;};func (_cga *HalftoneRegion )combineGrayscalePlanes (_bgaaf []*_f .Bitmap ,_dfe int )error {_debb :=0;
for _cegc :=0;_cegc < _bgaaf [_dfe ].Height ;_cegc ++{for _aaec :=0;_aaec < _bgaaf [_dfe ].Width ;_aaec +=8{_gaa ,_dcbd :=_bgaaf [_dfe +1].GetByte (_debb );if _dcbd !=nil {return _dcbd ;};_dfae ,_dcbd :=_bgaaf [_dfe ].GetByte (_debb );if _dcbd !=nil {return _dcbd ;
};_dcbd =_bgaaf [_dfe ].SetByte (_debb ,_f .CombineBytes (_dfae ,_gaa ,_f .CmbOpXor ));if _dcbd !=nil {return _dcbd ;};_debb ++;};};return nil ;};func (_bgfg *TextRegion )String ()string {_dfdg :=&_a .Builder {};_dfdg .WriteString ("\u000a[\u0054E\u0058\u0054\u0020\u0052\u0045\u0047\u0049\u004f\u004e\u005d\u000a");
_dfdg .WriteString (_bgfg .RegionInfo .String ()+"\u000a");_dfdg .WriteString (_b .Sprintf ("\u0009\u002d\u0020\u0053br\u0054\u0065\u006d\u0070\u006c\u0061\u0074\u0065\u003a\u0020\u0025\u0076\u000a",_bgfg .SbrTemplate ));_dfdg .WriteString (_b .Sprintf ("\u0009-\u0020S\u0062\u0044\u0073\u004f\u0066f\u0073\u0065t\u003a\u0020\u0025\u0076\u000a",_bgfg .SbDsOffset ));
_dfdg .WriteString (_b .Sprintf ("\t\u002d \u0044\u0065\u0066\u0061\u0075\u006c\u0074\u0050i\u0078\u0065\u006c\u003a %\u0076\u000a",_bgfg .DefaultPixel ));_dfdg .WriteString (_b .Sprintf ("\t\u002d\u0020\u0043\u006f\u006d\u0062i\u006e\u0061\u0074\u0069\u006f\u006e\u004f\u0070\u0065r\u0061\u0074\u006fr\u003a \u0025\u0076\u000a",_bgfg .CombinationOperator ));
_dfdg .WriteString (_b .Sprintf ("\t\u002d \u0049\u0073\u0054\u0072\u0061\u006e\u0073\u0070o\u0073\u0065\u0064\u003a %\u0076\u000a",_bgfg .IsTransposed ));_dfdg .WriteString (_b .Sprintf ("\u0009\u002d\u0020Re\u0066\u0065\u0072\u0065\u006e\u0063\u0065\u0043\u006f\u0072\u006e\u0065\u0072\u003a\u0020\u0025\u0076\u000a",_bgfg .ReferenceCorner ));
_dfdg .WriteString (_b .Sprintf ("\t\u002d\u0020\u0055\u0073eR\u0065f\u0069\u006e\u0065\u006d\u0065n\u0074\u003a\u0020\u0025\u0076\u000a",_bgfg .UseRefinement ));_dfdg .WriteString (_b .Sprintf ("\u0009-\u0020\u0049\u0073\u0048\u0075\u0066\u0066\u006d\u0061\u006e\u0045n\u0063\u006f\u0064\u0065\u0064\u003a\u0020\u0025\u0076\u000a",_bgfg .IsHuffmanEncoded ));
if _bgfg .IsHuffmanEncoded {_dfdg .WriteString (_b .Sprintf ("\u0009\u002d\u0020\u0053bH\u0075\u0066\u0066\u0052\u0053\u0069\u007a\u0065\u003a\u0020\u0025\u0076\u000a",_bgfg .SbHuffRSize ));_dfdg .WriteString (_b .Sprintf ("\u0009\u002d\u0020\u0053\u0062\u0048\u0075\u0066\u0066\u0052\u0044\u0059:\u0020\u0025\u0076\u000a",_bgfg .SbHuffRDY ));
_dfdg .WriteString (_b .Sprintf ("\u0009\u002d\u0020\u0053\u0062\u0048\u0075\u0066\u0066\u0052\u0044\u0058:\u0020\u0025\u0076\u000a",_bgfg .SbHuffRDX ));_dfdg .WriteString (_b .Sprintf ("\u0009\u002d\u0020\u0053bH\u0075\u0066\u0066\u0052\u0044\u0048\u0065\u0069\u0067\u0068\u0074\u003a\u0020\u0025v\u000a",_bgfg .SbHuffRDHeight ));
_dfdg .WriteString (_b .Sprintf ("\t\u002d\u0020\u0053\u0062Hu\u0066f\u0052\u0044\u0057\u0069\u0064t\u0068\u003a\u0020\u0025\u0076\u000a",_bgfg .SbHuffRDWidth ));_dfdg .WriteString (_b .Sprintf ("\u0009\u002d \u0053\u0062\u0048u\u0066\u0066\u0044\u0054\u003a\u0020\u0025\u0076\u000a",_bgfg .SbHuffDT ));
_dfdg .WriteString (_b .Sprintf ("\u0009\u002d \u0053\u0062\u0048u\u0066\u0066\u0044\u0053\u003a\u0020\u0025\u0076\u000a",_bgfg .SbHuffDS ));_dfdg .WriteString (_b .Sprintf ("\u0009\u002d \u0053\u0062\u0048u\u0066\u0066\u0046\u0053\u003a\u0020\u0025\u0076\u000a",_bgfg .SbHuffFS ));
};_dfdg .WriteString (_b .Sprintf ("\u0009\u002d\u0020\u0053\u0062\u0072\u0041\u0054\u0058:\u0020\u0025\u0076\u000a",_bgfg .SbrATX ));_dfdg .WriteString (_b .Sprintf ("\u0009\u002d\u0020\u0053\u0062\u0072\u0041\u0054\u0059:\u0020\u0025\u0076\u000a",_bgfg .SbrATY ));
_dfdg .WriteString (_b .Sprintf ("\u0009\u002d\u0020N\u0075\u006d\u0062\u0065r\u004f\u0066\u0053\u0079\u006d\u0062\u006fl\u0049\u006e\u0073\u0074\u0061\u006e\u0063\u0065\u0073\u003a\u0020\u0025\u0076\u000a",_bgfg .NumberOfSymbolInstances ));_dfdg .WriteString (_b .Sprintf ("\u0009\u002d\u0020\u0053\u0062\u0072\u0041\u0054\u0058:\u0020\u0025\u0076\u000a",_bgfg .SbrATX ));
return _dfdg .String ();};func (_cefb *PatternDictionary )extractPatterns (_cfcg *_f .Bitmap )error {var _gcad int ;_acgb :=make ([]*_f .Bitmap ,_cefb .GrayMax +1);for _gcad <=int (_cefb .GrayMax ){_dgfa :=int (_cefb .HdpWidth )*_gcad ;_edabc :=_e .Rect (_dgfa ,0,_dgfa +int (_cefb .HdpWidth ),int (_cefb .HdpHeight ));
_dcdf ,_cbbc :=_f .Extract (_edabc ,_cfcg );if _cbbc !=nil {return _cbbc ;};_acgb [_gcad ]=_dcdf ;_gcad ++;};_cefb .Patterns =_acgb ;return nil ;};func (_dbd *GenericRegion )readGBAtPixels (_begd int )error {const _aeac ="\u0072\u0065\u0061\u0064\u0047\u0042\u0041\u0074\u0050i\u0078\u0065\u006c\u0073";
_dbd .GBAtX =make ([]int8 ,_begd );_dbd .GBAtY =make ([]int8 ,_begd );for _gbfc :=0;_gbfc < _begd ;_gbfc ++{_fecd ,_bab :=_dbd ._cfag .ReadByte ();if _bab !=nil {return _gf .Wrapf (_bab ,_aeac ,"\u0058\u0020\u0061t\u0020\u0069\u003a\u0020\u0027\u0025\u0064\u0027",_gbfc );
};_dbd .GBAtX [_gbfc ]=int8 (_fecd );_fecd ,_bab =_dbd ._cfag .ReadByte ();if _bab !=nil {return _gf .Wrapf (_bab ,_aeac ,"\u0059\u0020\u0061t\u0020\u0069\u003a\u0020\u0027\u0025\u0064\u0027",_gbfc );};_dbd .GBAtY [_gbfc ]=int8 (_fecd );};return nil ;};
func (_bcfda *Header )readSegmentPageAssociation (_gge Documenter ,_gdcb *_af .Reader ,_gadb uint64 ,_bdd ...int )(_agbd error ){const _ddeg ="\u0072\u0065\u0061\u0064\u0053\u0065\u0067\u006d\u0065\u006e\u0074P\u0061\u0067\u0065\u0041\u0073\u0073\u006f\u0063\u0069\u0061t\u0069\u006f\u006e";
if !_bcfda .PageAssociationFieldSize {_ecddf ,_ebg :=_gdcb .ReadBits (8);if _ebg !=nil {return _gf .Wrap (_ebg ,_ddeg ,"\u0073\u0068\u006fr\u0074\u0020\u0066\u006f\u0072\u006d\u0061\u0074");};_bcfda .PageAssociation =int (_ecddf &0xFF);}else {_gefa ,_agege :=_gdcb .ReadBits (32);
if _agege !=nil {return _gf .Wrap (_agege ,_ddeg ,"l\u006f\u006e\u0067\u0020\u0066\u006f\u0072\u006d\u0061\u0074");};_bcfda .PageAssociation =int (_gefa &_g .MaxInt32 );};if _gadb ==0{return nil ;};if _bcfda .PageAssociation !=0{_cfgga ,_bbeec :=_gge .GetPage (_bcfda .PageAssociation );
if _bbeec !=nil {return _gf .Wrap (_bbeec ,_ddeg ,"\u0061s\u0073\u006f\u0063\u0069a\u0074\u0065\u0064\u0020\u0070a\u0067e\u0020n\u006f\u0074\u0020\u0066\u006f\u0075\u006ed");};var _dfgb int ;for _bfaa :=uint64 (0);_bfaa < _gadb ;_bfaa ++{_dfgb =_bdd [_bfaa ];
_bcfda .RTSegments [_bfaa ],_bbeec =_cfgga .GetSegment (_dfgb );if _bbeec !=nil {var _ggeg error ;_bcfda .RTSegments [_bfaa ],_ggeg =_gge .GetGlobalSegment (_dfgb );if _ggeg !=nil {return _gf .Wrapf (_bbeec ,_ddeg ,"\u0072\u0065\u0066\u0065\u0072\u0065n\u0063\u0065\u0020s\u0065\u0067\u006de\u006e\u0074\u0020\u006e\u006f\u0074\u0020\u0066\u006f\u0075n\u0064\u0020\u0061\u0074\u0020pa\u0067\u0065\u003a\u0020\u0027\u0025\u0064\u0027\u0020\u006e\u006f\u0072\u0020\u0069\u006e\u0020\u0067\u006c\u006f\u0062\u0061\u006c\u0073",_bcfda .PageAssociation );
};};};return nil ;};for _cfcd :=uint64 (0);_cfcd < _gadb ;_cfcd ++{_bcfda .RTSegments [_cfcd ],_agbd =_gge .GetGlobalSegment (_bdd [_cfcd ]);if _agbd !=nil {return _gf .Wrapf (_agbd ,_ddeg ,"\u0067\u006c\u006f\u0062\u0061\u006c\u0020\u0073\u0065\u0067m\u0065\u006e\u0074\u003a\u0020\u0027\u0025d\u0027\u0020\u006e\u006f\u0074\u0020\u0066\u006f\u0075\u006e\u0064",_bdd [_cfcd ]);
};};return nil ;};func (_eaae *TextRegion )decodeDT ()(_gcec int64 ,_eaeca error ){if _eaae .IsHuffmanEncoded {if _eaae .SbHuffDT ==3{_gcec ,_eaeca =_eaae ._ccdfc .Decode (_eaae ._bbbd );if _eaeca !=nil {return 0,_eaeca ;};}else {var _dcfba _eb .Tabler ;
_dcfba ,_eaeca =_eb .GetStandardTable (11+int (_eaae .SbHuffDT ));if _eaeca !=nil {return 0,_eaeca ;};_gcec ,_eaeca =_dcfba .Decode (_eaae ._bbbd );if _eaeca !=nil {return 0,_eaeca ;};};}else {var _ccb int32 ;_ccb ,_eaeca =_eaae ._dbf .DecodeInt (_eaae ._bace );
if _eaeca !=nil {return 0,_eaeca ;};_gcec =int64 (_ccb );};_gcec *=int64 (_eaae .SbStrips );return _gcec ,nil ;};func (_aafg *TextRegion )decodeIds ()(int64 ,error ){const _abce ="\u0064e\u0063\u006f\u0064\u0065\u0049\u0064s";if _aafg .IsHuffmanEncoded {if _aafg .SbHuffDS ==3{if _aafg ._cgga ==nil {_ccfab :=0;
if _aafg .SbHuffFS ==3{_ccfab ++;};var _cage error ;_aafg ._cgga ,_cage =_aafg .getUserTable (_ccfab );if _cage !=nil {return 0,_gf .Wrap (_cage ,_abce ,"");};};return _aafg ._cgga .Decode (_aafg ._bbbd );};_edag ,_dga :=_eb .GetStandardTable (8+int (_aafg .SbHuffDS ));
if _dga !=nil {return 0,_gf .Wrap (_dga ,_abce ,"");};return _edag .Decode (_aafg ._bbbd );};_fddf ,_dcfe :=_aafg ._dbf .DecodeInt (_aafg ._bageg );if _dcfe !=nil {return 0,_gf .Wrap (_dcfe ,_abce ,"\u0063\u0078\u0049\u0041\u0044\u0053");};return int64 (_fddf ),nil ;
};func (_ff *GenericRefinementRegion )decodeSLTP ()(int ,error ){_ff .Template .setIndex (_ff ._ceb );return _ff ._ec .DecodeBit (_ff ._ceb );};func (_ecaee *TextRegion )readUseRefinement ()error {if !_ecaee .UseRefinement ||_ecaee .SbrTemplate !=0{return nil ;
};var (_ddab byte ;_dbed error ;);_ecaee .SbrATX =make ([]int8 ,2);_ecaee .SbrATY =make ([]int8 ,2);_ddab ,_dbed =_ecaee ._bbbd .ReadByte ();if _dbed !=nil {return _dbed ;};_ecaee .SbrATX [0]=int8 (_ddab );_ddab ,_dbed =_ecaee ._bbbd .ReadByte ();if _dbed !=nil {return _dbed ;
};_ecaee .SbrATY [0]=int8 (_ddab );_ddab ,_dbed =_ecaee ._bbbd .ReadByte ();if _dbed !=nil {return _dbed ;};_ecaee .SbrATX [1]=int8 (_ddab );_ddab ,_dbed =_ecaee ._bbbd .ReadByte ();if _dbed !=nil {return _dbed ;};_ecaee .SbrATY [1]=int8 (_ddab );return nil ;
};type OrganizationType uint8 ;type SegmentEncoder interface{Encode (_acbd _af .BinaryWriter )(_aaae int ,_gbga error );};func (_abbd *Header )writeReferredToSegments (_ffba _af .BinaryWriter )(_dce int ,_agf error ){const _gfdc ="\u0077\u0072\u0069te\u0052\u0065\u0066\u0065\u0072\u0072\u0065\u0064\u0054\u006f\u0053\u0065\u0067\u006d\u0065\u006e\u0074\u0073";
var (_fcce uint16 ;_ecgd uint32 ;);_gdac :=_abbd .referenceSize ();_bgad :=1;_abdb :=make ([]byte ,_gdac );for _ ,_baaeg :=range _abbd .RTSNumbers {switch _gdac {case 4:_ecgd =uint32 (_baaeg );_da .BigEndian .PutUint32 (_abdb ,_ecgd );_bgad ,_agf =_ffba .Write (_abdb );
if _agf !=nil {return 0,_gf .Wrap (_agf ,_gfdc ,"u\u0069\u006e\u0074\u0033\u0032\u0020\u0073\u0069\u007a\u0065");};case 2:_fcce =uint16 (_baaeg );_da .BigEndian .PutUint16 (_abdb ,_fcce );_bgad ,_agf =_ffba .Write (_abdb );if _agf !=nil {return 0,_gf .Wrap (_agf ,_gfdc ,"\u0075\u0069\u006e\u0074\u0031\u0036");
};default:if _agf =_ffba .WriteByte (byte (_baaeg ));_agf !=nil {return 0,_gf .Wrap (_agf ,_gfdc ,"\u0075\u0069\u006et\u0038");};};_dce +=_bgad ;};return _dce ,nil ;};func (_bgdf *TextRegion )encodeFlags (_abffc _af .BinaryWriter )(_fcfe int ,_gcea error ){const _deaf ="e\u006e\u0063\u006f\u0064\u0065\u0046\u006c\u0061\u0067\u0073";
if _gcea =_abffc .WriteBit (int (_bgdf .SbrTemplate ));_gcea !=nil {return _fcfe ,_gf .Wrap (_gcea ,_deaf ,"s\u0062\u0072\u0054\u0065\u006d\u0070\u006c\u0061\u0074\u0065");};if _ ,_gcea =_abffc .WriteBits (uint64 (_bgdf .SbDsOffset ),5);_gcea !=nil {return _fcfe ,_gf .Wrap (_gcea ,_deaf ,"\u0073\u0062\u0044\u0073\u004f\u0066\u0066\u0073\u0065\u0074");
};if _gcea =_abffc .WriteBit (int (_bgdf .DefaultPixel ));_gcea !=nil {return _fcfe ,_gf .Wrap (_gcea ,_deaf ,"\u0044\u0065\u0066a\u0075\u006c\u0074\u0050\u0069\u0078\u0065\u006c");};if _ ,_gcea =_abffc .WriteBits (uint64 (_bgdf .CombinationOperator ),2);
_gcea !=nil {return _fcfe ,_gf .Wrap (_gcea ,_deaf ,"\u0043\u006f\u006d\u0062in\u0061\u0074\u0069\u006f\u006e\u004f\u0070\u0065\u0072\u0061\u0074\u006f\u0072");};if _gcea =_abffc .WriteBit (int (_bgdf .IsTransposed ));_gcea !=nil {return _fcfe ,_gf .Wrap (_gcea ,_deaf ,"\u0069\u0073\u0020\u0074\u0072\u0061\u006e\u0073\u0070\u006f\u0073\u0065\u0064");
};if _ ,_gcea =_abffc .WriteBits (uint64 (_bgdf .ReferenceCorner ),2);_gcea !=nil {return _fcfe ,_gf .Wrap (_gcea ,_deaf ,"\u0072\u0065f\u0065\u0072\u0065n\u0063\u0065\u0020\u0063\u006f\u0072\u006e\u0065\u0072");};if _ ,_gcea =_abffc .WriteBits (uint64 (_bgdf .LogSBStrips ),2);
_gcea !=nil {return _fcfe ,_gf .Wrap (_gcea ,_deaf ,"L\u006f\u0067\u0053\u0042\u0053\u0074\u0072\u0069\u0070\u0073");};var _egacc int ;if _bgdf .UseRefinement {_egacc =1;};if _gcea =_abffc .WriteBit (_egacc );_gcea !=nil {return _fcfe ,_gf .Wrap (_gcea ,_deaf ,"\u0075\u0073\u0065\u0020\u0072\u0065\u0066\u0069\u006ee\u006d\u0065\u006e\u0074");
};_egacc =0;if _bgdf .IsHuffmanEncoded {_egacc =1;};if _gcea =_abffc .WriteBit (_egacc );_gcea !=nil {return _fcfe ,_gf .Wrap (_gcea ,_deaf ,"u\u0073\u0065\u0020\u0068\u0075\u0066\u0066\u006d\u0061\u006e");};_fcfe =2;return _fcfe ,nil ;};func (_dfee *SymbolDictionary )encodeFlags (_cece _af .BinaryWriter )(_fadf int ,_edbb error ){const _cdde ="e\u006e\u0063\u006f\u0064\u0065\u0046\u006c\u0061\u0067\u0073";
if _edbb =_cece .SkipBits (3);_edbb !=nil {return 0,_gf .Wrap (_edbb ,_cdde ,"\u0065\u006d\u0070\u0074\u0079\u0020\u0062\u0069\u0074\u0073");};var _cabb int ;if _dfee .SdrTemplate > 0{_cabb =1;};if _edbb =_cece .WriteBit (_cabb );_edbb !=nil {return _fadf ,_gf .Wrap (_edbb ,_cdde ,"s\u0064\u0072\u0054\u0065\u006d\u0070\u006c\u0061\u0074\u0065");
};_cabb =0;if _dfee .SdTemplate > 1{_cabb =1;};if _edbb =_cece .WriteBit (_cabb );_edbb !=nil {return _fadf ,_gf .Wrap (_edbb ,_cdde ,"\u0073\u0064\u0054\u0065\u006d\u0070\u006c\u0061\u0074\u0065");};_cabb =0;if _dfee .SdTemplate ==1||_dfee .SdTemplate ==3{_cabb =1;
};if _edbb =_cece .WriteBit (_cabb );_edbb !=nil {return _fadf ,_gf .Wrap (_edbb ,_cdde ,"\u0073\u0064\u0054\u0065\u006d\u0070\u006c\u0061\u0074\u0065");};_cabb =0;if _dfee ._aeged {_cabb =1;};if _edbb =_cece .WriteBit (_cabb );_edbb !=nil {return _fadf ,_gf .Wrap (_edbb ,_cdde ,"\u0063\u006f\u0064in\u0067\u0020\u0063\u006f\u006e\u0074\u0065\u0078\u0074\u0020\u0072\u0065\u0074\u0061\u0069\u006e\u0065\u0064");
};_cabb =0;if _dfee ._bfdb {_cabb =1;};if _edbb =_cece .WriteBit (_cabb );_edbb !=nil {return _fadf ,_gf .Wrap (_edbb ,_cdde ,"\u0063\u006f\u0064\u0069ng\u0020\u0063\u006f\u006e\u0074\u0065\u0078\u0074\u0020\u0075\u0073\u0065\u0064");};_cabb =0;if _dfee .SdHuffAggInstanceSelection {_cabb =1;
};if _edbb =_cece .WriteBit (_cabb );_edbb !=nil {return _fadf ,_gf .Wrap (_edbb ,_cdde ,"\u0073\u0064\u0048\u0075\u0066\u0066\u0041\u0067\u0067\u0049\u006e\u0073\u0074");};_cabb =int (_dfee .SdHuffBMSizeSelection );if _edbb =_cece .WriteBit (_cabb );_edbb !=nil {return _fadf ,_gf .Wrap (_edbb ,_cdde ,"\u0073\u0064\u0048u\u0066\u0066\u0042\u006d\u0053\u0069\u007a\u0065");
};_cabb =0;if _dfee .SdHuffDecodeWidthSelection > 1{_cabb =1;};if _edbb =_cece .WriteBit (_cabb );_edbb !=nil {return _fadf ,_gf .Wrap (_edbb ,_cdde ,"s\u0064\u0048\u0075\u0066\u0066\u0057\u0069\u0064\u0074\u0068");};_cabb =0;switch _dfee .SdHuffDecodeWidthSelection {case 1,3:_cabb =1;
};if _edbb =_cece .WriteBit (_cabb );_edbb !=nil {return _fadf ,_gf .Wrap (_edbb ,_cdde ,"s\u0064\u0048\u0075\u0066\u0066\u0057\u0069\u0064\u0074\u0068");};_cabb =0;if _dfee .SdHuffDecodeHeightSelection > 1{_cabb =1;};if _edbb =_cece .WriteBit (_cabb );
_edbb !=nil {return _fadf ,_gf .Wrap (_edbb ,_cdde ,"\u0073\u0064\u0048u\u0066\u0066\u0048\u0065\u0069\u0067\u0068\u0074");};_cabb =0;switch _dfee .SdHuffDecodeHeightSelection {case 1,3:_cabb =1;};if _edbb =_cece .WriteBit (_cabb );_edbb !=nil {return _fadf ,_gf .Wrap (_edbb ,_cdde ,"\u0073\u0064\u0048u\u0066\u0066\u0048\u0065\u0069\u0067\u0068\u0074");
};_cabb =0;if _dfee .UseRefinementAggregation {_cabb =1;};if _edbb =_cece .WriteBit (_cabb );_edbb !=nil {return _fadf ,_gf .Wrap (_edbb ,_cdde ,"\u0073\u0064\u0052\u0065\u0066\u0041\u0067\u0067");};_cabb =0;if _dfee .IsHuffmanEncoded {_cabb =1;};if _edbb =_cece .WriteBit (_cabb );
_edbb !=nil {return _fadf ,_gf .Wrap (_edbb ,_cdde ,"\u0073\u0064\u0048\u0075\u0066\u0066");};return 2,nil ;};type PageInformationSegment struct{_fccb *_af .Reader ;PageBMHeight int ;PageBMWidth int ;ResolutionX int ;ResolutionY int ;_affa bool ;_egeb _f .CombinationOperator ;
_bgca bool ;DefaultPixelValue uint8 ;_fdfa bool ;IsLossless bool ;IsStripe bool ;MaxStripeSize uint16 ;};func (_ffbe *TextRegion )setCodingStatistics ()error {if _ffbe ._bace ==nil {_ffbe ._bace =_afd .NewStats (512,1);};if _ffbe ._deggc ==nil {_ffbe ._deggc =_afd .NewStats (512,1);
};if _ffbe ._bageg ==nil {_ffbe ._bageg =_afd .NewStats (512,1);};if _ffbe ._adge ==nil {_ffbe ._adge =_afd .NewStats (512,1);};if _ffbe ._gaec ==nil {_ffbe ._gaec =_afd .NewStats (512,1);};if _ffbe ._cdfe ==nil {_ffbe ._cdfe =_afd .NewStats (512,1);};
if _ffbe ._egcg ==nil {_ffbe ._egcg =_afd .NewStats (512,1);};if _ffbe ._bea ==nil {_ffbe ._bea =_afd .NewStats (1<<uint (_ffbe ._fdge ),1);};if _ffbe ._gffge ==nil {_ffbe ._gffge =_afd .NewStats (512,1);};if _ffbe ._gfae ==nil {_ffbe ._gfae =_afd .NewStats (512,1);
};if _ffbe ._dbf ==nil {var _fddfa error ;_ffbe ._dbf ,_fddfa =_afd .New (_ffbe ._bbbd );if _fddfa !=nil {return _fddfa ;};};return nil ;};type Segmenter interface{Init (_gfcg *Header ,_cccc *_af .Reader )error ;};func (_cffge *SymbolDictionary )setSymbolsArray ()error {if _cffge ._agc ==nil {if _fgbb :=_cffge .retrieveImportSymbols ();
_fgbb !=nil {return _fgbb ;};};if _cffge ._gggg ==nil {_cffge ._gggg =append (_cffge ._gggg ,_cffge ._agc ...);};return nil ;};func (_bdab *Header )readSegmentNumber (_dddd *_af .Reader )error {const _gfdf ="\u0072\u0065\u0061\u0064\u0053\u0065\u0067\u006d\u0065\u006e\u0074\u004eu\u006d\u0062\u0065\u0072";
_fcea :=make ([]byte ,4);_ ,_ccea :=_dddd .Read (_fcea );if _ccea !=nil {return _gf .Wrap (_ccea ,_gfdf ,"");};_bdab .SegmentNumber =_da .BigEndian .Uint32 (_fcea );return nil ;};var _ SegmentEncoder =&RegionSegment {};func (_ab *EndOfStripe )Init (h *Header ,r *_af .Reader )error {_ab ._cg =r ;
return _ab .parseHeader ()};func (_edgg *TextRegion )InitEncode (globalSymbolsMap ,localSymbolsMap map[int ]int ,comps []int ,inLL *_f .Points ,symbols *_f .Bitmaps ,classIDs *_ae .IntSlice ,boxes *_f .Boxes ,width ,height ,symBits int ){_edgg .RegionInfo =&RegionSegment {BitmapWidth :uint32 (width ),BitmapHeight :uint32 (height )};
_edgg ._cgba =globalSymbolsMap ;_edgg ._bcbgd =localSymbolsMap ;_edgg ._bcgb =comps ;_edgg ._eced =inLL ;_edgg ._aggd =symbols ;_edgg ._fdbf =classIDs ;_edgg ._agbe =boxes ;_edgg ._cddba =symBits ;};func (_bfefc *PatternDictionary )computeSegmentDataStructure ()error {_bfefc .DataOffset =_bfefc ._cffg .AbsolutePosition ();
_bfefc .DataHeaderLength =_bfefc .DataOffset -_bfefc .DataHeaderOffset ;_bfefc .DataLength =int64 (_bfefc ._cffg .AbsoluteLength ())-_bfefc .DataHeaderLength ;return nil ;};func (_gba Type )String ()string {switch _gba {case TSymbolDictionary :return "\u0053\u0079\u006d\u0062\u006f\u006c\u0020\u0044\u0069\u0063\u0074\u0069o\u006e\u0061\u0072\u0079";
case TIntermediateTextRegion :return "\u0049n\u0074\u0065\u0072\u006d\u0065\u0064\u0069\u0061\u0074\u0065\u0020T\u0065\u0078\u0074\u0020\u0052\u0065\u0067\u0069\u006f\u006e";case TImmediateTextRegion :return "I\u006d\u006d\u0065\u0064ia\u0074e\u0020\u0054\u0065\u0078\u0074 \u0052\u0065\u0067\u0069\u006f\u006e";
case TImmediateLosslessTextRegion :return "\u0049\u006d\u006d\u0065\u0064\u0069\u0061\u0074\u0065\u0020L\u006f\u0073\u0073\u006c\u0065\u0073\u0073 \u0054\u0065\u0078\u0074\u0020\u0052\u0065\u0067\u0069\u006f\u006e";case TPatternDictionary :return "\u0050a\u0074t\u0065\u0072\u006e\u0020\u0044i\u0063\u0074i\u006f\u006e\u0061\u0072\u0079";
case TIntermediateHalftoneRegion :return "\u0049\u006e\u0074\u0065r\u006d\u0065\u0064\u0069\u0061\u0074\u0065\u0020\u0048\u0061l\u0066t\u006f\u006e\u0065\u0020\u0052\u0065\u0067i\u006f\u006e";case TImmediateHalftoneRegion :return "\u0049m\u006d\u0065\u0064\u0069a\u0074\u0065\u0020\u0048\u0061l\u0066t\u006fn\u0065\u0020\u0052\u0065\u0067\u0069\u006fn";
case TImmediateLosslessHalftoneRegion :return "\u0049\u006d\u006ded\u0069\u0061\u0074\u0065\u0020\u004c\u006f\u0073\u0073l\u0065s\u0073 \u0048a\u006c\u0066\u0074\u006f\u006e\u0065\u0020\u0052\u0065\u0067\u0069\u006f\u006e";case TIntermediateGenericRegion :return "I\u006e\u0074\u0065\u0072\u006d\u0065d\u0069\u0061\u0074\u0065\u0020\u0047\u0065\u006e\u0065r\u0069\u0063\u0020R\u0065g\u0069\u006f\u006e";
case TImmediateGenericRegion :return "\u0049m\u006d\u0065\u0064\u0069\u0061\u0074\u0065\u0020\u0047\u0065\u006ee\u0072\u0069\u0063\u0020\u0052\u0065\u0067\u0069\u006f\u006e";case TImmediateLosslessGenericRegion :return "\u0049\u006d\u006d\u0065\u0064\u0069a\u0074\u0065\u0020\u004c\u006f\u0073\u0073\u006c\u0065\u0073\u0073\u0020\u0047e\u006e\u0065\u0072\u0069\u0063\u0020\u0052e\u0067\u0069\u006f\u006e";
case TIntermediateGenericRefinementRegion :return "\u0049\u006e\u0074\u0065\u0072\u006d\u0065\u0064\u0069\u0061\u0074\u0065\u0020\u0047\u0065\u006e\u0065\u0072\u0069\u0063\u0020\u0052\u0065\u0066i\u006e\u0065\u006d\u0065\u006et\u0020\u0052e\u0067\u0069\u006f\u006e";
case TImmediateGenericRefinementRegion :return "I\u006d\u006d\u0065\u0064\u0069\u0061t\u0065\u0020\u0047\u0065\u006e\u0065r\u0069\u0063\u0020\u0052\u0065\u0066\u0069n\u0065\u006d\u0065\u006e\u0074\u0020\u0052\u0065\u0067\u0069o\u006e";case TImmediateLosslessGenericRefinementRegion :return "\u0049m\u006d\u0065d\u0069\u0061\u0074\u0065 \u004c\u006f\u0073s\u006c\u0065\u0073\u0073\u0020\u0047\u0065\u006e\u0065ri\u0063\u0020\u0052e\u0066\u0069n\u0065\u006d\u0065\u006e\u0074\u0020R\u0065\u0067i\u006f\u006e";
case TPageInformation :return "\u0050\u0061g\u0065\u0020\u0049n\u0066\u006f\u0072\u006d\u0061\u0074\u0069\u006f\u006e";case TEndOfPage :return "E\u006e\u0064\u0020\u004f\u0066\u0020\u0050\u0061\u0067\u0065";case TEndOfStrip :return "\u0045\u006e\u0064 \u004f\u0066\u0020\u0053\u0074\u0072\u0069\u0070";
case TEndOfFile :return "E\u006e\u0064\u0020\u004f\u0066\u0020\u0046\u0069\u006c\u0065";case TProfiles :return "\u0050\u0072\u006f\u0066\u0069\u006c\u0065\u0073";case TTables :return "\u0054\u0061\u0062\u006c\u0065\u0073";case TExtension :return "\u0045x\u0074\u0065\u006e\u0073\u0069\u006fn";
case TBitmap :return "\u0042\u0069\u0074\u006d\u0061\u0070";};return "I\u006ev\u0061\u006c\u0069\u0064\u0020\u0053\u0065\u0067m\u0065\u006e\u0074\u0020Ki\u006e\u0064";};func (_eddb *SymbolDictionary )decodeDirectlyThroughGenericRegion (_ccge ,_dfaf uint32 )error {if _eddb ._daee ==nil {_eddb ._daee =NewGenericRegion (_eddb ._ecdbb );
};_eddb ._daee .setParametersWithAt (false ,byte (_eddb .SdTemplate ),false ,false ,_eddb .SdATX ,_eddb .SdATY ,_ccge ,_dfaf ,_eddb ._gabd ,_eddb ._edffg );return _eddb .addSymbol (_eddb ._daee );};func (_egbb *PatternDictionary )readPatternWidthAndHeight ()error {_bdge ,_efbcc :=_egbb ._cffg .ReadByte ();
if _efbcc !=nil {return _efbcc ;};_egbb .HdpWidth =_bdge ;_bdge ,_efbcc =_egbb ._cffg .ReadByte ();if _efbcc !=nil {return _efbcc ;};_egbb .HdpHeight =_bdge ;return nil ;};func (_fage *GenericRegion )setParameters (_bbad bool ,_gca ,_ceab int64 ,_fffb ,_gee uint32 ){_fage .IsMMREncoded =_bbad ;
_fage .DataOffset =_gca ;_fage .DataLength =_ceab ;_fage .RegionSegment .BitmapHeight =_fffb ;_fage .RegionSegment .BitmapWidth =_gee ;_fage ._aaa =nil ;_fage .Bitmap =nil ;};func (_fdd *GenericRegion )InitEncode (bm *_f .Bitmap ,xLoc ,yLoc ,template int ,duplicateLineRemoval bool )error {const _dc ="\u0047e\u006e\u0065\u0072\u0069\u0063\u0052\u0065\u0067\u0069\u006f\u006e.\u0049\u006e\u0069\u0074\u0045\u006e\u0063\u006f\u0064\u0065";
if bm ==nil {return _gf .Error (_dc ,"\u0070\u0072\u006f\u0076id\u0065\u0064\u0020\u006e\u0069\u006c\u0020\u0062\u0069\u0074\u006d\u0061\u0070");};if xLoc < 0||yLoc < 0{return _gf .Error (_dc ,"\u0078\u0020\u0061\u006e\u0064\u0020\u0079\u0020\u006c\u006f\u0063\u0061\u0074i\u006f\u006e\u0020\u006d\u0075\u0073t\u0020\u0062\u0065\u0020\u0067\u0072\u0065\u0061\u0074\u0065\u0072\u0020\u0074h\u0061\u006e\u0020\u0030");
};_fdd .Bitmap =bm ;_fdd .GBTemplate =byte (template );switch _fdd .GBTemplate {case 0:_fdd .GBAtX =[]int8 {3,-3,2,-2};_fdd .GBAtY =[]int8 {-1,-1,-2,-2};case 1:_fdd .GBAtX =[]int8 {3};_fdd .GBAtY =[]int8 {-1};case 2,3:_fdd .GBAtX =[]int8 {2};_fdd .GBAtY =[]int8 {-1};
default:return _gf .Errorf (_dc ,"\u0070\u0072o\u0076\u0069\u0064\u0065\u0064 \u0074\u0065\u006d\u0070\u006ca\u0074\u0065\u003a\u0020\u0027\u0025\u0064\u0027\u0020\u006e\u006f\u0074\u0020\u0069\u006e\u0020\u0076\u0061\u006c\u0069\u0064\u0020\u0072\u0061\u006e\u0067\u0065\u0020\u007b\u0030\u002c\u0031\u002c\u0032\u002c\u0033\u007d",template );
};_fdd .RegionSegment =&RegionSegment {BitmapHeight :uint32 (bm .Height ),BitmapWidth :uint32 (bm .Width ),XLocation :uint32 (xLoc ),YLocation :uint32 (yLoc )};_fdd .IsTPGDon =duplicateLineRemoval ;return nil ;};func NewGenericRegion (r *_af .Reader )*GenericRegion {return &GenericRegion {RegionSegment :NewRegionSegment (r ),_cfag :r };
};func (_gde *GenericRefinementRegion )overrideAtTemplate0 (_fbd ,_aacg ,_cdb ,_dd ,_efc int )int {if _gde ._ba [0]{_fbd &=0xfff7;if _gde .GrAtY [0]==0&&int (_gde .GrAtX [0])>=-_efc {_fbd |=(_dd >>uint (7-(_efc +int (_gde .GrAtX [0])))&0x1)<<3;}else {_fbd |=_gde .getPixel (_gde .RegionBitmap ,_aacg +int (_gde .GrAtX [0]),_cdb +int (_gde .GrAtY [0]))<<3;
};};if _gde ._ba [1]{_fbd &=0xefff;if _gde .GrAtY [1]==0&&int (_gde .GrAtX [1])>=-_efc {_fbd |=(_dd >>uint (7-(_efc +int (_gde .GrAtX [1])))&0x1)<<12;}else {_fbd |=_gde .getPixel (_gde .ReferenceBitmap ,_aacg +int (_gde .GrAtX [1]),_cdb +int (_gde .GrAtY [1]));
};};return _fbd ;};func (_deag *Header )String ()string {_cggb :=&_a .Builder {};_cggb .WriteString ("\u000a[\u0053E\u0047\u004d\u0045\u004e\u0054-\u0048\u0045A\u0044\u0045\u0052\u005d\u000a");_cggb .WriteString (_b .Sprintf ("\t\u002d\u0020\u0053\u0065gm\u0065n\u0074\u004e\u0075\u006d\u0062e\u0072\u003a\u0020\u0025\u0076\u000a",_deag .SegmentNumber ));
_cggb .WriteString (_b .Sprintf ("\u0009\u002d\u0020T\u0079\u0070\u0065\u003a\u0020\u0025\u0076\u000a",_deag .Type ));_cggb .WriteString (_b .Sprintf ("\u0009-\u0020R\u0065\u0074\u0061\u0069\u006eF\u006c\u0061g\u003a\u0020\u0025\u0076\u000a",_deag .RetainFlag ));
_cggb .WriteString (_b .Sprintf ("\u0009\u002d\u0020Pa\u0067\u0065\u0041\u0073\u0073\u006f\u0063\u0069\u0061\u0074\u0069\u006f\u006e\u003a\u0020\u0025\u0076\u000a",_deag .PageAssociation ));_cggb .WriteString (_b .Sprintf ("\u0009\u002d\u0020\u0050\u0061\u0067\u0065\u0041\u0073\u0073\u006f\u0063\u0069\u0061\u0074i\u006fn\u0046\u0069\u0065\u006c\u0064\u0053\u0069\u007a\u0065\u003a\u0020\u0025\u0076\u000a",_deag .PageAssociationFieldSize ));
_cggb .WriteString ("\u0009-\u0020R\u0054\u0053\u0045\u0047\u004d\u0045\u004e\u0054\u0053\u003a\u000a");for _ ,_aafa :=range _deag .RTSNumbers {_cggb .WriteString (_b .Sprintf ("\u0009\t\u002d\u0020\u0025\u0064\u000a",_aafa ));};_cggb .WriteString (_b .Sprintf ("\t\u002d \u0048\u0065\u0061\u0064\u0065\u0072\u004c\u0065n\u0067\u0074\u0068\u003a %\u0076\u000a",_deag .HeaderLength ));
_cggb .WriteString (_b .Sprintf ("\u0009-\u0020\u0053\u0065\u0067m\u0065\u006e\u0074\u0044\u0061t\u0061L\u0065n\u0067\u0074\u0068\u003a\u0020\u0025\u0076\n",_deag .SegmentDataLength ));_cggb .WriteString (_b .Sprintf ("\u0009\u002d\u0020\u0053\u0065\u0067\u006d\u0065\u006e\u0074D\u0061\u0074\u0061\u0053\u0074\u0061\u0072t\u004f\u0066\u0066\u0073\u0065\u0074\u003a\u0020\u0025\u0076\u000a",_deag .SegmentDataStartOffset ));
return _cggb .String ();};func (_acd *PageInformationSegment )Init (h *Header ,r *_af .Reader )(_cbgc error ){_acd ._fccb =r ;if _cbgc =_acd .parseHeader ();_cbgc !=nil {return _gf .Wrap (_cbgc ,"P\u0061\u0067\u0065\u0049\u006e\u0066o\u0072\u006d\u0061\u0074\u0069\u006f\u006e\u0053\u0065g\u006d\u0065\u006et\u002eI\u006e\u0069\u0074","");
};return nil ;};func (_accd *GenericRefinementRegion )Init (header *Header ,r *_af .Reader )error {_accd ._fa =header ;_accd ._cf =r ;_accd .RegionInfo =NewRegionSegment (r );return _accd .parseHeader ();};func (_fabc *HalftoneRegion )computeSegmentDataStructure ()error {_fabc .DataOffset =_fabc ._efbc .AbsolutePosition ();
_fabc .DataHeaderLength =_fabc .DataOffset -_fabc .DataHeaderOffset ;_fabc .DataLength =int64 (_fabc ._efbc .AbsoluteLength ())-_fabc .DataHeaderLength ;return nil ;};func (_caba *Header )readSegmentDataLength (_abbg *_af .Reader )(_affed error ){_caba .SegmentDataLength ,_affed =_abbg .ReadBits (32);
if _affed !=nil {return _affed ;};_caba .SegmentDataLength &=_g .MaxInt32 ;return nil ;};func (_eaeb *GenericRegion )overrideAtTemplate3 (_fcbd ,_abecc ,_beg ,_efd ,_cbe int )int {_fcbd &=0x3EF;if _eaeb .GBAtY [0]==0&&_eaeb .GBAtX [0]>=-int8 (_cbe ){_fcbd |=(_efd >>uint (7-(int8 (_cbe )+_eaeb .GBAtX [0]))&0x1)<<4;
}else {_fcbd |=int (_eaeb .getPixel (_abecc +int (_eaeb .GBAtX [0]),_beg +int (_eaeb .GBAtY [0])))<<4;};return _fcbd ;};func (_gbfa *TextRegion )getUserTable (_cfcc int )(_eb .Tabler ,error ){const _begg ="\u0067\u0065\u0074U\u0073\u0065\u0072\u0054\u0061\u0062\u006c\u0065";
var _geee int ;for _ ,_ccccd :=range _gbfa .Header .RTSegments {if _ccccd .Type ==53{if _geee ==_cfcc {_aeff ,_fcee :=_ccccd .GetSegmentData ();if _fcee !=nil {return nil ,_fcee ;};_efdee ,_dgfe :=_aeff .(*TableSegment );if !_dgfe {_ac .Log .Debug (_b .Sprintf ("\u0073\u0065\u0067\u006d\u0065\u006e\u0074 \u0077\u0069\u0074h\u0020\u0054\u0079p\u0065\u00205\u0033\u0020\u002d\u0020\u0061\u006ed\u0020in\u0064\u0065\u0078\u003a\u0020\u0025\u0064\u0020\u006e\u006f\u0074\u0020\u0061\u0020\u0054\u0061\u0062\u006c\u0065\u0053\u0065\u0067\u006d\u0065\u006e\u0074",_ccccd .SegmentNumber ));
return nil ,_gf .Error (_begg ,"\u0073\u0065\u0067\u006d\u0065\u006e\u0074 \u0077\u0069\u0074h\u0020\u0054\u0079\u0070e\u0020\u0035\u0033\u0020\u0069\u0073\u0020\u006e\u006f\u0074\u0020\u0061\u0020\u002a\u0054\u0061\u0062\u006c\u0065\u0053\u0065\u0067\u006d\u0065\u006e\u0074");
};return _eb .NewEncodedTable (_efdee );};_geee ++;};};return nil ,nil ;};func (_fagg *SymbolDictionary )setRefinementAtPixels ()error {if !_fagg .UseRefinementAggregation ||_fagg .SdrTemplate !=0{return nil ;};if _gebd :=_fagg .readRefinementAtPixels (2);
_gebd !=nil {return _gebd ;};return nil ;};func (_gaff *GenericRegion )decodeTemplate1 (_bfcb ,_gdaf ,_caa int ,_dgc ,_efe int )(_dfda error ){const _adfc ="\u0064e\u0063o\u0064\u0065\u0054\u0065\u006d\u0070\u006c\u0061\u0074\u0065\u0031";var (_ebca ,_bfb int ;
_beba ,_ccacg int ;_cabg byte ;_eafe ,_abef int ;);if _bfcb >=1{_cabg ,_dfda =_gaff .Bitmap .GetByte (_efe );if _dfda !=nil {return _gf .Wrap (_dfda ,_adfc ,"\u006ci\u006e\u0065\u0020\u003e\u003d\u00201");};_beba =int (_cabg );};if _bfcb >=2{_cabg ,_dfda =_gaff .Bitmap .GetByte (_efe -_gaff .Bitmap .RowStride );
if _dfda !=nil {return _gf .Wrap (_dfda ,_adfc ,"\u006ci\u006e\u0065\u0020\u003e\u003d\u00202");};_ccacg =int (_cabg )<<5;};_ebca =((_beba >>1)&0x1f8)|((_ccacg >>1)&0x1e00);for _bgaa :=0;_bgaa < _caa ;_bgaa =_eafe {var (_bdgb byte ;_bfcbg int ;);_eafe =_bgaa +8;
if _gfdd :=_gdaf -_bgaa ;_gfdd > 8{_bfcbg =8;}else {_bfcbg =_gfdd ;};if _bfcb > 0{_beba <<=8;if _eafe < _gdaf {_cabg ,_dfda =_gaff .Bitmap .GetByte (_efe +1);if _dfda !=nil {return _gf .Wrap (_dfda ,_adfc ,"\u006c\u0069\u006e\u0065\u0020\u003e\u0020\u0030");
};_beba |=int (_cabg );};};if _bfcb > 1{_ccacg <<=8;if _eafe < _gdaf {_cabg ,_dfda =_gaff .Bitmap .GetByte (_efe -_gaff .Bitmap .RowStride +1);if _dfda !=nil {return _gf .Wrap (_dfda ,_adfc ,"\u006c\u0069\u006e\u0065\u0020\u003e\u0020\u0031");};_ccacg |=int (_cabg )<<5;
};};for _gfb :=0;_gfb < _bfcbg ;_gfb ++{if _gaff ._bgda {_bfb =_gaff .overrideAtTemplate1 (_ebca ,_bgaa +_gfb ,_bfcb ,int (_bdgb ),_gfb );_gaff ._ged .SetIndex (int32 (_bfb ));}else {_gaff ._ged .SetIndex (int32 (_ebca ));};_abef ,_dfda =_gaff ._egg .DecodeBit (_gaff ._ged );
if _dfda !=nil {return _gf .Wrap (_dfda ,_adfc ,"");};_bdgb |=byte (_abef )<<uint (7-_gfb );_egaa :=uint (8-_gfb );_ebca =((_ebca &0xefb)<<1)|_abef |((_beba >>_egaa )&0x8)|((_ccacg >>_egaa )&0x200);};if _cac :=_gaff .Bitmap .SetByte (_dgc ,_bdgb );_cac !=nil {return _gf .Wrap (_cac ,_adfc ,"");
};_dgc ++;_efe ++;};return nil ;};func (_ccfa *SymbolDictionary )readNumberOfExportedSymbols ()error {_gcce ,_faaa :=_ccfa ._ecdbb .ReadBits (32);if _faaa !=nil {return _faaa ;};_ccfa .NumberOfExportedSymbols =uint32 (_gcce &_g .MaxUint32 );return nil ;
};type TextRegion struct{_bbbd *_af .Reader ;RegionInfo *RegionSegment ;SbrTemplate int8 ;SbDsOffset int8 ;DefaultPixel int8 ;CombinationOperator _f .CombinationOperator ;IsTransposed int8 ;ReferenceCorner int16 ;LogSBStrips int16 ;UseRefinement bool ;
IsHuffmanEncoded bool ;SbHuffRSize int8 ;SbHuffRDY int8 ;SbHuffRDX int8 ;SbHuffRDHeight int8 ;SbHuffRDWidth int8 ;SbHuffDT int8 ;SbHuffDS int8 ;SbHuffFS int8 ;SbrATX []int8 ;SbrATY []int8 ;NumberOfSymbolInstances uint32 ;_abdc int64 ;SbStrips int8 ;NumberOfSymbols uint32 ;
RegionBitmap *_f .Bitmap ;Symbols []*_f .Bitmap ;_dbf *_afd .Decoder ;_gced *GenericRefinementRegion ;_bace *_afd .DecoderStats ;_deggc *_afd .DecoderStats ;_bageg *_afd .DecoderStats ;_adge *_afd .DecoderStats ;_gaec *_afd .DecoderStats ;_cdfe *_afd .DecoderStats ;
_egcg *_afd .DecoderStats ;_bea *_afd .DecoderStats ;_gffge *_afd .DecoderStats ;_gfae *_afd .DecoderStats ;_ceed *_afd .DecoderStats ;_fdge int8 ;_bdea *_eb .FixedSizeTable ;Header *Header ;_bdbb _eb .Tabler ;_cgga _eb .Tabler ;_ccdfc _eb .Tabler ;_cad _eb .Tabler ;
_gfcfg _eb .Tabler ;_cegdd _eb .Tabler ;_cgea _eb .Tabler ;_ebee _eb .Tabler ;_cgba ,_bcbgd map[int ]int ;_bcgb []int ;_eced *_f .Points ;_aggd *_f .Bitmaps ;_fdbf *_ae .IntSlice ;_ggba ,_cddba int ;_agbe *_f .Boxes ;};func (_cdbc *RegionSegment )Encode (w _af .BinaryWriter )(_gfa int ,_add error ){const _cgag ="R\u0065g\u0069\u006f\u006e\u0053\u0065\u0067\u006d\u0065n\u0074\u002e\u0045\u006eco\u0064\u0065";
_cacd :=make ([]byte ,4);_da .BigEndian .PutUint32 (_cacd ,_cdbc .BitmapWidth );_gfa ,_add =w .Write (_cacd );if _add !=nil {return 0,_gf .Wrap (_add ,_cgag ,"\u0057\u0069\u0064t\u0068");};_da .BigEndian .PutUint32 (_cacd ,_cdbc .BitmapHeight );var _fffc int ;
_fffc ,_add =w .Write (_cacd );if _add !=nil {return 0,_gf .Wrap (_add ,_cgag ,"\u0048\u0065\u0069\u0067\u0068\u0074");};_gfa +=_fffc ;_da .BigEndian .PutUint32 (_cacd ,_cdbc .XLocation );_fffc ,_add =w .Write (_cacd );if _add !=nil {return 0,_gf .Wrap (_add ,_cgag ,"\u0058L\u006f\u0063\u0061\u0074\u0069\u006fn");
};_gfa +=_fffc ;_da .BigEndian .PutUint32 (_cacd ,_cdbc .YLocation );_fffc ,_add =w .Write (_cacd );if _add !=nil {return 0,_gf .Wrap (_add ,_cgag ,"\u0059L\u006f\u0063\u0061\u0074\u0069\u006fn");};_gfa +=_fffc ;if _add =w .WriteByte (byte (_cdbc .CombinaionOperator )&0x07);
_add !=nil {return 0,_gf .Wrap (_add ,_cgag ,"c\u006fm\u0062\u0069\u006e\u0061\u0074\u0069\u006f\u006e \u006f\u0070\u0065\u0072at\u006f\u0072");};_gfa ++;return _gfa ,nil ;};func (_cbga *SymbolDictionary )readNumberOfNewSymbols ()error {_fageg ,_dgga :=_cbga ._ecdbb .ReadBits (32);
if _dgga !=nil {return _dgga ;};_cbga .NumberOfNewSymbols =uint32 (_fageg &_g .MaxUint32 );return nil ;};type PatternDictionary struct{_cffg *_af .Reader ;DataHeaderOffset int64 ;DataHeaderLength int64 ;DataOffset int64 ;DataLength int64 ;GBAtX []int8 ;
GBAtY []int8 ;IsMMREncoded bool ;HDTemplate byte ;HdpWidth byte ;HdpHeight byte ;Patterns []*_f .Bitmap ;GrayMax uint32 ;};func (_afdd *GenericRefinementRegion )decodeTemplate (_cea ,_ffab ,_gbc ,_gdg ,_gad ,_adb ,_cde ,_dgb ,_fd ,_ag int ,_ecb templater )(_cba error ){var (_ded ,_bcb ,_dee ,_bbf ,_fef int16 ;
_edec ,_gcg ,_bfg ,_afc int ;_fce byte ;);if _fd >=1&&(_fd -1)< _afdd .ReferenceBitmap .Height {_fce ,_cba =_afdd .ReferenceBitmap .GetByte (_ag -_gdg );if _cba !=nil {return _cba ;};_edec =int (_fce );};if _fd >=0&&(_fd )< _afdd .ReferenceBitmap .Height {_fce ,_cba =_afdd .ReferenceBitmap .GetByte (_ag );
if _cba !=nil {return _cba ;};_gcg =int (_fce );};if _fd >=-1&&(_fd +1)< _afdd .ReferenceBitmap .Height {_fce ,_cba =_afdd .ReferenceBitmap .GetByte (_ag +_gdg );if _cba !=nil {return _cba ;};_bfg =int (_fce );};_ag ++;if _cea >=1{_fce ,_cba =_afdd .RegionBitmap .GetByte (_dgb -_gbc );
if _cba !=nil {return _cba ;};_afc =int (_fce );};_dgb ++;_eace :=_afdd .ReferenceDX %8;_bgd :=6+_eace ;_dfa :=_ag %_gdg ;if _bgd >=0{if _bgd < 8{_ded =int16 (_edec >>uint (_bgd ))&0x07;};if _bgd < 8{_bcb =int16 (_gcg >>uint (_bgd ))&0x07;};if _bgd < 8{_dee =int16 (_bfg >>uint (_bgd ))&0x07;
};if _bgd ==6&&_dfa > 1{if _fd >=1&&(_fd -1)< _afdd .ReferenceBitmap .Height {_fce ,_cba =_afdd .ReferenceBitmap .GetByte (_ag -_gdg -2);if _cba !=nil {return _cba ;};_ded |=int16 (_fce <<2)&0x04;};if _fd >=0&&_fd < _afdd .ReferenceBitmap .Height {_fce ,_cba =_afdd .ReferenceBitmap .GetByte (_ag -2);
if _cba !=nil {return _cba ;};_bcb |=int16 (_fce <<2)&0x04;};if _fd >=-1&&_fd +1< _afdd .ReferenceBitmap .Height {_fce ,_cba =_afdd .ReferenceBitmap .GetByte (_ag +_gdg -2);if _cba !=nil {return _cba ;};_dee |=int16 (_fce <<2)&0x04;};};if _bgd ==0{_edec =0;
_gcg =0;_bfg =0;if _dfa < _gdg -1{if _fd >=1&&_fd -1< _afdd .ReferenceBitmap .Height {_fce ,_cba =_afdd .ReferenceBitmap .GetByte (_ag -_gdg );if _cba !=nil {return _cba ;};_edec =int (_fce );};if _fd >=0&&_fd < _afdd .ReferenceBitmap .Height {_fce ,_cba =_afdd .ReferenceBitmap .GetByte (_ag );
if _cba !=nil {return _cba ;};_gcg =int (_fce );};if _fd >=-1&&_fd +1< _afdd .ReferenceBitmap .Height {_fce ,_cba =_afdd .ReferenceBitmap .GetByte (_ag +_gdg );if _cba !=nil {return _cba ;};_bfg =int (_fce );};};_ag ++;};}else {_ded =int16 (_edec <<1)&0x07;
_bcb =int16 (_gcg <<1)&0x07;_dee =int16 (_bfg <<1)&0x07;_edec =0;_gcg =0;_bfg =0;if _dfa < _gdg -1{if _fd >=1&&_fd -1< _afdd .ReferenceBitmap .Height {_fce ,_cba =_afdd .ReferenceBitmap .GetByte (_ag -_gdg );if _cba !=nil {return _cba ;};_edec =int (_fce );
};if _fd >=0&&_fd < _afdd .ReferenceBitmap .Height {_fce ,_cba =_afdd .ReferenceBitmap .GetByte (_ag );if _cba !=nil {return _cba ;};_gcg =int (_fce );};if _fd >=-1&&_fd +1< _afdd .ReferenceBitmap .Height {_fce ,_cba =_afdd .ReferenceBitmap .GetByte (_ag +_gdg );
if _cba !=nil {return _cba ;};_bfg =int (_fce );};_ag ++;};_ded |=int16 ((_edec >>7)&0x07);_bcb |=int16 ((_gcg >>7)&0x07);_dee |=int16 ((_bfg >>7)&0x07);};_bbf =int16 (_afc >>6);_fef =0;_eba :=(2-_eace )%8;_edec <<=uint (_eba );_gcg <<=uint (_eba );_bfg <<=uint (_eba );
_afc <<=2;var _gab int ;for _baa :=0;_baa < _ffab ;_baa ++{_abeb :=_baa &0x07;_cceb :=_ecb .form (_ded ,_bcb ,_dee ,_bbf ,_fef );if _afdd ._faf {_fce ,_cba =_afdd .RegionBitmap .GetByte (_afdd .RegionBitmap .GetByteIndex (_baa ,_cea ));if _cba !=nil {return _cba ;
};_afdd ._ceb .SetIndex (int32 (_afdd .overrideAtTemplate0 (int (_cceb ),_baa ,_cea ,int (_fce ),_abeb )));}else {_afdd ._ceb .SetIndex (int32 (_cceb ));};_gab ,_cba =_afdd ._ec .DecodeBit (_afdd ._ceb );if _cba !=nil {return _cba ;};if _cba =_afdd .RegionBitmap .SetPixel (_baa ,_cea ,byte (_gab ));
_cba !=nil {return _cba ;};_ded =((_ded <<1)|0x01&int16 (_edec >>7))&0x07;_bcb =((_bcb <<1)|0x01&int16 (_gcg >>7))&0x07;_dee =((_dee <<1)|0x01&int16 (_bfg >>7))&0x07;_bbf =((_bbf <<1)|0x01&int16 (_afc >>7))&0x07;_fef =int16 (_gab );if (_baa -int (_afdd .ReferenceDX ))%8==5{_edec =0;
_gcg =0;_bfg =0;if ((_baa -int (_afdd .ReferenceDX ))/8)+1< _afdd .ReferenceBitmap .RowStride {if _fd >=1&&(_fd -1)< _afdd .ReferenceBitmap .Height {_fce ,_cba =_afdd .ReferenceBitmap .GetByte (_ag -_gdg );if _cba !=nil {return _cba ;};_edec =int (_fce );
};if _fd >=0&&_fd < _afdd .ReferenceBitmap .Height {_fce ,_cba =_afdd .ReferenceBitmap .GetByte (_ag );if _cba !=nil {return _cba ;};_gcg =int (_fce );};if _fd >=-1&&(_fd +1)< _afdd .ReferenceBitmap .Height {_fce ,_cba =_afdd .ReferenceBitmap .GetByte (_ag +_gdg );
if _cba !=nil {return _cba ;};_bfg =int (_fce );};};_ag ++;}else {_edec <<=1;_gcg <<=1;_bfg <<=1;};if _abeb ==5&&_cea >=1{if ((_baa >>3)+1)>=_afdd .RegionBitmap .RowStride {_afc =0;}else {_fce ,_cba =_afdd .RegionBitmap .GetByte (_dgb -_gbc );if _cba !=nil {return _cba ;
};_afc =int (_fce );};_dgb ++;}else {_afc <<=1;};};return nil ;};func (_abge *SymbolDictionary )decodeAggregate (_feea ,_gffg uint32 )error {var (_gcde int64 ;_fca error ;);if _abge .IsHuffmanEncoded {_gcde ,_fca =_abge .huffDecodeRefAggNInst ();if _fca !=nil {return _fca ;
};}else {_fgdba ,_cfcgg :=_abge ._edffg .DecodeInt (_abge ._bafd );if _cfcgg !=nil {return _cfcgg ;};_gcde =int64 (_fgdba );};if _gcde > 1{return _abge .decodeThroughTextRegion (_feea ,_gffg ,uint32 (_gcde ));}else if _gcde ==1{return _abge .decodeRefinedSymbol (_feea ,_gffg );
};return nil ;};func _eecb (_gdc int )int {if _gdc ==0{return 0;};_gdc |=_gdc >>1;_gdc |=_gdc >>2;_gdc |=_gdc >>4;_gdc |=_gdc >>8;_gdc |=_gdc >>16;return (_gdc +1)>>1;};func (_bff *Header )subInputReader ()(*_af .Reader ,error ){_cbded :=int (_bff .SegmentDataLength );
if _bff .SegmentDataLength ==_g .MaxInt32 {_cbded =-1;};return _bff .Reader .NewPartialReader (int (_bff .SegmentDataStartOffset ),_cbded ,false );};func (_eaa *GenericRefinementRegion )updateOverride ()error {if _eaa .GrAtX ==nil ||_eaa .GrAtY ==nil {return _ad .New ("\u0041\u0054\u0020\u0070\u0069\u0078\u0065\u006c\u0073\u0020\u006e\u006ft\u0020\u0073\u0065\u0074");
};if len (_eaa .GrAtX )!=len (_eaa .GrAtY ){return _ad .New ("A\u0054\u0020\u0070\u0069xe\u006c \u0069\u006e\u0063\u006f\u006es\u0069\u0073\u0074\u0065\u006e\u0074");};_eaa ._ba =make ([]bool ,len (_eaa .GrAtX ));switch _eaa .TemplateID {case 0:if _eaa .GrAtX [0]!=-1&&_eaa .GrAtY [0]!=-1{_eaa ._ba [0]=true ;
_eaa ._faf =true ;};if _eaa .GrAtX [1]!=-1&&_eaa .GrAtY [1]!=-1{_eaa ._ba [1]=true ;_eaa ._faf =true ;};case 1:_eaa ._faf =false ;};return nil ;};func (_geef *TableSegment )Init (h *Header ,r *_af .Reader )error {_geef ._fcae =r ;return _geef .parseHeader ();
};func (_fbab *PageInformationSegment )readIsStriped ()error {_bcgf ,_ecce :=_fbab ._fccb .ReadBit ();if _ecce !=nil {return _ecce ;};if _bcgf ==1{_fbab .IsStripe =true ;};return nil ;};func (_cbff *TextRegion )decodeStripT ()(_eaed int64 ,_aabc error ){if _cbff .IsHuffmanEncoded {if _cbff .SbHuffDT ==3{if _cbff ._ccdfc ==nil {var _bcec int ;
if _cbff .SbHuffFS ==3{_bcec ++;};if _cbff .SbHuffDS ==3{_bcec ++;};_cbff ._ccdfc ,_aabc =_cbff .getUserTable (_bcec );if _aabc !=nil {return 0,_aabc ;};};_eaed ,_aabc =_cbff ._ccdfc .Decode (_cbff ._bbbd );if _aabc !=nil {return 0,_aabc ;};}else {var _eefg _eb .Tabler ;
_eefg ,_aabc =_eb .GetStandardTable (11+int (_cbff .SbHuffDT ));if _aabc !=nil {return 0,_aabc ;};_eaed ,_aabc =_eefg .Decode (_cbff ._bbbd );if _aabc !=nil {return 0,_aabc ;};};}else {var _dabd int32 ;_dabd ,_aabc =_cbff ._dbf .DecodeInt (_cbff ._bace );
if _aabc !=nil {return 0,_aabc ;};_eaed =int64 (_dabd );};_eaed *=int64 (-_cbff .SbStrips );return _eaed ,nil ;};func (_ebae *SymbolDictionary )decodeThroughTextRegion (_dadg ,_cegd ,_gdfc uint32 )error {if _ebae ._bbbab ==nil {_ebae ._bbbab =_gdcbe (_ebae ._ecdbb ,nil );
_ebae ._bbbab .setContexts (_ebae ._gabd ,_afd .NewStats (512,1),_afd .NewStats (512,1),_afd .NewStats (512,1),_afd .NewStats (512,1),_ebae ._dbg ,_afd .NewStats (512,1),_afd .NewStats (512,1),_afd .NewStats (512,1),_afd .NewStats (512,1));};if _cdcg :=_ebae .setSymbolsArray ();
_cdcg !=nil {return _cdcg ;};_ebae ._bbbab .setParameters (_ebae ._edffg ,_ebae .IsHuffmanEncoded ,true ,_dadg ,_cegd ,_gdfc ,1,_ebae ._gagg +_ebae ._aagc ,0,0,0,1,0,0,0,0,0,0,0,0,0,_ebae .SdrTemplate ,_ebae .SdrATX ,_ebae .SdrATY ,_ebae ._gggg ,_ebae ._aegf );
return _ebae .addSymbol (_ebae ._bbbab );};func (_bdg *GenericRegion )decodeTemplate0b (_adbac ,_bcaf ,_dgeb int ,_gbdc ,_gbce int )(_gcge error ){const _bec ="\u0064\u0065c\u006f\u0064\u0065T\u0065\u006d\u0070\u006c\u0061\u0074\u0065\u0030\u0062";var (_dead ,_dca int ;
_fgggb ,_ecfe int ;_ffee byte ;_adac int ;);if _adbac >=1{_ffee ,_gcge =_bdg .Bitmap .GetByte (_gbce );if _gcge !=nil {return _gf .Wrap (_gcge ,_bec ,"\u006ci\u006e\u0065\u0020\u003e\u003d\u00201");};_fgggb =int (_ffee );};if _adbac >=2{_ffee ,_gcge =_bdg .Bitmap .GetByte (_gbce -_bdg .Bitmap .RowStride );
if _gcge !=nil {return _gf .Wrap (_gcge ,_bec ,"\u006ci\u006e\u0065\u0020\u003e\u003d\u00202");};_ecfe =int (_ffee )<<6;};_dead =(_fgggb &0xf0)|(_ecfe &0x3800);for _aaf :=0;_aaf < _dgeb ;_aaf =_adac {var (_bagf byte ;_ffg int ;);_adac =_aaf +8;if _efgf :=_bcaf -_aaf ;
_efgf > 8{_ffg =8;}else {_ffg =_efgf ;};if _adbac > 0{_fgggb <<=8;if _adac < _bcaf {_ffee ,_gcge =_bdg .Bitmap .GetByte (_gbce +1);if _gcge !=nil {return _gf .Wrap (_gcge ,_bec ,"\u006c\u0069\u006e\u0065\u0020\u003e\u0020\u0030");};_fgggb |=int (_ffee );
};};if _adbac > 1{_ecfe <<=8;if _adac < _bcaf {_ffee ,_gcge =_bdg .Bitmap .GetByte (_gbce -_bdg .Bitmap .RowStride +1);if _gcge !=nil {return _gf .Wrap (_gcge ,_bec ,"\u006c\u0069\u006e\u0065\u0020\u003e\u0020\u0031");};_ecfe |=int (_ffee )<<6;};};for _agd :=0;
_agd < _ffg ;_agd ++{_acg :=uint (7-_agd );if _bdg ._bgda {_dca =_bdg .overrideAtTemplate0b (_dead ,_aaf +_agd ,_adbac ,int (_bagf ),_agd ,int (_acg ));_bdg ._ged .SetIndex (int32 (_dca ));}else {_bdg ._ged .SetIndex (int32 (_dead ));};var _bcdb int ;_bcdb ,_gcge =_bdg ._egg .DecodeBit (_bdg ._ged );
if _gcge !=nil {return _gf .Wrap (_gcge ,_bec ,"");};_bagf |=byte (_bcdb <<_acg );_dead =((_dead &0x7bf7)<<1)|_bcdb |((_fgggb >>_acg )&0x10)|((_ecfe >>_acg )&0x800);};if _feb :=_bdg .Bitmap .SetByte (_gbdc ,_bagf );_feb !=nil {return _gf .Wrap (_feb ,_bec ,"");
};_gbdc ++;_gbce ++;};return nil ;};func (_adfbe *TextRegion )setParameters (_degb *_afd .Decoder ,_adfd ,_agff bool ,_gade ,_dcff uint32 ,_deba uint32 ,_efefe int8 ,_ddgdg uint32 ,_fecfe int8 ,_ggge _f .CombinationOperator ,_fcgca int8 ,_gbgb int16 ,_ddfd ,_ffac ,_fbca ,_badd ,_cbeff ,_dgbc ,_fabb ,_egbae ,_abcgd ,_cfb int8 ,_bdcd ,_afaf []int8 ,_eaga []*_f .Bitmap ,_cfaac int8 ){_adfbe ._dbf =_degb ;
_adfbe .IsHuffmanEncoded =_adfd ;_adfbe .UseRefinement =_agff ;_adfbe .RegionInfo .BitmapWidth =_gade ;_adfbe .RegionInfo .BitmapHeight =_dcff ;_adfbe .NumberOfSymbolInstances =_deba ;_adfbe .SbStrips =_efefe ;_adfbe .NumberOfSymbols =_ddgdg ;_adfbe .DefaultPixel =_fecfe ;
_adfbe .CombinationOperator =_ggge ;_adfbe .IsTransposed =_fcgca ;_adfbe .ReferenceCorner =_gbgb ;_adfbe .SbDsOffset =_ddfd ;_adfbe .SbHuffFS =_ffac ;_adfbe .SbHuffDS =_fbca ;_adfbe .SbHuffDT =_badd ;_adfbe .SbHuffRDWidth =_cbeff ;_adfbe .SbHuffRDHeight =_dgbc ;
_adfbe .SbHuffRSize =_abcgd ;_adfbe .SbHuffRDX =_fabb ;_adfbe .SbHuffRDY =_egbae ;_adfbe .SbrTemplate =_cfb ;_adfbe .SbrATX =_bdcd ;_adfbe .SbrATY =_afaf ;_adfbe .Symbols =_eaga ;_adfbe ._fdge =_cfaac ;};func (_fdfb *PatternDictionary )setGbAtPixels (){if _fdfb .HDTemplate ==0{_fdfb .GBAtX =make ([]int8 ,4);
_fdfb .GBAtY =make ([]int8 ,4);_fdfb .GBAtX [0]=-int8 (_fdfb .HdpWidth );_fdfb .GBAtY [0]=0;_fdfb .GBAtX [1]=-3;_fdfb .GBAtY [1]=-1;_fdfb .GBAtX [2]=2;_fdfb .GBAtY [2]=-2;_fdfb .GBAtX [3]=-2;_fdfb .GBAtY [3]=-2;}else {_fdfb .GBAtX =[]int8 {-int8 (_fdfb .HdpWidth )};
_fdfb .GBAtY =[]int8 {0};};};func (_fdag *HalftoneRegion )GetRegionBitmap ()(*_f .Bitmap ,error ){if _fdag .HalftoneRegionBitmap !=nil {return _fdag .HalftoneRegionBitmap ,nil ;};var _eaec error ;_fdag .HalftoneRegionBitmap =_f .New (int (_fdag .RegionSegment .BitmapWidth ),int (_fdag .RegionSegment .BitmapHeight ));
if _fdag .Patterns ==nil ||(_fdag .Patterns !=nil &&len (_fdag .Patterns )==0){_fdag .Patterns ,_eaec =_fdag .GetPatterns ();if _eaec !=nil {return nil ,_eaec ;};};if _fdag .HDefaultPixel ==1{_fdag .HalftoneRegionBitmap .SetDefaultPixel ();};_fccd :=_g .Ceil (_g .Log (float64 (len (_fdag .Patterns )))/_g .Log (2));
_dda :=int (_fccd );var _aegc [][]int ;_aegc ,_eaec =_fdag .grayScaleDecoding (_dda );if _eaec !=nil {return nil ,_eaec ;};if _eaec =_fdag .renderPattern (_aegc );_eaec !=nil {return nil ,_eaec ;};return _fdag .HalftoneRegionBitmap ,nil ;};type Type int ;
type Header struct{SegmentNumber uint32 ;Type Type ;RetainFlag bool ;PageAssociation int ;PageAssociationFieldSize bool ;RTSegments []*Header ;HeaderLength int64 ;SegmentDataLength uint64 ;SegmentDataStartOffset uint64 ;Reader *_af .Reader ;SegmentData Segmenter ;
RTSNumbers []int ;RetainBits []uint8 ;};func (_cc *GenericRefinementRegion )GetRegionBitmap ()(*_f .Bitmap ,error ){var _ece error ;_ac .Log .Trace ("\u005b\u0047E\u004e\u0045\u0052\u0049\u0043\u002d\u0052\u0045\u0046\u002d\u0052\u0045\u0047\u0049\u004f\u004e\u005d\u0020\u0047\u0065\u0074\u0052\u0065\u0067\u0069\u006f\u006e\u0042\u0069\u0074\u006d\u0061\u0070\u0020\u0062\u0065\u0067\u0069\u006e\u0073\u002e\u002e\u002e");
defer func (){if _ece !=nil {_ac .Log .Trace ("[\u0047\u0045\u004e\u0045\u0052\u0049\u0043\u002d\u0052E\u0046\u002d\u0052\u0045\u0047\u0049\u004fN]\u0020\u0047\u0065\u0074R\u0065\u0067\u0069\u006f\u006e\u0042\u0069\u0074\u006dap\u0020\u0066a\u0069\u006c\u0065\u0064\u002e\u0020\u0025\u0076",_ece );
}else {_ac .Log .Trace ("\u005b\u0047E\u004e\u0045\u0052\u0049\u0043\u002d\u0052\u0045\u0046\u002d\u0052\u0045\u0047\u0049\u004f\u004e\u005d\u0020\u0047\u0065\u0074\u0052\u0065\u0067\u0069\u006f\u006e\u0042\u0069\u0074\u006d\u0061\u0070\u0020\u0066\u0069\u006e\u0069\u0073\u0068\u0065\u0064\u002e");
};}();if _cc .RegionBitmap !=nil {return _cc .RegionBitmap ,nil ;};_gg :=0;if _cc .ReferenceBitmap ==nil {_cc .ReferenceBitmap ,_ece =_cc .getGrReference ();if _ece !=nil {return nil ,_ece ;};};if _cc ._ec ==nil {_cc ._ec ,_ece =_afd .New (_cc ._cf );if _ece !=nil {return nil ,_ece ;
};};if _cc ._ceb ==nil {_cc ._ceb =_afd .NewStats (8192,1);};_cc .RegionBitmap =_f .New (int (_cc .RegionInfo .BitmapWidth ),int (_cc .RegionInfo .BitmapHeight ));if _cc .TemplateID ==0{if _ece =_cc .updateOverride ();_ece !=nil {return nil ,_ece ;};};
_fe :=(_cc .RegionBitmap .Width +7)&-8;var _ed int ;if _cc .IsTPGROn {_ed =int (-_cc .ReferenceDY )*_cc .ReferenceBitmap .RowStride ;};_ef :=_ed +1;for _aea :=0;_aea < _cc .RegionBitmap .Height ;_aea ++{if _cc .IsTPGROn {_fc ,_efg :=_cc .decodeSLTP ();
if _efg !=nil {return nil ,_efg ;};_gg ^=_fc ;};if _gg ==0{_ece =_cc .decodeOptimized (_aea ,_cc .RegionBitmap .Width ,_cc .RegionBitmap .RowStride ,_cc .ReferenceBitmap .RowStride ,_fe ,_ed ,_ef );if _ece !=nil {return nil ,_ece ;};}else {_ece =_cc .decodeTypicalPredictedLine (_aea ,_cc .RegionBitmap .Width ,_cc .RegionBitmap .RowStride ,_cc .ReferenceBitmap .RowStride ,_fe ,_ed );
if _ece !=nil {return nil ,_ece ;};};};return _cc .RegionBitmap ,nil ;};type TableSegment struct{_fcae *_af .Reader ;_bbdg int32 ;_dabe int32 ;_ffdd int32 ;_ecge int32 ;_eag int32 ;};func (_fdfdg *TextRegion )parseHeader ()error {var _dabg error ;_ac .Log .Trace ("\u005b\u0054E\u0058\u0054\u0020\u0052E\u0047\u0049O\u004e\u005d\u005b\u0050\u0041\u0052\u0053\u0045-\u0048\u0045\u0041\u0044\u0045\u0052\u005d\u0020\u0062\u0065\u0067\u0069n\u0073\u002e\u002e\u002e");
defer func (){if _dabg !=nil {_ac .Log .Trace ("\u005b\u0054\u0045\u0058\u0054\u0020\u0052\u0045\u0047\u0049\u004f\u004e\u005d\u005b\u0050\u0041\u0052\u0053\u0045\u002d\u0048\u0045\u0041\u0044E\u0052\u005d\u0020\u0066\u0061i\u006c\u0065d\u002e\u0020\u0025\u0076",_dabg );
}else {_ac .Log .Trace ("\u005b\u0054E\u0058\u0054\u0020\u0052E\u0047\u0049O\u004e\u005d\u005b\u0050\u0041\u0052\u0053\u0045-\u0048\u0045\u0041\u0044\u0045\u0052\u005d\u0020\u0066\u0069\u006e\u0069s\u0068\u0065\u0064\u002e");};}();if _dabg =_fdfdg .RegionInfo .parseHeader ();
_dabg !=nil {return _dabg ;};if _dabg =_fdfdg .readRegionFlags ();_dabg !=nil {return _dabg ;};if _fdfdg .IsHuffmanEncoded {if _dabg =_fdfdg .readHuffmanFlags ();_dabg !=nil {return _dabg ;};};if _dabg =_fdfdg .readUseRefinement ();_dabg !=nil {return _dabg ;
};if _dabg =_fdfdg .readAmountOfSymbolInstances ();_dabg !=nil {return _dabg ;};if _dabg =_fdfdg .getSymbols ();_dabg !=nil {return _dabg ;};if _dabg =_fdfdg .computeSymbolCodeLength ();_dabg !=nil {return _dabg ;};if _dabg =_fdfdg .checkInput ();_dabg !=nil {return _dabg ;
};_ac .Log .Trace ("\u0025\u0073",_fdfdg .String ());return nil ;};func (_fbad *SymbolDictionary )setInSyms ()error {if _fbad .Header .RTSegments !=nil {return _fbad .retrieveImportSymbols ();};_fbad ._agc =make ([]*_f .Bitmap ,0);return nil ;};func (_faff *GenericRegion )GetRegionInfo ()*RegionSegment {return _faff .RegionSegment };
func (_gfdfc *TextRegion )readHuffmanFlags ()error {var (_cbfad int ;_dfcc uint64 ;_edaf error ;);_ ,_edaf =_gfdfc ._bbbd .ReadBit ();if _edaf !=nil {return _edaf ;};_cbfad ,_edaf =_gfdfc ._bbbd .ReadBit ();if _edaf !=nil {return _edaf ;};_gfdfc .SbHuffRSize =int8 (_cbfad );
_dfcc ,_edaf =_gfdfc ._bbbd .ReadBits (2);if _edaf !=nil {return _edaf ;};_gfdfc .SbHuffRDY =int8 (_dfcc )&0xf;_dfcc ,_edaf =_gfdfc ._bbbd .ReadBits (2);if _edaf !=nil {return _edaf ;};_gfdfc .SbHuffRDX =int8 (_dfcc )&0xf;_dfcc ,_edaf =_gfdfc ._bbbd .ReadBits (2);
if _edaf !=nil {return _edaf ;};_gfdfc .SbHuffRDHeight =int8 (_dfcc )&0xf;_dfcc ,_edaf =_gfdfc ._bbbd .ReadBits (2);if _edaf !=nil {return _edaf ;};_gfdfc .SbHuffRDWidth =int8 (_dfcc )&0xf;_dfcc ,_edaf =_gfdfc ._bbbd .ReadBits (2);if _edaf !=nil {return _edaf ;
};_gfdfc .SbHuffDT =int8 (_dfcc )&0xf;_dfcc ,_edaf =_gfdfc ._bbbd .ReadBits (2);if _edaf !=nil {return _edaf ;};_gfdfc .SbHuffDS =int8 (_dfcc )&0xf;_dfcc ,_edaf =_gfdfc ._bbbd .ReadBits (2);if _edaf !=nil {return _edaf ;};_gfdfc .SbHuffFS =int8 (_dfcc )&0xf;
return nil ;};func (_cegf *SymbolDictionary )setAtPixels ()error {if _cegf .IsHuffmanEncoded {return nil ;};_bbbac :=1;if _cegf .SdTemplate ==0{_bbbac =4;};if _ebba :=_cegf .readAtPixels (_bbbac );_ebba !=nil {return _ebba ;};return nil ;};func NewRegionSegment (r *_af .Reader )*RegionSegment {return &RegionSegment {_gdgd :r }};
func (_daab *SymbolDictionary )decodeNewSymbols (_deefd ,_gedd uint32 ,_cgbg *_f .Bitmap ,_gafa ,_caac int32 )error {if _daab ._ebgd ==nil {_daab ._ebgd =_ade (_daab ._ecdbb ,nil );if _daab ._edffg ==nil {var _adfcd error ;_daab ._edffg ,_adfcd =_afd .New (_daab ._ecdbb );
if _adfcd !=nil {return _adfcd ;};};if _daab ._gabd ==nil {_daab ._gabd =_afd .NewStats (65536,1);};};_daab ._ebgd .setParameters (_daab ._gabd ,_daab ._edffg ,_daab .SdrTemplate ,_deefd ,_gedd ,_cgbg ,_gafa ,_caac ,false ,_daab .SdrATX ,_daab .SdrATY );
return _daab .addSymbol (_daab ._ebgd );};func (_dbdg *HalftoneRegion )GetRegionInfo ()*RegionSegment {return _dbdg .RegionSegment };func (_aeb *GenericRefinementRegion )parseHeader ()(_fggg error ){_ac .Log .Trace ("\u005b\u0047\u0045\u004e\u0045\u0052\u0049\u0043\u002d\u0052\u0045\u0046\u002d\u0052\u0045\u0047\u0049\u004f\u004e\u005d\u0020\u0070\u0061\u0072s\u0069\u006e\u0067\u0020\u0048e\u0061\u0064e\u0072\u002e\u002e\u002e");
_fda :=_eg .Now ();defer func (){if _fggg ==nil {_ac .Log .Trace ("\u005b\u0047\u0045\u004e\u0045\u0052\u0049\u0043\u002d\u0052\u0045\u0046\u002d\u0052\u0045G\u0049\u004f\u004e\u005d\u0020\u0070\u0061\u0072\u0073\u0069\u006e\u0067\u0020h\u0065\u0061\u0064\u0065\u0072\u0020\u0066\u0069\u006e\u0069\u0073\u0068id\u0020\u0069\u006e\u003a\u0020\u0025\u0064\u0020\u006e\u0073",_eg .Since (_fda ).Nanoseconds ());
}else {_ac .Log .Trace ("\u005b\u0047E\u004e\u0045\u0052\u0049\u0043\u002d\u0052\u0045\u0046\u002d\u0052\u0045\u0047\u0049\u004f\u004e\u005d\u0020\u0070\u0061\u0072\u0073\u0069\u006e\u0067\u0020\u0068\u0065\u0061\u0064\u0065\u0072\u0020\u0066\u0061\u0069\u006c\u0065\u0064\u003a\u0020\u0025\u0073",_fggg );
};}();if _fggg =_aeb .RegionInfo .parseHeader ();_fggg !=nil {return _fggg ;};_ ,_fggg =_aeb ._cf .ReadBits (6);if _fggg !=nil {return _fggg ;};_aeb .IsTPGROn ,_fggg =_aeb ._cf .ReadBool ();if _fggg !=nil {return _fggg ;};var _cge int ;_cge ,_fggg =_aeb ._cf .ReadBit ();
if _fggg !=nil {return _fggg ;};_aeb .TemplateID =int8 (_cge );switch _aeb .TemplateID {case 0:_aeb .Template =_aeb ._be ;if _fggg =_aeb .readAtPixels ();_fggg !=nil {return _fggg ;};case 1:_aeb .Template =_aeb ._bb ;};return nil ;};func (_gdacb *SymbolDictionary )String ()string {_fccc :=&_a .Builder {};
_fccc .WriteString ("\n\u005b\u0053\u0059\u004dBO\u004c-\u0044\u0049\u0043\u0054\u0049O\u004e\u0041\u0052\u0059\u005d\u000a");_fccc .WriteString (_b .Sprintf ("\u0009-\u0020S\u0064\u0072\u0054\u0065\u006dp\u006c\u0061t\u0065\u0020\u0025\u0076\u000a",_gdacb .SdrTemplate ));
_fccc .WriteString (_b .Sprintf ("\u0009\u002d\u0020\u0053\u0064\u0054\u0065\u006d\u0070\u006c\u0061\u0074e\u0020\u0025\u0076\u000a",_gdacb .SdTemplate ));_fccc .WriteString (_b .Sprintf ("\u0009\u002d\u0020\u0069\u0073\u0043\u006f\u0064\u0069\u006eg\u0043\u006f\u006e\u0074\u0065\u0078\u0074R\u0065\u0074\u0061\u0069\u006e\u0065\u0064\u0020\u0025\u0076\u000a",_gdacb ._aeged ));
_fccc .WriteString (_b .Sprintf ("\u0009\u002d\u0020\u0069\u0073\u0043\u006f\u0064\u0069\u006e\u0067C\u006f\u006e\u0074\u0065\u0078\u0074\u0055\u0073\u0065\u0064 \u0025\u0076\u000a",_gdacb ._bfdb ));_fccc .WriteString (_b .Sprintf ("\u0009\u002d\u0020\u0053\u0064\u0048u\u0066\u0066\u0041\u0067\u0067\u0049\u006e\u0073\u0074\u0061\u006e\u0063\u0065S\u0065\u006c\u0065\u0063\u0074\u0069\u006fn\u0020\u0025\u0076\u000a",_gdacb .SdHuffAggInstanceSelection ));
_fccc .WriteString (_b .Sprintf ("\u0009\u002d\u0020\u0053d\u0048\u0075\u0066\u0066\u0042\u004d\u0053\u0069\u007a\u0065S\u0065l\u0065\u0063\u0074\u0069\u006f\u006e\u0020%\u0076\u000a",_gdacb .SdHuffBMSizeSelection ));_fccc .WriteString (_b .Sprintf ("\u0009\u002d\u0020\u0053\u0064\u0048u\u0066\u0066\u0044\u0065\u0063\u006f\u0064\u0065\u0057\u0069\u0064\u0074\u0068S\u0065\u006c\u0065\u0063\u0074\u0069\u006fn\u0020\u0025\u0076\u000a",_gdacb .SdHuffDecodeWidthSelection ));
_fccc .WriteString (_b .Sprintf ("\u0009\u002d\u0020Sd\u0048\u0075\u0066\u0066\u0044\u0065\u0063\u006f\u0064e\u0048e\u0069g\u0068t\u0053\u0065\u006c\u0065\u0063\u0074\u0069\u006f\u006e\u0020\u0025\u0076\u000a",_gdacb .SdHuffDecodeHeightSelection ));_fccc .WriteString (_b .Sprintf ("\u0009\u002d\u0020U\u0073\u0065\u0052\u0065f\u0069\u006e\u0065\u006d\u0065\u006e\u0074A\u0067\u0067\u0072\u0065\u0067\u0061\u0074\u0069\u006f\u006e\u0020\u0025\u0076\u000a",_gdacb .UseRefinementAggregation ));
_fccc .WriteString (_b .Sprintf ("\u0009\u002d\u0020is\u0048\u0075\u0066\u0066\u006d\u0061\u006e\u0045\u006e\u0063\u006f\u0064\u0065\u0064\u0020\u0025\u0076\u000a",_gdacb .IsHuffmanEncoded ));_fccc .WriteString (_b .Sprintf ("\u0009\u002d\u0020S\u0064\u0041\u0054\u0058\u0020\u0025\u0076\u000a",_gdacb .SdATX ));
_fccc .WriteString (_b .Sprintf ("\u0009\u002d\u0020S\u0064\u0041\u0054\u0059\u0020\u0025\u0076\u000a",_gdacb .SdATY ));_fccc .WriteString (_b .Sprintf ("\u0009\u002d\u0020\u0053\u0064\u0072\u0041\u0054\u0058\u0020\u0025\u0076\u000a",_gdacb .SdrATX ));
_fccc .WriteString (_b .Sprintf ("\u0009\u002d\u0020\u0053\u0064\u0072\u0041\u0054\u0059\u0020\u0025\u0076\u000a",_gdacb .SdrATY ));_fccc .WriteString (_b .Sprintf ("\u0009\u002d\u0020\u004e\u0075\u006d\u0062\u0065\u0072\u004ff\u0045\u0078\u0070\u006f\u0072\u0074\u0065d\u0053\u0079\u006d\u0062\u006f\u006c\u0073\u0020\u0025\u0076\u000a",_gdacb .NumberOfExportedSymbols ));
_fccc .WriteString (_b .Sprintf ("\u0009-\u0020\u004e\u0075\u006db\u0065\u0072\u004f\u0066\u004ee\u0077S\u0079m\u0062\u006f\u006c\u0073\u0020\u0025\u0076\n",_gdacb .NumberOfNewSymbols ));_fccc .WriteString (_b .Sprintf ("\u0009\u002d\u0020\u006e\u0075\u006d\u0062\u0065\u0072\u004ff\u0049\u006d\u0070\u006f\u0072\u0074\u0065d\u0053\u0079\u006d\u0062\u006f\u006c\u0073\u0020\u0025\u0076\u000a",_gdacb ._gagg ));
_fccc .WriteString (_b .Sprintf ("\u0009\u002d \u006e\u0075\u006d\u0062\u0065\u0072\u004f\u0066\u0044\u0065\u0063\u006f\u0064\u0065\u0064\u0053\u0079\u006d\u0062\u006f\u006c\u0073 %\u0076\u000a",_gdacb ._aagc ));return _fccc .String ();};func (_fceg *SymbolDictionary )Init (h *Header ,r *_af .Reader )error {_fceg .Header =h ;
_fceg ._ecdbb =r ;return _fceg .parseHeader ();};func (_cecb *HalftoneRegion )shiftAndFill (_abcb int )int {_abcb >>=8;if _abcb < 0{_dfb :=int (_g .Log (float64 (_eecb (_abcb )))/_g .Log (2));_ccaa :=31-_dfb ;for _gcac :=1;_gcac < _ccaa ;_gcac ++{_abcb |=1<<uint (31-_gcac );
};};return _abcb ;};func (_cgc *EndOfStripe )parseHeader ()error {_acc ,_dg :=_cgc ._cg .ReadBits (32);if _dg !=nil {return _dg ;};_cgc ._bg =int (_acc &_g .MaxInt32 );return nil ;};func (_fegc *TextRegion )decodeIb (_fagegc ,_ecfcd int64 )(*_f .Bitmap ,error ){const _cecc ="\u0064\u0065\u0063\u006f\u0064\u0065\u0049\u0062";
var (_cafc error ;_afcf *_f .Bitmap ;);if _fagegc ==0{if int (_ecfcd )> len (_fegc .Symbols )-1{return nil ,_gf .Error (_cecc ,"\u0064\u0065\u0063\u006f\u0064\u0069\u006e\u0067\u0020\u0049\u0042\u0020\u0062\u0069\u0074\u006d\u0061\u0070\u002e\u0020\u0069\u006e\u0064\u0065x\u0020\u006f\u0075\u0074\u0020o\u0066\u0020r\u0061\u006e\u0067\u0065");
};return _fegc .Symbols [int (_ecfcd )],nil ;};var _cfgd ,_agdba ,_fcbea ,_gacd int64 ;_cfgd ,_cafc =_fegc .decodeRdw ();if _cafc !=nil {return nil ,_gf .Wrap (_cafc ,_cecc ,"");};_agdba ,_cafc =_fegc .decodeRdh ();if _cafc !=nil {return nil ,_gf .Wrap (_cafc ,_cecc ,"");
};_fcbea ,_cafc =_fegc .decodeRdx ();if _cafc !=nil {return nil ,_gf .Wrap (_cafc ,_cecc ,"");};_gacd ,_cafc =_fegc .decodeRdy ();if _cafc !=nil {return nil ,_gf .Wrap (_cafc ,_cecc ,"");};if _fegc .IsHuffmanEncoded {if _ ,_cafc =_fegc .decodeSymInRefSize ();
_cafc !=nil {return nil ,_gf .Wrap (_cafc ,_cecc ,"");};_fegc ._bbbd .Align ();};_adbe :=_fegc .Symbols [_ecfcd ];_eebd :=uint32 (_adbe .Width );_adaaf :=uint32 (_adbe .Height );_gdgag :=int32 (uint32 (_cfgd )>>1)+int32 (_fcbea );_eafa :=int32 (uint32 (_agdba )>>1)+int32 (_gacd );
if _fegc ._gced ==nil {_fegc ._gced =_ade (_fegc ._bbbd ,nil );};_fegc ._gced .setParameters (_fegc ._ceed ,_fegc ._dbf ,_fegc .SbrTemplate ,_eebd +uint32 (_cfgd ),_adaaf +uint32 (_agdba ),_adbe ,_gdgag ,_eafa ,false ,_fegc .SbrATX ,_fegc .SbrATY );_afcf ,_cafc =_fegc ._gced .GetRegionBitmap ();
if _cafc !=nil {return nil ,_gf .Wrap (_cafc ,_cecc ,"\u0067\u0072\u0066");};if _fegc .IsHuffmanEncoded {_fegc ._bbbd .Align ();};return _afcf ,nil ;};func (_agba *Header )GetSegmentData ()(Segmenter ,error ){var _adbc Segmenter ;if _agba .SegmentData !=nil {_adbc =_agba .SegmentData ;
};if _adbc ==nil {_fbdg ,_ggd :=_dfba [_agba .Type ];if !_ggd {return nil ,_b .Errorf ("\u0074\u0079\u0070\u0065\u003a\u0020\u0025\u0073\u002f\u0020\u0025\u0064\u0020\u0063\u0072e\u0061t\u006f\u0072\u0020\u006e\u006f\u0074\u0020\u0066\u006f\u0075\u006e\u0064\u002e\u0020",_agba .Type ,_agba .Type );
};_adbc =_fbdg ();_ac .Log .Trace ("\u005b\u0053E\u0047\u004d\u0045\u004e\u0054-\u0048\u0045\u0041\u0044\u0045R\u005d\u005b\u0023\u0025\u0064\u005d\u0020\u0047\u0065\u0074\u0053\u0065\u0067\u006d\u0065\u006e\u0074\u0044\u0061\u0074\u0061\u0020\u0061\u0074\u0020\u004f\u0066\u0066\u0073\u0065\u0074\u003a\u0020\u0025\u0030\u0034\u0058",_agba .SegmentNumber ,_agba .SegmentDataStartOffset );
_ebcad ,_geaa :=_agba .subInputReader ();if _geaa !=nil {return nil ,_geaa ;};if _fdcc :=_adbc .Init (_agba ,_ebcad );_fdcc !=nil {_ac .Log .Debug ("\u0049\u006e\u0069\u0074 \u0066\u0061\u0069\u006c\u0065\u0064\u003a\u0020\u0025\u0076 \u0066o\u0072\u0020\u0074\u0079\u0070\u0065\u003a \u0025\u0054",_fdcc ,_adbc );
return nil ,_fdcc ;};_agba .SegmentData =_adbc ;};return _adbc ,nil ;};func (_ebaf *PageInformationSegment )CombinationOperatorOverrideAllowed ()bool {return _ebaf ._affa };func (_cged *PatternDictionary )GetDictionary ()([]*_f .Bitmap ,error ){if _cged .Patterns !=nil {return _cged .Patterns ,nil ;
};if !_cged .IsMMREncoded {_cged .setGbAtPixels ();};_bggd :=NewGenericRegion (_cged ._cffg );_bggd .setParametersMMR (_cged .IsMMREncoded ,_cged .DataOffset ,_cged .DataLength ,uint32 (_cged .HdpHeight ),(_cged .GrayMax +1)*uint32 (_cged .HdpWidth ),_cged .HDTemplate ,false ,false ,_cged .GBAtX ,_cged .GBAtY );
_fdfaa ,_ebfb :=_bggd .GetRegionBitmap ();if _ebfb !=nil {return nil ,_ebfb ;};if _ebfb =_cged .extractPatterns (_fdfaa );_ebfb !=nil {return nil ,_ebfb ;};return _cged .Patterns ,nil ;};type RegionSegment struct{_gdgd *_af .Reader ;BitmapWidth uint32 ;
BitmapHeight uint32 ;XLocation uint32 ;YLocation uint32 ;CombinaionOperator _f .CombinationOperator ;};func (_dffg *SymbolDictionary )checkInput ()error {if _dffg .SdHuffDecodeHeightSelection ==2{_ac .Log .Debug ("\u0053\u0079\u006d\u0062\u006fl\u0020\u0044\u0069\u0063\u0074i\u006fn\u0061\u0072\u0079\u0020\u0044\u0065\u0063\u006f\u0064\u0065\u0020\u0048\u0065\u0069\u0067\u0068\u0074\u0020\u0053e\u006c\u0065\u0063\u0074\u0069\u006f\u006e\u003a\u0020\u0025\u0064\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u006e\u006f\u0074\u0020\u0070\u0065r\u006d\u0069\u0074\u0074\u0065\u0064",_dffg .SdHuffDecodeHeightSelection );
};if _dffg .SdHuffDecodeWidthSelection ==2{_ac .Log .Debug ("\u0053\u0079\u006d\u0062\u006f\u006c\u0020\u0044\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079 \u0044\u0065\u0063\u006f\u0064\u0065\u0020\u0057\u0069\u0064t\u0068\u0020\u0053\u0065\u006c\u0065\u0063\u0074\u0069\u006f\u006e\u003a\u0020\u0025\u0064\u0020\u0076\u0061l\u0075\u0065\u0020\u006e\u006f\u0074 \u0070\u0065r\u006d\u0069t\u0074e\u0064",_dffg .SdHuffDecodeWidthSelection );
};if _dffg .IsHuffmanEncoded {if _dffg .SdTemplate !=0{_ac .Log .Debug ("\u0053\u0044T\u0065\u006d\u0070\u006c\u0061\u0074\u0065\u0020\u003d\u0020\u0025\u0064\u0020\u0028\u0073\u0068\u006f\u0075\u006c\u0064\u0020\u0062e \u0030\u0029",_dffg .SdTemplate );
};if !_dffg .UseRefinementAggregation {if !_dffg .UseRefinementAggregation {if _dffg ._aeged {_ac .Log .Debug ("\u0049\u0073\u0043\u006f\u0064\u0069\u006e\u0067C\u006f\u006e\u0074ex\u0074\u0052\u0065\u0074\u0061\u0069n\u0065\u0064\u0020\u003d\u0020\u0074\u0072\u0075\u0065\u0020\u0028\u0073\u0068\u006f\u0075l\u0064\u0020\u0062\u0065\u0020\u0066\u0061\u006cs\u0065\u0029");
_dffg ._aeged =false ;};if _dffg ._bfdb {_ac .Log .Debug ("\u0069s\u0043\u006fd\u0069\u006e\u0067\u0043o\u006e\u0074\u0065x\u0074\u0055\u0073\u0065\u0064\u0020\u003d\u0020\u0074ru\u0065\u0020\u0028s\u0068\u006fu\u006c\u0064\u0020\u0062\u0065\u0020f\u0061\u006cs\u0065\u0029");
_dffg ._bfdb =false ;};};};}else {if _dffg .SdHuffBMSizeSelection !=0{_ac .Log .Debug ("\u0053\u0064\u0048\u0075\u0066\u0066B\u004d\u0053\u0069\u007a\u0065\u0053\u0065\u006c\u0065\u0063\u0074\u0069\u006fn\u0020\u0073\u0068\u006f\u0075\u006c\u0064 \u0062\u0065\u0020\u0030");
_dffg .SdHuffBMSizeSelection =0;};if _dffg .SdHuffDecodeWidthSelection !=0{_ac .Log .Debug ("\u0053\u0064\u0048\u0075\u0066\u0066\u0044\u0065\u0063\u006f\u0064\u0065\u0057\u0069\u0064\u0074\u0068\u0053\u0065\u006c\u0065\u0063\u0074\u0069o\u006e\u0020\u0073\u0068\u006fu\u006c\u0064 \u0062\u0065\u0020\u0030");
_dffg .SdHuffDecodeWidthSelection =0;};if _dffg .SdHuffDecodeHeightSelection !=0{_ac .Log .Debug ("\u0053\u0064\u0048\u0075\u0066\u0066\u0044\u0065\u0063\u006f\u0064\u0065\u0048e\u0069\u0067\u0068\u0074\u0053\u0065l\u0065\u0063\u0074\u0069\u006f\u006e\u0020\u0073\u0068\u006f\u0075\u006c\u0064 \u0062\u0065\u0020\u0030");
_dffg .SdHuffDecodeHeightSelection =0;};};if !_dffg .UseRefinementAggregation {if _dffg .SdrTemplate !=0{_ac .Log .Debug ("\u0053\u0044\u0052\u0054\u0065\u006d\u0070\u006c\u0061\u0074e\u0020\u003d\u0020\u0025\u0064\u0020\u0028s\u0068\u006f\u0075\u006c\u0064\u0020\u0062\u0065\u0020\u0030\u0029",_dffg .SdrTemplate );
_dffg .SdrTemplate =0;};};if !_dffg .IsHuffmanEncoded ||!_dffg .UseRefinementAggregation {if _dffg .SdHuffAggInstanceSelection {_ac .Log .Debug ("\u0053d\u0048\u0075f\u0066\u0041\u0067g\u0049\u006e\u0073\u0074\u0061\u006e\u0063e\u0053\u0065\u006c\u0065\u0063\u0074i\u006f\u006e\u0020\u003d\u0020\u0025\u0064\u0020\u0028\u0073\u0068o\u0075\u006c\u0064\u0020\u0062\u0065\u0020\u0030\u0029",_dffg .SdHuffAggInstanceSelection );
};};return nil ;};func (_dbec *PageInformationSegment )readContainsRefinement ()error {_gbbb ,_caab :=_dbec ._fccb .ReadBit ();if _caab !=nil {return _caab ;};if _gbbb ==1{_dbec ._fdfa =true ;};return nil ;};func (_afa *GenericRefinementRegion )readAtPixels ()error {_afa .GrAtX =make ([]int8 ,2);
_afa .GrAtY =make ([]int8 ,2);_aec ,_cbb :=_afa ._cf .ReadByte ();if _cbb !=nil {return _cbb ;};_afa .GrAtX [0]=int8 (_aec );_aec ,_cbb =_afa ._cf .ReadByte ();if _cbb !=nil {return _cbb ;};_afa .GrAtY [0]=int8 (_aec );_aec ,_cbb =_afa ._cf .ReadByte ();
if _cbb !=nil {return _cbb ;};_afa .GrAtX [1]=int8 (_aec );_aec ,_cbb =_afa ._cf .ReadByte ();if _cbb !=nil {return _cbb ;};_afa .GrAtY [1]=int8 (_aec );return nil ;};func (_bfcd *PageInformationSegment )encodeStripingInformation (_fdae _af .BinaryWriter )(_ccab int ,_cdgf error ){const _adg ="\u0065n\u0063\u006f\u0064\u0065S\u0074\u0072\u0069\u0070\u0069n\u0067I\u006ef\u006f\u0072\u006d\u0061\u0074\u0069\u006fn";
if !_bfcd .IsStripe {if _ccab ,_cdgf =_fdae .Write ([]byte {0x00,0x00});_cdgf !=nil {return 0,_gf .Wrap (_cdgf ,_adg ,"n\u006f\u0020\u0073\u0074\u0072\u0069\u0070\u0069\u006e\u0067");};return _ccab ,nil ;};_cgaa :=make ([]byte ,2);_da .BigEndian .PutUint16 (_cgaa ,_bfcd .MaxStripeSize |1<<15);
if _ccab ,_cdgf =_fdae .Write (_cgaa );_cdgf !=nil {return 0,_gf .Wrapf (_cdgf ,_adg ,"\u0073\u0074\u0072i\u0070\u0069\u006e\u0067\u003a\u0020\u0025\u0064",_bfcd .MaxStripeSize );};return _ccab ,nil ;};func (_acda *SymbolDictionary )encodeRefinementATFlags (_fbgc _af .BinaryWriter )(_cfee int ,_aaeb error ){const _cggg ="\u0065\u006e\u0063od\u0065\u0052\u0065\u0066\u0069\u006e\u0065\u006d\u0065\u006e\u0074\u0041\u0054\u0046\u006c\u0061\u0067\u0073";
if !_acda .UseRefinementAggregation ||_acda .SdrTemplate !=0{return 0,nil ;};for _eedd :=0;_eedd < 2;_eedd ++{if _aaeb =_fbgc .WriteByte (byte (_acda .SdrATX [_eedd ]));_aaeb !=nil {return _cfee ,_gf .Wrapf (_aaeb ,_cggg ,"\u0053\u0064\u0072\u0041\u0054\u0058\u005b\u0025\u0064\u005d",_eedd );
};_cfee ++;if _aaeb =_fbgc .WriteByte (byte (_acda .SdrATY [_eedd ]));_aaeb !=nil {return _cfee ,_gf .Wrapf (_aaeb ,_cggg ,"\u0053\u0064\u0072\u0041\u0054\u0059\u005b\u0025\u0064\u005d",_eedd );};_cfee ++;};return _cfee ,nil ;};func (_ccaaf *SymbolDictionary )InitEncode (symbols *_f .Bitmaps ,symbolList []int ,symbolMap map[int ]int ,unborderSymbols bool )error {const _bfbg ="S\u0079\u006d\u0062\u006f\u006c\u0044i\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u002eI\u006e\u0069\u0074E\u006ec\u006f\u0064\u0065";
_ccaaf .SdATX =[]int8 {3,-3,2,-2};_ccaaf .SdATY =[]int8 {-1,-1,-2,-2};_ccaaf ._ebcd =symbols ;_ccaaf ._cag =make ([]int ,len (symbolList ));copy (_ccaaf ._cag ,symbolList );if len (_ccaaf ._cag )!=_ccaaf ._ebcd .Size (){return _gf .Error (_bfbg ,"s\u0079\u006d\u0062\u006f\u006c\u0073\u0020\u0061\u006e\u0064\u0020\u0073\u0079\u006d\u0062\u006f\u006c\u004ci\u0073\u0074\u0020\u006f\u0066\u0020\u0064\u0069\u0066\u0066er\u0065\u006e\u0074 \u0073i\u007a\u0065");
};_ccaaf .NumberOfNewSymbols =uint32 (symbols .Size ());_ccaaf .NumberOfExportedSymbols =uint32 (symbols .Size ());_ccaaf ._gbfb =symbolMap ;_ccaaf ._bdged =unborderSymbols ;return nil ;};func (_bdfe *SymbolDictionary )getSymbol (_cdcf int )(*_f .Bitmap ,error ){const _ccdg ="\u0067e\u0074\u0053\u0079\u006d\u0062\u006fl";
_baacf ,_bdbe :=_bdfe ._ebcd .GetBitmap (_bdfe ._cag [_cdcf ]);if _bdbe !=nil {return nil ,_gf .Wrap (_bdbe ,_ccdg ,"\u0063\u0061n\u0027\u0074\u0020g\u0065\u0074\u0020\u0073\u0079\u006d\u0062\u006f\u006c");};return _baacf ,nil ;};func (_efef *TextRegion )readAmountOfSymbolInstances ()error {_aaaf ,_gfbg :=_efef ._bbbd .ReadBits (32);
if _gfbg !=nil {return _gfbg ;};_efef .NumberOfSymbolInstances =uint32 (_aaaf &_g .MaxUint32 );_ffge :=_efef .RegionInfo .BitmapWidth *_efef .RegionInfo .BitmapHeight ;if _ffge < _efef .NumberOfSymbolInstances {_ac .Log .Debug ("\u004c\u0069\u006d\u0069t\u0069\u006e\u0067\u0020t\u0068\u0065\u0020n\u0075\u006d\u0062\u0065\u0072\u0020o\u0066\u0020d\u0065\u0063\u006f\u0064e\u0064\u0020\u0073\u0079m\u0062\u006f\u006c\u0020\u0069n\u0073\u0074\u0061\u006e\u0063\u0065\u0073 \u0074\u006f\u0020\u006f\u006ee\u0020\u0070\u0065\u0072\u0020\u0070\u0069\u0078\u0065l\u0020\u0028\u0020\u0025\u0064\u0020\u0069\u006e\u0073\u0074\u0065\u0061\u0064\u0020\u006f\u0066\u0020\u0025\u0064\u0029",_ffge ,_efef .NumberOfSymbolInstances );
_efef .NumberOfSymbolInstances =_ffge ;};return nil ;};func (_geb *GenericRefinementRegion )GetRegionInfo ()*RegionSegment {return _geb .RegionInfo };func (_gded *SymbolDictionary )decodeHeightClassBitmap (_bagb *_f .Bitmap ,_bdcb int64 ,_bcca int ,_gebe []int )error {for _ecdfe :=_bdcb ;
_ecdfe < int64 (_gded ._aagc );_ecdfe ++{var _fbgb int ;for _bagg :=_bdcb ;_bagg <=_ecdfe -1;_bagg ++{_fbgb +=_gebe [_bagg ];};_caaa :=_e .Rect (_fbgb ,0,_fbgb +_gebe [_ecdfe ],_bcca );_cbdg ,_ddfc :=_f .Extract (_caaa ,_bagb );if _ddfc !=nil {return _ddfc ;
};_gded ._fgga [_ecdfe ]=_cbdg ;_gded ._gggg =append (_gded ._gggg ,_cbdg );};return nil ;};type EndOfStripe struct{_cg *_af .Reader ;_bg int ;};func (_egb *GenericRegion )updateOverrideFlags ()error {const _edef ="\u0075\u0070\u0064\u0061te\u004f\u0076\u0065\u0072\u0072\u0069\u0064\u0065\u0046\u006c\u0061\u0067\u0073";
if _egb .GBAtX ==nil ||_egb .GBAtY ==nil {return nil ;};if len (_egb .GBAtX )!=len (_egb .GBAtY ){return _gf .Errorf (_edef ,"i\u006eco\u0073i\u0073t\u0065\u006e\u0074\u0020\u0041T\u0020\u0070\u0069x\u0065\u006c\u002e\u0020\u0041m\u006f\u0075\u006et\u0020\u006f\u0066\u0020\u0027\u0078\u0027\u0020\u0070\u0069\u0078e\u006c\u0073\u003a %d\u002c\u0020\u0041\u006d\u006f\u0075n\u0074\u0020\u006f\u0066\u0020\u0027\u0079\u0027\u0020\u0070\u0069\u0078e\u006cs\u003a\u0020\u0025\u0064",len (_egb .GBAtX ),len (_egb .GBAtY ));
};_egb .GBAtOverride =make ([]bool ,len (_egb .GBAtX ));switch _egb .GBTemplate {case 0:if !_egb .UseExtTemplates {if _egb .GBAtX [0]!=3||_egb .GBAtY [0]!=-1{_egb .setOverrideFlag (0);};if _egb .GBAtX [1]!=-3||_egb .GBAtY [1]!=-1{_egb .setOverrideFlag (1);
};if _egb .GBAtX [2]!=2||_egb .GBAtY [2]!=-2{_egb .setOverrideFlag (2);};if _egb .GBAtX [3]!=-2||_egb .GBAtY [3]!=-2{_egb .setOverrideFlag (3);};}else {if _egb .GBAtX [0]!=-2||_egb .GBAtY [0]!=0{_egb .setOverrideFlag (0);};if _egb .GBAtX [1]!=0||_egb .GBAtY [1]!=-2{_egb .setOverrideFlag (1);
};if _egb .GBAtX [2]!=-2||_egb .GBAtY [2]!=-1{_egb .setOverrideFlag (2);};if _egb .GBAtX [3]!=-1||_egb .GBAtY [3]!=-2{_egb .setOverrideFlag (3);};if _egb .GBAtX [4]!=1||_egb .GBAtY [4]!=-2{_egb .setOverrideFlag (4);};if _egb .GBAtX [5]!=2||_egb .GBAtY [5]!=-1{_egb .setOverrideFlag (5);
};if _egb .GBAtX [6]!=-3||_egb .GBAtY [6]!=0{_egb .setOverrideFlag (6);};if _egb .GBAtX [7]!=-4||_egb .GBAtY [7]!=0{_egb .setOverrideFlag (7);};if _egb .GBAtX [8]!=2||_egb .GBAtY [8]!=-2{_egb .setOverrideFlag (8);};if _egb .GBAtX [9]!=3||_egb .GBAtY [9]!=-1{_egb .setOverrideFlag (9);
};if _egb .GBAtX [10]!=-2||_egb .GBAtY [10]!=-2{_egb .setOverrideFlag (10);};if _egb .GBAtX [11]!=-3||_egb .GBAtY [11]!=-1{_egb .setOverrideFlag (11);};};case 1:if _egb .GBAtX [0]!=3||_egb .GBAtY [0]!=-1{_egb .setOverrideFlag (0);};case 2:if _egb .GBAtX [0]!=2||_egb .GBAtY [0]!=-1{_egb .setOverrideFlag (0);
};case 3:if _egb .GBAtX [0]!=2||_egb .GBAtY [0]!=-1{_egb .setOverrideFlag (0);};};return nil ;};func (_cbgd *TextRegion )decodeDfs ()(int64 ,error ){if _cbgd .IsHuffmanEncoded {if _cbgd .SbHuffFS ==3{if _cbgd ._bdbb ==nil {var _dabb error ;_cbgd ._bdbb ,_dabb =_cbgd .getUserTable (0);
if _dabb !=nil {return 0,_dabb ;};};return _cbgd ._bdbb .Decode (_cbgd ._bbbd );};_gecd ,_eccg :=_eb .GetStandardTable (6+int (_cbgd .SbHuffFS ));if _eccg !=nil {return 0,_eccg ;};return _gecd .Decode (_cbgd ._bbbd );};_efeb ,_bcda :=_cbgd ._dbf .DecodeInt (_cbgd ._deggc );
if _bcda !=nil {return 0,_bcda ;};return int64 (_efeb ),nil ;};func (_acgc *TableSegment )HtPS ()int32 {return _acgc ._dabe };func (_gfdcg *PatternDictionary )readGrayMax ()error {_abddg ,_afe :=_gfdcg ._cffg .ReadBits (32);if _afe !=nil {return _afe ;
};_gfdcg .GrayMax =uint32 (_abddg &_g .MaxUint32 );return nil ;};type template0 struct{};func (_edgf *TextRegion )Init (header *Header ,r *_af .Reader )error {_edgf .Header =header ;_edgf ._bbbd =r ;_edgf .RegionInfo =NewRegionSegment (_edgf ._bbbd );return _edgf .parseHeader ();
};func (_adacg *SymbolDictionary )decodeHeightClassDeltaHeightWithHuffman ()(int64 ,error ){switch _adacg .SdHuffDecodeHeightSelection {case 0:_ecaa ,_gdcf :=_eb .GetStandardTable (4);if _gdcf !=nil {return 0,_gdcf ;};return _ecaa .Decode (_adacg ._ecdbb );
case 1:_gcee ,_dbab :=_eb .GetStandardTable (5);if _dbab !=nil {return 0,_dbab ;};return _gcee .Decode (_adacg ._ecdbb );case 3:if _adacg ._gbeb ==nil {_bbg ,_deagc :=_eb .GetStandardTable (0);if _deagc !=nil {return 0,_deagc ;};_adacg ._gbeb =_bbg ;};
return _adacg ._gbeb .Decode (_adacg ._ecdbb );};return 0,nil ;};func (_aeeff *SymbolDictionary )parseHeader ()(_aeea error ){_ac .Log .Trace ("\u005b\u0053\u0059\u004d\u0042\u004f\u004c \u0044\u0049\u0043T\u0049\u004f\u004e\u0041R\u0059\u005d\u005b\u0050\u0041\u0052\u0053\u0045\u002d\u0048\u0045\u0041\u0044\u0045\u0052\u005d\u0020\u0062\u0065\u0067\u0069\u006e\u0073\u002e\u002e\u002e");
defer func (){if _aeea !=nil {_ac .Log .Trace ("\u005bS\u0059\u004dB\u004f\u004c\u0020\u0044I\u0043\u0054\u0049O\u004e\u0041\u0052\u0059\u005d\u005b\u0050\u0041\u0052SE\u002d\u0048\u0045A\u0044\u0045R\u005d\u0020\u0066\u0061\u0069\u006ce\u0064\u002e \u0025\u0076",_aeea );
}else {_ac .Log .Trace ("\u005b\u0053\u0059\u004d\u0042\u004f\u004c \u0044\u0049\u0043T\u0049\u004f\u004e\u0041R\u0059\u005d\u005b\u0050\u0041\u0052\u0053\u0045\u002d\u0048\u0045\u0041\u0044\u0045\u0052\u005d\u0020\u0066\u0069\u006e\u0069\u0073\u0068\u0065\u0064\u002e");
};}();if _aeea =_aeeff .readRegionFlags ();_aeea !=nil {return _aeea ;};if _aeea =_aeeff .setAtPixels ();_aeea !=nil {return _aeea ;};if _aeea =_aeeff .setRefinementAtPixels ();_aeea !=nil {return _aeea ;};if _aeea =_aeeff .readNumberOfExportedSymbols ();
_aeea !=nil {return _aeea ;};if _aeea =_aeeff .readNumberOfNewSymbols ();_aeea !=nil {return _aeea ;};if _aeea =_aeeff .setInSyms ();_aeea !=nil {return _aeea ;};if _aeeff ._bfdb {_ecca :=_aeeff .Header .RTSegments ;for _bbde :=len (_ecca )-1;_bbde >=0;
_bbde --{if _ecca [_bbde ].Type ==0{_ggfc ,_gadf :=_ecca [_bbde ].SegmentData .(*SymbolDictionary );if !_gadf {_aeea =_b .Errorf ("\u0072\u0065\u006c\u0061\u0074\u0065\u0064\u0020\u0053\u0065\u0067\u006d\u0065\u006e\u0074:\u0020\u0025\u0076\u0020\u0069\u0073\u0020\u006e\u006f\u0074\u0020\u0061\u0020S\u0079\u006d\u0062\u006f\u006c\u0020\u0044\u0069\u0063\u0074\u0069\u006fna\u0072\u0079\u0020\u0053\u0065\u0067\u006d\u0065\u006e\u0074",_ecca [_bbde ]);
return _aeea ;};if _ggfc ._bfdb {_aeeff .setRetainedCodingContexts (_ggfc );};break ;};};};if _aeea =_aeeff .checkInput ();_aeea !=nil {return _aeea ;};return nil ;};var (_dbcd Segmenter ;_dfba =map[Type ]func ()Segmenter {TSymbolDictionary :func ()Segmenter {return &SymbolDictionary {}},TIntermediateTextRegion :func ()Segmenter {return &TextRegion {}},TImmediateTextRegion :func ()Segmenter {return &TextRegion {}},TImmediateLosslessTextRegion :func ()Segmenter {return &TextRegion {}},TPatternDictionary :func ()Segmenter {return &PatternDictionary {}},TIntermediateHalftoneRegion :func ()Segmenter {return &HalftoneRegion {}},TImmediateHalftoneRegion :func ()Segmenter {return &HalftoneRegion {}},TImmediateLosslessHalftoneRegion :func ()Segmenter {return &HalftoneRegion {}},TIntermediateGenericRegion :func ()Segmenter {return &GenericRegion {}},TImmediateGenericRegion :func ()Segmenter {return &GenericRegion {}},TImmediateLosslessGenericRegion :func ()Segmenter {return &GenericRegion {}},TIntermediateGenericRefinementRegion :func ()Segmenter {return &GenericRefinementRegion {}},TImmediateGenericRefinementRegion :func ()Segmenter {return &GenericRefinementRegion {}},TImmediateLosslessGenericRefinementRegion :func ()Segmenter {return &GenericRefinementRegion {}},TPageInformation :func ()Segmenter {return &PageInformationSegment {}},TEndOfPage :func ()Segmenter {return _dbcd },TEndOfStrip :func ()Segmenter {return &EndOfStripe {}},TEndOfFile :func ()Segmenter {return _dbcd },TProfiles :func ()Segmenter {return _dbcd },TTables :func ()Segmenter {return &TableSegment {}},TExtension :func ()Segmenter {return _dbcd },TBitmap :func ()Segmenter {return _dbcd }};
);func (_agfc *PageInformationSegment )Size ()int {return 19};func (_cbg *template0 )form (_eacec ,_ca ,_dgf ,_gbde ,_ced int16 )int16 {return (_eacec <<10)|(_ca <<7)|(_dgf <<4)|(_gbde <<1)|_ced ;};func (_bbca *GenericRegion )setOverrideFlag (_gadg int ){_bbca .GBAtOverride [_gadg ]=true ;
_bbca ._bgda =true ;};func (_eceb *GenericRefinementRegion )getPixel (_fgd *_f .Bitmap ,_bbb ,_fad int )int {if _bbb < 0||_bbb >=_fgd .Width {return 0;};if _fad < 0||_fad >=_fgd .Height {return 0;};if _fgd .GetPixel (_bbb ,_fad ){return 1;};return 0;};
func (_bdba *GenericRegion )Size ()int {return _bdba .RegionSegment .Size ()+1+2*len (_bdba .GBAtX )};func (_cefbc *RegionSegment )parseHeader ()error {const _fac ="p\u0061\u0072\u0073\u0065\u0048\u0065\u0061\u0064\u0065\u0072";_ac .Log .Trace ("\u005b\u0052\u0045\u0047I\u004f\u004e\u005d\u005b\u0050\u0041\u0052\u0053\u0045\u002dH\u0045A\u0044\u0045\u0052\u005d\u0020\u0042\u0065g\u0069\u006e");
defer func (){_ac .Log .Trace ("\u005b\u0052\u0045G\u0049\u004f\u004e\u005d[\u0050\u0041\u0052\u0053\u0045\u002d\u0048E\u0041\u0044\u0045\u0052\u005d\u0020\u0046\u0069\u006e\u0069\u0073\u0068\u0065\u0064");}();_defc ,_ggag :=_cefbc ._gdgd .ReadBits (32);
if _ggag !=nil {return _gf .Wrap (_ggag ,_fac ,"\u0077\u0069\u0064t\u0068");};_cefbc .BitmapWidth =uint32 (_defc &_g .MaxUint32 );_defc ,_ggag =_cefbc ._gdgd .ReadBits (32);if _ggag !=nil {return _gf .Wrap (_ggag ,_fac ,"\u0068\u0065\u0069\u0067\u0068\u0074");
};_cefbc .BitmapHeight =uint32 (_defc &_g .MaxUint32 );_defc ,_ggag =_cefbc ._gdgd .ReadBits (32);if _ggag !=nil {return _gf .Wrap (_ggag ,_fac ,"\u0078\u0020\u006c\u006f\u0063\u0061\u0074\u0069\u006f\u006e");};_cefbc .XLocation =uint32 (_defc &_g .MaxUint32 );
_defc ,_ggag =_cefbc ._gdgd .ReadBits (32);if _ggag !=nil {return _gf .Wrap (_ggag ,_fac ,"\u0079\u0020\u006c\u006f\u0063\u0061\u0074\u0069\u006f\u006e");};_cefbc .YLocation =uint32 (_defc &_g .MaxUint32 );if _ ,_ggag =_cefbc ._gdgd .ReadBits (5);_ggag !=nil {return _gf .Wrap (_ggag ,_fac ,"\u0064i\u0072\u0079\u0020\u0072\u0065\u0061d");
};if _ggag =_cefbc .readCombinationOperator ();_ggag !=nil {return _gf .Wrap (_ggag ,_fac ,"c\u006fm\u0062\u0069\u006e\u0061\u0074\u0069\u006f\u006e \u006f\u0070\u0065\u0072at\u006f\u0072");};return nil ;};func (_edcc *TextRegion )decodeID ()(int64 ,error ){if _edcc .IsHuffmanEncoded {if _edcc ._bdea ==nil {_aecc ,_bgcb :=_edcc ._bbbd .ReadBits (byte (_edcc ._fdge ));
return int64 (_aecc ),_bgcb ;};return _edcc ._bdea .Decode (_edcc ._bbbd );};return _edcc ._dbf .DecodeIAID (uint64 (_edcc ._fdge ),_edcc ._bea );};func (_ccac *GenericRegion )decodeLine (_dge ,_ccd ,_abd int )error {const _dec ="\u0064\u0065\u0063\u006f\u0064\u0065\u004c\u0069\u006e\u0065";
_fdc :=_ccac .Bitmap .GetByteIndex (0,_dge );_abde :=_fdc -_ccac .Bitmap .RowStride ;switch _ccac .GBTemplate {case 0:if !_ccac .UseExtTemplates {return _ccac .decodeTemplate0a (_dge ,_ccd ,_abd ,_fdc ,_abde );};return _ccac .decodeTemplate0b (_dge ,_ccd ,_abd ,_fdc ,_abde );
case 1:return _ccac .decodeTemplate1 (_dge ,_ccd ,_abd ,_fdc ,_abde );case 2:return _ccac .decodeTemplate2 (_dge ,_ccd ,_abd ,_fdc ,_abde );case 3:return _ccac .decodeTemplate3 (_dge ,_ccd ,_abd ,_fdc ,_abde );};return _gf .Errorf (_dec ,"\u0069\u006e\u0076a\u006c\u0069\u0064\u0020G\u0042\u0054\u0065\u006d\u0070\u006c\u0061t\u0065\u0020\u0070\u0072\u006f\u0076\u0069\u0064\u0065\u0064\u003a\u0020\u0025\u0064",_ccac .GBTemplate );
};func (_fcfc *SymbolDictionary )readRefinementAtPixels (_bfefd int )error {_fcfc .SdrATX =make ([]int8 ,_bfefd );_fcfc .SdrATY =make ([]int8 ,_bfefd );var (_dfag byte ;_cegdb error ;);for _fecf :=0;_fecf < _bfefd ;_fecf ++{_dfag ,_cegdb =_fcfc ._ecdbb .ReadByte ();
if _cegdb !=nil {return _cegdb ;};_fcfc .SdrATX [_fecf ]=int8 (_dfag );_dfag ,_cegdb =_fcfc ._ecdbb .ReadByte ();if _cegdb !=nil {return _cegdb ;};_fcfc .SdrATY [_fecf ]=int8 (_dfag );};return nil ;};func (_ebe *SymbolDictionary )getUserTable (_dggb int )(_eb .Tabler ,error ){var _cdbb int ;
for _ ,_cggba :=range _ebe .Header .RTSegments {if _cggba .Type ==53{if _cdbb ==_dggb {_ebff ,_ecfc :=_cggba .GetSegmentData ();if _ecfc !=nil {return nil ,_ecfc ;};_fgaaf :=_ebff .(_eb .BasicTabler );return _eb .NewEncodedTable (_fgaaf );};_cdbb ++;};
};return nil ,nil ;};func (_fece *SymbolDictionary )encodeNumSyms (_fdbg _af .BinaryWriter )(_aead int ,_ffeef error ){const _eccbd ="\u0065\u006e\u0063\u006f\u0064\u0065\u004e\u0075\u006d\u0053\u0079\u006d\u0073";_gbbc :=make ([]byte ,4);_da .BigEndian .PutUint32 (_gbbc ,_fece .NumberOfExportedSymbols );
if _aead ,_ffeef =_fdbg .Write (_gbbc );_ffeef !=nil {return _aead ,_gf .Wrap (_ffeef ,_eccbd ,"\u0065\u0078p\u006f\u0072\u0074e\u0064\u0020\u0073\u0079\u006d\u0062\u006f\u006c\u0073");};_da .BigEndian .PutUint32 (_gbbc ,_fece .NumberOfNewSymbols );_gace ,_ffeef :=_fdbg .Write (_gbbc );
if _ffeef !=nil {return _aead ,_gf .Wrap (_ffeef ,_eccbd ,"n\u0065\u0077\u0020\u0073\u0079\u006d\u0062\u006f\u006c\u0073");};return _aead +_gace ,nil ;};func (_ccdf *Header )pageSize ()uint {if _ccdf .PageAssociation <=255{return 1;};return 4;};func (_fcaf *SymbolDictionary )setCodingStatistics ()error {if _fcaf ._cgecb ==nil {_fcaf ._cgecb =_afd .NewStats (512,1);
};if _fcaf ._gacf ==nil {_fcaf ._gacf =_afd .NewStats (512,1);};if _fcaf ._ebdd ==nil {_fcaf ._ebdd =_afd .NewStats (512,1);};if _fcaf ._bafd ==nil {_fcaf ._bafd =_afd .NewStats (512,1);};if _fcaf ._fgde ==nil {_fcaf ._fgde =_afd .NewStats (512,1);};if _fcaf .UseRefinementAggregation &&_fcaf ._dbg ==nil {_fcaf ._dbg =_afd .NewStats (1<<uint (_fcaf ._aegf ),1);
_fcaf ._edd =_afd .NewStats (512,1);_fcaf ._bgeg =_afd .NewStats (512,1);};if _fcaf ._gabd ==nil {_fcaf ._gabd =_afd .NewStats (65536,1);};if _fcaf ._edffg ==nil {var _bdabg error ;_fcaf ._edffg ,_bdabg =_afd .New (_fcaf ._ecdbb );if _bdabg !=nil {return _bdabg ;
};};return nil ;};func (_egba *Header )readNumberOfReferredToSegments (_daf *_af .Reader )(uint64 ,error ){const _fdf ="\u0072\u0065\u0061\u0064\u004e\u0075\u006d\u0062\u0065\u0072O\u0066\u0052\u0065\u0066\u0065\u0072\u0072e\u0064\u0054\u006f\u0053\u0065\u0067\u006d\u0065\u006e\u0074\u0073";
_dgge ,_gbe :=_daf .ReadBits (3);if _gbe !=nil {return 0,_gf .Wrap (_gbe ,_fdf ,"\u0063\u006f\u0075n\u0074\u0020\u006f\u0066\u0020\u0072\u0074\u0073");};_dgge &=0xf;var _abda []byte ;if _dgge <=4{_abda =make ([]byte ,5);for _dfea :=0;_dfea <=4;_dfea ++{_cdeg ,_acfd :=_daf .ReadBit ();
if _acfd !=nil {return 0,_gf .Wrap (_acfd ,_fdf ,"\u0073\u0068\u006fr\u0074\u0020\u0066\u006f\u0072\u006d\u0061\u0074");};_abda [_dfea ]=byte (_cdeg );};}else {_dgge ,_gbe =_daf .ReadBits (29);if _gbe !=nil {return 0,_gbe ;};_dgge &=_g .MaxInt32 ;_dfg :=(_dgge +8)>>3;
_dfg <<=3;_abda =make ([]byte ,_dfg );var _dccff uint64 ;for _dccff =0;_dccff < _dfg ;_dccff ++{_fcgc ,_dbb :=_daf .ReadBit ();if _dbb !=nil {return 0,_gf .Wrap (_dbb ,_fdf ,"l\u006f\u006e\u0067\u0020\u0066\u006f\u0072\u006d\u0061\u0074");};_abda [_dccff ]=byte (_fcgc );
};};return _dgge ,nil ;};type SymbolDictionary struct{_ecdbb *_af .Reader ;SdrTemplate int8 ;SdTemplate int8 ;_aeged bool ;_bfdb bool ;SdHuffAggInstanceSelection bool ;SdHuffBMSizeSelection int8 ;SdHuffDecodeWidthSelection int8 ;SdHuffDecodeHeightSelection int8 ;
UseRefinementAggregation bool ;IsHuffmanEncoded bool ;SdATX []int8 ;SdATY []int8 ;SdrATX []int8 ;SdrATY []int8 ;NumberOfExportedSymbols uint32 ;NumberOfNewSymbols uint32 ;Header *Header ;_gagg uint32 ;_agc []*_f .Bitmap ;_aagc uint32 ;_fgga []*_f .Bitmap ;
_gbeb _eb .Tabler ;_dcdd _eb .Tabler ;_bad _eb .Tabler ;_aaeg _eb .Tabler ;_fgfb []*_f .Bitmap ;_gggg []*_f .Bitmap ;_edffg *_afd .Decoder ;_bbbab *TextRegion ;_daee *GenericRegion ;_ebgd *GenericRefinementRegion ;_gabd *_afd .DecoderStats ;_gacf *_afd .DecoderStats ;
_ebdd *_afd .DecoderStats ;_bafd *_afd .DecoderStats ;_fgde *_afd .DecoderStats ;_edd *_afd .DecoderStats ;_bgeg *_afd .DecoderStats ;_cgecb *_afd .DecoderStats ;_dbg *_afd .DecoderStats ;_aegf int8 ;_ebcd *_f .Bitmaps ;_cag []int ;_gbfb map[int ]int ;
_bdged bool ;};func (_fb *GenericRefinementRegion )decodeTypicalPredictedLine (_ee ,_acb ,_bba ,_de ,_bcf ,_ceg int )error {_edf :=_ee -int (_fb .ReferenceDY );_feg :=_fb .ReferenceBitmap .GetByteIndex (0,_edf );_cca :=_fb .RegionBitmap .GetByteIndex (0,_ee );
var _beb error ;switch _fb .TemplateID {case 0:_beb =_fb .decodeTypicalPredictedLineTemplate0 (_ee ,_acb ,_bba ,_de ,_bcf ,_ceg ,_cca ,_edf ,_feg );case 1:_beb =_fb .decodeTypicalPredictedLineTemplate1 (_ee ,_acb ,_bba ,_de ,_bcf ,_ceg ,_cca ,_edf ,_feg );
};return _beb ;};func (_fdcb *PageInformationSegment )encodeFlags (_cfagb _af .BinaryWriter )(_edab error ){const _ccebc ="e\u006e\u0063\u006f\u0064\u0065\u0046\u006c\u0061\u0067\u0073";if _edab =_cfagb .SkipBits (1);_edab !=nil {return _gf .Wrap (_edab ,_ccebc ,"\u0072\u0065\u0073e\u0072\u0076\u0065\u0064\u0020\u0062\u0069\u0074");
};var _acdc int ;if _fdcb .CombinationOperatorOverrideAllowed (){_acdc =1;};if _edab =_cfagb .WriteBit (_acdc );_edab !=nil {return _gf .Wrap (_edab ,_ccebc ,"\u0063\u006f\u006db\u0069\u006e\u0061\u0074i\u006f\u006e\u0020\u006f\u0070\u0065\u0072a\u0074\u006f\u0072\u0020\u006f\u0076\u0065\u0072\u0072\u0069\u0064\u0064\u0065\u006e");
};_acdc =0;if _fdcb ._bgca {_acdc =1;};if _edab =_cfagb .WriteBit (_acdc );_edab !=nil {return _gf .Wrap (_edab ,_ccebc ,"\u0072e\u0071\u0075\u0069\u0072e\u0073\u0020\u0061\u0075\u0078i\u006ci\u0061r\u0079\u0020\u0062\u0075\u0066\u0066\u0065r");};if _edab =_cfagb .WriteBit ((int (_fdcb ._egeb )>>1)&0x01);
_edab !=nil {return _gf .Wrap (_edab ,_ccebc ,"\u0063\u006f\u006d\u0062\u0069\u006e\u0061\u0074\u0069\u006fn\u0020\u006f\u0070\u0065\u0072\u0061\u0074o\u0072\u0020\u0066\u0069\u0072\u0073\u0074\u0020\u0062\u0069\u0074");};if _edab =_cfagb .WriteBit (int (_fdcb ._egeb )&0x01);
_edab !=nil {return _gf .Wrap (_edab ,_ccebc ,"\u0063\u006f\u006db\u0069\u006e\u0061\u0074i\u006f\u006e\u0020\u006f\u0070\u0065\u0072a\u0074\u006f\u0072\u0020\u0073\u0065\u0063\u006f\u006e\u0064\u0020\u0062\u0069\u0074");};_acdc =int (_fdcb .DefaultPixelValue );
if _edab =_cfagb .WriteBit (_acdc );_edab !=nil {return _gf .Wrap (_edab ,_ccebc ,"\u0064e\u0066\u0061\u0075\u006c\u0074\u0020\u0070\u0061\u0067\u0065\u0020p\u0069\u0078\u0065\u006c\u0020\u0076\u0061\u006c\u0075\u0065");};_acdc =0;if _fdcb ._fdfa {_acdc =1;
};if _edab =_cfagb .WriteBit (_acdc );_edab !=nil {return _gf .Wrap (_edab ,_ccebc ,"\u0063\u006f\u006e\u0074ai\u006e\u0073\u0020\u0072\u0065\u0066\u0069\u006e\u0065\u006d\u0065\u006e\u0074");};_acdc =0;if _fdcb .IsLossless {_acdc =1;};if _edab =_cfagb .WriteBit (_acdc );
_edab !=nil {return _gf .Wrap (_edab ,_ccebc ,"p\u0061\u0067\u0065\u0020\u0069\u0073 \u0065\u0076\u0065\u006e\u0074\u0075\u0061\u006c\u006cy\u0020\u006c\u006fs\u0073l\u0065\u0073\u0073");};return nil ;};func (_ccg *GenericRegion )setParametersWithAt (_bfef bool ,_ffcg byte ,_ebd ,_acgg bool ,_efce ,_aeeb []int8 ,_efee ,_cabd uint32 ,_dgcc *_afd .DecoderStats ,_ecdd *_afd .Decoder ){_ccg .IsMMREncoded =_bfef ;
_ccg .GBTemplate =_ffcg ;_ccg .IsTPGDon =_ebd ;_ccg .GBAtX =_efce ;_ccg .GBAtY =_aeeb ;_ccg .RegionSegment .BitmapHeight =_cabd ;_ccg .RegionSegment .BitmapWidth =_efee ;_ccg ._aaa =nil ;_ccg .Bitmap =nil ;if _dgcc !=nil {_ccg ._ged =_dgcc ;};if _ecdd !=nil {_ccg ._egg =_ecdd ;
};_ac .Log .Trace ("\u005b\u0047\u0045\u004e\u0045\u0052\u0049\u0043\u002d\u0052\u0045\u0047\u0049O\u004e\u005d\u0020\u0073\u0065\u0074P\u0061\u0072\u0061\u006d\u0065\u0074\u0065\u0072\u0073\u0020\u0053\u0044\u0041t\u003a\u0020\u0025\u0073",_ccg );};func (_bcfdd *PatternDictionary )parseHeader ()error {_ac .Log .Trace ("\u005b\u0050\u0041\u0054\u0054\u0045\u0052\u004e\u002d\u0044\u0049\u0043\u0054I\u004f\u004e\u0041\u0052\u0059\u005d[\u0070\u0061\u0072\u0073\u0065\u0048\u0065\u0061\u0064\u0065\u0072\u005d\u0020b\u0065\u0067\u0069\u006e");
defer func (){_ac .Log .Trace ("\u005b\u0050\u0041T\u0054\u0045\u0052\u004e\u002d\u0044\u0049\u0043\u0054\u0049\u004f\u004e\u0041\u0052\u0059\u005d\u005b\u0070\u0061\u0072\u0073\u0065\u0048\u0065\u0061\u0064\u0065\u0072\u005d \u0066\u0069\u006e\u0069\u0073\u0068\u0065\u0064");
}();_ ,_defa :=_bcfdd ._cffg .ReadBits (5);if _defa !=nil {return _defa ;};if _defa =_bcfdd .readTemplate ();_defa !=nil {return _defa ;};if _defa =_bcfdd .readIsMMREncoded ();_defa !=nil {return _defa ;};if _defa =_bcfdd .readPatternWidthAndHeight ();
_defa !=nil {return _defa ;};if _defa =_bcfdd .readGrayMax ();_defa !=nil {return _defa ;};if _defa =_bcfdd .computeSegmentDataStructure ();_defa !=nil {return _defa ;};return _bcfdd .checkInput ();};func (_gfda *SymbolDictionary )decodeHeightClassCollectiveBitmap (_gadbb int64 ,_begc ,_eeeb uint32 )(*_f .Bitmap ,error ){if _gadbb ==0{_afed :=_f .New (int (_eeeb ),int (_begc ));
var (_agfb byte ;_gdeb error ;);for _agae :=0;_agae < len (_afed .Data );_agae ++{_agfb ,_gdeb =_gfda ._ecdbb .ReadByte ();if _gdeb !=nil {return nil ,_gdeb ;};if _gdeb =_afed .SetByte (_agae ,_agfb );_gdeb !=nil {return nil ,_gdeb ;};};return _afed ,nil ;
};if _gfda ._daee ==nil {_gfda ._daee =NewGenericRegion (_gfda ._ecdbb );};_gfda ._daee .setParameters (true ,_gfda ._ecdbb .AbsolutePosition (),_gadbb ,_begc ,_eeeb );_bfdg ,_gebf :=_gfda ._daee .GetRegionBitmap ();if _gebf !=nil {return nil ,_gebf ;};
return _bfdg ,nil ;};func (_bcab *GenericRegion )decodeTemplate0a (_ega ,_bgde ,_ffcb int ,_fee ,_eegf int )(_baad error ){const _dgec ="\u0064\u0065c\u006f\u0064\u0065T\u0065\u006d\u0070\u006c\u0061\u0074\u0065\u0030\u0061";var (_abec ,_cbgb int ;_cab ,_eegc int ;
_cdfg byte ;_bgf int ;);if _ega >=1{_cdfg ,_baad =_bcab .Bitmap .GetByte (_eegf );if _baad !=nil {return _gf .Wrap (_baad ,_dgec ,"\u006ci\u006e\u0065\u0020\u003e\u003d\u00201");};_cab =int (_cdfg );};if _ega >=2{_cdfg ,_baad =_bcab .Bitmap .GetByte (_eegf -_bcab .Bitmap .RowStride );
if _baad !=nil {return _gf .Wrap (_baad ,_dgec ,"\u006ci\u006e\u0065\u0020\u003e\u003d\u00202");};_eegc =int (_cdfg )<<6;};_abec =(_cab &0xf0)|(_eegc &0x3800);for _egef :=0;_egef < _ffcb ;_egef =_bgf {var (_gaga byte ;_dab int ;);_bgf =_egef +8;if _cgca :=_bgde -_egef ;
_cgca > 8{_dab =8;}else {_dab =_cgca ;};if _ega > 0{_cab <<=8;if _bgf < _bgde {_cdfg ,_baad =_bcab .Bitmap .GetByte (_eegf +1);if _baad !=nil {return _gf .Wrap (_baad ,_dgec ,"\u006c\u0069\u006e\u0065\u0020\u003e\u0020\u0030");};_cab |=int (_cdfg );};};
if _ega > 1{_efb :=_eegf -_bcab .Bitmap .RowStride +1;_eegc <<=8;if _bgf < _bgde {_cdfg ,_baad =_bcab .Bitmap .GetByte (_efb );if _baad !=nil {return _gf .Wrap (_baad ,_dgec ,"\u006c\u0069\u006e\u0065\u0020\u003e\u0020\u0031");};_eegc |=int (_cdfg )<<6;
}else {_eegc |=0;};};for _bbfg :=0;_bbfg < _dab ;_bbfg ++{_cef :=uint (7-_bbfg );if _bcab ._bgda {_cbgb =_bcab .overrideAtTemplate0a (_abec ,_egef +_bbfg ,_ega ,int (_gaga ),_bbfg ,int (_cef ));_bcab ._ged .SetIndex (int32 (_cbgb ));}else {_bcab ._ged .SetIndex (int32 (_abec ));
};var _dcg int ;_dcg ,_baad =_bcab ._egg .DecodeBit (_bcab ._ged );if _baad !=nil {return _gf .Wrap (_baad ,_dgec ,"");};_gaga |=byte (_dcg )<<_cef ;_abec =((_abec &0x7bf7)<<1)|_dcg |((_cab >>_cef )&0x10)|((_eegc >>_cef )&0x800);};if _bfe :=_bcab .Bitmap .SetByte (_fee ,_gaga );
_bfe !=nil {return _gf .Wrap (_bfe ,_dgec ,"");};_fee ++;_eegf ++;};return nil ;};func (_agcc *TableSegment )HtHigh ()int32 {return _agcc ._eag };func (_bddg *TextRegion )GetRegionInfo ()*RegionSegment {return _bddg .RegionInfo };type templater interface{form (_gdga ,_bbba ,_fff ,_daa ,_aefe int16 )int16 ;
setIndex (_fbb *_afd .DecoderStats );};func (_cdc *Header )CleanSegmentData (){if _cdc .SegmentData !=nil {_cdc .SegmentData =nil ;};};func (_bgaab *PatternDictionary )readTemplate ()error {_aabf ,_daff :=_bgaab ._cffg .ReadBits (2);if _daff !=nil {return _daff ;
};_bgaab .HDTemplate =byte (_aabf );return nil ;};func (_cfgg *HalftoneRegion )computeX (_ccc ,_deda int )int {return _cfgg .shiftAndFill (int (_cfgg .HGridX )+_ccc *int (_cfgg .HRegionY )+_deda *int (_cfgg .HRegionX ));};func (_ffc *GenericRefinementRegion )decodeOptimized (_cfc ,_aff ,_ecd ,_aca ,_gbd ,_bc ,_bca int )error {var (_aa error ;
_cd int ;_bcg int ;);_fea :=_cfc -int (_ffc .ReferenceDY );if _baf :=int (-_ffc .ReferenceDX );_baf > 0{_cd =_baf ;};_bbd :=_ffc .ReferenceBitmap .GetByteIndex (_cd ,_fea );if _ffc .ReferenceDX > 0{_bcg =int (_ffc .ReferenceDX );};_dag :=_ffc .RegionBitmap .GetByteIndex (_bcg ,_cfc );
switch _ffc .TemplateID {case 0:_aa =_ffc .decodeTemplate (_cfc ,_aff ,_ecd ,_aca ,_gbd ,_bc ,_bca ,_dag ,_fea ,_bbd ,_ffc ._be );case 1:_aa =_ffc .decodeTemplate (_cfc ,_aff ,_ecd ,_aca ,_gbd ,_bc ,_bca ,_dag ,_fea ,_bbd ,_ffc ._bb );};return _aa ;};func (_eaee *SymbolDictionary )decodeRefinedSymbol (_feaf ,_gggc uint32 )error {var (_bgae int ;
_ddcf ,_dagf int32 ;);if _eaee .IsHuffmanEncoded {_fbaa ,_ddbe :=_eaee ._ecdbb .ReadBits (byte (_eaee ._aegf ));if _ddbe !=nil {return _ddbe ;};_bgae =int (_fbaa );_fgbd ,_ddbe :=_eb .GetStandardTable (15);if _ddbe !=nil {return _ddbe ;};_bcfgg ,_ddbe :=_fgbd .Decode (_eaee ._ecdbb );
if _ddbe !=nil {return _ddbe ;};_ddcf =int32 (_bcfgg );_bcfgg ,_ddbe =_fgbd .Decode (_eaee ._ecdbb );if _ddbe !=nil {return _ddbe ;};_dagf =int32 (_bcfgg );_fgbd ,_ddbe =_eb .GetStandardTable (1);if _ddbe !=nil {return _ddbe ;};if _ ,_ddbe =_fgbd .Decode (_eaee ._ecdbb );
_ddbe !=nil {return _ddbe ;};_eaee ._ecdbb .Align ();}else {_fgaa ,_efbcd :=_eaee ._edffg .DecodeIAID (uint64 (_eaee ._aegf ),_eaee ._dbg );if _efbcd !=nil {return _efbcd ;};_bgae =int (_fgaa );_ddcf ,_efbcd =_eaee ._edffg .DecodeInt (_eaee ._edd );if _efbcd !=nil {return _efbcd ;
};_dagf ,_efbcd =_eaee ._edffg .DecodeInt (_eaee ._bgeg );if _efbcd !=nil {return _efbcd ;};};if _cfdad :=_eaee .setSymbolsArray ();_cfdad !=nil {return _cfdad ;};_cfgb :=_eaee ._gggg [_bgae ];if _aeef :=_eaee .decodeNewSymbols (_feaf ,_gggc ,_cfgb ,_ddcf ,_dagf );
_aeef !=nil {return _aeef ;};if _eaee .IsHuffmanEncoded {_eaee ._ecdbb .Align ();};return nil ;};func (_eff *TableSegment )StreamReader ()*_af .Reader {return _eff ._fcae };func (_ccgb *TextRegion )decodeRdh ()(int64 ,error ){const _feaef ="\u0064e\u0063\u006f\u0064\u0065\u0052\u0064h";
if _ccgb .IsHuffmanEncoded {if _ccgb .SbHuffRDHeight ==3{if _ccgb ._gfcfg ==nil {var (_ddcc int ;_dbba error ;);if _ccgb .SbHuffFS ==3{_ddcc ++;};if _ccgb .SbHuffDS ==3{_ddcc ++;};if _ccgb .SbHuffDT ==3{_ddcc ++;};if _ccgb .SbHuffRDWidth ==3{_ddcc ++;};
_ccgb ._gfcfg ,_dbba =_ccgb .getUserTable (_ddcc );if _dbba !=nil {return 0,_gf .Wrap (_dbba ,_feaef ,"");};};return _ccgb ._gfcfg .Decode (_ccgb ._bbbd );};_afcg ,_ccef :=_eb .GetStandardTable (14+int (_ccgb .SbHuffRDHeight ));if _ccef !=nil {return 0,_gf .Wrap (_ccef ,_feaef ,"");
};return _afcg .Decode (_ccgb ._bbbd );};_gbgc ,_edfa :=_ccgb ._dbf .DecodeInt (_ccgb ._egcg );if _edfa !=nil {return 0,_gf .Wrap (_edfa ,_feaef ,"");};return int64 (_gbgc ),nil ;};func (_eeddc *SymbolDictionary )setExportedSymbols (_caaf []int ){for _degac :=uint32 (0);
_degac < _eeddc ._gagg +_eeddc .NumberOfNewSymbols ;_degac ++{if _caaf [_degac ]==1{var _bdag *_f .Bitmap ;if _degac < _eeddc ._gagg {_bdag =_eeddc ._agc [_degac ];}else {_bdag =_eeddc ._fgga [_degac -_eeddc ._gagg ];};_ac .Log .Trace ("\u005bS\u0059\u004dB\u004f\u004c\u002d\u0044I\u0043\u0054\u0049O\u004e\u0041\u0052\u0059\u005d\u0020\u0041\u0064\u0064 E\u0078\u0070\u006fr\u0074\u0065d\u0053\u0079\u006d\u0062\u006f\u006c:\u0020\u0027%\u0073\u0027",_bdag );
_eeddc ._fgfb =append (_eeddc ._fgfb ,_bdag );};};};func _ade (_ada *_af .Reader ,_gda *Header )*GenericRefinementRegion {return &GenericRefinementRegion {_cf :_ada ,RegionInfo :NewRegionSegment (_ada ),_fa :_gda ,_be :&template0 {},_bb :&template1 {}};
};func (_acdcd *PatternDictionary )checkInput ()error {if _acdcd .HdpHeight < 1||_acdcd .HdpWidth < 1{return _ad .New ("in\u0076\u0061l\u0069\u0064\u0020\u0048\u0065\u0061\u0064\u0065\u0072 \u0056\u0061\u006c\u0075\u0065\u003a\u0020\u0057\u0069\u0064\u0074\u0068\u002f\u0048\u0065\u0069\u0067\u0068\u0074\u0020\u006d\u0075\u0073\u0074\u0020\u0062\u0065\u0020g\u0072e\u0061\u0074\u0065\u0072\u0020\u0074\u0068\u0061n\u0020z\u0065\u0072o");
};if _acdcd .IsMMREncoded {if _acdcd .HDTemplate !=0{_ac .Log .Debug ("\u0076\u0061\u0072\u0069\u0061\u0062\u006c\u0065\u0020\u0048\u0044\u0054\u0065\u006d\u0070\u006c\u0061\u0074e\u0020\u0073\u0068\u006f\u0075\u006c\u0064 \u006e\u006f\u0074\u0020\u0063\u006f\u006e\u0074\u0061\u0069\u006e \u0074\u0068\u0065\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u0030");
};};return nil ;};func (_cdfd *GenericRegion )Init (h *Header ,r *_af .Reader )error {_cdfd .RegionSegment =NewRegionSegment (r );_cdfd ._cfag =r ;return _cdfd .parseHeader ();};func (_bdfg *PageInformationSegment )readMaxStripeSize ()error {_bgb ,_gcbg :=_bdfg ._fccb .ReadBits (15);
if _gcbg !=nil {return _gcbg ;};_bdfg .MaxStripeSize =uint16 (_bgb &_g .MaxUint16 );return nil ;};type template1 struct{};func (_feefb *SymbolDictionary )huffDecodeBmSize ()(int64 ,error ){if _feefb ._bad ==nil {var (_ffcc int ;_bbbf error ;);if _feefb .SdHuffDecodeHeightSelection ==3{_ffcc ++;
};if _feefb .SdHuffDecodeWidthSelection ==3{_ffcc ++;};_feefb ._bad ,_bbbf =_feefb .getUserTable (_ffcc );if _bbbf !=nil {return 0,_bbbf ;};};return _feefb ._bad .Decode (_feefb ._ecdbb );};func (_bbe *GenericRefinementRegion )decodeTypicalPredictedLineTemplate1 (_fgg ,_eefb ,_eefd ,_aag ,_abe ,_ege ,_bga ,_aeg ,_ede int )(_bbc error ){var (_dbe ,_eae int ;
_bcd ,_ffa int ;_cb ,_eeb int ;_cbd byte ;);if _fgg > 0{_cbd ,_bbc =_bbe .RegionBitmap .GetByte (_bga -_eefd );if _bbc !=nil {return _bbc ;};_bcd =int (_cbd );};if _aeg > 0&&_aeg <=_bbe .ReferenceBitmap .Height {_cbd ,_bbc =_bbe .ReferenceBitmap .GetByte (_ede -_aag +_ege );
if _bbc !=nil {return _bbc ;};_ffa =int (_cbd )<<2;};if _aeg >=0&&_aeg < _bbe .ReferenceBitmap .Height {_cbd ,_bbc =_bbe .ReferenceBitmap .GetByte (_ede +_ege );if _bbc !=nil {return _bbc ;};_cb =int (_cbd );};if _aeg > -2&&_aeg < _bbe .ReferenceBitmap .Height -1{_cbd ,_bbc =_bbe .ReferenceBitmap .GetByte (_ede +_aag +_ege );
if _bbc !=nil {return _bbc ;};_eeb =int (_cbd );};_dbe =((_bcd >>5)&0x6)|((_eeb >>2)&0x30)|(_cb &0xc0)|(_ffa &0x200);_eae =((_eeb >>2)&0x70)|(_cb &0xc0)|(_ffa &0x700);var _fga int ;for _fcd :=0;_fcd < _abe ;_fcd =_fga {var (_fega int ;_aef int ;);_fga =_fcd +8;
if _fega =_eefb -_fcd ;_fega > 8{_fega =8;};_geg :=_fga < _eefb ;_gaf :=_fga < _bbe .ReferenceBitmap .Width ;_egcb :=_ege +1;if _fgg > 0{_cbd =0;if _geg {_cbd ,_bbc =_bbe .RegionBitmap .GetByte (_bga -_eefd +1);if _bbc !=nil {return _bbc ;};};_bcd =(_bcd <<8)|int (_cbd );
};if _aeg > 0&&_aeg <=_bbe .ReferenceBitmap .Height {var _bfa int ;if _gaf {_cbd ,_bbc =_bbe .ReferenceBitmap .GetByte (_ede -_aag +_egcb );if _bbc !=nil {return _bbc ;};_bfa =int (_cbd )<<2;};_ffa =(_ffa <<8)|_bfa ;};if _aeg >=0&&_aeg < _bbe .ReferenceBitmap .Height {_cbd =0;
if _gaf {_cbd ,_bbc =_bbe .ReferenceBitmap .GetByte (_ede +_egcb );if _bbc !=nil {return _bbc ;};};_cb =(_cb <<8)|int (_cbd );};if _aeg > -2&&_aeg < (_bbe .ReferenceBitmap .Height -1){_cbd =0;if _gaf {_cbd ,_bbc =_bbe .ReferenceBitmap .GetByte (_ede +_aag +_egcb );
if _bbc !=nil {return _bbc ;};};_eeb =(_eeb <<8)|int (_cbd );};for _aeab :=0;_aeab < _fega ;_aeab ++{var _fgc int ;_ecc :=(_eae >>4)&0x1ff;switch _ecc {case 0x1ff:_fgc =1;case 0x00:_fgc =0;default:_bbe ._ceb .SetIndex (int32 (_dbe ));_fgc ,_bbc =_bbe ._ec .DecodeBit (_bbe ._ceb );
if _bbc !=nil {return _bbc ;};};_eca :=uint (7-_aeab );_aef |=_fgc <<_eca ;_dbe =((_dbe &0x0d6)<<1)|_fgc |(_bcd >>_eca +5)&0x002|((_eeb >>_eca +2)&0x010)|((_cb >>_eca )&0x040)|((_ffa >>_eca )&0x200);_eae =((_eae &0xdb)<<1)|((_eeb >>_eca +2)&0x010)|((_cb >>_eca )&0x080)|((_ffa >>_eca )&0x400);
};_bbc =_bbe .RegionBitmap .SetByte (_bga ,byte (_aef ));if _bbc !=nil {return _bbc ;};_bga ++;_ede ++;};return nil ;};func (_gbb *Header )readHeaderFlags ()error {const _cefd ="\u0072e\u0061d\u0048\u0065\u0061\u0064\u0065\u0072\u0046\u006c\u0061\u0067\u0073";
_dgebf ,_cbed :=_gbb .Reader .ReadBit ();if _cbed !=nil {return _gf .Wrap (_cbed ,_cefd ,"r\u0065\u0074\u0061\u0069\u006e\u0020\u0066\u006c\u0061\u0067");};if _dgebf !=0{_gbb .RetainFlag =true ;};_dgebf ,_cbed =_gbb .Reader .ReadBit ();if _cbed !=nil {return _gf .Wrap (_cbed ,_cefd ,"\u0070\u0061g\u0065\u0020\u0061s\u0073\u006f\u0063\u0069\u0061\u0074\u0069\u006f\u006e");
};if _dgebf !=0{_gbb .PageAssociationFieldSize =true ;};_ceece ,_cbed :=_gbb .Reader .ReadBits (6);if _cbed !=nil {return _gf .Wrap (_cbed ,_cefd ,"\u0073\u0065\u0067m\u0065\u006e\u0074\u0020\u0074\u0079\u0070\u0065");};_gbb .Type =Type (int (_ceece ));
return nil ;};func (_ccfb *RegionSegment )readCombinationOperator ()error {_fbbc ,_aaef :=_ccfb ._gdgd .ReadBits (3);if _aaef !=nil {return _aaef ;};_ccfb .CombinaionOperator =_f .CombinationOperator (_fbbc &0xF);return nil ;};func (_baae *GenericRegion )overrideAtTemplate2 (_gfe ,_bafc ,_ffb ,_aceg ,_gbf int )int {_gfe &=0x3FB;
if _baae .GBAtY [0]==0&&_baae .GBAtX [0]>=-int8 (_gbf ){_gfe |=(_aceg >>uint (7-(int8 (_gbf )+_baae .GBAtX [0]))&0x1)<<2;}else {_gfe |=int (_baae .getPixel (_bafc +int (_baae .GBAtX [0]),_ffb +int (_baae .GBAtY [0])))<<2;};return _gfe ;};func (_cbfb *PageInformationSegment )readCombinationOperatorOverrideAllowed ()error {_gefd ,_ecae :=_cbfb ._fccb .ReadBit ();
if _ecae !=nil {return _ecae ;};if _gefd ==1{_cbfb ._affa =true ;};return nil ;};func (_eedg *HalftoneRegion )renderPattern (_gbg [][]int )(_ecg error ){var _dabcf ,_eefbb int ;for _ebad :=0;_ebad < int (_eedg .HGridHeight );_ebad ++{for _bgcc :=0;_bgcc < int (_eedg .HGridWidth );
_bgcc ++{_dabcf =_eedg .computeX (_ebad ,_bgcc );_eefbb =_eedg .computeY (_ebad ,_bgcc );_eee :=_eedg .Patterns [_gbg [_ebad ][_bgcc ]];if _ecg =_f .Blit (_eee ,_eedg .HalftoneRegionBitmap ,_dabcf +int (_eedg .HGridX ),_eefbb +int (_eedg .HGridY ),_eedg .CombinationOperator );
_ecg !=nil {return _ecg ;};};};return nil ;};func (_aadb *TextRegion )createRegionBitmap ()error {_aadb .RegionBitmap =_f .New (int (_aadb .RegionInfo .BitmapWidth ),int (_aadb .RegionInfo .BitmapHeight ));if _aadb .DefaultPixel !=0{_aadb .RegionBitmap .SetDefaultPixel ();
};return nil ;};var _ SegmentEncoder =&GenericRegion {};func (_fdfd *TextRegion )encodeSymbols (_gddb _af .BinaryWriter )(_adgf int ,_effa error ){const _fccce ="\u0065\u006e\u0063\u006f\u0064\u0065\u0053\u0079\u006d\u0062\u006f\u006c\u0073";_cddea :=make ([]byte ,4);
_da .BigEndian .PutUint32 (_cddea ,_fdfd .NumberOfSymbols );if _adgf ,_effa =_gddb .Write (_cddea );_effa !=nil {return _adgf ,_gf .Wrap (_effa ,_fccce ,"\u004e\u0075\u006dbe\u0072\u004f\u0066\u0053\u0079\u006d\u0062\u006f\u006c\u0049\u006e\u0073\u0074\u0061\u006e\u0063\u0065\u0073");
};_ddgd ,_effa :=_f .NewClassedPoints (_fdfd ._eced ,_fdfd ._bcgb );if _effa !=nil {return 0,_gf .Wrap (_effa ,_fccce ,"");};var _cadd ,_efdec int ;_dbgg :=_gd .New ();_dbgg .Init ();if _effa =_dbgg .EncodeInteger (_gd .IADT ,0);_effa !=nil {return _adgf ,_gf .Wrap (_effa ,_fccce ,"\u0069\u006e\u0069\u0074\u0069\u0061\u006c\u0020\u0044\u0054");
};_gaadf ,_effa :=_ddgd .GroupByY ();if _effa !=nil {return 0,_gf .Wrap (_effa ,_fccce ,"");};for _ ,_ccaab :=range _gaadf {_dffcb :=int (_ccaab .YAtIndex (0));_cace :=_dffcb -_cadd ;if _effa =_dbgg .EncodeInteger (_gd .IADT ,_cace );_effa !=nil {return _adgf ,_gf .Wrap (_effa ,_fccce ,"");
};var _gddef int ;for _eafd ,_abgdd :=range _ccaab .IntSlice {switch _eafd {case 0:_gbge :=int (_ccaab .XAtIndex (_eafd ))-_efdec ;if _effa =_dbgg .EncodeInteger (_gd .IAFS ,_gbge );_effa !=nil {return _adgf ,_gf .Wrap (_effa ,_fccce ,"");};_efdec +=_gbge ;
_gddef =_efdec ;default:_eefgf :=int (_ccaab .XAtIndex (_eafd ))-_gddef ;if _effa =_dbgg .EncodeInteger (_gd .IADS ,_eefgf );_effa !=nil {return _adgf ,_gf .Wrap (_effa ,_fccce ,"");};_gddef +=_eefgf ;};_ggfdb ,_ddad :=_fdfd ._fdbf .Get (_abgdd );if _ddad !=nil {return _adgf ,_gf .Wrap (_ddad ,_fccce ,"");
};_gbgg ,_fbaaa :=_fdfd ._cgba [_ggfdb ];if !_fbaaa {_gbgg ,_fbaaa =_fdfd ._bcbgd [_ggfdb ];if !_fbaaa {return _adgf ,_gf .Errorf (_fccce ,"\u0053\u0079\u006d\u0062\u006f\u006c:\u0020\u0027\u0025d\u0027\u0020\u0069s\u0020\u006e\u006f\u0074\u0020\u0066\u006f\u0075\u006e\u0064 \u0069\u006e\u0020\u0067\u006cob\u0061\u006c\u0020\u0061\u006e\u0064\u0020\u006c\u006f\u0063\u0061\u006c\u0020\u0073\u0079\u006d\u0062\u006f\u006c\u0020\u006d\u0061\u0070",_ggfdb );
};};if _ddad =_dbgg .EncodeIAID (_fdfd ._cddba ,_gbgg );_ddad !=nil {return _adgf ,_gf .Wrap (_ddad ,_fccce ,"");};};if _effa =_dbgg .EncodeOOB (_gd .IADS );_effa !=nil {return _adgf ,_gf .Wrap (_effa ,_fccce ,"");};};_dbgg .Final ();_cdad ,_effa :=_dbgg .WriteTo (_gddb );
if _effa !=nil {return _adgf ,_gf .Wrap (_effa ,_fccce ,"");};_adgf +=int (_cdad );return _adgf ,nil ;};func (_ce *EndOfStripe )LineNumber ()int {return _ce ._bg };const (TSymbolDictionary Type =0;TIntermediateTextRegion Type =4;TImmediateTextRegion Type =6;
TImmediateLosslessTextRegion Type =7;TPatternDictionary Type =16;TIntermediateHalftoneRegion Type =20;TImmediateHalftoneRegion Type =22;TImmediateLosslessHalftoneRegion Type =23;TIntermediateGenericRegion Type =36;TImmediateGenericRegion Type =38;TImmediateLosslessGenericRegion Type =39;
TIntermediateGenericRefinementRegion Type =40;TImmediateGenericRefinementRegion Type =42;TImmediateLosslessGenericRefinementRegion Type =43;TPageInformation Type =48;TEndOfPage Type =49;TEndOfStrip Type =50;TEndOfFile Type =51;TProfiles Type =52;TTables Type =53;
TExtension Type =62;TBitmap Type =70;);func (_daef *PatternDictionary )readIsMMREncoded ()error {_fcga ,_gbbd :=_daef ._cffg .ReadBit ();if _gbbd !=nil {return _gbbd ;};if _fcga !=0{_daef .IsMMREncoded =true ;};return nil ;};func (_cee *GenericRefinementRegion )String ()string {_bfc :=&_a .Builder {};
_bfc .WriteString ("\u000a[\u0047E\u004e\u0045\u0052\u0049\u0043 \u0052\u0045G\u0049\u004f\u004e\u005d\u000a");_bfc .WriteString (_cee .RegionInfo .String ()+"\u000a");_bfc .WriteString (_b .Sprintf ("\u0009\u002d \u0049\u0073\u0054P\u0047\u0052\u006f\u006e\u003a\u0020\u0025\u0076\u000a",_cee .IsTPGROn ));
_bfc .WriteString (_b .Sprintf ("\u0009-\u0020T\u0065\u006d\u0070\u006c\u0061t\u0065\u0049D\u003a\u0020\u0025\u0076\u000a",_cee .TemplateID ));_bfc .WriteString (_b .Sprintf ("\u0009\u002d\u0020\u0047\u0072\u0041\u0074\u0058\u003a\u0020\u0025\u0076\u000a",_cee .GrAtX ));
_bfc .WriteString (_b .Sprintf ("\u0009\u002d\u0020\u0047\u0072\u0041\u0074\u0059\u003a\u0020\u0025\u0076\u000a",_cee .GrAtY ));_bfc .WriteString (_b .Sprintf ("\u0009-\u0020R\u0065\u0066\u0065\u0072\u0065n\u0063\u0065D\u0058\u0020\u0025\u0076\u000a",_cee .ReferenceDX ));
_bfc .WriteString (_b .Sprintf ("\u0009\u002d\u0020\u0052ef\u0065\u0072\u0065\u006e\u0063\u0044\u0065\u0059\u003a\u0020\u0025\u0076\u000a",_cee .ReferenceDY ));return _bfc .String ();};func (_agdc *RegionSegment )Size ()int {return 17};type EncodeInitializer interface{InitEncode ();
};func (_dccg *TextRegion )Encode (w _af .BinaryWriter )(_accce int ,_bbea error ){const _eeff ="\u0054\u0065\u0078\u0074\u0052\u0065\u0067\u0069\u006f\u006e\u002e\u0045n\u0063\u006f\u0064\u0065";if _accce ,_bbea =_dccg .RegionInfo .Encode (w );_bbea !=nil {return _accce ,_gf .Wrap (_bbea ,_eeff ,"");
};var _acddb int ;if _acddb ,_bbea =_dccg .encodeFlags (w );_bbea !=nil {return _accce ,_gf .Wrap (_bbea ,_eeff ,"");};_accce +=_acddb ;if _acddb ,_bbea =_dccg .encodeSymbols (w );_bbea !=nil {return _accce ,_gf .Wrap (_bbea ,_eeff ,"");};_accce +=_acddb ;
return _accce ,nil ;};func _gdcbe (_ebcb *_af .Reader ,_agcf *Header )*TextRegion {_gebfc :=&TextRegion {_bbbd :_ebcb ,Header :_agcf ,RegionInfo :NewRegionSegment (_ebcb )};return _gebfc ;};func (_egdf *SymbolDictionary )encodeATFlags (_egfb _af .BinaryWriter )(_edcd int ,_ggb error ){const _gbbgb ="\u0065\u006e\u0063\u006f\u0064\u0065\u0041\u0054\u0046\u006c\u0061\u0067\u0073";
if _egdf .IsHuffmanEncoded ||_egdf .SdTemplate !=0{return 0,nil ;};_fcdc :=4;if _egdf .SdTemplate !=0{_fcdc =1;};for _aed :=0;_aed < _fcdc ;_aed ++{if _ggb =_egfb .WriteByte (byte (_egdf .SdATX [_aed ]));_ggb !=nil {return _edcd ,_gf .Wrapf (_ggb ,_gbbgb ,"\u0053d\u0041\u0054\u0058\u005b\u0025\u0064]",_aed );
};_edcd ++;if _ggb =_egfb .WriteByte (byte (_egdf .SdATY [_aed ]));_ggb !=nil {return _edcd ,_gf .Wrapf (_ggb ,_gbbgb ,"\u0053d\u0041\u0054\u0059\u005b\u0025\u0064]",_aed );};_edcd ++;};return _edcd ,nil ;};func (_bac *Header )writeSegmentPageAssociation (_gacb _af .BinaryWriter )(_acegg int ,_cbef error ){const _dff ="w\u0072\u0069\u0074\u0065\u0053\u0065g\u006d\u0065\u006e\u0074\u0050\u0061\u0067\u0065\u0041s\u0073\u006f\u0063i\u0061t\u0069\u006f\u006e";
if _bac .pageSize ()!=4{if _cbef =_gacb .WriteByte (byte (_bac .PageAssociation ));_cbef !=nil {return 0,_gf .Wrap (_cbef ,_dff ,"\u0070\u0061\u0067\u0065\u0053\u0069\u007a\u0065\u0020\u0021\u003d\u0020\u0034");};return 1,nil ;};_ebb :=make ([]byte ,4);
_da .BigEndian .PutUint32 (_ebb ,uint32 (_bac .PageAssociation ));if _acegg ,_cbef =_gacb .Write (_ebb );_cbef !=nil {return 0,_gf .Wrap (_cbef ,_dff ,"\u0034 \u0062y\u0074\u0065\u0020\u0070\u0061g\u0065\u0020n\u0075\u006d\u0062\u0065\u0072");};return _acegg ,nil ;
};func (_dcgde *TextRegion )decodeRdw ()(int64 ,error ){const _cbgba ="\u0064e\u0063\u006f\u0064\u0065\u0052\u0064w";if _dcgde .IsHuffmanEncoded {if _dcgde .SbHuffRDWidth ==3{if _dcgde ._cad ==nil {var (_eaced int ;_eggf error ;);if _dcgde .SbHuffFS ==3{_eaced ++;
};if _dcgde .SbHuffDS ==3{_eaced ++;};if _dcgde .SbHuffDT ==3{_eaced ++;};_dcgde ._cad ,_eggf =_dcgde .getUserTable (_eaced );if _eggf !=nil {return 0,_gf .Wrap (_eggf ,_cbgba ,"");};};return _dcgde ._cad .Decode (_dcgde ._bbbd );};_gbdg ,_acfa :=_eb .GetStandardTable (14+int (_dcgde .SbHuffRDWidth ));
if _acfa !=nil {return 0,_gf .Wrap (_acfa ,_cbgba ,"");};return _gbdg .Decode (_dcgde ._bbbd );};_fed ,_fggd :=_dcgde ._dbf .DecodeInt (_dcgde ._cdfe );if _fggd !=nil {return 0,_gf .Wrap (_fggd ,_cbgba ,"");};return int64 (_fed ),nil ;};func (_edg *GenericRefinementRegion )getGrReference ()(*_f .Bitmap ,error ){segments :=_edg ._fa .RTSegments ;
if len (segments )==0{return nil ,_ad .New ("\u0052\u0065f\u0065\u0072\u0065\u006e\u0063\u0065\u0064\u0020\u0053\u0065\u0067\u006d\u0065\u006e\u0074\u0020\u006e\u006f\u0074\u0020\u0065\u0078is\u0074\u0073");};_bgc ,_gea :=segments [0].GetSegmentData ();
if _gea !=nil {return nil ,_gea ;};_ea ,_cce :=_bgc .(Regioner );if !_cce {return nil ,_b .Errorf ("\u0072\u0065\u0066\u0065\u0072r\u0065\u0064\u0020\u0074\u006f\u0020\u0053\u0065\u0067\u006d\u0065\u006e\u0074 \u0069\u0073\u0020\u006e\u006f\u0074\u0020\u0061\u0020\u0052\u0065\u0067\u0069\u006f\u006e\u0065\u0072\u003a\u0020\u0025\u0054",_bgc );
};return _ea .GetRegionBitmap ();};func (_ggfd *SymbolDictionary )getSbSymCodeLen ()int8 {_abbf :=int8 (_g .Ceil (_g .Log (float64 (_ggfd ._gagg +_ggfd .NumberOfNewSymbols ))/_g .Log (2)));if _ggfd .IsHuffmanEncoded &&_abbf < 1{return 1;};return _abbf ;
};func (_dfbb *SymbolDictionary )GetDictionary ()([]*_f .Bitmap ,error ){_ac .Log .Trace ("\u005b\u0053\u0059\u004d\u0042\u004f\u004c-\u0044\u0049\u0043T\u0049\u004f\u004e\u0041R\u0059\u005d\u0020\u0047\u0065\u0074\u0044\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u0020\u0062\u0065\u0067\u0069\u006e\u0073\u002e\u002e\u002e");
defer func (){_ac .Log .Trace ("\u005b\u0053\u0059M\u0042\u004f\u004c\u002d\u0044\u0049\u0043\u0054\u0049\u004f\u004e\u0041\u0052\u0059\u005d\u0020\u0047\u0065\u0074\u0044\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079 \u0066\u0069\u006e\u0069\u0073\u0068\u0065\u0064");
_ac .Log .Trace ("\u005b\u0053Y\u004d\u0042\u004f\u004c\u002dD\u0049\u0043\u0054\u0049\u004fN\u0041\u0052\u0059\u005d\u0020\u0044\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u002e\u0020\u000a\u0045\u0078\u003a\u0020\u0027\u0025\u0073\u0027\u002c\u0020\u000a\u006e\u0065\u0077\u003a\u0027\u0025\u0073\u0027",_dfbb ._fgfb ,_dfbb ._fgga );
}();if _dfbb ._fgfb ==nil {var _dagg error ;if _dfbb .UseRefinementAggregation {_dfbb ._aegf =_dfbb .getSbSymCodeLen ();};if !_dfbb .IsHuffmanEncoded {if _dagg =_dfbb .setCodingStatistics ();_dagg !=nil {return nil ,_dagg ;};};_dfbb ._fgga =make ([]*_f .Bitmap ,_dfbb .NumberOfNewSymbols );
var _cffa []int ;if _dfbb .IsHuffmanEncoded &&!_dfbb .UseRefinementAggregation {_cffa =make ([]int ,_dfbb .NumberOfNewSymbols );};if _dagg =_dfbb .setSymbolsArray ();_dagg !=nil {return nil ,_dagg ;};var _ddegf ,_eedb int64 ;_dfbb ._aagc =0;for _dfbb ._aagc < _dfbb .NumberOfNewSymbols {_eedb ,_dagg =_dfbb .decodeHeightClassDeltaHeight ();
if _dagg !=nil {return nil ,_dagg ;};_ddegf +=_eedb ;var _ddf ,_gega uint32 ;_gffbd :=int64 (_dfbb ._aagc );for {var _bbcb int64 ;_bbcb ,_dagg =_dfbb .decodeDifferenceWidth ();if _ad .Is (_dagg ,_gb .ErrOOB ){break ;};if _dagg !=nil {return nil ,_dagg ;
};if _dfbb ._aagc >=_dfbb .NumberOfNewSymbols {break ;};_ddf +=uint32 (_bbcb );_gega +=_ddf ;if !_dfbb .IsHuffmanEncoded ||_dfbb .UseRefinementAggregation {if !_dfbb .UseRefinementAggregation {_dagg =_dfbb .decodeDirectlyThroughGenericRegion (_ddf ,uint32 (_ddegf ));
if _dagg !=nil {return nil ,_dagg ;};}else {_dagg =_dfbb .decodeAggregate (_ddf ,uint32 (_ddegf ));if _dagg !=nil {return nil ,_dagg ;};};}else if _dfbb .IsHuffmanEncoded &&!_dfbb .UseRefinementAggregation {_cffa [_dfbb ._aagc ]=int (_ddf );};_dfbb ._aagc ++;
};if _dfbb .IsHuffmanEncoded &&!_dfbb .UseRefinementAggregation {var _dgbe int64 ;if _dfbb .SdHuffBMSizeSelection ==0{var _cfda _eb .Tabler ;_cfda ,_dagg =_eb .GetStandardTable (1);if _dagg !=nil {return nil ,_dagg ;};_dgbe ,_dagg =_cfda .Decode (_dfbb ._ecdbb );
if _dagg !=nil {return nil ,_dagg ;};}else {_dgbe ,_dagg =_dfbb .huffDecodeBmSize ();if _dagg !=nil {return nil ,_dagg ;};};_dfbb ._ecdbb .Align ();var _affb *_f .Bitmap ;_affb ,_dagg =_dfbb .decodeHeightClassCollectiveBitmap (_dgbe ,uint32 (_ddegf ),_gega );
if _dagg !=nil {return nil ,_dagg ;};_dagg =_dfbb .decodeHeightClassBitmap (_affb ,_gffbd ,int (_ddegf ),_cffa );if _dagg !=nil {return nil ,_dagg ;};};};_afad ,_dagg :=_dfbb .getToExportFlags ();if _dagg !=nil {return nil ,_dagg ;};_dfbb .setExportedSymbols (_afad );
};return _dfbb ._fgfb ,nil ;};func (_dfbd *PageInformationSegment )readResolution ()error {_feab ,_ecbg :=_dfbd ._fccb .ReadBits (32);if _ecbg !=nil {return _ecbg ;};_dfbd .ResolutionX =int (_feab &_g .MaxInt32 );_feab ,_ecbg =_dfbd ._fccb .ReadBits (32);
if _ecbg !=nil {return _ecbg ;};_dfbd .ResolutionY =int (_feab &_g .MaxInt32 );return nil ;};var _ templater =&template1 {};func (_fcge *Header )writeReferredToCount (_efa _af .BinaryWriter )(_gcgd int ,_bbeeb error ){const _fbg ="w\u0072i\u0074\u0065\u0052\u0065\u0066\u0065\u0072\u0072e\u0064\u0054\u006f\u0043ou\u006e\u0074";
_fcge .RTSNumbers =make ([]int ,len (_fcge .RTSegments ));for _cgdb ,_ccae :=range _fcge .RTSegments {_fcge .RTSNumbers [_cgdb ]=int (_ccae .SegmentNumber );};if len (_fcge .RTSNumbers )<=4{var _aaga byte ;if len (_fcge .RetainBits )>=1{_aaga =_fcge .RetainBits [0];
};_aaga |=byte (len (_fcge .RTSNumbers ))<<5;if _bbeeb =_efa .WriteByte (_aaga );_bbeeb !=nil {return 0,_gf .Wrap (_bbeeb ,_fbg ,"\u0073\u0068\u006fr\u0074\u0020\u0066\u006f\u0072\u006d\u0061\u0074");};return 1,nil ;};_afff :=uint32 (len (_fcge .RTSNumbers ));
_ccdd :=make ([]byte ,4+_ae .Ceil (len (_fcge .RTSNumbers )+1,8));_afff |=0x7<<29;_da .BigEndian .PutUint32 (_ccdd ,_afff );copy (_ccdd [1:],_fcge .RetainBits );_gcgd ,_bbeeb =_efa .Write (_ccdd );if _bbeeb !=nil {return 0,_gf .Wrap (_bbeeb ,_fbg ,"l\u006f\u006e\u0067\u0020\u0066\u006f\u0072\u006d\u0061\u0074");
};return _gcgd ,nil ;};func (_ccead *PageInformationSegment )String ()string {_cgee :=&_a .Builder {};_cgee .WriteString ("\u000a\u005b\u0050\u0041G\u0045\u002d\u0049\u004e\u0046\u004f\u0052\u004d\u0041\u0054I\u004fN\u002d\u0053\u0045\u0047\u004d\u0045\u004eT\u005d\u000a");
_cgee .WriteString (_b .Sprintf ("\u0009\u002d \u0042\u004d\u0048e\u0069\u0067\u0068\u0074\u003a\u0020\u0025\u0064\u000a",_ccead .PageBMHeight ));_cgee .WriteString (_b .Sprintf ("\u0009-\u0020B\u004d\u0057\u0069\u0064\u0074\u0068\u003a\u0020\u0025\u0064\u000a",_ccead .PageBMWidth ));
_cgee .WriteString (_b .Sprintf ("\u0009\u002d\u0020\u0052es\u006f\u006c\u0075\u0074\u0069\u006f\u006e\u0058\u003a\u0020\u0025\u0064\u000a",_ccead .ResolutionX ));_cgee .WriteString (_b .Sprintf ("\u0009\u002d\u0020\u0052es\u006f\u006c\u0075\u0074\u0069\u006f\u006e\u0059\u003a\u0020\u0025\u0064\u000a",_ccead .ResolutionY ));
_cgee .WriteString (_b .Sprintf ("\t\u002d\u0020\u0043\u006f\u006d\u0062i\u006e\u0061\u0074\u0069\u006f\u006e\u004f\u0070\u0065r\u0061\u0074\u006fr\u003a \u0025\u0073\u000a",_ccead ._egeb ));_cgee .WriteString (_b .Sprintf ("\t\u002d\u0020\u0043\u006f\u006d\u0062i\u006e\u0061\u0074\u0069\u006f\u006eO\u0070\u0065\u0072\u0061\u0074\u006f\u0072O\u0076\u0065\u0072\u0072\u0069\u0064\u0065\u003a\u0020\u0025v\u000a",_ccead ._affa ));
_cgee .WriteString (_b .Sprintf ("\u0009-\u0020I\u0073\u004c\u006f\u0073\u0073l\u0065\u0073s\u003a\u0020\u0025\u0076\u000a",_ccead .IsLossless ));_cgee .WriteString (_b .Sprintf ("\u0009\u002d\u0020R\u0065\u0071\u0075\u0069r\u0065\u0073\u0041\u0075\u0078\u0069\u006ci\u0061\u0072\u0079\u0042\u0075\u0066\u0066\u0065\u0072\u003a\u0020\u0025\u0076\u000a",_ccead ._bgca ));
_cgee .WriteString (_b .Sprintf ("\u0009\u002d\u0020M\u0069\u0067\u0068\u0074C\u006f\u006e\u0074\u0061\u0069\u006e\u0052e\u0066\u0069\u006e\u0065\u006d\u0065\u006e\u0074\u0073\u003a\u0020\u0025\u0076\u000a",_ccead ._fdfa ));_cgee .WriteString (_b .Sprintf ("\u0009\u002d\u0020\u0049\u0073\u0053\u0074\u0072\u0069\u0070\u0065\u0064:\u0020\u0025\u0076\u000a",_ccead .IsStripe ));
_cgee .WriteString (_b .Sprintf ("\t\u002d\u0020\u004d\u0061xS\u0074r\u0069\u0070\u0065\u0053\u0069z\u0065\u003a\u0020\u0025\u0076\u000a",_ccead .MaxStripeSize ));return _cgee .String ();};type GenericRefinementRegion struct{_be templater ;_bb templater ;
_cf *_af .Reader ;_fa *Header ;RegionInfo *RegionSegment ;IsTPGROn bool ;TemplateID int8 ;Template templater ;GrAtX []int8 ;GrAtY []int8 ;RegionBitmap *_f .Bitmap ;ReferenceBitmap *_f .Bitmap ;ReferenceDX int32 ;ReferenceDY int32 ;_ec *_afd .Decoder ;_ceb *_afd .DecoderStats ;
_faf bool ;_ba []bool ;};var _ _eb .BasicTabler =&TableSegment {};type Regioner interface{GetRegionBitmap ()(*_f .Bitmap ,error );GetRegionInfo ()*RegionSegment ;};func (_bgg *template0 )setIndex (_dea *_afd .DecoderStats ){_dea .SetIndex (0x100)};func (_ffd *SymbolDictionary )decodeHeightClassDeltaHeight ()(int64 ,error ){if _ffd .IsHuffmanEncoded {return _ffd .decodeHeightClassDeltaHeightWithHuffman ();
};_efbca ,_gefg :=_ffd ._edffg .DecodeInt (_ffd ._gacf );if _gefg !=nil {return 0,_gefg ;};return int64 (_efbca ),nil ;};func (_gffgd *TextRegion )computeSymbolCodeLength ()error {if _gffgd .IsHuffmanEncoded {return _gffgd .symbolIDCodeLengths ();};_gffgd ._fdge =int8 (_g .Ceil (_g .Log (float64 (_gffgd .NumberOfSymbols ))/_g .Log (2)));
return nil ;};func (_afcgf *TextRegion )initSymbols ()error {const _bcfe ="i\u006e\u0069\u0074\u0053\u0079\u006d\u0062\u006f\u006c\u0073";for _ ,_gccd :=range _afcgf .Header .RTSegments {if _gccd ==nil {return _gf .Error (_bcfe ,"\u006e\u0069\u006c\u0020\u0073\u0065\u0067\u006de\u006e\u0074\u0020pr\u006f\u0076\u0069\u0064\u0065\u0064 \u0066\u006f\u0072\u0020\u0074\u0068\u0065\u0020\u0074\u0065\u0078\u0074\u0020\u0072\u0065g\u0069\u006f\u006e\u0020\u0053\u0079\u006d\u0062o\u006c\u0073");
};if _gccd .Type ==0{_ceaf ,_dgae :=_gccd .GetSegmentData ();if _dgae !=nil {return _gf .Wrap (_dgae ,_bcfe ,"");};_gaca ,_dbfc :=_ceaf .(*SymbolDictionary );if !_dbfc {return _gf .Error (_bcfe ,"\u0072e\u0066\u0065r\u0072\u0065\u0064 \u0054\u006f\u0020\u0053\u0065\u0067\u006de\u006e\u0074\u0020\u0069\u0073\u0020n\u006f\u0074\u0020\u0061\u0020\u0053\u0079\u006d\u0062\u006f\u006cD\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079");
};_gaca ._dbg =_afcgf ._bea ;_ddeb ,_dgae :=_gaca .GetDictionary ();if _dgae !=nil {return _gf .Wrap (_dgae ,_bcfe ,"");};_afcgf .Symbols =append (_afcgf .Symbols ,_ddeb ...);};};_afcgf .NumberOfSymbols =uint32 (len (_afcgf .Symbols ));return nil ;};type Documenter interface{GetPage (int )(Pager ,error );
GetGlobalSegment (int )(*Header ,error );};func (_def *GenericRegion )GetRegionBitmap ()(_edcf *_f .Bitmap ,_cgfe error ){if _def .Bitmap !=nil {return _def .Bitmap ,nil ;};if _def .IsMMREncoded {if _def ._aaa ==nil {_def ._aaa ,_cgfe =_c .New (_def ._cfag ,int (_def .RegionSegment .BitmapWidth ),int (_def .RegionSegment .BitmapHeight ),_def .DataOffset ,_def .DataLength );
if _cgfe !=nil {return nil ,_cgfe ;};};_def .Bitmap ,_cgfe =_def ._aaa .UncompressMMR ();return _def .Bitmap ,_cgfe ;};if _cgfe =_def .updateOverrideFlags ();_cgfe !=nil {return nil ,_cgfe ;};var _cdf int ;if _def ._egg ==nil {_def ._egg ,_cgfe =_afd .New (_def ._cfag );
if _cgfe !=nil {return nil ,_cgfe ;};};if _def ._ged ==nil {_def ._ged =_afd .NewStats (65536,1);};_def .Bitmap =_f .New (int (_def .RegionSegment .BitmapWidth ),int (_def .RegionSegment .BitmapHeight ));_gag :=int (uint32 (_def .Bitmap .Width +7)&(^uint32 (7)));
for _dfd :=0;_dfd < _def .Bitmap .Height ;_dfd ++{if _def .IsTPGDon {var _ecbd int ;_ecbd ,_cgfe =_def .decodeSLTP ();if _cgfe !=nil {return nil ,_cgfe ;};_cdf ^=_ecbd ;};if _cdf ==1{if _dfd > 0{if _cgfe =_def .copyLineAbove (_dfd );_cgfe !=nil {return nil ,_cgfe ;
};};}else {if _cgfe =_def .decodeLine (_dfd ,_def .Bitmap .Width ,_gag );_cgfe !=nil {return nil ,_cgfe ;};};};return _def .Bitmap ,nil ;};func (_egeca *TextRegion )checkInput ()error {const _ddae ="\u0063\u0068\u0065\u0063\u006b\u0049\u006e\u0070\u0075\u0074";
if !_egeca .UseRefinement {if _egeca .SbrTemplate !=0{_ac .Log .Debug ("\u0053\u0062\u0072Te\u006d\u0070\u006c\u0061\u0074\u0065\u0020\u0073\u0068\u006f\u0075\u006c\u0064\u0020\u0062\u0065\u0020\u0030");_egeca .SbrTemplate =0;};};if _egeca .SbHuffFS ==2||_egeca .SbHuffRDWidth ==2||_egeca .SbHuffRDHeight ==2||_egeca .SbHuffRDX ==2||_egeca .SbHuffRDY ==2{return _gf .Error (_ddae ,"h\u0075\u0066\u0066\u006d\u0061\u006e \u0066\u006c\u0061\u0067\u0020\u0076a\u006c\u0075\u0065\u0020\u0069\u0073\u0020n\u006f\u0074\u0020\u0070\u0065\u0072\u006d\u0069\u0074\u0074e\u0064");
};if !_egeca .UseRefinement {if _egeca .SbHuffRSize !=0{_ac .Log .Debug ("\u0053\u0062\u0048uf\u0066\u0052\u0053\u0069\u007a\u0065\u0020\u0073\u0068\u006f\u0075\u006c\u0064\u0020\u0062\u0065\u0020\u0030");_egeca .SbHuffRSize =0;};if _egeca .SbHuffRDY !=0{_ac .Log .Debug ("S\u0062\u0048\u0075\u0066fR\u0044Y\u0020\u0073\u0068\u006f\u0075l\u0064\u0020\u0062\u0065\u0020\u0030");
_egeca .SbHuffRDY =0;};if _egeca .SbHuffRDX !=0{_ac .Log .Debug ("S\u0062\u0048\u0075\u0066fR\u0044X\u0020\u0073\u0068\u006f\u0075l\u0064\u0020\u0062\u0065\u0020\u0030");_egeca .SbHuffRDX =0;};if _egeca .SbHuffRDWidth !=0{_ac .Log .Debug ("\u0053b\u0048\u0075\u0066\u0066R\u0044\u0057\u0069\u0064\u0074h\u0020s\u0068o\u0075\u006c\u0064\u0020\u0062\u0065\u00200");
_egeca .SbHuffRDWidth =0;};if _egeca .SbHuffRDHeight !=0{_ac .Log .Debug ("\u0053\u0062\u0048\u0075\u0066\u0066\u0052\u0044\u0048\u0065\u0069g\u0068\u0074\u0020\u0073\u0068\u006f\u0075\u006c\u0064\u0020b\u0065\u0020\u0030");_egeca .SbHuffRDHeight =0;};
};return nil ;};func (_cbda *PageInformationSegment )parseHeader ()(_aceb error ){_ac .Log .Trace ("\u005b\u0050\u0061\u0067\u0065I\u006e\u0066\u006f\u0072\u006d\u0061\u0074\u0069\u006f\u006e\u0053\u0065\u0067m\u0065\u006e\u0074\u005d\u0020\u0050\u0061\u0072\u0073\u0069\u006e\u0067\u0048\u0065\u0061\u0064\u0065\u0072\u002e\u002e\u002e");
defer func (){var _dggc ="[\u0050\u0061\u0067\u0065\u0049\u006e\u0066\u006f\u0072m\u0061\u0074\u0069\u006f\u006e\u0053\u0065gm\u0065\u006e\u0074\u005d \u0050\u0061\u0072\u0073\u0069\u006e\u0067\u0048\u0065ad\u0065\u0072 \u0046\u0069\u006e\u0069\u0073\u0068\u0065\u0064";
if _aceb !=nil {_dggc +="\u0020\u0077\u0069t\u0068\u0020\u0065\u0072\u0072\u006f\u0072\u0020"+_aceb .Error ();}else {_dggc +="\u0020\u0073\u0075\u0063\u0063\u0065\u0073\u0073\u0066\u0075\u006c\u006c\u0079";};_ac .Log .Trace (_dggc );}();if _aceb =_cbda .readWidthAndHeight ();
_aceb !=nil {return _aceb ;};if _aceb =_cbda .readResolution ();_aceb !=nil {return _aceb ;};_ ,_aceb =_cbda ._fccb .ReadBit ();if _aceb !=nil {return _aceb ;};if _aceb =_cbda .readCombinationOperatorOverrideAllowed ();_aceb !=nil {return _aceb ;};if _aceb =_cbda .readRequiresAuxiliaryBuffer ();
_aceb !=nil {return _aceb ;};if _aceb =_cbda .readCombinationOperator ();_aceb !=nil {return _aceb ;};if _aceb =_cbda .readDefaultPixelValue ();_aceb !=nil {return _aceb ;};if _aceb =_cbda .readContainsRefinement ();_aceb !=nil {return _aceb ;};if _aceb =_cbda .readIsLossless ();
_aceb !=nil {return _aceb ;};if _aceb =_cbda .readIsStriped ();_aceb !=nil {return _aceb ;};if _aceb =_cbda .readMaxStripeSize ();_aceb !=nil {return _aceb ;};if _aceb =_cbda .checkInput ();_aceb !=nil {return _aceb ;};_ac .Log .Trace ("\u0025\u0073",_cbda );
return nil ;};func (_edbd *TextRegion )decodeSymInRefSize ()(int64 ,error ){const _abff ="\u0064e\u0063o\u0064\u0065\u0053\u0079\u006dI\u006e\u0052e\u0066\u0053\u0069\u007a\u0065";if _edbd .SbHuffRSize ==0{_cfga ,_dbecc :=_eb .GetStandardTable (1);if _dbecc !=nil {return 0,_gf .Wrap (_dbecc ,_abff ,"");
};return _cfga .Decode (_edbd ._bbbd );};if _edbd ._ebee ==nil {var (_gddc int ;_ggeb error ;);if _edbd .SbHuffFS ==3{_gddc ++;};if _edbd .SbHuffDS ==3{_gddc ++;};if _edbd .SbHuffDT ==3{_gddc ++;};if _edbd .SbHuffRDWidth ==3{_gddc ++;};if _edbd .SbHuffRDHeight ==3{_gddc ++;
};if _edbd .SbHuffRDX ==3{_gddc ++;};if _edbd .SbHuffRDY ==3{_gddc ++;};_edbd ._ebee ,_ggeb =_edbd .getUserTable (_gddc );if _ggeb !=nil {return 0,_gf .Wrap (_ggeb ,_abff ,"");};};_cbegc ,_gceb :=_edbd ._ebee .Decode (_edbd ._bbbd );if _gceb !=nil {return 0,_gf .Wrap (_gceb ,_abff ,"");
};return _cbegc ,nil ;};func (_bcfdf *TextRegion )decodeRdx ()(int64 ,error ){const _dgebd ="\u0064e\u0063\u006f\u0064\u0065\u0052\u0064x";if _bcfdf .IsHuffmanEncoded {if _bcfdf .SbHuffRDX ==3{if _bcfdf ._cegdd ==nil {var (_ecfcb int ;_geae error ;);if _bcfdf .SbHuffFS ==3{_ecfcb ++;
};if _bcfdf .SbHuffDS ==3{_ecfcb ++;};if _bcfdf .SbHuffDT ==3{_ecfcb ++;};if _bcfdf .SbHuffRDWidth ==3{_ecfcb ++;};if _bcfdf .SbHuffRDHeight ==3{_ecfcb ++;};_bcfdf ._cegdd ,_geae =_bcfdf .getUserTable (_ecfcb );if _geae !=nil {return 0,_gf .Wrap (_geae ,_dgebd ,"");
};};return _bcfdf ._cegdd .Decode (_bcfdf ._bbbd );};_eaag ,_gbgad :=_eb .GetStandardTable (14+int (_bcfdf .SbHuffRDX ));if _gbgad !=nil {return 0,_gf .Wrap (_gbgad ,_dgebd ,"");};return _eaag .Decode (_bcfdf ._bbbd );};_aefc ,_cadf :=_bcfdf ._dbf .DecodeInt (_bcfdf ._gffge );
if _cadf !=nil {return 0,_gf .Wrap (_cadf ,_dgebd ,"");};return int64 (_aefc ),nil ;};func (_bae *PageInformationSegment )readIsLossless ()error {_cda ,_abdd :=_bae ._fccb .ReadBit ();if _abdd !=nil {return _abdd ;};if _cda ==1{_bae .IsLossless =true ;
};return nil ;};func (_beeae *TextRegion )GetRegionBitmap ()(*_f .Bitmap ,error ){if _beeae .RegionBitmap !=nil {return _beeae .RegionBitmap ,nil ;};if !_beeae .IsHuffmanEncoded {if _dcddc :=_beeae .setCodingStatistics ();_dcddc !=nil {return nil ,_dcddc ;
};};if _accf :=_beeae .createRegionBitmap ();_accf !=nil {return nil ,_accf ;};if _dbdb :=_beeae .decodeSymbolInstances ();_dbdb !=nil {return nil ,_dbdb ;};return _beeae .RegionBitmap ,nil ;};func (_abf *GenericRegion )decodeSLTP ()(int ,error ){switch _abf .GBTemplate {case 0:_abf ._ged .SetIndex (0x9B25);
case 1:_abf ._ged .SetIndex (0x795);case 2:_abf ._ged .SetIndex (0xE5);case 3:_abf ._ged .SetIndex (0x195);};return _abf ._egg .DecodeBit (_abf ._ged );};func (_dde *template1 )form (_cgf ,_fegf ,_ggf ,_ffe ,_cfa int16 )int16 {return ((_cgf &0x02)<<8)|(_fegf <<6)|((_ggf &0x03)<<4)|(_ffe <<1)|_cfa ;
};func (_dbcde *TableSegment )HtLow ()int32 {return _dbcde ._ecge };func (_aebg *GenericRegion )decodeTemplate2 (_cae ,_aebf ,_dcb int ,_fcc ,_aege int )(_fbac error ){const _gga ="\u0064e\u0063o\u0064\u0065\u0054\u0065\u006d\u0070\u006c\u0061\u0074\u0065\u0032";
var (_bee ,_eacd int ;_cfd ,_adeb int ;_deef byte ;_dcgc ,_bfd int ;);if _cae >=1{_deef ,_fbac =_aebg .Bitmap .GetByte (_aege );if _fbac !=nil {return _gf .Wrap (_fbac ,_gga ,"\u006ci\u006ee\u004e\u0075\u006d\u0062\u0065\u0072\u0020\u003e\u003d\u0020\u0031");
};_cfd =int (_deef );};if _cae >=2{_deef ,_fbac =_aebg .Bitmap .GetByte (_aege -_aebg .Bitmap .RowStride );if _fbac !=nil {return _gf .Wrap (_fbac ,_gga ,"\u006ci\u006ee\u004e\u0075\u006d\u0062\u0065\u0072\u0020\u003e\u003d\u0020\u0032");};_adeb =int (_deef )<<4;
};_bee =(_cfd >>3&0x7c)|(_adeb >>3&0x380);for _egff :=0;_egff < _dcb ;_egff =_dcgc {var (_fgdc byte ;_affg int ;);_dcgc =_egff +8;if _dcf :=_aebf -_egff ;_dcf > 8{_affg =8;}else {_affg =_dcf ;};if _cae > 0{_cfd <<=8;if _dcgc < _aebf {_deef ,_fbac =_aebg .Bitmap .GetByte (_aege +1);
if _fbac !=nil {return _gf .Wrap (_fbac ,_gga ,"\u006c\u0069\u006e\u0065\u004e\u0075\u006d\u0062\u0065r\u0020\u003e\u0020\u0030");};_cfd |=int (_deef );};};if _cae > 1{_adeb <<=8;if _dcgc < _aebf {_deef ,_fbac =_aebg .Bitmap .GetByte (_aege -_aebg .Bitmap .RowStride +1);
if _fbac !=nil {return _gf .Wrap (_fbac ,_gga ,"\u006c\u0069\u006e\u0065\u004e\u0075\u006d\u0062\u0065r\u0020\u003e\u0020\u0031");};_adeb |=int (_deef )<<4;};};for _gfbe :=0;_gfbe < _affg ;_gfbe ++{_dabc :=uint (10-_gfbe );if _aebg ._bgda {_eacd =_aebg .overrideAtTemplate2 (_bee ,_egff +_gfbe ,_cae ,int (_fgdc ),_gfbe );
_aebg ._ged .SetIndex (int32 (_eacd ));}else {_aebg ._ged .SetIndex (int32 (_bee ));};_bfd ,_fbac =_aebg ._egg .DecodeBit (_aebg ._ged );if _fbac !=nil {return _gf .Wrap (_fbac ,_gga ,"");};_fgdc |=byte (_bfd <<uint (7-_gfbe ));_bee =((_bee &0x1bd)<<1)|_bfd |((_cfd >>_dabc )&0x4)|((_adeb >>_dabc )&0x80);
};if _deg :=_aebg .Bitmap .SetByte (_fcc ,_fgdc );_deg !=nil {return _gf .Wrap (_deg ,_gga ,"");};_fcc ++;_aege ++;};return nil ;};func (_dceb *PageInformationSegment )readDefaultPixelValue ()error {_cdgg ,_abgd :=_dceb ._fccb .ReadBit ();if _abgd !=nil {return _abgd ;
};_dceb .DefaultPixelValue =uint8 (_cdgg &0xf);return nil ;};const (ORandom OrganizationType =iota ;OSequential ;);type GenericRegion struct{_cfag *_af .Reader ;DataHeaderOffset int64 ;DataHeaderLength int64 ;DataOffset int64 ;DataLength int64 ;RegionSegment *RegionSegment ;
UseExtTemplates bool ;IsTPGDon bool ;GBTemplate byte ;IsMMREncoded bool ;UseMMR bool ;GBAtX []int8 ;GBAtY []int8 ;GBAtOverride []bool ;_bgda bool ;Bitmap *_f .Bitmap ;_egg *_afd .Decoder ;_ged *_afd .DecoderStats ;_aaa *_c .Decoder ;};func (_efbe *TextRegion )decodeCurrentT ()(int64 ,error ){if _efbe .SbStrips !=1{if _efbe .IsHuffmanEncoded {_febe ,_egac :=_efbe ._bbbd .ReadBits (byte (_efbe .LogSBStrips ));
return int64 (_febe ),_egac ;};_eaeg ,_ddbg :=_efbe ._dbf .DecodeInt (_efbe ._adge );if _ddbg !=nil {return 0,_ddbg ;};return int64 (_eaeg ),nil ;};return 0,nil ;};func (_gegd *Header )Encode (w _af .BinaryWriter )(_dgg int ,_bbdd error ){const _cefg ="\u0048\u0065\u0061d\u0065\u0072\u002e\u0057\u0072\u0069\u0074\u0065";
var _bfcg _af .BinaryWriter ;_ac .Log .Trace ("\u005b\u0053\u0045G\u004d\u0045\u004e\u0054-\u0048\u0045\u0041\u0044\u0045\u0052\u005d[\u0045\u004e\u0043\u004f\u0044\u0045\u005d\u0020\u0042\u0065\u0067\u0069\u006e\u0073");defer func (){if _bbdd !=nil {_ac .Log .Trace ("[\u0053\u0045\u0047\u004d\u0045\u004eT\u002d\u0048\u0045\u0041\u0044\u0045R\u005d\u005b\u0045\u004e\u0043\u004f\u0044E\u005d\u0020\u0046\u0061\u0069\u006c\u0065\u0064\u002e\u0020%\u0076",_bbdd );
}else {_ac .Log .Trace ("\u005b\u0053\u0045\u0047ME\u004e\u0054\u002d\u0048\u0045\u0041\u0044\u0045\u0052\u005d\u0020\u0025\u0076",_gegd );_ac .Log .Trace ("\u005b\u0053\u0045\u0047\u004d\u0045N\u0054\u002d\u0048\u0045\u0041\u0044\u0045\u0052\u005d\u005b\u0045\u004e\u0043O\u0044\u0045\u005d\u0020\u0046\u0069\u006ei\u0073\u0068\u0065\u0064");
};}();w .FinishByte ();if _gegd .SegmentData !=nil {_cdd ,_ddb :=_gegd .SegmentData .(SegmentEncoder );if !_ddb {return 0,_gf .Errorf (_cefg ,"\u0053\u0065\u0067\u006d\u0065\u006e\u0074\u003a\u0020\u0025\u0054\u0020\u0064\u006f\u0065s\u006e\u0027\u0074\u0020\u0069\u006d\u0070\u006c\u0065\u006d\u0065\u006e\u0074 \u0053\u0065\u0067\u006d\u0065\u006e\u0074\u0045\u006e\u0063\u006f\u0064er\u0020\u0069\u006e\u0074\u0065\u0072\u0066\u0061\u0063\u0065",_gegd .SegmentData );
};_bfcg =_af .BufferedMSB ();_dgg ,_bbdd =_cdd .Encode (_bfcg );if _bbdd !=nil {return 0,_gf .Wrap (_bbdd ,_cefg ,"");};_gegd .SegmentDataLength =uint64 (_dgg );};if _gegd .pageSize ()==4{_gegd .PageAssociationFieldSize =true ;};var _daec int ;_daec ,_bbdd =_gegd .writeSegmentNumber (w );
if _bbdd !=nil {return 0,_gf .Wrap (_bbdd ,_cefg ,"");};_dgg +=_daec ;if _bbdd =_gegd .writeFlags (w );_bbdd !=nil {return _dgg ,_gf .Wrap (_bbdd ,_cefg ,"");};_dgg ++;_daec ,_bbdd =_gegd .writeReferredToCount (w );if _bbdd !=nil {return 0,_gf .Wrap (_bbdd ,_cefg ,"");
};_dgg +=_daec ;_daec ,_bbdd =_gegd .writeReferredToSegments (w );if _bbdd !=nil {return 0,_gf .Wrap (_bbdd ,_cefg ,"");};_dgg +=_daec ;_daec ,_bbdd =_gegd .writeSegmentPageAssociation (w );if _bbdd !=nil {return 0,_gf .Wrap (_bbdd ,_cefg ,"");};_dgg +=_daec ;
_daec ,_bbdd =_gegd .writeSegmentDataLength (w );if _bbdd !=nil {return 0,_gf .Wrap (_bbdd ,_cefg ,"");};_dgg +=_daec ;_gegd .HeaderLength =int64 (_dgg )-int64 (_gegd .SegmentDataLength );if _bfcg !=nil {if _ ,_bbdd =w .Write (_bfcg .Data ());_bbdd !=nil {return _dgg ,_gf .Wrap (_bbdd ,_cefg ,"\u0077r\u0069t\u0065\u0020\u0073\u0065\u0067m\u0065\u006et\u0020\u0064\u0061\u0074\u0061");
};};return _dgg ,nil ;};func (_fagc *TextRegion )decodeRI ()(int64 ,error ){if !_fagc .UseRefinement {return 0,nil ;};if _fagc .IsHuffmanEncoded {_cbfag ,_gagb :=_fagc ._bbbd .ReadBit ();return int64 (_cbfag ),_gagb ;};_acad ,_acbdb :=_fagc ._dbf .DecodeInt (_fagc ._gaec );
return int64 (_acad ),_acbdb ;};func (_efca *GenericRegion )copyLineAbove (_cbf int )error {_gcgf :=_cbf *_efca .Bitmap .RowStride ;_bag :=_gcgf -_efca .Bitmap .RowStride ;for _adba :=0;_adba < _efca .Bitmap .RowStride ;_adba ++{_fbag ,_edff :=_efca .Bitmap .GetByte (_bag );
if _edff !=nil {return _edff ;};_bag ++;if _edff =_efca .Bitmap .SetByte (_gcgf ,_fbag );_edff !=nil {return _edff ;};_gcgf ++;};return nil ;};func (_acaa *HalftoneRegion )grayScaleDecoding (_fdg int )([][]int ,error ){var (_gaae []int8 ;_ecdc []int8 ;
);if !_acaa .IsMMREncoded {_gaae =make ([]int8 ,4);_ecdc =make ([]int8 ,4);if _acaa .HTemplate <=1{_gaae [0]=3;}else if _acaa .HTemplate >=2{_gaae [0]=2;};_ecdc [0]=-1;_gaae [1]=-3;_ecdc [1]=-1;_gaae [2]=2;_ecdc [2]=-2;_gaae [3]=-2;_ecdc [3]=-2;};_gdf :=make ([]*_f .Bitmap ,_fdg );
_cdeb :=NewGenericRegion (_acaa ._efbc );_cdeb .setParametersMMR (_acaa .IsMMREncoded ,_acaa .DataOffset ,_acaa .DataLength ,_acaa .HGridHeight ,_acaa .HGridWidth ,_acaa .HTemplate ,false ,_acaa .HSkipEnabled ,_gaae ,_ecdc );_gef :=_fdg -1;var _fcbe error ;
_gdf [_gef ],_fcbe =_cdeb .GetRegionBitmap ();if _fcbe !=nil {return nil ,_fcbe ;};for _gef > 0{_gef --;_cdeb .Bitmap =nil ;_gdf [_gef ],_fcbe =_cdeb .GetRegionBitmap ();if _fcbe !=nil {return nil ,_fcbe ;};if _fcbe =_acaa .combineGrayscalePlanes (_gdf ,_gef );
_fcbe !=nil {return nil ,_fcbe ;};};return _acaa .computeGrayScalePlanes (_gdf ,_fdg );};func (_fg *GenericRefinementRegion )decodeTypicalPredictedLineTemplate0 (_adf ,_bcc ,_bce ,_abc ,_db ,_fegg ,_ecdf ,_ga ,_ggg int )error {var (_deb ,_bf ,_gc ,_gac ,_ccee ,_afg int ;
_bd byte ;_bdb error ;);if _adf > 0{_bd ,_bdb =_fg .RegionBitmap .GetByte (_ecdf -_bce );if _bdb !=nil {return _bdb ;};_gc =int (_bd );};if _ga > 0&&_ga <=_fg .ReferenceBitmap .Height {_bd ,_bdb =_fg .ReferenceBitmap .GetByte (_ggg -_abc +_fegg );if _bdb !=nil {return _bdb ;
};_gac =int (_bd )<<4;};if _ga >=0&&_ga < _fg .ReferenceBitmap .Height {_bd ,_bdb =_fg .ReferenceBitmap .GetByte (_ggg +_fegg );if _bdb !=nil {return _bdb ;};_ccee =int (_bd )<<1;};if _ga > -2&&_ga < _fg .ReferenceBitmap .Height -1{_bd ,_bdb =_fg .ReferenceBitmap .GetByte (_ggg +_abc +_fegg );
if _bdb !=nil {return _bdb ;};_afg =int (_bd );};_deb =((_gc >>5)&0x6)|((_afg >>2)&0x30)|(_ccee &0x180)|(_gac &0xc00);var _cfe int ;for _dba :=0;_dba < _db ;_dba =_cfe {var _abg int ;_cfe =_dba +8;var _fcb int ;if _fcb =_bcc -_dba ;_fcb > 8{_fcb =8;};_edc :=_cfe < _bcc ;
_df :=_cfe < _fg .ReferenceBitmap .Width ;_eac :=_fegg +1;if _adf > 0{_bd =0;if _edc {_bd ,_bdb =_fg .RegionBitmap .GetByte (_ecdf -_bce +1);if _bdb !=nil {return _bdb ;};};_gc =(_gc <<8)|int (_bd );};if _ga > 0&&_ga <=_fg .ReferenceBitmap .Height {var _cgcb int ;
if _df {_bd ,_bdb =_fg .ReferenceBitmap .GetByte (_ggg -_abc +_eac );if _bdb !=nil {return _bdb ;};_cgcb =int (_bd )<<4;};_gac =(_gac <<8)|_cgcb ;};if _ga >=0&&_ga < _fg .ReferenceBitmap .Height {var _ecf int ;if _df {_bd ,_bdb =_fg .ReferenceBitmap .GetByte (_ggg +_eac );
if _bdb !=nil {return _bdb ;};_ecf =int (_bd )<<1;};_ccee =(_ccee <<8)|_ecf ;};if _ga > -2&&_ga < (_fg .ReferenceBitmap .Height -1){_bd =0;if _df {_bd ,_bdb =_fg .ReferenceBitmap .GetByte (_ggg +_abc +_eac );if _bdb !=nil {return _bdb ;};};_afg =(_afg <<8)|int (_bd );
};for _egc :=0;_egc < _fcb ;_egc ++{var _eef int ;_gff :=false ;_aac :=(_deb >>4)&0x1ff;if _aac ==0x1ff{_gff =true ;_eef =1;}else if _aac ==0x00{_gff =true ;};if !_gff {if _fg ._faf {_bf =_fg .overrideAtTemplate0 (_deb ,_dba +_egc ,_adf ,_abg ,_egc );_fg ._ceb .SetIndex (int32 (_bf ));
}else {_fg ._ceb .SetIndex (int32 (_deb ));};_eef ,_bdb =_fg ._ec .DecodeBit (_fg ._ceb );if _bdb !=nil {return _bdb ;};};_fba :=uint (7-_egc );_abg |=_eef <<_fba ;_deb =((_deb &0xdb6)<<1)|_eef |(_gc >>_fba +5)&0x002|((_afg >>_fba +2)&0x010)|((_ccee >>_fba )&0x080)|((_gac >>_fba )&0x400);
};_bdb =_fg .RegionBitmap .SetByte (_ecdf ,byte (_abg ));if _bdb !=nil {return _bdb ;};_ecdf ++;_ggg ++;};return nil ;};func (_geda *TextRegion )readRegionFlags ()error {var (_feec int ;_cddd uint64 ;_aebb error ;);_feec ,_aebb =_geda ._bbbd .ReadBit ();
if _aebb !=nil {return _aebb ;};_geda .SbrTemplate =int8 (_feec );_cddd ,_aebb =_geda ._bbbd .ReadBits (5);if _aebb !=nil {return _aebb ;};_geda .SbDsOffset =int8 (_cddd );if _geda .SbDsOffset > 0x0f{_geda .SbDsOffset -=0x20;};_feec ,_aebb =_geda ._bbbd .ReadBit ();
if _aebb !=nil {return _aebb ;};_geda .DefaultPixel =int8 (_feec );_cddd ,_aebb =_geda ._bbbd .ReadBits (2);if _aebb !=nil {return _aebb ;};_geda .CombinationOperator =_f .CombinationOperator (int (_cddd )&0x3);_feec ,_aebb =_geda ._bbbd .ReadBit ();if _aebb !=nil {return _aebb ;
};_geda .IsTransposed =int8 (_feec );_cddd ,_aebb =_geda ._bbbd .ReadBits (2);if _aebb !=nil {return _aebb ;};_geda .ReferenceCorner =int16 (_cddd )&0x3;_cddd ,_aebb =_geda ._bbbd .ReadBits (2);if _aebb !=nil {return _aebb ;};_geda .LogSBStrips =int16 (_cddd )&0x3;
_geda .SbStrips =1<<uint (_geda .LogSBStrips );_feec ,_aebb =_geda ._bbbd .ReadBit ();if _aebb !=nil {return _aebb ;};if _feec ==1{_geda .UseRefinement =true ;};_feec ,_aebb =_geda ._bbbd .ReadBit ();if _aebb !=nil {return _aebb ;};if _feec ==1{_geda .IsHuffmanEncoded =true ;
};return nil ;};func (_cec *HalftoneRegion )checkInput ()error {if _cec .IsMMREncoded {if _cec .HTemplate !=0{_ac .Log .Debug ("\u0048\u0054\u0065\u006d\u0070l\u0061\u0074\u0065\u0020\u003d\u0020\u0025\u0064\u0020\u0073\u0068\u006f\u0075l\u0064\u0020\u0063\u006f\u006e\u0074\u0061\u0069\u006e\u0020\u0074\u0068\u0065\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u0030",_cec .HTemplate );
};if _cec .HSkipEnabled {_ac .Log .Debug ("\u0048\u0053\u006b\u0069\u0070\u0045\u006e\u0061\u0062\u006c\u0065\u0064\u0020\u0030\u0020\u0025\u0076\u0020(\u0073\u0068\u006f\u0075\u006c\u0064\u0020c\u006f\u006e\u0074\u0061\u0069\u006e\u0020\u0074\u0068\u0065\u0020v\u0061\u006c\u0075\u0065\u0020\u0066\u0061\u006c\u0073\u0065\u0029",_cec .HSkipEnabled );
};};return nil ;};func (_bef *PageInformationSegment )readWidthAndHeight ()error {_cgcc ,_bdeg :=_bef ._fccb .ReadBits (32);if _bdeg !=nil {return _bdeg ;};_bef .PageBMWidth =int (_cgcc &_g .MaxInt32 );_cgcc ,_bdeg =_bef ._fccb .ReadBits (32);if _bdeg !=nil {return _bdeg ;
};_bef .PageBMHeight =int (_cgcc &_g .MaxInt32 );return nil ;};func (_bbae *GenericRegion )decodeTemplate3 (_aded ,_edfd ,_ddgc int ,_fgb ,_beea int )(_gffd error ){const _cgb ="\u0064e\u0063o\u0064\u0065\u0054\u0065\u006d\u0070\u006c\u0061\u0074\u0065\u0033";
var (_dfc ,_dcc int ;_cgd int ;_ddc byte ;_gdge ,_aae int ;);if _aded >=1{_ddc ,_gffd =_bbae .Bitmap .GetByte (_beea );if _gffd !=nil {return _gf .Wrap (_gffd ,_cgb ,"\u006ci\u006e\u0065\u0020\u003e\u003d\u00201");};_cgd =int (_ddc );};_dfc =(_cgd >>1)&0x70;
for _degc :=0;_degc < _ddgc ;_degc =_gdge {var (_gae byte ;_adfg int ;);_gdge =_degc +8;if _dac :=_edfd -_degc ;_dac > 8{_adfg =8;}else {_adfg =_dac ;};if _aded >=1{_cgd <<=8;if _gdge < _edfd {_ddc ,_gffd =_bbae .Bitmap .GetByte (_beea +1);if _gffd !=nil {return _gf .Wrap (_gffd ,_cgb ,"\u0069\u006e\u006e\u0065\u0072\u0020\u002d\u0020\u006c\u0069\u006e\u0065 \u003e\u003d\u0020\u0031");
};_cgd |=int (_ddc );};};for _bde :=0;_bde < _adfg ;_bde ++{if _bbae ._bgda {_dcc =_bbae .overrideAtTemplate3 (_dfc ,_degc +_bde ,_aded ,int (_gae ),_bde );_bbae ._ged .SetIndex (int32 (_dcc ));}else {_bbae ._ged .SetIndex (int32 (_dfc ));};_aae ,_gffd =_bbae ._egg .DecodeBit (_bbae ._ged );
if _gffd !=nil {return _gf .Wrap (_gffd ,_cgb ,"");};_gae |=byte (_aae )<<byte (7-_bde );_dfc =((_dfc &0x1f7)<<1)|_aae |((_cgd >>uint (8-_bde ))&0x010);};if _caf :=_bbae .Bitmap .SetByte (_fgb ,_gae );_caf !=nil {return _gf .Wrap (_caf ,_cgb ,"");};_fgb ++;
_beea ++;};return nil ;};func (_cega *GenericRegion )getPixel (_dcfb ,_bdc int )int8 {if _dcfb < 0||_dcfb >=_cega .Bitmap .Width {return 0;};if _bdc < 0||_bdc >=_cega .Bitmap .Height {return 0;};if _cega .Bitmap .GetPixel (_dcfb ,_bdc ){return 1;};return 0;
};func (_cefc *RegionSegment )String ()string {_ddbd :=&_a .Builder {};_ddbd .WriteString ("\u0009[\u0052E\u0047\u0049\u004f\u004e\u0020S\u0045\u0047M\u0045\u004e\u0054\u005d\u000a");_ddbd .WriteString (_b .Sprintf ("\t\u0009\u002d\u0020\u0042\u0069\u0074m\u0061\u0070\u0020\u0028\u0077\u0069d\u0074\u0068\u002c\u0020\u0068\u0065\u0069g\u0068\u0074\u0029\u0020\u005b\u0025\u0064\u0078\u0025\u0064]\u000a",_cefc .BitmapWidth ,_cefc .BitmapHeight ));
_ddbd .WriteString (_b .Sprintf ("\u0009\u0009\u002d\u0020L\u006f\u0063\u0061\u0074\u0069\u006f\u006e\u0020\u0028\u0078,\u0079)\u003a\u0020\u005b\u0025\u0064\u002c\u0025d\u005d\u000a",_cefc .XLocation ,_cefc .YLocation ));_ddbd .WriteString (_b .Sprintf ("\t\u0009\u002d\u0020\u0043\u006f\u006db\u0069\u006e\u0061\u0074\u0069\u006f\u006e\u004f\u0070e\u0072\u0061\u0074o\u0072:\u0020\u0025\u0073",_cefc .CombinaionOperator ));
return _ddbd .String ();};func (_agac *SymbolDictionary )huffDecodeRefAggNInst ()(int64 ,error ){if !_agac .SdHuffAggInstanceSelection {_bdae ,_aebae :=_eb .GetStandardTable (1);if _aebae !=nil {return 0,_aebae ;};return _bdae .Decode (_agac ._ecdbb );
};if _agac ._aaeg ==nil {var (_ggef int ;_bdgbg error ;);if _agac .SdHuffDecodeHeightSelection ==3{_ggef ++;};if _agac .SdHuffDecodeWidthSelection ==3{_ggef ++;};if _agac .SdHuffBMSizeSelection ==3{_ggef ++;};_agac ._aaeg ,_bdgbg =_agac .getUserTable (_ggef );
if _bdgbg !=nil {return 0,_bdgbg ;};};return _agac ._aaeg .Decode (_agac ._ecdbb );};func (_daebb *TextRegion )getSymbols ()error {if _daebb .Header .RTSegments !=nil {return _daebb .initSymbols ();};return nil ;};func (_ddea *PageInformationSegment )Encode (w _af .BinaryWriter )(_egce int ,_bage error ){const _bfee ="\u0050\u0061g\u0065\u0049\u006e\u0066\u006f\u0072\u006d\u0061\u0074\u0069\u006f\u006e\u0053\u0065\u0067\u006d\u0065\u006e\u0074\u002e\u0045\u006eco\u0064\u0065";
_gdfa :=make ([]byte ,4);_da .BigEndian .PutUint32 (_gdfa ,uint32 (_ddea .PageBMWidth ));_egce ,_bage =w .Write (_gdfa );if _bage !=nil {return _egce ,_gf .Wrap (_bage ,_bfee ,"\u0077\u0069\u0064t\u0068");};_da .BigEndian .PutUint32 (_gdfa ,uint32 (_ddea .PageBMHeight ));
var _gbcb int ;_gbcb ,_bage =w .Write (_gdfa );if _bage !=nil {return _gbcb +_egce ,_gf .Wrap (_bage ,_bfee ,"\u0068\u0065\u0069\u0067\u0068\u0074");};_egce +=_gbcb ;_da .BigEndian .PutUint32 (_gdfa ,uint32 (_ddea .ResolutionX ));_gbcb ,_bage =w .Write (_gdfa );
if _bage !=nil {return _gbcb +_egce ,_gf .Wrap (_bage ,_bfee ,"\u0078\u0020\u0072e\u0073\u006f\u006c\u0075\u0074\u0069\u006f\u006e");};_egce +=_gbcb ;_da .BigEndian .PutUint32 (_gdfa ,uint32 (_ddea .ResolutionY ));if _gbcb ,_bage =w .Write (_gdfa );_bage !=nil {return _gbcb +_egce ,_gf .Wrap (_bage ,_bfee ,"\u0079\u0020\u0072e\u0073\u006f\u006c\u0075\u0074\u0069\u006f\u006e");
};_egce +=_gbcb ;if _bage =_ddea .encodeFlags (w );_bage !=nil {return _egce ,_gf .Wrap (_bage ,_bfee ,"");};_egce ++;if _gbcb ,_bage =_ddea .encodeStripingInformation (w );_bage !=nil {return _egce ,_gf .Wrap (_bage ,_bfee ,"");};_egce +=_gbcb ;return _egce ,nil ;
};func (_aace *SymbolDictionary )encodeSymbols (_cgff _af .BinaryWriter )(_fefc int ,_fddg error ){const _geeb ="\u0065\u006e\u0063o\u0064\u0065\u0053\u0079\u006d\u0062\u006f\u006c";_baac :=_gd .New ();_baac .Init ();_defd ,_fddg :=_aace ._ebcd .SelectByIndexes (_aace ._cag );
if _fddg !=nil {return 0,_gf .Wrap (_fddg ,_geeb ,"\u0069n\u0069\u0074\u0069\u0061\u006c");};_gfag :=map[*_f .Bitmap ]int {};for _dfab ,_daeb :=range _defd .Values {_gfag [_daeb ]=_dfab ;};_defd .SortByHeight ();var _baca ,_aaefg int ;_dcce ,_fddg :=_defd .GroupByHeight ();
if _fddg !=nil {return 0,_gf .Wrap (_fddg ,_geeb ,"");};for _ ,_agdbf :=range _dcce .Values {_gaaec :=_agdbf .Values [0].Height ;_fbgg :=_gaaec -_baca ;if _fddg =_baac .EncodeInteger (_gd .IADH ,_fbgg );_fddg !=nil {return 0,_gf .Wrapf (_fddg ,_geeb ,"\u0049\u0041\u0044\u0048\u0020\u0066\u006f\u0072\u0020\u0064\u0068\u003a \u0027\u0025\u0064\u0027",_fbgg );
};_baca =_gaaec ;_ebgg ,_degg :=_agdbf .GroupByWidth ();if _degg !=nil {return 0,_gf .Wrapf (_degg ,_geeb ,"\u0068\u0065\u0069g\u0068\u0074\u003a\u0020\u0027\u0025\u0064\u0027",_gaaec );};var _cagd int ;for _ ,_bgfe :=range _ebgg .Values {for _ ,_cbbd :=range _bgfe .Values {_badb :=_cbbd .Width ;
_adfb :=_badb -_cagd ;if _degg =_baac .EncodeInteger (_gd .IADW ,_adfb );_degg !=nil {return 0,_gf .Wrapf (_degg ,_geeb ,"\u0049\u0041\u0044\u0057\u0020\u0066\u006f\u0072\u0020\u0064\u0077\u003a \u0027\u0025\u0064\u0027",_adfb );};_cagd +=_adfb ;if _degg =_baac .EncodeBitmap (_cbbd ,false );
_degg !=nil {return 0,_gf .Wrapf (_degg ,_geeb ,"H\u0065i\u0067\u0068\u0074\u003a\u0020\u0025\u0064\u0020W\u0069\u0064\u0074\u0068: \u0025\u0064",_gaaec ,_badb );};_dddb :=_gfag [_cbbd ];_aace ._gbfb [_dddb ]=_aaefg ;_aaefg ++;};};if _degg =_baac .EncodeOOB (_gd .IADW );
_degg !=nil {return 0,_gf .Wrap (_degg ,_geeb ,"\u0049\u0041\u0044\u0057");};};if _fddg =_baac .EncodeInteger (_gd .IAEX ,0);_fddg !=nil {return 0,_gf .Wrap (_fddg ,_geeb ,"\u0065\u0078p\u006f\u0072\u0074e\u0064\u0020\u0073\u0079\u006d\u0062\u006f\u006c\u0073");
};if _fddg =_baac .EncodeInteger (_gd .IAEX ,len (_aace ._cag ));_fddg !=nil {return 0,_gf .Wrap (_fddg ,_geeb ,"\u006e\u0075\u006d\u0062\u0065\u0072\u0020\u006f\u0066\u0020\u0073\u0079m\u0062\u006f\u006c\u0073");};_baac .Final ();_bgcf ,_fddg :=_baac .WriteTo (_cgff );
if _fddg !=nil {return 0,_gf .Wrap (_fddg ,_geeb ,"\u0077\u0072i\u0074\u0069\u006e\u0067 \u0065\u006ec\u006f\u0064\u0065\u0072\u0020\u0063\u006f\u006et\u0065\u0078\u0074\u0020\u0074\u006f\u0020\u0027\u0077\u0027\u0020\u0077r\u0069\u0074\u0065\u0072");};
return int (_bgcf ),nil ;};type HalftoneRegion struct{_efbc *_af .Reader ;_bgac *Header ;DataHeaderOffset int64 ;DataHeaderLength int64 ;DataOffset int64 ;DataLength int64 ;RegionSegment *RegionSegment ;HDefaultPixel int8 ;CombinationOperator _f .CombinationOperator ;
HSkipEnabled bool ;HTemplate byte ;IsMMREncoded bool ;HGridWidth uint32 ;HGridHeight uint32 ;HGridX int32 ;HGridY int32 ;HRegionX uint16 ;HRegionY uint16 ;HalftoneRegionBitmap *_f .Bitmap ;Patterns []*_f .Bitmap ;};func (_fcg *GenericRegion )overrideAtTemplate0b (_fgdce ,_acf ,_geca ,_eed ,_aad ,_dcd int )int {if _fcg .GBAtOverride [0]{_fgdce &=0xFFFD;
if _fcg .GBAtY [0]==0&&_fcg .GBAtX [0]>=-int8 (_aad ){_fgdce |=(_eed >>uint (int8 (_dcd )-_fcg .GBAtX [0]&0x1))<<1;}else {_fgdce |=int (_fcg .getPixel (_acf +int (_fcg .GBAtX [0]),_geca +int (_fcg .GBAtY [0])))<<1;};};if _fcg .GBAtOverride [1]{_fgdce &=0xDFFF;
if _fcg .GBAtY [1]==0&&_fcg .GBAtX [1]>=-int8 (_aad ){_fgdce |=(_eed >>uint (int8 (_dcd )-_fcg .GBAtX [1]&0x1))<<13;}else {_fgdce |=int (_fcg .getPixel (_acf +int (_fcg .GBAtX [1]),_geca +int (_fcg .GBAtY [1])))<<13;};};if _fcg .GBAtOverride [2]{_fgdce &=0xFDFF;
if _fcg .GBAtY [2]==0&&_fcg .GBAtX [2]>=-int8 (_aad ){_fgdce |=(_eed >>uint (int8 (_dcd )-_fcg .GBAtX [2]&0x1))<<9;}else {_fgdce |=int (_fcg .getPixel (_acf +int (_fcg .GBAtX [2]),_geca +int (_fcg .GBAtY [2])))<<9;};};if _fcg .GBAtOverride [3]{_fgdce &=0xBFFF;
if _fcg .GBAtY [3]==0&&_fcg .GBAtX [3]>=-int8 (_aad ){_fgdce |=(_eed >>uint (int8 (_dcd )-_fcg .GBAtX [3]&0x1))<<14;}else {_fgdce |=int (_fcg .getPixel (_acf +int (_fcg .GBAtX [3]),_geca +int (_fcg .GBAtY [3])))<<14;};};if _fcg .GBAtOverride [4]{_fgdce &=0xEFFF;
if _fcg .GBAtY [4]==0&&_fcg .GBAtX [4]>=-int8 (_aad ){_fgdce |=(_eed >>uint (int8 (_dcd )-_fcg .GBAtX [4]&0x1))<<12;}else {_fgdce |=int (_fcg .getPixel (_acf +int (_fcg .GBAtX [4]),_geca +int (_fcg .GBAtY [4])))<<12;};};if _fcg .GBAtOverride [5]{_fgdce &=0xFFDF;
if _fcg .GBAtY [5]==0&&_fcg .GBAtX [5]>=-int8 (_aad ){_fgdce |=(_eed >>uint (int8 (_dcd )-_fcg .GBAtX [5]&0x1))<<5;}else {_fgdce |=int (_fcg .getPixel (_acf +int (_fcg .GBAtX [5]),_geca +int (_fcg .GBAtY [5])))<<5;};};if _fcg .GBAtOverride [6]{_fgdce &=0xFFFB;
if _fcg .GBAtY [6]==0&&_fcg .GBAtX [6]>=-int8 (_aad ){_fgdce |=(_eed >>uint (int8 (_dcd )-_fcg .GBAtX [6]&0x1))<<2;}else {_fgdce |=int (_fcg .getPixel (_acf +int (_fcg .GBAtX [6]),_geca +int (_fcg .GBAtY [6])))<<2;};};if _fcg .GBAtOverride [7]{_fgdce &=0xFFF7;
if _fcg .GBAtY [7]==0&&_fcg .GBAtX [7]>=-int8 (_aad ){_fgdce |=(_eed >>uint (int8 (_dcd )-_fcg .GBAtX [7]&0x1))<<3;}else {_fgdce |=int (_fcg .getPixel (_acf +int (_fcg .GBAtX [7]),_geca +int (_fcg .GBAtY [7])))<<3;};};if _fcg .GBAtOverride [8]{_fgdce &=0xF7FF;
if _fcg .GBAtY [8]==0&&_fcg .GBAtX [8]>=-int8 (_aad ){_fgdce |=(_eed >>uint (int8 (_dcd )-_fcg .GBAtX [8]&0x1))<<11;}else {_fgdce |=int (_fcg .getPixel (_acf +int (_fcg .GBAtX [8]),_geca +int (_fcg .GBAtY [8])))<<11;};};if _fcg .GBAtOverride [9]{_fgdce &=0xFFEF;
if _fcg .GBAtY [9]==0&&_fcg .GBAtX [9]>=-int8 (_aad ){_fgdce |=(_eed >>uint (int8 (_dcd )-_fcg .GBAtX [9]&0x1))<<4;}else {_fgdce |=int (_fcg .getPixel (_acf +int (_fcg .GBAtX [9]),_geca +int (_fcg .GBAtY [9])))<<4;};};if _fcg .GBAtOverride [10]{_fgdce &=0x7FFF;
if _fcg .GBAtY [10]==0&&_fcg .GBAtX [10]>=-int8 (_aad ){_fgdce |=(_eed >>uint (int8 (_dcd )-_fcg .GBAtX [10]&0x1))<<15;}else {_fgdce |=int (_fcg .getPixel (_acf +int (_fcg .GBAtX [10]),_geca +int (_fcg .GBAtY [10])))<<15;};};if _fcg .GBAtOverride [11]{_fgdce &=0xFDFF;
if _fcg .GBAtY [11]==0&&_fcg .GBAtX [11]>=-int8 (_aad ){_fgdce |=(_eed >>uint (int8 (_dcd )-_fcg .GBAtX [11]&0x1))<<10;}else {_fgdce |=int (_fcg .getPixel (_acf +int (_fcg .GBAtX [11]),_geca +int (_fcg .GBAtY [11])))<<10;};};return _fgdce ;};func (_bcff *Header )referenceSize ()uint {switch {case _bcff .SegmentNumber <=255:return 1;
case _bcff .SegmentNumber <=65535:return 2;default:return 4;};};func (_fag *GenericRegion )computeSegmentDataStructure ()error {_fag .DataOffset =_fag ._cfag .AbsolutePosition ();_fag .DataHeaderLength =_fag .DataOffset -_fag .DataHeaderOffset ;_fag .DataLength =int64 (_fag ._cfag .AbsoluteLength ())-_fag .DataHeaderLength ;
return nil ;};func (_fadc *PatternDictionary )Init (h *Header ,r *_af .Reader )error {_fadc ._cffg =r ;return _fadc .parseHeader ();};func (_dedf *TableSegment )HtOOB ()int32 {return _dedf ._bbdg };func (_fbgcf *SymbolDictionary )readRegionFlags ()error {var (_bcfbb uint64 ;
_efaa int ;);_ ,_aebe :=_fbgcf ._ecdbb .ReadBits (3);if _aebe !=nil {return _aebe ;};_efaa ,_aebe =_fbgcf ._ecdbb .ReadBit ();if _aebe !=nil {return _aebe ;};_fbgcf .SdrTemplate =int8 (_efaa );_bcfbb ,_aebe =_fbgcf ._ecdbb .ReadBits (2);if _aebe !=nil {return _aebe ;
};_fbgcf .SdTemplate =int8 (_bcfbb &0xf);_efaa ,_aebe =_fbgcf ._ecdbb .ReadBit ();if _aebe !=nil {return _aebe ;};if _efaa ==1{_fbgcf ._aeged =true ;};_efaa ,_aebe =_fbgcf ._ecdbb .ReadBit ();if _aebe !=nil {return _aebe ;};if _efaa ==1{_fbgcf ._bfdb =true ;
};_efaa ,_aebe =_fbgcf ._ecdbb .ReadBit ();if _aebe !=nil {return _aebe ;};if _efaa ==1{_fbgcf .SdHuffAggInstanceSelection =true ;};_efaa ,_aebe =_fbgcf ._ecdbb .ReadBit ();if _aebe !=nil {return _aebe ;};_fbgcf .SdHuffBMSizeSelection =int8 (_efaa );_bcfbb ,_aebe =_fbgcf ._ecdbb .ReadBits (2);
if _aebe !=nil {return _aebe ;};_fbgcf .SdHuffDecodeWidthSelection =int8 (_bcfbb &0xf);_bcfbb ,_aebe =_fbgcf ._ecdbb .ReadBits (2);if _aebe !=nil {return _aebe ;};_fbgcf .SdHuffDecodeHeightSelection =int8 (_bcfbb &0xf);_efaa ,_aebe =_fbgcf ._ecdbb .ReadBit ();
if _aebe !=nil {return _aebe ;};if _efaa ==1{_fbgcf .UseRefinementAggregation =true ;};_efaa ,_aebe =_fbgcf ._ecdbb .ReadBit ();if _aebe !=nil {return _aebe ;};if _efaa ==1{_fbgcf .IsHuffmanEncoded =true ;};return nil ;};func (_cfgc *TextRegion )blit (_ggbf *_f .Bitmap ,_fcgb int64 )error {if _cfgc .IsTransposed ==0&&(_cfgc .ReferenceCorner ==2||_cfgc .ReferenceCorner ==3){_cfgc ._abdc +=int64 (_ggbf .Width -1);
}else if _cfgc .IsTransposed ==1&&(_cfgc .ReferenceCorner ==0||_cfgc .ReferenceCorner ==2){_cfgc ._abdc +=int64 (_ggbf .Height -1);};_dedb :=_cfgc ._abdc ;if _cfgc .IsTransposed ==1{_dedb ,_fcgb =_fcgb ,_dedb ;};switch _cfgc .ReferenceCorner {case 0:_fcgb -=int64 (_ggbf .Height -1);
case 2:_fcgb -=int64 (_ggbf .Height -1);_dedb -=int64 (_ggbf .Width -1);case 3:_dedb -=int64 (_ggbf .Width -1);};_cegg :=_f .Blit (_ggbf ,_cfgc .RegionBitmap ,int (_dedb ),int (_fcgb ),_cfgc .CombinationOperator );if _cegg !=nil {return _cegg ;};if _cfgc .IsTransposed ==0&&(_cfgc .ReferenceCorner ==0||_cfgc .ReferenceCorner ==1){_cfgc ._abdc +=int64 (_ggbf .Width -1);
};if _cfgc .IsTransposed ==1&&(_cfgc .ReferenceCorner ==1||_cfgc .ReferenceCorner ==3){_cfgc ._abdc +=int64 (_ggbf .Height -1);};return nil ;};func NewHeader (d Documenter ,r *_af .Reader ,offset int64 ,organizationType OrganizationType )(*Header ,error ){_fdcf :=&Header {Reader :r };
if _agb :=_fdcf .parse (d ,r ,offset ,organizationType );_agb !=nil {return nil ,_gf .Wrap (_agb ,"\u004ee\u0077\u0048\u0065\u0061\u0064\u0065r","");};return _fdcf ,nil ;};func (_eedf *PageInformationSegment )readCombinationOperator ()error {_bbaa ,_edabd :=_eedf ._fccb .ReadBits (2);
if _edabd !=nil {return _edabd ;};_eedf ._egeb =_f .CombinationOperator (int (_bbaa ));return nil ;};func (_dgfc *Header )readHeaderLength (_dgff *_af .Reader ,_gcc int64 ){_dgfc .HeaderLength =_dgff .AbsolutePosition ()-_gcc ;};