//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package document ;import (_g "encoding/binary";_b "fmt";_c "github.com/unidoc/unipdf/v3/common";_eg "github.com/unidoc/unipdf/v3/internal/bitwise";_bgf "github.com/unidoc/unipdf/v3/internal/jbig2/basic";_ec "github.com/unidoc/unipdf/v3/internal/jbig2/bitmap";
_dc "github.com/unidoc/unipdf/v3/internal/jbig2/document/segments";_d "github.com/unidoc/unipdf/v3/internal/jbig2/encoder/classer";_ac "github.com/unidoc/unipdf/v3/internal/jbig2/errors";_bg "io";_ea "math";_a "runtime/debug";);func (_agf *Document )reachedEOF (_bgecg int64 )(bool ,error ){const _gcee ="\u0072\u0065\u0061\u0063\u0068\u0065\u0064\u0045\u004f\u0046";
_ ,_ed :=_agf .InputStream .Seek (_bgecg ,_bg .SeekStart );if _ed !=nil {_c .Log .Debug ("\u0072\u0065\u0061c\u0068\u0065\u0064\u0045\u004f\u0046\u0020\u002d\u0020\u0064\u002e\u0049\u006e\u0070\u0075\u0074\u0053\u0074\u0072\u0065\u0061\u006d\u002e\u0053\u0065\u0065\u006b\u0020\u0066a\u0069\u006c\u0065\u0064\u003a\u0020\u0025\u0076",_ed );
return false ,_ac .Wrap (_ed ,_gcee ,"\u0069n\u0070\u0075\u0074\u0020\u0073\u0074\u0072\u0065\u0061\u006d\u0020s\u0065\u0065\u006b\u0020\u0066\u0061\u0069\u006c\u0065\u0064");};_ ,_ed =_agf .InputStream .ReadBits (32);if _ed ==_bg .EOF {return true ,nil ;
}else if _ed !=nil {return false ,_ac .Wrap (_ed ,_gcee ,"");};return false ,nil ;};func (_aefga *Globals )GetSymbolDictionary ()(*_dc .Header ,error ){const _aac ="G\u006c\u006f\u0062\u0061\u006c\u0073.\u0047\u0065\u0074\u0053\u0079\u006d\u0062\u006f\u006cD\u0069\u0063\u0074i\u006fn\u0061\u0072\u0079";
if _aefga ==nil {return nil ,_ac .Error (_aac ,"\u0067\u006c\u006f\u0062al\u0073\u0020\u006e\u006f\u0074\u0020\u0064\u0065\u0066\u0069\u006e\u0065\u0064");};if len (_aefga .Segments )==0{return nil ,_ac .Error (_aac ,"\u0067\u006c\u006f\u0062\u0061\u006c\u0073\u0020\u0061\u0072\u0065\u0020e\u006d\u0070\u0074\u0079");
};for _ ,_ecb :=range _aefga .Segments {if _ecb .Type ==_dc .TSymbolDictionary {return _ecb ,nil ;};};return nil ,_ac .Error (_aac ,"\u0067\u006c\u006fba\u006c\u0020\u0073\u0079\u006d\u0062\u006f\u006c\u0020d\u0069c\u0074i\u006fn\u0061\u0072\u0079\u0020\u006e\u006f\u0074\u0020\u0066\u006f\u0075\u006e\u0064");
};func (_fdff *Page )AddGenericRegion (bm *_ec .Bitmap ,xloc ,yloc ,template int ,tp _dc .Type ,duplicateLineRemoval bool )error {const _cbaa ="P\u0061\u0067\u0065\u002eAd\u0064G\u0065\u006e\u0065\u0072\u0069c\u0052\u0065\u0067\u0069\u006f\u006e";_cdc :=&_dc .GenericRegion {};
if _bcea :=_cdc .InitEncode (bm ,xloc ,yloc ,template ,duplicateLineRemoval );_bcea !=nil {return _ac .Wrap (_bcea ,_cbaa ,"");};_faa :=&_dc .Header {Type :_dc .TImmediateGenericRegion ,PageAssociation :_fdff .PageNumber ,SegmentData :_cdc };_fdff .Segments =append (_fdff .Segments ,_faa );
return nil ;};func (_fad *Document )GetPage (pageNumber int )(_dc .Pager ,error ){const _dd ="\u0044\u006fc\u0075\u006d\u0065n\u0074\u002e\u0047\u0065\u0074\u0050\u0061\u0067\u0065";if pageNumber < 0{_c .Log .Debug ("\u004a\u0042\u0049\u00472\u0020\u0050\u0061\u0067\u0065\u0020\u002d\u0020\u0047e\u0074\u0050\u0061\u0067\u0065\u003a\u0020\u0025\u0064\u002e\u0020\u0050\u0061\u0067\u0065\u0020\u0063\u0061n\u006e\u006f\u0074\u0020\u0062e\u0020\u006c\u006f\u0077\u0065\u0072\u0020\u0074\u0068\u0061\u006e\u0020\u0030\u002e\u0020\u0025\u0073",pageNumber ,_a .Stack ());
return nil ,_ac .Errorf (_dd ,"\u0069\u006e\u0076\u0061l\u0069\u0064\u0020\u006a\u0062\u0069\u0067\u0032\u0020d\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u0020\u002d\u0020\u0070\u0072\u006f\u0076\u0069\u0064\u0065\u0064 \u0069\u006e\u0076\u0061\u006ci\u0064\u0020\u0070\u0061\u0067\u0065\u0020\u006e\u0075\u006d\u0062\u0065\u0072\u003a\u0020\u0025\u0064",pageNumber );
};if pageNumber > len (_fad .Pages ){_c .Log .Debug ("\u0050\u0061\u0067\u0065 n\u006f\u0074\u0020\u0066\u006f\u0075\u006e\u0064\u003a\u0020\u0025\u0064\u002e\u0020%\u0073",pageNumber ,_a .Stack ());return nil ,_ac .Error (_dd ,"\u0069\u006e\u0076\u0061\u006c\u0069\u0064\u0020\u006a\u0062\u0069\u0067\u0032 \u0064\u006f\u0063\u0075\u006d\u0065n\u0074\u0020\u002d\u0020\u0070\u0061\u0067\u0065\u0020\u006e\u006f\u0074\u0020f\u006f\u0075\u006e\u0064");
};_def ,_ffcc :=_fad .Pages [pageNumber ];if !_ffcc {_c .Log .Debug ("\u0050\u0061\u0067\u0065 n\u006f\u0074\u0020\u0066\u006f\u0075\u006e\u0064\u003a\u0020\u0025\u0064\u002e\u0020%\u0073",pageNumber ,_a .Stack ());return nil ,_ac .Errorf (_dd ,"\u0069\u006e\u0076\u0061\u006c\u0069\u0064\u0020\u006a\u0062\u0069\u0067\u0032 \u0064\u006f\u0063\u0075\u006d\u0065n\u0074\u0020\u002d\u0020\u0070\u0061\u0067\u0065\u0020\u006e\u006f\u0074\u0020f\u006f\u0075\u006e\u0064");
};return _def ,nil ;};func (_fgec *Globals )GetSegment (segmentNumber int )(*_dc .Header ,error ){const _cdb ="\u0047l\u006fb\u0061\u006c\u0073\u002e\u0047e\u0074\u0053e\u0067\u006d\u0065\u006e\u0074";if _fgec ==nil {return nil ,_ac .Error (_cdb ,"\u0067\u006c\u006f\u0062al\u0073\u0020\u006e\u006f\u0074\u0020\u0064\u0065\u0066\u0069\u006e\u0065\u0064");
};if len (_fgec .Segments )==0{return nil ,_ac .Error (_cdb ,"\u0067\u006c\u006f\u0062\u0061\u006c\u0073\u0020\u0061\u0072\u0065\u0020e\u006d\u0070\u0074\u0079");};var _cde *_dc .Header ;for _ ,_cde =range _fgec .Segments {if _cde .SegmentNumber ==uint32 (segmentNumber ){break ;
};};if _cde ==nil {return nil ,_ac .Error (_cdb ,"\u0073\u0065\u0067\u006d\u0065\u006e\u0074\u0020\u006e\u006f\u0074\u0020f\u006f\u0075\u006e\u0064");};return _cde ,nil ;};func (_gce *Document )Encode ()(_geb []byte ,_be error ){const _ffb ="\u0044o\u0063u\u006d\u0065\u006e\u0074\u002e\u0045\u006e\u0063\u006f\u0064\u0065";
var _dbb ,_adg int ;if _gce .FullHeaders {if _dbb ,_be =_gce .encodeFileHeader (_gce ._dg );_be !=nil {return nil ,_ac .Wrap (_be ,_ffb ,"");};};var (_fb bool ;_gbd *_dc .Header ;_cfc *Page ;);if _be =_gce .completeClassifiedPages ();_be !=nil {return nil ,_ac .Wrap (_be ,_ffb ,"");
};if _be =_gce .produceClassifiedPages ();_be !=nil {return nil ,_ac .Wrap (_be ,_ffb ,"");};if _gce .GlobalSegments !=nil {for _ ,_gbd =range _gce .GlobalSegments .Segments {if _be =_gce .encodeSegment (_gbd ,&_dbb );_be !=nil {return nil ,_ac .Wrap (_be ,_ffb ,"");
};};};for _ffc :=1;_ffc <=int (_gce .NumberOfPages );_ffc ++{if _cfc ,_fb =_gce .Pages [_ffc ];!_fb {return nil ,_ac .Errorf (_ffb ,"p\u0061g\u0065\u003a\u0020\u0027\u0025\u0064\u0027\u0020n\u006f\u0074\u0020\u0066ou\u006e\u0064",_ffc );};for _ ,_gbd =range _cfc .Segments {if _be =_gce .encodeSegment (_gbd ,&_dbb );
_be !=nil {return nil ,_ac .Wrap (_be ,_ffb ,"");};};};if _gce .FullHeaders {if _adg ,_be =_gce .encodeEOFHeader (_gce ._dg );_be !=nil {return nil ,_ac .Wrap (_be ,_ffb ,"");};_dbb +=_adg ;};_geb =_gce ._dg .Data ();if len (_geb )!=_dbb {_c .Log .Debug ("\u0042\u0079\u0074\u0065\u0073 \u0077\u0072\u0069\u0074\u0074\u0065\u006e \u0028\u006e\u0029\u003a\u0020\u0027\u0025\u0064\u0027\u0020\u0069\u0073\u0020\u006e\u006f\u0074\u0020\u0065\u0071\u0075\u0061\u006c\u0020\u0074\u006f\u0020\u0074\u0068\u0065\u0020\u006c\u0065\u006e\u0067\u0074\u0068\u0020\u006f\u0066\u0020t\u0068\u0065\u0020\u0064\u0061\u0074\u0061\u0020\u0065\u006e\u0063\u006fd\u0065\u0064\u003a\u0020\u0027\u0025d\u0027",_dbb ,len (_geb ));
};return _geb ,nil ;};func (_gad *Page )createStripedPage (_afab *_dc .PageInformationSegment )error {const _gca ="\u0063\u0072\u0065\u0061\u0074\u0065\u0053\u0074\u0072\u0069\u0070\u0065d\u0050\u0061\u0067\u0065";_bfe ,_ebdg :=_gad .collectPageStripes ();
if _ebdg !=nil {return _ac .Wrap (_ebdg ,_gca ,"");};var _bec int ;for _ ,_edgb :=range _bfe {if _gcd ,_bgd :=_edgb .(*_dc .EndOfStripe );_bgd {_bec =_gcd .LineNumber ()+1;}else {_eca :=_edgb .(_dc .Regioner );_aacd :=_eca .GetRegionInfo ();_gffb :=_gad .getCombinationOperator (_afab ,_aacd .CombinaionOperator );
_eegd ,_cbf :=_eca .GetRegionBitmap ();if _cbf !=nil {return _ac .Wrap (_cbf ,_gca ,"");};_cbf =_ec .Blit (_eegd ,_gad .Bitmap ,int (_aacd .XLocation ),_bec ,_gffb );if _cbf !=nil {return _ac .Wrap (_cbf ,_gca ,"");};};};return nil ;};func (_bfg *Page )lastSegmentNumber ()(_edd uint32 ,_geba error ){const _abgg ="\u006c\u0061\u0073\u0074\u0053\u0065\u0067\u006d\u0065\u006e\u0074\u004eu\u006d\u0062\u0065\u0072";
if len (_bfg .Segments )==0{return _edd ,_ac .Errorf (_abgg ,"\u006e\u006f\u0020se\u0067\u006d\u0065\u006e\u0074\u0073\u0020\u0066\u006fu\u006ed\u0020i\u006e \u0074\u0068\u0065\u0020\u0070\u0061\u0067\u0065\u0020\u0027\u0025\u0064\u0027",_bfg .PageNumber );
};return _bfg .Segments [len (_bfg .Segments )-1].SegmentNumber ,nil ;};func (_ecd *Globals )GetSegmentByIndex (index int )(*_dc .Header ,error ){const _bcd ="\u0047l\u006f\u0062\u0061\u006cs\u002e\u0047\u0065\u0074\u0053e\u0067m\u0065n\u0074\u0042\u0079\u0049\u006e\u0064\u0065x";
if _ecd ==nil {return nil ,_ac .Error (_bcd ,"\u0067\u006c\u006f\u0062al\u0073\u0020\u006e\u006f\u0074\u0020\u0064\u0065\u0066\u0069\u006e\u0065\u0064");};if len (_ecd .Segments )==0{return nil ,_ac .Error (_bcd ,"\u0067\u006c\u006f\u0062\u0061\u006c\u0073\u0020\u0061\u0072\u0065\u0020e\u006d\u0070\u0074\u0079");
};if index > len (_ecd .Segments )-1{return nil ,_ac .Error (_bcd ,"\u0069n\u0064e\u0078\u0020\u006f\u0075\u0074 \u006f\u0066 \u0072\u0061\u006e\u0067\u0065");};return _ecd .Segments [index ],nil ;};var _gg =[]byte {0x97,0x4A,0x42,0x32,0x0D,0x0A,0x1A,0x0A};
func (_bgec *Document )isFileHeaderPresent ()(bool ,error ){_bgec .InputStream .Mark ();for _ ,_ab :=range _gg {_fgg ,_dada :=_bgec .InputStream .ReadByte ();if _dada !=nil {return false ,_dada ;};if _ab !=_fgg {_bgec .InputStream .Reset ();return false ,nil ;
};};_bgec .InputStream .Reset ();return true ,nil ;};func (_dcc *Page )createPage (_cag *_dc .PageInformationSegment )error {var _ffbg error ;if !_cag .IsStripe ||_cag .PageBMHeight !=-1{_ffbg =_dcc .createNormalPage (_cag );}else {_ffbg =_dcc .createStripedPage (_cag );
};return _ffbg ;};func (_effg *Page )getHeight ()(int ,error ){const _egeg ="\u0067e\u0074\u0048\u0065\u0069\u0067\u0068t";if _effg .FinalHeight !=0{return _effg .FinalHeight ,nil ;};_adb :=_effg .getPageInformationSegment ();if _adb ==nil {return 0,_ac .Error (_egeg ,"n\u0069l\u0020\u0070\u0061\u0067\u0065\u0020\u0069\u006ef\u006f\u0072\u006d\u0061ti\u006f\u006e");
};_egacc ,_bdc :=_adb .GetSegmentData ();if _bdc !=nil {return 0,_ac .Wrap (_bdc ,_egeg ,"");};_ddda ,_cdcb :=_egacc .(*_dc .PageInformationSegment );if !_cdcb {return 0,_ac .Errorf (_egeg ,"\u0070\u0061\u0067\u0065\u0020\u0069n\u0066\u006f\u0072\u006d\u0061\u0074\u0069\u006f\u006e\u0020\u0073\u0065\u0067\u006d\u0065\u006e\u0074\u0020\u0069\u0073 \u006f\u0066\u0020\u0069\u006e\u0076\u0061\u006c\u0069\u0064\u0020\u0074\u0079\u0070e\u003a \u0027\u0025\u0054\u0027",_egacc );
};if _ddda .PageBMHeight ==_ea .MaxInt32 {_ ,_bdc =_effg .GetBitmap ();if _bdc !=nil {return 0,_ac .Wrap (_bdc ,_egeg ,"");};}else {_effg .FinalHeight =_ddda .PageBMHeight ;};return _effg .FinalHeight ,nil ;};func (_edg *Page )GetBitmap ()(_aacc *_ec .Bitmap ,_ccad error ){_c .Log .Trace (_b .Sprintf ("\u005b\u0050\u0041G\u0045\u005d\u005b\u0023%\u0064\u005d\u0020\u0047\u0065\u0074\u0042i\u0074\u006d\u0061\u0070\u0020\u0062\u0065\u0067\u0069\u006e\u0073\u002e\u002e\u002e",_edg .PageNumber ));
defer func (){if _ccad !=nil {_c .Log .Trace (_b .Sprintf ("\u005b\u0050\u0041\u0047\u0045\u005d\u005b\u0023\u0025\u0064\u005d\u0020\u0047\u0065\u0074B\u0069t\u006d\u0061\u0070\u0020\u0066\u0061\u0069\u006c\u0065\u0064\u002e\u0020\u0025\u0076",_edg .PageNumber ,_ccad ));
}else {_c .Log .Trace (_b .Sprintf ("\u005b\u0050\u0041\u0047\u0045\u005d\u005b\u0023\u0025\u0064]\u0020\u0047\u0065\u0074\u0042\u0069\u0074m\u0061\u0070\u0020\u0066\u0069\u006e\u0069\u0073\u0068\u0065\u0064",_edg .PageNumber ));};}();if _edg .Bitmap !=nil {return _edg .Bitmap ,nil ;
};_ccad =_edg .composePageBitmap ();if _ccad !=nil {return nil ,_ccad ;};return _edg .Bitmap ,nil ;};func (_abg *Document )nextPageNumber ()uint32 {_abg .NumberOfPages ++;return _abg .NumberOfPages };func (_acdb *Page )String ()string {return _b .Sprintf ("\u0050\u0061\u0067\u0065\u0020\u0023\u0025\u0064",_acdb .PageNumber );
};func (_ccf *Document )encodeEOFHeader (_bc _eg .BinaryWriter )(_dad int ,_eb error ){_fc :=&_dc .Header {SegmentNumber :_ccf .nextSegmentNumber (),Type :_dc .TEndOfFile };if _dad ,_eb =_fc .Encode (_bc );_eb !=nil {return 0,_ac .Wrap (_eb ,"\u0065n\u0063o\u0064\u0065\u0045\u004f\u0046\u0048\u0065\u0061\u0064\u0065\u0072","");
};return _dad ,nil ;};func (_cge *Page )AddPageInformationSegment (){_add :=&_dc .PageInformationSegment {PageBMWidth :_cge .FinalWidth ,PageBMHeight :_cge .FinalHeight ,ResolutionX :_cge .ResolutionX ,ResolutionY :_cge .ResolutionY ,IsLossless :_cge .IsLossless };
if _cge .BlackIsOne {_add .DefaultPixelValue =uint8 (0x1);};_beb :=&_dc .Header {PageAssociation :_cge .PageNumber ,SegmentDataLength :uint64 (_add .Size ()),SegmentData :_add ,Type :_dc .TPageInformation };_cge .Segments =append (_cge .Segments ,_beb );
};func (_abga *Page )GetResolutionX ()(int ,error ){return _abga .getResolutionX ()};func (_gef *Page )GetHeight ()(int ,error ){return _gef .getHeight ()};type Page struct{Segments []*_dc .Header ;PageNumber int ;Bitmap *_ec .Bitmap ;FinalHeight int ;
FinalWidth int ;ResolutionX int ;ResolutionY int ;IsLossless bool ;Document *Document ;FirstSegmentNumber int ;EncodingMethod EncodingMethod ;BlackIsOne bool ;};func DecodeDocument (input *_eg .Reader ,globals *Globals )(*Document ,error ){return _bdd (input ,globals );
};func (_gcb *Page )AddEndOfPageSegment (){_ecbd :=&_dc .Header {Type :_dc .TEndOfPage ,PageAssociation :_gcb .PageNumber };_gcb .Segments =append (_gcb .Segments ,_ecbd );};func (_fgef *Globals )AddSegment (segment *_dc .Header ){_fgef .Segments =append (_fgef .Segments ,segment );
};func (_bef *Document )mapData ()error {const _dcg ="\u006da\u0070\u0044\u0061\u0074\u0061";var (_eeae []*_dc .Header ;_dafb int64 ;_eaab _dc .Type ;);_fge ,_bce :=_bef .isFileHeaderPresent ();if _bce !=nil {return _ac .Wrap (_bce ,_dcg ,"");};if _fge {if _bce =_bef .parseFileHeader ();
_bce !=nil {return _ac .Wrap (_bce ,_dcg ,"");};_dafb +=int64 (_bef ._bge );_bef .FullHeaders =true ;};var (_dda *Page ;_ca bool ;);for _eaab !=51&&!_ca {_caf ,_abb :=_dc .NewHeader (_bef ,_bef .InputStream ,_dafb ,_bef .OrganizationType );if _abb !=nil {return _ac .Wrap (_abb ,_dcg ,"");
};_c .Log .Trace ("\u0044\u0065c\u006f\u0064\u0069\u006eg\u0020\u0073e\u0067\u006d\u0065\u006e\u0074\u0020\u006e\u0075m\u0062\u0065\u0072\u003a\u0020\u0025\u0064\u002c\u0020\u0054\u0079\u0070e\u003a\u0020\u0025\u0073",_caf .SegmentNumber ,_caf .Type );
_eaab =_caf .Type ;if _eaab !=_dc .TEndOfFile {if _caf .PageAssociation !=0{_dda =_bef .Pages [_caf .PageAssociation ];if _dda ==nil {_dda =_cfgb (_bef ,_caf .PageAssociation );_bef .Pages [_caf .PageAssociation ]=_dda ;if _bef .NumberOfPagesUnknown {_bef .NumberOfPages ++;
};};_dda .Segments =append (_dda .Segments ,_caf );}else {_bef .GlobalSegments .AddSegment (_caf );};};_eeae =append (_eeae ,_caf );_dafb =_bef .InputStream .AbsolutePosition ();if _bef .OrganizationType ==_dc .OSequential {_dafb +=int64 (_caf .SegmentDataLength );
};_ca ,_abb =_bef .reachedEOF (_dafb );if _abb !=nil {_c .Log .Debug ("\u006a\u0062\u0069\u0067\u0032 \u0064\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u0020\u0072\u0065\u0061\u0063h\u0065\u0064\u0020\u0045\u004f\u0046\u0020\u0077\u0069\u0074\u0068\u0020\u0065\u0072\u0072\u006f\u0072\u003a\u0020\u0025\u0076",_abb );
return _ac .Wrap (_abb ,_dcg ,"");};};_bef .determineRandomDataOffsets (_eeae ,uint64 (_dafb ));return nil ;};func (_bbg *Page )getPageInformationSegment ()*_dc .Header {for _ ,_dafe :=range _bbg .Segments {if _dafe .Type ==_dc .TPageInformation {return _dafe ;
};};_c .Log .Debug ("\u0050\u0061\u0067\u0065\u0020\u0069\u006e\u0066o\u0072\u006d\u0061ti\u006f\u006e\u0020\u0073\u0065\u0067m\u0065\u006e\u0074\u0020\u006e\u006f\u0074\u0020\u0066\u006f\u0075\u006e\u0064\u0020\u0066o\u0072\u0020\u0070\u0061\u0067\u0065\u003a\u0020%\u0073\u002e",_bbg );
return nil ;};type EncodingMethod int ;func (_ag *Document )AddClassifiedPage (bm *_ec .Bitmap ,method _d .Method )(_cc error ){const _agc ="\u0044\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u002e\u0041\u0064d\u0043\u006c\u0061\u0073\u0073\u0069\u0066\u0069\u0065\u0064P\u0061\u0067\u0065";
if !_ag .FullHeaders &&_ag .NumberOfPages !=0{return _ac .Error (_agc ,"\u0064\u006f\u0063\u0075\u006de\u006e\u0074\u0020\u0061\u006c\u0072\u0065a\u0064\u0079\u0020\u0063\u006f\u006e\u0074\u0061\u0069\u006e\u0073\u0020\u0070\u0061\u0067\u0065\u002e\u0020\u0046\u0069\u006c\u0065\u004d\u006f\u0064\u0065\u0020\u0064\u0069\u0073\u0061\u006c\u006c\u006f\u0077\u0073\u0020\u0061\u0064\u0064i\u006e\u0067\u0020\u006d\u006f\u0072\u0065\u0020\u0074\u0068\u0061\u006e \u006f\u006e\u0065\u0020\u0070\u0061g\u0065");
};if _ag .Classer ==nil {if _ag .Classer ,_cc =_d .Init (_d .DefaultSettings ());_cc !=nil {return _ac .Wrap (_cc ,_agc ,"");};};_ba :=int (_ag .nextPageNumber ());_dgf :=&Page {Segments :[]*_dc .Header {},Bitmap :bm ,Document :_ag ,FinalHeight :bm .Height ,FinalWidth :bm .Width ,PageNumber :_ba };
_ag .Pages [_ba ]=_dgf ;switch method {case _d .RankHaus :_dgf .EncodingMethod =RankHausEM ;case _d .Correlation :_dgf .EncodingMethod =CorrelationEM ;};_dgf .AddPageInformationSegment ();if _cc =_ag .Classer .AddPage (bm ,_ba ,method );_cc !=nil {return _ac .Wrap (_cc ,_agc ,"");
};if _ag .FullHeaders {_dgf .AddEndOfPageSegment ();};return nil ;};func _cfgb (_fbb *Document ,_fdfc int )*Page {return &Page {Document :_fbb ,PageNumber :_fdfc ,Segments :[]*_dc .Header {}};};func InitEncodeDocument (fullHeaders bool )*Document {return &Document {FullHeaders :fullHeaders ,_dg :_eg .BufferedMSB (),Pages :map[int ]*Page {},_de :map[int ][]int {},_bd :map[int ]int {},_da :map[int ][]int {}};
};func (_fgge *Page )createNormalPage (_acg *_dc .PageInformationSegment )error {const _egfg ="\u0063\u0072e\u0061\u0074\u0065N\u006f\u0072\u006d\u0061\u006c\u0050\u0061\u0067\u0065";_fgge .Bitmap =_ec .New (_acg .PageBMWidth ,_acg .PageBMHeight );if _acg .DefaultPixelValue !=0{_fgge .Bitmap .SetDefaultPixel ();
};for _ ,_dac :=range _fgge .Segments {switch _dac .Type {case 6,7,22,23,38,39,42,43:_c .Log .Trace ("\u0047\u0065\u0074\u0074in\u0067\u0020\u0053\u0065\u0067\u006d\u0065\u006e\u0074\u003a\u0020\u0025\u0064",_dac .SegmentNumber );_fce ,_df :=_dac .GetSegmentData ();
if _df !=nil {return _df ;};_faff ,_degb :=_fce .(_dc .Regioner );if !_degb {_c .Log .Debug ("\u0053\u0065g\u006d\u0065\u006e\u0074\u003a\u0020\u0025\u0054\u0020\u0069\u0073\u0020\u006e\u006f\u0074\u0020\u0061\u0020\u0052\u0065\u0067\u0069on\u0065\u0072",_fce );
return _ac .Errorf (_egfg ,"i\u006e\u0076\u0061\u006c\u0069\u0064\u0020\u006a\u0062i\u0067\u0032\u0020\u0073\u0065\u0067\u006den\u0074\u0020\u0074\u0079p\u0065\u0020\u002d\u0020\u006e\u006f\u0074\u0020\u0061 R\u0065\u0067i\u006f\u006e\u0065\u0072\u003a\u0020\u0025\u0054",_fce );
};_afc ,_df :=_faff .GetRegionBitmap ();if _df !=nil {return _ac .Wrap (_df ,_egfg ,"");};if _fgge .fitsPage (_acg ,_afc ){_fgge .Bitmap =_afc ;}else {_aba :=_faff .GetRegionInfo ();_ffde :=_fgge .getCombinationOperator (_acg ,_aba .CombinaionOperator );
_df =_ec .Blit (_afc ,_fgge .Bitmap ,int (_aba .XLocation ),int (_aba .YLocation ),_ffde );if _df !=nil {return _ac .Wrap (_df ,_egfg ,"");};};};};return nil ;};const (GenericEM EncodingMethod =iota ;CorrelationEM ;RankHausEM ;);func (_ffd *Document )produceClassifiedPages ()(_aga error ){const _gb ="\u0070\u0072\u006f\u0064uc\u0065\u0043\u006c\u0061\u0073\u0073\u0069\u0066\u0069\u0065\u0064\u0050\u0061\u0067e\u0073";
if _ffd .Classer ==nil {return nil ;};var (_geae *Page ;_eed bool ;_ceb *_dc .Header ;);for _gbg :=1;_gbg <=int (_ffd .NumberOfPages );_gbg ++{if _geae ,_eed =_ffd .Pages [_gbg ];!_eed {return _ac .Errorf (_gb ,"p\u0061g\u0065\u003a\u0020\u0027\u0025\u0064\u0027\u0020n\u006f\u0074\u0020\u0066ou\u006e\u0064",_gbg );
};if _geae .EncodingMethod ==GenericEM {continue ;};if _ceb ==nil {if _ceb ,_aga =_ffd .GlobalSegments .GetSymbolDictionary ();_aga !=nil {return _ac .Wrap (_aga ,_gb ,"");};};if _aga =_ffd .produceClassifiedPage (_geae ,_ceb );_aga !=nil {return _ac .Wrapf (_aga ,_gb ,"\u0070\u0061\u0067\u0065\u003a\u0020\u0027\u0025\u0064\u0027",_gbg );
};};return nil ;};func (_faf *Document )encodeSegment (_gfd *_dc .Header ,_acb *int )error {const _aefg ="\u0065\u006e\u0063\u006f\u0064\u0065\u0053\u0065\u0067\u006d\u0065\u006e\u0074";_gfd .SegmentNumber =_faf .nextSegmentNumber ();_aaf ,_ffeb :=_gfd .Encode (_faf ._dg );
if _ffeb !=nil {return _ac .Wrapf (_ffeb ,_aefg ,"\u0073\u0065\u0067\u006d\u0065\u006e\u0074\u003a\u0020\u0027\u0025\u0064\u0027",_gfd .SegmentNumber );};*_acb +=_aaf ;return nil ;};func (_gcg *Page )composePageBitmap ()error {const _adgd ="\u0063\u006f\u006d\u0070\u006f\u0073\u0065\u0050\u0061\u0067\u0065\u0042i\u0074\u006d\u0061\u0070";
if _gcg .PageNumber ==0{return nil ;};_cbe :=_gcg .getPageInformationSegment ();if _cbe ==nil {return _ac .Error (_adgd ,"\u0070\u0061\u0067e \u0069\u006e\u0066\u006f\u0072\u006d\u0061\u0074\u0069o\u006e \u0073e\u0067m\u0065\u006e\u0074\u0020\u006e\u006f\u0074\u0020\u0066\u006f\u0075\u006e\u0064");
};_ecg ,_gagf :=_cbe .GetSegmentData ();if _gagf !=nil {return _gagf ;};_dadb ,_egf :=_ecg .(*_dc .PageInformationSegment );if !_egf {return _ac .Error (_adgd ,"\u0070\u0061\u0067\u0065\u0020\u0069\u006ef\u006f\u0072\u006da\u0074\u0069\u006f\u006e \u0073\u0065\u0067\u006d\u0065\u006e\u0074\u0020\u0069\u0073\u0020\u006f\u0066\u0020\u0069\u006e\u0076\u0061\u006c\u0069\u0064\u0020\u0074\u0079\u0070\u0065");
};if _gagf =_gcg .createPage (_dadb );_gagf !=nil {return _ac .Wrap (_gagf ,_adgd ,"");};_gcg .clearSegmentData ();return nil ;};func (_efc *Page )fitsPage (_egac *_dc .PageInformationSegment ,_ebg *_ec .Bitmap )bool {return _efc .countRegions ()==1&&_egac .DefaultPixelValue ==0&&_egac .PageBMWidth ==_ebg .Width &&_egac .PageBMHeight ==_ebg .Height ;
};func (_fe *Document )AddGenericPage (bm *_ec .Bitmap ,duplicateLineRemoval bool )(_gf error ){const _ga ="\u0044\u006f\u0063um\u0065\u006e\u0074\u002e\u0041\u0064\u0064\u0047\u0065\u006e\u0065\u0072\u0069\u0063\u0050\u0061\u0067\u0065";if !_fe .FullHeaders &&_fe .NumberOfPages !=0{return _ac .Error (_ga ,"\u0064\u006f\u0063\u0075\u006de\u006e\u0074\u0020\u0061\u006c\u0072\u0065a\u0064\u0079\u0020\u0063\u006f\u006e\u0074\u0061\u0069\u006e\u0073\u0020\u0070\u0061\u0067\u0065\u002e\u0020\u0046\u0069\u006c\u0065\u004d\u006f\u0064\u0065\u0020\u0064\u0069\u0073\u0061\u006c\u006c\u006f\u0077\u0073\u0020\u0061\u0064\u0064i\u006e\u0067\u0020\u006d\u006f\u0072\u0065\u0020\u0074\u0068\u0061\u006e \u006f\u006e\u0065\u0020\u0070\u0061g\u0065");
};_ee :=&Page {Segments :[]*_dc .Header {},Bitmap :bm ,Document :_fe ,FinalHeight :bm .Height ,FinalWidth :bm .Width ,IsLossless :true ,BlackIsOne :bm .Color ==_ec .Chocolate };_ee .PageNumber =int (_fe .nextPageNumber ());_fe .Pages [_ee .PageNumber ]=_ee ;
bm .InverseData ();_ee .AddPageInformationSegment ();if _gf =_ee .AddGenericRegion (bm ,0,0,0,_dc .TImmediateGenericRegion ,duplicateLineRemoval );_gf !=nil {return _ac .Wrap (_gf ,_ga ,"");};if _fe .FullHeaders {_ee .AddEndOfPageSegment ();};return nil ;
};func (_dacd *Page )collectPageStripes ()(_eda []_dc .Segmenter ,_fbd error ){const _abd ="\u0063o\u006cl\u0065\u0063\u0074\u0050\u0061g\u0065\u0053t\u0072\u0069\u0070\u0065\u0073";var _gcbb _dc .Segmenter ;for _ ,_gfcg :=range _dacd .Segments {switch _gfcg .Type {case 6,7,22,23,38,39,42,43:_gcbb ,_fbd =_gfcg .GetSegmentData ();
if _fbd !=nil {return nil ,_ac .Wrap (_fbd ,_abd ,"");};_eda =append (_eda ,_gcbb );case 50:_gcbb ,_fbd =_gfcg .GetSegmentData ();if _fbd !=nil {return nil ,_fbd ;};_eec ,_dff :=_gcbb .(*_dc .EndOfStripe );if !_dff {return nil ,_ac .Errorf (_abd ,"\u0045\u006e\u0064\u004f\u0066\u0053\u0074\u0072\u0069\u0070\u0065\u0020\u0069\u0073\u0020\u006e\u006f\u0074\u0020\u006f\u0066\u0020\u0076\u0061l\u0069\u0064\u0020\u0074\u0079p\u0065\u003a \u0027\u0025\u0054\u0027",_gcbb );
};_eda =append (_eda ,_eec );_dacd .FinalHeight =_eec .LineNumber ();};};return _eda ,nil ;};func (_eaf *Page )countRegions ()int {var _dag int ;for _ ,_cafa :=range _eaf .Segments {switch _cafa .Type {case 6,7,22,23,38,39,42,43:_dag ++;};};return _dag ;
};type Globals struct{Segments []*_dc .Header ;};func (_aef *Document )addSymbolDictionary (_db int ,_fdf *_ec .Bitmaps ,_bfc []int ,_ece map[int ]int ,_fg bool )(*_dc .Header ,error ){const _eeg ="\u0061\u0064\u0064\u0053ym\u0062\u006f\u006c\u0044\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079";
_feg :=&_dc .SymbolDictionary {};if _gd :=_feg .InitEncode (_fdf ,_bfc ,_ece ,_fg );_gd !=nil {return nil ,_gd ;};_ffdc :=&_dc .Header {Type :_dc .TSymbolDictionary ,PageAssociation :_db ,SegmentData :_feg };if _db ==0{if _aef .GlobalSegments ==nil {_aef .GlobalSegments =&Globals {};
};_aef .GlobalSegments .AddSegment (_ffdc );return _ffdc ,nil ;};_ad ,_cca :=_aef .Pages [_db ];if !_cca {return nil ,_ac .Errorf (_eeg ,"p\u0061g\u0065\u003a\u0020\u0027\u0025\u0064\u0027\u0020n\u006f\u0074\u0020\u0066ou\u006e\u0064",_db );};var (_ef int ;
_bgb *_dc .Header ;);for _ef ,_bgb =range _ad .Segments {if _bgb .Type ==_dc .TPageInformation {break ;};};_ef ++;_ad .Segments =append (_ad .Segments ,nil );copy (_ad .Segments [_ef +1:],_ad .Segments [_ef :]);_ad .Segments [_ef ]=_ffdc ;return _ffdc ,nil ;
};func (_fd *Document )completeClassifiedPages ()(_cd error ){const _ega ="\u0063\u006f\u006dpl\u0065\u0074\u0065\u0043\u006c\u0061\u0073\u0073\u0069\u0066\u0069\u0065\u0064\u0050\u0061\u0067\u0065\u0073";if _fd .Classer ==nil {return nil ;};_fd ._f =make ([]int ,_fd .Classer .UndilatedTemplates .Size ());
for _ff :=0;_ff < _fd .Classer .ClassIDs .Size ();_ff ++{_cf ,_ce :=_fd .Classer .ClassIDs .Get (_ff );if _ce !=nil {return _ac .Wrapf (_ce ,_ega ,"\u0063\u006c\u0061\u0073s \u0077\u0069\u0074\u0068\u0020\u0069\u0064\u003a\u0020\u0027\u0025\u0064\u0027",_ff );
};_fd ._f [_cf ]++;};var _cgg []int ;for _agg :=0;_agg < _fd .Classer .UndilatedTemplates .Size ();_agg ++{if _fd .NumberOfPages ==1||_fd ._f [_agg ]> 1{_cgg =append (_cgg ,_agg );};};var (_ge *Page ;_af bool ;);for _bf ,_eef :=range *_fd .Classer .ComponentPageNumbers {if _ge ,_af =_fd .Pages [_eef ];
!_af {return _ac .Errorf (_ega ,"p\u0061g\u0065\u003a\u0020\u0027\u0025\u0064\u0027\u0020n\u006f\u0074\u0020\u0066ou\u006e\u0064",_bf );};if _ge .EncodingMethod ==GenericEM {_c .Log .Error ("\u0047\u0065\u006e\u0065\u0072\u0069c\u0020\u0070\u0061g\u0065\u0020\u0077i\u0074\u0068\u0020\u006e\u0075\u006d\u0062\u0065\u0072\u003a \u0027\u0025\u0064\u0027\u0020ma\u0070\u0070\u0065\u0064\u0020\u0061\u0073\u0020\u0063\u006c\u0061\u0073\u0073\u0069\u0066\u0069\u0065\u0064\u0020\u0070\u0061\u0067\u0065",_bf );
continue ;};_fd ._da [_eef ]=append (_fd ._da [_eef ],_bf );_cgd ,_acd :=_fd .Classer .ClassIDs .Get (_bf );if _acd !=nil {return _ac .Wrapf (_acd ,_ega ,"\u006e\u006f\u0020\u0073uc\u0068\u0020\u0063\u006c\u0061\u0073\u0073\u0049\u0044\u003a\u0020\u0025\u0064",_bf );
};if _fd ._f [_cgd ]==1&&_fd .NumberOfPages !=1{_gea :=append (_fd ._de [_eef ],_cgd );_fd ._de [_eef ]=_gea ;};};if _cd =_fd .Classer .ComputeLLCorners ();_cd !=nil {return _ac .Wrap (_cd ,_ega ,"");};if _ ,_cd =_fd .addSymbolDictionary (0,_fd .Classer .UndilatedTemplates ,_cgg ,_fd ._bd ,false );
_cd !=nil {return _ac .Wrap (_cd ,_ega ,"");};return nil ;};func (_dea *Page )GetResolutionY ()(int ,error ){return _dea .getResolutionY ()};type Document struct{Pages map[int ]*Page ;NumberOfPagesUnknown bool ;NumberOfPages uint32 ;GBUseExtTemplate bool ;
InputStream *_eg .Reader ;GlobalSegments *Globals ;OrganizationType _dc .OrganizationType ;Classer *_d .Classer ;XRes ,YRes int ;FullHeaders bool ;CurrentSegmentNumber uint32 ;AverageTemplates *_ec .Bitmaps ;BaseIndexes []int ;Refinement bool ;RefineLevel int ;
_bge uint8 ;_dg *_eg .BufferedWriter ;EncodeGlobals bool ;_cg int ;_de map[int ][]int ;_da map[int ][]int ;_f []int ;_bd map[int ]int ;};func _dge (_cgda int )int {_agd :=0;_gfc :=(_cgda &(_cgda -1))==0;_cgda >>=1;for ;_cgda !=0;_cgda >>=1{_agd ++;};if _gfc {return _agd ;
};return _agd +1;};func (_ddac *Page )getResolutionX ()(int ,error ){const _cgdae ="\u0067\u0065\u0074\u0052\u0065\u0073\u006f\u006c\u0075t\u0069\u006f\u006e\u0058";if _ddac .ResolutionX !=0{return _ddac .ResolutionX ,nil ;};_egca :=_ddac .getPageInformationSegment ();
if _egca ==nil {return 0,_ac .Error (_cgdae ,"n\u0069l\u0020\u0070\u0061\u0067\u0065\u0020\u0069\u006ef\u006f\u0072\u006d\u0061ti\u006f\u006e");};_dega ,_fgee :=_egca .GetSegmentData ();if _fgee !=nil {return 0,_ac .Wrap (_fgee ,_cgdae ,"");};_cdcbg ,_egd :=_dega .(*_dc .PageInformationSegment );
if !_egd {return 0,_ac .Errorf (_cgdae ,"\u0070\u0061\u0067\u0065\u0020\u0069n\u0066\u006f\u0072\u006d\u0061\u0074\u0069\u006f\u006e\u0020\u0073\u0065\u0067\u006d\u0065\u006e\u0074\u0020\u0069\u0073 \u006f\u0066\u0020\u0069\u006e\u0076\u0061\u006c\u0069\u0064\u0020\u0074\u0079\u0070e\u003a \u0027\u0025\u0054\u0027",_dega );
};_ddac .ResolutionX =_cdcbg .ResolutionX ;return _ddac .ResolutionX ,nil ;};func (_fga *Document )nextSegmentNumber ()uint32 {_cfcg :=_fga .CurrentSegmentNumber ;_fga .CurrentSegmentNumber ++;return _cfcg ;};func (_abbg *Page )getWidth ()(int ,error ){const _abgaf ="\u0067\u0065\u0074\u0057\u0069\u0064\u0074\u0068";
if _abbg .FinalWidth !=0{return _abbg .FinalWidth ,nil ;};_bgde :=_abbg .getPageInformationSegment ();if _bgde ==nil {return 0,_ac .Error (_abgaf ,"n\u0069l\u0020\u0070\u0061\u0067\u0065\u0020\u0069\u006ef\u006f\u0072\u006d\u0061ti\u006f\u006e");};_bgecc ,_afcb :=_bgde .GetSegmentData ();
if _afcb !=nil {return 0,_ac .Wrap (_afcb ,_abgaf ,"");};_cbb ,_dfg :=_bgecc .(*_dc .PageInformationSegment );if !_dfg {return 0,_ac .Errorf (_abgaf ,"\u0070\u0061\u0067\u0065\u0020\u0069n\u0066\u006f\u0072\u006d\u0061\u0074\u0069\u006f\u006e\u0020\u0073\u0065\u0067\u006d\u0065\u006e\u0074\u0020\u0069\u0073 \u006f\u0066\u0020\u0069\u006e\u0076\u0061\u006c\u0069\u0064\u0020\u0074\u0079\u0070e\u003a \u0027\u0025\u0054\u0027",_bgecc );
};_abbg .FinalWidth =_cbb .PageBMWidth ;return _abbg .FinalWidth ,nil ;};func (_eea *Document )GetGlobalSegment (i int )(*_dc .Header ,error ){_fbg ,_cfa :=_eea .GlobalSegments .GetSegment (i );if _cfa !=nil {return nil ,_ac .Wrap (_cfa ,"\u0047\u0065t\u0047\u006c\u006fb\u0061\u006c\u0053\u0065\u0067\u006d\u0065\u006e\u0074","");
};return _fbg ,nil ;};func (_aca *Document )produceClassifiedPage (_gc *Page ,_ae *_dc .Header )(_acda error ){const _gbe ="p\u0072\u006f\u0064\u0075ce\u0043l\u0061\u0073\u0073\u0069\u0066i\u0065\u0064\u0050\u0061\u0067\u0065";var _bff map[int ]int ;_gab :=_aca ._cg ;
_ffe :=[]*_dc .Header {_ae };if len (_aca ._de [_gc .PageNumber ])> 0{_bff =map[int ]int {};_ccg ,_fec :=_aca .addSymbolDictionary (_gc .PageNumber ,_aca .Classer .UndilatedTemplates ,_aca ._de [_gc .PageNumber ],_bff ,false );if _fec !=nil {return _ac .Wrap (_fec ,_gbe ,"");
};_ffe =append (_ffe ,_ccg );_gab +=len (_aca ._de [_gc .PageNumber ]);};_eaa :=_aca ._da [_gc .PageNumber ];_c .Log .Debug ("P\u0061g\u0065\u003a\u0020\u0027\u0025\u0064\u0027\u0020c\u006f\u006d\u0070\u0073: \u0025\u0076",_gc .PageNumber ,_eaa );_gc .addTextRegionSegment (_ffe ,_aca ._bd ,_bff ,_aca ._da [_gc .PageNumber ],_aca .Classer .PtaLL ,_aca .Classer .UndilatedTemplates ,_aca .Classer .ClassIDs ,nil ,_dge (_gab ),len (_aca ._da [_gc .PageNumber ]));
return nil ;};func (_bde *Page )clearSegmentData (){for _dadd :=range _bde .Segments {_bde .Segments [_dadd ].CleanSegmentData ();};};func (_fed *Document )GetNumberOfPages ()(uint32 ,error ){if _fed .NumberOfPagesUnknown ||_fed .NumberOfPages ==0{if len (_fed .Pages )==0{if _daf :=_fed .mapData ();
_daf !=nil {return 0,_ac .Wrap (_daf ,"\u0044o\u0063\u0075\u006d\u0065n\u0074\u002e\u0047\u0065\u0074N\u0075m\u0062e\u0072\u004f\u0066\u0050\u0061\u0067\u0065s","");};};return uint32 (len (_fed .Pages )),nil ;};return _fed .NumberOfPages ,nil ;};func (_bbe *Document )encodeFileHeader (_cb _eg .BinaryWriter )(_ege int ,_fgc error ){const _ceg ="\u0065\u006ec\u006f\u0064\u0065F\u0069\u006c\u0065\u0048\u0065\u0061\u0064\u0065\u0072";
_ege ,_fgc =_cb .Write (_gg );if _fgc !=nil {return _ege ,_ac .Wrap (_fgc ,_ceg ,"\u0069\u0064");};if _fgc =_cb .WriteByte (0x01);_fgc !=nil {return _ege ,_ac .Wrap (_fgc ,_ceg ,"\u0066\u006c\u0061g\u0073");};_ege ++;_egc :=make ([]byte ,4);_g .BigEndian .PutUint32 (_egc ,_bbe .NumberOfPages );
_gebf ,_fgc :=_cb .Write (_egc );if _fgc !=nil {return _gebf ,_ac .Wrap (_fgc ,_ceg ,"p\u0061\u0067\u0065\u0020\u006e\u0075\u006d\u0062\u0065\u0072");};_ege +=_gebf ;return _ege ,nil ;};func (_gaf *Page )nextSegmentNumber ()uint32 {return _gaf .Document .nextSegmentNumber ()};
func (_bffc *Document )parseFileHeader ()error {const _caa ="\u0070a\u0072s\u0065\u0046\u0069\u006c\u0065\u0048\u0065\u0061\u0064\u0065\u0072";_ ,_fgeb :=_bffc .InputStream .Seek (8,_bg .SeekStart );if _fgeb !=nil {return _ac .Wrap (_fgeb ,_caa ,"\u0069\u0064");
};_ ,_fgeb =_bffc .InputStream .ReadBits (5);if _fgeb !=nil {return _ac .Wrap (_fgeb ,_caa ,"\u0072\u0065\u0073\u0065\u0072\u0076\u0065\u0064\u0020\u0062\u0069\u0074\u0073");};_afb ,_fgeb :=_bffc .InputStream .ReadBit ();if _fgeb !=nil {return _ac .Wrap (_fgeb ,_caa ,"\u0065x\u0074e\u006e\u0064\u0065\u0064\u0020t\u0065\u006dp\u006c\u0061\u0074\u0065\u0073");
};if _afb ==1{_bffc .GBUseExtTemplate =true ;};_afb ,_fgeb =_bffc .InputStream .ReadBit ();if _fgeb !=nil {return _ac .Wrap (_fgeb ,_caa ,"\u0075\u006e\u006b\u006eow\u006e\u0020\u0070\u0061\u0067\u0065\u0020\u006e\u0075\u006d\u0062\u0065\u0072");};if _afb !=1{_bffc .NumberOfPagesUnknown =false ;
};_afb ,_fgeb =_bffc .InputStream .ReadBit ();if _fgeb !=nil {return _ac .Wrap (_fgeb ,_caa ,"\u006f\u0072\u0067\u0061\u006e\u0069\u007a\u0061\u0074\u0069\u006f\u006e \u0074\u0079\u0070\u0065");};_bffc .OrganizationType =_dc .OrganizationType (_afb );if !_bffc .NumberOfPagesUnknown {_bffc .NumberOfPages ,_fgeb =_bffc .InputStream .ReadUint32 ();
if _fgeb !=nil {return _ac .Wrap (_fgeb ,_caa ,"\u006eu\u006db\u0065\u0072\u0020\u006f\u0066\u0020\u0070\u0061\u0067\u0065\u0073");};_bffc ._bge =13;};return nil ;};func (_gag *Document )completeSymbols ()(_fdb error ){const _aa ="\u0063o\u006dp\u006c\u0065\u0074\u0065\u0053\u0079\u006d\u0062\u006f\u006c\u0073";
if _gag .Classer ==nil {return nil ;};if _gag .Classer .UndilatedTemplates ==nil {return _ac .Error (_aa ,"\u006e\u006f t\u0065\u006d\u0070l\u0061\u0074\u0065\u0073 de\u0066in\u0065\u0064\u0020\u0066\u006f\u0072\u0020th\u0065\u0020\u0063\u006c\u0061\u0073\u0073e\u0072");
};_fdbd :=len (_gag .Pages )==1;_gae :=make ([]int ,_gag .Classer .UndilatedTemplates .Size ());var _adf int ;for _baa :=0;_baa < _gag .Classer .ClassIDs .Size ();_baa ++{_adf ,_fdb =_gag .Classer .ClassIDs .Get (_baa );if _fdb !=nil {return _ac .Wrap (_fdb ,_aa ,"\u0063\u006c\u0061\u0073\u0073\u0020\u0049\u0044\u0027\u0073");
};_gae [_adf ]++;};var _bb []int ;for _eff :=0;_eff < _gag .Classer .UndilatedTemplates .Size ();_eff ++{if _gae [_eff ]==0{return _ac .Error (_aa ,"\u006eo\u0020\u0073y\u006d\u0062\u006f\u006cs\u0020\u0069\u006es\u0074\u0061\u006e\u0063\u0065\u0073\u0020\u0066\u006fun\u0064\u0020\u0066o\u0072\u0020g\u0069\u0076\u0065\u006e\u0020\u0063l\u0061\u0073s\u003f\u0020");
};if _gae [_eff ]> 1||_fdbd {_bb =append (_bb ,_eff );};};_gag ._cg =len (_bb );var _fff ,_fa int ;for _deg :=0;_deg < _gag .Classer .ComponentPageNumbers .Size ();_deg ++{_fff ,_fdb =_gag .Classer .ComponentPageNumbers .Get (_deg );if _fdb !=nil {return _ac .Wrapf (_fdb ,_aa ,"p\u0061\u0067\u0065\u003a\u0020\u0027\u0025\u0064\u0027 \u006e\u006f\u0074\u0020\u0066\u006f\u0075nd\u0020\u0069\u006e\u0020t\u0068\u0065\u0020\u0063\u006c\u0061\u0073\u0073\u0065r \u0070\u0061g\u0065\u006e\u0075\u006d\u0062\u0065\u0072\u0073",_deg );
};_fa ,_fdb =_gag .Classer .ClassIDs .Get (_deg );if _fdb !=nil {return _ac .Wrapf (_fdb ,_aa ,"\u0063\u0061\u006e\u0027\u0074\u0020\u0067e\u0074\u0020\u0073y\u006d\u0062\u006f\u006c \u0066\u006f\u0072\u0020\u0070\u0061\u0067\u0065\u0020\u0027\u0025\u0064\u0027\u0020\u0066\u0072\u006f\u006d\u0020\u0063\u006c\u0061\u0073\u0073\u0065\u0072",_fff );
};if _gae [_fa ]==1&&!_fdbd {_gag ._de [_fff ]=append (_gag ._de [_fff ],_fa );};};if _fdb =_gag .Classer .ComputeLLCorners ();_fdb !=nil {return _ac .Wrap (_fdb ,_aa ,"");};return nil ;};func (_cbd *Page )addTextRegionSegment (_ded []*_dc .Header ,_ebd ,_fcd map[int ]int ,_adc []int ,_cfe *_ec .Points ,_efa *_ec .Bitmaps ,_aeg *_bgf .IntSlice ,_bceg *_ec .Boxes ,_aag ,_agdd int ){_ecee :=&_dc .TextRegion {NumberOfSymbols :uint32 (_agdd )};
_ecee .InitEncode (_ebd ,_fcd ,_adc ,_cfe ,_efa ,_aeg ,_bceg ,_cbd .FinalWidth ,_cbd .FinalHeight ,_aag );_gbc :=&_dc .Header {RTSegments :_ded ,SegmentData :_ecee ,PageAssociation :_cbd .PageNumber ,Type :_dc .TImmediateTextRegion };_cae :=_dc .TPageInformation ;
if _fcd !=nil {_cae =_dc .TSymbolDictionary ;};var _dab int ;for ;_dab < len (_cbd .Segments );_dab ++{if _cbd .Segments [_dab ].Type ==_cae {_dab ++;break ;};};_cbd .Segments =append (_cbd .Segments ,nil );copy (_cbd .Segments [_dab +1:],_cbd .Segments [_dab :]);
_cbd .Segments [_dab ]=_gbc ;};func (_adfd *Page )GetWidth ()(int ,error ){return _adfd .getWidth ()};func (_adcf *Page )getCombinationOperator (_dcd *_dc .PageInformationSegment ,_ecbc _ec .CombinationOperator )_ec .CombinationOperator {if _dcd .CombinationOperatorOverrideAllowed (){return _ecbc ;
};return _dcd .CombinationOperator ();};func (_dgg *Page )GetSegment (number int )(*_dc .Header ,error ){const _gaee ="\u0050a\u0067e\u002e\u0047\u0065\u0074\u0053\u0065\u0067\u006d\u0065\u006e\u0074";for _ ,_adgg :=range _dgg .Segments {if _adgg .SegmentNumber ==uint32 (number ){return _adgg ,nil ;
};};_cfg :=make ([]uint32 ,len (_dgg .Segments ));for _cebe ,_abe :=range _dgg .Segments {_cfg [_cebe ]=_abe .SegmentNumber ;};return nil ,_ac .Errorf (_gaee ,"\u0073e\u0067\u006d\u0065n\u0074\u0020\u0077i\u0074h \u006e\u0075\u006d\u0062\u0065\u0072\u003a \u0027\u0025\u0064\u0027\u0020\u006e\u006f\u0074\u0020\u0066\u006f\u0075\u006e\u0064\u0020\u0069\u006e\u0020\u0074\u0068\u0065\u0020\u0070\u0061\u0067\u0065\u003a\u0020'%\u0064'\u002e\u0020\u004b\u006e\u006f\u0077n\u0020\u0073\u0065\u0067\u006de\u006e\u0074\u0020\u006e\u0075\u006d\u0062\u0065\u0072\u0073\u003a \u0025\u0076",number ,_dgg .PageNumber ,_cfg );
};func (_eag *Page )getResolutionY ()(int ,error ){const _ead ="\u0067\u0065\u0074\u0052\u0065\u0073\u006f\u006c\u0075t\u0069\u006f\u006e\u0059";if _eag .ResolutionY !=0{return _eag .ResolutionY ,nil ;};_deac :=_eag .getPageInformationSegment ();if _deac ==nil {return 0,_ac .Error (_ead ,"n\u0069l\u0020\u0070\u0061\u0067\u0065\u0020\u0069\u006ef\u006f\u0072\u006d\u0061ti\u006f\u006e");
};_cfd ,_bgff :=_deac .GetSegmentData ();if _bgff !=nil {return 0,_ac .Wrap (_bgff ,_ead ,"");};_fdd ,_dfgg :=_cfd .(*_dc .PageInformationSegment );if !_dfgg {return 0,_ac .Errorf (_ead ,"\u0070\u0061\u0067\u0065\u0020\u0069\u006e\u0066o\u0072\u006d\u0061ti\u006f\u006e\u0020\u0073\u0065\u0067m\u0065\u006e\u0074\u0020\u0069\u0073\u0020\u006f\u0066\u0020\u0069\u006e\u0076\u0061\u006ci\u0064\u0020\u0074\u0079\u0070\u0065\u003a\u0027%\u0054\u0027",_cfd );
};_eag .ResolutionY =_fdd .ResolutionY ;return _eag .ResolutionY ,nil ;};func _bdd (_afd *_eg .Reader ,_fcc *Globals )(*Document ,error ){_dbc :=&Document {Pages :make (map[int ]*Page ),InputStream :_afd ,OrganizationType :_dc .OSequential ,NumberOfPagesUnknown :true ,GlobalSegments :_fcc ,_bge :9};
if _dbc .GlobalSegments ==nil {_dbc .GlobalSegments =&Globals {};};if _bagf :=_dbc .mapData ();_bagf !=nil {return nil ,_bagf ;};return _dbc ,nil ;};func (_cda *Page )Encode (w _eg .BinaryWriter )(_agfc int ,_cegg error ){const _bbc ="P\u0061\u0067\u0065\u002e\u0045\u006e\u0063\u006f\u0064\u0065";
var _fcb int ;for _ ,_acae :=range _cda .Segments {if _fcb ,_cegg =_acae .Encode (w );_cegg !=nil {return _agfc ,_ac .Wrap (_cegg ,_bbc ,"");};_agfc +=_fcb ;};return _agfc ,nil ;};func (_feca *Document )determineRandomDataOffsets (_bffg []*_dc .Header ,_gff uint64 ){if _feca .OrganizationType !=_dc .ORandom {return ;
};for _ ,_gbgf :=range _bffg {_gbgf .SegmentDataStartOffset =_gff ;_gff +=_gbgf .SegmentDataLength ;};};