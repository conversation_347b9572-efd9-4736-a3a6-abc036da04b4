//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package bitmap ;import (_gg "encoding/binary";_gb "github.com/stretchr/testify/require";_bb "github.com/unidoc/unipdf/v3/common";_ed "github.com/unidoc/unipdf/v3/internal/bitwise";_d "github.com/unidoc/unipdf/v3/internal/imageutil";_eb "github.com/unidoc/unipdf/v3/internal/jbig2/basic";
_ga "github.com/unidoc/unipdf/v3/internal/jbig2/errors";_c "image";_b "math";_e "sort";_gaf "strings";_f "testing";);func _dfc (_ebde *Bitmap ,_ad *Bitmap ,_fff int )(_ebdc error ){const _ae ="e\u0078\u0070\u0061\u006edB\u0069n\u0061\u0072\u0079\u0050\u006fw\u0065\u0072\u0032\u004c\u006f\u0077";
switch _fff {case 2:_ebdc =_da (_ebde ,_ad );case 4:_ebdc =_cd (_ebde ,_ad );case 8:_ebdc =_ebd (_ebde ,_ad );default:return _ga .Error (_ae ,"\u0065\u0078p\u0061\u006e\u0073\u0069o\u006e\u0020f\u0061\u0063\u0074\u006f\u0072\u0020\u006e\u006ft\u0020\u0069\u006e\u0020\u007b\u0032\u002c\u0034\u002c\u0038\u007d\u0020r\u0061\u006e\u0067\u0065");
};if _ebdc !=nil {_ebdc =_ga .Wrap (_ebdc ,_ae ,"");};return _ebdc ;};func (_agbc *Bitmap )inverseData (){if _eedc :=_agbc .RasterOperation (0,0,_agbc .Width ,_agbc .Height ,PixNotDst ,nil ,0,0);_eedc !=nil {_bb .Log .Debug ("\u0049n\u0076\u0065\u0072\u0073e\u0020\u0064\u0061\u0074\u0061 \u0066a\u0069l\u0065\u0064\u003a\u0020\u0027\u0025\u0076'",_eedc );
};if _agbc .Color ==Chocolate {_agbc .Color =Vanilla ;}else {_agbc .Color =Chocolate ;};};func (_ccaa *ClassedPoints )SortByX (){_ccaa ._bbba =_ccaa .xSortFunction ();_e .Sort (_ccaa )};func (_cbg *Bitmap )GetUnpaddedData ()([]byte ,error ){_ffbf :=uint (_cbg .Width &0x07);
if _ffbf ==0{return _cbg .Data ,nil ;};_adc :=_cbg .Width *_cbg .Height ;if _adc %8!=0{_adc >>=3;_adc ++;}else {_adc >>=3;};_fcb :=make ([]byte ,_adc );_dfg :=_ed .NewWriterMSB (_fcb );const _ddb ="\u0047e\u0074U\u006e\u0070\u0061\u0064\u0064\u0065\u0064\u0044\u0061\u0074\u0061";
for _cdg :=0;_cdg < _cbg .Height ;_cdg ++{for _eaf :=0;_eaf < _cbg .RowStride ;_eaf ++{_ffc :=_cbg .Data [_cdg *_cbg .RowStride +_eaf ];if _eaf !=_cbg .RowStride -1{_cbfc :=_dfg .WriteByte (_ffc );if _cbfc !=nil {return nil ,_ga .Wrap (_cbfc ,_ddb ,"");
};continue ;};for _bgaf :=uint (0);_bgaf < _ffbf ;_bgaf ++{_daafe :=_dfg .WriteBit (int (_ffc >>(7-_bgaf )&0x01));if _daafe !=nil {return nil ,_ga .Wrap (_daafe ,_ddb ,"");};};};};return _fcb ,nil ;};type MorphOperation int ;func (_fbc *Bitmap )GetPixel (x ,y int )bool {_cab :=_fbc .GetByteIndex (x ,y );
_bdc :=_fbc .GetBitOffset (x );_dfa :=uint (7-_bdc );if _cab > len (_fbc .Data )-1{_bb .Log .Debug ("\u0054\u0072\u0079\u0069\u006e\u0067\u0020\u0074\u006f\u0020\u0067\u0065\u0074\u0020\u0070\u0069\u0078\u0065\u006c\u0020o\u0075\u0074\u0020\u006f\u0066\u0020\u0074\u0068\u0065\u0020\u0064\u0061\u0074\u0061\u0020\u0072\u0061\u006e\u0067\u0065\u002e \u0078\u003a\u0020\u0027\u0025\u0064\u0027\u002c\u0020\u0079\u003a\u0027\u0025\u0064'\u002c\u0020\u0062m\u003a\u0020\u0027\u0025\u0073\u0027",x ,y ,_fbc );
return false ;};if (_fbc .Data [_cab ]>>_dfa )&0x01>=1{return true ;};return false ;};func Extract (roi _c .Rectangle ,src *Bitmap )(*Bitmap ,error ){_fbab :=New (roi .Dx (),roi .Dy ());_agfb :=roi .Min .X &0x07;_cdc :=8-_agfb ;_ebfb :=uint (8-_fbab .Width &0x07);
_abfe :=src .GetByteIndex (roi .Min .X ,roi .Min .Y );_edfe :=src .GetByteIndex (roi .Max .X -1,roi .Min .Y );_bcc :=_fbab .RowStride ==_edfe +1-_abfe ;var _dgab int ;for _addc :=roi .Min .Y ;_addc < roi .Max .Y ;_addc ++{_dfff :=_abfe ;_fgecb :=_dgab ;
switch {case _abfe ==_edfe :_geabc ,_eedb :=src .GetByte (_dfff );if _eedb !=nil {return nil ,_eedb ;};_geabc <<=uint (_agfb );_eedb =_fbab .SetByte (_fgecb ,_dcfe (_ebfb ,_geabc ));if _eedb !=nil {return nil ,_eedb ;};case _agfb ==0:for _aaag :=_abfe ;
_aaag <=_edfe ;_aaag ++{_cdgc ,_ebeac :=src .GetByte (_dfff );if _ebeac !=nil {return nil ,_ebeac ;};_dfff ++;if _aaag ==_edfe &&_bcc {_cdgc =_dcfe (_ebfb ,_cdgc );};_ebeac =_fbab .SetByte (_fgecb ,_cdgc );if _ebeac !=nil {return nil ,_ebeac ;};_fgecb ++;
};default:_ecdb :=_dcgd (src ,_fbab ,uint (_agfb ),uint (_cdc ),_ebfb ,_abfe ,_edfe ,_bcc ,_dfff ,_fgecb );if _ecdb !=nil {return nil ,_ecdb ;};};_abfe +=src .RowStride ;_edfe +=src .RowStride ;_dgab +=_fbab .RowStride ;};return _fbab ,nil ;};func _gaa ()(_aac [256]uint16 ){for _abff :=0;
_abff < 256;_abff ++{if _abff &0x01!=0{_aac [_abff ]|=0x3;};if _abff &0x02!=0{_aac [_abff ]|=0xc;};if _abff &0x04!=0{_aac [_abff ]|=0x30;};if _abff &0x08!=0{_aac [_abff ]|=0xc0;};if _abff &0x10!=0{_aac [_abff ]|=0x300;};if _abff &0x20!=0{_aac [_abff ]|=0xc00;
};if _abff &0x40!=0{_aac [_abff ]|=0x3000;};if _abff &0x80!=0{_aac [_abff ]|=0xc000;};};return _aac ;};func TstISymbol (t *_f .T ,scale ...int )*Bitmap {_bagb ,_gcad :=NewWithData (1,5,[]byte {0x80,0x80,0x80,0x80,0x80});_gb .NoError (t ,_gcad );return TstGetScaledSymbol (t ,_bagb ,scale ...);
};type Bitmap struct{Width ,Height int ;BitmapNumber int ;RowStride int ;Data []byte ;Color Color ;Special int ;Text string ;XResolution ,YResolution int ;};func (_dbfc *Bitmaps )selectByIndexes (_babcg []int )(*Bitmaps ,error ){_dfeef :=&Bitmaps {};for _ ,_fcaf :=range _babcg {_bcfe ,_eeaea :=_dbfc .GetBitmap (_fcaf );
if _eeaea !=nil {return nil ,_ga .Wrap (_eeaea ,"\u0073e\u006ce\u0063\u0074\u0042\u0079\u0049\u006e\u0064\u0065\u0078\u0065\u0073","");};_dfeef .AddBitmap (_bcfe );};return _dfeef ,nil ;};func CorrelationScoreSimple (bm1 ,bm2 *Bitmap ,area1 ,area2 int ,delX ,delY float32 ,maxDiffW ,maxDiffH int ,tab []int )(_cddg float64 ,_befg error ){const _cced ="\u0043\u006f\u0072\u0072el\u0061\u0074\u0069\u006f\u006e\u0053\u0063\u006f\u0072\u0065\u0053\u0069\u006d\u0070l\u0065";
if bm1 ==nil ||bm2 ==nil {return _cddg ,_ga .Error (_cced ,"n\u0069l\u0020\u0062\u0069\u0074\u006d\u0061\u0070\u0073 \u0070\u0072\u006f\u0076id\u0065\u0064");};if tab ==nil {return _cddg ,_ga .Error (_cced ,"\u0074\u0061\u0062\u0020\u0075\u006e\u0064\u0065\u0066\u0069\u006e\u0065\u0064");
};if area1 ==0||area2 ==0{return _cddg ,_ga .Error (_cced ,"\u0070\u0072\u006f\u0076\u0069\u0064\u0065\u0064\u0020\u0061\u0072e\u0061\u0073\u0020\u006d\u0075\u0073\u0074\u0020\u0062\u0065 \u003e\u0020\u0030");};_bdde ,_bffb :=bm1 .Width ,bm1 .Height ;_efgg ,_gcgc :=bm2 .Width ,bm2 .Height ;
if _agfg (_bdde -_efgg )> maxDiffW {return 0,nil ;};if _agfg (_bffb -_gcgc )> maxDiffH {return 0,nil ;};var _dafg ,_cfdf int ;if delX >=0{_dafg =int (delX +0.5);}else {_dafg =int (delX -0.5);};if delY >=0{_cfdf =int (delY +0.5);}else {_cfdf =int (delY -0.5);
};_eec :=bm1 .createTemplate ();if _befg =_eec .RasterOperation (_dafg ,_cfdf ,_efgg ,_gcgc ,PixSrc ,bm2 ,0,0);_befg !=nil {return _cddg ,_ga .Wrap (_befg ,_cced ,"\u0062m\u0032 \u0074\u006f\u0020\u0054\u0065\u006d\u0070\u006c\u0061\u0074\u0065");};if _befg =_eec .RasterOperation (0,0,_bdde ,_bffb ,PixSrcAndDst ,bm1 ,0,0);
_befg !=nil {return _cddg ,_ga .Wrap (_befg ,_cced ,"b\u006d\u0031\u0020\u0061\u006e\u0064\u0020\u0062\u006d\u0054");};_baffg :=_eec .countPixels ();_cddg =float64 (_baffg )*float64 (_baffg )/(float64 (area1 )*float64 (area2 ));return _cddg ,nil ;};func _be (_ebda ,_eeag *Bitmap ,_bga int ,_gcg []byte ,_cdb int )(_fgb error ){const _eca ="\u0072\u0065\u0064uc\u0065\u0052\u0061\u006e\u006b\u0042\u0069\u006e\u0061\u0072\u0079\u0032\u004c\u0065\u0076\u0065\u006c\u0034";
var (_afb ,_afe ,_edd ,_dec ,_fdf ,_affd ,_dgb ,_feaa int ;_daaf ,_eee uint32 ;_gge ,_ggae byte ;_agcb uint16 ;);_cgd :=make ([]byte ,4);_gbb :=make ([]byte ,4);for _edd =0;_edd < _ebda .Height -1;_edd ,_dec =_edd +2,_dec +1{_afb =_edd *_ebda .RowStride ;
_afe =_dec *_eeag .RowStride ;for _fdf ,_affd =0,0;_fdf < _cdb ;_fdf ,_affd =_fdf +4,_affd +1{for _dgb =0;_dgb < 4;_dgb ++{_feaa =_afb +_fdf +_dgb ;if _feaa <=len (_ebda .Data )-1&&_feaa < _afb +_ebda .RowStride {_cgd [_dgb ]=_ebda .Data [_feaa ];}else {_cgd [_dgb ]=0x00;
};_feaa =_afb +_ebda .RowStride +_fdf +_dgb ;if _feaa <=len (_ebda .Data )-1&&_feaa < _afb +(2*_ebda .RowStride ){_gbb [_dgb ]=_ebda .Data [_feaa ];}else {_gbb [_dgb ]=0x00;};};_daaf =_gg .BigEndian .Uint32 (_cgd );_eee =_gg .BigEndian .Uint32 (_gbb );
_eee &=_daaf ;_eee &=_eee <<1;_eee &=0xaaaaaaaa;_daaf =_eee |(_eee <<7);_gge =byte (_daaf >>24);_ggae =byte ((_daaf >>8)&0xff);_feaa =_afe +_affd ;if _feaa +1==len (_eeag .Data )-1||_feaa +1>=_afe +_eeag .RowStride {_eeag .Data [_feaa ]=_gcg [_gge ];if _fgb =_eeag .SetByte (_feaa ,_gcg [_gge ]);
_fgb !=nil {return _ga .Wrapf (_fgb ,_eca ,"\u0069n\u0064\u0065\u0078\u003a\u0020\u0025d",_feaa );};}else {_agcb =(uint16 (_gcg [_gge ])<<8)|uint16 (_gcg [_ggae ]);if _fgb =_eeag .setTwoBytes (_feaa ,_agcb );_fgb !=nil {return _ga .Wrapf (_fgb ,_eca ,"s\u0065\u0074\u0074\u0069\u006e\u0067 \u0074\u0077\u006f\u0020\u0062\u0079t\u0065\u0073\u0020\u0066\u0061\u0069\u006ce\u0064\u002c\u0020\u0069\u006e\u0064\u0065\u0078\u003a\u0020%\u0064",_feaa );
};_affd ++;};};};return nil ;};func Centroids (bms []*Bitmap )(*Points ,error ){_ccfb :=make ([]Point ,len (bms ));_acgc :=_fcca ();_eeae :=_dfeeb ();var _feeg error ;for _deb ,_bfba :=range bms {_ccfb [_deb ],_feeg =_bfba .centroid (_acgc ,_eeae );if _feeg !=nil {return nil ,_feeg ;
};};_fffe :=Points (_ccfb );return &_fffe ,nil ;};func TstRSymbol (t *_f .T ,scale ...int )*Bitmap {_cbbd ,_dadb :=NewWithData (4,5,[]byte {0xF0,0x90,0xF0,0xA0,0x90});_gb .NoError (t ,_dadb );return TstGetScaledSymbol (t ,_cbbd ,scale ...);};func (_ceef *Bitmap )removeBorderGeneral (_fbdc ,_dggg ,_fgec ,_bgef int )(*Bitmap ,error ){const _cge ="\u0072\u0065\u006d\u006fve\u0042\u006f\u0072\u0064\u0065\u0072\u0047\u0065\u006e\u0065\u0072\u0061\u006c";
if _fbdc < 0||_dggg < 0||_fgec < 0||_bgef < 0{return nil ,_ga .Error (_cge ,"\u006e\u0065g\u0061\u0074\u0069\u0076\u0065\u0020\u0062\u0072\u006f\u0064\u0065\u0072\u0020\u0072\u0065\u006d\u006f\u0076\u0065\u0020\u0076\u0061lu\u0065\u0073");};_caf ,_bbg :=_ceef .Width ,_ceef .Height ;
_dbgb :=_caf -_fbdc -_dggg ;_ccbb :=_bbg -_fgec -_bgef ;if _dbgb <=0{return nil ,_ga .Errorf (_cge ,"w\u0069\u0064\u0074\u0068: \u0025d\u0020\u006d\u0075\u0073\u0074 \u0062\u0065\u0020\u003e\u0020\u0030",_dbgb );};if _ccbb <=0{return nil ,_ga .Errorf (_cge ,"\u0068\u0065\u0069\u0067ht\u003a\u0020\u0025\u0064\u0020\u006d\u0075\u0073\u0074\u0020\u0062\u0065\u0020\u003e \u0030",_ccbb );
};_begg :=New (_dbgb ,_ccbb );_begg .Color =_ceef .Color ;_bgge :=_begg .RasterOperation (0,0,_dbgb ,_ccbb ,PixSrc ,_ceef ,_fbdc ,_fgec );if _bgge !=nil {return nil ,_ga .Wrap (_bgge ,_cge ,"");};return _begg ,nil ;};func (_acaf *Boxes )Add (box *_c .Rectangle )error {if _acaf ==nil {return _ga .Error ("\u0042o\u0078\u0065\u0073\u002e\u0041\u0064d","\u0027\u0042\u006f\u0078es\u0027\u0020\u006e\u006f\u0074\u0020\u0064\u0065\u0066\u0069\u006e\u0065\u0064");
};*_acaf =append (*_acaf ,box );return nil ;};func init (){for _gfcg :=0;_gfcg < 256;_gfcg ++{_faab [_gfcg ]=uint8 (_gfcg &0x1)+(uint8 (_gfcg >>1)&0x1)+(uint8 (_gfcg >>2)&0x1)+(uint8 (_gfcg >>3)&0x1)+(uint8 (_gfcg >>4)&0x1)+(uint8 (_gfcg >>5)&0x1)+(uint8 (_gfcg >>6)&0x1)+(uint8 (_gfcg >>7)&0x1);
};};func _edda (_cgde ,_eeea ,_dbdd *Bitmap )(*Bitmap ,error ){const _fcfc ="\u0062\u0069\u0074\u006d\u0061\u0070\u002e\u0078\u006f\u0072";if _eeea ==nil {return nil ,_ga .Error (_fcfc ,"'\u0062\u0031\u0027\u0020\u0069\u0073\u0020\u006e\u0069\u006c");};
if _dbdd ==nil {return nil ,_ga .Error (_fcfc ,"'\u0062\u0032\u0027\u0020\u0069\u0073\u0020\u006e\u0069\u006c");};if _cgde ==_dbdd {return nil ,_ga .Error (_fcfc ,"'\u0064\u0027\u0020\u003d\u003d\u0020\u0027\u0062\u0032\u0027");};if !_eeea .SizesEqual (_dbdd ){_bb .Log .Debug ("\u0025s\u0020\u002d \u0042\u0069\u0074\u006da\u0070\u0020\u0027b\u0031\u0027\u0020\u0069\u0073\u0020\u006e\u006f\u0074 e\u0071\u0075\u0061l\u0020\u0073i\u007a\u0065\u0020\u0077\u0069\u0074h\u0020\u0027b\u0032\u0027",_fcfc );
};var _bggf error ;if _cgde ,_bggf =_acda (_cgde ,_eeea );_bggf !=nil {return nil ,_ga .Wrap (_bggf ,_fcfc ,"\u0063\u0061n\u0027\u0074\u0020c\u0072\u0065\u0061\u0074\u0065\u0020\u0027\u0064\u0027");};if _bggf =_cgde .RasterOperation (0,0,_cgde .Width ,_cgde .Height ,PixSrcXorDst ,_dbdd ,0,0);
_bggf !=nil {return nil ,_ga .Wrap (_bggf ,_fcfc ,"");};return _cgde ,nil ;};func _ebfdd (_affeb ,_cdac int ,_dbeb string )*Selection {_fdaee :=&Selection {Height :_affeb ,Width :_cdac ,Name :_dbeb };_fdaee .Data =make ([][]SelectionValue ,_affeb );for _fedfb :=0;
_fedfb < _affeb ;_fedfb ++{_fdaee .Data [_fedfb ]=make ([]SelectionValue ,_cdac );};return _fdaee ;};func (_dcgg *Bitmap )Zero ()bool {_ebab :=_dcgg .Width /8;_cdd :=_dcgg .Width &7;var _fbbg byte ;if _cdd !=0{_fbbg =byte (0xff<<uint (8-_cdd ));};var _egad ,_gedg ,_efg int ;
for _gedg =0;_gedg < _dcgg .Height ;_gedg ++{_egad =_dcgg .RowStride *_gedg ;for _efg =0;_efg < _ebab ;_efg ,_egad =_efg +1,_egad +1{if _dcgg .Data [_egad ]!=0{return false ;};};if _cdd > 0{if _dcgg .Data [_egad ]&_fbbg !=0{return false ;};};};return true ;
};func _efad (_ecfga *Bitmap ,_feaf ,_abdc ,_fdae ,_dcbg int ,_gbdd RasterOperator ,_ddbee *Bitmap ,_dfec ,_gcfg int )error {var (_ebfeg byte ;_ccdg int ;_agbfc int ;_gafa ,_bdfa int ;_egffe ,_cfffb int ;);_dbcgc :=_fdae >>3;_ffag :=_fdae &7;if _ffag > 0{_ebfeg =_bcee [_ffag ];
};_ccdg =_ddbee .RowStride *_gcfg +(_dfec >>3);_agbfc =_ecfga .RowStride *_abdc +(_feaf >>3);switch _gbdd {case PixSrc :for _egffe =0;_egffe < _dcbg ;_egffe ++{_gafa =_ccdg +_egffe *_ddbee .RowStride ;_bdfa =_agbfc +_egffe *_ecfga .RowStride ;for _cfffb =0;
_cfffb < _dbcgc ;_cfffb ++{_ecfga .Data [_bdfa ]=_ddbee .Data [_gafa ];_bdfa ++;_gafa ++;};if _ffag > 0{_ecfga .Data [_bdfa ]=_aacc (_ecfga .Data [_bdfa ],_ddbee .Data [_gafa ],_ebfeg );};};case PixNotSrc :for _egffe =0;_egffe < _dcbg ;_egffe ++{_gafa =_ccdg +_egffe *_ddbee .RowStride ;
_bdfa =_agbfc +_egffe *_ecfga .RowStride ;for _cfffb =0;_cfffb < _dbcgc ;_cfffb ++{_ecfga .Data [_bdfa ]=^(_ddbee .Data [_gafa ]);_bdfa ++;_gafa ++;};if _ffag > 0{_ecfga .Data [_bdfa ]=_aacc (_ecfga .Data [_bdfa ],^_ddbee .Data [_gafa ],_ebfeg );};};case PixSrcOrDst :for _egffe =0;
_egffe < _dcbg ;_egffe ++{_gafa =_ccdg +_egffe *_ddbee .RowStride ;_bdfa =_agbfc +_egffe *_ecfga .RowStride ;for _cfffb =0;_cfffb < _dbcgc ;_cfffb ++{_ecfga .Data [_bdfa ]|=_ddbee .Data [_gafa ];_bdfa ++;_gafa ++;};if _ffag > 0{_ecfga .Data [_bdfa ]=_aacc (_ecfga .Data [_bdfa ],_ddbee .Data [_gafa ]|_ecfga .Data [_bdfa ],_ebfeg );
};};case PixSrcAndDst :for _egffe =0;_egffe < _dcbg ;_egffe ++{_gafa =_ccdg +_egffe *_ddbee .RowStride ;_bdfa =_agbfc +_egffe *_ecfga .RowStride ;for _cfffb =0;_cfffb < _dbcgc ;_cfffb ++{_ecfga .Data [_bdfa ]&=_ddbee .Data [_gafa ];_bdfa ++;_gafa ++;};
if _ffag > 0{_ecfga .Data [_bdfa ]=_aacc (_ecfga .Data [_bdfa ],_ddbee .Data [_gafa ]&_ecfga .Data [_bdfa ],_ebfeg );};};case PixSrcXorDst :for _egffe =0;_egffe < _dcbg ;_egffe ++{_gafa =_ccdg +_egffe *_ddbee .RowStride ;_bdfa =_agbfc +_egffe *_ecfga .RowStride ;
for _cfffb =0;_cfffb < _dbcgc ;_cfffb ++{_ecfga .Data [_bdfa ]^=_ddbee .Data [_gafa ];_bdfa ++;_gafa ++;};if _ffag > 0{_ecfga .Data [_bdfa ]=_aacc (_ecfga .Data [_bdfa ],_ddbee .Data [_gafa ]^_ecfga .Data [_bdfa ],_ebfeg );};};case PixNotSrcOrDst :for _egffe =0;
_egffe < _dcbg ;_egffe ++{_gafa =_ccdg +_egffe *_ddbee .RowStride ;_bdfa =_agbfc +_egffe *_ecfga .RowStride ;for _cfffb =0;_cfffb < _dbcgc ;_cfffb ++{_ecfga .Data [_bdfa ]|=^(_ddbee .Data [_gafa ]);_bdfa ++;_gafa ++;};if _ffag > 0{_ecfga .Data [_bdfa ]=_aacc (_ecfga .Data [_bdfa ],^(_ddbee .Data [_gafa ])|_ecfga .Data [_bdfa ],_ebfeg );
};};case PixNotSrcAndDst :for _egffe =0;_egffe < _dcbg ;_egffe ++{_gafa =_ccdg +_egffe *_ddbee .RowStride ;_bdfa =_agbfc +_egffe *_ecfga .RowStride ;for _cfffb =0;_cfffb < _dbcgc ;_cfffb ++{_ecfga .Data [_bdfa ]&=^(_ddbee .Data [_gafa ]);_bdfa ++;_gafa ++;
};if _ffag > 0{_ecfga .Data [_bdfa ]=_aacc (_ecfga .Data [_bdfa ],^(_ddbee .Data [_gafa ])&_ecfga .Data [_bdfa ],_ebfeg );};};case PixSrcOrNotDst :for _egffe =0;_egffe < _dcbg ;_egffe ++{_gafa =_ccdg +_egffe *_ddbee .RowStride ;_bdfa =_agbfc +_egffe *_ecfga .RowStride ;
for _cfffb =0;_cfffb < _dbcgc ;_cfffb ++{_ecfga .Data [_bdfa ]=_ddbee .Data [_gafa ]|^(_ecfga .Data [_bdfa ]);_bdfa ++;_gafa ++;};if _ffag > 0{_ecfga .Data [_bdfa ]=_aacc (_ecfga .Data [_bdfa ],_ddbee .Data [_gafa ]|^(_ecfga .Data [_bdfa ]),_ebfeg );};
};case PixSrcAndNotDst :for _egffe =0;_egffe < _dcbg ;_egffe ++{_gafa =_ccdg +_egffe *_ddbee .RowStride ;_bdfa =_agbfc +_egffe *_ecfga .RowStride ;for _cfffb =0;_cfffb < _dbcgc ;_cfffb ++{_ecfga .Data [_bdfa ]=_ddbee .Data [_gafa ]&^(_ecfga .Data [_bdfa ]);
_bdfa ++;_gafa ++;};if _ffag > 0{_ecfga .Data [_bdfa ]=_aacc (_ecfga .Data [_bdfa ],_ddbee .Data [_gafa ]&^(_ecfga .Data [_bdfa ]),_ebfeg );};};case PixNotPixSrcOrDst :for _egffe =0;_egffe < _dcbg ;_egffe ++{_gafa =_ccdg +_egffe *_ddbee .RowStride ;_bdfa =_agbfc +_egffe *_ecfga .RowStride ;
for _cfffb =0;_cfffb < _dbcgc ;_cfffb ++{_ecfga .Data [_bdfa ]=^(_ddbee .Data [_gafa ]|_ecfga .Data [_bdfa ]);_bdfa ++;_gafa ++;};if _ffag > 0{_ecfga .Data [_bdfa ]=_aacc (_ecfga .Data [_bdfa ],^(_ddbee .Data [_gafa ]|_ecfga .Data [_bdfa ]),_ebfeg );};
};case PixNotPixSrcAndDst :for _egffe =0;_egffe < _dcbg ;_egffe ++{_gafa =_ccdg +_egffe *_ddbee .RowStride ;_bdfa =_agbfc +_egffe *_ecfga .RowStride ;for _cfffb =0;_cfffb < _dbcgc ;_cfffb ++{_ecfga .Data [_bdfa ]=^(_ddbee .Data [_gafa ]&_ecfga .Data [_bdfa ]);
_bdfa ++;_gafa ++;};if _ffag > 0{_ecfga .Data [_bdfa ]=_aacc (_ecfga .Data [_bdfa ],^(_ddbee .Data [_gafa ]&_ecfga .Data [_bdfa ]),_ebfeg );};};case PixNotPixSrcXorDst :for _egffe =0;_egffe < _dcbg ;_egffe ++{_gafa =_ccdg +_egffe *_ddbee .RowStride ;_bdfa =_agbfc +_egffe *_ecfga .RowStride ;
for _cfffb =0;_cfffb < _dbcgc ;_cfffb ++{_ecfga .Data [_bdfa ]=^(_ddbee .Data [_gafa ]^_ecfga .Data [_bdfa ]);_bdfa ++;_gafa ++;};if _ffag > 0{_ecfga .Data [_bdfa ]=_aacc (_ecfga .Data [_bdfa ],^(_ddbee .Data [_gafa ]^_ecfga .Data [_bdfa ]),_ebfeg );};
};default:_bb .Log .Debug ("\u0050\u0072ov\u0069\u0064\u0065d\u0020\u0069\u006e\u0076ali\u0064 r\u0061\u0073\u0074\u0065\u0072\u0020\u006fpe\u0072\u0061\u0074\u006f\u0072\u003a\u0020%\u0076",_gbdd );return _ga .Error ("\u0072\u0061\u0073\u0074er\u004f\u0070\u0042\u0079\u0074\u0065\u0041\u006c\u0069\u0067\u006e\u0065\u0064\u004co\u0077","\u0069\u006e\u0076al\u0069\u0064\u0020\u0072\u0061\u0073\u0074\u0065\u0072\u0020\u006f\u0070\u0065\u0072\u0061\u0074\u006f\u0072");
};return nil ;};func (_dede *ClassedPoints )xSortFunction ()func (_eddc int ,_degbb int )bool {return func (_fefae ,_bbbd int )bool {return _dede .XAtIndex (_fefae )< _dede .XAtIndex (_bbbd )};};func _cabbg (_aeea *Bitmap ,_bbfcg *_eb .Stack ,_agdac ,_eedcg int )(_adca *_c .Rectangle ,_bafba error ){const _aef ="\u0073e\u0065d\u0046\u0069\u006c\u006c\u0053\u0074\u0061\u0063\u006b\u0042\u0042";
if _aeea ==nil {return nil ,_ga .Error (_aef ,"\u0070\u0072\u006fvi\u0064\u0065\u0064\u0020\u006e\u0069\u006c\u0020\u0027\u0073\u0027\u0020\u0042\u0069\u0074\u006d\u0061\u0070");};if _bbfcg ==nil {return nil ,_ga .Error (_aef ,"p\u0072o\u0076\u0069\u0064\u0065\u0064\u0020\u006e\u0069l\u0020\u0027\u0073\u0074ac\u006b\u0027");
};_gceg ,_agbff :=_aeea .Width ,_aeea .Height ;_abfad :=_gceg -1;_eedca :=_agbff -1;if _agdac < 0||_agdac > _abfad ||_eedcg < 0||_eedcg > _eedca ||!_aeea .GetPixel (_agdac ,_eedcg ){return nil ,nil ;};_geeb :=_c .Rect (100000,100000,0,0);if _bafba =_fbcc (_bbfcg ,_agdac ,_agdac ,_eedcg ,1,_eedca ,&_geeb );
_bafba !=nil {return nil ,_ga .Wrap (_bafba ,_aef ,"\u0069\u006e\u0069t\u0069\u0061\u006c\u0020\u0070\u0075\u0073\u0068");};if _bafba =_fbcc (_bbfcg ,_agdac ,_agdac ,_eedcg +1,-1,_eedca ,&_geeb );_bafba !=nil {return nil ,_ga .Wrap (_bafba ,_aef ,"\u0032\u006ed\u0020\u0069\u006ei\u0074\u0069\u0061\u006c\u0020\u0070\u0075\u0073\u0068");
};_geeb .Min .X ,_geeb .Max .X =_agdac ,_agdac ;_geeb .Min .Y ,_geeb .Max .Y =_eedcg ,_eedcg ;var (_abfec *fillSegment ;_fcbbg int ;);for _bbfcg .Len ()> 0{if _abfec ,_bafba =_eeed (_bbfcg );_bafba !=nil {return nil ,_ga .Wrap (_bafba ,_aef ,"");};_eedcg =_abfec ._dcdf ;
for _agdac =_abfec ._effa -1;_agdac >=0&&_aeea .GetPixel (_agdac ,_eedcg );_agdac --{if _bafba =_aeea .SetPixel (_agdac ,_eedcg ,0);_bafba !=nil {return nil ,_ga .Wrap (_bafba ,_aef ,"\u0031s\u0074\u0020\u0073\u0065\u0074");};};if _agdac >=_abfec ._effa -1{for {for _agdac ++;
_agdac <=_abfec ._bacba +1&&_agdac <=_abfad &&!_aeea .GetPixel (_agdac ,_eedcg );_agdac ++{};_fcbbg =_agdac ;if !(_agdac <=_abfec ._bacba +1&&_agdac <=_abfad ){break ;};for ;_agdac <=_abfad &&_aeea .GetPixel (_agdac ,_eedcg );_agdac ++{if _bafba =_aeea .SetPixel (_agdac ,_eedcg ,0);
_bafba !=nil {return nil ,_ga .Wrap (_bafba ,_aef ,"\u0032n\u0064\u0020\u0073\u0065\u0074");};};if _bafba =_fbcc (_bbfcg ,_fcbbg ,_agdac -1,_abfec ._dcdf ,_abfec ._abbca ,_eedca ,&_geeb );_bafba !=nil {return nil ,_ga .Wrap (_bafba ,_aef ,"n\u006f\u0072\u006d\u0061\u006c\u0020\u0070\u0075\u0073\u0068");
};if _agdac > _abfec ._bacba {if _bafba =_fbcc (_bbfcg ,_abfec ._bacba +1,_agdac -1,_abfec ._dcdf ,-_abfec ._abbca ,_eedca ,&_geeb );_bafba !=nil {return nil ,_ga .Wrap (_bafba ,_aef ,"\u006ce\u0061k\u0020\u006f\u006e\u0020\u0072i\u0067\u0068t\u0020\u0073\u0069\u0064\u0065");
};};};continue ;};_fcbbg =_agdac +1;if _fcbbg < _abfec ._effa {if _bafba =_fbcc (_bbfcg ,_fcbbg ,_abfec ._effa -1,_abfec ._dcdf ,-_abfec ._abbca ,_eedca ,&_geeb );_bafba !=nil {return nil ,_ga .Wrap (_bafba ,_aef ,"\u006c\u0065\u0061\u006b\u0020\u006f\u006e\u0020\u006c\u0065\u0066\u0074 \u0073\u0069\u0064\u0065");
};};_agdac =_abfec ._effa ;for {for ;_agdac <=_abfad &&_aeea .GetPixel (_agdac ,_eedcg );_agdac ++{if _bafba =_aeea .SetPixel (_agdac ,_eedcg ,0);_bafba !=nil {return nil ,_ga .Wrap (_bafba ,_aef ,"\u0032n\u0064\u0020\u0073\u0065\u0074");};};if _bafba =_fbcc (_bbfcg ,_fcbbg ,_agdac -1,_abfec ._dcdf ,_abfec ._abbca ,_eedca ,&_geeb );
_bafba !=nil {return nil ,_ga .Wrap (_bafba ,_aef ,"n\u006f\u0072\u006d\u0061\u006c\u0020\u0070\u0075\u0073\u0068");};if _agdac > _abfec ._bacba {if _bafba =_fbcc (_bbfcg ,_abfec ._bacba +1,_agdac -1,_abfec ._dcdf ,-_abfec ._abbca ,_eedca ,&_geeb );_bafba !=nil {return nil ,_ga .Wrap (_bafba ,_aef ,"\u006ce\u0061k\u0020\u006f\u006e\u0020\u0072i\u0067\u0068t\u0020\u0073\u0069\u0064\u0065");
};};for _agdac ++;_agdac <=_abfec ._bacba +1&&_agdac <=_abfad &&!_aeea .GetPixel (_agdac ,_eedcg );_agdac ++{};_fcbbg =_agdac ;if !(_agdac <=_abfec ._bacba +1&&_agdac <=_abfad ){break ;};};};_geeb .Max .X ++;_geeb .Max .Y ++;return &_geeb ,nil ;};func (_bebc *BitmapsArray )AddBitmaps (bm *Bitmaps ){_bebc .Values =append (_bebc .Values ,bm )};
func (_gdeb *Bitmap )nextOnPixelLow (_cfd ,_eadb ,_feba ,_bca ,_cadb int )(_bbd _c .Point ,_cggb bool ,_aeef error ){const _cee ="B\u0069\u0074\u006d\u0061p.\u006ee\u0078\u0074\u004f\u006e\u0050i\u0078\u0065\u006c\u004c\u006f\u0077";var (_gfde int ;_gcda byte ;
);_bcgc :=_cadb *_feba ;_aedd :=_bcgc +(_bca /8);if _gcda ,_aeef =_gdeb .GetByte (_aedd );_aeef !=nil {return _bbd ,false ,_ga .Wrap (_aeef ,_cee ,"\u0078\u0053\u0074\u0061\u0072\u0074\u0020\u0061\u006e\u0064 \u0079\u0053\u0074\u0061\u0072\u0074\u0020o\u0075\u0074\u0020\u006f\u0066\u0020\u0072\u0061\u006e\u0067\u0065");
};if _gcda !=0{_fgd :=_bca -(_bca %8)+7;for _gfde =_bca ;_gfde <=_fgd &&_gfde < _cfd ;_gfde ++{if _gdeb .GetPixel (_gfde ,_cadb ){_bbd .X =_gfde ;_bbd .Y =_cadb ;return _bbd ,true ,nil ;};};};_bfbg :=(_bca /8)+1;_gfde =8*_bfbg ;var _afca int ;for _aedd =_bcgc +_bfbg ;
_gfde < _cfd ;_aedd ,_gfde =_aedd +1,_gfde +8{if _gcda ,_aeef =_gdeb .GetByte (_aedd );_aeef !=nil {return _bbd ,false ,_ga .Wrap (_aeef ,_cee ,"r\u0065\u0073\u0074\u0020of\u0020t\u0068\u0065\u0020\u006c\u0069n\u0065\u0020\u0062\u0079\u0074\u0065");};if _gcda ==0{continue ;
};for _afca =0;_afca < 8&&_gfde < _cfd ;_afca ,_gfde =_afca +1,_gfde +1{if _gdeb .GetPixel (_gfde ,_cadb ){_bbd .X =_gfde ;_bbd .Y =_cadb ;return _bbd ,true ,nil ;};};};for _bfbb :=_cadb +1;_bfbb < _eadb ;_bfbb ++{_bcgc =_bfbb *_feba ;for _aedd ,_gfde =_bcgc ,0;
_gfde < _cfd ;_aedd ,_gfde =_aedd +1,_gfde +8{if _gcda ,_aeef =_gdeb .GetByte (_aedd );_aeef !=nil {return _bbd ,false ,_ga .Wrap (_aeef ,_cee ,"\u0066o\u006cl\u006f\u0077\u0069\u006e\u0067\u0020\u006c\u0069\u006e\u0065\u0073");};if _gcda ==0{continue ;
};for _afca =0;_afca < 8&&_gfde < _cfd ;_afca ,_gfde =_afca +1,_gfde +1{if _gdeb .GetPixel (_gfde ,_bfbb ){_bbd .X =_gfde ;_bbd .Y =_bfbb ;return _bbd ,true ,nil ;};};};};return _bbd ,false ,nil ;};func _fe (_ebe *Bitmap ,_fdc ...int )(_bbe *Bitmap ,_gaad error ){const _faa ="\u0072\u0065\u0064uc\u0065\u0052\u0061\u006e\u006b\u0042\u0069\u006e\u0061\u0072\u0079\u0043\u0061\u0073\u0063\u0061\u0064\u0065";
if _ebe ==nil {return nil ,_ga .Error (_faa ,"\u0073o\u0075\u0072\u0063\u0065 \u0062\u0069\u0074\u006d\u0061p\u0020n\u006ft\u0020\u0064\u0065\u0066\u0069\u006e\u0065d");};if len (_fdc )==0||len (_fdc )> 4{return nil ,_ga .Error (_faa ,"t\u0068\u0065\u0072\u0065\u0020\u006d\u0075\u0073\u0074 \u0062\u0065\u0020\u0061\u0074\u0020\u006cea\u0073\u0074\u0020\u006fn\u0065\u0020\u0061\u006e\u0064\u0020\u0061\u0074\u0020mo\u0073\u0074 \u0034\u0020\u006c\u0065\u0076\u0065\u006c\u0073");
};if _fdc [0]<=0{_bb .Log .Debug ("\u006c\u0065\u0076\u0065\u006c\u0031\u0020\u003c\u003d\u0020\u0030 \u002d\u0020\u006e\u006f\u0020\u0072\u0065\u0064\u0075\u0063t\u0069\u006f\u006e");_bbe ,_gaad =_acda (nil ,_ebe );if _gaad !=nil {return nil ,_ga .Wrap (_gaad ,_faa ,"l\u0065\u0076\u0065\u006c\u0031\u0020\u003c\u003d\u0020\u0030");
};return _bbe ,nil ;};_fdad :=_fba ();_bbe =_ebe ;for _dbgg ,_dgd :=range _fdc {if _dgd <=0{break ;};_bbe ,_gaad =_cfg (_bbe ,_dgd ,_fdad );if _gaad !=nil {return nil ,_ga .Wrapf (_gaad ,_faa ,"\u006c\u0065\u0076\u0065\u006c\u0025\u0064\u0020\u0072\u0065\u0064\u0075c\u0074\u0069\u006f\u006e",_dbgg );
};};return _bbe ,nil ;};const (SelDontCare SelectionValue =iota ;SelHit ;SelMiss ;);const _fefee =5000;func (_agaf *Bitmap )thresholdPixelSum (_cgga int )bool {var (_efgdf int ;_gbe uint8 ;_adg byte ;_fbff int ;);_gfge :=_agaf .RowStride ;_fec :=uint (_agaf .Width &0x07);
if _fec !=0{_gbe =uint8 ((0xff<<(8-_fec ))&0xff);_gfge --;};for _ecfc :=0;_ecfc < _agaf .Height ;_ecfc ++{for _fbff =0;_fbff < _gfge ;_fbff ++{_adg =_agaf .Data [_ecfc *_agaf .RowStride +_fbff ];_efgdf +=int (_faab [_adg ]);};if _fec !=0{_adg =_agaf .Data [_ecfc *_agaf .RowStride +_fbff ]&_gbe ;
_efgdf +=int (_faab [_adg ]);};if _efgdf > _cgga {return true ;};};return false ;};func _agd (_ddgf *Bitmap ,_ggb int )(*Bitmap ,error ){const _dbf ="\u0065x\u0070a\u006e\u0064\u0042\u0069\u006ea\u0072\u0079P\u006f\u0077\u0065\u0072\u0032";if _ddgf ==nil {return nil ,_ga .Error (_dbf ,"\u0073o\u0075r\u0063\u0065\u0020\u006e\u006ft\u0020\u0064e\u0066\u0069\u006e\u0065\u0064");
};if _ggb ==1{return _acda (nil ,_ddgf );};if _ggb !=2&&_ggb !=4&&_ggb !=8{return nil ,_ga .Error (_dbf ,"\u0066\u0061\u0063t\u006f\u0072\u0020\u006du\u0073\u0074\u0020\u0062\u0065\u0020\u0069n\u0020\u007b\u0032\u002c\u0034\u002c\u0038\u007d\u0020\u0072\u0061\u006e\u0067\u0065");
};_daa :=_ggb *_ddgf .Width ;_fda :=_ggb *_ddgf .Height ;_ff :=New (_daa ,_fda );var _aff error ;switch _ggb {case 2:_aff =_da (_ff ,_ddgf );case 4:_aff =_cd (_ff ,_ddgf );case 8:_aff =_ebd (_ff ,_ddgf );};if _aff !=nil {return nil ,_ga .Wrap (_aff ,_dbf ,"");
};return _ff ,nil ;};func (_cbca *Bitmaps )GetBox (i int )(*_c .Rectangle ,error ){const _acbg ="\u0047\u0065\u0074\u0042\u006f\u0078";if _cbca ==nil {return nil ,_ga .Error (_acbg ,"\u0070\u0072\u006f\u0076id\u0065\u0064\u0020\u006e\u0069\u006c\u0020\u0027\u0042\u0069\u0074\u006d\u0061\u0070s\u0027");
};if i > len (_cbca .Boxes )-1{return nil ,_ga .Errorf (_acbg ,"\u0069n\u0064\u0065\u0078\u003a\u0020\u0027\u0025\u0064\u0027\u0020\u006fu\u0074\u0020\u006f\u0066\u0020\u0072\u0061\u006e\u0067\u0065",i );};return _cbca .Boxes [i ],nil ;};func (_cef *Bitmap )addPadBits ()(_bfe error ){const _bcg ="\u0062\u0069\u0074\u006d\u0061\u0070\u002e\u0061\u0064\u0064\u0050\u0061d\u0042\u0069\u0074\u0073";
_fdgb :=_cef .Width %8;if _fdgb ==0{return nil ;};_aada :=_cef .Width /8;_fga :=_ed .NewReader (_cef .Data );_bac :=make ([]byte ,_cef .Height *_cef .RowStride );_gce :=_ed .NewWriterMSB (_bac );_ebg :=make ([]byte ,_aada );var (_ffcb int ;_cabc uint64 ;
);for _ffcb =0;_ffcb < _cef .Height ;_ffcb ++{if _ ,_bfe =_fga .Read (_ebg );_bfe !=nil {return _ga .Wrap (_bfe ,_bcg ,"\u0066u\u006c\u006c\u0020\u0062\u0079\u0074e");};if _ ,_bfe =_gce .Write (_ebg );_bfe !=nil {return _ga .Wrap (_bfe ,_bcg ,"\u0066\u0075\u006c\u006c\u0020\u0062\u0079\u0074\u0065\u0073");
};if _cabc ,_bfe =_fga .ReadBits (byte (_fdgb ));_bfe !=nil {return _ga .Wrap (_bfe ,_bcg ,"\u0073\u006b\u0069\u0070\u0070\u0069\u006e\u0067\u0020\u0062\u0069\u0074\u0073");};if _bfe =_gce .WriteByte (byte (_cabc )<<uint (8-_fdgb ));_bfe !=nil {return _ga .Wrap (_bfe ,_bcg ,"\u006ca\u0073\u0074\u0020\u0062\u0079\u0074e");
};};_cef .Data =_gce .Data ();return nil ;};func TstWSymbol (t *_f .T ,scale ...int )*Bitmap {_bbbb ,_dbad :=NewWithData (5,5,[]byte {0x88,0x88,0xA8,0xD8,0x88});_gb .NoError (t ,_dbad );return TstGetScaledSymbol (t ,_bbbb ,scale ...);};func (_bgeb *Bitmap )countPixels ()int {var (_daca int ;
_agdb uint8 ;_bea byte ;_ebbg int ;);_efc :=_bgeb .RowStride ;_feg :=uint (_bgeb .Width &0x07);if _feg !=0{_agdb =uint8 ((0xff<<(8-_feg ))&0xff);_efc --;};for _eadg :=0;_eadg < _bgeb .Height ;_eadg ++{for _ebbg =0;_ebbg < _efc ;_ebbg ++{_bea =_bgeb .Data [_eadg *_bgeb .RowStride +_ebbg ];
_daca +=int (_faab [_bea ]);};if _feg !=0{_daca +=int (_faab [_bgeb .Data [_eadg *_bgeb .RowStride +_ebbg ]&_agdb ]);};};return _daca ;};func (_beagc Points )GetIntX (i int )(int ,error ){if i >=len (_beagc ){return 0,_ga .Errorf ("\u0050\u006f\u0069\u006e\u0074\u0073\u002e\u0047\u0065t\u0049\u006e\u0074\u0058","\u0069n\u0064\u0065\u0078\u003a\u0020\u0027\u0025\u0064\u0027\u0020\u006fu\u0074\u0020\u006f\u0066\u0020\u0072\u0061\u006e\u0067\u0065",i );
};return int (_beagc [i ].X ),nil ;};var (_bcee =[]byte {0x00,0x80,0xC0,0xE0,0xF0,0xF8,0xFC,0xFE,0xFF};_bbaa =[]byte {0x00,0x01,0x03,0x07,0x0F,0x1F,0x3F,0x7F,0xFF};);func _dcgd (_ddeg ,_baffe *Bitmap ,_cccc ,_ecbc ,_ggf uint ,_gdee ,_eafb int ,_cgcb bool ,_cffc ,_acc int )error {for _cdeb :=_gdee ;
_cdeb < _eafb ;_cdeb ++{if _cffc +1< len (_ddeg .Data ){_ebga :=_cdeb +1==_eafb ;_ceed ,_geaba :=_ddeg .GetByte (_cffc );if _geaba !=nil {return _geaba ;};_cffc ++;_ceed <<=_cccc ;_adde ,_geaba :=_ddeg .GetByte (_cffc );if _geaba !=nil {return _geaba ;
};_adde >>=_ecbc ;_dedc :=_ceed |_adde ;if _ebga &&!_cgcb {_dedc =_dcfe (_ggf ,_dedc );};_geaba =_baffe .SetByte (_acc ,_dedc );if _geaba !=nil {return _geaba ;};_acc ++;if _ebga &&_cgcb {_bacf ,_bfaf :=_ddeg .GetByte (_cffc );if _bfaf !=nil {return _bfaf ;
};_bacf <<=_cccc ;_dedc =_dcfe (_ggf ,_bacf );if _bfaf =_baffe .SetByte (_acc ,_dedc );_bfaf !=nil {return _bfaf ;};};continue ;};_ddbc ,_cbfe :=_ddeg .GetByte (_cffc );if _cbfe !=nil {_bb .Log .Debug ("G\u0065\u0074\u0074\u0069\u006e\u0067 \u0074\u0068\u0065\u0020\u0076\u0061l\u0075\u0065\u0020\u0061\u0074\u003a\u0020%\u0064\u0020\u0066\u0061\u0069\u006c\u0065\u0064\u003a\u0020%\u0073",_cffc ,_cbfe );
return _cbfe ;};_ddbc <<=_cccc ;_cffc ++;_cbfe =_baffe .SetByte (_acc ,_ddbc );if _cbfe !=nil {return _cbfe ;};_acc ++;};return nil ;};func _dfeeb ()[]int {_gfed :=make ([]int ,256);for _ddaae :=0;_ddaae <=0xff;_ddaae ++{_bgcf :=byte (_ddaae );_gfed [_bgcf ]=int (_bgcf &0x1)+(int (_bgcf >>1)&0x1)+(int (_bgcf >>2)&0x1)+(int (_bgcf >>3)&0x1)+(int (_bgcf >>4)&0x1)+(int (_bgcf >>5)&0x1)+(int (_bgcf >>6)&0x1)+(int (_bgcf >>7)&0x1);
};return _gfed ;};func (_faea *ClassedPoints )GetIntYByClass (i int )(int ,error ){const _befgd ="\u0043\u006c\u0061\u0073s\u0065\u0064\u0050\u006f\u0069\u006e\u0074\u0073\u002e\u0047e\u0074I\u006e\u0074\u0059\u0042\u0079\u0043\u006ca\u0073\u0073";if i >=_faea .IntSlice .Size (){return 0,_ga .Errorf (_befgd ,"\u0069\u003a\u0020\u0027\u0025\u0064\u0027 \u0069\u0073\u0020o\u0075\u0074\u0020\u006ff\u0020\u0074\u0068\u0065\u0020\u0072\u0061\u006e\u0067\u0065\u0020\u006f\u0066\u0020\u0074\u0068\u0065\u0020\u0049\u006e\u0074\u0053\u006c\u0069\u0063\u0065",i );
};return int (_faea .YAtIndex (i )),nil ;};func _fbcc (_acged *_eb .Stack ,_afed ,_abga ,_cdcg ,_bgfg ,_fgea int ,_adebb *_c .Rectangle )(_feebf error ){const _dedgc ="\u0070\u0075\u0073\u0068\u0046\u0069\u006c\u006c\u0053\u0065\u0067m\u0065\u006e\u0074\u0042\u006f\u0075\u006e\u0064\u0069\u006eg\u0042\u006f\u0078";
if _acged ==nil {return _ga .Error (_dedgc ,"\u006ei\u006c \u0073\u0074\u0061\u0063\u006b \u0070\u0072o\u0076\u0069\u0064\u0065\u0064");};if _adebb ==nil {return _ga .Error (_dedgc ,"\u0070\u0072\u006f\u0076i\u0064\u0065\u0064\u0020\u006e\u0069\u006c\u0020\u0069\u006da\u0067e\u002e\u0052\u0065\u0063\u0074\u0061\u006eg\u006c\u0065");
};_adebb .Min .X =_eb .Min (_adebb .Min .X ,_afed );_adebb .Max .X =_eb .Max (_adebb .Max .X ,_abga );_adebb .Min .Y =_eb .Min (_adebb .Min .Y ,_cdcg );_adebb .Max .Y =_eb .Max (_adebb .Max .Y ,_cdcg );if !(_cdcg +_bgfg >=0&&_cdcg +_bgfg <=_fgea ){return nil ;
};if _acged .Aux ==nil {return _ga .Error (_dedgc ,"a\u0075x\u0053\u0074\u0061\u0063\u006b\u0020\u006e\u006ft\u0020\u0064\u0065\u0066in\u0065\u0064");};var _ebba *fillSegment ;_acbf ,_dbfa :=_acged .Aux .Pop ();if _dbfa {if _ebba ,_dbfa =_acbf .(*fillSegment );
!_dbfa {return _ga .Error (_dedgc ,"a\u0075\u0078\u0053\u0074\u0061\u0063k\u0020\u0064\u0061\u0074\u0061\u0020i\u0073\u0020\u006e\u006f\u0074\u0020\u0061 \u002a\u0066\u0069\u006c\u006c\u0053\u0065\u0067\u006d\u0065n\u0074");};}else {_ebba =&fillSegment {};
};_ebba ._effa =_afed ;_ebba ._bacba =_abga ;_ebba ._dcdf =_cdcg ;_ebba ._abbca =_bgfg ;_acged .Push (_ebba );return nil ;};func MorphSequence (src *Bitmap ,sequence ...MorphProcess )(*Bitmap ,error ){return _abdf (src ,sequence ...);};func TstWordBitmapWithSpaces (t *_f .T ,scale ...int )*Bitmap {_bdcd :=1;
if len (scale )> 0{_bdcd =scale [0];};_gcfc :=3;_ebafb :=9+7+15+2*_gcfc +2*_gcfc ;_cede :=5+_gcfc +5+2*_gcfc ;_agbfa :=New (_ebafb *_bdcd ,_cede *_bdcd );_eaeb :=&Bitmaps {};var _cdef *int ;_gcfc *=_bdcd ;_ebee :=_gcfc ;_cdef =&_ebee ;_caee :=_gcfc ;_bgafd :=TstDSymbol (t ,scale ...);
TstAddSymbol (t ,_eaeb ,_bgafd ,_cdef ,_caee ,1*_bdcd );_bgafd =TstOSymbol (t ,scale ...);TstAddSymbol (t ,_eaeb ,_bgafd ,_cdef ,_caee ,_gcfc );_bgafd =TstISymbol (t ,scale ...);TstAddSymbol (t ,_eaeb ,_bgafd ,_cdef ,_caee ,1*_bdcd );_bgafd =TstTSymbol (t ,scale ...);
TstAddSymbol (t ,_eaeb ,_bgafd ,_cdef ,_caee ,_gcfc );_bgafd =TstNSymbol (t ,scale ...);TstAddSymbol (t ,_eaeb ,_bgafd ,_cdef ,_caee ,1*_bdcd );_bgafd =TstOSymbol (t ,scale ...);TstAddSymbol (t ,_eaeb ,_bgafd ,_cdef ,_caee ,1*_bdcd );_bgafd =TstWSymbol (t ,scale ...);
TstAddSymbol (t ,_eaeb ,_bgafd ,_cdef ,_caee ,0);*_cdef =_gcfc ;_caee =5*_bdcd +_gcfc ;_bgafd =TstOSymbol (t ,scale ...);TstAddSymbol (t ,_eaeb ,_bgafd ,_cdef ,_caee ,1*_bdcd );_bgafd =TstRSymbol (t ,scale ...);TstAddSymbol (t ,_eaeb ,_bgafd ,_cdef ,_caee ,_gcfc );
_bgafd =TstNSymbol (t ,scale ...);TstAddSymbol (t ,_eaeb ,_bgafd ,_cdef ,_caee ,1*_bdcd );_bgafd =TstESymbol (t ,scale ...);TstAddSymbol (t ,_eaeb ,_bgafd ,_cdef ,_caee ,1*_bdcd );_bgafd =TstVSymbol (t ,scale ...);TstAddSymbol (t ,_eaeb ,_bgafd ,_cdef ,_caee ,1*_bdcd );
_bgafd =TstESymbol (t ,scale ...);TstAddSymbol (t ,_eaeb ,_bgafd ,_cdef ,_caee ,1*_bdcd );_bgafd =TstRSymbol (t ,scale ...);TstAddSymbol (t ,_eaeb ,_bgafd ,_cdef ,_caee ,0);TstWriteSymbols (t ,_eaeb ,_agbfa );return _agbfa ;};func (_cceg *Bitmap )Equivalent (s *Bitmap )bool {return _cceg .equivalent (s )};
func (_bfcaf *Bitmaps )AddBitmap (bm *Bitmap ){_bfcaf .Values =append (_bfcaf .Values ,bm )};type SizeSelection int ;func _eedg (_cfed ,_acec *Bitmap ,_dfad ,_gbfg ,_beaa ,_affc ,_cagb ,_adb ,_ddbg ,_efcd int ,_def CombinationOperator ,_dcaf int )error {var _beag int ;
_fefd :=func (){_beag ++;_beaa +=_acec .RowStride ;_affc +=_cfed .RowStride ;_cagb +=_cfed .RowStride };for _beag =_dfad ;_beag < _gbfg ;_fefd (){var _bbb uint16 ;_edac :=_beaa ;for _bdfc :=_affc ;_bdfc <=_cagb ;_bdfc ++{_bcgfc ,_gbca :=_acec .GetByte (_edac );
if _gbca !=nil {return _gbca ;};_fecd ,_gbca :=_cfed .GetByte (_bdfc );if _gbca !=nil {return _gbca ;};_bbb =(_bbb |(uint16 (_fecd )&0xff))<<uint (_efcd );_fecd =byte (_bbb >>8);if _gbca =_acec .SetByte (_edac ,_ggde (_bcgfc ,_fecd ,_def ));_gbca !=nil {return _gbca ;
};_edac ++;_bbb <<=uint (_ddbg );if _bdfc ==_cagb {_fecd =byte (_bbb >>(8-uint8 (_efcd )));if _dcaf !=0{_fecd =_dcfe (uint (8+_adb ),_fecd );};_bcgfc ,_gbca =_acec .GetByte (_edac );if _gbca !=nil {return _gbca ;};if _gbca =_acec .SetByte (_edac ,_ggde (_bcgfc ,_fecd ,_def ));
_gbca !=nil {return _gbca ;};};};};return nil ;};func (_dedf *Bitmap )setBit (_ddaa int ){_dedf .Data [(_ddaa >>3)]|=0x80>>uint (_ddaa &7)};func _dag (_gafc ,_ea *Bitmap ,_deg int ,_eae []byte ,_gfc int )(_ccd error ){const _gadd ="\u0072\u0065\u0064uc\u0065\u0052\u0061\u006e\u006b\u0042\u0069\u006e\u0061\u0072\u0079\u0032\u004c\u0065\u0076\u0065\u006c\u0032";
var (_eeff ,_dfb ,_acg ,_fg ,_fcgc ,_fab ,_bag ,_gfa int ;_fbge ,_gga ,_ecd ,_ecf uint32 ;_dga ,_fed byte ;_cgb uint16 ;);_acgd :=make ([]byte ,4);_dfbc :=make ([]byte ,4);for _acg =0;_acg < _gafc .Height -1;_acg ,_fg =_acg +2,_fg +1{_eeff =_acg *_gafc .RowStride ;
_dfb =_fg *_ea .RowStride ;for _fcgc ,_fab =0,0;_fcgc < _gfc ;_fcgc ,_fab =_fcgc +4,_fab +1{for _bag =0;_bag < 4;_bag ++{_gfa =_eeff +_fcgc +_bag ;if _gfa <=len (_gafc .Data )-1&&_gfa < _eeff +_gafc .RowStride {_acgd [_bag ]=_gafc .Data [_gfa ];}else {_acgd [_bag ]=0x00;
};_gfa =_eeff +_gafc .RowStride +_fcgc +_bag ;if _gfa <=len (_gafc .Data )-1&&_gfa < _eeff +(2*_gafc .RowStride ){_dfbc [_bag ]=_gafc .Data [_gfa ];}else {_dfbc [_bag ]=0x00;};};_fbge =_gg .BigEndian .Uint32 (_acgd );_gga =_gg .BigEndian .Uint32 (_dfbc );
_ecd =_fbge &_gga ;_ecd |=_ecd <<1;_ecf =_fbge |_gga ;_ecf &=_ecf <<1;_gga =_ecd |_ecf ;_gga &=0xaaaaaaaa;_fbge =_gga |(_gga <<7);_dga =byte (_fbge >>24);_fed =byte ((_fbge >>8)&0xff);_gfa =_dfb +_fab ;if _gfa +1==len (_ea .Data )-1||_gfa +1>=_dfb +_ea .RowStride {if _ccd =_ea .SetByte (_gfa ,_eae [_dga ]);
_ccd !=nil {return _ga .Wrapf (_ccd ,_gadd ,"\u0069n\u0064\u0065\u0078\u003a\u0020\u0025d",_gfa );};}else {_cgb =(uint16 (_eae [_dga ])<<8)|uint16 (_eae [_fed ]);if _ccd =_ea .setTwoBytes (_gfa ,_cgb );_ccd !=nil {return _ga .Wrapf (_ccd ,_gadd ,"s\u0065\u0074\u0074\u0069\u006e\u0067 \u0074\u0077\u006f\u0020\u0062\u0079t\u0065\u0073\u0020\u0066\u0061\u0069\u006ce\u0064\u002c\u0020\u0069\u006e\u0064\u0065\u0078\u003a\u0020%\u0064",_gfa );
};_fab ++;};};};return nil ;};func (_dbd *Bitmap )SetPadBits (value int ){_dbd .setPadBits (value )};func (_eed *Bitmap )AddBorderGeneral (left ,right ,top ,bot int ,val int )(*Bitmap ,error ){return _eed .addBorderGeneral (left ,right ,top ,bot ,val );
};func (_ebcfe *Bitmap )clipRectangle (_afgb ,_ebcd *_c .Rectangle )(_cfb *Bitmap ,_ebea error ){const _geab ="\u0063\u006c\u0069\u0070\u0052\u0065\u0063\u0074\u0061\u006e\u0067\u006c\u0065";if _afgb ==nil {return nil ,_ga .Error (_geab ,"\u0070r\u006fv\u0069\u0064\u0065\u0064\u0020n\u0069\u006c \u0027\u0062\u006f\u0078\u0027");
};_egcg ,_bbc :=_ebcfe .Width ,_ebcfe .Height ;_aeg ,_ebea :=ClipBoxToRectangle (_afgb ,_egcg ,_bbc );if _ebea !=nil {_bb .Log .Warning ("\u0027\u0062ox\u0027\u0020\u0064o\u0065\u0073\u006e\u0027t o\u0076er\u006c\u0061\u0070\u0020\u0062\u0069\u0074ma\u0070\u0020\u0027\u0062\u0027\u003a\u0020%\u0076",_ebea );
return nil ,nil ;};_bae ,_bdg :=_aeg .Min .X ,_aeg .Min .Y ;_ccdc ,_fffb :=_aeg .Max .X -_aeg .Min .X ,_aeg .Max .Y -_aeg .Min .Y ;_cfb =New (_ccdc ,_fffb );_cfb .Text =_ebcfe .Text ;if _ebea =_cfb .RasterOperation (0,0,_ccdc ,_fffb ,PixSrc ,_ebcfe ,_bae ,_bdg );
_ebea !=nil {return nil ,_ga .Wrap (_ebea ,_geab ,"");};if _ebcd !=nil {*_ebcd =*_aeg ;};return _cfb ,nil ;};func _gcgca (_gbaf ,_gdbaa ,_afbc *Bitmap ,_bafb int )(*Bitmap ,error ){const _agdd ="\u0073\u0065\u0065\u0064\u0046\u0069\u006c\u006c\u0042i\u006e\u0061\u0072\u0079";
if _gdbaa ==nil {return nil ,_ga .Error (_agdd ,"s\u006fu\u0072\u0063\u0065\u0020\u0062\u0069\u0074\u006da\u0070\u0020\u0069\u0073 n\u0069\u006c");};if _afbc ==nil {return nil ,_ga .Error (_agdd ,"'\u006da\u0073\u006b\u0027\u0020\u0062\u0069\u0074\u006da\u0070\u0020\u0069\u0073 n\u0069\u006c");
};if _bafb !=4&&_bafb !=8{return nil ,_ga .Error (_agdd ,"\u0063\u006f\u006en\u0065\u0063\u0074\u0069v\u0069\u0074\u0079\u0020\u006e\u006f\u0074 \u0069\u006e\u0020\u0072\u0061\u006e\u0067\u0065\u0020\u007b\u0034\u002c\u0038\u007d");};var _gebe error ;_gbaf ,_gebe =_acda (_gbaf ,_gdbaa );
if _gebe !=nil {return nil ,_ga .Wrap (_gebe ,_agdd ,"\u0063o\u0070y\u0020\u0073\u006f\u0075\u0072c\u0065\u0020t\u006f\u0020\u0027\u0064\u0027");};_ebac :=_gdbaa .createTemplate ();_afbc .setPadBits (0);for _agafc :=0;_agafc < _fefee ;_agafc ++{_ebac ,_gebe =_acda (_ebac ,_gbaf );
if _gebe !=nil {return nil ,_ga .Wrapf (_gebe ,_agdd ,"\u0069\u0074\u0065\u0072\u0061\u0074\u0069\u006f\u006e\u003a\u0020\u0025\u0064",_agafc );};if _gebe =_efbg (_gbaf ,_afbc ,_bafb );_gebe !=nil {return nil ,_ga .Wrapf (_gebe ,_agdd ,"\u0069\u0074\u0065\u0072\u0061\u0074\u0069\u006f\u006e\u003a\u0020\u0025\u0064",_agafc );
};if _ebac .Equals (_gbaf ){break ;};};return _gbaf ,nil ;};func (_gfdf *Bitmap )connComponentsBB (_ebfe int )(_caeb *Boxes ,_aabb error ){const _ddbd ="\u0042\u0069\u0074ma\u0070\u002e\u0063\u006f\u006e\u006e\u0043\u006f\u006d\u0070\u006f\u006e\u0065\u006e\u0074\u0073\u0042\u0042";
if _ebfe !=4&&_ebfe !=8{return nil ,_ga .Error (_ddbd ,"\u0063\u006f\u006e\u006e\u0065\u0063t\u0069\u0076\u0069\u0074\u0079\u0020\u006d\u0075\u0073\u0074\u0020\u0062\u0065 \u0061\u0020\u0027\u0034\u0027\u0020\u006fr\u0020\u0027\u0038\u0027");};if _gfdf .Zero (){return &Boxes {},nil ;
};_gfdf .setPadBits (0);_bggd ,_aabb :=_acda (nil ,_gfdf );if _aabb !=nil {return nil ,_ga .Wrap (_aabb ,_ddbd ,"\u0062\u006d\u0031");};_gfdb :=&_eb .Stack {};_gfdb .Aux =&_eb .Stack {};_caeb =&Boxes {};var (_gfgc ,_abe int ;_bcf _c .Point ;_egaf bool ;
_acef *_c .Rectangle ;);for {if _bcf ,_egaf ,_aabb =_bggd .nextOnPixel (_abe ,_gfgc );_aabb !=nil {return nil ,_ga .Wrap (_aabb ,_ddbd ,"");};if !_egaf {break ;};if _acef ,_aabb =_gfba (_bggd ,_gfdb ,_bcf .X ,_bcf .Y ,_ebfe );_aabb !=nil {return nil ,_ga .Wrap (_aabb ,_ddbd ,"");
};if _aabb =_caeb .Add (_acef );_aabb !=nil {return nil ,_ga .Wrap (_aabb ,_ddbd ,"");};_abe =_bcf .X ;_gfgc =_bcf .Y ;};return _caeb ,nil ;};func (_bfccb *Boxes )makeSizeIndicator (_affg ,_dcb int ,_aaed LocationFilter ,_eag SizeComparison )*_eb .NumSlice {_bacb :=&_eb .NumSlice {};
var _egee ,_cfgg ,_cfbf int ;for _ ,_ceac :=range *_bfccb {_egee =0;_cfgg ,_cfbf =_ceac .Dx (),_ceac .Dy ();switch _aaed {case LocSelectWidth :if (_eag ==SizeSelectIfLT &&_cfgg < _affg )||(_eag ==SizeSelectIfGT &&_cfgg > _affg )||(_eag ==SizeSelectIfLTE &&_cfgg <=_affg )||(_eag ==SizeSelectIfGTE &&_cfgg >=_affg ){_egee =1;
};case LocSelectHeight :if (_eag ==SizeSelectIfLT &&_cfbf < _dcb )||(_eag ==SizeSelectIfGT &&_cfbf > _dcb )||(_eag ==SizeSelectIfLTE &&_cfbf <=_dcb )||(_eag ==SizeSelectIfGTE &&_cfbf >=_dcb ){_egee =1;};case LocSelectIfEither :if (_eag ==SizeSelectIfLT &&(_cfbf < _dcb ||_cfgg < _affg ))||(_eag ==SizeSelectIfGT &&(_cfbf > _dcb ||_cfgg > _affg ))||(_eag ==SizeSelectIfLTE &&(_cfbf <=_dcb ||_cfgg <=_affg ))||(_eag ==SizeSelectIfGTE &&(_cfbf >=_dcb ||_cfgg >=_affg )){_egee =1;
};case LocSelectIfBoth :if (_eag ==SizeSelectIfLT &&(_cfbf < _dcb &&_cfgg < _affg ))||(_eag ==SizeSelectIfGT &&(_cfbf > _dcb &&_cfgg > _affg ))||(_eag ==SizeSelectIfLTE &&(_cfbf <=_dcb &&_cfgg <=_affg ))||(_eag ==SizeSelectIfGTE &&(_cfbf >=_dcb &&_cfgg >=_affg )){_egee =1;
};};_bacb .AddInt (_egee );};return _bacb ;};func (_cgdea *BitmapsArray )GetBox (i int )(*_c .Rectangle ,error ){const _gegeg ="\u0042\u0069\u0074\u006dap\u0073\u0041\u0072\u0072\u0061\u0079\u002e\u0047\u0065\u0074\u0042\u006f\u0078";if _cgdea ==nil {return nil ,_ga .Error (_gegeg ,"p\u0072\u006f\u0076\u0069\u0064\u0065d\u0020\u006e\u0069\u006c\u0020\u0027\u0042\u0069\u0074m\u0061\u0070\u0073A\u0072r\u0061\u0079\u0027");
};if i > len (_cgdea .Boxes )-1{return nil ,_ga .Errorf (_gegeg ,"\u0069n\u0064\u0065\u0078\u003a\u0020\u0027\u0025\u0064\u0027\u0020\u006fu\u0074\u0020\u006f\u0066\u0020\u0072\u0061\u006e\u0067\u0065",i );};return _cgdea .Boxes [i ],nil ;};func _gfba (_gfeg *Bitmap ,_bafe *_eb .Stack ,_gaec ,_eefb ,_baaf int )(_fbdcg *_c .Rectangle ,_fcea error ){const _egae ="\u0073e\u0065d\u0046\u0069\u006c\u006c\u0053\u0074\u0061\u0063\u006b\u0042\u0042";
if _gfeg ==nil {return nil ,_ga .Error (_egae ,"\u0070\u0072\u006fvi\u0064\u0065\u0064\u0020\u006e\u0069\u006c\u0020\u0027\u0073\u0027\u0020\u0042\u0069\u0074\u006d\u0061\u0070");};if _bafe ==nil {return nil ,_ga .Error (_egae ,"p\u0072o\u0076\u0069\u0064\u0065\u0064\u0020\u006e\u0069l\u0020\u0027\u0073\u0074ac\u006b\u0027");
};switch _baaf {case 4:if _fbdcg ,_fcea =_aadgd (_gfeg ,_bafe ,_gaec ,_eefb );_fcea !=nil {return nil ,_ga .Wrap (_fcea ,_egae ,"");};return _fbdcg ,nil ;case 8:if _fbdcg ,_fcea =_cabbg (_gfeg ,_bafe ,_gaec ,_eefb );_fcea !=nil {return nil ,_ga .Wrap (_fcea ,_egae ,"");
};return _fbdcg ,nil ;default:return nil ,_ga .Errorf (_egae ,"\u0063\u006f\u006e\u006e\u0065\u0063\u0074\u0069\u0076\u0069\u0074\u0079\u0020\u0069\u0073 \u006eo\u0074\u0020\u0034\u0020\u006f\u0072\u0020\u0038\u003a\u0020\u0027\u0025\u0064\u0027",_baaf );
};};func (_aeaee *Bitmaps )CountPixels ()*_eb .NumSlice {_dceb :=&_eb .NumSlice {};for _ ,_bbec :=range _aeaee .Values {_dceb .AddInt (_bbec .CountPixels ());};return _dceb ;};func New (width ,height int )*Bitmap {_bbef :=_fbgc (width ,height );_bbef .Data =make ([]byte ,height *_bbef .RowStride );
return _bbef ;};type LocationFilter int ;func _aacc (_bddeb ,_egce ,_fcgb byte )byte {return (_bddeb &^(_fcgb ))|(_egce &_fcgb )};func (_acd *Bitmap )Equals (s *Bitmap )bool {if len (_acd .Data )!=len (s .Data )||_acd .Width !=s .Width ||_acd .Height !=s .Height {return false ;
};for _cdba :=0;_cdba < _acd .Height ;_cdba ++{_ead :=_cdba *_acd .RowStride ;for _geag :=0;_geag < _acd .RowStride ;_geag ++{if _acd .Data [_ead +_geag ]!=s .Data [_ead +_geag ]{return false ;};};};return true ;};type Component int ;func (_bda *Bitmap )RemoveBorderGeneral (left ,right ,top ,bot int )(*Bitmap ,error ){return _bda .removeBorderGeneral (left ,right ,top ,bot );
};func TstVSymbol (t *_f .T ,scale ...int )*Bitmap {_bdgb ,_dffc :=NewWithData (5,5,[]byte {0x88,0x88,0x88,0x50,0x20});_gb .NoError (t ,_dffc );return TstGetScaledSymbol (t ,_bdgb ,scale ...);};func _bdbad (_egda *Bitmap ,_bfed ,_fefc int ,_cfbe ,_eaee int ,_ecge RasterOperator ){var (_adfc int ;
_bbea byte ;_gbffb ,_aec int ;_bacc int ;);_dedcc :=_cfbe >>3;_affe :=_cfbe &7;if _affe > 0{_bbea =_bcee [_affe ];};_adfc =_egda .RowStride *_fefc +(_bfed >>3);switch _ecge {case PixClr :for _gbffb =0;_gbffb < _eaee ;_gbffb ++{_bacc =_adfc +_gbffb *_egda .RowStride ;
for _aec =0;_aec < _dedcc ;_aec ++{_egda .Data [_bacc ]=0x0;_bacc ++;};if _affe > 0{_egda .Data [_bacc ]=_aacc (_egda .Data [_bacc ],0x0,_bbea );};};case PixSet :for _gbffb =0;_gbffb < _eaee ;_gbffb ++{_bacc =_adfc +_gbffb *_egda .RowStride ;for _aec =0;
_aec < _dedcc ;_aec ++{_egda .Data [_bacc ]=0xff;_bacc ++;};if _affe > 0{_egda .Data [_bacc ]=_aacc (_egda .Data [_bacc ],0xff,_bbea );};};case PixNotDst :for _gbffb =0;_gbffb < _eaee ;_gbffb ++{_bacc =_adfc +_gbffb *_egda .RowStride ;for _aec =0;_aec < _dedcc ;
_aec ++{_egda .Data [_bacc ]=^_egda .Data [_bacc ];_bacc ++;};if _affe > 0{_egda .Data [_bacc ]=_aacc (_egda .Data [_bacc ],^_egda .Data [_bacc ],_bbea );};};};};const (_gbbde shift =iota ;_gcfgf ;);func SelCreateBrick (h ,w int ,cy ,cx int ,tp SelectionValue )*Selection {_dbacb :=_ebfdd (h ,w ,"");
_dbacb .setOrigin (cy ,cx );var _bgd ,_fbefd int ;for _bgd =0;_bgd < h ;_bgd ++{for _fbefd =0;_fbefd < w ;_fbefd ++{_dbacb .Data [_bgd ][_fbefd ]=tp ;};};return _dbacb ;};func (_adga Points )GetIntY (i int )(int ,error ){if i >=len (_adga ){return 0,_ga .Errorf ("\u0050\u006f\u0069\u006e\u0074\u0073\u002e\u0047\u0065t\u0049\u006e\u0074\u0059","\u0069n\u0064\u0065\u0078\u003a\u0020\u0027\u0025\u0064\u0027\u0020\u006fu\u0074\u0020\u006f\u0066\u0020\u0072\u0061\u006e\u0067\u0065",i );
};return int (_adga [i ].Y ),nil ;};func CorrelationScore (bm1 ,bm2 *Bitmap ,area1 ,area2 int ,delX ,delY float32 ,maxDiffW ,maxDiffH int ,tab []int )(_daed float64 ,_fgaf error ){const _fcbd ="\u0063\u006fr\u0072\u0065\u006ca\u0074\u0069\u006f\u006e\u0053\u0063\u006f\u0072\u0065";
if bm1 ==nil ||bm2 ==nil {return 0,_ga .Error (_fcbd ,"p\u0072o\u0076\u0069\u0064\u0065\u0064\u0020\u006e\u0069l\u0020\u0062\u0069\u0074ma\u0070\u0073");};if tab ==nil {return 0,_ga .Error (_fcbd ,"\u0027\u0074\u0061\u0062\u0027\u0020\u006e\u006f\u0074\u0020\u0064\u0065f\u0069\u006e\u0065\u0064");
};if area1 <=0||area2 <=0{return 0,_ga .Error (_fcbd ,"\u0061\u0072\u0065\u0061s\u0020\u006d\u0075\u0073\u0074\u0020\u0062\u0065\u0020\u0067r\u0065a\u0074\u0065\u0072\u0020\u0074\u0068\u0061n\u0020\u0030");};_bfg ,_eafbf :=bm1 .Width ,bm1 .Height ;_bafd ,_eafd :=bm2 .Width ,bm2 .Height ;
_ggcc :=_agfg (_bfg -_bafd );if _ggcc > maxDiffW {return 0,nil ;};_dfef :=_agfg (_eafbf -_eafd );if _dfef > maxDiffH {return 0,nil ;};var _cgca ,_fgbdc int ;if delX >=0{_cgca =int (delX +0.5);}else {_cgca =int (delX -0.5);};if delY >=0{_fgbdc =int (delY +0.5);
}else {_fgbdc =int (delY -0.5);};_aegg :=_fde (_fgbdc ,0);_gaca :=_dfce (_eafd +_fgbdc ,_eafbf );_gccf :=bm1 .RowStride *_aegg ;_feaeb :=bm2 .RowStride *(_aegg -_fgbdc );_eeb :=_fde (_cgca ,0);_decc :=_dfce (_bafd +_cgca ,_bfg );_acbc :=bm2 .RowStride ;
var _eaa ,_edaf int ;if _cgca >=8{_eaa =_cgca >>3;_gccf +=_eaa ;_eeb -=_eaa <<3;_decc -=_eaa <<3;_cgca &=7;}else if _cgca <=-8{_edaf =-((_cgca +7)>>3);_feaeb +=_edaf ;_acbc -=_edaf ;_cgca +=_edaf <<3;};if _eeb >=_decc ||_aegg >=_gaca {return 0,nil ;};_dedd :=(_decc +7)>>3;
var (_fdab ,_cbgb ,_bdfg byte ;_egbb ,_dega ,_dafc int ;);switch {case _cgca ==0:for _dafc =_aegg ;_dafc < _gaca ;_dafc ,_gccf ,_feaeb =_dafc +1,_gccf +bm1 .RowStride ,_feaeb +bm2 .RowStride {for _dega =0;_dega < _dedd ;_dega ++{_bdfg =bm1 .Data [_gccf +_dega ]&bm2 .Data [_feaeb +_dega ];
_egbb +=tab [_bdfg ];};};case _cgca > 0:if _acbc < _dedd {for _dafc =_aegg ;_dafc < _gaca ;_dafc ,_gccf ,_feaeb =_dafc +1,_gccf +bm1 .RowStride ,_feaeb +bm2 .RowStride {_fdab ,_cbgb =bm1 .Data [_gccf ],bm2 .Data [_feaeb ]>>uint (_cgca );_bdfg =_fdab &_cbgb ;
_egbb +=tab [_bdfg ];for _dega =1;_dega < _acbc ;_dega ++{_fdab ,_cbgb =bm1 .Data [_gccf +_dega ],(bm2 .Data [_feaeb +_dega ]>>uint (_cgca ))|(bm2 .Data [_feaeb +_dega -1]<<uint (8-_cgca ));_bdfg =_fdab &_cbgb ;_egbb +=tab [_bdfg ];};_fdab =bm1 .Data [_gccf +_dega ];
_cbgb =bm2 .Data [_feaeb +_dega -1]<<uint (8-_cgca );_bdfg =_fdab &_cbgb ;_egbb +=tab [_bdfg ];};}else {for _dafc =_aegg ;_dafc < _gaca ;_dafc ,_gccf ,_feaeb =_dafc +1,_gccf +bm1 .RowStride ,_feaeb +bm2 .RowStride {_fdab ,_cbgb =bm1 .Data [_gccf ],bm2 .Data [_feaeb ]>>uint (_cgca );
_bdfg =_fdab &_cbgb ;_egbb +=tab [_bdfg ];for _dega =1;_dega < _dedd ;_dega ++{_fdab =bm1 .Data [_gccf +_dega ];_cbgb =(bm2 .Data [_feaeb +_dega ]>>uint (_cgca ))|(bm2 .Data [_feaeb +_dega -1]<<uint (8-_cgca ));_bdfg =_fdab &_cbgb ;_egbb +=tab [_bdfg ];
};};};default:if _dedd < _acbc {for _dafc =_aegg ;_dafc < _gaca ;_dafc ,_gccf ,_feaeb =_dafc +1,_gccf +bm1 .RowStride ,_feaeb +bm2 .RowStride {for _dega =0;_dega < _dedd ;_dega ++{_fdab =bm1 .Data [_gccf +_dega ];_cbgb =bm2 .Data [_feaeb +_dega ]<<uint (-_cgca );
_cbgb |=bm2 .Data [_feaeb +_dega +1]>>uint (8+_cgca );_bdfg =_fdab &_cbgb ;_egbb +=tab [_bdfg ];};};}else {for _dafc =_aegg ;_dafc < _gaca ;_dafc ,_gccf ,_feaeb =_dafc +1,_gccf +bm1 .RowStride ,_feaeb +bm2 .RowStride {for _dega =0;_dega < _dedd -1;_dega ++{_fdab =bm1 .Data [_gccf +_dega ];
_cbgb =bm2 .Data [_feaeb +_dega ]<<uint (-_cgca );_cbgb |=bm2 .Data [_feaeb +_dega +1]>>uint (8+_cgca );_bdfg =_fdab &_cbgb ;_egbb +=tab [_bdfg ];};_fdab =bm1 .Data [_gccf +_dega ];_cbgb =bm2 .Data [_feaeb +_dega ]<<uint (-_cgca );_bdfg =_fdab &_cbgb ;
_egbb +=tab [_bdfg ];};};};_daed =float64 (_egbb )*float64 (_egbb )/(float64 (area1 )*float64 (area2 ));return _daed ,nil ;};func _bbff (_adbb ,_cacf *Bitmap ,_fcegf *Selection )(*Bitmap ,error ){const _babe ="\u0065\u0072\u006fd\u0065";var (_agcd error ;
_gedf *Bitmap ;);_adbb ,_agcd =_aafd (_adbb ,_cacf ,_fcegf ,&_gedf );if _agcd !=nil {return nil ,_ga .Wrap (_agcd ,_babe ,"");};if _agcd =_adbb .setAll ();_agcd !=nil {return nil ,_ga .Wrap (_agcd ,_babe ,"");};var _affdf SelectionValue ;for _dgff :=0;
_dgff < _fcegf .Height ;_dgff ++{for _bdag :=0;_bdag < _fcegf .Width ;_bdag ++{_affdf =_fcegf .Data [_dgff ][_bdag ];if _affdf ==SelHit {_agcd =_bebe (_adbb ,_fcegf .Cx -_bdag ,_fcegf .Cy -_dgff ,_cacf .Width ,_cacf .Height ,PixSrcAndDst ,_gedf ,0,0);if _agcd !=nil {return nil ,_ga .Wrap (_agcd ,_babe ,"");
};};};};if MorphBC ==SymmetricMorphBC {return _adbb ,nil ;};_ffef ,_baea ,_ffdcb ,_ddfgg :=_fcegf .findMaxTranslations ();if _ffef > 0{if _agcd =_adbb .RasterOperation (0,0,_ffef ,_cacf .Height ,PixClr ,nil ,0,0);_agcd !=nil {return nil ,_ga .Wrap (_agcd ,_babe ,"\u0078\u0070\u0020\u003e\u0020\u0030");
};};if _ffdcb > 0{if _agcd =_adbb .RasterOperation (_cacf .Width -_ffdcb ,0,_ffdcb ,_cacf .Height ,PixClr ,nil ,0,0);_agcd !=nil {return nil ,_ga .Wrap (_agcd ,_babe ,"\u0078\u006e\u0020\u003e\u0020\u0030");};};if _baea > 0{if _agcd =_adbb .RasterOperation (0,0,_cacf .Width ,_baea ,PixClr ,nil ,0,0);
_agcd !=nil {return nil ,_ga .Wrap (_agcd ,_babe ,"\u0079\u0070\u0020\u003e\u0020\u0030");};};if _ddfgg > 0{if _agcd =_adbb .RasterOperation (0,_cacf .Height -_ddfgg ,_cacf .Width ,_ddfgg ,PixClr ,nil ,0,0);_agcd !=nil {return nil ,_ga .Wrap (_agcd ,_babe ,"\u0079\u006e\u0020\u003e\u0020\u0030");
};};return _adbb ,nil ;};func (_cbab *Points )Add (pt *Points )error {const _agda ="\u0050\u006f\u0069\u006e\u0074\u0073\u002e\u0041\u0064\u0064";if _cbab ==nil {return _ga .Error (_agda ,"\u0070o\u0069n\u0074\u0073\u0020\u006e\u006ft\u0020\u0064e\u0066\u0069\u006e\u0065\u0064");
};if pt ==nil {return _ga .Error (_agda ,"a\u0072\u0067\u0075\u006d\u0065\u006et\u0020\u0070\u006f\u0069\u006e\u0074\u0073\u0020\u006eo\u0074\u0020\u0064e\u0066i\u006e\u0065\u0064");};*_cbab =append (*_cbab ,*pt ...);return nil ;};func _bfefg (_baeg *Bitmap ,_ccfe ,_afbda ,_efefc ,_eeaf int ,_cbdg RasterOperator ){if _ccfe < 0{_efefc +=_ccfe ;
_ccfe =0;};_gead :=_ccfe +_efefc -_baeg .Width ;if _gead > 0{_efefc -=_gead ;};if _afbda < 0{_eeaf +=_afbda ;_afbda =0;};_cfca :=_afbda +_eeaf -_baeg .Height ;if _cfca > 0{_eeaf -=_cfca ;};if _efefc <=0||_eeaf <=0{return ;};if (_ccfe &7)==0{_bdbad (_baeg ,_ccfe ,_afbda ,_efefc ,_eeaf ,_cbdg );
}else {_fabed (_baeg ,_ccfe ,_afbda ,_efefc ,_eeaf ,_cbdg );};};func _dagc (_fgg ,_bffc *Bitmap ,_dee ,_bcfc int )(*Bitmap ,error ){const _bage ="\u0063\u006c\u006f\u0073\u0065\u0042\u0072\u0069\u0063\u006b";if _bffc ==nil {return nil ,_ga .Error (_bage ,"\u0073o\u0075r\u0063\u0065\u0020\u006e\u006ft\u0020\u0064e\u0066\u0069\u006e\u0065\u0064");
};if _dee < 1||_bcfc < 1{return nil ,_ga .Error (_bage ,"\u0068S\u0069\u007a\u0065\u0020\u0061\u006e\u0064\u0020\u0076\u0053\u0069z\u0065\u0020\u006e\u006f\u0074\u0020\u003e\u003d\u0020\u0031");};if _dee ==1&&_bcfc ==1{return _bffc .Copy (),nil ;};if _dee ==1||_bcfc ==1{_gaga :=SelCreateBrick (_bcfc ,_dee ,_bcfc /2,_dee /2,SelHit );
var _bbca error ;_fgg ,_bbca =_bcad (_fgg ,_bffc ,_gaga );if _bbca !=nil {return nil ,_ga .Wrap (_bbca ,_bage ,"\u0068S\u0069\u007a\u0065\u0020\u003d\u003d\u0020\u0031\u0020\u007c\u007c \u0076\u0053\u0069\u007a\u0065\u0020\u003d\u003d\u0020\u0031");};return _fgg ,nil ;
};_afbaf :=SelCreateBrick (1,_dee ,0,_dee /2,SelHit );_gedbf :=SelCreateBrick (_bcfc ,1,_bcfc /2,0,SelHit );_afge ,_bfgd :=_bgbfc (nil ,_bffc ,_afbaf );if _bfgd !=nil {return nil ,_ga .Wrap (_bfgd ,_bage ,"\u0031\u0073\u0074\u0020\u0064\u0069\u006c\u0061\u0074\u0065");
};if _fgg ,_bfgd =_bgbfc (_fgg ,_afge ,_gedbf );_bfgd !=nil {return nil ,_ga .Wrap (_bfgd ,_bage ,"\u0032\u006e\u0064\u0020\u0064\u0069\u006c\u0061\u0074\u0065");};if _ ,_bfgd =_bbff (_afge ,_fgg ,_afbaf );_bfgd !=nil {return nil ,_ga .Wrap (_bfgd ,_bage ,"\u0031s\u0074\u0020\u0065\u0072\u006f\u0064e");
};if _ ,_bfgd =_bbff (_fgg ,_afge ,_gedbf );_bfgd !=nil {return nil ,_ga .Wrap (_bfgd ,_bage ,"\u0032n\u0064\u0020\u0065\u0072\u006f\u0064e");};return _fgg ,nil ;};func _agfg (_ggag int )int {if _ggag < 0{return -_ggag ;};return _ggag ;};func _afgbc (_gbbd ,_ceb *Bitmap ,_daba ,_befbg int )(*Bitmap ,error ){const _defe ="\u0063\u006c\u006f\u0073\u0065\u0053\u0061\u0066\u0065B\u0072\u0069\u0063\u006b";
if _ceb ==nil {return nil ,_ga .Error (_defe ,"\u0073\u006f\u0075\u0072\u0063\u0065\u0020\u0069\u0073\u0020\u006e\u0069\u006c");};if _daba < 1||_befbg < 1{return nil ,_ga .Error (_defe ,"\u0068s\u0069\u007a\u0065\u0020\u0061\u006e\u0064\u0020\u0076\u0073\u0069z\u0065\u0020\u006e\u006f\u0074\u0020\u003e\u003d\u0020\u0031");
};if _daba ==1&&_befbg ==1{return _acda (_gbbd ,_ceb );};if MorphBC ==SymmetricMorphBC {_dabd ,_bbfc :=_dagc (_gbbd ,_ceb ,_daba ,_befbg );if _bbfc !=nil {return nil ,_ga .Wrap (_bbfc ,_defe ,"\u0053\u0079m\u006d\u0065\u0074r\u0069\u0063\u004d\u006f\u0072\u0070\u0068\u0042\u0043");
};return _dabd ,nil ;};_dfeff :=_fde (_daba /2,_befbg /2);_fgff :=8*((_dfeff +7)/8);_aadf ,_degb :=_ceb .AddBorder (_fgff ,0);if _degb !=nil {return nil ,_ga .Wrapf (_degb ,_defe ,"\u0042\u006f\u0072\u0064\u0065\u0072\u0053\u0069\u007ae\u003a\u0020\u0025\u0064",_fgff );
};var _caea ,_geabe *Bitmap ;if _daba ==1||_befbg ==1{_bccf :=SelCreateBrick (_befbg ,_daba ,_befbg /2,_daba /2,SelHit );_caea ,_degb =_bcad (nil ,_aadf ,_bccf );if _degb !=nil {return nil ,_ga .Wrap (_degb ,_defe ,"\u0068S\u0069\u007a\u0065\u0020\u003d\u003d\u0020\u0031\u0020\u007c\u007c \u0076\u0053\u0069\u007a\u0065\u0020\u003d\u003d\u0020\u0031");
};}else {_bec :=SelCreateBrick (1,_daba ,0,_daba /2,SelHit );_cgf ,_abd :=_bgbfc (nil ,_aadf ,_bec );if _abd !=nil {return nil ,_ga .Wrap (_abd ,_defe ,"\u0072\u0065\u0067\u0075la\u0072\u0020\u002d\u0020\u0066\u0069\u0072\u0073\u0074\u0020\u0064\u0069\u006c\u0061t\u0065");
};_dafbe :=SelCreateBrick (_befbg ,1,_befbg /2,0,SelHit );_caea ,_abd =_bgbfc (nil ,_cgf ,_dafbe );if _abd !=nil {return nil ,_ga .Wrap (_abd ,_defe ,"\u0072\u0065\u0067ul\u0061\u0072\u0020\u002d\u0020\u0073\u0065\u0063\u006f\u006e\u0064\u0020\u0064\u0069\u006c\u0061\u0074\u0065");
};if _ ,_abd =_bbff (_cgf ,_caea ,_bec );_abd !=nil {return nil ,_ga .Wrap (_abd ,_defe ,"r\u0065\u0067\u0075\u006car\u0020-\u0020\u0066\u0069\u0072\u0073t\u0020\u0065\u0072\u006f\u0064\u0065");};if _ ,_abd =_bbff (_caea ,_cgf ,_dafbe );_abd !=nil {return nil ,_ga .Wrap (_abd ,_defe ,"\u0072\u0065\u0067\u0075la\u0072\u0020\u002d\u0020\u0073\u0065\u0063\u006f\u006e\u0064\u0020\u0065\u0072\u006fd\u0065");
};};if _geabe ,_degb =_caea .RemoveBorder (_fgff );_degb !=nil {return nil ,_ga .Wrap (_degb ,_defe ,"\u0072e\u0067\u0075\u006c\u0061\u0072");};if _gbbd ==nil {return _geabe ,nil ;};if _ ,_degb =_acda (_gbbd ,_geabe );_degb !=nil {return nil ,_degb ;};
return _gbbd ,nil ;};const (_ SizeSelection =iota ;SizeSelectByWidth ;SizeSelectByHeight ;SizeSelectByMaxDimension ;SizeSelectByArea ;SizeSelectByPerimeter ;);func _bee (_gdfa ,_gef *Bitmap ,_aagb *Selection )(*Bitmap ,error ){const _efdg ="\u0070\u0072\u006f\u0063\u0065\u0073\u0073\u004d\u006f\u0072\u0070\u0068A\u0072\u0067\u0073\u0032";
var _dgba ,_ceag int ;if _gef ==nil {return nil ,_ga .Error (_efdg ,"s\u006fu\u0072\u0063\u0065\u0020\u0062\u0069\u0074\u006da\u0070\u0020\u0069\u0073 n\u0069\u006c");};if _aagb ==nil {return nil ,_ga .Error (_efdg ,"\u0073e\u006c \u006e\u006f\u0074\u0020\u0064\u0065\u0066\u0069\u006e\u0065\u0064");
};_dgba =_aagb .Width ;_ceag =_aagb .Height ;if _dgba ==0||_ceag ==0{return nil ,_ga .Error (_efdg ,"\u0073\u0065\u006c\u0020\u006f\u0066\u0020\u0073\u0069\u007a\u0065\u0020\u0030");};if _gdfa ==nil {return _gef .createTemplate (),nil ;};if _gagae :=_gdfa .resizeImageData (_gef );
_gagae !=nil {return nil ,_gagae ;};return _gdfa ,nil ;};func _gbbf (_egea *Bitmap ,_bbee ,_fadb ,_becc ,_efbf int ,_afcb RasterOperator ,_fafd *Bitmap ,_dbfd ,_cbdc int )error {var (_agad bool ;_acge bool ;_feefa int ;_aeae int ;_ecbe int ;_fdfe bool ;
_acgef byte ;_ggad int ;_ceacd int ;_ceaaf int ;_dgac ,_dfae int ;);_adac :=8-(_bbee &7);_ebaf :=_bbaa [_adac ];_bcdb :=_egea .RowStride *_fadb +(_bbee >>3);_bbegd :=_fafd .RowStride *_cbdc +(_dbfd >>3);if _becc < _adac {_agad =true ;_ebaf &=_bcee [8-_adac +_becc ];
};if !_agad {_feefa =(_becc -_adac )>>3;if _feefa > 0{_acge =true ;_aeae =_bcdb +1;_ecbe =_bbegd +1;};};_ggad =(_bbee +_becc )&7;if !(_agad ||_ggad ==0){_fdfe =true ;_acgef =_bcee [_ggad ];_ceacd =_bcdb +1+_feefa ;_ceaaf =_bbegd +1+_feefa ;};switch _afcb {case PixSrc :for _dgac =0;
_dgac < _efbf ;_dgac ++{_egea .Data [_bcdb ]=_aacc (_egea .Data [_bcdb ],_fafd .Data [_bbegd ],_ebaf );_bcdb +=_egea .RowStride ;_bbegd +=_fafd .RowStride ;};if _acge {for _dgac =0;_dgac < _efbf ;_dgac ++{for _dfae =0;_dfae < _feefa ;_dfae ++{_egea .Data [_aeae +_dfae ]=_fafd .Data [_ecbe +_dfae ];
};_aeae +=_egea .RowStride ;_ecbe +=_fafd .RowStride ;};};if _fdfe {for _dgac =0;_dgac < _efbf ;_dgac ++{_egea .Data [_ceacd ]=_aacc (_egea .Data [_ceacd ],_fafd .Data [_ceaaf ],_acgef );_ceacd +=_egea .RowStride ;_ceaaf +=_fafd .RowStride ;};};case PixNotSrc :for _dgac =0;
_dgac < _efbf ;_dgac ++{_egea .Data [_bcdb ]=_aacc (_egea .Data [_bcdb ],^_fafd .Data [_bbegd ],_ebaf );_bcdb +=_egea .RowStride ;_bbegd +=_fafd .RowStride ;};if _acge {for _dgac =0;_dgac < _efbf ;_dgac ++{for _dfae =0;_dfae < _feefa ;_dfae ++{_egea .Data [_aeae +_dfae ]=^_fafd .Data [_ecbe +_dfae ];
};_aeae +=_egea .RowStride ;_ecbe +=_fafd .RowStride ;};};if _fdfe {for _dgac =0;_dgac < _efbf ;_dgac ++{_egea .Data [_ceacd ]=_aacc (_egea .Data [_ceacd ],^_fafd .Data [_ceaaf ],_acgef );_ceacd +=_egea .RowStride ;_ceaaf +=_fafd .RowStride ;};};case PixSrcOrDst :for _dgac =0;
_dgac < _efbf ;_dgac ++{_egea .Data [_bcdb ]=_aacc (_egea .Data [_bcdb ],_fafd .Data [_bbegd ]|_egea .Data [_bcdb ],_ebaf );_bcdb +=_egea .RowStride ;_bbegd +=_fafd .RowStride ;};if _acge {for _dgac =0;_dgac < _efbf ;_dgac ++{for _dfae =0;_dfae < _feefa ;
_dfae ++{_egea .Data [_aeae +_dfae ]|=_fafd .Data [_ecbe +_dfae ];};_aeae +=_egea .RowStride ;_ecbe +=_fafd .RowStride ;};};if _fdfe {for _dgac =0;_dgac < _efbf ;_dgac ++{_egea .Data [_ceacd ]=_aacc (_egea .Data [_ceacd ],_fafd .Data [_ceaaf ]|_egea .Data [_ceacd ],_acgef );
_ceacd +=_egea .RowStride ;_ceaaf +=_fafd .RowStride ;};};case PixSrcAndDst :for _dgac =0;_dgac < _efbf ;_dgac ++{_egea .Data [_bcdb ]=_aacc (_egea .Data [_bcdb ],_fafd .Data [_bbegd ]&_egea .Data [_bcdb ],_ebaf );_bcdb +=_egea .RowStride ;_bbegd +=_fafd .RowStride ;
};if _acge {for _dgac =0;_dgac < _efbf ;_dgac ++{for _dfae =0;_dfae < _feefa ;_dfae ++{_egea .Data [_aeae +_dfae ]&=_fafd .Data [_ecbe +_dfae ];};_aeae +=_egea .RowStride ;_ecbe +=_fafd .RowStride ;};};if _fdfe {for _dgac =0;_dgac < _efbf ;_dgac ++{_egea .Data [_ceacd ]=_aacc (_egea .Data [_ceacd ],_fafd .Data [_ceaaf ]&_egea .Data [_ceacd ],_acgef );
_ceacd +=_egea .RowStride ;_ceaaf +=_fafd .RowStride ;};};case PixSrcXorDst :for _dgac =0;_dgac < _efbf ;_dgac ++{_egea .Data [_bcdb ]=_aacc (_egea .Data [_bcdb ],_fafd .Data [_bbegd ]^_egea .Data [_bcdb ],_ebaf );_bcdb +=_egea .RowStride ;_bbegd +=_fafd .RowStride ;
};if _acge {for _dgac =0;_dgac < _efbf ;_dgac ++{for _dfae =0;_dfae < _feefa ;_dfae ++{_egea .Data [_aeae +_dfae ]^=_fafd .Data [_ecbe +_dfae ];};_aeae +=_egea .RowStride ;_ecbe +=_fafd .RowStride ;};};if _fdfe {for _dgac =0;_dgac < _efbf ;_dgac ++{_egea .Data [_ceacd ]=_aacc (_egea .Data [_ceacd ],_fafd .Data [_ceaaf ]^_egea .Data [_ceacd ],_acgef );
_ceacd +=_egea .RowStride ;_ceaaf +=_fafd .RowStride ;};};case PixNotSrcOrDst :for _dgac =0;_dgac < _efbf ;_dgac ++{_egea .Data [_bcdb ]=_aacc (_egea .Data [_bcdb ],^(_fafd .Data [_bbegd ])|_egea .Data [_bcdb ],_ebaf );_bcdb +=_egea .RowStride ;_bbegd +=_fafd .RowStride ;
};if _acge {for _dgac =0;_dgac < _efbf ;_dgac ++{for _dfae =0;_dfae < _feefa ;_dfae ++{_egea .Data [_aeae +_dfae ]|=^(_fafd .Data [_ecbe +_dfae ]);};_aeae +=_egea .RowStride ;_ecbe +=_fafd .RowStride ;};};if _fdfe {for _dgac =0;_dgac < _efbf ;_dgac ++{_egea .Data [_ceacd ]=_aacc (_egea .Data [_ceacd ],^(_fafd .Data [_ceaaf ])|_egea .Data [_ceacd ],_acgef );
_ceacd +=_egea .RowStride ;_ceaaf +=_fafd .RowStride ;};};case PixNotSrcAndDst :for _dgac =0;_dgac < _efbf ;_dgac ++{_egea .Data [_bcdb ]=_aacc (_egea .Data [_bcdb ],^(_fafd .Data [_bbegd ])&_egea .Data [_bcdb ],_ebaf );_bcdb +=_egea .RowStride ;_bbegd +=_fafd .RowStride ;
};if _acge {for _dgac =0;_dgac < _efbf ;_dgac ++{for _dfae =0;_dfae < _feefa ;_dfae ++{_egea .Data [_aeae +_dfae ]&=^_fafd .Data [_ecbe +_dfae ];};_aeae +=_egea .RowStride ;_ecbe +=_fafd .RowStride ;};};if _fdfe {for _dgac =0;_dgac < _efbf ;_dgac ++{_egea .Data [_ceacd ]=_aacc (_egea .Data [_ceacd ],^(_fafd .Data [_ceaaf ])&_egea .Data [_ceacd ],_acgef );
_ceacd +=_egea .RowStride ;_ceaaf +=_fafd .RowStride ;};};case PixSrcOrNotDst :for _dgac =0;_dgac < _efbf ;_dgac ++{_egea .Data [_bcdb ]=_aacc (_egea .Data [_bcdb ],_fafd .Data [_bbegd ]|^(_egea .Data [_bcdb ]),_ebaf );_bcdb +=_egea .RowStride ;_bbegd +=_fafd .RowStride ;
};if _acge {for _dgac =0;_dgac < _efbf ;_dgac ++{for _dfae =0;_dfae < _feefa ;_dfae ++{_egea .Data [_aeae +_dfae ]=_fafd .Data [_ecbe +_dfae ]|^(_egea .Data [_aeae +_dfae ]);};_aeae +=_egea .RowStride ;_ecbe +=_fafd .RowStride ;};};if _fdfe {for _dgac =0;
_dgac < _efbf ;_dgac ++{_egea .Data [_ceacd ]=_aacc (_egea .Data [_ceacd ],_fafd .Data [_ceaaf ]|^(_egea .Data [_ceacd ]),_acgef );_ceacd +=_egea .RowStride ;_ceaaf +=_fafd .RowStride ;};};case PixSrcAndNotDst :for _dgac =0;_dgac < _efbf ;_dgac ++{_egea .Data [_bcdb ]=_aacc (_egea .Data [_bcdb ],_fafd .Data [_bbegd ]&^(_egea .Data [_bcdb ]),_ebaf );
_bcdb +=_egea .RowStride ;_bbegd +=_fafd .RowStride ;};if _acge {for _dgac =0;_dgac < _efbf ;_dgac ++{for _dfae =0;_dfae < _feefa ;_dfae ++{_egea .Data [_aeae +_dfae ]=_fafd .Data [_ecbe +_dfae ]&^(_egea .Data [_aeae +_dfae ]);};_aeae +=_egea .RowStride ;
_ecbe +=_fafd .RowStride ;};};if _fdfe {for _dgac =0;_dgac < _efbf ;_dgac ++{_egea .Data [_ceacd ]=_aacc (_egea .Data [_ceacd ],_fafd .Data [_ceaaf ]&^(_egea .Data [_ceacd ]),_acgef );_ceacd +=_egea .RowStride ;_ceaaf +=_fafd .RowStride ;};};case PixNotPixSrcOrDst :for _dgac =0;
_dgac < _efbf ;_dgac ++{_egea .Data [_bcdb ]=_aacc (_egea .Data [_bcdb ],^(_fafd .Data [_bbegd ]|_egea .Data [_bcdb ]),_ebaf );_bcdb +=_egea .RowStride ;_bbegd +=_fafd .RowStride ;};if _acge {for _dgac =0;_dgac < _efbf ;_dgac ++{for _dfae =0;_dfae < _feefa ;
_dfae ++{_egea .Data [_aeae +_dfae ]=^(_fafd .Data [_ecbe +_dfae ]|_egea .Data [_aeae +_dfae ]);};_aeae +=_egea .RowStride ;_ecbe +=_fafd .RowStride ;};};if _fdfe {for _dgac =0;_dgac < _efbf ;_dgac ++{_egea .Data [_ceacd ]=_aacc (_egea .Data [_ceacd ],^(_fafd .Data [_ceaaf ]|_egea .Data [_ceacd ]),_acgef );
_ceacd +=_egea .RowStride ;_ceaaf +=_fafd .RowStride ;};};case PixNotPixSrcAndDst :for _dgac =0;_dgac < _efbf ;_dgac ++{_egea .Data [_bcdb ]=_aacc (_egea .Data [_bcdb ],^(_fafd .Data [_bbegd ]&_egea .Data [_bcdb ]),_ebaf );_bcdb +=_egea .RowStride ;_bbegd +=_fafd .RowStride ;
};if _acge {for _dgac =0;_dgac < _efbf ;_dgac ++{for _dfae =0;_dfae < _feefa ;_dfae ++{_egea .Data [_aeae +_dfae ]=^(_fafd .Data [_ecbe +_dfae ]&_egea .Data [_aeae +_dfae ]);};_aeae +=_egea .RowStride ;_ecbe +=_fafd .RowStride ;};};if _fdfe {for _dgac =0;
_dgac < _efbf ;_dgac ++{_egea .Data [_ceacd ]=_aacc (_egea .Data [_ceacd ],^(_fafd .Data [_ceaaf ]&_egea .Data [_ceacd ]),_acgef );_ceacd +=_egea .RowStride ;_ceaaf +=_fafd .RowStride ;};};case PixNotPixSrcXorDst :for _dgac =0;_dgac < _efbf ;_dgac ++{_egea .Data [_bcdb ]=_aacc (_egea .Data [_bcdb ],^(_fafd .Data [_bbegd ]^_egea .Data [_bcdb ]),_ebaf );
_bcdb +=_egea .RowStride ;_bbegd +=_fafd .RowStride ;};if _acge {for _dgac =0;_dgac < _efbf ;_dgac ++{for _dfae =0;_dfae < _feefa ;_dfae ++{_egea .Data [_aeae +_dfae ]=^(_fafd .Data [_ecbe +_dfae ]^_egea .Data [_aeae +_dfae ]);};_aeae +=_egea .RowStride ;
_ecbe +=_fafd .RowStride ;};};if _fdfe {for _dgac =0;_dgac < _efbf ;_dgac ++{_egea .Data [_ceacd ]=_aacc (_egea .Data [_ceacd ],^(_fafd .Data [_ceaaf ]^_egea .Data [_ceacd ]),_acgef );_ceacd +=_egea .RowStride ;_ceaaf +=_fafd .RowStride ;};};default:_bb .Log .Debug ("I\u006e\u0076\u0061\u006c\u0069\u0064 \u0072\u0061\u0073\u0074\u0065\u0072\u0020\u006f\u0070e\u0072\u0061\u0074o\u0072:\u0020\u0025\u0064",_afcb );
return _ga .Error ("\u0072\u0061\u0073\u0074er\u004f\u0070\u0056\u0041\u006c\u0069\u0067\u006e\u0065\u0064\u004c\u006f\u0077","\u0069\u006e\u0076al\u0069\u0064\u0020\u0072\u0061\u0073\u0074\u0065\u0072\u0020\u006f\u0070\u0065\u0072\u0061\u0074\u006f\u0072");
};return nil ;};func (_dfgec *Bitmap )setEightBytes (_adab int ,_adce uint64 )error {_eab :=_dfgec .RowStride -(_adab %_dfgec .RowStride );if _dfgec .RowStride !=_dfgec .Width >>3{_eab --;};if _eab >=8{return _dfgec .setEightFullBytes (_adab ,_adce );};
return _dfgec .setEightPartlyBytes (_adab ,_eab ,_adce );};type SizeComparison int ;func (_acbe *Boxes )SelectBySize (width ,height int ,tp LocationFilter ,relation SizeComparison )(_cea *Boxes ,_gfe error ){const _ccf ="\u0042o\u0078e\u0073\u002e\u0053\u0065\u006ce\u0063\u0074B\u0079\u0053\u0069\u007a\u0065";
if _acbe ==nil {return nil ,_ga .Error (_ccf ,"b\u006f\u0078\u0065\u0073 '\u0062'\u0020\u006e\u006f\u0074\u0020d\u0065\u0066\u0069\u006e\u0065\u0064");};if len (*_acbe )==0{return _acbe ,nil ;};switch tp {case LocSelectWidth ,LocSelectHeight ,LocSelectIfEither ,LocSelectIfBoth :default:return nil ,_ga .Errorf (_ccf ,"\u0069\u006e\u0076al\u0069\u0064\u0020\u0066\u0069\u006c\u0074\u0065\u0072\u0020\u0074\u0079\u0070\u0065\u003a\u0020\u0025\u0064",tp );
};switch relation {case SizeSelectIfLT ,SizeSelectIfGT ,SizeSelectIfLTE ,SizeSelectIfGTE :default:return nil ,_ga .Errorf (_ccf ,"i\u006e\u0076\u0061\u006c\u0069\u0064 \u0072\u0065\u006c\u0061\u0074\u0069\u006f\u006e\u0020t\u0079\u0070\u0065:\u0020'\u0025\u0064\u0027",tp );
};_eff :=_acbe .makeSizeIndicator (width ,height ,tp ,relation );_gcae ,_gfe :=_acbe .selectWithIndicator (_eff );if _gfe !=nil {return nil ,_ga .Wrap (_gfe ,_ccf ,"");};return _gcae ,nil ;};func (_adae Points )GetGeometry (i int )(_gdgg ,_eaddb float32 ,_ecae error ){if i > len (_adae )-1{return 0,0,_ga .Errorf ("\u0050\u006f\u0069\u006e\u0074\u0073\u002e\u0047\u0065\u0074","\u0069n\u0064\u0065\u0078\u003a\u0020\u0027\u0025\u0064\u0027\u0020\u006fu\u0074\u0020\u006f\u0066\u0020\u0072\u0061\u006e\u0067\u0065",i );
};_fefdd :=_adae [i ];return _fefdd .X ,_fefdd .Y ,nil ;};type CombinationOperator int ;func (_cbcc *Bitmaps )String ()string {_beac :=_gaf .Builder {};for _ ,_fdfb :=range _cbcc .Values {_beac .WriteString (_fdfb .String ());_beac .WriteRune ('\n');};
return _beac .String ();};func (_dfge *Bitmap )SetPixel (x ,y int ,pixel byte )error {_ccb :=_dfge .GetByteIndex (x ,y );if _ccb > len (_dfge .Data )-1{return _ga .Errorf ("\u0053\u0065\u0074\u0050\u0069\u0078\u0065\u006c","\u0069\u006e\u0064\u0065x \u006f\u0075\u0074\u0020\u006f\u0066\u0020\u0072\u0061\u006e\u0067\u0065\u003a\u0020%\u0064",_ccb );
};_fbga :=_dfge .GetBitOffset (x );_fcf :=uint (7-_fbga );_ada :=_dfge .Data [_ccb ];var _dbcad byte ;if pixel ==1{_dbcad =_ada |(pixel &0x01<<_fcf );}else {_dbcad =_ada &^(1<<_fcf );};_dfge .Data [_ccb ]=_dbcad ;return nil ;};type byWidth Bitmaps ;func _gcgb (_dedg *Bitmap )(_ccdd *Bitmap ,_ccbc int ,_dafb error ){const _baffd ="\u0042i\u0074\u006d\u0061\u0070.\u0077\u006f\u0072\u0064\u004da\u0073k\u0042y\u0044\u0069\u006c\u0061\u0074\u0069\u006fn";
if _dedg ==nil {return nil ,0,_ga .Errorf (_baffd ,"\u0027\u0073\u0027\u0020bi\u0074\u006d\u0061\u0070\u0020\u006e\u006f\u0074\u0020\u0064\u0065\u0066\u0069\u006ee\u0064");};var _feec ,_bacg *Bitmap ;if _feec ,_dafb =_acda (nil ,_dedg );_dafb !=nil {return nil ,0,_ga .Wrap (_dafb ,_baffd ,"\u0063\u006f\u0070\u0079\u0020\u0027\u0073\u0027");
};var (_gaff [13]int ;_eaedf ,_ecfg int ;);_gaddb :=12;_abfc :=_eb .NewNumSlice (_gaddb +1);_befb :=_eb .NewNumSlice (_gaddb +1);var _acdf *Boxes ;for _gccbf :=0;_gccbf <=_gaddb ;_gccbf ++{if _gccbf ==0{if _bacg ,_dafb =_acda (nil ,_feec );_dafb !=nil {return nil ,0,_ga .Wrap (_dafb ,_baffd ,"\u0066i\u0072\u0073\u0074\u0020\u0062\u006d2");
};}else {if _bacg ,_dafb =_abdf (_feec ,MorphProcess {Operation :MopDilation ,Arguments :[]int {2,1}});_dafb !=nil {return nil ,0,_ga .Wrap (_dafb ,_baffd ,"\u0064\u0069\u006ca\u0074\u0069\u006f\u006e\u0020\u0062\u006d\u0032");};};if _acdf ,_dafb =_bacg .connComponentsBB (4);
_dafb !=nil {return nil ,0,_ga .Wrap (_dafb ,_baffd ,"");};_gaff [_gccbf ]=len (*_acdf );_abfc .AddInt (_gaff [_gccbf ]);switch _gccbf {case 0:_eaedf =_gaff [0];default:_ecfg =_gaff [_gccbf -1]-_gaff [_gccbf ];_befb .AddInt (_ecfg );};_feec =_bacg ;};_gegd :=true ;
_edae :=2;var _abc ,_bff int ;for _aag :=1;_aag < len (*_befb );_aag ++{if _abc ,_dafb =_abfc .GetInt (_aag );_dafb !=nil {return nil ,0,_ga .Wrap (_dafb ,_baffd ,"\u0043\u0068\u0065\u0063ki\u006e\u0067\u0020\u0062\u0065\u0073\u0074\u0020\u0064\u0069\u006c\u0061\u0074\u0069o\u006e");
};if _gegd &&_abc < int (0.3*float32 (_eaedf )){_edae =_aag +1;_gegd =false ;};if _ecfg ,_dafb =_befb .GetInt (_aag );_dafb !=nil {return nil ,0,_ga .Wrap (_dafb ,_baffd ,"\u0067\u0065\u0074\u0074\u0069\u006e\u0067\u0020\u006ea\u0044\u0069\u0066\u0066");
};if _ecfg > _bff {_bff =_ecfg ;};};_egadg :=_dedg .XResolution ;if _egadg ==0{_egadg =150;};if _egadg > 110{_edae ++;};if _edae < 2{_bb .Log .Trace ("J\u0042\u0049\u0047\u0032\u0020\u0073\u0065\u0074\u0074\u0069\u006e\u0067\u0020\u0069\u0042\u0065\u0073\u0074 \u0074\u006f\u0020\u006d\u0069\u006e\u0069\u006d\u0075\u006d a\u006c\u006c\u006fw\u0061b\u006c\u0065");
_edae =2;};_ccbc =_edae +1;if _ccdd ,_dafb =_dagc (nil ,_dedg ,_edae +1,1);_dafb !=nil {return nil ,0,_ga .Wrap (_dafb ,_baffd ,"\u0067\u0065\u0074\u0074in\u0067\u0020\u006d\u0061\u0073\u006b\u0020\u0066\u0061\u0069\u006c\u0065\u0064");};return _ccdd ,_ccbc ,nil ;
};func _gdcb (_efef *Bitmap ,_gccd int )(*Bitmap ,error ){const _afda ="\u0065x\u0070a\u006e\u0064\u0052\u0065\u0070\u006c\u0069\u0063\u0061\u0074\u0065";if _efef ==nil {return nil ,_ga .Error (_afda ,"\u0073o\u0075r\u0063\u0065\u0020\u006e\u006ft\u0020\u0064e\u0066\u0069\u006e\u0065\u0064");
};if _gccd <=0{return nil ,_ga .Error (_afda ,"i\u006e\u0076\u0061\u006cid\u0020f\u0061\u0063\u0074\u006f\u0072 \u002d\u0020\u003c\u003d\u0020\u0030");};if _gccd ==1{_bccad ,_cbda :=_acda (nil ,_efef );if _cbda !=nil {return nil ,_ga .Wrap (_cbda ,_afda ,"\u0066\u0061\u0063\u0074\u006f\u0072\u0020\u003d\u0020\u0031");
};return _bccad ,nil ;};_eadd ,_cdabb :=_ebc (_efef ,_gccd ,_gccd );if _cdabb !=nil {return nil ,_ga .Wrap (_cdabb ,_afda ,"");};return _eadd ,nil ;};func Dilate (d *Bitmap ,s *Bitmap ,sel *Selection )(*Bitmap ,error ){return _bgbfc (d ,s ,sel )};func Blit (src *Bitmap ,dst *Bitmap ,x ,y int ,op CombinationOperator )error {var _ggd ,_cfdd int ;
_ccc :=src .RowStride -1;if x < 0{_cfdd =-x ;x =0;}else if x +src .Width > dst .Width {_ccc -=src .Width +x -dst .Width ;};if y < 0{_ggd =-y ;y =0;_cfdd +=src .RowStride ;_ccc +=src .RowStride ;}else if y +src .Height > dst .Height {_ggd =src .Height +y -dst .Height ;
};var (_dbcg int ;_dace error ;);_ccbe :=x &0x07;_gcee :=8-_ccbe ;_agcf :=src .Width &0x07;_gaac :=_gcee -_agcf ;_cag :=_gcee &0x07!=0;_aead :=src .Width <=((_ccc -_cfdd )<<3)+_gcee ;_cfcdb :=dst .GetByteIndex (x ,y );_bcgf :=_ggd +dst .Height ;if src .Height > _bcgf {_dbcg =_bcgf ;
}else {_dbcg =src .Height ;};switch {case !_cag :_dace =_fdgc (src ,dst ,_ggd ,_dbcg ,_cfcdb ,_cfdd ,_ccc ,op );case _aead :_dace =_caba (src ,dst ,_ggd ,_dbcg ,_cfcdb ,_cfdd ,_ccc ,_gaac ,_ccbe ,_gcee ,op );default:_dace =_eedg (src ,dst ,_ggd ,_dbcg ,_cfcdb ,_cfdd ,_ccc ,_gaac ,_ccbe ,_gcee ,op ,_agcf );
};return _dace ;};func (_gfcgd *Bitmaps )GroupByHeight ()(*BitmapsArray ,error ){const _gcdag ="\u0047\u0072\u006f\u0075\u0070\u0042\u0079\u0048\u0065\u0069\u0067\u0068\u0074";if len (_gfcgd .Values )==0{return nil ,_ga .Error (_gcdag ,"\u006eo\u0020v\u0061\u006c\u0075\u0065\u0073 \u0070\u0072o\u0076\u0069\u0064\u0065\u0064");
};_dcagg :=&BitmapsArray {};_gfcgd .SortByHeight ();_dffgc :=-1;_bfgc :=-1;for _ebef :=0;_ebef < len (_gfcgd .Values );_ebef ++{_affb :=_gfcgd .Values [_ebef ].Height ;if _affb > _dffgc {_dffgc =_affb ;_bfgc ++;_dcagg .Values =append (_dcagg .Values ,&Bitmaps {});
};_dcagg .Values [_bfgc ].AddBitmap (_gfcgd .Values [_ebef ]);};return _dcagg ,nil ;};func TstNSymbol (t *_f .T ,scale ...int )*Bitmap {_febed ,_abbg :=NewWithData (4,5,[]byte {0x90,0xD0,0xB0,0x90,0x90});_gb .NoError (t ,_abbg );return TstGetScaledSymbol (t ,_febed ,scale ...);
};func (_feeeg *Selection )setOrigin (_ggagd ,_cggg int ){_feeeg .Cy ,_feeeg .Cx =_ggagd ,_cggg };func TstASymbol (t *_f .T )*Bitmap {t .Helper ();_fccd :=New (6,6);_gb .NoError (t ,_fccd .SetPixel (1,0,1));_gb .NoError (t ,_fccd .SetPixel (2,0,1));_gb .NoError (t ,_fccd .SetPixel (3,0,1));
_gb .NoError (t ,_fccd .SetPixel (4,0,1));_gb .NoError (t ,_fccd .SetPixel (5,1,1));_gb .NoError (t ,_fccd .SetPixel (1,2,1));_gb .NoError (t ,_fccd .SetPixel (2,2,1));_gb .NoError (t ,_fccd .SetPixel (3,2,1));_gb .NoError (t ,_fccd .SetPixel (4,2,1));
_gb .NoError (t ,_fccd .SetPixel (5,2,1));_gb .NoError (t ,_fccd .SetPixel (0,3,1));_gb .NoError (t ,_fccd .SetPixel (5,3,1));_gb .NoError (t ,_fccd .SetPixel (0,4,1));_gb .NoError (t ,_fccd .SetPixel (5,4,1));_gb .NoError (t ,_fccd .SetPixel (1,5,1));
_gb .NoError (t ,_fccd .SetPixel (2,5,1));_gb .NoError (t ,_fccd .SetPixel (3,5,1));_gb .NoError (t ,_fccd .SetPixel (4,5,1));_gb .NoError (t ,_fccd .SetPixel (5,5,1));return _fccd ;};func (_fcbbc *Bitmaps )GetBitmap (i int )(*Bitmap ,error ){const _cfdb ="\u0047e\u0074\u0042\u0069\u0074\u006d\u0061p";
if _fcbbc ==nil {return nil ,_ga .Error (_cfdb ,"p\u0072o\u0076\u0069\u0064\u0065\u0064\u0020\u006e\u0069l\u0020\u0042\u0069\u0074ma\u0070\u0073");};if i > len (_fcbbc .Values )-1{return nil ,_ga .Errorf (_cfdb ,"\u0069n\u0064\u0065\u0078\u003a\u0020\u0027\u0025\u0064\u0027\u0020\u006fu\u0074\u0020\u006f\u0066\u0020\u0072\u0061\u006e\u0067\u0065",i );
};return _fcbbc .Values [i ],nil ;};func (_ef *Bitmap )ClipRectangle (box *_c .Rectangle )(_bcd *Bitmap ,_dbe *_c .Rectangle ,_bbae error ){const _bab ="\u0043\u006c\u0069\u0070\u0052\u0065\u0063\u0074\u0061\u006e\u0067\u006c\u0065";if box ==nil {return nil ,nil ,_ga .Error (_bab ,"\u0062o\u0078 \u0069\u0073\u0020\u006e\u006ft\u0020\u0064e\u0066\u0069\u006e\u0065\u0064");
};_gcb ,_cbf :=_ef .Width ,_ef .Height ;_bgb :=_c .Rect (0,0,_gcb ,_cbf );if !box .Overlaps (_bgb ){return nil ,nil ,_ga .Error (_bab ,"b\u006f\u0078\u0020\u0064oe\u0073n\u0027\u0074\u0020\u006f\u0076e\u0072\u006c\u0061\u0070\u0020\u0062");};_fge :=box .Intersect (_bgb );
_cga ,_dffg :=_fge .Min .X ,_fge .Min .Y ;_fefe ,_afg :=_fge .Dx (),_fge .Dy ();_bcd =New (_fefe ,_afg );_bcd .Text =_ef .Text ;if _bbae =_bcd .RasterOperation (0,0,_fefe ,_afg ,PixSrc ,_ef ,_cga ,_dffg );_bbae !=nil {return nil ,nil ,_ga .Wrap (_bbae ,_bab ,"\u0050\u0069\u0078\u0053\u0072\u0063\u0020\u0074\u006f\u0020\u0063\u006ci\u0070\u0070\u0065\u0064");
};_dbe =&_fge ;return _bcd ,_dbe ,nil ;};func (_ede *Bitmap )CreateTemplate ()*Bitmap {return _ede .createTemplate ()};func (_egff *Bitmap )GetComponents (components Component ,maxWidth ,maxHeight int )(_cffb *Bitmaps ,_cda *Boxes ,_bed error ){const _ebag ="B\u0069t\u006d\u0061\u0070\u002e\u0047\u0065\u0074\u0043o\u006d\u0070\u006f\u006een\u0074\u0073";
if _egff ==nil {return nil ,nil ,_ga .Error (_ebag ,"\u0073\u006f\u0075\u0072\u0063\u0065\u0020\u0042\u0069\u0074\u006da\u0070\u0020\u006e\u006f\u0074\u0020\u0064\u0065\u0066\u0069n\u0065\u0064\u002e");};switch components {case ComponentConn ,ComponentCharacters ,ComponentWords :default:return nil ,nil ,_ga .Error (_ebag ,"\u0069\u006e\u0076\u0061l\u0069\u0064\u0020\u0063\u006f\u006d\u0070\u006f\u006e\u0065n\u0074s\u0020\u0070\u0061\u0072\u0061\u006d\u0065t\u0065\u0072");
};if _egff .Zero (){_cda =&Boxes {};_cffb =&Bitmaps {};return _cffb ,_cda ,nil ;};switch components {case ComponentConn :_cffb =&Bitmaps {};if _cda ,_bed =_egff .ConnComponents (_cffb ,8);_bed !=nil {return nil ,nil ,_ga .Wrap (_bed ,_ebag ,"\u006e\u006f \u0070\u0072\u0065p\u0072\u006f\u0063\u0065\u0073\u0073\u0069\u006e\u0067");
};case ComponentCharacters :_dfdf ,_egg :=MorphSequence (_egff ,MorphProcess {Operation :MopClosing ,Arguments :[]int {1,6}});if _egg !=nil {return nil ,nil ,_ga .Wrap (_egg ,_ebag ,"\u0063h\u0061\u0072\u0061\u0063\u0074\u0065\u0072\u0073\u0020\u0070\u0072e\u0070\u0072\u006f\u0063\u0065\u0073\u0073\u0069\u006e\u0067");
};if _bb .Log .IsLogLevel (_bb .LogLevelTrace ){_bb .Log .Trace ("\u0043o\u006d\u0070o\u006e\u0065\u006e\u0074C\u0068\u0061\u0072a\u0063\u0074\u0065\u0072\u0073\u0020\u0062\u0069\u0074ma\u0070\u0020\u0061f\u0074\u0065r\u0020\u0063\u006c\u006f\u0073\u0069n\u0067\u003a \u0025\u0073",_dfdf .String ());
};_ecbg :=&Bitmaps {};_cda ,_egg =_dfdf .ConnComponents (_ecbg ,8);if _egg !=nil {return nil ,nil ,_ga .Wrap (_egg ,_ebag ,"\u0063h\u0061\u0072\u0061\u0063\u0074\u0065\u0072\u0073\u0020\u0070\u0072e\u0070\u0072\u006f\u0063\u0065\u0073\u0073\u0069\u006e\u0067");
};if _bb .Log .IsLogLevel (_bb .LogLevelTrace ){_bb .Log .Trace ("\u0043\u006f\u006d\u0070\u006f\u006ee\u006e\u0074\u0043\u0068\u0061\u0072\u0061\u0063\u0074\u0065\u0072\u0073\u0020\u0062\u0069\u0074\u006d\u0061\u0070\u0020a\u0066\u0074\u0065\u0072\u0020\u0063\u006f\u006e\u006e\u0065\u0063\u0074\u0069\u0076i\u0074y\u003a\u0020\u0025\u0073",_ecbg .String ());
};if _cffb ,_egg =_ecbg .ClipToBitmap (_egff );_egg !=nil {return nil ,nil ,_ga .Wrap (_egg ,_ebag ,"\u0063h\u0061\u0072\u0061\u0063\u0074\u0065\u0072\u0073\u0020\u0070\u0072e\u0070\u0072\u006f\u0063\u0065\u0073\u0073\u0069\u006e\u0067");};case ComponentWords :_fddc :=1;
var _fcfca *Bitmap ;switch {case _egff .XResolution <=200:_fcfca =_egff ;case _egff .XResolution <=400:_fddc =2;_fcfca ,_bed =_fe (_egff ,1,0,0,0);if _bed !=nil {return nil ,nil ,_ga .Wrap (_bed ,_ebag ,"w\u006f\u0072\u0064\u0020\u0070\u0072e\u0070\u0072\u006f\u0063\u0065\u0073\u0073\u0020\u002d \u0078\u0072\u0065s\u003c=\u0034\u0030\u0030");
};default:_fddc =4;_fcfca ,_bed =_fe (_egff ,1,1,0,0);if _bed !=nil {return nil ,nil ,_ga .Wrap (_bed ,_ebag ,"\u0077\u006f\u0072\u0064 \u0070\u0072\u0065\u0070\u0072\u006f\u0063\u0065\u0073\u0073 \u002d \u0078\u0072\u0065\u0073\u0020\u003e\u00204\u0030\u0030");
};};_cbga ,_ ,_fbee :=_gcgb (_fcfca );if _fbee !=nil {return nil ,nil ,_ga .Wrap (_fbee ,_ebag ,"\u0077o\u0072d\u0020\u0070\u0072\u0065\u0070\u0072\u006f\u0063\u0065\u0073\u0073");};_eggd ,_fbee :=_gdcb (_cbga ,_fddc );if _fbee !=nil {return nil ,nil ,_ga .Wrap (_fbee ,_ebag ,"\u0077o\u0072d\u0020\u0070\u0072\u0065\u0070\u0072\u006f\u0063\u0065\u0073\u0073");
};_eefa :=&Bitmaps {};if _cda ,_fbee =_eggd .ConnComponents (_eefa ,4);_fbee !=nil {return nil ,nil ,_ga .Wrap (_fbee ,_ebag ,"\u0077\u006f\u0072\u0064\u0020\u0070r\u0065\u0070\u0072\u006f\u0063\u0065\u0073\u0073\u002c\u0020\u0063\u006f\u006en\u0065\u0063\u0074\u0020\u0065\u0078\u0070a\u006e\u0064\u0065\u0064");
};if _cffb ,_fbee =_eefa .ClipToBitmap (_egff );_fbee !=nil {return nil ,nil ,_ga .Wrap (_fbee ,_ebag ,"\u0077o\u0072d\u0020\u0070\u0072\u0065\u0070\u0072\u006f\u0063\u0065\u0073\u0073");};};_cffb ,_bed =_cffb .SelectBySize (maxWidth ,maxHeight ,LocSelectIfBoth ,SizeSelectIfLTE );
if _bed !=nil {return nil ,nil ,_ga .Wrap (_bed ,_ebag ,"");};_cda ,_bed =_cda .SelectBySize (maxWidth ,maxHeight ,LocSelectIfBoth ,SizeSelectIfLTE );if _bed !=nil {return nil ,nil ,_ga .Wrap (_bed ,_ebag ,"");};return _cffb ,_cda ,nil ;};func (_gggd *Bitmap )ThresholdPixelSum (thresh int ,tab8 []int )(_ffdc bool ,_aca error ){const _cbc ="\u0042i\u0074\u006d\u0061\u0070\u002e\u0054\u0068\u0072\u0065\u0073\u0068o\u006c\u0064\u0050\u0069\u0078\u0065\u006c\u0053\u0075\u006d";
if tab8 ==nil {tab8 =_dfeeb ();};_ffbb :=_gggd .Width >>3;_gcga :=_gggd .Width &7;_ace :=byte (0xff<<uint (8-_gcga ));var (_ged ,_fee ,_dfd ,_gbgc int ;_gee byte ;);for _ged =0;_ged < _gggd .Height ;_ged ++{_dfd =_gggd .RowStride *_ged ;for _fee =0;_fee < _ffbb ;
_fee ++{_gee ,_aca =_gggd .GetByte (_dfd +_fee );if _aca !=nil {return false ,_ga .Wrap (_aca ,_cbc ,"\u0066\u0075\u006c\u006c\u0042\u0079\u0074\u0065");};_gbgc +=tab8 [_gee ];};if _gcga !=0{_gee ,_aca =_gggd .GetByte (_dfd +_fee );if _aca !=nil {return false ,_ga .Wrap (_aca ,_cbc ,"p\u0061\u0072\u0074\u0069\u0061\u006c\u0042\u0079\u0074\u0065");
};_gee &=_ace ;_gbgc +=tab8 [_gee ];};if _gbgc > thresh {return true ,nil ;};};return _ffdc ,nil ;};func (_ege *Bitmap )ToImage ()_c .Image {_dffb ,_dcg :=_d .NewImage (_ege .Width ,_ege .Height ,1,1,_ege .Data ,nil ,nil );if _dcg !=nil {_bb .Log .Error ("\u0043\u006f\u006e\u0076\u0065\u0072\u0074\u0069\u006e\u0067\u0020j\u0062\u0069\u0067\u0032\u002e\u0042\u0069\u0074m\u0061p\u0020\u0074\u006f\u0020\u0069\u006d\u0061\u0067\u0065\u0075\u0074\u0069\u006c\u002e\u0049\u006d\u0061\u0067e\u0020\u0066\u0061\u0069\u006c\u0065\u0064\u003a\u0020\u0025\u0076",_dcg );
};return _dffb ;};func (_fdb *Bitmaps )SelectByIndexes (idx []int )(*Bitmaps ,error ){const _cdfc ="B\u0069\u0074\u006d\u0061\u0070\u0073.\u0053\u006f\u0072\u0074\u0049\u006e\u0064\u0065\u0078e\u0073\u0042\u0079H\u0065i\u0067\u0068\u0074";_agbgd ,_bagc :=_fdb .selectByIndexes (idx );
if _bagc !=nil {return nil ,_ga .Wrap (_bagc ,_cdfc ,"");};return _agbgd ,nil ;};func (_fabe *Bitmap )setEightFullBytes (_gcde int ,_ebbe uint64 )error {if _gcde +7> len (_fabe .Data )-1{return _ga .Error ("\u0073\u0065\u0074\u0045\u0069\u0067\u0068\u0074\u0042\u0079\u0074\u0065\u0073","\u0069n\u0064e\u0078\u0020\u006f\u0075\u0074 \u006f\u0066 \u0072\u0061\u006e\u0067\u0065");
};_fabe .Data [_gcde ]=byte ((_ebbe &0xff00000000000000)>>56);_fabe .Data [_gcde +1]=byte ((_ebbe &0xff000000000000)>>48);_fabe .Data [_gcde +2]=byte ((_ebbe &0xff0000000000)>>40);_fabe .Data [_gcde +3]=byte ((_ebbe &0xff00000000)>>32);_fabe .Data [_gcde +4]=byte ((_ebbe &0xff000000)>>24);
_fabe .Data [_gcde +5]=byte ((_ebbe &0xff0000)>>16);_fabe .Data [_gcde +6]=byte ((_ebbe &0xff00)>>8);_fabe .Data [_gcde +7]=byte (_ebbe &0xff);return nil ;};type shift int ;func (_cccgb *byWidth )Less (i ,j int )bool {return _cccgb .Values [i ].Width < _cccgb .Values [j ].Width };
func (_faggc *ClassedPoints )ySortFunction ()func (_fdgfb int ,_dfffe int )bool {return func (_dgef ,_gfce int )bool {return _faggc .YAtIndex (_dgef )< _faggc .YAtIndex (_gfce )};};func (_gbbdb *Bitmaps )SortByHeight (){_dfgab :=(*byHeight )(_gbbdb );_e .Sort (_dfgab )};
func _aafd (_ccbd *Bitmap ,_baca *Bitmap ,_ebdfb *Selection ,_cade **Bitmap )(*Bitmap ,error ){const _agff ="\u0070\u0072\u006f\u0063\u0065\u0073\u0073\u004d\u006f\u0072\u0070\u0068A\u0072\u0067\u0073\u0031";if _baca ==nil {return nil ,_ga .Error (_agff ,"\u004d\u006f\u0072\u0070\u0068\u0041\u0072\u0067\u0073\u0031\u0020'\u0073\u0027\u0020\u006e\u006f\u0074\u0020\u0064\u0065\u0066i\u006e\u0065\u0064");
};if _ebdfb ==nil {return nil ,_ga .Error (_agff ,"\u004d\u006f\u0072\u0068p\u0041\u0072\u0067\u0073\u0031\u0020\u0027\u0073\u0065\u006c'\u0020n\u006f\u0074\u0020\u0064\u0065\u0066\u0069n\u0065\u0064");};_cbe ,_adcf :=_ebdfb .Height ,_ebdfb .Width ;if _cbe ==0||_adcf ==0{return nil ,_ga .Error (_agff ,"\u0073\u0065\u006c\u0065ct\u0069\u006f\u006e\u0020\u006f\u0066\u0020\u0073\u0069\u007a\u0065\u0020\u0030");
};if _ccbd ==nil {_ccbd =_baca .createTemplate ();*_cade =_baca ;return _ccbd ,nil ;};_ccbd .Width =_baca .Width ;_ccbd .Height =_baca .Height ;_ccbd .RowStride =_baca .RowStride ;_ccbd .Color =_baca .Color ;_ccbd .Data =make ([]byte ,_baca .RowStride *_baca .Height );
if _ccbd ==_baca {*_cade =_baca .Copy ();}else {*_cade =_baca ;};return _ccbd ,nil ;};func (_gea *Bitmap )CountPixels ()int {return _gea .countPixels ()};func (_ecfd *Bitmap )setFourBytes (_gccb int ,_bce uint32 )error {if _gccb +3> len (_ecfd .Data )-1{return _ga .Errorf ("\u0073\u0065\u0074F\u006f\u0075\u0072\u0042\u0079\u0074\u0065\u0073","\u0069n\u0064\u0065\u0078\u003a\u0020\u0027\u0025\u0064\u0027\u0020\u006fu\u0074\u0020\u006f\u0066\u0020\u0072\u0061\u006e\u0067\u0065",_gccb );
};_ecfd .Data [_gccb ]=byte ((_bce &0xff000000)>>24);_ecfd .Data [_gccb +1]=byte ((_bce &0xff0000)>>16);_ecfd .Data [_gccb +2]=byte ((_bce &0xff00)>>8);_ecfd .Data [_gccb +3]=byte (_bce &0xff);return nil ;};func (_gffb Points )Get (i int )(Point ,error ){if i > len (_gffb )-1{return Point {},_ga .Errorf ("\u0050\u006f\u0069\u006e\u0074\u0073\u002e\u0047\u0065\u0074","\u0069n\u0064\u0065\u0078\u003a\u0020\u0027\u0025\u0064\u0027\u0020\u006fu\u0074\u0020\u006f\u0066\u0020\u0072\u0061\u006e\u0067\u0065",i );
};return _gffb [i ],nil ;};type BoundaryCondition int ;func TstImageBitmapInverseData ()[]byte {_fbfbe :=_abdg .Copy ();_fbfbe .InverseData ();return _fbfbe .Data ;};const (AsymmetricMorphBC BoundaryCondition =iota ;SymmetricMorphBC ;);func (_age *Bitmap )GetByte (index int )(byte ,error ){if index > len (_age .Data )-1||index < 0{return 0,_ga .Errorf ("\u0047e\u0074\u0042\u0079\u0074\u0065","\u0069\u006e\u0064\u0065x:\u0020\u0025\u0064\u0020\u006f\u0075\u0074\u0020\u006f\u0066\u0020\u0072\u0061\u006eg\u0065",index );
};return _age .Data [index ],nil ;};type Boxes []*_c .Rectangle ;func _fde (_cfda ,_dfea int )int {if _cfda > _dfea {return _cfda ;};return _dfea ;};const (ComponentConn Component =iota ;ComponentCharacters ;ComponentWords ;);func (_acfe *Bitmap )SetByte (index int ,v byte )error {if index > len (_acfe .Data )-1||index < 0{return _ga .Errorf ("\u0053e\u0074\u0042\u0079\u0074\u0065","\u0069\u006e\u0064\u0065x \u006f\u0075\u0074\u0020\u006f\u0066\u0020\u0072\u0061\u006e\u0067\u0065\u003a\u0020%\u0064",index );
};_acfe .Data [index ]=v ;return nil ;};func _afd (_egc ,_aad *Bitmap ,_bbeg int ,_gde []byte ,_dbca int )(_cgg error ){const _bba ="\u0072\u0065\u0064uc\u0065\u0052\u0061\u006e\u006b\u0042\u0069\u006e\u0061\u0072\u0079\u0032\u004c\u0065\u0076\u0065\u006c\u0031";
var (_daf ,_ggga ,_aace ,_dda ,_fbg ,_ccg ,_aab ,_aaf int ;_fea ,_gdf uint32 ;_ce ,_ec byte ;_gecc uint16 ;);_dab :=make ([]byte ,4);_fae :=make ([]byte ,4);for _aace =0;_aace < _egc .Height -1;_aace ,_dda =_aace +2,_dda +1{_daf =_aace *_egc .RowStride ;
_ggga =_dda *_aad .RowStride ;for _fbg ,_ccg =0,0;_fbg < _dbca ;_fbg ,_ccg =_fbg +4,_ccg +1{for _aab =0;_aab < 4;_aab ++{_aaf =_daf +_fbg +_aab ;if _aaf <=len (_egc .Data )-1&&_aaf < _daf +_egc .RowStride {_dab [_aab ]=_egc .Data [_aaf ];}else {_dab [_aab ]=0x00;
};_aaf =_daf +_egc .RowStride +_fbg +_aab ;if _aaf <=len (_egc .Data )-1&&_aaf < _daf +(2*_egc .RowStride ){_fae [_aab ]=_egc .Data [_aaf ];}else {_fae [_aab ]=0x00;};};_fea =_gg .BigEndian .Uint32 (_dab );_gdf =_gg .BigEndian .Uint32 (_fae );_gdf |=_fea ;
_gdf |=_gdf <<1;_gdf &=0xaaaaaaaa;_fea =_gdf |(_gdf <<7);_ce =byte (_fea >>24);_ec =byte ((_fea >>8)&0xff);_aaf =_ggga +_ccg ;if _aaf +1==len (_aad .Data )-1||_aaf +1>=_ggga +_aad .RowStride {_aad .Data [_aaf ]=_gde [_ce ];}else {_gecc =(uint16 (_gde [_ce ])<<8)|uint16 (_gde [_ec ]);
if _cgg =_aad .setTwoBytes (_aaf ,_gecc );_cgg !=nil {return _ga .Wrapf (_cgg ,_bba ,"s\u0065\u0074\u0074\u0069\u006e\u0067 \u0074\u0077\u006f\u0020\u0062\u0079t\u0065\u0073\u0020\u0066\u0061\u0069\u006ce\u0064\u002c\u0020\u0069\u006e\u0064\u0065\u0078\u003a\u0020%\u0064",_aaf );
};_ccg ++;};};};return nil ;};func _efbg (_cbaa *Bitmap ,_cagd *Bitmap ,_fcbb int )(_febce error ){const _eggdb ="\u0073\u0065\u0065\u0064\u0066\u0069\u006c\u006c\u0042\u0069\u006e\u0061r\u0079\u004c\u006f\u0077";_degd :=_dfce (_cbaa .Height ,_cagd .Height );
_bdcf :=_dfce (_cbaa .RowStride ,_cagd .RowStride );switch _fcbb {case 4:_febce =_fedc (_cbaa ,_cagd ,_degd ,_bdcf );case 8:_febce =_aadg (_cbaa ,_cagd ,_degd ,_bdcf );default:return _ga .Errorf (_eggdb ,"\u0063\u006f\u006e\u006e\u0065\u0063\u0074\u0069\u0076\u0069\u0074\u0079\u0020\u006d\u0075\u0073\u0074\u0020\u0062\u0065\u0020\u0034\u0020\u006fr\u0020\u0038\u0020\u002d\u0020i\u0073\u003a \u0027\u0025\u0064\u0027",_fcbb );
};if _febce !=nil {return _ga .Wrap (_febce ,_eggdb ,"");};return nil ;};func _cgeb (_efge ,_dgdgd ,_fcee *Bitmap )(*Bitmap ,error ){const _abg ="\u0073\u0075\u0062\u0074\u0072\u0061\u0063\u0074";if _dgdgd ==nil {return nil ,_ga .Error (_abg ,"'\u0073\u0031\u0027\u0020\u0069\u0073\u0020\u006e\u0069\u006c");
};if _fcee ==nil {return nil ,_ga .Error (_abg ,"'\u0073\u0032\u0027\u0020\u0069\u0073\u0020\u006e\u0069\u006c");};var _fgfe error ;switch {case _efge ==_dgdgd :if _fgfe =_efge .RasterOperation (0,0,_dgdgd .Width ,_dgdgd .Height ,PixNotSrcAndDst ,_fcee ,0,0);
_fgfe !=nil {return nil ,_ga .Wrap (_fgfe ,_abg ,"\u0064 \u003d\u003d\u0020\u0073\u0031");};case _efge ==_fcee :if _fgfe =_efge .RasterOperation (0,0,_dgdgd .Width ,_dgdgd .Height ,PixNotSrcAndDst ,_dgdgd ,0,0);_fgfe !=nil {return nil ,_ga .Wrap (_fgfe ,_abg ,"\u0064 \u003d\u003d\u0020\u0073\u0032");
};default:_efge ,_fgfe =_acda (_efge ,_dgdgd );if _fgfe !=nil {return nil ,_ga .Wrap (_fgfe ,_abg ,"");};if _fgfe =_efge .RasterOperation (0,0,_dgdgd .Width ,_dgdgd .Height ,PixNotSrcAndDst ,_fcee ,0,0);_fgfe !=nil {return nil ,_ga .Wrap (_fgfe ,_abg ,"\u0064e\u0066\u0061\u0075\u006c\u0074");
};};return _efge ,nil ;};func TstPSymbol (t *_f .T )*Bitmap {t .Helper ();_cbbfd :=New (5,8);_gb .NoError (t ,_cbbfd .SetPixel (0,0,1));_gb .NoError (t ,_cbbfd .SetPixel (1,0,1));_gb .NoError (t ,_cbbfd .SetPixel (2,0,1));_gb .NoError (t ,_cbbfd .SetPixel (3,0,1));
_gb .NoError (t ,_cbbfd .SetPixel (4,1,1));_gb .NoError (t ,_cbbfd .SetPixel (0,1,1));_gb .NoError (t ,_cbbfd .SetPixel (4,2,1));_gb .NoError (t ,_cbbfd .SetPixel (0,2,1));_gb .NoError (t ,_cbbfd .SetPixel (4,3,1));_gb .NoError (t ,_cbbfd .SetPixel (0,3,1));
_gb .NoError (t ,_cbbfd .SetPixel (0,4,1));_gb .NoError (t ,_cbbfd .SetPixel (1,4,1));_gb .NoError (t ,_cbbfd .SetPixel (2,4,1));_gb .NoError (t ,_cbbfd .SetPixel (3,4,1));_gb .NoError (t ,_cbbfd .SetPixel (0,5,1));_gb .NoError (t ,_cbbfd .SetPixel (0,6,1));
_gb .NoError (t ,_cbbfd .SetPixel (0,7,1));return _cbbfd ;};func ClipBoxToRectangle (box *_c .Rectangle ,wi ,hi int )(_ebdgc *_c .Rectangle ,_bcb error ){const _dba ="\u0043l\u0069p\u0042\u006f\u0078\u0054\u006fR\u0065\u0063t\u0061\u006e\u0067\u006c\u0065";
if box ==nil {return nil ,_ga .Error (_dba ,"\u0027\u0062\u006f\u0078\u0027\u0020\u006e\u006f\u0074\u0020\u0064\u0065f\u0069\u006e\u0065\u0064");};if box .Min .X >=wi ||box .Min .Y >=hi ||box .Max .X <=0||box .Max .Y <=0{return nil ,_ga .Error (_dba ,"\u0027\u0062\u006fx'\u0020\u006f\u0075\u0074\u0073\u0069\u0064\u0065\u0020\u0072\u0065\u0063\u0074\u0061\u006e\u0067\u006c\u0065");
};_bfca :=*box ;_ebdgc =&_bfca ;if _ebdgc .Min .X < 0{_ebdgc .Max .X +=_ebdgc .Min .X ;_ebdgc .Min .X =0;};if _ebdgc .Min .Y < 0{_ebdgc .Max .Y +=_ebdgc .Min .Y ;_ebdgc .Min .Y =0;};if _ebdgc .Max .X > wi {_ebdgc .Max .X =wi ;};if _ebdgc .Max .Y > hi {_ebdgc .Max .Y =hi ;
};return _ebdgc ,nil ;};func (_eccg *ClassedPoints )Len ()int {return _eccg .IntSlice .Size ()};func _ggde (_cgded ,_fbef byte ,_feab CombinationOperator )byte {switch _feab {case CmbOpOr :return _fbef |_cgded ;case CmbOpAnd :return _fbef &_cgded ;case CmbOpXor :return _fbef ^_cgded ;
case CmbOpXNor :return ^(_fbef ^_cgded );case CmbOpNot :return ^(_fbef );default:return _fbef ;};};func (_feae *Bitmap )setEightPartlyBytes (_ddbbg ,_dgdf int ,_edf uint64 )(_edc error ){var (_caa byte ;_cgcg int ;);const _agbg ="\u0073\u0065\u0074\u0045ig\u0068\u0074\u0050\u0061\u0072\u0074\u006c\u0079\u0042\u0079\u0074\u0065\u0073";
for _geceed :=1;_geceed <=_dgdf ;_geceed ++{_cgcg =64-_geceed *8;_caa =byte (_edf >>uint (_cgcg )&0xff);_bb .Log .Trace ("\u0074\u0065\u006d\u0070\u003a\u0020\u0025\u0030\u0038\u0062\u002c\u0020\u0069\u006e\u0064\u0065\u0078\u003a %\u0064,\u0020\u0069\u0064\u0078\u003a\u0020\u0025\u0064\u002c\u0020\u0066\u0075l\u006c\u0042\u0079\u0074\u0065\u0073\u004e\u0075\u006d\u0062\u0065\u0072\u003a\u0020\u0025\u0064\u002c \u0073\u0068\u0069\u0066\u0074\u003a\u0020\u0025\u0064",_caa ,_ddbbg ,_ddbbg +_geceed -1,_dgdf ,_cgcg );
if _edc =_feae .SetByte (_ddbbg +_geceed -1,_caa );_edc !=nil {return _ga .Wrap (_edc ,_agbg ,"\u0066\u0075\u006c\u006c\u0042\u0079\u0074\u0065");};};_ecdd :=_feae .RowStride *8-_feae .Width ;if _ecdd ==0{return nil ;};_cgcg -=8;_caa =byte (_edf >>uint (_cgcg )&0xff)<<uint (_ecdd );
if _edc =_feae .SetByte (_ddbbg +_dgdf ,_caa );_edc !=nil {return _ga .Wrap (_edc ,_agbg ,"\u0070\u0061\u0064\u0064\u0065\u0064");};return nil ;};func (_cdeg *Bitmap )String ()string {var _fad ="\u000a";for _gdc :=0;_gdc < _cdeg .Height ;_gdc ++{var _aee string ;
for _dcag :=0;_dcag < _cdeg .Width ;_dcag ++{_bad :=_cdeg .GetPixel (_dcag ,_gdc );if _bad {_aee +="\u0031";}else {_aee +="\u0030";};};_fad +=_aee +"\u000a";};return _fad ;};func _beggf (_ddec ,_fgc *Bitmap ,_cabb CombinationOperator )*Bitmap {_ecgc :=New (_ddec .Width ,_ddec .Height );
for _beaf :=0;_beaf < len (_ecgc .Data );_beaf ++{_ecgc .Data [_beaf ]=_ggde (_ddec .Data [_beaf ],_fgc .Data [_beaf ],_cabb );};return _ecgc ;};func _ddecc (_feea *Bitmap ,_ecfa ,_ceedg ,_aaaef ,_abbb int ,_eccf RasterOperator ,_cdfb *Bitmap ,_ggce ,_beeg int )error {var (_gbcg bool ;
_fbgce bool ;_dabb byte ;_bfef int ;_abeb int ;_bcgfe int ;_ggcb int ;_egeb bool ;_gdde int ;_befbb int ;_abfa int ;_gbaa bool ;_cgcc byte ;_fbcae int ;_ccgf int ;_adgg int ;_cddf byte ;_bdba int ;_fada int ;_cecb uint ;_edga uint ;_cafg byte ;_dbce shift ;
_fafa bool ;_cbfcg bool ;_dfda ,_bccd int ;);if _ggce &7!=0{_fada =8-(_ggce &7);};if _ecfa &7!=0{_abeb =8-(_ecfa &7);};if _fada ==0&&_abeb ==0{_cafg =_bbaa [0];}else {if _abeb > _fada {_cecb =uint (_abeb -_fada );}else {_cecb =uint (8-(_fada -_abeb ));
};_edga =8-_cecb ;_cafg =_bbaa [_cecb ];};if (_ecfa &7)!=0{_gbcg =true ;_bfef =8-(_ecfa &7);_dabb =_bbaa [_bfef ];_bcgfe =_feea .RowStride *_ceedg +(_ecfa >>3);_ggcb =_cdfb .RowStride *_beeg +(_ggce >>3);_bdba =8-(_ggce &7);if _bfef > _bdba {_dbce =_gbbde ;
if _aaaef >=_fada {_fafa =true ;};}else {_dbce =_gcfgf ;};};if _aaaef < _bfef {_fbgce =true ;_dabb &=_bcee [8-_bfef +_aaaef ];};if !_fbgce {_gdde =(_aaaef -_bfef )>>3;if _gdde !=0{_egeb =true ;_befbb =_feea .RowStride *_ceedg +((_ecfa +_abeb )>>3);_abfa =_cdfb .RowStride *_beeg +((_ggce +_abeb )>>3);
};};_fbcae =(_ecfa +_aaaef )&7;if !(_fbgce ||_fbcae ==0){_gbaa =true ;_cgcc =_bcee [_fbcae ];_ccgf =_feea .RowStride *_ceedg +((_ecfa +_abeb )>>3)+_gdde ;_adgg =_cdfb .RowStride *_beeg +((_ggce +_abeb )>>3)+_gdde ;if _fbcae > int (_edga ){_cbfcg =true ;
};};switch _eccf {case PixSrc :if _gbcg {for _dfda =0;_dfda < _abbb ;_dfda ++{if _dbce ==_gbbde {_cddf =_cdfb .Data [_ggcb ]<<_cecb ;if _fafa {_cddf =_aacc (_cddf ,_cdfb .Data [_ggcb +1]>>_edga ,_cafg );};}else {_cddf =_cdfb .Data [_ggcb ]>>_edga ;};_feea .Data [_bcgfe ]=_aacc (_feea .Data [_bcgfe ],_cddf ,_dabb );
_bcgfe +=_feea .RowStride ;_ggcb +=_cdfb .RowStride ;};};if _egeb {for _dfda =0;_dfda < _abbb ;_dfda ++{for _bccd =0;_bccd < _gdde ;_bccd ++{_cddf =_aacc (_cdfb .Data [_abfa +_bccd ]<<_cecb ,_cdfb .Data [_abfa +_bccd +1]>>_edga ,_cafg );_feea .Data [_befbb +_bccd ]=_cddf ;
};_befbb +=_feea .RowStride ;_abfa +=_cdfb .RowStride ;};};if _gbaa {for _dfda =0;_dfda < _abbb ;_dfda ++{_cddf =_cdfb .Data [_adgg ]<<_cecb ;if _cbfcg {_cddf =_aacc (_cddf ,_cdfb .Data [_adgg +1]>>_edga ,_cafg );};_feea .Data [_ccgf ]=_aacc (_feea .Data [_ccgf ],_cddf ,_cgcc );
_ccgf +=_feea .RowStride ;_adgg +=_cdfb .RowStride ;};};case PixNotSrc :if _gbcg {for _dfda =0;_dfda < _abbb ;_dfda ++{if _dbce ==_gbbde {_cddf =_cdfb .Data [_ggcb ]<<_cecb ;if _fafa {_cddf =_aacc (_cddf ,_cdfb .Data [_ggcb +1]>>_edga ,_cafg );};}else {_cddf =_cdfb .Data [_ggcb ]>>_edga ;
};_feea .Data [_bcgfe ]=_aacc (_feea .Data [_bcgfe ],^_cddf ,_dabb );_bcgfe +=_feea .RowStride ;_ggcb +=_cdfb .RowStride ;};};if _egeb {for _dfda =0;_dfda < _abbb ;_dfda ++{for _bccd =0;_bccd < _gdde ;_bccd ++{_cddf =_aacc (_cdfb .Data [_abfa +_bccd ]<<_cecb ,_cdfb .Data [_abfa +_bccd +1]>>_edga ,_cafg );
_feea .Data [_befbb +_bccd ]=^_cddf ;};_befbb +=_feea .RowStride ;_abfa +=_cdfb .RowStride ;};};if _gbaa {for _dfda =0;_dfda < _abbb ;_dfda ++{_cddf =_cdfb .Data [_adgg ]<<_cecb ;if _cbfcg {_cddf =_aacc (_cddf ,_cdfb .Data [_adgg +1]>>_edga ,_cafg );};
_feea .Data [_ccgf ]=_aacc (_feea .Data [_ccgf ],^_cddf ,_cgcc );_ccgf +=_feea .RowStride ;_adgg +=_cdfb .RowStride ;};};case PixSrcOrDst :if _gbcg {for _dfda =0;_dfda < _abbb ;_dfda ++{if _dbce ==_gbbde {_cddf =_cdfb .Data [_ggcb ]<<_cecb ;if _fafa {_cddf =_aacc (_cddf ,_cdfb .Data [_ggcb +1]>>_edga ,_cafg );
};}else {_cddf =_cdfb .Data [_ggcb ]>>_edga ;};_feea .Data [_bcgfe ]=_aacc (_feea .Data [_bcgfe ],_cddf |_feea .Data [_bcgfe ],_dabb );_bcgfe +=_feea .RowStride ;_ggcb +=_cdfb .RowStride ;};};if _egeb {for _dfda =0;_dfda < _abbb ;_dfda ++{for _bccd =0;
_bccd < _gdde ;_bccd ++{_cddf =_aacc (_cdfb .Data [_abfa +_bccd ]<<_cecb ,_cdfb .Data [_abfa +_bccd +1]>>_edga ,_cafg );_feea .Data [_befbb +_bccd ]|=_cddf ;};_befbb +=_feea .RowStride ;_abfa +=_cdfb .RowStride ;};};if _gbaa {for _dfda =0;_dfda < _abbb ;
_dfda ++{_cddf =_cdfb .Data [_adgg ]<<_cecb ;if _cbfcg {_cddf =_aacc (_cddf ,_cdfb .Data [_adgg +1]>>_edga ,_cafg );};_feea .Data [_ccgf ]=_aacc (_feea .Data [_ccgf ],_cddf |_feea .Data [_ccgf ],_cgcc );_ccgf +=_feea .RowStride ;_adgg +=_cdfb .RowStride ;
};};case PixSrcAndDst :if _gbcg {for _dfda =0;_dfda < _abbb ;_dfda ++{if _dbce ==_gbbde {_cddf =_cdfb .Data [_ggcb ]<<_cecb ;if _fafa {_cddf =_aacc (_cddf ,_cdfb .Data [_ggcb +1]>>_edga ,_cafg );};}else {_cddf =_cdfb .Data [_ggcb ]>>_edga ;};_feea .Data [_bcgfe ]=_aacc (_feea .Data [_bcgfe ],_cddf &_feea .Data [_bcgfe ],_dabb );
_bcgfe +=_feea .RowStride ;_ggcb +=_cdfb .RowStride ;};};if _egeb {for _dfda =0;_dfda < _abbb ;_dfda ++{for _bccd =0;_bccd < _gdde ;_bccd ++{_cddf =_aacc (_cdfb .Data [_abfa +_bccd ]<<_cecb ,_cdfb .Data [_abfa +_bccd +1]>>_edga ,_cafg );_feea .Data [_befbb +_bccd ]&=_cddf ;
};_befbb +=_feea .RowStride ;_abfa +=_cdfb .RowStride ;};};if _gbaa {for _dfda =0;_dfda < _abbb ;_dfda ++{_cddf =_cdfb .Data [_adgg ]<<_cecb ;if _cbfcg {_cddf =_aacc (_cddf ,_cdfb .Data [_adgg +1]>>_edga ,_cafg );};_feea .Data [_ccgf ]=_aacc (_feea .Data [_ccgf ],_cddf &_feea .Data [_ccgf ],_cgcc );
_ccgf +=_feea .RowStride ;_adgg +=_cdfb .RowStride ;};};case PixSrcXorDst :if _gbcg {for _dfda =0;_dfda < _abbb ;_dfda ++{if _dbce ==_gbbde {_cddf =_cdfb .Data [_ggcb ]<<_cecb ;if _fafa {_cddf =_aacc (_cddf ,_cdfb .Data [_ggcb +1]>>_edga ,_cafg );};}else {_cddf =_cdfb .Data [_ggcb ]>>_edga ;
};_feea .Data [_bcgfe ]=_aacc (_feea .Data [_bcgfe ],_cddf ^_feea .Data [_bcgfe ],_dabb );_bcgfe +=_feea .RowStride ;_ggcb +=_cdfb .RowStride ;};};if _egeb {for _dfda =0;_dfda < _abbb ;_dfda ++{for _bccd =0;_bccd < _gdde ;_bccd ++{_cddf =_aacc (_cdfb .Data [_abfa +_bccd ]<<_cecb ,_cdfb .Data [_abfa +_bccd +1]>>_edga ,_cafg );
_feea .Data [_befbb +_bccd ]^=_cddf ;};_befbb +=_feea .RowStride ;_abfa +=_cdfb .RowStride ;};};if _gbaa {for _dfda =0;_dfda < _abbb ;_dfda ++{_cddf =_cdfb .Data [_adgg ]<<_cecb ;if _cbfcg {_cddf =_aacc (_cddf ,_cdfb .Data [_adgg +1]>>_edga ,_cafg );};
_feea .Data [_ccgf ]=_aacc (_feea .Data [_ccgf ],_cddf ^_feea .Data [_ccgf ],_cgcc );_ccgf +=_feea .RowStride ;_adgg +=_cdfb .RowStride ;};};case PixNotSrcOrDst :if _gbcg {for _dfda =0;_dfda < _abbb ;_dfda ++{if _dbce ==_gbbde {_cddf =_cdfb .Data [_ggcb ]<<_cecb ;
if _fafa {_cddf =_aacc (_cddf ,_cdfb .Data [_ggcb +1]>>_edga ,_cafg );};}else {_cddf =_cdfb .Data [_ggcb ]>>_edga ;};_feea .Data [_bcgfe ]=_aacc (_feea .Data [_bcgfe ],^_cddf |_feea .Data [_bcgfe ],_dabb );_bcgfe +=_feea .RowStride ;_ggcb +=_cdfb .RowStride ;
};};if _egeb {for _dfda =0;_dfda < _abbb ;_dfda ++{for _bccd =0;_bccd < _gdde ;_bccd ++{_cddf =_aacc (_cdfb .Data [_abfa +_bccd ]<<_cecb ,_cdfb .Data [_abfa +_bccd +1]>>_edga ,_cafg );_feea .Data [_befbb +_bccd ]|=^_cddf ;};_befbb +=_feea .RowStride ;_abfa +=_cdfb .RowStride ;
};};if _gbaa {for _dfda =0;_dfda < _abbb ;_dfda ++{_cddf =_cdfb .Data [_adgg ]<<_cecb ;if _cbfcg {_cddf =_aacc (_cddf ,_cdfb .Data [_adgg +1]>>_edga ,_cafg );};_feea .Data [_ccgf ]=_aacc (_feea .Data [_ccgf ],^_cddf |_feea .Data [_ccgf ],_cgcc );_ccgf +=_feea .RowStride ;
_adgg +=_cdfb .RowStride ;};};case PixNotSrcAndDst :if _gbcg {for _dfda =0;_dfda < _abbb ;_dfda ++{if _dbce ==_gbbde {_cddf =_cdfb .Data [_ggcb ]<<_cecb ;if _fafa {_cddf =_aacc (_cddf ,_cdfb .Data [_ggcb +1]>>_edga ,_cafg );};}else {_cddf =_cdfb .Data [_ggcb ]>>_edga ;
};_feea .Data [_bcgfe ]=_aacc (_feea .Data [_bcgfe ],^_cddf &_feea .Data [_bcgfe ],_dabb );_bcgfe +=_feea .RowStride ;_ggcb +=_cdfb .RowStride ;};};if _egeb {for _dfda =0;_dfda < _abbb ;_dfda ++{for _bccd =0;_bccd < _gdde ;_bccd ++{_cddf =_aacc (_cdfb .Data [_abfa +_bccd ]<<_cecb ,_cdfb .Data [_abfa +_bccd +1]>>_edga ,_cafg );
_feea .Data [_befbb +_bccd ]&=^_cddf ;};_befbb +=_feea .RowStride ;_abfa +=_cdfb .RowStride ;};};if _gbaa {for _dfda =0;_dfda < _abbb ;_dfda ++{_cddf =_cdfb .Data [_adgg ]<<_cecb ;if _cbfcg {_cddf =_aacc (_cddf ,_cdfb .Data [_adgg +1]>>_edga ,_cafg );};
_feea .Data [_ccgf ]=_aacc (_feea .Data [_ccgf ],^_cddf &_feea .Data [_ccgf ],_cgcc );_ccgf +=_feea .RowStride ;_adgg +=_cdfb .RowStride ;};};case PixSrcOrNotDst :if _gbcg {for _dfda =0;_dfda < _abbb ;_dfda ++{if _dbce ==_gbbde {_cddf =_cdfb .Data [_ggcb ]<<_cecb ;
if _fafa {_cddf =_aacc (_cddf ,_cdfb .Data [_ggcb +1]>>_edga ,_cafg );};}else {_cddf =_cdfb .Data [_ggcb ]>>_edga ;};_feea .Data [_bcgfe ]=_aacc (_feea .Data [_bcgfe ],_cddf |^_feea .Data [_bcgfe ],_dabb );_bcgfe +=_feea .RowStride ;_ggcb +=_cdfb .RowStride ;
};};if _egeb {for _dfda =0;_dfda < _abbb ;_dfda ++{for _bccd =0;_bccd < _gdde ;_bccd ++{_cddf =_aacc (_cdfb .Data [_abfa +_bccd ]<<_cecb ,_cdfb .Data [_abfa +_bccd +1]>>_edga ,_cafg );_feea .Data [_befbb +_bccd ]=_cddf |^_feea .Data [_befbb +_bccd ];};
_befbb +=_feea .RowStride ;_abfa +=_cdfb .RowStride ;};};if _gbaa {for _dfda =0;_dfda < _abbb ;_dfda ++{_cddf =_cdfb .Data [_adgg ]<<_cecb ;if _cbfcg {_cddf =_aacc (_cddf ,_cdfb .Data [_adgg +1]>>_edga ,_cafg );};_feea .Data [_ccgf ]=_aacc (_feea .Data [_ccgf ],_cddf |^_feea .Data [_ccgf ],_cgcc );
_ccgf +=_feea .RowStride ;_adgg +=_cdfb .RowStride ;};};case PixSrcAndNotDst :if _gbcg {for _dfda =0;_dfda < _abbb ;_dfda ++{if _dbce ==_gbbde {_cddf =_cdfb .Data [_ggcb ]<<_cecb ;if _fafa {_cddf =_aacc (_cddf ,_cdfb .Data [_ggcb +1]>>_edga ,_cafg );};
}else {_cddf =_cdfb .Data [_ggcb ]>>_edga ;};_feea .Data [_bcgfe ]=_aacc (_feea .Data [_bcgfe ],_cddf &^_feea .Data [_bcgfe ],_dabb );_bcgfe +=_feea .RowStride ;_ggcb +=_cdfb .RowStride ;};};if _egeb {for _dfda =0;_dfda < _abbb ;_dfda ++{for _bccd =0;_bccd < _gdde ;
_bccd ++{_cddf =_aacc (_cdfb .Data [_abfa +_bccd ]<<_cecb ,_cdfb .Data [_abfa +_bccd +1]>>_edga ,_cafg );_feea .Data [_befbb +_bccd ]=_cddf &^_feea .Data [_befbb +_bccd ];};_befbb +=_feea .RowStride ;_abfa +=_cdfb .RowStride ;};};if _gbaa {for _dfda =0;
_dfda < _abbb ;_dfda ++{_cddf =_cdfb .Data [_adgg ]<<_cecb ;if _cbfcg {_cddf =_aacc (_cddf ,_cdfb .Data [_adgg +1]>>_edga ,_cafg );};_feea .Data [_ccgf ]=_aacc (_feea .Data [_ccgf ],_cddf &^_feea .Data [_ccgf ],_cgcc );_ccgf +=_feea .RowStride ;_adgg +=_cdfb .RowStride ;
};};case PixNotPixSrcOrDst :if _gbcg {for _dfda =0;_dfda < _abbb ;_dfda ++{if _dbce ==_gbbde {_cddf =_cdfb .Data [_ggcb ]<<_cecb ;if _fafa {_cddf =_aacc (_cddf ,_cdfb .Data [_ggcb +1]>>_edga ,_cafg );};}else {_cddf =_cdfb .Data [_ggcb ]>>_edga ;};_feea .Data [_bcgfe ]=_aacc (_feea .Data [_bcgfe ],^(_cddf |_feea .Data [_bcgfe ]),_dabb );
_bcgfe +=_feea .RowStride ;_ggcb +=_cdfb .RowStride ;};};if _egeb {for _dfda =0;_dfda < _abbb ;_dfda ++{for _bccd =0;_bccd < _gdde ;_bccd ++{_cddf =_aacc (_cdfb .Data [_abfa +_bccd ]<<_cecb ,_cdfb .Data [_abfa +_bccd +1]>>_edga ,_cafg );_feea .Data [_befbb +_bccd ]=^(_cddf |_feea .Data [_befbb +_bccd ]);
};_befbb +=_feea .RowStride ;_abfa +=_cdfb .RowStride ;};};if _gbaa {for _dfda =0;_dfda < _abbb ;_dfda ++{_cddf =_cdfb .Data [_adgg ]<<_cecb ;if _cbfcg {_cddf =_aacc (_cddf ,_cdfb .Data [_adgg +1]>>_edga ,_cafg );};_feea .Data [_ccgf ]=_aacc (_feea .Data [_ccgf ],^(_cddf |_feea .Data [_ccgf ]),_cgcc );
_ccgf +=_feea .RowStride ;_adgg +=_cdfb .RowStride ;};};case PixNotPixSrcAndDst :if _gbcg {for _dfda =0;_dfda < _abbb ;_dfda ++{if _dbce ==_gbbde {_cddf =_cdfb .Data [_ggcb ]<<_cecb ;if _fafa {_cddf =_aacc (_cddf ,_cdfb .Data [_ggcb +1]>>_edga ,_cafg );
};}else {_cddf =_cdfb .Data [_ggcb ]>>_edga ;};_feea .Data [_bcgfe ]=_aacc (_feea .Data [_bcgfe ],^(_cddf &_feea .Data [_bcgfe ]),_dabb );_bcgfe +=_feea .RowStride ;_ggcb +=_cdfb .RowStride ;};};if _egeb {for _dfda =0;_dfda < _abbb ;_dfda ++{for _bccd =0;
_bccd < _gdde ;_bccd ++{_cddf =_aacc (_cdfb .Data [_abfa +_bccd ]<<_cecb ,_cdfb .Data [_abfa +_bccd +1]>>_edga ,_cafg );_feea .Data [_befbb +_bccd ]=^(_cddf &_feea .Data [_befbb +_bccd ]);};_befbb +=_feea .RowStride ;_abfa +=_cdfb .RowStride ;};};if _gbaa {for _dfda =0;
_dfda < _abbb ;_dfda ++{_cddf =_cdfb .Data [_adgg ]<<_cecb ;if _cbfcg {_cddf =_aacc (_cddf ,_cdfb .Data [_adgg +1]>>_edga ,_cafg );};_feea .Data [_ccgf ]=_aacc (_feea .Data [_ccgf ],^(_cddf &_feea .Data [_ccgf ]),_cgcc );_ccgf +=_feea .RowStride ;_adgg +=_cdfb .RowStride ;
};};case PixNotPixSrcXorDst :if _gbcg {for _dfda =0;_dfda < _abbb ;_dfda ++{if _dbce ==_gbbde {_cddf =_cdfb .Data [_ggcb ]<<_cecb ;if _fafa {_cddf =_aacc (_cddf ,_cdfb .Data [_ggcb +1]>>_edga ,_cafg );};}else {_cddf =_cdfb .Data [_ggcb ]>>_edga ;};_feea .Data [_bcgfe ]=_aacc (_feea .Data [_bcgfe ],^(_cddf ^_feea .Data [_bcgfe ]),_dabb );
_bcgfe +=_feea .RowStride ;_ggcb +=_cdfb .RowStride ;};};if _egeb {for _dfda =0;_dfda < _abbb ;_dfda ++{for _bccd =0;_bccd < _gdde ;_bccd ++{_cddf =_aacc (_cdfb .Data [_abfa +_bccd ]<<_cecb ,_cdfb .Data [_abfa +_bccd +1]>>_edga ,_cafg );_feea .Data [_befbb +_bccd ]=^(_cddf ^_feea .Data [_befbb +_bccd ]);
};_befbb +=_feea .RowStride ;_abfa +=_cdfb .RowStride ;};};if _gbaa {for _dfda =0;_dfda < _abbb ;_dfda ++{_cddf =_cdfb .Data [_adgg ]<<_cecb ;if _cbfcg {_cddf =_aacc (_cddf ,_cdfb .Data [_adgg +1]>>_edga ,_cafg );};_feea .Data [_ccgf ]=_aacc (_feea .Data [_ccgf ],^(_cddf ^_feea .Data [_ccgf ]),_cgcc );
_ccgf +=_feea .RowStride ;_adgg +=_cdfb .RowStride ;};};default:_bb .Log .Debug ("\u004f\u0070e\u0072\u0061\u0074\u0069\u006f\u006e\u003a\u0020\u0027\u0025\u0064\u0027\u0020\u006e\u006f\u0074\u0020\u0070\u0065\u0072\u006d\u0069tt\u0065\u0064",_eccf );return _ga .Error ("\u0072a\u0073t\u0065\u0072\u004f\u0070\u0047e\u006e\u0065r\u0061\u006c\u004c\u006f\u0077","\u0072\u0061\u0073\u0074\u0065\u0072\u0020\u006f\u0070\u0065r\u0061\u0074\u0069\u006f\u006e\u0020\u006eo\u0074\u0020\u0070\u0065\u0072\u006d\u0069\u0074\u0074\u0065\u0064");
};return nil ;};func _fbbf (_acee ...MorphProcess )(_fcdd error ){const _agac ="v\u0065r\u0069\u0066\u0079\u004d\u006f\u0072\u0070\u0068P\u0072\u006f\u0063\u0065ss\u0065\u0073";var _ffeg ,_agec int ;for _aeda ,_daaa :=range _acee {if _fcdd =_daaa .verify (_aeda ,&_ffeg ,&_agec );
_fcdd !=nil {return _ga .Wrap (_fcdd ,_agac ,"");};};if _agec !=0&&_ffeg !=0{return _ga .Error (_agac ,"\u004d\u006f\u0072\u0070\u0068\u0020\u0073\u0065\u0071\u0075\u0065n\u0063\u0065\u0020\u002d\u0020\u0062\u006f\u0072d\u0065r\u0020\u0061\u0064\u0064\u0065\u0064\u0020\u0062\u0075\u0074\u0020\u006e\u0065\u0074\u0020\u0072\u0065\u0064u\u0063\u0074\u0069\u006f\u006e\u0020\u006e\u006f\u0074\u0020\u0030");
};return nil ;};func HausTest (p1 ,p2 ,p3 ,p4 *Bitmap ,delX ,delY float32 ,maxDiffW ,maxDiffH int )(bool ,error ){const _gege ="\u0048\u0061\u0075\u0073\u0054\u0065\u0073\u0074";_gadfa ,_adf :=p1 .Width ,p1 .Height ;_bfeb ,_cec :=p3 .Width ,p3 .Height ;
if _eb .Abs (_gadfa -_bfeb )> maxDiffW {return false ,nil ;};if _eb .Abs (_adf -_cec )> maxDiffH {return false ,nil ;};_adad :=int (delX +_eb .Sign (delX )*0.5);_adee :=int (delY +_eb .Sign (delY )*0.5);var _gafcb error ;_gace :=p1 .CreateTemplate ();if _gafcb =_gace .RasterOperation (0,0,_gadfa ,_adf ,PixSrc ,p1 ,0,0);
_gafcb !=nil {return false ,_ga .Wrap (_gafcb ,_gege ,"p\u0031\u0020\u002d\u0053\u0052\u0043\u002d\u003e\u0020\u0074");};if _gafcb =_gace .RasterOperation (_adad ,_adee ,_gadfa ,_adf ,PixNotSrcAndDst ,p4 ,0,0);_gafcb !=nil {return false ,_ga .Wrap (_gafcb ,_gege ,"\u0021p\u0034\u0020\u0026\u0020\u0074");
};if _gace .Zero (){return false ,nil ;};if _gafcb =_gace .RasterOperation (_adad ,_adee ,_bfeb ,_cec ,PixSrc ,p3 ,0,0);_gafcb !=nil {return false ,_ga .Wrap (_gafcb ,_gege ,"p\u0033\u0020\u002d\u0053\u0052\u0043\u002d\u003e\u0020\u0074");};if _gafcb =_gace .RasterOperation (0,0,_bfeb ,_cec ,PixNotSrcAndDst ,p2 ,0,0);
_gafcb !=nil {return false ,_ga .Wrap (_gafcb ,_gege ,"\u0021p\u0032\u0020\u0026\u0020\u0074");};return _gace .Zero (),nil ;};func (_dde *Bitmap )setAll ()error {_geg :=_bebe (_dde ,0,0,_dde .Width ,_dde .Height ,PixSet ,nil ,0,0);if _geg !=nil {return _ga .Wrap (_geg ,"\u0073\u0065\u0074\u0041\u006c\u006c","");
};return nil ;};func (_dbda *Bitmap )equivalent (_bdf *Bitmap )bool {if _dbda ==_bdf {return true ;};if !_dbda .SizesEqual (_bdf ){return false ;};_agf :=_beggf (_dbda ,_bdf ,CmbOpXor );_egd :=_dbda .countPixels ();_abfd :=int (0.25*float32 (_egd ));if _agf .thresholdPixelSum (_abfd ){return false ;
};var (_fceg [9][9]int ;_acb [18][9]int ;_efe [9][18]int ;_aafe int ;_feb int ;);_ebf :=9;_baaa :=_dbda .Height /_ebf ;_ebdg :=_dbda .Width /_ebf ;_ggcf ,_fcc :=_baaa /2,_ebdg /2;if _baaa < _ebdg {_ggcf =_ebdg /2;_fcc =_baaa /2;};_dgdg :=float64 (_ggcf )*float64 (_fcc )*_b .Pi ;
_ebfd :=int (float64 (_baaa *_ebdg /2)*0.9);_gbd :=int (float64 (_ebdg *_baaa /2)*0.9);for _dbfe :=0;_dbfe < _ebf ;_dbfe ++{_bfa :=_ebdg *_dbfe +_aafe ;var _ddc int ;if _dbfe ==_ebf -1{_aafe =0;_ddc =_dbda .Width ;}else {_ddc =_bfa +_ebdg ;if ((_dbda .Width -_aafe )%_ebf )> 0{_aafe ++;
_ddc ++;};};for _aaae :=0;_aaae < _ebf ;_aaae ++{_gdg :=_baaa *_aaae +_feb ;var _gca int ;if _aaae ==_ebf -1{_feb =0;_gca =_dbda .Height ;}else {_gca =_gdg +_baaa ;if (_dbda .Height -_feb )%_ebf > 0{_feb ++;_gca ++;};};var _ggba ,_gbgb ,_bgbf ,_aedb int ;
_bfag :=(_bfa +_ddc )/2;_ddbb :=(_gdg +_gca )/2;for _baef :=_bfa ;_baef < _ddc ;_baef ++{for _acfed :=_gdg ;_acfed < _gca ;_acfed ++{if _agf .GetPixel (_baef ,_acfed ){if _baef < _bfag {_ggba ++;}else {_gbgb ++;};if _acfed < _ddbb {_aedb ++;}else {_bgbf ++;
};};};};_fceg [_dbfe ][_aaae ]=_ggba +_gbgb ;_acb [_dbfe *2][_aaae ]=_ggba ;_acb [_dbfe *2+1][_aaae ]=_gbgb ;_efe [_dbfe ][_aaae *2]=_aedb ;_efe [_dbfe ][_aaae *2+1]=_bgbf ;};};for _beg :=0;_beg < _ebf *2-1;_beg ++{for _bade :=0;_bade < (_ebf -1);_bade ++{var _eead int ;
for _bdd :=0;_bdd < 2;_bdd ++{for _fefed :=0;_fefed < 2;_fefed ++{_eead +=_acb [_beg +_bdd ][_bade +_fefed ];};};if _eead > _gbd {return false ;};};};for _bfb :=0;_bfb < (_ebf -1);_bfb ++{for _fdd :=0;_fdd < ((_ebf *2)-1);_fdd ++{var _cgc int ;for _ffa :=0;
_ffa < 2;_ffa ++{for _bfc :=0;_bfc < 2;_bfc ++{_cgc +=_efe [_bfb +_ffa ][_fdd +_bfc ];};};if _cgc > _ebfd {return false ;};};};for _fbcb :=0;_fbcb < (_ebf -2);_fbcb ++{for _gfb :=0;_gfb < (_ebf -2);_gfb ++{var _aga ,_fdff int ;for _dge :=0;_dge < 3;_dge ++{for _gbf :=0;
_gbf < 3;_gbf ++{if _dge ==_gbf {_aga +=_fceg [_fbcb +_dge ][_gfb +_gbf ];};if (2-_dge )==_gbf {_fdff +=_fceg [_fbcb +_dge ][_gfb +_gbf ];};};};if _aga > _gbd ||_fdff > _gbd {return false ;};};};for _fgbg :=0;_fgbg < (_ebf -1);_fgbg ++{for _gbc :=0;_gbc < (_ebf -1);
_gbc ++{var _egb int ;for _gda :=0;_gda < 2;_gda ++{for _fbgf :=0;_fbgf < 2;_fbgf ++{_egb +=_fceg [_fgbg +_gda ][_gbc +_fbgf ];};};if float64 (_egb )> _dgdg {return false ;};};};return true ;};const (MopDilation MorphOperation =iota ;MopErosion ;MopOpening ;
MopClosing ;MopRankBinaryReduction ;MopReplicativeBinaryExpansion ;MopAddBorder ;);func _dfce (_cggc ,_eced int )int {if _cggc < _eced {return _cggc ;};return _eced ;};type ClassedPoints struct{*Points ;_eb .IntSlice ;_bbba func (_fgaa ,_efaf int )bool ;
};func NewClassedPoints (points *Points ,classes _eb .IntSlice )(*ClassedPoints ,error ){const _cfef ="\u004e\u0065w\u0043\u006c\u0061s\u0073\u0065\u0064\u0050\u006f\u0069\u006e\u0074\u0073";if points ==nil {return nil ,_ga .Error (_cfef ,"\u0070\u0072\u006f\u0076id\u0065\u0064\u0020\u006e\u0069\u006c\u0020\u0070\u006f\u0069\u006e\u0074\u0073");
};if classes ==nil {return nil ,_ga .Error (_cfef ,"p\u0072o\u0076\u0069\u0064\u0065\u0064\u0020\u006e\u0069l\u0020\u0063\u006c\u0061ss\u0065\u0073");};_afgc :=&ClassedPoints {Points :points ,IntSlice :classes };if _cfff :=_afgc .validateIntSlice ();_cfff !=nil {return nil ,_ga .Wrap (_cfff ,_cfef ,"");
};return _afgc ,nil ;};type Selection struct{Height ,Width int ;Cx ,Cy int ;Name string ;Data [][]SelectionValue ;};var (_gfcb =_gaa ();_eedbc =_geb ();_dce =_eea (););func TstDSymbol (t *_f .T ,scale ...int )*Bitmap {_fbbe ,_abfea :=NewWithData (4,5,[]byte {0xf0,0x90,0x90,0x90,0xE0});
_gb .NoError (t ,_abfea );return TstGetScaledSymbol (t ,_fbbe ,scale ...);};type fillSegment struct{_effa int ;_bacba int ;_dcdf int ;_abbca int ;};func (_eccc *byWidth )Len ()int {return len (_eccc .Values )};func (_cabbe *ClassedPoints )Less (i ,j int )bool {return _cabbe ._bbba (i ,j )};
func (_gabe *Bitmaps )SortByWidth (){_cfddb :=(*byWidth )(_gabe );_e .Sort (_cfddb )};func (_ecbf *byHeight )Swap (i ,j int ){_ecbf .Values [i ],_ecbf .Values [j ]=_ecbf .Values [j ],_ecbf .Values [i ];if _ecbf .Boxes !=nil {_ecbf .Boxes [i ],_ecbf .Boxes [j ]=_ecbf .Boxes [j ],_ecbf .Boxes [i ];
};};var MorphBC BoundaryCondition ;func (_gefd Points )XSorter ()func (_febe ,_ecedf int )bool {return func (_aeefe ,_fcdg int )bool {return _gefd [_aeefe ].X < _gefd [_fcdg ].X };};func TstImageBitmapData ()[]byte {return _abdg .Data };type RasterOperator int ;
func TstWriteSymbols (t *_f .T ,bms *Bitmaps ,src *Bitmap ){for _abebg :=0;_abebg < bms .Size ();_abebg ++{_gfdc :=bms .Values [_abebg ];_gcdaa :=bms .Boxes [_abebg ];_bcdd :=src .RasterOperation (_gcdaa .Min .X ,_gcdaa .Min .Y ,_gfdc .Width ,_gfdc .Height ,PixSrc ,_gfdc ,0,0);
_gb .NoError (t ,_bcdd );};};func _fedc (_bedd ,_badb *Bitmap ,_beeb ,_adcee int )(_aded error ){const _bfbbe ="\u0073e\u0065d\u0066\u0069\u006c\u006c\u0042i\u006e\u0061r\u0079\u004c\u006f\u0077\u0034";var (_egba ,_eggee ,_faec ,_afcce int ;_dece ,_gcdab ,_acfb ,_bbbg ,_dgfd ,_faagg ,_dfga byte ;
);for _egba =0;_egba < _beeb ;_egba ++{_faec =_egba *_bedd .RowStride ;_afcce =_egba *_badb .RowStride ;for _eggee =0;_eggee < _adcee ;_eggee ++{_dece ,_aded =_bedd .GetByte (_faec +_eggee );if _aded !=nil {return _ga .Wrap (_aded ,_bfbbe ,"\u0066i\u0072\u0073\u0074\u0020\u0067\u0065t");
};_gcdab ,_aded =_badb .GetByte (_afcce +_eggee );if _aded !=nil {return _ga .Wrap (_aded ,_bfbbe ,"\u0073\u0065\u0063\u006f\u006e\u0064\u0020\u0067\u0065\u0074");};if _egba > 0{_acfb ,_aded =_bedd .GetByte (_faec -_bedd .RowStride +_eggee );if _aded !=nil {return _ga .Wrap (_aded ,_bfbbe ,"\u0069\u0020\u003e \u0030");
};_dece |=_acfb ;};if _eggee > 0{_bbbg ,_aded =_bedd .GetByte (_faec +_eggee -1);if _aded !=nil {return _ga .Wrap (_aded ,_bfbbe ,"\u006a\u0020\u003e \u0030");};_dece |=_bbbg <<7;};_dece &=_gcdab ;if _dece ==0||(^_dece )==0{if _aded =_bedd .SetByte (_faec +_eggee ,_dece );
_aded !=nil {return _ga .Wrap (_aded ,_bfbbe ,"b\u0074\u0020\u003d\u003d 0\u0020|\u007c\u0020\u0028\u005e\u0062t\u0029\u0020\u003d\u003d\u0020\u0030");};continue ;};for {_dfga =_dece ;_dece =(_dece |(_dece >>1)|(_dece <<1))&_gcdab ;if (_dece ^_dfga )==0{if _aded =_bedd .SetByte (_faec +_eggee ,_dece );
_aded !=nil {return _ga .Wrap (_aded ,_bfbbe ,"\u0073\u0065\u0074\u0074\u0069\u006e\u0067\u0020\u0070\u0072\u0065\u0076 \u0062\u0079\u0074\u0065");};break ;};};};};for _egba =_beeb -1;_egba >=0;_egba --{_faec =_egba *_bedd .RowStride ;_afcce =_egba *_badb .RowStride ;
for _eggee =_adcee -1;_eggee >=0;_eggee --{if _dece ,_aded =_bedd .GetByte (_faec +_eggee );_aded !=nil {return _ga .Wrap (_aded ,_bfbbe ,"\u0072\u0065\u0076\u0065\u0072\u0073\u0065\u0020\u0066\u0069\u0072\u0073t\u0020\u0067\u0065\u0074");};if _gcdab ,_aded =_badb .GetByte (_afcce +_eggee );
_aded !=nil {return _ga .Wrap (_aded ,_bfbbe ,"r\u0065\u0076\u0065\u0072se\u0020g\u0065\u0074\u0020\u006d\u0061s\u006b\u0020\u0062\u0079\u0074\u0065");};if _egba < _beeb -1{if _dgfd ,_aded =_bedd .GetByte (_faec +_bedd .RowStride +_eggee );_aded !=nil {return _ga .Wrap (_aded ,_bfbbe ,"\u0072\u0065v\u0065\u0072\u0073e\u0020\u0069\u0020\u003c\u0020\u0068\u0020\u002d\u0031");
};_dece |=_dgfd ;};if _eggee < _adcee -1{if _faagg ,_aded =_bedd .GetByte (_faec +_eggee +1);_aded !=nil {return _ga .Wrap (_aded ,_bfbbe ,"\u0072\u0065\u0076\u0065rs\u0065\u0020\u006a\u0020\u003c\u0020\u0077\u0070\u006c\u0020\u002d\u0020\u0031");};_dece |=_faagg >>7;
};_dece &=_gcdab ;if _dece ==0||(^_dece )==0{if _aded =_bedd .SetByte (_faec +_eggee ,_dece );_aded !=nil {return _ga .Wrap (_aded ,_bfbbe ,"\u0073\u0065\u0074\u0074\u0069\u006e\u0067\u0020\u006d\u0061\u0073k\u0065\u0064\u0020\u0062\u0079\u0074\u0065\u0020\u0066\u0061i\u006c\u0065\u0064");
};continue ;};for {_dfga =_dece ;_dece =(_dece |(_dece >>1)|(_dece <<1))&_gcdab ;if (_dece ^_dfga )==0{if _aded =_bedd .SetByte (_faec +_eggee ,_dece );_aded !=nil {return _ga .Wrap (_aded ,_bfbbe ,"\u0072e\u0076\u0065\u0072\u0073e\u0020\u0073\u0065\u0074\u0074i\u006eg\u0020p\u0072\u0065\u0076\u0020\u0062\u0079\u0074e");
};break ;};};};};return nil ;};func _bcad (_eegg ,_efa *Bitmap ,_dccf *Selection )(*Bitmap ,error ){const _bcgg ="c\u006c\u006f\u0073\u0065\u0042\u0069\u0074\u006d\u0061\u0070";var _bbga error ;if _eegg ,_bbga =_bee (_eegg ,_efa ,_dccf );_bbga !=nil {return nil ,_bbga ;
};_ceecb ,_bbga :=_bgbfc (nil ,_efa ,_dccf );if _bbga !=nil {return nil ,_ga .Wrap (_bbga ,_bcgg ,"");};if _ ,_bbga =_bbff (_eegg ,_ceecb ,_dccf );_bbga !=nil {return nil ,_ga .Wrap (_bbga ,_bcgg ,"");};return _eegg ,nil ;};const (_ LocationFilter =iota ;
LocSelectWidth ;LocSelectHeight ;LocSelectXVal ;LocSelectYVal ;LocSelectIfEither ;LocSelectIfBoth ;);func _ebc (_gd *Bitmap ,_cg ,_cfe int )(*Bitmap ,error ){const _eef ="e\u0078\u0070\u0061\u006edB\u0069n\u0061\u0072\u0079\u0052\u0065p\u006c\u0069\u0063\u0061\u0074\u0065";
if _gd ==nil {return nil ,_ga .Error (_eef ,"\u0073o\u0075r\u0063\u0065\u0020\u006e\u006ft\u0020\u0064e\u0066\u0069\u006e\u0065\u0064");};if _cg <=0||_cfe <=0{return nil ,_ga .Error (_eef ,"\u0069\u006e\u0076\u0061\u006c\u0069\u0064\u0020\u0073\u0063\u0061l\u0065\u0020\u0066\u0061\u0063\u0074\u006f\u0072\u003a\u0020<\u003d\u0020\u0030");
};if _cg ==_cfe {if _cg ==1{_ffb ,_gba :=_acda (nil ,_gd );if _gba !=nil {return nil ,_ga .Wrap (_gba ,_eef ,"\u0078\u0046\u0061\u0063\u0074\u0020\u003d\u003d\u0020y\u0046\u0061\u0063\u0074");};return _ffb ,nil ;};if _cg ==2||_cg ==4||_cg ==8{_dfed ,_de :=_agd (_gd ,_cg );
if _de !=nil {return nil ,_ga .Wrap (_de ,_eef ,"\u0078\u0046a\u0063\u0074\u0020i\u006e\u0020\u007b\u0032\u002c\u0034\u002c\u0038\u007d");};return _dfed ,nil ;};};_eg :=_cg *_gd .Width ;_fbd :=_cfe *_gd .Height ;_dff :=New (_eg ,_fbd );_agc :=_dff .RowStride ;
var (_ba ,_fag ,_agb ,_dg ,_fbe int ;_bgg byte ;_fbb error ;);for _fag =0;_fag < _gd .Height ;_fag ++{_ba =_cfe *_fag *_agc ;for _agb =0;_agb < _gd .Width ;_agb ++{if _dca :=_gd .GetPixel (_agb ,_fag );_dca {_fbe =_cg *_agb ;for _dg =0;_dg < _cg ;_dg ++{_dff .setBit (_ba *8+_fbe +_dg );
};};};for _dg =1;_dg < _cfe ;_dg ++{_gbg :=_ba +_dg *_agc ;for _gcd :=0;_gcd < _agc ;_gcd ++{if _bgg ,_fbb =_dff .GetByte (_ba +_gcd );_fbb !=nil {return nil ,_ga .Wrapf (_fbb ,_eef ,"\u0072\u0065\u0070\u006cic\u0061\u0074\u0069\u006e\u0067\u0020\u006c\u0069\u006e\u0065\u003a\u0020\u0027\u0025d\u0027",_dg );
};if _fbb =_dff .SetByte (_gbg +_gcd ,_bgg );_fbb !=nil {return nil ,_ga .Wrap (_fbb ,_eef ,"\u0053\u0065\u0074\u0074in\u0067\u0020\u0062\u0079\u0074\u0065\u0020\u0066\u0061\u0069\u006c\u0065\u0064");};};};};return _dff ,nil ;};const (Vanilla Color =iota ;
Chocolate ;);func (_gagc *Bitmap )AddBorder (borderSize ,val int )(*Bitmap ,error ){if borderSize ==0{return _gagc .Copy (),nil ;};_fabf ,_adeb :=_gagc .addBorderGeneral (borderSize ,borderSize ,borderSize ,borderSize ,val );if _adeb !=nil {return nil ,_ga .Wrap (_adeb ,"\u0041d\u0064\u0042\u006f\u0072\u0064\u0065r","");
};return _fabf ,nil ;};func _fcga (_gecee ,_dcab *Bitmap ,_gcc int ,_dcf []byte ,_gdb int )(_bbf error ){const _fef ="\u0072\u0065\u0064uc\u0065\u0052\u0061\u006e\u006b\u0042\u0069\u006e\u0061\u0072\u0079\u0032\u004c\u0065\u0076\u0065\u006c\u0033";var (_gggaf ,_baa ,_dffa ,_aaa ,_ddgff ,_cff ,_ceg ,_ece int ;
_cac ,_ffd ,_dgf ,_gff uint32 ;_fdg ,_cfc byte ;_eeac uint16 ;);_cbb :=make ([]byte ,4);_fagg :=make ([]byte ,4);for _dffa =0;_dffa < _gecee .Height -1;_dffa ,_aaa =_dffa +2,_aaa +1{_gggaf =_dffa *_gecee .RowStride ;_baa =_aaa *_dcab .RowStride ;for _ddgff ,_cff =0,0;
_ddgff < _gdb ;_ddgff ,_cff =_ddgff +4,_cff +1{for _ceg =0;_ceg < 4;_ceg ++{_ece =_gggaf +_ddgff +_ceg ;if _ece <=len (_gecee .Data )-1&&_ece < _gggaf +_gecee .RowStride {_cbb [_ceg ]=_gecee .Data [_ece ];}else {_cbb [_ceg ]=0x00;};_ece =_gggaf +_gecee .RowStride +_ddgff +_ceg ;
if _ece <=len (_gecee .Data )-1&&_ece < _gggaf +(2*_gecee .RowStride ){_fagg [_ceg ]=_gecee .Data [_ece ];}else {_fagg [_ceg ]=0x00;};};_cac =_gg .BigEndian .Uint32 (_cbb );_ffd =_gg .BigEndian .Uint32 (_fagg );_dgf =_cac &_ffd ;_dgf |=_dgf <<1;_gff =_cac |_ffd ;
_gff &=_gff <<1;_ffd =_dgf &_gff ;_ffd &=0xaaaaaaaa;_cac =_ffd |(_ffd <<7);_fdg =byte (_cac >>24);_cfc =byte ((_cac >>8)&0xff);_ece =_baa +_cff ;if _ece +1==len (_dcab .Data )-1||_ece +1>=_baa +_dcab .RowStride {if _bbf =_dcab .SetByte (_ece ,_dcf [_fdg ]);
_bbf !=nil {return _ga .Wrapf (_bbf ,_fef ,"\u0069n\u0064\u0065\u0078\u003a\u0020\u0025d",_ece );};}else {_eeac =(uint16 (_dcf [_fdg ])<<8)|uint16 (_dcf [_cfc ]);if _bbf =_dcab .setTwoBytes (_ece ,_eeac );_bbf !=nil {return _ga .Wrapf (_bbf ,_fef ,"s\u0065\u0074\u0074\u0069\u006e\u0067 \u0074\u0077\u006f\u0020\u0062\u0079t\u0065\u0073\u0020\u0066\u0061\u0069\u006ce\u0064\u002c\u0020\u0069\u006e\u0064\u0065\u0078\u003a\u0020%\u0064",_ece );
};_cff ++;};};};return nil ;};func (_dcc *Bitmap )centroid (_bfafe ,_gbba []int )(Point ,error ){_fbgcd :=Point {};_dcc .setPadBits (0);if len (_bfafe )==0{_bfafe =_fcca ();};if len (_gbba )==0{_gbba =_dfeeb ();};var _defc ,_cafag ,_gagg ,_gdd ,_fccge ,_fbbd int ;
var _fgcc byte ;for _fccge =0;_fccge < _dcc .Height ;_fccge ++{_gcgaa :=_dcc .RowStride *_fccge ;_gdd =0;for _fbbd =0;_fbbd < _dcc .RowStride ;_fbbd ++{_fgcc =_dcc .Data [_gcgaa +_fbbd ];if _fgcc !=0{_gdd +=_gbba [_fgcc ];_defc +=_bfafe [_fgcc ]+_fbbd *8*_gbba [_fgcc ];
};};_gagg +=_gdd ;_cafag +=_gdd *_fccge ;};if _gagg !=0{_fbgcd .X =float32 (_defc )/float32 (_gagg );_fbgcd .Y =float32 (_cafag )/float32 (_gagg );};return _fbgcd ,nil ;};func _fabed (_gged *Bitmap ,_gegea ,_egadfd int ,_fbfb ,_bffcf int ,_afcag RasterOperator ){var (_accc bool ;
_eaeddc bool ;_dbgbd int ;_dcdb int ;_ecfcd int ;_egcb int ;_faag bool ;_bdae byte ;);_afbf :=8-(_gegea &7);_aaeb :=_bbaa [_afbf ];_cacfc :=_gged .RowStride *_egadfd +(_gegea >>3);if _fbfb < _afbf {_accc =true ;_aaeb &=_bcee [8-_afbf +_fbfb ];};if !_accc {_dbgbd =(_fbfb -_afbf )>>3;
if _dbgbd !=0{_eaeddc =true ;_dcdb =_cacfc +1;};};_ecfcd =(_gegea +_fbfb )&7;if !(_accc ||_ecfcd ==0){_faag =true ;_bdae =_bcee [_ecfcd ];_egcb =_cacfc +1+_dbgbd ;};var _bdcc ,_cggdf int ;switch _afcag {case PixClr :for _bdcc =0;_bdcc < _bffcf ;_bdcc ++{_gged .Data [_cacfc ]=_aacc (_gged .Data [_cacfc ],0x0,_aaeb );
_cacfc +=_gged .RowStride ;};if _eaeddc {for _bdcc =0;_bdcc < _bffcf ;_bdcc ++{for _cggdf =0;_cggdf < _dbgbd ;_cggdf ++{_gged .Data [_dcdb +_cggdf ]=0x0;};_dcdb +=_gged .RowStride ;};};if _faag {for _bdcc =0;_bdcc < _bffcf ;_bdcc ++{_gged .Data [_egcb ]=_aacc (_gged .Data [_egcb ],0x0,_bdae );
_egcb +=_gged .RowStride ;};};case PixSet :for _bdcc =0;_bdcc < _bffcf ;_bdcc ++{_gged .Data [_cacfc ]=_aacc (_gged .Data [_cacfc ],0xff,_aaeb );_cacfc +=_gged .RowStride ;};if _eaeddc {for _bdcc =0;_bdcc < _bffcf ;_bdcc ++{for _cggdf =0;_cggdf < _dbgbd ;
_cggdf ++{_gged .Data [_dcdb +_cggdf ]=0xff;};_dcdb +=_gged .RowStride ;};};if _faag {for _bdcc =0;_bdcc < _bffcf ;_bdcc ++{_gged .Data [_egcb ]=_aacc (_gged .Data [_egcb ],0xff,_bdae );_egcb +=_gged .RowStride ;};};case PixNotDst :for _bdcc =0;_bdcc < _bffcf ;
_bdcc ++{_gged .Data [_cacfc ]=_aacc (_gged .Data [_cacfc ],^_gged .Data [_cacfc ],_aaeb );_cacfc +=_gged .RowStride ;};if _eaeddc {for _bdcc =0;_bdcc < _bffcf ;_bdcc ++{for _cggdf =0;_cggdf < _dbgbd ;_cggdf ++{_gged .Data [_dcdb +_cggdf ]=^(_gged .Data [_dcdb +_cggdf ]);
};_dcdb +=_gged .RowStride ;};};if _faag {for _bdcc =0;_bdcc < _bffcf ;_bdcc ++{_gged .Data [_egcb ]=_aacc (_gged .Data [_egcb ],^_gged .Data [_egcb ],_bdae );_egcb +=_gged .RowStride ;};};};};func _cd (_dd ,_ab *Bitmap )(_gece error ){const _ac ="\u0065\u0078\u0070\u0061nd\u0042\u0069\u006e\u0061\u0072\u0079\u0046\u0061\u0063\u0074\u006f\u0072\u0034";
_dc :=_ab .RowStride ;_fce :=_dd .RowStride ;_fcg :=_ab .RowStride *4-_dd .RowStride ;var (_dbc ,_ag byte ;_eda uint32 ;_cde ,_fa ,_gc ,_gadg ,_df ,_aa ,_af int ;);for _gc =0;_gc < _ab .Height ;_gc ++{_cde =_gc *_dc ;_fa =4*_gc *_fce ;for _gadg =0;_gadg < _dc ;
_gadg ++{_dbc =_ab .Data [_cde +_gadg ];_eda =_eedbc [_dbc ];_aa =_fa +_gadg *4;if _fcg !=0&&(_gadg +1)*4> _dd .RowStride {for _df =_fcg ;_df > 0;_df --{_ag =byte ((_eda >>uint (_df *8))&0xff);_af =_aa +(_fcg -_df );if _gece =_dd .SetByte (_af ,_ag );_gece !=nil {return _ga .Wrapf (_gece ,_ac ,"D\u0069\u0066\u0066\u0065\u0072\u0065n\u0074\u0020\u0072\u006f\u0077\u0073\u0074\u0072\u0069d\u0065\u0073\u002e \u004b:\u0020\u0025\u0064",_df );
};};}else if _gece =_dd .setFourBytes (_aa ,_eda );_gece !=nil {return _ga .Wrap (_gece ,_ac ,"");};if _gece =_dd .setFourBytes (_fa +_gadg *4,_eedbc [_ab .Data [_cde +_gadg ]]);_gece !=nil {return _ga .Wrap (_gece ,_ac ,"");};};for _df =1;_df < 4;_df ++{for _gadg =0;
_gadg < _fce ;_gadg ++{if _gece =_dd .SetByte (_fa +_df *_fce +_gadg ,_dd .Data [_fa +_gadg ]);_gece !=nil {return _ga .Wrapf (_gece ,_ac ,"\u0063\u006f\u0070\u0079\u0020\u0027\u0071\u0075\u0061\u0064\u0072\u0061\u0062l\u0065\u0027\u0020\u006c\u0069\u006ee\u003a\u0020\u0027\u0025\u0064\u0027\u002c\u0020\u0062\u0079\u0074\u0065\u003a \u0027\u0025\u0064\u0027",_df ,_gadg );
};};};};return nil ;};func RasterOperation (dest *Bitmap ,dx ,dy ,dw ,dh int ,op RasterOperator ,src *Bitmap ,sx ,sy int )error {return _bebe (dest ,dx ,dy ,dw ,dh ,op ,src ,sx ,sy );};func (_cfgc *Boxes )selectWithIndicator (_ddae *_eb .NumSlice )(_deda *Boxes ,_aaaea error ){const _cfdc ="\u0042o\u0078\u0065\u0073\u002es\u0065\u006c\u0065\u0063\u0074W\u0069t\u0068I\u006e\u0064\u0069\u0063\u0061\u0074\u006fr";
if _cfgc ==nil {return nil ,_ga .Error (_cfdc ,"b\u006f\u0078\u0065\u0073 '\u0062'\u0020\u006e\u006f\u0074\u0020d\u0065\u0066\u0069\u006e\u0065\u0064");};if _ddae ==nil {return nil ,_ga .Error (_cfdc ,"\u0027\u006ea\u0027\u0020\u006eo\u0074\u0020\u0064\u0065\u0066\u0069\u006e\u0065\u0064");
};if len (*_ddae )!=len (*_cfgc ){return nil ,_ga .Error (_cfdc ,"\u0062\u006f\u0078\u0065\u0073\u0020\u0027\u0062\u0027\u0020\u0068\u0061\u0073\u0020\u0064\u0069\u0066\u0066\u0065\u0072\u0065\u006e\u0074\u0020s\u0069\u007a\u0065\u0020\u0074h\u0061\u006e \u0027\u006e\u0061\u0027");
};var _bfad ,_afea int ;for _dfdg :=0;_dfdg < len (*_ddae );_dfdg ++{if _bfad ,_aaaea =_ddae .GetInt (_dfdg );_aaaea !=nil {return nil ,_ga .Wrap (_aaaea ,_cfdc ,"\u0063\u0068\u0065\u0063\u006b\u0069\u006e\u0067\u0020c\u006f\u0075\u006e\u0074");};if _bfad ==1{_afea ++;
};};if _afea ==len (*_cfgc ){return _cfgc ,nil ;};_dbeg :=Boxes {};for _eeee :=0;_eeee < len (*_ddae );_eeee ++{_bfad =int ((*_ddae )[_eeee ]);if _bfad ==0{continue ;};_dbeg =append (_dbeg ,(*_cfgc )[_eeee ]);};_deda =&_dbeg ;return _deda ,nil ;};func (_effe *byHeight )Len ()int {return len (_effe .Values )};
func (_edad *Bitmaps )SelectBySize (width ,height int ,tp LocationFilter ,relation SizeComparison )(_bacd *Bitmaps ,_bgafe error ){const _agg ="B\u0069t\u006d\u0061\u0070\u0073\u002e\u0053\u0065\u006ce\u0063\u0074\u0042\u0079Si\u007a\u0065";if _edad ==nil {return nil ,_ga .Error (_agg ,"\u0027\u0062\u0027 B\u0069\u0074\u006d\u0061\u0070\u0073\u0020\u006e\u006f\u0074\u0020\u0064\u0065\u0066\u0069\u006e\u0065\u0064");
};switch tp {case LocSelectWidth ,LocSelectHeight ,LocSelectIfEither ,LocSelectIfBoth :default:return nil ,_ga .Errorf (_agg ,"\u0070\u0072\u006f\u0076\u0069d\u0065\u0064\u0020\u0069\u006e\u0076\u0061\u006c\u0069\u0064\u0020\u006c\u006fc\u0061\u0074\u0069\u006f\u006e\u0020\u0066\u0069\u006c\u0074\u0065\u0072\u0020\u0074\u0079\u0070\u0065\u003a\u0020\u0025\u0064",tp );
};switch relation {case SizeSelectIfLT ,SizeSelectIfGT ,SizeSelectIfLTE ,SizeSelectIfGTE ,SizeSelectIfEQ :default:return nil ,_ga .Errorf (_agg ,"\u0069\u006e\u0076\u0061li\u0064\u0020\u0072\u0065\u006c\u0061\u0074\u0069\u006f\u006e\u003a\u0020\u0027\u0025d\u0027",relation );
};_gdfe ,_bgafe :=_edad .makeSizeIndicator (width ,height ,tp ,relation );if _bgafe !=nil {return nil ,_ga .Wrap (_bgafe ,_agg ,"");};_bacd ,_bgafe =_edad .selectByIndicator (_gdfe );if _bgafe !=nil {return nil ,_ga .Wrap (_bgafe ,_agg ,"");};return _bacd ,nil ;
};func _ecc (_defb ,_fegb *Bitmap ,_cgcbd *Selection )(*Bitmap ,error ){const _dfeg ="\u006f\u0070\u0065\u006e";var _ceeg error ;_defb ,_ceeg =_bee (_defb ,_fegb ,_cgcbd );if _ceeg !=nil {return nil ,_ga .Wrap (_ceeg ,_dfeg ,"");};_cgaf ,_ceeg :=_bbff (nil ,_fegb ,_cgcbd );
if _ceeg !=nil {return nil ,_ga .Wrap (_ceeg ,_dfeg ,"");};_ ,_ceeg =_bgbfc (_defb ,_cgaf ,_cgcbd );if _ceeg !=nil {return nil ,_ga .Wrap (_ceeg ,_dfeg ,"");};return _defb ,nil ;};func TstFrameBitmap ()*Bitmap {return _affa .Copy ()};var _ _e .Interface =&ClassedPoints {};
func (_ffdcg *ClassedPoints )GetIntXByClass (i int )(int ,error ){const _fffbc ="\u0043\u006c\u0061\u0073s\u0065\u0064\u0050\u006f\u0069\u006e\u0074\u0073\u002e\u0047e\u0074I\u006e\u0074\u0059\u0042\u0079\u0043\u006ca\u0073\u0073";if i >=_ffdcg .IntSlice .Size (){return 0,_ga .Errorf (_fffbc ,"\u0069\u003a\u0020\u0027\u0025\u0064\u0027 \u0069\u0073\u0020o\u0075\u0074\u0020\u006ff\u0020\u0074\u0068\u0065\u0020\u0072\u0061\u006e\u0067\u0065\u0020\u006f\u0066\u0020\u0074\u0068\u0065\u0020\u0049\u006e\u0074\u0053\u006c\u0069\u0063\u0065",i );
};return int (_ffdcg .XAtIndex (i )),nil ;};func DilateBrick (d ,s *Bitmap ,hSize ,vSize int )(*Bitmap ,error ){return _febcb (d ,s ,hSize ,vSize )};var _faab [256]uint8 ;func _fcca ()[]int {_degg :=make ([]int ,256);_degg [0]=0;_degg [1]=7;var _ffgf int ;
for _ffgf =2;_ffgf < 4;_ffgf ++{_degg [_ffgf ]=_degg [_ffgf -2]+6;};for _ffgf =4;_ffgf < 8;_ffgf ++{_degg [_ffgf ]=_degg [_ffgf -4]+5;};for _ffgf =8;_ffgf < 16;_ffgf ++{_degg [_ffgf ]=_degg [_ffgf -8]+4;};for _ffgf =16;_ffgf < 32;_ffgf ++{_degg [_ffgf ]=_degg [_ffgf -16]+3;
};for _ffgf =32;_ffgf < 64;_ffgf ++{_degg [_ffgf ]=_degg [_ffgf -32]+2;};for _ffgf =64;_ffgf < 128;_ffgf ++{_degg [_ffgf ]=_degg [_ffgf -64]+1;};for _ffgf =128;_ffgf < 256;_ffgf ++{_degg [_ffgf ]=_degg [_ffgf -128];};return _degg ;};func TstTSymbol (t *_f .T ,scale ...int )*Bitmap {_dbdb ,_acgdc :=NewWithData (5,5,[]byte {0xF8,0x20,0x20,0x20,0x20});
_gb .NoError (t ,_acgdc );return TstGetScaledSymbol (t ,_dbdb ,scale ...);};func (_ffaf *Selection )findMaxTranslations ()(_aegd ,_bccb ,_dbgbc ,_ddgbg int ){for _effc :=0;_effc < _ffaf .Height ;_effc ++{for _ecedd :=0;_ecedd < _ffaf .Width ;_ecedd ++{if _ffaf .Data [_effc ][_ecedd ]==SelHit {_aegd =_fde (_aegd ,_ffaf .Cx -_ecedd );
_bccb =_fde (_bccb ,_ffaf .Cy -_effc );_dbgbc =_fde (_dbgbc ,_ecedd -_ffaf .Cx );_ddgbg =_fde (_ddgbg ,_effc -_ffaf .Cy );};};};return _aegd ,_bccb ,_dbgbc ,_ddgbg ;};func _eeed (_dgeb *_eb .Stack )(_aegba *fillSegment ,_cddc error ){const _bbfa ="\u0070\u006f\u0070\u0046\u0069\u006c\u006c\u0053\u0065g\u006d\u0065\u006e\u0074";
if _dgeb ==nil {return nil ,_ga .Error (_bbfa ,"\u006ei\u006c \u0073\u0074\u0061\u0063\u006b \u0070\u0072o\u0076\u0069\u0064\u0065\u0064");};if _dgeb .Aux ==nil {return nil ,_ga .Error (_bbfa ,"a\u0075x\u0053\u0074\u0061\u0063\u006b\u0020\u006e\u006ft\u0020\u0064\u0065\u0066in\u0065\u0064");
};_acae ,_dged :=_dgeb .Pop ();if !_dged {return nil ,nil ;};_aefg ,_dged :=_acae .(*fillSegment );if !_dged {return nil ,_ga .Error (_bbfa ,"\u0073\u0074\u0061ck\u0020\u0064\u006f\u0065\u0073\u006e\u0027\u0074\u0020c\u006fn\u0074a\u0069n\u0020\u002a\u0066\u0069\u006c\u006c\u0053\u0065\u0067\u006d\u0065\u006e\u0074");
};_aegba =&fillSegment {_aefg ._effa ,_aefg ._bacba ,_aefg ._dcdf +_aefg ._abbca ,_aefg ._abbca };_dgeb .Aux .Push (_aefg );return _aegba ,nil ;};func TstImageBitmap ()*Bitmap {return _abdg .Copy ()};const (PixSrc RasterOperator =0xc;PixDst RasterOperator =0xa;
PixNotSrc RasterOperator =0x3;PixNotDst RasterOperator =0x5;PixClr RasterOperator =0x0;PixSet RasterOperator =0xf;PixSrcOrDst RasterOperator =0xe;PixSrcAndDst RasterOperator =0x8;PixSrcXorDst RasterOperator =0x6;PixNotSrcOrDst RasterOperator =0xb;PixNotSrcAndDst RasterOperator =0x2;
PixSrcOrNotDst RasterOperator =0xd;PixSrcAndNotDst RasterOperator =0x4;PixNotPixSrcOrDst RasterOperator =0x1;PixNotPixSrcAndDst RasterOperator =0x7;PixNotPixSrcXorDst RasterOperator =0x9;PixPaint =PixSrcOrDst ;PixSubtract =PixNotSrcAndDst ;PixMask =PixSrcAndDst ;
);func (_ddgg *Bitmap )connComponentsBitmapsBB (_cafa *Bitmaps ,_fac int )(_fca *Boxes ,_bggg error ){const _fabg ="\u0063\u006f\u006enC\u006f\u006d\u0070\u006f\u006e\u0065\u006e\u0074\u0073\u0042\u0069\u0074\u006d\u0061\u0070\u0073\u0042\u0042";if _fac !=4&&_fac !=8{return nil ,_ga .Error (_fabg ,"\u0063\u006f\u006e\u006e\u0065\u0063t\u0069\u0076\u0069\u0074\u0079\u0020\u006d\u0075\u0073\u0074\u0020\u0062\u0065 \u0061\u0020\u0027\u0034\u0027\u0020\u006fr\u0020\u0027\u0038\u0027");
};if _cafa ==nil {return nil ,_ga .Error (_fabg ,"p\u0072o\u0076\u0069\u0064\u0065\u0064\u0020\u006e\u0069l\u0020\u0042\u0069\u0074ma\u0070\u0073");};if len (_cafa .Values )> 0{return nil ,_ga .Error (_fabg ,"\u0070\u0072\u006f\u0076\u0069\u0064\u0065\u0064\u0020\u006e\u006fn\u002d\u0065\u006d\u0070\u0074\u0079\u0020\u0042\u0069\u0074m\u0061\u0070\u0073");
};if _ddgg .Zero (){return &Boxes {},nil ;};var (_gbdc ,_gfec ,_afbd ,_dacf *Bitmap ;);_ddgg .setPadBits (0);if _gbdc ,_bggg =_acda (nil ,_ddgg );_bggg !=nil {return nil ,_ga .Wrap (_bggg ,_fabg ,"\u0062\u006d\u0031");};if _gfec ,_bggg =_acda (nil ,_ddgg );
_bggg !=nil {return nil ,_ga .Wrap (_bggg ,_fabg ,"\u0062\u006d\u0032");};_babc :=&_eb .Stack {};_babc .Aux =&_eb .Stack {};_fca =&Boxes {};var (_ffaa ,_cdab int ;_ebdf _c .Point ;_bdfca bool ;_gae *_c .Rectangle ;);for {if _ebdf ,_bdfca ,_bggg =_gbdc .nextOnPixel (_ffaa ,_cdab );
_bggg !=nil {return nil ,_ga .Wrap (_bggg ,_fabg ,"");};if !_bdfca {break ;};if _gae ,_bggg =_gfba (_gbdc ,_babc ,_ebdf .X ,_ebdf .Y ,_fac );_bggg !=nil {return nil ,_ga .Wrap (_bggg ,_fabg ,"");};if _bggg =_fca .Add (_gae );_bggg !=nil {return nil ,_ga .Wrap (_bggg ,_fabg ,"");
};if _afbd ,_bggg =_gbdc .clipRectangle (_gae ,nil );_bggg !=nil {return nil ,_ga .Wrap (_bggg ,_fabg ,"\u0062\u006d\u0033");};if _dacf ,_bggg =_gfec .clipRectangle (_gae ,nil );_bggg !=nil {return nil ,_ga .Wrap (_bggg ,_fabg ,"\u0062\u006d\u0034");};
if _ ,_bggg =_edda (_afbd ,_afbd ,_dacf );_bggg !=nil {return nil ,_ga .Wrap (_bggg ,_fabg ,"\u0062m\u0033\u0020\u005e\u0020\u0062\u006d4");};if _bggg =_gfec .RasterOperation (_gae .Min .X ,_gae .Min .Y ,_gae .Dx (),_gae .Dy (),PixSrcXorDst ,_afbd ,0,0);
_bggg !=nil {return nil ,_ga .Wrap (_bggg ,_fabg ,"\u0062\u006d\u0032\u0020\u002d\u0058\u004f\u0052\u002d>\u0020\u0062\u006d\u0033");};_cafa .AddBitmap (_afbd );_ffaa =_ebdf .X ;_cdab =_ebdf .Y ;};_cafa .Boxes =*_fca ;return _fca ,nil ;};func Rect (x ,y ,w ,h int )(*_c .Rectangle ,error ){const _faf ="b\u0069\u0074\u006d\u0061\u0070\u002e\u0052\u0065\u0063\u0074";
if x < 0{w +=x ;x =0;if w <=0{return nil ,_ga .Errorf (_faf ,"x\u003a\u0027\u0025\u0064\u0027\u0020<\u0020\u0030\u0020\u0061\u006e\u0064\u0020\u0077\u003a \u0027\u0025\u0064'\u0020<\u003d\u0020\u0030",x ,w );};};if y < 0{h +=y ;y =0;if h <=0{return nil ,_ga .Error (_faf ,"\u0079\u0020\u003c 0\u0020\u0061\u006e\u0064\u0020\u0062\u006f\u0078\u0020\u006f\u0066\u0066\u0020\u002b\u0071\u0075\u0061\u0064");
};};_gdef :=_c .Rect (x ,y ,x +w ,y +h );return &_gdef ,nil ;};func TstWordBitmap (t *_f .T ,scale ...int )*Bitmap {_cfdbc :=1;if len (scale )> 0{_cfdbc =scale [0];};_cdbe :=3;_dbba :=9+7+15+2*_cdbe ;_ebgc :=5+_cdbe +5;_caac :=New (_dbba *_cfdbc ,_ebgc *_cfdbc );
_eaddd :=&Bitmaps {};var _ddaad *int ;_cdbe *=_cfdbc ;_ceage :=0;_ddaad =&_ceage ;_gddec :=0;_cada :=TstDSymbol (t ,scale ...);TstAddSymbol (t ,_eaddd ,_cada ,_ddaad ,_gddec ,1*_cfdbc );_cada =TstOSymbol (t ,scale ...);TstAddSymbol (t ,_eaddd ,_cada ,_ddaad ,_gddec ,_cdbe );
_cada =TstISymbol (t ,scale ...);TstAddSymbol (t ,_eaddd ,_cada ,_ddaad ,_gddec ,1*_cfdbc );_cada =TstTSymbol (t ,scale ...);TstAddSymbol (t ,_eaddd ,_cada ,_ddaad ,_gddec ,_cdbe );_cada =TstNSymbol (t ,scale ...);TstAddSymbol (t ,_eaddd ,_cada ,_ddaad ,_gddec ,1*_cfdbc );
_cada =TstOSymbol (t ,scale ...);TstAddSymbol (t ,_eaddd ,_cada ,_ddaad ,_gddec ,1*_cfdbc );_cada =TstWSymbol (t ,scale ...);TstAddSymbol (t ,_eaddd ,_cada ,_ddaad ,_gddec ,0);*_ddaad =0;_gddec =5*_cfdbc +_cdbe ;_cada =TstOSymbol (t ,scale ...);TstAddSymbol (t ,_eaddd ,_cada ,_ddaad ,_gddec ,1*_cfdbc );
_cada =TstRSymbol (t ,scale ...);TstAddSymbol (t ,_eaddd ,_cada ,_ddaad ,_gddec ,_cdbe );_cada =TstNSymbol (t ,scale ...);TstAddSymbol (t ,_eaddd ,_cada ,_ddaad ,_gddec ,1*_cfdbc );_cada =TstESymbol (t ,scale ...);TstAddSymbol (t ,_eaddd ,_cada ,_ddaad ,_gddec ,1*_cfdbc );
_cada =TstVSymbol (t ,scale ...);TstAddSymbol (t ,_eaddd ,_cada ,_ddaad ,_gddec ,1*_cfdbc );_cada =TstESymbol (t ,scale ...);TstAddSymbol (t ,_eaddd ,_cada ,_ddaad ,_gddec ,1*_cfdbc );_cada =TstRSymbol (t ,scale ...);TstAddSymbol (t ,_eaddd ,_cada ,_ddaad ,_gddec ,0);
TstWriteSymbols (t ,_eaddd ,_caac );return _caac ;};func (_ecg *Bitmap )resizeImageData (_fadd *Bitmap )error {if _fadd ==nil {return _ga .Error ("\u0072e\u0073i\u007a\u0065\u0049\u006d\u0061\u0067\u0065\u0044\u0061\u0074\u0061","\u0073r\u0063 \u0069\u0073\u0020\u006e\u006ft\u0020\u0064e\u0066\u0069\u006e\u0065\u0064");
};if _ecg .SizesEqual (_fadd ){return nil ;};_ecg .Data =make ([]byte ,len (_fadd .Data ));_ecg .Width =_fadd .Width ;_ecg .Height =_fadd .Height ;_ecg .RowStride =_fadd .RowStride ;return nil ;};type BitmapsArray struct{Values []*Bitmaps ;Boxes []*_c .Rectangle ;
};func (_fcbe *ClassedPoints )SortByY (){_fcbe ._bbba =_fcbe .ySortFunction ();_e .Sort (_fcbe )};func (_aed *Bitmap )GetBitOffset (x int )int {return x &0x07};func NewWithData (width ,height int ,data []byte )(*Bitmap ,error ){const _cae ="N\u0065\u0077\u0057\u0069\u0074\u0068\u0044\u0061\u0074\u0061";
_dgc :=_fbgc (width ,height );_dgc .Data =data ;if len (data )< height *_dgc .RowStride {return nil ,_ga .Errorf (_cae ,"\u0069\u006e\u0076\u0061\u006c\u0069\u0064\u0020\u0064\u0061\u0074\u0061\u0020l\u0065\u006e\u0067\u0074\u0068\u003a \u0025\u0064\u0020\u002d\u0020\u0073\u0068\u006f\u0075\u006c\u0064\u0020\u0062e\u003a\u0020\u0025\u0064",len (data ),height *_dgc .RowStride );
};return _dgc ,nil ;};func _abdf (_gdcd *Bitmap ,_ccdcg ...MorphProcess )(_fdabd *Bitmap ,_egadf error ){const _egeed ="\u006d\u006f\u0072\u0070\u0068\u0053\u0065\u0071\u0075\u0065\u006e\u0063\u0065";if _gdcd ==nil {return nil ,_ga .Error (_egeed ,"\u006d\u006f\u0072\u0070\u0068\u0053\u0065\u0071\u0075\u0065\u006e\u0063\u0065 \u0073\u006f\u0075\u0072\u0063\u0065 \u0062\u0069\u0074\u006d\u0061\u0070\u0020\u006e\u006f\u0074\u0020\u0064\u0065f\u0069\u006e\u0065\u0064");
};if len (_ccdcg )==0{return nil ,_ga .Error (_egeed ,"m\u006f\u0072\u0070\u0068\u0053\u0065q\u0075\u0065\u006e\u0063\u0065\u002c \u0073\u0065\u0071\u0075\u0065\u006e\u0063e\u0020\u006e\u006f\u0074\u0020\u0064\u0065\u0066\u0069\u006ee\u0064");};if _egadf =_fbbf (_ccdcg ...);
_egadf !=nil {return nil ,_ga .Wrap (_egadf ,_egeed ,"");};var _eaedd ,_abbc ,_aeag int ;_fdabd =_gdcd .Copy ();for _ ,_ccda :=range _ccdcg {switch _ccda .Operation {case MopDilation :_eaedd ,_abbc =_ccda .getWidthHeight ();_fdabd ,_egadf =DilateBrick (nil ,_fdabd ,_eaedd ,_abbc );
if _egadf !=nil {return nil ,_ga .Wrap (_egadf ,_egeed ,"");};case MopErosion :_eaedd ,_abbc =_ccda .getWidthHeight ();_fdabd ,_egadf =_cfgcg (nil ,_fdabd ,_eaedd ,_abbc );if _egadf !=nil {return nil ,_ga .Wrap (_egadf ,_egeed ,"");};case MopOpening :_eaedd ,_abbc =_ccda .getWidthHeight ();
_fdabd ,_egadf =_cbge (nil ,_fdabd ,_eaedd ,_abbc );if _egadf !=nil {return nil ,_ga .Wrap (_egadf ,_egeed ,"");};case MopClosing :_eaedd ,_abbc =_ccda .getWidthHeight ();_fdabd ,_egadf =_afgbc (nil ,_fdabd ,_eaedd ,_abbc );if _egadf !=nil {return nil ,_ga .Wrap (_egadf ,_egeed ,"");
};case MopRankBinaryReduction :_fdabd ,_egadf =_fe (_fdabd ,_ccda .Arguments ...);if _egadf !=nil {return nil ,_ga .Wrap (_egadf ,_egeed ,"");};case MopReplicativeBinaryExpansion :_fdabd ,_egadf =_gdcb (_fdabd ,_ccda .Arguments [0]);if _egadf !=nil {return nil ,_ga .Wrap (_egadf ,_egeed ,"");
};case MopAddBorder :_aeag =_ccda .Arguments [0];_fdabd ,_egadf =_fdabd .AddBorder (_aeag ,0);if _egadf !=nil {return nil ,_ga .Wrap (_egadf ,_egeed ,"");};default:return nil ,_ga .Error (_egeed ,"i\u006e\u0076\u0061\u006c\u0069\u0064\u0020\u006d\u006fr\u0070\u0068\u004f\u0070\u0065\u0072\u0061ti\u006f\u006e\u0020\u0070r\u006f\u0076\u0069\u0064\u0065\u0064\u0020\u0074\u006f t\u0068\u0065 \u0073\u0065\u0071\u0075\u0065\u006e\u0063\u0065");
};};if _aeag > 0{_fdabd ,_egadf =_fdabd .RemoveBorder (_aeag );if _egadf !=nil {return nil ,_ga .Wrap (_egadf ,_egeed ,"\u0062\u006f\u0072\u0064\u0065\u0072\u0020\u003e\u0020\u0030");};};return _fdabd ,nil ;};func TstESymbol (t *_f .T ,scale ...int )*Bitmap {_aeegb ,_egab :=NewWithData (4,5,[]byte {0xF0,0x80,0xE0,0x80,0xF0});
_gb .NoError (t ,_egab );return TstGetScaledSymbol (t ,_aeegb ,scale ...);};func _dcfe (_ecaf uint ,_gab byte )byte {return _gab >>_ecaf <<_ecaf };func _bebe (_dcddc *Bitmap ,_agbf ,_ccge ,_edaa ,_bdcab int ,_ffda RasterOperator ,_ffgfe *Bitmap ,_accf ,_abda int )error {const _cgda ="\u0072a\u0073t\u0065\u0072\u004f\u0070\u0065\u0072\u0061\u0074\u0069\u006f\u006e";
if _dcddc ==nil {return _ga .Error (_cgda ,"\u006e\u0069\u006c\u0020\u0027\u0064\u0065\u0073\u0074\u0027\u0020\u0042i\u0074\u006d\u0061\u0070");};if _ffda ==PixDst {return nil ;};switch _ffda {case PixClr ,PixSet ,PixNotDst :_bfefg (_dcddc ,_agbf ,_ccge ,_edaa ,_bdcab ,_ffda );
return nil ;};if _ffgfe ==nil {_bb .Log .Debug ("\u0052a\u0073\u0074e\u0072\u004f\u0070\u0065r\u0061\u0074\u0069o\u006e\u0020\u0073\u006f\u0075\u0072\u0063\u0065\u0020bi\u0074\u006d\u0061p\u0020\u0069s\u0020\u006e\u006f\u0074\u0020\u0064e\u0066\u0069n\u0065\u0064");
return _ga .Error (_cgda ,"\u006e\u0069l\u0020\u0027\u0073r\u0063\u0027\u0020\u0062\u0069\u0074\u006d\u0061\u0070");};if _ecaff :=_gbde (_dcddc ,_agbf ,_ccge ,_edaa ,_bdcab ,_ffda ,_ffgfe ,_accf ,_abda );_ecaff !=nil {return _ga .Wrap (_ecaff ,_cgda ,"");
};return nil ;};func (_fgdd *Bitmaps )GroupByWidth ()(*BitmapsArray ,error ){const _gacc ="\u0047\u0072\u006fu\u0070\u0042\u0079\u0057\u0069\u0064\u0074\u0068";if len (_fgdd .Values )==0{return nil ,_ga .Error (_gacc ,"\u006eo\u0020v\u0061\u006c\u0075\u0065\u0073 \u0070\u0072o\u0076\u0069\u0064\u0065\u0064");
};_beec :=&BitmapsArray {};_fgdd .SortByWidth ();_fdabf :=-1;_ffac :=-1;for _eddfg :=0;_eddfg < len (_fgdd .Values );_eddfg ++{_begc :=_fgdd .Values [_eddfg ].Width ;if _begc > _fdabf {_fdabf =_begc ;_ffac ++;_beec .Values =append (_beec .Values ,&Bitmaps {});
};_beec .Values [_ffac ].AddBitmap (_fgdd .Values [_eddfg ]);};return _beec ,nil ;};func _acda (_dae ,_bdbc *Bitmap )(*Bitmap ,error ){if _bdbc ==nil {return nil ,_ga .Error ("\u0063\u006f\u0070\u0079\u0042\u0069\u0074\u006d\u0061\u0070","\u0073o\u0075r\u0063\u0065\u0020\u006e\u006ft\u0020\u0064e\u0066\u0069\u006e\u0065\u0064");
};if _bdbc ==_dae {return _dae ,nil ;};if _dae ==nil {_dae =_bdbc .createTemplate ();copy (_dae .Data ,_bdbc .Data );return _dae ,nil ;};_bbed :=_dae .resizeImageData (_bdbc );if _bbed !=nil {return nil ,_ga .Wrap (_bbed ,"\u0063\u006f\u0070\u0079\u0042\u0069\u0074\u006d\u0061\u0070","");
};_dae .Text =_bdbc .Text ;copy (_dae .Data ,_bdbc .Data );return _dae ,nil ;};func (_aaff *Bitmap )SetDefaultPixel (){for _aaee :=range _aaff .Data {_aaff .Data [_aaee ]=byte (0xff);};};func (_ccdge *Bitmaps )WidthSorter ()func (_aafc ,_bbad int )bool {return func (_bcbc ,_adec int )bool {return _ccdge .Values [_bcbc ].Width < _ccdge .Values [_adec ].Width };
};func (_cdbad *Bitmap )GetByteIndex (x ,y int )int {return y *_cdbad .RowStride +(x >>3)};func (_adcbb *Bitmaps )AddBox (box *_c .Rectangle ){_adcbb .Boxes =append (_adcbb .Boxes ,box )};func (_cfbb *ClassedPoints )validateIntSlice ()error {const _gccbg ="\u0076\u0061l\u0069\u0064\u0061t\u0065\u0049\u006e\u0074\u0053\u006c\u0069\u0063\u0065";
for _ ,_acgb :=range _cfbb .IntSlice {if _acgb >=(_cfbb .Points .Size ()){return _ga .Errorf (_gccbg ,"c\u006c\u0061\u0073\u0073\u0020\u0069\u0064\u003a\u0020\u0027\u0025\u0064\u0027\u0020\u0069\u0073\u0020\u006e\u006f\u0074\u0020\u0061\u0020\u0076\u0061\u006ci\u0064 \u0069\u006e\u0064\u0065x\u0020\u0069n\u0020\u0074\u0068\u0065\u0020\u0070\u006f\u0069\u006e\u0074\u0073\u0020\u006f\u0066\u0020\u0073\u0069\u007a\u0065\u003a\u0020\u0025\u0064",_acgb ,_cfbb .Points .Size ());
};};return nil ;};func (_eeg *Bitmap )nextOnPixel (_ebgg ,_geed int )(_dcdd _c .Point ,_gadb bool ,_fbf error ){const _bagg ="n\u0065\u0078\u0074\u004f\u006e\u0050\u0069\u0078\u0065\u006c";_dcdd ,_gadb ,_fbf =_eeg .nextOnPixelLow (_eeg .Width ,_eeg .Height ,_eeg .RowStride ,_ebgg ,_geed );
if _fbf !=nil {return _dcdd ,false ,_ga .Wrap (_fbf ,_bagg ,"");};return _dcdd ,_gadb ,nil ;};func (_dgda *Bitmap )ConnComponents (bms *Bitmaps ,connectivity int )(_beb *Boxes ,_bbcd error ){const _feee ="B\u0069\u0074\u006d\u0061p.\u0043o\u006e\u006e\u0043\u006f\u006dp\u006f\u006e\u0065\u006e\u0074\u0073";
if _dgda ==nil {return nil ,_ga .Error (_feee ,"\u0070r\u006f\u0076\u0069\u0064e\u0064\u0020\u0065\u006d\u0070t\u0079 \u0027b\u0027\u0020\u0062\u0069\u0074\u006d\u0061p");};if connectivity !=4&&connectivity !=8{return nil ,_ga .Error (_feee ,"\u0063\u006f\u006ene\u0063\u0074\u0069\u0076\u0069\u0074\u0079\u0020\u006e\u006f\u0074\u0020\u0034\u0020\u006f\u0072\u0020\u0038");
};if bms ==nil {if _beb ,_bbcd =_dgda .connComponentsBB (connectivity );_bbcd !=nil {return nil ,_ga .Wrap (_bbcd ,_feee ,"");};}else {if _beb ,_bbcd =_dgda .connComponentsBitmapsBB (bms ,connectivity );_bbcd !=nil {return nil ,_ga .Wrap (_bbcd ,_feee ,"");
};};return _beb ,nil ;};type MorphProcess struct{Operation MorphOperation ;Arguments []int ;};func (_cggd *Bitmap )setPadBits (_fddb int ){_gcbg :=8-_cggd .Width %8;if _gcbg ==8{return ;};_baf :=_cggd .Width /8;_aea :=_bbaa [_gcbg ];if _fddb ==0{_aea ^=_aea ;
};var _baff int ;for _egf :=0;_egf < _cggd .Height ;_egf ++{_baff =_egf *_cggd .RowStride +_baf ;if _fddb ==0{_cggd .Data [_baff ]&=_aea ;}else {_cggd .Data [_baff ]|=_aea ;};};};func (_abgg *byHeight )Less (i ,j int )bool {return _abgg .Values [i ].Height < _abgg .Values [j ].Height };
func (_cafd *ClassedPoints )XAtIndex (i int )float32 {return (*_cafd .Points )[_cafd .IntSlice [i ]].X };func (_gacea *BitmapsArray )AddBox (box *_c .Rectangle ){_gacea .Boxes =append (_gacea .Boxes ,box )};func (_aegf MorphProcess )verify (_geef int ,_gbff ,_fbgg *int )error {const _edca ="\u004d\u006f\u0072\u0070hP\u0072\u006f\u0063\u0065\u0073\u0073\u002e\u0076\u0065\u0072\u0069\u0066\u0079";
switch _aegf .Operation {case MopDilation ,MopErosion ,MopOpening ,MopClosing :if len (_aegf .Arguments )!=2{return _ga .Error (_edca ,"\u004f\u0070\u0065\u0072\u0061\u0074\u0069\u006f\u006e\u003a\u0020\u0027\u0064\u0027\u002c\u0020\u0027\u0065\u0027\u002c \u0027\u006f\u0027\u002c\u0020\u0027\u0063\u0027\u0020\u0072\u0065\u0071\u0075\u0069\u0072\u0065\u0073\u0020\u0061\u0074\u0020\u006c\u0065\u0061\u0073\u0074\u0020\u0032\u0020\u0061r\u0067\u0075\u006d\u0065\u006et\u0073");
};_dfdge ,_dfag :=_aegf .getWidthHeight ();if _dfdge <=0||_dfag <=0{return _ga .Error (_edca ,"O\u0070er\u0061t\u0069o\u006e\u003a\u0020\u0027\u0064'\u002c\u0020\u0027e\u0027\u002c\u0020\u0027\u006f'\u002c\u0020\u0027c\u0027\u0020\u0020\u0072\u0065\u0071\u0075\u0069\u0072\u0065\u0073 \u0062\u006f\u0074h w\u0069\u0064\u0074\u0068\u0020\u0061n\u0064\u0020\u0068\u0065\u0069\u0067\u0068\u0074\u0020\u0074\u006f\u0020b\u0065 \u003e\u003d\u0020\u0030");
};case MopRankBinaryReduction :_ecfgc :=len (_aegf .Arguments );*_gbff +=_ecfgc ;if _ecfgc < 1||_ecfgc > 4{return _ga .Error (_edca ,"\u004f\u0070\u0065\u0072\u0061\u0074\u0069\u006f\u006e\u003a\u0020\u0027\u0072\u0027\u0020\u0072\u0065\u0071\u0075\u0069r\u0065\u0073\u0020\u0061\u0074\u0020\u006c\u0065\u0061s\u0074\u0020\u0031\u0020\u0061\u006e\u0064\u0020\u0061\u0074\u0020\u006d\u006fs\u0074\u0020\u0034\u0020\u0061\u0072g\u0075\u006d\u0065n\u0074\u0073");
};for _efga :=0;_efga < _ecfgc ;_efga ++{if _aegf .Arguments [_efga ]< 1||_aegf .Arguments [_efga ]> 4{return _ga .Error (_edca ,"\u0052\u0061\u006e\u006b\u0042\u0069n\u0061\u0072\u0079\u0052\u0065\u0064\u0075\u0063\u0074\u0069\u006f\u006e\u0020\u006c\u0065\u0076\u0065\u006c\u0020\u006du\u0073\u0074\u0020\u0062\u0065\u0020\u0069\u006e\u0020\u0072\u0061\u006e\u0067\u0065 \u00280\u002c\u0020\u0034\u003e");
};};case MopReplicativeBinaryExpansion :if len (_aegf .Arguments )==0{return _ga .Error (_edca ,"\u0052\u0065\u0070\u006c\u0069\u0063\u0061\u0074i\u0076\u0065\u0042in\u0061\u0072\u0079\u0045\u0078\u0070a\u006e\u0073\u0069\u006f\u006e\u0020\u0072\u0065\u0071\u0075\u0069\u0072\u0065\u0073\u0020o\u006e\u0065\u0020\u0061\u0072\u0067\u0075\u006de\u006e\u0074");
};_afa :=_aegf .Arguments [0];if _afa !=2&&_afa !=4&&_afa !=8{return _ga .Error (_edca ,"R\u0065\u0070\u006c\u0069\u0063\u0061\u0074\u0069\u0076\u0065\u0042\u0069\u006e\u0061\u0072\u0079\u0045\u0078\u0070\u0061\u006e\u0073\u0069\u006f\u006e\u0020m\u0075s\u0074\u0020\u0062\u0065 \u006f\u0066 \u0066\u0061\u0063\u0074\u006f\u0072\u0020\u0069\u006e\u0020\u0072\u0061\u006e\u0067\u0065\u0020\u007b\u0032\u002c\u0034\u002c\u0038\u007d");
};*_gbff -=_cbd [_afa /4];case MopAddBorder :if len (_aegf .Arguments )==0{return _ga .Error (_edca ,"\u0041\u0064\u0064B\u006f\u0072\u0064\u0065r\u0020\u0072\u0065\u0071\u0075\u0069\u0072e\u0073\u0020\u006f\u006e\u0065\u0020\u0061\u0072\u0067\u0075\u006d\u0065\u006e\u0074");
};_gcec :=_aegf .Arguments [0];if _geef > 0{return _ga .Error (_edca ,"\u0041\u0064\u0064\u0042\u006f\u0072\u0064\u0065\u0072\u0020\u006d\u0075\u0073t\u0020\u0062\u0065\u0020\u0061\u0020f\u0069\u0072\u0073\u0074\u0020\u006d\u006f\u0072\u0070\u0068\u0020\u0070\u0072o\u0063\u0065\u0073\u0073");
};if _gcec < 1{return _ga .Error (_edca ,"\u0041\u0064\u0064\u0042o\u0072\u0064\u0065\u0072\u0020\u0076\u0061\u006c\u0075\u0065 \u006co\u0077\u0065\u0072\u0020\u0074\u0068\u0061n\u0020\u0030");};*_fbgg =_gcec ;};return nil ;};type SelectionValue int ;func (_dgea *Bitmaps )ClipToBitmap (s *Bitmap )(*Bitmaps ,error ){const _eedf ="B\u0069t\u006d\u0061\u0070\u0073\u002e\u0043\u006c\u0069p\u0054\u006f\u0042\u0069tm\u0061\u0070";
if _dgea ==nil {return nil ,_ga .Error (_eedf ,"\u0042\u0069\u0074\u006dap\u0073\u0020\u006e\u006f\u0074\u0020\u0064\u0065\u0066\u0069\u006e\u0065\u0064");};if s ==nil {return nil ,_ga .Error (_eedf ,"\u0073o\u0075\u0072\u0063\u0065 \u0062\u0069\u0074\u006d\u0061p\u0020n\u006ft\u0020\u0064\u0065\u0066\u0069\u006e\u0065d");
};_daag :=len (_dgea .Values );_efbb :=&Bitmaps {Values :make ([]*Bitmap ,_daag ),Boxes :make ([]*_c .Rectangle ,_daag )};var (_gede ,_gdefg *Bitmap ;_gddef *_c .Rectangle ;_ceedge error ;);for _gegdc :=0;_gegdc < _daag ;_gegdc ++{if _gede ,_ceedge =_dgea .GetBitmap (_gegdc );
_ceedge !=nil {return nil ,_ga .Wrap (_ceedge ,_eedf ,"");};if _gddef ,_ceedge =_dgea .GetBox (_gegdc );_ceedge !=nil {return nil ,_ga .Wrap (_ceedge ,_eedf ,"");};if _gdefg ,_ceedge =s .clipRectangle (_gddef ,nil );_ceedge !=nil {return nil ,_ga .Wrap (_ceedge ,_eedf ,"");
};if _gdefg ,_ceedge =_gdefg .And (_gede );_ceedge !=nil {return nil ,_ga .Wrap (_ceedge ,_eedf ,"");};_efbb .Values [_gegdc ]=_gdefg ;_efbb .Boxes [_gegdc ]=_gddef ;};return _efbb ,nil ;};func (_acgg *Bitmaps )Size ()int {return len (_acgg .Values )};
func _da (_ca ,_gad *Bitmap )(_db error ){const _ggg ="\u0065\u0078\u0070\u0061nd\u0042\u0069\u006e\u0061\u0072\u0079\u0046\u0061\u0063\u0074\u006f\u0072\u0032";_ge :=_gad .RowStride ;_fc :=_ca .RowStride ;var (_dac byte ;_fd uint16 ;_ee ,_bg ,_gf ,_gec ,_a int ;
);for _gf =0;_gf < _gad .Height ;_gf ++{_ee =_gf *_ge ;_bg =2*_gf *_fc ;for _gec =0;_gec < _ge ;_gec ++{_dac =_gad .Data [_ee +_gec ];_fd =_gfcb [_dac ];_a =_bg +_gec *2;if _ca .RowStride !=_gad .RowStride *2&&(_gec +1)*2> _ca .RowStride {_db =_ca .SetByte (_a ,byte (_fd >>8));
}else {_db =_ca .setTwoBytes (_a ,_fd );};if _db !=nil {return _ga .Wrap (_db ,_ggg ,"");};};for _gec =0;_gec < _fc ;_gec ++{_a =_bg +_fc +_gec ;_dac =_ca .Data [_bg +_gec ];if _db =_ca .SetByte (_a ,_dac );_db !=nil {return _ga .Wrapf (_db ,_ggg ,"c\u006f\u0070\u0079\u0020\u0064\u006fu\u0062\u006c\u0065\u0064\u0020\u006ci\u006e\u0065\u003a\u0020\u0027\u0025\u0064'\u002c\u0020\u0042\u0079\u0074\u0065\u003a\u0020\u0027\u0025d\u0027",_bg +_gec ,_bg +_fc +_gec );
};};};return nil ;};func _geb ()(_aba [256]uint32 ){for _gag :=0;_gag < 256;_gag ++{if _gag &0x01!=0{_aba [_gag ]|=0xf;};if _gag &0x02!=0{_aba [_gag ]|=0xf0;};if _gag &0x04!=0{_aba [_gag ]|=0xf00;};if _gag &0x08!=0{_aba [_gag ]|=0xf000;};if _gag &0x10!=0{_aba [_gag ]|=0xf0000;
};if _gag &0x20!=0{_aba [_gag ]|=0xf00000;};if _gag &0x40!=0{_aba [_gag ]|=0xf000000;};if _gag &0x80!=0{_aba [_gag ]|=0xf0000000;};};return _aba ;};type Color int ;func (_gbae *Bitmap )GetVanillaData ()[]byte {if _gbae .Color ==Chocolate {_gbae .inverseData ();
};return _gbae .Data ;};func (_ffe *Bitmap )createTemplate ()*Bitmap {return &Bitmap {Width :_ffe .Width ,Height :_ffe .Height ,RowStride :_ffe .RowStride ,Color :_ffe .Color ,Text :_ffe .Text ,BitmapNumber :_ffe .BitmapNumber ,Special :_ffe .Special ,Data :make ([]byte ,len (_ffe .Data ))};
};func _febcb (_ddf ,_bgea *Bitmap ,_dada ,_aacd int )(*Bitmap ,error ){const _ddfg ="d\u0069\u006c\u0061\u0074\u0065\u0042\u0072\u0069\u0063\u006b";if _bgea ==nil {_bb .Log .Debug ("\u0064\u0069\u006c\u0061\u0074\u0065\u0042\u0072\u0069\u0063k\u0020\u0073\u006f\u0075\u0072\u0063\u0065 \u006e\u006f\u0074\u0020\u0064\u0065\u0066\u0069\u006e\u0065\u0064");
return nil ,_ga .Error (_ddfg ,"\u0073o\u0075\u0072\u0063\u0065 \u0062\u0069\u0074\u006d\u0061p\u0020n\u006ft\u0020\u0064\u0065\u0066\u0069\u006e\u0065d");};if _dada < 1||_aacd < 1{return nil ,_ga .Error (_ddfg ,"\u0068\u0053\u007a\u0069\u0065 \u0061\u006e\u0064\u0020\u0076\u0053\u0069\u007a\u0065\u0020\u0061\u0072\u0065 \u006e\u006f\u0020\u0067\u0072\u0065\u0061\u0074\u0065\u0072\u0020\u0065\u0071\u0075\u0061\u006c\u0020\u0074\u006f\u0020\u0031");
};if _dada ==1&&_aacd ==1{_afag ,_dbdf :=_acda (_ddf ,_bgea );if _dbdf !=nil {return nil ,_ga .Wrap (_dbdf ,_ddfg ,"\u0068S\u0069\u007a\u0065\u0020\u003d\u003d\u0020\u0031\u0020\u0026\u0026 \u0076\u0053\u0069\u007a\u0065\u0020\u003d\u003d\u0020\u0031");
};return _afag ,nil ;};if _dada ==1||_aacd ==1{_dbfef :=SelCreateBrick (_aacd ,_dada ,_aacd /2,_dada /2,SelHit );_ffbe ,_efgga :=_bgbfc (_ddf ,_bgea ,_dbfef );if _efgga !=nil {return nil ,_ga .Wrap (_efgga ,_ddfg ,"\u0068s\u0069\u007a\u0065\u0020\u003d\u003d\u0020\u0031\u0020\u007c\u007c \u0076\u0053\u0069\u007a\u0065\u0020\u003d\u003d\u0020\u0031");
};return _ffbe ,nil ;};_edea :=SelCreateBrick (1,_dada ,0,_dada /2,SelHit );_aebb :=SelCreateBrick (_aacd ,1,_aacd /2,0,SelHit );_cfcg ,_eaff :=_bgbfc (nil ,_bgea ,_edea );if _eaff !=nil {return nil ,_ga .Wrap (_eaff ,_ddfg ,"\u0031\u0073\u0074\u0020\u0064\u0069\u006c\u0061\u0074\u0065");
};_ddf ,_eaff =_bgbfc (_ddf ,_cfcg ,_aebb );if _eaff !=nil {return nil ,_ga .Wrap (_eaff ,_ddfg ,"\u0032\u006e\u0064\u0020\u0064\u0069\u006c\u0061\u0074\u0065");};return _ddf ,nil ;};func (_aaca *ClassedPoints )GroupByY ()([]*ClassedPoints ,error ){const _dadc ="\u0043\u006c\u0061\u0073se\u0064\u0050\u006f\u0069\u006e\u0074\u0073\u002e\u0047\u0072\u006f\u0075\u0070\u0042y\u0059";
if _gcdc :=_aaca .validateIntSlice ();_gcdc !=nil {return nil ,_ga .Wrap (_gcdc ,_dadc ,"");};if _aaca .IntSlice .Size ()==0{return nil ,_ga .Error (_dadc ,"\u004e\u006f\u0020\u0063la\u0073\u0073\u0065\u0073\u0020\u0070\u0072\u006f\u0076\u0069\u0064\u0065\u0064");
};_aaca .SortByY ();var (_aaad []*ClassedPoints ;_dbac int ;);_abab :=-1;var _fedf *ClassedPoints ;for _cefg :=0;_cefg < len (_aaca .IntSlice );_cefg ++{_dbac =int (_aaca .YAtIndex (_cefg ));if _dbac !=_abab {_fedf =&ClassedPoints {Points :_aaca .Points };
_abab =_dbac ;_aaad =append (_aaad ,_fedf );};_fedf .IntSlice =append (_fedf .IntSlice ,_aaca .IntSlice [_cefg ]);};for _ ,_bace :=range _aaad {_bace .SortByX ();};return _aaad ,nil ;};func (_aebg *Bitmap )RasterOperation (dx ,dy ,dw ,dh int ,op RasterOperator ,src *Bitmap ,sx ,sy int )error {return _bebe (_aebg ,dx ,dy ,dw ,dh ,op ,src ,sx ,sy );
};func _fbgc (_fdgf ,_bc int )*Bitmap {return &Bitmap {Width :_fdgf ,Height :_bc ,RowStride :(_fdgf +7)>>3};};func (_dcca *Points )AddPoint (x ,y float32 ){*_dcca =append (*_dcca ,Point {x ,y })};func (_agfc *Boxes )Get (i int )(*_c .Rectangle ,error ){const _defd ="\u0042o\u0078\u0065\u0073\u002e\u0047\u0065t";
if _agfc ==nil {return nil ,_ga .Error (_defd ,"\u0027\u0042\u006f\u0078es\u0027\u0020\u006e\u006f\u0074\u0020\u0064\u0065\u0066\u0069\u006e\u0065\u0064");};if i > len (*_agfc )-1{return nil ,_ga .Errorf (_defd ,"\u0069n\u0064\u0065\u0078\u003a\u0020\u0027\u0025\u0064\u0027\u0020\u006fu\u0074\u0020\u006f\u0066\u0020\u0072\u0061\u006e\u0067\u0065",i );
};return (*_agfc )[i ],nil ;};func _ebd (_abf ,_fb *Bitmap )(_dcd error ){const _eba ="\u0065\u0078\u0070\u0061nd\u0042\u0069\u006e\u0061\u0072\u0079\u0046\u0061\u0063\u0074\u006f\u0072\u0038";_aae :=_fb .RowStride ;_dbg :=_abf .RowStride ;var _cf ,_acf ,_dfe ,_ddg ,_cb int ;
for _dfe =0;_dfe < _fb .Height ;_dfe ++{_cf =_dfe *_aae ;_acf =8*_dfe *_dbg ;for _ddg =0;_ddg < _aae ;_ddg ++{if _dcd =_abf .setEightBytes (_acf +_ddg *8,_dce [_fb .Data [_cf +_ddg ]]);_dcd !=nil {return _ga .Wrap (_dcd ,_eba ,"");};};for _cb =1;_cb < 8;
_cb ++{for _ddg =0;_ddg < _dbg ;_ddg ++{if _dcd =_abf .SetByte (_acf +_cb *_dbg +_ddg ,_abf .Data [_acf +_ddg ]);_dcd !=nil {return _ga .Wrap (_dcd ,_eba ,"");};};};};return nil ;};func TstFrameBitmapData ()[]byte {return _affa .Data };func _fdgc (_ddbe ,_gffg *Bitmap ,_ffg ,_caed ,_efb ,_gfda ,_cfdg int ,_beab CombinationOperator )error {var _gcf int ;
_agdbd :=func (){_gcf ++;_efb +=_gffg .RowStride ;_gfda +=_ddbe .RowStride ;_cfdg +=_ddbe .RowStride };for _gcf =_ffg ;_gcf < _caed ;_agdbd (){_afba :=_efb ;for _dbdab :=_gfda ;_dbdab <=_cfdg ;_dbdab ++{_fagb ,_fbaf :=_gffg .GetByte (_afba );if _fbaf !=nil {return _fbaf ;
};_ebcg ,_fbaf :=_ddbe .GetByte (_dbdab );if _fbaf !=nil {return _fbaf ;};if _fbaf =_gffg .SetByte (_afba ,_ggde (_fagb ,_ebcg ,_beab ));_fbaf !=nil {return _fbaf ;};_afba ++;};};return nil ;};func (_fcddc *Bitmaps )selectByIndicator (_adced *_eb .NumSlice )(_cgcae *Bitmaps ,_bgee error ){const _cacb ="\u0042i\u0074\u006d\u0061\u0070s\u002e\u0073\u0065\u006c\u0065c\u0074B\u0079I\u006e\u0064\u0069\u0063\u0061\u0074\u006fr";
if _fcddc ==nil {return nil ,_ga .Error (_cacb ,"\u0027\u0062\u0027 b\u0069\u0074\u006d\u0061\u0070\u0073\u0020\u006e\u006f\u0074\u0020\u0064\u0065\u0066\u0069\u006e\u0065\u0064");};if _adced ==nil {return nil ,_ga .Error (_cacb ,"'\u006e\u0061\u0027\u0020\u0069\u006ed\u0069\u0063\u0061\u0074\u006f\u0072\u0073\u0020\u006eo\u0074\u0020\u0064e\u0066i\u006e\u0065\u0064");
};if len (_fcddc .Values )==0{return _fcddc ,nil ;};if len (*_adced )!=len (_fcddc .Values ){return nil ,_ga .Errorf (_cacb ,"\u006ea\u0020\u006ce\u006e\u0067\u0074\u0068:\u0020\u0025\u0064,\u0020\u0069\u0073\u0020\u0064\u0069\u0066\u0066\u0065re\u006e\u0074\u0020t\u0068\u0061n\u0020\u0062\u0069\u0074\u006d\u0061p\u0073\u003a \u0025\u0064",len (*_adced ),len (_fcddc .Values ));
};var _cdega ,_gcecg ,_fffge int ;for _gcecg =0;_gcecg < len (*_adced );_gcecg ++{if _cdega ,_bgee =_adced .GetInt (_gcecg );_bgee !=nil {return nil ,_ga .Wrap (_bgee ,_cacb ,"f\u0069\u0072\u0073\u0074\u0020\u0063\u0068\u0065\u0063\u006b");};if _cdega ==1{_fffge ++;
};};if _fffge ==len (_fcddc .Values ){return _fcddc ,nil ;};_cgcae =&Bitmaps {};_egced :=len (_fcddc .Values )==len (_fcddc .Boxes );for _gcecg =0;_gcecg < len (*_adced );_gcecg ++{if _cdega =int ((*_adced )[_gcecg ]);_cdega ==0{continue ;};_cgcae .Values =append (_cgcae .Values ,_fcddc .Values [_gcecg ]);
if _egced {_cgcae .Boxes =append (_cgcae .Boxes ,_fcddc .Boxes [_gcecg ]);};};return _cgcae ,nil ;};func _cfgcg (_dcdg ,_egag *Bitmap ,_gfab ,_ccgc int )(*Bitmap ,error ){const _cafc ="\u0065\u0072\u006f\u0064\u0065\u0042\u0072\u0069\u0063\u006b";if _egag ==nil {return nil ,_ga .Error (_cafc ,"\u0073o\u0075r\u0063\u0065\u0020\u006e\u006ft\u0020\u0064e\u0066\u0069\u006e\u0065\u0064");
};if _gfab < 1||_ccgc < 1{return nil ,_ga .Error (_cafc ,"\u0068\u0073\u0069\u007a\u0065\u0020\u0061\u006e\u0064\u0020\u0076\u0073\u0069\u007a\u0065\u0020\u0061\u0072e\u0020\u006e\u006f\u0074\u0020\u0067\u0072e\u0061\u0074\u0065\u0072\u0020\u0074\u0068\u0061\u006e\u0020\u006fr\u0020\u0065\u0071\u0075\u0061\u006c\u0020\u0074\u006f\u0020\u0031");
};if _gfab ==1&&_ccgc ==1{_ffeff ,_gaea :=_acda (_dcdg ,_egag );if _gaea !=nil {return nil ,_ga .Wrap (_gaea ,_cafc ,"\u0068S\u0069\u007a\u0065\u0020\u003d\u003d\u0020\u0031\u0020\u0026\u0026 \u0076\u0053\u0069\u007a\u0065\u0020\u003d\u003d\u0020\u0031");
};return _ffeff ,nil ;};if _gfab ==1||_ccgc ==1{_bdeg :=SelCreateBrick (_ccgc ,_gfab ,_ccgc /2,_gfab /2,SelHit );_gfdbg ,_defec :=_bbff (_dcdg ,_egag ,_bdeg );if _defec !=nil {return nil ,_ga .Wrap (_defec ,_cafc ,"\u0068S\u0069\u007a\u0065\u0020\u003d\u003d\u0020\u0031\u0020\u007c\u007c \u0076\u0053\u0069\u007a\u0065\u0020\u003d\u003d\u0020\u0031");
};return _gfdbg ,nil ;};_gcff :=SelCreateBrick (1,_gfab ,0,_gfab /2,SelHit );_dcaga :=SelCreateBrick (_ccgc ,1,_ccgc /2,0,SelHit );_dceg ,_abad :=_bbff (nil ,_egag ,_gcff );if _abad !=nil {return nil ,_ga .Wrap (_abad ,_cafc ,"\u0031s\u0074\u0020\u0065\u0072\u006f\u0064e");
};_dcdg ,_abad =_bbff (_dcdg ,_dceg ,_dcaga );if _abad !=nil {return nil ,_ga .Wrap (_abad ,_cafc ,"\u0032n\u0064\u0020\u0065\u0072\u006f\u0064e");};return _dcdg ,nil ;};func (_bgf *Bitmap )Copy ()*Bitmap {_bd :=make ([]byte ,len (_bgf .Data ));copy (_bd ,_bgf .Data );
return &Bitmap {Width :_bgf .Width ,Height :_bgf .Height ,RowStride :_bgf .RowStride ,Data :_bd ,Color :_bgf .Color ,Text :_bgf .Text ,BitmapNumber :_bgf .BitmapNumber ,Special :_bgf .Special };};var (_affa *Bitmap ;_abdg *Bitmap ;);func (_gfbg Points )Size ()int {return len (_gfbg )};
func (_fcec *Bitmap )GetChocolateData ()[]byte {if _fcec .Color ==Vanilla {_fcec .inverseData ();};return _fcec .Data ;};var _cbd =[5]int {1,2,3,0,4};func (_gedb *Bitmap )setTwoBytes (_bef int ,_bfcc uint16 )error {if _bef +1> len (_gedb .Data )-1{return _ga .Errorf ("s\u0065\u0074\u0054\u0077\u006f\u0042\u0079\u0074\u0065\u0073","\u0069n\u0064\u0065\u0078\u003a\u0020\u0027\u0025\u0064\u0027\u0020\u006fu\u0074\u0020\u006f\u0066\u0020\u0072\u0061\u006e\u0067\u0065",_bef );
};_gedb .Data [_bef ]=byte ((_bfcc &0xff00)>>8);_gedb .Data [_bef +1]=byte (_bfcc &0xff);return nil ;};func (_daac *Bitmaps )HeightSorter ()func (_fdfdc ,_edff int )bool {return func (_fadab ,_dcad int )bool {_bged :=_daac .Values [_fadab ].Height < _daac .Values [_dcad ].Height ;
_bb .Log .Debug ("H\u0065i\u0067\u0068\u0074\u003a\u0020\u0025\u0076\u0020<\u0020\u0025\u0076\u0020= \u0025\u0076",_daac .Values [_fadab ].Height ,_daac .Values [_dcad ].Height ,_bged );return _bged ;};};func TstAddSymbol (t *_f .T ,bms *Bitmaps ,sym *Bitmap ,x *int ,y int ,space int ){bms .AddBitmap (sym );
_cbabg :=_c .Rect (*x ,y ,*x +sym .Width ,y +sym .Height );bms .AddBox (&_cbabg );*x +=sym .Width +space ;};type Points []Point ;func (_ccdf *byWidth )Swap (i ,j int ){_ccdf .Values [i ],_ccdf .Values [j ]=_ccdf .Values [j ],_ccdf .Values [i ];if _ccdf .Boxes !=nil {_ccdf .Boxes [i ],_ccdf .Boxes [j ]=_ccdf .Boxes [j ],_ccdf .Boxes [i ];
};};func Centroid (bm *Bitmap ,centTab ,sumTab []int )(Point ,error ){return bm .centroid (centTab ,sumTab )};func (_cccg Points )YSorter ()func (_aacee ,_edg int )bool {return func (_cbbf ,_bdea int )bool {return _cccg [_cbbf ].Y < _cccg [_bdea ].Y };
};func (_fede CombinationOperator )String ()string {var _fcce string ;switch _fede {case CmbOpOr :_fcce ="\u004f\u0052";case CmbOpAnd :_fcce ="\u0041\u004e\u0044";case CmbOpXor :_fcce ="\u0058\u004f\u0052";case CmbOpXNor :_fcce ="\u0058\u004e\u004f\u0052";
case CmbOpReplace :_fcce ="\u0052E\u0050\u004c\u0041\u0043\u0045";case CmbOpNot :_fcce ="\u004e\u004f\u0054";};return _fcce ;};func Copy (d ,s *Bitmap )(*Bitmap ,error ){return _acda (d ,s )};func _bgbfc (_eaec *Bitmap ,_aage *Bitmap ,_ecfb *Selection )(*Bitmap ,error ){var (_degae *Bitmap ;
_effd error ;);_eaec ,_effd =_aafd (_eaec ,_aage ,_ecfb ,&_degae );if _effd !=nil {return nil ,_effd ;};if _effd =_eaec .clearAll ();_effd !=nil {return nil ,_effd ;};var _ddaed SelectionValue ;for _fffg :=0;_fffg < _ecfb .Height ;_fffg ++{for _gddg :=0;
_gddg < _ecfb .Width ;_gddg ++{_ddaed =_ecfb .Data [_fffg ][_gddg ];if _ddaed ==SelHit {if _effd =_eaec .RasterOperation (_gddg -_ecfb .Cx ,_fffg -_ecfb .Cy ,_aage .Width ,_aage .Height ,PixSrcOrDst ,_degae ,0,0);_effd !=nil {return nil ,_effd ;};};};};
return _eaec ,nil ;};type byHeight Bitmaps ;func CorrelationScoreThresholded (bm1 ,bm2 *Bitmap ,area1 ,area2 int ,delX ,delY float32 ,maxDiffW ,maxDiffH int ,tab ,downcount []int ,scoreThreshold float32 )(bool ,error ){const _bdaa ="C\u006f\u0072\u0072\u0065\u006c\u0061t\u0069\u006f\u006e\u0053\u0063\u006f\u0072\u0065\u0054h\u0072\u0065\u0073h\u006fl\u0064\u0065\u0064";
if bm1 ==nil {return false ,_ga .Error (_bdaa ,"\u0063\u006f\u0072\u0072\u0065\u006c\u0061\u0074\u0069\u006f\u006e\u0053\u0063\u006f\u0072\u0065\u0054\u0068\u0072\u0065\u0073\u0068\u006f\u006cd\u0065\u0064\u0020\u0062\u006d1\u0020\u0069s\u0020\u006e\u0069\u006c");
};if bm2 ==nil {return false ,_ga .Error (_bdaa ,"\u0063\u006f\u0072\u0072\u0065\u006c\u0061\u0074\u0069\u006f\u006e\u0053\u0063\u006f\u0072\u0065\u0054\u0068\u0072\u0065\u0073\u0068\u006f\u006cd\u0065\u0064\u0020\u0062\u006d2\u0020\u0069s\u0020\u006e\u0069\u006c");
};if area1 <=0||area2 <=0{return false ,_ga .Error (_bdaa ,"c\u006f\u0072\u0072\u0065\u006c\u0061\u0074\u0069\u006fn\u0053\u0063\u006f\u0072\u0065\u0054\u0068re\u0073\u0068\u006f\u006cd\u0065\u0064\u0020\u002d\u0020\u0061\u0072\u0065\u0061s \u006d\u0075s\u0074\u0020\u0062\u0065\u0020\u003e\u0020\u0030");
};if downcount ==nil {return false ,_ga .Error (_bdaa ,"\u0070\u0072\u006fvi\u0064\u0065\u0064\u0020\u006e\u006f\u0020\u0027\u0064\u006f\u0077\u006e\u0063\u006f\u0075\u006e\u0074\u0027");};if tab ==nil {return false ,_ga .Error (_bdaa ,"p\u0072\u006f\u0076\u0069de\u0064 \u006e\u0069\u006c\u0020\u0027s\u0075\u006d\u0074\u0061\u0062\u0027");
};_defda ,_bbcf :=bm1 .Width ,bm1 .Height ;_ecgcf ,_gcfe :=bm2 .Width ,bm2 .Height ;if _eb .Abs (_defda -_ecgcf )> maxDiffW {return false ,nil ;};if _eb .Abs (_bbcf -_gcfe )> maxDiffH {return false ,nil ;};_geca :=int (delX +_eb .Sign (delX )*0.5);_bcde :=int (delY +_eb .Sign (delY )*0.5);
_aeeg :=int (_b .Ceil (_b .Sqrt (float64 (scoreThreshold )*float64 (area1 )*float64 (area2 ))));_ggge :=bm2 .RowStride ;_faac :=_fde (_bcde ,0);_ffge :=_dfce (_gcfe +_bcde ,_bbcf );_feef :=bm1 .RowStride *_faac ;_bgaa :=bm2 .RowStride *(_faac -_bcde );
var _ffab int ;if _ffge <=_bbcf {_ffab =downcount [_ffge -1];};_dgeca :=_fde (_geca ,0);_aegb :=_dfce (_ecgcf +_geca ,_defda );var _gfcgbb ,_ceec int ;if _geca >=8{_gfcgbb =_geca >>3;_feef +=_gfcgbb ;_dgeca -=_gfcgbb <<3;_aegb -=_gfcgbb <<3;_geca &=7;}else if _geca <=-8{_ceec =-((_geca +7)>>3);
_bgaa +=_ceec ;_ggge -=_ceec ;_geca +=_ceec <<3;};var (_fecc ,_ffbfd ,_egcc int ;_fgab ,_gded ,_cafb byte ;);if _dgeca >=_aegb ||_faac >=_ffge {return false ,nil ;};_fbca :=(_aegb +7)>>3;switch {case _geca ==0:for _ffbfd =_faac ;_ffbfd < _ffge ;_ffbfd ,_feef ,_bgaa =_ffbfd +1,_feef +bm1 .RowStride ,_bgaa +bm2 .RowStride {for _egcc =0;
_egcc < _fbca ;_egcc ++{_fgab =bm1 .Data [_feef +_egcc ]&bm2 .Data [_bgaa +_egcc ];_fecc +=tab [_fgab ];};if _fecc >=_aeeg {return true ,nil ;};if _cffcd :=_fecc +downcount [_ffbfd ]-_ffab ;_cffcd < _aeeg {return false ,nil ;};};case _geca > 0&&_ggge < _fbca :for _ffbfd =_faac ;
_ffbfd < _ffge ;_ffbfd ,_feef ,_bgaa =_ffbfd +1,_feef +bm1 .RowStride ,_bgaa +bm2 .RowStride {_gded =bm1 .Data [_feef ];_cafb =bm2 .Data [_bgaa ]>>uint (_geca );_fgab =_gded &_cafb ;_fecc +=tab [_fgab ];for _egcc =1;_egcc < _ggge ;_egcc ++{_gded =bm1 .Data [_feef +_egcc ];
_cafb =bm2 .Data [_bgaa +_egcc ]>>uint (_geca )|bm2 .Data [_bgaa +_egcc -1]<<uint (8-_geca );_fgab =_gded &_cafb ;_fecc +=tab [_fgab ];};_gded =bm1 .Data [_feef +_egcc ];_cafb =bm2 .Data [_bgaa +_egcc -1]<<uint (8-_geca );_fgab =_gded &_cafb ;_fecc +=tab [_fgab ];
if _fecc >=_aeeg {return true ,nil ;}else if _fecc +downcount [_ffbfd ]-_ffab < _aeeg {return false ,nil ;};};case _geca > 0&&_ggge >=_fbca :for _ffbfd =_faac ;_ffbfd < _ffge ;_ffbfd ,_feef ,_bgaa =_ffbfd +1,_feef +bm1 .RowStride ,_bgaa +bm2 .RowStride {_gded =bm1 .Data [_feef ];
_cafb =bm2 .Data [_bgaa ]>>uint (_geca );_fgab =_gded &_cafb ;_fecc +=tab [_fgab ];for _egcc =1;_egcc < _fbca ;_egcc ++{_gded =bm1 .Data [_feef +_egcc ];_cafb =bm2 .Data [_bgaa +_egcc ]>>uint (_geca );_cafb |=bm2 .Data [_bgaa +_egcc -1]<<uint (8-_geca );
_fgab =_gded &_cafb ;_fecc +=tab [_fgab ];};if _fecc >=_aeeg {return true ,nil ;}else if _fecc +downcount [_ffbfd ]-_ffab < _aeeg {return false ,nil ;};};case _fbca < _ggge :for _ffbfd =_faac ;_ffbfd < _ffge ;_ffbfd ,_feef ,_bgaa =_ffbfd +1,_feef +bm1 .RowStride ,_bgaa +bm2 .RowStride {for _egcc =0;
_egcc < _fbca ;_egcc ++{_gded =bm1 .Data [_feef +_egcc ];_cafb =bm2 .Data [_bgaa +_egcc ]<<uint (-_geca );_cafb |=bm2 .Data [_bgaa +_egcc +1]>>uint (8+_geca );_fgab =_gded &_cafb ;_fecc +=tab [_fgab ];};if _fecc >=_aeeg {return true ,nil ;}else if _afeb :=_fecc +downcount [_ffbfd ]-_ffab ;
_afeb < _aeeg {return false ,nil ;};};case _ggge >=_fbca :for _ffbfd =_faac ;_ffbfd < _ffge ;_ffbfd ,_feef ,_bgaa =_ffbfd +1,_feef +bm1 .RowStride ,_bgaa +bm2 .RowStride {for _egcc =0;_egcc < _fbca ;_egcc ++{_gded =bm1 .Data [_feef +_egcc ];_cafb =bm2 .Data [_bgaa +_egcc ]<<uint (-_geca );
_cafb |=bm2 .Data [_bgaa +_egcc +1]>>uint (8+_geca );_fgab =_gded &_cafb ;_fecc +=tab [_fgab ];};_gded =bm1 .Data [_feef +_egcc ];_cafb =bm2 .Data [_bgaa +_egcc ]<<uint (-_geca );_fgab =_gded &_cafb ;_fecc +=tab [_fgab ];if _fecc >=_aeeg {return true ,nil ;
}else if _fecc +downcount [_ffbfd ]-_ffab < _aeeg {return false ,nil ;};};};_ffee :=float32 (_fecc )*float32 (_fecc )/(float32 (area1 )*float32 (area2 ));if _ffee >=scoreThreshold {_bb .Log .Trace ("\u0063\u006f\u0075\u006e\u0074\u003a\u0020\u0025\u0064\u0020\u003c\u0020\u0074\u0068\u0072\u0065\u0073\u0068\u006f\u006cd\u0020\u0025\u0064\u0020\u0062\u0075\u0074\u0020\u0073c\u006f\u0072\u0065\u0020\u0025\u0066\u0020\u003e\u003d\u0020\u0073\u0063\u006fr\u0065\u0054\u0068\u0072\u0065\u0073h\u006f\u006c\u0064 \u0025\u0066",_fecc ,_aeeg ,_ffee ,scoreThreshold );
};return false ,nil ;};func _cbge (_ecfcc ,_adcb *Bitmap ,_fccb ,_gagf int )(*Bitmap ,error ){const _aadb ="\u006fp\u0065\u006e\u0042\u0072\u0069\u0063k";if _adcb ==nil {return nil ,_ga .Error (_aadb ,"\u0073o\u0075\u0072\u0063\u0065 \u0062\u0069\u0074\u006d\u0061p\u0020n\u006ft\u0020\u0064\u0065\u0066\u0069\u006e\u0065d");
};if _fccb < 1&&_gagf < 1{return nil ,_ga .Error (_aadb ,"\u0068\u0053\u0069\u007ae \u003c\u0020\u0031\u0020\u0026\u0026\u0020\u0076\u0053\u0069\u007a\u0065\u0020\u003c \u0031");};if _fccb ==1&&_gagf ==1{return _adcb .Copy (),nil ;};if _fccb ==1||_gagf ==1{var _dbbc error ;
_acab :=SelCreateBrick (_gagf ,_fccb ,_gagf /2,_fccb /2,SelHit );_ecfcc ,_dbbc =_ecc (_ecfcc ,_adcb ,_acab );if _dbbc !=nil {return nil ,_ga .Wrap (_dbbc ,_aadb ,"\u0068S\u0069\u007a\u0065\u0020\u003d\u003d\u0020\u0031\u0020\u007c\u007c \u0076\u0053\u0069\u007a\u0065\u0020\u003d\u003d\u0020\u0031");
};return _ecfcc ,nil ;};_edbge :=SelCreateBrick (1,_fccb ,0,_fccb /2,SelHit );_ddce :=SelCreateBrick (_gagf ,1,_gagf /2,0,SelHit );_fgccd ,_ceace :=_bbff (nil ,_adcb ,_edbge );if _ceace !=nil {return nil ,_ga .Wrap (_ceace ,_aadb ,"\u0031s\u0074\u0020\u0065\u0072\u006f\u0064e");
};_ecfcc ,_ceace =_bbff (_ecfcc ,_fgccd ,_ddce );if _ceace !=nil {return nil ,_ga .Wrap (_ceace ,_aadb ,"\u0032n\u0064\u0020\u0065\u0072\u006f\u0064e");};_ ,_ceace =_bgbfc (_fgccd ,_ecfcc ,_edbge );if _ceace !=nil {return nil ,_ga .Wrap (_ceace ,_aadb ,"\u0031\u0073\u0074\u0020\u0064\u0069\u006c\u0061\u0074\u0065");
};_ ,_ceace =_bgbfc (_ecfcc ,_fgccd ,_ddce );if _ceace !=nil {return nil ,_ga .Wrap (_ceace ,_aadb ,"\u0032\u006e\u0064\u0020\u0064\u0069\u006c\u0061\u0074\u0065");};return _ecfcc ,nil ;};func _cfg (_ade *Bitmap ,_dgg int ,_gfd []byte )(_gac *Bitmap ,_eefe error ){const _dbfg ="\u0072\u0065\u0064\u0075\u0063\u0065\u0052\u0061\u006e\u006b\u0042\u0069n\u0061\u0072\u0079\u0032";
if _ade ==nil {return nil ,_ga .Error (_dbfg ,"\u0073o\u0075\u0072\u0063\u0065 \u0062\u0069\u0074\u006d\u0061p\u0020n\u006ft\u0020\u0064\u0065\u0066\u0069\u006e\u0065d");};if _dgg < 1||_dgg > 4{return nil ,_ga .Error (_dbfg ,"\u006c\u0065\u0076\u0065\u006c\u0020\u006d\u0075\u0073\u0074 \u0062\u0065\u0020\u0069\u006e\u0020\u0073e\u0074\u0020\u007b\u0031\u002c\u0032\u002c\u0033\u002c\u0034\u007d");
};if _ade .Height <=1{return nil ,_ga .Errorf (_dbfg ,"\u0073o\u0075\u0072c\u0065\u0020\u0068e\u0069\u0067\u0068\u0074\u0020\u006d\u0075s\u0074\u0020\u0062\u0065\u0020\u0061t\u0020\u006c\u0065\u0061\u0073\u0074\u0020\u0027\u0032\u0027\u0020-\u0020\u0069\u0073\u003a\u0020\u0027\u0025\u0064\u0027",_ade .Height );
};_gac =New (_ade .Width /2,_ade .Height /2);if _gfd ==nil {_gfd =_fba ();};_dbff :=_dfce (_ade .RowStride ,2*_gac .RowStride );switch _dgg {case 1:_eefe =_afd (_ade ,_gac ,_dgg ,_gfd ,_dbff );case 2:_eefe =_dag (_ade ,_gac ,_dgg ,_gfd ,_dbff );case 3:_eefe =_fcga (_ade ,_gac ,_dgg ,_gfd ,_dbff );
case 4:_eefe =_be (_ade ,_gac ,_dgg ,_gfd ,_dbff );};if _eefe !=nil {return nil ,_eefe ;};return _gac ,nil ;};func TstCSymbol (t *_f .T )*Bitmap {t .Helper ();_beff :=New (6,6);_gb .NoError (t ,_beff .SetPixel (1,0,1));_gb .NoError (t ,_beff .SetPixel (2,0,1));
_gb .NoError (t ,_beff .SetPixel (3,0,1));_gb .NoError (t ,_beff .SetPixel (4,0,1));_gb .NoError (t ,_beff .SetPixel (0,1,1));_gb .NoError (t ,_beff .SetPixel (5,1,1));_gb .NoError (t ,_beff .SetPixel (0,2,1));_gb .NoError (t ,_beff .SetPixel (0,3,1));
_gb .NoError (t ,_beff .SetPixel (0,4,1));_gb .NoError (t ,_beff .SetPixel (5,4,1));_gb .NoError (t ,_beff .SetPixel (1,5,1));_gb .NoError (t ,_beff .SetPixel (2,5,1));_gb .NoError (t ,_beff .SetPixel (3,5,1));_gb .NoError (t ,_beff .SetPixel (4,5,1));
return _beff ;};func (_efee MorphProcess )getWidthHeight ()(_eebe ,_aeb int ){return _efee .Arguments [0],_efee .Arguments [1];};func (_afc *Bitmap )RemoveBorder (borderSize int )(*Bitmap ,error ){if borderSize ==0{return _afc .Copy (),nil ;};_ded ,_bdb :=_afc .removeBorderGeneral (borderSize ,borderSize ,borderSize ,borderSize );
if _bdb !=nil {return nil ,_ga .Wrap (_bdb ,"\u0052\u0065\u006do\u0076\u0065\u0042\u006f\u0072\u0064\u0065\u0072","");};return _ded ,nil ;};func (_fdgff *ClassedPoints )YAtIndex (i int )float32 {return (*_fdgff .Points )[_fdgff .IntSlice [i ]].Y };func (_fedb *Bitmaps )makeSizeIndicator (_cgcd ,_fedbe int ,_ddfb LocationFilter ,_ffdae SizeComparison )(_ebbaf *_eb .NumSlice ,_babg error ){const _cdad ="\u0042i\u0074\u006d\u0061\u0070s\u002e\u006d\u0061\u006b\u0065S\u0069z\u0065I\u006e\u0064\u0069\u0063\u0061\u0074\u006fr";
if _fedb ==nil {return nil ,_ga .Error (_cdad ,"\u0062\u0069\u0074ma\u0070\u0073\u0020\u0027\u0062\u0027\u0020\u006e\u006f\u0074\u0020\u0064\u0065\u0066\u0069\u006e\u0065\u0064");};switch _ddfb {case LocSelectWidth ,LocSelectHeight ,LocSelectIfEither ,LocSelectIfBoth :default:return nil ,_ga .Errorf (_cdad ,"\u0070\u0072\u006f\u0076\u0069d\u0065\u0064\u0020\u0069\u006e\u0076\u0061\u006c\u0069\u0064\u0020\u006c\u006fc\u0061\u0074\u0069\u006f\u006e\u0020\u0066\u0069\u006c\u0074\u0065\u0072\u0020\u0074\u0079\u0070\u0065\u003a\u0020\u0025\u0064",_ddfb );
};switch _ffdae {case SizeSelectIfLT ,SizeSelectIfGT ,SizeSelectIfLTE ,SizeSelectIfGTE ,SizeSelectIfEQ :default:return nil ,_ga .Errorf (_cdad ,"\u0069\u006e\u0076\u0061li\u0064\u0020\u0072\u0065\u006c\u0061\u0074\u0069\u006f\u006e\u003a\u0020\u0027\u0025d\u0027",_ffdae );
};_ebbaf =&_eb .NumSlice {};var (_gegde ,_fbgcec ,_efbce int ;_aadc *Bitmap ;);for _ ,_aadc =range _fedb .Values {_gegde =0;_fbgcec ,_efbce =_aadc .Width ,_aadc .Height ;switch _ddfb {case LocSelectWidth :if (_ffdae ==SizeSelectIfLT &&_fbgcec < _cgcd )||(_ffdae ==SizeSelectIfGT &&_fbgcec > _cgcd )||(_ffdae ==SizeSelectIfLTE &&_fbgcec <=_cgcd )||(_ffdae ==SizeSelectIfGTE &&_fbgcec >=_cgcd )||(_ffdae ==SizeSelectIfEQ &&_fbgcec ==_cgcd ){_gegde =1;
};case LocSelectHeight :if (_ffdae ==SizeSelectIfLT &&_efbce < _fedbe )||(_ffdae ==SizeSelectIfGT &&_efbce > _fedbe )||(_ffdae ==SizeSelectIfLTE &&_efbce <=_fedbe )||(_ffdae ==SizeSelectIfGTE &&_efbce >=_fedbe )||(_ffdae ==SizeSelectIfEQ &&_efbce ==_fedbe ){_gegde =1;
};case LocSelectIfEither :if (_ffdae ==SizeSelectIfLT &&(_fbgcec < _cgcd ||_efbce < _fedbe ))||(_ffdae ==SizeSelectIfGT &&(_fbgcec > _cgcd ||_efbce > _fedbe ))||(_ffdae ==SizeSelectIfLTE &&(_fbgcec <=_cgcd ||_efbce <=_fedbe ))||(_ffdae ==SizeSelectIfGTE &&(_fbgcec >=_cgcd ||_efbce >=_fedbe ))||(_ffdae ==SizeSelectIfEQ &&(_fbgcec ==_cgcd ||_efbce ==_fedbe )){_gegde =1;
};case LocSelectIfBoth :if (_ffdae ==SizeSelectIfLT &&(_fbgcec < _cgcd &&_efbce < _fedbe ))||(_ffdae ==SizeSelectIfGT &&(_fbgcec > _cgcd &&_efbce > _fedbe ))||(_ffdae ==SizeSelectIfLTE &&(_fbgcec <=_cgcd &&_efbce <=_fedbe ))||(_ffdae ==SizeSelectIfGTE &&(_fbgcec >=_cgcd &&_efbce >=_fedbe ))||(_ffdae ==SizeSelectIfEQ &&(_fbgcec ==_cgcd &&_efbce ==_fedbe )){_gegde =1;
};};_ebbaf .AddInt (_gegde );};return _ebbaf ,nil ;};func (_ggac *Bitmap )And (s *Bitmap )(_fgbd *Bitmap ,_dfca error ){const _cdf ="\u0042\u0069\u0074\u006d\u0061\u0070\u002e\u0041\u006e\u0064";if _ggac ==nil {return nil ,_ga .Error (_cdf ,"\u0027b\u0069t\u006d\u0061\u0070\u0020\u0027b\u0027\u0020i\u0073\u0020\u006e\u0069\u006c");
};if s ==nil {return nil ,_ga .Error (_cdf ,"\u0062\u0069\u0074\u006d\u0061\u0070\u0020\u0027\u0073\u0027\u0020\u0069s\u0020\u006e\u0069\u006c");};if !_ggac .SizesEqual (s ){_bb .Log .Debug ("\u0025\u0073\u0020-\u0020\u0042\u0069\u0074\u006d\u0061\u0070\u0020\u0027\u0073\u0027\u0020\u0069\u0073\u0020\u006e\u006f\u0074\u0020\u0065\u0071\u0075\u0061\u006c\u0020\u0073\u0069\u007a\u0065 \u0077\u0069\u0074\u0068\u0020\u0027\u0062\u0027",_cdf );
};if _fgbd ,_dfca =_acda (_fgbd ,_ggac );_dfca !=nil {return nil ,_ga .Wrap (_dfca ,_cdf ,"\u0063\u0061\u006e't\u0020\u0063\u0072\u0065\u0061\u0074\u0065\u0020\u0027\u0064\u0027\u0020\u0062\u0069\u0074\u006d\u0061\u0070");};if _dfca =_fgbd .RasterOperation (0,0,_fgbd .Width ,_fgbd .Height ,PixSrcAndDst ,s ,0,0);
_dfca !=nil {return nil ,_ga .Wrap (_dfca ,_cdf ,"");};return _fgbd ,nil ;};func (_agef *BitmapsArray )GetBitmaps (i int )(*Bitmaps ,error ){const _bfac ="\u0042\u0069\u0074ma\u0070\u0073\u0041\u0072\u0072\u0061\u0079\u002e\u0047\u0065\u0074\u0042\u0069\u0074\u006d\u0061\u0070\u0073";
if _agef ==nil {return nil ,_ga .Error (_bfac ,"p\u0072\u006f\u0076\u0069\u0064\u0065d\u0020\u006e\u0069\u006c\u0020\u0027\u0042\u0069\u0074m\u0061\u0070\u0073A\u0072r\u0061\u0079\u0027");};if i > len (_agef .Values )-1{return nil ,_ga .Errorf (_bfac ,"\u0069n\u0064\u0065\u0078\u003a\u0020\u0027\u0025\u0064\u0027\u0020\u006fu\u0074\u0020\u006f\u0066\u0020\u0072\u0061\u006e\u0067\u0065",i );
};return _agef .Values [i ],nil ;};func (_abb *Bitmap )clearAll ()error {return _abb .RasterOperation (0,0,_abb .Width ,_abb .Height ,PixClr ,nil ,0,0);};type Getter interface{GetBitmap ()*Bitmap ;};func init (){const _ccgfa ="\u0062\u0069\u0074\u006dap\u0073\u002e\u0069\u006e\u0069\u0074\u0069\u0061\u006c\u0069\u007a\u0061\u0074\u0069o\u006e";
_affa =New (50,40);var _beddb error ;_affa ,_beddb =_affa .AddBorder (2,1);if _beddb !=nil {panic (_ga .Wrap (_beddb ,_ccgfa ,"f\u0072\u0061\u006d\u0065\u0042\u0069\u0074\u006d\u0061\u0070"));};_abdg ,_beddb =NewWithData (50,22,_gcagg );if _beddb !=nil {panic (_ga .Wrap (_beddb ,_ccgfa ,"i\u006d\u0061\u0067\u0065\u0042\u0069\u0074\u006d\u0061\u0070"));
};};func (_gfg *Bitmap )addBorderGeneral (_bgfd ,_bgc ,_dbb ,_aacg int ,_dggf int )(*Bitmap ,error ){const _efgd ="\u0061\u0064d\u0042\u006f\u0072d\u0065\u0072\u0047\u0065\u006e\u0065\u0072\u0061\u006c";if _bgfd < 0||_bgc < 0||_dbb < 0||_aacg < 0{return nil ,_ga .Error (_efgd ,"n\u0065\u0067\u0061\u0074iv\u0065 \u0062\u006f\u0072\u0064\u0065r\u0020\u0061\u0064\u0064\u0065\u0064");
};_efd ,_edb :=_gfg .Width ,_gfg .Height ;_aabc :=_efd +_bgfd +_bgc ;_ebb :=_edb +_dbb +_aacg ;_cad :=New (_aabc ,_ebb );_cad .Color =_gfg .Color ;_fcgg :=PixClr ;if _dggf > 0{_fcgg =PixSet ;};_eaed :=_cad .RasterOperation (0,0,_bgfd ,_ebb ,_fcgg ,nil ,0,0);
if _eaed !=nil {return nil ,_ga .Wrap (_eaed ,_efgd ,"\u006c\u0065\u0066\u0074");};_eaed =_cad .RasterOperation (_aabc -_bgc ,0,_bgc ,_ebb ,_fcgg ,nil ,0,0);if _eaed !=nil {return nil ,_ga .Wrap (_eaed ,_efgd ,"\u0072\u0069\u0067h\u0074");};_eaed =_cad .RasterOperation (0,0,_aabc ,_dbb ,_fcgg ,nil ,0,0);
if _eaed !=nil {return nil ,_ga .Wrap (_eaed ,_efgd ,"\u0074\u006f\u0070");};_eaed =_cad .RasterOperation (0,_ebb -_aacg ,_aabc ,_aacg ,_fcgg ,nil ,0,0);if _eaed !=nil {return nil ,_ga .Wrap (_eaed ,_efgd ,"\u0062\u006f\u0074\u0074\u006f\u006d");};_eaed =_cad .RasterOperation (_bgfd ,_dbb ,_efd ,_edb ,PixSrc ,_gfg ,0,0);
if _eaed !=nil {return nil ,_ga .Wrap (_eaed ,_efgd ,"\u0063\u006f\u0070\u0079");};return _cad ,nil ;};const (CmbOpOr CombinationOperator =iota ;CmbOpAnd ;CmbOpXor ;CmbOpXNor ;CmbOpReplace ;CmbOpNot ;);func NewWithUnpaddedData (width ,height int ,data []byte )(*Bitmap ,error ){const _cce ="\u004e\u0065\u0077\u0057it\u0068\u0055\u006e\u0070\u0061\u0064\u0064\u0065\u0064\u0044\u0061\u0074\u0061";
_ggc :=_fbgc (width ,height );_ggc .Data =data ;if _cfcd :=((width *height )+7)>>3;len (data )< _cfcd {return nil ,_ga .Errorf (_cce ,"\u0069\u006e\u0076\u0061\u006c\u0069\u0064\u0020\u0064a\u0074\u0061\u0020\u006c\u0065\u006e\u0067\u0074\u0068\u003a\u0020\u0027\u0025\u0064\u0027\u002e\u0020\u0054\u0068\u0065\u0020\u0064\u0061t\u0061\u0020s\u0068\u006fu\u006c\u0064\u0020\u0063\u006f\u006e\u0074\u0061\u0069\u006e\u0020\u0061\u0074 l\u0065\u0061\u0073\u0074\u003a\u0020\u0027\u0025\u0064'\u0020\u0062\u0079\u0074\u0065\u0073",len (data ),_cfcd );
};if _ced :=_ggc .addPadBits ();_ced !=nil {return nil ,_ga .Wrap (_ced ,_cce ,"");};return _ggc ,nil ;};func _eea ()(_bf [256]uint64 ){for _cc :=0;_cc < 256;_cc ++{if _cc &0x01!=0{_bf [_cc ]|=0xff;};if _cc &0x02!=0{_bf [_cc ]|=0xff00;};if _cc &0x04!=0{_bf [_cc ]|=0xff0000;
};if _cc &0x08!=0{_bf [_cc ]|=0xff000000;};if _cc &0x10!=0{_bf [_cc ]|=0xff00000000;};if _cc &0x20!=0{_bf [_cc ]|=0xff0000000000;};if _cc &0x40!=0{_bf [_cc ]|=0xff000000000000;};if _cc &0x80!=0{_bf [_cc ]|=0xff00000000000000;};};return _bf ;};type Point struct{X ,Y float32 ;
};func _caba (_gfcgb ,_gdba *Bitmap ,_afcc ,_fefa ,_cgbc ,_eeda ,_gadf ,_faeb ,_bgfa ,_efbc int ,_gbee CombinationOperator )error {var _efca int ;_bcgfb :=func (){_efca ++;_cgbc +=_gdba .RowStride ;_eeda +=_gfcgb .RowStride ;_gadf +=_gfcgb .RowStride };
for _efca =_afcc ;_efca < _fefa ;_bcgfb (){var _gadgd uint16 ;_eeeg :=_cgbc ;for _dgec :=_eeda ;_dgec <=_gadf ;_dgec ++{_dgcc ,_efea :=_gdba .GetByte (_eeeg );if _efea !=nil {return _efea ;};_fdec ,_efea :=_gfcgb .GetByte (_dgec );if _efea !=nil {return _efea ;
};_gadgd =(_gadgd |uint16 (_fdec ))<<uint (_efbc );_fdec =byte (_gadgd >>8);if _dgec ==_gadf {_fdec =_dcfe (uint (_faeb ),_fdec );};if _efea =_gdba .SetByte (_eeeg ,_ggde (_dgcc ,_fdec ,_gbee ));_efea !=nil {return _efea ;};_eeeg ++;_gadgd <<=uint (_bgfa );
};};return nil ;};func (_gbdf *ClassedPoints )Swap (i ,j int ){_gbdf .IntSlice [i ],_gbdf .IntSlice [j ]=_gbdf .IntSlice [j ],_gbdf .IntSlice [i ];};func MakePixelCentroidTab8 ()[]int {return _fcca ()};func RankHausTest (p1 ,p2 ,p3 ,p4 *Bitmap ,delX ,delY float32 ,maxDiffW ,maxDiffH ,area1 ,area3 int ,rank float32 ,tab8 []int )(_cdfd bool ,_febc error ){const _cca ="\u0052\u0061\u006ek\u0048\u0061\u0075\u0073\u0054\u0065\u0073\u0074";
_abbe ,_egge :=p1 .Width ,p1 .Height ;_bde ,_adceb :=p3 .Width ,p3 .Height ;if _eb .Abs (_abbe -_bde )> maxDiffW {return false ,nil ;};if _eb .Abs (_egge -_adceb )> maxDiffH {return false ,nil ;};_fcef :=int (float32 (area1 )*(1.0-rank )+0.5);_eddf :=int (float32 (area3 )*(1.0-rank )+0.5);
var _eaga ,_gdea int ;if delX >=0{_eaga =int (delX +0.5);}else {_eaga =int (delX -0.5);};if delY >=0{_gdea =int (delY +0.5);}else {_gdea =int (delY -0.5);};_bdca :=p1 .CreateTemplate ();if _febc =_bdca .RasterOperation (0,0,_abbe ,_egge ,PixSrc ,p1 ,0,0);
_febc !=nil {return false ,_ga .Wrap (_febc ,_cca ,"p\u0031\u0020\u002d\u0053\u0052\u0043\u002d\u003e\u0020\u0074");};if _febc =_bdca .RasterOperation (_eaga ,_gdea ,_abbe ,_egge ,PixNotSrcAndDst ,p4 ,0,0);_febc !=nil {return false ,_ga .Wrap (_febc ,_cca ,"\u0074 \u0026\u0020\u0021\u0070\u0034");
};_cdfd ,_febc =_bdca .ThresholdPixelSum (_fcef ,tab8 );if _febc !=nil {return false ,_ga .Wrap (_febc ,_cca ,"\u0074\u002d\u003e\u0074\u0068\u0072\u0065\u0073\u0068\u0031");};if _cdfd {return false ,nil ;};if _febc =_bdca .RasterOperation (_eaga ,_gdea ,_bde ,_adceb ,PixSrc ,p3 ,0,0);
_febc !=nil {return false ,_ga .Wrap (_febc ,_cca ,"p\u0033\u0020\u002d\u0053\u0052\u0043\u002d\u003e\u0020\u0074");};if _febc =_bdca .RasterOperation (0,0,_bde ,_adceb ,PixNotSrcAndDst ,p2 ,0,0);_febc !=nil {return false ,_ga .Wrap (_febc ,_cca ,"\u0074 \u0026\u0020\u0021\u0070\u0032");
};_cdfd ,_febc =_bdca .ThresholdPixelSum (_eddf ,tab8 );if _febc !=nil {return false ,_ga .Wrap (_febc ,_cca ,"\u0074\u002d\u003e\u0074\u0068\u0072\u0065\u0073\u0068\u0033");};return !_cdfd ,nil ;};func MakePixelSumTab8 ()[]int {return _dfeeb ()};func TstOSymbol (t *_f .T ,scale ...int )*Bitmap {_ebdfe ,_cfedc :=NewWithData (4,5,[]byte {0xF0,0x90,0x90,0x90,0xF0});
_gb .NoError (t ,_cfedc );return TstGetScaledSymbol (t ,_ebdfe ,scale ...);};func _fba ()(_fgf []byte ){_fgf =make ([]byte ,256);for _ega :=0;_ega < 256;_ega ++{_add :=byte (_ega );_fgf [_add ]=(_add &0x01)|((_add &0x04)>>1)|((_add &0x10)>>2)|((_add &0x40)>>3)|((_add &0x02)<<3)|((_add &0x08)<<2)|((_add &0x20)<<1)|(_add &0x80);
};return _fgf ;};func (_ecb *Bitmap )InverseData (){_ecb .inverseData ()};func (_ebcf *Bitmap )SizesEqual (s *Bitmap )bool {if _ebcf ==s {return true ;};if _ebcf .Width !=s .Width ||_ebcf .Height !=s .Height {return false ;};return true ;};func _gbde (_fege *Bitmap ,_eade ,_ddgb int ,_afgg ,_gade int ,_aagba RasterOperator ,_fcgaa *Bitmap ,_beee ,_gcag int )error {var _aacge ,_ebdaf ,_cafage ,_fccbc int ;
if _eade < 0{_beee -=_eade ;_afgg +=_eade ;_eade =0;};if _beee < 0{_eade -=_beee ;_afgg +=_beee ;_beee =0;};_aacge =_eade +_afgg -_fege .Width ;if _aacge > 0{_afgg -=_aacge ;};_ebdaf =_beee +_afgg -_fcgaa .Width ;if _ebdaf > 0{_afgg -=_ebdaf ;};if _ddgb < 0{_gcag -=_ddgb ;
_gade +=_ddgb ;_ddgb =0;};if _gcag < 0{_ddgb -=_gcag ;_gade +=_gcag ;_gcag =0;};_cafage =_ddgb +_gade -_fege .Height ;if _cafage > 0{_gade -=_cafage ;};_fccbc =_gcag +_gade -_fcgaa .Height ;if _fccbc > 0{_gade -=_fccbc ;};if _afgg <=0||_gade <=0{return nil ;
};var _cadg error ;switch {case _eade &7==0&&_beee &7==0:_cadg =_efad (_fege ,_eade ,_ddgb ,_afgg ,_gade ,_aagba ,_fcgaa ,_beee ,_gcag );case _eade &7==_beee &7:_cadg =_gbbf (_fege ,_eade ,_ddgb ,_afgg ,_gade ,_aagba ,_fcgaa ,_beee ,_gcag );default:_cadg =_ddecc (_fege ,_eade ,_ddgb ,_afgg ,_gade ,_aagba ,_fcgaa ,_beee ,_gcag );
};if _cadg !=nil {return _ga .Wrap (_cadg ,"r\u0061\u0073\u0074\u0065\u0072\u004f\u0070\u004c\u006f\u0077","");};return nil ;};func TstGetScaledSymbol (t *_f .T ,sm *Bitmap ,scale ...int )*Bitmap {if len (scale )==0{return sm ;};if scale [0]==1{return sm ;
};_ecbd ,_cfefb :=MorphSequence (sm ,MorphProcess {Operation :MopReplicativeBinaryExpansion ,Arguments :scale });_gb .NoError (t ,_cfefb );return _ecbd ;};func CombineBytes (oldByte ,newByte byte ,op CombinationOperator )byte {return _ggde (oldByte ,newByte ,op );
};func _aadgd (_egebb *Bitmap ,_fcde *_eb .Stack ,_egac ,_bffa int )(_bgfb *_c .Rectangle ,_efff error ){const _eafa ="\u0073e\u0065d\u0046\u0069\u006c\u006c\u0053\u0074\u0061\u0063\u006b\u0042\u0042";if _egebb ==nil {return nil ,_ga .Error (_eafa ,"\u0070\u0072\u006fvi\u0064\u0065\u0064\u0020\u006e\u0069\u006c\u0020\u0027\u0073\u0027\u0020\u0042\u0069\u0074\u006d\u0061\u0070");
};if _fcde ==nil {return nil ,_ga .Error (_eafa ,"p\u0072o\u0076\u0069\u0064\u0065\u0064\u0020\u006e\u0069l\u0020\u0027\u0073\u0074ac\u006b\u0027");};_ddgd ,_bfcb :=_egebb .Width ,_egebb .Height ;_aeaga :=_ddgd -1;_facb :=_bfcb -1;if _egac < 0||_egac > _aeaga ||_bffa < 0||_bffa > _facb ||!_egebb .GetPixel (_egac ,_bffa ){return nil ,nil ;
};var _gbce *_c .Rectangle ;_gbce ,_efff =Rect (100000,100000,0,0);if _efff !=nil {return nil ,_ga .Wrap (_efff ,_eafa ,"");};if _efff =_fbcc (_fcde ,_egac ,_egac ,_bffa ,1,_facb ,_gbce );_efff !=nil {return nil ,_ga .Wrap (_efff ,_eafa ,"\u0069\u006e\u0069t\u0069\u0061\u006c\u0020\u0070\u0075\u0073\u0068");
};if _efff =_fbcc (_fcde ,_egac ,_egac ,_bffa +1,-1,_facb ,_gbce );_efff !=nil {return nil ,_ga .Wrap (_efff ,_eafa ,"\u0032\u006ed\u0020\u0069\u006ei\u0074\u0069\u0061\u006c\u0020\u0070\u0075\u0073\u0068");};_gbce .Min .X ,_gbce .Max .X =_egac ,_egac ;
_gbce .Min .Y ,_gbce .Max .Y =_bffa ,_bffa ;var (_ccbf *fillSegment ;_eefg int ;);for _fcde .Len ()> 0{if _ccbf ,_efff =_eeed (_fcde );_efff !=nil {return nil ,_ga .Wrap (_efff ,_eafa ,"");};_bffa =_ccbf ._dcdf ;for _egac =_ccbf ._effa ;_egac >=0&&_egebb .GetPixel (_egac ,_bffa );
_egac --{if _efff =_egebb .SetPixel (_egac ,_bffa ,0);_efff !=nil {return nil ,_ga .Wrap (_efff ,_eafa ,"");};};if _egac >=_ccbf ._effa {for _egac ++;_egac <=_ccbf ._bacba &&_egac <=_aeaga &&!_egebb .GetPixel (_egac ,_bffa );_egac ++{};_eefg =_egac ;if !(_egac <=_ccbf ._bacba &&_egac <=_aeaga ){continue ;
};}else {_eefg =_egac +1;if _eefg < _ccbf ._effa -1{if _efff =_fbcc (_fcde ,_eefg ,_ccbf ._effa -1,_ccbf ._dcdf ,-_ccbf ._abbca ,_facb ,_gbce );_efff !=nil {return nil ,_ga .Wrap (_efff ,_eafa ,"\u006c\u0065\u0061\u006b\u0020\u006f\u006e\u0020\u006c\u0065\u0066\u0074 \u0073\u0069\u0064\u0065");
};};_egac =_ccbf ._effa +1;};for {for ;_egac <=_aeaga &&_egebb .GetPixel (_egac ,_bffa );_egac ++{if _efff =_egebb .SetPixel (_egac ,_bffa ,0);_efff !=nil {return nil ,_ga .Wrap (_efff ,_eafa ,"\u0032n\u0064\u0020\u0073\u0065\u0074");};};if _efff =_fbcc (_fcde ,_eefg ,_egac -1,_ccbf ._dcdf ,_ccbf ._abbca ,_facb ,_gbce );
_efff !=nil {return nil ,_ga .Wrap (_efff ,_eafa ,"n\u006f\u0072\u006d\u0061\u006c\u0020\u0070\u0075\u0073\u0068");};if _egac > _ccbf ._bacba +1{if _efff =_fbcc (_fcde ,_ccbf ._bacba +1,_egac -1,_ccbf ._dcdf ,-_ccbf ._abbca ,_facb ,_gbce );_efff !=nil {return nil ,_ga .Wrap (_efff ,_eafa ,"\u006ce\u0061k\u0020\u006f\u006e\u0020\u0072i\u0067\u0068t\u0020\u0073\u0069\u0064\u0065");
};};for _egac ++;_egac <=_ccbf ._bacba &&_egac <=_aeaga &&!_egebb .GetPixel (_egac ,_bffa );_egac ++{};_eefg =_egac ;if !(_egac <=_ccbf ._bacba &&_egac <=_aeaga ){break ;};};};_gbce .Max .X ++;_gbce .Max .Y ++;return _gbce ,nil ;};var _gcagg =[]byte {0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3E,0x78,0x27,0xC2,0x27,0x91,0x00,0x22,0x48,0x21,0x03,0x24,0x91,0x00,0x22,0x48,0x21,0x02,0xA4,0x95,0x00,0x22,0x48,0x21,0x02,0x64,0x9B,0x00,0x3C,0x78,0x21,0x02,0x27,0x91,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7F,0xF8,0x00,0x00,0x00,0x00,0x00,0x7F,0xF8,0x00,0x00,0x00,0x00,0x00,0x63,0x18,0x00,0x00,0x00,0x00,0x00,0x63,0x18,0x00,0x00,0x00,0x00,0x00,0x63,0x18,0x00,0x00,0x00,0x00,0x00,0x7F,0xF8,0x00,0x00,0x00,0x00,0x00,0x15,0x50,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00};
type Bitmaps struct{Values []*Bitmap ;Boxes []*_c .Rectangle ;};const (_ SizeComparison =iota ;SizeSelectIfLT ;SizeSelectIfGT ;SizeSelectIfLTE ;SizeSelectIfGTE ;SizeSelectIfEQ ;);func _aadg (_dbag ,_dfde *Bitmap ,_dagf ,_dbbd int )(_gbdde error ){const _aeaf ="\u0073e\u0065d\u0066\u0069\u006c\u006c\u0042i\u006e\u0061r\u0079\u004c\u006f\u0077\u0038";
var (_facc ,_adeg ,_fdgfg ,_ffbbd int ;_cdcd ,_fgcb ,_cbgd ,_gabd ,_fbggc ,_feeb ,_fdfd ,_fadag byte ;);for _facc =0;_facc < _dagf ;_facc ++{_fdgfg =_facc *_dbag .RowStride ;_ffbbd =_facc *_dfde .RowStride ;for _adeg =0;_adeg < _dbbd ;_adeg ++{if _cdcd ,_gbdde =_dbag .GetByte (_fdgfg +_adeg );
_gbdde !=nil {return _ga .Wrap (_gbdde ,_aeaf ,"\u0067e\u0074 \u0073\u006f\u0075\u0072\u0063\u0065\u0020\u0062\u0079\u0074\u0065");};if _fgcb ,_gbdde =_dfde .GetByte (_ffbbd +_adeg );_gbdde !=nil {return _ga .Wrap (_gbdde ,_aeaf ,"\u0067\u0065\u0074\u0020\u006d\u0061\u0073\u006b\u0020\u0062\u0079\u0074\u0065");
};if _facc > 0{if _cbgd ,_gbdde =_dbag .GetByte (_fdgfg -_dbag .RowStride +_adeg );_gbdde !=nil {return _ga .Wrap (_gbdde ,_aeaf ,"\u0069\u0020\u003e\u0020\u0030\u0020\u0062\u0079\u0074\u0065");};_cdcd |=_cbgd |(_cbgd <<1)|(_cbgd >>1);if _adeg > 0{if _fadag ,_gbdde =_dbag .GetByte (_fdgfg -_dbag .RowStride +_adeg -1);
_gbdde !=nil {return _ga .Wrap (_gbdde ,_aeaf ,"\u0069\u0020\u003e\u00200 \u0026\u0026\u0020\u006a\u0020\u003e\u0020\u0030\u0020\u0062\u0079\u0074\u0065");};_cdcd |=_fadag <<7;};if _adeg < _dbbd -1{if _fadag ,_gbdde =_dbag .GetByte (_fdgfg -_dbag .RowStride +_adeg +1);
_gbdde !=nil {return _ga .Wrap (_gbdde ,_aeaf ,"\u006a\u0020<\u0020\u0077\u0070l\u0020\u002d\u0020\u0031\u0020\u0062\u0079\u0074\u0065");};_cdcd |=_fadag >>7;};};if _adeg > 0{if _gabd ,_gbdde =_dbag .GetByte (_fdgfg +_adeg -1);_gbdde !=nil {return _ga .Wrap (_gbdde ,_aeaf ,"\u006a\u0020\u003e \u0030");
};_cdcd |=_gabd <<7;};_cdcd &=_fgcb ;if _cdcd ==0||^_cdcd ==0{if _gbdde =_dbag .SetByte (_fdgfg +_adeg ,_cdcd );_gbdde !=nil {return _ga .Wrap (_gbdde ,_aeaf ,"\u0073e\u0074t\u0069\u006e\u0067\u0020\u0065m\u0070\u0074y\u0020\u0062\u0079\u0074\u0065");};
};for {_fdfd =_cdcd ;_cdcd =(_cdcd |(_cdcd >>1)|(_cdcd <<1))&_fgcb ;if (_cdcd ^_fdfd )==0{if _gbdde =_dbag .SetByte (_fdgfg +_adeg ,_cdcd );_gbdde !=nil {return _ga .Wrap (_gbdde ,_aeaf ,"\u0073\u0065\u0074\u0074\u0069\u006e\u0067\u0020\u0070\u0072\u0065\u0076 \u0062\u0079\u0074\u0065");
};break ;};};};};for _facc =_dagf -1;_facc >=0;_facc --{_fdgfg =_facc *_dbag .RowStride ;_ffbbd =_facc *_dfde .RowStride ;for _adeg =_dbbd -1;_adeg >=0;_adeg --{if _cdcd ,_gbdde =_dbag .GetByte (_fdgfg +_adeg );_gbdde !=nil {return _ga .Wrap (_gbdde ,_aeaf ,"\u0072\u0065\u0076er\u0073\u0065\u0020\u0067\u0065\u0074\u0020\u0073\u006f\u0075\u0072\u0063\u0065\u0020\u0062\u0079\u0074\u0065");
};if _fgcb ,_gbdde =_dfde .GetByte (_ffbbd +_adeg );_gbdde !=nil {return _ga .Wrap (_gbdde ,_aeaf ,"r\u0065\u0076\u0065\u0072se\u0020g\u0065\u0074\u0020\u006d\u0061s\u006b\u0020\u0062\u0079\u0074\u0065");};if _facc < _dagf -1{if _fbggc ,_gbdde =_dbag .GetByte (_fdgfg +_dbag .RowStride +_adeg );
_gbdde !=nil {return _ga .Wrap (_gbdde ,_aeaf ,"\u0069\u0020\u003c\u0020h\u0020\u002d\u0020\u0031\u0020\u002d\u003e\u0020\u0067\u0065t\u0020s\u006f\u0075\u0072\u0063\u0065\u0020\u0062y\u0074\u0065");};_cdcd |=_fbggc |(_fbggc <<1)|_fbggc >>1;if _adeg > 0{if _fadag ,_gbdde =_dbag .GetByte (_fdgfg +_dbag .RowStride +_adeg -1);
_gbdde !=nil {return _ga .Wrap (_gbdde ,_aeaf ,"\u0069\u0020\u003c h\u002d\u0031\u0020\u0026\u0020\u006a\u0020\u003e\u00200\u0020-\u003e \u0067e\u0074\u0020\u0073\u006f\u0075\u0072\u0063\u0065\u0020\u0062\u0079\u0074\u0065");};_cdcd |=_fadag <<7;};if _adeg < _dbbd -1{if _fadag ,_gbdde =_dbag .GetByte (_fdgfg +_dbag .RowStride +_adeg +1);
_gbdde !=nil {return _ga .Wrap (_gbdde ,_aeaf ,"\u0069\u0020\u003c\u0020\u0068\u002d\u0031\u0020\u0026\u0026\u0020\u006a\u0020\u003c\u0077\u0070\u006c\u002d\u0031\u0020\u002d\u003e\u0020\u0067e\u0074\u0020\u0073\u006f\u0075r\u0063\u0065 \u0062\u0079\u0074\u0065");
};_cdcd |=_fadag >>7;};};if _adeg < _dbbd -1{if _feeb ,_gbdde =_dbag .GetByte (_fdgfg +_adeg +1);_gbdde !=nil {return _ga .Wrap (_gbdde ,_aeaf ,"\u006a\u0020<\u0020\u0077\u0070\u006c\u0020\u002d\u0031\u0020\u002d\u003e\u0020\u0067\u0065\u0074\u0020\u0073\u006f\u0075\u0072\u0063\u0065\u0020by\u0074\u0065");
};_cdcd |=_feeb >>7;};_cdcd &=_fgcb ;if _cdcd ==0||(^_cdcd )==0{if _gbdde =_dbag .SetByte (_fdgfg +_adeg ,_cdcd );_gbdde !=nil {return _ga .Wrap (_gbdde ,_aeaf ,"\u0073e\u0074 \u006d\u0061\u0073\u006b\u0065\u0064\u0020\u0062\u0079\u0074\u0065");};};for {_fdfd =_cdcd ;
_cdcd =(_cdcd |(_cdcd >>1)|(_cdcd <<1))&_fgcb ;if (_cdcd ^_fdfd )==0{if _gbdde =_dbag .SetByte (_fdgfg +_adeg ,_cdcd );_gbdde !=nil {return _ga .Wrap (_gbdde ,_aeaf ,"r\u0065\u0076\u0065\u0072se\u0020s\u0065\u0074\u0020\u0070\u0072e\u0076\u0020\u0062\u0079\u0074\u0065");
};break ;};};};};return nil ;};