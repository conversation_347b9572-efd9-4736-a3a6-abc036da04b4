//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package cmap ;import (_a "bufio";_d "bytes";_g "encoding/hex";_de "errors";_b "fmt";_cea "github.com/unidoc/unipdf/v3/common";_cc "github.com/unidoc/unipdf/v3/core";_f "github.com/unidoc/unipdf/v3/internal/cmap/bcmaps";_bf "io";_gc "sort";_ce "strconv";
_bc "strings";_e "unicode/utf16";);type cmapDict struct{Dict map[string ]cmapObject ;};type CIDSystemInfo struct{Registry string ;Ordering string ;Supplement int ;};type Codespace struct{NumBytes int ;Low CharCode ;High CharCode ;};func (cmap *CMap )CharcodeToUnicode (code CharCode )(string ,bool ){if _fcd ,_ea :=cmap ._ef [code ];
_ea {return _fcd ,true ;};return MissingCodeString ,false ;};func (cmap *CMap )CIDSystemInfo ()CIDSystemInfo {return cmap ._bdb };const (_dfc ="\u0043\u0049\u0044\u0053\u0079\u0073\u0074\u0065\u006d\u0049\u006e\u0066\u006f";_fab ="\u0062e\u0067\u0069\u006e\u0063\u006d\u0061p";
_gba ="\u0065n\u0064\u0063\u006d\u0061\u0070";_efb ="\u0062\u0065\u0067\u0069nc\u006f\u0064\u0065\u0073\u0070\u0061\u0063\u0065\u0072\u0061\u006e\u0067\u0065";_cdf ="\u0065\u006e\u0064\u0063\u006f\u0064\u0065\u0073\u0070\u0061\u0063\u0065r\u0061\u006e\u0067\u0065";
_ded ="b\u0065\u0067\u0069\u006e\u0062\u0066\u0063\u0068\u0061\u0072";_efd ="\u0065n\u0064\u0062\u0066\u0063\u0068\u0061r";_bcdd ="\u0062\u0065\u0067i\u006e\u0062\u0066\u0072\u0061\u006e\u0067\u0065";_deg ="\u0065\u006e\u0064\u0062\u0066\u0072\u0061\u006e\u0067\u0065";
_acg ="\u0062\u0065\u0067\u0069\u006e\u0063\u0069\u0064\u0072\u0061\u006e\u0067\u0065";_ddf ="e\u006e\u0064\u0063\u0069\u0064\u0072\u0061\u006e\u0067\u0065";_gdd ="\u0075s\u0065\u0063\u006d\u0061\u0070";_bdd ="\u0057\u004d\u006fd\u0065";_fdgc ="\u0043\u004d\u0061\u0070\u004e\u0061\u006d\u0065";
_beb ="\u0043\u004d\u0061\u0070\u0054\u0079\u0070\u0065";_fagb ="C\u004d\u0061\u0070\u0056\u0065\u0072\u0073\u0069\u006f\u006e";);type CharCode uint32 ;func LoadCmapFromData (data []byte ,isSimple bool )(*CMap ,error ){_cea .Log .Trace ("\u004c\u006fa\u0064\u0043\u006d\u0061\u0070\u0046\u0072\u006f\u006d\u0044\u0061\u0074\u0061\u003a\u0020\u0069\u0073\u0053\u0069\u006d\u0070\u006ce=\u0025\u0074",isSimple );
cmap :=_ccd (isSimple );cmap .cMapParser =_bgg (data );_eg :=cmap .parse ();if _eg !=nil {return nil ,_eg ;};if len (cmap ._bb )==0{if cmap ._aac !=""{return cmap ,nil ;};_cea .Log .Debug ("\u0045\u0052R\u004f\u0052\u003a\u0020\u004e\u006f\u0020\u0063\u006f\u0064\u0065\u0073\u0070\u0061\u0063\u0065\u0073\u002e\u0020\u0063\u006d\u0061p=\u0025\u0073",cmap );
};cmap .computeInverseMappings ();return cmap ,nil ;};func (cmap *CMap )parse ()error {var _cbcc cmapObject ;for {_dda ,_ggg :=cmap .parseObject ();if _ggg !=nil {if _ggg ==_bf .EOF {break ;};_cea .Log .Debug ("\u0045\u0052\u0052OR\u003a\u0020\u0070\u0061\u0072\u0073\u0069\u006e\u0067\u0020\u0043\u004d\u0061\u0070\u003a\u0020\u0025\u0076",_ggg );
return _ggg ;};switch _dab :=_dda .(type ){case cmapOperand :_edc :=_dab ;switch _edc .Operand {case _efb :_beg :=cmap .parseCodespaceRange ();if _beg !=nil {return _beg ;};case _acg :_fef :=cmap .parseCIDRange ();if _fef !=nil {return _fef ;};case _ded :_dccb :=cmap .parseBfchar ();
if _dccb !=nil {return _dccb ;};case _bcdd :_gfd :=cmap .parseBfrange ();if _gfd !=nil {return _gfd ;};case _gdd :if _cbcc ==nil {_cea .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u0075\u0073\u0065\u0063m\u0061\u0070\u0020\u0077\u0069\u0074\u0068\u0020\u006e\u006f \u0061\u0072\u0067");
return ErrBadCMap ;};_aee ,_fdbc :=_cbcc .(cmapName );if !_fdbc {_cea .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a \u0075\u0073\u0065\u0063\u006d\u0061\u0070\u0020\u0061\u0072\u0067\u0020\u006eo\u0074\u0020\u0061\u0020\u006e\u0061\u006de\u0020\u0025\u0023\u0076",_cbcc );
return ErrBadCMap ;};cmap ._aac =_aee .Name ;case _dfc :_cdb :=cmap .parseSystemInfo ();if _cdb !=nil {return _cdb ;};};case cmapName :_gb :=_dab ;switch _gb .Name {case _dfc :_eaf :=cmap .parseSystemInfo ();if _eaf !=nil {return _eaf ;};case _fdgc :_efa :=cmap .parseName ();
if _efa !=nil {return _efa ;};case _beb :_bcg :=cmap .parseType ();if _bcg !=nil {return _bcg ;};case _fagb :_gbg :=cmap .parseVersion ();if _gbg !=nil {return _gbg ;};case _bdd :if _ggg =cmap .parseWMode ();_ggg !=nil {return _ggg ;};};};_cbcc =_dda ;
};return nil ;};func _dgc (_fae ,_adac int )int {if _fae < _adac {return _fae ;};return _adac ;};func (cmap *CMap )Bytes ()[]byte {_cea .Log .Trace ("\u0063\u006d\u0061\u0070.B\u0079\u0074\u0065\u0073\u003a\u0020\u0063\u006d\u0061\u0070\u003d\u0025\u0073",cmap .String ());
if len (cmap ._ga )> 0{return cmap ._ga ;};cmap ._ga =[]byte (_bc .Join ([]string {_dfdc ,cmap .toBfData (),_fge },"\u000a"));return cmap ._ga ;};type cmapHexString struct{_efef int ;_cgad []byte ;};func (cmap *CMap )WMode ()(int ,bool ){return cmap ._cg ._cgfd ,cmap ._cg ._dfgb };
func (cmap *CMap )parseName ()error {_dccbf :="";_adc :=false ;for _aabb :=0;_aabb < 20&&!_adc ;_aabb ++{_ccgb ,_dccf :=cmap .parseObject ();if _dccf !=nil {return _dccf ;};switch _dfef :=_ccgb .(type ){case cmapOperand :switch _dfef .Operand {case "\u0064\u0065\u0066":_adc =true ;
default:_cea .Log .Debug ("\u0070\u0061\u0072\u0073\u0065\u004e\u0061\u006d\u0065\u003a\u0020\u0053\u0074\u0061\u0074\u0065\u0020\u0065\u0072\u0072\u006f\u0072\u002e\u0020o\u003d\u0025\u0023\u0076\u0020n\u0061\u006de\u003d\u0025\u0023\u0071",_ccgb ,_dccbf );
if _dccbf !=""{_dccbf =_b .Sprintf ("\u0025\u0073\u0020%\u0073",_dccbf ,_dfef .Operand );};_cea .Log .Debug ("\u0070\u0061\u0072\u0073\u0065\u004e\u0061\u006d\u0065\u003a \u0052\u0065\u0063\u006f\u0076\u0065\u0072e\u0064\u002e\u0020\u006e\u0061\u006d\u0065\u003d\u0025\u0023\u0071",_dccbf );
};case cmapName :_dccbf =_dfef .Name ;};};if !_adc {_cea .Log .Debug ("\u0045R\u0052\u004f\u0052\u003a \u0070\u0061\u0072\u0073\u0065N\u0061m\u0065:\u0020\u004e\u006f\u0020\u0064\u0065\u0066 ");return ErrBadCMap ;};cmap ._cb =_dccbf ;return nil ;};func (cmap *CMap )computeInverseMappings (){for _abc ,_cfe :=range cmap ._ff {if _ccg ,_bfe :=cmap ._gf [_cfe ];
!_bfe ||(_bfe &&_ccg > _abc ){cmap ._gf [_cfe ]=_abc ;};};for _fgd ,_df :=range cmap ._ef {if _gaa ,_abd :=cmap ._fgb [_df ];!_abd ||(_abd &&_gaa > _fgd ){cmap ._fgb [_df ]=_fgd ;};};_gc .Slice (cmap ._bb ,func (_gaad ,_ge int )bool {return cmap ._bb [_gaad ].Low < cmap ._bb [_ge ].Low });
};func (_gdb *cMapParser )parseObject ()(cmapObject ,error ){_gdb .skipSpaces ();for {_bfac ,_ggbc :=_gdb ._dcbe .Peek (2);if _ggbc !=nil {return nil ,_ggbc ;};if _bfac [0]=='%'{_gdb .parseComment ();_gdb .skipSpaces ();continue ;}else if _bfac [0]=='/'{_ebf ,_fcb :=_gdb .parseName ();
return _ebf ,_fcb ;}else if _bfac [0]=='('{_gfc ,_bbb :=_gdb .parseString ();return _gfc ,_bbb ;}else if _bfac [0]=='['{_bfaa ,_agb :=_gdb .parseArray ();return _bfaa ,_agb ;}else if (_bfac [0]=='<')&&(_bfac [1]=='<'){_bbc ,_gdeg :=_gdb .parseDict ();return _bbc ,_gdeg ;
}else if _bfac [0]=='<'{_gdga ,_cegc :=_gdb .parseHexString ();return _gdga ,_cegc ;}else if _cc .IsDecimalDigit (_bfac [0])||(_bfac [0]=='-'&&_cc .IsDecimalDigit (_bfac [1])){_abg ,_deee :=_gdb .parseNumber ();if _deee !=nil {return nil ,_deee ;};return _abg ,nil ;
}else {_acdd ,_fefc :=_gdb .parseOperand ();if _fefc !=nil {return nil ,_fefc ;};return _acdd ,nil ;};};};func (_ec *CIDSystemInfo )String ()string {return _b .Sprintf ("\u0025\u0073\u002d\u0025\u0073\u002d\u0025\u0030\u0033\u0064",_ec .Registry ,_ec .Ordering ,_ec .Supplement );
};func (cmap *CMap )NBits ()int {return cmap ._bfc };func (cmap *CMap )inCodespace (_fdb CharCode ,_feb int )bool {for _ ,_ffae :=range cmap ._bb {if _ffae .Low <=_fdb &&_fdb <=_ffae .High &&_feb ==_ffae .NumBytes {return true ;};};return false ;};func (cmap *CMap )String ()string {_dfgf :=cmap ._bdb ;
_ceac :=[]string {_b .Sprintf ("\u006e\u0062\u0069\u0074\u0073\u003a\u0025\u0064",cmap ._bfc ),_b .Sprintf ("\u0074y\u0070\u0065\u003a\u0025\u0064",cmap ._cef )};if cmap ._gcc !=""{_ceac =append (_ceac ,_b .Sprintf ("\u0076\u0065\u0072\u0073\u0069\u006f\u006e\u003a\u0025\u0073",cmap ._gcc ));
};if cmap ._aac !=""{_ceac =append (_ceac ,_b .Sprintf ("u\u0073\u0065\u0063\u006d\u0061\u0070\u003a\u0025\u0023\u0071",cmap ._aac ));};_ceac =append (_ceac ,_b .Sprintf ("\u0073\u0079\u0073\u0074\u0065\u006d\u0049\u006e\u0066\u006f\u003a\u0025\u0073",_dfgf .String ()));
if len (cmap ._bb )> 0{_ceac =append (_ceac ,_b .Sprintf ("\u0063\u006f\u0064\u0065\u0073\u0070\u0061\u0063\u0065\u0073\u003a\u0025\u0064",len (cmap ._bb )));};if len (cmap ._ef )> 0{_ceac =append (_ceac ,_b .Sprintf ("\u0063\u006fd\u0065\u0054\u006fU\u006e\u0069\u0063\u006f\u0064\u0065\u003a\u0025\u0064",len (cmap ._ef )));
};return _b .Sprintf ("\u0043\u004d\u0041P\u007b\u0025\u0023\u0071\u0020\u0025\u0073\u007d",cmap ._cb ,_bc .Join (_ceac ,"\u0020"));};func (cmap *CMap )parseCodespaceRange ()error {for {_cdad ,_eaee :=cmap .parseObject ();if _eaee !=nil {if _eaee ==_bf .EOF {break ;
};return _eaee ;};_gdc ,_abde :=_cdad .(cmapHexString );if !_abde {if _fdc ,_fff :=_cdad .(cmapOperand );_fff {if _fdc .Operand ==_cdf {return nil ;};return _de .New ("\u0075n\u0065x\u0070\u0065\u0063\u0074\u0065d\u0020\u006fp\u0065\u0072\u0061\u006e\u0064");
};};_cdad ,_eaee =cmap .parseObject ();if _eaee !=nil {if _eaee ==_bf .EOF {break ;};return _eaee ;};_gaeg ,_abde :=_cdad .(cmapHexString );if !_abde {return _de .New ("\u006e\u006f\u006e-\u0068\u0065\u0078\u0020\u0068\u0069\u0067\u0068");};if len (_gdc ._cgad )!=len (_gaeg ._cgad ){return _de .New ("\u0075\u006e\u0065\u0071\u0075\u0061\u006c\u0020\u006e\u0075\u006d\u0062\u0065\u0072\u0020o\u0066 \u0062\u0079\u0074\u0065\u0073\u0020\u0069\u006e\u0020\u0072\u0061\u006e\u0067\u0065");
};_fdg :=_gaec (_gdc );_feag :=_gaec (_gaeg );if _feag < _fdg {_cea .Log .Debug ("\u0045R\u0052\u004fR\u003a\u0020\u0042\u0061d\u0020\u0063\u006fd\u0065\u0073\u0070\u0061\u0063\u0065\u002e\u0020\u006cow\u003d\u0030\u0078%\u0030\u0032x\u0020\u0068\u0069\u0067\u0068\u003d0\u0078\u00250\u0032\u0078",_fdg ,_feag );
return ErrBadCMap ;};_edg :=_gaeg ._efef ;_abda :=Codespace {NumBytes :_edg ,Low :_fdg ,High :_feag };cmap ._bb =append (cmap ._bb ,_abda );_cea .Log .Trace ("\u0043\u006f\u0064e\u0073\u0070\u0061\u0063e\u0020\u006c\u006f\u0077\u003a\u0020\u0030x\u0025\u0058\u002c\u0020\u0068\u0069\u0067\u0068\u003a\u0020\u0030\u0078\u0025\u0058",_fdg ,_feag );
};if len (cmap ._bb )==0{_cea .Log .Debug ("\u0045\u0052R\u004f\u0052\u003a\u0020\u004e\u006f\u0020\u0063\u006f\u0064\u0065\u0073\u0070\u0061\u0063\u0065\u0073\u0020\u0069\u006e\u0020\u0063ma\u0070\u002e");return ErrBadCMap ;};return nil ;};func (cmap *CMap )CIDToCharcode (cid CharCode )(CharCode ,bool ){_aaf ,_cgc :=cmap ._gf [cid ];
return _aaf ,_cgc ;};var (ErrBadCMap =_de .New ("\u0062\u0061\u0064\u0020\u0063\u006d\u0061\u0070");ErrBadCMapComment =_de .New ("c\u006f\u006d\u006d\u0065\u006e\u0074 \u0073\u0068\u006f\u0075\u006c\u0064\u0020\u0073\u0074a\u0072\u0074\u0020w\u0069t\u0068\u0020\u0025");
ErrBadCMapDict =_de .New ("\u0069\u006e\u0076a\u006c\u0069\u0064\u0020\u0064\u0069\u0063\u0074"););func (_dabd *cMapParser )skipSpaces ()(int ,error ){_eeb :=0;for {_aeee ,_gfce :=_dabd ._dcbe .Peek (1);if _gfce !=nil {return 0,_gfce ;};if _cc .IsWhiteSpace (_aeee [0]){_dabd ._dcbe .ReadByte ();
_eeb ++;}else {break ;};};return _eeb ,nil ;};func (_ecc *cMapParser )parseOperand ()(cmapOperand ,error ){_gfb :=cmapOperand {};_fee :=_d .Buffer {};for {_eef ,_bgb :=_ecc ._dcbe .Peek (1);if _bgb !=nil {if _bgb ==_bf .EOF {break ;};return _gfb ,_bgb ;
};if _cc .IsDelimiter (_eef [0]){break ;};if _cc .IsWhiteSpace (_eef [0]){break ;};_dga ,_ :=_ecc ._dcbe .ReadByte ();_fee .WriteByte (_dga );};if _fee .Len ()==0{return _gfb ,_b .Errorf ("\u0069\u006e\u0076al\u0069\u0064\u0020\u006f\u0070\u0065\u0072\u0061\u006e\u0064\u0020\u0028\u0065\u006d\u0070\u0074\u0079\u0029");
};_gfb .Operand =_fee .String ();return _gfb ,nil ;};func _be (_ecb string )(*CMap ,error ){_cbc ,_edb :=_f .Asset (_ecb );if _edb !=nil {return nil ,_edb ;};return LoadCmapFromDataCID (_cbc );};func (cmap *CMap )parseVersion ()error {_ege :="";_dcf :=false ;
for _ceb :=0;_ceb < 3&&!_dcf ;_ceb ++{_dca ,_cefa :=cmap .parseObject ();if _cefa !=nil {return _cefa ;};switch _dfge :=_dca .(type ){case cmapOperand :switch _dfge .Operand {case "\u0064\u0065\u0066":_dcf =true ;default:_cea .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u0070\u0061\u0072\u0073\u0065\u0056e\u0072\u0073\u0069\u006f\u006e\u003a \u0073\u0074\u0061\u0074\u0065\u0020\u0065\u0072\u0072\u006f\u0072\u002e\u0020o\u003d\u0025\u0023\u0076",_dca );
return ErrBadCMap ;};case cmapInt :_ege =_b .Sprintf ("\u0025\u0064",_dfge ._cgcg );case cmapFloat :_ege =_b .Sprintf ("\u0025\u0066",_dfge ._egfe );case cmapString :_ege =_dfge .String ;default:_cea .Log .Debug ("\u0045\u0052RO\u0052\u003a\u0020p\u0061\u0072\u0073\u0065Ver\u0073io\u006e\u003a\u0020\u0042\u0061\u0064\u0020ty\u0070\u0065\u002e\u0020\u006f\u003d\u0025#\u0076",_dca );
};};cmap ._gcc =_ege ;return nil ;};type CMap struct{*cMapParser ;_cb string ;_bfc int ;_cef int ;_gcc string ;_aac string ;_bdb CIDSystemInfo ;_bb []Codespace ;_ff map[CharCode ]CharCode ;_gf map[CharCode ]CharCode ;_ef map[CharCode ]string ;_fgb map[string ]CharCode ;
_ga []byte ;_adg *_cc .PdfObjectStream ;_cg integer ;};func LoadPredefinedCMap (name string )(*CMap ,error ){cmap ,_ffa :=_be (name );if _ffa !=nil {return nil ,_ffa ;};if cmap ._aac ==""{cmap .computeInverseMappings ();return cmap ,nil ;};_ceaf ,_ffa :=_be (cmap ._aac );
if _ffa !=nil {return nil ,_ffa ;};for _cac ,_eb :=range _ceaf ._ff {if _ ,_gd :=cmap ._ff [_cac ];!_gd {cmap ._ff [_cac ]=_eb ;};};cmap ._bb =append (cmap ._bb ,_ceaf ._bb ...);cmap .computeInverseMappings ();return cmap ,nil ;};func (cmap *CMap )CharcodeBytesToUnicode (data []byte )(string ,int ){_fc ,_bg :=cmap .BytesToCharcodes (data );
if !_bg {_cea .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u0043\u0068\u0061\u0072\u0063\u006f\u0064\u0065\u0042\u0079\u0074\u0065s\u0054\u006f\u0055\u006e\u0069\u0063\u006f\u0064\u0065\u002e\u0020\u004e\u006f\u0074\u0020\u0069n\u0020\u0063\u006f\u0064\u0065\u0073\u0070\u0061\u0063\u0065\u0073\u002e\u0020\u0064\u0061\u0074\u0061\u003d\u005b\u0025\u0020\u0030\u0032\u0078]\u0020\u0063\u006d\u0061\u0070=\u0025\u0073",data ,cmap );
return "",0;};_caf :=make ([]string ,len (_fc ));var _ecbc []CharCode ;for _ae ,_cgf :=range _fc {_gccb ,_dcb :=cmap ._ef [_cgf ];if !_dcb {_ecbc =append (_ecbc ,_cgf );_gccb =MissingCodeString ;};_caf [_ae ]=_gccb ;};_cgd :=_bc .Join (_caf ,"");if len (_ecbc )> 0{_cea .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020C\u0068\u0061\u0072c\u006f\u0064\u0065\u0042y\u0074\u0065\u0073\u0054\u006f\u0055\u006e\u0069\u0063\u006f\u0064\u0065\u002e\u0020\u004e\u006f\u0074\u0020\u0069\u006e\u0020\u006d\u0061\u0070\u002e\u000a"+"\u0009d\u0061t\u0061\u003d\u005b\u0025\u00200\u0032\u0078]\u003d\u0025\u0023\u0071\u000a"+"\u0009\u0063h\u0061\u0072\u0063o\u0064\u0065\u0073\u003d\u0025\u0030\u0032\u0078\u000a"+"\u0009\u006d\u0069\u0073\u0073\u0069\u006e\u0067\u003d\u0025\u0064\u0020%\u0030\u0032\u0078\u000a"+"\u0009\u0075\u006e\u0069\u0063\u006f\u0064\u0065\u003d`\u0025\u0073\u0060\u000a"+"\u0009\u0063\u006d\u0061\u0070\u003d\u0025\u0073",data ,string (data ),_fc ,len (_ecbc ),_ecbc ,_cgd ,cmap );
};return _cgd ,len (_ecbc );};func _fda ()cmapDict {return cmapDict {Dict :map[string ]cmapObject {}}};type cmapOperand struct{Operand string ;};func (cmap *CMap )CharcodeToCID (code CharCode )(CharCode ,bool ){_ceg ,_agc :=cmap ._ff [code ];return _ceg ,_agc ;
};type cmapFloat struct{_egfe float64 };func (cmap *CMap )toBfData ()string {if len (cmap ._ef )==0{return "";};_egb :=make ([]CharCode ,0,len (cmap ._ef ));for _cgcb :=range cmap ._ef {_egb =append (_egb ,_cgcb );};_gc .Slice (_egb ,func (_cefe ,_dcc int )bool {return _egb [_cefe ]< _egb [_dcc ]});
var _acdf []charRange ;_ba :=charRange {_egb [0],_egb [0]};_abf :=cmap ._ef [_egb [0]];for _ ,_dgf :=range _egb [1:]{_aab :=cmap ._ef [_dgf ];if _dgf ==_ba ._ad +1&&_bac (_aab )==_bac (_abf )+1{_ba ._ad =_dgf ;}else {_acdf =append (_acdf ,_ba );_ba ._dg ,_ba ._ad =_dgf ,_dgf ;
};_abf =_aab ;};_acdf =append (_acdf ,_ba );var _bfb []CharCode ;var _dec []fbRange ;for _ ,_eeg :=range _acdf {if _eeg ._dg ==_eeg ._ad {_bfb =append (_bfb ,_eeg ._dg );}else {_dec =append (_dec ,fbRange {_adf :_eeg ._dg ,_ca :_eeg ._ad ,_fg :cmap ._ef [_eeg ._dg ]});
};};_cea .Log .Trace ("\u0063\u0068ar\u0052\u0061\u006eg\u0065\u0073\u003d\u0025d f\u0062Ch\u0061\u0072\u0073\u003d\u0025\u0064\u0020fb\u0052\u0061\u006e\u0067\u0065\u0073\u003d%\u0064",len (_acdf ),len (_bfb ),len (_dec ));var _gab []string ;if len (_bfb )> 0{_bff :=(len (_bfb )+_acf -1)/_acf ;
for _age :=0;_age < _bff ;_age ++{_eac :=_dgc (len (_bfb )-_age *_acf ,_acf );_gab =append (_gab ,_b .Sprintf ("\u0025\u0064\u0020\u0062\u0065\u0067\u0069\u006e\u0062f\u0063\u0068\u0061\u0072",_eac ));for _gee :=0;_gee < _eac ;_gee ++{_bdg :=_bfb [_age *_acf +_gee ];
_dfd :=cmap ._ef [_bdg ];_gab =append (_gab ,_b .Sprintf ("\u003c%\u0030\u0034\u0078\u003e\u0020\u0025s",_bdg ,_aeb (_dfd )));};_gab =append (_gab ,"\u0065n\u0064\u0062\u0066\u0063\u0068\u0061r");};};if len (_dec )> 0{_dde :=(len (_dec )+_acf -1)/_acf ;
for _bef :=0;_bef < _dde ;_bef ++{_dfa :=_dgc (len (_dec )-_bef *_acf ,_acf );_gab =append (_gab ,_b .Sprintf ("\u0025d\u0020b\u0065\u0067\u0069\u006e\u0062\u0066\u0072\u0061\u006e\u0067\u0065",_dfa ));for _dcce :=0;_dcce < _dfa ;_dcce ++{_cda :=_dec [_bef *_acf +_dcce ];
_gab =append (_gab ,_b .Sprintf ("\u003c%\u00304\u0078\u003e\u003c\u0025\u0030\u0034\u0078\u003e\u0020\u0025\u0073",_cda ._adf ,_cda ._ca ,_aeb (_cda ._fg )));};_gab =append (_gab ,"\u0065\u006e\u0064\u0062\u0066\u0072\u0061\u006e\u0067\u0065");};};return _bc .Join (_gab ,"\u000a");
};type cMapParser struct{_dcbe *_a .Reader };type fbRange struct{_adf CharCode ;_ca CharCode ;_fg string ;};func (cmap *CMap )parseCIDRange ()error {for {_edd ,_dce :=cmap .parseObject ();if _dce !=nil {if _dce ==_bf .EOF {break ;};return _dce ;};_adcf ,_ebdf :=_edd .(cmapHexString );
if !_ebdf {if _bda ,_efag :=_edd .(cmapOperand );_efag {if _bda .Operand ==_ddf {return nil ;};return _de .New ("\u0063\u0069\u0064\u0020\u0069\u006e\u0074\u0065\u0072\u0076\u0061\u006c\u0020s\u0074\u0061\u0072\u0074\u0020\u006du\u0073\u0074\u0020\u0062\u0065\u0020\u0061\u0020\u0068\u0065\u0078\u0020\u0073t\u0072\u0069\u006e\u0067");
};};_ccf :=_gaec (_adcf );_edd ,_dce =cmap .parseObject ();if _dce !=nil {if _dce ==_bf .EOF {break ;};return _dce ;};_dgbc ,_ebdf :=_edd .(cmapHexString );if !_ebdf {return _de .New ("\u0063\u0069d\u0020\u0069\u006e\u0074e\u0072\u0076a\u006c\u0020\u0065\u006e\u0064\u0020\u006d\u0075s\u0074\u0020\u0062\u0065\u0020\u0061\u0020\u0068\u0065\u0078\u0020\u0073t\u0072\u0069\u006e\u0067");
};if len (_adcf ._cgad )!=len (_dgbc ._cgad ){return _de .New ("\u0075\u006e\u0065\u0071\u0075\u0061\u006c\u0020\u006e\u0075\u006d\u0062\u0065\u0072\u0020o\u0066 \u0062\u0079\u0074\u0065\u0073\u0020\u0069\u006e\u0020\u0072\u0061\u006e\u0067\u0065");};_feg :=_gaec (_dgbc );
if _ccf > _feg {_cea .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a \u0069\u006e\u0076\u0061\u006c\u0069\u0064\u0020\u0043\u0049\u0044\u0020\u0072\u0061\u006e\u0067\u0065\u002e\u0020\u0073t\u0061\u0072\u0074\u003d\u0030\u0078\u0025\u0030\u0032\u0078\u0020\u0065\u006e\u0064=\u0030x\u0025\u0030\u0032\u0078",_ccf ,_feg );
return ErrBadCMap ;};_edd ,_dce =cmap .parseObject ();if _dce !=nil {if _dce ==_bf .EOF {break ;};return _dce ;};_cgfc ,_ebdf :=_edd .(cmapInt );if !_ebdf {return _de .New ("\u0063\u0069\u0064\u0020\u0073t\u0061\u0072\u0074\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u006d\u0075\u0073t\u0020\u0062\u0065\u0020\u0061\u006e\u0020\u0064\u0065\u0063\u0069\u006d\u0061\u006c\u0020\u006e\u0075\u006d\u0062\u0065\u0072");
};if _cgfc ._cgcg < 0{return _de .New ("\u0069\u006e\u0076al\u0069\u0064\u0020\u0063\u0069\u0064\u0020\u0073\u0074\u0061\u0072\u0074\u0020\u0076\u0061\u006c\u0075\u0065");};_eged :=_cgfc ._cgcg ;for _gdcd :=_ccf ;_gdcd <=_feg ;_gdcd ++{cmap ._ff [_gdcd ]=CharCode (_eged );
_eged ++;};_cea .Log .Trace ("C\u0049\u0044\u0020\u0072\u0061\u006eg\u0065\u003a\u0020\u003c\u0030\u0078\u0025\u0058\u003e \u003c\u0030\u0078%\u0058>\u0020\u0025\u0064",_ccf ,_feg ,_cgfc ._cgcg );};return nil ;};func (cmap *CMap )parseBfrange ()error {for {var _gge CharCode ;
_ebe ,_gbf :=cmap .parseObject ();if _gbf !=nil {if _gbf ==_bf .EOF {break ;};return _gbf ;};switch _deb :=_ebe .(type ){case cmapOperand :if _deb .Operand ==_deg {return nil ;};return _de .New ("\u0075n\u0065x\u0070\u0065\u0063\u0074\u0065d\u0020\u006fp\u0065\u0072\u0061\u006e\u0064");
case cmapHexString :_gge =_gaec (_deb );default:return _de .New ("\u0075n\u0065x\u0070\u0065\u0063\u0074\u0065\u0064\u0020\u0074\u0079\u0070\u0065");};var _cfec CharCode ;_ebe ,_gbf =cmap .parseObject ();if _gbf !=nil {if _gbf ==_bf .EOF {break ;};return _gbf ;
};switch _agee :=_ebe .(type ){case cmapOperand :_cea .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a \u0049\u006e\u0063\u006f\u006d\u0070\u006c\u0065\u0074\u0065\u0020\u0062\u0066r\u0061\u006e\u0067\u0065\u0020\u0074\u0072i\u0070\u006c\u0065\u0074");
return ErrBadCMap ;case cmapHexString :_cfec =_gaec (_agee );if _cfec > 0xffff{_cfec =0xffff;};default:_cea .Log .Debug ("\u0045R\u0052\u004f\u0052\u003a \u0055\u006e\u0065\u0078\u0070e\u0063t\u0065d\u0020\u0074\u0079\u0070\u0065\u0020\u0025T",_ebe );return ErrBadCMap ;
};_ebe ,_gbf =cmap .parseObject ();if _gbf !=nil {if _gbf ==_bf .EOF {break ;};return _gbf ;};switch _bced :=_ebe .(type ){case cmapArray :if len (_bced .Array )!=int (_cfec -_gge )+1{_cea .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u0049\u006e\u0076\u0061\u006c\u0069d\u0020\u006e\u0075\u006d\u0062\u0065r\u0020\u006f\u0066\u0020\u0069\u0074\u0065\u006d\u0073\u0020\u0069\u006e\u0020a\u0072\u0072\u0061\u0079");
return ErrBadCMap ;};for _fga :=_gge ;_fga <=_cfec ;_fga ++{_dea :=_bced .Array [_fga -_gge ];_dge ,_cce :=_dea .(cmapHexString );if !_cce {return _de .New ("\u006e\u006f\u006e-h\u0065\u0078\u0020\u0073\u0074\u0072\u0069\u006e\u0067\u0020\u0069\u006e\u0020\u0061\u0072\u0072\u0061\u0079");
};_fegd :=_ccca (_dge );cmap ._ef [_fga ]=string (_fegd );};case cmapHexString :_cdbe :=_ccca (_bced );_gad :=len (_cdbe );for _gde :=_gge ;_gde <=_cfec ;_gde ++{cmap ._ef [_gde ]=string (_cdbe );if _gad > 0{_cdbe [_gad -1]++;}else {_cea .Log .Debug ("\u004e\u006f\u0020c\u006d\u0061\u0070\u0020\u0074\u0061\u0072\u0067\u0065\u0074\u0020\u0063\u0068\u0061\u0072\u0061\u0063\u0074\u0065\u0072\u0020\u0073\u0070\u0065\u0063\u0069\u0066\u0069\u0065d\u0020\u0066\u006f\u0072\u0020\u0025\u0023\u0076",_gde );
};if _gde ==1<<32-1{break ;};};default:_cea .Log .Debug ("\u0045R\u0052\u004f\u0052\u003a \u0055\u006e\u0065\u0078\u0070e\u0063t\u0065d\u0020\u0074\u0079\u0070\u0065\u0020\u0025T",_ebe );return ErrBadCMap ;};};return nil ;};type cmapString struct{String string ;
};func (cmap *CMap )parseType ()error {_egf :=0;_bae :=false ;for _bbg :=0;_bbg < 3&&!_bae ;_bbg ++{_dgbd ,_gdg :=cmap .parseObject ();if _gdg !=nil {return _gdg ;};switch _dgg :=_dgbd .(type ){case cmapOperand :switch _dgg .Operand {case "\u0064\u0065\u0066":_bae =true ;
default:_cea .Log .Error ("\u0070\u0061r\u0073\u0065\u0054\u0079\u0070\u0065\u003a\u0020\u0073\u0074\u0061\u0074\u0065\u0020\u0065\u0072\u0072\u006f\u0072\u002e\u0020\u006f=%\u0023\u0076",_dgbd );return ErrBadCMap ;};case cmapInt :_egf =int (_dgg ._cgcg );
};};cmap ._cef =_egf ;return nil ;};func NewCIDSystemInfo (obj _cc .PdfObject )(_aa CIDSystemInfo ,_ag error ){_fe ,_cd :=_cc .GetDict (obj );if !_cd {return CIDSystemInfo {},_cc .ErrTypeError ;};_cde ,_cd :=_cc .GetStringVal (_fe .Get ("\u0052\u0065\u0067\u0069\u0073\u0074\u0072\u0079"));
if !_cd {return CIDSystemInfo {},_cc .ErrTypeError ;};_def ,_cd :=_cc .GetStringVal (_fe .Get ("\u004f\u0072\u0064\u0065\u0072\u0069\u006e\u0067"));if !_cd {return CIDSystemInfo {},_cc .ErrTypeError ;};_bd ,_cd :=_cc .GetIntVal (_fe .Get ("\u0053\u0075\u0070\u0070\u006c\u0065\u006d\u0065\u006e\u0074"));
if !_cd {return CIDSystemInfo {},_cc .ErrTypeError ;};return CIDSystemInfo {Registry :_cde ,Ordering :_def ,Supplement :_bd },nil ;};func _bgg (_dbc []byte )*cMapParser {_eegc :=cMapParser {};_eddb :=_d .NewBuffer (_dbc );_eegc ._dcbe =_a .NewReader (_eddb );
return &_eegc ;};func (cmap *CMap )parseSystemInfo ()error {_cfd :=false ;_db :=false ;_fgc :="";_bdf :=false ;_cgfa :=CIDSystemInfo {};for _ecgb :=0;_ecgb < 50&&!_bdf ;_ecgb ++{_cga ,_acfb :=cmap .parseObject ();if _acfb !=nil {return _acfb ;};switch _bbd :=_cga .(type ){case cmapDict :_aec :=_bbd .Dict ;
_ccc ,_dac :=_aec ["\u0052\u0065\u0067\u0069\u0073\u0074\u0072\u0079"];if !_dac {_cea .Log .Debug ("\u0045\u0052\u0052\u004fR:\u0020\u0042\u0061\u0064\u0020\u0053\u0079\u0073\u0074\u0065\u006d\u0020\u0049\u006ef\u006f");return ErrBadCMap ;};_gcf ,_dac :=_ccc .(cmapString );
if !_dac {_cea .Log .Debug ("\u0045\u0052\u0052\u004fR:\u0020\u0042\u0061\u0064\u0020\u0053\u0079\u0073\u0074\u0065\u006d\u0020\u0049\u006ef\u006f");return ErrBadCMap ;};_cgfa .Registry =_gcf .String ;_ccc ,_dac =_aec ["\u004f\u0072\u0064\u0065\u0072\u0069\u006e\u0067"];
if !_dac {_cea .Log .Debug ("\u0045\u0052\u0052\u004fR:\u0020\u0042\u0061\u0064\u0020\u0053\u0079\u0073\u0074\u0065\u006d\u0020\u0049\u006ef\u006f");return ErrBadCMap ;};_gcf ,_dac =_ccc .(cmapString );if !_dac {_cea .Log .Debug ("\u0045\u0052\u0052\u004fR:\u0020\u0042\u0061\u0064\u0020\u0053\u0079\u0073\u0074\u0065\u006d\u0020\u0049\u006ef\u006f");
return ErrBadCMap ;};_cgfa .Ordering =_gcf .String ;_ebd ,_dac :=_aec ["\u0053\u0075\u0070\u0070\u006c\u0065\u006d\u0065\u006e\u0074"];if !_dac {_cea .Log .Debug ("\u0045\u0052\u0052\u004fR:\u0020\u0042\u0061\u0064\u0020\u0053\u0079\u0073\u0074\u0065\u006d\u0020\u0049\u006ef\u006f");
return ErrBadCMap ;};_gcbd ,_dac :=_ebd .(cmapInt );if !_dac {_cea .Log .Debug ("\u0045\u0052\u0052\u004fR:\u0020\u0042\u0061\u0064\u0020\u0053\u0079\u0073\u0074\u0065\u006d\u0020\u0049\u006ef\u006f");return ErrBadCMap ;};_cgfa .Supplement =int (_gcbd ._cgcg );
_bdf =true ;case cmapOperand :switch _bbd .Operand {case "\u0062\u0065\u0067i\u006e":_cfd =true ;case "\u0065\u006e\u0064":_bdf =true ;case "\u0064\u0065\u0066":_db =false ;};case cmapName :if _cfd {_fgc =_bbd .Name ;_db =true ;};case cmapString :if _db {switch _fgc {case "\u0052\u0065\u0067\u0069\u0073\u0074\u0072\u0079":_cgfa .Registry =_bbd .String ;
case "\u004f\u0072\u0064\u0065\u0072\u0069\u006e\u0067":_cgfa .Ordering =_bbd .String ;};};case cmapInt :if _db {switch _fgc {case "\u0053\u0075\u0070\u0070\u006c\u0065\u006d\u0065\u006e\u0074":_cgfa .Supplement =int (_bbd ._cgcg );};};};};if !_bdf {_cea .Log .Debug ("\u0045\u0052\u0052O\u0052\u003a\u0020\u0050\u0061\u0072\u0073\u0065\u0064\u0020\u0053\u0079\u0073\u0074\u0065\u006d\u0020\u0049\u006e\u0066\u006f\u0020\u0064\u0069\u0063\u0074\u0020\u0069\u006ec\u006f\u0072\u0072\u0065\u0063\u0074\u006c\u0079");
return ErrBadCMap ;};cmap ._bdb =_cgfa ;return nil ;};type charRange struct{_dg CharCode ;_ad CharCode ;};func (_ebfg *cMapParser )parseArray ()(cmapArray ,error ){_acfa :=cmapArray {};_acfa .Array =[]cmapObject {};_ebfg ._dcbe .ReadByte ();for {_ebfg .skipSpaces ();
_efff ,_ccb :=_ebfg ._dcbe .Peek (1);if _ccb !=nil {return _acfa ,_ccb ;};if _efff [0]==']'{_ebfg ._dcbe .ReadByte ();break ;};_cec ,_ccb :=_ebfg .parseObject ();if _ccb !=nil {return _acfa ,_ccb ;};_acfa .Array =append (_acfa .Array ,_cec );};return _acfa ,nil ;
};func (cmap *CMap )matchCode (_eed []byte )(_fgf CharCode ,_acd int ,_ceaa bool ){for _dgb :=0;_dgb < _bcf ;_dgb ++{if _dgb < len (_eed ){_fgf =_fgf <<8|CharCode (_eed [_dgb ]);_acd ++;};_ceaa =cmap .inCodespace (_fgf ,_dgb +1);if _ceaa {return _fgf ,_acd ,true ;
};};_cea .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u004e\u006f\u0020\u0063o\u0064\u0065\u0073\u0070\u0061\u0063\u0065\u0020m\u0061t\u0063\u0068\u0065\u0073\u0020\u0062\u0079\u0074\u0065\u0073\u003d\u005b\u0025\u0020\u0030\u0032\u0078\u005d=\u0025\u0023\u0071\u0020\u0063\u006d\u0061\u0070\u003d\u0025\u0073",_eed ,string (_eed ),cmap );
return 0,0,false ;};func (cmap *CMap )StringToCID (s string )(CharCode ,bool ){_dcg ,_ee :=cmap ._fgb [s ];return _dcg ,_ee };func (_dfea *cMapParser )parseComment ()(string ,error ){var _eag _d .Buffer ;_ ,_fegc :=_dfea .skipSpaces ();if _fegc !=nil {return _eag .String (),_fegc ;
};_gabg :=true ;for {_aebc ,_edgf :=_dfea ._dcbe .Peek (1);if _edgf !=nil {_cea .Log .Debug ("p\u0061r\u0073\u0065\u0043\u006f\u006d\u006d\u0065\u006et\u003a\u0020\u0065\u0072r=\u0025\u0076",_edgf );return _eag .String (),_edgf ;};if _gabg &&_aebc [0]!='%'{return _eag .String (),ErrBadCMapComment ;
};_gabg =false ;if (_aebc [0]!='\r')&&(_aebc [0]!='\n'){_egd ,_ :=_dfea ._dcbe .ReadByte ();_eag .WriteByte (_egd );}else {break ;};};return _eag .String (),nil ;};type cmapName struct{Name string ;};func _bac (_fba string )rune {_gdf :=[]rune (_fba );
return _gdf [len (_gdf )-1]};func (cmap *CMap )Type ()int {return cmap ._cef };func (cmap *CMap )BytesToCharcodes (data []byte )([]CharCode ,bool ){var _fea []CharCode ;if cmap ._bfc ==8{for _ ,_dfg :=range data {_fea =append (_fea ,CharCode (_dfg ));};
return _fea ,true ;};for _faa :=0;_faa < len (data );{_fbd ,_bgd ,_ade :=cmap .matchCode (data [_faa :]);if !_ade {_cea .Log .Debug ("\u0045\u0052R\u004f\u0052\u003a\u0020\u004e\u006f\u0020\u0063\u006f\u0064\u0065\u0020\u006d\u0061\u0074\u0063\u0068\u0020\u0061\u0074\u0020\u0069\u003d\u0025\u0064\u0020\u0062\u0079\u0074\u0065\u0073\u003d\u005b\u0025\u0020\u0030\u0032\u0078\u005d\u003d\u0025\u0023\u0071",_faa ,data ,string (data ));
return _fea ,false ;};_fea =append (_fea ,_fbd );_faa +=_bgd ;};return _fea ,true ;};func (_ccdc *cMapParser )parseHexString ()(cmapHexString ,error ){_ccdc ._dcbe .ReadByte ();_geg :=[]byte ("\u0030\u0031\u0032\u003345\u0036\u0037\u0038\u0039\u0061\u0062\u0063\u0064\u0065\u0066\u0041\u0042\u0043\u0044E\u0046");
_dag :=_d .Buffer {};for {_ccdc .skipSpaces ();_gea ,_fcde :=_ccdc ._dcbe .Peek (1);if _fcde !=nil {return cmapHexString {},_fcde ;};if _gea [0]=='>'{_ccdc ._dcbe .ReadByte ();break ;};_gdbd ,_ :=_ccdc ._dcbe .ReadByte ();if _d .IndexByte (_geg ,_gdbd )>=0{_dag .WriteByte (_gdbd );
};};if _dag .Len ()%2==1{_cea .Log .Debug ("\u0070\u0061rs\u0065\u0048\u0065x\u0053\u0074\u0072\u0069ng:\u0020ap\u0070\u0065\u006e\u0064\u0069\u006e\u0067 '\u0030\u0027\u0020\u0074\u006f\u0020\u0025#\u0071",_dag .String ());_dag .WriteByte ('0');};_ddag :=_dag .Len ()/2;
_ebg ,_ :=_g .DecodeString (_dag .String ());return cmapHexString {_efef :_ddag ,_cgad :_ebg },nil ;};const (_acf =100;_dfdc ="\u000a\u002f\u0043\u0049\u0044\u0049\u006e\u0069\u0074\u0020\u002f\u0050\u0072\u006fc\u0053\u0065\u0074\u0020\u0066\u0069\u006e\u0064\u0072es\u006fu\u0072c\u0065 \u0062\u0065\u0067\u0069\u006e\u000a\u0031\u0032\u0020\u0064\u0069\u0063\u0074\u0020\u0062\u0065\u0067\u0069n\u000a\u0062\u0065\u0067\u0069\u006e\u0063\u006d\u0061\u0070\n\u002f\u0043\u0049\u0044\u0053\u0079\u0073\u0074\u0065m\u0049\u006e\u0066\u006f\u0020\u003c\u003c\u0020\u002f\u0052\u0065\u0067\u0069\u0073t\u0072\u0079\u0020\u0028\u0041\u0064\u006f\u0062\u0065\u0029\u0020\u002f\u004f\u0072\u0064\u0065\u0072\u0069\u006e\u0067\u0020\u0028\u0055\u0043\u0053)\u0020\u002f\u0053\u0075\u0070p\u006c\u0065\u006d\u0065\u006et\u0020\u0030\u0020\u003e\u003e\u0020\u0064\u0065\u0066\u000a\u002f\u0043\u004d\u0061\u0070\u004e\u0061\u006d\u0065\u0020\u002f\u0041\u0064\u006f\u0062\u0065-\u0049\u0064\u0065\u006e\u0074\u0069\u0074\u0079\u002d\u0055\u0043\u0053\u0020\u0064\u0065\u0066\u000a\u002fC\u004d\u0061\u0070\u0054\u0079\u0070\u0065\u0020\u0032\u0020\u0064\u0065\u0066\u000a\u0031\u0020\u0062\u0065\u0067\u0069\u006e\u0063\u006f\u0064\u0065\u0073\u0070\u0061\u0063e\u0072\u0061n\u0067\u0065\n\u003c\u0030\u0030\u0030\u0030\u003e\u0020<\u0046\u0046\u0046\u0046\u003e\u000a\u0065\u006e\u0064\u0063\u006f\u0064\u0065\u0073\u0070\u0061\u0063\u0065r\u0061\u006e\u0067\u0065\u000a";
_fge ="\u0065\u006e\u0064\u0063\u006d\u0061\u0070\u000a\u0043\u004d\u0061\u0070\u004e\u0061\u006d\u0065\u0020\u0063ur\u0072e\u006e\u0074\u0064\u0069\u0063\u0074\u0020\u002f\u0043\u004d\u0061\u0070 \u0064\u0065\u0066\u0069\u006e\u0065\u0072\u0065\u0073\u006f\u0075\u0072\u0063\u0065\u0020\u0070\u006fp\u000a\u0065\u006e\u0064\u000a\u0065\u006e\u0064\u000a";
);type cmapArray struct{Array []cmapObject ;};func _ccd (_gg bool )*CMap {_fa :=16;if _gg {_fa =8;};return &CMap {_bfc :_fa ,_ff :make (map[CharCode ]CharCode ),_gf :make (map[CharCode ]CharCode ),_ef :make (map[CharCode ]string ),_fgb :make (map[string ]CharCode )};
};func _edcg (_fgg cmapHexString )rune {_bgc :=_ccca (_fgg );if _eebf :=len (_bgc );_eebf ==0{_cea .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u0068\u0065\u0078\u0054o\u0052\u0075\u006e\u0065\u002e\u0020\u0045\u0078p\u0065c\u0074\u0065\u0064\u0020\u0061\u0074\u0020\u006c\u0065\u0061\u0073\u0074\u0020\u006f\u006e\u0065\u0020\u0072u\u006e\u0065\u0020\u0073\u0068\u0065\u0078\u003d\u0025\u0023\u0076",_fgg );
return MissingCodeRune ;};if len (_bgc )> 1{_cea .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u0068\u0065\u0078\u0054\u006f\u0052\u0075\u006e\u0065\u002e\u0020\u0045\u0078p\u0065\u0063\u0074\u0065\u0064\u0020\u0065\u0078\u0061\u0063\u0074\u006c\u0079\u0020\u006f\u006e\u0065\u0020\u0072\u0075\u006e\u0065\u0020\u0073\u0068\u0065\u0078\u003d\u0025\u0023v\u0020\u002d\u003e\u0020\u0025#\u0076",_fgg ,_bgc );
};return _bgc [0];};func IsPredefinedCMap (name string )bool {return _f .AssetExists (name )};func _aeb (_bgf string )string {_cbb :=[]rune (_bgf );_cab :=make ([]string ,len (_cbb ));for _dgbf ,_agcd :=range _cbb {_cab [_dgbf ]=_b .Sprintf ("\u0025\u0030\u0034\u0078",_agcd );
};return _b .Sprintf ("\u003c\u0025\u0073\u003e",_bc .Join (_cab ,""));};func LoadCmapFromDataCID (data []byte )(*CMap ,error ){return LoadCmapFromData (data ,false )};func (_ffe *cMapParser )parseDict ()(cmapDict ,error ){_cea .Log .Trace ("\u0052\u0065\u0061\u0064\u0069\u006e\u0067\u0020\u0050\u0044\u0046\u0020D\u0069\u0063\u0074\u0021");
_fcf :=_fda ();_cfb ,_ :=_ffe ._dcbe .ReadByte ();if _cfb !='<'{return _fcf ,ErrBadCMapDict ;};_cfb ,_ =_ffe ._dcbe .ReadByte ();if _cfb !='<'{return _fcf ,ErrBadCMapDict ;};for {_ffe .skipSpaces ();_gaf ,_aaca :=_ffe ._dcbe .Peek (2);if _aaca !=nil {return _fcf ,_aaca ;
};if (_gaf [0]=='>')&&(_gaf [1]=='>'){_ffe ._dcbe .ReadByte ();_ffe ._dcbe .ReadByte ();break ;};_ced ,_aaca :=_ffe .parseName ();_cea .Log .Trace ("\u004be\u0079\u003a\u0020\u0025\u0073",_ced .Name );if _aaca !=nil {_cea .Log .Debug ("\u0045\u0052R\u004f\u0052\u003a\u0020\u0052\u0065\u0074\u0075\u0072\u006e\u0069\u006e\u0067\u0020\u006e\u0061\u006d\u0065\u002e\u0020\u0065\u0072r=\u0025\u0076",_aaca );
return _fcf ,_aaca ;};_ffe .skipSpaces ();_acdc ,_aaca :=_ffe .parseObject ();if _aaca !=nil {return _fcf ,_aaca ;};_fcf .Dict [_ced .Name ]=_acdc ;_ffe .skipSpaces ();_gaf ,_aaca =_ffe ._dcbe .Peek (3);if _aaca !=nil {return _fcf ,_aaca ;};if string (_gaf )=="\u0064\u0065\u0066"{_ffe ._dcbe .Discard (3);
};};return _fcf ,nil ;};func (_cag *cMapParser )parseNumber ()(cmapObject ,error ){_eaa ,_gcg :=_cc .ParseNumber (_cag ._dcbe );if _gcg !=nil {return nil ,_gcg ;};switch _becb :=_eaa .(type ){case *_cc .PdfObjectFloat :return cmapFloat {float64 (*_becb )},nil ;
case *_cc .PdfObjectInteger :return cmapInt {int64 (*_becb )},nil ;};return nil ,_b .Errorf ("\u0075n\u0068\u0061\u006e\u0064\u006c\u0065\u0064\u0020\u006e\u0075\u006db\u0065\u0072\u0020\u0074\u0079\u0070\u0065\u0020\u0025\u0054",_eaa );};func (cmap *CMap )Name ()string {return cmap ._cb };
type integer struct{_dfgb bool ;_cgfd int ;};func (_dbg *cMapParser )parseName ()(cmapName ,error ){_eafb :="";_aga :=false ;for {_bba ,_bbbe :=_dbg ._dcbe .Peek (1);if _bbbe ==_bf .EOF {break ;};if _bbbe !=nil {return cmapName {_eafb },_bbbe ;};if !_aga {if _bba [0]=='/'{_aga =true ;
_dbg ._dcbe .ReadByte ();}else {_cea .Log .Debug ("\u0045\u0052\u0052OR\u003a\u0020\u004e\u0061\u006d\u0065\u0020\u0073\u0074a\u0072t\u0069n\u0067 \u0077\u0069\u0074\u0068\u0020\u0025\u0073\u0020\u0028\u0025\u0020\u0078\u0029",_bba ,_bba );return cmapName {_eafb },_b .Errorf ("\u0069n\u0076a\u006c\u0069\u0064\u0020\u006ea\u006d\u0065:\u0020\u0028\u0025\u0063\u0029",_bba [0]);
};}else {if _cc .IsWhiteSpace (_bba [0]){break ;}else if (_bba [0]=='/')||(_bba [0]=='[')||(_bba [0]=='(')||(_bba [0]==']')||(_bba [0]=='<')||(_bba [0]=='>'){break ;}else if _bba [0]=='#'{_fgce ,_gga :=_dbg ._dcbe .Peek (3);if _gga !=nil {return cmapName {_eafb },_gga ;
};_dbg ._dcbe .Discard (3);_eddf ,_gga :=_g .DecodeString (string (_fgce [1:3]));if _gga !=nil {return cmapName {_eafb },_gga ;};_eafb +=string (_eddf );}else {_gef ,_ :=_dbg ._dcbe .ReadByte ();_eafb +=string (_gef );};};};return cmapName {_eafb },nil ;
};const (_bcf =4;MissingCodeRune ='\ufffd';MissingCodeString =string (MissingCodeRune ););func _gaec (_gec cmapHexString )CharCode {_bfbc :=CharCode (0);for _ ,_ffff :=range _gec ._cgad {_bfbc <<=8;_bfbc |=CharCode (_ffff );};return _bfbc ;};func (cmap *CMap )Stream ()(*_cc .PdfObjectStream ,error ){if cmap ._adg !=nil {return cmap ._adg ,nil ;
};_eff ,_gae :=_cc .MakeStream (cmap .Bytes (),_cc .NewFlateEncoder ());if _gae !=nil {return nil ,_gae ;};cmap ._adg =_eff ;return cmap ._adg ,nil ;};func (cmap *CMap )parseBfchar ()error {for {_dfgfe ,_agf :=cmap .parseObject ();if _agf !=nil {if _agf ==_bf .EOF {break ;
};return _agf ;};var _aece CharCode ;switch _eda :=_dfgfe .(type ){case cmapOperand :if _eda .Operand ==_efd {return nil ;};return _de .New ("\u0075n\u0065x\u0070\u0065\u0063\u0074\u0065d\u0020\u006fp\u0065\u0072\u0061\u006e\u0064");case cmapHexString :_aece =_gaec (_eda );
default:return _de .New ("\u0075n\u0065x\u0070\u0065\u0063\u0074\u0065\u0064\u0020\u0074\u0079\u0070\u0065");};_dfgfe ,_agf =cmap .parseObject ();if _agf !=nil {if _agf ==_bf .EOF {break ;};return _agf ;};var _bee []rune ;switch _fbac :=_dfgfe .(type ){case cmapOperand :if _fbac .Operand ==_efd {return nil ;
};_cea .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u0055\u006e\u0065x\u0070\u0065\u0063\u0074\u0065\u0064\u0020o\u0070\u0065\u0072\u0061\u006e\u0064\u002e\u0020\u0025\u0023\u0076",_fbac );return ErrBadCMap ;case cmapHexString :_bee =_ccca (_fbac );
case cmapName :_cea .Log .Debug ("E\u0052\u0052\u004f\u0052\u003a\u0020U\u006e\u0065\u0078\u0070\u0065\u0063\u0074\u0065\u0064 \u006e\u0061\u006de\u002e \u0025\u0023\u0076",_fbac );_bee =[]rune {MissingCodeRune };default:_cea .Log .Debug ("E\u0052\u0052\u004f\u0052\u003a\u0020U\u006e\u0065\u0078\u0070\u0065\u0063\u0074\u0065\u0064 \u0074\u0079\u0070e\u002e \u0025\u0023\u0076",_dfgfe );
return ErrBadCMap ;};cmap ._ef [_aece ]=string (_bee );};return nil ;};type cmapObject interface{};func (_cccb *cMapParser )parseString ()(cmapString ,error ){_cccb ._dcbe .ReadByte ();_acb :=_d .Buffer {};_egedg :=1;for {_gcd ,_ggcb :=_cccb ._dcbe .Peek (1);
if _ggcb !=nil {return cmapString {_acb .String ()},_ggcb ;};if _gcd [0]=='\\'{_cccb ._dcbe .ReadByte ();_ggbf ,_dad :=_cccb ._dcbe .ReadByte ();if _dad !=nil {return cmapString {_acb .String ()},_dad ;};if _cc .IsOctalDigit (_ggbf ){_bdgb ,_gdea :=_cccb ._dcbe .Peek (2);
if _gdea !=nil {return cmapString {_acb .String ()},_gdea ;};var _fbb []byte ;_fbb =append (_fbb ,_ggbf );for _ ,_egede :=range _bdgb {if _cc .IsOctalDigit (_egede ){_fbb =append (_fbb ,_egede );}else {break ;};};_cccb ._dcbe .Discard (len (_fbb )-1);_cea .Log .Trace ("\u004e\u0075\u006d\u0065ri\u0063\u0020\u0073\u0074\u0072\u0069\u006e\u0067\u0020\u0022\u0025\u0073\u0022",_fbb );
_dff ,_gdea :=_ce .ParseUint (string (_fbb ),8,32);if _gdea !=nil {return cmapString {_acb .String ()},_gdea ;};_acb .WriteByte (byte (_dff ));continue ;};switch _ggbf {case 'n':_acb .WriteByte ('\n');case 'r':_acb .WriteByte ('\r');case 't':_acb .WriteByte ('\t');
case 'b':_acb .WriteByte ('\b');case 'f':_acb .WriteByte ('\f');case '(':_acb .WriteByte ('(');case ')':_acb .WriteByte (')');case '\\':_acb .WriteByte ('\\');};continue ;}else if _gcd [0]=='('{_egedg ++;}else if _gcd [0]==')'{_egedg --;if _egedg ==0{_cccb ._dcbe .ReadByte ();
break ;};};_ada ,_ :=_cccb ._dcbe .ReadByte ();_acb .WriteByte (_ada );};return cmapString {_acb .String ()},nil ;};func _ccca (_fgcf cmapHexString )[]rune {if len (_fgcf ._cgad )==1{return []rune {rune (_fgcf ._cgad [0])};};_deag :=_fgcf ._cgad ;if len (_deag )%2!=0{_deag =append (_deag ,0);
_cea .Log .Debug ("\u0045\u0052\u0052O\u0052\u003a\u0020\u0068\u0065\u0078\u0054\u006f\u0052\u0075\u006e\u0065\u0073\u002e\u0020\u0050\u0061\u0064\u0064\u0069\u006e\u0067\u0020\u0073\u0068\u0065\u0078\u003d\u0025#\u0076\u0020\u0074\u006f\u0020\u0025\u002b\u0076",_fgcf ,_deag );
};_afg :=len (_deag )>>1;_dbf :=make ([]uint16 ,_afg );for _fbde :=0;_fbde < _afg ;_fbde ++{_dbf [_fbde ]=uint16 (_deag [_fbde <<1])<<8+uint16 (_deag [_fbde <<1+1]);};_bace :=_e .Decode (_dbf );return _bace ;};func (cmap *CMap )parseWMode ()error {var _gff int ;
_dgfg :=false ;for _bce :=0;_bce < 3&&!_dgfg ;_bce ++{_afa ,_gda :=cmap .parseObject ();if _gda !=nil {return _gda ;};switch _cfg :=_afa .(type ){case cmapOperand :switch _cfg .Operand {case "\u0064\u0065\u0066":_dgfg =true ;default:_cea .Log .Error ("\u0070\u0061\u0072\u0073\u0065\u0057\u004d\u006f\u0064\u0065:\u0020\u0073\u0074\u0061\u0074\u0065\u0020e\u0072\u0072\u006f\u0072\u002e\u0020\u006f\u003d\u0025\u0023\u0076",_afa );
return ErrBadCMap ;};case cmapInt :_gff =int (_cfg ._cgcg );};};cmap ._cg =integer {_dfgb :true ,_cgfd :_gff };return nil ;};func NewToUnicodeCMap (codeToRune map[CharCode ]rune )*CMap {_cf :=make (map[CharCode ]string ,len (codeToRune ));for _dd ,_fb :=range codeToRune {_cf [_dd ]=string (_fb );
};cmap :=&CMap {_cb :"\u0041d\u006fb\u0065\u002d\u0049\u0064\u0065n\u0074\u0069t\u0079\u002d\u0055\u0043\u0053",_cef :2,_bfc :16,_bdb :CIDSystemInfo {Registry :"\u0041\u0064\u006fb\u0065",Ordering :"\u0055\u0043\u0053",Supplement :0},_bb :[]Codespace {{Low :0,High :0xffff}},_ef :_cf ,_fgb :make (map[string ]CharCode ,len (codeToRune )),_ff :make (map[CharCode ]CharCode ,len (codeToRune )),_gf :make (map[CharCode ]CharCode ,len (codeToRune ))};
cmap .computeInverseMappings ();return cmap ;};type cmapInt struct{_cgcg int64 };