//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package bitwise ;import (_b "encoding/binary";_a "errors";_c "fmt";_g "github.com/unidoc/unipdf/v3/common";_dff "github.com/unidoc/unipdf/v3/internal/jbig2/errors";_df "io";);func (_eb *BufferedWriter )writeByte (_gee byte ){switch {case _eb ._gb ==0:_eb ._e [_eb ._cg ]=_gee ;
_eb ._cg ++;case _eb ._ee :_eb ._e [_eb ._cg ]|=_gee >>_eb ._gb ;_eb ._cg ++;_eb ._e [_eb ._cg ]=byte (uint16 (_gee )<<(8-_eb ._gb )&0xff);default:_eb ._e [_eb ._cg ]|=byte (uint16 (_gee )<<_eb ._gb &0xff);_eb ._cg ++;_eb ._e [_eb ._cg ]=_gee >>(8-_eb ._gb );
};};func NewWriterMSB (data []byte )*Writer {return &Writer {_cae :data ,_gcc :true }};func (_edg *Reader )Seek (offset int64 ,whence int )(int64 ,error ){_edg ._dec =-1;_edg ._geb =0;_edg ._feb =0;_edg ._cbg =0;var _gbg int64 ;switch whence {case _df .SeekStart :_gbg =offset ;
case _df .SeekCurrent :_gbg =_edg ._gbe +offset ;case _df .SeekEnd :_gbg =int64 (_edg ._afa ._fc )+offset ;default:return 0,_a .New ("\u0072\u0065\u0061de\u0072\u002e\u0052\u0065\u0061\u0064\u0065\u0072\u002eS\u0065e\u006b:\u0020i\u006e\u0076\u0061\u006c\u0069\u0064\u0020\u0077\u0068\u0065\u006e\u0063\u0065");
};if _gbg < 0{return 0,_a .New ("\u0072\u0065a\u0064\u0065\u0072\u002eR\u0065\u0061d\u0065\u0072\u002e\u0053\u0065\u0065\u006b\u003a \u006e\u0065\u0067\u0061\u0074\u0069\u0076\u0065\u0020\u0070\u006f\u0073i\u0074\u0069\u006f\u006e");};_edg ._gbe =_gbg ;
_edg ._geb =0;return _gbg ,nil ;};type StreamReader interface{_df .Reader ;_df .ByteReader ;_df .Seeker ;Align ()byte ;BitPosition ()int ;Mark ();Length ()uint64 ;ReadBit ()(int ,error );ReadBits (_ddff byte )(uint64 ,error );ReadBool ()(bool ,error );
ReadUint32 ()(uint32 ,error );Reset ();AbsolutePosition ()int64 ;};func (_fac *Reader )readBool ()(_febe bool ,_bca error ){if _fac ._geb ==0{_fac ._feb ,_bca =_fac .readBufferByte ();if _bca !=nil {return false ,_bca ;};_febe =(_fac ._feb &0x80)!=0;_fac ._feb ,_fac ._geb =_fac ._feb &0x7f,7;
return _febe ,nil ;};_fac ._geb --;_febe =(_fac ._feb &(1<<_fac ._geb ))!=0;_fac ._feb &=1<<_fac ._geb -1;return _febe ,nil ;};func (_fb *BufferedWriter )Write (d []byte )(int ,error ){_fb .expandIfNeeded (len (d ));if _fb ._gb ==0{return _fb .writeFullBytes (d ),nil ;
};return _fb .writeShiftedBytes (d ),nil ;};func (_debb *Reader )read (_aec []byte )(int ,error ){if _debb ._gbe >=int64 (_debb ._afa ._fc ){return 0,_df .EOF ;};_debb ._dec =-1;_edf :=copy (_aec ,_debb ._afa ._bde [(int64 (_debb ._afa ._eg )+_debb ._gbe ):(_debb ._afa ._eg +_debb ._afa ._fc )]);
_debb ._gbe +=int64 (_edf );return _edf ,nil ;};func (_dgc *Writer )writeBit (_efgd uint8 )error {if len (_dgc ._cae )-1< _dgc ._ecf {return _df .EOF ;};_dgb :=_dgc ._gba ;if _dgc ._gcc {_dgb =7-_dgc ._gba ;};_dgc ._cae [_dgc ._ecf ]|=byte (uint16 (_efgd <<_dgb )&0xff);
_dgc ._gba ++;if _dgc ._gba ==8{_dgc ._ecf ++;_dgc ._gba =0;};return nil ;};func (_eca *BufferedWriter )grow (_ccc int ){if _eca ._e ==nil &&_ccc < _bb {_eca ._e =make ([]byte ,_ccc ,_bb );return ;};_bd :=len (_eca ._e );if _eca ._gb !=0{_bd ++;};_cgde :=cap (_eca ._e );
switch {case _ccc <=_cgde /2-_bd :_g .Log .Trace ("\u005b\u0042\u0075\u0066\u0066\u0065r\u0065\u0064\u0057\u0072\u0069t\u0065\u0072\u005d\u0020\u0067\u0072o\u0077\u0020\u002d\u0020\u0072e\u0073\u006c\u0069\u0063\u0065\u0020\u006f\u006e\u006c\u0079\u002e\u0020L\u0065\u006e\u003a\u0020\u0027\u0025\u0064\u0027\u002c\u0020\u0043\u0061\u0070\u003a\u0020'\u0025\u0064\u0027\u002c\u0020\u006e\u003a\u0020'\u0025\u0064\u0027",len (_eca ._e ),cap (_eca ._e ),_ccc );
_g .Log .Trace ("\u0020\u006e\u0020\u003c\u003d\u0020\u0063\u0020\u002f\u0020\u0032\u0020\u002d\u006d\u002e \u0043:\u0020\u0027\u0025\u0064\u0027\u002c\u0020\u006d\u003a\u0020\u0027\u0025\u0064\u0027",_cgde ,_bd );copy (_eca ._e ,_eca ._e [_eca .fullOffset ():]);
case _cgde > _af -_cgde -_ccc :_g .Log .Error ("\u0042\u0055F\u0046\u0045\u0052 \u0074\u006f\u006f\u0020\u006c\u0061\u0072\u0067\u0065");return ;default:_efg :=make ([]byte ,2*_cgde +_ccc );copy (_efg ,_eca ._e );_eca ._e =_efg ;};_eca ._e =_eca ._e [:_bd +_ccc ];
};func NewReader (data []byte )*Reader {return &Reader {_afa :readerSource {_bde :data ,_fc :len (data ),_eg :0}};};func (_fff *Reader )AbsolutePosition ()int64 {return _fff ._gbe +int64 (_fff ._afa ._eg )};var _ _df .Writer =&BufferedWriter {};func (_bbc *Writer )writeByte (_dffe byte )error {if _bbc ._ecf > len (_bbc ._cae )-1{return _df .EOF ;
};if _bbc ._ecf ==len (_bbc ._cae )-1&&_bbc ._gba !=0{return _df .EOF ;};if _bbc ._gba ==0{_bbc ._cae [_bbc ._ecf ]=_dffe ;_bbc ._ecf ++;return nil ;};if _bbc ._gcc {_bbc ._cae [_bbc ._ecf ]|=_dffe >>_bbc ._gba ;_bbc ._ecf ++;_bbc ._cae [_bbc ._ecf ]=byte (uint16 (_dffe )<<(8-_bbc ._gba )&0xff);
}else {_bbc ._cae [_bbc ._ecf ]|=byte (uint16 (_dffe )<<_bbc ._gba &0xff);_bbc ._ecf ++;_bbc ._cae [_bbc ._ecf ]=_dffe >>(8-_bbc ._gba );};return nil ;};func (_ge *BufferedWriter )Len ()int {return _ge .byteCapacity ()};func (_dd *BufferedWriter )WriteBits (bits uint64 ,number int )(_bf int ,_bfa error ){const _aa ="\u0042u\u0066\u0066\u0065\u0072e\u0064\u0057\u0072\u0069\u0074e\u0072.\u0057r\u0069\u0074\u0065\u0072\u0042\u0069\u0074s";
if number < 0||number > 64{return 0,_dff .Errorf (_aa ,"\u0062i\u0074\u0073 \u006e\u0075\u006db\u0065\u0072\u0020\u006d\u0075\u0073\u0074 \u0062\u0065\u0020\u0069\u006e\u0020r\u0061\u006e\u0067\u0065\u0020\u003c\u0030\u002c\u0036\u0034\u003e,\u0020\u0069\u0073\u003a\u0020\u0027\u0025\u0064\u0027",number );
};_cb :=number /8;if _cb > 0{_fg :=number -_cb *8;for _cf :=_cb -1;_cf >=0;_cf --{_eec :=byte ((bits >>uint (_cf *8+_fg ))&0xff);if _bfa =_dd .WriteByte (_eec );_bfa !=nil {return _bf ,_dff .Wrapf (_bfa ,_aa ,"\u0062\u0079\u0074\u0065\u003a\u0020\u0027\u0025\u0064\u0027",_cb -_cf +1);
};};number -=_cb *8;if number ==0{return _cb ,nil ;};};var _gd int ;for _cbd :=0;_cbd < number ;_cbd ++{if _dd ._ee {_gd =int ((bits >>uint (number -1-_cbd ))&0x1);}else {_gd =int (bits &0x1);bits >>=1;};if _bfa =_dd .WriteBit (_gd );_bfa !=nil {return _bf ,_dff .Wrapf (_bfa ,_aa ,"\u0062i\u0074\u003a\u0020\u0025\u0064",_cbd );
};};return _cb ,nil ;};func (_bc *BufferedWriter )writeFullBytes (_caa []byte )int {_ea :=copy (_bc ._e [_bc .fullOffset ():],_caa );_bc ._cg +=_ea ;return _ea ;};var _ BinaryWriter =&Writer {};func (_cfa *Reader )Align ()(_dc byte ){_dc =_cfa ._geb ;_cfa ._geb =0;
return _dc };func (_gbce *Reader )Read (p []byte )(_fde int ,_ebf error ){if _gbce ._geb ==0{return _gbce .read (p );};for ;_fde < len (p );_fde ++{if p [_fde ],_ebf =_gbce .readUnalignedByte ();_ebf !=nil {return 0,_ebf ;};};return _fde ,nil ;};func (_cfd *BufferedWriter )tryGrowByReslice (_ddf int )bool {if _ggdc :=len (_cfd ._e );
_ddf <=cap (_cfd ._e )-_ggdc {_cfd ._e =_cfd ._e [:_ggdc +_ddf ];return true ;};return false ;};const (_bb =64;_af =int (^uint (0)>>1););func (_ecga *Reader )Mark (){_ecga ._ag =_ecga ._gbe ;_ecga ._efd =_ecga ._geb ;_ecga ._acb =_ecga ._feb ;_ecga ._ae =_ecga ._cbg ;
};func (_gef *Reader )BitPosition ()int {return int (_gef ._geb )};func (_ca *BufferedWriter )FinishByte (){if _ca ._gb ==0{return ;};_ca ._gb =0;_ca ._cg ++;};func (_fdc *Writer )ResetBit (){_fdc ._gba =0};func (_ade *Writer )byteCapacity ()int {_ceb :=len (_ade ._cae )-_ade ._ecf ;
if _ade ._gba !=0{_ceb --;};return _ceb ;};type readerSource struct{_bde []byte ;_eg int ;_fc int ;};func (_bgbd *Reader )ReadByte ()(byte ,error ){if _bgbd ._geb ==0{return _bgbd .readBufferByte ();};return _bgbd .readUnalignedByte ();};func (_bfb *Reader )readUnalignedByte ()(_beb byte ,_deef error ){_gfe :=_bfb ._geb ;
_beb =_bfb ._feb <<(8-_gfe );_bfb ._feb ,_deef =_bfb .readBufferByte ();if _deef !=nil {return 0,_deef ;};_beb |=_bfb ._feb >>_gfe ;_bfb ._feb &=1<<_gfe -1;return _beb ,nil ;};func (_ece *BufferedWriter )fullOffset ()int {_ac :=_ece ._cg ;if _ece ._gb !=0{_ac ++;
};return _ac ;};func (_gbb *BufferedWriter )SkipBits (skip int )error {if skip ==0{return nil ;};_gc :=int (_gbb ._gb )+skip ;if _gc >=0&&_gc < 8{_gbb ._gb =uint8 (_gc );return nil ;};_gc =int (_gbb ._gb )+_gbb ._cg *8+skip ;if _gc < 0{return _dff .Errorf ("\u0057r\u0069t\u0065\u0072\u002e\u0053\u006b\u0069\u0070\u0042\u0069\u0074\u0073","\u0069n\u0064e\u0078\u0020\u006f\u0075\u0074 \u006f\u0066 \u0072\u0061\u006e\u0067\u0065");
};_de :=_gc /8;_ef :=_gc %8;_gbb ._gb =uint8 (_ef );if _ggd :=_de -_gbb ._cg ;_ggd > 0&&len (_gbb ._e )-1< _de {if _gbb ._gb !=0{_ggd ++;};_gbb .expandIfNeeded (_ggd );};_gbb ._cg =_de ;return nil ;};func (_dee *Reader )AbsoluteLength ()uint64 {return uint64 (len (_dee ._afa ._bde ))};
func (_gg *BufferedWriter )ResetBitIndex (){_gg ._gb =0};func (_ec *BufferedWriter )Data ()[]byte {return _ec ._e };func (_abc *Writer )Data ()[]byte {return _abc ._cae };func (_dae *Reader )readBufferByte ()(byte ,error ){if _dae ._gbe >=int64 (_dae ._afa ._fc ){return 0,_df .EOF ;
};_dae ._dec =-1;_cfb :=_dae ._afa ._bde [int64 (_dae ._afa ._eg )+_dae ._gbe ];_dae ._gbe ++;_dae ._cbg =int (_cfb );return _cfb ,nil ;};type BinaryWriter interface{BitWriter ;_df .Writer ;_df .ByteWriter ;Data ()[]byte ;};func (_cge *BufferedWriter )expandIfNeeded (_ff int ){if !_cge .tryGrowByReslice (_ff ){_cge .grow (_ff );
};};type BitWriter interface{WriteBit (_bg int )error ;WriteBits (_bgb uint64 ,_ddd int )(_gdb int ,_fe error );FinishByte ();SkipBits (_gbc int )error ;};func (_ab *BufferedWriter )WriteByte (bt byte )error {if _ab ._cg > len (_ab ._e )-1||(_ab ._cg ==len (_ab ._e )-1&&_ab ._gb !=0){_ab .expandIfNeeded (1);
};_ab .writeByte (bt );return nil ;};type BufferedWriter struct{_e []byte ;_gb uint8 ;_cg int ;_ee bool ;};func (_fea *Reader )ConsumeRemainingBits ()(uint64 ,error ){if _fea ._geb !=0{return _fea .ReadBits (_fea ._geb );};return 0,nil ;};func (_dfg *Reader )ReadUint32 ()(uint32 ,error ){_ce :=make ([]byte ,4);
_ ,_dg :=_dfg .Read (_ce );if _dg !=nil {return 0,_dg ;};return _b .BigEndian .Uint32 (_ce ),nil ;};type Reader struct{_afa readerSource ;_feb byte ;_geb byte ;_gbe int64 ;_cbg int ;_dec int ;_ag int64 ;_efd byte ;_acb byte ;_ae int ;};func (_feg *Reader )ReadBit ()(_acg int ,_eeb error ){_decb ,_eeb :=_feg .readBool ();
if _eeb !=nil {return 0,_eeb ;};if _decb {_acg =1;};return _acg ,nil ;};func (_fgcd *Reader )Length ()uint64 {return uint64 (_fgcd ._afa ._fc )};func (_eaa *Writer )FinishByte (){if _eaa ._gba ==0{return ;};_eaa ._gba =0;_eaa ._ecf ++;};var _ _df .ByteWriter =&BufferedWriter {};
var _ BinaryWriter =&BufferedWriter {};func (_f *BufferedWriter )Reset (){_f ._e =_f ._e [:0];_f ._cg =0;_f ._gb =0};func (_ba *BufferedWriter )WriteBit (bit int )error {if bit !=1&&bit !=0{return _dff .Errorf ("\u0042\u0075\u0066fe\u0072\u0065\u0064\u0057\u0072\u0069\u0074\u0065\u0072\u002e\u0057\u0072\u0069\u0074\u0065\u0042\u0069\u0074","\u0062\u0069\u0074\u0020\u0076\u0061\u006cu\u0065\u0020\u006du\u0073\u0074\u0020\u0062e\u0020\u0069\u006e\u0020\u0072\u0061\u006e\u0067\u0065\u0020\u007b\u0030\u002c\u0031\u007d\u0020\u0062\u0075\u0074\u0020\u0069\u0073\u003a\u0020\u0025\u0064",bit );
};if len (_ba ._e )-1< _ba ._cg {_ba .expandIfNeeded (1);};_fa :=_ba ._gb ;if _ba ._ee {_fa =7-_ba ._gb ;};_ba ._e [_ba ._cg ]|=byte (uint16 (bit <<_fa )&0xff);_ba ._gb ++;if _ba ._gb ==8{_ba ._cg ++;_ba ._gb =0;};return nil ;};func (_be *BufferedWriter )byteCapacity ()int {_fgc :=len (_be ._e )-_be ._cg ;
if _be ._gb !=0{_fgc --;};return _fgc ;};type Writer struct{_cae []byte ;_gba uint8 ;_ecf int ;_gcc bool ;};func (_fd *Reader )NewPartialReader (offset ,length int ,relative bool )(*Reader ,error ){if offset < 0{return nil ,_a .New ("p\u0061\u0072\u0074\u0069\u0061\u006c\u0020\u0072\u0065\u0061\u0064\u0065\u0072\u0020\u006f\u0066\u0066\u0073e\u0074\u0020\u0063\u0061\u006e\u006e\u006f\u0074\u0020\u0062e \u006e\u0065\u0067a\u0074i\u0076\u0065");
};if relative {offset =_fd ._afa ._eg +offset ;};if length > 0{_gdg :=len (_fd ._afa ._bde );if relative {_gdg =_fd ._afa ._fc ;};if offset +length > _gdg {return nil ,_c .Errorf ("\u0070\u0061r\u0074\u0069\u0061l\u0020\u0072\u0065\u0061\u0064e\u0072\u0020\u006f\u0066\u0066se\u0074\u0028\u0025\u0064\u0029\u002b\u006c\u0065\u006e\u0067\u0074\u0068\u0028\u0025\u0064\u0029\u003d\u0025d\u0020i\u0073\u0020\u0067\u0072\u0065\u0061ter\u0020\u0074\u0068\u0061\u006e\u0020\u0074\u0068\u0065\u0020\u006f\u0072ig\u0069n\u0061\u006c\u0020\u0072e\u0061d\u0065r\u0020\u006ce\u006e\u0067th\u003a\u0020\u0025\u0064",offset ,length ,offset +length ,_fd ._afa ._fc );
};};if length < 0{_ebb :=len (_fd ._afa ._bde );if relative {_ebb =_fd ._afa ._fc ;};length =_ebb -offset ;};return &Reader {_afa :readerSource {_bde :_fd ._afa ._bde ,_fc :length ,_eg :offset }},nil ;};func (_bbga *Writer )WriteBits (bits uint64 ,number int )(_fcc int ,_dcb error ){const _daf ="\u0057\u0072\u0069\u0074\u0065\u0072\u002e\u0057\u0072\u0069\u0074\u0065r\u0042\u0069\u0074\u0073";
if number < 0||number > 64{return 0,_dff .Errorf (_daf ,"\u0062i\u0074\u0073 \u006e\u0075\u006db\u0065\u0072\u0020\u006d\u0075\u0073\u0074 \u0062\u0065\u0020\u0069\u006e\u0020r\u0061\u006e\u0067\u0065\u0020\u003c\u0030\u002c\u0036\u0034\u003e,\u0020\u0069\u0073\u003a\u0020\u0027\u0025\u0064\u0027",number );
};if number ==0{return 0,nil ;};_cef :=number /8;if _cef > 0{_caf :=number -_cef *8;for _bcg :=_cef -1;_bcg >=0;_bcg --{_bdf :=byte ((bits >>uint (_bcg *8+_caf ))&0xff);if _dcb =_bbga .WriteByte (_bdf );_dcb !=nil {return _fcc ,_dff .Wrapf (_dcb ,_daf ,"\u0062\u0079\u0074\u0065\u003a\u0020\u0027\u0025\u0064\u0027",_cef -_bcg +1);
};};number -=_cef *8;if number ==0{return _cef ,nil ;};};var _eeg int ;for _dab :=0;_dab < number ;_dab ++{if _bbga ._gcc {_eeg =int ((bits >>uint (number -1-_dab ))&0x1);}else {_eeg =int (bits &0x1);bits >>=1;};if _dcb =_bbga .WriteBit (_eeg );_dcb !=nil {return _fcc ,_dff .Wrapf (_dcb ,_daf ,"\u0062i\u0074\u003a\u0020\u0025\u0064",_dab );
};};return _cef ,nil ;};func BufferedMSB ()*BufferedWriter {return &BufferedWriter {_ee :true }};func (_gbgb *Writer )SkipBits (skip int )error {const _dda ="\u0057r\u0069t\u0065\u0072\u002e\u0053\u006b\u0069\u0070\u0042\u0069\u0074\u0073";if skip ==0{return nil ;
};_aaa :=int (_gbgb ._gba )+skip ;if _aaa >=0&&_aaa < 8{_gbgb ._gba =uint8 (_aaa );return nil ;};_aaa =int (_gbgb ._gba )+_gbgb ._ecf *8+skip ;if _aaa < 0{return _dff .Errorf (_dda ,"\u0069n\u0064e\u0078\u0020\u006f\u0075\u0074 \u006f\u0066 \u0072\u0061\u006e\u0067\u0065");
};_dgf :=_aaa /8;_fdg :=_aaa %8;_g .Log .Trace ("\u0053\u006b\u0069\u0070\u0042\u0069\u0074\u0073");_g .Log .Trace ("\u0042\u0069\u0074\u0049\u006e\u0064\u0065\u0078\u003a\u0020\u0027\u0025\u0064\u0027\u0020\u0042\u0079\u0074\u0065\u0049n\u0064\u0065\u0078\u003a\u0020\u0027\u0025\u0064\u0027\u002c\u0020\u0046\u0075\u006c\u006c\u0042\u0069\u0074\u0073\u003a\u0020'\u0025\u0064\u0027\u002c\u0020\u004c\u0065\u006e\u003a\u0020\u0027\u0025\u0064\u0027,\u0020\u0043\u0061p\u003a\u0020\u0027\u0025\u0064\u0027",_gbgb ._gba ,_gbgb ._ecf ,int (_gbgb ._gba )+(_gbgb ._ecf )*8,len (_gbgb ._cae ),cap (_gbgb ._cae ));
_g .Log .Trace ("S\u006b\u0069\u0070\u003a\u0020\u0027%\u0064\u0027\u002c\u0020\u0064\u003a \u0027\u0025\u0064\u0027\u002c\u0020\u0062i\u0074\u0049\u006e\u0064\u0065\u0078\u003a\u0020\u0027\u0025d\u0027",skip ,_aaa ,_fdg );_gbgb ._gba =uint8 (_fdg );if _cd :=_dgf -_gbgb ._ecf ;
_cd > 0&&len (_gbgb ._cae )-1< _dgf {_g .Log .Trace ("\u0042\u0079\u0074e\u0044\u0069\u0066\u0066\u003a\u0020\u0025\u0064",_cd );return _dff .Errorf (_dda ,"\u0069n\u0064e\u0078\u0020\u006f\u0075\u0074 \u006f\u0066 \u0072\u0061\u006e\u0067\u0065");};_gbgb ._ecf =_dgf ;
_g .Log .Trace ("\u0042\u0069\u0074I\u006e\u0064\u0065\u0078:\u0020\u0027\u0025\u0064\u0027\u002c\u0020B\u0079\u0074\u0065\u0049\u006e\u0064\u0065\u0078\u003a\u0020\u0027\u0025\u0064\u0027",_gbgb ._gba ,_gbgb ._ecf );return nil ;};func NewWriter (data []byte )*Writer {return &Writer {_cae :data }};
func (_cccg *Writer )UseMSB ()bool {return _cccg ._gcc };func (_deb *BufferedWriter )writeShiftedBytes (_bbg []byte )int {for _ ,_bcc :=range _bbg {_deb .writeByte (_bcc );};return len (_bbg );};func (_bgg *Writer )WriteByte (c byte )error {return _bgg .writeByte (c )};
func (_ggc *Reader )Reset (){_ggc ._gbe =_ggc ._ag ;_ggc ._geb =_ggc ._efd ;_ggc ._feb =_ggc ._acb ;_ggc ._cbg =_ggc ._ae ;};var (_ _df .Reader =&Reader {};_ _df .ByteReader =&Reader {};_ _df .Seeker =&Reader {};_ StreamReader =&Reader {};);func (_eae *Writer )WriteBit (bit int )error {switch bit {case 0,1:return _eae .writeBit (uint8 (bit ));
};return _dff .Error ("\u0057\u0072\u0069\u0074\u0065\u0042\u0069\u0074","\u0069\u006e\u0076\u0061\u006c\u0069\u0064\u0020\u0062\u0069\u0074\u0020v\u0061\u006c\u0075\u0065");};func (_gf *Reader )ReadBool ()(bool ,error ){return _gf .readBool ()};func (_bba *Reader )RelativePosition ()int64 {return _bba ._gbe };
func (_decf *Writer )Write (p []byte )(int ,error ){if len (p )> _decf .byteCapacity (){return 0,_df .EOF ;};for _ ,_gbed :=range p {if _febd :=_decf .writeByte (_gbed );_febd !=nil {return 0,_febd ;};};return len (p ),nil ;};func (_gec *Reader )ReadBits (n byte )(_afg uint64 ,_db error ){if n < _gec ._geb {_ed :=_gec ._geb -n ;
_afg =uint64 (_gec ._feb >>_ed );_gec ._feb &=1<<_ed -1;_gec ._geb =_ed ;return _afg ,nil ;};if n > _gec ._geb {if _gec ._geb > 0{_afg =uint64 (_gec ._feb );n -=_gec ._geb ;};for n >=8{_afc ,_da :=_gec .readBufferByte ();if _da !=nil {return 0,_da ;};_afg =_afg <<8+uint64 (_afc );
n -=8;};if n > 0{if _gec ._feb ,_db =_gec .readBufferByte ();_db !=nil {return 0,_db ;};_gcg :=8-n ;_afg =_afg <<n +uint64 (_gec ._feb >>_gcg );_gec ._feb &=1<<_gcg -1;_gec ._geb =_gcg ;}else {_gec ._geb =0;};return _afg ,nil ;};_gec ._geb =0;return uint64 (_gec ._feb ),nil ;
};