//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package transform ;import (_ee "fmt";_ea "github.com/unidoc/unipdf/v3/common";_c "math";);func TranslationMatrix (tx ,ty float64 )Matrix {return NewMatrix (1,0,0,1,tx ,ty )};func (_de Matrix )Mult (b Matrix )Matrix {_de .Concat (b );return _de };const _bc =1e-10;
func (_fae Point )Interpolate (b Point ,t float64 )Point {return Point {X :(1-t )*_fae .X +t *b .X ,Y :(1-t )*_fae .Y +t *b .Y };};func (_bgc *Matrix )Set (a ,b ,c ,d ,tx ,ty float64 ){_bgc [0],_bgc [1]=a ,b ;_bgc [3],_bgc [4]=c ,d ;_bgc [6],_bgc [7]=tx ,ty ;
_bgc .clampRange ();};func (_ceg Matrix )Unrealistic ()bool {_dad ,_bec ,_eea ,_bae :=_c .Abs (_ceg [0]),_c .Abs (_ceg [1]),_c .Abs (_ceg [3]),_c .Abs (_ceg [4]);_eb :=_dad > _ecf &&_bae > _ecf ;_gb :=_bec > _ecf &&_eea > _ecf ;return !(_eb ||_gb );};func ShearMatrix (x ,y float64 )Matrix {return NewMatrix (1,y ,x ,1,0,0)};
func (_a Matrix )Identity ()bool {return _a [0]==1&&_a [1]==0&&_a [2]==0&&_a [3]==0&&_a [4]==1&&_a [5]==0&&_a [6]==0&&_a [7]==0&&_a [8]==1;};func (_bag Matrix )Rotate (theta float64 )Matrix {return _bag .Mult (RotationMatrix (theta ))};func (_ga Matrix )Inverse ()(Matrix ,bool ){_ge ,_eab :=_ga [0],_ga [1];
_ce ,_cad :=_ga [3],_ga [4];_gg ,_cef :=_ga [6],_ga [7];_ff :=_ge *_cad -_eab *_ce ;if _c .Abs (_ff )< _dac {return Matrix {},false ;};_cg ,_da :=_cad /_ff ,-_eab /_ff ;_cbb ,_db :=-_ce /_ff ,_ge /_ff ;_dg :=-(_cg *_gg +_cbb *_cef );_ecc :=-(_da *_gg +_db *_cef );
return NewMatrix (_cg ,_da ,_cbb ,_db ,_dg ,_ecc ),true ;};func (_gc Point )Distance (b Point )float64 {return _c .Hypot (_gc .X -b .X ,_gc .Y -b .Y )};func (_abg Point )String ()string {return _ee .Sprintf ("(\u0025\u002e\u0032\u0066\u002c\u0025\u002e\u0032\u0066\u0029",_abg .X ,_abg .Y );
};func (_bed Matrix )ScalingFactorX ()float64 {return _c .Hypot (_bed [0],_bed [1])};func (_fc Matrix )Angle ()float64 {_g :=_c .Atan2 (-_fc [1],_fc [0]);if _g < 0.0{_g +=2*_c .Pi ;};return _g /_c .Pi *180.0;};func NewMatrixFromTransforms (xScale ,yScale ,theta ,tx ,ty float64 )Matrix {return IdentityMatrix ().Scale (xScale ,yScale ).Rotate (theta ).Translate (tx ,ty );
};const _ecf =1e-6;func (_cb *Matrix )Clone ()Matrix {return NewMatrix (_cb [0],_cb [1],_cb [3],_cb [4],_cb [6],_cb [7])};func (_ag *Matrix )Shear (x ,y float64 ){_ag .Concat (ShearMatrix (x ,y ))};func (_ec Matrix )Transform (x ,y float64 )(float64 ,float64 ){_dd :=x *_ec [0]+y *_ec [3]+_ec [6];
_be :=x *_ec [1]+y *_ec [4]+_ec [7];return _dd ,_be ;};func (_ebe Point )Rotate (theta float64 )Point {_bf :=_c .Hypot (_ebe .X ,_ebe .Y );_aa :=_c .Atan2 (_ebe .Y ,_ebe .X );_cde ,_afb :=_c .Sincos (_aa +theta /180.0*_c .Pi );return Point {_bf *_afb ,_bf *_cde };
};func (_fb Matrix )Translate (tx ,ty float64 )Matrix {return _fb .Mult (TranslationMatrix (tx ,ty ))};func (_bg Matrix )Scale (xScale ,yScale float64 )Matrix {return _bg .Mult (ScaleMatrix (xScale ,yScale ))};func NewMatrix (a ,b ,c ,d ,tx ,ty float64 )Matrix {_af :=Matrix {a ,b ,0,c ,d ,0,tx ,ty ,1};
_af .clampRange ();return _af ;};func (_bgcc Matrix )ScalingFactorY ()float64 {return _c .Hypot (_bgcc [3],_bgcc [4])};type Point struct{X float64 ;Y float64 ;};func RotationMatrix (angle float64 )Matrix {_ef :=_c .Cos (angle );_cd :=_c .Sin (angle );return NewMatrix (_ef ,_cd ,-_cd ,_ef ,0,0);
};func (_bad *Point )transformByMatrix (_dga Matrix ){_bad .X ,_bad .Y =_dga .Transform (_bad .X ,_bad .Y )};const _age =1e9;func ScaleMatrix (x ,y float64 )Matrix {return NewMatrix (x ,0,0,y ,0,0)};func (_ae Matrix )String ()string {_b ,_ad ,_dc ,_ba ,_ab ,_eff :=_ae [0],_ae [1],_ae [3],_ae [4],_ae [6],_ae [7];
return _ee .Sprintf ("\u005b\u00257\u002e\u0034\u0066\u002c%\u0037\u002e4\u0066\u002c\u0025\u0037\u002e\u0034\u0066\u002c%\u0037\u002e\u0034\u0066\u003a\u0025\u0037\u002e\u0034\u0066\u002c\u00257\u002e\u0034\u0066\u005d",_b ,_ad ,_dc ,_ba ,_ab ,_eff );
};type Matrix [9]float64 ;func (_fe *Point )Set (x ,y float64 ){_fe .X ,_fe .Y =x ,y };func (_gge Point )Displace (delta Point )Point {return Point {_gge .X +delta .X ,_gge .Y +delta .Y }};func IdentityMatrix ()Matrix {return NewMatrix (1,0,0,1,0,0)};const _dac =1.0e-6;
func (_dgc *Matrix )clampRange (){for _ed ,_aef :=range _dgc {if _aef > _age {_ea .Log .Debug ("\u0043L\u0041M\u0050\u003a\u0020\u0025\u0067\u0020\u002d\u003e\u0020\u0025\u0067",_aef ,_age );_dgc [_ed ]=_age ;}else if _aef < -_age {_ea .Log .Debug ("\u0043L\u0041M\u0050\u003a\u0020\u0025\u0067\u0020\u002d\u003e\u0020\u0025\u0067",_aef ,-_age );
_dgc [_ed ]=-_age ;};};};func (_fa *Point )Transform (a ,b ,c ,d ,tx ,ty float64 ){_fg :=NewMatrix (a ,b ,c ,d ,tx ,ty );_fa .transformByMatrix (_fg );};func NewPoint (x ,y float64 )Point {return Point {X :x ,Y :y }};func (_cf Matrix )Singular ()bool {return _c .Abs (_cf [0]*_cf [4]-_cf [1]*_cf [3])< _bc };
func (_ca *Matrix )Concat (b Matrix ){*_ca =Matrix {b [0]*_ca [0]+b [1]*_ca [3],b [0]*_ca [1]+b [1]*_ca [4],0,b [3]*_ca [0]+b [4]*_ca [3],b [3]*_ca [1]+b [4]*_ca [4],0,b [6]*_ca [0]+b [7]*_ca [3]+_ca [6],b [6]*_ca [1]+b [7]*_ca [4]+_ca [7],1};_ca .clampRange ();
};func (_bd Matrix )Translation ()(float64 ,float64 ){return _bd [6],_bd [7]};func (_f Matrix )Round (precision float64 )Matrix {for _d :=range _f {_f [_d ]=_c .Round (_f [_d ]/precision )*precision ;};return _f ;};