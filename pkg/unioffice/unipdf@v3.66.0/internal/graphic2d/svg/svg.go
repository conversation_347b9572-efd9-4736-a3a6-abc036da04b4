//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package svg ;import (_ae "encoding/xml";_a "fmt";_abg "github.com/unidoc/unipdf/v3/common";_g "github.com/unidoc/unipdf/v3/contentstream";_d "github.com/unidoc/unipdf/v3/contentstream/draw";_be "github.com/unidoc/unipdf/v3/core";_ac "github.com/unidoc/unipdf/v3/internal/graphic2d";
_fc "github.com/unidoc/unipdf/v3/model";_fb "golang.org/x/net/html/charset";_ab "io";_f "math";_bb "os";_ba "strconv";_fe "strings";_b "unicode";);func _dbfe (_eac float64 ,_dbfb int )float64 {_abf :=_f .Pow (10,float64 (_dbfb ));return float64 (_gccf (_eac *_abf ))/_abf ;
};func _egf (_dac []*Command )*Path {_abcb :=&Path {};var _cdgc []*Command ;for _ggaf ,_dgba :=range _dac {switch _fe .ToLower (_dgba .Symbol ){case _beg ._acbe :if len (_cdgc )> 0{_abcb .Subpaths =append (_abcb .Subpaths ,&Subpath {_cdgc });};_cdgc =[]*Command {_dgba };
case _beg ._dada :_cdgc =append (_cdgc ,_dgba );_abcb .Subpaths =append (_abcb .Subpaths ,&Subpath {_cdgc });_cdgc =[]*Command {};default:_cdgc =append (_cdgc ,_dgba );if len (_dac )==_ggaf +1{_abcb .Subpaths =append (_abcb .Subpaths ,&Subpath {_cdgc });
};};};return _abcb ;};func _ffd (_egebc []token )([]*Command ,error ){var (_ddca []*Command ;_cee []float64 ;);for _fcf :=len (_egebc )-1;_fcf >=0;_fcf --{_edcb :=_egebc [_fcf ];if _edcb ._fdb {_degb :=_beg ._bfdc [_fe .ToLower (_edcb ._cbc )];_bcaf :=len (_cee );
if _degb ==0&&_bcaf ==0{_cag :=&Command {Symbol :_edcb ._cbc };_ddca =append ([]*Command {_cag },_ddca ...);}else if _degb !=0&&_bcaf %_degb ==0{_ccd :=_bcaf /_degb ;for _ded :=0;_ded < _ccd ;_ded ++{_dbe :=_edcb ._cbc ;if _dbe =="\u006d"&&_ded < _ccd -1{_dbe ="\u006c";
};if _dbe =="\u004d"&&_ded < _ccd -1{_dbe ="\u004c";};_egagd :=&Command {_dbe ,_fef (_cee [:_degb ])};_ddca =append ([]*Command {_egagd },_ddca ...);_cee =_cee [_degb :];};}else {_dea :=pathParserError {"I\u006e\u0063\u006f\u0072\u0072\u0065c\u0074\u0020\u006e\u0075\u006d\u0062e\u0072\u0020\u006f\u0066\u0020\u0070\u0061r\u0061\u006d\u0065\u0074\u0065\u0072\u0073\u0020\u0066\u006fr\u0020"+_edcb ._cbc };
return nil ,_dea ;};}else {_dgd ,_bad :=_aebd (_edcb ._cbc ,64);if _bad !=nil {return nil ,_bad ;};_cee =append (_cee ,_dgd );};};return _ddca ,nil ;};func (_bgffb pathParserError )Error ()string {return _bgffb ._afa };type GraphicSVG struct{ViewBox struct{X ,Y ,W ,H float64 ;
};Name string ;Attributes map[string ]string ;Children []*GraphicSVG ;Content string ;Style *GraphicSVGStyle ;Width float64 ;Height float64 ;_ddc float64 ;};func _bcc (_gagg map[string ]string ,_gga float64 )(*GraphicSVGStyle ,error ){_gbbe :=_cfed ();
_geb ,_bbed :=_gagg ["\u0066\u0069\u006c\u006c"];if _bbed {_gbbe .FillColor =_geb ;if _geb =="\u006e\u006f\u006e\u0065"{_gbbe .FillColor ="";};};_gfa ,_gfce :=_gagg ["\u0066\u0069\u006cl\u002d\u006f\u0070\u0061\u0063\u0069\u0074\u0079"];if _gfce {_gccd ,_edb :=_gfae (_gfa );
if _edb !=nil {return nil ,_edb ;};_gbbe .FillOpacity =_gccd ;};_bgff ,_dbaa :=_gagg ["\u0073\u0074\u0072\u006f\u006b\u0065"];if _dbaa {_gbbe .StrokeColor =_bgff ;if _bgff =="\u006e\u006f\u006e\u0065"{_gbbe .StrokeColor ="";};};_afe ,_dcc :=_gagg ["\u0073\u0074\u0072o\u006b\u0065\u002d\u0077\u0069\u0064\u0074\u0068"];
if _dcc {_gcd ,_agde :=_aebd (_afe ,64);if _agde !=nil {return nil ,_agde ;};_gbbe .StrokeWidth =_gcd *_gga ;};return _gbbe ,nil ;};func _bebg (_fbgd string )(_agdf []float64 ,_gefa error ){var _fbfc float64 ;_abcd :=0;_degg :=true ;for _aaf ,_gdgb :=range _fbgd {if _gdgb =='.'{if _degg {_degg =false ;
continue ;};_fbfc ,_gefa =_aebd (_fbgd [_abcd :_aaf ],64);if _gefa !=nil {return ;};_agdf =append (_agdf ,_fbfc );_abcd =_aaf ;};};_fbfc ,_gefa =_aebd (_fbgd [_abcd :],64);if _gefa !=nil {return ;};_agdf =append (_agdf ,_fbfc );return ;};func _bfdf ()commands {var _egag =map[string ]int {"\u006d":2,"\u007a":0,"\u006c":2,"\u0068":1,"\u0076":1,"\u0063":6,"\u0073":4,"\u0071":4,"\u0074":2,"\u0061":7};
var _cdda []string ;for _bgbeb :=range _egag {_cdda =append (_cdda ,_bgbeb );};return commands {_cdda ,_egag ,"\u006d","\u007a"};};type Subpath struct{Commands []*Command ;};func _gccf (_aab float64 )int {return int (_aab +_f .Copysign (0.5,_aab ))};type GraphicSVGStyle struct{FillColor string ;
StrokeColor string ;StrokeWidth float64 ;FillOpacity float64 ;};func _ade (_dbd string )(*_fc .PdfFont ,error ){_faf ,_cgbc :=map[string ]_fc .StdFontName {"\u0063o\u0075\u0072\u0069\u0065\u0072":_fc .CourierName ,"\u0063\u006f\u0075r\u0069\u0065\u0072\u002d\u0062\u006f\u006c\u0064":_fc .CourierBoldName ,"\u0063o\u0075r\u0069\u0065\u0072\u002d\u006f\u0062\u006c\u0069\u0071\u0075\u0065":_fc .CourierObliqueName ,"c\u006fu\u0072\u0069\u0065\u0072\u002d\u0062\u006f\u006cd\u002d\u006f\u0062\u006ciq\u0075\u0065":_fc .CourierBoldObliqueName ,"\u0068e\u006c\u0076\u0065\u0074\u0069\u0063a":_fc .HelveticaName ,"\u0068\u0065\u006c\u0076\u0065\u0074\u0069\u0063\u0061-\u0062\u006f\u006c\u0064":_fc .HelveticaBoldName ,"\u0068\u0065\u006c\u0076\u0065\u0074\u0069\u0063\u0061\u002d\u006f\u0062l\u0069\u0071\u0075\u0065":_fc .HelveticaObliqueName ,"\u0068\u0065\u006c\u0076et\u0069\u0063\u0061\u002d\u0062\u006f\u006c\u0064\u002d\u006f\u0062\u006c\u0069\u0071u\u0065":_fc .HelveticaBoldObliqueName ,"\u0073\u0079\u006d\u0062\u006f\u006c":_fc .SymbolName ,"\u007a\u0061\u0070\u0066\u002d\u0064\u0069\u006e\u0067\u0062\u0061\u0074\u0073":_fc .ZapfDingbatsName ,"\u0074\u0069\u006de\u0073":_fc .TimesRomanName ,"\u0074\u0069\u006d\u0065\u0073\u002d\u0062\u006f\u006c\u0064":_fc .TimesBoldName ,"\u0074\u0069\u006de\u0073\u002d\u0069\u0074\u0061\u006c\u0069\u0063":_fc .TimesItalicName ,"\u0074\u0069\u006d\u0065\u0073\u002d\u0062\u006f\u006c\u0064\u002d\u0069t\u0061\u006c\u0069\u0063":_fc .TimesBoldItalicName }[_dbd ];
if !_cgbc {return nil ,_a .Errorf ("\u0066\u006f\u006e\u0074\u002df\u0061\u006d\u0069\u006c\u0079\u0020\u0025\u0073\u0020\u006e\u006f\u0074\u0020f\u006f\u0075\u006e\u0064\u0020\u0069\u006e\u0020\u0074\u0068\u0065\u0020\u0073\u0074\u0061\u006e\u0064\u0061\u0072\u0064\u0020\u0066\u006f\u006e\u0074\u0073\u0020\u006c\u0069\u0073t",_dbd );
};_gdg ,_bce :=_fc .NewStandard14Font (_faf );if _bce !=nil {return nil ,_bce ;};return _gdg ,nil ;};func _cad (_bfgc string )(_deaf ,_gaef string ){if _bfgc ==""||(_bfgc [len (_bfgc )-1]>='0'&&_bfgc [len (_bfgc )-1]<='9'){return _bfgc ,"";};_deaf =_bfgc ;
for _ ,_dce :=range _db {if _fe .Contains (_deaf ,_dce ){_gaef =_dce ;};_deaf =_fe .TrimSuffix (_deaf ,_dce );};return ;};func (_bcgd *Command )compare (_dcca *Command )bool {if _bcgd .Symbol !=_dcca .Symbol {return false ;};for _cddg ,_edbd :=range _bcgd .Params {if _edbd !=_dcca .Params [_cddg ]{return false ;
};};return true ;};type commands struct{_ace []string ;_bfdc map[string ]int ;_acbe string ;_dada string ;};func _aebd (_acgd string ,_abd int )(float64 ,error ){_dacg ,_ggafb :=_cad (_acgd );_afeb ,_aafb :=_ba .ParseFloat (_dacg ,_abd );if _aafb !=nil {return 0,_aafb ;
};if _bgge ,_cda :=_ga [_ggafb ];_cda {_afeb =_afeb *_bgge ;}else {_afeb =_afeb *_ge ;};return _afeb ,nil ;};var _beg commands ;var (_db =[]string {"\u0063\u006d","\u006d\u006d","\u0070\u0078","\u0070\u0074"};_ga =map[string ]float64 {"\u0063\u006d":_ff ,"\u006d\u006d":_fd ,"\u0070\u0078":_ge ,"\u0070\u0074":1};
);func _dga (_fbef string )([]float64 ,error ){_agbf :=-1;var _afbg []float64 ;_gfag :=' ';for _ecc ,_adf :=range _fbef {if !_b .IsNumber (_adf )&&_adf !='.'&&!(_adf =='-'&&_gfag =='e')&&_adf !='e'{if _agbf !=-1{_bdgd ,_fcbb :=_bebg (_fbef [_agbf :_ecc ]);
if _fcbb !=nil {return _afbg ,_fcbb ;};_afbg =append (_afbg ,_bdgd ...);};if _adf =='-'{_agbf =_ecc ;}else {_agbf =-1;};}else if _agbf ==-1{_agbf =_ecc ;};_gfag =_adf ;};if _agbf !=-1&&_agbf !=len (_fbef ){_debg ,_aef :=_bebg (_fbef [_agbf :]);if _aef !=nil {return _afbg ,_aef ;
};_afbg =append (_afbg ,_debg ...);};return _afbg ,nil ;};func (_egc *GraphicSVG )setDefaultScaling (_dgg float64 ){_egc ._ddc =_dgg ;if _egc .Style !=nil &&_egc .Style .StrokeWidth > 0{_egc .Style .StrokeWidth =_egc .Style .StrokeWidth *_egc ._ddc ;};
for _ ,_dfde :=range _egc .Children {_dfde .setDefaultScaling (_dgg );};};func _fgf (_fcdg *_ae .Decoder )(*GraphicSVG ,error ){for {_aece ,_gbe :=_fcdg .Token ();if _aece ==nil &&_gbe ==_ab .EOF {break ;};if _gbe !=nil {return nil ,_gbe ;};switch _dbgdg :=_aece .(type ){case _ae .StartElement :return _afd (_dbgdg ),nil ;
};};return &GraphicSVG {},nil ;};type Command struct{Symbol string ;Params []float64 ;};func _fbfb (_cedb string )(float64 ,error ){_cedb =_fe .TrimSpace (_cedb );var _edafb float64 ;if _fe .HasSuffix (_cedb ,"\u0025"){_ggg ,_gce :=_ba .ParseFloat (_fe .TrimSuffix (_cedb ,"\u0025"),64);
if _gce !=nil {return 0,_gce ;};_edafb =(_ggg *255.0)/100.0;}else {_adca ,_ffe :=_ba .Atoi (_cedb );if _ffe !=nil {return 0,_ffe ;};_edafb =float64 (_adca );};return _edafb ,nil ;};func (_bfg *Path )compare (_bebd *Path )bool {if len (_bfg .Subpaths )!=len (_bebd .Subpaths ){return false ;
};for _egee ,_bcf :=range _bfg .Subpaths {if !_bcf .compare (_bebd .Subpaths [_egee ]){return false ;};};return true ;};func _efb (_fg *GraphicSVG ,_dfe *_g .ContentCreator ,_gbc *_fc .PdfPageResources ){_dfe .Add_q ();_fg .Style .toContentStream (_dfe ,_gbc );
_bdc ,_bdf :=_aebd (_fg .Attributes ["\u0063\u0078"],64);if _bdf !=nil {_abg .Log .Debug ("\u0045\u0072\u0072or\u0020\u0077\u0068\u0069\u006c\u0065\u0020\u0070\u0061r\u0073i\u006eg\u0020`\u0063\u0078\u0060\u0020\u0076\u0061\u006c\u0075\u0065\u003a\u0020\u0025\u0076",_bdf .Error ());
};_dgb ,_bdf :=_aebd (_fg .Attributes ["\u0063\u0079"],64);if _bdf !=nil {_abg .Log .Debug ("\u0045\u0072\u0072or\u0020\u0077\u0068\u0069\u006c\u0065\u0020\u0070\u0061r\u0073i\u006eg\u0020`\u0063\u0079\u0060\u0020\u0076\u0061\u006c\u0075\u0065\u003a\u0020\u0025\u0076",_bdf .Error ());
};_fcg ,_bdf :=_aebd (_fg .Attributes ["\u0072\u0078"],64);if _bdf !=nil {_abg .Log .Debug ("\u0045\u0072\u0072or\u0020\u0077\u0068\u0069\u006c\u0065\u0020\u0070\u0061r\u0073i\u006eg\u0020`\u0072\u0078\u0060\u0020\u0076\u0061\u006c\u0075\u0065\u003a\u0020\u0025\u0076",_bdf .Error ());
};_ec ,_bdf :=_aebd (_fg .Attributes ["\u0072\u0079"],64);if _bdf !=nil {_abg .Log .Debug ("\u0045\u0072\u0072or\u0020\u0077\u0068\u0069\u006c\u0065\u0020\u0070\u0061r\u0073i\u006eg\u0020`\u0072\u0079\u0060\u0020\u0076\u0061\u006c\u0075\u0065\u003a\u0020\u0025\u0076",_bdf .Error ());
};_gc :=_fcg *_fg ._ddc ;_cdd :=_ec *_fg ._ddc ;_cfe :=_bdc *_fg ._ddc ;_cge :=_dgb *_fg ._ddc ;_ffbb :=_gc *_ffg ;_bca :=_cdd *_ffg ;_aed :=_d .NewCubicBezierPath ();_aed =_aed .AppendCurve (_d .NewCubicBezierCurve (-_gc ,0,-_gc ,_bca ,-_ffbb ,_cdd ,0,_cdd ));
_aed =_aed .AppendCurve (_d .NewCubicBezierCurve (0,_cdd ,_ffbb ,_cdd ,_gc ,_bca ,_gc ,0));_aed =_aed .AppendCurve (_d .NewCubicBezierCurve (_gc ,0,_gc ,-_bca ,_ffbb ,-_cdd ,0,-_cdd ));_aed =_aed .AppendCurve (_d .NewCubicBezierCurve (0,-_cdd ,-_ffbb ,-_cdd ,-_gc ,-_bca ,-_gc ,0));
_aed =_aed .Offset (_cfe ,_cge );if _fg .Style .StrokeWidth > 0{_aed =_aed .Offset (_fg .Style .StrokeWidth /2,_fg .Style .StrokeWidth /2);};_d .DrawBezierPathWithCreator (_aed ,_dfe );_fg .Style .fillStroke (_dfe );_dfe .Add_h ();_dfe .Add_Q ();};func _edaf (_egbg []token ,_eaad string )([]token ,string ){if _eaad !=""{_egbg =append (_egbg ,token {_eaad ,false });
_eaad ="";};return _egbg ,_eaad ;};func ParseFromStream (source _ab .Reader )(*GraphicSVG ,error ){_dge :=_ae .NewDecoder (source );_dge .CharsetReader =_fb .NewReaderLabel ;_gec ,_dda :=_fgf (_dge );if _dda !=nil {return nil ,_dda ;};if _ced :=_gec .Decode (_dge );
_ced !=nil &&_ced !=_ab .EOF {return nil ,_ced ;};return _gec ,nil ;};func (_afc *GraphicSVG )Decode (decoder *_ae .Decoder )error {for {_bbge ,_deb :=decoder .Token ();if _bbge ==nil &&_deb ==_ab .EOF {break ;};if _deb !=nil {return _deb ;};switch _cacb :=_bbge .(type ){case _ae .StartElement :_edaa :=_afd (_cacb );
_fdc :=_edaa .Decode (decoder );if _fdc !=nil {return _fdc ;};_afc .Children =append (_afc .Children ,_edaa );case _ae .CharData :_cef :=_fe .TrimSpace (string (_cacb ));if _cef !=""{_afc .Content =string (_cacb );};case _ae .EndElement :if _cacb .Name .Local ==_afc .Name {return nil ;
};};};return nil ;};func _fecf (_afeg string )[]token {var (_eeg []token ;_gac string ;);for _ ,_agbe :=range _afeg {_fba :=string (_agbe );switch {case _beg .isCommand (_fba ):_eeg ,_gac =_edaf (_eeg ,_gac );_eeg =append (_eeg ,token {_fba ,true });case _fba =="\u002e":if _gac ==""{_gac ="\u0030";
};if _fe .Contains (_gac ,_fba ){_eeg =append (_eeg ,token {_gac ,false });_gac ="\u0030";};fallthrough;case _fba >="\u0030"&&_fba <="\u0039"||_fba =="\u0065":_gac +=_fba ;case _fba =="\u002d":if _fe .HasSuffix (_gac ,"\u0065"){_gac +=_fba ;}else {_eeg ,_ =_edaf (_eeg ,_gac );
_gac =_fba ;};default:_eeg ,_gac =_edaf (_eeg ,_gac );};};_eeg ,_ =_edaf (_eeg ,_gac );return _eeg ;};func _fa (_cbeg *GraphicSVG ,_cgb *_g .ContentCreator ,_bf *_fc .PdfPageResources ){_cgb .Add_q ();_cbeg .Style .toContentStream (_cgb ,_bf );_baf ,_ffc :=_aebd (_cbeg .Attributes ["\u0078\u0031"],64);
if _ffc !=nil {_abg .Log .Debug ("\u0045\u0072\u0072or\u0020\u0077\u0068\u0069\u006c\u0065\u0020\u0070\u0061r\u0073i\u006eg\u0020`\u0063\u0078\u0060\u0020\u0076\u0061\u006c\u0075\u0065\u003a\u0020\u0025\u0076",_ffc .Error ());};_aac ,_ffc :=_aebd (_cbeg .Attributes ["\u0079\u0031"],64);
if _ffc !=nil {_abg .Log .Debug ("\u0045\u0072\u0072or\u0020\u0077\u0068\u0069\u006c\u0065\u0020\u0070\u0061r\u0073i\u006eg\u0020`\u0063\u0079\u0060\u0020\u0076\u0061\u006c\u0075\u0065\u003a\u0020\u0025\u0076",_ffc .Error ());};_fbee ,_ffc :=_aebd (_cbeg .Attributes ["\u0078\u0032"],64);
if _ffc !=nil {_abg .Log .Debug ("\u0045\u0072\u0072or\u0020\u0077\u0068\u0069\u006c\u0065\u0020\u0070\u0061r\u0073i\u006eg\u0020`\u0072\u0078\u0060\u0020\u0076\u0061\u006c\u0075\u0065\u003a\u0020\u0025\u0076",_ffc .Error ());};_dfd ,_ffc :=_aebd (_cbeg .Attributes ["\u0079\u0032"],64);
if _ffc !=nil {_abg .Log .Debug ("\u0045\u0072\u0072or\u0020\u0077\u0068\u0069\u006c\u0065\u0020\u0070\u0061r\u0073i\u006eg\u0020`\u0072\u0079\u0060\u0020\u0076\u0061\u006c\u0075\u0065\u003a\u0020\u0025\u0076",_ffc .Error ());};_cgb .Add_m (_baf *_cbeg ._ddc ,_aac *_cbeg ._ddc );
_cgb .Add_l (_fbee *_cbeg ._ddc ,_dfd *_cbeg ._ddc );_cbeg .Style .fillStroke (_cgb );_cgb .Add_h ();_cgb .Add_Q ();};func _bbg (_aec *GraphicSVG ,_dbga *_g .ContentCreator ){_bbe ,_cacg :=_aec .Attributes ["\u0074r\u0061\u006e\u0073\u0066\u006f\u0072m"];
if _cacg {_gab :=_fe .Fields (_bbe );for _ ,_bgag :=range _gab {_dcb :=_fe .FieldsFunc (_bgag ,_eda );if len (_dcb )< 3{_abg .Log .Debug ("\u0063\u0061\u006e't\u0020\u0070\u0061\u0072\u0073\u0065\u0020\u0074\u0072a\u006es\u0066o\u0072m\u0020\u0061\u0074\u0074\u0072\u0069\u0062\u0075\u0074\u0065\u0020\u0025\u0076",_bbe );
return ;};_egb ,_cbd :=_aebd (_dcb [1],64);if _cbd !=nil {_abg .Log .Debug ("\u0063\u0061\u006e't\u0020\u0070\u0061\u0072\u0073\u0065\u0020\u0074\u0072a\u006es\u0066o\u0072m\u0020\u0061\u0074\u0074\u0072\u0069\u0062\u0075\u0074\u0065\u0020\u0025\u0076",_bbe );
return ;};_bdg ,_cbd :=_aebd (_dcb [2],64);if _cbd !=nil {_abg .Log .Debug ("\u0063\u0061\u006e't\u0020\u0070\u0061\u0072\u0073\u0065\u0020\u0074\u0072a\u006es\u0066o\u0072m\u0020\u0061\u0074\u0074\u0072\u0069\u0062\u0075\u0074\u0065\u0020\u0025\u0076",_bbe );
return ;};if _dcb [0]=="\u0074r\u0061\u006e\u0073\u006c\u0061\u0074e"{_dbga .Translate (_egb ,_bdg );}else if _dcb [0]=="\u0073\u0063\u0061l\u0065"{_dbga .Scale (_egb ,_bdg );}else {_abg .Log .Debug ("\u0063\u0061\u006e't\u0020\u0070\u0061\u0072\u0073\u0065\u0020\u0074\u0072a\u006es\u0066o\u0072m\u0020\u0061\u0074\u0074\u0072\u0069\u0062\u0075\u0074\u0065\u0020\u0025\u0076",_bbe );
return ;};};};};func _aff (_gbb *GraphicSVG ,_acg *_g .ContentCreator ,_gae *_fc .PdfPageResources ){_acg .Add_BT ();_fec ,_cbeb :=_aebd (_gbb .Attributes ["\u0078"],64);if _cbeb !=nil {_abg .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020w\u0068\u0069\u006c\u0065\u0020\u0070\u0061\u0072\u0073\u0069\u006e\u0067\u0020`\u0078\u0060\u0020\u0076\u0061\u006c\u0075e\u003a\u0020\u0025\u0076",_cbeb .Error ());
};_fdeg ,_cbeb :=_aebd (_gbb .Attributes ["\u0079"],64);if _cbeb !=nil {_abg .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020w\u0068\u0069\u006c\u0065\u0020\u0070\u0061\u0072\u0073\u0069\u006e\u0067\u0020`\u0079\u0060\u0020\u0076\u0061\u006c\u0075e\u003a\u0020\u0025\u0076",_cbeb .Error ());
};_eeb :=_gbb .Attributes ["\u0066\u0069\u006c\u006c"];var _ecg ,_bea ,_cfa float64 ;if _cab ,_bdd :=_ac .ColorMap [_eeb ];_bdd {_ggb ,_agdb ,_cebc ,_ :=_cab .RGBA ();_ecg ,_bea ,_cfa =float64 (_ggb ),float64 (_agdb ),float64 (_cebc );}else if _fe .HasPrefix (_eeb ,"\u0072\u0067\u0062\u0028"){_ecg ,_bea ,_cfa =_bcgf (_eeb );
}else {_ecg ,_bea ,_cfa =_cfef (_eeb );};_acg .Add_rg (_ecg ,_bea ,_cfa );_bcg :=_aeb ;if _abe ,_dcd :=_gbb .Attributes ["\u0066o\u006e\u0074\u002d\u0073\u0069\u007ae"];_dcd {_bcg ,_cbeb =_ba .ParseFloat (_abe ,64);if _cbeb !=nil {_abg .Log .Debug ("\u0045\u0072\u0072\u006f\u0072 \u0077\u0068\u0069\u006c\u0065\u0020\u0070\u0061\u0072\u0073\u0069\u006e\u0067 \u0060\u0066\u006f\u006e\u0074\u002d\u0073\u0069\u007a\u0065\u0060\u0020\u0076\u0061\u006c\u0075\u0065\u003a\u0020\u0025\u0076",_cbeb .Error ());
_bcg =_aeb ;};};_bbc :=_gbb ._ddc *_bcg *_aa /_ffgb ;_cea :=_be .PdfObjectName ("\u0053\u0046\u006fn\u0074");_aead :=_fc .DefaultFont ();_cgf ,_cac :=_gbb .Attributes ["f\u006f\u006e\u0074\u002d\u0066\u0061\u006d\u0069\u006c\u0079"];if _cac {if _eee ,_eba :=_ade (_cgf );
_eba ==nil {_aead =_eee ;_geg :=1;for _gae .HasFontByName (_cea ){_cea =_be .PdfObjectName ("\u0053\u0046\u006fn\u0074"+_ba .Itoa (_geg ));_geg ++;};};};_egd :=0.0;_dfa ,_cac :=_gbb .Attributes ["t\u0065\u0078\u0074\u002d\u0061\u006e\u0063\u0068\u006f\u0072"];
if _cac &&_dfa !="\u0073\u0074\u0061r\u0074"{var _bfa float64 ;for _ ,_bfb :=range _gbb .Content {_eaf ,_cgdb :=_aead .GetRuneMetrics (_bfb );if !_cgdb {_abg .Log .Debug ("\u0045\u0052\u0052OR\u003a\u0020\u0075\u006e\u0073\u0075\u0070\u0070\u006fr\u0074e\u0064 \u0072u\u006e\u0065\u0020\u0025\u0076\u0020\u0069\u006e\u0020\u0066\u006f\u006e\u0074",_bfb );
};_bfa +=_eaf .Wx ;};_bfa =_bfa *_bbc /1000.0;if _dfa =="\u006d\u0069\u0064\u0064\u006c\u0065"{_egd =-_bfa /2;}else if _dfa =="\u0065\u006e\u0064"{_egd =-_bfa ;};};_acg .Add_Tm (1,0,0,-1,_fec *_gbb ._ddc +_egd ,_fdeg *_gbb ._ddc );_gae .SetFontByName (_cea ,_aead .ToPdfObject ());
_acg .Add_Tf (_cea ,_bbc );_cc :=_gbb .Content ;_fcd :=_be .MakeString (_cc );_acg .Add_Tj (*_fcd );_acg .Add_ET ();};const (_ge =0.72;_ff =28.3464;_fd =_ff /10;_ffg =0.551784;_ffgb =96;_aeb =16.0;_aa float64 =72;);type token struct{_cbc string ;_fdb bool ;
};func (_gfef *Command )isAbsolute ()bool {return _gfef .Symbol ==_fe .ToUpper (_gfef .Symbol )};type Path struct{Subpaths []*Subpath ;};func _bcgf (_dccf string )(float64 ,float64 ,float64 ){_gefd :=_fe .TrimPrefix (_dccf ,"\u0072\u0067\u0062\u0028");
_gefd =_fe .TrimSuffix (_gefd ,"\u0029");_edcbd :=_fe .Split (_gefd ,"\u002c");if len (_edcbd )!=3{_abg .Log .Debug ("I\u006e\u0076\u0061\u006c\u0069\u0064 \u0072\u0067\u0062\u0020\u0063\u006fl\u006f\u0072\u0020\u0073\u0070\u0065\u0063i\u0066\u0069\u0063\u0061\u0074\u0069\u006f\u006e\u003a\u0020%\u0073",_dccf );
return 0,0,0;};var _edg ,_baee ,_gge float64 ;_edg ,_fae :=_fbfb (_edcbd [0]);if _fae !=nil {_abg .Log .Debug ("I\u006e\u0076\u0061\u006c\u0069\u0064 \u0072\u0067\u0062\u0020\u0063\u006fl\u006f\u0072\u0020\u0073\u0070\u0065\u0063i\u0066\u0069\u0063\u0061\u0074\u0069\u006f\u006e\u003a\u0020%\u0073",_dccf );
return 0,0,0;};_baee ,_fae =_fbfb (_edcbd [1]);if _fae !=nil {_abg .Log .Debug ("I\u006e\u0076\u0061\u006c\u0069\u0064 \u0072\u0067\u0062\u0020\u0063\u006fl\u006f\u0072\u0020\u0073\u0070\u0065\u0063i\u0066\u0069\u0063\u0061\u0074\u0069\u006f\u006e\u003a\u0020%\u0073",_dccf );
return 0,0,0;};_gge ,_fae =_fbfb (_edcbd [2]);if _fae !=nil {_abg .Log .Debug ("I\u006e\u0076\u0061\u006c\u0069\u0064 \u0072\u0067\u0062\u0020\u0063\u006fl\u006f\u0072\u0020\u0073\u0070\u0065\u0063i\u0066\u0069\u0063\u0061\u0074\u0069\u006f\u006e\u003a\u0020%\u0073",_dccf );
return 0,0,0;};_caba :=_edg /255.0;_ebce :=_baee /255.0;_bab :=_gge /255.0;return _caba ,_ebce ,_bab ;};func _gcf (_dfdb string )(*Path ,error ){_beg =_bfdf ();_bacc ,_fge :=_ffd (_fecf (_dfdb ));if _fge !=nil {return nil ,_fge ;};return _egf (_bacc ),nil ;
};func _ca (_edc *GraphicSVG ,_da *_g .ContentCreator ,_dc *_fc .PdfPageResources ){_da .Add_q ();_edc .Style .toContentStream (_da ,_dc );_cec ,_ega :=_dga (_edc .Attributes ["\u0070\u006f\u0069\u006e\u0074\u0073"]);if _ega !=nil {_abg .Log .Debug ("\u0045\u0052\u0052O\u0052\u0020\u0075\u006e\u0061\u0062\u006c\u0065\u0020\u0074\u006f\u0020\u0070\u0061\u0072\u0073\u0065\u0020\u0070\u006f\u0069\u006e\u0074\u0073\u0020\u0061\u0074\u0074\u0072i\u0062\u0075\u0074\u0065\u003a\u0020\u0025\u0076",_ega );
return ;};if len (_cec )%2> 0{_abg .Log .Debug ("\u0045\u0052R\u004f\u0052\u0020\u0069n\u0076\u0061l\u0069\u0064\u0020\u0070\u006f\u0069\u006e\u0074s\u0020\u0061\u0074\u0074\u0072\u0069\u0062\u0075\u0074\u0065\u0020\u006ce\u006e\u0067\u0074\u0068");return ;
};for _gea :=0;_gea < len (_cec );{if _gea ==0{_da .Add_m (_cec [_gea ]*_edc ._ddc ,_cec [_gea +1]*_edc ._ddc );}else {_da .Add_l (_cec [_gea ]*_edc ._ddc ,_cec [_gea +1]*_edc ._ddc );};_gea +=2;};_edc .Style .fillStroke (_da );_da .Add_h ();_da .Add_Q ();
};func _eda (_edcf rune )bool {return _edcf =='('||_edcf ==','||_edcf ==')'};func _dg (_ag *GraphicSVG ,_ce *_g .ContentCreator ,_e *_fc .PdfPageResources ){_ce .Add_q ();_ag .Style .toContentStream (_ce ,_e );_bbg (_ag ,_ce );_bg ,_ee :=_gcf (_ag .Attributes ["\u0064"]);
if _ee !=nil {_abg .Log .Error ("\u0045R\u0052\u004f\u0052\u003a\u0020\u0025s",_ee .Error ());};var (_gf ,_dba =0.0,0.0;_bef ,_bc =0.0,0.0;_agb *Command ;);for _ ,_fbe :=range _bg .Subpaths {for _ ,_aea :=range _fbe .Commands {switch _fe .ToLower (_aea .Symbol ){case "\u006d":_bef ,_bc =_aea .Params [0]*_ag ._ddc ,_aea .Params [1]*_ag ._ddc ;
if !_aea .isAbsolute (){_bef ,_bc =_gf +_bef -_ag .ViewBox .X ,_dba +_bc -_ag .ViewBox .Y ;};_ce .Add_m (_dbfe (_bef ,3),_dbfe (_bc ,3));_gf ,_dba =_bef ,_bc ;case "\u0063":_ef ,_eb ,_af ,_feb ,_bgf ,_gd :=_aea .Params [0]*_ag ._ddc ,_aea .Params [1]*_ag ._ddc ,_aea .Params [2]*_ag ._ddc ,_aea .Params [3]*_ag ._ddc ,_aea .Params [4]*_ag ._ddc ,_aea .Params [5]*_ag ._ddc ;
if !_aea .isAbsolute (){_ef ,_eb ,_af ,_feb ,_bgf ,_gd =_gf +_ef ,_dba +_eb ,_gf +_af ,_dba +_feb ,_gf +_bgf ,_dba +_gd ;};_ce .Add_c (_dbfe (_ef ,3),_dbfe (_eb ,3),_dbfe (_af ,3),_dbfe (_feb ,3),_dbfe (_bgf ,3),_dbfe (_gd ,3));_gf ,_dba =_bgf ,_gd ;case "\u0073":_cg ,_cd ,_de ,_gde :=_aea .Params [0]*_ag ._ddc ,_aea .Params [1]*_ag ._ddc ,_aea .Params [2]*_ag ._ddc ,_aea .Params [3]*_ag ._ddc ;
if !_aea .isAbsolute (){_cg ,_cd ,_de ,_gde =_gf +_cg ,_dba +_cd ,_gf +_de ,_dba +_gde ;};_ce .Add_c (_dbfe (_gf ,3),_dbfe (_dba ,3),_dbfe (_cg ,3),_dbfe (_cd ,3),_dbfe (_de ,3),_dbfe (_gde ,3));_gf ,_dba =_de ,_gde ;case "\u006c":_dec ,_eg :=_aea .Params [0]*_ag ._ddc ,_aea .Params [1]*_ag ._ddc ;
if !_aea .isAbsolute (){_dec ,_eg =_gf +_dec ,_dba +_eg ;};_ce .Add_l (_dbfe (_dec ,3),_dbfe (_eg ,3));_gf ,_dba =_dec ,_eg ;case "\u0068":_fce :=_aea .Params [0]*_ag ._ddc ;if !_aea .isAbsolute (){_fce =_gf +_fce ;};_ce .Add_l (_dbfe (_fce ,3),_dbfe (_dba ,3));
_gf =_fce ;case "\u0076":_df :=_aea .Params [0]*_ag ._ddc ;if !_aea .isAbsolute (){_df =_dba +_df ;};_ce .Add_l (_dbfe (_gf ,3),_dbfe (_df ,3));_dba =_df ;case "\u0071":_gb ,_ad ,_gfc ,_abc :=_aea .Params [0]*_ag ._ddc ,_aea .Params [1]*_ag ._ddc ,_aea .Params [2]*_ag ._ddc ,_aea .Params [3]*_ag ._ddc ;
if !_aea .isAbsolute (){_gb ,_ad ,_gfc ,_abc =_gf +_gb ,_dba +_ad ,_gf +_gfc ,_dba +_abc ;};_ffb ,_gfcc :=_ac .QuadraticToCubicBezier (_gf ,_dba ,_gb ,_ad ,_gfc ,_abc );_ce .Add_c (_dbfe (_ffb .X ,3),_dbfe (_ffb .Y ,3),_dbfe (_gfcc .X ,3),_dbfe (_gfcc .Y ,3),_dbfe (_gfc ,3),_dbfe (_abc ,3));
_gf ,_dba =_gfc ,_abc ;case "\u0074":var _fda ,_gag _ac .Point ;_bae ,_cf :=_aea .Params [0]*_ag ._ddc ,_aea .Params [1]*_ag ._ddc ;if !_aea .isAbsolute (){_bae ,_cf =_gf +_bae ,_dba +_cf ;};if _agb !=nil &&_fe .ToLower (_agb .Symbol )=="\u0071"{_ed :=_ac .Point {X :_agb .Params [0]*_ag ._ddc ,Y :_agb .Params [1]*_ag ._ddc };
_bga :=_ac .Point {X :_agb .Params [2]*_ag ._ddc ,Y :_agb .Params [3]*_ag ._ddc };_cb :=_bga .Mul (2.0).Sub (_ed );_fda ,_gag =_ac .QuadraticToCubicBezier (_gf ,_dba ,_cb .X ,_cb .Y ,_bae ,_cf );};_ce .Add_c (_dbfe (_fda .X ,3),_dbfe (_fda .Y ,3),_dbfe (_gag .X ,3),_dbfe (_gag .Y ,3),_dbfe (_bae ,3),_dbfe (_cf ,3));
_gf ,_dba =_bae ,_cf ;case "\u0061":_ebc ,_bd :=_aea .Params [0]*_ag ._ddc ,_aea .Params [1]*_ag ._ddc ;_fdf :=_aea .Params [2];_bda :=_aea .Params [3]> 0;_ea :=_aea .Params [4]> 0;_bcb ,_eaa :=_aea .Params [5]*_ag ._ddc ,_aea .Params [6]*_ag ._ddc ;if !_aea .isAbsolute (){_bcb ,_eaa =_gf +_bcb ,_dba +_eaa ;
};_aaa :=_ac .EllipseToCubicBeziers (_gf ,_dba ,_ebc ,_bd ,_fdf ,_bda ,_ea ,_bcb ,_eaa );for _ ,_bgg :=range _aaa {_ce .Add_c (_dbfe (_bgg [1].X ,3),_dbfe ((_bgg [1].Y ),3),_dbfe ((_bgg [2].X ),3),_dbfe ((_bgg [2].Y ),3),_dbfe ((_bgg [3].X ),3),_dbfe ((_bgg [3].Y ),3));
};_gf ,_dba =_bcb ,_eaa ;case "\u007a":_ce .Add_h ();};_agb =_aea ;};};_ag .Style .fillStroke (_ce );_ce .Add_h ();_ce .Add_Q ();};func _fef (_dgga []float64 )[]float64 {for _bgffbe ,_dafa :=0,len (_dgga )-1;_bgffbe < _dafa ;_bgffbe ,_dafa =_bgffbe +1,_dafa -1{_dgga [_bgffbe ],_dgga [_dafa ]=_dgga [_dafa ],_dgga [_bgffbe ];
};return _dgga ;};func (_dee *commands )isCommand (_ged string )bool {for _ ,_gcb :=range _dee ._ace {if _fe .ToLower (_ged )==_gcb {return true ;};};return false ;};func _cfef (_afg string )(_geba ,_bceb ,_bge float64 ){if (len (_afg )!=4&&len (_afg )!=7)||_afg [0]!='#'{_abg .Log .Debug ("I\u006ev\u0061\u006c\u0069\u0064\u0020\u0068\u0065\u0078 \u0063\u006f\u0064\u0065: \u0025\u0073",_afg );
return _geba ,_bceb ,_bge ;};var _fgdf ,_cfga ,_aafbd int ;if len (_afg )==4{var _gegg ,_ecf ,_aeaf int ;_bggb ,_ffbc :=_a .Sscanf (_afg ,"\u0023\u0025\u0031\u0078\u0025\u0031\u0078\u0025\u0031\u0078",&_gegg ,&_ecf ,&_aeaf );if _ffbc !=nil {_abg .Log .Debug ("\u0049\u006e\u0076a\u006c\u0069\u0064\u0020h\u0065\u0078\u0020\u0063\u006f\u0064\u0065:\u0020\u0025\u0073\u002c\u0020\u0065\u0072\u0072\u006f\u0072\u003a\u0020\u0025\u0076",_afg ,_ffbc );
return _geba ,_bceb ,_bge ;};if _bggb !=3{_abg .Log .Debug ("I\u006ev\u0061\u006c\u0069\u0064\u0020\u0068\u0065\u0078 \u0063\u006f\u0064\u0065: \u0025\u0073",_afg );return _geba ,_bceb ,_bge ;};_fgdf =_gegg *16+_gegg ;_cfga =_ecf *16+_ecf ;_aafbd =_aeaf *16+_aeaf ;
}else {_dgda ,_agbc :=_a .Sscanf (_afg ,"\u0023\u0025\u0032\u0078\u0025\u0032\u0078\u0025\u0032\u0078",&_fgdf ,&_cfga ,&_aafbd );if _agbc !=nil {_abg .Log .Debug ("I\u006ev\u0061\u006c\u0069\u0064\u0020\u0068\u0065\u0078 \u0063\u006f\u0064\u0065: \u0025\u0073",_afg );
return _geba ,_bceb ,_bge ;};if _dgda !=3{_abg .Log .Debug ("\u0049\u006e\u0076\u0061\u006c\u0069d\u0020\u0068\u0065\u0078\u0020\u0063\u006f\u0064\u0065\u003a\u0020\u0025\u0073,\u0020\u006e\u0020\u0021\u003d\u0020\u0033 \u0028\u0025\u0064\u0029",_afg ,_dgda );
return _geba ,_bceb ,_bge ;};};_gbea :=float64 (_fgdf )/255.0;_cebb :=float64 (_cfga )/255.0;_aeg :=float64 (_aafbd )/255.0;return _gbea ,_cebb ,_aeg ;};func _eae (_cdc *GraphicSVG ,_ceb *_g .ContentCreator ,_eab *_fc .PdfPageResources ){_ceb .Add_q ();
_cdc .Style .toContentStream (_ceb ,_eab );_gef ,_efd :=_aebd (_cdc .Attributes ["\u0078"],64);if _efd !=nil {_abg .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020w\u0068\u0069\u006c\u0065\u0020\u0070\u0061\u0072\u0073\u0069\u006e\u0067\u0020`\u0078\u0060\u0020\u0076\u0061\u006c\u0075e\u003a\u0020\u0025\u0076",_efd .Error ());
};_fceb ,_efd :=_aebd (_cdc .Attributes ["\u0079"],64);if _efd !=nil {_abg .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020w\u0068\u0069\u006c\u0065\u0020\u0070\u0061\u0072\u0073\u0069\u006e\u0067\u0020`\u0079\u0060\u0020\u0076\u0061\u006c\u0075e\u003a\u0020\u0025\u0076",_efd .Error ());
};_ead ,_efd :=_aebd (_cdc .Attributes ["\u0077\u0069\u0064t\u0068"],64);if _efd !=nil {_abg .Log .Debug ("\u0045\u0072\u0072o\u0072\u0020\u0077\u0068\u0069\u006c\u0065\u0020\u0070\u0061\u0072\u0073\u0069\u006e\u0067\u0020\u0073\u0074\u0072\u006f\u006b\u0065\u0020\u0077\u0069\u0064\u0074\u0068\u0020v\u0061\u006c\u0075\u0065\u003a\u0020\u0025\u0076",_efd .Error ());
};_febe ,_efd :=_aebd (_cdc .Attributes ["\u0068\u0065\u0069\u0067\u0068\u0074"],64);if _efd !=nil {_abg .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u0077h\u0069\u006c\u0065 \u0070\u0061\u0072\u0073i\u006e\u0067\u0020\u0073\u0074\u0072\u006f\u006b\u0065\u0020\u0068\u0065\u0069\u0067\u0068\u0074\u0020\u0076\u0061\u006c\u0075\u0065\u003a\u0020\u0025\u0076",_efd .Error ());
};_ceb .Add_re (_gef *_cdc ._ddc ,_fceb *_cdc ._ddc ,_ead *_cdc ._ddc ,_febe *_cdc ._ddc );_cdc .Style .fillStroke (_ceb );_ceb .Add_Q ();};func (_gaa *GraphicSVGStyle )toContentStream (_fgb *_g .ContentCreator ,_gca *_fc .PdfPageResources ){if _gaa ==nil {return ;
};if _gaa .FillColor !=""{var _dad ,_egea ,_daf float64 ;if _dgf ,_gda :=_ac .ColorMap [_gaa .FillColor ];_gda {_gcc ,_befb ,_gcg ,_ :=_dgf .RGBA ();_dad ,_egea ,_daf =float64 (_gcc ),float64 (_befb ),float64 (_gcg );}else if _fe .HasPrefix (_gaa .FillColor ,"\u0072\u0067\u0062\u0028"){_dad ,_egea ,_daf =_bcgf (_gaa .FillColor );
}else {_dad ,_egea ,_daf =_cfef (_gaa .FillColor );};_fgb .Add_rg (_dad ,_egea ,_daf );};if _gaa .FillOpacity < 1.0{_bbf :=0;_adc :=_be .PdfObjectName (_a .Sprintf ("\u0047\u0053\u0025\u0064",_bbf ));for {_ ,_gba :=_gca .GetExtGState (_adc );if !_gba {break ;
};_bbf ++;_adc =_be .PdfObjectName (_a .Sprintf ("\u0047\u0053\u0025\u0064",_bbf ));};_afdg :=_be .MakeDict ();_afdg .Set ("\u0063\u0061",_be .MakeFloat (_gaa .FillOpacity ));_cgg :=_gca .AddExtGState (_adc ,_be .MakeIndirectObject (_afdg ));if _cgg !=nil {_abg .Log .Debug (_cgg .Error ());
return ;};_fgb .Add_gs (_adc );};if _gaa .StrokeColor !=""{var _fbeb ,_adb ,_eefd float64 ;if _efe ,_fbc :=_ac .ColorMap [_gaa .StrokeColor ];_fbc {_efa ,_fgd ,_afff ,_ :=_efe .RGBA ();_fbeb ,_adb ,_eefd =float64 (_efa )/255.0,float64 (_fgd )/255.0,float64 (_afff )/255.0;
}else if _fe .HasPrefix (_gaa .FillColor ,"\u0072\u0067\u0062\u0028"){_fbeb ,_adb ,_eefd =_bcgf (_gaa .FillColor );}else {_fbeb ,_adb ,_eefd =_cfef (_gaa .StrokeColor );};_fgb .Add_RG (_fbeb ,_adb ,_eefd );};if _gaa .StrokeWidth > 0{_fgb .Add_w (_gaa .StrokeWidth );
};};type pathParserError struct{_afa string };func _cfed ()*GraphicSVGStyle {return &GraphicSVGStyle {FillColor :"\u00230\u0030\u0030\u0030\u0030\u0030",StrokeColor :"",StrokeWidth :0,FillOpacity :1.0};};func _fde (_eaaa *GraphicSVG ,_febc *_g .ContentCreator ,_bdae *_fc .PdfPageResources ){_febc .Add_q ();
_eaaa .Style .toContentStream (_febc ,_bdae );_dbg ,_gg :=_aebd (_eaaa .Attributes ["\u0063\u0078"],64);if _gg !=nil {_abg .Log .Debug ("\u0045\u0072\u0072or\u0020\u0077\u0068\u0069\u006c\u0065\u0020\u0070\u0061r\u0073i\u006eg\u0020`\u0063\u0078\u0060\u0020\u0076\u0061\u006c\u0075\u0065\u003a\u0020\u0025\u0076",_gg .Error ());
};_cbe ,_gg :=_aebd (_eaaa .Attributes ["\u0063\u0079"],64);if _gg !=nil {_abg .Log .Debug ("\u0045\u0072\u0072or\u0020\u0077\u0068\u0069\u006c\u0065\u0020\u0070\u0061r\u0073i\u006eg\u0020`\u0063\u0079\u0060\u0020\u0076\u0061\u006c\u0075\u0065\u003a\u0020\u0025\u0076",_gg .Error ());
};_ege ,_gg :=_aebd (_eaaa .Attributes ["\u0072"],64);if _gg !=nil {_abg .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020w\u0068\u0069\u006c\u0065\u0020\u0070\u0061\u0072\u0073\u0069\u006e\u0067\u0020`\u0072\u0060\u0020\u0076\u0061\u006c\u0075e\u003a\u0020\u0025\u0076",_gg .Error ());
};_dd :=_ege *_eaaa ._ddc ;_acb :=_ege *_eaaa ._ddc ;_ebeg :=_dd *_ffg ;_cgd :=_acb *_ffg ;_bba :=_d .NewCubicBezierPath ();_bba =_bba .AppendCurve (_d .NewCubicBezierCurve (-_dd ,0,-_dd ,_cgd ,-_ebeg ,_acb ,0,_acb ));_bba =_bba .AppendCurve (_d .NewCubicBezierCurve (0,_acb ,_ebeg ,_acb ,_dd ,_cgd ,_dd ,0));
_bba =_bba .AppendCurve (_d .NewCubicBezierCurve (_dd ,0,_dd ,-_cgd ,_ebeg ,-_acb ,0,-_acb ));_bba =_bba .AppendCurve (_d .NewCubicBezierCurve (0,-_acb ,-_ebeg ,-_acb ,-_dd ,-_cgd ,-_dd ,0));_bba =_bba .Offset (_dbg *_eaaa ._ddc ,_cbe *_eaaa ._ddc );if _eaaa .Style .StrokeWidth > 0{_bba =_bba .Offset (_eaaa .Style .StrokeWidth /2,_eaaa .Style .StrokeWidth /2);
};_d .DrawBezierPathWithCreator (_bba ,_febc );_eaaa .Style .fillStroke (_febc );_febc .Add_h ();_febc .Add_Q ();};func _gfae (_eagg string )(float64 ,error ){_eagg =_fe .TrimSpace (_eagg );var _dgbd float64 ;if _fe .HasSuffix (_eagg ,"\u0025"){_bbca ,_aafg :=_ba .ParseFloat (_fe .TrimSuffix (_eagg ,"\u0025"),64);
if _aafg !=nil {return 0,_aafg ;};_dgbd =_bbca /100.0;}else {_cdfe ,_cgge :=_ba .ParseFloat (_eagg ,64);if _cgge !=nil {return 0,_cgge ;};_dgbd =_cdfe ;};return _dgbd ,nil ;};func (_gbfd *Subpath )compare (_egbb *Subpath )bool {if len (_gbfd .Commands )!=len (_egbb .Commands ){return false ;
};for _ddg ,_eag :=range _gbfd .Commands {if !_eag .compare (_egbb .Commands [_ddg ]){return false ;};};return true ;};func (_dbc *GraphicSVG )SetScaling (xFactor ,yFactor float64 ){_ebcg :=_dbc .Width /_dbc .ViewBox .W ;_cgdf :=_dbc .Height /_dbc .ViewBox .H ;
_dbc .setDefaultScaling (_f .Max (_ebcg ,_cgdf ));for _ ,_bac :=range _dbc .Children {_bac .SetScaling (xFactor ,yFactor );};};func _afd (_beb _ae .StartElement )*GraphicSVG {_gbbg :=&GraphicSVG {};_afda :=make (map[string ]string );for _ ,_decf :=range _beb .Attr {_afda [_decf .Name .Local ]=_decf .Value ;
};_gbbg .Name =_beb .Name .Local ;_gbbg .Attributes =_afda ;_gbbg ._ddc =1;if _gbbg .Name =="\u0073\u0076\u0067"{_cfg ,_dbf :=_dga (_afda ["\u0076i\u0065\u0077\u0042\u006f\u0078"]);if _dbf !=nil {_abg .Log .Debug ("\u0055\u006ea\u0062\u006c\u0065\u0020t\u006f\u0020p\u0061\u0072\u0073\u0065\u0020\u0076\u0069\u0065w\u0042\u006f\u0078\u0020\u0061\u0074\u0074\u0072\u0069\u0062\u0075\u0074e\u003a\u0020\u0025\u0076",_dbf );
return nil ;};if len (_cfg )>=4{_gbbg .ViewBox .X =_cfg [0];_gbbg .ViewBox .Y =_cfg [1];_gbbg .ViewBox .W =_cfg [2];_gbbg .ViewBox .H =_cfg [3];};_gbbg .Width =_gbbg .ViewBox .W ;_gbbg .Height =_gbbg .ViewBox .H ;if _ggf ,_ggbg :=_afda ["\u0077\u0069\u0064t\u0068"];
_ggbg {if _fe .HasSuffix (_ggf ,"\u0025"){_fbf ,_fdgd :=_ba .ParseFloat (_fe .TrimSuffix (_ggf ,"\u0025"),64);if _fdgd !=nil {_abg .Log .Debug ("U\u006e\u0061\u0062\u006c\u0065\u0020t\u006f\u0020\u0070\u0061\u0072\u0073e\u0020\u0077\u0069\u0064\u0074\u0068\u0020a\u0074\u0074\u0072\u0069\u0062\u0075\u0074\u0065\u003a\u0020%\u0076",_fdgd );
return nil ;};_gbbg .Width =_fbf *_gbbg .ViewBox .W ;}else {_bdcc ,_cdfb :=_aebd (_ggf ,64);if _cdfb !=nil {_abg .Log .Debug ("U\u006e\u0061\u0062\u006c\u0065\u0020t\u006f\u0020\u0070\u0061\u0072\u0073e\u0020\u0077\u0069\u0064\u0074\u0068\u0020a\u0074\u0074\u0072\u0069\u0062\u0075\u0074\u0065\u003a\u0020%\u0076",_cdfb );
return nil ;};_gbbg .Width =_bdcc ;if len (_cfg )< 4{_gbbg .ViewBox .W =_bdcc ;};};};if _cgfd ,_befg :=_afda ["\u0068\u0065\u0069\u0067\u0068\u0074"];_befg {if _fe .HasSuffix (_cgfd ,"\u0025"){_gbcd ,_bdcb :=_ba .ParseFloat (_fe .TrimSuffix (_cgfd ,"\u0025"),64);
if _bdcb !=nil {_abg .Log .Debug ("\u0055\u006eab\u006c\u0065\u0020t\u006f\u0020\u0070\u0061rse\u0020he\u0069\u0067\u0068\u0074\u0020\u0061\u0074tr\u0069\u0062\u0075\u0074\u0065\u003a\u0020%\u0076",_bdcb );return nil ;};_gbbg .Height =_gbcd *_gbbg .ViewBox .H ;
}else {_afcg ,_agg :=_aebd (_cgfd ,64);if _agg !=nil {_abg .Log .Debug ("\u0055\u006eab\u006c\u0065\u0020t\u006f\u0020\u0070\u0061rse\u0020he\u0069\u0067\u0068\u0074\u0020\u0061\u0074tr\u0069\u0062\u0075\u0074\u0065\u003a\u0020%\u0076",_agg );return nil ;
};_gbbg .Height =_afcg ;if len (_cfg )< 4{_gbbg .ViewBox .H =_afcg ;};};};if _gbbg .Width > 0&&_gbbg .Height > 0{_gbbg ._ddc =_gbbg .Width /_gbbg .ViewBox .W ;};};return _gbbg ;};func ParseFromString (svgStr string )(*GraphicSVG ,error ){return ParseFromStream (_fe .NewReader (svgStr ));
};func (_eca *GraphicSVG )ToContentCreator (cc *_g .ContentCreator ,res *_fc .PdfPageResources ,scaleX ,scaleY ,translateX ,translateY float64 )*_g .ContentCreator {if _eca .Name =="\u0073\u0076\u0067"{_eca .SetScaling (scaleX ,scaleY );cc .Add_cm (1,0,0,1,translateX ,translateY );
_eca .setDefaultScaling (_eca ._ddc );cc .Add_q ();_baec :=_f .Max (scaleX ,scaleY );cc .Add_re (_eca .ViewBox .X *_baec ,_eca .ViewBox .Y *_baec ,_eca .ViewBox .W *_baec ,_eca .ViewBox .H *_baec );cc .Add_W ();cc .Add_n ();for _ ,_caa :=range _eca .Children {_caa .ViewBox =_eca .ViewBox ;
_caa .toContentStream (cc ,res );};cc .Add_Q ();return cc ;};return nil ;};func (_baa *GraphicSVG )toContentStream (_deg *_g .ContentCreator ,_ccg *_fc .PdfPageResources ){_cgdc ,_acf :=_bcc (_baa .Attributes ,_baa ._ddc );if _acf !=nil {_abg .Log .Debug ("U\u006e\u0061\u0062\u006c\u0065\u0020t\u006f\u0020\u0070\u0061\u0072\u0073e\u0020\u0073\u0074\u0079\u006c\u0065\u0020a\u0074\u0074\u0072\u0069\u0062\u0075\u0074\u0065\u003a\u0020%\u0076",_acf );
};_baa .Style =_cgdc ;switch _baa .Name {case "\u0070\u0061\u0074\u0068":_dg (_baa ,_deg ,_ccg );for _ ,_afb :=range _baa .Children {_afb .toContentStream (_deg ,_ccg );};case "\u0072\u0065\u0063\u0074":_eae (_baa ,_deg ,_ccg );for _ ,_fbg :=range _baa .Children {_fbg .toContentStream (_deg ,_ccg );
};case "\u0063\u0069\u0072\u0063\u006c\u0065":_fde (_baa ,_deg ,_ccg );for _ ,_bgb :=range _baa .Children {_bgb .toContentStream (_deg ,_ccg );};case "\u0065l\u006c\u0069\u0070\u0073\u0065":_efb (_baa ,_deg ,_ccg );for _ ,_bdb :=range _baa .Children {_bdb .toContentStream (_deg ,_ccg );
};case "\u0070\u006f\u006c\u0079\u006c\u0069\u006e\u0065":_ca (_baa ,_deg ,_ccg );for _ ,_eadc :=range _baa .Children {_eadc .toContentStream (_deg ,_ccg );};case "\u0070o\u006c\u0079\u0067\u006f\u006e":_gbf (_baa ,_deg ,_ccg );for _ ,_fag :=range _baa .Children {_fag .toContentStream (_deg ,_ccg );
};case "\u006c\u0069\u006e\u0065":_fa (_baa ,_deg ,_ccg );for _ ,_fca :=range _baa .Children {_fca .toContentStream (_deg ,_ccg );};case "\u0074\u0065\u0078\u0074":_aff (_baa ,_deg ,_ccg );for _ ,_eaba :=range _baa .Children {_eaba .toContentStream (_deg ,_ccg );
};case "\u0067":_cde ,_gee :=_baa .Attributes ["\u0066\u0069\u006c\u006c"];_gdeb ,_eef :=_baa .Attributes ["\u0073\u0074\u0072\u006f\u006b\u0065"];_adea ,_bgbe :=_baa .Attributes ["\u0073\u0074\u0072o\u006b\u0065\u002d\u0077\u0069\u0064\u0074\u0068"];_fga ,_dgc :=_baa .Attributes ["\u0074r\u0061\u006e\u0073\u0066\u006f\u0072m"];
for _ ,_bddc :=range _baa .Children {if _ ,_caaa :=_bddc .Attributes ["\u0066\u0069\u006c\u006c"];!_caaa &&_gee {_bddc .Attributes ["\u0066\u0069\u006c\u006c"]=_cde ;};if _ ,_aeba :=_bddc .Attributes ["\u0073\u0074\u0072\u006f\u006b\u0065"];!_aeba &&_eef {_bddc .Attributes ["\u0073\u0074\u0072\u006f\u006b\u0065"]=_gdeb ;
};if _ ,_gbd :=_bddc .Attributes ["\u0073\u0074\u0072o\u006b\u0065\u002d\u0077\u0069\u0064\u0074\u0068"];!_gbd &&_bgbe {_bddc .Attributes ["\u0073\u0074\u0072o\u006b\u0065\u002d\u0077\u0069\u0064\u0074\u0068"]=_adea ;};if _ ,_acga :=_bddc .Attributes ["\u0074r\u0061\u006e\u0073\u0066\u006f\u0072m"];
!_acga &&_dgc {_bddc .Attributes ["\u0074r\u0061\u006e\u0073\u0066\u006f\u0072m"]=_fga ;};_bddc .toContentStream (_deg ,_ccg );};};};func _gbf (_fgg *GraphicSVG ,_cdf *_g .ContentCreator ,_age *_fc .PdfPageResources ){_cdf .Add_q ();_fgg .Style .toContentStream (_cdf ,_age );
_bag ,_agd :=_dga (_fgg .Attributes ["\u0070\u006f\u0069\u006e\u0074\u0073"]);if _agd !=nil {_abg .Log .Debug ("\u0045\u0052\u0052O\u0052\u0020\u0075\u006e\u0061\u0062\u006c\u0065\u0020\u0074\u006f\u0020\u0070\u0061\u0072\u0073\u0065\u0020\u0070\u006f\u0069\u006e\u0074\u0073\u0020\u0061\u0074\u0074\u0072i\u0062\u0075\u0074\u0065\u003a\u0020\u0025\u0076",_agd );
return ;};if len (_bag )%2> 0{_abg .Log .Debug ("\u0045\u0052R\u004f\u0052\u0020\u0069n\u0076\u0061l\u0069\u0064\u0020\u0070\u006f\u0069\u006e\u0074s\u0020\u0061\u0074\u0074\u0072\u0069\u0062\u0075\u0074\u0065\u0020\u006ce\u006e\u0067\u0074\u0068");return ;
};for _gfe :=0;_gfe < len (_bag );{if _gfe ==0{_cdf .Add_m (_bag [_gfe ]*_fgg ._ddc ,_bag [_gfe +1]*_fgg ._ddc );}else {_cdf .Add_l (_bag [_gfe ]*_fgg ._ddc ,_bag [_gfe +1]*_fgg ._ddc );};_gfe +=2;};_cdf .Add_l (_bag [0]*_fgg ._ddc ,_bag [1]*_fgg ._ddc );
_fgg .Style .fillStroke (_cdf );_cdf .Add_h ();_cdf .Add_Q ();};func (_ebcd *GraphicSVGStyle )fillStroke (_cca *_g .ContentCreator ){if _ebcd .FillColor !=""&&_ebcd .StrokeColor !=""{_cca .Add_B ();}else if _ebcd .FillColor !=""{_cca .Add_f ();}else if _ebcd .StrokeColor !=""{_cca .Add_S ();
};};func ParseFromFile (path string )(*GraphicSVG ,error ){_abec ,_dbce :=_bb .Open (path );if _dbce !=nil {return nil ,_dbce ;};defer _abec .Close ();return ParseFromStream (_abec );};