//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package syncmap ;import _a "sync";func (_db *RuneSet )Write (r rune ){_db ._cc .Lock ();defer _db ._cc .Unlock ();_db ._cf [r ]=struct{}{}};func MakeRuneSet (length int )*RuneSet {return &RuneSet {_cf :make (map[rune ]struct{},length )}};func (_gg *RuneByteMap )Read (r rune )(byte ,bool ){_gg ._gb .RLock ();
defer _gg ._gb .RUnlock ();_ce ,_ada :=_gg ._fb [r ];return _ce ,_ada ;};func (_ggf *RuneStringMap )Length ()int {_ggf ._daa .RLock ();defer _ggf ._daa .RUnlock ();return len (_ggf ._gec );};func MakeRuneByteMap (length int )*RuneByteMap {_ad :=make (map[rune ]byte ,length );
return &RuneByteMap {_fb :_ad };};func (_dcf *StringsMap )Read (g string )(string ,bool ){_dcf ._ffg .RLock ();defer _dcf ._ffg .RUnlock ();_eeb ,_baf :=_dcf ._fbd [g ];return _eeb ,_baf ;};func (_fe *RuneByteMap )Length ()int {_fe ._gb .RLock ();defer _fe ._gb .RUnlock ();
return len (_fe ._fb )};type StringsMap struct{_fbd map[string ]string ;_ffg _a .RWMutex ;};func (_deb *RuneUint16Map )Delete (r rune ){_deb ._df .Lock ();defer _deb ._df .Unlock ();delete (_deb ._ff ,r );};func (_cfe *RuneSet )Length ()int {_cfe ._cc .RLock ();
defer _cfe ._cc .RUnlock ();return len (_cfe ._cf )};func (_adg *StringRuneMap )Read (g string )(rune ,bool ){_adg ._egb .RLock ();defer _adg ._egb .RUnlock ();_fg ,_dce :=_adg ._dac [g ];return _fg ,_dce ;};func (_gfdb *RuneUint16Map )Write (r rune ,g uint16 ){_gfdb ._df .Lock ();
defer _gfdb ._df .Unlock ();_gfdb ._ff [r ]=g ;};func (_da *RuneSet )Exists (r rune )bool {_da ._cc .RLock ();defer _da ._cc .RUnlock ();_ ,_gfc :=_da ._cf [r ];return _gfc ;};func (_adf *RuneUint16Map )Range (f func (_cg rune ,_ec uint16 )(_eca bool )){_adf ._df .RLock ();
defer _adf ._df .RUnlock ();for _ba ,_gbf :=range _adf ._ff {if f (_ba ,_gbf ){break ;};};};func (_bcc *RuneUint16Map )Read (r rune )(uint16 ,bool ){_bcc ._df .RLock ();defer _bcc ._df .RUnlock ();_beg ,_fc :=_bcc ._ff [r ];return _beg ,_fc ;};func (_gca *RuneUint16Map )Length ()int {_gca ._df .RLock ();
defer _gca ._df .RUnlock ();return len (_gca ._ff );};func (_dg *RuneStringMap )Read (r rune )(string ,bool ){_dg ._daa .RLock ();defer _dg ._daa .RUnlock ();_dbf ,_ga :=_dg ._gec [r ];return _dbf ,_ga ;};func (_ebe *StringsMap )Range (f func (_cca ,_bd string )(_aag bool )){_ebe ._ffg .RLock ();
defer _ebe ._ffg .RUnlock ();for _aeg ,_gga :=range _ebe ._fbd {if f (_aeg ,_gga ){break ;};};};type StringRuneMap struct{_dac map[string ]rune ;_egb _a .RWMutex ;};func (_ge *ByteRuneMap )Read (b byte )(rune ,bool ){_ge ._gf .RLock ();defer _ge ._gf .RUnlock ();
_d ,_ae :=_ge ._f [b ];return _d ,_ae ;};func NewRuneStringMap (m map[rune ]string )*RuneStringMap {return &RuneStringMap {_gec :m }};type RuneStringMap struct{_gec map[rune ]string ;_daa _a .RWMutex ;};func (_af *RuneStringMap )Write (r rune ,s string ){_af ._daa .Lock ();
defer _af ._daa .Unlock ();_af ._gec [r ]=s ;};func (_bcb *StringsMap )Write (g1 ,g2 string ){_bcb ._ffg .Lock ();defer _bcb ._ffg .Unlock ();_bcb ._fbd [g1 ]=g2 ;};type RuneSet struct{_cf map[rune ]struct{};_cc _a .RWMutex ;};func (_be *ByteRuneMap )Range (f func (_de byte ,_ab rune )(_gd bool )){_be ._gf .RLock ();
defer _be ._gf .RUnlock ();for _c ,_ca :=range _be ._f {if f (_c ,_ca ){break ;};};};func (_ee *RuneByteMap )Write (r rune ,b byte ){_ee ._gb .Lock ();defer _ee ._gb .Unlock ();_ee ._fb [r ]=b };func (_b *ByteRuneMap )Write (b byte ,r rune ){_b ._gf .Lock ();
defer _b ._gf .Unlock ();_b ._f [b ]=r };func (_e *ByteRuneMap )Length ()int {_e ._gf .RLock ();defer _e ._gf .RUnlock ();return len (_e ._f )};type RuneUint16Map struct{_ff map[rune ]uint16 ;_df _a .RWMutex ;};func (_geg *RuneSet )Range (f func (_eea rune )(_aa bool )){_geg ._cc .RLock ();
defer _geg ._cc .RUnlock ();for _bg :=range _geg ._cf {if f (_bg ){break ;};};};func (_fcf *StringsMap )Copy ()*StringsMap {_fcf ._ffg .RLock ();defer _fcf ._ffg .RUnlock ();_fbdb :=map[string ]string {};for _fgd ,_fbf :=range _fcf ._fbd {_fbdb [_fgd ]=_fbf ;
};return &StringsMap {_fbd :_fbdb };};func NewStringsMap (tuples []StringsTuple )*StringsMap {_ade :=map[string ]string {};for _ ,_ced :=range tuples {_ade [_ced .Key ]=_ced .Value ;};return &StringsMap {_fbd :_ade };};func MakeRuneUint16Map (length int )*RuneUint16Map {return &RuneUint16Map {_ff :make (map[rune ]uint16 ,length )};
};func NewStringRuneMap (m map[string ]rune )*StringRuneMap {return &StringRuneMap {_dac :m }};func (_gbg *StringRuneMap )Range (f func (_dec string ,_dbg rune )(_bce bool )){_gbg ._egb .RLock ();defer _gbg ._egb .RUnlock ();for _dgg ,_ceg :=range _gbg ._dac {if f (_dgg ,_ceg ){break ;
};};};func (_edg *StringRuneMap )Length ()int {_edg ._egb .RLock ();defer _edg ._egb .RUnlock ();return len (_edg ._dac );};type ByteRuneMap struct{_f map[byte ]rune ;_gf _a .RWMutex ;};type RuneByteMap struct{_fb map[rune ]byte ;_gb _a .RWMutex ;};func (_eb *StringRuneMap )Write (g string ,r rune ){_eb ._egb .Lock ();
defer _eb ._egb .Unlock ();_eb ._dac [g ]=r ;};func NewByteRuneMap (m map[byte ]rune )*ByteRuneMap {return &ByteRuneMap {_f :m }};func (_bf *RuneByteMap )Range (f func (_gfd rune ,_aba byte )(_eg bool )){_bf ._gb .RLock ();defer _bf ._gb .RUnlock ();for _gc ,_ed :=range _bf ._fb {if f (_gc ,_ed ){break ;
};};};func MakeByteRuneMap (length int )*ByteRuneMap {return &ByteRuneMap {_f :make (map[byte ]rune ,length )}};func (_dbb *RuneUint16Map )RangeDelete (f func (_gdc rune ,_cac uint16 )(_ea bool ,_bff bool )){_dbb ._df .Lock ();defer _dbb ._df .Unlock ();
for _dc ,_fee :=range _dbb ._ff {_fa ,_bb :=f (_dc ,_fee );if _fa {delete (_dbb ._ff ,_dc );};if _bb {break ;};};};func (_eec *RuneStringMap )Range (f func (_gbe rune ,_deg string )(_ggc bool )){_eec ._daa .RLock ();defer _eec ._daa .RUnlock ();for _eee ,_afb :=range _eec ._gec {if f (_eee ,_afb ){break ;
};};};type StringsTuple struct{Key ,Value string ;};