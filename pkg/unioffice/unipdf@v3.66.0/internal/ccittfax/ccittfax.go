//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package ccittfax ;import (_a "errors";_ee "github.com/unidoc/unipdf/v3/internal/bitwise";_ea "io";_ae "math";);func (_cde *Decoder )looseFetchEOL ()(bool ,error ){_gba ,_ead :=_cde ._bdb .ReadBits (12);if _ead !=nil {return false ,_ead ;};switch _gba {case 0x1:return true ,nil ;
case 0x0:for {_ecb ,_bgcf :=_cde ._bdb .ReadBool ();if _bgcf !=nil {return false ,_bgcf ;};if _ecb {return true ,nil ;};};default:return false ,nil ;};};func init (){_b =&treeNode {_bgcc :true ,_gefd :_eed };_g =&treeNode {_gefd :_ab ,_feea :_b };_g ._efc =_g ;
_ge =&tree {_fad :&treeNode {}};if _bc :=_ge .fillWithNode (12,0,_g );_bc !=nil {panic (_bc .Error ());};if _f :=_ge .fillWithNode (12,1,_b );_f !=nil {panic (_f .Error ());};_eeb =&tree {_fad :&treeNode {}};for _abb :=0;_abb < len (_ad );_abb ++{for _ba :=0;
_ba < len (_ad [_abb ]);_ba ++{if _gg :=_eeb .fill (_abb +2,int (_ad [_abb ][_ba ]),int (_bb [_abb ][_ba ]));_gg !=nil {panic (_gg .Error ());};};};if _fb :=_eeb .fillWithNode (12,0,_g );_fb !=nil {panic (_fb .Error ());};if _gd :=_eeb .fillWithNode (12,1,_b );
_gd !=nil {panic (_gd .Error ());};_eg =&tree {_fad :&treeNode {}};for _bca :=0;_bca < len (_abbg );_bca ++{for _aeg :=0;_aeg < len (_abbg [_bca ]);_aeg ++{if _af :=_eg .fill (_bca +4,int (_abbg [_bca ][_aeg ]),int (_eee [_bca ][_aeg ]));_af !=nil {panic (_af .Error ());
};};};if _bd :=_eg .fillWithNode (12,0,_g );_bd !=nil {panic (_bd .Error ());};if _bdg :=_eg .fillWithNode (12,1,_b );_bdg !=nil {panic (_bdg .Error ());};_ed =&tree {_fad :&treeNode {}};if _de :=_ed .fill (4,1,_bf );_de !=nil {panic (_de .Error ());};
if _ce :=_ed .fill (3,1,_d );_ce !=nil {panic (_ce .Error ());};if _fc :=_ed .fill (1,1,0);_fc !=nil {panic (_fc .Error ());};if _gdc :=_ed .fill (3,3,1);_gdc !=nil {panic (_gdc .Error ());};if _fg :=_ed .fill (6,3,2);_fg !=nil {panic (_fg .Error ());};
if _df :=_ed .fill (7,3,3);_df !=nil {panic (_df .Error ());};if _fba :=_ed .fill (3,2,-1);_fba !=nil {panic (_fba .Error ());};if _cb :=_ed .fill (6,2,-2);_cb !=nil {panic (_cb .Error ());};if _db :=_ed .fill (7,2,-3);_db !=nil {panic (_db .Error ());
};};func _afe (_cfb ,_ecgb []byte ,_eagc int )int {_aaga :=_gga (_ecgb ,_eagc );if _aaga < len (_ecgb )&&(_eagc ==-1&&_ecgb [_aaga ]==_bec ||_eagc >=0&&_eagc < len (_cfb )&&_cfb [_eagc ]==_ecgb [_aaga ]||_eagc >=len (_cfb )&&_cfb [_eagc -1]!=_ecgb [_aaga ]){_aaga =_gga (_ecgb ,_aaga );
};return _aaga ;};func (_bdbg *Decoder )decodeRow ()(_fa error ){if !_bdbg ._fd &&_bdbg ._bdd > 0&&_bdbg ._bdd ==_bdbg ._cbec {return _ea .EOF ;};switch _bdbg ._dea {case _eab :_fa =_bdbg .decodeRowType2 ();case _dab :_fa =_bdbg .decodeRowType4 ();case _bdgb :_fa =_bdbg .decodeRowType6 ();
};if _fa !=nil {return _fa ;};_fec :=0;_fag :=true ;_bdbg ._cgc =0;for _cgg :=0;_cgg < _bdbg ._cdg ;_cgg ++{_abf :=_bdbg ._bcd ;if _cgg !=_bdbg ._cdg {_abf =_bdbg ._cd [_cgg ];};if _abf > _bdbg ._bcd {_abf =_bdbg ._bcd ;};_eda :=_fec /8;for _fec %8!=0&&_abf -_fec > 0{var _bfee byte ;
if !_fag {_bfee =1<<uint (7-(_fec %8));};_bdbg ._bfe [_eda ]|=_bfee ;_fec ++;};if _fec %8==0{_eda =_fec /8;var _ffd byte ;if !_fag {_ffd =0xff;};for _abf -_fec > 7{_bdbg ._bfe [_eda ]=_ffd ;_fec +=8;_eda ++;};};for _abf -_fec > 0{if _fec %8==0{_bdbg ._bfe [_eda ]=0;
};var _bbb byte ;if !_fag {_bbb =1<<uint (7-(_fec %8));};_bdbg ._bfe [_eda ]|=_bbb ;_fec ++;};_fag =!_fag ;};if _fec !=_bdbg ._bcd {return _a .New ("\u0073\u0075\u006d\u0020\u006f\u0066 \u0072\u0075\u006e\u002d\u006c\u0065\u006e\u0067\u0074\u0068\u0073\u0020\u0064\u006f\u0065\u0073\u0020\u006e\u006f\u0074 \u0065\u0071\u0075\u0061\u006c\u0020\u0073\u0063\u0061\u006e\u0020\u006c\u0069\u006ee\u0020w\u0069\u0064\u0074\u0068");
};_bdbg ._ade =(_fec +7)/8;_bdbg ._cbec ++;return nil ;};func (_cdec *Encoder )appendEncodedRow (_ecc ,_cdfa []byte ,_cdbd int )[]byte {if len (_ecc )> 0&&_cdbd !=0&&!_cdec .EncodedByteAlign {_ecc [len (_ecc )-1]=_ecc [len (_ecc )-1]|_cdfa [0];_ecc =append (_ecc ,_cdfa [1:]...);
}else {_ecc =append (_ecc ,_cdfa ...);};return _ecc ;};var (_abd =_a .New ("\u0063\u0063\u0069\u0074tf\u0061\u0078\u0020\u0063\u006f\u0072\u0072\u0075\u0070\u0074\u0065\u0064\u0020\u0052T\u0043");_ac =_a .New ("\u0063\u0063\u0069\u0074tf\u0061\u0078\u0020\u0045\u004f\u004c\u0020\u006e\u006f\u0074\u0020\u0066\u006f\u0075n\u0064");
);const (_ tiffType =iota ;_eab ;_dab ;_bdgb ;);type tree struct{_fad *treeNode };func init (){_da =make (map[int ]code );_da [0]=code {Code :13<<8|3<<6,BitsWritten :10};_da [1]=code {Code :2<<(5+8),BitsWritten :3};_da [2]=code {Code :3<<(6+8),BitsWritten :2};
_da [3]=code {Code :2<<(6+8),BitsWritten :2};_da [4]=code {Code :3<<(5+8),BitsWritten :3};_da [5]=code {Code :3<<(4+8),BitsWritten :4};_da [6]=code {Code :2<<(4+8),BitsWritten :4};_da [7]=code {Code :3<<(3+8),BitsWritten :5};_da [8]=code {Code :5<<(2+8),BitsWritten :6};
_da [9]=code {Code :4<<(2+8),BitsWritten :6};_da [10]=code {Code :4<<(1+8),BitsWritten :7};_da [11]=code {Code :5<<(1+8),BitsWritten :7};_da [12]=code {Code :7<<(1+8),BitsWritten :7};_da [13]=code {Code :4<<8,BitsWritten :8};_da [14]=code {Code :7<<8,BitsWritten :8};
_da [15]=code {Code :12<<8,BitsWritten :9};_da [16]=code {Code :5<<8|3<<6,BitsWritten :10};_da [17]=code {Code :6<<8,BitsWritten :10};_da [18]=code {Code :2<<8,BitsWritten :10};_da [19]=code {Code :12<<8|7<<5,BitsWritten :11};_da [20]=code {Code :13<<8,BitsWritten :11};
_da [21]=code {Code :13<<8|4<<5,BitsWritten :11};_da [22]=code {Code :6<<8|7<<5,BitsWritten :11};_da [23]=code {Code :5<<8,BitsWritten :11};_da [24]=code {Code :2<<8|7<<5,BitsWritten :11};_da [25]=code {Code :3<<8,BitsWritten :11};_da [26]=code {Code :12<<8|10<<4,BitsWritten :12};
_da [27]=code {Code :12<<8|11<<4,BitsWritten :12};_da [28]=code {Code :12<<8|12<<4,BitsWritten :12};_da [29]=code {Code :12<<8|13<<4,BitsWritten :12};_da [30]=code {Code :6<<8|8<<4,BitsWritten :12};_da [31]=code {Code :6<<8|9<<4,BitsWritten :12};_da [32]=code {Code :6<<8|10<<4,BitsWritten :12};
_da [33]=code {Code :6<<8|11<<4,BitsWritten :12};_da [34]=code {Code :13<<8|2<<4,BitsWritten :12};_da [35]=code {Code :13<<8|3<<4,BitsWritten :12};_da [36]=code {Code :13<<8|4<<4,BitsWritten :12};_da [37]=code {Code :13<<8|5<<4,BitsWritten :12};_da [38]=code {Code :13<<8|6<<4,BitsWritten :12};
_da [39]=code {Code :13<<8|7<<4,BitsWritten :12};_da [40]=code {Code :6<<8|12<<4,BitsWritten :12};_da [41]=code {Code :6<<8|13<<4,BitsWritten :12};_da [42]=code {Code :13<<8|10<<4,BitsWritten :12};_da [43]=code {Code :13<<8|11<<4,BitsWritten :12};_da [44]=code {Code :5<<8|4<<4,BitsWritten :12};
_da [45]=code {Code :5<<8|5<<4,BitsWritten :12};_da [46]=code {Code :5<<8|6<<4,BitsWritten :12};_da [47]=code {Code :5<<8|7<<4,BitsWritten :12};_da [48]=code {Code :6<<8|4<<4,BitsWritten :12};_da [49]=code {Code :6<<8|5<<4,BitsWritten :12};_da [50]=code {Code :5<<8|2<<4,BitsWritten :12};
_da [51]=code {Code :5<<8|3<<4,BitsWritten :12};_da [52]=code {Code :2<<8|4<<4,BitsWritten :12};_da [53]=code {Code :3<<8|7<<4,BitsWritten :12};_da [54]=code {Code :3<<8|8<<4,BitsWritten :12};_da [55]=code {Code :2<<8|7<<4,BitsWritten :12};_da [56]=code {Code :2<<8|8<<4,BitsWritten :12};
_da [57]=code {Code :5<<8|8<<4,BitsWritten :12};_da [58]=code {Code :5<<8|9<<4,BitsWritten :12};_da [59]=code {Code :2<<8|11<<4,BitsWritten :12};_da [60]=code {Code :2<<8|12<<4,BitsWritten :12};_da [61]=code {Code :5<<8|10<<4,BitsWritten :12};_da [62]=code {Code :6<<8|6<<4,BitsWritten :12};
_da [63]=code {Code :6<<8|7<<4,BitsWritten :12};_abc =make (map[int ]code );_abc [0]=code {Code :53<<8,BitsWritten :8};_abc [1]=code {Code :7<<(2+8),BitsWritten :6};_abc [2]=code {Code :7<<(4+8),BitsWritten :4};_abc [3]=code {Code :8<<(4+8),BitsWritten :4};
_abc [4]=code {Code :11<<(4+8),BitsWritten :4};_abc [5]=code {Code :12<<(4+8),BitsWritten :4};_abc [6]=code {Code :14<<(4+8),BitsWritten :4};_abc [7]=code {Code :15<<(4+8),BitsWritten :4};_abc [8]=code {Code :19<<(3+8),BitsWritten :5};_abc [9]=code {Code :20<<(3+8),BitsWritten :5};
_abc [10]=code {Code :7<<(3+8),BitsWritten :5};_abc [11]=code {Code :8<<(3+8),BitsWritten :5};_abc [12]=code {Code :8<<(2+8),BitsWritten :6};_abc [13]=code {Code :3<<(2+8),BitsWritten :6};_abc [14]=code {Code :52<<(2+8),BitsWritten :6};_abc [15]=code {Code :53<<(2+8),BitsWritten :6};
_abc [16]=code {Code :42<<(2+8),BitsWritten :6};_abc [17]=code {Code :43<<(2+8),BitsWritten :6};_abc [18]=code {Code :39<<(1+8),BitsWritten :7};_abc [19]=code {Code :12<<(1+8),BitsWritten :7};_abc [20]=code {Code :8<<(1+8),BitsWritten :7};_abc [21]=code {Code :23<<(1+8),BitsWritten :7};
_abc [22]=code {Code :3<<(1+8),BitsWritten :7};_abc [23]=code {Code :4<<(1+8),BitsWritten :7};_abc [24]=code {Code :40<<(1+8),BitsWritten :7};_abc [25]=code {Code :43<<(1+8),BitsWritten :7};_abc [26]=code {Code :19<<(1+8),BitsWritten :7};_abc [27]=code {Code :36<<(1+8),BitsWritten :7};
_abc [28]=code {Code :24<<(1+8),BitsWritten :7};_abc [29]=code {Code :2<<8,BitsWritten :8};_abc [30]=code {Code :3<<8,BitsWritten :8};_abc [31]=code {Code :26<<8,BitsWritten :8};_abc [32]=code {Code :27<<8,BitsWritten :8};_abc [33]=code {Code :18<<8,BitsWritten :8};
_abc [34]=code {Code :19<<8,BitsWritten :8};_abc [35]=code {Code :20<<8,BitsWritten :8};_abc [36]=code {Code :21<<8,BitsWritten :8};_abc [37]=code {Code :22<<8,BitsWritten :8};_abc [38]=code {Code :23<<8,BitsWritten :8};_abc [39]=code {Code :40<<8,BitsWritten :8};
_abc [40]=code {Code :41<<8,BitsWritten :8};_abc [41]=code {Code :42<<8,BitsWritten :8};_abc [42]=code {Code :43<<8,BitsWritten :8};_abc [43]=code {Code :44<<8,BitsWritten :8};_abc [44]=code {Code :45<<8,BitsWritten :8};_abc [45]=code {Code :4<<8,BitsWritten :8};
_abc [46]=code {Code :5<<8,BitsWritten :8};_abc [47]=code {Code :10<<8,BitsWritten :8};_abc [48]=code {Code :11<<8,BitsWritten :8};_abc [49]=code {Code :82<<8,BitsWritten :8};_abc [50]=code {Code :83<<8,BitsWritten :8};_abc [51]=code {Code :84<<8,BitsWritten :8};
_abc [52]=code {Code :85<<8,BitsWritten :8};_abc [53]=code {Code :36<<8,BitsWritten :8};_abc [54]=code {Code :37<<8,BitsWritten :8};_abc [55]=code {Code :88<<8,BitsWritten :8};_abc [56]=code {Code :89<<8,BitsWritten :8};_abc [57]=code {Code :90<<8,BitsWritten :8};
_abc [58]=code {Code :91<<8,BitsWritten :8};_abc [59]=code {Code :74<<8,BitsWritten :8};_abc [60]=code {Code :75<<8,BitsWritten :8};_abc [61]=code {Code :50<<8,BitsWritten :8};_abc [62]=code {Code :51<<8,BitsWritten :8};_abc [63]=code {Code :52<<8,BitsWritten :8};
_cg =make (map[int ]code );_cg [64]=code {Code :3<<8|3<<6,BitsWritten :10};_cg [128]=code {Code :12<<8|8<<4,BitsWritten :12};_cg [192]=code {Code :12<<8|9<<4,BitsWritten :12};_cg [256]=code {Code :5<<8|11<<4,BitsWritten :12};_cg [320]=code {Code :3<<8|3<<4,BitsWritten :12};
_cg [384]=code {Code :3<<8|4<<4,BitsWritten :12};_cg [448]=code {Code :3<<8|5<<4,BitsWritten :12};_cg [512]=code {Code :3<<8|12<<3,BitsWritten :13};_cg [576]=code {Code :3<<8|13<<3,BitsWritten :13};_cg [640]=code {Code :2<<8|10<<3,BitsWritten :13};_cg [704]=code {Code :2<<8|11<<3,BitsWritten :13};
_cg [768]=code {Code :2<<8|12<<3,BitsWritten :13};_cg [832]=code {Code :2<<8|13<<3,BitsWritten :13};_cg [896]=code {Code :3<<8|18<<3,BitsWritten :13};_cg [960]=code {Code :3<<8|19<<3,BitsWritten :13};_cg [1024]=code {Code :3<<8|20<<3,BitsWritten :13};_cg [1088]=code {Code :3<<8|21<<3,BitsWritten :13};
_cg [1152]=code {Code :3<<8|22<<3,BitsWritten :13};_cg [1216]=code {Code :119<<3,BitsWritten :13};_cg [1280]=code {Code :2<<8|18<<3,BitsWritten :13};_cg [1344]=code {Code :2<<8|19<<3,BitsWritten :13};_cg [1408]=code {Code :2<<8|20<<3,BitsWritten :13};_cg [1472]=code {Code :2<<8|21<<3,BitsWritten :13};
_cg [1536]=code {Code :2<<8|26<<3,BitsWritten :13};_cg [1600]=code {Code :2<<8|27<<3,BitsWritten :13};_cg [1664]=code {Code :3<<8|4<<3,BitsWritten :13};_cg [1728]=code {Code :3<<8|5<<3,BitsWritten :13};_ga =make (map[int ]code );_ga [64]=code {Code :27<<(3+8),BitsWritten :5};
_ga [128]=code {Code :18<<(3+8),BitsWritten :5};_ga [192]=code {Code :23<<(2+8),BitsWritten :6};_ga [256]=code {Code :55<<(1+8),BitsWritten :7};_ga [320]=code {Code :54<<8,BitsWritten :8};_ga [384]=code {Code :55<<8,BitsWritten :8};_ga [448]=code {Code :100<<8,BitsWritten :8};
_ga [512]=code {Code :101<<8,BitsWritten :8};_ga [576]=code {Code :104<<8,BitsWritten :8};_ga [640]=code {Code :103<<8,BitsWritten :8};_ga [704]=code {Code :102<<8,BitsWritten :9};_ga [768]=code {Code :102<<8|1<<7,BitsWritten :9};_ga [832]=code {Code :105<<8,BitsWritten :9};
_ga [896]=code {Code :105<<8|1<<7,BitsWritten :9};_ga [960]=code {Code :106<<8,BitsWritten :9};_ga [1024]=code {Code :106<<8|1<<7,BitsWritten :9};_ga [1088]=code {Code :107<<8,BitsWritten :9};_ga [1152]=code {Code :107<<8|1<<7,BitsWritten :9};_ga [1216]=code {Code :108<<8,BitsWritten :9};
_ga [1280]=code {Code :108<<8|1<<7,BitsWritten :9};_ga [1344]=code {Code :109<<8,BitsWritten :9};_ga [1408]=code {Code :109<<8|1<<7,BitsWritten :9};_ga [1472]=code {Code :76<<8,BitsWritten :9};_ga [1536]=code {Code :76<<8|1<<7,BitsWritten :9};_ga [1600]=code {Code :77<<8,BitsWritten :9};
_ga [1664]=code {Code :24<<(2+8),BitsWritten :6};_ga [1728]=code {Code :77<<8|1<<7,BitsWritten :9};_bg =make (map[int ]code );_bg [1792]=code {Code :1<<8,BitsWritten :11};_bg [1856]=code {Code :1<<8|4<<5,BitsWritten :11};_bg [1920]=code {Code :1<<8|5<<5,BitsWritten :11};
_bg [1984]=code {Code :1<<8|2<<4,BitsWritten :12};_bg [2048]=code {Code :1<<8|3<<4,BitsWritten :12};_bg [2112]=code {Code :1<<8|4<<4,BitsWritten :12};_bg [2176]=code {Code :1<<8|5<<4,BitsWritten :12};_bg [2240]=code {Code :1<<8|6<<4,BitsWritten :12};_bg [2304]=code {Code :1<<8|7<<4,BitsWritten :12};
_bg [2368]=code {Code :1<<8|12<<4,BitsWritten :12};_bg [2432]=code {Code :1<<8|13<<4,BitsWritten :12};_bg [2496]=code {Code :1<<8|14<<4,BitsWritten :12};_bg [2560]=code {Code :1<<8|15<<4,BitsWritten :12};_cge =make (map[int ]byte );_cge [0]=0xFF;_cge [1]=0xFE;
_cge [2]=0xFC;_cge [3]=0xF8;_cge [4]=0xF0;_cge [5]=0xE0;_cge [6]=0xC0;_cge [7]=0x80;_cge [8]=0x00;};func (_dbg *tree )fillWithNode (_fagcd ,_ffcf int ,_ccbe *treeNode )error {_fdbb :=_dbg ._fad ;for _aee :=0;_aee < _fagcd ;_aee ++{_edcg :=uint (_fagcd -1-_aee );
_edgd :=((_ffcf >>_edcg )&1)!=0;_bgde :=_fdbb .walk (_edgd );if _bgde !=nil {if _bgde ._bgcc {return _a .New ("\u006e\u006f\u0064\u0065\u0020\u0069\u0073\u0020\u006c\u0065\u0061\u0066\u002c\u0020\u006eo\u0020o\u0074\u0068\u0065\u0072\u0020\u0066\u006f\u006c\u006c\u006f\u0077\u0069\u006e\u0067");
};_fdbb =_bgde ;continue ;};if _aee ==_fagcd -1{_bgde =_ccbe ;}else {_bgde =&treeNode {};};if _ffcf ==0{_bgde ._fcfa =true ;};_fdbb .set (_edgd ,_bgde );_fdbb =_bgde ;};return nil ;};func _ddbd (_dae []byte ,_eag int ,_afg code )([]byte ,int ){_ecef :=0;
for _ecef < _afg .BitsWritten {_fbg :=_eag /8;_bgda :=_eag %8;if _fbg >=len (_dae ){_dae =append (_dae ,0);};_edce :=8-_bgda ;_cgba :=_afg .BitsWritten -_ecef ;if _edce > _cgba {_edce =_cgba ;};if _ecef < 8{_dae [_fbg ]=_dae [_fbg ]|byte (_afg .Code >>uint (8+_bgda -_ecef ))&_cge [8-_edce -_bgda ];
}else {_dae [_fbg ]=_dae [_fbg ]|(byte (_afg .Code <<uint (_ecef -8))&_cge [8-_edce ])>>uint (_bgda );};_eag +=_edce ;_ecef +=_edce ;};return _dae ,_eag ;};var _eee =[...][]uint16 {{2,3,4,5,6,7},{128,8,9,64,10,11},{192,1664,16,17,13,14,15,1,12},{26,21,28,27,18,24,25,22,256,23,20,19},{33,34,35,36,37,38,31,32,29,53,54,39,40,41,42,43,44,30,61,62,63,0,320,384,45,59,60,46,49,50,51,52,55,56,57,58,448,512,640,576,47,48},{1472,1536,1600,1728,704,768,832,896,960,1024,1088,1152,1216,1280,1344,1408},{},{1792,1856,1920},{1984,2048,2112,2176,2240,2304,2368,2432,2496,2560}};
var (_da map[int ]code ;_abc map[int ]code ;_cg map[int ]code ;_ga map[int ]code ;_bg map[int ]code ;_cge map[int ]byte ;_gc =code {Code :1<<4,BitsWritten :12};_fe =code {Code :3<<3,BitsWritten :13};_fbe =code {Code :2<<3,BitsWritten :13};_dff =code {Code :1<<12,BitsWritten :4};
_dbb =code {Code :1<<13,BitsWritten :3};_fgf =code {Code :1<<15,BitsWritten :1};_bbe =code {Code :3<<13,BitsWritten :3};_ff =code {Code :3<<10,BitsWritten :6};_ec =code {Code :3<<9,BitsWritten :7};_eaa =code {Code :2<<13,BitsWritten :3};_cgd =code {Code :2<<10,BitsWritten :6};
_fgff =code {Code :2<<9,BitsWritten :7};);type Decoder struct{_bcd int ;_bdd int ;_cbec int ;_bfe []byte ;_egd int ;_fcc bool ;_dac bool ;_acf bool ;_edc bool ;_eca bool ;_fd bool ;_gea bool ;_ade int ;_fbc int ;_cf []int ;_cd []int ;_cdd int ;_cdg int ;
_baf int ;_cgc int ;_bdb *_ee .Reader ;_dea tiffType ;_dee error ;};func _dcd (_fagc []byte ,_ddbc ,_fbgf ,_agef int )([]byte ,int ){_cggd :=_dgc (_fbgf ,_agef );_fagc ,_ddbc =_ddbd (_fagc ,_ddbc ,_cggd );return _fagc ,_ddbc ;};func (_bae *Decoder )decodeG32D ()error {_bae ._cdd =_bae ._cdg ;
_bae ._cd ,_bae ._cf =_bae ._cf ,_bae ._cd ;_cc :=true ;var (_gdf bool ;_fae int ;_bab error ;);_bae ._cdg =0;_fcg :for _fae < _bae ._bcd {_gcf :=_ed ._fad ;for {_gdf ,_bab =_bae ._bdb .ReadBool ();if _bab !=nil {return _bab ;};_gcf =_gcf .walk (_gdf );
if _gcf ==nil {continue _fcg ;};if !_gcf ._bgcc {continue ;};switch _gcf ._gefd {case _d :var _ag int ;if _cc {_ag ,_bab =_bae .decodeRun (_eg );}else {_ag ,_bab =_bae .decodeRun (_eeb );};if _bab !=nil {return _bab ;};_fae +=_ag ;_bae ._cd [_bae ._cdg ]=_fae ;
_bae ._cdg ++;if _cc {_ag ,_bab =_bae .decodeRun (_eeb );}else {_ag ,_bab =_bae .decodeRun (_eg );};if _bab !=nil {return _bab ;};_fae +=_ag ;_bae ._cd [_bae ._cdg ]=_fae ;_bae ._cdg ++;case _bf :_age :=_bae .getNextChangingElement (_fae ,_cc )+1;if _age >=_bae ._cdd {_fae =_bae ._bcd ;
}else {_fae =_bae ._cf [_age ];};default:_dbf :=_bae .getNextChangingElement (_fae ,_cc );if _dbf >=_bae ._cdd ||_dbf ==-1{_fae =_bae ._bcd +_gcf ._gefd ;}else {_fae =_bae ._cf [_dbf ]+_gcf ._gefd ;};_bae ._cd [_bae ._cdg ]=_fae ;_bae ._cdg ++;_cc =!_cc ;
};continue _fcg ;};};return nil ;};func _baga (_bbd int ,_dfb bool )(code ,int ,bool ){if _bbd < 64{if _dfb {return _abc [_bbd ],0,true ;};return _da [_bbd ],0,true ;};_bbeb :=_bbd /64;if _bbeb > 40{return _bg [2560],_bbd -2560,false ;};if _bbeb > 27{return _bg [_bbeb *64],_bbd -_bbeb *64,false ;
};if _dfb {return _ga [_bbeb *64],_bbd -_bbeb *64,false ;};return _cg [_bbeb *64],_bbd -_bbeb *64,false ;};func (_gab *Decoder )decodeRowType2 ()error {if _gab ._gea {_gab ._bdb .Align ();};if _cgea :=_gab .decode1D ();_cgea !=nil {return _cgea ;};return nil ;
};func _afcc (_gbd []byte ,_gaa bool ,_efg int )(int ,int ){_daf :=0;for _efg < len (_gbd ){if _gaa {if _gbd [_efg ]!=_bec {break ;};}else {if _gbd [_efg ]!=_eef {break ;};};_daf ++;_efg ++;};return _daf ,_efg ;};func _bgcb (_bdec int )([]byte ,int ){var _daca []byte ;
for _gf :=0;_gf < 6;_gf ++{_daca ,_bdec =_ddbd (_daca ,_bdec ,_gc );};return _daca ,_bdec %8;};func (_dde *Decoder )decode2D ()error {_dde ._cdd =_dde ._cdg ;_dde ._cd ,_dde ._cf =_dde ._cf ,_dde ._cd ;_daa :=true ;var (_ccd bool ;_fde int ;_eaac error ;
);_dde ._cdg =0;_fdb :for _fde < _dde ._bcd {_adc :=_ed ._fad ;for {_ccd ,_eaac =_dde ._bdb .ReadBool ();if _eaac !=nil {return _eaac ;};_adc =_adc .walk (_ccd );if _adc ==nil {continue _fdb ;};if !_adc ._bgcc {continue ;};switch _adc ._gefd {case _d :var _acb int ;
if _daa {_acb ,_eaac =_dde .decodeRun (_eg );}else {_acb ,_eaac =_dde .decodeRun (_eeb );};if _eaac !=nil {return _eaac ;};_fde +=_acb ;_dde ._cd [_dde ._cdg ]=_fde ;_dde ._cdg ++;if _daa {_acb ,_eaac =_dde .decodeRun (_eeb );}else {_acb ,_eaac =_dde .decodeRun (_eg );
};if _eaac !=nil {return _eaac ;};_fde +=_acb ;_dde ._cd [_dde ._cdg ]=_fde ;_dde ._cdg ++;case _bf :_aab :=_dde .getNextChangingElement (_fde ,_daa )+1;if _aab >=_dde ._cdd {_fde =_dde ._bcd ;}else {_fde =_dde ._cf [_aab ];};default:_fccf :=_dde .getNextChangingElement (_fde ,_daa );
if _fccf >=_dde ._cdd ||_fccf ==-1{_fde =_dde ._bcd +_adc ._gefd ;}else {_fde =_dde ._cf [_fccf ]+_adc ._gefd ;};_dde ._cd [_dde ._cdg ]=_fde ;_dde ._cdg ++;_daa =!_daa ;};continue _fdb ;};};return nil ;};func _bbeda (_fcgb ,_eff []byte ,_eea int ,_faa bool )int {_fbcb :=_gga (_eff ,_eea );
if _fbcb < len (_eff )&&(_eea ==-1&&_eff [_fbcb ]==_bec ||_eea >=0&&_eea < len (_fcgb )&&_fcgb [_eea ]==_eff [_fbcb ]||_eea >=len (_fcgb )&&_faa &&_eff [_fbcb ]==_bec ||_eea >=len (_fcgb )&&!_faa &&_eff [_fbcb ]==_eef ){_fbcb =_gga (_eff ,_fbcb );};return _fbcb ;
};func (_cbe tiffType )String ()string {switch _cbe {case _eab :return "\u0074\u0069\u0066\u0066\u0054\u0079\u0070\u0065\u004d\u006f\u0064i\u0066\u0069\u0065\u0064\u0048\u0075\u0066\u0066\u006d\u0061n\u0052\u006c\u0065";case _dab :return "\u0074\u0069\u0066\u0066\u0054\u0079\u0070\u0065\u0054\u0034";
case _bdgb :return "\u0074\u0069\u0066\u0066\u0054\u0079\u0070\u0065\u0054\u0036";default:return "\u0075n\u0064\u0065\u0066\u0069\u006e\u0065d";};};func (_ecd *Decoder )decodeRowType4 ()error {if !_ecd ._fcc {return _ecd .decoderRowType41D ();};if _ecd ._gea {_ecd ._bdb .Align ();
};_ecd ._bdb .Mark ();_dag ,_dbe :=_ecd .tryFetchEOL ();if _dbe !=nil {return _dbe ;};if !_dag &&_ecd ._eca {_ecd ._baf ++;if _ecd ._baf > _ecd ._egd {return _ac ;};_ecd ._bdb .Reset ();};if !_dag {_ecd ._bdb .Reset ();};_eaf ,_dbe :=_ecd ._bdb .ReadBool ();
if _dbe !=nil {return _dbe ;};if _eaf {if _dag &&_ecd ._fd {if _dbe =_ecd .tryFetchRTC2D ();_dbe !=nil {return _dbe ;};};_dbe =_ecd .decode1D ();}else {_dbe =_ecd .decode2D ();};if _dbe !=nil {return _dbe ;};return nil ;};func (_aa *Decoder )fetch ()error {if _aa ._ade ==-1{return nil ;
};if _aa ._fbc < _aa ._ade {return nil ;};_aa ._ade =0;_bgg :=_aa .decodeRow ();if _bgg !=nil {if !_a .Is (_bgg ,_ea .EOF ){return _bgg ;};if _aa ._ade !=0{return _bgg ;};_aa ._ade =-1;};_aa ._fbc =0;return nil ;};func (_bgce *Encoder )encodeG31D (_bgdb [][]byte )[]byte {var _eafc []byte ;
_bbfg :=0;for _aca :=range _bgdb {if _bgce .Rows > 0&&!_bgce .EndOfBlock &&_aca ==_bgce .Rows {break ;};_bef ,_agg :=_ddb (_bgdb [_aca ],_bbfg ,_gc );_eafc =_bgce .appendEncodedRow (_eafc ,_bef ,_bbfg );if _bgce .EncodedByteAlign {_agg =0;};_bbfg =_agg ;
};if _bgce .EndOfBlock {_egc ,_ :=_bgcb (_bbfg );_eafc =_bgce .appendEncodedRow (_eafc ,_egc ,_bbfg );};return _eafc ;};func (_fbef *Decoder )decodeRun (_acba *tree )(int ,error ){var _bbf int ;_fgd :=_acba ._fad ;for {_cgb ,_bgd :=_fbef ._bdb .ReadBool ();
if _bgd !=nil {return 0,_bgd ;};_fgd =_fgd .walk (_cgb );if _fgd ==nil {return 0,_a .New ("\u0075\u006e\u006bno\u0077\u006e\u0020\u0063\u006f\u0064\u0065\u0020\u0069n\u0020H\u0075f\u0066m\u0061\u006e\u0020\u0052\u004c\u0045\u0020\u0073\u0074\u0072\u0065\u0061\u006d");
};if _fgd ._bgcc {_bbf +=_fgd ._gefd ;switch {case _fgd ._gefd >=64:_fgd =_acba ._fad ;case _fgd ._gefd >=0:return _bbf ,nil ;default:return _fbef ._bcd ,nil ;};};};};func (_dfa *Decoder )tryFetchEOL1 ()(bool ,error ){_ced ,_dc :=_dfa ._bdb .ReadBits (13);
if _dc !=nil {return false ,_dc ;};return _ced ==0x3,nil ;};func _gdcf (_fcac []byte ,_afcf int ,_ebc int ,_bbed bool )([]byte ,int ){var (_deb code ;_bcg bool ;);for !_bcg {_deb ,_ebc ,_bcg =_baga (_ebc ,_bbed );_fcac ,_afcf =_ddbd (_fcac ,_afcf ,_deb );
};return _fcac ,_afcf ;};func (_dfd *Decoder )tryFetchRTC2D ()(_gdff error ){_dfd ._bdb .Mark ();var _dg bool ;for _dbeg :=0;_dbeg < 5;_dbeg ++{_dg ,_gdff =_dfd .tryFetchEOL1 ();if _gdff !=nil {if _a .Is (_gdff ,_ea .EOF ){if _dbeg ==0{break ;};return _abd ;
};};if _dg {continue ;};if _dbeg > 0{return _abd ;};break ;};if _dg {return _ea .EOF ;};_dfd ._bdb .Reset ();return _gdff ;};var (_b *treeNode ;_g *treeNode ;_eeb *tree ;_eg *tree ;_ge *tree ;_ed *tree ;_eed =-2000;_ab =-1000;_bf =-3000;_d =-4000;);func (_gcb *Decoder )decoderRowType41D ()error {if _gcb ._gea {_gcb ._bdb .Align ();
};_gcb ._bdb .Mark ();var (_gcd bool ;_bfd error ;);if _gcb ._eca {_gcd ,_bfd =_gcb .tryFetchEOL ();if _bfd !=nil {return _bfd ;};if !_gcd {return _ac ;};}else {_gcd ,_bfd =_gcb .looseFetchEOL ();if _bfd !=nil {return _bfd ;};};if !_gcd {_gcb ._bdb .Reset ();
};if _gcd &&_gcb ._fd {_gcb ._bdb .Mark ();for _aae :=0;_aae < 5;_aae ++{_gcd ,_bfd =_gcb .tryFetchEOL ();if _bfd !=nil {if _a .Is (_bfd ,_ea .EOF ){if _aae ==0{break ;};return _abd ;};};if _gcd {continue ;};if _aae > 0{return _abd ;};break ;};if _gcd {return _ea .EOF ;
};_gcb ._bdb .Reset ();};if _bfd =_gcb .decode1D ();_bfd !=nil {return _bfd ;};return nil ;};var _bb =[...][]uint16 {{3,2},{1,4},{6,5},{7},{9,8},{10,11,12},{13,14},{15},{16,17,0,18,64},{24,25,23,22,19,20,21,1792,1856,1920},{1984,2048,2112,2176,2240,2304,2368,2432,2496,2560,52,55,56,59,60,320,384,448,53,54,50,51,44,45,46,47,57,58,61,256,48,49,62,63,30,31,32,33,40,41,128,192,26,27,28,29,34,35,36,37,38,39,42,43},{640,704,768,832,1280,1344,1408,1472,1536,1600,1664,1728,512,576,896,960,1024,1088,1152,1216}};
func (_cace *treeNode )walk (_eggf bool )*treeNode {if _eggf {return _cace ._feea ;};return _cace ._efc ;};func (_efb *Encoder )encodeG4 (_eb [][]byte )[]byte {_efa :=make ([][]byte ,len (_eb ));copy (_efa ,_eb );_efa =_gef (_efa );var _ebd []byte ;var _eebb int ;
for _fccg :=1;_fccg < len (_efa );_fccg ++{if _efb .Rows > 0&&!_efb .EndOfBlock &&_fccg ==(_efb .Rows +1){break ;};var _dfg []byte ;var _acfg ,_fca ,_gad int ;_dec :=_eebb ;_fbcc :=-1;for _fbcc < len (_efa [_fccg ]){_acfg =_gga (_efa [_fccg ],_fbcc );_fca =_afe (_efa [_fccg ],_efa [_fccg -1],_fbcc );
_gad =_gga (_efa [_fccg -1],_fca );if _gad < _acfg {_dfg ,_dec =_ddbd (_dfg ,_dec ,_dff );_fbcc =_gad ;}else {if _ae .Abs (float64 (_fca -_acfg ))> 3{_dfg ,_dec ,_fbcc =_baa (_efa [_fccg ],_dfg ,_dec ,_fbcc ,_acfg );}else {_dfg ,_dec =_dcd (_dfg ,_dec ,_acfg ,_fca );
_fbcc =_acfg ;};};};_ebd =_efb .appendEncodedRow (_ebd ,_dfg ,_eebb );if _efb .EncodedByteAlign {_dec =0;};_eebb =_dec %8;};if _efb .EndOfBlock {_acg ,_ :=_daad (_eebb );_ebd =_efb .appendEncodedRow (_ebd ,_acg ,_eebb );};return _ebd ;};func _bag (_cec int )([]byte ,int ){var _fgg []byte ;
for _ece :=0;_ece < 6;_ece ++{_fgg ,_cec =_ddbd (_fgg ,_cec ,_fe );};return _fgg ,_cec %8;};func (_ggd *Decoder )getNextChangingElement (_fded int ,_aabg bool )int {_acc :=0;if !_aabg {_acc =1;};_cbd :=int (uint32 (_ggd ._cgc )&0xFFFFFFFE)+_acc ;if _cbd > 2{_cbd -=2;
};if _fded ==0{return _cbd ;};for _bcdf :=_cbd ;_bcdf < _ggd ._cdd ;_bcdf +=2{if _fded < _ggd ._cf [_bcdf ]{_ggd ._cgc =_bcdf ;return _bcdf ;};};return -1;};func (_ddcb *Encoder )Encode (pixels [][]byte )[]byte {if _ddcb .BlackIs1 {_bec =0;_eef =1;}else {_bec =1;
_eef =0;};if _ddcb .K ==0{return _ddcb .encodeG31D (pixels );};if _ddcb .K > 0{return _ddcb .encodeG32D (pixels );};if _ddcb .K < 0{return _ddcb .encodeG4 (pixels );};return nil ;};func (_beg *tree )fill (_cac ,_dcgg ,_ega int )error {_bbebc :=_beg ._fad ;
for _egg :=0;_egg < _cac ;_egg ++{_befd :=_cac -1-_egg ;_cecc :=((_dcgg >>uint (_befd ))&1)!=0;_ccb :=_bbebc .walk (_cecc );if _ccb !=nil {if _ccb ._bgcc {return _a .New ("\u006e\u006f\u0064\u0065\u0020\u0069\u0073\u0020\u006c\u0065\u0061\u0066\u002c\u0020\u006eo\u0020o\u0074\u0068\u0065\u0072\u0020\u0066\u006f\u006c\u006c\u006f\u0077\u0069\u006e\u0067");
};_bbebc =_ccb ;continue ;};_ccb =&treeNode {};if _egg ==_cac -1{_ccb ._gefd =_ega ;_ccb ._bgcc =true ;};if _dcgg ==0{_ccb ._fcfa =true ;};_bbebc .set (_cecc ,_ccb );_bbebc =_ccb ;};return nil ;};type Encoder struct{K int ;EndOfLine bool ;EncodedByteAlign bool ;
Columns int ;Rows int ;EndOfBlock bool ;BlackIs1 bool ;DamagedRowsBeforeError int ;};type code struct{Code uint16 ;BitsWritten int ;};func _gef (_cddf [][]byte )[][]byte {_fab :=make ([]byte ,len (_cddf [0]));for _bac :=range _fab {_fab [_bac ]=_bec ;};
_cddf =append (_cddf ,[]byte {});for _efe :=len (_cddf )-1;_efe > 0;_efe --{_cddf [_efe ]=_cddf [_efe -1];};_cddf [0]=_fab ;return _cddf ;};func _baa (_ddce ,_dfbb []byte ,_gece ,_fee ,_dca int )([]byte ,int ,int ){_bee :=_gga (_ddce ,_dca );_gade :=_fee >=0&&_ddce [_fee ]==_bec ||_fee ==-1;
_dfbb ,_gece =_ddbd (_dfbb ,_gece ,_dbb );var _fdd int ;if _fee > -1{_fdd =_dca -_fee ;}else {_fdd =_dca -_fee -1;};_dfbb ,_gece =_gdcf (_dfbb ,_gece ,_fdd ,_gade );_gade =!_gade ;_ca :=_bee -_dca ;_dfbb ,_gece =_gdcf (_dfbb ,_gece ,_ca ,_gade );_fee =_bee ;
return _dfbb ,_gece ,_fee ;};func (_gdce *Decoder )tryFetchEOL ()(bool ,error ){_dagd ,_aag :=_gdce ._bdb .ReadBits (12);if _aag !=nil {return false ,_aag ;};return _dagd ==0x1,nil ;};func _daad (_aed int )([]byte ,int ){var _gdcc []byte ;for _cbc :=0;
_cbc < 2;_cbc ++{_gdcc ,_aed =_ddbd (_gdcc ,_aed ,_gc );};return _gdcc ,_aed %8;};func (_be *Decoder )decodeRowType6 ()error {if _be ._gea {_be ._bdb .Align ();};if _be ._fd {_be ._bdb .Mark ();_dba ,_cdb :=_be .tryFetchEOL ();if _cdb !=nil {return _cdb ;
};if _dba {_dba ,_cdb =_be .tryFetchEOL ();if _cdb !=nil {return _cdb ;};if _dba {return _ea .EOF ;};};_be ._bdb .Reset ();};return _be .decode2D ();};func NewDecoder (data []byte ,options DecodeOptions )(*Decoder ,error ){_bde :=&Decoder {_bdb :_ee .NewReader (data ),_bcd :options .Columns ,_bdd :options .Rows ,_egd :options .DamagedRowsBeforeError ,_bfe :make ([]byte ,(options .Columns +7)/8),_cf :make ([]int ,options .Columns +2),_cd :make ([]int ,options .Columns +2),_gea :options .EncodedByteAligned ,_edc :options .BlackIsOne ,_eca :options .EndOfLine ,_fd :options .EndOfBlock };
switch {case options .K ==0:_bde ._dea =_dab ;if len (data )< 20{return nil ,_a .New ("\u0074o\u006f\u0020\u0073\u0068o\u0072\u0074\u0020\u0063\u0063i\u0074t\u0066a\u0078\u0020\u0073\u0074\u0072\u0065\u0061m");};_bdgd :=data [:20];if _bdgd [0]!=0||(_bdgd [1]>>4!=1&&_bdgd [1]!=1){_bde ._dea =_eab ;
_fbea :=(uint16 (_bdgd [0])<<8+uint16 (_bdgd [1]&0xff))>>4;for _gdd :=12;_gdd < 160;_gdd ++{_fbea =(_fbea <<1)+uint16 ((_bdgd [_gdd /8]>>uint16 (7-(_gdd %8)))&0x01);if _fbea &0xfff==1{_bde ._dea =_dab ;break ;};};};case options .K < 0:_bde ._dea =_bdgb ;
case options .K > 0:_bde ._dea =_dab ;_bde ._fcc =true ;};switch _bde ._dea {case _eab ,_dab ,_bdgb :default:return nil ,_a .New ("\u0075\u006ek\u006e\u006f\u0077\u006e\u0020\u0063\u0063\u0069\u0074\u0074\u0066\u0061\u0078\u002e\u0044\u0065\u0063\u006f\u0064\u0065\u0072\u0020ty\u0070\u0065");
};return _bde ,nil ;};func _dbff (_bbg []byte ,_dacf int )([]byte ,int ){return _ddbd (_bbg ,_dacf ,_dff )};func _dgc (_ffce ,_cbcc int )code {var _ddd code ;switch _cbcc -_ffce {case -1:_ddd =_bbe ;case -2:_ddd =_ff ;case -3:_ddd =_ec ;case 0:_ddd =_fgf ;
case 1:_ddd =_eaa ;case 2:_ddd =_cgd ;case 3:_ddd =_fgff ;};return _ddd ;};type treeNode struct{_efc *treeNode ;_feea *treeNode ;_gefd int ;_fcfa bool ;_bgcc bool ;};func (_ada *Decoder )decode1D ()error {var (_gb int ;_bgc error ;);_abdc :=true ;_ada ._cdg =0;
for {var _gec int ;if _abdc {_gec ,_bgc =_ada .decodeRun (_eg );}else {_gec ,_bgc =_ada .decodeRun (_eeb );};if _bgc !=nil {return _bgc ;};_gb +=_gec ;_ada ._cd [_ada ._cdg ]=_gb ;_ada ._cdg ++;_abdc =!_abdc ;if _gb >=_ada ._bcd {break ;};};return nil ;
};type tiffType int ;var _abbg =[...][]uint16 {{0x7,0x8,0xb,0xc,0xe,0xf},{0x12,0x13,0x14,0x1b,0x7,0x8},{0x17,0x18,0x2a,0x2b,0x3,0x34,0x35,0x7,0x8},{0x13,0x17,0x18,0x24,0x27,0x28,0x2b,0x3,0x37,0x4,0x8,0xc},{0x12,0x13,0x14,0x15,0x16,0x17,0x1a,0x1b,0x2,0x24,0x25,0x28,0x29,0x2a,0x2b,0x2c,0x2d,0x3,0x32,0x33,0x34,0x35,0x36,0x37,0x4,0x4a,0x4b,0x5,0x52,0x53,0x54,0x55,0x58,0x59,0x5a,0x5b,0x64,0x65,0x67,0x68,0xa,0xb},{0x98,0x99,0x9a,0x9b,0xcc,0xcd,0xd2,0xd3,0xd4,0xd5,0xd6,0xd7,0xd8,0xd9,0xda,0xdb},{},{0x8,0xc,0xd},{0x12,0x13,0x14,0x15,0x16,0x17,0x1c,0x1d,0x1e,0x1f}};
func (_eeda *treeNode )set (_aef bool ,_fabg *treeNode ){if !_aef {_eeda ._efc =_fabg ;}else {_eeda ._feea =_fabg ;};};func (_afd *Encoder )encodeG32D (_ef [][]byte )[]byte {var _edg []byte ;var _cbb int ;for _dacg :=0;_dacg < len (_ef );_dacg +=_afd .K {if _afd .Rows > 0&&!_afd .EndOfBlock &&_dacg ==_afd .Rows {break ;
};_ffc ,_bdee :=_ddb (_ef [_dacg ],_cbb ,_fe );_edg =_afd .appendEncodedRow (_edg ,_ffc ,_cbb );if _afd .EncodedByteAlign {_bdee =0;};_cbb =_bdee ;for _gbc :=_dacg +1;_gbc < (_dacg +_afd .K )&&_gbc < len (_ef );_gbc ++{if _afd .Rows > 0&&!_afd .EndOfBlock &&_gbc ==_afd .Rows {break ;
};_fgc ,_bba :=_ddbd (nil ,_cbb ,_fbe );var _eabg ,_bbae ,_aegg int ;_dcg :=-1;for _dcg < len (_ef [_gbc ]){_eabg =_gga (_ef [_gbc ],_dcg );_bbae =_afe (_ef [_gbc ],_ef [_gbc -1],_dcg );_aegg =_gga (_ef [_gbc -1],_bbae );if _aegg < _eabg {_fgc ,_bba =_dbff (_fgc ,_bba );
_dcg =_aegg ;}else {if _ae .Abs (float64 (_bbae -_eabg ))> 3{_fgc ,_bba ,_dcg =_baa (_ef [_gbc ],_fgc ,_bba ,_dcg ,_eabg );}else {_fgc ,_bba =_dcd (_fgc ,_bba ,_eabg ,_bbae );_dcg =_eabg ;};};};_edg =_afd .appendEncodedRow (_edg ,_fgc ,_cbb );if _afd .EncodedByteAlign {_bba =0;
};_cbb =_bba %8;};};if _afd .EndOfBlock {_aaa ,_ :=_bag (_cbb );_edg =_afd .appendEncodedRow (_edg ,_aaa ,_cbb );};return _edg ;};func _ddb (_cdf []byte ,_aad int ,_eced code )([]byte ,int ){_bgf :=true ;var _ddf []byte ;_ddf ,_aad =_ddbd (nil ,_aad ,_eced );
_gbcg :=0;var _cbba int ;for _gbcg < len (_cdf ){_cbba ,_gbcg =_afcc (_cdf ,_bgf ,_gbcg );_ddf ,_aad =_gdcf (_ddf ,_aad ,_cbba ,_bgf );_bgf =!_bgf ;};return _ddf ,_aad %8;};var (_bec byte =1;_eef byte =0;);func (_fcf *Decoder )Read (in []byte )(int ,error ){if _fcf ._dee !=nil {return 0,_fcf ._dee ;
};_deaa :=len (in );var (_fbcf int ;_cdc int ;);for _deaa !=0{if _fcf ._fbc >=_fcf ._ade {if _deg :=_fcf .fetch ();_deg !=nil {_fcf ._dee =_deg ;return 0,_deg ;};};if _fcf ._ade ==-1{return _fbcf ,_ea .EOF ;};switch {case _deaa <=_fcf ._ade -_fcf ._fbc :_ecg :=_fcf ._bfe [_fcf ._fbc :_fcf ._fbc +_deaa ];
for _ ,_gcg :=range _ecg {if !_fcf ._edc {_gcg =^_gcg ;};in [_cdc ]=_gcg ;_cdc ++;};_fbcf +=len (_ecg );_fcf ._fbc +=len (_ecg );return _fbcf ,nil ;default:_ddc :=_fcf ._bfe [_fcf ._fbc :];for _ ,_afc :=range _ddc {if !_fcf ._edc {_afc =^_afc ;};in [_cdc ]=_afc ;
_cdc ++;};_fbcf +=len (_ddc );_fcf ._fbc +=len (_ddc );_deaa -=len (_ddc );};};return _fbcf ,nil ;};func _gga (_dbc []byte ,_edb int )int {if _edb >=len (_dbc ){return _edb ;};if _edb < -1{_edb =-1;};var _gfb byte ;if _edb > -1{_gfb =_dbc [_edb ];}else {_gfb =_bec ;
};_add :=_edb +1;for _add < len (_dbc ){if _dbc [_add ]!=_gfb {break ;};_add ++;};return _add ;};type DecodeOptions struct{Columns int ;Rows int ;K int ;EncodedByteAligned bool ;BlackIsOne bool ;EndOfBlock bool ;EndOfLine bool ;DamagedRowsBeforeError int ;
};var _ad =[...][]uint16 {{0x2,0x3},{0x2,0x3},{0x2,0x3},{0x3},{0x4,0x5},{0x4,0x5,0x7},{0x4,0x7},{0x18},{0x17,0x18,0x37,0x8,0xf},{0x17,0x18,0x28,0x37,0x67,0x68,0x6c,0x8,0xc,0xd},{0x12,0x13,0x14,0x15,0x16,0x17,0x1c,0x1d,0x1e,0x1f,0x24,0x27,0x28,0x2b,0x2c,0x33,0x34,0x35,0x37,0x38,0x52,0x53,0x54,0x55,0x56,0x57,0x58,0x59,0x5a,0x5b,0x64,0x65,0x66,0x67,0x68,0x69,0x6a,0x6b,0x6c,0x6d,0xc8,0xc9,0xca,0xcb,0xcc,0xcd,0xd2,0xd3,0xd4,0xd5,0xd6,0xd7,0xda,0xdb},{0x4a,0x4b,0x4c,0x4d,0x52,0x53,0x54,0x55,0x5a,0x5b,0x64,0x65,0x6c,0x6d,0x72,0x73,0x74,0x75,0x76,0x77}};
