//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package unichart ;import (_a "bytes";_ag "fmt";_c "github.com/unidoc/unichart/render";_g "github.com/unidoc/unipdf/v3/common";_aeb "github.com/unidoc/unipdf/v3/contentstream";_fe "github.com/unidoc/unipdf/v3/contentstream/draw";_ae "github.com/unidoc/unipdf/v3/core";
_ee "github.com/unidoc/unipdf/v3/model";_f "image/color";_e "io";_agf "math";);func (_gc *Renderer )ResetStyle (){_gc .SetFillColor (_f .Black );_gc .SetStrokeColor (_f .Transparent );_gc .SetStrokeWidth (0);_gc .SetFont (_ee .DefaultFont ());_gc .SetFontColor (_f .Black );
_gc .SetFontSize (12);_gc .SetTextRotation (0);};func (_bg *Renderer )SetFillColor (color _f .Color ){_bg ._ff =color ;_agd ,_ea ,_ge ,_ :=_dce (color );_bg ._acg .Add_rg (_agd ,_ea ,_ge );};func (_af *Renderer )SetDPI (dpi float64 ){_af ._cc =dpi };func (_eae *Renderer )getTextWidth (_ef string )float64 {var _bafc float64 ;
for _ ,_ga :=range _ef {_fg ,_cac :=_eae ._gg .GetRuneMetrics (_ga );if !_cac {_g .Log .Debug ("\u0045\u0052\u0052OR\u003a\u0020\u0075\u006e\u0073\u0075\u0070\u0070\u006fr\u0074e\u0064 \u0072u\u006e\u0065\u0020\u0025\u0076\u0020\u0069\u006e\u0020\u0066\u006f\u006e\u0074",_ga );
};_bafc +=_fg .Wx ;};return _eae ._fec *_bafc /1000.0;};func (_fab *Renderer )wrapText (_beg string )[]string {var (_dda []string ;_fae []rune ;);for _ ,_deg :=range _beg {if _deg =='\n'{_dda =append (_dda ,string (_fae ));_fae =[]rune {};continue ;};_fae =append (_fae ,_deg );
};if len (_fae )> 0{_dda =append (_dda ,string (_fae ));};return _dda ;};func (_aa *Renderer )GetDPI ()float64 {return _aa ._cc };func NewRenderer (cc *_aeb .ContentCreator ,res *_ee .PdfPageResources )func (int ,int )(_c .Renderer ,error ){return func (_d ,_feg int )(_c .Renderer ,error ){_ce :=&Renderer {_cg :_d ,_ac :_feg ,_cc :72,_acg :cc ,_cgg :res ,_ed :map[*_ee .PdfFont ]_ae .PdfObjectName {}};
_ce .ResetStyle ();return _ce ,nil ;};};func (_eb *Renderer )SetStrokeWidth (width float64 ){_eb ._bd =width ;_eb ._acg .Add_w (width )};func (_ccg *Renderer )FillStroke (){_ccg ._acg .Add_B ()};func (_bdb *Renderer )SetClassName (name string ){};func (_ccgb *Renderer )SetTextRotation (radians float64 ){_ccgb ._ba =_faba (-radians )};
func (_fegb *Renderer )SetFontColor (color _f .Color ){_fegb ._fea =color };func (_gf *Renderer )SetStrokeDashArray (dashArray []float64 ){_fc :=make ([]int64 ,len (dashArray ));for _edc ,_gba :=range dashArray {_fc [_edc ]=int64 (_gba );};_gf ._acg .Add_d (_fc ,0);
};func (_dg *Renderer )Fill (){_dg ._acg .Add_f ()};func _dce (_bag _f .Color )(float64 ,float64 ,float64 ,float64 ){_dggg ,_fcd ,_acc ,_gcd :=_eg (_bag );return float64 (_dggg )/255,float64 (_fcd )/255,float64 (_acc )/255,float64 (_gcd )/255;};type Renderer struct{_cg int ;
_ac int ;_cc float64 ;_acg *_aeb .ContentCreator ;_cgg *_ee .PdfPageResources ;_ff _f .Color ;_gd _f .Color ;_bd float64 ;_gg *_ee .PdfFont ;_fec float64 ;_fea _f .Color ;_ba float64 ;_ed map[*_ee .PdfFont ]_ae .PdfObjectName ;};func (_cfg *Renderer )MeasureText (text string )_c .Box {_eag :=_cfg ._fec ;
_gcbb ,_gcc :=_cfg ._gg .GetFontDescriptor ();if _gcc !=nil {_g .Log .Debug ("W\u0041\u0052\u004e\u003a\u0020\u0055n\u0061\u0062\u006c\u0065\u0020\u0074o\u0020\u0067\u0065\u0074\u0020\u0066\u006fn\u0074\u0020\u0064\u0065\u0073\u0063\u0072\u0069\u0070\u0074o\u0072");
}else {_ec ,_bdde :=_gcbb .GetCapHeight ();if _bdde !=nil {_g .Log .Debug ("\u0057\u0041\u0052\u004e\u003a\u0020\u0055\u006e\u0061\u0062\u006c\u0065\u0020t\u006f\u0020\u0067\u0065\u0074\u0020f\u006f\u006e\u0074\u0020\u0063\u0061\u0070\u0020\u0068\u0065\u0069\u0067\u0068t\u003a\u0020\u0025\u0076",_bdde );
}else {_eag =_ec /1000.0*_cfg ._fec ;};};var (_fdd =0.0;_gdf =_cfg .wrapText (text ););for _ ,_gdb :=range _gdf {if _gbaa :=_cfg .getTextWidth (_gdb );_gbaa > _fdd {_fdd =_gbaa ;};};_cbb :=_c .NewBox (0,0,int (_fdd ),int (_eag ));if _bge :=_cfg ._ba ;_bge !=0{_cbb =_cbb .Corners ().Rotate (_bge ).Box ();
};return _cbb ;};func (_ab *Renderer )LineTo (x ,y int ){_ab ._acg .Add_l (float64 (x ),float64 (y ))};func _eg (_gda _f .Color )(uint8 ,uint8 ,uint8 ,uint8 ){_df ,_fgd ,_cff ,_gde :=_gda .RGBA ();return uint8 (_df >>8),uint8 (_fgd >>8),uint8 (_cff >>8),uint8 (_gde >>8);
};func (_fbc *Renderer )Save (w _e .Writer )error {if w ==nil {return nil ;};_ ,_cda :=_e .Copy (w ,_a .NewBuffer (_fbc ._acg .Bytes ()));return _cda ;};func _cgf (_cfge float64 )float64 {return _cfge *_agf .Pi /180.0};func (_ca *Renderer )Circle (radius float64 ,x ,y int ){_ffb :=radius ;
if _de :=_ca ._bd ;_de !=0{_ffb -=_de /2;};_dd :=_ffb *0.551784;_dgg :=_fe .CubicBezierPath {Curves :[]_fe .CubicBezierCurve {_fe .NewCubicBezierCurve (-_ffb ,0,-_ffb ,_dd ,-_dd ,_ffb ,0,_ffb ),_fe .NewCubicBezierCurve (0,_ffb ,_dd ,_ffb ,_ffb ,_dd ,_ffb ,0),_fe .NewCubicBezierCurve (_ffb ,0,_ffb ,-_dd ,_dd ,-_ffb ,0,-_ffb ),_fe .NewCubicBezierCurve (0,-_ffb ,-_dd ,-_ffb ,-_ffb ,-_dd ,-_ffb ,0)}};
if _eab :=_ca ._bd ;_eab !=0{_dgg =_dgg .Offset (_eab /2,_eab /2);};_dgg =_dgg .Offset (float64 (x ),float64 (y ));_fe .DrawBezierPathWithCreator (_dgg ,_ca ._acg );};func (_eec *Renderer )QuadCurveTo (cx ,cy ,x ,y int ){_eec ._acg .Add_v (float64 (x ),float64 (y ),float64 (cx ),float64 (cy ));
};func (_dc *Renderer )ClearTextRotation (){_dc ._ba =0};func (_aag *Renderer )SetStrokeColor (color _f .Color ){_aag ._gd =color ;_gb ,_bdd ,_fa ,_ :=_dce (color );_aag ._acg .Add_RG (_gb ,_bdd ,_fa );};func _faba (_dbd float64 )float64 {return _dbd *180/_agf .Pi };
func (_caa *Renderer )Text (text string ,x ,y int ){_caa ._acg .Add_q ();_caa .SetFont (_caa ._gg );_bed ,_ddb ,_fdc ,_ :=_dce (_caa ._fea );_caa ._acg .Add_rg (_bed ,_ddb ,_fdc );_caa ._acg .Translate (float64 (x ),float64 (y )).Scale (1,-1);if _bfg :=_caa ._ba ;
_bfg !=0{_caa ._acg .RotateDeg (_bfg );};_caa ._acg .Add_BT ().Add_TL (_caa ._fec );var (_gce =_caa ._gg .Encoder ();_bdca =_caa .wrapText (text );_ceb =len (_bdca ););for _gcb ,_aadb :=range _bdca {_caa ._acg .Add_TJ (_ae .MakeStringFromBytes (_gce .Encode (_aadb )));
if _gcb !=_ceb -1{_caa ._acg .Add_Tstar ();};};_caa ._acg .Add_ET ();_caa ._acg .Add_Q ();};func (_fd *Renderer )MoveTo (x ,y int ){_fd ._acg .Add_m (float64 (x ),float64 (y ))};func (_agdd *Renderer )SetFont (font _c .Font ){_eaa ,_aad :=font .(*_ee .PdfFont );
if !_aad {_g .Log .Debug ("\u0045R\u0052\u004f\u0052\u003a\u0020\u0069\u006e\u0076\u0061\u006c\u0069d\u0020\u0066\u006f\u006e\u0074\u0020\u0074\u0079\u0070\u0065");return ;};_fca ,_aad :=_agdd ._ed [_eaa ];if !_aad {_fca =_cebf ("\u0046\u006f\u006e\u0074",1,_agdd ._cgg .HasFontByName );
if _afd :=_agdd ._cgg .SetFontByName (_fca ,_eaa .ToPdfObject ());_afd !=nil {_g .Log .Debug ("\u0045\u0052\u0052\u004f\u0052:\u0020\u0063\u006f\u0075\u006c\u0064\u0020\u006e\u006f\u0074\u0020\u0061\u0064d\u0020\u0066\u006f\u006e\u0074\u0020\u0025\u0076\u0020\u0074\u006f\u0020\u0072\u0065\u0073\u006f\u0075\u0072\u0063\u0065\u0073",_eaa );
};_agdd ._ed [_eaa ]=_fca ;};_agdd ._acg .Add_Tf (_fca ,_agdd ._fec );_agdd ._gg =_eaa ;};func (_aga *Renderer )Close (){_aga ._acg .Add_h ()};func (_dee *Renderer )SetFontSize (size float64 ){_dee ._fec =size };func (_ead *Renderer )Stroke (){_ead ._acg .Add_S ()};
func (_bdc *Renderer )ArcTo (cx ,cy int ,rx ,ry ,startAngle ,deltaAngle float64 ){startAngle =_faba (2.0*_agf .Pi -startAngle );deltaAngle =_faba (-deltaAngle );_bc ,_fb :=deltaAngle ,1;if _agf .Abs (deltaAngle )> 90.0{_fb =int (_agf .Ceil (_agf .Abs (deltaAngle )/90.0));
_bc =deltaAngle /float64 (_fb );};var (_bf =_cgf (_bc /2);_bdg =_agf .Abs (4.0/3.0*(1.0-_agf .Cos (_bf ))/_agf .Sin (_bf ));_cb =float64 (cx );_aaga =float64 (cy ););for _bb :=0;_bb < _fb ;_bb ++{_bfe :=_cgf (startAngle +float64 (_bb )*_bc );_fcc :=_cgf (startAngle +float64 (_bb +1)*_bc );
_eeb :=_agf .Cos (_bfe );_be :=_agf .Cos (_fcc );_afa :=_agf .Sin (_bfe );_baf :=_agf .Sin (_fcc );var _cf []float64 ;if _bc > 0{_cf =[]float64 {_cb +rx *_eeb ,_aaga -ry *_afa ,_cb +rx *(_eeb -_bdg *_afa ),_aaga -ry *(_afa +_bdg *_eeb ),_cb +rx *(_be +_bdg *_baf ),_aaga -ry *(_baf -_bdg *_be ),_cb +rx *_be ,_aaga -ry *_baf };
}else {_cf =[]float64 {_cb +rx *_eeb ,_aaga -ry *_afa ,_cb +rx *(_eeb +_bdg *_afa ),_aaga -ry *(_afa -_bdg *_eeb ),_cb +rx *(_be -_bdg *_baf ),_aaga -ry *(_baf +_bdg *_be ),_cb +rx *_be ,_aaga -ry *_baf };};if _bb ==0{_bdc ._acg .Add_l (_cf [0],_cf [1]);
};_bdc ._acg .Add_c (_cf [2],_cf [3],_cf [4],_cf [5],_cf [6],_cf [7]);};};func _cebf (_cae string ,_ffd int ,_fbd func (_ae .PdfObjectName )bool )_ae .PdfObjectName {_gbd :=_ae .PdfObjectName (_ag .Sprintf ("\u0025\u0073\u0025\u0064",_cae ,_ffd ));for _fabf :=_ffd ;
_fbd (_gbd );{_fabf ++;_gbd =_ae .PdfObjectName (_ag .Sprintf ("\u0025\u0073\u0025\u0064",_cae ,_fabf ));};return _gbd ;};