//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package docutil ;import (_e "errors";_ea "fmt";_c "github.com/unidoc/unipdf/v3/common";_ec "github.com/unidoc/unipdf/v3/core";);func (_eg *Catalog )SetOutputIntents (outputIntents *OutputIntents ){if _ca :=_eg .Object .Get ("\u004f\u0075\u0074\u0070\u0075\u0074\u0049\u006e\u0074\u0065\u006e\u0074\u0073");
_ca !=nil {for _eefe ,_gd :=range _eg ._be .Objects {if _gd ==_ca {if outputIntents ._eb ==_ca {return ;};_eg ._be .Objects =append (_eg ._be .Objects [:_eefe ],_eg ._be .Objects [_eefe +1:]...);break ;};};};_bee :=outputIntents ._eb ;if _bee ==nil {_bee =_ec .MakeIndirectObject (outputIntents ._dc );
};_eg .Object .Set ("\u004f\u0075\u0074\u0070\u0075\u0074\u0049\u006e\u0074\u0065\u006e\u0074\u0073",_bee );_eg ._be .Objects =append (_eg ._be .Objects ,_bee );};func (_egd Content )GetData ()([]byte ,error ){_agbc ,_cfg :=_ec .NewEncoderFromStream (_egd .Stream );
if _cfg !=nil {return nil ,_cfg ;};_cdde ,_cfg :=_agbc .DecodeStream (_egd .Stream );if _cfg !=nil {return nil ,_cfg ;};return _cdde ,nil ;};func (_edc Page )GetContents ()([]Content ,bool ){_fdc ,_ggg :=_ec .GetArray (_edc .Object .Get ("\u0043\u006f\u006e\u0074\u0065\u006e\u0074\u0073"));
if !_ggg {_daa ,_cbeg :=_ec .GetStream (_edc .Object .Get ("\u0043\u006f\u006e\u0074\u0065\u006e\u0074\u0073"));if !_cbeg {return nil ,false ;};return []Content {{Stream :_daa ,_gbe :_edc ,_aee :0}},true ;};_cge :=make ([]Content ,_fdc .Len ());for _daaf ,_dee :=range _fdc .Elements (){_ecd ,_cff :=_ec .GetStream (_dee );
if !_cff {continue ;};_cge [_daaf ]=Content {Stream :_ecd ,_gbe :_edc ,_aee :_daaf };};return _cge ,true ;};func (_cb *Catalog )SetMarkInfo (mi _ec .PdfObject ){if mi ==nil {_cb .Object .Remove ("\u004d\u0061\u0072\u006b\u0049\u006e\u0066\u006f");return ;
};_ac :=_ec .MakeIndirectObject (mi );_cb .Object .Set ("\u004d\u0061\u0072\u006b\u0049\u006e\u0066\u006f",_ac );_cb ._be .Objects =append (_cb ._be .Objects ,_ac );};func (_cbc *Document )AddIndirectObject (indirect *_ec .PdfIndirectObject ){for _ ,_cee :=range _cbc .Objects {if _cee ==indirect {return ;
};};_cbc .Objects =append (_cbc .Objects ,indirect );};func (_ffg Page )GetResourcesXObject ()(*_ec .PdfObjectDictionary ,bool ){_eag ,_bebg :=_ffg .GetResources ();if !_bebg {return nil ,false ;};return _ec .GetDict (_eag .Get ("\u0058O\u0062\u006a\u0065\u0063\u0074"));
};type OutputIntents struct{_dc *_ec .PdfObjectArray ;_cec *Document ;_eb *_ec .PdfIndirectObject ;};func (_bb *Catalog )HasMetadata ()bool {_fba :=_bb .Object .Get ("\u004d\u0065\u0074\u0061\u0064\u0061\u0074\u0061");return _fba !=nil ;};func (_a *Catalog )GetMetadata ()(*_ec .PdfObjectStream ,bool ){return _ec .GetStream (_a .Object .Get ("\u004d\u0065\u0074\u0061\u0064\u0061\u0074\u0061"));
};type Content struct{Stream *_ec .PdfObjectStream ;_aee int ;_gbe Page ;};type ImageSMask struct{Image *Image ;Stream *_ec .PdfObjectStream ;};func (_dec *Page )Number ()int {return _dec ._ebc };type Image struct{Name string ;Width int ;Height int ;Colorspace _ec .PdfObjectName ;
ColorComponents int ;BitsPerComponent int ;SMask *ImageSMask ;Stream *_ec .PdfObjectStream ;};func (_fc *Catalog )SetMetadata (data []byte )error {_bd ,_cd :=_ec .MakeStream (data ,nil );if _cd !=nil {return _cd ;};_bd .Set ("\u0054\u0079\u0070\u0065",_ec .MakeName ("\u004d\u0065\u0074\u0061\u0064\u0061\u0074\u0061"));
_bd .Set ("\u0053u\u0062\u0074\u0079\u0070\u0065",_ec .MakeName ("\u0058\u004d\u004c"));_fc .Object .Set ("\u004d\u0065\u0074\u0061\u0064\u0061\u0074\u0061",_bd );_fc ._be .Objects =append (_fc ._be .Objects ,_bd );return nil ;};func (_bdg *OutputIntents )Get (i int )(OutputIntent ,bool ){if _bdg ._dc ==nil {return OutputIntent {},false ;
};if i >=_bdg ._dc .Len (){return OutputIntent {},false ;};_ge :=_bdg ._dc .Get (i );_ecg ,_adb :=_ec .GetIndirect (_ge );if !_adb {_ag ,_aa :=_ec .GetDict (_ge );return OutputIntent {Object :_ag },_aa ;};_cc ,_fbb :=_ec .GetDict (_ecg .PdfObject );return OutputIntent {Object :_cc },_fbb ;
};func (_ggd *Document )AddStream (stream *_ec .PdfObjectStream ){for _ ,_dae :=range _ggd .Objects {if _dae ==stream {return ;};};_ggd .Objects =append (_ggd .Objects ,stream );};func _dab (_gc _ec .PdfObject )(_ec .PdfObjectName ,error ){var _ed *_ec .PdfObjectName ;
var _fd *_ec .PdfObjectArray ;if _cag ,_aag :=_gc .(*_ec .PdfIndirectObject );_aag {if _cdf ,_cbg :=_cag .PdfObject .(*_ec .PdfObjectArray );_cbg {_fd =_cdf ;}else if _def ,_egc :=_cag .PdfObject .(*_ec .PdfObjectName );_egc {_ed =_def ;};}else if _afd ,_ef :=_gc .(*_ec .PdfObjectArray );
_ef {_fd =_afd ;}else if _abg ,_ceb :=_gc .(*_ec .PdfObjectName );_ceb {_ed =_abg ;};if _ed !=nil {switch *_ed {case "\u0044\u0065\u0076\u0069\u0063\u0065\u0047\u0072\u0061\u0079","\u0044e\u0076\u0069\u0063\u0065\u0052\u0047B","\u0044\u0065\u0076\u0069\u0063\u0065\u0043\u004d\u0059\u004b":return *_ed ,nil ;
case "\u0050a\u0074\u0074\u0065\u0072\u006e":return *_ed ,nil ;};};if _fd !=nil &&_fd .Len ()> 0{if _fda ,_cdd :=_fd .Get (0).(*_ec .PdfObjectName );_cdd {switch *_fda {case "\u0044\u0065\u0076\u0069\u0063\u0065\u0047\u0072\u0061\u0079","\u0044e\u0076\u0069\u0063\u0065\u0052\u0047B","\u0044\u0065\u0076\u0069\u0063\u0065\u0043\u004d\u0059\u004b":if _fd .Len ()==1{return *_fda ,nil ;
};case "\u0043a\u006c\u0047\u0072\u0061\u0079","\u0043\u0061\u006c\u0052\u0047\u0042","\u004c\u0061\u0062":return *_fda ,nil ;case "\u0049\u0043\u0043\u0042\u0061\u0073\u0065\u0064","\u0050a\u0074\u0074\u0065\u0072\u006e","\u0049n\u0064\u0065\u0078\u0065\u0064":return *_fda ,nil ;
case "\u0053\u0065\u0070\u0061\u0072\u0061\u0074\u0069\u006f\u006e","\u0044e\u0076\u0069\u0063\u0065\u004e":return *_fda ,nil ;};};};return "",nil ;};func (_edcb Page )FindXObjectImages ()([]*Image ,error ){_agc ,_fa :=_edcb .GetResourcesXObject ();if !_fa {return nil ,nil ;
};var _bde []*Image ;var _bea error ;_fab :=map[*_ec .PdfObjectStream ]int {};_eac :=map[*_ec .PdfObjectStream ]struct{}{};var _dgf int ;for _ ,_cffc :=range _agc .Keys (){_fea ,_dac :=_ec .GetStream (_agc .Get (_cffc ));if !_dac {continue ;};if _ ,_daf :=_fab [_fea ];
_daf {continue ;};_ced ,_acg :=_ec .GetName (_fea .Get ("\u0053u\u0062\u0074\u0079\u0070\u0065"));if !_acg ||_ced .String ()!="\u0049\u006d\u0061g\u0065"{continue ;};_bbe :=Image {BitsPerComponent :8,Stream :_fea ,Name :string (_cffc )};if _bbe .Colorspace ,_bea =_dab (_fea .PdfObjectDictionary .Get ("\u0043\u006f\u006c\u006f\u0072\u0053\u0070\u0061\u0063\u0065"));
_bea !=nil {_c .Log .Error ("\u0045\u0072\u0072\u006f\u0072\u0020\u0064\u0065\u0074\u0065r\u006d\u0069\u006e\u0065\u0020\u0063\u006fl\u006f\u0072\u0020\u0073\u0070\u0061\u0063\u0065\u0020\u0025\u0073",_bea );continue ;};if _fac ,_ade :=_ec .GetIntVal (_fea .PdfObjectDictionary .Get ("\u0042\u0069t\u0073\u0050\u0065r\u0043\u006f\u006d\u0070\u006f\u006e\u0065\u006e\u0074"));
_ade {_bbe .BitsPerComponent =_fac ;};if _gga ,_daeg :=_ec .GetIntVal (_fea .PdfObjectDictionary .Get ("\u0057\u0069\u0064t\u0068"));_daeg {_bbe .Width =_gga ;};if _bac ,_fdaa :=_ec .GetIntVal (_fea .PdfObjectDictionary .Get ("\u0048\u0065\u0069\u0067\u0068\u0074"));
_fdaa {_bbe .Height =_bac ;};if _fcd ,_ace :=_ec .GetStream (_fea .Get ("\u0053\u004d\u0061s\u006b"));_ace {_bbe .SMask =&ImageSMask {Image :&_bbe ,Stream :_fcd };_eac [_fcd ]=struct{}{};};switch _bbe .Colorspace {case "\u0044e\u0076\u0069\u0063\u0065\u0052\u0047B":_bbe .ColorComponents =3;
case "\u0044\u0065\u0076\u0069\u0063\u0065\u0047\u0072\u0061\u0079":_bbe .ColorComponents =1;case "\u0044\u0065\u0076\u0069\u0063\u0065\u0043\u004d\u0059\u004b":_bbe .ColorComponents =4;default:_bbe .ColorComponents =-1;};_fab [_fea ]=_dgf ;_bde =append (_bde ,&_bbe );
_dgf ++;};var _fgf []int ;for _ ,_ceeb :=range _bde {if _ceeb .SMask !=nil {_gge ,_edg :=_fab [_ceeb .SMask .Stream ];if _edg {_fgf =append (_fgf ,_gge );};};};_cced :=make ([]*Image ,len (_bde )-len (_fgf ));_dgf =0;_bbc :for _gfcg ,_ggb :=range _bde {for _ ,_faba :=range _fgf {if _gfcg ==_faba {continue _bbc ;
};};_cced [_dgf ]=_ggb ;_dgf ++;};return _bde ,nil ;};func (_agb Page )GetResources ()(*_ec .PdfObjectDictionary ,bool ){return _ec .GetDict (_agb .Object .Get ("\u0052e\u0073\u006f\u0075\u0072\u0063\u0065s"));};func (_ff *Catalog )SetStructTreeRoot (structTreeRoot _ec .PdfObject ){if structTreeRoot ==nil {_ff .Object .Remove ("\u0053\u0074\u0072\u0075\u0063\u0074\u0054\u0072\u0065e\u0052\u006f\u006f\u0074");
return ;};_fe :=_ec .MakeIndirectObject (structTreeRoot );_ff .Object .Set ("\u0053\u0074\u0072\u0075\u0063\u0074\u0054\u0072\u0065e\u0052\u006f\u006f\u0074",_fe );_ff ._be .Objects =append (_ff ._be .Objects ,_fe );};func (_eef *Catalog )GetStructTreeRoot ()(*_ec .PdfObjectDictionary ,bool ){return _ec .GetDict (_eef .Object .Get ("\u0053\u0074\u0072\u0075\u0063\u0074\u0054\u0072\u0065e\u0052\u006f\u006f\u0074"));
};type OutputIntent struct{Object *_ec .PdfObjectDictionary ;};type Page struct{_ebc int ;Object *_ec .PdfObjectDictionary ;_bbb *Document ;};func (_d *Catalog )GetPages ()([]Page ,bool ){_ee ,_ba :=_ec .GetDict (_d .Object .Get ("\u0050\u0061\u0067e\u0073"));
if !_ba {return nil ,false ;};_f ,_fb :=_ec .GetArray (_ee .Get ("\u004b\u0069\u0064\u0073"));if !_fb {return nil ,false ;};_bg :=make ([]Page ,_f .Len ());for _cf ,_gb :=range _f .Elements (){_ce ,_beb :=_ec .GetDict (_gb );if !_beb {continue ;};_bg [_cf ]=Page {Object :_ce ,_ebc :_cf +1,_bbb :_d ._be };
};return _bg ,true ;};func (_acb *OutputIntents )Add (oi _ec .PdfObject )error {_cg ,_ad :=oi .(*_ec .PdfObjectDictionary );if !_ad {return _e .New ("\u0069\u006e\u0070\u0075\u0074\u0020\u006f\u0075\u0074\u0070\u0075\u0074\u0020\u0069\u006e\u0074\u0065\u006et\u0020\u0073\u0068\u006f\u0075\u006c\u0064 \u0062\u0065\u0020\u0061\u006e\u0020\u006f\u0062\u006a\u0065\u0063t\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079");
};if _cgf ,_df :=_ec .GetStream (_cg .Get ("\u0044\u0065\u0073\u0074\u004f\u0075\u0074\u0070\u0075\u0074\u0050\u0072o\u0066\u0069\u006c\u0065"));_df {_acb ._cec .Objects =append (_acb ._cec .Objects ,_cgf );};_aeg ,_ga :=oi .(*_ec .PdfIndirectObject );
if !_ga {_aeg =_ec .MakeIndirectObject (oi );};if _acb ._dc ==nil {_acb ._dc =_ec .MakeArray (_aeg );}else {_acb ._dc .Append (_aeg );};_acb ._cec .Objects =append (_acb ._cec .Objects ,_aeg );return nil ;};func (_acc *Catalog )GetOutputIntents ()(*OutputIntents ,bool ){_ab :=_acc .Object .Get ("\u004f\u0075\u0074\u0070\u0075\u0074\u0049\u006e\u0074\u0065\u006e\u0074\u0073");
if _ab ==nil {return nil ,false ;};_cbe ,_dg :=_ec .GetIndirect (_ab );if !_dg {return nil ,false ;};_egb ,_ae :=_ec .GetArray (_cbe .PdfObject );if !_ae {return nil ,false ;};return &OutputIntents {_eb :_cbe ,_dc :_egb ,_cec :_acc ._be },true ;};func (_bf *Document )GetPages ()([]Page ,bool ){_gf ,_eefg :=_bf .FindCatalog ();
if !_eefg {return nil ,false ;};return _gf .GetPages ();};func (_age *Content )SetData (data []byte )error {_dff ,_fdg :=_ec .MakeStream (data ,_ec .NewFlateEncoder ());if _fdg !=nil {return _fdg ;};_fbcc ,_fgfb :=_ec .GetArray (_age ._gbe .Object .Get ("\u0043\u006f\u006e\u0074\u0065\u006e\u0074\u0073"));
if !_fgfb &&_age ._aee ==0{_age ._gbe .Object .Set ("\u0043\u006f\u006e\u0074\u0065\u006e\u0074\u0073",_dff );}else {if _fdg =_fbcc .Set (_age ._aee ,_dff );_fdg !=nil {return _fdg ;};};_age ._gbe ._bbb .Objects =append (_age ._gbe ._bbb .Objects ,_dff );
return nil ;};type Catalog struct{Object *_ec .PdfObjectDictionary ;_be *Document ;};func (_edb Page )FindXObjectForms ()[]*_ec .PdfObjectStream {_adee ,_bfa :=_edb .GetResourcesXObject ();if !_bfa {return nil ;};_ggaf :=map[*_ec .PdfObjectStream ]struct{}{};
var _agd func (_cga *_ec .PdfObjectDictionary ,_beg map[*_ec .PdfObjectStream ]struct{});_agd =func (_ged *_ec .PdfObjectDictionary ,_eeee map[*_ec .PdfObjectStream ]struct{}){for _ ,_gfg :=range _ged .Keys (){_fgbb ,_fdf :=_ec .GetStream (_ged .Get (_gfg ));
if !_fdf {continue ;};if _ ,_aaa :=_eeee [_fgbb ];_aaa {continue ;};_ega ,_eeg :=_ec .GetName (_fgbb .Get ("\u0053u\u0062\u0074\u0079\u0070\u0065"));if !_eeg ||_ega .String ()!="\u0046\u006f\u0072\u006d"{continue ;};_eeee [_fgbb ]=struct{}{};_cde ,_eeg :=_ec .GetDict (_fgbb .Get ("\u0052e\u0073\u006f\u0075\u0072\u0063\u0065s"));
if !_eeg {continue ;};_beba ,_accb :=_ec .GetDict (_cde .Get ("\u0058O\u0062\u006a\u0065\u0063\u0074"));if _accb {_agd (_beba ,_eeee );};};};_agd (_adee ,_ggaf );var _dfd []*_ec .PdfObjectStream ;for _eda :=range _ggaf {_dfd =append (_dfd ,_eda );};return _dfd ;
};func (_afg *OutputIntents )Len ()int {return _afg ._dc .Len ()};type Document struct{ID [2]string ;Version _ec .Version ;Objects []_ec .PdfObject ;Info _ec .PdfObject ;Crypt *_ec .PdfCrypt ;UseHashBasedID bool ;};func (_g *Catalog )SetVersion (){_g .Object .Set ("\u0056e\u0072\u0073\u0069\u006f\u006e",_ec .MakeName (_ea .Sprintf ("\u0025\u0064\u002e%\u0064",_g ._be .Version .Major ,_g ._be .Version .Minor )));
};func (_dgd *Document )FindCatalog ()(*Catalog ,bool ){var _cfd *_ec .PdfObjectDictionary ;for _ ,_ada :=range _dgd .Objects {_da ,_bae :=_ec .GetDict (_ada );if !_bae {continue ;};if _fg ,_eee :=_ec .GetName (_da .Get ("\u0054\u0079\u0070\u0065"));_eee &&*_fg =="\u0043a\u0074\u0061\u006c\u006f\u0067"{_cfd =_da ;
break ;};};if _cfd ==nil {return nil ,false ;};return &Catalog {Object :_cfd ,_be :_dgd },true ;};func (_de *Catalog )NewOutputIntents ()*OutputIntents {return &OutputIntents {_cec :_de ._be }};func (_fbc *Catalog )GetMarkInfo ()(*_ec .PdfObjectDictionary ,bool ){_fed ,_af :=_ec .GetDict (_fbc .Object .Get ("\u004d\u0061\u0072\u006b\u0049\u006e\u0066\u006f"));
return _fed ,_af ;};