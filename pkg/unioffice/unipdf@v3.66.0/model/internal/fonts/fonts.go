//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package fonts ;import (_ba "bytes";_d "encoding/binary";_b "errors";_e "fmt";_cd "github.com/unidoc/unipdf/v3/common";_ga "github.com/unidoc/unipdf/v3/core";_bg "github.com/unidoc/unipdf/v3/internal/cmap";_ff "github.com/unidoc/unipdf/v3/internal/textencoding";
_gb "io";_ed "os";_a "regexp";_cg "sort";_c "strings";_f "sync";);func _ffc ()StdFont {_edb .Do (_bbb );_bef :=Descriptor {Name :TimesItalicName ,Family :_egac ,Weight :FontWeightMedium ,Flags :0x0060,BBox :[4]float64 {-169,-217,1010,883},ItalicAngle :-15.5,Ascent :683,Descent :-217,CapHeight :653,XHeight :441,StemV :76,StemH :32};
return NewStdFont (_bef ,_afb );};var _ggbc =[]int16 {722,1000,722,722,722,722,722,722,722,722,722,722,722,722,722,722,722,722,722,612,667,667,667,667,667,667,667,667,667,722,556,611,778,778,778,722,278,278,278,278,278,278,278,278,556,722,722,611,611,611,611,611,833,722,722,722,722,722,778,1000,778,778,778,778,778,778,778,778,667,778,722,722,722,722,667,667,667,667,667,611,611,611,667,722,722,722,722,722,722,722,722,722,667,944,667,667,667,667,611,611,611,611,556,556,556,556,333,556,889,556,556,722,556,556,584,584,389,975,556,611,278,280,389,389,333,333,333,280,350,556,556,333,556,556,333,556,333,333,278,250,737,556,611,556,556,743,611,400,333,584,556,333,278,556,556,556,556,556,556,556,556,1000,556,1000,556,556,584,611,333,333,333,611,556,611,556,556,167,611,611,611,611,333,584,549,556,556,333,333,611,333,333,278,278,278,278,278,278,278,278,556,556,278,278,400,278,584,549,584,494,278,889,333,584,611,584,611,611,611,611,556,549,611,556,611,611,611,611,944,333,611,611,611,556,834,834,333,370,365,611,611,611,556,333,333,494,889,278,278,1000,584,584,611,611,611,474,500,500,500,278,278,278,238,389,389,549,389,389,737,333,556,556,556,556,556,556,333,556,556,278,278,556,600,333,389,333,611,556,834,333,333,1000,556,333,611,611,611,611,611,611,611,556,611,611,556,778,556,556,556,556,556,500,500,500,500,556};
func (_aa CharMetrics )String ()string {return _e .Sprintf ("<\u0025\u002e\u0031\u0066\u002c\u0025\u002e\u0031\u0066\u003e",_aa .Wx ,_aa .Wy );};func _ad ()StdFont {_gbf .Do (_cbc );_acf :=Descriptor {Name :CourierName ,Family :string (CourierName ),Weight :FontWeightMedium ,Flags :0x0021,BBox :[4]float64 {-23,-250,715,805},ItalicAngle :0,Ascent :629,Descent :-157,CapHeight :562,XHeight :426,StemV :51,StemH :51};
return NewStdFont (_acf ,_cf );};func (_af *RuneCharSafeMap )Length ()int {_af ._fb .RLock ();defer _af ._fb .RUnlock ();return len (_af ._edg );};var _acb =[]int16 {722,1000,722,722,722,722,722,722,722,722,722,667,722,722,722,722,722,722,722,612,667,667,667,667,667,667,667,667,667,722,500,611,778,778,778,778,389,389,389,389,389,389,389,389,500,778,778,667,667,667,667,667,944,722,722,722,722,722,778,1000,778,778,778,778,778,778,778,778,611,778,722,722,722,722,556,556,556,556,556,667,667,667,611,722,722,722,722,722,722,722,722,722,722,1000,722,722,722,722,667,667,667,667,500,500,500,500,333,500,722,500,500,833,500,500,581,520,500,930,500,556,278,220,394,394,333,333,333,220,350,444,444,333,444,444,333,500,333,333,250,250,747,500,556,500,500,672,556,400,333,570,500,333,278,444,444,444,444,444,444,444,500,1000,444,1000,500,444,570,500,333,333,333,556,500,556,500,500,167,500,500,500,556,333,570,549,500,500,333,333,556,333,333,278,278,278,278,278,278,278,333,556,556,278,278,394,278,570,549,570,494,278,833,333,570,556,570,556,556,556,556,500,549,556,500,500,500,500,500,722,333,500,500,500,500,750,750,300,300,330,500,500,556,540,333,333,494,1000,250,250,1000,570,570,556,500,500,555,500,500,500,333,333,333,278,444,444,549,444,444,747,333,389,389,389,389,389,500,333,500,500,278,250,500,600,333,416,333,556,500,750,300,333,1000,500,300,556,556,556,556,556,556,556,500,556,556,500,722,500,500,500,500,500,444,444,444,444,500};
func (_gbeb *ttfParser )ParseOS2 ()error {if _feg :=_gbeb .Seek ("\u004f\u0053\u002f\u0032");_feg !=nil {return _feg ;};_gaba :=_gbeb .ReadUShort ();_gbeb .Skip (4*2);_gbeb .Skip (11*2+10+4*4+4);_bebb :=_gbeb .ReadUShort ();_gbeb ._eab .Bold =(_bebb &32)!=0;
_gbeb .Skip (2*2);_gbeb ._eab .TypoAscender =_gbeb .ReadShort ();_gbeb ._eab .TypoDescender =_gbeb .ReadShort ();if _gaba >=2{_gbeb .Skip (3*2+2*4+2);_gbeb ._eab .CapHeight =_gbeb .ReadShort ();}else {_gbeb ._eab .CapHeight =0;};return nil ;};func _gbd ()StdFont {_ecb .Do (_fbf );
_dd :=Descriptor {Name :HelveticaObliqueName ,Family :string (HelveticaName ),Weight :FontWeightMedium ,Flags :0x0060,BBox :[4]float64 {-170,-225,1116,931},ItalicAngle :-12,Ascent :718,Descent :-207,CapHeight :718,XHeight :523,StemV :88,StemH :76};return NewStdFont (_dd ,_bdc );
};var _cdf *RuneCharSafeMap ;type GlyphName =_ff .GlyphName ;func NewFontFile2FromPdfObject (obj _ga .PdfObject )(TtfType ,error ){obj =_ga .TraceToDirectObject (obj );_eee ,_faed :=obj .(*_ga .PdfObjectStream );if !_faed {_cd .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u0046\u006f\u006e\u0074\u0046\u0069\u006c\u0065\u0032\u0020\u006d\u0075\u0073\u0074\u0020\u0062\u0065 \u0061\u0020\u0073\u0074\u0072e\u0061\u006d \u0028\u0025\u0054\u0029",obj );
return TtfType {},_ga .ErrTypeError ;};_dgg ,_acbb :=_ga .DecodeStream (_eee );if _acbb !=nil {return TtfType {},_acbb ;};_gcdg :=ttfParser {_gga :_ba .NewReader (_dgg )};return _gcdg .Parse ();};func (_agd StdFont )Name ()string {return string (_agd ._aac .Name )};
func (_abe *ttfParser )ReadStr (length int )(string ,error ){_efc :=make ([]byte ,length );_ddbad ,_aaee :=_abe ._gga .Read (_efc );if _aaee !=nil {return "",_aaee ;}else if _ddbad !=length {return "",_e .Errorf ("\u0075\u006e\u0061bl\u0065\u0020\u0074\u006f\u0020\u0072\u0065\u0061\u0064\u0020\u0025\u0064\u0020\u0062\u0079\u0074\u0065\u0073",length );
};return string (_efc ),nil ;};func (_dbg *ttfParser )ParseHead ()error {if _bgb :=_dbg .Seek ("\u0068\u0065\u0061\u0064");_bgb !=nil {return _bgb ;};_dbg .Skip (3*4);_fgf :=_dbg .ReadULong ();if _fgf !=0x5F0F3CF5{_cd .Log .Debug ("\u0045\u0052\u0052\u004f\u0052:\u0020\u0049\u006e\u0063\u006fr\u0072e\u0063\u0074\u0020\u006d\u0061\u0067\u0069\u0063\u0020\u006e\u0075\u006d\u0062\u0065\u0072\u002e\u0020\u0046\u006fn\u0074\u0020\u006d\u0061\u0079\u0020\u006e\u006f\u0074\u0020\u0064\u0069\u0073\u0070\u006c\u0061\u0079\u0020\u0063\u006f\u0072\u0072\u0065\u0063t\u006c\u0079\u002e\u0020\u0025\u0073",_dbg );
};_dbg .Skip (2);_dbg ._eab .UnitsPerEm =_dbg .ReadUShort ();_dbg .Skip (2*8);_dbg ._eab .Xmin =_dbg .ReadShort ();_dbg ._eab .Ymin =_dbg .ReadShort ();_dbg ._eab .Xmax =_dbg .ReadShort ();_dbg ._eab .Ymax =_dbg .ReadShort ();return nil ;};var _cc *RuneCharSafeMap ;
var _bdc *RuneCharSafeMap ;func RegisterStdFont (name StdFontName ,fnc func ()StdFont ,aliases ...StdFontName ){if _ ,_ac :=_df .read (name );_ac {panic ("\u0066o\u006e\u0074\u0020\u0061l\u0072\u0065\u0061\u0064\u0079 \u0072e\u0067i\u0073\u0074\u0065\u0072\u0065\u0064\u003a "+string (name ));
};_df .write (name ,fnc );for _ ,_eag :=range aliases {RegisterStdFont (_eag ,fnc );};};var _fbe *RuneCharSafeMap ;func (_fe *fontMap )write (_fee StdFontName ,_eb func ()StdFont ){_fe .Lock ();defer _fe .Unlock ();_fe ._ea [_fee ]=_eb ;};func _ebe ()StdFont {_ecb .Do (_fbf );
_agdd :=Descriptor {Name :HelveticaBoldObliqueName ,Family :string (HelveticaName ),Weight :FontWeightBold ,Flags :0x0060,BBox :[4]float64 {-174,-228,1114,962},ItalicAngle :-12,Ascent :718,Descent :-207,CapHeight :718,XHeight :532,StemV :140,StemH :118};
return NewStdFont (_agdd ,_dca );};var _bfg *RuneCharSafeMap ;var _ Font =StdFont {};func _eccd ()StdFont {_edb .Do (_bbb );_eef :=Descriptor {Name :TimesRomanName ,Family :_egac ,Weight :FontWeightRoman ,Flags :0x0020,BBox :[4]float64 {-168,-218,1000,898},ItalicAngle :0,Ascent :683,Descent :-217,CapHeight :662,XHeight :450,StemV :84,StemH :28};
return NewStdFont (_eef ,_aeb );};func (_gccec *ttfParser )ReadShort ()(_abd int16 ){_d .Read (_gccec ._gga ,_d .BigEndian ,&_abd );return _abd ;};func _gdff (_acd map[string ]uint32 )string {var _ebc []string ;for _efb :=range _acd {_ebc =append (_ebc ,_efb );
};_cg .Slice (_ebc ,func (_gcbg ,_eba int )bool {return _acd [_ebc [_gcbg ]]< _acd [_ebc [_eba ]]});_gbe :=[]string {_e .Sprintf ("\u0054\u0072\u0075\u0065Ty\u0070\u0065\u0020\u0074\u0061\u0062\u006c\u0065\u0073\u003a\u0020\u0025\u0064",len (_acd ))};for _ ,_bfcb :=range _ebc {_gbe =append (_gbe ,_e .Sprintf ("\u0009%\u0071\u0020\u0025\u0035\u0064",_bfcb ,_acd [_bfcb ]));
};return _c .Join (_gbe ,"\u000a");};const (CourierName =StdFontName ("\u0043o\u0075\u0072\u0069\u0065\u0072");CourierBoldName =StdFontName ("\u0043\u006f\u0075r\u0069\u0065\u0072\u002d\u0042\u006f\u006c\u0064");CourierObliqueName =StdFontName ("\u0043o\u0075r\u0069\u0065\u0072\u002d\u004f\u0062\u006c\u0069\u0071\u0075\u0065");
CourierBoldObliqueName =StdFontName ("\u0043\u006f\u0075\u0072ie\u0072\u002d\u0042\u006f\u006c\u0064\u004f\u0062\u006c\u0069\u0071\u0075\u0065"););type Descriptor struct{Name StdFontName ;Family string ;Weight FontWeight ;Flags uint ;BBox [4]float64 ;ItalicAngle float64 ;
Ascent float64 ;Descent float64 ;CapHeight float64 ;XHeight float64 ;StemV float64 ;StemH float64 ;};func (_cfc *TtfType )String ()string {return _e .Sprintf ("\u0046\u004fN\u0054\u005f\u0046\u0049\u004cE\u0032\u007b\u0025\u0023\u0071 \u0055\u006e\u0069\u0074\u0073\u0050\u0065\u0072\u0045\u006d\u003d\u0025\u0064\u0020\u0042\u006f\u006c\u0064\u003d\u0025\u0074\u0020\u0049\u0074\u0061\u006c\u0069\u0063\u0041\u006e\u0067\u006c\u0065\u003d\u0025\u0066\u0020"+"\u0043\u0061pH\u0065\u0069\u0067h\u0074\u003d\u0025\u0064 Ch\u0061rs\u003d\u0025\u0064\u0020\u0047\u006c\u0079ph\u004e\u0061\u006d\u0065\u0073\u003d\u0025d\u007d",_cfc .PostScriptName ,_cfc .UnitsPerEm ,_cfc .Bold ,_cfc .ItalicAngle ,_cfc .CapHeight ,len (_cfc .Chars ),len (_cfc .GlyphNames ));
};func _bc ()StdFont {_gbf .Do (_cbc );_abb :=Descriptor {Name :CourierObliqueName ,Family :string (CourierName ),Weight :FontWeightMedium ,Flags :0x0061,BBox :[4]float64 {-27,-250,849,805},ItalicAngle :-12,Ascent :629,Descent :-157,CapHeight :562,XHeight :426,StemV :51,StemH :51};
return NewStdFont (_abb ,_fc );};var _eeac =[]GlyphName {"\u002en\u006f\u0074\u0064\u0065\u0066","\u002e\u006e\u0075l\u006c","\u006e\u006fn\u006d\u0061\u0072k\u0069\u006e\u0067\u0072\u0065\u0074\u0075\u0072\u006e","\u0073\u0070\u0061c\u0065","\u0065\u0078\u0063\u006c\u0061\u006d","\u0071\u0075\u006f\u0074\u0065\u0064\u0062\u006c","\u006e\u0075\u006d\u0062\u0065\u0072\u0073\u0069\u0067\u006e","\u0064\u006f\u006c\u006c\u0061\u0072","\u0070e\u0072\u0063\u0065\u006e\u0074","\u0061m\u0070\u0065\u0072\u0073\u0061\u006ed","q\u0075\u006f\u0074\u0065\u0073\u0069\u006e\u0067\u006c\u0065","\u0070a\u0072\u0065\u006e\u006c\u0065\u0066t","\u0070\u0061\u0072\u0065\u006e\u0072\u0069\u0067\u0068\u0074","\u0061\u0073\u0074\u0065\u0072\u0069\u0073\u006b","\u0070\u006c\u0075\u0073","\u0063\u006f\u006dm\u0061","\u0068\u0079\u0070\u0068\u0065\u006e","\u0070\u0065\u0072\u0069\u006f\u0064","\u0073\u006c\u0061s\u0068","\u007a\u0065\u0072\u006f","\u006f\u006e\u0065","\u0074\u0077\u006f","\u0074\u0068\u0072e\u0065","\u0066\u006f\u0075\u0072","\u0066\u0069\u0076\u0065","\u0073\u0069\u0078","\u0073\u0065\u0076e\u006e","\u0065\u0069\u0067h\u0074","\u006e\u0069\u006e\u0065","\u0063\u006f\u006co\u006e","\u0073e\u006d\u0069\u0063\u006f\u006c\u006fn","\u006c\u0065\u0073\u0073","\u0065\u0071\u0075a\u006c","\u0067r\u0065\u0061\u0074\u0065\u0072","\u0071\u0075\u0065\u0073\u0074\u0069\u006f\u006e","\u0061\u0074","\u0041","\u0042","\u0043","\u0044","\u0045","\u0046","\u0047","\u0048","\u0049","\u004a","\u004b","\u004c","\u004d","\u004e","\u004f","\u0050","\u0051","\u0052","\u0053","\u0054","\u0055","\u0056","\u0057","\u0058","\u0059","\u005a","b\u0072\u0061\u0063\u006b\u0065\u0074\u006c\u0065\u0066\u0074","\u0062a\u0063\u006b\u0073\u006c\u0061\u0073h","\u0062\u0072\u0061c\u006b\u0065\u0074\u0072\u0069\u0067\u0068\u0074","a\u0073\u0063\u0069\u0069\u0063\u0069\u0072\u0063\u0075\u006d","\u0075\u006e\u0064\u0065\u0072\u0073\u0063\u006f\u0072\u0065","\u0067\u0072\u0061v\u0065","\u0061","\u0062","\u0063","\u0064","\u0065","\u0066","\u0067","\u0068","\u0069","\u006a","\u006b","\u006c","\u006d","\u006e","\u006f","\u0070","\u0071","\u0072","\u0073","\u0074","\u0075","\u0076","\u0077","\u0078","\u0079","\u007a","\u0062r\u0061\u0063\u0065\u006c\u0065\u0066t","\u0062\u0061\u0072","\u0062\u0072\u0061\u0063\u0065\u0072\u0069\u0067\u0068\u0074","\u0061\u0073\u0063\u0069\u0069\u0074\u0069\u006c\u0064\u0065","\u0041d\u0069\u0065\u0072\u0065\u0073\u0069s","\u0041\u0072\u0069n\u0067","\u0043\u0063\u0065\u0064\u0069\u006c\u006c\u0061","\u0045\u0061\u0063\u0075\u0074\u0065","\u004e\u0074\u0069\u006c\u0064\u0065","\u004fd\u0069\u0065\u0072\u0065\u0073\u0069s","\u0055d\u0069\u0065\u0072\u0065\u0073\u0069s","\u0061\u0061\u0063\u0075\u0074\u0065","\u0061\u0067\u0072\u0061\u0076\u0065","a\u0063\u0069\u0072\u0063\u0075\u006d\u0066\u006c\u0065\u0078","\u0061d\u0069\u0065\u0072\u0065\u0073\u0069s","\u0061\u0074\u0069\u006c\u0064\u0065","\u0061\u0072\u0069n\u0067","\u0063\u0063\u0065\u0064\u0069\u006c\u006c\u0061","\u0065\u0061\u0063\u0075\u0074\u0065","\u0065\u0067\u0072\u0061\u0076\u0065","e\u0063\u0069\u0072\u0063\u0075\u006d\u0066\u006c\u0065\u0078","\u0065d\u0069\u0065\u0072\u0065\u0073\u0069s","\u0069\u0061\u0063\u0075\u0074\u0065","\u0069\u0067\u0072\u0061\u0076\u0065","i\u0063\u0069\u0072\u0063\u0075\u006d\u0066\u006c\u0065\u0078","\u0069d\u0069\u0065\u0072\u0065\u0073\u0069s","\u006e\u0074\u0069\u006c\u0064\u0065","\u006f\u0061\u0063\u0075\u0074\u0065","\u006f\u0067\u0072\u0061\u0076\u0065","o\u0063\u0069\u0072\u0063\u0075\u006d\u0066\u006c\u0065\u0078","\u006fd\u0069\u0065\u0072\u0065\u0073\u0069s","\u006f\u0074\u0069\u006c\u0064\u0065","\u0075\u0061\u0063\u0075\u0074\u0065","\u0075\u0067\u0072\u0061\u0076\u0065","u\u0063\u0069\u0072\u0063\u0075\u006d\u0066\u006c\u0065\u0078","\u0075d\u0069\u0065\u0072\u0065\u0073\u0069s","\u0064\u0061\u0067\u0067\u0065\u0072","\u0064\u0065\u0067\u0072\u0065\u0065","\u0063\u0065\u006e\u0074","\u0073\u0074\u0065\u0072\u006c\u0069\u006e\u0067","\u0073e\u0063\u0074\u0069\u006f\u006e","\u0062\u0075\u006c\u006c\u0065\u0074","\u0070a\u0072\u0061\u0067\u0072\u0061\u0070h","\u0067\u0065\u0072\u006d\u0061\u006e\u0064\u0062\u006c\u0073","\u0072\u0065\u0067\u0069\u0073\u0074\u0065\u0072\u0065\u0064","\u0063o\u0070\u0079\u0072\u0069\u0067\u0068t","\u0074r\u0061\u0064\u0065\u006d\u0061\u0072k","\u0061\u0063\u0075t\u0065","\u0064\u0069\u0065\u0072\u0065\u0073\u0069\u0073","\u006e\u006f\u0074\u0065\u0071\u0075\u0061\u006c","\u0041\u0045","\u004f\u0073\u006c\u0061\u0073\u0068","\u0069\u006e\u0066\u0069\u006e\u0069\u0074\u0079","\u0070l\u0075\u0073\u006d\u0069\u006e\u0075s","\u006ce\u0073\u0073\u0065\u0071\u0075\u0061l","\u0067\u0072\u0065a\u0074\u0065\u0072\u0065\u0071\u0075\u0061\u006c","\u0079\u0065\u006e","\u006d\u0075","p\u0061\u0072\u0074\u0069\u0061\u006c\u0064\u0069\u0066\u0066","\u0073u\u006d\u006d\u0061\u0074\u0069\u006fn","\u0070r\u006f\u0064\u0075\u0063\u0074","\u0070\u0069","\u0069\u006e\u0074\u0065\u0067\u0072\u0061\u006c","o\u0072\u0064\u0066\u0065\u006d\u0069\u006e\u0069\u006e\u0065","\u006f\u0072\u0064m\u0061\u0073\u0063\u0075\u006c\u0069\u006e\u0065","\u004f\u006d\u0065g\u0061","\u0061\u0065","\u006f\u0073\u006c\u0061\u0073\u0068","\u0071\u0075\u0065s\u0074\u0069\u006f\u006e\u0064\u006f\u0077\u006e","\u0065\u0078\u0063\u006c\u0061\u006d\u0064\u006f\u0077\u006e","\u006c\u006f\u0067\u0069\u0063\u0061\u006c\u006e\u006f\u0074","\u0072a\u0064\u0069\u0063\u0061\u006c","\u0066\u006c\u006f\u0072\u0069\u006e","a\u0070\u0070\u0072\u006f\u0078\u0065\u0071\u0075\u0061\u006c","\u0044\u0065\u006ct\u0061","\u0067\u0075\u0069\u006c\u006c\u0065\u006d\u006f\u0074\u006c\u0065\u0066\u0074","\u0067\u0075\u0069\u006c\u006c\u0065\u006d\u006f\u0074r\u0069\u0067\u0068\u0074","\u0065\u006c\u006c\u0069\u0070\u0073\u0069\u0073","\u006e\u006fn\u0062\u0072\u0065a\u006b\u0069\u006e\u0067\u0073\u0070\u0061\u0063\u0065","\u0041\u0067\u0072\u0061\u0076\u0065","\u0041\u0074\u0069\u006c\u0064\u0065","\u004f\u0074\u0069\u006c\u0064\u0065","\u004f\u0045","\u006f\u0065","\u0065\u006e\u0064\u0061\u0073\u0068","\u0065\u006d\u0064\u0061\u0073\u0068","\u0071\u0075\u006ft\u0065\u0064\u0062\u006c\u006c\u0065\u0066\u0074","\u0071\u0075\u006f\u0074\u0065\u0064\u0062\u006c\u0072\u0069\u0067\u0068\u0074","\u0071u\u006f\u0074\u0065\u006c\u0065\u0066t","\u0071\u0075\u006f\u0074\u0065\u0072\u0069\u0067\u0068\u0074","\u0064\u0069\u0076\u0069\u0064\u0065","\u006co\u007a\u0065\u006e\u0067\u0065","\u0079d\u0069\u0065\u0072\u0065\u0073\u0069s","\u0059d\u0069\u0065\u0072\u0065\u0073\u0069s","\u0066\u0072\u0061\u0063\u0074\u0069\u006f\u006e","\u0063\u0075\u0072\u0072\u0065\u006e\u0063\u0079","\u0067\u0075\u0069\u006c\u0073\u0069\u006e\u0067\u006c\u006c\u0065\u0066\u0074","\u0067\u0075\u0069\u006c\u0073\u0069\u006e\u0067\u006cr\u0069\u0067\u0068\u0074","\u0066\u0069","\u0066\u006c","\u0064a\u0067\u0067\u0065\u0072\u0064\u0062l","\u0070\u0065\u0072\u0069\u006f\u0064\u0063\u0065\u006et\u0065\u0072\u0065\u0064","\u0071\u0075\u006f\u0074\u0065\u0073\u0069\u006e\u0067l\u0062\u0061\u0073\u0065","\u0071\u0075\u006ft\u0065\u0064\u0062\u006c\u0062\u0061\u0073\u0065","p\u0065\u0072\u0074\u0068\u006f\u0075\u0073\u0061\u006e\u0064","A\u0063\u0069\u0072\u0063\u0075\u006d\u0066\u006c\u0065\u0078","E\u0063\u0069\u0072\u0063\u0075\u006d\u0066\u006c\u0065\u0078","\u0041\u0061\u0063\u0075\u0074\u0065","\u0045d\u0069\u0065\u0072\u0065\u0073\u0069s","\u0045\u0067\u0072\u0061\u0076\u0065","\u0049\u0061\u0063\u0075\u0074\u0065","I\u0063\u0069\u0072\u0063\u0075\u006d\u0066\u006c\u0065\u0078","\u0049d\u0069\u0065\u0072\u0065\u0073\u0069s","\u0049\u0067\u0072\u0061\u0076\u0065","\u004f\u0061\u0063\u0075\u0074\u0065","O\u0063\u0069\u0072\u0063\u0075\u006d\u0066\u006c\u0065\u0078","\u0061\u0070\u0070l\u0065","\u004f\u0067\u0072\u0061\u0076\u0065","\u0055\u0061\u0063\u0075\u0074\u0065","U\u0063\u0069\u0072\u0063\u0075\u006d\u0066\u006c\u0065\u0078","\u0055\u0067\u0072\u0061\u0076\u0065","\u0064\u006f\u0074\u006c\u0065\u0073\u0073\u0069","\u0063\u0069\u0072\u0063\u0075\u006d\u0066\u006c\u0065\u0078","\u0074\u0069\u006cd\u0065","\u006d\u0061\u0063\u0072\u006f\u006e","\u0062\u0072\u0065v\u0065","\u0064o\u0074\u0061\u0063\u0063\u0065\u006et","\u0072\u0069\u006e\u0067","\u0063e\u0064\u0069\u006c\u006c\u0061","\u0068\u0075\u006eg\u0061\u0072\u0075\u006d\u006c\u0061\u0075\u0074","\u006f\u0067\u006f\u006e\u0065\u006b","\u0063\u0061\u0072o\u006e","\u004c\u0073\u006c\u0061\u0073\u0068","\u006c\u0073\u006c\u0061\u0073\u0068","\u0053\u0063\u0061\u0072\u006f\u006e","\u0073\u0063\u0061\u0072\u006f\u006e","\u005a\u0063\u0061\u0072\u006f\u006e","\u007a\u0063\u0061\u0072\u006f\u006e","\u0062r\u006f\u006b\u0065\u006e\u0062\u0061r","\u0045\u0074\u0068","\u0065\u0074\u0068","\u0059\u0061\u0063\u0075\u0074\u0065","\u0079\u0061\u0063\u0075\u0074\u0065","\u0054\u0068\u006fr\u006e","\u0074\u0068\u006fr\u006e","\u006d\u0069\u006eu\u0073","\u006d\u0075\u006c\u0074\u0069\u0070\u006c\u0079","o\u006e\u0065\u0073\u0075\u0070\u0065\u0072\u0069\u006f\u0072","t\u0077\u006f\u0073\u0075\u0070\u0065\u0072\u0069\u006f\u0072","\u0074\u0068\u0072\u0065\u0065\u0073\u0075\u0070\u0065\u0072\u0069\u006f\u0072","\u006fn\u0065\u0068\u0061\u006c\u0066","\u006f\u006e\u0065\u0071\u0075\u0061\u0072\u0074\u0065\u0072","\u0074\u0068\u0072\u0065\u0065\u0071\u0075\u0061\u0072\u0074\u0065\u0072\u0073","\u0066\u0072\u0061n\u0063","\u0047\u0062\u0072\u0065\u0076\u0065","\u0067\u0062\u0072\u0065\u0076\u0065","\u0049\u0064\u006f\u0074\u0061\u0063\u0063\u0065\u006e\u0074","\u0053\u0063\u0065\u0064\u0069\u006c\u006c\u0061","\u0073\u0063\u0065\u0064\u0069\u006c\u006c\u0061","\u0043\u0061\u0063\u0075\u0074\u0065","\u0063\u0061\u0063\u0075\u0074\u0065","\u0043\u0063\u0061\u0072\u006f\u006e","\u0063\u0063\u0061\u0072\u006f\u006e","\u0064\u0063\u0072\u006f\u0061\u0074"};
func (_aabg *ttfParser )parseCmapSubtable10 (_gfc int64 )error {if _aabg ._eab .Chars ==nil {_aabg ._eab .Chars =make (map[rune ]GID );};_aabg ._gga .Seek (int64 (_aabg ._adf ["\u0063\u006d\u0061\u0070"])+_gfc ,_gb .SeekStart );var _bbg ,_dgd uint32 ;_fbg :=_aabg .ReadUShort ();
if _fbg < 8{_bbg =uint32 (_aabg .ReadUShort ());_dgd =uint32 (_aabg .ReadUShort ());}else {_aabg .ReadUShort ();_bbg =_aabg .ReadULong ();_dgd =_aabg .ReadULong ();};_cd .Log .Trace ("\u0070\u0061r\u0073\u0065\u0043\u006d\u0061p\u0053\u0075\u0062\u0074\u0061b\u006c\u0065\u0031\u0030\u003a\u0020\u0066\u006f\u0072\u006d\u0061\u0074\u003d\u0025\u0064\u0020\u006c\u0065\u006e\u0067\u0074\u0068\u003d\u0025\u0064\u0020\u006c\u0061\u006e\u0067\u0075\u0061\u0067\u0065\u003d\u0025\u0064",_fbg ,_bbg ,_dgd );
if _fbg !=0{return _b .New ("\u0075\u006e\u0073\u0075\u0070\u0070\u006f\u0072\u0074\u0065\u0064\u0020\u0063\u006d\u0061p\u0020s\u0075\u0062\u0074\u0061\u0062\u006c\u0065\u0020\u0066\u006f\u0072\u006d\u0061\u0074");};_ffgc ,_dgfe :=_aabg .ReadStr (256);
if _dgfe !=nil {return _dgfe ;};_egaf :=[]byte (_ffgc );for _cfe ,_gad :=range _egaf {_aabg ._eab .Chars [rune (_cfe )]=GID (_gad );if _gad !=0{_e .Printf ("\u0009\u0030\u0078\u002502\u0078\u0020\u279e\u0020\u0030\u0078\u0025\u0030\u0032\u0078\u003d\u0025\u0063\u000a",_cfe ,_gad ,rune (_gad ));
};};return nil ;};type fontMap struct{_f .Mutex ;_ea map[StdFontName ]func ()StdFont ;};func init (){RegisterStdFont (CourierName ,_ad ,"\u0043\u006f\u0075\u0072\u0069\u0065\u0072\u0043\u006f\u0075\u0072\u0069e\u0072\u004e\u0065\u0077","\u0043\u006f\u0075\u0072\u0069\u0065\u0072\u004e\u0065\u0077");
RegisterStdFont (CourierBoldName ,_fdb ,"\u0043o\u0075r\u0069\u0065\u0072\u004e\u0065\u0077\u002c\u0042\u006f\u006c\u0064");RegisterStdFont (CourierObliqueName ,_bc ,"\u0043\u006f\u0075\u0072\u0069\u0065\u0072\u004e\u0065\u0077\u002c\u0049t\u0061\u006c\u0069\u0063");
RegisterStdFont (CourierBoldObliqueName ,_gcc ,"C\u006f\u0075\u0072\u0069er\u004ee\u0077\u002c\u0042\u006f\u006cd\u0049\u0074\u0061\u006c\u0069\u0063");};var _acg =[]int16 {667,1000,667,667,667,667,667,667,667,667,667,667,722,722,722,722,722,722,722,612,667,667,667,667,667,667,667,667,667,722,556,611,778,778,778,722,278,278,278,278,278,278,278,278,500,667,667,556,556,556,556,556,833,722,722,722,722,722,778,1000,778,778,778,778,778,778,778,778,667,778,722,722,722,722,667,667,667,667,667,611,611,611,667,722,722,722,722,722,722,722,722,722,667,944,667,667,667,667,611,611,611,611,556,556,556,556,333,556,889,556,556,667,556,556,469,584,389,1015,556,556,278,260,334,334,278,278,333,260,350,500,500,333,500,500,333,556,333,278,278,250,737,556,556,556,556,643,556,400,333,584,556,333,278,556,556,556,556,556,556,556,556,1000,556,1000,556,556,584,556,278,333,278,500,556,500,556,556,167,556,556,556,611,333,584,549,556,556,333,333,556,333,333,222,278,278,278,278,278,222,222,500,500,222,222,299,222,584,549,584,471,222,833,333,584,556,584,556,556,556,556,556,549,556,556,556,556,556,556,944,333,556,556,556,556,834,834,333,370,365,611,556,556,537,333,333,476,889,278,278,1000,584,584,556,556,611,355,333,333,333,222,222,222,191,333,333,453,333,333,737,333,500,500,500,500,500,556,278,556,556,278,278,556,600,278,317,278,556,556,834,333,333,1000,556,333,556,556,556,556,556,556,556,556,556,556,500,722,500,500,500,500,556,500,500,500,500,556};
func (_abfd *ttfParser )parseCmapFormat0 ()error {_acda ,_eced :=_abfd .ReadStr (256);if _eced !=nil {return _eced ;};_fdce :=[]byte (_acda );_cd .Log .Trace ("\u0070a\u0072\u0073e\u0043\u006d\u0061p\u0046\u006f\u0072\u006d\u0061\u0074\u0030:\u0020\u0025\u0073\u000a\u0064\u0061t\u0061\u0053\u0074\u0072\u003d\u0025\u002b\u0071\u000a\u0064\u0061t\u0061\u003d\u005b\u0025\u0020\u0030\u0032\u0078\u005d",_abfd ._eab .String (),_acda ,_fdce );
for _ada ,_gaa :=range _fdce {_abfd ._eab .Chars [rune (_ada )]=GID (_gaa );};return nil ;};func _agc ()StdFont {_fae :=_ff .NewZapfDingbatsEncoder ();_dff :=Descriptor {Name :ZapfDingbatsName ,Family :string (ZapfDingbatsName ),Weight :FontWeightMedium ,Flags :0x0004,BBox :[4]float64 {-1,-143,981,820},ItalicAngle :0,Ascent :0,Descent :0,CapHeight :0,XHeight :0,StemV :90,StemH :28};
return NewStdFontWithEncoding (_dff ,_ffb ,_fae );};func IsStdFont (name StdFontName )bool {_ ,_fdd :=_df .read (name );return _fdd };func (_gfa *TtfType )MakeToUnicode ()*_bg .CMap {_cce :=make (map[_bg .CharCode ]rune );if len (_gfa .GlyphNames )==0{for _fbc :=range _gfa .Chars {_cce [_bg .CharCode (_fbc )]=_fbc ;
};return _bg .NewToUnicodeCMap (_cce );};for _cea ,_bfc :=range _gfa .Chars {_bab :=_bg .CharCode (_cea );_gcb :=_gfa .GlyphNames [_bfc ];_gbgc ,_eaed :=_ff .GlyphToRune (_gcb );if !_eaed {_cd .Log .Debug ("\u004e\u006f \u0072\u0075\u006e\u0065\u002e\u0020\u0063\u006f\u0064\u0065\u003d\u0030\u0078\u0025\u0030\u0034\u0078\u0020\u0067\u006c\u0079\u0070h=\u0025\u0071",_cea ,_gcb );
_gbgc =_ff .MissingCodeRune ;};_cce [_bab ]=_gbgc ;};return _bg .NewToUnicodeCMap (_cce );};type StdFontName string ;func _ebg ()StdFont {_ecb .Do (_fbf );_bec :=Descriptor {Name :HelveticaBoldName ,Family :string (HelveticaName ),Weight :FontWeightBold ,Flags :0x0020,BBox :[4]float64 {-170,-228,1003,962},ItalicAngle :0,Ascent :718,Descent :-207,CapHeight :718,XHeight :532,StemV :140,StemH :118};
return NewStdFont (_bec ,_bfg );};func (_ab *RuneCharSafeMap )Range (f func (_ef rune ,_be CharMetrics )(_cb bool )){_ab ._fb .RLock ();defer _ab ._fb .RUnlock ();for _beg ,_bf :=range _ab ._edg {if f (_beg ,_bf ){break ;};};};func (_bge *ttfParser )ParseCmap ()error {var _ccb int64 ;
if _fdg :=_bge .Seek ("\u0063\u006d\u0061\u0070");_fdg !=nil {return _fdg ;};_bge .ReadUShort ();_gdd :=int (_bge .ReadUShort ());_abfb :=int64 (0);_cfbd :=int64 (0);_abcf :=int64 (0);for _bcd :=0;_bcd < _gdd ;_bcd ++{_cdg :=_bge .ReadUShort ();_fdc :=_bge .ReadUShort ();
_ccb =int64 (_bge .ReadULong ());if _cdg ==3&&_fdc ==1{_cfbd =_ccb ;}else if _cdg ==3&&_fdc ==10{_abcf =_ccb ;}else if _cdg ==1&&_fdc ==0{_abfb =_ccb ;};};if _abfb !=0{if _dce :=_bge .parseCmapVersion (_abfb );_dce !=nil {return _dce ;};};if _cfbd !=0{if _cdbc :=_bge .parseCmapSubtable31 (_cfbd );
_cdbc !=nil {return _cdbc ;};};if _abcf !=0{if _acgd :=_bge .parseCmapVersion (_abcf );_acgd !=nil {return _acgd ;};};if _cfbd ==0&&_abfb ==0&&_abcf ==0{_cd .Log .Debug ("\u0074\u0074\u0066P\u0061\u0072\u0073\u0065\u0072\u002e\u0050\u0061\u0072\u0073\u0065\u0043\u006d\u0061\u0070\u002e\u0020\u004e\u006f\u0020\u0033\u0031\u002c\u0020\u0031\u0030\u002c\u0020\u00331\u0030\u0020\u0074\u0061\u0062\u006c\u0065\u002e");
};return nil ;};func _cbf ()StdFont {_ecb .Do (_fbf );_bd :=Descriptor {Name :HelveticaName ,Family :string (HelveticaName ),Weight :FontWeightMedium ,Flags :0x0020,BBox :[4]float64 {-166,-225,1000,931},ItalicAngle :0,Ascent :718,Descent :-207,CapHeight :718,XHeight :523,StemV :88,StemH :76};
return NewStdFont (_bd ,_ebbcf );};var _cde =[]int16 {722,889,722,722,722,722,722,722,722,722,722,667,667,667,667,667,722,722,722,612,611,611,611,611,611,611,611,611,611,722,500,556,722,722,722,722,333,333,333,333,333,333,333,333,389,722,722,611,611,611,611,611,889,722,722,722,722,722,722,889,722,722,722,722,722,722,722,722,556,722,667,667,667,667,556,556,556,556,556,611,611,611,556,722,722,722,722,722,722,722,722,722,722,944,722,722,722,722,611,611,611,611,444,444,444,444,333,444,667,444,444,778,444,444,469,541,500,921,444,500,278,200,480,480,333,333,333,200,350,444,444,333,444,444,333,500,333,278,250,250,760,500,500,500,500,588,500,400,333,564,500,333,278,444,444,444,444,444,444,444,500,1000,444,1000,500,444,564,500,333,333,333,556,500,556,500,500,167,500,500,500,500,333,564,549,500,500,333,333,500,333,333,278,278,278,278,278,278,278,278,500,500,278,278,344,278,564,549,564,471,278,778,333,564,500,564,500,500,500,500,500,549,500,500,500,500,500,500,722,333,500,500,500,500,750,750,300,276,310,500,500,500,453,333,333,476,833,250,250,1000,564,564,500,444,444,408,444,444,444,333,333,333,180,333,333,453,333,333,760,333,389,389,389,389,389,500,278,500,500,278,250,500,600,278,326,278,500,500,750,300,333,980,500,300,500,500,500,500,500,500,500,500,500,500,500,722,500,500,500,500,500,444,444,444,444,500};
func init (){RegisterStdFont (HelveticaName ,_cbf ,"\u0041\u0072\u0069a\u006c");RegisterStdFont (HelveticaBoldName ,_ebg ,"\u0041\u0072\u0069\u0061\u006c\u002c\u0042\u006f\u006c\u0064");RegisterStdFont (HelveticaObliqueName ,_gbd ,"\u0041\u0072\u0069a\u006c\u002c\u0049\u0074\u0061\u006c\u0069\u0063");
RegisterStdFont (HelveticaBoldObliqueName ,_ebe ,"\u0041\u0072i\u0061\u006c\u002cB\u006f\u006c\u0064\u0049\u0074\u0061\u006c\u0069\u0063");};var _df =&fontMap {_ea :make (map[StdFontName ]func ()StdFont )};type FontWeight int ;type CharMetrics struct{Wx float64 ;
Wy float64 ;};func (_fbb *fontMap )read (_gd StdFontName )(func ()StdFont ,bool ){_fbb .Lock ();defer _fbb .Unlock ();_gg ,_dc :=_fbb ._ea [_gd ];return _gg ,_dc ;};func TtfParse (r _gb .ReadSeeker )(TtfType ,error ){_aed :=&ttfParser {_gga :r };return _aed .Parse ()};
func (_dgfc *ttfParser )ReadULong ()(_dbeb uint32 ){_d .Read (_dgfc ._gga ,_d .BigEndian ,&_dbeb );return _dbeb ;};func (_gcd StdFont )GetMetricsTable ()*RuneCharSafeMap {return _gcd ._eg };var _abc =[]rune {'A','Æ','Á','Ă','Â','Ä','À','Ā','Ą','Å','Ã','B','C','Ć','Č','Ç','D','Ď','Đ','∆','E','É','Ě','Ê','Ë','Ė','È','Ē','Ę','Ð','€','F','G','Ğ','Ģ','H','I','Í','Î','Ï','İ','Ì','Ī','Į','J','K','Ķ','L','Ĺ','Ľ','Ļ','Ł','M','N','Ń','Ň','Ņ','Ñ','O','Œ','Ó','Ô','Ö','Ò','Ő','Ō','Ø','Õ','P','Q','R','Ŕ','Ř','Ŗ','S','Ś','Š','Ş','Ș','T','Ť','Ţ','Þ','U','Ú','Û','Ü','Ù','Ű','Ū','Ų','Ů','V','W','X','Y','Ý','Ÿ','Z','Ź','Ž','Ż','a','á','ă','â','´','ä','æ','à','ā','&','ą','å','^','~','*','@','ã','b','\\','|','{','}','[',']','˘','¦','•','c','ć','ˇ','č','ç','¸','¢','ˆ',':',',','\uf6c3','©','¤','d','†','‡','ď','đ','°','¨','÷','$','˙','ı','e','é','ě','ê','ë','ė','è','8','…','ē','—','–','ę','=','ð','!','¡','f','ﬁ','5','ﬂ','ƒ','4','⁄','g','ğ','ģ','ß','`','>','≥','«','»','‹','›','h','˝','-','i','í','î','ï','ì','ī','į','j','k','ķ','l','ĺ','ľ','ļ','<','≤','¬','◊','ł','m','¯','−','µ','×','n','ń','ň','ņ','9','≠','ñ','#','o','ó','ô','ö','œ','˛','ò','ő','ō','1','½','¼','¹','ª','º','ø','õ','p','¶','(',')','∂','%','.','·','‰','+','±','q','?','¿','"','„','“','”','‘','’','‚','\'','r','ŕ','√','ř','ŗ','®','˚','s','ś','š','ş','ș','§',';','7','6','/',' ','£','∑','t','ť','ţ','þ','3','¾','³','˜','™','2','²','u','ú','û','ü','ù','ű','ū','_','ų','ů','v','w','x','y','ý','ÿ','¥','z','ź','ž','ż','0'};
func (_aede *ttfParser )parseCmapFormat6 ()error {_ccde :=int (_aede .ReadUShort ());_gdc :=int (_aede .ReadUShort ());_cd .Log .Trace ("p\u0061\u0072\u0073\u0065\u0043\u006d\u0061\u0070\u0046o\u0072\u006d\u0061\u0074\u0036\u003a\u0020%s\u0020\u0066\u0069\u0072s\u0074\u0043\u006f\u0064\u0065\u003d\u0025\u0064\u0020en\u0074\u0072y\u0043\u006f\u0075\u006e\u0074\u003d\u0025\u0064",_aede ._eab .String (),_ccde ,_gdc );
for _dgff :=0;_dgff < _gdc ;_dgff ++{_fdcd :=GID (_aede .ReadUShort ());_aede ._eab .Chars [rune (_dgff +_ccde )]=_fdcd ;};return nil ;};func (_ded *ttfParser )ReadSByte ()(_gcf int8 ){_d .Read (_ded ._gga ,_d .BigEndian ,&_gcf );return _gcf };func (_afge *ttfParser )ParseName ()error {if _ddc :=_afge .Seek ("\u006e\u0061\u006d\u0065");
_ddc !=nil {return _ddc ;};_dadc ,_ :=_afge ._gga .Seek (0,_gb .SeekCurrent );_afge ._eab .PostScriptName ="";_afge .Skip (2);_dgge :=_afge .ReadUShort ();_edcg :=_afge .ReadUShort ();for _cbff :=uint16 (0);_cbff < _dgge &&_afge ._eab .PostScriptName =="";
_cbff ++{_afge .Skip (3*2);_cbe :=_afge .ReadUShort ();_ebgd :=_afge .ReadUShort ();_gcg :=_afge .ReadUShort ();if _cbe ==6{_afge ._gga .Seek (_dadc +int64 (_edcg )+int64 (_gcg ),_gb .SeekStart );_eefd ,_aga :=_afge .ReadStr (int (_ebgd ));if _aga !=nil {return _aga ;
};_eefd =_c .Replace (_eefd ,"\u0000","",-1);_cae ,_aga :=_a .Compile ("\u005b\u0028\u0029\u007b\u007d\u003c\u003e\u0020\u002f%\u005b\u005c\u005d\u005d");if _aga !=nil {return _aga ;};_afge ._eab .PostScriptName =_cae .ReplaceAllString (_eefd ,"");};};
if _afge ._eab .PostScriptName ==""{_cd .Log .Debug ("\u0050a\u0072\u0073e\u004e\u0061\u006de\u003a\u0020\u0054\u0068\u0065\u0020\u006ea\u006d\u0065\u0020\u0050\u006f\u0073t\u0053\u0063\u0072\u0069\u0070\u0074\u0020\u0077\u0061\u0073\u0020n\u006f\u0074\u0020\u0066\u006f\u0075\u006e\u0064\u002e");
};return nil ;};func _fdb ()StdFont {_gbf .Do (_cbc );_gea :=Descriptor {Name :CourierBoldName ,Family :string (CourierName ),Weight :FontWeightBold ,Flags :0x0021,BBox :[4]float64 {-113,-250,749,801},ItalicAngle :0,Ascent :629,Descent :-157,CapHeight :562,XHeight :439,StemV :106,StemH :84};
return NewStdFont (_gea ,_cdf );};var _cf *RuneCharSafeMap ;func init (){RegisterStdFont (SymbolName ,_dcf ,"\u0053\u0079\u006d\u0062\u006f\u006c\u002c\u0049\u0074\u0061\u006c\u0069\u0063","S\u0079\u006d\u0062\u006f\u006c\u002c\u0042\u006f\u006c\u0064","\u0053\u0079\u006d\u0062\u006f\u006c\u002c\u0042\u006f\u006c\u0064\u0049t\u0061\u006c\u0069\u0063");
RegisterStdFont (ZapfDingbatsName ,_agc );};func (_dbd *ttfParser )parseCmapFormat12 ()error {_aeafb :=_dbd .ReadULong ();_cd .Log .Trace ("\u0070\u0061\u0072se\u0043\u006d\u0061\u0070\u0046\u006f\u0072\u006d\u0061t\u00312\u003a \u0025s\u0020\u006e\u0075\u006d\u0047\u0072\u006f\u0075\u0070\u0073\u003d\u0025\u0064",_dbd ._eab .String (),_aeafb );
for _dfb :=uint32 (0);_dfb < _aeafb ;_dfb ++{_cggf :=_dbd .ReadULong ();_afbf :=_dbd .ReadULong ();_efaf :=_dbd .ReadULong ();if _cggf > 0x0010FFFF||(0xD800<=_cggf &&_cggf <=0xDFFF){return _b .New ("\u0069n\u0076\u0061\u006c\u0069\u0064\u0020\u0063\u0068\u0061\u0072\u0061c\u0074\u0065\u0072\u0073\u0020\u0063\u006f\u0064\u0065\u0073");
};if _afbf < _cggf ||_afbf > 0x0010FFFF||(0xD800<=_afbf &&_afbf <=0xDFFF){return _b .New ("\u0069n\u0076\u0061\u006c\u0069\u0064\u0020\u0063\u0068\u0061\u0072\u0061c\u0074\u0065\u0072\u0073\u0020\u0063\u006f\u0064\u0065\u0073");};for _ceaa :=_cggf ;_ceaa <=_afbf ;
_ceaa ++{if _ceaa > 0x10FFFF{_cd .Log .Debug ("\u0046\u006fr\u006d\u0061\u0074\u0020\u0031\u0032\u0020\u0063\u006d\u0061\u0070\u0020\u0063\u006f\u006e\u0074\u0061\u0069\u006e\u0073\u0020\u0063\u0068\u0061\u0072\u0061\u0063\u0074\u0065\u0072\u0020\u0062\u0065\u0079\u006f\u006e\u0064\u0020\u0055\u0043\u0053\u002d\u0034");
};_dbd ._eab .Chars [rune (_ceaa )]=GID (_efaf );_efaf ++;};};return nil ;};func (_dg *RuneCharSafeMap )Copy ()*RuneCharSafeMap {_fg :=MakeRuneCharSafeMap (_dg .Length ());_dg .Range (func (_fd rune ,_da CharMetrics )(_bb bool ){_fg ._edg [_fd ]=_da ;return false });
return _fg ;};const (_egac ="\u0054\u0069\u006de\u0073";TimesRomanName =StdFontName ("T\u0069\u006d\u0065\u0073\u002d\u0052\u006f\u006d\u0061\u006e");TimesBoldName =StdFontName ("\u0054\u0069\u006d\u0065\u0073\u002d\u0042\u006f\u006c\u0064");TimesItalicName =StdFontName ("\u0054\u0069\u006de\u0073\u002d\u0049\u0074\u0061\u006c\u0069\u0063");
TimesBoldItalicName =StdFontName ("\u0054\u0069m\u0065\u0073\u002dB\u006f\u006c\u0064\u0049\u0074\u0061\u006c\u0069\u0063"););func (_ee *RuneCharSafeMap )Write (b rune ,r CharMetrics ){_ee ._fb .Lock ();defer _ee ._fb .Unlock ();_ee ._edg [b ]=r ;};var _ffe =[]int16 {667,944,667,667,667,667,667,667,667,667,667,667,667,667,667,667,722,722,722,612,667,667,667,667,667,667,667,667,667,722,500,667,722,722,722,778,389,389,389,389,389,389,389,389,500,667,667,611,611,611,611,611,889,722,722,722,722,722,722,944,722,722,722,722,722,722,722,722,611,722,667,667,667,667,556,556,556,556,556,611,611,611,611,722,722,722,722,722,722,722,722,722,667,889,667,611,611,611,611,611,611,611,500,500,500,500,333,500,722,500,500,778,500,500,570,570,500,832,500,500,278,220,348,348,333,333,333,220,350,444,444,333,444,444,333,500,333,333,250,250,747,500,500,500,500,608,500,400,333,570,500,333,278,444,444,444,444,444,444,444,500,1000,444,1000,500,444,570,500,389,389,333,556,500,556,500,500,167,500,500,500,500,333,570,549,500,500,333,333,556,333,333,278,278,278,278,278,278,278,278,500,500,278,278,382,278,570,549,606,494,278,778,333,606,576,570,556,556,556,556,500,549,556,500,500,500,500,500,722,333,500,500,500,500,750,750,300,266,300,500,500,500,500,333,333,494,833,250,250,1000,570,570,500,500,500,555,500,500,500,333,333,333,278,389,389,549,389,389,747,333,389,389,389,389,389,500,333,500,500,278,250,500,600,278,366,278,500,500,750,300,333,1000,500,300,556,556,556,556,556,556,556,500,556,556,444,667,500,444,444,444,500,389,389,389,389,500};
func init (){RegisterStdFont (TimesRomanName ,_eccd ,"\u0054\u0069\u006d\u0065\u0073\u004e\u0065\u0077\u0052\u006f\u006d\u0061\u006e","\u0054\u0069\u006de\u0073");RegisterStdFont (TimesBoldName ,_dad ,"\u0054i\u006de\u0073\u004e\u0065\u0077\u0052o\u006d\u0061n\u002c\u0042\u006f\u006c\u0064","\u0054\u0069\u006d\u0065\u0073\u002c\u0042\u006f\u006c\u0064");
RegisterStdFont (TimesItalicName ,_ffc ,"T\u0069m\u0065\u0073\u004e\u0065\u0077\u0052\u006f\u006da\u006e\u002c\u0049\u0074al\u0069\u0063","\u0054\u0069\u006de\u0073\u002c\u0049\u0074\u0061\u006c\u0069\u0063");RegisterStdFont (TimesBoldItalicName ,_ce ,"\u0054i\u006d\u0065\u0073\u004e\u0065\u0077\u0052\u006f\u006d\u0061\u006e,\u0042\u006f\u006c\u0064\u0049\u0074\u0061\u006c\u0069\u0063","\u0054\u0069m\u0065\u0073\u002cB\u006f\u006c\u0064\u0049\u0074\u0061\u006c\u0069\u0063");
};func _fbf (){_ebbcf =MakeRuneCharSafeMap (len (_abc ));_bfg =MakeRuneCharSafeMap (len (_abc ));for _bgd ,_aea :=range _abc {_ebbcf .Write (_aea ,CharMetrics {Wx :float64 (_acg [_bgd ])});_bfg .Write (_aea ,CharMetrics {Wx :float64 (_ggbc [_bgd ])});};
_bdc =_ebbcf .Copy ();_dca =_bfg .Copy ();};var _agf =&RuneCharSafeMap {_edg :map[rune ]CharMetrics {' ':{Wx :250},'!':{Wx :333},'#':{Wx :500},'%':{Wx :833},'&':{Wx :778},'(':{Wx :333},')':{Wx :333},'+':{Wx :549},',':{Wx :250},'.':{Wx :250},'/':{Wx :278},'0':{Wx :500},'1':{Wx :500},'2':{Wx :500},'3':{Wx :500},'4':{Wx :500},'5':{Wx :500},'6':{Wx :500},'7':{Wx :500},'8':{Wx :500},'9':{Wx :500},':':{Wx :278},';':{Wx :278},'<':{Wx :549},'=':{Wx :549},'>':{Wx :549},'?':{Wx :444},'[':{Wx :333},']':{Wx :333},'_':{Wx :500},'{':{Wx :480},'|':{Wx :200},'}':{Wx :480},'¬':{Wx :713},'°':{Wx :400},'±':{Wx :549},'µ':{Wx :576},'×':{Wx :549},'÷':{Wx :549},'ƒ':{Wx :500},'Α':{Wx :722},'Β':{Wx :667},'Γ':{Wx :603},'Ε':{Wx :611},'Ζ':{Wx :611},'Η':{Wx :722},'Θ':{Wx :741},'Ι':{Wx :333},'Κ':{Wx :722},'Λ':{Wx :686},'Μ':{Wx :889},'Ν':{Wx :722},'Ξ':{Wx :645},'Ο':{Wx :722},'Π':{Wx :768},'Ρ':{Wx :556},'Σ':{Wx :592},'Τ':{Wx :611},'Υ':{Wx :690},'Φ':{Wx :763},'Χ':{Wx :722},'Ψ':{Wx :795},'α':{Wx :631},'β':{Wx :549},'γ':{Wx :411},'δ':{Wx :494},'ε':{Wx :439},'ζ':{Wx :494},'η':{Wx :603},'θ':{Wx :521},'ι':{Wx :329},'κ':{Wx :549},'λ':{Wx :549},'ν':{Wx :521},'ξ':{Wx :493},'ο':{Wx :549},'π':{Wx :549},'ρ':{Wx :549},'ς':{Wx :439},'σ':{Wx :603},'τ':{Wx :439},'υ':{Wx :576},'φ':{Wx :521},'χ':{Wx :549},'ψ':{Wx :686},'ω':{Wx :686},'ϑ':{Wx :631},'ϒ':{Wx :620},'ϕ':{Wx :603},'ϖ':{Wx :713},'•':{Wx :460},'…':{Wx :1000},'′':{Wx :247},'″':{Wx :411},'⁄':{Wx :167},'€':{Wx :750},'ℑ':{Wx :686},'℘':{Wx :987},'ℜ':{Wx :795},'Ω':{Wx :768},'ℵ':{Wx :823},'←':{Wx :987},'↑':{Wx :603},'→':{Wx :987},'↓':{Wx :603},'↔':{Wx :1042},'↵':{Wx :658},'⇐':{Wx :987},'⇑':{Wx :603},'⇒':{Wx :987},'⇓':{Wx :603},'⇔':{Wx :1042},'∀':{Wx :713},'∂':{Wx :494},'∃':{Wx :549},'∅':{Wx :823},'∆':{Wx :612},'∇':{Wx :713},'∈':{Wx :713},'∉':{Wx :713},'∋':{Wx :439},'∏':{Wx :823},'∑':{Wx :713},'−':{Wx :549},'∗':{Wx :500},'√':{Wx :549},'∝':{Wx :713},'∞':{Wx :713},'∠':{Wx :768},'∧':{Wx :603},'∨':{Wx :603},'∩':{Wx :768},'∪':{Wx :768},'∫':{Wx :274},'∴':{Wx :863},'∼':{Wx :549},'≅':{Wx :549},'≈':{Wx :549},'≠':{Wx :549},'≡':{Wx :549},'≤':{Wx :549},'≥':{Wx :549},'⊂':{Wx :713},'⊃':{Wx :713},'⊄':{Wx :713},'⊆':{Wx :713},'⊇':{Wx :713},'⊕':{Wx :768},'⊗':{Wx :768},'⊥':{Wx :658},'⋅':{Wx :250},'⌠':{Wx :686},'⌡':{Wx :686},'〈':{Wx :329},'〉':{Wx :329},'◊':{Wx :494},'♠':{Wx :753},'♣':{Wx :753},'♥':{Wx :753},'♦':{Wx :753},'\uf6d9':{Wx :790},'\uf6da':{Wx :790},'\uf6db':{Wx :890},'\uf8e5':{Wx :500},'\uf8e6':{Wx :603},'\uf8e7':{Wx :1000},'\uf8e8':{Wx :790},'\uf8e9':{Wx :790},'\uf8ea':{Wx :786},'\uf8eb':{Wx :384},'\uf8ec':{Wx :384},'\uf8ed':{Wx :384},'\uf8ee':{Wx :384},'\uf8ef':{Wx :384},'\uf8f0':{Wx :384},'\uf8f1':{Wx :494},'\uf8f2':{Wx :494},'\uf8f3':{Wx :494},'\uf8f4':{Wx :494},'\uf8f5':{Wx :686},'\uf8f6':{Wx :384},'\uf8f7':{Wx :384},'\uf8f8':{Wx :384},'\uf8f9':{Wx :384},'\uf8fa':{Wx :384},'\uf8fb':{Wx :384},'\uf8fc':{Wx :494},'\uf8fd':{Wx :494},'\uf8fe':{Wx :494},'\uf8ff':{Wx :790}}};
type StdFont struct{_aac Descriptor ;_eg *RuneCharSafeMap ;_eea _ff .TextEncoder ;};func (_becb *ttfParser )parseCmapSubtable31 (_fbcf int64 )error {_ggc :=make ([]rune ,0,8);_bda :=make ([]rune ,0,8);_cbbe :=make ([]int16 ,0,8);_cff :=make ([]uint16 ,0,8);
_becb ._eab .Chars =make (map[rune ]GID );_becb ._gga .Seek (int64 (_becb ._adf ["\u0063\u006d\u0061\u0070"])+_fbcf ,_gb .SeekStart );_aad :=_becb .ReadUShort ();if _aad !=4{_cd .Log .Debug ("u\u006e\u0065\u0078\u0070\u0065\u0063t\u0065\u0064\u0020\u0073\u0075\u0062t\u0061\u0062\u006c\u0065\u0020\u0066\u006fr\u006d\u0061\u0074\u003a\u0020\u0025\u0064\u0020\u0028\u0025w\u0029",_aad );
return nil ;};_becb .Skip (2*2);_beb :=int (_becb .ReadUShort ()/2);_becb .Skip (3*2);for _aeaf :=0;_aeaf < _beb ;_aeaf ++{_bda =append (_bda ,rune (_becb .ReadUShort ()));};_becb .Skip (2);for _ede :=0;_ede < _beb ;_ede ++{_ggc =append (_ggc ,rune (_becb .ReadUShort ()));
};for _bbbf :=0;_bbbf < _beb ;_bbbf ++{_cbbe =append (_cbbe ,_becb .ReadShort ());};_agcf ,_ :=_becb ._gga .Seek (int64 (0),_gb .SeekCurrent );for _cffa :=0;_cffa < _beb ;_cffa ++{_cff =append (_cff ,_becb .ReadUShort ());};for _efae :=0;_efae < _beb ;
_efae ++{_bdf :=_ggc [_efae ];_ccg :=_bda [_efae ];_dec :=_cbbe [_efae ];_baa :=_cff [_efae ];if _baa > 0{_becb ._gga .Seek (_agcf +2*int64 (_efae )+int64 (_baa ),_gb .SeekStart );};for _ged :=_bdf ;_ged <=_ccg ;_ged ++{if _ged ==0xFFFF{break ;};var _baga int32 ;
if _baa > 0{_baga =int32 (_becb .ReadUShort ());if _baga > 0{_baga +=int32 (_dec );};}else {_baga =_ged +int32 (_dec );};if _baga >=65536{_baga -=65536;};if _baga > 0{_becb ._eab .Chars [_ged ]=GID (_baga );};};};return nil ;};var _gbf _f .Once ;func (_bcc *ttfParser )parseTTC ()(TtfType ,error ){_bcc .Skip (2*2);
_eac :=_bcc .ReadULong ();if _eac < 1{return TtfType {},_b .New ("N\u006f \u0066\u006f\u006e\u0074\u0073\u0020\u0069\u006e \u0054\u0054\u0043\u0020fi\u006c\u0065");};_babd :=_bcc .ReadULong ();_ ,_acgf :=_bcc ._gga .Seek (int64 (_babd ),_gb .SeekStart );
if _acgf !=nil {return TtfType {},_acgf ;};return _bcc .Parse ();};type Font interface{Encoder ()_ff .TextEncoder ;GetRuneMetrics (_ge rune )(CharMetrics ,bool );};func (_ceag *ttfParser )ParsePost ()error {if _adff :=_ceag .Seek ("\u0070\u006f\u0073\u0074");
_adff !=nil {return _adff ;};_aaac :=_ceag .Read32Fixed ();_ceag ._eab .ItalicAngle =_ceag .Read32Fixed ();_ceag ._eab .UnderlinePosition =_ceag .ReadShort ();_ceag ._eab .UnderlineThickness =_ceag .ReadShort ();_ceag ._eab .IsFixedPitch =_ceag .ReadULong ()!=0;
_ceag .ReadULong ();_ceag .ReadULong ();_ceag .ReadULong ();_ceag .ReadULong ();_cd .Log .Trace ("\u0050a\u0072\u0073\u0065\u0050\u006f\u0073\u0074\u003a\u0020\u0066\u006fr\u006d\u0061\u0074\u0054\u0079\u0070\u0065\u003d\u0025\u0066",_aaac );switch _aaac {case 1.0:_ceag ._eab .GlyphNames =_eeac ;
case 2.0:_ddf :=int (_ceag .ReadUShort ());_gag :=make ([]int ,_ddf );_ceag ._eab .GlyphNames =make ([]GlyphName ,_ddf );_cdgf :=-1;for _dag :=0;_dag < _ddf ;_dag ++{_bca :=int (_ceag .ReadUShort ());_gag [_dag ]=_bca ;if _bca <=0x7fff&&_bca > _cdgf {_cdgf =_bca ;
};};var _ddba []GlyphName ;if _cdgf >=len (_eeac ){_ddba =make ([]GlyphName ,_cdgf -len (_eeac )+1);for _fga :=0;_fga < _cdgf -len (_eeac )+1;_fga ++{_cdgff :=int (_ceag .readByte ());_abbe ,_eeaa :=_ceag .ReadStr (_cdgff );if _eeaa !=nil {return _eeaa ;
};_ddba [_fga ]=GlyphName (_abbe );};};for _bad :=0;_bad < _ddf ;_bad ++{_cced :=_gag [_bad ];if _cced < len (_eeac ){_ceag ._eab .GlyphNames [_bad ]=_eeac [_cced ];}else if _cced >=len (_eeac )&&_cced <=32767{_ceag ._eab .GlyphNames [_bad ]=_ddba [_cced -len (_eeac )];
}else {_ceag ._eab .GlyphNames [_bad ]="\u002e\u0075\u006e\u0064\u0065\u0066\u0069\u006e\u0065\u0064";};};case 2.5:_ggab :=make ([]int ,_ceag ._abfa );for _dae :=0;_dae < len (_ggab );_dae ++{_ffbb :=int (_ceag .ReadSByte ());_ggab [_dae ]=_dae +1+_ffbb ;
};_ceag ._eab .GlyphNames =make ([]GlyphName ,len (_ggab ));for _eefe :=0;_eefe < len (_ceag ._eab .GlyphNames );_eefe ++{_gbb :=_eeac [_ggab [_eefe ]];_ceag ._eab .GlyphNames [_eefe ]=_gbb ;};case 3.0:_cd .Log .Debug ("\u004e\u006f\u0020\u0050\u006f\u0073t\u0053\u0063\u0072i\u0070\u0074\u0020n\u0061\u006d\u0065\u0020\u0069\u006e\u0066\u006f\u0072\u006da\u0074\u0069\u006f\u006e\u0020is\u0020\u0070\u0072\u006f\u0076\u0069\u0064\u0065\u0064\u0020\u0066\u006f\u0072\u0020\u0074\u0068\u0065\u0020\u0066\u006f\u006e\u0074\u002e");
default:_cd .Log .Debug ("\u0045\u0052\u0052\u004fR\u003a\u0020\u0055\u006e\u006b\u006e\u006f\u0077\u006e\u0020f\u006fr\u006d\u0061\u0074\u0054\u0079\u0070\u0065=\u0025\u0066",_aaac );};return nil ;};func (_de StdFont )Encoder ()_ff .TextEncoder {return _de ._eea };
var _fc *RuneCharSafeMap ;func (_dcg *ttfParser )ParseComponents ()error {if _fec :=_dcg .ParseHead ();_fec !=nil {return _fec ;};if _fdf :=_dcg .ParseHhea ();_fdf !=nil {return _fdf ;};if _cgb :=_dcg .ParseMaxp ();_cgb !=nil {return _cgb ;};if _cgbb :=_dcg .ParseHmtx ();
_cgbb !=nil {return _cgbb ;};if _ ,_fdbd :=_dcg ._adf ["\u006e\u0061\u006d\u0065"];_fdbd {if _edc :=_dcg .ParseName ();_edc !=nil {return _edc ;};};if _ ,_cbb :=_dcg ._adf ["\u004f\u0053\u002f\u0032"];_cbb {if _cgc :=_dcg .ParseOS2 ();_cgc !=nil {return _cgc ;
};};if _ ,_cbba :=_dcg ._adf ["\u0070\u006f\u0073\u0074"];_cbba {if _cbcg :=_dcg .ParsePost ();_cbcg !=nil {return _cbcg ;};};if _ ,_eca :=_dcg ._adf ["\u0063\u006d\u0061\u0070"];_eca {if _bag :=_dcg .ParseCmap ();_bag !=nil {return _bag ;};};return nil ;
};type RuneCharSafeMap struct{_edg map[rune ]CharMetrics ;_fb _f .RWMutex ;};func (_aaa StdFont )ToPdfObject ()_ga .PdfObject {_cdb :=_ga .MakeDict ();_cdb .Set ("\u0054\u0079\u0070\u0065",_ga .MakeName ("\u0046\u006f\u006e\u0074"));_cdb .Set ("\u0053u\u0062\u0074\u0079\u0070\u0065",_ga .MakeName ("\u0054\u0079\u0070e\u0031"));
_cdb .Set ("\u0042\u0061\u0073\u0065\u0046\u006f\u006e\u0074",_ga .MakeName (_aaa .Name ()));_cdb .Set ("\u0045\u006e\u0063\u006f\u0064\u0069\u006e\u0067",_aaa ._eea .ToPdfObject ());return _ga .MakeIndirectObject (_cdb );};var _ebbcf *RuneCharSafeMap ;
type ttfParser struct{_eab TtfType ;_gga _gb .ReadSeeker ;_adf map[string ]uint32 ;_bece uint16 ;_abfa uint16 ;};const (FontWeightMedium FontWeight =iota ;FontWeightBold ;FontWeightRoman ;);func _dcf ()StdFont {_cfa :=_ff .NewSymbolEncoder ();_fda :=Descriptor {Name :SymbolName ,Family :string (SymbolName ),Weight :FontWeightMedium ,Flags :0x0004,BBox :[4]float64 {-180,-293,1090,1010},ItalicAngle :0,Ascent :0,Descent :0,CapHeight :0,XHeight :0,StemV :85,StemH :92};
return NewStdFontWithEncoding (_fda ,_agf ,_cfa );};func (_age *TtfType )MakeEncoder ()(_ff .SimpleEncoder ,error ){_eceb :=make (map[_ff .CharCode ]GlyphName );for _dbe :=_ff .CharCode (0);_dbe <=256;_dbe ++{_gbg :=rune (_dbe );_ceb ,_eec :=_age .Chars [_gbg ];
if !_eec {continue ;};var _adc GlyphName ;if int (_ceb )>=0&&int (_ceb )< len (_age .GlyphNames ){_adc =_age .GlyphNames [_ceb ];}else {_dabd :=rune (_ceb );if _eae ,_bgc :=_ff .RuneToGlyph (_dabd );_bgc {_adc =_eae ;};};if _adc !=""{_eceb [_dbe ]=_adc ;
};};if len (_eceb )==0{_cd .Log .Debug ("WA\u0052\u004eI\u004e\u0047\u003a\u0020\u005a\u0065\u0072\u006f\u0020l\u0065\u006e\u0067\u0074\u0068\u0020\u0054\u0072\u0075\u0065\u0054\u0079\u0070\u0065\u0020\u0065\u006e\u0063\u006f\u0064\u0069\u006e\u0067\u002e\u0020\u0074\u0074\u0066=\u0025s\u0020\u0043\u0068\u0061\u0072\u0073\u003d\u005b%\u00200\u0032\u0078]",_age ,_age .Chars );
};return _ff .NewCustomSimpleTextEncoder (_eceb ,nil );};func (_ccd *ttfParser )ParseHhea ()error {if _ffg :=_ccd .Seek ("\u0068\u0068\u0065\u0061");_ffg !=nil {return _ffg ;};_ccd .Skip (4+15*2);_ccd ._bece =_ccd .ReadUShort ();return nil ;};type GID =_ff .GID ;
func NewStdFont (desc Descriptor ,metrics *RuneCharSafeMap )StdFont {return NewStdFontWithEncoding (desc ,metrics ,_ff .NewStandardEncoder ());};type TtfType struct{UnitsPerEm uint16 ;PostScriptName string ;Bold bool ;ItalicAngle float64 ;IsFixedPitch bool ;
TypoAscender int16 ;TypoDescender int16 ;UnderlinePosition int16 ;UnderlineThickness int16 ;Xmin ,Ymin ,Xmax ,Ymax int16 ;CapHeight int16 ;Widths []uint16 ;Chars map[rune ]GID ;GlyphNames []GlyphName ;};var _fbeb =[]int16 {611,889,611,611,611,611,611,611,611,611,611,611,667,667,667,667,722,722,722,612,611,611,611,611,611,611,611,611,611,722,500,611,722,722,722,722,333,333,333,333,333,333,333,333,444,667,667,556,556,611,556,556,833,667,667,667,667,667,722,944,722,722,722,722,722,722,722,722,611,722,611,611,611,611,500,500,500,500,500,556,556,556,611,722,722,722,722,722,722,722,722,722,611,833,611,556,556,556,556,556,556,556,500,500,500,500,333,500,667,500,500,778,500,500,422,541,500,920,500,500,278,275,400,400,389,389,333,275,350,444,444,333,444,444,333,500,333,333,250,250,760,500,500,500,500,544,500,400,333,675,500,333,278,444,444,444,444,444,444,444,500,889,444,889,500,444,675,500,333,389,278,500,500,500,500,500,167,500,500,500,500,333,675,549,500,500,333,333,500,333,333,278,278,278,278,278,278,278,278,444,444,278,278,300,278,675,549,675,471,278,722,333,675,500,675,500,500,500,500,500,549,500,500,500,500,500,500,667,333,500,500,500,500,750,750,300,276,310,500,500,500,523,333,333,476,833,250,250,1000,675,675,500,500,500,420,556,556,556,333,333,333,214,389,389,453,389,389,760,333,389,389,389,389,389,500,333,500,500,278,250,500,600,278,300,278,500,500,750,300,333,980,500,300,500,500,500,500,500,500,500,500,500,500,444,667,444,444,444,444,500,389,389,389,389,500};
var _edb _f .Once ;func _cbc (){const _gdf =600;_cf =MakeRuneCharSafeMap (len (_abc ));for _ ,_fa :=range _abc {_cf .Write (_fa ,CharMetrics {Wx :_gdf });};_cdf =_cf .Copy ();_fbe =_cf .Copy ();_fc =_cf .Copy ();};func MakeRuneCharSafeMap (length int )*RuneCharSafeMap {return &RuneCharSafeMap {_edg :make (map[rune ]CharMetrics ,length )};
};func NewStdFontWithEncoding (desc Descriptor ,metrics *RuneCharSafeMap ,encoder _ff .TextEncoder )StdFont {var _aabf rune =0xA0;if _ ,_ece :=metrics .Read (_aabf );!_ece {_gc ,_ :=metrics .Read (0x20);metrics .Write (_aabf ,_gc );};return StdFont {_aac :desc ,_eg :metrics ,_eea :encoder };
};func (_ebbc StdFont )Descriptor ()Descriptor {return _ebbc ._aac };func (_bbba *ttfParser )parseCmapVersion (_ca int64 )error {_cd .Log .Trace ("p\u0061\u0072\u0073\u0065\u0043\u006da\u0070\u0056\u0065\u0072\u0073\u0069\u006f\u006e\u003a \u006f\u0066\u0066s\u0065t\u003d\u0025\u0064",_ca );
if _bbba ._eab .Chars ==nil {_bbba ._eab .Chars =make (map[rune ]GID );};_bbba ._gga .Seek (int64 (_bbba ._adf ["\u0063\u006d\u0061\u0070"])+_ca ,_gb .SeekStart );var _bcf ,_cec uint32 ;_cbfb :=_bbba .ReadUShort ();if _cbfb < 8{_bcf =uint32 (_bbba .ReadUShort ());
_cec =uint32 (_bbba .ReadUShort ());}else {_bbba .ReadUShort ();_bcf =_bbba .ReadULong ();_cec =_bbba .ReadULong ();};_cd .Log .Debug ("\u0070\u0061\u0072\u0073\u0065\u0043m\u0061\u0070\u0056\u0065\u0072\u0073\u0069\u006f\u006e\u003a\u0020\u0066\u006f\u0072\u006d\u0061\u0074\u003d\u0025\u0064 \u006c\u0065\u006e\u0067\u0074\u0068\u003d\u0025\u0064\u0020\u006c\u0061\u006e\u0067u\u0061g\u0065\u003d\u0025\u0064",_cbfb ,_bcf ,_cec );
switch _cbfb {case 0:return _bbba .parseCmapFormat0 ();case 6:return _bbba .parseCmapFormat6 ();case 12:return _bbba .parseCmapFormat12 ();default:_cd .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a \u0055\u006e\u0073\u0075\u0070\u0070\u006f\u0072\u0074\u0065\u0064\u0020\u0063m\u0061\u0070\u0020\u0066\u006f\u0072\u006da\u0074\u003d\u0025\u0064",_cbfb );
return nil ;};};var _dca *RuneCharSafeMap ;func (_fcd *ttfParser )Seek (tag string )error {_ffa ,_fba :=_fcd ._adf [tag ];if !_fba {return _e .Errorf ("\u0074\u0061\u0062\u006ce \u006e\u006f\u0074\u0020\u0066\u006f\u0075\u006e\u0064\u003a\u0020\u0025\u0073",tag );
};_fcd ._gga .Seek (int64 (_ffa ),_gb .SeekStart );return nil ;};var _afb *RuneCharSafeMap ;func _ce ()StdFont {_edb .Do (_bbb );_gf :=Descriptor {Name :TimesBoldItalicName ,Family :_egac ,Weight :FontWeightBold ,Flags :0x0060,BBox :[4]float64 {-200,-218,996,921},ItalicAngle :-15,Ascent :683,Descent :-217,CapHeight :669,XHeight :462,StemV :121,StemH :42};
return NewStdFont (_gf ,_cc );};func _dad ()StdFont {_edb .Do (_bbb );_abf :=Descriptor {Name :TimesBoldName ,Family :_egac ,Weight :FontWeightBold ,Flags :0x0020,BBox :[4]float64 {-168,-218,1000,935},ItalicAngle :0,Ascent :683,Descent :-217,CapHeight :676,XHeight :461,StemV :139,StemH :44};
return NewStdFont (_abf ,_db );};func (_aec *TtfType )NewEncoder ()_ff .TextEncoder {return _ff .NewTrueTypeFontEncoder (_aec .Chars )};func (_afa *ttfParser )Parse ()(TtfType ,error ){_bbf ,_ddb :=_afa .ReadStr (4);if _ddb !=nil {return TtfType {},_ddb ;
};if _bbf =="\u0074\u0074\u0063\u0066"{return _afa .parseTTC ();}else if _bbf !="\u0000\u0001\u0000\u0000"&&_bbf !="\u0074\u0072\u0075\u0065"{_cd .Log .Debug ("\u0055n\u0072\u0065c\u006f\u0067\u006ei\u007a\u0065\u0064\u0020\u0054\u0072\u0075e\u0054\u0079\u0070\u0065\u0020\u0066i\u006c\u0065\u0020\u0066\u006f\u0072\u006d\u0061\u0074\u002e\u0020v\u0065\u0072\u0073\u0069\u006f\u006e\u003d\u0025\u0071",_bbf );
};_befc :=int (_afa .ReadUShort ());_afa .Skip (3*2);_afa ._adf =make (map[string ]uint32 );var _eda string ;for _egf :=0;_egf < _befc ;_egf ++{_eda ,_ddb =_afa .ReadStr (4);if _ddb !=nil {return TtfType {},_ddb ;};_afa .Skip (4);_bbff :=_afa .ReadULong ();
_afa .Skip (4);_afa ._adf [_eda ]=_bbff ;};_cd .Log .Trace (_gdff (_afa ._adf ));if _ddb =_afa .ParseComponents ();_ddb !=nil {return TtfType {},_ddb ;};return _afa ._eab ,nil ;};const (HelveticaName =StdFontName ("\u0048e\u006c\u0076\u0065\u0074\u0069\u0063a");
HelveticaBoldName =StdFontName ("\u0048\u0065\u006c\u0076\u0065\u0074\u0069\u0063\u0061-\u0042\u006f\u006c\u0064");HelveticaObliqueName =StdFontName ("\u0048\u0065\u006c\u0076\u0065\u0074\u0069\u0063\u0061\u002d\u004f\u0062l\u0069\u0071\u0075\u0065");HelveticaBoldObliqueName =StdFontName ("H\u0065\u006c\u0076\u0065ti\u0063a\u002d\u0042\u006f\u006c\u0064O\u0062\u006c\u0069\u0071\u0075\u0065");
);func (_bagg *ttfParser )ParseHmtx ()error {if _efa :=_bagg .Seek ("\u0068\u006d\u0074\u0078");_efa !=nil {return _efa ;};_bagg ._eab .Widths =make ([]uint16 ,0,8);for _bba :=uint16 (0);_bba < _bagg ._bece ;_bba ++{_bagg ._eab .Widths =append (_bagg ._eab .Widths ,_bagg .ReadUShort ());
_bagg .Skip (2);};if _bagg ._bece < _bagg ._abfa &&_bagg ._bece > 0{_cef :=_bagg ._eab .Widths [_bagg ._bece -1];for _ccf :=_bagg ._bece ;_ccf < _bagg ._abfa ;_ccf ++{_bagg ._eab .Widths =append (_bagg ._eab .Widths ,_cef );};};return nil ;};func (_abdf *ttfParser )Read32Fixed ()float64 {_dgged :=float64 (_abdf .ReadShort ());
_fbge :=float64 (_abdf .ReadUShort ())/65536.0;return _dgged +_fbge ;};func TtfParseFile (fileStr string )(TtfType ,error ){_cba ,_geab :=_ed .Open (fileStr );if _geab !=nil {return TtfType {},_geab ;};defer _cba .Close ();return TtfParse (_cba );};func (_bga *ttfParser )ReadUShort ()(_aecf uint16 ){_d .Read (_bga ._gga ,_d .BigEndian ,&_aecf );
return _aecf ;};var _aeb *RuneCharSafeMap ;func (_edge *RuneCharSafeMap )Read (b rune )(CharMetrics ,bool ){_edge ._fb .RLock ();defer _edge ._fb .RUnlock ();_ec ,_dgf :=_edge ._edg [b ];return _ec ,_dgf ;};var _ecb _f .Once ;const (SymbolName =StdFontName ("\u0053\u0079\u006d\u0062\u006f\u006c");
ZapfDingbatsName =StdFontName ("\u005a\u0061\u0070f\u0044\u0069\u006e\u0067\u0062\u0061\u0074\u0073"););var _ffb =&RuneCharSafeMap {_edg :map[rune ]CharMetrics {' ':{Wx :278},'→':{Wx :838},'↔':{Wx :1016},'↕':{Wx :458},'①':{Wx :788},'②':{Wx :788},'③':{Wx :788},'④':{Wx :788},'⑤':{Wx :788},'⑥':{Wx :788},'⑦':{Wx :788},'⑧':{Wx :788},'⑨':{Wx :788},'⑩':{Wx :788},'■':{Wx :761},'▲':{Wx :892},'▼':{Wx :892},'◆':{Wx :788},'●':{Wx :791},'◗':{Wx :438},'★':{Wx :816},'☎':{Wx :719},'☛':{Wx :960},'☞':{Wx :939},'♠':{Wx :626},'♣':{Wx :776},'♥':{Wx :694},'♦':{Wx :595},'✁':{Wx :974},'✂':{Wx :961},'✃':{Wx :974},'✄':{Wx :980},'✆':{Wx :789},'✇':{Wx :790},'✈':{Wx :791},'✉':{Wx :690},'✌':{Wx :549},'✍':{Wx :855},'✎':{Wx :911},'✏':{Wx :933},'✐':{Wx :911},'✑':{Wx :945},'✒':{Wx :974},'✓':{Wx :755},'✔':{Wx :846},'✕':{Wx :762},'✖':{Wx :761},'✗':{Wx :571},'✘':{Wx :677},'✙':{Wx :763},'✚':{Wx :760},'✛':{Wx :759},'✜':{Wx :754},'✝':{Wx :494},'✞':{Wx :552},'✟':{Wx :537},'✠':{Wx :577},'✡':{Wx :692},'✢':{Wx :786},'✣':{Wx :788},'✤':{Wx :788},'✥':{Wx :790},'✦':{Wx :793},'✧':{Wx :794},'✩':{Wx :823},'✪':{Wx :789},'✫':{Wx :841},'✬':{Wx :823},'✭':{Wx :833},'✮':{Wx :816},'✯':{Wx :831},'✰':{Wx :923},'✱':{Wx :744},'✲':{Wx :723},'✳':{Wx :749},'✴':{Wx :790},'✵':{Wx :792},'✶':{Wx :695},'✷':{Wx :776},'✸':{Wx :768},'✹':{Wx :792},'✺':{Wx :759},'✻':{Wx :707},'✼':{Wx :708},'✽':{Wx :682},'✾':{Wx :701},'✿':{Wx :826},'❀':{Wx :815},'❁':{Wx :789},'❂':{Wx :789},'❃':{Wx :707},'❄':{Wx :687},'❅':{Wx :696},'❆':{Wx :689},'❇':{Wx :786},'❈':{Wx :787},'❉':{Wx :713},'❊':{Wx :791},'❋':{Wx :785},'❍':{Wx :873},'❏':{Wx :762},'❐':{Wx :762},'❑':{Wx :759},'❒':{Wx :759},'❖':{Wx :784},'❘':{Wx :138},'❙':{Wx :277},'❚':{Wx :415},'❛':{Wx :392},'❜':{Wx :392},'❝':{Wx :668},'❞':{Wx :668},'❡':{Wx :732},'❢':{Wx :544},'❣':{Wx :544},'❤':{Wx :910},'❥':{Wx :667},'❦':{Wx :760},'❧':{Wx :760},'❶':{Wx :788},'❷':{Wx :788},'❸':{Wx :788},'❹':{Wx :788},'❺':{Wx :788},'❻':{Wx :788},'❼':{Wx :788},'❽':{Wx :788},'❾':{Wx :788},'❿':{Wx :788},'➀':{Wx :788},'➁':{Wx :788},'➂':{Wx :788},'➃':{Wx :788},'➄':{Wx :788},'➅':{Wx :788},'➆':{Wx :788},'➇':{Wx :788},'➈':{Wx :788},'➉':{Wx :788},'➊':{Wx :788},'➋':{Wx :788},'➌':{Wx :788},'➍':{Wx :788},'➎':{Wx :788},'➏':{Wx :788},'➐':{Wx :788},'➑':{Wx :788},'➒':{Wx :788},'➓':{Wx :788},'➔':{Wx :894},'➘':{Wx :748},'➙':{Wx :924},'➚':{Wx :748},'➛':{Wx :918},'➜':{Wx :927},'➝':{Wx :928},'➞':{Wx :928},'➟':{Wx :834},'➠':{Wx :873},'➡':{Wx :828},'➢':{Wx :924},'➣':{Wx :924},'➤':{Wx :917},'➥':{Wx :930},'➦':{Wx :931},'➧':{Wx :463},'➨':{Wx :883},'➩':{Wx :836},'➪':{Wx :836},'➫':{Wx :867},'➬':{Wx :867},'➭':{Wx :696},'➮':{Wx :696},'➯':{Wx :874},'➱':{Wx :874},'➲':{Wx :760},'➳':{Wx :946},'➴':{Wx :771},'➵':{Wx :865},'➶':{Wx :771},'➷':{Wx :888},'➸':{Wx :967},'➹':{Wx :888},'➺':{Wx :831},'➻':{Wx :873},'➼':{Wx :927},'➽':{Wx :970},'➾':{Wx :918},'\uf8d7':{Wx :390},'\uf8d8':{Wx :390},'\uf8d9':{Wx :317},'\uf8da':{Wx :317},'\uf8db':{Wx :276},'\uf8dc':{Wx :276},'\uf8dd':{Wx :509},'\uf8de':{Wx :509},'\uf8df':{Wx :410},'\uf8e0':{Wx :410},'\uf8e1':{Wx :234},'\uf8e2':{Wx :234},'\uf8e3':{Wx :334},'\uf8e4':{Wx :334}}};
var _db *RuneCharSafeMap ;func (_gcbb *ttfParser )Skip (n int ){_gcbb ._gga .Seek (int64 (n ),_gb .SeekCurrent )};func (_gfe *ttfParser )ParseMaxp ()error {if _agcg :=_gfe .Seek ("\u006d\u0061\u0078\u0070");_agcg !=nil {return _agcg ;};_gfe .Skip (4);_gfe ._abfa =_gfe .ReadUShort ();
return nil ;};func (_ega StdFont )GetRuneMetrics (r rune )(CharMetrics ,bool ){_gab ,_ebb :=_ega ._eg .Read (r );return _gab ,_ebb ;};func (_bbfe *ttfParser )readByte ()(_bfgf uint8 ){_d .Read (_bbfe ._gga ,_d .BigEndian ,&_bfgf );return _bfgf ;};func _bbb (){_aeb =MakeRuneCharSafeMap (len (_abc ));
_db =MakeRuneCharSafeMap (len (_abc ));_cc =MakeRuneCharSafeMap (len (_abc ));_afb =MakeRuneCharSafeMap (len (_abc ));for _feb ,_gcce :=range _abc {_aeb .Write (_gcce ,CharMetrics {Wx :float64 (_cde [_feb ])});_db .Write (_gcce ,CharMetrics {Wx :float64 (_acb [_feb ])});
_cc .Write (_gcce ,CharMetrics {Wx :float64 (_ffe [_feb ])});_afb .Write (_gcce ,CharMetrics {Wx :float64 (_fbeb [_feb ])});};};func _gcc ()StdFont {_gbf .Do (_cbc );_dfa :=Descriptor {Name :CourierBoldObliqueName ,Family :string (CourierName ),Weight :FontWeightBold ,Flags :0x0061,BBox :[4]float64 {-57,-250,869,801},ItalicAngle :-12,Ascent :629,Descent :-157,CapHeight :562,XHeight :439,StemV :106,StemH :84};
return NewStdFont (_dfa ,_fbe );};func NewStdFontByName (name StdFontName )(StdFont ,bool ){_ae ,_ag :=_df .read (name );if !_ag {return StdFont {},false ;};return _ae (),true ;};