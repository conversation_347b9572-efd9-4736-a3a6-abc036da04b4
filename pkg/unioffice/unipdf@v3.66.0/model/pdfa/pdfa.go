//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

// Package pdfa provides abstraction to optimize and verify documents with respect to the PDF/A standards.
// NOTE: This implementation is in experimental development state.
//
//	Keep in mind that it might change in the subsequent minor versions.
package pdfa ;import (_ce "errors";_fg "fmt";_ae "github.com/adrg/sysfont";_fd "github.com/trimmer-io/go-xmp/models/dc";_fb "github.com/trimmer-io/go-xmp/models/pdf";_cg "github.com/trimmer-io/go-xmp/models/xmp_base";_bd "github.com/trimmer-io/go-xmp/models/xmp_mm";
_b "github.com/trimmer-io/go-xmp/models/xmp_rights";_de "github.com/trimmer-io/go-xmp/xmp";_g "github.com/unidoc/unipdf/v3/common";_fbd "github.com/unidoc/unipdf/v3/contentstream";_ag "github.com/unidoc/unipdf/v3/core";_ab "github.com/unidoc/unipdf/v3/internal/cmap";
_fe "github.com/unidoc/unipdf/v3/internal/imageutil";_dda "github.com/unidoc/unipdf/v3/internal/timeutils";_a "github.com/unidoc/unipdf/v3/model";_gcf "github.com/unidoc/unipdf/v3/model/internal/colorprofile";_gc "github.com/unidoc/unipdf/v3/model/internal/docutil";
_fba "github.com/unidoc/unipdf/v3/model/internal/fonts";_cd "github.com/unidoc/unipdf/v3/model/xmputil";_gf "github.com/unidoc/unipdf/v3/model/xmputil/pdfaextension";_da "github.com/unidoc/unipdf/v3/model/xmputil/pdfaid";_fa "image/color";_c "math";_d "sort";
_dd "strings";_e "time";);func _adf (_eae bool ,_bad standardType )(pageColorspaceOptimizeFunc ,documentColorspaceOptimizeFunc ){var _dgbbd ,_eea ,_adb bool ;_add :=func (_bddg *_gc .Document ,_ddc *_gc .Page ,_age []*_gc .Image )error {_eea =true ;for _ ,_aeeb :=range _age {switch _aeeb .Colorspace {case "\u0044\u0065\u0076\u0069\u0063\u0065\u0047\u0072\u0061\u0079":_eea =true ;
case "\u0044e\u0076\u0069\u0063\u0065\u0052\u0047B":_dgbbd =true ;case "\u0044\u0065\u0076\u0069\u0063\u0065\u0043\u004d\u0059\u004b":_adb =true ;};};_cfc ,_abc :=_ddc .GetContents ();if !_abc {return nil ;};for _ ,_cbfb :=range _cfc {_efaf ,_cdff :=_cbfb .GetData ();
if _cdff !=nil {continue ;};_gcfd :=_fbd .NewContentStreamParser (string (_efaf ));_gcb ,_cdff :=_gcfd .Parse ();if _cdff !=nil {continue ;};for _ ,_gfag :=range *_gcb {switch _gfag .Operand {case "\u0047","\u0067":_eea =true ;case "\u0052\u0047","\u0072\u0067":_dgbbd =true ;
case "\u004b","\u006b":_adb =true ;case "\u0043\u0053","\u0063\u0073":if len (_gfag .Params )==0{continue ;};_bgc ,_bbf :=_ag .GetName (_gfag .Params [0]);if !_bbf {continue ;};switch _bgc .String (){case "\u0052\u0047\u0042","\u0044e\u0076\u0069\u0063\u0065\u0052\u0047B":_dgbbd =true ;
case "\u0047","\u0044\u0065\u0076\u0069\u0063\u0065\u0047\u0072\u0061\u0079":_eea =true ;case "\u0043\u004d\u0059\u004b","\u0044\u0065\u0076\u0069\u0063\u0065\u0043\u004d\u0059\u004b":_adb =true ;};};};};_affb :=_ddc .FindXObjectForms ();for _ ,_aegb :=range _affb {_fga :=_fbd .NewContentStreamParser (string (_aegb .Stream ));
_cbd ,_cfdg :=_fga .Parse ();if _cfdg !=nil {continue ;};for _ ,_dba :=range *_cbd {switch _dba .Operand {case "\u0047","\u0067":_eea =true ;case "\u0052\u0047","\u0072\u0067":_dgbbd =true ;case "\u004b","\u006b":_adb =true ;case "\u0043\u0053","\u0063\u0073":if len (_dba .Params )==0{continue ;
};_fcaa ,_eaage :=_ag .GetName (_dba .Params [0]);if !_eaage {continue ;};switch _fcaa .String (){case "\u0052\u0047\u0042","\u0044e\u0076\u0069\u0063\u0065\u0052\u0047B":_dgbbd =true ;case "\u0047","\u0044\u0065\u0076\u0069\u0063\u0065\u0047\u0072\u0061\u0079":_eea =true ;
case "\u0043\u004d\u0059\u004b","\u0044\u0065\u0076\u0069\u0063\u0065\u0043\u004d\u0059\u004b":_adb =true ;};};};_cefa ,_cddf :=_ag .GetArray (_ddc .Object .Get ("\u0041\u006e\u006e\u006f\u0074\u0073"));if !_cddf {return nil ;};for _ ,_ddgc :=range _cefa .Elements (){_dddb ,_cfe :=_ag .GetDict (_ddgc );
if !_cfe {continue ;};_egbg :=_dddb .Get ("\u0043");if _egbg ==nil {continue ;};_aagg ,_cfe :=_ag .GetArray (_egbg );if !_cfe {continue ;};switch _aagg .Len (){case 0:case 1:_eea =true ;case 3:_dgbbd =true ;case 4:_adb =true ;};};};return nil ;};_ecf :=func (_eadg *_gc .Document ,_dae []*_gc .Image )error {_feba ,_aeec :=_eadg .FindCatalog ();
if !_aeec {return nil ;};_daec ,_aeec :=_feba .GetOutputIntents ();if _aeec &&_daec .Len ()> 0{return nil ;};if !_aeec {_daec =_feba .NewOutputIntents ();};if !(_dgbbd ||_adb ||_eea ){return nil ;};defer _feba .SetOutputIntents (_daec );if _dgbbd &&!_adb &&!_eea {return _dad (_eadg ,_bad ,_daec );
};if _adb &&!_dgbbd &&!_eea {return _dbbe (_bad ,_daec );};if _eea &&!_dgbbd &&!_adb {return _fece (_bad ,_daec );};if (_dgbbd &&_adb )||(_dgbbd &&_eea )||(_adb &&_eea ){if _ebd :=_eagg (_dae ,_eae );_ebd !=nil {return _ebd ;};if _caa :=_cbgc (_eadg ,_eae );
_caa !=nil {return _caa ;};if _eege :=_bbdb (_eadg ,_eae );_eege !=nil {return _eege ;};if _eeag :=_fbdf (_eadg ,_eae );_eeag !=nil {return _eeag ;};if _eae {return _dbbe (_bad ,_daec );};return _dad (_eadg ,_bad ,_daec );};return nil ;};return _add ,_ecf ;
};func _dcfb (_cefe *_a .CompliancePdfReader )(_bbgg []ViolatedRule ){if _cefe .ParserMetadata ().HasOddLengthHexStrings (){_bbgg =append (_bbgg ,_bb ("\u0036.\u0031\u002e\u0036\u002d\u0031","\u0068\u0065\u0078a\u0064\u0065\u0063\u0069\u006d\u0061\u006c\u0020\u0073\u0074\u0072\u0069\u006e\u0067\u0073\u0020\u0073\u0068\u006f\u0075\u006c\u0064\u0020\u0062\u0065\u0020\u006f\u0066\u0020e\u0076\u0065\u006e\u0020\u0073\u0069\u007a\u0065"));
};if _cefe .ParserMetadata ().HasOddLengthHexStrings (){_bbgg =append (_bbgg ,_bb ("\u0036.\u0031\u002e\u0036\u002d\u0032","\u0068\u0065\u0078\u0061\u0064\u0065\u0063\u0069\u006da\u006c\u0020s\u0074\u0072\u0069\u006e\u0067\u0073\u0020\u0073\u0068o\u0075\u006c\u0064\u0020c\u006f\u006e\u0074\u0061\u0069\u006e\u0020\u006f\u006e\u006c\u0079\u0020\u0063\u0068\u0061\u0072\u0061\u0063\u0074\u0065\u0072\u0073\u0020\u0066\u0072\u006f\u006d\u0020\u0072\u0061n\u0067\u0065\u0020[\u0030\u002d\u0039\u003b\u0041\u002d\u0046\u003b\u0061\u002d\u0066\u005d"));
};return _bbgg ;};func _agged (_ecffa *_a .CompliancePdfReader )(_fbea []ViolatedRule ){var _bgagb ,_cccaa bool ;_fdecc :=func ()bool {return _bgagb &&_cccaa };for _ ,_fdcbg :=range _ecffa .GetObjectNums (){_aeda ,_ccdf :=_ecffa .GetIndirectObjectByNumber (_fdcbg );
if _ccdf !=nil {_g .Log .Debug ("G\u0065\u0074\u0074\u0069\u006e\u0067\u0020\u006f\u0062\u006a\u0065\u0063\u0074\u0020\u0077\u0069\u0074\u0068 \u006e\u0075\u006d\u0062\u0065\u0072\u0020\u0025\u0064\u0020fa\u0069\u006c\u0065d\u003a \u0025\u0076",_fdcbg ,_ccdf );
continue ;};_daeec ,_eeeac :=_ag .GetDict (_aeda );if !_eeeac {continue ;};_fbde ,_eeeac :=_ag .GetName (_daeec .Get ("\u0054\u0079\u0070\u0065"));if !_eeeac {continue ;};if *_fbde !="\u0041\u0063\u0074\u0069\u006f\u006e"{continue ;};_cggc ,_eeeac :=_ag .GetName (_daeec .Get ("\u0053"));
if !_eeeac {if !_bgagb {_fbea =append (_fbea ,_bb ("\u0036.\u0035\u002e\u0031\u002d\u0031","\u0054\u0068\u0065\u0020\u004caun\u0063\u0068\u002c\u0020S\u006f\u0075\u006e\u0064,\u0020\u004d\u006f\u0076\u0069\u0065\u002c\u0020\u0052\u0065\u0073\u0065\u0074\u0046\u006f\u0072\u006d\u002c\u0020\u0049\u006d\u0070\u006f\u0072\u0074\u0044a\u0074\u0061,\u0020\u0048\u0069\u0064\u0065\u002c\u0020\u0053\u0065\u0074\u004f\u0043\u0047\u0053\u0074\u0061\u0074\u0065\u002c\u0020\u0052\u0065\u006e\u0064\u0069\u0074\u0069\u006f\u006e\u002c\u0020T\u0072\u0061\u006e\u0073\u002c\u0020\u0047o\u0054\u006f\u0033\u0044\u0056\u0069\u0065\u0077\u0020\u0061\u006e\u0064\u0020\u004a\u0061v\u0061Sc\u0072\u0069p\u0074\u0020\u0061\u0063\u0074\u0069\u006f\u006e\u0073\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u006e\u006f\u0074 \u0062\u0065\u0020\u0070\u0065\u0072m\u0069\u0074\u0074\u0065\u0064\u002e \u0041\u0064d\u0069\u0074\u0069\u006f\u006e\u0061\u006c\u006c\u0079\u002c\u0020t\u0068\u0065\u0020\u0064\u0065\u0070\u0072\u0065\u0063\u0061\u0074\u0065\u0064\u0020\u0073\u0065\u0074\u002d\u0073\u0074\u0061\u0074\u0065\u0020\u0061\u006e\u0064\u0020\u006e\u006f\u006f\u0070\u0020\u0061c\u0074\u0069\u006f\u006e\u0073\u0020\u0073\u0068\u0061l\u006c\u0020\u006e\u006f\u0074\u0020\u0062\u0065\u0020\u0070e\u0072\u006d\u0069\u0074\u0074\u0065\u0064\u002e"));
_bgagb =true ;if _fdecc (){return _fbea ;};};continue ;};switch _a .PdfActionType (*_cggc ){case _a .ActionTypeLaunch ,_a .ActionTypeSound ,_a .ActionTypeMovie ,_a .ActionTypeResetForm ,_a .ActionTypeImportData ,_a .ActionTypeJavaScript ,_a .ActionTypeHide ,_a .ActionTypeSetOCGState ,_a .ActionTypeRendition ,_a .ActionTypeTrans ,_a .ActionTypeGoTo3DView :if !_bgagb {_fbea =append (_fbea ,_bb ("\u0036.\u0035\u002e\u0031\u002d\u0031","\u0054\u0068\u0065\u0020\u004caun\u0063\u0068\u002c\u0020S\u006f\u0075\u006e\u0064,\u0020\u004d\u006f\u0076\u0069\u0065\u002c\u0020\u0052\u0065\u0073\u0065\u0074\u0046\u006f\u0072\u006d\u002c\u0020\u0049\u006d\u0070\u006f\u0072\u0074\u0044a\u0074\u0061,\u0020\u0048\u0069\u0064\u0065\u002c\u0020\u0053\u0065\u0074\u004f\u0043\u0047\u0053\u0074\u0061\u0074\u0065\u002c\u0020\u0052\u0065\u006e\u0064\u0069\u0074\u0069\u006f\u006e\u002c\u0020T\u0072\u0061\u006e\u0073\u002c\u0020\u0047o\u0054\u006f\u0033\u0044\u0056\u0069\u0065\u0077\u0020\u0061\u006e\u0064\u0020\u004a\u0061v\u0061Sc\u0072\u0069p\u0074\u0020\u0061\u0063\u0074\u0069\u006f\u006e\u0073\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u006e\u006f\u0074 \u0062\u0065\u0020\u0070\u0065\u0072m\u0069\u0074\u0074\u0065\u0064\u002e \u0041\u0064d\u0069\u0074\u0069\u006f\u006e\u0061\u006c\u006c\u0079\u002c\u0020t\u0068\u0065\u0020\u0064\u0065\u0070\u0072\u0065\u0063\u0061\u0074\u0065\u0064\u0020\u0073\u0065\u0074\u002d\u0073\u0074\u0061\u0074\u0065\u0020\u0061\u006e\u0064\u0020\u006e\u006f\u006f\u0070\u0020\u0061c\u0074\u0069\u006f\u006e\u0073\u0020\u0073\u0068\u0061l\u006c\u0020\u006e\u006f\u0074\u0020\u0062\u0065\u0020\u0070e\u0072\u006d\u0069\u0074\u0074\u0065\u0064\u002e"));
_bgagb =true ;if _fdecc (){return _fbea ;};};continue ;case _a .ActionTypeNamed :if !_cccaa {_acfc ,_fbbd :=_ag .GetName (_daeec .Get ("\u004e"));if !_fbbd {_fbea =append (_fbea ,_bb ("\u0036.\u0035\u002e\u0031\u002d\u0032","N\u0061\u006d\u0065\u0064\u0020\u0061\u0063t\u0069\u006f\u006e\u0073\u0020\u006f\u0074\u0068e\u0072\u0020\u0074h\u0061\u006e\u0020\u004e\u0065\u0078\u0074\u0050\u0061\u0067\u0065\u002c\u0020P\u0072\u0065v\u0050\u0061\u0067\u0065\u002c\u0020\u0046\u0069\u0072\u0073\u0074\u0050a\u0067e\u002c\u0020\u0061\u006e\u0064\u0020\u004c\u0061\u0073\u0074\u0050\u0061\u0067\u0065\u0020\u0073h\u0061\u006c\u006c\u0020\u006e\u006f\u0074\u0020\u0062\u0065\u0020\u0070\u0065\u0072\u006d\u0069\u0074\u0074\u0065\u0064\u002e"));
_cccaa =true ;if _fdecc (){return _fbea ;};continue ;};switch *_acfc {case "\u004e\u0065\u0078\u0074\u0050\u0061\u0067\u0065","\u0050\u0072\u0065\u0076\u0050\u0061\u0067\u0065","\u0046i\u0072\u0073\u0074\u0050\u0061\u0067e","\u004c\u0061\u0073\u0074\u0050\u0061\u0067\u0065":default:_fbea =append (_fbea ,_bb ("\u0036.\u0035\u002e\u0031\u002d\u0032","N\u0061\u006d\u0065\u0064\u0020\u0061\u0063t\u0069\u006f\u006e\u0073\u0020\u006f\u0074\u0068e\u0072\u0020\u0074h\u0061\u006e\u0020\u004e\u0065\u0078\u0074\u0050\u0061\u0067\u0065\u002c\u0020P\u0072\u0065v\u0050\u0061\u0067\u0065\u002c\u0020\u0046\u0069\u0072\u0073\u0074\u0050a\u0067e\u002c\u0020\u0061\u006e\u0064\u0020\u004c\u0061\u0073\u0074\u0050\u0061\u0067\u0065\u0020\u0073h\u0061\u006c\u006c\u0020\u006e\u006f\u0074\u0020\u0062\u0065\u0020\u0070\u0065\u0072\u006d\u0069\u0074\u0074\u0065\u0064\u002e"));
_cccaa =true ;if _fdecc (){return _fbea ;};continue ;};};};};return _fbea ;};func _ggf (_fcdg *_gc .Document )error {_ace :=map[string ]*_ag .PdfObjectDictionary {};_afc :=_ae .NewFinder (&_ae .FinderOpts {Extensions :[]string {"\u002e\u0074\u0074\u0066","\u002e\u0074\u0074\u0063"}});
_aag :=map[_ag .PdfObject ]struct{}{};_ced :=map[_ag .PdfObject ]struct{}{};for _ ,_bff :=range _fcdg .Objects {_gfe ,_ffb :=_ag .GetDict (_bff );if !_ffb {continue ;};_geg :=_gfe .Get ("\u0054\u0079\u0070\u0065");if _geg ==nil {continue ;};if _cdg ,_agf :=_ag .GetName (_geg );
_agf &&_cdg .String ()!="\u0046\u006f\u006e\u0074"{continue ;};if _ ,_fdf :=_aag [_bff ];_fdf {continue ;};_gab ,_ccfb :=_a .NewPdfFontFromPdfObject (_gfe );if _ccfb !=nil {_g .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u0063\u006f\u0075\u006c\u0064\u0020\u006e\u006f\u0074\u0020\u006c\u006f\u0061\u0064\u0020\u0066\u006fn\u0074\u0020\u0066\u0072\u006fm\u0020\u006fb\u006a\u0065\u0063\u0074");
return _ccfb ;};if _gab .Encoder ()!=nil &&(_gab .Encoder ().String ()=="\u0049\u0064\u0065\u006e\u0074\u0069\u0074\u0079\u002d\u0048"||_gab .Encoder ().String ()=="\u0049\u0064\u0065\u006e\u0074\u0069\u0074\u0079\u002d\u0056"){continue ;};if _gab .Subtype ()=="\u0043\u0049\u0044F\u006f\u006e\u0074\u0054\u0079\u0070\u0065\u0032"{_ade :=_gab .GetCIDToGIDMapObject ();
if _ade !=nil {continue ;};};_cda ,_ccfb :=_gab .GetFontDescriptor ();if _ccfb !=nil {return _ccfb ;};if _cda !=nil &&(_cda .FontFile !=nil ||_cda .FontFile2 !=nil ||_cda .FontFile3 !=nil ){continue ;};_cdb :=_gab .BaseFont ();if _cdb ==""{_baa ,_dgbb :=_gab .GetFontDescriptor ();
if _dgbb !=nil {return _fg .Errorf ("\u0063\u0061\u006e\u0027\u0074\u0020\u0067\u0065t\u0020\u0074\u0068e \u0066\u006f\u006e\u0074\u0020\u006ea\u006d\u0065\u0020\u0066\u0072\u006f\u006d\u0020\u0066\u006f\u006e\u0074\u0020\u0064\u0065s\u0063\u0072\u0069\u0070\u0074\u006f\u0072\u003a \u0025\u0073",_gfe .String ());
};_cdb =_baa .FontName .String ();if _cdb ==""{return _fg .Errorf ("\u006f\u006e\u0065\u0020\u006f\u0066\u0020\u0074\u0068\u0065\u0020\u0066\u006f\u006e\u0074\u0020\u006f\u0062\u006a\u0065c\u0074\u0073\u0020\u0073\u0079\u006e\u0074\u0061\u0078\u0020\u0069\u0073\u0020\u006e\u006f\u0074\u0020\u0076\u0061\u006c\u0069d\u0020\u002d\u0020\u0042\u0061\u0073\u0065\u0046\u006f\u006e\u0074\u0020\u0075\u006ed\u0065\u0066\u0069n\u0065\u0064\u003a\u0020\u0025\u0073",_gfe .String ());
};};_gdgb ,_ccfba :=_ace [_cdb ];if !_ccfba {if len (_cdb )> 7&&_cdb [6]=='+'{_cdb =_cdb [7:];};_cega :=[]string {_cdb ,"\u0054i\u006de\u0073\u0020\u004e\u0065\u0077\u0020\u0052\u006f\u006d\u0061\u006e","\u0041\u0072\u0069a\u006c","D\u0065\u006a\u0061\u0056\u0075\u0020\u0053\u0061\u006e\u0073"};
for _ ,_cef :=range _cega {_g .Log .Debug ("\u0044\u0045\u0042\u0055\u0047\u003a \u0073\u0065\u0061\u0072\u0063\u0068\u0069\u006e\u0067\u0020\u0073\u0079\u0073t\u0065\u006d\u0020\u0066\u006f\u006e\u0074 \u0060\u0025\u0073\u0060",_cef );if _gdgb ,_ccfba =_ace [_cef ];
_ccfba {break ;};_abeg :=_afc .Match (_cef );if _abeg ==nil {_g .Log .Debug ("c\u006f\u0075\u006c\u0064\u0020\u006eo\u0074\u0020\u0066\u0069\u006e\u0064\u0020\u0066\u006fn\u0074\u0020\u0066i\u006ce\u0020\u0025\u0073",_cef );continue ;};_eac ,_efd :=_a .NewPdfFontFromTTFFile (_abeg .Filename );
if _efd !=nil {return _efd ;};_fcb :=_eac .FontDescriptor ();if _fcb .FontFile !=nil {if _ ,_ccfba =_ced [_fcb .FontFile ];!_ccfba {_fcdg .Objects =append (_fcdg .Objects ,_fcb .FontFile );_ced [_fcb .FontFile ]=struct{}{};};};if _fcb .FontFile2 !=nil {if _ ,_ccfba =_ced [_fcb .FontFile2 ];
!_ccfba {_fcdg .Objects =append (_fcdg .Objects ,_fcb .FontFile2 );_ced [_fcb .FontFile2 ]=struct{}{};};};if _fcb .FontFile3 !=nil {if _ ,_ccfba =_ced [_fcb .FontFile3 ];!_ccfba {_fcdg .Objects =append (_fcdg .Objects ,_fcb .FontFile3 );_ced [_fcb .FontFile3 ]=struct{}{};
};};_ffa ,_efa :=_eac .ToPdfObject ().(*_ag .PdfIndirectObject );if !_efa {_g .Log .Debug ("\u0066\u006f\u006e\u0074\u0020\u0069\u0073\u0020\u006e\u006ft\u0020\u0061\u006e\u0020\u0069\u006e\u0064i\u0072\u0065\u0063\u0074\u0020\u006f\u0062\u006a\u0065\u0063\u0074");
continue ;};_aaa ,_efa :=_ffa .PdfObject .(*_ag .PdfObjectDictionary );if !_efa {_g .Log .Debug ("\u0046\u006fn\u0074\u0020\u0074\u0079p\u0065\u0020i\u0073\u0020\u006e\u006f\u0074\u0020\u0061\u006e \u006f\u0062\u006a\u0065\u0063\u0074\u0020\u0064\u0069\u0063\u0074\u0069o\u006e\u0061\u0072\u0079");
continue ;};_ace [_cef ]=_aaa ;_gdgb =_aaa ;break ;};if _gdgb ==nil {_g .Log .Debug ("\u004e\u006f\u0020\u006d\u0061\u0074\u0063\u0068\u0069\u006eg\u0020\u0066\u006f\u006e\u0074\u0020\u0066o\u0075\u006e\u0064\u0020\u0066\u006f\u0072\u003a\u0020\u0025\u0073",_gab .BaseFont ());
return _ce .New ("\u006e\u006f m\u0061\u0074\u0063h\u0069\u006e\u0067\u0020fon\u0074 f\u006f\u0075\u006e\u0064\u0020\u0069\u006e t\u0068\u0065\u0020\u0073\u0079\u0073\u0074e\u006d");};};for _ ,_gabb :=range _gdgb .Keys (){_gfe .Set (_gabb ,_gdgb .Get (_gabb ));
};_eaa :=_gdgb .Get ("\u0057\u0069\u0064\u0074\u0068\u0073");if _eaa !=nil {if _ ,_ccfba =_ced [_eaa ];!_ccfba {_fcdg .Objects =append (_fcdg .Objects ,_eaa );_ced [_eaa ]=struct{}{};};};_aag [_bff ]=struct{}{};_daf :=_gfe .Get ("\u0046\u006f\u006e\u0074\u0044\u0065\u0073\u0063\u0072i\u0070\u0074\u006f\u0072");
if _daf !=nil {_fcdg .Objects =append (_fcdg .Objects ,_daf );_ced [_daf ]=struct{}{};};};return nil ;};func _dfcb (_cagg *_a .CompliancePdfReader )(_ffaec ViolatedRule ){_bfda ,_acgb :=_gfcb (_cagg );if !_acgb {return _eag ;};if _bfda .Get ("\u0041\u0041")!=nil {return _bb ("\u0036.\u0036\u002e\u0032\u002d\u0033","\u0054\u0068e\u0020\u0064\u006f\u0063\u0075\u006d\u0065n\u0074 \u0063\u0061\u0074a\u006c\u006f\u0067\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u006eo\u0074\u0020\u0069\u006e\u0063\u006c\u0075\u0064\u0065\u0020\u0061\u006e\u0020\u0041\u0041\u0020\u0065n\u0074r\u0079 \u0066\u006f\u0072 \u0061\u006e\u0020\u0061\u0064\u0064\u0069\u0074\u0069\u006f\u006e\u0061\u006c\u002d\u0061\u0063\u0074i\u006f\u006e\u0073\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u002e");
};return _eag ;};type profile2 struct{_ebecg standardType ;_ece Profile2Options ;};func _cedc (_fbdfd *_a .CompliancePdfReader )(_gecf []ViolatedRule ){var _fdfece ,_bdfe ,_ffgdc ,_adgbe bool ;_fbcba :=func ()bool {return _fdfece &&_bdfe &&_ffgdc &&_adgbe };
_geda ,_fecee :=_aggec (_fbdfd );var _ggcbe _gcf .ProfileHeader ;if _fecee {_ggcbe ,_ =_gcf .ParseHeader (_geda .DestOutputProfile );};_decd :=map[_ag .PdfObject ]struct{}{};var _cdab func (_gfaeg _a .PdfColorspace )bool ;_cdab =func (_agcd _a .PdfColorspace )bool {switch _gfeb :=_agcd .(type ){case *_a .PdfColorspaceDeviceGray :if !_fdfece {if !_fecee {_gecf =append (_gecf ,_bb ("\u0036.\u0032\u002e\u0034\u002e\u0033\u002d4","\u0044\u0065\u0076\u0069\u0063\u0065\u0047\u0072\u0061\u0079 \u0073\u0068\u0061\u006c\u006c\u0020\u006f\u006e\u006c\u0079\u0020\u0062\u0065\u0020\u0075\u0073\u0065\u0064 \u0069\u0066\u0020\u0061\u0020\u0064\u0065v\u0069\u0063\u0065\u0020\u0069\u006e\u0064\u0065p\u0065\u006e\u0064\u0065\u006e\u0074\u0020\u0044\u0065\u0066\u0061\u0075\u006c\u0074\u0047\u0072\u0061\u0079\u0020\u0063\u006f\u006c\u006f\u0075r \u0073\u0070\u0061\u0063\u0065\u0020\u0068\u0061\u0073\u0020\u0062\u0065\u0065\u006e \u0073\u0065\u0074\u0020\u0077\u0068\u0065n \u0074\u0068\u0065\u0020\u0044\u0065\u0076\u0069\u0063\u0065\u0047\u0072a\u0079\u0020\u0063\u006f\u006c\u006f\u0075\u0072\u0020\u0073\u0070\u0061\u0063\u0065\u0020\u0069\u0073\u0020\u0075\u0073\u0065\u0064\u002c o\u0072\u0020\u0069\u0066\u0020\u0061\u0020\u0050\u0044\u0046\u002fA\u0020\u004f\u0075tp\u0075\u0074\u0049\u006e\u0074\u0065\u006e\u0074\u0020\u0069\u0073\u0020\u0070\u0072\u0065\u0073\u0065\u006e\u0074\u002e"));
_fdfece =true ;if _fbcba (){return true ;};};};case *_a .PdfColorspaceDeviceRGB :if !_bdfe {if !_fecee ||_ggcbe .ColorSpace !=_gcf .ColorSpaceRGB {_gecf =append (_gecf ,_bb ("\u0036.\u0032\u002e\u0034\u002e\u0033\u002d2","\u0044\u0065\u0076\u0069c\u0065\u0052\u0047\u0042\u0020\u0073\u0068\u0061\u006cl\u0020\u006f\u006e\u006c\u0079\u0020\u0062e\u0020\u0075\u0073\u0065\u0064\u0020\u0069f\u0020\u0061\u0020\u0064\u0065\u0076\u0069\u0063e\u0020\u0069n\u0064\u0065\u0070e\u006e\u0064\u0065\u006et \u0044\u0065\u0066\u0061\u0075\u006c\u0074\u0052\u0047\u0042\u0020\u0063\u006fl\u006f\u0075r\u0020\u0073\u0070\u0061\u0063\u0065\u0020\u0068\u0061\u0073\u0020b\u0065\u0065\u006e\u0020s\u0065\u0074 \u0077\u0068\u0065\u006e\u0020\u0074\u0068\u0065\u0020\u0044\u0065\u0076\u0069\u0063\u0065\u0052\u0047\u0042\u0020c\u006flou\u0072\u0020\u0073\u0070\u0061\u0063\u0065\u0020i\u0073\u0020\u0075\u0073\u0065\u0064\u002c\u0020\u006f\u0072\u0020if\u0020\u0074\u0068\u0065\u0020\u0066\u0069\u006c\u0065\u0020\u0068\u0061\u0073\u0020\u0061\u0020\u0050\u0044F\u002f\u0041\u0020\u004fut\u0070\u0075\u0074\u0049\u006e\u0074\u0065n\u0074\u0020t\u0068\u0061t\u0020\u0063\u006f\u006e\u0074\u0061\u0069\u006e\u0073\u0020\u0061\u006e\u0020\u0052\u0047\u0042\u0020\u0064\u0065\u0073\u0074\u0069\u006e\u0061\u0074io\u006e\u0020\u0070\u0072\u006f\u0066\u0069\u006c\u0065\u002e"));
_bdfe =true ;if _fbcba (){return true ;};};};case *_a .PdfColorspaceDeviceCMYK :if !_ffgdc {if !_fecee ||_ggcbe .ColorSpace !=_gcf .ColorSpaceCMYK {_gecf =append (_gecf ,_bb ("\u0036.\u0032\u002e\u0034\u002e\u0033\u002d3","\u0044e\u0076\u0069c\u0065\u0043\u004d\u0059\u004b\u0020\u0073hal\u006c\u0020\u006f\u006e\u006c\u0079\u0020\u0062\u0065\u0020\u0075\u0073\u0065\u0064\u0020\u0069\u0066\u0020\u0061\u0020\u0064\u0065\u0076\u0069\u0063\u0065\u0020\u0069\u006e\u0064\u0065\u0070\u0065\u006e\u0064\u0065\u006e\u0074\u0020\u0044ef\u0061\u0075\u006c\u0074\u0043\u004d\u0059K\u0020\u0063\u006f\u006c\u006f\u0075\u0072\u0020\u0073\u0070\u0061\u0063\u0065\u0020\u0068\u0061s\u0020\u0062\u0065\u0065\u006e \u0073\u0065\u0074\u0020\u006fr \u0069\u0066\u0020\u0061\u0020\u0044e\u0076\u0069\u0063\u0065\u004e\u002d\u0062\u0061\u0073\u0065\u0064\u0020\u0044\u0065f\u0061\u0075\u006c\u0074\u0043\u004d\u0059\u004b\u0020c\u006f\u006c\u006f\u0075r\u0020\u0073\u0070\u0061\u0063e\u0020\u0068\u0061\u0073\u0020\u0062\u0065\u0065\u006e\u0020\u0073\u0065\u0074\u0020\u0077\u0068\u0065\u006e\u0020\u0074h\u0065\u0020\u0044\u0065\u0076\u0069c\u0065\u0043\u004d\u0059\u004b\u0020c\u006f\u006c\u006fu\u0072\u0020\u0073\u0070\u0061\u0063\u0065\u0020\u0069\u0073\u0020\u0075\u0073\u0065\u0064\u0020\u006f\u0072\u0020t\u0068\u0065\u0020\u0066\u0069l\u0065\u0020\u0068\u0061\u0073\u0020\u0061\u0020\u0050\u0044\u0046\u002f\u0041\u0020\u004f\u0075\u0074p\u0075\u0074\u0049\u006e\u0074\u0065\u006e\u0074\u0020\u0074\u0068\u0061\u0074\u0020\u0063\u006f\u006e\u0074\u0061\u0069\u006e\u0073\u0020\u0061\u0020\u0043\u004d\u0059\u004b\u0020d\u0065\u0073\u0074\u0069\u006e\u0061t\u0069\u006f\u006e\u0020\u0070r\u006f\u0066\u0069\u006c\u0065\u002e"));
_ffgdc =true ;if _fbcba (){return true ;};};};case *_a .PdfColorspaceICCBased :if !_adgbe {_bgfg ,_eceb :=_gcf .ParseHeader (_gfeb .Data );if _eceb !=nil {_g .Log .Debug ("\u0070\u0061\u0072si\u006e\u0067\u0020\u0049\u0043\u0043\u0042\u0061\u0073e\u0064 \u0068e\u0061d\u0065\u0072\u0020\u0066\u0061\u0069\u006c\u0065\u0064\u003a\u0020\u0025\u0076",_eceb );
_gecf =append (_gecf ,func ()ViolatedRule {return _bb ("\u0036.\u0032\u002e\u0034\u002e\u0032\u002d1","\u0054\u0068e\u0020\u0070\u0072\u006f\u0066\u0069\u006c\u0065\u0020\u0074\u0068\u0061\u0074\u0020\u0066o\u0072\u006d\u0073\u0020\u0074\u0068\u0065\u0020\u0073\u0074r\u0065\u0061\u006d o\u0066\u0020\u0061\u006e\u0020\u0049C\u0043\u0042\u0061\u0073\u0065\u0064\u0020\u0063\u006fl\u006f\u0075\u0072\u0020\u0073p\u0061\u0063\u0065\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0063\u006f\u006e\u0066\u006f\u0072\u006d\u0020\u0074o\u0020\u0049\u0043\u0043.\u0031\u003a\u0031\u0039\u0039\u0038-\u0030\u0039,\u0020\u0049\u0043\u0043\u002e\u0031\u003a\u0032\u0030\u0030\u0031\u002d\u00312\u002c\u0020\u0049\u0043\u0043\u002e\u0031\u003a\u0032\u0030\u0030\u0033\u002d\u0030\u0039\u0020\u006f\u0072\u0020I\u0053\u004f\u0020\u0031\u0035\u0030\u0037\u0036\u002d\u0031\u002e");
}());_adgbe =true ;if _fbcba (){return true ;};};if !_adgbe {var _gdgf ,_fadgf bool ;switch _bgfg .DeviceClass {case _gcf .DeviceClassPRTR ,_gcf .DeviceClassMNTR ,_gcf .DeviceClassSCNR ,_gcf .DeviceClassSPAC :default:_gdgf =true ;};switch _bgfg .ColorSpace {case _gcf .ColorSpaceRGB ,_gcf .ColorSpaceCMYK ,_gcf .ColorSpaceGRAY ,_gcf .ColorSpaceLAB :default:_fadgf =true ;
};if _gdgf ||_fadgf {_gecf =append (_gecf ,_bb ("\u0036.\u0032\u002e\u0034\u002e\u0032\u002d1","\u0054\u0068e\u0020\u0070\u0072\u006f\u0066\u0069\u006c\u0065\u0020\u0074\u0068\u0061\u0074\u0020\u0066o\u0072\u006d\u0073\u0020\u0074\u0068\u0065\u0020\u0073\u0074r\u0065\u0061\u006d o\u0066\u0020\u0061\u006e\u0020\u0049C\u0043\u0042\u0061\u0073\u0065\u0064\u0020\u0063\u006fl\u006f\u0075\u0072\u0020\u0073p\u0061\u0063\u0065\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0063\u006f\u006e\u0066\u006f\u0072\u006d\u0020\u0074o\u0020\u0049\u0043\u0043.\u0031\u003a\u0031\u0039\u0039\u0038-\u0030\u0039,\u0020\u0049\u0043\u0043\u002e\u0031\u003a\u0032\u0030\u0030\u0031\u002d\u00312\u002c\u0020\u0049\u0043\u0043\u002e\u0031\u003a\u0032\u0030\u0030\u0033\u002d\u0030\u0039\u0020\u006f\u0072\u0020I\u0053\u004f\u0020\u0031\u0035\u0030\u0037\u0036\u002d\u0031\u002e"));
_adgbe =true ;if _fbcba (){return true ;};};};};if _gfeb .Alternate !=nil {return _cdab (_gfeb .Alternate );};};return false ;};for _ ,_dffd :=range _fbdfd .GetObjectNums (){_ccecf ,_ffdeg :=_fbdfd .GetIndirectObjectByNumber (_dffd );if _ffdeg !=nil {continue ;
};_bcgeb ,_abda :=_ag .GetStream (_ccecf );if !_abda {continue ;};_abged ,_abda :=_ag .GetName (_bcgeb .Get ("\u0054\u0079\u0070\u0065"));if !_abda ||_abged .String ()!="\u0058O\u0062\u006a\u0065\u0063\u0074"{continue ;};_efdf ,_abda :=_ag .GetName (_bcgeb .Get ("\u0053u\u0062\u0074\u0079\u0070\u0065"));
if !_abda {continue ;};_decd [_bcgeb ]=struct{}{};switch _efdf .String (){case "\u0049\u006d\u0061g\u0065":_ebag ,_dddbc :=_a .NewXObjectImageFromStream (_bcgeb );if _dddbc !=nil {continue ;};_decd [_bcgeb ]=struct{}{};if _cdab (_ebag .ColorSpace ){return _gecf ;
};case "\u0046\u006f\u0072\u006d":_dgec ,_bfbeg :=_ag .GetDict (_bcgeb .Get ("\u0047\u0072\u006fu\u0070"));if !_bfbeg {continue ;};_bgagg :=_dgec .Get ("\u0043\u0053");if _bgagg ==nil {continue ;};_bfea ,_dbedb :=_a .NewPdfColorspaceFromPdfObject (_bgagg );
if _dbedb !=nil {continue ;};if _cdab (_bfea ){return _gecf ;};};};for _ ,_dcfc :=range _fbdfd .PageList {_feeb ,_ffef :=_dcfc .GetContentStreams ();if _ffef !=nil {continue ;};for _ ,_afge :=range _feeb {_geefc ,_eaae :=_fbd .NewContentStreamParser (_afge ).Parse ();
if _eaae !=nil {continue ;};for _ ,_gcecb :=range *_geefc {if len (_gcecb .Params )> 1{continue ;};switch _gcecb .Operand {case "\u0042\u0049":_ggfg ,_fcdd :=_gcecb .Params [0].(*_fbd .ContentStreamInlineImage );if !_fcdd {continue ;};_fdad ,_fdfc :=_ggfg .GetColorSpace (_dcfc .Resources );
if _fdfc !=nil {continue ;};if _cdab (_fdad ){return _gecf ;};case "\u0044\u006f":_cgbf ,_gddb :=_ag .GetName (_gcecb .Params [0]);if !_gddb {continue ;};_gcdga ,_cggf :=_dcfc .Resources .GetXObjectByName (*_cgbf );if _ ,_aede :=_decd [_gcdga ];_aede {continue ;
};switch _cggf {case _a .XObjectTypeImage :_dedab ,_gefaa :=_a .NewXObjectImageFromStream (_gcdga );if _gefaa !=nil {continue ;};_decd [_gcdga ]=struct{}{};if _cdab (_dedab .ColorSpace ){return _gecf ;};case _a .XObjectTypeForm :_eeeb ,_adbf :=_ag .GetDict (_gcdga .Get ("\u0047\u0072\u006fu\u0070"));
if !_adbf {continue ;};_fbcbab ,_adbf :=_ag .GetName (_eeeb .Get ("\u0043\u0053"));if !_adbf {continue ;};_egdae ,_cfaf :=_a .NewPdfColorspaceFromPdfObject (_fbcbab );if _cfaf !=nil {continue ;};_decd [_gcdga ]=struct{}{};if _cdab (_egdae ){return _gecf ;
};};};};};};return _gecf ;};func _fedab (_gadf *_a .CompliancePdfReader )(_ceda ViolatedRule ){_cgdg ,_gdgec :=_gfcb (_gadf );if !_gdgec {return _eag ;};if _cgdg .Get ("\u0041\u0041")!=nil {return _bb ("\u0036.\u0035\u002e\u0032\u002d\u0031","\u0054h\u0065\u0020\u0064\u006fc\u0075m\u0065\u006e\u0074\u0020\u0063\u0061\u0074\u0061\u006co\u0067\u0020\u0073\u0068\u0061\u006c\u006c\u0020n\u006f\u0074\u0020\u0069\u006e\u0063\u006c\u0075\u0064\u0065\u0020a\u006e\u0020\u0041\u0041\u0020\u0065\u006e\u0074\u0072\u0079 \u0066\u006f\u0072\u0020\u0061\u006e\u0020\u0061\u0064\u0064\u0069\u0074\u0069\u006f\u006e\u0061\u006c\u002d\u0061c\u0074\u0069\u006f\u006e\u0073\u0020\u0064\u0069\u0063\u0074\u0069\u006fn\u0061r\u0079\u002e");
};return _eag ;};func _adgba (_ffdee *_a .PdfFont ,_caefa *_ag .PdfObjectDictionary ,_befg bool )ViolatedRule {const (_efad ="\u0036.\u0033\u002e\u0034\u002d\u0031";_becc ="\u0054\u0068\u0065\u0020\u0066\u006f\u006et\u0020\u0070\u0072\u006f\u0067\u0072\u0061\u006d\u0073\u0020\u0066\u006f\u0072\u0020\u0061\u006c\u006c\u0020\u0066\u006f\u006e\u0074\u0073\u0020\u0075\u0073\u0065\u0064\u0020\u0077\u0069\u0074\u0068\u0069\u006e \u0061\u0020\u0063\u006f\u006e\u0066\u006f\u0072\u006d\u0069\u006e\u0067\u0020\u0066\u0069l\u0065\u0020s\u0068\u0061\u006cl\u0020\u0062\u0065\u0020\u0065\u006d\u0062\u0065\u0064\u0064\u0065\u0064\u0020\u0077\u0069\u0074\u0068i\u006e\u0020\u0074h\u0061\u0074\u0020\u0066\u0069\u006ce\u002c\u0020a\u0073\u0020\u0064\u0065\u0066\u0069\u006e\u0065\u0064\u0020\u0069\u006e\u0020\u0050\u0044\u0046\u0020\u0052e\u0066\u0065\u0072\u0065\u006e\u0063\u0065 \u0035\u002e\u0038\u002c\u0020\u0065\u0078\u0063\u0065\u0070\u0074\u0020\u0077h\u0065\u006e\u0020\u0074\u0068\u0065 \u0066\u006f\u006e\u0074\u0073\u0020\u0061\u0072\u0065\u0020\u0075\u0073\u0065\u0064\u0020\u0065\u0078\u0063\u006cu\u0073i\u0076\u0065\u006c\u0079\u0020\u0077\u0069t\u0068\u0020\u0074\u0065\u0078\u0074\u0020\u0072e\u006ed\u0065\u0072\u0069\u006e\u0067\u0020\u006d\u006f\u0064\u0065\u0020\u0033\u002e";
);if _befg {return _eag ;};_ecbfc :=_ffdee .FontDescriptor ();var _ecbfa string ;if _cag ,_adbc :=_ag .GetName (_caefa .Get ("\u0053u\u0062\u0074\u0079\u0070\u0065"));_adbc {_ecbfa =_cag .String ();};switch _ecbfa {case "\u0054\u0079\u0070e\u0031":if _ecbfc .FontFile ==nil {return _bb (_efad ,_becc );
};case "\u0054\u0072\u0075\u0065\u0054\u0079\u0070\u0065":if _ecbfc .FontFile2 ==nil {return _bb (_efad ,_becc );};case "\u0054\u0079\u0070e\u0030","\u0054\u0079\u0070e\u0033":default:if _ecbfc .FontFile3 ==nil {return _bb (_efad ,_becc );};};return _eag ;
};func (_gbb *documentImages )hasOnlyDeviceGray ()bool {return _gbb ._ac &&!_gbb ._fc &&!_gbb ._fgb };func _dc ()standardType {return standardType {_dg :3,_db :"\u0041"}};func _aggec (_ggbe *_a .CompliancePdfReader )(*_a .PdfOutputIntent ,bool ){_aage ,_eccd :=_cgaba (_ggbe );
if !_eccd {return nil ,false ;};_dafb ,_bgfdf :=_a .NewPdfOutputIntentFromPdfObject (_aage );if _bgfdf !=nil {return nil ,false ;};return _dafb ,true ;};func _bfdb (_gbcg *_a .PdfFont ,_bffce *_ag .PdfObjectDictionary )ViolatedRule {const (_afgcd ="\u0036.\u0033\u002e\u0037\u002d\u0031";
_abfe ="\u0041\u006cl \u006e\u006f\u006e\u002d\u0073\u0079\u006db\u006f\u006c\u0069\u0063\u0020\u0054\u0072\u0075\u0065\u0054\u0079\u0070\u0065\u0020\u0066o\u006e\u0074s\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0068\u0061\u0076\u0065\u0020e\u0069\u0074h\u0065\u0072\u0020\u004d\u0061\u0063\u0052\u006f\u006d\u0061\u006e\u0045\u006e\u0063\u006fd\u0069\u006e\u0067\u0020\u006f\u0072\u0020\u0057\u0069\u006e\u0041\u006e\u0073i\u0045n\u0063\u006f\u0064\u0069n\u0067\u0020\u0061\u0073\u0020\u0074\u0068\u0065\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u0066o\u0072\u0020t\u0068\u0065 \u0045n\u0063\u006f\u0064\u0069\u006e\u0067\u0020\u006b\u0065\u0079 \u0069\u006e\u0020t\u0068e\u0020\u0046o\u006e\u0074\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u0020\u006f\u0072\u0020\u0061\u0073\u0020\u0074\u0068\u0065\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u0066\u006f\u0072 \u0074\u0068\u0065\u0020\u0042\u0061\u0073\u0065\u0045\u006e\u0063\u006fd\u0069\u006e\u0067\u0020\u006b\u0065\u0079\u0020\u0069\u006e\u0020\u0074\u0068\u0065 \u0064i\u0063\u0074i\u006fn\u0061\u0072\u0079\u0020\u0077\u0068\u0069\u0063\u0068\u0020\u0069s\u0020\u0074\u0068\u0065\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u006ff\u0020\u0074\u0068e\u0020\u0045\u006e\u0063\u006f\u0064\u0069\u006e\u0067\u0020\u006be\u0079\u0020\u0069\u006e\u0020\u0074\u0068\u0065\u0020\u0046\u006f\u006e\u0074 \u0064\u0069\u0063\u0074i\u006f\u006e\u0061\u0072\u0079\u002e\u0020\u0049\u006e\u0020\u0061\u0064\u0064\u0069\u0074\u0069\u006f\u006e, \u006eo\u0020n\u006f\u006e\u002d\u0073\u0079\u006d\u0062\u006f\u006c\u0069\u0063\u0020\u0054\u0072\u0075\u0065\u0054\u0079p\u0065 \u0066\u006f\u006e\u0074\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0064\u0065\u0066\u0069\u006e\u0065\u0020\u0061\u0020\u0044\u0069\u0066\u0066e\u0072\u0065\u006e\u0063\u0065\u0073\u0020a\u0072\u0072\u0061\u0079\u0020\u0075n\u006c\u0065s\u0073\u0020\u0061\u006c\u006c\u0020\u006f\u0066\u0020\u0074h\u0065\u0020\u0067\u006c\u0079\u0070\u0068\u0020\u006e\u0061\u006d\u0065\u0073 \u0069\u006e\u0020\u0074\u0068\u0065\u0020\u0044\u0069f\u0066\u0065\u0072\u0065\u006ec\u0065\u0073\u0020a\u0072\u0072\u0061\u0079\u0020\u0061\u0072\u0065\u0020\u006c\u0069\u0073\u0074\u0065\u0064 \u0069\u006e \u0074\u0068\u0065\u0020\u0041\u0064\u006f\u0062\u0065 G\u006c\u0079\u0070\u0068\u0020\u004c\u0069\u0073t\u0020\u0061\u006e\u0064\u0020\u0074h\u0065\u0020\u0065\u006d\u0062\u0065\u0064\u0064\u0065\u0064\u0020\u0066o\u006e\u0074\u0020\u0070\u0072\u006f\u0067\u0072a\u006d\u0020\u0063\u006f\u006e\u0074\u0061\u0069n\u0073\u0020\u0061\u0074\u0020\u006c\u0065\u0061\u0073t\u0020\u0074\u0068\u0065\u0020\u004d\u0069\u0063\u0072o\u0073o\u0066\u0074\u0020\u0055\u006e\u0069\u0063\u006f\u0064\u0065\u0020\u0028\u0033\u002c\u0031 \u2013 P\u006c\u0061\u0074\u0066\u006f\u0072\u006d\u0020I\u0044\u003d\u0033\u002c\u0020\u0045\u006e\u0063\u006f\u0064\u0069\u006e\u0067 I\u0044\u003d\u0031\u0029\u0020\u0065\u006e\u0063\u006f\u0064i\u006e\u0067 \u0069\u006e\u0020t\u0068\u0065\u0020'\u0063\u006d\u0061\u0070\u0027\u0020\u0074\u0061\u0062\u006c\u0065\u002e";
);var _cffdd string ;if _cac ,_dedd :=_ag .GetName (_bffce .Get ("\u0053u\u0062\u0074\u0079\u0070\u0065"));_dedd {_cffdd =_cac .String ();};if _cffdd !="\u0054\u0072\u0075\u0065\u0054\u0079\u0070\u0065"{return _eag ;};_egea :=_gbcg .FontDescriptor ();_dabce ,_afba :=_ag .GetIntVal (_egea .Flags );
if !_afba {_g .Log .Debug ("\u0066\u006c\u0061\u0067\u0073 \u006e\u006f\u0074\u0020\u0064\u0065\u0066\u0069\u006e\u0065\u0064\u0020\u0066o\u0072\u0020\u0074\u0068\u0065\u0020\u0066\u006f\u006e\u0074\u0020\u0064\u0065\u0073\u0063\u0072\u0069\u0070\u0074\u006f\u0072");
return _bb (_afgcd ,_abfe );};_efbbf :=(uint32 (_dabce )>>3)!=0;if _efbbf {return _eag ;};_feda ,_afba :=_ag .GetName (_bffce .Get ("\u0045\u006e\u0063\u006f\u0064\u0069\u006e\u0067"));if !_afba {return _bb (_afgcd ,_abfe );};switch _feda .String (){case "\u004d\u0061c\u0052\u006f\u006da\u006e\u0045\u006e\u0063\u006f\u0064\u0069\u006e\u0067","\u0057i\u006eA\u006e\u0073\u0069\u0045\u006e\u0063\u006f\u0064\u0069\u006e\u0067":return _eag ;
default:return _bb (_afgcd ,_abfe );};};func _dge (_def *_gc .Document ){if _def .ID [0]!=""&&_def .ID [1]!=""{return ;};_def .UseHashBasedID =true ;};func _adgdd (_dgfa *_a .CompliancePdfReader )ViolatedRule {for _ ,_fbcg :=range _dgfa .PageList {_dgaa ,_fgec :=_fbcg .GetContentStreams ();
if _fgec !=nil {continue ;};for _ ,_bcbff :=range _dgaa {_ecfc :=_fbd .NewContentStreamParser (_bcbff );_ ,_fgec =_ecfc .Parse ();if _fgec !=nil {return _bb ("\u0036\u002e\u0032\u002e\u0031\u0030\u002d\u0031","\u0041\u0020\u0063onten\u0074\u0020\u0073\u0074\u0072\u0065\u0061\u006d\u0020\u0073\u0068\u0061\u006c\u006c n\u006f\u0074\u0020c\u006f\u006e\u0074\u0061\u0069\u006e\u0020\u0061\u006e\u0079 \u006f\u0070\u0065\u0072\u0061\u0074\u006f\u0072\u0073\u0020\u006e\u006ft\u0020\u0064\u0065\u0066\u0069\u006e\u0065\u0064\u0020\u0069\u006e\u0020\u0050\u0044\u0046\u0020\u0052\u0065f\u0065\u0072\u0065\u006e\u0063\u0065\u0020\u0065\u0076\u0065\u006e\u0020\u0069\u0066\u0020s\u0075\u0063\u0068\u0020\u006f\u0070\u0065r\u0061\u0074\u006f\u0072\u0073\u0020\u0061\u0072\u0065\u0020\u0062\u0072\u0061\u0063\u006b\u0065\u0074\u0065\u0064\u0020\u0062\u0079\u0020\u0074\u0068\u0065\u0020\u0042\u0058\u002f\u0045\u0058\u0020\u0063\u006f\u006d\u0070\u0061\u0074\u0069\u0062i\u006c\u0069\u0074\u0079\u0020\u006f\u0070\u0065\u0072\u0061\u0074\u006f\u0072\u0073\u002e");
};};};return _eag ;};func _eegf (_feed *_gc .Document ,_gdcc []pageColorspaceOptimizeFunc ,_ccfd []documentColorspaceOptimizeFunc )error {_aee ,_feg :=_feed .GetPages ();if !_feg {return nil ;};var _deg []*_gc .Image ;for _aec ,_bed :=range _aee {_egdd ,_cecg :=_bed .FindXObjectImages ();
if _cecg !=nil {return _cecg ;};for _ ,_gced :=range _gdcc {if _cecg =_gced (_feed ,&_aee [_aec ],_egdd );_cecg !=nil {return _cecg ;};};_deg =append (_deg ,_egdd ...);};for _ ,_acaa :=range _ccfd {if _efeb :=_acaa (_feed ,_deg );_efeb !=nil {return _efeb ;
};};return nil ;};func (_dec *documentImages )hasOnlyDeviceCMYK ()bool {return _dec ._fgb &&!_dec ._fc &&!_dec ._ac };func _aea ()standardType {return standardType {_dg :2,_db :"\u0041"}};func _cgfea (_dgff *_a .CompliancePdfReader )ViolatedRule {for _ ,_gcaf :=range _dgff .GetObjectNums (){_ceeg ,_bbbbb :=_dgff .GetIndirectObjectByNumber (_gcaf );
if _bbbbb !=nil {continue ;};_dgad ,_gggea :=_ag .GetStream (_ceeg );if !_gggea {continue ;};_eaea ,_gggea :=_ag .GetName (_dgad .Get ("\u0054\u0079\u0070\u0065"));if !_gggea {continue ;};if *_eaea !="\u0058O\u0062\u006a\u0065\u0063\u0074"{continue ;};
if _dgad .Get ("\u0053\u004d\u0061s\u006b")!=nil {return _bb ("\u0036\u002e\u0034-\u0032","\u0041\u006e\u0020\u0058\u004f\u0062\u006a\u0065\u0063\u0074\u0020\u0064\u0069\u0063\u0074i\u006f\u006e\u0061\u0072\u0079\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u006eo\u0074\u0020\u0063\u006f\u006e\u0074\u0061\u0069\u006e\u0020\u0074\u0068e \u0053\u004d\u0061\u0073\u006b\u0020\u006b\u0065\u0079\u002e");
};};return _eag ;};func _efcf (_cceb *_a .CompliancePdfReader )(_eaagf []ViolatedRule ){var _ceba ,_bcab ,_eeea bool ;if _cceb .ParserMetadata ().HasNonConformantStream (){_eaagf =[]ViolatedRule {_bb ("\u0036.\u0031\u002e\u0037\u002d\u0032","T\u0068\u0065\u0020\u0073\u0074\u0072\u0065\u0061\u006d\u0020\u006b\u0065\u0079\u0077\u006fr\u0064\u0020\u0073\u0068\u0061\u006c\u006c \u0062\u0065\u0020f\u006f\u006cl\u006fw\u0065\u0064\u0020e\u0069\u0074h\u0065\u0072\u0020\u0062\u0079\u0020\u0061 \u0043\u0041\u0052\u0052I\u0041\u0047\u0045\u0020\u0052E\u0054\u0055\u0052\u004e\u0020\u00280\u0044\u0068\u0029\u0020\u0061\u006e\u0064\u0020\u004c\u0049\u004e\u0045\u0020F\u0045\u0045\u0044\u0020\u0028\u0030\u0041\u0068\u0029\u0020\u0063\u0068\u0061\u0072\u0061\u0063\u0074\u0065\u0072\u0020\u0073\u0065\u0071\u0075\u0065\u006e\u0063\u0065\u0020o\u0072\u0020\u0062\u0079\u0020\u0061 \u0073\u0069ng\u006c\u0065\u0020\u004cIN\u0045 \u0046\u0045\u0045\u0044 \u0063\u0068\u0061r\u0061\u0063\u0074\u0065\u0072\u002e\u0020T\u0068\u0065\u0020e\u006e\u0064\u0073\u0074r\u0065\u0061\u006d\u0020\u006b\u0065\u0079\u0077\u006fr\u0064\u0020\u0073\u0068\u0061\u006c\u006c \u0062e\u0020p\u0072\u0065\u0063\u0065\u0064\u0065\u0064\u0020\u0062\u0079\u0020\u0061n\u0020\u0045\u004f\u004c \u006d\u0061\u0072\u006b\u0065\u0072\u002e")};
};for _ ,_cbafc :=range _cceb .GetObjectNums (){_adaa ,_ :=_cceb .GetIndirectObjectByNumber (_cbafc );if _adaa ==nil {continue ;};_ggcd ,_fdcb :=_ag .GetStream (_adaa );if !_fdcb {continue ;};if !_ceba {_aefdg :=_ggcd .Get ("\u004c\u0065\u006e\u0067\u0074\u0068");
if _aefdg ==nil {_eaagf =append (_eaagf ,_bb ("\u0036.\u0031\u002e\u0037\u002d\u0031","\u006e\u006f\u0020'\u004c\u0065\u006e\u0067\u0074\u0068\u0027\u0020\u006b\u0065\u0079\u0020\u0066\u006f\u0075\u006e\u0064\u0020\u0069\u006e\u0020\u0074\u0068\u0065\u0020\u0073\u0074\u0072\u0065a\u006d\u0020\u006f\u0062\u006a\u0065\u0063\u0074"));
_ceba =true ;}else {_ggecb ,_fbbf :=_ag .GetIntVal (_aefdg );if !_fbbf {_eaagf =append (_eaagf ,_bb ("\u0036.\u0031\u002e\u0037\u002d\u0031","s\u0074\u0072\u0065\u0061\u006d\u0020\u0027\u004c\u0065\u006e\u0067\u0074\u0068\u0027\u0020\u006b\u0065\u0079 \u0073\u0068\u006f\u0075\u006c\u0064\u0020\u0062\u0065\u0020an\u0020\u0069\u006et\u0065g\u0065\u0072"));
_ceba =true ;}else {if len (_ggcd .Stream )!=_ggecb {_eaagf =append (_eaagf ,_bb ("\u0036.\u0031\u002e\u0037\u002d\u0031","\u0073\u0074\u0072\u0065\u0061\u006d\u0020\u006f\u0062\u006a\u0065\u0063\u0074\u0020\u006c\u0065\u006e\u0067th\u0020v\u0061\u006c\u0075\u0065\u0020\u0073\u0068\u006f\u0075\u006c\u0064\u0020m\u0061\u0074\u0063\u0068\u0020\u0074\u0068\u0065\u0020\u0073\u0069\u007a\u0065\u0020\u006f\u0066\u0020t\u0068\u0065\u0020\u0073\u0074\u0072\u0065\u0061\u006d"));
_ceba =true ;};};};};if !_bcab {if _ggcd .Get ("\u0046")!=nil {_bcab =true ;_eaagf =append (_eaagf ,_bb ("\u0036.\u0031\u002e\u0037\u002d\u0033","\u0073\u0074\u0072\u0065\u0061\u006d\u0020\u006f\u0062j\u0065\u0063\u0074\u0020\u0073\u0068\u006f\u0075\u006c\u0064\u0020\u006e\u006f\u0074\u0020\u0063\u006f\u006e\u0074\u0061\u0069\u006e\u0020'\u0046\u0027,\u0020\u0027F\u0046\u0069\u006c\u0074\u0065\u0072\u0027\u002c\u0020\u006f\u0072\u0020\u0027FD\u0065\u0063\u006f\u0064\u0065\u0050\u0061\u0072\u0061m\u0073\u0027\u0020\u006b\u0065\u0079"));
};if _ggcd .Get ("\u0046F\u0069\u006c\u0074\u0065\u0072")!=nil &&!_bcab {_bcab =true ;_eaagf =append (_eaagf ,_bb ("\u0036.\u0031\u002e\u0037\u002d\u0033","\u0073\u0074\u0072\u0065\u0061\u006d\u0020\u006f\u0062j\u0065\u0063\u0074\u0020\u0073\u0068\u006f\u0075\u006c\u0064\u0020\u006e\u006f\u0074\u0020\u0063\u006f\u006e\u0074\u0061\u0069\u006e\u0020'\u0046\u0027,\u0020\u0027F\u0046\u0069\u006c\u0074\u0065\u0072\u0027\u002c\u0020\u006f\u0072\u0020\u0027FD\u0065\u0063\u006f\u0064\u0065\u0050\u0061\u0072\u0061m\u0073\u0027\u0020\u006b\u0065\u0079"));
continue ;};if _ggcd .Get ("\u0046\u0044\u0065\u0063\u006f\u0064\u0065\u0050\u0061\u0072\u0061\u006d\u0073")!=nil &&!_bcab {_bcab =true ;_eaagf =append (_eaagf ,_bb ("\u0036.\u0031\u002e\u0037\u002d\u0033","\u0073\u0074\u0072\u0065\u0061\u006d\u0020\u006f\u0062j\u0065\u0063\u0074\u0020\u0073\u0068\u006f\u0075\u006c\u0064\u0020\u006e\u006f\u0074\u0020\u0063\u006f\u006e\u0074\u0061\u0069\u006e\u0020'\u0046\u0027,\u0020\u0027F\u0046\u0069\u006c\u0074\u0065\u0072\u0027\u002c\u0020\u006f\u0072\u0020\u0027FD\u0065\u0063\u006f\u0064\u0065\u0050\u0061\u0072\u0061m\u0073\u0027\u0020\u006b\u0065\u0079"));
continue ;};};if !_eeea {_acdeg ,_daefb :=_ag .GetName (_ag .TraceToDirectObject (_ggcd .Get ("\u0046\u0069\u006c\u0074\u0065\u0072")));if !_daefb {continue ;};if *_acdeg ==_ag .StreamEncodingFilterNameLZW {_eeea =true ;_eaagf =append (_eaagf ,_bb ("\u0036.\u0031\u002e\u0037\u002d\u0034","\u0054h\u0065\u0020L\u005a\u0057\u0044\u0065c\u006f\u0064\u0065 \u0066\u0069\u006c\u0074\u0065\u0072\u0020\u0073\u0068al\u006c\u0020\u006eo\u0074\u0020b\u0065\u0020\u0070\u0065\u0072\u006di\u0074\u0074e\u0064\u002e"));
};};};return _eaagf ;};func _dgc (_cfa *_gc .Document ,_dcf int ){if _cfa .Version .Major ==0{_cfa .Version .Major =1;};if _cfa .Version .Minor < _dcf {_cfa .Version .Minor =_dcf ;};};

// NewProfile2B creates a new Profile2B with the given options.
func NewProfile2B (options *Profile2Options )*Profile2B {if options ==nil {options =DefaultProfile2Options ();};_ecbf (options );return &Profile2B {profile2 {_ece :*options ,_ebecg :_bc ()}};};func _adab (_gfed *_ag .PdfObjectDictionary ,_befd map[*_ag .PdfObjectStream ][]byte ,_egae map[*_ag .PdfObjectStream ]*_ab .CMap )ViolatedRule {const (_cgcfbb ="\u0036\u002e\u0032\u002e\u0031\u0031\u002e\u0033\u002d\u0034";
_eaagfd ="\u0046\u006f\u0072\u0020\u0074\u0068\u006fs\u0065\u0020\u0043\u004d\u0061\u0070\u0073\u0020\u0074\u0068\u0061\u0074\u0020\u0061\u0072e\u0020\u0065m\u0062\u0065\u0064de\u0064\u002c\u0020\u0074\u0068\u0065\u0020\u0069\u006et\u0065\u0067\u0065\u0072 \u0076\u0061\u006c\u0075\u0065\u0020\u006f\u0066\u0020\u0074\u0068\u0065\u0020\u0057\u004d\u006f\u0064\u0065\u0020\u0065\u006e\u0074r\u0079\u0020i\u006e t\u0068\u0065\u0020CM\u0061\u0070\u0020\u0064\u0069\u0063\u0074\u0069o\u006ea\u0072\u0079\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0062\u0065\u0020\u0069\u0064\u0065\u006e\u0074\u0069\u0063\u0061\u006c\u0020\u0074\u006f \u0074h\u0065\u0020\u0057\u004d\u006f\u0064e\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u0069\u006e\u0020\u0074h\u0065\u0020\u0065\u006d\u0062\u0065\u0064\u0064ed\u0020\u0043\u004d\u0061\u0070\u0020\u0073\u0074\u0072\u0065\u0061\u006d\u002e";
);var _gafd string ;if _fddaf ,_ecegf :=_ag .GetName (_gfed .Get ("\u0053u\u0062\u0074\u0079\u0070\u0065"));_ecegf {_gafd =_fddaf .String ();};if _gafd !="\u0054\u0079\u0070e\u0030"{return _eag ;};_egge :=_gfed .Get ("\u0045\u006e\u0063\u006f\u0064\u0069\u006e\u0067");
if _ ,_fgffb :=_ag .GetName (_egge );_fgffb {return _eag ;};_eggb ,_gbfbg :=_ag .GetStream (_egge );if !_gbfbg {return _bb (_cgcfbb ,_eaagfd );};_gffbb ,_cggdd :=_gfbb (_eggb ,_befd ,_egae );if _cggdd !=nil {return _bb (_cgcfbb ,_eaagfd );};_fage ,_gdeacc :=_ag .GetIntVal (_eggb .Get ("\u0057\u004d\u006fd\u0065"));
_adaed ,_cafac :=_gffbb .WMode ();if _gdeacc &&_cafac {if _adaed !=_fage {return _bb (_cgcfbb ,_eaagfd );};};if (_gdeacc &&!_cafac )||(!_gdeacc &&_cafac ){return _bb (_cgcfbb ,_eaagfd );};return _eag ;};

// Profile3Options are the options that changes the way how optimizer may try to adapt document into PDF/A standard.
type Profile3Options struct{

// CMYKDefaultColorSpace is an option that refers PDF/A
CMYKDefaultColorSpace bool ;

// Now is a function that returns current time.
Now func ()_e .Time ;

// Xmp is the xmp options information.
Xmp XmpOptions ;};

// NewProfile3A creates a new Profile3A with given options.
func NewProfile3A (options *Profile3Options )*Profile3A {if options ==nil {options =DefaultProfile3Options ();};_abaf (options );return &Profile3A {profile3 {_gae :*options ,_gfdf :_dc ()}};};func _abe ()standardType {return standardType {_dg :2,_db :"\u0055"}};
func _ggga (_ggdb *_gc .Document ,_cbaf *_gc .Page ,_adc []*_gc .Image )error {for _ ,_gceda :=range _adc {if _gceda .SMask ==nil {continue ;};_afb ,_bedc :=_a .NewXObjectImageFromStream (_gceda .Stream );if _bedc !=nil {return _bedc ;};_bae ,_bedc :=_afb .ToImage ();
if _bedc !=nil {return _bedc ;};_ddfg ,_bedc :=_bae .ToGoImage ();if _bedc !=nil {return _bedc ;};_gdda ,_bedc :=_fe .RGBAConverter .Convert (_ddfg );if _bedc !=nil {return _bedc ;};_bdgf :=_gdda .Base ();_cgc :=&_a .Image {Width :int64 (_bdgf .Width ),Height :int64 (_bdgf .Height ),BitsPerComponent :int64 (_bdgf .BitsPerComponent ),ColorComponents :_bdgf .ColorComponents ,Data :_bdgf .Data };
_cgc .SetDecode (_bdgf .Decode );_cgc .SetAlpha (_bdgf .Alpha );if _bedc =_afb .SetImage (_cgc ,nil );_bedc !=nil {return _bedc ;};_afb .SMask =_ag .MakeNull ();var _gagd _ag .PdfObject ;_fbfb :=-1;for _fbfb ,_gagd =range _ggdb .Objects {if _gagd ==_gceda .SMask .Stream {break ;
};};if _fbfb !=-1{_ggdb .Objects =append (_ggdb .Objects [:_fbfb ],_ggdb .Objects [_fbfb +1:]...);};_gceda .SMask =nil ;_afb .ToPdfObject ();};return nil ;};

// ValidateStandard checks if provided input CompliancePdfReader matches rules that conforms PDF/A-1 standard.
func (_aeecg *profile1 )ValidateStandard (r *_a .CompliancePdfReader )error {_gdcdg :=VerificationError {ConformanceLevel :_aeecg ._acab ._dg ,ConformanceVariant :_aeecg ._acab ._db };if _agad :=_bdeg (r );_agad !=_eag {_gdcdg .ViolatedRules =append (_gdcdg .ViolatedRules ,_agad );
};if _bagd :=_gddfg (r );_bagd !=_eag {_gdcdg .ViolatedRules =append (_gdcdg .ViolatedRules ,_bagd );};if _dada :=_dgf (r );_dada !=_eag {_gdcdg .ViolatedRules =append (_gdcdg .ViolatedRules ,_dada );};if _cfad :=_ebcf (r );_cfad !=_eag {_gdcdg .ViolatedRules =append (_gdcdg .ViolatedRules ,_cfad );
};if _ccde :=_dfbe (r );_ccde !=_eag {_gdcdg .ViolatedRules =append (_gdcdg .ViolatedRules ,_ccde );};if _eccb :=_fagg (r );len (_eccb )!=0{_gdcdg .ViolatedRules =append (_gdcdg .ViolatedRules ,_eccb ...);};if _edde :=_fdfe (r );_edde !=_eag {_gdcdg .ViolatedRules =append (_gdcdg .ViolatedRules ,_edde );
};if _fbg :=_dcfb (r );len (_fbg )!=0{_gdcdg .ViolatedRules =append (_gdcdg .ViolatedRules ,_fbg ...);};if _bfec :=_acee (r );len (_bfec )!=0{_gdcdg .ViolatedRules =append (_gdcdg .ViolatedRules ,_bfec ...);};if _gfbe :=_dcbac (r );len (_gfbe )!=0{_gdcdg .ViolatedRules =append (_gdcdg .ViolatedRules ,_gfbe ...);
};if _bgdc :=_dcceg (r );_bgdc !=_eag {_gdcdg .ViolatedRules =append (_gdcdg .ViolatedRules ,_bgdc );};if _cfcf :=_gfde (r );len (_cfcf )!=0{_gdcdg .ViolatedRules =append (_gdcdg .ViolatedRules ,_cfcf ...);};if _fcdge :=_gbcb (r );len (_fcdge )!=0{_gdcdg .ViolatedRules =append (_gdcdg .ViolatedRules ,_fcdge ...);
};if _fbdbc :=_ggb (r );_fbdbc !=_eag {_gdcdg .ViolatedRules =append (_gdcdg .ViolatedRules ,_fbdbc );};if _edfc :=_fdfb (r ,false );len (_edfc )!=0{_gdcdg .ViolatedRules =append (_gdcdg .ViolatedRules ,_edfc ...);};if _fbdc :=_gbegd (r );len (_fbdc )!=0{_gdcdg .ViolatedRules =append (_gdcdg .ViolatedRules ,_fbdc ...);
};if _dccc :=_bgbb (r );_dccc !=_eag {_gdcdg .ViolatedRules =append (_gdcdg .ViolatedRules ,_dccc );};if _dfa :=_fcc (r );_dfa !=_eag {_gdcdg .ViolatedRules =append (_gdcdg .ViolatedRules ,_dfa );};if _gcad :=_bgebb (r );_gcad !=_eag {_gdcdg .ViolatedRules =append (_gdcdg .ViolatedRules ,_gcad );
};if _gcdg :=_ggaf (r );_gcdg !=_eag {_gdcdg .ViolatedRules =append (_gdcdg .ViolatedRules ,_gcdg );};if _fgfed :=_adgdd (r );_fgfed !=_eag {_gdcdg .ViolatedRules =append (_gdcdg .ViolatedRules ,_fgfed );};if _bbdgf :=_cbdec (r );len (_bbdgf )!=0{_gdcdg .ViolatedRules =append (_gdcdg .ViolatedRules ,_bbdgf ...);
};if _adgg :=_eaab (r ,_aeecg ._acab );len (_adgg )!=0{_gdcdg .ViolatedRules =append (_gdcdg .ViolatedRules ,_adgg ...);};if _edgb :=_dfda (r );len (_edgb )!=0{_gdcdg .ViolatedRules =append (_gdcdg .ViolatedRules ,_edgb ...);};if _cdfb :=_cgfea (r );_cdfb !=_eag {_gdcdg .ViolatedRules =append (_gdcdg .ViolatedRules ,_cdfb );
};if _dfdc :=_eaege (r );_dfdc !=_eag {_gdcdg .ViolatedRules =append (_gdcdg .ViolatedRules ,_dfdc );};if _dbd :=_gabcf (r );len (_dbd )!=0{_gdcdg .ViolatedRules =append (_gdcdg .ViolatedRules ,_dbd ...);};if _eeagb :=_dgcgbf (r );len (_eeagb )!=0{_gdcdg .ViolatedRules =append (_gdcdg .ViolatedRules ,_eeagb ...);
};if _dcea :=_dgeb (r );_dcea !=_eag {_gdcdg .ViolatedRules =append (_gdcdg .ViolatedRules ,_dcea );};if _dfdcc :=_dfcb (r );_dfdcc !=_eag {_gdcdg .ViolatedRules =append (_gdcdg .ViolatedRules ,_dfdcc );};if _fbfc :=_abge (r ,_aeecg ._acab ,false );len (_fbfc )!=0{_gdcdg .ViolatedRules =append (_gdcdg .ViolatedRules ,_fbfc ...);
};if _aeecg ._acab ==_fbad (){if _dadg :=_cdbe (r );len (_dadg )!=0{_gdcdg .ViolatedRules =append (_gdcdg .ViolatedRules ,_dadg ...);};};if _fafe :=_ccdcg (r );len (_fafe )!=0{_gdcdg .ViolatedRules =append (_gdcdg .ViolatedRules ,_fafe ...);};if len (_gdcdg .ViolatedRules )> 0{_d .Slice (_gdcdg .ViolatedRules ,func (_facc ,_ccba int )bool {return _gdcdg .ViolatedRules [_facc ].RuleNo < _gdcdg .ViolatedRules [_ccba ].RuleNo ;
});return _gdcdg ;};return nil ;};

// DefaultProfile2Options are the default options for the Profile2.
func DefaultProfile2Options ()*Profile2Options {return &Profile2Options {Now :_e .Now ,Xmp :XmpOptions {MarshalIndent :"\u0009"}};};

// Part gets the PDF/A version level.
func (_gegd *profile1 )Part ()int {return _gegd ._acab ._dg };func _cbgc (_bgfd *_gc .Document ,_eaeg bool )error {_degg ,_dged :=_bgfd .GetPages ();if !_dged {return nil ;};for _ ,_adee :=range _degg {_ggdg ,_ged :=_adee .GetContents ();if !_ged {continue ;
};var _fbdg *_a .PdfPageResources ;_eed ,_ged :=_adee .GetResources ();if _ged {_fbdg ,_ =_a .NewPdfPageResourcesFromDict (_eed );};for _efgf ,_dbba :=range _ggdg {_fdgb ,_edeb :=_dbba .GetData ();if _edeb !=nil {continue ;};_abag :=_fbd .NewContentStreamParser (string (_fdgb ));
_bffg ,_edeb :=_abag .Parse ();if _edeb !=nil {continue ;};_fad ,_edeb :=_gbbe (_fbdg ,_bffg ,_eaeg );if _edeb !=nil {return _edeb ;};if _fad ==nil {continue ;};if _edeb =(&_ggdg [_efgf ]).SetData (_fad );_edeb !=nil {return _edeb ;};};};return nil ;};
func _egeb (_cbdf *_ag .PdfObjectDictionary )ViolatedRule {const (_ebaa ="\u0036.\u0033\u002e\u0033\u002d\u0032";_dffg ="\u0046\u006f\u0072\u0020\u0061\u006c\u006c\u0020\u0054y\u0070\u0065\u0020\u0032\u0020\u0043\u0049\u0044\u0046\u006f\u006e\u0074\u0073\u0020\u0074\u0068\u0061\u0074\u0020\u0061\u0072\u0065\u0020\u0075\u0073\u0065\u0064\u0020f\u006f\u0072 \u0072\u0065\u006e\u0064\u0065\u0072\u0069\u006e\u0067,\u0020\u0074\u0068\u0065\u0020\u0043\u0049\u0044\u0046\u006fn\u0074\u0020\u0064\u0069c\u0074\u0069o\u006e\u0061\u0072\u0079\u0020\u0073\u0068\u0061\u006c\u006c \u0063\u006f\u006e\u0074\u0061i\u006e\u0020\u0061\u0020\u0043\u0049\u0044\u0054\u006f\u0047\u0049D\u004d\u0061\u0070\u0020\u0065\u006e\u0074\u0072\u0079\u0020\u0074\u0068\u0061\u0074\u0020\u0073\u0068\u0061\u006c\u006c \u0062\u0065\u0020a\u0020\u0073\u0074\u0072\u0065\u0061\u006d\u0020\u006d\u0061\u0070\u0070\u0069\u006e\u0067\u0020\u0066\u0072\u006f\u006d\u0020\u0043\u0049\u0044\u0073\u0020\u0074\u006f\u0020\u0067\u006c\u0079\u0070\u0068\u0020\u0069\u006e\u0064\u0069c\u0065\u0073\u0020\u006f\u0072\u0020\u0074\u0068\u0065\u0020\u006e\u0061\u006d\u0065\u0020\u0049d\u0065\u006e\u0074\u0069\u0074\u0079\u002c\u0020\u0061s d\u0065\u0073\u0063\u0072\u0069\u0062\u0065\u0064\u0020\u0069n\u0020P\u0044\u0046\u0020\u0052\u0065\u0066\u0065\u0072e\u006e\u0063\u0065\u0020\u0054a\u0062\u006c\u0065\u0020\u0035\u002e\u00313";
);var _ggfbg string ;if _abcb ,_ccca :=_ag .GetName (_cbdf .Get ("\u0053u\u0062\u0074\u0079\u0070\u0065"));_ccca {_ggfbg =_abcb .String ();};if _ggfbg !="\u0043\u0049\u0044F\u006f\u006e\u0074\u0054\u0079\u0070\u0065\u0032"{return _eag ;};if _cbdf .Get ("C\u0049\u0044\u0054\u006f\u0047\u0049\u0044\u004d\u0061\u0070")==nil {return _bb (_ebaa ,_dffg );
};return _eag ;};func _aggbe (_dccba *_ag .PdfObjectDictionary ,_fcbbaf map[*_ag .PdfObjectStream ][]byte ,_cbdad map[*_ag .PdfObjectStream ]*_ab .CMap )ViolatedRule {const (_gcae ="\u0046\u006f\u0072 \u0061\u006e\u0079\u0020\u0067\u0069\u0076\u0065\u006e\u0020\u0063\u006f\u006d\u0070\u006f\u0073\u0069\u0074\u0065\u0020\u0028\u0054\u0079\u0070\u0065\u0020\u0030\u0029\u0020\u0066\u006f\u006et \u0072\u0065\u0066\u0065\u0072\u0065\u006ec\u0065\u0064 \u0077\u0069\u0074\u0068\u0069\u006e\u0020\u0061\u0020\u0063\u006fn\u0066\u006fr\u006d\u0069\u006e\u0067\u0020\u0066\u0069\u006c\u0065\u002c\u0020\u0074\u0068\u0065\u0020\u0043I\u0044\u0053y\u0073\u0074\u0065\u006d\u0049nf\u006f\u0020\u0065\u006e\u0074\u0072\u0069\u0065\u0073\u0020\u006f\u0066\u0020i\u0074\u0073\u0020\u0043\u0049\u0044\u0046\u006f\u006e\u0074\u0020\u0061\u006e\u0064 \u0043\u004d\u0061\u0070 \u0064\u0069\u0063\u0074i\u006f\u006e\u0061\u0072\u0069\u0065\u0073\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0062\u0065\u0020\u0063\u006f\u006d\u0070\u0061\u0074i\u0062\u006c\u0065\u002e\u0020\u0049\u006e\u0020o\u0074\u0068\u0065\u0072\u0020\u0077\u006f\u0072\u0064\u0073\u002c\u0020\u0074\u0068\u0065\u0020R\u0065\u0067\u0069\u0073\u0074\u0072\u0079\u0020a\u006e\u0064\u0020\u004fr\u0064\u0065\u0072\u0069\u006e\u0067 \u0073\u0074\u0072i\u006e\u0067\u0073\u0020\u006f\u0066\u0020\u0074\u0068\u0065\u0020\u0043\u0049\u0044\u0053\u0079\u0073\u0074\u0065\u006d\u0049\u006e\u0066\u006f\u0020\u0064\u0069\u0063\u0074i\u006f\u006e\u0061\u0072\u0069\u0065\u0073\u0020\u0066\u006f\u0072 \u0074\u0068\u0061\u0074\u0020\u0066o\u006e\u0074\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0062\u0065\u0020\u0069\u0064\u0065\u006e\u0074\u0069\u0063\u0061\u006c\u002c\u0020u\u006el\u0065ss \u0074\u0068\u0065\u0020\u0076a\u006c\u0075\u0065\u0020\u006f\u0066\u0020\u0074\u0068\u0065\u0020\u0045\u006e\u0063\u006f\u0064\u0069\u006eg\u0020\u006b\u0065\u0079\u0020\u0069\u006e\u0020\u0074h\u0065 \u0066\u006f\u006e\u0074\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u0020\u0069\u0073 \u0049\u0064\u0065\u006e\u0074\u0069t\u0079\u002d\u0048\u0020o\u0072\u0020\u0049\u0064\u0065\u006e\u0074\u0069\u0074y\u002dV\u002e";
_fafg ="\u0036.\u0033\u002e\u0033\u002d\u0031";);var _gbgbf string ;if _bfdd ,_dcbedd :=_ag .GetName (_dccba .Get ("\u0053u\u0062\u0074\u0079\u0070\u0065"));_dcbedd {_gbgbf =_bfdd .String ();};if _gbgbf !="\u0054\u0079\u0070e\u0030"{return _eag ;};_dccd :=_dccba .Get ("\u0045\u006e\u0063\u006f\u0064\u0069\u006e\u0067");
if _cbeec ,_gegdb :=_ag .GetName (_dccd );_gegdb {switch _cbeec .String (){case "\u0049\u0064\u0065\u006e\u0074\u0069\u0074\u0079\u002d\u0048","\u0049\u0064\u0065\u006e\u0074\u0069\u0074\u0079\u002d\u0056":return _eag ;};_bdae ,_baee :=_ab .LoadPredefinedCMap (_cbeec .String ());
if _baee !=nil {return _bb (_fafg ,_gcae );};_cfea :=_bdae .CIDSystemInfo ();if _cfea .Ordering !=_cfea .Registry {return _bb (_fafg ,_gcae );};return _eag ;};_fedg ,_efba :=_ag .GetStream (_dccd );if !_efba {return _bb (_fafg ,_gcae );};_ddbd ,_egbgf :=_gfbb (_fedg ,_fcbbaf ,_cbdad );
if _egbgf !=nil {return _bb (_fafg ,_gcae );};_cgfe :=_ddbd .CIDSystemInfo ();if _cgfe .Ordering !=_cgfe .Registry {return _bb (_fafg ,_gcae );};return _eag ;};var _ Profile =(*Profile3A )(nil );

// Profile is the model.StandardImplementer enhanced by the information about the profile conformance level.
type Profile interface{_a .StandardImplementer ;Conformance ()string ;Part ()int ;};

// NewProfile1A creates a new Profile1A with given options.
func NewProfile1A (options *Profile1Options )*Profile1A {if options ==nil {options =DefaultProfile1Options ();};_bgfa (options );return &Profile1A {profile1 {_fcgb :*options ,_acab :_fbad ()}};};

// Conformance gets the PDF/A conformance.
func (_adag *profile3 )Conformance ()string {return _adag ._gfdf ._db };

// ValidateStandard checks if provided input CompliancePdfReader matches rules that conforms PDF/A-3 standard.
func (_ffbab *profile3 )ValidateStandard (r *_a .CompliancePdfReader )error {_aagag :=VerificationError {ConformanceLevel :_ffbab ._gfdf ._dg ,ConformanceVariant :_ffbab ._gfdf ._db };if _gcbd :=_eada (r );_gcbd !=_eag {_aagag .ViolatedRules =append (_aagag .ViolatedRules ,_gcbd );
};if _dbfa :=_gddfg (r );_dbfa !=_eag {_aagag .ViolatedRules =append (_aagag .ViolatedRules ,_dbfa );};if _ddgd :=_dgf (r );_ddgd !=_eag {_aagag .ViolatedRules =append (_aagag .ViolatedRules ,_ddgd );};if _ddfe :=_ebcf (r );_ddfe !=_eag {_aagag .ViolatedRules =append (_aagag .ViolatedRules ,_ddfe );
};if _bfac :=_aafg (r );_bfac !=_eag {_aagag .ViolatedRules =append (_aagag .ViolatedRules ,_bfac );};if _dgca :=_dcfb (r );len (_dgca )!=0{_aagag .ViolatedRules =append (_aagag .ViolatedRules ,_dgca ...);};if _aebd :=_efcf (r );len (_aebd )!=0{_aagag .ViolatedRules =append (_aagag .ViolatedRules ,_aebd ...);
};if _dcbgf :=_dcabc (r );len (_dcbgf )!=0{_aagag .ViolatedRules =append (_aagag .ViolatedRules ,_dcbgf ...);};if _gcbe :=_dadda (r );_gcbe !=_eag {_aagag .ViolatedRules =append (_aagag .ViolatedRules ,_gcbe );};if _cgcfb :=_afcf (r );len (_cgcfb )!=0{_aagag .ViolatedRules =append (_aagag .ViolatedRules ,_cgcfb ...);
};if _abg :=_cgga (r );len (_abg )!=0{_aagag .ViolatedRules =append (_aagag .ViolatedRules ,_abg ...);};if _cdgd :=_dgdbc (r );_cdgd !=_eag {_aagag .ViolatedRules =append (_aagag .ViolatedRules ,_cdgd );};if _gdac :=_cedc (r );len (_gdac )!=0{_aagag .ViolatedRules =append (_aagag .ViolatedRules ,_gdac ...);
};if _beef :=_bcdd (r );len (_beef )!=0{_aagag .ViolatedRules =append (_aagag .ViolatedRules ,_beef ...);};if _ffag :=_agbe (r );_ffag !=_eag {_aagag .ViolatedRules =append (_aagag .ViolatedRules ,_ffag );};if _cgdd :=_edfcbd (r );len (_cgdd )!=0{_aagag .ViolatedRules =append (_aagag .ViolatedRules ,_cgdd ...);
};if _dea :=_cecfd (r );len (_dea )!=0{_aagag .ViolatedRules =append (_aagag .ViolatedRules ,_dea ...);};if _fda :=_fdbg (r );_fda !=_eag {_aagag .ViolatedRules =append (_aagag .ViolatedRules ,_fda );};if _gdag :=_bbcg (r );len (_gdag )!=0{_aagag .ViolatedRules =append (_aagag .ViolatedRules ,_gdag ...);
};if _aeac :=_ggbd (r ,_ffbab ._gfdf );len (_aeac )!=0{_aagag .ViolatedRules =append (_aagag .ViolatedRules ,_aeac ...);};if _ffbc :=_eeffc (r );len (_ffbc )!=0{_aagag .ViolatedRules =append (_aagag .ViolatedRules ,_ffbc ...);};if _ebfb :=_fffdf (r );len (_ebfb )!=0{_aagag .ViolatedRules =append (_aagag .ViolatedRules ,_ebfb ...);
};if _fdca :=_bbcb (r );len (_fdca )!=0{_aagag .ViolatedRules =append (_aagag .ViolatedRules ,_fdca ...);};if _gadde :=_dgcb (r );_gadde !=_eag {_aagag .ViolatedRules =append (_aagag .ViolatedRules ,_gadde );};if _edfb :=_agged (r );len (_edfb )!=0{_aagag .ViolatedRules =append (_aagag .ViolatedRules ,_edfb ...);
};if _efee :=_fedab (r );_efee !=_eag {_aagag .ViolatedRules =append (_aagag .ViolatedRules ,_efee );};if _eaad :=_cggb (r ,_ffbab ._gfdf ,false );len (_eaad )!=0{_aagag .ViolatedRules =append (_aagag .ViolatedRules ,_eaad ...);};if _ffbab ._gfdf ==_dc (){if _ffgd :=_daag (r );
len (_ffgd )!=0{_aagag .ViolatedRules =append (_aagag .ViolatedRules ,_ffgd ...);};};if _edea :=_cfbce (r );len (_edea )!=0{_aagag .ViolatedRules =append (_aagag .ViolatedRules ,_edea ...);};if _eccbf :=_eddf (r );len (_eccbf )!=0{_aagag .ViolatedRules =append (_aagag .ViolatedRules ,_eccbf ...);
};if _eefe :=_efccg (r );len (_eefe )!=0{_aagag .ViolatedRules =append (_aagag .ViolatedRules ,_eefe ...);};if _gfdg :=_dbbg (r );_gfdg !=_eag {_aagag .ViolatedRules =append (_aagag .ViolatedRules ,_gfdg );};if len (_aagag .ViolatedRules )> 0{_d .Slice (_aagag .ViolatedRules ,func (_bedb ,_gbde int )bool {return _aagag .ViolatedRules [_bedb ].RuleNo < _aagag .ViolatedRules [_gbde ].RuleNo ;
});return _aagag ;};return nil ;};func _gbegd (_baagg *_a .CompliancePdfReader )(_dgg []ViolatedRule ){var _dgcd ,_ebbd ,_ecea ,_dff ,_ecdc ,_gdead bool ;_cecf :=map[*_ag .PdfObjectStream ]struct{}{};for _ ,_befb :=range _baagg .GetObjectNums (){if _dgcd &&_ebbd &&_ecdc &&_ecea &&_dff &&_gdead {return _dgg ;
};_ccag ,_addg :=_baagg .GetIndirectObjectByNumber (_befb );if _addg !=nil {continue ;};_gefb ,_gdffg :=_ag .GetStream (_ccag );if !_gdffg {continue ;};if _ ,_gdffg =_cecf [_gefb ];_gdffg {continue ;};_cecf [_gefb ]=struct{}{};_cfdb ,_gdffg :=_ag .GetName (_gefb .Get ("\u0053u\u0062\u0054\u0079\u0070\u0065"));
if !_gdffg {continue ;};if !_dff {if _gefb .Get ("\u0052\u0065\u0066")!=nil {_dgg =append (_dgg ,_bb ("\u0036.\u0032\u002e\u0036\u002d\u0031","\u0041\u0020\u0063\u006f\u006e\u0066\u006f\u0072\u006d\u0069\u006e\u0067\u0020\u0066\u0069\u006c\u0065\u0020\u0073\u0068a\u006c\u006c\u0020\u006e\u006f\u0074\u0020\u0063\u006f\u006e\u0074\u0061\u0069\u006e\u0020\u0061\u006e\u0079\u0020\u0072\u0065\u0066\u0065\u0072\u0065\u006e\u0063\u0065\u0020\u0058O\u0062\u006a\u0065\u0063\u0074s\u002e"));
_dff =true ;};};if _cfdb .String ()=="\u0050\u0053"{if !_gdead {_dgg =append (_dgg ,_bb ("\u0036.\u0032\u002e\u0037\u002d\u0031","A \u0063\u006fn\u0066\u006f\u0072\u006d\u0069\u006e\u0067\u0020\u0066i\u006c\u0065\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u006e\u006f\u0074\u0020\u0063\u006f\u006e\u0074\u0061\u0069\u006e\u0020\u0061\u006e\u0079\u0020\u0050\u006f\u0073t\u0053c\u0072\u0069\u0070\u0074\u0020\u0058\u004f\u0062j\u0065c\u0074\u0073."));
_gdead =true ;continue ;};};if _cfdb .String ()=="\u0046\u006f\u0072\u006d"{if _ebbd &&_ecea &&_dff {continue ;};if !_ebbd &&_gefb .Get ("\u004f\u0050\u0049")!=nil {_dgg =append (_dgg ,_bb ("\u0036.\u0032\u002e\u0034\u002d\u0032","\u0041\u006e\u0020\u0058\u004f\u0062\u006a\u0065\u0063\u0074\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072y\u0020\u0028\u0049\u006d\u0061\u0067\u0065\u0020\u006f\u0072\u0020\u0046\u006f\u0072\u006d\u0029\u0020\u0073\u0068\u0061\u006cl\u0020\u006e\u006f\u0074\u0020\u0063\u006f\u006e\u0074\u0061\u0069\u006e\u0020\u0074h\u0065\u0020\u004fP\u0049\u0020\u006b\u0065\u0079\u002e"));
_ebbd =true ;};if !_ecea {if _gefb .Get ("\u0050\u0053")!=nil {_ecea =true ;};if _edge :=_gefb .Get ("\u0053\u0075\u0062\u0074\u0079\u0070\u0065\u0032");_edge !=nil &&!_ecea {if _ccabb ,_gbdeb :=_ag .GetName (_edge );_gbdeb &&*_ccabb =="\u0050\u0053"{_ecea =true ;
};};if _ecea {_dgg =append (_dgg ,_bb ("\u0036.\u0032\u002e\u0035\u002d\u0031","A\u0020\u0066\u006fr\u006d\u0020\u0058\u004f\u0062\u006a\u0065\u0063\u0074\u0020\u0064\u0069\u0063\u0074\u0069o\u006e\u0061\u0072\u0079\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u006e\u006ft\u0020\u0063\u006f\u006e\u0074\u0061\u0069\u006e \u0074\u0068\u0065\u0020\u0053\u0075\u0062\u0074\u0079\u0070\u0065\u0032\u0020\u006b\u0065\u0079 \u0077\u0069\u0074\u0068\u0020a\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u006f\u0066\u0020\u0050\u0053\u0020o\u0072\u0020\u0074\u0068e\u0020\u0050\u0053\u0020\u006b\u0065\u0079\u002e"));
};};continue ;};if _cfdb .String ()!="\u0049\u006d\u0061g\u0065"{continue ;};if !_dgcd &&_gefb .Get ("\u0041\u006c\u0074\u0065\u0072\u006e\u0061\u0074\u0065\u0073")!=nil {_dgg =append (_dgg ,_bb ("\u0036.\u0032\u002e\u0034\u002d\u0031","\u0041\u006e\u0020\u0049m\u0061\u0067\u0065\u0020\u0064\u0069\u0063\u0074\u0069o\u006e\u0061\u0072\u0079\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u006e\u006f\u0074\u0020\u0063\u006f\u006et\u0061\u0069\u006e\u0020\u0074h\u0065\u0020\u0041\u006c\u0074\u0065\u0072\u006e\u0061\u0074\u0065\u0073\u0020\u006b\u0065\u0079\u002e"));
_dgcd =true ;};if !_ecdc &&_gefb .Get ("I\u006e\u0074\u0065\u0072\u0070\u006f\u006c\u0061\u0074\u0065")!=nil {_eede ,_cege :=_ag .GetBool (_gefb .Get ("I\u006e\u0074\u0065\u0072\u0070\u006f\u006c\u0061\u0074\u0065"));if _cege &&bool (*_eede ){continue ;
};_dgg =append (_dgg ,_bb ("\u0036.\u0032\u002e\u0034\u002d\u0033","\u0049\u0066 a\u006e\u0020\u0049\u006d\u0061\u0067\u0065\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u0020\u0063o\u006e\u0074\u0061\u0069n\u0073\u0020\u0074\u0068e \u0049\u006et\u0065r\u0070\u006f\u006c\u0061\u0074\u0065 \u006b\u0065\u0079,\u0020\u0069t\u0073\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u0073\u0068\u0061\u006c\u006c\u0020b\u0065\u0020\u0066\u0061\u006c\u0073\u0065\u002e"));
_ecdc =true ;};};return _dgg ;};func _edfcbd (_cdgbg *_a .CompliancePdfReader )(_agcdf []ViolatedRule ){var _eace ,_aefgg ,_fdbdc ,_aecd ,_dfdb ,_gcfbc ,_fgfg bool ;_bcgae :=map[*_ag .PdfObjectStream ]struct{}{};for _ ,_agfg :=range _cdgbg .GetObjectNums (){if _eace &&_aefgg &&_dfdb &&_fdbdc &&_aecd &&_gcfbc &&_fgfg {return _agcdf ;
};_cccab ,_bece :=_cdgbg .GetIndirectObjectByNumber (_agfg );if _bece !=nil {continue ;};_cgggd ,_aegc :=_ag .GetStream (_cccab );if !_aegc {continue ;};if _ ,_aegc =_bcgae [_cgggd ];_aegc {continue ;};_bcgae [_cgggd ]=struct{}{};_afaed ,_aegc :=_ag .GetName (_cgggd .Get ("\u0053u\u0062\u0054\u0079\u0070\u0065"));
if !_aegc {continue ;};if !_aecd {if _cgggd .Get ("\u0052\u0065\u0066")!=nil {_agcdf =append (_agcdf ,_bb ("\u0036.\u0032\u002e\u0039\u002d\u0032","\u0041\u0020\u0063\u006f\u006e\u0066\u006f\u0072\u006d\u0069\u006e\u0067\u0020\u0066\u0069\u006c\u0065\u0020\u0073\u0068a\u006c\u006c\u0020\u006e\u006f\u0074\u0020\u0063\u006f\u006e\u0074\u0061\u0069\u006e\u0020\u0061\u006e\u0079\u0020\u0072\u0065\u0066\u0065\u0072\u0065\u006e\u0063\u0065\u0020\u0058O\u0062\u006a\u0065\u0063\u0074s\u002e"));
_aecd =true ;};};if _afaed .String ()=="\u0050\u0053"{if !_gcfbc {_agcdf =append (_agcdf ,_bb ("\u0036.\u0032\u002e\u0039\u002d\u0033","A \u0063\u006fn\u0066\u006f\u0072\u006d\u0069\u006e\u0067\u0020\u0066i\u006c\u0065\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u006e\u006f\u0074\u0020\u0063\u006f\u006e\u0074\u0061\u0069\u006e\u0020\u0061\u006e\u0079\u0020\u0050\u006f\u0073t\u0053c\u0072\u0069\u0070\u0074\u0020\u0058\u004f\u0062j\u0065c\u0074\u0073."));
_gcfbc =true ;continue ;};};if _afaed .String ()=="\u0046\u006f\u0072\u006d"{if _aefgg &&_fdbdc &&_aecd {continue ;};if !_aefgg &&_cgggd .Get ("\u004f\u0050\u0049")!=nil {_agcdf =append (_agcdf ,_bb ("\u0036.\u0032\u002e\u0039\u002d\u0031","\u0041\u0020\u0066\u006f\u0072\u006d \u0058\u004f\u0062j\u0065\u0063\u0074 \u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079 \u0073\u0068\u0061\u006c\u006c n\u006f\u0074\u0020\u0063\u006f\u006e\u0074\u0061\u0069\u006e\u0020\u0074\u0068\u0065\u0020\u004f\u0050\u0049\u0020\u006b\u0065\u0079\u002e"));
_aefgg =true ;};if !_fdbdc {if _cgggd .Get ("\u0050\u0053")!=nil {_fdbdc =true ;};if _egbea :=_cgggd .Get ("\u0053\u0075\u0062\u0074\u0079\u0070\u0065\u0032");_egbea !=nil &&!_fdbdc {if _bdef ,_fcbcd :=_ag .GetName (_egbea );_fcbcd &&*_bdef =="\u0050\u0053"{_fdbdc =true ;
};};if _fdbdc {_agcdf =append (_agcdf ,_bb ("\u0036.\u0032\u002e\u0039\u002d\u0031","\u0041\u0020\u0066\u006f\u0072\u006d\u0020\u0058\u004f\u0062\u006a\u0065\u0063\u0074\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u006eo\u0074\u0020\u0063\u006f\u006e\u0074\u0061\u0069\u006e\u0020\u0074\u0068\u0065\u0020\u0053\u0075\u0062\u0074\u0079\u0070\u0065\u0032\u0020\u006b\u0065y \u0077\u0069\u0074\u0068\u0020\u0061\u0020\u0076\u0061\u006cu\u0065 o\u0066 \u0050\u0053\u0020\u0061\u006e\u0064\u0020t\u0068\u0065\u0020\u0050\u0053\u0020\u006b\u0065\u0079\u002e"));
};};continue ;};if _afaed .String ()!="\u0049\u006d\u0061g\u0065"{continue ;};if !_eace &&_cgggd .Get ("\u0041\u006c\u0074\u0065\u0072\u006e\u0061\u0074\u0065\u0073")!=nil {_agcdf =append (_agcdf ,_bb ("\u0036.\u0032\u002e\u0038\u002d\u0031","\u0041\u006e\u0020\u0049m\u0061\u0067\u0065\u0020\u0064\u0069\u0063\u0074\u0069o\u006e\u0061\u0072\u0079\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u006e\u006f\u0074\u0020\u0063\u006f\u006et\u0061\u0069\u006e\u0020\u0074h\u0065\u0020\u0041\u006c\u0074\u0065\u0072\u006e\u0061\u0074\u0065\u0073\u0020\u006b\u0065\u0079\u002e"));
_eace =true ;};if !_fgfg &&_cgggd .Get ("\u004f\u0050\u0049")!=nil {_agcdf =append (_agcdf ,_bb ("\u0036.\u0032\u002e\u0038\u002d\u0032","\u0041\u006e\u0020\u0049\u006d\u0061\u0067\u0065\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072y\u0020\u0073\u0068\u0061\u006c\u006c\u0020n\u006f\u0074\u0020\u0063\u006f\u006e\u0074\u0061\u0069\u006e\u0020t\u0068\u0065\u0020\u004f\u0050\u0049\u0020\u006b\u0065\u0079\u002e"));
_fgfg =true ;};if !_dfdb &&_cgggd .Get ("I\u006e\u0074\u0065\u0072\u0070\u006f\u006c\u0061\u0074\u0065")!=nil {_eebdg ,_ddbca :=_ag .GetBool (_cgggd .Get ("I\u006e\u0074\u0065\u0072\u0070\u006f\u006c\u0061\u0074\u0065"));if _ddbca &&bool (*_eebdg ){continue ;
};_agcdf =append (_agcdf ,_bb ("\u0036.\u0032\u002e\u0038\u002d\u0033","\u0049\u0066 a\u006e\u0020\u0049\u006d\u0061\u0067\u0065\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u0020\u0063o\u006e\u0074\u0061\u0069n\u0073\u0020\u0074\u0068e \u0049\u006et\u0065r\u0070\u006f\u006c\u0061\u0074\u0065 \u006b\u0065\u0079,\u0020\u0069t\u0073\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u0073\u0068\u0061\u006c\u006c\u0020b\u0065\u0020\u0066\u0061\u006c\u0073\u0065\u002e"));
_dfdb =true ;};};return _agcdf ;};func _fffdf (_dfdcg *_a .CompliancePdfReader )(_ebaf []ViolatedRule ){for _ ,_abgge :=range _dfdcg .GetObjectNums (){_aedb ,_abcdd :=_dfdcg .GetIndirectObjectByNumber (_abgge );if _abcdd !=nil {continue ;};_eegcd ,_abdad :=_ag .GetDict (_aedb );
if !_abdad {continue ;};_gbaa ,_abdad :=_ag .GetName (_eegcd .Get ("\u0054\u0079\u0070\u0065"));if !_abdad {continue ;};if _gbaa .String ()!="\u0041\u0063\u0072\u006f\u0046\u006f\u0072\u006d"{continue ;};_eddeg ,_abdad :=_ag .GetBool (_eegcd .Get ("\u004ee\u0065d\u0041\u0070\u0070\u0065\u0061\u0072\u0061\u006e\u0063\u0065\u0073"));
if _abdad &&bool (*_eddeg ){_ebaf =append (_ebaf ,_bb ("\u0036.\u0034\u002e\u0031\u002d\u0033","\u0054\u0068\u0065\u0020\u004e\u0065e\u0064\u0041\u0070\u0070\u0065a\u0072\u0061\u006e\u0063\u0065\u0073\u0020\u0066\u006c\u0061\u0067\u0020\u006f\u0066\u0020\u0074\u0068\u0065\u0020\u0069\u006e\u0074\u0065\u0072\u0061\u0063\u0074\u0069\u0076e\u0020\u0066\u006f\u0072\u006d \u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0065\u0069\u0074\u0068\u0065\u0072\u0020\u006e\u006f\u0074\u0020b\u0065\u0020\u0070\u0072\u0065se\u006e\u0074\u0020\u006f\u0072\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0062\u0065\u0020\u0066\u0061\u006c\u0073\u0065\u002e"));
};if _eegcd .Get ("\u0058\u0046\u0041")!=nil {_ebaf =append (_ebaf ,_bb ("\u0036.\u0034\u002e\u0032\u002d\u0031","\u0054\u0068\u0065\u0020\u0064o\u0063\u0075\u006d\u0065\u006e\u0074\u0027\u0073\u0020i\u006e\u0074\u0065\u0072\u0061\u0063\u0074\u0069\u0076\u0065\u0020\u0066\u006f\u0072\u006d\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061r\u0079\u0020t\u0068\u0061\u0074\u0020f\u006f\u0072\u006d\u0073\u0020\u0074\u0068\u0065\u0020\u0076\u0061\u006c\u0075\u0065 \u006f\u0066\u0020\u0074\u0068\u0065\u0020\u0041\u0063\u0072\u006f\u0046\u006f\u0072\u006d \u006b\u0065\u0079\u0020i\u006e\u0020\u0074\u0068\u0065\u0020\u0064\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u0027\u0073\u0020\u0043\u0061\u0074\u0061\u006c\u006f\u0067\u0020\u006f\u0066 \u0061 \u0050\u0044F\u002fA\u002d\u0032\u0020\u0066ile\u002c\u0020\u0069\u0066\u0020\u0070\u0072\u0065\u0073\u0065n\u0074\u002c\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u006e\u006ft\u0020\u0063\u006f\u006e\u0074a\u0069\u006e\u0020\u0074\u0068\u0065\u0020\u0058\u0046\u0041\u0020\u006b\u0065y."));
};};_babca ,_gedad :=_gfcb (_dfdcg );if _gedad &&_babca .Get ("\u004e\u0065\u0065\u0064\u0073\u0052\u0065\u006e\u0064e\u0072\u0069\u006e\u0067")!=nil {_ebaf =append (_ebaf ,_bb ("\u0036.\u0034\u002e\u0032\u002d\u0032","\u0041\u0020\u0064\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u0027\u0073\u0020\u0043\u0061\u0074\u0061\u006cog\u0020s\u0068\u0061\u006c\u006c\u0020\u006e\u006f\u0074\u0020\u0063\u006f\u006et\u0061\u0069\u006e\u0020\u0074\u0068\u0065\u0020\u004e\u0065\u0065\u0064\u0073\u0052\u0065\u006e\u0064e\u0072\u0069\u006e\u0067\u0020\u006b\u0065\u0079\u002e"));
};return _ebaf ;};

// Profile3B is the implementation of the PDF/A-3B standard profile.
// Implements model.StandardImplementer, Profile interfaces.
type Profile3B struct{profile3 };func _dcabc (_fege *_a .CompliancePdfReader )[]ViolatedRule {return nil };func _gbbe (_gec *_a .PdfPageResources ,_geec *_fbd .ContentStreamOperations ,_edd bool )([]byte ,error ){var _fffe bool ;for _ ,_gadd :=range *_geec {_babg :switch _gadd .Operand {case "\u0042\u0049":_bcgg ,_afgc :=_gadd .Params [0].(*_fbd .ContentStreamInlineImage );
if !_afgc {break ;};_cbec ,_eafg :=_bcgg .GetColorSpace (_gec );if _eafg !=nil {return nil ,_eafg ;};switch _cbec .(type ){case *_a .PdfColorspaceDeviceCMYK :if _edd {break _babg ;};case *_a .PdfColorspaceDeviceGray :case *_a .PdfColorspaceDeviceRGB :if !_edd {break _babg ;
};default:break _babg ;};_fffe =true ;_ddbc ,_eafg :=_bcgg .ToImage (_gec );if _eafg !=nil {return nil ,_eafg ;};_acd ,_eafg :=_ddbc .ToGoImage ();if _eafg !=nil {return nil ,_eafg ;};if _edd {_acd ,_eafg =_fe .CMYKConverter .Convert (_acd );}else {_acd ,_eafg =_fe .NRGBAConverter .Convert (_acd );
};if _eafg !=nil {return nil ,_eafg ;};_egda ,_afgc :=_acd .(_fe .Image );if !_afgc {return nil ,_ce .New ("\u0069\u006d\u0061\u0067\u0065\u0020\u0064\u006f\u0065\u0073\u006e\u0027\u0074 \u0069\u006d\u0070\u006c\u0065\u006de\u006e\u0074\u0020\u0069\u006d\u0061\u0067\u0065\u0075\u0074\u0069\u006c\u002eI\u006d\u0061\u0067\u0065");
};_aeee :=_egda .Base ();_cbff :=_a .Image {Width :int64 (_aeee .Width ),Height :int64 (_aeee .Height ),BitsPerComponent :int64 (_aeee .BitsPerComponent ),ColorComponents :_aeee .ColorComponents ,Data :_aeee .Data };_cbff .SetDecode (_aeee .Decode );_cbff .SetAlpha (_aeee .Alpha );
_bdca ,_eafg :=_bcgg .GetEncoder ();if _eafg !=nil {_bdca =_ag .NewFlateEncoder ();};_bdg ,_eafg :=_fbd .NewInlineImageFromImage (_cbff ,_bdca );if _eafg !=nil {return nil ,_eafg ;};_gadd .Params [0]=_bdg ;case "\u0047","\u0067":if len (_gadd .Params )!=1{break ;
};_dcce ,_gbed :=_ag .GetNumberAsFloat (_gadd .Params [0]);if _gbed !=nil {break ;};if _edd {_gadd .Params =[]_ag .PdfObject {_ag .MakeFloat (0),_ag .MakeFloat (0),_ag .MakeFloat (0),_ag .MakeFloat (1-_dcce )};_ada :="\u004b";if _gadd .Operand =="\u0067"{_ada ="\u006b";
};_gadd .Operand =_ada ;}else {_gadd .Params =[]_ag .PdfObject {_ag .MakeFloat (_dcce ),_ag .MakeFloat (_dcce ),_ag .MakeFloat (_dcce )};_fcfe :="\u0052\u0047";if _gadd .Operand =="\u0067"{_fcfe ="\u0072\u0067";};_gadd .Operand =_fcfe ;};_fffe =true ;case "\u0052\u0047","\u0072\u0067":if !_edd {break ;
};if len (_gadd .Params )!=3{break ;};_febac ,_dcbc :=_ag .GetNumbersAsFloat (_gadd .Params );if _dcbc !=nil {break ;};_fffe =true ;_cfaa ,_fbf ,_ffc :=_febac [0],_febac [1],_febac [2];_eegb ,_ddcf ,_fbb ,_afa :=_fa .RGBToCMYK (uint8 (_cfaa *255),uint8 (_fbf *255),uint8 (255*_ffc ));
_gadd .Params =[]_ag .PdfObject {_ag .MakeFloat (float64 (_eegb )/255),_ag .MakeFloat (float64 (_ddcf )/255),_ag .MakeFloat (float64 (_fbb )/255),_ag .MakeFloat (float64 (_afa )/255)};_edee :="\u004b";if _gadd .Operand =="\u0072\u0067"{_edee ="\u006b";
};_gadd .Operand =_edee ;case "\u004b","\u006b":if _edd {break ;};if len (_gadd .Params )!=4{break ;};_bafb ,_fcbb :=_ag .GetNumbersAsFloat (_gadd .Params );if _fcbb !=nil {break ;};_fgcf ,_fgag ,_abb ,_ccee :=_bafb [0],_bafb [1],_bafb [2],_bafb [3];_adda ,_fdbc ,_cca :=_fa .CMYKToRGB (uint8 (255*_fgcf ),uint8 (255*_fgag ),uint8 (255*_abb ),uint8 (255*_ccee ));
_gadd .Params =[]_ag .PdfObject {_ag .MakeFloat (float64 (_adda )/255),_ag .MakeFloat (float64 (_fdbc )/255),_ag .MakeFloat (float64 (_cca )/255)};_bdac :="\u0052\u0047";if _gadd .Operand =="\u006b"{_bdac ="\u0072\u0067";};_gadd .Operand =_bdac ;_fffe =true ;
};};if !_fffe {return nil ,nil ;};_bgcc :=_fbd .NewContentCreator ();for _ ,_dbbf :=range *_geec {_bgcc .AddOperand (*_dbbf );};_gbea :=_bgcc .Bytes ();return _gbea ,nil ;};func _dcbg (_egce *_gc .Document )error {_bfbg ,_bcbb :=_egce .FindCatalog ();if !_bcbb {return _ce .New ("\u0063\u0061\u0074\u0061\u006c\u006f\u0067\u0020\u006e\u006f\u0074\u0020f\u006f\u0075\u006e\u0064");
};_gbeg ,_bcbb :=_ag .GetDict (_bfbg .Object .Get ("\u004f\u0043\u0050r\u006f\u0070\u0065\u0072\u0074\u0069\u0065\u0073"));if !_bcbb {return nil ;};_gfc ,_bcbb :=_ag .GetDict (_gbeg .Get ("\u0044"));if _bcbb {if _gfc .Get ("\u0041\u0053")!=nil {_gfc .Remove ("\u0041\u0053");
};};_faa ,_bcbb :=_ag .GetArray (_gbeg .Get ("\u0043o\u006e\u0066\u0069\u0067\u0073"));if _bcbb {for _agge :=0;_agge < _faa .Len ();_agge ++{_bdcdb ,_eacd :=_ag .GetDict (_faa .Get (_agge ));if !_eacd {continue ;};if _bdcdb .Get ("\u0041\u0053")!=nil {_bdcdb .Remove ("\u0041\u0053");
};};};return nil ;};func _cgga (_eddee *_a .CompliancePdfReader )(_ebbcf []ViolatedRule ){var (_efag ,_dbfac ,_fgece ,_fcdb ,_fcbc bool ;_dfge func (_ag .PdfObject ););_dfge =func (_efae _ag .PdfObject ){switch _gdaa :=_efae .(type ){case *_ag .PdfObjectInteger :if !_efag &&(int64 (*_gdaa )> _c .MaxInt32 ||int64 (*_gdaa )< -_c .MaxInt32 ){_ebbcf =append (_ebbcf ,_bb ("\u0036\u002e\u0031\u002e\u0031\u0033\u002d\u0031","L\u0061\u0072\u0067e\u0073\u0074\u0020\u0049\u006e\u0074\u0065\u0067\u0065\u0072\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u0069\u0073\u0020\u0032\u002c\u0031\u0034\u0037,\u0034\u0038\u0033,\u0036\u0034\u0037\u002e\u0020\u0053\u006d\u0061\u006c\u006c\u0065\u0073\u0074 \u0069\u006e\u0074\u0065g\u0065\u0072\u0020\u0076a\u006c\u0075\u0065\u0020\u0069\u0073\u0020\u002d\u0032\u002c\u0031\u0034\u0037\u002c\u0034\u0038\u0033,\u0036\u0034\u0038\u002e"));
_efag =true ;};case *_ag .PdfObjectFloat :if !_dbfac &&(_c .Abs (float64 (*_gdaa ))> _c .MaxFloat32 ){_ebbcf =append (_ebbcf ,_bb ("\u0036\u002e\u0031\u002e\u0031\u0033\u002d\u0032","\u0041 \u0063\u006f\u006e\u0066orm\u0069\u006e\u0067\u0020f\u0069\u006c\u0065\u0020\u0073\u0068\u0061\u006c\u006c\u0020n\u006f\u0074\u0020\u0063\u006f\u006e\u0074\u0061\u0069\u006e\u0020\u0061\u006e\u0079\u0020\u0072\u0065\u0061\u006c\u0020\u006e\u0075\u006db\u0065\u0072\u0020\u006f\u0075\u0074\u0073\u0069de\u0020\u0074\u0068e\u0020\u0072\u0061\u006e\u0067e\u0020o\u0066\u0020\u002b\u002f\u002d\u0033\u002e\u0034\u00303\u0020\u0078\u0020\u0031\u0030\u005e\u0033\u0038\u002e"));
};case *_ag .PdfObjectString :if !_fgece &&len ([]byte (_gdaa .Str ()))> 32767{_ebbcf =append (_ebbcf ,_bb ("\u0036\u002e\u0031\u002e\u0031\u0033\u002d\u0033","M\u0061\u0078\u0069\u006d\u0075\u006d\u0020\u006c\u0065n\u0067\u0074\u0068\u0020\u006f\u0066\u0020a \u0073\u0074\u0072\u0069n\u0067\u0020\u0028\u0069\u006e\u0020\u0062\u0079\u0074es\u0029\u0020i\u0073\u0020\u0033\u0032\u0037\u0036\u0037\u002e"));
_fgece =true ;};case *_ag .PdfObjectName :if !_fcdb &&len ([]byte (*_gdaa ))> 127{_ebbcf =append (_ebbcf ,_bb ("\u0036\u002e\u0031\u002e\u0031\u0033\u002d\u0034","\u004d\u0061\u0078\u0069\u006d\u0075\u006d \u006c\u0065\u006eg\u0074\u0068\u0020\u006ff\u0020\u0061\u0020\u006e\u0061\u006d\u0065\u0020\u0028\u0069\u006e\u0020\u0062\u0079\u0074\u0065\u0073\u0029\u0020\u0069\u0073\u0020\u0031\u0032\u0037\u002e"));
_fcdb =true ;};case *_ag .PdfObjectArray :for _ ,_gacc :=range _gdaa .Elements (){_dfge (_gacc );};if !_fcbc &&(_gdaa .Len ()==4||_gdaa .Len ()==5){_agada ,_beed :=_ag .GetName (_gdaa .Get (0));if !_beed {return ;};if *_agada !="\u0044e\u0076\u0069\u0063\u0065\u004e"{return ;
};_gggab :=_gdaa .Get (1);_gggab =_ag .TraceToDirectObject (_gggab );_begaf ,_beed :=_ag .GetArray (_gggab );if !_beed {return ;};if _begaf .Len ()> 32{_ebbcf =append (_ebbcf ,_bb ("\u0036\u002e\u0031\u002e\u0031\u0033\u002d\u0039","\u004d\u0061\u0078\u0069\u006d\u0075\u006d \u006e\u0075\u006db\u0065\u0072\u0020\u006ff\u0020\u0044\u0065\u0076\u0069\u0063\u0065\u004e\u0020\u0063\u006f\u006d\u0070\u006f\u006e\u0065\u006e\u0074\u0073\u0020\u0069\u0073\u0020\u0033\u0032\u002e"));
_fcbc =true ;};};case *_ag .PdfObjectDictionary :_afaf :=_gdaa .Keys ();for _gfdaf ,_bedfg :=range _afaf {_dfge (&_afaf [_gfdaf ]);_dfge (_gdaa .Get (_bedfg ));};case *_ag .PdfObjectStream :_dfge (_gdaa .PdfObjectDictionary );case *_ag .PdfObjectStreams :for _ ,_gacb :=range _gdaa .Elements (){_dfge (_gacb );
};case *_ag .PdfObjectReference :_dfge (_gdaa .Resolve ());};};_geafc :=_eddee .GetObjectNums ();if len (_geafc )> 8388607{_ebbcf =append (_ebbcf ,_bb ("\u0036\u002e\u0031\u002e\u0031\u0033\u002d\u0037","\u004d\u0061\u0078\u0069\u006d\u0075\u006d\u0020\u006e\u0075\u006d\u0062\u0065\u0072\u0020\u006f\u0066\u0020in\u0064i\u0072\u0065\u0063\u0074\u0020\u006f\u0062\u006a\u0065\u0063\u0074\u0073 \u0069\u006e\u0020\u0061\u0020\u0050\u0044\u0046\u0020\u0066\u0069\u006c\u0065\u0020\u0069\u0073\u00208\u002c\u0033\u0038\u0038\u002c\u0036\u0030\u0037\u002e"));
};for _ ,_eefef :=range _geafc {_ggecf ,_ddda :=_eddee .GetIndirectObjectByNumber (_eefef );if _ddda !=nil {continue ;};_dgadf :=_ag .TraceToDirectObject (_ggecf );_dfge (_dgadf );};return _ebbcf ;};type imageModifications struct{_bec *colorspaceModification ;
_feb _ag .StreamEncoder ;};func _acee (_dgbae *_a .CompliancePdfReader )(_eead []ViolatedRule ){var _dcfba ,_gdff ,_cceg bool ;if _dgbae .ParserMetadata ().HasNonConformantStream (){_eead =[]ViolatedRule {_bb ("\u0036.\u0031\u002e\u0037\u002d\u0031","T\u0068\u0065\u0020\u0073\u0074\u0072\u0065\u0061\u006d\u0020\u006b\u0065\u0079\u0077\u006fr\u0064\u0020\u0073\u0068\u0061\u006c\u006c \u0062\u0065\u0020f\u006f\u006cl\u006fw\u0065\u0064\u0020e\u0069\u0074h\u0065\u0072\u0020\u0062\u0079\u0020\u0061 \u0043\u0041\u0052\u0052I\u0041\u0047\u0045\u0020\u0052E\u0054\u0055\u0052\u004e\u0020\u00280\u0044\u0068\u0029\u0020\u0061\u006e\u0064\u0020\u004c\u0049\u004e\u0045\u0020F\u0045\u0045\u0044\u0020\u0028\u0030\u0041\u0068\u0029\u0020\u0063\u0068\u0061\u0072\u0061\u0063\u0074\u0065\u0072\u0020\u0073\u0065\u0071\u0075\u0065\u006e\u0063\u0065\u0020o\u0072\u0020\u0062\u0079\u0020\u0061 \u0073\u0069ng\u006c\u0065\u0020\u004cIN\u0045 \u0046\u0045\u0045\u0044 \u0063\u0068\u0061r\u0061\u0063\u0074\u0065\u0072\u002e\u0020T\u0068\u0065\u0020e\u006e\u0064\u0073\u0074r\u0065\u0061\u006d\u0020\u006b\u0065\u0079\u0077\u006fr\u0064\u0020\u0073\u0068\u0061\u006c\u006c \u0062e\u0020p\u0072\u0065\u0063\u0065\u0064\u0065\u0064\u0020\u0062\u0079\u0020\u0061n\u0020\u0045\u004f\u004c \u006d\u0061\u0072\u006b\u0065\u0072\u002e")};
};for _ ,_fdfec :=range _dgbae .GetObjectNums (){_caaf ,_ :=_dgbae .GetIndirectObjectByNumber (_fdfec );if _caaf ==nil {continue ;};_ebebc ,_fgd :=_ag .GetStream (_caaf );if !_fgd {continue ;};if !_dcfba {_dcge :=_ebebc .Get ("\u004c\u0065\u006e\u0067\u0074\u0068");
if _dcge ==nil {_eead =append (_eead ,_bb ("\u0036.\u0031\u002e\u0037\u002d\u0032","\u006e\u006f\u0020'\u004c\u0065\u006e\u0067\u0074\u0068\u0027\u0020\u006b\u0065\u0079\u0020\u0066\u006f\u0075\u006e\u0064\u0020\u0069\u006e\u0020\u0074\u0068\u0065\u0020\u0073\u0074\u0072\u0065a\u006d\u0020\u006f\u0062\u006a\u0065\u0063\u0074"));
_dcfba =true ;}else {_baca ,_eegdd :=_ag .GetIntVal (_dcge );if !_eegdd {_eead =append (_eead ,_bb ("\u0036.\u0031\u002e\u0037\u002d\u0032","s\u0074\u0072\u0065\u0061\u006d\u0020\u0027\u004c\u0065\u006e\u0067\u0074\u0068\u0027\u0020\u006b\u0065\u0079 \u0073\u0068\u006f\u0075\u006c\u0064\u0020\u0062\u0065\u0020an\u0020\u0069\u006et\u0065g\u0065\u0072"));
_dcfba =true ;}else {if len (_ebebc .Stream )!=_baca {_eead =append (_eead ,_bb ("\u0036.\u0031\u002e\u0037\u002d\u0032","\u0073\u0074\u0072\u0065\u0061\u006d\u0020\u006f\u0062\u006a\u0065\u0063\u0074\u0020\u006c\u0065\u006e\u0067th\u0020v\u0061\u006c\u0075\u0065\u0020\u0073\u0068\u006f\u0075\u006c\u0064\u0020m\u0061\u0074\u0063\u0068\u0020\u0074\u0068\u0065\u0020\u0073\u0069\u007a\u0065\u0020\u006f\u0066\u0020t\u0068\u0065\u0020\u0073\u0074\u0072\u0065\u0061\u006d"));
_dcfba =true ;};};};};if !_gdff {if _ebebc .Get ("\u0046")!=nil {_gdff =true ;_eead =append (_eead ,_bb ("\u0036.\u0031\u002e\u0037\u002d\u0033","\u0073\u0074r\u0065\u0061\u006d\u0020\u006f\u0062\u006a\u0065\u0063\u0074\u0020\u0073\u0068\u006f\u0075\u006c\u0064\u0020\u006eo\u0074\u0020\u0063\u006f\u006e\u0074a\u0069\u006e\u0020\u0027\u0046\u0027\u002c\u0027\u0046\u0046\u0069\u006c\u0074\u0065r\u0027\u002c'\u0046\u0044\u0065\u0063o\u0064\u0065\u0050\u0061\u0072a\u006d\u0073\u0027\u0020\u006b\u0065\u0079"));
};if _ebebc .Get ("\u0046F\u0069\u006c\u0074\u0065\u0072")!=nil &&!_gdff {_gdff =true ;_eead =append (_eead ,_bb ("\u0036.\u0031\u002e\u0037\u002d\u0033","\u0073\u0074r\u0065\u0061\u006d\u0020\u006f\u0062\u006a\u0065\u0063\u0074\u0020\u0073\u0068\u006f\u0075\u006c\u0064\u0020\u006eo\u0074\u0020\u0063\u006f\u006e\u0074a\u0069\u006e\u0020\u0027\u0046\u0027\u002c\u0027\u0046\u0046\u0069\u006c\u0074\u0065r\u0027\u002c'\u0046\u0044\u0065\u0063o\u0064\u0065\u0050\u0061\u0072a\u006d\u0073\u0027\u0020\u006b\u0065\u0079"));
continue ;};if _ebebc .Get ("\u0046\u0044\u0065\u0063\u006f\u0064\u0065\u0050\u0061\u0072\u0061\u006d\u0073")!=nil &&!_gdff {_gdff =true ;_eead =append (_eead ,_bb ("\u0036.\u0031\u002e\u0037\u002d\u0033","\u0073\u0074r\u0065\u0061\u006d\u0020\u006f\u0062\u006a\u0065\u0063\u0074\u0020\u0073\u0068\u006f\u0075\u006c\u0064\u0020\u006eo\u0074\u0020\u0063\u006f\u006e\u0074a\u0069\u006e\u0020\u0027\u0046\u0027\u002c\u0027\u0046\u0046\u0069\u006c\u0074\u0065r\u0027\u002c'\u0046\u0044\u0065\u0063o\u0064\u0065\u0050\u0061\u0072a\u006d\u0073\u0027\u0020\u006b\u0065\u0079"));
continue ;};};if !_cceg {_eddbb ,_ffea :=_ag .GetName (_ag .TraceToDirectObject (_ebebc .Get ("\u0046\u0069\u006c\u0074\u0065\u0072")));if !_ffea {continue ;};if *_eddbb ==_ag .StreamEncodingFilterNameLZW {_cceg =true ;_eead =append (_eead ,_bb ("\u0036\u002e\u0031\u002e\u0031\u0030\u002d\u0031","\u0054h\u0065\u0020L\u005a\u0057\u0044\u0065c\u006f\u0064\u0065 \u0066\u0069\u006c\u0074\u0065\u0072\u0020\u0073\u0068al\u006c\u0020\u006eo\u0074\u0020b\u0065\u0020\u0070\u0065\u0072\u006di\u0074\u0074e\u0064\u002e"));
};};};return _eead ;};func _dgeb (_cdced *_a .CompliancePdfReader )(_ffeca ViolatedRule ){_fabbb ,_bgcad :=_gfcb (_cdced );if !_bgcad {return _eag ;};_acbb ,_bgcad :=_ag .GetDict (_fabbb .Get ("\u0041\u0063\u0072\u006f\u0046\u006f\u0072\u006d"));if !_bgcad {return _eag ;
};_eaggc ,_bgcad :=_ag .GetArray (_acbb .Get ("\u0046\u0069\u0065\u006c\u0064\u0073"));if !_bgcad {return _eag ;};for _edca :=0;_edca < _eaggc .Len ();_edca ++{_efcef ,_eefc :=_ag .GetDict (_eaggc .Get (_edca ));if !_eefc {continue ;};if _efcef .Get ("\u0041\u0041")!=nil {return _bb ("\u0036.\u0036\u002e\u0032\u002d\u0032","\u0041\u0020F\u0069\u0065\u006cd\u0020\u0064\u0069\u0063\u0074i\u006f\u006e\u0061\u0072\u0079 s\u0068\u0061\u006c\u006c\u0020\u006e\u006f\u0074\u0020\u0069\u006e\u0063\u006c\u0075\u0064\u0065\u0020\u0061n\u0020A\u0041\u0020\u0065\u006e\u0074\u0072y f\u006f\u0072\u0020\u0061\u006e\u0020\u0061\u0064\u0064\u0069\u0074\u0069on\u0061l\u002d\u0061\u0063\u0074i\u006fn\u0073 \u0064\u0069c\u0074\u0069on\u0061\u0072\u0079\u002e");
};};return _eag ;};func (_ebe *documentImages )hasUncalibratedImages ()bool {return _ebe ._fc ||_ebe ._fgb ||_ebe ._ac };func _dbbe (_cddbf standardType ,_gede *_gc .OutputIntents )error {_abae ,_aebc :=_gcf .NewCmykIsoCoatedV2OutputIntent (_cddbf .outputIntentSubtype ());
if _aebc !=nil {return _aebc ;};if _aebc =_gede .Add (_abae .ToPdfObject ());_aebc !=nil {return _aebc ;};return nil ;};func _bcag (_edc *_gc .Document ,_dafc int )error {for _ ,_bdc :=range _edc .Objects {_ded ,_gcfc :=_ag .GetDict (_bdc );if !_gcfc {continue ;
};_gcc :=_ded .Get ("\u0054\u0079\u0070\u0065");if _gcc ==nil {continue ;};if _ggd ,_aae :=_ag .GetName (_gcc );_aae &&_ggd .String ()!="\u0041\u0063\u0074\u0069\u006f\u006e"{continue ;};_decb ,_ddbb :=_ag .GetName (_ded .Get ("\u0053"));if !_ddbb {continue ;
};switch _a .PdfActionType (*_decb ){case _a .ActionTypeLaunch ,_a .ActionTypeSound ,_a .ActionTypeMovie ,_a .ActionTypeResetForm ,_a .ActionTypeImportData ,_a .ActionTypeJavaScript :_ded .Remove ("\u0053");case _a .ActionTypeHide ,_a .ActionTypeSetOCGState ,_a .ActionTypeRendition ,_a .ActionTypeTrans ,_a .ActionTypeGoTo3DView :if _dafc ==2{_ded .Remove ("\u0053");
};case _a .ActionTypeNamed :_ege ,_ddd :=_ag .GetName (_ded .Get ("\u004e"));if !_ddd {continue ;};switch *_ege {case "\u004e\u0065\u0078\u0074\u0050\u0061\u0067\u0065","\u0050\u0072\u0065\u0076\u0050\u0061\u0067\u0065","\u0046i\u0072\u0073\u0074\u0050\u0061\u0067e","\u004c\u0061\u0073\u0074\u0050\u0061\u0067\u0065":default:_ded .Remove ("\u004e");
};};};return nil ;};func (_abd *documentImages )hasOnlyDeviceRGB ()bool {return _abd ._fc &&!_abd ._fgb &&!_abd ._ac };func _fgbf (_cfaag *_a .PdfFont ,_ecgee *_ag .PdfObjectDictionary )ViolatedRule {const (_bfgf ="\u0036.\u0033\u002e\u0037\u002d\u0033";
_gefa ="\u0046\u006f\u006e\u0074\u0020\u0070\u0072\u006f\u0067\u0072\u0061\u006d\u0073\u0027\u0020\u0022\u0063\u006d\u0061\u0070\u0022\u0020\u0074\u0061\u0062\u006c\u0065\u0073\u0020\u0066\u006f\u0072\u0020\u0061\u006c\u006c\u0020\u0073\u0079\u006d\u0062o\u006c\u0069c\u0020\u0054\u0072\u0075e\u0054\u0079\u0070\u0065\u0020\u0066\u006f\u006e\u0074\u0073 \u0073\u0068\u0061\u006c\u006c\u0020\u0063\u006f\u006et\u0061\u0069\u006e\u0020\u0065\u0078\u0061\u0063\u0074\u006cy\u0020\u006f\u006ee\u0020\u0065\u006e\u0063\u006f\u0064\u0069n\u0067\u002e";
);var _bbab string ;if _cbfa ,_cfbd :=_ag .GetName (_ecgee .Get ("\u0053u\u0062\u0074\u0079\u0070\u0065"));_cfbd {_bbab =_cbfa .String ();};if _bbab !="\u0054\u0072\u0075\u0065\u0054\u0079\u0070\u0065"{return _eag ;};_geca :=_cfaag .FontDescriptor ();_cfgfg ,_fggb :=_ag .GetIntVal (_geca .Flags );
if !_fggb {_g .Log .Debug ("\u0066\u006c\u0061\u0067\u0073 \u006e\u006f\u0074\u0020\u0064\u0065\u0066\u0069\u006e\u0065\u0064\u0020\u0066o\u0072\u0020\u0074\u0068\u0065\u0020\u0066\u006f\u006e\u0074\u0020\u0064\u0065\u0073\u0063\u0072\u0069\u0070\u0074\u006f\u0072");
return _bb (_bfgf ,_gefa );};_dagbc :=(uint32 (_cfgfg )>>3)!=0;if !_dagbc {return _eag ;};return _eag ;};func _bbcb (_abca *_a .CompliancePdfReader )(_gccg []ViolatedRule ){return _gccg };func _ccfbe (_bfaf *_ag .PdfObjectDictionary ,_ffeaa map[*_ag .PdfObjectStream ][]byte ,_dded map[*_ag .PdfObjectStream ]*_ab .CMap )ViolatedRule {const (_abcg ="\u0036\u002e\u0032\u002e\u0031\u0031\u002e\u0037\u002d\u0031";
_gbda ="\u0054\u0068\u0065\u0020\u0066\u006f\u006e\u0074\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006ea\u0072\u0079\u0020\u0073\u0068\u0061\u006cl\u0020\u0069\u006e\u0063l\u0075\u0064e\u0020\u0061 \u0054\u006f\u0055\u006e\u0069\u0063\u006f\u0064\u0065\u0020\u0065\u006e\u0074\u0072\u0079\u0020w\u0068\u006f\u0073\u0065\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u0069\u0073 \u0061\u0020\u0043M\u0061\u0070\u0020\u0073\u0074\u0072\u0065\u0061\u006d \u006f\u0062\u006a\u0065\u0063\u0074\u0020\u0074\u0068\u0061\u0074\u0020\u006d\u0061p\u0073\u0020\u0063\u0068\u0061\u0072ac\u0074\u0065\u0072\u0020\u0063\u006fd\u0065s\u0020\u0074\u006f\u0020\u0055\u006e\u0069\u0063\u006f\u0064e \u0076a\u006c\u0075\u0065\u0073,\u0020\u0061\u0073\u0020\u0064\u0065\u0073\u0063r\u0069\u0062\u0065\u0064\u0020\u0069\u006e\u0020P\u0044\u0046\u0020\u0052\u0065f\u0065\u0072\u0065\u006e\u0063\u0065\u0020\u0035.\u0039\u002c\u0020\u0075\u006e\u006ce\u0073\u0073\u0020\u0074\u0068\u0065\u0020\u0066\u006f\u006e\u0074\u0020\u006d\u0065\u0065\u0074\u0073 \u0061\u006e\u0079\u0020\u006f\u0066\u0020\u0074\u0068\u0065\u0020\u0066\u006f\u006c\u006c\u006f\u0077\u0069\u006e\u0067\u0020\u0074\u0068\u0072\u0065\u0065\u0020\u0063\u006f\u006e\u0064\u0069\u0074\u0069\u006f\u006e\u0073\u003a\u000a\u0020\u002d\u0020\u0066o\u006e\u0074\u0073\u0020\u0074\u0068\u0061\u0074\u0020\u0075\u0073\u0065\u0020\u0074\u0068\u0065\u0020\u0070\u0072\u0065\u0064\u0065\u0066\u0069\u006e\u0065\u0064\u0020\u0065\u006e\u0063\u006f\u0064\u0069n\u0067\u0073\u0020M\u0061\u0063\u0052o\u006d\u0061\u006e\u0045\u006e\u0063\u006f\u0064\u0069\u006e\u0067\u002c\u0020\u004d\u0061\u0063\u0045\u0078\u0070\u0065\u0072\u0074E\u006e\u0063\u006f\u0064\u0069\u006e\u0067\u0020\u006f\u0072\u0020\u0057\u0069\u006e\u0041n\u0073\u0069\u0045\u006e\u0063\u006f\u0064\u0069\u006e\u0067\u002c\u0020\u006f\u0072\u0020\u0074\u0068\u0061\u0074\u0020\u0075\u0073\u0065\u0020t\u0068\u0065\u0020\u0070\u0072\u0065d\u0065\u0066\u0069\u006e\u0065\u0064\u0020\u0049\u0064\u0065\u006e\u0074\u0069\u0074\u0079\u002d\u0048\u0020\u006f\u0072\u0020\u0049\u0064\u0065n\u0074\u0069\u0074\u0079\u002d\u0056\u0020C\u004d\u0061\u0070s\u003b\u000a\u0020\u002d\u0020\u0054\u0079\u0070\u0065\u0020\u0031\u0020\u0066\u006f\u006e\u0074\u0073\u0020\u0077\u0068\u006f\u0073\u0065\u0020\u0063\u0068\u0061\u0072\u0061\u0063\u0074\u0065\u0072\u0020\u006e\u0061\u006d\u0065\u0073\u0020a\u0072\u0065 \u0074\u0061k\u0065\u006e\u0020\u0066\u0072\u006f\u006d\u0020\u0074\u0068\u0065\u0020\u0041\u0064\u006f\u0062\u0065\u0020\u0073\u0074\u0061n\u0064\u0061\u0072\u0064\u0020L\u0061t\u0069\u006e\u0020\u0063\u0068a\u0072\u0061\u0063\u0074\u0065\u0072\u0020\u0073\u0065\u0074\u0020\u006fr\u0020\u0074\u0068\u0065 \u0073\u0065\u0074\u0020\u006f\u0066 \u006e\u0061\u006d\u0065\u0064\u0020\u0063\u0068\u0061\u0072\u0061\u0063\u0074\u0065r\u0073\u0020\u0069\u006e\u0020\u0074\u0068\u0065\u0020\u0053\u0079\u006d\u0062\u006f\u006c\u0020\u0066\u006f\u006e\u0074\u002c\u0020\u0061\u0073\u0020\u0064\u0065\u0066\u0069\u006e\u0065\u0064\u0020i\u006e\u0020\u0050\u0044\u0046 \u0052\u0065\u0066\u0065\u0072\u0065\u006e\u0063\u0065\u0020\u0041\u0070\u0070\u0065\u006e\u0064\u0069\u0078 \u0044\u003b\u000a\u0020\u002d\u0020\u0054\u0079\u0070\u0065\u0020\u0030\u0020\u0066\u006f\u006e\u0074\u0073\u0020w\u0068\u006f\u0073e\u0020d\u0065\u0073\u0063\u0065n\u0064\u0061\u006e\u0074 \u0043\u0049\u0044\u0046\u006f\u006e\u0074\u0020\u0075\u0073\u0065\u0073\u0020\u0074\u0068\u0065\u0020\u0041\u0064\u006f\u0062\u0065\u002d\u0047B\u0031\u002c\u0020\u0041\u0064\u006fb\u0065\u002d\u0043\u004e\u0053\u0031\u002c\u0020\u0041\u0064\u006f\u0062\u0065\u002d\u004a\u0061\u0070\u0061\u006e\u0031\u0020\u006f\u0072\u0020\u0041\u0064\u006f\u0062\u0065\u002d\u004b\u006fr\u0065\u0061\u0031\u0020\u0063\u0068\u0061r\u0061\u0063\u0074\u0065\u0072\u0020\u0063\u006f\u006c\u006c\u0065\u0063\u0074\u0069\u006f\u006e\u0073\u002e";
);_ddedd ,_cgce :=_ag .GetStream (_bfaf .Get ("\u0054o\u0055\u006e\u0069\u0063\u006f\u0064e"));if _cgce {_ ,_egcb :=_gfbb (_ddedd ,_ffeaa ,_dded );if _egcb !=nil {return _bb (_abcg ,_gbda );};return _eag ;};_edeag ,_cgce :=_ag .GetName (_bfaf .Get ("\u0053u\u0062\u0074\u0079\u0070\u0065"));
if !_cgce {return _bb (_abcg ,_gbda );};switch _edeag .String (){case "\u0054\u0079\u0070e\u0031":return _eag ;};return _bb (_abcg ,_gbda );};func _bfga (_gag *_gc .Document )error {_aaga ,_gfd :=_gag .FindCatalog ();if !_gfd {return _ce .New ("\u0063\u0061\u0074\u0061\u006c\u006f\u0067\u0020\u006e\u006f\u0074\u0020f\u006f\u0075\u006e\u0064");
};_ ,_gfd =_ag .GetDict (_aaga .Object .Get ("\u0041\u0041"));if !_gfd {return nil ;};_aaga .Object .Remove ("\u0041\u0041");return nil ;};func _ecbf (_egede *Profile2Options ){if _egede .Now ==nil {_egede .Now =_e .Now ;};};func _ggaf (_cdcc *_a .CompliancePdfReader )ViolatedRule {return _eag };
var _ Profile =(*Profile1B )(nil );func _afcf (_fefg *_a .CompliancePdfReader )(_gbdb []ViolatedRule ){_fedgg ,_dbec :=_gfcb (_fefg );if !_dbec {return _gbdb ;};_aacd ,_dbec :=_ag .GetDict (_fedgg .Get ("\u0050\u0065\u0072m\u0073"));if !_dbec {return _gbdb ;
};_gddfag :=_aacd .Keys ();for _ ,_ebeg :=range _gddfag {if _ebeg .String ()!="\u0055\u0052\u0033"&&_ebeg .String ()!="\u0044\u006f\u0063\u004d\u0044\u0050"{_gbdb =append (_gbdb ,_bb ("\u0036\u002e\u0031\u002e\u0031\u0032\u002d\u0031","\u004e\u006f\u0020\u006b\u0065\u0079\u0073 \u006f\u0074\u0068\u0065\u0072\u0020\u0074\u0068\u0061\u006e\u0020\u0055\u0052\u0033 \u0061n\u0064\u0020\u0044\u006f\u0063\u004dD\u0050\u0020\u0073\u0068\u0061\u006c\u006c \u0062\u0065\u0020\u0070\u0072\u0065\u0073\u0065\u006e\u0074\u0020\u0069\u006e\u0020\u0061\u0020\u0070\u0065\u0072\u006d\u0069\u0073\u0073i\u006f\u006e\u0073\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072y\u002e"));
};};return _gbdb ;};var _ Profile =(*Profile3B )(nil );func _gabcf (_bdbf *_a .CompliancePdfReader )(_fgcba []ViolatedRule ){var _fdcgd ,_bdcdf ,_gdage ,_cdcf ,_ddbga ,_ddee ,_ccbe bool ;_decef :=func ()bool {return _fdcgd &&_bdcdf &&_gdage &&_cdcf &&_ddbga &&_ddee &&_ccbe };
for _ ,_dbgg :=range _bdbf .PageList {_bdfd ,_dbdc :=_dbgg .GetAnnotations ();if _dbdc !=nil {_g .Log .Trace ("\u006c\u006f\u0061\u0064\u0069\u006e\u0067\u0020\u0061\u006en\u006f\u0074\u0061\u0074\u0069\u006f\u006es\u0020\u0066\u0061\u0069\u006c\u0065\u0064\u003a\u0020\u0025\u0076",_dbdc );
continue ;};for _ ,_gcadd :=range _bdfd {if !_fdcgd {switch _gcadd .GetContext ().(type ){case *_a .PdfAnnotationFileAttachment ,*_a .PdfAnnotationSound ,*_a .PdfAnnotationMovie ,nil :_fgcba =append (_fgcba ,_bb ("\u0036.\u0035\u002e\u0032\u002d\u0031","\u0041\u006e\u006e\u006f\u0074\u0061\u0074\u0069\u006f\u006e\u0020\u0074\u0079\u0070\u0065\u0073\u0020\u006e\u006f\u0074 \u0064\u0065\u0066\u0069\u006e\u0065\u0064\u0020i\u006e\u0020\u0050\u0044\u0046\u0020\u0052\u0065\u0066\u0065\u0072\u0065\u006ec\u0065\u0020\u0073\u0068\u0061l\u006c\u0020\u006e\u006f\u0074 \u0062\u0065\u0020p\u0065\u0072m\u0069\u0074\u0074\u0065\u0064\u002e\u0020\u0041d\u0064\u0069\u0074\u0069\u006f\u006e\u0061\u006c\u006c\u0079\u002c\u0020\u0074\u0068\u0065\u0020F\u0069\u006c\u0065\u0041\u0074\u0074\u0061\u0063\u0068\u006de\u006e\u0074\u002c\u0020\u0053\u006f\u0075\u006e\u0064\u0020\u0061\u006e\u0064\u0020\u004d\u006f\u0076\u0069e\u0020\u0074\u0079\u0070\u0065s \u0073ha\u006c\u006c\u0020\u006eo\u0074\u0020\u0062\u0065\u0020\u0070\u0065\u0072\u006d\u0069\u0074\u0074\u0065\u0064\u002e"));
_fdcgd =true ;if _decef (){return _fgcba ;};};};_abcbb ,_gegg :=_ag .GetDict (_gcadd .GetContainingPdfObject ());if !_gegg {continue ;};if !_bdcdf {_cagc ,_gba :=_ag .GetFloatVal (_abcbb .Get ("\u0043\u0041"));if _gba &&_cagc !=1.0{_fgcba =append (_fgcba ,_bb ("\u0036.\u0035\u002e\u0033\u002d\u0031","\u0041\u006e\u0020\u0061\u006e\u006e\u006ft\u0061\u0074\u0069\u006f\u006e\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u0020\u0073h\u0061\u006c\u006c\u0020\u006eo\u0074\u0020\u0063\u006f\u006e\u0074\u0061\u0069\u006e \u0074\u0068\u0065\u0020\u0043\u0041\u0020\u006b\u0065\u0079\u0020\u0077\u0069\u0074\u0068\u0020\u0061\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u006f\u0074\u0068\u0065\u0072\u0020\u0074\u0068\u0061\u006e\u0020\u0031\u002e\u0030\u002e"));
_bdcdf =true ;if _decef (){return _fgcba ;};};};if !_gdage {_gbedg ,_cdfe :=_ag .GetIntVal (_abcbb .Get ("\u0046"));if !(_cdfe &&_gbedg &4==4&&_gbedg &1==0&&_gbedg &2==0&&_gbedg &32==0){_fgcba =append (_fgcba ,_bb ("\u0036.\u0035\u002e\u0033\u002d\u0032","\u0041\u006e\u0020\u0061\u006e\u006e\u006f\u0074\u0061\u0074i\u006f\u006e\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079 \u0073\u0068\u0061\u006c\u006c\u0020\u0063\u006f\u006e\u0074\u0061\u0069n\u0020\u0074\u0068\u0065\u0020\u0046\u0020\u006b\u0065\u0079\u002e\u0020\u0054\u0068\u0065\u0020\u0046\u0020\u006b\u0065\u0079\u0027\u0073\u0020\u0050\u0072\u0069\u006e\u0074\u0020\u0066\u006c\u0061\u0067\u0020\u0062\u0069\u0074\u0020\u0073h\u0061\u006c\u006c\u0020\u0062\u0065 s\u0065\u0074\u0020\u0074\u006f\u0020\u0031\u0020\u0061\u006e\u0064\u0020\u0069\u0074\u0073\u0020\u0048\u0069\u0064\u0064\u0065\u006e\u002c\u0020I\u006e\u0076\u0069\u0073\u0069\u0062\u006c\u0065\u0020\u0061\u006e\u0064\u0020\u004e\u006f\u0056\u0069\u0065\u0077\u0020\u0066\u006c\u0061\u0067\u0020b\u0069\u0074\u0073 \u0073\u0068\u0061\u006c\u006c\u0020\u0062\u0065\u0020\u0073e\u0074\u0020t\u006f\u0020\u0030\u002e"));
_gdage =true ;if _decef (){return _fgcba ;};};};if !_cdcf {_gfdbe ,_caafg :=_ag .GetDict (_abcbb .Get ("\u0041\u0050"));if _caafg {_dfgb :=_gfdbe .Get ("\u004e");if _dfgb ==nil ||len (_gfdbe .Keys ())> 1{_fgcba =append (_fgcba ,_bb ("\u0036.\u0035\u002e\u0033\u002d\u0034","\u0046\u006f\u0072\u0020\u0061\u006c\u006c\u0020\u0061\u006e\u006e\u006ft\u0061\u0074\u0069\u006f\u006e\u0020d\u0069\u0063t\u0069\u006f\u006ea\u0072\u0069\u0065\u0073 \u0063\u006f\u006e\u0074\u0061\u0069\u006e\u0069\u006e\u0067\u0020\u0061\u006e\u0020\u0041\u0050 \u006b\u0065\u0079\u002c\u0020\u0074\u0068\u0065\u0020\u0061p\u0070\u0065\u0061\u0072\u0061\u006e\u0063\u0065\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u0020\u0074\u0068\u0061\u0074\u0020\u0069\u0074\u0020\u0064\u0065\u0066\u0069\u006e\u0065\u0073\u0020\u0061\u0073\u0020it\u0073\u0020\u0076\u0061\u006cu\u0065\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0063\u006f\u006e\u0074\u0061i\u006e\u0020o\u006e\u006c\u0079\u0020\u0074\u0068\u0065\u0020\u004e\u0020\u006b\u0065\u0079\u002e\u0020\u0049\u0066\u0020\u0061\u006e\u0020\u0061\u006e\u006e\u006f\u0074\u0061\u0074\u0069\u006f\u006e\u0020\u0064i\u0063\u0074\u0069o\u006e\u0061\u0072\u0079\u0027\u0073\u0020\u0053\u0075\u0062ty\u0070\u0065\u0020\u006b\u0065\u0079\u0020\u0068\u0061\u0073\u0020\u0061\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u006f\u0066\u0020\u0057\u0069\u0064g\u0065\u0074\u0020\u0061\u006e\u0064\u0020\u0069\u0074s\u0020\u0046\u0054 \u006be\u0079\u0020\u0068\u0061\u0073\u0020\u0061\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u006f\u0066\u0020B\u0074\u006e,\u0020\u0074he \u0076a\u006c\u0075\u0065\u0020\u006f\u0066\u0020\u0074\u0068\u0065\u0020\u004e\u0020\u006b\u0065\u0079\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0062\u0065\u0020\u0061\u006e\u0020\u0061\u0070\u0070\u0065\u0061\u0072\u0061\u006e\u0063\u0065\u0020\u0073\u0075\u0062\u0064\u0069\u0063\u0074\u0069\u006fn\u0061r\u0079; \u006f\u0074\u0068\u0065\u0072\u0077\u0069s\u0065\u0020\u0074\u0068\u0065\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u006f\u0066\u0020th\u0065\u0020N\u0020\u006b\u0065y\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0062e\u0020\u0061\u006e\u0020\u0061\u0070\u0070\u0065\u0061\u0072\u0061n\u0063\u0065\u0020\u0073\u0074\u0072\u0065\u0061\u006d\u002e"));
_cdcf =true ;if _decef (){return _fgcba ;};continue ;};_ ,_gebe :=_gcadd .GetContext ().(*_a .PdfAnnotationWidget );if _gebe {_cacf ,_bbec :=_ag .GetName (_abcbb .Get ("\u0046\u0054"));if _bbec &&*_cacf =="\u0042\u0074\u006e"{if _ ,_gdae :=_ag .GetDict (_dfgb );
!_gdae {_fgcba =append (_fgcba ,_bb ("\u0036.\u0035\u002e\u0033\u002d\u0034","\u0046\u006f\u0072\u0020\u0061\u006c\u006c\u0020\u0061\u006e\u006e\u006ft\u0061\u0074\u0069\u006f\u006e\u0020d\u0069\u0063t\u0069\u006f\u006ea\u0072\u0069\u0065\u0073 \u0063\u006f\u006e\u0074\u0061\u0069\u006e\u0069\u006e\u0067\u0020\u0061\u006e\u0020\u0041\u0050 \u006b\u0065\u0079\u002c\u0020\u0074\u0068\u0065\u0020\u0061p\u0070\u0065\u0061\u0072\u0061\u006e\u0063\u0065\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u0020\u0074\u0068\u0061\u0074\u0020\u0069\u0074\u0020\u0064\u0065\u0066\u0069\u006e\u0065\u0073\u0020\u0061\u0073\u0020it\u0073\u0020\u0076\u0061\u006cu\u0065\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0063\u006f\u006e\u0074\u0061i\u006e\u0020o\u006e\u006c\u0079\u0020\u0074\u0068\u0065\u0020\u004e\u0020\u006b\u0065\u0079\u002e\u0020\u0049\u0066\u0020\u0061\u006e\u0020\u0061\u006e\u006e\u006f\u0074\u0061\u0074\u0069\u006f\u006e\u0020\u0064i\u0063\u0074\u0069o\u006e\u0061\u0072\u0079\u0027\u0073\u0020\u0053\u0075\u0062ty\u0070\u0065\u0020\u006b\u0065\u0079\u0020\u0068\u0061\u0073\u0020\u0061\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u006f\u0066\u0020\u0057\u0069\u0064g\u0065\u0074\u0020\u0061\u006e\u0064\u0020\u0069\u0074s\u0020\u0046\u0054 \u006be\u0079\u0020\u0068\u0061\u0073\u0020\u0061\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u006f\u0066\u0020B\u0074\u006e,\u0020\u0074he \u0076a\u006c\u0075\u0065\u0020\u006f\u0066\u0020\u0074\u0068\u0065\u0020\u004e\u0020\u006b\u0065\u0079\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0062\u0065\u0020\u0061\u006e\u0020\u0061\u0070\u0070\u0065\u0061\u0072\u0061\u006e\u0063\u0065\u0020\u0073\u0075\u0062\u0064\u0069\u0063\u0074\u0069\u006fn\u0061r\u0079; \u006f\u0074\u0068\u0065\u0072\u0077\u0069s\u0065\u0020\u0074\u0068\u0065\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u006f\u0066\u0020th\u0065\u0020N\u0020\u006b\u0065y\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0062e\u0020\u0061\u006e\u0020\u0061\u0070\u0070\u0065\u0061\u0072\u0061n\u0063\u0065\u0020\u0073\u0074\u0072\u0065\u0061\u006d\u002e"));
_cdcf =true ;if _decef (){return _fgcba ;};continue ;};};};_ ,_cecgd :=_ag .GetStream (_dfgb );if !_cecgd {_fgcba =append (_fgcba ,_bb ("\u0036.\u0035\u002e\u0033\u002d\u0034","\u0046\u006f\u0072\u0020\u0061\u006c\u006c\u0020\u0061\u006e\u006e\u006ft\u0061\u0074\u0069\u006f\u006e\u0020d\u0069\u0063t\u0069\u006f\u006ea\u0072\u0069\u0065\u0073 \u0063\u006f\u006e\u0074\u0061\u0069\u006e\u0069\u006e\u0067\u0020\u0061\u006e\u0020\u0041\u0050 \u006b\u0065\u0079\u002c\u0020\u0074\u0068\u0065\u0020\u0061p\u0070\u0065\u0061\u0072\u0061\u006e\u0063\u0065\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u0020\u0074\u0068\u0061\u0074\u0020\u0069\u0074\u0020\u0064\u0065\u0066\u0069\u006e\u0065\u0073\u0020\u0061\u0073\u0020it\u0073\u0020\u0076\u0061\u006cu\u0065\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0063\u006f\u006e\u0074\u0061i\u006e\u0020o\u006e\u006c\u0079\u0020\u0074\u0068\u0065\u0020\u004e\u0020\u006b\u0065\u0079\u002e\u0020\u0049\u0066\u0020\u0061\u006e\u0020\u0061\u006e\u006e\u006f\u0074\u0061\u0074\u0069\u006f\u006e\u0020\u0064i\u0063\u0074\u0069o\u006e\u0061\u0072\u0079\u0027\u0073\u0020\u0053\u0075\u0062ty\u0070\u0065\u0020\u006b\u0065\u0079\u0020\u0068\u0061\u0073\u0020\u0061\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u006f\u0066\u0020\u0057\u0069\u0064g\u0065\u0074\u0020\u0061\u006e\u0064\u0020\u0069\u0074s\u0020\u0046\u0054 \u006be\u0079\u0020\u0068\u0061\u0073\u0020\u0061\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u006f\u0066\u0020B\u0074\u006e,\u0020\u0074he \u0076a\u006c\u0075\u0065\u0020\u006f\u0066\u0020\u0074\u0068\u0065\u0020\u004e\u0020\u006b\u0065\u0079\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0062\u0065\u0020\u0061\u006e\u0020\u0061\u0070\u0070\u0065\u0061\u0072\u0061\u006e\u0063\u0065\u0020\u0073\u0075\u0062\u0064\u0069\u0063\u0074\u0069\u006fn\u0061r\u0079; \u006f\u0074\u0068\u0065\u0072\u0077\u0069s\u0065\u0020\u0074\u0068\u0065\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u006f\u0066\u0020th\u0065\u0020N\u0020\u006b\u0065y\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0062e\u0020\u0061\u006e\u0020\u0061\u0070\u0070\u0065\u0061\u0072\u0061n\u0063\u0065\u0020\u0073\u0074\u0072\u0065\u0061\u006d\u002e"));
_cdcf =true ;if _decef (){return _fgcba ;};continue ;};};};if !_ddbga {if _abcbb .Get ("\u0043")!=nil ||_abcbb .Get ("\u0049\u0043")!=nil {_afcag ,_cgdf :=_cgaba (_bdbf );if !_cgdf {_fgcba =append (_fgcba ,_bb ("\u0036.\u0035\u002e\u0033\u002d\u0033","\u0041\u006e\u0020\u0061\u006e\u006e\u006f\u0074\u0061\u0074\u0069\u006f\u006e\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006ea\u0072\u0079\u0020\u0073\u0068\u0061l\u006c\u0020\u006e\u006f\u0074\u0020\u0063\u006fn\u0074a\u0069\u006e\u0020t\u0068e\u0020\u0043\u0020\u0061\u0072\u0072\u0061\u0079\u0020\u006f\u0072\u0020\u0074\u0068e\u0020\u0049\u0043\u0020\u0061\u0072\u0072\u0061\u0079\u0020\u0075\u006e\u006c\u0065\u0073\u0073\u0020\u0074\u0068\u0065\u0020\u0063o\u006c\u006f\u0072\u0020\u0073\u0070\u0061\u0063\u0065\u0020\u006f\u0066\u0020\u0074\u0068\u0065\u0020\u0044\u0065\u0073\u0074\u004f\u0075\u0074\u0070\u0075\u0074\u0050\u0072\u006ff\u0069\u006ce\u0020\u0069\u006e\u0020\u0074h\u0065\u0020\u0050\u0044\u0046\u002f\u0041\u002d\u0031\u0020\u004f\u0075\u0074\u0070\u0075\u0074\u0049\u006e\u0074\u0065\u006e\u0074\u0020\u0064\u0069\u0063t\u0069\u006f\u006e\u0061\u0072\u0079\u002c\u0020\u0064\u0065\u0066\u0069\u006e\u0065\u0064\u0020\u0069n\u0020\u0036\u002e\u0032\u002e2\u002c\u0020\u0069\u0073\u0020\u0052\u0047\u0042."));
_ddbga =true ;if _decef (){return _fgcba ;};}else {_gbbd ,_cbeee :=_ag .GetIntVal (_afcag .Get ("\u004e"));if !_cbeee ||_gbbd !=3{_fgcba =append (_fgcba ,_bb ("\u0036.\u0035\u002e\u0033\u002d\u0033","\u0041\u006e\u0020\u0061\u006e\u006e\u006f\u0074\u0061\u0074\u0069\u006f\u006e\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006ea\u0072\u0079\u0020\u0073\u0068\u0061l\u006c\u0020\u006e\u006f\u0074\u0020\u0063\u006fn\u0074a\u0069\u006e\u0020t\u0068e\u0020\u0043\u0020\u0061\u0072\u0072\u0061\u0079\u0020\u006f\u0072\u0020\u0074\u0068e\u0020\u0049\u0043\u0020\u0061\u0072\u0072\u0061\u0079\u0020\u0075\u006e\u006c\u0065\u0073\u0073\u0020\u0074\u0068\u0065\u0020\u0063o\u006c\u006f\u0072\u0020\u0073\u0070\u0061\u0063\u0065\u0020\u006f\u0066\u0020\u0074\u0068\u0065\u0020\u0044\u0065\u0073\u0074\u004f\u0075\u0074\u0070\u0075\u0074\u0050\u0072\u006ff\u0069\u006ce\u0020\u0069\u006e\u0020\u0074h\u0065\u0020\u0050\u0044\u0046\u002f\u0041\u002d\u0031\u0020\u004f\u0075\u0074\u0070\u0075\u0074\u0049\u006e\u0074\u0065\u006e\u0074\u0020\u0064\u0069\u0063t\u0069\u006f\u006e\u0061\u0072\u0079\u002c\u0020\u0064\u0065\u0066\u0069\u006e\u0065\u0064\u0020\u0069n\u0020\u0036\u002e\u0032\u002e2\u002c\u0020\u0069\u0073\u0020\u0052\u0047\u0042."));
_ddbga =true ;if _decef (){return _fgcba ;};};};};};_acec ,_bgfbf :=_gcadd .GetContext ().(*_a .PdfAnnotationWidget );if !_bgfbf {continue ;};if !_ddee {if _acec .A !=nil {_fgcba =append (_fgcba ,_bb ("\u0036.\u0036\u002e\u0031\u002d\u0033","A \u0057\u0069d\u0067\u0065\u0074\u0020\u0061\u006e\u006e\u006f\u0074a\u0074\u0069\u006f\u006e\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u006e\u006f\u0074\u0020\u0069\u006ec\u006cu\u0064\u0065\u0020\u0061\u006e\u0020\u0041\u0020e\u006et\u0072\u0079."));
_ddee =true ;if _decef (){return _fgcba ;};};};if !_ccbe {if _acec .AA !=nil {_fgcba =append (_fgcba ,_bb ("\u0036.\u0036\u002e\u0032\u002d\u0031","\u0041\u0020\u0057\u0069\u0064\u0067\u0065\u0074\u0020\u0061\u006e\u006eo\u0074\u0061\u0074i\u006f\u006e\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061r\u0079\u0020\u0073h\u0061\u006c\u006c\u0020n\u006f\u0074\u0020\u0069\u006e\u0063\u006c\u0075\u0064\u0065\u0020\u0061\u006e\u0020\u0041\u0041\u0020\u0065\u006e\u0074\u0072\u0079\u0020\u0066\u006f\u0072\u0020\u0061\u006e\u0020\u0061d\u0064\u0069\u0074\u0069\u006f\u006e\u0061\u006c\u002d\u0061\u0063t\u0069\u006f\u006e\u0073\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u002e"));
_ccbe =true ;if _decef (){return _fgcba ;};};};};};return _fgcba ;};

// Profile1Options are the options that changes the way how optimizer may try to adapt document into PDF/A standard.
type Profile1Options struct{

// CMYKDefaultColorSpace is an option that refers PDF/A-1
CMYKDefaultColorSpace bool ;

// Now is a function that returns current time.
Now func ()_e .Time ;

// Xmp is the xmp options information.
Xmp XmpOptions ;};func _dcbac (_dbcbg *_a .CompliancePdfReader )[]ViolatedRule {return nil };type documentColorspaceOptimizeFunc func (_bfbe *_gc .Document ,_bac []*_gc .Image )error ;

// ApplyStandard tries to change the content of the writer to match the PDF/A-3 standard.
// Implements model.StandardApplier.
func (_ddfag *profile3 )ApplyStandard (document *_gc .Document )(_adgd error ){_dgc (document ,7);if _adgd =_eec (document ,_ddfag ._gae .Now );_adgd !=nil {return _adgd ;};if _adgd =_cea (document );_adgd !=nil {return _adgd ;};_geee ,_gcbg :=_adf (_ddfag ._gae .CMYKDefaultColorSpace ,_ddfag ._gfdf );
_adgd =_eegf (document ,[]pageColorspaceOptimizeFunc {_geee },[]documentColorspaceOptimizeFunc {_gcbg });if _adgd !=nil {return _adgd ;};_dge (document );if _adgd =_bedf (document );_adgd !=nil {return _adgd ;};if _adgd =_fgf (document ,_ddfag ._gfdf ._dg );
_adgd !=nil {return _adgd ;};if _adgd =_cbda (document );_adgd !=nil {return _adgd ;};if _adgd =_aaeg (document );_adgd !=nil {return _adgd ;};if _adgd =_ggf (document );_adgd !=nil {return _adgd ;};if _adgd =_bce (document );_adgd !=nil {return _adgd ;
};if _ddfag ._gfdf ._db =="\u0041"{_agdd (document );};if _adgd =_bcag (document ,_ddfag ._gfdf ._dg );_adgd !=nil {return _adgd ;};if _adgd =_bfga (document );_adgd !=nil {return _adgd ;};if _fbgc :=_gcfb (document ,_ddfag ._gfdf ,_ddfag ._gae .Xmp );
_fbgc !=nil {return _fbgc ;};if _ddfag ._gfdf ==_dc (){if _adgd =_gcdd (document );_adgd !=nil {return _adgd ;};};if _adgd =_dcbg (document );_adgd !=nil {return _adgd ;};if _adgd =_aef (document );_adgd !=nil {return _adgd ;};if _adgd =_cgcf (document );
_adgd !=nil {return _adgd ;};return nil ;};

// Profile1A is the implementation of the PDF/A-1A standard profile.
// Implements model.StandardImplementer, Profile interfaces.
type Profile1A struct{profile1 };func _gbcb (_bfacg *_a .CompliancePdfReader )(_agcc []ViolatedRule ){var (_bbbc ,_degd ,_dagd ,_fgdg ,_aefd ,_bbcc ,_gadac bool ;_fgff func (_ag .PdfObject ););_fgff =func (_fcfeb _ag .PdfObject ){switch _bgdg :=_fcfeb .(type ){case *_ag .PdfObjectInteger :if !_bbbc &&(int64 (*_bgdg )> _c .MaxInt32 ||int64 (*_bgdg )< -_c .MaxInt32 ){_agcc =append (_agcc ,_bb ("\u0036\u002e\u0031\u002e\u0031\u0032\u002d\u0031","L\u0061\u0072\u0067e\u0073\u0074\u0020\u0049\u006e\u0074\u0065\u0067\u0065\u0072\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u0069\u0073\u0020\u0032\u002c\u0031\u0034\u0037,\u0034\u0038\u0033,\u0036\u0034\u0037\u002e\u0020\u0053\u006d\u0061\u006c\u006c\u0065\u0073\u0074 \u0069\u006e\u0074\u0065g\u0065\u0072\u0020\u0076a\u006c\u0075\u0065\u0020\u0069\u0073\u0020\u002d\u0032\u002c\u0031\u0034\u0037\u002c\u0034\u0038\u0033,\u0036\u0034\u0038\u002e"));
_bbbc =true ;};case *_ag .PdfObjectFloat :if !_degd &&(_c .Abs (float64 (*_bgdg ))> 32767.0){_agcc =append (_agcc ,_bb ("\u0036\u002e\u0031\u002e\u0031\u0032\u002d\u0032","\u0041\u0062\u0073\u006f\u006c\u0075\u0074\u0065\u0020\u0072\u0065\u0061\u006c\u0020\u0076\u0061\u006c\u0075\u0065\u0020m\u0075\u0073\u0074\u0020\u0062\u0065\u0020\u006c\u0065s\u0073\u0020\u0074\u0068\u0061\u006e\u0020\u006f\u0072\u0020\u0065\u0071\u0075a\u006c\u0020\u0074\u006f\u0020\u00332\u0037\u0036\u0037.\u0030\u002e"));
};case *_ag .PdfObjectString :if !_dagd &&len ([]byte (_bgdg .Str ()))> 65535{_agcc =append (_agcc ,_bb ("\u0036\u002e\u0031\u002e\u0031\u0032\u002d\u0033","M\u0061\u0078\u0069\u006d\u0075\u006d\u0020\u006c\u0065n\u0067\u0074\u0068\u0020\u006f\u0066\u0020a \u0073\u0074\u0072\u0069n\u0067\u0020\u0028\u0069\u006e\u0020\u0062\u0079\u0074es\u0029\u0020i\u0073\u0020\u0036\u0035\u0035\u0033\u0035\u002e"));
_dagd =true ;};case *_ag .PdfObjectName :if !_fgdg &&len ([]byte (*_bgdg ))> 127{_agcc =append (_agcc ,_bb ("\u0036\u002e\u0031\u002e\u0031\u0032\u002d\u0034","\u004d\u0061\u0078\u0069\u006d\u0075\u006d \u006c\u0065\u006eg\u0074\u0068\u0020\u006ff\u0020\u0061\u0020\u006e\u0061\u006d\u0065\u0020\u0028\u0069\u006e\u0020\u0062\u0079\u0074\u0065\u0073\u0029\u0020\u0069\u0073\u0020\u0031\u0032\u0037\u002e"));
_fgdg =true ;};case *_ag .PdfObjectArray :if !_aefd &&_bgdg .Len ()> 8191{_agcc =append (_agcc ,_bb ("\u0036\u002e\u0031\u002e\u0031\u0032\u002d\u0035","\u004d\u0061\u0078\u0069\u006d\u0075m\u0020\u006c\u0065\u006e\u0067\u0074\u0068\u0020\u006f\u0066\u0020\u0061\u006e\u0020\u0061\u0072\u0072\u0061\u0079\u0020(\u0069\u006e\u0020\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0073\u0029\u0020\u0069s\u00208\u0031\u0039\u0031\u002e"));
_aefd =true ;};for _ ,_cbae :=range _bgdg .Elements (){_fgff (_cbae );};if !_gadac &&(_bgdg .Len ()==4||_bgdg .Len ()==5){_abecb ,_addad :=_ag .GetName (_bgdg .Get (0));if !_addad {return ;};if *_abecb !="\u0044e\u0076\u0069\u0063\u0065\u004e"{return ;
};_bgbe :=_bgdg .Get (1);_bgbe =_ag .TraceToDirectObject (_bgbe );_edfcb ,_addad :=_ag .GetArray (_bgbe );if !_addad {return ;};if _edfcb .Len ()> 8{_agcc =append (_agcc ,_bb ("\u0036\u002e\u0031\u002e\u0031\u0032\u002d\u0039","\u004d\u0061\u0078i\u006d\u0075\u006d\u0020\u006e\u0075\u006d\u0062\u0065\u0072\u0020\u006f\u0066\u0020\u0044\u0065\u0076\u0069\u0063\u0065\u004e\u0020\u0063\u006f\u006d\u0070\u006f\u006e\u0065n\u0074\u0073\u0020\u0069\u0073\u0020\u0038\u002e"));
_gadac =true ;};};case *_ag .PdfObjectDictionary :_baba :=_bgdg .Keys ();if !_bbcc &&len (_baba )> 4095{_agcc =append (_agcc ,_bb ("\u0036.\u0031\u002e\u0031\u0032\u002d\u00311","\u004d\u0061\u0078\u0069\u006d\u0075\u006d\u0020\u0063\u0061\u0070\u0061\u0063\u0069\u0074y\u0020\u006f\u0066\u0020\u0061\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006ea\u0072\u0079\u0020\u0028\u0069\u006e\u0020\u0065\u006e\u0074\u0072\u0069es\u0029\u0020\u0069\u0073\u0020\u0034\u0030\u0039\u0035\u002e"));
_bbcc =true ;};for _adgb ,_eaac :=range _baba {_fgff (&_baba [_adgb ]);_fgff (_bgdg .Get (_eaac ));};case *_ag .PdfObjectStream :_fgff (_bgdg .PdfObjectDictionary );case *_ag .PdfObjectStreams :for _ ,_edad :=range _bgdg .Elements (){_fgff (_edad );};case *_ag .PdfObjectReference :_fgff (_bgdg .Resolve ());
};};_aab :=_bfacg .GetObjectNums ();if len (_aab )> 8388607{_agcc =append (_agcc ,_bb ("\u0036\u002e\u0031\u002e\u0031\u0032\u002d\u0037","\u004d\u0061\u0078\u0069\u006d\u0075\u006d\u0020\u006e\u0075\u006d\u0062\u0065\u0072\u0020\u006f\u0066\u0020in\u0064i\u0072\u0065\u0063\u0074\u0020\u006f\u0062\u006a\u0065\u0063\u0074\u0073 \u0069\u006e\u0020\u0061\u0020\u0050\u0044\u0046\u0020\u0066\u0069\u006c\u0065\u0020\u0069\u0073\u00208\u002c\u0033\u0038\u0038\u002c\u0036\u0030\u0037\u002e"));
};for _ ,_eeda :=range _aab {_ecba ,_bgde :=_bfacg .GetIndirectObjectByNumber (_eeda );if _bgde !=nil {continue ;};_eaaf :=_ag .TraceToDirectObject (_ecba );_fgff (_eaaf );};return _agcc ;};func _dcceg (_gcfca *_a .CompliancePdfReader )ViolatedRule {for _ ,_aggf :=range _gcfca .PageList {_caf :=_aggf .GetContentStreamObjs ();
for _ ,_efgd :=range _caf {_efgd =_ag .TraceToDirectObject (_efgd );var _adcb string ;switch _bfgd :=_efgd .(type ){case *_ag .PdfObjectString :_adcb =_bfgd .Str ();case *_ag .PdfObjectStream :_gbgb ,_ccad :=_ag .GetName (_ag .TraceToDirectObject (_bfgd .Get ("\u0046\u0069\u006c\u0074\u0065\u0072")));
if _ccad {if *_gbgb ==_ag .StreamEncodingFilterNameLZW {return _bb ("\u0036\u002e\u0031\u002e\u0031\u0030\u002d\u0032","\u0054h\u0065\u0020L\u005a\u0057\u0044\u0065c\u006f\u0064\u0065 \u0066\u0069\u006c\u0074\u0065\u0072\u0020\u0073\u0068al\u006c\u0020\u006eo\u0074\u0020b\u0065\u0020\u0070\u0065\u0072\u006di\u0074\u0074e\u0064\u002e");
};};_aegd ,_fadg :=_ag .DecodeStream (_bfgd );if _fadg !=nil {_g .Log .Debug ("\u0045r\u0072\u003a\u0020\u0025\u0076",_fadg );continue ;};_adcb =string (_aegd );default:_g .Log .Debug ("\u0049\u006e\u0076\u0061\u006c\u0069d\u0020\u0063\u006f\u006e\u0074\u0065\u006e\u0074\u0020\u0073\u0074\u0072\u0065a\u006d\u0020\u006f\u0062\u006a\u0065\u0063t\u003a\u0020\u0025\u0054",_efgd );
continue ;};_gegca :=_fbd .NewContentStreamParser (_adcb );_ggfb ,_adcee :=_gegca .Parse ();if _adcee !=nil {_g .Log .Debug ("\u0049\u006e\u0076\u0061\u006c\u0069\u0064\u0020\u0063\u006f\u006et\u0065\u006e\u0074\u0020\u0073\u0074\u0072\u0065\u0061\u006d:\u0020\u0025\u0076",_adcee );
continue ;};for _ ,_cffc :=range *_ggfb {if !(_cffc .Operand =="\u0042\u0049"&&len (_cffc .Params )==1){continue ;};_edce ,_dbcc :=_cffc .Params [0].(*_fbd .ContentStreamInlineImage );if !_dbcc {continue ;};_gada ,_cgde :=_edce .GetEncoder ();if _cgde !=nil {_g .Log .Debug ("\u0067\u0065\u0074\u0074\u0069\u006e\u0067\u0020\u0069\u006e\u006c\u0069\u006ee\u0020\u0069\u006d\u0061\u0067\u0065 \u0065\u006e\u0063\u006f\u0064\u0065\u0072\u0020\u0066\u0061\u0069\u006c\u0065d\u003a\u0020\u0025\u0076",_cgde );
continue ;};if _gada .GetFilterName ()==_ag .StreamEncodingFilterNameLZW {return _bb ("\u0036\u002e\u0031\u002e\u0031\u0030\u002d\u0032","\u0054h\u0065\u0020L\u005a\u0057\u0044\u0065c\u006f\u0064\u0065 \u0066\u0069\u006c\u0074\u0065\u0072\u0020\u0073\u0068al\u006c\u0020\u006eo\u0074\u0020b\u0065\u0020\u0070\u0065\u0072\u006di\u0074\u0074e\u0064\u002e");
};};};};return _eag ;};func _fdbg (_gdbf *_a .CompliancePdfReader )(_deecb ViolatedRule ){for _ ,_dbge :=range _gdbf .GetObjectNums (){_fdbe ,_eecc :=_gdbf .GetIndirectObjectByNumber (_dbge );if _eecc !=nil {continue ;};_fdcfe ,_ebadd :=_ag .GetStream (_fdbe );
if !_ebadd {continue ;};_egcd ,_ebadd :=_ag .GetName (_fdcfe .Get ("\u0054\u0079\u0070\u0065"));if !_ebadd {continue ;};if *_egcd !="\u0058O\u0062\u006a\u0065\u0063\u0074"{continue ;};_ ,_ebadd =_ag .GetName (_fdcfe .Get ("\u004f\u0050\u0049"));if _ebadd {return _bb ("\u0036.\u0032\u002e\u0039\u002d\u0031","\u0041\u0020\u0066\u006f\u0072m\u0020\u0058\u004f\u0062\u006a\u0065c\u0074\u0020\u0064i\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u006e\u006ft\u0020\u0063\u006f\u006e\u0074\u0061\u0069n\u0020\u0061\u006e\u0079\u0020\u006f\u0066\u0020\u0074\u0068\u0065\u0020\u0066\u006f\u006c\u006c\u006f\u0077\u0069\u006e\u0067\u003a \u002d\u0020\u0074\u0068\u0065\u0020O\u0050\u0049\u0020\u006b\u0065\u0079\u003b \u002d\u0020\u0074\u0068e \u0053u\u0062\u0074\u0079\u0070\u0065\u0032 ke\u0079 \u0077\u0069t\u0068\u0020\u0061\u0020\u0076\u0061l\u0075\u0065\u0020\u006f\u0066\u0020\u0050\u0053\u003b\u0020\u002d \u0074\u0068\u0065\u0020\u0050\u0053\u0020\u006b\u0065\u0079\u002e");
};_gefab ,_ebadd :=_ag .GetName (_fdcfe .Get ("\u0053\u0075\u0062\u0074\u0079\u0070\u0065\u0032"));if !_ebadd {continue ;};if *_gefab =="\u0050\u0053"{return _bb ("\u0036.\u0032\u002e\u0039\u002d\u0031","\u0041\u0020\u0066\u006f\u0072m\u0020\u0058\u004f\u0062\u006a\u0065c\u0074\u0020\u0064i\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u006e\u006ft\u0020\u0063\u006f\u006e\u0074\u0061\u0069n\u0020\u0061\u006e\u0079\u0020\u006f\u0066\u0020\u0074\u0068\u0065\u0020\u0066\u006f\u006c\u006c\u006f\u0077\u0069\u006e\u0067\u003a \u002d\u0020\u0074\u0068\u0065\u0020O\u0050\u0049\u0020\u006b\u0065\u0079\u003b \u002d\u0020\u0074\u0068e \u0053u\u0062\u0074\u0079\u0070\u0065\u0032 ke\u0079 \u0077\u0069t\u0068\u0020\u0061\u0020\u0076\u0061l\u0075\u0065\u0020\u006f\u0066\u0020\u0050\u0053\u003b\u0020\u002d \u0074\u0068\u0065\u0020\u0050\u0053\u0020\u006b\u0065\u0079\u002e");
};if _fdcfe .Get ("\u0050\u0053")!=nil {return _bb ("\u0036.\u0032\u002e\u0039\u002d\u0031","\u0041\u0020\u0066\u006f\u0072m\u0020\u0058\u004f\u0062\u006a\u0065c\u0074\u0020\u0064i\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u006e\u006ft\u0020\u0063\u006f\u006e\u0074\u0061\u0069n\u0020\u0061\u006e\u0079\u0020\u006f\u0066\u0020\u0074\u0068\u0065\u0020\u0066\u006f\u006c\u006c\u006f\u0077\u0069\u006e\u0067\u003a \u002d\u0020\u0074\u0068\u0065\u0020O\u0050\u0049\u0020\u006b\u0065\u0079\u003b \u002d\u0020\u0074\u0068e \u0053u\u0062\u0074\u0079\u0070\u0065\u0032 ke\u0079 \u0077\u0069t\u0068\u0020\u0061\u0020\u0076\u0061l\u0075\u0065\u0020\u006f\u0066\u0020\u0050\u0053\u003b\u0020\u002d \u0074\u0068\u0065\u0020\u0050\u0053\u0020\u006b\u0065\u0079\u002e");
};};return _deecb ;};

// NewProfile1B creates a new Profile1B with the given options.
func NewProfile1B (options *Profile1Options )*Profile1B {if options ==nil {options =DefaultProfile1Options ();};_bgfa (options );return &Profile1B {profile1 {_fcgb :*options ,_acab :_ga ()}};};func _dgcb (_bcfc *_a .CompliancePdfReader )(_cdfacd ViolatedRule ){_bfdc ,_egca :=_gfcb (_bcfc );
if !_egca {return _eag ;};_fgba ,_egca :=_ag .GetDict (_bfdc .Get ("\u0041\u0063\u0072\u006f\u0046\u006f\u0072\u006d"));if !_egca {return _eag ;};_dfabb ,_egca :=_ag .GetArray (_fgba .Get ("\u0046\u0069\u0065\u006c\u0064\u0073"));if !_egca {return _eag ;
};for _bedcd :=0;_bedcd < _dfabb .Len ();_bedcd ++{_aacc ,_aaeeb :=_ag .GetDict (_dfabb .Get (_bedcd ));if !_aaeeb {continue ;};if _aacc .Get ("\u0041")!=nil {return _bb ("\u0036.\u0034\u002e\u0031\u002d\u0032","\u0041\u0020\u0046\u0069\u0065\u006c\u0064\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006ea\u0072\u0079\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u006e\u006f\u0074\u0020c\u006f\u006e\u0074\u0061\u0069\u006e\u0020\u0074\u0068\u0065\u0020\u0041 o\u0072\u0020\u0041\u0041\u0020\u006b\u0065\u0079\u0073\u002e");
};if _aacc .Get ("\u0041\u0041")!=nil {return _bb ("\u0036.\u0034\u002e\u0031\u002d\u0032","\u0041\u0020\u0046\u0069\u0065\u006c\u0064\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006ea\u0072\u0079\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u006e\u006f\u0074\u0020c\u006f\u006e\u0074\u0061\u0069\u006e\u0020\u0074\u0068\u0065\u0020\u0041 o\u0072\u0020\u0041\u0041\u0020\u006b\u0065\u0079\u0073\u002e");
};};return _eag ;};func _fbdf (_gfg *_gc .Document ,_eggg bool )error {_cbb ,_ceag :=_gfg .GetPages ();if !_ceag {return nil ;};for _ ,_gbf :=range _cbb {_cddb ,_ebebd :=_ag .GetArray (_gbf .Object .Get ("\u0041\u006e\u006e\u006f\u0074\u0073"));if !_ebebd {continue ;
};for _ ,_gff :=range _cddb .Elements (){_cegg ,_cfcb :=_ag .GetDict (_gff );if !_cfcb {continue ;};_fcf :=_cegg .Get ("\u0043");if _fcf ==nil {continue ;};_bda ,_cfcb :=_ag .GetArray (_fcf );if !_cfcb {continue ;};_edb ,_bfd :=_bda .GetAsFloat64Slice ();
if _bfd !=nil {return _bfd ;};switch _bda .Len (){case 0,1:if _eggg {_cegg .Set ("\u0043",_ag .MakeArrayFromIntegers ([]int {1,1,1,1}));}else {_cegg .Set ("\u0043",_ag .MakeArrayFromIntegers ([]int {1,1,1}));};case 3:if _eggg {_eca ,_fea ,_fab ,_eccg :=_fa .RGBToCMYK (uint8 (_edb [0]*255),uint8 (_edb [1]*255),uint8 (_edb [2]*255));
_cegg .Set ("\u0043",_ag .MakeArrayFromFloats ([]float64 {float64 (_eca )/255,float64 (_fea )/255,float64 (_fab )/255,float64 (_eccg )/255}));};case 4:if !_eggg {_dab ,_bfag ,_gfeg :=_fa .CMYKToRGB (uint8 (_edb [0]*255),uint8 (_edb [1]*255),uint8 (_edb [2]*255),uint8 (_edb [3]*255));
_cegg .Set ("\u0043",_ag .MakeArrayFromFloats ([]float64 {float64 (_dab )/255,float64 (_bfag )/255,float64 (_gfeg )/255}));};};};};return nil ;};func _dfda (_dbdd *_a .CompliancePdfReader )(_ddgge []ViolatedRule ){var _gbegg ,_dcga ,_acea ,_cbbf ,_cafe ,_cafed bool ;
_cfggd :=func ()bool {return _gbegg &&_dcga &&_acea &&_cbbf &&_cafe &&_cafed };_dceb :=func (_efgfd *_ag .PdfObjectDictionary )bool {if !_gbegg &&_efgfd .Get ("\u0054\u0052")!=nil {_gbegg =true ;_ddgge =append (_ddgge ,_bb ("\u0036.\u0032\u002e\u0038\u002d\u0031","\u0041\u006e\u0020\u0045\u0078\u0074\u0047\u0053\u0074\u0061\u0074e\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006ea\u0072y\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u006e\u006f\u0074\u0020\u0063\u006f\u006e\u0074\u0061\u0069\u006e \u0074\u0068\u0065\u0020\u0054\u0052\u0020\u006b\u0065\u0079\u002e"));
};if _dfga :=_efgfd .Get ("\u0054\u0052\u0032");!_dcga &&_dfga !=nil {_gggad ,_caagg :=_ag .GetName (_dfga );if !_caagg ||(_caagg &&*_gggad !="\u0044e\u0066\u0061\u0075\u006c\u0074"){_dcga =true ;_ddgge =append (_ddgge ,_bb ("\u0036.\u0032\u002e\u0038\u002d\u0032","\u0041\u006e \u0045\u0078\u0074G\u0053\u0074\u0061\u0074\u0065 \u0064\u0069\u0063\u0074\u0069on\u0061\u0072\u0079\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u006e\u006f\u0074\u0020\u0063\u006f\u006e\u0074a\u0069n\u0020\u0074\u0068\u0065\u0020\u0054R2 \u006b\u0065\u0079\u0020\u0077\u0069\u0074\u0068\u0020\u0061\u0020\u0076al\u0075e\u0020\u006f\u0074\u0068e\u0072 \u0074h\u0061\u006e \u0044\u0065fa\u0075\u006c\u0074\u002e"));
if _cfggd (){return true ;};};};if _gffcc :=_efgfd .Get ("\u0053\u004d\u0061s\u006b");!_acea &&_gffcc !=nil {_bdbdbd ,_cgffb :=_ag .GetName (_gffcc );if !_cgffb ||(_cgffb &&*_bdbdbd !="\u004e\u006f\u006e\u0065"){_acea =true ;_ddgge =append (_ddgge ,_bb ("\u0036\u002e\u0034-\u0031","\u0049\u0066\u0020\u0061\u006e \u0053\u004d\u0061\u0073\u006b\u0020\u006be\u0079\u0020\u0061\u0070\u0070\u0065\u0061\u0072\u0073\u0020\u0069\u006e\u0020\u0061\u006e\u0020\u0045\u0078\u0074\u0047\u0053\u0074\u0061\u0074\u0065\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u002c\u0020\u0069\u0074s\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u0073\u0068\u0061\u006c\u006c \u0062\u0065\u0020\u004e\u006f\u006ee\u002e"));
if _cfggd (){return true ;};};};if _acge :=_efgfd .Get ("\u0043\u0041");!_cafe &&_acge !=nil {_bcd ,_cabg :=_ag .GetNumberAsFloat (_acge );if _cabg ==nil &&_bcd !=1.0{_cafe =true ;_ddgge =append (_ddgge ,_bb ("\u0036\u002e\u0034-\u0035","\u0054\u0068\u0065\u0020\u0066ol\u006c\u006fw\u0069\u006e\u0067\u0020\u006b\u0065\u0079\u0073\u002c\u0020\u0069\u0066\u0020\u0070\u0072\u0065\u0073\u0065\u006e\u0074\u0020\u0069\u006e\u0020\u0061\u006e\u0020\u0045\u0078t\u0047\u0053\u0074a\u0074\u0065\u0020\u006f\u0062\u006a\u0065\u0063\u0074\u002c\u0020\u0073\u0068a\u006c\u006c\u0020\u0068\u0061v\u0065\u0020\u0074\u0068\u0065\u0020\u0076\u0061\u006cu\u0065\u0073 \u0073h\u006f\u0077\u006e\u003a\u0020\u0043\u0041 \u002d\u0020\u0031\u002e\u0030\u002e"));
if _cfggd (){return true ;};};};if _dgdf :=_efgfd .Get ("\u0063\u0061");!_cafed &&_dgdf !=nil {_adeag ,_egddb :=_ag .GetNumberAsFloat (_dgdf );if _egddb ==nil &&_adeag !=1.0{_cafed =true ;_ddgge =append (_ddgge ,_bb ("\u0036\u002e\u0034-\u0036","\u0054\u0068\u0065\u0020\u0066ol\u006c\u006fw\u0069\u006e\u0067\u0020\u006b\u0065\u0079\u0073\u002c\u0020\u0069\u0066\u0020\u0070\u0072\u0065\u0073\u0065\u006e\u0074\u0020\u0069\u006e\u0020\u0061\u006e\u0020\u0045\u0078t\u0047\u0053\u0074a\u0074\u0065\u0020\u006f\u0062\u006a\u0065\u0063\u0074\u002c\u0020\u0073\u0068a\u006c\u006c\u0020\u0068\u0061v\u0065\u0020\u0074\u0068\u0065\u0020\u0076\u0061\u006cu\u0065\u0073 \u0073h\u006f\u0077\u006e\u003a\u0020\u0063\u0061 \u002d\u0020\u0031\u002e\u0030\u002e"));
if _cfggd (){return true ;};};};if _edae :=_efgfd .Get ("\u0042\u004d");!_cbbf &&_edae !=nil {_fadb ,_gfcf :=_ag .GetName (_edae );if _gfcf {switch _fadb .String (){case "\u004e\u006f\u0072\u006d\u0061\u006c","\u0043\u006f\u006d\u0070\u0061\u0074\u0069\u0062\u006c\u0065":default:_cbbf =true ;
_ddgge =append (_ddgge ,_bb ("\u0036\u002e\u0034-\u0034","T\u0068\u0065\u0020\u0066\u006f\u006cl\u006f\u0077\u0069\u006e\u0067 \u006b\u0065y\u0073\u002c\u0020\u0069\u0066 \u0070res\u0065\u006e\u0074\u0020\u0069\u006e\u0020\u0061\u006e\u0020\u0045\u0078\u0074\u0047S\u0074\u0061t\u0065\u0020\u006f\u0062\u006a\u0065\u0063\u0074\u002c\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0068\u0061\u0076\u0065 \u0074\u0068\u0065 \u0076\u0061\u006c\u0075\u0065\u0073\u0020\u0073\u0068\u006f\u0077n\u003a\u0020\u0042\u004d\u0020\u002d\u0020\u004e\u006f\u0072m\u0061\u006c\u0020\u006f\u0072\u0020\u0043\u006f\u006d\u0070\u0061t\u0069\u0062\u006c\u0065\u002e"));
if _cfggd (){return true ;};};};};return false ;};for _ ,_afae :=range _dbdd .PageList {_gdga :=_afae .Resources ;if _gdga ==nil {continue ;};if _gdga .ExtGState ==nil {continue ;};_afac ,_egcc :=_ag .GetDict (_gdga .ExtGState );if !_egcc {continue ;};
_dgcgb :=_afac .Keys ();for _ ,_fgeg :=range _dgcgb {_aggeb ,_ggafc :=_ag .GetDict (_afac .Get (_fgeg ));if !_ggafc {continue ;};if _dceb (_aggeb ){return _ddgge ;};};};for _ ,_cccf :=range _dbdd .PageList {_bdfb :=_cccf .Resources ;if _bdfb ==nil {continue ;
};_efce ,_ecce :=_ag .GetDict (_bdfb .XObject );if !_ecce {continue ;};for _ ,_addga :=range _efce .Keys (){_gadb ,_fdec :=_ag .GetStream (_efce .Get (_addga ));if !_fdec {continue ;};_ffdeb ,_fdec :=_ag .GetDict (_gadb .Get ("\u0052e\u0073\u006f\u0075\u0072\u0063\u0065s"));
if !_fdec {continue ;};_gbbee ,_fdec :=_ag .GetDict (_ffdeb .Get ("\u0045x\u0074\u0047\u0053\u0074\u0061\u0074e"));if !_fdec {continue ;};for _ ,_daed :=range _gbbee .Keys (){_bffb ,_adbgg :=_ag .GetDict (_gbbee .Get (_daed ));if !_adbgg {continue ;};if _dceb (_bffb ){return _ddgge ;
};};};};return _ddgge ;};func _aef (_eaef *_gc .Document )error {_fgac ,_gbdg :=_eaef .FindCatalog ();if !_gbdg {return _ce .New ("\u0063\u0061\u0074\u0061\u006c\u006f\u0067\u0020\u006e\u006f\u0074\u0020f\u006f\u0075\u006e\u0064");};_bdaa ,_gbdg :=_ag .GetDict (_fgac .Object .Get ("\u004e\u0061\u006de\u0073"));
if !_gbdg {return nil ;};if _bdaa .Get ("\u0041\u006c\u0074\u0065rn\u0061\u0074\u0065\u0050\u0072\u0065\u0073\u0065\u006e\u0074\u0061\u0074\u0069\u006fn\u0073")!=nil {_bdaa .Remove ("\u0041\u006c\u0074\u0065rn\u0061\u0074\u0065\u0050\u0072\u0065\u0073\u0065\u006e\u0074\u0061\u0074\u0069\u006fn\u0073");
};return nil ;};func _cggb (_efcc *_a .CompliancePdfReader ,_egcff standardType ,_cafc bool )(_dcgaf []ViolatedRule ){_fbfca ,_ceged :=_gfcb (_efcc );if !_ceged {return []ViolatedRule {_bb ("\u0036.\u0036\u002e\u0032\u002e\u0031\u002d1","\u0063a\u0074a\u006c\u006f\u0067\u0020\u006eo\u0074\u0020f\u006f\u0075\u006e\u0064\u002e")};
};_aebbf :=_fbfca .Get ("\u004d\u0065\u0074\u0061\u0064\u0061\u0074\u0061");if _aebbf ==nil {return []ViolatedRule {_bb ("\u0036.\u0036\u002e\u0032\u002e\u0031\u002d1","\u0054\u0068\u0065\u0020\u0043\u0061\u0074\u0061\u006c\u006f\u0067\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072y\u0020\u006f\u0066\u0020\u0061\u0020\u0063\u006f\u006e\u0066\u006f\u0072\u006d\u0069\u006e\u0067\u0020\u0066\u0069\u006c\u0065\u0020\u0073h\u0061\u006c\u006c\u0020\u0063\u006f\u006e\u0074ai\u006e\u0020\u0074\u0068\u0065\u0020\u004d\u0065\u0074\u0061\u0064\u0061\u0074\u0061\u0020\u006b\u0065\u0079\u0020\u0077\u0068\u006f\u0073\u0065\u0020v\u0061\u006c\u0075\u0065\u0020\u0069\u0073\u0020\u0061\u0020m\u0065\u0074\u0061\u0064\u0061\u0074\u0061\u0020s\u0074\u0072\u0065\u0061\u006d")};
};_agfdc ,_ceged :=_ag .GetStream (_aebbf );if !_ceged {return []ViolatedRule {_bb ("\u0036.\u0036\u002e\u0032\u002e\u0031\u002d1","\u0054\u0068\u0065\u0020\u0043\u0061\u0074\u0061\u006c\u006f\u0067\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072y\u0020\u006f\u0066\u0020\u0061\u0020\u0063\u006f\u006e\u0066\u006f\u0072\u006d\u0069\u006e\u0067\u0020\u0066\u0069\u006c\u0065\u0020\u0073h\u0061\u006c\u006c\u0020\u0063\u006f\u006e\u0074ai\u006e\u0020\u0074\u0068\u0065\u0020\u004d\u0065\u0074\u0061\u0064\u0061\u0074\u0061\u0020\u006b\u0065\u0079\u0020\u0077\u0068\u006f\u0073\u0065\u0020v\u0061\u006c\u0075\u0065\u0020\u0069\u0073\u0020\u0061\u0020m\u0065\u0074\u0061\u0064\u0061\u0074\u0061\u0020s\u0074\u0072\u0065\u0061\u006d")};
};_deaff ,_gdeg :=_cd .LoadDocument (_agfdc .Stream );if _gdeg !=nil {return []ViolatedRule {_bb ("\u0036.\u0036\u002e\u0032\u002e\u0031\u002d4","\u0041\u006c\u006c\u0020\u006de\u0074\u0061\u0064a\u0074\u0061\u0020\u0073\u0074\u0072\u0065\u0061\u006d\u0073\u0020\u0070\u0072\u0065\u0073\u0065\u006e\u0074\u0020i\u006e \u0074\u0068\u0065\u0020\u0050\u0044\u0046 \u0073\u0068\u0061\u006c\u006c\u0020\u0063o\u006e\u0066\u006f\u0072\u006d\u0020\u0074\u006f\u0020\u0074\u0068\u0065\u0020\u0058\u004d\u0050\u0020\u0053\u0070\u0065ci\u0066\u0069\u0063\u0061\u0074\u0069\u006fn\u002e\u0020\u0041\u006c\u006c\u0020c\u006fn\u0074\u0065\u006e\u0074\u0020\u006f\u0066\u0020\u0061\u006c\u006c\u0020\u0058\u004d\u0050\u0020p\u0061\u0063\u006b\u0065\u0074\u0073 \u0073h\u0061\u006c\u006c \u0062\u0065\u0020\u0077\u0065\u006c\u006c\u002d\u0066o\u0072\u006de\u0064")};
};_bgccde :=_deaff .GetGoXmpDocument ();var _bdgd []*_de .Namespace ;for _ ,_fbdcff :=range _bgccde .Namespaces (){switch _fbdcff .Name {case _fd .NsDc .Name ,_fb .NsPDF .Name ,_cg .NsXmp .Name ,_b .NsXmpRights .Name ,_da .Namespace .Name ,_gf .Namespace .Name ,_bd .NsXmpMM .Name ,_gf .FieldNS .Name ,_gf .SchemaNS .Name ,_gf .PropertyNS .Name ,"\u0073\u0074\u0045v\u0074","\u0073\u0074\u0056e\u0072","\u0073\u0074\u0052e\u0066","\u0073\u0074\u0044i\u006d","\u0078a\u0070\u0047\u0049\u006d\u0067","\u0073\u0074\u004ao\u0062","\u0078\u006d\u0070\u0069\u0064\u0071":continue ;
};_bdgd =append (_bdgd ,_fbdcff );};_ffgb :=true ;_ffac ,_gdeg :=_deaff .GetPdfaExtensionSchemas ();if _gdeg ==nil {for _ ,_abfda :=range _bdgd {var _gaaf bool ;for _degdge :=range _ffac {if _abfda .URI ==_ffac [_degdge ].NamespaceURI {_gaaf =true ;break ;
};};if !_gaaf {_ffgb =false ;break ;};};}else {_ffgb =false ;};if !_ffgb {_dcgaf =append (_dcgaf ,_bb ("\u0036.\u0036\u002e\u0032\u002e\u0033\u002d7","\u0041\u006c\u006c\u0020\u0070\u0072\u006f\u0070e\u0072\u0074\u0069e\u0073\u0020\u0073\u0070\u0065\u0063i\u0066\u0069\u0065\u0064\u0020\u0069\u006e\u0020\u0058\u004d\u0050\u0020\u0066\u006f\u0072m\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0075s\u0065\u0020\u0065\u0069\u0074\u0068\u0065\u0072\u0020\u0074\u0068\u0065\u0020\u0070\u0072\u0065\u0064\u0065\u0066\u0069\u006e\u0065\u0064\u0020\u0073\u0063he\u006da\u0073 \u0064\u0065\u0066\u0069\u006e\u0065\u0064\u0020\u0069\u006e\u0020\u0074\u0068\u0065\u0020\u0058\u004d\u0050\u0020\u0053\u0070\u0065\u0063\u0069\u0066\u0069\u0063\u0061\u0074\u0069\u006f\u006e\u002c\u0020\u0049\u0053\u004f\u0020\u0031\u00390\u0030\u0035-\u0031\u0020\u006f\u0072\u0020\u0074h\u0069s\u0020\u0070\u0061\u0072\u0074\u0020\u006f\u0066\u0020\u0049\u0053\u004f\u0020\u0031\u0039\u0030\u0030\u0035\u002c\u0020o\u0072\u0020\u0061\u006e\u0079\u0020e\u0078\u0074\u0065\u006e\u0073\u0069\u006f\u006e\u0020\u0073c\u0068\u0065\u006das\u0020\u0074\u0068\u0061\u0074\u0020\u0063\u006fm\u0070\u006c\u0079\u0020\u0077\u0069\u0074\u0068\u0020\u0036\u002e\u0036\u002e\u0032.\u0033\u002e\u0032\u002e"));
};_fecc ,_ceged :=_deaff .GetPdfAID ();if !_ceged {_dcgaf =append (_dcgaf ,_bb ("\u0036.\u0036\u002e\u0034\u002d\u0031","\u0054\u0068\u0065\u0020\u0050\u0044\u0046\u002f\u0041\u0020\u0076\u0065\u0072\u0073\u0069\u006f\u006e\u0020\u0061n\u0064\u0020\u0063\u006f\u006e\u0066\u006f\u0072\u006d\u0061\u006ec\u0065\u0020\u006c\u0065\u0076\u0065l\u0020\u006f\u0066\u0020\u0061\u0020\u0066\u0069\u006c\u0065\u0020\u0073h\u0061\u006c\u006c\u0020\u0062\u0065\u0020\u0073\u0070e\u0063\u0069\u0066\u0069\u0065\u0064\u0020\u0075\u0073\u0069\u006e\u0067\u0020\u0074\u0068\u0065\u0020\u0050\u0044\u0046\u002f\u0041\u0020\u0049\u0064\u0065\u006e\u0074\u0069\u0066\u0069\u0063\u0061\u0074\u0069\u006f\u006e\u0020\u0065\u0078\u0074\u0065\u006e\u0073\u0069\u006f\u006e\u0020\u0073\u0063h\u0065\u006da."));
}else {if _fecc .Part !=_egcff ._dg {_dcgaf =append (_dcgaf ,_bb ("\u0036.\u0036\u002e\u0034\u002d\u0032","\u0054h\u0065\u0020\u0076\u0061lue\u0020\u006f\u0066\u0020p\u0064\u0066\u0061\u0069\u0064\u003a\u0070\u0061\u0072\u0074 \u0073\u0068\u0061\u006c\u006c\u0020\u0062\u0065\u0020\u0074\u0068\u0065\u0020\u0070\u0061\u0072\u0074\u0020\u006e\u0075\u006d\u0062\u0065r\u0020\u006f\u0066\u0020\u0049\u0053\u004f\u002019\u0030\u0030\u0035 \u0074\u006f\u0020\u0077\u0068i\u0063h\u0020\u0074\u0068\u0065\u0020\u0066\u0069\u006c\u0065 \u0063\u006f\u006e\u0066\u006f\u0072\u006d\u0073\u002e"));
};if _egcff ._db =="\u0041"&&_fecc .Conformance !="\u0041"{_dcgaf =append (_dcgaf ,_bb ("\u0036.\u0036\u002e\u0034\u002d\u0033","\u0041\u0020\u004c\u0065\u0076\u0065\u006c\u0020\u0041\u0020\u0063\u006f\u006e\u0066\u006f\u0072\u006d\u0069\u006e\u0067\u0020\u0066\u0069\u006c\u0065 \u0073\u0068\u0061l\u006c\u0020\u0073\u0070ec\u0069\u0066\u0079\u0020\u0074\u0068\u0065\u0020\u0076\u0061\u006cu\u0065\u0020\u006f\u0066\u0020\u0070\u0064\u0066\u0061\u0069\u0064\u003a\u0063\u006f\u006e\u0066\u006f\u0072\u006d\u0061\u006ec\u0065\u0020as\u0020\u0041\u002e\u0020\u0041 \u004c\u0065v\u0065\u006c\u0020\u0042\u0020c\u006f\u006e\u0066\u006f\u0072\u006d\u0069\u006e\u0067\u0020\u0066\u0069\u006c\u0065\u0020\u0073\u0068\u0061\u006cl\u0020\u0073\u0070\u0065\u0063\u0069\u0066\u0079\u0020\u0074\u0068\u0065\u0020\u0076\u0061lu\u0065\u0020o\u0066 \u0070\u0064\u0066\u0061\u0069d\u003a\u0063\u006f\u006e\u0066\u006f\u0072\u006d\u0061\u006e\u0063\u0065\u0020\u0061\u0073\u0020\u0042\u002e\u0020\u0041\u0020\u004c\u0065\u0076\u0065\u006c \u0055\u0020\u0063\u006f\u006e\u0066\u006fr\u006d\u0069\u006e\u0067\u0020\u0066\u0069\u006c\u0065\u0020\u0073\u0068\u0061\u006c\u006c\u0020s\u0070\u0065\u0063\u0069\u0066\u0079 \u0074\u0068\u0065\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u006ff\u0020\u0070\u0064f\u0061i\u0064\u003ac\u006fn\u0066\u006f\u0072\u006d\u0061\u006e\u0063\u0065 \u0061\u0073\u0020\u0055."));
}else if _egcff ._db =="\u0055"&&(_fecc .Conformance !="\u0041"&&_fecc .Conformance !="\u0055"){_dcgaf =append (_dcgaf ,_bb ("\u0036.\u0036\u002e\u0034\u002d\u0033","\u0041\u0020\u004c\u0065\u0076\u0065\u006c\u0020\u0041\u0020\u0063\u006f\u006e\u0066\u006f\u0072\u006d\u0069\u006e\u0067\u0020\u0066\u0069\u006c\u0065 \u0073\u0068\u0061l\u006c\u0020\u0073\u0070ec\u0069\u0066\u0079\u0020\u0074\u0068\u0065\u0020\u0076\u0061\u006cu\u0065\u0020\u006f\u0066\u0020\u0070\u0064\u0066\u0061\u0069\u0064\u003a\u0063\u006f\u006e\u0066\u006f\u0072\u006d\u0061\u006ec\u0065\u0020as\u0020\u0041\u002e\u0020\u0041 \u004c\u0065v\u0065\u006c\u0020\u0042\u0020c\u006f\u006e\u0066\u006f\u0072\u006d\u0069\u006e\u0067\u0020\u0066\u0069\u006c\u0065\u0020\u0073\u0068\u0061\u006cl\u0020\u0073\u0070\u0065\u0063\u0069\u0066\u0079\u0020\u0074\u0068\u0065\u0020\u0076\u0061lu\u0065\u0020o\u0066 \u0070\u0064\u0066\u0061\u0069d\u003a\u0063\u006f\u006e\u0066\u006f\u0072\u006d\u0061\u006e\u0063\u0065\u0020\u0061\u0073\u0020\u0042\u002e\u0020\u0041\u0020\u004c\u0065\u0076\u0065\u006c \u0055\u0020\u0063\u006f\u006e\u0066\u006fr\u006d\u0069\u006e\u0067\u0020\u0066\u0069\u006c\u0065\u0020\u0073\u0068\u0061\u006c\u006c\u0020s\u0070\u0065\u0063\u0069\u0066\u0079 \u0074\u0068\u0065\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u006ff\u0020\u0070\u0064f\u0061i\u0064\u003ac\u006fn\u0066\u006f\u0072\u006d\u0061\u006e\u0063\u0065 \u0061\u0073\u0020\u0055."));
}else if _egcff ._db =="\u0042"&&(_fecc .Conformance !="\u0041"&&_fecc .Conformance !="\u0042"&&_fecc .Conformance !="\u0055"){_dcgaf =append (_dcgaf ,_bb ("\u0036.\u0036\u002e\u0034\u002d\u0033","\u0041\u0020\u004c\u0065\u0076\u0065\u006c\u0020\u0041\u0020\u0063\u006f\u006e\u0066\u006f\u0072\u006d\u0069\u006e\u0067\u0020\u0066\u0069\u006c\u0065 \u0073\u0068\u0061l\u006c\u0020\u0073\u0070ec\u0069\u0066\u0079\u0020\u0074\u0068\u0065\u0020\u0076\u0061\u006cu\u0065\u0020\u006f\u0066\u0020\u0070\u0064\u0066\u0061\u0069\u0064\u003a\u0063\u006f\u006e\u0066\u006f\u0072\u006d\u0061\u006ec\u0065\u0020as\u0020\u0041\u002e\u0020\u0041 \u004c\u0065v\u0065\u006c\u0020\u0042\u0020c\u006f\u006e\u0066\u006f\u0072\u006d\u0069\u006e\u0067\u0020\u0066\u0069\u006c\u0065\u0020\u0073\u0068\u0061\u006cl\u0020\u0073\u0070\u0065\u0063\u0069\u0066\u0079\u0020\u0074\u0068\u0065\u0020\u0076\u0061lu\u0065\u0020o\u0066 \u0070\u0064\u0066\u0061\u0069d\u003a\u0063\u006f\u006e\u0066\u006f\u0072\u006d\u0061\u006e\u0063\u0065\u0020\u0061\u0073\u0020\u0042\u002e\u0020\u0041\u0020\u004c\u0065\u0076\u0065\u006c \u0055\u0020\u0063\u006f\u006e\u0066\u006fr\u006d\u0069\u006e\u0067\u0020\u0066\u0069\u006c\u0065\u0020\u0073\u0068\u0061\u006c\u006c\u0020s\u0070\u0065\u0063\u0069\u0066\u0079 \u0074\u0068\u0065\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u006ff\u0020\u0070\u0064f\u0061i\u0064\u003ac\u006fn\u0066\u006f\u0072\u006d\u0061\u006e\u0063\u0065 \u0061\u0073\u0020\u0055."));
};};return _dcgaf ;};func _eeffc (_bccbc *_a .CompliancePdfReader )(_eadgf []ViolatedRule ){var _gefbe ,_babcb ,_edfad ,_face ,_edac ,_ddgdb ,_gggd bool ;_feef :=func ()bool {return _gefbe &&_babcb &&_edfad &&_face &&_edac &&_ddgdb &&_gggd };for _ ,_gdbeb :=range _bccbc .PageList {_cfbc ,_dgbe :=_gdbeb .GetAnnotations ();
if _dgbe !=nil {_g .Log .Trace ("\u006c\u006f\u0061\u0064\u0069\u006e\u0067\u0020\u0061\u006en\u006f\u0074\u0061\u0074\u0069\u006f\u006es\u0020\u0066\u0061\u0069\u006c\u0065\u0064\u003a\u0020\u0025\u0076",_dgbe );continue ;};for _ ,_cbgdb :=range _cfbc {if !_gefbe {switch _cbgdb .GetContext ().(type ){case *_a .PdfAnnotationScreen ,*_a .PdfAnnotation3D ,*_a .PdfAnnotationSound ,*_a .PdfAnnotationMovie ,nil :_eadgf =append (_eadgf ,_bb ("\u0036.\u0033\u002e\u0031\u002d\u0031","\u0041nn\u006f\u0074\u0061\u0074i\u006f\u006e t\u0079\u0070\u0065\u0073\u0020\u006e\u006f\u0074\u0020\u0064\u0065f\u0069\u006e\u0065\u0064\u0020i\u006e\u0020\u0050\u0044\u0046\u0020\u0052\u0065\u0066\u0065\u0072e\u006e\u0063\u0065\u0020\u0073\u0068\u0061\u006cl\u0020\u006e\u006f\u0074\u0020\u0062\u0065\u0020\u0070\u0065r\u006d\u0069t\u0074\u0065\u0064\u002e\u0020\u0041\u0064d\u0069\u0074\u0069\u006f\u006e\u0061\u006c\u006c\u0079\u002c\u0020\u0074\u0068\u0065\u0020\u0033\u0044\u002c\u0020\u0053\u006f\u0075\u006e\u0064\u002c\u0020\u0053\u0063\u0072\u0065\u0065\u006e\u0020\u0061n\u0064\u0020\u004d\u006f\u0076\u0069\u0065\u0020\u0074\u0079\u0070\u0065\u0073\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u006e\u006f\u0074\u0020\u0062\u0065\u0020\u0070\u0065\u0072\u006d\u0069\u0074\u0074\u0065\u0064\u002e"));
_gefbe =true ;if _feef (){return _eadgf ;};};};_eecg ,_gdce :=_ag .GetDict (_cbgdb .GetContainingPdfObject ());if !_gdce {continue ;};_ ,_ggcbee :=_cbgdb .GetContext ().(*_a .PdfAnnotationPopup );if !_ggcbee &&!_babcb {_ ,_gfgc :=_ag .GetIntVal (_eecg .Get ("\u0046"));
if !_gfgc {_eadgf =append (_eadgf ,_bb ("\u0036.\u0033\u002e\u0032\u002d\u0031","\u0045\u0078\u0063\u0065\u0070\u0074\u0020\u0066\u006f\u0072\u0020\u0061\u006e\u006e\u006f\u0074\u0061\u0074\u0069o\u006e\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072i\u0065\u0073\u0020\u0077\u0068\u006fs\u0065\u0020\u0053\u0075\u0062\u0074\u0079\u0070\u0065\u0020\u0076\u0061l\u0075\u0065\u0020\u0069\u0073\u0020\u0050\u006f\u0070u\u0070\u002c\u0020\u0061\u006c\u006c\u0020\u0061\u006e\u006e\u006f\u0074\u0061\u0074\u0069\u006f\u006e\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0069\u0065\u0073\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0063\u006f\u006e\u0074\u0061\u0069\u006e\u0020\u0074\u0068\u0065\u0020\u0046 \u006b\u0065y."));
_babcb =true ;if _feef (){return _eadgf ;};};};if !_edfad {_gfge ,_cdbg :=_ag .GetIntVal (_eecg .Get ("\u0046"));if _cdbg &&!(_gfge &4==4&&_gfge &1==0&&_gfge &2==0&&_gfge &32==0&&_gfge &256==0){_eadgf =append (_eadgf ,_bb ("\u0036.\u0033\u002e\u0032\u002d\u0032","I\u0066\u0020\u0070\u0072\u0065\u0073\u0065\u006e\u0074\u002c\u0020\u0074\u0068\u0065\u0020\u0046 \u006b\u0065\u0079\u0027\u0073\u0020\u0050\u0072\u0069\u006e\u0074\u0020\u0066\u006c\u0061\u0067\u0020\u0062\u0069\u0074\u0020\u0073\u0068\u0061l\u006c\u0020\u0062\u0065\u0020\u0073\u0065\u0074\u0020\u0074\u006f\u0020\u0031\u0020\u0061\u006e\u0064\u0020\u0069\u0074\u0073\u0020\u0048\u0069\u0064\u0064\u0065\u006e\u002c\u0020\u0049\u006e\u0076\u0069\u0073\u0069\u0062\u006c\u0065\u002c\u0020\u0054\u006f\u0067\u0067\u006c\u0065\u004e\u006f\u0056\u0069\u0065\u0077\u002c\u0020\u0061\u006e\u0064 \u004eo\u0056\u0069\u0065\u0077\u0020\u0066\u006c\u0061\u0067\u0020\u0062\u0069\u0074\u0073\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0062\u0065\u0020s\u0065\u0074\u0020t\u006f\u0020\u0030."));
_edfad =true ;if _feef (){return _eadgf ;};};};_ ,_ffcd :=_cbgdb .GetContext ().(*_a .PdfAnnotationText );if _ffcd &&!_face {_fcaag ,_cbge :=_ag .GetIntVal (_eecg .Get ("\u0046"));if _cbge &&!(_fcaag &8==8&&_fcaag &16==16){_eadgf =append (_eadgf ,_bb ("\u0036.\u0033\u002e\u0032\u002d\u0033","\u0054\u0065\u0078\u0074\u0020a\u006e\u006e\u006f\u0074\u0061t\u0069o\u006e\u0020\u0068\u0061\u0073\u0020\u006f\u006e\u0065\u0020\u006f\u0066\u0020\u0074\u0068\u0065\u0020\u0066\u006ca\u0067\u0073\u0020\u004e\u006f\u005a\u006f\u006f\u006d\u0020\u006f\u0072\u0020\u004e\u006f\u0052\u006f\u0074\u0061\u0074\u0065\u0020\u0073\u0065t\u0020\u0074\u006f\u0020\u0030\u002e"));
_face =true ;if _feef (){return _eadgf ;};};};if !_edac {_gage ,_fdgbf :=_ag .GetDict (_eecg .Get ("\u0041\u0050"));if _fdgbf {_edaf :=_gage .Get ("\u004e");if _edaf ==nil ||len (_gage .Keys ())> 1{_eadgf =append (_eadgf ,_bb ("\u0036.\u0033\u002e\u0033\u002d\u0032","\u0046\u006f\u0072\u0020\u0061\u006c\u006c\u0020\u0061\u006e\u006e\u006ft\u0061\u0074\u0069\u006f\u006e\u0020d\u0069\u0063t\u0069\u006f\u006ea\u0072\u0069\u0065\u0073 \u0063\u006f\u006e\u0074\u0061\u0069\u006e\u0069\u006e\u0067\u0020\u0061\u006e\u0020\u0041\u0050 \u006b\u0065\u0079\u002c\u0020\u0074\u0068\u0065\u0020\u0061p\u0070\u0065\u0061\u0072\u0061\u006e\u0063\u0065\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u0020\u0074\u0068\u0061\u0074\u0020\u0069\u0074\u0020\u0064\u0065\u0066\u0069\u006e\u0065\u0073\u0020\u0061\u0073\u0020it\u0073\u0020\u0076\u0061\u006cu\u0065\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0063\u006f\u006e\u0074\u0061i\u006e\u0020o\u006e\u006c\u0079\u0020\u0074\u0068\u0065\u0020\u004e\u0020\u006b\u0065\u0079\u002e\u0020\u0049\u0066\u0020\u0061\u006e\u0020\u0061\u006e\u006e\u006f\u0074\u0061\u0074\u0069\u006f\u006e\u0020\u0064i\u0063\u0074\u0069o\u006e\u0061\u0072\u0079\u0027\u0073\u0020\u0053\u0075\u0062ty\u0070\u0065\u0020\u006b\u0065\u0079\u0020\u0068\u0061\u0073\u0020\u0061\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u006f\u0066\u0020\u0057\u0069\u0064g\u0065\u0074\u0020\u0061\u006e\u0064\u0020\u0069\u0074s\u0020\u0046\u0054 \u006be\u0079\u0020\u0068\u0061\u0073\u0020\u0061\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u006f\u0066\u0020B\u0074\u006e,\u0020\u0074he \u0076a\u006c\u0075\u0065\u0020\u006f\u0066\u0020\u0074\u0068\u0065\u0020\u004e\u0020\u006b\u0065\u0079\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0062\u0065\u0020\u0061\u006e\u0020\u0061\u0070\u0070\u0065\u0061\u0072\u0061\u006e\u0063\u0065\u0020\u0073\u0075\u0062\u0064\u0069\u0063\u0074\u0069\u006fn\u0061r\u0079; \u006f\u0074\u0068\u0065\u0072\u0077\u0069s\u0065\u0020\u0074\u0068\u0065\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u006f\u0066\u0020th\u0065\u0020N\u0020\u006b\u0065y\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0062e\u0020\u0061\u006e\u0020\u0061\u0070\u0070\u0065\u0061\u0072\u0061n\u0063\u0065\u0020\u0073\u0074\u0072\u0065\u0061\u006d\u002e"));
_edac =true ;if _feef (){return _eadgf ;};continue ;};_ ,_baaf :=_cbgdb .GetContext ().(*_a .PdfAnnotationWidget );if _baaf {_effe ,_dddc :=_ag .GetName (_eecg .Get ("\u0046\u0054"));if _dddc &&*_effe =="\u0042\u0074\u006e"{if _ ,_dfgaf :=_ag .GetDict (_edaf );
!_dfgaf {_eadgf =append (_eadgf ,_bb ("\u0036.\u0033\u002e\u0033\u002d\u0032","\u0046\u006f\u0072\u0020\u0061\u006c\u006c\u0020\u0061\u006e\u006e\u006ft\u0061\u0074\u0069\u006f\u006e\u0020d\u0069\u0063t\u0069\u006f\u006ea\u0072\u0069\u0065\u0073 \u0063\u006f\u006e\u0074\u0061\u0069\u006e\u0069\u006e\u0067\u0020\u0061\u006e\u0020\u0041\u0050 \u006b\u0065\u0079\u002c\u0020\u0074\u0068\u0065\u0020\u0061p\u0070\u0065\u0061\u0072\u0061\u006e\u0063\u0065\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u0020\u0074\u0068\u0061\u0074\u0020\u0069\u0074\u0020\u0064\u0065\u0066\u0069\u006e\u0065\u0073\u0020\u0061\u0073\u0020it\u0073\u0020\u0076\u0061\u006cu\u0065\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0063\u006f\u006e\u0074\u0061i\u006e\u0020o\u006e\u006c\u0079\u0020\u0074\u0068\u0065\u0020\u004e\u0020\u006b\u0065\u0079\u002e\u0020\u0049\u0066\u0020\u0061\u006e\u0020\u0061\u006e\u006e\u006f\u0074\u0061\u0074\u0069\u006f\u006e\u0020\u0064i\u0063\u0074\u0069o\u006e\u0061\u0072\u0079\u0027\u0073\u0020\u0053\u0075\u0062ty\u0070\u0065\u0020\u006b\u0065\u0079\u0020\u0068\u0061\u0073\u0020\u0061\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u006f\u0066\u0020\u0057\u0069\u0064g\u0065\u0074\u0020\u0061\u006e\u0064\u0020\u0069\u0074s\u0020\u0046\u0054 \u006be\u0079\u0020\u0068\u0061\u0073\u0020\u0061\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u006f\u0066\u0020B\u0074\u006e,\u0020\u0074he \u0076a\u006c\u0075\u0065\u0020\u006f\u0066\u0020\u0074\u0068\u0065\u0020\u004e\u0020\u006b\u0065\u0079\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0062\u0065\u0020\u0061\u006e\u0020\u0061\u0070\u0070\u0065\u0061\u0072\u0061\u006e\u0063\u0065\u0020\u0073\u0075\u0062\u0064\u0069\u0063\u0074\u0069\u006fn\u0061r\u0079; \u006f\u0074\u0068\u0065\u0072\u0077\u0069s\u0065\u0020\u0074\u0068\u0065\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u006f\u0066\u0020th\u0065\u0020N\u0020\u006b\u0065y\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0062e\u0020\u0061\u006e\u0020\u0061\u0070\u0070\u0065\u0061\u0072\u0061n\u0063\u0065\u0020\u0073\u0074\u0072\u0065\u0061\u006d\u002e"));
_edac =true ;if _feef (){return _eadgf ;};continue ;};};};_ ,_afbdc :=_ag .GetStream (_edaf );if !_afbdc {_eadgf =append (_eadgf ,_bb ("\u0036.\u0033\u002e\u0033\u002d\u0032","\u0046\u006f\u0072\u0020\u0061\u006c\u006c\u0020\u0061\u006e\u006e\u006ft\u0061\u0074\u0069\u006f\u006e\u0020d\u0069\u0063t\u0069\u006f\u006ea\u0072\u0069\u0065\u0073 \u0063\u006f\u006e\u0074\u0061\u0069\u006e\u0069\u006e\u0067\u0020\u0061\u006e\u0020\u0041\u0050 \u006b\u0065\u0079\u002c\u0020\u0074\u0068\u0065\u0020\u0061p\u0070\u0065\u0061\u0072\u0061\u006e\u0063\u0065\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u0020\u0074\u0068\u0061\u0074\u0020\u0069\u0074\u0020\u0064\u0065\u0066\u0069\u006e\u0065\u0073\u0020\u0061\u0073\u0020it\u0073\u0020\u0076\u0061\u006cu\u0065\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0063\u006f\u006e\u0074\u0061i\u006e\u0020o\u006e\u006c\u0079\u0020\u0074\u0068\u0065\u0020\u004e\u0020\u006b\u0065\u0079\u002e\u0020\u0049\u0066\u0020\u0061\u006e\u0020\u0061\u006e\u006e\u006f\u0074\u0061\u0074\u0069\u006f\u006e\u0020\u0064i\u0063\u0074\u0069o\u006e\u0061\u0072\u0079\u0027\u0073\u0020\u0053\u0075\u0062ty\u0070\u0065\u0020\u006b\u0065\u0079\u0020\u0068\u0061\u0073\u0020\u0061\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u006f\u0066\u0020\u0057\u0069\u0064g\u0065\u0074\u0020\u0061\u006e\u0064\u0020\u0069\u0074s\u0020\u0046\u0054 \u006be\u0079\u0020\u0068\u0061\u0073\u0020\u0061\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u006f\u0066\u0020B\u0074\u006e,\u0020\u0074he \u0076a\u006c\u0075\u0065\u0020\u006f\u0066\u0020\u0074\u0068\u0065\u0020\u004e\u0020\u006b\u0065\u0079\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0062\u0065\u0020\u0061\u006e\u0020\u0061\u0070\u0070\u0065\u0061\u0072\u0061\u006e\u0063\u0065\u0020\u0073\u0075\u0062\u0064\u0069\u0063\u0074\u0069\u006fn\u0061r\u0079; \u006f\u0074\u0068\u0065\u0072\u0077\u0069s\u0065\u0020\u0074\u0068\u0065\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u006f\u0066\u0020th\u0065\u0020N\u0020\u006b\u0065y\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0062e\u0020\u0061\u006e\u0020\u0061\u0070\u0070\u0065\u0061\u0072\u0061n\u0063\u0065\u0020\u0073\u0074\u0072\u0065\u0061\u006d\u002e"));
_edac =true ;if _feef (){return _eadgf ;};continue ;};};};_bccc ,_dcdf :=_cbgdb .GetContext ().(*_a .PdfAnnotationWidget );if !_dcdf {continue ;};if !_ddgdb {if _bccc .A !=nil {_eadgf =append (_eadgf ,_bb ("\u0036.\u0034\u002e\u0031\u002d\u0031","A \u0057\u0069d\u0067\u0065\u0074\u0020\u0061\u006e\u006e\u006f\u0074a\u0074\u0069\u006f\u006e\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u006e\u006f\u0074\u0020\u0069\u006ec\u006cu\u0064\u0065\u0020\u0061\u006e\u0020\u0041\u0020e\u006et\u0072\u0079."));
_ddgdb =true ;if _feef (){return _eadgf ;};};};if !_gggd {if _bccc .AA !=nil {_eadgf =append (_eadgf ,_bb ("\u0036.\u0034\u002e\u0031\u002d\u0031","\u0041\u0020\u0057\u0069\u0064\u0067\u0065\u0074\u0020\u0061\u006e\u006eo\u0074\u0061\u0074i\u006f\u006e\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061r\u0079\u0020\u0073h\u0061\u006c\u006c\u0020n\u006f\u0074\u0020\u0069\u006e\u0063\u006c\u0075\u0064\u0065\u0020\u0061\u006e\u0020\u0041\u0041\u0020\u0065\u006e\u0074\u0072\u0079\u0020\u0066\u006f\u0072\u0020\u0061\u006e\u0020\u0061d\u0064\u0069\u0074\u0069\u006f\u006e\u0061\u006c\u002d\u0061\u0063t\u0069\u006f\u006e\u0073\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u002e"));
_gggd =true ;if _feef (){return _eadgf ;};};};};};return _eadgf ;};func _fgdf (_acebg *_ag .PdfObjectDictionary ,_acdf map[*_ag .PdfObjectStream ][]byte ,_caae map[*_ag .PdfObjectStream ]*_ab .CMap )ViolatedRule {const (_gbdc ="\u0036.\u0033\u002e\u0033\u002d\u0034";
_afdg ="\u0046\u006f\u0072\u0020\u0074\u0068\u006fs\u0065\u0020\u0043\u004d\u0061\u0070\u0073\u0020\u0074\u0068\u0061\u0074\u0020\u0061\u0072e\u0020\u0065m\u0062\u0065\u0064de\u0064\u002c\u0020\u0074\u0068\u0065\u0020\u0069\u006et\u0065\u0067\u0065\u0072 \u0076\u0061\u006c\u0075\u0065\u0020\u006f\u0066\u0020\u0074\u0068\u0065\u0020\u0057\u004d\u006f\u0064\u0065\u0020\u0065\u006e\u0074r\u0079\u0020i\u006e t\u0068\u0065\u0020CM\u0061\u0070\u0020\u0064\u0069\u0063\u0074\u0069o\u006ea\u0072\u0079\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0062\u0065\u0020\u0069\u0064\u0065\u006e\u0074\u0069\u0063\u0061\u006c\u0020\u0074\u006f \u0074h\u0065\u0020\u0057\u004d\u006f\u0064e\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u0069\u006e\u0020\u0074h\u0065\u0020\u0065\u006d\u0062\u0065\u0064\u0064ed\u0020\u0043\u004d\u0061\u0070\u0020\u0073\u0074\u0072\u0065\u0061\u006d\u002e";
);var _febc string ;if _dedf ,_bdacb :=_ag .GetName (_acebg .Get ("\u0053u\u0062\u0074\u0079\u0070\u0065"));_bdacb {_febc =_dedf .String ();};if _febc !="\u0054\u0079\u0070e\u0030"{return _eag ;};_bbbe :=_acebg .Get ("\u0045\u006e\u0063\u006f\u0064\u0069\u006e\u0067");
if _ ,_bfeca :=_ag .GetName (_bbbe );_bfeca {return _eag ;};_gfegeb ,_gbdd :=_ag .GetStream (_bbbe );if !_gbdd {return _bb (_gbdc ,_afdg );};_ceef ,_ceggd :=_gfbb (_gfegeb ,_acdf ,_caae );if _ceggd !=nil {return _bb (_gbdc ,_afdg );};_cece ,_dbea :=_ag .GetIntVal (_gfegeb .Get ("\u0057\u004d\u006fd\u0065"));
_gaff ,_eegbe :=_ceef .WMode ();if _dbea &&_eegbe {if _gaff !=_cece {return _bb (_gbdc ,_afdg );};};if (_dbea &&!_eegbe )||(!_dbea &&_eegbe ){return _bb (_gbdc ,_afdg );};return _eag ;};func _fdfb (_fcaf *_a .CompliancePdfReader ,_aad bool )(_deag []ViolatedRule ){var _fced ,_fddd ,_ebca ,_bcf ,_cffa ,_agdg ,_fgdeg bool ;
_bdeac :=func ()bool {return _fced &&_fddd &&_ebca &&_bcf &&_cffa &&_agdg &&_fgdeg };_ffbcb ,_fbe :=_aggec (_fcaf );var _debd _gcf .ProfileHeader ;if _fbe {_debd ,_ =_gcf .ParseHeader (_ffbcb .DestOutputProfile );};var _aebf bool ;_ccec :=map[_ag .PdfObject ]struct{}{};
var _cdaaa func (_adceg _a .PdfColorspace )bool ;_cdaaa =func (_gdbg _a .PdfColorspace )bool {switch _aabb :=_gdbg .(type ){case *_a .PdfColorspaceDeviceGray :if !_agdg {if !_fbe {_aebf =true ;_deag =append (_deag ,_bb ("\u0036.\u0032\u002e\u0033\u002d\u0034","\u0044\u0065\u0076\u0069\u0063\u0065G\u0072\u0061\u0079\u0020\u006da\u0079\u0020\u0062\u0065\u0020\u0075s\u0065\u0064\u0020\u006f\u006el\u0079\u0020\u0069\u0066\u0020\u0074\u0068\u0065\u0020\u0066\u0069\u006ce\u0020\u0068\u0061\u0073\u0020\u0061\u0020\u0050\u0044\u0046\u002f\u0041\u002d\u0031\u0020O\u0075\u0074\u0070\u0075\u0074\u0049\u006e\u0074e\u006e\u0074\u002e"));
_agdg =true ;if _bdeac (){return true ;};};};case *_a .PdfColorspaceDeviceRGB :if !_bcf {if !_fbe ||_debd .ColorSpace !=_gcf .ColorSpaceRGB {_aebf =true ;_deag =append (_deag ,_bb ("\u0036.\u0032\u002e\u0033\u002d\u0032","\u0044\u0065\u0076\u0069\u0063\u0065\u0052\u0047\u0042\u0020\u006d\u0061\u0079\u0020\u0062\u0065\u0020\u0075\u0073\u0065\u0064\u0020\u006f\u006e\u006c\u0079\u0020\u0069\u0066\u0020\u0074\u0068\u0065 \u0066\u0069\u006c\u0065\u0020\u0068\u0061\u0073\u0020\u0061\u0020\u0050\u0044\u0046\u002f\u0041\u002d\u0031\u0020\u004f\u0075\u0074\u0070\u0075\u0074In\u0074\u0065\u006e\u0074\u0020\u0074\u0068\u0061\u0074\u0020u\u0073es\u0020a\u006e\u0020\u0052\u0047\u0042\u0020\u0063o\u006c\u006f\u0072\u0020\u0073\u0070\u0061\u0063\u0065\u002e"));
_bcf =true ;if _bdeac (){return true ;};};};case *_a .PdfColorspaceDeviceCMYK :if !_cffa {if !_fbe ||_debd .ColorSpace !=_gcf .ColorSpaceCMYK {_aebf =true ;_deag =append (_deag ,_bb ("\u0036.\u0032\u002e\u0033\u002d\u0033","\u0044\u0065\u0076\u0069\u0063e\u0043\u004d\u0059\u004b \u006d\u0061\u0079\u0020\u0062\u0065\u0020\u0075\u0073\u0065\u0064\u0020\u006f\u006e\u006c\u0079\u0020\u0069\u0066\u0020\u0074h\u0065\u0020\u0066\u0069\u006ce \u0068\u0061\u0073\u0020\u0061 \u0050\u0044\u0046\u002f\u0041\u002d\u0031\u0020\u004f\u0075\u0074p\u0075\u0074\u0049\u006e\u0074\u0065\u006e\u0074\u0020\u0074\u0068a\u0074\u0020\u0075\u0073\u0065\u0073\u0020\u0061\u006e \u0043\u004d\u0059\u004b\u0020\u0063\u006f\u006c\u006f\u0072\u0020s\u0070\u0061\u0063e\u002e"));
_cffa =true ;if _bdeac (){return true ;};};};case *_a .PdfColorspaceICCBased :if !_ebca ||!_fgdeg {_bacc ,_dgdb :=_gcf .ParseHeader (_aabb .Data );if _dgdb !=nil {_g .Log .Debug ("\u0070\u0061\u0072si\u006e\u0067\u0020\u0049\u0043\u0043\u0042\u0061\u0073e\u0064 \u0068e\u0061d\u0065\u0072\u0020\u0066\u0061\u0069\u006c\u0065\u0064\u003a\u0020\u0025\u0076",_dgdb );
_deag =append (_deag ,func ()ViolatedRule {return _bb ("\u0036.\u0032\u002e\u0033\u002d\u0031","\u0041\u006cl \u0049\u0043\u0043\u0042\u0061\u0073\u0065\u0064\u0020\u0063\u006f\u006co\u0072\u0020\u0073\u0070a\u0063e\u0073\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0062\u0065\u0020\u0065\u006d\u0062\u0065\u0064\u0064\u0065d\u0020\u0061\u0073\u0020\u0049\u0043\u0043 \u0070\u0072\u006f\u0066\u0069\u006c\u0065\u0020\u0073\u0074\u0072\u0065a\u006d\u0073 \u0061\u0073\u0020d\u0065\u0073\u0063\u0072\u0069\u0062\u0065\u0064\u0020\u0069\u006e\u0020\u0050\u0044\u0046\u0020R\u0065\u0066\u0065\u0072\u0065\u006e\u0063\u0065\u0020\u0034\u002e\u0035");
}());_ebca =true ;if _bdeac (){return true ;};};if !_ebca {var _faac ,_befa bool ;switch _bacc .DeviceClass {case _gcf .DeviceClassPRTR ,_gcf .DeviceClassMNTR ,_gcf .DeviceClassSCNR ,_gcf .DeviceClassSPAC :default:_faac =true ;};switch _bacc .ColorSpace {case _gcf .ColorSpaceRGB ,_gcf .ColorSpaceCMYK ,_gcf .ColorSpaceGRAY ,_gcf .ColorSpaceLAB :default:_befa =true ;
};if _faac ||_befa {_deag =append (_deag ,_bb ("\u0036.\u0032\u002e\u0033\u002d\u0031","\u0041\u006cl \u0049\u0043\u0043\u0042\u0061\u0073\u0065\u0064\u0020\u0063\u006f\u006co\u0072\u0020\u0073\u0070a\u0063e\u0073\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0062\u0065\u0020\u0065\u006d\u0062\u0065\u0064\u0064\u0065d\u0020\u0061\u0073\u0020\u0049\u0043\u0043 \u0070\u0072\u006f\u0066\u0069\u006c\u0065\u0020\u0073\u0074\u0072\u0065a\u006d\u0073 \u0061\u0073\u0020d\u0065\u0073\u0063\u0072\u0069\u0062\u0065\u0064\u0020\u0069\u006e\u0020\u0050\u0044\u0046\u0020R\u0065\u0066\u0065\u0072\u0065\u006e\u0063\u0065\u0020\u0034\u002e\u0035"));
_ebca =true ;if _bdeac (){return true ;};};};if !_fgdeg {_cdfdg ,_ :=_ag .GetStream (_aabb .GetContainingPdfObject ());if _cdfdg .Get ("\u004e")==nil ||(_aabb .N ==1&&_bacc .ColorSpace !=_gcf .ColorSpaceGRAY )||(_aabb .N ==3&&!(_bacc .ColorSpace ==_gcf .ColorSpaceRGB ||_bacc .ColorSpace ==_gcf .ColorSpaceLAB ))||(_aabb .N ==4&&_bacc .ColorSpace !=_gcf .ColorSpaceCMYK ){_deag =append (_deag ,_bb ("\u0036.\u0032\u002e\u0033\u002d\u0035","\u0049\u0066\u0020a\u006e\u0020u\u006e\u0063\u0061\u006c\u0069\u0062\u0072a\u0074\u0065\u0064\u0020\u0063\u006fl\u006f\u0072 \u0073\u0070\u0061c\u0065\u0020\u0069\u0073\u0020\u0075\u0073\u0065\u0064\u0020\u0069\u006e\u0020\u0061\u0020\u0066\u0069\u006c\u0065 \u0074\u0068\u0065\u006e \u0074\u0068\u0061\u0074 \u0066\u0069\u006c\u0065\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0063o\u006e\u0074\u0061\u0069\u006e\u0020\u0061\u0020\u0050\u0044\u0046\u002f\u0041-\u0031\u0020\u004f\u0075\u0074\u0070\u0075\u0074\u0049\u006e\u0074\u0065\u006e\u0074\u002c\u0020\u0061\u0073\u0020\u0064\u0065\u0066\u0069\u006e\u0065d\u0020\u0069\u006e\u0020\u0036\u002e\u0032\u002e\u0032\u002e"));
_fgdeg =true ;if _bdeac (){return true ;};};};};if _aabb .Alternate !=nil {return _cdaaa (_aabb .Alternate );};};return false ;};for _ ,_edfa :=range _fcaf .GetObjectNums (){_agcf ,_cad :=_fcaf .GetIndirectObjectByNumber (_edfa );if _cad !=nil {continue ;
};_edcee ,_afgd :=_ag .GetStream (_agcf );if !_afgd {continue ;};_ffce ,_afgd :=_ag .GetName (_edcee .Get ("\u0054\u0079\u0070\u0065"));if !_afgd ||_ffce .String ()!="\u0058O\u0062\u006a\u0065\u0063\u0074"{continue ;};_ccdbf ,_afgd :=_ag .GetName (_edcee .Get ("\u0053u\u0062\u0074\u0079\u0070\u0065"));
if !_afgd {continue ;};_ccec [_edcee ]=struct{}{};switch _ccdbf .String (){case "\u0049\u006d\u0061g\u0065":_ceab ,_eebd :=_a .NewXObjectImageFromStream (_edcee );if _eebd !=nil {continue ;};_ccec [_edcee ]=struct{}{};if _cdaaa (_ceab .ColorSpace ){return _deag ;
};case "\u0046\u006f\u0072\u006d":_aadg ,_dcdg :=_ag .GetDict (_edcee .Get ("\u0047\u0072\u006fu\u0070"));if !_dcdg {continue ;};_dfag :=_aadg .Get ("\u0043\u0053");if _dfag ==nil {continue ;};_begc ,_bfeg :=_a .NewPdfColorspaceFromPdfObject (_dfag );if _bfeg !=nil {continue ;
};if _cdaaa (_begc ){return _deag ;};};};for _ ,_edeed :=range _fcaf .PageList {_bedda ,_dcab :=_edeed .GetContentStreams ();if _dcab !=nil {continue ;};for _ ,_dbca :=range _bedda {_aaag ,_geag :=_fbd .NewContentStreamParser (_dbca ).Parse ();if _geag !=nil {continue ;
};for _ ,_degdg :=range *_aaag {if len (_degdg .Params )> 1{continue ;};switch _degdg .Operand {case "\u0042\u0049":_bccdb ,_fbec :=_degdg .Params [0].(*_fbd .ContentStreamInlineImage );if !_fbec {continue ;};_aefg ,_edfe :=_bccdb .GetColorSpace (_edeed .Resources );
if _edfe !=nil {continue ;};if _cdaaa (_aefg ){return _deag ;};case "\u0044\u006f":_babd ,_gegcg :=_ag .GetName (_degdg .Params [0]);if !_gegcg {continue ;};_ceaa ,_ecfe :=_edeed .Resources .GetXObjectByName (*_babd );if _ ,_edef :=_ccec [_ceaa ];_edef {continue ;
};switch _ecfe {case _a .XObjectTypeImage :_aebb ,_cgab :=_a .NewXObjectImageFromStream (_ceaa );if _cgab !=nil {continue ;};_ccec [_ceaa ]=struct{}{};if _cdaaa (_aebb .ColorSpace ){return _deag ;};case _a .XObjectTypeForm :_eedb ,_gceg :=_ag .GetDict (_ceaa .Get ("\u0047\u0072\u006fu\u0070"));
if !_gceg {continue ;};_geb ,_gceg :=_ag .GetName (_eedb .Get ("\u0043\u0053"));if !_gceg {continue ;};_dega ,_dagdc :=_a .NewPdfColorspaceFromPdfObject (_geb );if _dagdc !=nil {continue ;};_ccec [_ceaa ]=struct{}{};if _cdaaa (_dega ){return _deag ;};};
};};};};if !_aebf {return _deag ;};if (_debd .DeviceClass ==_gcf .DeviceClassPRTR ||_debd .DeviceClass ==_gcf .DeviceClassMNTR )&&(_debd .ColorSpace ==_gcf .ColorSpaceRGB ||_debd .ColorSpace ==_gcf .ColorSpaceCMYK ||_debd .ColorSpace ==_gcf .ColorSpaceGRAY ){return _deag ;
};if !_aad {return _deag ;};_eegea ,_abfd :=_gfcb (_fcaf );if !_abfd {return _deag ;};_ccga ,_abfd :=_ag .GetArray (_eegea .Get ("\u004f\u0075\u0074\u0070\u0075\u0074\u0049\u006e\u0074\u0065\u006e\u0074\u0073"));if !_abfd {_deag =append (_deag ,_bb ("\u0036.\u0032\u002e\u0032\u002d\u0031","\u0041\u0020\u0050\u0044\u0046\u002f\u0041\u002d\u0031\u0020\u004f\u0075\u0074p\u0075\u0074\u0049\u006e\u0074e\u006e\u0074\u0020\u0069\u0073\u0020a\u006e \u004f\u0075\u0074\u0070\u0075\u0074\u0049n\u0074\u0065\u006e\u0074\u0020\u0064i\u0063\u0074\u0069\u006fn\u0061\u0072\u0079\u002c\u0020\u0061\u0073\u0020\u0064\u0065\u0066\u0069\u006e\u0065\u0064\u0020\u0062y\u0020\u0050\u0044F\u0020\u0052\u0065\u0066\u0065\u0072\u0065\u006e\u0063\u0065 \u0039\u002e\u0031\u0030.4\u002c\u0020\u0074\u0068\u0061\u0074\u0020\u0069\u0073 \u0069\u006e\u0063\u006c\u0075\u0064e\u0064\u0020i\u006e\u0020\u0074\u0068\u0065\u0020\u0066\u0069\u006c\u0065\u0027\u0073\u0020O\u0075\u0074p\u0075\u0074I\u006e\u0074\u0065\u006e\u0074\u0073\u0020\u0061\u0072\u0072\u0061\u0079\u0020a\u006e\u0064\u0020h\u0061\u0073\u0020\u0047\u0054\u0053\u005f\u0050\u0044\u0046\u0041\u0031\u0020\u0061\u0073 \u0074\u0068\u0065\u0020\u0076a\u006c\u0075e\u0020\u006f\u0066\u0020i\u0074\u0073 \u0053\u0020\u006b\u0065\u0079\u0020\u0061\u006e\u0064\u0020\u0061\u0020\u0076\u0061\u006c\u0069\u0064\u0020I\u0043\u0043\u0020\u0070\u0072\u006f\u0066\u0069\u006ce\u0020s\u0074\u0072\u0065\u0061\u006d \u0061\u0073\u0020\u0074h\u0065\u0020\u0076a\u006c\u0075\u0065\u0020\u0069\u0074\u0073\u0020\u0044\u0065\u0073t\u004f\u0075t\u0070\u0075\u0074P\u0072\u006f\u0066\u0069\u006c\u0065 \u006b\u0065\u0079\u002e"),_bb ("\u0036.\u0032\u002e\u0032\u002d\u0032","\u0049\u0066\u0020\u0061\u0020\u0066\u0069\u006c\u0065's\u0020O\u0075\u0074\u0070u\u0074\u0049\u006e\u0074\u0065\u006e\u0074\u0073 \u0061\u0072\u0072a\u0079\u0020\u0063\u006f\u006e\u0074\u0061\u0069n\u0073\u0020\u006d\u006f\u0072\u0065\u0020\u0074\u0068a\u006e\u0020\u006f\u006ee\u0020\u0065\u006e\u0074\u0072\u0079\u002c\u0020\u0074\u0068\u0065\u006e\u0020\u0061\u006c\u006c\u0020\u0065n\u0074\u0072\u0069\u0065\u0073\u0020\u0074\u0068\u0061\u0074\u0020\u0063\u006f\u006e\u0074\u0061\u0069\u006e a \u0044\u0065\u0073\u0074\u004f\u0075\u0074\u0070\u0075\u0074\u0050\u0072\u006f\u0066\u0069\u006c\u0065\u0020\u006b\u0065y\u0020\u0073\u0068\u0061\u006cl\u0020\u0068\u0061\u0076\u0065 \u0061\u0073\u0020\u0074\u0068\u0065 \u0076\u0061\u006c\u0075\u0065\u0020\u006f\u0066\u0020\u0074\u0068a\u0074\u0020\u006b\u0065\u0079 \u0074\u0068\u0065\u0020\u0073\u0061\u006d\u0065\u0020\u0069\u006e\u0064\u0069\u0072\u0065c\u0074\u0020\u006fb\u006ae\u0063t\u002c\u0020\u0077h\u0069\u0063\u0068\u0020\u0073\u0068\u0061\u006c\u006c \u0062\u0065\u0020\u0061\u0020\u0076\u0061\u006c\u0069d\u0020\u0049\u0043\u0043\u0020\u0070\u0072\u006f\u0066\u0069\u006c\u0065\u0020\u0073\u0074r\u0065\u0061m\u002e"));
return _deag ;};if _ccga .Len ()> 1{_ggbb :=map[*_ag .PdfObjectDictionary ]struct{}{};for _ffdff :=0;_ffdff < _ccga .Len ();_ffdff ++{_egfb ,_fdcg :=_ag .GetDict (_ccga .Get (_ffdff ));if !_fdcg {continue ;};if _ffdff ==0{_ggbb [_egfb ]=struct{}{};continue ;
};if _ ,_ebcd :=_ggbb [_egfb ];!_ebcd {_deag =append (_deag ,_bb ("\u0036.\u0032\u002e\u0032\u002d\u0032","\u0049\u0066\u0020\u0061\u0020\u0066\u0069\u006c\u0065's\u0020O\u0075\u0074\u0070u\u0074\u0049\u006e\u0074\u0065\u006e\u0074\u0073 \u0061\u0072\u0072a\u0079\u0020\u0063\u006f\u006e\u0074\u0061\u0069n\u0073\u0020\u006d\u006f\u0072\u0065\u0020\u0074\u0068a\u006e\u0020\u006f\u006ee\u0020\u0065\u006e\u0074\u0072\u0079\u002c\u0020\u0074\u0068\u0065\u006e\u0020\u0061\u006c\u006c\u0020\u0065n\u0074\u0072\u0069\u0065\u0073\u0020\u0074\u0068\u0061\u0074\u0020\u0063\u006f\u006e\u0074\u0061\u0069\u006e a \u0044\u0065\u0073\u0074\u004f\u0075\u0074\u0070\u0075\u0074\u0050\u0072\u006f\u0066\u0069\u006c\u0065\u0020\u006b\u0065y\u0020\u0073\u0068\u0061\u006cl\u0020\u0068\u0061\u0076\u0065 \u0061\u0073\u0020\u0074\u0068\u0065 \u0076\u0061\u006c\u0075\u0065\u0020\u006f\u0066\u0020\u0074\u0068a\u0074\u0020\u006b\u0065\u0079 \u0074\u0068\u0065\u0020\u0073\u0061\u006d\u0065\u0020\u0069\u006e\u0064\u0069\u0072\u0065c\u0074\u0020\u006fb\u006ae\u0063t\u002c\u0020\u0077h\u0069\u0063\u0068\u0020\u0073\u0068\u0061\u006c\u006c \u0062\u0065\u0020\u0061\u0020\u0076\u0061\u006c\u0069d\u0020\u0049\u0043\u0043\u0020\u0070\u0072\u006f\u0066\u0069\u006c\u0065\u0020\u0073\u0074r\u0065\u0061m\u002e"));
break ;};};};return _deag ;};

// StandardName gets the name of the standard.
func (_dfbg *profile2 )StandardName ()string {return _fg .Sprintf ("\u0050D\u0046\u002f\u0041\u002d\u0032\u0025s",_dfbg ._ebecg ._db );};

// ValidateStandard checks if provided input CompliancePdfReader matches rules that conforms PDF/A-2 standard.
func (_cbgb *profile2 )ValidateStandard (r *_a .CompliancePdfReader )error {_bacf :=VerificationError {ConformanceLevel :_cbgb ._ebecg ._dg ,ConformanceVariant :_cbgb ._ebecg ._db };if _eebf :=_eada (r );_eebf !=_eag {_bacf .ViolatedRules =append (_bacf .ViolatedRules ,_eebf );
};if _ffdf :=_gddfg (r );_ffdf !=_eag {_bacf .ViolatedRules =append (_bacf .ViolatedRules ,_ffdf );};if _ccgf :=_dgf (r );_ccgf !=_eag {_bacf .ViolatedRules =append (_bacf .ViolatedRules ,_ccgf );};if _cdaad :=_ebcf (r );_cdaad !=_eag {_bacf .ViolatedRules =append (_bacf .ViolatedRules ,_cdaad );
};if _gcec :=_aafg (r );_gcec !=_eag {_bacf .ViolatedRules =append (_bacf .ViolatedRules ,_gcec );};if _febe :=_dcfb (r );len (_febe )!=0{_bacf .ViolatedRules =append (_bacf .ViolatedRules ,_febe ...);};if _efbb :=_efcf (r );len (_efbb )!=0{_bacf .ViolatedRules =append (_bacf .ViolatedRules ,_efbb ...);
};if _agda :=_dcabc (r );len (_agda )!=0{_bacf .ViolatedRules =append (_bacf .ViolatedRules ,_agda ...);};if _cfgg :=_dadda (r );_cfgg !=_eag {_bacf .ViolatedRules =append (_bacf .ViolatedRules ,_cfgg );};if _bgcg :=_afcf (r );len (_bgcg )!=0{_bacf .ViolatedRules =append (_bacf .ViolatedRules ,_bgcg ...);
};if _cdef :=_cgga (r );len (_cdef )!=0{_bacf .ViolatedRules =append (_bacf .ViolatedRules ,_cdef ...);};if _gdea :=_dgdbc (r );_gdea !=_eag {_bacf .ViolatedRules =append (_bacf .ViolatedRules ,_gdea );};if _gcfbe :=_cedc (r );len (_gcfbe )!=0{_bacf .ViolatedRules =append (_bacf .ViolatedRules ,_gcfbe ...);
};if _adca :=_bcdd (r );len (_adca )!=0{_bacf .ViolatedRules =append (_bacf .ViolatedRules ,_adca ...);};if _dbcb :=_agbe (r );_dbcb !=_eag {_bacf .ViolatedRules =append (_bacf .ViolatedRules ,_dbcb );};if _fbbe :=_edfcbd (r );len (_fbbe )!=0{_bacf .ViolatedRules =append (_bacf .ViolatedRules ,_fbbe ...);
};if _gcg :=_cecfd (r );len (_gcg )!=0{_bacf .ViolatedRules =append (_bacf .ViolatedRules ,_gcg ...);};if _dfeb :=_fdbg (r );_dfeb !=_eag {_bacf .ViolatedRules =append (_bacf .ViolatedRules ,_dfeb );};if _agcb :=_bbcg (r );len (_agcb )!=0{_bacf .ViolatedRules =append (_bacf .ViolatedRules ,_agcb ...);
};if _gfcd :=_ggbd (r ,_cbgb ._ebecg );len (_gfcd )!=0{_bacf .ViolatedRules =append (_bacf .ViolatedRules ,_gfcd ...);};if _dcgb :=_eeffc (r );len (_dcgb )!=0{_bacf .ViolatedRules =append (_bacf .ViolatedRules ,_dcgb ...);};if _fedc :=_fffdf (r );len (_fedc )!=0{_bacf .ViolatedRules =append (_bacf .ViolatedRules ,_fedc ...);
};if _dbda :=_bbcb (r );len (_dbda )!=0{_bacf .ViolatedRules =append (_bacf .ViolatedRules ,_dbda ...);};if _bgccg :=_dgcb (r );_bgccg !=_eag {_bacf .ViolatedRules =append (_bacf .ViolatedRules ,_bgccg );};if _cae :=_agged (r );len (_cae )!=0{_bacf .ViolatedRules =append (_bacf .ViolatedRules ,_cae ...);
};if _caef :=_fedab (r );_caef !=_eag {_bacf .ViolatedRules =append (_bacf .ViolatedRules ,_caef );};if _gcbb :=_cggb (r ,_cbgb ._ebecg ,false );len (_gcbb )!=0{_bacf .ViolatedRules =append (_bacf .ViolatedRules ,_gcbb ...);};if _cbgb ._ebecg ==_aea (){if _cfde :=_daag (r );
len (_cfde )!=0{_bacf .ViolatedRules =append (_bacf .ViolatedRules ,_cfde ...);};};if _dbgc :=_abdf (r );len (_dbgc )!=0{_bacf .ViolatedRules =append (_bacf .ViolatedRules ,_dbgc ...);};if _cbde :=_eddf (r );len (_cbde )!=0{_bacf .ViolatedRules =append (_bacf .ViolatedRules ,_cbde ...);
};if _bbca :=_efccg (r );len (_bbca )!=0{_bacf .ViolatedRules =append (_bacf .ViolatedRules ,_bbca ...);};if _dege :=_dbbg (r );_dege !=_eag {_bacf .ViolatedRules =append (_bacf .ViolatedRules ,_dege );};if len (_bacf .ViolatedRules )> 0{_d .Slice (_bacf .ViolatedRules ,func (_bdbd ,_fbada int )bool {return _bacf .ViolatedRules [_bdbd ].RuleNo < _bacf .ViolatedRules [_fbada ].RuleNo ;
});return _bacf ;};return nil ;};

// Error implements error interface.
func (_cc VerificationError )Error ()string {_eb :=_dd .Builder {};_eb .WriteString ("\u0053\u0074\u0061\u006e\u0064\u0061\u0072\u0064\u003a\u0020");_eb .WriteString (_fg .Sprintf ("\u0050\u0044\u0046\u002f\u0041\u002d\u0025\u0064\u0025\u0073",_cc .ConformanceLevel ,_cc .ConformanceVariant ));
_eb .WriteString ("\u0020\u0056\u0069\u006f\u006c\u0061\u0074\u0065\u0064\u0020\u0072\u0075l\u0065\u0073\u003a\u0020");for _bcb ,_bcg :=range _cc .ViolatedRules {_eb .WriteString (_bcg .String ());if _bcb !=len (_cc .ViolatedRules )-1{_eb .WriteRune ('\n');
};};return _eb .String ();};func _dad (_ebec *_gc .Document ,_deee standardType ,_dccea *_gc .OutputIntents )error {var (_ccdc *_a .PdfOutputIntent ;_fed error ;);if _ebec .Version .Minor <=7{_ccdc ,_fed =_gcf .NewSRGBv2OutputIntent (_deee .outputIntentSubtype ());
}else {_ccdc ,_fed =_gcf .NewSRGBv4OutputIntent (_deee .outputIntentSubtype ());};if _fed !=nil {return _fed ;};if _fed =_dccea .Add (_ccdc .ToPdfObject ());_fed !=nil {return _fed ;};return nil ;};

// DefaultProfile3Options the default options for the Profile3.
func DefaultProfile3Options ()*Profile3Options {return &Profile3Options {Now :_e .Now ,Xmp :XmpOptions {MarshalIndent :"\u0009"}};};func _fbad ()standardType {return standardType {_dg :1,_db :"\u0041"}};func _bedf (_aedgb *_gc .Document )error {_beeb ,_dca :=_aedgb .FindCatalog ();
if !_dca {return _ce .New ("\u0063\u0061\u0074\u0061\u006c\u006f\u0067\u0020\u006e\u006f\u0074\u0020f\u006f\u0075\u006e\u0064");};_egf ,_dca :=_ag .GetDict (_beeb .Object .Get ("\u0050\u0065\u0072m\u0073"));if _dca {_eeb :=_ag .MakeDict ();_dcd :=_egf .Keys ();
for _ ,_fcfd :=range _dcd {if _fcfd .String ()=="\u0055\u0052\u0033"||_fcfd .String ()=="\u0044\u006f\u0063\u004d\u0044\u0050"{_eeb .Set (_fcfd ,_egf .Get (_fcfd ));};};_beeb .Object .Set ("\u0050\u0065\u0072m\u0073",_eeb );};return nil ;};

// Profile2Options are the options that changes the way how optimizer may try to adapt document into PDF/A standard.
type Profile2Options struct{

// CMYKDefaultColorSpace is an option that refers PDF/A
CMYKDefaultColorSpace bool ;

// Now is a function that returns current time.
Now func ()_e .Time ;

// Xmp is the xmp options information.
Xmp XmpOptions ;};func _bdeg (_ebda *_a .CompliancePdfReader )ViolatedRule {if _ebda .ParserMetadata ().HeaderPosition ()!=0{return _bb ("\u0036.\u0031\u002e\u0032\u002d\u0031","h\u0065\u0061\u0064\u0065\u0072\u0020\u0070\u006f\u0073\u0069\u0074\u0069\u006f\u006e\u0020\u0069\u0073\u0020n\u006f\u0074\u0020\u0061\u0074\u0020\u0074\u0068\u0065\u0020fi\u0072\u0073\u0074 \u0062y\u0074\u0065");
};return _eag ;};func _bc ()standardType {return standardType {_dg :2,_db :"\u0042"}};func _cgaba (_ebgf *_a .CompliancePdfReader )(*_ag .PdfObjectDictionary ,bool ){_cbeae ,_dcgd :=_gfcb (_ebgf );if !_dcgd {return nil ,false ;};_abgc ,_dcgd :=_ag .GetArray (_cbeae .Get ("\u004f\u0075\u0074\u0070\u0075\u0074\u0049\u006e\u0074\u0065\u006e\u0074\u0073"));
if !_dcgd {return nil ,false ;};if _abgc .Len ()==0{return nil ,false ;};return _ag .GetDict (_abgc .Get (0));};func _fgf (_fdeb *_gc .Document ,_eafb int )error {_faee :=map[*_ag .PdfObjectStream ]struct{}{};for _ ,_abeb :=range _fdeb .Objects {_gcfe ,_aeed :=_ag .GetStream (_abeb );
if !_aeed {continue ;};if _ ,_aeed =_faee [_gcfe ];_aeed {continue ;};_faee [_gcfe ]=struct{}{};_gceb ,_aeed :=_ag .GetName (_gcfe .Get ("\u0053u\u0062\u0054\u0079\u0070\u0065"));if !_aeed {continue ;};if _gcfe .Get ("\u0052\u0065\u0066")!=nil {_gcfe .Remove ("\u0052\u0065\u0066");
};if _gceb .String ()=="\u0050\u0053"{_gcfe .Remove ("\u0050\u0053");continue ;};if _gceb .String ()=="\u0046\u006f\u0072\u006d"{if _gcfe .Get ("\u004f\u0050\u0049")!=nil {_gcfe .Remove ("\u004f\u0050\u0049");};if _gcfe .Get ("\u0050\u0053")!=nil {_gcfe .Remove ("\u0050\u0053");
};if _adae :=_gcfe .Get ("\u0053\u0075\u0062\u0074\u0079\u0070\u0065\u0032");_adae !=nil {if _eggge ,_ebdd :=_ag .GetName (_adae );_ebdd &&*_eggge =="\u0050\u0053"{_gcfe .Remove ("\u0053\u0075\u0062\u0074\u0079\u0070\u0065\u0032");};};continue ;};if _gceb .String ()=="\u0049\u006d\u0061g\u0065"{_bdde ,_affbc :=_ag .GetBool (_gcfe .Get ("I\u006e\u0074\u0065\u0072\u0070\u006f\u006c\u0061\u0074\u0065"));
if _affbc &&bool (*_bdde ){_gcfe .Set ("I\u006e\u0074\u0065\u0072\u0070\u006f\u006c\u0061\u0074\u0065",_ag .MakeBool (false ));};if _eafb ==2{if _gcfe .Get ("\u004f\u0050\u0049")!=nil {_gcfe .Remove ("\u004f\u0050\u0049");};};if _gcfe .Get ("\u0041\u006c\u0074\u0065\u0072\u006e\u0061\u0074\u0065\u0073")!=nil {_gcfe .Remove ("\u0041\u006c\u0074\u0065\u0072\u006e\u0061\u0074\u0065\u0073");
};continue ;};};return nil ;};func _fae (_fdb *_gc .Document )error {_dgb :=func (_df *_ag .PdfObjectDictionary )error {if _bcc :=_df .Get ("\u0053\u004d\u0061s\u006b");_bcc !=nil {_df .Set ("\u0053\u004d\u0061s\u006b",_ag .MakeName ("\u004e\u006f\u006e\u0065"));
};_gdb :=_df .Get ("\u0043\u0041");if _gdb !=nil {_ef ,_gee :=_ag .GetNumberAsFloat (_gdb );if _gee !=nil {_g .Log .Debug ("\u0045x\u0074\u0047S\u0074\u0061\u0074\u0065 \u006f\u0062\u006ae\u0063\u0074\u0020\u0043\u0041\u0020\u0076\u0061\u006cue\u0020\u0069\u0073 \u006e\u006ft\u0020\u0061\u0020\u0066\u006c\u006fa\u0074\u003a \u0025\u0076",_gee );
_ef =0;};if _ef !=1.0{_df .Set ("\u0043\u0041",_ag .MakeFloat (1.0));};};_gdb =_df .Get ("\u0063\u0061");if _gdb !=nil {_ddb ,_daa :=_ag .GetNumberAsFloat (_gdb );if _daa !=nil {_g .Log .Debug ("\u0045\u0078t\u0047\u0053\u0074\u0061\u0074\u0065\u0020\u006f\u0062\u006a\u0065\u0063\u0074\u0020\u0027\u0063\u0061\u0027\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u0069\u0073\u0020\u006e\u006f\u0074\u0020\u0061\u0020\u0066\u006c\u006f\u0061\u0074\u003a\u0020\u0025\u0076",_daa );
_ddb =0;};if _ddb !=1.0{_df .Set ("\u0063\u0061",_ag .MakeFloat (1.0));};};_gad :=_df .Get ("\u0042\u004d");if _gad !=nil {_faf ,_dcb :=_ag .GetName (_gad );if !_dcb {_g .Log .Debug ("E\u0078\u0074\u0047\u0053\u0074\u0061t\u0065\u0020\u006f\u0062\u006a\u0065c\u0074\u0020\u0027\u0042\u004d\u0027\u0020i\u0073\u0020\u006e\u006f\u0074\u0020\u0061\u0020\u004e\u0061m\u0065");
_faf =_ag .MakeName ("");};_cebb :=_faf .String ();switch _cebb {case "\u004e\u006f\u0072\u006d\u0061\u006c","\u0043\u006f\u006d\u0070\u0061\u0074\u0069\u0062\u006c\u0065":default:_df .Set ("\u0042\u004d",_ag .MakeName ("\u004e\u006f\u0072\u006d\u0061\u006c"));
};};_ecg :=_df .Get ("\u0054\u0052");if _ecg !=nil {_g .Log .Debug ("\u0045\u0078\u0074\u0047\u0053\u0074\u0061\u0074\u0065\u0020\u006f\u0062\u006a\u0065\u0063t\u0020c\u006f\u006e\u0074\u0061\u0069\u006e\u0073\u0020\u0054\u0052\u0020\u006b\u0065\u0079");
_df .Remove ("\u0054\u0052");};_ead :=_df .Get ("\u0054\u0052\u0032");if _ead !=nil {_bcce :=_ead .String ();if _bcce !="\u0044e\u0066\u0061\u0075\u006c\u0074"{_g .Log .Debug ("\u0045x\u0074\u0047\u0053\u0074\u0061\u0074\u0065 o\u0062\u006a\u0065\u0063\u0074\u0020\u0063\u006f\u006e\u0074\u0061\u0069\u006e\u0073 \u0054\u00522\u0020\u006b\u0065y\u0020\u0077\u0069\u0074\u0068\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u006f\u0074\u0068\u0065r\u0020\u0074ha\u006e\u0020\u0044e\u0066\u0061\u0075\u006c\u0074");
_df .Set ("\u0054\u0052\u0032",_ag .MakeName ("\u0044e\u0066\u0061\u0075\u006c\u0074"));};};return nil ;};_fge ,_fdd :=_fdb .GetPages ();if !_fdd {return nil ;};for _ ,_ba :=range _fge {_gea ,_bfcf :=_ba .GetResources ();if !_bfcf {continue ;};_ad ,_ddbg :=_ag .GetDict (_gea .Get ("\u0045x\u0074\u0047\u0053\u0074\u0061\u0074e"));
if !_ddbg {return nil ;};_cec :=_ad .Keys ();for _ ,_eafe :=range _cec {_bg ,_dbb :=_ag .GetDict (_ad .Get (_eafe ));if !_dbb {continue ;};_eaga :=_dgb (_bg );if _eaga !=nil {continue ;};};};for _ ,_fcd :=range _fge {_bgf ,_gfa :=_fcd .GetContents ();if !_gfa {return nil ;
};for _ ,_fdea :=range _bgf {_eff ,_egg :=_fdea .GetData ();if _egg !=nil {continue ;};_cf :=_fbd .NewContentStreamParser (string (_eff ));_aac ,_egg :=_cf .Parse ();if _egg !=nil {continue ;};for _ ,_abec :=range *_aac {if len (_abec .Params )==0{continue ;
};_ ,_cbe :=_ag .GetName (_abec .Params [0]);if !_cbe {continue ;};_cggg ,_fca :=_fcd .GetResourcesXObject ();if !_fca {continue ;};for _ ,_gbca :=range _cggg .Keys (){_beg ,_fac :=_ag .GetStream (_cggg .Get (_gbca ));if !_fac {continue ;};_bge ,_fac :=_ag .GetDict (_beg .Get ("\u0052e\u0073\u006f\u0075\u0072\u0063\u0065s"));
if !_fac {continue ;};_begf ,_fac :=_ag .GetDict (_bge .Get ("\u0045x\u0074\u0047\u0053\u0074\u0061\u0074e"));if !_fac {continue ;};for _ ,_abeca :=range _begf .Keys (){_gade ,_gbcf :=_ag .GetDict (_begf .Get (_abeca ));if !_gbcf {continue ;};_cgb :=_dgb (_gade );
if _cgb !=nil {continue ;};};};};};};return nil ;};func _fece (_gaf standardType ,_cgff *_gc .OutputIntents )error {_gcce ,_bea :=_gcf .NewISOCoatedV2Gray1CBasOutputIntent (_gaf .outputIntentSubtype ());if _bea !=nil {return _bea ;};if _bea =_cgff .Add (_gcce .ToPdfObject ());
_bea !=nil {return _bea ;};return nil ;};var _ Profile =(*Profile2A )(nil );func _gdcd (_dcbad *_a .PdfInfo ,_abbc func ()_e .Time )error {var _cgaf *_a .PdfDate ;if _dcbad .CreationDate ==nil {_aege ,_fgfe :=_a .NewPdfDateFromTime (_abbc ());if _fgfe !=nil {return _fgfe ;
};_cgaf =&_aege ;_dcbad .CreationDate =_cgaf ;};if _dcbad .ModifiedDate ==nil {if _cgaf !=nil {_geab ,_bbae :=_a .NewPdfDateFromTime (_abbc ());if _bbae !=nil {return _bbae ;};_cgaf =&_geab ;};_dcbad .ModifiedDate =_cgaf ;};return nil ;};func _ggb (_agae *_a .CompliancePdfReader )ViolatedRule {_ecge ,_bdf :=_agae .GetTrailer ();
if _bdf !=nil {_g .Log .Debug ("\u0043\u0061\u006en\u006f\u0074\u0020\u0067e\u0074\u0020\u0064\u006f\u0063\u0075\u006de\u006e\u0074\u0020\u0074\u0072\u0061\u0069\u006c\u0065\u0072\u003a\u0020\u0025\u0076",_bdf );return _eag ;};_gaee ,_aggg :=_ecge .Get ("\u0052\u006f\u006f\u0074").(*_ag .PdfObjectReference );
if !_aggg {_g .Log .Debug ("\u0043a\u006e\u006e\u006f\u0074 \u0066\u0069\u006e\u0064\u0020d\u006fc\u0075m\u0065\u006e\u0074\u0020\u0072\u006f\u006ft");return _eag ;};_bbggf ,_aggg :=_ag .GetDict (_ag .ResolveReference (_gaee ));if !_aggg {_g .Log .Debug ("\u0063\u0061\u006e\u006e\u006f\u0074 \u0072\u0065\u0073\u006f\u006c\u0076\u0065\u0020\u0063\u0061\u0074\u0061\u006co\u0067\u0020\u0064\u0069\u0063\u0074\u0069o\u006e\u0061\u0072\u0079");
return _eag ;};if _bbggf .Get ("\u004f\u0043\u0050r\u006f\u0070\u0065\u0072\u0074\u0069\u0065\u0073")!=nil {return _bb ("\u0036\u002e\u0031\u002e\u0031\u0033\u002d\u0031","\u0054\u0068\u0065\u0020\u0064\u006f\u0063u\u006d\u0065\u006e\u0074\u0020\u0063\u0061\u0074\u0061\u006c\u006f\u0067\u0020\u0064\u0069\u0063\u0074\u0069o\u006e\u0061\u0072\u0079\u0020s\u0068\u0061\u006c\u006c\u0020\u006e\u006f\u0074\u0020c\u006f\u006e\u0074\u0061\u0069\u006e\u0020\u0061\u0020\u006b\u0065\u0079\u0020\u0077\u0069\u0074\u0068\u0020\u0074\u0068\u0065\u0020\u006e\u0061\u006d\u0065\u0020\u004f\u0043\u0050\u0072\u006f\u0070\u0065\u0072\u0074\u0069\u0065\u0073");
};return _eag ;};

// NewProfile2U creates a new Profile2U with the given options.
func NewProfile2U (options *Profile2Options )*Profile2U {if options ==nil {options =DefaultProfile2Options ();};_ecbf (options );return &Profile2U {profile2 {_ece :*options ,_ebecg :_abe ()}};};func _gddfg (_cfgf *_a .CompliancePdfReader )ViolatedRule {_efdc :=_cfgf .ParserMetadata ().HeaderCommentBytes ();
if _efdc [0]> 127&&_efdc [1]> 127&&_efdc [2]> 127&&_efdc [3]> 127{return _eag ;};return _bb ("\u0036.\u0031\u002e\u0032\u002d\u0032","\u0054\u0068\u0065\u0020\u0066\u0069\u006c\u0065\u0020\u0068\u0065\u0061\u0064\u0065\u0072\u0020\u006c\u0069\u006e\u0065\u0020\u0073\u0068\u0061\u006c\u006c b\u0065\u0020i\u006d\u006d\u0065\u0064\u0069a\u0074\u0065\u006c\u0079 \u0066\u006f\u006c\u006co\u0077\u0065\u0064\u0020\u0062\u0079\u0020\u0061\u0020\u0063\u006f\u006d\u006d\u0065n\u0074\u0020\u0063\u006f\u006e\u0073\u0069s\u0074\u0069\u006e\u0067\u0020o\u0066\u0020\u0061\u0020\u0025\u0020\u0063\u0068\u0061\u0072\u0061\u0063\u0074\u0065\u0072\u0020\u0066\u006f\u006c\u006c\u006fwe\u0064\u0020\u0062y\u0020a\u0074\u0009\u006c\u0065a\u0073\u0074\u0020f\u006f\u0075\u0072\u0020\u0063\u0068\u0061\u0072\u0061\u0063\u0074\u0065r\u0073\u002c\u0020e\u0061\u0063\u0068\u0020\u006f\u0066\u0020\u0077\u0068\u006f\u0073\u0065 \u0065\u006e\u0063\u006f\u0064e\u0064\u0020\u0062\u0079\u0074e\u0020\u0076\u0061\u006c\u0075\u0065s\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0068\u0061\u0076\u0065\u0020\u0061\u0020\u0064e\u0063\u0069\u006d\u0061\u006c \u0076\u0061\u006c\u0075\u0065\u0020\u0067\u0072\u0065\u0061\u0074\u0065\u0072\u0020\u0074\u0068\u0061\u006e\u0020\u0031\u0032\u0037\u002e");
};func _ccegb (_cgbac *_a .PdfFont ,_fadd *_ag .PdfObjectDictionary )ViolatedRule {const (_fdef ="\u0036.\u0033\u002e\u0035\u002d\u0032";_bfbd ="\u0046\u006f\u0072\u0020\u0061l\u006c\u0020\u0054\u0079\u0070\u0065\u0020\u0031\u0020\u0066\u006f\u006e\u0074 \u0073\u0075bs\u0065\u0074\u0073 \u0072\u0065\u0066e\u0072\u0065\u006e\u0063\u0065\u0064\u0020\u0077\u0069\u0074\u0068\u0069\u006e\u0020\u0061\u0020\u0063\u006fn\u0066\u006f\u0072\u006d\u0069\u006e\u0067\u0020\u0066\u0069\u006c\u0065\u002c\u0020\u0074he\u0020f\u006f\u006e\u0074\u0020\u0064\u0065s\u0063r\u0069\u0070\u0074o\u0072\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0069\u006ec\u006c\u0075\u0064e\u0020\u0061\u0020\u0043\u0068\u0061\u0072\u0053\u0065\u0074\u0020\u0073\u0074\u0072\u0069\u006e\u0067\u0020\u006c\u0069\u0073\u0074\u0069\u006e\u0067\u0020\u0074\u0068\u0065\u0020\u0063\u0068\u0061\u0072a\u0063\u0074\u0065\u0072 \u006e\u0061\u006d\u0065\u0073\u0020d\u0065\u0066i\u006e\u0065\u0064\u0020i\u006e\u0020\u0074\u0068\u0065\u0020f\u006f\u006e\u0074\u0020s\u0075\u0062\u0073\u0065\u0074, \u0061\u0073 \u0064\u0065s\u0063\u0072\u0069\u0062\u0065\u0064\u0020\u0069\u006e \u0050\u0044\u0046\u0020\u0052e\u0066\u0065\u0072\u0065\u006e\u0063\u0065\u0020\u0054\u0061\u0062\u006ce\u0020\u0035\u002e1\u0038\u002e";
);var _degf string ;if _ebfec ,_dece :=_ag .GetName (_fadd .Get ("\u0053u\u0062\u0074\u0079\u0070\u0065"));_dece {_degf =_ebfec .String ();};if _degf !="\u0054\u0079\u0070e\u0031"{return _eag ;};if _fba .IsStdFont (_fba .StdFontName (_cgbac .BaseFont ())){return _eag ;
};_dgcg :=_cgbac .FontDescriptor ();if _dgcg .CharSet ==nil {return _bb (_fdef ,_bfbd );};return _eag ;};func _abaf (_cbea *Profile3Options ){if _cbea .Now ==nil {_cbea .Now =_e .Now ;};};func _dbf (_ffd ,_degc ,_fcbba ,_bagc string )(string ,bool ){_deca :=_dd .Index (_ffd ,_degc );
if _deca ==-1{return "",false ;};_bdaf :=_dd .Index (_ffd ,_fcbba );if _bdaf ==-1{return "",false ;};if _bdaf < _deca {return "",false ;};return _ffd [:_deca ]+_degc +_bagc +_ffd [_bdaf :],true ;};

// Profile2U is the implementation of the PDF/A-2U standard profile.
// Implements model.StandardImplementer, Profile interfaces.
type Profile2U struct{profile2 };func _fcc (_fbce *_a .CompliancePdfReader )(_cbecg ViolatedRule ){for _ ,_fbcc :=range _fbce .GetObjectNums (){_facg ,_fcca :=_fbce .GetIndirectObjectByNumber (_fbcc );if _fcca !=nil {continue ;};_ggde ,_cfaaf :=_ag .GetStream (_facg );
if !_cfaaf {continue ;};_fffg ,_cfaaf :=_ag .GetName (_ggde .Get ("\u0054\u0079\u0070\u0065"));if !_cfaaf {continue ;};if *_fffg !="\u0058O\u0062\u006a\u0065\u0063\u0074"{continue ;};if _ggde .Get ("\u0052\u0065\u0066")!=nil {return _bb ("\u0036.\u0032\u002e\u0036\u002d\u0031","\u0041\u0020\u0063\u006f\u006e\u0066\u006f\u0072\u006d\u0069\u006e\u0067\u0020\u0066\u0069\u006c\u0065\u0020\u0073\u0068a\u006c\u006c\u0020\u006e\u006f\u0074\u0020\u0063\u006f\u006e\u0074\u0061\u0069\u006e\u0020\u0061\u006e\u0079\u0020\u0072\u0065\u0066\u0065\u0072\u0065\u006e\u0063\u0065\u0020\u0058O\u0062\u006a\u0065\u0063\u0074s\u002e");
};};return _cbecg ;};func _bgebb (_fbcb *_a .CompliancePdfReader )(_bbfg ViolatedRule ){for _ ,_aaagb :=range _fbcb .GetObjectNums (){_efda ,_dcbed :=_fbcb .GetIndirectObjectByNumber (_aaagb );if _dcbed !=nil {continue ;};_gdeac ,_ega :=_ag .GetStream (_efda );
if !_ega {continue ;};_baae ,_ega :=_ag .GetName (_gdeac .Get ("\u0054\u0079\u0070\u0065"));if !_ega {continue ;};if *_baae !="\u0058O\u0062\u006a\u0065\u0063\u0074"{continue ;};_bccdd ,_ega :=_ag .GetName (_gdeac .Get ("\u0053u\u0062\u0074\u0079\u0070\u0065"));
if !_ega {continue ;};if *_bccdd =="\u0050\u0053"{return _bb ("\u0036.\u0032\u002e\u0037\u002d\u0031","A \u0063\u006fn\u0066\u006f\u0072\u006d\u0069\u006e\u0067\u0020\u0066i\u006c\u0065\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u006e\u006f\u0074\u0020\u0063\u006f\u006e\u0074\u0061\u0069\u006e\u0020\u0061\u006e\u0079\u0020\u0050\u006f\u0073t\u0053c\u0072\u0069\u0070\u0074\u0020\u0058\u004f\u0062j\u0065c\u0074\u0073.");
};};return _bbfg ;};func _bgfa (_cffd *Profile1Options ){if _cffd .Now ==nil {_cffd .Now =_e .Now ;};};func _cbda (_bgeb *_gc .Document )error {_afcg ,_eedd :=_bgeb .GetPages ();if !_eedd {return nil ;};for _ ,_fedf :=range _afcg {_ffe ,_accc :=_ag .GetArray (_fedf .Object .Get ("\u0041\u006e\u006e\u006f\u0074\u0073"));
if !_accc {continue ;};for _ ,_cab :=range _ffe .Elements (){_cab =_ag .ResolveReference (_cab );if _ ,_dbaa :=_cab .(*_ag .PdfObjectNull );_dbaa {continue ;};_eebg ,_baef :=_ag .GetDict (_cab );if !_baef {continue ;};_ggdf ,_ :=_ag .GetIntVal (_eebg .Get ("\u0046"));
_ggdf &=^(1<<0);_ggdf &=^(1<<1);_ggdf &=^(1<<5);_ggdf &=^(1<<8);_ggdf |=1<<2;_eebg .Set ("\u0046",_ag .MakeInteger (int64 (_ggdf )));_ddca :=false ;if _gdge :=_eebg .Get ("\u0053u\u0062\u0074\u0079\u0070\u0065");_gdge !=nil {_agc ,_ggea :=_ag .GetName (_gdge );
if _ggea &&_agc .String ()=="\u0057\u0069\u0064\u0067\u0065\u0074"{_ddca =true ;if _eebg .Get ("\u0041\u0041")!=nil {_eebg .Remove ("\u0041\u0041");};if _eebg .Get ("\u0041")!=nil {_eebg .Remove ("\u0041");};};if _ggea &&_agc .String ()=="\u0054\u0065\u0078\u0074"{_becf ,_ :=_ag .GetIntVal (_eebg .Get ("\u0046"));
_becf |=1<<3;_becf |=1<<4;_eebg .Set ("\u0046",_ag .MakeInteger (int64 (_becf )));};};_aaee ,_baef :=_ag .GetDict (_eebg .Get ("\u0041\u0050"));if _baef {_dfe :=_aaee .Get ("\u004e");if _dfe ==nil {continue ;};if len (_aaee .Keys ())> 1{_aaee .Clear ();
_aaee .Set ("\u004e",_dfe );};if _ddca {_fdfg ,_fegdb :=_ag .GetName (_eebg .Get ("\u0046\u0054"));if _fegdb &&*_fdfg =="\u0042\u0074\u006e"{continue ;};};};};};return nil ;};func _badb (_aebcc *_ag .PdfObjectDictionary ,_dcbeg map[*_ag .PdfObjectStream ][]byte ,_bccb map[*_ag .PdfObjectStream ]*_ab .CMap )ViolatedRule {const (_bdba ="\u0036\u002e\u0032\u002e\u0031\u0031\u002e\u0033\u002d\u0033";
_dbeb ="\u0041\u006c\u006c \u0043\u004d\u0061\u0070s\u0020\u0075\u0073ed\u0020\u0077\u0069\u0074\u0068i\u006e\u0020\u0061\u0020\u0050\u0044\u0046\u002f\u0041\u002d\u0032\u0020\u0066\u0069\u006c\u0065\u002c\u0020\u0065\u0078\u0063\u0065\u0070\u0074 th\u006f\u0073\u0065\u0020\u006ci\u0073\u0074\u0065\u0064\u0020i\u006e\u0020\u0049\u0053\u004f\u0020\u0033\u00320\u00300\u002d1\u003a\u0032\u0030\u0030\u0038\u002c\u0020\u0039\u002e\u0037\u002e\u0035\u002e\u0032\u002c\u0020\u0054\u0061\u0062\u006c\u0065 \u0031\u00318,\u0020\u0073h\u0061\u006c\u006c\u0020\u0062\u0065\u0020\u0065\u006d\u0062\u0065\u0064\u0064\u0065\u0064\u0020\u0069\u006e \u0074\u0068\u0061\u0074\u0020\u0066\u0069\u006c\u0065\u0020\u0061\u0073\u0020\u0064e\u0073\u0063\u0072\u0069\u0062\u0065\u0064\u0020\u0069\u006e\u0020\u0049\u0053\u004f\u0020\u0033\u0032\u00300\u0030-\u0031\u003a\u0032\u0030\u0030\u0038\u002c\u00209\u002e\u0037\u002e\u0035\u002e";
);var _fefa string ;if _badg ,_adef :=_ag .GetName (_aebcc .Get ("\u0053u\u0062\u0074\u0079\u0070\u0065"));_adef {_fefa =_badg .String ();};if _fefa !="\u0054\u0079\u0070e\u0030"{return _eag ;};_bcbc :=_aebcc .Get ("\u0045\u006e\u0063\u006f\u0064\u0069\u006e\u0067");
if _bbebc ,_gddac :=_ag .GetName (_bcbc );_gddac {switch _bbebc .String (){case "\u0049\u0064\u0065\u006e\u0074\u0069\u0074\u0079\u002d\u0048","\u0049\u0064\u0065\u006e\u0074\u0069\u0074\u0079\u002d\u0056":return _eag ;default:return _bb (_bdba ,_dbeb );
};};_dacd ,_bdcg :=_ag .GetStream (_bcbc );if !_bdcg {return _bb (_bdba ,_dbeb );};_ ,_bebd :=_gfbb (_dacd ,_dcbeg ,_bccb );if _bebd !=nil {return _bb (_bdba ,_dbeb );};return _eag ;};func _gcfb (_cdc *_gc .Document ,_cdgb standardType ,_ccd XmpOptions )error {_aeb ,_gga :=_cdc .FindCatalog ();
if !_gga {return nil ;};var _accd *_cd .Document ;_fbaa ,_gga :=_aeb .GetMetadata ();if !_gga {_accd =_cd .NewDocument ();}else {var _cdgg error ;_accd ,_cdgg =_cd .LoadDocument (_fbaa .Stream );if _cdgg !=nil {return _cdgg ;};};_cbc :=_cd .PdfInfoOptions {InfoDict :_cdc .Info ,PdfVersion :_fg .Sprintf ("\u0025\u0064\u002e%\u0064",_cdc .Version .Major ,_cdc .Version .Minor ),Copyright :_ccd .Copyright ,Overwrite :true };
_edg ,_gga :=_aeb .GetMarkInfo ();if _gga {_aaec ,_bdd :=_ag .GetBool (_edg .Get ("\u004d\u0061\u0072\u006b\u0065\u0064"));if _bdd &&bool (*_aaec ){_cbc .Marked =true ;};};if _gdd :=_accd .SetPdfInfo (&_cbc );_gdd !=nil {return _gdd ;};if _gfda :=_accd .SetPdfAID (_cdgb ._dg ,_cdgb ._db );
_gfda !=nil {return _gfda ;};_bgfe :=_cd .MediaManagementOptions {OriginalDocumentID :_ccd .OriginalDocumentID ,DocumentID :_ccd .DocumentID ,InstanceID :_ccd .InstanceID ,NewDocumentID :!_ccd .NewDocumentVersion ,ModifyComment :"O\u0070\u0074\u0069\u006d\u0069\u007ae\u0020\u0064\u006f\u0063\u0075\u006de\u006e\u0074\u0020\u0074\u006f\u0020\u0050D\u0046\u002f\u0041\u0020\u0073\u0074\u0061\u006e\u0064\u0061r\u0064"};
_fdg ,_gga :=_ag .GetDict (_cdc .Info );if _gga {if _gce ,_dcbe :=_ag .GetString (_fdg .Get ("\u004do\u0064\u0044\u0061\u0074\u0065"));_dcbe &&_gce .String ()!=""{_fdgf ,_ebb :=_dda .ParsePdfTime (_gce .String ());if _ebb !=nil {return _fg .Errorf ("\u0069n\u0076\u0061\u006c\u0069d\u0020\u004d\u006f\u0064\u0044a\u0074e\u0020f\u0069\u0065\u006c\u0064\u003a\u0020\u0025w",_ebb );
};_bgfe .ModifyDate =_fdgf ;};};if _aca :=_accd .SetMediaManagement (&_bgfe );_aca !=nil {return _aca ;};if _eef :=_accd .SetPdfAExtension ();_eef !=nil {return _eef ;};_ebbc ,_dee :=_accd .MarshalIndent (_ccd .MarshalPrefix ,_ccd .MarshalIndent );if _dee !=nil {return _dee ;
};if _dag :=_aeb .SetMetadata (_ebbc );_dag !=nil {return _dag ;};return nil ;};func _bb (_gb string ,_eaf string )ViolatedRule {return ViolatedRule {RuleNo :_gb ,Detail :_eaf }};func _bce (_cecgc *_gc .Document )error {for _ ,_abba :=range _cecgc .Objects {_deff ,_abf :=_ag .GetDict (_abba );
if !_abf {continue ;};_fdda :=_deff .Get ("\u0054\u0079\u0070\u0065");if _fdda ==nil {continue ;};if _deba ,_gffc :=_ag .GetName (_fdda );_gffc &&_deba .String ()!="\u0041\u0063\u0072\u006f\u0046\u006f\u0072\u006d"{continue ;};_effa ,_effd :=_ag .GetBool (_deff .Get ("\u004ee\u0065d\u0041\u0070\u0070\u0065\u0061\u0072\u0061\u006e\u0063\u0065\u0073"));
if _effd &&bool (*_effa ){_deff .Set ("\u004ee\u0065d\u0041\u0070\u0070\u0065\u0061\u0072\u0061\u006e\u0063\u0065\u0073",_ag .MakeBool (false ));};if _deff .Get ("\u0058\u0046\u0041")!=nil {_deff .Remove ("\u0058\u0046\u0041");};};_gbfb ,_cfb :=_cecgc .FindCatalog ();
if !_cfb {return _ce .New ("\u0063\u0061\u0074\u0061\u006c\u006f\u0067\u0020\u006e\u006f\u0074\u0020f\u006f\u0075\u006e\u0064");};if _gbfb .Object .Get ("\u004e\u0065\u0065\u0064\u0073\u0052\u0065\u006e\u0064e\u0072\u0069\u006e\u0067")!=nil {_gbfb .Object .Remove ("\u004e\u0065\u0065\u0064\u0073\u0052\u0065\u006e\u0064e\u0072\u0069\u006e\u0067");
};return nil ;};

// ApplyStandard tries to change the content of the writer to match the PDF/A-1 standard.
// Implements model.StandardApplier.
func (_eadef *profile1 )ApplyStandard (document *_gc .Document )(_faca error ){_dgc (document ,4);if _faca =_eec (document ,_eadef ._fcgb .Now );_faca !=nil {return _faca ;};if _faca =_cea (document );_faca !=nil {return _faca ;};_dbag ,_cdbd :=_adf (_eadef ._fcgb .CMYKDefaultColorSpace ,_eadef ._acab );
_faca =_eegf (document ,[]pageColorspaceOptimizeFunc {_ggga ,_dbag },[]documentColorspaceOptimizeFunc {_cdbd });if _faca !=nil {return _faca ;};_dge (document );if _faca =_fgf (document ,_eadef ._acab ._dg );_faca !=nil {return _faca ;};if _faca =_ebf (document );
_faca !=nil {return _faca ;};if _faca =_bbg (document );_faca !=nil {return _faca ;};if _faca =_fae (document );_faca !=nil {return _faca ;};if _faca =_ggf (document );_faca !=nil {return _faca ;};if _eadef ._acab ._db =="\u0041"{_agdd (document );};if _faca =_bcag (document ,_eadef ._acab ._dg );
_faca !=nil {return _faca ;};if _faca =_bfga (document );_faca !=nil {return _faca ;};if _ccfc :=_gcfb (document ,_eadef ._acab ,_eadef ._fcgb .Xmp );_ccfc !=nil {return _ccfc ;};if _eadef ._acab ==_fbad (){if _faca =_gcdd (document );_faca !=nil {return _faca ;
};};if _faca =_fbab (document );_faca !=nil {return _faca ;};return nil ;};

// ApplyStandard tries to change the content of the writer to match the PDF/A-2 standard.
// Implements model.StandardApplier.
func (_ggcc *profile2 )ApplyStandard (document *_gc .Document )(_dde error ){_dgc (document ,7);if _dde =_eec (document ,_ggcc ._ece .Now );_dde !=nil {return _dde ;};if _dde =_cea (document );_dde !=nil {return _dde ;};_eegd ,_ffba :=_adf (_ggcc ._ece .CMYKDefaultColorSpace ,_ggcc ._ebecg );
_dde =_eegf (document ,[]pageColorspaceOptimizeFunc {_eegd },[]documentColorspaceOptimizeFunc {_ffba });if _dde !=nil {return _dde ;};_dge (document );if _dde =_bedf (document );_dde !=nil {return _dde ;};if _dde =_fgf (document ,_ggcc ._ebecg ._dg );_dde !=nil {return _dde ;
};if _dde =_cbda (document );_dde !=nil {return _dde ;};if _dde =_aaeg (document );_dde !=nil {return _dde ;};if _dde =_ggf (document );_dde !=nil {return _dde ;};if _dde =_bce (document );_dde !=nil {return _dde ;};if _ggcc ._ebecg ._db =="\u0041"{_agdd (document );
};if _dde =_bcag (document ,_ggcc ._ebecg ._dg );_dde !=nil {return _dde ;};if _dde =_bfga (document );_dde !=nil {return _dde ;};if _ceac :=_gcfb (document ,_ggcc ._ebecg ,_ggcc ._ece .Xmp );_ceac !=nil {return _ceac ;};if _ggcc ._ebecg ==_aea (){if _dde =_gcdd (document );
_dde !=nil {return _dde ;};};if _dde =_dcbg (document );_dde !=nil {return _dde ;};if _dde =_aef (document );_dde !=nil {return _dde ;};if _dde =_cgcf (document );_dde !=nil {return _dde ;};return nil ;};type imageInfo struct{ColorSpace _ag .PdfObjectName ;
BitsPerComponent int ;ColorComponents int ;Width int ;Height int ;Stream *_ag .PdfObjectStream ;_ddaf bool ;};func _ebcf (_debg *_a .CompliancePdfReader )ViolatedRule {if _debg .ParserMetadata ().HasDataAfterEOF (){return _bb ("\u0036.\u0031\u002e\u0033\u002d\u0033","\u004e\u006f\u0020\u0064\u0061ta\u0020\u0073h\u0061\u006c\u006c\u0020\u0066\u006f\u006c\u006c\u006f\u0077\u0020\u0074\u0068\u0065\u0020\u006c\u0061\u0073\u0074\u0020\u0065\u006e\u0064\u002d\u006f\u0066\u002d\u0066\u0069l\u0065\u0020\u006da\u0072\u006b\u0065\u0072\u0020\u0065\u0078\u0063\u0065\u0070\u0074\u0020\u0061 \u0073\u0069\u006e\u0067\u006ce\u0020\u006f\u0070\u0074\u0069\u006f\u006e\u0061\u006c \u0065\u006ed\u002do\u0066\u002d\u006c\u0069\u006e\u0065\u0020m\u0061\u0072\u006b\u0065\u0072\u002e");
};return _eag ;};

// Part gets the PDF/A version level.
func (_ccdb *profile3 )Part ()int {return _ccdb ._gfdf ._dg };func _cbdec (_befe *_a .CompliancePdfReader )(_bgfae []ViolatedRule ){var _dcgbc ,_gddd ,_bgag ,_addb ,_bccf ,_ebfe ,_afdd bool ;_edcf :=func ()bool {return _dcgbc &&_gddd &&_bgag &&_addb &&_bccf &&_ebfe &&_afdd };
for _ ,_ffaeg :=range _befe .PageList {if _ffaeg .Resources ==nil {continue ;};_bdee ,_cdga :=_ag .GetDict (_ffaeg .Resources .Font );if !_cdga {continue ;};for _ ,_adcd :=range _bdee .Keys (){_aebe ,_bbgc :=_ag .GetDict (_bdee .Get (_adcd ));if !_bbgc {if !_dcgbc {_bgfae =append (_bgfae ,_bb ("\u0036.\u0033\u002e\u0032\u002d\u0031","\u0041\u006c\u006c\u0020\u0066\u006fn\u0074\u0073\u0020\u0075\u0073e\u0064\u0020\u0069\u006e\u0020\u0061\u0020\u0063\u006f\u006e\u0066\u006f\u0072\u006d\u0069\u006e\u0067\u0020\u0066\u0069\u006c\u0065\u0020\u0073\u0068\u0061\u006c\u006c\u0020c\u006f\u006e\u0066\u006f\u0072m\u0020\u0074\u006f\u0020\u0074\u0068\u0065\u0020\u0066\u006f\u006e\u0074\u0020\u0073\u0070\u0065\u0063\u0069\u0066\u0069\u0063\u0061\u0074\u0069\u006f\u006e\u0073\u0020d\u0065\u0066\u0069\u006e\u0065d \u0069\u006e\u0020\u0050\u0044\u0046\u0020\u0052\u0065\u0066\u0065\u0072\u0065\u006e\u0063\u0065\u0020\u0035\u002e\u0035\u002e"));
_dcgbc =true ;if _edcf (){return _bgfae ;};};continue ;};if _eee ,_gedf :=_ag .GetName (_aebe .Get ("\u0054\u0079\u0070\u0065"));!_dcgbc &&(!_gedf ||_eee .String ()!="\u0046\u006f\u006e\u0074"){_bgfae =append (_bgfae ,_bb ("\u0036.\u0033\u002e\u0032\u002d\u0031","\u0054\u0079\u0070e\u0020\u002d\u0020\u006e\u0061\u006d\u0065\u0020\u002d\u0020\u0028\u0052\u0065\u0071\u0075i\u0072\u0065\u0064\u0029 Th\u0065\u0020\u0074\u0079\u0070\u0065\u0020\u006f\u0066 \u0050\u0044\u0046\u0020\u006fbj\u0065\u0063\u0074\u0020\u0074\u0068\u0061t\u0020\u0074\u0068\u0069s\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u0020\u0064\u0065\u0073c\u0072\u0069\u0062\u0065\u0073\u003b\u0020\u006d\u0075\u0073t\u0020\u0062\u0065\u0020\u0046\u006f\u006e\u0074\u0020\u0066\u006fr\u0020\u0061\u0020\u0066\u006f\u006e\u0074\u0020\u0064\u0069\u0063t\u0069\u006f\u006e\u0061\u0072\u0079\u002e"));
_dcgbc =true ;if _edcf (){return _bgfae ;};};_gdbgf ,_afad :=_a .NewPdfFontFromPdfObject (_aebe );if _afad !=nil {continue ;};var _gcge string ;if _bafg ,_feedc :=_ag .GetName (_aebe .Get ("\u0053u\u0062\u0074\u0079\u0070\u0065"));_feedc {_gcge =_bafg .String ();
};if !_gddd {switch _gcge {case "\u0054\u0079\u0070e\u0030","\u0054\u0079\u0070e\u0031","\u004dM\u0054\u0079\u0070\u0065\u0031","\u0054\u0072\u0075\u0065\u0054\u0079\u0070\u0065","\u0043\u0049\u0044F\u006f\u006e\u0074\u0054\u0079\u0070\u0065\u0030","\u0043\u0049\u0044F\u006f\u006e\u0074\u0054\u0079\u0070\u0065\u0032":default:_gddd =true ;
_bgfae =append (_bgfae ,_bb ("\u0036.\u0033\u002e\u0032\u002d\u0032","\u0053\u0075\u0062\u0074\u0079\u0070\u0065\u0020\u002d\u0020\u006e\u0061\u006d\u0065\u0020\u002d\u0020\u0028\u0052\u0065\u0071\u0075\u0069\u0072\u0065d\u0029\u0020\u0054\u0068e \u0074\u0079\u0070\u0065 \u006f\u0066\u0020\u0066\u006f\u006et\u003b\u0020\u006d\u0075\u0073\u0074\u0020b\u0065\u0020\u0022\u0054\u0079\u0070\u0065\u0031\u0022\u0020f\u006f\u0072\u0020\u0054\u0079\u0070\u0065\u0020\u0031\u0020f\u006f\u006e\u0074\u0073\u002c\u0020\u0022\u004d\u004d\u0054\u0079\u0070\u0065\u0031\u0022\u0020\u0066\u006f\u0072\u0020\u006d\u0075\u006c\u0074\u0069\u0070\u006c\u0065\u0020\u006da\u0073\u0074e\u0072\u0020\u0066\u006f\u006e\u0074s\u002c\u0020\u0022\u0054\u0072\u0075\u0065T\u0079\u0070\u0065\u0022\u0020\u0066\u006f\u0072\u0020\u0054\u0072\u0075\u0065T\u0079\u0070\u0065\u0020\u0066\u006f\u006e\u0074\u0073\u0020\u0022\u0054\u0079\u0070\u0065\u0033\u0022\u0020\u0066\u006f\u0072\u0020\u0054\u0079\u0070e\u0020\u0033\u0020\u0066\u006f\u006e\u0074\u0073\u002c\u0020\"\u0054\u0079\u0070\u0065\u0030\"\u0020\u0066\u006f\u0072\u0020\u0054\u0079\u0070\u0065\u0020\u0030\u0020\u0066\u006f\u006e\u0074\u0073\u0020\u0061\u006ed\u0020\u0022\u0043\u0049\u0044\u0046\u006fn\u0074\u0054\u0079\u0070\u0065\u0030\u0022 \u006f\u0072\u0020\u0022\u0043\u0049\u0044\u0046\u006f\u006e\u0074T\u0079\u0070e\u0032\u0022\u0020\u0066\u006f\u0072\u0020\u0043\u0049\u0044\u0020\u0066\u006f\u006e\u0074\u0073\u002e"));
if _edcf (){return _bgfae ;};};};if !_bgag {if _gcge !="\u0054\u0079\u0070e\u0033"{_ddef ,_aceb :=_ag .GetName (_aebe .Get ("\u0042\u0061\u0073\u0065\u0046\u006f\u006e\u0074"));if !_aceb ||_ddef .String ()==""{_bgfae =append (_bgfae ,_bb ("\u0036.\u0033\u002e\u0032\u002d\u0033","B\u0061\u0073\u0065\u0046\u006f\u006e\u0074\u0020\u002d\u0020\u006e\u0061\u006d\u0065\u0020\u002d\u0020\u0028\u0052\u0065\u0071\u0075\u0069\u0072\u0065\u0064)\u0020T\u0068\u0065\u0020\u0050o\u0073\u0074S\u0063\u0072\u0069\u0070\u0074\u0020\u006e\u0061\u006d\u0065\u0020\u006f\u0066\u0020\u0074\u0068\u0065\u0020\u0066\u006f\u006e\u0074\u002e"));
_bgag =true ;if _edcf (){return _bgfae ;};};};};if _gcge !="\u0054\u0079\u0070e\u0031"{continue ;};_gcea :=_fba .IsStdFont (_fba .StdFontName (_gdbgf .BaseFont ()));if _gcea {continue ;};_accf ,_deda :=_ag .GetIntVal (_aebe .Get ("\u0046i\u0072\u0073\u0074\u0043\u0068\u0061r"));
if !_deda &&!_addb {_bgfae =append (_bgfae ,_bb ("\u0036.\u0033\u002e\u0032\u002d\u0034","\u0046\u0069r\u0073t\u0043\u0068\u0061\u0072\u0020\u002d\u0020\u0069\u006e\u0074\u0065\u0067\u0065\u0072\u0020\u002d\u0020\u0028\u0052\u0065\u0071\u0075i\u0072\u0065\u0064\u0020\u0065\u0078\u0063\u0065\u0070t\u0020\u0066\u006f\u0072\u0020\u0074h\u0065\u0020\u0073\u0074\u0061\u006e\u0064\u0061\u0072d\u0020\u0031\u0034\u0020\u0066\u006f\u006e\u0074\u0073\u0029\u0020\u0054\u0068\u0065\u0020\u0066\u0069\u0072\u0073\u0074\u0020\u0063\u0068\u0061\u0072\u0061\u0063\u0074\u0065\u0072\u0020\u0063\u006f\u0064e\u0020\u0064\u0065\u0066i\u006ee\u0064\u0020\u0069\u006e\u0020\u0074\u0068\u0065\u0020\u0066\u006f\u006e\u0074\u0027\u0073\u0020\u0057i\u0064\u0074\u0068\u0073 \u0061r\u0072\u0061y\u002e"));
_addb =true ;if _edcf (){return _bgfae ;};};_gccd ,_eadc :=_ag .GetIntVal (_aebe .Get ("\u004c\u0061\u0073\u0074\u0043\u0068\u0061\u0072"));if !_eadc &&!_bccf {_bgfae =append (_bgfae ,_bb ("\u0036.\u0033\u002e\u0032\u002d\u0035","\u004c\u0061\u0073t\u0043\u0068\u0061\u0072\u0020\u002d\u0020\u0069n\u0074\u0065\u0067e\u0072 \u002d\u0020\u0028\u0052\u0065\u0071u\u0069\u0072\u0065d\u0020\u0065\u0078\u0063\u0065\u0070\u0074\u0020\u0066\u006f\u0072\u0020t\u0068\u0065 s\u0074\u0061\u006e\u0064\u0061\u0072\u0064\u0020\u0031\u0034\u0020\u0066\u006f\u006ets\u0029\u0020\u0054\u0068\u0065\u0020\u006c\u0061\u0073t\u0020\u0063\u0068\u0061\u0072\u0061\u0063\u0074\u0065\u0072\u0020\u0063\u006f\u0064\u0065\u0020\u0064\u0065\u0066\u0069\u006e\u0065\u0064\u0020\u0069\u006e\u0020\u0074\u0068\u0065\u0020\u0066\u006f\u006e\u0074\u0027\u0073\u0020\u0057\u0069\u0064\u0074h\u0073\u0020\u0061\u0072\u0072\u0061\u0079\u002e"));
_bccf =true ;if _edcf (){return _bgfae ;};};if !_ebfe {_ffcef ,_bgfb :=_ag .GetArray (_aebe .Get ("\u0057\u0069\u0064\u0074\u0068\u0073"));if !_bgfb ||!_deda ||!_eadc ||_ffcef .Len ()!=_gccd -_accf +1{_bgfae =append (_bgfae ,_bb ("\u0036.\u0033\u002e\u0032\u002d\u0036","\u0057\u0069\u0064\u0074\u0068\u0073\u0020\u002d a\u0072\u0072\u0061y \u002d\u0020\u0028\u0052\u0065\u0071\u0075\u0069\u0072\u0065\u0064\u0020\u0065\u0078\u0063\u0065\u0070t\u0020\u0066\u006f\u0072\u0020\u0074\u0068\u0065\u0020\u0073\u0074a\u006e\u0064a\u0072\u0064\u00201\u0034\u0020\u0066\u006f\u006e\u0074\u0073\u003b\u0020\u0069\u006ed\u0069\u0072\u0065\u0063\u0074\u0020\u0072\u0065\u0066\u0065\u0072\u0065\u006e\u0063\u0065\u0020\u0070\u0072\u0065\u0066e\u0072\u0072e\u0064\u0029\u0020\u0041\u006e \u0061\u0072\u0072\u0061\u0079\u0020\u006f\u0066\u0020\u0028\u004c\u0061\u0073\u0074\u0043\u0068\u0061\u0072\u0020\u2212 F\u0069\u0072\u0073\u0074\u0043\u0068\u0061\u0072\u0020\u002b\u00201\u0029\u0020\u0077\u0069\u0064\u0074\u0068\u0073."));
_ebfe =true ;if _edcf (){return _bgfae ;};};};};};return _bgfae ;};func _bcdd (_cdca *_a .CompliancePdfReader )(_acbg []ViolatedRule ){var _cadd ,_dfdae ,_fecf ,_eeed ,_bbcfd ,_adcg ,_ggfbd bool ;_dfeba :=func ()bool {return _cadd &&_dfdae &&_fecf &&_eeed &&_bbcfd &&_adcg &&_ggfbd };
_fcbbe :=func (_fafc *_ag .PdfObjectDictionary )bool {if !_cadd &&_fafc .Get ("\u0054\u0052")!=nil {_cadd =true ;_acbg =append (_acbg ,_bb ("\u0036.\u0032\u002e\u0035\u002d\u0031","\u0041\u006e\u0020\u0045\u0078\u0074\u0047\u0053\u0074\u0061\u0074e\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006ea\u0072y\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u006e\u006f\u0074\u0020\u0063\u006f\u006e\u0074\u0061\u0069\u006e \u0074\u0068\u0065\u0020\u0054\u0052\u0020\u006b\u0065\u0079\u002e"));
};if _ecda :=_fafc .Get ("\u0054\u0052\u0032");!_dfdae &&_ecda !=nil {_bfdbd ,_dgffb :=_ag .GetName (_ecda );if !_dgffb ||(_dgffb &&*_bfdbd !="\u0044e\u0066\u0061\u0075\u006c\u0074"){_dfdae =true ;_acbg =append (_acbg ,_bb ("\u0036.\u0032\u002e\u0035\u002d\u0032","\u0041\u006e \u0045\u0078\u0074G\u0053\u0074\u0061\u0074\u0065 \u0064\u0069\u0063\u0074\u0069on\u0061\u0072\u0079\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u006e\u006f\u0074\u0020\u0063\u006f\u006e\u0074a\u0069n\u0020\u0074\u0068\u0065\u0020\u0054R2 \u006b\u0065\u0079\u0020\u0077\u0069\u0074\u0068\u0020\u0061\u0020\u0076al\u0075e\u0020\u006f\u0074\u0068e\u0072 \u0074h\u0061\u006e \u0044\u0065fa\u0075\u006c\u0074\u002e"));
if _dfeba (){return true ;};};};if !_fecf &&_fafc .Get ("\u0048\u0054\u0050")!=nil {_fecf =true ;_acbg =append (_acbg ,_bb ("\u0036.\u0032\u002e\u0035\u002d\u0033","\u0041\u006e\u0020\u0045\u0078\u0074\u0047\u0053\u0074\u0061\u0074\u0065\u0020\u0064\u0069c\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u0020\u0073\u0068\u0061\u006c\u006c \u006e\u006f\u0074\u0020\u0063\u006f\u006e\u0074\u0061\u0069\u006e\u0020th\u0065\u0020\u0048\u0054\u0050\u0020\u006b\u0065\u0079\u002e"));
};_aaegb ,_feag :=_ag .GetDict (_fafc .Get ("\u0048\u0054"));if _feag {if _dbcg :=_aaegb .Get ("\u0048\u0061\u006cf\u0074\u006f\u006e\u0065\u0054\u0079\u0070\u0065");!_eeed &&_dbcg !=nil {_bgbeg ,_eafa :=_ag .GetInt (_dbcg );if !_eafa ||(_eafa &&!(*_bgbeg ==1||*_bgbeg ==5)){_acbg =append (_acbg ,_bb ("\u0020\u0036\u002e\u0032\u002e\u0035\u002d\u0034","\u0041\u006c\u006c\u0020\u0068\u0061\u006c\u0066\u0074\u006f\u006e\u0065\u0073\u0020\u0069\u006e\u0020\u0061\u0020\u0063\u006f\u006e\u0066\u006f\u0072\u006d\u0069\u006e\u0067\u0020\u0050\u0044\u0046\u002f\u0041\u002d\u0032\u0020\u0066\u0069\u006ce\u0020\u0073h\u0061\u006c\u006c\u0020h\u0061\u0076\u0065\u0020\u0074\u0068\u0065\u0020\u0076\u0061l\u0075\u0065\u0020\u0031\u0020\u006f\u0072\u0020\u0035 \u0066\u006f\u0072\u0020\u0074\u0068\u0065\u0020\u0048\u0061l\u0066\u0074\u006fn\u0065\u0054\u0079\u0070\u0065\u0020\u006be\u0079\u002e"));
if _dfeba (){return true ;};};};if _dgffe :=_aaegb .Get ("\u0048\u0061\u006cf\u0074\u006f\u006e\u0065\u004e\u0061\u006d\u0065");!_bbcfd &&_dgffe !=nil {_bbcfd =true ;_acbg =append (_acbg ,_bb ("\u0036.\u0032\u002e\u0035\u002d\u0035","\u0048\u0061\u006c\u0066\u0074o\u006e\u0065\u0073\u0020\u0069\u006e\u0020a\u0020\u0063\u006f\u006e\u0066\u006f\u0072\u006d\u0069\u006e\u0067\u0020\u0050\u0044\u0046\u002f\u0041\u002d\u0032\u0020\u0066\u0069\u006c\u0065\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u006e\u006f\u0074\u0020\u0063\u006f\u006e\u0074\u0061i\u006e\u0020\u0061\u0020\u0048\u0061\u006c\u0066\u0074\u006f\u006e\u0065N\u0061\u006d\u0065\u0020\u006b\u0065y\u002e"));
if _dfeba (){return true ;};};};_ ,_bbabd :=_aggec (_cdca );var _ecgg bool ;_bdceb ,_feag :=_ag .GetDict (_fafc .Get ("\u0047\u0072\u006fu\u0070"));if _feag {_ ,_cebad :=_ag .GetName (_bdceb .Get ("\u0043\u0053"));if _cebad {_ecgg =true ;};};if _cgfd :=_fafc .Get ("\u0042\u004d");
!_adcg &&!_ggfbd &&_cgfd !=nil {_bacd ,_fbdcf :=_ag .GetName (_cgfd );if _fbdcf {switch _bacd .String (){case "\u004e\u006f\u0072\u006d\u0061\u006c","\u0043\u006f\u006d\u0070\u0061\u0074\u0069\u0062\u006c\u0065","\u004d\u0075\u006c\u0074\u0069\u0070\u006c\u0079","\u0053\u0063\u0072\u0065\u0065\u006e","\u004fv\u0065\u0072\u006c\u0061\u0079","\u0044\u0061\u0072\u006b\u0065\u006e","\u004ci\u0067\u0068\u0074\u0065\u006e","\u0043\u006f\u006c\u006f\u0072\u0044\u006f\u0064\u0067\u0065","\u0043o\u006c\u006f\u0072\u0042\u0075\u0072n","\u0048a\u0072\u0064\u004c\u0069\u0067\u0068t","\u0053o\u0066\u0074\u004c\u0069\u0067\u0068t","\u0044\u0069\u0066\u0066\u0065\u0072\u0065\u006e\u0063\u0065","\u0045x\u0063\u006c\u0075\u0073\u0069\u006fn","\u0048\u0075\u0065","\u0053\u0061\u0074\u0075\u0072\u0061\u0074\u0069\u006f\u006e","\u0043\u006f\u006co\u0072","\u004c\u0075\u006d\u0069\u006e\u006f\u0073\u0069\u0074\u0079":default:_adcg =true ;
_acbg =append (_acbg ,_bb ("\u0036\u002e\u0032\u002e\u0031\u0030\u002d\u0031","\u004f\u006el\u0079\u0020\u0062\u006c\u0065\u006e\u0064\u0020\u006d\u006f\u0064\u0065\u0073\u0020\u0074h\u0061\u0074\u0020\u0061\u0072\u0065\u0020\u0073\u0070\u0065c\u0069\u0066\u0069ed\u0020\u0069\u006e\u0020\u0049\u0053O\u0020\u0033\u0032\u0030\u0030\u0030\u002d\u0031\u003a2\u0030\u0030\u0038\u0020\u0073h\u0061\u006c\u006c\u0020\u0062\u0065\u0020\u0075\u0073\u0065\u0064\u0020\u0066\u006f\u0072\u0020\u0074\u0068\u0065 \u0076\u0061\u006c\u0075e\u0020\u006f\u0066\u0020\u0074\u0068e\u0020\u0042M\u0020\u006b\u0065\u0079\u0020\u0069\u006e\u0020\u0061\u006e\u0020\u0065\u0078t\u0065\u006e\u0064\u0065\u0064\u0020\u0067\u0072\u0061\u0070\u0068\u0069\u0063\u0020\u0073\u0074\u0061\u0074\u0065 \u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u002e"));
if _dfeba (){return true ;};};if _bacd .String ()!="\u004e\u006f\u0072\u006d\u0061\u006c"&&!_bbabd &&!_ecgg {_ggfbd =true ;_acbg =append (_acbg ,_bb ("\u0036\u002e\u0032\u002e\u0031\u0030\u002d\u0032","\u0049\u0066\u0020\u0074\u0068\u0065 \u0064\u006f\u0063\u0075\u006de\u006e\u0074\u0020\u0064\u006f\u0065\u0073\u0020\u006e\u006f\u0074\u0020c\u006f\u006e\u0074\u0061\u0069\u006e\u0020\u0061\u0020P\u0044\u0046\u002f\u0041\u0020\u004f\u0075\u0074\u0070u\u0074\u0049\u006e\u0074\u0065\u006e\u0074\u002c\u0020\u0074\u0068\u0065\u006e\u0020\u0061\u006c\u006c\u0020\u0050\u0061\u0067\u0065\u0020\u006f\u0062\u006a\u0065\u0063t\u0073\u0020\u0074\u0068a\u0074 \u0063\u006f\u006e\u0074\u0061\u0069\u006e\u0020t\u0072\u0061\u006e\u0073\u0070\u0061\u0072\u0065\u006e\u0063\u0079\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0069\u006e\u0063l\u0075\u0064\u0065\u0020\u0074\u0068\u0065\u0020\u0047\u0072\u006f\u0075\u0070\u0020\u006b\u0065y\u002c a\u006e\u0064\u0020\u0074\u0068\u0065\u0020\u0061\u0074\u0074\u0072\u0069\u0062\u0075\u0074\u0065\u0020\u0064\u0069c\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u0020\u0074\u0068\u0061\u0074\u0020\u0066\u006f\u0072\u006d\u0073\u0020\u0074\u0068\u0065\u0020\u0076\u0061\u006cu\u0065\u0020\u006f\u0066\u0020\u0074\u0068\u0061\u0074\u0020\u0047\u0072\u006fu\u0070\u0020\u006b\u0065y\u0020sh\u0061\u006c\u006c\u0020\u0069\u006e\u0063\u006c\u0075d\u0065\u0020\u0061\u0020\u0043\u0053\u0020\u0065\u006e\u0074\u0072\u0079\u0020\u0077\u0068\u006fs\u0065\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u0073\u0068\u0061\u006c\u006c \u0062\u0065 \u0075\u0073\u0065\u0064\u0020\u0061\u0073\u0020\u0074\u0068\u0065\u0020\u0064\u0065\u0066\u0061\u0075\u006c\u0074\u0020\u0062\u006c\u0065\u006e\u0064\u0069n\u0067 \u0063\u006f\u006c\u006f\u0075\u0072\u0020\u0073p\u0061\u0063\u0065\u002e"));
if _dfeba (){return true ;};};};};if _ ,_feag =_ag .GetDict (_fafc .Get ("\u0053\u004d\u0061s\u006b"));!_ggfbd &&_feag &&!_bbabd &&!_ecgg {_ggfbd =true ;_acbg =append (_acbg ,_bb ("\u0036\u002e\u0032\u002e\u0031\u0030\u002d\u0032","\u0049\u0066\u0020\u0074\u0068\u0065 \u0064\u006f\u0063\u0075\u006de\u006e\u0074\u0020\u0064\u006f\u0065\u0073\u0020\u006e\u006f\u0074\u0020c\u006f\u006e\u0074\u0061\u0069\u006e\u0020\u0061\u0020P\u0044\u0046\u002f\u0041\u0020\u004f\u0075\u0074\u0070u\u0074\u0049\u006e\u0074\u0065\u006e\u0074\u002c\u0020\u0074\u0068\u0065\u006e\u0020\u0061\u006c\u006c\u0020\u0050\u0061\u0067\u0065\u0020\u006f\u0062\u006a\u0065\u0063t\u0073\u0020\u0074\u0068a\u0074 \u0063\u006f\u006e\u0074\u0061\u0069\u006e\u0020t\u0072\u0061\u006e\u0073\u0070\u0061\u0072\u0065\u006e\u0063\u0079\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0069\u006e\u0063l\u0075\u0064\u0065\u0020\u0074\u0068\u0065\u0020\u0047\u0072\u006f\u0075\u0070\u0020\u006b\u0065y\u002c a\u006e\u0064\u0020\u0074\u0068\u0065\u0020\u0061\u0074\u0074\u0072\u0069\u0062\u0075\u0074\u0065\u0020\u0064\u0069c\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u0020\u0074\u0068\u0061\u0074\u0020\u0066\u006f\u0072\u006d\u0073\u0020\u0074\u0068\u0065\u0020\u0076\u0061\u006cu\u0065\u0020\u006f\u0066\u0020\u0074\u0068\u0061\u0074\u0020\u0047\u0072\u006fu\u0070\u0020\u006b\u0065y\u0020sh\u0061\u006c\u006c\u0020\u0069\u006e\u0063\u006c\u0075d\u0065\u0020\u0061\u0020\u0043\u0053\u0020\u0065\u006e\u0074\u0072\u0079\u0020\u0077\u0068\u006fs\u0065\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u0073\u0068\u0061\u006c\u006c \u0062\u0065 \u0075\u0073\u0065\u0064\u0020\u0061\u0073\u0020\u0074\u0068\u0065\u0020\u0064\u0065\u0066\u0061\u0075\u006c\u0074\u0020\u0062\u006c\u0065\u006e\u0064\u0069n\u0067 \u0063\u006f\u006c\u006f\u0075\u0072\u0020\u0073p\u0061\u0063\u0065\u002e"));
if _dfeba (){return true ;};};if _febf :=_fafc .Get ("\u0043\u0041");!_ggfbd &&_febf !=nil &&!_bbabd &&!_ecgg {_caeb ,_aedfd :=_ag .GetNumberAsFloat (_febf );if _aedfd ==nil &&_caeb < 1.0{_ggfbd =true ;_acbg =append (_acbg ,_bb ("\u0036\u002e\u0032\u002e\u0031\u0030\u002d\u0032","\u0049\u0066\u0020\u0074\u0068\u0065 \u0064\u006f\u0063\u0075\u006de\u006e\u0074\u0020\u0064\u006f\u0065\u0073\u0020\u006e\u006f\u0074\u0020c\u006f\u006e\u0074\u0061\u0069\u006e\u0020\u0061\u0020P\u0044\u0046\u002f\u0041\u0020\u004f\u0075\u0074\u0070u\u0074\u0049\u006e\u0074\u0065\u006e\u0074\u002c\u0020\u0074\u0068\u0065\u006e\u0020\u0061\u006c\u006c\u0020\u0050\u0061\u0067\u0065\u0020\u006f\u0062\u006a\u0065\u0063t\u0073\u0020\u0074\u0068a\u0074 \u0063\u006f\u006e\u0074\u0061\u0069\u006e\u0020t\u0072\u0061\u006e\u0073\u0070\u0061\u0072\u0065\u006e\u0063\u0079\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0069\u006e\u0063l\u0075\u0064\u0065\u0020\u0074\u0068\u0065\u0020\u0047\u0072\u006f\u0075\u0070\u0020\u006b\u0065y\u002c a\u006e\u0064\u0020\u0074\u0068\u0065\u0020\u0061\u0074\u0074\u0072\u0069\u0062\u0075\u0074\u0065\u0020\u0064\u0069c\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u0020\u0074\u0068\u0061\u0074\u0020\u0066\u006f\u0072\u006d\u0073\u0020\u0074\u0068\u0065\u0020\u0076\u0061\u006cu\u0065\u0020\u006f\u0066\u0020\u0074\u0068\u0061\u0074\u0020\u0047\u0072\u006fu\u0070\u0020\u006b\u0065y\u0020sh\u0061\u006c\u006c\u0020\u0069\u006e\u0063\u006c\u0075d\u0065\u0020\u0061\u0020\u0043\u0053\u0020\u0065\u006e\u0074\u0072\u0079\u0020\u0077\u0068\u006fs\u0065\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u0073\u0068\u0061\u006c\u006c \u0062\u0065 \u0075\u0073\u0065\u0064\u0020\u0061\u0073\u0020\u0074\u0068\u0065\u0020\u0064\u0065\u0066\u0061\u0075\u006c\u0074\u0020\u0062\u006c\u0065\u006e\u0064\u0069n\u0067 \u0063\u006f\u006c\u006f\u0075\u0072\u0020\u0073p\u0061\u0063\u0065\u002e"));
if _dfeba (){return true ;};};};if _gedg :=_fafc .Get ("\u0063\u0061");!_ggfbd &&_gedg !=nil &&!_bbabd &&!_ecgg {_afcb ,_adgf :=_ag .GetNumberAsFloat (_gedg );if _adgf ==nil &&_afcb < 1.0{_ggfbd =true ;_acbg =append (_acbg ,_bb ("\u0036\u002e\u0032\u002e\u0031\u0030\u002d\u0032","\u0049\u0066\u0020\u0074\u0068\u0065 \u0064\u006f\u0063\u0075\u006de\u006e\u0074\u0020\u0064\u006f\u0065\u0073\u0020\u006e\u006f\u0074\u0020c\u006f\u006e\u0074\u0061\u0069\u006e\u0020\u0061\u0020P\u0044\u0046\u002f\u0041\u0020\u004f\u0075\u0074\u0070u\u0074\u0049\u006e\u0074\u0065\u006e\u0074\u002c\u0020\u0074\u0068\u0065\u006e\u0020\u0061\u006c\u006c\u0020\u0050\u0061\u0067\u0065\u0020\u006f\u0062\u006a\u0065\u0063t\u0073\u0020\u0074\u0068a\u0074 \u0063\u006f\u006e\u0074\u0061\u0069\u006e\u0020t\u0072\u0061\u006e\u0073\u0070\u0061\u0072\u0065\u006e\u0063\u0079\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0069\u006e\u0063l\u0075\u0064\u0065\u0020\u0074\u0068\u0065\u0020\u0047\u0072\u006f\u0075\u0070\u0020\u006b\u0065y\u002c a\u006e\u0064\u0020\u0074\u0068\u0065\u0020\u0061\u0074\u0074\u0072\u0069\u0062\u0075\u0074\u0065\u0020\u0064\u0069c\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u0020\u0074\u0068\u0061\u0074\u0020\u0066\u006f\u0072\u006d\u0073\u0020\u0074\u0068\u0065\u0020\u0076\u0061\u006cu\u0065\u0020\u006f\u0066\u0020\u0074\u0068\u0061\u0074\u0020\u0047\u0072\u006fu\u0070\u0020\u006b\u0065y\u0020sh\u0061\u006c\u006c\u0020\u0069\u006e\u0063\u006c\u0075d\u0065\u0020\u0061\u0020\u0043\u0053\u0020\u0065\u006e\u0074\u0072\u0079\u0020\u0077\u0068\u006fs\u0065\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u0073\u0068\u0061\u006c\u006c \u0062\u0065 \u0075\u0073\u0065\u0064\u0020\u0061\u0073\u0020\u0074\u0068\u0065\u0020\u0064\u0065\u0066\u0061\u0075\u006c\u0074\u0020\u0062\u006c\u0065\u006e\u0064\u0069n\u0067 \u0063\u006f\u006c\u006f\u0075\u0072\u0020\u0073p\u0061\u0063\u0065\u002e"));
if _dfeba (){return true ;};};};return false ;};for _ ,_dace :=range _cdca .PageList {_ggcf :=_dace .Resources ;if _ggcf ==nil {continue ;};if _ggcf .ExtGState ==nil {continue ;};_aeaf ,_geaa :=_ag .GetDict (_ggcf .ExtGState );if !_geaa {continue ;};_eeab :=_aeaf .Keys ();
for _ ,_bbfa :=range _eeab {_gdad ,_gbab :=_ag .GetDict (_aeaf .Get (_bbfa ));if !_gbab {continue ;};if _fcbbe (_gdad ){return _acbg ;};};};for _ ,_adec :=range _cdca .PageList {_dbab :=_adec .Resources ;if _dbab ==nil {continue ;};_bfbca ,_caec :=_ag .GetDict (_dbab .XObject );
if !_caec {continue ;};for _ ,_gefc :=range _bfbca .Keys (){_gfbbf ,_cdacgb :=_ag .GetStream (_bfbca .Get (_gefc ));if !_cdacgb {continue ;};_edcfb ,_cdacgb :=_ag .GetDict (_gfbbf .Get ("\u0052e\u0073\u006f\u0075\u0072\u0063\u0065s"));if !_cdacgb {continue ;
};_cdeb ,_cdacgb :=_ag .GetDict (_edcfb .Get ("\u0045x\u0074\u0047\u0053\u0074\u0061\u0074e"));if !_cdacgb {continue ;};for _ ,_cdcab :=range _cdeb .Keys (){_ecbc ,_ddeb :=_ag .GetDict (_cdeb .Get (_cdcab ));if !_ddeb {continue ;};if _fcbbe (_ecbc ){return _acbg ;
};};};};return _acbg ;};func _dgf (_efea *_a .CompliancePdfReader )ViolatedRule {_baec ,_ggfc :=_efea .PdfReader .GetTrailer ();if _ggfc !=nil {return _bb ("\u0036.\u0031\u002e\u0033\u002d\u0031","\u006d\u0069\u0073s\u0069\u006e\u0067\u0020t\u0072\u0061\u0069\u006c\u0065\u0072\u0020i\u006e\u0020\u0074\u0068\u0065\u0020\u0064\u006f\u0063\u0075\u006d\u0065\u006e\u0074");
};if _baec .Get ("\u0049\u0044")==nil {return _bb ("\u0036.\u0031\u002e\u0033\u002d\u0031","\u0054\u0068\u0065\u0020\u0066\u0069\u006c\u0065\u0020\u0074\u0072\u0061\u0069\u006c\u0065\u0072\u0020\u0064\u0069\u0063t\u0069\u006f\u006e\u0061\u0072\u0079\u0020\u0073\u0068a\u006c\u006c\u0020\u0063\u006f\u006e\u0074\u0061\u0069\u006e\u0020\u0074\u0068e\u0020\u0027\u0049\u0044\u0027\u0020k\u0065\u0079\u0077o\u0072\u0064");
};if _baec .Get ("\u0045n\u0063\u0072\u0079\u0070\u0074")!=nil {return _bb ("\u0036.\u0031\u002e\u0033\u002d\u0032","\u0054\u0068\u0065\u0020\u006b\u0065y\u0077\u006f\u0072\u0064\u0020'\u0045\u006e\u0063\u0072\u0079\u0070t\u0027\u0020\u0073\u0068\u0061l\u006c\u0020\u006e\u006f\u0074\u0020\u0062\u0065\u0020\u0075\u0073\u0065d\u0020\u0069\u006e\u0020\u0074\u0068\u0065\u0020\u0074\u0072\u0061\u0069\u006c\u0065\u0072 \u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061r\u0079\u002e\u0020");
};return _eag ;};func _gfde (_bbbf *_a .CompliancePdfReader )(_dgd []ViolatedRule ){_geea :=_bbbf .GetObjectNums ();for _ ,_bga :=range _geea {_cafb ,_ebebb :=_bbbf .GetIndirectObjectByNumber (_bga );if _ebebb !=nil {continue ;};_ebeca ,_cdac :=_ag .GetDict (_cafb );
if !_cdac {continue ;};_fgee ,_cdac :=_ag .GetName (_ebeca .Get ("\u0054\u0079\u0070\u0065"));if !_cdac {continue ;};if _fgee .String ()!="\u0046\u0069\u006c\u0065\u0073\u0070\u0065\u0063"{continue ;};if _ebeca .Get ("\u0045\u0046")!=nil {_dgd =append (_dgd ,_bb ("\u0036\u002e\u0031\u002e\u0031\u0031\u002d\u0031","\u0041 \u0066\u0069\u006c\u0065 \u0073p\u0065\u0063\u0069\u0066\u0069\u0063\u0061\u0074\u0069o\u006e\u0020\u0064\u0069\u0063\u0074\u0069\u006fn\u0061\u0072\u0079\u002c\u0020\u0061\u0073\u0020\u0064\u0065\u0066i\u006e\u0065\u0064\u0020\u0069\u006e\u0020\u0050\u0044\u0046 \u0033\u002e\u0031\u0030\u002e\u0032\u002c\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u006e\u006f\u0074\u0020\u0063o\u006e\u0074\u0061\u0069\u006e\u0020\u0074\u0068\u0065\u0020\u0045\u0046 \u006be\u0079\u002e"));
break ;};};_fgde ,_dfdg :=_gfcb (_bbbf );if !_dfdg {return _dgd ;};_bfbc ,_dfdg :=_ag .GetDict (_fgde .Get ("\u004e\u0061\u006de\u0073"));if !_dfdg {return _dgd ;};if _bfbc .Get ("\u0045\u006d\u0062\u0065\u0064\u0064\u0065\u0064\u0046\u0069\u006c\u0065\u0073")!=nil {_dgd =append (_dgd ,_bb ("\u0036\u002e\u0031\u002e\u0031\u0031\u002d\u0032","\u0041\u0020\u0066i\u006c\u0065\u0027\u0073\u0020\u006e\u0061\u006d\u0065\u0020d\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u002c\u0020\u0061\u0073\u0020d\u0065\u0066\u0069\u006ee\u0064\u0020\u0069\u006e\u0020PD\u0046 \u0052\u0065\u0066er\u0065\u006e\u0063\u0065\u0020\u0033\u002e6\u002e\u0033\u002c\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u006e\u006f\u0074\u0020\u0063\u006f\u006e\u0074\u0061\u0069\u006e\u0020\u0074h\u0065\u0020\u0045m\u0062\u0065\u0064\u0064\u0065\u0064\u0046i\u006c\u0065\u0073\u0020\u006b\u0065\u0079\u002e"));
};return _dgd ;};func _dgcgbf (_cgbd *_a .CompliancePdfReader )(_ffdd []ViolatedRule ){var _eggd ,_caaeg bool ;_dfdd :=func ()bool {return _eggd &&_caaeg };for _ ,_ggcb :=range _cgbd .GetObjectNums (){_eaee ,_cecgcc :=_cgbd .GetIndirectObjectByNumber (_ggcb );
if _cecgcc !=nil {_g .Log .Debug ("G\u0065\u0074\u0074\u0069\u006e\u0067\u0020\u006f\u0062\u006a\u0065\u0063\u0074\u0020\u0077\u0069\u0074\u0068 \u006e\u0075\u006d\u0062\u0065\u0072\u0020\u0025\u0064\u0020fa\u0069\u006c\u0065d\u003a \u0025\u0076",_ggcb ,_cecgcc );
continue ;};_cabb ,_fgfc :=_ag .GetDict (_eaee );if !_fgfc {continue ;};_cfbf ,_fgfc :=_ag .GetName (_cabb .Get ("\u0054\u0079\u0070\u0065"));if !_fgfc {continue ;};if *_cfbf !="\u0041\u0063\u0074\u0069\u006f\u006e"{continue ;};_eceg ,_fgfc :=_ag .GetName (_cabb .Get ("\u0053"));
if !_fgfc {if !_eggd {_ffdd =append (_ffdd ,_bb ("\u0036.\u0036\u002e\u0031\u002d\u0031","\u0054\u0068\u0065\u0020\u004c\u0061\u0075\u006e\u0063\u0068\u002c\u0020\u0053\u006f\u0075\u006e\u0064\u002c\u0020\u004d\u006f\u0076\u0069\u0065\u002c\u0020\u0052\u0065\u0073\u0065\u0074\u0046o\u0072\u006d\u002c\u0020\u0049\u006d\u0070\u006f\u0072\u0074\u0044\u0061\u0074\u0061\u0020\u0061\u006e\u0064 \u004a\u0061\u0076a\u0053\u0063\u0072\u0069\u0070\u0074\u0020\u0061\u0063\u0074\u0069\u006f\u006e\u0073\u0020s\u0068\u0061\u006c\u006c\u0020\u006eo\u0074\u0020\u0062\u0065\u0020\u0070\u0065\u0072\u006d\u0069\u0074\u0074e\u0064\u002e \u0041\u0064\u0064\u0069\u0074\u0069\u006f\u006e\u0061\u006c\u006c\u0079\u002c\u0020th\u0065\u0020\u0064\u0065p\u0072\u0065\u0063\u0061\u0074\u0065\u0064\u0020s\u0065\u0074\u002d\u0073\u0074\u0061\u0074\u0065\u0020\u0061\u006e\u0064\u0020\u006e\u006f\u002d\u006f\u0070\u0020\u0061\u0063\u0074\u0069\u006f\u006e\u0073\u0020\u0073\u0068a\u006c\u006c\u0020\u006e\u006f\u0074\u0020\u0062e\u0020\u0070\u0065\u0072\u006d\u0069\u0074\u0074e\u0064\u002e\u0020T\u0068\u0065\u0020\u0048\u0069\u0064\u0065\u0020a\u0063\u0074\u0069\u006f\u006e \u0073\u0068\u0061\u006c\u006c\u0020\u006e\u006f\u0074\u0020\u0062\u0065\u0020\u0070\u0065\u0072\u006d\u0069\u0074\u0074\u0065\u0064\u002e"));
_eggd =true ;if _dfdd (){return _ffdd ;};};continue ;};switch _a .PdfActionType (*_eceg ){case _a .ActionTypeLaunch ,_a .ActionTypeSound ,_a .ActionTypeMovie ,_a .ActionTypeResetForm ,_a .ActionTypeImportData ,_a .ActionTypeJavaScript :if !_eggd {_ffdd =append (_ffdd ,_bb ("\u0036.\u0036\u002e\u0031\u002d\u0031","\u0054\u0068\u0065\u0020\u004c\u0061\u0075\u006e\u0063\u0068\u002c\u0020\u0053\u006f\u0075\u006e\u0064\u002c\u0020\u004d\u006f\u0076\u0069\u0065\u002c\u0020\u0052\u0065\u0073\u0065\u0074\u0046o\u0072\u006d\u002c\u0020\u0049\u006d\u0070\u006f\u0072\u0074\u0044\u0061\u0074\u0061\u0020\u0061\u006e\u0064 \u004a\u0061\u0076a\u0053\u0063\u0072\u0069\u0070\u0074\u0020\u0061\u0063\u0074\u0069\u006f\u006e\u0073\u0020s\u0068\u0061\u006c\u006c\u0020\u006eo\u0074\u0020\u0062\u0065\u0020\u0070\u0065\u0072\u006d\u0069\u0074\u0074e\u0064\u002e \u0041\u0064\u0064\u0069\u0074\u0069\u006f\u006e\u0061\u006c\u006c\u0079\u002c\u0020th\u0065\u0020\u0064\u0065p\u0072\u0065\u0063\u0061\u0074\u0065\u0064\u0020s\u0065\u0074\u002d\u0073\u0074\u0061\u0074\u0065\u0020\u0061\u006e\u0064\u0020\u006e\u006f\u002d\u006f\u0070\u0020\u0061\u0063\u0074\u0069\u006f\u006e\u0073\u0020\u0073\u0068a\u006c\u006c\u0020\u006e\u006f\u0074\u0020\u0062e\u0020\u0070\u0065\u0072\u006d\u0069\u0074\u0074e\u0064\u002e\u0020T\u0068\u0065\u0020\u0048\u0069\u0064\u0065\u0020a\u0063\u0074\u0069\u006f\u006e \u0073\u0068\u0061\u006c\u006c\u0020\u006e\u006f\u0074\u0020\u0062\u0065\u0020\u0070\u0065\u0072\u006d\u0069\u0074\u0074\u0065\u0064\u002e"));
_eggd =true ;if _dfdd (){return _ffdd ;};};continue ;case _a .ActionTypeNamed :if !_caaeg {_abbe ,_gbfa :=_ag .GetName (_cabb .Get ("\u004e"));if !_gbfa {_ffdd =append (_ffdd ,_bb ("\u0036.\u0036\u002e\u0031\u002d\u0032","N\u0061\u006d\u0065\u0064\u0020\u0061\u0063t\u0069\u006f\u006e\u0073\u0020\u006f\u0074\u0068e\u0072\u0020\u0074h\u0061\u006e\u0020\u004e\u0065\u0078\u0074\u0050\u0061\u0067\u0065\u002c\u0020P\u0072\u0065v\u0050\u0061\u0067\u0065\u002c\u0020\u0046\u0069\u0072\u0073\u0074\u0050a\u0067e\u002c\u0020\u0061\u006e\u0064\u0020\u004c\u0061\u0073\u0074\u0050\u0061\u0067\u0065\u0020\u0073h\u0061\u006c\u006c\u0020\u006e\u006f\u0074\u0020\u0062\u0065\u0020\u0070\u0065\u0072\u006d\u0069\u0074\u0074\u0065\u0064\u002e"));
_caaeg =true ;if _dfdd (){return _ffdd ;};continue ;};switch *_abbe {case "\u004e\u0065\u0078\u0074\u0050\u0061\u0067\u0065","\u0050\u0072\u0065\u0076\u0050\u0061\u0067\u0065","\u0046i\u0072\u0073\u0074\u0050\u0061\u0067e","\u004c\u0061\u0073\u0074\u0050\u0061\u0067\u0065":default:_ffdd =append (_ffdd ,_bb ("\u0036.\u0036\u002e\u0031\u002d\u0032","N\u0061\u006d\u0065\u0064\u0020\u0061\u0063t\u0069\u006f\u006e\u0073\u0020\u006f\u0074\u0068e\u0072\u0020\u0074h\u0061\u006e\u0020\u004e\u0065\u0078\u0074\u0050\u0061\u0067\u0065\u002c\u0020P\u0072\u0065v\u0050\u0061\u0067\u0065\u002c\u0020\u0046\u0069\u0072\u0073\u0074\u0050a\u0067e\u002c\u0020\u0061\u006e\u0064\u0020\u004c\u0061\u0073\u0074\u0050\u0061\u0067\u0065\u0020\u0073h\u0061\u006c\u006c\u0020\u006e\u006f\u0074\u0020\u0062\u0065\u0020\u0070\u0065\u0072\u006d\u0069\u0074\u0074\u0065\u0064\u002e"));
_caaeg =true ;if _dfdd (){return _ffdd ;};continue ;};};};};return _ffdd ;};func _bgbb (_fbc *_a .CompliancePdfReader )(_cgdc ViolatedRule ){for _ ,_ccfg :=range _fbc .GetObjectNums (){_gffb ,_edff :=_fbc .GetIndirectObjectByNumber (_ccfg );if _edff !=nil {continue ;
};_dfab ,_afbg :=_ag .GetStream (_gffb );if !_afbg {continue ;};_ccfdc ,_afbg :=_ag .GetName (_dfab .Get ("\u0054\u0079\u0070\u0065"));if !_afbg {continue ;};if *_ccfdc !="\u0058O\u0062\u006a\u0065\u0063\u0074"{continue ;};_cddg ,_afbg :=_ag .GetName (_dfab .Get ("\u0053\u0075\u0062\u0074\u0079\u0070\u0065\u0032"));
if !_afbg {continue ;};if *_cddg =="\u0050\u0053"{return _bb ("\u0036.\u0032\u002e\u0035\u002d\u0031","A\u0020\u0066\u006fr\u006d\u0020\u0058\u004f\u0062\u006a\u0065\u0063\u0074\u0020\u0064\u0069\u0063\u0074\u0069o\u006e\u0061\u0072\u0079\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u006e\u006ft\u0020\u0063\u006f\u006e\u0074\u0061\u0069\u006e \u0074\u0068\u0065\u0020\u0053\u0075\u0062\u0074\u0079\u0070\u0065\u0032\u0020\u006b\u0065\u0079 \u0077\u0069\u0074\u0068\u0020a\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u006f\u0066\u0020\u0050\u0053\u0020o\u0072\u0020\u0074\u0068e\u0020\u0050\u0053\u0020\u006b\u0065\u0079\u002e");
};if _dfab .Get ("\u0050\u0053")!=nil {return _bb ("\u0036.\u0032\u002e\u0035\u002d\u0031","A\u0020\u0066\u006fr\u006d\u0020\u0058\u004f\u0062\u006a\u0065\u0063\u0074\u0020\u0064\u0069\u0063\u0074\u0069o\u006e\u0061\u0072\u0079\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u006e\u006ft\u0020\u0063\u006f\u006e\u0074\u0061\u0069\u006e \u0074\u0068\u0065\u0020\u0053\u0075\u0062\u0074\u0079\u0070\u0065\u0032\u0020\u006b\u0065\u0079 \u0077\u0069\u0074\u0068\u0020a\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u006f\u0066\u0020\u0050\u0053\u0020o\u0072\u0020\u0074\u0068e\u0020\u0050\u0053\u0020\u006b\u0065\u0079\u002e");
};};return _cgdc ;};func _efccg (_egfe *_a .CompliancePdfReader )(_ddbf []ViolatedRule ){_eegg ,_efab :=_gfcb (_egfe );if !_efab {return _ddbf ;};_dfec ,_efab :=_ag .GetDict (_eegg .Get ("\u004e\u0061\u006de\u0073"));if !_efab {return _ddbf ;};if _dfec .Get ("\u0041\u006c\u0074\u0065rn\u0061\u0074\u0065\u0050\u0072\u0065\u0073\u0065\u006e\u0074\u0061\u0074\u0069\u006fn\u0073")!=nil {_ddbf =append (_ddbf ,_bb ("\u0036\u002e\u0031\u0030\u002d\u0031","T\u0068\u0065\u0072e\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0062\u0065\u0020\u006e\u006f\u0020\u0041\u006c\u0074\u0065\u0072\u006e\u0061\u0074\u0065\u0050\u0072\u0065s\u0065\u006e\u0074a\u0074\u0069\u006f\u006e\u0073\u0020\u0065\u006e\u0074\u0072\u0079\u0020\u0069n\u0020\u0074\u0068\u0065 \u0064\u006f\u0063\u0075m\u0065\u006e\u0074\u0027\u0073\u0020\u006e\u0061\u006d\u0065\u0020\u0064\u0069\u0063\u0074\u0069\u006fn\u0061\u0072\u0079\u002e"));
};return _ddbf ;};

// NewProfile3U creates a new Profile3U with the given options.
func NewProfile3U (options *Profile3Options )*Profile3U {if options ==nil {options =DefaultProfile3Options ();};_abaf (options );return &Profile3U {profile3 {_gae :*options ,_gfdf :_gd ()}};};func _gfcb (_fef *_a .CompliancePdfReader )(*_ag .PdfObjectDictionary ,bool ){_dgdc ,_bfaca :=_fef .GetTrailer ();
if _bfaca !=nil {_g .Log .Debug ("\u0043\u0061\u006en\u006f\u0074\u0020\u0067e\u0074\u0020\u0064\u006f\u0063\u0075\u006de\u006e\u0074\u0020\u0074\u0072\u0061\u0069\u006c\u0065\u0072\u003a\u0020\u0025\u0076",_bfaca );return nil ,false ;};_fdgbe ,_becg :=_dgdc .Get ("\u0052\u006f\u006f\u0074").(*_ag .PdfObjectReference );
if !_becg {_g .Log .Debug ("\u0043a\u006e\u006e\u006f\u0074 \u0066\u0069\u006e\u0064\u0020d\u006fc\u0075m\u0065\u006e\u0074\u0020\u0072\u006f\u006ft");return nil ,false ;};_egedg ,_becg :=_ag .GetDict (_ag .ResolveReference (_fdgbe ));if !_becg {_g .Log .Debug ("\u0063\u0061\u006e\u006e\u006f\u0074 \u0072\u0065\u0073\u006f\u006c\u0076\u0065\u0020\u0063\u0061\u0074\u0061\u006co\u0067\u0020\u0064\u0069\u0063\u0074\u0069o\u006e\u0061\u0072\u0079");
return nil ,false ;};return _egedg ,true ;};func _bdfg (_dacb *_a .PdfFont ,_gdfbg *_ag .PdfObjectDictionary ,_cfdee bool )ViolatedRule {const (_gdgg ="\u0036\u002e\u0032\u002e\u0031\u0031\u002e\u0034\u002d\u0031";_dcbcg ="\u0054\u0068\u0065\u0020\u0066\u006f\u006e\u0074\u0020\u0070\u0072\u006f\u0067\u0072\u0061\u006ds\u0020\u0066\u006fr\u0020\u0061\u006c\u006c\u0020f\u006f\u006e\u0074\u0073\u0020\u0075\u0073\u0065\u0064\u0020\u0066\u006f\u0072\u0020\u0072e\u006e\u0064\u0065\u0072\u0069\u006eg\u0020\u0077\u0069\u0074\u0068\u0069\u006e\u0020\u0061\u0020\u0063\u006f\u006e\u0066\u006f\u0072\u006d\u0069\u006e\u0067\u0020\u0066\u0069\u006c\u0065\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0062\u0065\u0020\u0065\u006d\u0062\u0065\u0064\u0064\u0065\u0064\u0020w\u0069t\u0068\u0069\u006e\u0020\u0074\u0068\u0061\u0074\u0020\u0066\u0069\u006c\u0065\u002c \u0061\u0073\u0020\u0064\u0065\u0066\u0069n\u0065\u0064 \u0069\u006e\u0020\u0049S\u004f\u0020\u0033\u0032\u00300\u0030\u002d\u0031\u003a\u0032\u0030\u0030\u0038\u002c\u0020\u0039\u002e\u0039\u002e";
);if _cfdee {return _eag ;};_fdcag :=_dacb .FontDescriptor ();var _egfg string ;if _fcgf ,_eabee :=_ag .GetName (_gdfbg .Get ("\u0053u\u0062\u0074\u0079\u0070\u0065"));_eabee {_egfg =_fcgf .String ();};switch _egfg {case "\u0054\u0079\u0070e\u0031":if _fdcag .FontFile ==nil {return _bb (_gdgg ,_dcbcg );
};case "\u0054\u0072\u0075\u0065\u0054\u0079\u0070\u0065":if _fdcag .FontFile2 ==nil {return _bb (_gdgg ,_dcbcg );};case "\u0054\u0079\u0070e\u0030","\u0054\u0079\u0070e\u0033":default:if _fdcag .FontFile3 ==nil {return _bb (_gdgg ,_dcbcg );};};return _eag ;
};

// Part gets the PDF/A version level.
func (_cfdc *profile2 )Part ()int {return _cfdc ._ebecg ._dg };func _agdd (_faba *_gc .Document ){_afea ,_daef :=_faba .FindCatalog ();if !_daef {return ;};_bgccd ,_daef :=_afea .GetMarkInfo ();if !_daef {_bgccd =_ag .MakeDict ();};_dfef ,_daef :=_ag .GetBool (_bgccd .Get ("\u004d\u0061\u0072\u006b\u0065\u0064"));
if !_daef ||!bool (*_dfef ){_bgccd .Set ("\u004d\u0061\u0072\u006b\u0065\u0064",_ag .MakeBool (true ));_afea .SetMarkInfo (_bgccd );};};func _ebf (_dcba *_gc .Document )error {_faff ,_bbbg :=_dcba .GetPages ();if !_bbbg {return nil ;};for _ ,_bbfd :=range _faff {_bgg :=_bbfd .FindXObjectForms ();
for _ ,_aaac :=range _bgg {_bdea ,_gfff :=_ag .GetDict (_aaac .Get ("\u0047\u0072\u006fu\u0070"));if _gfff {if _eade :=_bdea .Get ("\u0053");_eade !=nil {_deb ,_bgd :=_ag .GetName (_eade );if _bgd &&_deb .String ()=="\u0054\u0072\u0061n\u0073\u0070\u0061\u0072\u0065\u006e\u0063\u0079"{_aaac .Remove ("\u0047\u0072\u006fu\u0070");
};};};};_dac ,_bfgb :=_bbfd .GetResourcesXObject ();if _bfgb {_febae ,_cgag :=_ag .GetDict (_dac .Get ("\u0047\u0072\u006fu\u0070"));if _cgag {_aeab :=_febae .Get ("\u0053");if _aeab !=nil {_gddf ,_cffg :=_ag .GetName (_aeab );if _cffg &&_gddf .String ()=="\u0054\u0072\u0061n\u0073\u0070\u0061\u0072\u0065\u006e\u0063\u0079"{_dac .Remove ("\u0047\u0072\u006fu\u0070");
};};};};_bbef ,_cdcb :=_ag .GetDict (_bbfd .Object .Get ("\u0047\u0072\u006fu\u0070"));if _cdcb {_fdccb :=_bbef .Get ("\u0053");if _fdccb !=nil {_bag ,_bggb :=_ag .GetName (_fdccb );if _bggb &&_bag .String ()=="\u0054\u0072\u0061n\u0073\u0070\u0061\u0072\u0065\u006e\u0063\u0079"{_bbfd .Object .Remove ("\u0047\u0072\u006fu\u0070");
};};};};return nil ;};var _eag =ViolatedRule {};func _fgad (_gffba *_a .PdfFont ,_eedbf *_ag .PdfObjectDictionary )ViolatedRule {const (_afbd ="\u0036.\u0033\u002e\u0035\u002d\u0033";_edba ="\u0046\u006f\u0072\u0020\u0061\u006c\u006c\u0020\u0043\u0049\u0044\u0046\u006f\u006e\u0074\u0020\u0073\u0075\u0062\u0073\u0065\u0074\u0073 \u0072e\u0066\u0065\u0072\u0065\u006e\u0063\u0065\u0064\u0020\u0077i\u0074\u0068\u0069n\u0020\u0061\u0020c\u006f\u006e\u0066\u006f\u0072\u006d\u0069\u006e\u0067\u0020\u0066\u0069l\u0065\u002c\u0020\u0074\u0068\u0065\u0020\u0066\u006f\u006et\u0020\u0064\u0065s\u0063\u0072\u0069\u0070\u0074\u006f\u0072\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061r\u0079\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0069\u006e\u0063\u006c\u0075\u0064\u0065\u0020\u0061\u0020\u0043\u0049\u0044\u0053\u0065\u0074\u0020s\u0074\u0072\u0065\u0061\u006d\u0020\u0069\u0064\u0065\u006e\u0074\u0069\u0066\u0079\u0069\u006eg\u0020\u0077\u0068i\u0063\u0068\u0020\u0043\u0049\u0044\u0073 \u0061\u0072e\u0020\u0070\u0072\u0065\u0073\u0065\u006e\u0074\u0020\u0069\u006e \u0074\u0068\u0065\u0020\u0065\u006d\u0062\u0065\u0064d\u0065\u0064\u0020\u0043\u0049D\u0046\u006f\u006e\u0074\u0020\u0066\u0069l\u0065,\u0020\u0061\u0073 \u0064\u0065\u0073\u0063\u0072\u0069b\u0065\u0064 \u0069\u006e\u0020\u0050\u0044\u0046\u0020\u0052\u0065\u0066\u0065\u0072\u0065\u006e\u0063e\u0020\u0054ab\u006c\u0065\u0020\u0035.\u00320\u002e";
);var _fbca string ;if _acgf ,_fddc :=_ag .GetName (_eedbf .Get ("\u0053u\u0062\u0074\u0079\u0070\u0065"));_fddc {_fbca =_acgf .String ();};switch _fbca {case "\u0043\u0049\u0044F\u006f\u006e\u0074\u0054\u0079\u0070\u0065\u0030","\u0043\u0049\u0044F\u006f\u006e\u0074\u0054\u0079\u0070\u0065\u0032":_ebfa :=_gffba .FontDescriptor ();
if _ebfa .CIDSet ==nil {return _bb (_afbd ,_edba );};return _eag ;default:return _eag ;};};func _afeaa (_acb *_ag .PdfObjectDictionary ,_gaa map[*_ag .PdfObjectStream ][]byte ,_eabf map[*_ag .PdfObjectStream ]*_ab .CMap )ViolatedRule {const (_gdfb ="\u0036.\u0033\u002e\u0038\u002d\u0031";
_ggfa ="\u0054\u0068\u0065\u0020\u0066\u006f\u006e\u0074\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006ea\u0072\u0079\u0020\u0073\u0068\u0061\u006cl\u0020\u0069\u006e\u0063l\u0075\u0064e\u0020\u0061 \u0054\u006f\u0055\u006e\u0069\u0063\u006f\u0064\u0065\u0020\u0065\u006e\u0074\u0072\u0079\u0020w\u0068\u006f\u0073\u0065\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u0069\u0073 \u0061\u0020\u0043M\u0061\u0070\u0020\u0073\u0074\u0072\u0065\u0061\u006d \u006f\u0062\u006a\u0065\u0063\u0074\u0020\u0074\u0068\u0061\u0074\u0020\u006d\u0061p\u0073\u0020\u0063\u0068\u0061\u0072ac\u0074\u0065\u0072\u0020\u0063\u006fd\u0065s\u0020\u0074\u006f\u0020\u0055\u006e\u0069\u0063\u006f\u0064e \u0076a\u006c\u0075\u0065\u0073,\u0020\u0061\u0073\u0020\u0064\u0065\u0073\u0063r\u0069\u0062\u0065\u0064\u0020\u0069\u006e\u0020P\u0044\u0046\u0020\u0052\u0065f\u0065\u0072\u0065\u006e\u0063\u0065\u0020\u0035.\u0039\u002c\u0020\u0075\u006e\u006ce\u0073\u0073\u0020\u0074\u0068\u0065\u0020\u0066\u006f\u006e\u0074\u0020\u006d\u0065\u0065\u0074\u0073 \u0061\u006e\u0079\u0020\u006f\u0066\u0020\u0074\u0068\u0065\u0020\u0066\u006f\u006c\u006c\u006f\u0077\u0069\u006e\u0067\u0020\u0074\u0068\u0072\u0065\u0065\u0020\u0063\u006f\u006e\u0064\u0069\u0074\u0069\u006f\u006e\u0073\u003a\u000a\u0020\u002d\u0020\u0066o\u006e\u0074\u0073\u0020\u0074\u0068\u0061\u0074\u0020\u0075\u0073\u0065\u0020\u0074\u0068\u0065\u0020\u0070\u0072\u0065\u0064\u0065\u0066\u0069\u006e\u0065\u0064\u0020\u0065\u006e\u0063\u006f\u0064\u0069n\u0067\u0073\u0020M\u0061\u0063\u0052o\u006d\u0061\u006e\u0045\u006e\u0063\u006f\u0064\u0069\u006e\u0067\u002c\u0020\u004d\u0061\u0063\u0045\u0078\u0070\u0065\u0072\u0074E\u006e\u0063\u006f\u0064\u0069\u006e\u0067\u0020\u006f\u0072\u0020\u0057\u0069\u006e\u0041n\u0073\u0069\u0045\u006e\u0063\u006f\u0064\u0069\u006e\u0067\u002c\u0020\u006f\u0072\u0020\u0074\u0068\u0061\u0074\u0020\u0075\u0073\u0065\u0020t\u0068\u0065\u0020\u0070\u0072\u0065d\u0065\u0066\u0069\u006e\u0065\u0064\u0020\u0049\u0064\u0065\u006e\u0074\u0069\u0074\u0079\u002d\u0048\u0020\u006f\u0072\u0020\u0049\u0064\u0065n\u0074\u0069\u0074\u0079\u002d\u0056\u0020C\u004d\u0061\u0070s\u003b\u000a\u0020\u002d\u0020\u0054\u0079\u0070\u0065\u0020\u0031\u0020\u0066\u006f\u006e\u0074\u0073\u0020\u0077\u0068\u006f\u0073\u0065\u0020\u0063\u0068\u0061\u0072\u0061\u0063\u0074\u0065\u0072\u0020\u006e\u0061\u006d\u0065\u0073\u0020a\u0072\u0065 \u0074\u0061k\u0065\u006e\u0020\u0066\u0072\u006f\u006d\u0020\u0074\u0068\u0065\u0020\u0041\u0064\u006f\u0062\u0065\u0020\u0073\u0074\u0061n\u0064\u0061\u0072\u0064\u0020L\u0061t\u0069\u006e\u0020\u0063\u0068a\u0072\u0061\u0063\u0074\u0065\u0072\u0020\u0073\u0065\u0074\u0020\u006fr\u0020\u0074\u0068\u0065 \u0073\u0065\u0074\u0020\u006f\u0066 \u006e\u0061\u006d\u0065\u0064\u0020\u0063\u0068\u0061\u0072\u0061\u0063\u0074\u0065r\u0073\u0020\u0069\u006e\u0020\u0074\u0068\u0065\u0020\u0053\u0079\u006d\u0062\u006f\u006c\u0020\u0066\u006f\u006e\u0074\u002c\u0020\u0061\u0073\u0020\u0064\u0065\u0066\u0069\u006e\u0065\u0064\u0020i\u006e\u0020\u0050\u0044\u0046 \u0052\u0065\u0066\u0065\u0072\u0065\u006e\u0063\u0065\u0020\u0041\u0070\u0070\u0065\u006e\u0064\u0069\u0078 \u0044\u003b\u000a\u0020\u002d\u0020\u0054\u0079\u0070\u0065\u0020\u0030\u0020\u0066\u006f\u006e\u0074\u0073\u0020w\u0068\u006f\u0073e\u0020d\u0065\u0073\u0063\u0065n\u0064\u0061\u006e\u0074 \u0043\u0049\u0044\u0046\u006f\u006e\u0074\u0020\u0075\u0073\u0065\u0073\u0020\u0074\u0068\u0065\u0020\u0041\u0064\u006f\u0062\u0065\u002d\u0047B\u0031\u002c\u0020\u0041\u0064\u006fb\u0065\u002d\u0043\u004e\u0053\u0031\u002c\u0020\u0041\u0064\u006f\u0062\u0065\u002d\u004a\u0061\u0070\u0061\u006e\u0031\u0020\u006f\u0072\u0020\u0041\u0064\u006f\u0062\u0065\u002d\u004b\u006fr\u0065\u0061\u0031\u0020\u0063\u0068\u0061r\u0061\u0063\u0074\u0065\u0072\u0020\u0063\u006f\u006c\u006c\u0065\u0063\u0074\u0069\u006f\u006e\u0073\u002e";
);_gbbg :=_acb .Get ("\u0045\u006e\u0063\u006f\u0064\u0069\u006e\u0067");if _cegf ,_eefg :=_ag .GetName (_gbbg );_eefg {if _cegf .String ()=="\u0049\u0064\u0065\u006e\u0074\u0069\u0074\u0079\u002d\u0048"||_cegf .String ()=="\u0049\u0064\u0065\u006e\u0074\u0069\u0074\u0079\u002d\u0056"||_cegf .String ()=="\u004d\u0061c\u0052\u006f\u006da\u006e\u0045\u006e\u0063\u006f\u0064\u0069\u006e\u0067"||_cegf .String ()=="\u004d\u0061\u0063\u0045\u0078\u0070\u0065\u0072\u0074\u0045\u006e\u0063o\u0064\u0069\u006e\u0067"||_cegf .String ()=="\u0057i\u006eA\u006e\u0073\u0069\u0045\u006e\u0063\u006f\u0064\u0069\u006e\u0067"{return _eag ;
};};_aegee ,_gegdbb :=_ag .GetStream (_acb .Get ("\u0054o\u0055\u006e\u0069\u0063\u006f\u0064e"));if _gegdbb {_ ,_aaf :=_gfbb (_aegee ,_gaa ,_eabf );if _aaf !=nil {return _bb (_gdfb ,_ggfa );};return _eag ;};_ggge ,_gegdbb :=_ag .GetName (_acb .Get ("\u0053u\u0062\u0074\u0079\u0070\u0065"));
if !_gegdbb {return _bb (_gdfb ,_ggfa );};switch _ggge .String (){case "\u0054\u0079\u0070e\u0031":return _eag ;};return _bb (_gdfb ,_ggfa );};func _fdfe (_cddd *_a .CompliancePdfReader )ViolatedRule {return _eag };func _ggbd (_ccff *_a .CompliancePdfReader ,_aedec standardType )(_dgceb []ViolatedRule ){var _cgge ,_eagb ,_accb ,_ecdb ,_egad ,_fcfc ,_bgac bool ;
_efeeg :=func ()bool {return _cgge &&_eagb &&_accb &&_ecdb &&_egad &&_fcfc &&_bgac };_adge :=map[*_ag .PdfObjectStream ]*_ab .CMap {};_fdccd :=map[*_ag .PdfObjectStream ][]byte {};_aaecg :=map[_ag .PdfObject ]*_a .PdfFont {};for _ ,_abgg :=range _ccff .GetObjectNums (){_aabf ,_gafc :=_ccff .GetIndirectObjectByNumber (_abgg );
if _gafc !=nil {continue ;};_aafa ,_adcf :=_ag .GetDict (_aabf );if !_adcf {continue ;};_agaf ,_adcf :=_ag .GetName (_aafa .Get ("\u0054\u0079\u0070\u0065"));if !_adcf {continue ;};if *_agaf !="\u0046\u006f\u006e\u0074"{continue ;};_gdba ,_gafc :=_a .NewPdfFontFromPdfObject (_aafa );
if _gafc !=nil {_g .Log .Debug ("g\u0065\u0074\u0074\u0069\u006e\u0067 \u0066\u006f\u006e\u0074\u0020\u0066r\u006f\u006d\u0020\u006f\u0062\u006a\u0065c\u0074\u0020\u0066\u0061\u0069\u006c\u0065\u0064\u003a\u0020%\u0076",_gafc );continue ;};_aaecg [_aafa ]=_gdba ;
};for _ ,_bace :=range _ccff .PageList {_bfddg ,_gbfbb :=_bace .GetContentStreams ();if _gbfbb !=nil {_g .Log .Debug ("G\u0065\u0074\u0074\u0069\u006e\u0067 \u0070\u0061\u0067\u0065\u0020\u0063o\u006e\u0074\u0065\u006e\u0074\u0020\u0073t\u0072\u0065\u0061\u006d\u0073\u0020\u0066\u0061\u0069\u006ce\u0064");
continue ;};for _ ,_dcdb :=range _bfddg {_aagd :=_fbd .NewContentStreamParser (_dcdb );_gfad ,_cgdcc :=_aagd .Parse ();if _cgdcc !=nil {_g .Log .Debug ("\u0070\u0061\u0072\u0073\u0069\u006e\u0067\u0020\u0063\u006f\u006e\u0074\u0065\u006e\u0074s\u0074r\u0065\u0061\u006d\u0020\u0066\u0061\u0069\u006c\u0065\u0064\u003a\u0020\u0025\u0076",_cgdcc );
continue ;};var _ecfd bool ;for _ ,_eccf :=range *_gfad {if _eccf .Operand !="\u0054\u0072"{continue ;};if len (_eccf .Params )!=1{_g .Log .Debug ("\u0069\u006e\u0076\u0061\u006ci\u0064\u0020\u006e\u0075\u006d\u0062\u0065r\u0020\u006f\u0066\u0020\u0070\u0061\u0072\u0061\u006d\u0065\u0074\u0065\u0072\u0073\u0020\u0066\u006f\u0072\u0020\u0074\u0068\u0065\u0020\u0027\u0054\u0072\u0027\u0020\u006f\u0070\u0065\u0072\u0061\u006e\u0064\u002c\u0020\u0065\u0078\u0070e\u0063\u0074\u0065\u0064\u0020\u0027\u0031\u0027\u0020\u0062\u0075\u0074 \u0069\u0073\u003a\u0020\u0027\u0025d\u0027",len (_eccf .Params ));
continue ;};_efeca ,_afeg :=_ag .GetIntVal (_eccf .Params [0]);if !_afeg {_g .Log .Debug ("\u0072\u0065\u006e\u0064\u0065\u0072\u0069\u006e\u0067\u0020\u006d\u006f\u0064\u0065\u0020i\u0073 \u006e\u006f\u0074\u0020\u0061\u006e\u0020\u0069\u006e\u0074\u0065\u0067\u0065\u0072");
continue ;};if _efeca ==3{_ecfd =true ;break ;};};for _ ,_dafbf :=range *_gfad {if _dafbf .Operand !="\u0054\u0066"{continue ;};if len (_dafbf .Params )!=2{_g .Log .Debug ("i\u006eva\u006ci\u0064 \u006e\u0075\u006d\u0062\u0065r\u0020\u006f\u0066 \u0070\u0061\u0072\u0061\u006de\u0074\u0065\u0072s\u0020\u0066\u006f\u0072\u0020\u0074\u0068\u0065\u0020\u0027\u0054f\u0027\u0020\u006fper\u0061\u006e\u0064\u002c\u0020\u0065x\u0070\u0065\u0063\u0074\u0065\u0064\u0020\u0027\u0032\u0027\u0020\u0069s\u003a \u0027\u0025\u0064\u0027",len (_dafbf .Params ));
continue ;};_ebdba ,_dfdbc :=_ag .GetName (_dafbf .Params [0]);if !_dfdbc {_g .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a \u0054\u0066\u0020\u006f\u0070\u003d\u0025\u0073\u0020\u0047\u0065\u0074\u004ea\u006d\u0065\u0056\u0061\u006c\u0020\u0066a\u0069\u006c\u0065\u0064",_dafbf );
continue ;};_fedb ,_gcgb :=_bace .Resources .GetFontByName (*_ebdba );if !_gcgb {_g .Log .Debug ("\u0066\u006f\u006e\u0074\u0020\u006e\u006f\u0074\u0020f\u006f\u0075\u006e\u0064");continue ;};_fbecb ,_dfdbc :=_ag .GetDict (_fedb );if !_dfdbc {_g .Log .Debug ("\u0066\u006f\u006e\u0074 d\u0069\u0063\u0074\u0020\u006e\u006f\u0074\u0020\u0066\u006f\u0075\u006e\u0064");
continue ;};_gafg ,_dfdbc :=_aaecg [_fbecb ];if !_dfdbc {var _dfcc error ;_gafg ,_dfcc =_a .NewPdfFontFromPdfObject (_fbecb );if _dfcc !=nil {_g .Log .Debug ("\u0067\u0065\u0074\u0074i\u006e\u0067\u0020\u0066\u006f\u006e\u0074\u0020\u0066\u0072o\u006d \u006f\u0062\u006a\u0065\u0063\u0074\u003a \u0025\u0076",_dfcc );
continue ;};_aaecg [_fbecb ]=_gafg ;};if !_cgge {_begd :=_eeagbe (_fbecb ,_fdccd ,_adge );if _begd !=_eag {_dgceb =append (_dgceb ,_begd );_cgge =true ;if _efeeg (){return _dgceb ;};};};if !_eagb {_ebee :=_dbgcd (_fbecb );if _ebee !=_eag {_dgceb =append (_dgceb ,_ebee );
_eagb =true ;if _efeeg (){return _dgceb ;};};};if !_accb {_gagc :=_badb (_fbecb ,_fdccd ,_adge );if _gagc !=_eag {_dgceb =append (_dgceb ,_gagc );_accb =true ;if _efeeg (){return _dgceb ;};};};if !_ecdb {_bgfba :=_adab (_fbecb ,_fdccd ,_adge );if _bgfba !=_eag {_dgceb =append (_dgceb ,_bgfba );
_ecdb =true ;if _efeeg (){return _dgceb ;};};};if !_egad {_acff :=_bdfg (_gafg ,_fbecb ,_ecfd );if _acff !=_eag {_egad =true ;_dgceb =append (_dgceb ,_acff );if _efeeg (){return _dgceb ;};};};if !_fcfc {_ccgfb :=_ffee (_gafg ,_fbecb );if _ccgfb !=_eag {_fcfc =true ;
_dgceb =append (_dgceb ,_ccgfb );if _efeeg (){return _dgceb ;};};};if !_bgac &&(_aedec ._db =="\u0041"||_aedec ._db =="\u0055"){_gccda :=_ccfbe (_fbecb ,_fdccd ,_adge );if _gccda !=_eag {_bgac =true ;_dgceb =append (_dgceb ,_gccda );if _efeeg (){return _dgceb ;
};};};};};};return _dgceb ;};func (_gg standardType )String ()string {return _fg .Sprintf ("\u0050\u0044\u0046\u002f\u0041\u002d\u0025\u0064\u0025\u0073",_gg ._dg ,_gg ._db );};func _ea ()standardType {return standardType {_dg :3,_db :"\u0042"}};

// Conformance gets the PDF/A conformance.
func (_cdag *profile1 )Conformance ()string {return _cdag ._acab ._db };

// NewProfile2A creates a new Profile2A with given options.
func NewProfile2A (options *Profile2Options )*Profile2A {if options ==nil {options =DefaultProfile2Options ();};_ecbf (options );return &Profile2A {profile2 {_ece :*options ,_ebecg :_aea ()}};};func _cfbce (_cdcfa *_a .CompliancePdfReader )(_gged []ViolatedRule ){_cdbf :=_cdcfa .GetObjectNums ();
for _ ,_eafea :=range _cdbf {_dfgbg ,_gefbg :=_cdcfa .GetIndirectObjectByNumber (_eafea );if _gefbg !=nil {continue ;};_ebgb ,_cfdf :=_ag .GetDict (_dfgbg );if !_cfdf {continue ;};_ebdfd ,_cfdf :=_ag .GetName (_ebgb .Get ("\u0054\u0079\u0070\u0065"));if !_cfdf {continue ;
};if _ebdfd .String ()!="\u0046\u0069\u006c\u0065\u0073\u0070\u0065\u0063"{continue ;};_bcced ,_gefbg :=_a .NewPdfFilespecFromObj (_ebgb );if _gefbg !=nil {continue ;};if _bcced .EF !=nil {if _bcced .F ==nil ||_bcced .UF ==nil {_gged =append (_gged ,_bb ("\u0036\u002e\u0038-\u0032","\u0054h\u0065\u0020\u0066\u0069\u006c\u0065\u0020\u0073\u0070\u0065\u0063\u0069\u0066i\u0063\u0061\u0074i\u006f\u006e\u0020\u0064\u0069\u0063t\u0069\u006fn\u0061\u0072\u0079\u0020\u0066\u006f\u0072\u0020\u0061\u006e\u0020\u0065\u006d\u0062\u0065\u0064\u0064\u0065\u0064\u0020\u0066\u0069\u006c\u0065\u0020\u0073\u0068\u0061\u006cl\u0020\u0063\u006f\u006e\u0074a\u0069\u006e\u0020t\u0068\u0065\u0020\u0046\u0020a\u006e\u0064\u0020\u0055\u0046\u0020\u006b\u0065\u0079\u0073\u002e"));
break ;};if _bcced .AFRelationship ==nil {_gged =append (_gged ,_bb ("\u0036\u002e\u0038-\u0033","\u0049\u006e\u0020\u006f\u0072d\u0065\u0072\u0020\u0074\u006f\u0020\u0065\u006e\u0061\u0062\u006c\u0065\u0020i\u0064\u0065nt\u0069\u0066\u0069c\u0061\u0074\u0069o\u006e\u0020\u006f\u0066\u0020\u0074\u0068\u0065\u0020\u0072\u0065\u006c\u0061\u0074\u0069\u006f\u006e\u0073h\u0069\u0070\u0020\u0062\u0065\u0074\u0077\u0065\u0065\u006e\u0020\u0074\u0068\u0065\u0020fi\u006ce\u0020\u0073\u0070\u0065\u0063\u0069f\u0069c\u0061\u0074\u0069o\u006e\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u0020\u0061\u006e\u0064\u0020\u0074\u0068\u0065\u0020c\u006f\u006e\u0074e\u006e\u0074\u0020\u0074\u0068\u0061\u0074\u0020\u0069\u0073\u0020\u0072\u0065\u0066\u0065\u0072\u0072\u0069\u006e\u0067\u0020\u0074\u006f\u0020\u0069\u0074\u002c\u0020\u0061\u0020\u006e\u0065\u0077\u0020(\u0072\u0065\u0071\u0075i\u0072\u0065\u0064\u0029\u0020\u006be\u0079\u0020h\u0061\u0073\u0020\u0062e\u0065\u006e\u0020\u0064\u0065\u0066i\u006e\u0065\u0064\u0020a\u006e\u0064\u0020\u0069\u0074s \u0070\u0072e\u0073\u0065n\u0063\u0065\u0020\u0028\u0069\u006e\u0020\u0074\u0068e\u0020\u0064\u0069\u0063\u0074i\u006f\u006e\u0061\u0072\u0079\u0029\u0020\u0069\u0073\u0020\u0072\u0065q\u0075\u0069\u0072e\u0064\u002e"));
break ;};};};return _gged ;};func (_fdc standardType )outputIntentSubtype ()_a .PdfOutputIntentType {switch _fdc ._dg {case 1:return _a .PdfOutputIntentTypeA1 ;case 2:return _a .PdfOutputIntentTypeA2 ;case 3:return _a .PdfOutputIntentTypeA3 ;case 4:return _a .PdfOutputIntentTypeA4 ;
default:return 0;};};

// Validate checks if provided input document reader matches given PDF/A profile.
func Validate (d *_a .CompliancePdfReader ,profile Profile )error {return profile .ValidateStandard (d )};func _cecfd (_fbff *_a .CompliancePdfReader )(_dcebc []ViolatedRule ){return _dcebc };

// Profile1B is the implementation of the PDF/A-1B standard profile.
// Implements model.StandardImplementer, Profile interfaces.
type Profile1B struct{profile1 };func _eec (_efb *_gc .Document ,_afd func ()_e .Time )error {_aga ,_aff :=_a .NewPdfInfoFromObject (_efb .Info );if _aff !=nil {return _aff ;};if _cgf :=_gdcd (_aga ,_afd );_cgf !=nil {return _cgf ;};_efb .Info =_aga .ToPdfObject ();
return nil ;};func _bbg (_aggb *_gc .Document )error {_eab ,_afe :=_aggb .GetPages ();if !_afe {return nil ;};for _ ,_aedf :=range _eab {_cee ,_eaba :=_ag .GetArray (_aedf .Object .Get ("\u0041\u006e\u006e\u006f\u0074\u0073"));if !_eaba {continue ;};for _ ,_bba :=range _cee .Elements (){_bba =_ag .ResolveReference (_bba );
if _ ,_daff :=_bba .(*_ag .PdfObjectNull );_daff {continue ;};_fgfd ,_ebba :=_ag .GetDict (_bba );if !_ebba {continue ;};_gfaga ,_ :=_ag .GetIntVal (_fgfd .Get ("\u0046"));_gfaga &=^(1<<0);_gfaga &=^(1<<1);_gfaga &=^(1<<5);_gfaga |=1<<2;_fgfd .Set ("\u0046",_ag .MakeInteger (int64 (_gfaga )));
_eefd :=false ;if _efge :=_fgfd .Get ("\u0053u\u0062\u0074\u0079\u0070\u0065");_efge !=nil {_fag ,_ffae :=_ag .GetName (_efge );if _ffae &&_fag .String ()=="\u0057\u0069\u0064\u0067\u0065\u0074"{_eefd =true ;if _fgfd .Get ("\u0041\u0041")!=nil {_fgfd .Remove ("\u0041\u0041");
};};};if _fgfd .Get ("\u0043")!=nil ||_fgfd .Get ("\u0049\u0043")!=nil {_cgba ,_efbe :=_dgbd (_aggb );if !_efbe {_fgfd .Remove ("\u0043");_fgfd .Remove ("\u0049\u0043");}else {_dgba ,_egc :=_ag .GetIntVal (_cgba .Get ("\u004e"));if !_egc ||_dgba !=3{_fgfd .Remove ("\u0043");
_fgfd .Remove ("\u0049\u0043");};};};_ffbe ,_ebba :=_ag .GetDict (_fgfd .Get ("\u0041\u0050"));if _ebba {_dbed :=_ffbe .Get ("\u004e");if _dbed ==nil {continue ;};if len (_ffbe .Keys ())> 1{_ffbe .Clear ();_ffbe .Set ("\u004e",_dbed );};if _eefd {_caac ,_ecb :=_ag .GetName (_fgfd .Get ("\u0046\u0054"));
if _ecb &&*_caac =="\u0042\u0074\u006e"{continue ;};};};};};return nil ;};

// StandardName gets the name of the standard.
func (_efc *profile1 )StandardName ()string {return _fg .Sprintf ("\u0050D\u0046\u002f\u0041\u002d\u0031\u0025s",_efc ._acab ._db );};

// Conformance gets the PDF/A conformance.
func (_eddb *profile2 )Conformance ()string {return _eddb ._ebecg ._db };func _bbcg (_cfec *_a .CompliancePdfReader )(_bgbd []ViolatedRule ){var _fcgd ,_deecc ,_aeff ,_ddad ,_efaga ,_cegaf bool ;_afbb :=func ()bool {return _fcgd &&_deecc &&_aeff &&_ddad &&_efaga &&_cegaf };
for _ ,_gdfdd :=range _cfec .PageList {if _gdfdd .Resources ==nil {continue ;};_bebb ,_dcdgd :=_ag .GetDict (_gdfdd .Resources .Font );if !_dcdgd {continue ;};for _ ,_babc :=range _bebb .Keys (){_gacd ,_ffggd :=_ag .GetDict (_bebb .Get (_babc ));if !_ffggd {if !_fcgd {_bgbd =append (_bgbd ,_bb ("\u0036\u002e\u0032\u002e\u0031\u0031\u002e\u0032\u002d\u0031","\u0041\u006c\u006c\u0020\u0066\u006f\u006e\u0074\u0073\u0020\u0061\u006e\u0064\u0020\u0066on\u0074 \u0070\u0072\u006fg\u0072\u0061\u006ds\u0020\u0075\u0073\u0065\u0064\u0020\u0069\u006e\u0020\u0061\u0020\u0063\u006f\u006e\u0066\u006f\u0072mi\u006e\u0067\u0020\u0066\u0069\u006ce\u002c\u0020\u0072\u0065\u0067\u0061\u0072\u0064\u006c\u0065s\u0073\u0020\u006f\u0066\u0020\u0072\u0065\u006e\u0064\u0065\u0072\u0069\u006eg m\u006f\u0064\u0065\u0020\u0075\u0073\u0061\u0067\u0065\u002c\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0063\u006f\u006e\u0066\u006f\u0072\u006d\u0020\u0074o\u0020\u0074\u0068e\u0020\u0070\u0072o\u0076\u0069\u0073\u0069\u006f\u006e\u0073\u0020\u0069\u006e \u0049\u0053\u004f\u0020\u0033\u0032\u0030\u0030\u0030\u002d\u0031:\u0032\u0030\u0030\u0038\u002c \u0039\u002e\u0036\u0020a\u006e\u0064\u0020\u0039.\u0037\u002e"));
_fcgd =true ;if _afbb (){return _bgbd ;};};continue ;};if _cffe ,_bdeab :=_ag .GetName (_gacd .Get ("\u0054\u0079\u0070\u0065"));!_fcgd &&(!_bdeab ||_cffe .String ()!="\u0046\u006f\u006e\u0074"){_bgbd =append (_bgbd ,_bb ("\u0036\u002e\u0032\u002e\u0031\u0031\u002e\u0032\u002d\u0031","\u0054\u0079\u0070e\u0020\u002d\u0020\u006e\u0061\u006d\u0065\u0020\u002d\u0020\u0028\u0052\u0065\u0071\u0075i\u0072\u0065\u0064\u0029 Th\u0065\u0020\u0074\u0079\u0070\u0065\u0020\u006f\u0066 \u0050\u0044\u0046\u0020\u006fbj\u0065\u0063\u0074\u0020\u0074\u0068\u0061t\u0020\u0074\u0068\u0069s\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u0020\u0064\u0065\u0073c\u0072\u0069\u0062\u0065\u0073\u003b\u0020\u006d\u0075\u0073t\u0020\u0062\u0065\u0020\u0046\u006f\u006e\u0074\u0020\u0066\u006fr\u0020\u0061\u0020\u0066\u006f\u006e\u0074\u0020\u0064\u0069\u0063t\u0069\u006f\u006e\u0061\u0072\u0079\u002e"));
_fcgd =true ;if _afbb (){return _bgbd ;};};_ffab ,_baefc :=_a .NewPdfFontFromPdfObject (_gacd );if _baefc !=nil {continue ;};var _eebc string ;if _fdeaa ,_cafa :=_ag .GetName (_gacd .Get ("\u0053u\u0062\u0074\u0079\u0070\u0065"));_cafa {_eebc =_fdeaa .String ();
};if !_deecc {switch _eebc {case "\u0054\u0079\u0070e\u0030","\u0054\u0079\u0070e\u0031","\u004dM\u0054\u0079\u0070\u0065\u0031","\u0054\u0072\u0075\u0065\u0054\u0079\u0070\u0065","\u0043\u0049\u0044F\u006f\u006e\u0074\u0054\u0079\u0070\u0065\u0030","\u0043\u0049\u0044F\u006f\u006e\u0074\u0054\u0079\u0070\u0065\u0032":default:_deecc =true ;
_bgbd =append (_bgbd ,_bb ("\u0036\u002e\u0032\u002e\u0031\u0031\u002e\u0032\u002d\u0032","\u0053\u0075\u0062\u0074\u0079\u0070\u0065\u0020\u002d\u0020\u006e\u0061\u006d\u0065\u0020\u002d\u0020\u0028\u0052\u0065\u0071\u0075\u0069\u0072\u0065d\u0029\u0020\u0054\u0068e \u0074\u0079\u0070\u0065 \u006f\u0066\u0020\u0066\u006f\u006et\u003b\u0020\u006d\u0075\u0073\u0074\u0020b\u0065\u0020\u0022\u0054\u0079\u0070\u0065\u0031\u0022\u0020f\u006f\u0072\u0020\u0054\u0079\u0070\u0065\u0020\u0031\u0020f\u006f\u006e\u0074\u0073\u002c\u0020\u0022\u004d\u004d\u0054\u0079\u0070\u0065\u0031\u0022\u0020\u0066\u006f\u0072\u0020\u006d\u0075\u006c\u0074\u0069\u0070\u006c\u0065\u0020\u006da\u0073\u0074e\u0072\u0020\u0066\u006f\u006e\u0074s\u002c\u0020\u0022\u0054\u0072\u0075\u0065T\u0079\u0070\u0065\u0022\u0020\u0066\u006f\u0072\u0020\u0054\u0072\u0075\u0065T\u0079\u0070\u0065\u0020\u0066\u006f\u006e\u0074\u0073\u0020\u0022\u0054\u0079\u0070\u0065\u0033\u0022\u0020\u0066\u006f\u0072\u0020\u0054\u0079\u0070e\u0020\u0033\u0020\u0066\u006f\u006e\u0074\u0073\u002c\u0020\"\u0054\u0079\u0070\u0065\u0030\"\u0020\u0066\u006f\u0072\u0020\u0054\u0079\u0070\u0065\u0020\u0030\u0020\u0066\u006f\u006e\u0074\u0073\u0020\u0061\u006ed\u0020\u0022\u0043\u0049\u0044\u0046\u006fn\u0074\u0054\u0079\u0070\u0065\u0030\u0022 \u006f\u0072\u0020\u0022\u0043\u0049\u0044\u0046\u006f\u006e\u0074T\u0079\u0070e\u0032\u0022\u0020\u0066\u006f\u0072\u0020\u0043\u0049\u0044\u0020\u0066\u006f\u006e\u0074\u0073\u002e"));
if _afbb (){return _bgbd ;};};};if !_aeff {if _eebc !="\u0054\u0079\u0070e\u0033"{_daffd ,_gcdff :=_ag .GetName (_gacd .Get ("\u0042\u0061\u0073\u0065\u0046\u006f\u006e\u0074"));if !_gcdff ||_daffd .String ()==""{_bgbd =append (_bgbd ,_bb ("\u0036\u002e\u0032\u002e\u0031\u0031\u002e\u0032\u002d\u0033","B\u0061\u0073\u0065\u0046\u006f\u006e\u0074\u0020\u002d\u0020\u006e\u0061\u006d\u0065\u0020\u002d\u0020\u0028\u0052\u0065\u0071\u0075\u0069\u0072\u0065\u0064)\u0020T\u0068\u0065\u0020\u0050o\u0073\u0074S\u0063\u0072\u0069\u0070\u0074\u0020\u006e\u0061\u006d\u0065\u0020\u006f\u0066\u0020\u0074\u0068\u0065\u0020\u0066\u006f\u006e\u0074\u002e"));
_aeff =true ;if _afbb (){return _bgbd ;};};};};if _eebc !="\u0054\u0079\u0070e\u0031"{continue ;};_efdfb :=_fba .IsStdFont (_fba .StdFontName (_ffab .BaseFont ()));if _efdfb {continue ;};_fgea ,_bfeaf :=_ag .GetIntVal (_gacd .Get ("\u0046i\u0072\u0073\u0074\u0043\u0068\u0061r"));
if !_bfeaf &&!_ddad {_bgbd =append (_bgbd ,_bb ("\u0036\u002e\u0032\u002e\u0031\u0031\u002e\u0032\u002d\u0034","\u0046\u0069r\u0073t\u0043\u0068\u0061\u0072\u0020\u002d\u0020\u0069\u006e\u0074\u0065\u0067\u0065\u0072\u0020\u002d\u0020\u0028\u0052\u0065\u0071\u0075i\u0072\u0065\u0064\u0020\u0065\u0078\u0063\u0065\u0070t\u0020\u0066\u006f\u0072\u0020\u0074h\u0065\u0020\u0073\u0074\u0061\u006e\u0064\u0061\u0072d\u0020\u0031\u0034\u0020\u0066\u006f\u006e\u0074\u0073\u0029\u0020\u0054\u0068\u0065\u0020\u0066\u0069\u0072\u0073\u0074\u0020\u0063\u0068\u0061\u0072\u0061\u0063\u0074\u0065\u0072\u0020\u0063\u006f\u0064e\u0020\u0064\u0065\u0066i\u006ee\u0064\u0020\u0069\u006e\u0020\u0074\u0068\u0065\u0020\u0066\u006f\u006e\u0074\u0027\u0073\u0020\u0057i\u0064\u0074\u0068\u0073 \u0061r\u0072\u0061y\u002e"));
_ddad =true ;if _afbb (){return _bgbd ;};};_edfd ,_gdbecg :=_ag .GetIntVal (_gacd .Get ("\u004c\u0061\u0073\u0074\u0043\u0068\u0061\u0072"));if !_gdbecg &&!_efaga {_bgbd =append (_bgbd ,_bb ("\u0036\u002e\u0032\u002e\u0031\u0031\u002e\u0032\u002d\u0035","\u004c\u0061\u0073t\u0043\u0068\u0061\u0072\u0020\u002d\u0020\u0069n\u0074\u0065\u0067e\u0072 \u002d\u0020\u0028\u0052\u0065\u0071u\u0069\u0072\u0065d\u0020\u0065\u0078\u0063\u0065\u0070\u0074\u0020\u0066\u006f\u0072\u0020t\u0068\u0065 s\u0074\u0061\u006e\u0064\u0061\u0072\u0064\u0020\u0031\u0034\u0020\u0066\u006f\u006ets\u0029\u0020\u0054\u0068\u0065\u0020\u006c\u0061\u0073t\u0020\u0063\u0068\u0061\u0072\u0061\u0063\u0074\u0065\u0072\u0020\u0063\u006f\u0064\u0065\u0020\u0064\u0065\u0066\u0069\u006e\u0065\u0064\u0020\u0069\u006e\u0020\u0074\u0068\u0065\u0020\u0066\u006f\u006e\u0074\u0027\u0073\u0020\u0057\u0069\u0064\u0074h\u0073\u0020\u0061\u0072\u0072\u0061\u0079\u002e"));
_efaga =true ;if _afbb (){return _bgbd ;};};if !_cegaf {_bdab ,_gdabg :=_ag .GetArray (_gacd .Get ("\u0057\u0069\u0064\u0074\u0068\u0073"));if !_gdabg ||!_bfeaf ||!_gdbecg ||_bdab .Len ()!=_edfd -_fgea +1{_bgbd =append (_bgbd ,_bb ("\u0036\u002e\u0032\u002e\u0031\u0031\u002e\u0032\u002d\u0036","\u0057\u0069\u0064\u0074\u0068\u0073\u0020\u002d a\u0072\u0072\u0061y \u002d\u0020\u0028\u0052\u0065\u0071\u0075\u0069\u0072\u0065\u0064\u0020\u0065\u0078\u0063\u0065\u0070t\u0020\u0066\u006f\u0072\u0020\u0074\u0068\u0065\u0020\u0073\u0074a\u006e\u0064a\u0072\u0064\u00201\u0034\u0020\u0066\u006f\u006e\u0074\u0073\u003b\u0020\u0069\u006ed\u0069\u0072\u0065\u0063\u0074\u0020\u0072\u0065\u0066\u0065\u0072\u0065\u006e\u0063\u0065\u0020\u0070\u0072\u0065\u0066e\u0072\u0072e\u0064\u0029\u0020\u0041\u006e \u0061\u0072\u0072\u0061\u0079\u0020\u006f\u0066\u0020\u0028\u004c\u0061\u0073\u0074\u0043\u0068\u0061\u0072\u0020\u2212 F\u0069\u0072\u0073\u0074\u0043\u0068\u0061\u0072\u0020\u002b\u00201\u0029\u0020\u0077\u0069\u0064\u0074\u0068\u0073."));
_cegaf =true ;if _afbb (){return _bgbd ;};};};};};return _bgbd ;};func _dfbe (_bbcf *_a .CompliancePdfReader )ViolatedRule {return _eag };func _gcdd (_afg *_gc .Document )error {_abdd ,_ebc :=_afg .FindCatalog ();if !_ebc {return nil ;};_ ,_ebc =_abdd .GetStructTreeRoot ();
if !_ebc {_cce :=_a .NewStructTreeRoot ();_efe :=_cce .ToPdfObject ().(*_ag .PdfIndirectObject );_bee :=_efe .PdfObject .(*_ag .PdfObjectDictionary );_abdd .SetStructTreeRoot (_bee );};return nil ;};var _ Profile =(*Profile2U )(nil );func _eaab (_dcgbca *_a .CompliancePdfReader ,_adcec standardType )(_aadf []ViolatedRule ){var _dabcd ,_dfee ,_egcf ,_aece ,_ccgd ,_eadefd ,_gdagd ,_cgbg ,_bbdf ,_efed ,_bdeea bool ;
_cge :=func ()bool {return _dabcd &&_dfee &&_egcf &&_aece &&_ccgd &&_eadefd &&_gdagd &&_cgbg &&_bbdf &&_efed &&_bdeea ;};_fbadb :=map[*_ag .PdfObjectStream ]*_ab .CMap {};_bfca :=map[*_ag .PdfObjectStream ][]byte {};_cgggb :=map[_ag .PdfObject ]*_a .PdfFont {};
for _ ,_edfab :=range _dcgbca .GetObjectNums (){_acdg ,_fabe :=_dcgbca .GetIndirectObjectByNumber (_edfab );if _fabe !=nil {continue ;};_begb ,_eacdf :=_ag .GetDict (_acdg );if !_eacdf {continue ;};_eeadd ,_eacdf :=_ag .GetName (_begb .Get ("\u0054\u0079\u0070\u0065"));
if !_eacdf {continue ;};if *_eeadd !="\u0046\u006f\u006e\u0074"{continue ;};_ffde ,_fabe :=_a .NewPdfFontFromPdfObject (_begb );if _fabe !=nil {_g .Log .Debug ("g\u0065\u0074\u0074\u0069\u006e\u0067 \u0066\u006f\u006e\u0074\u0020\u0066r\u006f\u006d\u0020\u006f\u0062\u006a\u0065c\u0074\u0020\u0066\u0061\u0069\u006c\u0065\u0064\u003a\u0020%\u0076",_fabe );
continue ;};_cgggb [_begb ]=_ffde ;};for _ ,_beab :=range _dcgbca .PageList {_beff ,_bfgae :=_beab .GetContentStreams ();if _bfgae !=nil {_g .Log .Debug ("G\u0065\u0074\u0074\u0069\u006e\u0067 \u0070\u0061\u0067\u0065\u0020\u0063o\u006e\u0074\u0065\u006e\u0074\u0020\u0073t\u0072\u0065\u0061\u006d\u0073\u0020\u0066\u0061\u0069\u006ce\u0064");
continue ;};for _ ,_bead :=range _beff {_bcee :=_fbd .NewContentStreamParser (_bead );_defd ,_aceee :=_bcee .Parse ();if _aceee !=nil {_g .Log .Debug ("\u0070\u0061\u0072\u0073\u0069\u006e\u0067\u0020\u0063\u006f\u006e\u0074\u0065\u006e\u0074s\u0074r\u0065\u0061\u006d\u0020\u0066\u0061\u0069\u006c\u0065\u0064\u003a\u0020\u0025\u0076",_aceee );
continue ;};var _dadab bool ;for _ ,_gdbgd :=range *_defd {if _gdbgd .Operand !="\u0054\u0072"{continue ;};if len (_gdbgd .Params )!=1{_g .Log .Debug ("\u0069\u006e\u0076\u0061\u006ci\u0064\u0020\u006e\u0075\u006d\u0062\u0065r\u0020\u006f\u0066\u0020\u0070\u0061\u0072\u0061\u006d\u0065\u0074\u0065\u0072\u0073\u0020\u0066\u006f\u0072\u0020\u0074\u0068\u0065\u0020\u0027\u0054\u0072\u0027\u0020\u006f\u0070\u0065\u0072\u0061\u006e\u0064\u002c\u0020\u0065\u0078\u0070e\u0063\u0074\u0065\u0064\u0020\u0027\u0031\u0027\u0020\u0062\u0075\u0074 \u0069\u0073\u003a\u0020\u0027\u0025d\u0027",len (_gdbgd .Params ));
continue ;};_dbga ,_cdce :=_ag .GetIntVal (_gdbgd .Params [0]);if !_cdce {_g .Log .Debug ("\u0072\u0065\u006e\u0064\u0065\u0072\u0069\u006e\u0067\u0020\u006d\u006f\u0064\u0065\u0020i\u0073 \u006e\u006f\u0074\u0020\u0061\u006e\u0020\u0069\u006e\u0074\u0065\u0067\u0065\u0072");
continue ;};if _dbga ==3{_dadab =true ;break ;};};for _ ,_dbbee :=range *_defd {if _dbbee .Operand !="\u0054\u0066"{continue ;};if len (_dbbee .Params )!=2{_g .Log .Debug ("i\u006eva\u006ci\u0064 \u006e\u0075\u006d\u0062\u0065r\u0020\u006f\u0066 \u0070\u0061\u0072\u0061\u006de\u0074\u0065\u0072s\u0020\u0066\u006f\u0072\u0020\u0074\u0068\u0065\u0020\u0027\u0054f\u0027\u0020\u006fper\u0061\u006e\u0064\u002c\u0020\u0065x\u0070\u0065\u0063\u0074\u0065\u0064\u0020\u0027\u0032\u0027\u0020\u0069s\u003a \u0027\u0025\u0064\u0027",len (_dbbee .Params ));
continue ;};_gbeeb ,_eebfd :=_ag .GetName (_dbbee .Params [0]);if !_eebfd {_g .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a \u0054\u0066\u0020\u006f\u0070\u003d\u0025\u0073\u0020\u0047\u0065\u0074\u004ea\u006d\u0065\u0056\u0061\u006c\u0020\u0066a\u0069\u006c\u0065\u0064",_dbbee );
continue ;};_fgffg ,_effg :=_beab .Resources .GetFontByName (*_gbeeb );if !_effg {_g .Log .Debug ("\u0066\u006f\u006e\u0074\u0020\u006e\u006f\u0074\u0020f\u006f\u0075\u006e\u0064");continue ;};_affa ,_eebfd :=_ag .GetDict (_fgffg );if !_eebfd {_g .Log .Debug ("\u0066\u006f\u006e\u0074 d\u0069\u0063\u0074\u0020\u006e\u006f\u0074\u0020\u0066\u006f\u0075\u006e\u0064");
continue ;};_ddgg ,_eebfd :=_cgggb [_affa ];if !_eebfd {var _bbbb error ;_ddgg ,_bbbb =_a .NewPdfFontFromPdfObject (_affa );if _bbbb !=nil {_g .Log .Debug ("\u0067\u0065\u0074\u0074i\u006e\u0067\u0020\u0066\u006f\u006e\u0074\u0020\u0066\u0072o\u006d \u006f\u0062\u006a\u0065\u0063\u0074\u003a \u0025\u0076",_bbbb );
continue ;};_cgggb [_affa ]=_ddgg ;};if !_dabcd {_geecc :=_aggbe (_affa ,_bfca ,_fbadb );if _geecc !=_eag {_aadf =append (_aadf ,_geecc );_dabcd =true ;if _cge (){return _aadf ;};};};if !_dfee {_badff :=_egeb (_affa );if _badff !=_eag {_aadf =append (_aadf ,_badff );
_dfee =true ;if _cge (){return _aadf ;};};};if !_egcf {_dfbf :=_bgfde (_affa ,_bfca ,_fbadb );if _dfbf !=_eag {_aadf =append (_aadf ,_dfbf );_egcf =true ;if _cge (){return _aadf ;};};};if !_aece {_bcgb :=_fgdf (_affa ,_bfca ,_fbadb );if _bcgb !=_eag {_aadf =append (_aadf ,_bcgb );
_aece =true ;if _cge (){return _aadf ;};};};if !_ccgd {_efec :=_adgba (_ddgg ,_affa ,_dadab );if _efec !=_eag {_ccgd =true ;_aadf =append (_aadf ,_efec );if _cge (){return _aadf ;};};};if !_eadefd {_edfg :=_ccegb (_ddgg ,_affa );if _edfg !=_eag {_eadefd =true ;
_aadf =append (_aadf ,_edfg );if _cge (){return _aadf ;};};};if !_gdagd {_eecd :=_fgad (_ddgg ,_affa );if _eecd !=_eag {_gdagd =true ;_aadf =append (_aadf ,_eecd );if _cge (){return _aadf ;};};};if !_cgbg {_dfg :=_bfdb (_ddgg ,_affa );if _dfg !=_eag {_cgbg =true ;
_aadf =append (_aadf ,_dfg );if _cge (){return _aadf ;};};};if !_bbdf {_fbfg :=_bcbg (_ddgg ,_affa );if _fbfg !=_eag {_bbdf =true ;_aadf =append (_aadf ,_fbfg );if _cge (){return _aadf ;};};};if !_efed {_ffec :=_fgbf (_ddgg ,_affa );if _ffec !=_eag {_efed =true ;
_aadf =append (_aadf ,_ffec );if _cge (){return _aadf ;};};};if !_bdeea &&_adcec ._db =="\u0041"{_ebg :=_afeaa (_affa ,_bfca ,_fbadb );if _ebg !=_eag {_bdeea =true ;_aadf =append (_aadf ,_ebg );if _cge (){return _aadf ;};};};};};};return _aadf ;};type documentImages struct{_fc ,_fgb ,_ac bool ;
_bbe map[_ag .PdfObject ]struct{};_cdd []*imageInfo ;};func _eeagbe (_bgfgc *_ag .PdfObjectDictionary ,_gafa map[*_ag .PdfObjectStream ][]byte ,_bbeb map[*_ag .PdfObjectStream ]*_ab .CMap )ViolatedRule {const (_eeebc ="\u0046\u006f\u0072\u0020\u0061\u006e\u0079\u0020\u0067\u0069\u0076\u0065\u006e\u0020\u0063\u006f\u006d\u0070o\u0073\u0069\u0074e\u0020\u0028\u0054\u0079\u0070\u0065\u0020\u0030\u0029 \u0066\u006fn\u0074\u0020\u0077\u0069\u0074\u0068\u0069\u006e \u0061\u0020\u0063\u006fn\u0066\u006fr\u006d\u0069\u006e\u0067\u0020\u0066\u0069\u006c\u0065\u002c\u0020\u0074\u0068\u0065\u0020\u0043\u0049\u0044\u0053\u0079\u0073\u0074\u0065\u006d\u0049\u006e\u0066\u006f \u0065\u006e\u0074\u0072\u0079\u0020\u0069\u006e\u0020\u0069\u0074\u0073 \u0043\u0049\u0044\u0046\u006f\u006e\u0074\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u0020\u0061\u006e\u0064\u0020\u0069\u0074\u0073\u0020\u0045\u006e\u0063\u006f\u0064\u0069\u006e\u0067\u0020\u0064\u0069\u0063\u0074\u0069o\u006e\u0061\u0072y\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0068\u0061\u0076\u0065\u0020\u0074\u0068\u0065\u0020\u0066\u006fl\u006c\u006f\u0077\u0069\u006e\u0067\u0020\u0072\u0065l\u0061t\u0069\u006f\u006e\u0073\u0068\u0069\u0070. \u0049\u0066\u0020\u0074\u0068\u0065\u0020\u0045\u006e\u0063\u006f\u0064\u0069\u006e\u0067\u0020\u006b\u0065\u0079 \u0069\u006e\u0020\u0074\u0068\u0065\u0020\u0054\u0079\u0070\u0065\u0020\u0030 \u0066\u006f\u006e\u0074\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079 \u0069\u0073\u0020I\u0064\u0065n\u0074\u0069\u0074\u0079\u002d\u0048\u0020\u006f\u0072\u0020\u0049\u0064\u0065\u006e\u0074\u0069\u0074\u0079\u002d\u0056\u002c\u0020\u0061\u006e\u0079\u0020v\u0061\u006c\u0075\u0065\u0073\u0020\u006f\u0066\u0020\u0052\u0065\u0067\u0069\u0073\u0074\u0072\u0079\u002c\u0020\u004f\u0072\u0064\u0065\u0072\u0069\u006e\u0067\u002c\u0020\u0061\u006e\u0064\u0020\u0053up\u0070\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006d\u0061\u0079\u0020\u0062\u0065\u0020\u0075\u0073\u0065\u0064\u0020\u0069n\u0020\u0074h\u0065\u0020\u0043\u0049\u0044\u0053\u0079\u0073\u0074\u0065\u006d\u0049\u006e\u0066\u006f\u0020\u0065\u006e\u0074r\u0079\u0020\u006ff\u0020\u0074\u0068\u0065\u0020\u0043\u0049\u0044F\u006f\u006e\u0074\u002e\u0020\u004f\u0074\u0068\u0065\u0072\u0077\u0069\u0073\u0065\u002c\u0020\u0074\u0068\u0065\u0020\u0063\u006f\u0072\u0072\u0065\u0073\u0070\u006f\u006e\u0064\u0069\u006e\u0067\u0020\u0052\u0065\u0067\u0069\u0073\u0074\u0072\u0079\u0020a\u006e\u0064\u0020\u004f\u0072\u0064\u0065\u0072\u0069\u006e\u0067\u0020s\u0074\u0072\u0069\u006e\u0067\u0073\u0020\u0069\u006e\u0020\u0062\u006f\u0074h\u0020\u0043\u0049\u0044\u0053\u0079\u0073\u0074\u0065m\u0049\u006e\u0066\u006f\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0069\u0065\u0073\u0020\u0073\u0068\u0061\u006cl\u0020\u0062\u0065\u0020i\u0064en\u0074\u0069\u0063\u0061\u006c\u002c \u0061n\u0064\u0020\u0074\u0068\u0065\u0020v\u0061\u006c\u0075\u0065\u0020\u006f\u0066\u0020\u0074\u0068\u0065\u0020\u0053\u0075\u0070\u0070l\u0065\u006d\u0065\u006e\u0074 \u006b\u0065\u0079\u0020\u0069\u006e\u0020t\u0068\u0065\u0020\u0043I\u0044S\u0079\u0073\u0074\u0065\u006d\u0049\u006e\u0066o\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u0020\u006ff\u0020\u0074\u0068\u0065\u0020\u0043\u0049\u0044\u0046\u006f\u006e\u0074\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0062\u0065\u0020\u0067re\u0061\u0074\u0065\u0072\u0020\u0074\u0068\u0061\u006e\u0020\u006f\u0072\u0020\u0065\u0071\u0075\u0061\u006c\u0020\u0074\u006f t\u0068\u0065\u0020\u0053\u0075\u0070\u0070\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006b\u0065\u0079\u0020\u0069\u006e\u0020\u0074h\u0065\u0020\u0043\u0049\u0044\u0053\u0079\u0073\u0074\u0065\u006d\u0049\u006e\u0066o\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u0020\u006ff\u0020\u0074\u0068\u0065\u0020\u0043M\u0061p\u002e";
_eaggd ="\u0036\u002e\u0032\u002e\u0031\u0031\u002e\u0033\u002d\u0031";);var _dabb string ;if _efdca ,_cgdfg :=_ag .GetName (_bgfgc .Get ("\u0053u\u0062\u0074\u0079\u0070\u0065"));_cgdfg {_dabb =_efdca .String ();};if _dabb !="\u0054\u0079\u0070e\u0030"{return _eag ;
};_fdae :=_bgfgc .Get ("\u0045\u006e\u0063\u006f\u0064\u0069\u006e\u0067");if _baac ,_gfdd :=_ag .GetName (_fdae );_gfdd {switch _baac .String (){case "\u0049\u0064\u0065\u006e\u0074\u0069\u0074\u0079\u002d\u0048","\u0049\u0064\u0065\u006e\u0074\u0069\u0074\u0079\u002d\u0056":return _eag ;
};_deaf ,_dabf :=_ab .LoadPredefinedCMap (_baac .String ());if _dabf !=nil {return _bb (_eaggd ,_eeebc );};_ggaea :=_deaf .CIDSystemInfo ();if _ggaea .Ordering !=_ggaea .Registry {return _bb (_eaggd ,_eeebc );};return _eag ;};_ggad ,_ddeg :=_ag .GetStream (_fdae );
if !_ddeg {return _bb (_eaggd ,_eeebc );};_gbbfc ,_dfae :=_gfbb (_ggad ,_gafa ,_bbeb );if _dfae !=nil {return _bb (_eaggd ,_eeebc );};_dbddf :=_gbbfc .CIDSystemInfo ();if _dbddf .Ordering !=_dbddf .Registry {return _bb (_eaggd ,_eeebc );};return _eag ;
};func _eddf (_acda *_a .CompliancePdfReader )(_cbfe []ViolatedRule ){_efde :=func (_gdde *_ag .PdfObjectDictionary ,_dedad *[]string ,_bebae *[]ViolatedRule )error {_cabe :=_gdde .Get ("\u004e\u0061\u006d\u0065");if _cabe ==nil ||len (_cabe .String ())==0{*_bebae =append (*_bebae ,_bb ("\u0036\u002e\u0039-\u0031","\u0045\u0061\u0063\u0068\u0020o\u0070\u0074\u0069\u006f\u006e\u0061l\u0020\u0063\u006f\u006e\u0074\u0065\u006et\u0020\u0063\u006fn\u0066\u0069\u0067\u0075r\u0061\u0074\u0069\u006f\u006e\u0020d\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0063o\u006e\u0074\u0061\u0069\u006e\u0020\u0074\u0068\u0065\u0020\u004e\u0061\u006d\u0065\u0020\u006b\u0065\u0079\u002e"));
};for _ ,_acaf :=range *_dedad {if _acaf ==_cabe .String (){*_bebae =append (*_bebae ,_bb ("\u0036\u002e\u0039-\u0032","\u0045\u0061\u0063\u0068\u0020\u006f\u0070\u0074\u0069\u006f\u006e\u0061l\u0020\u0063\u006f\u006e\u0074\u0065\u006e\u0074\u0020\u0063\u006f\u006e\u0066\u0069\u0067\u0075\u0072a\u0074\u0069\u006fn\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u0020\u0073\u0068a\u006c\u006c\u0020\u0063\u006f\u006e\u0074\u0061\u0069\u006e\u0020\u0074\u0068\u0065\u0020N\u0061\u006d\u0065\u0020\u006b\u0065\u0079\u002c w\u0068\u006fs\u0065\u0020\u0076\u0061\u006c\u0075\u0065\u0020s\u0068\u0061\u006c\u006c\u0020\u0062\u0065\u0020\u0075ni\u0071\u0075\u0065 \u0061\u006d\u006f\u006e\u0067\u0073\u0074\u0020\u0061\u006c\u006c\u0020o\u0070\u0074\u0069\u006f\u006e\u0061\u006c\u0020\u0063\u006fn\u0074\u0065\u006e\u0074 \u0063\u006f\u006e\u0066\u0069\u0067u\u0072\u0061\u0074\u0069\u006f\u006e\u0020\u0064\u0069\u0063\u0074i\u006fn\u0061\u0072\u0069\u0065\u0073\u0020\u0077\u0069\u0074\u0068\u0069\u006e\u0020\u0074\u0068e\u0020\u0050\u0044\u0046\u002fA\u002d\u0032\u0020\u0066\u0069l\u0065\u002e"));
}else {*_dedad =append (*_dedad ,_cabe .String ());};};if _gdde .Get ("\u0041\u0053")!=nil {*_bebae =append (*_bebae ,_bb ("\u0036\u002e\u0039-\u0034","Th\u0065\u0020\u0041\u0053\u0020\u006b\u0065y \u0073\u0068\u0061\u006c\u006c\u0020\u006e\u006f\u0074\u0020\u0061\u0070\u0070\u0065\u0061r\u0020\u0069\u006e\u0020\u0061\u006e\u0079\u0020\u006f\u0070\u0074\u0069\u006f\u006e\u0061\u006c\u0020\u0063\u006f\u006et\u0065\u006e\u0074\u0020\u0063\u006fn\u0066\u0069\u0067\u0075\u0072\u0061\u0074\u0069\u006fn\u0020d\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u002e"));
};return nil ;};_gaef ,_begg :=_gfcb (_acda );if !_begg {return _cbfe ;};_ffdeba ,_begg :=_ag .GetDict (_gaef .Get ("\u004f\u0043\u0050r\u006f\u0070\u0065\u0072\u0074\u0069\u0065\u0073"));if !_begg {return _cbfe ;};var _fedga []string ;_cccfb ,_begg :=_ag .GetDict (_ffdeba .Get ("\u0044"));
if _begg {_efde (_cccfb ,&_fedga ,&_cbfe );};_fafd ,_begg :=_ag .GetArray (_ffdeba .Get ("\u0043o\u006e\u0066\u0069\u0067\u0073"));if _begg {for _ddga :=0;_ddga < _fafd .Len ();_ddga ++{_gbeda ,_bfcdf :=_ag .GetDict (_fafd .Get (_ddga ));if !_bfcdf {continue ;
};_efde (_gbeda ,&_fedga ,&_cbfe );};};return _cbfe ;};func _aafg (_egegb *_a .CompliancePdfReader )ViolatedRule {_bacg :=_egegb .ParserMetadata ();if _bacg .HasInvalidSeparationAfterXRef (){return _bb ("\u0036.\u0031\u002e\u0034\u002d\u0032","\u0054\u0068\u0065 \u0078\u0072\u0065\u0066\u0020\u006b\u0065\u0079\u0077\u006fr\u0064\u0020\u0061\u006e\u0064\u0020\u0074\u0068\u0065\u0020\u0063\u0072\u006f\u0073s\u0020\u0072\u0065\u0066e\u0072\u0065\u006e\u0063\u0065 s\u0075b\u0073\u0065\u0063ti\u006f\u006e\u0020\u0068\u0065\u0061\u0064e\u0072\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0062\u0065\u0020\u0073\u0065\u0070\u0061\u0072\u0061\u0074\u0065\u0064\u0020\u0062\u0079 \u0061\u0020\u0073i\u006e\u0067\u006c\u0065\u0020\u0045\u004fL\u0020\u006d\u0061\u0072\u006b\u0065\u0072\u002e");
};return _eag ;};func _dadda (_faeb *_a .CompliancePdfReader )ViolatedRule {return _eag };func _eaege (_cbgcg *_a .CompliancePdfReader )ViolatedRule {_ggec :=map[*_ag .PdfObjectStream ]struct{}{};for _ ,_daeg :=range _cbgcg .PageList {if _daeg .Resources ==nil &&_daeg .Contents ==nil {continue ;
};if _ccfa :=_daeg .GetPageDict ();_ccfa !=nil {_aadc ,_bcef :=_ag .GetDict (_ccfa .Get ("\u0047\u0072\u006fu\u0070"));if _bcef {if _cfbad :=_aadc .Get ("\u0053");_cfbad !=nil {_gcaba ,_ggeg :=_ag .GetName (_cfbad );if _ggeg &&_gcaba .String ()=="\u0054\u0072\u0061n\u0073\u0070\u0061\u0072\u0065\u006e\u0063\u0079"{return _bb ("\u0036\u002e\u0034-\u0033","\u0041\u0020\u0047\u0072\u006f\u0075\u0070\u0020\u006f\u0062\u006a\u0065\u0063\u0074\u0020\u0077\u0069\u0074\u0068\u0020\u0061\u006e\u0020\u0053\u0020\u0078Ob\u006a\u0065c\u0074\u0020\u0077\u0069\u0074h\u0020\u0061\u0020\u0076a\u006c\u0075\u0065\u0020o\u0066\u0020\u0054\u0072\u0061\u006e\u0073\u0070\u0061\u0072\u0065\u006e\u0063\u0079 \u0073\u0068\u0061\u006c\u006c\u0020\u006eo\u0074\u0020\u0062\u0065\u0020i\u006e\u0063\u006c\u0075\u0064\u0065\u0064\u0020\u0069\u006e\u0020\u0061\u0020\u0066\u006f\u0072\u006d\u0020\u0058\u004f\u0062je\u0063\u0074\u002e\n\u0041 \u0047\u0072\u006f\u0075p\u0020\u006f\u0062j\u0065\u0063\u0074\u0020\u0077\u0069\u0074\u0068\u0020\u0061\u006e\u0020S\u0020\u0078\u004fb\u006a\u0065\u0063\u0074\u0020\u0077\u0069\u0074\u0068\u0020\u0061\u0020v\u0061\u006c\u0075\u0065\u0020o\u0066\u0020\u0054\u0072\u0061n\u0073\u0070\u0061\u0072\u0065\u006ec\u0079\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u006e\u006f\u0074\u0020\u0062\u0065\u0020i\u006e\u0063\u006c\u0075\u0064e\u0064\u0020\u0069\u006e\u0020\u0061\u0020\u0070\u0061\u0067\u0065\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u002e");
};};};};if _daeg .Resources !=nil {if _fdcd ,_dcbb :=_ag .GetDict (_daeg .Resources .XObject );_dcbb {for _ ,_gcgc :=range _fdcd .Keys (){_ddfbf ,_cgca :=_ag .GetStream (_fdcd .Get (_gcgc ));if !_cgca {continue ;};if _ ,_afab :=_ggec [_ddfbf ];_afab {continue ;
};_bcdf ,_cgca :=_ag .GetDict (_ddfbf .Get ("\u0047\u0072\u006fu\u0070"));if !_cgca {_ggec [_ddfbf ]=struct{}{};continue ;};_bdafa :=_bcdf .Get ("\u0053");if _bdafa !=nil {_dgaf ,_bggf :=_ag .GetName (_bdafa );if _bggf &&_dgaf .String ()=="\u0054\u0072\u0061n\u0073\u0070\u0061\u0072\u0065\u006e\u0063\u0079"{return _bb ("\u0036\u002e\u0034-\u0033","\u0041\u0020\u0047\u0072\u006f\u0075\u0070\u0020\u006f\u0062\u006a\u0065\u0063\u0074\u0020\u0077\u0069\u0074\u0068\u0020\u0061\u006e\u0020\u0053\u0020\u0078Ob\u006a\u0065c\u0074\u0020\u0077\u0069\u0074h\u0020\u0061\u0020\u0076a\u006c\u0075\u0065\u0020o\u0066\u0020\u0054\u0072\u0061\u006e\u0073\u0070\u0061\u0072\u0065\u006e\u0063\u0079 \u0073\u0068\u0061\u006c\u006c\u0020\u006eo\u0074\u0020\u0062\u0065\u0020i\u006e\u0063\u006c\u0075\u0064\u0065\u0064\u0020\u0069\u006e\u0020\u0061\u0020\u0066\u006f\u0072\u006d\u0020\u0058\u004f\u0062je\u0063\u0074\u002e\n\u0041 \u0047\u0072\u006f\u0075p\u0020\u006f\u0062j\u0065\u0063\u0074\u0020\u0077\u0069\u0074\u0068\u0020\u0061\u006e\u0020S\u0020\u0078\u004fb\u006a\u0065\u0063\u0074\u0020\u0077\u0069\u0074\u0068\u0020\u0061\u0020v\u0061\u006c\u0075\u0065\u0020o\u0066\u0020\u0054\u0072\u0061n\u0073\u0070\u0061\u0072\u0065\u006ec\u0079\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u006e\u006f\u0074\u0020\u0062\u0065\u0020i\u006e\u0063\u006c\u0075\u0064e\u0064\u0020\u0069\u006e\u0020\u0061\u0020\u0070\u0061\u0067\u0065\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u002e");
};};_ggec [_ddfbf ]=struct{}{};continue ;};};};if _daeg .Contents !=nil {_cffb ,_dbee :=_daeg .GetContentStreams ();if _dbee !=nil {continue ;};for _ ,_cgee :=range _cffb {_gbdebg ,_cgdeg :=_fbd .NewContentStreamParser (_cgee ).Parse ();if _cgdeg !=nil {continue ;
};for _ ,_abbb :=range *_gbdebg {if len (_abbb .Params )==0{continue ;};_fgcb ,_aecc :=_ag .GetName (_abbb .Params [0]);if !_aecc {continue ;};_acde ,_egdad :=_daeg .Resources .GetXObjectByName (*_fgcb );if _egdad !=_a .XObjectTypeForm {continue ;};if _ ,_afag :=_ggec [_acde ];
_afag {continue ;};_bbee ,_aecc :=_ag .GetDict (_acde .Get ("\u0047\u0072\u006fu\u0070"));if !_aecc {_ggec [_acde ]=struct{}{};continue ;};_efadb :=_bbee .Get ("\u0053");if _efadb !=nil {_dgga ,_ffbd :=_ag .GetName (_efadb );if _ffbd &&_dgga .String ()=="\u0054\u0072\u0061n\u0073\u0070\u0061\u0072\u0065\u006e\u0063\u0079"{return _bb ("\u0036\u002e\u0034-\u0033","\u0041\u0020\u0047\u0072\u006f\u0075\u0070\u0020\u006f\u0062\u006a\u0065\u0063\u0074\u0020\u0077\u0069\u0074\u0068\u0020\u0061\u006e\u0020\u0053\u0020\u0078Ob\u006a\u0065c\u0074\u0020\u0077\u0069\u0074h\u0020\u0061\u0020\u0076a\u006c\u0075\u0065\u0020o\u0066\u0020\u0054\u0072\u0061\u006e\u0073\u0070\u0061\u0072\u0065\u006e\u0063\u0079 \u0073\u0068\u0061\u006c\u006c\u0020\u006eo\u0074\u0020\u0062\u0065\u0020i\u006e\u0063\u006c\u0075\u0064\u0065\u0064\u0020\u0069\u006e\u0020\u0061\u0020\u0066\u006f\u0072\u006d\u0020\u0058\u004f\u0062je\u0063\u0074\u002e\n\u0041 \u0047\u0072\u006f\u0075p\u0020\u006f\u0062j\u0065\u0063\u0074\u0020\u0077\u0069\u0074\u0068\u0020\u0061\u006e\u0020S\u0020\u0078\u004fb\u006a\u0065\u0063\u0074\u0020\u0077\u0069\u0074\u0068\u0020\u0061\u0020v\u0061\u006c\u0075\u0065\u0020o\u0066\u0020\u0054\u0072\u0061n\u0073\u0070\u0061\u0072\u0065\u006ec\u0079\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u006e\u006f\u0074\u0020\u0062\u0065\u0020i\u006e\u0063\u006c\u0075\u0064e\u0064\u0020\u0069\u006e\u0020\u0061\u0020\u0070\u0061\u0067\u0065\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u002e");
};};_ggec [_acde ]=struct{}{};};};};};return _eag ;};func _abdf (_accbb *_a .CompliancePdfReader )(_dcbbb []ViolatedRule ){_eagag :=_accbb .GetObjectNums ();for _ ,_efgb :=range _eagag {_eaaa ,_dgbbb :=_accbb .GetIndirectObjectByNumber (_efgb );if _dgbbb !=nil {continue ;
};_bbbbc ,_dfeef :=_ag .GetDict (_eaaa );if !_dfeef {continue ;};_cggcf ,_dfeef :=_ag .GetName (_bbbbc .Get ("\u0054\u0079\u0070\u0065"));if !_dfeef {continue ;};if _cggcf .String ()!="\u0046\u0069\u006c\u0065\u0073\u0070\u0065\u0063"{continue ;};_fefd ,_dgbbb :=_a .NewPdfFilespecFromObj (_bbbbc );
if _dgbbb !=nil {continue ;};if _fefd .EF !=nil {if _fefd .F ==nil ||_fefd .UF ==nil {_dcbbb =append (_dcbbb ,_bb ("\u0036\u002e\u0038-\u0032","\u0054h\u0065\u0020\u0066\u0069\u006c\u0065\u0020\u0073\u0070\u0065\u0063\u0069\u0066i\u0063\u0061\u0074i\u006f\u006e\u0020\u0064\u0069\u0063t\u0069\u006fn\u0061\u0072\u0079\u0020\u0066\u006f\u0072\u0020\u0061\u006e\u0020\u0065\u006d\u0062\u0065\u0064\u0064\u0065\u0064\u0020\u0066\u0069\u006c\u0065\u0020\u0073\u0068\u0061\u006cl\u0020\u0063\u006f\u006e\u0074a\u0069\u006e\u0020t\u0068\u0065\u0020\u0046\u0020a\u006e\u0064\u0020\u0055\u0046\u0020\u006b\u0065\u0079\u0073\u002e"));
break ;};if _fefd .AFRelationship ==nil {_dcbbb =append (_dcbbb ,_bb ("\u0036\u002e\u0038-\u0033","\u0049\u006e\u0020\u006f\u0072d\u0065\u0072\u0020\u0074\u006f\u0020\u0065\u006e\u0061\u0062\u006c\u0065\u0020i\u0064\u0065nt\u0069\u0066\u0069c\u0061\u0074\u0069o\u006e\u0020\u006f\u0066\u0020\u0074\u0068\u0065\u0020\u0072\u0065\u006c\u0061\u0074\u0069\u006f\u006e\u0073h\u0069\u0070\u0020\u0062\u0065\u0074\u0077\u0065\u0065\u006e\u0020\u0074\u0068\u0065\u0020fi\u006ce\u0020\u0073\u0070\u0065\u0063\u0069f\u0069c\u0061\u0074\u0069o\u006e\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u0020\u0061\u006e\u0064\u0020\u0074\u0068\u0065\u0020c\u006f\u006e\u0074e\u006e\u0074\u0020\u0074\u0068\u0061\u0074\u0020\u0069\u0073\u0020\u0072\u0065\u0066\u0065\u0072\u0072\u0069\u006e\u0067\u0020\u0074\u006f\u0020\u0069\u0074\u002c\u0020\u0061\u0020\u006e\u0065\u0077\u0020(\u0072\u0065\u0071\u0075i\u0072\u0065\u0064\u0029\u0020\u006be\u0079\u0020h\u0061\u0073\u0020\u0062e\u0065\u006e\u0020\u0064\u0065\u0066i\u006e\u0065\u0064\u0020a\u006e\u0064\u0020\u0069\u0074s \u0070\u0072e\u0073\u0065n\u0063\u0065\u0020\u0028\u0069\u006e\u0020\u0074\u0068e\u0020\u0064\u0069\u0063\u0074i\u006f\u006e\u0061\u0072\u0079\u0029\u0020\u0069\u0073\u0020\u0072\u0065q\u0075\u0069\u0072e\u0064\u002e"));
break ;};_bdad ,_cfeb :=_a .NewEmbeddedFileFromObject (_fefd .EF );if _cfeb !=nil {continue ;};if _dd .ToLower (_bdad .FileType )!="\u0061p\u0070l\u0069\u0063\u0061\u0074\u0069\u006f\u006e\u002f\u0070\u0064\u0066"{_dcbbb =append (_dcbbb ,_bb ("\u0036\u002e\u0038-\u0034","\u0041\u006c\u006c\u0020\u0065\u006d\u0062\u0065\u0064d\u0065\u0064 \u0066\u0069\u006c\u0065\u0073\u0020\u0073\u0068\u006fu\u006c\u0064\u0020\u0062e\u0020\u0061\u0020\u0050\u0044\u0046\u0020\u0066\u0069\u006c\u0065\u0020\u0074\u0068\u0061\u0074\u0020\u0063\u006f\u006e\u0066\u006f\u0072\u006d\u0020\u0074\u006f\u0020\u0050\u0044F\u002f\u0041\u002d1\u0020\u006f\u0072\u0020\u0050\u0044\u0046\u002f\u0041\u002d\u0032\u002e"));
break ;};};};return _dcbbb ;};func _gd ()standardType {return standardType {_dg :3,_db :"\u0055"}};

// Profile2A is the implementation of the PDF/A-2A standard profile.
// Implements model.StandardImplementer, Profile interfaces.
type Profile2A struct{profile2 };

// VerificationError is the PDF/A verification error structure, that contains all violated rules.
type VerificationError struct{

// ViolatedRules are the rules that were violated during error verification.
ViolatedRules []ViolatedRule ;

// ConformanceLevel defines the standard on verification failed.
ConformanceLevel int ;

// ConformanceVariant is the standard variant used on verification.
ConformanceVariant string ;};type standardType struct{_dg int ;_db string ;};func _ga ()standardType {return standardType {_dg :1,_db :"\u0042"}};

// Profile3A is the implementation of the PDF/A-3A standard profile.
// Implements model.StandardImplementer, Profile interfaces.
type Profile3A struct{profile3 };func _agbe (_ggbc *_a .CompliancePdfReader )ViolatedRule {return _eag };

// XmpOptions are the options used by the optimization of the XMP metadata.
type XmpOptions struct{

// Copyright information.
Copyright string ;

// OriginalDocumentID is the original document identifier.
// By default, if this field is empty the value is extracted from the XMP Metadata or generated UUID.
OriginalDocumentID string ;

// DocumentID is the original document identifier.
// By default, if this field is empty the value is extracted from the XMP Metadata or generated UUID.
DocumentID string ;

// InstanceID is the original document identifier.
// By default, if this field is empty the value is set to generated UUID.
InstanceID string ;

// NewDocumentVersion is a flag that defines if a document was overwritten.
// If the new document was created this should be true. On changing given document file, and overwriting it it should be true.
NewDocumentVersion bool ;

// MarshalIndent defines marshaling indent of the XMP metadata.
MarshalIndent string ;

// MarshalPrefix defines marshaling prefix of the XMP metadata.
MarshalPrefix string ;};func _bgfde (_dadd *_ag .PdfObjectDictionary ,_geaf map[*_ag .PdfObjectStream ][]byte ,_dfc map[*_ag .PdfObjectStream ]*_ab .CMap )ViolatedRule {const (_cfba ="\u0036.\u0033\u002e\u0033\u002d\u0033";_efbc ="\u0041\u006cl \u0043\u004d\u0061\u0070\u0073\u0020\u0075\u0073e\u0064 \u0077i\u0074\u0068\u0069\u006e\u0020\u0061\u0020\u0063\u006f\u006e\u0066\u006f\u0072m\u0069n\u0067\u0020\u0066\u0069\u006c\u0065\u002c\u0020\u0065\u0078\u0063\u0065\u0070\u0074\u0020\u0049\u0064\u0065\u006e\u0074\u0069\u0074\u0079\u002d\u0048\u0020a\u006e\u0064\u0020\u0049\u0064\u0065\u006et\u0069\u0074\u0079-\u0056\u002c\u0020\u0073\u0068a\u006c\u006c \u0062\u0065\u0020\u0065\u006d\u0062\u0065\u0064\u0064\u0065\u0064\u0020\u0069\u006e\u0020\u0074h\u0061\u0074\u0020\u0066\u0069\u006c\u0065\u0020\u0061\u0073\u0020\u0064es\u0063\u0072\u0069\u0062\u0065\u0064\u0020\u0069\u006e\u0020\u0050\u0044F\u0020\u0052\u0065\u0066\u0065\u0072\u0065\u006e\u0063\u0065\u00205\u002e\u0036\u002e\u0034\u002e";
);var _eabe string ;if _ccfdd ,_cgbaf :=_ag .GetName (_dadd .Get ("\u0053u\u0062\u0074\u0079\u0070\u0065"));_cgbaf {_eabe =_ccfdd .String ();};if _eabe !="\u0054\u0079\u0070e\u0030"{return _eag ;};_bdcef :=_dadd .Get ("\u0045\u006e\u0063\u006f\u0064\u0069\u006e\u0067");
if _cbad ,_aaba :=_ag .GetName (_bdcef );_aaba {switch _cbad .String (){case "\u0049\u0064\u0065\u006e\u0074\u0069\u0074\u0079\u002d\u0048","\u0049\u0064\u0065\u006e\u0074\u0069\u0074\u0079\u002d\u0056":return _eag ;default:return _bb (_cfba ,_efbc );};
};_dgce ,_bgaf :=_ag .GetStream (_bdcef );if !_bgaf {return _bb (_cfba ,_efbc );};_ ,_ccabc :=_gfbb (_dgce ,_geaf ,_dfc );if _ccabc !=nil {return _bb (_cfba ,_efbc );};return _eag ;};func _dbbg (_egdf *_a .CompliancePdfReader )(_fcaac ViolatedRule ){_addfa ,_ebfd :=_gfcb (_egdf );
if !_ebfd {return _eag ;};if _addfa .Get ("\u0052\u0065\u0071u\u0069\u0072\u0065\u006d\u0065\u006e\u0074\u0073")!=nil {return _bb ("\u0036\u002e\u0031\u0031\u002d\u0031","Th\u0065\u0020d\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u0020\u0063a\u0074\u0061\u006c\u006f\u0067\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u006e\u006f\u0074\u0020\u0063\u006f\u006e\u0074\u0061\u0069\u006e\u0020\u0074\u0068\u0065\u0020R\u0065q\u0075\u0069\u0072\u0065\u006d\u0065\u006e\u0074s\u0020k\u0065\u0079.");
};return _eag ;};func _dgdbc (_beba *_a .CompliancePdfReader )ViolatedRule {for _ ,_eedg :=range _beba .PageList {_afbda ,_gccc :=_eedg .GetContentStreams ();if _gccc !=nil {continue ;};for _ ,_gdbec :=range _afbda {_daegd :=_fbd .NewContentStreamParser (_gdbec );
_ ,_gccc =_daegd .Parse ();if _gccc !=nil {return _bb ("\u0036.\u0032\u002e\u0032\u002d\u0031","\u0041\u0020\u0063onten\u0074\u0020\u0073\u0074\u0072\u0065\u0061\u006d\u0020\u0073\u0068\u0061\u006c\u006c n\u006f\u0074\u0020c\u006f\u006e\u0074\u0061\u0069\u006e\u0020\u0061\u006e\u0079 \u006f\u0070\u0065\u0072\u0061\u0074\u006f\u0072\u0073\u0020\u006e\u006ft\u0020\u0064\u0065\u0066\u0069\u006e\u0065\u0064\u0020\u0069\u006e\u0020\u0050\u0044\u0046\u0020\u0052\u0065f\u0065\u0072\u0065\u006e\u0063\u0065\u0020\u0065\u0076\u0065\u006e\u0020\u0069\u0066\u0020s\u0075\u0063\u0068\u0020\u006f\u0070\u0065r\u0061\u0074\u006f\u0072\u0073\u0020\u0061\u0072\u0065\u0020\u0062\u0072\u0061\u0063\u006b\u0065\u0074\u0065\u0064\u0020\u0062\u0079\u0020\u0074\u0068\u0065\u0020\u0042\u0058\u002f\u0045\u0058\u0020\u0063\u006f\u006d\u0070\u0061\u0074\u0069\u0062i\u006c\u0069\u0074\u0079\u0020\u006f\u0070\u0065\u0072\u0061\u0074\u006f\u0072\u0073\u002e");
};};};return _eag ;};func _dbgcd (_fgcbc *_ag .PdfObjectDictionary )ViolatedRule {const (_fbgd ="\u0036\u002e\u0032\u002e\u0031\u0031\u002e\u0033\u002d\u0032";_ceaf ="IS\u004f\u0020\u0033\u0032\u0030\u0030\u0030\u002d\u0031\u003a\u0032\u0030\u0030\u0038\u002c\u00209\u002e\u0037\u002e\u0034\u002c\u0020\u0054\u0061\u0062\u006c\u0065\u0020\u0031\u0031\u0037\u0020\u0072\u0065\u0071\u0075\u0069\u0072\u0065\u0073\u0020\u0074\u0068a\u0074\u0020\u0061\u006c\u006c\u0020\u0065m\u0062\u0065\u0064\u0064\u0065\u0064\u0020\u0054\u0079\u0070\u0065\u0020\u0032\u0020\u0043\u0049\u0044\u0046\u006fn\u0074\u0073\u0020\u0069n\u0020t\u0068e\u0020\u0043\u0049D\u0046\u006f\u006e\u0074\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061r\u0079\u0020\u0073\u0068a\u006c\u006c\u0020\u0063\u006f\u006e\u0074\u0061\u0069\u006e\u0020\u0061\u0020\u0043\u0049\u0044\u0054\u006fG\u0049\u0044M\u0061\u0070\u0020\u0065\u006e\u0074\u0072\u0079 \u0074\u0068\u0061\u0074\u0020\u0073\u0068\u0061\u006c\u006c \u0062e\u0020\u0061\u0020\u0073t\u0072\u0065\u0061\u006d\u0020\u006d\u0061\u0070p\u0069\u006e\u0067 f\u0072\u006f\u006d \u0043\u0049\u0044\u0073\u0020\u0074\u006f\u0020\u0067\u006c\u0079p\u0068 \u0069\u006e\u0064\u0069c\u0065\u0073\u0020\u006fr\u0020\u0074\u0068\u0065\u0020\u006e\u0061\u006d\u0065\u0020\u0049d\u0065\u006e\u0074\u0069\u0074\u0079\u002c\u0020\u0061\u0073\u0020\u0064\u0065\u0073\u0063\u0072\u0069\u0062\u0065\u0064\u0020\u0069\u006e\u0020\u0049\u0053\u004f\u0020\u0033\u0032\u0030\u0030\u0030\u002d\u0031\u003a\u0032\u0030\u0030\u0038\u002c\u0020\u0039\u002e\u0037\u002e\u0034\u002c\u0020\u0054\u0061\u0062\u006c\u0065\u0020\u0031\u0031\u0037\u002e";
);var _bacgb string ;if _dgea ,_eedeg :=_ag .GetName (_fgcbc .Get ("\u0053u\u0062\u0074\u0079\u0070\u0065"));_eedeg {_bacgb =_dgea .String ();};if _bacgb !="\u0043\u0049\u0044F\u006f\u006e\u0074\u0054\u0079\u0070\u0065\u0032"{return _eag ;};if _fgcbc .Get ("C\u0049\u0044\u0054\u006f\u0047\u0049\u0044\u004d\u0061\u0070")==nil {return _bb (_fbgd ,_ceaf );
};return _eag ;};func _daag (_gedd *_a .CompliancePdfReader )(_cbefa []ViolatedRule ){_caafa :=true ;_edaba ,_aggeg :=_gedd .GetCatalogMarkInfo ();if !_aggeg {_caafa =false ;}else {_acfb ,_eedee :=_ag .GetDict (_edaba );if _eedee {_dcbcf ,_gecbe :=_ag .GetBool (_acfb .Get ("\u004d\u0061\u0072\u006b\u0065\u0064"));
if !bool (*_dcbcf )||!_gecbe {_caafa =false ;};}else {_caafa =false ;};};if !_caafa {_cbefa =append (_cbefa ,_bb ("\u0036.\u0037\u002e\u0032\u002e\u0032\u002d1","\u0054\u0068\u0065\u0020\u0064\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u0020\u0063\u0061\u0074\u0061\u006cog\u0020d\u0069\u0063\u0074\u0069\u006f\u006e\u0061r\u0079 \u0073\u0068\u0061\u006c\u006c\u0020\u0069\u006e\u0063\u006c\u0075\u0064\u0065\u0020\u0061\u0020M\u0061r\u006b\u0049\u006e\u0066\u006f\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006ea\u0072\u0079\u0020\u0077\u0069\u0074\u0068\u0020\u0061 \u004d\u0061\u0072\u006b\u0065\u0064\u0020\u0065\u006et\u0072\u0079\u0020\u0069\u006e\u0020\u0069\u0074,\u0020\u0077\u0068\u006f\u0073\u0065\u0020\u0076\u0061lu\u0065\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0062\u0065\u0020\u0074\u0072\u0075\u0065"));
};_bfegb ,_aggeg :=_gedd .GetCatalogStructTreeRoot ();if !_aggeg {_cbefa =append (_cbefa ,_bb ("\u0036.\u0037\u002e\u0033\u002e\u0033\u002d1","\u0054\u0068\u0065\u0020\u006c\u006f\u0067\u0069\u0063\u0061\u006c\u0020\u0073\u0074\u0072\u0075\u0063\u0074\u0075r\u0065\u0020\u006f\u0066\u0020\u0074\u0068e\u0020\u0063\u006f\u006e\u0066\u006f\u0072\u006d\u0069\u006e\u0067 \u0066\u0069\u006c\u0065\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0062\u0065\u0020\u0064\u0065\u0073\u0063\u0072\u0069\u0062\u0065d \u0062\u0079\u0020a\u0020s\u0074\u0072\u0075\u0063\u0074\u0075\u0072e\u0020\u0068\u0069\u0065\u0072\u0061\u0072\u0063\u0068\u0079\u0020\u0072\u006f\u006ft\u0065\u0064\u0020i\u006e\u0020\u0074\u0068\u0065\u0020\u0053\u0074\u0072\u0075\u0063\u0074\u0054\u0072\u0065\u0065\u0052\u006f\u006f\u0074\u0020\u0065\u006e\u0074r\u0079\u0020\u006f\u0066\u0020\u0074h\u0065\u0020d\u006fc\u0075\u006d\u0065\u006e\u0074\u0020\u0063\u0061t\u0061\u006c\u006fg \u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u002c\u0020\u0061\u0073\u0020\u0064\u0065\u0073\u0063\u0072\u0069\u0062\u0065\u0064\u0020\u0069n\u0020\u0050\u0044\u0046\u0020\u0052\u0065\u0066\u0065\u0072\u0065\u006e\u0063\u0065 \u0039\u002e\u0036\u002e"));
};_edda ,_aggeg :=_ag .GetDict (_bfegb );if _aggeg {_cefee ,_dffe :=_ag .GetName (_edda .Get ("\u0052o\u006c\u0065\u004d\u0061\u0070"));if _dffe {_cgfdb ,_fgda :=_ag .GetDict (_cefee );if _fgda {for _ ,_eeeg :=range _cgfdb .Keys (){_fgfef :=_cgfdb .Get (_eeeg );
if _fgfef ==nil {_cbefa =append (_cbefa ,_bb ("\u0036.\u0037\u002e\u0033\u002e\u0034\u002d1","\u0041\u006c\u006c\u0020\u006eo\u006e\u002ds\u0074\u0061\u006e\u0064\u0061\u0072\u0064\u0020\u0073t\u0072\u0075\u0063\u0074ure\u0020\u0074\u0079\u0070\u0065s\u0020\u0073\u0068\u0061\u006c\u006c \u0062\u0065\u0020\u006d\u0061\u0070\u0070\u0065d\u0020\u0074\u006f\u0020\u0074\u0068\u0065\u0020n\u0065\u0061\u0072\u0065\u0073\u0074\u0020\u0066\u0075\u006e\u0063t\u0069\u006f\u006e\u0061\u006c\u006c\u0079\u0020\u0065\u0071\u0075\u0069\u0076\u0061\u006c\u0065\u006e\u0074\u0020\u0073\u0074a\u006ed\u0061r\u0064\u0020\u0074\u0079\u0070\u0065\u002c\u0020\u0061\u0073\u0020\u0064\u0065\u0066\u0069\u006ee\u0064\u0020\u0069\u006e\u0020\u0050\u0044\u0046\u0020\u0052\u0065\u0066\u0065re\u006e\u0063e\u0020\u0039\u002e\u0037\u002e\u0034\u002c\u0020i\u006e\u0020\u0074\u0068e\u0020\u0072\u006fl\u0065\u0020\u006d\u0061p \u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u0020\u006f\u0066 \u0074h\u0065\u0020\u0073\u0074\u0072\u0075c\u0074\u0075r\u0065\u0020\u0074\u0072e\u0065\u0020\u0072\u006f\u006ft\u002e"));
};};};};};return _cbefa ;};type profile1 struct{_acab standardType ;_fcgb Profile1Options ;};func _eagg (_ccf []*_gc .Image ,_ge bool )error {_ed :=_ag .PdfObjectName ("\u0044e\u0076\u0069\u0063\u0065\u0052\u0047B");if _ge {_ed ="\u0044\u0065\u0076\u0069\u0063\u0065\u0043\u004d\u0059\u004b";
};for _ ,_ffg :=range _ccf {if _ffg .Colorspace ==_ed {continue ;};_fdcc ,_gdfd :=_a .NewXObjectImageFromStream (_ffg .Stream );if _gdfd !=nil {return _gdfd ;};_cbg ,_gdfd :=_fdcc .ToImage ();if _gdfd !=nil {return _gdfd ;};_fce ,_gdfd :=_cbg .ToGoImage ();
if _gdfd !=nil {return _gdfd ;};var _cde _a .PdfColorspace ;if _ge {_cde =_a .NewPdfColorspaceDeviceCMYK ();_fce ,_gdfd =_fe .CMYKConverter .Convert (_fce );}else {_cde =_a .NewPdfColorspaceDeviceRGB ();_fce ,_gdfd =_fe .NRGBAConverter .Convert (_fce );
};if _gdfd !=nil {return _gdfd ;};_fffd ,_fgc :=_fce .(_fe .Image );if !_fgc {return _ce .New ("\u0069\u006d\u0061\u0067\u0065\u0020\u0064\u006f\u0065\u0073\u006e\u0027\u0074 \u0069\u006d\u0070\u006c\u0065\u006de\u006e\u0074\u0020\u0069\u006d\u0061\u0067\u0065\u0075\u0074\u0069\u006c\u002eI\u006d\u0061\u0067\u0065");
};_fee :=_fffd .Base ();_egd :=&_a .Image {Width :int64 (_fee .Width ),Height :int64 (_fee .Height ),BitsPerComponent :int64 (_fee .BitsPerComponent ),ColorComponents :_fee .ColorComponents ,Data :_fee .Data };_egd .SetDecode (_fee .Decode );_egd .SetAlpha (_fee .Alpha );
if _gdfd =_fdcc .SetImage (_egd ,_cde );_gdfd !=nil {return _gdfd ;};_fdcc .ToPdfObject ();_ffg .ColorComponents =_fee .ColorComponents ;_ffg .Colorspace =_ed ;};return nil ;};func _bade (_bdfa *_a .CompliancePdfReader )(_bffc []ViolatedRule ){_cdae ,_bgba :=_gfcb (_bdfa );
if !_bgba {return _bffc ;};_bedd :=_bb ("\u0036.\u0032\u002e\u0032\u002d\u0031","\u0041\u0020\u0050\u0044\u0046\u002f\u0041\u002d\u0031\u0020\u004f\u0075\u0074p\u0075\u0074\u0049\u006e\u0074e\u006e\u0074\u0020\u0069\u0073\u0020a\u006e \u004f\u0075\u0074\u0070\u0075\u0074\u0049n\u0074\u0065\u006e\u0074\u0020\u0064i\u0063\u0074\u0069\u006fn\u0061\u0072\u0079\u002c\u0020\u0061\u0073\u0020\u0064\u0065\u0066\u0069\u006e\u0065\u0064\u0020\u0062y\u0020\u0050\u0044F\u0020\u0052\u0065\u0066\u0065\u0072\u0065\u006e\u0063\u0065 \u0039\u002e\u0031\u0030.4\u002c\u0020\u0074\u0068\u0061\u0074\u0020\u0069\u0073 \u0069\u006e\u0063\u006c\u0075\u0064e\u0064\u0020i\u006e\u0020\u0074\u0068\u0065\u0020\u0066\u0069\u006c\u0065\u0027\u0073\u0020O\u0075\u0074p\u0075\u0074I\u006e\u0074\u0065\u006e\u0074\u0073\u0020\u0061\u0072\u0072\u0061\u0079\u0020a\u006e\u0064\u0020h\u0061\u0073\u0020\u0047\u0054\u0053\u005f\u0050\u0044\u0046\u0041\u0031\u0020\u0061\u0073 \u0074\u0068\u0065\u0020\u0076a\u006c\u0075e\u0020\u006f\u0066\u0020i\u0074\u0073 \u0053\u0020\u006b\u0065\u0079\u0020\u0061\u006e\u0064\u0020\u0061\u0020\u0076\u0061\u006c\u0069\u0064\u0020I\u0043\u0043\u0020\u0070\u0072\u006f\u0066\u0069\u006ce\u0020s\u0074\u0072\u0065\u0061\u006d \u0061\u0073\u0020\u0074h\u0065\u0020\u0076a\u006c\u0075\u0065\u0020\u0069\u0074\u0073\u0020\u0044\u0065\u0073t\u004f\u0075t\u0070\u0075\u0074P\u0072\u006f\u0066\u0069\u006c\u0065 \u006b\u0065\u0079\u002e");
_abbf ,_bgba :=_ag .GetArray (_cdae .Get ("\u004f\u0075\u0074\u0070\u0075\u0074\u0049\u006e\u0074\u0065\u006e\u0074\u0073"));if !_bgba {_bffc =append (_bffc ,_bedd );return _bffc ;};_edade :=_bb ("\u0036.\u0032\u002e\u0032\u002d\u0032","\u0049\u0066\u0020\u0061\u0020\u0066\u0069\u006c\u0065's\u0020O\u0075\u0074\u0070u\u0074\u0049\u006e\u0074\u0065\u006e\u0074\u0073 \u0061\u0072\u0072a\u0079\u0020\u0063\u006f\u006e\u0074\u0061\u0069n\u0073\u0020\u006d\u006f\u0072\u0065\u0020\u0074\u0068a\u006e\u0020\u006f\u006ee\u0020\u0065\u006e\u0074\u0072\u0079\u002c\u0020\u0074\u0068\u0065\u006e\u0020\u0061\u006c\u006c\u0020\u0065n\u0074\u0072\u0069\u0065\u0073\u0020\u0074\u0068\u0061\u0074\u0020\u0063\u006f\u006e\u0074\u0061\u0069\u006e a \u0044\u0065\u0073\u0074\u004f\u0075\u0074\u0070\u0075\u0074\u0050\u0072\u006f\u0066\u0069\u006c\u0065\u0020\u006b\u0065y\u0020\u0073\u0068\u0061\u006cl\u0020\u0068\u0061\u0076\u0065 \u0061\u0073\u0020\u0074\u0068\u0065 \u0076\u0061\u006c\u0075\u0065\u0020\u006f\u0066\u0020\u0074\u0068a\u0074\u0020\u006b\u0065\u0079 \u0074\u0068\u0065\u0020\u0073\u0061\u006d\u0065\u0020\u0069\u006e\u0064\u0069\u0072\u0065c\u0074\u0020\u006fb\u006ae\u0063t\u002c\u0020\u0077h\u0069\u0063\u0068\u0020\u0073\u0068\u0061\u006c\u006c \u0062\u0065\u0020\u0061\u0020\u0076\u0061\u006c\u0069d\u0020\u0049\u0043\u0043\u0020\u0070\u0072\u006f\u0066\u0069\u006c\u0065\u0020\u0073\u0074r\u0065\u0061m\u002e");
if _abbf .Len ()> 1{_bcgf :=map[*_ag .PdfObjectDictionary ]struct{}{};for _ccfbd :=0;_ccfbd < _abbf .Len ();_ccfbd ++{_eagf ,_fdbba :=_ag .GetDict (_abbf .Get (_ccfbd ));if !_fdbba {_bffc =append (_bffc ,_bedd );return _bffc ;};if _ccfbd ==0{_bcgf [_eagf ]=struct{}{};
continue ;};if _ ,_fdac :=_bcgf [_eagf ];!_fdac {_bffc =append (_bffc ,_edade );break ;};};}else if _abbf .Len ()==0{_bffc =append (_bffc ,_bedd );return _bffc ;};_fcae ,_bgba :=_ag .GetDict (_abbf .Get (0));if !_bgba {_bffc =append (_bffc ,_bedd );return _bffc ;
};if _ddfbd ,_bbccd :=_ag .GetName (_fcae .Get ("\u0053"));!_bbccd ||(*_ddfbd )!="\u0047T\u0053\u005f\u0050\u0044\u0046\u00411"{_bffc =append (_bffc ,_bedd );return _bffc ;};_bdgfd ,_gcac :=_a .NewPdfOutputIntentFromPdfObject (_fcae );if _gcac !=nil {_g .Log .Debug ("\u0070\u0061\u0072\u0073\u0069\u006e\u0067\u0020\u006f\u0075\u0074\u0070\u0075\u0074\u0020i\u006et\u0065\u006e\u0074\u0020\u0066\u0061\u0069\u006c\u0065\u0064\u003a\u0020\u0025\u0076",_gcac );
return _bffc ;};_bfef ,_gcac :=_gcf .ParseHeader (_bdgfd .DestOutputProfile );if _gcac !=nil {_g .Log .Debug ("\u0070\u0061\u0072\u0073\u0069\u006e\u0067\u0020\u0063\u006f\u006c\u006f\u0072\u0070\u0072\u006f\u0066\u0069\u006c\u0065\u0020\u0068\u0065\u0061d\u0065\u0072\u0020\u0066\u0061i\u006c\u0065d\u003a\u0020\u0025\u0076",_gcac );
return _bffc ;};if (_bfef .DeviceClass ==_gcf .DeviceClassPRTR ||_bfef .DeviceClass ==_gcf .DeviceClassMNTR )&&(_bfef .ColorSpace ==_gcf .ColorSpaceRGB ||_bfef .ColorSpace ==_gcf .ColorSpaceCMYK ||_bfef .ColorSpace ==_gcf .ColorSpaceGRAY ){return _bffc ;
};_bffc =append (_bffc ,_bedd );return _bffc ;};func _ffee (_cabd *_a .PdfFont ,_afeb *_ag .PdfObjectDictionary )ViolatedRule {const (_dfdbe ="\u0036\u002e\u0032\u002e\u0031\u0031\u002e\u0036\u002d\u0033";_cgffec ="\u0041l\u006c\u0020\u0073\u0079\u006d\u0062\u006f\u006c\u0069\u0063\u0020\u0054\u0072u\u0065\u0054\u0079p\u0065\u0020\u0066\u006f\u006e\u0074s\u0020\u0073h\u0061\u006c\u006c\u0020\u006e\u006f\u0074\u0020\u0073\u0070\u0065\u0063\u0069\u0066\u0079\u0020\u0061\u006e\u0020\u0045\u006e\u0063\u006f\u0064\u0069\u006e\u0067\u0020\u0065n\u0074\u0072\u0079\u0020\u0069n\u0020\u0074\u0068e\u0020\u0066\u006f\u006e\u0074 \u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u002e";
);var _fbbfe string ;if _aefgd ,_gcgbb :=_ag .GetName (_afeb .Get ("\u0053u\u0062\u0074\u0079\u0070\u0065"));_gcgbb {_fbbfe =_aefgd .String ();};if _fbbfe !="\u0054\u0072\u0075\u0065\u0054\u0079\u0070\u0065"{return _eag ;};_ccadb :=_cabd .FontDescriptor ();
_ddbgb ,_bebg :=_ag .GetIntVal (_ccadb .Flags );if !_bebg {_g .Log .Debug ("\u0066\u006c\u0061\u0067\u0073 \u006e\u006f\u0074\u0020\u0064\u0065\u0066\u0069\u006e\u0065\u0064\u0020\u0066o\u0072\u0020\u0074\u0068\u0065\u0020\u0066\u006f\u006e\u0074\u0020\u0064\u0065\u0073\u0063\u0072\u0069\u0070\u0074\u006f\u0072");
return _bb (_dfdbe ,_cgffec );};_eeff :=(uint32 (_ddbgb )>>3)&1;_feeg :=_eeff !=0;if !_feeg {return _eag ;};if _afeb .Get ("\u0045\u006e\u0063\u006f\u0064\u0069\u006e\u0067")!=nil {return _bb (_dfdbe ,_cgffec );};return _eag ;};

// ViolatedRule is the structure that defines violated PDF/A rule.
type ViolatedRule struct{RuleNo string ;Detail string ;};

// String gets a string representation of the violated rule.
func (_bf ViolatedRule )String ()string {return _fg .Sprintf ("\u0025\u0073\u003a\u0020\u0025\u0073",_bf .RuleNo ,_bf .Detail );};func _adeagg (_bbcd string ,_ecca string ,_bfgdc string )(string ,bool ){_eacg :=_dd .Index (_bbcd ,_ecca );if _eacg ==-1{return "",false ;
};_eacg +=len (_ecca );_bbaa :=_dd .Index (_bbcd [_eacg :],_bfgdc );if _bbaa ==-1{return "",false ;};_bbaa =_eacg +_bbaa ;return _bbcd [_eacg :_bbaa ],true ;};func _cdbe (_feae *_a .CompliancePdfReader )(_deec []ViolatedRule ){_ebde :=true ;_ebad ,_abbca :=_feae .GetCatalogMarkInfo ();
if !_abbca {_ebde =false ;}else {_abega ,_aaagc :=_ag .GetDict (_ebad );if _aaagc {_egcg ,_cccfa :=_ag .GetBool (_abega .Get ("\u004d\u0061\u0072\u006b\u0065\u0064"));if !bool (*_egcg )||!_cccfa {_ebde =false ;};}else {_ebde =false ;};};if !_ebde {_deec =append (_deec ,_bb ("\u0036.\u0038\u002e\u0032\u002e\u0032\u002d1","\u0054\u0068\u0065\u0020\u0064\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u0020\u0063\u0061\u0074\u0061\u006cog\u0020d\u0069\u0063\u0074\u0069\u006f\u006e\u0061r\u0079 \u0073\u0068\u0061\u006c\u006c\u0020\u0069\u006e\u0063\u006c\u0075\u0064\u0065\u0020\u0061\u0020M\u0061r\u006b\u0049\u006e\u0066\u006f\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006ea\u0072\u0079\u0020\u0077\u0069\u0074\u0068\u0020\u0061 \u004d\u0061\u0072\u006b\u0065\u0064\u0020\u0065\u006et\u0072\u0079\u0020\u0069\u006e\u0020\u0069\u0074,\u0020\u0077\u0068\u006f\u0073\u0065\u0020\u0076\u0061lu\u0065\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0062\u0065\u0020\u0074\u0072\u0075\u0065"));
};_bacb ,_abbca :=_feae .GetCatalogStructTreeRoot ();if !_abbca {_deec =append (_deec ,_bb ("\u0036.\u0038\u002e\u0033\u002e\u0033\u002d1","\u0054\u0068\u0065\u0020\u006c\u006f\u0067\u0069\u0063\u0061\u006c\u0020\u0073\u0074\u0072\u0075\u0063\u0074\u0075r\u0065\u0020\u006f\u0066\u0020\u0074\u0068e\u0020\u0063\u006f\u006e\u0066\u006f\u0072\u006d\u0069\u006e\u0067 \u0066\u0069\u006c\u0065\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0062\u0065\u0020\u0064\u0065\u0073\u0063\u0072\u0069\u0062\u0065d \u0062\u0079\u0020a\u0020s\u0074\u0072\u0075\u0063\u0074\u0075\u0072e\u0020\u0068\u0069\u0065\u0072\u0061\u0072\u0063\u0068\u0079\u0020\u0072\u006f\u006ft\u0065\u0064\u0020i\u006e\u0020\u0074\u0068\u0065\u0020\u0053\u0074\u0072\u0075\u0063\u0074\u0054\u0072\u0065\u0065\u0052\u006f\u006f\u0074\u0020\u0065\u006e\u0074r\u0079\u0020\u006f\u0066\u0020\u0074h\u0065\u0020d\u006fc\u0075\u006d\u0065\u006e\u0074\u0020\u0063\u0061t\u0061\u006c\u006fg \u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u002c\u0020\u0061\u0073\u0020\u0064\u0065\u0073\u0063\u0072\u0069\u0062\u0065\u0064\u0020\u0069n\u0020\u0050\u0044\u0046\u0020\u0052\u0065\u0066\u0065\u0072\u0065\u006e\u0063\u0065 \u0039\u002e\u0036\u002e"));
};_gcgf ,_abbca :=_ag .GetDict (_bacb );if _abbca {_cbac ,_acebc :=_ag .GetName (_gcgf .Get ("\u0052o\u006c\u0065\u004d\u0061\u0070"));if _acebc {_gdgbc ,_cbef :=_ag .GetDict (_cbac );if _cbef {for _ ,_bbgd :=range _gdgbc .Keys (){_fcee :=_gdgbc .Get (_bbgd );
if _fcee ==nil {_deec =append (_deec ,_bb ("\u0036.\u0038\u002e\u0033\u002e\u0034\u002d1","\u0041\u006c\u006c\u0020\u006eo\u006e\u002ds\u0074\u0061\u006e\u0064\u0061\u0072\u0064\u0020\u0073t\u0072\u0075\u0063\u0074ure\u0020\u0074\u0079\u0070\u0065s\u0020\u0073\u0068\u0061\u006c\u006c \u0062\u0065\u0020\u006d\u0061\u0070\u0070\u0065d\u0020\u0074\u006f\u0020\u0074\u0068\u0065\u0020n\u0065\u0061\u0072\u0065\u0073\u0074\u0020\u0066\u0075\u006e\u0063t\u0069\u006f\u006e\u0061\u006c\u006c\u0079\u0020\u0065\u0071\u0075\u0069\u0076\u0061\u006c\u0065\u006e\u0074\u0020\u0073\u0074a\u006ed\u0061r\u0064\u0020\u0074\u0079\u0070\u0065\u002c\u0020\u0061\u0073\u0020\u0064\u0065\u0066\u0069\u006ee\u0064\u0020\u0069\u006e\u0020\u0050\u0044\u0046\u0020\u0052\u0065\u0066\u0065re\u006e\u0063e\u0020\u0039\u002e\u0037\u002e\u0034\u002c\u0020i\u006e\u0020\u0074\u0068e\u0020\u0072\u006fl\u0065\u0020\u006d\u0061p \u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u0020\u006f\u0066 \u0074h\u0065\u0020\u0073\u0074\u0072\u0075c\u0074\u0075r\u0065\u0020\u0074\u0072e\u0065\u0020\u0072\u006f\u006ft\u002e"));
};};};};};return _deec ;};func _ca (_gca []_ag .PdfObject )(*documentImages ,error ){_be :=_ag .PdfObjectName ("\u0053u\u0062\u0074\u0079\u0070\u0065");_dcg :=make (map[*_ag .PdfObjectStream ]struct{});_ddf :=make (map[_ag .PdfObject ]struct{});var (_ec ,_aa ,_agd bool ;
_fgg []*imageInfo ;_gac error ;);for _ ,_dbg :=range _gca {_bef ,_gdf :=_ag .GetStream (_dbg );if !_gdf {continue ;};if _ ,_cb :=_dcg [_bef ];_cb {continue ;};_dcg [_bef ]=struct{}{};_bde :=_bef .PdfObjectDictionary .Get (_be );_cdf ,_gdf :=_ag .GetName (_bde );
if !_gdf ||string (*_cdf )!="\u0049\u006d\u0061g\u0065"{continue ;};if _dbc :=_bef .PdfObjectDictionary .Get ("\u0053\u004d\u0061s\u006b");_dbc !=nil {_ddf [_dbc ]=struct{}{};};_gcd :=imageInfo {BitsPerComponent :8,Stream :_bef };_gcd .ColorSpace ,_gac =_a .DetermineColorspaceNameFromPdfObject (_bef .PdfObjectDictionary .Get ("\u0043\u006f\u006c\u006f\u0072\u0053\u0070\u0061\u0063\u0065"));
if _gac !=nil {return nil ,_gac ;};if _cgg ,_ee :=_ag .GetIntVal (_bef .PdfObjectDictionary .Get ("\u0042\u0069t\u0073\u0050\u0065r\u0043\u006f\u006d\u0070\u006f\u006e\u0065\u006e\u0074"));_ee {_gcd .BitsPerComponent =_cgg ;};if _egb ,_gdc :=_ag .GetIntVal (_bef .PdfObjectDictionary .Get ("\u0057\u0069\u0064t\u0068"));
_gdc {_gcd .Width =_egb ;};if _gcab ,_bbc :=_ag .GetIntVal (_bef .PdfObjectDictionary .Get ("\u0048\u0065\u0069\u0067\u0068\u0074"));_bbc {_gcd .Height =_gcab ;};switch _gcd .ColorSpace {case "\u0044\u0065\u0076\u0069\u0063\u0065\u0047\u0072\u0061\u0079":_agd =true ;
_gcd .ColorComponents =1;case "\u0044e\u0076\u0069\u0063\u0065\u0052\u0047B":_ec =true ;_gcd .ColorComponents =3;case "\u0044\u0065\u0076\u0069\u0063\u0065\u0043\u004d\u0059\u004b":_aa =true ;_gcd .ColorComponents =4;default:_gcd ._ddaf =true ;};_fgg =append (_fgg ,&_gcd );
};if len (_ddf )> 0{if len (_ddf )==len (_fgg ){_fgg =nil ;}else {_acc :=make ([]*imageInfo ,len (_fgg )-len (_ddf ));var _ff int ;for _ ,_ceg :=range _fgg {if _ ,_bfc :=_ddf [_ceg .Stream ];_bfc {continue ;};_acc [_ff ]=_ceg ;_ff ++;};_fgg =_acc ;};};
return &documentImages {_fc :_ec ,_fgb :_aa ,_ac :_agd ,_bbe :_ddf ,_cdd :_fgg },nil ;};func _ccdcg (_bdcde *_a .CompliancePdfReader )(_agcbd []ViolatedRule ){for _ ,_cfab :=range _bdcde .GetObjectNums (){_acbbe ,_gecb :=_bdcde .GetIndirectObjectByNumber (_cfab );
if _gecb !=nil {continue ;};_bffga ,_fffgf :=_ag .GetDict (_acbbe );if !_fffgf {continue ;};_cbbc ,_fffgf :=_ag .GetName (_bffga .Get ("\u0054\u0079\u0070\u0065"));if !_fffgf {continue ;};if _cbbc .String ()!="\u0041\u0063\u0072\u006f\u0046\u006f\u0072\u006d"{continue ;
};_aebg ,_fffgf :=_ag .GetBool (_bffga .Get ("\u004ee\u0065d\u0041\u0070\u0070\u0065\u0061\u0072\u0061\u006e\u0063\u0065\u0073"));if !_fffgf {return _agcbd ;};if bool (*_aebg ){_agcbd =append (_agcbd ,_bb ("\u0036\u002e\u0039-\u0031","\u0054\u0068\u0065\u0020\u004e\u0065e\u0064\u0041\u0070\u0070\u0065a\u0072\u0061\u006e\u0063\u0065\u0073\u0020\u0066\u006c\u0061\u0067\u0020\u006f\u0066\u0020\u0074\u0068\u0065\u0020\u0069\u006e\u0074\u0065\u0072\u0061\u0063\u0074\u0069\u0076e\u0020\u0066\u006f\u0072\u006d \u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0065\u0069\u0074\u0068\u0065\u0072\u0020\u006e\u006f\u0074\u0020b\u0065\u0020\u0070\u0072\u0065se\u006e\u0074\u0020\u006f\u0072\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0062\u0065\u0020\u0066\u0061\u006c\u0073\u0065\u002e"));
};};return _agcbd ;};func _bbdb (_gbe *_gc .Document ,_gde bool )error {_bab ,_baf :=_gbe .GetPages ();if !_baf {return nil ;};for _ ,_ccbc :=range _bab {_bafc :=_ccbc .FindXObjectForms ();for _ ,_gfae :=range _bafc {_gedc ,_febg :=_a .NewXObjectFormFromStream (_gfae );
if _febg !=nil {return _febg ;};_cegd ,_febg :=_gedc .GetContentStream ();if _febg !=nil {return _febg ;};_dcc :=_fbd .NewContentStreamParser (string (_cegd ));_ggc ,_febg :=_dcc .Parse ();if _febg !=nil {return _febg ;};_caag ,_febg :=_gbbe (_gedc .Resources ,_ggc ,_gde );
if _febg !=nil {return _febg ;};if len (_caag )==0{continue ;};if _febg =_gedc .SetContentStream (_caag ,_ag .NewFlateEncoder ());_febg !=nil {return _febg ;};_gedc .ToPdfObject ();};};return nil ;};type profile3 struct{_gfdf standardType ;_gae Profile3Options ;
};

// Profile2B is the implementation of the PDF/A-2B standard profile.
// Implements model.StandardImplementer, Profile interfaces.
type Profile2B struct{profile2 };var _ Profile =(*Profile2B )(nil );func _cgcf (_afca *_gc .Document )error {_bddgc ,_fdga :=_afca .FindCatalog ();if !_fdga {return _ce .New ("\u0063\u0061\u0074\u0061\u006c\u006f\u0067\u0020\u006e\u006f\u0074\u0020f\u006f\u0075\u006e\u0064");
};if _bddgc .Object .Get ("\u0052\u0065\u0071u\u0069\u0072\u0065\u006d\u0065\u006e\u0074\u0073")!=nil {_bddgc .Object .Remove ("\u0052\u0065\u0071u\u0069\u0072\u0065\u006d\u0065\u006e\u0074\u0073");};return nil ;};

// StandardName gets the name of the standard.
func (_dagg *profile3 )StandardName ()string {return _fg .Sprintf ("\u0050D\u0046\u002f\u0041\u002d\u0033\u0025s",_dagg ._gfdf ._db );};

// Profile3U is the implementation of the PDF/A-3U standard profile.
// Implements model.StandardImplementer, Profile interfaces.
type Profile3U struct{profile3 };func _abge (_agfd *_a .CompliancePdfReader ,_fegb standardType ,_egfd bool )(_ceaca []ViolatedRule ){_cfcg ,_acebga :=_gfcb (_agfd );if !_acebga {return []ViolatedRule {_bb ("\u0036.\u0037\u002e\u0032\u002d\u0031","\u0063a\u0074a\u006c\u006f\u0067\u0020\u006eo\u0074\u0020f\u006f\u0075\u006e\u0064\u002e")};
};_eabb :=_cfcg .Get ("\u004d\u0065\u0074\u0061\u0064\u0061\u0074\u0061");if _eabb ==nil {return []ViolatedRule {_bb ("\u0036.\u0037\u002e\u0032\u002d\u0031","\u006e\u006f\u0020\u0027\u004d\u0065\u0074\u0061d\u0061\u0074\u0061' \u006b\u0065\u0079\u0020\u0066\u006fu\u006e\u0064\u0020\u0069\u006e\u0020\u0074\u0068\u0065\u0020\u0064\u006f\u0063\u0075\u006de\u006e\u0074\u0020\u0063\u0061\u0074\u0061\u006co\u0067\u002e"),_bb ("\u0036.\u0037\u002e\u0033\u002d\u0031","\u0049\u0066\u0020\u005b\u0061\u0020\u0064\u006fc\u0075\u006d\u0065\u006e\u0074\u0020\u0069\u006e\u0066o\u0072\u006d\u0061t\u0069\u006f\u006e\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u0020\u0061\u0070p\u0065\u0061r\u0073\u0020\u0069n\u0020\u0061 \u0064\u006f\u0063um\u0065\u006e\u0074\u005d\u002c\u0020\u0074\u0068\u0065n\u0020\u0061\u006c\u006c\u0020\u006f\u0066\u0020\u0069\u0074\u0073\u0020\u0065\u006e\u0074\u0072\u0069\u0065\u0073\u0020\u0074\u0068\u0061\u0074\u0020\u0068\u0061\u0076\u0065\u0020\u0061\u006e\u0061\u006c\u006f\u0067\u006fu\u0073\u0020\u0070\u0072\u006f\u0070\u0065\u0072\u0074\u0069\u0065\u0073 \u0069\u006e\u0020\u0070\u0072\u0065\u0064e\u0066\u0069\u006e\u0065\u0064\u0020\u0058\u004d\u0050\u0020\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u0020\u2026 \u0073\u0068\u0061\u006c\u006c\u0020\u0061\u006c\u0073\u006f\u0020\u0062\u0065\u0020\u0065\u006d\u0062\u0065\u0064\u0064\u0065d\u0020\u0069\u006e\u0020\u0074he\u0020\u0066i\u006c\u0065 \u0069\u006e\u0020\u0058\u004d\u0050\u0020\u0066\u006f\u0072\u006d\u0020\u0077\u0069\u0074\u0068\u0020\u0065\u0071\u0075\u0069\u0076\u0061\u006c\u0065\u006e\u0074\u0020\u0076\u0061\u006c\u0075\u0065\u0073\u002e")};
};_ggfd ,_acebga :=_ag .GetStream (_eabb );if !_acebga {return []ViolatedRule {_bb ("\u0036.\u0037\u002e\u0032\u002d\u0032","\u0063\u0061\u0074a\u006c\u006f\u0067\u0020\u0027\u004d\u0065\u0074\u0061\u0064\u0061\u0074\u0061\u0027\u0020\u006f\u0062\u006a\u0065\u0063\u0074\u0020\u0069\u0073\u0020\u006e\u006f\u0074\u0020a\u0020\u0073\u0074\u0072\u0065\u0061\u006d\u002e"),_bb ("\u0036.\u0037\u002e\u0033\u002d\u0031","\u0049\u0066\u0020\u005b\u0061\u0020\u0064\u006fc\u0075\u006d\u0065\u006e\u0074\u0020\u0069\u006e\u0066o\u0072\u006d\u0061t\u0069\u006f\u006e\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u0020\u0061\u0070p\u0065\u0061r\u0073\u0020\u0069n\u0020\u0061 \u0064\u006f\u0063um\u0065\u006e\u0074\u005d\u002c\u0020\u0074\u0068\u0065n\u0020\u0061\u006c\u006c\u0020\u006f\u0066\u0020\u0069\u0074\u0073\u0020\u0065\u006e\u0074\u0072\u0069\u0065\u0073\u0020\u0074\u0068\u0061\u0074\u0020\u0068\u0061\u0076\u0065\u0020\u0061\u006e\u0061\u006c\u006f\u0067\u006fu\u0073\u0020\u0070\u0072\u006f\u0070\u0065\u0072\u0074\u0069\u0065\u0073 \u0069\u006e\u0020\u0070\u0072\u0065\u0064e\u0066\u0069\u006e\u0065\u0064\u0020\u0058\u004d\u0050\u0020\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u0020\u2026 \u0073\u0068\u0061\u006c\u006c\u0020\u0061\u006c\u0073\u006f\u0020\u0062\u0065\u0020\u0065\u006d\u0062\u0065\u0064\u0064\u0065d\u0020\u0069\u006e\u0020\u0074he\u0020\u0066i\u006c\u0065 \u0069\u006e\u0020\u0058\u004d\u0050\u0020\u0066\u006f\u0072\u006d\u0020\u0077\u0069\u0074\u0068\u0020\u0065\u0071\u0075\u0069\u0076\u0061\u006c\u0065\u006e\u0074\u0020\u0076\u0061\u006c\u0075\u0065\u0073\u002e")};
};if _ggfd .Get ("\u0046\u0069\u006c\u0074\u0065\u0072")!=nil {_ceaca =append (_ceaca ,_bb ("\u0036.\u0037\u002e\u0032\u002d\u0032","M\u0065\u0074a\u0064\u0061\u0074\u0061\u0020\u006f\u0062\u006a\u0065\u0063\u0074\u0020\u0073\u0074\u0072\u0065\u0061\u006d\u0020\u0064i\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0069\u0065\u0073\u0020\u0073\u0068\u0061\u006c\u006c \u006e\u006f\u0074\u0020\u0063\u006f\u006e\u0074\u0061\u0069\u006e\u0020\u0074h\u0065\u0020\u0046\u0069\u006c\u0074\u0065\u0072\u0020\u006b\u0065y\u002e"));
};_dbgcf ,_bgdge :=_cd .LoadDocument (_ggfd .Stream );if _bgdge !=nil {return []ViolatedRule {_bb ("\u0036.\u0037\u002e\u0039\u002d\u0031","The\u0020\u006d\u0065\u0074a\u0064\u0061t\u0061\u0020\u0073\u0074\u0072\u0065\u0061\u006d\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0063o\u006e\u0066\u006f\u0072\u006d\u0020\u0074o\u0020\u0058\u004d\u0050\u0020\u0053\u0070\u0065\u0063\u0069\u0066\u0069\u0063\u0061\u0074\u0069\u006f\u006e\u0020\u0061\u006e\u0064\u0020\u0077\u0065\u006c\u006c\u0020\u0066\u006f\u0072\u006de\u0064\u0020\u0050\u0044\u0046\u0041\u0045\u0078\u0074e\u006e\u0073\u0069\u006f\u006e\u0020\u0053\u0063\u0068\u0065\u006da\u0020\u0066\u006fr\u0020\u0061\u006c\u006c\u0020\u0065\u0078\u0074\u0065\u006e\u0073\u0069\u006f\u006e\u0073\u002e")};
};_cgffe :=_dbgcf .GetGoXmpDocument ();var _dfbb []*_de .Namespace ;for _ ,_fgeb :=range _cgffe .Namespaces (){switch _fgeb .Name {case _fd .NsDc .Name ,_fb .NsPDF .Name ,_cg .NsXmp .Name ,_b .NsXmpRights .Name ,_da .Namespace .Name ,_gf .Namespace .Name ,_bd .NsXmpMM .Name ,_gf .FieldNS .Name ,_gf .SchemaNS .Name ,_gf .PropertyNS .Name ,"\u0073\u0074\u0045v\u0074","\u0073\u0074\u0056e\u0072","\u0073\u0074\u0052e\u0066","\u0073\u0074\u0044i\u006d","\u0078a\u0070\u0047\u0049\u006d\u0067","\u0073\u0074\u004ao\u0062","\u0078\u006d\u0070\u0069\u0064\u0071":continue ;
};_dfbb =append (_dfbb ,_fgeb );};_edcc :=true ;_bgdb ,_bgdge :=_dbgcf .GetPdfaExtensionSchemas ();if _bgdge ==nil {for _ ,_bddf :=range _dfbb {var _abad bool ;for _acdfd :=range _bgdb {if _bddf .URI ==_bgdb [_acdfd ].NamespaceURI {_abad =true ;break ;
};};if !_abad {_edcc =false ;break ;};};}else {_edcc =false ;};if !_edcc {_ceaca =append (_ceaca ,_bb ("\u0036.\u0037\u002e\u0039\u002d\u0032","\u0050\u0072\u006f\u0070\u0065\u0072\u0074i\u0065\u0073 \u0073\u0070\u0065\u0063\u0069\u0066\u0069ed\u0020\u0069\u006e\u0020\u0058M\u0050\u0020\u0066\u006f\u0072\u006d\u0020\u0073\u0068\u0061\u006cl\u0020\u0075\u0073\u0065\u0020\u0065\u0069\u0074\u0068\u0065\u0072\u0020\u0074\u0068\u0065\u0020\u0070\u0072\u0065\u0064\u0065\u0066\u0069\u006e\u0065\u0064\u0020\u0073\u0063\u0068\u0065\u006d\u0061\u0073 \u0064\u0065\u0066i\u006e\u0065\u0064\u0020\u0069\u006e\u0020\u0058\u004d\u0050\u0020\u0053\u0070\u0065\u0063\u0069\u0066\u0069\u0063\u0061\u0074\u0069\u006fn\u002c\u0020\u006f\u0072\u0020\u0065\u0078\u0074\u0065ns\u0069\u006f\u006e\u0020\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u0020\u0074\u0068\u0061\u0074 \u0063\u006f\u006d\u0070\u006c\u0079\u0020\u0077\u0069\u0074h\u0020\u0058\u004d\u0050\u0020\u0053\u0070e\u0063\u0069\u0066\u0069\u0063\u0061\u0074\u0069\u006f\u006e\u002e"));
};_ccgdg ,_bgdge :=_agfd .GetPdfInfo ();if _bgdge ==nil {if !_ffbf (_ccgdg ,_dbgcf ){_ceaca =append (_ceaca ,_bb ("\u0036.\u0037\u002e\u0033\u002d\u0031","\u0049\u0066\u0020\u005b\u0061\u0020\u0064\u006fc\u0075\u006d\u0065\u006e\u0074\u0020\u0069\u006e\u0066o\u0072\u006d\u0061t\u0069\u006f\u006e\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u0020\u0061\u0070p\u0065\u0061r\u0073\u0020\u0069n\u0020\u0061 \u0064\u006f\u0063um\u0065\u006e\u0074\u005d\u002c\u0020\u0074\u0068\u0065n\u0020\u0061\u006c\u006c\u0020\u006f\u0066\u0020\u0069\u0074\u0073\u0020\u0065\u006e\u0074\u0072\u0069\u0065\u0073\u0020\u0074\u0068\u0061\u0074\u0020\u0068\u0061\u0076\u0065\u0020\u0061\u006e\u0061\u006c\u006f\u0067\u006fu\u0073\u0020\u0070\u0072\u006f\u0070\u0065\u0072\u0074\u0069\u0065\u0073 \u0069\u006e\u0020\u0070\u0072\u0065\u0064e\u0066\u0069\u006e\u0065\u0064\u0020\u0058\u004d\u0050\u0020\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u0020\u2026 \u0073\u0068\u0061\u006c\u006c\u0020\u0061\u006c\u0073\u006f\u0020\u0062\u0065\u0020\u0065\u006d\u0062\u0065\u0064\u0064\u0065d\u0020\u0069\u006e\u0020\u0074he\u0020\u0066i\u006c\u0065 \u0069\u006e\u0020\u0058\u004d\u0050\u0020\u0066\u006f\u0072\u006d\u0020\u0077\u0069\u0074\u0068\u0020\u0065\u0071\u0075\u0069\u0076\u0061\u006c\u0065\u006e\u0074\u0020\u0076\u0061\u006c\u0075\u0065\u0073\u002e"));
};}else if _ ,_febd :=_dbgcf .GetMediaManagement ();_febd {_ceaca =append (_ceaca ,_bb ("\u0036.\u0037\u002e\u0033\u002d\u0031","\u0049\u0066\u0020\u005b\u0061\u0020\u0064\u006fc\u0075\u006d\u0065\u006e\u0074\u0020\u0069\u006e\u0066o\u0072\u006d\u0061t\u0069\u006f\u006e\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u0020\u0061\u0070p\u0065\u0061r\u0073\u0020\u0069n\u0020\u0061 \u0064\u006f\u0063um\u0065\u006e\u0074\u005d\u002c\u0020\u0074\u0068\u0065n\u0020\u0061\u006c\u006c\u0020\u006f\u0066\u0020\u0069\u0074\u0073\u0020\u0065\u006e\u0074\u0072\u0069\u0065\u0073\u0020\u0074\u0068\u0061\u0074\u0020\u0068\u0061\u0076\u0065\u0020\u0061\u006e\u0061\u006c\u006f\u0067\u006fu\u0073\u0020\u0070\u0072\u006f\u0070\u0065\u0072\u0074\u0069\u0065\u0073 \u0069\u006e\u0020\u0070\u0072\u0065\u0064e\u0066\u0069\u006e\u0065\u0064\u0020\u0058\u004d\u0050\u0020\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u0020\u2026 \u0073\u0068\u0061\u006c\u006c\u0020\u0061\u006c\u0073\u006f\u0020\u0062\u0065\u0020\u0065\u006d\u0062\u0065\u0064\u0064\u0065d\u0020\u0069\u006e\u0020\u0074he\u0020\u0066i\u006c\u0065 \u0069\u006e\u0020\u0058\u004d\u0050\u0020\u0066\u006f\u0072\u006d\u0020\u0077\u0069\u0074\u0068\u0020\u0065\u0071\u0075\u0069\u0076\u0061\u006c\u0065\u006e\u0074\u0020\u0076\u0061\u006c\u0075\u0065\u0073\u002e"));
};_bfbea ,_acebga :=_dbgcf .GetPdfAID ();if !_acebga {_ceaca =append (_ceaca ,_bb ("\u0036\u002e\u0037\u002e\u0031\u0031\u002d\u0031","\u0054\u0068\u0065\u0020\u0050\u0044\u0046\u002f\u0041\u0020\u0076\u0065\u0072\u0073\u0069\u006f\u006e\u0020\u0061n\u0064\u0020\u0063\u006f\u006e\u0066\u006f\u0072\u006d\u0061\u006ec\u0065\u0020\u006c\u0065\u0076\u0065l\u0020\u006f\u0066\u0020\u0061\u0020\u0066\u0069\u006c\u0065\u0020\u0073h\u0061\u006c\u006c\u0020\u0062\u0065\u0020\u0073\u0070e\u0063\u0069\u0066\u0069\u0065\u0064\u0020\u0075\u0073\u0069\u006e\u0067\u0020\u0074\u0068\u0065\u0020\u0050\u0044\u0046\u002f\u0041\u0020\u0049\u0064\u0065\u006e\u0074\u0069\u0066\u0069\u0063\u0061\u0074\u0069\u006f\u006e\u0020\u0065\u0078\u0074\u0065\u006e\u0073\u0069\u006f\u006e\u0020\u0073\u0063h\u0065\u006da."));
}else {if _bfbea .Part !=_fegb ._dg {_ceaca =append (_ceaca ,_bb ("\u0036\u002e\u0037\u002e\u0031\u0031\u002d\u0032","\u0054h\u0065\u0020\u0076\u0061lue\u0020\u006f\u0066\u0020p\u0064\u0066\u0061\u0069\u0064\u003a\u0070\u0061\u0072\u0074 \u0073\u0068\u0061\u006c\u006c\u0020\u0062\u0065\u0020\u0074\u0068\u0065\u0020\u0070\u0061\u0072\u0074\u0020\u006e\u0075\u006d\u0062\u0065r\u0020\u006f\u0066\u0020\u0049\u0053\u004f\u002019\u0030\u0030\u0035 \u0074\u006f\u0020\u0077\u0068i\u0063h\u0020\u0074\u0068\u0065\u0020\u0066\u0069\u006c\u0065 \u0063\u006f\u006e\u0066\u006f\u0072\u006d\u0073\u002e"));
};if _fegb ._db =="\u0041"&&_bfbea .Conformance !="\u0041"{_ceaca =append (_ceaca ,_bb ("\u0036\u002e\u0037\u002e\u0031\u0031\u002d\u0033","\u0041\u0020\u004c\u0065\u0076e\u006c\u0020\u0041\u0020\u0063\u006f\u006e\u0066\u006f\u0072\u006d\u0069\u006e\u0067\u0020\u0066\u0069\u006c\u0065 \u0073\u0068\u0061\u006c\u006c\u0020s\u0070\u0065\u0063i\u0066\u0079\u0020\u0074\u0068\u0065\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u006f\u0066\u0020\u0070\u0064\u0066\u0061\u0069\u0064\u003a\u0063o\u006e\u0066\u006fr\u006d\u0061\u006e\u0063\u0065\u0020\u0061\u0073\u0020\u0041\u002e\u0020\u0041\u0020\u004c\u0065\u0076e\u006c\u0020\u0042\u0020\u0063\u006f\u006e\u0066o\u0072\u006d\u0069\u006e\u0067\u0020\u0066\u0069\u006c\u0065\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0073\u0070e\u0063\u0069\u0066\u0079\u0020\u0074\u0068\u0065\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u006f\u0066\u0020\u0070\u0064\u0066\u0061\u0069d\u003a\u0063\u006f\u006e\u0066\u006f\u0072\u006d\u0061\u006e\u0063\u0065\u0020\u0061\u0073\u0020\u0042\u002e"));
}else if _fegb ._db =="\u0042"&&(_bfbea .Conformance !="\u0041"&&_bfbea .Conformance !="\u0042"){_ceaca =append (_ceaca ,_bb ("\u0036\u002e\u0037\u002e\u0031\u0031\u002d\u0033","\u0041\u0020\u004c\u0065\u0076e\u006c\u0020\u0041\u0020\u0063\u006f\u006e\u0066\u006f\u0072\u006d\u0069\u006e\u0067\u0020\u0066\u0069\u006c\u0065 \u0073\u0068\u0061\u006c\u006c\u0020s\u0070\u0065\u0063i\u0066\u0079\u0020\u0074\u0068\u0065\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u006f\u0066\u0020\u0070\u0064\u0066\u0061\u0069\u0064\u003a\u0063o\u006e\u0066\u006fr\u006d\u0061\u006e\u0063\u0065\u0020\u0061\u0073\u0020\u0041\u002e\u0020\u0041\u0020\u004c\u0065\u0076e\u006c\u0020\u0042\u0020\u0063\u006f\u006e\u0066o\u0072\u006d\u0069\u006e\u0067\u0020\u0066\u0069\u006c\u0065\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0073\u0070e\u0063\u0069\u0066\u0079\u0020\u0074\u0068\u0065\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u006f\u0066\u0020\u0070\u0064\u0066\u0061\u0069d\u003a\u0063\u006f\u006e\u0066\u006f\u0072\u006d\u0061\u006e\u0063\u0065\u0020\u0061\u0073\u0020\u0042\u002e"));
};};return _ceaca ;};func _dgbd (_fcba *_gc .Document )(*_ag .PdfObjectDictionary ,bool ){_cfd ,_faed :=_fcba .FindCatalog ();if !_faed {return nil ,false ;};_fged ,_faed :=_ag .GetArray (_cfd .Object .Get ("\u004f\u0075\u0074\u0070\u0075\u0074\u0049\u006e\u0074\u0065\u006e\u0074\u0073"));
if !_faed {return nil ,false ;};if _fged .Len ()==0{return nil ,false ;};return _ag .GetDict (_fged .Get (0));};func _bcbg (_gffg *_a .PdfFont ,_gdaf *_ag .PdfObjectDictionary )ViolatedRule {const (_gdbc ="\u0036.\u0033\u002e\u0037\u002d\u0032";_bfaga ="\u0041l\u006c\u0020\u0073\u0079\u006d\u0062\u006f\u006c\u0069\u0063\u0020\u0054\u0072u\u0065\u0054\u0079p\u0065\u0020\u0066\u006f\u006e\u0074s\u0020\u0073h\u0061\u006c\u006c\u0020\u006e\u006f\u0074\u0020\u0073\u0070\u0065\u0063\u0069\u0066\u0079\u0020\u0061\u006e\u0020\u0045\u006e\u0063\u006f\u0064\u0069\u006e\u0067\u0020\u0065n\u0074\u0072\u0079\u0020\u0069n\u0020\u0074\u0068e\u0020\u0066\u006f\u006e\u0074 \u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u002e";
);var _dddg string ;if _eeee ,_bbgf :=_ag .GetName (_gdaf .Get ("\u0053u\u0062\u0074\u0079\u0070\u0065"));_bbgf {_dddg =_eeee .String ();};if _dddg !="\u0054\u0072\u0075\u0065\u0054\u0079\u0070\u0065"{return _eag ;};_ebdb :=_gffg .FontDescriptor ();_gbbfe ,_ebdf :=_ag .GetIntVal (_ebdb .Flags );
if !_ebdf {_g .Log .Debug ("\u0066\u006c\u0061\u0067\u0073 \u006e\u006f\u0074\u0020\u0064\u0065\u0066\u0069\u006e\u0065\u0064\u0020\u0066o\u0072\u0020\u0074\u0068\u0065\u0020\u0066\u006f\u006e\u0074\u0020\u0064\u0065\u0073\u0063\u0072\u0069\u0070\u0074\u006f\u0072");
return _bb (_gdbc ,_bfaga );};_efdcf :=(uint32 (_gbbfe )>>3)&1;_gabc :=_efdcf !=0;if !_gabc {return _eag ;};if _gdaf .Get ("\u0045\u006e\u0063\u006f\u0064\u0069\u006e\u0067")!=nil {return _bb (_gdbc ,_bfaga );};return _eag ;};func _eada (_egefg *_a .CompliancePdfReader )ViolatedRule {if _egefg .ParserMetadata ().HeaderPosition ()!=0{return _bb ("\u0036.\u0031\u002e\u0032\u002d\u0031","h\u0065\u0061\u0064\u0065\u0072\u0020\u0070\u006f\u0073\u0069\u0074\u0069\u006f\u006e\u0020\u0069\u0073\u0020n\u006f\u0074\u0020\u0061\u0074\u0020\u0074\u0068\u0065\u0020fi\u0072\u0073\u0074 \u0062y\u0074\u0065");
};if _egefg .PdfVersion ().Major !=1{return _bb ("\u0036.\u0031\u002e\u0032\u002d\u0031","\u0054\u0068\u0065\u0020\u0066\u0069l\u0065\u0020\u0068\u0065\u0061\u0064e\u0072 \u0073\u0068\u0061\u006c\u006c\u0020c\u006f\u006e\u0073\u0069s\u0074 \u006f\u0066\u0020\u201c%\u0050\u0044\u0046\u002d\u0031\u002e\u006e\u201d\u0020\u0066\u006f\u006c\u006c\u006f\u0077\u0065\u0064\u0020\u0062\u0079\u0020\u0061\u0020\u0073\u0069\u006e\u0067\u006c\u0065 \u0045\u004f\u004c\u0020ma\u0072\u006b\u0065\u0072\u002c \u0077\u0068\u0065\u0072\u0065\u0020\u0027\u006e\u0027\u0020\u0069s\u0020\u0061\u0020\u0073\u0069\u006e\u0067\u006c\u0065\u0020\u0064\u0069\u0067\u0069t\u0020\u006e\u0075\u006d\u0062e\u0072\u0020\u0062\u0065\u0074\u0077\u0065\u0065\u006e\u0020\u0030\u0020(\u0033\u0030h\u0029\u0020\u0061\u006e\u0064\u0020\u0037\u0020\u0028\u0033\u0037\u0068\u0029");
};if _egefg .PdfVersion ().Minor < 0||_egefg .PdfVersion ().Minor > 7{return _bb ("\u0036.\u0031\u002e\u0032\u002d\u0031","\u0054\u0068\u0065\u0020\u0066\u0069l\u0065\u0020\u0068\u0065\u0061\u0064e\u0072 \u0073\u0068\u0061\u006c\u006c\u0020c\u006f\u006e\u0073\u0069s\u0074 \u006f\u0066\u0020\u201c%\u0050\u0044\u0046\u002d\u0031\u002e\u006e\u201d\u0020\u0066\u006f\u006c\u006c\u006f\u0077\u0065\u0064\u0020\u0062\u0079\u0020\u0061\u0020\u0073\u0069\u006e\u0067\u006c\u0065 \u0045\u004f\u004c\u0020ma\u0072\u006b\u0065\u0072\u002c \u0077\u0068\u0065\u0072\u0065\u0020\u0027\u006e\u0027\u0020\u0069s\u0020\u0061\u0020\u0073\u0069\u006e\u0067\u006c\u0065\u0020\u0064\u0069\u0067\u0069t\u0020\u006e\u0075\u006d\u0062e\u0072\u0020\u0062\u0065\u0074\u0077\u0065\u0065\u006e\u0020\u0030\u0020(\u0033\u0030h\u0029\u0020\u0061\u006e\u0064\u0020\u0037\u0020\u0028\u0033\u0037\u0068\u0029");
};return _eag ;};func _cea (_eafc *_gc .Document )error {_aed ,_ccb :=_eafc .FindCatalog ();if !_ccb {return _ce .New ("\u0063\u0061\u0074\u0061\u006c\u006f\u0067\u0020\u006e\u006f\u0074\u0020f\u006f\u0075\u006e\u0064");};_aed .SetVersion ();return nil ;
};var _ Profile =(*Profile1A )(nil );func _fagg (_egba *_a .CompliancePdfReader )(_fdbb []ViolatedRule ){_ccdg :=_egba .ParserMetadata ();if _ccdg .HasInvalidSubsectionHeader (){_fdbb =append (_fdbb ,_bb ("\u0036.\u0031\u002e\u0034\u002d\u0031","\u006e\u0020\u0061\u0020\u0063\u0072\u006f\u0073\u0073\u0020\u0072\u0065\u0066\u0065\u0072\u0065\u006e\u0063\u0065\u0020\u0073\u0075\u0062\u0073\u0065c\u0074\u0069\u006f\u006e\u0020h\u0065a\u0064\u0065\u0072\u0020t\u0068\u0065\u0020\u0073\u0074\u0061\u0072t\u0069\u006e\u0067\u0020\u006fb\u006a\u0065\u0063\u0074 \u006e\u0075\u006d\u0062\u0065\u0072\u0020\u0061\u006e\u0064\u0020\u0074\u0068\u0065\u0020\u0072\u0061n\u0067e\u0020s\u0068\u0061\u006c\u006c\u0020\u0062\u0065\u0020\u0073\u0065\u0070\u0061\u0072\u0061\u0074\u0065\u0064\u0020\u0062\u0079\u0020\u0061\u0020s\u0069\u006e\u0067\u006c\u0065\u0020\u0053\u0050\u0041C\u0045\u0020\u0063\u0068\u0061\u0072\u0061\u0063\u0074e\u0072\u0020\u0028\u0032\u0030\u0068\u0029\u002e"));
};if _ccdg .HasInvalidSeparationAfterXRef (){_fdbb =append (_fdbb ,_bb ("\u0036.\u0031\u002e\u0034\u002d\u0032","\u0054\u0068\u0065 \u0078\u0072\u0065\u0066\u0020\u006b\u0065\u0079\u0077\u006fr\u0064\u0020\u0061\u006e\u0064\u0020\u0074\u0068\u0065\u0020\u0063\u0072\u006f\u0073s\u0020\u0072\u0065\u0066e\u0072\u0065\u006e\u0063\u0065 s\u0075b\u0073\u0065\u0063ti\u006f\u006e\u0020\u0068\u0065\u0061\u0064e\u0072\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0062\u0065\u0020\u0073\u0065\u0070\u0061\u0072\u0061\u0074\u0065\u0064\u0020\u0062\u0079 \u0061\u0020\u0073i\u006e\u0067\u006c\u0065\u0020\u0045\u004fL\u0020\u006d\u0061\u0072\u006b\u0065\u0072\u002e"));
};return _fdbb ;};func _ffbf (_agea *_a .PdfInfo ,_edbc *_cd .Document )bool {_dcca ,_eegc :=_edbc .GetPdfInfo ();if !_eegc {return false ;};if _dcca .InfoDict ==nil {return false ;};_cdfbb ,_bfgbd :=_a .NewPdfInfoFromObject (_dcca .InfoDict );if _bfgbd !=nil {return false ;
};if _agea .Creator !=nil {if _cdfbb .Creator ==nil ||_cdfbb .Creator .String ()!=_agea .Creator .String (){return false ;};};if _agea .CreationDate !=nil {if _cdfbb .CreationDate ==nil ||!_cdfbb .CreationDate .ToGoTime ().Equal (_agea .CreationDate .ToGoTime ()){return false ;
};};if _agea .ModifiedDate !=nil {if _cdfbb .ModifiedDate ==nil ||!_cdfbb .ModifiedDate .ToGoTime ().Equal (_agea .ModifiedDate .ToGoTime ()){return false ;};};if _agea .Producer !=nil {if _cdfbb .Producer ==nil ||_cdfbb .Producer .String ()!=_agea .Producer .String (){return false ;
};};if _agea .Keywords !=nil {if _cdfbb .Keywords ==nil ||_cdfbb .Keywords .String ()!=_agea .Keywords .String (){return false ;};};if _agea .Trapped !=nil {if _cdfbb .Trapped ==nil {return false ;};switch _agea .Trapped .String (){case "\u0054\u0072\u0075\u0065":if _cdfbb .Trapped .String ()!="\u0054\u0072\u0075\u0065"{return false ;
};case "\u0046\u0061\u006cs\u0065":if _cdfbb .Trapped .String ()!="\u0046\u0061\u006cs\u0065"{return false ;};default:if _cdfbb .Trapped .String ()!="\u0046\u0061\u006cs\u0065"{return false ;};};};if _agea .Title !=nil {if _cdfbb .Title ==nil ||_cdfbb .Title .String ()!=_agea .Title .String (){return false ;
};};if _agea .Subject !=nil {if _cdfbb .Subject ==nil ||_cdfbb .Subject .String ()!=_agea .Subject .String (){return false ;};};return true ;};func _fbab (_bbb *_gc .Document )error {for _ ,_ecc :=range _bbb .Objects {_bfa ,_edab :=_ag .GetDict (_ecc );
if !_edab {continue ;};_gcde :=_bfa .Get ("\u0054\u0079\u0070\u0065");if _gcde ==nil {continue ;};if _ddg ,_gaba :=_ag .GetName (_gcde );_gaba &&_ddg .String ()!="\u0041\u0063\u0072\u006f\u0046\u006f\u0072\u006d"{continue ;};_ddfb ,_adg :=_ag .GetBool (_bfa .Get ("\u004ee\u0065d\u0041\u0070\u0070\u0065\u0061\u0072\u0061\u006e\u0063\u0065\u0073"));
if _adg {if bool (*_ddfb ){_bfa .Set ("\u004ee\u0065d\u0041\u0070\u0070\u0065\u0061\u0072\u0061\u006e\u0063\u0065\u0073",_ag .MakeBool (false ));};};_dfb :=_bfa .Get ("\u0041");if _dfb !=nil {_bfa .Remove ("\u0041");};_ccef ,_adg :=_ag .GetArray (_bfa .Get ("\u0046\u0069\u0065\u006c\u0064\u0073"));
if _adg {for _cdba :=0;_cdba < _ccef .Len ();_cdba ++{_bbdg ,_cga :=_ag .GetDict (_ccef .Get (_cdba ));if !_cga {continue ;};if _bbdg .Get ("\u0041\u0041")!=nil {_bbdg .Remove ("\u0041\u0041");};};};};return nil ;};var _ Profile =(*Profile3U )(nil );

// DefaultProfile1Options are the default options for the Profile1.
func DefaultProfile1Options ()*Profile1Options {return &Profile1Options {Now :_e .Now ,Xmp :XmpOptions {MarshalIndent :"\u0009"}};};func _aaeg (_dabc *_gc .Document )error {_fgcfc :=func (_bfaa *_ag .PdfObjectDictionary )error {if _bfaa .Get ("\u0054\u0052")!=nil {_g .Log .Debug ("\u0045\u0078\u0074\u0047\u0053\u0074\u0061\u0074\u0065\u0020\u006f\u0062\u006a\u0065\u0063t\u0020c\u006f\u006e\u0074\u0061\u0069\u006e\u0073\u0020\u0054\u0052\u0020\u006b\u0065\u0079");
_bfaa .Remove ("\u0054\u0052");};_cdaa :=_bfaa .Get ("\u0054\u0052\u0032");if _cdaa !=nil {_eadgd :=_cdaa .String ();if _eadgd !="\u0044e\u0066\u0061\u0075\u006c\u0074"{_g .Log .Debug ("\u0045x\u0074\u0047\u0053\u0074\u0061\u0074\u0065 o\u0062\u006a\u0065\u0063\u0074\u0020\u0063\u006f\u006e\u0074\u0061\u0069\u006e\u0073 \u0054\u00522\u0020\u006b\u0065y\u0020\u0077\u0069\u0074\u0068\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u006f\u0074\u0068\u0065r\u0020\u0074ha\u006e\u0020\u0044e\u0066\u0061\u0075\u006c\u0074");
_bfaa .Set ("\u0054\u0052\u0032",_ag .MakeName ("\u0044e\u0066\u0061\u0075\u006c\u0074"));};};if _bfaa .Get ("\u0048\u0054\u0050")!=nil {_g .Log .Debug ("\u0045\u0078\u0074\u0047\u0053\u0074a\u0074\u0065\u0020\u006f\u0062\u006a\u0065\u0063\u0074\u0020\u0063\u006f\u006et\u0061\u0069\u006e\u0073\u0020\u0048\u0054P\u0020\u006b\u0065\u0079");
_bfaa .Remove ("\u0048\u0054\u0050");};_beb :=_bfaa .Get ("\u0042\u004d");if _beb !=nil {_bfe ,_dfde :=_ag .GetName (_beb );if !_dfde {_g .Log .Debug ("E\u0078\u0074\u0047\u0053\u0074\u0061t\u0065\u0020\u006f\u0062\u006a\u0065c\u0074\u0020\u0027\u0042\u004d\u0027\u0020i\u0073\u0020\u006e\u006f\u0074\u0020\u0061\u0020\u004e\u0061m\u0065");
_bfe =_ag .MakeName ("");};_dbcd :=_bfe .String ();switch _dbcd {case "\u004e\u006f\u0072\u006d\u0061\u006c","\u0043\u006f\u006d\u0070\u0061\u0074\u0069\u0062\u006c\u0065","\u004d\u0075\u006c\u0074\u0069\u0070\u006c\u0079","\u0053\u0063\u0072\u0065\u0065\u006e","\u004fv\u0065\u0072\u006c\u0061\u0079","\u0044\u0061\u0072\u006b\u0065\u006e","\u004ci\u0067\u0068\u0074\u0065\u006e","\u0043\u006f\u006c\u006f\u0072\u0044\u006f\u0064\u0067\u0065","\u0043o\u006c\u006f\u0072\u0042\u0075\u0072n","\u0048a\u0072\u0064\u004c\u0069\u0067\u0068t","\u0053o\u0066\u0074\u004c\u0069\u0067\u0068t","\u0044\u0069\u0066\u0066\u0065\u0072\u0065\u006e\u0063\u0065","\u0045x\u0063\u006c\u0075\u0073\u0069\u006fn","\u0048\u0075\u0065","\u0053\u0061\u0074\u0075\u0072\u0061\u0074\u0069\u006f\u006e","\u0043\u006f\u006co\u0072","\u004c\u0075\u006d\u0069\u006e\u006f\u0073\u0069\u0074\u0079":default:_bfaa .Set ("\u0042\u004d",_ag .MakeName ("\u004e\u006f\u0072\u006d\u0061\u006c"));
};};return nil ;};_ddfa ,_badf :=_dabc .GetPages ();if !_badf {return nil ;};for _ ,_fdbce :=range _ddfa {_gcdf ,_dga :=_fdbce .GetResources ();if !_dga {continue ;};_gegc ,_ccab :=_ag .GetDict (_gcdf .Get ("\u0045x\u0074\u0047\u0053\u0074\u0061\u0074e"));
if !_ccab {return nil ;};_bdce :=_gegc .Keys ();for _ ,_cdfd :=range _bdce {_gbeb ,_edf :=_ag .GetDict (_gegc .Get (_cdfd ));if !_edf {continue ;};_geef :=_fgcfc (_gbeb );if _geef !=nil {continue ;};};};for _ ,_bccd :=range _ddfa {_fggf ,_fgefg :=_bccd .GetContents ();
if !_fgefg {return nil ;};for _ ,_fegd :=range _fggf {_gef ,_gbg :=_fegd .GetData ();if _gbg !=nil {continue ;};_ecd :=_fbd .NewContentStreamParser (string (_gef ));_bbeg ,_gbg :=_ecd .Parse ();if _gbg !=nil {continue ;};for _ ,_ccefg :=range *_bbeg {if len (_ccefg .Params )==0{continue ;
};_ ,_cgd :=_ag .GetName (_ccefg .Params [0]);if !_cgd {continue ;};_eged ,_gbee :=_bccd .GetResourcesXObject ();if !_gbee {continue ;};for _ ,_cbbe :=range _eged .Keys (){_bdcd ,_bcga :=_ag .GetStream (_eged .Get (_cbbe ));if !_bcga {continue ;};_dce ,_bcga :=_ag .GetDict (_bdcd .Get ("\u0052e\u0073\u006f\u0075\u0072\u0063\u0065s"));
if !_bcga {continue ;};_fbdb ,_bcga :=_ag .GetDict (_dce .Get ("\u0045x\u0074\u0047\u0053\u0074\u0061\u0074e"));if !_bcga {continue ;};for _ ,_afef :=range _fbdb .Keys (){_cfg ,_ddfc :=_ag .GetDict (_fbdb .Get (_afef ));if !_ddfc {continue ;};_gfac :=_fgcfc (_cfg );
if _gfac !=nil {continue ;};};};};};};return nil ;};type pageColorspaceOptimizeFunc func (_bbea *_gc .Document ,_ggg *_gc .Page ,_bfb []*_gc .Image )error ;type colorspaceModification struct{_af _fe .ColorConverter ;_cbf _a .PdfColorspace ;};func _gfbb (_edcfd *_ag .PdfObjectStream ,_ffad map[*_ag .PdfObjectStream ][]byte ,_adba map[*_ag .PdfObjectStream ]*_ab .CMap )(*_ab .CMap ,error ){_dgag ,_dggd :=_adba [_edcfd ];
if !_dggd {var _fgbb error ;_aeeba ,_abbce :=_ffad [_edcfd ];if !_abbce {_aeeba ,_fgbb =_ag .DecodeStream (_edcfd );if _fgbb !=nil {_g .Log .Debug ("\u0064\u0065\u0063\u006f\u0064\u0069\u006e\u0067\u0020\u0073\u0074r\u0065\u0061\u006d\u0020\u0066\u0061\u0069\u006c\u0065\u0064:\u0020\u0025\u0076",_fgbb );
return nil ,_fgbb ;};_ffad [_edcfd ]=_aeeba ;};_dgag ,_fgbb =_ab .LoadCmapFromData (_aeeba ,false );if _fgbb !=nil {return nil ,_fgbb ;};_adba [_edcfd ]=_dgag ;};return _dgag ,nil ;};func _fde (_fff *_a .XObjectImage ,_gbc imageModifications )error {_fec ,_gbd :=_fff .ToImage ();
if _gbd !=nil {return _gbd ;};if _gbc ._feb !=nil {_fff .Filter =_gbc ._feb ;};_ceb :=_ag .MakeDict ();_ceb .Set ("\u0051u\u0061\u006c\u0069\u0074\u0079",_ag .MakeInteger (100));_ceb .Set ("\u0050r\u0065\u0064\u0069\u0063\u0074\u006fr",_ag .MakeInteger (1));
_fff .Decode =nil ;if _gbd =_fff .SetImage (_fec ,nil );_gbd !=nil {return _gbd ;};_fff .ToPdfObject ();return nil ;};

// NewProfile3B creates a new Profile3B with the given options.
func NewProfile3B (options *Profile3Options )*Profile3B {if options ==nil {options =DefaultProfile3Options ();};_abaf (options );return &Profile3B {profile3 {_gae :*options ,_gfdf :_ea ()}};};