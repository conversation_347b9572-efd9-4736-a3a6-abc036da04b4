//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package mdp ;import (_c "errors";_cd "fmt";_ce "github.com/unidoc/unipdf/v3/core";);func _fbg (_fecg _ce .PdfObject )([]_ce .PdfObject ,error ){_gce :=make ([]_ce .PdfObject ,0);if _fecg !=nil {_ccg :=_fecg ;if _abd ,_gbfc :=_ce .GetIndirect (_fecg );_gbfc {_ccg =_abd .PdfObject ;
};if _ddda ,_gaf :=_ce .GetArray (_ccg );_gaf {_gce =_ddda .Elements ();}else {return nil ,_c .New ("\u0075n\u0065\u0078\u0070\u0065c\u0074\u0065\u0064\u0020\u0061n\u006eo\u0074s\u0027\u0020\u006f\u0062\u006a\u0065\u0063t");};};return _gce ,nil ;};func (_dag *DiffResults )addWarningWithDescription (_ebf int ,_caf string ){if _dag .Warnings ==nil {_dag .Warnings =make ([]*DiffResult ,0);
};_dag .Warnings =append (_dag .Warnings ,&DiffResult {Revision :_ebf ,Description :_caf });};

// MDPParameters describes parameters for the MDP checks (now only DocMDP).
type MDPParameters struct{DocMDPLevel DocMDPPermission ;};

// DiffResults describes the results of the DiffPolicy.
type DiffResults struct{Warnings []*DiffResult ;Errors []*DiffResult ;};

// IsPermitted returns true if changes permitted.
func (_ded *DiffResults )IsPermitted ()bool {return len (_ded .Errors )==0};func (_bb *DiffResults )addErrorWithDescription (_cfd int ,_efc string ){if _bb .Errors ==nil {_bb .Errors =make ([]*DiffResult ,0);};_bb .Errors =append (_bb .Errors ,&DiffResult {Revision :_cfd ,Description :_efc });
};func NewDefaultDiffPolicy ()DiffPolicy {return &defaultDiffPolicy {_ec :nil ,_f :&DiffResults {},_d :0};};

// DocMDPPermission is values for set up access permissions for DocMDP.
// (Section ********, Table 254 - Entries in a signature dictionary p. 471 in PDF32000_2008).
type DocMDPPermission int64 ;func (_eed *defaultDiffPolicy )comparePages (_fgd int ,_fgc ,_afa *_ce .PdfIndirectObject )error {if _ ,_affb :=_eed ._ec [_afa .ObjectNumber ];_affb {_eed ._f .addErrorWithDescription (_fgd ,"\u0050a\u0067e\u0073\u0020\u0077\u0065\u0072e\u0020\u0063h\u0061\u006e\u0067\u0065\u0064");
};_edb ,_fbea :=_ce .GetDict (_afa .PdfObject );_be ,_df :=_ce .GetDict (_fgc .PdfObject );if !_fbea ||!_df {return _c .New ("\u0075n\u0065\u0078\u0070\u0065\u0063\u0074\u0065\u0064\u0020\u0050\u0061g\u0065\u0073\u0027\u0020\u006f\u0062\u006a\u0065\u0063\u0074");
};_fcb ,_fbea :=_ce .GetArray (_edb .Get ("\u004b\u0069\u0064\u0073"));_fbeb ,_df :=_ce .GetArray (_be .Get ("\u004b\u0069\u0064\u0073"));if !_fbea ||!_df {return _c .New ("\u0075\u006e\u0065\u0078p\u0065\u0063\u0074\u0065\u0064\u0020\u0050\u0061\u0067\u0065s\u0027 \u0064\u0069\u0063\u0074\u0069\u006f\u006ea\u0072\u0079");
};_ddd :=_fcb .Len ();if _ddd > _fbeb .Len (){_ddd =_fbeb .Len ();};for _dfc :=0;_dfc < _ddd ;_dfc ++{_cde ,_gee :=_ce .GetIndirect (_ce .ResolveReference (_fbeb .Get (_dfc )));_fde ,_gad :=_ce .GetIndirect (_ce .ResolveReference (_fcb .Get (_dfc )));if !_gee ||!_gad {return _c .New ("\u0075\u006e\u0065\u0078pe\u0063\u0074\u0065\u0064\u0020\u0070\u0061\u0067\u0065\u0020\u006f\u0062\u006a\u0065c\u0074");
};if _cde .ObjectNumber !=_fde .ObjectNumber {_eed ._f .addErrorWithDescription (_fgd ,_cd .Sprintf ("p\u0061\u0067\u0065\u0020#%\u0064 \u0077\u0061\u0073\u0020\u0072e\u0070\u006c\u0061\u0063\u0065\u0064",_dfc ));};_ddg ,_gee :=_ce .GetDict (_fde );_ace ,_gad :=_ce .GetDict (_cde );
if !_gee ||!_gad {return _c .New ("\u0075\u006e\u0065\u0078p\u0065\u0063\u0074\u0065\u0064\u0020\u0070\u0061\u0067\u0065'\u0073 \u0064\u0069\u0063\u0074\u0069\u006f\u006ea\u0072\u0079");};_geed ,_fdf :=_fbg (_ddg .Get ("\u0041\u006e\u006e\u006f\u0074\u0073"));
if _fdf !=nil {return _fdf ;};_ef ,_fdf :=_fbg (_ace .Get ("\u0041\u006e\u006e\u006f\u0074\u0073"));if _fdf !=nil {return _fdf ;};if _bd :=_eed .compareAnnots (_fgd ,_ef ,_geed );_bd !=nil {return _bd ;};};for _cfa :=_ddd +1;_cfa <=_fcb .Len ();_cfa ++{_eed ._f .addErrorWithDescription (_fgd ,_cd .Sprintf ("\u0070a\u0067e\u0020\u0023\u0025\u0064\u0020w\u0061\u0073 \u0061\u0064\u0064\u0065\u0064",_cfa ));
};for _bg :=_ddd +1;_bg <=_fbeb .Len ();_bg ++{_eed ._f .addErrorWithDescription (_fgd ,_cd .Sprintf ("p\u0061g\u0065\u0020\u0023\u0025\u0064\u0020\u0077\u0061s\u0020\u0072\u0065\u006dov\u0065\u0064",_bg ));};return nil ;};func (_eca *defaultDiffPolicy )compareRevisions (_ede *_ce .PdfParser ,_af *_ce .PdfParser )(*DiffResults ,error ){var _ad error ;
_eca ._ec ,_ad =_af .GetUpdatedObjects (_ede );if _ad !=nil {return &DiffResults {},_ad ;};if len (_eca ._ec )==0{return &DiffResults {},nil ;};_cdg :=_af .GetRevisionNumber ();_cf ,_b :=_ce .GetIndirect (_ce .ResolveReference (_ede .GetTrailer ().Get ("\u0052\u006f\u006f\u0074")));
_ac ,_cfg :=_ce .GetIndirect (_ce .ResolveReference (_af .GetTrailer ().Get ("\u0052\u006f\u006f\u0074")));if !_b ||!_cfg {return &DiffResults {},_c .New ("\u0065\u0072\u0072o\u0072\u0020\u0077\u0068i\u006c\u0065\u0020\u0067\u0065\u0074\u0074i\u006e\u0067\u0020\u0072\u006f\u006f\u0074\u0020\u006f\u0062\u006a\u0065\u0063\u0074");
};_eg ,_b :=_ce .GetDict (_ce .ResolveReference (_cf .PdfObject ));_ada ,_cfg :=_ce .GetDict (_ce .ResolveReference (_ac .PdfObject ));if !_b ||!_cfg {return &DiffResults {},_c .New ("\u0065\u0072\u0072\u006f\u0072\u0020\u0077\u0068\u0069\u006c\u0065\u0020\u0067e\u0074\u0074\u0069\u006e\u0067\u0020a\u0020\u0072\u006f\u006f\u0074\u0027\u0073\u0020\u0064\u0069\u0063\u0074\u0069o\u006e\u0061\u0072\u0079");
};if _fg ,_ee :=_ce .GetIndirect (_ada .Get ("\u0041\u0063\u0072\u006f\u0046\u006f\u0072\u006d"));_ee {_aff ,_fa :=_ce .GetDict (_fg );if !_fa {return &DiffResults {},_c .New ("\u0065\u0072\u0072\u006f\u0072 \u0077\u0068\u0069\u006c\u0065\u0020\u0067\u0065\u0074\u0074\u0069\u006e\u0067 \u0041\u0063\u0072\u006f\u0046\u006f\u0072\u006d\u0027\u0073\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079");
};_da :=make ([]_ce .PdfObject ,0);if _fe ,_fb :=_ce .GetIndirect (_eg .Get ("\u0041\u0063\u0072\u006f\u0046\u006f\u0072\u006d"));_fb {if _fbe ,_cba :=_ce .GetDict (_fe );_cba {if _egb ,_ecc :=_ce .GetArray (_fbe .Get ("\u0046\u0069\u0065\u006c\u0064\u0073"));
_ecc {_da =_egb .Elements ();};};};_bf ,_fa :=_ce .GetArray (_aff .Get ("\u0046\u0069\u0065\u006c\u0064\u0073"));if !_fa {return &DiffResults {},_c .New ("\u0065\u0072r\u006f\u0072\u0020\u0077h\u0069\u006ce\u0020\u0067\u0065\u0074\u0074\u0069\u006e\u0067 \u0041\u0063\u0072\u006f\u0046\u006f\u0072\u006d\u0027\u0073\u0020\u0066i\u0065\u006c\u0064\u0073");
};if _eb :=_eca .compareFields (_cdg ,_da ,_bf .Elements ());_eb !=nil {return &DiffResults {},_eb ;};};_adg ,_dbb :=_ce .GetIndirect (_ada .Get ("\u0050\u0061\u0067e\u0073"));if !_dbb {return &DiffResults {},_c .New ("\u0065\u0072\u0072\u006f\u0072\u0020w\u0068\u0069\u006c\u0065\u0020\u0067\u0065\u0074\u0074\u0069\u006e\u0067\u0020p\u0061\u0067\u0065\u0073\u0027\u0020\u006fb\u006a\u0065\u0063\u0074");
};_dg ,_dbb :=_ce .GetIndirect (_eg .Get ("\u0050\u0061\u0067e\u0073"));if !_dbb {return &DiffResults {},_c .New ("\u0065\u0072\u0072\u006f\u0072\u0020w\u0068\u0069\u006c\u0065\u0020\u0067\u0065\u0074\u0074\u0069\u006e\u0067\u0020p\u0061\u0067\u0065\u0073\u0027\u0020\u006fb\u006a\u0065\u0063\u0074");
};if _g :=_eca .comparePages (_cdg ,_dg ,_adg );_g !=nil {return &DiffResults {},_g ;};return _eca ._f ,nil ;};const (NoRestrictions DocMDPPermission =0;NoChanges DocMDPPermission =1;FillForms DocMDPPermission =2;FillFormsAndAnnots DocMDPPermission =3;
);func (_fafc *DiffResults )addWarning (_fae *DiffResult ){if _fafc .Warnings ==nil {_fafc .Warnings =make ([]*DiffResult ,0);};_fafc .Warnings =append (_fafc .Warnings ,_fae );};func (_fec *defaultDiffPolicy )compareFields (_gd int ,_bc ,_ab []_ce .PdfObject )error {_ebc :=make (map[int64 ]*_ce .PdfObjectDictionary );
for _ ,_fc :=range _bc {_ade ,_dd :=_ce .GetIndirect (_fc );if !_dd {return _c .New ("\u0075\u006e\u0065\u0078p\u0065\u0063\u0074\u0065\u0064\u0020\u0066\u0069\u0065\u006cd\u0027s\u0020\u0073\u0074\u0072\u0075\u0063\u0074u\u0072\u0065");};_ge ,_dd :=_ce .GetDict (_ade .PdfObject );
if !_dd {return _c .New ("\u0075\u006e\u0065\u0078p\u0065\u0063\u0074\u0065\u0064\u0020\u0061\u006e\u006e\u006ft\u0027s\u0020\u0073\u0074\u0072\u0075\u0063\u0074u\u0072\u0065");};_ebc [_ade .ObjectNumber ]=_ge ;};for _ ,_dcc :=range _ab {_ag ,_dba :=_ce .GetIndirect (_dcc );
if !_dba {return _c .New ("\u0075\u006e\u0065\u0078p\u0065\u0063\u0074\u0065\u0064\u0020\u0066\u0069\u0065\u006cd\u0027s\u0020\u0073\u0074\u0072\u0075\u0063\u0074u\u0072\u0065");};_fef ,_dba :=_ce .GetDict (_ag .PdfObject );if !_dba {return _c .New ("\u0075\u006e\u0065\u0078p\u0065\u0063\u0074\u0065\u0064\u0020\u0066\u0069\u0065\u006cd\u0027s\u0020\u0073\u0074\u0072\u0075\u0063\u0074u\u0072\u0065");
};T :=_fef .Get ("\u0054");if _ ,_agc :=_fec ._ec [_ag .ObjectNumber ];_agc {switch _fec ._d {case NoRestrictions ,FillForms ,FillFormsAndAnnots :_fec ._f .addWarningWithDescription (_gd ,_cd .Sprintf ("F\u0069e\u006c\u0064\u0020\u0025\u0073\u0020\u0077\u0061s\u0020\u0063\u0068\u0061ng\u0065\u0064",T ));
default:_fec ._f .addErrorWithDescription (_gd ,_cd .Sprintf ("F\u0069e\u006c\u0064\u0020\u0025\u0073\u0020\u0077\u0061s\u0020\u0063\u0068\u0061ng\u0065\u0064",T ));};};if _ ,_gb :=_ebc [_ag .ObjectNumber ];!_gb {switch _fec ._d {case NoRestrictions ,FillForms ,FillFormsAndAnnots :_fec ._f .addWarningWithDescription (_gd ,_cd .Sprintf ("\u0046i\u0065l\u0064\u0020\u0025\u0073\u0020w\u0061\u0073 \u0061\u0064\u0064\u0065\u0064",_fef .Get ("\u0054")));
default:_fec ._f .addErrorWithDescription (_gd ,_cd .Sprintf ("\u0046i\u0065l\u0064\u0020\u0025\u0073\u0020w\u0061\u0073 \u0061\u0064\u0064\u0065\u0064",_fef .Get ("\u0054")));};}else {delete (_ebc ,_ag .ObjectNumber );if _ ,_gf :=_fec ._ec [_ag .ObjectNumber ];
_gf {switch _fec ._d {case NoRestrictions ,FillForms ,FillFormsAndAnnots :_fec ._f .addWarningWithDescription (_gd ,_cd .Sprintf ("F\u0069e\u006c\u0064\u0020\u0025\u0073\u0020\u0077\u0061s\u0020\u0063\u0068\u0061ng\u0065\u0064",_fef .Get ("\u0054")));default:_fec ._f .addErrorWithDescription (_gd ,_cd .Sprintf ("F\u0069e\u006c\u0064\u0020\u0025\u0073\u0020\u0077\u0061s\u0020\u0063\u0068\u0061ng\u0065\u0064",_fef .Get ("\u0054")));
};};};if FT ,_ga :=_ce .GetNameVal (_fef .Get ("\u0046\u0054"));_ga {if FT =="\u0053\u0069\u0067"{if _dad ,_cbg :=_ce .GetIndirect (_fef .Get ("\u0056"));_cbg {if _ ,_egd :=_fec ._ec [_dad .ObjectNumber ];_egd {switch _fec ._d {case NoRestrictions ,FillForms ,FillFormsAndAnnots :_fec ._f .addWarningWithDescription (_gd ,_cd .Sprintf ("\u0053\u0069\u0067na\u0074\u0075\u0072\u0065\u0020\u0066\u006f\u0072\u0020%\u0073 \u0066i\u0065l\u0064\u0020\u0077\u0061\u0073\u0020\u0063\u0068\u0061\u006e\u0067\u0065\u0064",T ));
default:_fec ._f .addErrorWithDescription (_gd ,_cd .Sprintf ("\u0053\u0069\u0067na\u0074\u0075\u0072\u0065\u0020\u0066\u006f\u0072\u0020%\u0073 \u0066i\u0065l\u0064\u0020\u0077\u0061\u0073\u0020\u0063\u0068\u0061\u006e\u0067\u0065\u0064",T ));};};};};
};};for _ ,_edd :=range _ebc {switch _fec ._d {case NoRestrictions :_fec ._f .addWarningWithDescription (_gd ,_cd .Sprintf ("F\u0069e\u006c\u0064\u0020\u0025\u0073\u0020\u0077\u0061s\u0020\u0072\u0065\u006dov\u0065\u0064",_edd .Get ("\u0054")));default:_fec ._f .addErrorWithDescription (_gd ,_cd .Sprintf ("F\u0069e\u006c\u0064\u0020\u0025\u0073\u0020\u0077\u0061s\u0020\u0072\u0065\u006dov\u0065\u0064",_edd .Get ("\u0054")));
};};return nil ;};type defaultDiffPolicy struct{_ec map[int64 ]_ce .PdfObject ;_f *DiffResults ;_d DocMDPPermission ;};func (_de *defaultDiffPolicy )compareAnnots (_ba int ,_fea ,_abe []_ce .PdfObject )error {_dfg :=make (map[int64 ]*_ce .PdfObjectDictionary );
for _ ,_dbc :=range _fea {_cef ,_beg :=_ce .GetIndirect (_dbc );if !_beg {return _c .New ("\u0075\u006e\u0065\u0078p\u0065\u0063\u0074\u0065\u0064\u0020\u0061\u006e\u006e\u006ft\u0027s\u0020\u0073\u0074\u0072\u0075\u0063\u0074u\u0072\u0065");};_deb ,_beg :=_ce .GetDict (_cef .PdfObject );
if !_beg {return _c .New ("\u0075\u006e\u0065\u0078p\u0065\u0063\u0074\u0065\u0064\u0020\u0061\u006e\u006e\u006ft\u0027s\u0020\u0073\u0074\u0072\u0075\u0063\u0074u\u0072\u0065");};_dfg [_cef .ObjectNumber ]=_deb ;};for _ ,_ced :=range _abe {_beb ,_egg :=_ce .GetIndirect (_ced );
if !_egg {return _c .New ("\u0075\u006e\u0065\u0078p\u0065\u0063\u0074\u0065\u0064\u0020\u0061\u006e\u006e\u006ft\u0027s\u0020\u0073\u0074\u0072\u0075\u0063\u0074u\u0072\u0065");};_faf ,_egg :=_ce .GetDict (_beb .PdfObject );if !_egg {return _c .New ("\u0075\u006e\u0065\u0078p\u0065\u0063\u0074\u0065\u0064\u0020\u0061\u006e\u006e\u006ft\u0027s\u0020\u0073\u0074\u0072\u0075\u0063\u0074u\u0072\u0065");
};_aa ,_ :=_ce .GetStringVal (_faf .Get ("\u0054"));_fag ,_ :=_ce .GetNameVal (_faf .Get ("\u0053u\u0062\u0074\u0079\u0070\u0065"));if _ ,_fefc :=_dfg [_beb .ObjectNumber ];!_fefc {switch _de ._d {case NoRestrictions ,FillFormsAndAnnots :_de ._f .addWarningWithDescription (_ba ,_cd .Sprintf ("\u0025\u0073\u0020\u0061\u006e\u006e\u006f\u0074\u0061\u0074\u0069o\u006e\u0020\u0025\u0073\u0020\u0077\u0061\u0073\u0020\u0061d\u0064\u0065\u0064",_fag ,_aa ));
default:_bcg ,_gaa :=_ce .GetDict (_beb .PdfObject );if !_gaa {return _c .New ("u\u006ed\u0065\u0066\u0069\u006e\u0065\u0064\u0020\u0061n\u006e\u006f\u0074\u0061ti\u006f\u006e");};_acf ,_gaa :=_ce .GetNameVal (_bcg .Get ("\u0053u\u0062\u0074\u0079\u0070\u0065"));
if !_gaa {return _c .New ("\u0075\u006e\u0064\u0065\u0066\u0069\u006e\u0065\u0064\u0020a\u006e\u006e\u006f\u0074\u0061\u0074\u0069o\u006e\u0027\u0073\u0020\u0073\u0075\u0062\u0074\u0079\u0070\u0065");};if _acf =="\u0057\u0069\u0064\u0067\u0065\u0074"{switch _de ._d {case NoRestrictions ,FillFormsAndAnnots ,FillForms :_de ._f .addWarningWithDescription (_ba ,_cd .Sprintf ("\u0025\u0073\u0020\u0061\u006e\u006e\u006f\u0074\u0061\u0074\u0069o\u006e\u0020\u0025\u0073\u0020\u0077\u0061\u0073\u0020\u0061d\u0064\u0065\u0064",_fag ,_aa ));
default:_de ._f .addErrorWithDescription (_ba ,_cd .Sprintf ("\u0025\u0073\u0020\u0061\u006e\u006e\u006f\u0074\u0061\u0074\u0069o\u006e\u0020\u0025\u0073\u0020\u0077\u0061\u0073\u0020\u0061d\u0064\u0065\u0064",_fag ,_aa ));};}else {_de ._f .addErrorWithDescription (_ba ,_cd .Sprintf ("\u0025\u0073\u0020\u0061\u006e\u006e\u006f\u0074\u0061\u0074\u0069o\u006e\u0020\u0025\u0073\u0020\u0077\u0061\u0073\u0020\u0061d\u0064\u0065\u0064",_fag ,_aa ));
};};}else {delete (_dfg ,_beb .ObjectNumber );if _aaa ,_bcc :=_de ._ec [_beb .ObjectNumber ];_bcc {switch _de ._d {case NoRestrictions ,FillFormsAndAnnots :_de ._f .addWarningWithDescription (_ba ,_cd .Sprintf ("\u0025\u0073\u0020\u0061n\u006e\u006f\u0074\u0061\u0074\u0069\u006f\u006e\u0020\u0025s\u0020w\u0061\u0073\u0020\u0063\u0068\u0061\u006eg\u0065\u0064",_fag ,_aa ));
default:_fgca ,_gc :=_ce .GetIndirect (_aaa );if !_gc {return _c .New ("u\u006ed\u0065\u0066\u0069\u006e\u0065\u0064\u0020\u0061n\u006e\u006f\u0074\u0061ti\u006f\u006e");};_cbe ,_gc :=_ce .GetDict (_fgca .PdfObject );if !_gc {return _c .New ("u\u006ed\u0065\u0066\u0069\u006e\u0065\u0064\u0020\u0061n\u006e\u006f\u0074\u0061ti\u006f\u006e");
};_aaf ,_gc :=_ce .GetNameVal (_cbe .Get ("\u0053u\u0062\u0074\u0079\u0070\u0065"));if !_gc {return _c .New ("\u0075\u006e\u0064\u0065\u0066\u0069\u006e\u0065\u0064\u0020a\u006e\u006e\u006f\u0074\u0061\u0074\u0069o\u006e\u0027\u0073\u0020\u0073\u0075\u0062\u0074\u0079\u0070\u0065");
};if _aaf =="\u0057\u0069\u0064\u0067\u0065\u0074"{switch _de ._d {case NoRestrictions ,FillFormsAndAnnots ,FillForms :_de ._f .addWarningWithDescription (_ba ,_cd .Sprintf ("\u0025\u0073\u0020\u0061n\u006e\u006f\u0074\u0061\u0074\u0069\u006f\u006e\u0020\u0025s\u0020w\u0061\u0073\u0020\u0063\u0068\u0061\u006eg\u0065\u0064",_fag ,_aa ));
default:_de ._f .addErrorWithDescription (_ba ,_cd .Sprintf ("\u0025\u0073\u0020\u0061n\u006e\u006f\u0074\u0061\u0074\u0069\u006f\u006e\u0020\u0025s\u0020w\u0061\u0073\u0020\u0063\u0068\u0061\u006eg\u0065\u0064",_fag ,_aa ));};}else {_de ._f .addErrorWithDescription (_ba ,_cd .Sprintf ("\u0025\u0073\u0020\u0061n\u006e\u006f\u0074\u0061\u0074\u0069\u006f\u006e\u0020\u0025s\u0020w\u0061\u0073\u0020\u0063\u0068\u0061\u006eg\u0065\u0064",_fag ,_aa ));
};};};};};for _ ,_ffg :=range _dfg {_cbb ,_ :=_ce .GetStringVal (_ffg .Get ("\u0054"));_gfc ,_ :=_ce .GetNameVal (_ffg .Get ("\u0053u\u0062\u0074\u0079\u0070\u0065"));switch _de ._d {case NoRestrictions ,FillFormsAndAnnots :_de ._f .addWarningWithDescription (_ba ,_cd .Sprintf ("\u0025\u0073\u0020\u0061n\u006e\u006f\u0074\u0061\u0074\u0069\u006f\u006e\u0020\u0025s\u0020w\u0061\u0073\u0020\u0072\u0065\u006d\u006fv\u0065\u0064",_gfc ,_cbb ));
default:_de ._f .addErrorWithDescription (_ba ,_cd .Sprintf ("\u0025\u0073\u0020\u0061n\u006e\u006f\u0074\u0061\u0074\u0069\u006f\u006e\u0020\u0025s\u0020w\u0061\u0073\u0020\u0072\u0065\u006d\u006fv\u0065\u0064",_gfc ,_cbb ));};};return nil ;};

// ReviewFile implementation of DiffPolicy interface
// The default policy only checks the next types of objects:
// Page, Pages (container for page objects), Annot, Annots (container for annotation objects), Field.
// It checks adding, removing and modifying objects of these types.
func (_ca *defaultDiffPolicy )ReviewFile (oldParser *_ce .PdfParser ,newParser *_ce .PdfParser ,params *MDPParameters )(*DiffResults ,error ){if oldParser .GetRevisionNumber ()> newParser .GetRevisionNumber (){return nil ,_c .New ("\u006f\u006c\u0064\u0020\u0072\u0065\u0076\u0069\u0073\u0069\u006f\u006e\u0020\u0067\u0072\u0065\u0061\u0074\u0065\u0072\u0020\u0074\u0068\u0061n\u0020\u006e\u0065\u0077\u0020r\u0065\u0076i\u0073\u0069\u006f\u006e");
};if oldParser .GetRevisionNumber ()==newParser .GetRevisionNumber (){if oldParser !=newParser {return nil ,_c .New ("\u0073\u0061m\u0065\u0020\u0072\u0065v\u0069\u0073i\u006f\u006e\u0073\u002c\u0020\u0062\u0075\u0074 \u0064\u0069\u0066\u0066\u0065\u0072\u0065\u006e\u0074\u0020\u0070\u0061r\u0073\u0065\u0072\u0073");
};return &DiffResults {},nil ;};if params ==nil {_ca ._d =NoRestrictions ;}else {_ca ._d =params .DocMDPLevel ;};_cb :=&DiffResults {};for _ff :=oldParser .GetRevisionNumber ()+1;_ff <=newParser .GetRevisionNumber ();_ff ++{_cae ,_a :=newParser .GetRevision (_ff -1);
if _a !=nil {return nil ,_a ;};_db ,_a :=newParser .GetRevision (_ff );if _a !=nil {return nil ,_a ;};_ed ,_a :=_ca .compareRevisions (_cae ,_db );if _a !=nil {return nil ,_a ;};_cb .Warnings =append (_cb .Warnings ,_ed .Warnings ...);_cb .Errors =append (_cb .Errors ,_ed .Errors ...);
};return _cb ,nil ;};

// String returns the state of the warning.
func (_feg *DiffResult )String ()string {return _cd .Sprintf ("\u0025\u0073\u0020\u0069n \u0072\u0065\u0076\u0069\u0073\u0069\u006f\u006e\u0073\u0020\u0023\u0025\u0064",_feg .Description ,_feg .Revision );};func (_dgd *DiffResults )addError (_bec *DiffResult ){if _dgd .Errors ==nil {_dgd .Errors =make ([]*DiffResult ,0);
};_dgd .Errors =append (_dgd .Errors ,_bec );};

// DiffResult describes the warning or the error for the DiffPolicy results.
type DiffResult struct{Revision int ;Description string ;};

// DiffPolicy interface for comparing two revisions of the Pdf document.
type DiffPolicy interface{

// ReviewFile should check the revisions of the old and new parsers
// and evaluate the differences between the revisions.
// Each implementation of this interface must decide
// how to handle cases where there are multiple revisions between the old and new revisions.
ReviewFile (_ccf *_ce .PdfParser ,_cbgd *_ce .PdfParser ,_ecf *MDPParameters )(*DiffResults ,error );};