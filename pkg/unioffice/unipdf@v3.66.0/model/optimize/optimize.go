//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package optimize ;import (_c "bytes";_d "crypto/md5";_eb "errors";_ff "fmt";_g "github.com/unidoc/unipdf/v3/common";_de "github.com/unidoc/unipdf/v3/contentstream";_bc "github.com/unidoc/unipdf/v3/core";_ec "github.com/unidoc/unipdf/v3/extractor";_ab "github.com/unidoc/unipdf/v3/internal/imageutil";
_gb "github.com/unidoc/unipdf/v3/internal/textencoding";_cd "github.com/unidoc/unipdf/v3/model";_bd "github.com/unidoc/unitype";_b "golang.org/x/image/draw";_f "math";_a "strings";);

// Chain allows to use sequence of optimizers.
// It implements interface model.Optimizer.
type Chain struct{_fd []_cd .Optimizer };

// New creates a optimizers chain from options.
func New (options Options )*Chain {_dbgf :=new (Chain );if options .CleanFonts ||options .SubsetFonts {_dbgf .Append (&CleanFonts {Subset :options .SubsetFonts });};if options .CleanContentstream {_dbgf .Append (new (CleanContentstream ));};if options .ImageUpperPPI > 0{_cfbg :=new (ImagePPI );
_cfbg .ImageUpperPPI =options .ImageUpperPPI ;_dbgf .Append (_cfbg );};if options .ImageQuality > 0{_efee :=new (Image );_efee .ImageQuality =options .ImageQuality ;_dbgf .Append (_efee );};if options .CombineDuplicateDirectObjects {_dbgf .Append (new (CombineDuplicateDirectObjects ));
};if options .CombineDuplicateStreams {_dbgf .Append (new (CombineDuplicateStreams ));};if options .CombineIdenticalIndirectObjects {_dbgf .Append (new (CombineIdenticalIndirectObjects ));};if options .UseObjectStreams {_dbgf .Append (new (ObjectStreams ));
};if options .CompressStreams {_dbgf .Append (new (CompressStreams ));};if options .CleanUnusedResources {_dbgf .Append (new (CleanUnusedResources ));};return _dbgf ;};

// Optimize implements Optimizer interface.
func (_ed *CleanUnusedResources )Optimize (objects []_bc .PdfObject )(_fgfe []_bc .PdfObject ,_ccc error ){_dfe ,_ccc :=_cfbd (objects );if _ccc !=nil {return nil ,_ccc ;};_bbd :=[]_bc .PdfObject {};for _ ,_gfgg :=range objects {_ ,_ecd :=_dfe [_gfgg ];
if _ecd {continue ;};_bbd =append (_bbd ,_gfgg );};return _bbd ,nil ;};

// CombineDuplicateDirectObjects combines duplicated direct objects by its data hash.
// It implements interface model.Optimizer.
type CombineDuplicateDirectObjects struct{};func _acbe (_cgac _bc .PdfObject ,_fcc map[_bc .PdfObject ]struct{})error {if _geec ,_cba :=_cgac .(*_bc .PdfIndirectObject );_cba {_fcc [_cgac ]=struct{}{};_bea :=_acbe (_geec .PdfObject ,_fcc );if _bea !=nil {return _bea ;
};return nil ;};if _dgbf ,_aegc :=_cgac .(*_bc .PdfObjectStream );_aegc {_fcc [_dgbf ]=struct{}{};_bgc :=_acbe (_dgbf .PdfObjectDictionary ,_fcc );if _bgc !=nil {return _bgc ;};return nil ;};if _fcgg ,_ffga :=_cgac .(*_bc .PdfObjectDictionary );_ffga {for _ ,_agf :=range _fcgg .Keys (){_fefgf :=_fcgg .Get (_agf );
_ =_fefgf ;if _dcee ,_ced :=_fefgf .(*_bc .PdfObjectReference );_ced {_fefgf =_dcee .Resolve ();_fcgg .Set (_agf ,_fefgf );};if _agf !="\u0050\u0061\u0072\u0065\u006e\u0074"{if _ggdf :=_acbe (_fefgf ,_fcc );_ggdf !=nil {return _ggdf ;};};};return nil ;
};if _dgd ,_acga :=_cgac .(*_bc .PdfObjectArray );_acga {if _dgd ==nil {return _eb .New ("\u0061\u0072\u0072a\u0079\u0020\u0069\u0073\u0020\u006e\u0069\u006c");};for _bgg ,_fec :=range _dgd .Elements (){if _edb ,_egfg :=_fec .(*_bc .PdfObjectReference );
_egfg {_fec =_edb .Resolve ();_dgd .Set (_bgg ,_fec );};if _dddae :=_acbe (_fec ,_fcc );_dddae !=nil {return _dddae ;};};return nil ;};return nil ;};func _gabdc (_daed *_bc .PdfObjectDictionary )[]string {_aeec :=[]string {};for _ ,_bad :=range _daed .Keys (){_aeec =append (_aeec ,_bad .String ());
};return _aeec ;};

// ImagePPI optimizes images by scaling images such that the PPI (pixels per inch) is never higher than ImageUpperPPI.
// TODO(a5i): Add support for inline images.
// It implements interface model.Optimizer.
type ImagePPI struct{ImageUpperPPI float64 ;};

// CleanFonts cleans up embedded fonts, reducing font sizes.
type CleanFonts struct{

// Subset embedded fonts if encountered (if true).
// Otherwise attempts to reduce the font program.
Subset bool ;};

// Optimize optimizes PDF objects to decrease PDF size.
func (_acffd *ImagePPI )Optimize (objects []_bc .PdfObject )(_bbaaa []_bc .PdfObject ,_eccee error ){if _acffd .ImageUpperPPI <=0{return objects ,nil ;};_decc :=_cab (objects );if len (_decc )==0{return objects ,nil ;};_gddad :=make (map[_bc .PdfObject ]struct{});
for _ ,_dbg :=range _decc {_afb :=_dbg .Stream .PdfObjectDictionary .Get ("\u0053\u004d\u0061s\u006b");_gddad [_afb ]=struct{}{};};_ceg :=make (map[*_bc .PdfObjectStream ]*imageInfo );for _ ,_ffe :=range _decc {_ceg [_ffe .Stream ]=_ffe ;};var _afe *_bc .PdfObjectDictionary ;
for _ ,_eaec :=range objects {if _ccbg ,_acfff :=_bc .GetDict (_eaec );_afe ==nil &&_acfff {if _ecbc ,_bacc :=_bc .GetName (_ccbg .Get ("\u0054\u0079\u0070\u0065"));_bacc &&*_ecbc =="\u0043a\u0074\u0061\u006c\u006f\u0067"{_afe =_ccbg ;};};};if _afe ==nil {return objects ,nil ;
};_cacg ,_cffc :=_bc .GetDict (_afe .Get ("\u0050\u0061\u0067e\u0073"));if !_cffc {return objects ,nil ;};_geea ,_gbcb :=_bc .GetArray (_cacg .Get ("\u004b\u0069\u0064\u0073"));if !_gbcb {return objects ,nil ;};for _ ,_ebgb :=range _geea .Elements (){_debf :=make (map[string ]*imageInfo );
_aeecf ,_daac :=_bc .GetDict (_ebgb );if !_daac {continue ;};_bbagb ,_ :=_fffc (_aeecf .Get ("\u0043\u006f\u006e\u0074\u0065\u006e\u0074\u0073"));if len (_bbagb )==0{continue ;};_dfcb ,_bcbd :=_bc .GetDict (_aeecf .Get ("\u0052e\u0073\u006f\u0075\u0072\u0063\u0065s"));
if !_bcbd {continue ;};_daae ,_dagb :=_cd .NewPdfPageResourcesFromDict (_dfcb );if _dagb !=nil {_g .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u0020\u0070\u0061\u0072\u0073\u0069\u006e\u0067\u0020\u0072\u0065\u0073\u006f\u0075\u0072\u0063\u0065\u0073\u0020-\u0020\u0069\u0067\u006e\u006fr\u0069\u006eg\u003a\u0020\u0025\u0076",_dagb );
continue ;};_ggfd ,_eee :=_bc .GetDict (_dfcb .Get ("\u0058O\u0062\u006a\u0065\u0063\u0074"));if !_eee {continue ;};_eaee :=_ggfd .Keys ();for _ ,_geb :=range _eaee {if _dbbc ,_gbec :=_bc .GetStream (_ggfd .Get (_geb ));_gbec {if _adegf ,_dff :=_ceg [_dbbc ];
_dff {_debf [string (_geb )]=_adegf ;};};};_ecgc :=_de .NewContentStreamParser (_bbagb );_eggb ,_dagb :=_ecgc .Parse ();if _dagb !=nil {_g .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u0025\u002b\u0076",_dagb );continue ;};_bggf :=_de .NewContentStreamProcessor (*_eggb );
_bggf .AddHandler (_de .HandlerConditionEnumAllOperands ,"",func (_dfbc *_de .ContentStreamOperation ,_bcce _de .GraphicsState ,_dafa *_cd .PdfPageResources )error {switch _dfbc .Operand {case "\u0044\u006f":if len (_dfbc .Params )!=1{_g .Log .Debug ("E\u0052\u0052\u004f\u0052\u003a\u0020\u0049\u0067\u006e\u006f\u0072\u0069\u006e\u0067\u0020\u0044\u006f\u0020w\u0069\u0074\u0068\u0020\u006c\u0065\u006e\u0028\u0070\u0061ra\u006d\u0073\u0029 \u0021=\u0020\u0031");
return nil ;};_bbff ,_dcgg :=_bc .GetName (_dfbc .Params [0]);if !_dcgg {_g .Log .Debug ("\u0045\u0052\u0052O\u0052\u003a\u0020\u0049\u0067\u006e\u006f\u0072\u0069\u006e\u0067\u0020\u0044\u006f\u0020\u0077\u0069\u0074\u0068\u0020\u006e\u006f\u006e\u0020\u004e\u0061\u006d\u0065\u0020p\u0061\u0072\u0061\u006d\u0065\u0074\u0065\u0072");
return nil ;};if _dfcg ,_eff :=_debf [string (*_bbff )];_eff {_cdfe :=_bcce .CTM .ScalingFactorX ();_cded :=_bcce .CTM .ScalingFactorY ();_efbe ,_aad :=_cdfe /72.0,_cded /72.0;_abcf ,_baaa :=float64 (_dfcg .Width )/_efbe ,float64 (_dfcg .Height )/_aad ;
if _efbe ==0||_aad ==0{_abcf =72.0;_baaa =72.0;};_dfcg .PPI =_f .Max (_dfcg .PPI ,_abcf );_dfcg .PPI =_f .Max (_dfcg .PPI ,_baaa );};};return nil ;});_dagb =_bggf .Process (_daae );if _dagb !=nil {_g .Log .Debug ("E\u0052\u0052\u004f\u0052 p\u0072o\u0063\u0065\u0073\u0073\u0069n\u0067\u003a\u0020\u0025\u002b\u0076",_dagb );
continue ;};};for _ ,_bbca :=range _decc {if _ ,_beb :=_gddad [_bbca .Stream ];_beb {continue ;};if _bbca .PPI <=_acffd .ImageUpperPPI {continue ;};_faf ,_cabc :=_cd .NewXObjectImageFromStream (_bbca .Stream );if _cabc !=nil {_g .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u0025\u002b\u0076",_cabc );
continue ;};var _ebb imageModifications ;_ebb .Scale =_acffd .ImageUpperPPI /_bbca .PPI ;if _bbca .BitsPerComponent ==1&&_bbca .ColorComponents ==1{_fgg :=_f .Round (_bbca .PPI /_acffd .ImageUpperPPI );_beff :=_ab .NextPowerOf2 (uint (_fgg ));if _ab .InDelta (float64 (_beff ),1/_ebb .Scale ,0.3){_ebb .Scale =float64 (1)/float64 (_beff );
};if _ ,_gabe :=_faf .Filter .(*_bc .JBIG2Encoder );!_gabe {_ebb .Encoding =_bc .NewJBIG2Encoder ();};};if _cabc =_egaf (_faf ,_ebb );_cabc !=nil {_g .Log .Debug ("\u0045\u0072\u0072\u006f\u0072 \u0073\u0063\u0061\u006c\u0065\u0020\u0069\u006d\u0061\u0067\u0065\u0020\u006be\u0065\u0070\u0020\u006f\u0072\u0069\u0067\u0069\u006e\u0061\u006c\u0020\u0069\u006d\u0061\u0067\u0065\u003a\u0020\u0025\u0073",_cabc );
continue ;};_ebb .Encoding =nil ;if _abe ,_cbb :=_bc .GetStream (_bbca .Stream .PdfObjectDictionary .Get ("\u0053\u004d\u0061s\u006b"));_cbb {_dbfea ,_gfc :=_cd .NewXObjectImageFromStream (_abe );if _gfc !=nil {_g .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u0025\u002b\u0076",_gfc );
continue ;};if _gfc =_egaf (_dbfea ,_ebb );_gfc !=nil {_g .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u0025\u002b\u0076",_gfc );continue ;};};};return objects ,nil ;};func _adc (_eba _bc .PdfObject )(string ,error ){_cef :=_bc .TraceToDirectObject (_eba );
switch _dbd :=_cef .(type ){case *_bc .PdfObjectString :return _dbd .Str (),nil ;case *_bc .PdfObjectStream :_bdf ,_bccb :=_bc .DecodeStream (_dbd );if _bccb !=nil {return "",_bccb ;};return string (_bdf ),nil ;};return "",_ff .Errorf ("\u0069\u006e\u0076\u0061\u006ci\u0064\u0020\u0063\u006f\u006e\u0074\u0065\u006e\u0074\u0020\u0073\u0074\u0072e\u0061\u006d\u0020\u006f\u0062\u006a\u0065\u0063\u0074\u0020\u0068\u006f\u006c\u0064\u0065\u0072\u0020\u0028\u0025\u0054\u0029",_cef );
};func _fffc (_edg _bc .PdfObject )(_afee string ,_effa []_bc .PdfObject ){var _caeb _c .Buffer ;switch _ccef :=_edg .(type ){case *_bc .PdfIndirectObject :_effa =append (_effa ,_ccef );_edg =_ccef .PdfObject ;};switch _cda :=_edg .(type ){case *_bc .PdfObjectStream :if _dcb ,_adef :=_bc .DecodeStream (_cda );
_adef ==nil {_caeb .Write (_dcb );_effa =append (_effa ,_cda );};case *_bc .PdfObjectArray :for _ ,_dceg :=range _cda .Elements (){switch _dgf :=_dceg .(type ){case *_bc .PdfObjectStream :if _dbdd ,_afa :=_bc .DecodeStream (_dgf );_afa ==nil {_caeb .Write (_dbdd );
_effa =append (_effa ,_dgf );};};};};return _caeb .String (),_effa ;};

// Options describes PDF optimization parameters.
type Options struct{CombineDuplicateStreams bool ;CombineDuplicateDirectObjects bool ;ImageUpperPPI float64 ;ImageQuality int ;UseObjectStreams bool ;CombineIdenticalIndirectObjects bool ;CompressStreams bool ;CleanFonts bool ;SubsetFonts bool ;CleanContentstream bool ;
CleanUnusedResources bool ;};

// Optimize optimizes PDF objects to decrease PDF size.
func (_cff *CombineDuplicateDirectObjects )Optimize (objects []_bc .PdfObject )(_egc []_bc .PdfObject ,_bda error ){_decb (objects );_cca :=make (map[string ][]*_bc .PdfObjectDictionary );var _fgb func (_ffcaa *_bc .PdfObjectDictionary );_fgb =func (_eca *_bc .PdfObjectDictionary ){for _ ,_eed :=range _eca .Keys (){_efb :=_eca .Get (_eed );
if _fcgb ,_bgcd :=_efb .(*_bc .PdfObjectDictionary );_bgcd {if _ddfd :=_fcgb .Keys ();len (_ddfd )==0{continue ;};_eebg :=_d .New ();_eebg .Write ([]byte (_fcgb .WriteString ()));_beg :=string (_eebg .Sum (nil ));_cca [_beg ]=append (_cca [_beg ],_fcgb );
_fgb (_fcgb );};};};for _ ,_cge :=range objects {_gaad ,_ccaf :=_cge .(*_bc .PdfIndirectObject );if !_ccaf {continue ;};if _cdde ,_fbe :=_gaad .PdfObject .(*_bc .PdfObjectDictionary );_fbe {_fgb (_cdde );};};_dgda :=make ([]_bc .PdfObject ,0,len (_cca ));
_dbee :=make (map[_bc .PdfObject ]_bc .PdfObject );for _ ,_ceeee :=range _cca {if len (_ceeee )< 2{continue ;};_gbc :=_bc .MakeDict ();_gbc .Merge (_ceeee [0]);_eaa :=_bc .MakeIndirectObject (_gbc );_dgda =append (_dgda ,_eaa );for _fceg :=0;_fceg < len (_ceeee );
_fceg ++{_faa :=_ceeee [_fceg ];_dbee [_faa ]=_eaa ;};};_egc =make ([]_bc .PdfObject ,len (objects ));copy (_egc ,objects );_egc =append (_dgda ,_egc ...);_gga (_egc ,_dbee );return _egc ,nil ;};

// GetOptimizers gets the list of optimizers in chain `c`.
func (_gg *Chain )GetOptimizers ()[]_cd .Optimizer {return _gg ._fd };func _gc (_aac *_bc .PdfObjectStream )error {_abb ,_fe :=_bc .DecodeStream (_aac );if _fe !=nil {return _fe ;};_feb :=_de .NewContentStreamParser (string (_abb ));_aag ,_fe :=_feb .Parse ();
if _fe !=nil {return _fe ;};_aag =_ffc (_aag );_fb :=_aag .Bytes ();if len (_fb )>=len (_abb ){return nil ;};_ca ,_fe :=_bc .MakeStream (_aag .Bytes (),_bc .NewFlateEncoder ());if _fe !=nil {return _fe ;};_aac .Stream =_ca .Stream ;_aac .Merge (_ca .PdfObjectDictionary );
return nil ;};func _deede (_ecbcb []_bc .PdfObject )objectStructure {_debge :=objectStructure {};_fee :=false ;for _ ,_dbcf :=range _ecbcb {switch _bdgc :=_dbcf .(type ){case *_bc .PdfIndirectObject :_cgcdd ,_fegf :=_bc .GetDict (_bdgc );if !_fegf {continue ;
};_eagd ,_fegf :=_bc .GetName (_cgcdd .Get ("\u0054\u0079\u0070\u0065"));if !_fegf {continue ;};switch _eagd .String (){case "\u0043a\u0074\u0061\u006c\u006f\u0067":_debge ._ebbf =_cgcdd ;_fee =true ;};};if _fee {break ;};};if !_fee {return _debge ;};_ebadf ,_adfc :=_bc .GetDict (_debge ._ebbf .Get ("\u0050\u0061\u0067e\u0073"));
if !_adfc {return _debge ;};_debge ._gde =_ebadf ;_abcg ,_adfc :=_bc .GetArray (_ebadf .Get ("\u004b\u0069\u0064\u0073"));if !_adfc {return _debge ;};for _ ,_ege :=range _abcg .Elements (){_dded ,_ffbf :=_bc .GetIndirect (_ege );if !_ffbf {break ;};_debge ._gce =append (_debge ._gce ,_dded );
};return _debge ;};

// Append appends optimizers to the chain.
func (_ad *Chain )Append (optimizers ..._cd .Optimizer ){_ad ._fd =append (_ad ._fd ,optimizers ...)};type content struct{_egf string ;_ddf *_cd .PdfPageResources ;};func _ffc (_cgf *_de .ContentStreamOperations )*_de .ContentStreamOperations {if _cgf ==nil {return nil ;
};_bde :=_de .ContentStreamOperations {};for _ ,_cde :=range *_cgf {switch _cde .Operand {case "\u0042\u0044\u0043","\u0042\u004d\u0043","\u0045\u004d\u0043":continue ;case "\u0054\u006d":if len (_cde .Params )==6{if _ebd ,_bcb :=_bc .GetNumbersAsFloat (_cde .Params );
_bcb ==nil {if _ebd [0]==1&&_ebd [1]==0&&_ebd [2]==0&&_ebd [3]==1{_cde =&_de .ContentStreamOperation {Params :[]_bc .PdfObject {_cde .Params [4],_cde .Params [5]},Operand :"\u0054\u0064"};};};};};_bde =append (_bde ,_cde );};return &_bde ;};

// Optimize optimizes PDF objects to decrease PDF size.
func (_debg *CombineIdenticalIndirectObjects )Optimize (objects []_bc .PdfObject )(_ggb []_bc .PdfObject ,_bac error ){_decb (objects );_ecce :=make (map[_bc .PdfObject ]_bc .PdfObject );_gdba :=make (map[_bc .PdfObject ]struct{});_bcbb :=make (map[string ][]*_bc .PdfIndirectObject );
for _ ,_cdc :=range objects {_fdcb ,_egge :=_cdc .(*_bc .PdfIndirectObject );if !_egge {continue ;};if _bbg ,_gdg :=_fdcb .PdfObject .(*_bc .PdfObjectDictionary );_gdg {if _ceaa ,_dbec :=_bbg .Get ("\u0054\u0079\u0070\u0065").(*_bc .PdfObjectName );_dbec &&*_ceaa =="\u0050\u0061\u0067\u0065"{continue ;
};if _fcfdd :=_bbg .Keys ();len (_fcfdd )==0{continue ;};_cdba :=_d .New ();_cdba .Write ([]byte (_bbg .WriteString ()));_bggd :=string (_cdba .Sum (nil ));_bcbb [_bggd ]=append (_bcbb [_bggd ],_fdcb );};};for _ ,_bcbba :=range _bcbb {if len (_bcbba )< 2{continue ;
};_bdad :=_bcbba [0];for _aedc :=1;_aedc < len (_bcbba );_aedc ++{_bfg :=_bcbba [_aedc ];_ecce [_bfg ]=_bdad ;_gdba [_bfg ]=struct{}{};};};_ggb =make ([]_bc .PdfObject ,0,len (objects )-len (_gdba ));for _ ,_fga :=range objects {if _ ,_dbc :=_gdba [_fga ];
_dbc {continue ;};_ggb =append (_ggb ,_fga );};_gga (_ggb ,_ecce );return _ggb ,nil ;};func _egaf (_fdab *_cd .XObjectImage ,_cgff imageModifications )error {_gfga ,_dbfg :=_fdab .ToImage ();if _dbfg !=nil {return _dbfg ;};if _cgff .Scale !=0{_gfga ,_dbfg =_beee (_gfga ,_cgff .Scale );
if _dbfg !=nil {return _dbfg ;};};if _cgff .Encoding !=nil {_fdab .Filter =_cgff .Encoding ;};_fdab .Decode =nil ;switch _dbbe :=_fdab .Filter .(type ){case *_bc .FlateEncoder :if _dbbe .Predictor !=1&&_dbbe .Predictor !=11{_dbbe .Predictor =1;};};if _dbfg =_fdab .SetImage (_gfga ,nil );
_dbfg !=nil {_g .Log .Debug ("\u0045\u0072\u0072or\u0020\u0073\u0065\u0074\u0074\u0069\u006e\u0067\u0020\u0069\u006d\u0061\u0067\u0065\u003a\u0020\u0025\u0076",_dbfg );return _dbfg ;};_fdab .ToPdfObject ();return nil ;};

// Optimize optimizes PDF objects to decrease PDF size.
func (_fgc *CleanFonts )Optimize (objects []_bc .PdfObject )(_aga []_bc .PdfObject ,_efe error ){var _gee map[*_bc .PdfObjectStream ]struct{};if _fgc .Subset {var _acg error ;_gee ,_acg =_cfb (objects );if _acg !=nil {_g .Log .Debug ("\u0045\u0052\u0052\u004fR\u003a\u0020\u0046\u0061\u0069\u006c\u0065\u0064\u0020\u0073u\u0062s\u0065\u0074\u0074\u0069\u006e\u0067\u003a \u0025\u0076",_acg );
return nil ,_acg ;};};for _ ,_fbga :=range objects {_agc ,_gffg :=_bc .GetStream (_fbga );if !_gffg {continue ;};if _ ,_dc :=_gee [_agc ];_dc {continue ;};_ged ,_gd :=_bc .NewEncoderFromStream (_agc );if _gd !=nil {_g .Log .Debug ("\u0045\u0052RO\u0052\u0020\u0067e\u0074\u0074\u0069\u006eg e\u006eco\u0064\u0065\u0072\u003a\u0020\u0025\u0076 -\u0020\u0069\u0067\u006e\u006f\u0072\u0069n\u0067",_gd );
continue ;};_acdc ,_gd :=_ged .DecodeStream (_agc );if _gd !=nil {_g .Log .Debug ("\u0044\u0065\u0063\u006f\u0064\u0069\u006e\u0067\u0020\u0065r\u0072\u006f\u0072\u0020\u003a\u0020\u0025v\u0020\u002d\u0020\u0069\u0067\u006e\u006f\u0072\u0069\u006e\u0067",_gd );
continue ;};if len (_acdc )< 4{continue ;};_dcf :=string (_acdc [:4]);if _dcf =="\u004f\u0054\u0054\u004f"{continue ;};if _dcf !="\u0000\u0001\u0000\u0000"&&_dcf !="\u0074\u0072\u0075\u0065"{continue ;};_efa ,_gd :=_bd .Parse (_c .NewReader (_acdc ));if _gd !=nil {_g .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u0020P\u0061\u0072\u0073\u0069\u006e\u0067\u0020\u0066\u006f\u006e\u0074\u003a\u0020%\u0076\u0020\u002d\u0020\u0069\u0067\u006eo\u0072\u0069\u006e\u0067",_gd );
continue ;};_gd =_efa .Optimize ();if _gd !=nil {_g .Log .Debug ("\u0045\u0052RO\u0052\u0020\u004fp\u0074\u0069\u006d\u0069zin\u0067 f\u006f\u006e\u0074\u003a\u0020\u0025\u0076 -\u0020\u0073\u006b\u0069\u0070\u0070\u0069n\u0067",_gd );continue ;};var _fefd _c .Buffer ;
_gd =_efa .Write (&_fefd );if _gd !=nil {_g .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u0020W\u0072\u0069\u0074\u0069\u006e\u0067\u0020\u0066\u006f\u006e\u0074\u003a\u0020%\u0076\u0020\u002d\u0020\u0069\u0067\u006eo\u0072\u0069\u006e\u0067",_gd );continue ;
};if _fefd .Len ()> len (_acdc ){_g .Log .Debug ("\u0052\u0065-\u0077\u0072\u0069\u0074\u0074\u0065\u006e\u0020\u0066\u006f\u006e\u0074\u0020\u0069\u0073\u0020\u006c\u0061\u0072\u0067\u0065\u0072\u0020\u0074\u0068\u0061\u006e\u0020\u006f\u0072\u0069\u0067\u0069\u006e\u0061\u006c\u0020\u002d\u0020\u0073\u006b\u0069\u0070");
continue ;};_fag ,_gd :=_bc .MakeStream (_fefd .Bytes (),_bc .NewFlateEncoder ());if _gd !=nil {continue ;};*_agc =*_fag ;_agc .Set ("\u004ce\u006e\u0067\u0074\u0068\u0031",_bc .MakeInteger (int64 (_fefd .Len ())));};return objects ,nil ;};

// CleanUnusedResources represents an optimizer used to clean unused resources.
type CleanUnusedResources struct{};func _cfb (_eag []_bc .PdfObject )(_gag map[*_bc .PdfObjectStream ]struct{},_bb error ){_gag =map[*_bc .PdfObjectStream ]struct{}{};_abc :=map[*_cd .PdfFont ]struct{}{};_fea :=_deede (_eag );for _ ,_cfc :=range _fea ._gce {_dg ,_ebe :=_bc .GetDict (_cfc .PdfObject );
if !_ebe {continue ;};_dbfe ,_ebe :=_bc .GetDict (_dg .Get ("\u0052e\u0073\u006f\u0075\u0072\u0063\u0065s"));if !_ebe {continue ;};_dfc ,_ :=_fffc (_dg .Get ("\u0043\u006f\u006e\u0074\u0065\u006e\u0074\u0073"));_fa ,_bff :=_cd .NewPdfPageResourcesFromDict (_dbfe );
if _bff !=nil {return nil ,_bff ;};_bbb :=[]content {{_egf :_dfc ,_ddf :_fa }};_adee :=_dgb (_dg .Get ("\u0041\u006e\u006e\u006f\u0074\u0073"));if _adee !=nil {_bbb =append (_bbb ,_adee ...);};for _ ,_aca :=range _bbb {_fbf ,_fab :=_ec .NewFromContents (_aca ._egf ,_aca ._ddf );
if _fab !=nil {return nil ,_fab ;};_fbc ,_ ,_ ,_fab :=_fbf .ExtractPageText ();if _fab !=nil {return nil ,_fab ;};for _ ,_cfg :=range _fbc .Marks ().Elements (){if _cfg .Font ==nil {continue ;};if _ ,_cdd :=_abc [_cfg .Font ];!_cdd {_abc [_cfg .Font ]=struct{}{};
};};};};_daf :=map[*_bc .PdfObjectStream ][]*_cd .PdfFont {};for _gea :=range _abc {_ecc :=_gea .FontDescriptor ();if _ecc ==nil ||_ecc .FontFile2 ==nil {continue ;};_fcg ,_fgf :=_bc .GetStream (_ecc .FontFile2 );if !_fgf {continue ;};_daf [_fcg ]=append (_daf [_fcg ],_gea );
};for _cc :=range _daf {var _daef []rune ;var _ee []_bd .GlyphIndex ;for _ ,_ffgc :=range _daf [_cc ]{switch _ag :=_ffgc .Encoder ().(type ){case *_gb .IdentityEncoder :_adeed :=_ag .RegisteredRunes ();_egd :=make ([]_bd .GlyphIndex ,len (_adeed ));for _gbb ,_dd :=range _adeed {_egd [_gbb ]=_bd .GlyphIndex (_dd );
};_ee =append (_ee ,_egd ...);case *_gb .TrueTypeFontEncoder :_gfe :=_ag .RegisteredRunes ();_daef =append (_daef ,_gfe ...);case _gb .SimpleEncoder :_eeb :=_ag .Charcodes ();for _ ,_gab :=range _eeb {_fce ,_dbfd :=_ag .CharcodeToRune (_gab );if !_dbfd {_g .Log .Debug ("\u0043\u0068a\u0072\u0063\u006f\u0064\u0065\u003c\u002d\u003e\u0072\u0075\u006e\u0065\u0020\u006e\u006f\u0074\u0020\u0066\u006f\u0075\u006e\u0064: \u0025\u0064",_gab );
continue ;};_daef =append (_daef ,_fce );};};};_bb =_bce (_cc ,_daef ,_ee );if _bb !=nil {_g .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u0020\u0073\u0075\u0062\u0073\u0065\u0074\u0074\u0069\u006eg\u0020f\u006f\u006e\u0074\u0020\u0073\u0074\u0072\u0065\u0061\u006d\u003a\u0020\u0025\u0076",_bb );
return nil ,_bb ;};_gag [_cc ]=struct{}{};};return _gag ,nil ;};

// Optimize optimizes PDF objects to decrease PDF size.
func (_bcbc *ObjectStreams )Optimize (objects []_bc .PdfObject )(_gca []_bc .PdfObject ,_abd error ){_aae :=&_bc .PdfObjectStreams {};_bfbf :=make ([]_bc .PdfObject ,0,len (objects ));for _ ,_dab :=range objects {if _ddeg ,_ebf :=_dab .(*_bc .PdfIndirectObject );
_ebf &&_ddeg .GenerationNumber ==0{_aae .Append (_dab );}else {_bfbf =append (_bfbf ,_dab );};};if _aae .Len ()==0{return _bfbf ,nil ;};_gca =make ([]_bc .PdfObject ,0,len (_bfbf )+_aae .Len ()+1);if _aae .Len ()> 1{_gca =append (_gca ,_aae );};_gca =append (_gca ,_aae .Elements ()...);
_gca =append (_gca ,_bfbf ...);return _gca ,nil ;};type objectStructure struct{_ebbf *_bc .PdfObjectDictionary ;_gde *_bc .PdfObjectDictionary ;_gce []*_bc .PdfIndirectObject ;};type imageInfo struct{BitsPerComponent int ;ColorComponents int ;Width int ;
Height int ;Stream *_bc .PdfObjectStream ;PPI float64 ;};func _beee (_ded *_cd .Image ,_adb float64 )(*_cd .Image ,error ){_cafc ,_agdc :=_ded .ToGoImage ();if _agdc !=nil {return nil ,_agdc ;};var _gedc _ab .Image ;_degf ,_gbfff :=_cafc .(*_ab .Monochrome );
if _gbfff {if _agdc =_degf .ResolveDecode ();_agdc !=nil {return nil ,_agdc ;};_gedc ,_agdc =_degf .Scale (_adb );if _agdc !=nil {return nil ,_agdc ;};}else {_geca :=int (_f .RoundToEven (float64 (_ded .Width )*_adb ));_ddfce :=int (_f .RoundToEven (float64 (_ded .Height )*_adb ));
_gedc ,_agdc =_ab .NewImage (_geca ,_ddfce ,int (_ded .BitsPerComponent ),_ded .ColorComponents ,nil ,nil ,nil );if _agdc !=nil {return nil ,_agdc ;};_b .CatmullRom .Scale (_gedc ,_gedc .Bounds (),_cafc ,_cafc .Bounds (),_b .Over ,&_b .Options {});};_dba :=_gedc .Base ();
_dbde :=&_cd .Image {Width :int64 (_dba .Width ),Height :int64 (_dba .Height ),BitsPerComponent :int64 (_dba .BitsPerComponent ),ColorComponents :_dba .ColorComponents ,Data :_dba .Data };_dbde .SetDecode (_dba .Decode );_dbde .SetAlpha (_dba .Alpha );
return _dbde ,nil ;};func _dgb (_acfa _bc .PdfObject )[]content {if _acfa ==nil {return nil ;};_ce ,_bbaf :=_bc .GetArray (_acfa );if !_bbaf {_g .Log .Debug ("\u0041\u006e\u006e\u006fts\u0020\u006e\u006f\u0074\u0020\u0061\u006e\u0020\u0061\u0072\u0072\u0061\u0079");
return nil ;};var _agd []content ;for _ ,_cbe :=range _ce .Elements (){_dgc ,_cbg :=_bc .GetDict (_cbe );if !_cbg {_g .Log .Debug ("I\u0067\u006e\u006f\u0072\u0069\u006eg\u0020\u006e\u006f\u006e\u002d\u0064i\u0063\u0074\u0020\u0065\u006c\u0065\u006de\u006e\u0074\u0020\u0069\u006e\u0020\u0041\u006e\u006e\u006ft\u0073");
continue ;};_gae ,_cbg :=_bc .GetDict (_dgc .Get ("\u0041\u0050"));if !_cbg {_g .Log .Debug ("\u004e\u006f\u0020\u0041P \u0065\u006e\u0074\u0072\u0079\u0020\u002d\u0020\u0073\u006b\u0069\u0070\u0070\u0069n\u0067");continue ;};_egg :=_bc .TraceToDirectObject (_gae .Get ("\u004e"));
if _egg ==nil {_g .Log .Debug ("N\u006f\u0020\u004e\u0020en\u0074r\u0079\u0020\u002d\u0020\u0073k\u0069\u0070\u0070\u0069\u006e\u0067");continue ;};var _cae *_bc .PdfObjectStream ;switch _ddfc :=_egg .(type ){case *_bc .PdfObjectDictionary :_gaa ,_dde :=_bc .GetName (_dgc .Get ("\u0041\u0053"));
if !_dde {_g .Log .Debug ("\u004e\u006f\u0020\u0041S \u0065\u006e\u0074\u0072\u0079\u0020\u002d\u0020\u0073\u006b\u0069\u0070\u0070\u0069n\u0067");continue ;};_cae ,_dde =_bc .GetStream (_ddfc .Get (*_gaa ));if !_dde {_g .Log .Debug ("\u0046o\u0072\u006d\u0020\u006eo\u0074\u0020\u0066\u006f\u0075n\u0064 \u002d \u0073\u006b\u0069\u0070\u0070\u0069\u006eg");
continue ;};case *_bc .PdfObjectStream :_cae =_ddfc ;};if _cae ==nil {_g .Log .Debug ("\u0046\u006f\u0072m\u0020\u006e\u006f\u0074 \u0066\u006f\u0075\u006e\u0064\u0020\u0028n\u0069\u006c\u0029\u0020\u002d\u0020\u0073\u006b\u0069\u0070\u0070\u0069\u006e\u0067");
continue ;};_gfec ,_aed :=_cd .NewXObjectFormFromStream (_cae );if _aed !=nil {_g .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020l\u006f\u0061\u0064\u0069\u006e\u0067\u0020\u0066\u006f\u0072\u006d\u003a\u0020%\u0076\u0020\u002d\u0020\u0069\u0067\u006eo\u0072\u0069\u006e\u0067",_aed );
continue ;};_dda ,_aed :=_gfec .GetContentStream ();if _aed !=nil {_g .Log .Debug ("E\u0072\u0072\u006f\u0072\u0020\u0064e\u0063\u006f\u0064\u0069\u006e\u0067\u0020\u0063\u006fn\u0074\u0065\u006et\u0073:\u0020\u0025\u0076",_aed );continue ;};_agd =append (_agd ,content {_egf :string (_dda ),_ddf :_gfec .Resources });
};return _agd ;};

// Optimize optimizes PDF objects to decrease PDF size.
func (_fda *Image )Optimize (objects []_bc .PdfObject )(_ebad []_bc .PdfObject ,_cfge error ){if _fda .ImageQuality <=0{return objects ,nil ;};_efab :=_cab (objects );if len (_efab )==0{return objects ,nil ;};_agg :=make (map[_bc .PdfObject ]_bc .PdfObject );
_cbgf :=make (map[_bc .PdfObject ]struct{});for _ ,_gbff :=range _efab {_ffff :=_gbff .Stream .Get ("\u0053\u004d\u0061s\u006b");_cbgf [_ffff ]=struct{}{};};for _cbad ,_aaa :=range _efab {_ffa :=_aaa .Stream ;if _ ,_fgbe :=_cbgf [_ffa ];_fgbe {continue ;
};_gdfg ,_eea :=_cd .NewXObjectImageFromStream (_ffa );if _eea !=nil {_g .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u0025\u002b\u0076",_eea );continue ;};switch _gdfg .Filter .(type ){case *_bc .JBIG2Encoder :continue ;case *_bc .CCITTFaxEncoder :continue ;
};_cfgec ,_eea :=_gdfg .ToImage ();if _eea !=nil {_g .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u0025\u002b\u0076",_eea );continue ;};_cdfc :=_bc .NewDCTEncoder ();_cdfc .ColorComponents =_cfgec .ColorComponents ;_cdfc .Quality =_fda .ImageQuality ;
_cdfc .BitsPerComponent =_aaa .BitsPerComponent ;_cdfc .Width =_aaa .Width ;_cdfc .Height =_aaa .Height ;_edcg ,_eea :=_cdfc .EncodeBytes (_cfgec .Data );if _eea !=nil {_g .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u0025\u002b\u0076",_eea );
continue ;};var _gaac _bc .StreamEncoder ;_gaac =_cdfc ;{_bfd :=_bc .NewFlateEncoder ();_dgg :=_bc .NewMultiEncoder ();_dgg .AddEncoder (_bfd );_dgg .AddEncoder (_cdfc );_gccf ,_cgg :=_dgg .EncodeBytes (_cfgec .Data );if _cgg !=nil {_g .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u0025\u002b\u0076",_cgg );
continue ;};if len (_gccf )< len (_edcg ){_g .Log .Trace ("\u004d\u0075\u006c\u0074\u0069\u0020\u0065\u006e\u0063\u0020\u0069\u006d\u0070\u0072\u006f\u0076\u0065\u0073\u003a\u0020\u0025\u0064\u0020\u0074o\u0020\u0025\u0064\u0020\u0028o\u0072\u0069g\u0020\u0025\u0064\u0029",len (_edcg ),len (_gccf ),len (_ffa .Stream ));
_edcg =_gccf ;_gaac =_dgg ;};};_bfgg :=len (_ffa .Stream );if _bfgg < len (_edcg ){continue ;};_fffg :=&_bc .PdfObjectStream {Stream :_edcg };_fffg .PdfObjectReference =_ffa .PdfObjectReference ;_fffg .PdfObjectDictionary =_bc .MakeDict ();_fffg .Merge (_ffa .PdfObjectDictionary );
_fffg .Merge (_gaac .MakeStreamDict ());_fffg .Set ("\u004c\u0065\u006e\u0067\u0074\u0068",_bc .MakeInteger (int64 (len (_edcg ))));_agg [_ffa ]=_fffg ;_efab [_cbad ].Stream =_fffg ;};_ebad =make ([]_bc .PdfObject ,len (objects ));copy (_ebad ,objects );
_gga (_ebad ,_agg );return _ebad ,nil ;};func _cfbd (_bcc []_bc .PdfObject )(map[_bc .PdfObject ]struct{},error ){_egfa :=_deede (_bcc );_geg :=_egfa ._gce ;_gabd :=make (map[_bc .PdfObject ]struct{});_dce :=_cgc (_geg );for _ ,_edc :=range _geg {_cfa ,_cee :=_bc .GetDict (_edc .PdfObject );
if !_cee {continue ;};_gdd ,_cee :=_bc .GetDict (_cfa .Get ("\u0052e\u0073\u006f\u0075\u0072\u0063\u0065s"));if !_cee {continue ;};_dbe :=_dce ["\u0058O\u0062\u006a\u0065\u0063\u0074"];_egb ,_cee :=_bc .GetDict (_gdd .Get ("\u0058O\u0062\u006a\u0065\u0063\u0074"));
if _cee {_cea :=_gabdc (_egb );for _ ,_ceee :=range _cea {if _fadg (_ceee ,_dbe ){continue ;};_fae :=*_bc .MakeName (_ceee );_abff :=_egb .Get (_fae );_gabd [_abff ]=struct{}{};_egb .Remove (_fae );_fcfa :=_acbe (_abff ,_gabd );if _fcfa !=nil {_g .Log .Debug ("\u0066\u0061\u0069\u006ce\u0064\u0020\u0074\u006f\u0020\u0074\u0072\u0061\u0076\u0065r\u0073e\u0020\u006f\u0062\u006a\u0065\u0063\u0074 \u0025\u0076",_abff );
};};};_gdf ,_cee :=_bc .GetDict (_gdd .Get ("\u0046\u006f\u006e\u0074"));_ecdd :=_dce ["\u0046\u006f\u006e\u0074"];if _cee {_fbd :=_gabdc (_gdf );for _ ,_aba :=range _fbd {if _fadg (_aba ,_ecdd ){continue ;};_ggd :=*_bc .MakeName (_aba );_edf :=_gdf .Get (_ggd );
_gabd [_edf ]=struct{}{};_gdf .Remove (_ggd );_dfg :=_acbe (_edf ,_gabd );if _dfg !=nil {_g .Log .Debug ("\u0046\u0061i\u006c\u0065\u0064\u0020\u0074\u006f\u0020\u0074\u0072\u0061\u0076\u0065\u0072\u0073\u0065\u0020\u006f\u0062\u006a\u0065\u0063\u0074 %\u0076\u000a",_edf );
};};};_bceb ,_cee :=_bc .GetDict (_gdd .Get ("\u0045x\u0074\u0047\u0053\u0074\u0061\u0074e"));if _cee {_dfa :=_gabdc (_bceb );_aeg :=_dce ["\u0045x\u0074\u0047\u0053\u0074\u0061\u0074e"];for _ ,_fdb :=range _dfa {if _fadg (_fdb ,_aeg ){continue ;};_acaf :=*_bc .MakeName (_fdb );
_eebc :=_bceb .Get (_acaf );_gabd [_eebc ]=struct{}{};_bceb .Remove (_acaf );_fefa :=_acbe (_eebc ,_gabd );if _fefa !=nil {_g .Log .Debug ("\u0066\u0061i\u006c\u0065\u0064\u0020\u0074\u006f\u0020\u0074\u0072\u0061\u0076\u0065\u0072\u0073\u0065\u0020\u006f\u0062\u006a\u0065\u0063\u0074 %\u0076\u000a",_eebc );
};};};};return _gabd ,nil ;};

// Optimize optimizes PDF objects to decrease PDF size.
func (_fbea *CompressStreams )Optimize (objects []_bc .PdfObject )(_defc []_bc .PdfObject ,_ebc error ){_defc =make ([]_bc .PdfObject ,len (objects ));copy (_defc ,objects );for _ ,_eaga :=range objects {_febf ,_fagc :=_bc .GetStream (_eaga );if !_fagc {continue ;
};if _fbgg :=_febf .Get ("\u0046\u0069\u006c\u0074\u0065\u0072");_fbgg !=nil {if _ ,_fdg :=_bc .GetName (_fbgg );_fdg {continue ;};if _cbgb ,_ace :=_bc .GetArray (_fbgg );_ace &&_cbgb .Len ()> 0{continue ;};};_gbee :=_bc .NewFlateEncoder ();var _bdac []byte ;
_bdac ,_ebc =_gbee .EncodeBytes (_febf .Stream );if _ebc !=nil {return _defc ,_ebc ;};_ffb :=_gbee .MakeStreamDict ();if len (_bdac )+len (_ffb .WriteString ())< len (_febf .Stream ){_febf .Stream =_bdac ;_febf .PdfObjectDictionary .Merge (_ffb );_febf .PdfObjectDictionary .Set ("\u004c\u0065\u006e\u0067\u0074\u0068",_bc .MakeInteger (int64 (len (_febf .Stream ))));
};};return _defc ,nil ;};type imageModifications struct{Scale float64 ;Encoding _bc .StreamEncoder ;};func _fadg (_eaef string ,_ebde []string )bool {for _ ,_bef :=range _ebde {if _eaef ==_bef {return true ;};};return false ;};

// Optimize optimizes PDF objects to decrease PDF size.
func (_ecg *Chain )Optimize (objects []_bc .PdfObject )(_aa []_bc .PdfObject ,_gf error ){_bcg :=objects ;for _ ,_abf :=range _ecg ._fd {_ga ,_cg :=_abf .Optimize (_bcg );if _cg !=nil {_g .Log .Debug ("\u0045\u0052\u0052OR\u0020\u004f\u0070\u0074\u0069\u006d\u0069\u007a\u0061\u0074\u0069\u006f\u006e\u003a\u0020\u0025\u002b\u0076",_cg );
continue ;};_bcg =_ga ;};return _bcg ,nil ;};func _gga (_aaaf []_bc .PdfObject ,_gcgc map[_bc .PdfObject ]_bc .PdfObject ){if len (_gcgc )==0{return ;};for _eegb ,_dfd :=range _aaaf {if _ebcd ,_gfgd :=_gcgc [_dfd ];_gfgd {_aaaf [_eegb ]=_ebcd ;continue ;
};_gcgc [_dfd ]=_dfd ;switch _fgac :=_dfd .(type ){case *_bc .PdfObjectArray :_faff :=make ([]_bc .PdfObject ,_fgac .Len ());copy (_faff ,_fgac .Elements ());_gga (_faff ,_gcgc );for _ebab ,_bada :=range _faff {_fgac .Set (_ebab ,_bada );};case *_bc .PdfObjectStreams :_gga (_fgac .Elements (),_gcgc );
case *_bc .PdfObjectStream :_fbb :=[]_bc .PdfObject {_fgac .PdfObjectDictionary };_gga (_fbb ,_gcgc );_fgac .PdfObjectDictionary =_fbb [0].(*_bc .PdfObjectDictionary );case *_bc .PdfObjectDictionary :_acgac :=_fgac .Keys ();_bga :=make ([]_bc .PdfObject ,len (_acgac ));
for _bbbc ,_bgce :=range _acgac {_bga [_bbbc ]=_fgac .Get (_bgce );};_gga (_bga ,_gcgc );for _agec ,_ffbd :=range _acgac {_fgac .Set (_ffbd ,_bga [_agec ]);};case *_bc .PdfIndirectObject :_bdgb :=[]_bc .PdfObject {_fgac .PdfObject };_gga (_bdgb ,_gcgc );
_fgac .PdfObject =_bdgb [0];};};};

// CompressStreams compresses uncompressed streams.
// It implements interface model.Optimizer.
type CompressStreams struct{};

// Optimize optimizes PDF objects to decrease PDF size.
func (_cdb *CombineDuplicateStreams )Optimize (objects []_bc .PdfObject )(_egdc []_bc .PdfObject ,_fdc error ){_gdb :=make (map[_bc .PdfObject ]_bc .PdfObject );_bbag :=make (map[_bc .PdfObject ]struct{});_ccf :=make (map[string ][]*_bc .PdfObjectStream );
for _ ,_fabc :=range objects {if _gge ,_dbed :=_fabc .(*_bc .PdfObjectStream );_dbed {_dfb :=_d .New ();_dfb .Write (_gge .Stream );_dfb .Write ([]byte (_gge .PdfObjectDictionary .WriteString ()));_fcfd :=string (_dfb .Sum (nil ));_ccf [_fcfd ]=append (_ccf [_fcfd ],_gge );
};};for _ ,_cce :=range _ccf {if len (_cce )< 2{continue ;};_ccea :=_cce [0];for _egfe :=1;_egfe < len (_cce );_egfe ++{_cfeg :=_cce [_egfe ];_gdb [_cfeg ]=_ccea ;_bbag [_cfeg ]=struct{}{};};};_egdc =make ([]_bc .PdfObject ,0,len (objects )-len (_bbag ));
for _ ,_adeg :=range objects {if _ ,_cedb :=_bbag [_adeg ];_cedb {continue ;};_egdc =append (_egdc ,_adeg );};_gga (_egdc ,_gdb );return _egdc ,nil ;};

// CleanContentstream cleans up redundant operands in content streams, including Page and XObject Form
// contents. This process includes:
// 1. Marked content operators are removed.
// 2. Some operands are simplified (shorter form).
// TODO: Add more reduction methods and improving the methods for identifying unnecessary operands.
type CleanContentstream struct{};func _decb (_abaf []_bc .PdfObject ){for _gbbe ,_cgd :=range _abaf {switch _fbed :=_cgd .(type ){case *_bc .PdfIndirectObject :_fbed .ObjectNumber =int64 (_gbbe +1);_fbed .GenerationNumber =0;case *_bc .PdfObjectStream :_fbed .ObjectNumber =int64 (_gbbe +1);
_fbed .GenerationNumber =0;case *_bc .PdfObjectStreams :_fbed .ObjectNumber =int64 (_gbbe +1);_fbed .GenerationNumber =0;};};};func _cab (_dfbg []_bc .PdfObject )[]*imageInfo {_fff :=_bc .PdfObjectName ("\u0053u\u0062\u0074\u0079\u0070\u0065");_dbbf :=make (map[*_bc .PdfObjectStream ]struct{});
var _aeb []*imageInfo ;for _ ,_ffge :=range _dfbg {_egbg ,_ccfa :=_bc .GetStream (_ffge );if !_ccfa {continue ;};if _ ,_edca :=_dbbf [_egbg ];_edca {continue ;};_dbbf [_egbg ]=struct{}{};_cdg :=_egbg .PdfObjectDictionary .Get (_fff );_adg ,_ccfa :=_bc .GetName (_cdg );
if !_ccfa ||string (*_adg )!="\u0049\u006d\u0061g\u0065"{continue ;};_bgga :=&imageInfo {Stream :_egbg ,BitsPerComponent :8};if _ebdd ,_adf :=_bc .GetIntVal (_egbg .Get ("\u0042\u0069t\u0073\u0050\u0065r\u0043\u006f\u006d\u0070\u006f\u006e\u0065\u006e\u0074"));
_adf {_bgga .BitsPerComponent =_ebdd ;};if _gdda ,_fegb :=_bc .GetIntVal (_egbg .Get ("\u0057\u0069\u0064t\u0068"));_fegb {_bgga .Width =_gdda ;};if _baa ,_egffb :=_bc .GetIntVal (_egbg .Get ("\u0048\u0065\u0069\u0067\u0068\u0074"));_egffb {_bgga .Height =_baa ;
};_cbfb ,_efc :=_cd .NewPdfColorspaceFromPdfObject (_egbg .Get ("\u0043\u006f\u006c\u006f\u0072\u0053\u0070\u0061\u0063\u0065"));if _efc !=nil {_g .Log .Debug ("\u0045R\u0052\u004f\u0052\u003a\u0020\u0025v",_efc );continue ;};if _cbfb ==nil {_cdf ,_age :=_bc .GetName (_egbg .Get ("\u0046\u0069\u006c\u0074\u0065\u0072"));
if _age {switch _cdf .String (){case "\u0043\u0043\u0049\u0054\u0054\u0046\u0061\u0078\u0044e\u0063\u006f\u0064\u0065","J\u0042\u0049\u0047\u0032\u0044\u0065\u0063\u006f\u0064\u0065":_cbfb =_cd .NewPdfColorspaceDeviceGray ();_bgga .BitsPerComponent =1;
};};};switch _gaef :=_cbfb .(type ){case *_cd .PdfColorspaceDeviceRGB :_bgga .ColorComponents =3;case *_cd .PdfColorspaceDeviceGray :_bgga .ColorComponents =1;default:_g .Log .Debug ("\u004f\u0070\u0074\u0069\u006d\u0069\u007aa\u0074\u0069\u006fn\u0020\u0069\u0073 \u006e\u006ft\u0020\u0073\u0075\u0070\u0070\u006fr\u0074ed\u0020\u0066\u006f\u0072\u0020\u0063\u006f\u006c\u006f\u0072\u0020\u0073\u0070\u0061\u0063\u0065\u0020\u0025\u0054\u0020\u002d\u0020\u0073\u006b\u0069\u0070",_gaef );
continue ;};_aeb =append (_aeb ,_bgga );};return _aeb ;};

// CombineIdenticalIndirectObjects combines identical indirect objects.
// It implements interface model.Optimizer.
type CombineIdenticalIndirectObjects struct{};

// Optimize optimizes PDF objects to decrease PDF size.
func (_aacc *CleanContentstream )Optimize (objects []_bc .PdfObject )(_ge []_bc .PdfObject ,_fef error ){_da :=map[*_bc .PdfObjectStream ]struct{}{};var _gbf []*_bc .PdfObjectStream ;_ffg :=func (_def *_bc .PdfObjectStream ){if _ ,_ae :=_da [_def ];!_ae {_da [_def ]=struct{}{};
_gbf =append (_gbf ,_def );};};_aaf :=map[_bc .PdfObject ]bool {};_be :=map[_bc .PdfObject ]bool {};for _ ,_db :=range objects {switch _cb :=_db .(type ){case *_bc .PdfIndirectObject :switch _feg :=_cb .PdfObject .(type ){case *_bc .PdfObjectDictionary :if _ega ,_ebg :=_bc .GetName (_feg .Get ("\u0054\u0079\u0070\u0065"));
!_ebg ||_ega .String ()!="\u0050\u0061\u0067\u0065"{continue ;};if _fcf ,_bdc :=_bc .GetStream (_feg .Get ("\u0043\u006f\u006e\u0074\u0065\u006e\u0074\u0073"));_bdc {_ffg (_fcf );}else if _fg ,_cac :=_bc .GetArray (_feg .Get ("\u0043\u006f\u006e\u0074\u0065\u006e\u0074\u0073"));
_cac {var _dea []*_bc .PdfObjectStream ;for _ ,_ea :=range _fg .Elements (){if _gcc ,_bg :=_bc .GetStream (_ea );_bg {_dea =append (_dea ,_gcc );};};if len (_dea )> 0{var _ade _c .Buffer ;for _ ,_acd :=range _dea {if _gcb ,_cga :=_bc .DecodeStream (_acd );
_cga ==nil {_ade .Write (_gcb );};_aaf [_acd ]=true ;};_ecb ,_df :=_bc .MakeStream (_ade .Bytes (),_bc .NewFlateEncoder ());if _df !=nil {return nil ,_df ;};_be [_ecb ]=true ;_feg .Set ("\u0043\u006f\u006e\u0074\u0065\u006e\u0074\u0073",_ecb );_ffg (_ecb );
};};};case *_bc .PdfObjectStream :if _fefe ,_ef :=_bc .GetName (_cb .Get ("\u0054\u0079\u0070\u0065"));!_ef ||_fefe .String ()!="\u0058O\u0062\u006a\u0065\u0063\u0074"{continue ;};if _bed ,_aee :=_bc .GetName (_cb .Get ("\u0053u\u0062\u0074\u0079\u0070\u0065"));
!_aee ||_bed .String ()!="\u0046\u006f\u0072\u006d"{continue ;};_ffg (_cb );};};for _ ,_dbf :=range _gbf {_fef =_gc (_dbf );if _fef !=nil {return nil ,_fef ;};};_ge =nil ;for _ ,_cf :=range objects {if _aaf [_cf ]{continue ;};_ge =append (_ge ,_cf );};
for _af :=range _be {_ge =append (_ge ,_af );};return _ge ,nil ;};

// ObjectStreams groups PDF objects to object streams.
// It implements interface model.Optimizer.
type ObjectStreams struct{};

// CombineDuplicateStreams combines duplicated streams by its data hash.
// It implements interface model.Optimizer.
type CombineDuplicateStreams struct{};func _bce (_gaf *_bc .PdfObjectStream ,_bfb []rune ,_afc []_bd .GlyphIndex )error {_gaf ,_fabf :=_bc .GetStream (_gaf );if !_fabf {_g .Log .Debug ("\u0045\u006d\u0062\u0065\u0064\u0064\u0065\u0064\u0020\u0066\u006f\u006e\u0074\u0020\u006f\u0062\u006a\u0065c\u0074\u0020\u006e\u006f\u0074\u0020\u0066o\u0075\u006e\u0064\u0020\u002d\u002d\u0020\u0041\u0042\u004f\u0052T\u0020\u0073\u0075\u0062\u0073\u0065\u0074\u0074\u0069\u006e\u0067");
return _eb .New ("\u0066\u006f\u006e\u0074fi\u006c\u0065\u0032\u0020\u006e\u006f\u0074\u0020\u0066\u006f\u0075\u006e\u0064");};_fbcg ,_cdeg :=_bc .DecodeStream (_gaf );if _cdeg !=nil {_g .Log .Debug ("\u0044\u0065c\u006f\u0064\u0065 \u0065\u0072\u0072\u006f\u0072\u003a\u0020\u0025\u0076",_cdeg );
return _cdeg ;};_cbc ,_cdeg :=_bd .Parse (_c .NewReader (_fbcg ));if _cdeg !=nil {_g .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u0070\u0061\u0072\u0073\u0069n\u0067\u0020\u0025\u0064\u0020\u0062\u0079\u0074\u0065\u0020f\u006f\u006e\u0074",len (_gaf .Stream ));
return _cdeg ;};_cfe :=_afc ;if len (_bfb )> 0{_fcd :=_cbc .LookupRunes (_bfb );_cfe =append (_cfe ,_fcd ...);};_cbc ,_cdeg =_cbc .SubsetKeepIndices (_cfe );if _cdeg !=nil {_g .Log .Debug ("\u0045R\u0052\u004f\u0052\u0020s\u0075\u0062\u0073\u0065\u0074t\u0069n\u0067 \u0066\u006f\u006e\u0074\u003a\u0020\u0025v",_cdeg );
return _cdeg ;};var _bba _c .Buffer ;_cdeg =_cbc .Write (&_bba );if _cdeg !=nil {_g .Log .Debug ("\u0045\u0052\u0052\u004fR \u0057\u0072\u0069\u0074\u0069\u006e\u0067\u0020\u0066\u006f\u006e\u0074\u003a\u0020%\u0076",_cdeg );return _cdeg ;};if _bba .Len ()> len (_fbcg ){_g .Log .Debug ("\u0052\u0065-\u0077\u0072\u0069\u0074\u0074\u0065\u006e\u0020\u0066\u006f\u006e\u0074\u0020\u0069\u0073\u0020\u006c\u0061\u0072\u0067\u0065\u0072\u0020\u0074\u0068\u0061\u006e\u0020\u006f\u0072\u0069\u0067\u0069\u006e\u0061\u006c\u0020\u002d\u0020\u0073\u006b\u0069\u0070");
return nil ;};_dec ,_cdeg :=_bc .MakeStream (_bba .Bytes (),_bc .NewFlateEncoder ());if _cdeg !=nil {_g .Log .Debug ("\u0045\u0052\u0052\u004fR \u0057\u0072\u0069\u0074\u0069\u006e\u0067\u0020\u0066\u006f\u006e\u0074\u003a\u0020%\u0076",_cdeg );return _cdeg ;
};*_gaf =*_dec ;_gaf .Set ("\u004ce\u006e\u0067\u0074\u0068\u0031",_bc .MakeInteger (int64 (_bba .Len ())));return nil ;};

// Image optimizes images by rewrite images into JPEG format with quality equals to ImageQuality.
// TODO(a5i): Add support for inline images.
// It implements interface model.Optimizer.
type Image struct{ImageQuality int ;};func _cgc (_ba []*_bc .PdfIndirectObject )map[string ][]string {_ccb :=map[string ][]string {};for _ ,_dbb :=range _ba {_gagd ,_cbf :=_bc .GetDict (_dbb .PdfObject );if !_cbf {continue ;};_dee :=_gagd .Get ("\u0043\u006f\u006e\u0074\u0065\u006e\u0074\u0073");
_bbaa :=_bc .TraceToDirectObject (_dee );_ddb :="";if _cfgf ,_dag :=_bbaa .(*_bc .PdfObjectArray );_dag {var _fcge []string ;for _ ,_bag :=range _cfgf .Elements (){_acff ,_cgcd :=_adc (_bag );if _cgcd !=nil {continue ;};_fcge =append (_fcge ,_acff );};
_ddb =_a .Join (_fcge ,"\u0020");};if _cbed ,_ecddd :=_bbaa .(*_bc .PdfObjectStream );_ecddd {_caf ,_bfe :=_bc .DecodeStream (_cbed );if _bfe !=nil {continue ;};_ddb =string (_caf );};_dac :=_de .NewContentStreamParser (_ddb );_bbf ,_gedd :=_dac .Parse ();
if _gedd !=nil {continue ;};for _ ,_deed :=range *_bbf {_egff :=_deed .Operand ;_acb :=_deed .Params ;switch _egff {case "\u0044\u006f":_bagd :=_acb [0].String ();if _ ,_fgd :=_ccb ["\u0058O\u0062\u006a\u0065\u0063\u0074"];!_fgd {_ccb ["\u0058O\u0062\u006a\u0065\u0063\u0074"]=[]string {_bagd };
}else {_ccb ["\u0058O\u0062\u006a\u0065\u0063\u0074"]=append (_ccb ["\u0058O\u0062\u006a\u0065\u0063\u0074"],_bagd );};case "\u0054\u0066":_daa :=_acb [0].String ();if _ ,_eae :=_ccb ["\u0046\u006f\u006e\u0074"];!_eae {_ccb ["\u0046\u006f\u006e\u0074"]=[]string {_daa };
}else {_ccb ["\u0046\u006f\u006e\u0074"]=append (_ccb ["\u0046\u006f\u006e\u0074"],_daa );};case "\u0067\u0073":_eec :=_acb [0].String ();if _ ,_dga :=_ccb ["\u0045x\u0074\u0047\u0053\u0074\u0061\u0074e"];!_dga {_ccb ["\u0045x\u0074\u0047\u0053\u0074\u0061\u0074e"]=[]string {_eec };
}else {_ccb ["\u0045x\u0074\u0047\u0053\u0074\u0061\u0074e"]=append (_ccb ["\u0045x\u0074\u0047\u0053\u0074\u0061\u0074e"],_eec );};};};};return _ccb ;};