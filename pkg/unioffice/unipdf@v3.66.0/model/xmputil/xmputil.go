//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

// Package xmputil provides abstraction used by the pdf document XMP Metadata.
package xmputil ;import (_cg "errors";_cb "fmt";_ca "github.com/trimmer-io/go-xmp/models/pdf";_gg "github.com/trimmer-io/go-xmp/models/xmp_base";_d "github.com/trimmer-io/go-xmp/models/xmp_mm";_b "github.com/trimmer-io/go-xmp/xmp";_gd "github.com/unidoc/unipdf/v3/core";
_gb "github.com/unidoc/unipdf/v3/internal/timeutils";_gc "github.com/unidoc/unipdf/v3/internal/uuid";_a "github.com/unidoc/unipdf/v3/model/xmputil/pdfaextension";_f "github.com/unidoc/unipdf/v3/model/xmputil/pdfaid";_bc "strconv";_g "time";);

// MediaManagementVersion is the version of the media management xmp metadata.
type MediaManagementVersion struct{VersionID string ;ModifyDate _g .Time ;Comments string ;Modifier string ;};

// SetPdfAExtension sets the pdfaExtension XMP metadata.
func (_ea *Document )SetPdfAExtension ()error {_eab ,_ed :=_a .MakeModel (_ea ._dg );if _ed !=nil {return _ed ;};if _ed =_a .FillModel (_ea ._dg ,_eab );_ed !=nil {return _ed ;};if _ed =_eab .SyncToXMP (_ea ._dg );_ed !=nil {return _ed ;};return nil ;};


// Marshal the document into xml byte stream.
func (_df *Document )Marshal ()([]byte ,error ){if _df ._dg .IsDirty (){if _af :=_df ._dg .SyncModels ();_af !=nil {return nil ,_af ;};};return _b .Marshal (_df ._dg );};

// MediaManagementOptions are the options for the Media management xmp metadata.
type MediaManagementOptions struct{

// OriginalDocumentID  as media is imported and projects is started, an original-document ID
// must be created to identify a new document. This identifies a document as a conceptual entity.
// By default, this value is generated.
OriginalDocumentID string ;

// NewDocumentID is a flag which generates a new Document identifier while setting media management.
// This value should be set to true only if the document is stored and saved as new document.
// Otherwise, if the document is modified and overwrites previous file, it should be set to false.
NewDocumentID bool ;

// DocumentID when a document is copied to a new file path or converted to a new format with
// Save As, another new document ID should usually be assigned. This identifies a general version or
// branch of a document. You can use it to track different versions or extracted portions of a document
// with the same original-document ID.
// By default, this value is generated if NewDocumentID is true or previous doesn't exist.
DocumentID string ;

// InstanceID to track a document’s editing history, you must assign a new instance ID
// whenever a document is saved after any changes. This uniquely identifies an exact version of a
// document. It is used in resource references (to identify both the document or part itself and the
// referenced or referencing documents), and in document-history resource events (to identify the
// document instance that resulted from the change).
// By default, this value is generated.
InstanceID string ;

// DerivedFrom references the source document from which this one is derived,
// typically through a Save As operation that changes the file name or format. It is a minimal reference;
// missing components can be assumed to be unchanged. For example, a new version might only need
// to specify the instance ID and version number of the previous version, or a rendition might only need
// to specify the instance ID and rendition class of the original.
// By default, the derived from structure is filled from previous XMP metadata (if exists).
DerivedFrom string ;

// VersionID are meant to associate the document with a product version that is part of a release process. They can be useful in tracking the
// document history, but should not be used to identify a document uniquely in any context.
// Usually it simply works by incrementing integers 1,2,3...
// By default, this values is incremented or set to the next version number.
VersionID string ;

// ModifyComment is a comment to given modification
ModifyComment string ;

// ModifyDate is a custom modification date for the versions.
// By default, this would be set to time.Now().
ModifyDate _g .Time ;

// Modifier is a person who did the modification.
Modifier string ;};

// LoadDocument loads up the xmp document from provided input stream.
func LoadDocument (stream []byte )(*Document ,error ){_ag :=_b .NewDocument ();if _bf :=_b .Unmarshal (stream ,_ag );_bf !=nil {return nil ,_bf ;};return &Document {_dg :_ag },nil ;};

// GetPdfAID gets the pdfaid xmp metadata model.
func (_ab *Document )GetPdfAID ()(*PdfAID ,bool ){_fcc ,_fcd :=_ab ._dg .FindModel (_f .Namespace ).(*_f .Model );if !_fcd {return nil ,false ;};return &PdfAID {Part :_fcc .Part ,Conformance :_fcc .Conformance },true ;};

// GetPdfaExtensionSchemas gets a pdfa extension schemas.
func (_ba *Document )GetPdfaExtensionSchemas ()([]_a .Schema ,error ){_gf :=_ba ._dg .FindModel (_a .Namespace );if _gf ==nil {return nil ,nil ;};_dfb ,_dfd :=_gf .(*_a .Model );if !_dfd {return nil ,_cb .Errorf ("\u0069\u006eva\u006c\u0069\u0064 \u006d\u006f\u0064\u0065l f\u006fr \u0070\u0064\u0066\u0061\u0045\u0078\u0074en\u0073\u0069\u006f\u006e\u0073\u003a\u0020%\u0054",_gf );
};return _dfb .Schemas ,nil ;};

// PdfInfo is the xmp document pdf info.
type PdfInfo struct{InfoDict _gd .PdfObject ;PdfVersion string ;Copyright string ;Marked bool ;};

// GetMediaManagement gets the media management metadata from provided xmp document.
func (_afg *Document )GetMediaManagement ()(*MediaManagement ,bool ){_ae :=_d .FindModel (_afg ._dg );if _ae ==nil {return nil ,false ;};_eb :=make ([]MediaManagementVersion ,len (_ae .Versions ));for _aee ,_gbbe :=range _ae .Versions {_eb [_aee ]=MediaManagementVersion {VersionID :_gbbe .Version ,ModifyDate :_gbbe .ModifyDate .Value (),Comments :_gbbe .Comments ,Modifier :_gbbe .Modifier };
};_bcd :=&MediaManagement {OriginalDocumentID :GUID (_ae .OriginalDocumentID .Value ()),DocumentID :GUID (_ae .DocumentID .Value ()),InstanceID :GUID (_ae .InstanceID .Value ()),VersionID :_ae .VersionID ,Versions :_eb };if _ae .DerivedFrom !=nil {_bcd .DerivedFrom =&MediaManagementDerivedFrom {OriginalDocumentID :GUID (_ae .DerivedFrom .OriginalDocumentID ),DocumentID :GUID (_ae .DerivedFrom .DocumentID ),InstanceID :GUID (_ae .DerivedFrom .InstanceID ),VersionID :_ae .DerivedFrom .VersionID };
};return _bcd ,true ;};

// Document is an implementation of the xmp document.
// It is a wrapper over go-xmp/xmp.Document that provides some Pdf predefined functionality.
type Document struct{_dg *_b .Document };

// SetPdfAID sets up pdfaid xmp metadata.
// In example: Part: '1' Conformance: 'B' states for PDF/A 1B.
func (_bcf *Document )SetPdfAID (part int ,conformance string )error {_bdf ,_fbe :=_f .MakeModel (_bcf ._dg );if _fbe !=nil {return _fbe ;};_bdf .Part =part ;_bdf .Conformance =conformance ;if _gcf :=_bdf .SyncToXMP (_bcf ._dg );_gcf !=nil {return _gcf ;
};return nil ;};

// MediaManagementDerivedFrom is a structure that contains references of identifiers and versions
// from which given document was derived.
type MediaManagementDerivedFrom struct{OriginalDocumentID GUID ;DocumentID GUID ;InstanceID GUID ;VersionID string ;};

// NewDocument creates a new document without any previous xmp information.
func NewDocument ()*Document {_ce :=_b .NewDocument ();return &Document {_dg :_ce }};

// MarshalIndent the document into xml byte stream with predefined prefix and indent.
func (_bg *Document )MarshalIndent (prefix ,indent string )([]byte ,error ){if _bg ._dg .IsDirty (){if _dd :=_bg ._dg .SyncModels ();_dd !=nil {return nil ,_dd ;};};return _b .MarshalIndent (_bg ._dg ,prefix ,indent );};

// MediaManagement are the values from the document media management metadata.
type MediaManagement struct{

// OriginalDocumentID  as media is imported and projects is started, an original-document ID
// must be created to identify a new document. This identifies a document as a conceptual entity.
OriginalDocumentID GUID ;

// DocumentID when a document is copied to a new file path or converted to a new format with
// Save As, another new document ID should usually be assigned. This identifies a general version or
// branch of a document. You can use it to track different versions or extracted portions of a document
// with the same original-document ID.
DocumentID GUID ;

// InstanceID to track a document’s editing history, you must assign a new instance ID
// whenever a document is saved after any changes. This uniquely identifies an exact version of a
// document. It is used in resource references (to identify both the document or part itself and the
// referenced or referencing documents), and in document-history resource events (to identify the
// document instance that resulted from the change).
InstanceID GUID ;

// DerivedFrom references the source document from which this one is derived,
// typically through a Save As operation that changes the file name or format. It is a minimal reference;
// missing components can be assumed to be unchanged. For example, a new version might only need
// to specify the instance ID and version number of the previous version, or a rendition might only need
// to specify the instance ID and rendition class of the original.
DerivedFrom *MediaManagementDerivedFrom ;

// VersionID are meant to associate the document with a product version that is part of a release process. They can be useful in tracking the
// document history, but should not be used to identify a document uniquely in any context.
// Usually it simply works by incrementing integers 1,2,3...
VersionID string ;

// Versions is the history of the document versions along with the comments, timestamps and issuers.
Versions []MediaManagementVersion ;};

// PdfAID is the result of the XMP pdfaid metadata.
type PdfAID struct{Part int ;Conformance string ;};

// GetPdfInfo gets the document pdf info.
func (_eg *Document )GetPdfInfo ()(*PdfInfo ,bool ){_caf :=PdfInfo {};var _edb *_gd .PdfObjectDictionary ;_ec :=func (_ecf string ,_ff _gd .PdfObject ){if _edb ==nil {_edb =_gd .MakeDict ();};_edb .Set (_gd .PdfObjectName (_ecf ),_ff );};_gccf ,_fb :=_eg ._dg .FindModel (_ca .NsPDF ).(*_ca .PDFInfo );
if !_fb {_dcb ,_fc :=_eg ._dg .FindModel (_gg .NsXmp ).(*_gg .XmpBase );if !_fc {return nil ,false ;};if _dcb .CreatorTool !=""{_ec ("\u0043r\u0065\u0061\u0074\u006f\u0072",_gd .MakeString (string (_dcb .CreatorTool )));};if !_dcb .CreateDate .IsZero (){_ec ("\u0043\u0072\u0065a\u0074\u0069\u006f\u006e\u0044\u0061\u0074\u0065",_gd .MakeString (_gb .FormatPdfTime (_dcb .CreateDate .Value ())));
};if !_dcb .ModifyDate .IsZero (){_ec ("\u004do\u0064\u0044\u0061\u0074\u0065",_gd .MakeString (_gb .FormatPdfTime (_dcb .ModifyDate .Value ())));};_caf .InfoDict =_edb ;return &_caf ,true ;};_caf .Copyright =_gccf .Copyright ;_caf .PdfVersion =_gccf .PDFVersion ;
_caf .Marked =bool (_gccf .Marked );if len (_gccf .Title )> 0{_ec ("\u0054\u0069\u0074l\u0065",_gd .MakeString (_gccf .Title .Default ()));};if len (_gccf .Author )> 0{_ec ("\u0041\u0075\u0074\u0068\u006f\u0072",_gd .MakeString (_gccf .Author [0]));};if _gccf .Keywords !=""{_ec ("\u004b\u0065\u0079\u0077\u006f\u0072\u0064\u0073",_gd .MakeString (_gccf .Keywords ));
};if len (_gccf .Subject )> 0{_ec ("\u0053u\u0062\u006a\u0065\u0063\u0074",_gd .MakeString (_gccf .Subject .Default ()));};if _gccf .Creator !=""{_ec ("\u0043r\u0065\u0061\u0074\u006f\u0072",_gd .MakeString (string (_gccf .Creator )));};if _gccf .Producer !=""{_ec ("\u0050\u0072\u006f\u0064\u0075\u0063\u0065\u0072",_gd .MakeString (string (_gccf .Producer )));
};if _gccf .Trapped {_ec ("\u0054r\u0061\u0070\u0070\u0065\u0064",_gd .MakeName ("\u0054\u0072\u0075\u0065"));};if !_gccf .CreationDate .IsZero (){_ec ("\u0043\u0072\u0065a\u0074\u0069\u006f\u006e\u0044\u0061\u0074\u0065",_gd .MakeString (_gb .FormatPdfTime (_gccf .CreationDate .Value ())));
};if !_gccf .ModifyDate .IsZero (){_ec ("\u004do\u0064\u0044\u0061\u0074\u0065",_gd .MakeString (_gb .FormatPdfTime (_gccf .ModifyDate .Value ())));};_caf .InfoDict =_edb ;return &_caf ,true ;};

// GUID is a string representing a globally unique identifier.
type GUID string ;

// GetGoXmpDocument gets direct access to the go-xmp.Document.
// All changes done to specified document would result in change of this document 'd'.
func (_e *Document )GetGoXmpDocument ()*_b .Document {return _e ._dg };

// SetPdfInfo sets the pdf info into selected document.
func (_ee *Document )SetPdfInfo (options *PdfInfoOptions )error {if options ==nil {return _cg .New ("\u006ei\u006c\u0020\u0070\u0064\u0066\u0020\u006f\u0070\u0074\u0069\u006fn\u0073\u0020\u0070\u0072\u006f\u0076\u0069\u0064\u0065\u0064");};_aa ,_eae :=_ca .MakeModel (_ee ._dg );
if _eae !=nil {return _eae ;};if options .Overwrite {*_aa =_ca .PDFInfo {};};if options .InfoDict !=nil {_gcc ,_cgc :=_gd .GetDict (options .InfoDict );if !_cgc {return _cb .Errorf ("i\u006e\u0076\u0061\u006c\u0069\u0064 \u0070\u0064\u0066\u0020\u006f\u0062\u006a\u0065\u0063t\u0020\u0074\u0079p\u0065:\u0020\u0025\u0054",options .InfoDict );
};var _ge *_gd .PdfObjectString ;for _ ,_dde :=range _gcc .Keys (){switch _dde {case "\u0054\u0069\u0074l\u0065":_ge ,_cgc =_gd .GetString (_gcc .Get ("\u0054\u0069\u0074l\u0065"));if _cgc {_aa .Title =_b .NewAltString (_ge );};case "\u0041\u0075\u0074\u0068\u006f\u0072":_ge ,_cgc =_gd .GetString (_gcc .Get ("\u0041\u0075\u0074\u0068\u006f\u0072"));
if _cgc {_aa .Author =_b .NewStringList (_ge .String ());};case "\u004b\u0065\u0079\u0077\u006f\u0072\u0064\u0073":_ge ,_cgc =_gd .GetString (_gcc .Get ("\u004b\u0065\u0079\u0077\u006f\u0072\u0064\u0073"));if _cgc {_aa .Keywords =_ge .String ();};case "\u0043r\u0065\u0061\u0074\u006f\u0072":_ge ,_cgc =_gd .GetString (_gcc .Get ("\u0043r\u0065\u0061\u0074\u006f\u0072"));
if _cgc {_aa .Creator =_b .AgentName (_ge .String ());};case "\u0053u\u0062\u006a\u0065\u0063\u0074":_ge ,_cgc =_gd .GetString (_gcc .Get ("\u0053u\u0062\u006a\u0065\u0063\u0074"));if _cgc {_aa .Subject =_b .NewAltString (_ge .String ());};case "\u0050\u0072\u006f\u0064\u0075\u0063\u0065\u0072":_ge ,_cgc =_gd .GetString (_gcc .Get ("\u0050\u0072\u006f\u0064\u0075\u0063\u0065\u0072"));
if _cgc {_aa .Producer =_b .AgentName (_ge .String ());};case "\u0054r\u0061\u0070\u0070\u0065\u0064":_gfg ,_bgd :=_gd .GetName (_gcc .Get ("\u0054r\u0061\u0070\u0070\u0065\u0064"));if _bgd {switch _gfg .String (){case "\u0054\u0072\u0075\u0065":_aa .Trapped =true ;
case "\u0046\u0061\u006cs\u0065":_aa .Trapped =false ;default:_aa .Trapped =true ;};};case "\u0043\u0072\u0065a\u0074\u0069\u006f\u006e\u0044\u0061\u0074\u0065":if _bd ,_cd :=_gd .GetString (_gcc .Get ("\u0043\u0072\u0065a\u0074\u0069\u006f\u006e\u0044\u0061\u0074\u0065"));
_cd &&_bd .String ()!=""{_aae ,_db :=_gb .ParsePdfTime (_bd .String ());if _db !=nil {return _cb .Errorf ("\u0069\u006e\u0076\u0061\u006c\u0069\u0064\u0020\u0043\u0072e\u0061\u0074\u0069\u006f\u006e\u0044\u0061t\u0065\u0020\u0066\u0069\u0065\u006c\u0064\u003a\u0020\u0025\u0077",_db );
};_aa .CreationDate =_b .NewDate (_aae );};case "\u004do\u0064\u0044\u0061\u0074\u0065":if _gca ,_dcc :=_gd .GetString (_gcc .Get ("\u004do\u0064\u0044\u0061\u0074\u0065"));_dcc &&_gca .String ()!=""{_bdc ,_gbb :=_gb .ParsePdfTime (_gca .String ());if _gbb !=nil {return _cb .Errorf ("\u0069n\u0076\u0061\u006c\u0069d\u0020\u004d\u006f\u0064\u0044a\u0074e\u0020f\u0069\u0065\u006c\u0064\u003a\u0020\u0025w",_gbb );
};_aa .ModifyDate =_b .NewDate (_bdc );};};};};if options .PdfVersion !=""{_aa .PDFVersion =options .PdfVersion ;};if options .Marked {_aa .Marked =_b .Bool (options .Marked );};if options .Copyright !=""{_aa .Copyright =options .Copyright ;};if _eae =_aa .SyncToXMP (_ee ._dg );
_eae !=nil {return _eae ;};return nil ;};

// PdfInfoOptions are the options used for setting pdf info.
type PdfInfoOptions struct{InfoDict _gd .PdfObject ;PdfVersion string ;Copyright string ;Marked bool ;

// Overwrite if set to true, overwrites all values found in the current pdf info xmp model to the ones provided.
Overwrite bool ;};

// SetMediaManagement sets up XMP media management metadata: namespace xmpMM.
func (_fd *Document )SetMediaManagement (options *MediaManagementOptions )error {_gbc ,_dgd :=_d .MakeModel (_fd ._dg );if _dgd !=nil {return _dgd ;};if options ==nil {options =new (MediaManagementOptions );};_fe :=_d .ResourceRef {};switch {case options .DocumentID !="":_gbc .DocumentID =_b .GUID (options .DocumentID );
case options .NewDocumentID ||_gbc .DocumentID .IsZero ():if !_gbc .DocumentID .IsZero (){_fe .DocumentID =_gbc .DocumentID ;};_cag ,_dca :=_gc .NewUUID ();if _dca !=nil {return _dca ;};_gbc .DocumentID =_b .GUID (_cag .String ());};if !_gbc .InstanceID .IsZero (){_fe .InstanceID =_gbc .InstanceID ;
};_gbc .InstanceID =_b .GUID (options .InstanceID );if _gbc .InstanceID ==""{_bdd ,_cc :=_gc .NewUUID ();if _cc !=nil {return _cc ;};_gbc .InstanceID =_b .GUID (_bdd .String ());};if !_fe .IsZero (){_gbc .DerivedFrom =&_fe ;};_fg :=options .VersionID ;
if _gbc .VersionID !=""{_ecd ,_cge :=_bc .Atoi (_gbc .VersionID );if _cge !=nil {_fg =_bc .Itoa (len (_gbc .Versions )+1);}else {_fg =_bc .Itoa (_ecd +1);};};if _fg ==""{_fg ="\u0031";};_gbc .VersionID =_fg ;if _dgd =_gbc .SyncToXMP (_fd ._dg );_dgd !=nil {return _dgd ;
};return nil ;};