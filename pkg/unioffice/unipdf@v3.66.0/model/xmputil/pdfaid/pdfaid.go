//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package pdfaid ;import (_ff "fmt";_g "github.com/trimmer-io/go-xmp/xmp";_d "github.com/unidoc/unipdf/v3/model/xmputil/pdfaextension";);

// SyncToXMP implements xmp.Model interface.
func (_b *Model )SyncToXMP (d *_g .Document )error {return nil };func init (){_g .Register (Namespace ,_g .XmpMetadata );_d .RegisterSchema (Namespace ,&Schema )};var _ _g .Model =(*Model )(nil );

// Model is the XMP model for the PdfA metadata.
type Model struct{Part int `xmp:"pdfaid:part"`;Conformance string `xmp:"pdfaid:conformance"`;};

// SyncFromXMP implements xmp.Model interface.
func (_ec *Model )SyncFromXMP (d *_g .Document )error {return nil };

// Can implements xmp.Model interface.
func (_cd *Model )Can (nsName string )bool {return Namespace .GetName ()==nsName };

// MakeModel gets or create sa new model for PDF/A ID namespace.
func MakeModel (d *_g .Document )(*Model ,error ){_fc ,_db :=d .MakeModel (Namespace );if _db !=nil {return nil ,_db ;};return _fc .(*Model ),nil ;};

// SetTag implements xmp.Model interface.
func (_dc *Model )SetTag (tag ,value string )error {if _ce :=_g .SetNativeField (_dc ,tag ,value );_ce !=nil {return _ff .Errorf ("\u0025\u0073\u003a\u0020\u0025\u0076",Namespace .GetName (),_ce );};return nil ;};

// SyncModel implements xmp.Model interface.
func (_eg *Model )SyncModel (d *_g .Document )error {return nil };

// GetTag implements xmp.Model interface.
func (_fb *Model )GetTag (tag string )(string ,error ){_gc ,_cbf :=_g .GetNativeField (_fb ,tag );if _cbf !=nil {return "",_ff .Errorf ("\u0025\u0073\u003a\u0020\u0025\u0076",Namespace .GetName (),_cbf );};return _gc ,nil ;};

// Namespaces implements xmp.Model interface.
func (_e *Model )Namespaces ()_g .NamespaceList {return _g .NamespaceList {Namespace }};var Namespace =_g .NewNamespace ("\u0070\u0064\u0066\u0061\u0069\u0064","\u0068\u0074\u0074p\u003a\u002f\u002f\u0077w\u0077\u002e\u0061\u0069\u0069\u006d\u002eo\u0072\u0067\u002f\u0070\u0064\u0066\u0061\u002f\u006e\u0073\u002f\u0069\u0064\u002f",NewModel );
var Schema =_d .Schema {NamespaceURI :Namespace .URI ,Prefix :Namespace .Name ,Schema :"\u0050D\u0046/\u0041\u0020\u0049\u0044\u0020\u0053\u0063\u0068\u0065\u006d\u0061",Property :[]_d .Property {{Category :_d .PropertyCategoryInternal ,Description :"\u0050\u0061\u0072\u0074 o\u0066\u0020\u0050\u0044\u0046\u002f\u0041\u0020\u0073\u0074\u0061\u006e\u0064\u0061r\u0064",Name :"\u0070\u0061\u0072\u0074",ValueType :_d .ValueTypeNameInteger },{Category :_d .PropertyCategoryInternal ,Description :"A\u006d\u0065\u006e\u0064\u006d\u0065n\u0074\u0020\u006f\u0066\u0020\u0050\u0044\u0046\u002fA\u0020\u0073\u0074a\u006ed\u0061\u0072\u0064",Name :"\u0061\u006d\u0064",ValueType :_d .ValueTypeNameText },{Category :_d .PropertyCategoryInternal ,Description :"C\u006f\u006e\u0066\u006f\u0072\u006da\u006e\u0063\u0065\u0020\u006c\u0065v\u0065\u006c\u0020\u006f\u0066\u0020\u0050D\u0046\u002f\u0041\u0020\u0073\u0074\u0061\u006e\u0064\u0061r\u0064",Name :"c\u006f\u006e\u0066\u006f\u0072\u006d\u0061\u006e\u0063\u0065",ValueType :_d .ValueTypeNameText }},ValueType :nil };


// NewModel creates a new model.
func NewModel (name string )_g .Model {return &Model {}};

// CanTag implements xmp.Model interface.
func (_cb *Model )CanTag (tag string )bool {_ ,_fce :=_g .GetNativeField (_cb ,tag );return _fce ==nil };