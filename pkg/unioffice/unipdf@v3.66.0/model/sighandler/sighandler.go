//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

// Package sighandler implements digital signature handlers for PDF signature validation and signing.
package sighandler ;import (_gc "bytes";_aae "crypto";_fc "crypto/rand";_e "crypto/rsa";_aa "crypto/x509";_ge "crypto/x509/pkix";_fe "encoding/asn1";_ab "encoding/hex";_d "errors";_f "fmt";_dg "github.com/unidoc/pkcs7";_ce "github.com/unidoc/timestamp";
_fg "github.com/unidoc/unipdf/v3/common";_de "github.com/unidoc/unipdf/v3/core";_ac "github.com/unidoc/unipdf/v3/model";_daf "github.com/unidoc/unipdf/v3/model/mdp";_cc "github.com/unidoc/unipdf/v3/model/sigutil";_da "hash";_c "math/big";_df "strings";
_a "time";);

// ValidateWithOpts validates a PDF signature by checking PdfReader or PdfParser by the DiffPolicy
// params describes parameters for the DocMDP checks.
func (_db *DocMDPHandler )ValidateWithOpts (sig *_ac .PdfSignature ,digest _ac .Hasher ,params _ac .SignatureHandlerDocMDPParams )(_ac .SignatureValidationResult ,error ){_adc ,_be :=_db ._fa .Validate (sig ,digest );if _be !=nil {return _adc ,_be ;};_ff :=params .Parser ;
if _ff ==nil {return _ac .SignatureValidationResult {},_d .New ("p\u0061r\u0073\u0065\u0072\u0020\u0063\u0061\u006e\u0027t\u0020\u0062\u0065\u0020nu\u006c\u006c");};if !_adc .IsVerified {return _adc ,nil ;};_cee :=params .DiffPolicy ;if _cee ==nil {_cee =_daf .NewDefaultDiffPolicy ();
};for _dd :=0;_dd <=_ff .GetRevisionNumber ();_dd ++{_faf ,_bc :=_ff .GetRevision (_dd );if _bc !=nil {return _ac .SignatureValidationResult {},_bc ;};_ddf :=_faf .GetTrailer ();if _ddf ==nil {return _ac .SignatureValidationResult {},_d .New ("\u0075\u006e\u0064\u0065f\u0069\u006e\u0065\u0064\u0020\u0074\u0068\u0065\u0020\u0074r\u0061i\u006c\u0065\u0072\u0020\u006f\u0062\u006ae\u0063\u0074");
};_dea ,_fgb :=_de .GetDict (_ddf .Get ("\u0052\u006f\u006f\u0074"));if !_fgb {return _ac .SignatureValidationResult {},_d .New ("\u0075n\u0064\u0065\u0066\u0069n\u0065\u0064\u0020\u0074\u0068e\u0020r\u006fo\u0074\u0020\u006f\u0062\u006a\u0065\u0063t");
};_abf ,_fgb :=_de .GetDict (_dea .Get ("\u0041\u0063\u0072\u006f\u0046\u006f\u0072\u006d"));if !_fgb {continue ;};_gf ,_fgb :=_de .GetArray (_abf .Get ("\u0046\u0069\u0065\u006c\u0064\u0073"));if !_fgb {continue ;};for _ ,_gfd :=range _gf .Elements (){_eeg ,_dfff :=_de .GetDict (_gfd );
if !_dfff {continue ;};_ffa ,_dfff :=_de .GetDict (_eeg .Get ("\u0056"));if !_dfff {continue ;};if _de .EqualObjects (_ffa .Get ("\u0043\u006f\u006e\u0074\u0065\u006e\u0074\u0073"),sig .Contents ){_adc .DiffResults ,_bc =_cee .ReviewFile (_faf ,_ff ,&_daf .MDPParameters {DocMDPLevel :_db .Permission });
if _bc !=nil {return _ac .SignatureValidationResult {},_bc ;};_adc .IsVerified =_adc .DiffResults .IsPermitted ();return _adc ,nil ;};};};return _ac .SignatureValidationResult {},_d .New ("\u0064\u006f\u006e\u0027\u0074\u0020\u0066o\u0075\u006e\u0064 \u0074\u0068\u0069\u0073 \u0073\u0069\u0067\u006e\u0061\u0074\u0075\u0072\u0065\u0020\u0069\u006e\u0020\u0074\u0068\u0065\u0020\u0072\u0065\u0076\u0069\u0073\u0069\u006f\u006e\u0073");
};func (_dab *adobeX509RSASHA1 )getCertificate (_cdb *_ac .PdfSignature )(*_aa .Certificate ,error ){if _dab ._eed !=nil {return _dab ._eed ,nil ;};_eec ,_cfb :=_cdb .GetCerts ();if _cfb !=nil {return nil ,_cfb ;};return _eec [0],nil ;};type docTimeStamp struct{_egdce string ;
_dcbf _aae .Hash ;_fccd int ;_fbfa *_cc .TimestampClient ;};

// Sign sets the Contents fields for the PdfSignature.
func (_deaa *etsiPAdES )Sign (sig *_ac .PdfSignature ,digest _ac .Hasher )error {_efec ,_gff :=digest .(*_gc .Buffer );if !_gff {return _f .Errorf ("c\u0061s\u0074\u0020\u0074\u006f\u0020\u0062\u0075\u0066f\u0065\u0072\u0020\u0066ai\u006c\u0073");};_adb ,_dec :=_dg .NewSignedData (_efec .Bytes ());
if _dec !=nil {return _dec ;};_adb .SetDigestAlgorithm (_dg .OIDDigestAlgorithmSHA256 );_gffc :=_dg .SignerInfoConfig {};_efg :=_aae .SHA256 .New ();_efg .Write (_deaa ._ege .Raw );var _gcc struct{Seq struct{Seq struct{Value []byte ;};};};_gcc .Seq .Seq .Value =_efg .Sum (nil );
var _dgg []*_aa .Certificate ;var _edec []*_aa .Certificate ;if _deaa ._aba !=nil {_edec =[]*_aa .Certificate {_deaa ._aba };};_cae :=RevocationInfoArchival {Crl :[]_fe .RawValue {},Ocsp :[]_fe .RawValue {},OtherRevInfo :[]_fe .RawValue {}};_gae :=0;if _deaa ._dba !=nil &&len (_deaa ._gec )> 0{_dce ,_ccfc :=_deaa .makeTimestampRequest (_deaa ._gec ,([]byte )(""));
if _ccfc !=nil {return _ccfc ;};_ec ,_ccfc :=_ce .Parse (_dce .FullBytes );if _ccfc !=nil {return _ccfc ;};_dgg =append (_dgg ,_ec .Certificates ...);};if _deaa ._dba !=nil {_bbd ,_ddg :=_deaa .addDss ([]*_aa .Certificate {_deaa ._ege },_edec ,&_cae );
if _ddg !=nil {return _ddg ;};_gae +=_bbd ;if len (_dgg )> 0{_bbd ,_ddg =_deaa .addDss (_dgg ,nil ,&_cae );if _ddg !=nil {return _ddg ;};_gae +=_bbd ;};if !_deaa ._bd {_deaa ._dba .SetDSS (_deaa ._dfe );};};_gffc .ExtraSignedAttributes =append (_gffc .ExtraSignedAttributes ,_dg .Attribute {Type :_dg .OIDAttributeSigningCertificateV2 ,Value :_gcc },_dg .Attribute {Type :_dg .OIDAttributeAdobeRevocation ,Value :_cae });
if _gbe :=_adb .AddSignerChainPAdES (_deaa ._ege ,_deaa ._ag ,_edec ,_gffc );_gbe !=nil {return _gbe ;};_adb .Detach ();if len (_deaa ._gec )> 0{_gfg :=_adb .GetSignedData ().SignerInfos [0].EncryptedDigest ;_acba ,_bfb :=_deaa .makeTimestampRequest (_deaa ._gec ,_gfg );
if _bfb !=nil {return _bfb ;};_bfb =_adb .AddTimestampTokenToSigner (0,_acba .FullBytes );if _bfb !=nil {return _bfb ;};};_adf ,_dec :=_adb .Finish ();if _dec !=nil {return _dec ;};_ffg :=make ([]byte ,len (_adf )+1024*2+_gae );copy (_ffg ,_adf );sig .Contents =_de .MakeHexString (string (_ffg ));
if !_deaa ._bd &&_deaa ._dfe !=nil {_efg =_aae .SHA1 .New ();_efg .Write (_ffg );_cec :=_df .ToUpper (_ab .EncodeToString (_efg .Sum (nil )));if _cec !=""{_deaa ._dfe .VRI [_cec ]=&_ac .VRI {Cert :_deaa ._dfe .Certs ,OCSP :_deaa ._dfe .OCSPs ,CRL :_deaa ._dfe .CRLs };
};_deaa ._dba .SetDSS (_deaa ._dfe );};return nil ;};

// IsApplicable returns true if the signature handler is applicable for the PdfSignature.
func (_def *DocMDPHandler )IsApplicable (sig *_ac .PdfSignature )bool {_dff :=false ;for _ ,_aab :=range sig .Reference .Elements (){if _ee ,_ad :=_de .GetDict (_aab );_ad {if _ae ,_aabe :=_de .GetNameVal (_ee .Get ("\u0054r\u0061n\u0073\u0066\u006f\u0072\u006d\u004d\u0065\u0074\u0068\u006f\u0064"));
_aabe {if _ae !="\u0044\u006f\u0063\u004d\u0044\u0050"{return false ;};if _eb ,_bb :=_de .GetDict (_ee .Get ("\u0054r\u0061n\u0073\u0066\u006f\u0072\u006d\u0050\u0061\u0072\u0061\u006d\u0073"));_bb {_ ,_feb :=_de .GetNumberAsInt64 (_eb .Get ("\u0050"));
if _feb !=nil {return false ;};_dff =true ;break ;};};};};return _dff &&_def ._fa .IsApplicable (sig );};

// IsApplicable returns true if the signature handler is applicable for the PdfSignature.
func (_ggb *docTimeStamp )IsApplicable (sig *_ac .PdfSignature )bool {if sig ==nil ||sig .Filter ==nil ||sig .SubFilter ==nil {return false ;};return (*sig .Filter =="A\u0064\u006f\u0062\u0065\u002e\u0050\u0050\u004b\u004d\u0053"||*sig .Filter =="\u0041\u0064\u006f\u0062\u0065\u002e\u0050\u0050\u004b\u004c\u0069\u0074\u0065")&&*sig .SubFilter =="\u0045\u0054\u0053I\u002e\u0052\u0046\u0043\u0033\u0031\u0036\u0031";
};

// SignFunc represents a custom signing function. The function should return
// the computed signature.
type SignFunc func (_ddb *_ac .PdfSignature ,_agg _ac .Hasher )([]byte ,error );type timestampInfo struct{Version int ;Policy _fe .RawValue ;MessageImprint struct{HashAlgorithm _ge .AlgorithmIdentifier ;HashedMessage []byte ;};SerialNumber _fe .RawValue ;
GeneralizedTime _a .Time ;};

// NewEtsiPAdESLevelT creates a new Adobe.PPKLite ETSI.CAdES.detached Level T signature handler.
func NewEtsiPAdESLevelT (privateKey *_e .PrivateKey ,certificate *_aa .Certificate ,caCert *_aa .Certificate ,certificateTimestampServerURL string )(_ac .SignatureHandler ,error ){return &etsiPAdES {_ege :certificate ,_ag :privateKey ,_aba :caCert ,_gec :certificateTimestampServerURL },nil ;
};

// InitSignature initialises the PdfSignature.
func (_cd *etsiPAdES )InitSignature (sig *_ac .PdfSignature )error {if !_cd ._dgc {if _cd ._ege ==nil {return _d .New ("c\u0065\u0072\u0074\u0069\u0066\u0069c\u0061\u0074\u0065\u0020\u006d\u0075\u0073\u0074\u0020n\u006f\u0074\u0020b\u0065 \u006e\u0069\u006c");
};if _cd ._ag ==nil {return _d .New ("\u0070\u0072\u0069\u0076\u0061\u0074\u0065\u004b\u0065\u0079\u0020m\u0075\u0073\u0074\u0020\u006e\u006f\u0074\u0020\u0062\u0065 \u006e\u0069\u006c");};};_abd :=*_cd ;sig .Handler =&_abd ;sig .Filter =_de .MakeName ("\u0041\u0064\u006f\u0062\u0065\u002e\u0050\u0050\u004b\u004c\u0069\u0074\u0065");
sig .SubFilter =_de .MakeName ("\u0045\u0054\u0053\u0049.C\u0041\u0064\u0045\u0053\u002e\u0064\u0065\u0074\u0061\u0063\u0068\u0065\u0064");sig .Reference =nil ;_eda ,_abg :=_abd .NewDigest (sig );if _abg !=nil {return _abg ;};_ ,_abg =_eda .Write ([]byte ("\u0063\u0061\u006c\u0063\u0075\u006ca\u0074\u0065\u0020\u0074\u0068\u0065\u0020\u0043\u006f\u006e\u0074\u0065\u006et\u0073\u0020\u0066\u0069\u0065\u006c\u0064 \u0073\u0069\u007a\u0065"));
if _abg !=nil {return _abg ;};_abd ._bd =true ;_abg =_abd .Sign (sig ,_eda );_abd ._bd =false ;return _abg ;};

// InitSignature initialises the PdfSignature.
func (_ccg *adobePKCS7Detached )InitSignature (sig *_ac .PdfSignature )error {if !_ccg ._bfe {if _ccg ._deg ==nil {return _d .New ("c\u0065\u0072\u0074\u0069\u0066\u0069c\u0061\u0074\u0065\u0020\u006d\u0075\u0073\u0074\u0020n\u006f\u0074\u0020b\u0065 \u006e\u0069\u006c");
};if _ccg ._ddce ==nil {return _d .New ("\u0070\u0072\u0069\u0076\u0061\u0074\u0065\u004b\u0065\u0079\u0020m\u0075\u0073\u0074\u0020\u006e\u006f\u0074\u0020\u0062\u0065 \u006e\u0069\u006c");};};_aaba :=*_ccg ;sig .Handler =&_aaba ;sig .Filter =_de .MakeName ("\u0041\u0064\u006f\u0062\u0065\u002e\u0050\u0050\u004b\u004c\u0069\u0074\u0065");
sig .SubFilter =_de .MakeName ("\u0061\u0064\u0062\u0065.p\u006b\u0063\u0073\u0037\u002e\u0064\u0065\u0074\u0061\u0063\u0068\u0065\u0064");sig .Reference =nil ;_dfeb ,_edeca :=_aaba .NewDigest (sig );if _edeca !=nil {return _edeca ;};_dfeb .Write ([]byte ("\u0063\u0061\u006c\u0063\u0075\u006ca\u0074\u0065\u0020\u0074\u0068\u0065\u0020\u0043\u006f\u006e\u0074\u0065\u006et\u0073\u0020\u0066\u0069\u0065\u006c\u0064 \u0073\u0069\u007a\u0065"));
return _aaba .Sign (sig ,_dfeb );};

// DocTimeStampOpts defines options for configuring the timestamp handler.
type DocTimeStampOpts struct{

// SignatureSize is the estimated size of the signature contents in bytes.
// If not provided, a default signature size of 4192 is used.
// The signing process will report the model.ErrSignNotEnoughSpace error
// if the estimated signature size is smaller than the actual size of the
// signature.
SignatureSize int ;

// Client is the timestamp client used to make the signature request.
// If no client is provided, a default one is used.
Client *_cc .TimestampClient ;};func _bege (_gda _fe .ObjectIdentifier )(_aae .Hash ,error ){switch {case _gda .Equal (_dg .OIDDigestAlgorithmSHA1 ),_gda .Equal (_dg .OIDDigestAlgorithmECDSASHA1 ),_gda .Equal (_dg .OIDDigestAlgorithmDSA ),_gda .Equal (_dg .OIDDigestAlgorithmDSASHA1 ),_gda .Equal (_dg .OIDEncryptionAlgorithmRSA ):return _aae .SHA1 ,nil ;
case _gda .Equal (_dg .OIDDigestAlgorithmSHA256 ),_gda .Equal (_dg .OIDDigestAlgorithmECDSASHA256 ):return _aae .SHA256 ,nil ;case _gda .Equal (_dg .OIDDigestAlgorithmSHA384 ),_gda .Equal (_dg .OIDDigestAlgorithmECDSASHA384 ):return _aae .SHA384 ,nil ;
case _gda .Equal (_dg .OIDDigestAlgorithmSHA512 ),_gda .Equal (_dg .OIDDigestAlgorithmECDSASHA512 ):return _aae .SHA512 ,nil ;};return _aae .Hash (0),_dg .ErrUnsupportedAlgorithm ;};

// NewEtsiPAdESLevelB creates a new Adobe.PPKLite ETSI.CAdES.detached Level B signature handler.
func NewEtsiPAdESLevelB (privateKey *_e .PrivateKey ,certificate *_aa .Certificate ,caCert *_aa .Certificate )(_ac .SignatureHandler ,error ){return &etsiPAdES {_ege :certificate ,_ag :privateKey ,_aba :caCert },nil ;};func (_aff *etsiPAdES )buildCertChain (_abfe ,_abe []*_aa .Certificate )([]*_aa .Certificate ,map[string ]*_aa .Certificate ,error ){_efc :=map[string ]*_aa .Certificate {};
for _ ,_eged :=range _abfe {_efc [_eged .Subject .CommonName ]=_eged ;};_fce :=_abfe ;for _ ,_abab :=range _abe {_cab :=_abab .Subject .CommonName ;if _ ,_cgea :=_efc [_cab ];_cgea {continue ;};_efc [_cab ]=_abab ;_fce =append (_fce ,_abab );};if len (_fce )==0{return nil ,nil ,_ac .ErrSignNoCertificates ;
};var _efe error ;for _bac :=_fce [0];_bac !=nil &&!_aff .CertClient .IsCA (_bac );{var _bef *_aa .Certificate ;_ ,_bace :=_efc [_bac .Issuer .CommonName ];if !_bace {if _bef ,_efe =_aff .CertClient .GetIssuer (_bac );_efe !=nil {_fg .Log .Debug ("W\u0041\u0052\u004e\u003a\u0020\u0043\u006f\u0075\u006cd\u0020\u006e\u006f\u0074\u0020\u0072\u0065tr\u0069\u0065\u0076\u0065 \u0063\u0065\u0072\u0074\u0069\u0066\u0069\u0063\u0061te\u0020\u0069s\u0073\u0075\u0065\u0072\u003a\u0020\u0025\u0076",_efe );
break ;};_efc [_bac .Issuer .CommonName ]=_bef ;_fce =append (_fce ,_bef );}else {break ;};_bac =_bef ;};return _fce ,_efc ,nil ;};

// InitSignature initialises the PdfSignature.
func (_bbf *adobeX509RSASHA1 )InitSignature (sig *_ac .PdfSignature )error {if _bbf ._eed ==nil {return _d .New ("c\u0065\u0072\u0074\u0069\u0066\u0069c\u0061\u0074\u0065\u0020\u006d\u0075\u0073\u0074\u0020n\u006f\u0074\u0020b\u0065 \u006e\u0069\u006c");
};if _bbf ._fae ==nil &&_bbf ._agf ==nil {return _d .New ("\u006d\u0075\u0073\u0074\u0020\u0070\u0072o\u0076\u0069\u0064e\u0020\u0065\u0069t\u0068\u0065r\u0020\u0061\u0020\u0070\u0072\u0069v\u0061te\u0020\u006b\u0065\u0079\u0020\u006f\u0072\u0020\u0061\u0020\u0073\u0069\u0067\u006e\u0069\u006e\u0067\u0020\u0066\u0075\u006e\u0063\u0074\u0069\u006f\u006e");
};_fd :=*_bbf ;sig .Handler =&_fd ;sig .Filter =_de .MakeName ("\u0041\u0064\u006f\u0062\u0065\u002e\u0050\u0050\u004b\u004c\u0069\u0074\u0065");sig .SubFilter =_de .MakeName ("\u0061d\u0062e\u002e\u0078\u0035\u0030\u0039.\u0072\u0073a\u005f\u0073\u0068\u0061\u0031");
sig .Cert =_de .MakeString (string (_fd ._eed .Raw ));sig .Reference =nil ;_dca ,_acf :=_fd .NewDigest (sig );if _acf !=nil {return _acf ;};_dca .Write ([]byte ("\u0063\u0061\u006c\u0063\u0075\u006ca\u0074\u0065\u0020\u0074\u0068\u0065\u0020\u0043\u006f\u006e\u0074\u0065\u006et\u0073\u0020\u0066\u0069\u0065\u006c\u0064 \u0073\u0069\u007a\u0065"));
return _fd .sign (sig ,_dca ,_bbf ._cga );};

// NewDocMDPHandler returns the new DocMDP handler with the specific DocMDP restriction level.
func NewDocMDPHandler (handler _ac .SignatureHandler ,permission _daf .DocMDPPermission )(_ac .SignatureHandler ,error ){return &DocMDPHandler {_fa :handler ,Permission :permission },nil ;};

// IsApplicable returns true if the signature handler is applicable for the PdfSignature.
func (_ddd *adobeX509RSASHA1 )IsApplicable (sig *_ac .PdfSignature )bool {if sig ==nil ||sig .Filter ==nil ||sig .SubFilter ==nil {return false ;};return (*sig .Filter =="A\u0064\u006f\u0062\u0065\u002e\u0050\u0050\u004b\u004d\u0053"||*sig .Filter =="\u0041\u0064\u006f\u0062\u0065\u002e\u0050\u0050\u004b\u004c\u0069\u0074\u0065")&&*sig .SubFilter =="\u0061d\u0062e\u002e\u0078\u0035\u0030\u0039.\u0072\u0073a\u005f\u0073\u0068\u0061\u0031";
};

// IsApplicable returns true if the signature handler is applicable for the PdfSignature
func (_abdb *adobePKCS7Detached )IsApplicable (sig *_ac .PdfSignature )bool {if sig ==nil ||sig .Filter ==nil ||sig .SubFilter ==nil {return false ;};return (*sig .Filter =="A\u0064\u006f\u0062\u0065\u002e\u0050\u0050\u004b\u004d\u0053"||*sig .Filter =="\u0041\u0064\u006f\u0062\u0065\u002e\u0050\u0050\u004b\u004c\u0069\u0074\u0065")&&*sig .SubFilter =="\u0061\u0064\u0062\u0065.p\u006b\u0063\u0073\u0037\u002e\u0064\u0065\u0074\u0061\u0063\u0068\u0065\u0064";
};

// NewAdobePKCS7Detached creates a new Adobe.PPKMS/Adobe.PPKLite adbe.pkcs7.detached signature handler.
// Both parameters may be nil for the signature validation.
func NewAdobePKCS7Detached (privateKey *_e .PrivateKey ,certificate *_aa .Certificate )(_ac .SignatureHandler ,error ){return &adobePKCS7Detached {_deg :certificate ,_ddce :privateKey },nil ;};

// NewDigest creates a new digest.
func (_fca *etsiPAdES )NewDigest (_ *_ac .PdfSignature )(_ac .Hasher ,error ){return _gc .NewBuffer (nil ),nil ;};

// Sign sets the Contents fields.
func (_bbe *adobePKCS7Detached )Sign (sig *_ac .PdfSignature ,digest _ac .Hasher )error {if _bbe ._bfe {_ece :=_bbe ._ggc ;if _ece <=0{_ece =8192;};sig .Contents =_de .MakeHexString (string (make ([]byte ,_ece )));return nil ;};_bca ,_gga :=digest .(*_gc .Buffer );
if !_gga {return _f .Errorf ("c\u0061s\u0074\u0020\u0074\u006f\u0020\u0062\u0075\u0066f\u0065\u0072\u0020\u0066ai\u006c\u0073");};_dace ,_fbf :=_dg .NewSignedData (_bca .Bytes ());if _fbf !=nil {return _fbf ;};if _dffd :=_dace .AddSigner (_bbe ._deg ,_bbe ._ddce ,_dg .SignerInfoConfig {});
_dffd !=nil {return _dffd ;};_dace .Detach ();_ceed ,_fbf :=_dace .Finish ();if _fbf !=nil {return _fbf ;};_daa :=make ([]byte ,8192);copy (_daa ,_ceed );sig .Contents =_de .MakeHexString (string (_daa ));return nil ;};

// NewDigest creates a new digest.
func (_fcc *adobePKCS7Detached )NewDigest (sig *_ac .PdfSignature )(_ac .Hasher ,error ){return _gc .NewBuffer (nil ),nil ;};

// Sign sets the Contents fields for the PdfSignature.
func (_eea *docTimeStamp )Sign (sig *_ac .PdfSignature ,digest _ac .Hasher )error {_egdcf ,_feba :=_cc .NewTimestampRequest (digest .(*_gc .Buffer ),&_ce .RequestOptions {Hash :_eea ._dcbf ,Certificates :true });if _feba !=nil {return _feba ;};_dcad :=_eea ._fbfa ;
if _dcad ==nil {_dcad =_cc .NewTimestampClient ();};_edb ,_feba :=_dcad .GetEncodedToken (_eea ._egdce ,_egdcf );if _feba !=nil {return _feba ;};_edd :=len (_edb );if _eea ._fccd > 0&&_edd > _eea ._fccd {return _ac .ErrSignNotEnoughSpace ;};if _edd > 0{_eea ._fccd =_edd +128;
};if sig .Contents !=nil {_geb :=sig .Contents .Bytes ();copy (_geb ,_edb );_edb =_geb ;};sig .Contents =_de .MakeHexString (string (_edb ));return nil ;};func (_bbff *docTimeStamp )getCertificate (_gee *_ac .PdfSignature )(*_aa .Certificate ,error ){_ffd ,_edaf :=_gee .GetCerts ();
if _edaf !=nil {return nil ,_edaf ;};return _ffd [0],nil ;};

// Validate implementation of the SignatureHandler interface
// This check is impossible without checking the document's content.
// Please, use ValidateWithOpts with the PdfParser.
func (_ed *DocMDPHandler )Validate (sig *_ac .PdfSignature ,digest _ac .Hasher )(_ac .SignatureValidationResult ,error ){return _ac .SignatureValidationResult {},_d .New ("i\u006d\u0070\u006f\u0073\u0073\u0069b\u006c\u0065\u0020\u0076\u0061\u006ci\u0064\u0061\u0074\u0069\u006f\u006e\u0020w\u0069\u0074\u0068\u006f\u0075\u0074\u0020\u0070\u0061\u0072s\u0065");
};func (_gdbc *adobeX509RSASHA1 )sign (_bg *_ac .PdfSignature ,_bcg _ac .Hasher ,_abgf bool )error {if !_abgf {return _gdbc .Sign (_bg ,_bcg );};_edg ,_ead :=_gdbc ._eed .PublicKey .(*_e .PublicKey );if !_ead {return _f .Errorf ("i\u006e\u0076\u0061\u006c\u0069\u0064 \u0070\u0075\u0062\u006c\u0069\u0063\u0020\u006b\u0065y\u0020\u0074\u0079p\u0065:\u0020\u0025\u0054",_edg );
};_fbb ,_egf :=_fe .Marshal (make ([]byte ,_edg .Size ()));if _egf !=nil {return _egf ;};_bg .Contents =_de .MakeHexString (string (_fbb ));return nil ;};

// NewEmptyAdobePKCS7Detached creates a new Adobe.PPKMS/Adobe.PPKLite adbe.pkcs7.detached
// signature handler. The generated signature is empty and of size signatureLen.
// The signatureLen parameter can be 0 for the signature validation.
func NewEmptyAdobePKCS7Detached (signatureLen int )(_ac .SignatureHandler ,error ){return &adobePKCS7Detached {_bfe :true ,_ggc :signatureLen },nil ;};type adobeX509RSASHA1 struct{_fae *_e .PrivateKey ;_eed *_aa .Certificate ;_agf SignFunc ;_cga bool ;
_bff _aae .Hash ;};

// NewAdobeX509RSASHA1CustomWithOpts creates a new Adobe.PPKMS/Adobe.PPKLite
// adbe.x509.rsa_sha1 signature handler with a custom signing function. The
// handler is configured based on the provided options. If no options are
// provided, default options will be used. Both the certificate and the sign
// function can be nil for the signature validation.
func NewAdobeX509RSASHA1CustomWithOpts (certificate *_aa .Certificate ,signFunc SignFunc ,opts *AdobeX509RSASHA1Opts )(_ac .SignatureHandler ,error ){if opts ==nil {opts =&AdobeX509RSASHA1Opts {};};return &adobeX509RSASHA1 {_eed :certificate ,_agf :signFunc ,_cga :opts .EstimateSize ,_bff :opts .Algorithm },nil ;
};func (_gea *etsiPAdES )addDss (_ebf ,_adg []*_aa .Certificate ,_gfff *RevocationInfoArchival )(int ,error ){_ded ,_ccfg ,_acd :=_gea .buildCertChain (_ebf ,_adg );if _acd !=nil {return 0,_acd ;};_bbg ,_acd :=_gea .getCerts (_ded );if _acd !=nil {return 0,_acd ;
};var _ccdc ,_gffe [][]byte ;if _gea .OCSPClient !=nil {_ccdc ,_acd =_gea .getOCSPs (_ded ,_ccfg );if _acd !=nil {return 0,_acd ;};};if _gea .CRLClient !=nil {_gffe ,_acd =_gea .getCRLs (_ded );if _acd !=nil {return 0,_acd ;};};if !_gea ._bd {_ ,_acd =_gea ._dfe .AddCerts (_bbg );
if _acd !=nil {return 0,_acd ;};_ ,_acd =_gea ._dfe .AddOCSPs (_ccdc );if _acd !=nil {return 0,_acd ;};_ ,_acd =_gea ._dfe .AddCRLs (_gffe );if _acd !=nil {return 0,_acd ;};};_fea :=0;for _ ,_ace :=range _gffe {_fea +=len (_ace );_gfff .Crl =append (_gfff .Crl ,_fe .RawValue {FullBytes :_ace });
};for _ ,_fbc :=range _ccdc {_fea +=len (_fbc );_gfff .Ocsp =append (_gfff .Ocsp ,_fe .RawValue {FullBytes :_fbc });};return _fea ,nil ;};

// NewAdobeX509RSASHA1Custom creates a new Adobe.PPKMS/Adobe.PPKLite
// adbe.x509.rsa_sha1 signature handler with a custom signing function. Both the
// certificate and the sign function can be nil for the signature validation.
// NOTE: the handler will do a mock Sign when initializing the signature in
// order to estimate the signature size. Use NewAdobeX509RSASHA1CustomWithOpts
// for configuring the handler to estimate the signature size.
func NewAdobeX509RSASHA1Custom (certificate *_aa .Certificate ,signFunc SignFunc )(_ac .SignatureHandler ,error ){return &adobeX509RSASHA1 {_eed :certificate ,_agf :signFunc },nil ;};

// NewDocTimeStampWithOpts returns a new DocTimeStamp configured using the
// specified options. If no options are provided, default options will be used.
// Both the timestamp server URL and the hash algorithm can be empty for the
// signature validation.
// The following hash algorithms are supported:
// crypto.SHA1, crypto.SHA256, crypto.SHA384, crypto.SHA512.
func NewDocTimeStampWithOpts (timestampServerURL string ,hashAlgorithm _aae .Hash ,opts *DocTimeStampOpts )(_ac .SignatureHandler ,error ){if opts ==nil {opts =&DocTimeStampOpts {};};if opts .SignatureSize <=0{opts .SignatureSize =4192;};return &docTimeStamp {_egdce :timestampServerURL ,_dcbf :hashAlgorithm ,_fccd :opts .SignatureSize ,_fbfa :opts .Client },nil ;
};func (_fag *etsiPAdES )getOCSPs (_acb []*_aa .Certificate ,_dgd map[string ]*_aa .Certificate )([][]byte ,error ){_dde :=make ([][]byte ,0,len (_acb ));for _ ,_ccc :=range _acb {for _ ,_ccb :=range _ccc .OCSPServer {if _fag .CertClient .IsCA (_ccc ){continue ;
};_afd ,_feg :=_dgd [_ccc .Issuer .CommonName ];if !_feg {_fg .Log .Debug ("\u0057\u0041\u0052\u004e:\u0020\u0053\u006b\u0069\u0070\u0070\u0069\u006e\u0067 \u004f\u0043\u0053\u0050\u0020\u0072\u0065\u0071\u0075\u0065\u0073\u0074\u003a\u0020\u0069\u0073\u0073\u0075e\u0072\u0020\u0063\u0065\u0072t\u0069\u0066\u0069\u0063\u0061\u0074\u0065\u0020\u006e\u006f\u0074\u0020\u0066\u006f\u0075\u006e\u0064");
continue ;};_ ,_acc ,_ba :=_fag .OCSPClient .MakeRequest (_ccb ,_ccc ,_afd );if _ba !=nil {_fg .Log .Debug ("\u0057\u0041\u0052\u004e:\u0020\u004f\u0043\u0053\u0050\u0020\u0072\u0065\u0071\u0075e\u0073t\u0020\u0065\u0072\u0072\u006f\u0072\u003a \u0025\u0076",_ba );
continue ;};_dde =append (_dde ,_acc );};};return _dde ,nil ;};

// Validate validates PdfSignature.
func (_edc *docTimeStamp )Validate (sig *_ac .PdfSignature ,digest _ac .Hasher )(_ac .SignatureValidationResult ,error ){_decf :=sig .Contents .Bytes ();_fdgc ,_aade :=_dg .Parse (_decf );if _aade !=nil {return _ac .SignatureValidationResult {},_aade ;
};if _aade =_fdgc .Verify ();_aade !=nil {return _ac .SignatureValidationResult {},_aade ;};var _bec timestampInfo ;_ ,_aade =_fe .Unmarshal (_fdgc .Content ,&_bec );if _aade !=nil {return _ac .SignatureValidationResult {},_aade ;};_gde ,_aade :=_bege (_bec .MessageImprint .HashAlgorithm .Algorithm );
if _aade !=nil {return _ac .SignatureValidationResult {},_aade ;};_ebbe :=_gde .New ();_bcb ,_bee :=digest .(*_gc .Buffer );if !_bee {return _ac .SignatureValidationResult {},_f .Errorf ("c\u0061s\u0074\u0020\u0074\u006f\u0020\u0062\u0075\u0066f\u0065\u0072\u0020\u0066ai\u006c\u0073");
};_ebbe .Write (_bcb .Bytes ());_ceee :=_ebbe .Sum (nil );_bdeb :=_ac .SignatureValidationResult {IsSigned :true ,IsVerified :_gc .Equal (_ceee ,_bec .MessageImprint .HashedMessage ),GeneralizedTime :_bec .GeneralizedTime };return _bdeb ,nil ;};

// Validate validates PdfSignature.
func (_dbf *etsiPAdES )Validate (sig *_ac .PdfSignature ,digest _ac .Hasher )(_ac .SignatureValidationResult ,error ){_dcb :=sig .Contents .Bytes ();_cce ,_aadf :=_dg .Parse (_dcb );if _aadf !=nil {return _ac .SignatureValidationResult {},_aadf ;};_gfgf ,_bdg :=digest .(*_gc .Buffer );
if !_bdg {return _ac .SignatureValidationResult {},_f .Errorf ("c\u0061s\u0074\u0020\u0074\u006f\u0020\u0062\u0075\u0066f\u0065\u0072\u0020\u0066ai\u006c\u0073");};_cce .Content =_gfgf .Bytes ();if _aadf =_cce .Verify ();_aadf !=nil {return _ac .SignatureValidationResult {},_aadf ;
};_eef :=false ;_gd :=false ;var _fgaa _a .Time ;for _ ,_accf :=range _cce .Signers {_dcf :=_accf .EncryptedDigest ;var _gef RevocationInfoArchival ;_aadf =_cce .UnmarshalSignedAttribute (_dg .OIDAttributeAdobeRevocation ,&_gef );if _aadf ==nil {if len (_gef .Crl )> 0{_gd =true ;
};if len (_gef .Ocsp )> 0{_eef =true ;};};for _ ,_ebd :=range _accf .UnauthenticatedAttributes {if _ebd .Type .Equal (_dg .OIDAttributeTimeStampToken ){_gcf ,_eag :=_ce .Parse (_ebd .Value .Bytes );if _eag !=nil {return _ac .SignatureValidationResult {},_eag ;
};_fgaa =_gcf .Time ;_bag :=_gcf .HashAlgorithm .New ();_bag .Write (_dcf );if !_gc .Equal (_bag .Sum (nil ),_gcf .HashedMessage ){return _ac .SignatureValidationResult {},_f .Errorf ("\u0048\u0061\u0073\u0068\u0020i\u006e\u0020\u0074\u0069\u006d\u0065\u0073\u0074\u0061\u006d\u0070\u0020\u0069s\u0020\u0064\u0069\u0066\u0066\u0065\u0072\u0065\u006e\u0074\u0020\u0066\u0072\u006f\u006d\u0020\u0070\u006b\u0063\u0073\u0037");
};break ;};};};_gdb :=_ac .SignatureValidationResult {IsSigned :true ,IsVerified :true ,IsCrlFound :_gd ,IsOcspFound :_eef ,GeneralizedTime :_fgaa };return _gdb ,nil ;};

// NewDigest creates a new digest.
func (_gdc *adobeX509RSASHA1 )NewDigest (sig *_ac .PdfSignature )(_ac .Hasher ,error ){if _gba ,_caeg :=_gdc .getHashAlgorithm (sig );_gba !=0&&_caeg ==nil {return _gba .New (),nil ;};return _gdbd .New (),nil ;};

// NewEtsiPAdESLevelLT creates a new Adobe.PPKLite ETSI.CAdES.detached Level LT signature handler.
func NewEtsiPAdESLevelLT (privateKey *_e .PrivateKey ,certificate *_aa .Certificate ,caCert *_aa .Certificate ,certificateTimestampServerURL string ,appender *_ac .PdfAppender )(_ac .SignatureHandler ,error ){_egd :=appender .Reader .DSS ;if _egd ==nil {_egd =_ac .NewDSS ();
};if _af :=_egd .GenerateHashMaps ();_af !=nil {return nil ,_af ;};return &etsiPAdES {_ege :certificate ,_ag :privateKey ,_aba :caCert ,_gec :certificateTimestampServerURL ,CertClient :_cc .NewCertClient (),OCSPClient :_cc .NewOCSPClient (),CRLClient :_cc .NewCRLClient (),_dba :appender ,_dfe :_egd },nil ;
};

// Sign sets the Contents fields for the PdfSignature.
func (_bfg *adobeX509RSASHA1 )Sign (sig *_ac .PdfSignature ,digest _ac .Hasher )error {var _ebb []byte ;var _baf error ;if _bfg ._agf !=nil {_ebb ,_baf =_bfg ._agf (sig ,digest );if _baf !=nil {return _baf ;};}else {_abac ,_agfg :=digest .(_da .Hash );
if !_agfg {return _d .New ("\u0068a\u0073h\u0020\u0074\u0079\u0070\u0065\u0020\u0065\u0072\u0072\u006f\u0072");};_bfgb :=_gdbd ;if _bfg ._bff !=0{_bfgb =_bfg ._bff ;};_ebb ,_baf =_e .SignPKCS1v15 (_fc .Reader ,_bfg ._fae ,_bfgb ,_abac .Sum (nil ));if _baf !=nil {return _baf ;
};};_ebb ,_baf =_fe .Marshal (_ebb );if _baf !=nil {return _baf ;};sig .Contents =_de .MakeHexString (string (_ebb ));return nil ;};

// AdobeX509RSASHA1Opts defines options for configuring the adbe.x509.rsa_sha1
// signature handler.
type AdobeX509RSASHA1Opts struct{

// EstimateSize specifies whether the size of the signature contents
// should be estimated based on the modulus size of the public key
// extracted from the signing certificate. If set to false, a mock Sign
// call is made in order to estimate the size of the signature contents.
EstimateSize bool ;

// Algorithm specifies the algorithm used for performing signing.
// If not specified, defaults to SHA1.
Algorithm _aae .Hash ;};

// Validate validates PdfSignature.
func (_ecf *adobePKCS7Detached )Validate (sig *_ac .PdfSignature ,digest _ac .Hasher )(_ac .SignatureValidationResult ,error ){_dga :=sig .Contents .Bytes ();_dge ,_ceeg :=_dg .Parse (_dga );if _ceeg !=nil {return _ac .SignatureValidationResult {},_ceeg ;
};_aef ,_acce :=digest .(*_gc .Buffer );if !_acce {return _ac .SignatureValidationResult {},_f .Errorf ("c\u0061s\u0074\u0020\u0074\u006f\u0020\u0062\u0075\u0066f\u0065\u0072\u0020\u0066ai\u006c\u0073");};_dge .Content =_aef .Bytes ();if _ceeg =_dge .Verify ();
_ceeg !=nil {return _ac .SignatureValidationResult {},_ceeg ;};return _ac .SignatureValidationResult {IsSigned :true ,IsVerified :true },nil ;};func _bge (_eae []byte ,_abb int )(_bab []byte ){_fda :=len (_eae );if _fda > _abb {_fda =_abb ;};_bab =make ([]byte ,_abb );
copy (_bab [len (_bab )-_fda :],_eae );return ;};func (_ceb *etsiPAdES )getCerts (_beb []*_aa .Certificate )([][]byte ,error ){_ede :=make ([][]byte ,0,len (_beb ));for _ ,_cfa :=range _beb {_ede =append (_ede ,_cfa .Raw );};return _ede ,nil ;};

// RevocationInfoArchival is OIDAttributeAdobeRevocation attribute.
type RevocationInfoArchival struct{Crl []_fe .RawValue `asn1:"explicit,tag:0,optional"`;Ocsp []_fe .RawValue `asn1:"explicit,tag:1,optional"`;OtherRevInfo []_fe .RawValue `asn1:"explicit,tag:2,optional"`;};type etsiPAdES struct{_ag *_e .PrivateKey ;_ege *_aa .Certificate ;
_dgc bool ;_bd bool ;_aba *_aa .Certificate ;_gec string ;

// CertClient is the client used to retrieve certificates.
CertClient *_cc .CertClient ;

// OCSPClient is the client used to retrieve OCSP validation information.
OCSPClient *_cc .OCSPClient ;

// CRLClient is the client used to retrieve CRL validation information.
CRLClient *_cc .CRLClient ;_dba *_ac .PdfAppender ;_dfe *_ac .DSS ;};

// DocMDPHandler describes handler for the DocMDP realization.
type DocMDPHandler struct{_fa _ac .SignatureHandler ;Permission _daf .DocMDPPermission ;};

// NewDigest creates a new digest.
func (_eg *DocMDPHandler )NewDigest (sig *_ac .PdfSignature )(_ac .Hasher ,error ){return _eg ._fa .NewDigest (sig );};

// Sign adds a new reference to signature's references array.
func (_dbg *DocMDPHandler )Sign (sig *_ac .PdfSignature ,digest _ac .Hasher )error {return _dbg ._fa .Sign (sig ,digest );};

// NewAdobeX509RSASHA1 creates a new Adobe.PPKMS/Adobe.PPKLite
// adbe.x509.rsa_sha1 signature handler. Both the private key and the
// certificate can be nil for the signature validation.
func NewAdobeX509RSASHA1 (privateKey *_e .PrivateKey ,certificate *_aa .Certificate )(_ac .SignatureHandler ,error ){return &adobeX509RSASHA1 {_eed :certificate ,_fae :privateKey },nil ;};type adobePKCS7Detached struct{_ddce *_e .PrivateKey ;_deg *_aa .Certificate ;
_bfe bool ;_ggc int ;};func (_aec *adobePKCS7Detached )getCertificate (_adfa *_ac .PdfSignature )(*_aa .Certificate ,error ){if _aec ._deg !=nil {return _aec ._deg ,nil ;};_fad ,_ade :=_adfa .GetCerts ();if _ade !=nil {return nil ,_ade ;};return _fad [0],nil ;
};

// IsApplicable returns true if the signature handler is applicable for the PdfSignature.
func (_gg *etsiPAdES )IsApplicable (sig *_ac .PdfSignature )bool {if sig ==nil ||sig .Filter ==nil ||sig .SubFilter ==nil {return false ;};return (*sig .Filter =="\u0041\u0064\u006f\u0062\u0065\u002e\u0050\u0050\u004b\u004c\u0069\u0074\u0065")&&*sig .SubFilter =="\u0045\u0054\u0053\u0049.C\u0041\u0064\u0045\u0053\u002e\u0064\u0065\u0074\u0061\u0063\u0068\u0065\u0064";
};

// NewDocTimeStamp creates a new DocTimeStamp signature handler.
// Both the timestamp server URL and the hash algorithm can be empty for the
// signature validation.
// The following hash algorithms are supported:
// crypto.SHA1, crypto.SHA256, crypto.SHA384, crypto.SHA512.
// NOTE: the handler will do a mock Sign when initializing the signature
// in order to estimate the signature size. Use NewDocTimeStampWithOpts
// for providing the signature size.
func NewDocTimeStamp (timestampServerURL string ,hashAlgorithm _aae .Hash )(_ac .SignatureHandler ,error ){return &docTimeStamp {_egdce :timestampServerURL ,_dcbf :hashAlgorithm },nil ;};func (_dc *etsiPAdES )makeTimestampRequest (_ca string ,_cdf []byte )(_fe .RawValue ,error ){_ef :=_aae .SHA512 .New ();
_ef .Write (_cdf );_aaf :=_ef .Sum (nil );_egdc :=_ce .Request {HashAlgorithm :_aae .SHA512 ,HashedMessage :_aaf ,Certificates :true ,Extensions :nil ,ExtraExtensions :nil };_aad :=_cc .NewTimestampClient ();_fga ,_cg :=_aad .GetEncodedToken (_ca ,&_egdc );
if _cg !=nil {return _fe .NullRawValue ,_cg ;};return _fe .RawValue {FullBytes :_fga },nil ;};

// InitSignature initialises the PdfSignature.
func (_cfaa *docTimeStamp )InitSignature (sig *_ac .PdfSignature )error {_bcc :=*_cfaa ;sig .Type =_de .MakeName ("\u0044\u006f\u0063T\u0069\u006d\u0065\u0053\u0074\u0061\u006d\u0070");sig .Handler =&_bcc ;sig .Filter =_de .MakeName ("\u0041\u0064\u006f\u0062\u0065\u002e\u0050\u0050\u004b\u004c\u0069\u0074\u0065");
sig .SubFilter =_de .MakeName ("\u0045\u0054\u0053I\u002e\u0052\u0046\u0043\u0033\u0031\u0036\u0031");sig .Reference =nil ;if _cfaa ._fccd > 0{sig .Contents =_de .MakeHexString (string (make ([]byte ,_cfaa ._fccd )));}else {_gge ,_bgd :=_cfaa .NewDigest (sig );
if _bgd !=nil {return _bgd ;};_gge .Write ([]byte ("\u0063\u0061\u006c\u0063\u0075\u006ca\u0074\u0065\u0020\u0074\u0068\u0065\u0020\u0043\u006f\u006e\u0074\u0065\u006et\u0073\u0020\u0066\u0069\u0065\u006c\u0064 \u0073\u0069\u007a\u0065"));if _bgd =_bcc .Sign (sig ,_gge );
_bgd !=nil {return _bgd ;};_cfaa ._fccd =_bcc ._fccd ;};return nil ;};const _gdbd =_aae .SHA1 ;

// NewDigest creates a new digest.
func (_bacc *docTimeStamp )NewDigest (sig *_ac .PdfSignature )(_ac .Hasher ,error ){return _gc .NewBuffer (nil ),nil ;};func _bafb (_dgdc *_e .PublicKey ,_dbfb []byte )_aae .Hash {_dbd :=_dgdc .Size ();if _dbd !=len (_dbfb ){return 0;};_aafa :=func (_egedg *_c .Int ,_aeg *_e .PublicKey ,_aed *_c .Int )*_c .Int {_bbgc :=_c .NewInt (int64 (_aeg .E ));
_egedg .Exp (_aed ,_bbgc ,_aeg .N );return _egedg ;};_ddgg :=new (_c .Int ).SetBytes (_dbfb );_beg :=_aafa (new (_c .Int ),_dgdc ,_ddgg );_abed :=_bge (_beg .Bytes (),_dbd );if _abed [0]!=0||_abed [1]!=1{return 0;};_ccad :=[]struct{Hash _aae .Hash ;Prefix []byte ;
}{{Hash :_aae .SHA1 ,Prefix :[]byte {0x30,0x21,0x30,0x09,0x06,0x05,0x2b,0x0e,0x03,0x02,0x1a,0x05,0x00,0x04,0x14}},{Hash :_aae .SHA256 ,Prefix :[]byte {0x30,0x31,0x30,0x0d,0x06,0x09,0x60,0x86,0x48,0x01,0x65,0x03,0x04,0x02,0x01,0x05,0x00,0x04,0x20}},{Hash :_aae .SHA384 ,Prefix :[]byte {0x30,0x41,0x30,0x0d,0x06,0x09,0x60,0x86,0x48,0x01,0x65,0x03,0x04,0x02,0x02,0x05,0x00,0x04,0x30}},{Hash :_aae .SHA512 ,Prefix :[]byte {0x30,0x51,0x30,0x0d,0x06,0x09,0x60,0x86,0x48,0x01,0x65,0x03,0x04,0x02,0x03,0x05,0x00,0x04,0x40}},{Hash :_aae .RIPEMD160 ,Prefix :[]byte {0x30,0x20,0x30,0x08,0x06,0x06,0x28,0xcf,0x06,0x03,0x00,0x31,0x04,0x14}}};
for _ ,_fef :=range _ccad {_bda :=_fef .Hash .Size ();_ffgf :=len (_fef .Prefix )+_bda ;if _gc .Equal (_abed [_dbd -_ffgf :_dbd -_bda ],_fef .Prefix ){return _fef .Hash ;};};return 0;};

// InitSignature initialization of the DocMDP signature.
func (_bbc *DocMDPHandler )InitSignature (sig *_ac .PdfSignature )error {_gb :=_bbc ._fa .InitSignature (sig );if _gb !=nil {return _gb ;};sig .Handler =_bbc ;if sig .Reference ==nil {sig .Reference =_de .MakeArray ();};sig .Reference .Append (_ac .NewPdfSignatureReferenceDocMDP (_ac .NewPdfTransformParamsDocMDP (_bbc .Permission )).ToPdfObject ());
return nil ;};

// Validate validates PdfSignature.
func (_cca *adobeX509RSASHA1 )Validate (sig *_ac .PdfSignature ,digest _ac .Hasher )(_ac .SignatureValidationResult ,error ){_eagf ,_dcee :=_cca .getCertificate (sig );if _dcee !=nil {return _ac .SignatureValidationResult {},_dcee ;};_acg :=sig .Contents .Bytes ();
var _fdg []byte ;if _ ,_caea :=_fe .Unmarshal (_acg ,&_fdg );_caea !=nil {return _ac .SignatureValidationResult {},_caea ;};_abef ,_baca :=digest .(_da .Hash );if !_baca {return _ac .SignatureValidationResult {},_d .New ("\u0068a\u0073h\u0020\u0074\u0079\u0070\u0065\u0020\u0065\u0072\u0072\u006f\u0072");
};_gad ,_ :=_cca .getHashAlgorithm (sig );if _gad ==0{_gad =_gdbd ;};if _gfgb :=_e .VerifyPKCS1v15 (_eagf .PublicKey .(*_e .PublicKey ),_gad ,_abef .Sum (nil ),_fdg );_gfgb !=nil {return _ac .SignatureValidationResult {},_gfgb ;};return _ac .SignatureValidationResult {IsSigned :true ,IsVerified :true },nil ;
};func (_dac *etsiPAdES )getCRLs (_cge []*_aa .Certificate )([][]byte ,error ){_cf :=make ([][]byte ,0,len (_cge ));for _ ,_bf :=range _cge {for _ ,_ga :=range _bf .CRLDistributionPoints {if _dac .CertClient .IsCA (_bf ){continue ;};_ccf ,_aga :=_dac .CRLClient .MakeRequest (_ga ,_bf );
if _aga !=nil {_fg .Log .Debug ("W\u0041\u0052\u004e\u003a\u0020\u0043R\u004c\u0020\u0072\u0065\u0071\u0075\u0065\u0073\u0074 \u0065\u0072\u0072o\u0072:\u0020\u0025\u0076",_aga );continue ;};_cf =append (_cf ,_ccf );};};return _cf ,nil ;};func (_egdd *adobeX509RSASHA1 )getHashAlgorithm (_fcg *_ac .PdfSignature )(_aae .Hash ,error ){_ecd ,_aabed :=_egdd .getCertificate (_fcg );
if _aabed !=nil {if _egdd ._bff !=0{return _egdd ._bff ,nil ;};return _gdbd ,_aabed ;};if _fcg .Contents !=nil {_cfd :=_fcg .Contents .Bytes ();var _bde []byte ;if _ ,_bcd :=_fe .Unmarshal (_cfd ,&_bde );_bcd ==nil {_gbeb :=_bafb (_ecd .PublicKey .(*_e .PublicKey ),_bde );
if _gbeb > 0{return _gbeb ,nil ;};};};if _egdd ._bff !=0{return _egdd ._bff ,nil ;};return _gdbd ,nil ;};