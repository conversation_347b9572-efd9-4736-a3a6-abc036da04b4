//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

// Package fdf provides support for loading form field data from Form Field Data (FDF) files.
package fdf ;import (_egc "bufio";_ge "bytes";_d "encoding/hex";_a "errors";_gc "fmt";_be "github.com/unidoc/unipdf/v3/common";_gg "github.com/unidoc/unipdf/v3/core";_gd "io";_f "os";_eg "regexp";_c "sort";_b "strconv";_e "strings";);func (_cd *fdfParser )setFileOffset (_gcg int64 ){_cd ._ace .Seek (_gcg ,_gd .SeekStart );
_cd ._ccc =_egc .NewReader (_cd ._ace );};func _acec (_bcfd string )(_gg .PdfObjectReference ,error ){_afb :=_gg .PdfObjectReference {};_feb :=_af .FindStringSubmatch (_bcfd );if len (_feb )< 3{_be .Log .Debug ("\u0045\u0072\u0072or\u0020\u0070\u0061\u0072\u0073\u0069\u006e\u0067\u0020\u0072\u0065\u0066\u0065\u0072\u0065\u006e\u0063\u0065");
return _afb ,_a .New ("\u0075n\u0061\u0062\u006c\u0065 \u0074\u006f\u0020\u0070\u0061r\u0073e\u0020r\u0065\u0066\u0065\u0072\u0065\u006e\u0063e");};_dbb ,_ecb :=_b .Atoi (_feb [1]);if _ecb !=nil {_be .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u0070a\u0072\u0073\u0069n\u0067\u0020\u006fb\u006a\u0065c\u0074\u0020\u006e\u0075\u006d\u0062e\u0072 '\u0025\u0073\u0027\u0020\u002d\u0020\u0055\u0073\u0069\u006e\u0067\u0020\u006f\u0062\u006a\u0065\u0063\u0074\u0020\u006e\u0075\u006d\u0020\u003d\u0020\u0030",_feb [1]);
return _afb ,nil ;};_afb .ObjectNumber =int64 (_dbb );_bf ,_ecb :=_b .Atoi (_feb [2]);if _ecb !=nil {_be .Log .Debug ("\u0045\u0072r\u006f\u0072\u0020\u0070\u0061r\u0073\u0069\u006e\u0067\u0020g\u0065\u006e\u0065\u0072\u0061\u0074\u0069\u006f\u006e\u0020\u006e\u0075\u006d\u0062\u0065\u0072\u0020\u0027\u0025\u0073\u0027\u0020\u002d\u0020\u0055\u0073\u0069\u006e\u0067\u0020\u0067\u0065\u006e\u0020\u003d\u0020\u0030",_feb [2]);
return _afb ,nil ;};_afb .GenerationNumber =int64 (_bf );return _afb ,nil ;};

// FieldValues implements interface model.FieldValueProvider.
// Returns a map of field names to values (PdfObjects).
func (fdf *Data )FieldValues ()(map[string ]_gg .PdfObject ,error ){_dd ,_fd :=fdf .FieldDictionaries ();if _fd !=nil {return nil ,_fd ;};var _ad []string ;for _bc :=range _dd {_ad =append (_ad ,_bc );};_c .Strings (_ad );_da :=map[string ]_gg .PdfObject {};
for _ ,_ee :=range _ad {_ac :=_dd [_ee ];_ca :=_gg .TraceToDirectObject (_ac .Get ("\u0056"));_da [_ee ]=_ca ;};return _da ,nil ;};func _cfe (_ebbe _gd .ReadSeeker )(*fdfParser ,error ){_bfb :=&fdfParser {};_bfb ._ace =_ebbe ;_bfb ._adc =map[int64 ]_gg .PdfObject {};
_abbg ,_efc ,_fef :=_bfb .parseFdfVersion ();if _fef !=nil {_be .Log .Error ("U\u006e\u0061\u0062\u006c\u0065\u0020t\u006f\u0020\u0070\u0061\u0072\u0073\u0065\u0020\u0076e\u0072\u0073\u0069o\u006e:\u0020\u0025\u0076",_fef );return nil ,_fef ;};_bfb ._ae =_abbg ;
_bfb ._bdc =_efc ;_fef =_bfb .parse ();return _bfb ,_fef ;};func (_aegd *fdfParser )parseBool ()(_gg .PdfObjectBool ,error ){_gcf ,_eff :=_aegd ._ccc .Peek (4);if _eff !=nil {return _gg .PdfObjectBool (false ),_eff ;};if (len (_gcf )>=4)&&(string (_gcf [:4])=="\u0074\u0072\u0075\u0065"){_aegd ._ccc .Discard (4);
return _gg .PdfObjectBool (true ),nil ;};_gcf ,_eff =_aegd ._ccc .Peek (5);if _eff !=nil {return _gg .PdfObjectBool (false ),_eff ;};if (len (_gcf )>=5)&&(string (_gcf [:5])=="\u0066\u0061\u006cs\u0065"){_aegd ._ccc .Discard (5);return _gg .PdfObjectBool (false ),nil ;
};return _gg .PdfObjectBool (false ),_a .New ("\u0075n\u0065\u0078\u0070\u0065c\u0074\u0065\u0064\u0020\u0062o\u006fl\u0065a\u006e\u0020\u0073\u0074\u0072\u0069\u006eg");};var _cg =_eg .MustCompile ("\u0028\u005c\u0064\u002b)\\\u0073\u002b\u0028\u005c\u0064\u002b\u0029\u005c\u0073\u002b\u006f\u0062\u006a");
func (_ccg *fdfParser )parseFdfVersion ()(int ,int ,error ){_ccg ._ace .Seek (0,_gd .SeekStart );_gedg :=20;_dba :=make ([]byte ,_gedg );_ccg ._ace .Read (_dba );_aafb :=_eab .FindStringSubmatch (string (_dba ));if len (_aafb )< 3{_gae ,_fag ,_edab :=_ccg .seekFdfVersionTopDown ();
if _edab !=nil {_be .Log .Debug ("F\u0061\u0069\u006c\u0065\u0064\u0020\u0072\u0065\u0063\u006f\u0076\u0065\u0072\u0079\u0020\u002d\u0020\u0075n\u0061\u0062\u006c\u0065\u0020\u0074\u006f\u0020\u0066\u0069nd\u0020\u0076\u0065r\u0073i\u006f\u006e");return 0,0,_edab ;
};return _gae ,_fag ,nil ;};_dgf ,_dgge :=_b .Atoi (_aafb [1]);if _dgge !=nil {return 0,0,_dgge ;};_gad ,_dgge :=_b .Atoi (_aafb [2]);if _dgge !=nil {return 0,0,_dgge ;};_be .Log .Debug ("\u0046\u0064\u0066\u0020\u0076\u0065\u0072\u0073\u0069\u006f\u006e\u0020%\u0064\u002e\u0025\u0064",_dgf ,_gad );
return _dgf ,_gad ,nil ;};func (_dcc *fdfParser )parseObject ()(_gg .PdfObject ,error ){_be .Log .Trace ("\u0052e\u0061d\u0020\u0064\u0069\u0072\u0065c\u0074\u0020o\u0062\u006a\u0065\u0063\u0074");_dcc .skipSpaces ();for {_dda ,_bed :=_dcc ._ccc .Peek (2);
if _bed !=nil {return nil ,_bed ;};_be .Log .Trace ("\u0050e\u0065k\u0020\u0073\u0074\u0072\u0069\u006e\u0067\u003a\u0020\u0025\u0073",string (_dda ));if _dda [0]=='/'{_fdg ,_cae :=_dcc .parseName ();_be .Log .Trace ("\u002d\u003e\u004ea\u006d\u0065\u003a\u0020\u0027\u0025\u0073\u0027",_fdg );
return &_fdg ,_cae ;}else if _dda [0]=='('{_be .Log .Trace ("\u002d>\u0053\u0074\u0072\u0069\u006e\u0067!");return _dcc .parseString ();}else if _dda [0]=='['{_be .Log .Trace ("\u002d\u003e\u0041\u0072\u0072\u0061\u0079\u0021");return _dcc .parseArray ();
}else if (_dda [0]=='<')&&(_dda [1]=='<'){_be .Log .Trace ("\u002d>\u0044\u0069\u0063\u0074\u0021");return _dcc .parseDict ();}else if _dda [0]=='<'{_be .Log .Trace ("\u002d\u003e\u0048\u0065\u0078\u0020\u0073\u0074\u0072\u0069\u006e\u0067\u0021");return _dcc .parseHexString ();
}else if _dda [0]=='%'{_dcc .readComment ();_dcc .skipSpaces ();}else {_be .Log .Trace ("\u002d\u003eN\u0075\u006d\u0062e\u0072\u0020\u006f\u0072\u0020\u0072\u0065\u0066\u003f");_dda ,_ =_dcc ._ccc .Peek (15);_abe :=string (_dda );_be .Log .Trace ("\u0050\u0065\u0065k\u0020\u0073\u0074\u0072\u003a\u0020\u0025\u0073",_abe );
if (len (_abe )> 3)&&(_abe [:4]=="\u006e\u0075\u006c\u006c"){_bfa ,_deg :=_dcc .parseNull ();return &_bfa ,_deg ;}else if (len (_abe )> 4)&&(_abe [:5]=="\u0066\u0061\u006cs\u0065"){_ggcaf ,_ecg :=_dcc .parseBool ();return &_ggcaf ,_ecg ;}else if (len (_abe )> 3)&&(_abe [:4]=="\u0074\u0072\u0075\u0065"){_gca ,_fae :=_dcc .parseBool ();
return &_gca ,_fae ;};_fad :=_af .FindStringSubmatch (_abe );if len (_fad )> 1{_dda ,_ =_dcc ._ccc .ReadBytes ('R');_be .Log .Trace ("\u002d\u003e\u0020\u0021\u0052\u0065\u0066\u003a\u0020\u0027\u0025\u0073\u0027",string (_dda [:]));_bad ,_dab :=_acec (string (_dda ));
return &_bad ,_dab ;};_gcb :=_fgg .FindStringSubmatch (_abe );if len (_gcb )> 1{_be .Log .Trace ("\u002d\u003e\u0020\u004e\u0075\u006d\u0062\u0065\u0072\u0021");return _dcc .parseNumber ();};_gcb =_gdf .FindStringSubmatch (_abe );if len (_gcb )> 1{_be .Log .Trace ("\u002d\u003e\u0020\u0045xp\u006f\u006e\u0065\u006e\u0074\u0069\u0061\u006c\u0020\u004e\u0075\u006d\u0062\u0065r\u0021");
_be .Log .Trace ("\u0025\u0020\u0073",_gcb );return _dcc .parseNumber ();};_be .Log .Debug ("\u0045R\u0052\u004f\u0052\u0020U\u006e\u006b\u006e\u006f\u0077n\u0020(\u0070e\u0065\u006b\u0020\u0022\u0025\u0073\u0022)",_abe );return nil ,_a .New ("\u006f\u0062\u006a\u0065\u0063t\u0020\u0070\u0061\u0072\u0073\u0069\u006e\u0067\u0020\u0065\u0072\u0072\u006fr\u0020\u002d\u0020\u0075\u006e\u0065\u0078\u0070\u0065\u0063\u0074\u0065\u0064\u0020\u0070\u0061\u0074\u0074\u0065\u0072\u006e");
};};};func (_edcf *fdfParser )skipComments ()error {if _ ,_df :=_edcf .skipSpaces ();_df !=nil {return _df ;};_fe :=true ;for {_dga ,_ec :=_edcf ._ccc .Peek (1);if _ec !=nil {_be .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u0025\u0073",_ec .Error ());
return _ec ;};if _fe &&_dga [0]!='%'{return nil ;};_fe =false ;if (_dga [0]!='\r')&&(_dga [0]!='\n'){_edcf ._ccc .ReadByte ();}else {break ;};};return _edcf .skipComments ();};func (_cde *fdfParser )readComment ()(string ,error ){var _fgd _ge .Buffer ;
_ ,_adb :=_cde .skipSpaces ();if _adb !=nil {return _fgd .String (),_adb ;};_edf :=true ;for {_bcd ,_cb :=_cde ._ccc .Peek (1);if _cb !=nil {_be .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u0025\u0073",_cb .Error ());return _fgd .String (),_cb ;
};if _edf &&_bcd [0]!='%'{return _fgd .String (),_a .New ("c\u006f\u006d\u006d\u0065\u006e\u0074 \u0073\u0068\u006f\u0075\u006c\u0064\u0020\u0073\u0074a\u0072\u0074\u0020w\u0069t\u0068\u0020\u0025");};_edf =false ;if (_bcd [0]!='\r')&&(_bcd [0]!='\n'){_dag ,_ :=_cde ._ccc .ReadByte ();
_fgd .WriteByte (_dag );}else {break ;};};return _fgd .String (),nil ;};type fdfParser struct{_ae int ;_bdc int ;_adc map[int64 ]_gg .PdfObject ;_ace _gd .ReadSeeker ;_ccc *_egc .Reader ;_ga int64 ;_fdc *_gg .PdfObjectDictionary ;};var _gdf =_eg .MustCompile ("\u005e\u005b\u005c+-\u002e\u005d\u002a\u0028\u005b\u0030\u002d\u0039\u002e]\u002b)\u0065[\u005c+\u002d\u002e\u005d\u002a\u0028\u005b\u0030\u002d\u0039\u002e\u005d\u002b\u0029");
func (_aec *fdfParser )seekFdfVersionTopDown ()(int ,int ,error ){_aec ._ace .Seek (0,_gd .SeekStart );_aec ._ccc =_egc .NewReader (_aec ._ace );_cge :=20;_fbc :=make ([]byte ,_cge );for {_gb ,_fbf :=_aec ._ccc .ReadByte ();if _fbf !=nil {if _fbf ==_gd .EOF {break ;
}else {return 0,0,_fbf ;};};if _gg .IsDecimalDigit (_gb )&&_fbc [_cge -1]=='.'&&_gg .IsDecimalDigit (_fbc [_cge -2])&&_fbc [_cge -3]=='-'&&_fbc [_cge -4]=='F'&&_fbc [_cge -5]=='D'&&_fbc [_cge -6]=='P'{_dcg :=int (_fbc [_cge -2]-'0');_bdg :=int (_gb -'0');
return _dcg ,_bdg ,nil ;};_fbc =append (_fbc [1:_cge ],_gb );};return 0,0,_a .New ("\u0076\u0065\u0072\u0073\u0069\u006f\u006e\u0020\u006e\u006f\u0074\u0020f\u006f\u0075\u006e\u0064");};

// Root returns the Root of the FDF document.
func (_gadg *fdfParser )Root ()(*_gg .PdfObjectDictionary ,error ){if _gadg ._fdc !=nil {if _dcb ,_ebaa :=_gadg .trace (_gadg ._fdc .Get ("\u0052\u006f\u006f\u0074")).(*_gg .PdfObjectDictionary );_ebaa {if _acb ,_dfc :=_gadg .trace (_dcb .Get ("\u0046\u0044\u0046")).(*_gg .PdfObjectDictionary );
_dfc {return _acb ,nil ;};};};var _cgd []int64 ;for _fga :=range _gadg ._adc {_cgd =append (_cgd ,_fga );};_c .Slice (_cgd ,func (_gfde ,_dbd int )bool {return _cgd [_gfde ]< _cgd [_dbd ]});for _ ,_dddg :=range _cgd {_aba :=_gadg ._adc [_dddg ];if _fgc ,_cag :=_gadg .trace (_aba ).(*_gg .PdfObjectDictionary );
_cag {if _dcf ,_ebe :=_gadg .trace (_fgc .Get ("\u0046\u0044\u0046")).(*_gg .PdfObjectDictionary );_ebe {return _dcf ,nil ;};};};return nil ,_a .New ("\u0046\u0044\u0046\u0020\u006e\u006f\u0074\u0020\u0066\u006f\u0075\u006e\u0064");};func (_fca *fdfParser )parseNumber ()(_gg .PdfObject ,error ){return _gg .ParseNumber (_fca ._ccc )};
func (_bcg *fdfParser )parseArray ()(*_gg .PdfObjectArray ,error ){_gcc :=_gg .MakeArray ();_bcg ._ccc .ReadByte ();for {_bcg .skipSpaces ();_ggda ,_ccf :=_bcg ._ccc .Peek (1);if _ccf !=nil {return _gcc ,_ccf ;};if _ggda [0]==']'{_bcg ._ccc .ReadByte ();
break ;};_aea ,_ccf :=_bcg .parseObject ();if _ccf !=nil {return _gcc ,_ccf ;};_gcc .Append (_aea );};return _gcc ,nil ;};func (_eed *fdfParser )parseIndirectObject ()(_gg .PdfObject ,error ){_dbe :=_gg .PdfIndirectObject {};_be .Log .Trace ("\u002dR\u0065a\u0064\u0020\u0069\u006e\u0064i\u0072\u0065c\u0074\u0020\u006f\u0062\u006a");
_def ,_bg :=_eed ._ccc .Peek (20);if _bg !=nil {_be .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u0046\u0061\u0069\u006c\u0020\u0074\u006f\u0020r\u0065a\u0064\u0020\u0069\u006e\u0064\u0069\u0072\u0065\u0063\u0074\u0020\u006f\u0062\u006a");return &_dbe ,_bg ;
};_be .Log .Trace ("\u0028\u0069\u006edi\u0072\u0065\u0063\u0074\u0020\u006f\u0062\u006a\u0020\u0070\u0065\u0065\u006b\u0020\u0022\u0025\u0073\u0022",string (_def ));_bgb :=_cg .FindStringSubmatchIndex (string (_def ));if len (_bgb )< 6{_be .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020U\u006e\u0061\u0062l\u0065\u0020\u0074\u006f \u0066\u0069\u006e\u0064\u0020\u006f\u0062\u006a\u0065\u0063\u0074\u0020\u0073\u0069\u0067\u006e\u0061\u0074\u0075\u0072\u0065\u0020\u0028\u0025\u0073\u0029",string (_def ));
return &_dbe ,_a .New ("\u0075\u006e\u0061b\u006c\u0065\u0020\u0074\u006f\u0020\u0064\u0065\u0074\u0065\u0063\u0074\u0020\u0069\u006e\u0064\u0069\u0072\u0065\u0063\u0074\u0020\u006f\u0062\u006a\u0065\u0063\u0074\u0020s\u0069\u0067\u006e\u0061\u0074\u0075\u0072\u0065");
};_eed ._ccc .Discard (_bgb [0]);_be .Log .Trace ("O\u0066\u0066\u0073\u0065\u0074\u0073\u0020\u0025\u0020\u0064",_bgb );_fadc :=_bgb [1]-_bgb [0];_bgc :=make ([]byte ,_fadc );_ ,_bg =_eed .readAtLeast (_bgc ,_fadc );if _bg !=nil {_be .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u0075\u006e\u0061\u0062l\u0065\u0020\u0074\u006f\u0020\u0072\u0065\u0061\u0064\u0020-\u0020\u0025\u0073",_bg );
return nil ,_bg ;};_be .Log .Trace ("\u0074\u0065\u0078t\u006c\u0069\u006e\u0065\u003a\u0020\u0025\u0073",_bgc );_gfc :=_cg .FindStringSubmatch (string (_bgc ));if len (_gfc )< 3{_be .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020U\u006e\u0061\u0062l\u0065\u0020\u0074\u006f \u0066\u0069\u006e\u0064\u0020\u006f\u0062\u006a\u0065\u0063\u0074\u0020\u0073\u0069\u0067\u006e\u0061\u0074\u0075\u0072\u0065\u0020\u0028\u0025\u0073\u0029",string (_bgc ));
return &_dbe ,_a .New ("\u0075\u006e\u0061b\u006c\u0065\u0020\u0074\u006f\u0020\u0064\u0065\u0074\u0065\u0063\u0074\u0020\u0069\u006e\u0064\u0069\u0072\u0065\u0063\u0074\u0020\u006f\u0062\u006a\u0065\u0063\u0074\u0020s\u0069\u0067\u006e\u0061\u0074\u0075\u0072\u0065");
};_ffbe ,_ :=_b .Atoi (_gfc [1]);_ccga ,_ :=_b .Atoi (_gfc [2]);_dbe .ObjectNumber =int64 (_ffbe );_dbe .GenerationNumber =int64 (_ccga );for {_ecad ,_acg :=_eed ._ccc .Peek (2);if _acg !=nil {return &_dbe ,_acg ;};_be .Log .Trace ("I\u006ed\u002e\u0020\u0070\u0065\u0065\u006b\u003a\u0020%\u0073\u0020\u0028\u0025 x\u0029\u0021",string (_ecad ),string (_ecad ));
if _gg .IsWhiteSpace (_ecad [0]){_eed .skipSpaces ();}else if _ecad [0]=='%'{_eed .skipComments ();}else if (_ecad [0]=='<')&&(_ecad [1]=='<'){_be .Log .Trace ("\u0043\u0061\u006c\u006c\u0020\u0050\u0061\u0072\u0073e\u0044\u0069\u0063\u0074");_dbe .PdfObject ,_acg =_eed .parseDict ();
_be .Log .Trace ("\u0045\u004f\u0046\u0020Ca\u006c\u006c\u0020\u0050\u0061\u0072\u0073\u0065\u0044\u0069\u0063\u0074\u003a\u0020%\u0076",_acg );if _acg !=nil {return &_dbe ,_acg ;};_be .Log .Trace ("\u0050\u0061\u0072\u0073\u0065\u0064\u0020\u0064\u0069\u0063t\u0069\u006f\u006e\u0061\u0072\u0079\u002e.\u002e\u0020\u0066\u0069\u006e\u0069\u0073\u0068\u0065\u0064\u002e");
}else if (_ecad [0]=='/')||(_ecad [0]=='(')||(_ecad [0]=='[')||(_ecad [0]=='<'){_dbe .PdfObject ,_acg =_eed .parseObject ();if _acg !=nil {return &_dbe ,_acg ;};_be .Log .Trace ("P\u0061\u0072\u0073\u0065\u0064\u0020o\u0062\u006a\u0065\u0063\u0074\u0020\u002e\u002e\u002e \u0066\u0069\u006ei\u0073h\u0065\u0064\u002e");
}else {if _ecad [0]=='e'{_bdb ,_gddf :=_eed .readTextLine ();if _gddf !=nil {return nil ,_gddf ;};if len (_bdb )>=6&&_bdb [0:6]=="\u0065\u006e\u0064\u006f\u0062\u006a"{break ;};}else if _ecad [0]=='s'{_ecad ,_ =_eed ._ccc .Peek (10);if string (_ecad [:6])=="\u0073\u0074\u0072\u0065\u0061\u006d"{_dee :=6;
if len (_ecad )> 6{if _gg .IsWhiteSpace (_ecad [_dee ])&&_ecad [_dee ]!='\r'&&_ecad [_dee ]!='\n'{_be .Log .Debug ("\u004e\u006fn\u002d\u0063\u006f\u006e\u0066\u006f\u0072\u006d\u0061\u006e\u0074\u0020\u0046\u0044\u0046\u0020\u006e\u006f\u0074 \u0065\u006e\u0064\u0069\u006e\u0067 \u0073\u0074\u0072\u0065\u0061\u006d\u0020\u006c\u0069\u006e\u0065\u0020\u0070\u0072o\u0070\u0065r\u006c\u0079\u0020\u0077i\u0074\u0068\u0020\u0045\u004fL\u0020\u006d\u0061\u0072\u006b\u0065\u0072");
_dee ++;};if _ecad [_dee ]=='\r'{_dee ++;if _ecad [_dee ]=='\n'{_dee ++;};}else if _ecad [_dee ]=='\n'{_dee ++;};};_eed ._ccc .Discard (_dee );_afg ,_dcd :=_dbe .PdfObject .(*_gg .PdfObjectDictionary );if !_dcd {return nil ,_a .New ("\u0073\u0074\u0072\u0065\u0061\u006d\u0020\u006f\u0062\u006a\u0065\u0063\u0074\u0020\u006di\u0073s\u0069\u006e\u0067\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079");
};_be .Log .Trace ("\u0053\u0074\u0072\u0065\u0061\u006d\u0020\u0064\u0069c\u0074\u0020\u0025\u0073",_afg );_faed ,_aefg :=_afg .Get ("\u004c\u0065\u006e\u0067\u0074\u0068").(*_gg .PdfObjectInteger );if !_aefg {return nil ,_a .New ("\u0073\u0074re\u0061\u006d\u0020l\u0065\u006e\u0067\u0074h n\u0065ed\u0073\u0020\u0074\u006f\u0020\u0062\u0065 a\u006e\u0020\u0069\u006e\u0074\u0065\u0067e\u0072");
};_afgd :=*_faed ;if _afgd < 0{return nil ,_a .New ("\u0073\u0074\u0072\u0065\u0061\u006d\u0020\u006e\u0065\u0065\u0064\u0073\u0020\u0074\u006f \u0062e\u0020\u006c\u006f\u006e\u0067\u0065\u0072\u0020\u0074\u0068\u0061\u006e\u0020\u0030");};if int64 (_afgd )> _eed ._ga {_be .Log .Debug ("\u0045\u0052R\u004f\u0052\u003a\u0020\u0053t\u0072\u0065\u0061\u006d\u0020l\u0065\u006e\u0067\u0074\u0068\u0020\u0063\u0061\u006e\u006e\u006f\u0074\u0020\u0062\u0065\u0020\u006c\u0061\u0072\u0067\u0065\u0072\u0020\u0074\u0068\u0061\u006e\u0020\u0066\u0069\u006c\u0065\u0020\u0073\u0069\u007a\u0065");
return nil ,_a .New ("\u0069n\u0076\u0061l\u0069\u0064\u0020\u0073t\u0072\u0065\u0061m\u0020\u006c\u0065\u006e\u0067\u0074\u0068\u002c\u0020la\u0072\u0067\u0065r\u0020\u0074h\u0061\u006e\u0020\u0066\u0069\u006ce\u0020\u0073i\u007a\u0065");};_bca :=make ([]byte ,_afgd );
_ ,_acg =_eed .readAtLeast (_bca ,int (_afgd ));if _acg !=nil {_be .Log .Debug ("E\u0052\u0052\u004f\u0052 s\u0074r\u0065\u0061\u006d\u0020\u0028%\u0064\u0029\u003a\u0020\u0025\u0058",len (_bca ),_bca );_be .Log .Debug ("\u0045R\u0052\u004f\u0052\u003a\u0020\u0025v",_acg );
return nil ,_acg ;};_dbg :=_gg .PdfObjectStream {};_dbg .Stream =_bca ;_dbg .PdfObjectDictionary =_dbe .PdfObject .(*_gg .PdfObjectDictionary );_dbg .ObjectNumber =_dbe .ObjectNumber ;_dbg .GenerationNumber =_dbe .GenerationNumber ;_eed .skipSpaces ();
_eed ._ccc .Discard (9);_eed .skipSpaces ();return &_dbg ,nil ;};};_dbe .PdfObject ,_acg =_eed .parseObject ();return &_dbe ,_acg ;};};_be .Log .Trace ("\u0052\u0065\u0074\u0075rn\u0069\u006e\u0067\u0020\u0069\u006e\u0064\u0069\u0072\u0065\u0063\u0074\u0021");
return &_dbe ,nil ;};func (_ddd *fdfParser )parseName ()(_gg .PdfObjectName ,error ){var _eb _ge .Buffer ;_fdb :=false ;for {_fdd ,_ebb :=_ddd ._ccc .Peek (1);if _ebb ==_gd .EOF {break ;};if _ebb !=nil {return _gg .PdfObjectName (_eb .String ()),_ebb ;
};if !_fdb {if _fdd [0]=='/'{_fdb =true ;_ddd ._ccc .ReadByte ();}else if _fdd [0]=='%'{_ddd .readComment ();_ddd .skipSpaces ();}else {_be .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u0020N\u0061\u006d\u0065\u0020\u0073\u0074\u0061\u0072\u0074\u0069\u006e\u0067\u0020w\u0069\u0074\u0068\u0020\u0025\u0073\u0020(\u0025\u0020\u0078\u0029",_fdd ,_fdd );
return _gg .PdfObjectName (_eb .String ()),_gc .Errorf ("\u0069n\u0076a\u006c\u0069\u0064\u0020\u006ea\u006d\u0065:\u0020\u0028\u0025\u0063\u0029",_fdd [0]);};}else {if _gg .IsWhiteSpace (_fdd [0]){break ;}else if (_fdd [0]=='/')||(_fdd [0]=='[')||(_fdd [0]=='(')||(_fdd [0]==']')||(_fdd [0]=='<')||(_fdd [0]=='>'){break ;
}else if _fdd [0]=='#'{_aeb ,_eba :=_ddd ._ccc .Peek (3);if _eba !=nil {return _gg .PdfObjectName (_eb .String ()),_eba ;};_ddd ._ccc .Discard (3);_ggd ,_eba :=_d .DecodeString (string (_aeb [1:3]));if _eba !=nil {return _gg .PdfObjectName (_eb .String ()),_eba ;
};_eb .Write (_ggd );}else {_gdd ,_ :=_ddd ._ccc .ReadByte ();_eb .WriteByte (_gdd );};};};return _gg .PdfObjectName (_eb .String ()),nil ;};

// Load loads FDF form data from `r`.
func Load (r _gd .ReadSeeker )(*Data ,error ){_ggc ,_ed :=_cfe (r );if _ed !=nil {return nil ,_ed ;};_gf ,_ed :=_ggc .Root ();if _ed !=nil {return nil ,_ed ;};_egf ,_egb :=_gg .GetArray (_gf .Get ("\u0046\u0069\u0065\u006c\u0064\u0073"));if !_egb {return nil ,_a .New ("\u0066\u0069\u0065\u006c\u0064\u0073\u0020\u006d\u0069s\u0073\u0069\u006e\u0067");
};return &Data {_ab :_egf ,_dg :_gf },nil ;};var _fgg =_eg .MustCompile ("\u005e\u005b\u005c\u002b\u002d\u002e\u005d\u002a\u0028\u005b\u0030\u002d9\u002e\u005d\u002b\u0029");var _agc =_eg .MustCompile ("\u0025\u0025\u0045O\u0046");func (_edg *fdfParser )parse ()error {_edg ._ace .Seek (0,_gd .SeekStart );
_edg ._ccc =_egc .NewReader (_edg ._ace );for {_edg .skipComments ();_edgd ,_bbf :=_edg ._ccc .Peek (20);if _bbf !=nil {_be .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u0046\u0061\u0069\u006c\u0020\u0074\u006f\u0020r\u0065a\u0064\u0020\u0069\u006e\u0064\u0069\u0072\u0065\u0063\u0074\u0020\u006f\u0062\u006a");
return _bbf ;};if _e .HasPrefix (string (_edgd ),"\u0074r\u0061\u0069\u006c\u0065\u0072"){_edg ._ccc .Discard (7);_edg .skipSpaces ();_edg .skipComments ();_abg ,_ :=_edg .parseDict ();_edg ._fdc =_abg ;break ;};_adf :=_cg .FindStringSubmatchIndex (string (_edgd ));
if len (_adf )< 6{_be .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020U\u006e\u0061\u0062l\u0065\u0020\u0074\u006f \u0066\u0069\u006e\u0064\u0020\u006f\u0062\u006a\u0065\u0063\u0074\u0020\u0073\u0069\u0067\u006e\u0061\u0074\u0075\u0072\u0065\u0020\u0028\u0025\u0073\u0029",string (_edgd ));
return _a .New ("\u0075\u006e\u0061b\u006c\u0065\u0020\u0074\u006f\u0020\u0064\u0065\u0074\u0065\u0063\u0074\u0020\u0069\u006e\u0064\u0069\u0072\u0065\u0063\u0074\u0020\u006f\u0062\u006a\u0065\u0063\u0074\u0020s\u0069\u0067\u006e\u0061\u0074\u0075\u0072\u0065");
};_gaea ,_bbf :=_edg .parseIndirectObject ();if _bbf !=nil {return _bbf ;};switch _bbde :=_gaea .(type ){case *_gg .PdfIndirectObject :_edg ._adc [_bbde .ObjectNumber ]=_bbde ;case *_gg .PdfObjectStream :_edg ._adc [_bbde .ObjectNumber ]=_bbde ;default:return _a .New ("\u0074\u0079\u0070\u0065\u0020\u0065\u0072\u0072\u006f\u0072");
};};return nil ;};func (_adbb *fdfParser )parseDict ()(*_gg .PdfObjectDictionary ,error ){_be .Log .Trace ("\u0052\u0065\u0061\u0064\u0069\u006e\u0067\u0020\u0046\u0044\u0046\u0020D\u0069\u0063\u0074\u0021");_ggbf :=_gg .MakeDict ();_eea ,_ :=_adbb ._ccc .ReadByte ();
if _eea !='<'{return nil ,_a .New ("\u0069\u006e\u0076a\u006c\u0069\u0064\u0020\u0064\u0069\u0063\u0074");};_eea ,_ =_adbb ._ccc .ReadByte ();if _eea !='<'{return nil ,_a .New ("\u0069\u006e\u0076a\u006c\u0069\u0064\u0020\u0064\u0069\u0063\u0074");};for {_adbb .skipSpaces ();
_adbb .skipComments ();_bff ,_geb :=_adbb ._ccc .Peek (2);if _geb !=nil {return nil ,_geb ;};_be .Log .Trace ("D\u0069c\u0074\u0020\u0070\u0065\u0065\u006b\u003a\u0020%\u0073\u0020\u0028\u0025 x\u0029\u0021",string (_bff ),string (_bff ));if (_bff [0]=='>')&&(_bff [1]=='>'){_be .Log .Trace ("\u0045\u004f\u0046\u0020\u0064\u0069\u0063\u0074\u0069o\u006e\u0061\u0072\u0079");
_adbb ._ccc .ReadByte ();_adbb ._ccc .ReadByte ();break ;};_be .Log .Trace ("\u0050a\u0072s\u0065\u0020\u0074\u0068\u0065\u0020\u006e\u0061\u006d\u0065\u0021");_afbe ,_geb :=_adbb .parseName ();_be .Log .Trace ("\u004be\u0079\u003a\u0020\u0025\u0073",_afbe );
if _geb !=nil {_be .Log .Debug ("E\u0052\u0052\u004f\u0052\u0020\u0052e\u0074\u0075\u0072\u006e\u0069\u006e\u0067\u0020\u006ea\u006d\u0065\u0020e\u0072r\u0020\u0025\u0073",_geb );return nil ,_geb ;};if len (_afbe )> 4&&_afbe [len (_afbe )-4:]=="\u006e\u0075\u006c\u006c"{_dcce :=_afbe [0:len (_afbe )-4];
_be .Log .Debug ("\u0054\u0061\u006b\u0069n\u0067\u0020\u0063\u0061\u0072\u0065\u0020\u006f\u0066\u0020n\u0075l\u006c\u0020\u0062\u0075\u0067\u0020\u0028%\u0073\u0029",_afbe );_be .Log .Debug ("\u004e\u0065\u0077\u0020ke\u0079\u0020\u0022\u0025\u0073\u0022\u0020\u003d\u0020\u006e\u0075\u006c\u006c",_dcce );
_adbb .skipSpaces ();_aebd ,_ :=_adbb ._ccc .Peek (1);if _aebd [0]=='/'{_ggbf .Set (_dcce ,_gg .MakeNull ());continue ;};};_adbb .skipSpaces ();_bea ,_geb :=_adbb .parseObject ();if _geb !=nil {return nil ,_geb ;};_ggbf .Set (_afbe ,_bea );_be .Log .Trace ("\u0064\u0069\u0063\u0074\u005b\u0025\u0073\u005d\u0020\u003d\u0020\u0025\u0073",_afbe ,_bea .String ());
};_be .Log .Trace ("\u0072\u0065\u0074\u0075rn\u0069\u006e\u0067\u0020\u0046\u0044\u0046\u0020\u0044\u0069\u0063\u0074\u0021");return _ggbf ,nil ;};func (_ffc *fdfParser )parseNull ()(_gg .PdfObjectNull ,error ){_ ,_fdce :=_ffc ._ccc .Discard (4);return _gg .PdfObjectNull {},_fdce ;
};

// Data represents forms data format (FDF) file data.
type Data struct{_dg *_gg .PdfObjectDictionary ;_ab *_gg .PdfObjectArray ;};func (_ff *fdfParser )readAtLeast (_edc []byte ,_ada int )(int ,error ){_bd :=_ada ;_dc :=0;_db :=0;for _bd > 0{_ce ,_fg :=_ff ._ccc .Read (_edc [_dc :]);if _fg !=nil {_be .Log .Debug ("\u0045\u0052\u0052O\u0052\u0020\u0046\u0061i\u006c\u0065\u0064\u0020\u0072\u0065\u0061d\u0069\u006e\u0067\u0020\u0028\u0025\u0064\u003b\u0025\u0064\u0029\u0020\u0025\u0073",_ce ,_db ,_fg .Error ());
return _dc ,_a .New ("\u0066\u0061\u0069\u006c\u0065\u0064\u0020\u0072\u0065a\u0064\u0069\u006e\u0067");};_db ++;_dc +=_ce ;_bd -=_ce ;};return _dc ,nil ;};

// LoadFromPath loads FDF form data from file path `fdfPath`.
func LoadFromPath (fdfPath string )(*Data ,error ){_fc ,_bb :=_f .Open (fdfPath );if _bb !=nil {return nil ,_bb ;};defer _fc .Close ();return Load (_fc );};var _af =_eg .MustCompile ("^\u005c\u0073\u002a\u0028\\d\u002b)\u005c\u0073\u002b\u0028\u005cd\u002b\u0029\u005c\u0073\u002b\u0052");
func (_fbd *fdfParser )getFileOffset ()int64 {_de ,_ :=_fbd ._ace .Seek (0,_gd .SeekCurrent );_de -=int64 (_fbd ._ccc .Buffered ());return _de ;};var _eab =_eg .MustCompile ("\u0025F\u0044F\u002d\u0028\u005c\u0064\u0029\u005c\u002e\u0028\u005c\u0064\u0029");
func _ccgf (_eaf string )(*fdfParser ,error ){_bac :=fdfParser {};_dagc :=[]byte (_eaf );_bda :=_ge .NewReader (_dagc );_bac ._ace =_bda ;_bac ._adc =map[int64 ]_gg .PdfObject {};_fgb :=_egc .NewReader (_bda );_bac ._ccc =_fgb ;_bac ._ga =int64 (len (_eaf ));
return &_bac ,_bac .parse ();};func (_aee *fdfParser )parseHexString ()(*_gg .PdfObjectString ,error ){_aee ._ccc .ReadByte ();var _efg _ge .Buffer ;for {_ggb ,_ggca :=_aee ._ccc .Peek (1);if _ggca !=nil {return _gg .MakeHexString (""),_ggca ;};if _ggb [0]=='>'{_aee ._ccc .ReadByte ();
break ;};_aeg ,_ :=_aee ._ccc .ReadByte ();if !_gg .IsWhiteSpace (_aeg ){_efg .WriteByte (_aeg );};};if _efg .Len ()%2==1{_efg .WriteRune ('0');};_ba ,_dgb :=_d .DecodeString (_efg .String ());if _dgb !=nil {_be .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u0020\u0050\u0061\u0072\u0073\u0069\u006e\u0067\u0020\u0068\u0065\u0078\u0020\u0073\u0074r\u0069\u006e\u0067\u003a\u0020\u0027\u0025\u0073\u0027 \u002d\u0020\u0072\u0065\u0074\u0075\u0072\u006e\u0069\u006e\u0067\u0020\u0061n\u0020\u0065\u006d\u0070\u0074\u0079 \u0073\u0074\u0072i\u006e\u0067",_efg .String ());
return _gg .MakeHexString (""),nil ;};return _gg .MakeHexString (string (_ba )),nil ;};func (_cac *fdfParser )readTextLine ()(string ,error ){var _abc _ge .Buffer ;for {_eca ,_cf :=_cac ._ccc .Peek (1);if _cf !=nil {_be .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u0025\u0073",_cf .Error ());
return _abc .String (),_cf ;};if (_eca [0]!='\r')&&(_eca [0]!='\n'){_gfb ,_ :=_cac ._ccc .ReadByte ();_abc .WriteByte (_gfb );}else {break ;};};return _abc .String (),nil ;};

// FieldDictionaries returns a map of field names to field dictionaries.
func (fdf *Data )FieldDictionaries ()(map[string ]*_gg .PdfObjectDictionary ,error ){_dgg :=map[string ]*_gg .PdfObjectDictionary {};for _ea :=0;_ea < fdf ._ab .Len ();_ea ++{_cc ,_aga :=_gg .GetDict (fdf ._ab .Get (_ea ));if _aga {_ega ,_ :=_gg .GetString (_cc .Get ("\u0054"));
if _ega !=nil {_dgg [_ega .Str ()]=_cc ;};};};return _dgg ,nil ;};func (_fgae *fdfParser )trace (_bdbe _gg .PdfObject )_gg .PdfObject {switch _cfeg :=_bdbe .(type ){case *_gg .PdfObjectReference :_aefe ,_fee :=_fgae ._adc [_cfeg .ObjectNumber ].(*_gg .PdfIndirectObject );
if _fee {return _aefe .PdfObject ;};_be .Log .Debug ("\u0054\u0079\u0070\u0065\u0020\u0065\u0072\u0072\u006f\u0072");return nil ;case *_gg .PdfIndirectObject :return _cfeg .PdfObject ;};return _bdbe ;};func (_ade *fdfParser )parseString ()(*_gg .PdfObjectString ,error ){_ade ._ccc .ReadByte ();
var _adbc _ge .Buffer ;_aa :=1;for {_cff ,_ged :=_ade ._ccc .Peek (1);if _ged !=nil {return _gg .MakeString (_adbc .String ()),_ged ;};if _cff [0]=='\\'{_ade ._ccc .ReadByte ();_aef ,_aaf :=_ade ._ccc .ReadByte ();if _aaf !=nil {return _gg .MakeString (_adbc .String ()),_aaf ;
};if _gg .IsOctalDigit (_aef ){_fea ,_gfdd :=_ade ._ccc .Peek (2);if _gfdd !=nil {return _gg .MakeString (_adbc .String ()),_gfdd ;};var _fbb []byte ;_fbb =append (_fbb ,_aef );for _ ,_aae :=range _fea {if _gg .IsOctalDigit (_aae ){_fbb =append (_fbb ,_aae );
}else {break ;};};_ade ._ccc .Discard (len (_fbb )-1);_be .Log .Trace ("\u004e\u0075\u006d\u0065ri\u0063\u0020\u0073\u0074\u0072\u0069\u006e\u0067\u0020\u0022\u0025\u0073\u0022",_fbb );_bbd ,_gfdd :=_b .ParseUint (string (_fbb ),8,32);if _gfdd !=nil {return _gg .MakeString (_adbc .String ()),_gfdd ;
};_adbc .WriteByte (byte (_bbd ));continue ;};switch _aef {case 'n':_adbc .WriteRune ('\n');case 'r':_adbc .WriteRune ('\r');case 't':_adbc .WriteRune ('\t');case 'b':_adbc .WriteRune ('\b');case 'f':_adbc .WriteRune ('\f');case '(':_adbc .WriteRune ('(');
case ')':_adbc .WriteRune (')');case '\\':_adbc .WriteRune ('\\');};continue ;}else if _cff [0]=='('{_aa ++;}else if _cff [0]==')'{_aa --;if _aa ==0{_ade ._ccc .ReadByte ();break ;};};_cdd ,_ :=_ade ._ccc .ReadByte ();_adbc .WriteByte (_cdd );};return _gg .MakeString (_adbc .String ()),nil ;
};func (_daa *fdfParser )seekToEOFMarker (_caeg int64 )error {_abbb :=int64 (0);_gac :=int64 (1000);for _abbb < _caeg {if _caeg <=(_gac +_abbb ){_gac =_caeg -_abbb ;};_ ,_bbdf :=_daa ._ace .Seek (-_abbb -_gac ,_gd .SeekEnd );if _bbdf !=nil {return _bbdf ;
};_dfd :=make ([]byte ,_gac );_daa ._ace .Read (_dfd );_be .Log .Trace ("\u004c\u006f\u006f\u006bi\u006e\u0067\u0020\u0066\u006f\u0072\u0020\u0045\u004f\u0046 \u006da\u0072\u006b\u0065\u0072\u003a\u0020\u0022%\u0073\u0022",string (_dfd ));_ebd :=_agc .FindAllStringIndex (string (_dfd ),-1);
if _ebd !=nil {_dccee :=_ebd [len (_ebd )-1];_be .Log .Trace ("\u0049\u006e\u0064\u003a\u0020\u0025\u0020\u0064",_ebd );_daa ._ace .Seek (-_abbb -_gac +int64 (_dccee [0]),_gd .SeekEnd );return nil ;};_be .Log .Debug ("\u0057\u0061\u0072\u006e\u0069\u006eg\u003a\u0020\u0045\u004f\u0046\u0020\u006d\u0061\u0072\u006b\u0065\u0072\u0020\u006e\u006f\u0074\u0020\u0066\u006f\u0075n\u0064\u0021\u0020\u002d\u0020\u0063\u006f\u006e\u0074\u0069\u006e\u0075\u0065\u0020s\u0065e\u006b\u0069\u006e\u0067");
_abbb +=_gac ;};_be .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u003a\u0020\u0045\u004f\u0046\u0020\u006d\u0061\u0072\u006be\u0072 \u0077\u0061\u0073\u0020\u006e\u006f\u0074\u0020\u0066\u006f\u0075\u006e\u0064\u002e");return _a .New ("\u0045\u004f\u0046\u0020\u006e\u006f\u0074\u0020\u0066\u006f\u0075\u006e\u0064");
};func (_gea *fdfParser )skipSpaces ()(int ,error ){_ef :=0;for {_eda ,_gfd :=_gea ._ccc .ReadByte ();if _gfd !=nil {return 0,_gfd ;};if _gg .IsWhiteSpace (_eda ){_ef ++;}else {_gea ._ccc .UnreadByte ();break ;};};return _ef ,nil ;};