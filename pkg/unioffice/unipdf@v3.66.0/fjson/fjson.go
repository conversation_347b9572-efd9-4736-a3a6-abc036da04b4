//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

// Package fjson provides support for loading PDF form field data from JSON data/files.
package fjson ;import (_e "encoding/json";_fd "github.com/unidoc/unipdf/v3/common";_af "github.com/unidoc/unipdf/v3/core";_eb "github.com/unidoc/unipdf/v3/model";_f "io";_a "os";);

// LoadFromJSONFile loads form field data from a JSON file.
func LoadFromJSONFile (filePath string )(*FieldData ,error ){_cg ,_ae :=_a .Open (filePath );if _ae !=nil {return nil ,_ae ;};defer _cg .Close ();return LoadFromJSON (_cg );};

// FieldData represents form field data loaded from JSON file.
type FieldData struct{_ee []fieldValue };

// LoadFromPDF loads form field data from a PDF.
func LoadFromPDF (rs _f .ReadSeeker )(*FieldData ,error ){_g ,_gf :=_eb .NewPdfReader (rs );if _gf !=nil {return nil ,_gf ;};if _g .AcroForm ==nil {return nil ,nil ;};var _ff []fieldValue ;_be :=_g .AcroForm .AllFields ();for _ ,_df :=range _be {var _db []string ;
_cf :=make (map[string ]struct{});_bc ,_de :=_df .FullName ();if _de !=nil {return nil ,_de ;};if _ag ,_afg :=_df .V .(*_af .PdfObjectString );_afg {_ff =append (_ff ,fieldValue {Name :_bc ,Value :_ag .Decoded ()});continue ;};var _gc string ;for _ ,_ceb :=range _df .Annotations {_ef ,_dc :=_af .GetName (_ceb .AS );
if _dc {_gc =_ef .String ();};_ba ,_bf :=_af .GetDict (_ceb .AP );if !_bf {continue ;};_ea ,_ :=_af .GetDict (_ba .Get ("\u004e"));for _ ,_cc :=range _ea .Keys (){_ed :=_cc .String ();if _ ,_dbe :=_cf [_ed ];!_dbe {_db =append (_db ,_ed );_cf [_ed ]=struct{}{};
};};_ga ,_ :=_af .GetDict (_ba .Get ("\u0044"));for _ ,_gd :=range _ga .Keys (){_ac :=_gd .String ();if _ ,_aa :=_cf [_ac ];!_aa {_db =append (_db ,_ac );_cf [_ac ]=struct{}{};};};};_beg :=fieldValue {Name :_bc ,Value :_gc ,Options :_db };_ff =append (_ff ,_beg );
};_fgb :=FieldData {_ee :_ff };return &_fgb ,nil ;};

// LoadFromJSON loads JSON form data from `r`.
func LoadFromJSON (r _f .Reader )(*FieldData ,error ){var _d FieldData ;_b :=_e .NewDecoder (r ).Decode (&_d ._ee );if _b !=nil {return nil ,_b ;};return &_d ,nil ;};

// FieldValues implements model.FieldValueProvider interface.
func (_cff *FieldData )FieldValues ()(map[string ]_af .PdfObject ,error ){_cgg :=make (map[string ]_af .PdfObject );for _ ,_gb :=range _cff ._ee {if len (_gb .Value )> 0{_cgg [_gb .Name ]=_af .MakeString (_gb .Value );};};return _cgg ,nil ;};

// SetImage assign model.Image to a specific field identified by fieldName.
func (_dd *FieldData )SetImage (fieldName string ,img *_eb .Image ,opt []string )error {_ede :=fieldValue {Name :fieldName ,ImageValue :img ,Options :opt };_dd ._ee =append (_dd ._ee ,_ede );return nil ;};type fieldValue struct{Name string `json:"name"`;
Value string `json:"value"`;ImageValue *_eb .Image `json:"-"`;

// Options lists allowed values if present.
Options []string `json:"options,omitempty"`;};

// FieldImageValues implements model.FieldImageProvider interface.
func (_gae *FieldData )FieldImageValues ()(map[string ]*_eb .Image ,error ){_acc :=make (map[string ]*_eb .Image );for _ ,_eef :=range _gae ._ee {if _eef .ImageValue !=nil {_acc [_eef .Name ]=_eef .ImageValue ;};};return _acc ,nil ;};

// LoadFromPDFFile loads form field data from a PDF file.
func LoadFromPDFFile (filePath string )(*FieldData ,error ){_bca ,_bcg :=_a .Open (filePath );if _bcg !=nil {return nil ,_bcg ;};defer _bca .Close ();return LoadFromPDF (_bca );};

// SetImageFromFile assign image file to a specific field identified by fieldName.
func (_gcg *FieldData )SetImageFromFile (fieldName string ,imagePath string ,opt []string )error {_fba ,_da :=_a .Open (imagePath );if _da !=nil {return _da ;};defer _fba .Close ();_gfc ,_da :=_eb .ImageHandling .Read (_fba );if _da !=nil {_fd .Log .Error ("\u0045\u0072\u0072or\u0020\u006c\u006f\u0061\u0064\u0069\u006e\u0067\u0020\u0069\u006d\u0061\u0067\u0065\u003a\u0020\u0025\u0073",_da );
return _da ;};return _gcg .SetImage (fieldName ,_gfc ,opt );};

// JSON returns the field data as a string in JSON format.
func (_agf FieldData )JSON ()(string ,error ){_ge ,_fb :=_e .MarshalIndent (_agf ._ee ,"","\u0020\u0020\u0020\u0020");return string (_ge ),_fb ;};