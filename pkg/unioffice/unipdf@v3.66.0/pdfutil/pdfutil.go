//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package pdfutil ;import (_d "github.com/unidoc/unipdf/v3/common";_e "github.com/unidoc/unipdf/v3/contentstream";_de "github.com/unidoc/unipdf/v3/contentstream/draw";_b "github.com/unidoc/unipdf/v3/core";_a "github.com/unidoc/unipdf/v3/model";);

// NormalizePage performs the following operations on the passed in page:
//   - Normalize the page rotation.
//     Rotates the contents of the page according to the Rotate entry, thus
//     flattening the rotation. The Rotate entry of the page is set to nil.
//   - Normalize the media box.
//     If the media box of the page is offsetted (Llx != 0 or Lly != 0),
//     the contents of the page are translated to (-Llx, -Lly). After
//     normalization, the media box is updated (Llx and Lly are set to 0 and
//     Urx and Ury are updated accordingly).
//   - Normalize the crop box.
//     The crop box of the page is updated based on the previous operations.
//
// After normalization, the page should look the same if openend using a
// PDF viewer.
// NOTE: This function does not normalize annotations, outlines other parts
// that are not part of the basic geometry and page content streams.
func NormalizePage (page *_a .PdfPage )error {_ba ,_ec :=page .GetMediaBox ();if _ec !=nil {return _ec ;};_ecg ,_ec :=page .GetRotate ();if _ec !=nil {_d .Log .Debug ("\u0045\u0052R\u004f\u0052\u003a\u0020\u0025\u0073\u0020\u002d\u0020\u0069\u0067\u006e\u006f\u0072\u0069\u006e\u0067\u0020\u0061\u006e\u0064\u0020\u0061\u0073\u0073\u0075\u006d\u0069\u006e\u0067\u0020\u006e\u006f\u0020\u0072\u006f\u0074\u0061\u0074\u0069\u006f\u006e\u000a",_ec .Error ());
};_g :=_ecg %360!=0&&_ecg %90==0;_ba .Normalize ();_ed ,_f ,_bg ,_fa :=_ba .Llx ,_ba .Lly ,_ba .Width (),_ba .Height ();_gf :=_ed !=0||_f !=0;if !_g &&!_gf {return nil ;};_ga :=func (_ff ,_bb ,_dc float64 )_de .BoundingBox {return _de .Path {Points :[]_de .Point {_de .NewPoint (0,0).Rotate (_dc ),_de .NewPoint (_ff ,0).Rotate (_dc ),_de .NewPoint (0,_bb ).Rotate (_dc ),_de .NewPoint (_ff ,_bb ).Rotate (_dc )}}.GetBoundingBox ();
};_da :=_e .NewContentCreator ();var _ab float64 ;if _g {_ab =-float64 (_ecg );_be :=_ga (_bg ,_fa ,_ab );_da .Translate ((_be .Width -_bg )/2+_bg /2,(_be .Height -_fa )/2+_fa /2);_da .RotateDeg (_ab );_da .Translate (-_bg /2,-_fa /2);_bg ,_fa =_be .Width ,_be .Height ;
};if _gf {_da .Translate (-_ed ,-_f );};_gg :=_da .Operations ();_fe ,_ec :=_b .MakeStream (_gg .Bytes (),_b .NewFlateEncoder ());if _ec !=nil {return _ec ;};_aa :=_b .MakeArray (_fe );_aa .Append (page .GetContentStreamObjs ()...);*_ba =_a .PdfRectangle {Urx :_bg ,Ury :_fa };
if _ag :=page .CropBox ;_ag !=nil {_ag .Normalize ();_ae ,_db ,_ggf ,_cd :=_ag .Llx -_ed ,_ag .Lly -_f ,_ag .Width (),_ag .Height ();if _g {_dag :=_ga (_ggf ,_cd ,_ab );_ggf ,_cd =_dag .Width ,_dag .Height ;};*_ag =_a .PdfRectangle {Llx :_ae ,Lly :_db ,Urx :_ae +_ggf ,Ury :_db +_cd };
};_d .Log .Debug ("\u0052\u006f\u0074\u0061\u0074\u0065\u003d\u0025\u0066\u00b0\u0020\u004f\u0070\u0073\u003d%\u0071 \u004d\u0065\u0064\u0069\u0061\u0042\u006f\u0078\u003d\u0025\u002e\u0032\u0066",_ab ,_gg ,_ba );page .Contents =_aa ;page .Rotate =nil ;
return nil ;};