//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package redactor ;import (_f "errors";_c "fmt";_fe "github.com/unidoc/unipdf/v3/common";_dg "github.com/unidoc/unipdf/v3/contentstream";_da "github.com/unidoc/unipdf/v3/core";_bb "github.com/unidoc/unipdf/v3/creator";_fc "github.com/unidoc/unipdf/v3/extractor";
_bba "github.com/unidoc/unipdf/v3/model";_d "io";_a "regexp";_ae "sort";_ad "strings";);func _afa (_cdgg *_fc .TextMarkArray ,_cebb int ,_eeae int )int {_gdfb :=_cdgg .Elements ();_becc :=_cebb -1;_bfcb :=_cebb +1;_afc :=-1;if _becc >=0{_dfec :=_gdfb [_becc ];
_gcdf :=_dfec .ObjString ;_cebd :=len (_gcdf );_aacb :=_dfec .Index ;if _aacb +1< _cebd {_afc =_becc ;return _afc ;};};if _bfcb < len (_gdfb ){_ffe :=_gdfb [_bfcb ];_cad :=_ffe .ObjString ;if _cad [0]!=_ffe .Text {_afc =_bfcb ;return _afc ;};};return _afc ;
};func _dbbc (_aacf *_fc .TextMarkArray )[]*_fc .TextMarkArray {_cgfc :=_aacf .Elements ();_egef :=len (_cgfc );var _ace _da .PdfObject ;_ggd :=[]*_fc .TextMarkArray {};_cbge :=&_fc .TextMarkArray {};_ffgg :=-1;for _baeb ,_cda :=range _cgfc {_aec :=_cda .DirectObject ;
_ffgg =_cda .Index ;if _aec ==nil {_fff :=_afa (_aacf ,_baeb ,_ffgg );if _ace !=nil {if _fff ==-1||_fff > _baeb {_ggd =append (_ggd ,_cbge );_cbge =&_fc .TextMarkArray {};};};}else if _aec !=nil &&_ace ==nil {if _ffgg ==0&&_baeb > 0{_ggd =append (_ggd ,_cbge );
_cbge =&_fc .TextMarkArray {};};}else if _aec !=nil &&_ace !=nil {if _aec !=_ace {_ggd =append (_ggd ,_cbge );_cbge =&_fc .TextMarkArray {};};};_ace =_aec ;_cbge .Append (_cda );if _baeb ==(_egef -1){_ggd =append (_ggd ,_cbge );};};return _ggd ;};func _dcg (_dfb ,_dgfc ,_aega float64 )float64 {_aega =_aega /100;
_gba :=(-1000*_dfb )/(_dgfc *_aega );return _gba ;};func _gf (_daa *_dg .ContentStreamOperations ,_dc string ,_af int )error {_cgc :=_dg .ContentStreamOperations {};var _ba _dg .ContentStreamOperation ;for _ac ,_gcc :=range *_daa {if _ac ==_af {if _dc =="\u0027"{_bc :=_dg .ContentStreamOperation {Operand :"\u0054\u002a"};
_cgc =append (_cgc ,&_bc );_ba .Params =_gcc .Params ;_ba .Operand ="\u0054\u006a";_cgc =append (_cgc ,&_ba );}else if _dc =="\u0022"{_daaa :=_gcc .Params [:2];Tc ,Tw :=_daaa [0],_daaa [1];_ee :=_dg .ContentStreamOperation {Params :[]_da .PdfObject {Tc },Operand :"\u0054\u0063"};
_cgc =append (_cgc ,&_ee );_ee =_dg .ContentStreamOperation {Params :[]_da .PdfObject {Tw },Operand :"\u0054\u0077"};_cgc =append (_cgc ,&_ee );_ba .Params =[]_da .PdfObject {_gcc .Params [2]};_ba .Operand ="\u0054\u006a";_cgc =append (_cgc ,&_ba );};};
_cgc =append (_cgc ,_gcc );};*_daa =_cgc ;return nil ;};func _dgfbe (_dgbd []*targetMap ){for _dfa ,_deee :=range _dgbd {for _dacc ,_gbf :=range _dgbd {if _dfa !=_dacc {_affd ,_dcf :=_dfe (*_deee ,*_gbf );if _affd {_bacc (_gbf ,_dcf );};};};};};func _bccfb (_aag int ,_fdbe []int )bool {for _ ,_gbff :=range _fdbe {if _gbff ==_aag {return true ;
};};return false ;};func _ea (_ec *_dg .ContentStreamOperations ,_g map[_da .PdfObject ][]localSpanMarks )error {for _gc ,_ecg :=range _g {if _gc ==nil {continue ;};_fb ,_dafg ,_fcc :=_faa (_ec ,_gc );if !_fcc {_fe .Log .Debug ("Pd\u0066\u004fb\u006a\u0065\u0063\u0074\u0020\u0025\u0073\u006e\u006ft\u0020\u0066\u006f\u0075\u006e\u0064\u0020\u0069\u006e\u0020\u0073\u0069\u0064\u0065\u0020\u0074\u0068\u0065\u0020\u0063\u006f\u006e\u0074\u0065\u006e\u0074\u0073\u0074r\u0065a\u006d\u0020\u006f\u0070\u0065\u0072\u0061\u0074i\u006fn\u0020\u0025s",_gc ,_ec );
return nil ;};if _fb .Operand =="\u0054\u006a"{_eg :=_ded (_fb ,_gc ,_ecg );if _eg !=nil {return _eg ;};}else if _fb .Operand =="\u0054\u004a"{_cg :=_add (_fb ,_gc ,_ecg );if _cg !=nil {return _cg ;};}else if _fb .Operand =="\u0027"||_fb .Operand =="\u0022"{_bge :=_gf (_ec ,_fb .Operand ,_dafg );
if _bge !=nil {return _bge ;};_bge =_ded (_fb ,_gc ,_ecg );if _bge !=nil {return _bge ;};};};return nil ;};func _bfd (_aff string ,_ebb *_bba .PdfFont )[]byte {_eaa ,_eae :=_ebb .StringToCharcodeBytes (_aff );if _eae !=0{_fe .Log .Debug ("\u0057\u0041\u0052\u004e\u003a\u0020\u0073\u006fm\u0065\u0020\u0072un\u0065\u0073\u0020\u0063\u006f\u0075l\u0064\u0020\u006e\u006f\u0074\u0020\u0062\u0065\u0020\u0065\u006e\u0063\u006f\u0064\u0065d\u002e\u000a\u0009\u0025\u0073\u0020\u002d\u003e \u0025\u0076",_aff ,_eaa );
};return _eaa ;};type localSpanMarks struct{_bdgc *_fc .TextMarkArray ;_fec int ;_aaea string ;};func _aef (_aaca string ,_dbac []replacement ,_cgd *_bba .PdfFont )[]_da .PdfObject {_bcf :=[]_da .PdfObject {};_gfb :=0;_acgc :=_aaca ;for _gcdb ,_cgf :=range _dbac {_fee :=_cgf ._bg ;
_fcf :=_cgf ._fg ;_gcag :=_cgf ._daf ;_bda :=_da .MakeFloat (_fcf );if _gfb > _fee ||_fee ==-1{continue ;};_dde :=_aaca [_gfb :_fee ];_gcdg :=_bfd (_dde ,_cgd );_ged :=_da .MakeStringFromBytes (_gcdg );_bcf =append (_bcf ,_ged );_bcf =append (_bcf ,_bda );
_cfa :=_fee +len (_gcag );_acgc =_aaca [_cfa :];_gfb =_cfa ;if _gcdb ==len (_dbac )-1{_gcdg =_bfd (_acgc ,_cgd );_ged =_da .MakeStringFromBytes (_gcdg );_bcf =append (_bcf ,_ged );};};return _bcf ;};

// RedactionOptions is a collection of RedactionTerm objects.
type RedactionOptions struct{Terms []RedactionTerm ;};func _agd (_bce *_fc .TextMarkArray )*_bba .PdfFont {_ ,_bfag :=_ff (_bce );_fgb :=_bce .Elements ()[_bfag ];_dbb :=_fgb .Font ;return _dbb ;};func _cab (_bbbc RedactionTerm )(*regexMatcher ,error ){return &regexMatcher {_ggba :_bbbc },nil };
func (_bdgb *regexMatcher )match (_babf string )([]*matchedIndex ,error ){_bca :=_bdgb ._ggba .Pattern ;if _bca ==nil {return nil ,_f .New ("\u006e\u006f\u0020\u0070at\u0074\u0065\u0072\u006e\u0020\u0063\u006f\u006d\u0070\u0069\u006c\u0065\u0064");};var (_bdga =_bca .FindAllStringIndex (_babf ,-1);
_bgc []*matchedIndex ;);for _ ,_beg :=range _bdga {_bgc =append (_bgc ,&matchedIndex {_fef :_beg [0],_fdd :_beg [1],_dge :_babf [_beg [0]:_beg [1]]});};return _bgc ,nil ;};

// RectangleProps defines properties of the redaction rectangle to be drawn.
type RectangleProps struct{FillColor _bb .Color ;BorderWidth float64 ;FillOpacity float64 ;};func _gdc (_aae *_fc .TextMarkArray )(float64 ,error ){_fa ,_gff :=_aae .BBox ();if !_gff {return 0.0,_c .Errorf ("\u0073\u0070\u0061\u006e\u004d\u0061\u0072\u006bs\u002e\u0042\u0042ox\u0020\u0068\u0061\u0073\u0020\u006eo\u0020\u0062\u006f\u0075\u006e\u0064\u0069\u006e\u0067\u0020\u0062\u006f\u0078\u002e\u0020s\u0070\u0061\u006e\u004d\u0061\u0072\u006b\u0073=\u0025\u0073",_aae );
};_fcec :=_fea (_aae );_gce :=0.0;_ ,_efae :=_ff (_aae );_edcd :=_aae .Elements ()[_efae ];_adedf :=_edcd .Font ;if _fcec > 0{_gce =_gfa (_adedf ,_edcd );};_dbg :=(_fa .Urx -_fa .Llx );_dbg =_dbg +_gce *float64 (_fcec );Tj :=_dcg (_dbg ,_edcd .FontSize ,_edcd .Th );
return Tj ,nil ;};

// RedactionTerm holds the regexp pattern and the replacement string for the redaction process.
type RedactionTerm struct{Pattern *_a .Regexp ;};func _gfa (_gdf *_bba .PdfFont ,_bd _fc .TextMark )float64 {_bfe :=0.001;_gcbe :=_bd .Th /100;if _gdf .Subtype ()=="\u0054\u0079\u0070e\u0033"{_bfe =1;};_fbg ,_gcbd :=_gdf .GetRuneMetrics (' ');if !_gcbd {_fbg ,_gcbd =_gdf .GetCharMetrics (32);
};if !_gcbd {_fbg ,_ =_bba .DefaultFont ().GetRuneMetrics (' ');};_ebd :=_bfe *((_fbg .Wx *_bd .FontSize +_bd .Tc +_bd .Tw )/_gcbe );return _ebd ;};type matchedBBox struct{_ggc _bba .PdfRectangle ;_gfbc string ;};func _ff (_ggb *_fc .TextMarkArray )(_da .PdfObject ,int ){var _cc _da .PdfObject ;
_fbf :=-1;for _afb ,_ag :=range _ggb .Elements (){_cc =_ag .DirectObject ;_fbf =_afb ;if _cc !=nil {break ;};};return _cc ,_fbf ;};

// Write writes the content of `re.creator` to writer of type io.Writer interface.
func (_dfdb *Redactor )Write (writer _d .Writer )error {return _dfdb ._faab .Write (writer )};func _cce (_ccea ,_dfc string )[]int {if len (_dfc )==0{return nil ;};var _dea []int ;for _gag :=0;_gag < len (_ccea );{_cfd :=_ad .Index (_ccea [_gag :],_dfc );
if _cfd < 0{return _dea ;};_dea =append (_dea ,_gag +_cfd );_gag +=_cfd +len (_dfc );};return _dea ;};func _gab (_feb []localSpanMarks )(map[string ][]localSpanMarks ,[]string ){_dbef :=make (map[string ][]localSpanMarks );_ecd :=[]string {};for _ ,_ge :=range _feb {_gfg :=_ge ._aaea ;
if _bfaa ,_agad :=_dbef [_gfg ];_agad {_dbef [_gfg ]=append (_bfaa ,_ge );}else {_dbef [_gfg ]=[]localSpanMarks {_ge };_ecd =append (_ecd ,_gfg );};};return _dbef ,_ecd ;};func _ebdf (_dbd _da .PdfObject ,_gfbd *_bba .PdfFont )(string ,error ){_afdeb ,_adeb :=_da .GetStringBytes (_dbd );
if !_adeb {return "",_da .ErrTypeError ;};_gde :=_gfbd .BytesToCharcodes (_afdeb );_ecbg ,_edda ,_gbg :=_gfbd .CharcodesToStrings (_gde ,"");if _gbg > 0{_fe .Log .Debug ("\u0072\u0065nd\u0065\u0072\u0054e\u0078\u0074\u003a\u0020num\u0043ha\u0072\u0073\u003d\u0025\u0064\u0020\u006eum\u004d\u0069\u0073\u0073\u0065\u0073\u003d%\u0064",_edda ,_gbg );
};_fcb :=_ad .Join (_ecbg ,"");return _fcb ,nil ;};func _dfe (_dacg ,_beca targetMap )(bool ,[]int ){_caa :=_ad .Contains (_dacg ._bbbg ,_beca ._bbbg );var _bagb []int ;for _ ,_edf :=range _dacg ._bdeb {for _feee ,_fbgf :=range _beca ._bdeb {if _fbgf [0]>=_edf [0]&&_fbgf [1]<=_edf [1]{_bagb =append (_bagb ,_feee );
};};};return _caa ,_bagb ;};func _dgf (_cge *_fc .TextMarkArray )string {_caf :="";for _ ,_bfef :=range _cge .Elements (){_caf +=_bfef .Text ;};return _caf ;};type replacement struct{_daf string ;_fg float64 ;_bg int ;};

// New instantiates a Redactor object with given PdfReader and `regex` pattern.
func New (reader *_bba .PdfReader ,opts *RedactionOptions ,rectProps *RectangleProps )*Redactor {if rectProps ==nil {rectProps =RedactRectanglePropsNew ();};return &Redactor {_age :reader ,_dcb :opts ,_faab :_bb .New (),_ecc :rectProps };};type placeHolders struct{_ce []int ;
_e string ;_eb float64 ;};type matchedIndex struct{_fef int ;_fdd int ;_dge string ;};func _fea (_gd *_fc .TextMarkArray )int {_gg :=0;_dab :=_gd .Elements ();if _dab [0].Text =="\u0020"{_gg ++;};if _dab [_gd .Len ()-1].Text =="\u0020"{_gg ++;};return _gg ;
};

// WriteToFile writes the redacted document to file specified by `outputPath`.
func (_cfef *Redactor )WriteToFile (outputPath string )error {if _cbgc :=_cfef ._faab .WriteToFile (outputPath );_cbgc !=nil {return _c .Errorf ("\u0066\u0061\u0069l\u0065\u0064\u0020\u0074o\u0020\u0077\u0072\u0069\u0074\u0065\u0020t\u0068\u0065\u0020\u006f\u0075\u0074\u0070\u0075\u0074\u0020\u0066\u0069\u006c\u0065");
};return nil ;};func _badd (_bgff *matchedIndex ,_dgg [][]int )(bool ,[][]int ){_cadb :=[][]int {};for _ ,_fdbc :=range _dgg {if _bgff ._fef < _fdbc [0]&&_bgff ._fdd > _fdbc [1]{_cadb =append (_cadb ,_fdbc );};};return len (_cadb )> 0,_cadb ;};func _aeb (_cfg string )(string ,[][]int ){_gcfc :=_a .MustCompile ("\u005c\u006e");
_dbeg :=_gcfc .FindAllStringIndex (_cfg ,-1);_cgag :=_gcfc .ReplaceAllString (_cfg ,"\u0020");return _cgag ,_dbeg ;};func _ded (_ebe *_dg .ContentStreamOperation ,_baf _da .PdfObject ,_aa []localSpanMarks )error {var _cff *_da .PdfObjectArray ;_gcd ,_ecf :=_gab (_aa );
if len (_ecf )==1{_bbc :=_ecf [0];_cdb :=_gcd [_bbc ];if len (_cdb )==1{_gcf :=_cdb [0];_cbe :=_gcf ._bdgc ;_ceg :=_agd (_cbe );_dag ,_fbfe :=_ebdf (_baf ,_ceg );if _fbfe !=nil {return _fbfe ;};_aca ,_fbfe :=_afd (_gcf ,_cbe ,_ceg ,_dag ,_bbc );if _fbfe !=nil {return _fbfe ;
};_cff =_da .MakeArray (_aca ...);}else {_bde :=_cdb [0]._bdgc ;_bab :=_agd (_bde );_bfdf ,_ede :=_ebdf (_baf ,_bab );if _ede !=nil {return _ede ;};_gdfc ,_ede :=_dfg (_bfdf ,_cdb );if _ede !=nil {return _ede ;};_cfe :=_ffc (_gdfc );_acg :=_aef (_bfdf ,_cfe ,_bab );
_cff =_da .MakeArray (_acg ...);};}else if len (_ecf )> 1{_acb :=_aa [0];_bdf :=_acb ._bdgc ;_ ,_cga :=_ff (_bdf );_bfdc :=_bdf .Elements ()[_cga ];_dafe :=_bfdc .Font ;_bdg ,_cgg :=_ebdf (_baf ,_dafe );if _cgg !=nil {return _cgg ;};_gfag ,_cgg :=_dfg (_bdg ,_aa );
if _cgg !=nil {return _cgg ;};_gcbf :=_ffc (_gfag );_gea :=_aef (_bdg ,_gcbf ,_dafe );_cff =_da .MakeArray (_gea ...);};_ebe .Params [0]=_cff ;_ebe .Operand ="\u0054\u004a";return nil ;};type targetMap struct{_bbbg string ;_bdeb [][]int ;};func _egeg (_gaef []*matchedIndex )[]*targetMap {_acf :=make (map[string ][][]int );
_cgge :=[]*targetMap {};for _ ,_eaec :=range _gaef {_dbcd :=_eaec ._dge ;_eede :=[]int {_eaec ._fef ,_eaec ._fdd };if _cafe ,_dgbe :=_acf [_dbcd ];_dgbe {_acf [_dbcd ]=append (_cafe ,_eede );}else {_acf [_dbcd ]=[][]int {_eede };};};for _cdf ,_bgdf :=range _acf {_ffd :=&targetMap {_bbbg :_cdf ,_bdeb :_bgdf };
_cgge =append (_cgge ,_ffd );};return _cgge ;};func _gbcb (_dbeb []*matchedIndex ,_dcc [][]int )[]*matchedIndex {_egb :=[]*matchedIndex {};for _ ,_ffdb :=range _dbeb {_edbac ,_cfde :=_badd (_ffdb ,_dcc );if _edbac {_dage :=_bccc (_ffdb ,_cfde );_egb =append (_egb ,_dage ...);
}else {_egb =append (_egb ,_ffdb );};};return _egb ;};func _ffc (_bced []placeHolders )[]replacement {_edd :=[]replacement {};for _ ,_abg :=range _bced {_fge :=_abg ._ce ;_deeb :=_abg ._e ;_addg :=_abg ._eb ;for _ ,_becb :=range _fge {_gdd :=replacement {_daf :_deeb ,_fg :_addg ,_bg :_becb };
_edd =append (_edd ,_gdd );};};_ae .Slice (_edd ,func (_fcd ,_afbf int )bool {return _edd [_fcd ]._bg < _edd [_afbf ]._bg });return _edd ;};

// Redact executes the redact operation on a pdf file and updates the content streams of all pages of the file.
func (_defd *Redactor )Redact ()error {_dgb ,_gbga :=_defd ._age .GetNumPages ();if _gbga !=nil {return _c .Errorf ("\u0066\u0061\u0069\u006c\u0065\u0064 \u0074\u006f\u0020\u0067\u0065\u0074\u0020\u0074\u0068\u0065\u0020\u006e\u0075m\u0062\u0065\u0072\u0020\u006f\u0066\u0020P\u0061\u0067\u0065\u0073");
};_bac :=_defd ._ecc .FillColor ;_cde :=_defd ._ecc .BorderWidth ;_gdca :=_defd ._ecc .FillOpacity ;for _eacd :=1;_eacd <=_dgb ;_eacd ++{_gdg ,_fbd :=_defd ._age .GetPage (_eacd );if _fbd !=nil {return _fbd ;};_aegb ,_fbd :=_fc .New (_gdg );if _fbd !=nil {return _fbd ;
};_febg ,_ ,_ ,_fbd :=_aegb .ExtractPageText ();if _fbd !=nil {return _fbd ;};_fdb :=_febg .GetContentStreamOps ();_bef ,_dbgc ,_fbd :=_defd .redactPage (_fdb ,_gdg .Resources );if _dbgc ==nil {_fe .Log .Info ("N\u006f\u0020\u006d\u0061\u0074\u0063\u0068\u0020\u0066\u006f\u0075\u006e\u0064\u0020\u0066\u006f\u0072\u0020t\u0068\u0065\u0020\u0070\u0072\u006f\u0076\u0069\u0064\u0065d \u0070\u0061\u0074t\u0061r\u006e\u002e");
_dbgc =_fdb ;};_faabg :=_dg .ContentStreamOperation {Operand :"\u006e"};*_dbgc =append (*_dbgc ,&_faabg );_gdg .SetContentStreams ([]string {_dbgc .String ()},_da .NewFlateEncoder ());if _fbd !=nil {return _fbd ;};_baa ,_fbd :=_gdg .GetMediaBox ();if _fbd !=nil {return _fbd ;
};if _gdg .MediaBox ==nil {_gdg .MediaBox =_baa ;};if _dbc :=_defd ._faab .AddPage (_gdg );_dbc !=nil {return _dbc ;};_ae .Slice (_bef ,func (_ffg ,_fdcb int )bool {return _bef [_ffg ]._gfbc < _bef [_fdcb ]._gfbc });_befd :=_baa .Ury ;for _ ,_dgfe :=range _bef {_fgd :=_dgfe ._ggc ;
_eag :=_defd ._faab .NewRectangle (_fgd .Llx ,_befd -_fgd .Lly ,_fgd .Urx -_fgd .Llx ,-(_fgd .Ury -_fgd .Lly ));_eag .SetFillColor (_bac );_eag .SetBorderWidth (_cde );_eag .SetFillOpacity (_gdca );if _edba :=_defd ._faab .Draw (_eag );_edba !=nil {return nil ;
};};};_defd ._faab .SetOutlineTree (_defd ._age .GetOutlineTree ());return nil ;};func _afd (_cbb localSpanMarks ,_eeec *_fc .TextMarkArray ,_ecdb *_bba .PdfFont ,_gfaa ,_bccf string )([]_da .PdfObject ,error ){_cbg :=_dgf (_eeec );Tj ,_agg :=_gdc (_eeec );
if _agg !=nil {return nil ,_agg ;};_abe :=len (_gfaa );_bec :=len (_cbg );_afde :=-1;_aac :=_da .MakeFloat (Tj );if _cbg !=_bccf {_dgc :=_cbb ._fec ;if _dgc ==0{_afde =_ad .LastIndex (_gfaa ,_cbg );}else {_afde =_ad .Index (_gfaa ,_cbg );};}else {_afde =_ad .Index (_gfaa ,_cbg );
};_edgg :=_afde +_bec ;_eea :=[]_da .PdfObject {};if _afde ==0&&_edgg ==_abe {_eea =append (_eea ,_aac );}else if _afde ==0&&_edgg < _abe {_ece :=_bfd (_gfaa [_edgg :],_ecdb );_gge :=_da .MakeStringFromBytes (_ece );_eea =append (_eea ,_aac ,_gge );}else if _afde > 0&&_edgg >=_abe {_cea :=_bfd (_gfaa [:_afde ],_ecdb );
_def :=_da .MakeStringFromBytes (_cea );_eea =append (_eea ,_def ,_aac );}else if _afde > 0&&_edgg < _abe {_acd :=_bfd (_gfaa [:_afde ],_ecdb );_ggf :=_bfd (_gfaa [_edgg :],_ecdb );_bed :=_da .MakeStringFromBytes (_acd );_dba :=_da .MakeString (string (_ggf ));
_eea =append (_eea ,_bed ,_aac ,_dba );};return _eea ,nil ;};func _faa (_aace *_dg .ContentStreamOperations ,PdfObj _da .PdfObject )(*_dg .ContentStreamOperation ,int ,bool ){for _cffa ,_ecb :=range *_aace {_abga :=_ecb .Operand ;if _abga =="\u0054\u006a"{_ddd :=_da .TraceToDirectObject (_ecb .Params [0]);
if _ddd ==PdfObj {return _ecb ,_cffa ,true ;};}else if _abga =="\u0054\u004a"{_ddc ,_fdc :=_da .GetArray (_ecb .Params [0]);if !_fdc {return nil ,_cffa ,_fdc ;};for _ ,_edce :=range _ddc .Elements (){if _edce ==PdfObj {return _ecb ,_cffa ,true ;};};}else if _abga =="\u0022"{_bgfg :=_da .TraceToDirectObject (_ecb .Params [2]);
if _bgfg ==PdfObj {return _ecb ,_cffa ,true ;};}else if _abga =="\u0027"{_ddaa :=_da .TraceToDirectObject (_ecb .Params [0]);if _ddaa ==PdfObj {return _ecb ,_cffa ,true ;};};};return nil ,-1,false ;};func (_fbbg *Redactor )redactPage (_afg *_dg .ContentStreamOperations ,_debd *_bba .PdfPageResources )([]matchedBBox ,*_dg .ContentStreamOperations ,error ){_cdd ,_ceb :=_fc .NewFromContents (_afg .String (),_debd );
if _ceb !=nil {return nil ,nil ,_ceb ;};_dgfb ,_ ,_ ,_ceb :=_cdd .ExtractPageText ();if _ceb !=nil {return nil ,nil ,_ceb ;};_afg =_dgfb .GetContentStreamOps ();_ege :=_dgfb .Marks ();_eebb :=_dgfb .Text ();_eebb ,_egf :=_aeb (_eebb );_gfbb :=[]matchedBBox {};
_bbf :=make (map[_da .PdfObject ][]localSpanMarks );_eagb :=[]*targetMap {};for _ ,_ffb :=range _fbbg ._dcb .Terms {_cfc ,_cefa :=_cab (_ffb );if _cefa !=nil {return nil ,nil ,_cefa ;};_bad ,_cefa :=_cfc .match (_eebb );if _cefa !=nil {return nil ,nil ,_cefa ;
};_bad =_gbcb (_bad ,_egf );_agc :=_egeg (_bad );_eagb =append (_eagb ,_agc ...);};_dgfbe (_eagb );for _ ,_eaae :=range _eagb {_daad :=_eaae ._bbbg ;_ecfg :=_eaae ._bdeb ;_dec :=[]matchedBBox {};for _ ,_eaga :=range _ecfg {_dfd ,_dabf ,_deac :=_dfbc (_eaga ,_ege ,_daad );
if _deac !=nil {return nil ,nil ,_deac ;};_aea :=_dbbc (_dfd );for _cbd ,_bgg :=range _aea {_ebeg :=localSpanMarks {_bdgc :_bgg ,_fec :_cbd ,_aaea :_daad };_ddg ,_ :=_ff (_bgg );if _cca ,_eace :=_bbf [_ddg ];_eace {_bbf [_ddg ]=append (_cca ,_ebeg );}else {_bbf [_ddg ]=[]localSpanMarks {_ebeg };
};};_dec =append (_dec ,_dabf );};_gfbb =append (_gfbb ,_dec ...);};_ceb =_ea (_afg ,_bbf );if _ceb !=nil {return nil ,nil ,_ceb ;};return _gfbb ,_afg ,nil ;};type regexMatcher struct{_ggba RedactionTerm };

// RedactRectanglePropsNew return a new pointer to a default RectangleProps object.
func RedactRectanglePropsNew ()*RectangleProps {return &RectangleProps {FillColor :_bb .ColorBlack ,BorderWidth :0.0,FillOpacity :1.0};};func _bccc (_dfeb *matchedIndex ,_gdgc [][]int )[]*matchedIndex {_fdgb :=[]*matchedIndex {};_fffc :=_dfeb ._fef ;_bfda :=_fffc ;
_defb :=_dfeb ._dge ;_efd :=0;for _ ,_dgdg :=range _gdgc {_ddf :=_dgdg [0]-_fffc ;if _efd >=_ddf {continue ;};_aaf :=_defb [_efd :_ddf ];_dbegg :=&matchedIndex {_dge :_aaf ,_fef :_bfda ,_fdd :_dgdg [0]};if len (_ad .TrimSpace (_aaf ))!=0{_fdgb =append (_fdgb ,_dbegg );
};_efd =_dgdg [1]-_fffc ;_bfda =_fffc +_efd ;};_bgde :=_defb [_efd :];_gccb :=&matchedIndex {_dge :_bgde ,_fef :_bfda ,_fdd :_dfeb ._fdd };if len (_ad .TrimSpace (_bgde ))!=0{_fdgb =append (_fdgb ,_gccb );};return _fdgb ;};func _add (_db *_dg .ContentStreamOperation ,_gb _da .PdfObject ,_dac []localSpanMarks )error {_gca ,_eee :=_da .GetArray (_db .Params [0]);
_gbb :=[]_da .PdfObject {};if !_eee {_fe .Log .Debug ("\u0045\u0052\u0052OR\u003a\u0020\u0054\u004a\u0020\u006f\u0070\u003d\u0025s\u0020G\u0065t\u0041r\u0072\u0061\u0079\u0056\u0061\u006c\u0020\u0066\u0061\u0069\u006c\u0065\u0064",_db );return _c .Errorf ("\u0073\u0070\u0061\u006e\u004d\u0061\u0072\u006bs\u002e\u0042\u0042ox\u0020\u0068\u0061\u0073\u0020\u006eo\u0020\u0062\u006f\u0075\u006e\u0064\u0069\u006e\u0067\u0020\u0062\u006f\u0078\u002e\u0020s\u0070\u0061\u006e\u004d\u0061\u0072\u006b\u0073=\u0025\u0073",_db );
};_fce ,_ade :=_gab (_dac );if len (_ade )==1{_eaf :=_ade [0];_gga :=_fce [_eaf ];if len (_gga )==1{_dd :=_gga [0];_cf :=_dd ._bdgc ;_bag :=_agd (_cf );_eafd ,_de :=_ebdf (_gb ,_bag );if _de !=nil {return _de ;};_ef ,_de :=_afd (_dd ,_cf ,_bag ,_eafd ,_eaf );
if _de !=nil {return _de ;};for _ ,_dad :=range _gca .Elements (){if _dad ==_gb {_gbb =append (_gbb ,_ef ...);}else {_gbb =append (_gbb ,_dad );};};}else {_dda :=_gga [0]._bdgc ;_bgf :=_agd (_dda );_ga ,_cb :=_ebdf (_gb ,_bgf );if _cb !=nil {return _cb ;
};_fbb ,_cb :=_dfg (_ga ,_gga );if _cb !=nil {return _cb ;};_ed :=_ffc (_fbb );_ab :=_aef (_ga ,_ed ,_bgf );for _ ,_cec :=range _gca .Elements (){if _cec ==_gb {_gbb =append (_gbb ,_ab ...);}else {_gbb =append (_gbb ,_cec );};};};_db .Params [0]=_da .MakeArray (_gbb ...);
}else if len (_ade )> 1{_be :=_dac [0];_aeg :=_be ._bdgc ;_ ,_gcb :=_ff (_aeg );_aga :=_aeg .Elements ()[_gcb ];_deb :=_aga .Font ;_bgd ,_bbg :=_ebdf (_gb ,_deb );if _bbg !=nil {return _bbg ;};_bfa ,_bbg :=_dfg (_bgd ,_dac );if _bbg !=nil {return _bbg ;
};_cecd :=_ffc (_bfa );_dbe :=_aef (_bgd ,_cecd ,_deb );for _ ,_bbb :=range _gca .Elements (){if _bbb ==_gb {_gbb =append (_gbb ,_dbe ...);}else {_gbb =append (_gbb ,_bbb );};};_db .Params [0]=_da .MakeArray (_gbb ...);};return nil ;};func _dfbc (_fdg []int ,_cfcf *_fc .TextMarkArray ,_ffcf string )(*_fc .TextMarkArray ,matchedBBox ,error ){_cdg :=matchedBBox {};
_bedg :=_fdg [0];_ebc :=_fdg [1];_adg :=len (_ffcf )-len (_ad .TrimLeft (_ffcf ,"\u0020"));_efc :=len (_ffcf )-len (_ad .TrimRight (_ffcf ,"\u0020\u000a"));_bedg =_bedg +_adg ;_ebc =_ebc -_efc ;_ffcf =_ad .Trim (_ffcf ,"\u0020\u000a");_daae ,_eedf :=_cfcf .RangeOffset (_bedg ,_ebc );
if _eedf !=nil {return nil ,_cdg ,_eedf ;};_dbf ,_deab :=_daae .BBox ();if !_deab {return nil ,_cdg ,_c .Errorf ("\u0073\u0070\u0061\u006e\u004d\u0061\u0072\u006bs\u002e\u0042\u0042ox\u0020\u0068\u0061\u0073\u0020\u006eo\u0020\u0062\u006f\u0075\u006e\u0064\u0069\u006e\u0067\u0020\u0062\u006f\u0078\u002e\u0020s\u0070\u0061\u006e\u004d\u0061\u0072\u006b\u0073=\u0025\u0073",_daae );
};_cdg =matchedBBox {_gfbc :_ffcf ,_ggc :_dbf };return _daae ,_cdg ,nil ;};func _bacc (_ecfe *targetMap ,_bcef []int ){var _bfc [][]int ;for _fba ,_gfd :=range _ecfe ._bdeb {if _bccfb (_fba ,_bcef ){continue ;};_bfc =append (_bfc ,_gfd );};_ecfe ._bdeb =_bfc ;
};func _dfg (_bae string ,_cef []localSpanMarks )([]placeHolders ,error ){_aded :="";_eba :=[]placeHolders {};for _edc ,_eac :=range _cef {_dee :=_eac ._bdgc ;_fgc :=_eac ._aaea ;_fd :=_dgf (_dee );_gcce ,_eed :=_gdc (_dee );if _eed !=nil {return nil ,_eed ;
};if _fd !=_aded {var _deg []int ;if _edc ==0&&_fgc !=_fd {_fca :=_ad .Index (_bae ,_fd );_deg =[]int {_fca };}else if _edc ==len (_cef )-1{_agb :=_ad .LastIndex (_bae ,_fd );_deg =[]int {_agb };}else {_deg =_cce (_bae ,_fd );};_eeb :=placeHolders {_ce :_deg ,_e :_fd ,_eb :_gcce };
_eba =append (_eba ,_eeb );};_aded =_fd ;};return _eba ,nil ;};

// Redactor represents a Redactor object.
type Redactor struct{_age *_bba .PdfReader ;_dcb *RedactionOptions ;_faab *_bb .Creator ;_ecc *RectangleProps ;};