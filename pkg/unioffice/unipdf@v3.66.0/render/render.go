//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package render ;import (_g "errors";_cb "fmt";_ab "github.com/adrg/sysfont";_df "github.com/unidoc/unipdf/v3/annotator";_cd "github.com/unidoc/unipdf/v3/common";_bc "github.com/unidoc/unipdf/v3/contentstream";_ga "github.com/unidoc/unipdf/v3/contentstream/draw";
_fa "github.com/unidoc/unipdf/v3/core";_dg "github.com/unidoc/unipdf/v3/internal/license";_ae "github.com/unidoc/unipdf/v3/internal/transform";_gae "github.com/unidoc/unipdf/v3/model";_gd "github.com/unidoc/unipdf/v3/render/internal/context";_cbf "github.com/unidoc/unipdf/v3/render/internal/context/imagerender";
_ec "golang.org/x/image/draw";_bf "image";_ba "image/color";_d "image/draw";_f "image/jpeg";_da "image/png";_c "math";_b "os";_ef "path/filepath";_e "strings";);func _dac (_bdgc string ,_gfdd _bf .Image )error {_fea ,_egfg :=_b .Create (_bdgc );if _egfg !=nil {return _egfg ;
};defer _fea .Close ();return _da .Encode (_fea ,_gfdd );};func (_gcgc renderer )processLinearShading (_efdb _gd .Context ,_bag *_gae .PdfShading )(_gd .Gradient ,*_fa .PdfObjectArray ,error ){_aggc :=_bag .GetContext ().(*_gae .PdfShadingType2 );if len (_aggc .Function )==0{return nil ,nil ,_g .New ("\u006e\u006f\u0020\u0067\u0072\u0061\u0064i\u0065\u006e\u0074 \u0066\u0075\u006e\u0063t\u0069\u006f\u006e\u0020\u0066\u006f\u0075\u006e\u0064\u002c\u0020\u0073\u006b\u0069\u0070\u0020\u0063\u006f\u006e\u0076\u0065\u0072\u0073\u0069\u006f\u006e");
};_gdbe ,_cfaa :=_aggc .Coords .ToFloat64Array ();if _cfaa !=nil {return nil ,nil ,_g .New ("\u0066\u0061\u0069l\u0065\u0064\u0020\u0067e\u0074\u0074\u0069\u006e\u0067\u0020\u0073h\u0061\u0064\u0069\u006e\u0067\u0020\u0070\u006f\u0073\u0069\u0074\u0069\u006f\u006e");
};_cfc :=_bag .ColorSpace ;_adg ,_cca :=_efdb .Matrix ().Transform (_gdbe [0],_gdbe [1]);_bdba ,_bbfe :=_efdb .Matrix ().Transform (_gdbe [2],_gdbe [3]);_dbf :=_cbf .NewLinearGradient (_adg ,_cca ,_bdba ,_bbfe );_ace :=_fa .MakeArrayFromFloats ([]float64 {0,0,1,1});
for _ ,_fcc :=range _gdbe {if _fcc > 1{_ace =_aggc .Coords ;break ;};};if _gccf ,_aeca :=_aggc .Function [0].(*_gae .PdfFunctionType2 );_aeca {_dbf ,_cfaa =_bccg (_dbf ,_gccf ,_cfc ,1.0,true );}else if _cee ,_ebg :=_aggc .Function [0].(*_gae .PdfFunctionType3 );
_ebg {_fga :=append ([]float64 {0},_cee .Bounds ...);_fga =append (_fga ,1.0);_dbf ,_cfaa =_cddc (_dbf ,_cee ,_cfc ,_fga );};return _dbf ,_ace ,_cfaa ;};

// RenderToPath converts the specified PDF page into an image and saves the
// result at the specified location.
func (_gbf *ImageDevice )RenderToPath (page *_gae .PdfPage ,outputPath string )error {_aa ,_bdg :=_gbf .Render (page );if _bdg !=nil {return _bdg ;};_cg :=_e .ToLower (_ef .Ext (outputPath ));if _cg ==""{return _g .New ("\u0063\u006ful\u0064\u0020\u006eo\u0074\u0020\u0072\u0065cog\u006eiz\u0065\u0020\u006f\u0075\u0074\u0070\u0075t \u0066\u0069\u006c\u0065\u0020\u0074\u0079p\u0065");
};switch _cg {case "\u002e\u0070\u006e\u0067":return _dac (outputPath ,_aa );case "\u002e\u006a\u0070\u0067","\u002e\u006a\u0070e\u0067":return _aeecg (outputPath ,_aa ,100);};return _cb .Errorf ("\u0075\u006e\u0072\u0065\u0063\u006fg\u006e\u0069\u007a\u0065\u0064\u0020\u006f\u0075\u0074\u0070\u0075\u0074\u0020f\u0069\u006c\u0065\u0020\u0074\u0079\u0070e\u003a\u0020\u0025\u0073",_cg );
};func (_dad renderer )processShading (_ddac _gd .Context ,_ccc *_gae .PdfShading )(_gd .Gradient ,*_fa .PdfObjectArray ,error ){_gdba :=int64 (*_ccc .ShadingType );if _gdba ==int64 (ShadingTypeAxial ){return _dad .processLinearShading (_ddac ,_ccc );}else if _gdba ==int64 (ShadingTypeRadial ){return _dad .processRadialShading (_ddac ,_ccc );
}else {_cd .Log .Debug (_cb .Sprintf ("\u0050r\u006f\u0063e\u0073\u0073\u0069n\u0067\u0020\u0067\u0072\u0061\u0064\u0069e\u006e\u0074\u0020\u0074\u0079\u0070e\u0020\u0025\u0064\u0020\u006e\u006f\u0074\u0020\u0079\u0065\u0074 \u0073\u0075\u0070\u0070\u006f\u0072\u0074\u0065\u0064",_gdba ));
};return nil ,nil ,nil ;};func _eaae (_ccaa ,_cbac _bf .Image )_bf .Image {_gfb ,_ceaa :=_cbac .Bounds ().Size (),_ccaa .Bounds ().Size ();_aefe ,_gdd :=_gfb .X ,_gfb .Y ;if _ceaa .X > _aefe {_aefe =_ceaa .X ;};if _ceaa .Y > _gdd {_gdd =_ceaa .Y ;};_fccg :=_bf .Rect (0,0,_aefe ,_gdd );
if _gfb .X !=_aefe ||_gfb .Y !=_gdd {_egcae :=_bf .NewRGBA (_fccg );_ec .BiLinear .Scale (_egcae ,_fccg ,_ccaa ,_cbac .Bounds (),_ec .Over ,nil );_cbac =_egcae ;};if _ceaa .X !=_aefe ||_ceaa .Y !=_gdd {_eecg :=_bf .NewRGBA (_fccg );_ec .BiLinear .Scale (_eecg ,_fccg ,_ccaa ,_ccaa .Bounds (),_ec .Over ,nil );
_ccaa =_eecg ;};_fbgg :=_bf .NewRGBA (_fccg );_ec .DrawMask (_fbgg ,_fccg ,_ccaa ,_bf .Point {},_cbac ,_bf .Point {},_ec .Over );return _fbgg ;};func _aefb (_egcac ,_bed ,_ggd float64 )_ga .BoundingBox {return _ga .Path {Points :[]_ga .Point {_ga .NewPoint (0,0).Rotate (_ggd ),_ga .NewPoint (_egcac ,0).Rotate (_ggd ),_ga .NewPoint (0,_bed ).Rotate (_ggd ),_ga .NewPoint (_egcac ,_bed ).Rotate (_ggd )}}.GetBoundingBox ();
};var (_cbg =_g .New ("\u0074\u0079p\u0065\u0020\u0063h\u0065\u0063\u006b\u0020\u0065\u0072\u0072\u006f\u0072");_db =_g .New ("\u0072\u0061\u006e\u0067\u0065\u0020\u0063\u0068\u0065\u0063\u006b\u0020e\u0072\u0072\u006f\u0072"););type renderer struct{_ge float64 };
func (_gbgb renderer )processGradient (_ddd _gd .Context ,_feg *_bc .ContentStreamOperation ,_gbbbg *_gae .PdfPageResources ,_gcge *_fa .PdfObjectName )(_gd .Gradient ,error ){if _dgb ,_beg :=_gbbbg .GetPatternByName (*_gcge );_beg &&_dgb .IsShading (){_fgbb :=_dgb .GetAsShadingPattern ().Shading ;
_gdg ,_ ,_cag :=_gbgb .processShading (_ddd ,_fgbb );if _cag !=nil {return nil ,_cag ;};return _gdg ,nil ;};return nil ,nil ;};func _aebf (_bdagd *_gae .Image ,_gbad _ba .Color )_bf .Image {_cbfd ,_cfe :=int (_bdagd .Width ),int (_bdagd .Height );_bfgd :=_bf .NewRGBA (_bf .Rect (0,0,_cbfd ,_cfe ));
for _acab :=0;_acab < _cfe ;_acab ++{for _fcf :=0;_fcf < _cbfd ;_fcf ++{_aabb ,_gdcaa :=_bdagd .ColorAt (_fcf ,_acab );if _gdcaa !=nil {_cd .Log .Debug ("\u0057\u0041\u0052\u004e\u003a\u0020\u0063o\u0075\u006c\u0064\u0020\u006e\u006f\u0074\u0020\u0072\u0065\u0074\u0072\u0069\u0065v\u0065 \u0069\u006d\u0061\u0067\u0065\u0020m\u0061\u0073\u006b\u0020\u0076\u0061\u006cu\u0065\u0020\u0061\u0074\u0020\u0028\u0025\u0064\u002c\u0020\u0025\u0064\u0029\u002e\u0020\u004f\u0075\u0074\u0070\u0075\u0074\u0020\u006da\u0079\u0020\u0062\u0065\u0020\u0069\u006e\u0063\u006f\u0072\u0072\u0065\u0063t\u002e",_fcf ,_acab );
continue ;};_acd ,_eaad ,_dgd ,_ :=_aabb .RGBA ();var _def _ba .Color ;if _acd +_eaad +_dgd ==0{_def =_gbad ;}else {_def =_ba .Transparent ;};_bfgd .Set (_fcf ,_acab ,_def );};};return _bfgd ;};

// NewImageDevice returns a new image device.
func NewImageDevice ()*ImageDevice {const _ca ="r\u0065\u006e\u0064\u0065r.\u004ee\u0077\u0049\u006d\u0061\u0067e\u0044\u0065\u0076\u0069\u0063\u0065";_dg .TrackUse (_ca );return &ImageDevice {};};func _cddc (_bdab _gd .Gradient ,_fab *_gae .PdfFunctionType3 ,_gggd _gae .PdfColorspace ,_gdec []float64 )(_gd .Gradient ,error ){var _eafc error ;
for _geed :=0;_geed < len (_fab .Functions );_geed ++{if _aedf ,_deb :=_fab .Functions [_geed ].(*_gae .PdfFunctionType2 );_deb {_bdab ,_eafc =_bccg (_bdab ,_aedf ,_gggd ,_gdec [_geed +1],_geed ==0);if _eafc !=nil {return nil ,_eafc ;};};};return _bdab ,nil ;
};

// RenderWithOpts converts the specified PDF page into an image, optionally flattens annotations and returns the result.
func (_dge *ImageDevice )RenderWithOpts (page *_gae .PdfPage ,skipFlattening bool )(_bf .Image ,error ){_fc ,_ggg :=page .GetMediaBox ();if _ggg !=nil {return nil ,_ggg ;};_fc .Normalize ();_gdc :=page .CropBox ;var _bd ,_aec float64 ;if _gdc !=nil {_gdc .Normalize ();
_bd ,_aec =_gdc .Width (),_gdc .Height ();};_bg :=page .Rotate ;_ad ,_bb ,_fb ,_fbd :=_fc .Llx ,_fc .Lly ,_fc .Width (),_fc .Height ();_gc :=_ae .IdentityMatrix ();if _bg !=nil &&*_bg %360!=0&&*_bg %90==0{_gdcg :=-float64 (*_bg );_gf :=_aefb (_fb ,_fbd ,_gdcg );
_gc =_gc .Translate ((_gf .Width -_fb )/2+_fb /2,(_gf .Height -_fbd )/2+_fbd /2).Rotate (_gdcg *_c .Pi /180).Translate (-_fb /2,-_fbd /2);_fb ,_fbd =_gf .Width ,_gf .Height ;if _gdc !=nil {_bcb :=_aefb (_bd ,_aec ,_gdcg );_bd ,_aec =_bcb .Width ,_bcb .Height ;
};};if _ad !=0||_bb !=0{_gc =_gc .Translate (-_ad ,-_bb );};_dge ._ge =1.0;if _dge .OutputWidth !=0{_ed :=_fb ;if _gdc !=nil {_ed =_bd ;};_dge ._ge =float64 (_dge .OutputWidth )/_ed ;_fb ,_fbd ,_bd ,_aec =_fb *_dge ._ge ,_fbd *_dge ._ge ,_bd *_dge ._ge ,_aec *_dge ._ge ;
_gc =_ae .ScaleMatrix (_dge ._ge ,_dge ._ge ).Mult (_gc );};_aee :=_cbf .NewContext (int (_fb ),int (_fbd ));if _dae :=_dge .renderPage (_aee ,page ,_gc ,skipFlattening );_dae !=nil {return nil ,_dae ;};_de :=_aee .Image ();if _gdc !=nil {_bcd ,_ded :=(_gdc .Llx -_ad )*_dge ._ge ,(_gdc .Lly -_bb )*_dge ._ge ;
_gb :=_bf .Rect (0,0,int (_bd ),int (_aec ));_be :=_bf .Pt (int (_bcd ),int (_fbd -_ded -_aec ));_bfe :=_bf .NewRGBA (_gb );_d .Draw (_bfe ,_gb ,_de ,_be ,_d .Src );_de =_bfe ;};return _de ,nil ;};func _bccg (_fefg _gd .Gradient ,_begd *_gae .PdfFunctionType2 ,_gabc _gae .PdfColorspace ,_afgc float64 ,_bgg bool )(_gd .Gradient ,error ){switch _gabc .(type ){case *_gae .PdfColorspaceDeviceRGB :if len (_begd .C0 )!=3||len (_begd .C1 )!=3{return nil ,_g .New ("\u0069\u006e\u0063\u006f\u0072\u0072\u0065\u0063\u0074\u0020\u0052\u0047\u0042\u0020\u0063o\u006co\u0072\u0020\u0061\u0072\u0072\u0061\u0079\u0020\u006c\u0065\u006e\u0067\u0074\u0068");
};_dagf :=_begd .C0 ;_eecd :=_begd .C1 ;if _bgg {_fefg .AddColorStop (0.0,_ba .RGBA {R :uint8 (_dagf [0]*255),G :uint8 (_dagf [1]*255),B :uint8 (_dagf [2]*255),A :255});};_fefg .AddColorStop (_afgc ,_ba .RGBA {R :uint8 (_eecd [0]*255),G :uint8 (_eecd [1]*255),B :uint8 (_eecd [2]*255),A :255});
case *_gae .PdfColorspaceDeviceCMYK :if len (_begd .C0 )!=4||len (_begd .C1 )!=4{return nil ,_g .New ("\u0069\u006e\u0063\u006f\u0072\u0072e\u0063\u0074\u0020\u0043\u004d\u0059\u004b\u0020\u0063\u006f\u006c\u006f\u0072 \u0061\u0072\u0072\u0061\u0079\u0020\u006ce\u006e\u0067\u0074\u0068");
};_dfe :=_begd .C0 ;_fff :=_begd .C1 ;if _bgg {_fefg .AddColorStop (0.0,_ba .CMYK {C :uint8 (_dfe [0]*255),M :uint8 (_dfe [1]*255),Y :uint8 (_dfe [2]*255),K :uint8 (_dfe [3]*255)});};_fefg .AddColorStop (_afgc ,_ba .CMYK {C :uint8 (_fff [0]*255),M :uint8 (_fff [1]*255),Y :uint8 (_fff [2]*255),K :uint8 (_fff [3]*255)});
default:return nil ,_cb .Errorf ("u\u006e\u0073\u0075\u0070\u0070\u006fr\u0074\u0065\u0064\u0020\u0063\u006f\u006c\u006f\u0072 \u0073\u0070\u0061c\u0065:\u0020\u0025\u0073",_gabc .String ());};return _fefg ,nil ;};func _ffa (_fdac _fa .PdfObject ,_fcdg _ba .Color )(_bf .Image ,error ){_eca ,_egbg :=_fa .GetStream (_fdac );
if !_egbg {return nil ,nil ;};_adc ,_edb :=_gae .NewXObjectImageFromStream (_eca );if _edb !=nil {return nil ,_edb ;};_aafc ,_edb :=_adc .ToImage ();if _edb !=nil {return nil ,_edb ;};return _aebf (_aafc ,_fcdg ),nil ;};

// PdfShadingType defines PDF shading types.
// Source: PDF32000_2008.pdf. Chapter 8.7.4.5
type PdfShadingType int64 ;const (ShadingTypeFunctionBased PdfShadingType =1;ShadingTypeAxial PdfShadingType =2;ShadingTypeRadial PdfShadingType =3;ShadingTypeFreeForm PdfShadingType =4;ShadingTypeLatticeForm PdfShadingType =5;ShadingTypeCoons PdfShadingType =6;
ShadingTypeTensorProduct PdfShadingType =7;);func _acca (_eegb *_gae .Image ,_ddaf _ba .Color )_bf .Image {_cabd ,_ffe :=int (_eegb .Width ),int (_eegb .Height );_dbaa :=_bf .NewRGBA (_bf .Rect (0,0,_cabd ,_ffe ));for _bdc :=0;_bdc < _ffe ;_bdc ++{for _bbfd :=0;
_bbfd < _cabd ;_bbfd ++{_becgc ,_aggb :=_eegb .ColorAt (_bbfd ,_bdc );if _aggb !=nil {_cd .Log .Debug ("\u0057\u0041\u0052\u004e\u003a\u0020\u0063o\u0075\u006c\u0064\u0020\u006e\u006f\u0074\u0020\u0072\u0065\u0074\u0072\u0069\u0065v\u0065 \u0069\u006d\u0061\u0067\u0065\u0020m\u0061\u0073\u006b\u0020\u0076\u0061\u006cu\u0065\u0020\u0061\u0074\u0020\u0028\u0025\u0064\u002c\u0020\u0025\u0064\u0029\u002e\u0020\u004f\u0075\u0074\u0070\u0075\u0074\u0020\u006da\u0079\u0020\u0062\u0065\u0020\u0069\u006e\u0063\u006f\u0072\u0072\u0065\u0063t\u002e",_bbfd ,_bdc );
continue ;};_fcfg ,_gcbc ,_fbb ,_ :=_becgc .RGBA ();var _aedc _ba .Color ;if _fcfg +_gcbc +_fbb ==0{_aedc =_ba .Transparent ;}else {_aedc =_ddaf ;};_dbaa .Set (_bbfd ,_bdc ,_aedc );};};return _dbaa ;};

// Render converts the specified PDF page into an image, flattens annotations by default and returns the result.
func (_gg *ImageDevice )Render (page *_gae .PdfPage )(_bf .Image ,error ){return _gg .RenderWithOpts (page ,false );};func _cfag (_cedb _fa .PdfObject ,_fdca _ba .Color )(_bf .Image ,error ){_caa ,_cec :=_fa .GetStream (_cedb );if !_cec {return nil ,nil ;
};_fdbe ,_gdcd :=_gae .NewXObjectImageFromStream (_caa );if _gdcd !=nil {return nil ,_gdcd ;};_dgcb ,_gdcd :=_fdbe .ToImage ();if _gdcd !=nil {return nil ,_gdcd ;};return _acca (_dgcb ,_fdca ),nil ;};func (_adge renderer )processRadialShading (_dbac _gd .Context ,_aca *_gae .PdfShading )(_gd .Gradient ,*_fa .PdfObjectArray ,error ){_bdag :=_aca .GetContext ().(*_gae .PdfShadingType3 );
if len (_bdag .Function )==0{return nil ,nil ,_g .New ("\u006e\u006f\u0020\u0067\u0072\u0061\u0064i\u0065\u006e\u0074 \u0066\u0075\u006e\u0063t\u0069\u006f\u006e\u0020\u0066\u006f\u0075\u006e\u0064\u002c\u0020\u0073\u006b\u0069\u0070\u0020\u0063\u006f\u006e\u0076\u0065\u0072\u0073\u0069\u006f\u006e");
};_dcad ,_ffdd :=_bdag .Coords .ToFloat64Array ();if _ffdd !=nil {return nil ,nil ,_g .New ("\u0066\u0061\u0069l\u0065\u0064\u0020\u0067e\u0074\u0074\u0069\u006e\u0067\u0020\u0073h\u0061\u0064\u0069\u006e\u0067\u0020\u0070\u006f\u0073\u0069\u0074\u0069\u006f\u006e");
};_caed :=_aca .ColorSpace ;_cbeg :=_fa .MakeArrayFromFloats ([]float64 {0,0,1,1});var _aae ,_gacg ,_acbb ,_gebe ,_bdeg ,_bcbg float64 ;_aae ,_gacg =_dbac .Matrix ().Transform (_dcad [0],_dcad [1]);_acbb ,_gebe =_dbac .Matrix ().Transform (_dcad [3],_dcad [4]);
_bdeg ,_ =_dbac .Matrix ().Transform (_dcad [2],0);_bcbg ,_ =_dbac .Matrix ().Transform (_dcad [5],0);_ecgb ,_ :=_dbac .Matrix ().Translation ();_bdeg -=_ecgb ;_bcbg -=_ecgb ;for _eae ,_acc :=range _dcad {if _eae ==2||_eae ==5{continue ;};if _acc > 1.0{_geab :=_c .Min (_aae -_bdeg ,_acbb -_bcbg );
_aegf :=_c .Min (_gacg -_bdeg ,_gebe -_bcbg );_bbfee :=_c .Max (_aae +_bdeg ,_acbb +_bcbg );_becg :=_c .Max (_gacg +_bdeg ,_gebe +_bcbg );_gcbd :=_bbfee -_geab ;_acea :=_aegf -_becg ;_cbeg =_fa .MakeArrayFromFloats ([]float64 {_geab ,_aegf ,_gcbd ,_acea });
break ;};};_aeeg :=_cbf .NewRadialGradient (_aae ,_gacg ,_bdeg ,_acbb ,_gebe ,_bcbg );if _aeec ,_dbb :=_bdag .Function [0].(*_gae .PdfFunctionType2 );_dbb {_aeeg ,_ffdd =_bccg (_aeeg ,_aeec ,_caed ,1.0,true );}else if _fgc ,_fbc :=_bdag .Function [0].(*_gae .PdfFunctionType3 );
_fbc {_ebef :=append ([]float64 {0},_fgc .Bounds ...);_ebef =append (_ebef ,1.0);_aeeg ,_ffdd =_cddc (_aeeg ,_fgc ,_caed ,_ebef );};if _ffdd !=nil {return nil ,nil ,_ffdd ;};return _aeeg ,_cbeg ,nil ;};func _aeecg (_fgg string ,_bfg _bf .Image ,_fbeg int )error {_cff ,_eaed :=_b .Create (_fgg );
if _eaed !=nil {return _eaed ;};defer _cff .Close ();return _f .Encode (_cff ,_bfg ,&_f .Options {Quality :_fbeg });};func (_dede renderer )renderContentStream (_dba _gd .Context ,_eb string ,_dc *_gae .PdfPageResources )error {_bfb ,_bbe :=_bc .NewContentStreamParser (_eb ).Parse ();
if _bbe !=nil {return _bbe ;};_gee :=_dba .TextState ();_gee .GlobalScale =_dede ._ge ;_cba :=map[string ]*_gd .TextFont {};_cgd :=_ab .NewFinder (&_ab .FinderOpts {Extensions :[]string {"\u002e\u0074\u0074\u0066","\u002e\u0074\u0074\u0063"}});var _ag *_bc .ContentStreamOperation ;
_gcf :=_bc .NewContentStreamProcessor (*_bfb );_gcf .AddHandler (_bc .HandlerConditionEnumAllOperands ,"",func (_ade *_bc .ContentStreamOperation ,_gag _bc .GraphicsState ,_dga *_gae .PdfPageResources )error {_cd .Log .Debug ("\u0050\u0072\u006f\u0063\u0065\u0073\u0073\u0069\u006e\u0067\u0020\u0025\u0073",_ade .Operand );
switch _ade .Operand {case "\u0071":_dba .Push ();case "\u0051":_dba .Pop ();_gee =_dba .TextState ();case "\u0063\u006d":if len (_ade .Params )!=6{return _db ;};_ff ,_ce :=_fa .GetNumbersAsFloat (_ade .Params );if _ce !=nil {return _ce ;};_gdcf :=_ae .NewMatrix (_ff [0],_ff [1],_ff [2],_ff [3],_ff [4],_ff [5]);
_cd .Log .Debug ("\u0047\u0072\u0061\u0070\u0068\u0069\u0063\u0073\u0020\u0073\u0074a\u0074\u0065\u0020\u006d\u0061\u0074\u0072\u0069\u0078\u003a \u0025\u002b\u0076",_gdcf );_dba .SetMatrix (_dba .Matrix ().Mult (_gdcf ));case "\u0077":if len (_ade .Params )!=1{return _db ;
};_aeb ,_gef :=_fa .GetNumbersAsFloat (_ade .Params );if _gef !=nil {return _gef ;};_dba .SetLineWidth (_aeb [0]);case "\u004a":if len (_ade .Params )!=1{return _db ;};_ada ,_bdb :=_fa .GetIntVal (_ade .Params [0]);if !_bdb {return _cbg ;};switch _ada {case 0:_dba .SetLineCap (_gd .LineCapButt );
case 1:_dba .SetLineCap (_gd .LineCapRound );case 2:_dba .SetLineCap (_gd .LineCapSquare );default:_cd .Log .Debug ("\u0049\u006e\u0076\u0061\u006c\u0069\u0064\u0020\u006c\u0069\u006ee\u0020\u0063\u0061\u0070\u0020\u0073\u0074\u0079\u006c\u0065:\u0020\u0025\u0064",_ada );
return _db ;};case "\u006a":if len (_ade .Params )!=1{return _db ;};_fee ,_ecd :=_fa .GetIntVal (_ade .Params [0]);if !_ecd {return _cbg ;};switch _fee {case 0:_dba .SetLineJoin (_gd .LineJoinBevel );case 1:_dba .SetLineJoin (_gd .LineJoinRound );case 2:_dba .SetLineJoin (_gd .LineJoinBevel );
default:_cd .Log .Debug ("I\u006e\u0076\u0061\u006c\u0069\u0064 \u006c\u0069\u006e\u0065\u0020\u006a\u006f\u0069\u006e \u0073\u0074\u0079l\u0065:\u0020\u0025\u0064",_fee );return _db ;};case "\u004d":if len (_ade .Params )!=1{return _db ;};_gea ,_edf :=_fa .GetNumbersAsFloat (_ade .Params );
if _edf !=nil {return _edf ;};_ =_gea ;_cd .Log .Debug ("\u004di\u0074\u0065\u0072\u0020l\u0069\u006d\u0069\u0074\u0020n\u006ft\u0020s\u0075\u0070\u0070\u006f\u0072\u0074\u0065d");case "\u0064":if len (_ade .Params )!=2{return _db ;};_fcdf ,_gce :=_fa .GetArray (_ade .Params [0]);
if !_gce {return _cbg ;};_cbgf ,_gce :=_fa .GetIntVal (_ade .Params [1]);if !_gce {_ ,_fbg :=_fa .GetFloatVal (_ade .Params [1]);if !_fbg {return _cbg ;};};_ecf ,_fec :=_fa .GetNumbersAsFloat (_fcdf .Elements ());if _fec !=nil {return _fec ;};_dba .SetDash (_ecf ...);
_ =_cbgf ;_cd .Log .Debug ("\u004c\u0069n\u0065\u0020\u0064\u0061\u0073\u0068\u0020\u0070\u0068\u0061\u0073\u0065\u0020\u006e\u006f\u0074\u0020\u0073\u0075\u0070\u0070\u006frt\u0065\u0064");case "\u0072\u0069":_cd .Log .Debug ("\u0052\u0065\u006e\u0064\u0065\u0072\u0069\u006e\u0067\u0020i\u006e\u0074\u0065\u006e\u0074\u0020\u006eo\u0074\u0020\u0073\u0075\u0070\u0070\u006f\u0072\u0074\u0065\u0064");
case "\u0069":_cd .Log .Debug ("\u0046\u006c\u0061\u0074\u006e\u0065\u0073\u0073\u0020\u0074\u006f\u006c\u0065\u0072\u0061n\u0063e\u0020\u006e\u006f\u0074\u0020\u0073\u0075\u0070\u0070\u006f\u0072\u0074\u0065\u0064");case "\u0067\u0073":if len (_ade .Params )!=1{return _db ;
};_dec ,_aed :=_fa .GetName (_ade .Params [0]);if !_aed {return _cbg ;};if _dec ==nil {return _db ;};_afg ,_aed :=_dga .GetExtGState (*_dec );if !_aed {_cd .Log .Debug ("\u0045\u0052\u0052OR\u003a\u0020\u0063\u006f\u0075\u006c\u0064\u0020\u006eo\u0074 \u0066i\u006ed\u0020\u0072\u0065\u0073\u006f\u0075\u0072\u0063\u0065\u003a\u0020\u0025\u0073",*_dec );
return _g .New ("\u0072e\u0073o\u0075\u0072\u0063\u0065\u0020n\u006f\u0074 \u0066\u006f\u0075\u006e\u0064");};_gfg ,_aed :=_fa .GetDict (_afg );if !_aed {_cd .Log .Debug ("\u0045\u0052RO\u0052\u003a\u0020c\u006f\u0075\u006c\u0064 ge\u0074 g\u0072\u0061\u0070\u0068\u0069\u0063\u0073 s\u0074\u0061\u0074\u0065\u0020\u0064\u0069c\u0074");
return _cbg ;};_cd .Log .Debug ("G\u0053\u0020\u0064\u0069\u0063\u0074\u003a\u0020\u0025\u0073",_gfg .String ());_egf :=_gfg .Get ("\u0063\u0061");if _egf !=nil {_cab ,_bbb :=_fa .GetNumberAsFloat (_egf );if _bbb ==nil {_dcg ,_ecdb :=_gag .ColorspaceNonStroking .ColorToRGB (_gag .ColorNonStroking );
if _ecdb !=nil {_cd .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u0063\u006f\u006e\u0076\u0065r\u0074\u0069\u006e\u0067\u0020\u0063\u006f\u006c\u006f\u0072:\u0020\u0025\u0076",_ecdb );return _ecdb ;};_edd ,_dbc :=_dcg .(*_gae .PdfColorDeviceRGB );
if !_dbc {_cd .Log .Debug ("\u0045\u0072\u0072\u006fr \u0063\u006f\u006e\u0076\u0065\u0072\u0074\u0069\u006e\u0067\u0020\u0063\u006f\u006co\u0072");return _ecdb ;};_dba .SetFillRGBA (_edd .R (),_edd .G (),_edd .B (),_cab );};};case "\u006d":if len (_ade .Params )!=2{_cd .Log .Debug ("\u0057\u0041\u0052\u004e\u003a\u0020\u0065\u0072\u0072o\u0072\u0020\u0077\u0068\u0069\u006c\u0065\u0020\u0070\u0072\u006f\u0063\u0065\u0073\u0073\u0069\u006e\u0067\u0020\u0060\u006d\u0060\u0020o\u0070\u0065r\u0061\u0074o\u0072\u003a\u0020\u0025\u0073\u002e\u0020\u004f\u0075\u0074\u0070\u0075\u0074 m\u0061\u0079\u0020\u0062\u0065\u0020\u0069\u006e\u0063o\u0072\u0072\u0065\u0063\u0074\u002e",_db );
return nil ;};_dd ,_fd :=_fa .GetNumbersAsFloat (_ade .Params );if _fd !=nil {return _fd ;};_cd .Log .Debug ("M\u006f\u0076\u0065\u0020\u0074\u006f\u003a\u0020\u0025\u0076",_dd );_dba .NewSubPath ();_dba .MoveTo (_dd [0],_dd [1]);case "\u006c":if len (_ade .Params )!=2{_cd .Log .Debug ("\u0057\u0041\u0052\u004e\u003a\u0020\u0065\u0072\u0072o\u0072\u0020\u0077\u0068\u0069\u006c\u0065\u0020\u0070\u0072\u006f\u0063\u0065\u0073\u0073\u0069\u006e\u0067\u0020\u0060\u006c\u0060\u0020o\u0070\u0065r\u0061\u0074o\u0072\u003a\u0020\u0025\u0073\u002e\u0020\u004f\u0075\u0074\u0070\u0075\u0074 m\u0061\u0079\u0020\u0062\u0065\u0020\u0069\u006e\u0063o\u0072\u0072\u0065\u0063\u0074\u002e",_db );
return nil ;};_bgb ,_dbd :=_fa .GetNumbersAsFloat (_ade .Params );if _dbd !=nil {return _dbd ;};_dba .LineTo (_bgb [0],_bgb [1]);case "\u0063":if len (_ade .Params )!=6{return _db ;};_gbg ,_ebe :=_fa .GetNumbersAsFloat (_ade .Params );if _ebe !=nil {return _ebe ;
};_cd .Log .Debug ("\u0043u\u0062\u0069\u0063\u0020\u0062\u0065\u007a\u0069\u0065\u0072\u0020p\u0061\u0072\u0061\u006d\u0073\u003a\u0020\u0025\u002b\u0076",_gbg );_dba .CubicTo (_gbg [0],_gbg [1],_gbg [2],_gbg [3],_gbg [4],_gbg [5]);case "\u0076","\u0079":if len (_ade .Params )!=4{return _db ;
};_ddc ,_dgab :=_fa .GetNumbersAsFloat (_ade .Params );if _dgab !=nil {return _dgab ;};_cd .Log .Debug ("\u0043u\u0062\u0069\u0063\u0020\u0062\u0065\u007a\u0069\u0065\u0072\u0020p\u0061\u0072\u0061\u006d\u0073\u003a\u0020\u0025\u002b\u0076",_ddc );_dba .QuadraticTo (_ddc [0],_ddc [1],_ddc [2],_ddc [3]);
case "\u0068":_dba .ClosePath ();_dba .NewSubPath ();case "\u0072\u0065":if len (_ade .Params )!=4{return _db ;};_dcc ,_cf :=_fa .GetNumbersAsFloat (_ade .Params );if _cf !=nil {return _cf ;};_dba .DrawRectangle (_dcc [0],_dcc [1],_dcc [2],_dcc [3]);_dba .NewSubPath ();
case "\u0053":_ffd ,_abg :=_gag .ColorspaceStroking .ColorToRGB (_gag .ColorStroking );if _abg !=nil {_cd .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u0063\u006f\u006e\u0076\u0065r\u0074\u0069\u006e\u0067\u0020\u0063\u006f\u006c\u006f\u0072:\u0020\u0025\u0076",_abg );
return _abg ;};_cc ,_ac :=_ffd .(*_gae .PdfColorDeviceRGB );if !_ac {_cd .Log .Debug ("\u0045\u0072\u0072\u006fr \u0063\u006f\u006e\u0076\u0065\u0072\u0074\u0069\u006e\u0067\u0020\u0063\u006f\u006co\u0072");return _abg ;};_dba .SetRGBA (_cc .R (),_cc .G (),_cc .B (),1);
_dba .Stroke ();case "\u0073":_afe ,_fcb :=_gag .ColorspaceStroking .ColorToRGB (_gag .ColorStroking );if _fcb !=nil {_cd .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u0063\u006f\u006e\u0076\u0065r\u0074\u0069\u006e\u0067\u0020\u0063\u006f\u006c\u006f\u0072:\u0020\u0025\u0076",_fcb );
return _fcb ;};_fbgf ,_cdg :=_afe .(*_gae .PdfColorDeviceRGB );if !_cdg {_cd .Log .Debug ("\u0045\u0072\u0072\u006fr \u0063\u006f\u006e\u0076\u0065\u0072\u0074\u0069\u006e\u0067\u0020\u0063\u006f\u006co\u0072");return _fcb ;};_dba .ClosePath ();_dba .NewSubPath ();
_dba .SetRGBA (_fbgf .R (),_fbgf .G (),_fbgf .B (),1);_dba .Stroke ();case "\u0066","\u0046":_bgc ,_dgad :=_gag .ColorspaceNonStroking .ColorToRGB (_gag .ColorNonStroking );if _dgad !=nil {_cd .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u0063\u006f\u006e\u0076\u0065r\u0074\u0069\u006e\u0067\u0020\u0063\u006f\u006c\u006f\u0072:\u0020\u0025\u0076",_dgad );
return _dgad ;};switch _fde :=_bgc .(type ){case *_gae .PdfColorDeviceRGB :_dba .SetRGBA (_fde .R (),_fde .G (),_fde .B (),1);_dba .SetFillRule (_gd .FillRuleWinding );_dba .Fill ();case *_gae .PdfColorPattern :_dba .Fill ();};_cd .Log .Debug ("\u0045\u0072\u0072\u006fr \u0063\u006f\u006e\u0076\u0065\u0072\u0074\u0069\u006e\u0067\u0020\u0063\u006f\u006co\u0072");
case "\u0066\u002a":_gde ,_bba :=_gag .ColorspaceNonStroking .ColorToRGB (_gag .ColorNonStroking );if _bba !=nil {_cd .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u0063\u006f\u006e\u0076\u0065r\u0074\u0069\u006e\u0067\u0020\u0063\u006f\u006c\u006f\u0072:\u0020\u0025\u0076",_bba );
return _bba ;};_gbd ,_dag :=_gde .(*_gae .PdfColorDeviceRGB );if !_dag {_cd .Log .Debug ("\u0045\u0072\u0072\u006fr \u0063\u006f\u006e\u0076\u0065\u0072\u0074\u0069\u006e\u0067\u0020\u0063\u006f\u006co\u0072");return _bba ;};_dba .SetRGBA (_gbd .R (),_gbd .G (),_gbd .B (),1);
_dba .SetFillRule (_gd .FillRuleEvenOdd );_dba .Fill ();case "\u0042":_dff ,_bbf :=_gag .ColorspaceNonStroking .ColorToRGB (_gag .ColorNonStroking );if _bbf !=nil {_cd .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u0063\u006f\u006e\u0076\u0065r\u0074\u0069\u006e\u0067\u0020\u0063\u006f\u006c\u006f\u0072:\u0020\u0025\u0076",_bbf );
return _bbf ;};switch _age :=_dff .(type ){case *_gae .PdfColorDeviceRGB :_dba .SetRGBA (_age .R (),_age .G (),_age .B (),1);_dba .SetFillRule (_gd .FillRuleWinding );_dba .FillPreserve ();_dff ,_bbf =_gag .ColorspaceStroking .ColorToRGB (_gag .ColorStroking );
if _bbf !=nil {_cd .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u0063\u006f\u006e\u0076\u0065r\u0074\u0069\u006e\u0067\u0020\u0063\u006f\u006c\u006f\u0072:\u0020\u0025\u0076",_bbf );return _bbf ;};if _ebb ,_ee :=_dff .(*_gae .PdfColorDeviceRGB );
_ee {_dba .SetRGBA (_ebb .R (),_ebb .G (),_ebb .B (),1);_dba .Stroke ();};case *_gae .PdfColorPattern :_dba .SetFillRule (_gd .FillRuleWinding );_dba .Fill ();_dba .StrokePattern ();};case "\u0042\u002a":_gbfb ,_bdd :=_gag .ColorspaceNonStroking .ColorToRGB (_gag .ColorNonStroking );
if _bdd !=nil {_cd .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u0063\u006f\u006e\u0076\u0065r\u0074\u0069\u006e\u0067\u0020\u0063\u006f\u006c\u006f\u0072:\u0020\u0025\u0076",_bdd );return _bdd ;};switch _fdg :=_gbfb .(type ){case *_gae .PdfColorDeviceRGB :_dba .SetRGBA (_fdg .R (),_fdg .G (),_fdg .B (),1);
_dba .SetFillRule (_gd .FillRuleEvenOdd );_dba .FillPreserve ();_gbfb ,_bdd =_gag .ColorspaceStroking .ColorToRGB (_gag .ColorStroking );if _bdd !=nil {_cd .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u0063\u006f\u006e\u0076\u0065r\u0074\u0069\u006e\u0067\u0020\u0063\u006f\u006c\u006f\u0072:\u0020\u0025\u0076",_bdd );
return _bdd ;};if _eab ,_ega :=_gbfb .(*_gae .PdfColorDeviceRGB );_ega {_dba .SetRGBA (_eab .R (),_eab .G (),_eab .B (),1);_dba .Stroke ();};case *_gae .PdfColorPattern :_dba .SetFillRule (_gd .FillRuleEvenOdd );_dba .Fill ();_dba .StrokePattern ();};case "\u0062":_dba .ClosePath ();
_gab ,_ecfa :=_gag .ColorspaceNonStroking .ColorToRGB (_gag .ColorNonStroking );if _ecfa !=nil {_cd .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u0063\u006f\u006e\u0076\u0065r\u0074\u0069\u006e\u0067\u0020\u0063\u006f\u006c\u006f\u0072:\u0020\u0025\u0076",_ecfa );
return _ecfa ;};switch _abd :=_gab .(type ){case *_gae .PdfColorDeviceRGB :_dba .SetRGBA (_abd .R (),_abd .G (),_abd .B (),1);_dba .NewSubPath ();_dba .SetFillRule (_gd .FillRuleWinding );_dba .FillPreserve ();_gab ,_ecfa =_gag .ColorspaceStroking .ColorToRGB (_gag .ColorStroking );
if _ecfa !=nil {_cd .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u0063\u006f\u006e\u0076\u0065r\u0074\u0069\u006e\u0067\u0020\u0063\u006f\u006c\u006f\u0072:\u0020\u0025\u0076",_ecfa );return _ecfa ;};if _cbgg ,_bce :=_gab .(*_gae .PdfColorDeviceRGB );
_bce {_dba .SetRGBA (_cbgg .R (),_cbgg .G (),_cbgg .B (),1);_dba .Stroke ();};case *_gae .PdfColorPattern :_dba .NewSubPath ();_dba .SetFillRule (_gd .FillRuleWinding );_dba .Fill ();_dba .StrokePattern ();};case "\u0062\u002a":_dba .ClosePath ();_egc ,_adee :=_gag .ColorspaceNonStroking .ColorToRGB (_gag .ColorNonStroking );
if _adee !=nil {_cd .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u0063\u006f\u006e\u0076\u0065r\u0074\u0069\u006e\u0067\u0020\u0063\u006f\u006c\u006f\u0072:\u0020\u0025\u0076",_adee );return _adee ;};switch _fba :=_egc .(type ){case *_gae .PdfColorDeviceRGB :_dba .SetRGBA (_fba .R (),_fba .G (),_fba .B (),1);
_dba .NewSubPath ();_dba .SetFillRule (_gd .FillRuleEvenOdd );_dba .FillPreserve ();_egc ,_adee =_gag .ColorspaceStroking .ColorToRGB (_gag .ColorStroking );if _adee !=nil {_cd .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u0063\u006f\u006e\u0076\u0065r\u0074\u0069\u006e\u0067\u0020\u0063\u006f\u006c\u006f\u0072:\u0020\u0025\u0076",_adee );
return _adee ;};if _aea ,_eff :=_egc .(*_gae .PdfColorDeviceRGB );_eff {_dba .SetRGBA (_aea .R (),_aea .G (),_aea .B (),1);_dba .Stroke ();};case *_gae .PdfColorPattern :_dba .NewSubPath ();_dba .SetFillRule (_gd .FillRuleEvenOdd );_dba .Fill ();_dba .StrokePattern ();
};case "\u006e":_dba .ClearPath ();case "\u0057":_dba .SetFillRule (_gd .FillRuleWinding );_dba .ClipPreserve ();case "\u0057\u002a":_dba .SetFillRule (_gd .FillRuleEvenOdd );_dba .ClipPreserve ();case "\u0072\u0067":_dfa ,_fca :=_gag .ColorNonStroking .(*_gae .PdfColorDeviceRGB );
if !_fca {_cd .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u0063\u006f\u006e\u0076\u0065r\u0074\u0069\u006e\u0067\u0020\u0063\u006f\u006c\u006f\u0072:\u0020\u0025\u0076",_gag .ColorNonStroking );return nil ;};_dba .SetFillRGBA (_dfa .R (),_dfa .G (),_dfa .B (),1);
case "\u0052\u0047":_bca ,_gcef :=_gag .ColorStroking .(*_gae .PdfColorDeviceRGB );if !_gcef {_cd .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u0063\u006f\u006e\u0076\u0065r\u0074\u0069\u006e\u0067\u0020\u0063\u006f\u006c\u006f\u0072:\u0020\u0025\u0076",_gag .ColorStroking );
return nil ;};_dba .SetStrokeRGBA (_bca .R (),_bca .G (),_bca .B (),1);case "\u006b":_gfd ,_aeg :=_gag .ColorNonStroking .(*_gae .PdfColorDeviceCMYK );if !_aeg {_cd .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u0063\u006f\u006e\u0076\u0065r\u0074\u0069\u006e\u0067\u0020\u0063\u006f\u006c\u006f\u0072:\u0020\u0025\u0076",_gag .ColorNonStroking );
return nil ;};_acb ,_daa :=_gag .ColorspaceNonStroking .ColorToRGB (_gfd );if _daa !=nil {_cd .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u0063\u006f\u006e\u0076\u0065r\u0074\u0069\u006e\u0067\u0020\u0063\u006f\u006c\u006f\u0072:\u0020\u0025\u0076",_gag .ColorNonStroking );
return nil ;};_agc ,_aeg :=_acb .(*_gae .PdfColorDeviceRGB );if !_aeg {_cd .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u0063\u006f\u006e\u0076\u0065r\u0074\u0069\u006e\u0067\u0020\u0063\u006f\u006c\u006f\u0072:\u0020\u0025\u0076",_acb );return nil ;
};_dba .SetFillRGBA (_agc .R (),_agc .G (),_agc .B (),1);case "\u004b":_aac ,_bcaf :=_gag .ColorStroking .(*_gae .PdfColorDeviceCMYK );if !_bcaf {_cd .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u0063\u006f\u006e\u0076\u0065r\u0074\u0069\u006e\u0067\u0020\u0063\u006f\u006c\u006f\u0072:\u0020\u0025\u0076",_gag .ColorStroking );
return nil ;};_aacf ,_cdf :=_gag .ColorspaceStroking .ColorToRGB (_aac );if _cdf !=nil {_cd .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u0063\u006f\u006e\u0076\u0065r\u0074\u0069\u006e\u0067\u0020\u0063\u006f\u006c\u006f\u0072:\u0020\u0025\u0076",_gag .ColorStroking );
return nil ;};_bff ,_bcaf :=_aacf .(*_gae .PdfColorDeviceRGB );if !_bcaf {_cd .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u0063\u006f\u006e\u0076\u0065r\u0074\u0069\u006e\u0067\u0020\u0063\u006f\u006c\u006f\u0072:\u0020\u0025\u0076",_aacf );return nil ;
};_dba .SetStrokeRGBA (_bff .R (),_bff .G (),_bff .B (),1);case "\u0067":_daf ,_gac :=_gag .ColorNonStroking .(*_gae .PdfColorDeviceGray );if !_gac {_cd .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u0063\u006f\u006e\u0076\u0065r\u0074\u0069\u006e\u0067\u0020\u0063\u006f\u006c\u006f\u0072:\u0020\u0025\u0076",_gag .ColorNonStroking );
return nil ;};_eec ,_caf :=_gag .ColorspaceNonStroking .ColorToRGB (_daf );if _caf !=nil {_cd .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u0063\u006f\u006e\u0076\u0065r\u0074\u0069\u006e\u0067\u0020\u0063\u006f\u006c\u006f\u0072:\u0020\u0025\u0076",_gag .ColorNonStroking );
return nil ;};_egb ,_gac :=_eec .(*_gae .PdfColorDeviceRGB );if !_gac {_cd .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u0063\u006f\u006e\u0076\u0065r\u0074\u0069\u006e\u0067\u0020\u0063\u006f\u006c\u006f\u0072:\u0020\u0025\u0076",_eec );return nil ;
};_dba .SetFillRGBA (_egb .R (),_egb .G (),_egb .B (),1);case "\u0047":_acf ,_faga :=_gag .ColorStroking .(*_gae .PdfColorDeviceGray );if !_faga {_cd .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u0063\u006f\u006e\u0076\u0065r\u0074\u0069\u006e\u0067\u0020\u0063\u006f\u006c\u006f\u0072:\u0020\u0025\u0076",_gag .ColorStroking );
return nil ;};_fg ,_bcbe :=_gag .ColorspaceStroking .ColorToRGB (_acf );if _bcbe !=nil {_cd .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u0063\u006f\u006e\u0076\u0065r\u0074\u0069\u006e\u0067\u0020\u0063\u006f\u006c\u006f\u0072:\u0020\u0025\u0076",_gag .ColorStroking );
return nil ;};_cfa ,_faga :=_fg .(*_gae .PdfColorDeviceRGB );if !_faga {_cd .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u0063\u006f\u006e\u0076\u0065r\u0074\u0069\u006e\u0067\u0020\u0063\u006f\u006c\u006f\u0072:\u0020\u0025\u0076",_fg );return nil ;
};_dba .SetStrokeRGBA (_cfa .R (),_cfa .G (),_cfa .B (),1);case "\u0063\u0073":if len (_ade .Params )> 0{if _gbb ,_cgb :=_fa .GetName (_ade .Params [0]);_cgb &&_gbb .String ()=="\u0050a\u0074\u0074\u0065\u0072\u006e"{break ;};};_aga ,_cdb :=_gag .ColorspaceNonStroking .ColorToRGB (_gag .ColorNonStroking );
if _cdb !=nil {_cd .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u0063\u006f\u006e\u0076\u0065r\u0074\u0069\u006e\u0067\u0020\u0063\u006f\u006c\u006f\u0072:\u0020\u0025\u0076",_gag .ColorNonStroking );return nil ;};_eeca ,_aefg :=_aga .(*_gae .PdfColorDeviceRGB );
if !_aefg {_cd .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u0063\u006f\u006e\u0076\u0065r\u0074\u0069\u006e\u0067\u0020\u0063\u006f\u006c\u006f\u0072:\u0020\u0025\u0076",_aga );return nil ;};_dba .SetFillRGBA (_eeca .R (),_eeca .G (),_eeca .B (),1);
case "\u0073\u0063":_fbe ,_dda :=_gag .ColorspaceNonStroking .ColorToRGB (_gag .ColorNonStroking );if _dda !=nil {_cd .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u0063\u006f\u006e\u0076\u0065r\u0074\u0069\u006e\u0067\u0020\u0063\u006f\u006c\u006f\u0072:\u0020\u0025\u0076",_gag .ColorNonStroking );
return nil ;};_gca ,_gba :=_fbe .(*_gae .PdfColorDeviceRGB );if !_gba {_cd .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u0063\u006f\u006e\u0076\u0065r\u0074\u0069\u006e\u0067\u0020\u0063\u006f\u006c\u006f\u0072:\u0020\u0025\u0076",_fbe );return nil ;
};_dba .SetFillRGBA (_gca .R (),_gca .G (),_gca .B (),1);case "\u0073\u0063\u006e":if len (_ade .Params )> 0&&len (_ag .Params )> 0{if _aaf ,_gdb :=_fa .GetName (_ag .Params [0]);_gdb &&_aaf .String ()=="\u0050a\u0074\u0074\u0065\u0072\u006e"{if _egaf ,_bfc :=_fa .GetName (_ade .Params [0]);
_bfc {_dca ,_bfa :=_dede .processGradient (_dba ,_ade ,_dga ,_egaf );if _bfa !=nil {_cd .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u0077\u0068\u0065\u006e\u0020\u0070\u0072o\u0063\u0065\u0073\u0073\u0069\u006eg\u0020\u0067\u0072\u0061\u0064\u0069\u0065\u006e\u0074\u0020\u0064\u0061\u0074a\u003a\u0020\u0025\u0076",_bfa );
break ;};if _dca ==nil {_cd .Log .Debug ("\u0055\u006ek\u006e\u006f\u0077n\u0020\u0067\u0072\u0061\u0064\u0069\u0065\u006e\u0074");break ;};_dba .SetFillStyle (_dca );_dba .SetStrokeStyle (_dca );break ;};};};_ecg ,_gge :=_gag .ColorspaceNonStroking .ColorToRGB (_gag .ColorNonStroking );
if _gge !=nil {_cd .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u0063\u006f\u006e\u0076\u0065r\u0074\u0069\u006e\u0067\u0020\u0063\u006f\u006c\u006f\u0072:\u0020\u0025\u0076",_gag .ColorNonStroking );return nil ;};_cea ,_fecg :=_ecg .(*_gae .PdfColorDeviceRGB );
if !_fecg {_cd .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u0063\u006f\u006e\u0076\u0065r\u0074\u0069\u006e\u0067\u0020\u0063\u006f\u006c\u006f\u0072:\u0020\u0025\u0076",_ecg );return nil ;};_dba .SetFillRGBA (_cea .R (),_cea .G (),_cea .B (),1);
case "\u0043\u0053":if len (_ade .Params )> 0{if _fcea ,_cae :=_fa .GetName (_ade .Params [0]);_cae &&_fcea .String ()=="\u0050a\u0074\u0074\u0065\u0072\u006e"{break ;};};_abgf ,_bfbd :=_gag .ColorspaceStroking .ColorToRGB (_gag .ColorStroking );if _bfbd !=nil {_cd .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u0063\u006f\u006e\u0076\u0065r\u0074\u0069\u006e\u0067\u0020\u0063\u006f\u006c\u006f\u0072:\u0020\u0025\u0076",_gag .ColorStroking );
return nil ;};_dcae ,_gcb :=_abgf .(*_gae .PdfColorDeviceRGB );if !_gcb {_cd .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u0063\u006f\u006e\u0076\u0065r\u0074\u0069\u006e\u0067\u0020\u0063\u006f\u006c\u006f\u0072:\u0020\u0025\u0076",_abgf );return nil ;
};_dba .SetStrokeRGBA (_dcae .R (),_dcae .G (),_dcae .B (),1);case "\u0053\u0043":_ccg ,_fed :=_gag .ColorspaceStroking .ColorToRGB (_gag .ColorStroking );if _fed !=nil {_cd .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u0063\u006f\u006e\u0076\u0065r\u0074\u0069\u006e\u0067\u0020\u0063\u006f\u006c\u006f\u0072:\u0020\u0025\u0076",_gag .ColorStroking );
return nil ;};_ceg ,_fdb :=_ccg .(*_gae .PdfColorDeviceRGB );if !_fdb {_cd .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u0063\u006f\u006e\u0076\u0065r\u0074\u0069\u006e\u0067\u0020\u0063\u006f\u006c\u006f\u0072:\u0020\u0025\u0076",_ccg );return nil ;
};_dba .SetStrokeRGBA (_ceg .R (),_ceg .G (),_ceg .B (),1);case "\u0053\u0043\u004e":if len (_ade .Params )> 0&&len (_ag .Params )> 0{if _ecfg ,_fgd :=_fa .GetName (_ag .Params [0]);_fgd &&_ecfg .String ()=="\u0050a\u0074\u0074\u0065\u0072\u006e"{if _ddcb ,_gfa :=_fa .GetName (_ade .Params [0]);
_gfa {_efdd ,_eaf :=_dede .processGradient (_dba ,_ade ,_dga ,_ddcb );if _eaf !=nil {_cd .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u0077\u0068\u0065\u006e\u0020\u0070\u0072o\u0063\u0065\u0073\u0073\u0069\u006eg\u0020\u0067\u0072\u0061\u0064\u0069\u0065\u006e\u0074\u0020\u0064\u0061\u0074a\u003a\u0020\u0025\u0076",_eaf );
break ;};if _efdd ==nil {_cd .Log .Debug ("\u0055\u006ek\u006e\u006f\u0077n\u0020\u0067\u0072\u0061\u0064\u0069\u0065\u006e\u0074");break ;};_dba .SetFillStyle (_efdd );_dba .SetStrokeStyle (_efdd );break ;};};};_cdd ,_fge :=_gag .ColorspaceStroking .ColorToRGB (_gag .ColorStroking );
if _fge !=nil {_cd .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u0063\u006f\u006e\u0076\u0065r\u0074\u0069\u006e\u0067\u0020\u0063\u006f\u006c\u006f\u0072:\u0020\u0025\u0076",_gag .ColorStroking );return nil ;};_fac ,_gdf :=_cdd .(*_gae .PdfColorDeviceRGB );
if !_gdf {_cd .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u0063\u006f\u006e\u0076\u0065r\u0074\u0069\u006e\u0067\u0020\u0063\u006f\u006c\u006f\u0072:\u0020\u0025\u0076",_cdd );return nil ;};_dba .SetStrokeRGBA (_fac .R (),_fac .G (),_fac .B (),1);
case "\u0073\u0068":if len (_ade .Params )!=1{_cd .Log .Debug ("\u0049n\u0076\u0061\u006c\u0069\u0064\u0020\u0073\u0068\u0020\u0070\u0061r\u0061\u006d\u0073\u0020\u0066\u006f\u0072\u006d\u0061\u0074");break ;};_feee ,_aefga :=_fa .GetName (_ade .Params [0]);
if !_aefga {_cd .Log .Debug ("F\u0061\u0069\u006c\u0065\u0064\u0020g\u0065\u0074\u0074\u0069\u006e\u0067\u0020\u0073\u0068a\u0064\u0069\u006eg\u0020n\u0061\u006d\u0065");break ;};_eeg ,_aefga :=_dga .GetShadingByName (*_feee );if !_aefga {_cd .Log .Debug ("F\u0061\u0069\u006c\u0065\u0064\u0020g\u0065\u0074\u0074\u0069\u006e\u0067\u0020\u0073\u0068a\u0064\u0069\u006eg\u0020d\u0061\u0074\u0061");
break ;};_fdc ,_ebf ,_cddf :=_dede .processShading (_dba ,_eeg );if _cddf !=nil {_cd .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u0077\u0068\u0065\u006e\u0020\u0070\u0072\u006f\u0063\u0065\u0073\u0073\u0069\u006e\u0067\u0020\u0073\u0068a\u0064\u0069\u006e\u0067\u0020d\u0061\u0074a\u003a\u0020\u0025\u0076",_cddf );
break ;};if _fdc ==nil {_cd .Log .Debug ("\u0055\u006ek\u006e\u006f\u0077n\u0020\u0067\u0072\u0061\u0064\u0069\u0065\u006e\u0074");break ;};_gdca ,_cddf :=_ebf .ToFloat64Array ();if _cddf !=nil {_cd .Log .Debug ("\u0045\u0072r\u006f\u0072\u0020\u0070\u0061\u0072\u0073\u0069\u006e\u0067\u0020\u0063\u006f\u006f\u0072\u0064\u0069\u006e\u0061\u0074\u0065\u0073: \u0025\u0076",_cddf );
break ;};_dba .DrawRectangle (_gdca [0],_gdca [1],_gdca [2],_gdca [3]);_dba .NewSubPath ();_dba .SetFillStyle (_fdc );_dba .SetStrokeStyle (_fdc );_dba .Fill ();case "\u0044\u006f":if len (_ade .Params )!=1{return _db ;};_gfca ,_gad :=_fa .GetName (_ade .Params [0]);
if !_gad {return _cbg ;};_ ,_fgf :=_dga .GetXObjectByName (*_gfca );switch _fgf {case _gae .XObjectTypeImage :_cd .Log .Debug ("\u0058\u004f\u0062\u006a\u0065\u0063\u0074\u0020\u0069\u006d\u0061\u0067e\u003a\u0020\u0025\u0073",_gfca .String ());_gga ,_adf :=_dga .GetXObjectImageByName (*_gfca );
if _adf !=nil {return _adf ;};_fcbe ,_adf :=_gga .ToImage ();if _adf !=nil {_cd .Log .Debug ("\u0052\u0065\u006e\u0064\u0065\u0072\u0069\u006e\u0067\u0020\u0072\u0065\u0073\u0075\u006c\u0074\u0020\u006day\u0020b\u0065\u0020\u0069\u006e\u0063\u006f\u006d\u0070\u006c\u0065\u0074\u0065.\u0020\u0049\u006d\u0061\u0067\u0065\u0020\u0063\u006f\u006e\u0076\u0065\u0072\u0073\u0069\u006f\u006e \u0065\u0072\u0072\u006f\u0072\u003a\u0020\u0025\u0076",_adf );
return nil ;};if _abgfc :=_gga .ColorSpace ;_abgfc !=nil {var _egca bool ;switch _abgfc .(type ){case *_gae .PdfColorspaceSpecialIndexed :_egca =true ;};if _egca {if _ced ,_gaga :=_abgfc .ImageToRGB (*_fcbe );_gaga !=nil {_cd .Log .Debug ("\u0057\u0041\u0052\u004e\u003a\u0020\u0063\u006f\u0075\u006c\u0064\u0020\u006e\u006f\u0074\u0020\u0063\u006fnv\u0065r\u0074\u0020\u0069\u006d\u0061\u0067\u0065\u0020\u0074\u006f\u0020\u0052G\u0042\u002e\u0020\u004f\u0075\u0074\u0070\u0075\u0074\u0020\u006d\u0061\u0079\u0020\u0062\u0065\u0020i\u006e\u0063\u006f\u0072\u0072\u0065\u0063\u0074\u002e");
}else {_fcbe =&_ced ;};};};_beb :=_dba .FillPattern ().ColorAt (0,0);var _eaab _bf .Image ;if _gga .Mask !=nil {if _eaab ,_adf =_ffa (_gga .Mask ,_beb );_adf !=nil {_cd .Log .Debug ("\u0057\u0041\u0052\u004e\u003a \u0063\u006f\u0075\u006c\u0064 \u006eo\u0074\u0020\u0067\u0065\u0074\u0020\u0065\u0078\u0070\u006c\u0069\u0063\u0069\u0074\u0020\u0069\u006d\u0061\u0067e\u0020\u006d\u0061\u0073\u006b\u002e\u0020\u004f\u0075\u0074\u0070\u0075\u0074\u0020\u006d\u0061\u0079\u0020\u0062\u0065\u0020\u0069\u006e\u0063o\u0072\u0072\u0065\u0063\u0074\u002e");
};}else if _gga .SMask !=nil {if _eaab ,_adf =_cfag (_gga .SMask ,_beb );_adf !=nil {_cd .Log .Debug ("W\u0041\u0052\u004e\u003a\u0020\u0063\u006f\u0075\u006c\u0064\u0020\u006e\u006f\u0074\u0020\u0067\u0065\u0074\u0020\u0073\u006f\u0066\u0074\u0020\u0069\u006da\u0067e\u0020\u006d\u0061\u0073k\u002e\u0020O\u0075\u0074\u0070\u0075\u0074\u0020\u006d\u0061\u0079\u0020\u0062\u0065\u0020\u0069\u006e\u0063\u006f\u0072\u0072\u0065\u0063\u0074\u002e");
};};var _fda _bf .Image ;if _ecc ,_ :=_fa .GetBoolVal (_gga .ImageMask );_ecc {_fda =_aebf (_fcbe ,_beb );}else {_fda ,_adf =_fcbe .ToGoImage ();if _adf !=nil {_cd .Log .Debug ("\u0052\u0065\u006e\u0064\u0065\u0072\u0069\u006e\u0067\u0020\u0072\u0065\u0073\u0075\u006c\u0074\u0020\u006day\u0020b\u0065\u0020\u0069\u006e\u0063\u006f\u006d\u0070\u006c\u0065\u0074\u0065.\u0020\u0049\u006d\u0061\u0067\u0065\u0020\u0063\u006f\u006e\u0076\u0065\u0072\u0073\u0069\u006f\u006e \u0065\u0072\u0072\u006f\u0072\u003a\u0020\u0025\u0076",_adf );
return nil ;};};if _eaab !=nil {_fda =_eaae (_fda ,_eaab );};_fcg :=_fda .Bounds ();_dba .Push ();_dba .Scale (1.0/float64 (_fcg .Dx ()),-1.0/float64 (_fcg .Dy ()));_dba .DrawImageAnchored (_fda ,0,0,0,1);_dba .Pop ();case _gae .XObjectTypeForm :_cd .Log .Debug ("\u0058\u004fb\u006a\u0065\u0063t\u0020\u0066\u006f\u0072\u006d\u003a\u0020\u0025\u0073",_gfca .String ());
_cbd ,_bac :=_dga .GetXObjectFormByName (*_gfca );if _bac !=nil {return _bac ;};_ede ,_bac :=_cbd .GetContentStream ();if _bac !=nil {return _bac ;};_gfge :=_cbd .Resources ;if _gfge ==nil {_gfge =_dga ;};_dba .Push ();if _cbd .Matrix !=nil {_ffb ,_bfd :=_fa .GetArray (_cbd .Matrix );
if !_bfd {return _cbg ;};_cgc ,_afed :=_fa .GetNumbersAsFloat (_ffb .Elements ());if _afed !=nil {return _afed ;};if len (_cgc )!=6{return _db ;};_dedd :=_ae .NewMatrix (_cgc [0],_cgc [1],_cgc [2],_cgc [3],_cgc [4],_cgc [5]);_dba .SetMatrix (_dba .Matrix ().Mult (_dedd ));
};if _cbd .BBox !=nil {_aag ,_bfbe :=_fa .GetArray (_cbd .BBox );if !_bfbe {return _cbg ;};_cce ,_gcae :=_fa .GetNumbersAsFloat (_aag .Elements ());if _gcae !=nil {return _gcae ;};if len (_cce )!=4{_cd .Log .Debug ("\u004c\u0065\u006e\u0020\u003d\u0020\u0025\u0064",len (_cce ));
return _db ;};_dba .DrawRectangle (_cce [0],_cce [1],_cce [2]-_cce [0],_cce [3]-_cce [1]);_dba .SetRGBA (1,0,0,1);_dba .Clip ();}else {_cd .Log .Debug ("\u0045R\u0052\u004fR\u003a\u0020\u0052\u0065q\u0075\u0069\u0072e\u0064\u0020\u0042\u0042\u006f\u0078\u0020\u006d\u0069ss\u0069\u006e\u0067 \u006f\u006e \u0058\u004f\u0062\u006a\u0065\u0063t\u0020\u0046o\u0072\u006d");
};_bac =_dede .renderContentStream (_dba ,string (_ede ),_gfge );if _bac !=nil {return _bac ;};_dba .Pop ();};case "\u0042\u0049":if len (_ade .Params )!=1{return _db ;};_bcc ,_daga :=_ade .Params [0].(*_bc .ContentStreamInlineImage );if !_daga {return nil ;
};_gdff ,_feb :=_bcc .ToImage (_dga );if _feb !=nil {_cd .Log .Debug ("\u0052\u0065\u006e\u0064\u0065\u0072\u0069\u006e\u0067\u0020\u0072\u0065\u0073\u0075\u006c\u0074\u0020\u006day\u0020b\u0065\u0020\u0069\u006e\u0063\u006f\u006d\u0070\u006c\u0065\u0074\u0065.\u0020\u0049\u006d\u0061\u0067\u0065\u0020\u0063\u006f\u006e\u0076\u0065\u0072\u0073\u0069\u006f\u006e \u0065\u0072\u0072\u006f\u0072\u003a\u0020\u0025\u0076",_feb );
return nil ;};_dde ,_feb :=_gdff .ToGoImage ();if _feb !=nil {_cd .Log .Debug ("\u0052\u0065\u006e\u0064\u0065\u0072\u0069\u006e\u0067\u0020\u0072\u0065\u0073\u0075\u006c\u0074\u0020\u006day\u0020b\u0065\u0020\u0069\u006e\u0063\u006f\u006d\u0070\u006c\u0065\u0074\u0065.\u0020\u0049\u006d\u0061\u0067\u0065\u0020\u0063\u006f\u006e\u0076\u0065\u0072\u0073\u0069\u006f\u006e \u0065\u0072\u0072\u006f\u0072\u003a\u0020\u0025\u0076",_feb );
return nil ;};_gfdb :=_dde .Bounds ();_dba .Push ();_dba .Scale (1.0/float64 (_gfdb .Dx ()),-1.0/float64 (_gfdb .Dy ()));_dba .DrawImageAnchored (_dde ,0,0,0,1);_dba .Pop ();case "\u0042\u0054":_gee .Reset ();case "\u0045\u0054":_gee .Reset ();case "\u0054\u0072":if len (_ade .Params )!=1{return _db ;
};_bde ,_bad :=_fa .GetNumberAsFloat (_ade .Params [0]);if _bad !=nil {return _bad ;};_gee .Tr =_gd .TextRenderingMode (_bde );case "\u0054\u004c":if len (_ade .Params )!=1{return _db ;};_bgcd ,_ebc :=_fa .GetNumberAsFloat (_ade .Params [0]);if _ebc !=nil {return _ebc ;
};_gee .Tl =_bgcd ;case "\u0054\u0063":if len (_ade .Params )!=1{return _db ;};_gcc ,_efa :=_fa .GetNumberAsFloat (_ade .Params [0]);if _efa !=nil {return _efa ;};_cd .Log .Debug ("\u0054\u0063\u003a\u0020\u0025\u0076",_gcc );_gee .Tc =_gcc ;case "\u0054\u0077":if len (_ade .Params )!=1{return _db ;
};_agg ,_ege :=_fa .GetNumberAsFloat (_ade .Params [0]);if _ege !=nil {return _ege ;};_cd .Log .Debug ("\u0054\u0077\u003a\u0020\u0025\u0076",_agg );_gee .Tw =_agg ;case "\u0054\u007a":if len (_ade .Params )!=1{return _db ;};_cbe ,_fbed :=_fa .GetNumberAsFloat (_ade .Params [0]);
if _fbed !=nil {return _fbed ;};_gee .Th =_cbe ;case "\u0054\u0073":if len (_ade .Params )!=1{return _db ;};_dgc ,_eegc :=_fa .GetNumberAsFloat (_ade .Params [0]);if _eegc !=nil {return _eegc ;};_gee .Ts =_dgc ;case "\u0054\u0064":if len (_ade .Params )!=2{return _db ;
};_gec ,_eed :=_fa .GetNumbersAsFloat (_ade .Params );if _eed !=nil {return _eed ;};_cd .Log .Debug ("\u0054\u0064\u003a\u0020\u0025\u0076",_gec );_gee .ProcTd (_gec [0],_gec [1]);case "\u0054\u0044":if len (_ade .Params )!=2{return _db ;};_ece ,_afd :=_fa .GetNumbersAsFloat (_ade .Params );
if _afd !=nil {return _afd ;};_cd .Log .Debug ("\u0054\u0044\u003a\u0020\u0025\u0076",_ece );_gee .ProcTD (_ece [0],_ece [1]);case "\u0054\u002a":_gee .ProcTStar ();case "\u0054\u006d":if len (_ade .Params )!=6{return _db ;};_cgca ,_bcbc :=_fa .GetNumbersAsFloat (_ade .Params );
if _bcbc !=nil {return _bcbc ;};_cd .Log .Debug ("\u0054\u0065x\u0074\u0020\u006da\u0074\u0072\u0069\u0078\u003a\u0020\u0025\u002b\u0076",_cgca );_gee .ProcTm (_cgca [0],_cgca [1],_cgca [2],_cgca [3],_cgca [4],_cgca [5]);case "\u0027":if len (_ade .Params )!=1{return _db ;
};_afa ,_daeb :=_fa .GetStringBytes (_ade .Params [0]);if !_daeb {return _cbg ;};_cd .Log .Debug ("\u0027\u0020\u0073t\u0072\u0069\u006e\u0067\u003a\u0020\u0025\u0073",string (_afa ));_gee .ProcQ (_afa ,_dba );case "\u0022":if len (_ade .Params )!=3{return _db ;
};_dfc ,_bbbf :=_fa .GetNumberAsFloat (_ade .Params [0]);if _bbbf !=nil {return _bbbf ;};_gdee ,_bbbf :=_fa .GetNumberAsFloat (_ade .Params [1]);if _bbbf !=nil {return _bbbf ;};_bfed ,_aab :=_fa .GetStringBytes (_ade .Params [2]);if !_aab {return _cbg ;
};_gee .ProcDQ (_bfed ,_dfc ,_gdee ,_dba );case "\u0054\u006a":if len (_ade .Params )!=1{return _db ;};_gfgc ,_agd :=_fa .GetStringBytes (_ade .Params [0]);if !_agd {return _cbg ;};_cd .Log .Debug ("\u0054j\u0020s\u0074\u0072\u0069\u006e\u0067\u003a\u0020\u0060\u0025\u0073\u0060",string (_gfgc ));
_gee .ProcTj (_gfgc ,_dba );case "\u0054\u004a":if len (_ade .Params )!=1{return _db ;};_gcec ,_abc :=_fa .GetArray (_ade .Params [0]);if !_abc {_cd .Log .Debug ("\u0054\u0079\u0070\u0065\u003a\u0020\u0025\u0054",_gcec );return _cbg ;};_cd .Log .Debug ("\u0054\u004a\u0020\u0061\u0072\u0072\u0061\u0079\u003a\u0020\u0025\u002b\u0076",_gcec );
for _ ,_aecg :=range _gcec .Elements (){switch _aead :=_aecg .(type ){case *_fa .PdfObjectString :if _aead !=nil {_gee .ProcTj (_aead .Bytes (),_dba );};case *_fa .PdfObjectFloat ,*_fa .PdfObjectInteger :_cgce ,_gcac :=_fa .GetNumberAsFloat (_aead );if _gcac ==nil {_gee .Translate (-_cgce *0.001*_gee .Tf .Size *_gee .Th /100.0,0);
};};};case "\u0054\u0066":if len (_ade .Params )!=2{return _db ;};_cd .Log .Debug ("\u0025\u0023\u0076",_ade .Params );_baf ,_fgb :=_fa .GetName (_ade .Params [0]);if !_fgb ||_baf ==nil {_cd .Log .Debug ("\u0069\u006e\u0076\u0061l\u0069\u0064\u0020\u0066\u006f\u006e\u0074\u0020\u006e\u0061m\u0065 \u006f\u0062\u006a\u0065\u0063\u0074\u003a \u0025\u0076",_ade .Params [0]);
return _cbg ;};_cd .Log .Debug ("\u0046\u006f\u006e\u0074\u0020\u006e\u0061\u006d\u0065\u003a\u0020\u0025\u0073",_baf .String ());_bec ,_egcc :=_fa .GetNumberAsFloat (_ade .Params [1]);if _egcc !=nil {_cd .Log .Debug ("\u0069\u006e\u0076\u0061l\u0069\u0064\u0020\u0066\u006f\u006e\u0074\u0020\u0073\u0069z\u0065 \u006f\u0062\u006a\u0065\u0063\u0074\u003a \u0025\u0076",_ade .Params [1]);
return _cbg ;};_cd .Log .Debug ("\u0046\u006f\u006e\u0074\u0020\u0073\u0069\u007a\u0065\u003a\u0020\u0025\u0076",_bec );_cabg ,_gff :=_dga .GetFontByName (*_baf );if !_gff {_cd .Log .Debug ("\u0045R\u0052\u004f\u0052\u003a\u0020\u0046\u006f\u006e\u0074\u0020\u0025s\u0020\u006e\u006f\u0074\u0020\u0066\u006f\u0075\u006e\u0064",_baf .String ());
return _g .New ("\u0066\u006f\u006e\u0074\u0020\u006e\u006f\u0074\u0020f\u006f\u0075\u006e\u0064");};_cd .Log .Debug ("\u0046\u006f\u006e\u0074\u003a\u0020\u0025\u0054",_cabg );_gcg ,_fgb :=_fa .GetDict (_cabg );if !_fgb {_cd .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u0063\u006f\u0075l\u0064\u0020\u006e\u006f\u0074\u0020\u0067e\u0074\u0020\u0066\u006f\u006e\u0074\u0020\u0064\u0069\u0063\u0074");
return _cbg ;};_gbbb ,_egcc :=_gae .NewPdfFontFromPdfObject (_gcg );if _egcc !=nil {_cd .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u0063\u006f\u0075\u006c\u0064\u0020\u006e\u006f\u0074\u0020\u006c\u006f\u0061\u0064\u0020\u0066\u006fn\u0074\u0020\u0066\u0072\u006fm\u0020\u006fb\u006a\u0065\u0063\u0074");
return _egcc ;};_gda :=_gbbb .BaseFont ();if _gda ==""{_gda =_baf .String ();};_dcge ,_fgb :=_cba [_gda ];if !_fgb {_dcge ,_egcc =_gd .NewTextFont (_gbbb ,_bec );if _egcc !=nil {_cd .Log .Debug ("\u0045R\u0052\u004f\u0052\u003a\u0020\u0025v",_egcc );};
};if _dcge ==nil {if len (_gda )> 7&&_gda [6]=='+'{_gda =_gda [7:];};_becb :=[]string {_gda ,"\u0054i\u006de\u0073\u0020\u004e\u0065\u0077\u0020\u0052\u006f\u006d\u0061\u006e","\u0041\u0072\u0069a\u006c","D\u0065\u006a\u0061\u0056\u0075\u0020\u0053\u0061\u006e\u0073"};
for _ ,_ddca :=range _becb {_cd .Log .Debug ("\u0044\u0045\u0042\u0055\u0047\u003a \u0073\u0065\u0061\u0072\u0063\u0068\u0069\u006e\u0067\u0020\u0073\u0079\u0073t\u0065\u006d\u0020\u0066\u006f\u006e\u0074 \u0060\u0025\u0073\u0060",_ddca );if _dcge ,_fgb =_cba [_ddca ];
_fgb {break ;};_agag :=_cgd .Match (_ddca );if _agag ==nil {_cd .Log .Debug ("c\u006f\u0075\u006c\u0064\u0020\u006eo\u0074\u0020\u0066\u0069\u006e\u0064\u0020\u0066\u006fn\u0074\u0020\u0066i\u006ce\u0020\u0025\u0073",_ddca );continue ;};_dcge ,_egcc =_gd .NewTextFontFromPath (_agag .Filename ,_bec );
if _egcc !=nil {_cd .Log .Debug ("c\u006f\u0075\u006c\u0064\u0020\u006eo\u0074\u0020\u006c\u006f\u0061\u0064\u0020\u0066\u006fn\u0074\u0020\u0066i\u006ce\u0020\u0025\u0073",_agag .Filename );continue ;};_cd .Log .Debug ("\u0053\u0075\u0062\u0073\u0074\u0069t\u0075\u0074\u0069\u006e\u0067\u0020\u0066\u006f\u006e\u0074\u0020\u0025\u0073 \u0077\u0069\u0074\u0068\u0020\u0025\u0073 \u0028\u0025\u0073\u0029",_gda ,_agag .Name ,_agag .Filename );
_cba [_ddca ]=_dcge ;break ;};};if _dcge ==nil {_cd .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u0063\u006f\u0075\u006c\u0064\u0020n\u006f\u0074\u0020\u0066\u0069\u006ed\u0020\u0061\u006e\u0079\u0020\u0073\u0075\u0069\u0074\u0061\u0062\u006c\u0065 \u0066\u006f\u006e\u0074");
return _g .New ("\u0063\u006f\u0075\u006c\u0064\u0020\u006e\u006f\u0074\u0020\u0066\u0069\u006e\u0064\u0020a\u006ey\u0020\u0073\u0075\u0069\u0074\u0061\u0062\u006c\u0065\u0020\u0066\u006f\u006e\u0074");};_gee .ProcTf (_dcge .WithSize (_bec ,_gbbb ));case "\u0042\u004d\u0043","\u0042\u0044\u0043":case "\u0045\u004d\u0043":default:_cd .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u0075\u006e\u0073u\u0070\u0070\u006f\u0072\u0074\u0065\u0064 \u006f\u0070\u0065\u0072\u0061\u006e\u0064\u003a\u0020\u0025\u0073",_ade .Operand );
};_ag =_ade ;return nil ;});_bbe =_gcf .Process (_dc );if _bbe !=nil {return _bbe ;};return nil ;};

// ImageDevice is used to render PDF pages to image targets.
type ImageDevice struct{renderer ;

// OutputWidth represents the width of the rendered images in pixels.
// The heights of the output images are calculated based on the selected
// width and the original height of each rendered page.
OutputWidth int ;};func (_fce renderer )renderPage (_fcd _gd .Context ,_fbdg *_gae .PdfPage ,_aef _ae .Matrix ,_gfc bool )error {if !_gfc {_ea :=_gae .FieldFlattenOpts {AnnotFilterFunc :func (_fag *_gae .PdfAnnotation )bool {switch _fag .GetContext ().(type ){case *_gae .PdfAnnotationLine :return true ;
case *_gae .PdfAnnotationSquare :return true ;case *_gae .PdfAnnotationCircle :return true ;case *_gae .PdfAnnotationPolygon :return true ;case *_gae .PdfAnnotationPolyLine :return true ;};return false ;}};_eg :=_df .FieldAppearance {};_efd :=_fbdg .FlattenFieldsWithOpts (_eg ,&_ea );
if _efd !=nil {_cd .Log .Debug ("\u0045\u0072r\u006f\u0072\u0020\u0064u\u0072\u0069n\u0067\u0020\u0061\u006e\u006e\u006f\u0074\u0061t\u0069\u006f\u006e\u0020\u0066\u006c\u0061\u0074\u0074\u0065\u006e\u0069n\u0067\u0020\u0025\u0076",_efd );};};_eaa ,_fe :=_fbdg .GetAllContentStreams ();
if _fe !=nil {return _fe ;};if _af :=_aef ;!_af .Identity (){_eaa =_cb .Sprintf ("%\u002e\u0032\u0066\u0020\u0025\u002e2\u0066\u0020\u0025\u002e\u0032\u0066 \u0025\u002e\u0032\u0066\u0020\u0025\u002e2\u0066\u0020\u0025\u002e\u0032\u0066\u0020\u0063\u006d\u0020%\u0073",_af [0],_af [1],_af [3],_af [4],_af [6],_af [7],_eaa );
};_fcd .Translate (0,float64 (_fcd .Height ()));_fcd .Scale (1,-1);_fcd .Push ();_fcd .SetRGBA (1,1,1,1);_fcd .DrawRectangle (0,0,float64 (_fcd .Width ()),float64 (_fcd .Height ()));_fcd .Fill ();_fcd .Pop ();_fcd .SetLineWidth (1.0);_fcd .SetRGBA (0,0,0,1);
return _fce .renderContentStream (_fcd ,_eaa ,_fbdg .Resources );};