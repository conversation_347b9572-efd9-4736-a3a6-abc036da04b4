//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package context ;import (_bc "errors";_de "github.com/unidoc/freetype/truetype";_fa "github.com/unidoc/unipdf/v3/core";_fb "github.com/unidoc/unipdf/v3/internal/cmap";_ba "github.com/unidoc/unipdf/v3/internal/textencoding";_ec "github.com/unidoc/unipdf/v3/internal/transform";
_bac "github.com/unidoc/unipdf/v3/model";_e "golang.org/x/image/font";_d "image";_b "image/color";_f "strconv";_bg "strings";);const (LineJoinRound LineJoin =iota ;LineJoinBevel ;);const (LineCapRound LineCap =iota ;LineCapButt ;LineCapSquare ;);func (_aff *TextState )ProcTD (tx ,ty float64 ){_aff .Tl =-ty ;
_aff .ProcTd (tx ,ty )};type Gradient interface{Pattern ;AddColorStop (_ee float64 ,_dd _b .Color );};type TextFont struct{Font *_bac .PdfFont ;Size float64 ;_abb *_de .Font ;_gaa *_bac .PdfFont ;};func (_cgc *TextFont )BytesToCharcodes (data []byte )[]_ba .CharCode {if _cgc ._gaa !=nil {return _cgc ._gaa .BytesToCharcodes (data );
};return _cgc .Font .BytesToCharcodes (data );};func NewTextFontFromPath (filePath string ,size float64 )(*TextFont ,error ){_dg ,_fagf :=_bac .NewPdfFontFromTTFFile (filePath );if _fagf !=nil {return nil ,_fagf ;};return NewTextFont (_dg ,size );};func NewTextFont (font *_bac .PdfFont ,size float64 )(*TextFont ,error ){_gdf :=font .FontDescriptor ();
if _gdf ==nil {return nil ,_bc .New ("\u0063\u006fu\u006c\u0064\u0020\u006e\u006f\u0074\u0020\u0067\u0065\u0074\u0020\u0066\u006f\u006e\u0074\u0020\u0064\u0065\u0073\u0063\u0072\u0069pt\u006f\u0072");};_af ,_cee :=_fa .GetStream (_gdf .FontFile2 );if !_cee {return nil ,_bc .New ("\u006di\u0073\u0073\u0069\u006e\u0067\u0020\u0066\u006f\u006e\u0074\u0020f\u0069\u006c\u0065\u0020\u0073\u0074\u0072\u0065\u0061\u006d");
};_cbf ,_ddd :=_fa .DecodeStream (_af );if _ddd !=nil {return nil ,_ddd ;};_acb ,_ddd :=_de .Parse (_cbf );if _ddd !=nil {return nil ,_ddd ;};_egf :=font .FontDescriptor ().FontName .String ();_ged :=len (_egf )> 7&&_egf [6]=='+';if _gdf .Flags !=nil {_baf ,_fag :=_f .Atoi (_gdf .Flags .String ());
if _fag ==nil &&_baf ==32{_ged =false ;};};if !_acb .HasCmap ()&&(!_bg .Contains (font .Encoder ().String (),"\u0049d\u0065\u006e\u0074\u0069\u0074\u0079-")||!_ged ){return nil ,_bc .New ("\u006e\u006f c\u006d\u0061\u0070 \u0061\u006e\u0064\u0020enc\u006fdi\u006e\u0067\u0020\u0069\u0073\u0020\u006eot\u0020\u0069\u0064\u0065\u006e\u0074\u0069t\u0079");
};return &TextFont {Font :font ,Size :size ,_abb :_acb },nil ;};func (_bfe *TextState )ProcTj (data []byte ,ctx Context ){_afc :=_bfe .Tf .Size ;_ca :=_bfe .Th /100.0;_bcc :=_bfe .GlobalScale ;_fgb :=_ec .NewMatrix (_afc *_ca ,0,0,_afc ,0,_bfe .Ts );_fc :=ctx .Matrix ();
_bdg :=_fc .Clone ().Mult (_bfe .Tm .Clone ().Mult (_fgb )).ScalingFactorY ();_fba :=_bfe .Tf .NewFace (_bdg );_efa :=_bfe .Tf .BytesToCharcodes (data );for _ ,_da :=range _efa {_cbb ,_bccb :=_bfe .Tf .CharcodeToRunes (_da );_cae :=string (_bccb );if _cae =="\u0000"{continue ;
};_faa :=_fc .Clone ().Mult (_bfe .Tm .Clone ().Mult (_fgb ));_dbe :=_faa .ScalingFactorY ();_faa =_faa .Scale (1/_dbe ,-1/_dbe );if _bfe .Tr !=TextRenderingModeInvisible {ctx .SetMatrix (_faa );ctx .DrawString (_cae ,_fba ,0,0);ctx .SetMatrix (_fc );};
_gaaf :=0.0;if _cae =="\u0020"{_gaaf =_bfe .Tw ;};_acf ,_ ,_cdd :=_bfe .Tf .GetCharMetrics (_cbb );if _cdd {_acf =_acf *0.001*_afc ;}else {_acf ,_ =ctx .MeasureString (_cae ,_fba );_acf =_acf /_bcc ;};_eec :=(_acf +_bfe .Tc +_gaaf )*_ca ;_bfe .Tm =_bfe .Tm .Mult (_ec .TranslationMatrix (_eec ,0));
};};type Context interface{Push ();Pop ();Matrix ()_ec .Matrix ;SetMatrix (_db _ec .Matrix );Translate (_df ,_g float64 );Scale (_ga ,_c float64 );Rotate (_cd float64 );MoveTo (_gg ,_cf float64 );LineTo (_ad ,_aa float64 );CubicTo (_fg ,_aaf ,_ff ,_ag ,_agg ,_dbd float64 );
QuadraticTo (_ac ,_baa ,_be ,_ab float64 );NewSubPath ();ClosePath ();ClearPath ();Clip ();ClipPreserve ();ResetClip ();LineWidth ()float64 ;SetLineWidth (_fgc float64 );SetLineCap (_cff LineCap );SetLineJoin (_ddf LineJoin );SetDash (_fge ...float64 );
SetDashOffset (_gb float64 );Fill ();FillPreserve ();Stroke ();StrokePreserve ();SetRGBA (_dc ,_ae ,_dfd ,_fbb float64 );SetFillRGBA (_bce ,_gf ,_bdf ,_gae float64 );SetFillStyle (_gd Pattern );SetFillRule (_ed FillRule );SetStrokeRGBA (_eda ,_ddg ,_cc ,_bdff float64 );
SetStrokeStyle (_fe Pattern );FillPattern ()Pattern ;StrokePattern ()Pattern ;TextState ()*TextState ;DrawString (_ge string ,_fga _e .Face ,_aaa ,_deb float64 );MeasureString (_bcd string ,_gdg _e .Face )(_bf ,_cfb float64 );DrawRectangle (_eeb ,_agd ,_eg ,_cb float64 );
DrawImage (_abf _d .Image ,_acg ,_cg int );DrawImageAnchored (_feb _d .Image ,_cba ,_ce int ,_dbg ,_ea float64 );Height ()int ;Width ()int ;};const (FillRuleWinding FillRule =iota ;FillRuleEvenOdd ;);func (_dec *TextState )ProcDQ (data []byte ,aw ,ac float64 ,ctx Context ){_dec .Tw =aw ;
_dec .Tc =ac ;_dec .ProcQ (data ,ctx );};type FillRule int ;const (TextRenderingModeFill TextRenderingMode =iota ;TextRenderingModeStroke ;TextRenderingModeFillStroke ;TextRenderingModeInvisible ;TextRenderingModeFillClip ;TextRenderingModeStrokeClip ;
TextRenderingModeFillStrokeClip ;TextRenderingModeClip ;);func (_gea *TextState )ProcQ (data []byte ,ctx Context ){_gea .ProcTStar ();_gea .ProcTj (data ,ctx )};func (_cac *TextState )Reset (){_cac .Tm =_ec .IdentityMatrix ();_cac .Tlm =_ec .IdentityMatrix ()};
type TextState struct{Tc float64 ;Tw float64 ;Th float64 ;Tl float64 ;Tf *TextFont ;Ts float64 ;Tm _ec .Matrix ;Tlm _ec .Matrix ;Tr TextRenderingMode ;GlobalScale float64 ;};func (_fbf *TextState )Translate (tx ,ty float64 ){_fbf .Tm =_fbf .Tm .Mult (_ec .TranslationMatrix (tx ,ty ));
};func (_dda *TextFont )GetCharMetrics (code _ba .CharCode )(float64 ,float64 ,bool ){if _aad ,_debd :=_dda .Font .GetCharMetrics (code );_debd &&_aad .Wx !=0{return _aad .Wx ,_aad .Wy ,_debd ;};if _dda ._gaa ==nil {return 0,0,false ;};_bae ,_eebd :=_dda ._gaa .GetCharMetrics (code );
return _bae .Wx ,_bae .Wy ,_eebd &&_bae .Wx !=0;};type LineJoin int ;type TextRenderingMode int ;type LineCap int ;func (_gfc *TextState )ProcTm (a ,b ,c ,d ,e ,f float64 ){_gfc .Tm =_ec .NewMatrix (a ,b ,c ,d ,e ,f );_gfc .Tlm =_gfc .Tm .Clone ();};func (_eeg *TextFont )charcodeToRunesSimple (_ef _ba .CharCode )(_ba .CharCode ,[]rune ){_gbc :=[]_ba .CharCode {_ef };
if _eeg .Font .IsSimple ()&&_eeg ._abb !=nil {if _fgeb :=_eeg ._abb .Index (rune (_ef ));_fgeb > 0{return _ef ,[]rune {rune (_ef )};};};if _eeg ._abb !=nil &&!_eeg ._abb .HasCmap ()&&_bg .Contains (_eeg .Font .Encoder ().String (),"\u0049d\u0065\u006e\u0074\u0069\u0074\u0079-"){if _dge :=_eeg ._abb .Index (rune (_ef ));
_dge > 0{return _ef ,[]rune {rune (_ef )};};};return _ef ,_eeg .Font .CharcodesToUnicode (_gbc );};func NewTextState ()TextState {return TextState {Th :100,Tm :_ec .IdentityMatrix (),Tlm :_ec .IdentityMatrix ()};};func (_ggg *TextFont )NewFace (size float64 )_e .Face {return _de .NewFace (_ggg ._abb ,&_de .Options {Size :size });
};func (_egc *TextState )ProcTf (font *TextFont ){_egc .Tf =font };func (_gaab *TextState )ProcTd (tx ,ty float64 ){_gaab .Tlm .Concat (_ec .TranslationMatrix (tx ,ty ));_gaab .Tm =_gaab .Tlm .Clone ();};func (_dgb *TextState )ProcTStar (){_dgb .ProcTd (0,-_dgb .Tl )};
func (_aac *TextFont )CharcodeToRunes (charcode _ba .CharCode )(_ba .CharCode ,[]rune ){_dfb :=[]_ba .CharCode {charcode };if _aac ._gaa ==nil ||_aac ._gaa ==_aac .Font {return _aac .charcodeToRunesSimple (charcode );};_debb :=_aac ._gaa .CharcodesToUnicode (_dfb );
_bde ,_ :=_aac .Font .RunesToCharcodeBytes (_debb );_ecd :=_aac .Font .BytesToCharcodes (_bde );_dfg :=charcode ;if len (_ecd )> 0&&_ecd [0]!=0{_dfg =_ecd [0];};if string (_debb )==string (_fb .MissingCodeRune )&&_aac ._gaa .BaseFont ()==_aac .Font .BaseFont (){return _aac .charcodeToRunesSimple (charcode );
};return _dfg ,_debb ;};type Pattern interface{ColorAt (_bd ,_eb int )_b .Color ;};func (_edb *TextFont )WithSize (size float64 ,originalFont *_bac .PdfFont )*TextFont {return &TextFont {Font :_edb .Font ,Size :size ,_abb :_edb ._abb ,_gaa :originalFont };
};