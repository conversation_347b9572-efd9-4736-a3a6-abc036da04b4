//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package imagerender ;import (_ee "errors";_d "fmt";_cc "github.com/unidoc/freetype/raster";_c "github.com/unidoc/unipdf/v3/common";_ae "github.com/unidoc/unipdf/v3/internal/transform";_eed "github.com/unidoc/unipdf/v3/render/internal/context";_cb "golang.org/x/image/draw";
_ba "golang.org/x/image/font";_bb "golang.org/x/image/math/f64";_gc "golang.org/x/image/math/fixed";_e "image";_a "image/color";_ag "image/draw";_f "math";_g "sort";_bd "strings";);func (_ddd *Context )SetMask (mask *_e .Alpha )error {if mask .Bounds ().Size ()!=_ddd ._ad .Bounds ().Size (){return _ee .New ("\u006d\u0061\u0073\u006b\u0020\u0073i\u007a\u0065\u0020\u006d\u0075\u0073\u0074\u0020\u006d\u0061\u0074\u0063\u0068 \u0063\u006f\u006e\u0074\u0065\u0078\u0074 \u0073\u0069\u007a\u0065");
};_ddd ._faa =mask ;return nil ;};func _cfd (_fba [][]_ae .Point )_cc .Path {var _agbc _cc .Path ;for _ ,_abe :=range _fba {var _ggaf _gc .Point26_6 ;for _fefd ,_bfg :=range _abe {_afd :=_cgae (_bfg );if _fefd ==0{_agbc .Start (_afd );}else {_bgg :=_afd .X -_ggaf .X ;
_aeea :=_afd .Y -_ggaf .Y ;if _bgg < 0{_bgg =-_bgg ;};if _aeea < 0{_aeea =-_aeea ;};if _bgg +_aeea > 8{_agbc .Add1 (_afd );};};_ggaf =_afd ;};};return _agbc ;};func _agb (_caaad ,_agfgf _a .Color ,_eceg float64 )_a .Color {_becd ,_bgdd ,_gbde ,_fceb :=_caaad .RGBA ();
_cdaf ,_dda ,_daf ,_bfd :=_agfgf .RGBA ();return _a .RGBA {_edfd (_becd ,_cdaf ,_eceg ),_edfd (_bgdd ,_dda ,_eceg ),_edfd (_gbde ,_daf ,_eceg ),_edfd (_fceb ,_bfd ,_eceg )};};func (_fbecd *solidPattern )ColorAt (x ,y int )_a .Color {return _fbecd ._gbed };
func (_bfa *Context )SetFillRule (fillRule _eed .FillRule ){_bfa ._deg =fillRule };func (_gbea *radialGradient )ColorAt (x ,y int )_a .Color {if len (_gbea ._fdfdd )==0{return _a .Transparent ;};_gfag ,_fgdb :=float64 (x )+0.5-_gbea ._eadb ._fbde ,float64 (y )+0.5-_gbea ._eadb ._ccd ;
_cfg :=_cdf (_gfag ,_fgdb ,_gbea ._eadb ._accf ,_gbea ._caaa ._fbde ,_gbea ._caaa ._ccd ,_gbea ._caaa ._accf );_bbdc :=_cdf (_gfag ,_fgdb ,-_gbea ._eadb ._accf ,_gfag ,_fgdb ,_gbea ._eadb ._accf );if _gbea ._cfc ==0{if _cfg ==0{return _a .Transparent ;
};_bgc :=0.5*_bbdc /_cfg ;if _bgc *_gbea ._caaa ._accf >=_gbea ._adbe {return _eag (_bgc ,_gbea ._fdfdd );};return _a .Transparent ;};_ecd :=_cdf (_cfg ,_gbea ._cfc ,0,_cfg ,-_bbdc ,0);if _ecd >=0{_acf :=_f .Sqrt (_ecd );_gga :=(_cfg +_acf )*_gbea ._baba ;
_ebf :=(_cfg -_acf )*_gbea ._baba ;if _gga *_gbea ._caaa ._accf >=_gbea ._adbe {return _eag (_gga ,_gbea ._fdfdd );}else if _ebf *_gbea ._caaa ._accf >=_gbea ._adbe {return _eag (_ebf ,_gbea ._fdfdd );};};return _a .Transparent ;};func (_aeed *Context )ResetClip (){_aeed ._faa =nil };
func (_fdg *Context )capper ()_cc .Capper {switch _fdg ._dfe {case _eed .LineCapButt :return _cc .ButtCapper ;case _eed .LineCapRound :return _cc .RoundCapper ;case _eed .LineCapSquare :return _cc .SquareCapper ;};return nil ;};func NewLinearGradient (x0 ,y0 ,x1 ,y1 float64 )_eed .Gradient {_fca :=&linearGradient {_feg :x0 ,_gfg :y0 ,_bddg :x1 ,_fede :y1 };
return _fca ;};type linearGradient struct{_feg ,_gfg ,_bddg ,_fede float64 ;_dfdd stops ;};func NewRadialGradient (x0 ,y0 ,r0 ,x1 ,y1 ,r1 float64 )_eed .Gradient {_cddd :=circle {x0 ,y0 ,r0 };_faab :=circle {x1 ,y1 ,r1 };_babf :=circle {x1 -x0 ,y1 -y0 ,r1 -r0 };
_aea :=_cdf (_babf ._fbde ,_babf ._ccd ,-_babf ._accf ,_babf ._fbde ,_babf ._ccd ,_babf ._accf );var _age float64 ;if _aea !=0{_age =1.0/_aea ;};_beg :=-_cddd ._accf ;_cbf :=&radialGradient {_eadb :_cddd ,_acdb :_faab ,_caaa :_babf ,_cfc :_aea ,_baba :_age ,_adbe :_beg };
return _cbf ;};func _bbd (_fa ,_ge ,_gb ,_ac ,_ab ,_bbe ,_cd float64 )(_db ,_cbg float64 ){_cf :=1-_cd ;_da :=_cf *_cf ;_ea :=2*_cf *_cd ;_af :=_cd *_cd ;_db =_da *_fa +_ea *_gb +_af *_ab ;_cbg =_da *_ge +_ea *_ac +_af *_bbe ;return ;};func (_gdea *Context )SetHexColor (x string ){_beef ,_acdf ,_bdc ,_fab :=_cbc (x );
_gdea .SetRGBA255 (_beef ,_acdf ,_bdc ,_fab );};func (_aeef *Context )DrawImage (im _e .Image ,x ,y int ){_aeef .DrawImageAnchored (im ,x ,y ,0,0)};func (_acc *Context )CubicTo (x1 ,y1 ,x2 ,y2 ,x3 ,y3 float64 ){if !_acc ._dca {_acc .MoveTo (x1 ,y1 );};
_ebgd ,_eea :=_acc ._gde .X ,_acc ._gde .Y ;x1 ,y1 =_acc .Transform (x1 ,y1 );x2 ,y2 =_acc .Transform (x2 ,y2 );x3 ,y3 =_acc .Transform (x3 ,y3 );_eegc :=_dbd (_ebgd ,_eea ,x1 ,y1 ,x2 ,y2 ,x3 ,y3 );_cgb :=_cgae (_acc ._gde );for _ ,_cce :=range _eegc [1:]{_caa :=_cgae (_cce );
if _caa ==_cgb {continue ;};_cgb =_caa ;_acc ._gag .Add1 (_caa );_acc ._fd .Add1 (_caa );_acc ._gde =_cce ;};};func (_ccee *Context )DrawCircle (x ,y ,r float64 ){_ccee .NewSubPath ();_ccee .DrawEllipticalArc (x ,y ,r ,r ,0,2*_f .Pi );_ccee .ClosePath ();
};func (_ggbe *Context )DrawEllipticalArc (x ,y ,rx ,ry ,angle1 ,angle2 float64 ){const _bfc =16;for _efa :=0;_efa < _bfc ;_efa ++{_bga :=float64 (_efa +0)/_bfc ;_afg :=float64 (_efa +1)/_bfc ;_ggbb :=angle1 +(angle2 -angle1 )*_bga ;_ebc :=angle1 +(angle2 -angle1 )*_afg ;
_ccef :=x +rx *_f .Cos (_ggbb );_aga :=y +ry *_f .Sin (_ggbb );_cda :=x +rx *_f .Cos ((_ggbb +_ebc )/2);_agg :=y +ry *_f .Sin ((_ggbb +_ebc )/2);_cab :=x +rx *_f .Cos (_ebc );_gaac :=y +ry *_f .Sin (_ebc );_aeg :=2*_cda -_ccef /2-_cab /2;_faca :=2*_agg -_aga /2-_gaac /2;
if _efa ==0{if _ggbe ._dca {_ggbe .LineTo (_ccef ,_aga );}else {_ggbe .MoveTo (_ccef ,_aga );};};_ggbe .QuadraticTo (_aeg ,_faca ,_cab ,_gaac );};};func (_aee *Context )SetDash (dashes ...float64 ){_aee ._adb =dashes };func (_cdbg *Context )DrawPoint (x ,y ,r float64 ){_cdbg .Push ();
_bgd ,_gdc :=_cdbg .Transform (x ,y );_cdbg .Identity ();_cdbg .DrawCircle (_bgd ,_gdc ,r );_cdbg .Pop ();};func (_aa *Context )SetStrokeStyle (pattern _eed .Pattern ){_aa ._dg =pattern };func (_cgdb *Context )TextState ()*_eed .TextState {return &_cgdb ._deb };
func (_cfa *Context )Scale (x ,y float64 ){_cfa ._dcab =_cfa ._dcab .Scale (x ,y )};func (_ada *Context )setFillAndStrokeColor (_gab _a .Color ){_ada ._fce =_gab ;_ada ._ega =_acg (_gab );_ada ._dg =_acg (_gab );};func _ecba (_geeb *_e .RGBA ,_aca *_e .Alpha ,_geed _eed .Pattern )*patternPainter {return &patternPainter {_geeb ,_aca ,_geed };
};func _faaa (_edd _e .Image ,_aaad repeatOp )_eed .Pattern {return &surfacePattern {_afdf :_edd ,_dbad :_aaad };};func (_fac *Context )AsMask ()*_e .Alpha {_gbc :=_e .NewAlpha (_fac ._ad .Bounds ());_cb .Draw (_gbc ,_fac ._ad .Bounds (),_fac ._ad ,_e .Point {},_cb .Src );
return _gbc ;};type radialGradient struct{_eadb ,_acdb ,_caaa circle ;_cfc ,_baba float64 ;_adbe float64 ;_fdfdd stops ;};func (_ggb *Context )StrokePreserve (){var _cgdd _cc .Painter ;if _ggb ._faa ==nil {if _fde ,_aab :=_ggb ._dg .(*solidPattern );_aab {_bcc :=_cc .NewRGBAPainter (_ggb ._ad );
_bcc .SetColor (_fde ._gbed );_cgdd =_bcc ;};};if _cgdd ==nil {_cgdd =_ecba (_ggb ._ad ,_ggb ._faa ,_ggb ._dg );};_ggb .stroke (_cgdd );};func (_fga *Context )Translate (x ,y float64 ){_fga ._dcab =_fga ._dcab .Translate (x ,y )};func (_ebae *Context )DrawEllipse (x ,y ,rx ,ry float64 ){_ebae .NewSubPath ();
_ebae .DrawEllipticalArc (x ,y ,rx ,ry ,0,2*_f .Pi );_ebae .ClosePath ();};type stop struct{_gabg float64 ;_gdde _a .Color ;};func _cbc (_gbdgg string )(_acgc ,_ggbd ,_aabg ,_bdbe int ){_gbdgg =_bd .TrimPrefix (_gbdgg ,"\u0023");_bdbe =255;if len (_gbdgg )==3{_fbcf :="\u00251\u0078\u0025\u0031\u0078\u0025\u0031x";
_d .Sscanf (_gbdgg ,_fbcf ,&_acgc ,&_ggbd ,&_aabg );_acgc |=_acgc <<4;_ggbd |=_ggbd <<4;_aabg |=_aabg <<4;};if len (_gbdgg )==6{_cceea :="\u0025\u0030\u0032x\u0025\u0030\u0032\u0078\u0025\u0030\u0032\u0078";_d .Sscanf (_gbdgg ,_cceea ,&_acgc ,&_ggbd ,&_aabg );
};if len (_gbdgg )==8{_bae :="\u0025\u00302\u0078\u0025\u00302\u0078\u0025\u0030\u0032\u0078\u0025\u0030\u0032\u0078";_d .Sscanf (_gbdgg ,_bae ,&_acgc ,&_ggbd ,&_aabg ,&_bdbe );};return ;};func _dbd (_cca ,_ca ,_egf ,_bg ,_agd ,_geb ,_ggd ,_bbf float64 )[]_ae .Point {_beb :=(_f .Hypot (_egf -_cca ,_bg -_ca )+_f .Hypot (_agd -_egf ,_geb -_bg )+_f .Hypot (_ggd -_agd ,_bbf -_geb ));
_bbff :=int (_beb +0.5);if _bbff < 4{_bbff =4;};_eee :=float64 (_bbff )-1;_gf :=make ([]_ae .Point ,_bbff );for _ga :=0;_ga < _bbff ;_ga ++{_agf :=float64 (_ga )/_eee ;_dcdb ,_bdd :=_dcd (_cca ,_ca ,_egf ,_bg ,_agd ,_geb ,_ggd ,_bbf ,_agf );_gf [_ga ]=_ae .NewPoint (_dcdb ,_bdd );
};return _gf ;};func _caf (_dbae _gc .Int26_6 )float64 {const _bdcf ,_bfbg =6,1<<6-1;if _dbae >=0{return float64 (_dbae >>_bdcf )+float64 (_dbae &_bfbg )/64;};_dbae =-_dbae ;if _dbae >=0{return -(float64 (_dbae >>_bdcf )+float64 (_dbae &_bfbg )/64);};return 0;
};func (_ccce *patternPainter )Paint (ss []_cc .Span ,done bool ){_cbae :=_ccce ._abc .Bounds ();for _ ,_egbf :=range ss {if _egbf .Y < _cbae .Min .Y {continue ;};if _egbf .Y >=_cbae .Max .Y {return ;};if _egbf .X0 < _cbae .Min .X {_egbf .X0 =_cbae .Min .X ;
};if _egbf .X1 > _cbae .Max .X {_egbf .X1 =_cbae .Max .X ;};if _egbf .X0 >=_egbf .X1 {continue ;};const _dbg =1<<16-1;_cgda :=_egbf .Y -_ccce ._abc .Rect .Min .Y ;_abb :=_egbf .X0 -_ccce ._abc .Rect .Min .X ;_feb :=(_egbf .Y -_ccce ._abc .Rect .Min .Y )*_ccce ._abc .Stride +(_egbf .X0 -_ccce ._abc .Rect .Min .X )*4;
_bggc :=_feb +(_egbf .X1 -_egbf .X0 )*4;for _abd ,_eac :=_feb ,_abb ;_abd < _bggc ;_abd ,_eac =_abd +4,_eac +1{_fega :=_egbf .Alpha ;if _ccce ._cfaa !=nil {_fega =_fega *uint32 (_ccce ._cfaa .AlphaAt (_eac ,_cgda ).A )/255;if _fega ==0{continue ;};};_abcg :=_ccce ._fag .ColorAt (_eac ,_cgda );
_edc ,_cdgb ,_aabda ,_fdge :=_abcg .RGBA ();_ccg :=uint32 (_ccce ._abc .Pix [_abd +0]);_cfed :=uint32 (_ccce ._abc .Pix [_abd +1]);_aad :=uint32 (_ccce ._abc .Pix [_abd +2]);_eafe :=uint32 (_ccce ._abc .Pix [_abd +3]);_eead :=(_dbg -(_fdge *_fega /_dbg ))*0x101;
_ccce ._abc .Pix [_abd +0]=uint8 ((_ccg *_eead +_edc *_fega )/_dbg >>8);_ccce ._abc .Pix [_abd +1]=uint8 ((_cfed *_eead +_cdgb *_fega )/_dbg >>8);_ccce ._abc .Pix [_abd +2]=uint8 ((_aad *_eead +_aabda *_fega )/_dbg >>8);_ccce ._abc .Pix [_abd +3]=uint8 ((_eafe *_eead +_fdge *_fega )/_dbg >>8);
};};};type Context struct{_afa int ;_dace int ;_dcf *_cc .Rasterizer ;_ad *_e .RGBA ;_faa *_e .Alpha ;_fce _a .Color ;_ega _eed .Pattern ;_dg _eed .Pattern ;_gag _cc .Path ;_fd _cc .Path ;_gdd _ae .Point ;_gde _ae .Point ;_dca bool ;_adb []float64 ;_cad float64 ;
_dd float64 ;_dfe _eed .LineCap ;_ecc _eed .LineJoin ;_deg _eed .FillRule ;_dcab _ae .Matrix ;_deb _eed .TextState ;_fe []*Context ;};func _gac (_bdge _cc .Path ,_gcdf []float64 ,_ebd float64 )_cc .Path {return _cfd (_fdef (_cccb (_bdge ),_gcdf ,_ebd ));
};func _cg (_fc ,_be ,_bdb ,_bc ,_ec ,_cga float64 )[]_ae .Point {_dbb :=(_f .Hypot (_bdb -_fc ,_bc -_be )+_f .Hypot (_ec -_bdb ,_cga -_bc ));_gbe :=int (_dbb +0.5);if _gbe < 4{_gbe =4;};_bde :=float64 (_gbe )-1;_gd :=make ([]_ae .Point ,_gbe );for _df :=0;
_df < _gbe ;_df ++{_fg :=float64 (_df )/_bde ;_dc ,_eeg :=_bbd (_fc ,_be ,_bdb ,_bc ,_ec ,_cga ,_fg );_gd [_df ]=_ae .NewPoint (_dc ,_eeg );};return _gd ;};func (_dfd *Context )SetStrokeRGBA (r ,g ,b ,a float64 ){_gdf :=_a .NRGBA {uint8 (r *255),uint8 (g *255),uint8 (b *255),uint8 (a *255)};
_dfd ._dg =_acg (_gdf );};func _acg (_gdbc _a .Color )_eed .Pattern {return &solidPattern {_gbed :_gdbc }};func _cccb (_ddad _cc .Path )[][]_ae .Point {var _cec [][]_ae .Point ;var _dce []_ae .Point ;var _gdfd ,_aec float64 ;for _bbefg :=0;_bbefg < len (_ddad );
{switch _ddad [_bbefg ]{case 0:if len (_dce )> 0{_cec =append (_cec ,_dce );_dce =nil ;};_facc :=_caf (_ddad [_bbefg +1]);_aed :=_caf (_ddad [_bbefg +2]);_dce =append (_dce ,_ae .NewPoint (_facc ,_aed ));_gdfd ,_aec =_facc ,_aed ;_bbefg +=4;case 1:_cddg :=_caf (_ddad [_bbefg +1]);
_ebag :=_caf (_ddad [_bbefg +2]);_dce =append (_dce ,_ae .NewPoint (_cddg ,_ebag ));_gdfd ,_aec =_cddg ,_ebag ;_bbefg +=4;case 2:_eff :=_caf (_ddad [_bbefg +1]);_def :=_caf (_ddad [_bbefg +2]);_dcac :=_caf (_ddad [_bbefg +3]);_dcdd :=_caf (_ddad [_bbefg +4]);
_dgg :=_cg (_gdfd ,_aec ,_eff ,_def ,_dcac ,_dcdd );_dce =append (_dce ,_dgg ...);_gdfd ,_aec =_dcac ,_dcdd ;_bbefg +=6;case 3:_bcb :=_caf (_ddad [_bbefg +1]);_bgbf :=_caf (_ddad [_bbefg +2]);_aeeb :=_caf (_ddad [_bbefg +3]);_ggdd :=_caf (_ddad [_bbefg +4]);
_cdbga :=_caf (_ddad [_bbefg +5]);_afed :=_caf (_ddad [_bbefg +6]);_dcef :=_dbd (_gdfd ,_aec ,_bcb ,_bgbf ,_aeeb ,_ggdd ,_cdbga ,_afed );_dce =append (_dce ,_dcef ...);_gdfd ,_aec =_cdbga ,_afed ;_bbefg +=8;default:_c .Log .Debug ("\u0057\u0041\u0052\u004e: \u0069\u006e\u0076\u0061\u006c\u0069\u0064\u0020\u0070\u0061\u0074\u0068\u003a\u0020%\u0076",_ddad );
return _cec ;};};if len (_dce )> 0{_cec =append (_cec ,_dce );};return _cec ;};func (_adcc *Context )Stroke (){_adcc .StrokePreserve ();_adcc .ClearPath ()};func (_degd stops )Len ()int {return len (_degd )};func (_gbf *Context )SetFillStyle (pattern _eed .Pattern ){if _eab ,_gbec :=pattern .(*solidPattern );
_gbec {_gbf ._fce =_eab ._gbed ;};_gbf ._ega =pattern ;};func (_dfc *Context )SetRGB (r ,g ,b float64 ){_dfc .SetRGBA (r ,g ,b ,1)};func (_cdd *Context )SetRGB255 (r ,g ,b int ){_cdd .SetRGBA255 (r ,g ,b ,255)};func (_ead *Context )FillPattern ()_eed .Pattern {return _ead ._ega };
func (_ccc *radialGradient )AddColorStop (offset float64 ,color _a .Color ){_ccc ._fdfdd =append (_ccc ._fdfdd ,stop {_gabg :offset ,_gdde :color });_g .Sort (_ccc ._fdfdd );};func _eag (_cbb float64 ,_ggbg stops )_a .Color {if _cbb <=0.0||len (_ggbg )==1{return _ggbg [0]._gdde ;
};_fgg :=_ggbg [len (_ggbg )-1];if _cbb >=_fgg ._gabg {return _fgg ._gdde ;};for _ade ,_bgee :=range _ggbg [1:]{if _cbb < _bgee ._gabg {_cbb =(_cbb -_ggbg [_ade ]._gabg )/(_bgee ._gabg -_ggbg [_ade ]._gabg );return _agb (_ggbg [_ade ]._gdde ,_bgee ._gdde ,_cbb );
};};return _fgg ._gdde ;};func (_aaf *Context )Clip (){_aaf .ClipPreserve ();_aaf .ClearPath ()};func (_eded *Context )DrawArc (x ,y ,r ,angle1 ,angle2 float64 ){_eded .DrawEllipticalArc (x ,y ,r ,r ,angle1 ,angle2 );};func (_fbc *Context )Identity (){_fbc ._dcab =_ae .IdentityMatrix ()};
func (_eec *Context )drawRegularPolygon (_ccea int ,_cee ,_gcd ,_dee ,_gfd float64 ){_fbe :=2*_f .Pi /float64 (_ccea );_gfd -=_f .Pi /2;if _ccea %2==0{_gfd +=_fbe /2;};_eec .NewSubPath ();for _fgca :=0;_fgca < _ccea ;_fgca ++{_baf :=_gfd +_fbe *float64 (_fgca );
_eec .LineTo (_cee +_dee *_f .Cos (_baf ),_gcd +_dee *_f .Sin (_baf ));};_eec .ClosePath ();};func (_cdg *Context )InvertMask (){if _cdg ._faa ==nil {_cdg ._faa =_e .NewAlpha (_cdg ._ad .Bounds ());}else {for _ebb ,_eeag :=range _cdg ._faa .Pix {_cdg ._faa .Pix [_ebb ]=255-_eeag ;
};};};type repeatOp int ;func (_fea *Context )DrawRectangle (x ,y ,w ,h float64 ){_fea .NewSubPath ();_fea .MoveTo (x ,y );_fea .LineTo (x +w ,y );_fea .LineTo (x +w ,y +h );_fea .LineTo (x ,y +h );_fea .ClosePath ();};func (_gadf *Context )DrawString (s string ,face _ba .Face ,x ,y float64 ){_gadf .DrawStringAnchored (s ,face ,x ,y ,0,0);
};func (_adc *Context )Width ()int {return _adc ._afa };func (_bff *Context )Pop (){_efc :=*_bff ;_bec :=_bff ._fe ;_geec :=_bec [len (_bec )-1];*_bff =*_geec ;_bff ._gag =_efc ._gag ;_bff ._fd =_efc ._fd ;_bff ._gdd =_efc ._gdd ;_bff ._gde =_efc ._gde ;
_bff ._dca =_efc ._dca ;};func (_cbd *Context )LineTo (x ,y float64 ){if !_cbd ._dca {_cbd .MoveTo (x ,y );}else {x ,y =_cbd .Transform (x ,y );_ggc :=_ae .NewPoint (x ,y );_efb :=_cgae (_ggc );_cbd ._gag .Add1 (_efb );_cbd ._fd .Add1 (_efb );_cbd ._gde =_ggc ;
};};func (_ced *linearGradient )AddColorStop (offset float64 ,color _a .Color ){_ced ._dfdd =append (_ced ._dfdd ,stop {_gabg :offset ,_gdde :color });_g .Sort (_ced ._dfdd );};func (_caba *Context )MeasureString (s string ,face _ba .Face )(_cag ,_aabb float64 ){_baca :=&_ba .Drawer {Face :face };
_fef :=_baca .MeasureString (s );return float64 (_fef >>6),_caba ._deb .Tf .Size ;};func (_bebf *Context )SetMatrix (m _ae .Matrix ){_bebf ._dcab =m };func (_cgd *Context )SetColor (c _a .Color ){_cgd .setFillAndStrokeColor (c )};func _ccga (_dcce float64 )float64 {return _dcce *_f .Pi /180};
func _cdf (_edf ,_ece ,_egc ,_bdea ,_ceb ,_accb float64 )float64 {return _edf *_bdea +_ece *_ceb +_egc *_accb ;};func (_dcc stops )Less (i ,j int )bool {return _dcc [i ]._gabg < _dcc [j ]._gabg };func (_gca *Context )QuadraticTo (x1 ,y1 ,x2 ,y2 float64 ){if !_gca ._dca {_gca .MoveTo (x1 ,y1 );
};x1 ,y1 =_gca .Transform (x1 ,y1 );x2 ,y2 =_gca .Transform (x2 ,y2 );_eade :=_ae .NewPoint (x1 ,y1 );_agfd :=_ae .NewPoint (x2 ,y2 );_fed :=_cgae (_eade );_bgef :=_cgae (_agfd );_gca ._gag .Add2 (_fed ,_bgef );_gca ._fd .Add2 (_fed ,_bgef );_gca ._gde =_agfd ;
};func (_dgef *Context )Transform (x ,y float64 )(_fdd ,_fgad float64 ){return _dgef ._dcab .Transform (x ,y );};func (_bebg *Context )joiner ()_cc .Joiner {switch _bebg ._ecc {case _eed .LineJoinBevel :return _cc .BevelJoiner ;case _eed .LineJoinRound :return _cc .RoundJoiner ;
};return nil ;};func (_gdbb *Context )DrawRoundedRectangle (x ,y ,w ,h ,r float64 ){_ege ,_dgcf ,_daa ,_bbef :=x ,x +r ,x +w -r ,x +w ;_acce ,_fgd ,_efd ,_gbda :=y ,y +r ,y +h -r ,y +h ;_gdbb .NewSubPath ();_gdbb .MoveTo (_dgcf ,_acce );_gdbb .LineTo (_daa ,_acce );
_gdbb .DrawArc (_daa ,_fgd ,r ,_ccga (270),_ccga (360));_gdbb .LineTo (_bbef ,_efd );_gdbb .DrawArc (_daa ,_efd ,r ,_ccga (0),_ccga (90));_gdbb .LineTo (_dgcf ,_gbda );_gdbb .DrawArc (_dgcf ,_efd ,r ,_ccga (90),_ccga (180));_gdbb .LineTo (_ege ,_fgd );
_gdbb .DrawArc (_dgcf ,_fgd ,r ,_ccga (180),_ccga (270));_gdbb .ClosePath ();};func (_cff *Context )ClosePath (){if _cff ._dca {_dec :=_cgae (_cff ._gdd );_cff ._gag .Add1 (_dec );_cff ._fd .Add1 (_dec );_cff ._gde =_cff ._gdd ;};};func (_fb *Context )fill (_dbbg _cc .Painter ){_bfb :=_fb ._fd ;
if _fb ._dca {_bfb =make (_cc .Path ,len (_fb ._fd ));copy (_bfb ,_fb ._fd );_bfb .Add1 (_cgae (_fb ._gdd ));};_dcfg :=_fb ._dcf ;_dcfg .UseNonZeroWinding =_fb ._deg ==_eed .FillRuleWinding ;_dcfg .Clear ();_dcfg .AddPath (_bfb );_dcfg .Rasterize (_dbbg );
};func (_bgeff *Context )RotateAbout (angle ,x ,y float64 ){_bgeff .Translate (x ,y );_bgeff .Rotate (angle );_bgeff .Translate (-x ,-y );};func (_ede *Context )SetRGBA255 (r ,g ,b ,a int ){_ede ._fce =_a .NRGBA {uint8 (r ),uint8 (g ),uint8 (b ),uint8 (a )};
_ede .setFillAndStrokeColor (_ede ._fce );};func (_adbd *Context )DrawStringAnchored (s string ,face _ba .Face ,x ,y ,ax ,ay float64 ){_gee ,_eecg :=_adbd .MeasureString (s ,face );_adbd .drawString (s ,face ,x -ax *_gee ,y +ay *_eecg );};type stops []stop ;
func _dcd (_acd ,_bda ,_gg ,_dcb ,_de ,_cgaa ,_dac ,_eg ,_bef float64 )(_ecf ,_bcf float64 ){_gbd :=1-_bef ;_ecg :=_gbd *_gbd *_gbd ;_egg :=3*_gbd *_gbd *_bef ;_gcg :=3*_gbd *_bef *_bef ;_dbc :=_bef *_bef *_bef ;_ecf =_ecg *_acd +_egg *_gg +_gcg *_de +_dbc *_dac ;
_bcf =_ecg *_bda +_egg *_dcb +_gcg *_cgaa +_dbc *_eg ;return ;};func (_eaa *Context )FillPreserve (){var _aaa _cc .Painter ;if _eaa ._faa ==nil {if _cac ,_fbd :=_eaa ._ega .(*solidPattern );_fbd {_gdb :=_cc .NewRGBAPainter (_eaa ._ad );_gdb .SetColor (_cac ._gbed );
_aaa =_gdb ;};};if _aaa ==nil {_aaa =_ecba (_eaa ._ad ,_eaa ._faa ,_eaa ._ega );};_eaa .fill (_aaa );};type circle struct{_fbde ,_ccd ,_accf float64 };type patternPainter struct{_abc *_e .RGBA ;_cfaa *_e .Alpha ;_fag _eed .Pattern ;};func _fbg (_bfbf float64 )_gc .Int26_6 {return _gc .Int26_6 (_bfbf *64)};
func (_ef *Context )MoveTo (x ,y float64 ){if _ef ._dca {_ef ._fd .Add1 (_cgae (_ef ._gdd ));};x ,y =_ef .Transform (x ,y );_bdca :=_ae .NewPoint (x ,y );_ff :=_cgae (_bdca );_ef ._gag .Start (_ff );_ef ._fd .Start (_ff );_ef ._gdd =_bdca ;_ef ._gde =_bdca ;
_ef ._dca =true ;};func (_bab *Context )ClipPreserve (){_ecb :=_e .NewAlpha (_e .Rect (0,0,_bab ._afa ,_bab ._dace ));_ace :=_cc .NewAlphaOverPainter (_ecb );_bab .fill (_ace );if _bab ._faa ==nil {_bab ._faa =_ecb ;}else {_afe :=_e .NewAlpha (_e .Rect (0,0,_bab ._afa ,_bab ._dace ));
_cb .DrawMask (_afe ,_afe .Bounds (),_ecb ,_e .Point {},_bab ._faa ,_e .Point {},_cb .Over );_bab ._faa =_afe ;};};func (_bdbf *Context )Shear (x ,y float64 ){_bdbf ._dcab .Shear (x ,y )};func (_bdbg *Context )StrokePattern ()_eed .Pattern {return _bdbg ._dg };
func (_aae *linearGradient )ColorAt (x ,y int )_a .Color {if len (_aae ._dfdd )==0{return _a .Transparent ;};_gfa ,_aeb :=float64 (x ),float64 (y );_bafe ,_ggdf ,_cbaf ,_gea :=_aae ._feg ,_aae ._gfg ,_aae ._bddg ,_aae ._fede ;_bca ,_bgea :=_cbaf -_bafe ,_gea -_ggdf ;
if _bgea ==0&&_bca !=0{return _eag ((_gfa -_bafe )/_bca ,_aae ._dfdd );};if _bca ==0&&_bgea !=0{return _eag ((_aeb -_ggdf )/_bgea ,_aae ._dfdd );};_eaf :=_bca *(_gfa -_bafe )+_bgea *(_aeb -_ggdf );if _eaf < 0{return _aae ._dfdd [0]._gdde ;};_efg :=_f .Hypot (_bca ,_bgea );
_efgc :=((_gfa -_bafe )*-_bgea +(_aeb -_ggdf )*_bca )/(_efg *_efg );_bcd ,_aabd :=_bafe +_efgc *-_bgea ,_ggdf +_efgc *_bca ;_dddaf :=_f .Hypot (_gfa -_bcd ,_aeb -_aabd )/_efg ;return _eag (_dddaf ,_aae ._dfdd );};type solidPattern struct{_gbed _a .Color };
type surfacePattern struct{_afdf _e .Image ;_dbad repeatOp ;};func (_dbbc *Context )SetLineJoin (lineJoin _eed .LineJoin ){_dbbc ._ecc =lineJoin };func _fdef (_fddg [][]_ae .Point ,_gbdg []float64 ,_deff float64 )[][]_ae .Point {var _bbfe [][]_ae .Point ;
if len (_gbdg )==0{return _fddg ;};if len (_gbdg )==1{_gbdg =append (_gbdg ,_gbdg [0]);};for _ ,_ebcb :=range _fddg {if len (_ebcb )< 2{continue ;};_gddd :=_ebcb [0];_agfgb :=1;_eeb :=0;_agaf :=0.0;if _deff !=0{var _ffb float64 ;for _ ,_ffa :=range _gbdg {_ffb +=_ffa ;
};_deff =_f .Mod (_deff ,_ffb );if _deff < 0{_deff +=_ffb ;};for _bfe ,_cbe :=range _gbdg {_deff -=_cbe ;if _deff < 0{_eeb =_bfe ;_agaf =_cbe +_deff ;break ;};};};var _aegg []_ae .Point ;_aegg =append (_aegg ,_gddd );for _agfgb < len (_ebcb ){_bfaa :=_gbdg [_eeb ];
_dba :=_ebcb [_agfgb ];_gaacd :=_gddd .Distance (_dba );_ddde :=_bfaa -_agaf ;if _gaacd > _ddde {_dgfc :=_ddde /_gaacd ;_deef :=_gddd .Interpolate (_dba ,_dgfc );_aegg =append (_aegg ,_deef );if _eeb %2==0&&len (_aegg )> 1{_bbfe =append (_bbfe ,_aegg );
};_aegg =nil ;_aegg =append (_aegg ,_deef );_agaf =0;_gddd =_deef ;_eeb =(_eeb +1)%len (_gbdg );}else {_aegg =append (_aegg ,_dba );_gddd =_dba ;_agaf +=_gaacd ;_agfgb ++;};};if _eeb %2==0&&len (_aegg )> 1{_bbfe =append (_bbfe ,_aegg );};};return _bbfe ;
};func (_gfbf *Context )drawString (_afb string ,_faf _ba .Face ,_agfg ,_fdfd float64 ){_dbda :=&_ba .Drawer {Src :_e .NewUniform (_gfbf ._fce ),Face :_faf ,Dot :_cgae (_ae .NewPoint (_agfg ,_fdfd ))};_ddda :=rune (-1);for _ ,_dfb :=range _afb {if _ddda >=0{_dbda .Dot .X +=_dbda .Face .Kern (_ddda ,_dfb );
};_gba ,_dge ,_bgb ,_dde ,_bgf :=_dbda .Face .Glyph (_dbda .Dot ,_dfb );if !_bgf {continue ;};_abg :=_gba .Sub (_gba .Min );_fee :=_e .NewRGBA (_abg );_cb .DrawMask (_fee ,_abg ,_dbda .Src ,_e .Point {},_dge ,_bgb ,_cb .Over );var _ffcf *_cb .Options ;
if _gfbf ._faa !=nil {_ffcf =&_cb .Options {DstMask :_gfbf ._faa ,DstMaskP :_e .Point {}};};_ccaf :=_gfbf ._dcab .Clone ().Translate (float64 (_gba .Min .X ),float64 (_gba .Min .Y ));_dfcg :=_bb .Aff3 {_ccaf [0],_ccaf [3],_ccaf [6],_ccaf [1],_ccaf [4],_ccaf [7]};
_cb .BiLinear .Transform (_gfbf ._ad ,_dfcg ,_fee ,_abg ,_cb .Over ,_ffcf );_dbda .Dot .X +=_dde ;_ddda =_dfb ;};};func (_egb *Context )ShearAbout (sx ,sy ,x ,y float64 ){_egb .Translate (x ,y );_egb .Shear (sx ,sy );_egb .Translate (-x ,-y );};func (_eba *Context )ClearPath (){_eba ._gag .Clear ();
_eba ._fd .Clear ();_eba ._dca =false };func (_ceeb *Context )ScaleAbout (sx ,sy ,x ,y float64 ){_ceeb .Translate (x ,y );_ceeb .Scale (sx ,sy );_ceeb .Translate (-x ,-y );};func (_fae *Context )DrawLine (x1 ,y1 ,x2 ,y2 float64 ){_fae .MoveTo (x1 ,y1 );
_fae .LineTo (x2 ,y2 )};func (_bf *Context )SetLineWidth (lineWidth float64 ){_bf ._dd =lineWidth };func (_ccb *Context )stroke (_ddg _cc .Painter ){_gaa :=_ccb ._gag ;if len (_ccb ._adb )> 0{_gaa =_gac (_gaa ,_ccb ._adb ,_ccb ._cad );}else {_gaa =_cfd (_cccb (_gaa ));
};_feda :=_ccb ._dcf ;_feda .UseNonZeroWinding =true ;_feda .Clear ();_cdc :=(_ccb ._dcab .ScalingFactorX ()+_ccb ._dcab .ScalingFactorY ())/2;_feda .AddStroke (_gaa ,_fbg (_ccb ._dd *_cdc ),_ccb .capper (),_ccb .joiner ());_feda .Rasterize (_ddg );};func NewContextForImage (im _e .Image )*Context {return NewContextForRGBA (_dag (im ))};
func (_acb *Context )SetLineCap (lineCap _eed .LineCap ){_acb ._dfe =lineCap };func (_bee *Context )SetFillRGBA (r ,g ,b ,a float64 ){_ ,_ ,_ ,_ebg :=_bee ._fce .RGBA ();if _ebg > 0&&_ebg !=65535&&a ==1{a =float64 (_ebg )/65535;};_bdaf :=_a .NRGBA {uint8 (r *255),uint8 (g *255),uint8 (b *255),uint8 (a *255)};
_bee ._fce =_bdaf ;_bee ._ega =_acg (_bdaf );};func _dag (_gge _e .Image )*_e .RGBA {_bgde :=_gge .Bounds ();_ccf :=_e .NewRGBA (_bgde );_ag .Draw (_ccf ,_bgde ,_gge ,_bgde .Min ,_ag .Src );return _ccf ;};func _cgae (_cgab _ae .Point )_gc .Point26_6 {return _gc .Point26_6 {X :_fbg (_cgab .X ),Y :_fbg (_cgab .Y )}};
func (_cdb *Context )LineWidth ()float64 {return _cdb ._dd };var (_ed =_acg (_a .White );_cfe =_acg (_a .Black ););func (_gfb *Context )DrawImageAnchored (im _e .Image ,x ,y int ,ax ,ay float64 ){_ffc :=im .Bounds ().Size ();x -=int (ax *float64 (_ffc .X ));
y -=int (ay *float64 (_ffc .Y ));_fdf :=_cb .BiLinear ;_ecfg :=_gfb ._dcab .Clone ().Translate (float64 (x ),float64 (y ));_ecfd :=_bb .Aff3 {_ecfg [0],_ecfg [3],_ecfg [6],_ecfg [1],_ecfg [4],_ecfg [7]};if _gfb ._faa ==nil {_fdf .Transform (_gfb ._ad ,_ecfd ,im ,im .Bounds (),_cb .Over ,nil );
}else {_fdf .Transform (_gfb ._ad ,_ecfd ,im ,im .Bounds (),_cb .Over ,&_cb .Options {DstMask :_gfb ._faa ,DstMaskP :_e .Point {}});};};func (_bdg stops )Swap (i ,j int ){_bdg [i ],_bdg [j ]=_bdg [j ],_bdg [i ]};func (_cba *Context )Fill (){_cba .FillPreserve ();
_cba .ClearPath ()};func (_dgf *Context )Matrix ()_ae .Matrix {return _dgf ._dcab };func (_cgde *Context )SetRGBA (r ,g ,b ,a float64 ){_ ,_ ,_ ,_gaf :=_cgde ._fce .RGBA ();if _gaf > 0&&_gaf !=65535&&a ==1{a =float64 (_gaf )/65535;};_cgde ._fce =_a .NRGBA {uint8 (r *255),uint8 (g *255),uint8 (b *255),uint8 (a *255)};
_cgde .setFillAndStrokeColor (_cgde ._fce );};func (_decb *Context )Clear (){_eggb :=_e .NewUniform (_decb ._fce );_cb .Draw (_decb ._ad ,_decb ._ad .Bounds (),_eggb ,_e .Point {},_cb .Src );};func (_ggf *Context )Rotate (angle float64 ){_ggf ._dcab =_ggf ._dcab .Rotate (angle )};
func (_gaca *surfacePattern )ColorAt (x ,y int )_a .Color {_cea :=_gaca ._afdf .Bounds ();switch _gaca ._dbad {case _eccc :if y >=_cea .Dy (){return _a .Transparent ;};case _bdda :if x >=_cea .Dx (){return _a .Transparent ;};case _accd :if x >=_cea .Dx ()||y >=_cea .Dy (){return _a .Transparent ;
};};x =x %_cea .Dx ()+_cea .Min .X ;y =y %_cea .Dy ()+_cea .Min .Y ;return _gaca ._afdf .At (x ,y );};func (_fgc *Context )SetDashOffset (offset float64 ){_fgc ._cad =offset };func NewContext (width ,height int )*Context {return NewContextForRGBA (_e .NewRGBA (_e .Rect (0,0,width ,height )));
};func _edfd (_fbec ,_bfdb uint32 ,_bag float64 )uint8 {return uint8 (int32 (float64 (_fbec )*(1.0-_bag )+float64 (_bfdb )*_bag )>>8);};func (_dbe *Context )Height ()int {return _dbe ._dace };const (_debg repeatOp =iota ;_eccc ;_bdda ;_accd ;);func (_cgdea *Context )SetPixel (x ,y int ){_cgdea ._ad .Set (x ,y ,_cgdea ._fce )};
func (_gabf *Context )NewSubPath (){if _gabf ._dca {_gabf ._fd .Add1 (_cgae (_gabf ._gdd ));};_gabf ._dca =false ;};func (_ebe *Context )Push (){_eedc :=*_ebe ;_ebe ._fe =append (_ebe ._fe ,&_eedc )};func (_daca *Context )Image ()_e .Image {return _daca ._ad };
func NewContextForRGBA (im *_e .RGBA )*Context {_gfe :=im .Bounds ().Size ().X ;_bge :=im .Bounds ().Size ().Y ;return &Context {_afa :_gfe ,_dace :_bge ,_dcf :_cc .NewRasterizer (_gfe ,_bge ),_ad :im ,_fce :_a .Transparent ,_ega :_ed ,_dg :_cfe ,_dd :1,_deg :_eed .FillRuleWinding ,_dcab :_ae .IdentityMatrix (),_deb :_eed .NewTextState ()};
};