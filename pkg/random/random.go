package random

import (
	"fmt"
	"math/rand"
	"strings"
	"sync"
	"time"

	"github.com/oklog/ulid/v2"
)

var (
	mux        sync.Mutex
	lastTs     int64
	sequence   int64
	randSource = rand.New(rand.NewSource(time.Now().UnixNano()))
)

func UniqueID() string {
	mux.Lock()
	defer mux.Unlock()

	now := time.Now().UnixNano() / 1e6

	if now == lastTs {
		sequence++
	} else {
		sequence = 0
		lastTs = now
	}

	return fmt.Sprintf("%08d%04d%04d", now%1e8, sequence, randSource.Intn(10000))
}

func RandomID() string {
	return strings.ToLower(ulid.Make().String())
}

func RandomInt(min, max int) int {
	return rand.New(rand.NewSource(time.Now().UnixNano())).Intn(max-min+1) + min
}
